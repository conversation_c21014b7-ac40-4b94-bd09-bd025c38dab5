# Git Ignore Files - 23.08.2025 07:20

_volumes

# General
!.gitkeep

.DS_Store
*.log
*.tmp
*.zip
*.rar
*.tar.gz
*.dmg
*.exe
*.msi
*.swp
*.swo
Thumbs.db
desktop.ini

# Miscs & Artifacts & AI Stuffs
*.o
*.so
*.dylib
*.jsbundle
.claude/
.crush/
.yoyo/

# ENVs
.env*
!.env*.example

# Android/IntelliJ
.idea
**/.gradle
*.iml
*.hprof
.cxx/
*.keystore
!debug.keystore
.kotlin/
*.orig.*
*.jks
*.p8
*.p12
*.key
*.apk
*.aab
*.mobileprovision
**/android/app/src/main/assets/index.android.bundle
**/android/app/src/main/res/drawable-*
**/android/app/src/main/res/raw/
**/android/app/release/
**/android/app/build/
**/android/local.properties
**/android/captures/
**/android/build/
**/android/*.iml
**/android/*.kotlin_module
**/android/.gitignore


# IOS/Xcode
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
*.xccheckout
*.moved-aside
*.hmap
*.xcuserstate
*.ipa
*.dSYM.zip
*.dSYM
# Build Stuffs / Ruby / CocoaPods
**/ios/build/
**/ios/Pods/
**/ios/DerivedData/
**/ios/.xcode.env.local
**/ios/*.xcworkspace/xcuserdata/
**/ios/*.xcodeproj/xcuserdata/
**/ios/.swiftpm/
**/ios/Package.resolved
**/ios/*.xcodeproj/project.xcworkspace/xcuserdata/
**/ios/*.xcworkspace/xcshareddata/WorkspaceSettings.xcsettings
**/vendor/bundle/
**/ios/.gitignore

# VS Code & Sublime Text
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
!.vscode/*.code-snippets
*.sublime-project
*.sublime-workspace

# RN & Expo
.expo
web-build/
expo-env.d.ts
# Temporary files created by Metro to check the health of the file watcher
.metro-health-check*
# fastlane: It is recommended to not store the screenshots in the git repo. Instead, use fastlane to re-generate the screenshots whenever they are needed. For more information about the recommended setup visit: https://docs.fastlane.tools/best-practices/source-control/
**/fastlane/report.xml
**/fastlane/Preview.html
**/fastlane/screenshots
**/fastlane/test_output

# Node & JS & TS & npm & yarn
npm-debug.*
yarn-debug.*
yarn-error.*
*.tsbuildinfo
node_modules/
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions
.pnp*
.astro/
.next/
.turbo/

# Tests
coverage/
.nyc_output/
*.lcov
.jest/
.playwright/

# PHP & Composer & All Others
_plugins/
vendor/
build/
dist/
.terraform/
.vault/
.cache/
.tmp/
tmp/
temp/
__pycache__/
*.py[cod]
.terraform.lock.hcl
*.tmp
*.temp
