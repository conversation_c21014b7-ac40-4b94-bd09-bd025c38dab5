-- MariaDB Database Initialization
-- Optimized schema - keeping only reference data and configuration
-- Domain data moved to ScyllaDB + Manticore for better performance

USE pr__domainr;

-- Domain Categories (Reference Data Only)
CREATE TABLE IF NOT EXISTS domain_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    parent_category_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_category_id) REFERENCES domain_categories(id),
    INDEX idx_parent (parent_category_id),
    INDEX idx_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- System Configuration
CREATE TABLE IF NOT EXISTS system_config (
    config_key VARCHAR(100) PRIMARY KEY,
    config_value TEXT,
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- REMOVED TABLES (moved to better locations):
-- - domain_category_mapping -> Manticore Search (category attribute)
-- - backlinks -> ScyllaDB (high volume, time-series)
-- - domain_whois -> ScyllaDB (co-located with domain data)
-- - domain_reviews -> Manticore Search (review search index)

-- Insert default categories
INSERT IGNORE INTO domain_categories (name, description) VALUES
('Technology', 'Technology and software companies'),
('E-commerce', 'Online retail and shopping platforms'),
('News & Media', 'News websites and media organizations'),
('Education', 'Educational institutions and learning platforms'),
('Healthcare', 'Healthcare and medical services'),
('Finance', 'Financial services and banking'),
('Entertainment', 'Entertainment and gaming platforms'),
('Business Services', 'B2B services and consulting'),
('Government', 'Government and public sector websites'),
('Non-profit', 'Non-profit organizations and charities');

-- Insert subcategories
INSERT IGNORE INTO domain_categories (name, description, parent_category_id) VALUES
('Software Development', 'Software development companies', (SELECT id FROM domain_categories WHERE name = 'Technology' LIMIT 1)),
('Cloud Services', 'Cloud computing and hosting services', (SELECT id FROM domain_categories WHERE name = 'Technology' LIMIT 1)),
('Online Retail', 'E-commerce stores and marketplaces', (SELECT id FROM domain_categories WHERE name = 'E-commerce' LIMIT 1)),
('Digital Marketing', 'Marketing and advertising services', (SELECT id FROM domain_categories WHERE name = 'Business Services' LIMIT 1));

-- Sample data removed - domain data now stored in ScyllaDB and Manticore

-- Insert system configuration
INSERT IGNORE INTO system_config (config_key, config_value, description) VALUES
('crawl_rate_limit', '60', 'Maximum crawl requests per minute'),
('ranking_update_interval', '3600', 'Ranking update interval in seconds'),
('cache_ttl', '1800', 'Default cache TTL in seconds'),
('max_concurrent_crawls', '10', 'Maximum concurrent crawl operations');

-- Show table structure
SHOW TABLES;
DESCRIBE domain_categories;
DESCRIBE backlinks;
DESCRIBE domain_whois;
