{"name": "domain-ranking-database-scripts", "version": "1.0.0", "description": "Database migration and utility scripts", "type": "module", "main": "migrate.js", "scripts": {"migrate": "node migrate.js migrate", "sample-data": "node migrate.js sample-data", "status": "node migrate.js status", "init": "node migrate.js init"}, "dependencies": {"cassandra-driver": "^4.7.2", "mysql2": "^3.6.0", "dotenv": "^16.3.1"}, "engines": {"node": ">=20.0.0"}}