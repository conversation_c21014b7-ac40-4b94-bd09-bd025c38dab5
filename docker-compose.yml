name: pr__domainr
# ----- NETWORKS ----- #
networks:
  mynet_internal:
    name: mynet_internal
    driver: bridge
    internal: true
  mynet_external:
    name: mynet_external
    driver: bridge
    external: true
# ----- SERVICES ----- #
services:
  web-app:
    build:
      context: .
      dockerfile: services/web-app/Dockerfile
    container_name: domain-ranking-web-app
    expose:
      - "3000"
    environment:
      - NODE_ENV=${NODE_ENV:-production}
      - SERVICE_NAME=web-app
      - SERVICE_PORT=3000
      - SCYLLA_HOSTS=scylla:9042
      - SCYLLA_KEYSPACE=${SCYLLA_KEYSPACE:-pr__domainr}
      - MARIA_HOST=mariadb
      - MARIA_PORT=3306
      - MARIA_USER=${MARIA_USER:-root}
      - MARIA_PASSWORD=${MARIA_PASSWORD}
      - MARIA_DATABASE=${MARIA_DATABASE:-pr__domainr}
      - REDIS_URL=redis://redis:6379
      - MANTICORE_HOST=manticore
      - MANTICORE_PORT=9308
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - CORS_ORIGIN=${CORS_ORIGIN:-*}
      - JWT_SECRET=${JWT_SECRET}
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    networks:
      - domain-ranking-network
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # Worker Service (Consolidated crawler, ranking-engine, scheduler)
  worker:
    build:
      context: .
      dockerfile: services/worker/Dockerfile
    container_name: domain-ranking-worker
    expose:
      - "3001"
    environment:
      - NODE_ENV=${NODE_ENV:-production}
      - SERVICE_NAME=worker
      - SERVICE_PORT=3001
      - SCYLLA_HOSTS=scylla:9042
      - SCYLLA_KEYSPACE=${SCYLLA_KEYSPACE:-domain_ranking}
      - MARIA_HOST=mariadb
      - MARIA_PORT=3306
      - MARIA_USER=${MARIA_USER:-root}
      - MARIA_PASSWORD=${MARIA_PASSWORD}
      - MARIA_DATABASE=${MARIA_DATABASE:-domain_ranking}
      - REDIS_URL=redis://redis:6379
      - MANTICORE_HOST=manticore
      - MANTICORE_PORT=9308
      - BROWSERLESS_URL=http://browserless:3000
      - IMAGE_PROXY_URL=https://images.weserv.nl
      # Crawler configuration
      - CRAWL_TIMEOUT=${CRAWL_TIMEOUT:-30000}
      - CRAWL_MAX_RETRIES=${CRAWL_MAX_RETRIES:-3}
      - CRAWL_CONCURRENT_REQUESTS=${CRAWL_CONCURRENT_REQUESTS:-5}
      - CRAWL_RATE_LIMIT=${CRAWL_RATE_LIMIT:-60}
      # Ranking configuration
      - RANKING_WEIGHT_PERFORMANCE=${RANKING_WEIGHT_PERFORMANCE:-0.25}
      - RANKING_WEIGHT_SECURITY=${RANKING_WEIGHT_SECURITY:-0.20}
      - RANKING_WEIGHT_SEO=${RANKING_WEIGHT_SEO:-0.20}
      - RANKING_WEIGHT_TECHNICAL=${RANKING_WEIGHT_TECHNICAL:-0.15}
      - RANKING_WEIGHT_BACKLINKS=${RANKING_WEIGHT_BACKLINKS:-0.20}
      # AI provider configuration (optional)
      - OPENAI_API_KEYS=${OPENAI_API_KEYS:-}
      - CLAUDE_API_KEYS=${CLAUDE_API_KEYS:-}
      - GEMINI_API_KEYS=${GEMINI_API_KEYS:-}
      - OPENROUTER_API_KEYS=${OPENROUTER_API_KEYS:-}
      - LOG_LEVEL=${LOG_LEVEL:-info}
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    depends_on:
      - scylla
      - mariadb
      - redis
      - manticore
      - browserless
    networks:
      - domain-ranking-network
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '4.0'
        reservations:
          memory: 2G
          cpus: '2.0'

  # Domain Seeder Service
  domain-seeder:
    build:
      context: .
      dockerfile: services/domain-seeder/Dockerfile
      target: production
    container_name: domain-ranking-seeder
    expose:
      - "3004"
    environment:
      - NODE_ENV=${NODE_ENV:-production}
      - SERVICE_NAME=domain-seeder
      - SERVICE_PORT=3004
      - PORT=3004
      # Database connections
      - SCYLLA_CONTACT_POINTS=scylla:9042
      - SCYLLA_LOCAL_DC=datacenter1
      - SCYLLA_KEYSPACE=${SCYLLA_KEYSPACE:-domain_ranking}
      - MARIA_HOST=mariadb
      - MARIA_PORT=3306
      - MARIA_USER=${MARIA_USER:-root}
      - MARIA_PASSWORD=${MARIA_PASSWORD}
      - MARIA_DATABASE=${MARIA_DATABASE:-domain_ranking}
      - MANTICORE_HOST=manticore
      - MANTICORE_PORT=9308
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
      - REDIS_DB=0
      # Domain seeder specific configuration
      - MAX_NEW_PER_DAY=${SEEDER_MAX_NEW_PER_DAY:-500000}
      - ENQUEUE_BATCH_SIZE=${SEEDER_ENQUEUE_BATCH:-1000}
      - ENQUEUE_INTERVAL_MS=${SEEDER_ENQUEUE_INTERVAL:-1000}
      - NEW_QUEUE_MAX_DEPTH=${SEEDER_QUEUE_MAX_DEPTH:-200000}
      - DB_CHECK_BATCH_SIZE=${SEEDER_DB_CHECK_BATCH:-5000}
      - BLOOM_FP_RATE=${SEEDER_BLOOM_FP_RATE:-0.01}
      # External source credentials (optional)
      - CZDS_USERNAME=${CZDS_USERNAME:-}
      - CZDS_PASSWORD=${CZDS_PASSWORD:-}
      - CLOUDFLARE_API_KEY=${CLOUDFLARE_API_KEY:-}
      - UMBRELLA_API_KEY=${UMBRELLA_API_KEY:-}
      - PIR_API_KEY=${PIR_API_KEY:-}
      - RAPID7_API_KEY=${RAPID7_API_KEY:-}
      # AI provider configuration (optional)
      - OPENAI_API_KEYS=${OPENAI_API_KEYS:-}
      - CLAUDE_API_KEYS=${CLAUDE_API_KEYS:-}
      - GEMINI_API_KEYS=${GEMINI_API_KEYS:-}
      - OPENROUTER_API_KEYS=${OPENROUTER_API_KEYS:-}
      # Proxy configuration (optional)
      - PROXY_IPS=${PROXY_IPS:-}
      - PROXY_ROTATION_ENABLED=${PROXY_ROTATION_ENABLED:-false}
      # Logging and monitoring
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - METRICS_ENABLED=${METRICS_ENABLED:-true}
      - HEALTH_CHECK_INTERVAL=${HEALTH_CHECK_INTERVAL:-30}
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data:ro
    restart: unless-stopped
    depends_on:
      - scylla
      - mariadb
      - redis
      - manticore
    networks:
      - domain-ranking-network
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '1.0'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3004/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # ScyllaDB Database
  scylla:
    image: scylladb/scylla:6.2
    container_name: domain-ranking-scylla
    command: --seeds=scylla --smp 2 --memory 2G --overprovisioned 1 --api-address 0.0.0.0
    ports:
      - "${SCYLLA_PORT:-9042}:9042"
      - "${SCYLLA_API_PORT:-10000}:10000"
    volumes:
      - scylla_data:/var/lib/scylla
      - ./database/scylla/init.cql:/docker-entrypoint-initdb.d/init.cql:ro
      - ./logs/scylla:/var/log/scylla
    restart: unless-stopped
    networks:
      - domain-ranking-network
    deploy:
      resources:
        limits:
          memory: 3G
          cpus: '2.0'
        reservations:
          memory: 2G
          cpus: '1.0'

  # MariaDB Database
  mariadb:
    image: mariadb:11.2
    container_name: domain-ranking-mariadb
    ports:
      - "${MARIA_PORT:-3306}:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=${MARIA_PASSWORD}
      - MYSQL_DATABASE=${MARIA_DATABASE:-domain_ranking}
      - MYSQL_USER=${MARIA_USER:-domain_user}
      - MYSQL_PASSWORD=${MARIA_PASSWORD}
    volumes:
      - mariadb_data:/var/lib/mysql
      - ./database/mariadb/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
      - ./logs/mariadb:/var/log/mysql
    restart: unless-stopped
    networks:
      - domain-ranking-network
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # Redis Cache & Queue
  redis:
    image: redis:7.2-alpine
    container_name: domain-ranking-redis
    ports:
      - "${REDIS_PORT:-6379}:6379"
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
      - ./logs/redis:/var/log/redis
    restart: unless-stopped
    networks:
      - domain-ranking-network
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # Manticore Search
  manticore:
    image: manticoresearch/manticore:6.2.12
    container_name: domain-ranking-manticore
    ports:
      - "${MANTICORE_PORT:-9308}:9308"
      - "${MANTICORE_BINARY_PORT:-9312}:9312"
    volumes:
      - manticore_data:/var/lib/manticore
      - ./database/manticore/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
      - ./logs/manticore:/var/log/manticore
    restart: unless-stopped
    networks:
      - domain-ranking-network
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # Browserless for Screenshots
  browserless:
    image: browserless/chrome:1.61.0-puppeteer-21.4.1
    container_name: domain-ranking-browserless
    ports:
      - "${BROWSERLESS_PORT:-3000}:3000"
    environment:
      - MAX_CONCURRENT_SESSIONS=${BROWSERLESS_MAX_SESSIONS:-5}
      - CONNECTION_TIMEOUT=${BROWSERLESS_TIMEOUT:-60000}
      - MAX_QUEUE_LENGTH=${BROWSERLESS_QUEUE:-10}
      - PREBOOT_CHROME=true
      - KEEP_ALIVE=true
      - CHROME_REFRESH_TIME=2000000
    restart: unless-stopped
    networks:
      - domain-ranking-network
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '1.0'

  # Monitoring - Prometheus
  prometheus:
    image: prom/prometheus:v2.48.0
    container_name: domain-ranking-prometheus
    ports:
      - "${PROMETHEUS_PORT:-9090}:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - domain-ranking-network

  # Monitoring - Grafana
  grafana:
    image: grafana/grafana:10.2.0
    container_name: domain-ranking-grafana
    ports:
      - "${GRAFANA_PORT:-3001}:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    restart: unless-stopped
    depends_on:
      - prometheus
    networks:
      - domain-ranking-network

volumes:
  scylla_data:
    driver: local
  mariadb_data:
    driver: local
  redis_data:
    driver: local
  manticore_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  domain-ranking-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
