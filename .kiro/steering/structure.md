---
inclusion: always
---

# Structure Rules v4 - 17.08.2025 22:25

## General Rules

- Don't repeat yourself, that's why there are dynamic programming languages
- Verify before changing
- Deliver production-ready solutions
- Prioritize logic and accuracy; handle complex solutions confidently
- Follow consistent naming and folder structures
- Handle errors gracefully

## General Coding Rules

- Follow project configs (`.editorconfig`, `tsconfig.json`, `.eslintrc.js`, `.eslintrc.cjs`, etc)
- Strip all trailing whitespace from line endings in code blocks and ensure no lines end with spaces or tabs
- Always use Allman-style braces with opening braces on new lines for all scopes including types, functions, objects, and arrays
- Always use strict equality (`===`)
- Prioritize performance, modularity, and reusability; avoid duplications
- Document all public APIs clearly
- Comment complex logic or structures
- Add JSDoc comments or inline comments to explain complex logic directly in TypeScript code
- Use descriptive names for variables, methods, and files
- Validate and sanitize all inputs
- When there are too many files on a folder like "services", try to put the related services in a subfolder like "services/email/Queue.ts"
- Never ever stop until you give up fixing something. Otherwise, tell me you're giving up due to "your reason"
- If you finish your task/work too early, then focus on fixing the problems for the rest of your time. Never stop until consume all of your time/tokens
- When you don't implement a part of your goal put a detailed todo to implement it later and inform me
- When you see "TODO-\*:" take care of the issue and fix it immediately
- When you see something commented, try to recognize if it's half implementation that you need to finish it immediately or you need to delete and it's outdated anymore
