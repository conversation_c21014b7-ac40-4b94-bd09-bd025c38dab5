// ESLint 9 Flat Config

const {
	createCLIConfig,
	createServerConfig,
	createBrowserConfig,
	createWebTestConfig,
	createMobileTestConfig,
} = require('./_config/eslint.rules.new');

const rootDir = __dirname;

// Helper to extract configs from factory functions
function extractConfigs(configArray, files, ignores = [])
{
	return configArray.map((config, index) => ({
		...config,
		files: files || config.files,
		...(ignores.length > 0 ? { ignores } : {}),
		name: config.name || `config-${index}`,
	}));
}

module.exports =
[
	// Global ignores
	{
		ignores: [
			'node_modules/',
			'dist/',
			'build/',
			'coverage/',
			'*.min.js',
			'*.bundle.js',
			'logs/',
			'temp/',
			'uploads/',
			'**/*.d.ts',
			'**/__mocks__/**',
			'**/android/**',
			'**/ios/**',
			'**/.expo/**',
			'**/metro.config.js',
			'vitest.config.ts',
			'vitest.workspace.ts',
			'shared/vitest.config.ts',
		],
	},

	// Standalone config files (no tsconfig project)
	{
		files: [
			'vitest.config.ts',
			'vitest.workspace.ts',
			'shared/vitest.config.ts',
			'services/*/vitest.config.ts',
			'services/**/vitest.config.ts',
			'services/**/vitest.config*.ts',
		],
		languageOptions: {
			parser: require('@typescript-eslint/parser'),
			parserOptions: {
				tsconfigRootDir: rootDir,
				requireConfigFile: false,
			},
		},
		plugins: {
			'@typescript-eslint': require('@typescript-eslint/eslint-plugin'),
			'@stylistic': require('@stylistic/eslint-plugin'),
			import: require('eslint-plugin-import'),
		},
		rules: {
			'@typescript-eslint/no-var-requires': 0,
			'@typescript-eslint/no-require-imports': 0,
			'import/no-extraneous-dependencies': [2, { devDependencies: true }],
			'import/no-unresolved': 0,
		},
		name: 'standalone-configs',
	},

	// Web test files
	...extractConfigs(
		createWebTestConfig(rootDir, './_config/tsconfig.test-web.json'),
		[
			'services/**/src/__tests__/**/*.test.{js,jsx,ts,tsx}',
			'services/**/src/__tests__/**/*.spec.{js,jsx,ts,tsx}',
			'services/**/__tests__/**/*.test.{js,jsx,ts,tsx}',
			'services/**/__tests__/**/*.spec.{js,jsx,ts,tsx}',
			'shared/**/__tests__/**/*.test.{js,jsx,ts,tsx}',
			'shared/**/__tests__/**/*.spec.{js,jsx,ts,tsx}',
			'**/__mocks__/**/*.{js,jsx,ts,tsx}',
			'**/*.{test,spec}.{js,jsx,ts,tsx}',
		],
		[
			'**/node_modules/**',
			'**/dist/**',
			'**/build/**',
			'**/*.(test|spec).(native|rn|mobile).{js,jsx,ts,tsx}',
			'**/react-native/**',
			'**/mobile/**',
		]
	).map((config, index) => ({ ...config, name: `web-test-${index}` })),

	// Mobile test files
	// ...extractConfigs(
	// 	createMobileTestConfig(rootDir, './_config/tsconfig.test-mobile.json'),
	// 	[
	// 		'**/*.(test|spec).(native|rn|mobile).{js,jsx,ts,tsx}',
	// 		'**/react-native/**/__tests__/**/*.{js,jsx,ts,tsx}',
	// 		'**/mobile/**/__tests__/**/*.{js,jsx,ts,tsx}',
	// 	],
	// 	[
	// 		'**/node_modules/**',
	// 		'**/dist/**',
	// 		'**/build/**',
	// 		'**/android/**',
	// 		'**/ios/**',
	// 	]
	// ).map((config, index) => ({ ...config, name: `mobile-test-${index}` })),

	// Web app (browser)
	...extractConfigs(
		createBrowserConfig(rootDir, './services/web-app/tsconfig.json'),
		['services/web-app/**/*.{js,jsx,ts,tsx}'],
		[
			'services/web-app/node_modules/**',
			'services/web-app/dist/**',
			'services/web-app/public/**',
			'services/web-app/**/__tests__/**',
			'services/web-app/**/__mocks__/**',
		]
	).map((config, index) => ({ ...config, name: `web-app-${index}` })),

	// Admin panel (browser with enhanced a11y)
	...extractConfigs(
		createBrowserConfig(rootDir, './services/admin/tsconfig.json'),
		['services/admin/**/*.{js,jsx,ts,tsx}'],
		[
			'services/admin/node_modules/**',
			'services/admin/.next/**',
			'services/admin/dist/**',
			'services/admin/build/**',
			'services/admin/public/**',
			'services/admin/**/__tests__/**',
			'services/admin/**/__mocks__/**',
		]
	).map((config, index) => ({ ...config, name: `admin-${index}` })),

	// Domain seeder (server)
	...extractConfigs(
		createServerConfig(rootDir, './services/domain-seeder/tsconfig.json'),
		['services/domain-seeder/**/*.{js,ts}'],
		[
			'services/domain-seeder/node_modules/**',
			'services/domain-seeder/dist/**',
			'services/domain-seeder/**/__tests__/**',
			'services/domain-seeder/**/__mocks__/**',
		]
	).map((config, index) => ({ ...config, name: `domain-seeder-${index}` })),

	// Worker service (server)
	...extractConfigs(
		createServerConfig(rootDir, './services/worker/tsconfig.json'),
		['services/worker/**/*.{js,ts}'],
		[
			'services/worker/node_modules/**',
			'services/worker/dist/**',
			'services/worker/**/__tests__/**',
			'services/worker/**/__mocks__/**',
			'services/worker/**/*.test.ts',
			'services/worker/**/*.spec.ts',
		]
	).map((config, index) => ({ ...config, name: `worker-${index}` })),

	// Shared libraries (server)
	...extractConfigs(
		createServerConfig(rootDir, './shared/tsconfig.json'),
		['shared/**/*.{js,jsx,ts,tsx}'],
		[
			'shared/node_modules/**',
			'shared/dist/**',
			'shared/**/__tests__/**',
			'shared/**/__mocks__/**',
			'shared/vitest.config.ts',
		]
	).map((config, index) => ({ ...config, name: `shared-${index}` })),

	// Scripts (server)
	...extractConfigs(
		createServerConfig(rootDir, './_config/tsconfig.base.json'),
		['scripts/**/*.{js,ts}']
	).map((config, index) => ({ ...config, name: `scripts-${index}` })),

	// Config files (CLI)
	...extractConfigs(
		createCLIConfig(rootDir, './_config/tsconfig.base.json'),
		['_config/**/*.{js,ts}']
	).map((config, index) => ({ ...config, name: `config-${index}` })),

	// Root files (server)
	...extractConfigs(
		createServerConfig(rootDir, './_config/tsconfig.base.json'),
		['*.{js,ts}'],
		[
			'node_modules/**',
			'services/**',
			'shared/**',
			'scripts/**',
			'vitest.config.ts',
			'vitest.workspace.ts',
		]
	).map((config, index) => ({ ...config, name: `root-${index}` })),
];
