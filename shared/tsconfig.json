{
  "extends": "../_config/tsconfig.base.json",
  "compilerOptions": {
    "outDir": "./dist",
    "rootDir": "./src",
    "lib": ["ES2022"],
    "types": ["node", "vitest/globals"],
    // Shared package can reference itself
    "baseUrl": ".",
    "paths": {
      "@shared/*": ["./src/*"],
      "@shared": ["./src/index.ts"]
    }
  },
  "include": ["src/**/*"],
  "exclude": [
    "node_modules",
    "dist",
    "src/**/__tests__/**",
    "src/**/*.test.ts",
    "src/**/*.test.tsx"
  ]
}
