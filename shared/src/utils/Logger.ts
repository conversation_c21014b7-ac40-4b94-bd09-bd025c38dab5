import type { Logger as PinoLogger, DestinationStream, Level } from 'pino';
import pino from 'pino';

type RequestLikeType =
{
	method: string;
	url: string;
	get: (header: string) => string | undefined;
	ip?: string;
};

type ResponseLikeType =
{
	statusCode: number;
	on: (event: 'finish', listener: () => void) => void;
};

type NextFunctionLikeType = () => void;

type LoggerMetadataType =
{
	[key: string]: unknown;
};

type HandlerParamType = unknown; // Avoid using any for public surface

type StreamEntry<TLevel extends Level = Level> =
{
	stream: DestinationStream;
	level?: TLevel | string;
};

interface PinoLoggerWithMethods extends PinoLogger
{
	exceptions: {
		handle: (transport?: HandlerParamType) => void;
	};
	rejections: {
		handle: (transport?: HandlerParamType) => void;
	};
}

/**
 * Centralized logging utility for all services
 */
class Logger
{
	private loggers: Map<string, PinoLoggerWithMethods>;

	private baseLogger: PinoLogger;

	constructor()
	{
		this.loggers = new Map();

		// Create base pino logger configuration
		const isDevelopment = (typeof process !== 'undefined' && process.env?.NODE_ENV !== 'production') || false;
		const isCLI = (typeof process !== 'undefined' && (process.env?.CLI_MODE === 'true' || process.argv?.[1]?.includes('cli'))) || false;

		this.baseLogger = pino({
			level: (typeof process !== 'undefined' ? process.env?.LOG_LEVEL : null) || 'info',
			timestamp: pino.stdTimeFunctions.isoTime,
			errorKey: 'error',
			...(isDevelopment && {
				transport: {
					target: 'pino-pretty',
					options: {
						colorize: true,
						translateTime: isCLI ? false : 'yyyy-mm-dd HH:MM:ss.l',
						ignore: isCLI ? 'pid,hostname,time' : 'pid,hostname',
						messageFormat: isCLI ? '{msg}' : undefined,
						levelFirst: !isCLI,
						hideObject: isCLI,
					},
				},
			}),
		});
	}

	/**
   * Get logger instance for a specific service/module
   */
	getLogger(serviceName: string, cliOptions?: { quiet?: boolean; json?: boolean }): PinoLoggerWithMethods
	{
		const cacheKey = `${serviceName}-${JSON.stringify(cliOptions || {})}`;

		if (this.loggers.has(cacheKey))
		{
			return this.loggers.get(cacheKey)!;
		}

		const isDevelopment = (typeof process !== 'undefined' && process.env?.NODE_ENV !== 'production') || false;
		const isCLI = (typeof process !== 'undefined' && (process.env?.CLI_MODE === 'true' || process.argv?.[1]?.includes('cli'))) || false;

		// Create child logger with service name binding
		let serviceLogger: PinoLogger;

		if (isDevelopment)
		{
			// Development: use pretty printing with CLI adjustments
			const prettyOptions = {
				colorize: true,
				translateTime: (isCLI && !cliOptions?.json) ? false : 'yyyy-mm-dd HH:MM:ss.l',
				ignore: isCLI ? 'pid,hostname,time' : 'pid,hostname',
				messageFormat: (isCLI && !cliOptions?.json) ? '{msg}' : undefined,
				levelFirst: !isCLI,
				hideObject: isCLI && !cliOptions?.json,
				...(cliOptions?.quiet && { level: 'error' }),
			};

			if (cliOptions?.json) {
				// For JSON output, don't use pino-pretty
				serviceLogger = pino({
					level: cliOptions.quiet ? 'error' : (process.env.LOG_LEVEL || 'info'),
					timestamp: pino.stdTimeFunctions.isoTime,
					errorKey: 'error',
				}).child({ service: serviceName });
			} else {
				serviceLogger = pino({
					level: cliOptions?.quiet ? 'error' : (process.env.LOG_LEVEL || 'info'),
					timestamp: pino.stdTimeFunctions.isoTime,
					errorKey: 'error',
					transport: {
						target: 'pino-pretty',
						options: prettyOptions,
					},
				}).child({ service: serviceName });
			}
		}
		else
		{
			// Production: use multiple destinations for different log levels
			const streams: Array<StreamEntry> =
			[
				// Console for all logs
				{ stream: process.stdout as unknown as DestinationStream },
				// File for errors only
				{
					stream: pino.destination({
						dest: `logs/${serviceName}-error.log`,
						sync: false,
						mkdir: true,
					}),
					level: 'error',
				},
				// File for all logs
				{
					stream: pino.destination({
						dest: `logs/${serviceName}-combined.log`,
						sync: false,
						mkdir: true,
					}),
				},
			];

			serviceLogger = pino({
				level: cliOptions?.quiet ? 'error' : (process.env.LOG_LEVEL || 'info'),
				timestamp: pino.stdTimeFunctions.isoTime,
				errorKey: 'error',
			}, pino.multistream(streams)).child({ service: serviceName });
		}

		// Add methods for exception/rejection handling
		const extendedLogger = serviceLogger as PinoLoggerWithMethods;
		extendedLogger.exceptions = {
			handle: (): void =>
			{
				// Pino handles uncaught exceptions differently
				// We'll create a separate file destination for exceptions
				if (!isDevelopment)
				{
					process.on('uncaughtException', (error) =>
					{
						extendedLogger.fatal({ error, msg: 'Uncaught Exception' });
						process.exit(1);
					});
				}
			},
		};

		extendedLogger.rejections = {
			handle: (): void =>
			{
				if (!isDevelopment)
				{
					process.on('unhandledRejection', (reason, promise) =>
					{
						extendedLogger.fatal({ reason, promise, msg: 'Unhandled Promise Rejection' });
						process.exit(1);
					});
				}
			},
		};

		this.loggers.set(cacheKey, extendedLogger);
		return extendedLogger;
	}

	/**
   * Create request logger middleware for Express
   */
	createRequestLogger(serviceName: string)
	{
		const logger = this.getLogger(serviceName);

		return (req: RequestLikeType, res: ResponseLikeType, next: NextFunctionLikeType): void =>
		{
			const start = Date.now();

			res.on('finish', () =>
			{
				const duration = Date.now() - start;
				logger.info({
					method: req.method,
					url: req.url,
					statusCode: res.statusCode,
					duration: `${duration}ms`,
					userAgent: req.get('User-Agent'),
					ip: req.ip,
				}, 'HTTP Request');
			});

			next();
		};
	}

	/**
   * Log performance metrics
   */
	logPerformance(
		serviceName: string,
		operation: string,
		duration: number,
		metadata: LoggerMetadataType = {},
	): void
	{
		const logger = this.getLogger(serviceName);
		logger.info({
			operation,
			duration: `${duration}ms`,
			...metadata,
		}, 'Performance Metric');
	}

	/**
   * Log database operations
   */
	logDatabaseOperation(
		serviceName: string,
		operation: string,
		query: string,
		duration: number,
		error: Error | null = null,
	): void
	{
		const logger = this.getLogger(serviceName);

		if (error)
		{
			logger.error({
				operation,
				query: query.substring(0, 200), // Truncate long queries
				duration: `${duration}ms`,
				error: error.message,
			}, 'Database Operation Failed');
		}
		else
		{
			logger.info({
				operation,
				query: query.substring(0, 200),
				duration: `${duration}ms`,
			}, 'Database Operation');
		}
	}

	/**
   * Log job queue operations
   */
	logJobOperation(
		serviceName: string,
		operation: string, jobId: string,
		queueName: string,
		metadata: LoggerMetadataType = {},
	): void
	{
		const logger = this.getLogger(serviceName);
		logger.info({
			operation,
			jobId,
			queueName,
			...metadata,
		}, 'Job Operation');
	}

	/**
   * Create CLI-optimized logger for command output
   */
	getCLILogger(serviceName: string, options: { quiet?: boolean; verbose?: boolean; json?: boolean } = {}): PinoLoggerWithMethods & {
		success: (message: string, data?: LoggerMetadataType) => void;
		outputData: (data: unknown) => void;
		table: (data: Record<string, unknown>[], headers?: string[]) => void;
	}
	{
		const cliOptions = {
			quiet: options.quiet,
			json: options.json || false,
		};

		const baseLogger = this.getLogger(serviceName, cliOptions);

		// Add CLI-specific methods
		const cliLogger = baseLogger as PinoLoggerWithMethods & {
			success: (message: string, data?: LoggerMetadataType) => void;
			outputData: (data: unknown) => void;
			table: (data: Record<string, unknown>[], headers?: string[]) => void;
		};

		cliLogger.success = (message: string, data?: LoggerMetadataType) => {
			if (options.json) {
				baseLogger.info({ status: 'success', message, data });
			} else {
				baseLogger.info(`✓ ${message}`);
			}
		};

		cliLogger.outputData = (data: unknown) => {
			if (options.json) {
				// For JSON mode, output directly to stdout to avoid pino formatting
				process.stdout.write(`${JSON.stringify(data, null, 2)}\n`);
			} else {
				baseLogger.info({ data }, 'Output');
			}
		};

		cliLogger.table = (data: Record<string, unknown>[], headers?: string[]) => {
			if (options.json) {
				cliLogger.outputData(data);
				return;
			}

			if (!Array.isArray(data) || data.length === 0) {
				baseLogger.info('No data to display');
				return;
			}

			// Simple table formatting for CLI
			const keys = headers || Object.keys(data[0] || {});
			if (keys.length === 0) return;

			// Calculate column widths
			const widths = keys.map(key =>
				Math.max(
					key.length,
					...data.map(row => String(row[key] || '').length)
				)
			);

			// Header
			const headerRow = keys.map((key, i) =>
				key.padEnd(widths[i])
			).join(' │ ');

			baseLogger.info(`┌─${widths.map(w => '─'.repeat(w)).join('─┼─')}─┐`);
			baseLogger.info(`│ ${headerRow} │`);
			baseLogger.info(`├─${widths.map(w => '─'.repeat(w)).join('─┼─')}─┤`);

			// Data rows
			data.forEach(row => {
				const dataRow = keys.map((key, i) =>
					String(row[key] || '').padEnd(widths[i])
				).join(' │ ');
				baseLogger.info(`│ ${dataRow} │`);
			});

			baseLogger.info(`└─${widths.map(w => '─'.repeat(w)).join('─┼─')}─┘`);
		};

		return cliLogger;
	}
}

// Export singleton instance
const logger = new Logger();

// Export Logger instance type for dependency injection
export type LoggerInstanceType = ReturnType<typeof logger.getLogger>;

export { logger };

export default Logger;
