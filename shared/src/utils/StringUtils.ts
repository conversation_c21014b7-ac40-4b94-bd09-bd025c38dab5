/**
 * Shared string utility functions to replace duplicated patterns
 * across services
 */

class StringUtils {
	/**
	 * Capitalize the first letter of a string
	 * Replaces patterns like: str.charAt(0).toUpperCase() + str.slice(1).toLowerCase()
	 */
	static capitalize(str: string): string {
		if (!str) return str;
		return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
	}

	/**
	 * Capitalize the first letter while keeping the rest unchanged
	 * Replaces: str.charAt(0).toUpperCase() + str.slice(1)
	 */
	static capitalizeFirst(str: string): string {
		if (!str) return str;
		return str.charAt(0).toUpperCase() + str.slice(1);
	}

	/**
	 * Capitalize each word in a string
	 * Replaces: str.replace with regex pattern for word transformation
	 */
	static titleCase(str: string): string {
		if (!str) return str;
		return str.replace(/\w\S*/g, (txt) => 
			txt.charAt(0).toUpperCase() + txt.slice(1).toLowerCase()
		);
	}

	/**
	 * Truncate a string to a maximum length with ellipsis
	 * Replaces patterns like: str.substring(0, maxLength - 3) + '...'
	 */
	static truncate(str: string, maxLength: number, suffix: string = '...'): string {
		if (!str) return str;
		if (str.length <= maxLength) return str;
		return str.substring(0, maxLength - suffix.length) + suffix;
	}

	/**
	 * Truncate a string by word count
	 */
	static truncateWords(str: string, maxWords: number, suffix: string = '...'): string {
		if (!str) return str;
		const words = str.split(/\s+/);
		if (words.length <= maxWords) return str;
		return words.slice(0, maxWords).join(' ') + suffix;
	}

	/**
	 * Normalize whitespace (replace multiple spaces with single space and trim)
	 */
	static normalizeWhitespace(str: string): string {
		if (!str) return str;
		return str.replace(/\s+/g, ' ').trim();
	}

	/**
	 * Generate a random string
	 */
	static generateRandom(length: number, charset?: string): string {
		const defaultCharset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
		const chars = charset || defaultCharset;
		let result = '';
		
		for (let i = 0; i < length; i++) {
			result += chars.charAt(Math.floor(Math.random() * chars.length));
		}
		
		return result;
	}

	/**
	 * Convert string to kebab-case
	 */
	static toKebabCase(str: string): string {
		if (!str) return str;
		return str
			.replace(/([a-z])([A-Z])/g, '$1-$2')
			.replace(/[\s_]+/g, '-')
			.toLowerCase();
	}

	/**
	 * Convert string to camelCase
	 */
	static toCamelCase(str: string): string {
		if (!str) return str;
		return str
			.replace(/[-_\s]+(.)?/g, (_, char) => char ? char.toUpperCase() : '')
			.replace(/^[A-Z]/, char => char.toLowerCase());
	}

	/**
	 * Convert string to PascalCase
	 */
	static toPascalCase(str: string): string {
		const camelCase = this.toCamelCase(str);
		return camelCase.charAt(0).toUpperCase() + camelCase.slice(1);
	}

	/**
	 * Convert string to snake_case
	 */
	static toSnakeCase(str: string): string {
		if (!str) return str;
		return str
			.replace(/([a-z])([A-Z])/g, '$1_$2')
			.replace(/[\s-]+/g, '_')
			.toLowerCase();
	}

	/**
	 * Check if string is empty or only whitespace
	 */
	static isEmpty(str: string | null | undefined): boolean {
		return !str || str.trim().length === 0;
	}

	/**
	 * Safe string comparison (case insensitive)
	 */
	static equals(str1: string | null | undefined, str2: string | null | undefined, caseSensitive = false): boolean {
		if (str1 === str2) return true;
		if (!str1 || !str2) return false;
		
		return caseSensitive 
			? str1 === str2
			: str1.toLowerCase() === str2.toLowerCase();
	}

	/**
	 * Extract initials from a name
	 */
	static getInitials(name: string, maxInitials = 2): string {
		if (!name) return '';
		
		return name
			.split(/\s+/)
			.slice(0, maxInitials)
			.map(word => word.charAt(0).toUpperCase())
			.join('');
	}

	/**
	 * Format file size in human readable format
	 */
	static formatFileSize(bytes: number): string {
		if (bytes === 0) return '0 Bytes';
		
		const k = 1024;
		const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		
		return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
	}

	/**
	 * Escape HTML special characters
	 */
	static escapeHtml(str: string): string {
		if (!str) return str;
		
		const htmlEscapes: Record<string, string> = {
			'&': '&amp;',
			'<': '&lt;',
			'>': '&gt;',
			'"': '&quot;',
			"'": '&#39;'
		};
		
		return str.replace(/[&<>"']/g, char => htmlEscapes[char]);
	}

	/**
	 * Remove special characters and keep only alphanumeric and specified chars
	 */
	static sanitize(str: string, allowedChars = '-_'): string {
		if (!str) return str;
		
		const pattern = new RegExp(`[^a-zA-Z0-9${allowedChars.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}]`, 'g');
		return str.replace(pattern, '');
	}

	/**
	 * Count words in a string
	 */
	static wordCount(str: string): number {
		if (!str) return 0;
		return str.trim().split(/\s+/).filter(word => word.length > 0).length;
	}
}


export default StringUtils;
export { StringUtils };