import { Curl } from 'node-libcurl';
import { logger } from './Logger';

type HttpClientOptionsType =
{
	baseURL?: string;
	timeout?: number;
	headers?: Record<string, string>;
	proxy?: string;
};

type HttpResponseType<T = unknown> =
{
	statusCode: number;
	data: T;
	headers: Record<string, string>;
};

interface HttpErrorInterface extends Error
{
	statusCode?: number;
	response?: {
		data?: unknown;
		status?: number;
		headers?: Record<string, string>;
	};
}

class HttpClient
{
	private logger: ReturnType<typeof logger.getLogger>;
	private defaultOptions: HttpClientOptionsType;

	constructor(
		loggerInstance: ReturnType<typeof logger.getLogger>,
		defaultOptions: HttpClientOptionsType = {},
	)
	{
		this.logger = loggerInstance;
		this.defaultOptions = {
			timeout: 30000,
			headers: {},
			...defaultOptions,
		};
	}

	async get<T = unknown>(
		url: string,
		options: HttpClientOptionsType = {},
	): Promise<HttpResponseType<T>>
	{
		return this.request<T>('GET', url, undefined, options);
	}

	async post<T = unknown>(
		url: string,
		data?: unknown,
		options: HttpClientOptionsType = {},
	): Promise<HttpResponseType<T>>
	{
		return this.request<T>('POST', url, data, options);
	}

	async put<T = unknown>(
		url: string,
		data?: unknown,
		options: HttpClientOptionsType = {},
	): Promise<HttpResponseType<T>>
	{
		return this.request<T>('PUT', url, data, options);
	}

	async delete<T = unknown>(
		url: string,
		options: HttpClientOptionsType = {},
	): Promise<HttpResponseType<T>>
	{
		return this.request<T>('DELETE', url, undefined, options);
	}

	async head<T = unknown>(
		url: string,
		options: HttpClientOptionsType = {},
	): Promise<HttpResponseType<T>>
	{
		return this.request<T>('HEAD', url, undefined, options);
	}

	private async request<T>(
		method: string,
		url: string,
		data?: unknown,
		options: HttpClientOptionsType = {}
	): Promise<HttpResponseType<T>>
	{
		const mergedOptions =
		{
			...this.defaultOptions,
			...options,
		};

		// Construct full URL if baseURL is provided
		const fullUrl = mergedOptions.baseURL
			? `${mergedOptions.baseURL.replace(/\/$/, '')}/${url.replace(/^\//, '')}`
			: url;

		this.logger.debug({
			timeout: mergedOptions.timeout,
			hasData: !!data,
			hasProxy: !!mergedOptions.proxy,
		}, `Making ${method} request to ${fullUrl}`);

		return new Promise((resolve, reject) =>
		{
			const curl = new Curl();

			try
			{
				// Set basic options
				curl.setOpt('URL', fullUrl);
				curl.setOpt('FOLLOWLOCATION', true);
				// Ensure TIMEOUT_MS receives a concrete number
				curl.setOpt('TIMEOUT_MS', mergedOptions.timeout ?? 30000);

				// Set method
				switch (method.toUpperCase())
				{
					case 'GET':
						curl.setOpt('HTTPGET', true);
						break;
					case 'POST':
						curl.setOpt('POST', true);
						break;
					case 'PUT':
						curl.setOpt('CUSTOMREQUEST', 'PUT');
						break;
					case 'DELETE':
						curl.setOpt('CUSTOMREQUEST', 'DELETE');
						break;
					case 'HEAD':
						curl.setOpt('NOBODY', true);
						break;
					default:
						curl.setOpt('CUSTOMREQUEST', method.toUpperCase());
						break;
				}

				// Set headers
				const headers: string[] = [];
				if (mergedOptions.headers)
				{
					Object.entries(mergedOptions.headers).forEach(([key, value]) =>
					{
						headers.push(`${key}: ${value}`);
					});
				}

				if (data && (method === 'POST' || method === 'PUT'))
				{
					// Set content type if not already set
					if (!headers.some(h => h.toLowerCase().startsWith('content-type:')))
					{
						headers.push('Content-Type: application/json');
					}

					// Set POST data
					const postData = typeof data === 'string' ? data : JSON.stringify(data);
					curl.setOpt('POSTFIELDS', postData);
				}

				if (headers.length > 0)
				{
					curl.setOpt('HTTPHEADER', headers);
				}

				// Set proxy if provided
				if (mergedOptions.proxy)
				{
					curl.setOpt('PROXY', mergedOptions.proxy);
				}

				// Enable automatic decompression
				curl.setOpt('ACCEPT_ENCODING', '');

				// Collect response data
				let responseData = '';
				const responseHeaders: Record<string, string> = {};
				let statusCode = 0;

				curl.on('data', (chunk: Buffer) =>
				{
					responseData += chunk.toString();
					return chunk.length;
				});

				curl.on('header', (chunk: Buffer) =>
				{
					const headerStr = chunk.toString();
					if (headerStr.includes(':'))
					{
						const [key, ...valueParts] = headerStr.split(':');
						const value = valueParts.join(':').trim();
						responseHeaders[key.trim().toLowerCase()] = value;
					}
					return chunk.length;
				});

				curl.on('end', (
					status: number,
					// data: unknown,
					// headers: unknown,
				) =>
				{
					statusCode = status;

					try
					{
						// Parse JSON response if content type is JSON
						let parsedData: T = responseData as unknown as T;
						const contentType = responseHeaders['content-type'] || '';
						if (contentType.includes('application/json') && responseData)
						{
							parsedData = JSON.parse(responseData) as T;
						}

						curl.close();

						if (statusCode >= 200 && statusCode < 300)
						{
							resolve({
								statusCode,
								data: parsedData,
								headers: responseHeaders,
							});
						}
						else
						{
							const error = new Error(`Request failed with status ${statusCode}`) as HttpErrorInterface;
							error.statusCode = statusCode;
							error.response = {
								data: parsedData,
								status: statusCode,
								headers: responseHeaders,
							};
							reject(error);
						}
					}
					catch (parseError)
					{
						curl.close();
						reject(parseError);
					}
				});

				curl.on('error', (error: Error) =>
				{
					curl.close();
					this.logger.error({ error, method, url }, `${method} request to ${url} failed`);
					reject(error);
				});

				curl.perform();
			}
			catch (error)
			{
				curl.close();
				this.logger.error({ error, method, url }, `${method} request to ${url} failed`);
				reject(error);
			}
		});
	}
}

export type {
	HttpClientOptionsType,
	HttpResponseType,
	HttpErrorInterface,
};

export default HttpClient;
