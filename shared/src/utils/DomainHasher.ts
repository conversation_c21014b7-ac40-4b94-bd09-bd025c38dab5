import crypto from 'crypto';

/**
 * Domain Hasher Utility for ScyllaDB Optimization
 *
 * Provides consistent hashing for domain names to enable:
 * - Even data distribution across ScyllaDB cluster
 * - Avoiding hot partitions for popular domains
 * - Predictable partition key generation
 */
export class DomainHasher {
    /**
     * Number of buckets for distribution (adjust based on cluster size)
     * Rule of thumb: 100-1000 buckets per node
     * For 10 nodes: 1000-10000 buckets
     */
    private static readonly BUCKET_COUNT = 10000;

    /**
     * Generate a hash bucket for a domain
     * Uses SHA-256 for consistency across services
     *
     * @param domain - Domain name to hash
     * @returns Bucket number (0 to BUCKET_COUNT-1)
     */
    static getDomainBucket(domain: string): number {
        if (!domain || typeof domain !== 'string') {
            throw new Error('Domain must be a non-empty string');
        }

        // Normalize domain: lowercase, remove trailing dots
        const normalizedDomain = domain.toLowerCase().replace(/\.$/, '');

        // Generate SHA-256 hash
        const hash = crypto.createHash('sha256').update(normalizedDomain).digest();

        // Convert first 4 bytes to unsigned integer
        const hashInt = hash.readUInt32BE(0);

        // Return bucket number
        return hashInt % this.BUCKET_COUNT;
    }

    /**
     * Generate a 64-bit hash for domain (alternative approach)
     * Uses murmurhash3 algorithm simulation
     *
     * @param domain - Domain name to hash
     * @returns 64-bit hash as bigint
     */
    static getDomainHash(domain: string): bigint {
        if (!domain || typeof domain !== 'string') {
            throw new Error('Domain must be a non-empty string');
        }

        const normalizedDomain = domain.toLowerCase().replace(/\.$/, '');

        // Simple murmur-like hash implementation
        let hash = 0x811c9dc5; // FNV offset basis

        for (let i = 0; i < normalizedDomain.length; i++) {
            hash ^= normalizedDomain.charCodeAt(i);
            hash = (hash * 0x01000193) >>> 0; // FNV prime, keep 32-bit
        }

        // Extend to 64-bit
        const hash64 = BigInt(hash) << 32n | BigInt(hash ^ 0xdeadbeef);

        return hash64;
    }

    /**
     * Validate that bucket distribution is working correctly
     * Use this for testing/monitoring
     *
     * @param domains - Array of domain names
     * @returns Distribution statistics
     */
    static analyzeBucketDistribution(domains: string[]): {
        totalDomains: number;
        uniqueBuckets: number;
        avgDomainsPerBucket: number;
        maxDomainsInBucket: number;
        minDomainsInBucket: number;
        bucketDistribution: Map<number, number>;
    } {
        const bucketCounts = new Map<number, number>();

        domains.forEach(domain => {
            const bucket = this.getDomainBucket(domain);
            bucketCounts.set(bucket, (bucketCounts.get(bucket) || 0) + 1);
        });

        const counts = Array.from(bucketCounts.values());

        return {
            totalDomains: domains.length,
            uniqueBuckets: bucketCounts.size,
            avgDomainsPerBucket: domains.length / bucketCounts.size,
            maxDomainsInBucket: Math.max(...counts),
            minDomainsInBucket: Math.min(...counts),
            bucketDistribution: bucketCounts
        };
    }

    /**
     * Get bucket count configuration
     */
    static getBucketCount(): number {
        return this.BUCKET_COUNT;
    }
}

// Example usage:
// const bucket = DomainHasher.getDomainBucket('google.com'); // Returns: 1234
// const hash = DomainHasher.getDomainHash('google.com');     // Returns: 123456789012345678n