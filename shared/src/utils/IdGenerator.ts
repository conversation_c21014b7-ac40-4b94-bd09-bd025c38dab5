import crypto from 'crypto';

/**
 * Shared ID generation utilities to replace duplicated patterns
 * across services like Date.now() + Math.random() combinations
 */

type IdOptions = {
	prefix?: string;
	length?: number;
	useTimestamp?: boolean;
	useCrypto?: boolean;
};

class IdGenerator {
	/**
	 * Generate a unique ID with timestamp and random component
	 * Replaces patterns like: `prefix-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
	 */
	static generate(options: IdOptions = {}): string {
		const {
			prefix = '',
			length = 9,
			useTimestamp = true,
			useCrypto = false,
		} = options;

		const parts: string[] = [];

		if (prefix) {
			parts.push(prefix);
		}

		if (useTimestamp) {
			parts.push(Date.now().toString(36));
		}

		// Generate random component
		if (useCrypto) {
			const bytes = Math.ceil(length * 0.75); // Base64 encoding efficiency
			parts.push(crypto.randomBytes(bytes).toString('base64url').substring(0, length));
		} else {
			parts.push(Math.random().toString(36).substring(2, 2 + length));
		}

		return parts.join('-');
	}

	/**
	 * Generate a recovery ID
	 * Replaces: `recovery-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
	 */
	static recoveryId(): string {
		return this.generate({ prefix: 'recovery' });
	}

	/**
	 * Generate an error ID
	 * Replaces: `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
	 */
	static errorId(): string {
		return this.generate({ prefix: 'error' });
	}

	/**
	 * Generate an alert ID
	 * Replaces: `alert-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
	 */
	static alertId(): string {
		return this.generate({ prefix: 'alert' });
	}

	/**
	 * Generate a batch ID
	 * Replaces: `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
	 */
	static batchId(): string {
		return this.generate({ prefix: 'batch' }).replace(/-/g, '_');
	}

	/**
	 * Generate a trigger ID
	 * Replaces: `trigger_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
	 */
	static triggerId(): string {
		return this.generate({ prefix: 'trigger' }).replace(/-/g, '_');
	}

	/**
	 * Generate a pattern ID
	 * Replaces: `pattern-${Date.now()}-${Math.random().toString(36).substr(2, 6)}`
	 */
	static patternId(): string {
		return this.generate({ prefix: 'pattern', length: 6 });
	}

	/**
	 * Generate a temporary file/directory name
	 * Replaces: `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}${extension}`
	 */
	static tempName(prefix: string = 'temp', extension: string = ''): string {
		const id = this.generate({ prefix, useTimestamp: true }).replace(/-/g, '_');
		return extension ? `${id}${extension}` : id;
	}

	/**
	 * Generate a secure ID using crypto
	 * For sensitive operations requiring cryptographically secure randomness
	 */
	static secureId(prefix?: string): string {
		return this.generate({ prefix, useCrypto: true, length: 16 });
	}

	/**
	 * Generate an operation ID for tracking
	 * Replaces: `${operationName}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
	 */
	static operationId(operationName: string): string {
		return this.generate({ prefix: operationName });
	}

	/**
	 * Generate a simple random string without timestamp
	 * For cases where timestamp is not needed
	 */
	static randomString(length: number = 9): string {
		return this.generate({ useTimestamp: false, length });
	}

	/**
	 * Generate with custom separator
	 * For legacy compatibility with underscore separators
	 */
	static generateWithSeparator(separator: string, options: IdOptions = {}): string {
		return this.generate(options).replace(/-/g, separator);
	}

	// Specific ID generators for common patterns found in codebase
	
	/**
	 * Generate admin service IDs
	 * Replaces: `admin-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
	 */
	static adminId(): string {
		return this.generate({ prefix: 'admin' });
	}

	/**
	 * Generate seeder service IDs
	 * Replaces: `seeder-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
	 */
	static seederId(): string {
		return this.generate({ prefix: 'seeder' });
	}

	/**
	 * Generate web app IDs
	 * Replaces: `web-app-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
	 */
	static webAppId(): string {
		return this.generate({ prefix: 'web-app' });
	}


	/**
	 * Generate query IDs
	 * Replaces: `query_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
	 */
	static queryId(): string {
		return this.generateWithSeparator('_', { prefix: 'query' });
	}

	/**
	 * Generate backup IDs
	 * Replaces: `backup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
	 */
	static backupId(): string {
		return this.generateWithSeparator('_', { prefix: 'backup' });
	}

	/**
	 * Generate connection IDs for SSE/WebSocket
	 * Replaces: `sse-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
	 */
	static connectionId(type: 'sse' | 'ws' = 'sse'): string {
		return this.generate({ prefix: type });
	}
}

/**
 * Utility functions for jitter calculations
 * Replaces duplicated jitter logic across services
 */
class JitterUtils {
	/**
	 * Add jitter to a delay value
	 * Replaces: delay += (Math.random() - 0.5) * 2 * jitterRange
	 */
	static addJitter(delay: number, jitterPercent: number = 0.1): number {
		const jitterRange = delay * jitterPercent;
		return delay + (Math.random() - 0.5) * 2 * jitterRange;
	}

	/**
	 * Apply proportional jitter
	 * Replaces: delay *= (0.8 + Math.random() * 0.4)
	 */
	static proportionalJitter(delay: number, minFactor: number = 0.8, maxFactor: number = 1.2): number {
		const range = maxFactor - minFactor;
		return delay * (minFactor + Math.random() * range);
	}

	/**
	 * Apply exponential backoff with jitter
	 */
	static exponentialBackoffWithJitter(baseDelay: number, attempt: number, maxDelay: number = 30000, jitterPercent: number = 0.1): number {
		const exponentialDelay = Math.min(baseDelay * Math.pow(2, attempt), maxDelay);
		return this.addJitter(exponentialDelay, jitterPercent);
	}
}

export { IdGenerator, JitterUtils };
export type { IdOptions };