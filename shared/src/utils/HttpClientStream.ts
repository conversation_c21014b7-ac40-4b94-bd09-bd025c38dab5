/**
 * HTTP Client with Streaming Support using node-libcurl
 * High-performance streaming implementation for large file downloads
 */

import { Curl, CurlCode, Easy } from 'node-libcurl';
import { createWriteStream, createReadStream, statSync } from 'fs';
import { Readable, Writable } from 'stream';
import { pipeline } from 'stream/promises';
import { logger as sharedLogger } from './Logger';

const logger = sharedLogger.getLogger('HttpClientStream');

type StreamOptions = {
	timeout?: number;
	headers?: Record<string, string>;
	proxy?: string;
	onProgress?: (downloaded: number, total: number) => void;
	maxRedirects?: number;
	userAgent?: string;
};

export class HttpClientStream
{
	private defaultTimeout: number = 300000; // 5 minutes for large files
	private userAgent: string = 'Domain-Seeder/1.0 (node-libcurl streaming)';

	/**
	 * Download a file using streaming to avoid memory issues
	 * Uses libcurl's WRITEFUNCTION for efficient streaming
	 */
	async downloadToFile(
		url: string,
		outputPath: string,
		options: StreamOptions = {}
	): Promise<void>
	{
		return new Promise((resolve, reject) =>
		{
			const curl = new Curl();
			const writeStream = createWriteStream(outputPath);
			let downloadedBytes = 0;
			let totalBytes = 0;
			let lastProgressUpdate = 0;

			try
			{
				// Basic options
				curl.setOpt('URL', url);
				curl.setOpt('FOLLOWLOCATION', true);
				curl.setOpt('MAXREDIRS', options.maxRedirects || 5);
				curl.setOpt('TIMEOUT_MS', options.timeout || this.defaultTimeout);
				curl.setOpt('USERAGENT', options.userAgent || this.userAgent);

				// Enable progress callback if provided
				if (options.onProgress)
				{
					curl.setOpt('NOPROGRESS', false);
					curl.setOpt('PROGRESSFUNCTION', (dltotal: number, dlnow: number) =>
					{
						totalBytes = dltotal;
						downloadedBytes = dlnow;

						// Throttle progress updates to every 100ms
						const now = Date.now();
						if (now - lastProgressUpdate > 100)
						{
							options.onProgress!(dlnow, dltotal);
							lastProgressUpdate = now;
						}

						return 0; // Return 0 to continue
					});
				}

				// Set headers
				if (options.headers)
				{
					const headers: string[] = [];
					Object.entries(options.headers).forEach(([key, value]) =>
					{
						headers.push(`${key}: ${value}`);
					});
					curl.setOpt('HTTPHEADER', headers);
				}

				// Set proxy if provided
				if (options.proxy)
				{
					curl.setOpt('PROXY', options.proxy);
				}

				// Stream data directly to file using WRITEFUNCTION
				curl.setOpt('WRITEFUNCTION', (buffer: Buffer, size: number, nmemb: number) =>
				{
					const chunk = buffer.subarray(0, size * nmemb);

					// Write to file stream
					if (!writeStream.write(chunk))
					{
						// Handle backpressure - pause curl if write buffer is full
						curl.pause(1); // Pause receiving

						writeStream.once('drain', () =>
						{
							curl.pause(0); // Resume receiving
						});
					}

					return size * nmemb; // Return number of bytes handled
				});

				// Handle completion
				curl.on('end', (statusCode: number, data: any, headers: any) =>
				{
					curl.close();
					writeStream.end();

					if (statusCode >= 200 && statusCode < 300)
					{
						logger.info(`Successfully downloaded ${outputPath} (${downloadedBytes} bytes)`);
						resolve();
					}
					else
					{
						writeStream.destroy();
						reject(new Error(`Download failed with status ${statusCode}`));
					}
				});

				// Handle errors
				curl.on('error', (error: Error, errorCode: CurlCode) =>
				{
					curl.close();
					writeStream.destroy();
					logger.error({ error }, `Download failed for ${url}`);
					reject(new Error(`Download failed: ${error.message} (code: ${errorCode})`));
				});

				// Start the request
				curl.perform();

			}
			catch (error)
			{
				curl.close();
				writeStream.destroy();
				logger.error({ error }, `Failed to initialize download for ${url}`);
				reject(error);
			}
		});
	}

	/**
	 * Upload a file using streaming
	 * Uses READFUNCTION for efficient streaming uploads
	 */
	async uploadFromFile(
		url: string,
		filePath: string,
		options: StreamOptions = {}
	): Promise<any>
	{
		return new Promise((resolve, reject) =>
		{
			const curl = new Curl();
			const fileStream = createReadStream(filePath);
			const fileSize = statSync(filePath).size;
			let uploadedBytes = 0;
			let responseData = '';

			try
			{
				// Basic options
				curl.setOpt('URL', url);
				curl.setOpt('UPLOAD', true);
				curl.setOpt('TIMEOUT_MS', options.timeout || this.defaultTimeout);
				curl.setOpt('USERAGENT', options.userAgent || this.userAgent);
				curl.setOpt('INFILESIZE_LARGE', fileSize);

				// Set headers
				const headers: string[] = ['Content-Type: application/octet-stream'];
				if (options.headers)
				{
					Object.entries(options.headers).forEach(([key, value]) =>
					{
						headers.push(`${key}: ${value}`);
					});
				}
				curl.setOpt('HTTPHEADER', headers);

				// Set proxy if provided
				if (options.proxy)
				{
					curl.setOpt('PROXY', options.proxy);
				}

				// Progress callback for uploads
				if (options.onProgress)
				{
					curl.setOpt('NOPROGRESS', false);
					curl.setOpt('PROGRESSFUNCTION', (ultotal: number, ulnow: number) =>
					{
						uploadedBytes = ulnow;
						options.onProgress!(ulnow, ultotal);
						return 0;
					});
				}

				// Stream data from file using READFUNCTION
				let isReading = false;
				curl.setOpt('READFUNCTION', (buffer: Buffer, size: number, nmemb: number) =>
				{
					if (isReading) return 0; // Prevent re-entrance
					isReading = true;

					const maxBytes = size * nmemb;
					const chunk = fileStream.read(maxBytes);

					if (chunk === null)
					{
						// No data available right now, return 0 to pause
						isReading = false;
						return 0;
					}

					if (chunk.length === 0)
					{
						// End of file
						isReading = false;
						return 0;
					}

					// Copy data to libcurl's buffer
					chunk.copy(buffer, 0, 0, chunk.length);
					isReading = false;
					return chunk.length;
				});

				// Collect response data
				curl.setOpt('WRITEFUNCTION', (buffer: Buffer, size: number, nmemb: number) =>
				{
					const chunk = buffer.toString('utf8', 0, size * nmemb);
					responseData += chunk;
					return size * nmemb;
				});

				// Handle completion
				curl.on('end', (statusCode: number) =>
				{
					curl.close();
					fileStream.destroy();

					if (statusCode >= 200 && statusCode < 300)
					{
						logger.info(`Successfully uploaded ${filePath} (${uploadedBytes} bytes)`);
						try
						{
							resolve(JSON.parse(responseData));
						}
						catch
						{
							resolve(responseData);
						}
					}
					else
					{
						reject(new Error(`Upload failed with status ${statusCode}: ${responseData}`));
					}
				});

				// Handle errors
				curl.on('error', (error: Error, errorCode: CurlCode) =>
				{
					curl.close();
					fileStream.destroy();
					logger.error({ error }, `Upload failed for ${filePath}`);
					reject(new Error(`Upload failed: ${error.message} (code: ${errorCode})`));
				});

				// Start the request
				curl.perform();

			}
			catch (error)
			{
				curl.close();
				fileStream.destroy();
				logger.error({ error }, `Failed to initialize upload for ${filePath}`);
				reject(error);
			}
		});
	}

	/**
	 * Stream process a large response line by line
	 * Uses WRITEFUNCTION to process data as it arrives
	 */
	async *streamLines(
		url: string,
		options: StreamOptions = {}
	): AsyncGenerator<string, void, unknown>
	{
		const curl = new Curl();
		let buffer = '';
		let isDone = false;
		const lines: string[] = [];
		let resolver: ((value: IteratorResult<string, void>) => void) | null = null;
		let rejecter: ((error: Error) => void) | null = null;

		try
		{
			// Basic options
			curl.setOpt('URL', url);
			curl.setOpt('FOLLOWLOCATION', true);
			curl.setOpt('MAXREDIRS', options.maxRedirects || 5);
			curl.setOpt('TIMEOUT_MS', options.timeout || this.defaultTimeout);
			curl.setOpt('USERAGENT', options.userAgent || this.userAgent);

			// Set headers
			if (options.headers)
			{
				const headers: string[] = [];
				Object.entries(options.headers).forEach(([key, value]) =>
				{
					headers.push(`${key}: ${value}`);
				});
				curl.setOpt('HTTPHEADER', headers);
			}

			// Set proxy
			if (options.proxy)
			{
				curl.setOpt('PROXY', options.proxy);
			}

			// Process data as it arrives
			curl.setOpt('WRITEFUNCTION', (chunk: Buffer, size: number, nmemb: number) =>
			{
				const data = chunk.toString('utf8', 0, size * nmemb);
				buffer += data;

				// Split into lines
				const newLines = buffer.split('\n');
				buffer = newLines.pop() || '';

				// Add complete lines to queue
				for (const line of newLines)
				{
					if (line.trim())
					{
						lines.push(line);

						// If there's a pending read, resolve it
						if (resolver)
						{
							const nextLine = lines.shift();
							if (nextLine !== undefined)
							{
								const res = resolver;
								resolver = null;
								res({ value: nextLine, done: false });
							}
						}
					}
				}

				return size * nmemb;
			});

			// Handle completion
			curl.on('end', (statusCode: number) =>
			{
				curl.close();

				if (statusCode >= 200 && statusCode < 300)
				{
					// Add any remaining data in buffer
					if (buffer.trim())
					{
						lines.push(buffer);
					}
					isDone = true;

					// Resolve any pending read with done
					if (resolver)
					{
						const res = resolver;
						resolver = null;
						const nextLine = lines.shift();
						if (nextLine !== undefined)
						{
							res({ value: nextLine, done: false });
						}
						else
						{
							res({ value: undefined, done: true });
						}
					}
				}
				else
				{
					const error = new Error(`Stream failed with status ${statusCode}`);
					if (rejecter)
					{
						rejecter(error);
					}
				}
			});

			// Handle errors
			curl.on('error', (error: Error, errorCode: CurlCode) =>
			{
				curl.close();
				isDone = true;
				const err = new Error(`Stream failed: ${error.message} (code: ${errorCode})`);
				if (rejecter)
				{
					rejecter(err);
				}
			});

			// Start the request
			curl.perform();

			// Async generator implementation
			while (!isDone || lines.length > 0)
			{
				// If we have lines queued, yield them
				if (lines.length > 0)
				{
					yield lines.shift()!;
				}
				else if (!isDone)
				{
					// Wait for more data; resolve is called when next line is ready or stream ends
					await new Promise<void>((resolve, reject) =>
					{
						resolver = (result: IteratorResult<string, void>) =>
						{
							if (!result.done && result.value !== undefined)
							{
								// push back to the queue to be yielded in next loop iteration
								lines.unshift(result.value);
							}
							resolve();
						};
						rejecter = reject;
					});
				}
			}

		}
		catch (error)
		{
			curl.close();
			logger.error({ error }, `Failed to stream from ${url}`);
			throw error;
		}
	}
}

// Export types
export type { StreamOptions };

// Export singleton instance
export const httpClientStream = new HttpClientStream();

export default HttpClientStream;
