import type { Logger as <PERSON><PERSON><PERSON>ogger } from 'pino';

type LogData = Record<string, unknown> | Error | undefined;

type LogLevel = 'trace' | 'debug' | 'info' | 'warn' | 'error' | 'fatal';

interface IsomorphicLogger {
	trace: (msg: string, data?: LogData) => void;
	debug: (msg: string, data?: LogData) => void;
	info: (msg: string, data?: LogData) => void;
	warn: (msg: string, data?: LogData) => void;
	error: (msg: string, data?: LogData) => void;
	fatal: (msg: string, data?: LogData) => void;
	child: (bindings: Record<string, unknown>) => IsomorphicLogger;
}

class BrowserLogger implements IsomorphicLogger {
	private context: Record<string, unknown>;

	constructor(context: Record<string, unknown> = {}) {
		this.context = context;
	}

	private log(level: LogLevel, msg: string, data?: LogData) {
		const timestamp = new Date().toISOString();
		const contextStr = Object.keys(this.context).length > 0 ? ` ${JSON.stringify(this.context)}` : '';
		const message = `[${timestamp}] ${level.toUpperCase()}${contextStr}: ${msg}`;

		const consoleMethod = level === 'trace' ? 'debug' : level === 'fatal' ? 'error' : level;
		if (data !== undefined) {
			console[consoleMethod](message, data);
		} else {
			console[consoleMethod](message);
		}
	}

	trace(msg: string, data?: LogData) {
		if (process.env.NODE_ENV === 'development') {
			this.log('trace', msg, data);
		}
	}

	debug(msg: string, data?: LogData) {
		if (process.env.NODE_ENV === 'development') {
			this.log('debug', msg, data);
		}
	}

	info(msg: string, data?: LogData) {
		this.log('info', msg, data);
	}

	warn(msg: string, data?: LogData) {
		this.log('warn', msg, data);
	}

	error(msg: string, data?: LogData) {
		this.log('error', msg, data);
	}

	fatal(msg: string, data?: LogData) {
		this.log('fatal', msg, data);
	}

	child(bindings: Record<string, unknown>): IsomorphicLogger {
		return new BrowserLogger({ ...this.context, ...bindings });
	}
}

export function createIsomorphicLogger(serviceName: string): IsomorphicLogger {
	// Always use browser logger for now - we'll rely on server-specific imports for API routes
	return new BrowserLogger({ service: serviceName });
}

export type { IsomorphicLogger };