/**
 * Async utility functions that appear duplicated across services
 */
class AsyncUtils {
	/**
	 * Sleep/delay utility function
	 * Replaces: new Promise(resolve => setTimeout(resolve, ms))
	 */
	static sleep(ms: number): Promise<void> {
		return new Promise(resolve => setTimeout(resolve, ms));
	}

	/**
	 * Sleep with random jitter
	 * Useful for rate limiting and avoiding thundering herd
	 */
	static sleepWithJitter(ms: number, jitterPercent: number = 0.1): Promise<void> {
		const jitterRange = ms * jitterPercent;
		const jitteredMs = ms + (Math.random() - 0.5) * 2 * jitterRange;
		return this.sleep(Math.max(0, jitteredMs));
	}

	/**
	 * Timeout wrapper for promises
	 * Throws error if promise doesn't resolve within timeout
	 */
	static withTimeout<T>(promise: Promise<T>, timeoutMs: number, errorMessage?: string): Promise<T> {
		return Promise.race([
			promise,
			new Promise<never>((_, reject) =>
				setTimeout(() => reject(new Error(errorMessage || `Operation timed out after ${timeoutMs}ms`)), timeoutMs)
			)
		]);
	}

	/**
	 * Retry an async operation with exponential backoff
	 */
	static async retry<T>(
		operation: () => Promise<T>,
		maxAttempts: number = 3,
		baseDelayMs: number = 1000,
		exponentialBackoff: boolean = true
	): Promise<T> {
		let lastError: Error;

		for (let attempt = 1; attempt <= maxAttempts; attempt++) {
			try {
				return await operation();
			} catch (error) {
				lastError = error as Error;

				if (attempt === maxAttempts) {
					throw lastError;
				}

				const delay = exponentialBackoff
					? baseDelayMs * Math.pow(2, attempt - 1)
					: baseDelayMs;

				await this.sleep(delay);
			}
		}

		throw lastError!;
	}

	/**
	 * Execute promises in batches with controlled concurrency
	 */
	static async batch<T, R>(
		items: T[],
		processor: (item: T) => Promise<R>,
		batchSize: number = 10,
		delayBetweenBatches: number = 0
	): Promise<R[]> {
		const results: R[] = [];

		for (let i = 0; i < items.length; i += batchSize) {
			const batch = items.slice(i, i + batchSize);
			const batchPromises = batch.map(processor);
			const batchResults = await Promise.all(batchPromises);

			results.push(...batchResults);

			// Add delay between batches if specified
			if (delayBetweenBatches > 0 && i + batchSize < items.length) {
				await this.sleep(delayBetweenBatches);
			}
		}

		return results;
	}

	/**
	 * Execute promises with limited concurrency
	 */
	static async limitConcurrency<T, R>(
		items: T[],
		processor: (item: T) => Promise<R>,
		maxConcurrency: number = 5
	): Promise<R[]> {
		const results: R[] = new Array(items.length);
		const executing: Promise<void>[] = [];

		for (let i = 0; i < items.length; i++) {
			const promise = processor(items[i]).then(result => {
				results[i] = result;
			});

			executing.push(promise);

			if (executing.length >= maxConcurrency) {
				await Promise.race(executing);
				// Remove completed promises
				for (let j = executing.length - 1; j >= 0; j--) {
					if (await Promise.race([executing[j].then(() => true), Promise.resolve(false)])) {
						executing.splice(j, 1);
					}
				}
			}
		}

		await Promise.all(executing);
		return results;
	}
}

export default AsyncUtils;
export { AsyncUtils };