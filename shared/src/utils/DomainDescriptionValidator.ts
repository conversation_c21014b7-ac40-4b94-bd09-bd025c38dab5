import { readFileSync } from 'node:fs';
import { fileURLToPath } from 'node:url';
import { resolve, dirname } from 'node:path';

import type { ValidateFunction, ErrorObject, JSONSchemaType } from 'ajv';
import Ajv from 'ajv';
import addFormats from 'ajv-formats';
import type DomainDescriptionInterface from '../models/DomainDescription';
import iso3166Countries from '../../data/iso-3166-countries.json';
import iso639Languages from '../../data/iso-639-languages.json';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const schemaPath = resolve(__dirname, '../../../docs/domain-description.schema.json');

type ValidationResult =
{
	ok: boolean;
	errors?: ErrorObject[];
	warnings?: string[];
};

type EnhancedValidationResult = ValidationResult & {
	sanitizedData?: DomainDescriptionInterface;
};

class DomainDescriptionValidator
{
	private static instance: DomainDescriptionValidator | null = null;

	private ajv: Ajv;

	private validateFn: ValidateFunction<DomainDescriptionInterface>;

	private iso3166Countries: Set<string> = new Set();

	private iso639Languages: Set<string> = new Set();

	private constructor()
	{
		this.ajv = new Ajv({
			strict: true,
			allErrors: true,
			loadSchema: undefined,
		});

		addFormats(this.ajv);
		const schema = JSON.parse(readFileSync(schemaPath, 'utf-8')) as JSONSchemaType<DomainDescriptionInterface>;
		// Remove $schema to avoid network fetch of meta-schema
		if ((schema as { $schema?: string }).$schema) delete (schema as { $schema?: string }).$schema;
		this.validateFn = this.ajv.compile<DomainDescriptionInterface>(schema);

		// Initialize ISO code sets for validation
		this.initializeISOCodes();
	}

	private initializeISOCodes(): void
	{
		// Load ISO codes from data files
		this.iso3166Countries = new Set(iso3166Countries);
		this.iso639Languages = new Set(iso639Languages);
	}

	static get(): DomainDescriptionValidator
	{
		if (!this.instance) this.instance = new DomainDescriptionValidator();
		return this.instance;
	}

	validate(obj: unknown): ValidationResult
	{
		const ok = this.validateFn(obj as DomainDescriptionInterface) as boolean;
		const errors = this.validateFn.errors || [];
		const warnings: string[] = [];

		// Additional custom validations - run regardless of schema validation result
		if (obj && typeof obj === 'object')
		{
			const domain = obj as DomainDescriptionInterface;

			// Validate ISO codes
			if (domain.metadata?.country && !this.iso3166Countries.has(domain.metadata.country))
			{
				warnings.push(`Invalid ISO 3166-1 country code: ${domain.metadata.country}`);
			}

			if (domain.metadata?.language && !this.iso639Languages.has(domain.metadata.language))
			{
				warnings.push(`Invalid ISO 639-1 language code: ${domain.metadata.language}`);
			}

			// Validate IDN structure
			if (domain.metadata?.idn)
			{
				if (domain.metadata.idn.isIdn && !domain.metadata.idn.unicode)
				{
					warnings.push('IDN marked as true but unicode field is missing');
				}
				if (!domain.metadata.idn.isIdn && domain.metadata.idn.unicode)
				{
					warnings.push('IDN marked as false but unicode field is present');
				}
			}

			// Enhanced validation for preGenerated content
			if (domain.metadata?.preGenerated)
			{
				// Tags are mandatory for preGenerated content
				if (!domain.metadata.tags || domain.metadata.tags.length === 0)
				{
					warnings.push('PreGenerated content requires tags array to be present and non-empty');
				}
				else if (domain.metadata.tags.length < 5)
				{
					warnings.push(`PreGenerated content requires at least 5 tags, found ${domain.metadata.tags.length}`);
				}
				else if (domain.metadata.tags.length > 12)
				{
					warnings.push(`PreGenerated content allows maximum 12 tags, found ${domain.metadata.tags.length}`);
				}

				// Summary is mandatory for preGenerated content
				if (!domain.overview?.summary)
				{
					warnings.push('PreGenerated content requires summary to be present');
				}
				else
				{
					const wordCount = domain.overview.summary.trim().split(/\s+/).filter((word: string) => word.length > 0).length;
					if (wordCount < 320)
					{
						warnings.push(`PreGenerated content requires at least 320 words in summary, found ${wordCount}`);
					}

					// SEO content quality checks
					const seoQualityResult = this.validateSEOContentQuality(domain.overview.summary);
					if (seoQualityResult.warnings.length > 0)
					{
						warnings.push(...seoQualityResult.warnings);
					}
				}

				// Category is mandatory for preGenerated content
				if (!domain.metadata.category?.primary)
				{
					warnings.push('PreGenerated content requires primary category to be present');
				}
			}

			// General content quality validation (applies to all content, not just preGenerated)
			if (domain.overview?.summary)
			{
				const readabilityResult = this.validateReadabilityStandards(domain.overview.summary);
				if (readabilityResult.warnings.length > 0)
				{
					warnings.push(...readabilityResult.warnings);
				}
			}

			// Validate registration timestamps format
			if (domain.metadata?.registration)
			{
				const reg = domain.metadata.registration;
				if (reg.firstRegisteredAt && !this.isValidISODateTime(reg.firstRegisteredAt))
				{
					warnings.push(`Invalid firstRegisteredAt timestamp format: ${reg.firstRegisteredAt}`);
				}
				if (reg.lastUpdatedAt && !this.isValidISODateTime(reg.lastUpdatedAt))
				{
					warnings.push(`Invalid lastUpdatedAt timestamp format: ${reg.lastUpdatedAt}`);
				}
				if (reg.expiresAt && !this.isValidISODateTime(reg.expiresAt))
				{
					warnings.push(`Invalid expiresAt timestamp format: ${reg.expiresAt}`);
				}
			}

			// Enhanced IDN validation
			if (domain.metadata?.idn)
			{
				const idn = domain.metadata.idn;
				if (idn.isIdn)
				{
					if (!idn.unicode)
					{
						warnings.push('IDN marked as true but unicode field is missing');
					}
					if (!idn.ascii)
					{
						warnings.push('IDN marked as true but ascii field is missing');
					}
					if (idn.unicode && idn.ascii && idn.unicode === idn.ascii)
					{
						warnings.push('IDN unicode and ascii fields should be different when isIdn is true');
					}
				}
				else
					if (idn.unicode && idn.unicode !== domain.metadata.domain)
					{
						warnings.push('IDN marked as false but unicode field differs from domain');
					}
			}

			// Enhanced social arrays validation
			if (domain.reputation?.social)
			{
				const social = domain.reputation.social;
				const socialPlatforms = ['twitter', 'facebook', 'linkedin', 'instagram'] as const;

				for (const platform of socialPlatforms)
				{
					const accounts = social[platform];
					if (accounts)
					{
						if (!Array.isArray(accounts))
						{
							warnings.push(`Social ${platform} field must be an array`);
						}
						else
						{
							// Validate URL format for social accounts
							for (const account of accounts)
							{
								if (typeof account !== 'string' || account.trim().length === 0)
								{
									warnings.push(`Social ${platform} contains invalid account: ${account}`);
								}
							}
						}
					}
				}
			}

			// Category hierarchy validation
			if (domain.metadata?.category)
			{
				const category = domain.metadata.category;
				if (category.primary === undefined || category.primary === null)
				{
					warnings.push('Primary category is required');
				}
				else if (typeof category.primary !== 'string')
				{
					warnings.push('Primary category must be a string');
				}
				else if (category.primary.trim().length === 0)
				{
					warnings.push('Primary category must be a non-empty string');
				}

				if (category.secondary)
				{
					if (typeof category.secondary !== 'string')
					{
						warnings.push('Secondary category must be a string');
					}
					else if (category.secondary === category.primary)
					{
						warnings.push('Secondary category should be different from primary category');
					}
				}
			}
		}

		return ({
			ok,
			errors: errors.length > 0 ? errors : undefined,
			warnings: warnings.length > 0 ? warnings : undefined,
		});
	}

	validateEnhanced(obj: unknown): EnhancedValidationResult
	{
		const result = this.validate(obj);

		if (obj && typeof obj === 'object')
		{
			// Sanitize and normalize data even if validation has warnings
			const sanitized = this.sanitizeData(obj as DomainDescriptionInterface);
			return { ...result, sanitizedData: sanitized };
		}

		return result;
	}

	private isValidISODateTime(dateString: string): boolean
	{
		// Accept RFC 3339 date-time format (a subset of ISO 8601) with optional fractional seconds
		// Examples: 2020-01-01T00:00:00Z, 2020-01-01T00:00:00.000Z, 2020-01-01T00:00:00+00:00, 2020-01-01T00:00:00-05:30
		const rfc3339DateTime = /^(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):(\d{2})(?:\.\d+)?(?:Z|[+-]\d{2}:\d{2})$/;
		if (!rfc3339DateTime.test(dateString))
		{
			return false;
		}

		// Additionally, ensure the date components are valid by attempting to parse
		const parsed = new Date(dateString);
		return !Number.isNaN(parsed.getTime());
	}

	private sanitizeData(domain: DomainDescriptionInterface): DomainDescriptionInterface
	{
		const sanitized = { ...domain };

		// Normalize country code to uppercase
		if (sanitized.metadata.country)
		{
			sanitized.metadata.country = sanitized.metadata.country.toUpperCase();
		}

		// Normalize language code to lowercase
		if (sanitized.metadata.language)
		{
			sanitized.metadata.language = sanitized.metadata.language.toLowerCase();
		}

		// Ensure tags are unique and trimmed
		if (sanitized.metadata.tags)
		{
			sanitized.metadata.tags = [...new Set(sanitized.metadata.tags.map((tag: string) => tag.trim()))];
		}

		// Validate and normalize social arrays
		if (sanitized.reputation?.social)
		{
			const social = sanitized.reputation.social;
			const socialPlatforms = ['twitter', 'facebook', 'linkedin', 'instagram'] as const;

			for (const platform of socialPlatforms)
			{
				if (social[platform] && Array.isArray(social[platform]))
				{
					// Normalize: keep only non-empty trimmed strings and deduplicate while preserving order
					const cleaned = (social[platform] as unknown[])
						.filter((account): account is string => typeof account === 'string')
						.map(account => account.trim())
						.filter(account => account.length > 0);
					social[platform] = Array.from(new Set(cleaned));
				}
			}
		}

		return sanitized;
	}

	assert(obj: unknown): void
	{
		const res = this.validate(obj);
		if (!res.ok)
		{
			const message = (res.errors || [])
				.map(e => `${e.instancePath || e.schemaPath}: ${e.message}`)
				.join('; ');
			const err = new Error(`DomainDescription validation failed: ${message}`) as Error & { errors?: unknown };
			err.errors = res.errors;
			throw err;
		}
	}

	/**
   * Validates backward compatibility with older schema versions
   */
	validateBackwardCompatibility(obj: unknown): ValidationResult
	{
		const warnings: string[] = [];

		if (obj && typeof obj === 'object')
		{
			const domain = obj as Partial<DomainDescriptionInterface>;

			// Check for deprecated fields using safe existence checks
			const metadataAny = domain.metadata as Record<string, unknown> | undefined;
			if (metadataAny && 'launchedYear' in metadataAny)
			{
				warnings.push('Field "launchedYear" is deprecated, use "registration.firstRegisteredAt" instead');
			}

			if (metadataAny && 'ageDays' in metadataAny)
			{
				warnings.push('Field "ageDays" is deprecated, calculate from "registration.firstRegisteredAt" instead');
			}

			// Check for old social format
			if (domain.reputation?.social)
			{
				const social = domain.reputation.social;
				if (typeof social.twitter === 'string' ||
            typeof social.facebook === 'string' ||
            typeof social.linkedin === 'string' ||
            typeof social.instagram === 'string')
				{
					warnings.push('Social media fields should now be arrays instead of strings');
				}
			}

			// Check for old category format
			if (domain.metadata?.category?.secondary && Array.isArray(domain.metadata.category.secondary))
			{
				warnings.push('Category secondary field should now be a string instead of an array');
			}
		}

		return { ok: true, warnings: warnings.length > 0 ? warnings : undefined };
	}

	/**
	 * Validates comprehensive content quality for domain descriptions
	 */
	validateContentQuality(obj: unknown): ValidationResult
	{
		const warnings: string[] = [];
		const errors: string[] = [];

		if (obj && typeof obj === 'object')
		{
			const domain = obj as DomainDescriptionInterface;

			// Validate content completeness (including empty content)
			const contentQualityResult = this.validateContentCompleteness(domain);
			warnings.push(...contentQualityResult.warnings);
			errors.push(...contentQualityResult.errors);

			// Validate SEO compliance
			const seoComplianceResult = this.validateSEOCompliance(domain);
			warnings.push(...seoComplianceResult.warnings);

			// Validate content consistency
			const consistencyResult = this.validateContentConsistency(domain);
			warnings.push(...consistencyResult.warnings);
		}

		// Convert custom error strings to Ajv-style ErrorObjects for consistency
		const ajvErrors: ErrorObject[] = errors.map(message => ({
			keyword: 'custom',
			instancePath: '',
			schemaPath: '#/contentQuality',
			params: {},
			message,
		} as ErrorObject));

		return {
			ok: ajvErrors.length === 0,
			errors: ajvErrors.length > 0 ? ajvErrors : undefined,
			warnings: warnings.length > 0 ? warnings : undefined,
		};
	}

	/**
	 * Validates content completeness and quality
	 */
	private validateContentCompleteness(
		domain: DomainDescriptionInterface,
	): {
		warnings: string[];
		errors: string[];
	}
	{
		const warnings: string[] = [];
		const errors: string[] = [];

		// Check summary quality
		if (domain.overview?.summary !== undefined)
		{
			const summary = domain.overview.summary;

			// Handle empty or whitespace-only content
			if (!summary || summary.trim().length === 0)
			{
				errors.push('Summary is empty or contains only whitespace');
				return { warnings, errors };
			}

			const wordCount = summary.trim().split(/\s+/).filter((word: string) => word.length > 0).length;

			// Critical errors
			if (wordCount < 50)
			{
				errors.push(`Summary is too short (${wordCount} words), minimum 50 words required for basic content`);
			}

			// Quality warnings
			if (wordCount < 320 && domain.metadata.preGenerated)
			{
				warnings.push(`PreGenerated content summary has ${wordCount} words, 320+ recommended for SEO`);
			}

			// Check for placeholder content
			const placeholderPatterns = [
				/lorem ipsum/gi,
				/placeholder/gi,
				/\[.*\]/g,
				/xxx+/gi,
				/test.*content/gi,
			];

			for (const pattern of placeholderPatterns)
			{
				if (pattern.test(summary))
				{
					errors.push('Summary contains placeholder content that should be replaced');
					break;
				}
			}

			// Check for repetitive content
			const sentences = summary.split(/[.!?]+/).filter(s => s.trim().length > 0);
			const uniqueSentences = new Set(sentences.map(s => s.trim().toLowerCase()));
			if (sentences.length > 3 && uniqueSentences.size < sentences.length * 0.8)
			{
				warnings.push('Summary contains repetitive content, consider varying sentence structure');
			}
		}

		// Check tag quality
		if (domain.metadata.tags)
		{
			const tags = domain.metadata.tags;

			// Check for empty or invalid tags
			const invalidTags = tags.filter(tag => !tag || tag.trim().length === 0 || tag.length < 2);
			if (invalidTags.length > 0)
			{
				errors.push(`Found ${invalidTags.length} invalid tag(s), tags must be at least 2 characters`);
			}

			// Check for overly long tags
			const longTags = tags.filter(tag => tag.length > 30);
			if (longTags.length > 0)
			{
				warnings.push(`Found ${longTags.length} tag(s) longer than 30 characters, consider shorter tags`);
			}

			// Check for duplicate tags (case-insensitive)
			const lowerTags = tags.map(tag => tag.toLowerCase());
			const uniqueTags = new Set(lowerTags);
			if (uniqueTags.size < tags.length)
			{
				warnings.push('Found duplicate tags (case-insensitive), consider removing duplicates');
			}
		}

		return { warnings, errors };
	}

	/**
	 * Validates SEO compliance
	 */
	private validateSEOCompliance(domain: DomainDescriptionInterface): { warnings: string[] }
	{
		const warnings: string[] = [];

		// Check meta description length
		if (domain.seo?.metaDescription)
		{
			const metaLength = domain.seo.metaDescription.length;
			if (metaLength > 160)
			{
				warnings.push(`Meta description is ${metaLength} characters, recommended maximum is 160`);
			}
			else if (metaLength < 120)
			{
				warnings.push(`Meta description is ${metaLength} characters, recommended minimum is 120`);
			}
		}

		// Check title length
		if (domain.seo?.title)
		{
			const titleLength = domain.seo.title.length;
			if (titleLength > 70)
			{
				warnings.push(`SEO title is ${titleLength} characters, recommended maximum is 70`);
			}
			else if (titleLength < 30)
			{
				warnings.push(`SEO title is ${titleLength} characters, recommended minimum is 30`);
			}
		}

		// Check for missing SEO elements
		if (!domain.seo?.title && domain.overview?.summary)
		{
			warnings.push('Missing SEO title, consider adding for better search visibility');
		}

		if (!domain.seo?.metaDescription && domain.overview?.summary)
		{
			warnings.push('Missing meta description, consider adding for better search snippets');
		}

		return { warnings };
	}

	/**
	 * Validates content consistency across fields
	 */
	private validateContentConsistency(domain: DomainDescriptionInterface): { warnings: string[] }
	{
		const warnings: string[] = [];

		// Check consistency between category and tags
		if (domain.metadata.category?.primary && domain.metadata.tags)
		{
			const primaryCategory = domain.metadata.category.primary.toLowerCase();
			const tagWords = domain.metadata.tags.join(' ').toLowerCase();

			// Check if primary category is reflected in tags
			const categoryWords = primaryCategory.split(/\s+/);
			const hasRelatedTag = categoryWords.some(word => word.length > 3 && tagWords.includes(word));

			if (!hasRelatedTag)
			{
				warnings.push('Primary category should be reflected in tags for consistency');
			}
		}

		// Check consistency between summary and tags
		if (domain.overview?.summary && domain.metadata.tags)
		{
			const summaryWords = domain.overview.summary.toLowerCase();
			const unusedTags = domain.metadata.tags.filter(
				tag => tag.length > 3 &&
					!summaryWords.includes(tag.toLowerCase()),
			);

			if (unusedTags.length > domain.metadata.tags.length * 0.5)
			{
				warnings.push('Many tags are not mentioned in summary, consider improving consistency');
			}
		}

		// Check language consistency
		if (domain.metadata.language && domain.overview?.summary)
		{
			const declaredLang = domain.metadata.language;
			const detectedLang = this.detectContentLanguage(domain.overview.summary);

			if (detectedLang && detectedLang !== declaredLang)
			{
				warnings.push(`Declared language (${declaredLang}) may not match content language (${detectedLang})`);
			}
		}

		return { warnings };
	}

	/**
	 * Simple language detection based on common words
	 */
	private detectContentLanguage(content: string): string | null
	{
		const words = content.toLowerCase().match(/\b\w+\b/g) || [];

		// Common English words
		const englishWords = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'];
		const englishCount = words.filter(word => englishWords.includes(word)).length;

		// Common Spanish words
		const spanishWords = ['el', 'la', 'de', 'que', 'y', 'en', 'un', 'es', 'se', 'no', 'te', 'lo'];
		const spanishCount = words.filter(word => spanishWords.includes(word)).length;

		// Common French words
		const frenchWords = ['le', 'de', 'et', 'à', 'un', 'il', 'être', 'et', 'en', 'avoir', 'que', 'pour'];
		const frenchCount = words.filter(word => frenchWords.includes(word)).length;

		const totalWords = words.length;
		if (totalWords < 10) return null; // Not enough content to detect

		const englishRatio = englishCount / totalWords;
		const spanishRatio = spanishCount / totalWords;
		const frenchRatio = frenchCount / totalWords;

		if (englishRatio > 0.1) return 'en';
		if (spanishRatio > 0.1) return 'es';
		if (frenchRatio > 0.1) return 'fr';

		return null; // Cannot detect
	}

	/**
	 * Validates SEO content quality guidelines
	 */
	private validateSEOContentQuality(content: string): { warnings: string[] }
	{
		const warnings: string[] = [];

		// Check for keyword density (should not exceed 3% for any single keyword)
		const keywordDensityResult = this.checkKeywordDensity(content);
		if (keywordDensityResult.warnings.length > 0)
		{
			warnings.push(...keywordDensityResult.warnings);
		}

		// Check for proper heading structure indicators
		const headingStructureResult = this.checkHeadingStructure(content);
		if (headingStructureResult.warnings.length > 0)
		{
			warnings.push(...headingStructureResult.warnings);
		}

		// Check for internal linking opportunities
		const linkingResult = this.checkInternalLinking(content);
		if (linkingResult.warnings.length > 0)
		{
			warnings.push(...linkingResult.warnings);
		}

		// Check for meta information completeness
		const metaResult = this.checkMetaInformation(content);
		if (metaResult.warnings.length > 0)
		{
			warnings.push(...metaResult.warnings);
		}

		return { warnings };
	}

	/**
	 * Validates readability standards
	 */
	private validateReadabilityStandards(content: string): { warnings: string[] }
	{
		const warnings: string[] = [];

		// Check average sentence length (should be under 20 words)
		const avgSentenceLength = this.calculateAverageSentenceLength(content);
		if (avgSentenceLength > 20)
		{
			warnings.push(`Average sentence length is ${avgSentenceLength.toFixed(1)} words, consider shorter sentences (recommended: <20 words)`);
		}

		// Check paragraph length (should be under 150 words)
		const longParagraphs = this.findLongParagraphs(content);
		if (longParagraphs.length > 0)
		{
			warnings.push(`Found ${longParagraphs.length} paragraph(s) longer than 150 words, consider breaking them up`);
		}

		// Check for passive voice overuse (should be under 25%)
		const passiveVoicePercentage = this.calculatePassiveVoicePercentage(content);
		if (passiveVoicePercentage > 25)
		{
			warnings.push(`Passive voice usage is ${passiveVoicePercentage.toFixed(1)}%, consider using more active voice (recommended: <25%)`);
		}

		// Check for transition words (should have some for flow)
		const transitionWordCount = this.countTransitionWords(content);
		const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
		const transitionRatio = (transitionWordCount / sentences.length) * 100;
		if (transitionRatio < 10)
		{
			warnings.push(`Low transition word usage (${transitionRatio.toFixed(1)}%), consider adding more for better flow`);
		}

		// Check for readability score (Flesch Reading Ease)
		const readabilityScore = this.calculateFleschReadingEase(content);
		if (readabilityScore < 60)
		{
			warnings.push(`Readability score is ${readabilityScore.toFixed(1)} (difficult), consider simplifying language (recommended: >60)`);
		}

		return ({ warnings });
	}

	/**
	 * Check keyword density to avoid over-optimization
	 */
	private checkKeywordDensity(content: string): { warnings: string[] }
	{
		const warnings: string[] = [];
		const words = content.toLowerCase().match(/\b\w+\b/g) || [];
		const totalWords = words.length;

		if (totalWords === 0) return { warnings };

		// Count word frequencies
		const wordFreq: Record<string, number> = {};
		for (const word of words)
		{
			if (word.length > 3) // Only check words longer than 3 characters
			{
				wordFreq[word] = (wordFreq[word] || 0) + 1;
			}
		}

		// Check for over-optimization (>3% density for any keyword)
		for (const [word, count] of Object.entries(wordFreq))
		{
			const density = (count / totalWords) * 100;
			if (density > 3)
			{
				warnings.push(`Keyword "${word}" has ${density.toFixed(1)}% density, consider reducing usage (recommended: <3%)`);
			}
		}

		return { warnings };
	}

	/**
	 * Check for proper heading structure indicators in content
	 */
	private checkHeadingStructure(content: string): { warnings: string[] }
	{
		const warnings: string[] = [];

		// Look for potential heading patterns (lines that could be headings)
		const lines = content
			.split('\n')
			.map(line => line.trim())
			.filter(line => line.length > 0);

		const potentialHeadings = lines.filter(
			line => line.length < 100 &&
				line.length > 10 &&
				!line.endsWith('.') &&
				!line.includes(','),
		);

		if (potentialHeadings.length === 0 && content.length > 500)
		{
			warnings.push('Long content should include headings for better structure and SEO');
		}

		return { warnings };
	}

	/**
	 * Check for internal linking opportunities
	 */
	private checkInternalLinking(content: string): { warnings: string[] }
	{
		const warnings: string[] = [];

		// Look for potential internal linking opportunities
		const linkableTerms = [
			'services', 'products', 'about', 'contact', 'blog', 'resources',
			'solutions', 'features', 'pricing', 'support', 'documentation',
		];

		const foundTerms = linkableTerms.filter(term => content.toLowerCase().includes(term));

		if (foundTerms.length > 2 && !content.includes('href=') && !content.includes('['))
		{
			warnings.push('Content mentions linkable terms but lacks internal links for SEO benefit');
		}

		return { warnings };
	}

	/**
	 * Check meta information completeness
	 */
	private checkMetaInformation(content: string): { warnings: string[] }
	{
		const warnings: string[] = [];

		// Check if content has a clear value proposition in the first paragraph
		const firstParagraph = content.split('\n')[0] || content.substring(0, 200);
		if (firstParagraph.length < 100)
		{
			warnings.push('First paragraph should clearly state the value proposition (recommended: 100+ characters)');
		}

		// Check for call-to-action indicators
		const ctaTerms = ['contact', 'learn more', 'get started', 'sign up', 'try', 'discover', 'explore'];
		const hasCTA = ctaTerms.some(term => content.toLowerCase().includes(term));
		if (!hasCTA && content.length > 300)
		{
			warnings.push('Content should include call-to-action elements for better engagement');
		}

		return { warnings };
	}

	/**
	 * Calculate average sentence length
	 */
	private calculateAverageSentenceLength(content: string): number
	{
		const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
		if (sentences.length === 0) return 0;

		const totalWords = sentences.reduce((sum, sentence) =>
		{
			const words = sentence.trim().split(/\s+/).filter(word => word.length > 0);
			return sum + words.length;
		}, 0);

		return totalWords / sentences.length;
	}

	/**
	 * Find paragraphs longer than 150 words
	 */
	private findLongParagraphs(content: string): string[]
	{
		const paragraphs = content.split('\n\n').filter(p => p.trim().length > 0);
		return paragraphs.filter((paragraph) =>
		{
			const words = paragraph.trim().split(/\s+/).filter(word => word.length > 0);
			return words.length > 150;
		});
	}

	/**
	 * Calculate passive voice percentage (simplified detection)
	 */
	private calculatePassiveVoicePercentage(content: string): number
	{
		const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
		if (sentences.length === 0) return 0;

		// Simple passive voice detection patterns
		const passivePatterns = [
			/\b(was|were|is|are|been|being)\s+\w+ed\b/gi,
			/\b(was|were|is|are|been|being)\s+\w+en\b/gi,
			/\bwas\s+\w+\s+by\b/gi,
			/\bwere\s+\w+\s+by\b/gi,
			/\bis\s+\w+\s+by\b/gi,
			/\bare\s+\w+\s+by\b/gi,
		];

		let passiveCount = 0;
		for (const sentence of sentences)
		{
			const hasPassive = passivePatterns.some(pattern => pattern.test(sentence));
			if (hasPassive)
			{
				passiveCount += 1;
			}
		}

		return (passiveCount / sentences.length) * 100;
	}

	/**
	 * Count transition words for flow assessment
	 */
	private countTransitionWords(content: string): number
	{
		const transitionWords = [
			'however', 'therefore', 'furthermore', 'moreover', 'additionally',
			'consequently', 'meanwhile', 'nevertheless', 'nonetheless', 'thus',
			'hence', 'accordingly', 'subsequently', 'likewise', 'similarly',
			'conversely', 'alternatively', 'specifically', 'particularly',
			'especially', 'notably', 'importantly', 'significantly',
		];

		const words = content.toLowerCase().match(/\b\w+\b/g) || [];
		return words.filter(word => transitionWords.includes(word)).length;
	}

	/**
	 * Calculate Flesch Reading Ease score
	 */
	private calculateFleschReadingEase(content: string): number
	{
		const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
		const words = content.match(/\b\w+\b/g) || [];
		const syllables = words.reduce((sum, word) => sum + this.countSyllables(word), 0);

		if (sentences.length === 0 || words.length === 0) return 0;

		const avgSentenceLength = words.length / sentences.length;
		const avgSyllablesPerWord = syllables / words.length;

		return 206.835 - (1.015 * avgSentenceLength) - (84.6 * avgSyllablesPerWord);
	}

	/**
	 * Count syllables in a word (simplified)
	 */
	private countSyllables(word: string): number
	{
		const lower = word.toLowerCase();
		if (lower.length <= 3) return 1;

		const vowels = 'aeiouy';
		let syllableCount = 0;
		let previousWasVowel = false;

		for (let i = 0; i < lower.length; i++)
		{
			const isVowel = vowels.includes(lower[i]);
			if (isVowel && !previousWasVowel)
			{
				syllableCount += 1;
			}
			previousWasVowel = isVowel;
		}

		// Handle silent 'e'
		if (lower.endsWith('e'))
		{
			syllableCount -= 1;
		}

		return Math.max(1, syllableCount);
	}
}

export default DomainDescriptionValidator;
