import type DomainDescriptionInterface from '../../models/DomainDescription';

export class ContentValidator
{
	validateContentCompleteness(domain: DomainDescriptionInterface): { warnings: string[] }
	{
		const warnings: string[] = [];

		// Essential fields
		if (!domain.metadata?.domain)
		{
			warnings.push('Domain name is required');
		}

		if (!domain.seo?.title || domain.seo.title.trim().length === 0)
		{
			warnings.push('Title is required for better SEO and user experience');
		}

		if (!domain.seo?.metaDescription || domain.seo.metaDescription.trim().length === 0)
		{
			warnings.push('Meta description is required for better SEO and user experience');
		}

		// Recommended fields
		if (!domain.metadata?.category?.primary || domain.metadata.category.primary.trim().length === 0)
		{
			warnings.push('Category is recommended for better organization');
		}

		if (!domain.metadata?.tags || domain.metadata.tags.length === 0)
		{
			warnings.push('Tags are recommended for better discoverability');
		}

		// Content completeness
		if (domain.overview?.summary && domain.overview.summary.trim().length > 0)
		{
			if (domain.overview.summary.length < 100)
			{
				warnings.push('Content is very short - consider adding more detailed information');
			}
		}
		else
		{
			warnings.push('Content is recommended for better user engagement');
		}

		// Technical information
		if (!domain.technical?.technologies || domain.technical.technologies.length === 0)
		{
			warnings.push('Technologies information is recommended for technical users');
		}

		if (!domain.technical?.security || !domain.technical.security.sslGrade)
		{
			warnings.push('SSL information is recommended for security transparency');
		}

		// Traffic and ranking information
		if (!domain.ranking?.trafficEstimateMonthly)
		{
			warnings.push('Traffic information is recommended for credibility');
		}

		if (!domain.ranking?.globalRank && !domain.ranking?.categoryRank)
		{
			warnings.push('Ranking information is recommended for competitive analysis');
		}

		// Social presence
		if (!domain.reputation?.social || Object.keys(domain.reputation.social).length === 0)
		{
			warnings.push('Social media presence is recommended for modern web properties');
		}

		return { warnings };
	}

	validateContentConsistency(domain: DomainDescriptionInterface): { warnings: string[] }
	{
		const warnings: string[] = [];

		// Check title-description consistency
		if (domain.seo?.title && domain.seo.metaDescription)
		{
			const titleWords = new Set(
				domain.seo.title.toLowerCase()
					.replace(/[^\w\s]/g, '')
					.split(/\s+/)
					.filter((word: string) => word.length > 3)
			);

			const descriptionWords = new Set(
				domain.seo.metaDescription.toLowerCase()
					.replace(/[^\w\s]/g, '')
					.split(/\s+/)
					.filter((word: string) => word.length > 3)
			);

			const commonWords = new Set(Array.from(titleWords).filter((word: string) => descriptionWords.has(word)));

			if (commonWords.size === 0)
			{
				warnings.push('Title and description have no common keywords - ensure they are related');
			}
		}

		// Check keywords-content consistency (using metadata.tags)
		if (domain.metadata?.tags && domain.overview?.summary)
		{
			const contentLower = domain.overview.summary.toLowerCase();
			let keywordsFound = 0;

			for (const keyword of domain.metadata.tags)
			{
				if (contentLower.includes(keyword.toLowerCase()))
				{
					keywordsFound++;
				}
			}

			const keywordCoverage = (keywordsFound / domain.metadata.tags.length) * 100;
			if (keywordCoverage < 50)
			{
				warnings.push(`Only ${keywordCoverage.toFixed(0)}% of keywords appear in content - improve keyword-content alignment`);
			}
		}

		// Check category-tags consistency
		if (domain.metadata?.category?.primary && domain.metadata?.tags)
		{
			const categoryLower = domain.metadata.category.primary.toLowerCase();
			const hasRelatedTag = Array.from(domain.metadata.tags).some((tag: string) =>
				tag.toLowerCase().includes(categoryLower) ||
				categoryLower.includes(tag.toLowerCase()));

			if (!hasRelatedTag)
			{
				warnings.push('Category and tags seem unrelated - ensure they are consistent');
			}
		}

		// Check language consistency
		if (domain.metadata?.language && domain.overview?.summary)
		{
			const detectedLanguage = this.detectContentLanguage(domain.overview.summary);
			if (detectedLanguage && detectedLanguage !== domain.metadata.language)
			{
				warnings.push(`Declared language (${domain.metadata.language}) doesn't match detected content language (${detectedLanguage})`);
			}
		}

		return { warnings };
	}

	private detectContentLanguage(content: string): string | null
	{
		// Simple language detection based on common words
		const cleanContent = content.toLowerCase().replace(/<[^>]*>/g, '').replace(/[^\w\s]/g, '');
		const words = cleanContent.split(/\s+/).slice(0, 100); // First 100 words for efficiency

		const languagePatterns: Record<string, string[]> = {
			'en': ['the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'her', 'was', 'one', 'our', 'had', 'have'],
			'es': ['que', 'las', 'por', 'con', 'una', 'para', 'son', 'los', 'del', 'sus', 'como', 'pero', 'sus', 'han', 'más'],
			'fr': ['que', 'les', 'pour', 'avec', 'une', 'dans', 'sont', 'des', 'ses', 'comme', 'mais', 'ont', 'plus', 'sur', 'lui'],
			'de': ['und', 'die', 'der', 'das', 'für', 'mit', 'eine', 'sind', 'des', 'wie', 'aber', 'haben', 'mehr', 'auf', 'ihm'],
			'it': ['che', 'per', 'con', 'una', 'sono', 'del', 'come', 'suoi', 'hanno', 'più', 'lui', 'anche', 'solo', 'anni', 'due'],
			'pt': ['que', 'para', 'com', 'uma', 'são', 'dos', 'como', 'seus', 'têm', 'mais', 'ele', 'também', 'anos', 'dois', 'foi'],
		};

		const scores: Record<string, number> = {};

		for (const [lang, patterns] of Object.entries(languagePatterns))
		{
			scores[lang] = 0;
			for (const word of words)
			{
				if (patterns.includes(word))
				{
					scores[lang]++;
				}
			}
		}

		let maxScore = 0;
		let detectedLang: string | null = null;

		for (const [lang, score] of Object.entries(scores))
		{
			if (score > maxScore && score >= 3)
			{ // Minimum threshold
				maxScore = score;
				detectedLang = lang;
			}
		}

		return detectedLang;
	}
}
