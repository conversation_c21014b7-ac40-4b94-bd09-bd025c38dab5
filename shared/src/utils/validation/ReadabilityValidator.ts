export class ReadabilityValidator 
{
	validateReadabilityStandards(content: string): { warnings: string[] } 
	{
		const warnings: string[] = [];

		// Remove HTML tags for analysis
		const plainText = content.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim();

		if (plainText.length === 0) 
		{
			warnings.push('No readable content found');
			return { warnings };
		}

		// Count sentences and words
		const sentences = plainText.split(/[.!?]+/).filter(s => s.trim().length > 0);
		const words = plainText.split(/\s+/).filter(w => w.length > 0);
		const syllables = this.countSyllables(plainText);

		if (sentences.length === 0 || words.length === 0) 
		{
			warnings.push('Content lacks proper sentence structure');
			return { warnings };
		}

		// Calculate readability metrics
		const avgWordsPerSentence = words.length / sentences.length;
		const avgSyllablesPerWord = syllables / words.length;

		// Flesch Reading Ease Score
		const fleschScore = 206.835 - (1.015 * avgWordsPerSentence) - (84.6 * avgSyllablesPerWord);

		// Flesch-Kincaid Grade Level
		const gradeLevel = (0.39 * avgWordsPerSentence) + (11.8 * avgSyllablesPerWord) - 15.59;

		// Readability warnings
		if (avgWordsPerSentence > 20) 
		{
			warnings.push(`Average sentence length is ${avgWordsPerSentence.toFixed(1)} words - consider shorter sentences (max 20 words recommended)`);
		}

		if (fleschScore < 30) 
		{
			warnings.push('Content is very difficult to read - consider simplifying language and sentence structure');
		}
		else if (fleschScore < 50) 
		{
			warnings.push('Content is fairly difficult to read - consider improving readability');
		}

		if (gradeLevel > 12) 
		{
			warnings.push(`Content requires college-level education (grade ${gradeLevel.toFixed(1)}) - consider simplifying for broader audience`);
		}

		// Check for passive voice (simplified detection)
		const passiveIndicators = ['is', 'are', 'was', 'were', 'be', 'been', 'being'].join('|');
		const passivePattern = new RegExp(`\\b(${passiveIndicators})\\s+\\w+ed\\b`, 'gi');
		const passiveMatches = plainText.match(passivePattern);
		
		if (passiveMatches && passiveMatches.length > sentences.length * 0.2) 
		{
			warnings.push('High use of passive voice detected - consider using more active voice for better readability');
		}

		// Check paragraph length
		const paragraphs = content.split(/\n\s*\n/).filter(p => p.trim().length > 0);
		const longParagraphs = paragraphs.filter((p) => 
		{
			const paragraphWords = p.replace(/<[^>]*>/g, ' ').split(/\s+/).filter(w => w.length > 0);
			return paragraphWords.length > 150;
		});

		if (longParagraphs.length > 0) 
		{
			warnings.push(`${longParagraphs.length} paragraph(s) are too long - break up paragraphs over 150 words for better readability`);
		}

		// Check for transition words
		const transitionWords = ['however', 'therefore', 'furthermore', 'moreover', 'consequently', 'additionally', 'meanwhile', 'nevertheless'];
		const hasTransitions = transitionWords.some(word => plainText.toLowerCase().includes(word));
		
		if (!hasTransitions && sentences.length > 5) 
		{
			warnings.push('Consider adding transition words to improve content flow and readability');
		}

		return { warnings };
	}

	private countSyllables(text: string): number 
	{
		const words = text.toLowerCase().replace(/[^\w\s]/g, '').split(/\s+/);
		let totalSyllables = 0;

		for (const word of words) 
		{
			if (word.length === 0) continue;

			// Count vowel groups
			let syllableCount = 0;
			let previousWasVowel = false;

			for (let i = 0; i < word.length; i++) 
			{
				const isVowel = /[aeiouy]/.test(word[i]);
				
				if (isVowel && !previousWasVowel) 
				{
					syllableCount++;
				}
				
				previousWasVowel = isVowel;
			}

			// Handle silent 'e'
			if (word.endsWith('e') && syllableCount > 1) 
			{
				syllableCount--;
			}

			// Every word has at least one syllable
			if (syllableCount === 0) 
			{
				syllableCount = 1;
			}

			totalSyllables += syllableCount;
		}

		return totalSyllables;
	}

	calculateFleschScore(content: string): number 
	{
		const plainText = content.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim();
		const sentences = plainText.split(/[.!?]+/).filter(s => s.trim().length > 0);
		const words = plainText.split(/\s+/).filter(w => w.length > 0);
		const syllables = this.countSyllables(plainText);

		if (sentences.length === 0 || words.length === 0) return 0;

		const avgWordsPerSentence = words.length / sentences.length;
		const avgSyllablesPerWord = syllables / words.length;

		return 206.835 - (1.015 * avgWordsPerSentence) - (84.6 * avgSyllablesPerWord);
	}

	calculateGradeLevel(content: string): number 
	{
		const plainText = content.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim();
		const sentences = plainText.split(/[.!?]+/).filter(s => s.trim().length > 0);
		const words = plainText.split(/\s+/).filter(w => w.length > 0);
		const syllables = this.countSyllables(plainText);

		if (sentences.length === 0 || words.length === 0) return 0;

		const avgWordsPerSentence = words.length / sentences.length;
		const avgSyllablesPerWord = syllables / words.length;

		return (0.39 * avgWordsPerSentence) + (11.8 * avgSyllablesPerWord) - 15.59;
	}
}
