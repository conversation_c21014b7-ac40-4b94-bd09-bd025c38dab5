import type DomainDescriptionInterface from '../../models/DomainDescription';

export class SEOValidator
{
	validateSEOCompliance(domain: DomainDescriptionInterface): { warnings: string[] }
	{
		const warnings: string[] = [];

		// Check title length
		if (domain.seo?.title)
		{
			if (domain.seo.title.length < 30)
			{
				warnings.push('Title is too short for optimal SEO (minimum 30 characters recommended)');
			}
			if (domain.seo.title.length > 60)
			{
				warnings.push('Title is too long for optimal SEO (maximum 60 characters recommended)');
			}
		}

		// Check meta description
		if (domain.seo?.metaDescription)
		{
			if (domain.seo.metaDescription.length < 120)
			{
				warnings.push('Description is too short for optimal SEO (minimum 120 characters recommended)');
			}
				if (domain.seo.metaDescription.length > 160)
			{
				warnings.push('Description is too long for optimal SEO (maximum 160 characters recommended)');
			}
		}

		// Check keywords
		if (!domain.metadata?.tags || domain.metadata.tags.length === 0)
		{
			warnings.push('No keywords defined - consider adding relevant keywords for better SEO');
		}
		else if (domain.metadata.tags.length > 10)
		{
			warnings.push('Too many keywords defined (maximum 10 recommended for focus)');
		}

		// Check for duplicate keywords
		if (domain.metadata?.tags && domain.metadata.tags.length > 0)
		{
			const uniqueKeywords = new Set(domain.metadata.tags.map((k: string) => k.toLowerCase()));
			if (uniqueKeywords.size < domain.metadata.tags.length)
			{
				warnings.push('Duplicate keywords detected - remove duplicates for better SEO');
			}
		}

		// Check social media presence
		if (!domain.reputation?.social || Object.keys(domain.reputation.social).length === 0)
		{
			warnings.push('No social media links - consider adding social presence for better SEO');
		}

		return ({ warnings });
	}

	validateSEOContentQuality(content: string): { warnings: string[] }
	{
		const warnings: string[] = [];

		// Check content length
		if (content.length < 300)
		{
			warnings.push('Content is too short for good SEO (minimum 300 words recommended)');
		}

		// Check for headings
		const headingMatches = content.match(/<h[1-6][^>]*>.*?<\/h[1-6]>/gi);
		if (!headingMatches || headingMatches.length === 0)
		{
			warnings.push('No headings found - use H1-H6 tags for better content structure');
		}

		// Check for images without alt text
		const imgMatches = content.match(/<img[^>]*>/gi);
		if (imgMatches)
		{
			for (const img of imgMatches)
			{
				if (!img.includes('alt=') || img.includes('alt=""') || img.includes("alt=''"))
				{
					warnings.push('Images without alt text found - add descriptive alt text for accessibility and SEO');
					break;
				}
			}
		}

		// Check for external links
		const externalLinkMatches = content.match(/<a[^>]*href=["']https?:\/\/[^"']*["'][^>]*>/gi);
		if (!externalLinkMatches || externalLinkMatches.length === 0)
		{
			warnings.push('No external links found - consider linking to authoritative sources');
		}

		// Check for internal links
		const internalLinkMatches = content.match(/<a[^>]*href=["'][^"']*["'][^>]*>/gi);
		if (!internalLinkMatches || internalLinkMatches.length < 2)
		{
			warnings.push('Limited internal linking - improve site structure with more internal links');
		}

		return ({ warnings });
	}

	checkKeywordDensity(content: string): { warnings: string[] }
	{
		const warnings: string[] = [];
		const words = content.toLowerCase()
			.replace(/<[^>]*>/g, '') // Remove HTML tags
			.replace(/[^\w\s]/g, '') // Remove punctuation
			.split(/\s+/)
			.filter(word => word.length > 2); // Filter out short words

		if (words.length === 0) return { warnings };

		// Count word frequency
		const wordFreq: Record<string, number> = {};
		for (const word of words)
		{
			wordFreq[word] = (wordFreq[word] || 0) + 1;
		}

		// Check for keyword stuffing
		for (const [word, count] of Object.entries(wordFreq))
		{
			const density = (count / words.length) * 100;
			if (density > 3)
			{
				warnings.push(`Keyword "${word}" density is ${density.toFixed(1)}% - consider reducing to avoid keyword stuffing (max 3% recommended)`);
			}
		}

		return { warnings };
	}

	checkHeadingStructure(content: string): { warnings: string[] }
	{
		const warnings: string[] = [];
		const headingMatches = content.match(/<h([1-6])[^>]*>.*?<\/h\1>/gi);

		if (!headingMatches)
		{
			warnings.push('No headings found - use proper heading hierarchy (H1-H6)');
			return { warnings };
		}

		// Extract heading levels
		const headingLevels = headingMatches.map((heading) =>
		{
			const match = heading.match(/<h([1-6])/i);
			return match ? parseInt(match[1], 10) : 0;
		});

		// Check for H1
		if (!headingLevels.includes(1))
		{
			warnings.push('No H1 heading found - every page should have exactly one H1');
		}

		// Check for multiple H1s
		const h1Count = headingLevels.filter(level => level === 1).length;
		if (h1Count > 1)
		{
			warnings.push(`Multiple H1 headings found (${h1Count}) - use only one H1 per page`);
		}

		// Check heading hierarchy
		let prevLevel = 0;
		for (let i = 0; i < headingLevels.length; i++)
		{
			const currentLevel = headingLevels[i];
			if (prevLevel > 0 && currentLevel > prevLevel + 1)
			{
				warnings.push(`Heading hierarchy skipped from H${prevLevel} to H${currentLevel} - maintain proper sequence`);
			}
			prevLevel = currentLevel;
		}

		return { warnings };
	}
}
