import iso3166Countries from '../../../data/iso-3166-countries.json';
import iso639Languages from '../../../data/iso-639-languages.json';

export class ISOValidator
{
	private iso3166Countries: Set<string> = new Set();
	private iso639Languages: Set<string> = new Set();

	constructor()
	{
		this.initializeISOCodes();
	}

	private initializeISOCodes(): void
	{
		// Load ISO 3166 country codes (array of alpha-2 strings)
		for (const country of iso3166Countries)
		{
			this.iso3166Countries.add(String(country).toUpperCase());
		}

		// Load ISO 639 language codes (array of iso-639-1 strings)
		for (const language of iso639Languages)
		{
			this.iso639Languages.add(String(language).toLowerCase());
		}
	}

	isValidCountryCode(code: string): boolean
	{
		return this.iso3166Countries.has(code.toUpperCase());
	}

	isValidLanguageCode(code: string): boolean
	{
		return this.iso639Languages.has(code.toLowerCase());
	}

	isValidISODateTime(dateString: string): boolean
	{
		// ISO 8601 format validation
		const isoDateRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$/;
		if (!isoDateRegex.test(dateString))
		{
			return false;
		}

		// Additional validation using Date constructor
		const date = new Date(dateString);
		return !Number.isNaN(date.getTime()) && date.toISOString().startsWith(dateString.substring(0, 19));
	}

	getValidCountryCodes(): string[]
	{
		return Array.from(this.iso3166Countries);
	}

	getValidLanguageCodes(): string[]
	{
		return Array.from(this.iso639Languages);
	}
}
