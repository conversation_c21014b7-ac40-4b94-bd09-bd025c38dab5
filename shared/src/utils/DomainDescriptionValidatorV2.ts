import { readFileSync } from 'node:fs';
import { fileURLToPath } from 'node:url';
import { resolve, dirname } from 'node:path';

import type { ValidateFunction, ErrorObject, JSONSchemaType } from 'ajv';
import Ajv from 'ajv';
import addFormats from 'ajv-formats';
import type DomainDescriptionInterface from '../models/DomainDescription';
import { ISOValidator, SEOValidator, ContentValidator, ReadabilityValidator } from './validation';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const schemaPath = resolve(__dirname, '../../../docs/domain-description.schema.json');

type ValidationResult = {
	ok: boolean;
	errors?: ErrorObject[];
	warnings?: string[];
};

type EnhancedValidationResult = ValidationResult & {
	sanitizedData?: DomainDescriptionInterface;
};

class DomainDescriptionValidator
{
	private static instance: DomainDescriptionValidator | null = null;

	private ajv: Ajv;
	private validateFn: ValidateFunction<DomainDescriptionInterface>;

	// Modular validators
	private isoValidator: ISOValidator;
	private seoValidator: SEOValidator;
	private contentValidator: ContentValidator;
	private readabilityValidator: ReadabilityValidator;

	private constructor()
	{
		this.ajv = new Ajv({
			strict: true,
			allErrors: true,
			loadSchema: undefined,
		});

		addFormats(this.ajv);
		const schema = JSON.parse(readFileSync(schemaPath, 'utf-8')) as JSONSchemaType<DomainDescriptionInterface>;

		// Remove $schema to avoid network fetch of meta-schema
		delete (schema as any).$schema;

		this.validateFn = this.ajv.compile(schema);

		// Initialize modular validators
		this.isoValidator = new ISOValidator();
		this.seoValidator = new SEOValidator();
		this.contentValidator = new ContentValidator();
		this.readabilityValidator = new ReadabilityValidator();
	}

	static get(): DomainDescriptionValidator
	{
		if (!DomainDescriptionValidator.instance)
		{
			DomainDescriptionValidator.instance = new DomainDescriptionValidator();
		}
		return DomainDescriptionValidator.instance;
	}

	/**
	 * Validate domain description with comprehensive checks
	 */
	validate(data: unknown): EnhancedValidationResult
	{
		// Basic JSON Schema validation
		const isValid = this.validateFn(data);
		const result: EnhancedValidationResult = {
			ok: isValid,
			errors: this.validateFn.errors || [],
			warnings: [],
		};

		if (!isValid)
		{
			return result;
		}

		const domain = data as DomainDescriptionInterface;

		// Sanitize data
		const sanitizedData = this.sanitizeData(domain);
		result.sanitizedData = sanitizedData;

		// Collect warnings from all validators
		const warnings: string[] = [];

		// Content completeness and consistency
		warnings.push(...this.contentValidator.validateContentCompleteness(sanitizedData).warnings);
		warnings.push(...this.contentValidator.validateContentConsistency(sanitizedData).warnings);

		// SEO validation
		warnings.push(...this.seoValidator.validateSEOCompliance(sanitizedData).warnings);

		if (sanitizedData.overview?.summary)
		{
			warnings.push(...this.seoValidator.validateSEOContentQuality(sanitizedData.overview.summary).warnings);
			warnings.push(...this.seoValidator.checkKeywordDensity(sanitizedData.overview.summary).warnings);
			warnings.push(...this.seoValidator.checkHeadingStructure(sanitizedData.overview.summary).warnings);

			// Readability validation
			warnings.push(...this.readabilityValidator.validateReadabilityStandards(sanitizedData.overview.summary).warnings);
		}

		result.warnings = warnings;
		result.ok = true; // Warnings don't make validation fail

		return result;
	}

	/**
	 * Quick validation (schema only)
	 */
	validateSchema(data: unknown): ValidationResult
	{
		const isValid = this.validateFn(data);
		return {
			ok: isValid,
			errors: this.validateFn.errors || [],
		};
	}

	/**
	 * Data sanitization
	 */
	private sanitizeData(domain: DomainDescriptionInterface): DomainDescriptionInterface
	{
		const sanitized: DomainDescriptionInterface = { ...domain };

		// Sanitize nested strings
		if (sanitized.seo?.title) sanitized.seo.title = sanitized.seo.title.trim();
		if (sanitized.seo?.metaDescription) sanitized.seo.metaDescription = sanitized.seo.metaDescription.trim();
		if (sanitized.overview?.summary) sanitized.overview.summary = sanitized.overview.summary.trim();
		if (sanitized.metadata?.category?.primary)
		{
			sanitized.metadata.category.primary = sanitized.metadata.category.primary.trim();
		}

		// ISO codes in metadata
		if (sanitized.metadata?.country)
		{
			sanitized.metadata.country = sanitized.metadata.country.toUpperCase();
			if (!this.isoValidator.isValidCountryCode(sanitized.metadata.country))
			{
				delete sanitized.metadata.country;
			}
		}
		if (sanitized.metadata?.language)
		{
			sanitized.metadata.language = sanitized.metadata.language.toLowerCase();
			if (!this.isoValidator.isValidLanguageCode(sanitized.metadata.language))
			{
				delete sanitized.metadata.language;
			}
		}

		// Sanitize arrays
		if (sanitized.metadata?.tags)
		{
			sanitized.metadata.tags = sanitized.metadata.tags
				.map((tag) => tag.trim())
				.filter((tag) => tag.length > 0);
		}
		if (sanitized.technical?.technologies)
		{
			sanitized.technical.technologies = sanitized.technical.technologies
				.map((tech) => tech.trim())
				.filter((tech) => tech.length > 0);
		}

		// Validate and sanitize registration dates
		if (sanitized.metadata?.registration)
		{
			const reg = sanitized.metadata.registration;
			if (reg.firstRegisteredAt && !this.isoValidator.isValidISODateTime(reg.firstRegisteredAt))
			{
				delete reg.firstRegisteredAt;
			}
			if (reg.lastUpdatedAt && !this.isoValidator.isValidISODateTime(reg.lastUpdatedAt))
			{
				delete reg.lastUpdatedAt;
			}
			if (reg.expiresAt && !this.isoValidator.isValidISODateTime(reg.expiresAt))
			{
				delete reg.expiresAt;
			}
		}

		// Sanitize URLs (if provided in crawl.screenshotUrls)
		if (sanitized.crawl?.screenshotUrls && Array.isArray(sanitized.crawl.screenshotUrls))
		{
			sanitized.crawl.screenshotUrls = sanitized.crawl.screenshotUrls.filter((u) =>
			{
				try { new URL(u); return true; } catch { return false; }
			});
		}

		return sanitized;
	}

	/**
	 * Get validation statistics
	 */
	getValidationStats(): {
		supportedCountries: number;
		supportedLanguages: number;
		validationRules: number;
	}
	{
		return ({
			supportedCountries: this.isoValidator.getValidCountryCodes().length,
			supportedLanguages: this.isoValidator.getValidLanguageCodes().length,
			validationRules: Object.keys(this.ajv.schemas).length,
		});
	}

	/**
	 * Calculate content quality score
	 */
	calculateContentQuality(domain: DomainDescriptionInterface): {
		score: number;
		breakdown: {
			completeness: number;
			seo: number;
			readability: number;
		};
	}
	{
		let completenessScore = 100;
		let seoScore = 100;
		let readabilityScore = 100;

		// Completeness scoring
		const completenessWarnings = this.contentValidator.validateContentCompleteness(domain).warnings;
		completenessScore = Math.max(0, 100 - (completenessWarnings.length * 5));

		// SEO scoring
		const seoWarnings = this.seoValidator.validateSEOCompliance(domain).warnings;
		if (domain.overview?.summary)
		{
			seoWarnings.push(...this.seoValidator.validateSEOContentQuality(domain.overview.summary).warnings);
		}
		seoScore = Math.max(0, 100 - (seoWarnings.length * 7));

		// Readability scoring
		if (domain.overview?.summary)
		{
			const fleschScore = this.readabilityValidator.calculateFleschScore(domain.overview.summary);
			readabilityScore = Math.max(0, Math.min(100, fleschScore));
		}

		const overallScore = Math.round((completenessScore + seoScore + readabilityScore) / 3);

		return ({
			score: overallScore,
			breakdown: {
				completeness: Math.round(completenessScore),
				seo: Math.round(seoScore),
				readability: Math.round(readabilityScore),
			},
		});
	}
}

export default DomainDescriptionValidator;
