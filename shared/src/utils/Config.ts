import { config as dotenvConfig } from 'dotenv';
import * as path from 'path';
import * as fs from 'fs';
// Ensure environment variables are loaded before reading them anywhere
// 1) Load from current working directory (service-level .env)
dotenvConfig();
// 2) Also attempt to load project-root .env (../../.env from service folders)
try
{
	const rootEnvPath = path.resolve(process.cwd(), '../../.env');
	if (fs.existsSync(rootEnvPath))
	{
		dotenvConfig({ path: rootEnvPath });
	}
}
catch
{
	// ignore
}

import Constants from '../constants/Constants';

// Helper function to safely access environment variables
function getEnv(key: string, defaultValue: string = ''): string
{
	if (typeof process !== 'undefined' && process.env)
	{
		return process.env[key] || defaultValue;
	}
	return defaultValue;
}

interface ConfigInterface
{
	NODE_ENV: string;
	SERVICE_NAME: string;
	SERVICE_PORT: number;
	SCYLLA_HOSTS: string[];
	SCYLLA_KEYSPACE: string;
	SCYLLA_LOCAL_DC: string;
	MARIA_HOST: string;
	MARIA_PORT: number;
	MARIA_USER: string;
	MARIA_PASSWORD: string;
	MARIA_DATABASE: string;
	REDIS_HOST: string;
	REDIS_PORT: number;
	REDIS_DB: number;
	REDIS_PASSWORD: string;
	MANTICORE_HOST: string;
	MANTICORE_PORT: number;
	MANTICORE_URL: string;
	IMAGE_PROXY_URL: string;
	CRAWL_TIMEOUT: number;
	CRAWL_MAX_RETRIES: number;
	CRAWL_RATE_LIMIT: number;
	CRAWL_CONCURRENT_REQUESTS: number;
	RANKING_WEIGHTS: {
		PERFORMANCE: number;
		SECURITY: number;
		SEO: number;
		TECHNICAL: number;
		BACKLINKS: number;
	};
	LOG_LEVEL: string;
	LOG_DIR: string;
	JWT_SECRET: string;
	CORS_ORIGIN: string;
	CACHE_TTL: number;
	REQUEST_TIMEOUT: number;
	ENABLE_SCREENSHOTS: boolean;
	ENABLE_PERFORMANCE_AUDIT: boolean;
	ENABLE_AI_DESCRIPTIONS: boolean;
	SYNC_INTERVAL_MS: string;
	TIMEZONE: string;
}

/**
 * Configuration management utility
 */
class Config
{
	private config: ConfigInterface;

	constructor()
	{
		this.config = {} as ConfigInterface;
		this.loadConfig();
	}

	/**
   * Load configuration from environment variables and defaults
   */
	loadConfig(): void
	{
		this.config =
		{
			// Environment
			NODE_ENV: getEnv('NODE_ENV', 'development'),

			// Service configuration
			SERVICE_NAME: getEnv('SERVICE_NAME', 'unknown'),
			SERVICE_PORT: parseInt(getEnv('SERVICE_PORT', '3000'), 10) || 3000,

			// Database configuration
			SCYLLA_HOSTS: getEnv('SCYLLA_HOSTS') ? getEnv('SCYLLA_HOSTS').split(',') : [...Constants.DATABASE.SCYLLA.CONTACT_POINTS],
			SCYLLA_KEYSPACE: getEnv('SCYLLA_KEYSPACE', Constants.DATABASE.SCYLLA.KEYSPACE),
			SCYLLA_LOCAL_DC: getEnv('SCYLLA_LOCAL_DC', Constants.DATABASE.SCYLLA.LOCAL_DATA_CENTER),

			MARIA_HOST: getEnv('MARIA_HOST', Constants.DATABASE.MARIA.HOST),
			MARIA_PORT: parseInt(getEnv('MARIA_PORT', '3306'), 10) || Constants.DATABASE.MARIA.PORT,
			MARIA_USER: getEnv('MARIA_USER', 'root'),
			MARIA_PASSWORD: getEnv('MARIA_PASSWORD', ''),
			MARIA_DATABASE: getEnv('MARIA_DATABASE', Constants.DATABASE.MARIA.DATABASE),

			REDIS_HOST: getEnv('REDIS_HOST', Constants.DATABASE.REDIS.HOST),
			REDIS_PORT: parseInt(getEnv('REDIS_PORT', '6379'), 10) || Constants.DATABASE.REDIS.PORT,
			REDIS_DB: parseInt(getEnv('REDIS_DB', '0'), 10) || Constants.DATABASE.REDIS.DB,
			REDIS_PASSWORD: getEnv('REDIS_PASSWORD', ''),

			MANTICORE_HOST: getEnv('MANTICORE_HOST', Constants.DATABASE.MANTICORE.HOST),
			MANTICORE_PORT: parseInt(getEnv('MANTICORE_PORT', '9308'), 10) || Constants.DATABASE.MANTICORE.PORT,
			MANTICORE_URL: getEnv('MANTICORE_URL') || `http://${getEnv('MANTICORE_HOST', Constants.DATABASE.MANTICORE.HOST)}:${getEnv('MANTICORE_PORT', Constants.DATABASE.MANTICORE.PORT.toString())}`,

			// External services
			IMAGE_PROXY_URL: getEnv('IMAGE_PROXY_URL', Constants.EXTERNAL.IMAGE_PROXY_URL),

			// Crawling configuration
			CRAWL_TIMEOUT: parseInt(getEnv('CRAWL_TIMEOUT', '30000'), 10) || Constants.CRAWL.TIMEOUT,
			CRAWL_MAX_RETRIES: parseInt(getEnv('CRAWL_MAX_RETRIES', '3'), 10) || Constants.CRAWL.MAX_RETRIES,
			CRAWL_RATE_LIMIT: parseInt(getEnv('CRAWL_RATE_LIMIT', '60'), 10) || Constants.CRAWL.RATE_LIMIT.REQUESTS_PER_MINUTE,
			CRAWL_CONCURRENT_REQUESTS: parseInt(getEnv('CRAWL_CONCURRENT_REQUESTS', '5'), 10) || Constants.CRAWL.RATE_LIMIT.CONCURRENT_REQUESTS,

			// Ranking configuration
			RANKING_WEIGHTS: {
				PERFORMANCE: parseFloat(getEnv('RANKING_WEIGHT_PERFORMANCE', '0.25')) || Constants.RANKING_WEIGHTS.PERFORMANCE,
				SECURITY: parseFloat(getEnv('RANKING_WEIGHT_SECURITY', '0.20')) || Constants.RANKING_WEIGHTS.SECURITY,
				SEO: parseFloat(getEnv('RANKING_WEIGHT_SEO', '0.20')) || Constants.RANKING_WEIGHTS.SEO,
				TECHNICAL: parseFloat(getEnv('RANKING_WEIGHT_TECHNICAL', '0.15')) || Constants.RANKING_WEIGHTS.TECHNICAL,
				BACKLINKS: parseFloat(getEnv('RANKING_WEIGHT_BACKLINKS', '0.20')) || Constants.RANKING_WEIGHTS.BACKLINKS,
			},

			// Logging
			LOG_LEVEL: getEnv('LOG_LEVEL', 'info'),
			LOG_DIR: getEnv('LOG_DIR', './logs'),

			// Security
			JWT_SECRET: getEnv('JWT_SECRET', 'your-secret-key'),
			CORS_ORIGIN: getEnv('CORS_ORIGIN', '*'),

			// Performance
			CACHE_TTL: parseInt(getEnv('CACHE_TTL', '3600'), 10) || 3600, // 1 hour
			REQUEST_TIMEOUT: parseInt(getEnv('REQUEST_TIMEOUT', '30000'), 10) || 30000, // 30 seconds

			// Feature flags
			ENABLE_SCREENSHOTS: getEnv('ENABLE_SCREENSHOTS') === 'true',
			ENABLE_PERFORMANCE_AUDIT: getEnv('ENABLE_PERFORMANCE_AUDIT') === 'true',
			ENABLE_AI_DESCRIPTIONS: getEnv('ENABLE_AI_DESCRIPTIONS') === 'true',

			// Additional configuration
			SYNC_INTERVAL_MS: getEnv('SYNC_INTERVAL_MS', '300000'),
			TIMEZONE: getEnv('TIMEZONE', 'UTC'),
		};
	}

	/**
   * Get configuration value
   */
	get<K extends keyof ConfigInterface>(key: K, defaultValue?: ConfigInterface[K]): ConfigInterface[K]
	{
		return this.config[key] !== undefined ? this.config[key] : (defaultValue as ConfigInterface[K]);
	}

	/**
   * Set configuration value
   */
	set<K extends keyof ConfigInterface>(key: K, value: ConfigInterface[K]): void
	{
		this.config[key] = value;
	}

	/**
   * Get all configuration
   */
	getAll(): ConfigInterface
	{
		return { ...this.config };
	}

	/**
   * Check if running in development mode
   */
	isDevelopment(): boolean
	{
		return this.config.NODE_ENV === 'development';
	}

	/**
   * Check if running in production mode
   */
	isProduction(): boolean
	{
		return this.config.NODE_ENV === 'production';
	}

	/**
   * Check if running in test mode
   */
	isTest(): boolean
	{
		return this.config.NODE_ENV === 'test';
	}

	/**
   * Get database connection string for MariaDB
   */
	getMariaConnectionString(): string
	{
		const {
			MARIA_HOST, MARIA_PORT, MARIA_USER, MARIA_PASSWORD, MARIA_DATABASE,
		} = this.config;
		return `mysql://${MARIA_USER}:${MARIA_PASSWORD}@${MARIA_HOST}:${MARIA_PORT}/${MARIA_DATABASE}`;
	}

	/**
   * Get Redis connection string
   */
	getRedisConnectionString(): string
	{
		const {
			REDIS_HOST, REDIS_PORT, REDIS_PASSWORD, REDIS_DB,
		} = this.config;
		const auth = REDIS_PASSWORD ? `:${REDIS_PASSWORD}@` : '';
		return `redis://${auth}${REDIS_HOST}:${REDIS_PORT}/${REDIS_DB}`;
	}

	/**
   * Validate required configuration
   */
	validate(): boolean
	{
		const required: (keyof ConfigInterface)[] = [
			'SERVICE_NAME',
			'SERVICE_PORT',
		];

		const missing = required.filter(key => !this.config[key]);

		if (missing.length > 0)
		{
			throw new Error(`Missing required configuration: ${missing.join(', ')}`);
		}

		return true;
	}
}

const config = new Config();

export { config };

// Export singleton instance
export default Config;
