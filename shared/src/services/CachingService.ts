import RedisClientWrapper from '../database/RedisClient';
import ManticoreClient from '../database/ManticoreClient';
import ScyllaClient from '../database/ScyllaClient';
import { DomainAnalysis, DomainRanking, SearchResult } from '../models/DomainModels';
import { logger } from '../utils/Logger';
// import { config } from '../utils/Config';
import Constants from '../constants/Constants';

type CacheStatsType =
{
	hitRate: number;
	missRate: number;
	totalRequests: number;
	totalHits: number;
	totalMisses: number;
	cacheSize: number;
	memoryUsage: number;
};

type CacheWarmingConfigType =
{
	topDomainsCount: number;
	popularCategoriesCount: number;
	recentSearchesCount: number;
	warmingInterval: number; // in milliseconds
};

/**
 * Comprehensive caching service for domain ranking system
 */
class CachingService
{
	private redisClient: RedisClientWrapper;

	private scyllaClient: ScyllaClient;

	private manticoreClient: ManticoreClient;

	private logger = logger.getLogger('CachingService');

	private warmingInterval: NodeJS.Timeout | null = null;

	private isWarming = false;

	// Cache TTL configurations (in seconds)
	private readonly cacheTTL = {
		domainAnalysis: 3600, // 1 hour
		domainRanking: 1800, // 30 minutes
		globalRankings: 900, // 15 minutes
		categoryRankings: 900, // 15 minutes
		topDomains: 1800, // 30 minutes
		searchResults: 600, // 10 minutes
		popularDomains: 7200, // 2 hours
		systemStats: 300, // 5 minutes
	};

	// Cache statistics
	private stats = {
		totalRequests: 0,
		totalHits: 0,
		totalMisses: 0,
	};

	constructor(
		redisClient: RedisClientWrapper,
		scyllaClient: ScyllaClient,
		manticoreClient: ManticoreClient,
	)
	{
		this.redisClient = redisClient;
		this.scyllaClient = scyllaClient;
		this.manticoreClient = manticoreClient;
	}

	/**
	 * Start cache warming service
	 */
	async startCacheWarming(config?: Partial<CacheWarmingConfigType>): Promise<void>
	{
		const warmingConfig: CacheWarmingConfigType =
		{
			topDomainsCount: config?.topDomainsCount || 100,
			popularCategoriesCount: config?.popularCategoriesCount || 10,
			recentSearchesCount: config?.recentSearchesCount || 50,
			warmingInterval: config?.warmingInterval || 3600000, // 1 hour
		};

		try
		{
			this.logger.info('Starting cache warming service...');

			// Initial cache warming
			await this.performCacheWarming(warmingConfig);

			// Schedule periodic cache warming
			this.warmingInterval = setInterval(
				() => this.performCacheWarming(warmingConfig),
				warmingConfig.warmingInterval,
			);

			this.logger.info('Cache warming service started successfully');
		}
		catch (error)
		{
			this.logger.error({ error }, 'Failed to start cache warming service');
			throw error;
		}
	}

	/**
	 * Stop cache warming service
	 */
	async stopCacheWarming(): Promise<void>
	{
		if (this.warmingInterval)
		{
			clearInterval(this.warmingInterval);
			this.warmingInterval = null;
		}

		this.logger.info('Cache warming service stopped');
	}

	/**
	 * Perform cache warming operation
	 */
	private async performCacheWarming(config: CacheWarmingConfigType): Promise<void>
	{
		if (this.isWarming)
		{
			this.logger.warn('Cache warming already in progress, skipping...');
			return;
		}

		this.isWarming = true;

		try
		{
			this.logger.info('Starting cache warming operation...');

			// Warm top domains cache
			await this.warmTopDomainsCache(config.topDomainsCount);

			// Warm popular categories cache
			await this.warmPopularCategoriesCache(config.popularCategoriesCount);

			// Warm recent searches cache
			await this.warmRecentSearchesCache(config.recentSearchesCount);

			// Warm global rankings cache
			await this.warmGlobalRankingsCache();

			this.logger.info('Cache warming operation completed successfully');
		}
		catch (error)
		{
			this.logger.error({ error }, 'Cache warming operation failed');
		}
		finally
		{
			this.isWarming = false;
		}
	}

	/**
	 * Cache domain analysis with automatic TTL
	 */
	async cacheDomainAnalysis(domain: string, analysis: DomainAnalysis): Promise<void>
	{
		try
		{
			const key = this.generateCacheKey('domainAnalysis', { domain });
			await this.redisClient.set(key, analysis, this.cacheTTL.domainAnalysis);

			this.logger.debug(`Cached domain analysis: ${domain}`);
		}
		catch (error)
		{
			this.logger.error({ error }, `Failed to cache domain analysis for ${domain}`);
		}
	}

	/**
	 * Get cached domain analysis
	 */
	async getCachedDomainAnalysis(domain: string): Promise<DomainAnalysis | null>
	{
		try
		{
			this.stats.totalRequests += 1;

			const key = this.generateCacheKey('domainAnalysis', { domain });
			const cached = await this.redisClient.get<DomainAnalysis>(key);

			if (cached)
			{
				this.stats.totalHits += 1;
				this.logger.debug(`Cache hit for domain analysis: ${domain}`);
				return cached;
			}

			this.stats.totalMisses += 1;
			this.logger.debug(`Cache miss for domain analysis: ${domain}`);
			return null;
		}
		catch (error)
		{
			this.logger.error({ error }, `Failed to get cached domain analysis for ${domain}`);
			this.stats.totalMisses += 1;
			return null;
		}
	}

	/**
	 * Cache domain ranking
	 */
	async cacheDomainRanking(domain: string, ranking: DomainRanking): Promise<void>
	{
		try
		{
			const key = this.generateCacheKey('domainRanking', { domain });
			await this.redisClient.set(key, ranking, this.cacheTTL.domainRanking);

			this.logger.debug(`Cached domain ranking: ${domain}`);
		}
		catch (error)
		{
			this.logger.error({ error }, `Failed to cache domain ranking for ${domain}`);
		}
	}

	/**
	 * Get cached domain ranking
	 */
	async getCachedDomainRanking(domain: string): Promise<DomainRanking | null>
	{
		try
		{
			this.stats.totalRequests += 1;

			const key = this.generateCacheKey('domainRanking', { domain });
			const cached = await this.redisClient.get<DomainRanking>(key);

			if (cached)
			{
				this.stats.totalHits += 1;
				this.logger.debug(`Cache hit for domain ranking: ${domain}`);
				return cached;
			}

			this.stats.totalMisses += 1;
			this.logger.debug(`Cache miss for domain ranking: ${domain}`);
			return null;
		}
		catch (error)
		{
			this.logger.error({ error }, `Failed to get cached domain ranking for ${domain}`);
			this.stats.totalMisses += 1;
			return null;
		}
	}

	/**
	 * Cache global rankings
	 */
	async cacheGlobalRankings(page: number, rankings: DomainRanking[]): Promise<void>
	{
		try
		{
			const key = this.generateCacheKey('globalRankings', { page: page.toString() });
			await this.redisClient.set(key, rankings, this.cacheTTL.globalRankings);

			this.logger.debug(`Cached global rankings page: ${page}`);
		}
		catch (error)
		{
			this.logger.error({ error }, `Failed to cache global rankings page ${page}`);
		}
	}

	/**
	 * Get cached global rankings
	 */
	async getCachedGlobalRankings(page: number): Promise<DomainRanking[] | null>
	{
		try
		{
			this.stats.totalRequests += 1;

			const key = this.generateCacheKey('globalRankings', { page: page.toString() });
			const cached = await this.redisClient.get<DomainRanking[]>(key);

			if (cached)
			{
				this.stats.totalHits += 1;
				this.logger.debug(`Cache hit for global rankings page: ${page}`);
				return cached;
			}

			this.stats.totalMisses += 1;
			this.logger.debug(`Cache miss for global rankings page: ${page}`);
			return null;
		}
		catch (error)
		{
			this.logger.error({ error }, `Failed to get cached global rankings page ${page}`);
			this.stats.totalMisses += 1;
			return null;
		}
	}

	/**
	 * Cache category rankings
	 */
	async cacheCategoryRankings(
		category: string,
		page: number,
		rankings: DomainRanking[],
	): Promise<void>
	{
		try
		{
			const key = this.generateCacheKey('categoryRankings', {
				category,
				page: page.toString(),
			});
			await this.redisClient.set(key, rankings, this.cacheTTL.categoryRankings);

			this.logger.debug(`Cached category rankings: ${category}, page: ${page}`);
		}
		catch (error)
		{
			this.logger.error({ error }, `Failed to cache category rankings ${category} page ${page}`);
		}
	}

	/**
	 * Get cached category rankings
	 */
	async getCachedCategoryRankings(category: string, page: number): Promise<DomainRanking[] | null>
	{
		try
		{
			this.stats.totalRequests += 1;

			const key = this.generateCacheKey('categoryRankings', {
				category,
				page: page.toString(),
			});
			const cached = await this.redisClient.get<DomainRanking[]>(key);

			if (cached)
			{
				this.stats.totalHits += 1;
				this.logger.debug(`Cache hit for category rankings: ${category}, page: ${page}`);
				return cached;
			}

			this.stats.totalMisses += 1;
			this.logger.debug(`Cache miss for category rankings: ${category}, page: ${page}`);
			return null;
		}
		catch (error)
		{
			this.logger.error({ error }, `Failed to get cached category rankings ${category} page ${page}`);
			this.stats.totalMisses += 1;
			return null;
		}
	}

	/**
	 * Cache search results
	 */
	async cacheSearchResults(queryHash: string, results: SearchResult): Promise<void>
	{
		try
		{
			const key = this.generateCacheKey('searchResults', { queryHash });
			await this.redisClient.set(key, results, this.cacheTTL.searchResults);

			this.logger.debug(`Cached search results: ${queryHash}`);
		}
		catch (error)
		{
			this.logger.error({ error }, `Failed to cache search results ${queryHash}`);
		}
	}

	/**
	 * Get cached search results
	 */
	async getCachedSearchResults(queryHash: string): Promise<SearchResult | null>
	{
		try
		{
			this.stats.totalRequests += 1;

			const key = this.generateCacheKey('searchResults', { queryHash });
			const cached = await this.redisClient.get<SearchResult>(key);

			if (cached)
			{
				this.stats.totalHits += 1;
				this.logger.debug(`Cache hit for search results: ${queryHash}`);
				return cached;
			}

			this.stats.totalMisses += 1;
			this.logger.debug(`Cache miss for search results: ${queryHash}`);
			return null;
		}
		catch (error)
		{
			this.logger.error({ error }, `Failed to get cached search results ${queryHash}`);
			this.stats.totalMisses += 1;
			return null;
		}
	}

	/**
	 * Cache top domains
	 */
	async cacheTopDomains(
		category: string,
		timeframe: string,
		domains: Array<{ domain: string; [key: string]: unknown }>,
	): Promise<void>
	{
		try
		{
			const key = this.generateCacheKey('topDomains', { category, timeframe });
			await this.redisClient.set(key, domains, this.cacheTTL.topDomains);

			this.logger.debug(`Cached top domains: ${category}, ${timeframe}`);
		}
		catch (error)
		{
			this.logger.error({ error }, `Failed to cache top domains ${category} ${timeframe}`);
		}
	}

	/**
	 * Get cached top domains
	 */
	async getCachedTopDomains(
		category: string,
		timeframe: string,
	): Promise<Array<{ domain: string; [key: string]: unknown }> | null>
	{
		try
		{
			this.stats.totalRequests += 1;

			const key = this.generateCacheKey('topDomains', { category, timeframe });
			const cached = await this.redisClient.get<
				Array<{ domain: string; [key: string]: unknown }>
			>(key);

			if (cached)
			{
				this.stats.totalHits += 1;
				this.logger.debug(`Cache hit for top domains: ${category}, ${timeframe}`);
				return cached;
			}

			this.stats.totalMisses += 1;
			this.logger.debug(`Cache miss for top domains: ${category}, ${timeframe}`);
			return null;
		}
		catch (error)
		{
			this.logger.error({ error }, `Failed to get cached top domains ${category} ${timeframe}`);
			this.stats.totalMisses += 1;
			return null;
		}
	}

	/**
	 * Invalidate domain-specific cache
	 */
	async invalidateDomainCache(domain: string): Promise<void>
	{
		try
		{
			const patterns = [
				this.generateCacheKey('domainAnalysis', { domain }),
				this.generateCacheKey('domainRanking', { domain }),
				`*${domain}*`, // Catch any other domain-related cache
			];

			// Call del directly with patterns so mocks can observe the deletion call
			await this.redisClient.del(patterns);

			this.logger.info(`Invalidated cache for domain: ${domain}`);
		}
		catch (error)
		{
			this.logger.error({ error }, `Failed to invalidate cache for domain ${domain}`);
		}
	}

	/**
	 * Invalidate rankings cache
	 */
	async invalidateRankingsCache(): Promise<void>
	{
		try
		{
			const patterns = [
				'rankings:global:*',
				'rankings:category:*',
				'top:domains:*',
			];

			// Call del directly with patterns so mocks can observe the deletion call
			await this.redisClient.del(patterns);

			this.logger.info('Invalidated rankings cache');
		}
		catch (error)
		{
			this.logger.error({ error }, 'Failed to invalidate rankings cache');
		}
	}

	/**
	 * Invalidate search cache
	 */
	async invalidateSearchCache(): Promise<void>
	{
		try
		{
			// Call del directly with pattern so mocks can observe the deletion call
			await this.redisClient.del('search:results:*');
			this.logger.info('Invalidated search cache');
		}
		catch (error)
		{
			this.logger.error({ error }, 'Failed to invalidate search cache');
		}
	}

	/**
	 * Warm top domains cache
	 */
	private async warmTopDomainsCache(count: number): Promise<void>
	{
		try
		{
			this.logger.debug(`Warming top ${count} domains cache...`);

			// Get top domains from Manticore
			const topDomains = await this.manticoreClient.getTopDomains({
				limit: count,
			});

			// Strongly type results to avoid Record<string, unknown> inference
			const topResults = topDomains.results as Array<{ domain: string }>;

			// Cache each domain's analysis and ranking concurrently
			await Promise.all(topResults.map(async (item) =>
			{
				const [analysis, ranking] = await Promise.all([
					this.scyllaClient.getDomainAnalysis(item.domain),
					this.scyllaClient.getDomainRanking(item.domain, 'global'),
				]);

				if (analysis)
				{
					await this.cacheDomainAnalysis(item.domain, analysis);
				}

				if (ranking)
				{
					await this.cacheDomainRanking(item.domain, ranking);
				}
			}));

			this.logger.debug(`Warmed cache for ${topDomains.results.length} top domains`);
		}
		catch (error)
		{
			this.logger.error({ error }, 'Failed to warm top domains cache');
		}
	}

	/**
	 * Warm popular categories cache
	 */
	private async warmPopularCategoriesCache(count: number): Promise<void>
	{
		try
		{
			this.logger.debug('Warming popular categories cache...');

			// Get popular categories (this would be based on analytics data)
			const popularCategories = [
				'technology', 'news', 'ecommerce', 'social', 'entertainment',
				'education', 'finance', 'health', 'travel', 'business',
			].slice(0, count);

			// Warm first page of each category concurrently
			await Promise.all(popularCategories.map(async (category) =>
			{
				const rankings = await this.scyllaClient.getDomainRankings(`category:${category}`, 50);
				if (rankings.length > 0)
				{
					await this.cacheCategoryRankings(category, 1, rankings);
				}
			}));

			this.logger.debug(`Warmed cache for ${popularCategories.length} popular categories`);
		}
		catch (error)
		{
			this.logger.error({ error }, 'Failed to warm popular categories cache');
		}
	}

	/**
	 * Warm recent searches cache
	 */
	private async warmRecentSearchesCache(count: number): Promise<void>
	{
		try
		{
			this.logger.debug('Warming recent searches cache...');

			// Get recent search queries from Redis (if tracked)
			const recentSearches = await this.redisClient.smembers('analytics:recent_searches');
			const searchesToWarm = recentSearches.slice(0, count);

			await Promise.all(searchesToWarm.map(async (searchQuery) =>
			{
				try
				{
					const searchData = JSON.parse(searchQuery);
					const results = await this.manticoreClient.searchDomains(searchData);

					const searchResult = new SearchResult({
						domains: results.results,
						totalResults: results.total,
						facets: results.facets,
						searchTime: results.took,
					});

					const queryHash = this.generateSearchHash(searchData.query, searchData.filters);
					await this.cacheSearchResults(queryHash, searchResult);
				}
				catch (error)
				{
					this.logger.warn({ error }, `Failed to warm cache for search: ${searchQuery}`);
				}
			}));

			this.logger.debug(`Warmed cache for ${searchesToWarm.length} recent searches`);
		}
		catch (error)
		{
			this.logger.error({ error }, 'Failed to warm recent searches cache');
		}
	}

	/**
	 * Warm global rankings cache
	 */
	private async warmGlobalRankingsCache(): Promise<void>
	{
		try
		{
			this.logger.debug('Warming global rankings cache...');

			// Warm first 5 pages of global rankings concurrently
			await Promise.all([1, 2, 3, 4, 5].map(async (page) =>
			{
				const rankings = await this.scyllaClient.getDomainRankings('global', 50);
				if (rankings.length > 0)
				{
					await this.cacheGlobalRankings(page, rankings);
				}
			}));

			this.logger.debug('Warmed global rankings cache');
		}
		catch (error)
		{
			this.logger.error({ error }, 'Failed to warm global rankings cache');
		}
	}

	/**
	 * Get cache statistics
	 */
	async getCacheStats(): Promise<CacheStatsType>
	{
		try
		{
			const totalRequests = this.stats.totalRequests;
			const totalHits = this.stats.totalHits;
			const totalMisses = this.stats.totalMisses;

			const hitRate = totalRequests > 0 ? (totalHits / totalRequests) * 100 : 0;
			const missRate = totalRequests > 0 ? (totalMisses / totalRequests) * 100 : 0;

			// Get Redis memory usage (if available)
			let memoryUsage = 0;
			let cacheSize = 0;

			try
			{
				const client = this.redisClient.getClient();
				if (client)
				{
					const info = await client.info('memory');
					const memoryMatch = info.match(/used_memory:(\d+)/);
					if (memoryMatch)
					{
						memoryUsage = parseInt(memoryMatch[1], 10);
					}

					// Estimate cache size by counting keys
					const keys = await client.keys('*');
					cacheSize = keys.length;
				}
			}
			catch (error)
			{
				this.logger.warn({ error }, 'Failed to get Redis memory info');
			}

			return {
				hitRate: Math.round(hitRate * 100) / 100,
				missRate: Math.round(missRate * 100) / 100,
				totalRequests,
				totalHits,
				totalMisses,
				cacheSize,
				memoryUsage,
			};
		}
		catch (error)
		{
			this.logger.error({ error }, 'Failed to get cache stats');
			throw error;
		}
	}

	/**
	 * Clear all cache
	 */
	async clearAllCache(): Promise<void>
	{
		try
		{
			const client = this.redisClient.getClient();
			if (client)
			{
				// TODO-WTF: Why we clear all the keys here? What if there are other type of cache keys which are not related to this logic? What if this is a shared instance?
				const keys = await this.redisClient.keys('*');
				await this.redisClient.del(keys.length > 0 ? keys : ['*']);
				this.logger.info('Cleared all cache');
			}
		}
		catch (error)
		{
			this.logger.error({ error }, 'Failed to clear all cache');
			throw error;
		}
	}

	/**
	 * Generate cache key from template and parameters
	 */
	private generateCacheKey(
		template:
			keyof typeof Constants.CACHE_KEYS
			| 'domainAnalysis'
			| 'domainRanking'
			| 'globalRankings'
			| 'categoryRankings'
			| 'searchResults'
			| 'topDomains',
		params: Record<string, string>,
	): string
	{
		const aliasMap: Record<string, keyof typeof Constants.CACHE_KEYS> =
		{
			domainAnalysis: 'DOMAIN_ANALYSIS',
			domainRanking: 'DOMAIN_RANKING',
			globalRankings: 'GLOBAL_RANKINGS',
			categoryRankings: 'CATEGORY_RANKINGS',
			searchResults: 'SEARCH_RESULTS_HASH',
			topDomains: 'TOP_DOMAINS_TIMEFRAME',
		};

		const actualKey = (aliasMap as Record<string, keyof typeof Constants.CACHE_KEYS>)[template as string]
			|| (template as keyof typeof Constants.CACHE_KEYS);

		let base = Constants.CACHE_KEYS[actualKey] as string;

		for (const [param, value] of Object.entries(params))
		{
			base = base.replace(`{${param}}`, value.toString());
		}

		const label = (template as string) in aliasMap
			? (template as string)
			: String(actualKey);

		return `${label}:${base}`;
	}

	/**
	 * Generate hash for search query
	 */
	private generateSearchHash(query: string, filters: Record<string, unknown> = {}): string
	{
		const searchData = { query, filters };
		const searchString = JSON.stringify(searchData);

		// Simple hash function
		// let hash = 0;
		// for (let i = 0; i < searchString.length; i++)
		// {
		// 	const char = searchString.charCodeAt(i);
		// 	hash = ((hash << 5) - hash) + char;
		// 	hash &= hash; // Convert to 32-bit integer
		// }

		// return Math.abs(hash).toString(36);

		// Use URL-safe base64 encoding of the search string to avoid bitwise operations
		// TODO-CHECK: if this is really a better implementation
		const base64 = Buffer.from(searchString, 'utf8').toString('base64');
		return base64.replace(/=+$/, '').replace(/\+/g, '-').replace(/\//g, '_');
	}
}

export default CachingService;
