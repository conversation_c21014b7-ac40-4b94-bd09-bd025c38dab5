import RedisClientWrapper from '../database/RedisClient';
import CachingService from './CachingService';
import { logger } from '../utils/Logger';

type InvalidationRuleType =
{
	trigger: 'domain_update' | 'ranking_update' | 'search_index_update' | 'manual';
	patterns: string[];
	delay?: number; // Delay in milliseconds before invalidation
};

type InvalidationEventType =
{
	type: 'domain_update' | 'ranking_update' | 'search_index_update' | 'manual';
	domain?: string;
	category?: string;
	timestamp: string;
	metadata?: Record<string, unknown>;
};

/**
 * Cache invalidation service that handles automatic cache invalidation
 * based on data changes and business rules
 */
class CacheInvalidationService
{
	private redisClient: RedisClientWrapper;

	private cachingService: CachingService;

	private logger = logger.getLogger('CacheInvalidationService');

	private invalidationRules: Map<string, InvalidationRuleType> = new Map();

	private pendingInvalidations: Map<string, NodeJS.Timeout> = new Map();

	constructor(redisClient: RedisClientWrapper, cachingService: CachingService)
	{
		this.redisClient = redisClient;
		this.cachingService = cachingService;
		this.setupDefaultRules();
	}

	/**
	 * Setup default invalidation rules
	 */
	private setupDefaultRules(): void
	{
		// Domain analysis update invalidation
		this.addInvalidationRule('domain_analysis_update', {
			trigger: 'domain_update',
			patterns: [
				'analysis:{domain}',
				'ranking:domain:{domain}',
				'search:results:*',
				'dd:desc:*:{domain}',
			],
			delay: 0, // Immediate invalidation
		});

		// Domain ranking update invalidation
		this.addInvalidationRule('domain_ranking_update', {
			trigger: 'ranking_update',
			patterns: [
				'ranking:domain:{domain}',
				'rankings:global:*',
				'rankings:category:*',
				'top:domains:*',
			],
			delay: 5000, // 5 second delay to allow for batch updates
		});

		// Search index update invalidation
		this.addInvalidationRule('search_index_update', {
			trigger: 'search_index_update',
			patterns: [
				'search:results:*',
				'top:domains:*',
			],
			delay: 10000, // 10 second delay for search index updates
		});
	}

	/**
	 * Add custom invalidation rule
	 */
	addInvalidationRule(name: string, rule: InvalidationRuleType): void
	{
		this.invalidationRules.set(name, rule);
		this.logger.debug({ ruleName: name }, 'Added invalidation rule');
	}

	/**
	 * Remove invalidation rule
	 */
	removeInvalidationRule(name: string): void
	{
		this.invalidationRules.delete(name);
		this.logger.debug({ ruleName: name }, 'Removed invalidation rule');
	}

	/**
	 * Trigger cache invalidation based on event
	 */
	async triggerInvalidation(event: InvalidationEventType): Promise<void>
	{
		try
		{
			this.logger.info({ event, msg: 'Triggering cache invalidation' });

			// Find matching rules
			const matchingRules = Array.from(this.invalidationRules.entries())
				.filter(([, rule]) => rule.trigger === event.type);

			if (matchingRules.length === 0)
			{
				this.logger.warn({ eventType: event.type }, 'No invalidation rules found for event type');
				return;
			}

			// Process each matching rule in parallel
			await Promise.all(
				matchingRules.map(([ruleName, rule]) => this.processInvalidationRule(ruleName, rule, event)),
			);

			// Log invalidation event
			await this.logInvalidationEvent(event);
		}
		catch (error)
		{
			this.logger.error({ error }, 'Failed to trigger cache invalidation');
		}
	}

	/**
	 * Process individual invalidation rule
	 */
	private async processInvalidationRule(
		ruleName: string,
		rule: InvalidationRuleType,
		event: InvalidationEventType,
	): Promise<void>
	{
		try
		{
			const invalidationKey = `${ruleName}_${event.domain || 'global'}_${Date.now()}`;

			if (rule.delay && rule.delay > 0)
			{
				// Schedule delayed invalidation
				this.scheduleDelayedInvalidation(invalidationKey, rule, event);
			}
			else
			{
				// Immediate invalidation
				await this.executeInvalidation(rule, event);
			}
		}
		catch (error)
		{
			this.logger.error({ error, ruleName }, 'Failed to process invalidation rule');
		}
	}

	/**
	 * Schedule delayed invalidation
	 */
	private scheduleDelayedInvalidation(
		key: string,
		rule: InvalidationRuleType,
		event: InvalidationEventType,
	): void
	{
		// Cancel existing delayed invalidation if any
		if (this.pendingInvalidations.has(key))
		{
			clearTimeout(this.pendingInvalidations.get(key)!);
		}

		// Schedule new invalidation
		const timeout = setTimeout(async () =>
		{
			try
			{
				await this.executeInvalidation(rule, event);
				this.pendingInvalidations.delete(key);
			}
			catch (error)
			{
				this.logger.error({ error, key }, 'Delayed invalidation failed');
			}
		}, rule.delay);

		this.pendingInvalidations.set(key, timeout);
		this.logger.debug({ key, delay: rule.delay }, 'Scheduled delayed invalidation');
	}

	/**
	 * Execute cache invalidation
	 */
	private async executeInvalidation(
		rule: InvalidationRuleType,
		event: InvalidationEventType,
	): Promise<void>
	{
		try
		{
			const patterns = this.resolvePatterns(rule.patterns, event);

			await Promise.all(patterns.map(async (pattern) =>
			{
				await this.redisClient.invalidatePattern(pattern);
				this.logger.debug({ pattern }, 'Invalidated cache pattern');
			}));

			this.logger.info({ patterns }, 'Executed invalidation');
		}
		catch (error)
		{
			this.logger.error({ error }, 'Failed to execute cache invalidation');
			throw error;
		}
	}

	/**
	 * Resolve pattern placeholders with event data
	 */
	private resolvePatterns(patterns: string[], event: InvalidationEventType): string[]
	{
		return patterns.map((pattern) =>
		{
			let resolvedPattern = pattern;

			// Replace domain placeholder
			if (event.domain)
			{
				resolvedPattern = resolvedPattern.replace('{domain}', event.domain);
			}

			// Replace category placeholder
			if (event.category)
			{
				resolvedPattern = resolvedPattern.replace('{category}', event.category);
			}

			// Replace other metadata placeholders
			if (event.metadata)
			{
				for (const [key, value] of Object.entries(event.metadata))
				{
					resolvedPattern = resolvedPattern.replace(`{${key}}`, String(value));
				}
			}

			return resolvedPattern;
		});
	}

	/**
	 * Invalidate domain-specific cache
	 */
	async invalidateDomainCache(domain: string): Promise<void>
	{
		const event: InvalidationEventType = {
			type: 'domain_update',
			domain,
			timestamp: new Date().toISOString(),
		};

		await this.triggerInvalidation(event);
	}

	/**
	 * Invalidate ranking cache
	 */
	async invalidateRankingCache(domain?: string, category?: string): Promise<void>
	{
		const event: InvalidationEventType = {
			type: 'ranking_update',
			domain,
			category,
			timestamp: new Date().toISOString(),
		};

		await this.triggerInvalidation(event);
	}

	/**
	 * Invalidate search cache
	 */
	async invalidateSearchCache(): Promise<void>
	{
		const event: InvalidationEventType = {
			type: 'search_index_update',
			timestamp: new Date().toISOString(),
		};

		await this.triggerInvalidation(event);
	}

	/**
	 * Invalidate domain description cache for a specific domain
	 */
	async invalidateDomainDescriptionCache(domain: string): Promise<void>
	{
		const patterns = [
			`dd:desc:full:${domain}`,
			`dd:desc:summary:${domain}`,
			`dd:desc:metadata:${domain}`,
			`dd:desc:seo:${domain}`,
		];

		await this.manualInvalidation(patterns, { domain, type: 'domain_description' });
		this.logger.info({ domain, patterns }, 'Invalidated domain description cache');
	}

	/**
	 * Invalidate domain description cache by pattern
	 */
	async invalidateDomainDescriptionPattern(pattern: string): Promise<number>
	{
		try
		{
			const fullPattern = `dd:desc:*${pattern}*`;
			const deletedCount = await this.redisClient.invalidatePattern(fullPattern);
			this.logger.info({ pattern: fullPattern, deletedCount }, 'Invalidated domain description cache pattern');
			return deletedCount;
		}
		catch (error)
		{
			this.logger.error({ error, pattern }, 'Failed to invalidate domain description cache pattern');
			return 0;
		}
	}

	/**
	 * Manual cache invalidation
	 */
	async manualInvalidation(patterns: string[], metadata?: Record<string, unknown>): Promise<void>
	{
		const event: InvalidationEventType = {
			type: 'manual',
			timestamp: new Date().toISOString(),
			metadata,
		};

		// Create temporary rule for manual invalidation
		const tempRule: InvalidationRuleType = {
			trigger: 'manual',
			patterns,
			delay: 0,
		};

		await this.executeInvalidation(tempRule, event);
	}

	/**
	 * Get pending invalidations
	 */
	getPendingInvalidations(): string[]
	{
		return Array.from(this.pendingInvalidations.keys());
	}

	/**
	 * Cancel pending invalidation
	 */
	cancelPendingInvalidation(key: string): boolean
	{
		const timeout = this.pendingInvalidations.get(key);
		if (timeout)
		{
			clearTimeout(timeout);
			this.pendingInvalidations.delete(key);
			this.logger.debug({ key }, 'Cancelled pending invalidation');
			return true;
		}
		return false;
	}

	/**
	 * Cancel all pending invalidations
	 */
	cancelAllPendingInvalidations(): void
	{
		for (const timeout of Array.from(this.pendingInvalidations.values()))
		{
			clearTimeout(timeout);
		}
		this.pendingInvalidations.clear();
		this.logger.info({ msg: 'Cancelled all pending invalidations' });
	}

	/**
	 * Log invalidation event for analytics
	 */
	private async logInvalidationEvent(event: InvalidationEventType): Promise<void>
	{
		try
		{
			const logKey = `invalidation:log:${Date.now()}`;
			await this.redisClient.set(logKey, event, 86400); // 24 hours TTL

			// Add to invalidation history list (keep last 100 events)
			await this.redisClient.lpush('invalidation:history', JSON.stringify(event));

			// Trim list to keep only last 100 events using LTRIM for efficiency
			const client = this.redisClient.getClient();
			await client?.lTrim('invalidation:history', 0, 99);
		}
		catch (error)
		{
			this.logger.error({ error, msg: 'Failed to log invalidation event' });
		}
	}

	/**
	 * Get invalidation history
	 */
	async getInvalidationHistory(limit: number = 50): Promise<InvalidationEventType[]>
	{
		try
		{
			const historyJson = await this.redisClient.getClient()?.lRange('invalidation:history', 0, limit - 1);
			if (!historyJson) return [];

			return historyJson.map((json: string) => JSON.parse(json) as InvalidationEventType);
		}
		catch (error)
		{
			this.logger.error({ error, msg: 'Failed to get invalidation history' });
			return [];
		}
	}

	/**
	 * Get invalidation statistics
	 */
	async getInvalidationStats(): Promise<{
		totalInvalidations: number;
		invalidationsByType: Record<string, number>;
		pendingInvalidations: number;
		averageInvalidationsPerHour: number;
	}>
	{
		try
		{
			const history = await this.getInvalidationHistory(1000);
			const now = Date.now();
			const oneHourAgo = now - (60 * 60 * 1000);

			const recentInvalidations = history
				.filter(e => new Date(e.timestamp).getTime() > oneHourAgo);

			const invalidationsByType: Record<string, number> = {};
			for (const event of history)
			{
				invalidationsByType[event.type] = (invalidationsByType[event.type] || 0) + 1;
			}

			return ({
				totalInvalidations: history.length,
				invalidationsByType,
				pendingInvalidations: this.pendingInvalidations.size,
				averageInvalidationsPerHour: recentInvalidations.length,
			});
		}
		catch (error)
		{
			this.logger.error({ error, msg: 'Failed to get invalidation stats' });
			return ({
				totalInvalidations: 0,
				invalidationsByType: {},
				pendingInvalidations: 0,
				averageInvalidationsPerHour: 0,
			});
		}
	}

	/**
	 * Cleanup service - cancel pending invalidations
	 */
	async cleanup(): Promise<void>
	{
		this.cancelAllPendingInvalidations();
		this.logger.info('Cache invalidation service cleaned up');
	}
}

export default CacheInvalidationService;
