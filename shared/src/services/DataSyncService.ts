import DatabaseManager from '../database/DatabaseManager';
import ScyllaClient from '../database/ScyllaClient';
import ManticoreClient from '../database/ManticoreClient';
import RedisClientWrapper from '../database/RedisClient';
import { DomainAnalysis, DomainRanking } from '../models/DomainModels';
import SyncMonitoringService from './SyncMonitoringService';
import { logger } from '../utils/Logger';
import { config } from '../utils/Config';

// Local type aliases to avoid explicit any usage and improve clarity
// Align with Scylla execute param expectations
type QueryParamType = string | number | boolean | null;

type ManticoreDocumentType =
{
	domain: string;
	title: string;
	description: string;
	content: string;
	category: string;
	country: string;
	language: string;
	technologies: string[];
	registrar: string;
	domain_age_days: number;
	global_rank: number;
	overall_score: number;
	performance_score: number;
	security_score: number;
	seo_score: number;
	technical_score: number;
	backlink_score: number;
	traffic_estimate: number;
	ssl_grade: string;
	mobile_friendly_score: number;
	accessibility_score: number;
	last_updated: string;
};

type DomainAnalysisRowType =
{
	domain: string;
	global_rank?: number;
	category_rank?: number;
	category?: string;
	performance_metrics?: Map<string, string>;
	security_metrics?: Map<string, string>;
	seo_metrics?: Map<string, string>;
	technical_metrics?: Map<string, string>;
	technologies?: Iterable<string>;
	server_info?: Map<string, unknown>;
	domain_age_days?: number;
	registration_date?: string;
	expiration_date?: string;
	registrar?: string;
	dns_records?: Map<string, unknown>;
	screenshot_urls?: string[];
	subdomains?: Iterable<string>;
	last_crawled?: string;
	crawl_status?: string;
};

type DomainRankingRowType =
{
	domain: string;
	ranking_type: string;
	rank: number;
	overall_score: number;
	performance_score: number;
	security_score: number;
	seo_score: number;
	technical_score: number;
	backlink_score: number;
	traffic_estimate: number;
	last_updated: string;
};

/**
 * Data synchronization service to keep Manticore indexes updated with ScyllaDB data
 */
class DataSyncService
{
	private scyllaClient: ScyllaClient;

	private manticoreClient: ManticoreClient;

	private redisClient: RedisClientWrapper;

	private monitoringService: SyncMonitoringService;

	private logger = logger.getLogger('DataSyncService');

	private isRunning = false;

	private syncInterval: NodeJS.Timeout | null = null;

	constructor(databaseManager: DatabaseManager)
	{
		this.scyllaClient = databaseManager.getScyllaClient();
		this.manticoreClient = databaseManager.getManticoreClient();
		this.redisClient = databaseManager.getRedisClient();
		this.monitoringService = new SyncMonitoringService(this.redisClient);
	}

	/**
	 * Start the data synchronization service
	 */
	async start(): Promise<void>
	{
		if (this.isRunning)
		{
			this.logger.warn('Data sync service is already running');
			return;
		}

		try
		{
			this.logger.info('Starting data synchronization service...');

			// Initialize Manticore indexes if they don't exist
			await this.initializeManticoreIndexes();

			// Start periodic sync
			const syncIntervalMs = config.get('SYNC_INTERVAL_MS', '300000'); // 5 minutes default
			this.syncInterval = setInterval(
				() => this.performIncrementalSync(),
				parseInt(syncIntervalMs, 10),
			);

			this.isRunning = true;
			this.logger.info('Data synchronization service started successfully');
		}
		catch (error)
		{
			this.logger.error('Failed to start data synchronization service:', error);
			throw error;
		}
	}

	/**
	 * Stop the data synchronization service
	 */
	async stop(): Promise<void>
	{
		if (!this.isRunning)
		{
			return;
		}

		this.logger.info('Stopping data synchronization service...');

		if (this.syncInterval)
		{
			clearInterval(this.syncInterval);
			this.syncInterval = null;
		}

		this.isRunning = false;
		this.logger.info('Data synchronization service stopped');
	}

	/**
	 * Initialize Manticore Search indexes
	 */
	private async initializeManticoreIndexes(): Promise<void>
	{
		try
		{
			// Check if domains_index exists
			const domainsIndexExists = await this.manticoreClient.indexExists('domains_index');
			if (!domainsIndexExists)
			{
				await this.createDomainsIndex();
			}

			// Check if domain_content_index exists
			const contentIndexExists = await this.manticoreClient.indexExists('domain_content_index');
			if (!contentIndexExists)
			{
				await this.createDomainContentIndex();
			}

			// Check if domain_reviews_index exists
			const reviewsIndexExists = await this.manticoreClient.indexExists('domain_reviews_index');
			if (!reviewsIndexExists)
			{
				await this.createDomainReviewsIndex();
			}

			this.logger.info('Manticore indexes initialized successfully');
		}
		catch (error)
		{
			this.logger.error('Failed to initialize Manticore indexes:', error);
			throw error;
		}
	}

	/**
	 * Create domains index in Manticore
	 */
	private async createDomainsIndex(): Promise<void>
	{
		const mapping = {
			properties: {
				domain: { type: 'text', analyzer: 'standard' },
				title: { type: 'text', analyzer: 'standard' },
				description: { type: 'text', analyzer: 'standard' },
				content: { type: 'text', analyzer: 'standard' },
				category: { type: 'keyword' },
				country: { type: 'keyword' },
				language: { type: 'keyword' },
				technologies: { type: 'keyword' },
				registrar: { type: 'keyword' },
				domain_age_days: { type: 'integer' },
				global_rank: { type: 'integer' },
				overall_score: { type: 'float' },
				performance_score: { type: 'float' },
				security_score: { type: 'float' },
				seo_score: { type: 'float' },
				technical_score: { type: 'float' },
				backlink_score: { type: 'float' },
				traffic_estimate: { type: 'long' },
				ssl_grade: { type: 'keyword' },
				mobile_friendly_score: { type: 'float' },
				accessibility_score: { type: 'float' },
				last_updated: { type: 'date' },
			},
		};

		await this.manticoreClient.createIndex('domains_index', mapping);
		this.logger.info('Created domains_index in Manticore');
	}

	/**
	 * Create domain content index in Manticore
	 */
	private async createDomainContentIndex(): Promise<void>
	{
		const mapping = {
			properties: {
				domain: { type: 'text', analyzer: 'standard' },
				page_type: { type: 'keyword' },
				title: { type: 'text', analyzer: 'standard' },
				content: { type: 'text', analyzer: 'standard' },
				headings: { type: 'text', analyzer: 'standard' },
				meta_keywords: { type: 'keyword' },
				language: { type: 'keyword' },
				last_updated: { type: 'date' },
			},
		};

		await this.manticoreClient.createIndex('domain_content_index', mapping);
		this.logger.info('Created domain_content_index in Manticore');
	}

	/**
	 * Create domain reviews index in Manticore
	 */
	private async createDomainReviewsIndex(): Promise<void>
	{
		const mapping = {
			properties: {
				domain: { type: 'text', analyzer: 'standard' },
				review_text: { type: 'text', analyzer: 'standard' },
				source: { type: 'keyword' },
				rating: { type: 'float' },
				sentiment_score: { type: 'float' },
				review_date: { type: 'date' },
				verified: { type: 'boolean' },
			},
		};

		await this.manticoreClient.createIndex('domain_reviews_index', mapping);
		this.logger.info('Created domain_reviews_index in Manticore');
	}

	/**
	 * Perform incremental synchronization
	 */
	async performIncrementalSync(): Promise<void>
	{
		if (!this.isRunning)
		{
			return;
		}

		const syncId = await this.monitoringService.startSyncMonitoring('incremental');

		try
		{
			this.logger.info('Starting incremental sync...');

			// Get last sync timestamp
			const lastSyncTime = await this.getLastSyncTimestamp();
			const currentTime = new Date().toISOString();

			// Sync domain analyses
			await this.syncDomainAnalyses(lastSyncTime, syncId);

			// Sync domain rankings
			await this.syncDomainRankings(lastSyncTime, syncId);

			// Update last sync timestamp
			await this.setLastSyncTimestamp(currentTime);

			await this.monitoringService.completeSyncMonitoring(syncId);
			this.logger.info('Incremental sync completed successfully');
		}
		catch (error)
		{
			this.logger.error('Incremental sync failed:', error);
			await this.monitoringService.recordSyncError(
				syncId,
				'system',
				error instanceof Error ? error.message : String(error),
			);
			await this.monitoringService.completeSyncMonitoring(syncId);
			// Continue running despite errors
		}
	}

	/**
	 * Sync domain analyses from ScyllaDB to Manticore
	 */
	private async syncDomainAnalyses(lastSyncTime: string | null, syncId: string): Promise<void>
	{
		try
		{
			// Get updated domain analyses from ScyllaDB
			const updatedDomains = await this.getUpdatedDomainAnalyses(lastSyncTime);

			this.logger.info(`Syncing ${updatedDomains.length} domain analyses to Manticore`);

			const tasks = updatedDomains.map(async (domainAnalysis) =>
			{
				try
				{
					await this.syncDomainToManticore(domainAnalysis);
					await this.monitoringService.recordSyncSuccess(syncId, domainAnalysis.domain);
				}
				catch (error)
				{
					this.logger.error(`Failed to sync domain ${domainAnalysis.domain}:`, error);
					await this.monitoringService.recordSyncError(
						syncId,
						domainAnalysis.domain,
						(error as Error).message,
					);
				}
			});

			await Promise.all(tasks);

			this.logger.info(`Successfully synced ${updatedDomains.length} domain analyses`);
		}
		catch (error)
		{
			this.logger.error('Failed to sync domain analyses:', error);
			throw error;
		}
	}

	/**
	 * Sync domain rankings from ScyllaDB to Manticore
	 */
	private async syncDomainRankings(lastSyncTime: string | null, syncId: string): Promise<void>
	{
		try
		{
			// Get updated domain rankings from ScyllaDB
			const updatedRankings = await this.getUpdatedDomainRankings(lastSyncTime);

			this.logger.info(`Syncing ${updatedRankings.length} domain rankings to Manticore`);

			const tasks = updatedRankings.map(async (ranking) =>
			{
				try
				{
					await this.updateDomainRankingInManticore(ranking);
					await this.monitoringService.recordSyncSuccess(syncId, ranking.domain);
				}
				catch (error)
				{
					this.logger.error(`Failed to sync ranking for domain ${ranking.domain}:`, error);
					await this.monitoringService.recordSyncError(
						syncId,
						ranking.domain,
						(error as Error).message,
					);
				}
			});

			await Promise.all(tasks);

			this.logger.info(`Successfully synced ${updatedRankings.length} domain rankings`);
		}
		catch (error)
		{
			this.logger.error('Failed to sync domain rankings:', error);
			throw error;
		}
	}

	/**
	 * Get updated domain analyses from ScyllaDB since last sync
	 */
	private async getUpdatedDomainAnalyses(lastSyncTime: string | null): Promise<DomainAnalysis[]>
	{
		try
		{
			let query: string;
			let params: QueryParamType[];

			if (lastSyncTime)
			{
				// Get domains updated since last sync
				query = `
					SELECT * FROM domain_analysis
					WHERE last_crawled > ?
					ALLOW FILTERING
				`;
				params = [lastSyncTime];
			}
			else
			{
				// Get all domains for initial sync
				query = 'SELECT * FROM domain_analysis';
				params = [];
			}

			const result = await this.scyllaClient.execute(query, params);
			const domainAnalyses: DomainAnalysis[] = [];

			for (const row of result.rows)
			{
				const analysis = this.mapRowToDomainAnalysis(row as unknown as DomainAnalysisRowType);
				domainAnalyses.push(analysis);
			}

			return domainAnalyses;
		}
		catch (error)
		{
			this.logger.error('Failed to get updated domain analyses:', error);
			throw error;
		}
	}

	/**
	 * Get updated domain rankings from ScyllaDB since last sync
	 */
	private async getUpdatedDomainRankings(lastSyncTime: string | null): Promise<DomainRanking[]>
	{
		try
		{
			let query: string;
			let params: QueryParamType[];

			if (lastSyncTime)
			{
				// Get rankings updated since last sync
				query = `
					SELECT * FROM domain_rankings
					WHERE last_updated > ?
					ALLOW FILTERING
				`;
				params = [lastSyncTime];
			}
			else
			{
				// Get all rankings for initial sync
				query = 'SELECT * FROM domain_rankings';
				params = [];
			}

			const result = await this.scyllaClient.execute(query, params);
			const rankings: DomainRanking[] = [];

			for (const row of result.rows)
			{
				const ranking = this.mapRowToDomainRanking(row as unknown as DomainRankingRowType);
				rankings.push(ranking);
			}

			return rankings;
		}
		catch (error)
		{
			this.logger.error('Failed to get updated domain rankings:', error);
			throw error;
		}
	}

	/**
	 * Sync single domain analysis to Manticore
	 */
	async syncDomainToManticore(domainAnalysis: DomainAnalysis): Promise<void>
	{
		try
		{
			const manticoreDoc = this.convertDomainAnalysisToManticoreDoc(domainAnalysis);
			const docId = this.generateDocumentId(domainAnalysis.domain);

			await this.manticoreClient.upsertDocument('domains_index', docId, manticoreDoc);

			this.logger.debug(`Synced domain to Manticore: ${domainAnalysis.domain}`);
		}
		catch (error)
		{
			this.logger.error(`Failed to sync domain ${domainAnalysis.domain} to Manticore:`, error);
			throw error;
		}
	}

	/**
	 * Update domain ranking in Manticore
	 */
	private async updateDomainRankingInManticore(ranking: DomainRanking): Promise<void>
	{
		try
		{
			const docId = this.generateDocumentId(ranking.domain);

			// Update ranking fields in existing document
			const updateDoc = {
				global_rank: ranking.rank,
				overall_score: ranking.overallScore,
				performance_score: ranking.performanceScore,
				security_score: ranking.securityScore,
				seo_score: ranking.seoScore,
				technical_score: ranking.technicalScore,
				backlink_score: ranking.backlinkScore,
				traffic_estimate: ranking.trafficEstimate,
				last_updated: ranking.lastUpdated,
			};

			await this.manticoreClient.upsertDocument('domains_index', docId, updateDoc);

			this.logger.debug(`Updated domain ranking in Manticore: ${ranking.domain}`);
		}
		catch (error)
		{
			this.logger.error(`Failed to update domain ranking ${ranking.domain} in Manticore:`, error);
			throw error;
		}
	}

	/**
	 * Convert DomainAnalysis to Manticore document format
	 */
	private convertDomainAnalysisToManticoreDoc(analysis: DomainAnalysis): ManticoreDocumentType
	{
		// Safely extract values from loosely-typed records
		const metaTags = analysis.seo?.metaTags ?? {};
		const metaTitle = typeof (metaTags as Record<string, unknown>).title === 'string'
			? (metaTags as Record<string, unknown>).title as string
			: '';
		const metaDescription = typeof (metaTags as Record<string, unknown>).description === 'string'
			? (metaTags as Record<string, unknown>).description as string
			: '';
		const serverInfo = analysis.technical?.serverInfo ?? {};
		const country = typeof (serverInfo as Record<string, unknown>).country === 'string'
			? (serverInfo as Record<string, unknown>).country as string
			: '';
		const languagePrimary = analysis.languageDetection?.primary;
		const languageFromServer = typeof (serverInfo as Record<string, unknown>).language === 'string'
			? (serverInfo as Record<string, unknown>).language as string
			: undefined;
		const language = typeof languagePrimary === 'string' && languagePrimary !== ''
			? languagePrimary
			: (languageFromServer ?? 'en');

		return ({
			domain: analysis.domain,
			title: metaTitle,
			description: metaDescription,
			content: '', // Will be populated from domain_content table
			category: analysis.category,
			country,
			language,
			technologies: analysis.technical.technologies || [],
			registrar: analysis.domainInfo.registrar,
			domain_age_days: analysis.domainInfo.age,
			global_rank: analysis.globalRank || 0,
			overall_score: analysis.overallScore,
			performance_score: analysis.performance.score,
			security_score: analysis.security.score,
			seo_score: analysis.seo.score,
			technical_score: analysis.technical.score,
			backlink_score: 0, // Will be updated when backlink analysis is available
			traffic_estimate: 0, // Will be updated when traffic analysis is available
			ssl_grade: analysis.security.sslGrade,
			mobile_friendly_score: 0, // Will be updated when mobile analysis is available
			accessibility_score: 0, // Will be updated when accessibility analysis is available
			last_updated: analysis.lastUpdated,
		});
	}

	/**
	 * Generate document ID for Manticore
	 */
	private generateDocumentId(domain: string): string
	{
		return Buffer.from(domain).toString('base64').replace(/[^a-zA-Z0-9]/g, '');
	}

	/**
	 * Get last sync timestamp from Redis
	 */
	private async getLastSyncTimestamp(): Promise<string | null>
	{
		try
		{
			const timestamp = await this.redisClient.get<string>('sync:last_sync_timestamp');
			return timestamp;
		}
		catch (error)
		{
			this.logger.error('Failed to get last sync timestamp:', error);
			return null;
		}
	}

	/**
	 * Set last sync timestamp in Redis
	 */
	private async setLastSyncTimestamp(timestamp: string): Promise<void>
	{
		try
		{
			await this.redisClient.set('sync:last_sync_timestamp', timestamp);
		}
		catch (error)
		{
			this.logger.error('Failed to set last sync timestamp:', error);
		}
	}

	/**
	 * Perform full synchronization (initial sync)
	 */
	async performFullSync(): Promise<void>
	{
		const syncId = await this.monitoringService.startSyncMonitoring('full');

		try
		{
			this.logger.info('Starting full synchronization...');

			// Clear last sync timestamp to force full sync
			await this.redisClient.del('sync:last_sync_timestamp');

			// Get last sync timestamp (will be null after deletion)
			const lastSyncTime = await this.getLastSyncTimestamp();
			const currentTime = new Date().toISOString();

			// Sync domain analyses
			await this.syncDomainAnalyses(lastSyncTime, syncId);

			// Sync domain rankings
			await this.syncDomainRankings(lastSyncTime, syncId);

			// Update last sync timestamp
			await this.setLastSyncTimestamp(currentTime);

			await this.monitoringService.completeSyncMonitoring(syncId);
			this.logger.info('Full synchronization completed successfully');
		}
		catch (error)
		{
			this.logger.error('Full synchronization failed:', error);
			const msg = error instanceof Error ? error.message : String(error);
			await this.monitoringService.recordSyncError(syncId, 'system', msg);
			await this.monitoringService.completeSyncMonitoring(syncId);
			throw error;
		}
	}

	/**
	 * Get sync statistics
	 */
	async getSyncStats(): Promise<{
		lastSyncTime: string | null;
		isRunning: boolean;
		totalDomains: number;
		lastSyncDuration: number | null;
	}>
	{
		try
		{
			const lastSyncTime = await this.getLastSyncTimestamp();
			const totalDomains = await this.getTotalDomainsCount();
			const lastSyncDuration = await this.getLastSyncDuration();

			return {
				lastSyncTime,
				isRunning: this.isRunning,
				totalDomains,
				lastSyncDuration,
			};
		}
		catch (error)
		{
			this.logger.error('Failed to get sync stats:', error);
			throw error;
		}
	}

	/**
	 * Get total domains count from ScyllaDB
	 */
	private async getTotalDomainsCount(): Promise<number>
	{
		try
		{
			const result = await this.scyllaClient.execute('SELECT COUNT(*) FROM domain_analysis');
			return result.first().count.toNumber();
		}
		catch (error)
		{
			this.logger.error('Failed to get total domains count:', error);
			return 0;
		}
	}

	/**
	 * Get last sync duration from Redis
	 */
	private async getLastSyncDuration(): Promise<number | null>
	{
		try
		{
			const duration = await this.redisClient.get<string>('sync:last_sync_duration');
			return duration ? parseInt(duration, 10) : null;
		}
		catch (error)
		{
			this.logger.error('Failed to get last sync duration:', error);
			return null;
		}
	}

	/**
	 * Map ScyllaDB row to DomainAnalysis object
	 */
	private mapRowToDomainAnalysis(row: DomainAnalysisRowType): DomainAnalysis
	{
		const performanceMetrics = row.performance_metrics || new Map();
		const securityMetrics = row.security_metrics || new Map();
		const seoMetrics = row.seo_metrics || new Map();
		const technicalMetrics = row.technical_metrics || new Map();

		return new DomainAnalysis({
			domain: row.domain,
			globalRank: row.global_rank,
			categoryRank: row.category_rank,
			category: row.category,
			performance: {
				loadTime: parseFloat(performanceMetrics.get('load_time') || '0'),
				firstContentfulPaint: parseFloat(performanceMetrics.get('fcp') || '0'),
				largestContentfulPaint: parseFloat(performanceMetrics.get('lcp') || '0'),
				cumulativeLayoutShift: parseFloat(performanceMetrics.get('cls') || '0'),
				firstInputDelay: parseFloat(performanceMetrics.get('fid') || '0'),
				speedIndex: parseFloat(performanceMetrics.get('speed_index') || '0'),
				score: parseFloat(performanceMetrics.get('score') || '0'),
			},
			security: {
				sslGrade: securityMetrics.get('ssl_grade') || '',
				securityHeaders: {},
				vulnerabilities: [],
				certificateInfo: {},
				score: parseFloat(securityMetrics.get('score') || '0'),
			},
			seo: {
				metaTags: {
					title: seoMetrics.get('title') || '',
					description: seoMetrics.get('description') || '',
				},
				structuredData: [],
				sitemap: {},
				robotsTxt: {},
				score: parseFloat(seoMetrics.get('score') || '0'),
			},
			technical: {
				technologies: Array.from(row.technologies || []),
				serverInfo: Object.fromEntries(row.server_info || new Map()),
				httpHeaders: {},
				pageSize: parseInt(technicalMetrics.get('page_size') || '0', 10),
				resourceCount: {},
				score: parseFloat(technicalMetrics.get('score') || '0'),
			},
			domainInfo: {
				age: row.domain_age_days || 0,
				registrationDate: row.registration_date || null,
				expirationDate: row.expiration_date || null,
				registrar: row.registrar || '',
				dnsRecords: Object.fromEntries(row.dns_records || new Map()),
			},
			screenshots: row.screenshot_urls || [],
			subdomains: Array.from(row.subdomains || []),
			lastCrawled: row.last_crawled,
			crawlStatus: row.crawl_status || 'pending',
		});
	}

	/**
	 * Map ScyllaDB row to DomainRanking object
	 */
	private mapRowToDomainRanking(row: DomainRankingRowType): DomainRanking
	{
		return new DomainRanking({
			domain: row.domain,
			rankingType: row.ranking_type,
			rank: row.rank,
			overallScore: row.overall_score,
			performanceScore: row.performance_score,
			securityScore: row.security_score,
			seoScore: row.seo_score,
			technicalScore: row.technical_score,
			backlinkScore: row.backlink_score,
			trafficEstimate: row.traffic_estimate,
			lastUpdated: row.last_updated,
		});
	}
}

export default DataSyncService;
