import type { Mocked, MockedClass } from 'vitest';
import {
	vi, describe, beforeEach, test, expect,
} from 'vitest';
import CachingService from '../CachingService';
import RedisClientWrapper from '../../database/RedisClient';
import ScyllaClient from '../../database/ScyllaClient';
import ManticoreClient from '../../database/ManticoreClient';

// Mock dependencies
vi.mock('../../database/RedisClient');
vi.mock('../../database/ScyllaClient');
vi.mock('../../database/ManticoreClient');

const MockedRedisClient = RedisClientWrapper as MockedClass<typeof RedisClientWrapper>;
const MockedScyllaClient = ScyllaClient as MockedClass<typeof ScyllaClient>;
const MockedManticoreClient = ManticoreClient as MockedClass<typeof ManticoreClient>;

describe('CachingService', () =>
{
	let cachingService: CachingService;
	let mockRedisClient: Mocked<RedisClientWrapper>;
	let mockScyllaClient: Mocked<ScyllaClient>;
	let mockManticoreClient: Mocked<ManticoreClient>;

	beforeEach(() =>
	{
		vi.clearAllMocks();

		mockRedisClient = new MockedRedisClient() as Mocked<RedisClientWrapper>;
		mockScyllaClient = new MockedScyllaClient() as Mocked<ScyllaClient>;
		mockManticoreClient = new MockedManticoreClient() as Mocked<ManticoreClient>;

		// Mock Redis client methods
		mockRedisClient.get = vi.fn();
		mockRedisClient.set = vi.fn();
		mockRedisClient.del = vi.fn();
		mockRedisClient.exists = vi.fn();
		mockRedisClient.expire = vi.fn();
		mockRedisClient.ttl = vi.fn();
		mockRedisClient.keys = vi.fn();
		mockRedisClient.getClient = vi.fn().mockReturnValue({});

		cachingService = new CachingService(mockRedisClient, mockScyllaClient, mockManticoreClient);
	});

	describe('domain analysis caching', () =>
	{
		test('should cache domain analysis', async () =>
		{
			const domain = 'example.com';
			const analysisData = {
				domain,
				overallScore: 0.85,
				performance: { score: 0.8, loadTime: 2500 },
				security: { score: 0.9, sslGrade: 'A' },
				seo: { score: 0.8, metaTags: { title: 'Test', description: 'Test desc' } },
				technical: { score: 0.85, technologies: ['React'] },
				domainInfo: { age: 365, registrar: 'Test Registrar' },
				lastUpdated: new Date().toISOString(),
			} as any;

			mockRedisClient.set.mockResolvedValue(undefined);

			await cachingService.cacheDomainAnalysis(domain, analysisData);

			expect(mockRedisClient.set).toHaveBeenCalledWith(
				expect.stringMatching(/domainAnalysis/),
				analysisData,
				expect.any(Number),
			);
		});

		test('should retrieve cached domain analysis', async () =>
		{
			const domain = 'example.com';
			const analysisData = {
				domain,
				overallScore: 0.85,
				lastUpdated: new Date().toISOString(),
			};

			mockRedisClient.get.mockResolvedValue(analysisData);

			const result = await cachingService.getCachedDomainAnalysis(domain);

			expect(mockRedisClient.get).toHaveBeenCalledWith(
				expect.stringMatching(/domainAnalysis/),
			);
			expect(result).toEqual(analysisData);
		});

		test('should return null for non-existent domain analysis', async () =>
		{
			mockRedisClient.get.mockResolvedValue(null);

			const result = await cachingService.getCachedDomainAnalysis('non-existent.com');

			expect(result).toBeNull();
		});
	});

	describe('domain ranking caching', () =>
	{
		test('should cache domain ranking', async () =>
		{
			const domain = 'example.com';
			const rankingData = {
				domain,
				globalRank: 1,
				categoryRank: 1,
				overallScore: 0.95,
				lastUpdated: new Date().toISOString(),
			} as any;

			mockRedisClient.set.mockResolvedValue(undefined);

			await cachingService.cacheDomainRanking(domain, rankingData);

			expect(mockRedisClient.set).toHaveBeenCalledWith(
				expect.stringMatching(/domainRanking/),
				rankingData,
				expect.any(Number),
			);
		});

		test('should retrieve cached domain ranking', async () =>
		{
			const domain = 'example.com';
			const rankingData = {
				domain,
				globalRank: 1,
				categoryRank: 1,
				overallScore: 0.95,
			};

			mockRedisClient.get.mockResolvedValue(rankingData);

			const result = await cachingService.getCachedDomainRanking(domain);

			expect(mockRedisClient.get).toHaveBeenCalledWith(
				expect.stringMatching(/domainRanking/),
			);
			expect(result).toEqual(rankingData);
		});
	});

	describe('search result caching', () =>
	{
		test('should cache search results', async () =>
		{
			const queryHash = 'test-hash';
			const searchResults = {
				domains: [
					{ domain: 'tech1.com', rank: 1 },
					{ domain: 'tech2.com', rank: 2 },
				],
				totalResults: 2,
				timestamp: new Date().toISOString(),
			} as any;

			mockRedisClient.set.mockResolvedValue(undefined);

			await cachingService.cacheSearchResults(queryHash, searchResults);

			expect(mockRedisClient.set).toHaveBeenCalledWith(
				expect.stringMatching(/searchResults/),
				searchResults,
				expect.any(Number),
			);
		});

		test('should retrieve cached search results', async () =>
		{
			const queryHash = 'test-hash';
			const searchResults = {
				domains: [{ domain: 'tech1.com', rank: 1 }],
				totalResults: 1,
			};

			mockRedisClient.get.mockResolvedValue(searchResults);

			const result = await cachingService.getCachedSearchResults(queryHash);

			expect(result).toEqual(searchResults);
		});
	});

	describe('cache invalidation', () =>
	{
		test('should invalidate domain cache', async () =>
		{
			const domain = 'example.com';

			mockRedisClient.del = vi.fn().mockResolvedValue(3);

			await cachingService.invalidateDomainCache(domain);

			expect(mockRedisClient.del).toHaveBeenCalled();
		});

		test('should invalidate rankings cache', async () =>
		{
			mockRedisClient.del = vi.fn().mockResolvedValue(5);

			await cachingService.invalidateRankingsCache();

			expect(mockRedisClient.del).toHaveBeenCalled();
		});

		test('should invalidate search cache', async () =>
		{
			mockRedisClient.del = vi.fn().mockResolvedValue(10);

			await cachingService.invalidateSearchCache();

			expect(mockRedisClient.del).toHaveBeenCalled();
		});
	});

	describe('cache statistics', () =>
	{
		test('should get cache statistics', async () =>
		{
			mockRedisClient.keys = vi.fn().mockResolvedValue(['key1', 'key2']);

			const stats = await cachingService.getCacheStats();

			expect(stats).toHaveProperty('hitRate');
			expect(stats).toHaveProperty('totalRequests');
			expect(stats).toHaveProperty('cacheSize');
		});
	});

	describe('cache management', () =>
	{
		test('should clear all cache', async () =>
		{
			mockRedisClient.keys = vi.fn().mockResolvedValue(['key1', 'key2']);
			mockRedisClient.del = vi.fn().mockResolvedValue(2);

			await cachingService.clearAllCache();

			expect(mockRedisClient.del).toHaveBeenCalled();
		});
	});
});
