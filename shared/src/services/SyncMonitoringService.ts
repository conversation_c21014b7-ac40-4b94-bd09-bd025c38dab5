import RedisClientWrapper from '../database/RedisClient';
import { logger } from '../utils/Logger';

type SyncMetricsType =
{
	totalSynced: number;
	successCount: number;
	errorCount: number;
	startTime: string;
	endTime: string | null;
	duration: number | null;
	errors: SyncErrorType[];
};

type SyncErrorType =
{
	domain: string;
	error: string;
	timestamp: string;
	retryCount: number;
};

/**
 * Monitoring service for data synchronization operations
 */
class SyncMonitoringService
{
	private redisClient: RedisClientWrapper;

	private logger = logger.getLogger('SyncMonitoringService');

	private currentSyncId: string | null = null;

	constructor(redisClient: RedisClientWrapper)
	{
		this.redisClient = redisClient;
	}

	/**
	 * Start monitoring a sync operation
	 */
	async startSyncMonitoring(syncType: 'incremental' | 'full'): Promise<string>
	{
		const syncId = this.generateSyncId(syncType);
		this.currentSyncId = syncId;

		const metrics: SyncMetricsType = {
			totalSynced: 0,
			successCount: 0,
			errorCount: 0,
			startTime: new Date().toISOString(),
			endTime: null,
			duration: null,
			errors: [],
		};

		await this.redisClient.setex(`sync:metrics:${syncId}`, 86400, JSON.stringify(metrics)); // 24 hours TTL
		await this.redisClient.set('sync:current_sync_id', syncId);

		this.logger.info(`Started monitoring sync operation: ${syncId}`);
		return syncId;
	}

	/**
	 * Record successful sync operation
	 */
	async recordSyncSuccess(syncId: string, domain: string): Promise<void>
	{
		try
		{
			const metrics = await this.getSyncMetrics(syncId);
			if (metrics)
			{
				metrics.totalSynced += 1;
				metrics.successCount += 1;
				await this.updateSyncMetrics(syncId, metrics);
			}
		}
		catch (error)
		{
			this.logger.error({ error, msg: `Failed to record sync success for ${domain}` });
		}
	}

	/**
	 * Record sync error
	 */
	async recordSyncError(
		syncId: string,
		domain: string,
		error: string,
		retryCount: number = 0,
	): Promise<void>
	{
		try
		{
			const metrics = await this.getSyncMetrics(syncId);
			if (metrics)
			{
				metrics.totalSynced += 1;
				metrics.errorCount += 1;
				metrics.errors.push({
					domain,
					error,
					timestamp: new Date().toISOString(),
					retryCount,
				});

				// Keep only last 100 errors to prevent memory issues
				if (metrics.errors.length > 100)
				{
					metrics.errors = metrics.errors.slice(-100);
				}

				await this.updateSyncMetrics(syncId, metrics);
			}
		}
		catch (currentError)
		{
			this.logger.error({ error: currentError, msg: `Failed to record sync error for ${domain}` });
		}
	}

	/**
	 * Complete sync monitoring
	 */
	async completeSyncMonitoring(syncId: string): Promise<SyncMetricsType>
	{
		try
		{
			const metrics = await this.getSyncMetrics(syncId);
			if (metrics)
			{
				const endTime = new Date().toISOString();
				const startTime = new Date(metrics.startTime);
				const duration = Date.now() - startTime.getTime();

				metrics.endTime = endTime;
				metrics.duration = duration;

				await this.updateSyncMetrics(syncId, metrics);
				await this.redisClient.set('sync:last_sync_duration', duration.toString());

				this.logger.info({
					totalSynced: metrics.totalSynced,
					successCount: metrics.successCount,
					errorCount: metrics.errorCount,
					duration,
					msg: `Completed sync monitoring: ${syncId}`,
				});

				return metrics;
			}

			throw new Error(`Sync metrics not found for ID: ${syncId}`);
		}
		catch (error)
		{
			this.logger.error({ error, msg: `Failed to complete sync monitoring for ${syncId}` });
			throw error;
		}
	}

	/**
	 * Get sync metrics by ID
	 */
	async getSyncMetrics(syncId: string): Promise<SyncMetricsType | null>
	{
		try
		{
			const metrics = await this.redisClient.get<SyncMetricsType>(`sync:metrics:${syncId}`);
			return metrics ?? null;
		}
		catch (error)
		{
			this.logger.error({ error, msg: `Failed to get sync metrics for ${syncId}` });
			return null;
		}
	}

	/**
	 * Get current sync metrics
	 */
	async getCurrentSyncMetrics(): Promise<SyncMetricsType | null>
	{
		try
		{
			const currentSyncId = await this.redisClient.get<string>('sync:current_sync_id');
			if (currentSyncId)
			{
				return await this.getSyncMetrics(currentSyncId);
			}
			return null;
		}
		catch (error)
		{
			this.logger.error({ error, msg: 'Failed to get current sync metrics' });
			return null;
		}
	}

	/**
	 * Get sync history
	 */
	async getSyncHistory(limit: number = 10): Promise<SyncMetricsType[]>
	{
		try
		{
			const keys = await this.redisClient.keys('sync:metrics:*');
			// Sort keys by timestamp (newest first)
			const sortedKeys = keys.sort().reverse().slice(0, limit);

			const metricsList = await Promise.all(
				sortedKeys.map(key => this.redisClient.get<SyncMetricsType>(key)),
			);
			const history = metricsList.filter((m): m is SyncMetricsType => Boolean(m));

			return history.sort(
				(a, b) => new Date(b.startTime).getTime()
					- new Date(a.startTime).getTime(),
			);
		}
		catch (error)
		{
			this.logger.error({ error, msg: 'Failed to get sync history' });
			return [];
		}
	}

	/**
	 * Get sync error summary
	 */
	async getSyncErrorSummary(syncId: string): Promise<{
		totalErrors: number;
		errorsByType: Record<string, number>;
		domainsWithErrors: string[];
		retryableErrors: number;
	}>
	{
		try
		{
			const metrics = await this.getSyncMetrics(syncId);
			if (!metrics)
			{
				throw new Error(`Sync metrics not found for ID: ${syncId}`);
			}

			const errorsByType: Record<string, number> = {};
			const domainsWithErrors: string[] = [];
			let retryableErrors = 0;

			for (const error of metrics.errors)
			{
				// Categorize error types
				const errorType = this.categorizeError(error.error);
				errorsByType[errorType] = (errorsByType[errorType] || 0) + 1;

				// Track domains with errors
				if (!domainsWithErrors.includes(error.domain))
				{
					domainsWithErrors.push(error.domain);
				}

				// Count retryable errors
				if (this.isRetryableError(error.error) && error.retryCount < 3)
				{
					retryableErrors += 1;
				}
			}

			return {
				totalErrors: metrics.errorCount,
				errorsByType,
				domainsWithErrors,
				retryableErrors,
			};
		}
		catch (error)
		{
			this.logger.error({ error, msg: `Failed to get sync error summary for ${syncId}` });
			throw error;
		}
	}

	/**
	 * Clean up old sync metrics
	 */
	async cleanupOldMetrics(maxAge: number = 7 * 24 * 60 * 60 * 1000): Promise<void>
	{
		try
		{
			const keys = await this.redisClient.keys('sync:metrics:*');
			const cutoffTime = Date.now() - maxAge;

			const results = await Promise.all(keys.map(async (key) =>
			{
				const metrics = await this.redisClient.get<SyncMetricsType>(key);
				if (metrics && new Date(metrics.startTime).getTime() < cutoffTime)
				{
					await this.redisClient.del(key);
					return 1;
				}
				return 0;
			}));

			const deletedCount = results.reduce<number>((sum, val) => sum + val, 0);
			this.logger.info(`Cleaned up ${deletedCount} old sync metrics`);
		}
		catch (error)
		{
			this.logger.error({ error, msg: 'Failed to cleanup old sync metrics' });
		}
	}

	/**
	 * Update sync metrics in Redis
	 */
	private async updateSyncMetrics(syncId: string, metrics: SyncMetricsType): Promise<void>
	{
		await this.redisClient.setex(`sync:metrics:${syncId}`, 86400, JSON.stringify(metrics));
	}

	/**
	 * Generate unique sync ID
	 */
	private generateSyncId(syncType: string): string
	{
		const timestamp = Date.now();
		const random = Math.random().toString(36).substring(2, 8);
		return `${syncType}_${timestamp}_${random}`;
	}

	/**
	 * Categorize error types for reporting
	 */
	private categorizeError(error: string): string
	{
		const errorLower = error.toLowerCase();

		if (errorLower.includes('timeout') || errorLower.includes('connection'))
		{
			return 'connection_error';
		}
		if (errorLower.includes('not found') || errorLower.includes('404'))
		{
			return 'not_found_error';
		}
		if (errorLower.includes('permission') || errorLower.includes('unauthorized'))
		{
			return 'permission_error';
		}
		if (errorLower.includes('validation') || errorLower.includes('invalid'))
		{
			return 'validation_error';
		}
		if (errorLower.includes('database') || errorLower.includes('sql'))
		{
			return 'database_error';
		}

		return 'unknown_error';
	}

	/**
	 * Check if error is retryable
	 */
	private isRetryableError(error: string): boolean
	{
		const errorLower = error.toLowerCase();
		const retryableErrors =
		[
			'timeout',
			'connection',
			'network',
			'temporary',
			'rate limit',
			'service unavailable',
		];

		return retryableErrors.some(retryableError => errorLower.includes(retryableError));
	}
}

export default SyncMonitoringService;
