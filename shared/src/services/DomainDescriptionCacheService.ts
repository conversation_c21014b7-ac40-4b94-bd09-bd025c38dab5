import type { default as RedisClientWrapper } from '../database/RedisClient';
import type { default as DomainDescriptionInterface } from '../models/DomainDescription';
import { logger } from '../utils/Logger';
import { IdGenerator } from '../utils/IdGenerator';

/**
 * Configuration for DomainDescription caching
 */
export type DomainDescriptionCacheConfig = {
	/** Cache key prefix for domain descriptions */
	keyPrefix: string;
	/** TTL for full descriptions in seconds (24 hours default) */
	fullDescriptionTtl: number;
	/** TTL for partial/summary descriptions in seconds (6 hours default) */
	summaryTtl: number;
	/** TTL for metadata-only cache in seconds (1 hour default) */
	metadataTtl: number;
	/** Enable compression for large descriptions */
	enableCompression: boolean;
	/** Maximum cache size in bytes (for memory usage monitoring) */
	maxCacheSize: number;
	/** Enable cache warming for popular domains */
	enableWarmup: boolean;
	/** Batch size for cache operations */
	batchSize: number;
};

/**
 * Cache statistics for monitoring
 */
export type CacheStats = {
	hits: number;
	misses: number;
	sets: number;
	deletes: number;
	errors: number;
	hitRate: number;
	avgResponseTime: number;
	cacheSize: number;
	memoryUsage: number;
};

/**
 * Different cache levels for domain descriptions
 */
export enum CacheLevel {
	/** Full description with all fields */
	FULL = 'full',
	/** Summary with overview and basic metadata */
	SUMMARY = 'summary', 
	/** Metadata only (no content) */
	METADATA = 'metadata',
	/** SEO-specific fields only */
	SEO = 'seo'
}

/**
 * Redis-based caching service for domain descriptions
 * Provides multiple cache levels, compression, and performance monitoring
 */
export class DomainDescriptionCacheService {
	private redis: RedisClientWrapper;
	private config: DomainDescriptionCacheConfig;
	private logger = logger.getLogger('DomainDescriptionCache');
	private stats: CacheStats = {
		hits: 0,
		misses: 0,
		sets: 0,
		deletes: 0,
		errors: 0,
		hitRate: 0,
		avgResponseTime: 0,
		cacheSize: 0,
		memoryUsage: 0,
	};

	constructor(redis: RedisClientWrapper, config?: Partial<DomainDescriptionCacheConfig>) {
		this.redis = redis;
		this.config = {
			keyPrefix: 'dd:desc',
			fullDescriptionTtl: 24 * 60 * 60, // 24 hours
			summaryTtl: 6 * 60 * 60, // 6 hours
			metadataTtl: 1 * 60 * 60, // 1 hour
			enableCompression: true,
			maxCacheSize: 100 * 1024 * 1024, // 100MB
			enableWarmup: true,
			batchSize: 100,
			...config,
		};
	}

	/**
	 * Get domain description from cache
	 */
	async get(domain: string, level: CacheLevel = CacheLevel.FULL): Promise<DomainDescriptionInterface | null> {
		const startTime = Date.now();
		
		try {
			const key = this.buildKey(domain, level);
			const cached = await this.redis.get(key);
			
			const responseTime = Date.now() - startTime;
			this.updateStats('hit', responseTime);
			
			if (cached) {
				this.stats.hits++;
				this.logger.debug(`Cache hit for domain ${domain} at level ${level}`);
				return this.deserializeDescription(cached as string);
			} else {
				this.stats.misses++;
				this.logger.debug(`Cache miss for domain ${domain} at level ${level}`);
				return null;
			}
		} catch (error) {
			this.stats.errors++;
			this.logger.error(`Cache get error for domain ${domain}: ${error}`);
			return null;
		}
	}

	/**
	 * Set domain description in cache
	 */
	async set(
		domain: string, 
		description: DomainDescriptionInterface, 
		level: CacheLevel = CacheLevel.FULL
	): Promise<boolean> {
		const startTime = Date.now();
		
		try {
			const key = this.buildKey(domain, level);
			const data = this.filterDescriptionByLevel(description, level);
			const serialized = this.serializeDescription(data);
			const ttl = this.getTtlForLevel(level);
			
			await this.redis.set(key, serialized, ttl);
			
			const responseTime = Date.now() - startTime;
			this.updateStats('set', responseTime);
			this.stats.sets++;
			
			this.logger.debug(`Cache set for domain ${domain} at level ${level}`);
			return true;
		} catch (error) {
			this.stats.errors++;
			this.logger.error(`Cache set error for domain ${domain}: ${error}`);
			return false;
		}
	}

	/**
	 * Delete domain description from cache
	 */
	async delete(domain: string, level?: CacheLevel): Promise<boolean> {
		try {
			if (level) {
				// Delete specific level
				const key = this.buildKey(domain, level);
				await this.redis.del(key);
				this.logger.debug(`Cache delete for domain ${domain} at level ${level}`);
			} else {
				// Delete all levels for the domain
				const pattern = this.buildKey(domain, '*');
				const keys = await this.redis.keys(pattern);
				if (keys.length > 0) {
					await this.redis.del(keys);
				}
				this.logger.debug(`Cache delete all levels for domain ${domain}`);
			}
			
			this.stats.deletes++;
			return true;
		} catch (error) {
			this.stats.errors++;
			this.logger.error(`Cache delete error for domain ${domain}: ${error}`);
			return false;
		}
	}

	/**
	 * Check if domain description exists in cache
	 */
	async exists(domain: string, level: CacheLevel = CacheLevel.FULL): Promise<boolean> {
		try {
			const key = this.buildKey(domain, level);
			return await this.redis.exists(key);
		} catch (error) {
			this.logger.error(`Cache exists error for domain ${domain}: ${error}`);
			return false;
		}
	}

	/**
	 * Get multiple domain descriptions in batch
	 */
	async getBatch(domains: string[], level: CacheLevel = CacheLevel.FULL): Promise<Map<string, DomainDescriptionInterface | null>> {
		const result = new Map<string, DomainDescriptionInterface | null>();
		
		try {
			const keys = domains.map(domain => this.buildKey(domain, level));
			const values = await this.redis.mget(keys);
			
			domains.forEach((domain, index) => {
				const value = values[index];
				result.set(domain, value ? this.deserializeDescription(value as string) : null);
			});
			
			const hits = values.filter(v => v !== null).length;
			this.stats.hits += hits;
			this.stats.misses += domains.length - hits;
			
			this.logger.debug(`Cache batch get: ${domains.length} domains, ${hits} hits`);
		} catch (error) {
			this.stats.errors++;
			this.logger.error(`Cache batch get error: ${error}`);
		}
		
		return result;
	}

	/**
	 * Set multiple domain descriptions in batch
	 */
	async setBatch(
		descriptions: Map<string, DomainDescriptionInterface>, 
		level: CacheLevel = CacheLevel.FULL
	): Promise<number> {
		let successCount = 0;
		
		try {
			const pipeline = this.redis.pipeline();
			const ttl = this.getTtlForLevel(level);
			
			for (const [domain, description] of descriptions) {
				const key = this.buildKey(domain, level);
				const data = this.filterDescriptionByLevel(description, level);
				const serialized = this.serializeDescription(data);
				pipeline.setex(key, ttl, serialized);
			}
			
			await pipeline.exec();
			successCount = descriptions.size; // Assume all operations succeeded
			
			this.stats.sets += successCount;
			this.logger.debug(`Cache batch set: ${descriptions.size} total, ${successCount} successful`);
		} catch (error) {
			this.stats.errors++;
			this.logger.error(`Cache batch set error: ${error}`);
		}
		
		return successCount;
	}

	/**
	 * Warm up cache with popular domains
	 */
	async warmupCache(domains: string[], descriptions: Map<string, DomainDescriptionInterface>): Promise<void> {
		if (!this.config.enableWarmup) {
			return;
		}
		
		this.logger.info(`Starting cache warmup for ${domains.length} domains`);
		
		// Process in batches
		for (let i = 0; i < domains.length; i += this.config.batchSize) {
			const batch = domains.slice(i, i + this.config.batchSize);
			const batchDescriptions = new Map<string, DomainDescriptionInterface>();
			
			batch.forEach(domain => {
				const desc = descriptions.get(domain);
				if (desc) {
					batchDescriptions.set(domain, desc);
				}
			});
			
			if (batchDescriptions.size > 0) {
				// Cache at multiple levels for popular domains
				await Promise.all([
					this.setBatch(batchDescriptions, CacheLevel.FULL),
					this.setBatch(batchDescriptions, CacheLevel.SUMMARY),
					this.setBatch(batchDescriptions, CacheLevel.METADATA),
				]);
			}
		}
		
		this.logger.info(`Cache warmup completed for ${domains.length} domains`);
	}

	/**
	 * Invalidate cache entries by pattern
	 */
	async invalidatePattern(pattern: string): Promise<number> {
		try {
			const keys = await this.redis.keys(`${this.config.keyPrefix}:${pattern}`);
			if (keys.length > 0) {
				await this.redis.del(keys);
			}
			
			this.logger.info(`Cache pattern invalidated: ${pattern}`);
			return keys.length;
		} catch (error) {
			this.logger.error(`Cache pattern invalidation error: ${error}`);
			return 0;
		}
	}

	/**
	 * Get cache statistics
	 */
	getStats(): CacheStats {
		const total = this.stats.hits + this.stats.misses;
		this.stats.hitRate = total > 0 ? this.stats.hits / total : 0;
		return { ...this.stats };
	}

	/**
	 * Reset cache statistics
	 */
	resetStats(): void {
		this.stats = {
			hits: 0,
			misses: 0,
			sets: 0,
			deletes: 0,
			errors: 0,
			hitRate: 0,
			avgResponseTime: 0,
			cacheSize: 0,
			memoryUsage: 0,
		};
	}

	/**
	 * Get cache info and memory usage
	 */
	async getCacheInfo(): Promise<{ keyCount: number; memoryUsage: number }> {
		try {
			const info = await this.redis.info('memory');
			const keyPattern = `${this.config.keyPrefix}:*`;
			const keys = await this.redis.keys(keyPattern);
			
			const memoryMatch = info.match(/used_memory:(\d+)/);
			const memoryUsage = memoryMatch ? parseInt(memoryMatch[1]) : 0;
			
			return {
				keyCount: keys.length,
				memoryUsage,
			};
		} catch (error) {
			this.logger.error(`Cache info error: ${error}`);
			return { keyCount: 0, memoryUsage: 0 };
		}
	}

	/**
	 * Build cache key for domain and level
	 */
	private buildKey(domain: string, level: CacheLevel | string): string {
		return `${this.config.keyPrefix}:${level}:${domain}`;
	}

	/**
	 * Get TTL based on cache level
	 */
	private getTtlForLevel(level: CacheLevel): number {
		switch (level) {
			case CacheLevel.FULL:
				return this.config.fullDescriptionTtl;
			case CacheLevel.SUMMARY:
				return this.config.summaryTtl;
			case CacheLevel.METADATA:
			case CacheLevel.SEO:
				return this.config.metadataTtl;
			default:
				return this.config.fullDescriptionTtl;
		}
	}

	/**
	 * Filter description based on cache level
	 */
	private filterDescriptionByLevel(
		description: DomainDescriptionInterface, 
		level: CacheLevel
	): Partial<DomainDescriptionInterface> {
		switch (level) {
			case CacheLevel.FULL:
				return description;
			
			case CacheLevel.SUMMARY:
				return {
					metadata: description.metadata,
					overview: description.overview,
				};
			
			case CacheLevel.METADATA:
				return {
					metadata: description.metadata,
				};
			
			case CacheLevel.SEO:
				return {
					metadata: {
						domain: description.metadata.domain,
						tld: description.metadata.tld,
						status: description.metadata.status,
						category: description.metadata.category,
						tags: description.metadata.tags,
						language: description.metadata.language,
					},
					seo: description.seo,
					overview: description.overview ? {
						summary: description.overview.summary,
					} : undefined,
				};
			
			default:
				return description;
		}
	}

	/**
	 * Serialize description for storage
	 */
	private serializeDescription(description: Partial<DomainDescriptionInterface>): string {
		if (this.config.enableCompression) {
			// For now, just use JSON stringify. In future, could add compression
			return JSON.stringify(description);
		}
		return JSON.stringify(description);
	}

	/**
	 * Deserialize description from storage
	 */
	private deserializeDescription(data: string): DomainDescriptionInterface {
		try {
			return JSON.parse(data);
		} catch (error) {
			this.logger.error(`Failed to deserialize cached description: ${error}`);
			throw new Error('Invalid cached data format');
		}
	}

	/**
	 * Update internal statistics
	 */
	private updateStats(operation: 'hit' | 'set', responseTime: number): void {
		const total = this.stats.hits + this.stats.misses + this.stats.sets;
		this.stats.avgResponseTime = 
			(this.stats.avgResponseTime * total + responseTime) / (total + 1);
	}
}

export default DomainDescriptionCacheService;