export interface ApiResponseType<T = any> {
	success: boolean;
	data?: T;
	error?: {
		message: string;
		code?: string;
		details?: any;
		timestamp?: string;
		requestId?: string;
	};
	meta?: {
		pagination?: PaginationMetaType;
		version?: string;
		timestamp?: string;
		requestId?: string;
	};
}

export interface PaginationMetaType {
	page: number;
	limit: number;
	total: number;
	totalPages: number;
	hasNext: boolean;
	hasPrev: boolean;
}

export interface PaginationOptionsType {
	page?: number;
	limit?: number;
	maxLimit?: number;
}

export class ApiResponse 
{
	/**
	 * Create a successful API response
	 */
	static success<T>(data: T, meta?: ApiResponseType<T>['meta']): ApiResponseType<T> 
	{
		return {
			success: true,
			data,
			meta: {
				timestamp: new Date().toISOString(),
				...meta,
			},
		};
	}

	/**
	 * Create a successful API response with pagination
	 */
	static successWithPagination<T>(
		data: T[],
		pagination: PaginationMetaType,
		meta?: Omit<ApiResponseType<T[]>['meta'], 'pagination'>
	): ApiResponseType<T[]> 
	{
		return {
			success: true,
			data,
			meta: {
				timestamp: new Date().toISOString(),
				pagination,
				...meta,
			},
		};
	}

	/**
	 * Create an error API response
	 */
	static error(
		message: string,
		code?: string,
		details?: any,
		requestId?: string
	): ApiResponseType<null> 
	{
		return {
			success: false,
			error: {
				message,
				code,
				details,
				timestamp: new Date().toISOString(),
				requestId,
			},
		};
	}

	/**
	 * Create a validation error response
	 */
	static validationError(
		message: string = 'Validation failed',
		validationDetails?: any,
		requestId?: string
	): ApiResponseType<null> 
	{
		return this.error(message, 'VALIDATION_ERROR', validationDetails, requestId);
	}

	/**
	 * Create a not found error response
	 */
	static notFound(
		resource: string = 'Resource',
		requestId?: string
	): ApiResponseType<null> 
	{
		return this.error(`${resource} not found`, 'NOT_FOUND', null, requestId);
	}

	/**
	 * Create an unauthorized error response
	 */
	static unauthorized(
		message: string = 'Unauthorized access',
		requestId?: string
	): ApiResponseType<null> 
	{
		return this.error(message, 'UNAUTHORIZED', null, requestId);
	}

	/**
	 * Create a forbidden error response
	 */
	static forbidden(
		message: string = 'Access forbidden',
		requestId?: string
	): ApiResponseType<null> 
	{
		return this.error(message, 'FORBIDDEN', null, requestId);
	}

	/**
	 * Create a rate limit error response
	 */
	static rateLimited(
		retryAfter?: number,
		requestId?: string
	): ApiResponseType<null> 
	{
		return this.error(
			'Rate limit exceeded',
			'RATE_LIMITED',
			retryAfter ? { retryAfter } : null,
			requestId
		);
	}

	/**
	 * Create an internal server error response
	 */
	static internalError(
		message: string = 'Internal server error',
		errorId?: string,
		requestId?: string
	): ApiResponseType<null> 
	{
		return this.error(
			message,
			'INTERNAL_ERROR',
			errorId ? { errorId } : null,
			requestId
		);
	}

	/**
	 * Create pagination metadata
	 */
	static createPagination(
		page: number,
		limit: number,
		total: number
	): PaginationMetaType 
	{
		const totalPages = Math.ceil(total / limit);
		
		return {
			page,
			limit,
			total,
			totalPages,
			hasNext: page < totalPages,
			hasPrev: page > 1,
		};
	}

	/**
	 * Validate and normalize pagination options
	 */
	static validatePagination(
		options: PaginationOptionsType = {}
	): { page: number; limit: number } 
	{
		const maxLimit = options.maxLimit || 100;
		const page = Math.max(1, parseInt(String(options.page || 1), 10));
		const limit = Math.min(maxLimit, Math.max(1, parseInt(String(options.limit || 10), 10)));

		return { page, limit };
	}

	/**
	 * Extract pagination from query parameters
	 */
	static parsePaginationQuery(query: Record<string, any>): PaginationOptionsType 
	{
		return {
			page: query.page ? parseInt(String(query.page), 10) : undefined,
			limit: query.limit ? parseInt(String(query.limit), 10) : undefined,
		};
	}

	/**
	 * Check if response is successful
	 */
	static isSuccess<T>(response: ApiResponseType<T>): response is ApiResponseType<T> & { success: true; data: T } 
	{
		return response.success === true;
	}

	/**
	 * Check if response is an error
	 */
	static isError<T>(response: ApiResponseType<T>): response is ApiResponseType<T> & { success: false; error: NonNullable<ApiResponseType<T>['error']> } 
	{
		return response.success === false;
	}

	/**
	 * Extract data from successful response or throw error
	 */
	static getData<T>(response: ApiResponseType<T>): T 
	{
		if (this.isSuccess(response)) 
		{
			return response.data;
		}
		throw new Error(response.error?.message || 'API request failed');
	}

	/**
	 * Transform response for different API versions
	 */
	static transformForVersion<T>(
		response: ApiResponseType<T>,
		version: string
	): ApiResponseType<T> 
	{
		return {
			...response,
			meta: {
				...response.meta,
				version,
			},
		};
	}
}

export default ApiResponse;
