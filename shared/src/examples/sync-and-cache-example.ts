import DatabaseManager from '../database/DatabaseManager';
import DataSyncService from '../services/DataSyncService';
import CachingService from '../services/CachingService';
import CacheInvalidationService from '../services/CacheInvalidationService';
import { logger } from '../utils/Logger';

/**
 * Example demonstrating how to use the data synchronization and caching services
 */
class SyncAndCacheExample
{
	private databaseManager: DatabaseManager;

	private dataSyncService!: DataSyncService;

	private cachingService!: CachingService;

	private cacheInvalidationService!: CacheInvalidationService;

	private logger = logger.getLogger('SyncAndCacheExample');

	constructor()
	{
		this.databaseManager = new DatabaseManager();
	}

	/**
	 * Initialize all services
	 */
	async initialize(): Promise<void>
	{
		try
		{
			this.logger.info('Initializing sync and cache services...');

			// Initialize database connections
			await this.databaseManager.initialize();

			// Initialize data sync service
			this.dataSyncService = new DataSyncService(this.databaseManager);

			// Initialize caching service
			this.cachingService = new CachingService(
				this.databaseManager.getRedisClient(),
				this.databaseManager.getScyllaClient(),
				this.databaseManager.getManticoreClient(),
			);

			// Initialize cache invalidation service
			this.cacheInvalidationService = new CacheInvalidationService(
				this.databaseManager.getRedisClient(),
				this.cachingService,
			);

			this.logger.info('All services initialized successfully');
		}
		catch (error)
		{
			this.logger.error('Failed to initialize services:', error);
			throw error;
		}
	}

	/**
	 * Start all services
	 */
	async start(): Promise<void>
	{
		try
		{
			this.logger.info('Starting sync and cache services...');

			// Start data synchronization service
			await this.dataSyncService.start();

			// Start cache warming
			await this.cachingService.startCacheWarming({
				topDomainsCount: 50,
				popularCategoriesCount: 5,
				recentSearchesCount: 25,
				warmingInterval: 1800000, // 30 minutes
			});

			this.logger.info('All services started successfully');
		}
		catch (error)
		{
			this.logger.error('Failed to start services:', error);
			throw error;
		}
	}

	/**
	 * Stop all services
	 */
	async stop(): Promise<void>
	{
		try
		{
			this.logger.info('Stopping sync and cache services...');

			// Stop data synchronization service
			await this.dataSyncService.stop();

			// Stop cache warming
			await this.cachingService.stopCacheWarming();

			// Cleanup cache invalidation service
			await this.cacheInvalidationService.cleanup();

			// Close database connections
			await this.databaseManager.close();

			this.logger.info('All services stopped successfully');
		}
		catch (error)
		{
			this.logger.error('Failed to stop services:', error);
			throw error;
		}
	}

	/**
	 * Example: Perform full synchronization
	 */
	async performFullSync(): Promise<void>
	{
		try
		{
			this.logger.info('Performing full synchronization...');

			// Perform full sync from ScyllaDB to Manticore
			await this.dataSyncService.performFullSync();

			// Invalidate all cache to ensure fresh data
			await this.cachingService.clearAllCache();

			// Warm cache with fresh data
			await this.cachingService.startCacheWarming({
				topDomainsCount: 100,
				popularCategoriesCount: 10,
				recentSearchesCount: 50,
				warmingInterval: 3600000,
			});

			this.logger.info('Full synchronization completed');
		}
		catch (error)
		{
			this.logger.error('Full synchronization failed:', error);
			throw error;
		}
	}

	/**
	 * Example: Handle domain update
	 */
	async handleDomainUpdate(domain: string): Promise<void>
	{
		try
		{
			this.logger.info(`Handling domain update: ${domain}`);

			// Invalidate domain-specific cache
			await this.cacheInvalidationService.invalidateDomainCache(domain);

			// Get fresh domain analysis from ScyllaDB
			const analysis = await this.databaseManager.getScyllaClient().getDomainAnalysis(domain);
			if (analysis)
			{
				// Sync to Manticore
				await this.dataSyncService.syncDomainToManticore(analysis);

				// Cache the fresh data
				await this.cachingService.cacheDomainAnalysis(domain, analysis);
			}

			// Get fresh domain ranking
			const ranking = await this.databaseManager.getScyllaClient().getDomainRanking(domain, 'global');
			if (ranking)
			{
				// Cache the fresh ranking
				await this.cachingService.cacheDomainRanking(domain, ranking);
			}

			this.logger.info(`Domain update handled: ${domain}`);
		}
		catch (error)
		{
			this.logger.error(`Failed to handle domain update for ${domain}:`, error);
			throw error;
		}
	}

	/**
	 * Example: Handle ranking update
	 */
	async handleRankingUpdate(category?: string): Promise<void>
	{
		try
		{
			this.logger.info(`Handling ranking update${category ? ` for category: ${category}` : ''}`);

			// Invalidate ranking cache
			await this.cacheInvalidationService.invalidateRankingCache(undefined, category);

			// Warm fresh ranking data
			if (category)
			{
				// Warm specific category rankings
				const rankings = await this.databaseManager.getScyllaClient()
					.getDomainRankings(`category:${category}`, 50);
				if (rankings.length > 0)
				{
					await this.cachingService.cacheCategoryRankings(category, 1, rankings);
				}
			}
			else
			{
				// Warm global rankings
				const globalRankings = await this.databaseManager.getScyllaClient()
					.getDomainRankings('global', 50);
				if (globalRankings.length > 0)
				{
					await this.cachingService.cacheGlobalRankings(1, globalRankings);
				}
			}

			this.logger.info('Ranking update handled');
		}
		catch (error)
		{
			this.logger.error('Failed to handle ranking update:', error);
			throw error;
		}
	}

	/**
	 * Example: Get service statistics
	 */
	async getServiceStats(): Promise<{
		syncStats: {
			lastSyncTime: string | null;
			isRunning: boolean;
			totalDomains: number;
			lastSyncDuration: number | null;
		};
		cacheStats: {
			hitRate: number;
			missRate: number;
			totalRequests: number;
			totalHits: number;
			totalMisses: number;
			cacheSize: number;
			memoryUsage: number;
		};
		invalidationStats: {
			totalInvalidations: number;
			invalidationsByType: Record<string, number>;
			pendingInvalidations: number;
			averageInvalidationsPerHour: number;
		};
	}>
	{
		try
		{
			const [syncStats, cacheStats, invalidationStats] = await Promise.all([
				this.dataSyncService.getSyncStats(),
				this.cachingService.getCacheStats(),
				this.cacheInvalidationService.getInvalidationStats(),
			]);

			return {
				syncStats,
				cacheStats,
				invalidationStats,
			};
		}
		catch (error)
		{
			this.logger.error('Failed to get service stats:', error);
			throw error;
		}
	}

	/**
	 * Example: Health check for all services
	 */
	async healthCheck(): Promise<{
		database: {
			scylla: boolean;
			maria: boolean;
			redis: boolean;
			manticore: boolean;
		};
		sync: boolean;
		cache: boolean;
		invalidation: boolean;
	}>
	{
		try
		{
			const databaseHealth = await this.databaseManager.healthCheck();

			return ({
				database: databaseHealth,
				sync: !!this.dataSyncService,
				cache: !!this.cachingService,
				invalidation: !!this.cacheInvalidationService,
			});
		}
		catch (error)
		{
			this.logger.error('Health check failed:', error);
			return ({
				database: {
					scylla: false, maria: false, redis: false, manticore: false,
				},
				sync: false,
				cache: false,
				invalidation: false,
			});
		}
	}
}

export default SyncAndCacheExample;
