/**
 * Search Result class for domain ranking system
 */
import type { SearchResultData } from './types/DomainTypes';

/**
 * Search Result Class
 */
class SearchResult
{
	domains: unknown[];

	totalResults: number;

	currentPage: number;

	totalPages: number;

	filters: Record<string, unknown>;

	facets: Record<string, unknown>;

	searchTime: number;

	constructor(data: SearchResultData = {})
	{
		this.domains = data.domains || [];
		this.totalResults = data.totalResults || 0;
		this.currentPage = data.currentPage || 1;
		this.totalPages = data.totalPages || 1;
		this.filters = data.filters || {};
		this.facets = data.facets || {};
		this.searchTime = data.searchTime || 0;
	}
}

export { SearchResult };
export default SearchResult;
