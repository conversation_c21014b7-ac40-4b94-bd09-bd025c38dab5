/**
 * Domain Crawl Job class for domain ranking system
 */
import type { DomainCrawlJobDataType } from './types/DomainTypes';

/**
 * Domain Crawl Job Class
 */
class DomainCrawlJob
{
	jobId: string;

	domain: string;

	crawlType: 'full' | 'quick' | 'security' | 'performance';

	priority: 'high' | 'medium' | 'low';

	status: 'pending' | 'processing' | 'completed' | 'failed';

	retryCount: number;

	maxRetries: number;

	scheduledAt: string;

	startedAt: string | null;

	completedAt: string | null;

	errorMessage: string | null;

	userAgent: string;

	pagesToCrawl: string[];

	constructor(data: DomainCrawlJobDataType = {})
	{
		this.jobId = data.jobId || this.generateJobId();
		this.domain = data.domain || '';
		this.crawlType = data.crawlType || 'full';
		this.priority = data.priority || 'medium';
		this.status = data.status || 'pending';
		this.retryCount = data.retryCount || 0;
		this.maxRetries = data.maxRetries || 3;
		this.scheduledAt = data.scheduledAt || new Date().toISOString();
		this.startedAt = data.startedAt || null;
		this.completedAt = data.completedAt || null;
		this.errorMessage = data.errorMessage || null;
		this.userAgent = data.userAgent || '';
		this.pagesToCrawl = data.pagesToCrawl || ['homepage'];
	}

	generateJobId(): string
	{
		return `crawl_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
	}

	/**
	 * Mark job as started
	 */
	markStarted(): void
	{
		this.status = 'processing';
		this.startedAt = new Date().toISOString();
	}

	/**
	 * Mark job as completed
	 */
	markCompleted(): void
	{
		this.status = 'completed';
		this.completedAt = new Date().toISOString();
	}

	/**
	 * Mark job as failed
	 */
	markFailed(errorMessage: string): void
	{
		this.status = 'failed';
		this.errorMessage = errorMessage;
		this.completedAt = new Date().toISOString();
	}

	/**
	 * Check if job can be retried
	 */
	canRetry(): boolean
	{
		return this.retryCount < this.maxRetries;
	}

	/**
	 * Increment retry count
	 */
	incrementRetry(): void
	{
		this.retryCount += 1;
		this.status = 'pending';
		this.startedAt = null;
		this.errorMessage = null;
	}
}

export default DomainCrawlJob;
