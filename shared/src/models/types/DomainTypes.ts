/**
 * Type definitions for domain ranking system
 */

type PerformanceMetricsType =
{
	loadTime: number;
	firstContentfulPaint: number;
	largestContentfulPaint: number;
	cumulativeLayoutShift: number;
	firstInputDelay: number;
	speedIndex: number;
	score: number;
};

type SecurityMetricsType =
{
	sslGrade: string;
	securityHeaders: Record<string, unknown>;
	vulnerabilities: unknown[];
	certificateInfo: Record<string, unknown>;
	score: number;
};

type SEOMetricsType =
{
	metaTags: Record<string, unknown>;
	structuredData: unknown[];
	sitemap: Record<string, unknown>;
	robotsTxt: Record<string, unknown>;
	score: number;
};

type TechnicalMetricsType =
{
	technologies: string[];
	serverInfo: Record<string, unknown>;
	httpHeaders: Record<string, unknown>;
	pageSize: number;
	resourceCount: Record<string, unknown>;
	score: number;
};

type ContentQualityMetricsType =
{
	overallScore: number;
	contentLength: number;
	readabilityScore: number;
	mediaRichness: number;
	structuralQuality: number;
	uniqueness: number;
	freshness: number;
};

type LanguageDetectionType =
{
	primary: string;
	confidence: number;
	alternatives: Array<{ language: string; confidence: number }>;
};

type ContentMetricsType =
{
	totalWordCount: number;
	averageReadability: number;
	duplicateContentPercentage: number;
	mediaToTextRatio: number;
	headingStructureScore: number;
	internalLinkDensity: number;
};

type TechnicalSEOType =
{
	hasStructuredData: boolean;
	structuredDataTypes: string[];
	hasCanonicalTags: boolean;
	hasMetaDescriptions: boolean;
	hasAltTags: boolean;
	imageOptimization: number;
};

type DomainInfoType =
{
	age: number;
	registrationDate: string | null;
	expirationDate: string | null;
	registrar: string;
	dnsRecords: Record<string, unknown>;
};

type DomainAnalysisDataType =
{
	domain?: string;
	globalRank?: number | null;
	categoryRank?: number | null;
	category?: string;
	overallScore?: number;
	performance?: Partial<PerformanceMetricsType>;
	security?: Partial<SecurityMetricsType>;
	seo?: Partial<SEOMetricsType>;
	technical?: Partial<TechnicalMetricsType>;
	domainInfo?: Partial<DomainInfoType>;
	contentQuality?: Partial<ContentQualityMetricsType>;
	languageDetection?: Partial<LanguageDetectionType>;
	contentMetrics?: Partial<ContentMetricsType>;
	technicalSEO?: Partial<TechnicalSEOType>;
	screenshots?: string[];
	subdomains?: string[];
	lastCrawled?: string | null;
	crawlStatus?: string;
	lastUpdated?: string;
};

type RankingWeightsType =
{
	performance: number;
	security: number;
	seo: number;
	technical: number;
	backlinks: number;
};

type DomainRankingDataType =
{
	domain?: string;
	rankingType?: string;
	rank?: number;
	overallScore?: number;
	performanceScore?: number;
	securityScore?: number;
	seoScore?: number;
	technicalScore?: number;
	backlinkScore?: number;
	trafficEstimate?: number;
	lastUpdated?: string;
};

type DomainCrawlJobDataType =
{
	jobId?: string;
	domain?: string;
	crawlType?: 'full' | 'quick' | 'security' | 'performance';
	priority?: 'high' | 'medium' | 'low';
	status?: 'pending' | 'processing' | 'completed' | 'failed';
	retryCount?: number;
	maxRetries?: number;
	scheduledAt?: string;
	startedAt?: string | null;
	completedAt?: string | null;
	errorMessage?: string | null;
	userAgent?: string;
	pagesToCrawl?: string[];
};

type SearchResultData =
{
	domains?: unknown[];
	totalResults?: number;
	currentPage?: number;
	totalPages?: number;
	filters?: Record<string, unknown>;
	facets?: Record<string, unknown>;
	searchTime?: number;
};

export type {
	PerformanceMetricsType,
	SecurityMetricsType,
	SEOMetricsType,
	TechnicalMetricsType,
	ContentQualityMetricsType,
	LanguageDetectionType,
	ContentMetricsType,
	TechnicalSEOType,
	DomainInfoType,
	DomainAnalysisDataType,
	RankingWeightsType,
	DomainRankingDataType,
	DomainCrawlJobDataType,
	SearchResultData,
};
