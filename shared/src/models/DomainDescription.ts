
interface DomainDescriptionInterface
{
	metadata: {
		domain: string
		tld: string
		status: 'active' | 'parked' | 'redirect' | 'inactive'
		country?: string // ISO 3166-1 alpha-2 country code
		language?: string // ISO 639-1 language code
		registration?: {
			firstRegisteredAt?: string
			lastUpdatedAt?: string
			expiresAt?: string
		}
		idn?: {
			isIdn: boolean
			unicode?: string
			ascii?: string
		}
		owner?: { organization?: string; registrar?: string }
		category: { primary: string; secondary?: string }
		tags: string[] // 5-12 items required, mandatory for preGenerated content
		preGenerated?: boolean // Indicates if content was generated without live analysis
	}
	overview?: {
		summary: string // Minimum 320 words for SEO optimization, mandatory for preGenerated content
		keyFeatures?: string[]
		cta?: { label?: string; url?: string }
	}
	technical?: {
		technologies?: string[]
		performance?: {
			loadTimeMs?: number;
			coreWebVitals?: {
				fcp?: number; lcp?: number; cls?: number; fid?: number; speedIndex?: number;
			};
		}
		security?: {
			sslGrade?: string
			securityHeaders?: { hsts?: boolean; csp?: boolean; xFrameOptions?: boolean }
			vulnerabilities?: { id: string; severity: 'low' | 'medium' | 'high' | 'critical'; summary?: string }[]
			certificate?: { issuer?: string; expiration?: string }
		}
		mobileFriendlyScore?: number
		accessibilityScore?: number
		dns?: {
			a?: string[]; aaaa?: string[]; mx?: string[]; cname?: string[]; txt?: string[]; ns?: string[];
		};
		hosting?: { provider?: string; region?: string }
	}
	seo?: {
		title?: string
		metaDescription?: string
		structuredData?: string[]
		sitemap?: { present?: boolean; url?: string }
		robots?: { present?: boolean; policy?: 'allow' | 'disallow' | 'mixed' }
		languages?: string[]
	}
	contentProfile?: {
		topics?: string[]
		formats?: string[]
		updateFrequency?: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'sporadic'
		contentLength?: { avgWords?: number }
		media?: { images?: number; videos?: number }
	}
	reputation?: {
		backlinks?: { estimate?: number; qualityScore?: number }
		mentions?: { source?: string; url?: string }[]
		social?: { twitter?: string[]; facebook?: string[]; linkedin?: string[]; instagram?: string[] }
		reviews?: { averageRating?: number; sources?: string[]; sentimentScore?: number }
	}
	ranking: {
		globalRank?: number
		categoryRank?: number
		scores?: {
			performance?: number;
			security?: number;
			seo?: number;
			technical?: number;
			backlink?: number;
			overall?: number;
		}
		trend?: 'improving' | 'stable' | 'declining'
		trafficEstimateMonthly?: number
	}
	compliance?: {
		privacyPolicyUrl?: string;
		termsUrl?: string; cookiesPresent?:
		boolean; accessibilityNotes?: string;
	}
	crawl: { lastCrawled: string; crawlType: 'quick' | 'full' | 'security' | 'performance' | 'content'; errors?: number; screenshotUrls?: string[]; subdomains?: string[] }
}

export default DomainDescriptionInterface;
