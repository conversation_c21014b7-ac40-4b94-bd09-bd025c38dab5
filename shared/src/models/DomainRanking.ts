/**
 * Domain Ranking class for domain ranking system
 */
import type { DomainRankingDataType } from './types/DomainTypes';

/**
 * Domain Ranking Class
 */
class DomainRanking
{
	domain: string;

	rankingType: string;

	rank: number;

	overallScore: number;

	performanceScore: number;

	securityScore: number;

	seoScore: number;

	technicalScore: number;

	backlinkScore: number;

	trafficEstimate: number;

	lastUpdated: string;

	constructor(data: DomainRankingDataType = {})
	{
		this.domain = data.domain || '';
		this.rankingType = data.rankingType || 'global';
		this.rank = data.rank || 0;
		this.overallScore = data.overallScore || 0;
		this.performanceScore = data.performanceScore || 0;
		this.securityScore = data.securityScore || 0;
		this.seoScore = data.seoScore || 0;
		this.technicalScore = data.technicalScore || 0;
		this.backlinkScore = data.backlinkScore || 0;
		this.trafficEstimate = data.trafficEstimate || 0;
		this.lastUpdated = data.lastUpdated || new Date().toISOString();
	}

	/**
	 * Convert to ScyllaDB format
	 */
	toScyllaFormat(): Record<string, unknown>
	{
		return ({
			ranking_type: this.rankingType,
			rank: this.rank,
			domain: this.domain,
			overall_score: this.overallScore,
			performance_score: this.performanceScore,
			security_score: this.securityScore,
			seo_score: this.seoScore,
			technical_score: this.technicalScore,
			backlink_score: this.backlinkScore,
			traffic_estimate: this.trafficEstimate,
			last_updated: this.lastUpdated,
		});
	}
}

export default DomainRanking;
