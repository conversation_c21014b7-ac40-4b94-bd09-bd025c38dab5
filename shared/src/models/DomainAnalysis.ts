/**
 * Domain Analysis class for domain ranking system
 */
import type {
	DomainAnalysisDataType,
	PerformanceMetricsType,
	SecurityMetricsType,
	SEOMetricsType,
	TechnicalMetricsType,
	DomainInfoType,
	ContentQualityMetricsType,
	LanguageDetectionType,
	ContentMetricsType,
	TechnicalSEOType,
	RankingWeightsType,
} from './types/DomainTypes';

/**
 * Domain Analysis Result Class
 */
class DomainAnalysis
{
	domain: string;

	globalRank: number | null;

	categoryRank: number | null;

	category: string;

	overallScore: number;

	performance: PerformanceMetricsType;

	security: SecurityMetricsType;

	seo: SEOMetricsType;

	technical: TechnicalMetricsType;

	domainInfo: DomainInfoType;

	contentQuality: ContentQualityMetricsType;

	languageDetection: LanguageDetectionType;

	contentMetrics: ContentMetricsType;

	technicalSEO: TechnicalSEOType;

	screenshots: string[];

	subdomains: string[];

	lastCrawled: string | null;

	crawlStatus: string;

	lastUpdated: string;

	constructor(data: DomainAnalysisDataType = {})
	{
		this.domain = data.domain || '';
		this.globalRank = data.globalRank || null;
		this.categoryRank = data.categoryRank || null;
		this.category = data.category || '';
		this.overallScore = data.overallScore || 0;

		// Performance metrics
		this.performance = {
			loadTime: data.performance?.loadTime || 0,
			firstContentfulPaint: data.performance?.firstContentfulPaint || 0,
			largestContentfulPaint: data.performance?.largestContentfulPaint || 0,
			cumulativeLayoutShift: data.performance?.cumulativeLayoutShift || 0,
			firstInputDelay: data.performance?.firstInputDelay || 0,
			speedIndex: data.performance?.speedIndex || 0,
			score: data.performance?.score || 0,
		};

		// Security metrics
		this.security = {
			sslGrade: data.security?.sslGrade || '',
			securityHeaders: data.security?.securityHeaders || {},
			vulnerabilities: data.security?.vulnerabilities || [],
			certificateInfo: data.security?.certificateInfo || {},
			score: data.security?.score || 0,
		};

		// SEO metrics
		this.seo = {
			metaTags: data.seo?.metaTags || {},
			structuredData: data.seo?.structuredData || [],
			sitemap: data.seo?.sitemap || {},
			robotsTxt: data.seo?.robotsTxt || {},
			score: data.seo?.score || 0,
		};

		// Technical metrics
		this.technical = {
			technologies: data.technical?.technologies || [],
			serverInfo: data.technical?.serverInfo || {},
			httpHeaders: data.technical?.httpHeaders || {},
			pageSize: data.technical?.pageSize || 0,
			resourceCount: data.technical?.resourceCount || {},
			score: data.technical?.score || 0,
		};

		// Domain information
		this.domainInfo = {
			age: data.domainInfo?.age || 0,
			registrationDate: data.domainInfo?.registrationDate || null,
			expirationDate: data.domainInfo?.expirationDate || null,
			registrar: data.domainInfo?.registrar || '',
			dnsRecords: data.domainInfo?.dnsRecords || {},
		};

		// Content analysis
		this.contentQuality = {
			overallScore: data.contentQuality?.overallScore || 0,
			contentLength: data.contentQuality?.contentLength || 0,
			readabilityScore: data.contentQuality?.readabilityScore || 0,
			mediaRichness: data.contentQuality?.mediaRichness || 0,
			structuralQuality: data.contentQuality?.structuralQuality || 0,
			uniqueness: data.contentQuality?.uniqueness || 0,
			freshness: data.contentQuality?.freshness || 0,
		};

		this.languageDetection = {
			primary: data.languageDetection?.primary || 'en',
			confidence: data.languageDetection?.confidence || 0,
			alternatives: data.languageDetection?.alternatives || [],
		};

		this.contentMetrics = {
			totalWordCount: data.contentMetrics?.totalWordCount || 0,
			averageReadability: data.contentMetrics?.averageReadability || 0,
			duplicateContentPercentage: data.contentMetrics?.duplicateContentPercentage || 0,
			mediaToTextRatio: data.contentMetrics?.mediaToTextRatio || 0,
			headingStructureScore: data.contentMetrics?.headingStructureScore || 0,
			internalLinkDensity: data.contentMetrics?.internalLinkDensity || 0,
		};

		this.technicalSEO = {
			hasStructuredData: data.technicalSEO?.hasStructuredData || false,
			structuredDataTypes: data.technicalSEO?.structuredDataTypes || [],
			hasCanonicalTags: data.technicalSEO?.hasCanonicalTags || false,
			hasMetaDescriptions: data.technicalSEO?.hasMetaDescriptions || false,
			hasAltTags: data.technicalSEO?.hasAltTags || false,
			imageOptimization: data.technicalSEO?.imageOptimization || 0,
		};

		// Visual data
		this.screenshots = data.screenshots || [];
		this.subdomains = data.subdomains || [];

		// Metadata
		this.lastCrawled = data.lastCrawled || null;
		this.crawlStatus = data.crawlStatus || 'pending';
		this.lastUpdated = data.lastUpdated || new Date().toISOString();
	}

	/**
	 * Calculate overall score based on weighted metrics
	 */
	calculateOverallScore(weights?: RankingWeightsType): number
	{
		const defaultWeights: RankingWeightsType = {
			performance: 0.25,
			security: 0.20,
			seo: 0.20,
			technical: 0.15,
			backlinks: 0.20,
		};

		const w = weights || defaultWeights;

		this.overallScore = (
			(this.performance.score * w.performance)
			+ (this.security.score * w.security)
			+ (this.seo.score * w.seo)
			+ (this.technical.score * w.technical)
			// Note: backlinks score would be added when available
		);

		return this.overallScore;
	}

	/**
	 * Convert to database format for ScyllaDB
	 */
	toScyllaFormat(): Record<string, unknown>
	{
		const metaTags = this.seo.metaTags as Record<string, unknown>;

		return {
			domain: this.domain,
			global_rank: this.globalRank,
			category: this.category,
			category_rank: this.categoryRank,
			performance_metrics: new Map(Object.entries({
				load_time: this.performance.loadTime.toString(),
				fcp: this.performance.firstContentfulPaint.toString(),
				lcp: this.performance.largestContentfulPaint.toString(),
				cls: this.performance.cumulativeLayoutShift.toString(),
				fid: this.performance.firstInputDelay.toString(),
				speed_index: this.performance.speedIndex.toString(),
				score: this.performance.score.toString(),
			})),
			security_metrics: new Map(Object.entries({
				ssl_grade: this.security.sslGrade,
				score: this.security.score.toString(),
				vulnerabilities_count: this.security.vulnerabilities.length.toString(),
			})),
			seo_metrics: new Map(Object.entries({
				title: typeof metaTags.title === 'string' ? metaTags.title : '',
				description: typeof metaTags.description === 'string' ? metaTags.description : '',
				score: this.seo.score.toString(),
			})),
			technical_metrics: new Map(Object.entries({
				page_size: this.technical.pageSize.toString(),
				score: this.technical.score.toString(),
			})),
			technologies: new Set(this.technical.technologies),
			server_info: new Map(Object.entries(this.technical.serverInfo)),
			domain_age_days: this.domainInfo.age,
			registration_date: this.domainInfo.registrationDate,
			expiration_date: this.domainInfo.expirationDate,
			registrar: this.domainInfo.registrar,
			dns_records: new Map(Object.entries(this.domainInfo.dnsRecords)),
			content_metrics: new Map(Object.entries({
				total_word_count: this.contentMetrics.totalWordCount.toString(),
				average_readability: this.contentMetrics.averageReadability.toString(),
				duplicate_content_percentage: this.contentMetrics.duplicateContentPercentage.toString(),
				media_to_text_ratio: this.contentMetrics.mediaToTextRatio.toString(),
				heading_structure_score: this.contentMetrics.headingStructureScore.toString(),
				internal_link_density: this.contentMetrics.internalLinkDensity.toString(),
			})),
			language_detected: this.languageDetection.primary,
			mobile_friendly_score: this.technicalSEO.imageOptimization, // Using as placeholder
			accessibility_score: this.contentQuality.overallScore,
			screenshot_urls: this.screenshots,
			subdomains: new Set(this.subdomains),
			last_crawled: this.lastCrawled,
			crawl_status: this.crawlStatus,
		};
	}
}

export default DomainAnalysis;
