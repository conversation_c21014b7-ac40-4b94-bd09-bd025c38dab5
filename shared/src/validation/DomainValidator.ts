/**
 * Shared domain validation utilities
 * Replaces duplicated validation logic across services
 */

export interface DomainValidationConfigType {
	maxDomainLength: number;
	maxLabelLength: number;
	allowSubdomains?: boolean;
	allowPunycode?: boolean;
}

export interface DomainValidationResultType {
	isValid: boolean;
	domain?: string;
	errors: string[];
	metadata?: {
		labels: string[];
		tld: string;
		sld?: string;
		subdomain?: string;
	};
}

export class DomainValidator {
	private readonly config: Required<DomainValidationConfigType>;

	constructor(config: Partial<DomainValidationConfigType> = {}) {
		this.config = {
			maxDomainLength: 253,
			maxLabelLength: 63,
			allowSubdomains: true,
			allowPunycode: true,
			...config,
		};
	}

	/**
	 * Validate domain format (replaces duplicated isValidDomainFormat methods)
	 */
	isValidFormat(domain: string): boolean {
		if (!domain || typeof domain !== 'string') {
			return false;
		}

		// Check length constraints
		if (domain.length > this.config.maxDomainLength) {
			return false;
		}

		// Basic domain format validation
		const domainRegex = /^[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?(\.[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i;
		
		if (!domainRegex.test(domain)) {
			return false;
		}

		// Check each label length
		const labels = domain.split('.');
		for (const label of labels) {
			if (label.length === 0 || label.length > this.config.maxLabelLength) {
				return false;
			}
		}

		// Must have at least TLD and SLD
		if (labels.length < 2) {
			return false;
		}

		return true;
	}

	/**
	 * Comprehensive domain validation with detailed results
	 */
	validate(domain: string): DomainValidationResultType {
		const errors: string[] = [];

		if (!domain || typeof domain !== 'string') {
			errors.push('Domain must be a non-empty string');
			return { isValid: false, errors };
		}

		const normalizedDomain = domain.toLowerCase().trim();

		// Length validation
		if (normalizedDomain.length > this.config.maxDomainLength) {
			errors.push(`Domain exceeds maximum length of ${this.config.maxDomainLength} characters`);
		}

		if (normalizedDomain.length === 0) {
			errors.push('Domain cannot be empty');
			return { isValid: false, errors };
		}

		// Split into labels
		const labels = normalizedDomain.split('.');
		
		if (labels.length < 2) {
			errors.push('Domain must contain at least a TLD and SLD');
		}

		// Validate each label
		for (let i = 0; i < labels.length; i++) {
			const label = labels[i];
			
			if (label.length === 0) {
				errors.push(`Label ${i + 1} cannot be empty`);
				continue;
			}

			if (label.length > this.config.maxLabelLength) {
				errors.push(`Label "${label}" exceeds maximum length of ${this.config.maxLabelLength} characters`);
			}

			// Label format validation
			if (!/^[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?$/i.test(label)) {
				errors.push(`Label "${label}" contains invalid characters or format`);
			}

			// Labels cannot start or end with hyphen
			if (label.startsWith('-') || label.endsWith('-')) {
				errors.push(`Label "${label}" cannot start or end with hyphen`);
			}
		}

		// Punycode validation
		if (!this.config.allowPunycode && normalizedDomain.includes('xn--')) {
			errors.push('Punycode domains are not allowed');
		}

		// Subdomain validation
		if (!this.config.allowSubdomains && labels.length > 2) {
			errors.push('Subdomains are not allowed');
		}

		const isValid = errors.length === 0;
		const result: DomainValidationResultType = {
			isValid,
			errors,
		};

		if (isValid) {
			result.domain = normalizedDomain;
			result.metadata = {
				labels,
				tld: labels[labels.length - 1],
				sld: labels.length >= 2 ? labels[labels.length - 2] : undefined,
				subdomain: labels.length > 2 ? labels.slice(0, -2).join('.') : undefined,
			};
		}

		return result;
	}

	/**
	 * Extract TLD from domain
	 */
	getTld(domain: string): string | null {
		if (!this.isValidFormat(domain)) {
			return null;
		}
		
		const labels = domain.toLowerCase().split('.');
		return labels[labels.length - 1];
	}

	/**
	 * Extract SLD (Second Level Domain) from domain
	 */
	getSld(domain: string): string | null {
		if (!this.isValidFormat(domain)) {
			return null;
		}
		
		const labels = domain.toLowerCase().split('.');
		return labels.length >= 2 ? labels[labels.length - 2] : null;
	}

	/**
	 * Check if domain is a subdomain
	 */
	isSubdomain(domain: string): boolean {
		if (!this.isValidFormat(domain)) {
			return false;
		}
		
		return domain.split('.').length > 2;
	}

	/**
	 * Normalize domain (lowercase, trim)
	 */
	normalize(domain: string): string {
		if (!domain || typeof domain !== 'string') {
			return '';
		}
		
		return domain.toLowerCase().trim();
	}

	/**
	 * Batch validate multiple domains
	 */
	validateBatch(domains: string[]): Array<DomainValidationResultType & { originalDomain: string }> {
		return domains.map(domain => ({
			originalDomain: domain,
			...this.validate(domain),
		}));
	}
}

// Default instance with standard configuration
export const domainValidator = new DomainValidator();

export default DomainValidator;