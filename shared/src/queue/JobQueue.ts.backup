
import { logger } from '@shared/utils/Logger';
import { Consumer, Producer } from 'redis-smq';

export type JobHandler<T = unknown> = (data: T, callback: (error?: Error | null) => void) => void;

class JobQueue
{
	private logger: typeof logger;
	private producer: Producer;
	private consumer: Consumer;
	private isRunning: boolean = false;

	constructor(loggerInstance: typeof logger)
	{
		this.logger = loggerInstance;
		this.producer = new Producer();
		this.consumer = new Consumer();
	}

	/**
	 * Add a job to the queue
	 */
	async addJob<T>(queueName: string, jobData: T): Promise<void>
	{
		return new Promise((resolve, reject) =>
		{
			try
			{
				// Create message object for redis-smq v8
				const messageData = {
					queue: queueName,
					body: jobData,
				};

				this.producer.produce(messageData, (error?: Error | null, messageIds?: string[]) =>
				{
					if (error)
					{
						this.logger.error('Failed to add job to queue', { error, queueName, jobData });
						reject(error);
					}
					else
					{
						this.logger.info('Job added to queue', { queueName, messageIds });
						resolve();
					}
				});
			}
			catch (error)
			{
				this.logger.error('Error creating job message', { error, queueName, jobData });
				reject(error);
			}
		});
	}

	/**
	 * Process jobs from a queue
	 */
	async processJobs<T>(queueName: string, handler: JobHandler<T>): Promise<void>
	{
		return new Promise((resolve, reject) =>
		{
			try
			{
				this.logger.info('Setting up job consumer', { queueName });

				// Set up message handler
				const messageHandler = (message: unknown, callback: (error?: Error | null) => void) =>
				{
					try
					{
						const jobData = (message as { body: T }).body;
						this.logger.debug('Processing job', { queueName, jobData });

						// Call the user-provided handler
						handler(jobData, (handlerError?: Error | null) =>
						{
							if (handlerError)
							{
								this.logger.error('Job processing failed', { error: handlerError, queueName, jobData });
								callback(handlerError);
							}
							else
							{
								this.logger.debug('Job processed successfully', { queueName, jobData });
								callback();
							}
						});
					}
					catch (error)
					{
						this.logger.error('Error in message handler', { error, queueName });
						callback(error as Error);
					}
				};

				// Set up consumer
				this.consumer.consume(queueName, messageHandler, (error?: Error | null) =>
				{
					if (error)
					{
						this.logger.error('Failed to set up consumer', { error, queueName });
						reject(error);
					}
					else
					{
						this.logger.info('Consumer set up successfully', { queueName });
						resolve();
					}
				});
			}
			catch (error)
			{
				this.logger.error('Error setting up consumer', { error, queueName });
				reject(error);
			}
		});
	}

	/**
	 * Start the queue consumer
	 */
	async start(): Promise<void>
	{
		if (this.isRunning)
		{
			this.logger.warn('Queue consumer is already running');
			return;
		}

		return new Promise((resolve, reject) =>
		{
			try
			{
				this.logger.info('Starting queue consumer');

				this.consumer.run((error?: Error | null) =>
				{
					if (error)
					{
						this.logger.error('Failed to start consumer', { error });
						reject(error);
					}
					else
					{
						this.isRunning = true;
						this.logger.info('Queue consumer started successfully');
						resolve();
					}
				});
			}
			catch (error)
			{
				this.logger.error('Error starting consumer', { error });
				reject(error);
			}
		});
	}

	/**
	 * Stop the queue consumer
	 */
	async stop(): Promise<void>
	{
		if (!this.isRunning)
		{
			this.logger.warn('Queue consumer is not running');
			return;
		}

		return new Promise((resolve, reject) =>
		{
			try
			{
				this.logger.info('Stopping queue consumer');

				// Stop consumer
				this.consumer.shutdown((consumerError?: Error | null) =>
				{
					if (consumerError)
					{
						this.logger.error('Error stopping consumer', { error: consumerError });
					}

					// Stop producer
					this.producer.shutdown((producerError?: Error | null) =>
					{
						if (producerError)
						{
							this.logger.error('Error stopping producer', { error: producerError });
						}

						const finalError = consumerError || producerError;
						if (finalError)
						{
							reject(finalError);
						}
						else
						{
							this.isRunning = false;
							this.logger.info('Queue consumer stopped successfully');
							resolve();
						}
					});
				});
			}
			catch (error)
			{
				this.logger.error('Error stopping queue', { error });
				reject(error);
			}
		});
	}

	/**
	 * Get queue status
	 */
	getStatus(): { isRunning: boolean }
	{
		return { isRunning: this.isRunning };
	}
}

export { JobQueue };
