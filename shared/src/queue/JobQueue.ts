/* eslint-disable max-classes-per-file */

import { Consumer, Producer, Message } from 'redis-smq';
import { logger } from '../utils/Logger';
import Constants from '../constants/Constants';
import { DomainCrawlJob } from '../models/DomainModels';

type JobDataType =
{
	id?: string;
	data: unknown;
	createdAt?: string;
	priority?: number;
	delay?: number;
	ttl?: number;
	retryCount?: number;
	maxRetries?: number;
};

type JobOptionsType =
{
	priority?: number;
	delay?: number;
	ttl?: number;
	maxRetries?: number;
};

type QueueStatsType =
{
	queueName: string;
	pendingJobs: number;
	processingJobs: number;
	completedJobs: number;
	failedJobs: number;
	deadLetterJobs: number;
};

type JobHandlerType<T = unknown> = (
	jobData: T,
	message: Message,
) => Promise<void>;

/**
 * Job queue manager using Redis-SMQ for inter-service communication
 */
class JobQueue
{
	private logger: any;
	private producer: Producer | null = null;
	private consumers: Map<string, Consumer> = new Map();
	private isRunning: boolean = false;
	private isInitialized: boolean = false;

	constructor(loggerInstance: typeof logger)
	{
		this.logger = loggerInstance.getLogger('JobQueue');
		this.producer = new Producer();
	}

	/**
	 * Initialize the job queue
	 */
	async initialize(): Promise<void>
	{
		if (this.isInitialized)
		{
			return;
		}

		try
		{
			if (this.producer)
			{
				await new Promise<void>((resolve, reject) =>
				{
					this.producer!.run((error?: Error | null) =>
					{
						if (error)
						{
							this.logger.error({ error }, 'Failed to initialize producer');
							reject(error);
						}
						else
						{
							this.logger.info('Producer initialized successfully');
							resolve();
						}
					});
				});
			}

			this.isInitialized = true;
			this.logger.info('Job queue initialized successfully');
		}
		catch (error)
		{
			this.logger.error({ error }, 'Failed to initialize job queue');
			throw error;
		}
	}

	/**
	 * Add a job to the queue
	 */
	async addJob<T>(queueName: string, jobData: T): Promise<void>
	{
		return new Promise((resolve, reject) =>
		{
			try
			{
				// Create message object for redis-smq v7
				const message = new Message();
				message.setQueue(queueName);
				message.setBody(jobData);

				if (!this.producer)
				{
					throw new Error('Producer not initialized');
				}
				this.producer.produce(message, (error?: Error | null, messageIds?: string[]) =>
				{
					if (error)
					{
						this.logger.error({ error, queueName, jobData }, 'Failed to add job');
						reject(error);
					}
					else
					{
						this.logger.info({ queueName, messageIds }, 'Job added to queue');
						resolve();
					}
				});
			}
			catch (error)
			{
				this.logger.error({ error }, 'Failed to add job to queue');
				reject(error);
			}
		});
	}

	/**
   * Shutdown job queue
   */
	async shutdown(): Promise<void>
	{
		try
		{
			if (this.producer)
			{
				await new Promise<void>((resolve, reject) =>
				{
					this.producer!.shutdown((error?: Error | null) =>
					{
						if (error)
						{
							this.logger.error({ error }, 'Error shutting down producer');
							reject(error);
						}
						else
						{
							this.logger.info('Producer shut down successfully');
							resolve(undefined);
						}
					});
				});
				this.producer = null;
			}

			const shutdownPromises = Array.from(this.consumers.entries())
				.map(([queueName, consumer]) =>
					new Promise<void>((resolve, reject) =>
					{
						consumer.shutdown((error?: Error | null) =>
						{
							if (error)
							{
								this.logger.error(`Error shutting down consumer for queue ${queueName}:`, error);
								reject(error);
							}
							else
							{
								this.logger.info(`Consumer for queue ${queueName} shut down`);
								resolve(undefined);
							}
						});
					}));
			await Promise.all(shutdownPromises);

			this.consumers.clear();
			this.isInitialized = false;
			this.logger.info('Job queue shut down successfully');
		}
		catch (error)
		{
			this.logger.error('Error shutting down job queue:', error);
			throw error;
		}
	}

	/**
   * Publish job to queue
   */
	async publishJob(
		queueName: string,
		jobData: unknown,
		options: JobOptionsType = {},
	): Promise<string>
	{
		if (!this.isInitialized || !this.producer)
		{
			throw new Error('Job queue not initialized');
		}

		try
		{
			const job: JobDataType =
			{
				id: this.generateJobId(),
				data: jobData,
				createdAt: new Date().toISOString(),
				priority: options.priority || 0,
				delay: options.delay || 0,
				ttl: options.ttl || 3600000, // 1 hour default TTL
				retryCount: 0,
				maxRetries: options.maxRetries || 3,
			};

			const message = new Message();
			message.setBody(job);
			message.setQueue(queueName);
			if (options.ttl)
			{
				message.setTTL(options.ttl);
			}
			if (options.delay)
			{
				message.setScheduledDelay(options.delay);
			}
			if (options.priority)
			{
				message.setPriority(options.priority);
			}
			await new Promise<void>((resolve, reject) =>
			{
				// Non-null assertion is safe here due to the guard above
				this.producer!.produce(message, (error?: Error | null) =>
				{
					if (error)
					{
						reject(error);
					}
					else
					{
						resolve(undefined);
					}
				});
			});

			this.logger.info(`Job published to queue ${queueName}:`, job.id);
			return job.id!;
		}
		catch (error)
		{
			this.logger.error(`Failed to publish job to queue ${queueName}:`, error);
			throw error;
		}
	}

	/**
   * Create consumer for a specific queue
   */
	async createConsumer(
		queueName: string,
		handler: JobHandlerType,
		// options: { concurrency?: number; retryThreshold?: number } = {},
	): Promise<Consumer>
	{
		try
		{
			const consumer = new Consumer();

			consumer.consume(queueName, (message: Message, cb: (error?: Error) => void) =>
			{
				const jobData = message.getBody() as JobDataType;

				try
				{
					this.logger.info(`Processing job from queue ${queueName}:`, jobData.id);

					// Call the handler
					handler(jobData.data, message)
						.then(() =>
						{
							// Acknowledge successful processing
							cb();
							this.logger.info(`Job completed successfully: ${jobData.id}`);
						})
						.catch((error) =>
						{
							this.logger.error(`Job processing failed for queue ${queueName}:`, error);

							// Check if we should retry
							const currentRetries = jobData.retryCount || 0;
							const maxRetries = jobData.maxRetries || 3;

							if (currentRetries < maxRetries)
							{
								// Increment retry count and requeue
								jobData.retryCount = currentRetries + 1;

								// Calculate exponential backoff delay
								const retryDelay = Math.min(1000 * 2 ** currentRetries, 300000); // Max 5 minutes

								this.logger.info(`Retrying job ${jobData.id} (attempt ${jobData.retryCount}/${maxRetries}) in ${retryDelay}ms`);

								// Requeue with delay
								setTimeout(async () =>
								{
									try
									{
										await this.publishJob(queueName, jobData.data, {
											priority: jobData.priority,
											ttl: jobData.ttl,
											maxRetries: jobData.maxRetries,
										});
									}
									catch (requeueError)
									{
										this.logger.error(`Failed to requeue job ${jobData.id}:`, requeueError);
									}
								}, retryDelay);

								cb(); // Acknowledge to remove from queue
							}
							else
							{
								// Max retries reached, send to dead letter queue
								this.logger.error(`Job ${jobData.id} failed after ${maxRetries} attempts, sending to dead letter queue`);
								cb(error as Error);
							}
						});
				}
				catch (syncError)
				{
					this.logger.error(`Synchronous error in job handler for ${jobData.id}:`, syncError);
					cb(syncError as Error);
				}
			}, (error?: Error | null) =>
			{
				if (error)
				{
					this.logger.error(`Consumer setup error for queue ${queueName}:`, error);
				}
			});

			consumer.run((error?: Error | null) =>
			{
				if (error)
				{
					this.logger.error(`Failed to run consumer for queue ${queueName}:`, error);
				}
				else
				{
					this.logger.info(`Consumer running for queue ${queueName}`);
				}
			});
			this.consumers.set(queueName, consumer);

			this.logger.info(`Consumer created for queue ${queueName}`);
			return consumer;
		}
		catch (error)
		{
			this.logger.error(`Failed to create consumer for queue ${queueName}:`, error);
			throw error;
		}
	}

	/**
   * Publish domain crawl job
   */
	async publishDomainCrawlJob(
		domain: string,
		crawlType: 'full' | 'quick' | 'security' | 'performance' = 'full',
		priority: 'low' | 'medium' | 'high' = 'medium',
	): Promise<string>
	{
		const crawlJob = new DomainCrawlJob({
			domain,
			crawlType,
			priority,
			scheduledAt: new Date().toISOString(),
		});

		return this.publishJob(Constants.JOB_QUEUES.DOMAIN_CRAWL, crawlJob, {
			priority: this.getPriorityValue(priority),
		});
	}

	/**
   * Publish ranking update job
   */
	async publishRankingUpdateJob(domain: string, rankingType: string = 'global'): Promise<string>
	{
		const jobData = {
			domain,
			rankingType,
			scheduledAt: new Date().toISOString(),
		};

		return this.publishJob(Constants.JOB_QUEUES.RANKING_UPDATE, jobData, {
			priority: this.getPriorityValue('medium'),
		});
	}

	/**
   * Publish traffic analysis job
   */
	async publishTrafficAnalysisJob(domain: string): Promise<string>
	{
		const jobData = {
			domain,
			scheduledAt: new Date().toISOString(),
		};

		return this.publishJob(Constants.JOB_QUEUES.TRAFFIC_ANALYSIS, jobData, {
			priority: this.getPriorityValue('low'),
		});
	}

	/**
   * Publish backlink analysis job
   */
	async publishBacklinkAnalysisJob(domain: string): Promise<string>
	{
		const jobData = {
			domain,
			scheduledAt: new Date().toISOString(),
		};

		return this.publishJob(Constants.JOB_QUEUES.BACKLINK_ANALYSIS, jobData, {
			priority: this.getPriorityValue('low'),
		});
	}

	/**
   * Publish Manticore sync job
   */
	async publishManticoreSyncJob(domain: string, syncType: 'full' | 'incremental' = 'incremental'): Promise<string>
	{
		const jobData = {
			domain,
			syncType,
			scheduledAt: new Date().toISOString(),
		};

		return this.publishJob(Constants.JOB_QUEUES.MANTICORE_SYNC, jobData, {
			priority: this.getPriorityValue('medium'),
		});
	}

	/**
   * Publish maintenance job
   */
	async publishMaintenanceJob(
		taskType: string,
		taskData: Record<string, unknown> = {},
	): Promise<string>
	{
		const jobData = {
			taskType,
			taskData,
			scheduledAt: new Date().toISOString(),
		};

		return this.publishJob(Constants.JOB_QUEUES.MAINTENANCE, jobData, {
			priority: this.getPriorityValue('low'),
		});
	}

	/**
   * Publish batch crawl jobs
   */
	async publishBatchCrawlJobs(
		domains: string[],
		crawlType: 'full' | 'quick' | 'security' | 'performance' = 'full',
		batchSize: number = 10,
	): Promise<string[]>
	{
		const batchPromises: Promise<string[]>[] = [];

		for (let i = 0; i < domains.length; i += batchSize)
		{
			const batch = domains.slice(i, i + batchSize);
			const batchIndex = Math.floor(i / batchSize);
			const delayMs = batchIndex * 100; // maintain spacing between batch starts

			const batchPromise = this.delay(delayMs).then(() => Promise.all(
				batch.map(domain => this.publishDomainCrawlJob(domain, crawlType, 'medium')),
			));

			batchPromises.push(batchPromise);
		}

		const idGroups = await Promise.all(batchPromises);
		const allJobIds = idGroups.flat();

		this.logger.info(`Published ${allJobIds.length} crawl jobs for ${domains.length} domains`);
		return allJobIds;
	}

	/**
   * Get queue statistics (basic implementation)
   */
	async getQueueStats(queueName: string): Promise<QueueStatsType>
	{
		try
		{
			// This is a basic implementation - Redis-SMQ may provide better APIs
			return {
				queueName,
				pendingJobs: 0,
				processingJobs: 0,
				completedJobs: 0,
				failedJobs: 0,
				deadLetterJobs: 0,
			};
		}
		catch (error)
		{
			this.logger.error(`Failed to get stats for queue ${queueName}:`, error);
			throw error;
		}
	}

	/**
   * Get all queue statistics
   */
	async getAllQueueStats(): Promise<QueueStatsType[]>
	{
		const queueNames = Object.values(Constants.JOB_QUEUES);
		const results = await Promise.all(queueNames.map(async (queueName) =>
		{
			try
			{
				return await this.getQueueStats(queueName);
			}
			catch (error)
			{
				this.logger.error(`Failed to get stats for queue ${queueName}:`, error);
				return null;
			}
		}));

		return results.filter((s): s is QueueStatsType => s !== null);
	}

	/**
   * Purge queue (remove all jobs)
   */
	async purgeQueue(queueName: string): Promise<void>
	{
		try
		{
			// Implementation depends on Redis-SMQ API
			this.logger.info(`Purging queue: ${queueName}`);
			// await this.producer.purgeQueue(queueName);
		}
		catch (error)
		{
			this.logger.error(`Failed to purge queue ${queueName}:`, error);
			throw error;
		}
	}

	/**
   * Schedule recurring job
   */
	async scheduleRecurringJob(
		queueName: string,
		jobData: unknown,
		cronExpression: string,
		options: JobOptionsType = {},
	): Promise<string>
	{
		// This would need to be implemented with a cron scheduler
		// For now, just publish a single job
		this.logger.info(`Scheduling recurring job for queue ${queueName} with cron: ${cronExpression}`);
		return this.publishJob(queueName, jobData, options);
	}

	/**
   * Generate unique job ID
   */
	private generateJobId(): string
	{
		return `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
	}

	/**
   * Convert priority string to numeric value
   */
	private getPriorityValue(priority: 'low' | 'medium' | 'high'): number
	{
		const priorities = {
			low: 1,
			medium: 5,
			high: 10,
		};
		return priorities[priority] || 5;
	}

	/**
   * Delay utility
   */
	private delay(ms: number): Promise<void>
	{
		// eslint-disable-next-line no-promise-executor-return
		return new Promise(resolve => setTimeout(resolve, ms));
	}

	/**
   * Health check for job queue
   */
	async healthCheck(): Promise<boolean>
	{
		try
		{
			if (!this.isInitialized || !this.producer)
			{
				return false;
			}

			// Try to publish a test job to a test queue
			const testJobId = await this.publishJob('test_queue', { test: true }, { ttl: 1000 });
			return !!testJobId;
		}
		catch (error)
		{
			this.logger.error('Job queue health check failed:', error);
			return false;
		}
	}
}

/**
 * Job queue factory for creating service-specific job queues
 */
class JobQueueFactory
{
	private static instances = new Map<string, JobQueue>();

	/**
	 * Get or create job queue instance for service
	 */
	static async getJobQueue(serviceName: string): Promise<JobQueue>
	{
		if (!this.instances.has(serviceName))
		{
			const jobQueue = new JobQueue(logger);
			await jobQueue.initialize();
			this.instances.set(serviceName, jobQueue);
		}

		return this.instances.get(serviceName)!;
	}

	/**
   * Shutdown all job queue instances
   */
	static async shutdownAll(): Promise<void>
	{
		const shutdownPromises = Array
			.from(this.instances.values())
			.map(queue => queue.shutdown());

		await Promise.all(shutdownPromises);
		this.instances.clear();
	}
}

export { JobQueueFactory };

export default JobQueue;
