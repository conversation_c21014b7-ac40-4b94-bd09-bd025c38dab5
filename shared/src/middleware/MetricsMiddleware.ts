import MetricsCollector, { CommonMetrics } from '../monitoring/MetricsCollector';
import type { LoggerInstanceType } from '../utils/Logger';
import type { RequestLike, ResponseLike, NextFunction, MetricsMiddlewareOptions } from './types';

export class MetricsMiddleware 
{
	private readonly logger: LoggerInstanceType;
	private readonly metricsCollector: MetricsCollector;
	private readonly commonMetrics: CommonMetrics;
	private readonly options: Required<MetricsMiddlewareOptions>;

	constructor(logger: LoggerInstanceType, options: MetricsMiddlewareOptions = {}) 
	{
		this.logger = logger;
		this.metricsCollector = new MetricsCollector();
		this.commonMetrics = new CommonMetrics(this.metricsCollector);
		
		this.options = {
			enabled: true,
			skipPaths: ['/health', '/metrics'],
			skipMethods: ['OPTIONS'],
			slowRequestThreshold: 5000,
			collectResponseSize: true,
			normalizeRoutePaths: true,
			...options,
		};
	}

	/**
	 * HTTP request metrics middleware
	 */
	httpMetrics() 
	{
		return (req: RequestLike, res: ResponseLike, next: NextFunction): void => 
		{
			if (!this.options.enabled || this.shouldSkipRequest(req)) 
			{
				return next();
			}

			// Record request start time
			req.startTime = Date.now();

			// Increment active requests counter
			this.metricsCollector.gauge('http_active_requests', 1);

			// Override response end to capture metrics
			const originalEnd = res.end;
			if (originalEnd && typeof originalEnd === 'function') 
			{
				res.end = (...args: any[]) => 
				{
					try 
					{
						this.recordHttpMetrics(req, res);
					}
					catch (error) 
					{
						this.logger.error('Error recording HTTP metrics:', error);
					}

					// Decrement active requests counter
					this.metricsCollector.gauge('http_active_requests', -1);

					// Call original end method
					return originalEnd.apply(res, args);
				};
			}

			next();
		};
	}

	/**
	 * Database operation metrics wrapper
	 */
	databaseMetrics<T>(
		database: string,
		operation: string,
		fn: () => Promise<T>,
	): Promise<T> 
	{
		return this.metricsCollector.time(
			'database_operation_duration',
			async () => 
			{
				try 
				{
					const result = await fn();
					this.commonMetrics.databaseQuery(database, operation, 0, true);
					return result;
				}
				catch (error) 
				{
					this.commonMetrics.databaseQuery(database, operation, 0, false);
					throw error;
				}
			},
			{ database, operation },
		);
	}

	/**
	 * Cache operation metrics wrapper
	 */
	cacheMetrics<T>(
		operation: string,
		fn: () => Promise<T | null>,
	): Promise<T | null> 
	{
		return this.metricsCollector.time(
			'cache_operation_duration',
			async () => 
			{
				const result = await fn();
				const hit = result !== null && result !== undefined;
				this.commonMetrics.cacheOperation(operation, hit);
				return result;
			},
			{ operation },
		);
	}

	/**
	 * Business metrics helpers
	 */
	domainSearch(query: string, resultsCount: number, duration: number): void 
	{
		this.metricsCollector.counter('domain_searches_total', 1, {
			has_results: (resultsCount > 0).toString(),
		});
		this.metricsCollector.timer('domain_search_duration', duration);
		this.metricsCollector.histogram('domain_search_results_count', resultsCount);
	}

	domainAnalysis(domain: string, success: boolean, duration: number): void 
	{
		this.metricsCollector.counter('domain_analysis_total', 1, {
			success: success.toString(),
		});
		this.metricsCollector.timer('domain_analysis_duration', duration);
	}

	crawlJob(jobType: string, success: boolean, duration: number): void 
	{
		this.metricsCollector.counter('crawl_jobs_total', 1, {
			job_type: jobType,
			success: success.toString(),
		});
		this.metricsCollector.timer('crawl_job_duration', duration, {
			job_type: jobType,
		});
	}

	private recordHttpMetrics(req: RequestLike, res: ResponseLike): void 
	{
		const duration = Date.now() - (req.startTime || Date.now());
		const method = req.method;
		const path = this.getRoutePath(req);
		const statusCode = res.statusCode;

		// Record HTTP metrics
		this.commonMetrics.httpRequest(method, path, statusCode, duration);

		// Record response size if available
		if (this.options.collectResponseSize && res.get) 
		{
			const contentLength = res.get('content-length');
			if (contentLength) 
			{
				this.metricsCollector.histogram('http_response_size_bytes', parseInt(contentLength, 10), {
					method,
					path,
					status_code: statusCode.toString(),
				});
			}
		}

		// Log slow requests
		if (duration > this.options.slowRequestThreshold) 
		{
			this.logger.warn(`Slow request detected: ${method} ${path} took ${duration}ms`);
		}
	}

	private getRoutePath(req: RequestLike): string 
	{
		if (!this.options.normalizeRoutePaths) 
		{
			return req.path || req.url;
		}

		// Use route path if available
		if (req.route?.path) 
		{
			return req.route.path;
		}

		// Normalize common patterns
		let path = req.path || req.url;

		// Replace domain names with placeholder
		path = path.replace(/\/api\/domains\/[a-zA-Z0-9.-]+/, '/api/domains/:domain');

		// Replace UUIDs with placeholder
		path = path.replace(/\/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/gi, '/:id');

		// Replace numeric IDs with placeholder
		path = path.replace(/\/\d+/g, '/:id');

		return path;
	}

	private shouldSkipRequest(req: RequestLike): boolean 
	{
		const path = req.path || req.url;
		const method = req.method;

		return (
			this.options.skipMethods.includes(method) ||
			this.options.skipPaths.some(skipPath => path.startsWith(skipPath))
		);
	}

	/**
	 * Get current metrics collector instance
	 */
	getMetricsCollector(): MetricsCollector 
	{
		return this.metricsCollector;
	}
}

export default MetricsMiddleware;
