// Common middleware types that can be used across different frameworks

export interface RequestLike {
	method: string;
	url: string;
	path?: string;
	route?: { path?: string };
	headers: Record<string, string | string[] | undefined>;
	ip?: string;
	get?: (header: string) => string | undefined;
	startTime?: number;
}

export interface ResponseLike {
	statusCode: number;
	get?: (header: string) => string | undefined;
	set?: (header: string, value: string) => void;
	end?: Function;
}

export type NextFunction = (error?: any) => void;

export interface MiddlewareOptions {
	enabled?: boolean;
	skipPaths?: string[];
	skipMethods?: string[];
}

export interface MetricsMiddlewareOptions extends MiddlewareOptions {
	slowRequestThreshold?: number;
	collectResponseSize?: boolean;
	normalizeRoutePaths?: boolean;
}

export interface SecurityMiddlewareOptions extends MiddlewareOptions {
	cors?: {
		origin?: string | string[] | boolean;
		methods?: string[];
		allowedHeaders?: string[];
		credentials?: boolean;
	};
	helmet?: {
		contentSecurityPolicy?: boolean;
		hsts?: boolean;
		noSniff?: boolean;
		xssFilter?: boolean;
	};
}

export interface RateLimitMiddlewareOptions extends MiddlewareOptions {
	windowMs?: number;
	maxRequests?: number;
	message?: string;
	keyGenerator?: (req: RequestLike) => string;
	skipSuccessfulRequests?: boolean;
	skipFailedRequests?: boolean;
}

export interface ErrorMiddlewareOptions extends MiddlewareOptions {
	includeStack?: boolean;
	logErrors?: boolean;
	customErrorHandler?: (error: Error, req: RequestLike, res: ResponseLike) => any;
}
