import type { LoggerInstanceType } from '../utils/Logger';
import type { RequestLike, ResponseLike, NextFunction, RateLimitMiddlewareOptions } from './types';

interface RateLimitEntry {
	count: number;
	resetTime: number;
}

export class RateLimitMiddleware 
{
	private readonly logger: LoggerInstanceType;
	private readonly options: Required<RateLimitMiddlewareOptions>;
	private readonly store: Map<string, RateLimitEntry> = new Map();
	private cleanupInterval?: NodeJS.Timeout;

	constructor(logger: LoggerInstanceType, options: RateLimitMiddlewareOptions = {}) 
	{
		this.logger = logger;
		
		this.options = {
			enabled: true,
			skipPaths: ['/health', '/metrics'],
			skipMethods: ['OPTIONS'],
			windowMs: 15 * 60 * 1000, // 15 minutes
			maxRequests: 100,
			message: 'Too many requests, please try again later',
			keyGenerator: this.defaultKeyGenerator.bind(this),
			skipSuccessfulRequests: false,
			skipFailedRequests: false,
			...options,
		};

		// Start cleanup interval to remove expired entries
		this.startCleanupInterval();
	}

	/**
	 * Rate limiting middleware
	 */
	rateLimit() 
	{
		return (req: RequestLike, res: ResponseLike, next: NextFunction): void => 
		{
			if (!this.options.enabled || this.shouldSkipRequest(req)) 
			{
				return next();
			}

			try 
			{
				const key = this.options.keyGenerator(req);
				const now = Date.now();
				const windowStart = now - this.options.windowMs;
				
				// Get or create rate limit entry
				let entry = this.store.get(key);
				if (!entry || entry.resetTime <= now) 
				{
					entry = {
						count: 0,
						resetTime: now + this.options.windowMs,
					};
					this.store.set(key, entry);
				}

				// Check if request should be rate limited
				if (entry.count >= this.options.maxRequests) 
				{
					this.handleRateLimit(req, res, entry);
					return;
				}

				// Increment counter
				entry.count++;

				// Set rate limit headers
				this.setRateLimitHeaders(res, entry);

				// Override response end to handle successful/failed request skipping
				if (!this.options.skipSuccessfulRequests || !this.options.skipFailedRequests) 
				{
					const originalEnd = res.end;
					if (originalEnd && typeof originalEnd === 'function') 
					{
						res.end = (...args: any[]) => 
						{
							const statusCode = res.statusCode;
							const isSuccessful = statusCode >= 200 && statusCode < 400;
							const shouldSkip = (
								(this.options.skipSuccessfulRequests && isSuccessful) ||
								(this.options.skipFailedRequests && !isSuccessful)
							);

							if (shouldSkip && entry) 
							{
								entry.count = Math.max(0, entry.count - 1);
							}

							return originalEnd.apply(res, args);
						};
					}
				}

				next();
			}
			catch (error) 
			{
				this.logger.error({ error }, 'Rate limit middleware error');
				next(error);
			}
		};
	}

	/**
	 * Burst protection middleware (stricter short-term limits)
	 */
	burstProtection(burstWindowMs: number = 60000, maxBurstRequests: number = 10) 
	{
		const burstStore = new Map<string, RateLimitEntry>();

		return (req: RequestLike, res: ResponseLike, next: NextFunction): void => 
		{
			if (!this.options.enabled || this.shouldSkipRequest(req)) 
			{
				return next();
			}

			try 
			{
				const key = this.options.keyGenerator(req);
				const now = Date.now();
				
				// Check burst limit
				let burstEntry = burstStore.get(key);
				if (!burstEntry || burstEntry.resetTime <= now) 
				{
					burstEntry = {
						count: 0,
						resetTime: now + burstWindowMs,
					};
					burstStore.set(key, burstEntry);
				}

				if (burstEntry.count >= maxBurstRequests) 
				{
					this.handleBurstLimit(req, res, burstEntry, burstWindowMs);
					return;
				}

				burstEntry.count++;
				next();
			}
			catch (error) 
			{
				this.logger.error({ error }, 'Burst protection middleware error');
				next(error);
			}
		};
	}

	/**
	 * Get current rate limit status for a request
	 */
	getStatus(req: RequestLike): { remaining: number; resetTime: number } 
	{
		const key = this.options.keyGenerator(req);
		const entry = this.store.get(key);
		
		if (!entry) 
		{
			return {
				remaining: this.options.maxRequests,
				resetTime: Date.now() + this.options.windowMs,
			};
		}

		return {
			remaining: Math.max(0, this.options.maxRequests - entry.count),
			resetTime: entry.resetTime,
		};
	}

	/**
	 * Clear rate limit data for a specific key or all keys
	 */
	reset(key?: string): void 
	{
		if (key) 
		{
			this.store.delete(key);
		}
		else 
		{
			this.store.clear();
		}
	}

	/**
	 * Cleanup expired entries and stop cleanup interval
	 */
	destroy(): void 
	{
		if (this.cleanupInterval) 
		{
			clearInterval(this.cleanupInterval);
			this.cleanupInterval = undefined;
		}
		this.store.clear();
	}

	private handleRateLimit(req: RequestLike, res: ResponseLike, entry: RateLimitEntry): void 
	{
		this.logger.warn(`Rate limit exceeded for ${this.options.keyGenerator(req)}`);
		
		res.statusCode = 429;
		this.setRateLimitHeaders(res, entry);
		
		if (res.set) 
		{
			res.set('Content-Type', 'application/json');
		}

		if (res.end) 
		{
			res.end(JSON.stringify({
				error: {
					message: this.options.message,
					type: 'RateLimitError',
					retryAfter: Math.ceil((entry.resetTime - Date.now()) / 1000),
				},
			}));
		}
	}

	private handleBurstLimit(req: RequestLike, res: ResponseLike, entry: RateLimitEntry, windowMs: number): void 
	{
		this.logger.warn(`Burst limit exceeded for ${this.options.keyGenerator(req)}`);
		
		res.statusCode = 429;
		
		if (res.set) 
		{
			res.set('Content-Type', 'application/json');
			res.set('Retry-After', Math.ceil((entry.resetTime - Date.now()) / 1000).toString());
		}

		if (res.end) 
		{
			res.end(JSON.stringify({
				error: {
					message: 'Too many requests in short time window',
					type: 'BurstLimitError',
					retryAfter: Math.ceil((entry.resetTime - Date.now()) / 1000),
				},
			}));
		}
	}

	private setRateLimitHeaders(res: ResponseLike, entry: RateLimitEntry): void 
	{
		if (!res.set) return;

		res.set('X-RateLimit-Limit', this.options.maxRequests.toString());
		res.set('X-RateLimit-Remaining', Math.max(0, this.options.maxRequests - entry.count).toString());
		res.set('X-RateLimit-Reset', Math.ceil(entry.resetTime / 1000).toString());
	}

	private shouldSkipRequest(req: RequestLike): boolean 
	{
		const path = req.path || req.url;
		const method = req.method;

		return (
			this.options.skipMethods.includes(method) ||
			this.options.skipPaths.some(skipPath => path.startsWith(skipPath))
		);
	}

	private defaultKeyGenerator(req: RequestLike): string 
	{
		return req.ip || req.headers['x-forwarded-for'] as string || 'unknown';
	}

	private startCleanupInterval(): void 
	{
		this.cleanupInterval = setInterval(() => 
		{
			const now = Date.now();
			for (const [key, entry] of Array.from(this.store.entries())) 
			{
				if (entry.resetTime <= now) 
				{
					this.store.delete(key);
				}
			}
		}, this.options.windowMs);
	}
}

export default RateLimitMiddleware;
