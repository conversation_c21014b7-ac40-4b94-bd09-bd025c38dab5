import type { LoggerInstanceType } from '../utils/Logger';
import type { RequestLike, ResponseLike, NextFunction, SecurityMiddlewareOptions } from './types';

export class SecurityMiddleware 
{
	private readonly logger: LoggerInstanceType;
	private readonly options: Required<SecurityMiddlewareOptions>;

	constructor(logger: LoggerInstanceType, options: SecurityMiddlewareOptions = {}) 
	{
		this.logger = logger;
		
		this.options = {
			enabled: true,
			skipPaths: [],
			skipMethods: [],
			cors: {
				origin: true,
				methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
				allowedHeaders: ['Content-Type', 'Authorization'],
				credentials: false,
				...options.cors,
			},
			helmet: {
				contentSecurityPolicy: true,
				hsts: true,
				noSniff: true,
				xssFilter: true,
				...options.helmet,
			},
			...options,
		};
	}

	/**
	 * CORS middleware
	 */
	cors() 
	{
		return (req: RequestLike, res: ResponseLike, next: NextFunction): void => 
		{
			if (!this.options.enabled || this.shouldSkipRequest(req)) 
			{
				return next();
			}

			try 
			{
				// Handle preflight requests
				if (req.method === 'OPTIONS') 
				{
					this.handlePreflight(req, res);
					return;
				}

				// Set CORS headers
				this.setCorsHeaders(req, res);
				next();
			}
			catch (error) 
			{
				this.logger.error('CORS middleware error:', error);
				next(error);
			}
		};
	}

	/**
	 * Security headers middleware (Helmet-like functionality)
	 */
	securityHeaders() 
	{
		return (req: RequestLike, res: ResponseLike, next: NextFunction): void => 
		{
			if (!this.options.enabled || this.shouldSkipRequest(req)) 
			{
				return next();
			}

			try 
			{
				this.setSecurityHeaders(res);
				next();
			}
			catch (error) 
			{
				this.logger.error('Security headers middleware error:', error);
				next(error);
			}
		};
	}

	/**
	 * Combined security middleware (CORS + headers)
	 */
	secure() 
	{
		return (req: RequestLike, res: ResponseLike, next: NextFunction): void => 
		{
			if (!this.options.enabled || this.shouldSkipRequest(req)) 
			{
				return next();
			}

			try 
			{
				// Handle CORS
				if (req.method === 'OPTIONS') 
				{
					this.handlePreflight(req, res);
					return;
				}

				this.setCorsHeaders(req, res);
				this.setSecurityHeaders(res);
				next();
			}
			catch (error) 
			{
				this.logger.error('Security middleware error:', error);
				next(error);
			}
		};
	}

	private handlePreflight(req: RequestLike, res: ResponseLike): void 
	{
		const origin = req.headers.origin;
		
		if (this.isOriginAllowed(origin)) 
		{
			if (res.set) 
			{
				res.set('Access-Control-Allow-Origin', (Array.isArray(origin) ? origin[0] : origin) || '*');
				res.set('Access-Control-Allow-Methods', this.options.cors.methods!.join(', '));
				res.set('Access-Control-Allow-Headers', this.options.cors.allowedHeaders!.join(', '));
				
				if (this.options.cors.credentials) 
				{
					res.set('Access-Control-Allow-Credentials', 'true');
				}
			}
		}

		res.statusCode = 200;
		if (res.end) 
		{
			res.end();
		}
	}

	private setCorsHeaders(req: RequestLike, res: ResponseLike): void 
	{
		const origin = req.headers.origin;
		
		if (this.isOriginAllowed(origin) && res.set) 
		{
			res.set('Access-Control-Allow-Origin', (Array.isArray(origin) ? origin[0] : origin) || '*');
			
			if (this.options.cors.credentials) 
			{
				res.set('Access-Control-Allow-Credentials', 'true');
			}
		}
	}

	private setSecurityHeaders(res: ResponseLike): void 
	{
		if (!res.set) return;

		if (this.options.helmet.contentSecurityPolicy) 
		{
			res.set('Content-Security-Policy', "default-src 'self'");
		}

		if (this.options.helmet.hsts) 
		{
			res.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
		}

		if (this.options.helmet.noSniff) 
		{
			res.set('X-Content-Type-Options', 'nosniff');
		}

		if (this.options.helmet.xssFilter) 
		{
			res.set('X-XSS-Protection', '1; mode=block');
		}

		res.set('X-Frame-Options', 'DENY');
		res.set('X-Permitted-Cross-Domain-Policies', 'none');
		res.set('Referrer-Policy', 'no-referrer');
	}

	private isOriginAllowed(origin?: string | string[]): boolean 
	{
		const allowedOrigin = this.options.cors.origin;
		
		if (allowedOrigin === true) 
		{
			return true;
		}
		
		if (allowedOrigin === false) 
		{
			return false;
		}
		
		if (typeof allowedOrigin === 'string') 
		{
			return origin === allowedOrigin;
		}
		
		if (Array.isArray(allowedOrigin)) 
		{
			return allowedOrigin.includes(origin as string);
		}
		
		return false;
	}

	private shouldSkipRequest(req: RequestLike): boolean 
	{
		const path = req.path || req.url;
		const method = req.method;

		return (
			this.options.skipMethods.includes(method) ||
			this.options.skipPaths.some(skipPath => path.startsWith(skipPath))
		);
	}

	private getDefaultCors() 
	{
		return {
			origin: true,
			methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
			allowedHeaders: ['Content-Type', 'Authorization'],
			credentials: false,
		};
	}

	private getDefaultHelmet() 
	{
		return {
			contentSecurityPolicy: true,
			hsts: true,
			noSniff: true,
			xssFilter: true,
		};
	}
}

export default SecurityMiddleware;
