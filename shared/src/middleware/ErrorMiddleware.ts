import { BaseErrorHandler } from '../errors/ErrorHandler';
import type { LoggerInstanceType } from '../utils/Logger';
import type { RequestLike, ResponseLike, NextFunction, ErrorMiddlewareOptions } from './types';

export class ErrorMiddleware
{
	private readonly logger: LoggerInstanceType;
	private readonly errorHandler?: BaseErrorHandler;
	private readonly options: Required<Omit<ErrorMiddlewareOptions, 'customErrorHandler'>> & { customErrorHandler?: ErrorMiddlewareOptions['customErrorHandler'] };

	constructor(
		logger: LoggerInstanceType,
		errorHandler?: BaseErrorHandler,
		options: ErrorMiddlewareOptions = {}
	)
	{
		this.logger = logger;
		this.errorHandler = errorHandler;

		this.options = {
			enabled: true,
			skipPaths: [],
			skipMethods: [],
			includeStack: process.env.NODE_ENV === 'development',
			logErrors: true,
			...options,
		};
	}

	/**
	 * Generic error handling middleware
	 */
	handleErrors()
	{
		return (error: Error, req: RequestLike, res: ResponseLike, next: NextFunction): void =>
		{
			if (!this.options.enabled)
			{
				return next(error);
			}

			// Use custom error handler if provided
			if (this.options.customErrorHandler)
			{
				try
				{
					const result = this.options.customErrorHandler(error, req, res);
					if (result) return;
				}
				catch (customError)
				{
					this.logger.error({ error: customError }, 'Custom error handler failed');
				}
			}

			// Handle error with shared error handler if available
			if (this.errorHandler)
			{
				this.handleErrorWithSharedHandler(error, req, res);
			}
			else
			{
				this.handleErrorFallback(error, req, res);
			}
		};
	}

	/**
	 * Async error wrapper for route handlers
	 */
	asyncHandler(fn: (req: RequestLike, res: ResponseLike, next: NextFunction) => Promise<any>)
	{
		return (req: RequestLike, res: ResponseLike, next: NextFunction): void =>
		{
			Promise.resolve(fn(req, res, next)).catch(next);
		};
	}

	/**
	 * Request timeout middleware
	 */
	timeout(timeoutMs: number = 30000)
	{
		return (req: RequestLike, res: ResponseLike, next: NextFunction): void =>
		{
			const timeout = setTimeout(() =>
			{
				const error = new Error(`Request timeout after ${timeoutMs}ms`);
				error.name = 'RequestTimeout';
				next(error);
			}, timeoutMs);

			// Clear timeout when response finishes
			if (res.end)
			{
				const originalEnd = res.end;
				res.end = (...args: any[]) =>
				{
					clearTimeout(timeout);
					return originalEnd.apply(res, args);
				};
			}

			next();
		};
	}

	private async handleErrorWithSharedHandler(error: Error, req: RequestLike, res: ResponseLike): Promise<void>
	{
		try
		{
			const context = {
				operationName: `${req.method} ${req.path || req.url}`,
				requestId: this.generateRequestId(),
				metadata: {
					method: req.method,
					url: req.url,
					headers: req.headers,
					userAgent: req.headers['user-agent'],
					ip: req.ip,
				},
			};

			const result = await this.errorHandler!.handleError(error, context);

			if (this.options.logErrors)
			{
				this.logger.error({
					method: req.method,
					url: req.url,
					error: error.message,
					errorId: result.reportId,
					classification: result.classification.classification,
					handled: result.handled,
				}, 'HTTP Request Error');
			}

			this.sendErrorResponse(error, req, res, result.reportId);
		}
		catch (handlingError)
		{
			this.logger.error({ error: handlingError }, 'Error handler failed');
			this.handleErrorFallback(error, req, res);
		}
	}

	private handleErrorFallback(error: Error, req: RequestLike, res: ResponseLike): void
	{
		if (this.options.logErrors)
		{
			this.logger.error({
				method: req.method,
				url: req.url,
				error: error.message,
				stack: error.stack,
			}, 'HTTP Request Error (fallback)');
		}

		this.sendErrorResponse(error, req, res);
	}

	private sendErrorResponse(error: Error, req: RequestLike, res: ResponseLike, errorId?: string): void
	{
		const statusCode = this.getStatusCodeFromError(error);
		const errorResponse: any = {
			error: {
				message: error.message,
				type: error.name,
				timestamp: new Date().toISOString(),
			},
		};

		if (errorId)
		{
			errorResponse.error.id = errorId;
		}

		if (this.options.includeStack && error.stack)
		{
			errorResponse.error.stack = error.stack;
		}

		// Set status code if possible
		if (res.statusCode !== statusCode)
		{
			try
			{
				res.statusCode = statusCode;
			}
			catch (e)
			{
				// Some frameworks might not allow setting status code after headers are sent
			}
		}

		// Try to send JSON response
		if (res.end)
		{
			try
			{
				if (res.set)
				{
					res.set('Content-Type', 'application/json');
				}
				res.end(JSON.stringify(errorResponse));
			}
			catch (e)
			{
				// Fallback to plain text
				res.end(`Error: ${error.message}`);
			}
		}
	}

	private getStatusCodeFromError(error: Error): number
	{
		// Check if error has status code
		const errorWithStatus = error as any;
		if (errorWithStatus.status && typeof errorWithStatus.status === 'number')
		{
			return errorWithStatus.status;
		}
		if (errorWithStatus.statusCode && typeof errorWithStatus.statusCode === 'number')
		{
			return errorWithStatus.statusCode;
		}

		// Determine status code from error type
		switch (error.name)
		{
			case 'ValidationError':
				return 400;
			case 'UnauthorizedError':
			case 'AuthenticationError':
				return 401;
			case 'ForbiddenError':
			case 'AuthorizationError':
				return 403;
			case 'NotFoundError':
				return 404;
			case 'ConflictError':
				return 409;
			case 'RequestTimeout':
				return 408;
			case 'RateLimitError':
				return 429;
			default:
				return 500;
		}
	}

	private generateRequestId(): string
	{
		return `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
	}
}

export default ErrorMiddleware;
