/**
 * Application constants and configuration values
 */

const CACHE_KEYS =
{
	DOMAIN_ANALYSIS: 'domain:analysis:{domain}',
	DOMAIN_RANKING: 'domain:ranking:{domain}',
	// Legacy search template (query + filters)
	SEARCH_RESULTS: 'search:results:{query}:{filters}',
	// Hash-based search cache used by CachingService
	SEARCH_RESULTS_HASH: 'search:results:{queryHash}',
	// Top domains (timeframe-based)
	TOP_DOMAINS_TIMEFRAME: 'top:domains:{category}:{timeframe}',
	// Legacy top domains (country-based)
	TOP_DOMAINS: 'domains:top:{category}:{country}',
	// Rankings
	GLOBAL_RANKINGS: 'rankings:global:{page}',
	CATEGORY_RANKINGS: 'rankings:category:{category}:{page}',
	DOMAIN_DESCRIPTION: 'domain:description:{domain}',
	DOMAIN_COMPARISON: 'domain:comparison:{domains}',
} as const;

const CACHE_TTL =
{
	DOMAIN_ANALYSIS: 3600, // 1 hour
	DOMAIN_RANKING: 1800, // 30 minutes
	SEARCH_RESULTS: 900, // 15 minutes
	TOP_DOMAINS: 1800, // 30 minutes
	DOMAIN_DESCRIPTION: 7200, // 2 hours
	DOMAIN_COMPARISON: 1800, // 30 minutes
} as const;

const LIMITS =
{
	MAX_SEARCH_RESULTS: 1000,
	MAX_COMPARISON_DOMAINS: 5,
	MAX_QUERY_LENGTH: 500,
	DEFAULT_PAGE_SIZE: 20,
	MAX_PAGE_SIZE: 100,
} as const;

const HTTP_STATUS =
{
	OK: 200,
	CREATED: 201,
	BAD_REQUEST: 400,
	UNAUTHORIZED: 401,
	FORBIDDEN: 403,
	NOT_FOUND: 404,
	INTERNAL_SERVER_ERROR: 500,
	SERVICE_UNAVAILABLE: 503,
} as const;

// Default database and service constants used by Config
const DATABASE =
{
	SCYLLA: {
		CONTACT_POINTS: ['scylla'],
		KEYSPACE: 'domainr',
		LOCAL_DATA_CENTER: 'datacenter1',
	},
	MARIA: {
		HOST: 'mariadb',
		PORT: 3306,
		DATABASE: 'domainr',
	},
	REDIS: {
		HOST: 'redis',
		PORT: 6379,
		DB: 0,
	},
	MANTICORE: {
		HOST: 'manticore',
		PORT: 9308,
	},
} as const;

const EXTERNAL =
{
	IMAGE_PROXY_URL: 'http://image-proxy:8080',
} as const;

const CRAWL =
{
	TIMEOUT: 30000,
	MAX_RETRIES: 3,
	RATE_LIMIT: {
		REQUESTS_PER_MINUTE: 60,
		CONCURRENT_REQUESTS: 5,
	},
} as const;

const RANKING_WEIGHTS =
{
	PERFORMANCE: 0.25,
	SECURITY: 0.20,
	SEO: 0.20,
	TECHNICAL: 0.15,
	BACKLINKS: 0.20,
} as const;

const JOB_QUEUES =
{
	DOMAIN_CRAWL: 'domain_crawl',
	RANKING_UPDATE: 'ranking_update',
	TRAFFIC_ANALYSIS: 'traffic_analysis',
	BACKLINK_ANALYSIS: 'backlink_analysis',
	MANTICORE_SYNC: 'manticore_sync',
	MAINTENANCE: 'maintenance',
} as const;

const Constants =
{
	CACHE_KEYS,
	CACHE_TTL,
	LIMITS,
	HTTP_STATUS,
	DATABASE,
	EXTERNAL,
	CRAWL,
	RANKING_WEIGHTS,
	JOB_QUEUES,
} as const;

export {
	CACHE_KEYS,
	CACHE_TTL,
	LIMITS,
	HTTP_STATUS,
	DATABASE,
	EXTERNAL,
	CRAWL,
	RANKING_WEIGHTS,
	JOB_QUEUES,
};

export default Constants;
