// Global test setup for shared module
import { vi } from 'vitest';

// Mock environment variables
process.env.NODE_ENV = 'test';
process.env.SCYLLA_HOSTS = 'localhost:9042';
process.env.MARIA_HOST = 'localhost';
process.env.MARIA_PORT = '3306';
process.env.MARIA_USER = 'test';
process.env.MARIA_PASSWORD = 'test';
process.env.MARIA_DATABASE = 'test_db';
process.env.REDIS_URL = 'redis://localhost:6379';
process.env.MANTICORE_HOST = 'localhost';
process.env.MANTICORE_PORT = '9308';

// Increase timeout for database operations
vi.setConfig({ testTimeout: 30000 });

// Mock console methods to reduce noise in tests
global.console = {
	...console,
	log: vi.fn(),
	debug: vi.fn(),
	info: vi.fn(),
	warn: vi.fn(),
	error: vi.fn(),
};
