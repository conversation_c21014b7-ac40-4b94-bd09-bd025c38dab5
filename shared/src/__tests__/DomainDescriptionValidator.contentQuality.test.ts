import {
	describe, it, expect, beforeEach,
} from 'vitest';
import DomainDescriptionValidator from '../utils/DomainDescriptionValidator';
import type { DomainDescriptionInterface } from '../models/DomainDescription';

describe('DomainDescriptionValidator - Content Quality', () =>
{
	let validator: DomainDescriptionValidator;

	beforeEach(() =>
	{
		validator = DomainDescriptionValidator.get();
	});

	const createBasicDomain = (
		overrides: Partial<DomainDescriptionInterface> = {},
	): DomainDescriptionInterface => ({
		metadata:
		{
			domain: 'example.com',
			tld: 'com',
			status: 'active',
			category: { primary: 'Technology' },
			tags: [
				'technology',
				'software',
				'development',
				'web',
				'programming',
			],
			...overrides.metadata,
		},
		ranking: {},
		crawl:
		{
			lastCrawled: '2024-01-01T00:00:00Z',
			crawlType: 'quick',
		},
		...overrides,
	});

	describe('SEO Content Quality Validation', () =>
	{
		it('should warn about keyword over-optimization', () =>
		{
			const domain = createBasicDomain({
				metadata: {
					domain: 'example.com',
					tld: 'com',
					status: 'active',
					category: { primary: 'Technology' },
					tags: ['technology', 'software', 'development', 'web', 'programming'],
					preGenerated: true,
				},
				overview: {
					summary: 'Technology technology technology is the best technology solution. Our technology platform provides technology services for technology companies. Technology experts use our technology tools for technology development. Technology innovation drives our technology products forward.',
				},
			});

			const result = validator.validate(domain);

			expect(result.warnings?.some(w => w.includes('density'))).toBe(true);
		});

		it('should warn about missing headings in long content', () =>
		{
			const longContent = Array(100).fill('This is a sentence without any headings.').join(' ');
			const domain = createBasicDomain({
				metadata: {
					domain: 'example.com',
					tld: 'com',
					status: 'active',
					category: { primary: 'Technology' },
					tags: ['technology', 'software', 'development', 'web', 'programming'],
					preGenerated: true,
				},
				overview: {
					summary: longContent,
				},
			});

			const result = validator.validate(domain);

			expect(result.warnings?.some(w => w.includes('headings'))).toBe(true);
		});

		it('should warn about missing internal links', () =>
		{
			const domain = createBasicDomain({
				metadata: {
					domain: 'example.com',
					tld: 'com',
					status: 'active',
					category: { primary: 'Technology' },
					tags: ['technology', 'software', 'development', 'web', 'programming'],
					preGenerated: true,
				},
				overview: {
					summary: 'Our services include products and solutions. Contact our support team for documentation about our features and pricing.',
				},
			});

			const result = validator.validate(domain);

			expect(result.warnings?.some(w => w.includes('internal links'))).toBe(true);
		});

		it('should warn about short first paragraph', () =>
		{
			const domain = createBasicDomain({
				metadata: {
					domain: 'example.com',
					tld: 'com',
					status: 'active',
					category: { primary: 'Technology' },
					tags: ['technology', 'software', 'development', 'web', 'programming'],
					preGenerated: true,
				},
				overview: {
					summary: 'Short.\n\nThis is a much longer paragraph that contains more detailed information about the domain and its services.',
				},
			});

			const result = validator.validate(domain);

			expect(result.warnings?.some(w => w.includes('value proposition'))).toBe(true);
		});

		it('should warn about missing call-to-action', () =>
		{
			const longContentWithoutCTA = Array(50).fill('This is content without any call to action elements.').join(' ');
			const domain = createBasicDomain({
				metadata: {
					domain: 'example.com',
					tld: 'com',
					status: 'active',
					category: { primary: 'Technology' },
					tags: ['technology', 'software', 'development', 'web', 'programming'],
					preGenerated: true,
				},
				overview: {
					summary: longContentWithoutCTA,
				},
			});

			const result = validator.validate(domain);

			expect(result.warnings?.some(w => w.includes('call-to-action'))).toBe(true);
		});
	});

	describe('Readability Standards Validation', () =>
	{
		it('should warn about long average sentence length', () =>
		{
			const longSentence = 'This is an extremely long sentence that contains way too many words and clauses and should be broken down into smaller more digestible pieces for better readability and user experience.';
			const domain = createBasicDomain({
				overview: {
					summary: Array(20).fill(longSentence).join(' '),
				},
			});

			const result = validator.validate(domain);

			expect(result.warnings?.some(w => w.includes('sentence length'))).toBe(true);
		});

		it('should warn about long paragraphs', () =>
		{
			const longParagraph = Array(200).fill('word').join(' ');
			const domain = createBasicDomain({
				overview: {
					summary: `${longParagraph}\n\nShort paragraph.`,
				},
			});

			const result = validator.validate(domain);

			expect(result.warnings?.some(w => w.includes('paragraph'))).toBe(true);
		});

		it('should warn about excessive passive voice', () =>
		{
			// Create content with clear passive voice patterns
			const passiveContent = 'The website was created by developers. The content was written by writers. The design was made by designers. The testing was done by testers. The system was built by engineers. The code was reviewed by experts. The project was managed by leaders. The documentation was prepared by technical writers.';
			const domain = createBasicDomain({
				overview: {
					summary: passiveContent,
				},
			});

			const result = validator.validate(domain);

			expect(result.warnings?.some(w => w.includes('Passive voice'))).toBe(true);
		});

		it('should warn about low transition word usage', () =>
		{
			const contentWithoutTransitions = 'This is a sentence. This is another sentence. This is a third sentence. This is a fourth sentence.';
			const domain = createBasicDomain({
				overview: {
					summary: contentWithoutTransitions,
				},
			});

			const result = validator.validate(domain);

			expect(result.warnings?.some(w => w.includes('transition word'))).toBe(true);
		});

		it('should warn about low readability score', () =>
		{
			const difficultContent = 'The implementation of sophisticated algorithmic methodologies necessitates comprehensive understanding of multifaceted computational paradigms and their intricate interdependencies within complex technological infrastructures.';
			const domain = createBasicDomain({
				overview: {
					summary: Array(10).fill(difficultContent).join(' '),
				},
			});

			const result = validator.validate(domain);

			expect(result.warnings?.some(w => w.includes('Readability score'))).toBe(true);
		});

		it('should accept content with good readability', () =>
		{
			const goodContent = 'Our platform helps businesses grow. We provide simple tools that work well. However, we also offer advanced features. Therefore, users can choose what they need. Additionally, our support team is always ready to help.';
			const domain = createBasicDomain({
				overview: {
					summary: Array(20).fill(goodContent).join(' '),
				},
			});

			const result = validator.validate(domain);

			// Should not have readability warnings
			expect(result.warnings?.some(w => w.includes('sentence length') || w.includes('passive voice') || w.includes('transition word'))).toBe(false);
		});
	});

	describe('Content Quality Validation', () =>
	{
		it('should error on extremely short content', () =>
		{
			const domain = createBasicDomain({
				overview: {
					summary: 'Too short.',
				},
			});

			const result = validator.validateContentQuality(domain);

			expect(result.ok).toBe(false);
			expect(result.errors?.some(e => e.message?.includes('too short'))).toBe(true);
		});

		it('should error on placeholder content', () =>
		{
			const domain = createBasicDomain({
				overview: {
					summary: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. This is placeholder content that should be replaced with real content.',
				},
			});

			const result = validator.validateContentQuality(domain);

			expect(result.ok).toBe(false);
			expect(result.errors?.some(e => e.message?.includes('placeholder'))).toBe(true);
		});

		it('should warn about repetitive content', () =>
		{
			const repetitiveContent = 'This is a sentence. This is a sentence. This is a sentence. This is a sentence.';
			const domain = createBasicDomain({
				overview: {
					summary: repetitiveContent,
				},
			});

			const result = validator.validateContentQuality(domain);

			expect(result.warnings?.some(w => w.includes('repetitive'))).toBe(true);
		});

		it('should error on invalid tags', () =>
		{
			const domain = createBasicDomain({
				metadata: {
					domain: 'example.com',
					tld: 'com',
					status: 'active',
					category: { primary: 'Technology' },
					tags: ['valid', '', 'a', '   ', 'another-valid'],
				},
				overview: {
					summary: 'This is a test summary with enough content to trigger validation.',
				},
			});

			const result = validator.validateContentQuality(domain);

			expect(result.ok).toBe(false);
			expect(result.errors?.some(e => e.message?.includes('invalid tag'))).toBe(true);
		});

		it('should warn about overly long tags', () =>
		{
			const domain = createBasicDomain({
				metadata: {
					domain: 'example.com',
					tld: 'com',
					status: 'active',
					category: { primary: 'Technology' },
					tags: ['technology', 'this-is-an-extremely-long-tag-that-exceeds-reasonable-length', 'software', 'development', 'web'],
				},
				overview: {
					summary: 'This is a test summary with enough content to trigger validation.',
				},
			});

			const result = validator.validateContentQuality(domain);

			expect(result.warnings?.some(w => w.includes('longer than 30 characters'))).toBe(true);
		});

		it('should warn about duplicate tags', () =>
		{
			const domain = createBasicDomain({
				metadata: {
					domain: 'example.com',
					tld: 'com',
					status: 'active',
					category: { primary: 'Technology' },
					tags: ['technology', 'Technology', 'TECHNOLOGY', 'software', 'development'],
				},
				overview: {
					summary: 'This is a test summary with enough content to trigger validation.',
				},
			});

			const result = validator.validateContentQuality(domain);

			expect(result.warnings?.some(w => w.includes('duplicate tags'))).toBe(true);
		});
	});

	describe('SEO Compliance Validation', () =>
	{
		it('should warn about long meta description', () =>
		{
			const domain = createBasicDomain({
				seo: {
					metaDescription: 'This is an extremely long meta description that exceeds the recommended 160 character limit and should be shortened for better search engine optimization and user experience in search results.',
				},
			});

			const result = validator.validateContentQuality(domain);

			expect(result.warnings?.some(w => w.includes('Meta description') && w.includes('160'))).toBe(true);
		});

		it('should warn about short meta description', () =>
		{
			const domain = createBasicDomain({
				seo: {
					metaDescription: 'Too short meta description.',
				},
			});

			const result = validator.validateContentQuality(domain);

			expect(result.warnings?.some(w => w.includes('Meta description') && w.includes('120'))).toBe(true);
		});

		it('should warn about long SEO title', () =>
		{
			const domain = createBasicDomain({
				seo: {
					title: 'This is an extremely long SEO title that exceeds the recommended 70 character limit',
				},
			});

			const result = validator.validateContentQuality(domain);

			expect(result.warnings?.some(w => w.includes('SEO title') && w.includes('70'))).toBe(true);
		});

		it('should warn about short SEO title', () =>
		{
			const domain = createBasicDomain({
				seo: {
					title: 'Short',
				},
			});

			const result = validator.validateContentQuality(domain);

			expect(result.warnings?.some(w => w.includes('SEO title') && w.includes('30'))).toBe(true);
		});

		it('should warn about missing SEO elements', () =>
		{
			const domain = createBasicDomain({
				overview: {
					summary: 'This domain has content but no SEO elements.',
				},
			});

			const result = validator.validateContentQuality(domain);

			expect(result.warnings?.some(w => w.includes('Missing SEO title'))).toBe(true);
			expect(result.warnings?.some(w => w.includes('Missing meta description'))).toBe(true);
		});
	});

	describe('Content Consistency Validation', () =>
	{
		it('should warn when category is not reflected in tags', () =>
		{
			const domain = createBasicDomain({
				metadata: {
					domain: 'example.com',
					tld: 'com',
					status: 'active',
					category: { primary: 'Healthcare Technology' },
					tags: ['business', 'finance', 'marketing', 'sales', 'consulting'],
				},
			});

			const result = validator.validateContentQuality(domain);

			expect(result.warnings?.some(w => w.includes('Primary category should be reflected in tags'))).toBe(true);
		});

		it('should warn when many tags are not mentioned in summary', () =>
		{
			const domain = createBasicDomain({
				metadata: {
					domain: 'example.com',
					tld: 'com',
					status: 'active',
					category: { primary: 'Technology' },
					tags: ['blockchain', 'cryptocurrency', 'artificial-intelligence', 'machine-learning', 'quantum-computing'],
				},
				overview: {
					summary: 'This is a simple technology company that provides basic web services.',
				},
			});

			const result = validator.validateContentQuality(domain);

			expect(result.warnings?.some(w => w.includes('Many tags are not mentioned in summary'))).toBe(true);
		});

		it('should warn about language inconsistency', () =>
		{
			const domain = createBasicDomain({
				metadata: {
					domain: 'example.com',
					tld: 'com',
					status: 'active',
					language: 'es',
					category: { primary: 'Technology' },
					tags: ['technology', 'software', 'development', 'web', 'programming'],
				},
				overview: {
					summary: 'This is an English summary with many English words like the, and, or, but, in, on, at, to, for, of, with, by.',
				},
			});

			const result = validator.validateContentQuality(domain);

			expect(result.warnings?.some(w => w.includes('language') && w.includes('may not match'))).toBe(true);
		});

		it('should accept consistent content', () =>
		{
			const domain = createBasicDomain({
				metadata: {
					domain: 'example.com',
					tld: 'com',
					status: 'active',
					language: 'en',
					category: { primary: 'Technology' },
					tags: ['technology', 'software', 'development', 'web', 'programming'],
				},
				overview: {
					summary: 'Our technology company specializes in software development and web programming solutions.',
				},
			});

			const result = validator.validateContentQuality(domain);

			// Should not have consistency warnings
			expect(result.warnings?.some(w => w.includes('category should be reflected') || w.includes('not mentioned in summary') || w.includes('may not match'))).toBe(false);
		});
	});

	describe('Edge Cases', () =>
	{
		it('should handle empty content gracefully', () =>
		{
			const domain = createBasicDomain({
				overview: {
					summary: '',
				},
			});

			const result = validator.validateContentQuality(domain);

			expect(result.ok).toBe(false);
		});

		it('should handle missing overview section', () =>
		{
			const domain = createBasicDomain();
			delete domain.overview;

			const result = validator.validateContentQuality(domain);

			expect(result.ok).toBe(true); // Should not error if no content to validate
		});

		it('should handle content with only whitespace', () =>
		{
			const domain = createBasicDomain({
				overview: {
					summary: '   \n\t   \n   ',
				},
			});

			const result = validator.validateContentQuality(domain);

			expect(result.ok).toBe(false);
		});

		it('should handle very short content for language detection', () =>
		{
			const domain = createBasicDomain({
				metadata: {
					domain: 'example.com',
					tld: 'com',
					status: 'active',
					language: 'es',
					category: { primary: 'Technology' },
					tags: ['technology', 'software', 'development', 'web', 'programming'],
				},
				overview: {
					summary: 'Short text.',
				},
			});

			const result = validator.validateContentQuality(domain);

			// Should not warn about language mismatch for very short content
			expect(result.warnings?.some(w => w.includes('may not match'))).toBe(false);
		});
	});
});
