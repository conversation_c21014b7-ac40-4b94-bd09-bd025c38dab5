import type { LoggerInstanceType } from '../utils/Logger';
import type { CircuitBreakerConfigType } from './types';
import { CircuitBreakerState } from './types';

export interface CircuitBreakerMetricsType {
	totalCalls: number;
	successfulCalls: number;
	failedCalls: number;
	consecutiveFailures: number;
	consecutiveSuccesses: number;
	lastFailureTime?: Date;
	lastSuccessTime?: Date;
	state: CircuitBreakerState;
	stateChanges: Array<{
		from: CircuitBreakerState;
		to: CircuitBreakerState;
		timestamp: Date;
		reason: string;
	}>;
}

export class CircuitBreaker
{
	private readonly name: string;
	private readonly logger: LoggerInstanceType;
	private readonly config: CircuitBreakerConfigType;
	private state: CircuitBreakerState;
	private metrics: CircuitBreakerMetricsType;
	private nextAttempt: number;
	private readonly rollingWindow: boolean[];
	private rollingWindowIndex: number;

	constructor(name: string, logger: LoggerInstanceType, config?: Partial<CircuitBreakerConfigType>)
	{
		this.name = name;
		this.logger = logger;
		this.config = {
			failureThreshold: 5,
			successThreshold: 2,
			timeout: 60000,
			volumeThreshold: 10,
			rollingWindowSize: 10,
			...config,
		};

		this.state = CircuitBreakerState.CLOSED;
		this.nextAttempt = Date.now();
		this.rollingWindow = new Array(this.config.rollingWindowSize).fill(true);
		this.rollingWindowIndex = 0;

		this.metrics = {
			totalCalls: 0,
			successfulCalls: 0,
			failedCalls: 0,
			consecutiveFailures: 0,
			consecutiveSuccesses: 0,
			state: CircuitBreakerState.CLOSED,
			stateChanges: [],
		};
	}

	async execute<T>(operation: () => Promise<T>): Promise<T>
	{
		if (this.state === CircuitBreakerState.OPEN)
		{
			if (Date.now() < this.nextAttempt)
			{
				throw new Error(`Circuit breaker is OPEN for ${this.name}`);
			}
			this.transitionTo(CircuitBreakerState.HALF_OPEN, 'Timeout expired');
		}

		try
		{
			const result = await operation();
			this.onSuccess();
			return result;
		}
		catch (error)
		{
			this.onFailure();
			throw error;
		}
	}

	private onSuccess(): void
	{
		this.metrics.totalCalls++;
		this.metrics.successfulCalls++;
		this.metrics.consecutiveSuccesses++;
		this.metrics.consecutiveFailures = 0;
		this.metrics.lastSuccessTime = new Date();

		this.updateRollingWindow(true);

		if (this.state === CircuitBreakerState.HALF_OPEN)
		{
			if (this.metrics.consecutiveSuccesses >= this.config.successThreshold)
			{
				this.transitionTo(CircuitBreakerState.CLOSED, 'Success threshold reached');
			}
		}
	}

	private onFailure(): void
	{
		this.metrics.totalCalls++;
		this.metrics.failedCalls++;
		this.metrics.consecutiveFailures++;
		this.metrics.consecutiveSuccesses = 0;
		this.metrics.lastFailureTime = new Date();

		this.updateRollingWindow(false);

		if (this.state === CircuitBreakerState.HALF_OPEN)
		{
			this.transitionTo(CircuitBreakerState.OPEN, 'Failure in HALF_OPEN state');
		}
		else if (this.state === CircuitBreakerState.CLOSED)
		{
			const failureRate = this.calculateFailureRate();

			if (this.metrics.totalCalls >= this.config.volumeThreshold &&
				failureRate > (this.config.failureThreshold / 100))
			{
				this.transitionTo(CircuitBreakerState.OPEN, `Failure rate ${failureRate.toFixed(2)} exceeded threshold`);
			}
		}
	}

	private updateRollingWindow(success: boolean): void
	{
		this.rollingWindow[this.rollingWindowIndex] = success;
		this.rollingWindowIndex = (this.rollingWindowIndex + 1) % this.config.rollingWindowSize;
	}

	private calculateFailureRate(): number
	{
		const recentCalls = Math.min(this.metrics.totalCalls, this.config.rollingWindowSize);
		if (recentCalls === 0) return 0;

		const failures = this.rollingWindow.filter(success => !success).length;
		return failures / recentCalls;
	}

	private transitionTo(newState: CircuitBreakerState, reason: string): void
	{
		const oldState = this.state;
		this.state = newState;
		this.metrics.state = newState;

		if (newState === CircuitBreakerState.OPEN)
		{
			this.nextAttempt = Date.now() + this.config.timeout;
			this.metrics.consecutiveFailures = 0;
			this.metrics.consecutiveSuccesses = 0;
		}

		this.metrics.stateChanges.push({
			from: oldState,
			to: newState,
			timestamp: new Date(),
			reason,
		});

		this.logger.info({
			name: this.name,
			from: oldState,
			to: newState,
			reason,
		}, 'Circuit breaker state changed');
	}

	getState(): CircuitBreakerState
	{
		return this.state;
	}

	getMetrics(): CircuitBreakerMetricsType
	{
		return { ...this.metrics };
	}

	reset(): void
	{
		this.transitionTo(CircuitBreakerState.CLOSED, 'Manual reset');
		this.metrics.consecutiveFailures = 0;
		this.metrics.consecutiveSuccesses = 0;
		this.rollingWindow.fill(true);
		this.rollingWindowIndex = 0;
	}

	forceOpen(reason?: string): void
	{
		this.transitionTo(CircuitBreakerState.OPEN, reason || 'Forced open');
	}

	forceClose(reason?: string): void
	{
		this.transitionTo(CircuitBreakerState.CLOSED, reason || 'Forced closed');
	}

	forceState(state: CircuitBreakerState, reason?: string): void
	{
		this.transitionTo(state, reason || `Forced to ${state}`);
	}
}

export default CircuitBreaker;
