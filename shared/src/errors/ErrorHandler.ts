import type { LoggerInstanceType } from '../utils/Logger';
import { ErrorClassificationService } from './ErrorClassification';
import { RetryManager } from './RetryManager';
import { CircuitBreaker } from './CircuitBreaker';
import { CircuitBreakerState } from './types';
import type {
	ErrorHandlingConfigType,
	ErrorHandlingContextType,
	ErrorHandlingResultType,
	ErrorClassificationResultType,
	ErrorMetricsType,
} from './types';

export abstract class BaseErrorHandler
{
	protected readonly logger: LoggerInstanceType;
	protected readonly config: ErrorHandlingConfigType;
	protected readonly classificationService: ErrorClassificationService;
	protected readonly retryManager: RetryManager;
	protected readonly circuitBreakers: Map<string, CircuitBreaker>;
	protected errorMetrics: ErrorMetricsType;

	constructor(logger: LoggerInstanceType, config?: Partial<ErrorHandlingConfigType>)
	{
		this.logger = logger;
		this.config = {
			classification: {
				enabled: true,
				confidenceThreshold: 0.7,
			},
			retry: {
				enabled: true,
				defaultStrategy: {
					maxAttempts: 3,
					baseDelay: 1000,
					maxDelay: 30000,
					backoffMultiplier: 2,
					jitterEnabled: true,
				},
			},
			circuitBreaker: {
				enabled: true,
				defaultConfig: {
					failureThreshold: 5,
					timeout: 60000,
					volumeThreshold: 10,
				},
			},
			...config,
		};

		this.classificationService = new ErrorClassificationService(logger);
		this.retryManager = new RetryManager(logger, this.config.retry.defaultStrategy);
		this.circuitBreakers = new Map();

		this.errorMetrics = {
			totalErrors: 0,
			errorsByCategory: {},
			errorsBySeverity: {},
			retrySuccessRate: 0,
			circuitBreakerTrips: 0,
		};

		this.initialize();
	}

	protected initialize(): void
	{
		this.logger.info({
			classification: this.config.classification.enabled,
			retry: this.config.retry.enabled,
			circuitBreaker: this.config.circuitBreaker.enabled,
			msg: 'Base error handler initialized',
		});
	}

	async handleError(
		error: Error,
		context: ErrorHandlingContextType
	): Promise<ErrorHandlingResultType>
	{
		const actions: string[] = [];
		let classification: ErrorClassificationResultType;
		const retryAttempted = false;
		let recoveryAttempted = false;
		let degradationTriggered = false;

		try
		{
			classification = this.classifyError(error, context);
			actions.push('Error classified');

			this.updateMetrics(classification);

			const reportId = await this.reportError(error, classification, context);
			if (reportId)
			{
				actions.push('Error reported');
			}

			const circuitBreaker = this.getOrCreateCircuitBreaker(context.operationName);
			if (circuitBreaker.getState() === CircuitBreakerState.OPEN)
			{
				actions.push('Circuit breaker is open - operation blocked');
				throw new Error(`Circuit breaker is open for operation: ${context.operationName}`);
			}

			const serviceSpecificResult = await this.handleServiceSpecificError(
				error,
				classification,
				context
			);

			if (serviceSpecificResult)
			{
				actions.push(...serviceSpecificResult.actions);
				recoveryAttempted = serviceSpecificResult.recoveryAttempted || false;
				degradationTriggered = serviceSpecificResult.degradationTriggered || false;
			}

			return {
				handled: true,
				classification,
				retryAttempted,
				recoveryAttempted,
				degradationTriggered,
				reportId,
				actions,
			};
		}
		catch (handlingError)
		{
			this.logger.error({
				originalError: error.message,
				handlingError: (handlingError as Error).message,
				context,
				msg: 'Error during error handling',
			});

			classification = classification! || this.getDefaultClassification(error, context.metadata);

			return {
				handled: false,
				classification,
				retryAttempted,
				recoveryAttempted,
				degradationTriggered,
				actions: [...actions, 'Error handling failed'],
			};
		}
	}

	async executeWithErrorHandling<T>(
		operationName: string,
		operation: () => Promise<T>,
		context: Partial<ErrorHandlingContextType> = {}
	): Promise<T>
	{
		const fullContext: ErrorHandlingContextType = {
			operationName,
			operationId: this.generateOperationId(),
			metadata: {},
			...context,
		};

		const circuitBreaker = this.getOrCreateCircuitBreaker(operationName);

		try
		{
			if (this.config.circuitBreaker.enabled)
			{
				return await circuitBreaker.execute(async () =>
				{
					if (this.config.retry.enabled)
					{
						const result = await this.retryManager.executeWithRetry(
							operationName,
							operation,
							undefined,
							this.config.retry.defaultStrategy
						);

						if (!result.success)
						{
							throw result.error;
						}

						return result.result!;
					}

					return await operation();
				});
			}

			if (this.config.retry.enabled)
			{
				const result = await this.retryManager.executeWithRetry(
					operationName,
					operation,
					undefined,
					this.config.retry.defaultStrategy
				);

				if (!result.success)
				{
					throw result.error;
				}

				return result.result!;
			}

			return await operation();
		}
		catch (error)
		{
			const handlingResult = await this.handleError(error as Error, fullContext);

			this.logger.error({
				operationName,
				operationId: fullContext.operationId,
				handlingResult,
				msg: 'Operation failed after error handling',
			});

			throw error;
		}
	}

	protected classifyError(error: Error, context: ErrorHandlingContextType): ErrorClassificationResultType
	{
		if (!this.config.classification.enabled)
		{
			return this.getDefaultClassification(error, context.metadata);
		}

		return this.classificationService.classifyError(error, context.metadata);
	}

	protected getDefaultClassification(error: Error, metadata: Record<string, any>): ErrorClassificationResultType
	{
		return {
			classification: {
				severity: 'medium',
				category: 'business_logic',
				persistence: 'permanent',
				recoverability: 'manual_recoverable',
				scope: 'service_specific',
				retryable: false,
				requiresImmediateAttention: false,
				escalationRequired: false,
			},
			confidence: 0.5,
			metadata,
		};
	}

	protected getOrCreateCircuitBreaker(operationName: string): CircuitBreaker
	{
		let circuitBreaker = this.circuitBreakers.get(operationName);

		if (!circuitBreaker)
		{
			circuitBreaker = new CircuitBreaker(
				operationName,
				this.logger,
				this.config.circuitBreaker.defaultConfig
			);
			this.circuitBreakers.set(operationName, circuitBreaker);
		}

		return circuitBreaker;
	}

	protected updateMetrics(classification: ErrorClassificationResultType): void
	{
		this.errorMetrics.totalErrors++;

		const category = classification.classification.category;
		this.errorMetrics.errorsByCategory[category] = (this.errorMetrics.errorsByCategory[category] || 0) + 1;

		const severity = classification.classification.severity;
		this.errorMetrics.errorsBySeverity[severity] = (this.errorMetrics.errorsBySeverity[severity] || 0) + 1;

		this.errorMetrics.lastError = {
			timestamp: new Date(),
			message: classification.metadata.errorMessage || 'Unknown error',
			category,
			severity,
		};

		const retryStats = this.retryManager.getStatistics();
		if (retryStats.length > 0)
		{
			const totalSuccessRate = retryStats.reduce((sum, stat) => sum + stat.successRate, 0) / retryStats.length;
			this.errorMetrics.retrySuccessRate = totalSuccessRate;
		}

		const openCircuits = Array.from(this.circuitBreakers.values())
			.filter(cb => cb.getState() === CircuitBreakerState.OPEN).length;
		this.errorMetrics.circuitBreakerTrips = openCircuits;
	}

	protected generateOperationId(): string
	{
		return `op-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
	}

	getMetrics(): ErrorMetricsType
	{
		return { ...this.errorMetrics };
	}

	getRetryStatistics()
	{
		return this.retryManager.getStatistics();
	}

	getCircuitBreakerStates(): Record<string, CircuitBreakerState>
	{
		const states: Record<string, CircuitBreakerState> = {};
		for (const [name, breaker] of this.circuitBreakers)
		{
			states[name] = breaker.getState();
		}
		return states;
	}

	resetCircuitBreaker(operationName: string): boolean
	{
		const circuitBreaker = this.circuitBreakers.get(operationName);
		if (circuitBreaker)
		{
			circuitBreaker.reset();
			return true;
		}
		return false;
	}

	forceCircuitBreakerState(operationName: string, state: CircuitBreakerState, reason?: string): boolean
	{
		const circuitBreaker = this.circuitBreakers.get(operationName);
		if (circuitBreaker)
		{
			circuitBreaker.forceState(state, reason);
			return true;
		}
		return false;
	}

	shutdown(): void
	{
		this.logger.info('Error handler shutdown initiated');
		this.onShutdown();
	}

	protected abstract reportError(
		error: Error,
		classification: ErrorClassificationResultType,
		context: ErrorHandlingContextType
	): Promise<string | undefined>;

	protected abstract handleServiceSpecificError(
		error: Error,
		classification: ErrorClassificationResultType,
		context: ErrorHandlingContextType
	): Promise<{ actions: string[]; recoveryAttempted?: boolean; degradationTriggered?: boolean } | null>;

	protected abstract onShutdown(): void;
}

export default BaseErrorHandler;
