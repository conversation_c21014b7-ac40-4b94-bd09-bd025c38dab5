import type { LoggerInstanceType } from '../utils/Logger';
import type { RetryStrategyConfigType, RetryResultType, ErrorClassificationResultType } from './types';

export interface RetryStatisticsType {
	operationName: string;
	totalAttempts: number;
	successfulRetries: number;
	failedRetries: number;
	successRate: number;
	averageDelay: number;
	lastAttemptTime?: Date;
}

export class RetryManager
{
	private readonly logger: LoggerInstanceType;
	private readonly defaultConfig: RetryStrategyConfigType;
	private readonly statistics: Map<string, RetryStatisticsType>;

	constructor(logger: LoggerInstanceType, defaultConfig?: Partial<RetryStrategyConfigType>)
	{
		this.logger = logger;
		this.defaultConfig = {
			maxAttempts: 3,
			baseDelay: 1000,
			maxDelay: 30000,
			backoffMultiplier: 2,
			jitterEnabled: true,
			retryableErrors: [],
			nonRetryableErrors: [],
			...defaultConfig,
		};
		this.statistics = new Map();
	}

	async executeWithRetry<T>(
		operationName: string,
		operation: () => Promise<T>,
		classification?: ErrorClassificationResultType,
		customConfig?: Partial<RetryStrategyConfigType>
	): Promise<RetryResultType<T>>
	{
		const config = { ...this.defaultConfig, ...customConfig };
		let lastError: Error | undefined;
		let totalDelay = 0;

		for (let attempt = 1; attempt <= config.maxAttempts; attempt++)
		{
			try
			{
				const result = await operation();
				this.updateStatistics(operationName, true);

				return {
					success: true,
					result,
					attempts: attempt,
					totalDelay,
				};
			}
			catch (error)
			{
				lastError = error as Error;

				if (!this.shouldRetry(error as Error, classification, config, attempt))
				{
					this.updateStatistics(operationName, false);
					break;
				}

				const delay = this.calculateDelay(attempt, config);
				totalDelay += delay;

				this.logger.debug({
					operationName,
					attempt,
					maxAttempts: config.maxAttempts,
					delay,
					error: lastError.message,
				}, 'Retrying operation');

				await this.sleep(delay);
			}
		}

		this.updateStatistics(operationName, false);

		return ({
			success: false,
			error: lastError,
			attempts: config.maxAttempts,
			totalDelay,
		});
	}

	private shouldRetry(
		error: Error,
		classification?: ErrorClassificationResultType,
		config?: RetryStrategyConfigType,
		attempt?: number
	): boolean
	{
		if (!config || !attempt) return false;
		if (attempt >= config.maxAttempts) return false;
		if (classification && !classification.classification.retryable) return false;

		const errorMessage = error.message.toLowerCase();

		if (config.nonRetryableErrors?.some(pattern => errorMessage.includes(pattern.toLowerCase())))
		{
			return false;
		}

		if (config.retryableErrors && config.retryableErrors.length > 0)
		{
			return config.retryableErrors.some(pattern => errorMessage.includes(pattern.toLowerCase()));
		}

		return true;
	}

	private calculateDelay(attempt: number, config: RetryStrategyConfigType): number
	{
		let delay = Math.min(
			config.baseDelay * config.backoffMultiplier**(attempt - 1),
			config.maxDelay
		);

		if (config.jitterEnabled)
		{
			const jitter = Math.random() * 0.3 * delay;
			delay += jitter;
		}

		return Math.round(delay);
	}

	private sleep(ms: number): Promise<void>
	{
		return new Promise(resolve => setTimeout(resolve, ms));
	}

	private updateStatistics(operationName: string, success: boolean): void
	{
		const stats = this.statistics.get(operationName) || {
			operationName,
			totalAttempts: 0,
			successfulRetries: 0,
			failedRetries: 0,
			successRate: 0,
			averageDelay: 0,
		};

		stats.totalAttempts++;
		if (success)
		{
			stats.successfulRetries++;
		}
		else
		{
			stats.failedRetries++;
		}

		stats.successRate = stats.successfulRetries / stats.totalAttempts;
		stats.lastAttemptTime = new Date();

		this.statistics.set(operationName, stats);
	}

	getStatistics(): RetryStatisticsType[]
	{
		return Array.from(this.statistics.values());
	}

	clearStatistics(): void
	{
		this.statistics.clear();
	}
}

export default RetryManager;
