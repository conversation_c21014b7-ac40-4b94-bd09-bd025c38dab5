// Type aliases for backward compatibility with worker service
export type ErrorSeverityType = 'low' | 'medium' | 'high' | 'critical';
export type ErrorCategoryType = 'network' | 'database' | 'validation' | 'authorization' | 'business_logic' | 'external_service' | 'system';

export interface ErrorClassificationType {
	severity: ErrorSeverityType;
	category: ErrorCategoryType;
	persistence: 'transient' | 'permanent' | 'intermittent';
	recoverability: 'auto_recoverable' | 'manual_recoverable' | 'fatal';
	scope: 'request_specific' | 'user_specific' | 'service_specific' | 'system_wide';
	retryable: boolean;
	requiresImmediateAttention: boolean;
	escalationRequired: boolean;
}

export interface ErrorClassificationResultType {
	classification: ErrorClassificationType;
	confidence: number;
	metadata: Record<string, any>;
}

export interface RetryStrategyConfigType {
	maxAttempts: number;
	baseDelay: number;
	maxDelay: number;
	backoffMultiplier: number;
	jitterEnabled: boolean;
	retryableErrors?: string[];
	nonRetryableErrors?: string[];
}

export interface RetryResultType<T> {
	success: boolean;
	result?: T;
	error?: Error;
	attempts: number;
	totalDelay: number;
}

export interface CircuitBreakerConfigType {
	failureThreshold: number;
	successThreshold: number;
	timeout: number;
	volumeThreshold: number;
	rollingWindowSize: number;
}

export enum CircuitBreakerState {
	CLOSED = 'CLOSED',
	OPEN = 'OPEN',
	HALF_OPEN = 'HALF_OPEN',
}

export interface ErrorHandlingConfigType {
	classification: {
		enabled: boolean;
		confidenceThreshold: number;
	};
	retry: {
		enabled: boolean;
		defaultStrategy: Partial<RetryStrategyConfigType>;
	};
	circuitBreaker: {
		enabled: boolean;
		defaultConfig: Partial<CircuitBreakerConfigType>;
	};
}

export interface ErrorHandlingContextType {
	operationName: string;
	operationId?: string;
	userId?: string;
	sessionId?: string;
	requestId?: string;
	metadata: Record<string, any>;
}

export interface ErrorHandlingResultType {
	handled: boolean;
	classification: ErrorClassificationResultType;
	retryAttempted: boolean;
	recoveryAttempted: boolean;
	degradationTriggered: boolean;
	reportId?: string;
	actions: string[];
}

export interface ErrorMetricsType {
	totalErrors: number;
	errorsByCategory: Record<string, number>;
	errorsBySeverity: Record<string, number>;
	retrySuccessRate: number;
	circuitBreakerTrips: number;
	lastError?: {
		timestamp: Date;
		message: string;
		category: string;
		severity: string;
	};
}
