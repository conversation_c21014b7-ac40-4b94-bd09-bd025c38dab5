import type { LoggerInstanceType } from '../utils/Logger';
import type { ErrorClassificationType, ErrorClassificationResultType } from './types';

export interface ErrorPatternType {
	name: string;
	pattern: RegExp | string;
	classification: ErrorClassificationType;
	description: string;
	commonCauses: string[];
	suggestedActions: string[];
}

export class ErrorClassificationService
{
	private readonly logger: LoggerInstanceType;
	private readonly patterns: ErrorPatternType[];

	constructor(logger: LoggerInstanceType)
	{
		this.logger = logger;
		this.patterns = this.initializePatterns();
	}

	private initializePatterns(): ErrorPatternType[]
	{
		return [
			{
				name: 'NetworkTimeout',
				pattern: /ETIMEDOUT|ESOCKETTIMEDOUT|timeout/i,
				classification: {
					severity: 'medium',
					category: 'network',
					persistence: 'transient',
					recoverability: 'auto_recoverable',
					scope: 'request_specific',
					retryable: true,
					requiresImmediateAttention: false,
					escalationRequired: false,
				},
				description: 'Network timeout error',
				commonCauses: ['Network congestion', 'Remote server overload', 'Firewall issues'],
				suggestedActions: ['Retry with exponential backoff', 'Check network connectivity', 'Increase timeout values'],
			},
			{
				name: 'DatabaseConnection',
				pattern: /ECONNREFUSED.*3306|ECONNREFUSED.*5432|database.*connection/i,
				classification: {
					severity: 'high',
					category: 'database',
					persistence: 'intermittent',
					recoverability: 'auto_recoverable',
					scope: 'service_specific',
					retryable: true,
					requiresImmediateAttention: true,
					escalationRequired: false,
				},
				description: 'Database connection error',
				commonCauses: ['Database server down', 'Connection pool exhausted', 'Network issues'],
				suggestedActions: ['Check database status', 'Verify connection pool settings', 'Implement circuit breaker'],
			},
			{
				name: 'ValidationError',
				pattern: /validation.*failed|invalid.*input|schema.*error/i,
				classification: {
					severity: 'low',
					category: 'validation',
					persistence: 'permanent',
					recoverability: 'manual_recoverable',
					scope: 'request_specific',
					retryable: false,
					requiresImmediateAttention: false,
					escalationRequired: false,
				},
				description: 'Input validation error',
				commonCauses: ['Invalid user input', 'Schema mismatch', 'Data type error'],
				suggestedActions: ['Return error to client', 'Log for debugging', 'Update validation rules if needed'],
			},
			{
				name: 'RateLimitExceeded',
				pattern: /rate.*limit|too.*many.*requests|429/i,
				classification: {
					severity: 'medium',
					category: 'external_service',
					persistence: 'transient',
					recoverability: 'auto_recoverable',
					scope: 'service_specific',
					retryable: true,
					requiresImmediateAttention: false,
					escalationRequired: false,
				},
				description: 'Rate limit exceeded',
				commonCauses: ['Too many requests', 'API quota exceeded', 'Burst limit reached'],
				suggestedActions: ['Implement backoff strategy', 'Queue requests', 'Check rate limit headers'],
			},
			{
				name: 'AuthorizationError',
				pattern: /unauthorized|forbidden|401|403|auth.*failed/i,
				classification: {
					severity: 'high',
					category: 'authorization',
					persistence: 'permanent',
					recoverability: 'manual_recoverable',
					scope: 'user_specific',
					retryable: false,
					requiresImmediateAttention: true,
					escalationRequired: true,
				},
				description: 'Authorization/Authentication error',
				commonCauses: ['Invalid credentials', 'Expired token', 'Insufficient permissions'],
				suggestedActions: ['Refresh authentication', 'Check permissions', 'Alert security team if suspicious'],
			},
			{
				name: 'OutOfMemory',
				pattern: /out.*of.*memory|heap.*out.*of.*memory|ENOMEM/i,
				classification: {
					severity: 'critical',
					category: 'system',
					persistence: 'intermittent',
					recoverability: 'fatal',
					scope: 'system_wide',
					retryable: false,
					requiresImmediateAttention: true,
					escalationRequired: true,
				},
				description: 'Out of memory error',
				commonCauses: ['Memory leak', 'Insufficient resources', 'Large data processing'],
				suggestedActions: ['Restart service', 'Analyze memory usage', 'Scale resources', 'Fix memory leaks'],
			},
		];
	}

	classifyError(error: Error, metadata: Record<string, any> = {}): ErrorClassificationResultType
	{
		const errorString = `${error.name} ${error.message} ${error.stack || ''}`;

		for (const errorPattern of this.patterns)
		{
			const pattern = errorPattern.pattern;
			const matches = typeof pattern === 'string'
				? errorString.includes(pattern)
				: pattern.test(errorString);

			if (matches)
			{
				this.logger.debug({
					errorName: error.name,
					patternName: errorPattern.name,
					classification: errorPattern.classification,
				}, 'Error classified');

				return {
					classification: errorPattern.classification,
					confidence: 0.9,
					metadata: {
						...metadata,
						pattern: errorPattern.name,
						suggestedActions: errorPattern.suggestedActions,
					},
				};
			}
		}

		return this.getDefaultClassification(error, metadata);
	}

	private getDefaultClassification(error: Error, metadata: Record<string, any>): ErrorClassificationResultType
	{
		this.logger.debug({
			errorName: error.name,
			errorMessage: error.message,
		}, 'Using default error classification');

		return {
			classification: {
				severity: 'medium',
				category: 'business_logic',
				persistence: 'permanent',
				recoverability: 'manual_recoverable',
				scope: 'service_specific',
				retryable: false,
				requiresImmediateAttention: false,
				escalationRequired: false,
			},
			confidence: 0.5,
			metadata,
		};
	}

	addPattern(pattern: ErrorPatternType): void
	{
		this.patterns.push(pattern);
	}

	getPatterns(): ErrorPatternType[]
	{
		return [...this.patterns];
	}
}

export default ErrorClassificationService;
