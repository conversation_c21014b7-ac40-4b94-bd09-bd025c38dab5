import ScyllaClient from './ScyllaClient';
import Maria<PERSON><PERSON> from './MariaClient';
import RedisClientWrapper from './RedisClient';
import ManticoreClient from './ManticoreClient';
import { logger } from '../utils/Logger';
import { AsyncUtils } from '../utils/AsyncUtils';

/**
 * Central database manager for all database connections
 */
class DatabaseManager
{
	private static instance: DatabaseManager | null = null;

	private static initializing: Promise<DatabaseManager> | null = null;

	private _scylla: ScyllaClient | null = null;

	private _maria: MariaClient | null = null;

	private _redis: RedisClientWrapper | null = null;

	private _manticore: ManticoreClient | null = null;

	private _ready = false;

	private logger = logger.getLogger('DatabaseManager');

	/**
		* Initialize all database connections
		*/
	async initialize(): Promise<void>
	{
		try
		{
			this.logger.info('Initializing database connections...');

			// Initialize ScyllaDB connection
			this._scylla = new ScyllaClient();
			await this._scylla.connect();

			// Initialize MariaDB connection
			this._maria = new MariaClient();
			await this._maria.connect();

			// Initialize Redis connection
			this._redis = new RedisClientWrapper();
			await this._redis.connect();

			// Initialize Manticore Search connection
			this._manticore = new ManticoreClient();
			await this._manticore.connect();

			this._ready = true;
			this.logger.info('All database connections initialized successfully');
		}
		catch (error)
		{
			this.logger.error({ error }, 'Failed to initialize database connections');
			throw error;
		}
	}

	/**
	 * Close all database connections
	 */
	async close(): Promise<void>
	{
		try
		{
			this.logger.info('Closing database connections...');

			if (this._scylla)
			{
				await this._scylla.disconnect();
			}

			if (this._maria)
			{
				await this._maria.disconnect();
			}

			if (this._redis)
			{
				await this._redis.disconnect();
			}

			if (this._manticore)
			{
				await this._manticore.disconnect();
			}

			this._ready = false;
			this.logger.info('All database connections closed');
		}
		catch (error)
		{
			this.logger.error({ error }, 'Error closing database connections');
			throw error;
		}
	}

	/**
		* Get ScyllaDB client
		*/
	getScyllaClient(): ScyllaClient
	{
		if (!this._scylla)
		{
			throw new Error('ScyllaDB client not initialized');
		}

		return this._scylla;
	}

	/**
	 * Get MariaDB client
	 */
	getMariaClient(): MariaClient
	{
		if (!this._maria)
		{
			throw new Error('MariaDB client not initialized');
		}

		return this._maria;
	}

	/**
	 * Get Redis client
	 */
	getRedisClient(): RedisClientWrapper
	{
		if (!this._redis)
		{
			throw new Error('Redis client not initialized');
		}

		return this._redis;
	}

	/**
	 * Get Manticore Search client
	 */
	getManticoreClient(): ManticoreClient
	{
		if (!this._manticore)
		{
			throw new Error('Manticore client not initialized');
		}

		return this._manticore;
	}

	/**
	 * Health check for all database connections
	 */
	async healthCheck(): Promise<{
		scylla: boolean;
		maria: boolean;
		redis: boolean;
		manticore: boolean;
	}>
	{
		const health = {
			scylla: false,
			maria: false,
			redis: false,
			manticore: false,
		};

		try
		{
			if (this._scylla)
			{
				health.scylla = await this._scylla.healthCheck();
			}

			if (this._maria)
			{
				health.maria = await this._maria.healthCheck();
			}

			if (this._redis)
			{
				health.redis = await this._redis.healthCheck();
			}

			if (this._manticore)
			{
				health.manticore = await this._manticore.healthCheck();
			}
		}
		catch (error)
		{
			this.logger.error({ error }, 'Health check failed');
		}

		return health;
	}

	/**
	 * Admin-style alias returning detailed health info
	 */
	async getHealthStatus(): Promise<{
		scylla: boolean;
		maria: boolean;
		redis: boolean;
		manticore: boolean;
	}>
	{
		return this.healthCheck();
	}

	// Admin-style API: properties and helpers
	get scylla(): ScyllaClient
	{
		return this.getScyllaClient();
	}

	get mariadb(): MariaClient
	{
		return this.getMariaClient();
	}

	get redis(): RedisClientWrapper
	{
		return this.getRedisClient();
	}

	get manticore(): ManticoreClient
	{
		return this.getManticoreClient();
	}

	get manticoreUrl(): string
	{
		return this.getManticoreClient().getBaseUrl();
	}

	isReady(): boolean
	{
		return this._ready;
	}

	static async getInstance(): Promise<DatabaseManager>
	{
		if (DatabaseManager.instance !== null && DatabaseManager.instance.isReady() === true)
		{
			return DatabaseManager.instance;
		}

		if (DatabaseManager.initializing !== null)
		{
			return DatabaseManager.initializing;
		}

		const manager = new DatabaseManager();
		DatabaseManager.initializing = manager.initialize()
			.then(() =>
			{
				DatabaseManager.instance = manager;
				DatabaseManager.initializing = null;
				return manager;
			})
			.catch((error) =>
			{
				DatabaseManager.initializing = null;
				logger.getLogger('DatabaseManager').error({ error }, 'DatabaseManager initialization failed in getInstance');
				throw error;
			});

		return DatabaseManager.initializing;
	}

	// Admin metrics placeholders for backwards compatibility
	async getConnectionPoolStats(dbName: string): Promise<{
		active: number;
		idle: number;
		total: number;
		maxConnections: number;
	}>
	{
		const mockStats = {
			scylla: { active: 15, idle: 35, total: 50, maxConnections: 100 },
			mariadb: { active: 8, idle: 12, total: 20, maxConnections: 50 },
			redis: { active: 5, idle: 15, total: 20, maxConnections: 100 },
			manticore: { active: 3, idle: 7, total: 10, maxConnections: 25 },
		} as const;
		return (mockStats as Record<string, { active: number; idle: number; total: number; maxConnections: number }>)[dbName]
			|| { active: 0, idle: 0, total: 0, maxConnections: 0 };
	}

	async getPerformanceStats(dbName: string): Promise<{
		queryTime: number;
		slowQueries: number;
		errorRate: number;
		throughput: number;
	}>
	{
		const mockStats = {
			scylla: { queryTime: 12.5, slowQueries: 3, errorRate: 0.2, throughput: 1250 },
			mariadb: { queryTime: 8.3, slowQueries: 1, errorRate: 0.1, throughput: 890 },
			redis: { queryTime: 1.2, slowQueries: 0, errorRate: 0.05, throughput: 5600 },
			manticore: { queryTime: 15.7, slowQueries: 2, errorRate: 0.3, throughput: 340 },
		} as const;
		return (mockStats as Record<string, { queryTime: number; slowQueries: number; errorRate: number; throughput: number }>)[dbName]
			|| { queryTime: 0, slowQueries: 0, errorRate: 0, throughput: 0 };
	}

	async getStorageStats(dbName: string): Promise<{
		used: number;
		available: number;
		total: number;
		growth: number;
	}>
	{
		const mockStats = {
			scylla: { used: 45_000_000_000, available: 155_000_000_000, total: 200_000_000_000, growth: 2.3 },
			mariadb: { used: 12_000_000_000, available: 38_000_000_000, total: 50_000_000_000, growth: 1.8 },
			redis: { used: 2_000_000_000, available: 6_000_000_000, total: 8_000_000_000, growth: 0.5 },
			manticore: { used: 8_000_000_000, available: 17_000_000_000, total: 25_000_000_000, growth: 1.2 },
		} as const;
		return (mockStats as Record<string, { used: number; available: number; total: number; growth: number }>)[dbName]
			|| { used: 0, available: 0, total: 0, growth: 0 };
	}

	async getIndexStats(dbName: string): Promise<{
		efficiency: number;
		usage: number;
		size: number;
	}>
	{
		const mockStats = {
			scylla: { efficiency: 87.5, usage: 92.1, size: 5_600_000_000 },
			mariadb: { efficiency: 91.2, usage: 88.7, size: 1_200_000_000 },
			redis: { efficiency: 95.8, usage: 76.3, size: 450_000_000 },
			manticore: { efficiency: 83.4, usage: 94.2, size: 890_000_000 },
		} as const;
		return (mockStats as Record<string, { efficiency: number; usage: number; size: number }>)[dbName]
			|| { efficiency: 0, usage: 0, size: 0 };
	}

	/**
	 * Shutdown alias for close() method
	 */
	async shutdown(): Promise<void>
	{
		return this.close();
	}

	/**
	 * Alias for healthCheck() method
	 */
	async checkHealth(): Promise<{
		scylla: boolean;
		maria: boolean;
		redis: boolean;
		manticore: boolean;
	}>
	{
		return this.healthCheck();
	}

	/**
	 * Execute operation with retry logic
	 */
	async executeWithRetry<T>(operation: () => Promise<T>, maxRetries: number = 3): Promise<T>
	{
		let lastError: Error;

		for (let attempt = 1; attempt <= maxRetries; attempt++)
		{
			try
			{
				return await operation();
			}
			catch (error)
			{
				lastError = error as Error;
				this.logger.warn(`Operation failed on attempt ${attempt}/${maxRetries}: ${lastError.message}`);

				if (attempt === maxRetries)
				{
					break;
				}

				await AsyncUtils.sleep(Math.pow(2, attempt) * 1000);
			}
		}

		throw lastError!;
	}

	/**
	 * Sync domain data placeholder method
	 */
	async syncDomainData(domainData: unknown): Promise<void>
	{
		this.logger.info('Syncing domain data...');

		if (this._scylla)
		{
			// Mock implementation for testing
			this.logger.info('Domain data synced to ScyllaDB');
		}

		if (this._maria)
		{
			// Mock implementation for testing
			this.logger.info('Domain data synced to MariaDB');
		}
	}
}

export default DatabaseManager;
