
import {
	createClient,
	RedisClientType,
	RedisDefaultModules,
	RedisFunctions,
	RedisModules,
	RedisScripts,
} from 'redis';
import { config } from '../utils/Config';
import { logger } from '../utils/Logger';
import Constants from '../constants/Constants';

type RedisClient = RedisClientType<
	RedisDefaultModules & RedisModules,
	RedisFunctions,
	RedisScripts
>;

/**
 * Redis client wrapper with connection management and caching utilities
 */
class RedisClientWrapper
{
	private client: RedisClient | null = null;

	private logger = logger.getLogger('RedisClient');

	private isConnected = false;

	/**
	 * Check if client is ready/connected
	 */
	isReady(): boolean
	{
		return this.isConnected && this.client !== null;
	}

	/**
	 * Get Redis server INFO, optionally for a specific section
	 */
	async info(section?: string): Promise<string>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			if (section && section.length > 0)
			{
				return await this.client.sendCommand(['INFO', section]) as string;
			}
			return await this.client.sendCommand(['INFO']) as string;
		}
		catch (error)
		{
			this.logger.error({ error }, 'Redis INFO failed');
			throw error;
		}
	}

	/**
	 * Trigger a background save operation
	 */
	async bgsave(): Promise<string>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			return await this.client.sendCommand(['BGSAVE']) as string;
		}
		catch (error)
		{
			this.logger.error({ error }, 'Redis BGSAVE failed');
			throw error;
		}
	}

	/**
   * Connect to Redis
   */
	async connect(): Promise<void>
	{
		try
		{
			const allConfig = config.getAll();

			this.client = createClient({
				socket: {
					host: allConfig.REDIS_HOST,
					port: allConfig.REDIS_PORT,
					connectTimeout: 10000,
				},
				database: allConfig.REDIS_DB,
				password: allConfig.REDIS_PASSWORD || undefined,
			});

			this.client.on('error', (err: Error) =>
			{
				this.logger.error({ error: err }, 'Redis client error');
				this.isConnected = false;
			});

			this.client.on('connect', () =>
			{
				this.logger.info('Connected to Redis');
			});

			this.client.on('ready', () =>
			{
				this.isConnected = true;
				this.logger.info('Redis client ready');
			});

			this.client.on('end', () =>
			{
				this.isConnected = false;
				this.logger.info('Redis connection ended');
			});

			this.client.on('reconnecting', () =>
			{
				this.logger.info('Redis client reconnecting...');
			});

			await this.client.connect();
		}
		catch (error)
		{
			this.logger.error({ error }, 'Failed to connect to Redis');
			throw error;
		}
	}

	/**
   * Disconnect from Redis
   */
	async disconnect(): Promise<void>
	{
		if (this.client)
		{
			await this.client.quit();
			this.isConnected = false;
			this.client = null;
			this.logger.info('Disconnected from Redis');
		}
	}

	/**
   * Set key-value pair with optional expiration
   */
	async set<T>(key: string, value: T, ttl?: number): Promise<void>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const serializedValue = JSON.stringify(value);
			if (ttl)
			{
				await this.client.set(key, serializedValue, { EX: ttl });
			}
			else
			{
				await this.client.set(key, serializedValue);
			}
		}
		catch (error)
		{
			this.logger.error({ error }, 'Redis SET failed');
			throw error;
		}
	}

	/**
   * Get value by key
   */
	async get<T>(key: string): Promise<T | null>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const value = await this.client.get(key);
			return value ? JSON.parse(value) : null;
		}
		catch (error)
		{
			this.logger.error({ error }, 'Redis GET failed');
			throw error;
		}
	}

	/**
   * Get multiple values by keys
   */
	async mget<T>(keys: string[]): Promise<(T | null)[]>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const values = await this.client.mget(keys);
			if (!values || !Array.isArray(values))
			{
				return keys.map(() => null);
			}

			return (values as (string | null)[])
				.map((value: string | null) => (value ? JSON.parse(value) : null));
		}
		catch (error)
		{
			this.logger.error({ error }, 'Redis MGET failed');
			throw error;
		}
	}

	/**
   * Set multiple key-value pairs
   */
	async mset<T>(keyValuePairs: Record<string, T>, ttl?: number): Promise<void>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const serializedPairs: Record<string, string> = {};
			for (const [key, value] of Object.entries(keyValuePairs))
			{
				serializedPairs[key] = JSON.stringify(value);
			}

			await this.client.mset(serializedPairs);

			if (ttl)
			{
				const pipeline = this.client.multi();
				for (const key of Object.keys(keyValuePairs))
				{
					pipeline.expire(key, ttl);
				}
				await pipeline.exec();
			}
		}
		catch (error)
		{
			this.logger.error({ error }, 'Redis MSET failed');
			throw error;
		}
	}

	/**
   * Delete key(s)
   */
	async del(keys: string | string[]): Promise<number>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const keyArray = Array.isArray(keys) ? keys : [keys];
			return await this.client.del(keyArray);
		}
		catch (error)
		{
			this.logger.error({ error }, 'Redis DEL failed');
			throw error;
		}
	}

	/**
   * Check if key exists
   */
	async exists(key: string): Promise<boolean>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const result = await this.client.exists(key);
			return result === 1;
		}
		catch (error)
		{
			this.logger.error({ error }, 'Redis EXISTS failed');
			throw error;
		}
	}

	/**
   * Set expiration for key
   */
	async expire(key: string, ttl: number): Promise<boolean>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const result = await this.client.expire(key, ttl);
			return Boolean(result);
		}
		catch (error)
		{
			this.logger.error({ error }, 'Redis EXPIRE failed');
			throw error;
		}
	}

	/**
   * Get time to live for key
   */
	async ttl(key: string): Promise<number>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			return await this.client.ttl(key);
		}
		catch (error)
		{
			this.logger.error({ error }, 'Redis TTL failed');
			throw error;
		}
	}

	/**
   * Increment value by amount
   */
	async incr(key: string, amount: number = 1): Promise<number>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			if (amount === 1)
			{
				return await this.client.incr(key) as number;
			}
			return await this.client.incrby(key, amount) as number;
		}
		catch (error)
		{
			this.logger.error({ error }, 'Redis INCR failed');
			throw error;
		}
	}

	/**
   * Decrement value by amount
   */
	async decr(key: string, amount: number = 1): Promise<number>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			if (amount === 1)
			{
				return await this.client.decr(key) as number;
			}
			return await this.client.decrby(key, amount) as number;
		}
		catch (error)
		{
			this.logger.error({ error }, 'Redis DECR failed');
			throw error;
		}
	}

	/**
   * Add members to set
   */
	async sadd(key: string, members: string | string[]): Promise<number>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const memberArray = Array.isArray(members) ? members : [members];
			return await this.client.sAdd(key, memberArray);
		}
		catch (error)
		{
			this.logger.error('Redis SADD failed:', error);
			throw error;
		}
	}

	/**
   * Get all members of set
   */
	async smembers(key: string): Promise<string[]>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			return await this.client.smembers(key) as string[];
		}
		catch (error)
		{
			this.logger.error({ error }, 'Redis SMEMBERS failed');
			throw error;
		}
	}

	/**
   * Remove members from set
   */
	async srem(key: string, members: string | string[]): Promise<number>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const memberArray = Array.isArray(members) ? members : [members];
			const result = await this.client.srem(key, ...memberArray);
			return result as number;
		}
		catch (error)
		{
			this.logger.error({ error }, 'Redis SREM failed');
			throw error;
		}
	}

	/**
   * Push to list (left)
   */
	async lpush(key: string, values: string | string[]): Promise<number>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const valueArray = Array.isArray(values) ? values : [values];
			const result = await this.client.lpush(key, ...valueArray);
			return result as number;
		}
		catch (error)
		{
			this.logger.error({ error }, 'Redis LPUSH failed');
			throw error;
		}
	}

	/**
   * Pop from list (right)
   */
	async rpop(key: string): Promise<string | null>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const result = await this.client.rpop(key);
			return result as string | null;
		}
		catch (error)
		{
			this.logger.error({ error }, 'Redis RPOP failed');
			throw error;
		}
	}

	/**
	 * Get list length
	 */
	async llen(key: string): Promise<number>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const result = await this.client.llen(key);
			return result as number;
		}
		catch (error)
		{
			this.logger.error({ error }, 'Redis LLEN failed');
			throw error;
		}
	}

	/**
	 * Trim list to specified range
	 */
	async ltrim(key: string, start: number, stop: number): Promise<string>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const result = await this.client.ltrim(key, start, stop);
			return result as string;
		}
		catch (error)
		{
			this.logger.error({ error }, 'Redis LTRIM failed');
			throw error;
		}
	}

	/**
	 * Get range of elements from list
	 */
	async lrange(key: string, start: number, stop: number): Promise<string[]>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const result = await this.client.lrange(key, start, stop);
			return result as string[];
		}
		catch (error)
		{
			this.logger.error({ error }, 'Redis LRANGE failed');
			throw error;
		}
	}

	/**
   * Health check
   */
	async healthCheck(): Promise<boolean>
	{
		try
		{
			if (!this.client) return false;
			const result = await this.client.ping();
			return result === 'PONG';
		}
		catch (error)
		{
			this.logger.error({ error }, 'Redis health check failed');
			return false;
		}
	}


	/**
   * Generate cache key from template
   */
	generateCacheKey(template: string, params: Record<string, string | number>): string
	{
		let key = template;
		for (const [param, value] of Object.entries(params))
		{
			key = key.replace(`{${param}}`, String(value));
		}
		return key;
	}

	/**
   * Cache with automatic serialization and TTL
   */
	async cache<T>(key: string, fetchFunction: () => Promise<T>, ttl: number = 3600): Promise<T>
	{
		try
		{
			// Try to get from cache first
			const cached = await this.get<T>(key);
			if (cached !== null)
			{
				return cached;
			}

			// Fetch fresh data
			const data = await fetchFunction();

			// Store in cache
			await this.set(key, data, ttl);

			return data;
		}
		catch (error)
		{
			this.logger.error({ error }, 'Cache operation failed');
			// Fallback to direct fetch if cache fails
			return await fetchFunction();
		}
	}

	/**
   * Set key-value pair with expiration (alias for set with ttl)
   */
	async setex<T>(key: string, ttl: number, value: T): Promise<void>
	{
		return this.set(key, value, ttl);
	}

	/**
   * Get keys matching pattern
   */
	async keys(pattern: string): Promise<string[]>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			return await this.client.keys(pattern);
		}
		catch (error)
		{
			this.logger.error({ error }, 'Redis KEYS failed');
			throw error;
		}
	}

	/**
   * Invalidate cache pattern
   */
	async invalidatePattern(pattern: string): Promise<number>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const keys = await this.keys(pattern);
			if (keys.length === 0) return 0;

			// Use wrapper del so tests spying on del can observe the call
			return await this.del(keys);
		}
		catch (error)
		{
			this.logger.error({ error }, 'Redis pattern invalidation failed');
			throw error;
		}
	}

	/**
   * Get raw Redis client for advanced operations
   */
	getClient(): RedisClient | null
	{
		return this.client;
	}

	/**
   * Check if client is connected
   */
	isClientConnected(): boolean
	{
		return this.isConnected;
	}

	/**
   * Ping Redis server
   */
	async ping(): Promise<string>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			return await this.client.ping();
		}
		catch (error)
		{
			this.logger.error({ error }, 'Redis PING failed');
			throw error;
		}
	}

	/**
	 * Get the length of a stream
	 */
	async xlen(key: string): Promise<number>
	{
		if (!this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const result = await this.client.xlen(key);
			return typeof result === 'number' ? result : 0;
		}
		catch (error)
		{
			this.logger.error({ error }, 'Redis XLEN failed');
			throw error;
		}
	}

	/**
	 * Add a message to a stream
	 */
	async xadd(
		key: string,
		id: string,
		message: Record<string, string>,
		options?: { NOMKSTREAM?: boolean; MAXLEN?: { threshold: number; strategy?: 'APPROX' | 'EXACT' }; MINID?: string; LIMIT?: number },
	): Promise<string>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const args: string[] = [key, id];

			// Add message fields
			Object.entries(message).forEach(([field, value]) =>
			{
				args.push(field, value);
			});

			if (options?.NOMKSTREAM) args.push('NOMKSTREAM');
			if (options?.MAXLEN?.threshold !== undefined)
			{
				args.push('MAXLEN');
				if (options.MAXLEN.strategy === 'APPROX') args.push('~');
				args.push(options.MAXLEN.threshold.toString());
			}
			if (options?.MINID !== undefined) args.push('MINID', options.MINID);
			if (options?.LIMIT !== undefined) args.push('LIMIT', options.LIMIT.toString());

			return await this.client.sendCommand(['XADD', ...args]) as string;
		}
		catch (error)
		{
			this.logger.error({ error }, 'Redis XADD failed');
			throw error;
		}
	}

	/**
	 * Read messages from a stream
	 */
	async xread(
		streams: { key: string; id: string }[],
		options?: { COUNT?: number; BLOCK?: number; NOACK?: boolean },
	): Promise<{
		key: string;
		messages: { id: string; message: Record<string, string> }[];
	}[] | null>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const args: string[] = [];

			if (options?.COUNT !== undefined) args.push('COUNT', options.COUNT.toString());
			if (options?.BLOCK !== undefined) args.push('BLOCK', options.BLOCK.toString());
			if (options?.NOACK) args.push('NOACK');

			args.push('STREAMS');

			// Add stream keys and IDs
			streams.forEach(stream => args.push(stream.key));
			streams.forEach(stream => args.push(stream.id));

			const result = await this.client.sendCommand(['XREAD', ...args]);
			return (result as unknown) as {
				key: string;
				messages: { id: string; message: Record<string, string> }[];
			}[] | null;
		}
		catch (error)
		{
			this.logger.error({ error }, 'Redis XREAD failed');
			throw error;
		}
	}

	/**
	 * Create a consumer group for a stream
	 */
	async xgroupCreate(
		key: string,
		group: string,
		id: string,
		options?: { MKSTREAM?: boolean },
	): Promise<string>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const args: string[] = ['CREATE', key, group, id];
			if (options?.MKSTREAM) args.push('MKSTREAM');

			return await this.client.sendCommand(['XGROUP', ...args]);
		}
		catch (error)
		{
			this.logger.error({ error }, 'Redis XGROUP CREATE failed');
			throw error;
		}
	}

	/**
	 * Read messages from a stream using a consumer group
	 */
	async xreadgroup(
		group: string,
		consumer: string,
		streams: { key: string; id: string }[],
		options?: { COUNT?: number; BLOCK?: number; NOACK?: boolean },
	): Promise<{
		key: string;
		messages: { id: string; message: Record<string, string> }[];
	}[] | null>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const args: string[] = [group, consumer];

			if (options?.COUNT !== undefined) args.push('COUNT', options.COUNT.toString());
			if (options?.BLOCK !== undefined) args.push('BLOCK', options.BLOCK.toString());
			if (options?.NOACK) args.push('NOACK');

			args.push('STREAMS');

			// Add stream keys and IDs
			streams.forEach(stream => args.push(stream.key));
			streams.forEach(stream => args.push(stream.id));

			const result = await this.client.sendCommand(['XREADGROUP', ...args]);
			return (result as unknown) as {
				key: string;
				messages: { id: string; message: Record<string, string> }[];
			}[] | null;
		}
		catch (error)
		{
			this.logger.error({ error }, 'Redis XREADGROUP failed');
			throw error;
		}
	}

	/**
	 * Acknowledge a message in a consumer group
	 */
	async xack(key: string, group: string, id: string | string[]): Promise<number>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const ids = Array.isArray(id) ? id : [id];
			return await this.client.xAck(key, group, ids);
		}
		catch (error)
		{
			this.logger.error({ error }, 'Redis XACK failed');
			throw error;
		}
	}

	/**
	 * Get pending messages in a consumer group
	 */
	async xpending(
		key: string,
		group: string,
	): Promise<{
		pending: number;
		firstId: string | null;
		lastId: string | null;
		consumers: {
			name: string;
			deliveriesCounter: number;
		}[] | null;
	}>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const result = await this.client.xPending(key, group);
			return result as {
				pending: number;
				firstId: string | null;
				lastId: string | null;
				consumers: { name: string; deliveriesCounter: number; }[] | null;
			};
		}
		catch (error)
		{
			this.logger.error({ error }, 'Redis XPENDING failed');
			throw error;
		}
	}

	/**
	 * Get pending messages in consumer group with range
	 */
	async xpendingRange(
		key: string,
		group: string,
		start: string,
		end: string,
		count: number,
		consumer?: string,
	): Promise<{
		id: string;
		consumer: string;
		millisecondsSinceLastDelivery: number;
		deliveryCount: number;
	}[]>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const args: string[] = [key, group, start, end, count.toString()];
			if (consumer) args.push(consumer);

			const result = await this.client.sendCommand(['XPENDING', ...args]);
			// Transform the result to match our expected interface
			// Redis XPENDING returns an array of arrays: [id, consumer, millisecondsSinceLastDelivery, deliveryCount]
			return (result as unknown as Array<[string, string, number, number]>).map(item => ({
				id: item[0],
				consumer: item[1],
				millisecondsSinceLastDelivery: item[2],
				deliveryCount: item[3],
			}));
		}
		catch (error)
		{
			this.logger.error({ error }, 'Redis XPENDINGRANGE failed');
			throw error;
		}
	}

	/**
	 * Claim ownership of messages in a consumer group
	 */
	async xclaim(
		key: string,
		group: string,
		consumer: string,
		minIdleTime: number,
		id: string | string[],
		options?: {
			IDLE?: number;
			TIME?: number;
			RETRYCOUNT?: number;
			FORCE?: boolean;
			JUSTID?: boolean;
		},
	): Promise<{ id: string; message: Record<string, string> }[]>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const args: string[] = [key, group, consumer, minIdleTime.toString()];
			const ids = Array.isArray(id) ? id : [id];
			args.push(...ids);

			if (options?.IDLE !== undefined) args.push('IDLE', options.IDLE.toString());
			if (options?.TIME !== undefined) args.push('TIME', options.TIME.toString());
			if (options?.RETRYCOUNT !== undefined) args.push('RETRYCOUNT', options.RETRYCOUNT.toString());
			if (options?.FORCE) args.push('FORCE');
			if (options?.JUSTID) args.push('JUSTID');

			const result = await this.client.sendCommand(['XCLAIM', ...args]);
			// Transform the raw Redis response to our expected format
			const transformedResult = (result as unknown as [string, string[]][]).map(item => ({
				id: item[0],
				message: item[1] ? Object.fromEntries(
					item[1].reduce(
						(acc: [string, string][], curr: string, index: number) =>
						{
							if (index % 2 === 0)
							{
								acc.push([curr, item[1][index + 1]]);
							}
							return acc;
						},
						[]
					)
				) : {},
			}));
			return transformedResult;
		}
		catch (error)
		{
			this.logger.error({ error }, 'Redis XCLAIM failed');
			throw error;
		}
	}

	/**
	 * Read messages from a stream
	 */
	async xrange(
		key: string,
		start: string,
		end: string,
		options?: { COUNT?: number },
	): Promise<{ id: string; message: Record<string, string> }[]>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const result = await this.client.xRange(key, start, end, options);
			return result as { id: string; message: Record<string, string> }[];
		}
		catch (error)
		{
			this.logger.error({ error }, 'Redis XRANGE failed');
			throw error;
		}
	}

	/**
	 * Read messages from a stream in reverse order
	 */
	async xrevrange(
		key: string,
		start: string,
		end: string,
		options?: { COUNT?: number },
	): Promise<{ id: string; message: Record<string, string> }[]>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const result = await this.client.xRevRange(key, start, end, options);
			return result as { id: string; message: Record<string, string> }[];
		}
		catch (error)
		{
			this.logger.error({ error }, 'Redis XREVRANGE failed');
			throw error;
		}
	}

	/**
	 * Delete messages from a stream
	 */
	async xdel(key: string, id: string | string[]): Promise<number>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const ids = Array.isArray(id) ? id : [id];
			const result = await this.client.xdel(key, ids);
			return typeof result === 'number' ? result : 0;
		}
		catch (error)
		{
			this.logger.error({ error }, 'Redis XDEL failed');
			throw error;
		}
	}

	/**
	 * Set if not exists (atomic operation)
	 */
	async setnx(key: string, value: string): Promise<boolean>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const result = await this.client.setNX(key, value);
			return Boolean(result);
		}
		catch (error)
		{
			this.logger.error({ error }, 'Redis SETNX failed');
			throw error;
		}
	}

	/**
	 * Execute Lua script
	 */
	async eval(script: string, numKeys: number, ...args: string[]): Promise<any>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			return await this.client.eval(script, {
				keys: args.slice(0, numKeys),
				arguments: args.slice(numKeys),
			});
		}
		catch (error)
		{
			this.logger.error({ error }, 'Redis EVAL failed');
			throw error;
		}
	}

	/**
	 * Get multiple hash fields
	 */
	async hmget(key: string, ...fields: string[]): Promise<(string | null)[]>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			return await this.client.hmGet(key, fields);
		}
		catch (error)
		{
			this.logger.error({ error }, 'Redis HMGET failed');
			throw error;
		}
	}

	/**
	 * Set multiple hash fields
	 */
	async hmset(key: string, ...args: string[]): Promise<number>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const hash: Record<string, string> = {};
			for (let i = 0; i < args.length; i += 2)
			{
				hash[args[i]] = args[i + 1];
			}
			return await this.client.hSet(key, hash);
		}
		catch (error)
		{
			this.logger.error({ error }, 'Redis HMSET failed');
			throw error;
		}
	}

	/**
	 * Create a Redis pipeline for batch operations
	 */
	pipeline(): any
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		return this.client.multi();
	}

	/**
	 * Get stream information
	 */
	async xinfo(subcommand: string, key: string): Promise<any>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			if (subcommand.toUpperCase() === 'STREAM')
			{
				return await this.client.xInfoStream(key);
			}
			if (subcommand.toUpperCase() === 'GROUPS')
			{
				return await this.client.xInfoGroups(key);
			}

			throw new Error(`Unsupported XINFO subcommand: ${subcommand}`);

		}
		catch (error)
		{
			this.logger.error({ error }, 'Redis XINFO failed');
			throw error;
		}
	}
}

export { CacheManager } from './CacheManager';

export default RedisClientWrapper;
