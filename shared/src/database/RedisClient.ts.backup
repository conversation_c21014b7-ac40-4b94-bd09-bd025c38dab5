/* eslint-disable max-classes-per-file */

import {
	createClient,
	RedisClientType,
	RedisDefaultModules,
	RedisFunctions,
	RedisModules,
	RedisScripts,
} from 'redis';
import { config } from '../utils/Config';
import { logger } from '../utils/Logger';
import Constants from '../constants/Constants';

type RedisClient = RedisClientType<
	RedisDefaultModules & RedisModules,
	RedisFunctions,
	RedisScripts
>;

/**
 * Redis client wrapper with connection management and caching utilities
 */
class RedisClientWrapper
{
	private client: RedisClient | null = null;

	private logger = logger.getLogger('RedisClient');

	private isConnected = false;

	/**
   * Connect to Redis
   */
	async connect(): Promise<void>
	{
		try
		{
			const allConfig = config.getAll();

			this.client = createClient({
				socket: {
					host: allConfig.REDIS_HOST,
					port: allConfig.REDIS_PORT,
					connectTimeout: 10000,
				},
				database: allConfig.REDIS_DB,
				password: allConfig.REDIS_PASSWORD || undefined,
			});

			this.client.on('error', (err) =>
			{
				this.logger.error('Redis client error:', err);
				this.isConnected = false;
			});

			this.client.on('connect', () =>
			{
				this.logger.info('Connected to Redis');
			});

			this.client.on('ready', () =>
			{
				this.isConnected = true;
				this.logger.info('Redis client ready');
			});

			this.client.on('end', () =>
			{
				this.isConnected = false;
				this.logger.info('Redis connection ended');
			});

			this.client.on('reconnecting', () =>
			{
				this.logger.info('Redis client reconnecting...');
			});

			await this.client.connect();
		}
		catch (error)
		{
			this.logger.error('Failed to connect to Redis:', error);
			throw error;
		}
	}

	/**
   * Disconnect from Redis
   */
	async disconnect(): Promise<void>
	{
		if (this.client)
		{
			await this.client.quit();
			this.isConnected = false;
			this.client = null;
			this.logger.info('Disconnected from Redis');
		}
	}

	/**
   * Set key-value pair with optional expiration
   */
	async set<T>(key: string, value: T, ttl?: number): Promise<void>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const serializedValue = JSON.stringify(value);
			if (ttl)
			{
				await this.client.setEx(key, ttl, serializedValue);
			}
			else
			{
				await this.client.set(key, serializedValue);
			}
		}
		catch (error)
		{
			this.logger.error('Redis SET failed:', error);
			throw error;
		}
	}

	/**
   * Get value by key
   */
	async get<T>(key: string): Promise<T | null>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const value = await this.client.get(key);
			return value ? JSON.parse(value) : null;
		}
		catch (error)
		{
			this.logger.error('Redis GET failed:', error);
			throw error;
		}
	}

	/**
   * Get multiple values by keys
   */
	async mget<T>(keys: string[]): Promise<(T | null)[]>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const values = await this.client.mGet(keys);
			return values.map(value => (value ? JSON.parse(value) : null));
		}
		catch (error)
		{
			this.logger.error('Redis MGET failed:', error);
			throw error;
		}
	}

	/**
   * Set multiple key-value pairs
   */
	async mset<T>(keyValuePairs: Record<string, T>, ttl?: number): Promise<void>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const serializedPairs: Record<string, string> = {};
			for (const [key, value] of Object.entries(keyValuePairs))
			{
				serializedPairs[key] = JSON.stringify(value);
			}

			await this.client.mSet(serializedPairs);

			if (ttl)
			{
				const pipeline = this.client.multi();
				for (const key of Object.keys(keyValuePairs))
				{
					pipeline.expire(key, ttl);
				}
				await pipeline.exec();
			}
		}
		catch (error)
		{
			this.logger.error('Redis MSET failed:', error);
			throw error;
		}
	}

	/**
   * Delete key(s)
   */
	async del(keys: string | string[]): Promise<number>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const keyArray = Array.isArray(keys) ? keys : [keys];
			return await this.client.del(keyArray);
		}
		catch (error)
		{
			this.logger.error('Redis DEL failed:', error);
			throw error;
		}
	}

	/**
   * Check if key exists
   */
	async exists(key: string): Promise<boolean>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const result = await this.client.exists(key);
			return result === 1;
		}
		catch (error)
		{
			this.logger.error('Redis EXISTS failed:', error);
			throw error;
		}
	}

	/**
   * Set expiration for key
   */
	async expire(key: string, ttl: number): Promise<boolean>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const result = await this.client.expire(key, ttl);
			return result;
		}
		catch (error)
		{
			this.logger.error('Redis EXPIRE failed:', error);
			throw error;
		}
	}

	/**
   * Get time to live for key
   */
	async ttl(key: string): Promise<number>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			return await this.client.ttl(key);
		}
		catch (error)
		{
			this.logger.error('Redis TTL failed:', error);
			throw error;
		}
	}

	/**
   * Increment value by amount
   */
	async incr(key: string, amount: number = 1): Promise<number>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			if (amount === 1)
			{
				return await this.client.incr(key);
			}
			return await this.client.incrBy(key, amount);
		}
		catch (error)
		{
			this.logger.error('Redis INCR failed:', error);
			throw error;
		}
	}

	/**
   * Decrement value by amount
   */
	async decr(key: string, amount: number = 1): Promise<number>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			if (amount === 1)
			{
				return await this.client.decr(key);
			}
			return await this.client.decrBy(key, amount);
		}
		catch (error)
		{
			this.logger.error('Redis DECR failed:', error);
			throw error;
		}
	}

	/**
   * Add members to set
   */
	async sadd(key: string, members: string | string[]): Promise<number>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const memberArray = Array.isArray(members) ? members : [members];
			return await this.client.sAdd(key, memberArray);
		}
		catch (error)
		{
			this.logger.error('Redis SADD failed:', error);
			throw error;
		}
	}

	/**
   * Get all members of set
   */
	async smembers(key: string): Promise<string[]>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			return await this.client.sMembers(key);
		}
		catch (error)
		{
			this.logger.error('Redis SMEMBERS failed:', error);
			throw error;
		}
	}

	/**
   * Remove members from set
   */
	async srem(key: string, members: string | string[]): Promise<number>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const memberArray = Array.isArray(members) ? members : [members];
			return await this.client.sRem(key, memberArray);
		}
		catch (error)
		{
			this.logger.error('Redis SREM failed:', error);
			throw error;
		}
	}

	/**
   * Push to list (left)
   */
	async lpush(key: string, values: string | string[]): Promise<number>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const valueArray = Array.isArray(values) ? values : [values];
			return await this.client.lPush(key, valueArray);
		}
		catch (error)
		{
			this.logger.error('Redis LPUSH failed:', error);
			throw error;
		}
	}

	/**
   * Pop from list (right)
   */
	async rpop(key: string): Promise<string | null>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			return await this.client.rPop(key);
		}
		catch (error)
		{
			this.logger.error('Redis RPOP failed:', error);
			throw error;
		}
	}

	/**
   * Get list length
   */
	async llen(key: string): Promise<number>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			return await this.client.lLen(key);
		}
		catch (error)
		{
			this.logger.error('Redis LLEN failed:', error);
			throw error;
		}
	}

	/**
   * Health check
   */
	async healthCheck(): Promise<boolean>
	{
		try
		{
			if (!this.client) return false;
			const result = await this.client.ping();
			return result === 'PONG';
		}
		catch (error)
		{
			this.logger.error('Redis health check failed:', error);
			return false;
		}
	}


	/**
   * Generate cache key from template
   */
	generateCacheKey(template: string, params: Record<string, string | number>): string
	{
		let key = template;
		for (const [param, value] of Object.entries(params))
		{
			key = key.replace(`{${param}}`, String(value));
		}
		return key;
	}

	/**
   * Cache with automatic serialization and TTL
   */
	async cache<T>(key: string, fetchFunction: () => Promise<T>, ttl: number = 3600): Promise<T>
	{
		try
		{
			// Try to get from cache first
			const cached = await this.get<T>(key);
			if (cached !== null)
			{
				return cached;
			}

			// Fetch fresh data
			const data = await fetchFunction();

			// Store in cache
			await this.set(key, data, ttl);

			return data;
		}
		catch (error)
		{
			this.logger.error('Cache operation failed:', error);
			// Fallback to direct fetch if cache fails
			return await fetchFunction();
		}
	}

	/**
   * Set key-value pair with expiration (alias for set with ttl)
   */
	async setex<T>(key: string, ttl: number, value: T): Promise<void>
	{
		return this.set(key, value, ttl);
	}

	/**
   * Get keys matching pattern
   */
	async keys(pattern: string): Promise<string[]>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			return await this.client.keys(pattern);
		}
		catch (error)
		{
			this.logger.error('Redis KEYS failed:', error);
			throw error;
		}
	}

	/**
   * Invalidate cache pattern
   */
	async invalidatePattern(pattern: string): Promise<number>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const keys = await this.keys(pattern);
			if (keys.length === 0) return 0;

			// Use wrapper del so tests spying on del can observe the call
			return await this.del(keys);
		}
		catch (error)
		{
			this.logger.error('Redis pattern invalidation failed:', error);
			throw error;
		}
	}

	/**
   * Get raw Redis client for advanced operations
   */
	getClient(): RedisClient | null
	{
		return this.client;
	}

	/**
   * Check if client is connected
   */
	isClientConnected(): boolean
	{
		return this.isConnected;
	}

	/**
   * Ping Redis server
   */
	async ping(): Promise<string>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			return await this.client.ping();
		}
		catch (error)
		{
			this.logger.error('Redis PING failed:', error);
			throw error;
		}
	}

	/**
	 * Get the length of a stream
	 */
	async xlen(key: string): Promise<number>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			return await this.client.xLen(key);
		}
		catch (error)
		{
			this.logger.error('Redis XLEN failed:', error);
			throw error;
		}
	}

	/**
	 * Add a message to a stream
	 */
	async xadd(key: string, id: string, message: Record<string, string>, options?: { NOMKSTREAM?: boolean; MAXLEN?: number; MINID?: string; LIMIT?: number }): Promise<string>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const args: any[] = [key];

			if (options?.NOMKSTREAM) args.push('NOMKSTREAM');
			if (options?.MAXLEN !== undefined) args.push('MAXLEN', '~', options.MAXLEN);
			if (options?.MINID !== undefined) args.push('MINID', options.MINID);
			if (options?.LIMIT !== undefined) args.push('LIMIT', options.LIMIT);

			args.push(id);

			// Add message fields
			Object.entries(message).forEach(([field, value]) =>
			{
				args.push(field, value);
			});

			return await this.client.xAdd(...args);
		}
		catch (error)
		{
			this.logger.error('Redis XADD failed:', error);
			throw error;
		}
	}

	/**
	 * Read messages from a stream
	 */
	async xread(streams: { key: string; id: string }[], options?: { COUNT?: number; BLOCK?: number; NOACK?: boolean }): Promise<{ key: string; messages: { id: string; message: Record<string, string> }[] }[] | null>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const args: any[] = [];

			if (options?.COUNT !== undefined) args.push('COUNT', options.COUNT);
			if (options?.BLOCK !== undefined) args.push('BLOCK', options.BLOCK);
			if (options?.NOACK) args.push('NOACK');

			args.push('STREAMS');

			// Add stream keys and IDs
			streams.forEach(stream => args.push(stream.key));
			streams.forEach(stream => args.push(stream.id));

			const result = await this.client.xRead(args);
			return result;
		}
		catch (error)
		{
			this.logger.error('Redis XREAD failed:', error);
			throw error;
		}
	}

	/**
	 * Create a consumer group
	 */
	async xgroupCreate(key: string, group: string, id: string, options?: { MKSTREAM?: boolean }): Promise<string>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const args: any[] = ['CREATE', key, group, id];
			if (options?.MKSTREAM) args.push('MKSTREAM');

			return await this.client.xGroup(args);
		}
		catch (error)
		{
			this.logger.error('Redis XGROUP CREATE failed:', error);
			throw error;
		}
	}

	/**
	 * Read messages from a stream using a consumer group
	 */
	async xreadgroup(group: string, consumer: string, streams: { key: string; id: string }[], options?: { COUNT?: number; BLOCK?: number; NOACK?: boolean }): Promise<{ key: string; messages: { id: string; message: Record<string, string> }[] }[] | null>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const args: any[] = [group, consumer];

			if (options?.COUNT !== undefined) args.push('COUNT', options.COUNT);
			if (options?.BLOCK !== undefined) args.push('BLOCK', options.BLOCK);
			if (options?.NOACK) args.push('NOACK');

			args.push('STREAMS');

			// Add stream keys and IDs
			streams.forEach(stream => args.push(stream.key));
			streams.forEach(stream => args.push(stream.id));

			const result = await this.client.xReadGroup(args);
			return result;
		}
		catch (error)
		{
			this.logger.error('Redis XREADGROUP failed:', error);
			throw error;
		}
	}

	/**
	 * Acknowledge a message in a consumer group
	 */
	async xack(key: string, group: string, id: string | string[]): Promise<number>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const ids = Array.isArray(id) ? id : [id];
			return await this.client.xAck(key, group, ...ids);
		}
		catch (error)
		{
			this.logger.error('Redis XACK failed:', error);
			throw error;
		}
	}

	/**
	 * Get pending messages in a consumer group
	 */
	async xpending(key: string, group: string): Promise<{ count: number; startId: string; endId: string; consumers: { name: string; count: number }[] }>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			return await this.client.xPending(key, group);
		}
		catch (error)
		{
			this.logger.error('Redis XPENDING failed:', error);
			throw error;
		}
	}

	/**
	 * Get pending messages in a consumer group with range
	 */
	async xpendingRange(key: string, group: string, start: string, end: string, count: number, consumer?: string): Promise<{ id: string; consumer: string; millisecondsSinceLastDelivery: number; deliveryCount: number }[]>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const args: any[] = [key, group, start, end, count];
			if (consumer) args.push(consumer);

			return await this.client.xPendingRange(args);
		}
		catch (error)
		{
			this.logger.error('Redis XPENDINGRANGE failed:', error);
			throw error;
		}
	}

	/**
	 * Claim ownership of messages in a consumer group
	 */
	async xclaim(key: string, group: string, consumer: string, minIdleTime: number, id: string | string[], options?: { IDLE?: number; TIME?: number; RETRYCOUNT?: number; FORCE?: boolean; JUSTID?: boolean }): Promise<{ id: string; message: Record<string, string> }[]>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const args: any[] = [key, group, consumer, minIdleTime];
			const ids = Array.isArray(id) ? id : [id];
			args.push(...ids);

			if (options?.IDLE !== undefined) args.push('IDLE', options.IDLE);
			if (options?.TIME !== undefined) args.push('TIME', options.TIME);
			if (options?.RETRYCOUNT !== undefined) args.push('RETRYCOUNT', options.RETRYCOUNT);
			if (options?.FORCE) args.push('FORCE');
			if (options?.JUSTID) args.push('JUSTID');

			return await this.client.xClaim(args);
		}
		catch (error)
		{
			this.logger.error('Redis XCLAIM failed:', error);
			throw error;
		}
	}

	/**
	 * Get messages in a range from a stream
	 */
	async xrange(key: string, start: string, end: string, options?: { COUNT?: number }): Promise<{ id: string; message: Record<string, string> }[]>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const args: any[] = [key, start, end];
			if (options?.COUNT !== undefined) args.push('COUNT', options.COUNT);

			return await this.client.xRange(args);
		}
		catch (error)
		{
			this.logger.error('Redis XRANGE failed:', error);
			throw error;
		}
	}

	/**
	 * Get messages in reverse range from a stream
	 */
	async xrevrange(key: string, start: string, end: string, options?: { COUNT?: number }): Promise<{ id: string; message: Record<string, string> }[]>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const args: any[] = [key, start, end];
			if (options?.COUNT !== undefined) args.push('COUNT', options.COUNT);

			return await this.client.xRevRange(args);
		}
		catch (error)
		{
			this.logger.error('Redis XREVRANGE failed:', error);
			throw error;
		}
	}

	/**
	 * Delete messages from a stream
	 */
	async xdel(key: string, id: string | string[]): Promise<number>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const ids = Array.isArray(id) ? id : [id];
			return await this.client.xDel(key, ...ids);
		}
		catch (error)
		{
			this.logger.error('Redis XDEL failed:', error);
			throw error;
		}
	}

	/**
	 * Set if not exists (atomic operation)
	 */
	async setnx(key: string, value: string): Promise<boolean>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('Redis client not connected');
		}

		try
		{
			const result = await this.client.setNX(key, value);
			return result;
		}
		catch (error)
		{
			this.logger.error('Redis SETNX failed:', error);
			throw error;
		}
	}
}

/**
 * Cache utility functions
 */
class CacheManager
{
	constructor(private redisClient: RedisClientWrapper) {}

	/**
   * Cache domain analysis data
   */
	async cacheDomainAnalysis(
		domain: string,
		data: Record<string, unknown>,
		ttl: number = 3600,
	): Promise<void>
	{
		const key = Constants.CACHE_KEYS.DOMAIN_ANALYSIS.replace('{domain}', domain);
		await this.redisClient.set(key, data, ttl);
	}

	/**
   * Get cached domain analysis
   */
	async getCachedDomainAnalysis(domain: string): Promise<Record<string, unknown> | null>
	{
		const key = Constants.CACHE_KEYS.DOMAIN_ANALYSIS.replace('{domain}', domain);
		return this.redisClient.get(key);
	}

	/**
   * Cache domain ranking
   */
	async cacheDomainRanking(
		domain: string,
		ranking: Record<string, unknown>,
		ttl: number = 1800,
	): Promise<void>
	{
		const key = Constants.CACHE_KEYS.DOMAIN_RANKING.replace('{domain}', domain);
		await this.redisClient.set(key, ranking, ttl);
	}

	/**
   * Get cached domain ranking
   */
	async getCachedDomainRanking(domain: string): Promise<Record<string, unknown> | null>
	{
		const key = Constants.CACHE_KEYS.DOMAIN_RANKING.replace('{domain}', domain);
		return this.redisClient.get(key);
	}

	/**
   * Cache global rankings
   */
	async cacheGlobalRankings(
		page: number,
		rankings: Array<Record<string, unknown>>,
		ttl: number = 900,
	): Promise<void>
	{
		const key = Constants.CACHE_KEYS.GLOBAL_RANKINGS.replace('{page}', page.toString());
		await this.redisClient.set(key, rankings, ttl);
	}

	/**
   * Get cached global rankings
   */
	async getCachedGlobalRankings(page: number): Promise<Array<Record<string, unknown>> | null>
	{
		const key = Constants.CACHE_KEYS.GLOBAL_RANKINGS.replace('{page}', page.toString());
		return this.redisClient.get(key);
	}

	/**
   * Cache category rankings
   */
	async cacheCategoryRankings(
		category: string,
		page: number,
		rankings: Array<Record<string, unknown>>,
		ttl: number = 900,
	): Promise<void>
	{
		const key = Constants.CACHE_KEYS.CATEGORY_RANKINGS
			.replace('{category}', category)
			.replace('{page}', page.toString());
		await this.redisClient.set(key, rankings, ttl);
	}

	/**
   * Get cached category rankings
   */
	async getCachedCategoryRankings(
		category: string,
		page: number,
	): Promise<Array<Record<string, unknown>> | null>
	{
		const key = Constants.CACHE_KEYS.CATEGORY_RANKINGS
			.replace('{category}', category)
			.replace('{page}', page.toString());
		return this.redisClient.get(key);
	}

	/**
   * Cache search results
   */
	async cacheSearchResults(
		queryHash: string,
		results: Record<string, unknown>,
		ttl: number = 600,
	): Promise<void>
	{
		const key = Constants.CACHE_KEYS.SEARCH_RESULTS.replace('{query_hash}', queryHash);
		await this.redisClient.set(key, results, ttl);
	}

	/**
   * Get cached search results
   */
	async getCachedSearchResults(queryHash: string): Promise<Record<string, unknown> | null>
	{
		const key = Constants.CACHE_KEYS.SEARCH_RESULTS.replace('{query_hash}', queryHash);
		return this.redisClient.get(key);
	}

	/**
   * Invalidate domain-related cache
   */
	async invalidateDomainCache(domain: string): Promise<void>
	{
		const patterns = [
			Constants.CACHE_KEYS.DOMAIN_ANALYSIS.replace('{domain}', domain),
			Constants.CACHE_KEYS.DOMAIN_RANKING.replace('{domain}', domain),
      `*${domain}*`, // Catch any other domain-related cache
		];

		for (const pattern of patterns)
		{

			await this.redisClient.invalidatePattern(pattern);
		}
	}

	/**
   * Invalidate rankings cache
   */
	async invalidateRankingsCache(): Promise<void>
	{
		const patterns = [
			Constants.CACHE_KEYS.GLOBAL_RANKINGS.replace('{page}', '*'),
			Constants.CACHE_KEYS.CATEGORY_RANKINGS.replace('{category}', '*').replace('{page}', '*'),
			Constants.CACHE_KEYS.TOP_DOMAINS.replace('{category}', '*').replace('{timeframe}', '*'),
		];

		for (const pattern of patterns)
		{

			await this.redisClient.invalidatePattern(pattern);
		}
	}

	/**
   * Generate hash for search query
   */
	generateSearchHash(query: string, filters: Record<string, unknown> = {}): string
	{
		const searchData = { query, filters };
		const searchString = JSON.stringify(searchData);

		// Simple hash function (in production, use crypto.createHash)
		let hash = 0;
		for (let i = 0; i < searchString.length; i++)
		{
			const char = searchString.charCodeAt(i);
			hash = (hash * 31 + char);
			if (hash > 0x7fffffff) hash -= 0x100000000; // clamp to 32-bit signed range
		}

		return Math.abs(hash).toString(36);
	}
}

export { CacheManager };

export default RedisClientWrapper;
