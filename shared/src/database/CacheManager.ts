import Constants from '../constants/Constants';
import type Redis<PERSON>lientWrapper from './RedisClient';

/**
 * Cache utility functions
 */
export class CacheManager
{
	constructor(private redisClient: RedisClientWrapper) {}

	/**
	 * Cache domain analysis data
	 */
	async cacheDomainAnalysis(
		domain: string,
		data: Record<string, unknown>,
		ttl: number = 3600,
	): Promise<void>
	{
		const key = Constants.CACHE_KEYS.DOMAIN_ANALYSIS.replace('{domain}', domain);
		await this.redisClient.set(key, data, ttl);
	}

	/**
	 * Get cached domain analysis
	 */
	async getCachedDomainAnalysis(domain: string): Promise<Record<string, unknown> | null>
	{
		const key = Constants.CACHE_KEYS.DOMAIN_ANALYSIS.replace('{domain}', domain);
		return this.redisClient.get(key);
	}

	/**
	 * Cache domain ranking
	 */
	async cacheDomainRanking(
		domain: string,
		ranking: Record<string, unknown>,
		ttl: number = 1800,
	): Promise<void>
	{
		const key = Constants.CACHE_KEYS.DOMAIN_RANKING.replace('{domain}', domain);
		await this.redisClient.set(key, ranking, ttl);
	}

	/**
	 * Get cached domain ranking
	 */
	async getCachedDomainRanking(domain: string): Promise<Record<string, unknown> | null>
	{
		const key = Constants.CACHE_KEYS.DOMAIN_RANKING.replace('{domain}', domain);
		return this.redisClient.get(key);
	}

	/**
	 * Cache global rankings
	 */
	async cacheGlobalRankings(
		page: number,
		rankings: Array<Record<string, unknown>>,
		ttl: number = 900,
	): Promise<void>
	{
		const key = Constants.CACHE_KEYS.GLOBAL_RANKINGS.replace('{page}', page.toString());
		await this.redisClient.set(key, rankings, ttl);
	}

	/**
	 * Get cached global rankings
	 */
	async getCachedGlobalRankings(page: number): Promise<Array<Record<string, unknown>> | null>
	{
		const key = Constants.CACHE_KEYS.GLOBAL_RANKINGS.replace('{page}', page.toString());
		return this.redisClient.get(key);
	}

	/**
	 * Cache category rankings
	 */
	async cacheCategoryRankings(
		category: string,
		page: number,
		rankings: Array<Record<string, unknown>>,
		ttl: number = 900,
	): Promise<void>
	{
		const key = Constants.CACHE_KEYS.CATEGORY_RANKINGS
			.replace('{category}', category)
			.replace('{page}', page.toString());
		await this.redisClient.set(key, rankings, ttl);
	}

	/**
	 * Get cached category rankings
	 */
	async getCachedCategoryRankings(
		category: string,
		page: number,
	): Promise<Array<Record<string, unknown>> | null>
	{
		const key = Constants.CACHE_KEYS.CATEGORY_RANKINGS
			.replace('{category}', category)
			.replace('{page}', page.toString());
		return this.redisClient.get(key);
	}

	/**
	 * Cache search results
	 */
	async cacheSearchResults(
		queryHash: string,
		results: Record<string, unknown>,
		ttl: number = 600,
	): Promise<void>
	{
		const key = Constants.CACHE_KEYS.SEARCH_RESULTS_HASH.replace('{queryHash}', queryHash);
		await this.redisClient.set(key, results, ttl);
	}

	/**
	 * Get cached search results
	 */
	async getCachedSearchResults(queryHash: string): Promise<Record<string, unknown> | null>
	{
		const key = Constants.CACHE_KEYS.SEARCH_RESULTS_HASH.replace('{queryHash}', queryHash);
		return this.redisClient.get(key);
	}

	/**
	 * Invalidate domain-related cache
	 */
	async invalidateDomainCache(domain: string): Promise<void>
	{
		const patterns = [
			Constants.CACHE_KEYS.DOMAIN_ANALYSIS.replace('{domain}', domain),
			Constants.CACHE_KEYS.DOMAIN_RANKING.replace('{domain}', domain),
			`*${domain}*`, // Catch any other domain-related cache
		];

		for (const pattern of patterns)
		{
			await this.redisClient.invalidatePattern(pattern);
		}
	}

	/**
	 * Invalidate rankings cache
	 */
	async invalidateRankingsCache(): Promise<void>
	{
		const patterns = [
			Constants.CACHE_KEYS.GLOBAL_RANKINGS.replace('{page}', '*'),
			Constants.CACHE_KEYS.CATEGORY_RANKINGS.replace('{category}', '*').replace('{page}', '*'),
			Constants.CACHE_KEYS.TOP_DOMAINS_TIMEFRAME.replace('{category}', '*').replace('{timeframe}', '*'),
		];

		for (const pattern of patterns)
		{
			await this.redisClient.invalidatePattern(pattern);
		}
	}

	/**
	 * Store domain analysis data (alias for cacheDomainAnalysis)
	 */
	async storeDomainAnalysis(
		domain: string,
		data: Record<string, unknown>,
		ttl: number = 3600,
	): Promise<void>
	{
		await this.cacheDomainAnalysis(domain, data, ttl);
	}

	/**
	 * Invalidate specific domain analysis cache
	 */
	async invalidateDomainAnalysis(domain: string): Promise<boolean>
	{
		try
		{
			const key = Constants.CACHE_KEYS.DOMAIN_ANALYSIS.replace('{domain}', domain);
			const deleted = await this.redisClient.del(key);
			return deleted > 0;
		}
		catch (error)
		{
			return false;
		}
	}

	/**
	 * Invalidate cache by pattern
	 */
	async invalidatePattern(pattern: string): Promise<number>
	{
		try
		{
			return await this.redisClient.invalidatePattern(pattern);
		}
		catch (error)
		{
			return 0;
		}
	}

	/**
	 * Get cache statistics for a pattern
	 */
	async getCacheStats(pattern: string): Promise<{
		keyCount: number;
		totalMemory: number;
		patterns: string[];
	}>
	{
		try
		{
			const keys = await this.redisClient.keys(pattern);
			const stats = {
				keyCount: keys.length,
				totalMemory: 0,
				patterns: keys,
			};

			// Calculate memory usage (approximate)
			for (const key of keys)
			{
				try
				{
					const ttl = await this.redisClient.ttl(key);
					const data = await this.redisClient.get(key);
					if (data)
					{
						stats.totalMemory += JSON.stringify(data).length;
					}
				}
				catch
				{
					// Skip if key is expired or not accessible
				}
			}

			return stats;
		}
		catch (error)
		{
			return {
				keyCount: 0,
				totalMemory: 0,
				patterns: [],
			};
		}
	}

	/**
	 * Generate hash for search query
	 */
	generateSearchHash(query: string, filters: Record<string, unknown> = {}): string
	{
		const searchData = { query, filters };
		const searchString = JSON.stringify(searchData);

		// Simple hash function (in production, use crypto.createHash)
		let hash = 0;
		for (let i = 0; i < searchString.length; i++)
		{
			const char = searchString.charCodeAt(i);
			hash = (hash * 31 + char);
			if (hash > 0x7fffffff) hash -= 0x100000000; // clamp to 32-bit signed range
		}

		return Math.abs(hash).toString(36);
	}
}
