import { describe, beforeEach, expect, test, vi } from 'vitest';
import type { Mocked, MockedClass } from 'vitest';
import DatabaseManager from '../DatabaseManager';
import ScyllaClient from '../ScyllaClient';
import MariaClient from '../MariaClient';
import RedisClientWrapper from '../RedisClient';
import ManticoreClient from '../ManticoreClient';

// Mock all database clients
vi.mock('../ScyllaClient.js');
vi.mock('../MariaClient.js');
vi.mock('../RedisClient.js');
vi.mock('../ManticoreClient.js');

const MockedScyllaClient = ScyllaClient as MockedClass<typeof ScyllaClient>;
const MockedMariaClient = MariaClient as MockedClass<typeof MariaClient>;
const MockedRedisClient = RedisClientWrapper as MockedClass<typeof RedisClientWrapper>;
const MockedManticoreClient = ManticoreClient as MockedClass<typeof ManticoreClient>;

describe('DatabaseManager', () =>
{
	let databaseManager: DatabaseManager;
	let mockScyllaClient: Mocked<ScyllaClient>;
	let mockMariaClient: Mocked<MariaClient>;
	let mockRedisClient: Mocked<RedisClientWrapper>;
	let mockManticoreClient: Mocked<ManticoreClient>;

	beforeEach(() =>
	{
		// Reset all mocks
		vi.clearAllMocks();

		// Create mock instances
		mockScyllaClient = new MockedScyllaClient() as Mocked<ScyllaClient>;
		mockMariaClient = new MockedMariaClient() as Mocked<MariaClient>;
		mockRedisClient = new MockedRedisClient() as Mocked<RedisClientWrapper>;
		mockManticoreClient = new MockedManticoreClient() as Mocked<ManticoreClient>;

		// Mock constructor returns
		MockedScyllaClient.mockImplementation(() => mockScyllaClient);
		MockedMariaClient.mockImplementation(() => mockMariaClient);
		MockedRedisClient.mockImplementation(() => mockRedisClient);
		MockedManticoreClient.mockImplementation(() => mockManticoreClient);

		// Mock client methods
		mockScyllaClient.connect = vi.fn().mockResolvedValue(undefined);
		mockScyllaClient.disconnect = vi.fn().mockResolvedValue(undefined);
		mockScyllaClient.isConnected = vi.fn().mockReturnValue(true);
		mockScyllaClient.execute = vi.fn().mockResolvedValue({ rows: [] });

		mockMariaClient.connect = vi.fn().mockResolvedValue(undefined);
		mockMariaClient.disconnect = vi.fn().mockResolvedValue(undefined);
		mockMariaClient.isConnected = vi.fn().mockReturnValue(true);
		mockMariaClient.query = vi.fn().mockResolvedValue([]);

		mockRedisClient.connect = vi.fn().mockResolvedValue(undefined);
		mockRedisClient.disconnect = vi.fn().mockResolvedValue(undefined);
		mockRedisClient.isConnected = vi.fn().mockReturnValue(true);
		mockRedisClient.get = vi.fn().mockResolvedValue(null);
		mockRedisClient.set = vi.fn().mockResolvedValue('OK');

		mockManticoreClient.connect = vi.fn().mockResolvedValue(undefined);
		mockManticoreClient.disconnect = vi.fn().mockResolvedValue(undefined);
		mockManticoreClient.isConnected = vi.fn().mockReturnValue(true);
		mockManticoreClient.search = vi.fn().mockResolvedValue({ hits: [] });

		databaseManager = new DatabaseManager();
	});

	describe('initialization', () =>
	{
		test('should provide access to individual clients', async () =>
		{
			// Need to initialize first
			await databaseManager.initialize();

			expect(databaseManager.getScyllaClient()).toBeDefined();
			expect(databaseManager.getMariaClient()).toBeDefined();
			expect(databaseManager.getRedisClient()).toBeDefined();
			expect(databaseManager.getManticoreClient()).toBeDefined();
		});

		test('should initialize all database clients', async () =>
		{
			await databaseManager.initialize();

			expect(mockScyllaClient.connect).toHaveBeenCalledTimes(1);
			expect(mockMariaClient.connect).toHaveBeenCalledTimes(1);
			expect(mockRedisClient.connect).toHaveBeenCalledTimes(1);
			expect(mockManticoreClient.connect).toHaveBeenCalledTimes(1);
		});

		test('should handle initialization failures gracefully', async () =>
		{
			mockScyllaClient.connect.mockRejectedValue(new Error('ScyllaDB connection failed'));

			await expect(databaseManager.initialize()).rejects.toThrow('ScyllaDB connection failed');
		});
	});

	describe('close', () =>
	{
		test('should close all database clients', async () =>
		{
			// Initialize first
			await databaseManager.initialize();
			await databaseManager.close();

			expect(mockScyllaClient.disconnect).toHaveBeenCalledTimes(1);
			expect(mockMariaClient.disconnect).toHaveBeenCalledTimes(1);
			expect(mockRedisClient.disconnect).toHaveBeenCalledTimes(1);
			expect(mockManticoreClient.disconnect).toHaveBeenCalledTimes(1);
		});

		test('should handle close failures gracefully', async () =>
		{
			await databaseManager.initialize();
			mockRedisClient.disconnect.mockRejectedValue(new Error('Redis disconnection failed'));

			// Should throw the error
			await expect(databaseManager.close()).rejects.toThrow('Redis disconnection failed');
		});
	});

	describe('health checks', () =>
	{
		test('should check health of all database connections', async () =>
		{
			// Mock health check methods
			mockScyllaClient.healthCheck = vi.fn().mockResolvedValue(true);
			mockMariaClient.healthCheck = vi.fn().mockResolvedValue(true);
			mockRedisClient.healthCheck = vi.fn().mockResolvedValue(true);
			mockManticoreClient.healthCheck = vi.fn().mockResolvedValue(true);

			await databaseManager.initialize();
			const health = await databaseManager.healthCheck();

			expect(health).toEqual({
				scylla: true,
				maria: true,
				redis: true,
				manticore: true,
			});
		});

		test('should detect unhealthy connections', async () =>
		{
			// Mock health check methods with failures
			mockScyllaClient.healthCheck = vi.fn().mockResolvedValue(false);
			mockMariaClient.healthCheck = vi.fn().mockResolvedValue(false);
			mockRedisClient.healthCheck = vi.fn().mockResolvedValue(true);
			mockManticoreClient.healthCheck = vi.fn().mockResolvedValue(true);

			await databaseManager.initialize();
			const health = await databaseManager.healthCheck();

			expect(health.scylla).toBe(false);
			expect(health.maria).toBe(false);
			expect(health.redis).toBe(true);
			expect(health.manticore).toBe(true);
		});
	});

	describe('basic functionality', () =>
	{
		test('should have basic database functionality', () =>
		{
			// Basic smoke test to ensure DatabaseManager can be instantiated
			expect(databaseManager).toBeDefined();
			expect(typeof databaseManager.getScyllaClient).toBe('function');
			expect(typeof databaseManager.getMariaClient).toBe('function');
			expect(typeof databaseManager.getRedisClient).toBe('function');
			expect(typeof databaseManager.getManticoreClient).toBe('function');
		});
	});
});
