// Database connections
import DatabaseManager from './database/DatabaseManager';
import ScyllaClient from './database/ScyllaClient';
import MariaClient from './database/MariaClient';
import ManticoreClient from './database/ManticoreClient';
import RedisClientWrapper, { CacheManager } from './database/RedisClient';

import JobQueue, { JobQueueFactory } from './queue/JobQueue';

// Services
import DataSyncService from './services/DataSyncService';
import SyncMonitoringService from './services/SyncMonitoringService';
import CachingService from './services/CachingService';
import CacheInvalidationService from './services/CacheInvalidationService';
import DomainDescriptionCacheService from './services/DomainDescriptionCacheService';

// Utilities
import Logger, { logger, type LoggerInstanceType } from './utils/Logger';
import { createIsomorphicLogger, type IsomorphicLogger } from './utils/IsomorphicLogger';
import Config, { config } from './utils/Config';
import { validator } from './utils/Validators';
// import DomainDescriptionValidator from './utils/DomainDescriptionValidator';
// import HttpClient from './utils/HttpClient';
// import HttpClientStream from './utils/HttpClientStream';

// Constants
import Constants from './constants/Constants';

// Monitoring
import HealthChecker from './monitoring/HealthChecker';
import MetricsCollector, { CommonMetrics } from './monitoring/MetricsCollector';

// Error Handling
import {
	BaseErrorHandler,
	ErrorClassificationService,
	RetryManager,
	CircuitBreaker,
	CircuitBreakerState,
} from './errors';
import type {
	ErrorHandlingConfigType,
	ErrorHandlingContextType,
	ErrorHandlingResultType,
	ErrorClassificationResultType,
	ErrorMetricsType,
	RetryStrategyConfigType,
	RetryResultType,
	CircuitBreakerConfigType,
} from './errors';

// Middleware
import {
	ErrorMiddleware,
	// MetricsMiddleware,
	SecurityMiddleware,
	RateLimitMiddleware,
} from './middleware';
import type {
	ErrorMiddlewareOptions,
	MetricsMiddlewareOptions,
	SecurityMiddlewareOptions,
	RateLimitMiddlewareOptions,
	RequestLike,
	ResponseLike,
	NextFunction,
	MiddlewareOptions,
} from './middleware';

// API utilities
import { ApiResponse } from './api';
import type {
	ApiResponseType,
	PaginationMetaType,
	PaginationOptionsType,
} from './api';

// Validation utilities
import { DomainValidator, domainValidator } from './validation';
import type {
	DomainValidationConfigType,
	DomainValidationResultType,
} from './validation';

// ID Generation utilities
import { IdGenerator, JitterUtils } from './utils/IdGenerator';
import type { IdOptions } from './utils/IdGenerator';

// String utilities
import { StringUtils } from './utils/StringUtils';
import { AsyncUtils } from './utils/AsyncUtils';
import { DomainHasher } from './utils/DomainHasher';

// Export types first
export type * from './models/DomainModels';
export type { default as DomainDescriptionInterface } from './models/DomainDescription';
export type { DomainDescriptionCacheConfig, CacheStats, CacheLevel } from './services/DomainDescriptionCacheService';
// export type { HttpClientOptionsType, HttpResponseType, HttpErrorInterface } from './utils/HttpClient';
// export type { StreamOptions } from './utils/HttpClientStream';
export type {
	ErrorHandlingConfigType,
	ErrorHandlingContextType,
	ErrorHandlingResultType,
	ErrorClassificationResultType,
	ErrorMetricsType,
	RetryStrategyConfigType,
	RetryResultType,
	CircuitBreakerConfigType,
	ErrorSeverityType,
	ErrorCategoryType,
} from './errors';
export type {
	ErrorMiddlewareOptions,
	MetricsMiddlewareOptions,
	SecurityMiddlewareOptions,
	RateLimitMiddlewareOptions,
	RequestLike,
	ResponseLike,
	NextFunction,
	MiddlewareOptions,
} from './middleware';
export type {
	ApiResponseType,
	PaginationMetaType,
	PaginationOptionsType,
} from './api';
export type {
	DomainValidationConfigType,
	DomainValidationResultType,
} from './validation';
export type { IdOptions } from './utils/IdGenerator';
export type { LoggerInstanceType } from './utils/Logger';
export type { IsomorphicLogger } from './utils/IsomorphicLogger';

// Export classes and utilities
export {
	DatabaseManager,
	ScyllaClient,
	MariaClient,
	ManticoreClient,
	RedisClientWrapper,
	CacheManager,
	JobQueue,
	JobQueueFactory,
	DataSyncService,
	SyncMonitoringService,
	CachingService,
	CacheInvalidationService,
	DomainDescriptionCacheService,
	Constants,
	HealthChecker,
	// MetricsCollector,
	// CommonMetrics,
	// DomainDescriptionValidator,
	// HttpClient,
	// HttpClientStream,
	// Error Handling
	BaseErrorHandler,
	ErrorClassificationService,
	RetryManager,
	CircuitBreaker,
	CircuitBreakerState,
	// Middleware
	ErrorMiddleware,
	// MetricsMiddleware,
	SecurityMiddleware,
	RateLimitMiddleware,
	// API utilities
	ApiResponse,
	// Validation utilities
	DomainValidator,
	domainValidator,
	// ID Generation utilities
	IdGenerator,
	JitterUtils,
	// String utilities
	StringUtils,
	AsyncUtils,
	DomainHasher,
	// Services/Classes
	// Utilities
	Config,
	Logger,
	createIsomorphicLogger,
	// Instances
	config,
	logger,
	validator,
};

export default DatabaseManager;
