// Server-only exports - database clients, Redis, etc.
export { default as DatabaseManager } from './database/DatabaseManager';
export { default as ScyllaClient } from './database/ScyllaClient';
export { default as MariaClient } from './database/MariaClient';
export { default as ManticoreClient } from './database/ManticoreClient';
export { default as RedisClientWrapper, CacheManager } from './database/RedisClient';

// export { default as JobQueue, JobQueueFactory } from './queue/JobQueue';

export { default as DataSyncService } from './services/DataSyncService';
export { default as SyncMonitoringService } from './services/SyncMonitoringService';
export { default as CachingService } from './services/CachingService';
export { default as CacheInvalidationService } from './services/CacheInvalidationService';
export { default as DomainDescriptionCacheService } from './services/DomainDescriptionCacheService';

export { default as Logger, logger, type LoggerInstanceType } from './utils/Logger';
export { default as Config, config } from './utils/Config';
export { default as HealthChecker } from './monitoring/HealthChecker';
export { default as MetricsCollector, CommonMetrics } from './monitoring/MetricsCollector';
export { default as HttpClient } from './utils/HttpClient';
export { default as HttpClientStream } from './utils/HttpClientStream';

// Error Handling (server parts)
export {
	BaseErrorHandler,
	ErrorClassificationService,
	RetryManager,
	CircuitBreaker,
	CircuitBreakerState,
} from './errors';

// Middleware (server-only)
export {
	ErrorMiddleware,
	SecurityMiddleware,
	RateLimitMiddleware,
} from './middleware';

// Re-export everything from client for convenience
export * from './client';
