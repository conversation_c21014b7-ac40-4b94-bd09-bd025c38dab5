/* eslint-disable max-classes-per-file */

import { logger } from '../utils/Logger';
import RedisClientWrapper from '../database/RedisClient';

type MetricType =
{
	name: string;
	value: number;
	timestamp: number;
	tags?: { [key: string]: string };
	type: 'counter' | 'gauge' | 'histogram' | 'timer';
};

type MetricsSummaryType =
{
	totalMetrics: number;
	timeRange:
	{
		start: number;
		end: number;
	};
	metrics:
	{
		[name: string]:
		{
			count: number;
			min: number;
			max: number;
			avg: number;
			latest: number;
		};
	};
};

class MetricsCollector
{
	private logger = logger.getLogger('MetricsCollector');

	private redisClient: RedisClientWrapper;

	private metricsBuffer: MetricType[] = [];

	private bufferSize: number = 100;

	private flushInterval: number = 30000; // 30 seconds

	private flushTimer?: NodeJS.Timeout;

	constructor(redisClient?: RedisClientWrapper)
	{
		this.redisClient = redisClient || new RedisClientWrapper();
		this.startPeriodicFlush();
	}

	/**
	 * Record a counter metric (incremental value)
	 */
	counter(name: string, value: number = 1, tags?: { [key: string]: string }): void
	{
		this.recordMetric({
			name,
			value,
			timestamp: Date.now(),
			tags,
			type: 'counter',
		});
	}

	/**
	 * Record a gauge metric (current value)
	 */
	gauge(name: string, value: number, tags?: { [key: string]: string }): void
	{
		this.recordMetric({
			name,
			value,
			timestamp: Date.now(),
			tags,
			type: 'gauge',
		});
	}

	/**
	 * Record a histogram metric (distribution of values)
	 */
	histogram(name: string, value: number, tags?: { [key: string]: string }): void
	{
		this.recordMetric({
			name,
			value,
			timestamp: Date.now(),
			tags,
			type: 'histogram',
		});
	}

	/**
	 * Record a timer metric (duration measurement)
	 */
	timer(name: string, duration: number, tags?: { [key: string]: string }): void
	{
		this.recordMetric({
			name,
			value: duration,
			timestamp: Date.now(),
			tags,
			type: 'timer',
		});
	}

	// Better-named aliases for common operations
	incrementCounter(name: string, value: number = 1, tags?: { [key: string]: string }): void
	{
		this.counter(name, value, tags);
	}

	setGauge(name: string, value: number, tags?: { [key: string]: string }): void
	{
		this.gauge(name, value, tags);
	}

	recordHistogram(name: string, value: number, tags?: { [key: string]: string }): void
	{
		this.histogram(name, value, tags);
	}

	/**
	 * Time a function execution
	 */
	async time<T>(name: string, fn: () => Promise<T>, tags?: { [key: string]: string }): Promise<T>
	{
		const startTime = Date.now();
		try
		{
			const result = await fn();
			this.timer(name, Date.now() - startTime, tags);
			return result;
		}
		catch (error)
		{
			this.timer(name, Date.now() - startTime, { ...tags, error: 'true' });
			throw error;
		}
	}

	/**
	 * Record a metric
	 */
	private recordMetric(metric: MetricType): void
	{
		this.metricsBuffer.push(metric);

		// Flush if buffer is full
		if (this.metricsBuffer.length >= this.bufferSize)
		{
			this.flush();
		}
	}

	/**
	 * Flush metrics to storage
	 */
	async flush(): Promise<void>
	{
		if (this.metricsBuffer.length === 0)
		{
			return;
		}

		const metricsToFlush = [...this.metricsBuffer];
		this.metricsBuffer = [];

		try
		{
			await this.storeMetrics(metricsToFlush);
			this.logger.debug(`Flushed ${metricsToFlush.length} metrics`);
		}
		catch (error)
		{
			this.logger.error('Failed to flush metrics:', error);
			// Put metrics back in buffer for retry
			this.metricsBuffer.unshift(...metricsToFlush);
		}
	}

	/**
	 * Store metrics in Redis
	 */
	private async storeMetrics(metrics: MetricType[]): Promise<void>
	{
		if (!this.redisClient.isClientConnected())
		{
			await this.redisClient.connect();
		}

		const client = this.redisClient.getClient();
		if (!client)
		{
			throw new Error('Redis client not available');
		}

		const pipeline = client.multi();

		for (const metric of metrics)
		{
			const key = this.getMetricKey(metric);
			const data = JSON.stringify({
				value: metric.value,
				timestamp: metric.timestamp,
				tags: metric.tags,
				type: metric.type,
			});

			// Store individual metric
			pipeline.zAdd(key, { score: metric.timestamp, value: data });

			// Set expiration (keep metrics for 7 days)
			pipeline.expire(key, 7 * 24 * 60 * 60);

			// Update metric summary
			const summaryKey = `metrics:summary:${metric.name}`;
			pipeline.hIncrBy(summaryKey, 'count', 1);
			pipeline.hSet(summaryKey, 'latest', metric.value.toString());
			pipeline.hSet(summaryKey, 'last_updated', metric.timestamp.toString());
			pipeline.expire(summaryKey, 7 * 24 * 60 * 60);
		}

		await pipeline.exec();
	}

	/**
	 * Get metric storage key
	 */
	private getMetricKey(metric: MetricType): string
	{
		const tagString = metric.tags
			? Object.entries(metric.tags)
				.sort(([a], [b]) => a.localeCompare(b))
				.map(([k, v]) => `${k}:${v}`)
				.join(',') : '';

		return `metrics:${metric.name}${tagString ? `:${tagString}` : ''}`;
	}

	/**
	 * Get metrics for a specific time range
	 */
	async getMetrics(
		name: string,
		startTime: number,
		endTime: number,
		tags?: { [key: string]: string },
	): Promise<MetricType[]>
	{
		if (!this.redisClient.isClientConnected())
		{
			await this.redisClient.connect();
		}

		const client = this.redisClient.getClient();
		if (!client)
		{
			throw new Error('Redis client not available');
		}

		const key = this.getMetricKey({
			name, value: 0, timestamp: 0, tags, type: 'gauge',
		});
		const results = await client.zRangeByScore(key, startTime, endTime);

		return results.map((result: string) =>
		{
			const data = JSON.parse(result);
			return {
				name,
				value: data.value,
				timestamp: data.timestamp,
				tags: data.tags,
				type: data.type,
			};
		});
	}

	/**
	 * Get metrics summary
	 */
	async getMetricsSummary(names?: string[]): Promise<MetricsSummaryType>
	{
		const client = this.redisClient.getClient();
		if (!client)
		{
			throw new Error('Redis client not available');
		}

		const summaryKeys = names
			? names.map(name => `metrics:summary:${name}`)
			: await this.redisClient.keys('metrics:summary:*');

		const metrics: Record<string, {
			count: number;
			min: number;
			max: number;
			avg: number;
			latest: number;
		}> = {};
		let totalMetrics = 0;
		let earliestTime = Date.now();
		let latestTime = 0;

		// Fetch all summaries in parallel to avoid await-in-loop
		const summaryResults = await Promise.all(
			summaryKeys.map(key => this.redisClient.get<Record<string, string>>(key)),
		);

		const nonEmptySummaries = summaryResults
			.map((summary, i) => ({ summary, key: summaryKeys[i] }))
			.filter(item => Boolean(item.summary));

		// Fetch latest detail values for the summaries found
		await Promise.all(
			nonEmptySummaries.map(async (item) =>
			{
				const match = /metrics:(.*):summary:(.*)/.exec(item.key);
				if (!match)
				{
					return null;
				}

				const metricName = match[1];
				const summary = item.summary as Record<string, string>;
				const tags = JSON.parse(summary.tags || '{}') as Record<string, string>;
				const latestValue = await this.redisClient.get<string>(
					`metrics:${metricName}:${summary.latest}`,
				);
				const latest = latestValue ? parseFloat(latestValue) : 0;

				// Update time range
				const firstTs = parseInt(summary.firstTimestamp || '0', 10);
				const lastTs = parseInt(summary.lastTimestamp || '0', 10);
				if (firstTs > 0 && firstTs < earliestTime)
				{
					earliestTime = firstTs;
				}
				if (lastTs > 0 && lastTs > latestTime)
				{
					latestTime = lastTs;
				}

				// Count metrics entries for this metric name and tags
				const tagsSuffix = Object.keys(tags).length > 0
					? `:${Object.values(tags).join(':')}`
					: '';
				const pattern = `metrics:${metricName}:${tagsSuffix}*`;
				const keys = await this.redisClient.keys(pattern);
				const count = keys.length;

				// Store metric in results
				metrics[metricName] = {
					count,
					min: parseFloat(summary.min || '0'),
					max: parseFloat(summary.max || '0'),
					avg: parseFloat(summary.avg || '0'),
					latest,
				};

				totalMetrics += count;

				return null;
			}),
		);

		return {
			totalMetrics,
			timeRange: {
				start: earliestTime < Date.now() ? earliestTime : Date.now() - 3600000, // Last hour fallback
				end: latestTime > 0 ? latestTime : Date.now(),
			},
			metrics,
		};
	}

	/**
	 * Start periodic flush
	 */
	private startPeriodicFlush(): void
	{
		this.flushTimer = setInterval(() =>
		{
			this.flush().catch((error) =>
			{
				this.logger.error('Periodic flush failed:', error);
			});
		}, this.flushInterval);
	}

	/**
	 * Stop periodic flush
	 */
	async stop(): Promise<void>
	{
		if (this.flushTimer)
		{
			clearInterval(this.flushTimer);
			this.flushTimer = undefined;
		}

		// Flush remaining metrics
		await this.flush();

		if (this.redisClient.isClientConnected())
		{
			await this.redisClient.disconnect();
		}
	}

	/**
	 * Clean up old metrics
	 */
	async cleanupOldMetrics(olderThanDays: number = 7): Promise<number>
	{
		if (!this.redisClient.isClientConnected())
		{
			await this.redisClient.connect();
		}

		const client = this.redisClient.getClient();
		if (!client)
		{
			throw new Error('Redis client not available');
		}

		const cutoffTime = Date.now() - (olderThanDays * 24 * 60 * 60 * 1000);
		const metricKeys = await this.redisClient.keys('metrics:*');

		// Filter out summary keys first, then delete in parallel
		const dataKeys = metricKeys.filter(key => !key.includes(':summary:'));
		const removedCounts = await Promise.all(
			dataKeys.map(key => client.zRemRangeByScore(key, 0, cutoffTime)),
		);
		const cleanedCount = removedCounts.reduce((acc: number, n: number) => acc + n, 0);

		this.logger.info(`Cleaned up ${cleanedCount} old metrics older than ${olderThanDays} days`);
		return cleanedCount;
	}
}

// Common metrics helper functions
export class CommonMetrics
{
	private metrics: MetricsCollector;

	constructor(metrics: MetricsCollector)
	{
		this.metrics = metrics;
	}

	/**
	 * Record HTTP request metrics
	 */
	httpRequest(method: string, path: string, statusCode: number, duration: number): void
	{
		this.metrics.counter('http_requests_total', 1, {
			method,
			path,
			status_code: statusCode.toString(),
		});

		this.metrics.timer('http_request_duration', duration, {
			method,
			path,
			status_code: statusCode.toString(),
		});
	}

	/**
	 * Record database query metrics
	 */
	databaseQuery(database: string, operation: string, duration: number, success: boolean): void
	{
		this.metrics.counter('database_queries_total', 1, {
			database,
			operation,
			success: success.toString(),
		});

		this.metrics.timer('database_query_duration', duration, {
			database,
			operation,
		});
	}

	/**
	 * Record cache metrics
	 */
	cacheOperation(operation: string, hit: boolean): void
	{
		this.metrics.counter('cache_operations_total', 1, {
			operation,
			result: hit ? 'hit' : 'miss',
		});
	}

	/**
	 * Record job processing metrics
	 */
	jobProcessing(jobType: string, duration: number, success: boolean): void
	{
		this.metrics.counter('jobs_processed_total', 1, {
			job_type: jobType,
			success: success.toString(),
		});

		this.metrics.timer('job_processing_duration', duration, {
			job_type: jobType,
		});
	}

	/**
	 * Record system resource metrics
	 */
	systemResources(memoryUsage: number, cpuUsage: number): void
	{
		this.metrics.gauge('system_memory_usage_bytes', memoryUsage);
		this.metrics.gauge('system_cpu_usage_percent', cpuUsage);
	}
}

export default MetricsCollector;
