import DatabaseManager from '../database/DatabaseManager';
import { logger } from '../utils/Logger';

type HealthStatus =
{
	status: 'healthy' | 'degraded' | 'unhealthy';
	timestamp: string;
	uptime: number;
	version: string;
	services: {
		[key: string]: ServiceHealth;
	};
	metrics?: SystemMetrics;
};

type ServiceHealth =
{
	status: 'healthy' | 'degraded' | 'unhealthy';
	responseTime?: number;
	lastCheck: string;
	error?: string;
	details?: unknown;
};

type SystemMetrics =
{
	memory: {
		used: number;
		total: number;
		percentage: number;
	};
	cpu: {
		usage: number;
		loadAverage: number[];
	};
	disk?: {
		used: number;
		total: number;
		percentage: number;
	};
	network?: {
		connections: number;
		bytesIn: number;
		bytesOut: number;
	};
};

class HealthChecker
{
	private logger = logger.getLogger('HealthChecker');

	private databaseManager: DatabaseManager;

	private startTime: number;

	private version: string;

	private serviceName: string;

	constructor(serviceName: string, version: string = '1.0.0')
	{
		this.serviceName = serviceName;
		this.version = version;
		this.startTime = Date.now();
		this.databaseManager = new DatabaseManager();
	}

	/**
	 * Get comprehensive health status
	 */
	async getHealthStatus(includeMetrics: boolean = false): Promise<HealthStatus>
	{
		const timestamp = new Date().toISOString();
		const uptime = Math.floor((Date.now() - this.startTime) / 1000);

		const services: { [key: string]: ServiceHealth } = {};

		// Check database connections
		try
		{
			const dbHealth = await this.checkDatabaseHealth();
			Object.assign(services, dbHealth);
		}
		catch (error)
		{
			this.logger.error({ error }, 'Database health check failed');
			services.database = {
				status: 'unhealthy',
				lastCheck: timestamp,
				error: (error as Error)?.message ?? String(error),
			};
		}

		// Check external services
		try
		{
			const externalHealth = await this.checkExternalServices();
			Object.assign(services, externalHealth);
		}
		catch (error)
		{
			this.logger.warn({ error }, 'External service health check failed');
		}

		// Determine overall status
		const overallStatus = this.determineOverallStatus(services);

		const healthStatus: HealthStatus = {
			status: overallStatus,
			timestamp,
			uptime,
			version: this.version,
			services,
		};

		// Include system metrics if requested
		if (includeMetrics)
		{
			healthStatus.metrics = await this.getSystemMetrics();
		}

		return healthStatus;
	}

	/**
	 * Check database health
	 */
	private async checkDatabaseHealth(): Promise<{ [key: string]: ServiceHealth }>
	{
		const services: { [key: string]: ServiceHealth } = {};
		const timestamp = new Date().toISOString();

		// Check ScyllaDB
		try
		{
			const startTime = Date.now();
			const scyllaClient = this.databaseManager.getScyllaClient();

			try
			{
				await scyllaClient.execute('SELECT now() FROM system.local LIMIT 1');
				services.scylladb = {
					status: 'healthy',
					responseTime: Date.now() - startTime,
					lastCheck: timestamp,
				};
			}
			catch
			{
				services.scylladb =
				{
					status: 'unhealthy',
					lastCheck: timestamp,
					error: 'Not connected',
				};
			}
		}
		catch (error)
		{
			services.scylladb =
			{
				status: 'unhealthy',
				lastCheck: timestamp,
				error: (error as Error)?.message ?? String(error),
			};
		}

		// Check MariaDB
		try
		{
			const startTime = Date.now();
			const mariaClient = this.databaseManager.getMariaClient();

			try
			{
				await mariaClient.execute('SELECT 1');
				services.mariadb = {
					status: 'healthy',
					responseTime: Date.now() - startTime,
					lastCheck: timestamp,
				};
			}
			catch
			{
				services.mariadb =
				{
					status: 'unhealthy',
					lastCheck: timestamp,
					error: 'Not connected',
				};
			}
		}
		catch (error)
		{
			services.mariadb = {
				status: 'unhealthy',
				lastCheck: timestamp,
				error: (error as Error)?.message ?? String(error),
			};
		}

		// Check Redis
		try
		{
			const startTime = Date.now();
			const redisClient = this.databaseManager.getRedisClient();

			try
			{
				await redisClient.ping();
				services.redis =
				{
					status: 'healthy',
					responseTime: Date.now() - startTime,
					lastCheck: timestamp,
				};
			}
			catch
			{
				services.redis =
				{
					status: 'unhealthy',
					lastCheck: timestamp,
					error: 'Connection failed',
				};
			}
		}
		catch (error)
		{
			services.redis = {
				status: 'unhealthy',
				lastCheck: timestamp,
				error: (error as Error)?.message ?? String(error),
			};
		}

		// Check Manticore Search
		try
		{
			const startTime = Date.now();
			const manticoreClient = this.databaseManager.getManticoreClient();

			try
			{
				await manticoreClient.healthCheck();
				services.manticore = {
					status: 'healthy',
					responseTime: Date.now() - startTime,
					lastCheck: timestamp,
				};
			}
			catch
			{
				services.manticore = {
					status: 'unhealthy',
					lastCheck: timestamp,
					error: 'Connection failed',
				};
			}
		}
		catch (error)
		{
			services.manticore = {
				status: 'degraded',
				lastCheck: timestamp,
				error: (error as Error)?.message ?? String(error),
			};
		}

		return services;
	}

	/**
	 * Check external services
	 */
	private async checkExternalServices(): Promise<{ [key: string]: ServiceHealth }>
	{
		const services: { [key: string]: ServiceHealth } = {};
		const timestamp = new Date().toISOString();

		// Check Browserless service (if configured)
		if (process.env.BROWSERLESS_URL)
		{
			try
			{
				const startTime = Date.now();
				const response = await fetch(`${process.env.BROWSERLESS_URL}/health`);

				if (response.ok)
				{
					services.browserless =
					{
						status: 'healthy',
						responseTime: Date.now() - startTime,
						lastCheck: timestamp,
					};
				}
				else
				{
					services.browserless = {
						status: 'degraded',
						lastCheck: timestamp,
						error: `HTTP ${response.status}`,
					};
				}
			}
			catch (error)
			{
				services.browserless =
				{
					status: 'unhealthy',
					lastCheck: timestamp,
					error: (error as Error)?.message ?? String(error),
				};
			}
		}

		// Check Image Proxy service
		if (process.env.IMAGE_PROXY_URL)
		{
			try
			{
				const startTime = Date.now();
				const response = await fetch(`${process.env.IMAGE_PROXY_URL}/health`);

				if (response.ok)
				{
					services.imageProxy =
					{
						status: 'healthy',
						responseTime: Date.now() - startTime,
						lastCheck: timestamp,
					};
				}
				else
				{
					services.imageProxy =
					{
						status: 'degraded',
						lastCheck: timestamp,
						error: `HTTP ${response.status}`,
					};
				}
			}
			catch (error)
			{
				services.imageProxy =
				{
					status: 'unhealthy',
					lastCheck: timestamp,
					error: (error as Error)?.message ?? String(error),
				};
			}
		}

		return services;
	}

	/**
	 * Get system metrics
	 */
	private async getSystemMetrics(): Promise<SystemMetrics>
	{
		const process = await import('process');
		const os = await import('os');

		const memoryUsage = process.memoryUsage();
		const totalMemory = os.totalmem();
		const freeMemory = os.freemem();
		const usedMemory = totalMemory - freeMemory;

		return ({
			memory: {
				used: Math.round(memoryUsage.heapUsed / 1024 / 1024), // MB
				total: Math.round(totalMemory / 1024 / 1024), // MB
				percentage: Math.round((usedMemory / totalMemory) * 100),
			},
			cpu: {
				usage: await this.getCpuUsage(),
				loadAverage: os.loadavg(),
			},
		});
	}

	/**
	 * Get CPU usage percentage
	 */
	private async getCpuUsage(): Promise<number>
	{
		const os = await import('os');

		const cpus = os.cpus();
		let totalIdle = 0;
		let totalTick = 0;

		cpus.forEach((cpu) =>
		{
			const times = Object.values(cpu.times);
			for (const time of times)
			{
				totalTick += time;
			}
			totalIdle += cpu.times.idle;
		});

		const idle = totalIdle / cpus.length;
		const total = totalTick / cpus.length;
		const usage = 100 - Math.floor((100 * idle) / total);

		return usage;
	}

	/**
	 * Determine overall system status
	 */
	private determineOverallStatus(services: { [key: string]: ServiceHealth }): 'healthy' | 'degraded' | 'unhealthy'
	{
		const statuses = Object.values(services).map(service => service.status);

		if (statuses.includes('unhealthy'))
		{
			// If any critical service is unhealthy, system is unhealthy
			const criticalServices = ['scylladb', 'mariadb', 'redis'];
			const unhealthyServices = Object.entries(services)
				.filter(entry => entry[1].status === 'unhealthy')
				.map(entry => entry[0]);

			if (unhealthyServices.some(name => criticalServices.includes(name)))
			{
				return 'unhealthy';
			}
		}

		if (statuses.includes('degraded') || statuses.includes('unhealthy'))
		{
			return 'degraded';
		}

		return 'healthy';
	}

	/**
	 * Get simple health check (for load balancers)
	 */
	async getSimpleHealth(): Promise<{ status: string; timestamp: string }>
	{
		try
		{
			const health = await this.getHealthStatus(false);
			return ({
				status: health.status,
				timestamp: health.timestamp,
			});
		}
		catch
		{
			return ({
				status: 'unhealthy',
				timestamp: new Date().toISOString(),
			});
		}
	}

	/**
	 * Check if service is ready to accept traffic
	 */
	async isReady(): Promise<boolean>
	{
		try
		{
			const health = await this.getHealthStatus(false);

			// Service is ready if overall status is healthy or degraded
			// (degraded might mean some non-critical services are down)
			return health.status === 'healthy' || health.status === 'degraded';
		}
		catch (error)
		{
			this.logger.error({ error }, 'Readiness check failed');
			return false;
		}
	}

	/**
	 * Check if service is alive (basic liveness check)
	 */
	async isAlive(): Promise<boolean>
	{
		try
		{
			// Check if the process is running and can execute basic operations
			const startTime = Date.now();

			// Perform a simple memory check to ensure the process is responsive
			const memoryUsage = process.memoryUsage();
			const responseTime = Date.now() - startTime;

			// Consider alive if memory check completes within reasonable time (100ms)
			const isResponsive = responseTime < 100 && memoryUsage.heapUsed > 0;

			if (!isResponsive)
			{
				this.logger.warn('Service responsiveness check failed');
			}

			return isResponsive;
		}
		catch (error)
		{
			this.logger.error({ error }, 'Liveness check failed');
			return false;
		}
	}
}

export default HealthChecker;
