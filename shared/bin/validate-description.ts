#!/usr/bin/env node
import { readFileSync } from 'node:fs';
import { resolve } from 'node:path';
import { DomainDescriptionValidator } from '../src/utils/DomainDescriptionValidator';

function usage() 
{
	 
	console.error('Usage: validate-description <file.json> [more.json ...]');
	process.exit(1);
}

async function main() 
{
	const files = process.argv.slice(2);
	if (files.length === 0) usage();

	const validator = DomainDescriptionValidator.get();
	let failed = 0;

	for (const f of files) 
	{
		const path = resolve(process.cwd(), 'shared', f);
		try 
		{
			const content = readFileSync(path, 'utf-8');
			const json = JSON.parse(content);
			const res = validator.validate(json);
			if (res.ok) 
			{
				 
				console.log(`OK  ${f}`);
			}
			else 
			{
				failed++;
				 
				console.error(`ERR ${f}`);
				for (const e of res.errors || []) 
				{
					 
					console.error(`  - ${e.instancePath || e.schemaPath}: ${e.message}`);
				}
			}
		}
		catch (e) 
		{
			failed++;
			 
			console.error(`EXC ${f}: ${(e as Error).message}`);
		}
	}

	if (failed > 0) process.exit(2);
}

main();
