{"name": "shared", "version": "1.0.0", "description": "Shared utilities and interfaces for domain ranking system services", "main": "index.js", "scripts": {"test": "vitest run -w", "test:watch": "vitest -w", "build": "echo skip", "validate:file": "tsx ./bin/validate-description.ts", "validate:samples": "tsx ./bin/validate-description.ts ../../docs/samples/example-domain.json"}, "dependencies": {"ajv": "^8.17.1", "ajv-formats": "^3.0.1", "cassandra-driver": "^4.7.2", "dotenv": "^16.3.1", "joi": "^17.9.2", "mysql2": "^3.6.0", "node-libcurl": "^4.1.0", "pino": "^9.7.0", "pino-pretty": "^13.1.1", "redis": "^5.8.1", "redis-smq": "^8.3.1", "redis-smq-common": "^8.3.1", "xss": "^1.0.15"}, "devDependencies": {"@babel/preset-env": "^7.22.9", "@types/ajv": "^1.0.4", "@types/node": "^22.0.0", "@types/pino": "^7.0.5", "@vitest/coverage-v8": "^2.0.5", "@vitest/ui": "^2.0.5", "typescript": "^5.0.0", "vitest": "^2.0.5"}, "engines": {"node": ">=22.0.0", "pnpm": ">=10.15.0"}, "packageManager": "pnpm@10.15.0"}