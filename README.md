# Domain Ranking System

A comprehensive domain ranking and analysis platform that evaluates websites based on performance, security, SEO, and technical metrics. Similar to Alexa Rankings, Cloudflare Radar, and URLScan.io, this system provides automated website analysis, data collection through web crawling, and user-friendly interfaces for browsing ranked domains.

## 🚀 Quick Deployment

### Prerequisites

- Docker 20.10+
- Docker Compose 2.0+
- Just (command runner) - optional but recommended
- OpenSSL (for SSL certificate generation)
- At least 8GB RAM and 4 CPU cores
- 50GB+ storage space

### Quick Start Deployment

1. **Clone and Setup**

   ```bash
   git clone <repository-url>
   cd domain-ranking-system
   cp .env.example .env
   # Edit .env with your configuration
   ```

2. **Deploy with Just (Recommended)**

   ```bash
   just setup
   just deploy
   ```

3. **Deploy with <PERSON><PERSON>ts**

   ```bash
   ./scripts/deploy.sh
   ```

### Configuration

Copy `.env.example` to `.env` and configure:

```bash
# Required - Set secure passwords
MARIA_PASSWORD=your_secure_password_here
JWT_SECRET=your_jwt_secret_here

# Optional - Customize as needed
NODE_ENV=production
LOG_LEVEL=info
CORS_ORIGIN=https://yourdomain.com
```

### SSL Certificates

For production, replace the self-signed certificates:

```bash
# Place your certificates in nginx/ssl/
cp your-cert.pem nginx/ssl/cert.pem
cp your-key.pem nginx/ssl/key.pem
```

### Deployment Process

#### Automated Deployment

```bash
# Full deployment with health checks
just deploy

# Check deployment status
just deploy-status

# View logs
just logs
```

#### Manual Deployment

```bash
# 1. Build images
docker-compose build

# 2. Start databases
docker-compose up -d scylla mariadb redis manticore

# 3. Wait for databases and run migrations
sleep 60
just db-init

# 4. Start application services
docker-compose up -d browserless worker web-app

# 5. Start reverse proxy and monitoring
docker-compose up -d nginx prometheus grafana
```

#### Database Initialization

```bash
# Initialize all databases with sample data
just db-init

# Or run individual steps
just db-migrate
just db-sample-data
```

### Service URLs

After deployment, services are available at:

- **Web Application**: <http://localhost> or <https://localhost>
- **Prometheus**: <http://localhost:9090>
- **Grafana**: <http://localhost:3001> (admin/admin)
- **ScyllaDB API**: <http://localhost:10000>

## 🏗️ Architecture

The system consists of three main services (previously four services were merged):

- **Web Application** - Express.js API and React frontend for domain search and analysis
- **Worker Service** - Combined service handling automated data collection, analysis, scoring, ranking calculations, and job scheduling (merged from previous crawler, ranking-engine, and scheduler services)
- **Domain Seeder** - Domain discovery and seeding service

## 🛠️ Technology Stack

### Backend Services

- **Node.js 18+** - Runtime environment
- **TypeScript** - Type-safe JavaScript development
- **tsx** - TypeScript execution and development
- **Express.js** - Web application framework
- **React 18** - Frontend user interface
- **Redis-SMQ** - Job queue and inter-service communication

### Databases (External Services)

- **ScyllaDB** - Main data storage for domain analysis and rankings
- **MariaDB** - Relational data (categories, backlinks, whois)
- **Manticore Search** - Full-text search and faceted filtering
- **Redis** - Caching and session storage

### External Services

- **images.weserv.nl** - Image optimization and WebP conversion

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- Yarn 4.0+
- External database services (ScyllaDB, MariaDB, Redis, Manticore)

### Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd domain-ranking-system/domainr
   ```

2. **Install dependencies**

   ```bash
   just install
   # or
   yarn install
   ```

3. **Set up environment variables**

   ```bash
   just setup
   # or manually:
   cp .env.example .env
   # Edit .env with your database endpoints
   ```

4. **Build TypeScript**

   ```bash
   just build
   ```

5. **Start services**

   ```bash
   # Development mode (run in separate terminals)
   just dev-web
   just dev-worker

   # Or see all commands:
   just dev-all
   ```

6. **Access the application**
   - Web Application: http://localhost:3000

## 📁 Project Structure

```
domainr/
├── services/
│   ├── web-app/          # Web application service
│   ├── worker/           # Combined worker service (crawler, ranking-engine, scheduler)
│   └── domain-seeder/    # Domain discovery and seeding service
├── shared/               # Shared utilities and models
│   ├── src/
│   │   ├── database/     # Database clients
│   │   ├── models/       # Data models
│   │   ├── utils/        # Utilities (Logger, Config, Validators)
│   │   ├── queue/        # Job queue management
│   │   └── constants/    # System constants
├── database/             # Database initialization scripts
│   ├── scylla/          # ScyllaDB schema
│   ├── mariadb/         # MariaDB schema
│   └── manticore/       # Manticore search indexes
├── docker-compose.yml    # Production deployment
├── docker-compose.dev.yml # Development environment
└── package.json         # Root package configuration
```

## 🔧 Development

### Local Development Setup

1. **Install dependencies**

   ```bash
   npm run install:all
   ```

2. **Start development environment**

   ```bash
   npm run dev
   ```

3. **Run tests**

   ```bash
   npm run test
   ```

4. **Lint code**
   ```bash
   npm run lint
   npm run lint:fix
   ```

### Service Development

Each service can be developed independently:

```bash
# Web Application
cd services/web-app
npm run dev

# Worker Service (Combined crawler, ranking-engine, scheduler)
cd services/worker
npm run dev

# Domain Seeder Service
cd services/domain-seeder
npm run dev
```

### pnpm Commands Reference

#### Installation

```bash
# Install all dependencies for all workspaces
pnpm install

# Install a dependency to a specific workspace
pnpm --filter domain-ranking-worker add axios
pnpm --filter shared add lodash

# Install a dev dependency
pnpm --filter domain-ranking-worker add -D @types/node
```

#### Running Scripts

```bash
# Run a script in all workspaces
pnpm -r run dev
pnpm -r run test
pnpm -r run build

# Run a script in a specific workspace
pnpm --filter domain-ranking-worker run dev
pnpm --filter shared run test

# Run scripts in parallel
pnpm -r --parallel run dev
```

#### Workspace Management

```bash
# List all workspaces
pnpm -r list --depth=0

# Execute a command in all workspaces
pnpm -r exec pwd
pnpm -r exec ls -la

# Filter workspaces
pnpm --filter "./services/*" run dev
pnpm --filter "domain-ranking-*" run test
```

#### Useful Commands

```bash
# Check outdated packages
pnpm -r outdated

# Update dependencies
pnpm -r update

# Clean node_modules
pnpm -r exec rm -rf node_modules
pnpm install

# Add workspace dependency
pnpm --filter domain-ranking-worker add domain-ranking-shared@workspace:*
```

#### Root Package Scripts

```bash
# Available from root
pnpm dev      # Run dev in all services
pnpm build    # Build all services
pnpm test     # Test all services
pnpm clean    # Clean all services
```

## 📊 Database Schema

### ScyllaDB (Main Data)

- `domain_rankings` - Global and category rankings
- `domain_analysis` - Comprehensive domain analysis data
- `domain_content` - Page content for search indexing
- `domain_crawl_jobs` - Crawl job management
- `domain_traffic_history` - Traffic estimates and trends

### MariaDB (Relational Data)

- `domain_categories` - Domain categorization
- `backlinks` - Backlink analysis data
- `domain_whois` - Domain registration information
- `domain_reviews` - User reviews and ratings

### Manticore Search (Search Indexes)

- `domains_index` - Main domain search index
- `domain_content_index` - Full-text content search
- `domain_reviews_index` - Review search and sentiment

### Database Initialization

The database initialization includes:

- `scylla/` - ScyllaDB initialization scripts
- `mariadb/` - MariaDB initialization scripts
- `manticore/` - Manticore Search initialization scripts
- `migrations/` - Database migration scripts
- `sample-data/` - Sample data for testing and development
- `scripts/` - Utility scripts for database management

#### Docker Compose Setup

The initialization scripts are automatically executed when containers start for the first time.

#### Manual Initialization

```bash
# Initialize ScyllaDB
docker exec -it scylla cqlsh -f /docker-entrypoint-initdb.d/init.cql

# Initialize MariaDB
docker exec -it mariadb mysql -u root -p < /docker-entrypoint-initdb.d/init.sql

# Initialize Manticore
docker exec -it manticore mysql -h127.0.0.1 -P9306 < /docker-entrypoint-initdb.d/init.sql
```

#### Migration Scripts

```bash
# Run migrations
node database/scripts/migrate.js

# Rollback migrations
node database/scripts/migrate.js --rollback

# Check migration status
node database/scripts/migrate.js --status
```

## 🔍 API Endpoints

### Domain Search

```http
GET /api/domains/search?query=example&category=tech&sort=rank
```

### Domain Analysis

```http
GET /api/domains/example.com/analysis
```

### Top Domains

```http
GET /api/domains/top?category=technology&limit=100
```

### Domain Comparison

```http
GET /api/domains/compare?domains=example.com,test.com
```

## 🎯 Ranking Algorithm

The system uses a weighted scoring algorithm:

- **Performance (25%)** - Page speed, Core Web Vitals
- **Security (20%)** - SSL grade, security headers
- **SEO (20%)** - Meta tags, structured data, content quality
- **Technical (15%)** - Technologies, server quality
- **Backlinks (20%)** - Link authority and quality

## 🔧 Configuration

### Environment Variables

Key configuration options in `.env`:

```bash
# Service Ports
WEB_APP_PORT=3000
WORKER_PORT=3001

# Database Configuration
SCYLLA_HOSTS=scylla:9042
MARIA_HOST=mariadb
REDIS_HOST=redis
MANTICORE_HOST=manticore

# Crawling Settings
CRAWL_RATE_LIMIT=60
CRAWL_CONCURRENT_REQUESTS=5
CRAWL_TIMEOUT=30000

# Ranking Weights
RANKING_WEIGHT_PERFORMANCE=0.25
RANKING_WEIGHT_SECURITY=0.20
RANKING_WEIGHT_SEO=0.20
RANKING_WEIGHT_TECHNICAL=0.15
RANKING_WEIGHT_BACKLINKS=0.20
```

## 📈 Monitoring and Health Checks

### Health Check Endpoints

- Web App: `GET /health`
- All services include health check endpoints

### Health Checks

```bash
# Check all services
just health

# Individual service health
curl http://localhost/health
```

### Logging

- Centralized logging with Pino
- Log files in `./logs/` directory
- Structured JSON logging in production

```bash
# All services
just logs

# Specific service
just logs web-app
just logs worker
```

### Metrics and Monitoring

- **Prometheus**: <http://localhost:9090>
- **Grafana**: <http://localhost:3001>

Default Grafana credentials: admin/admin

```bash
# View logs
npm run logs

# Health check
npm run health

# Service statistics
curl http://localhost:3000/health
```

### Backup and Recovery

#### Automated Backups

```bash
# Create backup
just backup

# Backups are stored in ./backups/
```

#### Manual Backup

```bash
# Run backup script directly
./scripts/backup.sh
```

#### Backup Contents

- ScyllaDB snapshots
- MariaDB dumps
- Redis data
- Manticore indexes
- Configuration files

### Scaling

#### Horizontal Scaling

```bash
# Scale specific services
docker-compose up -d --scale worker=3
docker-compose up -d --scale web-app=2
```

#### Resource Limits

Resource limits are configured in docker-compose.yml:

```yaml
deploy:
  resources:
    limits:
      memory: 2G
      cpus: "2.0"
    reservations:
      memory: 1G
      cpus: "1.0"
```

## 🛠️ Troubleshooting

### Common Issues

1. **Services not starting**

   ```bash
   # Check logs
   just logs [service-name]

   # Check resource usage
   docker stats
   ```

2. **Database connection errors**

   ```bash
   # Restart databases
   docker-compose restart scylla mariadb redis

   # Check database logs
   just logs scylla
   ```

3. **SSL certificate issues**

   ```bash
   # Recreate certificates
   just ssl-create
   ```

### Health Check Failures

```bash
# Check service status
just ps

# Perform health checks
just deploy-health

# View detailed logs
just logs
```

### Performance Issues

```bash
# Check resource usage
just top

# Scale services
docker-compose up -d --scale worker=2
```

### Maintenance

#### Updates

```bash
# Pull latest changes
git pull

# Rebuild and deploy
just build-no-cache
just deploy
```

#### Cleanup

```bash
# Clean unused resources
just clean

# Full cleanup (removes all data)
just clean-all
```

#### Database Maintenance

```bash
# Check migration status
just db-status

# Run new migrations
just db-migrate
```

### Production Checklist

- [ ] Environment variables configured
- [ ] SSL certificates installed
- [ ] Firewall configured
- [ ] Monitoring set up
- [ ] Backup strategy implemented
- [ ] Log rotation configured
- [ ] Resource limits set
- [ ] Health checks passing
- [ ] Performance tested

## 🔒 Security

- Non-root Docker containers
- Input validation with Joi
- Rate limiting for crawling
- CORS configuration
- Helmet.js security headers
- Environment variable secrets

## 🧪 Testing

### Unit Tests

```bash
npm run test
npm run test:shared
npm run test:web-app
```

### Integration Tests

```bash
npm run test:integration
```

### Load Testing

```bash
npm run test:load
```

## 📝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run linting and tests
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

- Documentation: [Wiki](link-to-wiki)
- Issues: [GitHub Issues](link-to-issues)
- Discussions: [GitHub Discussions](link-to-discussions)

## 🗺️ Roadmap

- [ ] AI-powered domain descriptions
- [ ] Advanced performance auditing
- [ ] Mobile app interface
- [ ] API rate limiting and authentication
- [ ] Multi-language support
- [ ] Advanced analytics dashboard
