/*
 * Compare effective ESLint rules between new and legacy configs (excluding jsdoc/*)
 * Usage:
 *   pnpm exec tsx scripts/compare-eslint-configs.ts [file1 file2 ...]
 * If no files are provided, a representative set across services is used.
 */

import { execSync } from 'node:child_process';
import { resolve } from 'node:path';
import { createRequire } from 'node:module';

type RulesMapType = Record<string, unknown>;

type CompareResultType =
{
    file: string;
    diffs: Array<{
        rule: string;
        newVal: unknown;
        oldVal: unknown;
    }>;
};

function normalizeSeverity(s: unknown): 0 | 1 | 2
{
    if (typeof s === 'number')
    {
        if (s <= 0)
        {
            return 0;
        }
        if (s === 1)
        {
            return 1;
        }
        return 2;
    }
    if (typeof s === 'string')
    {
        const v = s.toLowerCase();
        if (v === 'off' || v === '0' || v === 'false')
        {
            return 0;
        }
        if (v === 'warn' || v === 'warning' || v === '1' || v === 'true')
        {
            return 1;
        }
        return 2; // 'error' and everything else -> error
    }
    return 0;
}

function normalizeRuleValue(val: unknown): { level: 0 | 1 | 2; options?: unknown }
{
    if (val == null)
    {
        return { level: 0 };
    }

    if (Array.isArray(val))
    {
        const level = normalizeSeverity(val[0]);
        if (level === 0)
        {
            return { level: 0 };
        }
        // Keep options as-is for enabled rules
        return { level, options: val.slice(1) };
    }

    // If the rule is provided as a scalar severity
    const level = normalizeSeverity(val);
    return level === 0 ? { level: 0 } : { level };
}

function toNormalizedMap(rules: RulesMapType): RulesMapType
{
    const out: RulesMapType = {};
    for (const [k, v] of Object.entries(rules))
    {
        out[k] = normalizeRuleValue(v);
    }
    return out;
}

function safeParseJson(output: string): any
{
    // Some plugins print notices; find first JSON object start
    const start = output.indexOf('{');
    const end = output.lastIndexOf('}');
    if (start === -1 || end === -1 || end <= start)
    {
        throw new Error('Failed to locate JSON in ESLint --print-config output');
    }
    const jsonSegment = output.slice(start, end + 1);
    return JSON.parse(jsonSegment);
}

function getRules(configPath: string, filePath: string): RulesMapType
{
    const cmd = `pnpm exec eslint -c ${configPath} --print-config ${JSON.stringify(filePath)}`;
    const stdout = execSync(cmd, { encoding: 'utf8', stdio: ['ignore', 'pipe', 'pipe'] });
    const cfg = safeParseJson(stdout);
    const rules: RulesMapType = cfg.rules ?? {};

    // Filter out JSDoc-specific rules
    const filtered: RulesMapType = Object.fromEntries(
        Object.entries(rules).filter(([key]) => !key.startsWith('jsdoc/'))
    );

    return filtered;
}

function compareRules(newRules: RulesMapType, oldRules: RulesMapType): Array<{
    rule: string;
    newVal: unknown;
    oldVal: unknown;
}>
{
    const normNew = toNormalizedMap(newRules);
    const normOld = toNormalizedMap(oldRules);
    const keys = new Set<string>([...Object.keys(normNew), ...Object.keys(normOld)]);
    const diffs: Array<{ rule: string; newVal: unknown; oldVal: unknown }> = [];

    for (const key of Array.from(keys).sort())
    {
        const aRaw = normNew[key];
        const bRaw = normOld[key];
        const a = aRaw === undefined ? { level: 0 } : aRaw;
        const b = bRaw === undefined ? { level: 0 } : bRaw;
        if (JSON.stringify(a) !== JSON.stringify(b))
        {
            diffs.push({ rule: key, newVal: a, oldVal: b });
        }
    }

    return diffs;
}

function runForFile(file: string): CompareResultType
{
    const abs = resolve(process.cwd(), file);
    const newRules = getRules('eslint.config.new.js', abs);
    const oldRules = getRules('eslint.config.js', abs);
    const diffs = compareRules(newRules, oldRules);
    return { file, diffs };
}

function main(): void
{
    const args = process.argv.slice(2);

    // Load legacy ignore patterns and skip those files for a fair comparison
    const req = createRequire(import.meta.url);
    // scripts/compare-eslint-configs.ts -> root/.eslintrc.js
    const legacy = req('../.eslintrc.js');
    const ignorePatterns: string[] = Array.isArray(legacy?.ignorePatterns) ? legacy.ignorePatterns : [];

    function isIgnoredByLegacy(relPath: string): boolean
    {
        const p = relPath.replace(/^\.\/?/, '');
        for (const pat of ignorePatterns)
        {
            if (typeof pat !== 'string')
            {
                continue;
            }
            if (pat.endsWith('/**'))
            {
                const pref = pat.slice(0, -3);
                if (p.startsWith(pref))
                {
                    return true;
                }
            }
            else if (pat.endsWith('/'))
            {
                if (p.startsWith(pat))
                {
                    return true;
                }
            }
        }
        return false;
    }

    const targets = args.length > 0
        ? args
        : [
            'shared/src/utils/Logger.ts',
            'shared/src/utils/Validators.ts',
            'services/worker/src/crawler/analyzers/DomainInfoAnalyzer.ts',
            'services/domain-seeder/src/content/LiveContentAnalyzer.ts',
            'services/domain-seeder/src/commands/describe.ts',
            'services/ranking-engine/src/algorithms/SEOScorer.ts',
            'services/ranking-engine/src/services/RankingUpdateService.ts',
            'services/admin/src/components/Analytics/ReportGenerator.tsx',
            'services/admin/src/app/page.tsx',
        ];

    const results: CompareResultType[] = [];
    let totalDiffs = 0;

    for (const file of targets)
    {
        if (isIgnoredByLegacy(file))
        {
            console.log(`SKIP (ignored by legacy): ${file}`);
            continue;
        }
        try
        {
            const res = runForFile(file);
            results.push(res);
            totalDiffs += res.diffs.length;
        }
        catch (err)
        {
            const message = err instanceof Error ? err.message : String(err);
            results.push({ file, diffs: [{ rule: '__error__', newVal: message, oldVal: null }] });
            totalDiffs += 1;
        }
    }

    for (const res of results)
    {
        if (res.diffs.length === 0)
        {
            console.log(`MATCH: ${res.file}`);
            continue;
        }

        console.log(`DIFF (${res.diffs.length}): ${res.file}`);
        for (const d of res.diffs)
        {
            if (d.rule === '__error__')
            {
                console.log(`  ! Error: ${d.newVal}`);
                continue;
            }
            console.log(`  - ${d.rule}`);
            console.log(`    new: ${JSON.stringify(d.newVal)}`);
            console.log(`    old: ${JSON.stringify(d.oldVal)}`);
        }
    }

    if (totalDiffs === 0)
    {
        console.log('\nSummary: All checked files MATCH (excluding jsdoc/*). Migration looks good.');
        process.exit(0);
    }
    else
    {
        console.log(`\nSummary: Found differences in ${totalDiffs} rule entries across ${results.length} files (excluding jsdoc/*).`);
        process.exit(1);
    }
}

main();
