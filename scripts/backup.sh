#!/bin/bash

# Domain Ranking System Backup Script
# Creates backups of all databases and important data

set -e

# Configuration
BACKUP_DIR="${BACKUP_DIR:-./backups}"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="domain_ranking_backup_${DATE}"
RETENTION_DAYS="${RETENTION_DAYS:-7}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

# Create backup directory
mkdir -p "${BACKUP_DIR}/${BACKUP_NAME}"

log "Starting backup process..."

# Backup ScyllaDB
log "Backing up ScyllaDB..."
if docker exec domain-ranking-scylla nodetool snapshot domain_ranking; then
    docker exec domain-ranking-scylla tar -czf "/tmp/scylla_backup_${DATE}.tar.gz" -C /var/lib/scylla/data/domain_ranking .
    docker cp "domain-ranking-scylla:/tmp/scylla_backup_${DATE}.tar.gz" "${BACKUP_DIR}/${BACKUP_NAME}/"
    docker exec domain-ranking-scylla rm "/tmp/scylla_backup_${DATE}.tar.gz"
    log "ScyllaDB backup completed"
else
    error "ScyllaDB backup failed"
fi

# Backup MariaDB
log "Backing up MariaDB..."
if docker exec domain-ranking-mariadb mysqldump -u root -p"${MARIA_PASSWORD}" --all-databases --single-transaction --routines --triggers > "${BACKUP_DIR}/${BACKUP_NAME}/mariadb_backup_${DATE}.sql"; then
    log "MariaDB backup completed"
else
    error "MariaDB backup failed"
fi

# Backup Redis
log "Backing up Redis..."
if docker exec domain-ranking-redis redis-cli BGSAVE; then
    sleep 5  # Wait for background save to complete
    docker cp domain-ranking-redis:/data/dump.rdb "${BACKUP_DIR}/${BACKUP_NAME}/redis_backup_${DATE}.rdb"
    log "Redis backup completed"
else
    error "Redis backup failed"
fi

# Backup Manticore Search indexes
log "Backing up Manticore Search..."
if docker exec domain-ranking-manticore tar -czf "/tmp/manticore_backup_${DATE}.tar.gz" -C /var/lib/manticore .; then
    docker cp "domain-ranking-manticore:/tmp/manticore_backup_${DATE}.tar.gz" "${BACKUP_DIR}/${BACKUP_NAME}/"
    docker exec domain-ranking-manticore rm "/tmp/manticore_backup_${DATE}.tar.gz"
    log "Manticore Search backup completed"
else
    error "Manticore Search backup failed"
fi

# Backup configuration files
log "Backing up configuration files..."
cp -r nginx/ "${BACKUP_DIR}/${BACKUP_NAME}/"
cp -r monitoring/ "${BACKUP_DIR}/${BACKUP_NAME}/"
cp docker-compose.yml "${BACKUP_DIR}/${BACKUP_NAME}/"
cp .env "${BACKUP_DIR}/${BACKUP_NAME}/" 2>/dev/null || warn ".env file not found"

# Create backup metadata
cat > "${BACKUP_DIR}/${BACKUP_NAME}/backup_info.txt" << EOF
Backup Information
==================
Date: $(date)
Backup Name: ${BACKUP_NAME}
System: Domain Ranking System
Version: $(git describe --tags --always 2>/dev/null || echo "unknown")
Docker Compose Version: $(docker-compose --version)

Services Backed Up:
- ScyllaDB (domain_ranking keyspace)
- MariaDB (all databases)
- Redis (dump.rdb)
- Manticore Search (indexes)
- Configuration files

Backup Size: $(du -sh "${BACKUP_DIR}/${BACKUP_NAME}" | cut -f1)
EOF

# Compress the entire backup
log "Compressing backup..."
cd "${BACKUP_DIR}"
tar -czf "${BACKUP_NAME}.tar.gz" "${BACKUP_NAME}/"
rm -rf "${BACKUP_NAME}/"

log "Backup completed: ${BACKUP_DIR}/${BACKUP_NAME}.tar.gz"

# Clean up old backups
log "Cleaning up old backups (keeping last ${RETENTION_DAYS} days)..."
find "${BACKUP_DIR}" -name "domain_ranking_backup_*.tar.gz" -mtime +${RETENTION_DAYS} -delete

log "Backup process completed successfully!"

# Optional: Upload to cloud storage
if [ -n "${BACKUP_UPLOAD_COMMAND}" ]; then
    log "Uploading backup to cloud storage..."
    eval "${BACKUP_UPLOAD_COMMAND} ${BACKUP_DIR}/${BACKUP_NAME}.tar.gz"
fi
