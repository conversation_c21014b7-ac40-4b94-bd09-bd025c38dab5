#!/bin/bash

# Docker configuration validation script for domain-seeder service
# Usage: ./scripts/validate-docker-config.sh

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Validation results
VALIDATION_ERRORS=0
VALIDATION_WARNINGS=0

# Add error
add_error() {
    log_error "$1"
    ((VALIDATION_ERRORS++))
}

# Add warning
add_warning() {
    log_warning "$1"
    ((VALIDATION_WARNINGS++))
}

# Check if Docker is available
check_docker() {
    log_info "Checking Docker installation..."

    if ! command -v docker &> /dev/null; then
        add_error "Docker is not installed"
        return 1
    fi

    if ! docker info &> /dev/null; then
        add_error "Docker daemon is not running"
        return 1
    fi

    # Check Docker version
    local docker_version=$(docker --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' | head -1)
    local required_version="20.10.0"

    if ! printf '%s\n%s\n' "$required_version" "$docker_version" | sort -V -C; then
        add_warning "Docker version $docker_version is older than recommended $required_version"
    fi

    log_success "Docker is available (version $docker_version)"
}

# Check Docker Compose
check_docker_compose() {
    log_info "Checking Docker Compose..."

    if command -v docker-compose &> /dev/null; then
        local compose_version=$(docker-compose --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' | head -1)
        log_success "Docker Compose is available (version $compose_version)"
    elif docker compose version &> /dev/null; then
        local compose_version=$(docker compose version --short)
        log_success "Docker Compose V2 is available (version $compose_version)"
    else
        add_error "Docker Compose is not available"
        return 1
    fi
}

# Validate Dockerfile
validate_dockerfile() {
    log_info "Validating Dockerfile..."

    local dockerfile="$PROJECT_ROOT/services/domain-seeder/Dockerfile"

    if [[ ! -f "$dockerfile" ]]; then
        add_error "Dockerfile not found: $dockerfile"
        return 1
    fi

    # Check for security best practices
    if ! grep -q "USER seeder" "$dockerfile"; then
        add_error "Dockerfile should run as non-root user"
    fi

    if ! grep -q "HEALTHCHECK" "$dockerfile"; then
        add_warning "Dockerfile missing HEALTHCHECK instruction"
    fi

    if ! grep -q "dumb-init" "$dockerfile"; then
        add_warning "Dockerfile should use dumb-init for proper signal handling"
    fi

    # Check for multi-stage build
    if ! grep -q "FROM.*AS.*" "$dockerfile"; then
        add_warning "Dockerfile should use multi-stage build for optimization"
    fi

    # Check for proper COPY ownership
    if ! grep -q "COPY --chown=" "$dockerfile"; then
        add_warning "Dockerfile should use --chown with COPY instructions"
    fi

    log_success "Dockerfile validation completed"
}

# Validate docker-compose.yml
validate_docker_compose() {
    log_info "Validating docker-compose.yml..."

    local compose_file="$PROJECT_ROOT/docker-compose.yml"

    if [[ ! -f "$compose_file" ]]; then
        add_error "docker-compose.yml not found: $compose_file"
        return 1
    fi

    # Validate compose file syntax (use docker compose instead of docker-compose)
    if command -v docker-compose &> /dev/null; then
        if ! docker-compose -f "$compose_file" config &> /dev/null; then
            add_error "docker-compose.yml has syntax errors"
            return 1
        fi
    elif ! docker compose -f "$compose_file" config &> /dev/null; then
        add_error "docker-compose.yml has syntax errors"
        return 1
    fi

    # Get compose config output
    local compose_config
    if command -v docker-compose &> /dev/null; then
        compose_config=$(docker-compose -f "$compose_file" config 2>/dev/null)
    else
        compose_config=$(docker compose -f "$compose_file" config 2>/dev/null)
    fi

    # Check if domain-seeder service is defined
    if ! echo "$compose_config" | grep -q "domain-seeder:"; then
        add_error "domain-seeder service not found in docker-compose.yml"
    fi

    # Check for required services
    local required_services=("scylla" "mariadb" "redis" "manticore")
    for service in "${required_services[@]}"; do
        if ! echo "$compose_config" | grep -q "$service:"; then
            add_error "Required service '$service' not found in docker-compose.yml"
        fi
    done

    # Check for resource limits (look for limits in the domain-seeder section)
    if ! echo "$compose_config" | sed -n '/domain-seeder:/,/^  [a-z]/p' | grep -q "limits:"; then
        add_warning "domain-seeder service should have resource limits defined"
    fi

    # Check for health checks (look for healthcheck in the domain-seeder section)
    if ! echo "$compose_config" | sed -n '/domain-seeder:/,/^  [a-z]/p' | grep -q "healthcheck:"; then
        add_warning "domain-seeder service should have healthcheck defined"
    fi

    log_success "docker-compose.yml validation completed"
}

# Validate development compose file
validate_dev_compose() {
    log_info "Validating docker-compose.dev.yml..."

    local dev_compose_file="$PROJECT_ROOT/docker-compose.dev.yml"

    if [[ ! -f "$dev_compose_file" ]]; then
        add_warning "docker-compose.dev.yml not found (optional for development)"
        return 0
    fi

    # Validate dev compose file syntax
    if command -v docker-compose &> /dev/null; then
        if ! docker-compose -f "$PROJECT_ROOT/docker-compose.yml" -f "$dev_compose_file" config &> /dev/null; then
            add_error "docker-compose.dev.yml has syntax errors or conflicts"
            return 1
        fi
    elif ! docker compose -f "$PROJECT_ROOT/docker-compose.yml" -f "$dev_compose_file" config &> /dev/null; then
        add_error "docker-compose.dev.yml has syntax errors or conflicts"
        return 1
    fi

    # Check for development-specific overrides
    if ! grep -q "target: development" "$dev_compose_file"; then
        add_warning "Development compose should target development stage"
    fi

    if ! grep -q "ports:" "$dev_compose_file"; then
        add_warning "Development compose should expose ports for debugging"
    fi

    log_success "docker-compose.dev.yml validation completed"
}

# Validate environment configuration
validate_environment() {
    log_info "Validating environment configuration..."

    local env_example="$PROJECT_ROOT/.env.example"

    if [[ ! -f "$env_example" ]]; then
        add_error ".env.example not found: $env_example"
        return 1
    fi

    # Check for required environment variables
    local required_vars=(
        "NODE_ENV"
        "MARIA_PASSWORD"
        "SCYLLA_KEYSPACE"
        "SEEDER_PORT"
        "SEEDER_MAX_NEW_PER_DAY"
    )

    for var in "${required_vars[@]}"; do
        if ! grep -q "^$var=" "$env_example"; then
            add_error "Required environment variable '$var' not found in .env.example"
        fi
    done

    # Check for domain-seeder specific variables
    local seeder_vars=(
        "SEEDER_ENQUEUE_BATCH"
        "SEEDER_ENQUEUE_INTERVAL"
        "SEEDER_QUEUE_MAX_DEPTH"
        "SEEDER_DB_CHECK_BATCH"
    )

    for var in "${seeder_vars[@]}"; do
        if ! grep -q "^$var=" "$env_example"; then
            add_warning "Domain-seeder variable '$var' not found in .env.example"
        fi
    done

    log_success "Environment configuration validation completed"
}

# Validate deployment scripts
validate_scripts() {
    log_info "Validating deployment scripts..."

    local scripts=(
        "$PROJECT_ROOT/scripts/deploy-domain-seeder.sh"
        "$PROJECT_ROOT/scripts/setup-environment.sh"
    )

    for script in "${scripts[@]}"; do
        if [[ ! -f "$script" ]]; then
            add_error "Deployment script not found: $script"
            continue
        fi

        if [[ ! -x "$script" ]]; then
            add_warning "Deployment script not executable: $script"
        fi

        # Basic syntax check
        if ! bash -n "$script" &> /dev/null; then
            add_error "Deployment script has syntax errors: $script"
        fi
    done

    log_success "Deployment scripts validation completed"
}

# Validate documentation
validate_documentation() {
    log_info "Validating documentation..."

    local docs=(
        "$PROJECT_ROOT/services/domain-seeder/DEPLOYMENT.md"
        "$PROJECT_ROOT/services/domain-seeder/TROUBLESHOOTING.md"
    )

    for doc in "${docs[@]}"; do
        if [[ ! -f "$doc" ]]; then
            add_warning "Documentation file not found: $doc"
        fi
    done

    log_success "Documentation validation completed"
}

# Test Docker build
test_docker_build() {
    log_info "Testing Docker build..."

    cd "$PROJECT_ROOT"

    # Test production build
    if docker build -f services/domain-seeder/Dockerfile --target production -t domain-seeder-test . &> /dev/null; then
        log_success "Production Docker build successful"
        docker rmi domain-seeder-test &> /dev/null || true
    else
        add_error "Production Docker build failed"
    fi

    # Test development build
    if docker build -f services/domain-seeder/Dockerfile --target development -t domain-seeder-dev-test . &> /dev/null; then
        log_success "Development Docker build successful"
        docker rmi domain-seeder-dev-test &> /dev/null || true
    else
        add_error "Development Docker build failed"
    fi
}

# Test compose configuration
test_compose_config() {
    log_info "Testing Docker Compose configuration..."

    cd "$PROJECT_ROOT"

    # Test production compose
    if command -v docker-compose &> /dev/null; then
        if docker-compose config &> /dev/null; then
            log_success "Production compose configuration valid"
        else
            add_error "Production compose configuration invalid"
        fi
    elif docker compose config &> /dev/null; then
        log_success "Production compose configuration valid"
    else
        add_error "Production compose configuration invalid"
    fi

    # Test development compose
    if [[ -f "docker-compose.dev.yml" ]]; then
        if command -v docker-compose &> /dev/null; then
            if docker-compose -f docker-compose.yml -f docker-compose.dev.yml config &> /dev/null; then
                log_success "Development compose configuration valid"
            else
                add_error "Development compose configuration invalid"
            fi
        elif docker compose -f docker-compose.yml -f docker-compose.dev.yml config &> /dev/null; then
            log_success "Development compose configuration valid"
        else
            add_error "Development compose configuration invalid"
        fi
    fi
}

# Check system resources
check_system_resources() {
    log_info "Checking system resources..."

    # Check available memory (cross-platform)
    local available_memory
    local required_memory=8192  # 8GB

    if command -v free &> /dev/null; then
        # Linux
        available_memory=$(free -m | awk 'NR==2{printf "%.0f", $7}')
    elif [[ "$(uname)" == "Darwin" ]]; then
        # macOS
        local total_memory=$(sysctl -n hw.memsize)
        available_memory=$((total_memory / 1024 / 1024))  # Convert to MB
    else
        available_memory=0
        add_warning "Cannot determine available memory on this platform"
    fi

    if [[ $available_memory -gt 0 ]]; then
        if [[ $available_memory -lt $required_memory ]]; then
            add_warning "Available memory ($available_memory MB) is less than recommended ($required_memory MB)"
        else
            log_success "Sufficient memory available ($available_memory MB)"
        fi
    fi

    # Check available disk space
    local available_disk
    local required_disk=51200  # 50GB

    if [[ "$(uname)" == "Darwin" ]]; then
        # macOS
        available_disk=$(df -m . | awk 'NR==2{print $4}')
    else
        # Linux
        available_disk=$(df -m . | awk 'NR==2{print $4}')
    fi

    if [[ $available_disk -lt $required_disk ]]; then
        add_warning "Available disk space ($available_disk MB) is less than recommended ($required_disk MB)"
    else
        log_success "Sufficient disk space available ($available_disk MB)"
    fi

    # Check CPU cores (cross-platform)
    local cpu_cores
    local required_cores=2

    if command -v nproc &> /dev/null; then
        # Linux
        cpu_cores=$(nproc)
    elif [[ "$(uname)" == "Darwin" ]]; then
        # macOS
        cpu_cores=$(sysctl -n hw.ncpu)
    else
        cpu_cores=1
        add_warning "Cannot determine CPU cores on this platform"
    fi

    if [[ $cpu_cores -lt $required_cores ]]; then
        add_warning "CPU cores ($cpu_cores) is less than recommended ($required_cores)"
    else
        log_success "Sufficient CPU cores available ($cpu_cores)"
    fi
}

# Generate validation report
generate_report() {
    echo ""
    echo "=================================="
    echo "Docker Configuration Validation Report"
    echo "=================================="
    echo ""

    if [[ $VALIDATION_ERRORS -eq 0 && $VALIDATION_WARNINGS -eq 0 ]]; then
        log_success "All validations passed! Docker configuration is ready for deployment."
    elif [[ $VALIDATION_ERRORS -eq 0 ]]; then
        log_warning "Validation completed with $VALIDATION_WARNINGS warning(s). Configuration is usable but could be improved."
    else
        log_error "Validation failed with $VALIDATION_ERRORS error(s) and $VALIDATION_WARNINGS warning(s). Please fix errors before deployment."
    fi

    echo ""
    echo "Summary:"
    echo "- Errors: $VALIDATION_ERRORS"
    echo "- Warnings: $VALIDATION_WARNINGS"
    echo ""

    if [[ $VALIDATION_ERRORS -eq 0 ]]; then
        echo "Next steps:"
        echo "1. Review any warnings and consider improvements"
        echo "2. Set up environment: ./scripts/setup-environment.sh"
        echo "3. Deploy service: ./scripts/deploy-domain-seeder.sh"
    else
        echo "Required actions:"
        echo "1. Fix all validation errors listed above"
        echo "2. Re-run this validation script"
        echo "3. Proceed with deployment once all errors are resolved"
    fi
}

# Main validation function
main() {
    log_info "Starting Docker configuration validation for domain-seeder service"
    echo ""

    check_docker
    check_docker_compose
    validate_dockerfile
    validate_docker_compose
    validate_dev_compose
    validate_environment
    validate_scripts
    validate_documentation
    check_system_resources

    # Only run build tests if basic validations pass
    if [[ $VALIDATION_ERRORS -eq 0 ]]; then
        test_docker_build
        test_compose_config
    else
        log_warning "Skipping build tests due to validation errors"
    fi

    generate_report

    # Exit with appropriate code
    if [[ $VALIDATION_ERRORS -gt 0 ]]; then
        exit 1
    else
        exit 0
    fi
}

# Run validation
main "$@"
