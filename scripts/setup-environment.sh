#!/bin/bash

# Environment setup script for domain-seeder service
# Usage: ./scripts/setup-environment.sh [development|production]

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ENVIRONMENT="${1:-development}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Create .env file from template
create_env_file() {
    log_info "Setting up environment file for $ENVIRONMENT..."

    local env_file="$PROJECT_ROOT/.env"
    local env_example="$PROJECT_ROOT/.env.example"

    if [[ -f "$env_file" ]]; then
        log_warning ".env file already exists. Creating backup..."
        cp "$env_file" "$env_file.backup.$(date +%Y%m%d_%H%M%S)"
    fi

    if [[ ! -f "$env_example" ]]; then
        log_info "Creating .env.example template..."
        create_env_template
    fi

    # Copy template to .env
    cp "$env_example" "$env_file"

    # Update environment-specific values
    if [[ "$ENVIRONMENT" == "development" ]]; then
        setup_development_env "$env_file"
    else
        setup_production_env "$env_file"
    fi

    log_success "Environment file created: $env_file"
}

# Create .env.example template
create_env_template() {
    cat > "$PROJECT_ROOT/.env.example" << 'EOF'
# Environment Configuration
NODE_ENV=production
COMPOSE_PROJECT_NAME=domain-ranking

# Service Ports
HTTP_PORT=80
HTTPS_PORT=443
WEB_APP_PORT=3000
WORKER_PORT=3001
SEEDER_PORT=3004

# Database Configuration
SCYLLA_KEYSPACE=domain_ranking
SCYLLA_PORT=9042
SCYLLA_API_PORT=10000

MARIA_HOST=localhost
MARIA_PORT=3306
MARIA_USER=root
MARIA_PASSWORD=your_secure_password_here
MARIA_DATABASE=domain_ranking

REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

MANTICORE_HOST=localhost
MANTICORE_PORT=9308
MANTICORE_BINARY_PORT=9312

# Domain Seeder Configuration
SEEDER_MAX_NEW_PER_DAY=500000
SEEDER_ENQUEUE_BATCH=1000
SEEDER_ENQUEUE_INTERVAL=1000
SEEDER_QUEUE_MAX_DEPTH=200000
SEEDER_DB_CHECK_BATCH=5000
SEEDER_BLOOM_FP_RATE=0.01

# External API Keys (Optional)
CZDS_USERNAME=
CZDS_PASSWORD=
CLOUDFLARE_API_KEY=
UMBRELLA_API_KEY=
PIR_API_KEY=
RAPID7_API_KEY=

# AI Provider API Keys (Optional)
OPENAI_API_KEYS=
CLAUDE_API_KEYS=
GEMINI_API_KEYS=
OPENROUTER_API_KEYS=

# Proxy Configuration (Optional)
PROXY_IPS=
PROXY_ROTATION_ENABLED=false

# Monitoring Configuration
PROMETHEUS_PORT=9090
GRAFANA_PORT=3001
GRAFANA_PASSWORD=admin
BROWSERLESS_PORT=3000
BROWSERLESS_MAX_SESSIONS=5
BROWSERLESS_TIMEOUT=60000
BROWSERLESS_QUEUE=10

# Logging Configuration
LOG_LEVEL=info
METRICS_ENABLED=true
HEALTH_CHECK_INTERVAL=30

# Security Configuration
JWT_SECRET=your_jwt_secret_here
CORS_ORIGIN=*

# SSL Configuration (Production)
SSL_CERT_PATH=
SSL_KEY_PATH=
EOF
}

# Setup development environment
setup_development_env() {
    local env_file="$1"

    log_info "Configuring for development environment..."

    # Update development-specific values
    sed -i.bak \
        -e 's/NODE_ENV=production/NODE_ENV=development/' \
        -e 's/LOG_LEVEL=info/LOG_LEVEL=debug/' \
        -e 's/SEEDER_MAX_NEW_PER_DAY=500000/SEEDER_MAX_NEW_PER_DAY=10000/' \
        -e 's/SEEDER_ENQUEUE_BATCH=1000/SEEDER_ENQUEUE_BATCH=100/' \
        -e 's/SEEDER_ENQUEUE_INTERVAL=1000/SEEDER_ENQUEUE_INTERVAL=2000/' \
        -e 's/SEEDER_QUEUE_MAX_DEPTH=200000/SEEDER_QUEUE_MAX_DEPTH=5000/' \
        -e 's/SEEDER_DB_CHECK_BATCH=5000/SEEDER_DB_CHECK_BATCH=1000/' \
        -e 's/HEALTH_CHECK_INTERVAL=30/HEALTH_CHECK_INTERVAL=10/' \
        "$env_file"

    # Add development-specific ports
    cat >> "$env_file" << 'EOF'

# Development Port Overrides
WEB_APP_DEV_PORT=3000
WORKER_DEV_PORT=3001
SEEDER_DEV_PORT=3004
SCYLLA_DEV_PORT=9042
SCYLLA_API_DEV_PORT=10000
MARIA_DEV_PORT=3306
REDIS_DEV_PORT=6379
MANTICORE_DEV_PORT=9308
MANTICORE_BINARY_DEV_PORT=9312
PROMETHEUS_DEV_PORT=9090
GRAFANA_DEV_PORT=3001
EOF

    # Remove backup file
    rm -f "$env_file.bak"
}

# Setup production environment
setup_production_env() {
    local env_file="$1"

    log_info "Configuring for production environment..."

    # Generate secure passwords
    local maria_password=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
    local jwt_secret=$(openssl rand -base64 64 | tr -d "=+/" | cut -c1-50)
    local grafana_password=$(openssl rand -base64 16 | tr -d "=+/" | cut -c1-12)

    # Update production-specific values
    sed -i.bak \
        -e "s/MARIA_PASSWORD=your_secure_password_here/MARIA_PASSWORD=$maria_password/" \
        -e "s/JWT_SECRET=your_jwt_secret_here/JWT_SECRET=$jwt_secret/" \
        -e "s/GRAFANA_PASSWORD=admin/GRAFANA_PASSWORD=$grafana_password/" \
        -e 's/CORS_ORIGIN=\*/CORS_ORIGIN=https:\/\/yourdomain.com/' \
        "$env_file"

    # Remove backup file
    rm -f "$env_file.bak"

    log_warning "Generated secure passwords. Please save these credentials:"
    log_warning "MariaDB Password: $maria_password"
    log_warning "JWT Secret: $jwt_secret"
    log_warning "Grafana Password: $grafana_password"
}

# Create necessary directories
create_directories() {
    log_info "Creating necessary directories..."

    local directories=(
        "$PROJECT_ROOT/logs"
        "$PROJECT_ROOT/logs/domain-seeder"
        "$PROJECT_ROOT/logs/nginx"
        "$PROJECT_ROOT/logs/scylla"
        "$PROJECT_ROOT/logs/mariadb"
        "$PROJECT_ROOT/logs/redis"
        "$PROJECT_ROOT/logs/manticore"
        "$PROJECT_ROOT/data/backups"
        "$PROJECT_ROOT/nginx/ssl"
    )

    for dir in "${directories[@]}"; do
        if [[ ! -d "$dir" ]]; then
            mkdir -p "$dir"
            log_info "Created directory: $dir"
        fi
    done

    # Set proper permissions
    chmod 755 "$PROJECT_ROOT/logs"
    chmod 755 "$PROJECT_ROOT/data"

    log_success "Directories created successfully"
}

# Setup Docker networks
setup_docker_networks() {
    log_info "Setting up Docker networks..."

    # Check if network already exists
    if docker network ls | grep -q "domain-ranking-network"; then
        log_info "Docker network already exists"
    else
        docker network create \
            --driver bridge \
            --subnet=172.20.0.0/16 \
            domain-ranking-network
        log_success "Docker network created"
    fi
}

# Initialize databases
initialize_databases() {
    log_info "Initializing databases..."

    cd "$PROJECT_ROOT"

    # Start database services
    log_info "Starting database services..."
    docker-compose up -d scylla mariadb redis manticore

    # Wait for services to be ready
    log_info "Waiting for databases to initialize..."
    sleep 60

    # Check if databases are ready
    local max_attempts=30
    local attempt=1

    # Wait for ScyllaDB
    while [[ $attempt -le $max_attempts ]]; do
        if docker-compose exec -T scylla cqlsh -e "DESCRIBE KEYSPACES;" &> /dev/null; then
            log_success "ScyllaDB is ready"
            break
        fi

        if [[ $attempt -eq $max_attempts ]]; then
            log_error "ScyllaDB failed to start"
            return 1
        fi

        log_info "Waiting for ScyllaDB... (attempt $attempt/$max_attempts)"
        sleep 10
        ((attempt++))
    done

    # Wait for MariaDB
    attempt=1
    while [[ $attempt -le $max_attempts ]]; do
        if docker-compose exec -T mariadb mysql -u root -p"${MARIA_PASSWORD}" -e "SELECT 1;" &> /dev/null; then
            log_success "MariaDB is ready"
            break
        fi

        if [[ $attempt -eq $max_attempts ]]; then
            log_error "MariaDB failed to start"
            return 1
        fi

        log_info "Waiting for MariaDB... (attempt $attempt/$max_attempts)"
        sleep 5
        ((attempt++))
    done

    log_success "Databases initialized successfully"
}

# Validate setup
validate_setup() {
    log_info "Validating setup..."

    # Check if .env file exists and has required variables
    local env_file="$PROJECT_ROOT/.env"
    if [[ ! -f "$env_file" ]]; then
        log_error ".env file not found"
        return 1
    fi

    # Check if Docker is running
    if ! docker info &> /dev/null; then
        log_error "Docker is not running"
        return 1
    fi

    # Check if required directories exist
    local required_dirs=(
        "$PROJECT_ROOT/logs"
        "$PROJECT_ROOT/data"
    )

    for dir in "${required_dirs[@]}"; do
        if [[ ! -d "$dir" ]]; then
            log_error "Required directory not found: $dir"
            return 1
        fi
    done

    log_success "Setup validation passed"
}

# Show setup summary
show_summary() {
    log_info "Setup Summary:"
    echo ""
    echo "Environment: $ENVIRONMENT"
    echo "Project Root: $PROJECT_ROOT"
    echo "Environment File: $PROJECT_ROOT/.env"
    echo ""
    echo "Next Steps:"
    echo "1. Review and update the .env file with your specific configuration"
    echo "2. Add your external API keys to the .env file (optional but recommended)"
    echo "3. For development: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d"
    echo "4. For production: ./scripts/deploy-domain-seeder.sh"
    echo ""
    echo "Service URLs (after deployment):"
    echo "- Domain Seeder: http://localhost:${SEEDER_PORT:-3004}"
    echo "- Health Check: http://localhost:${SEEDER_PORT:-3004}/health"
    echo "- Metrics: http://localhost:${SEEDER_PORT:-3004}/metrics"
    echo "- Grafana: http://localhost:${GRAFANA_PORT:-3001}"
    echo "- Prometheus: http://localhost:${PROMETHEUS_PORT:-9090}"
}

# Main setup function
main() {
    log_info "Setting up environment for domain-seeder service ($ENVIRONMENT)"

    create_env_file
    create_directories
    setup_docker_networks

    if [[ "$ENVIRONMENT" == "production" ]]; then
        log_info "Skipping database initialization for production (run manually)"
    else
        # Load environment for database initialization
        set -a
        source "$PROJECT_ROOT/.env"
        set +a

        initialize_databases
    fi

    validate_setup
    show_summary

    log_success "Environment setup completed successfully!"
}

# Handle script arguments
case "${ENVIRONMENT}" in
    "development"|"dev")
        ENVIRONMENT="development"
        main
        ;;
    "production"|"prod")
        ENVIRONMENT="production"
        main
        ;;
    *)
        echo "Usage: $0 [development|production]"
        echo ""
        echo "Environments:"
        echo "  development - Setup for local development with debug settings"
        echo "  production  - Setup for production deployment with security hardening"
        exit 1
        ;;
esac
