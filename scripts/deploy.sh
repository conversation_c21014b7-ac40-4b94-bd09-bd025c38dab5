#!/bin/bash

# Domain Ranking System Deployment Script
# Handles production deployment with zero-downtime updates

set -e

# Configuration
ENVIRONMENT="${ENVIRONMENT:-production}"
BACKUP_BEFORE_DEPLOY="${BACKUP_BEFORE_DEPLOY:-true}"
RUN_MIGRATIONS="${RUN_MIGRATIONS:-true}"
HEALTH_CHECK_TIMEOUT="${HEALTH_CHECK_TIMEOUT:-300}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."

    # Check if Docker is running
    if ! docker info >/dev/null 2>&1; then
        error "Docker is not running"
    fi

    # Check if docker-compose is available
    if ! command -v docker-compose >/dev/null 2>&1; then
        error "docker-compose is not installed"
    fi

    # Check if .env file exists
    if [ ! -f .env ]; then
        warn ".env file not found, using default values"
    fi

    # Check if SSL certificates exist
    if [ ! -f nginx/ssl/cert.pem ] || [ ! -f nginx/ssl/key.pem ]; then
        warn "SSL certificates not found, HTTPS will not work"
    fi

    log "Prerequisites check completed"
}

# Create SSL certificates (self-signed for development)
create_ssl_certificates() {
    if [ ! -d nginx/ssl ]; then
        mkdir -p nginx/ssl
    fi

    if [ ! -f nginx/ssl/cert.pem ] || [ ! -f nginx/ssl/key.pem ]; then
        log "Creating self-signed SSL certificates..."
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout nginx/ssl/key.pem \
            -out nginx/ssl/cert.pem \
            -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"
        log "SSL certificates created"
    fi
}

# Backup current system
backup_system() {
    if [ "$BACKUP_BEFORE_DEPLOY" = "true" ]; then
        log "Creating backup before deployment..."
        ./scripts/backup.sh
        log "Backup completed"
    fi
}

# Build Docker images
build_images() {
    log "Building Docker images..."
    docker-compose build --no-cache
    log "Docker images built successfully"
}

# Run database migrations
run_migrations() {
    if [ "$RUN_MIGRATIONS" = "true" ]; then
        log "Running database migrations..."

        # Wait for databases to be ready
        log "Waiting for databases to be ready..."
        sleep 30

        # Run migrations
        cd database/scripts
        pnpm install
        node migrate.js init
        cd ../..

        log "Database migrations completed"
    fi
}

# Health check function
health_check() {
    local service=$1
    local url=$2
    local timeout=$3

    log "Performing health check for $service..."

    local count=0
    while [ $count -lt $timeout ]; do
        if curl -f -s "$url" >/dev/null 2>&1; then
            log "$service is healthy"
            return 0
        fi

        sleep 5
        count=$((count + 5))
        info "Waiting for $service to be ready... ($count/${timeout}s)"
    done

    error "$service health check failed after ${timeout}s"
}

# Deploy services
deploy_services() {
    log "Deploying services..."

    # Start databases first
    log "Starting database services..."
    docker-compose up -d scylla mariadb redis manticore

    # Wait for databases to be ready
    sleep 60

    # Run migrations
    run_migrations

    # Start application services
    log "Starting application services..."
    docker-compose up -d browserless
    sleep 10

    docker-compose up -d worker
    sleep 20

    docker-compose up -d web-app
    sleep 10

    # Start reverse proxy
    docker-compose up -d nginx

    # Start monitoring
    docker-compose up -d prometheus grafana

    log "All services started"
}

# Perform health checks
perform_health_checks() {
    log "Performing health checks..."

    # Check web application
    health_check "Web Application" "http://localhost/health" $HEALTH_CHECK_TIMEOUT

    # Check individual services
    health_check "ScyllaDB" "http://localhost:10000/storage_service/scylla_release_version" 60

    log "All health checks passed"
}

# Show deployment status
show_status() {
    log "Deployment Status:"
    echo ""
    docker-compose ps
    echo ""

    log "Service URLs:"
    echo "  Web Application: http://localhost (or https://localhost)"
    echo "  Prometheus: http://localhost:9090"
    echo "  Grafana: http://localhost:3001 (admin/admin)"
    echo "  ScyllaDB API: http://localhost:10000"
    echo ""

    log "Logs can be viewed with:"
    echo "  docker-compose logs -f [service-name]"
    echo ""
}

# Rollback function
rollback() {
    warn "Rolling back deployment..."
    docker-compose down
    # Restore from backup if needed
    error "Rollback completed"
}

# Main deployment process
main() {
    log "Starting Domain Ranking System deployment..."

    # Set trap for cleanup on error
    trap rollback ERR

    check_prerequisites
    create_ssl_certificates
    backup_system
    build_images
    deploy_services
    perform_health_checks
    show_status

    log "Deployment completed successfully!"
}

# Handle command line arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "rollback")
        rollback
        ;;
    "status")
        show_status
        ;;
    "health")
        perform_health_checks
        ;;
    *)
        echo "Usage: $0 [deploy|rollback|status|health]"
        exit 1
        ;;
esac
