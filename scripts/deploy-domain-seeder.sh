#!/bin/bash

# Production deployment script for domain-seeder service
# Usage: ./scripts/deploy-domain-seeder.sh [environment]

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ENVIRONMENT="${1:-production}"
SERVICE_NAME="domain-seeder"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "This script should not be run as root for security reasons"
        exit 1
    fi
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."

    # Check if Docker is installed and running
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed"
        exit 1
    fi

    if ! docker info &> /dev/null; then
        log_error "Docker daemon is not running"
        exit 1
    fi

    # Check if Docker Compose is available
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose is not installed"
        exit 1
    fi

    # Check if .env file exists
    if [[ ! -f "$PROJECT_ROOT/.env" ]]; then
        log_warning ".env file not found. Creating from .env.example..."
        if [[ -f "$PROJECT_ROOT/.env.example" ]]; then
            cp "$PROJECT_ROOT/.env.example" "$PROJECT_ROOT/.env"
            log_warning "Please edit .env file with your configuration before continuing"
            exit 1
        else
            log_error ".env.example file not found"
            exit 1
        fi
    fi

    log_success "Prerequisites check passed"
}

# Load environment variables
load_environment() {
    log_info "Loading environment configuration..."

    # Source environment file
    if [[ -f "$PROJECT_ROOT/.env" ]]; then
        set -a
        source "$PROJECT_ROOT/.env"
        set +a
    fi

    # Set default values
    export NODE_ENV="${NODE_ENV:-production}"
    export COMPOSE_PROJECT_NAME="${COMPOSE_PROJECT_NAME:-domain-ranking}"

    log_success "Environment loaded: $NODE_ENV"
}

# Validate configuration
validate_configuration() {
    log_info "Validating configuration..."

    local required_vars=(
        "MARIA_PASSWORD"
        "SCYLLA_KEYSPACE"
    )

    local missing_vars=()

    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            missing_vars+=("$var")
        fi
    done

    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        log_error "Missing required environment variables:"
        printf '%s\n' "${missing_vars[@]}"
        exit 1
    fi

    # Validate external API keys (optional but warn if missing)
    local optional_vars=(
        "CZDS_USERNAME"
        "CLOUDFLARE_API_KEY"
        "UMBRELLA_API_KEY"
    )

    local missing_optional=()

    for var in "${optional_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            missing_optional+=("$var")
        fi
    done

    if [[ ${#missing_optional[@]} -gt 0 ]]; then
        log_warning "Optional API keys not configured (limited functionality):"
        printf '%s\n' "${missing_optional[@]}"
    fi

    log_success "Configuration validation passed"
}

# Build and deploy service
deploy_service() {
    log_info "Deploying $SERVICE_NAME service..."

    cd "$PROJECT_ROOT"

    # Pull latest images for dependencies
    log_info "Pulling latest dependency images..."
    docker-compose pull scylla mariadb redis manticore

    # Build the domain-seeder service
    log_info "Building $SERVICE_NAME service..."
    docker-compose build --no-cache "$SERVICE_NAME"

    # Stop existing service if running
    log_info "Stopping existing $SERVICE_NAME service..."
    docker-compose stop "$SERVICE_NAME" || true
    docker-compose rm -f "$SERVICE_NAME" || true

    # Start dependencies first
    log_info "Starting database dependencies..."
    docker-compose up -d scylla mariadb redis manticore

    # Wait for databases to be ready
    log_info "Waiting for databases to be ready..."
    sleep 30

    # Check database health
    check_database_health

    # Start the domain-seeder service
    log_info "Starting $SERVICE_NAME service..."
    docker-compose up -d "$SERVICE_NAME"

    # Wait for service to be ready
    log_info "Waiting for $SERVICE_NAME to be ready..."
    wait_for_service_health

    log_success "$SERVICE_NAME service deployed successfully"
}

# Check database health
check_database_health() {
    log_info "Checking database health..."

    local max_attempts=30
    local attempt=1

    # Check ScyllaDB
    while [[ $attempt -le $max_attempts ]]; do
        if docker-compose exec -T scylla cqlsh -e "DESCRIBE KEYSPACES;" &> /dev/null; then
            log_success "ScyllaDB is ready"
            break
        fi

        if [[ $attempt -eq $max_attempts ]]; then
            log_error "ScyllaDB failed to start within timeout"
            exit 1
        fi

        log_info "Waiting for ScyllaDB... (attempt $attempt/$max_attempts)"
        sleep 10
        ((attempt++))
    done

    # Check MariaDB
    attempt=1
    while [[ $attempt -le $max_attempts ]]; do
        if docker-compose exec -T mariadb mysql -u root -p"${MARIA_PASSWORD}" -e "SELECT 1;" &> /dev/null; then
            log_success "MariaDB is ready"
            break
        fi

        if [[ $attempt -eq $max_attempts ]]; then
            log_error "MariaDB failed to start within timeout"
            exit 1
        fi

        log_info "Waiting for MariaDB... (attempt $attempt/$max_attempts)"
        sleep 5
        ((attempt++))
    done

    # Check Redis
    attempt=1
    while [[ $attempt -le $max_attempts ]]; do
        if docker-compose exec -T redis redis-cli ping | grep -q "PONG"; then
            log_success "Redis is ready"
            break
        fi

        if [[ $attempt -eq $max_attempts ]]; then
            log_error "Redis failed to start within timeout"
            exit 1
        fi

        log_info "Waiting for Redis... (attempt $attempt/$max_attempts)"
        sleep 5
        ((attempt++))
    done

    # Check Manticore
    attempt=1
    while [[ $attempt -le $max_attempts ]]; do
        if docker-compose exec -T manticore mysql -h127.0.0.1 -P9306 -e "SHOW STATUS;" &> /dev/null; then
            log_success "Manticore is ready"
            break
        fi

        if [[ $attempt -eq $max_attempts ]]; then
            log_error "Manticore failed to start within timeout"
            exit 1
        fi

        log_info "Waiting for Manticore... (attempt $attempt/$max_attempts)"
        sleep 5
        ((attempt++))
    done
}

# Wait for service health
wait_for_service_health() {
    local max_attempts=30
    local attempt=1
    local service_port="${SERVICE_PORT:-3004}"

    while [[ $attempt -le $max_attempts ]]; do
        if curl -f "http://localhost:$service_port/health/live" &> /dev/null; then
            log_success "$SERVICE_NAME is healthy"
            return 0
        fi

        if [[ $attempt -eq $max_attempts ]]; then
            log_error "$SERVICE_NAME failed to become healthy within timeout"
            show_service_logs
            exit 1
        fi

        log_info "Waiting for $SERVICE_NAME health check... (attempt $attempt/$max_attempts)"
        sleep 10
        ((attempt++))
    done
}

# Show service logs for debugging
show_service_logs() {
    log_info "Showing recent $SERVICE_NAME logs for debugging:"
    docker-compose logs --tail=50 "$SERVICE_NAME"
}

# Show deployment status
show_status() {
    log_info "Deployment status:"
    docker-compose ps "$SERVICE_NAME"

    log_info "Service health:"
    local service_port="${SERVICE_PORT:-3004}"
    if curl -f "http://localhost:$service_port/health" 2>/dev/null; then
        log_success "Service is healthy"
    else
        log_warning "Service health check failed"
    fi

    log_info "Recent logs:"
    docker-compose logs --tail=10 "$SERVICE_NAME"
}

# Rollback deployment
rollback_deployment() {
    log_warning "Rolling back deployment..."

    # Stop the service
    docker-compose stop "$SERVICE_NAME"

    # Remove the container
    docker-compose rm -f "$SERVICE_NAME"

    log_success "Rollback completed"
}

# Cleanup old images
cleanup_images() {
    log_info "Cleaning up old Docker images..."

    # Remove dangling images
    docker image prune -f

    # Remove old images for this service (keep last 3)
    local image_name="${COMPOSE_PROJECT_NAME}_${SERVICE_NAME}"
    local old_images=$(docker images "$image_name" --format "table {{.Repository}}:{{.Tag}}\t{{.CreatedAt}}" | tail -n +2 | sort -k2 -r | tail -n +4 | awk '{print $1}')

    if [[ -n "$old_images" ]]; then
        echo "$old_images" | xargs -r docker rmi
        log_success "Cleaned up old images"
    else
        log_info "No old images to clean up"
    fi
}

# Main deployment function
main() {
    log_info "Starting deployment of $SERVICE_NAME service to $ENVIRONMENT environment"

    check_root
    check_prerequisites
    load_environment
    validate_configuration

    # Set trap for cleanup on failure
    trap 'log_error "Deployment failed"; rollback_deployment; exit 1' ERR

    deploy_service
    show_status
    cleanup_images

    log_success "Deployment completed successfully!"
    log_info "Service is available at: http://localhost:${SERVICE_PORT:-3004}"
    log_info "Health check: http://localhost:${SERVICE_PORT:-3004}/health"
    log_info "Metrics: http://localhost:${SERVICE_PORT:-3004}/metrics"
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "status")
        load_environment
        show_status
        ;;
    "logs")
        load_environment
        docker-compose logs -f "$SERVICE_NAME"
        ;;
    "rollback")
        load_environment
        rollback_deployment
        ;;
    "cleanup")
        cleanup_images
        ;;
    *)
        echo "Usage: $0 [deploy|status|logs|rollback|cleanup]"
        echo ""
        echo "Commands:"
        echo "  deploy   - Deploy the domain-seeder service (default)"
        echo "  status   - Show deployment status"
        echo "  logs     - Show service logs"
        echo "  rollback - Rollback deployment"
        echo "  cleanup  - Clean up old Docker images"
        exit 1
        ;;
esac
