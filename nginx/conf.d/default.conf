# HTTP to HTTPS redirect
server {
	listen 80;
	server_name _;

	# Health check endpoint
	location /health {
		access_log off;
		return 200 "healthy\n";
		add_header Content-Type text/plain;
	}

	# Redirect all HTTP traffic to HTTPS
	location / {
		return 301 https://$host$request_uri;
	}
}

# HTTPS server
server {
	listen 443 ssl http2;
	server_name _;

	# SSL configuration
	ssl_certificate /etc/nginx/ssl/cert.pem;
	ssl_certificate_key /etc/nginx/ssl/key.pem;
	ssl_session_timeout 1d;
	ssl_session_cache shared:MozTLS:10m;
	ssl_session_tickets off;

	# Modern SSL configuration
	ssl_protocols TLSv1.2 TLSv1.3;
	ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
	ssl_prefer_server_ciphers off;

	# HSTS
	add_header Strict-Transport-Security "max-age=63072000" always;

	# Client max body size
	client_max_body_size 10M;

	# Proxy settings
	proxy_set_header Host $host;
	proxy_set_header X-Real-IP $remote_addr;
	proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
	proxy_set_header X-Forwarded-Proto $scheme;
	proxy_set_header X-Forwarded-Host $host;
	proxy_set_header X-Forwarded-Port $server_port;

	# Timeouts
	proxy_connect_timeout 30s;
	proxy_send_timeout 30s;
	proxy_read_timeout 30s;

	# Health check endpoint
	location /health {
		access_log off;
		proxy_pass http://web_app/health;
	}

	# API endpoints with rate limiting
	location /api/ {
		limit_req zone=api burst=20 nodelay;
		proxy_pass http://web_app;
		proxy_buffering on;
		proxy_buffer_size 4k;
		proxy_buffers 8 4k;
	}

	# Search endpoints with stricter rate limiting
	location /api/domains/search {
		limit_req zone=search burst=10 nodelay;
		proxy_pass http://web_app;
		proxy_buffering on;
		proxy_cache_valid 200 5m;
	}

	# Static assets with caching
	location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
		proxy_pass http://web_app;
		expires 1y;
		add_header Cache-Control "public, immutable";
		add_header Vary Accept-Encoding;
	}

	# Main application
	location / {
		proxy_pass http://web_app;
		proxy_buffering on;
		proxy_buffer_size 4k;
		proxy_buffers 8 4k;

		# Enable compression
		gzip_proxied any;
		gzip_vary on;
	}

	# Error pages
	error_page 404 /404.html;
	error_page 500 502 503 504 /50x.html;

	location = /50x.html {
		root /usr/share/nginx/html;
	}
}
