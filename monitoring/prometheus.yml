global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Web Application Service
  - job_name: 'web-app'
    static_configs:
      - targets: ['web-app:3000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Worker Service (Consolidated crawler, ranking-engine, scheduler)
  - job_name: 'worker'
    static_configs:
      - targets: ['worker:3001']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # ScyllaDB
  - job_name: 'scylla'
    static_configs:
      - targets: ['scylla:10000']
    metrics_path: '/metrics'
    scrape_interval: 60s

  # Redis
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s

  # Nginx
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:80']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Node Exporter (if available)
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s
