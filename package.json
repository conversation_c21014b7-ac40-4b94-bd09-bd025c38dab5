{"name": "domain-ranking-system", "version": "1.0.0", "description": "Domain ranking system with multiple microservices", "private": true, "packageManager": "pnpm@10.15.0", "engines": {"node": ">=22.0.0", "pnpm": ">=10.15.0"}, "scripts": {"dev": "pnpm --parallel --recursive run dev", "build": "pnpm --parallel --recursive run build", "build:services": "pnpm --parallel --filter './services/*' run build", "test": "vitest run -w", "test:watch": "vitest -w", "test:coverage": "vitest run --coverage -w", "test:services": "pnpm --recursive run test", "clean": "pnpm --recursive run clean", "install:all": "pnpm install", "lint": "eslint . --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint . --ext .ts,.tsx,.js,.jsx --fix", "lint:file": "eslint", "lint:file:fix": "eslint --fix", "lint:check": "eslint . --ext .ts,.tsx,.js,.jsx --max-warnings 0", "lint:new": "eslint -c eslint.config.new.js . --ext .ts,.tsx,.js,.jsx", "lint:new:fix": "eslint -c eslint.config.new.js . --ext .ts,.tsx,.js,.jsx --fix", "typecheck": "tsc --noEmit", "validate:descriptions": "pnpm --filter domain-ranking-shared validate:samples"}, "devDependencies": {"@babel/eslint-parser": "^7.28.0", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.33.0", "@stylistic/eslint-plugin": "^5.2.3", "@stylistic/eslint-plugin-ts": "^4.4.1", "@types/node": "^22.0.0", "@typescript-eslint/eslint-plugin": "^8.39.1", "@typescript-eslint/parser": "^8.39.1", "@vitest/coverage-v8": "^2.0.5", "@vitest/eslint-plugin": "^1.3.4", "@vitest/ui": "^2.0.5", "eslint": "^9.33.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jest": "^29.0.1", "eslint-plugin-jest-formatting": "^3.1.0", "eslint-plugin-jsdoc": "^54.1.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-native": "^5.0.0", "eslint-plugin-testing-library": "^7.6.3", "typescript": "^5.0.0", "vite-tsconfig-paths": "^5.1.4", "vitest": "^2.0.5", "xss": "^1.0.15"}, "dependencies": {"redis-smq-common": "^8.3.1"}}