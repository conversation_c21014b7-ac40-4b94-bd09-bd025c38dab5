# Domain Ranking System - Justfile
# Run commands with: just <command>

# Default recipe
default:
    @just --list

# Development commands
dev:
    @echo "Starting development environment..."
    docker-compose -f docker-compose.dev.yml up --build

dev-logs service="":
    @if [ -n "{{service}}" ]; then \
        docker-compose logs -f {{service}}; \
    else \
        docker-compose logs -f; \
    fi

# Production deployment
deploy:
    @echo "Deploying to production..."
    ./scripts/deploy.sh deploy

deploy-status:
    @echo "Checking deployment status..."
    ./scripts/deploy.sh status

deploy-health:
    @echo "Performing health checks..."
    ./scripts/deploy.sh health

rollback:
    @echo "Rolling back deployment..."
    ./scripts/deploy.sh rollback

# Database management
db-init:
    @echo "Initializing databases..."
    cd database/scripts && pnpm install && node migrate.js init

db-migrate:
    @echo "Running database migrations..."
    cd database/scripts && node migrate.js migrate

db-sample-data:
    @echo "Loading sample data..."
    cd database/scripts && node migrate.js sample-data

db-status:
    @echo "Checking database migration status..."
    cd database/scripts && node migrate.js status

# Backup and restore
backup:
    @echo "Creating system backup..."
    ./scripts/backup.sh

restore backup_file:
    @echo "Restoring from backup: {{backup_file}}"
    @echo "Restore functionality needs to be implemented"

# Service management
start:
    @echo "Starting all services..."
    docker-compose up -d

stop:
    @echo "Stopping all services..."
    docker-compose down

restart:
    @echo "Restarting all services..."
    docker-compose restart

logs service="":
    @if [ -n "{{service}}" ]; then \
        docker-compose logs -f {{service}}; \
    else \
        docker-compose logs -f; \
    fi

# Individual service management
start-db:
    @echo "Starting database services..."
    docker-compose up -d scylla mariadb redis manticore

start-app:
    @echo "Starting application services..."
    docker-compose up -d web-app worker

start-monitoring:
    @echo "Starting monitoring services..."
    docker-compose up -d prometheus grafana

# Build commands
build:
    @echo "Building all Docker images..."
    docker-compose build

build-no-cache:
    @echo "Building all Docker images (no cache)..."
    docker-compose build --no-cache

# Testing
test:
    @echo "Running tests..."
    pnpm test

test-services:
    @echo "Running service tests..."
    pnpm run test:services

test-coverage:
    @echo "Running tests with coverage..."
    pnpm run test:coverage

# Maintenance
clean:
    @echo "Cleaning up Docker resources..."
    docker-compose down -v
    docker system prune -f

clean-all:
    @echo "Cleaning up all Docker resources..."
    docker-compose down -v --remove-orphans
    docker system prune -af
    docker volume prune -f

# SSL certificate management
ssl-create:
    @echo "Creating SSL certificates..."
    mkdir -p nginx/ssl
    openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
        -keyout nginx/ssl/key.pem \
        -out nginx/ssl/cert.pem \
        -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"

# Monitoring and debugging
ps:
    @echo "Showing running containers..."
    docker-compose ps

top:
    @echo "Showing container resource usage..."
    docker stats

exec service command:
    @echo "Executing command in {{service}}: {{command}}"
    docker-compose exec {{service}} {{command}}

shell service:
    @echo "Opening shell in {{service}}..."
    docker-compose exec {{service}} /bin/sh

# Environment setup
setup:
    @echo "Setting up development environment..."
    @if [ ! -f .env ]; then cp .env.example .env; echo "Created .env file from template"; fi
    @just ssl-create
    @echo "Installing dependencies..."
    pnpm install
    @echo "Setup completed!"

# Health checks
health:
    @echo "Checking service health..."
    @curl -f http://localhost/health || echo "Web app not responding"
    @curl -f http://localhost:9090/-/healthy || echo "Prometheus not responding"
    @curl -f http://localhost:3001/api/health || echo "Grafana not responding"

# Performance testing
load-test:
    @echo "Running load tests..."
    @echo "Load testing functionality needs to be implemented"

# Documentation
docs:
    @echo "Generating documentation..."
    @echo "Documentation generation needs to be implemented"

# Version management
version:
    @echo "Current version:"
    @git describe --tags --always 2>/dev/null || echo "No version tags found"

tag version:
    @echo "Creating version tag: {{version}}"
    git tag -a {{version}} -m "Release {{version}}"
    git push origin {{version}}
