# Domain Ranking System Environment Configuration

# General Configuration
NODE_ENV=production
LOG_LEVEL=info

# Network Configuration
HTTP_PORT=80
HTTPS_PORT=443
WEB_APP_PORT=3000

# Database Configuration - ScyllaDB
SCYLLA_HOSTS=scylla:9042
SCYLLA_KEYSPACE=domain_ranking
SCYLLA_PORT=9042
SCYLLA_API_PORT=10000

# Database Configuration - MariaDB
MARIA_HOST=mariadb
MARIA_PORT=3306
MARIA_USER=root
MARIA_PASSWORD=your_secure_password_here
MARIA_DATABASE=domain_ranking

# Database Configuration - Redis
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0

# Database Configuration - Manticore Search
MANTICORE_HOST=manticore
MANTICORE_PORT=9308
MANTICORE_BINARY_PORT=9312

# External Services
BROWSERLESS_PORT=3000
BROWSERLESS_MAX_SESSIONS=5
BROWSERLESS_TIMEOUT=60000
BROWSERLESS_QUEUE=10
IMAGE_PROXY_URL=https://images.weserv.nl

# Application Configuration
CORS_ORIGIN=*
JWT_SECRET=your_jwt_secret_here

# Crawler Configuration
CRAWL_TIMEOUT=30000
CRAWL_MAX_RETRIES=3
CRAWL_CONCURRENT_REQUESTS=5

# Ranking Algorithm Weights
RANKING_WEIGHT_PERFORMANCE=0.25
RANKING_WEIGHT_SECURITY=0.20
RANKING_WEIGHT_SEO=0.20
RANKING_WEIGHT_TECHNICAL=0.15
RANKING_WEIGHT_BACKLINKS=0.20

# Domain Seeder Service Configuration
SEEDER_PORT=3004
SEEDER_MAX_NEW_PER_DAY=500000
SEEDER_ENQUEUE_BATCH=1000
SEEDER_ENQUEUE_INTERVAL=1000
SEEDER_QUEUE_MAX_DEPTH=200000
SEEDER_DB_CHECK_BATCH=5000
SEEDER_BLOOM_FP_RATE=0.01

# External API Keys for Domain Seeder (Optional)
CZDS_USERNAME=
CZDS_PASSWORD=
CLOUDFLARE_API_KEY=
UMBRELLA_API_KEY=
PIR_API_KEY=
RAPID7_API_KEY=

# AI Provider API Keys for Content Generation (Optional)
OPENAI_API_KEYS=
CLAUDE_API_KEYS=
GEMINI_API_KEYS=
OPENROUTER_API_KEYS=

# Proxy Configuration for External Requests (Optional)
PROXY_IPS=
PROXY_ROTATION_ENABLED=false

# Monitoring Configuration
PROMETHEUS_PORT=9090
GRAFANA_PORT=3001
GRAFANA_PASSWORD=admin
METRICS_ENABLED=true
HEALTH_CHECK_INTERVAL=30

# Backup Configuration
BACKUP_DIR=./backups
RETENTION_DAYS=7
BACKUP_UPLOAD_COMMAND=

# Deployment Configuration
ENVIRONMENT=production
BACKUP_BEFORE_DEPLOY=true
RUN_MIGRATIONS=true
HEALTH_CHECK_TIMEOUT=300
COMPOSE_PROJECT_NAME=domain-ranking

# Logging
LOG_PATH=./logs
