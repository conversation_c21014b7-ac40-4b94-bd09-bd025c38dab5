// Flat compat to import legacy shareable configs (e.g., airbnb)
const { join, resolve } = require('node:path');
// const { FlatCompat } = require('@eslint/eslintrc');

// const compat = new FlatCompat({ baseDirectory: resolve(__dirname, '..') });

// function getAirbnbRules(envType)
// {
// 	// web/mobile tests used airbnb-base historically; others use full airbnb + hooks
// 	const specs = (envType === 'web-test' || envType === 'mobile-test')
// 		? ['airbnb-base', 'plugin:@stylistic/disable-legacy']
// 		: ['airbnb', 'airbnb/hooks', 'plugin:@stylistic/disable-legacy'];

// 	const entries = compat.extends(...specs);
// 	const merged = {};
// 	for (const e of entries)
// 	{
// 		if (e && e.rules)
// 		{
// 			Object.assign(merged, e.rules);
// 		}
// 	}
// 	return merged;
// }

/*
 New flat-config helpers for ESLint v9
 Mirrors and modernizes all rules from `/_config/eslint.rules.js`
 without changing intent. Used by `eslint.config.new.js`.
*/

// All helpers are declared first, then exported in a grouped statement

// Top-level plugin and parser requires (no inline requires)
const js = require('@eslint/js');
const stylisticPlugin = require('@stylistic/eslint-plugin');
const importPlugin = require('eslint-plugin-import');
const vitestPlugin = require('@vitest/eslint-plugin');
const jestPlugin = require('eslint-plugin-jest');
const reactNativePlugin = require('eslint-plugin-react-native');
const reactPlugin = require('eslint-plugin-react');
const reactHooksPlugin = require('eslint-plugin-react-hooks');
const jsxA11yPlugin = require('eslint-plugin-jsx-a11y');
const jestFormattingPlugin = require('eslint-plugin-jest-formatting');
const testingLibraryPlugin = require('eslint-plugin-testing-library');

const babelEslintParser = require('@babel/eslint-parser');
const tsEslintParser = require('@typescript-eslint/parser');
const tsEslintPlugin = require('@typescript-eslint/eslint-plugin');

const jsdocPlugin = require('eslint-plugin-jsdoc');

// Load Airbnb configurations using FlatCompat for plugin defaults
const { FlatCompat } = require('@eslint/eslintrc');

const compat = new FlatCompat({ baseDirectory: resolve(__dirname, '..') });

// Get plugin recommended configurations using FlatCompat
const getAirbnbConfigs = (type) =>
{
	if (type === 'base')
	{
		return compat.extends('airbnb-base', 'plugin:@stylistic/disable-legacy');
	} if (type === 'full')
	{
		return compat.extends('airbnb', 'plugin:jsx-a11y/recommended', 'plugin:react/jsx-runtime', 'plugin:@stylistic/disable-legacy');
	} if (type === 'hooks')
	{
		return compat.extends('airbnb/hooks');
	}
	return [];
};

// ----- Globals (kept same semantics) -----
const COMMON_GLOBALS =
{
	DI: 'readonly',
	__DEV__: 'readonly',
	__PROD__: 'readonly',
	__TEST__: 'readonly',
	__DEBUG__: 'readonly',
	Atomics: 'readonly',
	SharedArrayBuffer: 'readonly',
};

// Central strict accessibility set for all browser React targets
const STRICT_A11Y_RULES =
{
	'jsx-a11y/alt-text': 'error',
	'jsx-a11y/anchor-has-content': 'error',
	'jsx-a11y/anchor-is-valid': 'error',
	'jsx-a11y/aria-activedescendant-has-tabindex': 'error',
	'jsx-a11y/aria-props': 'error',
	'jsx-a11y/aria-proptypes': 'error',
	'jsx-a11y/aria-role': 'error',
	'jsx-a11y/aria-unsupported-elements': 'error',
	'jsx-a11y/autocomplete-valid': 'error',
	'jsx-a11y/click-events-have-key-events': 'error',
	'jsx-a11y/heading-has-content': 'error',
	'jsx-a11y/html-has-lang': 'error',
	'jsx-a11y/iframe-has-title': 'error',
	'jsx-a11y/img-redundant-alt': 'error',
	'jsx-a11y/interactive-supports-focus': 'error',
	'jsx-a11y/label-has-associated-control': 'error',
	'jsx-a11y/media-has-caption': 'error',
	'jsx-a11y/mouse-events-have-key-events': 'error',
	'jsx-a11y/no-access-key': 'error',
	'jsx-a11y/no-autofocus': 'error',
	'jsx-a11y/no-distracting-elements': 'error',
	'jsx-a11y/no-interactive-element-to-noninteractive-role': 'error',
	'jsx-a11y/no-noninteractive-element-interactions': 'error',
	'jsx-a11y/no-noninteractive-element-to-interactive-role': 'error',
	'jsx-a11y/no-noninteractive-tabindex': 'error',
	'jsx-a11y/no-redundant-roles': 'error',
	'jsx-a11y/no-static-element-interactions': 'error',
	'jsx-a11y/role-has-required-aria-props': 'error',
	'jsx-a11y/role-supports-aria-props': 'error',
	'jsx-a11y/scope': 'error',
	'jsx-a11y/tabindex-no-positive': 'error',
};


// ----- JSDoc rule bundles (inline constants) -----
const JSDOC_DESCRIPTION_RULES =
{
	'jsdoc/require-description': 'error',
	'jsdoc/require-param-description': 'error',
	'jsdoc/require-returns-description': 'error',
	// Style, but softer
	'jsdoc/require-description-complete-sentence': 'warn',
	// Keep non-public docs concise when present (do not require JSDoc)
	'jsdoc/match-description': ['warn', {
		mainDescription:
		{
			match: '^[A-Z].{0,80}[.!?]$',
			message: 'Use a short single sentence (<= 80 chars) ending with punctuation.',
		},
		contexts: ['any'],
	}],
};

// Plain JS/JSX JSDoc rules
const JSDOC_JS_RULES =
{
	...JSDOC_DESCRIPTION_RULES,
	// Sanity checks
	'jsdoc/check-tag-names': 'error',
	'jsdoc/check-alignment': 'warn',
	'jsdoc/empty-tags': 'error',
	// Scope to public API via AST contexts; warn to avoid noise
	'jsdoc/require-jsdoc': ['warn', { publicOnly: { ancestorsOnly: false, cjs: true, esm: true } }],
};

// TS/TSX JSDoc rules
const JSDOC_TS_RULES =
{
	...JSDOC_DESCRIPTION_RULES,
	// TS owns types; keep JSDoc type tags off
	'jsdoc/no-types': 'error',
	'jsdoc/valid-types': 0,
	'jsdoc/require-param-type': 0,
	'jsdoc/require-returns-type': 0,
	'jsdoc/require-param': 0,
	'jsdoc/require-returns': 0,
	// Sanity checks
	'jsdoc/check-tag-names': 'error',
	'jsdoc/check-alignment': 'warn',
	'jsdoc/empty-tags': 'error',
	// Scope to public API; warn to avoid noise
	'jsdoc/require-jsdoc': ['warn', { publicOnly: { ancestorsOnly: false, cjs: true, esm: true } }],
};

/**
 * Returns a convenience entry for top-level ignores in flat config.
 * @param {Array<string>} extra Extra ignores to include.
 * @returns {Object} Convenience entry for top-level ignores.
 */
const getIgnores = (extra = []) => ({
	ignores: [
		'node_modules/',
		'dist/',
		'build/',
		'coverage/',
		'*.min.js',
		'*.bundle.js',
		'logs/',
		'temp/',
		'uploads/',
		'**/.next/**',
		'**/*.d.ts',
		'**/__mocks__/**',
		'**/android/**',
		'**/ios/**',
		'**/.expo/**',
		'**/metro.config.js',
		'vitest.config.ts',
		'vitest.workspace.ts',
		...extra,
	],
});


// Merge helper for flat config entries to apply common globals/plugins/settings
function withCommonEntry(entry)
{
	const out =
	{
		languageOptions: { globals: { ...COMMON_GLOBALS }, ...(entry.languageOptions || {}) },
		plugins: {
			'@stylistic': stylisticPlugin,
			'@typescript-eslint': tsEslintPlugin,
			jsdoc: jsdocPlugin,
			import: importPlugin,
			...(entry.plugins || {}),
		},
		settings: { react: { version: 'detect' }, ...(entry.settings || {}) },
		rules: { ...(entry.rules || {}) },
		linterOptions: entry.linterOptions || {},
		files: entry.files,
		name: entry.name,
	};

	const ignores = entry.ignores || entry.excludedFiles;
	if (Array.isArray(ignores) && ignores.length > 0)
	{
		out.ignores = ignores;
	}

	return out;
}

const COMMON_RULES =
{
	// Disable base stylistic rules in favor of @stylistic variants
	indent: 0,
	'arrow-parens': 0,
	'jsx-quotes': 0,
	'no-tabs': 0,
	'no-underscore-dangle': 0,
	'prefer-destructuring': 0,
	'prefer-object-spread': 0,
	'no-unused-expressions': 0,
	'class-methods-use-this': 0,
	'no-template-curly-in-string': 0,
	'no-return-await': 0,
	// 'brace-style': [1, 'allman', { allowSingleLine: true }],
	// Disable base brace-style in favor of @stylistic/brace-style
	'brace-style': 0,
	// Disable strict mode rule to match legacy
	strict: 0,
	'operator-linebreak': [1, 'before', {
		overrides: {
			'?': 'before',
			':': 'before',
			'{': 'ignore',
			'=': 'ignore',
			'??': 'after',
			'||': 'after',
			'&&': 'after',
		},
	}],
	'@stylistic/template-curly-spacing': 0,
	'@stylistic/linebreak-style': 0,
	'no-debugger': 1,
	'no-unreachable': 1,
	'no-unused-vars': 1,
	'@stylistic/spaced-comment': 1,
	'@stylistic/eol-last': 2,
	'no-param-reassign': [2, { props: false }],
	'@stylistic/indent': [2, 'tab', {
		MemberExpression: 1,
		ignoredNodes: ['TemplateLiteral'],
		SwitchCase: 1,
	}],
	'@stylistic/quotes': [1, 'single', { avoidEscape: true }],
	'@stylistic/semi': [1, 'always', { omitLastInOneLineBlock: true }],
	'@stylistic/max-len': [2, 100, 1, {
		ignoreUrls: true,
		ignoreComments: true,
		ignoreRegExpLiterals: true,
		ignoreStrings: true,
		ignoreTemplateLiterals: true,
	}],
	'@stylistic/brace-style': [1, 'allman', { allowSingleLine: true }],
	'@stylistic/comma-dangle': [1, 'only-multiline'],
	// Keep both core and @stylistic operator-linebreak for legacy parity
	'@stylistic/operator-linebreak': [1, 'before', {
		overrides: {
			'?': 'before',
			':': 'before',
			'{': 'ignore',
			'=': 'ignore',
			'??': 'after',
			'||': 'after',
			'&&': 'after',
		},
	}],
	'@stylistic/semi-spacing': [1, { before: false, after: true }],
	'@stylistic/function-paren-newline': [1, 'consistent'],
	'func-names': [1, 'as-needed'],
	'@stylistic/lines-between-class-members': 0,
	'prefer-arrow-callback': [1, { allowNamedFunctions: true }],
	'@stylistic/arrow-parens': [1, 'as-needed', { requireForBlockBody: true }],
	'no-plusplus': [1, { allowForLoopAfterthoughts: true }],
	'@stylistic/no-multiple-empty-lines': [1, { max: 2, maxEOF: 0, maxBOF: 1 }],
	'import/extensions': [2, 'ignorePackages', {
		'': 'never',
		js: 'never',
		jsx: 'never',
		ts: 'never',
		tsx: 'never',
	}],
};

const withCommonRules = rules => ({ ...COMMON_RULES, ...rules });

// React/JSX rules bundle aligned with legacy config
const REACT_RULES =
{
	// React hooks
	'react-hooks/exhaustive-deps': 0,
	// JSX stylistic
	'@stylistic/jsx-sort-props': 0,
	'@stylistic/jsx-one-expression-per-line': 0,
	'@stylistic/jsx-quotes': [1, 'prefer-double'],
	// Filenames allowed for JSX/TSX
	'react/jsx-filename-extension': [1, { extensions: ['.js', '.jsx', '.ts', '.tsx'] }],
	// React specifics
	'react/forbid-prop-types': 0,
	'react/jsx-props-no-spreading': 0,
	// In TS contexts we'll disable prop-types entirely; keep JS behavior here
	'react/prop-types': 0,
	// New JSX transform (no need React in scope)
	'react/react-in-jsx-scope': 0,
	'react/jsx-uses-react': 0,
	// Additional parity with legacy
	'react/jsx-no-duplicate-props': [2, { ignoreCase: true }],
	'react/jsx-no-target-blank': [2, { enforceDynamicLinks: 'always', links: true, forms: false }],
	'react/require-default-props': [2, { functions: 'defaultArguments' }],
	'react/display-name': 0,
	// React rules that need to match legacy exactly
	'react/jsx-key': 2,
	'react/no-direct-mutation-state': 2,
	// A11y adjustments kept from legacy
	'jsx-a11y/label-has-associated-control': 0,
	'jsx-a11y/label-has-for': [0, {
		required: { every: ['nesting', 'id'] },
		allowChildren: true,
	}],
	'jsx-a11y/anchor-is-valid': [1, {
		components: ['Link'],
		specialLink: ['to', '$to'],
		aspects: ['noHref', 'invalidHref', 'preferButton'],
	}],
	// Component definition style (Airbnb-like)
	'react/function-component-definition': [1, {
		namedComponents: ['function-declaration', 'arrow-function'],
		unnamedComponents: ['arrow-function'],
	}],
	// JSX A11y rules with legacy options
	'jsx-a11y/alt-text': [2, {
		elements: ['img', 'object', 'area', 'input[type="image"]'],
		img: [],
		object: [],
		area: [],
		'input[type="image"]': [],
	}],
	'jsx-a11y/anchor-has-content': [2, { components: [] }],
	'jsx-a11y/aria-role': [2, { ignoreNonDOM: false }],
	'jsx-a11y/autocomplete-valid': [2, { inputComponents: [] }],
	'jsx-a11y/heading-has-content': [2, { components: [''] }],
	'jsx-a11y/media-has-caption': [2, { audio: [], video: [], track: [] }],
	'jsx-a11y/no-autofocus': [2, { ignoreNonDOM: true }],
	'jsx-a11y/no-distracting-elements': [2, { elements: ['marquee', 'blink'] }],
};

const BABEL_LANGUAGE_OPTIONS =
{
	// Kept to match legacy behavior for JS requiring Babel syntax
	parser: babelEslintParser,
	parserOptions:
	{
		ecmaFeatures: { jsx: true },
		requireConfigFile: false,
		ecmaVersion: 2020,
		sourceType: 'module',
	},
};

const makeTsLanguageOptions = (rootDir, tsConfigPath) =>({
	parser: tsEslintParser,
	parserOptions:
	{
		tsconfigRootDir: rootDir,
		project: tsConfigPath ? join(rootDir, tsConfigPath) : undefined,
		requireConfigFile: false,
		ecmaVersion: 2020,
		sourceType: 'module',
	},
});

// ----- Import resolver settings helper -----
const makeImportSettings = (rootDir, tsConfigPath) => ({
	'import/resolver':
	{
		node:
		{
			moduleDirectory: ['node_modules'],
			extensions: ['.js', '.jsx', '.ts', '.tsx'],
		},
		typescript:
		{
			project: tsConfigPath ? join(rootDir, tsConfigPath) : undefined,
			alwaysTryTypes: true,
		},
	},
	'import/parsers':
	{
		'@typescript-eslint/parser': ['.js', '.ts', '.tsx'],
	},
});

// Small helpers to build env-specific rule deltas
function buildCliRules()
{
	return withCommonRules({
		'import/no-dynamic-require': 0,
		'global-require': 0,
		'no-console': 0,
		strict: 0,
		'import/no-extraneous-dependencies': [2, { devDependencies: true }],
		'no-restricted-syntax': ['error', 'ForInStatement', 'LabeledStatement', 'WithStatement'],
	});
}

function buildServerRules()
{
	return { ...withCommonRules({}), ...REACT_RULES, ...buildCliRules() };
}

function buildBrowserRules()
{
	return withCommonRules({
		...REACT_RULES,
		'default-case': [1, { commentPattern: '^no default$' }],
		'no-continue': 1,
		'no-restricted-syntax': [2, 'ForInStatement', 'LabeledStatement', 'WithStatement'],
		'no-await-in-loop': 2,
		'no-console': [1, { allow: ['warn', 'error'] }],
		'import/no-extraneous-dependencies': [2, { devDependencies: false }],
	});
}

function buildWebTestJsRules()
{
	return withCommonRules({
		'no-console': 0,
		'no-underscore-dangle': 0,
		'no-unused-expressions': 0,
		'max-classes-per-file': 0,
		'class-methods-use-this': 0,
		'no-promise-executor-return': 0,
		'no-await-in-loop': 0,
		'no-loop-func': 0,
		'import/no-extraneous-dependencies': [2, { devDependencies: true }],
	});
}

function buildMobileTestJsRules()
{
	return withCommonRules({
		'no-console': 0,
		'no-underscore-dangle': 0,
		'no-unused-expressions': 0,
		'max-classes-per-file': 0,
		'class-methods-use-this': 0,
		'no-promise-executor-return': 0,
		'no-await-in-loop': 0,
		'no-loop-func': 0,
		'import/no-extraneous-dependencies': [2, { devDependencies: true }],
		'import/extensions': 0,
		'import/no-unresolved': 0,
		// React Native specific
		'react-native/no-unused-styles': 2,
		'react-native/split-platform-components': 2,
		'react-native/no-inline-styles': 1,
		'react-native/no-color-literals': 1,
		// Testing-library adjustments for RN (no DOM)
		'testing-library/no-node-access': 0,
	});
}

/**
 * Build a flat config entry for the given environment.
 * Adds core @eslint/js and import recommended rules, then env-specific
 * plugin recommended rules and custom deltas to achieve parity.
 */
function getRules(envType, name, files, excludedFiles, tsconfigPath, extra)
{
	const isTs = Boolean(tsconfigPath) || Boolean(extra && extra.tsNoProject);
	const rootDir = resolve(__dirname, '..');

	const languageOptions = isTs
		? makeTsLanguageOptions(rootDir, tsconfigPath)
		: BABEL_LANGUAGE_OPTIONS;

	// Plugins map used by flat config entry
	const plugins =
	{
		'@stylistic': stylisticPlugin,
		import: importPlugin,
		jsdoc: jsdocPlugin,
		...(isTs ? { '@typescript-eslint': tsEslintPlugin } : {}),
		...(envType === 'browser' || envType === 'server' || envType === 'default' ? {
			react: reactPlugin,
			'react-hooks': reactHooksPlugin,
			'jsx-a11y': jsxA11yPlugin,
		} : {}),
		...(envType === 'web-test' || envType === 'mobile-test' ? {
			'testing-library': testingLibraryPlugin,
			jest: jestPlugin,
			'jest-formatting': jestFormattingPlugin,
		} : {}),
		...(envType === 'mobile-test' ? { 'react-native': reactNativePlugin } : {}),
	};

	// Import resolver + parsers (already included in makeImportSettings)
	const allSettings = makeImportSettings(rootDir, tsconfigPath);

	// Start from core recommended rules
	let rules =
	{
		...js.configs.recommended.rules,
		...importPlugin.configs.recommended.rules,
	};

	const mergeRulesFrom = configsArray => configsArray
		.reduce((acc, c) => ({ ...acc, ...(c && c.rules ? c.rules : {}) }), {});

	// Environment-specific rule assembly
	if (envType === 'web-test' || envType === 'mobile-test')
	{
		const airbnbBase = mergeRulesFrom(getAirbnbConfigs('base'));
		rules =
		{
			...rules,
			...airbnbBase,
			...(envType === 'web-test' ? testingLibraryPlugin.configs.dom.rules : {}),
			...(envType === 'mobile-test' ? testingLibraryPlugin.configs.react.rules : {}),
			...jestFormattingPlugin.configs.recommended.rules,
			...(envType === 'web-test' ? buildWebTestJsRules() : buildMobileTestJsRules()),
		};
	}
	else if (envType === 'browser')
	{
		const airbnbFull = mergeRulesFrom(getAirbnbConfigs('full'));
		const airbnbHooks = mergeRulesFrom(getAirbnbConfigs('hooks'));
		rules =
		{
			...rules,
			...airbnbFull,
			...airbnbHooks,
			...buildBrowserRules(),
		};
	}
	else if (envType === 'cli')
	{
		const airbnbFull = mergeRulesFrom(getAirbnbConfigs('full'));
		rules =
			{
				...rules,
				...airbnbFull,
				...buildCliRules(),
			};
	}
	else // server or default
	{
		const airbnbFull = mergeRulesFrom(getAirbnbConfigs('full'));
		const airbnbHooks = mergeRulesFrom(getAirbnbConfigs('hooks'));
		rules =
			{
				...rules,
				...airbnbFull,
				...airbnbHooks,
				...buildServerRules(),
			};
	}

	// TypeScript recommended + legacy TS customizations
	if (isTs)
	{
		rules =
			{
				...rules,
				...tsEslintPlugin.configs.recommended.rules,
				'@typescript-eslint/ban-ts-comment': [2, { 'ts-ignore': 'allow-with-description', minimumDescriptionLength: 4 }],
				'@typescript-eslint/naming-convention': [1, { selector: 'variableLike', format: null, leadingUnderscore: 'allow', trailingUnderscore: 'allow', filter: { regex: '^[_]$', match: true } }],
				'@typescript-eslint/no-empty-interface': [2, { allowSingleExtends: true }],
				'@typescript-eslint/no-namespace': [2, { allowDeclarations: true }],
			};
	}

	// JSDoc, stylistic disables, extras, and centralized TS severities
	const jsdocRules = isTs ? JSDOC_TS_RULES : JSDOC_JS_RULES;
	rules =
	{
		...rules,
		...jsdocRules,
		// Apply stylistic legacy-disable, then re-assert key stylistic rules below
		...stylisticPlugin.configs['disable-legacy'].rules,
		...(extra && extra.rules ? extra.rules : {}),
		...(extra && extra.tsNoProject ? { '@typescript-eslint/only-throw-error': 0 } : {}),
		...(isTs ? {
			// Turn off conflicting core rules in TS (legacy parity)
			'no-undef': 0,
			'no-unused-vars': 0,
			'no-use-before-define': 0,
			'constructor-super': 0,
			'default-param-last': 0,
			'getter-return': 0,
			'no-class-assign': 0,
			'no-const-assign': 0,
			'no-constant-binary-expression': 0,
			'no-dupe-args': 0,
			'no-dupe-class-members': 0,
			'no-dupe-keys': 0,
			'no-empty-static-block': 0,
			'no-func-assign': 0,
			'no-import-assign': 0,
			'no-new-native-nonconstructor': 0,
			'no-new-symbol': 0,
			'no-obj-calls': 0,
			'no-redeclare': 0,
			'no-setter-return': 0,
			'no-this-before-super': 0,
			'no-unsafe-negation': 0,
			'no-with': 0,
			// CLI-like relaxations that legacy TS inherits via merges
			'no-console': 0,
			'global-require': 0,
			'import/no-dynamic-require': 0,
			// TS rule severities
			'@typescript-eslint/no-unused-vars': 1,
			'@typescript-eslint/no-use-before-define': 1,
			'@typescript-eslint/only-throw-error': 2,
		} : {}),
		// Re-assert operator-linebreak parity after @stylistic/disable-legacy
		'operator-linebreak': [1, 'before', {
			overrides: {
				'?': 'before',
				':': 'before',
				'{': 'ignore',
				'=': 'ignore',
				'??': 'after',
				'||': 'after',
				'&&': 'after',
			},
		}],
		'@stylistic/operator-linebreak': [1, 'before', {
			overrides: {
				'?': 'before',
				':': 'before',
				'{': 'ignore',
				'=': 'ignore',
				'??': 'after',
				'||': 'after',
				'&&': 'after',
			},
		}],
	};

	return withCommonEntry({
		name,
		languageOptions,
		plugins,
		settings: allSettings,
		rules,
		files,
		...(excludedFiles ? { ignores: excludedFiles } : {}),
	});
}

// Public API (new)
module.exports =
{
	getIgnores,
	getRules,
};
