// ESLint 9 Flat Config Rules v3
// Migration from legacy .eslintrc.js format to flat config using FlatCompat workaround
const { join } = require('node:path');
const { FlatCompat } = require('@eslint/eslintrc');

// Plugin imports
const js = require('@eslint/js');
const stylisticPlugin = require('@stylistic/eslint-plugin');
const importPlugin = require('eslint-plugin-import');
const reactPlugin = require('eslint-plugin-react');
const reactHooksPlugin = require('eslint-plugin-react-hooks');
const jsxA11yPlugin = require('eslint-plugin-jsx-a11y');
const tsEslintPlugin = require('@typescript-eslint/eslint-plugin');
const vitestPlugin = require('@vitest/eslint-plugin');
const jestPlugin = require('eslint-plugin-jest');
const jestFormattingPlugin = require('eslint-plugin-jest-formatting');
const testingLibraryPlugin = require('eslint-plugin-testing-library');
const reactNativePlugin = require('eslint-plugin-react-native');

// Parser imports
const babelEslintParser = require('@babel/eslint-parser');
const tsEslintParser = require('@typescript-eslint/parser');

// FlatCompat setup
const compat = new FlatCompat({
	baseDirectory: __dirname,
	recommendedConfig: js.configs.recommended,
	allConfig: js.configs.all,
});

// Load Airbnb configs as flat configs
const airbnbBaseFlat = compat.extends('airbnb-base');
const airbnbFullFlat = compat.extends('airbnb');
const airbnbHooksFlat = compat.extends('airbnb/hooks');
const testingLibraryDomFlat = compat.extends('plugin:testing-library/dom');
const testingLibraryReactFlat = compat.extends('plugin:testing-library/react');
const jestFormattingFlat = compat.extends('plugin:jest-formatting/recommended');
const jsxA11yFlat = compat.extends('plugin:jsx-a11y/recommended');
const reactJsxRuntimeFlat = compat.extends('plugin:react/jsx-runtime');

// Helper to extract and process rules from FlatCompat configs
function processAirbnbRules(flatConfigs)
{
	// Extract list of rules
	const baseRuleEntries = flatConfigs.flatMap(cfg => (cfg.rules ? Object.entries(cfg.rules) : []));

	// 1. Replace deprecated stylistic rules with rules from stylistic plugin
	const ruleEntriesWithStylisticVersion = baseRuleEntries
		.map(([rule, setting]) => [rule.split('/').at(-1), setting])
		.filter(([rule]) => rule in stylisticPlugin.rules);

	const stylisticPatchedRules = {
		...Object.fromEntries(baseRuleEntries),
		...stylisticPlugin.configs['disable-legacy'].rules,
		...Object.fromEntries(ruleEntriesWithStylisticVersion.map(([rule, setting]) => [`@stylistic/${rule}`, setting])),
	};

	// 2. For typescript config, replace rules that have a typescript-eslint version
	const ruleEntriesWithTsEslintVersion = baseRuleEntries
		.filter(([rule]) => rule in (tsEslintPlugin.rules ?? {}));

	const typescriptPatchedRules = Object.fromEntries([
		...ruleEntriesWithTsEslintVersion.map(([rule]) => [rule, 'off']),
		...ruleEntriesWithTsEslintVersion.map(([rule, setting]) => [`@typescript-eslint/${rule}`, setting]),
	]);

	return { stylisticPatchedRules, typescriptPatchedRules };
}

// Process different Airbnb configs
const airbnbBase = processAirbnbRules(airbnbBaseFlat);
const airbnbFull = processAirbnbRules(airbnbFullFlat);
const airbnbHooks = processAirbnbRules(airbnbHooksFlat);

// Extract rules from other configs
function extractRules(configs)
{
	const rules = {};
	for (const config of configs)
	{
		if (config.rules)
		{
			Object.assign(rules, config.rules);
		}
	}
	return rules;
}

const TESTING_LIBRARY_DOM_RULES = extractRules(testingLibraryDomFlat);
const TESTING_LIBRARY_REACT_RULES = extractRules(testingLibraryReactFlat);
const JEST_FORMATTING_RULES = extractRules(jestFormattingFlat);
const JSX_A11Y_RULES = extractRules(jsxA11yFlat);
const REACT_JSX_RUNTIME_RULES = extractRules(reactJsxRuntimeFlat);

// Common globals
const COMMON_GLOBALS = {
	DI: 'readonly',
	__DEV__: 'readonly',
	__PROD__: 'readonly',
	__TEST__: 'readonly',
	__DEBUG__: 'readonly',
	Atomics: 'readonly',
	SharedArrayBuffer: 'readonly',
};

// Custom rule overrides that match legacy behavior
const CUSTOM_OVERRIDES = {
	// Disable most @stylistic rules that were added by FlatCompat but should be off
	'@stylistic/array-bracket-spacing': 0,
	'@stylistic/arrow-spacing': 0,
	'@stylistic/block-spacing': 0,
	'@stylistic/comma-spacing': 0,
	'@stylistic/comma-style': 0,
	'@stylistic/computed-property-spacing': 0,
	'@stylistic/dot-location': 0,
	'@stylistic/function-call-argument-newline': 0,
	'@stylistic/generator-star-spacing': 0,
	'@stylistic/implicit-arrow-linebreak': 0,
	'@stylistic/jsx-closing-bracket-location': 0,
	'@stylistic/jsx-closing-tag-location': 0,
	'@stylistic/jsx-curly-brace-presence': 0,
	'@stylistic/jsx-curly-newline': 0,
	'@stylistic/jsx-curly-spacing': 0,
	'@stylistic/jsx-equals-spacing': 0,
	'@stylistic/jsx-first-prop-new-line': 0,
	'@stylistic/jsx-indent': 0,
	'@stylistic/jsx-indent-props': 0,
	'@stylistic/jsx-max-props-per-line': 0,
	'@stylistic/jsx-pascal-case': 0,
	'@stylistic/jsx-props-no-multi-spaces': 0,
	'@stylistic/jsx-tag-spacing': 0,
	'@stylistic/jsx-wrap-multilines': 0,
	'@stylistic/key-spacing': 0,
	'@stylistic/keyword-spacing': 0,
	'@stylistic/linebreak-style': 0,
	'@stylistic/new-parens': 0,
	'@stylistic/newline-per-chained-call': 0,
	'@stylistic/no-confusing-arrow': 0,
	'@stylistic/no-extra-semi': 0,
	'@stylistic/no-floating-decimal': 0,
	'@stylistic/no-mixed-operators': 0,
	'@stylistic/no-mixed-spaces-and-tabs': 0,
	'@stylistic/no-multi-spaces': 0,
	'@stylistic/no-tabs': 0,
	'@stylistic/no-trailing-spaces': 0,
	'@stylistic/no-whitespace-before-property': 0,
	'@stylistic/nonblock-statement-body-position': 0,
	'@stylistic/object-curly-newline': 0,
	'@stylistic/object-curly-spacing': 0,
	'@stylistic/object-property-newline': 0,
	'@stylistic/one-var-declaration-per-line': 0,
	'@stylistic/padded-blocks': 0,
	'@stylistic/quote-props': 0,
	'@stylistic/rest-spread-spacing': 0,
	'@stylistic/semi-style': 0,
	'@stylistic/space-before-blocks': 0,
	'@stylistic/space-before-function-paren': 0,
	'@stylistic/space-in-parens': 0,
	'@stylistic/space-infix-ops': 0,
	'@stylistic/space-unary-ops': 0,
	'@stylistic/switch-colon-spacing': 0,
	'@stylistic/template-curly-spacing': 0,
	'@stylistic/template-tag-spacing': 0,
	'@stylistic/wrap-iife': 0,
	'@stylistic/yield-star-spacing': 0,

	// React hooks
	'react-hooks/exhaustive-deps': 0,
	// JSX stylistic
	'@stylistic/jsx-sort-props': 0,
	'@stylistic/jsx-one-expression-per-line': 0,
	'@stylistic/jsx-quotes': [1, 'prefer-double'],
	// React specifics
	'react/jsx-filename-extension': [1, { extensions: ['.js', '.jsx', '.ts', '.tsx'] }],
	'react/forbid-prop-types': 0,
	'react/jsx-props-no-spreading': 0,
	'react/prop-types': [1, { ignore: ['className'] }],
	// New JSX transform
	'react/react-in-jsx-scope': 0,
	'react/jsx-uses-react': 0,
	// A11y adjustments
	'jsx-a11y/label-has-for': [0, {
		required: { every: ['nesting', 'id'] },
		allowChildren: true,
	}],
	'react/function-component-definition': [1, {
		namedComponents: ['function-declaration', 'arrow-function'],
		unnamedComponents: ['arrow-function'],
	}],
	// Custom stylistic overrides (only the ones we want)
	'@stylistic/indent': [2, 'tab', {
		MemberExpression: 1,
		ignoredNodes: ['TemplateLiteral'],
		SwitchCase: 1,
	}],
	'@stylistic/quotes': [1, 'single', { avoidEscape: true }],
	'@stylistic/semi': [1, 'always', { omitLastInOneLineBlock: true }],
	'@stylistic/max-len': [2, 100, 1, {
		ignoreUrls: true,
		ignoreComments: true,
		ignoreRegExpLiterals: true,
		ignoreStrings: true,
		ignoreTemplateLiterals: true,
	}],
	'@stylistic/brace-style': [1, 'allman', { allowSingleLine: true }],
	'@stylistic/comma-dangle': [1, 'only-multiline'],
	'@stylistic/operator-linebreak': [1, 'before', {
		overrides: {
			'?': 'before',
			':': 'before',
			'{': 'ignore',
			'=': 'ignore',
			'??': 'after',
			'||': 'after',
			'&&': 'after',
		},
	}],
	'@stylistic/semi-spacing': [1, { before: false, after: true }],
	'@stylistic/function-paren-newline': [1, 'consistent'],
	'@stylistic/lines-between-class-members': [1, 'always', { exceptAfterSingleLine: true }],
	'@stylistic/arrow-parens': [1, 'as-needed', { requireForBlockBody: true }],
	'@stylistic/no-multiple-empty-lines': [1, { max: 2, maxEOF: 0, maxBOF: 1 }],
	// Core rule adjustments
	'func-names': [1, 'as-needed'],
	'prefer-arrow-callback': [1, { allowNamedFunctions: true }],
	'no-plusplus': [1, { allowForLoopAfterthoughts: true }],
	'no-debugger': 1,
	'no-unreachable': 1,
	'no-unused-vars': 1,
	'@stylistic/spaced-comment': 1,
	'@stylistic/eol-last': 2,
	'no-param-reassign': [2, { props: false }],
	'import/extensions': [2, 'ignorePackages', {
		js: 'never',
		jsx: 'never',
		ts: 'never',
		tsx: 'never',
	}],
	// Disable core rules that should be off
	'no-underscore-dangle': 0,
	'prefer-destructuring': 0,
	'prefer-object-spread': 0,
	'no-unused-expressions': 0,
	'class-methods-use-this': 0,
	'no-template-curly-in-string': 0,
	'no-return-await': 0,
	'strict': 0,
	'operator-linebreak': [1, 'before', {
		overrides: {
			'?': 'before',
			':': 'before',
			'{': 'ignore',
			'=': 'ignore',
			'??': 'after',
			'||': 'after',
			'&&': 'after',
		},
	}],
};

const CLI_OVERRIDES = {
	'import/no-dynamic-require': 0,
	'global-require': 0,
	'no-console': 0,
	strict: 0,
	'import/no-extraneous-dependencies': [2, { devDependencies: true }],
	'no-restricted-syntax': ['error', 'ForInStatement', 'LabeledStatement', 'WithStatement'],
	// Disable a11y rules for server-side code (they shouldn't apply)
	'jsx-a11y/anchor-is-valid': [1, {
		components: ['Link'],
		specialLink: ['to', '$to'],
		aspects: ['noHref', 'invalidHref', 'preferButton'],
	}],
	'jsx-a11y/autocomplete-valid': [2, { inputComponents: [] }],
	'jsx-a11y/control-has-associated-label': 0,
	'jsx-a11y/interactive-supports-focus': [2, {
		tabbable: ['button', 'checkbox', 'link', 'searchbox', 'spinbutton', 'switch', 'textbox'],
	}],
	'jsx-a11y/label-has-associated-control': 0,
	'jsx-a11y/no-interactive-element-to-noninteractive-role': [2, {
		tr: ['none', 'presentation'],
		canvas: ['img'],
	}],
	'jsx-a11y/no-noninteractive-element-interactions': [2, {
		handlers: ['onClick', 'onError', 'onLoad', 'onMouseDown', 'onMouseUp', 'onKeyPress', 'onKeyDown', 'onKeyUp'],
		alert: ['onKeyUp', 'onKeyDown', 'onKeyPress'],
		body: ['onError', 'onLoad'],
		dialog: ['onKeyUp', 'onKeyDown', 'onKeyPress'],
		iframe: ['onError', 'onLoad'],
		img: ['onError', 'onLoad'],
	}],
	'jsx-a11y/no-noninteractive-element-to-interactive-role': [2, {
		ul: ['listbox', 'menu', 'menubar', 'radiogroup', 'tablist', 'tree', 'treegrid'],
		ol: ['listbox', 'menu', 'menubar', 'radiogroup', 'tablist', 'tree', 'treegrid'],
		li: ['menuitem', 'menuitemradio', 'menuitemcheckbox', 'option', 'row', 'tab', 'treeitem'],
		table: ['grid'],
		td: ['gridcell'],
		fieldset: ['radiogroup', 'presentation'],
	}],
	'jsx-a11y/no-noninteractive-tabindex': [2, {
		tags: [],
		roles: ['tabpanel'],
		allowExpressionValues: true,
	}],
	'jsx-a11y/no-static-element-interactions': [2, {
		allowExpressionValues: true,
		handlers: ['onClick', 'onMouseDown', 'onMouseUp', 'onKeyPress', 'onKeyDown', 'onKeyUp'],
	}],
};

const BROWSER_OVERRIDES = {
	'default-case': [1, { commentPattern: '^no default$' }],
	'no-continue': 1,
	'no-restricted-syntax': [2, 'ForInStatement', 'LabeledStatement', 'WithStatement'],
	'no-await-in-loop': 2,
	'no-console': 0,
	'import/no-extraneous-dependencies': [2, { devDependencies: true }],
	'import/no-anonymous-default-export': 0,
	'import/no-dynamic-require': 0,
	'import/no-namespace': 0,
	'global-require': 0,
	'react-hooks/exhaustive-deps': 0,
	// A11y rule overrides to match legacy config
	'jsx-a11y/label-has-associated-control': 0,
	'jsx-a11y/anchor-is-valid': [1, {
		components: ['Link'],
		specialLink: ['to', '$to'],
		aspects: ['noHref', 'invalidHref', 'preferButton'],
	}],
	'jsx-a11y/alt-text': [2, {
		elements: ['img', 'object', 'area', 'input[type="image"]'],
		img: [],
		object: [],
		area: [],
		'input[type="image"]': [],
	}],
	'jsx-a11y/anchor-has-content': [2, { components: [] }],
	'jsx-a11y/aria-role': [2, { ignoreNonDOM: false }],
	'jsx-a11y/autocomplete-valid': [2, { inputComponents: [] }],
	'jsx-a11y/heading-has-content': [2, { components: [''] }],
	'jsx-a11y/interactive-supports-focus': [2, {
		tabbable: ['button', 'checkbox', 'link', 'searchbox', 'spinbutton', 'switch', 'textbox'],
	}],
	'jsx-a11y/media-has-caption': [2, { audio: [], video: [], track: [] }],
	'jsx-a11y/no-autofocus': [2, { ignoreNonDOM: true }],
	'jsx-a11y/no-distracting-elements': [2, { elements: ['marquee', 'blink'] }],
	'jsx-a11y/no-interactive-element-to-noninteractive-role': [2, {
		tr: ['none', 'presentation'],
		canvas: ['img'],
	}],
	'jsx-a11y/no-noninteractive-element-interactions': [2, {
		handlers: ['onClick', 'onError', 'onLoad', 'onMouseDown', 'onMouseUp', 'onKeyPress', 'onKeyDown', 'onKeyUp'],
		alert: ['onKeyUp', 'onKeyDown', 'onKeyPress'],
		body: ['onError', 'onLoad'],
		dialog: ['onKeyUp', 'onKeyDown', 'onKeyPress'],
		iframe: ['onError', 'onLoad'],
		img: ['onError', 'onLoad'],
	}],
	'jsx-a11y/no-noninteractive-element-to-interactive-role': [2, {
		ul: ['listbox', 'menu', 'menubar', 'radiogroup', 'tablist', 'tree', 'treegrid'],
		ol: ['listbox', 'menu', 'menubar', 'radiogroup', 'tablist', 'tree', 'treegrid'],
		li: ['menuitem', 'menuitemradio', 'menuitemcheckbox', 'option', 'row', 'tab', 'treeitem'],
		table: ['grid'],
		td: ['gridcell'],
		fieldset: ['radiogroup', 'presentation'],
	}],
	'jsx-a11y/no-noninteractive-tabindex': [2, {
		tags: [],
		roles: ['tabpanel'],
		allowExpressionValues: true,
	}],
	'jsx-a11y/no-static-element-interactions': [2, {
		allowExpressionValues: true,
		handlers: ['onClick', 'onMouseDown', 'onMouseUp', 'onKeyPress', 'onKeyDown', 'onKeyUp'],
	}],
};

const TYPESCRIPT_OVERRIDES = {
	// Disable @typescript-eslint rules that were auto-added but should be off
	'@typescript-eslint/class-methods-use-this': 0,
	'@typescript-eslint/consistent-return': 0,
	'@typescript-eslint/default-param-last': 0,
	'@typescript-eslint/dot-notation': 0,
	'@typescript-eslint/no-dupe-class-members': 0,
	'@typescript-eslint/no-empty-function': 0,
	'@typescript-eslint/no-implied-eval': 0,
	'@typescript-eslint/no-loop-func': 0,
	'@typescript-eslint/no-loss-of-precision': 0,
	'@typescript-eslint/no-redeclare': 0,
	'@typescript-eslint/no-shadow': 0,
	'@typescript-eslint/no-useless-constructor': 0,
	'@typescript-eslint/prefer-destructuring': 0,
	'@typescript-eslint/prefer-promise-reject-errors': 0,

	// Disable conflicting core rules that should be off in TS files
	quotes: 0,
	indent: 0,
	'no-unused-vars': 0,
	'no-use-before-define': 0,
	'default-param-last': 0,
	'brace-style': 0,
	'lines-between-class-members': 0,
	'@stylistic/lines-between-class-members': 0,
	'consistent-return': 0,
	'dot-notation': 0,
	'no-empty-function': 0,
	'no-implied-eval': 0,
	'no-loop-func': 0,
	'no-loss-of-precision': 0,
	'no-shadow': 0,
	'no-useless-constructor': 0,
	'prefer-promise-reject-errors': 0,
	// Core rules that should be disabled in TS
	'constructor-super': 0,
	'getter-return': 0,
	'no-class-assign': 0,
	'no-const-assign': 0,
	'no-dupe-args': 0,
	'no-dupe-keys': 0,
	'no-func-assign': 0,
	'no-import-assign': 0,
	'no-new-symbol': 0,
	'no-obj-calls': 0,
	'no-setter-return': 0,
	'no-this-before-super': 0,
	'no-undef': 0,
	'no-unsafe-negation': 0,
	'no-with': 0,

	// React overrides for TS
	'react/display-name': 0,
	'react/prop-types': 0,
	'react/react-in-jsx-scope': 0,
	'naming-convention': 0,
	'react/jsx-key': 0,
	'react/no-direct-mutation-state': 0,
	// TypeScript specific rules
	'@typescript-eslint/restrict-template-expressions': 0,
	'@typescript-eslint/no-unsafe-assignment': 0,
	'@typescript-eslint/no-unsafe-call': 0,
	'@typescript-eslint/no-unsafe-member-access': 0,
	'@typescript-eslint/no-floating-promises': 0,
	'@typescript-eslint/no-non-null-assertion': 0,
	// Note: The following rules should normally be disabled in TS files,
	// but the legacy config has them enabled, so we match that behavior
	// Re-enable rules that were disabled by TypeScript overrides
	'consistent-return': [2, { treatUndefinedAsUnspecified: false }],
	'dot-notation': [2, { allowKeywords: true, allowPattern: '' }],
	'no-empty-function': [2, { allow: ['arrowFunctions', 'functions', 'methods'] }],
	'no-implied-eval': 2,
	'no-loop-func': 2,
	'no-loss-of-precision': 2,
	'no-shadow': [2, {
		allow: [],
		builtinGlobals: false,
		hoist: 'functions',
		ignoreOnInitialization: false,
		ignoreTypeValueShadow: true,
		ignoreFunctionTypeParameterNameValueShadow: true,
	}],
	'no-useless-constructor': 2,
	'prefer-promise-reject-errors': [2, { allowEmptyReject: true }],
	// React rules that should be enabled to match legacy
	'react/jsx-key': 2,
	'react/no-direct-mutation-state': 2,
	// Enabled TS rules
	'@typescript-eslint/no-unused-vars': 1,
	'@typescript-eslint/no-use-before-define': 1,
	'@typescript-eslint/only-throw-error': 2,
	'@typescript-eslint/no-throw-literal': 0,
	'@typescript-eslint/naming-convention': [1, {
		selector: 'variableLike',
		format: null,
		leadingUnderscore: 'allow',
		trailingUnderscore: 'allow',
		filter: {
			regex: '^[_]$',
			match: true,
		},
	}],
	'@typescript-eslint/no-namespace': [2, { allowDeclarations: true }],
	'@typescript-eslint/no-empty-interface': [2, { allowSingleExtends: true }],
	'@typescript-eslint/ban-ts-comment': [2, {
		'ts-ignore': 'allow-with-description',
		minimumDescriptionLength: 4,
	}],
	// React TS rules
	'react/require-default-props': [2, { functions: 'defaultArguments' }],
	// Import extensions for TS
	'import/extensions': [2, 'ignorePackages', {
		'': 'never',
		js: 'never',
		jsx: 'never',
		ts: 'never',
		tsx: 'never',
	}],
};

const TEST_OVERRIDES = {
	'no-console': 0,
	'no-underscore-dangle': 0,
	'no-unused-expressions': 0,
	'max-classes-per-file': 0,
	'class-methods-use-this': 0,
	'no-promise-executor-return': 0,
	'no-await-in-loop': 0,
	'no-loop-func': 0,
	'import/no-extraneous-dependencies': [2, { devDependencies: true }],
};

const MOBILE_TEST_GLOBALS = {
	...COMMON_GLOBALS,
	// Jest globals
	jest: 'readonly',
	describe: 'readonly',
	it: 'readonly',
	test: 'readonly',
	expect: 'readonly',
	beforeAll: 'readonly',
	afterAll: 'readonly',
	beforeEach: 'readonly',
	afterEach: 'readonly',
	// React Native globals
	fetch: 'readonly',
	FormData: 'readonly',
	navigator: 'readonly',
	requestAnimationFrame: 'readonly',
	cancelAnimationFrame: 'readonly',
	global: 'readonly',
	window: 'readonly',
	document: 'readonly',
};

// Helper functions
function createLanguageOptions(parser, parserOptions = {})
{
	return {
		parser,
		parserOptions: {
			ecmaVersion: 2020,
			sourceType: 'module',
			...parserOptions,
		},
		globals: COMMON_GLOBALS,
	};
}

function createTsLanguageOptions(rootDir, tsConfigPath)
{
	return {
		parser: tsEslintParser,
		parserOptions: {
			tsconfigRootDir: rootDir,
			project: tsConfigPath ? join(rootDir, tsConfigPath) : undefined,
			requireConfigFile: false,
			ecmaVersion: 2020,
			sourceType: 'module',
		},
		globals: COMMON_GLOBALS,
	};
}

function createImportSettings(rootDir, tsConfigPath)
{
	return {
		'import/resolver': {
			node: {
				moduleDirectory: ['node_modules'],
				extensions: ['.js', '.jsx', '.ts', '.tsx'],
			},
			typescript: {
				project: tsConfigPath ? join(rootDir, tsConfigPath) : undefined,
				alwaysTryTypes: true,
			},
		},
		'import/parsers': {
			'@typescript-eslint/parser': ['.js', '.ts', '.tsx'],
		},
		react: {
			version: 'detect',
		},
	};
}

// Main factory functions
function createCLIConfig(rootDir, tsConfigPath)
{
	const baseConfig = {
		languageOptions: createLanguageOptions(babelEslintParser, {
			ecmaFeatures: { jsx: true },
			requireConfigFile: false,
		}),
		plugins: {
			'@stylistic': stylisticPlugin,
			import: importPlugin,
		},
		settings: createImportSettings(rootDir, tsConfigPath),
		rules: {
			...airbnbBase.stylisticPatchedRules,
			...CUSTOM_OVERRIDES,
			...CLI_OVERRIDES,
		},
	};

	const tsConfig = {
		files: ['**/*.{ts,tsx}'],
		languageOptions: createTsLanguageOptions(rootDir, tsConfigPath),
		plugins: {
			'@stylistic': stylisticPlugin,
			'@typescript-eslint': tsEslintPlugin,
			import: importPlugin,
		},
		settings: createImportSettings(rootDir, tsConfigPath),
		rules: {
			...airbnbBase.stylisticPatchedRules,
			...airbnbBase.typescriptPatchedRules,
			...tsEslintPlugin.configs.recommended.rules,
			...CUSTOM_OVERRIDES,
			...CLI_OVERRIDES,
			...TYPESCRIPT_OVERRIDES,
		},
	};

	return [baseConfig, tsConfig];
}

function createServerConfig(rootDir, tsConfigPath)
{
	const baseConfig = {
		languageOptions: createLanguageOptions(babelEslintParser, {
			ecmaFeatures: { jsx: true },
			requireConfigFile: false,
		}),
		plugins: {
			'@stylistic': stylisticPlugin,
			import: importPlugin,
			react: reactPlugin,
			'react-hooks': reactHooksPlugin,
			'jsx-a11y': jsxA11yPlugin,
		},
		settings: createImportSettings(rootDir, tsConfigPath),
		rules: {
			...airbnbFull.stylisticPatchedRules,
			...airbnbHooks.stylisticPatchedRules,
			...CUSTOM_OVERRIDES,
			...CLI_OVERRIDES,
		},
	};

	const tsConfig = {
		files: ['**/*.{ts,tsx}'],
		languageOptions: {
			...createTsLanguageOptions(rootDir, tsConfigPath),
			globals: {
				...COMMON_GLOBALS,
				React: 'writable',
			},
		},
		plugins: {
			'@stylistic': stylisticPlugin,
			'@typescript-eslint': tsEslintPlugin,
			import: importPlugin,
			react: reactPlugin,
			'react-hooks': reactHooksPlugin,
			'jsx-a11y': jsxA11yPlugin,
		},
		settings: createImportSettings(rootDir, tsConfigPath),
		rules: {
			...airbnbFull.stylisticPatchedRules,
			...airbnbFull.typescriptPatchedRules,
			...airbnbHooks.stylisticPatchedRules,
			...airbnbHooks.typescriptPatchedRules,
			...tsEslintPlugin.configs.recommended.rules,
			...CUSTOM_OVERRIDES,
			...CLI_OVERRIDES,
			...TYPESCRIPT_OVERRIDES,
		},
	};

	return [baseConfig, tsConfig];
}

function createBrowserConfig(rootDir, tsConfigPath)
{
	const baseConfig = {
		languageOptions: createLanguageOptions(babelEslintParser, {
			ecmaFeatures: { jsx: true },
			requireConfigFile: false,
		}),
		plugins: {
			'@stylistic': stylisticPlugin,
			import: importPlugin,
			react: reactPlugin,
			'react-hooks': reactHooksPlugin,
			'jsx-a11y': jsxA11yPlugin,
		},
		settings: createImportSettings(rootDir, tsConfigPath),
		rules: {
			...airbnbFull.stylisticPatchedRules,
			...airbnbHooks.stylisticPatchedRules,
			...JSX_A11Y_RULES,
			...REACT_JSX_RUNTIME_RULES,
			...CUSTOM_OVERRIDES,
			...BROWSER_OVERRIDES,
		},
	};

	const tsConfig = {
		files: ['**/*.{ts,tsx}'],
		languageOptions: {
			...createTsLanguageOptions(rootDir, tsConfigPath),
			globals: {
				...COMMON_GLOBALS,
				React: 'writable',
			},
		},
		plugins: {
			'@stylistic': stylisticPlugin,
			'@typescript-eslint': tsEslintPlugin,
			import: importPlugin,
			react: reactPlugin,
			'react-hooks': reactHooksPlugin,
			'jsx-a11y': jsxA11yPlugin,
		},
		settings: createImportSettings(rootDir, tsConfigPath),
		rules: {
			...airbnbFull.stylisticPatchedRules,
			...airbnbFull.typescriptPatchedRules,
			...airbnbHooks.stylisticPatchedRules,
			...airbnbHooks.typescriptPatchedRules,
			...tsEslintPlugin.configs.recommended.rules,
			...JSX_A11Y_RULES,
			...REACT_JSX_RUNTIME_RULES,
			...CUSTOM_OVERRIDES,
			...TYPESCRIPT_OVERRIDES,
			...BROWSER_OVERRIDES,
		},
	};

	return [baseConfig, tsConfig];
}

function createWebTestConfig(rootDir, tsConfigPath)
{
	const baseConfig = {
		languageOptions: createLanguageOptions(babelEslintParser, {
			ecmaFeatures: { jsx: true },
			requireConfigFile: false,
		}),
		plugins: {
			'@stylistic': stylisticPlugin,
			import: importPlugin,
			'testing-library': testingLibraryPlugin,
			'jest-formatting': jestFormattingPlugin,
			jest: jestPlugin,
		},
		settings: createImportSettings(rootDir, tsConfigPath),
		rules: {
			...airbnbBase.stylisticPatchedRules,
			...TESTING_LIBRARY_DOM_RULES,
			...JEST_FORMATTING_RULES,
			...CUSTOM_OVERRIDES,
			...TEST_OVERRIDES,
		},
	};

	const tsConfig = {
		files: ['**/*.{ts,tsx}'],
		languageOptions: createTsLanguageOptions(rootDir, tsConfigPath),
		plugins: {
			'@stylistic': stylisticPlugin,
			'@typescript-eslint': tsEslintPlugin,
			import: importPlugin,
			react: reactPlugin,
			'react-hooks': reactHooksPlugin,
			'jsx-a11y': jsxA11yPlugin,
			'testing-library': testingLibraryPlugin,
			'jest-formatting': jestFormattingPlugin,
			jest: jestPlugin,
		},
		settings: createImportSettings(rootDir, tsConfigPath),
		rules: {
			...airbnbBase.stylisticPatchedRules,
			...airbnbBase.typescriptPatchedRules,
			...tsEslintPlugin.configs.recommended.rules,
			...TESTING_LIBRARY_DOM_RULES,
			...JEST_FORMATTING_RULES,
			...CUSTOM_OVERRIDES,
			...TEST_OVERRIDES,
			...TYPESCRIPT_OVERRIDES,
			// Relaxed for tests
			'@typescript-eslint/no-explicit-any': 0,
			'@typescript-eslint/no-unsafe-assignment': 0,
			'@typescript-eslint/no-unsafe-call': 0,
			'@typescript-eslint/no-unsafe-member-access': 0,
			'@typescript-eslint/no-non-null-assertion': 0,
			'@typescript-eslint/no-unused-vars': [1, { argsIgnorePattern: '^_', varsIgnorePattern: '^_' }],
			'@typescript-eslint/consistent-type-assertions': 0,
		},
	};

	const vitestConfig = {
		files: ['**/*.{test,spec}.{ts,tsx,js,jsx}'],
		plugins: {
			'@vitest': vitestPlugin,
		},
		rules: {
			'@vitest/expect-expect': 'error',
			'@vitest/no-focused-tests': 'error',
			'@vitest/no-identical-title': 'error',
			'@vitest/valid-expect': 'error',
		},
	};

	const reactTestConfig = {
		files: ['**/*.{test,spec}.tsx'],
		plugins: {
			'testing-library': testingLibraryPlugin,
		},
		rules: {
			...TESTING_LIBRARY_REACT_RULES,
		},
	};

	return [baseConfig, tsConfig, vitestConfig, reactTestConfig];
}

function createMobileTestConfig(rootDir, tsConfigPath)
{
	const baseConfig = {
		languageOptions: {
			...createLanguageOptions(babelEslintParser, {
				ecmaFeatures: { jsx: true },
				requireConfigFile: false,
			}),
			globals: MOBILE_TEST_GLOBALS,
		},
		plugins: {
			'@stylistic': stylisticPlugin,
			import: importPlugin,
			'testing-library': testingLibraryPlugin,
			'jest-formatting': jestFormattingPlugin,
			jest: jestPlugin,
			'react-native': reactNativePlugin,
		},
		settings: createImportSettings(rootDir, tsConfigPath),
		rules: {
			...airbnbBase.stylisticPatchedRules,
			...TESTING_LIBRARY_REACT_RULES,
			...JEST_FORMATTING_RULES,
			...CUSTOM_OVERRIDES,
			...TEST_OVERRIDES,
			// React Native specific
			'import/extensions': 0,
			'import/no-unresolved': 0,
			'jest-formatting/padding-around-all': 1,
			'jest/expect-expect': 0,
			'jest/no-focused-tests': 1,
			'jest/no-identical-title': 2,
			'jest/valid-expect': 2,
			'jest/no-disabled-tests': 1,
			'react-native/no-unused-styles': 2,
			'react-native/split-platform-components': 2,
			'react-native/no-inline-styles': 1,
			'react-native/no-color-literals': 1,
			'testing-library/no-node-access': 0,
		},
	};

	const tsConfig = {
		files: ['**/*.{ts,tsx}'],
		languageOptions: {
			...createTsLanguageOptions(rootDir, tsConfigPath),
			globals: MOBILE_TEST_GLOBALS,
		},
		plugins: {
			'@stylistic': stylisticPlugin,
			'@typescript-eslint': tsEslintPlugin,
			import: importPlugin,
			react: reactPlugin,
			'react-hooks': reactHooksPlugin,
			'jsx-a11y': jsxA11yPlugin,
			'testing-library': testingLibraryPlugin,
			'jest-formatting': jestFormattingPlugin,
			jest: jestPlugin,
			'react-native': reactNativePlugin,
		},
		settings: createImportSettings(rootDir, tsConfigPath),
		rules: {
			...airbnbBase.stylisticPatchedRules,
			...airbnbBase.typescriptPatchedRules,
			...tsEslintPlugin.configs.recommended.rules,
			...TESTING_LIBRARY_REACT_RULES,
			...JEST_FORMATTING_RULES,
			...CUSTOM_OVERRIDES,
			...TEST_OVERRIDES,
			...TYPESCRIPT_OVERRIDES,
			// Mobile test specific relaxations
			'@typescript-eslint/no-explicit-any': 0,
			'@typescript-eslint/no-unsafe-assignment': 0,
			'@typescript-eslint/no-unsafe-call': 0,
			'@typescript-eslint/no-unsafe-member-access': 0,
			'@typescript-eslint/no-non-null-assertion': 0,
			'@typescript-eslint/no-unused-vars': [1, { argsIgnorePattern: '^_', varsIgnorePattern: '^_' }],
			'@typescript-eslint/consistent-type-assertions': 0,
			'import/extensions': 0,
			'import/no-unresolved': 0,
		},
	};

	return [baseConfig, tsConfig];
}

module.exports = {
	createCLIConfig,
	createServerConfig,
	createBrowserConfig,
	createWebTestConfig,
	createMobileTestConfig,
};
