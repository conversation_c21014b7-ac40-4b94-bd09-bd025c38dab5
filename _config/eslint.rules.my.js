
const { join } = require('node:path');
const { FlatCompat } = require('@eslint/eslintrc');

const projectRoot = join(__dirname, '..');

const moduleEntries =
{
	envTypes: ['cli', 'server', 'browser', 'mobile', 'desktop', 'web-test', 'mobile-test'],
	// Default plugins for all projects
	entries:
	{
		stylisticPlugin: require('@stylistic/eslint-plugin'),
		importPlugin: require('eslint-plugin-import'),
	},
	map:
	{
		stylisticPlugin: '@stylistic/eslint-plugin',
		importPlugin: 'eslint-plugin-import',
		reactPlugin: 'eslint-plugin-react',
		reactHooksPlugin: 'eslint-plugin-react-hooks',
		jsxA11yPlugin: 'eslint-plugin-jsx-a11y',
		tsEslintPlugin: '@typescript-eslint/eslint-plugin',
		vitestPlugin: '@vitest/eslint-plugin',
		jestPlugin: 'eslint-plugin-jest',
		jestFormattingPlugin: 'eslint-plugin-jest-formatting',
		testingLibraryPlugin: 'eslint-plugin-testing-library',
		reactNativePlugin: 'eslint-plugin-react-native',
		babelEslintParser: '@babel/eslint-parser',
		tsEslintParser: '@typescript-eslint/parser',
		// Specific
		eslintConfigNext: 'eslint-config-next',
	}
};


/* Lazy module import	*/
function getModuleEntry(moduleName)
{
	if (typeof moduleEntries.entries[moduleName] === 'undefined')
	{
		moduleEntries.entries[moduleName] = require(moduleEntries.map[moduleName]);
	}

	return moduleEntries.entries[moduleName];
}

const COMMON_GLOBALS =
{
	//
};

const COMMON_RULES =
{
	// Disable base stylistic rules in favor of @stylistic variants
	indent: 0,
	'arrow-parens': 0,
	// 'operator-linebreak': 0,
	'jsx-quotes': 0,
	'no-tabs': 0,
	'no-underscore-dangle': 0,
	'prefer-destructuring': 0,
	'prefer-object-spread': 0,
	'no-unused-expressions': 0,
	'class-methods-use-this': 0,
	'no-template-curly-in-string': 0,
	'no-return-await': 0, // No longer required
	'brace-style': [1, 'allman', { allowSingleLine: true }],
	'operator-linebreak': [1, 'before', {
		overrides: {
			'?': 'before',
			':': 'before',
			'{': 'ignore',
			'=': 'ignore',
			'??': 'after',
			'||': 'after',
			'&&': 'after',
			// '+': 'before',
		},
	}],
	'@stylistic/template-curly-spacing': 0,
	'@stylistic/linebreak-style': 0,
	'no-debugger': 1,
	'no-unreachable': 1,
	'no-unused-vars': 1,
	'@stylistic/spaced-comment': 1,
	'@stylistic/eol-last': 2,
	'no-param-reassign': [2, { props: false }],
	'@stylistic/indent': [2, 'tab', {
		MemberExpression: 1,
		ignoredNodes: ['TemplateLiteral'],
		SwitchCase: 1,
	}],
	'@stylistic/quotes': [1, 'single', { avoidEscape: true }],
	'@stylistic/semi': [1, 'always', { omitLastInOneLineBlock: true }],
	'@stylistic/max-len': [2, 100, 1, {
		ignoreUrls: true,
		ignoreComments: true,
		ignoreRegExpLiterals: true,
		ignoreStrings: true,
		ignoreTemplateLiterals: true,
	}],
	'@stylistic/brace-style': [1, 'allman', { allowSingleLine: true }],
	'@stylistic/comma-dangle': [1, 'only-multiline'],
	'@stylistic/operator-linebreak': [1, 'before', {
		overrides: {
			'?': 'before',
			':': 'before',
			'{': 'ignore',
			'=': 'ignore',
			'??': 'after',
			'||': 'after',
			'&&': 'after',
			// '+': 'before',
		},
	}],
	'@stylistic/semi-spacing': [1, { before: false, after: true }],
	'@stylistic/function-paren-newline': [1, 'consistent'],
	'func-names': [1, 'as-needed'],
	'@stylistic/lines-between-class-members': [1, 'always', { exceptAfterSingleLine: true }],
	'prefer-arrow-callback': [1, { allowNamedFunctions: true }],
	'@stylistic/arrow-parens': [1, 'as-needed', { requireForBlockBody: true }],
	'no-plusplus': [1, { allowForLoopAfterthoughts: true }],
	'@stylistic/no-multiple-empty-lines': [1, { max: 2, maxEOF: 0, maxBOF: 1 }],
	'import/extensions': [2, 'ignorePackages', {
		js: 'never',
		jsx: 'never',
		ts: 'never',
		tsx: 'never',
	}],
};

/**
 * Returns a convenience entry for top-level ignores in flat config.
 * @param {Array<string>} extra Extra ignores to include.
 * @returns {Object} Convenience entry for top-level ignores.
 */
const getIgnores = (extra = []) => ({
	ignores: [
		'**/node_modules/**',
		'coverage/',
		'*.min.js',
		'*.bundle.js',
		'logs/',
		'temp/',
		'uploads/',
		'**/*.d.ts',
		'**/.next/**',
		'**/dist/**',
		'**/build/**',
		'**/public/**',

		'**/android/**',
		'**/ios/**',
		'**/.expo/**',
		'**/metro.config.js',

		...extra,
	],
});

/**
 * Build a flat config entry for the given environment.
 * Adds core @eslint/js and import recommended rules, then env-specific
 * plugin recommended rules and custom deltas to achieve parity.
 */
function getRules(
	envType,
	baseDirectory,
	options = {},
	// name,
	// files,
	// excludedFiles,
	// tsconfigPath,
	// extra,
)
{
	const targetPath = join(projectRoot, baseDirectory);
	const currentOptions =
	{
		extraRules: {},
		extraFiles: [],
		extraIgnores: [],
		...options,
	};
	// projectRoot
	const config =
	{
		name: baseDirectory.trim().replace(/^\/+|\/+$/g, ''),
		files: [...currentOptions.files, ...currentOptions.extraFiles ],
		rules: { ...COMMON_RULES, ...currentOptions.extraRules },
		ignores: [...currentOptions.extraIgnores],
	};

	if (envType === 'cli')
	{
		config.rules =
		{
			...config.rules,
		};
	}
	else if (envType === 'server')
	{
		config.rules =
		{
			...config.rules,
		};
	}
	else if (envType === 'browser')
	{
		config.rules =
		{
			...config.rules,
		};
	}
	else if (envType === 'mobile')
	{
		config.rules =
		{
			...config.rules,
		};
	}
	else if (envType === 'desktop')
	{
		config.rules =
		{
			...config.rules,
		};
	}
	else if (envType === 'web-test')
	{
		config.rules =
		{
			...config.rules,
		};
	}
	else if (envType === 'mobile-test')
	{
		config.rules =
		{
			...config.rules,
		};
	}

	return config;
}

export { getIgnores };

export default getRules;
