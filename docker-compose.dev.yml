name: pr__domainr
# ----- NETWORKS ----- #
networks:
  mynet_internal:
    name: mynet_internal
    driver: bridge
    internal: true
  mynet_external:
    name: mynet_external
    driver: bridge
    external: true
# ----- SERVICES ----- #
services:
  # Override domain-seeder for development
  domain-seeder:
    container_name: domain-seeder
    restart: always
    build:
      context: .
      dockerfile: services/domain-seeder/Dockerfile
      target: development
    networks:
      - mynet_external
      - mynet_internal
    ports:
      - "${SEEDER_DEV_PORT:-3004}:3004"
    environment:
      - NODE_ENV=development
      - SERVICE_NAME=domain-seeder
      - SERVICE_PORT=3004
      - PORT=3004
      # Database connections
      - SCYLLA_CONTACT_POINTS=scylla:9042
      - SCYLLA_LOCAL_DC=dc1
      - SCYLLA_KEYSPACE=pr__domainr
      - MARIA_HOST=mariadb
      - MARIA_PORT=3306
      - MARIA_USER=root
      - MARIA_PASSWORD=me
      - MARIA_DATABASE=pr__domainr
      - MANTICORE_HOST=manticore
      - MANTICORE_PORT=9308
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=me
      - REDIS_DB=0
      # Development-specific settings
      - MAX_NEW_PER_DAY=10000
      - ENQUEUE_BATCH_SIZE=100
      - ENQUEUE_INTERVAL_MS=2000
      - NEW_QUEUE_MAX_DEPTH=5000
      - DB_CHECK_BATCH_SIZE=1000
      - BLOOM_FP_RATE=0.01
      # External source credentials (optional)
      - CZDS_USERNAME=${CZDS_USERNAME:-}
      - CZDS_PASSWORD=${CZDS_PASSWORD:-}
      - CLOUDFLARE_API_KEY=${CLOUDFLARE_API_KEY:-}
      - UMBRELLA_API_KEY=${UMBRELLA_API_KEY:-}
      - PIR_API_KEY=${PIR_API_KEY:-}
      - RAPID7_API_KEY=${RAPID7_API_KEY:-}
      # AI provider configuration (optional)
      - OPENAI_API_KEYS=${OPENAI_API_KEYS:-}
      - CLAUDE_API_KEYS=${CLAUDE_API_KEYS:-}
      - GEMINI_API_KEYS=${GEMINI_API_KEYS:-}
      - OPENROUTER_API_KEYS=${OPENROUTER_API_KEYS:-}
      # Development logging
      - LOG_LEVEL=debug
      - METRICS_ENABLED=true
      - HEALTH_CHECK_INTERVAL=10
    volumes:
      - ./_volumes/logs:/app/logs
      - ./data:/app/data:ro
      # Mount source code for hot reload
      - ./services/domain-seeder/src:/app/services/domain-seeder/src
      - ./shared/src:/app/shared/src
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    # Disable healthcheck in development for faster startup
    healthcheck:
      disable: true

  # Override web-app to expose port for development
  web-app:
    ports:
      - "${WEB_APP_DEV_PORT:-3000}:3000"

  # Override worker to expose port for development
  worker:
    ports:
      - "${WORKER_DEV_PORT:-3001}:3001"
