# CRUSH.md

Build/lint/test
- Install: pnpm install (root). Workspace managed by pnpm@9
- Build all: pnpm build (runs recursive builds)
- Dev all: pnpm dev (runs services in parallel)
- Lint (root): pnpm lint | Fix: pnpm lint:fix
- Typecheck: pnpm typecheck
- Test (root): pnpm test | Watch: pnpm test:watch | Coverage: pnpm test:coverage (Vitest)
- Test a single file: pnpm test -- shared/src/utils/__tests__/Validators.test.ts (or: pnpm -w vitest run shared/src/utils/__tests__/Validators.test.ts)
- Test by pattern: pnpm test -- -t "pattern" or pnpm test -- services/web-app/src/__tests__/integration/api.test.ts
- Per-package: cd into package then run its scripts (e.g., services/web-app: pnpm test:watch)

Conventions
- Language: TypeScript (Node >=20). ESM modules ("type": "module" in packages)
- Imports: absolute within package roots; prefer named imports; keep side-effect imports minimal; no default exports unless React components
- Formatting: follow ESLint (Airbnb + TypeScript). Run pnpm lint:fix before commits
- Types: strict typing; avoid any; use interfaces/types in shared; prefer readonly and exact shapes
- Naming: PascalCase for classes/types; camelCase for variables/functions; UPPER_SNAKE_CASE for constants/env; files in PascalCase for classes, kebab/consistent for modules
- Errors: never swallow; throw Error with context; use shared Logger for logging; no console.* in production code
- Async: use async/await; handle promise rejections; avoid blocking I/O
- Tests: Vitest across packages; colocate under __tests__; use setup files provided per package
- Config/Secrets: load via .env (see .env.example); never commit secrets; use Config util in shared
- Monitoring: use shared monitoring utilities; health endpoints at /health

Editors/AI rules
- Respect .editorconfig
- If using Cursor/Copilot rules, none found; keep this file updated if they are added later

Docs Map
- domain-content-generation-plan: SEO-focused content generation modes (preGenerated vs live), inputs/outputs, scheduler flow, heuristics, and tests
- domain-description-generation-plan: pipeline to produce description/tags/category; tools, new components, CLI, validation, testing, and next steps
- domain-description-schema: human doc for schema structure and storage mapping; example instance and authoring guidance
- domain-description.schema.json: JSON Schema used for validation
- domain-description-validation-integration-plan: validator goals, deliverables, design (Ajv), data flow across services, CLI/scripts/CI, rollout, risks, acceptance
- domain-description-validation-usage: env flag and commands to run validators in services
- domain-seeder-service-prompt: blueprint for services/domain-seeder with sources, pipeline, backpressure, quotas, content generation, metrics, testing, and milestones
- manticore-sync-plan: Manticore Search indexing pipeline, indices, shared client, worker, job contract, mapping, backfill, health/metrics, rollout
- top-domain-ranking-algorithm: comprehensive ranking system design (sources, scoring, smoothing, manipulation resistance, outputs/APIs, storage, evaluation, SLOs, implementation)
- samples: docs/samples/example-domain.json