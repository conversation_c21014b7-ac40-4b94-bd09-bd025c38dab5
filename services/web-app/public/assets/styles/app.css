/* Domain Ranking System - App CSS (2025 redesign foundation)
   Uses Bulma for base styles; this file provides tokens, light layout, and
   compatibility classes for existing markup while we migrate fully to Bulma components. */

:root {
  --bg: #fafbfc;
  --text: #1e293b;
  --muted: #64748b;
  --brand: #3b82f6;
  --brand-darker: #2563eb;
  --brand-light: #60a5fa;
  --accent: #06b6d4;
  --accent-light: #67e8f9;
  --card: #ffffff;
  --panel: #ffffff;
  --card-hover: #ffffff;
  --border: #e2e8f0;
  --border-hover: #cbd5e1;
  --radius: 12px;
  --radius-lg: 16px;
  --shadow-sm: 0 1px 3px rgba(0,0,0,0.05);
  --shadow: 0 4px 12px rgba(0,0,0,0.08);
  --shadow-lg: 0 8px 24px rgba(0,0,0,0.12);
  --font-sans: 'Inter', ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, 'Apple Color Emoji', 'Segoe UI Emoji';
  --bg-gradient: linear-gradient(135deg, #fafbfc 0%, #f1f5f9 100%);
  --glass-bg: rgba(255, 255, 255, 0.8);
  --glass-border: rgba(255, 255, 255, 0.9);
}

html, body {
  height: 100%;
  background: var(--bg-gradient);
  color: var(--text);
  font-family: var(--font-sans);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
}

/* Sticky footer layout */
body {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

* {
  box-sizing: border-box;
}

.container {
  max-width: 1200px;
  margin-inline: auto;
  padding-inline: 20px;
}

/* Header */
.header {
  background: linear-gradient(180deg, #ffffff 0%, #f5f7fb 100%);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  padding: 1rem 0;
}
.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}
.logo {
  font-size: 1.5rem;
  font-weight: 800;
  color: var(--brand);
  text-decoration: none;
}
.nav {
  display: flex;
  gap: 1.5rem;
}
.nav a {
  color: #475569;
  text-decoration: none;
  font-weight: 600;
}
.nav a.active {
  color: var(--brand);
}
.header-search { display: flex; gap: 0.5rem; align-items: center; }

/* Main */
.main { padding: 2rem 0; flex: 1 0 auto; }

/* Footer */
.footer {
  background: #111827;
  color: #fff;
  text-align: center;
  padding: 2rem 0;
  margin-top: 4rem;
}

/* Buttons compatibility (map .btn to Bulma .button look) */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border-radius: var(--radius);
  border: 1px solid var(--border);
  background: #fff;
  color: var(--text);
  font-weight: 600;
  padding: 0.65rem 1rem;
  box-shadow: var(--shadow-sm);
  transition: box-shadow .2s, transform .06s ease-out, background .2s;
}
.btn:hover { box-shadow: var(--shadow); }
.btn:active { transform: translateY(1px); }
.btn[disabled] { opacity: .6; cursor: not-allowed; }
.btn-primary { background: var(--brand); color: #fff; border-color: transparent; }

/* Inputs compatibility (align to Bulma .input) */
.input {
  width: 100%;
  padding: 0.7rem 0.9rem;
  border: 1px solid var(--border);
  border-radius: var(--radius);
  box-shadow: var(--shadow-sm);
}
.input:focus { border-color: var(--brand); box-shadow: 0 0 0 3px rgba(37,99,235,.15); outline: none; }

/* Cards compatibility */
.card { background: var(--card); border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,.06); }

/* Badge compatibility */
.badge { display: inline-block; padding: .25rem .5rem; border-radius: 999px; font-size: .75rem; background: #eef2ff; color: #3730a3; }

/* Progress compatibility */
.progress { width: 100%; height: 8px; background: #eef2ff; border-radius: 999px; overflow: hidden; }
.progress-bar { height: 100%; border-radius: 999px; background: linear-gradient(90deg,#10b981,#22c55e); }
.progress-bar.warn { background: linear-gradient(90deg,#f59e0b,#fbbf24); }
.progress-bar.danger { background: linear-gradient(90deg,#ef4444,#f97316); }

/* Dropdown compatibility */
.dropdown { position: absolute; top: 100%; left: 0; right: 0; background: #fff; border: 1px solid var(--border); border-radius: 8px; box-shadow: 0 10px 20px rgba(0,0,0,.08); z-index: 1000; margin-top: 4px; }
.dropdown-item { padding: .75rem 1rem; cursor: pointer; border-bottom: 1px solid #f3f4f6; }
.dropdown-item:hover { background: #f9fafb; }

/* Utilities */
.text-center { text-align: center; }
.mb-4 { margin-bottom: 2rem; }
.mt-4 { margin-top: 2rem; }

/* Responsive */
@media (max-width: 768px) {
  .header-content { flex-direction: column; }
  .nav { gap: 1rem; }
  .container { padding-inline: 15px; }
}



/* Clean Hero Section */
.page-hero {
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: 3rem 2rem;
  box-shadow: var(--shadow);
  margin: 2rem 0;
}

.title-gradient {
  background: linear-gradient(135deg, var(--brand) 0%, var(--brand-light) 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  font-weight: 700;
  letter-spacing: -0.01em;
}

.page-hero .input {
  height: 3rem;
  border-radius: 8px;
  background: var(--card);
  border: 1px solid var(--border);
  color: var(--text);
  font-size: 1rem;
  padding: 0 1rem;
  transition: all 0.2s ease;
}

.page-hero .input:focus {
  border-color: var(--brand);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
}

.page-hero .input::placeholder {
  color: var(--muted);
}

.page-hero .button.is-primary {
  height: 3rem;
  border-radius: 8px;
  padding-inline: 1.5rem;
  background: var(--brand);
  border: none;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.2s ease;
}

.page-hero .button.is-primary:hover {
  background: var(--brand-darker);
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

.page-hero .help {
  font-size: 0.875rem;
  color: var(--muted);
  margin-top: 0.5rem;
}

.loading {
  width: 40px;
  height: 40px;
  border: 3px solid #e5e7eb;
  border-top-color: var(--brand);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-inline: auto;
}
@keyframes spin { to { transform: rotate(360deg); } }

.mb-2 { margin-bottom: 0.75rem; }
.mb-3 { margin-bottom: 1.25rem; }

/* Gentle hover for elevated boxes */
.box {
  border-radius: 12px;
  transition: transform .12s ease-out, box-shadow .2s;
}
.box:hover { transform: translateY(-1px); box-shadow: 0 12px 24px rgba(0,0,0,.08); }


/* Dark mode variables and component theming */
[data-theme="light"] {
  --bg: #f8fafc;
  --text: #1e293b;
  --muted: #64748b;
  --brand: #6366f1;
  --brand-darker: #4f46e5;
  --brand-light: #8b5cf6;
  --accent: #06b6d4;
  --accent-light: #67e8f9;
  --card: rgba(255, 255, 255, 0.8);
  --card-hover: rgba(255, 255, 255, 0.95);
  --border: rgba(0, 0, 0, 0.1);
  --border-hover: rgba(0, 0, 0, 0.2);
  --bg-gradient: radial-gradient(circle at 20% 20%, rgba(99, 102, 241, 0.08) 0%, transparent 50%),
                 radial-gradient(circle at 80% 80%, rgba(139, 92, 246, 0.08) 0%, transparent 50%),
                 linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  --glass-bg: rgba(255, 255, 255, 0.3);
  --glass-border: rgba(255, 255, 255, 0.5);
}

/* Dark theme variables */
[data-theme="dark"] {
  --bg: #0b1220;
  --text: #e2e8f0;
  --muted: #94a3b8;
  --brand: #8b5cf6;
  --brand-darker: #7c3aed;
  --brand-light: #a78bfa;
  --accent: #22d3ee;
  --accent-light: #67e8f9;
  --card: rgba(2, 6, 23, 0.7);
  --card-hover: rgba(2, 6, 23, 0.9);
  --border: rgba(148, 163, 184, 0.16);
  --border-hover: rgba(148, 163, 184, 0.28);
  --bg-gradient: radial-gradient(circle at 20% 20%, rgba(139, 92, 246, 0.12) 0%, transparent 45%),
                 radial-gradient(circle at 80% 80%, rgba(99, 102, 241, 0.12) 0%, transparent 45%),
                 linear-gradient(135deg, #0b1220 0%, #111827 100%);
  --glass-bg: rgba(15, 23, 42, 0.5);
  --glass-border: rgba(148, 163, 184, 0.2);
}

/* Premium Element Styling */
body {
  background: var(--bg-gradient);
  color: var(--text);
  line-height: 1.6;
}

/* Brand-aligned primary hero */
.hero.is-primary {
  background: linear-gradient(135deg, var(--brand) 0%, var(--brand-light) 100%) !important;
}
.hero.is-primary .title,
.hero.is-primary .subtitle {
  color: #fff !important;
}

.navbar {
  background: var(--card);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border);
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
}

.navbar, .navbar.is-light {
  background: var(--card) !important;
  color: var(--text) !important;
}

.navbar .navbar-item.brand-logo {
  font-weight: 800;
  letter-spacing: -0.02em;
  color: var(--brand) !important;
}

.navbar .navbar-item {
  color: var(--text) !important;
  font-weight: 500;
  transition: all 0.3s ease;
  border-radius: 8px;
  margin: 0 4px;
}

.navbar .navbar-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--brand) !important;
}

.navbar .navbar-item.is-active {
  color: var(--brand) !important;
  background: color-mix(in srgb, var(--brand) 10%, transparent);
}

/* Navbar search sizing and control alignment */
.navbar-search { width: clamp(280px, 38vw, 460px); }
.navbar .field.has-addons .control .input,
.navbar .field.has-addons .control .button {
  height: 2.5rem;
}
.navbar .field.has-addons .control:first-child .input {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.navbar .field.has-addons .control:last-child .button {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.footer {
  background: var(--card);
  color: var(--text);
  border-top: 1px solid var(--border);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.box {
  background: var(--card);
  color: var(--text);
  border: 1px solid var(--border);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.input, .select select, .textarea {
  background: var(--card);
  color: var(--text);
  border: 1px solid var(--border);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.input:focus, .select select:focus, .textarea:focus {
  border-color: var(--brand);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
}

.input::placeholder {
  color: var(--muted);
}

.button.is-light {
  background: var(--card);
  color: var(--text);
  border: 1px solid var(--border);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.button.is-light:hover {
  background: var(--card-hover);
  border-color: var(--border-hover);
  transform: translateY(-1px);
}

/* Simple divider */
.divider-top { border-top: 1px solid var(--border); }

.button.is-primary { background: var(--brand); border-color: transparent; }

/* Tables */
.table { background: var(--card); color: var(--text); }
.table td, .table th { border-color: var(--border); }
.table tr:nth-child(even) { background: rgba(255,255,255,0.02); }

/* Links */
a { color: var(--brand); }
a:hover { color: var(--brand-darker); }
/* Text helpers override to tokens */
.has-text-grey { color: var(--muted) !important; }
.table th { color: var(--muted) !important; }
.table td { color: var(--text) !important; }



/* Domain visuals */
/* Cards and grids */
.hover-elevate { transition: transform 0.2s ease, box-shadow 0.2s ease; }
.hover-elevate:hover, .hover-elevate:focus { transform: translateY(-3px); box-shadow: 0 12px 30px rgba(2, 6, 23, 0.15); }
.features-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 2rem; }
.stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 2rem; }

.domain-favicon { width: 32px; height: 32px; border-radius: 8px; object-fit: cover; }
.domain-favicon--lg { width: 40px; height: 40px; border-radius: 8px; object-fit: cover; }
.link-domain { color: var(--brand); text-decoration: none; }
/* Clean Cards */
.card-pro {
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;
}

.card-pro:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow);
  border-color: var(--border-hover);
}

/* Clean Tags */
.tag.is-light {
  background: rgba(59, 130, 246, 0.1);
  color: var(--brand);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 6px;
  padding: 0.25rem 0.75rem;
  font-weight: 500;
  font-size: 0.875rem;
}
.tag.is-light:hover {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.25), rgba(139, 92, 246, 0.2));
  border-color: rgba(99, 102, 241, 0.5);
  transform: translateY(-1px);
}
/* Section Titles */
.section-title {
  background: linear-gradient(135deg, var(--text) 0%, var(--muted) 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  font-weight: 700;
  letter-spacing: -0.01em;
}

/* Enhanced Grid Layouts */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

/* Subtle animations removed for cleaner look */

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .card-pro {
    animation: none;
  }

  * {
    transition-duration: 0.01ms !important;
    animation-duration: 0.01ms !important;
  }
}
/* Typographic utilities */
.line-clamp-2 { display: -webkit-box; -webkit-line-clamp: 2; line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden; }

/* Links */
.link-domain { color: var(--brand); text-decoration: none; border-bottom: 1px solid color-mix(in srgb, var(--brand) 35%, transparent); padding-bottom: 1px; }
.link-domain:hover { border-bottom-color: var(--brand); }

.link-domain:hover, .link-domain:focus { text-decoration: underline; }
/* Score tokens */
:root {
  --score-good: #10b981;
  --score-good-soft: rgba(16, 185, 129, 0.15);
  --score-ok: #f59e0b;
  --score-ok-soft: rgba(245, 158, 11, 0.15);
  --score-warn: #f97316;
  --score-warn-soft: rgba(249, 115, 22, 0.15);
  --score-bad: #ef4444;
  --score-bad-soft: rgba(239, 68, 68, 0.15);
}

/***** Components *****/
.score-badge { display: inline-flex; align-items: center; justify-content: center; padding: 0.25rem 0.75rem; border-radius: 999px; font-weight: 600; font-size: 0.9rem; }
.score-badge--good { color: var(--score-good); background-color: var(--score-good-soft); }
.score-badge--ok { color: var(--score-ok); background-color: var(--score-ok-soft); }
.score-badge--warn { color: var(--score-warn); background-color: var(--score-warn-soft); }
.score-badge--bad { color: var(--score-bad); background-color: var(--score-bad-soft); }

/* Domain Analysis visuals */
.overview-card { background: var(--panel); border: 1px solid var(--border); }
.screenshot-img { width: 100%; max-width: 800px; height: auto; border-radius: 10px; box-shadow: 0 6px 18px rgba(0,0,0,0.15); border: 1px solid var(--border); display: block; margin: 0 auto; }

.metric-card { text-align: center; padding: 0.75rem; background: var(--panel); border: 1px solid var(--border); border-radius: 10px; }
.metric-card__value { font-size: 1.1rem; font-weight: 700; }
.metric-card--good .metric-card__value { color: var(--score-good); }
.metric-card--ok .metric-card__value { color: var(--score-ok); }
.metric-card--warn .metric-card__value { color: var(--score-warn); }
.metric-card--bad .metric-card__value { color: var(--score-bad); }
.metric-card__label { font-size: 0.8rem; color: var(--muted); }

.rank-change { display: inline-flex; align-items: center; gap: 0.25rem; font-size: 0.8rem; font-weight: 600; }
.rank-change--up { color: var(--score-good); }
.rank-change--down { color: var(--score-bad); }
.rank-change--same { color: var(--muted); }

/* Layouts */
.search-layout { display: grid; grid-template-columns: 280px 1fr; gap: 2rem; align-items: start; }
@media (max-width: 1024px){ .search-layout { grid-template-columns: 1fr; } }
.sticky-top { position: sticky; top: 2rem; }
.stack-lg > * + * { margin-top: 1rem; }

/* Results header */
.results-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem; padding: 1rem; background: var(--card); border: 1px solid var(--border); border-radius: var(--radius); box-shadow: var(--shadow-sm); }

/* Result cards */
.result-card { border: 1px solid var(--border); }
.favicon-fallback { width: 32px; height: 32px; border-radius: 8px; background: #f3f4f6; display: flex; align-items: center; justify-content: center; font-size: 1.2rem; }

/* Pagination */
.pager { display: flex; justify-content: center; align-items: center; gap: 0.5rem; margin-top: 2rem; padding: 1rem; background: var(--card); border: 1px solid var(--border); border-radius: 8px; box-shadow: var(--shadow-sm); }
.pager__btn { padding: 0.5rem 0.75rem; border: 1px solid var(--border); border-radius: 6px; background: var(--card); color: var(--text); cursor: pointer; font-weight: 500; transition: background-color .2s, color .2s; min-width: 40px; }
.pager__btn[disabled] { background: #0b1220; color: var(--muted); cursor: not-allowed; opacity: .6; }
.pager__btn--active { background: var(--brand); color: #fff; border-color: transparent; font-weight: 600; }
.pager__ellipsis { padding: 0 0.5rem; color: var(--muted); }

/* Search stats */
.search-stats { padding: 0.75rem 0; color: var(--muted); font-size: 0.9rem; border-bottom: 1px solid var(--border); }

/* Utilities */
.pt-2 { padding-top: 0.5rem; }
.pb-2 { padding-bottom: 0.5rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.gap-1 { display: inline-flex; gap: 0.25rem; }

/* Smaller spinner */
.loading.loading--sm { width: 16px; height: 16px; }

/* Grid helpers */
.grid-auto-200 { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; }
.grid-auto-250 { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; }
.grid-2 { display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; }
@media (max-width: 1024px){ .grid-2 { grid-template-columns: 1fr; } }

/* Factor chips */
.factor { padding: 1rem; border-radius: 8px; border: 1px solid var(--border); background: rgba(255,255,255,0.02); }
.factor--positive { border-color: color-mix(in srgb, var(--score-good) 30%, transparent); }
.factor--negative { border-color: color-mix(in srgb, var(--score-bad) 30%, transparent); }
.factor--neutral { border-color: var(--border); }

.domains-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(400px, 1fr)); gap: 1.5rem; }
.rank-badge { font-size: 1.1rem; font-weight: 700; color: var(--brand); background-color: color-mix(in srgb, var(--brand) 12%, transparent); padding: 0.4rem 0.6rem; border-radius: 8px; min-width: 60px; text-align: center; }

/***** Spacing helpers *****/
.gap-2 { display: inline-flex; gap: 0.5rem; }
.gap-3 { display: inline-flex; gap: 0.75rem; }

/***** Domain View Page Styles *****/
.domain-view-page {
  padding: 2rem 0;
}

/* Monospace utility for error ids and code blocks */
.mono {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

.domain-view-page .hero {
  margin-bottom: 2rem;
}

.domain-view-page .hero.is-primary {
  background: linear-gradient(135deg, var(--brand) 0%, var(--brand-light) 100%);
}

.domain-view-page .hero.is-bold {
  box-shadow: 0 8px 32px rgba(99, 102, 241, 0.2);
}

.domain-view-page .tabs.is-boxed {
  margin-bottom: 2rem;
}

.domain-view-page .tabs.is-boxed ul {
  border-bottom: 1px solid var(--border);
}

.domain-view-page .tabs.is-boxed li.is-active a {
  background: var(--card);
  border-color: var(--border);
  border-bottom-color: transparent;
  color: var(--brand);
  font-weight: 600;
}

.domain-view-page .tabs a {
  padding: 0.75rem 1.5rem;
  transition: all 0.3s ease;
}

.domain-view-page .tabs a:hover {
  background: rgba(99, 102, 241, 0.05);
  color: var(--brand);
}

.domain-view-page .tab-content {
  animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Score badge variants */
.score-badge--excellent {
  color: #059669;
  background: rgba(5, 150, 105, 0.15);
  border: 1px solid rgba(5, 150, 105, 0.3);
}

.score-badge.is-size-3 {
  font-size: 2rem !important;
  padding: 0.5rem 1rem;
}

/* Loader styles */
.domain-view-page .loader {
  width: 50px;
  height: 50px;
  border: 4px solid var(--border);
  border-top-color: var(--brand);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Overview card variants */
.overview-card--good {
  border-color: rgba(16, 185, 129, 0.3);
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.05) 0%, rgba(16, 185, 129, 0.02) 100%);
}

.overview-card--ok {
  border-color: rgba(245, 158, 11, 0.3);
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.05) 0%, rgba(245, 158, 11, 0.02) 100%);
}

.overview-card--warn {
  border-color: rgba(249, 115, 22, 0.3);
  background: linear-gradient(135deg, rgba(249, 115, 22, 0.05) 0%, rgba(249, 115, 22, 0.02) 100%);
}

/* Domain info sections */
.domain-view-page .box {
  margin-bottom: 2rem;
  transition: all 0.3s ease;
}

.domain-view-page .box:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.domain-view-page .image {
  border-radius: var(--radius);
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.domain-view-page .image img {
  transition: transform 0.3s ease;
}

.domain-view-page .image:hover img {
  transform: scale(1.05);
}

/* Technology tags */
.domain-view-page .tags.are-medium .tag {
  padding: 0.75rem 1rem;
  font-size: 0.95rem;
}

.domain-view-page .tag.is-info.is-light {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.15) 0%, rgba(139, 92, 246, 0.1) 100%);
  color: var(--brand);
  border: 1px solid rgba(99, 102, 241, 0.3);
}

/* Security indicators */
.domain-view-page .tag.is-success.is-light {
  background: rgba(16, 185, 129, 0.15);
  color: var(--score-good);
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.domain-view-page .tag.is-danger.is-light {
  background: rgba(239, 68, 68, 0.15);
  color: var(--score-bad);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.domain-view-page .tag.is-warning {
  background: rgba(245, 158, 11, 0.15);
  color: var(--score-warn);
  border: 1px solid rgba(245, 158, 11, 0.3);
}

/* Social link buttons */
.domain-view-page .button.is-info.is-light {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.05) 100%);
  color: var(--brand);
  border: 1px solid rgba(99, 102, 241, 0.2);
  transition: all 0.3s ease;
}

.domain-view-page .button.is-info.is-light:hover {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.2) 0%, rgba(139, 92, 246, 0.15) 100%);
  border-color: rgba(99, 102, 241, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.2);
}

/* Metric boxes */
.domain-view-page .box.is-shadowless.has-background-light {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(241, 245, 249, 0.6) 100%);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.5);
}

/* Performance metrics */
.domain-view-page .heading {
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: var(--muted);
  margin-bottom: 0.5rem;
}

.domain-view-page .title.is-4,
.domain-view-page .title.is-5 {
  color: var(--text);
  font-weight: 700;
}

/* Hero section enhancements */
.domain-view-page .hero-body .level {
  flex-wrap: wrap;
}

.domain-view-page .hero-body .title.is-2 {
  font-size: 2.5rem;
  font-weight: 800;
  letter-spacing: -0.02em;
}

.domain-view-page .hero-body .subtitle {
  opacity: 0.95;
}

.domain-view-page .hero-body .tag {
  font-size: 0.875rem;
  padding: 0.5rem 0.75rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .domain-view-page .hero-body {
    padding: 2rem 1rem;
  }

  .domain-view-page .tabs a {
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
  }

  .domain-view-page .level {
    flex-direction: column;
    align-items: flex-start;
  }

  .domain-view-page .level-left,
  .domain-view-page .level-right {
    width: 100%;
    margin-bottom: 1rem;
  }

  .domain-view-page .columns {
    padding: 0 1rem;
  }

  .domain-view-page .hero-body .title.is-2 {
    font-size: 1.75rem;
  }
}

