{"name": "domain-ranking-web-app", "version": "1.0.0", "description": "Web application service for domain ranking system", "main": "src/index.ts", "scripts": {"start": "tsx src/index.ts", "dev": "tsx watch src/index.ts", "test": "vitest run -w", "test:watch": "vitest -w"}, "dependencies": {"ajv": "^8.17.1", "ajv-formats": "^3.0.1", "axios": "^1.6.0", "cassandra-driver": "^4.7.2", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "ejs": "^3.1.9", "helmet": "^7.0.0", "joi": "^17.9.2", "lodash": "^4.17.21", "mysql2": "^3.6.0", "node-libcurl": "^4.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "redis": "^4.6.8", "redis-smq": "^8.3.1", "shared": "workspace:*", "ultimate-express": "^1.0.0"}, "devDependencies": {"@types/compression": "^1.7.2", "@types/cors": "^2.8.13", "@types/ejs": "^3.1.2", "@types/lodash": "^4.14.195", "@types/node": "^22.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/supertest": "^2.0.12", "@vitest/coverage-v8": "^2.0.5", "@vitest/ui": "^2.0.5", "supertest": "^6.3.3", "tsx": "^4.0.0", "typescript": "^5.0.0", "vitest": "^2.0.5"}, "engines": {"node": ">=22.0.0", "pnpm": ">=10.15.0"}, "packageManager": "pnpm@10.15.0"}