import { defineConfig } from 'vitest/config';

export default defineConfig(async () =>
{
	const { default: tsconfigPaths } = await import('vite-tsconfig-paths');
	return ({
		plugins: [tsconfigPaths()],
		test: {
			environment: 'node',
			include: [
				'src/**/__tests__/**/*.(test|spec).+(ts|tsx|js)',
				'src/**/*.(test|spec).+(ts|tsx|js)',
			],
			coverage: {
				provider: 'v8',
				reporter: ['text', 'lcov', 'html'],
				reportsDirectory: 'coverage',
				include: ['src/**/*.{ts,tsx}'],
				exclude: ['src/**/*.d.ts', 'src/__tests__/**/*'],
			},
		},
	});
});
