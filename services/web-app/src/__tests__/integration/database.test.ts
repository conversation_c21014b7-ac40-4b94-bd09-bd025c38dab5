import {
	describe, test, expect, beforeAll, afterAll,
} from 'vitest';
import DatabaseManager from '@shared';
import type { ResultSetHeader } from 'mysql2/promise';
import DomainSearchService from '../../services/DomainSearchService';
import DomainAnalysisService from '../../services/DomainAnalysisService';

// Integration tests for database operations
describe('Database Integration Tests', () =>
{
	let databaseManager: DatabaseManager;
	let searchService: DomainSearchService;
	let analysisService: DomainAnalysisService;

	beforeAll(async () =>
	{
		// Use test database configuration
		process.env.NODE_ENV = 'test';
		process.env.SCYLLA_HOSTS = 'localhost:9042';
		process.env.MARIA_HOST = 'localhost';
		process.env.REDIS_URL = 'redis://localhost:6379';
		process.env.MANTICORE_HOST = 'localhost';

		databaseManager = new DatabaseManager();

		// Only connect if databases are available (skip in CI)
		try
		{
			await databaseManager.initialize();
		}
		catch (error)
		{
			console.warn('Database connection failed, skipping integration tests:', error.message);
			return;
		}

		searchService = new DomainSearchService(databaseManager);
		analysisService = new DomainAnalysisService(databaseManager);
	});

	afterAll(async () =>
	{
		if (databaseManager)
		{
			await databaseManager.shutdown();
		}
	});

	describe('ScyllaDB Operations', () =>
	{
		test('should connect to ScyllaDB successfully', async () =>
		{
			const scyllaClient = databaseManager.getScyllaClient();

			expect(scyllaClient).toBeDefined();

			if (scyllaClient.isReady())
			{
				const result = await scyllaClient.execute('SELECT now() FROM system.local');

				expect(result.rows).toBeDefined();
			}
		});

		test('should store and retrieve domain analysis data', async () =>
		{
			const scyllaClient = databaseManager.getScyllaClient();

			if (!scyllaClient.isReady())
			{
				console.warn('ScyllaDB not connected, skipping test');
				return;
			}

			const testDomain = 'test-integration.com';
			const testData = {
				domain: testDomain,
				global_rank: 1000,
				overall_score: 0.85,
				performance_score: 0.80,
				security_score: 0.90,
				seo_score: 0.85,
				last_updated: new Date(),
			};

			// Insert test data
			const insertQuery = `
				INSERT INTO domain_analysis (
					domain, global_rank, overall_score, performance_score,
					security_score, seo_score, last_updated
				) VALUES (?, ?, ?, ?, ?, ?, ?)
			`;

			await scyllaClient.execute(insertQuery, [
				testData.domain,
				testData.global_rank,
				testData.overall_score,
				testData.performance_score,
				testData.security_score,
				testData.seo_score,
				testData.last_updated,
			]);

			// Retrieve and verify data
			const selectQuery = 'SELECT * FROM domain_analysis WHERE domain = ?';
			const result = await scyllaClient.execute(selectQuery, [testDomain]);

			expect(result.rows).toHaveLength(1);
			expect(result.rows[0].domain).toBe(testDomain);
			expect(result.rows[0].global_rank).toBe(testData.global_rank);

			// Cleanup
			await scyllaClient.execute('DELETE FROM domain_analysis WHERE domain = ?', [testDomain]);
		});
	});

	describe('MariaDB Operations', () =>
	{
		test('should connect to MariaDB successfully', async () =>
		{
			const mariaClient = databaseManager.getMariaClient();

			expect(mariaClient).toBeDefined();

			if (mariaClient.isReady())
			{
				const result = await mariaClient.query('SELECT 1 as test');

				expect(result).toBeDefined();
				expect(Array.isArray(result)).toBe(true);
			}
		});

		test('should manage domain categories', async () =>
		{
			const mariaClient = databaseManager.getMariaClient();

			if (!mariaClient.isReady())
			{
				console.warn('MariaDB not connected, skipping test');
				return;
			}

			// Insert test category
			const insertResult = await mariaClient.execute<ResultSetHeader>(
				'INSERT INTO domain_categories (name, description) VALUES (?, ?)',
				['test-category', 'Test category for integration testing'],
			);

			expect(insertResult.rows.insertId).toBeDefined();

			const categoryId = insertResult.rows.insertId;

			// Retrieve category
			const selectResult = await mariaClient.query(
				'SELECT * FROM domain_categories WHERE id = ?',
				[categoryId],
			);

			expect(selectResult).toHaveLength(1);
			expect(selectResult[0].name).toBe('test-category');

			// Cleanup
			await mariaClient.query('DELETE FROM domain_categories WHERE id = ?', [categoryId]);
		});
	});

	describe('Redis Operations', () =>
	{
		test('should connect to Redis successfully', async () =>
		{
			const redisClient = databaseManager.getRedisClient();

			expect(redisClient).toBeDefined();

			if (redisClient.isReady())
			{
				await redisClient.set('test-key', 'test-value');
				const value = await redisClient.get('test-key');

				expect(value).toBe('test-value');

				// Cleanup
				await redisClient.del('test-key');
			}
		});

		test('should cache domain ranking data', async () =>
		{
			const redisClient = databaseManager.getRedisClient();

			if (!redisClient.isReady())
			{
				console.warn('Redis not connected, skipping test');
				return;
			}

			const testDomain = 'cache-test.com';
			const rankingData = {
				domain: testDomain,
				globalRank: 500,
				categoryRank: 25,
				overallScore: 0.92,
				lastUpdated: new Date().toISOString(),
			};

			const cacheKey = `ranking:domain:${testDomain}`;

			// Cache data
			await redisClient.set(cacheKey, JSON.stringify(rankingData), 3600);

			// Retrieve cached data
			const cachedData = await redisClient.get<string>(cacheKey);

			expect(cachedData).toBeDefined();

			const parsedData = JSON.parse(cachedData!);

			expect(parsedData.domain).toBe(testDomain);
			expect(parsedData.globalRank).toBe(500);

			// Cleanup
			await redisClient.del(cacheKey);
		});
	});

	describe('Manticore Search Operations', () =>
	{
		test('should connect to Manticore successfully', async () =>
		{
			const manticoreClient = databaseManager.getManticoreClient();

			expect(manticoreClient).toBeDefined();

			if (manticoreClient.isReady())
			{
				const result = await manticoreClient.searchDomains({
					query: 'test',
					limit: 1,
				});

				expect(result).toHaveProperty('results');
				expect(Array.isArray(result.results)).toBe(true);
			}
		});

		test('should perform domain search operations', async () =>
		{
			const manticoreClient = databaseManager.getManticoreClient();

			if (!manticoreClient.isReady())
			{
				console.warn('Manticore not connected, skipping test');
				return;
			}

			// Test search functionality
			const searchResult = await manticoreClient.searchDomains({
				query: 'example',
				limit: 10,
			});

			expect(searchResult).toHaveProperty('results');
			expect(searchResult).toHaveProperty('total');
			expect(Array.isArray(searchResult.results)).toBe(true);
		});
	});

	describe('Cross-Database Data Consistency', () =>
	{
		test('should maintain data consistency across databases', async () =>
		{
			const testDomain = 'consistency-test.com';

			// Skip if databases are not connected
			if (!databaseManager.getScyllaClient().isReady() ||
				!databaseManager.getRedisClient().isReady())
			{
				console.warn('Databases not connected, skipping consistency test');
				return;
			}

			const domainData = {
				domain: testDomain,
				rank: 100,
				score: 0.88,
				category: 'technology',
			};

			try
			{
				// Sync data across databases
				await databaseManager.syncDomainData(domainData);

				// Verify data exists in ScyllaDB
				const scyllaResult = await databaseManager.getScyllaClient().execute(
					'SELECT * FROM domain_analysis WHERE domain = ?',
					[testDomain],
				);

				expect(scyllaResult.rows).toHaveLength(1);

				// Verify cache invalidation in Redis
				const cacheKey = `ranking:domain:${testDomain}`;
				const cachedData = await databaseManager.getRedisClient().get(cacheKey);

				expect(cachedData).toBeDefined();
			}
			finally
			{
				// Cleanup
				await databaseManager.getScyllaClient().execute(
					'DELETE FROM domain_analysis WHERE domain = ?',
					[testDomain],
				);
				await databaseManager.getRedisClient().del(`ranking:domain:${testDomain}`);
			}
		});
	});

	describe('Database Health Monitoring', () =>
	{
		test('should report database health status', async () =>
		{
			const healthStatus = await databaseManager.checkHealth();

			expect(healthStatus).toHaveProperty('scylla');
			expect(healthStatus).toHaveProperty('maria');
			expect(healthStatus).toHaveProperty('redis');
			expect(healthStatus).toHaveProperty('manticore');

			// Each database should have connection status
			Object.values(healthStatus).forEach((dbHealth) =>
			{
				expect(dbHealth).toHaveProperty('connected');
				expect(dbHealth).toHaveProperty('status');
			});
		});

		test('should track performance metrics', async () =>
		{
			// Execute some operations to generate metrics
			if (databaseManager.getScyllaClient().isReady())
			{
				await databaseManager.executeWithRetry(async () => await databaseManager.getScyllaClient().execute('SELECT now() FROM system.local'));
			}

			const stats = await databaseManager.getPerformanceStats('test');

			expect(stats).toHaveProperty('queryTime');
			expect(stats).toHaveProperty('slowQueries');
			expect(stats).toHaveProperty('errorRate');
			expect(stats).toHaveProperty('throughput');
			expect(typeof stats.queryTime).toBe('number');
			expect(typeof stats.slowQueries).toBe('number');
		});
	});

	describe('Service Integration', () =>
	{
		test('should integrate search service with databases', async () =>
		{
			if (!databaseManager.getManticoreClient().isReady())
			{
				console.warn('Manticore not connected, skipping service integration test');
				return;
			}

			try
			{
				const searchResults = await searchService.searchDomains('technology', {
					limit: 5,
				});

				expect(searchResults).toHaveProperty('domains');
				expect(searchResults).toHaveProperty('totalResults');
				expect(Array.isArray(searchResults.domains)).toBe(true);
			}
			catch (error)
			{
				// Service may not be fully configured in test environment
				console.warn('Search service integration test failed:', error.message);
			}
		});

		test('should integrate analysis service with databases', async () =>
		{
			if (!databaseManager.getScyllaClient().isReady())
			{
				console.warn('ScyllaDB not connected, skipping analysis service integration test');
				return;
			}

			try
			{
				const analysis = await analysisService.getDomainAnalysis('example.com');

				expect(analysis).toHaveProperty('domain');
				expect(analysis).toHaveProperty('metrics');
			}
			catch (error)
			{
				// Service may not find domain in test environment
				console.warn('Analysis service integration test failed:', error.message);
			}
		});
	});
});
