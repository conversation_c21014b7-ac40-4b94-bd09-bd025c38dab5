import {
	describe, test, expect, beforeAll, afterAll, vi,
} from 'vitest';
import request from 'supertest';
import { WebAppService } from '../..';

// Mock database connections for integration tests
vi.mock('@shared/database/DatabaseManager', () => ({
	DatabaseManager: vi.fn().mockImplementation(() => ({
		connect: vi.fn().mockResolvedValue(undefined),
		disconnect: vi.fn().mockResolvedValue(undefined),
		getScyllaClient: vi.fn().mockReturnValue({
			execute: vi.fn().mockResolvedValue({ rows: [] }),
		}),
		getMariaClient: vi.fn().mockReturnValue({
			query: vi.fn().mockResolvedValue([]),
		}),
		getRedisClient: vi.fn().mockReturnValue({
			get: vi.fn().mockResolvedValue(null),
			set: vi.fn().mockResolvedValue('OK'),
		}),
		getManticoreClient: vi.fn().mockReturnValue({
			search: vi.fn().mockResolvedValue({ hits: [], total: 0 }),
		}),
	})),
}));

describe('Web App API Integration Tests', () =>
{
	let app: any;
	let server: any;

	beforeAll(async () =>
	{
		// Initialize the web application
		app = new WebAppService();
		await app.initialize();
		server = app.getApp();
	});

	afterAll(async () =>
	{
		if (app)
		{
			await app.shutdown();
		}
	});

	describe('Domain Search API', () =>
	{
		test('GET /api/domains/search should return search results', async () =>
		{
			const response = await request(server)
				.get('/api/domains/search')
				.query({ query: 'technology', limit: 10 })
				.expect(200);

			expect(response.body).toHaveProperty('domains');
			expect(response.body).toHaveProperty('totalResults');
			expect(response.body).toHaveProperty('pagination');
			expect(Array.isArray(response.body.domains)).toBe(true);
		});

		test('GET /api/domains/search should validate query parameters', async () =>
		{
			await request(server)
				.get('/api/domains/search')
				.query({ query: '', limit: -1 })
				.expect(400);
		});

		test('GET /api/domains/search should handle search filters', async () =>
		{
			const response = await request(server)
				.get('/api/domains/search')
				.query({
					query: 'tech',
					category: 'technology',
					country: 'US',
					limit: 20,
					offset: 0,
				})
				.expect(200);

			expect(response.body.domains).toBeDefined();
		});
	});

	describe('Domain Analysis API', () =>
	{
		test('GET /api/domains/:domain/analysis should return domain analysis', async () =>
		{
			const response = await request(server)
				.get('/api/domains/example.com/analysis')
				.expect(200);

			expect(response.body).toHaveProperty('domain', 'example.com');
			expect(response.body).toHaveProperty('metrics');
			expect(response.body.metrics).toHaveProperty('performance');
			expect(response.body.metrics).toHaveProperty('security');
			expect(response.body.metrics).toHaveProperty('seo');
		});

		test('GET /api/domains/:domain/analysis should validate domain format', async () =>
		{
			await request(server)
				.get('/api/domains/invalid-domain/analysis')
				.expect(400);
		});

		test('GET /api/domains/:domain/analysis should handle non-existent domains', async () =>
		{
			await request(server)
				.get('/api/domains/nonexistent-domain-12345.com/analysis')
				.expect(404);
		});
	});


	describe('Top Domains API', () =>
	{
		test('GET /api/domains/top should return top domains', async () =>
		{
			const response = await request(server)
				.get('/api/domains/top')
				.query({ limit: 50 })
				.expect(200);

			expect(response.body).toHaveProperty('topDomains');
			expect(response.body).toHaveProperty('category');
			expect(Array.isArray(response.body.topDomains)).toBe(true);
		});

		test('GET /api/domains/top should filter by category', async () =>
		{
			const response = await request(server)
				.get('/api/domains/top')
				.query({ category: 'technology', limit: 25 })
				.expect(200);

			expect(response.body.category).toBe('technology');
		});

		test('GET /api/domains/top should validate limit parameter', async () =>
		{
			await request(server)
				.get('/api/domains/top')
				.query({ limit: 1001 })
				.expect(400);
		});
	});

	describe('Health Check API', () =>
	{
		test('GET /health should return service health status', async () =>
		{
			const response = await request(server)
				.get('/health')
				.expect(200);

			expect(response.body).toHaveProperty('status');
			expect(response.body).toHaveProperty('timestamp');
			expect(response.body).toHaveProperty('services');
		});

		test('GET /health/detailed should return detailed health information', async () =>
		{
			const response = await request(server)
				.get('/health/detailed')
				.expect(200);

			expect(response.body).toHaveProperty('status');
			expect(response.body).toHaveProperty('services');
			expect(response.body.services).toHaveProperty('database');
			expect(response.body.services).toHaveProperty('cache');
			expect(response.body.services).toHaveProperty('search');
		});
	});

	describe('Error Handling', () =>
	{
		test('should handle 404 for non-existent endpoints', async () =>
		{
			await request(server)
				.get('/api/nonexistent')
				.expect(404);
		});

		test('should handle malformed JSON in request body', async () =>
		{
			await request(server)
				.post('/api/domains/search')
				.send('invalid json')
				.set('Content-Type', 'application/json')
				.expect(400);
		});

		test('should include CORS headers', async () =>
		{
			const response = await request(server)
				.get('/api/domains/top')
				.expect(200);

			expect(response.headers).toHaveProperty('access-control-allow-origin');
		});

		test('should include security headers', async () =>
		{
			const response = await request(server)
				.get('/api/domains/top')
				.expect(200);

			expect(response.headers).toHaveProperty('x-content-type-options');
			expect(response.headers).toHaveProperty('x-frame-options');
		});
	});

	describe('Rate Limiting', () =>
	{
		test('should apply rate limiting to search endpoints', async () =>
		{
			// Make multiple rapid requests
			const requests = Array.from({ length: 20 }, () => request(server)
				.get('/api/domains/search')
				.query({ query: 'test' }));

			const responses = await Promise.all(requests);

			// Some requests should be rate limited
			const rateLimitedResponses = responses.filter(res => res.status === 429);

			expect(rateLimitedResponses.length).toBeGreaterThan(0);
		});
	});

	describe('Content Negotiation', () =>
	{
		test('should return JSON by default', async () =>
		{
			const response = await request(server)
				.get('/api/domains/top')
				.expect(200);

			expect(response.headers['content-type']).toMatch(/application\/json/);
		});

		test('should handle Accept header for JSON', async () =>
		{
			const response = await request(server)
				.get('/api/domains/top')
				.set('Accept', 'application/json')
				.expect(200);

			expect(response.headers['content-type']).toMatch(/application\/json/);
		});
	});
});
