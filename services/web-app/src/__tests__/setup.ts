// Test setup for web application
import { vi } from 'vitest';

// Mock environment variables
process.env.NODE_ENV = 'test';
process.env.PORT = '3001';
process.env.SCYLLA_HOSTS = 'localhost:9042';
process.env.MARIA_HOST = 'localhost';
process.env.MARIA_PORT = '3306';
process.env.MARIA_USER = 'test';
process.env.MARIA_PASSWORD = 'test';
process.env.MARIA_DATABASE = 'test_db';
process.env.REDIS_URL = 'redis://localhost:6379';
process.env.MANTICORE_HOST = 'localhost';
process.env.MANTICORE_PORT = '9308';

// Increase timeout for integration tests
vi.setConfig({ testTimeout: 30000 });

// Mock React SSR for testing
vi.mock('react-dom/server', () => ({
	renderToString: vi.fn(() => '<div>Mocked React Component</div>')
}));

// Mock ultimate-express
vi.mock('ultimate-express', () => {
	const mockApp = {
		use: vi.fn(),
		get: vi.fn(),
		post: vi.fn(),
		put: vi.fn(),
		delete: vi.fn(),
		listen: vi.fn((port, callback) => {
			if (callback) callback();
			return { close: vi.fn() };
		})
	};

	return vi.fn(() => mockApp);
});

// Global test utilities
global.testUtils = {
	createMockRequest: (overrides = {}) => ({
		params: {},
		query: {},
		body: {},
		headers: {},
		...overrides
	}),

	createMockResponse: () => {
		const res = {
			status: vi.fn().mockReturnThis(),
			json: vi.fn().mockReturnThis(),
			send: vi.fn().mockReturnThis(),
			render: vi.fn().mockReturnThis(),
			setHeader: vi.fn().mockReturnThis()
		};
		return res;
	}
};
