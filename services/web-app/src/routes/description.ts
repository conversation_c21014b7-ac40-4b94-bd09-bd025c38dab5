import { DatabaseManager, logger as sharedLogger, DomainDescriptionValidator, DomainDescriptionCacheService } from '@shared';
import { Router, Request, Response } from 'ultimate-express';

const router = Router();
const logger = sharedLogger.getLogger('DescriptionRoutes');
const db = new DatabaseManager();
let cacheService: DomainDescriptionCacheService | null = null;

router.get('/:domain/description', async (req: Request, res: Response) =>
{
	const { domain } = req.params;
	try
	{
		await db.initialize();
		
		// Initialize cache service if not already done
		if (!cacheService)
		{
			cacheService = new DomainDescriptionCacheService(db.getRedisClient(), {
				keyPrefix: 'dd:desc',
				fullDescriptionTtl: 24 * 60 * 60, // 24 hours
				summaryTtl: 6 * 60 * 60, // 6 hours
				metadataTtl: 1 * 60 * 60, // 1 hour
				enableCompression: true,
				enableWarmup: true,
				batchSize: 100,
			});
		}

		// Try to get from cache first
		const cached = await cacheService.get(domain);
		if (cached)
		{
			logger.debug(`Cache hit for domain description: ${domain}`);
			return res.status(200).json(cached);
		}

		logger.debug(`Cache miss for domain description: ${domain}`);

		const scylla = db.getScyllaClient();
		const query = 'SELECT domain, category, performance_metrics, security_metrics, seo_metrics, technical_metrics, technologies, server_info, domain_age_days, registrar, screenshot_urls, last_crawled FROM domain_analysis WHERE domain = ?';
		const result = await scylla.execute(query, [domain]);
		const row = result.rows?.[0];
		if (!row) return res.status(404).json({ error: 'Not found' });

		const desc: any = {
			metadata: {
				domain: row.domain,
				tld: String(row.domain).split('.').pop() || '',
				status: 'active',
				category: { primary: row.category || 'uncategorized' },
				owner: { registrar: row.registrar || undefined },
				ageDays: row.domain_age_days || undefined,
			},
			technical: {
				technologies: Array.isArray(row.technologies) ? row.technologies : (row.technologies ? Array.from(row.technologies) : []),
				performance: row.performance_metrics ? { loadTimeMs: Number(row.performance_metrics.load_time) || undefined } : undefined,
				security: row.security_metrics ? { sslGrade: row.security_metrics.ssl_grade } : undefined,
			},
			seo: row.seo_metrics ? { title: row.seo_metrics.title, metaDescription: row.seo_metrics.description } : {},
			ranking: {},
			crawl: { lastCrawled: new Date(row.last_crawled || Date.now()).toISOString(), crawlType: 'quick' },
		};

		if (process.env.VALIDATE_DESCRIPTIONS === '1')
		{
			const validator = DomainDescriptionValidator.get();
			validator.assert(desc);
		}

		// Cache the description for future requests
		await cacheService.set(domain, desc);

		return res.status(200).json(desc);
	}
	catch (error)
	{
		logger.error(`Description fetch failed for ${domain}: ${error}`);
		return res.status(500).json({ error: 'Internal Server Error' });
	}
});

export default router;
