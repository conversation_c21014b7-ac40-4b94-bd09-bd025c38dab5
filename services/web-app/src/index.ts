import { config as loadEnv } from 'dotenv';
import { logger as sharedLogger } from '@shared';
import { WebAppService } from './app/WebAppService';

// Load environment variables from .env
loadEnv();

const logger = sharedLogger.getLogger('WebApp');

// Initialize and start the service
async function main(): Promise<void>
{
	const webApp = new WebAppService();

	try
	{
		await webApp.initialize();
		await webApp.start();

		// Graceful shutdown handling
		process.on('SIGTERM', async () =>
		{
			await webApp.shutdown();
			process.exit(0);
		});

		process.on('SIGINT', async () =>
		{
			await webApp.shutdown();
			process.exit(0);
		});
	}
	catch (error)
	{
		logger.error({ error }, 'Failed to start Web Application Service');
		process.exit(1);
	}
}

// Start the service if this file is run directly
if (process.argv[1] && process.argv[1].endsWith('index.ts') || process.argv[1] && process.argv[1].endsWith('index.js'))
{
	main();
}

export { WebAppService };
