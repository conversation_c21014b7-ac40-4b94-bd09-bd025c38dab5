<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title><%= title || 'Error' %> · Domain Ranking</title>
  <meta name="color-scheme" content="light dark" />
  <meta name="theme-color" content="#f8fafc" id="meta-theme-color" />
  <script>
    (function () {
      try {
        var key = 'theme';
        var stored = localStorage.getItem(key);
        var prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
        var theme = stored || (prefersDark ? 'dark' : 'light');
        document.documentElement.setAttribute('data-theme', theme);
      } catch (e) {}
    })();
  </script>
  <script>
    (function () {
      try {
        var meta = document.getElementById('meta-theme-color');
        if (meta) {
          var t = document.documentElement.getAttribute('data-theme') || 'light';
          meta.setAttribute('content', t === 'dark' ? '#0b1220' : '#f8fafc');
        }
      } catch (e) {}
    })();
  </script>
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css" />
  <link rel="stylesheet" href="/static/assets/styles/app.css" />
</head>
<body>
  <section class="section">
    <div class="container" style="max-width: 860px;">
      <div class="box card-pro" style="padding: 2rem;">
        <div class="is-flex is-align-items-center is-justify-content-space-between mb-3">
          <h1 class="title is-4 m-0"><%= title || 'Something went wrong' %></h1>
          <span class="tag is-light mono">HTTP <%= status || 500 %></span>
        </div>
        <p class="has-text-grey mb-3"><%= message || 'An unexpected error occurred.' %></p>
        <% if (typeof errorId !== 'undefined' && errorId) { %>
          <p class="has-text-grey is-size-7">Error ID: <code class="mono"><%= errorId %></code></p>
        <% } %>
        <div class="mt-4">
          <a class="button is-primary" href="/">← Back to Home</a>
        </div>
      </div>

      <% if (typeof showStack !== 'undefined' && showStack && typeof error !== 'undefined' && error && error.stack) { %>
        <div class="box" style="background: var(--card); border: 1px solid var(--border);">
          <h3 class="title is-6">Stack trace</h3>
          <pre class="mono" style="white-space: pre-wrap; background: rgba(0,0,0,0.04); padding: 12px; border-radius: 8px; overflow:auto; color: var(--text);"><%= error.stack %></pre>
        </div>
      <% } %>
    </div>
  </section>
</body>
</html>

