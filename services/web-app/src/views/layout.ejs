<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title><%= title %> | Domain Ranking System</title>
		<meta name="description" content="<%= description %>" />

		<!-- SEO Meta Tags -->
		<meta property="og:title" content="<%= title %> | Domain Ranking System" />
		<meta property="og:description" content="<%= description %>" />
		<meta property="og:type" content="website" />
		<meta name="twitter:card" content="summary_large_image" />
		<meta name="twitter:title" content="<%= title %> | Domain Ranking System" />
		<meta name="twitter:description" content="<%= description %>" />

		<!-- Favicon -->
		<link rel="icon" type="image/x-icon" href="/favicon.ico" />

		<!-- System theme awareness and browser UI color -->
		<meta name="color-scheme" content="light dark" />
		<meta name="theme-color" content="#f8fafc" id="meta-theme-color" />

		<!-- Early theme resolver to avoid flash -->
		<script>
			(function () {
				try {
					var key = "theme";
					var stored = localStorage.getItem(key);
					var prefersDark =
						window.matchMedia &&
						window.matchMedia("(prefers-color-scheme: dark)").matches;
					var theme = stored || (prefersDark ? "dark" : "light");
					document.documentElement.setAttribute("data-theme", theme);
				} catch (e) {}
			})();
		</script>
		<!-- Initialize theme-color meta based on resolved theme above -->
		<script>
			(function () {
				try {
					var meta = document.getElementById("meta-theme-color");
					if (meta) {
						var t = document.documentElement.getAttribute("data-theme") || "light";
						meta.setAttribute("content", t === "dark" ? "#0b1220" : "#f8fafc");
					}
				} catch (e) {}
			})();
		</script>
		<!-- Fonts -->
		<link rel="preconnect" href="https://fonts.googleapis.com" />
		<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
		<link
			href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap"
			rel="stylesheet"
		/>

		<!-- Styles -->
		<link
			rel="stylesheet"
			href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css"
		/>
		<link rel="stylesheet" href="/static/assets/styles/app.css" />
	</head>
	<body>
		<nav class="navbar is-light" role="navigation" aria-label="main navigation">
			<div class="container">
				<div class="navbar-brand">
					<a class="navbar-item brand-logo" href="/">Domain Ranking System</a>
					<a
						role="button"
						class="navbar-burger"
						aria-label="menu"
						aria-expanded="false"
						data-target="mainNavbar"
					>
						<span aria-hidden="true"></span>
						<span aria-hidden="true"></span>
						<span aria-hidden="true"></span>
					</a>
				</div>
				<div id="mainNavbar" class="navbar-menu">
					<div class="navbar-start">
						<a class="navbar-item" href="/">Home</a>
						<a class="navbar-item" href="/search">Search</a>
						<a class="navbar-item" href="/top-domains">Top Domains</a>
						<!-- Mobile search -->
						<div class="navbar-item is-hidden-desktop" onsubmit="return false;">
							<div class="field has-addons is-fullwidth">
								<div class="control is-expanded">
									<input id="header-search-input-mobile" class="input" type="text" placeholder="Search domains..." />
								</div>
								<div class="control">
									<button id="header-search-btn-mobile" class="button is-primary" type="button">Search</button>
								</div>
							</div>
						</div>
					</div>
					<div class="navbar-end">
						<!-- Desktop search -->
						<div class="navbar-item navbar-search is-hidden-touch">
							<div class="field has-addons is-fullwidth" onsubmit="return false;">
								<div class="control is-expanded">
									<input id="header-search-input" class="input" type="text" placeholder="Search domains..." />
								</div>
								<div class="control">
									<button id="header-search-btn" class="button is-primary" type="button">Search</button>
								</div>
							</div>
						</div>
						<div class="navbar-item">
							<button
								id="theme-toggle"
								class="button is-light"
								type="button"
								aria-label="Toggle theme"
								title="Toggle theme"
							>
								🌓
							</button>
						</div>
					</div>
				</div>
			</div>
		</nav>

		<main class="main">
			<div class="container">
				<div id="react-root"><%- reactContent %></div>
			</div>
		</main>

		<footer class="footer">
			<div class="container">
				<div class="columns">
					<div class="column is-4">
						<p class="has-text-weight-semibold">Product</p>
						<ul>
							<li><a href="/">Home</a></li>
							<li><a href="/search">Search</a></li>
							<li><a href="/top-domains">Top Domains</a></li>
						</ul>
					</div>
					<div class="column is-4">
						<p class="has-text-weight-semibold">Company</p>
						<ul>
							<li><a href="/about">About</a></li>
							<li><a href="/contact">Contact</a></li>
						</ul>
					</div>
					<div class="column is-4">
						<p class="has-text-weight-semibold">Legal</p>
						<ul>
							<li><a href="/privacy">Privacy</a></li>
							<li><a href="/terms">Terms</a></li>
						</ul>
					</div>
				</div>
				<hr class="divider-top" />
				<div class="is-flex is-justify-content-space-between is-align-items-center" style="gap: 12px; flex-wrap: wrap;">
					<div>&copy; <%= new Date().getFullYear() %> Domain Ranking System</div>
					<div class="has-text-grey is-size-7">Built for fast domain insights</div>
				</div>
			</div>
		</footer>

		<!-- React hydration data -->
		<script id="__INITIAL_DATA__" type="application/json"><%- JSON.stringify(initialData) %></script>
		<script>
			// Parse initial data safely to avoid inline template lint issues
			(function () {
				try {
					var el = document.getElementById("__INITIAL_DATA__");
					window.__INITIAL_DATA__ = {};
					if (el && el.textContent) {
						window.__INITIAL_DATA__ = JSON.parse(el.textContent);
					}
				} catch (e) {
					window.__INITIAL_DATA__ = {};
				}
			})();
		</script>

		<!-- Client-side React bundle would be loaded here in production -->
		<script>
			// Basic client-side functionality
			document.addEventListener("DOMContentLoaded", function () {
				// Active nav link
				try {
					var path = window.location.pathname;
					document
						.querySelectorAll(".navbar-menu a.navbar-item")
						.forEach(function (a) {
							var href = a.getAttribute("href") || "";
							if (href === "/") {
								if (path === "/") a.classList.add("is-active");
							} else {
								if (path.startsWith(href)) a.classList.add("is-active");
							}
						});
				} catch (e) {}

				// Bulma navbar burger toggle
				var burgers = Array.prototype.slice.call(
					document.querySelectorAll(".navbar-burger"),
					0
				);
				burgers.forEach(function (el) {
					el.addEventListener("click", function () {
						var targetId = el.getAttribute("data-target") || "";
						var target = document.getElementById(targetId);
						el.classList.toggle("is-active");
						if (target) {
							target.classList.toggle("is-active");
						}
						// Sync aria-expanded for accessibility
						el.setAttribute("aria-expanded", el.classList.contains("is-active") ? "true" : "false");
					});
				});

				// Header quick search
				var input = document.getElementById("header-search-input");
				var btn = document.getElementById("header-search-btn");
				var inputM = document.getElementById("header-search-input-mobile");
				var btnM = document.getElementById("header-search-btn-mobile");
				function normalize(d) { return String(d).toLowerCase().replace(/^https?:\/\//, '').replace(/^www\./, '').replace(/\/$/, ''); }
				function isDomain(s) { return /^(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)+[a-z]{2,}$/i.test(normalize(s)); }
				function go() {
					if (!input) return;
					var v = (input.value || "").trim();
					if (!v) return;
					if (isDomain(v)) { window.location.href = "/domain/" + encodeURIComponent(normalize(v)); return; }
					window.location.href = "/search?q=" + encodeURIComponent(v);
				}
				if (input) {
					input.addEventListener("keydown", function (e) {
						if (e.key === "Enter") go();
					});
				}
				if (btn) {
					btn.addEventListener("click", go);
				}
				function goM() {
					if (!inputM) return;
					var v = (inputM.value || "").trim();
					if (!v) return;
					if (isDomain(v)) { window.location.href = "/domain/" + encodeURIComponent(normalize(v)); return; }
					window.location.href = "/search?q=" + encodeURIComponent(v);
				}
				if (inputM) {
					inputM.addEventListener("keydown", function (e) {
						if (e.key === "Enter") goM();
					});
				}
				if (btnM) {
					btnM.addEventListener("click", goM);
				}

				// Theme toggle
				var THEME_KEY = "theme";
				function setTheme(t) {
					try {
						localStorage.setItem(THEME_KEY, t);
					} catch (e) {}
					document.documentElement.setAttribute("data-theme", t);
				}
				function getTheme() {
					return document.documentElement.getAttribute("data-theme") || "light";
				}
				function updateThemeMeta() {
					try {
						var el = document.getElementById("meta-theme-color");
						if (el) {
							var t = getTheme();
							el.setAttribute("content", t === "dark" ? "#0b1220" : "#f8fafc");
						}
					} catch (e) {}
				}
				function renderThemeToggle() {
					var el = document.getElementById("theme-toggle");
					if (!el) return;
					var t = getTheme();
					el.textContent = t === "dark" ? "☀️" : "🌙";
					el.classList.toggle("is-primary", t === "dark");
					updateThemeMeta();
				}
				var toggleEl = document.getElementById("theme-toggle");
				if (toggleEl) {
					renderThemeToggle();
					toggleEl.addEventListener("click", function () {
						var next = getTheme() === "dark" ? "light" : "dark";
						setTheme(next);
						renderThemeToggle();
					});
				}
			});
		</script>
	</body>
</html>
