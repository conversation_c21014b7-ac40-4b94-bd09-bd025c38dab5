import React from 'react';
import { renderToString } from 'react-dom/server';
import DomainAnalysisPage from '../components/DomainAnalysisPage';
import DomainViewPage from '../components/DomainViewPage';
import HomePage from '../components/HomePage';
import SearchPage from '../components/SearchPage';
import TopDomainsPage from '../components/TopDomainsPage';
import AboutPage from '../components/AboutPage';
import PrivacyPage from '../components/PrivacyPage';
import TermsPage from '../components/TermsPage';
import ContactPage from '../components/ContactPage';

export type PageComponent = 'HomePage' | 'SearchPage' | 'DomainAnalysisPage' | 'DomainViewPage' | 'TopDomainsPage' | 'AboutPage' | 'PrivacyPage' | 'TermsPage' | 'ContactPage';

export interface RenderOptions
{
	component: PageComponent;
	props?: Record<string, unknown>;
	title?: string;
	description?: string;
}

/**
 * React Server-Side Rendering utility
 */
export class ReactRenderer
{
	/**
	 * Render a React component to HTML string
	 */
	static renderComponent(options: RenderOptions): string
	{
		const { component, props = {} } = options;

		let ComponentToRender: React.ComponentType<Record<string, unknown>>;

		switch (component)
		{
			case 'HomePage':
				ComponentToRender = HomePage;
				break;
			case 'SearchPage':
				ComponentToRender = SearchPage;
				break;
			case 'DomainAnalysisPage':
				ComponentToRender = DomainAnalysisPage;
				break;
			case 'DomainViewPage':
				ComponentToRender = DomainViewPage;
				break;
			case 'TopDomainsPage':
				ComponentToRender = TopDomainsPage;
				break;
			case 'AboutPage':
				ComponentToRender = AboutPage;
				break;
			case 'PrivacyPage':
				ComponentToRender = PrivacyPage;
				break;
			case 'TermsPage':
				ComponentToRender = TermsPage;
				break;
			case 'ContactPage':
				ComponentToRender = ContactPage;
				break;
			default:
				throw new Error(`Unknown component: ${component}`);
		}

		try
		{
			return renderToString(React.createElement(ComponentToRender, props));
		}
		catch (error)
		{
			return `<div class="error">Error rendering component: ${component}</div>`;
		}
	}

	/**
	 * Get default page metadata
	 */
	static getPageMetadata(
		component: PageComponent,
		props?: Record<string, unknown>,
	): { title: string; description: string }
	{
		switch (component)
		{
			case 'HomePage':
				return {
					title: 'Home',
					description: 'Comprehensive domain analysis and ranking platform for evaluating website performance, security, and SEO metrics.',
				};
			case 'SearchPage': {
				const p = props as { initialData?: { query?: string } } | undefined;
				const query = p?.initialData?.query;
				return {
					title: query ? `Search Results for "${query}"` : 'Domain Search',
					description: query
						? `Search results for domain "${query}" - analyze website performance, security, and rankings.`
						: 'Search and analyze domains with comprehensive metrics including performance, security, and SEO scores.',
				};
			}
			case 'DomainAnalysisPage': {
				const p = props as { initialData?: { analysis?: { domain?: string } } } | undefined;
				const domain = p?.initialData?.analysis?.domain;
				return {
					title: domain ? `${domain} Analysis` : 'Domain Analysis',
					description: domain
						? `Comprehensive analysis of ${domain} including performance, security, SEO metrics, and global ranking.`
						: 'Detailed domain analysis with comprehensive metrics and insights.',
				};
			}
			case 'DomainViewPage': {
				const p = props as { initialData?: { analysis?: { domain?: string } } } | undefined;
				const domain = p?.initialData?.analysis?.domain;
				return {
					title: domain ? `${domain} - Domain View` : 'Domain View',
					description: domain
						? `Comprehensive view of ${domain} with detailed metrics, performance analysis, security assessment, and ranking insights.`
						: 'Detailed domain view with comprehensive metrics, analytics, and insights.',
				};
			}
			case 'TopDomainsPage': {
				const p = props as { initialData?: { selectedCategory?: string } } | undefined;
				const category = p?.initialData?.selectedCategory;
				return {
					title: category ? `Top ${category} Domains` : 'Top Domains',
					description: category
						? `Discover the highest-ranked domains in ${category} based on comprehensive analysis of performance, security, and SEO metrics.`
						: 'Discover the highest-ranked domains across all categories based on comprehensive analysis and performance metrics.',
				};
			}
			case 'AboutPage':
				return { title: 'About', description: 'About Domain Ranking System' };
			case 'PrivacyPage':
				return { title: 'Privacy Policy', description: 'Our commitment to your privacy' };
			case 'TermsPage':
				return { title: 'Terms of Service', description: 'The terms governing the use of our service' };
			case 'ContactPage':
				return { title: 'Contact', description: 'Get in touch with the Domain Ranking team' };
			default:
				return {
					title: 'Domain Ranking System',
					description: 'Comprehensive domain analysis and ranking platform.',
				};
		}
	}

	/**
	 * Render a complete page with layout
	 */
	static renderPage(options: RenderOptions): {
		reactContent: string;
		title: string;
		description: string;
		initialData: Record<string, unknown>;
	}
	{
		const reactContent = this.renderComponent(options);
		const metadata = this.getPageMetadata(options.component, options.props);

		return {
			reactContent,
			title: options.title || metadata.title,
			description: options.description || metadata.description,
			initialData: (options.props?.initialData || {}) as Record<string, unknown>,
		};
	}
}

export default ReactRenderer;
