import React, { useState } from 'react';

interface DomainComparisonPageProps
{
	initialData?: {
		comparison?: DomainComparison;
		domains?: string[];
		error?: string;
	};
}

interface DomainComparison
{
	domains: DomainComparisonData[];
	metrics: MetricComparison;
	recommendations: string[];
}

interface DomainComparisonData
{
	domain: string;
	globalRank?: number;
	categoryRank?: number;
	category?: string;
	overallScore?: number;
	performanceScore?: number;
	securityScore?: number;
	seoScore?: number;
	technicalScore?: number;
	backlinkScore?: number;
	trafficEstimate?: number;
	sslGrade?: string;
	technologies?: string[];
	lastUpdated?: string;
	favicon?: string;
}

interface MetricComparison
{
	performance: MetricComparisonData;
	security: MetricComparisonData;
	seo: MetricComparisonData;
	technical: MetricComparisonData;
	overall: MetricComparisonData;
}

interface MetricComparisonData
{
	winner: string;
	scores: Array<{ domain: string; score: number }>;
	insights: string[];
}

const DomainComparisonPage: React.FC<DomainComparisonPageProps> = ({ initialData = {} as DomainComparisonPageProps['initialData'] }) =>
{
	const { comparison, domains = [], error } = initialData || {};
	const [selectedDomains, setSelectedDomains] = useState<string[]>(domains);
	const [isLoading, setIsLoading] = useState(false);
	const [comparisonData, setComparisonData] = useState<DomainComparison | null>(comparison || null);

	const handleAddDomain = (): void =>
	{
		if (selectedDomains.length < 5)
		{
			setSelectedDomains([...selectedDomains, '']);
		}
	};

	const handleRemoveDomain = (index: number): void =>
	{
		const newDomains = selectedDomains.filter((_, i) => i !== index);
		setSelectedDomains(newDomains);
	};

	const handleDomainChange = (index: number, value: string): void =>
	{
		const newDomains = [...selectedDomains];
		newDomains[index] = value;
		setSelectedDomains(newDomains);
	};

	const handleCompare = async (): Promise<void> =>
	{
		const validDomains = selectedDomains.filter(d => d.trim().length > 0);

		if (validDomains.length < 2)
		{
			alert('Please enter at least 2 domains to compare');
			return;
		}

		setIsLoading(true);

		try
		{
			const response = await fetch('/api/domains/compare', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ domains: validDomains }),
			});

			const data = await response.json();

			if (response.ok)
			{
				setComparisonData(data as DomainComparison);

				const params = new URLSearchParams();
				validDomains.forEach(domain => params.append('domain', domain));
				const newUrl = `/compare?${params.toString()}`;
				window.history.pushState({}, '', newUrl);
			}
			else
			{
				const message = (data as { message?: string }).message || 'Unknown error';
				alert(`Comparison failed: ${message}`);
			}
		}
		catch (err)
		{
			alert('Failed to compare domains. Please try again.');
		}
		finally
		{
			setIsLoading(false);
		}
	};

	if (error)
	{
		return (
			<div className="domain-comparison-page">
				<div
					style={{
						backgroundColor: 'white',
						padding: '3rem',
						borderRadius: '12px',
						textAlign: 'center',
						color: '#ef4444',
					}}
				>
					<div style={{ fontSize: '3rem', marginBottom: '1rem' }}>⚠️</div>
					<h2>Comparison Not Available</h2>
					<p>{error}</p>
				</div>
			</div>
		);
	}

	return (
		<div className="domain-comparison-page">
			<div
				style={{
					backgroundColor: 'white',
					padding: '2rem',
					borderRadius: '12px',
					boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
					marginBottom: '2rem',
				}}
			>
				<h1 style={{ marginBottom: '1rem', fontSize: '2.5rem', color: '#333' }}>
					Domain Comparison
				</h1>
				<p style={{ color: '#666', fontSize: '1.1rem', marginBottom: '2rem' }}>
					Compare up to 5 domains side by side to analyze their performance, security, and SEO metrics.
				</p>

				<div style={{ marginBottom: '2rem' }}>
					<h3 style={{ marginBottom: '1rem', color: '#666' }}>Domains to Compare</h3>
					<div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
						{selectedDomains.map((domain, index) =>
						{
							const key = `${domain || 'empty'}-${index}`;
							return (
								<div key={key} style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
									<input
										className="input"
										type="text"
										value={domain}
										onChange={e => handleDomainChange(index, e.target.value)}
										placeholder={`Domain ${index + 1} (e.g., example.com)`}
										style={{ flex: 1 }}
									/>
									{selectedDomains.length > 2 && (
										<button
											type="button"
											onClick={() => handleRemoveDomain(index)}
											className="btn"
											style={{ background: '#ef4444', color: '#fff' }}
										>
											Remove
										</button>
									)}
								</div>
							);
						})}
					</div>

					<div style={{ display: 'flex', gap: '1rem', marginTop: '1rem' }}>
						{selectedDomains.length < 5 && (
							<button
								type="button"
								onClick={handleAddDomain}
								style={{
									padding: '0.75rem 1.5rem',
									backgroundColor: '#6b7280',
									color: 'white',
									border: 'none',
									borderRadius: '8px',
									cursor: 'pointer',
									fontWeight: 500,
								}}
							>
								Add Domain
							</button>
						)}
						<button
							type="button"
							onClick={handleCompare}
							disabled={isLoading || selectedDomains.filter(d => d.trim()).length < 2}
							style={{
								padding: '0.75rem 2rem',
								backgroundColor: isLoading ? '#9ca3af' : '#2563eb',
								color: 'white',
								border: 'none',
								borderRadius: '8px',
								cursor: isLoading ? 'not-allowed' : 'pointer',
								fontWeight: 600,
								display: 'flex',
								alignItems: 'center',
								gap: '0.5rem',
							}}
						>
							{isLoading && <div className="loading" style={{ width: '16px', height: '16px' }} />}
							{isLoading ? 'Comparing...' : 'Compare Domains'}
						</button>
					</div>
				</div>
			</div>

			{isLoading && (
				<div
					style={{
						backgroundColor: 'white',
						padding: '3rem',
						borderRadius: '12px',
						textAlign: 'center',
						color: '#666',
					}}
				>
					<div className="loading" style={{ width: '40px', height: '40px', margin: '0 auto 1rem' }} />
					<h3>Analyzing domains...</h3>
					<p>Please wait while we compare the selected domains</p>
				</div>
			)}

			{comparisonData && !isLoading && (
				<>
					<OverviewComparison domains={comparisonData.domains} />
					<MetricsComparison metrics={comparisonData.metrics} />
					{comparisonData.recommendations && comparisonData.recommendations.length > 0 && (
						<RecommendationsSection recommendations={comparisonData.recommendations} />
					)}
				</>
			)}
		</div>
	);
};

interface OverviewComparisonProps
{
	domains: DomainComparisonData[];
}

const OverviewComparison: React.FC<OverviewComparisonProps> = ({ domains }) => (
	<div
		style={{
			backgroundColor: 'white',
			padding: '2rem',
			borderRadius: '12px',
			boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
			marginBottom: '2rem',
		}}
	>
		<h2 style={{
			marginBottom: '2rem', display: 'flex', alignItems: 'center', gap: '0.5rem',
		}}
		>
			<span>📊</span>
			Overview Comparison
		</h2>

		<div style={{ overflowX: 'auto' }}>
			<table style={{ width: '100%', borderCollapse: 'collapse' }}>
				<thead>
					<tr style={{ borderBottom: '2px solid #e5e7eb' }}>
						<th style={{ textAlign: 'left', padding: '1rem', color: '#666' }}>Domain</th>
						<th style={{ textAlign: 'center', padding: '1rem', color: '#666' }}>Global Rank</th>
						<th style={{ textAlign: 'center', padding: '1rem', color: '#666' }}>Overall Score</th>
						<th style={{ textAlign: 'center', padding: '1rem', color: '#666' }}>Performance</th>
						<th style={{ textAlign: 'center', padding: '1rem', color: '#666' }}>Security</th>
						<th style={{ textAlign: 'center', padding: '1rem', color: '#666' }}>SEO</th>
						<th style={{ textAlign: 'center', padding: '1rem', color: '#666' }}>Technical</th>
						<th style={{ textAlign: 'center', padding: '1rem', color: '#666' }}>Traffic</th>
					</tr>
				</thead>
				<tbody>
					{domains.map(domain => (
						<tr key={domain.domain} style={{ borderBottom: '1px solid #f3f4f6' }}>
							<td style={{ padding: '1rem' }}>
								<div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
									{domain.favicon && (
										<img
											src={domain.favicon}
											alt={`${domain.domain} favicon`}
											style={{ width: '24px', height: '24px', borderRadius: '4px' }}
										/>
									)}
									<div>
										<div style={{ fontWeight: 600 }}>{domain.domain}</div>
										<div style={{ fontSize: '0.8rem', color: '#666' }}>{domain.category}</div>
									</div>
								</div>
							</td>
							<td style={{ textAlign: 'center', padding: '1rem' }}>
								{domain.globalRank ? `#${domain.globalRank.toLocaleString()}` : 'N/A'}
							</td>
							<td style={{ textAlign: 'center', padding: '1rem' }}>
								<ScoreCell score={domain.overallScore} />
							</td>
							<td style={{ textAlign: 'center', padding: '1rem' }}>
								<ScoreCell score={domain.performanceScore} />
							</td>
							<td style={{ textAlign: 'center', padding: '1rem' }}>
								<ScoreCell score={domain.securityScore} />
							</td>
							<td style={{ textAlign: 'center', padding: '1rem' }}>
								<ScoreCell score={domain.seoScore} />
							</td>
							<td style={{ textAlign: 'center', padding: '1rem' }}>
								<ScoreCell score={domain.technicalScore} />
							</td>
							<td style={{ textAlign: 'center', padding: '1rem', fontSize: '0.9rem' }}>
								{formatTraffic(domain.trafficEstimate)}
							</td>
						</tr>
					))}
				</tbody>
			</table>
		</div>
	</div>
);

interface MetricsComparisonProps
{
	metrics: MetricComparison;
}

const MetricsComparison: React.FC<MetricsComparisonProps> = ({ metrics }) =>
{
	const metricCategories = [
		{ key: 'overall', title: 'Overall Score', icon: '📊' },
		{ key: 'performance', title: 'Performance', icon: '⚡' },
		{ key: 'security', title: 'Security', icon: '🔒' },
		{ key: 'seo', title: 'SEO', icon: '🔍' },
		{ key: 'technical', title: 'Technical', icon: '⚙️' },
	];

	return (
		<div
			style={{
				backgroundColor: 'white',
				padding: '2rem',
				borderRadius: '12px',
				boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
				marginBottom: '2rem',
			}}
		>
			<h2 style={{
				marginBottom: '2rem', display: 'flex', alignItems: 'center', gap: '0.5rem',
			}}
			>
				<span>🎯</span>
				Detailed Metrics Comparison
			</h2>

			<div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '2rem' }}>
				{metricCategories.map((category) =>
				{
					const metricData = metrics[category.key as keyof MetricComparison] as MetricComparisonData | undefined;
					if (!metricData) return null;

					return (
						<div
							key={category.key}
							style={{
								padding: '1.5rem',
								border: '2px solid #f3f4f6',
								borderRadius: '12px',
								backgroundColor: '#f9fafb',
							}}
						>
							<h3
								style={{
									marginBottom: '1rem',
									display: 'flex',
									alignItems: 'center',
									gap: '0.5rem',
								}}
							>
								<span>{category.icon}</span>
								{category.title}
							</h3>

							<div style={{ marginBottom: '1rem' }}>
								<div
									style={{
										fontSize: '0.9rem',
										color: '#666',
										marginBottom: '0.5rem',
									}}
								>
									Winner: <strong style={{ color: '#10b981' }}>{metricData.winner}</strong>
								</div>

								<div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
									{metricData.scores
										.sort((a, b) => b.score - a.score)
										.map((score, index) => (
											<div
												key={score.domain}
												style={{
													display: 'flex',
													justifyContent: 'space-between',
													alignItems: 'center',
													padding: '0.5rem',
													backgroundColor: index === 0 ? '#10b98120' : 'white',
													borderRadius: '6px',
													border: index === 0 ? '2px solid #10b981' : '1px solid #e5e7eb',
												}}
											>
												<span style={{ fontWeight: index === 0 ? 600 : 400 }}>{score.domain}</span>
												<span
													style={{
														fontWeight: 600,
														color: getScoreColor(score.score),
													}}
												>
													{Math.round(score.score * 100)}%
												</span>
											</div>
										))}
								</div>
							</div>

							{metricData.insights && metricData.insights.length > 0 && (
								<div>
									<div style={{ fontSize: '0.9rem', color: '#666', marginBottom: '0.5rem' }}>Key Insights:</div>
									<ul style={{ fontSize: '0.8rem', color: '#374151', paddingLeft: '1rem' }}>
										{metricData.insights.map((insight, index) => (
											<li key={index} style={{ marginBottom: '0.25rem' }}>
												{insight}
											</li>
										))}
									</ul>
								</div>
							)}
						</div>
					);
				})}
			</div>
		</div>
	);
};

interface RecommendationsSectionProps
{
	recommendations: string[];
}

const RecommendationsSection: React.FC<RecommendationsSectionProps> = ({ recommendations }) => (
	<div
		style={{
			backgroundColor: 'white',
			padding: '2rem',
			borderRadius: '12px',
			boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
		}}
	>
		<h2 style={{
			marginBottom: '1rem', display: 'flex', alignItems: 'center', gap: '0.5rem',
		}}
		>
			<span>💡</span>
			Recommendations
		</h2>
		<ul style={{ paddingLeft: '1.5rem', lineHeight: '1.6' }}>
			{recommendations.map((rec, index) => (
				<li key={index} style={{ marginBottom: '0.75rem', color: '#374151' }}>
					{rec}
				</li>
			))}
		</ul>
	</div>
);

interface ScoreCellProps
{
	score?: number;
}

const ScoreCell: React.FC<ScoreCellProps> = ({ score }) =>
{
	if (!score) return <span style={{ color: '#9ca3af' }}>N/A</span>;

	const percentage = Math.round(score * 100);
	const color = getScoreColor(score);

	return (
		<div
			style={{
				display: 'inline-flex',
				alignItems: 'center',
				justifyContent: 'center',
				padding: '0.25rem 0.75rem',
				backgroundColor: `${color}20`,
				color,
				borderRadius: '20px',
				fontWeight: 600,
				fontSize: '0.9rem',
			}}
		>
			{percentage}%
		</div>
	);
};

function getScoreColor(score: number): string
{
	if (score >= 0.8) return '#10b981';
	if (score >= 0.6) return '#f59e0b';
	if (score >= 0.4) return '#f97316';
	return '#ef4444';
}

function formatTraffic(traffic?: number): string
{
	if (!traffic) return 'N/A';
	if (traffic >= 1000000) return `${(traffic / 1000000).toFixed(1)}M`;
	if (traffic >= 1000) return `${(traffic / 1000).toFixed(1)}K`;
	return traffic.toString();
}

export default DomainComparisonPage;
