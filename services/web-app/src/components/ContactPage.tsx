import React from 'react';

type ContactPageProps = {
    initialData?: Record<string, unknown>;
};

function ContactCard(): JSX.Element
{
    return (
        <div className="box card-pro">
            <h2 className="title is-4 mb-3">Get in touch</h2>
            <p className="mb-4 has-text-grey">
                We would love to hear your feedback, feature requests, or partnership ideas.
            </p>
            <div className="content">
                <p className="mb-2">
                    <span className="icon" aria-hidden="true">📧</span>
                    <span className="ml-2">Email: <a href="mailto:<EMAIL>"><EMAIL></a></span>
                </p>
                <p className="mb-2">
                    <span className="icon" aria-hidden="true">💬</span>
                    <span className="ml-2">Twitter/X: <a href="https://twitter.com" target="_blank" rel="noopener noreferrer">@domainr</a></span>
                </p>
            </div>
        </div>
    );
}

function ContactPage(_props: ContactPageProps): JSX.Element
{
    return (
        <div className="content-page container is-max-desktop">
            <div className="box page-hero mb-5">
                <h1 className="title is-3 mb-2">Contact</h1>
                <p className="has-text-grey">Questions, feedback, or collaborations — drop us a line.</p>
            </div>
            <ContactCard />
        </div>
    );
}

export default ContactPage;
