import React, { useState } from 'react';

interface SearchPageProps
{
	initialData?: {
		query?: string;
		results?: DomainResult[];
		facets?: SearchFacets;
		pagination?: PaginationInfo;
		filters?: SearchFilters;
		took?: number;
	};
}

interface DomainResult
{
	domain: string;
	title?: string;
	description?: string;
	category?: string;
	country?: string;
	globalRank?: number;
	categoryRank?: number;
	overallScore?: number;
	performanceScore?: number;
	securityScore?: number;
	seoScore?: number;
	technicalScore?: number;
	backlinkScore?: number;
	trafficEstimate?: number;
	sslGrade?: string;
	technologies?: string[];
	lastUpdated?: string;
	screenshot?: string;
	favicon?: string;
}

interface SearchFacets
{
	categories?: Array<{ name: string; count: number }>;
	countries?: Array<{ name: string; count: number }>;
	technologies?: Array<{ name: string; count: number }>;
	sslGrades?: Array<{ name: string; count: number }>;
}

interface PaginationInfo
{
	page: number;
	limit: number;
	total: number;
	totalPages: number;
	hasNext: boolean;
	hasPrev: boolean;
}

interface SearchFilters
{
	category?: string;
	country?: string;
	technology?: string;
	sslGrade?: string;
	minRank?: number;
	maxRank?: number;
	minScore?: number;
	maxScore?: number;
	sort?: string;
}

const SearchPage: React.FC<SearchPageProps> = ({ initialData = {} }) =>
{
	const {
		query = '',
		results = [],
		facets = {},
		pagination = {},
		filters = {},
		took = 0,
	} = initialData || {};

	const [searchQuery, setSearchQuery] = useState(query);
	const [activeFilters, setActiveFilters] = useState<SearchFilters>(filters);
	const [isLoading, setIsLoading] = useState(false);
	const [searchResults, setSearchResults] = useState<DomainResult[]>(results);
	const [searchFacets, setSearchFacets] = useState<SearchFacets>(facets);
	const [paginationInfo, setPaginationInfo] = useState<PaginationInfo>(pagination as PaginationInfo);
	const [searchTime, setSearchTime] = useState(took);

	return (
		<div className="search-page">
			<div className="search-header mb-4">
				<h1 className="title is-2 mb-3">
					Domain Search
				</h1>
				<SearchForm
					initialQuery={searchQuery}
					onSearch={handleSearch}
					isLoading={isLoading}
				/>
				{searchQuery && (
					<SearchStats
						query={searchQuery}
						totalResults={paginationInfo.total || 0}
						searchTime={searchTime}
					/>
				)}
			</div>

			<div className="search-layout">
				<aside className="search-filters">
					<SearchFilters
						facets={searchFacets}
						activeFilters={activeFilters}
						onFilterChange={handleFilterChange}
						onClearFilters={handleClearFilters}
					/>
				</aside>

				<main className="search-results">
					<SearchResults
						results={searchResults}
						pagination={paginationInfo}
						isLoading={isLoading}
						onPageChange={handlePageChange}
						onSortChange={handleSortChange}
						activeSort={activeFilters.sort || 'rank'}
					/>
				</main>
			</div>
		</div>
	);

	async function handleSearch(newQuery: string): Promise<void>
	{
		setIsLoading(true);
		setSearchQuery(newQuery);

		try
		{
			const searchParams = new URLSearchParams({
				q: newQuery,
				...activeFilters,
				page: '1',
			});

			const response = await fetch(`/api/domains/search?${searchParams}`);
			const data = await response.json();

			if (response.ok)
			{
				setSearchResults(data.domains || []);
				setSearchFacets(data.facets || {});
				setPaginationInfo(data.pagination || {});
				setSearchTime(data.took || 0);

				// Update URL without page reload
				const newUrl = `/search?${searchParams}`;
				window.history.pushState({}, '', newUrl);
			}
			else
			{
				console.error('Search failed:', data.message);
			}
		}
		catch (error)
		{
			console.error('Search error:', error);
		}
		finally
		{
			setIsLoading(false);
		}
	}

	async function handleFilterChange(filterKey: keyof SearchFilters, value: string | number | undefined): Promise<void>
	{
		const newFilters = { ...activeFilters };

		if (value === undefined || value === '')
		{
			delete newFilters[filterKey];
		}
		else
		{
			newFilters[filterKey] = value;
		}

		setActiveFilters(newFilters);

		// Trigger new search with updated filters
		if (searchQuery)
		{
			await performSearch(searchQuery, newFilters, 1);
		}
	}

	async function handleClearFilters(): Promise<void>
	{
		setActiveFilters({});

		if (searchQuery)
		{
			await performSearch(searchQuery, {}, 1);
		}
	}

	async function handlePageChange(page: number): Promise<void>
	{
		await performSearch(searchQuery, activeFilters, page);
	}

	async function handleSortChange(sort: string): Promise<void>
	{
		const newFilters = { ...activeFilters, sort };
		setActiveFilters(newFilters);
		await performSearch(searchQuery, newFilters, 1);
	}

	async function performSearch(query: string, filters: SearchFilters, page: number): Promise<void>
	{
		setIsLoading(true);

		try
		{
			const searchParams = new URLSearchParams({
				q: query,
				...filters,
				page: page.toString(),
			});

			const response = await fetch(`/api/domains/search?${searchParams}`);
			const data = await response.json();

			if (response.ok)
			{
				setSearchResults(data.domains || []);
				setSearchFacets(data.facets || {});
				setPaginationInfo(data.pagination || {});
				setSearchTime(data.took || 0);

				// Update URL
				const newUrl = `/search?${searchParams}`;
				window.history.pushState({}, '', newUrl);
			}
		}
		catch (error)
		{
			console.error('Search error:', error);
		}
		finally
		{
			setIsLoading(false);
		}
	}
};

interface SearchFormProps
{
	initialQuery: string;
	onSearch: (query: string) => Promise<void>;
	isLoading: boolean;
}

const SearchForm: React.FC<SearchFormProps> = ({ initialQuery, onSearch, isLoading }) =>
{
	const [query, setQuery] = useState(initialQuery);
	const [suggestions, setSuggestions] = useState<string[]>([]);
	const [showSuggestions, setShowSuggestions] = useState(false);

	const handleSubmit = async (e: React.FormEvent): Promise<void> =>
	{
		e.preventDefault();
		if (query.trim())
		{
			await onSearch(query.trim());
			setShowSuggestions(false);
		}
	};

	const handleInputChange = async (value: string): Promise<void> =>
	{
		setQuery(value);

		// Get search suggestions
		if (value.length >= 2)
		{
			try
			{
				const response = await fetch(`/api/domains/search/suggestions?q=${encodeURIComponent(value)}&limit=5`);
				if (response.ok)
				{
					const data = await response.json();
					setSuggestions(data.suggestions || []);
					setShowSuggestions(true);
				}
			}
			catch (error)
			{
				console.error('Failed to get suggestions:', error);
			}
		}
		else
		{
			setSuggestions([]);
			setShowSuggestions(false);
		}
	};

	const handleSuggestionClick = async (suggestion: string): Promise<void> =>
	{
		setQuery(suggestion);
		setShowSuggestions(false);
		await onSearch(suggestion);
	};

	return (
		<div className="search-form">
			<form onSubmit={handleSubmit}>
				<div className="field has-addons">
					<div className="control is-expanded">
						<input
							className="input"
							type="text"
							value={query}
							onChange={e => handleInputChange(e.target.value)}
							onFocus={() => { if (suggestions.length > 0) setShowSuggestions(true); }}
							onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
							placeholder="Search domains (e.g., example.com, technology, e-commerce)..."
						/>
						{showSuggestions && suggestions.length > 0 && (
							<div className="dropdown">
								{suggestions.map((suggestion, index) => (
									<div
										key={index}
										className="dropdown-item"
										onClick={() => handleSuggestionClick(suggestion)}
									>
										{suggestion}
									</div>
								))}
							</div>
						)}
					</div>
					<div className="control">
						<button
							type="submit"
							className="button is-primary"
							disabled={isLoading || !query.trim()}
						>
							{isLoading && <div className="loading loading--sm" />}
							{isLoading ? 'Searching...' : 'Search'}
						</button>
					</div>
				</div>
			</form>
		</div>
	);
};

interface SearchFiltersProps
{
	facets: SearchFacets;
	activeFilters: SearchFilters;
	onFilterChange: (filterKey: keyof SearchFilters, value: string | number | undefined) => Promise<void>;
	onClearFilters: () => Promise<void>;
}

const SearchFilters: React.FC<SearchFiltersProps> = ({
	facets,
	activeFilters,
	onFilterChange,
	onClearFilters,
}) =>
{
	const hasActiveFilters = Object.keys(activeFilters).length > 0;

	return (
		<div className="box card-pro sticky-top">
			<div className="is-flex is-justify-content-space-between is-align-items-center mb-4">
				<h3 className="title is-5 m-0">Filters</h3>
				{hasActiveFilters && (
					<button onClick={onClearFilters} className="button is-text is-small">Clear all</button>
				)}
			</div>

			{/* Sort Options */}
			<FilterSection title="Sort by">
				<div className="select is-fullwidth">
					<select
						value={activeFilters.sort || 'rank'}
						onChange={e => onFilterChange('sort', e.target.value)}
					>
						<option value="rank">Global Rank</option>
						<option value="score">Overall Score</option>
						<option value="performance">Performance</option>
						<option value="security">Security</option>
						<option value="seo">SEO Score</option>
						<option value="traffic">Traffic Estimate</option>
					</select>
				</div>
			</FilterSection>

			{/* Categories */}
			{facets.categories && facets.categories.length > 0 && (
				<FilterSection title="Categories">
					<div className="select is-fullwidth">
						<select
							value={activeFilters.category || ''}
							onChange={e => onFilterChange('category', e.target.value || undefined)}
						>
							<option value="">All Categories</option>
							{facets.categories.map((cat, index) => (
								<option key={index} value={cat.name}>
									{cat.name} ({cat.count})
								</option>
							))}
						</select>
					</div>
				</FilterSection>
			)}

			{/* Countries */}
			{facets.countries && facets.countries.length > 0 && (
				<FilterSection title="Countries">
					<div className="select is-fullwidth">
						<select
							value={activeFilters.country || ''}
							onChange={e => onFilterChange('country', e.target.value || undefined)}
						>
							<option value="">All Countries</option>
							{facets.countries.map((country, index) => (
								<option key={index} value={country.name}>
									{country.name} ({country.count})
								</option>
							))}
						</select>
					</div>
				</FilterSection>
			)}

			{/* Technologies */}
			{facets.technologies && facets.technologies.length > 0 && (
				<FilterSection title="Technologies">
					<div className="select is-fullwidth">
						<select
							value={activeFilters.technology || ''}
							onChange={e => onFilterChange('technology', e.target.value || undefined)}
						>
							<option value="">All Technologies</option>
							{facets.technologies.map((tech, index) => (
								<option key={index} value={tech.name}>
									{tech.name} ({tech.count})
								</option>
							))}
						</select>
					</div>
				</FilterSection>
			)}

			{/* SSL Grade */}
			{facets.sslGrades && facets.sslGrades.length > 0 && (
				<FilterSection title="SSL Grade">
					<div className="select is-fullwidth">
						<select
							value={activeFilters.sslGrade || ''}
							onChange={e => onFilterChange('sslGrade', e.target.value || undefined)}
						>
							<option value="">All SSL Grades</option>
							{facets.sslGrades.map((grade, index) => (
								<option key={index} value={grade.name}>
									{grade.name} ({grade.count})
								</option>
							))}
						</select>
					</div>
				</FilterSection>
			)}

			{/* Score Range */}
			<FilterSection title="Score Range">
				<div className="is-flex is-align-items-center gap-1">
					<input
						className="input"
						type="number"
						placeholder="Min"
						min="0"
						max="100"
						value={activeFilters.minScore ? Math.round(activeFilters.minScore * 100) : ''}
						onChange={e => onFilterChange('minScore', e.target.value ? parseFloat(e.target.value) / 100 : undefined)}
					/>
					<span className="has-text-grey">-</span>
					<input
						className="input"
						type="number"
						placeholder="Max"
						min="0"
						max="100"
						value={activeFilters.maxScore ? Math.round(activeFilters.maxScore * 100) : ''}
						onChange={e => onFilterChange('maxScore', e.target.value ? parseFloat(e.target.value) / 100 : undefined)}
					/>
				</div>
			</FilterSection>

			{/* Rank Range */}
			<FilterSection title="Rank Range">
				<div className="is-flex is-align-items-center gap-1">
					<input
						className="input"
						type="number"
						placeholder="Min"
						min="1"
						value={activeFilters.minRank || ''}
						onChange={e => onFilterChange('minRank', e.target.value ? parseInt(e.target.value, 10) : undefined)}
					/>
					<span className="has-text-grey">-</span>
					<input
						className="input"
						type="number"
						placeholder="Max"
						min="1"
						value={activeFilters.maxRank || ''}
						onChange={e => onFilterChange('maxRank', e.target.value ? parseInt(e.target.value, 10) : undefined)}
					/>
				</div>
			</FilterSection>
		</div>
	);
};

interface FilterSectionProps
{
	title: string;
	children: React.ReactNode;
}

const FilterSection: React.FC<FilterSectionProps> = ({ title, children }) => (
	<div className="mb-4">
		<h4 className="has-text-grey is-size-7 has-text-weight-semibold mb-2">
			{title}
		</h4>
		{children}
	</div>
);

interface SearchResultsProps
{
	results: DomainResult[];
	pagination: PaginationInfo;
	isLoading: boolean;
	onPageChange: (page: number) => Promise<void>;
	onSortChange: (sort: string) => Promise<void>;
	activeSort: string;
}

const SearchResults: React.FC<SearchResultsProps> = ({
	results,
	pagination,
	isLoading,
	onPageChange,
	onSortChange,
	activeSort,
}) =>
{
	if (isLoading)
	{
		return (
			<div className="box card-pro has-text-centered">
				<div className="loading loading--sm mb-3" />
				<h3>Searching domains...</h3>
				<p className="has-text-grey">Please wait while we analyze the results</p>
			</div>
		);
	}

	if (!results.length)
	{
		return (
			<div className="box card-pro has-text-centered">
				<div className="is-size-1 mb-3">🔍</div>
				<h3>No results found</h3>
				<p className="has-text-grey">Try adjusting your search terms or filters</p>
			</div>
		);
	}

	return (
		<div>
			{/* Results Header */}
			<div className="results-header">
				<div className="has-text-grey">
					Showing {((pagination.page - 1) * pagination.limit) + 1}-{Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total.toLocaleString()} results
				</div>

				<div className="is-flex is-align-items-center gap-1">
					<span className="has-text-grey is-size-7">Sort by:</span>
					<div className="select is-small">
						<select value={activeSort} onChange={e => onSortChange(e.target.value)}>
							<option value="rank">Global Rank</option>
							<option value="score">Overall Score</option>
							<option value="performance">Performance</option>
							<option value="security">Security</option>
							<option value="seo">SEO Score</option>
							<option value="traffic">Traffic Estimate</option>
						</select>
					</div>
				</div>
			</div>

			{/* Results List */}
			<div className="stack-lg">
				{results.map(result => (
					<SearchResultCard key={result.domain} result={result} />
				))}
			</div>

			{/* Pagination */}
			{pagination.totalPages > 1 && (
				<Pagination pagination={pagination} onPageChange={onPageChange} />
			)}
		</div>
	);
};

interface SearchResultCardProps
{
	result: DomainResult;
}

const SearchResultCard: React.FC<SearchResultCardProps> = ({ result }) =>
{
	const {
		domain,
		title,
		description,
		category,
		country,
		globalRank,
		categoryRank,
		overallScore,
		performanceScore,
		securityScore,
		seoScore,
		technicalScore,
		trafficEstimate,
		sslGrade,
		technologies,
		lastUpdated,
		screenshot,
		favicon,
	} = result;

	const formatTraffic = (traffic?: number): string =>
	{
		if (!traffic) return 'N/A';
		if (traffic >= 1000000) return `${(traffic / 1000000).toFixed(1)}M`;
		if (traffic >= 1000) return `${(traffic / 1000).toFixed(1)}K`;
		return traffic.toString();
	};


	function getScoreVariant(score: number): 'good' | 'ok' | 'warn' | 'bad'
	{
		if (score >= 0.8) return 'good';
		if (score >= 0.6) return 'ok';
		if (score >= 0.4) return 'warn';
		return 'bad';
	}

	type ScoreBadgeProps = { score?: number };
	const ScoreBadge: React.FC<ScoreBadgeProps> = ({ score }) =>
	{
		if (score === undefined || score === null) return <span className="has-text-grey-light">N/A</span>;
		const pct = Math.round(score * 100);
		const v = getScoreVariant(score);
		return <span className={`score-badge score-badge--${v}`}>{pct}%</span>;
	};

	const formatDate = (dateString?: string): string =>
	{
		if (!dateString) return 'N/A';
		return new Date(dateString).toLocaleDateString();
	};

	return (
		<div className="box card-pro hover-elevate result-card">
			<div className="is-flex gap-3 mb-3">
				{/* Domain Icon/Screenshot */}
				<div>
					{favicon ? (
						<img
							src={favicon}
							alt={`${domain} favicon`}
							className="domain-favicon"
							width={32}
							height={32}
							onError={(e) => { e.currentTarget.style.display = 'none'; }}
						/>
					) : (
						<div className="favicon-fallback">🌐</div>
					)}
				</div>

				{/* Main Content */}
				<div>
					<div className="is-flex is-justify-content-space-between is-align-items-flex-start mb-2">
						<h3 className="title is-5 m-0">
							<a href={`/domain/${domain}`} className="link-domain">
								{title || domain}
							</a>
						</h3>

						{/* Overall Score */}
						<div className="has-text-right">
							<ScoreBadge score={overallScore || 0} />
							<div className="is-size-7 has-text-grey">Overall Score</div>
						</div>
					</div>

					{/* Domain Info */}
					<div className="is-flex is-flex-wrap-wrap gap-3 mb-2 has-text-grey is-size-7">
						<span>{domain}</span>
						{category && <span>• {category}</span>}
						{country && <span>• {country}</span>}
						{globalRank && <span>• Rank #{globalRank.toLocaleString()}</span>}
						{sslGrade && <span>• SSL: {sslGrade}</span>}
					</div>

					{/* Description */}
					{description && (
						<p className="has-text-grey is-size-6 mb-3">
							{description.length > 200 ? `${description.substring(0, 200)}...` : description}
						</p>
					)}

					{/* Technologies */}
					{technologies && technologies.length > 0 && (
						<div className="mb-3">
							<div className="tags">
								{technologies.slice(0, 5).map((tech, index) => (
									<span key={index} className="tag is-light">{tech}</span>
								))}
								{technologies.length > 5 && (
									<span className="has-text-grey is-size-7">+{technologies.length - 5} more</span>
								)}
							</div>
						</div>
					)}

					{/* Metrics */}
					<div className="grid-auto-200 mb-3">
						<div className="is-flex is-justify-content-space-between is-align-items-center">
							<span className="has-text-grey is-size-7">Performance</span>
							<ScoreBadge score={performanceScore || 0} />
						</div>
						<div className="is-flex is-justify-content-space-between is-align-items-center">
							<span className="has-text-grey is-size-7">Security</span>
							<ScoreBadge score={securityScore || 0} />
						</div>
						<div className="is-flex is-justify-content-space-between is-align-items-center">
							<span className="has-text-grey is-size-7">SEO</span>
							<ScoreBadge score={seoScore || 0} />
						</div>
						<div className="is-flex is-justify-content-space-between is-align-items-center">
							<span className="has-text-grey is-size-7">Technical</span>
							<ScoreBadge score={technicalScore || 0} />
						</div>
					</div>

					{/* Additional Info */}
					<div className="divider-top pt-2 is-flex is-justify-content-space-between is-align-items-center is-size-7 has-text-grey">
						<div className="is-flex gap-4">
							{trafficEstimate && (
								<span>Traffic: ~{formatTraffic(trafficEstimate)}/month</span>
							)}
							{categoryRank && (
								<span>Category Rank: #{categoryRank}</span>
							)}
						</div>
						<div>
							Last updated: {formatDate(lastUpdated)}
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};



interface PaginationProps
{
	pagination: PaginationInfo;
	onPageChange: (page: number) => Promise<void>;
}

const Pagination: React.FC<PaginationProps> = ({ pagination, onPageChange }) =>
{
	const {
		page, totalPages, hasPrev, hasNext,
	} = pagination;

	const handlePageChange = async (newPage: number): Promise<void> =>
	{
		if (newPage >= 1 && newPage <= totalPages && newPage !== page)
		{
			await onPageChange(newPage);
		}
	};

	const getPageNumbers = (): number[] =>
	{
		const pages: number[] = [];
		const maxVisible = 7;

		if (totalPages <= maxVisible)
		{
			for (let i = 1; i <= totalPages; i++)
			{
				pages.push(i);
			}
		}
		else
			if (page <= 4)
			{
				for (let i = 1; i <= 5; i++) pages.push(i);
				pages.push(-1); // Ellipsis
				pages.push(totalPages);
			}
			else if (page >= totalPages - 3)
			{
				pages.push(1);
				pages.push(-1); // Ellipsis
				for (let i = totalPages - 4; i <= totalPages; i++) pages.push(i);
			}
			else
			{
				pages.push(1);
				pages.push(-1); // Ellipsis
				for (let i = page - 1; i <= page + 1; i++) pages.push(i);
				pages.push(-2); // Ellipsis
				pages.push(totalPages);
			}

		return pages;
	};

	return (
		<div className="pager box is-flex is-align-items-center is-justify-content-center gap-2 mt-5">
			{/* Previous Button */}
			<button className="button" onClick={() => handlePageChange(page - 1)} disabled={!hasPrev}>← Previous</button>

			{/* Page Numbers */}
			{getPageNumbers().map((pageNum, index) =>
			{
				if (pageNum === -1 || pageNum === -2)
				{
					return (
						<span key={index} className="has-text-grey px-2">...</span>
					);
				}

				const isActive = pageNum === page;
				return (
					<button key={pageNum} onClick={() => handlePageChange(pageNum)} className={`button ${isActive ? 'is-primary' : ''}`}>{pageNum}</button>
				);
			})}

			{/* Next Button */}
			<button className="button" onClick={() => handlePageChange(page + 1)} disabled={!hasNext}>Next →</button>

			{/* Page Info */}
			<div className="ml-3 pl-3 has-text-grey is-size-7">Page {page} of {totalPages}</div>
		</div>
	);
};

export default SearchPage;

interface SearchStatsProps
{
	query: string;
	totalResults: number;
	searchTime: number;
}

const SearchStats: React.FC<SearchStatsProps> = ({ query, totalResults, searchTime }) => (
	<div className="divider-top pt-2 has-text-grey is-size-7">
		<span>
			Found <strong>{totalResults.toLocaleString()}</strong> results for "{query}"
			{searchTime > 0 && <span> in {searchTime}ms</span>}
		</span>
	</div>
);
