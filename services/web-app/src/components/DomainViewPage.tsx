import React, { useState, useEffect } from 'react';

interface DomainViewPageProps {
	domain?: string;
	initialData?: {
		analysis?: DomainAnalysis;
		error?: string;
	};
}

interface DomainAnalysis {
	domain: string;
	globalRank?: number;
	categoryRank?: number;
	category?: string;
	country?: string;
	overallScore?: number;
	performanceScore?: number;
	securityScore?: number;
	seoScore?: number;
	technicalScore?: number;
	backlinkScore?: number;
	trafficEstimate?: number;
	domainAge?: number;
	sslGrade?: string;
	technologies?: string[];
	lastUpdated?: string;
	screenshot?: string;
	screenshots?: string[];
	favicon?: string;
	subdomains?: string[];
	socialLinks?: Record<string, string>;
	crawlStatus?: string;
	domainInfo?: {
		age?: number;
		registrar?: string;
		registrationDate?: string;
		expirationDate?: string;
		country?: string;
	};
	metrics?: {
		performance?: PerformanceMetrics;
		security?: SecurityMetrics;
		seo?: SEOMetrics;
		technical?: TechnicalMetrics;
		traffic?: TrafficMetrics;
	};
	rankingExplanation?: RankingExplanation;
	historicalData?: HistoricalData[];
}

interface PerformanceMetrics {
	loadTime?: number;
	firstContentfulPaint?: number;
	largestContentfulPaint?: number;
	cumulativeLayoutShift?: number;
	firstInputDelay?: number;
	speedIndex?: number;
	pageSize?: number;
	resourceCount?: number;
	score?: number;
}

interface SecurityMetrics {
	sslGrade?: string;
	sslIssuer?: string;
	sslExpiration?: string;
	securityHeaders?: {
		hsts?: boolean;
		csp?: boolean;
		xFrameOptions?: boolean;
		xContentTypeOptions?: boolean;
	};
	vulnerabilities?: string[];
	certificateInfo?: {
		issuer?: string;
		expiration?: string;
		daysUntilExpiry?: number;
	};
	score?: number;
}

interface SEOMetrics {
	title?: string;
	description?: string;
	keywords?: string[];
	structuredData?: boolean;
	sitemap?: boolean;
	robotsTxt?: boolean;
	hasRobotsTxt?: boolean;
	hasSitemap?: boolean;
	mobileOptimized?: boolean;
	pageSpeed?: number;
	metaKeywords?: string;
	score?: number;
}

interface TechnicalMetrics {
	httpVersion?: string;
	compression?: boolean;
	caching?: boolean;
	cdn?: string;
	cdnProvider?: string;
	serverLocation?: string;
	serverSoftware?: string;
	uptime?: number;
	responseTime?: number;
	pageSize?: number;
	resourceCount?: number;
	score?: number;
}

interface TrafficMetrics {
	estimatedVisits?: number;
	bounceRate?: number;
	avgSessionDuration?: number;
	pageViewsPerSession?: number;
}

interface RankingExplanation {
	factors?: Array<{
		name: string;
		score: number;
		weight: number;
		impact: 'positive' | 'negative' | 'neutral';
		description: string;
	}>;
	recommendations?: string[];
	competitorComparison?: Array<{
		domain: string;
		rank: number;
		score: number;
	}>;
}

interface HistoricalData {
	date: string;
	globalRank: number;
	overallScore: number;
	trafficEstimate?: number;
}

// Score calculation helpers
function getScoreVariant(score: number): 'excellent' | 'good' | 'ok' | 'warn' | 'bad' {
	if (score >= 0.9) return 'excellent';
	if (score >= 0.75) return 'good';
	if (score >= 0.6) return 'ok';
	if (score >= 0.4) return 'warn';
	return 'bad';
}

const ScoreBadge: React.FC<{ score?: number; size?: 'small' | 'normal' | 'large' }> = ({ score, size = 'normal' }) => {
	if (score === undefined || score === null) return <span className="has-text-grey-light">N/A</span>;
	const pct = Math.round(score * 100);
	const variant = getScoreVariant(score);
	const sizeClass = size === 'large' ? 'is-size-3' : size === 'small' ? 'is-size-7' : '';
	return (
		<span className={`score-badge score-badge--${variant} ${sizeClass}`}>
			{pct}%
		</span>
	);
};

const DomainViewPage: React.FC<DomainViewPageProps> = ({ domain: propDomain, initialData = {} }) => {
	const [analysis, setAnalysis] = useState<DomainAnalysis | null>(initialData.analysis || null);
	const [loading, setLoading] = useState(!initialData.analysis);
	const [error, setError] = useState<string | null>(initialData.error || null);
	const [activeTab, setActiveTab] = useState<'overview' | 'performance' | 'security' | 'technical'>('overview');

	useEffect(() => {
		if (propDomain && !initialData.analysis) {
			fetchDomainAnalysis(propDomain);
		}
	}, [propDomain]);

	const fetchDomainAnalysis = async (domain: string) => {
		setLoading(true);
		setError(null);
		try {
			const response = await fetch(`/api/domains/${encodeURIComponent(domain)}/analysis`);
			if (!response.ok) {
				throw new Error(`Failed to fetch domain analysis: ${response.statusText}`);
			}
			const data = await response.json();
			setAnalysis(data);
		} catch (err) {
			setError(err instanceof Error ? err.message : 'Failed to load domain analysis');
		} finally {
			setLoading(false);
		}
	};

	if (loading) {
		return (
			<div className="domain-view-page">
				<div className="container">
					<div className="box has-text-centered p-6">
						<div className="loader mb-4"></div>
						<h3 className="title is-5">Analyzing domain...</h3>
						<p className="has-text-grey">Gathering comprehensive metrics and insights</p>
					</div>
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="domain-view-page">
				<div className="container">
					<div className="box has-text-centered p-6">
						<div className="is-size-1 mb-3">⚠️</div>
						<h2 className="title is-4">Analysis Not Available</h2>
						<p className="has-text-danger mb-4">{error}</p>
						<button className="button is-primary" onClick={() => window.history.back()}>
							Go Back
						</button>
					</div>
				</div>
			</div>
		);
	}

	if (!analysis) {
		return (
			<div className="domain-view-page">
				<div className="container">
					<div className="box has-text-centered p-6">
						<div className="is-size-1 mb-3">🔍</div>
						<h2 className="title is-4">No Domain Data</h2>
						<p className="has-text-grey mb-4">Please provide a domain to analyze</p>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="domain-view-page">
			<div className="container">
				{/* Hero Header */}
				<DomainHero analysis={analysis} />

				{/* Quick Stats */}
				<QuickStats analysis={analysis} />

				{/* Navigation Tabs */}
				<div className="tabs is-boxed is-centered mb-5">
					<ul>
						<li className={activeTab === 'overview' ? 'is-active' : ''}>
							<a onClick={() => setActiveTab('overview')}>
								<span className="icon is-small">📊</span>
								<span>Overview</span>
							</a>
						</li>
						<li className={activeTab === 'performance' ? 'is-active' : ''}>
							<a onClick={() => setActiveTab('performance')}>
								<span className="icon is-small">⚡</span>
								<span>Performance</span>
							</a>
						</li>
						<li className={activeTab === 'security' ? 'is-active' : ''}>
							<a onClick={() => setActiveTab('security')}>
								<span className="icon is-small">🔒</span>
								<span>Security</span>
							</a>
						</li>
						<li className={activeTab === 'technical' ? 'is-active' : ''}>
							<a onClick={() => setActiveTab('technical')}>
								<span className="icon is-small">⚙️</span>
								<span>Technical</span>
							</a>
						</li>
					</ul>
				</div>

				{/* Tab Content */}
				<div className="tab-content">
					{activeTab === 'overview' && <OverviewTab analysis={analysis} />}
					{activeTab === 'performance' && <PerformanceTab analysis={analysis} />}
					{activeTab === 'security' && <SecurityTab analysis={analysis} />}
					{activeTab === 'technical' && <TechnicalTab analysis={analysis} />}
				</div>

				{/* Ranking Explanation */}
				{analysis.rankingExplanation && (
					<RankingExplanationSection explanation={analysis.rankingExplanation} />
				)}
			</div>
		</div>
	);
};

// Hero Section
const DomainHero: React.FC<{ analysis: DomainAnalysis }> = ({ analysis }) => {
	return (
		<div className="box card-pro mb-5">
			<div className="is-flex is-justify-content-space-between is-align-items-center">
				<div className="is-flex is-align-items-center gap-3">
					{analysis.favicon && (
						<img src={analysis.favicon} alt="" className="domain-favicon domain-favicon--lg" />
					)}
					<div>
						<h1 className="title is-3">{analysis.domain}</h1>
						<div className="has-text-grey is-size-7">
							{analysis.category && <span className="tag is-light mr-2">{analysis.category}</span>}
							{analysis.country && <span className="tag is-light mr-2">📍 {analysis.country}</span>}
							{analysis.crawlStatus && <span className="tag is-light">Status: {analysis.crawlStatus}</span>}
						</div>
					</div>
				</div>
				<div className="is-flex is-align-items-center gap-3">
					<div className="has-text-centered">
						<p className="heading">Overall Score</p>
						<ScoreBadge score={analysis.overallScore} size="large" />
					</div>
					<div className="has-text-centered">
						<p className="heading">Global Rank</p>
						<div className="rank-badge">#{analysis.globalRank?.toLocaleString() || 'N/A'}</div>
					</div>
				</div>
			</div>
			{analysis.technologies && analysis.technologies.length > 0 && (
				<div className="tags mt-3">
					{analysis.technologies.slice(0, 6).map(tech => (
						<span key={tech} className="tag is-light">{tech}</span>
					))}
					{analysis.technologies.length > 6 && (
						<span className="has-text-grey is-size-7">+{analysis.technologies.length - 6} more</span>
					)}
				</div>
			)}
			<div className="buttons mt-3">
				<a className="button is-primary" href={`https://${analysis.domain}`} target="_blank" rel="noopener noreferrer">Open Website</a>
			</div>
		</div>
	);
};

// Quick Stats Section
const QuickStats: React.FC<{ analysis: DomainAnalysis }> = ({ analysis }) => (
	<div className="grid-auto-250 mb-5">
		<div className="metric-card">
			<div className="is-size-4 mb-1">🏆</div>
			<div className="metric-card__value">{analysis.globalRank ? `#${analysis.globalRank.toLocaleString()}` : 'N/A'}</div>
			<div className="metric-card__label">Global Rank</div>
		</div>
		<div className="metric-card">
			<div className="is-size-4 mb-1">🏅</div>
			<div className="metric-card__value">{analysis.categoryRank ? `#${analysis.categoryRank.toLocaleString()}` : 'N/A'}</div>
			<div className="metric-card__label">Category Rank</div>
		</div>
		<div className="metric-card">
			<div className="is-size-4 mb-1">📈</div>
			<div className="metric-card__value">{formatTraffic(analysis.trafficEstimate || analysis.metrics?.traffic?.estimatedVisits)}</div>
			<div className="metric-card__label">Traffic</div>
		</div>
		<div className="metric-card">
			<div className="is-size-4 mb-1">📅</div>
			<div className="metric-card__value">{formatDomainAge(analysis.domainAge || analysis.domainInfo?.age)}</div>
			<div className="metric-card__label">Domain Age</div>
		</div>
		<div className="metric-card">
			<div className="is-size-4 mb-1">🔐</div>
			<div className="metric-card__value">{analysis.sslGrade || analysis.metrics?.security?.sslGrade || 'N/A'}</div>
			<div className="metric-card__label">SSL Grade</div>
		</div>
		<div className="metric-card">
			<div className="is-size-4 mb-1">🛠️</div>
			<div className="metric-card__value">{analysis.technologies?.length || 0}</div>
			<div className="metric-card__label">Technologies</div>
		</div>
	</div>
);

// Overview Tab
const OverviewTab: React.FC<{ analysis: DomainAnalysis }> = ({ analysis }) => (
	<div>
		{/* Domain Information */}
		{analysis.domainInfo && (
			<div className="box mb-5">
				<h3 className="title is-5 mb-4">📋 Domain Information</h3>
				<div className="columns">
					<div className="column">
						<p className="heading">Registrar</p>
						<p className="has-text-weight-semibold">{analysis.domainInfo.registrar || 'N/A'}</p>
					</div>
					<div className="column">
						<p className="heading">Registration Date</p>
						<p className="has-text-weight-semibold">
							{analysis.domainInfo.registrationDate
								? new Date(analysis.domainInfo.registrationDate).toLocaleDateString()
								: 'N/A'}
						</p>
					</div>
					<div className="column">
						<p className="heading">Expiration Date</p>
						<p className="has-text-weight-semibold">
							{analysis.domainInfo.expirationDate
								? new Date(analysis.domainInfo.expirationDate).toLocaleDateString()
								: 'N/A'}
						</p>
					</div>
					<div className="column">
						<p className="heading">Country</p>
						<p className="has-text-weight-semibold">{analysis.domainInfo.country || 'N/A'}</p>
					</div>
				</div>
			</div>
		)}

		{/* Traffic Analytics */}
		{analysis.metrics?.traffic && (
			<div className="box mb-5">
				<h3 className="title is-5 mb-4">📊 Traffic Analytics</h3>
				<div className="columns">
					<div className="column">
						<p className="heading">Estimated Visits</p>
						<p className="title is-4">{formatTraffic(analysis.metrics.traffic.estimatedVisits)}</p>
					</div>
					<div className="column">
						<p className="heading">Bounce Rate</p>
						<p className="title is-4">
							{analysis.metrics.traffic.bounceRate
								? `${Math.round(analysis.metrics.traffic.bounceRate * 100)}%`
								: 'N/A'}
						</p>
					</div>
					<div className="column">
						<p className="heading">Avg. Session Duration</p>
						<p className="title is-4">
							{formatDuration(analysis.metrics.traffic.avgSessionDuration)}
						</p>
					</div>
					<div className="column">
						<p className="heading">Pages/Session</p>
						<p className="title is-4">
							{analysis.metrics.traffic.pageViewsPerSession?.toFixed(1) || 'N/A'}
						</p>
					</div>
				</div>
			</div>
		)}

		{/* Technologies */}
		{analysis.technologies && analysis.technologies.length > 0 && (
			<div className="box mb-5">
				<h3 className="title is-5 mb-4">🛠️ Detected Technologies</h3>
				<div className="tags are-medium">
					{analysis.technologies.map(tech => (
						<span key={tech} className="tag is-info is-light">{tech}</span>
					))}
				</div>
			</div>
		)}

		{/* Screenshots */}
		{(analysis.screenshot || (analysis.screenshots && analysis.screenshots.length > 0)) && (
			<div className="box mb-5">
				<h3 className="title is-5 mb-4">📸 Screenshots</h3>
				<div className="columns is-multiline">
					{analysis.screenshot && (
						<div className="column is-half">
							<figure className="image">
								<img className="screenshot-img" src={analysis.screenshot} alt={`${analysis.domain} screenshot`} />
							</figure>
						</div>
					)}
					{analysis.screenshots?.map((screenshot, index) => (
						<div key={index} className="column is-half">
							<figure className="image">
								<img className="screenshot-img" src={screenshot} alt={`${analysis.domain} screenshot ${index + 1}`} />
							</figure>
						</div>
					))}
				</div>
			</div>
		)}

		{/* Subdomains */}
		{analysis.subdomains && analysis.subdomains.length > 0 && (
			<div className="box mb-5">
				<h3 className="title is-5 mb-4">🌐 Subdomains ({analysis.subdomains.length})</h3>
				<div className="tags">
					{analysis.subdomains.slice(0, 20).map(subdomain => (
						<a
							key={subdomain}
							href={`https://${subdomain}`}
							target="_blank"
							rel="noopener noreferrer"
							className="tag is-light"
						>
							{subdomain}
						</a>
					))}
					{analysis.subdomains.length > 20 && (
						<span className="tag is-light has-text-grey">+{analysis.subdomains.length - 20} more</span>
					)}
				</div>
			</div>
		)}

		{/* Social Links */}
		{analysis.socialLinks && Object.keys(analysis.socialLinks).length > 0 && (
			<div className="box mb-5">
				<h3 className="title is-5 mb-4">🔗 Social Media Presence</h3>
				<div className="buttons">
					{Object.entries(analysis.socialLinks).map(([platform, url]) => (
						<a
							key={platform}
							href={url}
							target="_blank"
							rel="noopener noreferrer"
							className="button is-info is-light"
						>
							<span className="is-capitalized">{platform}</span>
						</a>
					))}
				</div>
			</div>
		)}
	</div>
);

// Performance Tab
const PerformanceTab: React.FC<{ analysis: DomainAnalysis }> = ({ analysis }) => (
	<div>
		<div className="box mb-5">
			<h3 className="title is-5 mb-4">⚡ Performance Score</h3>
			<div className="has-text-centered mb-4">
				<ScoreBadge score={analysis.performanceScore || analysis.metrics?.performance?.score} size="large" />
			</div>

			{analysis.metrics?.performance && (
				<div className="columns is-multiline">
					<div className="column is-half">
						<div className="box is-shadowless has-background-light">
							<p className="heading">Load Time</p>
							<p className="title is-5">
								{analysis.metrics.performance.loadTime
									? `${analysis.metrics.performance.loadTime}ms`
									: 'N/A'}
							</p>
						</div>
					</div>
					<div className="column is-half">
						<div className="box is-shadowless has-background-light">
							<p className="heading">First Contentful Paint</p>
							<p className="title is-5">
								{analysis.metrics.performance.firstContentfulPaint
									? `${analysis.metrics.performance.firstContentfulPaint}ms`
									: 'N/A'}
							</p>
						</div>
					</div>
					<div className="column is-half">
						<div className="box is-shadowless has-background-light">
							<p className="heading">Largest Contentful Paint</p>
							<p className="title is-5">
								{analysis.metrics.performance.largestContentfulPaint
									? `${analysis.metrics.performance.largestContentfulPaint}ms`
									: 'N/A'}
							</p>
						</div>
					</div>
					<div className="column is-half">
						<div className="box is-shadowless has-background-light">
							<p className="heading">Cumulative Layout Shift</p>
							<p className="title is-5">
								{analysis.metrics.performance.cumulativeLayoutShift?.toFixed(3) || 'N/A'}
							</p>
						</div>
					</div>
					<div className="column is-half">
						<div className="box is-shadowless has-background-light">
							<p className="heading">Page Size</p>
							<p className="title is-5">
								{analysis.metrics.performance.pageSize
									? formatBytes(analysis.metrics.performance.pageSize)
									: 'N/A'}
							</p>
						</div>
					</div>
					<div className="column is-half">
						<div className="box is-shadowless has-background-light">
							<p className="heading">Resource Count</p>
							<p className="title is-5">
								{analysis.metrics.performance.resourceCount || 'N/A'}
							</p>
						</div>
					</div>
				</div>
			)}
		</div>
	</div>
);

// Security Tab
const SecurityTab: React.FC<{ analysis: DomainAnalysis }> = ({ analysis }) => (
	<div>
		<div className="box mb-5">
			<h3 className="title is-5 mb-4">🔒 Security Score</h3>
			<div className="has-text-centered mb-4">
				<ScoreBadge score={analysis.securityScore || analysis.metrics?.security?.score} size="large" />
			</div>

			{analysis.metrics?.security && (
				<>
					<div className="columns mb-4">
						<div className="column">
							<div className="box is-shadowless has-background-light">
								<p className="heading">SSL Grade</p>
								<p className="title is-4">{analysis.metrics.security.sslGrade || 'N/A'}</p>
							</div>
						</div>
						<div className="column">
							<div className="box is-shadowless has-background-light">
								<p className="heading">SSL Issuer</p>
								<p className="title is-5">{analysis.metrics.security.sslIssuer || 'N/A'}</p>
							</div>
						</div>
						<div className="column">
							<div className="box is-shadowless has-background-light">
								<p className="heading">SSL Expiration</p>
								<p className="title is-5">
									{analysis.metrics.security.sslExpiration
										? new Date(analysis.metrics.security.sslExpiration).toLocaleDateString()
										: 'N/A'}
								</p>
							</div>
						</div>
					</div>

					{analysis.metrics.security.securityHeaders && (
						<div className="mb-4">
							<h4 className="title is-6">Security Headers</h4>
							<div className="columns">
								<div className="column">
									<span className={`tag is-medium ${analysis.metrics.security.securityHeaders.hsts ? 'is-success' : 'is-danger'} is-light`}>
										{analysis.metrics.security.securityHeaders.hsts ? '✓' : '✗'} HSTS
									</span>
								</div>
								<div className="column">
									<span className={`tag is-medium ${analysis.metrics.security.securityHeaders.csp ? 'is-success' : 'is-danger'} is-light`}>
										{analysis.metrics.security.securityHeaders.csp ? '✓' : '✗'} CSP
									</span>
								</div>
								<div className="column">
									<span className={`tag is-medium ${analysis.metrics.security.securityHeaders.xFrameOptions ? 'is-success' : 'is-danger'} is-light`}>
										{analysis.metrics.security.securityHeaders.xFrameOptions ? '✓' : '✗'} X-Frame-Options
									</span>
								</div>
								<div className="column">
									<span className={`tag is-medium ${analysis.metrics.security.securityHeaders.xContentTypeOptions ? 'is-success' : 'is-danger'} is-light`}>
										{analysis.metrics.security.securityHeaders.xContentTypeOptions ? '✓' : '✗'} X-Content-Type-Options
									</span>
								</div>
							</div>
						</div>
					)}

					{analysis.metrics.security.vulnerabilities && analysis.metrics.security.vulnerabilities.length > 0 && (
						<div>
							<h4 className="title is-6">⚠️ Vulnerabilities Detected</h4>
							<div className="tags">
								{analysis.metrics.security.vulnerabilities.map(vuln => (
									<span key={vuln} className="tag is-warning">{vuln}</span>
								))}
							</div>
						</div>
					)}
				</>
			)}
		</div>
	</div>
);

// Technical Tab
const TechnicalTab: React.FC<{ analysis: DomainAnalysis }> = ({ analysis }) => (
	<div>
		<div className="box mb-5">
			<h3 className="title is-5 mb-4">⚙️ Technical Score</h3>
			<div className="has-text-centered mb-4">
				<ScoreBadge score={analysis.technicalScore || analysis.metrics?.technical?.score} size="large" />
			</div>

			{analysis.metrics?.technical && (
				<div className="columns is-multiline">
					<div className="column is-half">
						<div className="box is-shadowless has-background-light">
							<p className="heading">HTTP Version</p>
							<p className="title is-5">{analysis.metrics.technical.httpVersion || 'N/A'}</p>
						</div>
					</div>
					<div className="column is-half">
						<div className="box is-shadowless has-background-light">
							<p className="heading">Server Software</p>
							<p className="title is-5">{analysis.metrics.technical.serverSoftware || 'N/A'}</p>
						</div>
					</div>
					<div className="column is-half">
						<div className="box is-shadowless has-background-light">
							<p className="heading">CDN Provider</p>
							<p className="title is-5">{analysis.metrics.technical.cdnProvider || 'None'}</p>
						</div>
					</div>
					<div className="column is-half">
						<div className="box is-shadowless has-background-light">
							<p className="heading">Compression</p>
							<p className="title is-5">
								<span className={`tag is-medium ${analysis.metrics.technical.compression ? 'is-success' : 'is-danger'} is-light`}>
									{analysis.metrics.technical.compression ? 'Enabled' : 'Disabled'}
								</span>
							</p>
						</div>
					</div>
					<div className="column is-half">
						<div className="box is-shadowless has-background-light">
							<p className="heading">Page Size</p>
							<p className="title is-5">
								{analysis.metrics.technical.pageSize
									? formatBytes(analysis.metrics.technical.pageSize)
									: 'N/A'}
							</p>
						</div>
					</div>
					<div className="column is-half">
						<div className="box is-shadowless has-background-light">
							<p className="heading">Resource Count</p>
							<p className="title is-5">{analysis.metrics.technical.resourceCount || 'N/A'}</p>
						</div>
					</div>
				</div>
			)}

			{/* SEO Metrics */}
			{analysis.metrics?.seo && (
				<div className="box mb-5">
					<h3 className="title is-5 mb-4">🔍 SEO Analysis</h3>
					<div className="has-text-centered mb-4">
						<ScoreBadge score={analysis.seoScore || analysis.metrics.seo.score} size="large" />
					</div>
					<div className="content">
						{analysis.metrics.seo.title && (
							<div className="mb-3">
								<p className="heading">Page Title</p>
								<p className="has-text-weight-medium">{analysis.metrics.seo.title}</p>
							</div>
						)}
						{analysis.metrics.seo.description && (
							<div className="mb-3">
								<p className="heading">Meta Description</p>
								<p className="has-text-weight-medium">{analysis.metrics.seo.description}</p>
							</div>
						)}
						<div className="columns">
							<div className="column">
								<span className={`tag is-medium ${analysis.metrics.seo.hasRobotsTxt || analysis.metrics.seo.robotsTxt ? 'is-success' : 'is-danger'} is-light`}>
									{analysis.metrics.seo.hasRobotsTxt || analysis.metrics.seo.robotsTxt ? '✓' : '✗'} Robots.txt
								</span>
							</div>
							<div className="column">
								<span className={`tag is-medium ${analysis.metrics.seo.hasSitemap || analysis.metrics.seo.sitemap ? 'is-success' : 'is-danger'} is-light`}>
									{analysis.metrics.seo.hasSitemap || analysis.metrics.seo.sitemap ? '✓' : '✗'} Sitemap
								</span>
							</div>
							<div className="column">
								<span className={`tag is-medium ${analysis.metrics.seo.structuredData ? 'is-success' : 'is-danger'} is-light`}>
									{analysis.metrics.seo.structuredData ? '✓' : '✗'} Structured Data
								</span>
							</div>
							<div className="column">
								<span className={`tag is-medium ${analysis.metrics.seo.mobileOptimized ? 'is-success' : 'is-danger'} is-light`}>
									{analysis.metrics.seo.mobileOptimized ? '✓' : '✗'} Mobile Optimized
								</span>
							</div>
						</div>
					</div>
				</div>
			)}
		</div>
	</div>
);

// Ranking Explanation Section
const RankingExplanationSection: React.FC<{ explanation: RankingExplanation }> = ({ explanation }) => (
	<div className="box">
		<h3 className="title is-5 mb-4">📊 Ranking Analysis</h3>

		{explanation.factors && explanation.factors.length > 0 && (
			<div className="mb-5">
				<h4 className="title is-6">Ranking Factors</h4>
				{explanation.factors.map(factor => (
					<div key={factor.name} className="box is-shadowless has-background-light mb-3">
						<div className="level">
							<div className="level-left">
								<div>
									<p className="has-text-weight-semibold">{factor.name}</p>
									<p className="is-size-7 has-text-grey">{factor.description}</p>
								</div>
							</div>
							<div className="level-right">
								<div className="has-text-right">
									<ScoreBadge score={factor.score} />
									<p className="is-size-7 has-text-grey mt-1">Weight: {Math.round(factor.weight * 100)}%</p>
								</div>
							</div>
						</div>
					</div>
				))}
			</div>
		)}

		{explanation.recommendations && explanation.recommendations.length > 0 && (
			<div className="mb-5">
				<h4 className="title is-6">💡 Recommendations</h4>
				<div className="content">
					<ul>
						{explanation.recommendations.map((rec, index) => (
							<li key={index}>{rec}</li>
						))}
					</ul>
				</div>
			</div>
		)}

		{explanation.competitorComparison && explanation.competitorComparison.length > 0 && (
			<div>
				<h4 className="title is-6">🏁 Competitor Comparison</h4>
				<div className="columns is-multiline">
					{explanation.competitorComparison.map(competitor => (
						<div key={competitor.domain} className="column is-one-third">
							<div className="box has-text-centered">
								<p className="has-text-weight-semibold">{competitor.domain}</p>
								<p className="is-size-7 has-text-grey">
									Rank #{competitor.rank} • Score: {Math.round(competitor.score * 100)}%
								</p>
							</div>
						</div>
					))}
				</div>
			</div>
		)}
	</div>
);

// Helper functions
function formatTraffic(traffic?: number): string {
	if (!traffic) return 'N/A';
	if (traffic >= 1000000) return `${(traffic / 1000000).toFixed(1)}M`;
	if (traffic >= 1000) return `${(traffic / 1000).toFixed(1)}K`;
	return traffic.toString();
}

function formatDomainAge(days?: number): string {
	if (!days) return 'N/A';
	if (days >= 365) return `${Math.floor(days / 365)}y`;
	if (days >= 30) return `${Math.floor(days / 30)}m`;
	return `${days}d`;
}

function formatDuration(seconds?: number): string {
	if (!seconds) return 'N/A';
	if (seconds >= 60) return `${Math.floor(seconds / 60)}m ${Math.floor(seconds % 60)}s`;
	return `${Math.floor(seconds)}s`;
}

function formatBytes(bytes: number): string {
	if (bytes === 0) return '0 B';
	const k = 1024;
	const sizes = ['B', 'KB', 'MB', 'GB'];
	const i = Math.floor(Math.log(bytes) / Math.log(k));
	return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
}

export default DomainViewPage;