import React, { useState } from 'react';

type HomePageProps =
{
	initialData?:
	{
		trending?:
		{
			domains?: Array<{
				domain: string;
				currentRank?: number;
				previousRank?: number;
				rankChange?: number;
				trendDirection?: string;
				overallScore?: number;
			}>;
			timeframe?: string;
		} | null;
		[key: string]: unknown;
	};
};

type FeatureCardProps =
{
	title: string;
	description: string;
	icon: string;
}

const FeatureCard: React.FC<FeatureCardProps> = ({ title, description, icon }) => (
	<div className="box card-pro hover-elevate has-text-centered">
		<div className="is-size-1 mb-3">{icon}</div>
		<h3 className="title is-4 mb-2">{title}</h3>
		<p className="has-text-grey">{description}</p>
	</div>
);

const StatCard: React.FC<{ title: string; value: string }> = ({ title, value }) => (
	<div className="box card-pro has-text-centered">
		<div className="is-size-2 has-text-weight-bold has-text-primary mb-2">{value}</div>
		<div className="has-text-grey">{title}</div>
	</div>
);

const CtaButtons: React.FC = () =>
{
		return (
				<div className="buttons is-centered mt-2">
						<a className="button is-primary" href="/top-domains">Explore Top Domains</a>
				</div>
		);
};

const CategoryChips: React.FC = () =>
{
		const cats = ['technology', 'news', 'finance', 'education', 'gaming', 'health'];
		return (
				<div className="has-text-centered" style={{ marginTop: 8 }}>
						<span className="has-text-grey is-size-7">Popular: </span>
						<div className="buttons is-centered is-small" style={{ marginTop: 8 }}>
								{cats.map(c => (
										<a key={c} className="button is-light is-small" href={`/search?q=${encodeURIComponent(c)}`}>{c}</a>
								))}
						</div>
				</div>
		);
};

const HowItWorksSection: React.FC = () => (
	<section id="how-it-works" className="mt-6">
		<h2 className="title is-2 has-text-centered mb-6 section-title">How it works</h2>
		<div className="grid-auto-250">
			<div className="box card-pro hover-elevate has-text-centered">
				<div className="is-size-1 mb-3">🔎</div>
				<h3 className="title is-5 mb-2">Search a domain</h3>
				<p className="has-text-grey">Start with a name or keyword to find domains of interest.</p>
			</div>
			<div className="box card-pro hover-elevate has-text-centered">
				<div className="is-size-1 mb-3">📊</div>
				<h3 className="title is-5 mb-2">View deep analysis</h3>
				<p className="has-text-grey">See performance, security, SEO and tech stack at a glance.</p>
			</div>
			<div className="box card-pro hover-elevate has-text-centered">
				<div className="is-size-1 mb-3">⚖️</div>
				<h3 className="title is-5 mb-2">Explore top lists</h3>
				<p className="has-text-grey">Browse top-ranked domains by category and country.</p>
			</div>
		</div>
	</section>
);


const SearchBox: React.FC = () =>
{
		const [value, setValue] = useState('');

		const go = (): void =>
		{
				const v = value.trim();
				if (!v) return;
				const normalize = (d: string): string => d.toLowerCase().replace(/^https?:\/\//, '').replace(/^www\./, '').replace(/\/$/, '');
				const isDomain = (s: string): boolean => /^(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)+[a-z]{2,}$/i.test(normalize(s));
				if (isDomain(v))
				{
						window.location.href = `/domain/${encodeURIComponent(normalize(v))}`;
						return;
				}
				window.location.href = `/search?q=${encodeURIComponent(v)}`;
		};

		return (
				<div className="container is-max-desktop">
						<div className="field has-addons is-align-items-center">
								<div className="control is-expanded">
										<input
												className="input"
												type="text"
												placeholder="Enter domain (e.g., example.com)"
												value={value}
												onChange={(e) => setValue(e.target.value)}
												onKeyDown={(e) => { if (e.key === 'Enter') go(); }}
										/>
								</div>
								<div className="control">
										<button
												className={`button is-primary`}
												type="button"
												onClick={go}
												disabled={!value.trim()}
										>
												Get Details
										</button>
								</div>
						</div>
						<p className="help has-text-grey">
								Tip: paste a domain and press Enter to jump to results
						</p>
				</div>
		);
};

type TrendingSectionProps = {
		items: Array<{
				domain: string;
				currentRank?: number;
				previousRank?: number;
				rankChange?: number;
				trendDirection?: string;
				overallScore?: number;
		}>;
		timeframe?: string;
};

const TrendingSection: React.FC<TrendingSectionProps> = ({ items, timeframe }) =>
{
		if (!items || items.length === 0)
		{
				return null;
		}

		const arrowFor = (dir?: string): string =>
		{
				if (dir === 'up') return '⬆️';
				if (dir === 'down') return '⬇️';
				return '↔️';
		};

		return (
				<section className="mt-6">
						<h2 className="title is-2 has-text-centered mb-6 section-title">Trending Domains{timeframe ? ` (${timeframe})` : ''}</h2>
						<div className="grid-auto-250">
								{items.map((d) => (
										<a key={d.domain} className="box card-pro hover-elevate is-block" href={`/domain/${encodeURIComponent(d.domain)}`}>
												<div className="is-flex is-justify-content-space-between is-align-items-center">
														<div>
																<div className="title is-5 m-0">{d.domain}</div>
																<div className="has-text-grey is-size-7">
																		Rank: {d.currentRank ?? '—'} {arrowFor(d.trendDirection)} {typeof d.rankChange === 'number' ? `${d.rankChange > 0 ? '+' : ''}${d.rankChange}` : ''}
																</div>
														</div>
														<div className="tag is-light">
																Score: {typeof d.overallScore === 'number' ? Math.round(d.overallScore * 100) : '—'}
														</div>
												</div>
										</a>
								))}
						</div>
				</section>
		);
};

const HomePage: React.FC<HomePageProps> = ({ initialData }) => (
		<div className="home-page">
				<div className="box page-hero has-text-centered mb-5">
						<h1 className="title is-1 mb-3 hero-title title-gradient">Domain Ranking System</h1>
						<p className="subtitle is-5 has-text-grey mb-5">Get domain details, rankings, and insights instantly</p>
						<div className="mb-5">
								<SearchBox />
						</div>
						<CtaButtons />
						<CategoryChips />
				</div>

				<TrendingSection items={(initialData?.trending as any)?.domains || []} timeframe={(initialData?.trending as any)?.timeframe} />

				<div className="features-section">
						<h2 className="title is-2 has-text-centered mb-6 section-title">Features</h2>
						<div className="features-grid">
								<FeatureCard
										title="Domain Analysis"
										description="Comprehensive analysis of website performance, security, and SEO metrics"
										icon="🔍"
								/>
								<FeatureCard
										title="Global Rankings"
										description="See how domains rank globally and within specific categories"
										icon="🏆"
								/>
								<FeatureCard
										title="Performance Metrics"
										description="Core Web Vitals, load times, and technical performance analysis"
										icon="⚡"
								/>
								<FeatureCard
										title="Security Analysis"
										description="SSL certificates, security headers, and vulnerability assessment"
										icon="🔒"
								/>
						</div>
				</div>

				<div className="stats-section mt-6">
						<h2 className="title is-2 has-text-centered mb-6 section-title">Platform Statistics</h2>
						<div className="stats-grid has-text-centered">
								<StatCard title="Domains Analyzed" value="1,000,000+" />
								<StatCard title="Daily Scans" value="50,000+" />
								<StatCard title="Categories" value="100+" />
								<StatCard title="Countries" value="200+" />
						</div>
				</div>

				<HowItWorksSection />
		</div>
);

export default HomePage;
