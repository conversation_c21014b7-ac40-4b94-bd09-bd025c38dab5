import React from 'react';
import type { DomainResult } from './types';

interface DomainCardProps
{
    domain: DomainResult;
    onClick?: () => void;
}

function formatNumber(num?: number): string
{
    if (!num) return '-';
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
}

function getScoreVariant(score: number): 'good' | 'ok' | 'warn' | 'bad'
{
    if (score >= 80) return 'good';
    if (score >= 60) return 'ok';
    if (score >= 40) return 'warn';
    return 'bad';
}

const MetricPill: React.FC<{ label: string; score?: number }> = ({ label, score }) =>
{
    if (score == null) return null;
    const variant = getScoreVariant(score);
    return (
        <div className={`metric-card metric-card--${variant}`}>
            <div className="metric-card__value">{Math.round(score)}%</div>
            <div className="metric-card__label">{label}</div>
        </div>
    );
};

export const DomainCard: React.FC<DomainCardProps> = ({ domain, onClick }) =>
{
    return (
        <div
            className="box card-pro hover-elevate"
            onClick={onClick}
            role={onClick ? 'button' : undefined}
            tabIndex={onClick ? 0 : undefined}
        >
            {/* Header */}
            <div className="is-flex is-justify-content-space-between is-align-items-flex-start mb-3">
                <div className="is-flex is-align-items-center gap-3">
                    {domain.favicon && (
                        <img
                            src={domain.favicon}
                            alt=""
                            className="domain-favicon domain-favicon--lg"
                            onError={(e) =>
                            {
                                (e.target as HTMLImageElement).style.display = 'none';
                            }}
                        />
                    )}
                    <div>
                        <h3 className="title is-5 mb-1">
                            <span className="has-text-weight-semibold link-domain">{domain.title || domain.domain}</span>
                        </h3>
                        <div className="has-text-grey is-size-7">
                            {domain.domain}
                            {domain.category && <span> • {domain.category}</span>}
                            {domain.country && <span> • {domain.country}</span>}
                        </div>
                    </div>
                </div>
                {domain.globalRank && (
                    <div className="rank-badge">#{formatNumber(domain.globalRank)}</div>
                )}
            </div>

            {/* Description */}
            {domain.description && (
                <p className="has-text-grey is-size-7 mb-3 line-clamp-2">{domain.description}</p>
            )}

            {/* Technologies */}
            {domain.technologies && domain.technologies.length > 0 && (
                <div className="mb-3">
                    <div className="tags">
                        {domain.technologies.slice(0, 4).map(tech => (
                            <span key={tech} className="tag is-light">{tech}</span>
                        ))}
                        {domain.technologies.length > 4 && (
                            <span className="has-text-grey is-size-7">+{domain.technologies.length - 4} more</span>
                        )}
                    </div>
                </div>
            )}

            {/* Score Pills */}
            <div className="grid-2 mb-3">
                <MetricPill label="Overall" score={domain.overallScore} />
                <MetricPill label="Performance" score={domain.performanceScore} />
                <MetricPill label="Security" score={domain.securityScore} />
                <MetricPill label="SEO" score={domain.seoScore} />
            </div>

            {/* Footer */}
            <div className="is-flex is-justify-content-space-between is-align-items-center is-size-7 has-text-grey divider-top pt-3">
                <div>Traffic: {formatNumber(domain.trafficEstimate)}</div>
                {domain.sslGrade && (
                    <span className="tag is-light">SSL: {domain.sslGrade}</span>
                )}
                {domain.lastUpdated && (
                    <div>{new Date(domain.lastUpdated).toLocaleDateString()}</div>
                )}
            </div>
        </div>
    );
};

export default DomainCard;
