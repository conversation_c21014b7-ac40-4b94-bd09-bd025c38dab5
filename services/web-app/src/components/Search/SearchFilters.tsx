import React, { useCallback } from 'react';
import type { SearchFilters as Filters, SearchFacets } from './types';

interface SearchFiltersProps {
	filters: Filters;
	facets: SearchFacets;
	onFilterChange: (filters: Filters) => void;
}

export const SearchFilters: React.FC<SearchFiltersProps> = ({
	filters,
	facets,
	onFilterChange
}) => 
{
	const handleFilterChange = useCallback((key: keyof Filters, value: any) => 
	{
		onFilterChange({
			...filters,
			[key]: value === filters[key] ? undefined : value
		});
	}, [filters, onFilterChange]);

	const handleRangeChange = useCallback((
		minKey: keyof Filters, 
		maxKey: keyof Filters, 
		min: number | undefined, 
		max: number | undefined
	) => 
	{
		onFilterChange({
			...filters,
			[minKey]: min,
			[maxKey]: max
		});
	}, [filters, onFilterChange]);

	const clearFilters = useCallback(() => 
	{
		onFilterChange({});
	}, [onFilterChange]);

	const hasActiveFilters = Object.values(filters).some(v => v !== undefined);

	return (
		<div className="search-filters">
			<div className="filters-header">
				<h3>Filters</h3>
				{hasActiveFilters && (
					<button 
						onClick={clearFilters}
						className="clear-filters-btn"
					>
						Clear all
					</button>
				)}
			</div>

			{/* Categories */}
			{facets.categories && facets.categories.length > 0 && (
				<div className="filter-group">
					<h4>Category</h4>
					<div className="filter-options">
						{facets.categories.map(({ name, count }) => (
							<label key={name} className="filter-option">
								<input
									type="radio"
									name="category"
									checked={filters.category === name}
									onChange={() => handleFilterChange('category', name)}
								/>
								<span className="filter-label">
									{name} 
									<span className="filter-count">({count})</span>
								</span>
							</label>
						))}
					</div>
				</div>
			)}

			{/* Countries */}
			{facets.countries && facets.countries.length > 0 && (
				<div className="filter-group">
					<h4>Country</h4>
					<div className="filter-options">
						{facets.countries.slice(0, 10).map(({ name, count }) => (
							<label key={name} className="filter-option">
								<input
									type="radio"
									name="country"
									checked={filters.country === name}
									onChange={() => handleFilterChange('country', name)}
								/>
								<span className="filter-label">
									{name}
									<span className="filter-count">({count})</span>
								</span>
							</label>
						))}
					</div>
				</div>
			)}

			{/* Technologies */}
			{facets.technologies && facets.technologies.length > 0 && (
				<div className="filter-group">
					<h4>Technology</h4>
					<div className="filter-options scrollable">
						{facets.technologies.slice(0, 20).map(({ name, count }) => (
							<label key={name} className="filter-option">
								<input
									type="checkbox"
									checked={filters.technology === name}
									onChange={() => handleFilterChange('technology', name)}
								/>
								<span className="filter-label">
									{name}
									<span className="filter-count">({count})</span>
								</span>
							</label>
						))}
					</div>
				</div>
			)}

			{/* SSL Grade */}
			{facets.sslGrades && facets.sslGrades.length > 0 && (
				<div className="filter-group">
					<h4>SSL Grade</h4>
					<div className="filter-options inline">
						{facets.sslGrades.map(({ name, count }) => (
							<button
								key={name}
								className={`filter-badge ${filters.sslGrade === name ? 'active' : ''}`}
								onClick={() => handleFilterChange('sslGrade', name)}
							>
								{name}
								<span className="badge-count">{count}</span>
							</button>
						))}
					</div>
				</div>
			)}

			{/* Rank Range */}
			<div className="filter-group">
				<h4>Global Rank</h4>
				<div className="range-inputs">
					<input
						type="number"
						placeholder="Min"
						value={filters.minRank || ''}
						onChange={e => handleRangeChange(
							'minRank', 
							'maxRank',
							e.target.value ? parseInt(e.target.value, 10) : undefined,
							filters.maxRank
						)}
						className="range-input"
					/>
					<span className="range-separator">-</span>
					<input
						type="number"
						placeholder="Max"
						value={filters.maxRank || ''}
						onChange={e => handleRangeChange(
							'minRank',
							'maxRank',
							filters.minRank,
							e.target.value ? parseInt(e.target.value, 10) : undefined
						)}
						className="range-input"
					/>
				</div>
			</div>

			{/* Score Range */}
			<div className="filter-group">
				<h4>Overall Score</h4>
				<div className="range-inputs">
					<input
						type="number"
						placeholder="Min"
						min="0"
						max="100"
						value={filters.minScore || ''}
						onChange={e => handleRangeChange(
							'minScore',
							'maxScore',
							e.target.value ? parseInt(e.target.value, 10) : undefined,
							filters.maxScore
						)}
						className="range-input"
					/>
					<span className="range-separator">-</span>
					<input
						type="number"
						placeholder="Max"
						min="0"
						max="100"
						value={filters.maxScore || ''}
						onChange={e => handleRangeChange(
							'minScore',
							'maxScore',
							filters.minScore,
							e.target.value ? parseInt(e.target.value, 10) : undefined
						)}
						className="range-input"
					/>
				</div>
			</div>

			{/* Sort Options */}
			<div className="filter-group">
				<h4>Sort By</h4>
				<select 
					value={filters.sort || 'relevance'}
					onChange={e => handleFilterChange('sort', e.target.value)}
					className="sort-select"
				>
					<option value="relevance">Relevance</option>
					<option value="rank_asc">Rank (Low to High)</option>
					<option value="rank_desc">Rank (High to Low)</option>
					<option value="score_desc">Score (High to Low)</option>
					<option value="score_asc">Score (Low to High)</option>
					<option value="traffic_desc">Traffic (High to Low)</option>
					<option value="domain_asc">Domain (A-Z)</option>
					<option value="domain_desc">Domain (Z-A)</option>
					<option value="updated_desc">Recently Updated</option>
				</select>
			</div>
		</div>
	);
};

export default SearchFilters;
