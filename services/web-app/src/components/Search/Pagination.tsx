import React, { useCallback, useMemo } from 'react';
import type { PaginationInfo } from './types';

interface PaginationProps {
	pagination: PaginationInfo;
	onPageChange: (page: number) => void;
}

export const Pagination: React.FC<PaginationProps> = ({
	pagination,
	onPageChange
}) => 
{
	const { page, totalPages, hasNext, hasPrev } = pagination;

	const pageNumbers = useMemo(() => 
	{
		const pages: (number | string)[] = [];
		const maxVisible = 7;
		const halfVisible = Math.floor(maxVisible / 2);

		if (totalPages <= maxVisible) 
		{
			// Show all pages if total is less than max visible
			for (let i = 1; i <= totalPages; i++) 
			{
				pages.push(i);
			}
		}
		else 
		{
			// Always show first page
			pages.push(1);

			// Calculate start and end of visible pages
			let start = Math.max(2, page - halfVisible);
			let end = Math.min(totalPages - 1, page + halfVisible);

			// Adjust if we're near the beginning
			if (page <= halfVisible + 1) 
			{
				end = maxVisible - 1;
			}

			// Adjust if we're near the end
			if (page >= totalPages - halfVisible) 
			{
				start = totalPages - maxVisible + 2;
			}

			// Add ellipsis if needed at the beginning
			if (start > 2) 
			{
				pages.push('...');
			}

			// Add visible page numbers
			for (let i = start; i <= end; i++) 
			{
				pages.push(i);
			}

			// Add ellipsis if needed at the end
			if (end < totalPages - 1) 
			{
				pages.push('...');
			}

			// Always show last page
			pages.push(totalPages);
		}

		return pages;
	}, [page, totalPages]);

	const handlePageClick = useCallback((pageNum: number | string) => 
	{
		if (typeof pageNum === 'number' && pageNum !== page) 
		{
			onPageChange(pageNum);
		}
	}, [page, onPageChange]);

	const handlePrevious = useCallback(() => 
	{
		if (hasPrev) 
		{
			onPageChange(page - 1);
		}
	}, [hasPrev, page, onPageChange]);

	const handleNext = useCallback(() => 
	{
		if (hasNext) 
		{
			onPageChange(page + 1);
		}
	}, [hasNext, page, onPageChange]);

	const handleFirst = useCallback(() => 
	{
		if (page !== 1) 
		{
			onPageChange(1);
		}
	}, [page, onPageChange]);

	const handleLast = useCallback(() => 
	{
		if (page !== totalPages) 
		{
			onPageChange(totalPages);
		}
	}, [page, totalPages, onPageChange]);

	if (totalPages <= 1) 
	{
		return null;
	}

	return (
		<div className="pagination">
			<div className="pagination-info">
				Showing page {page} of {totalPages}
				{pagination.total && (
					<span className="total-results">
						({pagination.total.toLocaleString()} results)
					</span>
				)}
			</div>

			<div className="pagination-controls">
				<button
					onClick={handleFirst}
					disabled={!hasPrev}
					className="pagination-btn first"
					aria-label="First page"
				>
					<svg viewBox="0 0 24 24" width="16" height="16">
						<path d="M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z" />
					</svg>
				</button>

				<button
					onClick={handlePrevious}
					disabled={!hasPrev}
					className="pagination-btn prev"
					aria-label="Previous page"
				>
					<svg viewBox="0 0 24 24" width="16" height="16">
						<path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z" />
					</svg>
					Previous
				</button>

				<div className="pagination-numbers">
					{pageNumbers.map((pageNum, index) => (
						<button
							key={index}
							onClick={() => handlePageClick(pageNum)}
							disabled={pageNum === '...'}
							className={`pagination-number ${pageNum === page ? 'active' : ''} ${
								pageNum === '...' ? 'ellipsis' : ''
							}`}
							aria-label={
								typeof pageNum === 'number' ? `Page ${pageNum}` : undefined
							}
							aria-current={pageNum === page ? 'page' : undefined}
						>
							{pageNum}
						</button>
					))}
				</div>

				<button
					onClick={handleNext}
					disabled={!hasNext}
					className="pagination-btn next"
					aria-label="Next page"
				>
					Next
					<svg viewBox="0 0 24 24" width="16" height="16">
						<path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z" />
					</svg>
				</button>

				<button
					onClick={handleLast}
					disabled={!hasNext}
					className="pagination-btn last"
					aria-label="Last page"
				>
					<svg viewBox="0 0 24 24" width="16" height="16">
						<path d="M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z" />
					</svg>
				</button>
			</div>

			<div className="pagination-jump">
				<label htmlFor="page-jump">Go to page:</label>
				<input
					id="page-jump"
					type="number"
					min="1"
					max={totalPages}
					placeholder={page.toString()}
					onKeyDown={(e) => 
					{
						if (e.key === 'Enter') 
						{
							const value = parseInt((e.target as HTMLInputElement).value, 10);
							if (value >= 1 && value <= totalPages && value !== page) 
							{
								onPageChange(value);
								(e.target as HTMLInputElement).value = '';
							}
						}
					}}
					className="page-jump-input"
				/>
			</div>
		</div>
	);
};

export default Pagination;
