import React from 'react';

interface SearchStatsProps {
	query: string;
	total: number;
	took: number;
}

export const SearchStats: React.FC<SearchStatsProps> = ({ query, total, took }) => 
{
	const formatTime = (ms: number) => 
	{
		if (ms < 1000) 
		{
			return `${ms}ms`;
		}
		return `${(ms / 1000).toFixed(2)}s`;
	};

	return (
		<div className="search-stats">
			<div className="stats-content">
				<span className="stats-query">
					Results for "<strong>{query}</strong>"
				</span>
				<span className="stats-info">
					Found <strong>{total.toLocaleString()}</strong> results 
					in <strong>{formatTime(took)}</strong>
				</span>
			</div>
		</div>
	);
};

export default SearchStats;
