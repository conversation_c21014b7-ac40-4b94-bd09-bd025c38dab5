import React, { useState, useCallback, useEffect } from 'react';
import { SearchForm } from './SearchForm';
import { SearchFilters } from './SearchFilters';
import { SearchResults } from './SearchResults';
import { Pagination } from './Pagination';
import { SearchStats } from './SearchStats';
import type { 
	SearchPageProps, 
	DomainResult, 
	SearchFacets, 
	PaginationInfo, 
	SearchFilters as Filters 
} from './types';

const SearchPage: React.FC<SearchPageProps> = ({ initialData = {} }) => 
{
	const {
		query = '',
		results = [],
		facets = {},
		pagination = {
			page: 1,
			limit: 20,
			total: 0,
			totalPages: 0,
			hasNext: false,
			hasPrev: false
		},
		filters = {},
		took = 0,
	} = initialData;

	const [searchQuery, setSearchQuery] = useState(query);
	const [activeFilters, setActiveFilters] = useState<Filters>(filters);
	const [isLoading, setIsLoading] = useState(false);
	const [searchResults, setSearchResults] = useState<DomainResult[]>(results);
	const [searchFacets, setSearchFacets] = useState<SearchFacets>(facets);
	const [paginationInfo, setPaginationInfo] = useState<PaginationInfo>(pagination as PaginationInfo);
	const [searchTime, setSearchTime] = useState(took);
	const [error, setError] = useState<string | null>(null);

	const performSearch = useCallback(async (
		query: string,
		filters: Filters,
		page: number = 1
	) => 
	{
		setIsLoading(true);
		setError(null);

		try 
		{
			const params = new URLSearchParams({
				q: query,
				page: page.toString(),
				limit: paginationInfo.limit.toString(),
				...Object.entries(filters).reduce((acc, [key, value]) => 
				{
					if (value !== undefined) 
					{
						acc[key] = value.toString();
					}
					return acc;
				}, {} as Record<string, string>)
			});

			const response = await fetch(`/api/domains/search?${params}`);
			
			if (!response.ok) 
			{
				throw new Error('Search failed');
			}

			const data = await response.json();

			setSearchResults(data.results || []);
			setSearchFacets(data.facets || {});
			setPaginationInfo(data.pagination || {
				page,
				limit: 20,
				total: 0,
				totalPages: 0,
				hasNext: false,
				hasPrev: false
			});
			setSearchTime(data.took || 0);
		}
		catch (err) 
		{
			setError(err instanceof Error ? err.message : 'An error occurred');
			setSearchResults([]);
		}
		finally 
		{
			setIsLoading(false);
		}
	}, [paginationInfo.limit]);

	const handleSearch = useCallback((query: string) => 
	{
		setSearchQuery(query);
		if (query) 
		{
			performSearch(query, activeFilters, 1);
		}
		else 
		{
			setSearchResults([]);
			setSearchFacets({});
		}
	}, [activeFilters, performSearch]);

	const handleFilterChange = useCallback((filters: Filters) => 
	{
		setActiveFilters(filters);
		if (searchQuery) 
		{
			performSearch(searchQuery, filters, 1);
		}
	}, [searchQuery, performSearch]);

	const handlePageChange = useCallback((page: number) => 
	{
		performSearch(searchQuery, activeFilters, page);
		// Scroll to top of results
		window.scrollTo({ top: 0, behavior: 'smooth' });
	}, [searchQuery, activeFilters, performSearch]);

	const handleDomainClick = useCallback((domain: string) => 
	{
		// Navigate to domain details page
		window.location.href = `/domain/${domain}`;
	}, []);

	// Update URL with search params
	useEffect(() => 
	{
		const params = new URLSearchParams();
		if (searchQuery) params.set('q', searchQuery);
		if (paginationInfo.page > 1) params.set('page', paginationInfo.page.toString());
		
		Object.entries(activeFilters).forEach(([key, value]) => 
		{
			if (value !== undefined) 
			{
				params.set(key, value.toString());
			}
		});

		const newUrl = params.toString() 
			? `${window.location.pathname}?${params}`
			: window.location.pathname;
		
		window.history.replaceState({}, '', newUrl);
	}, [searchQuery, activeFilters, paginationInfo.page]);

	return (
		<div className="search-page">
			{/* Hero/Header */}
			<div className="box page-hero has-text-centered mb-5">
				<h1 className="title is-2 mb-2 title-gradient">Domain Search</h1>
				<p className="subtitle is-6 has-text-grey mb-4">Find and analyze domains across performance, security, and SEO</p>
				<SearchForm
					initialQuery={searchQuery}
					onSearch={handleSearch}
					isLoading={isLoading}
				/>
				{searchQuery && !isLoading && (
					<SearchStats
						query={searchQuery}
						total={paginationInfo.total}
						took={searchTime}
					/>
				)}
			</div>

			{/* Error */}
			{error && (
				<div className="box has-text-danger">
					<div className="is-flex is-align-items-center gap-2">
						<span className="is-size-4">⚠️</span>
						<span>{error}</span>
					</div>
				</div>
			)}

			{/* Layout */}
			<div className="search-layout">
				{(searchFacets.categories?.length ||
				  searchFacets.countries?.length ||
				  searchFacets.technologies?.length) ? (
					<aside>
						<div className="box card-pro sticky-top">
							<SearchFilters
								filters={activeFilters}
								facets={searchFacets}
								onFilterChange={handleFilterChange}
							/>
						</div>
					</aside>
				) : null}

				<main>
					<SearchResults
						results={searchResults}
						isLoading={isLoading}
						onDomainClick={handleDomainClick}
					/>

					{searchResults.length > 0 && (
						<div className="mt-4">
							<Pagination
								pagination={paginationInfo}
								onPageChange={handlePageChange}
							/>
						</div>
					)}
				</main>
			</div>
		</div>
	);
};

export default SearchPage;
