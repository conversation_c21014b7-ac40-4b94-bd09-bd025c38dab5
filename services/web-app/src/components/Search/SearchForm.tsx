import React, { useState, useCallback, useEffect, useRef } from 'react';

interface SearchFormProps {
	initialQuery: string;
	onSearch: (query: string) => void;
	isLoading: boolean;
}

export const SearchForm: React.FC<SearchFormProps> = ({ 
	initialQuery, 
	onSearch, 
	isLoading 
}) => 
{
	const [query, setQuery] = useState(initialQuery);
	const [suggestions, setSuggestions] = useState<string[]>([]);
	const [showSuggestions, setShowSuggestions] = useState(false);
	const [activeSuggestionIndex, setActiveSuggestionIndex] = useState(-1);
	const inputRef = useRef<HTMLInputElement>(null);
	const suggestionsRef = useRef<HTMLDivElement>(null);

	const handleSubmit = useCallback((e: React.FormEvent) => 
	{
		e.preventDefault();
		if (query.trim()) 
		{
			onSearch(query.trim());
		}
	}, [query, onSearch]);

	const handleClear = useCallback(() => 
	{
		setQuery('');
		setSuggestions([]);
		setShowSuggestions(false);
		onSearch('');
	}, [onSearch]);

	// Get search suggestions
	const fetchSuggestions = useCallback(async (value: string) => 
	{
		if (value.length >= 2) 
		{
			try 
			{
				const response = await fetch(`/api/domains/search/suggestions?q=${encodeURIComponent(value)}&limit=5`);
				if (response.ok) 
				{
					const data = await response.json();
					setSuggestions(data.suggestions || []);
					setShowSuggestions(true);
				}
			}
			catch (error) 
			{
				console.error('Failed to fetch search suggestions:', error);
				setSuggestions([]);
				setShowSuggestions(false);
			}
		}
		else 
		{
			setSuggestions([]);
			setShowSuggestions(false);
		}
	}, []);

	// Handle input change with suggestions
	const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => 
	{
		const value = e.target.value;
		setQuery(value);
		setActiveSuggestionIndex(-1);
		fetchSuggestions(value);
	}, [fetchSuggestions]);

	// Handle keyboard navigation
	const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => 
	{
		if (!showSuggestions || suggestions.length === 0) return;

		switch (e.key) 
		{
			case 'ArrowDown':
				e.preventDefault();
				setActiveSuggestionIndex(prev => 
					prev < suggestions.length - 1 ? prev + 1 : 0);
				break;
			case 'ArrowUp':
				e.preventDefault();
				setActiveSuggestionIndex(prev => 
					prev > 0 ? prev - 1 : suggestions.length - 1);
				break;
			case 'Enter':
				if (activeSuggestionIndex >= 0) 
				{
					e.preventDefault();
					const selectedSuggestion = suggestions[activeSuggestionIndex];
					setQuery(selectedSuggestion);
					setShowSuggestions(false);
					onSearch(selectedSuggestion);
				}
				break;
			case 'Escape':
				setShowSuggestions(false);
				setActiveSuggestionIndex(-1);
				break;
		}
	}, [showSuggestions, suggestions, activeSuggestionIndex, onSearch]);

	// Handle suggestion click
	const handleSuggestionClick = useCallback((suggestion: string) => 
	{
		setQuery(suggestion);
		setShowSuggestions(false);
		onSearch(suggestion);
	}, [onSearch]);

	// Close suggestions when clicking outside
	useEffect(() => 
	{
		const handleClickOutside = (event: MouseEvent) => 
		{
			if (suggestionsRef.current && !suggestionsRef.current.contains(event.target as Node) &&
				inputRef.current && !inputRef.current.contains(event.target as Node)) 
			{
				setShowSuggestions(false);
			}
		};

		document.addEventListener('mousedown', handleClickOutside);
		return () => document.removeEventListener('mousedown', handleClickOutside);
	}, []);

	return (
		<form onSubmit={handleSubmit} className="mb-4">
			<div className="field has-addons is-justify-content-center">
				<div className="control is-expanded">
					<input
						ref={inputRef}
						type="text"
						value={query}
						onChange={handleInputChange}
						onKeyDown={handleKeyDown}
						placeholder="Search domains by name, keyword, or category..."
						className="input"
						disabled={isLoading}
						autoFocus
					/>
				</div>
				{query && (
					<div className="control">
						<button
							type="button"
							onClick={handleClear}
							className="button is-light"
							disabled={isLoading}
							aria-label="Clear search"
						>
							×
						</button>
					</div>
				)}
				<div className="control">
					<button
						type="submit"
						className="button is-primary"
						disabled={isLoading || !query.trim()}
					>
						{isLoading ? (
							<span className="loading loading--sm" />
						) : (
							'Search'
						)}
					</button>
				</div>
			</div>

			{/* Search Suggestions Dropdown */}
			{showSuggestions && suggestions.length > 0 && (
				<div className="is-flex is-justify-content-center">
					<div ref={suggestionsRef} className="dropdown is-active" style={{ width: '100%', maxWidth: 720 }}>
						<div className="dropdown-menu" role="menu" style={{ display: 'block', width: '100%' }}>
							<div className="dropdown-content">
								{suggestions.map((suggestion, index) => (
									<a
										key={suggestion}
										className={`dropdown-item ${index === activeSuggestionIndex ? 'is-active' : ''}`}
										onClick={() => handleSuggestionClick(suggestion)}
										onMouseEnter={() => setActiveSuggestionIndex(index)}
									>
										{suggestion}
									</a>
								))}
							</div>
						</div>
					</div>
				</div>
			)}

			<div className="has-text-centered mt-2">
				<span className="has-text-grey is-size-7">Try searching for: </span>
				<div className="buttons is-centered is-justify-content-center is-small" style={{ marginTop: 8 }}>
					<button type="button" className="button is-light is-small" onClick={() => onSearch('technology')}>
						technology
					</button>
					<button type="button" className="button is-light is-small" onClick={() => onSearch('news')}>
						news
					</button>
					<button type="button" className="button is-light is-small" onClick={() => onSearch('education')}>
						education
					</button>
					<button type="button" className="button is-light is-small" onClick={() => onSearch('finance')}>
						finance
					</button>
				</div>
			</div>
		</form>
	);
};

export default SearchForm;
