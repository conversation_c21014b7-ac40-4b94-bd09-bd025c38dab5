import React from 'react';
import type { DomainResult } from './types';
import { DomainCard } from './DomainCard';

interface SearchResultsProps {
    results: DomainResult[];
    isLoading: boolean;
    onDomainClick?: (domain: string) => void;
}

export const SearchResults: React.FC<SearchResultsProps> = ({
    results,
    isLoading,
    onDomainClick
}) =>
{
    if (isLoading)
    {
        return (
            <div className="box has-text-centered">
                <div className="loading mb-3" />
                <h3 className="title is-6">Searching domains…</h3>
                <p className="has-text-grey is-size-7">Fetching the most relevant results</p>
            </div>
        );
    }

    if (results.length === 0)
    {
        return (
            <div className="box has-text-centered">
                <div className="is-size-1 mb-2">🔍</div>
                <h3 className="title is-5">No results found</h3>
                <p className="has-text-grey is-size-7">Try adjusting your search query or filters</p>
            </div>
        );
    }

    return (
        <div>
            <div className="domains-grid">
                {results.map(result => (
                    <DomainCard
                        key={result.domain}
                        domain={result}
                        onClick={() => onDomainClick?.(result.domain)}
                    />
                ))}
            </div>
        </div>
    );
};

export default SearchResults;
