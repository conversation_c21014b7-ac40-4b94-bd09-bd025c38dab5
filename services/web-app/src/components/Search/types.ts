export interface DomainResult {
	domain: string;
	title?: string;
	description?: string;
	category?: string;
	country?: string;
	globalRank?: number;
	categoryRank?: number;
	overallScore?: number;
	performanceScore?: number;
	securityScore?: number;
	seoScore?: number;
	technicalScore?: number;
	backlinkScore?: number;
	trafficEstimate?: number;
	sslGrade?: string;
	technologies?: string[];
	lastUpdated?: string;
	screenshot?: string;
	favicon?: string;
}

export interface SearchFacets {
	categories?: Array<{ name: string; count: number }>;
	countries?: Array<{ name: string; count: number }>;
	technologies?: Array<{ name: string; count: number }>;
	sslGrades?: Array<{ name: string; count: number }>;
}

export interface PaginationInfo {
	page: number;
	limit: number;
	total: number;
	totalPages: number;
	hasNext: boolean;
	hasPrev: boolean;
}

export interface SearchFilters {
	category?: string;
	country?: string;
	technology?: string;
	sslGrade?: string;
	minRank?: number;
	maxRank?: number;
	minScore?: number;
	maxScore?: number;
	sort?: string;
}

export interface SearchPageProps {
	initialData?: {
		query?: string;
		results?: DomainResult[];
		facets?: SearchFacets;
		pagination?: PaginationInfo;
		filters?: SearchFilters;
		took?: number;
	};
}
