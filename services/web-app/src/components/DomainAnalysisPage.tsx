import React from 'react';

interface DomainAnalysisPageProps
{
	initialData?: {
		analysis?: DomainAnalysis;
		error?: string;
	};
}

interface DomainAnalysis
{
	domain: string;
	globalRank?: number;
	categoryRank?: number;
	category?: string;
	country?: string;
	overallScore?: number;
	performanceScore?: number;
	securityScore?: number;
	seoScore?: number;
	technicalScore?: number;
	backlinkScore?: number;
	trafficEstimate?: number;
	domainAge?: number;
	sslGrade?: string;
	technologies?: string[];
	lastUpdated?: string;
	screenshot?: string;
	favicon?: string;
	metrics?: {
		performance?: PerformanceMetrics;
		security?: SecurityMetrics;
		seo?: SEOMetrics;
		technical?: TechnicalMetrics;
	};
	rankingExplanation?: RankingExplanation;
}

interface PerformanceMetrics
{
	loadTime?: number;
	firstContentfulPaint?: number;
	largestContentfulPaint?: number;
	cumulativeLayoutShift?: number;
	firstInputDelay?: number;
	speedIndex?: number;
	pageSize?: number;
	resourceCount?: number;
}

interface SecurityMetrics
{
	sslGrade?: string;
	securityHeaders?: {
		hsts?: boolean;
		csp?: boolean;
		xframe?: boolean;
		xss?: boolean;
	};
	vulnerabilities?: string[];
	certificateInfo?: {
		issuer?: string;
		expiration?: string;
		daysUntilExpiry?: number;
	};
}

interface SEOMetrics
{
	title?: string;
	description?: string;
	keywords?: string[];
	structuredData?: boolean;
	sitemap?: boolean;
	robotsTxt?: boolean;
	mobileOptimized?: boolean;
	pageSpeed?: number;
}

interface TechnicalMetrics
{
	httpVersion?: string;
	compression?: boolean;
	caching?: boolean;
	cdn?: string;
	serverLocation?: string;
	uptime?: number;
	responseTime?: number;
}

interface RankingExplanation
{
	factors?: Array<{
		name: string;
		score: number;
		weight: number;
		impact: 'positive' | 'negative' | 'neutral';
		description: string;
	}>;
	recommendations?: string[];
	competitorComparison?: Array<{
		domain: string;
		rank: number;
		score: number;
	}>;
}

function getScoreVariant(score: number): 'good' | 'ok' | 'warn' | 'bad'
{
	if (score >= 0.8) return 'good';
	if (score >= 0.6) return 'ok';
	if (score >= 0.4) return 'warn';
	return 'bad';
}

const ScoreBadge: React.FC<{ score?: number }> = ({ score }) =>
{
	if (score === undefined || score === null) return <span className="has-text-grey-light">N/A</span>;
	const pct = Math.round(score * 100);
	const v = getScoreVariant(score);
	return <span className={`score-badge score-badge--${v}`}>{pct}%</span>;
};


const DomainAnalysisPage: React.FC<DomainAnalysisPageProps> = ({ initialData = {} }) =>
{
	const { analysis, error } = initialData as { analysis?: DomainAnalysis; error?: string };

	if (error)
	{
		return (
			<div className="domain-analysis-page">
				<div className="box card-pro has-text-centered">
					<div className="is-size-1 mb-3">⚠️</div>
					<h2 className="title is-4">Analysis Not Available</h2>
					<p className="has-text-danger">{error}</p>
				</div>
			</div>
		);
	}

	if (!analysis)
	{
		return (
			<div className="domain-analysis-page">
				<div className="box card-pro has-text-centered">
					<div className="loading mb-3" />
					<h3 className="title is-5">Loading domain analysis...</h3>
					<p className="has-text-grey">Please wait while we gather comprehensive metrics</p>
				</div>
			</div>
		);
	}

	return (
		<div className="domain-analysis-page">
			{/* Header Section */}
			<DomainHeader analysis={analysis} />

			{/* Overview Cards */}
			<div className="grid-auto-200 mb-5">
				<OverviewCard
					title="Global Rank"
					value={analysis.globalRank ? `#${analysis.globalRank.toLocaleString()}` : 'N/A'}
					icon="🏆"
				/>
				<OverviewCard
					title="Overall Score"
					value={analysis.overallScore ? `${Math.round(analysis.overallScore * 100)}%` : 'N/A'}
					icon="📊"
				/>
				<OverviewCard
					title="Traffic Estimate"
					value={formatTraffic(analysis.trafficEstimate)}
					icon="📈"
				/>
				<OverviewCard
					title="Domain Age"
					value={analysis.domainAge ? `${analysis.domainAge} days` : 'N/A'}
					icon="📅"
				/>
			</div>

			{/* Detailed Metrics */}
			<div className="grid-auto-200 mb-5">
				<MetricsCard
					title="Performance Metrics"
					icon="⚡"
					score={analysis.performanceScore}
					metrics={analysis.metrics?.performance}
				/>
				<MetricsCard
					title="Security Metrics"
					icon="🔒"
					score={analysis.securityScore}
					metrics={analysis.metrics?.security}
				/>
				<MetricsCard
					title="SEO Metrics"
					icon="🔍"
					score={analysis.seoScore}
					metrics={analysis.metrics?.seo}
				/>
				<MetricsCard
					title="Technical Metrics"
					icon="⚙️"
					score={analysis.technicalScore}
					metrics={analysis.metrics?.technical}
				/>
			</div>

			{/* Technologies */}
			{analysis.technologies && analysis.technologies.length > 0 && (
				<TechnologiesSection technologies={analysis.technologies} />
			)}

			{/* Ranking Explanation */}
			{analysis.rankingExplanation && (
				<RankingExplanationSection explanation={analysis.rankingExplanation} />
			)}
		</div>
	);
};

interface DomainHeaderProps
{
	analysis: DomainAnalysis;
}

const DomainHeader: React.FC<DomainHeaderProps> = ({ analysis }) => (
	<div className="box card-pro hover-elevate mb-5">
		<div className="is-flex is-align-items-center gap-3 mb-3">
			{analysis.favicon && (
				<img
					src={analysis.favicon}
					alt={`${analysis.domain} favicon`}
					className="domain-favicon"
					width={48}
					height={48}
					onError={(e) => { e.currentTarget.style.display = 'none'; }}
				/>
			)}
			<div>
				<h1 className="title is-2 m-0">
					{analysis.domain}
				</h1>
				<div className="has-text-grey is-size-6 mt-2">
					{analysis.category && <span>{analysis.category}</span>}
					{analysis.country && <span> • {analysis.country}</span>}
					{analysis.lastUpdated && (
						<span> • Last updated: {new Date(analysis.lastUpdated).toLocaleDateString()}</span>
					)}
				</div>
			</div>
		</div>

		{analysis.screenshot && (
			<div className="mt-3">
				<img src={analysis.screenshot} alt={`${analysis.domain} screenshot`} className="screenshot-img" />
			</div>
		)}
	</div>
);

interface OverviewCardProps
{
	title: string;
	value: string;
	icon: string;
}

const OverviewCard: React.FC<OverviewCardProps> = ({
	title, value, icon,
}) => (
	<div className="box card-pro has-text-centered overview-card">
		<div className="is-size-3 mb-2">{icon}</div>
		<div className="title is-3 mb-1">{value}</div>
		<div className="has-text-grey is-size-7">{title}</div>
	</div>
);

interface MetricsCardProps
{
	title: string;
	icon: string;
	score?: number;
	metrics?: unknown;
}

const MetricsCard: React.FC<MetricsCardProps> = ({
	title, icon, score, metrics,
}) =>
{
	return (
		<div className="box card-pro">
			<div className="is-flex is-justify-content-space-between is-align-items-center mb-3">
				<h3 className="title is-5 m-0 is-flex is-align-items-center gap-2">
					<span>{icon}</span>
					{title}
				</h3>
				<div>
					<ScoreBadge score={score} />
				</div>
			</div>

			{Boolean(metrics) && typeof metrics === 'object' && (
				<div className="is-size-7 has-text-grey">
					{Object.entries(metrics as Record<string, unknown>).map(([key, value]) => (
						<div key={key} className="is-flex is-justify-content-space-between mb-2">
							<span className="is-capitalized has-text-grey">
								{key.replace(/([A-Z])/g, ' $1').trim()}:
							</span>
							<span className="has-text-weight-medium">
								{formatMetricValue(key, value)}
							</span>
						</div>
					))}
				</div>
			)}
		</div>
	);
};

interface TechnologiesSectionProps
{
	technologies: string[];
}

const TechnologiesSection: React.FC<TechnologiesSectionProps> = ({ technologies }) => (
	<div className="box card-pro mb-5">
		<h3 className="title is-5 mb-3 is-flex is-align-items-center gap-2">
			<span>🛠️</span>
			Technologies Detected
		</h3>
		<div className="tags">
			{technologies.map(tech => (
				<span key={tech} className="tag is-light">{tech}</span>
			))}
		</div>
	</div>
);

interface RankingExplanationSectionProps
{
	explanation: RankingExplanation;
}

const RankingExplanationSection: React.FC<RankingExplanationSectionProps> = ({ explanation }) => (
	<div className="box card-pro">
		<h3 className="title is-5 mb-3 is-flex is-align-items-center gap-2">
			<span>📋</span>
			Ranking Explanation
		</h3>

		{explanation.factors && explanation.factors.length > 0 && (
			<div className="mb-5">
				<h4 className="title is-6 has-text-grey mb-3">Ranking Factors</h4>
				{explanation.factors.map(factor => (
					<div key={factor.name} className="box card-pro is-shadowless is-flex is-justify-content-space-between is-align-items-center mb-2">
						<div>
							<div className="has-text-weight-semibold mb-1">{factor.name}</div>
							<div className="is-size-7 has-text-grey">{factor.description}</div>
						</div>
						<div className="has-text-right ml-3">
							<ScoreBadge score={factor.score} />
							<div className="is-size-7 has-text-grey">Weight: {Math.round(factor.weight * 100)}%</div>
						</div>
					</div>
				))}
			</div>
		)}

		{explanation.recommendations && explanation.recommendations.length > 0 && (
			<div className="mb-5">
				<h4 className="title is-6 has-text-grey mb-3">Recommendations</h4>
				<ul className="is-size-7">
					{explanation.recommendations.map(rec => (
						<li key={rec} className="mb-2 has-text-grey-dark">{rec}</li>
					))}
				</ul>
			</div>
		)}

		{explanation.competitorComparison && explanation.competitorComparison.length > 0 && (
			<div>
				<h4 className="title is-6 has-text-grey mb-3">Competitor Comparison</h4>
				<div className="grid-auto-200">
					{explanation.competitorComparison.map(competitor => (
						<div key={competitor.domain} className="box card-pro has-text-centered">
							<div className="has-text-weight-semibold mb-1">{competitor.domain}</div>
							<div className="is-size-7 has-text-grey">Rank #{competitor.rank} • Score: {Math.round(competitor.score * 100)}%</div>
						</div>
					))}
				</div>
			</div>
		)}
	</div>
);



function formatTraffic(traffic?: number): string
{
	if (!traffic) return 'N/A';
	if (traffic >= 1000000) return `${(traffic / 1000000).toFixed(1)}M/month`;
	if (traffic >= 1000) return `${(traffic / 1000).toFixed(1)}K/month`;
	return `${traffic}/month`;
}

function formatMetricValue(key: string, value: unknown): string
{
	if (value === null || value === undefined) return 'N/A';

	if (typeof value === 'boolean') return value ? 'Yes' : 'No';
	if (typeof value === 'number')
	{
		if (key.includes('Time') || key.includes('Duration')) return `${value}ms`;
		if (key.includes('Size')) return `${(value / 1024).toFixed(1)}KB`;
		if (key.includes('Count')) return value.toString();
		return value.toString();
	}

	return String(value);
}

export default DomainAnalysisPage;
