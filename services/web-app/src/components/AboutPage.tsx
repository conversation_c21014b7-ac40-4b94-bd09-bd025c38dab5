import React from 'react';

const AboutPage: React.FC = () => (
  <section className="section">
    <div className="container">
      <div>
        <div className="box page-hero has-text-centered mb-6">
          <h1 className="title is-2 mb-2">About Domain Ranking System</h1>
          <p className="subtitle is-5 has-text-grey">
            A modern platform for searching, ranking, and deeply analyzing domains across performance,
            security, SEO, and technical dimensions.
          </p>
        </div>

        <div className="grid-auto-250 mb-6">
          <div className="box card-pro hover-elevate has-text-centered">
            <div className="is-size-3 mb-2">🔎</div>
            <div className="title is-5 mb-1">Powerful Search</div>
            <div className="has-text-grey is-size-7">Filter by category, country, tech stack, and more</div>
          </div>
          <div className="box card-pro hover-elevate has-text-centered">
            <div className="is-size-3 mb-2">📊</div>
            <div className="title is-5 mb-1">Comprehensive Analysis</div>
            <div className="has-text-grey is-size-7">Performance, security, SEO, and technical metrics</div>
          </div>
          <div className="box card-pro hover-elevate has-text-centered">
            <div className="is-size-3 mb-2">🏆</div>
            <div className="title is-5 mb-1">Top Domains</div>
            <div className="has-text-grey is-size-7">Explore leaders by category and timeframe</div>
          </div>
          <div className="box card-pro hover-elevate has-text-centered">
            <div className="is-size-3 mb-2">⚖️</div>
            <div className="title is-5 mb-1">Side‑by‑Side Compare</div>
            <div className="has-text-grey is-size-7">Understand differences quickly and clearly</div>
          </div>
        </div>

        <div className="grid-auto-200">
          <div className="box card-pro">
            <h2 className="title is-4 mb-2">Our Data</h2>
            <p className="has-text-grey">
              We combine internal crawlers with curated external sources to compute scores and ranks.
              Rankings refresh periodically; timestamps on pages indicate last update times.
            </p>
          </div>
          <div className="box card-pro">
            <h2 className="title is-4 mb-2">Contact</h2>
            <p className="has-text-grey">
              Have feedback or questions? We’d love to hear from you.
            </p>
          </div>
        </div>
      </div>
    </div>
  </section>
);

export default AboutPage;

