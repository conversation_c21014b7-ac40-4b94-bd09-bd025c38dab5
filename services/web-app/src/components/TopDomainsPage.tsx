import React, { useState, useEffect } from 'react';

interface TopDomainsPageProps
{
	initialData?: {
		topDomains?: TopDomainsList;
		categories?: Category[];
		selectedCategory?: string;
		selectedCountry?: string;
		selectedTimeframe?: string;
	};
}

interface TopDomainsList
{
	domains: TopDomain[];
	category?: string;
	country?: string;
	timeframe: string;
	totalCount: number;
	lastUpdated: string;
	pagination?: {
		page: number;
		limit: number;
		total: number;
		totalPages: number;
		hasNext: boolean;
		hasPrev: boolean;
	};
}

interface TopDomain
{
	domain: string;
	title?: string;
	description?: string;
	globalRank: number;
	categoryRank?: number;
	category: string;
	country?: string;
	overallScore: number;
	performanceScore?: number;
	securityScore?: number;
	seoScore?: number;
	technicalScore?: number;
	trafficEstimate?: number;
	sslGrade?: string;
	technologies?: string[];
	favicon?: string;
	screenshot?: string;
	lastUpdated: string;
	rankChange?: {
		direction: 'up' | 'down' | 'same';
		positions: number;
		period: string;
	};
	trendData?: Array<{
		date: string;
		rank: number;
		score: number;
	}>;
}

interface Category
{
	name: string;
	displayName: string;
	count: number;
	description?: string;
}

const TopDomainsPage: React.FC<TopDomainsPageProps> = ({ initialData }) =>
{
	const {
		topDomains,
		categories = [],
		selectedCategory = '',
		selectedCountry = '',
		selectedTimeframe = '7d',
	} = initialData || {};

	const [domainsData, setDomainsData] = useState<TopDomainsList | null>(topDomains || null);
	const [availableCategories, setAvailableCategories] = useState<Category[]>(categories);
	const [activeCategory, setActiveCategory] = useState(selectedCategory);
	const [activeCountry, setActiveCountry] = useState(selectedCountry);
	const [activeTimeframe, setActiveTimeframe] = useState(selectedTimeframe);
	const [isLoading, setIsLoading] = useState(false);
	const [viewMode, setViewMode] = useState<'table' | 'cards'>('table');

	const timeframeOptions = [
		{ value: '1d', label: 'Last 24 Hours' },
		{ value: '7d', label: 'Last 7 Days' },
		{ value: '30d', label: 'Last 30 Days' },
		{ value: '90d', label: 'Last 3 Months' },
		{ value: '1y', label: 'Last Year' },
	];

	const countryOptions = [
		{ value: '', label: 'All Countries' },
		{ value: 'US', label: 'United States' },
		{ value: 'GB', label: 'United Kingdom' },
		{ value: 'DE', label: 'Germany' },
		{ value: 'FR', label: 'France' },
		{ value: 'JP', label: 'Japan' },
		{ value: 'CN', label: 'China' },
		{ value: 'IN', label: 'India' },
		{ value: 'BR', label: 'Brazil' },
		{ value: 'CA', label: 'Canada' },
		{ value: 'AU', label: 'Australia' },
	];

	useEffect(() =>
	{
		if (!domainsData && !isLoading)
		{
			loadTopDomains();
		}
	}, []);

	const loadTopDomains = async (
		category?: string,
		country?: string,
		timeframe?: string,
		page: number = 1,
	): Promise<void> =>
	{
		setIsLoading(true);

		try
		{
			const params = new URLSearchParams({
				limit: '50',
				page: page.toString(),
			});

			if (category) params.set('category', category);
			if (country) params.set('country', country);
			if (timeframe) params.set('timeframe', timeframe);

			const response = await fetch(`/api/domains/top?${params}`);
			const data = await response.json();

			if (response.ok)
			{
				setDomainsData(data);

				// Update URL
				const urlParams = new URLSearchParams();
				if (category) urlParams.set('category', category);
				if (country) urlParams.set('country', country);
				if (timeframe && timeframe !== '7d') urlParams.set('timeframe', timeframe);
				if (page > 1) urlParams.set('page', page.toString());

				const newUrl = `/top-domains${urlParams.toString() ? `?${ urlParams.toString()}` : ''}`;
				window.history.pushState({}, '', newUrl);
			}
			else
			{
				console.error('Failed to load top domains:', data.message);
			}
		}
		catch (error)
		{
			console.error('Error loading top domains:', error);
		}
		finally
		{
			setIsLoading(false);
		}
	};

	const handleCategoryChange = async (category: string): Promise<void> =>
	{
		setActiveCategory(category);
		await loadTopDomains(category, activeCountry, activeTimeframe);
	};

	const handleCountryChange = async (country: string): Promise<void> =>
	{
		setActiveCountry(country);
		await loadTopDomains(activeCategory, country, activeTimeframe);
	};

	const handleTimeframeChange = async (timeframe: string): Promise<void> =>
	{
		setActiveTimeframe(timeframe);
		await loadTopDomains(activeCategory, activeCountry, timeframe);
	};

	const handlePageChange = async (page: number): Promise<void> =>
	{
		await loadTopDomains(activeCategory, activeCountry, activeTimeframe, page);
	};

	return (
		<div className="top-domains-page">
			{/* Header */}
			<div className="box page-hero">
				<h1 className="title is-2 mb-2">🏆 Top Domains</h1>
				<p className="subtitle is-5 has-text-grey mb-4">Discover the highest-ranked domains based on comprehensive analysis of performance, security, and SEO metrics.</p>

				{/* Filters */}
				<div className="columns is-variable is-4 is-multiline mb-4">
					<div className="column is-12-mobile is-3-tablet">
						<div className="field">
							<label className="label is-size-7 has-text-grey">Category</label>
							<div className="control">
								<div className="select is-fullwidth">
									<select value={activeCategory} onChange={e => handleCategoryChange(e.target.value)}>
										<option value="">All Categories</option>
										{availableCategories.map(cat => (
											<option key={cat.name} value={cat.name}>
												{cat.displayName} ({cat.count})
											</option>
										))}
									</select>
								</div>
							</div>
						</div>
					</div>

					<div className="column is-12-mobile is-3-tablet">
						<div className="field">
							<label className="label is-size-7 has-text-grey">Country</label>
							<div className="control">
								<div className="select is-fullwidth">
									<select value={activeCountry} onChange={e => handleCountryChange(e.target.value)}>
										{countryOptions.map(country => (
											<option key={country.value} value={country.value}>{country.label}</option>
										))}
									</select>
								</div>
							</div>
						</div>
					</div>

					<div className="column is-12-mobile is-3-tablet">
						<div className="field">
							<label className="label is-size-7 has-text-grey">Timeframe</label>
							<div className="control">
								<div className="select is-fullwidth">
									<select value={activeTimeframe} onChange={e => handleTimeframeChange(e.target.value)}>
										{timeframeOptions.map(option => (
											<option key={option.value} value={option.value}>{option.label}</option>
										))}
									</select>
								</div>
							</div>
						</div>
					</div>

					<div className="column is-12-mobile is-3-tablet">
						<div className="field">
							<label className="label is-size-7 has-text-grey">View Mode</label>
							<div className="buttons has-addons is-fullwidth">
								<button onClick={() => setViewMode('table')} className={`button is-light is-fullwidth ${viewMode === 'table' ? 'is-primary' : ''}`}>Table</button>
								<button onClick={() => setViewMode('cards')} className={`button is-light is-fullwidth ${viewMode === 'cards' ? 'is-primary' : ''}`}>Cards</button>
							</div>
						</div>
					</div>
				</div>

				{/* Stats */}
				{domainsData && (
					<div className="notification is-light is-flex is-align-items-center is-justify-content-space-between">
						<div>
							Showing top {domainsData.domains.length} domains
							{activeCategory && <span> in {activeCategory}</span>}
							{activeCountry && <span> from {countryOptions.find(c => c.value === activeCountry)?.label}</span>}
						</div>
						<div>
							Last updated: {new Date(domainsData.lastUpdated).toLocaleString()}
						</div>
					</div>
				)}
			</div>

			{/* Loading State */}
			{isLoading && (
				<div className="box has-text-centered">
					<div className="loading mb-3" />
					<h3 className="title is-5">Loading top domains...</h3>
					<p className="has-text-grey">Please wait while we gather the latest rankings</p>
				</div>
			)}

			{/* Results */}
			{domainsData && !isLoading && (
				<>
					{viewMode === 'table' ? (
						<TopDomainsTable domains={domainsData.domains} />
					) : (
						<TopDomainsCards domains={domainsData.domains} />
					)}

					{/* Pagination */}
					{domainsData.pagination && domainsData.pagination.totalPages > 1 && (
						<TopDomainsPagination
							pagination={domainsData.pagination}
							onPageChange={handlePageChange}
						/>
					)}
				</>
			)}
		</div>
	);
};

interface TopDomainsTableProps
{
	domains: TopDomain[];
}

const TopDomainsTable: React.FC<TopDomainsTableProps> = ({ domains }) => (
	<div className="box">
		<div className="table-container">
			<table className="table is-fullwidth is-hoverable is-striped">
				<thead>
					<tr>
						<th className="has-text-left">
							Rank
						</th>
						<th className="has-text-left">
							Domain
						</th>
						<th className="has-text-centered">
							Score
						</th>
						<th className="has-text-centered">
							Performance
						</th>
						<th className="has-text-centered">
							Security
						</th>
						<th className="has-text-centered">
							SEO
						</th>
						<th className="has-text-centered">
							Traffic
						</th>
						<th className="has-text-centered">
							Trend
						</th>
					</tr>
				</thead>
				<tbody>
					{domains.map((domain, index) => (
						<tr
							key={domain.domain}
																														>
							<td>
								<div className="is-flex is-align-items-center gap-2">
									<span className="is-size-5 has-text-weight-bold has-text-primary">
										#{domain.globalRank}
									</span>
									{domain.rankChange && (
										<RankChangeIndicator change={domain.rankChange} />
									)}
								</div>
							</td>
							<td>
								<div className="is-flex is-align-items-center gap-3">
									{domain.favicon && (
										<img
											src={domain.favicon}
											alt={`${domain.domain} favicon`}
											className="domain-favicon"
										/>
									)}
									<div>
										<div className="has-text-weight-semibold mb-2">
											<a
												href={`/domain/${domain.domain}`}
												className="link-domain"
															>
												{domain.title || domain.domain}
											</a>
										</div>
										<div className="is-size-7 has-text-grey">
											{domain.domain} • {domain.category}
										</div>
									</div>
								</div>
							</td>
							<td className="has-text-centered">
								<ScoreBadge score={domain.overallScore} />
							</td>
							<td className="has-text-centered">
								<ScoreBadge score={domain.performanceScore} />
							</td>
							<td className="has-text-centered">
								<ScoreBadge score={domain.securityScore} />
							</td>
							<td className="has-text-centered">
								<ScoreBadge score={domain.seoScore} />
							</td>
							<td className="has-text-centered is-size-7 has-text-grey">
								{formatTraffic(domain.trafficEstimate)}
							</td>
							<td className="has-text-centered">
								{domain.trendData && domain.trendData.length > 0 && (
									<TrendIndicator data={domain.trendData} />
								)}
							</td>
						</tr>
					))}
				</tbody>
			</table>
		</div>
	</div>
);

interface TopDomainsCardsProps
{
	domains: TopDomain[];
}

const TopDomainsCards: React.FC<TopDomainsCardsProps> = ({ domains }) => (
	<div className="domains-grid">
		{domains.map(domain => (
			<TopDomainCard key={domain.domain} domain={domain} />
		))}
	</div>
);

interface TopDomainCardProps
{
	domain: TopDomain;
}

const TopDomainCard: React.FC<TopDomainCardProps> = ({ domain }) => (
	<div className="box card-pro hover-elevate">
		{/* Header */}
		<div className="is-flex is-justify-content-space-between is-align-items-flex-start mb-4">
			<div className="is-flex is-align-items-center gap-3">
				<div className="rank-badge">
					#{domain.globalRank}
				</div>
				{domain.favicon && (
					<img
						src={domain.favicon}
						alt={`${domain.domain} favicon`}
						className="domain-favicon domain-favicon--lg"
					/>
				)}
			</div>
			{domain.rankChange && (
				<RankChangeIndicator change={domain.rankChange} />
			)}
		</div>

		{/* Domain Info */}
		<div className="mb-4">
			<h3 className="title is-5 mb-2">
				<a
					href={`/domain/${domain.domain}`}
					className="link-domain"
				>
					{domain.title || domain.domain}
				</a>
			</h3>
			<div className="has-text-grey is-size-7 mb-2">
				{domain.domain} • {domain.category}
				{domain.country && <span> • {domain.country}</span>}
			</div>
			{domain.description && (
				<p className="has-text-grey is-size-7 mb-0 line-clamp-2">
					{domain.description.length > 100
						? `${domain.description.substring(0, 100)}...`
						: domain.description}
				</p>
			)}
		</div>

		{/* Scores */}
		<div className="grid-2 mb-4">
			<MetricCard label="Overall" score={domain.overallScore} />
			<MetricCard label="Performance" score={domain.performanceScore} />
			<MetricCard label="Security" score={domain.securityScore} />
			<MetricCard label="SEO" score={domain.seoScore} />
		</div>

		{/* Technologies */}
		{domain.technologies && domain.technologies.length > 0 && (
			<div className="mb-4">
				<div className="is-size-7 has-text-grey mb-2">
					Technologies:
				</div>
				<div className="tags">
					{domain.technologies.slice(0, 4).map((tech, index) => (
						<span key={index} className="tag is-light">
							{tech}
						</span>
					))}
					{domain.technologies.length > 4 && (
						<span className="has-text-grey is-size-7">+{domain.technologies.length - 4} more</span>
					)}
				</div>
			</div>
		)}

		{/* Footer */}
		<div className="is-flex is-justify-content-space-between is-align-items-center is-size-7 has-text-grey divider-top pt-3">
			<div>Traffic: {formatTraffic(domain.trafficEstimate)}</div>
			<div>{new Date(domain.lastUpdated).toLocaleDateString()}</div>
		</div>
	</div>
);

// Helper Components
interface ScoreBadgeProps
{
	score?: number;
}

const ScoreBadge: React.FC<ScoreBadgeProps> = ({ score }) =>
{
	if (score == null) return <span className="has-text-grey-light">N/A</span>;

	const percentage = Math.round(score * 100);
	const variant = getScoreVariant(score);

	return (
		<span className={`score-badge score-badge--${variant}`}>
			{percentage}%
		</span>
	);
};

interface MetricCardProps
{
	label: string;
	score?: number;
}

const MetricCard: React.FC<MetricCardProps> = ({ label, score }) =>
{
	const percentage = score ? Math.round(score * 100) : 0;
	const variant = getScoreVariant(score || 0);

	return (
		<div className={`metric-card metric-card--${variant}`}>
			<div className="metric-card__value">
				{score ? `${percentage}%` : 'N/A'}
			</div>
			<div className="metric-card__label">{label}</div>
		</div>
	);
};

interface RankChangeIndicatorProps
{
	change: {
		direction: 'up' | 'down' | 'same';
		positions: number;
		period: string;
	};
}

const RankChangeIndicator: React.FC<RankChangeIndicatorProps> = ({ change }) =>
{
	const getIcon = (): string =>
	{
		switch (change.direction)
		{
			case 'up': return '↗️';
			case 'down': return '↘️';
			default: return '→';
		}
	};

	return (
		<div className={`rank-change rank-change--${change.direction}`}>
			<span>{getIcon()}</span>
			{change.positions > 0 && <span>{change.positions}</span>}
		</div>
	);
};

interface TrendIndicatorProps
{
	data: Array<{
		date: string;
		rank: number;
		score: number;
	}>;
}

const TrendIndicator: React.FC<TrendIndicatorProps> = ({ data }) =>
{
	if (data.length < 2) return <span className="has-text-grey-light">N/A</span>;

	const first = data[0];
	const last = data[data.length - 1];
	const trend = last.score > first.score ? 'up' : last.score < first.score ? 'down' : 'same';

	const icon = trend === 'up' ? '📈' : trend === 'down' ? '📉' : '📊';

	return (
		<div className="is-flex is-align-items-center is-justify-content-center is-size-5">
			<span title={`Trend: ${trend}`}>{icon}</span>
		</div>
	);
};

interface TopDomainsPaginationProps
{
	pagination: {
		page: number;
		totalPages: number;
		hasNext: boolean;
		hasPrev: boolean;
	};
	onPageChange: (page: number) => Promise<void>;
}

const TopDomainsPagination: React.FC<TopDomainsPaginationProps> = ({ pagination, onPageChange }) => (
	<div className="pager">
		<button
			onClick={() => onPageChange(pagination.page - 1)}
			disabled={!pagination.hasPrev}
			className="pager__btn"
		>
			← Previous
		</button>

		<span className="pager__ellipsis">
			Page {pagination.page} of {pagination.totalPages}
		</span>

		<button
			onClick={() => onPageChange(pagination.page + 1)}
			disabled={!pagination.hasNext}
			className="pager__btn"
		>
			Next →
		</button>
	</div>
);

// Helper functions
function getScoreVariant(score: number): 'good' | 'ok' | 'warn' | 'bad'
{
	if (score >= 0.8) return 'good';
	if (score >= 0.6) return 'ok';
	if (score >= 0.4) return 'warn';
	return 'bad';
}


function formatTraffic(traffic?: number): string
{
	if (!traffic) return 'N/A';
	if (traffic >= 1000000) return `${(traffic / 1000000).toFixed(1)}M`;
	if (traffic >= 1000) return `${(traffic / 1000).toFixed(1)}K`;
	return traffic.toString();
}

export default TopDomainsPage;
