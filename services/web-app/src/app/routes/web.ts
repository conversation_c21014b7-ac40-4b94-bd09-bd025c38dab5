import type { Application, Request, Response } from 'ultimate-express';
import type { LoggerInstanceType } from '@shared';
import type DomainSearchService from '../../services/DomainSearchService';
import type DomainAnalysisService from '../../services/DomainAnalysisService';
import type TopDomainsService from '../../services/TopDomainsService';
import { ReactRenderer } from '../../utils/ReactRenderer';

interface Services {
	domainSearchService: DomainSearchService | null;
	domainAnalysisService: DomainAnalysisService | null;
	topDomainsService: TopDomainsService | null;
}

/**
 * Setup web page routes with React SSR
 */
export function setupWebRoutes(app: Application, services: Services, logger: LoggerInstanceType): void
{
		// Favicon placeholder to avoid template errors in dev
		app.get('/favicon.ico', (req: Request, res: Response) =>
		{
			return res.status(204).end();
		});

	// Contact page
	app.get('/contact', (req: Request, res: Response) =>
	{
		try
		{
			const pageData = ReactRenderer.renderPage({
				component: 'ContactPage',
				props: {},
			});

			return res.render('layout', pageData);
		}
		catch (error)
		{
			logger.error({ error }, 'Contact page render error');
			return res.status(500).send('Error rendering contact page');
		}
	});

	// Homepage
	app.get('/', async (req: Request, res: Response) =>
	{
		try
		{
			let trending: any = null;
			if (services.topDomainsService)
			{
				try
				{
					trending = await services.topDomainsService.getTrendingDomains({ timeframe: '7d', limit: 8 });
				}
				catch (error)
				{
					logger.error({ error }, 'Trending domains fetch failed');
				}
			}

			const pageData = ReactRenderer.renderPage({
				component: 'HomePage',
				props: {
					initialData: { trending },
				},
			});

			return res.render('layout', pageData);
		}
		catch (error)
		{
			logger.error({ error }, 'Homepage render error');
			return res.status(500).send('Error rendering homepage');
		}
	});

	// Search page
	app.get('/search', async (req: Request, res: Response) =>
	{
		try
		{
			const {
				q,
				category,
				country,
				technology,
				page = 1,
				limit = 20,
			} = req.query;

			// If query looks like a domain, redirect to domain view for quick details
			const normalizeDomain = (domain: string): string =>
				String(domain).toLowerCase().replace(/^https?:\/\//, '').replace(/^www\./, '').replace(/\/$/, '');
			const isLikelyDomain = (s: string): boolean => /^(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)+[a-z]{2,}$/i.test(normalizeDomain(s));
			if (typeof q === 'string' && q.trim() && isLikelyDomain(q))
			{
				const d = encodeURIComponent(normalizeDomain(q));
				return res.redirect(`/domain/${d}`);
			}

			// Get search results if query is provided
			let searchResults = null;
			if (q && services.domainSearchService)
			{
				try
				{
					searchResults = await services.domainSearchService.searchDomains({
						query: q as string,
						category: category as string,
						country: country as string,
						technology: technology as string,
						page: parseInt(page as string, 10),
						limit: parseInt(limit as string, 10),
					});
				}
				catch (error)
				{
					logger.error({ error }, 'Search failed');
					// Continue with null results
				}
			}

			const pageData = ReactRenderer.renderPage({
				component: 'SearchPage',
				props: {
					initialData: {
						query: q as string || '',
						results: searchResults?.domains || [],
						facets: searchResults?.facets || {},
						pagination: searchResults?.pagination || {},
						filters: searchResults?.filters || {},
						took: searchResults?.took || 0,
					},
				},
			});

			return res.render('layout', pageData);
		}
		catch (error)
		{
			logger.error({ error }, 'Search page render error');
			return res.status(500).send('Error rendering search page');
		}
	});

	// Domain view page (new enhanced version)
	app.get('/domain/:domain', async (req: Request, res: Response) =>
	{
		try
		{
			const { domain } = req.params;

			let analysis = null;
			if (services.domainAnalysisService)
			{
				try
				{
					analysis = await services.domainAnalysisService.getDomainAnalysis(domain);
				}
				catch (error)
				{
					logger.error({ error }, 'Domain analysis failed');
					// Continue with null analysis
				}
			}

			if (!analysis)
			{
				// Render a styled 404 error page instead of plain text
				return res.status(404).render('error', {
					title: 'Domain Not Found',
					status: 404,
					message: `We could not find analysis for "${domain}". It may not exist or hasn't been analyzed yet.`,
				});
			}

			const pageData = ReactRenderer.renderPage({
				component: 'DomainViewPage',
				props: {
					domain,
					initialData: {
						analysis,
					},
				},
			});

			return res.render('layout', pageData);
		}
		catch (error)
		{
			logger.error({ error }, 'Domain view page render error');
			return res.status(500).render('error', {
				title: 'Server Error',
				status: 500,
				message: 'Error rendering domain view',
				error,
			});
		}
	});

	// Domain analysis page (legacy route, redirect to new view)
	app.get('/analysis/:domain', async (req: Request, res: Response) =>
	{
		const { domain } = req.params;
		return res.redirect(`/domain/${encodeURIComponent(domain)}`);
	});

		// Top Domains page
		app.get('/top-domains', async (req: Request, res: Response) =>
		{
			try
			{
				const { category, country, timeframe = '7d', page = '1', limit = '50' } = req.query;

				let categories: any = null;
				let topDomains: any = null;
				if (services.topDomainsService)
				{
					try
					{
						categories = await services.topDomainsService.getAvailableCategories();
						topDomains = await services.topDomainsService.getTopDomains({
							category: category as string,
							country: country as string,
							limit: parseInt(limit as string, 10),
							offset: (parseInt(page as string, 10) - 1) * parseInt(limit as string, 10),
							timeframe: timeframe as string,
						});
					}
					catch (error)
					{
						logger.error({ error }, 'Top domains fetch failed');
					}
				}

				const pageData = ReactRenderer.renderPage({
					component: 'TopDomainsPage',
					props: {
						initialData: {
							topDomains: topDomains || null,
							categories: categories?.categories || [],
							selectedCategory: (category as string) || '',
							selectedCountry: (country as string) || '',
							selectedTimeframe: (timeframe as string) || '7d',
						},
					},
				});

				return res.render('layout', pageData);
			}
			catch (error)
			{
				logger.error({ error }, 'Top domains page render error');
				return res.status(500).send('Error rendering top domains page');
			}
		});


	// Rankings page (legacy) -> redirect to Top Domains
	app.get('/rankings', async (req: Request, res: Response) =>
	{
		const q = req.url.includes('?') ? req.url.substring(req.url.indexOf('?')) : '';
		return res.redirect(`/top-domains${q}`);
	});

	// About page
	app.get('/about', (req: Request, res: Response) =>
	{
		try
		{
			const pageData = ReactRenderer.renderPage({
				component: 'AboutPage',
				props: {},
			});

			return res.render('layout', pageData);
		}
		catch (error)
		{
			logger.error({ error }, 'About page render error');
			return res.status(500).send('Error rendering about page');
		}
	});

	// Privacy policy page
	app.get('/privacy', (req: Request, res: Response) =>
	{
		try
		{
			const pageData = ReactRenderer.renderPage({
				component: 'PrivacyPage',
				props: {},
			});

			return res.render('layout', pageData);
		}
		catch (error)
		{
			logger.error({ error }, 'Privacy page render error');
			return res.status(500).send('Error rendering privacy page');
		}
	});

	// Terms of service page
	app.get('/terms', (req: Request, res: Response) =>
	{
		try
		{
			const pageData = ReactRenderer.renderPage({
				component: 'TermsPage',
				props: {},
			});

			return res.render('layout', pageData);
		}
		catch (error)
		{
			logger.error({ error }, 'Terms page render error');
			return res.status(500).send('Error rendering terms page');
		}
	});

}
