import type { Application } from 'ultimate-express';
import type { LoggerInstanceType } from '@shared';
import type DomainSearchService from '../../../services/DomainSearchService';
import type DomainAnalysisService from '../../../services/DomainAnalysisService';
import type TopDomainsService from '../../../services/TopDomainsService';
import { setupSearchRoutes } from './search';
import { setupDomainRoutes } from './domains';
import { setupRankingsRoutes } from './rankings';
import { setupAdminRoutes } from './admin';

interface Services {
	domainSearchService: DomainSearchService | null;
	domainAnalysisService: DomainAnalysisService | null;
	topDomainsService: TopDomainsService | null;
}

/**
 * Setup all API routes
 */
export function setupApiRoutes(app: Application, services: Services, logger: LoggerInstanceType): void
{
	// Setup search-related routes
	setupSearchRoutes(app, services.domainSearchService, logger);

	// Setup domain-specific routes
	setupDomainRoutes(app, services.domainAnalysisService, services.topDomainsService, logger);

	// Setup rankings routes
	setupRankingsRoutes(app, services.topDomainsService, logger);

	// Setup admin routes
	setupAdminRoutes(app, services.topDomainsService, logger);
}
