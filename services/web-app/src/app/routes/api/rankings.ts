import type { Application, Request, Response } from 'ultimate-express';
import type { LoggerInstanceType } from '@shared';
import type TopDomainsService from '../../../services/TopDomainsService';

/**
 * Setup rankings-related API routes
 */
export function setupRankingsRoutes(
	app: Application,
	topDomainsService: TopDomainsService | null,
	logger: LoggerInstanceType
): void
{
	// Global rankings API
	app.get('/api/rankings/global', async (req: Request, res: Response) =>
	{
		try
		{
			const { page = 1, limit = 50, sort } = req.query;

			if (!topDomainsService)
			{
				return res.status(503).json({
					error: 'Service unavailable',
					message: 'Top domains service not initialized',
				});
			}

			const rankings = await topDomainsService.getGlobalRankings({
				page: parseInt(page as string, 10),
				limit: parseInt(limit as string, 10),
				sort: sort as string,
			});

			res.json(rankings);
		}
		catch (error)
		{
			logger.error({ error }, 'Global rankings error');
			res.status(500).json({
				error: 'Failed to get global rankings',
				message: (error as Error).message,
			});
		}
	});

	// Category rankings API
	app.get('/api/rankings/category/:category', async (req: Request, res: Response) =>
	{
		try
		{
			const { category } = req.params;
			const { page = 1, limit = 50 } = req.query;

			if (!topDomainsService)
			{
				return res.status(503).json({
					error: 'Service unavailable',
					message: 'Top domains service not initialized',
				});
			}

			const rankings = await topDomainsService.getCategoryRankings({
				category,
				page: parseInt(page as string, 10),
				limit: parseInt(limit as string, 10),
			});

			res.json(rankings);
		}
		catch (error)
		{
			logger.error({ error }, 'Category rankings error');
			res.status(500).json({
				error: 'Failed to get category rankings',
				message: (error as Error).message,
			});
		}
	});

	// Available categories API
	app.get('/api/categories', async (req: Request, res: Response) =>
	{
		try
		{
			if (!topDomainsService)
			{
				return res.status(503).json({
					error: 'Service unavailable',
					message: 'Top domains service not initialized',
				});
			}

			const categories = await topDomainsService.getAvailableCategories();
			res.json(categories);
		}
		catch (error)
		{
			logger.error({ error }, 'Categories error');
			res.status(500).json({
				error: 'Failed to get categories',
				message: (error as Error).message,
			});
		}
	});
}
