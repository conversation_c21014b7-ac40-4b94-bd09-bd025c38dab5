import type { Application, Request, Response } from 'ultimate-express';
import type { LoggerInstanceType } from '@shared';
import type DomainSearchService from '../../../services/DomainSearchService';

/**
 * Setup search-related API routes
 */
export function setupSearchRoutes(
	app: Application,
	domainSearchService: DomainSearchService | null,
	logger: LoggerInstanceType
): void
{
	// Domain search API with Manticore integration
	app.get('/api/domains/search', async (req: Request, res: Response) =>
	{
		try
		{
			const {
				q,
				category,
				country,
				technology,
				ssl_grade: sslGrade,
				min_rank: minRank,
				max_rank: maxRank,
				min_score: minScore,
				max_score: maxScore,
				sort,
				page = 1,
				limit = 20,
			} = req.query;

			if (!domainSearchService)
			{
				return res.status(503).json({
					error: 'Service unavailable',
					message: 'Domain search service not initialized',
				});
			}

			const searchResults = await domainSearchService.searchDomains({
				query: q as string,
				category: category as string,
				country: country as string,
				technology: technology as string,
				sslGrade: sslGrade as string,
				minRank: minRank ? parseInt(minRank as string, 10) : undefined,
				maxRank: maxRank ? parseInt(maxRank as string, 10) : undefined,
				minScore: minScore ? parseFloat(minScore as string) : undefined,
				maxScore: maxScore ? parseFloat(maxScore as string) : undefined,
				sort: sort as string,
				page: parseInt(page as string, 10),
				limit: parseInt(limit as string, 10),
			});

			res.json(searchResults);
		}
		catch (error)
		{
			logger.error({ error }, 'Domain search error');
			return res.status(500).json({
				error: 'Search failed',
				message: (error as Error).message,
			});
		}
	});

	// Search suggestions API
	app.get('/api/domains/search/suggestions', async (req: Request, res: Response) =>
	{
		try
		{
			const { q, limit = 10 } = req.query;

			if (!q || typeof q !== 'string')
			{
				return res.status(400).json({
					error: 'Invalid request',
					message: 'Query parameter "q" is required',
				});
			}

			if (!domainSearchService)
			{
				return res.status(503).json({
					error: 'Service unavailable',
					message: 'Domain search service not initialized',
				});
			}

			const suggestions = await domainSearchService.getSearchSuggestions(
				q,
				parseInt(limit as string, 10),
			);

			res.json({ suggestions });
		}
		catch (error)
		{
			logger.error({ error }, 'Search suggestions error');
			res.status(500).json({
				error: 'Search suggestions failed',
				message: (error as Error).message,
			});
		}
	});

	// Popular searches API
	app.get('/api/domains/search/popular', async (req: Request, res: Response) =>
	{
		try
		{
			if (!domainSearchService)
			{
				return res.status(503).json({
					error: 'Service unavailable',
					message: 'Domain search service not initialized',
				});
			}

			const popularSearches = await domainSearchService.getPopularSearches();
			res.json(popularSearches);
		}
		catch (error)
		{
			logger.error({ error }, 'Popular searches error');
			res.status(500).json({
				error: 'Popular searches failed',
				message: (error as Error).message,
			});
		}
	});

	// Search statistics API
	app.get('/api/domains/search/stats', async (req: Request, res: Response) =>
	{
		try
		{
			if (!domainSearchService)
			{
				return res.status(503).json({
					error: 'Service unavailable',
					message: 'Domain search service not initialized',
				});
			}

			const stats = await domainSearchService.getSearchStats();
			res.json(stats);
		}
		catch (error)
		{
			logger.error({ error }, 'Search stats error');
			res.status(500).json({
				error: 'Search stats failed',
				message: (error as Error).message,
			});
		}
	});
}
