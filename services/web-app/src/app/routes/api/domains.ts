import type { Application, Request, Response } from 'ultimate-express';
import type { LoggerInstanceType } from '@shared';
import { CacheManager, RedisClientWrapper } from '@shared';
import type DomainAnalysisService from '../../../services/DomainAnalysisService';
import type TopDomainsService from '../../../services/TopDomainsService';

/**
 * Setup domain-specific API routes
 */
export function setupDomainRoutes(
	app: Application,
	domainAnalysisService: DomainAnalysisService | null,
	topDomainsService: TopDomainsService | null,
	logger: LoggerInstanceType
): void 
{
	// Domain analysis API
	app.get('/api/domains/:domain/analysis', async (req: Request, res: Response) => 
	{
		try 
		{
			const { domain } = req.params;

			if (!domainAnalysisService) 
			{
				return res.status(503).json({
					error: 'Service unavailable',
					message: 'Domain analysis service not initialized',
				});
			}

			const analysis = await domainAnalysisService.getDomainAnalysis(domain);

			if (!analysis) 
			{
				return res.status(404).json({
					error: 'Domain not found',
					message: `Analysis for domain ${domain} not found`,
				});
			}

			return res.json(analysis);
		}
		catch (error)
		{
			logger.error({ error }, 'Domain analysis error');
			return res.status(500).json({
				error: 'Analysis failed',
				message: (error as Error).message,
			});
		}
	});

	// Domain ranking explanation API
	app.get('/api/domains/:domain/ranking', async (req: Request, res: Response) => 
	{
		try 
		{
			const { domain } = req.params;

			if (!domainAnalysisService) 
			{
				return res.status(503).json({
					error: 'Service unavailable',
					message: 'Domain analysis service not initialized',
				});
			}

			const explanation = await domainAnalysisService.getDomainRankingExplanation(domain);

			if (!explanation) 
			{
				return res.status(404).json({
					error: 'Domain not found',
					message: `Ranking explanation for domain ${domain} not found`,
				});
			}

			return res.json(explanation);
		}
		catch (error) 
		{
			logger.error({ error }, 'Ranking explanation error');
			return res.status(500).json({
				error: 'Ranking explanation failed',
				message: (error as Error).message,
			});
		}
	});

	// Top domains API
	app.get('/api/domains/top', async (req: Request, res: Response) => 
	{
		try 
		{
			const {
				category,
				country,
				limit = 50,
				offset = 0,
				timeframe,
			} = req.query;

			if (!topDomainsService) 
			{
				return res.status(503).json({
					error: 'Service unavailable',
					message: 'Top domains service not initialized',
				});
			}

			const topDomains = await topDomainsService.getTopDomains({
				category: category as string,
				country: country as string,
				limit: parseInt(limit as string, 10),
				offset: parseInt(offset as string, 10),
				timeframe: timeframe as string,
			});

			return res.json(topDomains);
		}
		catch (error) 
		{
			logger.error({ error }, 'Top domains error');
			return res.status(500).json({
				error: 'Failed to get top domains',
				message: (error as Error).message,
			});
		}
	});


	// Trending domains API
	app.get('/api/domains/trending', async (req: Request, res: Response) =>
	{
		try
		{
			const { timeframe = '7d', limit = 20 } = req.query;

			if (!topDomainsService)
			{
				return res.status(503).json({
					error: 'Service unavailable',
					message: 'Top domains service not initialized',
				});
			}

			const trending = await topDomainsService.getTrendingDomains({
				timeframe: timeframe as string,
				limit: parseInt(limit as string, 10),
			});

			return res.json(trending);
		}
		catch (error)
		{
			logger.error({ error }, 'Trending domains error');
			return res.status(500).json({
				error: 'Failed to get trending domains',
				message: (error as Error).message,
			});
		}
	});

	// Cache warming API
	app.post('/api/domains/cache/warm', async (req: Request, res: Response) =>
	{
		try
		{
			const { domains, priority = 'medium', batchSize = 10 } = req.body;

			if (!domains || !Array.isArray(domains) || domains.length === 0)
			{
				return res.status(400).json({
					error: 'Invalid request',
					message: 'Domains array is required and cannot be empty',
				});
			}

			if (!domainAnalysisService)
			{
				return res.status(503).json({
					error: 'Service unavailable',
					message: 'Domain analysis service not initialized',
				});
			}

			// Initialize cache manager
			const redisClient = new RedisClientWrapper();
			const cacheManager = new CacheManager(redisClient);

			// Process domains in batches to avoid overwhelming the system
			const results: {
                total: number;
                processed: number;
                cached: number;
                errors: string[];
                startTime: string;
                endTime?: string;
            } = {
                total: domains.length,
                processed: 0,
                cached: 0,
                errors: [] as string[],
                startTime: new Date().toISOString(),
            };

			const processBatch = async (batch: string[]) => {
				const promises = batch.map(async (domain: string) => {
					try {
						// Check if already cached and fresh
						const cached = await cacheManager.getCachedDomainAnalysis(domain);
						if (cached) {
							logger.info({ domain }, 'Domain already cached, skipping');
							return { domain, action: 'skipped', reason: 'already_cached' };
						}

						// Warm cache by triggering analysis
						const analysis = await domainAnalysisService.getDomainAnalysis(domain);

						if (analysis) {
							// Store in cache with appropriate TTL based on priority
							const ttl = priority === 'high' ? 86400 : priority === 'medium' ? 43200 : 21600; // 24h, 12h, 6h
							await cacheManager.storeDomainAnalysis(domain, analysis, ttl);

							results.cached++;
							logger.info({ domain, priority, ttl }, 'Domain cache warmed successfully');
							return { domain, action: 'cached', ttl };
						} else {
							results.errors.push(`Failed to analyze domain: ${domain}`);
							return { domain, action: 'failed', reason: 'no_analysis' };
						}
					} catch (error) {
						const errorMsg = `Error warming cache for ${domain}: ${(error as Error).message}`;
						results.errors.push(errorMsg);
						logger.error({ domain, error }, 'Cache warming error');
						return { domain, action: 'error', error: (error as Error).message };
					} finally {
						results.processed++;
					}
				});

				return Promise.all(promises);
			};

			// Process domains in batches
			const batches = [];
			for (let i = 0; i < domains.length; i += parseInt(batchSize as string, 10)) {
				batches.push(domains.slice(i, i + parseInt(batchSize as string, 10)));
			}

			const batchResults = [];
			for (const batch of batches) {
				const batchResult = await processBatch(batch);
				batchResults.push(...batchResult);
			}

			results.endTime = new Date().toISOString();
			const duration = new Date(results.endTime).getTime() - new Date(results.startTime).getTime();

			logger.info({
				total: results.total,
				processed: results.processed,
				cached: results.cached,
				errors: results.errors.length,
				duration,
			}, 'Cache warming completed');

			return res.json({
				success: true,
				message: 'Cache warming completed',
				results: {
					...results,
					duration: `${duration}ms`,
					details: batchResults,
				},
			});
		}
		catch (error)
		{
			logger.error({ error }, 'Cache warming operation error');
			return res.status(500).json({
				error: 'Cache warming failed',
				message: (error as Error).message,
			});
		}
	});

	// Cache invalidation API
	app.delete('/api/domains/cache/:domain', async (req: Request, res: Response) =>
	{
		try
		{
			const { domain } = req.params;
			const { pattern } = req.query;

			// Initialize cache manager
			const redisClient = new RedisClientWrapper();
			const cacheManager = new CacheManager(redisClient);

			if (pattern === 'wildcard')
			{
				// Invalidate all cache entries matching domain pattern (e.g., *.example.com)
				const invalidated = await cacheManager.invalidatePattern(`domain:analysis:${domain}*`);
				logger.info({ domain, invalidated }, 'Cache invalidated by pattern');

				return res.json({
					success: true,
					message: `Cache invalidated for pattern: ${domain}*`,
					invalidated,
				});
			}
			else
			{
				// Invalidate specific domain cache
				const invalidated = await cacheManager.invalidateDomainAnalysis(domain);
				logger.info({ domain, invalidated }, 'Domain cache invalidated');

				return res.json({
					success: true,
					message: `Cache invalidated for domain: ${domain}`,
					invalidated,
				});
			}
		}
		catch (error)
		{
			logger.error({ error }, 'Cache invalidation error');
			return res.status(500).json({
				error: 'Cache invalidation failed',
				message: (error as Error).message,
			});
		}
	});

	// Bulk cache invalidation API
	app.post('/api/domains/cache/invalidate', async (req: Request, res: Response) =>
	{
		try
		{
			const { domains, patterns } = req.body;

			if ((!domains || !Array.isArray(domains) || domains.length === 0) &&
				(!patterns || !Array.isArray(patterns) || patterns.length === 0))
			{
				return res.status(400).json({
					error: 'Invalid request',
					message: 'Either domains array or patterns array is required',
				});
			}

			// Initialize cache manager
			const redisClient = new RedisClientWrapper();
			const cacheManager = new CacheManager(redisClient);

			const results = {
				domains: [] as Array<{ domain: string; invalidated: boolean; error?: string }>,
				patterns: [] as Array<{ pattern: string; invalidated: number; error?: string }>,
				total: 0,
			};

			// Invalidate specific domains
			if (domains && Array.isArray(domains))
			{
				for (const domain of domains)
				{
					try
					{
						const invalidated = await cacheManager.invalidateDomainAnalysis(domain);
						results.domains.push({ domain, invalidated });
						if (invalidated) results.total++;
					}
					catch (error)
					{
						results.domains.push({
							domain,
							invalidated: false,
							error: (error as Error).message,
						});
					}
				}
			}

			// Invalidate by patterns
			if (patterns && Array.isArray(patterns))
			{
				for (const pattern of patterns)
				{
					try
					{
						const invalidated = await cacheManager.invalidatePattern(`domain:analysis:${pattern}`);
						results.patterns.push({ pattern, invalidated });
						results.total += invalidated;
					}
					catch (error)
					{
						results.patterns.push({
							pattern,
							invalidated: 0,
							error: (error as Error).message,
						});
					}
				}
			}

			logger.info(results, 'Bulk cache invalidation completed');

			return res.json({
				success: true,
				message: 'Bulk cache invalidation completed',
				results,
			});
		}
		catch (error)
		{
			logger.error({ error }, 'Bulk cache invalidation error');
			return res.status(500).json({
				error: 'Bulk cache invalidation failed',
				message: (error as Error).message,
			});
		}
	});

	// Cache statistics API
	app.get('/api/domains/cache/stats', async (req: Request, res: Response) =>
	{
		try
		{
			// Initialize cache manager
			const redisClient = new RedisClientWrapper();
			const cacheManager = new CacheManager(redisClient);

			const stats = await cacheManager.getCacheStats('domain:analysis:*');

			return res.json({
				success: true,
				stats,
			});
		}
		catch (error)
		{
			logger.error({ error }, 'Cache stats error');
			return res.status(500).json({
				error: 'Failed to get cache stats',
				message: (error as Error).message,
			});
		}
	});
}
