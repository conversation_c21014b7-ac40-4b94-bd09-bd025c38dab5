import type { Application, Request, Response } from 'ultimate-express';
import type { LoggerInstanceType } from '@shared';
import type TopDomainsService from '../../../services/TopDomainsService';

/**
 * Setup admin-related API routes
 */
export function setupAdminRoutes(
	app: Application,
	topDomainsService: TopDomainsService | null,
	logger: LoggerInstanceType
): void 
{
	// Cache invalidation API (for admin use)
	app.post('/api/cache/invalidate', async (req: Request, res: Response) => 
	{
		try 
		{
			const { category } = req.body;

			if (!topDomainsService) 
			{
				return res.status(503).json({
					error: 'Service unavailable',
					message: 'Top domains service not initialized',
				});
			}

			await topDomainsService.invalidateCache(category);

			res.json({
				success: true,
				message: category ? `Cache invalidated for category: ${category}` : 'All cache invalidated',
			});
		}
		catch (error)
		{
			logger.error({ error }, 'Cache invalidation error');
			return res.status(500).json({
				error: 'Cache invalidation failed',
				message: (error as Error).message,
			});
		}
	});
}
