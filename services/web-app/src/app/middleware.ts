import path from 'node:path';
import express from 'ultimate-express';
import type { Application, Request, Response, NextFunction } from 'ultimate-express';
import { logger, config } from '@shared';
import type { LoggerInstanceType } from '@shared';

// Use process.cwd() for path resolution
const projectRoot = process.cwd();

/**
 * Setup express middleware
 */
export function setupMiddleware(app: Application): void 
{
	// Set up EJS template engine
	app.set('view engine', 'ejs');
	app.set('views', path.join(projectRoot, 'src', 'views'));

	// Request logging
	app.use(logger.createRequestLogger('WebApp'));

	// Basic middleware
	app.use(express.json({ limit: '10mb' }));
	app.use(express.urlencoded({ extended: true }));

	// Static files (for CSS, JS, images)
	app.use('/static', express.static(path.join(projectRoot, 'public')));

	// CORS
	app.use((req: Request, res: Response, next: NextFunction) => 
	{
		res.header('Access-Control-Allow-Origin', config.get('CORS_ORIGIN', '*'));
		res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
		res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
		next();
	});
}
