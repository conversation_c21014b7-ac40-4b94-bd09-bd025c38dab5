import type { Application, Request, Response, NextFunction } from 'ultimate-express';
import { BaseError<PERSON>and<PERSON>, logger as sharedLogger, IdGenerator } from '@shared';
import type { ErrorHandlingContextType, ErrorClassificationResultType, LoggerInstanceType } from '@shared';

interface ErrorWithStatus extends Error {
	status?: number;
	statusCode?: number;
}

/**
 * Web App specific error handler extending the shared BaseErrorHandler
 */
class WebAppErrorHandler extends BaseErrorHandler 
{
	constructor(loggerInstance: LoggerInstanceType) 
	{
		super(loggerInstance, {
			classification: {
				enabled: true,
				confidenceThreshold: 0.7,
			},
			retry: {
				enabled: true,
				defaultStrategy: {
					maxAttempts: 3,
					baseDelay: 1000,
					maxDelay: 30000,
					backoffMultiplier: 2,
					jitterEnabled: true,
				},
			},
			circuitBreaker: {
				enabled: true,
				defaultConfig: {
					failureThreshold: 5,
					timeout: 60000,
					volumeThreshold: 10,
				},
			},
		});
	}

	protected async reportError(
		error: Error,
		classification: ErrorClassificationResultType,
		context: ErrorHandlingContextType
	): Promise<string | undefined> 
	{
		// Log the error with classification details
		this.logger.error({
			error: {
				message: error.message,
				stack: error.stack,
				name: error.name,
			},
			classification: classification.classification,
			context,
		}, 'Web App error reported');

		// Generate a simple error ID for tracking
		return IdGenerator.webAppId();
	}

	protected async handleServiceSpecificError(
		error: Error,
		classification: ErrorClassificationResultType,
		context: ErrorHandlingContextType
	): Promise<{ actions: string[]; recoveryAttempted?: boolean; degradationTriggered?: boolean } | null> 
	{
		const actions: string[] = [];
		let recoveryAttempted = false;
		let degradationTriggered = false;

		// Handle specific error types for the web app
		if (error.message.includes('Database')) 
		{
			actions.push('Database error detected');
			
			// For database errors, we might want to serve from cache
			if (classification.classification.severity === 'high') 
			{
				degradationTriggered = true;
				actions.push('Switched to cache-only mode');
			}
		}
		else if (error.message.includes('Authentication')) 
		{
			actions.push('Authentication error detected');
			recoveryAttempted = true;
			actions.push('Attempted token refresh');
		}
		else if (error.message.includes('Rate limit')) 
		{
			actions.push('Rate limit error detected');
			degradationTriggered = true;
			actions.push('Enabled request throttling');
		}

		return {
			actions,
			recoveryAttempted,
			degradationTriggered,
		};
	}

	protected onShutdown(): void 
	{
		this.logger.info({}, 'Web App error handler shutting down');
		// Clean up any web app specific resources
	}
}

/**
 * Setup error handling middleware for Express
 */
export function setupErrorHandling(app: Application, loggerInstance: LoggerInstanceType): void
{
	// Create the error handler instance
	const errorHandler = new WebAppErrorHandler(loggerInstance);

	// Get logger instance for this module
	const logger = sharedLogger.getLogger('ErrorHandling');

	// 404 handler
	app.use((req: Request, res: Response) =>
	{
		logger.warn({ path: req.path, method: req.method }, 'Route not found');
		
		// Check if this is an API request
		if (req.path.startsWith('/api/')) 
		{
			return res.status(404).json({
				error: 'Not found',
				message: `Route ${req.path} not found`,
				status: 404,
			});
		}

		// Render 404 page for web requests
		return res.status(404).render('error', {
			title: 'Page Not Found',
			message: 'The page you are looking for does not exist.',
			status: 404,
		});
	});

	// Global error handler
	app.use(async (err: ErrorWithStatus, req: Request, res: Response, next: NextFunction) => 
	{
		const status = err.status || err.statusCode || 500;
		const isProd = process.env.NODE_ENV === 'production';
		// For rendered pages, force 500 in production; API keeps original status
		const pageStatus = isProd ? 500 : status;
		
		// Handle the error using our error handler
		const context: ErrorHandlingContextType = {
			operationName: `${req.method} ${req.path}`,
			operationId: `req-${Date.now()}`,
			metadata: {
				method: req.method,
				path: req.path,
				status,
				headers: req.headers,
				query: req.query,
				body: req.body,
			},
		};

		const handlingResult = await errorHandler.handleError(err, context);

		// Log the handling result
		logger.error({
			handlingResult,
			path: req.path,
			method: req.method,
			status,
		}, 'Request error handled');

		// Don't leak error details in production
		const message = isProd
			? 'Internal server error'
			: err.message;

		// Check if this is an API request
		if (req.path.startsWith('/api/')) 
		{
			return res.status(status).json({
				error: 'Request failed',
				message,
				status,
				errorId: handlingResult.reportId,
			});
		}

		// Render error page for web requests
		return res.status(pageStatus).render('error', {
			title: 'Error',
			message,
			status: pageStatus,
			errorId: handlingResult.reportId,
			error: isProd ? undefined : err,
			showStack: !isProd,
		});
	});

	// Export the error handler for use in other parts of the app
	(app as any).errorHandler = errorHandler;
}
