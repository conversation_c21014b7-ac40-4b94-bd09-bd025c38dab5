import { Server } from 'node:http';
import express from 'ultimate-express';
import type { Application } from 'ultimate-express';
import { DatabaseManager, logger as sharedLogger, config } from '@shared';
import DomainAnalysisService from '../services/DomainAnalysisService';
import DomainSearchService from '../services/DomainSearchService';
import TopDomainsService from '../services/TopDomainsService';
import { setupMiddleware } from './middleware';
import { setupWebRoutes } from './routes/web';
import { setupApiRoutes } from './routes/api';
import { setupErrorHandling } from './errorHandling';

const logger = sharedLogger.getLogger('WebApp');
const appConfig = config;

/**
 * Web Application Service
 * Provides domain search, analysis, and ranking interfaces
 */
export class WebAppService 
{
	private app: Application;
	private server: Server | null;
	private dbManager: DatabaseManager;
	private domainSearchService: DomainSearchService | null = null;
	private domainAnalysisService: DomainAnalysisService | null = null;
	private topDomainsService: TopDomainsService | null = null;

	constructor() 
	{
		this.app = express();
		this.server = null;
		this.dbManager = new DatabaseManager();
	}

	/**
	 * Initialize the web application service
	 */
	async initialize(): Promise<void> 
	{
		try 
		{
			logger.info({ phase: 'startup' }, 'Initializing Web Application Service...');

			// Validate configuration
			appConfig.validate();

			// Initialize database connections
			try 
			{
				await this.dbManager.initialize();
				this.domainSearchService = new DomainSearchService(this.dbManager);
				this.domainAnalysisService = new DomainAnalysisService(this.dbManager);
				this.topDomainsService = new TopDomainsService(this.dbManager);
				logger.info({ phase: 'startup' }, 'Database connections initialized successfully');
			}
			catch (error) 
			{
				logger.warn({ error, phase: 'startup' }, 'Database initialization failed, running in development mode');
				// Continue without database connections for development
			}

			// Setup middleware
			setupMiddleware(this.app);

			// Setup routes
			this.setupRoutes();

			// Setup error handling
			setupErrorHandling(this.app, logger);

			logger.info({ phase: 'startup' }, 'Web Application Service initialized successfully');
		}
		catch (error) 
		{
			logger.error({ error, phase: 'startup' }, 'Failed to initialize Web Application Service');
			throw error;
		}
	}

	/**
	 * Setup application routes
	 */
	private setupRoutes(): void 
	{
		// Health check endpoint
		this.app.get('/health', async (req, res) => 
		{
			try 
			{
				let databases = {
					scylla: false,
					maria: false,
					redis: false,
					manticore: false,
				};
				let message = 'Web server running';

				if (this.dbManager) 
				{
					try 
					{
						databases = await this.dbManager.healthCheck();
						message = 'Web server running with database connections';
					}
					catch (healthError) 
					{
						logger.warn({ error: healthError }, 'Database health check failed');
						message = 'Web server running, database health check failed';
					}
				}
				else 
				{
					message = 'Web server running, databases not connected';
				}

				res.json({
					status: 'ok',
					service: 'web-app',
					timestamp: new Date().toISOString(),
					databases,
					mode: config.isDevelopment() ? 'development' : 'production',
					message,
				});
			}
			catch (error) 
			{
				res.status(500).json({
					status: 'error',
					service: 'web-app',
					error: (error as Error).message,
				});
			}
		});

		// Web page routes with React SSR
		setupWebRoutes(this.app, {
			domainSearchService: this.domainSearchService,
			domainAnalysisService: this.domainAnalysisService,
			topDomainsService: this.topDomainsService,
		}, logger);

		// API routes
		setupApiRoutes(this.app, {
			domainSearchService: this.domainSearchService,
			domainAnalysisService: this.domainAnalysisService,
			topDomainsService: this.topDomainsService,
		}, logger);

		// Description API
		this.app.get('/api/domains/:domain/description', async (req, res) => 
		{
			try 
			{
				const { default: descriptionRouter } = await import('../routes/description');
				return descriptionRouter.handle(req, res);
			}
			catch (error) 
			{
				logger.error({ error }, 'Description route failed to load');
				return res.status(500).json({ error: 'Route load failed' });
			}
		});
	}

	/**
	 * Start the web server
	 */
	async start(): Promise<void> 
	{
		const port = appConfig.get('PORT', 3000);
		const host = appConfig.get('HOST', '0.0.0.0');

		return new Promise((resolve, reject) => 
		{
			this.server = this.app.listen(port, host, () => 
			{
				logger.info({ port, host }, `Web server listening on http://${host}:${port}`);
				resolve();
			});

			this.server.on('error', (error: NodeJS.ErrnoException) => 
			{
				if (error.code === 'EADDRINUSE') 
				{
					logger.error({ port }, `Port ${port} is already in use`);
				}
				else 
				{
					logger.error({ error }, 'Server error');
				}
				reject(error);
			});
		});
	}

	/**
	 * Stop the web server
	 */
	async stop(): Promise<void> 
	{
		return new Promise((resolve) => 
		{
			if (this.server) 
			{
				this.server.close(() => 
				{
					logger.info({}, 'Web server stopped');
					resolve();
				});
			}
			else 
			{
				resolve();
			}
		});
	}

	/**
	 * Shutdown the service gracefully
	 */
	async shutdown(): Promise<void> 
	{
		logger.info({}, 'Shutting down Web Application Service...');
		await this.stop();

		if (this.dbManager) 
		{
			await this.dbManager.shutdown();
		}

		logger.info({}, 'Web Application Service shutdown complete');
	}

	/**
	 * Get the Express application instance
	 */
	getApp(): Application 
	{
		return this.app;
	}
}
