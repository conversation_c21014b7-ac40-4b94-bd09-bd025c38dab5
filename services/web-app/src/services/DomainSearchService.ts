import { DatabaseManager, logger, ManticoreClient } from '@shared';

type SearchResultItemType =
{
	domain: string;
	globalRank?: number;
	category?: string;
	scores?: {
		overall?: number;
		performance?: number;
		security?: number;
		seo?: number;
		technical?: number;
	};
	trafficEstimate?: number;
};

type FacetBucketType = { value: string; count: number };

/**
 * Domain Search Service
 * Handles domain search operations with Manticore integration
 */
class DomainSearchService
{
	private dbManager: DatabaseManager;

	private manticoreClient: ManticoreClient;

	private logger = logger.getLogger('DomainSearchService');

	constructor(dbManager: DatabaseManager)
	{
		this.dbManager = dbManager;
		this.manticoreClient = dbManager.getManticoreClient();
	}

	/**
	 * Search domains with faceted filtering and pagination
	 * Accepts either a query string (with optional filters) or a params object
	 */
	async searchDomains(
		paramsOrQuery: {
			query?: string;
			category?: string;
			country?: string;
			technology?: string;
			sslGrade?: string;
			minRank?: number;
			maxRank?: number;
			minScore?: number;
			maxScore?: number;
			sort?: string;
			page?: number;
			limit?: number;
		} | string,
		potentialFilters?: Partial<{
			query?: string;
			category?: string;
			country?: string;
			technology?: string;
			sslGrade?: string;
			minRank?: number;
			maxRank?: number;
			minScore?: number;
			maxScore?: number;
			sort?: string;
			page?: number;
			limit?: number;
		}>,
	): Promise<{
		domains: any[];
		pagination: {
			page: number;
			limit: number;
			total: number;
			totalPages: number;
			hasNext: boolean;
			hasPrev: boolean;
		};
		facets: Record<string, any>;
		filters: Record<string, any>;
		took: number;
		totalResults: number;
	}>
	{
		try
		{
			// Normalize overloads into params object
			let p: any;
			if (typeof paramsOrQuery === 'string')
			{
				p = { query: paramsOrQuery, ...(potentialFilters || {}) };
			}
			else
			{
				p = paramsOrQuery as any;
			}

			const page = Math.max(1, p.page || 1);
			const limit = Math.min(100, Math.max(1, p.limit || 20));
			const offset = (page - 1) * limit;

			// Build filters
			const filters: Record<string, any> = {};
			if (p.category) filters.category = p.category;
			if (p.country) filters.country = p.country;
			if (p.technology) filters.technologies = p.technology;
			if (p.sslGrade) filters.ssl_grade = p.sslGrade;
			if (p.minRank) filters.min_rank = p.minRank;
			if (p.maxRank) filters.max_rank = p.maxRank;
			if (p.minScore) filters.min_score = p.minScore;
			if (p.maxScore) filters.max_score = p.maxScore;

			// Execute search
			const searchResult = await this.manticoreClient.searchDomains({
				query: p.query,
				filters,
				sort: p.sort || 'score',
				limit,
				offset,
				facets: ['category', 'country', 'technologies', 'ssl_grade'],
			});

			// Calculate pagination
			const totalPages = Math.ceil(searchResult.total / limit);
			const pagination = {
				page,
				limit,
				total: searchResult.total,
				totalPages,
				hasNext: page < totalPages,
				hasPrev: page > 1,
			};

			this.logger.info(`Domain search completed: ${searchResult.results.length} results in ${searchResult.took}ms`);

			return {
				domains: searchResult.results,
				pagination,
				facets: searchResult.facets,
				filters,
				took: searchResult.took,
				totalResults: searchResult.total,
			};
		}
		catch (error)
		{
			this.logger.error('Domain search failed:', error);
			throw error;
		}
	}

	/**
	 * Get top domains helper to satisfy tests
	 */
	async getTopDomains(category?: string, options?: { limit?: number; offset?: number }): Promise<any>
	{
		const res = await this.manticoreClient.getTopDomains({
			category,
			limit: options?.limit || 50,
			offset: options?.offset || 0,
		});
		return {
			domains: res.results,
			totalResults: res.total,
		} as any;
	}


	/**
	 * Get search suggestions based on partial query
	 */
	async getSearchSuggestions(query: string, limit: number = 10): Promise<string[]>
	{
		try
		{
			if (!query || query.length < 2)
			{
				return [];
			}

			// Search for domains that start with the query
			const searchResult = await this.manticoreClient.searchDomains({
				query: `${query}*`,
				limit,
				offset: 0,
			});

			// Extract unique domain suggestions
			const results = searchResult.results as SearchResultItemType[];
			const suggestions = results
				.map((r) => String(r.domain || ''))
				.filter((d, index, array) => d && array.indexOf(d) === index)
				.slice(0, limit);

			return suggestions;
		}
		catch (error)
		{
			this.logger.error('Search suggestions failed:', error);
			return [];
		}
	}

	/**
	 * Get popular search terms and categories
	 */
	async getPopularSearches(): Promise<{
		categories: Array<{ name: string; count: number }>;
		technologies: Array<{ name: string; count: number }>;
		countries: Array<{ name: string; count: number }>;
	}>
	{
		try
		{
			// Get facet data for popular terms
			const searchResult = await this.manticoreClient.searchDomains({
				query: '',
				limit: 1,
				offset: 0,
				facets: ['category', 'technologies', 'country'],
			});

			const catBuckets = (searchResult.facets.category as FacetBucketType[] | undefined) || [];
			const techBuckets = (searchResult.facets.technologies as FacetBucketType[] | undefined) || [];
			const countryBuckets = (searchResult.facets.country as FacetBucketType[] | undefined) || [];

			return {
				categories: catBuckets.map((b) => ({ name: String(b.value), count: Number(b.count || 0) })),
				technologies: techBuckets.map((b) => ({ name: String(b.value), count: Number(b.count || 0) })),
				countries: countryBuckets.map((b) => ({ name: String(b.value), count: Number(b.count || 0) })),
			};
		}
		catch (error)
		{
			this.logger.error('Popular searches failed:', error);
			return {
				categories: [],
				technologies: [],
				countries: [],
			};
		}
	}

	/**
	 * Get search statistics
	 */
	async getSearchStats(): Promise<{
		totalDomains: number;
		totalCategories: number;
		totalCountries: number;
		totalTechnologies: number;
		lastUpdated: string;
	}>
	{
		try
		{
			// Get total count and facet statistics
			const searchResult = await this.manticoreClient.searchDomains({
				query: '',
				limit: 0,
				offset: 0,
				facets: ['category', 'technologies', 'country'],
			});

			const catBuckets = (searchResult.facets.category as FacetBucketType[] | undefined) || [];
			const techBuckets = (searchResult.facets.technologies as FacetBucketType[] | undefined) || [];
			const countryBuckets = (searchResult.facets.country as FacetBucketType[] | undefined) || [];
			return {
				totalDomains: searchResult.total,
				totalCategories: catBuckets.length,
				totalCountries: countryBuckets.length,
				totalTechnologies: techBuckets.length,
				lastUpdated: new Date().toISOString(),
			};
		}
		catch (error)
		{
			this.logger.error('Search stats failed:', error);
			return {
				totalDomains: 0,
				totalCategories: 0,
				totalCountries: 0,
				totalTechnologies: 0,
				lastUpdated: new Date().toISOString(),
			};
		}
	}
}

export { DomainSearchService };

export default DomainSearchService;
