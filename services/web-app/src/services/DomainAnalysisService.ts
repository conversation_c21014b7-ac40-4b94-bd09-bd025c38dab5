import {
	DatabaseManager,
	ScyllaClient,
	MariaClient,
	ManticoreClient,
	DomainHasher,
	logger,
} from '@shared';

// Align with shared ManticoreClient.parseSearchResponse result shape
type SearchResultItemType = {
    domain: string;
    globalRank?: number;
    category?: string;
    scores?: {
        overall?: number;
        performance?: number;
        security?: number;
        seo?: number;
        technical?: number;
    };
    trafficEstimate?: number;
};

/**
 * Domain Analysis Service
 * Handles domain analysis operations with comprehensive metrics
 */
class DomainAnalysisService
{
	private dbManager: DatabaseManager;

	private scyllaClient: ScyllaClient;

	private mariaClient: MariaClient;

	private manticoreClient: ManticoreClient;

	private logger = logger.getLogger('DomainAnalysisService');

	constructor(dbManager: DatabaseManager)
	{
		this.dbManager = dbManager;
		this.scyllaClient = dbManager.getScyllaClient();
		this.mariaClient = dbManager.getMariaClient();
		this.manticoreClient = dbManager.getManticoreClient();
	}

	/**
	 * Get mock domain analysis for preview purposes
	 */
	private getMockDomainAnalysis(domain: string) {
		return {
			domain,
			globalRank: Math.floor(Math.random() * 1000000) + 1,
			categoryRank: Math.floor(Math.random() * 10000) + 1,
			category: ['Technology', 'E-commerce', 'News', 'Social Media', 'Entertainment'][Math.floor(Math.random() * 5)],
			country: 'United States',
			overallScore: 0.75 + Math.random() * 0.25,
			performanceScore: 0.65 + Math.random() * 0.35,
			securityScore: 0.7 + Math.random() * 0.3,
			seoScore: 0.6 + Math.random() * 0.4,
			technicalScore: 0.8 + Math.random() * 0.2,
			trafficEstimate: Math.floor(Math.random() * 10000000) + 100000,
			domainAge: Math.floor(Math.random() * 8000) + 365,
			sslGrade: ['A+', 'A', 'B', 'C'][Math.floor(Math.random() * 4)],
			technologies: ['React', 'Node.js', 'Redis', 'MySQL', 'CloudFlare', 'Google Analytics'],
			lastUpdated: new Date().toISOString(),
			screenshot: `https://via.placeholder.com/1200x800/4285f4/ffffff?text=${encodeURIComponent(domain)}`,
			screenshots: [
				`https://via.placeholder.com/1200x800/4285f4/ffffff?text=${encodeURIComponent(domain)}+Desktop`,
				`https://via.placeholder.com/400x800/34a853/ffffff?text=${encodeURIComponent(domain)}+Mobile`
			],
			favicon: `https://www.google.com/s2/favicons?domain=${domain}&sz=64`,
			subdomains: [`www.${domain}`, `api.${domain}`, `mail.${domain}`, `blog.${domain}`, `shop.${domain}`],
			socialLinks: {
				twitter: `https://twitter.com/${domain.split('.')[0]}`,
				facebook: `https://facebook.com/${domain.split('.')[0]}`,
				linkedin: `https://linkedin.com/company/${domain.split('.')[0]}`
			},
			crawlStatus: 'completed',
			domainInfo: {
				age: Math.floor(Math.random() * 8000) + 365,
				registrar: 'GoDaddy LLC',
				registrationDate: new Date(Date.now() - Math.random() * 8000 * 24 * 60 * 60 * 1000).toISOString(),
				expirationDate: new Date(Date.now() + Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
				country: 'United States'
			},
			metrics: {
				performance: {
					loadTime: Math.floor(Math.random() * 3000) + 500,
					firstContentfulPaint: Math.floor(Math.random() * 2000) + 300,
					largestContentfulPaint: Math.floor(Math.random() * 4000) + 1000,
					cumulativeLayoutShift: Math.random() * 0.25,
					firstInputDelay: Math.floor(Math.random() * 100) + 10,
					speedIndex: Math.floor(Math.random() * 5000) + 1000,
					pageSize: Math.floor(Math.random() * 5000000) + 500000,
					resourceCount: Math.floor(Math.random() * 150) + 50,
					score: 0.65 + Math.random() * 0.35
				},
				security: {
					sslGrade: ['A+', 'A', 'B', 'C'][Math.floor(Math.random() * 4)],
					sslIssuer: 'Let\'s Encrypt',
					sslExpiration: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString(),
					securityHeaders: {
						hsts: Math.random() > 0.3,
						csp: Math.random() > 0.4,
						xFrameOptions: Math.random() > 0.2,
						xContentTypeOptions: Math.random() > 0.1
					},
					vulnerabilities: Math.random() > 0.7 ? ['Outdated TLS version'] : [],
					certificateInfo: {
						issuer: 'Let\'s Encrypt',
						expiration: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString(),
						daysUntilExpiry: 90
					},
					score: 0.7 + Math.random() * 0.3
				},
				seo: {
					title: `${domain.split('.')[0]} - Best ${['Service', 'Platform', 'Solution', 'Tool'][Math.floor(Math.random() * 4)]} Online`,
					description: `Discover the power of ${domain}. Leading platform for innovative solutions and exceptional user experience.`,
					keywords: ['technology', 'innovation', 'platform', 'service'],
					structuredData: Math.random() > 0.4,
					sitemap: Math.random() > 0.3,
					robotsTxt: Math.random() > 0.2,
					hasRobotsTxt: Math.random() > 0.2,
					hasSitemap: Math.random() > 0.3,
					mobileOptimized: Math.random() > 0.1,
					pageSpeed: Math.floor(Math.random() * 100) + 60,
					metaKeywords: 'technology, innovation, platform',
					score: 0.6 + Math.random() * 0.4
				},
				technical: {
					httpVersion: ['HTTP/2', 'HTTP/1.1'][Math.floor(Math.random() * 2)],
					compression: Math.random() > 0.2,
					caching: Math.random() > 0.3,
					cdn: Math.random() > 0.5 ? 'CloudFlare' : null,
					cdnProvider: Math.random() > 0.5 ? 'CloudFlare' : 'None',
					serverLocation: 'United States',
					serverSoftware: 'nginx/1.18.0',
					uptime: 99.9,
					responseTime: Math.floor(Math.random() * 500) + 50,
					pageSize: Math.floor(Math.random() * 5000000) + 500000,
					resourceCount: Math.floor(Math.random() * 150) + 50,
					score: 0.8 + Math.random() * 0.2
				},
				traffic: {
					estimatedVisits: Math.floor(Math.random() * 10000000) + 100000,
					bounceRate: 0.3 + Math.random() * 0.4,
					avgSessionDuration: Math.floor(Math.random() * 300) + 60,
					pageViewsPerSession: 2 + Math.random() * 5
				}
			},
			rankingExplanation: {
				factors: [
					{
						name: 'Performance Score',
						score: 0.75 + Math.random() * 0.25,
						weight: 0.25,
						impact: 'positive' as const,
						description: 'Fast loading times and good Core Web Vitals'
					},
					{
						name: 'Security Grade',
						score: 0.8 + Math.random() * 0.2,
						weight: 0.2,
						impact: 'positive' as const,
						description: 'Strong SSL configuration and security headers'
					},
					{
						name: 'SEO Optimization',
						score: 0.65 + Math.random() * 0.35,
						weight: 0.3,
						impact: 'positive' as const,
						description: 'Good meta tags and structured data implementation'
					},
					{
						name: 'Technical Quality',
						score: 0.85 + Math.random() * 0.15,
						weight: 0.25,
						impact: 'positive' as const,
						description: 'Modern technologies and best practices'
					}
				],
				recommendations: [
					'Improve page loading speed by optimizing images',
					'Implement Content Security Policy headers',
					'Add structured data markup for better SEO',
					'Enable HTTP/2 for better performance'
				],
				competitorComparison: [
					{
						domain: 'example.com',
						rank: Math.floor(Math.random() * 100000) + 1000,
						score: 0.6 + Math.random() * 0.4
					},
					{
						domain: 'competitor.com',
						rank: Math.floor(Math.random() * 100000) + 1000,
						score: 0.5 + Math.random() * 0.5
					}
				]
			},
			historicalData: [
				{
					date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
					globalRank: Math.floor(Math.random() * 1000000) + 1000,
					overallScore: 0.6 + Math.random() * 0.4,
					trafficEstimate: Math.floor(Math.random() * 10000000) + 100000
				},
				{
					date: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
					globalRank: Math.floor(Math.random() * 1000000) + 1000,
					overallScore: 0.6 + Math.random() * 0.4,
					trafficEstimate: Math.floor(Math.random() * 10000000) + 100000
				}
			]
		};
	}

	/**
	 * Get comprehensive domain analysis
	 */
	async getDomainAnalysis(domain: string): Promise<{
		domain: string;
		globalRank: number;
		categoryRank: number;
		category: string;
		metrics: {
			performance: any;
			security: any;
			seo: any;
			technical: any;
			traffic: any;
		};
		domainInfo: {
			age: number;
			registrar: string;
			registrationDate: string;
			expirationDate: string;
			country: string;
		};
		technologies: string[];
		screenshots: string[];
		subdomains: string[];
		socialLinks: Record<string, unknown>;
		lastUpdated: string;
		crawlStatus: string;
	} | null>
	{
		try
		{
			// Normalize domain
			const normalizedDomain = this.normalizeDomain(domain);

			// Calculate domain bucket for even distribution
			const domainBucket = DomainHasher.getDomainBucket(normalizedDomain);

			// Get domain analysis from ScyllaDB using optimized bucketing
			const analysisQuery = `
				SELECT * FROM domain_analysis
				WHERE domain_bucket = ? AND domain = ?
				LIMIT 1
			`;

			const analysisResult = await this.scyllaClient.execute(analysisQuery, [domainBucket, normalizedDomain]);

			if (!analysisResult.rows || analysisResult.rows.length === 0)
			{
				// Only return mock data for example.tld
				if (normalizedDomain === 'example.tld')
				{
					this.logger.info({ domain: normalizedDomain }, 'Returning mock data for example.tld preview');
					return this.getMockDomainAnalysis(normalizedDomain);
				}

				// Return null for real domains without data
				this.logger.info({ domain: normalizedDomain }, 'No analysis data found for domain');
				return null;
			}

			const row = analysisResult.rows[0];

			return {
				domain: normalizedDomain,
				globalRank: row.global_rank || 0,
				categoryRank: row.category_rank || 0,
				category: row.category || 'Unknown',
				overallScore: row.overall_score || 0,
				performanceScore: row.performance_score || 0,
				securityScore: row.security_score || 0,
				seoScore: row.seo_score || 0,
				technicalScore: row.technical_score || 0,
				backlinkScore: row.backlink_score || 0,
				trafficEstimate: row.traffic_estimate || 0,
				domainAge: row.domain_age_days || 0,
				sslGrade: row.ssl_grade || 'N/A',
				technologies: Array.from(row.technologies || []),
				lastUpdated: row.last_crawled ? row.last_crawled.toISOString() : new Date().toISOString(),
				screenshot: row.screenshot_desktop_url || '',
				screenshots: [
					row.screenshot_desktop_url,
					row.screenshot_mobile_url
				].filter(Boolean),
				favicon: row.favicon_url || '',
				subdomains: Array.from(row.top_subdomains || []),
				socialLinks: {
					twitter: row.social_twitter || '',
					facebook: row.social_facebook || '',
					linkedin: row.social_linkedin || '',
					instagram: row.social_instagram || ''
				},
				crawlStatus: row.crawl_status || 'unknown',
				domainInfo: {
					age: row.domain_age_days || 0,
					registrar: row.registrar || 'Unknown',
					registrationDate: row.registration_date ? row.registration_date.toISOString() : '',
					expirationDate: row.expiration_date ? row.expiration_date.toISOString() : '',
					country: row.country || 'Unknown'
				},
				metrics: {
					performance: {
						loadTime: row.load_time_ms || 0,
						firstContentfulPaint: row.first_contentful_paint_ms || 0,
						largestContentfulPaint: row.largest_contentful_paint_ms || 0,
						cumulativeLayoutShift: row.cumulative_layout_shift || 0,
						firstInputDelay: row.first_input_delay_ms || 0,
						speedIndex: row.speed_index || 0,
						pageSize: row.page_size_bytes || 0,
						resourceCount: row.resource_count || 0,
						score: row.performance_score || 0
					},
					security: {
						sslGrade: row.ssl_grade || 'N/A',
						sslIssuer: row.ssl_issuer || '',
						sslExpiration: row.ssl_expiration ? row.ssl_expiration.toISOString() : '',
						securityHeaders: {
							hsts: row.has_hsts || false,
							csp: row.has_csp || false,
							xFrameOptions: row.has_x_frame_options || false,
							xContentTypeOptions: row.has_x_content_type_options || false
						},
						vulnerabilities: Array.from(row.vulnerabilities || []),
						score: row.security_score || 0
					},
					seo: {
						title: row.meta_title || '',
						description: row.meta_description || '',
						keywords: Array.from(row.meta_keywords || []),
						structuredData: row.has_structured_data || false,
						sitemap: row.has_sitemap || false,
						robotsTxt: row.has_robots_txt || false,
						hasRobotsTxt: row.has_robots_txt || false,
						hasSitemap: row.has_sitemap || false,
						mobileOptimized: row.mobile_friendly || false,
						pageSpeed: row.page_speed_score || 0,
						score: row.seo_score || 0
					},
					technical: {
						httpVersion: row.http_version || 'HTTP/1.1',
						compression: row.compression_enabled || false,
						caching: row.caching_enabled || false,
						cdnProvider: row.cdn_provider || 'None',
						serverLocation: row.server_location || 'Unknown',
						serverSoftware: row.server_software || 'Unknown',
						responseTime: row.response_time_ms || 0,
						uptime: row.uptime_percentage || 0,
						pageSize: row.page_size_bytes || 0,
						resourceCount: row.resource_count || 0,
						score: row.technical_score || 0
					},
					traffic: {
						estimatedVisits: row.traffic_estimate || 0,
						bounceRate: row.bounce_rate || 0,
						avgSessionDuration: row.avg_session_duration || 0,
						pageViewsPerSession: row.pages_per_session || 0
					}
				}
			};
		}
		catch (error)
		{
			this.logger.error({ error, msg: 'Domain analysis failed' });

			// Only return mock data for example.tld on error
			if (domain === 'example.tld')
			{
				this.logger.info({ domain }, 'Returning mock data for example.tld on error');
				return this.getMockDomainAnalysis(domain);
			}

			// Return null for real domains on error (don't crash the app)
			this.logger.warn({ domain }, 'Analysis failed for real domain, returning null');
			return null;
		}
	}

	/**
	 * Get domain ranking explanation
	 */
	async getDomainRankingExplanation(domain: string): Promise<{
		domain: string;
		globalRank: number;
		categoryRank: number;
		scoreBreakdown: {
			overall: number;
			performance: { score: number; factors: string[] };
			security: { score: number; factors: string[] };
			seo: { score: number; factors: string[] };
			technical: { score: number; factors: string[] };
		};
		strengths: string[];
		weaknesses: string[];
		recommendations: string[];
	} | null>
	{
		try
		{
			const analysis = await this.getDomainAnalysis(domain);

			if (!analysis)
			{
				return null;
			}

			const scoreBreakdown = {
				overall: this.calculateOverallScore(analysis.metrics),
				performance: {
					score: analysis.metrics.performance.score,
					factors: this.getPerformanceFactors(analysis.metrics.performance),
				},
				security: {
					score: analysis.metrics.security.score,
					factors: this.getSecurityFactors(analysis.metrics.security),
				},
				seo: {
					score: analysis.metrics.seo.score,
					factors: this.getSEOFactors(analysis.metrics.seo),
				},
				technical: {
					score: analysis.metrics.technical.score,
					factors: this.getTechnicalFactors(analysis.metrics.technical),
				},
			};

			const strengths = this.identifyStrengths(scoreBreakdown);
			const weaknesses = this.identifyWeaknesses(scoreBreakdown);
			const recommendations = this.generateRecommendations(scoreBreakdown, analysis);

			return {
				domain: analysis.domain,
				globalRank: analysis.globalRank,
				categoryRank: analysis.categoryRank,
				scoreBreakdown,
				strengths,
				weaknesses,
				recommendations,
			};
		}
		catch (error)
		{
			this.logger.error('Ranking explanation failed:', error);
			throw error;
		}
	}

	/**
	 * Get domain WHOIS information from MariaDB
	 */
	private async getDomainWhoisInfo(domain: string): Promise<any>
	{
		try
		{
			const query = `
				SELECT registrar, registration_date, expiration_date,
				       registrant_country, registrant_organization
				FROM domain_whois
				WHERE domain = ?
			`;

			const result = await this.mariaClient.execute(query, [domain]);
			const rows = (result as any)?.rows || [];
			return rows.length > 0 ? rows[0] : null;
		}
		catch (error)
		{
			this.logger.warn({ error, msg: 'WHOIS info query failed' });
			return null;
		}
	}

	/**
	 * Normalize domain name
	 */
	private normalizeDomain(domain: string): string
	{
		return domain.toLowerCase()
			.replace(/^https?:\/\//, '')
			.replace(/^www\./, '')
			.replace(/\/$/, '');
	}

	/**
	 * Parse metrics map from ScyllaDB
	 */
	private parseMetricsMap(metricsMap: any): Record<string, any>
	{
		if (!metricsMap || typeof metricsMap !== 'object') return ({});

		return metricsMap;
	}

	/**
	 * Calculate performance score
	 */
	private calculatePerformanceScore(metrics: any): number
	{
		const loadTime = parseFloat(metrics.load_time) || 0;
		const fcp = parseFloat(metrics.fcp) || 0;
		const lcp = parseFloat(metrics.lcp) || 0;
		const cls = parseFloat(metrics.cls) || 0;

		// Simple scoring algorithm (0-1 scale)
		let score = 1.0;

		// Penalize slow load times
		if (loadTime > 3) score -= 0.3;
		else if (loadTime > 1.5) score -= 0.1;

		// Penalize poor Core Web Vitals
		if (lcp > 2.5) score -= 0.2;
		if (fcp > 1.8) score -= 0.1;
		if (cls > 0.1) score -= 0.2;

		return Math.max(0, Math.min(1, score));
	}

	/**
	 * Calculate security score
	 */
	private calculateSecurityScore(securityMetrics: any, sslCertificate: any): number
	{
		let score = 0.5; // Base score

		// SSL grade bonus
		const sslGrade = sslCertificate.grade || '';
		if (sslGrade === 'A+') score += 0.3;
		else if (sslGrade === 'A') score += 0.2;
		else if (sslGrade === 'B') score += 0.1;

		// Security headers bonus
		if (securityMetrics.hsts === 'true') score += 0.1;
		if (securityMetrics.csp === 'true') score += 0.1;
		if (securityMetrics.x_frame_options === 'true') score += 0.1;

		return Math.max(0, Math.min(1, score));
	}

	/**
	 * Calculate SEO score
	 */
	private calculateSEOScore(seoMetrics: any): number
	{
		let score = 0.3; // Base score

		if (seoMetrics.title && seoMetrics.title.length > 10) score += 0.2;
		if (seoMetrics.description && seoMetrics.description.length > 50) score += 0.2;
		if (seoMetrics.robots_txt === 'true') score += 0.1;
		if (seoMetrics.sitemap === 'true') score += 0.1;
		if (seoMetrics.structured_data) score += 0.1;

		return Math.max(0, Math.min(1, score));
	}

	/**
	 * Calculate technical score
	 */
	private calculateTechnicalScore(technicalMetrics: any): number
	{
		let score = 0.5; // Base score

		const pageSize = parseInt(technicalMetrics.page_size, 10) || 0;
		if (pageSize < 1000000) score += 0.1; // Under 1MB
		else if (pageSize > 5000000) score -= 0.1; // Over 5MB

		if (technicalMetrics.compression === 'true') score += 0.1;
		if (technicalMetrics.http_version === 'HTTP/2') score += 0.1;

		return Math.max(0, Math.min(1, score));
	}

	/**
	 * Calculate overall score
	 */
	private calculateOverallScore(metrics: any): number
	{
		const weights = {
			performance: 0.25,
			security: 0.20,
			seo: 0.20,
			technical: 0.15,
			traffic: 0.20,
		};

		return (
			metrics.performance.score * weights.performance
			+ metrics.security.score * weights.security
			+ metrics.seo.score * weights.seo
			+ metrics.technical.score * weights.technical
			+ (metrics.traffic.estimatedVisits > 0 ? 0.8 : 0.2) * weights.traffic
		);
	}

	/**
	 * Parse security headers
	 */
	private parseSecurityHeaders(securityMetrics: any): any
	{
		return {
			hsts: securityMetrics.hsts === 'true',
			csp: securityMetrics.csp === 'true',
			xFrameOptions: securityMetrics.x_frame_options === 'true',
			xContentTypeOptions: securityMetrics.x_content_type_options === 'true',
		};
	}

	/**
	 * Parse vulnerabilities
	 */
	private parseVulnerabilities(securityMetrics: any): string[]
	{
		const vulnerabilities = [];
		if (securityMetrics.mixed_content === 'true') vulnerabilities.push('Mixed Content');
		if (securityMetrics.weak_ssl === 'true') vulnerabilities.push('Weak SSL Configuration');
		return vulnerabilities;
	}

	/**
	 * Get performance factors
	 */
	private getPerformanceFactors(performance: any): string[]
	{
		const factors = [];
		if (performance.loadTime < 1.5) factors.push('Fast load time');
		if (performance.firstContentfulPaint < 1.8) factors.push('Good First Contentful Paint');
		if (performance.largestContentfulPaint < 2.5) factors.push('Good Largest Contentful Paint');
		if (performance.cumulativeLayoutShift < 0.1) factors.push('Low layout shift');
		return factors;
	}

	/**
	 * Get security factors
	 */
	private getSecurityFactors(security: any): string[]
	{
		const factors = [];
		if (security.sslGrade === 'A+' || security.sslGrade === 'A') factors.push('Strong SSL certificate');
		if (security.securityHeaders.hsts) factors.push('HSTS enabled');
		if (security.securityHeaders.csp) factors.push('Content Security Policy');
		if (security.vulnerabilities.length === 0) factors.push('No known vulnerabilities');
		return factors;
	}

	/**
	 * Get SEO factors
	 */
	private getSEOFactors(seo: any): string[]
	{
		const factors = [];
		if (seo.title && seo.title.length > 10) factors.push('Good title tag');
		if (seo.description && seo.description.length > 50) factors.push('Good meta description');
		if (seo.hasRobotsTxt) factors.push('Robots.txt present');
		if (seo.hasSitemap) factors.push('Sitemap available');
		return factors;
	}

	/**
	 * Get technical factors
	 */
	private getTechnicalFactors(technical: any): string[]
	{
		const factors = [];
		if (technical.compression) factors.push('Compression enabled');
		if (technical.httpVersion === 'HTTP/2') factors.push('HTTP/2 support');
		if (technical.pageSize < 1000000) factors.push('Optimized page size');
		return factors;
	}


	/**
	 * Identify strengths
	 */
	private identifyStrengths(scoreBreakdown: any): string[]
	{
		const strengths = [];
		if (scoreBreakdown.performance.score > 0.8) strengths.push('Excellent performance');
		if (scoreBreakdown.security.score > 0.8) strengths.push('Strong security');
		if (scoreBreakdown.seo.score > 0.8) strengths.push('Good SEO optimization');
		if (scoreBreakdown.technical.score > 0.8) strengths.push('Solid technical implementation');
		return strengths;
	}

	/**
	 * Identify weaknesses
	 */
	private identifyWeaknesses(scoreBreakdown: any): string[]
	{
		const weaknesses = [];
		if (scoreBreakdown.performance.score < 0.5) weaknesses.push('Poor performance');
		if (scoreBreakdown.security.score < 0.5) weaknesses.push('Security concerns');
		if (scoreBreakdown.seo.score < 0.5) weaknesses.push('SEO needs improvement');
		if (scoreBreakdown.technical.score < 0.5) weaknesses.push('Technical issues');
		return weaknesses;
	}

	/**
	 * Generate recommendations
	 */
	private generateRecommendations(scoreBreakdown: any, analysis: any): string[]
	{
		const recommendations = [];

		if (scoreBreakdown.performance.score < 0.7)
		{
			recommendations.push('Optimize page load speed and Core Web Vitals');
		}

		if (scoreBreakdown.security.score < 0.7)
		{
			recommendations.push('Implement security headers and upgrade SSL certificate');
		}

		if (scoreBreakdown.seo.score < 0.7)
		{
			recommendations.push('Improve meta tags, add sitemap, and optimize content');
		}

		if (scoreBreakdown.technical.score < 0.7)
		{
			recommendations.push('Enable compression, upgrade to HTTP/2, and optimize resources');
		}

		return recommendations;
	}
}

export default DomainAnalysisService;
