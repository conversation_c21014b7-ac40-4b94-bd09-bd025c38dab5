# Production Dockerfile for Web Application Service
FROM node:22-bookworm-slim

# Set working directory
WORKDIR /app

# Install system dependencies and pnpm
RUN apt-get update && apt-get install -y --no-install-recommends \
	python3 \
	make \
	g++ \
	&& npm install -g pnpm@10.15.0 \
	&& apt-get clean \
	&& rm -rf /var/lib/apt/lists/*

# Copy package files
COPY package.json ./
COPY pnpm-lock.yaml ./
COPY pnpm-workspace.yaml ./
COPY services/web-app/package.json ./services/web-app/
COPY shared/package.json ./shared/

# Install dependencies
RUN pnpm install --frozen-lockfile --prod && pnpm store prune

# Copy shared utilities
COPY shared/ ./shared/

# Copy application source
COPY services/web-app/src/ ./services/web-app/src/

# Create logs directory
RUN mkdir -p logs

# Create non-root user
RUN groupadd -g 1001 nodejs && \
	useradd -r -u 1001 -g nodejs nodejs

# Change ownership of app directory
RUN chown -R nodejs:nodejs /app

# Switch to non-root user
USER nodejs

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
	CMD node -e "require('http').get('http://localhost:3000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) }).on('error', () => process.exit(1))"

# Environment variables for configuration
ENV NODE_ENV=production
ENV PORT=3000
ENV SCYLLA_HOSTS=scylla:9042
ENV MARIA_HOST=mariadb
ENV MARIA_PORT=3306
ENV MARIA_USER=root
ENV MARIA_DATABASE=domain_ranking
ENV REDIS_URL=redis://redis:6379
ENV MANTICORE_HOST=manticore
ENV MANTICORE_PORT=9308

# Add restart policy metadata
LABEL restart.policy="unless-stopped"
LABEL restart.max-retries="3"

# Start the application
CMD ["pnpm", "--filter", "domain-ranking-web-app", "start"]
