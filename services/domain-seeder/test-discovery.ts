#!/usr/bin/env tsx

/**
 * Test script to verify domain discovery functionality
 */

import { config } from 'dotenv';
import { DiscoveryOperations } from './src/core/DiscoveryOperations';
import { DatabaseManager } from '@shared';
import { logger } from '@shared';

// Load environment variables
config();

const testLogger = logger.getLogger('test-discovery');

async function testDiscovery() {
	try {
		testLogger.info('Starting discovery test...');

		// Initialize database manager
		const dbManager = new DatabaseManager();
		await dbManager.initialize();

		// Create discovery operations instance
		const discoveryOps = new DiscoveryOperations(dbManager);

		// Test getting candidates from Tranco (no API key required)
		testLogger.info('Testing Tranco connector...');
		const result = await discoveryOps.triggerTopSourcesRun({
			sources: ['tranco'],
			limit: 10,
			aggregate: false
		});

		testLogger.info('Discovery test results:', result);

		// Test discovery run with differential strategy
		testLogger.info('Testing discovery run with differential strategy...');
		const discoveryResult = await discoveryOps.triggerDiscoveryRun({
			strategies: ['differential'],
			sources: ['tranco'],
			limit: 5,
			dryRun: true
		});

		testLogger.info('Discovery run results:', discoveryResult);

		await dbManager.close();
		testLogger.info('Discovery test completed successfully!');

	} catch (error) {
		testLogger.error('Discovery test failed:', error);
		process.exit(1);
	}
}

// Run the test
testDiscovery().catch(console.error);