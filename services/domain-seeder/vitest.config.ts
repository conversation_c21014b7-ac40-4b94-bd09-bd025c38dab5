import { defineConfig } from 'vitest/config';
import { resolve } from 'path';

export default defineConfig({
	test: {
		globals: true,
		environment: 'node',
		setupFiles: ['./src/__tests__/setup.ts'],
		coverage: {
			provider: 'v8',
			reporter: ['text', 'json', 'html'],
			exclude: [
				'node_modules/',
				'src/__tests__/',
				'**/*.d.ts',
				'**/*.test.ts',
				'dist/',
			],
		},
	},
	resolve: {
		alias: {
			'@shared': resolve(__dirname, '../../shared/src'),
			'@': resolve(__dirname, './src'),
		},
	},
});
