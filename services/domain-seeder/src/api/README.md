# Domain Seeder HTTP API

The Domain Seeder service provides a comprehensive HTTP API for monitoring and controlling the domain discovery process.

## Endpoints

### Health Endpoints

#### GET /health

Returns the overall health status of the service.

**Response:**

```json
{
  "healthy": true,
  "status": "healthy",
  "checks": {
    "database": true,
    "redis": true,
    "queue": true,
    "sources": true,
    "psl": true,
    "memory": true,
    "disk": true
  },
  "details": {},
  "timestamp": "2025-01-08T19:30:00.000Z",
  "uptime": 60000,
  "version": "1.0.0",
  "environment": "production"
}
```

#### GET /health/ready

Kubernetes readiness probe endpoint.

**Response:**

```json
{
  "ready": true
}
```

#### GET /health/live

Kubernetes liveness probe endpoint.

**Response:**

```json
{
  "alive": true
}
```

### Metrics Endpoint

#### GET /metrics

Returns Prometheus-formatted metrics for monitoring.

**Response:**

```
# HELP candidates_fetched_total Total candidates fetched from sources
# TYPE candidates_fetched_total counter
candidates_fetched_total{source="tranco"} 1000
candidates_fetched_total{source="radar"} 800

# HELP new_discovered_total Total new domains discovered
# TYPE new_discovered_total counter
new_discovered_total{strategy="differential"} 50
new_discovered_total{strategy="zone-new"} 30
```

### Status Endpoint

#### GET /status

Returns detailed system status information.

**Response:**

```json
{
  "healthy": true,
  "uptime": 60000,
  "timestamp": "2025-01-08T19:30:00.000Z",
  "databases": {
    "scylla": { "healthy": true, "latency": 10 },
    "maria": { "healthy": true, "latency": 15 },
    "manticore": { "healthy": true, "latency": 5 },
    "redis": { "healthy": true, "latency": 2 }
  },
  "queue": {
    "depth": 1000,
    "highPriorityDepth": 50,
    "processingRate": 100
  },
  "sources": {
    "tranco": { "healthy": true, "lastUpdate": "2025-01-08T19:25:00.000Z" },
    "radar": { "healthy": true, "lastUpdate": "2025-01-08T19:25:00.000Z" }
  },
  "metrics": {
    "discovery": {
      "candidatesFetched": 1800,
      "newDiscovered": 80,
      "domainsEnqueued": 75,
      "discoveryRate": 4.4
    }
  }
}
```

### Trigger Endpoint

#### POST /trigger

Manually trigger discovery operations.

**Request Body:**

```json
{
  "operation": "discovery-run",
  "parameters": {
    "limit": 1000,
    "strategies": ["differential", "zone-new"]
  }
}
```

**Supported Operations:**

- `discovery-run` - Execute full discovery cycle
- `top-sources-only` - Process only top sources (Tranco/Radar)
- `backfill` - Process zone/long-tail sources
- `health-check` - Perform health check
- `clear-cache` - Clear health check cache

**Response:**

```json
{
  "operation": "discovery-run",
  "parameters": {
    "limit": 1000
  },
  "result": {
    "totalCandidates": 1000,
    "totalDiscovered": 50,
    "strategiesUsed": 4,
    "duration": 5000
  },
  "timestamp": "2025-01-08T19:30:00.000Z"
}
```

## Error Handling

The API returns appropriate HTTP status codes:

- `200` - Success
- `400` - Bad Request (invalid parameters)
- `404` - Not Found
- `413` - Payload Too Large
- `500` - Internal Server Error
- `503` - Service Unavailable (unhealthy)

Error responses include:

```json
{
  "error": "Error description",
  "timestamp": "2025-01-08T19:30:00.000Z"
}
```

## Security

The API includes security headers:

- `X-Content-Type-Options: nosniff`
- `X-Frame-Options: DENY`
- `X-XSS-Protection: 1; mode=block`
- Removes `X-Powered-By` header

## Usage Examples

### Check Service Health

```bash
curl http://localhost:3000/health
```

### Get Prometheus Metrics

```bash
curl http://localhost:3000/metrics
```

### Trigger Discovery Run

```bash
curl -X POST http://localhost:3000/trigger \
  -H "Content-Type: application/json" \
  -d '{"operation": "discovery-run", "parameters": {"limit": 5000}}'
```

### Get Detailed Status

```bash
curl http://localhost:3000/status
```
