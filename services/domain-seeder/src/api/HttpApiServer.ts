import express, { Express, Request, Response, NextFunction } from 'ultimate-express';
import type { Server } from 'node:http';

import type { <PERSON>Che<PERSON> } from '../monitoring/HealthChecker';
import type { MetricsCollector } from '../monitoring/MetricsCollector';
import { logger, type LoggerInstanceType } from '@shared';

type DataSyncServiceType = {
	getSyncMetrics?: () => Record<string, unknown>;
};

type HttpApiServerOptionsType =
{
	healthChecker: HealthChecker;
	metricsCollector: MetricsCollector;
	dataSyncService?: DataSyncServiceType;
	logger: LoggerInstanceType;
	port?: number;
};

type TriggerOperationResultType =
{
	operation: string;
	parameters?: Record<string, unknown>;
	result: unknown;
	timestamp: string;
};

class HttpApiServer
{
	private app: Express;

	private healthChecker: HealthChecker;

	private metricsCollector: MetricsCollector;

	private dataSyncService?: DataSyncServiceType;

	private logger: LoggerInstanceType;

	private port: number;

	private server?: Server;

	constructor(options: HttpApiServerOptionsType)
	{
		this.healthChecker = options.healthChecker;
		this.metricsCollector = options.metricsCollector;
		this.dataSyncService = options.dataSyncService;
		this.logger = options.logger;
		this.port = options.port || 3000;

		this.app = express();
		this.setupMiddleware();
		this.setupRoutes();
		this.setupErrorHandling();
	}

	private setupMiddleware(): void
	{
		// Security headers
		this.app.use((req: Request, res: Response, next: NextFunction) =>
		{
			res.removeHeader('X-Powered-By');
			res.setHeader('X-Content-Type-Options', 'nosniff');
			res.setHeader('X-Frame-Options', 'DENY');
			res.setHeader('X-XSS-Protection', '1; mode=block');
			next();
		});

		// Body parsing
		this.app.use(express.json({ limit: '10mb' }));
		this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

		// Request logging
		this.app.use((req: Request, res: Response, next: NextFunction) =>
		{
			this.logger.debug({
				method: req.method,
				url: req.url,
				userAgent: req.get('User-Agent'),
				ip: req.ip,
				msg: 'HTTP request',
			});
			next();
		});
	}

	private setupRoutes(): void
	{
		// Health endpoint - public
		this.app.get('/health', async (req: Request, res: Response) =>
		{
			try
			{
				const health = await this.healthChecker.checkHealth();
				res.status(health.status === 'healthy' ? 200 : 503).json(health);
			}
			catch (error)
			{
				this.logger.error({ error: (error as Error).message, msg: 'Health check endpoint error' });
				res.status(500).json({
					healthy: false,
					error: 'Internal server error',
					timestamp: new Date().toISOString(),
				});
			}
		});

		// Readiness endpoint - for Kubernetes
		this.app.get('/health/ready', async (req: Request, res: Response) =>
		{
			try
			{
				const health = await this.healthChecker.checkHealth();
				const ready = health.status === 'healthy';
				res.status(ready ? 200 : 503).json({ ready });
			}
			catch (error)
			{
				this.logger.error({ error: (error as Error).message, msg: 'Readiness check endpoint error' });
				res.status(500).json({
					ready: false,
					error: 'Internal server error',
					timestamp: new Date().toISOString(),
				});
			}
		});

		// Liveness endpoint - for Kubernetes
		this.app.get('/health/live', async (req: Request, res: Response) =>
		{
			try
			{
				const health = await this.healthChecker.checkHealth();
				const alive = health.status !== 'unhealthy';
				res.status(alive ? 200 : 503).json({ alive });
			}
			catch (error)
			{
				this.logger.error({ error: (error as Error).message, msg: 'Liveness check endpoint error' });
				res.status(500).json({
					alive: false,
					error: 'Internal server error',
					timestamp: new Date().toISOString(),
				});
			}
		});

		// Metrics endpoint - for Prometheus scraping
		this.app.get('/metrics', (req: Request, res: Response) =>
		{
			try
			{
				const metrics = this.metricsCollector.getSeederMetrics();
				res.json(metrics);
			}
			catch (error)
			{
				this.logger.error({ error: (error as Error).message, msg: 'Metrics endpoint error' });
				res.status(500).json({
					error: 'Internal server error',
					timestamp: new Date().toISOString(),
				});
			}
		});

		// Status endpoint - detailed system information
		this.app.get('/status', async (req: Request, res: Response) =>
		{
			try
			{
				const status = await this.healthChecker.checkHealth();
				const metrics = this.metricsCollector.getSeederMetrics();
				const syncMetrics = this.dataSyncService?.getSyncMetrics?.() || {};

				res.json({
					...status,
					metrics,
					syncMetrics,
					timestamp: new Date().toISOString(),
				});
			}
			catch (error)
			{
				this.logger.error({ error: (error as Error).message, msg: 'Status endpoint error' });
				res.status(500).json({
					error: 'Internal server error',
					timestamp: new Date().toISOString(),
				});
			}
		});

		// Trigger endpoint - for manual operations
		this.app.post('/trigger', async (req: Request, res: Response) =>
		{
			try
			{
				const { operation, parameters } = req.body;

				// Validate operation parameter
				if (!operation || typeof operation !== 'string')
				{
					return res.status(400).json({
						error: 'Missing or invalid operation parameter',
						supportedOperations: [
							'discovery-run',
							'top-sources-only',
							'backfill',
							'health-check',
							'clear-cache',
						],
						timestamp: new Date().toISOString(),
					});
				}

				// Sanitize operation name
				const sanitizedOperation = operation.replace(/[^a-z-]/g, '');

				const supportedOperations = [
					'discovery-run',
					'top-sources-only',
					'backfill',
					'health-check',
					'clear-cache',
				];

				if (!supportedOperations.includes(sanitizedOperation))
				{
					return res.status(400).json({
						error: `Unsupported operation: ${sanitizedOperation}`,
						supportedOperations,
						timestamp: new Date().toISOString(),
					});
				}

				// Validate parameters
				if (parameters && typeof parameters !== 'object')
				{
					return res.status(400).json({
						error: 'Parameters must be an object',
						timestamp: new Date().toISOString(),
					});
				}

				// Execute operation with timeout
				const operationTimeout = 30000; // 30 seconds
				const operationPromise = this.executeOperation(sanitizedOperation, parameters);
				const timeoutPromise = new Promise((_, reject) => setTimeout(() => reject(new Error('Operation timeout')), operationTimeout));

				const result = await Promise.race([operationPromise, timeoutPromise]);

				const response: TriggerOperationResultType = {
					operation: sanitizedOperation,
					parameters: parameters || {},
					result,
					timestamp: new Date().toISOString(),
				};

				return res.json(response);
			}
			catch (error)
			{
				this.logger.error({ error: (error as Error).message, msg: 'Trigger endpoint error' });
				return res.status(500).json({
					error: 'Internal server error',
					operation: req.body?.operation,
					timestamp: new Date().toISOString(),
				});
			}
		});
	}

	private setupErrorHandling(): void
	{
		// JSON parsing error handler
		this.app.use((error: Error, req: Request, res: Response, next: NextFunction) =>
		{
			if (error instanceof SyntaxError && 'body' in error)
			{
				this.logger.warn({ error: error.message, url: req.url, msg: 'Malformed JSON in request' });
				return res.status(400).json({
					error: 'Invalid JSON in request body',
					timestamp: new Date().toISOString(),
				});
			}

			if ((error as any).type === 'entity.too.large')
			{
				this.logger.warn({ url: req.url, msg: 'Payload too large' });
				return res.status(413).json({
					error: 'Payload too large',
					timestamp: new Date().toISOString(),
				});
			}

			return next(error);
		});

		// 404 handler
		this.app.use('*', (req: Request, res: Response) =>
		{
			res.status(404).json({
				error: 'Not Found',
				path: req.originalUrl,
				timestamp: new Date().toISOString(),
			});
		});

		// Global error handler
		this.app.use((error: Error, req: Request, res: Response, next: NextFunction) =>
		{
			this.logger.error({ error: error.message, msg: 'Unhandled HTTP error' });

			res.status(500).json({
				error: 'Internal server error',
				timestamp: new Date().toISOString(),
			});
		});
	}

	private async executeOperation(operation: string, parameters?: Record<string, unknown>): Promise<unknown>
	{
		switch (operation)
		{
			case 'health-check':
				return this.healthChecker.checkHealth();

			case 'clear-cache':
				// this.healthChecker.clearCache(); // Method doesn't exist
				return { message: 'Cache clearing not implemented' };

			case 'discovery-run':
				return this.mockTriggerDiscoveryRun(parameters);

			case 'top-sources-only':
				return this.mockTriggerTopSourcesRun(parameters);

			case 'backfill':
				return this.mockTriggerBackfillRun(parameters);

			default:
				throw new Error(`Unknown operation: ${operation}`);
		}
	}

	// Mock trigger methods for testing - in production these would call actual services
	private async mockTriggerDiscoveryRun(parameters?: Record<string, unknown>): Promise<unknown>
	{
		this.logger.info({ parameters, msg: 'Manual discovery run triggered' });

		// Simulate discovery run
		await new Promise(resolve => setTimeout(resolve, 100));

		return {
			operation: 'discovery-run',
			status: 'completed',
			totalCandidates: 1000,
			totalDiscovered: 50,
			strategiesUsed: 4,
			strategyResults: {
				differential: { candidates: 250, discovered: 15, confidence: 0.9 },
				'zone-new': { candidates: 250, discovered: 10, confidence: 0.95 },
				'long-tail': { candidates: 250, discovered: 15, confidence: 0.7 },
				temporal: { candidates: 250, discovered: 10, confidence: 0.8 },
			},
			duration: 5000,
			timestamp: new Date().toISOString(),
		};
	}

	private async mockTriggerTopSourcesRun(parameters?: Record<string, unknown>): Promise<unknown>
	{
		this.logger.info({ parameters, msg: 'Manual top sources run triggered' });

		// Simulate top sources run
		await new Promise(resolve => setTimeout(resolve, 100));

		return {
			operation: 'top-sources-only',
			status: 'completed',
			sources: ['tranco', 'radar'],
			totalCandidates: 20000,
			totalProcessed: 20000,
			sourceResults: {
				tranco: {
					candidates: 10000,
					discovered: 500,
					enqueued: 450,
					healthy: true,
					lastUpdate: new Date().toISOString(),
				},
				radar: {
					candidates: 10000,
					discovered: 400,
					enqueued: 380,
					healthy: true,
					lastUpdate: new Date().toISOString(),
				},
			},
			duration: 3000,
			timestamp: new Date().toISOString(),
		};
	}

	private async mockTriggerBackfillRun(parameters?: Record<string, unknown>): Promise<unknown>
	{
		this.logger.info({ parameters, msg: 'Manual backfill run triggered' });

		// Simulate backfill run
		await new Promise(resolve => setTimeout(resolve, 100));

		return {
			operation: 'backfill',
			status: 'completed',
			sources: ['common-crawl', 'sonar'],
			totalCandidates: 100000,
			totalDiscovered: 2500,
			totalEnqueued: 2200,
			sourceResults: {
				'common-crawl': {
					candidates: 50000,
					discovered: 1200,
					enqueued: 1100,
					healthy: true,
					lastUpdate: new Date().toISOString(),
				},
				sonar: {
					candidates: 50000,
					discovered: 1300,
					enqueued: 1100,
					healthy: true,
					lastUpdate: new Date().toISOString(),
				},
			},
			duration: 15000,
			maxNewPerDay: parameters?.maxNewPerDay || 500000,
			timestamp: new Date().toISOString(),
		};
	}

	async start(): Promise<void>
	{
		return new Promise((resolve, reject) =>
		{
			try
			{
				this.server = this.app.listen(this.port, () =>
				{
					this.logger.info({ port: this.port, msg: `HTTP API server listening on port ${this.port}` });
					resolve();
				});

				this.server.on('error', (error: Error) =>
				{
					this.logger.error({ error: error.message, msg: 'HTTP server error' });
					reject(error);
				});
			}
			catch (error)
			{
				this.logger.error({ error: (error as Error).message, msg: 'Failed to start HTTP server' });
				reject(error);
			}
		});
	}

	async stop(): Promise<void>
	{
		return new Promise((resolve) =>
		{
			if (this.server)
			{
				this.server.close(() =>
				{
					this.logger.info({ msg: 'HTTP API server stopped' });
					resolve();
				});
			}
			else
			{
				resolve();
			}
		});
	}

	getApp(): Express
	{
		return this.app;
	}

	getPort(): number
	{
		return this.port;
	}
}

export type { HttpApiServerOptionsType, TriggerOperationResultType };

export default HttpApiServer;
