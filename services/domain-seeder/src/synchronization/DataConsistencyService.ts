import type { DatabaseManager, ScyllaClient, ManticoreClient, RedisClientWrapper } from '@shared';
import { logger as sharedLogger, type LoggerInstanceType, AsyncUtils } from '@shared';

/**
 * Configuration for data consistency service
 */
type DataConsistencyConfigType =
{
	/**
	 * Batch size for synchronization operations
	 */
	syncBatchSize: number;

	/**
	 * Maximum number of retry attempts for failed operations
	 */
	maxRetries: number;

	/**
	 * Backoff time between retries in milliseconds
	 */
	retryBackoffMs: number;

	/**
	 * Enable exponential backoff for retries
	 */
	exponentialBackoff: boolean;

	/**
	 * Consistency check interval in milliseconds
	 */
	consistencyCheckInterval: number;

	/**
	 * Maximum acceptable inconsistency threshold (0.0 to 1.0)
	 */
	maxInconsistencyThreshold: number;

	/**
	 * Enable automatic repair of inconsistencies
	 */
	enableAutoRepair: boolean;

	/**
	 * Timeout for synchronization operations in milliseconds
	 */
	syncTimeout: number;

	/**
	 * Enable performance monitoring
	 */
	enableMetrics: boolean;
};

/**
 * Default configuration for data consistency service
 */
const DEFAULT_CONSISTENCY_CONFIG: DataConsistencyConfigType =
{
	syncBatchSize: 10000,
	maxRetries: 3,
	retryBackoffMs: 1000,
	exponentialBackoff: true,
	consistencyCheckInterval: 300000, // 5 minutes
	maxInconsistencyThreshold: 0.01, // 1% inconsistency threshold
	enableAutoRepair: true,
	syncTimeout: 30000, // 30 seconds
	enableMetrics: true,
};

/**
 * Consistency check result
 */
type ConsistencyCheckResultType =
{
	/**
	 * Total domains checked
	 */
	totalDomains: number;

	/**
	 * Domains that are consistent between ScyllaDB and Manticore
	 */
	consistentDomains: number;

	/**
	 * Domains that exist in ScyllaDB but not in Manticore
	 */
	missingInManticore: string[];

	/**
	 * Domains that exist in Manticore but not in ScyllaDB
	 */
	missingInScylla: string[];

	/**
	 * Inconsistency rate (0.0 to 1.0)
	 */
	inconsistencyRate: number;

	/**
	 * Check execution time in milliseconds
	 */
	executionTime: number;

	/**
	 * Whether the inconsistency rate is within acceptable threshold
	 */
	isWithinThreshold: boolean;
};

/**
 * Synchronization result
 */
type SynchronizationResultType =
{
	/**
	 * Domains successfully synchronized to Manticore
	 */
	syncedToManticore: number;

	/**
	 * Domains successfully synchronized to ScyllaDB
	 */
	syncedToScylla: number;

	/**
	 * Domains that failed synchronization
	 */
	failedDomains: Map<string, Error>;

	/**
	 * Synchronization execution time in milliseconds
	 */
	executionTime: number;

	/**
	 * Whether synchronization completed successfully
	 */
	success: boolean;
};

/**
 * Index rebuild result
 */
type IndexRebuildResultType =
{
	/**
	 * Total domains processed during rebuild
	 */
	totalDomains: number;

	/**
	 * Domains successfully indexed
	 */
	indexedDomains: number;

	/**
	 * Domains that failed indexing
	 */
	failedDomains: Map<string, Error>;

	/**
	 * Rebuild execution time in milliseconds
	 */
	executionTime: number;

	/**
	 * Whether rebuild completed successfully
	 */
	success: boolean;
};

/**
 * Data consistency and synchronization service
 * Maintains consistency between ScyllaDB (authoritative) and Manticore Search (index)
 */
class DataConsistencyService
{
	private readonly logger = sharedLogger.getLogger('DataConsistencyService');

	private readonly config: DataConsistencyConfigType;

	private readonly scyllaClient: ScyllaClient;

	private readonly manticoreClient: ManticoreClient;

	private readonly redisClient: RedisClientWrapper;

	// Performance metrics
	private totalConsistencyChecks = 0;

	private totalSynchronizations = 0;

	private totalInconsistenciesFound = 0;

	private totalInconsistenciesRepaired = 0;

	private lastConsistencyCheck = 0;

	private lastInconsistencyRate = 0;

	// Background monitoring
	private consistencyMonitorInterval: NodeJS.Timeout | null = null;

	constructor(
		private readonly dbManager: DatabaseManager,
		config: Partial<DataConsistencyConfigType> = {},
	)
	{
		this.config = { ...DEFAULT_CONSISTENCY_CONFIG, ...config };

		this.scyllaClient = this.dbManager.getScyllaClient();
		this.manticoreClient = this.dbManager.getManticoreClient();
		this.redisClient = this.dbManager.getRedisClient();

		this.logger.info({ msg: 'DataConsistencyService initialized', 
			config: this.config,
		});

		// Start consistency monitoring if enabled
		if (this.config.enableMetrics)
		{
			this.startConsistencyMonitoring();
		}
	}

	/**
	 * Perform comprehensive consistency check between ScyllaDB and Manticore
	 */
	async performConsistencyCheck(sampleSize?: number): Promise<ConsistencyCheckResultType>
	{
		const startTime = Date.now();
		this.totalConsistencyChecks++;

		try
		{
			this.logger.info({ msg: 'Starting consistency check',  sampleSize });

			// Get sample of domains from ScyllaDB (authoritative source)
			const scyllaDomains = await this.getSampleDomainsFromScylla(sampleSize);

			if (scyllaDomains.length === 0)
			{
				return this.createEmptyConsistencyResult(startTime);
			}

			// Check which domains exist in Manticore
			const manticoreResults = await this.checkDomainsInManticore(scyllaDomains);

			// Identify inconsistencies
			const missingInManticore: string[] = [];
			let consistentDomains = 0;

			scyllaDomains.forEach((domain) =>
			{
				if (manticoreResults.get(domain))
				{
					consistentDomains++;
				}
				else
				{
					missingInManticore.push(domain);
				}
			});

			// For completeness, also check for domains in Manticore that might not be in ScyllaDB
			// (This is less common but can happen during partial failures)
			const missingInScylla = await this.findOrphanedManticoreDomains(scyllaDomains);

			const totalInconsistencies = missingInManticore.length + missingInScylla.length;
			const inconsistencyRate = scyllaDomains.length > 0 ? totalInconsistencies / scyllaDomains.length : 0;

			this.totalInconsistenciesFound += totalInconsistencies;
			this.lastInconsistencyRate = inconsistencyRate;
			this.lastConsistencyCheck = Date.now();

			const result: ConsistencyCheckResultType = {
				totalDomains: scyllaDomains.length,
				consistentDomains,
				missingInManticore,
				missingInScylla,
				inconsistencyRate,
				executionTime: Date.now() - startTime,
				isWithinThreshold: inconsistencyRate <= this.config.maxInconsistencyThreshold,
			};

			this.logger.info({ msg: 'Consistency check completed', 
				totalDomains: result.totalDomains,
				consistentDomains: result.consistentDomains,
				missingInManticore: result.missingInManticore.length,
				missingInScylla: result.missingInScylla.length,
				inconsistencyRate: result.inconsistencyRate,
				isWithinThreshold: result.isWithinThreshold,
				executionTime: result.executionTime,
			});

			// Trigger automatic repair if enabled and inconsistencies found
			if (this.config.enableAutoRepair && totalInconsistencies > 0)
			{
				this.logger.info('Triggering automatic repair for inconsistencies');
				await this.repairInconsistencies(result);
			}

			return result;
		}
		catch (error)
		{
			this.logger.error({ msg: 'Consistency check failed', 
				error: (error as Error).message,
				sampleSize,
			});
			throw error;
		}
	}

	/**
	 * Synchronize ScyllaDB data to Manticore Search
	 */
	async synchronizeScyllaToManticore(domains?: string[]): Promise<SynchronizationResultType>
	{
		const startTime = Date.now();
		this.totalSynchronizations++;

		try
		{
			let domainsToSync: string[];

			if (domains)
			{
				domainsToSync = domains;
			}
			else
			{
				// Get all domains from ScyllaDB for full sync
				domainsToSync = await this.getAllDomainsFromScylla();
			}

			this.logger.info({ msg: 'Starting ScyllaDB to Manticore synchronization', 
				domainsCount: domainsToSync.length,
			});

			const failedDomains = new Map<string, Error>();
			let syncedToManticore = 0;

			// Process domains in batches
			for (let i = 0; i < domainsToSync.length; i += this.config.syncBatchSize)
			{
				const batch = domainsToSync.slice(i, i + this.config.syncBatchSize);

				try
				{
					// Get domain data from ScyllaDB
					const domainData = await this.getDomainDataFromScylla(batch);

					// Index domains in Manticore
					await this.indexDomainsInManticore(domainData);

					syncedToManticore += batch.length;

					this.logger.debug({ msg: 'Synchronized batch to Manticore', 
						batchSize: batch.length,
						totalSynced: syncedToManticore,
					});
				}
				catch (error)
				{
					// Mark all domains in failed batch as errors
					batch.forEach((domain) =>
					{
						failedDomains.set(domain, error as Error);
					});

					this.logger.warn({ msg: 'Batch synchronization to Manticore failed', 
						batchSize: batch.length,
						error: (error as Error).message,
					});
				}
			}

			const result: SynchronizationResultType = {
				syncedToManticore,
				syncedToScylla: 0,
				failedDomains,
				executionTime: Date.now() - startTime,
				success: failedDomains.size === 0,
			};

			this.logger.info({ msg: 'ScyllaDB to Manticore synchronization completed', 
				syncedToManticore: result.syncedToManticore,
				failedDomains: result.failedDomains.size,
				executionTime: result.executionTime,
				success: result.success,
			});

			return result;
		}
		catch (error)
		{
			this.logger.error({ msg: 'ScyllaDB to Manticore synchronization failed', 
				error: (error as Error).message,
			});
			throw error;
		}
	}

	/**
	 * Rebuild Manticore index from ScyllaDB data
	 */
	async rebuildManticoreIndex(): Promise<IndexRebuildResultType>
	{
		const startTime = Date.now();

		try
		{
			this.logger.info('Starting Manticore index rebuild from ScyllaDB');

			// Clear existing Manticore index
			await this.clearManticoreIndex();

			// Get all domains from ScyllaDB
			const allDomains = await this.getAllDomainsFromScylla();

			this.logger.info({ msg: 'Rebuilding index for domains', 
				totalDomains: allDomains.length,
			});

			const failedDomains = new Map<string, Error>();
			let indexedDomains = 0;

			// Process domains in batches
			for (let i = 0; i < allDomains.length; i += this.config.syncBatchSize)
			{
				const batch = allDomains.slice(i, i + this.config.syncBatchSize);

				try
				{
					// Get domain data from ScyllaDB
					const domainData = await this.getDomainDataFromScylla(batch);

					// Index domains in Manticore
					await this.indexDomainsInManticore(domainData);

					indexedDomains += batch.length;

					this.logger.debug({ msg: 'Indexed batch in Manticore', 
						batchSize: batch.length,
						totalIndexed: indexedDomains,
						progress: Math.round((indexedDomains / allDomains.length) * 100),
					});
				}
				catch (error)
				{
					// Mark all domains in failed batch as errors
					batch.forEach((domain) =>
					{
						failedDomains.set(domain, error as Error);
					});

					this.logger.warn({ msg: 'Batch indexing failed during rebuild', 
						batchSize: batch.length,
						error: (error as Error).message,
					});
				}
			}

			const result: IndexRebuildResultType = {
				totalDomains: allDomains.length,
				indexedDomains,
				failedDomains,
				executionTime: Date.now() - startTime,
				success: failedDomains.size === 0,
			};

			this.logger.info({ msg: 'Manticore index rebuild completed', 
				totalDomains: result.totalDomains,
				indexedDomains: result.indexedDomains,
				failedDomains: result.failedDomains.size,
				executionTime: result.executionTime,
				success: result.success,
			});

			return result;
		}
		catch (error)
		{
			this.logger.error({ msg: 'Manticore index rebuild failed', 
				error: (error as Error).message,
			});
			throw error;
		}
	}

	/**
	 * Repair inconsistencies found during consistency check
	 */
	async repairInconsistencies(consistencyResult: ConsistencyCheckResultType): Promise<SynchronizationResultType>
	{
		const startTime = Date.now();

		try
		{
			this.logger.info({ msg: 'Starting inconsistency repair', 
				missingInManticore: consistencyResult.missingInManticore.length,
				missingInScylla: consistencyResult.missingInScylla.length,
			});

			const failedDomains = new Map<string, Error>();
			let syncedToManticore = 0;
			let syncedToScylla = 0;

			// Repair domains missing in Manticore (sync from ScyllaDB)
			if (consistencyResult.missingInManticore.length > 0)
			{
				try
				{
					const manticoreSync = await this.synchronizeScyllaToManticore(
						consistencyResult.missingInManticore,
					);
					syncedToManticore = manticoreSync.syncedToManticore;

					// Merge failed domains
					manticoreSync.failedDomains.forEach((error, domain) =>
					{
						failedDomains.set(domain, error);
					});
				}
				catch (error)
				{
					this.logger.error({ msg: 'Failed to repair domains missing in Manticore', 
						error: (error as Error).message,
					});
				}
			}

			// Repair domains missing in ScyllaDB (remove from Manticore as ScyllaDB is authoritative)
			if (consistencyResult.missingInScylla.length > 0)
			{
				try
				{
					await this.removeDomainsFromManticore(consistencyResult.missingInScylla);
					syncedToScylla = consistencyResult.missingInScylla.length;
				}
				catch (error)
				{
					this.logger.error({ msg: 'Failed to remove orphaned domains from Manticore', 
						error: (error as Error).message,
					});

					// Mark all as failed
					consistencyResult.missingInScylla.forEach((domain) =>
					{
						failedDomains.set(domain, error as Error);
					});
				}
			}

			const repairedCount = syncedToManticore + syncedToScylla - failedDomains.size;
			this.totalInconsistenciesRepaired += repairedCount;

			const result: SynchronizationResultType = {
				syncedToManticore,
				syncedToScylla,
				failedDomains,
				executionTime: Date.now() - startTime,
				success: failedDomains.size === 0,
			};

			this.logger.info({ msg: 'Inconsistency repair completed', 
				syncedToManticore: result.syncedToManticore,
				syncedToScylla: result.syncedToScylla,
				failedDomains: result.failedDomains.size,
				executionTime: result.executionTime,
				success: result.success,
			});

			return result;
		}
		catch (error)
		{
			this.logger.error({ msg: 'Inconsistency repair failed', 
				error: (error as Error).message,
			});
			throw error;
		}
	}

	/**
	 * Get service metrics
	 */
	getMetrics(): {
		totalConsistencyChecks: number;
		totalSynchronizations: number;
		totalInconsistenciesFound: number;
		totalInconsistenciesRepaired: number;
		lastConsistencyCheck: number;
		lastInconsistencyRate: number;
		repairEfficiency: number;
		averageInconsistencyRate: number;
	}
	{
		return {
			totalConsistencyChecks: this.totalConsistencyChecks,
			totalSynchronizations: this.totalSynchronizations,
			totalInconsistenciesFound: this.totalInconsistenciesFound,
			totalInconsistenciesRepaired: this.totalInconsistenciesRepaired,
			lastConsistencyCheck: this.lastConsistencyCheck,
			lastInconsistencyRate: this.lastInconsistencyRate,
			repairEfficiency: this.totalInconsistenciesFound > 0
				? this.totalInconsistenciesRepaired / this.totalInconsistenciesFound
				: 1,
			averageInconsistencyRate: this.totalConsistencyChecks > 0
				? this.totalInconsistenciesFound / this.totalConsistencyChecks
				: 0,
		};
	}

	/**
	 * Health check for consistency service
	 */
	async healthCheck(): Promise<{
		isHealthy: boolean;
		scyllaConnected: boolean;
		manticoreConnected: boolean;
		redisConnected: boolean;
		lastConsistencyCheck: number;
		lastInconsistencyRate: number;
		isWithinThreshold: boolean;
	}>
	{
		try
		{
			const [scyllaHealth, manticoreHealth, redisHealth] = await Promise.allSettled([
				this.scyllaClient.healthCheck(),
				this.manticoreClient.healthCheck(),
				this.redisClient.healthCheck(),
			]);

			const results = {
				scyllaConnected: scyllaHealth.status === 'fulfilled' ? scyllaHealth.value : false,
				manticoreConnected: manticoreHealth.status === 'fulfilled' ? manticoreHealth.value : false,
				redisConnected: redisHealth.status === 'fulfilled' ? redisHealth.value : false,
			};

			const isHealthy = results.scyllaConnected && results.manticoreConnected;
			const isWithinThreshold = this.lastInconsistencyRate <= this.config.maxInconsistencyThreshold;

			return {
				isHealthy,
				...results,
				lastConsistencyCheck: this.lastConsistencyCheck,
				lastInconsistencyRate: this.lastInconsistencyRate,
				isWithinThreshold,
			};
		}
		catch (error)
		{
			this.logger.error({ msg: 'Health check failed',  error: (error as Error).message });
			return {
				isHealthy: false,
				scyllaConnected: false,
				manticoreConnected: false,
				redisConnected: false,
				lastConsistencyCheck: this.lastConsistencyCheck,
				lastInconsistencyRate: this.lastInconsistencyRate,
				isWithinThreshold: false,
			};
		}
	}

	/**
	 * Get current configuration
	 */
	getConfig(): DataConsistencyConfigType
	{
		return { ...this.config };
	}

	/**
	 * Stop consistency monitoring
	 */
	stopMonitoring(): void
	{
		if (this.consistencyMonitorInterval)
		{
			clearInterval(this.consistencyMonitorInterval);
			this.consistencyMonitorInterval = null;
			this.logger.info('Consistency monitoring stopped');
		}
	}

	/**
	 * Start consistency monitoring
	 */
	private startConsistencyMonitoring(): void
	{
		this.consistencyMonitorInterval = setInterval(async () =>
		{
			try
			{
				const result = await this.performConsistencyCheck();

				if (!result.isWithinThreshold)
				{
					this.logger.warn({ msg: 'Consistency threshold exceeded', 
						inconsistencyRate: result.inconsistencyRate,
						threshold: this.config.maxInconsistencyThreshold,
						missingInManticore: result.missingInManticore.length,
						missingInScylla: result.missingInScylla.length,
					});
				}
			}
			catch (error)
			{
				this.logger.error({ msg: 'Scheduled consistency check failed', 
					error: (error as Error).message,
				});
			}
		}, this.config.consistencyCheckInterval);

		this.logger.info({ msg: 'Consistency monitoring started', 
			interval: this.config.consistencyCheckInterval,
		});
	}

	/**
	 * Get sample domains from ScyllaDB
	 */
	private async getSampleDomainsFromScylla(sampleSize?: number): Promise<string[]>
	{
		const limit = sampleSize || this.config.syncBatchSize;

		try
		{
			const query = 'SELECT domain FROM domain_analysis LIMIT ?';
			const result = await this.scyllaClient.execute(query, [limit]);

			return result.rows.map(row => row.domain as string);
		}
		catch (error)
		{
			this.logger.error({ msg: 'Failed to get sample domains from ScyllaDB', 
				error: (error as Error).message,
				sampleSize: limit,
			});
			throw error;
		}
	}

	/**
	 * Get all domains from ScyllaDB
	 */
	private async getAllDomainsFromScylla(): Promise<string[]>
	{
		try
		{
			const query = 'SELECT domain FROM domain_analysis';
			const result = await this.scyllaClient.execute(query, []);

			return result.rows.map(row => row.domain as string);
		}
		catch (error)
		{
			this.logger.error({ msg: 'Failed to get all domains from ScyllaDB', 
				error: (error as Error).message,
			});
			throw error;
		}
	}

	/**
	 * Check which domains exist in Manticore
	 */
	private async checkDomainsInManticore(domains: string[]): Promise<Map<string, boolean>>
	{
		try
		{
			const searchResult = await this.manticoreClient.compareDomains(domains);
			const results = new Map<string, boolean>();

			// Initialize all as not found
			domains.forEach(domain => results.set(domain, false));

			// Mark found domains as true
			searchResult.results.forEach((result) =>
			{
				const domain = result.domain as string;
				if (results.has(domain))
				{
					results.set(domain, true);
				}
			});

			return results;
		}
		catch (error)
		{
			this.logger.error({ msg: 'Failed to check domains in Manticore', 
				error: (error as Error).message,
				domainsCount: domains.length,
			});
			throw error;
		}
	}

	/**
	 * Find domains that exist in Manticore but not in ScyllaDB (orphaned)
	 */
	private async findOrphanedManticoreDomains(scyllaDomains: string[]): Promise<string[]>
	{
		try
		{
			this.logger.debug({ msg: 'Finding orphaned Manticore domains', 
				scyllaDomainsCount: scyllaDomains.length,
			});

			// Create a Set for efficient lookup
			const scyllaDomainSet = new Set(scyllaDomains);
			const orphanedDomains: string[] = [];

			// Query Manticore in batches to find all domains
			const batchSize = 10000;
			let offset = 0;
			let hasMore = true;

			while (hasMore)
			{
				try
				{
					// Query Manticore for a batch of domains
					const manticoreResult = await this.manticoreClient.searchDomains({
						limit: batchSize,
						offset,
					});

					if (!manticoreResult.results || manticoreResult.results.length === 0)
					{
						hasMore = false;
						break;
					}

					// Check each Manticore domain against Scylla domains
					for (const result of manticoreResult.results)
					{
						const domain = result.domain as string;
						if (domain && !scyllaDomainSet.has(domain))
						{
							orphanedDomains.push(domain);
						}
					}

					offset += batchSize;
					hasMore = manticoreResult.results.length === batchSize;

					// Add delay to avoid overwhelming the system
					if (hasMore)
					{
						await AsyncUtils.sleep(100);
					}
				}
				catch (error)
				{
					this.logger.error({ msg: 'Error querying Manticore batch', 
						offset,
						batchSize,
						error: (error as Error).message,
					});
					break;
				}
			}

			this.logger.debug({ msg: 'Orphaned Manticore domains found', 
				orphanedCount: orphanedDomains.length,
				totalScyllaDomains: scyllaDomains.length,
			});

			return orphanedDomains;
		}
		catch (error)
		{
			this.logger.error({ msg: 'Failed to find orphaned Manticore domains', 
				error: (error as Error).message,
			});
			return [];
		}
	}

	/**
	 * Get domain data from ScyllaDB
	 */
	private async getDomainDataFromScylla(domains: string[]): Promise<Array<{
		domain: string;
		data: Record<string, unknown>;
	}>>
	{
		try
		{
			const placeholders = domains.map(() => '?').join(',');
			const query = `SELECT * FROM domain_analysis WHERE domain IN (${placeholders})`;
			const result = await this.scyllaClient.execute(query, domains);

			return result.rows.map(row => ({
				domain: row.domain as string,
				data: row,
			}));
		}
		catch (error)
		{
			this.logger.error({ msg: 'Failed to get domain data from ScyllaDB', 
				error: (error as Error).message,
				domainsCount: domains.length,
			});
			throw error;
		}
	}

	/**
	 * Index domains in Manticore
	 */
	private async indexDomainsInManticore(domainData: Array<{
		domain: string;
		data: Record<string, unknown>;
	}>): Promise<void>
	{
		try
		{
			// Use Manticore's bulk indexing API
			const manticoreClient = this.dbManager.getManticoreClient();

			// Prepare bulk insert data
			const bulkData = domainData.map(({ domain, data }) => ({
				domain,
				category: data.category || 'unknown',
				tags: data.tags || [],
				summary: data.summary || '',
				confidence: data.confidence || 0.5,
				indexed_at: new Date().toISOString(),
			}));

			// Perform bulk insert in batches
			const batchSize = 1000;
			for (let i = 0; i < bulkData.length; i += batchSize)
			{
				const batch = bulkData.slice(i, i + batchSize);

				// Use Manticore's bulk insert API
				await manticoreClient.bulkInsert('domains_index', batch);

				this.logger.debug({ msg: 'Indexed batch in Manticore', 
					batchSize: batch.length,
					totalProcessed: i + batch.length,
				});
			}
		}
		catch (error)
		{
			this.logger.error({ msg: 'Failed to index domains in Manticore', 
				error: (error as Error).message,
				domainsCount: domainData.length,
			});
			throw error;
		}
	}

	/**
	 * Remove domains from Manticore
	 */
	private async removeDomainsFromManticore(domains: string[]): Promise<void>
	{
		try
		{
			// Use Manticore's bulk deletion API
			const manticoreClient = this.dbManager.getManticoreClient();

			// Perform bulk delete in batches
			const batchSize = 1000;
			for (let i = 0; i < domains.length; i += batchSize)
			{
				const batch = domains.slice(i, i + batchSize);

				// Use Manticore's bulk delete API
				await manticoreClient.bulkDelete('domains_index', batch);

				this.logger.debug({ msg: 'Removed batch from Manticore', 
					batchSize: batch.length,
					totalProcessed: i + batch.length,
				});
			}
		}
		catch (error)
		{
			this.logger.error({ msg: 'Failed to remove domains from Manticore', 
				error: (error as Error).message,
				domainsCount: domains.length,
			});
			throw error;
		}
	}

	/**
	 * Clear Manticore index
	 */
	private async clearManticoreIndex(): Promise<void>
	{
		try
		{
			// Use Manticore's index clearing API
			const manticoreClient = this.dbManager.getManticoreClient();

			// Clear the entire domains index
			await manticoreClient.truncateIndex('domains_index');

			this.logger.info('Manticore index cleared successfully');
		}
		catch (error)
		{
			this.logger.error({ msg: 'Failed to clear Manticore index', 
				error: (error as Error).message,
			});
			throw error;
		}
	}

	/**
	 * Create empty consistency result
	 */
	private createEmptyConsistencyResult(startTime: number): ConsistencyCheckResultType
	{
		return ({
			totalDomains: 0,
			consistentDomains: 0,
			missingInManticore: [],
			missingInScylla: [],
			inconsistencyRate: 0,
			executionTime: Date.now() - startTime,
			isWithinThreshold: true,
		});
	}
}

export type {
	DataConsistencyConfigType,
	ConsistencyCheckResultType,
	SynchronizationResultType,
	IndexRebuildResultType,
};

export { DEFAULT_CONSISTENCY_CONFIG, DataConsistencyService };

export default DataConsistencyService;
