/* eslint-disable max-classes-per-file */
import { logger as sharedLogger, type LoggerInstanceType } from '@shared';
import type { ValidationResultType } from '../interfaces/ReliabilityManager';
import ValidationPipeline from './ValidationPipeline';
import ErrorRecoveryManager from './ErrorRecovery';
import type { PipelineStageDataType, SanitizedDataType, ValidationConfigType } from './ValidationPipeline';
import type { RecoveryConfigType, RecoveryResultType } from './ErrorRecovery';

type ValidationManagerConfigType =
{
	validation: Partial<ValidationConfigType>;
	recovery: Partial<RecoveryConfigType>;
	enableStrictMode: boolean;
	enableRecovery: boolean;
	logValidationResults: boolean;
};

type EnhancedValidationResultType = ValidationResultType & {
	stage: string;
	sanitizedData?: SanitizedDataType<keyof PipelineStageDataType>;
	recoveryApplied?: boolean;
	recoveryResult?: RecoveryResultType;
	processingTime: number;
	validationMetrics: {
		totalItems: number;
		validItems: number;
		invalidItems: number;
		recoveredItems: number;
		quarantinedItems: number;
	};
};

class ValidationManager
{
	private pipeline: ValidationPipeline;

	private recoveryManager: ErrorRecoveryManager;

	private config: ValidationManagerConfigType;

	private logger = sharedLogger.getLogger('ValidationManager');

	private validationHistory: Array<{
		isValid: boolean;
		wasRecovered: boolean;
		processingTime: number;
		errors: Array<{ stage?: string; message: string; }>;
		warnings: Array<{ stage?: string; message: string; }>;
		timestamp: Date;
	}> = [];

	constructor(config: Partial<ValidationManagerConfigType> = {})
	{
		this.config = {
			validation: {},
			recovery: {},
			enableStrictMode: false,
			enableRecovery: true,
			logValidationResults: false,
			...config,
		};

		this.pipeline = new ValidationPipeline(this.config.validation);
		this.recoveryManager = new ErrorRecoveryManager(this.config.recovery);
	}

	async validatePipelineStage<T extends keyof PipelineStageDataType>(
		stage: T,
		data: any,
	): Promise<EnhancedValidationResultType>
	{
		const startTime = Date.now();

		// Initial validation
		const validationResult = await this.pipeline.validateStage(stage, data);

		let finalResult: EnhancedValidationResultType =
		{
			...validationResult,
			stage,
			processingTime: Date.now() - startTime,
			validationMetrics: this.calculateMetrics(stage, data, validationResult),
		};

		// Apply error recovery if enabled and needed
		if (this.config.enableRecovery && !validationResult.isValid)
		{
			const recoveryResult = await this.recoveryManager.attemptRecovery(
				stage,
				data,
				validationResult,
			);

			finalResult = {
				...finalResult,
				isValid: recoveryResult.success,
				errors: recoveryResult.remainingErrors,
				warnings: [...finalResult.warnings, ...recoveryResult.warnings],
				sanitizedData: recoveryResult.recoveredData,
				recoveryApplied: recoveryResult.appliedStrategies.length > 0,
				recoveryResult,
				processingTime: Date.now() - startTime,
				validationMetrics: this.calculateMetrics(
					stage,
					recoveryResult.recoveredData || data,
					{
						isValid: recoveryResult.success,
						errors: recoveryResult.remainingErrors,
						warnings: recoveryResult.warnings,
					},
				),
			};
		}
		else if (validationResult.sanitizedData)
		{
			finalResult.sanitizedData = validationResult.sanitizedData;
		}

		// In strict mode, any warnings become errors
		if (this.config.enableStrictMode && finalResult.warnings.length > 0)
		{
			finalResult.errors.push(...finalResult.warnings.map(w => `Strict mode: ${w}`));
			finalResult.warnings = [];
			finalResult.isValid = false;
		}

		// Log results if enabled
		if (this.config.logValidationResults)
		{
			this.logValidationResult(finalResult);
		}

		// Record validation history
		this.recordValidationHistory(finalResult);

		return finalResult;
	}

	async validateBatch<T extends keyof PipelineStageDataType>(
		stage: T,
		dataItems: any[],
	): Promise<{
		results: EnhancedValidationResultType[];
		summary: {
			totalItems: number;
			validItems: number;
			invalidItems: number;
			recoveredItems: number;
			totalProcessingTime: number;
			averageProcessingTime: number;
		};
	}>
	{
		const startTime = Date.now();
		const results: EnhancedValidationResultType[] = [];

		// Process each item
		for (const data of dataItems)
		{
			const result = await this.validatePipelineStage(stage, data);
			results.push(result);
		}

		// Calculate summary
		const summary =
		{
			totalItems: results.length,
			validItems: results.filter(r => r.isValid).length,
			invalidItems: results.filter(r => !r.isValid).length,
			recoveredItems: results.filter(r => r.recoveryApplied).length,
			totalProcessingTime: Date.now() - startTime,
			averageProcessingTime: results.length > 0 ? (Date.now() - startTime) / results.length : 0,
		};

		return ({ results, summary });
	}

	async validatePipeline(
		stages: Array<{
			stage: keyof PipelineStageDataType;
			data: any;
		}>,
	): Promise<{
		results: EnhancedValidationResultType[];
		success: boolean;
		failedStage?: string;
	}>
	{
		const results: EnhancedValidationResultType[] = [];
		let currentData: any;

		for (const { stage, data } of stages)
		{
			// Use output from previous stage if available
			const inputData = currentData || data;
			const result = await this.validatePipelineStage(stage, inputData);

			results.push(result);

			if (!result.isValid)
			{
				return {
					results,
					success: false,
					failedStage: stage,
				};
			}

			// Use sanitized data for next stage
			currentData = result.sanitizedData;
		}

		return {
			results,
			success: true,
		};
	}

	getValidationMetrics(): {
		totalValidations: number;
		successRate: number;
		recoveryRate: number;
		averageProcessingTime: number;
		errorsByStage: Record<string, number>;
		warningsByStage: Record<string, number>;
	}
	{
		// Calculate metrics from validation history
		const recentValidations = this.validationHistory.slice(-1000); // Last 1000 validations

		if (recentValidations.length === 0)
		{
			return {
				totalValidations: 0,
				successRate: 0,
				recoveryRate: 0,
				averageProcessingTime: 0,
				errorsByStage: {},
				warningsByStage: {},
			};
		}

		const totalValidations = recentValidations.length;
		const successfulValidations = recentValidations.filter(v => v.isValid).length;
		const recoveredValidations = recentValidations.filter(v => v.wasRecovered).length;

		const totalProcessingTime = recentValidations.reduce((sum, v) => sum + v.processingTime, 0);
		const averageProcessingTime = totalProcessingTime / totalValidations;

		// Group errors and warnings by stage
		const errorsByStage: Record<string, number> = {};
		const warningsByStage: Record<string, number> = {};

		recentValidations.forEach((validation) =>
		{
			validation.errors.forEach((error) =>
			{
				const stage = error.stage || 'unknown';
				errorsByStage[stage] = (errorsByStage[stage] || 0) + 1;
			});

			validation.warnings.forEach((warning) =>
			{
				const stage = warning.stage || 'unknown';
				warningsByStage[stage] = (warningsByStage[stage] || 0) + 1;
			});
		});

		return {
			totalValidations,
			successRate: successfulValidations / totalValidations,
			recoveryRate: recoveredValidations / totalValidations,
			averageProcessingTime,
			errorsByStage,
			warningsByStage,
		};
	}

	/**
	 * Record validation result in history for metrics
	 */
	private recordValidationHistory(result: EnhancedValidationResultType): void
	{
		this.validationHistory.push({
			isValid: result.isValid,
			wasRecovered: result.recoveryApplied || false,
			processingTime: result.processingTime,
			errors: result.errors.map((error: any) => ({
				stage: result.stage,
				message: typeof error === 'string' ? error : String(error),
			})),
			warnings: result.warnings.map(warning => ({
				stage: result.stage,
				message: typeof warning === 'string' ? warning : String(warning),
			})),
			timestamp: new Date(),
		});

		// Keep only the last 10000 validation records
		if (this.validationHistory.length > 10000)
		{
			this.validationHistory = this.validationHistory.slice(-10000);
		}
	}

	updateConfig(config: Partial<ValidationManagerConfigType>): void
	{
		this.config = { ...this.config, ...config };

		// Recreate pipeline and recovery manager with new config
		this.pipeline = new ValidationPipeline(this.config.validation);
		this.recoveryManager = new ErrorRecoveryManager(this.config.recovery);
	}

	private calculateMetrics<T extends keyof PipelineStageDataType>(
		stage: T,
		data: any,
		validationResult: ValidationResultType,
	): {
		totalItems: number;
		validItems: number;
		invalidItems: number;
		recoveredItems: number;
		quarantinedItems: number;
	}
	{
		let totalItems = 0;
		let validItems = 0;
		let invalidItems = 0;

		// Calculate based on stage type
		switch (stage)
		{
			case 'source-fetch':
				totalItems = data?.candidates?.length || 0;
				break;

			case 'normalization':
				totalItems = data?.normalizedDomains?.length || 0;
				break;

			case 'existence-check':
				if (data?.results instanceof Map)
				{
					totalItems = data.results.size;
				}
				else if (data?.results && typeof data.results === 'object')
				{
					totalItems = Object.keys(data.results).length;
				}
				break;

			case 'enqueue':
				totalItems = data?.domains?.length || 0;
				break;
		}

		if (validationResult.isValid)
		{
			validItems = totalItems;
		}
		else
		{
			// Count errors that reference specific indices
			const indexErrors = validationResult.errors.filter(error => error.includes('at index'));
			const uniqueIndices = new Set(
				indexErrors.map((error) =>
				{
					const match = error.match(/at index (\d+)/);
					return match ? Number.parseInt(match[1], 10) : -1;
				}).filter(index => index >= 0),
			);

			invalidItems = uniqueIndices.size;
			validItems = Math.max(0, totalItems - invalidItems);
		}

		return {
			totalItems,
			validItems,
			invalidItems,
			recoveredItems: 0, // This would be calculated from recovery results
			quarantinedItems: 0, // This would be calculated from recovery results
		};
	}

	private logValidationResult(result: EnhancedValidationResultType): void
	{
		const logData = {
			stage: result.stage,
			isValid: result.isValid,
			errorCount: result.errors.length,
			warningCount: result.warnings.length,
			processingTime: result.processingTime,
			recoveryApplied: result.recoveryApplied,
			metrics: result.validationMetrics,
		};

		if (result.isValid)
		{
			this.logger.info(`✅ Validation passed for stage: ${result.stage}`, logData);
		}
		else
		{
			this.logger.error(`❌ Validation failed for stage: ${result.stage}`, {
				...logData,
				errors: result.errors,
				warnings: result.warnings,
			});
		}

		if (result.recoveryApplied && result.recoveryResult)
		{
			this.logger.info(`🔧 Recovery applied for stage: ${result.stage}`, {
				strategies: result.recoveryResult.appliedStrategies,
				remainingErrors: result.recoveryResult.remainingErrors.length,
			});
		}
	}
}

// Utility functions for common validation scenarios
class ValidationUtils
{
	static async validateSourceFetchData(
		data: any,
		config?: Partial<ValidationManagerConfigType>,
	): Promise<EnhancedValidationResultType>
	{
		const manager = new ValidationManager(config);
		return manager.validatePipelineStage('source-fetch', data);
	}

	static async validateNormalizationData(
		data: any,
		config?: Partial<ValidationManagerConfigType>,
	): Promise<EnhancedValidationResultType>
	{
		const manager = new ValidationManager(config);
		return manager.validatePipelineStage('normalization', data);
	}

	static async validateExistenceCheckData(
		data: unknown,
		config?: Partial<ValidationManagerConfigType>,
	): Promise<EnhancedValidationResultType>
	{
		const manager = new ValidationManager(config);
		return manager.validatePipelineStage('existence-check', data);
	}

	static async validateEnqueueData(
		data: unknown,
		config?: Partial<ValidationManagerConfigType>,
	): Promise<EnhancedValidationResultType>
	{
		const manager = new ValidationManager(config);
		return manager.validatePipelineStage('enqueue', data);
	}

	static createStrictValidator(config?: Partial<ValidationManagerConfigType>): ValidationManager
	{
		return new ValidationManager({
			...config,
			enableStrictMode: true,
			enableRecovery: false,
		});
	}

	static createLenientValidator(config?: Partial<ValidationManagerConfigType>): ValidationManager
	{
		return new ValidationManager({
			...config,
			enableStrictMode: false,
			enableRecovery: true,
		});
	}
}

export type { ValidationManagerConfigType, EnhancedValidationResultType };

export { ValidationUtils, ValidationManager };

export default ValidationManager;
