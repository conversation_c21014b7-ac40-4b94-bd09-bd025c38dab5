/* eslint-disable max-classes-per-file */
import { logger as sharedLogger, type LoggerInstanceType } from '@shared';
import type { ValidationResultType } from '../interfaces/ReliabilityManager';
import type { SanitizedDataType, PipelineStageDataType } from './ValidationPipeline';

type RecoveryStrategyType =
{
	name: string;
	description: string;
	canRecover: (errors: string[], stage: string) => boolean;
	recover: (data: any, errors: string[], stage: string) => Promise<RecoveryResultType>;
};

type RecoveryResultType =
{
	success: boolean;
	recoveredData?: any;
	appliedStrategies: string[];
	remainingErrors: string[];
	warnings: string[];
};

type RecoveryConfigType =
{
	maxRecoveryAttempts: number;
	enablePartialRecovery: boolean;
	quarantineInvalidData: boolean;
	logRecoveryAttempts: boolean;
};

class MissingFieldRecoveryStrategy implements RecoveryStrategyType
{
	name = 'MissingFieldRecovery';

	description = 'Adds missing required fields with default values';

	canRecover(errors: string[], stage: string): boolean
	{
		return errors.some(error => error.includes('missing') ||
			error.includes('must contain') ||
			error.includes('required'));
	}

	async recover(data: any, errors: string[], stage: string): Promise<RecoveryResultType>
	{
		const appliedStrategies: string[] = [];
		const warnings: string[] = [];
		const remainingErrors: string[] = [];
		const recoveredData = { ...data };

		for (const error of errors)
		{
			let recovered = false;

			// Handle missing candidates array
			if (error.includes('must contain candidates array'))
			{
				recoveredData.candidates = [];
				appliedStrategies.push('Added empty candidates array');
				warnings.push('Added empty candidates array - no data will be processed');
				recovered = true;
			}
			// Handle missing source field
			else if (error.includes('must contain valid source field'))
			{
				recoveredData.source = 'unknown';
				appliedStrategies.push('Added default source field');
				warnings.push('Added default source "unknown"');
				recovered = true;
			}
			// Handle missing timestamp fields
			else if (error.includes('fetchedAt') || error.includes('processedAt') || error.includes('checkedAt') || error.includes('enqueuedAt'))
			{
				const timestampField = error.match(/(fetchedAt|processedAt|checkedAt|enqueuedAt)/)?.[1];
				if (timestampField)
				{
					recoveredData[timestampField] = new Date();
					appliedStrategies.push(`Added current timestamp for ${timestampField}`);
					recovered = true;
				}
			}
			// Handle missing results field
			else if (error.includes('must contain results'))
			{
				recoveredData.results = new Map();
				appliedStrategies.push('Added empty results Map');
				warnings.push('Added empty results - no existence data available');
				recovered = true;
			}
			// Handle missing domains array
			else if (error.includes('must contain domains array'))
			{
				recoveredData.domains = [];
				appliedStrategies.push('Added empty domains array');
				warnings.push('Added empty domains array - no data will be enqueued');
				recovered = true;
			}
			// Handle missing normalizedDomains array
			else if (error.includes('must contain normalizedDomains array'))
			{
				recoveredData.normalizedDomains = [];
				appliedStrategies.push('Added empty normalizedDomains array');
				warnings.push('Added empty normalizedDomains array - no normalized data available');
				recovered = true;
			}

			if (!recovered)
			{
				remainingErrors.push(error);
			}
		}

		return {
			success: appliedStrategies.length > 0,
			recoveredData: appliedStrategies.length > 0 ? recoveredData : undefined,
			appliedStrategies,
			remainingErrors,
			warnings,
		};
	}
}

class InvalidFormatRecoveryStrategy implements RecoveryStrategyType
{
	name = 'InvalidFormatRecovery';

	description = 'Attempts to fix invalid data formats';

	canRecover(errors: string[], stage: string): boolean
	{
		return errors.some(error => error.includes('Invalid') ||
			error.includes('format') ||
			error.includes('timestamp'));
	}

	async recover(data: any, errors: string[], stage: string): Promise<RecoveryResultType>
	{
		const appliedStrategies: string[] = [];
		const warnings: string[] = [];
		const remainingErrors: string[] = [];
		const recoveredData = { ...data };

		for (const error of errors)
		{
			let recovered = false;

			// Handle invalid timestamp formats
			if (error.includes('timestamp format'))
			{
				const timestampField = error.match(/(fetchedAt|processedAt|checkedAt|enqueuedAt|seenAt)/)?.[1];
				if (timestampField)
				{
					// Try to recover timestamp
					const currentValue = this.getNestedValue(recoveredData, timestampField);
					if (currentValue)
					{
						try
						{
							const recoveredDate = this.parseFlexibleDate(currentValue);
							this.setNestedValue(recoveredData, timestampField, recoveredDate);
							appliedStrategies.push(`Recovered timestamp format for ${timestampField}`);
							recovered = true;
						}
						catch
						{
							// If we can't parse it, use current time
							this.setNestedValue(recoveredData, timestampField, new Date());
							appliedStrategies.push(`Replaced invalid timestamp with current time for ${timestampField}`);
							warnings.push(`Could not parse timestamp ${currentValue}, using current time`);
							recovered = true;
						}
					}
				}
			}
			// Handle invalid domain formats - try basic cleanup
			else if (error.includes('Invalid domain format') || error.includes('Invalid eTLD+1 format'))
			{
				// This is more complex and might require domain-specific logic
				// For now, we'll mark it as unrecoverable but log it
				warnings.push(`Domain format error detected but not automatically recoverable: ${error}`);
			}

			if (!recovered)
			{
				remainingErrors.push(error);
			}
		}

		return {
			success: appliedStrategies.length > 0,
			recoveredData: appliedStrategies.length > 0 ? recoveredData : undefined,
			appliedStrategies,
			remainingErrors,
			warnings,
		};
	}

	private parseFlexibleDate(value: any): Date
	{
		if (value instanceof Date)
		{
			return value;
		}

		if (typeof value === 'string')
		{
			// Try ISO format first
			let date = new Date(value);
			if (!Number.isNaN(date.getTime()))
			{
				return date;
			}

			// Try Unix timestamp (seconds)
			const timestamp = Number(value);
			if (!Number.isNaN(timestamp))
			{
				date = new Date(timestamp * 1000);
				if (!Number.isNaN(date.getTime()))
				{
					return date;
				}
			}
		}

		if (typeof value === 'number')
		{
			// Try as milliseconds first
			let date = new Date(value);
			if (!Number.isNaN(date.getTime()) && date.getFullYear() > 1970)
			{
				return date;
			}

			// Try as seconds
			date = new Date(value * 1000);
			if (!Number.isNaN(date.getTime()) && date.getFullYear() > 1970)
			{
				return date;
			}
		}

		throw new Error(`Cannot parse date: ${value}`);
	}

	private getNestedValue(obj: any, path: string): any
	{
		return path.split('.').reduce((current, key) => current?.[key], obj);
	}

	private setNestedValue(obj: any, path: string, value: any): void
	{
		const keys = path.split('.');
		const lastKey = keys.pop()!;
		const target = keys.reduce((current, key) =>
		{
			if (!current[key] || typeof current[key] !== 'object')
			{
				current[key] = {};
			}
			return current[key];
		}, obj);
		target[lastKey] = value;
	}
}

class TypeConversionRecoveryStrategy implements RecoveryStrategyType
{
	name = 'TypeConversionRecovery';

	description = 'Converts data to expected types when possible';

	canRecover(errors: string[], stage: string): boolean
	{
		return errors.some(error => error.includes('must be') ||
			error.includes('got:') ||
			error.includes('type'));
	}

	async recover(data: any, errors: string[], stage: string): Promise<RecoveryResultType>
	{
		const appliedStrategies: string[] = [];
		const warnings: string[] = [];
		const remainingErrors: string[] = [];
		const recoveredData = JSON.parse(JSON.stringify(data)); // Deep copy

		for (const error of errors)
		{
			let recovered = false;

			// Handle boolean type conversions
			if (error.includes('must be boolean'))
			{
				const match = error.match(/for (\w+)/);
				if (match)
				{
					const field = match[1];
					const value = this.getNestedValue(recoveredData, field);
					const boolValue = this.convertToBoolean(value);
					if (boolValue !== null)
					{
						this.setNestedValue(recoveredData, field, boolValue);
						appliedStrategies.push(`Converted ${field} to boolean`);
						recovered = true;
					}
				}
			}
			// Handle number type conversions
			else if (error.includes('must be number') || error.includes('Invalid rank'))
			{
				// Extract field name and try conversion
				const match = error.match(/(\w+).*must be number|Invalid rank.*: (.+)/);
				if (match)
				{
					const field = match[1] || 'rank';
					const value = this.getNestedValue(recoveredData, field);
					const numValue = this.convertToNumber(value);
					if (numValue !== null)
					{
						this.setNestedValue(recoveredData, field, numValue);
						appliedStrategies.push(`Converted ${field} to number`);
						recovered = true;
					}
				}
			}

			if (!recovered)
			{
				remainingErrors.push(error);
			}
		}

		return {
			success: appliedStrategies.length > 0,
			recoveredData: appliedStrategies.length > 0 ? recoveredData : undefined,
			appliedStrategies,
			remainingErrors,
			warnings,
		};
	}

	private convertToBoolean(value: any): boolean | null
	{
		if (typeof value === 'boolean')
		{
			return value;
		}

		if (typeof value === 'string')
		{
			const lower = value.toLowerCase();
			if (lower === 'true' || lower === '1' || lower === 'yes')
			{
				return true;
			}
			if (lower === 'false' || lower === '0' || lower === 'no')
			{
				return false;
			}
		}

		if (typeof value === 'number')
		{
			return value !== 0;
		}

		return null;
	}

	private convertToNumber(value: any): number | null
	{
		if (typeof value === 'number')
		{
			return value;
		}

		if (typeof value === 'string')
		{
			const num = Number(value);
			if (!Number.isNaN(num))
			{
				return num;
			}
		}

		return null;
	}

	private getNestedValue(obj: any, path: string): any
	{
		return path.split('.').reduce((current, key) => current?.[key], obj);
	}

	private setNestedValue(obj: any, path: string, value: any): void
	{
		const keys = path.split('.');
		const lastKey = keys.pop()!;
		const target = keys.reduce((current, key) =>
		{
			if (!current[key] || typeof current[key] !== 'object')
			{
				current[key] = {};
			}
			return current[key];
		}, obj);
		target[lastKey] = value;
	}
}

class PartialDataRecoveryStrategy implements RecoveryStrategyType
{
	name = 'PartialDataRecovery';

	description = 'Recovers valid portions of data while quarantining invalid items';

	canRecover(errors: string[], stage: string): boolean
	{
		// This strategy can always attempt recovery by filtering out invalid items
		return errors.some(error => error.includes('at index'));
	}

	async recover(data: any, errors: string[], stage: string): Promise<RecoveryResultType>
	{
		const appliedStrategies: string[] = [];
		const warnings: string[] = [];
		const remainingErrors: string[] = [];
		const recoveredData = { ...data };

		// Extract indices of invalid items
		const invalidIndices = new Set<number>();
		for (const error of errors)
		{
			const match = error.match(/at index (\d+)/);
			if (match)
			{
				invalidIndices.add(Number.parseInt(match[1], 10));
			}
			else
			{
				remainingErrors.push(error);
			}
		}

		if (invalidIndices.size > 0)
		{
			// Filter out invalid items from arrays
			if (Array.isArray(recoveredData.candidates))
			{
				const originalLength = recoveredData.candidates.length;
				recoveredData.candidates = recoveredData.candidates.filter((_, index) => !invalidIndices.has(index));
				const removedCount = originalLength - recoveredData.candidates.length;
				if (removedCount > 0)
				{
					appliedStrategies.push(`Removed ${removedCount} invalid candidates`);
					warnings.push(`Quarantined ${removedCount} invalid candidates out of ${originalLength}`);
				}
			}

			if (Array.isArray(recoveredData.normalizedDomains))
			{
				const originalLength = recoveredData.normalizedDomains.length;
				recoveredData.normalizedDomains = recoveredData.normalizedDomains.filter((_, index) => !invalidIndices.has(index));
				const removedCount = originalLength - recoveredData.normalizedDomains.length;
				if (removedCount > 0)
				{
					appliedStrategies.push(`Removed ${removedCount} invalid normalized domains`);
					warnings.push(`Quarantined ${removedCount} invalid normalized domains out of ${originalLength}`);
				}
			}

			if (Array.isArray(recoveredData.domains))
			{
				const originalLength = recoveredData.domains.length;
				recoveredData.domains = recoveredData.domains.filter((_, index) => !invalidIndices.has(index));
				const removedCount = originalLength - recoveredData.domains.length;
				if (removedCount > 0)
				{
					appliedStrategies.push(`Removed ${removedCount} invalid enqueue domains`);
					warnings.push(`Quarantined ${removedCount} invalid domains out of ${originalLength}`);
				}
			}
		}

		return {
			success: appliedStrategies.length > 0,
			recoveredData: appliedStrategies.length > 0 ? recoveredData : undefined,
			appliedStrategies,
			remainingErrors,
			warnings,
		};
	}
}

class QuarantineRecoveryStrategy implements RecoveryStrategyType
{
	name = 'QuarantineRecovery';

	description = 'Quarantines all invalid data and returns empty valid structure';

	canRecover(errors: string[], stage: string): boolean
	{
		// This is a last resort strategy that can always "recover" by quarantining everything
		return true;
	}

	async recover(data: any, errors: string[], stage: string): Promise<RecoveryResultType>
	{
		const appliedStrategies: string[] = [];
		const warnings: string[] = [];

		// Create minimal valid structure based on stage
		let recoveredData: unknown;

		switch (stage)
		{
			case 'source-fetch':
				recoveredData = {
					candidates: [],
					source: data.source || 'quarantined',
					fetchedAt: new Date(),
					totalCount: 0,
				};
				break;

			case 'normalization':
				recoveredData = {
					normalizedDomains: [],
					originalCount: 0,
					processedAt: new Date(),
				};
				break;

			case 'existence-check':
				recoveredData = {
					results: new Map(),
					checkedAt: new Date(),
					totalChecked: 0,
					foundCount: 0,
				};
				break;

			case 'enqueue':
				recoveredData = {
					domains: [],
					enqueuedAt: new Date(),
					totalEnqueued: 0,
				};
				break;

			default:
				recoveredData = {};
		}

		appliedStrategies.push('Quarantined all invalid data');
		warnings.push(`All data quarantined due to validation errors in stage: ${stage}`);
		warnings.push(`Original errors: ${errors.join('; ')}`);

		return {
			success: true,
			recoveredData,
			appliedStrategies,
			remainingErrors: [], // All errors are "resolved" by quarantining
			warnings,
		};
	}
}

class ErrorRecoveryManager
{
	private strategies: RecoveryStrategyType[];
	private config: RecoveryConfigType;
	private logger = sharedLogger.getLogger('ErrorRecoveryManager');

	constructor(config: Partial<RecoveryConfigType> = {})
	{
		this.config =
		{
			maxRecoveryAttempts: 3,
			enablePartialRecovery: true,
			quarantineInvalidData: true,
			logRecoveryAttempts: true,
			...config,
		};

		this.strategies = [
			new MissingFieldRecoveryStrategy(),
			new InvalidFormatRecoveryStrategy(),
			new TypeConversionRecoveryStrategy(),
			new PartialDataRecoveryStrategy(),
			new QuarantineRecoveryStrategy(),
		];
	}

	async attemptRecovery<T extends keyof PipelineStageDataType>(
		stage: T,
		data: any,
		validationResult: ValidationResultType,
	): Promise<RecoveryResultType>
	{
		if (validationResult.isValid)
		{
			return {
				success: true,
				recoveredData: data,
				appliedStrategies: [],
				remainingErrors: [],
				warnings: validationResult.warnings,
			};
		}

		let currentData = data;
		let remainingErrors = [...validationResult.errors];
		const appliedStrategies: string[] = [];
		const warnings = [...validationResult.warnings];
		let attempts = 0;

		while (remainingErrors.length > 0 && attempts < this.config.maxRecoveryAttempts)
		{
			attempts++;
			let recoveryApplied = false;

			for (const strategy of this.strategies)
			{
				if (strategy.canRecover(remainingErrors, stage))
				{
					try
					{
						const result = await strategy.recover(currentData, remainingErrors, stage);

						if (result.success && result.recoveredData)
						{
							currentData = result.recoveredData;
							appliedStrategies.push(...result.appliedStrategies);
							warnings.push(...result.warnings);

							// Remove errors that were successfully recovered
							remainingErrors = result.remainingErrors;
							recoveryApplied = true;

							if (this.config.logRecoveryAttempts)
							{
								this.logger.info('Recovery strategy applied successfully', {
									strategy: strategy.name,
									stage,
									originalErrors: validationResult.errors.length,
									remainingErrors: result.remainingErrors.length,
									recoveredErrors: validationResult.errors.length - result.remainingErrors.length,
									warnings: result.warnings.length,
								});
							}

							break; // Try one strategy at a time
						}
					}
					catch (error)
					{
						warnings.push(`Recovery strategy ${strategy.name} failed: ${error.message}`);
					}
				}
			}

			// If no recovery was applied in this iteration, break to avoid infinite loop
			if (!recoveryApplied)
			{
				break;
			}
		}

		const success = remainingErrors.length === 0 || (this.config.enablePartialRecovery && appliedStrategies.length > 0);

		return {
			success,
			recoveredData: success ? currentData : undefined,
			appliedStrategies,
			remainingErrors,
			warnings,
		};
	}

	addRecoveryStrategy(strategy: RecoveryStrategyType): void
	{
		this.strategies.push(strategy);
	}

	removeRecoveryStrategy(strategyName: string): boolean
	{
		const index = this.strategies.findIndex(s => s.name === strategyName);
		if (index >= 0)
		{
			this.strategies.splice(index, 1);
			return true;
		}
		return false;
	}
}

export type { RecoveryStrategyType, RecoveryResultType, RecoveryConfigType };

export default ErrorRecoveryManager;
