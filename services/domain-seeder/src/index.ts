/**
 * Domain Seeder Service - Main Entry Point
 * Orchestrates all domain discovery and seeding operations
 */

import { config } from 'dotenv';
import express from 'ultimate-express';
import type { Request, Response } from 'ultimate-express';
import { logger } from '@shared';
import {
	SeederDatabaseManager,
	SeederLogger,
	SeederConfig,
	SeederJobQueue,
} from './services/SharedServiceIntegration';
import { MetricsCollector } from './monitoring/MetricsCollector';
import { HealthChecker } from './monitoring/HealthChecker';
import SeederDataSyncService from './services/SeederDataSyncService';

// Modular components
import { HttpServer } from './core/HttpServer';
import { DiscoveryOperations } from './core/DiscoveryOperations';
import { GracefulShutdown } from './core/GracefulShutdown';

// Load environment variables
config();

interface DomainSeederServiceType {
	initialize(): Promise<void>;
	start(): Promise<void>;
	stop(): Promise<void>;
	getHealthChecker(): HealthChecker;
	getMetricsCollector(): MetricsCollector;
	getDatabaseManager(): SeederDatabaseManager;
}

class DomainSeederService implements DomainSeederServiceType
{
	private logger: SeederLogger;
	private config: SeederConfig;
	private dbManager!: SeederDatabaseManager;
	private jobQueue!: SeederJobQueue;
	private metricsCollector!: MetricsCollector;
	private healthChecker!: HealthChecker;
	private dataSyncService?: SeederDataSyncService;
	private httpServer!: HttpServer;
	private discoveryOps!: DiscoveryOperations;
	private gracefulShutdown: GracefulShutdown;
	private expressApp: any; // For backward compatibility
	private isInitialized = false;
	private isRunning = false;

	constructor()
	{
		this.logger = new SeederLogger('domain-seeder');
		this.config = SeederConfig.getInstance();
		this.gracefulShutdown = new GracefulShutdown(30000);
	}

	async initialize(): Promise<void>
	{
		if (this.isInitialized)
		{
			this.logger.warn({ msg: 'Service already initialized' });
			return;
		}

		try
		{
			this.logger.info({ msg: 'Initializing Domain Seeder Service...' });

			// Initialize database manager

			this.dbManager = new SeederDatabaseManager(this.config, this.logger.getLogger());
			await this.dbManager.initialize();

			// Initialize services
			const redis = this.dbManager.getRedisClient();
			let manticoreAvailable = true;
			let manticore: any = null;
			try
			{
				manticore = this.dbManager.getManticoreClient();
			}
			catch (e)
			{
				manticoreAvailable = false;
				this.logger.warn({ msg: 'Manticore not available, continuing in degraded mode' });
			}

			this.jobQueue = new SeederJobQueue(redis, logger);
			this.metricsCollector = new MetricsCollector(redis);
			if (manticoreAvailable)
			{
				try
				{
					this.dataSyncService = new SeederDataSyncService(manticore, logger);
				}
				catch (e)
				{
					this.logger.warn({ msg: 'Failed to initialize data sync service. Continuing without it.', error: e instanceof Error ? e.message : String(e) });
					this.dataSyncService = undefined;
				}
			}

			// Initialize health checker
			this.healthChecker = new HealthChecker();

			// Initialize HTTP server
			this.httpServer = new HttpServer(
				this.healthChecker,
				this.metricsCollector,
				this.config.getNumber('PORT', 3004)
			);

			// Initialize discovery operations
			this.discoveryOps = new DiscoveryOperations(this.dbManager.getSharedDatabaseManager());

			// Register shutdown handlers
			this.setupShutdownHandlers();

			this.isInitialized = true;
			this.logger.info({ msg: 'Domain Seeder Service initialized successfully' });
		}
		catch (error)
		{
			this.logger.error({ msg: 'Failed to initialize Domain Seeder Service', error: error instanceof Error ? error.message : String(error) });
			throw error;
		}
	}

	async start(): Promise<void>
	{
		if (!this.isInitialized)
		{
			await this.initialize();
		}

		if (this.isRunning)
		{
			this.logger.warn({ msg: 'Service already running' });
			return;
		}

		try
		{
			this.logger.info({ msg: 'Starting Domain Seeder Service...' });

			// Perform health check before starting
			const health = await this.healthChecker.checkHealth();
			if (health.status === 'unhealthy')
			{
				throw new Error('Service failed health check before start');
			}

			// Start HTTP server for health checks and metrics
			await this.startHttpServer();

			// Set up graceful shutdown handlers
			this.setupGracefulShutdown();

			this.isRunning = true;
			this.logger.info({ msg: 'Domain Seeder Service started successfully' });
		}
		catch (error)
		{
			this.logger.error({ msg: 'Failed to start Domain Seeder Service', error: error instanceof Error ? error.message : String(error) });
			throw error;
		}
	}

	async stop(): Promise<void>
	{
		if (!this.isRunning)
		{
			this.logger.warn({ msg: 'Service not running' });
			return;
		}

		try
		{
			this.logger.info({ msg: 'Stopping Domain Seeder Service...' });

			// Stop HTTP server
			if (this.httpServer)
			{
				await this.httpServer.stop();
			}

			// Close database connections
			if (this.dbManager)
			{
				await this.dbManager.close();
			}

			this.isRunning = false;
			this.logger.info({ msg: 'Domain Seeder Service stopped successfully' });
		}
		catch (error)
		{
			this.logger.error({ msg: 'Error stopping Domain Seeder Service', error: error instanceof Error ? error.message : String(error) });
			throw error;
		}
	}

	private async startHttpServer(): Promise<void>
	{
		// Create Express app for backward compatibility with existing API endpoints
		this.expressApp = express();

		// Add JSON body parser for POST requests
		this.expressApp.use(express.json({ limit: '10mb' }));
		this.expressApp.use(express.urlencoded({ extended: true, limit: '10mb' }));

		// Health check endpoint
		this.expressApp.get('/health', async (req: Request, res: Response) =>
		{
			try
			{
				const health = await this.healthChecker.checkHealth();
				const httpStatus = health.status === 'healthy' ? 200 : (health.status === 'degraded' ? 200 : 503);
				res.status(httpStatus).json(health);
			}
			catch (error)
			{
				this.logger.error({ msg: 'Health check error', error: error instanceof Error ? error.message : String(error) });
				res.status(503).json({ status: 'unhealthy', error: error.message });
			}
		});

		// Metrics endpoint
		this.expressApp.get('/metrics', async (req: Request, res: Response) =>
		{
			try
			{
				const metrics = this.metricsCollector.getSeederMetrics();
				res.json(metrics);
			}
			catch (error)
			{
				this.logger.error({ msg: 'Metrics retrieval error', error: error instanceof Error ? error.message : String(error) });
				res.status(500).json({ error: error.message });
			}
		});

		// Discovery API endpoints
		this.expressApp.post('/api/discovery/run', async (req: Request, res: Response) =>
		{
			try
			{
				const result = await this.triggerDiscoveryRun(req.body);
				res.json(result);
			}
			catch (error)
			{
				this.logger.error({ msg: 'Discovery run error', error: error instanceof Error ? error.message : String(error) });
				res.status(500).json({ error: error.message });
			}
		});

		this.expressApp.post('/api/discovery/top-sources', async (req: Request, res: Response) =>
		{
			try
			{
				const result = await this.triggerTopSourcesRun(req.body);
				res.json(result);
			}
			catch (error)
			{
				this.logger.error({ msg: 'Top sources run error', error: error instanceof Error ? error.message : String(error) });
				res.status(500).json({ error: error.message });
			}
		});

		this.expressApp.post('/api/discovery/backfill', async (req: Request, res: Response) =>
		{
			try
			{
				const result = await this.triggerBackfillRun(req.body);
				res.json(result);
			}
			catch (error)
			{
				this.logger.error({ msg: 'Backfill run error', error: error instanceof Error ? error.message : String(error) });
				res.status(500).json({ error: error.message });
			}
		});

		// Status endpoint
		this.expressApp.get('/api/status', async (req: Request, res: Response) =>
		{
			try
			{
				const status = {
					service: 'domain-seeder',
					initialized: this.isInitialized,
					running: this.isRunning,
					timestamp: new Date().toISOString(),
					health: await this.healthChecker.checkHealth(),
					metrics: this.metricsCollector.getSeederMetrics(),
				};
				res.json(status);
			}
			catch (error)
			{
				this.logger.error({ msg: 'Status endpoint error', error: error instanceof Error ? error.message : String(error) });
				res.status(500).json({ error: error.message });
			}
		});

		// Job queue status endpoint
		this.expressApp.get('/api/queue/status', async (req: Request, res: Response) =>
		{
			try
			{
				const queueStatus = {
					normal: await this.jobQueue.getQueueDepth('new:domains'),
					high: await this.jobQueue.getQueueDepth('new:domains:high'),
					healthy: await this.jobQueue.healthCheck()
				};
				res.json(queueStatus);
			}
			catch (error)
			{
				this.logger.error({ msg: 'Queue status error', error: error instanceof Error ? error.message : String(error) });
				res.status(500).json({ error: error.message });
			}
		});

		// Domain exists endpoint (Manticore backed)
		this.expressApp.get('/api/domain/exists', async (req: Request, res: Response) =>
		{
			try
			{
				const raw = String((req.query as any).domain || '').trim().toLowerCase();
				if (!raw)
				{
					res.status(400).json({ error: 'domain is required' });
					return;
				}
				const domain = raw.replace(/'/g, "''");
				const m = this.dbManager.getManticoreClient();
				const sql = `SELECT 1 FROM domains_index WHERE domain='${domain}' LIMIT 1`;
				const result = await m.sql(sql);
				const data = (result as any).data;
				const exists = Array.isArray(data) ? data.length > 0 : Boolean((result as any).total);
				res.json({ domain: raw, exists });
			}
			catch (error)
			{
				this.logger.error({ msg: 'Domain exists check failed', error: error instanceof Error ? error.message : String(error) });
				res.status(500).json({ error: 'Internal error' });
			}
		});

		// Start the HTTP server using the new module
		await this.httpServer.start();

		// Also start the Express app for backward compatibility
		const port = this.config.getNumber('PORT', 3004);
		const apiPort = this.config.getNumber('API_PORT', port + 1); // Use different port for API

		this.expressApp.listen(apiPort, () =>
		{
			this.logger.info({ msg: `API server listening on port ${apiPort}` });
		});
	}

	private setupGracefulShutdown(): void
	{
		// Use the new GracefulShutdown module
		this.gracefulShutdown.setupSignalHandlers();
	}

	private setupShutdownHandlers(): void
	{
		// Register cleanup handlers in order
		this.gracefulShutdown.registerHandler('HTTP Server', async () =>
		{
			if (this.httpServer)
			{
				await this.httpServer.stop();
			}
		});

		this.gracefulShutdown.registerHandler('Express App', async () =>
		{
			// Close Express app if needed
			this.logger.info({ msg: 'Closing Express app' });
		});

		this.gracefulShutdown.registerHandler('Job Queue', async () =>
		{
			if (this.jobQueue)
			{
				// Implement job queue shutdown if needed
				this.logger.info({ msg: 'Job queue shutdown placeholder' });
			}
		});

		this.gracefulShutdown.registerHandler('Database Connections', async () =>
		{
			if (this.dbManager)
			{
				await this.dbManager.close();
			}
		});
	}

	// Discovery operation methods - delegate to DiscoveryOperations
	private async triggerDiscoveryRun(parameters?: any): Promise<any>
	{
		return this.discoveryOps.triggerDiscoveryRun(parameters);
	}

	private async triggerTopSourcesRun(parameters?: any): Promise<any>
	{
		return this.discoveryOps.triggerTopSourcesRun(parameters);
	}

	private async triggerBackfillRun(parameters?: any): Promise<any>
	{
		return this.discoveryOps.triggerBackfillRun(parameters);
	}

	// Public getters for compatibility
	getHealthChecker(): HealthChecker
	{
		return this.healthChecker;
	}

	getMetricsCollector(): MetricsCollector
	{
		return this.metricsCollector;
	}

	getDatabaseManager(): SeederDatabaseManager
	{
		return this.dbManager;
	}

	// Additional getters for CLI compatibility
	getJobQueue(): SeederJobQueue
	{
		return this.jobQueue;
	}

	getDataSyncService(): SeederDataSyncService | undefined
	{
		return this.dataSyncService;
	}

	getConfig(): SeederConfig
	{
		return this.config;
	}

	getLogger(): SeederLogger
	{
		return this.logger;
	}
}

// Main function to start the service
async function main(): Promise<void>
{
	const service = new DomainSeederService();

	try
	{
		await service.initialize();
		await service.start();

		// Export service instance for CLI and other modules
		(global as any).domainSeederService = service;
	}
	catch (error)
	{
		logger.getLogger('domain-seeder').error('Failed to start service:', error);
		process.exit(1);
	}
}

// Run if this file is executed directly
if (require.main === module)
{
	main().catch((error) =>
	{
		logger.getLogger('domain-seeder').error('Unhandled error in main:', error);
		process.exit(1);
	});
}

// Export for use in other modules
export {
	DomainSeederService,
	main,
};

export default DomainSeederService;

// Re-export types for backward compatibility
export type * from './interfaces/SourceConnector';
export type * from './interfaces/DiscoveryEngine';
export type * from './interfaces/DomainNormalizer';
export type * from './interfaces/ReliabilityManager';
export type * from './ratelimiting';
