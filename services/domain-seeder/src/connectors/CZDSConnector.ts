import { createReadStream, createWriteStream } from 'fs';
import { pipeline } from 'stream/promises';
import { createGunzip } from 'zlib';
import { logger as sharedLogger, type LoggerInstanceType, AsyncUtils } from '@shared';
import HttpClient from '@shared/utils/HttpClient';
import { HttpClientStream } from '@shared/utils/HttpClientStream';
import type { DiscoveryStrategyType } from '../interfaces/DiscoveryEngine';
import type {
	DomainCandidateType,
	FetchOptionsType,
	SourceConnectorType,
} from '../interfaces/SourceConnector';

type CZDSConfigType =
{
	baseUrl: string;
	username: string;
	password: string;
	maxRetries: number;
	retryDelayMs: number;
	requestTimeoutMs: number;
	rateLimitDelayMs: number;
	tempDir: string;
	registrationCutoffHours: number;
};

type ZoneFileInfoType =
{
	tld: string;
	url: string;
	lastModified: Date;
	size: number;
};

type ZoneRecordType =
{
	domain: string;
	recordType: string;
	value: string;
	ttl?: number;
};

type ZoneDiffType =
{
	added: string[];
	removed: string[];
	modified: string[];
};

type ZoneResponseDataType =
{
	zone: string;
	downloadUrl: string;
	lastModified: string;
	size: number;
};

class CZDSConnector implements SourceConnectorType
{
	public readonly name = 'czds';

	public readonly priority = 4;

	public readonly cadence = 'daily' as const;

	private readonly config: CZDSConfigType;

	private readonly logger: LoggerInstanceType;

	private readonly client: HttpClient;

	private lastFetchTime: Date | null = null;

	private authToken: string | null = null;

	private tokenExpiry: Date | null = null;

	private availableZones: ZoneFileInfoType[] = [];

	constructor(config?: Partial<CZDSConfigType>)
	{
		this.config = {
			baseUrl: 'https://czds-api.icann.org',
			username: process.env.CZDS_USERNAME || '',
			password: process.env.CZDS_PASSWORD || '',
			maxRetries: 3,
			retryDelayMs: 1000,
			requestTimeoutMs: 60000, // Zone files can be large
			rateLimitDelayMs: 100,
			tempDir: '/tmp',
			registrationCutoffHours: 48, // Only include domains registered in last 48 hours
			...config,
		};

		this.logger = sharedLogger.getLogger('CZDSConnector');
		this.client = new HttpClient(this.logger, {
			timeout: this.config.requestTimeoutMs,
		});

		if (!this.config.username || !this.config.password)
		{
			throw new Error('CZDS credentials not provided. Set CZDS_USERNAME and CZDS_PASSWORD environment variables.');
		}
	}

	supportsStrategy(strategy: DiscoveryStrategyType): boolean
	{
		// CZDS primarily supports zone-new strategy for newly registered domains
		return strategy === 'zone-new';
	}

	async* fetchDomains(options: FetchOptionsType): AsyncIterable<DomainCandidateType>
	{
		this.logger.info({ options }, 'Starting CZDS domain fetch');

		try
		{
			// Ensure we have a valid authentication token
			await this.ensureAuthenticated();

			// Get available zone files
			await this.refreshAvailableZones();

			// Process zone files based on strategy
			yield* this.processZoneFiles(options);

			this.lastFetchTime = new Date();
			this.logger.info('CZDS domain fetch completed successfully');
		}
		catch (error)
		{
			this.logger.error({ error: (error as Error).message }, 'CZDS domain fetch failed');
			throw error;
		}
	}

	async getLastUpdate(): Promise<Date | null>
	{
		return this.lastFetchTime;
	}

	async healthCheck(): Promise<boolean>
	{
		try
		{
			await this.ensureAuthenticated();
			return this.authToken !== null;
		}
		catch (error)
		{
			this.logger.error({ error: (error as Error).message }, 'CZDS health check failed');
			return false;
		}
	}

	private async ensureAuthenticated(): Promise<void>
	{
		// Check if we have a valid token
		if (this.authToken && this.tokenExpiry && this.tokenExpiry > new Date())
		{
			return;
		}

		this.logger.info('Authenticating with CZDS API');

		const response = await this.makeRequest('/api/authenticate', {
			method: 'POST',
			data: {
				username: this.config.username,
				password: this.config.password,
			},
		});

		this.authToken = (response.data as { accessToken: string }).accessToken;

		// CZDS tokens typically expire in 24 hours
		this.tokenExpiry = new Date(Date.now() + 23 * 60 * 60 * 1000); // 23 hours to be safe

		this.logger.info('Successfully authenticated with CZDS API');
	}

	private async refreshAvailableZones(): Promise<void>
	{
		this.logger.info('Fetching available zone files');

		const response = await this.makeRequest('/api/czds/downloads/links', {
			method: 'GET',
			headers: {
				Authorization: `Bearer ${this.authToken}`,
			},
		});

		const responseData = response.data as ZoneResponseDataType[];
		this.availableZones = responseData.map((zone: ZoneResponseDataType) => ({
			tld: zone.zone,
			url: zone.downloadUrl,
			lastModified: new Date(zone.lastModified),
			size: zone.size,
		}));

		this.logger.info({
			count: this.availableZones.length,
			zones: this.availableZones.map(z => z.tld),
		});
	}

	private async* processZoneFiles(options: FetchOptionsType): AsyncIterable<DomainCandidateType>
	{
		const strategy = options.strategy || 'zone-new';
		const limit = options.limit;
		const since = options.since;

		let totalYielded = 0;

		// Filter zones based on options
		const zonesToProcess = this.availableZones.filter((zone) =>
		{
			if (since && zone.lastModified < since)
			{
				return false;
			}
			return true;
		});

		this.logger.info({
			totalZones: zonesToProcess.length,
			strategy,
		});

		for (const zoneInfo of zonesToProcess)
		{
			if (limit && totalYielded >= limit)
			{
				break;
			}

			try
			{
				this.logger.info({ tld: zoneInfo.tld }, 'Processing zone file');

				const remainingLimit = limit ? limit - totalYielded : undefined;
				const candidates = await this.processZoneFile(zoneInfo, strategy, remainingLimit);

				for (const candidate of candidates)
				{
					if (limit && totalYielded >= limit)
					{
						break;
					}

					yield candidate;
					totalYielded++;

					// Add rate limiting delay
					if (this.config.rateLimitDelayMs > 0)
					{
						await AsyncUtils.sleep(this.config.rateLimitDelayMs);
					}
				}
			}
			catch (error)
			{
				this.logger.error({
					tld: zoneInfo.tld,
					error: (error as Error).message,
				});
				// Continue with other zones
				continue;
			}
		}

		this.logger.info({ totalYielded }, 'Zone file processing completed');
	}

	private async processZoneFile(
		zoneInfo: ZoneFileInfoType,
		strategy: DiscoveryStrategyType,
		limit?: number,
	): Promise<DomainCandidateType[]>
	{
		// Download zone file
		const zoneFilePath = await this.downloadZoneFile(zoneInfo);

		// Parse zone file and extract domains
		const domains = await this.parseZoneFile(zoneFilePath, zoneInfo.tld, strategy, limit);

		return domains;
	}

	private async downloadZoneFile(zoneInfo: ZoneFileInfoType): Promise<string>
	{
		const fileName = `${zoneInfo.tld}-zone.txt.gz`;
		const filePath = `${this.config.tempDir}/${fileName}`;

		this.logger.info({
			tld: zoneInfo.tld,
			url: zoneInfo.url,
			size: zoneInfo.size,
		});

		// Removed unused axios-style request that was here before

		// Use streaming download to avoid memory issues with large zone files
		const streamClient = new HttpClientStream();
		await streamClient.downloadToFile(
			zoneInfo.url,
			filePath,
			{
				headers: {
					Authorization: `Bearer ${this.authToken}`,
				},
				onProgress: (downloaded: number, total: number) =>
				{
					if (total)
					{
						const percent = Math.round((downloaded / total) * 100);
						this.logger.debug(`Download progress for ${zoneInfo.tld}: ${percent}%`);
					}
				},
			}
		);

		this.logger.info({
			tld: zoneInfo.tld,
			filePath,
		});

		return filePath;
	}

	private async parseZoneFile(
		filePath: string,
		tld: string,
		strategy: DiscoveryStrategyType,
		limit?: number,
	): Promise<DomainCandidateType[]>
	{
		const candidates: DomainCandidateType[] = [];
		const cutoffDate = new Date(Date.now() - this.config.registrationCutoffHours * 60 * 60 * 1000);

		this.logger.info({
			filePath,
			tld,
			strategy,
			cutoffDate: cutoffDate.toISOString(),
		});

		return new Promise((resolve, reject) =>
		{
			const readStream = createReadStream(filePath);
			const gunzip = createGunzip();

			let lineBuffer = '';
			let processedLines = 0;
			let foundDomains = 0;

			readStream.pipe(gunzip);

			gunzip.on('data', (chunk: Buffer) =>
			{
				lineBuffer += chunk.toString();
				const lines = lineBuffer.split('\n');

				// Keep the last incomplete line in the buffer
				lineBuffer = lines.pop() || '';

				for (const line of lines)
				{
					processedLines++;

					if (limit && candidates.length >= limit)
					{
						break;
					}

					const candidate = this.parseZoneLine(line, tld, strategy, cutoffDate);
					if (candidate)
					{
						candidates.push(candidate);
						foundDomains++;
					}

					// Log progress every 100k lines
					if (processedLines % 100000 === 0)
					{
						this.logger.debug({
							tld,
							processedLines,
							foundDomains,
						});
					}
				}

				if (limit && candidates.length >= limit)
				{
					gunzip.destroy();
				}
			});

			gunzip.on('end', () =>
			{
				// Process any remaining line in buffer
				if (lineBuffer.trim())
				{
					const candidate = this.parseZoneLine(lineBuffer, tld, strategy, cutoffDate);
					if (candidate && (!limit || candidates.length < limit))
					{
						candidates.push(candidate);
						foundDomains++;
					}
				}

				this.logger.info({
					tld,
					processedLines,
					foundDomains,
					totalCandidates: candidates.length,
				});

				resolve(candidates);
			});

			gunzip.on('error', (error) =>
			{
				this.logger.error({
					tld,
					filePath,
					error: error.message,
				});
				reject(error);
			});
		});
	}

	private parseZoneLine(
		line: string,
		tld: string,
		strategy: DiscoveryStrategyType,
		cutoffDate: Date,
	): DomainCandidateType | null
	{
		try
		{
			// Skip comments and empty lines
			if (!line.trim() || line.startsWith(';'))
			{
				return null;
			}

			// Parse zone file record
			const record = this.parseZoneRecord(line);
			if (!record)
			{
				return null;
			}

			// For zone-new strategy, we're looking for newly registered domains
			if (strategy === 'zone-new')
			{
				// Extract domain name from the record
				const domain = this.extractDomainFromRecord(record, tld);
				if (!domain)
				{
					return null;
				}

				// For now, we'll include all domains since we can't easily determine
				// registration date from zone file alone. In a real implementation,
				// this would require additional WHOIS lookups or registry-specific APIs
				return {
					domain: domain.toLowerCase(),
					source: this.name,
					metadata: {
						tld,
						recordType: record.recordType,
						strategy,
						zoneFileDate: new Date().toISOString(),
						estimatedRegistration: this.estimateRegistrationAge(domain),
					},
				};
			}

			return null;
		}
		catch (error)
		{
			// Skip malformed lines
			return null;
		}
	}

	private parseZoneRecord(line: string): ZoneRecordType | null
	{
		// Skip comments and empty lines
		const trimmed = line.trim();
		if (!trimmed || trimmed.startsWith(';'))
		{
			return null;
		}

		// Basic zone file record parsing
		// Format: <domain> [TTL] [class] <type> <value>
		const parts = trimmed.split(/\s+/);

		if (parts.length < 3)
		{
			return null;
		}

		const domain = parts[0];
		let recordType = '';
		let value = '';
		let ttl: number | undefined;

		// Try to identify the record type and value
		for (let i = 1; i < parts.length; i++)
		{
			const part = parts[i];

			// Check if it's a record type
			if (['A', 'AAAA', 'CNAME', 'MX', 'NS', 'TXT', 'SOA'].includes(part.toUpperCase()))
			{
				recordType = part.toUpperCase();
				value = parts.slice(i + 1).join(' ');
				break;
			}

			// Check if it's a TTL (numeric)
			if (/^\d+$/.test(part))
			{
				ttl = parseInt(part, 10);
			}
		}

		if (!recordType)
		{
			return null;
		}

		return {
			domain,
			recordType,
			value,
			ttl,
		};
	}

	private extractDomainFromRecord(record: ZoneRecordType, tld: string): string | null
	{
		let domain = record.domain;

		// Remove trailing dot if present
		if (domain.endsWith('.'))
		{
			domain = domain.slice(0, -1);
		}

		// If domain doesn't end with TLD, append it
		if (!domain.endsWith(`.${tld}`))
		{
			domain = `${domain}.${tld}`;
		}

		// Extract eTLD+1 (remove subdomains)
		const parts = domain.split('.');
		if (parts.length >= 2)
		{
			// Return the last two parts (domain.tld)
			return parts.slice(-2).join('.');
		}

		return domain;
	}

	private async makeRequest(
		url: string,
		config?: Record<string, unknown>,
	): Promise<{
			data: unknown;
			statusCode: number;
			headers: Record<string, string>;
		}>
	{
		const fullUrl = url.startsWith('http') ? url : `${this.config.baseUrl}${url}`;

		let lastError: Error | null = null;

		for (let attempt = 1; attempt <= this.config.maxRetries; attempt++)
		{
			try
			{
				this.logger.debug({ url: fullUrl, attempt }, 'Making CZDS API request');

				// Extract method from config or default to GET
				const method = (config?.method as string)?.toUpperCase() || 'GET';

				// Extract data for POST/PUT requests
				const data = config?.data;

				// Extract headers
				const headers = config?.headers as Record<string, string> || {};

				// Set timeout
				const timeout = config?.timeout as number || this.config.requestTimeoutMs;

				// Make request based on method
				let response: {
					data: unknown;
					statusCode: number;
					headers: Record<string, string>;
				};

				switch (method) 
				{
					case 'POST':
						response = await this.client.post(fullUrl, data, { headers, timeout });
						break;
					case 'PUT':
						response = await this.client.put(fullUrl, data, { headers, timeout });
						break;
					case 'DELETE':
						response = await this.client.delete(fullUrl, { headers, timeout });
						break;
					case 'HEAD':
						response = await this.client.head(fullUrl, { headers, timeout });
						break;
					default: // GET
						response = await this.client.get(fullUrl, { headers, timeout });
						break;
				}

				return response;
			}
			catch (error)
			{
				lastError = error as Error;
				this.logger.warn({
					url: fullUrl,
					attempt,
					error: (error as Error).message,
				});

				if (attempt < this.config.maxRetries)
				{
					const delay = this.config.retryDelayMs * 2 ** (attempt - 1);
					await AsyncUtils.sleep(delay);
				}
			}
		}

		throw new Error(
			`Failed to make request after ${this.config.maxRetries} attempts: ${lastError?.message}`,
		);
	}


	/**
	 * Estimate registration age based on domain characteristics
	 */
	private estimateRegistrationAge(domain: string): string
	{
		try
		{
			// Simple heuristics for registration age estimation
			const domainParts = domain.split('.');
			const domainName = domainParts[0];

			// Very short domains (1-3 chars) are likely older
			if (domainName.length <= 3)
			{
				return 'old';
			}

			// Domains with numbers might be newer
			if (/\d/.test(domainName))
			{
				return 'recent';
			}

			// Domains with hyphens might be newer
			if (domainName.includes('-'))
			{
				return 'recent';
			}

			// Very long domains might be newer
			if (domainName.length > 15)
			{
				return 'recent';
			}

			// Common dictionary words might be older
			const commonWords = ['mail', 'www', 'ftp', 'blog', 'shop', 'news', 'info'];
			if (commonWords.some(word => domainName.includes(word)))
			{
				return 'medium';
			}

			// Default to medium age
			return 'medium';
		}
		catch (error)
		{
			// Fallback to recent if estimation fails
			return 'recent';
		}
	}
}

export type {
	CZDSConfigType,
	ZoneFileInfoType,
	ZoneRecordType,
	ZoneDiffType,
	ZoneResponseDataType,
};

export default CZDSConnector;
