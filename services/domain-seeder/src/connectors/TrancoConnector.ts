import { createReadStream, createWriteStream } from 'fs';
// import { pipeline } from 'stream/promises';
import { parse } from 'csv-parse';
import yauzl from 'yauzl';
import { logger as sharedLogger, type LoggerInstanceType, AsyncUtils } from '@shared';
import HttpClient, { type HttpResponseType } from '@shared/utils/HttpClient';
import { HttpClientStream } from '@shared/utils/HttpClientStream';
import type { DiscoveryStrategyType } from '../interfaces/DiscoveryEngine';
import type {
	DomainCandidateType,
	FetchOptionsType,
	SourceConnectorType,
} from '../interfaces/SourceConnector';

type TrancoConfigType =
{
	baseUrl: string;
	maxRetries: number;
	retryDelayMs: number;
	requestTimeoutMs: number;
	rateLimitDelayMs: number;
	tempDir: string;
};

type TrancoListInfoType =
{
	listId: string;
	date: string;
	downloadUrl: string;
};

class TrancoConnector implements SourceConnectorType
{
	public readonly name = 'tranco';

	public readonly priority = 1;

	public readonly cadence = 'daily' as const;

	private readonly config: TrancoConfigType;

	private readonly logger: LoggerInstanceType;

	private readonly client: HttpClient;

	private lastFetchTime: Date | null = null;

	private currentListInfo: TrancoListInfoType | null = null;

	constructor(config?: Partial<TrancoConfigType>)
	{
		this.config = {
			baseUrl: 'https://tranco-list.eu',
			maxRetries: 3,
			retryDelayMs: 1000,
			requestTimeoutMs: 30000,
			rateLimitDelayMs: 100,
			tempDir: '/tmp',
			...config,
		};

		this.logger = sharedLogger.getLogger('TrancoConnector');
		this.client = new HttpClient(this.logger, {
			timeout: this.config.requestTimeoutMs,
		});
	}

	supportsStrategy(strategy: DiscoveryStrategyType): boolean
	{
		// Tranco supports differential analysis and long-tail exploration
		return strategy === 'differential' || strategy === 'long-tail';
	}

	async* fetchDomains(options: FetchOptionsType): AsyncIterable<DomainCandidateType>
	{
		this.logger.info({ options });

		try
		{
			// Get current list information
			const listInfo = await this.getCurrentListInfo();
			this.currentListInfo = listInfo;

			// Download and extract the CSV file
			const csvFilePath = await this.downloadAndExtractList(listInfo);

			// Stream and parse domains based on strategy
			yield* this.streamDomains(csvFilePath, options);

			this.lastFetchTime = new Date();
			this.logger.info('Tranco domain fetch completed successfully');
		}
		catch (error)
		{
			this.logger.error({ error: (error as Error).message });
			throw error;
		}
	}

	async getLastUpdate(): Promise<Date | null>
	{
		return this.lastFetchTime;
	}

	async healthCheck(): Promise<boolean>
	{
		try
		{
			const response = await this.makeRequest('/');
			return response.status === 200;
		}
		catch (error)
		{
			this.logger.error({ error: (error as Error).message });
			return false;
		}
	}

	private async getCurrentListInfo(): Promise<TrancoListInfoType>
	{
		const response = await this.makeRequest('/list/latest');
		const html = response.data;

		// Extract list ID from the HTML response
		const listIdMatch = html.match(/\/list\/([A-Z0-9]+)/);
		if (!listIdMatch)
		{
			throw new Error('Could not extract Tranco list ID from response');
		}

		const listId = listIdMatch[1];
		const date = new Date().toISOString().split('T')[0];
		const downloadUrl = `${this.config.baseUrl}/top-1m-${listId}.csv.zip`;

		return {
			listId,
			date,
			downloadUrl,
		};
	}

	private async downloadAndExtractList(listInfo: TrancoListInfoType): Promise<string>
	{
		const zipFilePath = `${this.config.tempDir}/tranco-${listInfo.listId}.zip`;
		const csvFilePath = `${this.config.tempDir}/tranco-${listInfo.listId}.csv`;

		// Download the ZIP file
		this.logger.info({ url: listInfo.downloadUrl });

		// Use streaming download to avoid memory issues with large files
		const streamClient = new HttpClientStream();
		await streamClient.downloadToFile(
			listInfo.downloadUrl,
			zipFilePath,
			{
				onProgress: (downloaded, total) => 
				{
					if (total) 
					{
						const percent = Math.round((downloaded / total) * 100);
						this.logger.debug(`Download progress: ${percent}% (${downloaded}/${total} bytes)`);
					}
				},
			}
		);

		// Extract CSV from ZIP
		await this.extractZipFile(zipFilePath, csvFilePath);

		return csvFilePath;
	}

	private async extractZipFile(zipPath: string, outputPath: string): Promise<void>
	{
		return new Promise((resolve, reject) =>
		{
			yauzl.open(zipPath, { lazyEntries: true }, (err, zipfile) =>
			{
				if (err) return reject(err);

				zipfile.readEntry();
				zipfile.on('entry', (entry) =>
				{
					if (/\/$/.test(entry.fileName))
					{
						// Directory entry
						zipfile.readEntry();
					}
					else
					{
						// File entry
						zipfile.openReadStream(entry, (err, readStream) =>
						{
							if (err) return reject(err);

							const writeStream = createWriteStream(outputPath);
							readStream.pipe(writeStream);

							writeStream.on('close', () =>
							{
								zipfile.readEntry();
							});

							writeStream.on('error', reject);
						});
					}
				});

				zipfile.on('end', () =>
				{
					resolve();
				});

				zipfile.on('error', reject);
			});
		});
	}

	private async* streamDomains(
		csvFilePath: string,
		options: FetchOptionsType,
	): AsyncIterable<DomainCandidateType>
	{
		const strategy = options.strategy || 'differential';
		const limit = options.limit;
		const offset = options.offset || 0;

		let processedCount = 0;
		let yieldedCount = 0;

		const readStream = createReadStream(csvFilePath);
		const parser = parse({
			columns: false,
			skip_empty_lines: true,
			trim: true,
		});

		readStream.pipe(parser);

		for await (const record of parser)
		{
			try
			{
				const [rankStr, domain] = record as [string, string];
				const rank = parseInt(rankStr, 10);

				// Skip until we reach the offset
				if (processedCount < offset)
				{
					processedCount++;
					continue;
				}

				// Apply strategy-specific filtering
				if (!this.shouldIncludeDomain(rank, domain, strategy))
				{
					processedCount++;
					continue;
				}

				// Check if we've reached the limit
				if (limit && yieldedCount >= limit)
				{
					break;
				}

				const candidate: DomainCandidateType =
				{
					domain: domain.toLowerCase().trim(),
					rank,
					source: this.name,
					metadata: {
						listId: this.currentListInfo?.listId,
						listDate: this.currentListInfo?.date,
						strategy,
					},
				};

				processedCount++;
				yieldedCount++;

				yield candidate;

				// Add rate limiting delay
				if (this.config.rateLimitDelayMs > 0)
				{
					await AsyncUtils.sleep(this.config.rateLimitDelayMs);
				}
			}
			catch (error)
			{
				this.logger.error({ error: (error as Error).message, record });
				continue;
			}
		}

		this.logger.info({
			processedCount,
			yieldedCount,
			strategy,
		});
	}

	private shouldIncludeDomain(
		rank: number,
		domain: string,
		strategy: DiscoveryStrategyType,
		// options: FetchOptionsType,
	): boolean
	{
		// Basic domain validation
		if (!domain || domain.length === 0)
		{
			return false;
		}

		// Strategy-specific filtering
		switch (strategy)
		{
			case 'differential':
				// For differential analysis, include all domains (filtering happens later)
				return true;

			case 'long-tail':
				// For long-tail, only include domains ranked > 1M
				return rank > 1000000;

			default:
				return true;
		}
	}

	private async makeRequest(
		url: string,
		config?: Record<string, unknown>,
	): Promise<any>
	{
		const fullUrl = url.startsWith('http') ? url : `${this.config.baseUrl}${url}`;

		let lastError: Error | null = null;

		for (let attempt = 1; attempt <= this.config.maxRetries; attempt++)
		{
			try
			{
				this.logger.debug({ url: fullUrl, attempt });

				// Extract method from config or default to GET
				const method = (config?.method as string)?.toUpperCase() || 'GET';

				// Extract data for POST/PUT requests
				const data = config?.data;

				// Extract params and convert to query string
				const params = config?.params as Record<string, string> || {};
				const queryString = new URLSearchParams(params).toString();
				const finalUrl = queryString ? `${fullUrl}?${queryString}` : fullUrl;

				// Extract headers
				const headers = config?.headers as Record<string, string> || {};

				// Set timeout
				const timeout = config?.timeout as number || this.config.requestTimeoutMs;

				// Make request based on method
				let response: HttpResponseType;

				switch (method) 
				{
					case 'POST':
						response = await this.client.post(finalUrl, data, { headers, timeout });
						break;
					case 'PUT':
						response = await this.client.put(finalUrl, data, { headers, timeout });
						break;
					case 'DELETE':
						response = await this.client.delete(finalUrl, { headers, timeout });
						break;
					case 'HEAD':
						response = await this.client.head(finalUrl, { headers, timeout });
						break;
					default: // GET
						response = await this.client.get(finalUrl, { headers, timeout });
						break;
				}

				return response;
			}
			catch (error)
			{
				lastError = error as Error;
				this.logger.warn({
					url: fullUrl,
					attempt,
					error: (error as Error).message,
				});

				if (attempt < this.config.maxRetries)
				{
					const delay = this.config.retryDelayMs * 2 ** (attempt - 1);
					await AsyncUtils.sleep(delay);
				}
			}
		}

		throw new Error(
			`Failed to make request after ${this.config.maxRetries} attempts: ${lastError?.message}`,
		);
	}

}

export type { TrancoConfigType, TrancoListInfoType };

export default TrancoConnector;
