import { logger as sharedLogger, type LoggerInstanceType, AsyncUtils } from '@shared';
import HttpClient, { type HttpResponseType } from '@shared/utils/HttpClient';
import type { DiscoveryStrategyType } from '../interfaces/DiscoveryEngine';
import type {
	DomainCandidateType,
	FetchOptionsType,
	SourceConnectorType,
} from '../interfaces/SourceConnector';


type SonarConfigType =
{
	baseUrl: string;
	apiKey: string;
	maxRetries: number;
	retryDelayMs: number;
	requestTimeoutMs: number;
	rateLimitDelayMs: number;
	pageSize: number;
	longTailThreshold: number;
	maxResults: number;
	monthlyProcessingEnabled: boolean;
	backfillDays: number;
};

type SonarDNSRecordType =
{
	name: string;
	type: string;
	value: string;
	timestamp: string;
	source: string;
	count: number;
	first_seen?: string;
	last_seen?: string;
};

type SonarApiResponseType =
{
	results: SonarDNSRecordType[];
	total: number;
	page: number;
	per_page: number;
	has_more: boolean;
	query_time: number;
};

type SonarQueryOptionsType =
{
	query: string;
	type?: string;
	limit?: number;
	offset?: number;
	start_time?: string;
	end_time?: string;
};

type MonthlyBackfillType =
{
	month: string;
	totalRecords: number;
	processedRecords: number;
	lastProcessedTimestamp: string;
	completed: boolean;
	startTime: string;
	endTime?: string;
};

class SonarConnector implements SourceConnectorType
{
	public readonly name = 'sonar';

	public readonly priority = 7;

	public readonly cadence = 'monthly' as const;

	private readonly config: SonarConfigType;

	private readonly logger: LoggerInstanceType;

	private readonly client: HttpClient;

	private lastFetchTime: Date | null = null;

	private monthlyBackfills: Map<string, MonthlyBackfillType> = new Map();

	constructor(config?: Partial<SonarConfigType>)
	{
		const apiKey = process.env.RAPID7_API_KEY || config?.apiKey;
		if (!apiKey)
		{
			throw new Error('RAPID7_API_KEY environment variable is required');
		}

		this.config = {
			baseUrl: 'https://api.labs.rapid7.com',
			apiKey,
			maxRetries: 3,
			retryDelayMs: 2000, // Conservative for Rapid7 API
			requestTimeoutMs: 120000, // Longer timeout for large DNS queries
			rateLimitDelayMs: 1000, // Conservative rate limiting
			pageSize: 10000, // Large page size for efficiency
			longTailThreshold: 1000000, // Start from rank 1M+
			maxResults: 50000000, // Process up to 50M DNS records
			monthlyProcessingEnabled: true,
			backfillDays: 30, // Default backfill period
			...config,
		};

		this.logger = sharedLogger.getLogger('SonarConnector');
		this.client = new HttpClient(this.logger, {
			timeout: this.config.requestTimeoutMs,
		});
	}

	supportsStrategy(strategy: DiscoveryStrategyType): boolean
	{
		// Sonar primarily supports long-tail exploration from DNS resolution data
		return strategy === 'long-tail';
	}

	async* fetchDomains(options: FetchOptionsType): AsyncIterable<DomainCandidateType>
	{
		this.logger.info({ options });

		try
		{
			// For long-tail strategy, process DNS resolution data
			if (options.strategy === 'long-tail')
			{
				yield* this.fetchLongTailDomainsFromDNS(options);
			}
			else
			{
				this.logger.warn({
					strategy: options.strategy,
					supported: ['long-tail'],
				});
				return;
			}

			this.lastFetchTime = new Date();
			this.logger.info('Sonar FDNS domain fetch completed successfully');
		}
		catch (error)
		{
			this.logger.error({
				error: (error as Error).message,
			});
			throw error;
		}
	}

	async getLastUpdate(): Promise<Date | null>
	{
		return this.lastFetchTime;
	}

	async healthCheck(): Promise<boolean>
	{
		try
		{
			// Test API connectivity with a simple query
			const response = await this.makeRequest('/sonar/fdns/v2/search', {
				params: {
					q: 'type:A AND name:example.com',
					limit: 1,
				},
			});
			return response.statusCode === 200;
		}
		catch (error)
		{
			this.logger.error({
				error: (error as Error).message,
			});
			return false;
		}
	}

	/**
	 * Fetch long-tail domains from DNS resolution data
	 */
	private async* fetchLongTailDomainsFromDNS(options: FetchOptionsType): AsyncIterable<DomainCandidateType>
	{
		const limit = options.limit;
		const since = options.since || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // Default: last 30 days

		this.logger.info({
			since: since.toISOString(),
			limit,
		});

		let processedCount = 0;
		let yieldedCount = 0;
		let currentPage = 0;

		// Query for DNS A records to discover domains
		const queryOptions: SonarQueryOptionsType =
		{
			query: 'type:A',
			limit: this.config.pageSize,
			start_time: since.toISOString(),
		};

		// Process pages of DNS records
		while (!limit || yieldedCount < limit)
		{
			try
			{
				queryOptions.offset = currentPage * this.config.pageSize;

				const dnsRecords = await this.fetchDNSRecords(queryOptions);

				if (dnsRecords.length === 0)
				{
					this.logger.info('No more DNS records available');
					break;
				}

				// Process each DNS record to extract domains
				for (const record of dnsRecords)
				{
					if (limit && yieldedCount >= limit)
					{
						break;
					}

					// Extract domain from DNS record
					const domain = this.extractDomainFromDNSRecord(record);
					if (!domain)
					{
						processedCount++;
						continue;
					}

					// Apply long-tail filtering
					if (this.isValidLongTailDomain(domain, record))
					{
						const candidate = this.createDomainCandidate(domain, record, options);
						yieldedCount++;
						yield candidate;

						// Add rate limiting delay
						if (this.config.rateLimitDelayMs > 0)
						{
							await AsyncUtils.sleep(this.config.rateLimitDelayMs);
						}
					}

					processedCount++;
				}

				currentPage++;

				// Add delay between pages
				if (this.config.rateLimitDelayMs > 0)
				{
					await AsyncUtils.sleep(this.config.rateLimitDelayMs * 2);
				}

				this.logger.debug({
					page: currentPage,
					recordsInPage: dnsRecords.length,
					processedCount,
					yieldedCount,
				});
			}
			catch (error)
			{
				this.logger.error({
					page: currentPage,
					error: (error as Error).message,
				});

				// Continue with next page on error
				currentPage++;
			}
		}

		this.logger.info({
			processedCount,
			yieldedCount,
		});
	}

	/**
	 * Fetch DNS records from Sonar FDNS API
	 */
	private async fetchDNSRecords(queryOptions: SonarQueryOptionsType): Promise<SonarDNSRecordType[]>
	{
		this.logger.debug({
			query: queryOptions.query,
			limit: queryOptions.limit,
			offset: queryOptions.offset,
		});

		const response = await this.makeRequest('/sonar/fdns/v2/search', {
			params: {
				q: queryOptions.query,
				limit: queryOptions.limit,
				offset: queryOptions.offset,
				start_time: queryOptions.start_time,
				end_time: queryOptions.end_time,
			},
		});

		const apiResponse = response.data as SonarApiResponseType;

		if (!apiResponse.results || !Array.isArray(apiResponse.results))
		{
			this.logger.warn({
				response: apiResponse,
			});
			return [];
		}

		this.logger.debug({
			recordCount: apiResponse.results.length,
			total: apiResponse.total,
			hasMore: apiResponse.has_more,
		});

		return apiResponse.results;
	}

	/**
	 * Extract domain name from DNS record
	 */
	private extractDomainFromDNSRecord(record: SonarDNSRecordType): string | null
	{
		if (!record.name || record.name.length === 0)
		{
			return null;
		}

		// Remove trailing dot from FQDN
		let domain = record.name.toLowerCase().trim();
		if (domain.endsWith('.'))
		{
			domain = domain.slice(0, -1);
		}

		// Skip wildcard records
		if (domain.includes('*'))
		{
			return null;
		}

		// Skip obviously invalid domains
		if (domain.length < 3 || domain.length > 253)
		{
			return null;
		}

		return domain;
	}

	/**
	 * Check if domain qualifies as valid long-tail domain
	 */
	private isValidLongTailDomain(domain: string, record: SonarDNSRecordType): boolean
	{
		// Basic validation
		if (!domain || domain.length === 0)
		{
			return false;
		}

		// Skip localhost and private domains
		if (this.isPrivateOrLocalDomain(domain))
		{
			return false;
		}

		// Skip obvious spam or invalid domains
		if (this.isLikelySpamDomain(domain))
		{
			return false;
		}

		// Prefer domains with multiple DNS resolutions (indicates real usage)
		if (record.count && record.count < 2)
		{
			return false;
		}

		// Skip domains that are too new (might be temporary)
		if (record.first_seen)
		{
			const firstSeen = new Date(record.first_seen);
			const daysSinceFirstSeen = (Date.now() - firstSeen.getTime()) / (1000 * 60 * 60 * 24);
			if (daysSinceFirstSeen < 1)
			{
				return false;
			}
		}

		return true;
	}

	/**
	 * Check if domain is private or localhost
	 */
	private isPrivateOrLocalDomain(domain: string): boolean
	{
		const privatePatterns = [
			/^localhost$/,
			/\.local$/,
			/\.internal$/,
			/\.corp$/,
			/\.lan$/,
			/^10\./,
			/^192\.168\./,
			/^172\.(1[6-9]|2[0-9]|3[0-1])\./,
			/^127\./,
		];

		return privatePatterns.some(pattern => pattern.test(domain));
	}

	/**
	 * Check if domain appears to be spam or low quality
	 */
	private isLikelySpamDomain(domain: string): boolean
	{
		const spamPatterns = [
			// Excessive numbers
			/\d{10,}/,
			// Random character sequences
			/[a-z]{30,}/,
			// Known spam patterns
			/^(www\d+|test\d+|temp\d+|spam\d+|mail\d+)\./,
			// Suspicious TLDs
			/\.(tk|ml|ga|cf|click|download|zip|top)$/,
			// IP addresses
			/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,
			// CDN and infrastructure domains
			/\.(amazonaws|cloudfront|azureedge|fastly|cloudflare)\./,
		];

		return spamPatterns.some(pattern => pattern.test(domain));
	}

	/**
	 * Create domain candidate from DNS record
	 */
	private createDomainCandidate(
		domain: string,
		record: SonarDNSRecordType,
		options: FetchOptionsType,
	): DomainCandidateType
	{
		return {
			domain,
			source: this.name,
			metadata: {
				dnsType: record.type,
				dnsValue: record.value,
				timestamp: record.timestamp,
				dnsSource: record.source,
				resolutionCount: record.count,
				firstSeen: record.first_seen,
				lastSeen: record.last_seen,
				strategy: options.strategy || 'long-tail',
				fetchDate: new Date().toISOString(),
				discoveryMethod: 'fdns-resolution',
			},
		};
	}

	/**
	 * Process monthly backfill for comprehensive coverage
	 */
	async processMonthlyBackfill(month: string): Promise<MonthlyBackfillType>
	{
		if (!this.config.monthlyProcessingEnabled)
		{
			throw new Error('Monthly processing is disabled');
		}

		this.logger.info({ month });

		const backfill: MonthlyBackfillType = {
			month,
			totalRecords: 0,
			processedRecords: 0,
			lastProcessedTimestamp: '',
			completed: false,
			startTime: new Date().toISOString(),
		};

		try
		{
			// Calculate month date range
			const [year, monthNum] = month.split('-').map(Number);
			const startDate = new Date(year, monthNum - 1, 1);
			const endDate = new Date(year, monthNum, 0, 23, 59, 59);

			// Query for DNS records in the month
			const queryOptions: SonarQueryOptionsType = {
				query: 'type:A',
				start_time: startDate.toISOString(),
				end_time: endDate.toISOString(),
				limit: this.config.pageSize,
			};

			let currentPage = 0;
			let hasMore = true;

			while (hasMore)
			{
				try
				{
					queryOptions.offset = currentPage * this.config.pageSize;

					const dnsRecords = await this.fetchDNSRecords(queryOptions);

					if (dnsRecords.length === 0)
					{
						hasMore = false;
						break;
					}

					// Process records
					for (const record of dnsRecords)
					{
						const domain = this.extractDomainFromDNSRecord(record);
						if (domain && this.isValidLongTailDomain(domain, record))
						{
							backfill.processedRecords++;
							backfill.lastProcessedTimestamp = record.timestamp;
						}
					}

					backfill.totalRecords += dnsRecords.length;

					// Store backfill progress
					this.monthlyBackfills.set(month, { ...backfill });

					currentPage++;

					// Rate limiting
					await AsyncUtils.sleep(this.config.rateLimitDelayMs);

					this.logger.debug({
						month,
						page: currentPage,
						totalRecords: backfill.totalRecords,
						processedRecords: backfill.processedRecords,
					});
				}
				catch (error)
				{
					this.logger.error({
						month,
						page: currentPage,
						error: (error as Error).message,
					});
					break;
				}
			}

			backfill.completed = true;
			backfill.endTime = new Date().toISOString();
			this.monthlyBackfills.set(month, backfill);

			this.logger.info({
				month,
				totalRecords: backfill.totalRecords,
				processedRecords: backfill.processedRecords,
				duration: new Date(backfill.endTime).getTime() - new Date(backfill.startTime).getTime(),
			});

			return backfill;
		}
		catch (error)
		{
			this.logger.error({
				month,
				error: (error as Error).message,
			});
			throw error;
		}
	}

	/**
	 * Get monthly backfill status
	 */
	getMonthlyBackfillStatus(month: string): MonthlyBackfillType | null
	{
		return this.monthlyBackfills.get(month) || null;
	}

	/**
	 * Make HTTP request with retry logic and authentication
	 */
	private async makeRequest(
		endpoint: string,
		config?: Record<string, unknown>,
	): Promise<{ statusCode: number; data: unknown; headers: Record<string, string> }>
	{
		const url = endpoint.startsWith('http')
			? endpoint
			: `${this.config.baseUrl}${endpoint}`;

		let lastError: Error | null = null;

		for (let attempt = 1; attempt <= this.config.maxRetries; attempt++)
		{
			try
			{
				this.logger.debug({
					url,
					attempt,
				});

				// Extract method from config or default to GET
				const method = (config?.method as string)?.toUpperCase() || 'GET';

				// Extract data for POST/PUT requests
				const data = config?.data;

				// Extract params and convert to query string
				const params = config?.params as Record<string, string> || {};
				const queryString = new URLSearchParams(params).toString();
				const finalUrl = queryString ? `${url}?${queryString}` : url;

				// Set headers with API key and user agent
				const headers = {
					'X-API-Key': this.config.apiKey,
					'User-Agent': 'DomainSeeder/1.0 (Sonar FDNS Connector)',
					Accept: 'application/json',
					...(config?.headers as Record<string, string> || {}),
				};

				// Set timeout
				const timeout = config?.timeout as number || this.config.requestTimeoutMs;

				// Make request based on method
				let response: HttpResponseType;

				switch (method) 
				{
					case 'POST':
						response = await this.client.post(finalUrl, data, { headers, timeout });
						break;
					case 'PUT':
						response = await this.client.put(finalUrl, data, { headers, timeout });
						break;
					case 'DELETE':
						response = await this.client.delete(finalUrl, { headers, timeout });
						break;
					case 'HEAD':
						response = await this.client.head(finalUrl, { headers, timeout });
						break;
					default: // GET
						response = await this.client.get(finalUrl, { headers, timeout });
						break;
				}

				return response;
			}
			catch (error)
			{
				lastError = error as Error;
				this.logger.warn({
					url,
					attempt,
					error: (error as Error).message,
				});

				if (attempt < this.config.maxRetries)
				{
					const delay = this.config.retryDelayMs * (2 ** (attempt - 1));
					await AsyncUtils.sleep(delay);
				}
			}
		}

		throw new Error(
			`Failed to make Sonar FDNS API request after ${this.config.maxRetries} attempts: ${lastError?.message}`,
		);
	}

	/**
	 * Sleep for specified milliseconds
	 */
}

export type {
	SonarConfigType,
	SonarDNSRecordType,
	SonarApiResponseType,
	SonarQueryOptionsType,
	MonthlyBackfillType,
};

export default SonarConnector;
