import { logger as sharedLogger, type LoggerInstanceType, AsyncUtils } from '@shared';
import HttpClient, { type HttpResponseType } from '@shared/utils/HttpClient';
import type { DiscoveryStrategyType } from '../interfaces/DiscoveryEngine';
import type {
	DomainCandidateType,
	FetchOptionsType,
	SourceConnectorType,
} from '../interfaces/SourceConnector';

type RadarConfigType =
{
	baseUrl: string;
	apiKey: string;
	maxRetries: number;
	retryDelayMs: number;
	requestTimeoutMs: number;
	rateLimitDelayMs: number;
	pageSize: number;
};

type RadarDomainDataType =
{
	domain: string;
	rank: number;
	categories?: Array<{
		id: number;
		name: string;
		superCategoryId: number;
	}>;
};

type RadarApiResponseType =
{
	success: boolean;
	result: {
		trending?: RadarDomainDataType[];
		popular?: RadarDomainDataType[];
		top_0?: RadarDomainDataType[]; // Legacy format
		meta: {
			total_count?: number;
			page?: number;
			per_page?: number;
			dateRange?: Array<{
				startTime: string;
				endTime: string;
			}>;
			trending?: {
				date: string;
			};
			popular?: {
				date: string;
			};
			top_0?: {
				date: string;
			};
		};
	};
	errors?: Array<{ code: number; message: string }>;
};

type RadarRankingHistoryType =
{
	domain: string;
	rankings: Array<{
		date: string;
		rank: number;
	}>;
};

class RadarConnector implements SourceConnectorType
{
	public readonly name = 'radar';

	public readonly priority = 2;

	public readonly cadence = 'daily' as const;

	private readonly config: RadarConfigType;

	private readonly logger: LoggerInstanceType;

	private lastFetchTime: Date | null = null;

	private rankingHistory: Map<string, RadarRankingHistoryType> = new Map();

	private readonly client: HttpClient;

	constructor(config?: Partial<RadarConfigType>)
	{
		const apiKey = process.env.CLOUDFLARE_API_KEY;
		if (!apiKey)
		{
			throw new Error('CLOUDFLARE_API_KEY environment variable is required');
		}

		this.config = {
			baseUrl: 'https://api.cloudflare.com/client/v4/radar',
			apiKey,
			maxRetries: 3,
			retryDelayMs: 1000,
			requestTimeoutMs: 30000,
			rateLimitDelayMs: 200, // Cloudflare has stricter rate limits
			pageSize: 1000,
			...config,
		};

		this.logger = sharedLogger.getLogger('RadarConnector');
		this.client = new HttpClient(this.logger, {
			timeout: this.config.requestTimeoutMs,
		});
	}

	supportsStrategy(strategy: DiscoveryStrategyType): boolean
	{
		// Radar supports differential analysis and temporal analysis
		return strategy === 'differential' || strategy === 'temporal';
	}

	async* fetchDomains(options: FetchOptionsType): AsyncIterable<DomainCandidateType>
	{
		this.logger.info({ options });

		try
		{
			const strategy = options.strategy || 'differential';

			// For temporal analysis, we need to fetch historical data first
			if (strategy === 'temporal')
			{
				yield* this.fetchDomainsWithTemporalAnalysis(options);
			}
			else
			{
				yield* this.fetchDomainsWithPagination(options);
			}

			this.lastFetchTime = new Date();
			this.logger.info({ msg: 'Radar domain fetch completed successfully' });
		}
		catch (error)
		{
			this.logger.error({ error: (error as Error).message, msg: 'Failed to fetch domains from Radar' });
			throw error;
		}
	}

	async getLastUpdate(): Promise<Date | null>
	{
		return this.lastFetchTime;
	}

	async healthCheck(): Promise<boolean>
	{
		try
		{
			const response = await this.makeRequest('/ranking/top', {
				params: {
					limit: 1,
				},
			});
			return response.data.success === true;
		}
		catch (error)
		{
			this.logger.error({ error: (error as Error).message, msg: 'Radar health check failed' });
			return false;
		}
	}

	private async* fetchDomainsWithPagination(options: FetchOptionsType): AsyncIterable<DomainCandidateType>
	{
		const strategy = options.strategy || 'differential';
		const limit = options.limit;
		const offset = options.offset || 0;

		let processedCount = 0;
		let yieldedCount = 0;

		// Determine which ranking type to use based on strategy
		const useTrending = strategy === 'differential' || strategy === 'temporal' || strategy === 'long-tail';
		const rankingName = useTrending ? 'trending' : undefined; // Use undefined for default top domains

		try
		{
			this.logger.debug({
				requestedLimit: limit,
				rankingName,
				strategy
			});

			// Build request parameters
			const params: any = {
				limit: Math.min(this.config.pageSize, limit || 100),
			};

			// Add name parameter for trending domains
			if (rankingName) {
				params.name = rankingName;
			}

			// Add location parameter for geographic discovery
			if (options.location) {
				params.location = options.location;
			}

			const response = await this.makeRequest('/ranking/top', {
				params
			});

			const apiResponse = response.data as RadarApiResponseType;

			if (!apiResponse.success || !apiResponse.result)
			{
				this.logger.error({ response: apiResponse });
				return;
			}

			// Get domains from the appropriate key based on ranking type
			const domains = rankingName === 'trending' ?
				apiResponse.result.trending :
				apiResponse.result.top_0; // Default top domains use top_0

			if (!domains) {
				this.logger.error({
					msg: 'No domains found in response',
					rankingName,
					resultKeys: Object.keys(apiResponse.result)
				});
				return;
			}

			for (const domainData of domains)
			{
				// Skip until we reach the offset within the page
				if (processedCount < offset)
				{
					processedCount += 1;
					continue;
				}

				// Apply strategy-specific filtering
				if (!this.shouldIncludeDomain(domainData, strategy, options))
				{
					processedCount += 1;
					continue;
				}

				// Check if we've reached the limit
				if (limit && yieldedCount >= limit)
				{
					break;
				}

				const candidate: DomainCandidateType = {
					domain: domainData.domain.toLowerCase().trim(),
					rank: domainData.rank,
					source: this.name,
					metadata: {
						categories: domainData.categories,
						strategy,
						fetchDate: new Date().toISOString(),
					},
				};

				processedCount += 1;
				yieldedCount += 1;

				yield candidate;

				// Add rate limiting delay
				if (this.config.rateLimitDelayMs > 0)
				{
					await this.sleep(this.config.rateLimitDelayMs);
				}
			}
		}
		catch (error)
		{
			this.logger.error({
				error: (error as Error).message,
				msg: 'Failed to fetch domains from Radar'
			});
			throw error;
		}

		this.logger.info({
			processedCount,
			yieldedCount,
			strategy,
		});
	}

	private async* fetchDomainsWithTemporalAnalysis(options: FetchOptionsType): AsyncIterable<DomainCandidateType>
	{
		const limit = options.limit;
		let yieldedCount = 0;

		// First, fetch current rankings
		const currentDomains = new Map<string, RadarDomainDataType>();
		for await (const candidate of this.fetchDomainsWithPagination({
			...options,
			strategy: 'differential',
		}))
		{
			const domainData = candidate.metadata as RadarDomainDataType & { strategy: string };
			currentDomains.set(candidate.domain, {
				domain: candidate.domain,
				rank: candidate.rank || 0,
				category: domainData.category,
				subcategory: domainData.subcategory,
				popularity_rank: domainData.popularity_rank,
				change_24h: domainData.change_24h,
				change_7d: domainData.change_7d,
				change_30d: domainData.change_30d,
			});
		}

		// Analyze temporal patterns and yield rising domains
		for (const [domain, domainData] of Array.from(currentDomains.entries()))
		{
			if (limit && yieldedCount >= limit)
			{
				break;
			}

			// Check if domain shows significant upward movement
			if (this.isRisingDomain(domainData))
			{
				const candidate: DomainCandidateType = {
					domain,
					rank: domainData.rank,
					source: this.name,
					metadata: {
						...domainData,
						strategy: 'temporal',
						fetchDate: new Date().toISOString(),
						risingIndicator: this.calculateRisingIndicator(domainData),
					},
				};

				yieldedCount += 1;
				yield candidate;

				// Update ranking history
				this.updateRankingHistory(domain, domainData.rank);
			}
		}

		this.logger.info({
			totalDomains: currentDomains.size,
			risingDomains: yieldedCount,
		});
	}

	private shouldIncludeDomain(
		domainData: RadarDomainDataType,
		strategy: DiscoveryStrategyType,
		options: FetchOptionsType,
	): boolean
	{
		// Basic domain validation
		if (!domainData.domain || domainData.domain.length === 0)
		{
			return false;
		}

		// Strategy-specific filtering
		switch (strategy)
		{
			case 'differential':
				// For differential analysis, include all domains (filtering happens later)
				return true;

			case 'temporal':
				// For temporal analysis, include domains with significant changes
				return this.hasSignificantChange(domainData);

			default:
				return true;
		}
	}

	private hasSignificantChange(domainData: RadarDomainDataType): boolean
	{
		// Check for significant ranking changes in the last 7 or 30 days
		const change7d = domainData.change_7d || 0;
		const change30d = domainData.change_30d || 0;

		// Consider significant if domain improved by more than 50% in 7 days
		// or more than 30% in 30 days
		return change7d < -0.5 || change30d < -0.3;
	}

	private isRisingDomain(domainData: RadarDomainDataType): boolean
	{
		// Multiple criteria for identifying rising domains
		const change7d = domainData.change_7d || 0;
		const change30d = domainData.change_30d || 0;

		// Rising if:
		// 1. Significant improvement in 7 days (>50% rank improvement)
		// 2. Consistent improvement over 30 days (>30% rank improvement)
		// 3. High velocity change in recent period
		return (
			change7d < -0.5 || // 50%+ improvement in 7 days
			change30d < -0.3 || // 30%+ improvement in 30 days
			(change7d < -0.2 && change30d < -0.1) // Consistent upward trend
		);
	}

	private calculateRisingIndicator(domainData: RadarDomainDataType): number
	{
		const change7d = Math.abs(domainData.change_7d || 0);
		const change30d = Math.abs(domainData.change_30d || 0);

		// Calculate a composite rising score (0-1)
		const shortTermScore = Math.min(change7d * 2, 1); // Weight recent changes more
		const longTermScore = Math.min(change30d, 1);

		return (shortTermScore * 0.7) + (longTermScore * 0.3);
	}

	private updateRankingHistory(domain: string, currentRank: number): void
	{
		const today = new Date().toISOString().split('T')[0];

		if (!this.rankingHistory.has(domain))
		{
			this.rankingHistory.set(domain, {
				domain,
				rankings: [],
			});
		}

		const history = this.rankingHistory.get(domain)!;

		// Add current ranking if it's a new date
		const lastEntry = history.rankings[history.rankings.length - 1];
		if (!lastEntry || lastEntry.date !== today)
		{
			history.rankings.push({
				date: today,
				rank: currentRank,
			});

			// Keep only last 30 days of history
			if (history.rankings.length > 30)
			{
				history.rankings = history.rankings.slice(-30);
			}
		}
	}

	private async makeRequest(
		endpoint: string,
		config?: Record<string, unknown>,
	): Promise<any>
	{
		const url = `${this.config.baseUrl}${endpoint}`;

		let lastError: Error | null = null;

		for (let attempt = 1; attempt <= this.config.maxRetries; attempt += 1)
		{
			try
			{
				this.logger.debug({ url, attempt });

				// Extract method from config or default to GET
				const method = (config?.method as string)?.toUpperCase() || 'GET';

				// Extract data for POST/PUT requests
				const data = config?.data;

				// Extract params and convert to query string
				const params = config?.params as Record<string, string> || {};
				const queryString = new URLSearchParams(params).toString();
				const finalUrl = queryString ? `${url}?${queryString}` : url;

				// Set headers with API key and content type
				const headers = {
					Authorization: `Bearer ${this.config.apiKey}`,
					'Content-Type': 'application/json',
					...(config?.headers as Record<string, string> || {}),
				};

				// Set timeout
				const timeout = config?.timeout as number || this.config.requestTimeoutMs;

				// Make request based on method
				let response: HttpResponseType;

				switch (method)
				{
					case 'POST':
						response = await this.client.post(finalUrl, data, { headers, timeout });
						break;
					case 'PUT':
						response = await this.client.put(finalUrl, data, { headers, timeout });
						break;
					case 'DELETE':
						response = await this.client.delete(finalUrl, { headers, timeout });
						break;
					case 'HEAD':
						response = await this.client.head(finalUrl, { headers, timeout });
						break;
					default: // GET
						response = await this.client.get(finalUrl, { headers, timeout });
						break;
				}

				return response;
			}
			catch (error)
			{
				lastError = error as Error;
				this.logger.warn({
					url,
					attempt,
					error: (error as Error).message,
				});

				if (attempt < this.config.maxRetries)
				{
					const delay = this.config.retryDelayMs * (2 ** (attempt - 1));
					await this.sleep(delay);
				}
			}
		}

		throw new Error(
			`Failed to make Radar API request after ${this.config.maxRetries} attempts: ${lastError?.message}`,
		);
	}

	private sleep(ms: number): Promise<void>
	{
		return new Promise((resolve) =>
		{
			setTimeout(resolve, ms);
		});
	}
}

export type {
	RadarConfigType,
	RadarDomainDataType,
	RadarApiResponseType,
	RadarRankingHistoryType,
};

export default RadarConnector;
