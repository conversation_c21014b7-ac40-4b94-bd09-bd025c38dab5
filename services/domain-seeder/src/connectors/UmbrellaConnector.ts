import { logger as sharedLogger, type LoggerInstanceType, AsyncUtils } from '@shared';
import HttpClient, { type HttpResponseType } from '@shared/utils/HttpClient';
import type { DiscoveryStrategyType } from '../interfaces/DiscoveryEngine';
import type {
	DomainCandidateType,
	FetchOptionsType,
	SourceConnectorType,
} from '../interfaces/SourceConnector';


type UmbrellaConfigType =
{
	baseUrl: string;
	apiKey: string;
	maxRetries: number;
	retryDelayMs: number;
	requestTimeoutMs: number;
	rateLimitDelayMs: number;
	pageSize: number;
	dailyRequestLimit: number;
	monthlyRequestLimit: number;
	tosComplianceCheck: boolean;
};

type UmbrellaDomainDataType =
{
	domain: string;
	rank: number;
	category?: string;
	status?: string;
	first_seen?: string;
	last_seen?: string;
	popularity_score?: number;
};

type UmbrellaApiResponseType =
{
	data: UmbrellaDomainDataType[];
	meta: {
		total: number;
		page: number;
		per_page: number;
		total_pages: number;
	};
	status: string;
	message?: string;
};

type UmbrellaUsageStatsType =
{
	dailyRequests: number;
	monthlyRequests: number;
	lastResetDate: string;
	rateLimitRemaining: number;
	rateLimitReset: number;
};

class UmbrellaConnector implements SourceConnectorType
{
	public readonly name = 'umbrella';

	public readonly priority = 3;

	public readonly cadence = 'daily' as const;

	private readonly config: UmbrellaConfigType;

	private readonly logger: LoggerInstanceType;

	private readonly client: HttpClient;

	private lastFetchTime: Date | null = null;

	private usageStats: UmbrellaUsageStatsType;

	private tosAccepted: boolean = false;

	constructor(config?: Partial<UmbrellaConfigType>)
	{
		const apiKey = process.env.UMBRELLA_API_KEY;
		if (!apiKey)
		{
			throw new Error('UMBRELLA_API_KEY environment variable is required');
		}

		this.config = {
			baseUrl: 'https://api.umbrella.com/investigate/v2',
			apiKey,
			maxRetries: 3,
			retryDelayMs: 1000,
			requestTimeoutMs: 30000,
			rateLimitDelayMs: 250, // Conservative rate limiting for Umbrella
			pageSize: 1000,
			dailyRequestLimit: 10000, // Conservative daily limit
			monthlyRequestLimit: 300000, // Conservative monthly limit
			tosComplianceCheck: true,
			...config,
		};

		this.logger = sharedLogger.getLogger('UmbrellaConnector');
		this.client = new HttpClient(this.logger, {
			timeout: this.config.requestTimeoutMs,
		});

		// Initialize usage stats
		this.usageStats =
		{
			dailyRequests: 0,
			monthlyRequests: 0,
			lastResetDate: new Date().toISOString().split('T')[0],
			rateLimitRemaining: 1000,
			rateLimitReset: Date.now() + 3600000, // 1 hour from now
		};

		// Check ToS compliance on initialization
		if (this.config.tosComplianceCheck)
		{
			this.checkTosCompliance();
		}
	}

	supportsStrategy(strategy: DiscoveryStrategyType): boolean
	{
		// Umbrella supports differential analysis and long-tail exploration
		return strategy === 'differential' || strategy === 'long-tail';
	}

	async* fetchDomains(options: FetchOptionsType): AsyncIterable<DomainCandidateType>
	{
		this.logger.info({ options });

		// Check ToS compliance before fetching
		if (!this.tosAccepted)
		{
			throw new Error('ToS compliance check failed. Cannot proceed with Umbrella API requests.');
		}

		// Check usage limits
		if (!this.checkUsageLimits())
		{
			throw new Error('Usage limits exceeded. Cannot make more requests today.');
		}

		try
		{
			const strategy = options.strategy || 'differential';
			yield* this.fetchDomainsWithPagination(options, strategy);

			this.lastFetchTime = new Date();
			this.logger.info({
				usageStats: this.usageStats,
			});
		}
		catch (error)
		{
			this.logger.error({
				error: (error as Error).message,
				usageStats: this.usageStats,
			});
			throw error;
		}
	}

	async getLastUpdate(): Promise<Date | null>
	{
		return this.lastFetchTime;
	}

	async healthCheck(): Promise<boolean>
	{
		try
		{
			// Check ToS compliance
			if (!this.tosAccepted)
			{
				this.logger.warn('Health check failed: ToS not accepted');
				return false;
			}

			// Check usage limits
			if (!this.checkUsageLimits())
			{
				this.logger.warn('Health check failed: Usage limits exceeded');
				return false;
			}

			// Make a minimal API request to check connectivity
			const response = await this.makeRequest('/domains/top', {
				params: {
					limit: 1,
				},
			});

			const isHealthy = response.status === 200 && response.data?.status === 'success';

			if (isHealthy)
			{
				this.updateUsageStats(response);
			}

			return isHealthy;
		}
		catch (error)
		{
			this.logger.error({ error: (error as Error).message });
			return false;
		}
	}

	getUsageStats(): UmbrellaUsageStatsType
	{
		return { ...this.usageStats };
	}

	private checkTosCompliance(): void
	{
		// Check environment variable for ToS acceptance
		const tosAccepted = process.env.UMBRELLA_TOS_ACCEPTED;

		if (tosAccepted !== 'true')
		{
			this.logger.error('Umbrella ToS not accepted. Set UMBRELLA_TOS_ACCEPTED=true to proceed.');
			this.tosAccepted = false;
			return;
		}

		// Additional compliance checks
		const complianceChecks = [
			this.checkApiKeyFormat(),
			this.checkRateLimitCompliance(),
			this.checkDataUsageCompliance(),
		];

		this.tosAccepted = complianceChecks.every(check => check);

		if (this.tosAccepted)
		{
			this.logger.info('Umbrella ToS compliance checks passed');
		}
		else
		{
			this.logger.error('Umbrella ToS compliance checks failed');
		}
	}

	private checkApiKeyFormat(): boolean
	{
		// Basic API key format validation
		const apiKey = this.config.apiKey;

		// Umbrella API keys are typically 32-40 character hex strings
		const isValidFormat = /^[a-f0-9]{32,40}$/i.test(apiKey);

		if (!isValidFormat)
		{
			this.logger.error('Invalid Umbrella API key format');
		}

		return isValidFormat;
	}

	private checkRateLimitCompliance(): boolean
	{
		// Ensure rate limiting is configured appropriately
		const minDelayMs = 200; // Minimum 200ms between requests

		if (this.config.rateLimitDelayMs < minDelayMs)
		{
			this.logger.error(`Rate limit delay too low. Minimum ${minDelayMs}ms required.`);
			return false;
		}

		return true;
	}

	private checkDataUsageCompliance(): boolean
	{
		// Ensure daily and monthly limits are reasonable
		const maxDailyLimit = 50000;
		const maxMonthlyLimit = 1000000;

		if (this.config.dailyRequestLimit > maxDailyLimit)
		{
			this.logger.error(`Daily request limit too high. Maximum ${maxDailyLimit} allowed.`);
			return false;
		}

		if (this.config.monthlyRequestLimit > maxMonthlyLimit)
		{
			this.logger.error(`Monthly request limit too high. Maximum ${maxMonthlyLimit} allowed.`);
			return false;
		}

		return true;
	}

	private checkUsageLimits(): boolean
	{
		// Reset daily counter if needed
		const today = new Date().toISOString().split('T')[0];
		if (this.usageStats.lastResetDate !== today)
		{
			this.usageStats.dailyRequests = 0;
			this.usageStats.lastResetDate = today;
		}

		// Check daily limit
		if (this.usageStats.dailyRequests >= this.config.dailyRequestLimit)
		{
			this.logger.warn({
				current: this.usageStats.dailyRequests,
				limit: this.config.dailyRequestLimit,
			});
			return false;
		}

		// Check monthly limit
		if (this.usageStats.monthlyRequests >= this.config.monthlyRequestLimit)
		{
			this.logger.warn({
				current: this.usageStats.monthlyRequests,
				limit: this.config.monthlyRequestLimit,
			});
			return false;
		}

		// Check rate limit
		if (this.usageStats.rateLimitRemaining <= 0 && Date.now() < this.usageStats.rateLimitReset)
		{
			this.logger.warn({
				resetTime: new Date(this.usageStats.rateLimitReset).toISOString(),
			});
			return false;
		}

		return true;
	}

	private async* fetchDomainsWithPagination(
		options: FetchOptionsType,
		strategy: DiscoveryStrategyType,
	): AsyncIterable<DomainCandidateType>
	{
		const limit = options.limit;
		const offset = options.offset || 0;

		let currentPage = Math.floor(offset / this.config.pageSize) + 1;
		let processedCount = 0;
		let yieldedCount = 0;
		let hasMorePages = true;

		while (hasMorePages && (!limit || yieldedCount < limit))
		{
			// Check usage limits before each request
			if (!this.checkUsageLimits())
			{
				this.logger.warn('Usage limits reached during pagination');
				break;
			}

			try
			{
				this.logger.debug({ page: currentPage });

				const response = await this.makeRequest('/domains/top', {
					params: {
						limit: this.config.pageSize,
						offset: (currentPage - 1) * this.config.pageSize,
						format: 'json',
					},
				});

				const apiResponse = response.data as UmbrellaApiResponseType;

				if (apiResponse.status !== 'success' || !apiResponse.data)
				{
					this.logger.error({ response: apiResponse });
					break;
				}

				const domains = apiResponse.data;
				hasMorePages = domains.length === this.config.pageSize &&
					currentPage < apiResponse.meta.total_pages;

				for (const domainData of domains)
				{
					// Skip until we reach the offset within the page
					if (processedCount < offset)
					{
						processedCount += 1;
						continue;
					}

					// Apply strategy-specific filtering
					if (!this.shouldIncludeDomain(domainData, strategy, options))
					{
						processedCount += 1;
						continue;
					}

					// Check if we've reached the limit
					if (limit && yieldedCount >= limit)
					{
						hasMorePages = false;
						break;
					}

					const candidate: DomainCandidateType =
					{
						domain: domainData.domain.toLowerCase().trim(),
						rank: domainData.rank,
						source: this.name,
						metadata: {
							category: domainData.category,
							status: domainData.status,
							first_seen: domainData.first_seen,
							last_seen: domainData.last_seen,
							popularity_score: domainData.popularity_score,
							strategy,
							fetchDate: new Date().toISOString(),
						},
					};

					processedCount += 1;
					yieldedCount += 1;

					yield candidate;

					// Add rate limiting delay
					if (this.config.rateLimitDelayMs > 0)
					{
						await AsyncUtils.sleep(this.config.rateLimitDelayMs);
					}
				}

				// Update usage stats
				this.updateUsageStats(response);

				currentPage += 1;

				// Add delay between pages to respect rate limits
				if (hasMorePages && this.config.rateLimitDelayMs > 0)
				{
					await AsyncUtils.sleep(this.config.rateLimitDelayMs * 2);
				}
			}
			catch (error)
			{
				this.logger.error({
					page: currentPage,
					error: (error as Error).message,
				});
				throw error;
			}
		}

		this.logger.info({
			processedCount,
			yieldedCount,
			strategy,
			usageStats: this.usageStats,
		});
	}

	private shouldIncludeDomain(
		domainData: UmbrellaDomainDataType,
		strategy: DiscoveryStrategyType,
		options: FetchOptionsType,
	): boolean
	{
		// Basic domain validation
		if (!domainData.domain || domainData.domain.length === 0)
		{
			return false;
		}

		// Strategy-specific filtering
		switch (strategy)
		{
			case 'differential':
				// For differential analysis, include all domains (filtering happens later)
				return true;

			case 'long-tail':
				// For long-tail, only include domains ranked > 1M
				return domainData.rank > 1000000;

			default:
				return true;
		}
	}

	private updateUsageStats(response: HttpResponseType): void
	{
		// Update request counters
		this.usageStats.dailyRequests += 1;
		this.usageStats.monthlyRequests += 1;

		// Update rate limit info from response headers
		// Note: HttpClient headers are accessed differently than axios
		const rateLimitRemaining = response.headers['x-ratelimit-remaining'] || response.headers['X-RateLimit-Remaining'];
		const rateLimitReset = response.headers['x-ratelimit-reset'] || response.headers['X-RateLimit-Reset'];

		if (rateLimitRemaining !== undefined)
		{
			this.usageStats.rateLimitRemaining = parseInt(rateLimitRemaining, 10);
		}

		if (rateLimitReset !== undefined)
		{
			this.usageStats.rateLimitReset = parseInt(rateLimitReset, 10) * 1000; // Convert to milliseconds
		}

		// Log usage stats periodically
		if (this.usageStats.dailyRequests % 100 === 0)
		{
			this.logger.info({
				dailyRequests: this.usageStats.dailyRequests,
				monthlyRequests: this.usageStats.monthlyRequests,
				rateLimitRemaining: this.usageStats.rateLimitRemaining,
			});
		}
	}

	private async makeRequest(
		endpoint: string,
		config?: Record<string, unknown>,
	): Promise<any>
	{
		const url = `${this.config.baseUrl}${endpoint}`;

		let lastError: Error | null = null;

		for (let attempt = 1; attempt <= this.config.maxRetries; attempt += 1)
		{
			try
			{
				this.logger.debug({ url, attempt });

				// Extract method from config or default to GET
				const method = (config?.method as string)?.toUpperCase() || 'GET';

				// Extract data for POST/PUT requests
				const data = config?.data;

				// Extract params and convert to query string
				const params = config?.params as Record<string, string> || {};
				const queryString = new URLSearchParams(params).toString();
				const finalUrl = queryString ? `${url}?${queryString}` : url;

				// Set headers with API key and user agent
				const headers = {
					Authorization: `Bearer ${this.config.apiKey}`,
					'Content-Type': 'application/json',
					'User-Agent': 'DomainSeeder/1.0',
					...(config?.headers as Record<string, string> || {}),
				};

				// Set timeout
				const timeout = config?.timeout as number || this.config.requestTimeoutMs;

				// Make request based on method
				let response: HttpResponseType;

				switch (method) 
				{
					case 'POST':
						response = await this.client.post(finalUrl, data, { headers, timeout });
						break;
					case 'PUT':
						response = await this.client.put(finalUrl, data, { headers, timeout });
						break;
					case 'DELETE':
						response = await this.client.delete(finalUrl, { headers, timeout });
						break;
					case 'HEAD':
						response = await this.client.head(finalUrl, { headers, timeout });
						break;
					default: // GET
						response = await this.client.get(finalUrl, { headers, timeout });
						break;
				}

				return response;
			}
			catch (error)
			{
				lastError = error as Error;
				this.logger.warn({
					url,
					attempt,
					error: (error as Error).message,
				});

				// Check if it's a rate limit error
				// Note: HttpClient doesn't have the same error structure as axios
				// We'll need to check the response status code directly
				if ((error as any).statusCode === 429)
				{
					// For rate limiting with HttpClient, we'll use a fixed delay
					const waitTime = this.config.retryDelayMs * 2;

					this.logger.warn({ waitTime });
					await AsyncUtils.sleep(waitTime);
				}
				else if (attempt < this.config.maxRetries)
				{
					const delay = this.config.retryDelayMs * (2 ** (attempt - 1));
					await AsyncUtils.sleep(delay);
				}
			}
		}

		throw new Error(
			`Failed to make Umbrella API request after ${this.config.maxRetries} attempts: ${lastError?.message}`,
		);
	}

}

export type {
	UmbrellaConfigType,
	UmbrellaDomainDataType,
	UmbrellaApiResponseType,
	UmbrellaUsageStatsType,
};

export { UmbrellaConnector };

export default UmbrellaConnector;
