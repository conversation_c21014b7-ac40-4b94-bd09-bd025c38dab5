import { createReadStream, createWriteStream } from 'node:fs';
// import { pipeline } from 'node:stream/promises';
import { createGunzip } from 'zlib';
import { logger as sharedLogger, type LoggerInstanceType, AsyncUtils } from '@shared';
import HttpClient, { type HttpResponseType } from '@shared/utils/HttpClient';
import { HttpClientStream } from '@shared/utils/HttpClientStream';
import type { DiscoveryStrategyType } from '../interfaces/DiscoveryEngine';
import type {
	DomainCandidateType,
	FetchOptionsType,
	SourceConnectorType,
} from '../interfaces/SourceConnector';

type PIRConfigType =
{
	baseUrl: string;
	apiKey: string;
	maxRetries: number;
	retryDelayMs: number;
	requestTimeoutMs: number;
	rateLimitDelayMs: number;
	tempDir: string;
	registrationCutoffHours: number;
	pageSize: number;
};

type PIRDomainInfoType =
{
	domain: string;
	registrationDate: string;
	expirationDate: string;
	registrar: string;
	status: string[];
	nameservers: string[];
	contacts: {
		registrant?: PIRContactType;
		admin?: PIRContactType;
		tech?: PIRContactType;
	};
};

type PIRContactType =
{
	name: string;
	organization?: string;
	email: string;
	country: string;
};

type PIRZoneFileInfoType =
{
	filename: string;
	url: string;
	lastModified: Date;
	size: number;
	checksum: string;
};

type PIRRegistrationEventType =
{
	domain: string;
	eventType: 'create' | 'update' | 'delete' | 'transfer';
	timestamp: string;
	registrar: string;
};

type PIRAuthResponseType =
{
	accessToken: string;
	tokenType: string;
	expiresIn: number;
};

class PIRConnector implements SourceConnectorType
{
	public readonly name = 'pir';

	public readonly priority = 5;

	public readonly cadence = 'daily' as const;

	private readonly config: PIRConfigType;

	private readonly logger: LoggerInstanceType;

	private readonly client: HttpClient;

	private lastFetchTime: Date | null = null;

	private authToken: string | null = null;

	private tokenExpiry: Date | null = null;

	constructor(config?: Partial<PIRConfigType>)
	{
		this.config = {
			baseUrl: 'https://api.pir.org',
			apiKey: process.env.PIR_API_KEY || '',
			maxRetries: 3,
			retryDelayMs: 1000,
			requestTimeoutMs: 60000,
			rateLimitDelayMs: 200, // Conservative rate limiting for registry API
			tempDir: '/tmp',
			registrationCutoffHours: 48, // Only include domains registered in last 48 hours
			pageSize: 1000,
			...config,
		};

		this.logger = sharedLogger.getLogger('PIRConnector');
		this.client = new HttpClient(this.logger, {
			timeout: this.config.requestTimeoutMs,
		});

		if (!this.config.apiKey)
		{
			throw new Error('PIR API key not provided. Set PIR_API_KEY environment variable.');
		}
	}

	supportsStrategy(strategy: DiscoveryStrategyType): boolean
	{
		// PIR supports zone-new strategy for newly registered .org domains
		return strategy === 'zone-new';
	}

	async* fetchDomains(options: FetchOptionsType): AsyncIterable<DomainCandidateType>
	{
		this.logger.info({ options });

		try
		{
			// Ensure we have a valid authentication token
			await this.ensureAuthenticated();

			// Process domains based on strategy
			const strategy = options.strategy || 'zone-new';

			if (strategy === 'zone-new')
			{
				yield* this.fetchNewlyRegisteredDomains(options);
			}
			else
			{
				this.logger.warn({ strategy });
				return;
			}

			this.lastFetchTime = new Date();
			this.logger.info('PIR domain fetch completed successfully');
		}
		catch (error)
		{
			this.logger.error({ error: (error as Error).message });
			throw error;
		}
	}

	async getLastUpdate(): Promise<Date | null>
	{
		return this.lastFetchTime;
	}

	async healthCheck(): Promise<boolean>
	{
		try
		{
			await this.ensureAuthenticated();

			// Test API connectivity with a simple status endpoint
			const response = await this.makeRequest('/v1/status');
			return response.statusCode === 200;
		}
		catch (error)
		{
			this.logger.error({ error: (error as Error).message });
			return false;
		}
	}

	private async ensureAuthenticated(): Promise<void>
	{
		// Check if we have a valid token
		if (this.authToken && this.tokenExpiry && this.tokenExpiry > new Date())
		{
			return;
		}

		this.logger.info('Authenticating with PIR API');

		const response = await this.makeRequest<PIRAuthResponseType>('/v1/auth/token', {
			method: 'POST',
			data: {
				apiKey: this.config.apiKey,
				scope: 'registry:read',
			},
		});

		this.authToken = response.data.accessToken;

		// PIR tokens typically expire in 24 hours
		this.tokenExpiry = new Date(Date.now() + 23 * 60 * 60 * 1000); // 23 hours to be safe

		this.logger.info('Successfully authenticated with PIR API');
	}

	private async* fetchNewlyRegisteredDomains(options: FetchOptionsType): AsyncIterable<DomainCandidateType>
	{
		const cutoffDate = new Date(Date.now() - this.config.registrationCutoffHours * 60 * 60 * 1000);
		const limit = options.limit;
		const since = options.since || cutoffDate;

		let totalYielded = 0;
		let offset = options.offset || 0;

		this.logger.info({
			since: since.toISOString(),
			cutoffHours: this.config.registrationCutoffHours,
		});

		while (true)
		{
			if (limit && totalYielded >= limit)
			{
				break;
			}

			try
			{
				const remainingLimit = limit ? Math.min(this.config.pageSize, limit - totalYielded) : this.config.pageSize;

				const response = await this.makeRequest('/v1/domains/registrations', {
					method: 'GET',
					headers: {
						Authorization: `Bearer ${this.authToken}`,
					},
					params: {
						since: since.toISOString(),
						limit: remainingLimit,
						offset,
						tld: 'org',
						eventType: 'create',
					},
				});

				const responseData = response.data as { registrations: PIRRegistrationEventType[] };
				const registrations = responseData.registrations;

				if (!registrations || registrations.length === 0)
				{
					this.logger.info({ offset, totalYielded });
					break;
				}

				this.logger.debug({
					count: registrations.length,
					offset,
					totalYielded,
				});

				for (const registration of registrations)
				{
					if (limit && totalYielded >= limit)
					{
						break;
					}

					// Validate registration is within our cutoff window
					const registrationDate = new Date(registration.timestamp);
					if (registrationDate < since)
					{
						continue;
					}

					const candidate: DomainCandidateType = {
						domain: registration.domain.toLowerCase(),
						source: this.name,
						metadata: {
							tld: 'org',
							registrationDate: registration.timestamp,
							registrar: registration.registrar,
							eventType: registration.eventType,
							strategy: 'zone-new',
							fetchDate: new Date().toISOString(),
						},
					};

					yield candidate;
					totalYielded += 1;

					// Add rate limiting delay
					if (this.config.rateLimitDelayMs > 0)
					{
						await AsyncUtils.sleep(this.config.rateLimitDelayMs);
					}
				}

				// Check if we've reached the end of results
				if (registrations.length < this.config.pageSize)
				{
					this.logger.info({ totalYielded });
					break;
				}

				offset += registrations.length;
			}
			catch (error)
			{
				this.logger.error({
					offset,
					error: (error as Error).message,
				});
				throw error;
			}
		}

		this.logger.info({ totalYielded });
	}

	private async fetchDomainDetails(domain: string): Promise<PIRDomainInfoType | null>
	{
		try
		{
			const response = await this.makeRequest(`/v1/domains/${domain}`, {
				method: 'GET',
				headers: {
					Authorization: `Bearer ${this.authToken}`,
				},
			});

			return response.data as PIRDomainInfoType;
		}
		catch (error)
		{
			this.logger.warn({
				domain,
				error: (error as Error).message,
			});
			return null;
		}
	}

	private async getZoneFileInfo(): Promise<PIRZoneFileInfoType | null>
	{
		try
		{
			const response = await this.makeRequest('/v1/zones/org/info', {
				method: 'GET',
				headers: {
					Authorization: `Bearer ${this.authToken}`,
				},
			});

			return response.data as PIRZoneFileInfoType;
		}
		catch (error)
		{
			this.logger.error({ error: (error as Error).message });
			return null;
		}
	}

	private async downloadZoneFile(zoneInfo: PIRZoneFileInfoType): Promise<string>
	{
		const fileName = 'pir-org-zone.txt.gz';
		const filePath = `${this.config.tempDir}/${fileName}`;

		this.logger.info({
			url: zoneInfo.url,
			size: zoneInfo.size,
			checksum: zoneInfo.checksum,
		});

		// For streaming download, we need to make a separate request with streaming enabled
		// Use streaming download to avoid memory issues with large zone files
		const streamClient = new HttpClientStream();
		await streamClient.downloadToFile(
			zoneInfo.url,
			filePath,
			{
				headers: {
					Authorization: `Bearer ${this.authToken}`,
				},
				onProgress: (downloaded, total) => 
				{
					if (total) 
					{
						const percent = Math.round((downloaded / total) * 100);
						this.logger.debug(`Download progress: ${percent}% (${downloaded}/${total} bytes)`);
					}
				},
			}
		);

		// Verify checksum if provided
		if (zoneInfo.checksum)
		{
			const actualChecksum = await this.calculateFileChecksum(filePath);
			if (actualChecksum !== zoneInfo.checksum)
			{
				throw new Error(`Zone file checksum mismatch. Expected: ${zoneInfo.checksum}, Actual: ${actualChecksum}`);
			}
		}

		this.logger.info({ filePath });

		return filePath;
	}

	private async calculateFileChecksum(filePath: string): Promise<string>
	{
		const crypto = await import('crypto');
		const hash = crypto.createHash('sha256');
		const stream = createReadStream(filePath);

		return new Promise((resolve, reject) =>
		{
			stream.on('data', data => hash.update(data));
			stream.on('end', () => resolve(hash.digest('hex')));
			stream.on('error', reject);
		});
	}

	private async* parseZoneFile(
		filePath: string,
		cutoffDate: Date,
		limit?: number,
	): AsyncIterable<DomainCandidateType>
	{
		let totalYielded = 0;

		this.logger.info({
			filePath,
			cutoffDate: cutoffDate.toISOString(),
		});

		const readStream = createReadStream(filePath);
		const gunzip = createGunzip();

		let lineBuffer = '';
		let processedLines = 0;

		readStream.pipe(gunzip);

		for await (const chunk of gunzip)
		{
			lineBuffer += chunk.toString();
			const lines = lineBuffer.split('\n');

			// Keep the last incomplete line in the buffer
			lineBuffer = lines.pop() || '';

			for (const line of lines)
			{
				processedLines += 1;

				if (limit && totalYielded >= limit)
				{
					return;
				}

				const candidate = this.parseZoneLine(line, cutoffDate);
				if (candidate)
				{
					yield candidate;
					totalYielded += 1;
				}

				// Log progress every 100k lines
				if (processedLines % 100000 === 0)
				{
					this.logger.debug({
						processedLines,
						totalYielded,
					});
				}
			}
		}

		// Process any remaining line in buffer
		if (lineBuffer.trim())
		{
			const candidate = this.parseZoneLine(lineBuffer, cutoffDate);
			if (candidate && (!limit || totalYielded < limit))
			{
				yield candidate;
				totalYielded += 1;
			}
		}

		this.logger.info({
			processedLines,
			totalYielded,
		});
	}

	private parseZoneLine(line: string, cutoffDate: Date): DomainCandidateType | null
	{
		try
		{
			// Skip comments and empty lines
			if (!line.trim() || line.startsWith(';'))
			{
				return null;
			}

			// Basic zone file record parsing
			// Format: <domain> [TTL] [class] <type> <value>
			const parts = line.trim().split(/\s+/);

			if (parts.length < 3)
			{
				return null;
			}

			let domain = parts[0];

			// Remove trailing dot if present
			if (domain.endsWith('.'))
			{
				domain = domain.slice(0, -1);
			}

			// Only process .org domains (case insensitive)
			if (!domain.toLowerCase().endsWith('.org'))
			{
				return null;
			}

			// Extract eTLD+1 (remove subdomains)
			const domainParts = domain.split('.');
			if (domainParts.length >= 2)
			{
				domain = domainParts.slice(-2).join('.');
			}

			// For zone file processing, we can't easily determine registration date
			// from the zone file alone, so we'll include all domains and rely on
			// the registration API for accurate filtering
			return {
				domain: domain.toLowerCase(),
				source: this.name,
				metadata: {
					tld: 'org',
					strategy: 'zone-new',
					zoneFileDate: new Date().toISOString(),
					estimatedRegistration: 'recent',
				},
			};
		}
		catch (error)
		{
			// Skip malformed lines
			return null;
		}
	}

	private async makeRequest<T = unknown>(
		url: string,
		config?: Record<string, unknown>,
	): Promise<HttpResponseType<T>>
	{
		const fullUrl = url.startsWith('http') ? url : `${this.config.baseUrl}${url}`;

		let lastError: Error | null = null;

		for (let attempt = 1; attempt <= this.config.maxRetries; attempt += 1)
		{
			try
			{
				this.logger.debug({ url: fullUrl, attempt });

				// Extract method from config or default to GET
				const method = (config?.method as string)?.toUpperCase() || 'GET';

				// Extract data for POST/PUT requests
				const data = config?.data;

				// Extract params and convert to query string
				const params = config?.params as Record<string, string> || {};
				const queryString = new URLSearchParams(params).toString();
				const finalUrl = queryString ? `${fullUrl}?${queryString}` : fullUrl;

				// Extract headers
				const headers = config?.headers as Record<string, string> || {};

				// Set timeout
				const timeout = config?.timeout as number || this.config.requestTimeoutMs;

				// Make request based on method
				let response: HttpResponseType<T>;

				switch (method) 
				{
					case 'POST':
						response = await this.client.post(finalUrl, data, { headers, timeout });
						break;
					case 'PUT':
						response = await this.client.put(finalUrl, data, { headers, timeout });
						break;
					case 'DELETE':
						response = await this.client.delete(finalUrl, { headers, timeout });
						break;
					case 'HEAD':
						response = await this.client.head(finalUrl, { headers, timeout });
						break;
					default: // GET
						response = await this.client.get(finalUrl, { headers, timeout });
						break;
				}

				return response;
			}
			catch (error)
			{
				lastError = error as Error;
				this.logger.warn({
					url: fullUrl,
					attempt,
					error: (error as Error).message,
				});

				if (attempt < this.config.maxRetries)
				{
					const delay = this.config.retryDelayMs * (2 ** (attempt - 1));
					await AsyncUtils.sleep(delay);
				}
			}
		}

		throw new Error(
			`Failed to make request after ${this.config.maxRetries} attempts: ${lastError?.message}`,
		);
	}

}

export type {
	PIRConfigType,
	PIRDomainInfoType,
	PIRContactType,
	PIRZoneFileInfoType,
	PIRRegistrationEventType,
};

export default PIRConnector;
