# Domain Seeder Connectors

This directory contains source connectors that implement the `SourceConnector` interface to fetch domain candidates from external sources.

## TrancoConnector

The `TrancoConnector` fetches domain data from the Tranco ranking service, which provides a research-oriented top sites ranking.

### Features

- **CSV Streaming**: Efficiently processes large Tranco CSV files using Node.js streams
- **Strategy-Aware Fetching**: Supports different discovery strategies (differential, long-tail)
- **Rate Limiting**: Built-in rate limiting to respect service ToS
- **Error Handling**: Comprehensive retry logic with exponential backoff
- **ZIP File Processing**: Automatically downloads and extracts Tranco ZIP files

### Supported Strategies

- **Differential**: Fetches all domains for comparison with historical snapshots
- **Long-tail**: Fetches only domains ranked > 1M for long-tail exploration

### Configuration

```typescript
interface TrancoConfig {
  baseUrl: string; // Default: 'https://tranco-list.eu'
  maxRetries: number; // Default: 3
  retryDelayMs: number; // Default: 1000
  requestTimeoutMs: number; // Default: 30000
  rateLimitDelayMs: number; // Default: 100
  tempDir: string; // Default: '/tmp'
}
```

### Usage

```typescript
import { TrancoConnector } from "./connectors/TrancoConnector";

const connector = new TrancoConnector({
  tempDir: "/tmp/tranco",
  rateLimitDelayMs: 200,
});

// Fetch domains with differential strategy
const options = {
  strategy: "differential",
  limit: 10000,
};

for await (const domain of connector.fetchDomains(options)) {
  console.log(`${domain.rank}: ${domain.domain}`);
}
```

### Output Format

Each domain candidate includes:

```typescript
interface DomainCandidate {
  domain: string; // Normalized domain name
  rank?: number; // Tranco ranking position
  source: string; // Always 'tranco'
  metadata?: {
    listId: string; // Tranco list identifier
    listDate: string; // List generation date
    strategy: string; // Discovery strategy used
  };
}
```

### Error Handling

The connector implements robust error handling:

- **Network Errors**: Automatic retry with exponential backoff
- **File Processing Errors**: Graceful handling of corrupted ZIP/CSV files
- **Rate Limiting**: Built-in delays to prevent overwhelming the service
- **Validation**: Filters out invalid domains and malformed records

### Testing

The connector includes comprehensive tests:

- **Unit Tests**: Core functionality and domain filtering logic
- **Integration Tests**: Configuration and strategy support
- **Mock Tests**: Network requests and file operations

Run tests with:

```bash
pnpm test TrancoConnector
```

### Implementation Notes

1. **Memory Efficiency**: Uses streaming to handle large files without loading everything into memory
2. **Temporary Files**: Downloads are stored in configurable temp directory and cleaned up
3. **List ID Extraction**: Automatically detects current Tranco list ID from HTML response
4. **Strategy Filtering**: Applies different filtering logic based on discovery strategy
5. **Rate Limiting**: Configurable delays between domain yields to respect ToS

### Requirements Satisfied

This implementation satisfies the following requirements from the task:

- ✅ **CSV Streaming**: Efficiently processes large CSV files using Node.js streams
- ✅ **Strategy-Aware Fetching**: Supports full list (differential) vs long-tail (>1M rank) modes
- ✅ **Rate Limiting**: Implements configurable rate limiting with retry logic
- ✅ **Error Handling**: Comprehensive error handling with exponential backoff
- ✅ **Integration Tests**: Includes tests with mock Tranco API responses
- ✅ **Requirements 1.1, 1.2, 9.1**: Addresses domain discovery, source integration, and ToS compliance

## CommonCrawlConnector

The `CommonCrawlConnector` fetches domain data from Common Crawl's host frequency API, specifically designed for long-tail domain discovery beyond typical top-1M lists.

### Features

- **Long-tail Focus**: Processes domains ranked 1M+ for comprehensive long-tail exploration
- **Host Frequency API**: Integrates with Common Crawl's host frequency data
- **Monthly Rollup Processing**: Supports monthly data processing with progress tracking
- **Streaming Support**: Efficiently handles large datasets using streaming
- **Quality Filtering**: Filters out spam domains and low-quality entries
- **Rate Limiting**: Conservative rate limiting to respect Common Crawl's infrastructure
- **Error Handling**: Robust retry logic with exponential backoff

### Supported Strategies

- **Long-tail**: Fetches domains ranked > 1M for long-tail exploration

### Configuration

```typescript
interface CommonCrawlConfig {
  indexUrl: string; // Default: 'https://index.commoncrawl.org/'
  maxRetries: number; // Default: 3
  retryDelayMs: number; // Default: 2000
  requestTimeoutMs: number; // Default: 60000
  rateLimitDelayMs: number; // Default: 500
  pageSize: number; // Default: 10000
  longTailThreshold: number; // Default: 1000000
  maxRank: number; // Default: 10000000
  monthlyRollupEnabled: boolean; // Default: true
}
```

### Usage

```typescript
import { CommonCrawlConnector } from "./connectors/CommonCrawlConnector";

const connector = new CommonCrawlConnector({
  rateLimitDelayMs: 1000,
  longTailThreshold: 2000000, // Start from rank 2M
});

// Fetch long-tail domains
const options = {
  strategy: "long-tail",
  limit: 50000,
  offset: 1500000, // Start from rank 1.5M
};

for await (const domain of connector.fetchDomains(options)) {
  console.log(
    `${domain.rank}: ${domain.domain} (freq: ${domain.metadata?.frequency})`
  );
}
```

### Output Format

Each domain candidate includes:

```typescript
interface DomainCandidate {
  domain: string; // Normalized domain name
  rank?: number; // Common Crawl ranking position
  source: string; // Always 'common-crawl'
  metadata?: {
    frequency: number; // Host frequency in crawl data
    lastSeen: string; // Last seen timestamp
    responseCode?: number; // HTTP response code
    contentType?: string; // Content type
    category?: string; // Domain category
    strategy: string; // Discovery strategy used
    fetchDate: string; // Fetch timestamp
    indexId: string; // Common Crawl index ID
    indexName: string; // Common Crawl index name
  };
}
```

### Quality Filtering

The connector implements several quality filters:

- **Frequency Threshold**: Domains must have frequency ≥ 2
- **Response Code**: Filters out domains with HTTP errors (≥ 400)
- **Spam Detection**: Removes domains with spam patterns
- **Long-tail Range**: Only processes domains ranked 1M-10M

### Monthly Rollup Processing

```typescript
// Process monthly rollup for comprehensive coverage
const rollup = await connector.processMonthlyRollup("2024-10");

console.log(`Processed ${rollup.processedHosts} of ${rollup.totalHosts} hosts`);
console.log(`Completed: ${rollup.completed}`);

// Check rollup status
const status = connector.getMonthlyRollupStatus("2024-10");
```

### Error Handling

The connector implements comprehensive error handling:

- **Network Errors**: Automatic retry with exponential backoff
- **API Errors**: Graceful handling of Common Crawl API issues
- **Rate Limiting**: Built-in delays to prevent overwhelming the service
- **Data Validation**: Filters out invalid or malformed entries

### Testing

The connector includes comprehensive tests:

- **Unit Tests**: Core functionality and domain filtering logic
- **Integration Tests**: API interaction and strategy support
- **Mock Tests**: Network requests and error scenarios

Run tests with:

```bash
pnpm test CommonCrawlConnector
```

### Implementation Notes

1. **Conservative Rate Limiting**: Uses 500ms delays to respect Common Crawl's infrastructure
2. **Large Page Sizes**: Processes 10K domains per batch for efficiency
3. **Quality Focus**: Prioritizes domains with real traffic and valid responses
4. **Long-tail Specialization**: Designed specifically for domains beyond top-1M
5. **Monthly Processing**: Supports comprehensive monthly data processing

### Requirements Satisfied

This implementation satisfies the following requirements from the task:

- ✅ **Host Frequency API Integration**: Connects to Common Crawl's host frequency API
- ✅ **Long-tail Processing**: Processes domains starting from rank 1M+ with streaming
- ✅ **Monthly Rollup Processing**: Implements monthly data processing with progress tracking
- ✅ **Frequency-based Ranking**: Uses Common Crawl's frequency data for ranking
- ✅ **Mock Tests**: Includes comprehensive tests with mock Common Crawl responses
- ✅ **Requirements 1.1, 1.2, 7.3**: Addresses domain discovery, source integration, and long-tail exploration

## PIRConnector

The `PIRConnector` fetches domain data from the PIR (Public Interest Registry) .org registry API, specifically designed for discovering newly registered .org domains through registry API integration and zone file processing.

### Features

- **Registry API Integration**: Connects to PIR's official registry API with authentication
- **New Domain Discovery**: Focuses on newly registered .org domains using zone-new strategy
- **Registration Metadata**: Extracts comprehensive registration metadata including registrar, dates, and contacts
- **Zone File Processing**: Processes PIR .org zone files for comprehensive domain discovery
- **Authentication Management**: Handles API token authentication with automatic renewal
- **Rate Limiting**: Conservative rate limiting to respect registry API limits
- **Error Handling**: Robust retry logic with exponential backoff
- **Checksum Verification**: Verifies zone file integrity using checksums

### Supported Strategies

- **Zone-new**: Fetches newly registered .org domains from registry API and zone files

### Configuration

```typescript
interface PIRConfig {
  baseUrl: string; // Default: 'https://api.pir.org'
  apiKey: string; // Required: PIR API key
  maxRetries: number; // Default: 3
  retryDelayMs: number; // Default: 1000
  requestTimeoutMs: number; // Default: 60000
  rateLimitDelayMs: number; // Default: 200
  tempDir: string; // Default: '/tmp'
  registrationCutoffHours: number; // Default: 48
  pageSize: number; // Default: 1000
}
```

### Usage

```typescript
import { PIRConnector } from "./connectors/PIRConnector";

// Set PIR API key in environment
process.env.PIR_API_KEY = "your-pir-api-key";

const connector = new PIRConnector({
  tempDir: "/tmp/pir",
  rateLimitDelayMs: 200,
  registrationCutoffHours: 24, // Only domains registered in last 24 hours
});

// Fetch newly registered .org domains
const options = {
  strategy: "zone-new",
  limit: 5000,
  since: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
};

for await (const domain of connector.fetchDomains(options)) {
  console.log(`${domain.domain} registered by ${domain.metadata?.registrar}`);
}
```

### Output Format

Each domain candidate includes:

```typescript
interface DomainCandidate {
  domain: string; // Normalized .org domain name
  source: string; // Always 'pir'
  metadata?: {
    tld: string; // Always 'org'
    registrationDate: string; // ISO timestamp of registration
    registrar: string; // Registrar name
    eventType: string; // 'create', 'update', 'delete', 'transfer'
    strategy: string; // Discovery strategy used
    fetchDate: string; // Fetch timestamp
    // Zone file specific metadata
    zoneFileDate?: string; // Zone file processing timestamp
    estimatedRegistration?: string; // 'recent' for zone file entries
  };
}
```

### Authentication

The connector requires a PIR API key for authentication:

```bash
export PIR_API_KEY="your-pir-registry-api-key"
```

The connector automatically handles:

- Token acquisition and renewal
- Token expiry detection
- Authentication failures with retry

### Zone File Processing

The connector can process PIR .org zone files for comprehensive domain discovery:

- **Download**: Securely downloads compressed zone files
- **Verification**: Verifies file integrity using SHA-256 checksums
- **Streaming**: Processes large zone files using streaming
- **Parsing**: Extracts .org domains from zone file records
- **eTLD+1 Extraction**: Normalizes subdomains to eTLD+1 format

### Error Handling

The connector implements comprehensive error handling:

- **Network Errors**: Automatic retry with exponential backoff
- **API Errors**: Graceful handling of registry API issues
- **Authentication Errors**: Automatic token renewal
- **Rate Limiting**: Built-in delays to respect API limits
- **Data Validation**: Filters out invalid or malformed entries

### Testing

The connector includes comprehensive tests:

- **Unit Tests**: Core functionality and domain parsing logic
- **Integration Tests**: API interaction and authentication flows
- **Mock Tests**: Network requests and error scenarios

Run tests with:

```bash
pnpm test PIRConnector
```

### Implementation Notes

1. **Conservative Rate Limiting**: Uses 200ms delays to respect PIR's API infrastructure
2. **Registry Focus**: Designed specifically for newly registered .org domains
3. **Metadata Rich**: Provides comprehensive registration metadata
4. **Zone File Support**: Supports both API and zone file discovery methods
5. **Authentication Aware**: Handles PIR's OAuth-style authentication

### Requirements Satisfied

This implementation satisfies the following requirements from the task:

- ✅ **PIR Registry API Integration**: Connects to PIR's official .org registry API
- ✅ **Registration Metadata Extraction**: Extracts comprehensive domain registration data
- ✅ **Zone File Processing**: Implements zone file processing for newly registered .org domains
- ✅ **Authentication Management**: Handles API authentication with automatic renewal
- ✅ **Mock Tests**: Includes comprehensive tests with mock PIR registry responses
- ✅ **Requirements 1.1, 1.2, 7.2**: Addresses domain discovery, source integration, and registry data access

## SonarConnector

The `SonarConnector` fetches domain data from Rapid7's FDNS (Forward DNS) dataset through the Sonar API, specifically designed for long-tail domain discovery from DNS resolution data.

### Features

- **API Integration**: Connects to Rapid7's Sonar FDNS API with authentication
- **Long-tail Discovery**: Discovers domains from DNS resolution data beyond typical top lists
- **Data Streaming**: Efficiently processes large DNS datasets using streaming
- **Monthly Processing**: Supports monthly data processing with backfill capabilities
- **Quality Filtering**: Filters out spam, private, and low-quality domains
- **Rate Limiting**: Conservative rate limiting to respect Rapid7's API infrastructure
- **Error Handling**: Robust retry logic with exponential backoff
- **DNS Record Processing**: Processes A records to extract domain names

### Supported Strategies

- **Long-tail**: Fetches domains from DNS resolution data for long-tail exploration

### Configuration

```typescript
interface SonarConfig {
  baseUrl: string; // Default: 'https://api.labs.rapid7.com'
  apiKey: string; // Required: Rapid7 API key
  maxRetries: number; // Default: 3
  retryDelayMs: number; // Default: 2000
  requestTimeoutMs: number; // Default: 120000
  rateLimitDelayMs: number; // Default: 1000
  pageSize: number; // Default: 10000
  longTailThreshold: number; // Default: 1000000
  maxResults: number; // Default: 50000000
  monthlyProcessingEnabled: boolean; // Default: true
  backfillDays: number; // Default: 30
}
```

### Usage

```typescript
import { SonarConnector } from "./connectors/SonarConnector";

// Set Rapid7 API key in environment
process.env.RAPID7_API_KEY = "your-rapid7-api-key";

const connector = new SonarConnector({
  rateLimitDelayMs: 1500,
  pageSize: 5000,
  backfillDays: 7, // Process last 7 days
});

// Fetch long-tail domains from DNS data
const options = {
  strategy: "long-tail",
  limit: 10000,
  since: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
};

for await (const domain of connector.fetchDomains(options)) {
  console.log(
    `${domain.domain} (${domain.metadata?.resolutionCount} resolutions)`
  );
}
```

### Output Format

Each domain candidate includes:

```typescript
interface DomainCandidate {
  domain: string; // Normalized domain name
  source: string; // Always 'sonar'
  metadata?: {
    dnsType: string; // DNS record type (A, AAAA, etc.)
    dnsValue: string; // DNS record value (IP address)
    timestamp: string; // DNS record timestamp
    dnsSource: string; // DNS data source
    resolutionCount: number; // Number of DNS resolutions
    firstSeen: string; // First seen timestamp
    lastSeen: string; // Last seen timestamp
    strategy: string; // Discovery strategy used
    fetchDate: string; // Fetch timestamp
    discoveryMethod: string; // Always 'fdns-resolution'
  };
}
```

### Authentication

The connector requires a Rapid7 API key for authentication:

```bash
export RAPID7_API_KEY="your-rapid7-api-key"
```

The connector automatically handles:

- API key authentication via X-API-Key header
- Request authentication failures with retry
- Rate limiting to respect API quotas

### Quality Filtering

The connector implements several quality filters:

- **Private Domains**: Filters out localhost, .local, .internal, and private IP ranges
- **Spam Detection**: Removes domains with spam patterns and suspicious TLDs
- **Resolution Count**: Requires minimum 2 DNS resolutions to indicate real usage
- **Age Filter**: Excludes domains first seen less than 24 hours ago
- **Wildcard Filter**: Removes wildcard DNS records
- **CDN Filter**: Excludes CDN and infrastructure domains

### Monthly Backfill Processing

```typescript
// Process monthly backfill for comprehensive coverage
const backfill = await connector.processMonthlyBackfill("2024-01");

console.log(
  `Processed ${backfill.processedRecords} of ${backfill.totalRecords} records`
);
console.log(`Completed: ${backfill.completed}`);

// Check backfill status
const status = connector.getMonthlyBackfillStatus("2024-01");
```

### Error Handling

The connector implements comprehensive error handling:

- **Network Errors**: Automatic retry with exponential backoff
- **API Errors**: Graceful handling of Rapid7 API issues
- **Rate Limiting**: Built-in delays to prevent overwhelming the service
- **Data Validation**: Filters out invalid or malformed DNS records
- **Authentication Errors**: Proper handling of API key issues

### Testing

The connector includes comprehensive tests:

- **Unit Tests**: Core functionality and domain filtering logic
- **Integration Tests**: API interaction and strategy support
- **Mock Tests**: Network requests and error scenarios

Run tests with:

```bash
pnpm test SonarConnector
```

### Implementation Notes

1. **Conservative Rate Limiting**: Uses 1000ms delays to respect Rapid7's API infrastructure
2. **Large Page Sizes**: Processes 10K DNS records per batch for efficiency
3. **Quality Focus**: Prioritizes domains with real DNS traffic and valid resolutions
4. **Long-tail Specialization**: Designed specifically for domains beyond typical top lists
5. **Monthly Processing**: Supports comprehensive monthly DNS data processing
6. **DNS Record Focus**: Processes A records to discover IPv4-resolved domains

### Requirements Satisfied

This implementation satisfies the following requirements from the task:

- ✅ **API Integration**: Connects to Rapid7's Sonar FDNS API with authentication
- ✅ **Data Streaming**: Efficiently processes large DNS datasets using streaming
- ✅ **Long-tail Discovery**: Discovers domains from DNS resolution data beyond top lists
- ✅ **Monthly Processing**: Implements monthly data processing with backfill support
- ✅ **Mock Tests**: Includes comprehensive tests with mock FDNS data and API responses
- ✅ **Requirements 1.1, 1.2, 7.3**: Addresses domain discovery, source integration, and long-tail exploration
