import { logger as sharedLogger, type LoggerInstanceType, AsyncUtils } from '@shared';
import HttpClient, { type HttpResponseType } from '@shared/utils/HttpClient';
import type { DiscoveryStrategyType } from '../interfaces/DiscoveryEngine';
import type {
	DomainCandidateType,
	FetchOptionsType,
	SourceConnectorType,
} from '../interfaces/SourceConnector';


type CommonCrawlConfigType =
{
	indexUrl: string;
	maxRetries: number;
	retryDelayMs: number;
	requestTimeoutMs: number;
	rateLimitDelayMs: number;
	pageSize: number;
	longTailThreshold: number;
	maxRank: number;
	monthlyRollupEnabled: boolean;
};

type CommonCrawlIndexType =
{
	id: string;
	name: string;
	timegate: string;
	collections: Array<{
		id: string;
		name: string;
		timegate: string;
	}>;
};

type CommonCrawlHostDataType =
{
	host: string;
	frequency: number;
	rank: number;
	lastSeen: string;
	responseCode?: number;
	contentType?: string;
	category?: string;
};

type CommonCrawlApiResponseType =
{
	hosts: CommonCrawlHostDataType[];
	total: number;
	offset: number;
	limit: number;
	index: string;
	timestamp: string;
};

type MonthlyRollupType =
{
	month: string;
	totalHosts: number;
	processedHosts: number;
	lastProcessedRank: number;
	completed: boolean;
};

class CommonCrawlConnector implements SourceConnectorType
{
	public readonly name = 'common-crawl';

	public readonly priority = 6;

	public readonly cadence = 'monthly' as const;

	private readonly config: CommonCrawlConfigType;

	private readonly logger: LoggerInstanceType;

	private readonly client: HttpClient;

	private lastFetchTime: Date | null = null;

	private currentIndex: CommonCrawlIndexType | null = null;

	private monthlyRollups: Map<string, MonthlyRollupType> = new Map();

	constructor(config?: Partial<CommonCrawlConfigType>)
	{
		const indexUrl = process.env.COMMON_CRAWL_INDEX_URL || 'https://index.commoncrawl.org/';

		this.config = {
			indexUrl,
			maxRetries: 3,
			retryDelayMs: 2000, // Higher delay for Common Crawl
			requestTimeoutMs: 60000, // Longer timeout for large responses
			rateLimitDelayMs: 500, // Conservative rate limiting
			pageSize: 10000, // Larger page size for efficiency
			longTailThreshold: 1000000, // Start from rank 1M+
			maxRank: 10000000, // Process up to 10M
			monthlyRollupEnabled: true,
			...config,
		};

		this.logger = sharedLogger.getLogger('CommonCrawlConnector');
		this.client = new HttpClient(this.logger, {
			timeout: this.config.requestTimeoutMs,
		});
	}

	supportsStrategy(strategy: DiscoveryStrategyType): boolean
	{
		// Common Crawl primarily supports long-tail exploration
		return strategy === 'long-tail';
	}

	async* fetchDomains(options: FetchOptionsType): AsyncIterable<DomainCandidateType>
	{
		this.logger.info({ options }, 'Starting Common Crawl domain fetch');

		try
		{
			// Get current index information
			const index = await this.getCurrentIndex();
			this.currentIndex = index;

			// For long-tail strategy, process domains starting from 1M+
			if (options.strategy === 'long-tail')
			{
				yield* this.fetchLongTailDomains(options);
			}
			else
			{
				this.logger.warn({
					strategy: options.strategy,
					supported: ['long-tail'],
				}, 'Unsupported strategy for Common Crawl');
				return;
			}

			this.lastFetchTime = new Date();
			this.logger.info('Common Crawl domain fetch completed successfully');
		}
		catch (error)
		{
			this.logger.error({
				error: (error as Error).message,
			}, 'Failed to fetch Common Crawl domains');
			throw error;
		}
	}

	async getLastUpdate(): Promise<Date | null>
	{
		return this.lastFetchTime;
	}

	async healthCheck(): Promise<boolean>
	{
		try
		{
			const response = await this.makeRequest('');
			return response.status === 200;
		}
		catch (error)
		{
			this.logger.error({
				error: (error as Error).message,
			}, 'Common Crawl health check failed');
			return false;
		}
	}

	/**
	 * Get current Common Crawl index information
	 */
	private async getCurrentIndex(): Promise<CommonCrawlIndexType>
	{
		this.logger.debug('Fetching Common Crawl index information');

		const response = await this.makeRequest('collinfo.json');
		const indexes = response.data as CommonCrawlIndexType[];

		if (!indexes || indexes.length === 0)
		{
			throw new Error('No Common Crawl indexes available');
		}

		// Get the most recent index
		const latestIndex = indexes[0];

		this.logger.info({
			id: latestIndex.id,
			name: latestIndex.name,
		}, 'Using Common Crawl index');

		return latestIndex;
	}

	/**
	 * Fetch long-tail domains starting from rank 1M+
	 */
	private async* fetchLongTailDomains(options: FetchOptionsType): AsyncIterable<DomainCandidateType>
	{
		const startRank = Math.max(
			options.offset || this.config.longTailThreshold,
			this.config.longTailThreshold,
		);
		const limit = options.limit;
		const maxRank = Math.min(
			startRank + (limit || this.config.maxRank),
			this.config.maxRank,
		);

		this.logger.info({
			startRank,
			maxRank,
			limit,
		});

		let currentRank = startRank;
		let yieldedCount = 0;

		// Process in batches to handle large datasets efficiently
		while (currentRank < maxRank && (!limit || yieldedCount < limit))
		{
			const batchSize = Math.min(
				this.config.pageSize,
				maxRank - currentRank,
				limit ? limit - yieldedCount : this.config.pageSize,
			);

			try
			{
				const batchDomains = await this.fetchHostBatch(currentRank, batchSize);

				for (const hostData of batchDomains)
				{
					if (limit && yieldedCount >= limit)
					{
						break;
					}

					// Apply long-tail filtering
					if (this.isValidLongTailDomain(hostData))
					{
						const candidate = this.createDomainCandidate(hostData, options);
						yieldedCount++;
						yield candidate;

						// Add rate limiting delay
						if (this.config.rateLimitDelayMs > 0)
						{
							await AsyncUtils.sleep(this.config.rateLimitDelayMs);
						}
					}
				}

				currentRank += batchSize;

				// Add delay between batches
				if (currentRank < maxRank && this.config.rateLimitDelayMs > 0)
				{
					await AsyncUtils.sleep(this.config.rateLimitDelayMs * 2);
				}
			}
			catch (error)
			{
				this.logger.error({
					currentRank,
					batchSize,
					error: (error as Error).message,
				});

				// Continue with next batch on error
				currentRank += batchSize;
			}
		}

		this.logger.info({
			startRank,
			endRank: currentRank,
			yieldedCount,
		});
	}

	/**
	 * Fetch a batch of host data from Common Crawl
	 */
	private async fetchHostBatch(
		startRank: number,
		batchSize: number,
	): Promise<CommonCrawlHostDataType[]>
	{
		if (!this.currentIndex)
		{
			throw new Error('No Common Crawl index available');
		}

		this.logger.debug({
			startRank,
			batchSize,
			index: this.currentIndex.id,
		});

		// Common Crawl host frequency API endpoint
		const endpoint = `${this.currentIndex.id}/host-frequency`;
		const response = await this.makeRequest(endpoint, {
			params: {
				offset: startRank,
				limit: batchSize,
				format: 'json',
			},
		});

		const apiResponse = response.data as CommonCrawlApiResponseType;

		if (!apiResponse.hosts || !Array.isArray(apiResponse.hosts))
		{
			this.logger.warn({
				response: apiResponse,
			});
			return [];
		}

		// Transform API response to our format
		const hostData = apiResponse.hosts.map((host, index) => ({
			host: host.host,
			frequency: host.frequency || 1,
			rank: host.rank || (startRank + index),
			lastSeen: host.lastSeen || apiResponse.timestamp,
			responseCode: host.responseCode,
			contentType: host.contentType,
			category: host.category,
		}));

		this.logger.debug({
			startRank,
			batchSize,
			actualSize: hostData.length,
		});

		return hostData;
	}

	/**
	 * Check if domain qualifies as valid long-tail domain
	 */
	private isValidLongTailDomain(hostData: CommonCrawlHostDataType): boolean
	{
		// Basic validation
		if (!hostData.host || hostData.host.length === 0)
		{
			return false;
		}

		// Must be in long-tail range
		if (hostData.rank <= this.config.longTailThreshold)
		{
			return false;
		}

		// Must have minimum frequency to indicate real traffic
		if (hostData.frequency < 2)
		{
			return false;
		}

		// Skip obvious spam or invalid domains
		if (this.isLikelySpamDomain(hostData.host))
		{
			return false;
		}

		// Prefer domains with successful HTTP responses
		if (hostData.responseCode && hostData.responseCode >= 400)
		{
			return false;
		}

		return true;
	}

	/**
	 * Create domain candidate from host data
	 */
	private createDomainCandidate(
		hostData: CommonCrawlHostDataType,
		options: FetchOptionsType,
	): DomainCandidateType
	{
		return {
			domain: hostData.host.toLowerCase().trim(),
			rank: hostData.rank,
			source: this.name,
			metadata: {
				frequency: hostData.frequency,
				lastSeen: hostData.lastSeen,
				responseCode: hostData.responseCode,
				contentType: hostData.contentType,
				category: hostData.category,
				strategy: options.strategy || 'long-tail',
				fetchDate: new Date().toISOString(),
				indexId: this.currentIndex?.id,
				indexName: this.currentIndex?.name,
			},
		};
	}

	/**
	 * Check if domain appears to be spam or low quality
	 */
	private isLikelySpamDomain(domain: string): boolean
	{
		const spamPatterns = [
			// Excessive numbers
			/\d{8,}/,
			// Random character sequences
			/[a-z]{25,}/,
			// Known spam patterns
			/^(www\d+|test\d+|temp\d+|spam\d+)\./,
			// Suspicious TLDs
			/\.(tk|ml|ga|cf|click|download)$/,
			// IP addresses
			/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,
		];

		return spamPatterns.some(pattern => pattern.test(domain));
	}

	/**
	 * Process monthly rollup for comprehensive coverage
	 */
	async processMonthlyRollup(month: string): Promise<MonthlyRollupType>
	{
		if (!this.config.monthlyRollupEnabled)
		{
			throw new Error('Monthly rollup processing is disabled');
		}

		this.logger.info({ month });

		const rollup: MonthlyRollupType =
		{
			month,
			totalHosts: 0,
			processedHosts: 0,
			lastProcessedRank: this.config.longTailThreshold,
			completed: false,
		};

		try
		{
			// Get total host count for the month
			const totalResponse = await this.makeRequest('stats', {
				params: {
					month,
					metric: 'host-count',
				},
			});

			rollup.totalHosts = totalResponse.data.total || 0;

			// Process in chunks
			let currentRank = this.config.longTailThreshold;
			const chunkSize = this.config.pageSize;

			while (currentRank < this.config.maxRank)
			{
				try
				{
					const batch = await this.fetchHostBatch(currentRank, chunkSize);
					rollup.processedHosts += batch.length;
					rollup.lastProcessedRank = currentRank + batch.length;

					// Store rollup progress
					this.monthlyRollups.set(month, { ...rollup });

					currentRank += chunkSize;

					// Rate limiting
					await AsyncUtils.sleep(this.config.rateLimitDelayMs);
				}
				catch (error)
				{
					this.logger.error({
						month,
						currentRank,
						error: (error as Error).message,
					});
					break;
				}
			}

			rollup.completed = true;
			this.monthlyRollups.set(month, rollup);

			this.logger.info({
				month,
				processedHosts: rollup.processedHosts,
				totalHosts: rollup.totalHosts,
			});

			return rollup;
		}
		catch (error)
		{
			this.logger.error({
				month,
				error: (error as Error).message,
			});
			throw error;
		}
	}

	/**
	 * Get monthly rollup status
	 */
	getMonthlyRollupStatus(month: string): MonthlyRollupType | null
	{
		return this.monthlyRollups.get(month) || null;
	}

	/**
	 * Make HTTP request with retry logic
	 */
	private async makeRequest(
		endpoint: string,
		config?: Record<string, unknown>,
	): Promise<any>
	{
		const url = endpoint.startsWith('http')
			? endpoint
			: `${this.config.indexUrl}${endpoint}`;

		let lastError: Error | null = null;

		for (let attempt = 1; attempt <= this.config.maxRetries; attempt++)
		{
			try
			{
				this.logger.debug({
					url,
					attempt,
				});

				// Extract method from config or default to GET
				const method = (config?.method as string)?.toUpperCase() || 'GET';

				// Extract params and convert to query string
				const params = config?.params as Record<string, string> || {};
				const queryString = new URLSearchParams(params).toString();
				const fullUrl = queryString ? `${url}?${queryString}` : url;

				// Extract headers
				const headers = {
					'User-Agent': 'DomainSeeder/1.0 (Common Crawl Connector)',
					Accept: 'application/json',
					...(config?.headers as Record<string, string> || {}),
				};

				// Set timeout
				const timeout = config?.timeout as number || this.config.requestTimeoutMs;

				// Make request based on method
				let response: HttpResponseType;

				switch (method) 
				{
					case 'POST':
						response = await this.client.post(fullUrl, config?.data, { headers, timeout });
						break;
					case 'PUT':
						response = await this.client.put(fullUrl, config?.data, { headers, timeout });
						break;
					case 'DELETE':
						response = await this.client.delete(fullUrl, { headers, timeout });
						break;
					case 'HEAD':
						response = await this.client.head(fullUrl, { headers, timeout });
						break;
					default: // GET
						response = await this.client.get(fullUrl, { headers, timeout });
						break;
				}

				return response;
			}
			catch (error)
			{
				lastError = error as Error;
				this.logger.warn({
					url,
					attempt,
					error: (error as Error).message,
				});

				if (attempt < this.config.maxRetries)
				{
					const delay = this.config.retryDelayMs * (2 ** (attempt - 1));
					await AsyncUtils.sleep(delay);
				}
			}
		}

		throw new Error(
			`Failed to make Common Crawl API request after ${this.config.maxRetries} attempts: ${lastError?.message}`,
		);
	}

	/**
	 * Sleep for specified milliseconds
	 */
}

export type {
	CommonCrawlConfigType,
	CommonCrawlIndexType,
	CommonCrawlHostDataType,
	CommonCrawlApiResponseType,
	MonthlyRollupType,
};

export default CommonCrawlConnector;
