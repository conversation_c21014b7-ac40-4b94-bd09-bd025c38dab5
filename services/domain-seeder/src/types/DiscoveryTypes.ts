/**
 * Type definitions for domain discovery system
 */

export type DiscoveryStrategyType =
  | 'zone-file'
  | 'tranco'
  | 'czds'
  | 'umbrella'
  | 'radar'
  | 'sonar'
  | 'common-crawl'
  | 'pir'
  | 'heuristics'
  | 'differential'
  | 'long-tail'
  | 'temporal';

export interface DiscoveredDomainInterface {
  domain: string;
  source: string;
  discoveryStrategy: DiscoveryStrategyType;
  confidence: number;
  discoveryReason: string;
  rank?: number;
  metadata?: Record<string, unknown>;
}

export interface ContentMethodType {
  method: 'preGenerated' | 'live' | 'heuristics';
}