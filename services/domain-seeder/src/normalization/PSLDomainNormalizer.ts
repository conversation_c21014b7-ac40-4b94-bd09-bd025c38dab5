import { domainValidator, logger } from '@shared';
import { isIP } from 'node:net';
import punycode from 'punycode';

import type {
	DomainNormalizerType,
	NormalizedDomainType,
	NormalizationMetricsType,
} from '../interfaces/DomainNormalizer';
import PSLManagerImpl from './PSLManager';

class PSLDomainNormalizer implements DomainNormalizerType
{
	private logger: ReturnType<typeof logger.getLogger>;

	private pslManager: PSLManagerImpl;

	private metrics: NormalizationMetricsType;

	constructor(cacheDir?: string)
	{
		this.logger = logger.getLogger('PSLDomainNormalizer');
		this.pslManager = new PSLManagerImpl(cacheDir);
		this.metrics = {
			totalProcessed: 0,
			validDomains: 0,
			invalidDomains: 0,
			ipAddressesFiltered: 0,
			duplicatesRemoved: 0,
		};
	}

	normalize(domain: string): NormalizedDomainType | null
	{
		this.metrics.totalProcessed += 1;

		try
		{
			// Basic input validation
			if (!domain || typeof domain !== 'string')
			{
				this.metrics.invalidDomains += 1;
				return this.createInvalidResult(domain, ['Empty or invalid domain input']);
			}

			// Clean and prepare domain
			const cleaned = this.cleanDomain(domain);
			if (!cleaned)
			{
				this.metrics.invalidDomains += 1;
				return this.createInvalidResult(domain, ['Domain could not be cleaned']);
			}

			// Check for IP addresses
			if (this.isIPAddress(cleaned))
			{
				this.metrics.ipAddressesFiltered += 1;
				return null; // IP addresses are filtered out completely
			}

			// Convert to punycode if needed
			const punycoded = this.toPunycode(cleaned);
			if (!punycoded)
			{
				this.metrics.invalidDomains += 1;
				return this.createInvalidResult(domain, ['Failed to convert to punycode']);
			}

			// Extract eTLD+1 using PSL
			const etld1 = this.pslManager.getDomain(punycoded);
			if (!etld1)
			{
				this.metrics.invalidDomains += 1;
				return this.createInvalidResult(domain, ['Could not extract eTLD+1 - invalid or unknown TLD']);
			}

			// Get TLD
			const tld = this.pslManager.getPublicSuffix(punycoded);
			if (!tld)
			{
				this.metrics.invalidDomains += 1;
				return this.createInvalidResult(domain, ['Could not determine TLD']);
			}

			// Validate the extracted domain
			if (!this.isValidDomainFormat(etld1))
			{
				this.metrics.invalidDomains += 1;
				return this.createInvalidResult(domain, ['Invalid domain format after normalization']);
			}

			this.metrics.validDomains += 1;
			return {
				original: domain,
				normalized: punycoded,
				etld1,
				tld,
				isValid: true,
			};
		}
		catch (error)
		{
			this.metrics.invalidDomains += 1;
			this.logger.error({ msg: 'Domain normalization failed', domain, error });
			return this.createInvalidResult(domain, [`Normalization error: ${error instanceof Error ? error.message : 'Unknown error'}`]);
		}
	}

	private cleanDomain(domain: string): string | null
	{
		try
		{
			let cleaned = domain.trim().toLowerCase();

			// Remove protocol prefixes
			cleaned = cleaned.replace(/^https?:\/\//, '');
			cleaned = cleaned.replace(/^ftp:\/\//, '');
			cleaned = cleaned.replace(/^\/\//, '');

			// Remove www prefix (but preserve other subdomains)
			cleaned = cleaned.replace(/^www\./, '');

			// Remove path, query, and fragment
			cleaned = cleaned.split('/')[0];
			cleaned = cleaned.split('?')[0];
			cleaned = cleaned.split('#')[0];

			// Remove port numbers
			cleaned = cleaned.replace(/:\d+$/, '');

			// Remove trailing dots
			cleaned = cleaned.replace(/\.$/, '');

			// Basic validation
			if (!cleaned || cleaned.length === 0)
			{
				return null;
			}

			// Check for invalid characters (basic check)
			if (!/^[a-z0-9.-]+$/i.test(cleaned))
			{
				// Allow IDN characters, but check for obviously invalid ones
				if (/[\s<>\"'`]/.test(cleaned))
				{
					return null;
				}
			}

			return cleaned;
		}
		catch (error)
		{
			this.logger.warn({ msg: 'Domain cleaning failed', domain, error });
			return null;
		}
	}

	private isIPAddress(input: string): boolean
	{
		// Check for IPv4 and IPv6 addresses
		return isIP(input) !== 0;
	}

	private toPunycode(domain: string): string | null
	{
		try
		{
			// Split domain into parts
			const parts = domain.split('.');
			const punycoded = parts.map((part) =>
			{
				// Check if part contains non-ASCII characters
				if (/[^\x00-\x7F]/.test(part))
				{
					try
					{
						return punycode.toASCII(part);
					}
					catch (error)
					{
						this.logger.warn({ msg: 'Punycode conversion failed for part', part, error });
						return part; // Return original if conversion fails
					}
				}
				return part;
			});

			const result = punycoded.join('.');

			// Validate result
			if (result.length > 253)
			{
				// Domain name too long
				return null;
			}

			// Check each label length (max 63 characters)
			for (const label of punycoded)
			{
				if (label.length > 63)
				{
					return null;
				}
			}

			return result;
		}
		catch (error)
		{
			this.logger.warn({ msg: 'Punycode conversion failed', domain, error });
			return null;
		}
	}

	private isValidDomainFormat(domain: string): boolean
	{
		// Basic domain format validation
		if (!domain || domain.length === 0 || domain.length > 253)
		{
			return false;
		}

		// Must contain at least one dot
		if (!domain.includes('.'))
		{
			return false;
		}

		// Split into labels
		const labels = domain.split('.');

		// Must have at least 2 labels
		if (labels.length < 2)
		{
			return false;
		}

		// Validate each label
		for (const label of labels)
		{
			if (!this.isValidLabel(label))
			{
				return false;
			}
		}

		return true;
	}

	private isValidLabel(label: string): boolean
	{
		// Label cannot be empty or too long
		if (!label || label.length === 0 || label.length > 63)
		{
			return false;
		}

		// Label cannot start or end with hyphen
		if (label.startsWith('-') || label.endsWith('-'))
		{
			return false;
		}

		// Label must contain only valid characters
		if (!/^[a-z0-9-]+$/i.test(label))
		{
			return false;
		}

		return true;
	}

	private createInvalidResult(original: string, errors: string[]): NormalizedDomainType
	{
		return {
			original,
			normalized: '',
			etld1: '',
			tld: '',
			isValid: false,
			errors,
		};
	}

	isValidDomain(domain: string): boolean
	{
		const result = this.normalize(domain);
		return result !== null && result.isValid;
	}

	getETLD1(domain: string): string | null
	{
		const result = this.normalize(domain);
		return result?.isValid ? result.etld1 : null;
	}

	async batchNormalize(domains: string[]): Promise<Map<string, NormalizedDomainType>>
	{
		const results = new Map<string, NormalizedDomainType>();
		const seen = new Set<string>();

		for (const domain of domains)
		{
			// Skip duplicates
			if (seen.has(domain))
			{
				this.metrics.duplicatesRemoved++;
				continue;
			}
			seen.add(domain);

			const normalized = this.normalize(domain);
			if (normalized)
			{
				results.set(domain, normalized);
			}
		}

		return results;
	}

	getMetrics(): NormalizationMetricsType
	{
		return { ...this.metrics };
	}

	resetMetrics(): void
	{
		this.metrics = {
			totalProcessed: 0,
			validDomains: 0,
			invalidDomains: 0,
			ipAddressesFiltered: 0,
			duplicatesRemoved: 0,
		};
	}

	async healthCheck(): Promise<boolean>
	{
		try
		{
			// Check PSL manager health
			const pslHealthy = await this.pslManager.healthCheck();
			if (!pslHealthy)
			{
				return false;
			}

			// Test basic functionality
			const testResult = this.normalize('www.example.com');
			if (!testResult || !testResult.isValid || testResult.etld1 !== 'example.com')
			{
				return false;
			}

			// Test IDN functionality
			const idnResult = this.normalize('тест.example.com');
			if (!idnResult || !idnResult.isValid)
			{
				return false;
			}

			return true;
		}
		catch (error)
		{
			this.logger.error('Health check failed:', error);
			return false;
		}
	}

	destroy(): void
	{
		this.pslManager.destroy();
	}
}

export default PSLDomainNormalizer;
