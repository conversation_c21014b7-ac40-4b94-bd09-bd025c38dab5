import { Logger, logger } from '@shared';
import HttpClient from '@shared/utils/HttpClient';
import { createHash } from 'node:crypto';
import { readFile, writeFile, mkdir, access, unlink } from 'node:fs/promises';
import { join, dirname } from 'node:path';

import type { PSLManagerType } from '../interfaces/DomainNormalizer';

type PSLVersionType =
{
	version: string;
	downloadDate: Date;
	checksum: string;
	size: number;
	url: string;
};

type PSLDataType =
{
	rules: string[];
	exceptions: string[];
	wildcards: string[];
	lastModified: Date;
	version: string;
};

class PSLManagerImpl implements PSLManagerType
{
	private logger: ReturnType<Logger['getLogger']>;

	private client: HttpClient;

	private pslData: PSLDataType | null = null;

	private lastUpdate: Date | null = null;

	private updateInterval: number = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds

	private updateTimer: NodeJS.Timeout | null = null;

	private cacheDir: string;

	private currentVersion: PSLVersionType | null = null;

	private versionHistory: PSLVersionType[] = [];

	private readonly PSL_URL = 'https://publicsuffix.org/list/public_suffix_list.dat';

	private readonly PSL_CHECKSUM_URL = 'https://publicsuffix.org/list/public_suffix_list.dat.sha256';

	private readonly MAX_VERSION_HISTORY = 5;

	private readonly MIN_PSL_SIZE = 50000; // Minimum expected PSL size in bytes

	private readonly MAX_PSL_SIZE = 2000000; // Maximum expected PSL size in bytes

	constructor(cacheDir: string = './cache/psl', autoInit: boolean = true)
	{
		this.logger = logger.getLogger('PSLManager');
		this.client = new HttpClient(this.logger, { timeout: 30000 });
		this.cacheDir = cacheDir;

		if (autoInit)
		{
			// Don't initialize immediately in constructor to allow for testing
			setImmediate(() =>
			{
				this.initializeCache();
				this.scheduleUpdates();
			});
		}
	}

	async initialize(): Promise<void>
	{
		await this.initializeCache();
		this.scheduleUpdates();
	}

	private async initializeCache(): Promise<void>
	{
		try
		{
			await mkdir(this.cacheDir, { recursive: true });
			await this.loadVersionHistory();
			await this.loadLatestPSL();
		}
		catch (error)
		{
			this.logger.error('Failed to initialize PSL cache:', error);
		}
	}

	private async loadVersionHistory(): Promise<void>
	{
		try
		{
			const historyPath = join(this.cacheDir, 'version-history.json');
			const historyData = await readFile(historyPath, 'utf-8');
			this.versionHistory = JSON.parse(historyData).map((v: Omit<PSLVersionType, 'downloadDate'> & { downloadDate: string }) => ({
				...v,
				downloadDate: new Date(v.downloadDate),
			}));
		}
		catch
		{
			this.logger.info('No version history found, starting fresh');
			this.versionHistory = [];
		}
	}

	private async saveVersionHistory(): Promise<void>
	{
		try
		{
			const historyPath = join(this.cacheDir, 'version-history.json');
			await writeFile(historyPath, JSON.stringify(this.versionHistory, null, 2));
		}
		catch (error)
		{
			this.logger.error('Failed to save version history:', error);
		}
	}

	private async loadLatestPSL(): Promise<void>
	{
		if (this.versionHistory.length === 0)
		{
			this.logger.info('No cached PSL found, downloading fresh copy');
			await this.updatePSL();
			return;
		}

		// Load the latest version
		const latestVersion = this.versionHistory[0];
		const pslPath = join(this.cacheDir, `psl-${latestVersion.version}.dat`);

		try
		{
			const pslContent = await readFile(pslPath, 'utf-8');

			// Verify cached content integrity
			const actualChecksum = this.calculateChecksum(pslContent);
			if (actualChecksum !== latestVersion.checksum)
			{
				this.logger.error({
					msg: 'Cached PSL checksum mismatch, downloading fresh copy',
					expected: latestVersion.checksum,
					actual: actualChecksum,
					version: latestVersion.version,
				});
				await this.updatePSL();
				return;
			}

			// Verify content structure
			if (!this.verifyPSLContent(pslContent))
			{
				this.logger.error('Cached PSL content verification failed, downloading fresh copy');
				await this.updatePSL();
				return;
			}

			await this.parsePSLContent(pslContent);
			this.currentVersion = latestVersion;
			this.lastUpdate = latestVersion.downloadDate;
			this.logger.info({
				msg: 'Loaded cached PSL',
				version: latestVersion.version,
				checksumVerified: true,
			});
		}
		catch (error)
		{
			this.logger.error({ msg: 'Failed to load cached PSL, downloading fresh copy', error });
			await this.updatePSL();
		}
	}

	async updatePSL(): Promise<void>
	{
		try
		{
			this.logger.info({ msg: 'Downloading PSL from', url: this.PSL_URL });

			// Download PSL content and checksum in parallel
			const [pslResponse, checksumResponse] = await Promise.allSettled([
				this.client.get(this.PSL_URL, {
					headers: {
						'User-Agent': 'domain-seeder/1.0.0 (PSL auto-updater)',
					},
				}),
				this.client.get(this.PSL_CHECKSUM_URL, {
					headers: {
						'User-Agent': 'domain-seeder/1.0.0 (PSL auto-updater)',
					},
				}).catch(() => null), // Checksum is optional
			]);

			if (pslResponse.status !== 'fulfilled')
			{
				throw new Error(`Failed to download PSL: ${pslResponse.reason}`);
			}

			const pslContent = pslResponse.value.data as string;
			const expectedChecksum = checksumResponse.status === 'fulfilled' && checksumResponse.value
				? (checksumResponse.value.data as string).trim().split(' ')[0] // Extract checksum from "checksum filename" format
				: null;

			// Verify content integrity
			const actualChecksum = this.calculateChecksum(pslContent);

			// Verify against official checksum if available
			if (expectedChecksum && actualChecksum !== expectedChecksum)
			{
				throw new Error(`PSL checksum verification failed. Expected: ${expectedChecksum}, Got: ${actualChecksum}`);
			}

			// Check if this version already exists
			if (this.versionHistory.some(v => v.checksum === actualChecksum))
			{
				this.logger.info({ msg: 'PSL content unchanged, skipping update' });
				return;
			}

			// Verify content structure and integrity
			if (!this.verifyPSLContent(pslContent))
			{
				throw new Error('PSL content verification failed');
			}

			const version = this.generateVersion(actualChecksum);

			// Save new version
			const pslPath = join(this.cacheDir, `psl-${version}.dat`);
			await writeFile(pslPath, pslContent);

			// Parse and validate
			await this.parsePSLContent(pslContent);

			// Update version tracking
			const newVersion: PSLVersionType = {
				version,
				downloadDate: new Date(),
				checksum: actualChecksum,
				size: pslContent.length,
				url: this.PSL_URL,
			};

			this.versionHistory.unshift(newVersion);
			this.currentVersion = newVersion;
			this.lastUpdate = newVersion.downloadDate;

			// Cleanup old versions
			await this.cleanupOldVersions();

			// Save updated history
			await this.saveVersionHistory();

			this.logger.info({
				msg: 'PSL updated successfully',
				version,
				size: pslContent.length,
				rulesCount: this.pslData?.rules.length || 0,
				checksumVerified: !!expectedChecksum,
			});
		}
		catch (error)
		{
			this.logger.error({ msg: 'Failed to update PSL', error });
			throw error;
		}
	}

	private calculateChecksum(content: string): string
	{
		return createHash('sha256').update(content).digest('hex');
	}

	private generateVersion(checksum: string): string
	{
		const timestamp = new Date().toISOString().split('T')[0].replace(/-/g, '');
		return `${timestamp}-${checksum.substring(0, 8)}`;
	}

	private verifyPSLContent(content: string): boolean
	{
		try
		{
			// Basic size checks
			if (!content || content.length < this.MIN_PSL_SIZE || content.length > this.MAX_PSL_SIZE)
			{
				this.logger.error({
					msg: 'PSL content size verification failed',
					size: content?.length || 0,
					minExpected: this.MIN_PSL_SIZE,
					maxExpected: this.MAX_PSL_SIZE,
				});
				return false;
			}

			// Check for expected PSL structure markers
			const requiredMarkers = [
				'// ===BEGIN ICANN DOMAINS===',
				'// ===END ICANN DOMAINS===',
				'// ===BEGIN PRIVATE DOMAINS===',
				'// ===END PRIVATE DOMAINS===',
			];

			for (const marker of requiredMarkers)
			{
				if (!content.includes(marker))
				{
					this.logger.error({ msg: 'PSL content missing required marker', marker });
					return false;
				}
			}

			// Check for common TLDs that should always be present
			const requiredTlds = ['com', 'org', 'net', 'edu', 'gov', 'mil', 'int'];
			const missingTlds = requiredTlds.filter(tld => !content.includes(`\n${tld}\n`) && !content.includes(`\n${tld}\r\n`));

			if (missingTlds.length > 0)
			{
				this.logger.error({ msg: 'PSL content missing required TLDs', missingTlds });
				return false;
			}

			// Check for expected wildcard patterns
			const expectedWildcards = ['*.uk', '*.jp', '*.au'];
			const hasWildcards = expectedWildcards.some(wildcard => content.includes(wildcard));

			if (!hasWildcards)
			{
				this.logger.warn({ msg: 'PSL content missing expected wildcard patterns' });
			}

			// Check for expected exception patterns
			const expectedExceptions = ['!direct.uk', '!city.yokohama.jp'];
			const hasExceptions = expectedExceptions.some(exception => content.includes(exception));

			if (!hasExceptions)
			{
				this.logger.warn({ msg: 'PSL content missing expected exception patterns' });
			}

			// Verify content structure by parsing a sample
			const lines = content.split('\n').slice(0, 100); // Check first 100 lines
			let validLines = 0;

			for (const line of lines)
			{
				const trimmed = line.trim();
				if (!trimmed || trimmed.startsWith('//'))
				{
					validLines++;
					continue;
				}

				// Check if line looks like a valid PSL rule
				if (/^[!*]?[a-z0-9.-]+$/i.test(trimmed))
				{
					validLines++;
				}
			}

			if (validLines < lines.length * 0.8)
			{
				this.logger.error({
					msg: 'PSL content has too many invalid lines',
					validLines,
					totalLines: lines.length,
					validRatio: validLines / lines.length,
				});
				return false;
			}

			this.logger.info({
				msg: 'PSL content verification passed',
				size: content.length,
				hasAllMarkers: true,
				hasRequiredTlds: true,
			});

			return true;
		}
		catch (error)
		{
			this.logger.error({ msg: 'PSL content verification error', error });
			return false;
		}
	}

	private async parsePSLContent(content: string): Promise<void>
	{
		const rules: string[] = [];
		const exceptions: string[] = [];
		const wildcards: string[] = [];

		const lines = content.split('\n');
		let inIcannSection = false;
		let inPrivateSection = false;

		for (const line of lines)
		{
			const trimmed = line.trim();

			// Skip empty lines and comments
			if (!trimmed || trimmed.startsWith('//'))
			{
				// Track sections
				if (trimmed.includes('===BEGIN ICANN DOMAINS==='))
				{
					inIcannSection = true;
					inPrivateSection = false;
				}
				else if (trimmed.includes('===END ICANN DOMAINS==='))
				{
					inIcannSection = false;
				}
				else if (trimmed.includes('===BEGIN PRIVATE DOMAINS==='))
				{
					inPrivateSection = true;
					inIcannSection = false;
				}
				else if (trimmed.includes('===END PRIVATE DOMAINS==='))
				{
					inPrivateSection = false;
				}
				continue;
			}

			// Process rule
			if (trimmed.startsWith('!'))
			{
				// Exception rule
				exceptions.push(trimmed.substring(1).toLowerCase());
			}
			else if (trimmed.startsWith('*.'))
			{
				// Wildcard rule
				wildcards.push(trimmed.substring(2).toLowerCase());
			}
			else
			{
				// Regular rule
				rules.push(trimmed.toLowerCase());
			}
		}

		// Validate parsing results
		if (!this.validateParsedPSL(rules, exceptions, wildcards))
		{
			throw new Error('PSL parsing validation failed');
		}

		this.pslData = {
			rules,
			exceptions,
			wildcards,
			lastModified: new Date(),
			version: this.currentVersion?.version || 'unknown',
		};

		this.logger.info({
			msg: 'PSL parsed successfully',
			rules: rules.length,
			exceptions: exceptions.length,
			wildcards: wildcards.length,
		});
	}

	private validateParsedPSL(rules: string[], exceptions: string[], wildcards: string[]): boolean
	{
		try
		{
			// Check minimum counts
			const minRules = 1000; // Expect at least 1000 rules
			const minWildcards = 10; // Expect at least 10 wildcards

			if (rules.length < minRules)
			{
				this.logger.error({
					msg: 'PSL parsing validation failed: insufficient rules',
					rulesCount: rules.length,
					minExpected: minRules,
				});
				return false;
			}

			if (wildcards.length < minWildcards)
			{
				this.logger.error({
					msg: 'PSL parsing validation failed: insufficient wildcards',
					wildcardsCount: wildcards.length,
					minExpected: minWildcards,
				});
				return false;
			}

			// Check for required TLDs in rules
			const requiredTlds = ['com', 'org', 'net', 'edu', 'gov'];
			const missingTlds = requiredTlds.filter(tld => !rules.includes(tld));

			if (missingTlds.length > 0)
			{
				this.logger.error({
					msg: 'PSL parsing validation failed: missing required TLDs',
					missingTlds,
				});
				return false;
			}

			// Check for expected wildcards
			const expectedWildcards = ['uk', 'jp'];
			const hasExpectedWildcards = expectedWildcards.some(wildcard => wildcards.includes(wildcard));

			if (!hasExpectedWildcards)
			{
				this.logger.warn({
					msg: 'PSL parsing validation warning: missing expected wildcards',
					expectedWildcards,
				});
			}

			// Validate rule format
			const invalidRules = rules.filter(rule => !/^[a-z0-9.-]+$/.test(rule));
			if (invalidRules.length > 0)
			{
				this.logger.error({
					msg: 'PSL parsing validation failed: invalid rule format',
					invalidRules: invalidRules.slice(0, 10), // Show first 10
					totalInvalid: invalidRules.length,
				});
				return false;
			}

			// Validate exception format
			const invalidExceptions = exceptions.filter(exception => !/^[a-z0-9.-]+$/.test(exception));
			if (invalidExceptions.length > 0)
			{
				this.logger.error({
					msg: 'PSL parsing validation failed: invalid exception format',
					invalidExceptions: invalidExceptions.slice(0, 10),
					totalInvalid: invalidExceptions.length,
				});
				return false;
			}

			// Validate wildcard format
			const invalidWildcards = wildcards.filter(wildcard => !/^[a-z0-9.-]+$/.test(wildcard));
			if (invalidWildcards.length > 0)
			{
				this.logger.error({
					msg: 'PSL parsing validation failed: invalid wildcard format',
					invalidWildcards: invalidWildcards.slice(0, 10),
					totalInvalid: invalidWildcards.length,
				});
				return false;
			}

			this.logger.info('PSL parsing validation passed', {
				rulesCount: rules.length,
				exceptionsCount: exceptions.length,
				wildcardsCount: wildcards.length,
			});

			return true;
		}
		catch (error)
		{
			this.logger.error({ msg: 'PSL parsing validation error', error });
			return false;
		}
	}

	private async cleanupOldVersions(): Promise<void>
	{
		if (this.versionHistory.length <= this.MAX_VERSION_HISTORY)
		{
			return;
		}

		const versionsToRemove = this.versionHistory.slice(this.MAX_VERSION_HISTORY);

		for (const version of versionsToRemove)
		{
			try
			{
				const pslPath = join(this.cacheDir, `psl-${version.version}.dat`);
				await unlink(pslPath);
				this.logger.info({ msg: 'Removed old PSL version', version: version.version });
			}
			catch (error)
			{
				this.logger.warn({ msg: 'Failed to remove old PSL version', error });
			}
		}

		// Keep only the latest versions
		this.versionHistory = this.versionHistory.slice(0, this.MAX_VERSION_HISTORY);
	}

	private scheduleUpdates(): void
	{
		if (this.updateTimer)
		{
			clearInterval(this.updateTimer);
		}

		this.updateTimer = setInterval(async () =>
		{
			try
			{
				await this.updatePSL();
			}
			catch (error)
			{
				this.logger.error({ msg: 'Scheduled PSL update failed', error });
			}
		}, this.updateInterval);

		this.logger.info('PSL auto-update scheduled', {
			intervalDays: this.updateInterval / (24 * 60 * 60 * 1000),
		});
	}

	getLastUpdate(): Date | null
	{
		return this.lastUpdate;
	}

	isValidTLD(tld: string): boolean
	{
		if (!this.pslData)
		{
			return false;
		}

		const normalizedTld = tld.toLowerCase();
		return this.pslData.rules.includes(normalizedTld) ||
			this.pslData.wildcards.some(wildcard => normalizedTld.endsWith(wildcard));
	}

	getDomain(hostname: string): string | null
	{
		if (!this.pslData || !hostname)
		{
			return null;
		}

		const normalizedHostname = hostname.toLowerCase();
		const parts = normalizedHostname.split('.');

		if (parts.length < 2)
		{
			return null;
		}

		// Find the public suffix
		const publicSuffix = this.getPublicSuffix(normalizedHostname);
		if (!publicSuffix)
		{
			return null;
		}

		// Extract eTLD+1
		const suffixParts = publicSuffix.split('.');
		const domainParts = parts.slice(0, parts.length - suffixParts.length + 1);

		if (domainParts.length === 0)
		{
			return null;
		}

		return `${domainParts.join('.') }.${ publicSuffix}`;
	}

	getPublicSuffix(hostname: string): string | null
	{
		if (!this.pslData || !hostname)
		{
			return null;
		}

		const normalizedHostname = hostname.toLowerCase();
		const parts = normalizedHostname.split('.');

		let bestMatch = '';
		let bestMatchLength = 0;

		// Check exact matches
		for (let i = 0; i < parts.length; i++)
		{
			const suffix = parts.slice(i).join('.');

			if (this.pslData.rules.includes(suffix))
			{
				if (suffix.length > bestMatchLength)
				{
					bestMatch = suffix;
					bestMatchLength = suffix.length;
				}
			}
		}

		// Check wildcard matches
		for (const wildcard of this.pslData.wildcards)
		{
			for (let i = 0; i < parts.length - 1; i++)
			{
				const suffix = parts.slice(i + 1).join('.');
				if (suffix === wildcard)
				{
					const fullMatch = parts.slice(i).join('.');
					if (fullMatch.length > bestMatchLength)
					{
						bestMatch = fullMatch;
						bestMatchLength = fullMatch.length;
					}
				}
			}
		}

		// Check for exceptions
		for (const exception of this.pslData.exceptions)
		{
			if (normalizedHostname.endsWith(`.${ exception}`) || normalizedHostname === exception)
			{
				// Exception rules override wildcards
				const exceptionParts = exception.split('.');
				if (exceptionParts.length > 1)
				{
					const parentSuffix = exceptionParts.slice(1).join('.');
					if (parentSuffix.length < bestMatchLength)
					{
						bestMatch = parentSuffix;
						bestMatchLength = parentSuffix.length;
					}
				}
			}
		}

		return bestMatch || null;
	}

	async rollbackToVersion(version: string): Promise<void>
	{
		const targetVersion = this.versionHistory.find(v => v.version === version);
		if (!targetVersion)
		{
			throw new Error(`Version ${version} not found in history`);
		}

		const pslPath = join(this.cacheDir, `psl-${version}.dat`);

		try
		{
			const pslContent = await readFile(pslPath, 'utf-8');
			await this.parsePSLContent(pslContent);
			this.currentVersion = targetVersion;
			this.lastUpdate = targetVersion.downloadDate;

			this.logger.info({ msg: 'Rolled back to PSL version', version });
		}
		catch (error)
		{
			this.logger.error({ msg: 'Failed to rollback PSL version', error });
			throw error;
		}
	}

	getVersionHistory(): PSLVersionType[]
	{
		return [...this.versionHistory];
	}

	getCurrentVersion(): PSLVersionType | null
	{
		return this.currentVersion;
	}

	async healthCheck(): Promise<boolean>
	{
		try
		{
			if (!this.pslData)
			{
				return false;
			}

			// Check if PSL data is reasonably fresh (within 30 days)
			const thirtyDaysAgo = new Date();
			thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

			if (!this.lastUpdate || this.lastUpdate < thirtyDaysAgo)
			{
				this.logger.warn({ msg: 'PSL data is stale', lastUpdate: this.lastUpdate });
				return false;
			}

			// Basic functionality test
			const testDomain = this.getDomain('www.example.com');
			if (testDomain !== 'example.com')
			{
				this.logger.error({ msg: 'PSL functionality test failed' });
				return false;
			}

			return true;
		}
		catch (error)
		{
			this.logger.error({ msg: 'PSL health check failed', error });
			return false;
		}
	}

	getStatistics(): {
		rulesCount: number;
		exceptionsCount: number;
		wildcardsCount: number;
		lastUpdate: Date | null;
		currentVersion: string | null;
		cacheSize: number;
	}
	{
		return {
			rulesCount: this.pslData?.rules.length || 0,
			exceptionsCount: this.pslData?.exceptions.length || 0,
			wildcardsCount: this.pslData?.wildcards.length || 0,
			lastUpdate: this.lastUpdate,
			currentVersion: this.currentVersion?.version || null,
			cacheSize: this.versionHistory.length,
		};
	}

	async forceUpdate(): Promise<void>
	{
		this.logger.info({ msg: 'Forcing PSL update' });
		await this.updatePSL();
	}

	isUpdateDue(): boolean
	{
		if (!this.lastUpdate)
		{
			return true;
		}

		const updateDue = new Date();
		updateDue.setTime(this.lastUpdate.getTime() + this.updateInterval);

		return new Date() >= updateDue;
	}

	destroy(): void
	{
		if (this.updateTimer)
		{
			clearInterval(this.updateTimer);
			this.updateTimer = null;
		}
	}
}

export type { PSLVersionType, PSLDataType };

export default PSLManagerImpl;
