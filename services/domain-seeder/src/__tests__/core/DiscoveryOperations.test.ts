import { describe, it, expect, beforeEach, vi } from 'vitest';
import { DiscoveryOperations } from '../../core/DiscoveryOperations';
import type { DiscoveryStrategyType } from '../../interfaces/DiscoveryEngine';
import { mockDatabaseManager, mockLogger } from '../setup';

// Mock the dynamic imports
vi.mock('../../discovery/DiscoveryEngineFactory', () => ({
	DiscoveryEngineFactory: vi.fn().mockImplementation(() => ({
		createDiscoveryEngine: vi.fn().mockResolvedValue({
			processWithStrategy: vi.fn().mockImplementation(async function* () {
				yield { domain: 'test.com', confidence: 0.8 };
			}),
		}),
	})),
}));

vi.mock('../../enqueuer/RateLimitedDomainEnqueuer', () => ({
	default: vi.fn().mockImplementation(() => ({
		enqueueDomains: vi.fn().mockResolvedValue(1),
	})),
}));

describe('DiscoveryOperations', () => {
	let discoveryOps: DiscoveryOperations;

	beforeEach(() => {
		discoveryOps = new DiscoveryOperations(mockDatabaseManager);
	});

	describe('triggerDiscoveryRun', () => {
		it('should successfully trigger a discovery run with default parameters', async () => {
			const result = await discoveryOps.triggerDiscoveryRun();

			expect(result).toEqual({
				totalCandidates: 0,
				totalDiscovered: 1,
				totalEnqueued: 0,
				strategies: {
					'differential': {
						candidates: 0,
						discovered: 1,
						enqueued: 1,
					},
					'zone-new': {
						candidates: 0,
						discovered: 1,
						enqueued: 1,
					},
				},
			});
		});

		it('should handle custom parameters', async () => {
			const parameters = {
				strategies: ['differential'] as DiscoveryStrategyType[],
				sources: ['tranco'],
				limit: 5000,
				dryRun: true,
			};

			const result = await discoveryOps.triggerDiscoveryRun(parameters);

			expect(result.strategies).toHaveProperty('differential');
			expect(result.totalEnqueued).toBe(0); // dry run
		});

		it('should handle errors gracefully', async () => {
			vi.mocked(mockDatabaseManager.getRedisClient).mockImplementation(() => {
				throw new Error('Database connection failed');
			});

			await expect(discoveryOps.triggerDiscoveryRun()).rejects.toThrow('Database connection failed');
		});
	});

	describe('triggerTopSourcesRun', () => {
		it('should successfully analyze top sources', async () => {
			const result = await discoveryOps.triggerTopSourcesRun();

			expect(result).toHaveProperty('sources');
			expect(result).toHaveProperty('aggregated');
			expect(result.sources).toBeDefined();
		});

		it('should handle custom source parameters', async () => {
			const parameters = {
				sources: ['tranco'],
				limit: 1000,
				aggregate: false,
			};

			const result = await discoveryOps.triggerTopSourcesRun(parameters);

			expect(result.aggregated).toBeNull();
		});
	});

	describe('triggerBackfillRun', () => {
		it('should handle backfill run', async () => {
			const result = await discoveryOps.triggerBackfillRun();

			expect(result).toHaveProperty('totalCandidates');
			expect(result).toHaveProperty('totalDiscovered');
			expect(result).toHaveProperty('totalEnqueued');
		});

		it('should respect dry run parameter', async () => {
			const result = await discoveryOps.triggerBackfillRun({ dryRun: true });

			expect(result.totalEnqueued).toBe(0);
		});
	});
});