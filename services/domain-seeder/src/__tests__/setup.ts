import {
	beforeAll,
	afterAll,
	beforeEach,
	afterEach,
	vi,
} from 'vitest';
import { config } from 'dotenv';

// Load test environment variables
config({ path: '.env.test' });

// Mock shared services for testing
const mockScyllaClient = {
	healthCheck: vi.fn().mockResolvedValue(true),
	execute: vi.fn().mockResolvedValue({ rows: [] }),
};

const mockMariaClient = {
	healthCheck: vi.fn().mockResolvedValue(true),
	query: vi.fn().mockResolvedValue([]),
};

const mockManticoreClient = {
	healthCheck: vi.fn().mockResolvedValue(true),
	search: vi.fn().mockResolvedValue({ hits: [] }),
	insert: vi.fn().mockResolvedValue(undefined),
	bulkInsert: vi.fn().mockResolvedValue(undefined),
	delete: vi.fn().mockResolvedValue({ deleted: 0 }),
};

const mockRedisClient = {
	ping: vi.fn().mockResolvedValue('PONG'),
	get: vi.fn().mockResolvedValue(null),
	set: vi.fn().mockResolvedValue('OK'),
	setex: vi.fn().mockResolvedValue('OK'),
	setnx: vi.fn().mockResolvedValue(1),
	expire: vi.fn().mockResolvedValue(1),
	xadd: vi.fn().mockResolvedValue('**********-0'),
	xreadgroup: vi.fn().mockResolvedValue([]),
	xack: vi.fn().mockResolvedValue(1),
	llen: vi.fn().mockResolvedValue(0),
	info: vi.fn().mockResolvedValue('memory info'),
	keys: vi.fn().mockResolvedValue(['key1', 'key2']),
	exists: vi.fn().mockResolvedValue(1),
	pipeline: vi.fn().mockReturnValue({
		get: vi.fn(),
		exec: vi.fn().mockResolvedValue([]),
	}),
};

const mockLogger = {
	info: vi.fn(),
	warn: vi.fn(),
	error: vi.fn(),
	debug: vi.fn(),
	getLogger: vi.fn().mockReturnValue({
		info: vi.fn(),
		warn: vi.fn(),
		error: vi.fn(),
		debug: vi.fn(),
	}),
};

const mockDatabaseManager: any = {
	// Private properties that exist on the real DatabaseManager
	_scylla: mockScyllaClient,
	_maria: mockMariaClient,
	_redis: mockRedisClient,
	_manticore: mockManticoreClient,
	_ready: true,

	// Public methods
	initialize: vi.fn().mockResolvedValue(undefined),
	getScyllaClient: vi.fn().mockReturnValue(mockScyllaClient),
	getMariaClient: vi.fn().mockReturnValue(mockMariaClient),
	getManticoreClient: vi.fn().mockReturnValue(mockManticoreClient),
	getRedisClient: vi.fn().mockReturnValue(mockRedisClient),
	close: vi.fn().mockResolvedValue(undefined),
	healthCheck: vi.fn().mockResolvedValue(true),
	getInstance: vi.fn().mockReturnValue(() => mockDatabaseManager),
	getHealthStatus: vi.fn().mockResolvedValue({ overall: true }),
	scylla: mockScyllaClient,
	mariadb: mockMariaClient,
	redis: mockRedisClient,
	manticore: mockManticoreClient,
	logger: mockLogger,
};

const mockJobQueue = {
	enqueue: vi.fn().mockResolvedValue(undefined),
	enqueueBatch: vi.fn().mockResolvedValue(undefined),
	getQueueLength: vi.fn().mockResolvedValue(0),
};

const mockConfig = {
	getString: vi.fn().mockReturnValue('test-value'),
	getNumber: vi.fn().mockReturnValue(1000),
	getBoolean: vi.fn().mockReturnValue(true),
	getArray: vi.fn().mockReturnValue(['test']),
};

const mockMetricsCollector = {
	incrementCounter: vi.fn(),
	setGauge: vi.fn(),
	recordHistogram: vi.fn(),
};

// Global test setup
beforeAll(async () =>
{
	// Set test environment
	process.env.NODE_ENV = 'test';

	// Mock external dependencies
	vi.mock('base', () => ({
		DatabaseManager: vi.fn().mockImplementation(() => mockDatabaseManager),
		Logger: vi.fn().mockImplementation(() => mockLogger),
		Config: vi.fn().mockImplementation(() => mockConfig),
		JobQueue: vi.fn().mockImplementation(() => mockJobQueue),
		MetricsCollector: vi.fn().mockImplementation(() => mockMetricsCollector),
		HealthChecker: vi.fn().mockImplementation(() => ({})),
		CachingService: vi.fn().mockImplementation(() => ({
			initialize: vi.fn().mockResolvedValue(undefined),
		})),
		DataSyncService: vi.fn().mockImplementation(() => ({})),
	}));

	vi.mock('shared', () => ({
		DatabaseManager: vi.fn().mockImplementation(() => mockDatabaseManager),
		Logger: vi.fn().mockImplementation(() => mockLogger),
		Config: vi.fn().mockImplementation(() => mockConfig),
		JobQueue: vi.fn().mockImplementation(() => mockJobQueue),
		MetricsCollector: vi.fn().mockImplementation(() => mockMetricsCollector),
		HealthChecker: vi.fn().mockImplementation(() => ({})),
		CachingService: vi.fn().mockImplementation(() => ({
			initialize: vi.fn().mockResolvedValue(undefined),
		})),
		DataSyncService: vi.fn().mockImplementation(() => ({})),
	}));
});

beforeEach(() =>
{
	// Clear all mocks before each test
	vi.clearAllMocks();
});

afterEach(() =>
{
	// Clean up after each test
	vi.restoreAllMocks();
});

afterAll(async () =>
{
	// Global cleanup
});

// Export mocks for use in tests
export {
	mockDatabaseManager,
	mockLogger,
	mockJobQueue,
	mockConfig,
	mockScyllaClient,
	mockMariaClient,
	mockManticoreClient,
	mockRedisClient,
	mockMetricsCollector,
};
