import { describe, it, expect, beforeEach } from 'vitest';
import { MetricsCollector } from '../../monitoring/MetricsCollector';

describe('MetricsCollector', () => {
	let metricsCollector: MetricsCollector;

	beforeEach(() => {
		const mockRedis = {} as any; // Mock for testing
		metricsCollector = new MetricsCollector(mockRedis);
	});

	describe('domain seeder specific metrics', () => {
		it('should record discovery metrics', () => {
			metricsCollector.recordDiscovery('tranco', 150);
			metricsCollector.recordDiscovery('radar', 75);

			const metrics = metricsCollector.getSeederMetrics();

			expect(metrics.discoveries).toBeDefined();
		});

		it('should record content generation metrics', () => {
			metricsCollector.recordContentGeneration('example.com', true);
			metricsCollector.recordContentGeneration('failed.com', false);

			const metrics = metricsCollector.getSeederMetrics();

			expect(metrics.contentGeneration).toBeDefined();
		});

		it('should record validation metrics', () => {
			metricsCollector.recordValidation('valid.com', true);
			metricsCollector.recordValidation('invalid.com', false);

			const metrics = metricsCollector.getSeederMetrics();

			expect(metrics.validation).toBeDefined();
		});

		it('should record enqueuing metrics', () => {
			metricsCollector.recordEnqueuing('high-priority', 25);
			metricsCollector.recordEnqueuing('normal', 100);

			const metrics = metricsCollector.getSeederMetrics();

			expect(metrics.enqueued).toBeDefined();
		});

		it('should record provenance tracking', () => {
			metricsCollector.recordProvenance('example.com', ['tranco', 'radar']);

			const metrics = metricsCollector.getSeederMetrics();

			expect(metrics.provenance).toBeDefined();
		});
	});

	describe('metrics aggregation', () => {
		it('should aggregate multiple discovery records', () => {
			metricsCollector.recordDiscovery('tranco', 100);
			metricsCollector.recordDiscovery('tranco', 50);
			metricsCollector.recordDiscovery('radar', 75);

			const metrics = metricsCollector.getSeederMetrics();

			// Should have aggregated the tranco discoveries
			expect(metrics.discoveries).toBeDefined();
		});

		it('should provide comprehensive seeder metrics', () => {
			// Record various metrics
			metricsCollector.recordDiscovery('tranco', 100);
			metricsCollector.recordContentGeneration('test.com', true);
			metricsCollector.recordValidation('test.com', true);
			metricsCollector.recordEnqueuing('normal', 50);
			metricsCollector.recordProvenance('test.com', ['tranco']);

			const metrics = metricsCollector.getSeederMetrics();

			expect(metrics).toHaveProperty('discoveries');
			expect(metrics).toHaveProperty('contentGeneration');
			expect(metrics).toHaveProperty('validation');
			expect(metrics).toHaveProperty('enqueued');
			expect(metrics).toHaveProperty('provenance');
			expect(metrics).toHaveProperty('queueDepth');
			expect(metrics).toHaveProperty('dbCheckLatencyMs');
		});
	});

	describe('performance tracking', () => {
		it('should track queue depth', () => {
			const metrics = metricsCollector.getSeederMetrics();

			expect(typeof metrics.queueDepth).toBe('number');
		});

		it('should track database latency', () => {
			const metrics = metricsCollector.getSeederMetrics();

			expect(typeof metrics.dbCheckLatencyMs).toBe('number');
		});
	});
});