import { 
	BaseErro<PERSON><PERSON><PERSON><PERSON>,
	type LoggerInstanceType,
	type ErrorHandlingConfigType,
	type ErrorHandlingContextType,
	type ErrorClassificationResultType,
	IdGenerator
} from '@shared';

/**
 * Domain Seeder service error handler that extends the shared BaseErrorHandler
 * Adds seeder-specific error handling features like batch processing recovery
 */
export class SeederErrorHandler extends BaseErrorHandler 
{
	private failedDomainBatches: Map<string, { domains: string[]; attempts: number }> = new Map();
	
	constructor(logger: LoggerInstanceType, config?: Partial<ErrorHandlingConfigType>) 
	{
		super(logger, {
			classification: {
				enabled: true,
				confidenceThreshold: 0.8,
			},
			retry: {
				enabled: true,
				defaultStrategy: {
					maxAttempts: 5, // More retries for batch operations
					baseDelay: 2000,
					maxDelay: 60000,
					backoffMultiplier: 2.5,
					jitterEnabled: true,
				},
			},
			circuitBreaker: {
				enabled: true,
				defaultConfig: {
					failureThreshold: 10, // Higher threshold for batch operations
					timeout: 120000,
					volumeThreshold: 20,
				},
			},
			...config,
		});
	}

	protected async reportError(
		error: Error,
		classification: ErrorClassificationResultType,
		context: ErrorHandlingContextType
	): Promise<string | undefined> 
	{
		const errorId = IdGenerator.seederId();
		
		// Log error with seeder-specific context
		this.logger.error({
			msg: 'Seeder service error',
			errorId,
			error: error.message,
			classification,
			context,
			batchInfo: context.metadata?.batchInfo,
			domainCount: context.metadata?.domainCount,
		});

		// Track failed batches for recovery
		if (context.metadata?.batchId && context.metadata?.domains) 
		{
			const batch = this.failedDomainBatches.get(context.metadata.batchId) || {
				domains: context.metadata.domains,
				attempts: 0,
			};
			batch.attempts++;
			this.failedDomainBatches.set(context.metadata.batchId, batch);
		}

		return errorId;
	}

	protected async handleServiceSpecificError(
		error: Error,
		classification: ErrorClassificationResultType,
		context: ErrorHandlingContextType
	): Promise<{ actions: string[]; recoveryAttempted?: boolean; degradationTriggered?: boolean } | null> 
	{
		const actions: string[] = [];
		let recoveryAttempted = false;

		// Handle batch processing errors
		if (context.metadata?.isBatchError) 
		{
			actions.push('Batch error handled');
			
			// Split batch for retry if it's too large
			if (context.metadata?.domainCount > 100) 
			{
				context.metadata.shouldSplitBatch = true;
				actions.push('Batch marked for splitting');
			}
			
			// Attempt recovery for specific error types
			if (classification.classification.category === 'database' || 
				classification.classification.category === 'network') 
			{
				recoveryAttempted = await this.attemptBatchRecovery(context);
				if (recoveryAttempted) 
				{
					actions.push('Batch recovery attempted');
				}
			}
		}

		// Handle rate limiting errors
		if (error.message.includes('rate limit') || error.message.includes('429')) 
		{
			actions.push('Rate limit detected');
			
			// Implement adaptive rate limiting
			context.metadata.applyRateLimitBackoff = true;
			context.metadata.backoffDuration = this.calculateRateLimitBackoff(context);
			actions.push(`Rate limit backoff: ${context.metadata.backoffDuration}ms`);
		}

		// Handle source-specific errors
		if (context.metadata?.source) 
		{
			const sourceSpecificAction = this.handleSourceError(
				context.metadata.source,
				error,
				classification
			);
			if (sourceSpecificAction) 
			{
				actions.push(sourceSpecificAction);
			}
		}

		return {
			actions,
			recoveryAttempted,
			degradationTriggered: false,
		};
	}

	private async attemptBatchRecovery(context: ErrorHandlingContextType): Promise<boolean> 
	{
		if (!context.metadata?.batchId) return false;
		
		const batch = this.failedDomainBatches.get(context.metadata.batchId);
		if (!batch || batch.attempts > 3) 
		{
			// Too many attempts, give up on this batch
			this.failedDomainBatches.delete(context.metadata.batchId);
			return false;
		}

		// Store batch for later retry
		this.logger.info({
			msg: 'Batch queued for recovery',
			batchId: context.metadata.batchId,
			attempts: batch.attempts,
			domainCount: batch.domains.length,
		});

		return true;
	}

	private calculateRateLimitBackoff(context: ErrorHandlingContextType): number 
	{
		const baseBackoff = 5000;
		const attempts = context.metadata?.rateLimitAttempts || 0;
		return Math.min(baseBackoff * 2**attempts, 300000); // Max 5 minutes
	}

	private handleSourceError(
		source: string,
		error: Error,
		classification: ErrorClassificationResultType
	): string | null 
	{
		switch (source) 
		{
			case 'radar':
				if (error.message.includes('API key')) 
				{
					return 'Radar API key issue detected - switching to backup source';
				}
				break;
			case 'umbrella':
				if (classification.classification.category === 'network') 
				{
					return 'Umbrella connection issue - using cached data';
				}
				break;
			case 'psl':
				if (error.message.includes('parse')) 
				{
					return 'PSL parsing error - using previous version';
				}
				break;
		}
		return null;
	}

	protected onShutdown(): void 
	{
		// Save failed batches for recovery on restart
		if (this.failedDomainBatches.size > 0)
		{
			this.logger.info({
				msg: 'Saving failed batches for recovery',
				batchCount: this.failedDomainBatches.size,
			});
			// In a real implementation, save to persistent storage
		}
		
		this.logger.info({ msg: 'Seeder error handler shutdown completed' });
	}

	/**
	 * Get failed batches for recovery
	 */
	getFailedBatches(): Map<string, { domains: string[]; attempts: number }> 
	{
		return new Map(this.failedDomainBatches);
	}

	/**
	 * Clear recovered batch
	 */
	clearRecoveredBatch(batchId: string): void 
	{
		this.failedDomainBatches.delete(batchId);
	}

	/**
	 * Handle domain discovery errors with special logic
	 */
	async handleDiscoveryError(
		error: Error,
		source: string,
		domains: string[]
	): Promise<{ shouldRetry: boolean; alternativeSource?: string }> 
	{
		const context: ErrorHandlingContextType = {
			operationName: `discovery-${source}`,
			metadata: {
				source,
				domainCount: domains.length,
				domains: domains.slice(0, 10), // Only log first 10 for brevity
			},
		};

		const result = await this.handleError(error, context);
		
		// Determine if we should retry or switch sources
		const shouldRetry = result.classification.classification.retryable && 
						   result.classification.classification.persistence === 'transient';
		
		let alternativeSource: string | undefined;
		if (!shouldRetry && source === 'radar') 
		{
			alternativeSource = 'umbrella';
		}
		else if (!shouldRetry && source === 'umbrella') 
		{
			alternativeSource = 'psl';
		}

		return { shouldRetry, alternativeSource };
	}
}

export default SeederErrorHandler;
