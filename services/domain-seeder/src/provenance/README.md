# Provenance Tracking Module

This module provides comprehensive provenance tracking for domain discovery operations in the domain-seeder service. It implements lightweight metadata storage, strategy tracking, confidence scoring, and discovery statistics aggregation as required by the system specifications.

## Overview

The provenance tracking system maintains an audit trail of all domain discoveries, including:

- Discovery source and strategy information
- Confidence scores and discovery reasons
- Enqueue metadata and processing status
- Statistical aggregations for monitoring and analysis

## Components

### ProvenanceTracker

The main class that handles all provenance tracking operations.

#### Key Features

- **Dual Storage**: Uses both Redis (for fast access) and ScyllaDB (for persistence)
- **Batch Processing**: Efficiently handles large batches of domain discoveries
- **Statistics Aggregation**: Real-time calculation of discovery metrics
- **Query Interface**: Flexible querying with filters and pagination
- **Health Monitoring**: Built-in health checks and error handling
- **Cleanup Management**: Automatic cleanup of old records

#### Configuration

```typescript
const config: ProvenanceConfig = {
  ttlSeconds: 2592000, // 30 days TTL for Redis
  batchSize: 1000, // Batch size for operations
  enableScyllaStorage: true, // Enable ScyllaDB persistence
  enableStatisticsAggregation: true, // Enable real-time statistics
  statisticsWindowDays: 7, // Rolling window for statistics
};
```

## Usage

### Basic Usage

```typescript
import { ProvenanceTracker } from "./provenance/ProvenanceTracker";

const provenanceTracker = new ProvenanceTracker(
  redisClient,
  databaseManager,
  config
);

// Record a single discovery
await provenanceTracker.recordDiscovery(discoveredDomain);

// Record multiple discoveries in batch
await provenanceTracker.recordDiscoveryBatch(discoveredDomains);

// Update with enqueue information
await provenanceTracker.recordEnqueue(
  "example.com",
  "message-id-123",
  "high",
  true
);
```

### Querying Provenance Data

```typescript
// Get specific domain's provenance
const record = await provenanceTracker.getProvenanceRecord("example.com");

// Query with filters
const results = await provenanceTracker.queryProvenance({
  strategy: "differential",
  confidenceMin: 0.8,
  dateFrom: "2025-08-01T00:00:00Z",
  dateTo: "2025-08-06T23:59:59Z",
  limit: 100,
});
```

### Statistics and Monitoring

```typescript
// Get overall discovery statistics
const stats = await provenanceTracker.getDiscoveryStatistics(
  "2025-08-01T00:00:00Z",
  "2025-08-06T23:59:59Z"
);

// Get source-specific statistics
const sourceStats = await provenanceTracker.getSourceStatistics("tranco");

// Health monitoring
const health = await provenanceTracker.getHealthStatus();
```

## Data Models

### ProvenanceRecord

```typescript
interface ProvenanceRecord {
  domain: string;
  firstSeenAt: string;
  sources: string[];
  discoveryStrategy: DiscoveryStrategy;
  confidence: number;
  discoveryReason: string;
  preGeneratedContent?: boolean;
  metadata: {
    rank?: number;
    originalSource: string;
    enqueuedAt?: string;
    messageId?: string;
    priority?: "high" | "normal";
    [key: string]: any;
  };
}
```

### DiscoveryStatistics

```typescript
interface DiscoveryStatistics {
  totalDiscovered: number;
  bySource: Record<string, number>;
  byStrategy: Record<DiscoveryStrategy, number>;
  byConfidenceRange: {
    high: number; // 0.8+
    medium: number; // 0.6-0.8
    low: number; // <0.6
  };
  averageConfidence: number;
  timeRange: {
    start: string;
    end: string;
  };
}
```

## Database Schema

The provenance tracking system uses the following ScyllaDB tables:

### domain_discovery

```sql
CREATE TABLE domain_discovery (
    domain text PRIMARY KEY,
    first_seen_at timestamp,
    sources list<text>,
    discovery_strategy text,
    confidence decimal,
    discovery_reason text,
    pre_generated_content boolean,
    metadata text,
    last_updated timestamp
);
```

### discovery_statistics

```sql
CREATE TABLE discovery_statistics (
    date_bucket text,
    source text,
    strategy text,
    discovered_count counter,
    total_confidence decimal,
    PRIMARY KEY ((date_bucket), source, strategy)
);
```

## Integration with RateLimitedDomainEnqueuer

The provenance tracker integrates seamlessly with the domain enqueuer:

```typescript
const enqueuer = new RateLimitedDomainEnqueuer(
  redis,
  dbManager,
  tokenBucket,
  backpressureController,
  config,
  provenanceTracker // Optional provenance tracker
);
```

When a provenance tracker is provided, the enqueuer will:

- Automatically update provenance records when domains are enqueued
- Include message IDs and priority information
- Track enqueue timestamps and processing metadata

## Performance Considerations

### Batch Processing

The system is optimized for batch operations:

- Redis pipeline operations for bulk storage
- ScyllaDB batch operations for persistence
- Configurable batch sizes to balance memory and performance

### Caching Strategy

- Redis for fast access to recent records
- ScyllaDB for long-term persistence and complex queries
- In-memory caching for frequently accessed statistics

### Query Optimization

- Direct domain lookups use Redis first, then ScyllaDB
- Complex queries use ScyllaDB with proper indexing
- Statistics are cached with configurable TTL

## Error Handling

The provenance tracker implements comprehensive error handling:

- **Graceful Degradation**: Continues operation even if one storage backend fails
- **Retry Logic**: Automatic retries for transient failures
- **Error Logging**: Detailed error logging without blocking operations
- **Health Monitoring**: Built-in health checks for all dependencies

## Monitoring and Alerting

### Health Checks

```typescript
const health = await provenanceTracker.getHealthStatus();
// Returns: isHealthy, redisConnected, scyllaConnected, recordCount, etc.
```

### Metrics

The system exposes metrics for:

- Discovery rates by source and strategy
- Confidence score distributions
- Storage backend health
- Query performance
- Error rates

### Cleanup Operations

```typescript
// Clean up records older than 30 days
const cleanedCount = await provenanceTracker.cleanup(30);
```

## Testing

The module includes comprehensive tests:

- **Unit Tests**: Core functionality and edge cases
- **Performance Tests**: Batch processing and query performance
- **Integration Tests**: End-to-end workflows with other components
- **Property Tests**: Data integrity and consistency validation

Run tests with:

```bash
pnpm test ProvenanceTracker
```

## Requirements Compliance

This implementation satisfies the following requirements:

- **5.4**: Lightweight provenance data storage with discovery metadata
- **6.1**: Strategy tracking and confidence scoring in provenance data
- **6.2**: Discovery statistics aggregation by source and strategy
- **Performance**: Efficient batch processing and query operations
- **Data Integrity**: Comprehensive validation and error handling
- **Monitoring**: Health checks and operational metrics
