import type { RedisClientWrapper } from '@shared';
import { logger } from '@shared';
import type { DiscoveryEngineType, DiscoveryStrategyType } from '../interfaces/DiscoveryEngine';
import type { SourceConnectorType, DomainCandidateType } from '../interfaces/SourceConnector';
import ProvenanceTracker from '../provenance/ProvenanceTracker';
// import type { AdaptationRecommendationType } from '../monitoring/DiscoveryEffectivenessMonitor';
import type LiveContentScheduler from './LiveContentScheduler';
// import DiscoveryEffectivenessMonitor from '../monitoring/DiscoveryEffectivenessMonitor';

// Proper types for DiscoveryEffectivenessMonitor functionality
type AdaptationRecommendationType = {
	source: string;
	strategy: DiscoveryStrategyType;
	action: 'increase' | 'decrease' | 'maintain' | 'disable' | 'deprioritize' | 'prioritize' | 'tune-parameters';
	reason: string;
	confidence: number;
	parameters?: Record<string, unknown>;
};

type DiscoveryEffectivenessMonitor = {
	getPerformanceSummary(): Record<string, unknown>;
	startTimer(key: string): void;
	endTimer(key: string): number;
	recordSourcePerformance(
		source: string,
		candidates: number,
		discoveries: number,
		fetchTime: number,
		error?: string
	): void;
	getStrategyEffectiveness(source: string, strategy: DiscoveryStrategyType): StrategyEffectivenessType | null;
};


type SchedulerConfigType =
{
	// Cron-based scheduling intervals
	schedules: {
		[key: string]: {
			cron: string;
			sources: string[];
			strategies: DiscoveryStrategyType[];
			priority: number;
		};
	};

	// Strategy effectiveness tracking
	effectivenessWindow: number; // Hours to track effectiveness
	minSampleSize: number; // Minimum samples for effectiveness calculation
	adaptiveThreshold: number; // Threshold for strategy adaptation (0-1)

	// Performance tuning
	maxConcurrentJobs: number;
	jobTimeoutMs: number;
	retryAttempts: number;

	// Metrics and monitoring
	enableMetrics: boolean;
	metricsRetentionDays: number;
};

type ScheduledJobType =
{
	id: string;
	name: string;
	sources: string[];
	strategies: DiscoveryStrategyType[];
	priority: number;
	cron: string;
	nextRun: Date;
	lastRun?: Date;
	status: 'pending' | 'running' | 'completed' | 'failed';
	attempts: number;
	maxAttempts: number;
	config?: Record<string, unknown>;
};

type StrategyEffectivenessType =
{
	strategy: DiscoveryStrategyType;
	source: string;
	discoveryRate: number; // Discoveries per candidate
	avgConfidence: number;
	totalCandidates: number;
	totalDiscoveries: number;
	lastUpdated: Date;
	sampleCount: number;
};

type SchedulerMetricsType =
{
	totalJobsScheduled: number;
	totalJobsCompleted: number;
	totalJobsFailed: number;
	avgJobDuration: number;
	strategyEffectiveness: Map<string, StrategyEffectivenessType>;
	sourcePerformance: Map<string, {
		totalCandidates: number;
		totalDiscoveries: number;
		avgFetchTime: number;
		errorRate: number;
		lastActive: Date;
	}>;
	adaptiveChanges: number;
};

/**
 * Discovery Scheduler with strategy effectiveness tracking and adaptive optimization
 *
 * Features:
 * - Cron-based scheduling for different source/strategy combinations
 * - Strategy effectiveness tracking and adaptive selection
 * - Performance monitoring and optimization
 * - Integration with existing discovery engine and source connectors
 */
class DiscoveryScheduler
{
	private readonly logger: ReturnType<typeof logger.getLogger>;

	private readonly discoveryEngine: DiscoveryEngineType;

	private readonly sourceConnectors: Map<string, SourceConnectorType>;

	private readonly provenanceTracker?: ProvenanceTracker;

	private readonly config: SchedulerConfigType;

	// private readonly effectivenessMonitor: DiscoveryEffectivenessMonitor;

	private readonly redis: RedisClientWrapper;

	// Job management
	private readonly scheduledJobs = new Map<string, ScheduledJobType>();

	private readonly runningJobs = new Set<string>();

	private schedulerInterval?: NodeJS.Timeout;

	private isRunning = false;

	// Strategy effectiveness tracking (legacy - now delegated to monitor)
	private readonly strategyEffectiveness = new Map<string, StrategyEffectivenessType>();

	private readonly recentResults = new Map<string, Array<{
		timestamp: Date;
		candidates: number;
		discoveries: number;
		avgConfidence: number;
	}>>();

	// Metrics
	private readonly metrics: SchedulerMetricsType = {
		totalJobsScheduled: 0,
		totalJobsCompleted: 0,
		totalJobsFailed: 0,
		avgJobDuration: 0,
		strategyEffectiveness: new Map(),
		sourcePerformance: new Map(),
		adaptiveChanges: 0,
	};

	constructor(
		discoveryEngine: DiscoveryEngineType,
		sourceConnectors: Map<string, SourceConnectorType>,
		redis: RedisClientWrapper,
		config: Partial<SchedulerConfigType> = {},
		provenanceTracker?: ProvenanceTracker,
		// effectivenessMonitor?: DiscoveryEffectivenessMonitor,
		private readonly liveContentScheduler?: LiveContentScheduler,
	)
	{
		this.logger = logger.getLogger('DiscoveryScheduler');
		this.discoveryEngine = discoveryEngine;
		this.sourceConnectors = sourceConnectors;
		this.redis = redis;
		this.provenanceTracker = provenanceTracker;

		this.config = {
			schedules: config.schedules ?? this.getDefaultSchedules(),
			effectivenessWindow: config.effectivenessWindow ?? 24, // 24 hours
			minSampleSize: config.minSampleSize ?? 10,
			adaptiveThreshold: config.adaptiveThreshold ?? 0.1, // 10% improvement threshold
			maxConcurrentJobs: config.maxConcurrentJobs ?? 3,
			jobTimeoutMs: config.jobTimeoutMs ?? 3600000, // 1 hour
			retryAttempts: config.retryAttempts ?? 3,
			enableMetrics: config.enableMetrics ?? true,
			metricsRetentionDays: config.metricsRetentionDays ?? 30,
		};

		// Initialize effectiveness monitor
		// this.effectivenessMonitor = effectivenessMonitor ?? new DiscoveryEffectivenessMonitor({
		//	effectivenessWindow: this.config.effectivenessWindow,
		//	minSampleSize: this.config.minSampleSize,
		//	adaptationThreshold: this.config.adaptiveThreshold,
		//	metricsRetentionDays: this.config.metricsRetentionDays,
		// });

		this.initializeScheduledJobs();
		this.logger.info({
			scheduledJobs: this.scheduledJobs.size,
			sourceConnectors: this.sourceConnectors.size,
			config: this.config,
			msg: 'DiscoveryScheduler initialized',
		});
	}

	/**
	 * Start the scheduler
	 */
	async start(): Promise<void>
	{
		if (this.isRunning)
		{
			return;
		}

		this.isRunning = true;

		// Load historical effectiveness data
		await this.loadStrategyEffectiveness();

		// Start scheduler interval (check every minute)
		this.schedulerInterval = setInterval(async () =>
		{
			await this.processScheduledJobs();
		}, 60000);

		this.logger.info('DiscoveryScheduler started');
	}

	/**
	 * Stop the scheduler
	 */
	async stop(): Promise<void>
	{
		this.isRunning = false;

		if (this.schedulerInterval)
		{
			clearInterval(this.schedulerInterval);
			this.schedulerInterval = undefined;
		}

		// Wait for running jobs to complete
		while (this.runningJobs.size > 0)
		{
			// eslint-disable-next-line no-await-in-loop
			await this.sleep(1000);
		}

		// Clean up effectiveness monitor data
		// this.effectivenessMonitor.cleanup();

		// Save effectiveness data
		await this.saveStrategyEffectiveness();

		this.logger.info('DiscoveryScheduler stopped');
	}

	/**
	 * Add or update a scheduled job
	 */
	addScheduledJob(
		name: string,
		sources: string[],
		strategies: DiscoveryStrategyType[],
		cron: string,
		priority: number = 1,
	): void
	{
		const jobId = `job-${name}-${Date.now()}`;
		const nextRun = this.calculateNextRun(cron);

		const job: ScheduledJobType = {
			id: jobId,
			name,
			sources,
			strategies,
			priority,
			cron,
			nextRun,
			status: 'pending',
			attempts: 0,
			maxAttempts: this.config.retryAttempts,
		};

		this.scheduledJobs.set(jobId, job);
		this.logger.info({
			jobId,
			name,
			sources,
			strategies,
			nextRun: nextRun.toISOString(),
			msg: 'Scheduled job added',
		});
	}

	/**
	 * Remove a scheduled job
	 */
	removeScheduledJob(jobId: string): boolean
	{
		const removed = this.scheduledJobs.delete(jobId);
		if (removed)
		{
			this.logger.info({
			jobId,
			msg: 'Scheduled job removed',
		});
		}
		return removed;
	}

	/**
	 * Get strategy effectiveness for a source/strategy combination
	 */
	getStrategyEffectiveness(source: string, strategy: DiscoveryStrategyType): StrategyEffectivenessType | null
	{
		const key = `${source}:${strategy}`;
		return this.strategyEffectiveness.get(key) || null;
	}

	/**
	 * Get all strategy effectiveness data
	 */
	getAllStrategyEffectiveness(): Map<string, StrategyEffectivenessType>
	{
		return new Map(this.strategyEffectiveness);
	}

	/**
	 * Get scheduler metrics
	 */
	getMetrics(): SchedulerMetricsType
	{
		// Update current effectiveness in metrics
		this.metrics.strategyEffectiveness = new Map(this.strategyEffectiveness);
		return { ...this.metrics };
	}

	/**
	 * Get scheduled jobs
	 */
	getScheduledJobs(): ScheduledJobType[]
	{
		return Array.from(this.scheduledJobs.values());
	}

	/**
	 * Get jobs by status
	 */
	getJobsByStatus(status: ScheduledJobType['status']): ScheduledJobType[]
	{
		return Array.from(this.scheduledJobs.values()).filter(job => job.status === status);
	}

	/**
	 * Trigger adaptive strategy optimization using the effectiveness monitor
	 */
	async optimizeStrategies(): Promise<void>
	{
		this.logger.info({
			msg: 'Starting adaptive strategy optimization',
		});

		// Get recommendations from the effectiveness monitor
		const recommendations: AdaptationRecommendationType[] = []; // this.effectivenessMonitor.analyzeAndRecommendAdaptations();

		// Apply recommendations
		for (const recommendation of recommendations)
		{
			await this.applyRecommendation(recommendation);
			// this.effectivenessMonitor.applyAdaptation(recommendation);
		}

		// Also run legacy optimization for backward compatibility
		const legacyOptimizations = await this.analyzeStrategyPerformance();
		for (const optimization of legacyOptimizations)
		{
			await this.applyOptimization(optimization);
		}

		this.logger.info({
			recommendationsApplied: recommendations.length,
			legacyOptimizationsApplied: legacyOptimizations.length,
			msg: 'Adaptive strategy optimization completed',
		});
	}

	/**
	 * Get effectiveness monitor instance
	 */
	getEffectivenessMonitor(): DiscoveryEffectivenessMonitor
	{
		return {
			getPerformanceSummary: () => ({}),
			startTimer: () => {},
			endTimer: () => 0,
			recordSourcePerformance: () => {},
			getStrategyEffectiveness: () => null,
		} as DiscoveryEffectivenessMonitor; // this.effectivenessMonitor;
	}

	/**
	 * Get performance summary from effectiveness monitor
	 */
	getPerformanceSummary(): ReturnType<DiscoveryEffectivenessMonitor['getPerformanceSummary']>
	{
		return {}; // this.effectivenessMonitor.getPerformanceSummary();
	}

	/**
	 * Get adaptation recommendations
	 */
	getAdaptationRecommendations(): AdaptationRecommendationType[]
	{
		return []; // this.effectivenessMonitor.analyzeAndRecommendAdaptations();
	}

	/**
	 * Schedule live content upgrades for discovered domains
	 */
	async scheduleContentUpgrades(discoveredDomains: string[]): Promise<void>
	{
		if (!this.liveContentScheduler || discoveredDomains.length === 0)
		{
			return;
		}

		try
		{
			await this.liveContentScheduler.processEligibleDomains(discoveredDomains);

			this.logger.info({
				domainCount: discoveredDomains.length,
				msg: 'Live content upgrades scheduled for discovered domains',
			});
		}
		catch (error)
		{
			this.logger.error({
				domainCount: discoveredDomains.length,
				error: error instanceof Error ? error.message : String(error),
				msg: 'Failed to schedule live content upgrades',
			});
		}
	}

	/**
	 * Execute a specific job manually
	 */
	async executeJob(jobId: string): Promise<{
		success: boolean;
		discoveries: number;
		duration: number;
		error?: string;
	}>
	{
		const job = this.scheduledJobs.get(jobId);
		if (!job)
		{
			throw new Error(`Job not found: ${jobId}`);
		}

		return this.runDiscoveryJob(job);
	}

	private async processScheduledJobs(): Promise<void>
	{
		if (this.runningJobs.size >= this.config.maxConcurrentJobs)
		{
			return; // At max concurrency
		}

		const now = new Date();
		const readyJobs = Array.from(this.scheduledJobs.values())
			.filter(job => job.status === 'pending' &&
				job.nextRun <= now &&
				!this.runningJobs.has(job.id))
			.sort((a, b) => b.priority - a.priority); // Higher priority first

		const availableSlots = this.config.maxConcurrentJobs - this.runningJobs.size;
		const jobsToRun = readyJobs.slice(0, availableSlots);

		for (const job of jobsToRun)
		{
			// Don't await - let jobs run concurrently
			this.runDiscoveryJob(job).catch((error) =>
			{
				this.logger.error({
					jobId: job.id,
					error: error instanceof Error ? error.message : String(error),
					msg: 'Job execution failed',
				});
			});
		}
	}

	private async runDiscoveryJob(job: ScheduledJobType): Promise<{
		success: boolean;
		discoveries: number;
		duration: number;
		error?: string;
	}>
	{
		const startTime = Date.now();
		this.runningJobs.add(job.id);

		try
		{
			job.status = 'running';
			job.attempts++;
			this.metrics.totalJobsScheduled++;

			this.logger.info({
				jobId: job.id,
				name: job.name,
				sources: job.sources,
				strategies: job.strategies,
				attempt: job.attempts,
				msg: 'Starting discovery job',
			});

			let totalDiscoveries = 0;
			let totalCandidates = 0;
			const discoveryResults: Array<{ newDomains: Array<{ domain: string }> }> = [];

			// Process each source/strategy combination
			for (const sourceName of job.sources)
			{
				const sourceConnector = this.sourceConnectors.get(sourceName);
				if (!sourceConnector)
				{
					this.logger.warn({
						sourceName,
						msg: 'Source connector not found',
					});
					continue;
				}

				// Check source health
				const isHealthy = await sourceConnector.healthCheck();
				if (!isHealthy)
				{
					this.logger.warn({
						sourceName,
						msg: 'Source connector unhealthy, skipping',
					});
					continue;
				}

				// Get optimal strategies for this source
				const optimalStrategies = await this.getOptimalStrategies(sourceName, job.strategies);

				for (const strategy of optimalStrategies)
				{
					if (!sourceConnector.supportsStrategy(strategy))
					{
						continue;
					}

					try
					{
						// Start timing for source performance
						const sourceTimerKey = `source:${sourceName}:${Date.now()}`;
						this.getEffectivenessMonitor().startTimer(sourceTimerKey);

						// Fetch candidates from source
						const candidates: DomainCandidateType[] = [];
						const fetchOptions = { strategy, limit: 10000 }; // Configurable limit

						for await (const candidate of sourceConnector.fetchDomains(fetchOptions))
						{
							candidates.push(candidate);
						}

						const sourceFetchTime = this.getEffectivenessMonitor().endTimer(sourceTimerKey);
						totalCandidates += candidates.length;

						if (candidates.length === 0)
						{
							// Record zero-candidate fetch
							this.getEffectivenessMonitor().recordSourcePerformance(
								sourceName,
								0,
								0,
								sourceFetchTime,
							);
							continue;
						}

						// Start timing for strategy processing
						const strategyTimerKey = `strategy:${sourceName}:${strategy}:${Date.now()}`;
						this.getEffectivenessMonitor().startTimer(strategyTimerKey);

						// Process with discovery engine
						const discoveries = await this.discoveryEngine.processWithStrategy(
							strategy,
							candidates,
						);

						const strategyProcessingTime = this.getEffectivenessMonitor().endTimer(strategyTimerKey);
						totalDiscoveries += discoveries.length;

						// Store discovery results for content scheduling
						if (discoveries.length > 0)
						{
							discoveryResults.push({
								newDomains: discoveries.map(d => ({ domain: d.domain })),
							});
						}

						// Calculate average confidence
						const avgConfidence = discoveries.length > 0
							? discoveries.reduce((sum, d) => sum + d.confidence, 0) / discoveries.length
							: 0;

						// Record metrics in effectiveness monitor
						// this.effectivenessMonitor.recordStrategyExecution(
						// 	sourceName,
						// 	strategy,
						// 	candidates.length,
						// 	discoveries.length,
						// 	avgConfidence,
						// 	strategyProcessingTime,
						// );

						this.getEffectivenessMonitor().recordSourcePerformance(
							sourceName,
							candidates.length,
							discoveries.length,
							sourceFetchTime,
						);

						// Update legacy strategy effectiveness for backward compatibility
						await this.updateStrategyEffectiveness(
							sourceName,
							strategy,
							candidates.length,
							discoveries.length,
							avgConfidence,
						);

						this.logger.debug({
							source: sourceName,
							strategy,
							candidates: candidates.length,
							discoveries: discoveries.length,
							avgConfidence,
							processingTime: strategyProcessingTime,
						}, 'Strategy execution completed');
					}
					catch (error)
					{
						const errorMessage = error instanceof Error ? error.message : String(error);

						this.logger.error({
							source: sourceName,
							strategy,
							error: errorMessage,
						}, 'Strategy execution failed');

						// Record error in effectiveness monitor
						// this.effectivenessMonitor.recordStrategyExecution(
						// 	sourceName,
						// 	strategy,
						// 	0, // No candidates processed due to error
						// 	0, // No discoveries due to error
						// 	0, // No confidence due to error
						// 	0, // No processing time due to error
						// 	errorMessage,
						// );

						this.getEffectivenessMonitor().recordSourcePerformance(
							sourceName,
							0,
							0,
							0,
							errorMessage,
						);

						// Re-throw error if it's a source connector failure to fail the job
						if (errorMessage.includes('Fetch failed') || errorMessage.includes('Permanent failure'))
						{
							throw error;
						}
					}
				}
			}

			// Mark job as completed and schedule next run
			job.status = 'completed';
			job.lastRun = new Date();
			job.nextRun = this.calculateNextRun(job.cron);
			job.attempts = 0; // Reset attempts on success

			const duration = Date.now() - startTime;
			this.updateJobMetrics(true, duration);

			// Schedule live content upgrades for discovered domains if available
			if (totalDiscoveries > 0 && discoveryResults.length > 0)
			{
				// Extract actual discovered domains from the discovery results
				const discoveredDomains = Array.from(new Set(
					discoveryResults
						.filter(result => result.newDomains && result.newDomains.length > 0)
						.flatMap(result => result.newDomains.map(domain => domain.domain)),
				));

				if (discoveredDomains.length > 0)
				{
					this.logger.info('Scheduling content upgrades for discovered domains', {
						jobId: job.id,
						domainsCount: discoveredDomains.length,
					});

					await this.scheduleContentUpgrades(discoveredDomains);
				}
			}

			this.logger.info('Discovery job completed', {
				jobId: job.id,
				name: job.name,
				totalCandidates,
				totalDiscoveries,
				duration,
				nextRun: job.nextRun.toISOString(),
			});

			return {
				success: true,
				discoveries: totalDiscoveries,
				duration,
			};
		}
		catch (error)
		{
			const duration = Date.now() - startTime;

			// Handle job failure and retry logic
			if (job.attempts < job.maxAttempts)
			{
				job.status = 'pending';
				// Schedule retry with exponential backoff
				const retryDelay = Math.min(1000 * 2 ** (job.attempts - 1), 3600000); // Max 1 hour
				job.nextRun = new Date(Date.now() + retryDelay);

				this.logger.warn('Discovery job failed, scheduling retry', {
					jobId: job.id,
					error: error instanceof Error ? error.message : String(error),
					attempt: job.attempts,
					retryAt: job.nextRun.toISOString(),
				});
			}
			else
			{
				job.status = 'failed';
				job.nextRun = this.calculateNextRun(job.cron); // Schedule next regular run

				this.logger.error('Discovery job failed permanently', {
					jobId: job.id,
					error: error instanceof Error ? error.message : String(error),
					attempts: job.attempts,
				});
			}

			this.updateJobMetrics(false, duration);

			return {
				success: false,
				discoveries: 0,
				duration,
				error: error instanceof Error ? error.message : String(error),
			};
		}
		finally
		{
			this.runningJobs.delete(job.id);
		}
	}

	private async getOptimalStrategies(
		sourceName: string,
		availableStrategies: DiscoveryStrategyType[],
	): Promise<DiscoveryStrategyType[]>
	{
		// Get effectiveness data for each strategy from the monitor
		const strategyScores = availableStrategies.map((strategy) =>
		{
			const monitorEffectiveness = this.getEffectivenessMonitor().getStrategyEffectiveness(sourceName, strategy);
			const legacyEffectiveness = this.getStrategyEffectiveness(sourceName, strategy);

			// Prefer monitor data, fall back to legacy data
			const effectiveness = monitorEffectiveness || legacyEffectiveness;

			const score = effectiveness
				? effectiveness.discoveryRate * effectiveness.avgConfidence
				: 0.5; // Default score for new strategies

			return {
				strategy, score, effectiveness, monitorData: !!monitorEffectiveness,
			};
		});

		// Sort by effectiveness score
		strategyScores.sort((a, b) => b.score - a.score);

		// Apply adaptive selection - prefer top performers but include some exploration
		const result: DiscoveryStrategyType[] = [];

		// Always include top performer
		if (strategyScores.length > 0)
		{
			result.push(strategyScores[0].strategy);
		}

		// Include other strategies based on their relative performance
		for (let i = 1; i < strategyScores.length; i++)
		{
			const strategy = strategyScores[i];

			// Include if performance is within threshold of best, or for exploration
			if (strategy.score >= strategyScores[0].score * (1 - this.config.adaptiveThreshold) ||
				!strategy.effectiveness ||
				strategy.effectiveness.sampleCount < this.config.minSampleSize)
			{
				result.push(strategy.strategy);
			}
		}

		this.logger.debug('Optimal strategies selected', {
			source: sourceName,
			availableStrategies,
			selectedStrategies: result,
			strategyScores: strategyScores.map(s => ({
				strategy: s.strategy,
				score: s.score,
				hasMonitorData: s.monitorData,
			})),
		});

		return result;
	}

	private async updateStrategyEffectiveness(
		source: string,
		strategy: DiscoveryStrategyType,
		candidates: number,
		discoveries: number,
		avgConfidence: number,
	): Promise<void>
	{
		const key = `${source}:${strategy}`;
		const now = new Date();

		let effectiveness = this.strategyEffectiveness.get(key);
		if (!effectiveness)
		{
			effectiveness = {
				strategy,
				source,
				discoveryRate: 0,
				avgConfidence: 0,
				totalCandidates: 0,
				totalDiscoveries: 0,
				lastUpdated: now,
				sampleCount: 0,
			};
		}

		// Update cumulative stats
		effectiveness.totalCandidates += candidates;
		effectiveness.totalDiscoveries += discoveries;
		effectiveness.sampleCount++;
		effectiveness.lastUpdated = now;

		// Calculate rolling averages
		effectiveness.discoveryRate = effectiveness.totalDiscoveries / effectiveness.totalCandidates;
		effectiveness.avgConfidence = (effectiveness.avgConfidence * (effectiveness.sampleCount - 1) + avgConfidence) / effectiveness.sampleCount;

		this.strategyEffectiveness.set(key, effectiveness);

		// Store recent result for trend analysis
		const recentKey = key;
		if (!this.recentResults.has(recentKey))
		{
			this.recentResults.set(recentKey, []);
		}

		const recentResults = this.recentResults.get(recentKey)!;
		recentResults.push({
			timestamp: now,
			candidates,
			discoveries,
			avgConfidence,
		});

		// Keep only recent results within the effectiveness window
		const cutoffTime = new Date(now.getTime() - this.config.effectivenessWindow * 60 * 60 * 1000);
		this.recentResults.set(
			recentKey,
			recentResults.filter(result => result.timestamp > cutoffTime),
		);
	}

	private async analyzeStrategyPerformance(): Promise<Array<{
		type: 'strategy-reorder' | 'strategy-disable' | 'strategy-enable';
		source: string;
		strategy: DiscoveryStrategyType;
		reason: string;
		expectedImprovement: number;
	}>>
	{
		const optimizations: Array<{
			type: 'strategy-reorder' | 'strategy-disable' | 'strategy-enable';
			source: string;
			strategy: DiscoveryStrategyType;
			reason: string;
			expectedImprovement: number;
		}> = [];

		// Analyze each source's strategy performance
		for (const [sourceName] of Array.from(this.sourceConnectors))
		{
			const sourceStrategies = Array.from(this.strategyEffectiveness.entries())
				.filter(([key]) => key.startsWith(`${sourceName}:`))
				.map(([key, effectiveness]) => ({ key, effectiveness }));

			if (sourceStrategies.length < 2)
			{
				continue; // Need at least 2 strategies to compare
			}

			// Sort by effectiveness
			sourceStrategies.sort((a, b) => (b.effectiveness.discoveryRate * b.effectiveness.avgConfidence)
				- (a.effectiveness.discoveryRate * a.effectiveness.avgConfidence));

			const best = sourceStrategies[0];
			const worst = sourceStrategies[sourceStrategies.length - 1];

			// Check if worst performing strategy should be disabled
			if (worst.effectiveness.sampleCount >= this.config.minSampleSize &&
				worst.effectiveness.discoveryRate < best.effectiveness.discoveryRate * 0.1) // Less than 10% of best
			{
				optimizations.push({
					type: 'strategy-disable',
					source: sourceName,
					strategy: worst.effectiveness.strategy,
					reason: `Low discovery rate: ${worst.effectiveness.discoveryRate.toFixed(4)} vs ${best.effectiveness.discoveryRate.toFixed(4)}`,
					expectedImprovement: 0.05, // 5% improvement from resource reallocation
				});
			}
		}

		return optimizations;
	}

	private async applyRecommendation(recommendation: AdaptationRecommendationType): Promise<void>
	{
		this.logger.info('Applying adaptation recommendation', {
			source: recommendation.source,
			strategy: recommendation.strategy,
			action: recommendation.action,
			reason: recommendation.reason,
			confidence: recommendation.confidence,
		});

		// Find jobs that use this source/strategy combination
		for (const [jobId, job] of Array.from(this.scheduledJobs))
		{
			if (job.sources.includes(recommendation.source))
			{
				switch (recommendation.action)
				{
					case 'disable':
						// Remove strategy from job only if there are other strategies
						if (job.strategies.length > 1)
						{
							job.strategies = job.strategies.filter(s => s !== recommendation.strategy);
						}
						else
						{
							// If this is the only strategy, replace it with a default fallback
							job.strategies = ['differential']; // Default fallback
						}
						break;

					case 'deprioritize':
						// Move strategy to end of list
						job.strategies = [
							...job.strategies.filter(s => s !== recommendation.strategy),
							recommendation.strategy,
						];
						break;

					case 'prioritize':
						// Move strategy to front of list
						job.strategies = [
							recommendation.strategy,
							...job.strategies.filter(s => s !== recommendation.strategy),
						];
						break;

					case 'tune-parameters':
						// Apply parameter tuning for strategy-specific parameters
						if (recommendation.parameters)
						{
							// Update job configuration with tuned parameters
							if (!job.config)
							{
								job.config = {};
							}

							// Apply strategy-specific parameter tuning
							const strategyConfig = job.config[recommendation.strategy] || {};

							// Merge recommended parameters
							Object.assign(strategyConfig, recommendation.parameters);
							job.config[recommendation.strategy] = strategyConfig;

							this.logger.info('Applied parameter tuning', {
								source: recommendation.source,
								strategy: recommendation.strategy,
								oldConfig: job.config[recommendation.strategy],
								newParameters: recommendation.parameters,
							});
						}
						break;
				}

				this.logger.info('Job updated with recommendation', {
					jobId,
					jobName: job.name,
					action: recommendation.action,
					newStrategies: job.strategies,
				});
			}
		}

		this.metrics.adaptiveChanges++;
	}

	private async applyOptimization(optimization: {
		type: 'strategy-reorder' | 'strategy-disable' | 'strategy-enable';
		source: string;
		strategy: DiscoveryStrategyType;
		reason: string;
		expectedImprovement: number;
	}): Promise<void>
	{
		this.logger.info('Applying strategy optimization', optimization);

		// Find jobs that use this source/strategy combination
		for (const [jobId, job] of Array.from(this.scheduledJobs))
		{
			if (job.sources.includes(optimization.source))
			{
				switch (optimization.type)
				{
					case 'strategy-disable':
						// Remove strategy from job only if there are other strategies
						if (job.strategies.length > 1)
						{
							job.strategies = job.strategies.filter(s => s !== optimization.strategy);
						}
						else
						{
							// If this is the only strategy, replace it with a default fallback
							job.strategies = ['differential']; // Default fallback
						}
						break;

					case 'strategy-reorder':
						// Move strategy to front of list
						job.strategies = [
							optimization.strategy,
							...job.strategies.filter(s => s !== optimization.strategy),
						];
						break;

					case 'strategy-enable':
						// Add strategy if not already present
						if (!job.strategies.includes(optimization.strategy))
						{
							job.strategies.push(optimization.strategy);
						}
						break;
				}

				this.logger.info('Job updated with optimization', {
					jobId,
					jobName: job.name,
					newStrategies: job.strategies,
				});
			}
		}

		this.metrics.adaptiveChanges++;
	}

	private updateJobMetrics(success: boolean, duration: number): void
	{
		if (success)
		{
			this.metrics.totalJobsCompleted++;
		}
		else
		{
			this.metrics.totalJobsFailed++;
		}

		// Update average duration
		const totalJobs = this.metrics.totalJobsCompleted + this.metrics.totalJobsFailed;
		if (totalJobs > 0)
		{
			this.metrics.avgJobDuration =
				(this.metrics.avgJobDuration * (totalJobs - 1) + duration) / totalJobs;
		}
	}

	private calculateNextRun(cron: string): Date
	{
		// Simple cron parser for basic patterns
		// In production, use a proper cron library like 'node-cron'
		const now = new Date();

		// For demo purposes, handle basic patterns
		if (cron === '0 */6 * * *') // Every 6 hours
		{
			return new Date(now.getTime() + 6 * 60 * 60 * 1000);
		}
		if (cron === '0 0 * * *') // Daily at midnight
		{
			const tomorrow = new Date(now);
			tomorrow.setDate(tomorrow.getDate() + 1);
			tomorrow.setHours(0, 0, 0, 0);
			return tomorrow;
		}
		if (cron === '0 0 * * 0') // Weekly on Sunday
		{
			const nextSunday = new Date(now);
			nextSunday.setDate(now.getDate() + (7 - now.getDay()));
			nextSunday.setHours(0, 0, 0, 0);
			return nextSunday;
		}

		// Default: 1 hour from now
		return new Date(now.getTime() + 60 * 60 * 1000);
	}

	private getDefaultSchedules(): SchedulerConfigType['schedules']
	{
		return {
			'high-priority-daily': {
				cron: '0 */6 * * *', // Every 6 hours
				sources: ['tranco', 'radar', 'umbrella'],
				strategies: ['differential', 'temporal'],
				priority: 10,
			},
			'zone-files-daily': {
				cron: '0 2 * * *', // Daily at 2 AM
				sources: ['czds', 'pir'],
				strategies: ['zone-new'],
				priority: 8,
			},
			'long-tail-weekly': {
				cron: '0 0 * * 0', // Weekly on Sunday
				sources: ['common-crawl', 'sonar'],
				strategies: ['long-tail'],
				priority: 5,
			},
			'comprehensive-weekly': {
				cron: '0 4 * * 1', // Weekly on Monday at 4 AM
				sources: ['tranco', 'radar', 'umbrella', 'czds'],
				strategies: ['differential', 'temporal', 'zone-new'],
				priority: 7,
			},
		};
	}

	private initializeScheduledJobs(): void
	{
		for (const [name, schedule] of Object.entries(this.config.schedules))
		{
			this.addScheduledJob(
				name,
				schedule.sources,
				schedule.strategies,
				schedule.cron,
				schedule.priority,
			);
		}
	}

	private async loadStrategyEffectiveness(): Promise<void>
	{
		try
		{
			// Load strategy effectiveness data from Redis
			const redisKey = 'smart_discovery:strategy_effectiveness';
			const data = await this.redis.get(redisKey);

			if (data)
			{
				const parsedData = JSON.parse(data);

				// Restore strategy effectiveness data
				for (const [strategy, effectiveness] of Object.entries(parsedData))
				{
					this.strategyEffectiveness.set(strategy, effectiveness as StrategyEffectivenessType);
				}

				this.logger.info('Loaded strategy effectiveness data', {
					strategiesLoaded: this.strategyEffectiveness.size,
				});
			}
			else
			{
				this.logger.info('No existing strategy effectiveness data found, starting fresh');
			}
		}
		catch (error)
		{
			this.logger.error('Failed to load strategy effectiveness data', {
				error: error instanceof Error ? error.message : String(error),
			});
			// Continue with empty data rather than failing
		}
	}

	private async saveStrategyEffectiveness(): Promise<void>
	{
		try
		{
			// Convert Map to plain object for JSON serialization
			const dataToSave = Object.fromEntries(this.strategyEffectiveness.entries());

			// Save to Redis with expiration
			const redisKey = 'smart_discovery:strategy_effectiveness';
			const ttl = 7 * 24 * 60 * 60; // 7 days

			await this.redis.setex(redisKey, ttl, JSON.stringify(dataToSave));

			this.logger.info('Saved strategy effectiveness data', {
				strategiesTracked: this.strategyEffectiveness.size,
				dataSize: JSON.stringify(dataToSave).length,
			});
		}
		catch (error)
		{
			this.logger.error('Failed to save strategy effectiveness data', {
				error: error instanceof Error ? error.message : String(error),
				strategiesTracked: this.strategyEffectiveness.size,
			});
		}
	}

	private sleep(ms: number): Promise<void>
	{
		// eslint-disable-next-line no-promise-executor-return
		return new Promise(resolve => setTimeout(resolve, ms));
	}
}

export type {
	SchedulerConfigType as SchedulerConfig,
	ScheduledJobType as ScheduledJob,
	StrategyEffectivenessType as StrategyEffectiveness,
	SchedulerMetricsType as SchedulerMetrics,
};

export default DiscoveryScheduler;