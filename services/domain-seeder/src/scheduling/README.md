# Smart Discovery Scheduler

## Overview

The SmartDiscoveryScheduler is an adaptive scheduling system for the domain-seeder service that implements intelligent strategy effectiveness tracking and optimization. It provides cron-based scheduling for different source/strategy combinations with automatic adaptation based on discovery performance.

## Key Features

### 1. Strategy Effectiveness Tracking

- Tracks discovery rate (discoveries per candidate) for each source/strategy combination
- Monitors average confidence scores for discovered domains
- Maintains rolling averages and sample counts for statistical significance
- Stores recent results for trend analysis within configurable time windows

### 2. Adaptive Strategy Selection

- Automatically selects optimal strategies based on historical performance
- Includes exploration for strategies with insufficient data
- Applies adaptive thresholds to balance exploitation vs exploration
- Prioritizes high-performing strategies while maintaining diversity

### 3. Cron-based Scheduling

- Supports flexible cron expressions for different scheduling patterns
- Default schedules for high-priority daily, zone files, long-tail weekly, and comprehensive weekly runs
- Configurable priority levels for job execution order
- Automatic next-run calculation with retry logic

### 4. Performance Optimization

- Identifies underperforming strategies for potential disabling
- Maintains at least one strategy per job to ensure continued operation
- Tracks source performance metrics including error rates and response times
- Implements circuit breaker patterns for failed sources

### 5. Comprehensive Monitoring

- Prometheus-compatible metrics for job execution and strategy performance
- Health checks for scheduler status and running jobs
- Detailed logging with structured data for operational visibility
- Configurable metrics retention and cleanup

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Cron Jobs     │    │  Strategy        │    │  Source         │
│                 │    │  Effectiveness   │    │  Connectors     │
│ - Daily         │    │  Tracking        │    │                 │
│ - Weekly        │    │                  │    │ - Tranco        │
│ - Monthly       │    │ - Discovery Rate │    │ - Radar         │
│                 │    │ - Confidence     │    │ - CZDS          │
└─────────────────┘    │ - Sample Size    │    │ - Common Crawl  │
         │              └──────────────────┘    └─────────────────┘
         │                       │                       │
         v                       v                       v
┌─────────────────────────────────────────────────────────────────┐
│                Smart Discovery Scheduler                        │
│                                                                 │
│ ┌─────────────┐  ┌─────────────┐  ┌─────────────┐              │
│ │   Job       │  │  Strategy   │  │  Metrics    │              │
│ │ Management  │  │ Optimizer   │  │ Collector   │              │
│ └─────────────┘  └─────────────┘  └─────────────┘              │
└─────────────────────────────────────────────────────────────────┘
         │                       │                       │
         v                       v                       v
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  Discovery      │    │  Domain          │    │  Provenance     │
│  Engine         │    │  Candidates      │    │  Tracking       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## Configuration

### Scheduler Configuration

```typescript
interface SchedulerConfig {
  schedules: {
    [key: string]: {
      cron: string;
      sources: string[];
      strategies: DiscoveryStrategy[];
      priority: number;
    };
  };
  effectivenessWindow: number; // Hours to track effectiveness
  minSampleSize: number; // Minimum samples for effectiveness calculation
  adaptiveThreshold: number; // Threshold for strategy adaptation (0-1)
  maxConcurrentJobs: number;
  jobTimeoutMs: number;
  retryAttempts: number;
  enableMetrics: boolean;
  metricsRetentionDays: number;
}
```

### Default Schedules

- **High Priority Daily**: Every 6 hours for Tranco/Radar/Umbrella with differential and temporal strategies
- **Zone Files Daily**: Daily at 2 AM for CZDS/PIR with zone-new strategy
- **Long-tail Weekly**: Weekly on Sunday for Common Crawl/Sonar with long-tail strategy
- **Comprehensive Weekly**: Weekly on Monday at 4 AM for multiple sources and strategies

## Usage

### Basic Usage

```typescript
import SmartDiscoveryScheduler from "./scheduling/SmartDiscoveryScheduler";

const scheduler = new SmartDiscoveryScheduler(
  discoveryEngine,
  sourceConnectors,
  {
    effectivenessWindow: 24, // 24 hours
    minSampleSize: 10,
    adaptiveThreshold: 0.1, // 10% improvement threshold
  }
);

await scheduler.start();
```

### Adding Custom Jobs

```typescript
scheduler.addScheduledJob(
  "custom-job",
  ["tranco", "radar"],
  ["differential", "temporal"],
  "0 */4 * * *", // Every 4 hours
  8 // Priority
);
```

### Manual Job Execution

```typescript
const result = await scheduler.executeJob(jobId);
console.log(
  `Discoveries: ${result.discoveries}, Duration: ${result.duration}ms`
);
```

### Strategy Optimization

```typescript
// Trigger manual optimization
await scheduler.optimizeStrategies();

// Check strategy effectiveness
const effectiveness = scheduler.getStrategyEffectiveness(
  "tranco",
  "differential"
);
console.log(`Discovery rate: ${effectiveness.discoveryRate}`);
```

## Metrics

The scheduler exposes comprehensive metrics for monitoring:

- `totalJobsScheduled`: Total number of jobs scheduled
- `totalJobsCompleted`: Total number of jobs completed successfully
- `totalJobsFailed`: Total number of jobs that failed permanently
- `avgJobDuration`: Average job execution time in milliseconds
- `strategyEffectiveness`: Map of strategy effectiveness data by source/strategy
- `sourcePerformance`: Map of source performance metrics
- `adaptiveChanges`: Number of adaptive optimizations applied

## Error Handling

The scheduler implements robust error handling:

- **Source Failures**: Individual source failures don't fail the entire job
- **Strategy Failures**: Failed strategies are logged but don't stop other strategies
- **Retry Logic**: Failed jobs are retried with exponential backoff
- **Circuit Breakers**: Unhealthy sources are skipped automatically
- **Graceful Degradation**: System continues operating with reduced functionality

## Testing

The implementation includes comprehensive tests covering:

- Strategy effectiveness calculation and tracking
- Adaptive strategy selection and optimization
- Job execution with various failure scenarios
- Metrics tracking and reporting
- Cron scheduling and timing
- Error handling and recovery

Run tests with:

```bash
pnpm test SmartDiscoveryScheduler
```

## Integration

The scheduler integrates with existing domain-seeder components:

- **Discovery Engine**: Uses the IntelligentDiscoveryEngine for processing candidates
- **Source Connectors**: Works with all existing source connectors (Tranco, Radar, etc.)
- **Provenance Tracker**: Optional integration for tracking discovery metadata
- **Metrics System**: Compatible with existing Prometheus metrics collection

## Performance Considerations

- **Memory Usage**: Strategy effectiveness data is kept in memory with configurable retention
- **Concurrency**: Configurable maximum concurrent jobs to prevent resource exhaustion
- **Batch Processing**: Processes candidates in batches for optimal performance
- **Cleanup**: Automatic cleanup of old jobs and metrics data

## Future Enhancements

Potential improvements for future versions:

1. **Machine Learning**: Use ML models for strategy effectiveness prediction
2. **Dynamic Scheduling**: Adjust cron schedules based on source data freshness
3. **Resource Optimization**: Automatic resource allocation based on job requirements
4. **Advanced Analytics**: Trend analysis and anomaly detection for strategy performance
5. **Multi-tenant Support**: Support for multiple discovery pipelines with isolation
