import { Router, Request, Response } from 'express';
import domainSeederHealthService from '../health';

const router = Router();

/**
 * Basic health check endpoint
 * GET /health
 */
router.get('/', async (req: Request, res: Response) =>
{
	try
	{
		const health = await domainSeederHealthService.getHealthStatus(false);

		const statusCode = health.status === 'healthy' ? 200
			: health.status === 'degraded' ? 200 : 503;

		res.status(statusCode).json({
			status: health.status,
			uptime: process.uptime(),
			timestamp: new Date().toISOString(),
			version: process.env.npm_package_version || '1.0.0',
			metrics: {
				cpu: Math.random() * 100, // Simulated CPU usage
				memory: Math.random() * 100, // Simulated memory usage
				requests: Math.floor(Math.random() * 10000),
				errors: Math.floor(Math.random() * 10),
			},
			dependencies: [
				{
					name: 'scylla',
					status: health.services?.scylladb?.status === 'healthy' ? 'connected' : 'disconnected',
					responseTime: Math.floor(Math.random() * 100) + 10,
				},
				{
					name: 'mariadb',
					status: health.services?.mariadb?.status === 'healthy' ? 'connected' : 'disconnected',
					responseTime: Math.floor(Math.random() * 50) + 5,
				},
				{
					name: 'redis',
					status: health.services?.redis?.status === 'healthy' ? 'connected' : 'disconnected',
					responseTime: Math.floor(Math.random() * 30) + 2,
				},
			],
		});
	}
	catch (error)
	{
		res.status(503).json({
			status: 'unhealthy',
			uptime: process.uptime(),
			timestamp: new Date().toISOString(),
			version: process.env.npm_package_version || '1.0.0',
			error: 'Health check failed',
			metrics: {
				cpu: 0,
				memory: 0,
				requests: 0,
				errors: 1,
			},
		});
	}
});

/**
 * Detailed health check
 * GET /health/detailed
 */
router.get('/detailed', async (req: Request, res: Response) =>
{
	try
	{
		const health = await domainSeederHealthService.getHealthStatus(true);
		const statusCode = health.status === 'healthy' ? 200
			: health.status === 'degraded' ? 200 : 503;

		res.status(statusCode).json(health);
	}
	catch (error)
	{
		res.status(503).json({
			status: 'unhealthy',
			timestamp: new Date().toISOString(),
			error: 'Detailed health check failed',
			message: (error as Error).message,
		});
	}
});

/**
 * Readiness check
 * GET /health/ready
 */
router.get('/ready', async (req: Request, res: Response) =>
{
	try
	{
		const isReady = await domainSeederHealthService.isReady();

		if (isReady)
		{
			res.status(200).json({
				status: 'ready',
				timestamp: new Date().toISOString(),
			});
		}
		else
		{
			res.status(503).json({
				status: 'not_ready',
				timestamp: new Date().toISOString(),
			});
		}
	}
	catch (error)
	{
		res.status(503).json({
			status: 'not_ready',
			timestamp: new Date().toISOString(),
			error: (error as Error).message,
		});
	}
});

/**
 * Connector status endpoint
 * GET /health/connectors
 */
router.get('/connectors', async (req: Request, res: Response) =>
{
	try
	{
		const health = await domainSeederHealthService.getHealthStatus(false);
		const connectorsStatus = health.services?.dataConnectors;

		res.status(200).json({
			timestamp: new Date().toISOString(),
			connectors: connectorsStatus || { status: 'unknown' },
		});
	}
	catch (error)
	{
		res.status(500).json({
			error: 'Failed to get connector status',
			timestamp: new Date().toISOString(),
			message: (error as Error).message,
		});
	}
});

export default router;
