import type DatabaseManager from '@shared/database/DatabaseManager';
import { logger } from '@shared';
import { CompositeDomainRepository } from '../repositories';

// Define missing CacheWarmingSource interface for this example
interface CacheWarmingSource {
	name: string;
	getDomains(offset: number, limit: number): Promise<string[]>;
	getTotalCount(): Promise<number>;
}

/**
 * Example demonstrating Redis cache warming functionality
 * This shows how to warm the cache with domains from multiple sources
 */

/**
 * Example cache warming source that simulates a domain list
 */
class ExampleDomainSource implements CacheWarmingSource
{
	name = 'example-domains';

	private domains = [
		'example.com',
		'test.com',
		'demo.com',
		'sample.org',
		'placeholder.net',
		'mock.io',
		'fake.co',
		'dummy.info',
		'temp.biz',
		'trial.me',
	];

	async getDomains(offset: number, limit: number): Promise<string[]>
	{
		const end = Math.min(offset + limit, this.domains.length);
		return this.domains.slice(offset, end);
	}

	async getTotalCount(): Promise<number>
	{
		return this.domains.length;
	}
}

/**
 * Example cache warming source that simulates popular domains
 */
class PopularDomainsSource implements CacheWarmingSource
{
	name = 'popular-domains';

	private popularDomains = [
		'google.com',
		'facebook.com',
		'youtube.com',
		'amazon.com',
		'wikipedia.org',
		'twitter.com',
		'instagram.com',
		'linkedin.com',
		'reddit.com',
		'netflix.com',
	];

	async getDomains(offset: number, limit: number): Promise<string[]>
	{
		const end = Math.min(offset + limit, this.popularDomains.length);
		return this.popularDomains.slice(offset, end);
	}

	async getTotalCount(): Promise<number>
	{
		return this.popularDomains.length;
	}
}

/**
 * Example function demonstrating cache warming
 */
export async function demonstrateCacheWarming(dbManager: DatabaseManager): Promise<void>
{
	const exampleLogger = logger.getLogger('cache-warming-example');
	exampleLogger.info('Starting Redis Cache Warming Example');

	// Create composite repository with cache enabled
	const repository = new CompositeDomainRepository(dbManager, {
		enableRedisCache: true,
		redisCacheConfig: {
			keyPrefix: 'example:domain:',
			positiveTtl: 3600, // 1 hour for example
			negativeTtl: 1800, // 30 minutes for example
			enableWarmup: true,
			enableMetrics: true,
			maxWarmupDomains: 50,
			warmupBatchSize: 5,
		},
	});

	try
	{
		// Get initial metrics
		const initialMetrics = repository.getMetrics();
		exampleLogger.info({
			cacheHits: initialMetrics.cacheMetrics.totalHits,
			cacheMisses: initialMetrics.cacheMetrics.totalMisses,
			hitRate: initialMetrics.cacheMetrics.hitRate
		}, 'Initial cache metrics');

		// Create warming sources
		const sources: CacheWarmingSource[] = [
			new ExampleDomainSource(),
			new PopularDomainsSource(),
		];

		exampleLogger.info('Starting cache warmup');
		const warmupStart = Date.now();

		// Warm up the cache
		await repository.warmupCache(sources);

		const warmupDuration = Date.now() - warmupStart;
		exampleLogger.info({ warmupDuration }, 'Cache warmup completed');

		// Test cache performance after warmup
		const testDomains = [
			'example.com', // Should be cached
			'google.com', // Should be cached
			'unknown-domain.com', // Should not be cached
		];

		const testStart = Date.now();
		const results = await repository.batchHasDomains(testDomains);
		const testDuration = Date.now() - testStart;

		const resultMap = Object.fromEntries(results);
		exampleLogger.info({ testDuration, results: resultMap }, 'Batch check completed');

		// Get final metrics
		const finalMetrics = repository.getMetrics();
		exampleLogger.info({
			cacheHits: finalMetrics.cacheMetrics.totalHits,
			cacheMisses: finalMetrics.cacheMetrics.totalMisses,
			hitRate: finalMetrics.cacheMetrics.hitRate,
			averageResponseTime: finalMetrics.cacheMetrics.averageResponseTime,
			estimatedSize: finalMetrics.cacheMetrics.estimatedSize
		}, 'Final cache metrics');

		// Demonstrate cache invalidation
		exampleLogger.info('Starting cache invalidation');
		await repository.invalidateCache([
			{
				name: 'example-invalidation',
				pattern: 'example:domain:example*',
				deleteKeys: true,
			},
		]);

		exampleLogger.info('Cache invalidation completed');

		// Test performance after invalidation
		const postInvalidationResult = await repository.hasDomain('example.com');

		// Final metrics after invalidation
		const postInvalidationMetrics = repository.getMetrics();
		exampleLogger.info({
			exampleComExists: postInvalidationResult,
			totalInvalidations: postInvalidationMetrics.cacheMetrics.totalInvalidations
		}, 'Metrics after invalidation');
	}
	catch (error)
	{
		exampleLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Cache warming example failed');
	}
	finally
	{
		// Clean up
		repository.destroy();
		exampleLogger.info('Cleanup completed');
	}
}

/**
 * Example function demonstrating cache monitoring
 */
export async function demonstrateCacheMonitoring(dbManager: DatabaseManager): Promise<void>
{
	const monitorLogger = logger.getLogger('cache-monitoring-example');
	monitorLogger.info('Starting Cache Monitoring Example');

	const repository = new CompositeDomainRepository(dbManager, {
		enableRedisCache: true,
		redisCacheConfig: {
			keyPrefix: 'monitor:domain:',
			enableMetrics: true,
			metricsInterval: 1000, // 1 second for demo
		},
	});

	try
	{
		// Simulate various cache operations
		const testDomains = [
			'monitor-test-1.com',
			'monitor-test-2.com',
			'monitor-test-3.com',
		];

		monitorLogger.info('Performing cache operations');

		// Perform multiple operations to generate metrics
		for (let i = 0; i < 5; i++)
		{
			await repository.batchHasDomains(testDomains);
			await new Promise(resolve => setTimeout(resolve, 200)); // Small delay
		}

		// Monitor metrics over time
		monitorLogger.info('Monitoring cache metrics');
		for (let i = 0; i < 3; i++)
		{
			const metrics = repository.getMetrics();
			monitorLogger.info({
				snapshot: i + 1,
				totalOperations: metrics.cacheMetrics.totalGets,
				hitRate: metrics.cacheMetrics.hitRate,
				averageResponseTime: metrics.cacheMetrics.averageResponseTime,
				errorCount: metrics.cacheMetrics.errorCount
			}, 'Cache metrics snapshot');

			if (i < 2)
			{
				// Perform more operations
				await repository.batchHasDomains([`dynamic-${i}.com`]);
				await new Promise(resolve => setTimeout(resolve, 1000));
			}
		}

		monitorLogger.info('Cache monitoring completed');
	}
	catch (error)
	{
		monitorLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Cache monitoring example failed');
	}
	finally
	{
		repository.destroy();
		monitorLogger.info('Monitoring cleanup completed');
	}
}

// Export for use in other examples or tests
export { ExampleDomainSource, PopularDomainsSource };
