import type { DomainCandidateType } from './SourceConnector';

type DiscoveryStrategyType =
	| 'differential'
	| 'zone-new'
	| 'long-tail'
	| 'temporal'
;

interface DiscoveredDomainInterface extends DomainCandidateType
{
	discoveryStrategy: DiscoveryStrategyType;
	confidence: number;
	discoveryReason: string;
}

type DomainSnapshotType =
{
	source: string;
	date: string;
	domains: DomainCandidateType[];
	totalCount: number;
};

type StrategyProcessorType =
{
	process(
		candidates: DomainCandidateType[],
		snapshotStore: SnapshotStoreType,
	): Promise<DiscoveredDomainInterface[]>;
};

type SnapshotStoreType =
{
	getSnapshot(source: string, date: string): Promise<DomainSnapshotType | null>;
	storeSnapshot(source: string, candidates: DomainCandidateType[]): Promise<void>;
	getRecentSnapshots(source: string, days: number): Promise<DomainSnapshotType[]>;
	cleanup(retentionDays: number): Promise<void>;
};

type DiscoveryEngineType =
{
	processWithStrategy(
		strategy: DiscoveryStrategyType,
		candidates: DomainCandidateType[],
	): Promise<DiscoveredDomainInterface[]>;

	getHistoricalSnapshot(
		source: string,
		date: string,
	): Promise<DomainSnapshotType | null>;

	storeSnapshot(source: string, domains: DomainCandidateType[]): Promise<void>;
};

type DiscoveryMetricsType =
{
	totalCandidates: number;
	discoveredDomains: number;
	strategyBreakdown: Record<DiscoveryStrategyType, number>;
	confidenceDistribution: Record<string, number>;
};

export type {
	DiscoveryStrategyType,
	DiscoveredDomainInterface,
	DomainSnapshotType,
	StrategyProcessorType,
	SnapshotStoreType,
	DiscoveryEngineType,
	DiscoveryMetricsType,
};
