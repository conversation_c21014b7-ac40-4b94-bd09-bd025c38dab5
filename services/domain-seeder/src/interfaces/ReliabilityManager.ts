
type ValidationResultType =
{
	isValid: boolean;
	errors: string[];
	warnings: string[];
	sanitizedData?: unknown;
};

type CheckpointType =
{
	id: string;
	stage: string;
	timestamp: string;
	data: string;
	hash: string;
};

interface ReliabilityManagerInterface
{
	createCheckpoint(stage: string, data: unknown): Promise<string>;
	resumeFromCheckpoint(checkpointId: string): Promise<unknown>;
	validatePipelineStage(stage: string, data: unknown): Promise<ValidationResultType>;
	ensureIdempotency(operation: string, key: string): Promise<boolean>;
}

interface CheckpointStoreInterface
{
	store(checkpoint: CheckpointType): Promise<void>;
	retrieve(checkpointId: string): Promise<CheckpointType | null>;
	cleanup(maxAge: number): Promise<void>;
}

type StreamMessageType =
{
	id: string;
	fields: Record<string, string>;
};

interface StreamProcessorInterface
{
	processStream(
		streamName: string,
		consumerGroup: string,
		messageHandler: (message: StreamMessageType) => Promise<void>,
	): Promise<void>;
	acknowledgeMessage(streamName: string, messageId: string): Promise<void>;
	handleFailedMessage(
		streamName: string,
		messageId: string,
		error: Error,
		consumerGroup?: string,
		originalFields?: Record<string, string>,
	): Promise<void>;
	stopProcessing(): Promise<void>;
	recoverPendingMessages(
		streamName: string,
		consumerGroup: string,
		messageHandler: (message: StreamMessageType) => Promise<void>,
	): Promise<number>;
}

interface BackpressureControllerInterface
{
	shouldThrottle(): Promise<boolean>;
	getQueueDepth(): Promise<number>;
	waitForCapacity(): Promise<void>;
	updateMetrics(queueDepth: number, latency: number): void;
}

export type {
	ValidationResultType,
	CheckpointType,
	ReliabilityManagerInterface,
	CheckpointStoreInterface,
	StreamMessageType,
	StreamProcessorInterface,
	BackpressureControllerInterface,
};
