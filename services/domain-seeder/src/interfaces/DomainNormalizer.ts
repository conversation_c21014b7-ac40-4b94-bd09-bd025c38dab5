
type NormalizedDomainType =
{
	original: string;
	normalized: string;
	etld1: string;
	tld: string;
	isValid: boolean;
	errors?: string[];
};

type DomainNormalizerType =
{
	normalize(domain: string): NormalizedDomainType | null;
	isValidDomain(domain: string): boolean;
	getETLD1(domain: string): string | null;
	batchNormalize(domains: string[]): Promise<Map<string, NormalizedDomainType>>;
};

type PSLManagerType =
{
	updatePSL(): Promise<void>;
	getLastUpdate(): Date | null;
	isValidTLD(tld: string): boolean;
	getDomain(hostname: string): string | null;
	getPublicSuffix(hostname: string): string | null;
};

type NormalizationMetricsType =
{
	totalProcessed: number;
	validDomains: number;
	invalidDomains: number;
	ipAddressesFiltered: number;
	duplicatesRemoved: number;
};

export type {
	NormalizedDomainType,
	DomainNormalizerType,
	PSLManagerType,
	NormalizationMetricsType,
};
