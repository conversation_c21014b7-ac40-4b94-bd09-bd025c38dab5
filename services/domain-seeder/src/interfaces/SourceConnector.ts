import type { DiscoveryStrategyType } from './DiscoveryEngine';

type DomainCandidateType =
{
	domain: string;
	rank?: number;
	source: string;
	metadata?: Record<string, any>;
};

type FetchOptionsType =
{
	limit?: number;
	offset?: number;
	since?: Date;
	tiers?: ('top-10k' | '100k' | '1M' | 'long-tail')[];
	strategy?: DiscoveryStrategyType;
	location?: string; // ISO 3166-1 alpha-2 country code (US, GB, DE, etc.) or comma-separated list
};

type SourceConnectorType =
{
	name: string;
	priority: number;
	cadence: 'daily' | 'weekly' | 'monthly';

	fetchDomains(options: FetchOptionsType): AsyncIterable<DomainCandidateType>;
	getLastUpdate(): Promise<Date | null>;
	healthCheck(): Promise<boolean>;
	supportsStrategy(strategy: DiscoveryStrategyType): boolean;
};

type SourceMetricsType =
{
	candidatesFetched: number;
	fetchDuration: number;
	errors: number;
	lastFetchTime: Date | null;
};

export type {
	DomainCandidateType,
	FetchOptionsType,
	SourceConnectorType,
	SourceMetricsType,
};
