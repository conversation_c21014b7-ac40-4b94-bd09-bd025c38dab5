# Health Check System

The domain-seeder service includes a comprehensive health check system that provides detailed monitoring and alerting capabilities for container deployment and operational monitoring.

## Overview

The health check system is built around the `SeederHealthChecker` class, which extends the shared `HealthChecker` base class and provides domain-seeder specific health monitoring capabilities.

## Features

### Comprehensive Health Checks

The system monitors multiple components:

- **Database Health**: ScyllaDB, MariaDB, and Manticore Search connectivity and response times
- **Redis Health**: Connection status, memory usage, and performance metrics
- **Queue Health**: Queue depths, stream status, and backlog detection
- **Source Health**: External data source connectivity and staleness monitoring
- **PSL Health**: Public Suffix List freshness and update status
- **System Resources**: Memory, CPU, and disk usage monitoring

### Container Deployment Support

#### Readiness Probe (`/health/ready`)

- Checks if the service is ready to accept traffic
- Requires database and Redis connectivity
- Returns HTTP 200 when ready, 503 when not ready

#### Liveness Probe (`/health/live`)

- Checks if the service is alive and should not be restarted
- Basic connectivity check (primarily Redis)
- Returns HTTP 200 when alive, 503 when dead

#### Health Check (`/health`)

- Comprehensive health status
- Returns HTTP 200 for healthy, 503 for unhealthy/degraded
- Includes detailed component status and metrics

### Alert System

The health checker includes an intelligent alerting system that generates alerts for:

- **Critical Alerts**: Database failures, Redis failures, consecutive source failures
- **Warning Alerts**: High resource usage, queue backlogs, source staleness

### Monitoring Integration

#### Prometheus Metrics

- Health check duration and success rates
- Component-specific health status
- Resource usage metrics
- Alert counts by severity

#### Structured Logging

- Health check results with correlation IDs
- Component failure details
- Performance metrics

## API Endpoints

### GET /health

Comprehensive health check with detailed component status.

**Response Format:**

```json
{
  "healthy": true,
  "status": "healthy",
  "checks": {
    "database": true,
    "redis": true,
    "queue": true,
    "sources": true,
    "psl": true,
    "memory": true,
    "disk": true
  },
  "details": {
    "database": {
      "scylla": {
        "healthy": true,
        "responseTime": 15
      },
      "maria": {
        "healthy": true,
        "responseTime": 8
      },
      "manticore": {
        "healthy": true,
        "responseTime": 12,
        "indexStatus": "active"
      }
    },
    "redis": {
      "connected": true,
      "responseTime": 3,
      "server": {
        "redis_version": "7.0.0",
        "uptime_in_seconds": "3600"
      }
    },
    "queue": {
      "new:domains": {
        "length": 1250,
        "healthy": true,
        "backlog": false
      },
      "new:domains:high": {
        "length": 45,
        "healthy": true,
        "backlog": false
      }
    },
    "sources": [
      {
        "name": "tranco",
        "healthy": true,
        "lastUpdate": "2025-08-07T10:30:00Z",
        "staleness": 3600,
        "responseTime": 250
      }
    ],
    "psl": {
      "configured": true,
      "lastUpdate": "2025-08-05T14:20:00Z",
      "staleness": 172800,
      "stale": false,
      "status": "healthy"
    },
    "system": {
      "memory": {
        "used": 2048,
        "total": 8192,
        "percentage": 25,
        "healthy": true
      },
      "disk": {
        "used": 45000,
        "total": 100000,
        "percentage": 45,
        "healthy": true
      },
      "cpu": {
        "usage": 35,
        "loadAverage": [1.2, 1.5, 1.8],
        "healthy": true
      }
    }
  },
  "timestamp": "2025-08-07T14:30:00Z",
  "uptime": 86400,
  "version": "1.0.0",
  "environment": "production"
}
```

**Status Codes:**

- `200`: Service is healthy
- `503`: Service is unhealthy or degraded
- `500`: Health check system failure

### GET /health/ready

Kubernetes readiness probe endpoint.

**Response Format:**

```json
{
  "ready": true
}
```

**Status Codes:**

- `200`: Service is ready to accept traffic
- `503`: Service is not ready

### GET /health/live

Kubernetes liveness probe endpoint.

**Response Format:**

```json
{
  "alive": true
}
```

**Status Codes:**

- `200`: Service is alive
- `503`: Service should be restarted

### GET /health/status

Detailed status information for monitoring systems.

**Response Format:**

```json
{
  "healthy": true,
  "status": "healthy",
  "readiness": true,
  "liveness": true,
  "uptime": 86400,
  "memory": {
    "rss": 134217728,
    "heapTotal": 67108864,
    "heapUsed": 33554432,
    "external": 8388608,
    "arrayBuffers": 4194304
  },
  "version": "1.0.0",
  "checks": {
    /* ... */
  },
  "details": {
    /* ... */
  }
}
```

### GET /health/alerts

Alert information for monitoring and alerting systems.

**Response Format:**

```json
{
  "hasAlerts": true,
  "alerts": [
    {
      "level": "warning",
      "component": "memory",
      "message": "High memory usage: 87%",
      "timestamp": "2025-08-07T14:30:00Z",
      "details": {
        "used": 7168,
        "total": 8192,
        "percentage": 87
      }
    },
    {
      "level": "critical",
      "component": "source",
      "message": "Source tranco has 5 consecutive failures",
      "timestamp": "2025-08-07T14:30:00Z",
      "details": {
        "name": "tranco",
        "consecutiveFailures": 5,
        "lastError": "Connection timeout"
      }
    }
  ]
}
```

## Configuration

### Alert Thresholds

The health checker supports configurable alert thresholds:

```typescript
healthChecker.setAlertThresholds({
  memory: 85, // Memory usage percentage
  disk: 90, // Disk usage percentage
  cpu: 80, // CPU usage percentage
  queueDepth: 100000, // Maximum queue depth
  sourceStalenesss: 25 * 3600, // Source staleness in seconds (25 hours)
  pslStaleness: 7 * 24 * 3600, // PSL staleness in seconds (7 days)
  responseTime: 5000, // Maximum response time in ms
  consecutiveFailures: 3, // Maximum consecutive failures
});
```

### Source Registration

External data sources can be registered for health monitoring:

```typescript
healthChecker.registerSourceConnector("tranco", trancoConnector);
healthChecker.registerSourceConnector("radar", radarConnector);
```

### PSL Manager Integration

The Public Suffix List manager can be integrated for PSL health monitoring:

```typescript
healthChecker.setPSLManager(pslManager);
```

## Usage Examples

### Basic Health Check

```typescript
const health = await healthChecker.checkHealth();
if (health.healthy) {
  console.log("Service is healthy");
} else {
  console.log("Service has issues:", health.details);
}
```

### Readiness Check

```typescript
const ready = await healthChecker.checkReadiness();
if (ready) {
  // Service can accept traffic
  startAcceptingRequests();
}
```

### Alert Monitoring

```typescript
const alerts = await healthChecker.checkAlerts();
if (alerts.hasAlerts) {
  alerts.alerts.forEach((alert) => {
    if (alert.level === "critical") {
      sendCriticalAlert(alert);
    } else {
      sendWarningAlert(alert);
    }
  });
}
```

## Kubernetes Integration

### Deployment Configuration

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: domain-seeder
spec:
  template:
    spec:
      containers:
        - name: domain-seeder
          image: domain-seeder:latest
          ports:
            - containerPort: 3000
          readinessProbe:
            httpGet:
              path: /health/ready
              port: 3000
            initialDelaySeconds: 10
            periodSeconds: 5
            timeoutSeconds: 3
            failureThreshold: 3
          livenessProbe:
            httpGet:
              path: /health/live
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
```

### Service Configuration

```yaml
apiVersion: v1
kind: Service
metadata:
  name: domain-seeder-service
spec:
  selector:
    app: domain-seeder
  ports:
    - port: 80
      targetPort: 3000
  type: ClusterIP
```

## Monitoring Integration

### Prometheus Configuration

```yaml
scrape_configs:
  - job_name: "domain-seeder"
    static_configs:
      - targets: ["domain-seeder:3000"]
    metrics_path: /metrics
    scrape_interval: 30s
```

### Grafana Dashboard

The health check system provides metrics that can be visualized in Grafana:

- Health check success rate
- Component availability over time
- Response time trends
- Alert frequency by component
- Resource usage trends

### Alertmanager Rules

```yaml
groups:
  - name: domain-seeder
    rules:
      - alert: DomainSeederUnhealthy
        expr: domain_seeder_health_status != 1
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Domain Seeder service is unhealthy"

      - alert: DomainSeederHighMemory
        expr: domain_seeder_memory_usage_percent > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Domain Seeder high memory usage"
```

## Testing

The health check system includes comprehensive tests:

- **Unit Tests**: Individual component health checks
- **Integration Tests**: End-to-end health check workflows
- **Reliability Tests**: Failure detection and recovery scenarios
- **Performance Tests**: Response time and concurrency testing

Run tests with:

```bash
pnpm test SeederHealthChecker
```

## Troubleshooting

### Common Issues

1. **Health Check Timeouts**

   - Check database connectivity
   - Verify Redis connection
   - Review system resource usage

2. **False Positive Alerts**

   - Adjust alert thresholds
   - Check for transient network issues
   - Review source connector configurations

3. **Readiness Probe Failures**
   - Ensure database migrations are complete
   - Verify Redis connectivity
   - Check service startup dependencies

### Debug Information

Enable debug logging for detailed health check information:

```typescript
const logger = getLogger("SeederHealthChecker");
logger.setLevel("debug");
```

### Health Check Cache

The system includes caching to prevent excessive health checks:

```typescript
// Clear cache if needed
healthChecker.clearCache();

// Get current thresholds
const thresholds = healthChecker.getAlertThresholds();
```
