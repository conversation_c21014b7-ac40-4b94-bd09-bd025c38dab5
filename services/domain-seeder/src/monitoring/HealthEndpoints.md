# Health Check Endpoints

This document describes the comprehensive health check endpoints implemented for the domain-seeder service, fulfilling requirements 8.4, 8.5, and 10.5.

## Overview

The domain-seeder service provides multiple health check endpoints designed for different use cases:

- **General Health**: `/health` - Comprehensive health status
- **Readiness Probe**: `/health/ready` - Container readiness for traffic
- **Liveness Probe**: `/health/live` - Container liveness check
- **Detailed Status**: `/status` - Extended status with metrics
- **Manual Operations**: `/trigger` - Manual operation triggers

## Endpoints

### GET /health

Comprehensive health check endpoint that monitors all system components.

**Response Format:**

```json
{
  "healthy": true,
  "status": "healthy|degraded|unhealthy",
  "checks": {
    "database": true,
    "redis": true,
    "queue": true,
    "sources": true,
    "psl": true,
    "memory": true,
    "disk": true
  },
  "details": {
    "database": {
      "scylla": {
        "healthy": true,
        "responseTime": 45
      },
      "maria": {
        "healthy": true,
        "responseTime": 32
      },
      "manticore": {
        "healthy": true,
        "responseTime": 28,
        "indexStatus": "active"
      }
    },
    "redis": {
      "connected": true,
      "responseTime": 12,
      "server": {
        "redis_version": "7.0.0",
        "uptime_in_seconds": "3600"
      },
      "memory": {
        "used_memory": "1048576",
        "maxmemory": "0"
      }
    },
    "queue": {
      "new:domains": {
        "length": 1000,
        "healthy": true,
        "backlog": false
      },
      "new:domains:high": {
        "length": 500,
        "healthy": true,
        "backlog": false
      },
      "streams": {
        "stream:new:domains": {
          "length": 10,
          "consumerGroups": 1,
          "pendingMessages": 0
        }
      }
    },
    "sources": [
      {
        "name": "tranco",
        "healthy": true,
        "lastUpdate": "2025-08-07T18:00:00.000Z",
        "staleness": 3600,
        "responseTime": 250,
        "consecutiveFailures": 0
      }
    ],
    "psl": {
      "configured": true,
      "lastUpdate": "2025-08-07T12:00:00.000Z",
      "staleness": 21600,
      "stale": false,
      "status": "healthy"
    },
    "system": {
      "memory": {
        "used": 1024,
        "total": 8192,
        "percentage": 12,
        "healthy": true
      },
      "disk": {
        "used": 10240,
        "total": 102400,
        "percentage": 10,
        "healthy": true
      },
      "cpu": {
        "usage": 25,
        "loadAverage": [1.0, 1.5, 2.0],
        "healthy": true
      }
    }
  },
  "timestamp": "2025-08-07T18:30:00.000Z",
  "uptime": 7200,
  "version": "1.0.0",
  "environment": "production"
}
```

**Status Codes:**

- `200` - Service is healthy
- `503` - Service is unhealthy or degraded
- `500` - Health check failed

### GET /health/ready

Kubernetes readiness probe endpoint. Checks if the service is ready to accept traffic.

**Response Format:**

```json
{
  "ready": true
}
```

**Status Codes:**

- `200` - Service is ready
- `503` - Service is not ready
- `500` - Readiness check failed

**Readiness Criteria:**

- Database connections are healthy
- Redis connection is healthy

### GET /health/live

Kubernetes liveness probe endpoint. Checks if the service is alive and should not be restarted.

**Response Format:**

```json
{
  "alive": true
}
```

**Status Codes:**

- `200` - Service is alive
- `503` - Service is not alive
- `500` - Liveness check failed

**Liveness Criteria:**

- Basic Redis connectivity

### GET /status

Extended status endpoint with detailed metrics and sync information.

**Response Format:**

```json
{
  "healthy": true,
  "status": "healthy",
  "checks": {
    /* ... */
  },
  "details": {
    /* ... */
  },
  "readiness": true,
  "liveness": true,
  "uptime": 7200,
  "memory": {
    "rss": 104857600,
    "heapTotal": 67108864,
    "heapUsed": 33554432,
    "external": 1048576
  },
  "version": "1.0.0",
  "metrics": {
    "candidates_fetched_total": 1000000,
    "new_discovered_total": 50000,
    "enqueue_attempts_total": 45000,
    "enqueue_success_total": 44500
  },
  "syncMetrics": {
    "last_sync": "2025-08-07T18:25:00.000Z",
    "sync_status": "healthy",
    "manticore_index_size": 1000000
  }
}
```

### POST /trigger

Manual operation trigger endpoint for administrative tasks.

**Request Format:**

```json
{
  "operation": "discovery-run|top-sources-only|backfill|health-check|clear-cache",
  "parameters": {
    "limit": 10000,
    "maxNewPerDay": 500000
  }
}
```

**Response Format:**

```json
{
  "operation": "discovery-run",
  "parameters": {
    /* ... */
  },
  "result": {
    "totalCandidates": 100,
    "totalDiscovered": 10,
    "strategiesUsed": 4,
    "duration": 5000
  },
  "timestamp": "2025-08-07T18:30:00.000Z"
}
```

**Supported Operations:**

- `discovery-run` - Execute full discovery cycle
- `top-sources-only` - Process only top sources (Tranco/Radar)
- `backfill` - Process long-tail sources
- `health-check` - Perform health check
- `clear-cache` - Clear health check cache

## Monitoring Requirements Compliance

### Requirement 8.4 - System Monitoring

The health endpoints monitor the following metrics as required:

1. **Queue Depth**: Monitors `new:domains` and `new:domains:high` queue lengths
2. **Database Check Latency**: Measures response times for Scylla, Maria, and Manticore
3. **Source Staleness**: Tracks last update time for each source connector

### Requirement 8.5 - Alerting

The health system triggers alerts for:

1. **Zero Discoveries**: Detected through source monitoring and discovery metrics
2. **Content Generation Failures**: Monitored through validation failure metrics
3. **Database Errors**: Detected through database health checks
4. **Queue Depth Issues**: Triggered when queue length exceeds thresholds

### Requirement 10.5 - HTTP Endpoints

The service exposes the required endpoints:

1. **`/health`** - General health status
2. **`/trigger`** - Manual operation triggers

## Container Deployment Support

### Kubernetes Configuration

```yaml
apiVersion: v1
kind: Pod
spec:
  containers:
    - name: domain-seeder
      image: domain-seeder:latest
      ports:
        - containerPort: 3000
      livenessProbe:
        httpGet:
          path: /health/live
          port: 3000
        initialDelaySeconds: 30
        periodSeconds: 10
        timeoutSeconds: 5
        failureThreshold: 3
      readinessProbe:
        httpGet:
          path: /health/ready
          port: 3000
        initialDelaySeconds: 5
        periodSeconds: 5
        timeoutSeconds: 3
        failureThreshold: 3
```

### Docker Health Check

```dockerfile
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health/live || exit 1
```

## Alert Thresholds

The health checker uses configurable alert thresholds:

```typescript
{
  memory: 85,           // percentage
  disk: 90,             // percentage
  cpu: 80,              // percentage
  queueDepth: 100000,   // messages
  sourceStalenesss: 25 * 3600,  // 25 hours in seconds
  pslStaleness: 7 * 24 * 3600,  // 7 days in seconds
  responseTime: 5000,   // 5 seconds
  consecutiveFailures: 3
}
```

## Error Handling

All endpoints implement comprehensive error handling:

1. **Graceful Degradation**: Partial failures don't crash the entire health check
2. **Timeout Protection**: Health checks have configurable timeouts
3. **Circuit Breaker**: Consecutive failures trigger circuit breaker patterns
4. **Caching**: Results are cached to prevent excessive health check overhead

## Security Considerations

1. **No Sensitive Data**: Health responses don't expose sensitive configuration
2. **Rate Limiting**: Health checks are cached to prevent abuse
3. **Input Validation**: Trigger endpoint validates all input parameters
4. **Error Sanitization**: Error messages are sanitized in production

## Usage Examples

### Basic Health Check

```bash
curl http://localhost:3000/health
```

### Kubernetes Readiness

```bash
curl http://localhost:3000/health/ready
```

### Manual Discovery Run

```bash
curl -X POST http://localhost:3000/trigger \
  -H "Content-Type: application/json" \
  -d '{"operation": "discovery-run", "parameters": {"limit": 1000}}'
```

### Clear Health Cache

```bash
curl -X POST http://localhost:3000/trigger \
  -H "Content-Type: application/json" \
  -d '{"operation": "clear-cache"}'
```

## Monitoring Integration

### Prometheus Metrics

The `/metrics` endpoint exposes Prometheus-compatible metrics:

```
# HELP domain_seeder_health_check_duration_seconds Time spent on health checks
# TYPE domain_seeder_health_check_duration_seconds histogram
domain_seeder_health_check_duration_seconds_bucket{le="0.1"} 45
domain_seeder_health_check_duration_seconds_bucket{le="0.5"} 123
domain_seeder_health_check_duration_seconds_bucket{le="1.0"} 150
domain_seeder_health_check_duration_seconds_count 150
domain_seeder_health_check_duration_seconds_sum 45.2

# HELP domain_seeder_health_status Current health status (1=healthy, 0=unhealthy)
# TYPE domain_seeder_health_status gauge
domain_seeder_health_status{component="database"} 1
domain_seeder_health_status{component="redis"} 1
domain_seeder_health_status{component="queue"} 1
```

### Grafana Dashboard

The health metrics can be visualized in Grafana with panels for:

1. Overall health status over time
2. Component-specific health trends
3. Response time distributions
4. Alert frequency and resolution times
5. Queue depth and backlog trends

This comprehensive health check system ensures reliable monitoring and operational visibility for the domain-seeder service.
