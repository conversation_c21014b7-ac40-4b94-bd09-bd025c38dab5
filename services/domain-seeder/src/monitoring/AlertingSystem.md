# AlertingSystem

The AlertingSystem provides comprehensive monitoring and alerting capabilities for the domain-seeder service, implementing requirement 8.5 from the specification.

## Overview

The AlertingSystem continuously monitors various aspects of the domain-seeder service and triggers alerts when predefined conditions are met. It includes automated recovery mechanisms for common failure scenarios and provides detailed alert management capabilities.

## Features

### Alert Types

The system monitors and alerts on the following conditions:

1. **Zero Discoveries** (`zero_discoveries`)

   - **Level**: Critical
   - **Condition**: No new domains discovered in the last 24 hours
   - **Recovery**: Restart discovery strategies

2. **Content Generation Failures** (`content_generation_failures`)

   - **Level**: Warning
   - **Condition**: Content generation failure rate exceeds 10%
   - **Recovery**: None (manual intervention required)

3. **Database Errors Spike** (`db_errors_spike`)

   - **Level**: Critical
   - **Condition**: Database error count exceeds 10 errors in 1 hour
   - **Recovery**: Restart database connections

4. **Queue Depth Threshold** (`queue_depth_threshold`)

   - **Level**: Warning
   - **Condition**: Queue depth exceeds 100,000 messages for more than 1 hour
   - **Recovery**: Increase processing rate

5. **Source Staleness** (`source_staleness`)

   - **Level**: Warning
   - **Condition**: Source data is stale (>25 hours old)
   - **Recovery**: None (depends on external sources)

6. **Effectiveness Degradation** (`effectiveness_degradation`)
   - **Level**: Warning
   - **Condition**: Overall discovery effectiveness drops below 50%
   - **Recovery**: None (requires strategy analysis)

### Alert Management

- **Cooldown Periods**: Prevents alert spam by enforcing minimum time between identical alerts
- **Suppression**: Allows temporary suppression of specific alert types
- **Auto-Resolution**: Automatically resolves alerts when conditions improve
- **Occurrence Tracking**: Tracks repeated occurrences and escalates to critical level
- **History**: Maintains detailed alert history for analysis

### Recovery Actions

The system includes automated recovery mechanisms for common failure scenarios:

1. **Restart Discovery Strategies**: Restarts strategy processors when discoveries stop
2. **Restart Database Connections**: Restarts connection pools during DB errors
3. **Increase Processing Rate**: Temporarily increases queue processing rate

Recovery features:

- **Attempt Limiting**: Maximum 3 recovery attempts per alert
- **Exponential Backoff**: Increasing delays between recovery attempts
- **Success Tracking**: Stops recovery attempts when alerts are resolved

## Configuration

```typescript
interface AlertingConfig {
  // Alert thresholds
  zeroDiscoveryHours: number; // Default: 24
  contentFailureRate: number; // Default: 0.1 (10%)
  dbErrorSpikeThreshold: number; // Default: 10
  queueDepthThreshold: number; // Default: 100000
  queueDepthDurationMinutes: number; // Default: 60
  sourceStalenesshours: number; // Default: 25
  effectivenessDegradationThreshold: number; // Default: 0.5

  // System resource thresholds
  memoryThreshold: number; // Default: 85%
  diskThreshold: number; // Default: 90%
  cpuThreshold: number; // Default: 80%

  // Alert management
  maxActiveAlerts: number; // Default: 50
  alertRetentionDays: number; // Default: 30
  suppressDuplicateMinutes: number; // Default: 15

  // Recovery settings
  enableAutoRecovery: boolean; // Default: true
  maxRecoveryAttempts: number; // Default: 3
  recoveryBackoffMinutes: number; // Default: 5

  // Notification settings
  enableNotifications: boolean; // Default: true
  notificationChannels: string[]; // Default: ['log']
}
```

## Usage

### Basic Setup

```typescript
import AlertingSystem from "./monitoring/AlertingSystem";
import PrometheusMetricsCollector from "./monitoring/PrometheusMetricsCollector";
import SeederHealthChecker from "./monitoring/SeederHealthChecker";
import DiscoveryEffectivenessMonitor from "./monitoring/DiscoveryEffectivenessMonitor";

const alertingSystem = new AlertingSystem(
  {
    zeroDiscoveryHours: 24,
    contentFailureRate: 0.1,
    enableAutoRecovery: true,
  },
  redisClient,
  metricsCollector,
  healthChecker,
  effectivenessMonitor
);

// Start monitoring
alertingSystem.start();
```

### Manual Alert Management

```typescript
// Get active alerts
const activeAlerts = alertingSystem.getActiveAlerts();

// Get alert history
const history = alertingSystem.getAlertHistory(50);

// Suppress an alert
await alertingSystem.suppressAlert(
  "zero_discoveries",
  60,
  "Maintenance window"
);

// Resolve an alert manually
await alertingSystem.resolveAlert("db_errors_spike", "manual-fix");

// Get alert statistics
const stats = alertingSystem.getAlertStatistics();
```

### Testing

```typescript
// Trigger a test alert
const testAlert = await alertingSystem.testAlert();
console.log("Test alert created:", testAlert.id);
```

## Alert Structure

```typescript
interface Alert {
  id: string; // Unique alert identifier
  type: AlertType; // Type of alert
  level: AlertLevel; // Severity level
  title: string; // Human-readable title
  message: string; // Detailed message
  timestamp: Date; // When alert was triggered
  component: string; // Component that triggered alert
  details: Record<string, any>; // Additional context data
  resolved: boolean; // Resolution status
  resolvedAt?: Date; // When alert was resolved
  resolvedBy?: string; // Who/what resolved the alert
  suppressUntil?: Date; // Suppression end time
  occurrenceCount: number; // Number of occurrences
  firstOccurrence: Date; // First occurrence time
  lastOccurrence: Date; // Last occurrence time
}
```

## Monitoring Integration

The AlertingSystem integrates with other monitoring components:

- **PrometheusMetricsCollector**: Provides metrics data for alert conditions
- **SeederHealthChecker**: Provides health status for system alerts
- **DiscoveryEffectivenessMonitor**: Provides effectiveness data for performance alerts
- **Redis**: Stores alert state and provides coordination

## False Positive Prevention

The system includes several mechanisms to prevent false positives:

1. **Baseline Establishment**: Doesn't trigger zero discovery alerts on first run
2. **Duration Requirements**: Queue depth alerts require sustained high levels
3. **Rate Calculations**: Content failure alerts require actual content generation
4. **Data Validation**: Handles missing or invalid metric data gracefully
5. **Cooldown Periods**: Prevents duplicate alerts from transient conditions

## Recovery Action Safety

Recovery actions are designed to be safe and non-destructive:

1. **Attempt Limiting**: Maximum attempts prevent infinite loops
2. **Exponential Backoff**: Prevents overwhelming systems during recovery
3. **Success Detection**: Stops recovery when conditions improve
4. **Error Handling**: Gracefully handles recovery action failures
5. **Logging**: Comprehensive logging of all recovery attempts

## Monitoring and Observability

The AlertingSystem provides comprehensive observability:

- **Alert Statistics**: Counts, rates, and trends
- **Component Health**: Per-component alert tracking
- **Resolution Times**: Average time to resolve alerts
- **Recovery Success**: Success rates of automated recovery
- **Historical Analysis**: Long-term alert patterns

## Best Practices

1. **Threshold Tuning**: Adjust thresholds based on operational experience
2. **Suppression Usage**: Use suppression during maintenance windows
3. **Recovery Monitoring**: Monitor recovery action success rates
4. **Alert Review**: Regularly review alert history for patterns
5. **Documentation**: Document custom recovery procedures

## Troubleshooting

### Common Issues

1. **Too Many Alerts**: Adjust thresholds or increase cooldown periods
2. **False Positives**: Review alert conditions and add validation
3. **Recovery Failures**: Check recovery action implementations
4. **Missing Alerts**: Verify monitoring component integration

### Debug Mode

Enable debug logging to troubleshoot alert issues:

```typescript
// Set log level to debug
process.env.LOG_LEVEL = "debug";

// Check alert rule evaluation
await alertingSystem.checkAlerts();
```

## Performance Considerations

- **Check Frequency**: Default 1-minute intervals balance responsiveness and load
- **Alert Storage**: Redis-based storage with configurable retention
- **Recovery Backoff**: Exponential backoff prevents system overload
- **Metric Caching**: Health checks include caching to reduce load

## Security Considerations

- **Alert Data**: Alerts may contain sensitive system information
- **Recovery Actions**: Recovery actions have system-level privileges
- **Notification Channels**: Secure notification channel configuration
- **Access Control**: Implement appropriate access controls for alert management
