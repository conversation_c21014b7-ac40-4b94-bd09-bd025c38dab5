/**
 * Metrics Collector for Domain Seeder
 * Simplified version replacing SeederMetricsCollector
 */

import { RedisClientWrapper } from '@shared';

export class MetricsCollector
{
	private redis: RedisClientWrapper;

	constructor(redis: RedisClientWrapper)
	{
		this.redis = redis;
	}

	// Domain seeder specific metrics
	recordDiscovery(source: string, domainCount: number)
	{
		this.recordMetric('discoveries', domainCount, { source });
	}

	recordContentGeneration(domain: string, success: boolean)
	{
		this.recordMetric('content_generation', success ? 1 : 0, { domain, success });
	}

	recordValidation(domain: string, valid: boolean)
	{
		this.recordMetric('validation', valid ? 1 : 0, { domain, valid });
	}

	recordEnqueuing(queueName: string, count: number)
	{
		this.recordMetric('enqueued', count, { queue: queueName });
	}

	recordProvenance(domain: string, sources: string[])
	{
		this.recordMetric('provenance', 1, { domain, sourceCount: sources.length });
	}

	getSeederMetrics()
	{
		return {
			discoveries: this.getMetric('discoveries'),
			contentGeneration: this.getMetric('content_generation'),
			validation: this.getMetric('validation'),
			enqueued: this.getMetric('enqueued'),
			provenance: this.getMetric('provenance'),
			queueDepth: this.getMetric('queue_depth') || 0,
			dbCheckLatencyMs: this.getMetric('db_latency') || 0,
			sourceStalenessSeconds: this.getMetric('source_staleness') || 0,
			rateLimitedTotal: this.getMetric('rate_limited') || 0,
			strategyBreakdown: this.getMetric('strategy_breakdown') || {},
			confidenceDistribution: this.getMetric('confidence_distribution') || {},
			dbErrorsTotal: this.getMetric('db_errors') || 0,
			sourceErrorsTotal: this.getMetric('source_errors') || 0,
			normalizationErrorsTotal: this.getMetric('normalization_errors') || 0,
			discovery: {
				discoveryRate: Math.round(Math.random() * 100), // Mock discovery rate
				newDiscovered: this.getMetric('discoveries') || 0,
			},
		};
	}
}

export default MetricsCollector;
