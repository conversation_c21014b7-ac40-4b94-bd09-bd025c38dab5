/**
 * Graceful Shutdown Handler
 * Manages clean shutdown of services and resources
 */

import { logger as sharedLogger } from '@shared';

const logger = sharedLogger.getLogger('GracefulShutdown');

export class GracefulShutdown 
{
	private shutdownHandlers: Array<() => Promise<void>> = [];
	private isShuttingDown = false;
	private shutdownTimeout = 30000; // 30 seconds

	constructor(timeout?: number) 
	{
		if (timeout) 
		{
			this.shutdownTimeout = timeout;
		}
	}

	/**
	 * Register a cleanup handler
	 */
	registerHandler(name: string, handler: () => Promise<void>): void 
	{
		logger.info(`Registering shutdown handler: ${name}`);
		this.shutdownHandlers.push(async () => 
		{
			try 
			{
				logger.info(`Running shutdown handler: ${name}`);
				await handler();
				logger.info(`Completed shutdown handler: ${name}`);
			}
			catch (error) 
			{
				logger.error(`Error in shutdown handler ${name}:`, error);
			}
		});
	}

	/**
	 * Setup signal handlers
	 */
	setupSignalHandlers(): void 
	{
		// Handle SIGTERM (Kubernetes/Docker stop)
		process.on('SIGTERM', () => 
		{
			logger.info('Received SIGTERM signal');
			this.shutdown('SIGTERM');
		});

		// Handle SIGINT (Ctrl+C)
		process.on('SIGINT', () => 
		{
			logger.info('Received SIGINT signal');
			this.shutdown('SIGINT');
		});

		// Handle uncaught exceptions
		process.on('uncaughtException', (error) => 
		{
			logger.error({ error: error.message, msg: 'Uncaught exception' });
			this.shutdown('uncaughtException', 1);
		});

		// Handle unhandled promise rejections
		process.on('unhandledRejection', (reason, promise) => 
		{
			logger.error({ reason: String(reason), msg: 'Unhandled promise rejection' });
			this.shutdown('unhandledRejection', 1);
		});

		logger.info('Graceful shutdown handlers registered');
	}

	/**
	 * Execute shutdown sequence
	 */
	private async shutdown(signal: string, exitCode: number = 0): Promise<void> 
	{
		if (this.isShuttingDown) 
		{
			logger.warn('Shutdown already in progress');
			return;
		}

		this.isShuttingDown = true;
		logger.info(`Starting graceful shutdown (signal: ${signal})`);

		// Set a timeout for shutdown
		const shutdownTimer = setTimeout(() => 
		{
			logger.error('Graceful shutdown timeout exceeded, forcing exit');
			process.exit(exitCode);
		}, this.shutdownTimeout);

		try 
		{
			// Run all cleanup handlers in reverse order
			for (const handler of this.shutdownHandlers.reverse()) 
			{
				await handler();
			}

			logger.info('Graceful shutdown completed');
			clearTimeout(shutdownTimer);
			process.exit(exitCode);
		}
		catch (error) 
		{
			logger.error('Error during graceful shutdown:', error);
			clearTimeout(shutdownTimer);
			process.exit(1);
		}
	}

	/**
	 * Force immediate shutdown
	 */
	forceShutdown(reason: string): void 
	{
		logger.error(`Forcing immediate shutdown: ${reason}`);
		process.exit(1);
	}
}

export default GracefulShutdown;
