/**
 * HTTP Server Management for Domain Seeder
 * Handles HTTP endpoints and health checks
 */

import express, { Express, Request, Response } from 'ultimate-express';
import type { Server } from 'node:http';
import { logger as sharedLogger } from '@shared';
import type { HealthChecker } from '../monitoring/HealthChecker';
import type { MetricsCollector } from '../monitoring/MetricsCollector';

const logger = sharedLogger.getLogger('HttpServer');

export class HttpServer 
{
	private app: Express;
	private server: Server | null = null;
	private port: number;
	private healthChecker: HealthChecker;
	private metricsCollector: MetricsCollector;

	constructor(
		healthChecker: HealthChecker,
		metricsCollector: MetricsCollector,
		port: number = 3004
	) 
	{
		this.healthChecker = healthChecker;
		this.metricsCollector = metricsCollector;
		this.port = port;
		this.app = express();
		this.setupMiddleware();
		this.setupRoutes();
	}

	private setupMiddleware(): void 
	{
		this.app.use(express.json({ limit: '10mb' }));
		this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));
	}

	private setupRoutes(): void 
	{
		// Health endpoint
		this.app.get('/health', async (req: Request, res: Response) => 
		{
			try 
			{
				const health = await this.healthChecker.checkHealth();
				res.status(health.status === 'healthy' ? 200 : 503).json(health);
			}
			catch (error) 
			{
				res.status(500).json({ healthy: false, error: error.message });
			}
		});

		// Readiness endpoint
		this.app.get('/health/ready', async (req: Request, res: Response) => 
		{
			try 
			{
				const health = await this.healthChecker.checkHealth();
				const ready = health.status === 'healthy';
				res.status(ready ? 200 : 503).json({ ready });
			}
			catch (error) 
			{
				res.status(500).json({ ready: false, error: error.message });
			}
		});

		// Liveness endpoint
		this.app.get('/health/live', async (req: Request, res: Response) => 
		{
			try 
			{
				const health = await this.healthChecker.checkHealth();
				const alive = health.status !== 'unhealthy';
				res.status(alive ? 200 : 503).json({ alive });
			}
			catch (error) 
			{
				res.status(500).json({ alive: false, error: error.message });
			}
		});

		// Metrics endpoint
		this.app.get('/metrics', (req: Request, res: Response) => 
		{
			try 
			{
				const metrics = this.metricsCollector.getSeederMetrics();
				res.json(metrics);
			}
			catch (error) 
			{
				res.status(500).json({ error: error.message });
			}
		});

		// Status endpoint
		this.app.get('/status', async (req: Request, res: Response) => 
		{
			try 
			{
				const health = await this.healthChecker.checkHealth();
				const metrics = this.metricsCollector.getSeederMetrics();

				res.json({
					status: health.status,
					uptime: health.uptime,
					metrics,
					timestamp: new Date().toISOString(),
				});
			}
			catch (error) 
			{
				logger.error('Status endpoint error', error);
				res.status(500).json({ error: error.message });
			}
		});
	}

	async start(): Promise<void> 
	{
		return new Promise((resolve, reject) => 
		{
			this.server = this.app.listen(this.port, () => 
			{
				logger.info(`HTTP server listening on port ${this.port}`);
				resolve();
			});

			this.server.on('error', (error: Error) => 
			{
				logger.error({ error: error.message, msg: 'HTTP server error' });
				reject(error);
			});
		});
	}

	async stop(): Promise<void> 
	{
		return new Promise((resolve) => 
		{
			if (this.server) 
			{
				this.server.close(() => 
				{
					logger.info('HTTP server stopped');
					resolve();
				});
			}
			else 
			{
				resolve();
			}
		});
	}
}

export default HttpServer;
