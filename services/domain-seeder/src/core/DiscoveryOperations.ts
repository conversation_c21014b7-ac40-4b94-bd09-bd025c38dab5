/**
 * Discovery Operations
 * Handles all discovery-related operations for the Domain Seeder
 */

import { logger as sharedLogger } from '@shared';
import type { DatabaseManager } from '@shared';
import type { DomainCandidateType } from '../interfaces/SourceConnector';
import type { DiscoveryStrategyType } from '../interfaces/DiscoveryEngine';
import type RateLimitedDomainEnqueuer from '../enqueuer/RateLimitedDomainEnqueuer';
import TrancoConnector from '../connectors/TrancoConnector';
import RadarConnector from '../connectors/RadarConnector';
import UmbrellaConnector from '../connectors/UmbrellaConnector';
import CommonCrawlConnector from '../connectors/CommonCrawlConnector';
import SonarConnector from '../connectors/SonarConnector';
import CZDSConnector from '../connectors/CZDSConnector';

const logger = sharedLogger.getLogger('DiscoveryOperations');

type DiscoveryParametersType =
{
	strategies?: DiscoveryStrategyType[];
	sources?: string[];
	limit?: number;
	dryRun?: boolean;
};

type DiscoveryResultsType =
{
	totalCandidates: number;
	totalDiscovered: number;
	totalEnqueued: number;
	strategies: Record<string, {
		candidates: number;
		discovered: number;
		enqueued: number;
	}>;
};

type BackfillParametersType =
{
	sources?: string[];
	limit?: number;
	dryRun?: boolean;
	maxDomains?: number;
};

type TopSourcesParametersType =
{
	sources?: string[];
	limit?: number;
	aggregate?: boolean;
};

type SourceResultType =
{
	count: number;
	sample?: string[];
};

type TopSourcesResultsType =
{
	sources: Record<string, SourceResultType>;
	aggregated: {
		uniqueCount: number;
		domains: string[];
	} | null;
};

export class DiscoveryOperations 
{
	private dbManager: DatabaseManager;

	constructor(dbManager: DatabaseManager) 
	{
		this.dbManager = dbManager;
	}

	/**
	 * Trigger a discovery run with specified parameters
	 */
	async triggerDiscoveryRun(
		parameters?: DiscoveryParametersType,
		enqueuer?: RateLimitedDomainEnqueuer
	): Promise<DiscoveryResultsType> 
	{
		try 
		{
			logger.info({ parameters, msg: 'Triggering discovery run' });

			// Import discovery components dynamically
			const { DiscoveryEngineFactory } = await import('../discovery/DiscoveryEngineFactory');

			// Create discovery engine
			const factory = new DiscoveryEngineFactory();
			const discoveryEngine = await factory.createDiscoveryEngine(
				this.dbManager.getRedisClient(),
				{}
			);

			// Use the provided enqueuer or log that none was provided

			// Define strategies and sources
			const strategies: DiscoveryStrategyType[] = parameters?.strategies || ['differential', 'zone-new'];
			const sources = parameters?.sources || ['tranco', 'radar'];
			const limit = parameters?.limit || 100000;
			const dryRun = parameters?.dryRun || false;

			const results: DiscoveryResultsType = {
				totalCandidates: 0,
				totalDiscovered: 0,
				totalEnqueued: 0,
				strategies: {},
			};

			// Process each strategy
			for (const strategy of strategies) 
			{
				const candidates = await this.getCandidatesForStrategy(strategy, sources, limit);
				const discoveredIter = await discoveryEngine.processWithStrategy(strategy, candidates);
				const discovered = [];
				for await (const item of discoveredIter) {
					discovered.push(item);
				}

				results.totalCandidates += candidates.length;
				results.totalDiscovered += discovered.length;

				if (!dryRun && discovered.length > 0)
				{
					if (enqueuer) {
						// Convert discovered domains to the format expected by enqueuer
						const domainsForEnqueue = discovered.map(candidate => ({
							domain: candidate.domain,
							source: candidate.source,
							discoveryStrategy: strategy,
							confidence: 0.8, // Default confidence for discovered domains
							discoveryReason: `Discovered via ${strategy} strategy from ${candidate.source}`,
							rank: candidate.rank,
							metadata: candidate.metadata
						}));

						const enqueued = await enqueuer.enqueueDomains(domainsForEnqueue);
						results.totalEnqueued += enqueued;
						logger.info({
							discoveredCount: discovered.length,
							enqueuedCount: enqueued,
							strategy,
							msg: 'Domains enqueued successfully'
						});
					} else {
						logger.warn({
							discoveredCount: discovered.length,
							msg: 'No enqueuer provided, domains discovered but not enqueued'
						});
					}
				}

				results.strategies[strategy] = {
					candidates: candidates.length,
					discovered: discovered.length,
					enqueued: dryRun ? 0 : discovered.length,
				};
			}

			logger.info({ results, msg: 'Discovery run completed' });
			return results;
		}
		catch (error) 
		{
			logger.error({ error, msg: 'Discovery run failed' });
			throw error;
		}
	}

	/**
	 * Trigger top sources analysis run
	 */
	async triggerTopSourcesRun(parameters?: TopSourcesParametersType): Promise<TopSourcesResultsType> 
	{
		try 
		{
			logger.info({ parameters, msg: 'Triggering top sources run' });

			// Using imported connectors

			const limit = parameters?.limit || 10000;
			const sources = parameters?.sources || ['tranco', 'radar', 'umbrella'];
			const aggregate = parameters?.aggregate !== false;

			const results: TopSourcesResultsType = {
				sources: {},
				aggregated: null,
			};

			// Collect from each source
			if (sources.includes('tranco'))
			{
				const trancoConnector = new TrancoConnector({});
				const trancoDomainsIter = await trancoConnector.fetchDomains({ limit });
				const trancoDomains = [];
				for await (const domain of trancoDomainsIter) {
					trancoDomains.push(domain);
					if (trancoDomains.length >= limit) break;
				}
				results.sources.tranco = {
					count: trancoDomains.length,
					sample: trancoDomains.slice(0, 10).map(d => d.domain),
				};
			}

			if (sources.includes('radar'))
			{
				const radarConnector = new RadarConnector({});
				const radarDomainsIter = await radarConnector.fetchDomains({ limit });
				const radarDomains = [];
				for await (const domain of radarDomainsIter) {
					radarDomains.push(domain);
					if (radarDomains.length >= limit) break;
				}
				results.sources.radar = {
					count: radarDomains.length,
					sample: radarDomains.slice(0, 10).map(d => d.domain),
				};
			}

			if (sources.includes('umbrella'))
			{
				const umbrellaConnector = new UmbrellaConnector({});
				const umbrellaDomainsIter = await umbrellaConnector.fetchDomains({ limit });
				const umbrellaDomains = [];
				for await (const domain of umbrellaDomainsIter) {
					umbrellaDomains.push(domain);
					if (umbrellaDomains.length >= limit) break;
				}
				results.sources.umbrella = {
					count: umbrellaDomains.length,
					sample: umbrellaDomains.slice(0, 10).map(d => d.domain),
				};
			}

			// Aggregate if requested
			if (aggregate) 
			{
				const allDomains = new Set();
				Object.values(results.sources).forEach((source: SourceResultType) => 
				{
					if (source.sample) 
					{
						source.sample.forEach(d => allDomains.add(d));
					}
				});
				results.aggregated = {
					uniqueCount: allDomains.size,
					domains: Array.from(allDomains).slice(0, 100) as string[],
				};
			}

			logger.info({ results, msg: 'Top sources run completed' });
			return results;
		}
		catch (error) 
		{
			logger.error({ error, msg: 'Top sources run failed' });
			throw error;
		}
	}

	/**
	 * Trigger backfill operation
	 */
	async triggerBackfillRun(parameters?: BackfillParametersType): Promise<DiscoveryResultsType> 
	{
		try 
		{
			logger.info({ parameters, msg: 'Triggering backfill run' });

			const { BackfillEngine } = await import('../backfill/BackfillEngine');

			const sources = parameters?.sources || ['tranco'];
			const maxDomains = parameters?.maxDomains || 100000;
			const dryRun = parameters?.dryRun || false;

			const results: DiscoveryResultsType = {
				totalCandidates: 0,
				totalDiscovered: 0,
				totalEnqueued: 0,
				strategies: {},
			};

			// Process each source
			for (const source of sources)
			{
				const backfillEngine = new BackfillEngine(this.dbManager, logger);
				const candidates = await backfillEngine.getBackfillCandidates(source, maxDomains);
				const domains = await backfillEngine.processBackfillCandidates(candidates);

				results.totalCandidates += candidates.length;
				results.totalDiscovered += domains.length;

				if (!dryRun && domains.length > 0)
				{
					// Convert domains to the format expected by enqueuer
					const domainsForEnqueue = domains.map(domain => ({
						domain: domain,
						source: source,
						discoveryStrategy: 'backfill' as DiscoveryStrategyType,
						confidence: 0.7, // Medium confidence for backfill domains
						discoveryReason: `Backfill operation from ${source}`,
						metadata: { backfillSource: source }
					}));

					// Note: enqueuer would need to be passed to this method to actually enqueue
					// For now, we'll log the domains that would be enqueued
					logger.info({
						domainsCount: domains.length,
						source,
						msg: 'Backfill domains ready for enqueueing (enqueuer not available in this context)'
					});

					results.totalEnqueued += domains.length; // Assume all would be enqueued
				}

				results.strategies[source] = {
					candidates: candidates.length,
					discovered: domains.length,
					enqueued: dryRun ? 0 : domains.length,
				};
			}

			logger.info({ results, msg: 'Backfill run completed' });
			return results;
		}
		catch (error) 
		{
			logger.error({ error, msg: 'Backfill run failed' });
			throw error;
		}
	}

	/**
	 * Get candidates for a specific strategy
	 */
	private async getCandidatesForStrategy(
		strategy: DiscoveryStrategyType,
		sources: string[],
		limit: number
	): Promise<DomainCandidateType[]>
	{
		logger.debug({ strategy, sources, limit, msg: 'Getting candidates for strategy' });

		const candidates: DomainCandidateType[] = [];
		const limitPerSource = Math.ceil(limit / sources.length);

		try {
			// Process each source based on strategy requirements
			for (const source of sources) {
				const sourceCandidates = await this.getCandidatesFromSource(source, strategy, limitPerSource);
				candidates.push(...sourceCandidates);

				// Stop if we've reached the overall limit
				if (candidates.length >= limit) {
					break;
				}
			}

			// Trim to exact limit if we exceeded it
			const finalCandidates = candidates.slice(0, limit);

			logger.info({
				strategy,
				sources,
				requestedLimit: limit,
				actualCandidates: finalCandidates.length,
				msg: 'Candidates retrieved for strategy'
			});

			return finalCandidates;
		} catch (error) {
			logger.error({
				strategy,
				sources,
				limit,
				error: error instanceof Error ? error.message : String(error),
				msg: 'Failed to get candidates for strategy'
			});
			throw error;
		}
	}

	/**
	 * Get candidates from a specific source using the specified strategy
	 */
	private async getCandidatesFromSource(
		source: string,
		strategy: DiscoveryStrategyType,
		limit: number
	): Promise<DomainCandidateType[]>
	{
		logger.debug({ source, strategy, limit, msg: 'Getting candidates from source' });

		try {
			// Instantiate the appropriate connector
			let connector;

			switch (source.toLowerCase()) {
				case 'tranco':
					connector = new TrancoConnector({});
					break;

				case 'radar':
					connector = new RadarConnector({});
					break;

				case 'umbrella':
					connector = new UmbrellaConnector({});
					break;

				case 'commoncrawl':
					connector = new CommonCrawlConnector({});
					break;

				case 'sonar':
					connector = new SonarConnector({});
					break;

				case 'czds':
					connector = new CZDSConnector({});
					break;

				default:
					logger.warn({ source, msg: 'Unknown source, skipping' });
					return [];
			}

			// Check if connector supports the strategy
			if (!connector.supportsStrategy(strategy)) {
				logger.debug({
					source,
					strategy,
					msg: 'Source does not support strategy, skipping'
				});
				return [];
			}

			// Fetch domains using the connector
			const candidates: DomainCandidateType[] = [];
			const fetchOptions = {
				strategy,
				limit,
				offset: 0
			};

			logger.debug({ source, strategy, limit, msg: 'Fetching domains from connector' });

			// Collect candidates from the async iterator
			for await (const candidate of connector.fetchDomains(fetchOptions)) {
				candidates.push(candidate);

				// Stop if we've reached the limit for this source
				if (candidates.length >= limit) {
					break;
				}
			}

			logger.info({
				source,
				strategy,
				requestedLimit: limit,
				actualCandidates: candidates.length,
				msg: 'Candidates fetched from source'
			});

			return candidates;

		} catch (error) {
			logger.error({
				source,
				strategy,
				limit,
				error: error instanceof Error ? error.message : String(error),
				msg: 'Failed to get candidates from source'
			});

			// Return empty array instead of throwing to allow other sources to continue
			return [];
		}
	}
}

export default DiscoveryOperations;
