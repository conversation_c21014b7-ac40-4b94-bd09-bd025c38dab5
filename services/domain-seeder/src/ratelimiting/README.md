# Rate Limiting and Backpressure System

This module implements a comprehensive rate limiting and backpressure control system for the domain-seeder service. It provides distributed token bucket rate limiting with Redis-backed state and intelligent backpressure detection based on multiple system metrics.

## Components

### TokenBucket

A distributed token bucket rate limiter that uses Redis for state management across multiple service instances.

**Features:**

- Redis-backed distributed state
- Configurable capacity and refill rates
- Jitter support to prevent thundering herd
- Automatic token refilling with configurable intervals
- <PERSON><PERSON><PERSON> handling up to bucket capacity
- Graceful error handling and recovery

**Usage:**

```typescript
import { TokenBucket } from "./ratelimiting/TokenBucket";

const tokenBucket = new TokenBucket(redisClient, "my-bucket", {
  capacity: 1000,
  refillRate: 100, // tokens per second
  refillInterval: 1000, // milliseconds
  jitterRange: 100, // milliseconds
});

await tokenBucket.start();

// Try to consume tokens
if (await tokenBucket.consume(10)) {
  // Proceed with operation
} else {
  // Rate limited - wait or skip
}

// Wait for tokens to become available
const success = await tokenBucket.waitForTokens(10, 5000); // 5 second timeout
```

### ComprehensiveBackpressureController

An intelligent backpressure controller that monitors multiple system metrics and provides adaptive rate limiting with circuit breaker patterns.

**Features:**

- Multi-metric pressure calculation (queue depth, DB latency, memory usage, error rate)
- Adaptive rate limiting based on system performance
- Circuit breaker patterns for external service failures
- Configurable thresholds and recovery factors
- Real-time monitoring and metrics collection
- Distributed coordination across service instances

**Monitored Metrics:**

- **Queue Depth**: Redis stream lengths for domain queues
- **Database Latency**: Response times from Redis/Scylla/Maria/Manticore
- **Memory Usage**: Process memory consumption percentage
- **Error Rate**: Calculated from recent latency patterns

**Usage:**

```typescript
import { ComprehensiveBackpressureController } from "./ratelimiting/BackpressureController";

const controller = new ComprehensiveBackpressureController(
  redisClient,
  databaseManager,
  {
    queueDepthWarning: 100000,
    queueDepthCritical: 200000,
    dbLatencyWarning: 1000,
    dbLatencyCritical: 5000,
  },
  {
    baseRate: 1000,
    minRate: 100,
    maxRate: 5000,
  }
);

await controller.start();

// Check if system should throttle
if (await controller.shouldThrottle()) {
  // Wait for capacity or skip operation
  await controller.waitForCapacity();
}

// Record service failures for circuit breaker
await controller.recordServiceFailure("external-api");

// Record service success
await controller.recordServiceSuccess("external-api");
```

## Integration

The TokenBucket and BackpressureController work together to provide comprehensive rate limiting:

1. **TokenBucket** provides the actual rate limiting mechanism
2. **BackpressureController** provides intelligent throttling decisions based on system health
3. Both components coordinate to ensure system stability under load

**Typical Integration Pattern:**

```typescript
// Check backpressure first
if (await backpressureController.shouldThrottle()) {
  await backpressureController.waitForCapacity();
}

// Then check token availability
if (await tokenBucket.consume(requestCost)) {
  // Proceed with operation
} else {
  // Wait for tokens or defer operation
  await tokenBucket.waitForTokens(requestCost, maxWaitTime);
}
```

## Configuration

### TokenBucket Configuration

```typescript
interface TokenBucketConfig {
  capacity: number; // Maximum tokens in bucket
  refillRate: number; // Tokens added per second
  refillInterval: number; // Refill check interval (ms)
  jitterRange: number; // Random jitter range (ms)
  keyPrefix: string; // Redis key prefix
}
```

### BackpressureController Configuration

```typescript
interface BackpressureThresholds {
  queueDepthWarning: number; // Queue depth warning threshold
  queueDepthCritical: number; // Queue depth critical threshold
  dbLatencyWarning: number; // DB latency warning (ms)
  dbLatencyCritical: number; // DB latency critical (ms)
  memoryUsageWarning: number; // Memory usage warning (%)
  memoryUsageCritical: number; // Memory usage critical (%)
  errorRateWarning: number; // Error rate warning (%)
  errorRateCritical: number; // Error rate critical (%)
}

interface AdaptiveRateLimitConfig {
  baseRate: number; // Base rate limit
  minRate: number; // Minimum rate limit
  maxRate: number; // Maximum rate limit
  adjustmentFactor: number; // Rate reduction factor (0-1)
  recoveryFactor: number; // Rate recovery factor (>1)
}
```

## Monitoring and Metrics

The system provides comprehensive monitoring capabilities:

- **Token consumption rates** and bucket state
- **Backpressure levels** and pressure indicators
- **Circuit breaker states** and failure counts
- **Adaptive rate limit** adjustments
- **System performance metrics** (latency, memory, errors)

## Error Handling

Both components implement robust error handling:

- **Redis failures**: Graceful degradation with local fallbacks
- **Network partitions**: Automatic reconnection and state recovery
- **Service failures**: Circuit breaker protection with exponential backoff
- **Memory pressure**: Automatic rate limit adjustment
- **Configuration errors**: Validation and safe defaults

## Testing

Comprehensive test suites are provided:

- **Unit tests**: Individual component functionality
- **Integration tests**: Component interaction and coordination
- **Property-based tests**: Edge cases and invariant checking
- **Load tests**: Performance under high concurrency
- **Failure tests**: Behavior under various failure scenarios

## Performance Characteristics

- **Token bucket operations**: O(1) with Redis Lua scripts
- **Backpressure calculation**: O(1) with cached metrics
- **Memory usage**: Bounded history with configurable limits
- **Network overhead**: Minimal with batched operations
- **Latency impact**: Sub-millisecond for normal operations

## Requirements Satisfied

This implementation satisfies the following requirements:

- **4.1**: Strict backpressure controls with configurable limits
- **4.2**: Rate limiting with distributed token bucket implementation
- **4.4**: Backpressure detection with multiple pressure indicators
- **4.5**: Comprehensive error handling and recovery mechanisms

The system provides production-ready rate limiting and backpressure control suitable for high-throughput domain discovery operations while maintaining system stability and preventing infrastructure overload.
