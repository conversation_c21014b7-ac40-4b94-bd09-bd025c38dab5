import type { RedisClientWrapper } from '@shared';
import { logger } from '@shared';

type TokenBucketConfigType =
{
	capacity: number;
	refillRate: number; // tokens per second
	refillInterval: number; // milliseconds
	jitterRange: number; // milliseconds for jitter
	keyPrefix: string;
};

type TokenBucketStateType =
{
	tokens: number;
	lastRefill: number;
};

class TokenBucket
{
	private readonly config: TokenBucketConfigType;

	private readonly redis: RedisClientWrapper;

	private readonly bucketKey: string;

	private refillTimer?: NodeJS.Timeout;

	private isRunning: boolean = false;

	private readonly logger = logger.getLogger('TokenBucket');

	constructor(
		redis: RedisClientWrapper,
		bucketId: string,
		config: Partial<TokenBucketConfigType> = {},
	)
	{
		this.redis = redis;
		this.bucketKey = `${config.keyPrefix || 'token_bucket'}:${bucketId}`;

		this.config = {
			capacity: config.capacity || 1000,
			refillRate: config.refillRate || 100, // 100 tokens per second
			refillInterval: config.refillInterval || 1000, // 1 second
			jitterRange: config.jitterRange || 100, // 100ms jitter
			keyPrefix: config.keyPrefix || 'token_bucket',
		};


		this.logger.info('TokenBucket initialized', {
			bucketId,
			config: this.config,
		});
	}

	async start(): Promise<void>
	{
		if (this.isRunning)
		{
			return;
		}

		this.isRunning = true;
		await this.initializeBucket();
		this.startRefillTimer();

		this.logger.info('TokenBucket started', { bucketKey: this.bucketKey });
	}

	async stop(): Promise<void>
	{
		this.isRunning = false;

		if (this.refillTimer)
		{
			clearInterval(this.refillTimer);
			this.refillTimer = undefined;
		}

		this.logger.info('TokenBucket stopped', { bucketKey: this.bucketKey });
	}

	async consume(tokens: number = 1): Promise<boolean>
	{
		if (!this.isRunning)
		{
			throw new Error('TokenBucket not started');
		}

		if (tokens <= 0)
		{
			throw new Error('Token count must be positive');
		}

		if (tokens > this.config.capacity)
		{
			throw new Error(`Requested tokens (${tokens}) exceeds bucket capacity (${this.config.capacity})`);
		}

		const luaScript = `
			local key = KEYS[1]
			local tokens_requested = tonumber(ARGV[1])
			local capacity = tonumber(ARGV[2])
			local now = tonumber(ARGV[3])

			local bucket = redis.call('HMGET', key, 'tokens', 'lastRefill')
			local current_tokens = tonumber(bucket[1]) or capacity
			local last_refill = tonumber(bucket[2]) or now

			if current_tokens >= tokens_requested then
				local new_tokens = current_tokens - tokens_requested
				redis.call('HMSET', key, 'tokens', new_tokens, 'lastRefill', last_refill)
				redis.call('EXPIRE', key, 3600)
				return {1, new_tokens}
			else
				return {0, current_tokens}
			end
		`;

		const result = await this.redis.eval(
			luaScript,
			1,
			this.bucketKey,
			tokens.toString(),
			this.config.capacity.toString(),
			Date.now().toString(),
		) as [number, number];

		const [success, remainingTokens] = result;
		const consumed = success === 1;

		this.logger.debug('Token consumption attempt', {
			bucketKey: this.bucketKey,
			tokensRequested: tokens,
			consumed,
			remainingTokens,
		});

		return consumed;
	}

	async waitForTokens(tokens: number = 1, maxWaitMs: number = 30000): Promise<boolean>
	{
		const startTime = Date.now();

		while (Date.now() - startTime < maxWaitMs)
		{
			if (await this.consume(tokens))
			{
				return true;
			}

			// Add jitter to prevent thundering herd
			const jitter = Math.random() * this.config.jitterRange;
			const waitTime = Math.min(1000, maxWaitMs - (Date.now() - startTime)) + jitter;

			await this.sleep(waitTime);
		}

		this.logger.warn('Token wait timeout', {
			bucketKey: this.bucketKey,
			tokensRequested: tokens,
			maxWaitMs,
		});

		return false;
	}

	async getState(): Promise<TokenBucketStateType>
	{
		const result = await this.redis.hmget(this.bucketKey, 'tokens', 'lastRefill');

		return {
			tokens: parseInt(result[0] || this.config.capacity.toString(), 10),
			lastRefill: parseInt(result[1] || Date.now().toString(), 10),
		};
	}

	async reset(): Promise<void>
	{
		await this.redis.del(this.bucketKey);
		await this.initializeBucket();

		this.logger.info('TokenBucket reset', { bucketKey: this.bucketKey });
	}

	async getCapacity(): Promise<number>
	{
		return this.config.capacity;
	}

	async getRefillRate(): Promise<number>
	{
		return this.config.refillRate;
	}

	private async initializeBucket(): Promise<void>
	{
		const exists = await this.redis.exists(this.bucketKey);

		if (!exists)
		{
			await this.redis.hmset(
				this.bucketKey,
				'tokens',
				this.config.capacity.toString(),
				'lastRefill',
				Date.now().toString(),
			);
			await this.redis.expire(this.bucketKey, 3600); // 1 hour TTL
		}
	}

	private startRefillTimer(): void
	{
		this.refillTimer = setInterval(async () =>
		{
			try
			{
				await this.refillTokens();
			}
			catch (error)
			{
				const errorMessage = error instanceof Error ? error.message : 'Unknown error';
				this.logger.error('Token refill error', {
					bucketKey: this.bucketKey,
					error: errorMessage,
				});
			}
		}, this.config.refillInterval);
	}

	private async refillTokens(): Promise<void>
	{
		const luaScript = `
			local key = KEYS[1]
			local capacity = tonumber(ARGV[1])
			local refill_rate = tonumber(ARGV[2])
			local refill_interval = tonumber(ARGV[3])
			local now = tonumber(ARGV[4])

			local bucket = redis.call('HMGET', key, 'tokens', 'lastRefill')
			local current_tokens = tonumber(bucket[1]) or capacity
			local last_refill = tonumber(bucket[2]) or now

			local time_elapsed = now - last_refill
			local tokens_to_add = math.floor((time_elapsed / 1000) * refill_rate)

			if tokens_to_add > 0 then
				local new_tokens = math.min(capacity, current_tokens + tokens_to_add)
				redis.call('HMSET', key, 'tokens', new_tokens, 'lastRefill', now)
				redis.call('EXPIRE', key, 3600)
				return {new_tokens, tokens_to_add}
			else
				return {current_tokens, 0}
			end
		`;

		const result = await this.redis.eval(
			luaScript,
			1,
			this.bucketKey,
			this.config.capacity.toString(),
			this.config.refillRate.toString(),
			this.config.refillInterval.toString(),
			Date.now().toString(),
		) as [number, number];

		const [newTokens, tokensAdded] = result;

		if (tokensAdded > 0)
		{
			this.logger.debug('Tokens refilled', {
				bucketKey: this.bucketKey,
				tokensAdded,
				newTokens,
			});
		}
	}

	private sleep(ms: number): Promise<void>
	{
		// eslint-disable-next-line no-promise-executor-return
		return new Promise(resolve => setTimeout(resolve, ms));
	}
}

export type {
	TokenBucketConfigType,
	TokenBucketStateType,
};

export default TokenBucket;
