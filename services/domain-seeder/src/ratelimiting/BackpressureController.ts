import type { RedisClientWrapper, DatabaseManager } from '@shared';
import { logger } from '@shared';
// import type { BackpressureController } from '../interfaces/ReliabilityManager';

type BackpressureMetricsType =
{
	queueDepth: number;
	dbLatency: number;
	memoryUsage: number;
	errorRate: number;
	timestamp: number;
	responseTime?: number;
};

type BackpressureThresholdsType =
{
	queueDepthWarning: number;
	queueDepthCritical: number;
	dbLatencyWarning: number; // milliseconds
	dbLatencyCritical: number; // milliseconds
	memoryUsageWarning: number; // percentage
	memoryUsageCritical: number; // percentage
	errorRateWarning: number; // percentage
	errorRateCritical: number; // percentage
};

type CircuitBreakerStateType =
{
	isOpen: boolean;
	failureCount: number;
	lastFailureTime: number;
	nextRetryTime: number;
};

type AdaptiveRateLimitConfigType =
{
	baseRate: number;
	minRate: number;
	maxRate: number;
	adjustmentFactor: number;
	recoveryFactor: number;
};

class ComprehensiveBackpressureController
{
	private readonly redis: RedisClientWrapper;

	private readonly dbManager: DatabaseManager;

	private readonly thresholds: BackpressureThresholdsType;

	private readonly adaptiveConfig: AdaptiveRateLimitConfigType;

	private readonly metricsHistory: BackpressureMetricsType[] = [];

	private readonly maxHistorySize: number = 100;

	private readonly circuitBreakers: Map<string, CircuitBreakerStateType> = new Map();

	private currentRateLimit: number;

	private isMonitoring: boolean = false;

	private monitoringInterval?: NodeJS.Timeout;

	private readonly logger: any;

	constructor(
		redis: RedisClientWrapper,
		dbManager: DatabaseManager,
		thresholds: Partial<BackpressureThresholdsType> = {},
		adaptiveConfig: Partial<AdaptiveRateLimitConfigType> = {},
	)
	{
		this.redis = redis;
		this.dbManager = dbManager;

		this.thresholds =
		{
			queueDepthWarning: thresholds.queueDepthWarning || 100000,
			queueDepthCritical: thresholds.queueDepthCritical || 200000,
			dbLatencyWarning: thresholds.dbLatencyWarning || 1000,
			dbLatencyCritical: thresholds.dbLatencyCritical || 5000,
			memoryUsageWarning: thresholds.memoryUsageWarning || 80,
			memoryUsageCritical: thresholds.memoryUsageCritical || 90,
			errorRateWarning: thresholds.errorRateWarning || 5,
			errorRateCritical: thresholds.errorRateCritical || 15,
		};

		this.adaptiveConfig =
		{
			baseRate: adaptiveConfig.baseRate || 1000,
			minRate: adaptiveConfig.minRate || 100,
			maxRate: adaptiveConfig.maxRate || 5000,
			adjustmentFactor: adaptiveConfig.adjustmentFactor || 0.5,
			recoveryFactor: adaptiveConfig.recoveryFactor || 1.1,
		};

		this.currentRateLimit = this.adaptiveConfig.baseRate;
		this.logger = logger.getLogger('BackpressureController');

		this.logger.info('BackpressureController initialized', {
			thresholds: this.thresholds,
			adaptiveConfig: this.adaptiveConfig,
		});
	}

	async start(): Promise<void>
	{
		if (this.isMonitoring)
		{
			return;
		}

		this.isMonitoring = true;
		this.startMonitoring();

		this.logger.info('BackpressureController started');
	}

	async stop(): Promise<void>
	{
		this.isMonitoring = false;

		if (this.monitoringInterval)
		{
			clearInterval(this.monitoringInterval);
			this.monitoringInterval = undefined;
		}

		this.logger.info('BackpressureController stopped');
	}

	async shouldThrottle(): Promise<boolean>
	{
		const metrics = await this.collectCurrentMetrics();
		const pressureLevel = this.calculatePressureLevel(metrics);

		// Check circuit breakers
		if (this.hasOpenCircuitBreakers())
		{
			this.logger.warn('Circuit breakers open, throttling enabled');
			return true;
		}

		// Determine if throttling is needed based on pressure level
		const shouldThrottle = pressureLevel >= 0.7; // Throttle at 70% pressure

		if (shouldThrottle)
		{
			this.logger.warn('Backpressure detected, throttling enabled', {
				pressureLevel,
				metrics,
			});
		}

		return shouldThrottle;
	}

	async getQueueDepth(): Promise<number>
	{
		try
		{
			// Check multiple queue depths
			const [newDomainsDepth, highPriorityDepth] = await Promise.all([
				this.redis.xlen('stream:new:domains'),
				this.redis.xlen('stream:new:domains:high'),
			]);

			return newDomainsDepth + highPriorityDepth;
		}
		catch (error)
		{
			this.logger.error('Failed to get queue depth', { error: error instanceof Error ? error.message : String(error) });
			return 0;
		}
	}

	async waitForCapacity(): Promise<void>
	{
		const maxWaitTime = 30000; // 30 seconds
		const checkInterval = 1000; // 1 second
		const startTime = Date.now();

		while (Date.now() - startTime < maxWaitTime)
		{
			if (!(await this.shouldThrottle()))
			{
				return;
			}

			this.logger.debug('Waiting for capacity', {
				waitTime: Date.now() - startTime,
			});

			await this.sleep(checkInterval);
		}

		this.logger.warn('Capacity wait timeout exceeded');
	}

	updateMetrics(queueDepth: number, latency: number): void
	{
		const metrics: BackpressureMetricsType =
		{
			queueDepth,
			dbLatency: latency,
			memoryUsage: this.getMemoryUsagePercentage(),
			errorRate: this.calculateRecentErrorRate(),
			timestamp: Date.now(),
		};

		this.metricsHistory.push(metrics);

		// Keep history size manageable
		if (this.metricsHistory.length > this.maxHistorySize)
		{
			this.metricsHistory.shift();
		}

		// Update adaptive rate limiting
		this.updateAdaptiveRateLimit(metrics);
	}

	async getAdaptiveRateLimit(): Promise<number>
	{
		return this.currentRateLimit;
	}

	async recordServiceFailure(serviceName: string): Promise<void>
	{
		const circuitBreaker = this.getOrCreateCircuitBreaker(serviceName);
		circuitBreaker.failureCount++;
		circuitBreaker.lastFailureTime = Date.now();

		// Open circuit breaker if failure threshold exceeded
		if (circuitBreaker.failureCount >= 5)
		{
			circuitBreaker.isOpen = true;
			circuitBreaker.nextRetryTime = Date.now() + (30000 * 2 ** Math.min(circuitBreaker.failureCount - 5, 5)); // Exponential backoff

			this.logger.warn('Circuit breaker opened', {
				serviceName,
				failureCount: this.circuitBreakers.get(serviceName)?.failureCount,
				nextRetryTime: new Date(circuitBreaker.nextRetryTime).toISOString(),
			});
		}
	}

	async recordServiceSuccess(serviceName: string): Promise<void>
	{
		const circuitBreaker = this.circuitBreakers.get(serviceName);

		if (circuitBreaker)
		{
			// Reset failure count on success
			circuitBreaker.failureCount = Math.max(0, circuitBreaker.failureCount - 1);

			// Close circuit breaker if it was open and we're past retry time
			if (circuitBreaker.isOpen && Date.now() >= circuitBreaker.nextRetryTime)
			{
				circuitBreaker.isOpen = false;
				this.logger.info('Circuit breaker closed', { serviceName });
			}
		}
	}

	async isServiceAvailable(serviceName: string): Promise<boolean>
	{
		const circuitBreaker = this.circuitBreakers.get(serviceName);

		if (!circuitBreaker || !circuitBreaker.isOpen)
		{
			return true;
		}

		// Check if we can retry
		if (Date.now() >= circuitBreaker.nextRetryTime)
		{
			this.logger.info('Circuit breaker retry attempt', { serviceName });
			return true;
		}

		return false;
	}

	private async collectCurrentMetrics(): Promise<BackpressureMetricsType>
	{
		const [queueDepth, dbLatency] = await Promise.all([
			this.getQueueDepth(),
			this.measureDatabaseLatency(),
		]);

		return {
			queueDepth,
			dbLatency,
			memoryUsage: this.getMemoryUsagePercentage(),
			errorRate: this.calculateRecentErrorRate(),
			timestamp: Date.now(),
		};
	}

	private calculatePressureLevel(metrics: BackpressureMetricsType): number
	{
		let pressure = 0;

		// Queue depth pressure (0-0.4)
		if (metrics.queueDepth >= this.thresholds.queueDepthCritical)
		{
			pressure += 0.4;
		}
		else if (metrics.queueDepth >= this.thresholds.queueDepthWarning)
		{
			pressure += 0.2 * (metrics.queueDepth / this.thresholds.queueDepthWarning);
		}

		// Database latency pressure (0-0.3)
		if (metrics.dbLatency >= this.thresholds.dbLatencyCritical)
		{
			pressure += 0.3;
		}
		else if (metrics.dbLatency >= this.thresholds.dbLatencyWarning)
		{
			pressure += 0.15 * (metrics.dbLatency / this.thresholds.dbLatencyWarning);
		}

		// Memory usage pressure (0-0.2)
		if (metrics.memoryUsage >= this.thresholds.memoryUsageCritical)
		{
			pressure += 0.2;
		}
		else if (metrics.memoryUsage >= this.thresholds.memoryUsageWarning)
		{
			pressure += 0.1 * (metrics.memoryUsage / this.thresholds.memoryUsageWarning);
		}

		// Error rate pressure (0-0.1)
		if (metrics.errorRate >= this.thresholds.errorRateCritical)
		{
			pressure += 0.1;
		}
		else if (metrics.errorRate >= this.thresholds.errorRateWarning)
		{
			pressure += 0.05 * (metrics.errorRate / this.thresholds.errorRateWarning);
		}

		return Math.min(1.0, pressure);
	}

	private async measureDatabaseLatency(): Promise<number>
	{
		const startTime = Date.now();

		try
		{
			// Simple ping to measure latency
			await this.redis.ping();
			return Date.now() - startTime;
		}
		catch (error)
		{
			this.logger.error('Database latency measurement failed', { error: error instanceof Error ? error.message : String(error) });
			return 5000; // Return high latency on error
		}
	}

	private getMemoryUsagePercentage(): number
	{
		const memUsage = process.memoryUsage();
		const totalMemory = memUsage.heapTotal + memUsage.external;
		const usedMemory = memUsage.heapUsed;

		return (usedMemory / totalMemory) * 100;
	}

	private calculateRecentErrorRate(): number
	{
		const recentMetrics = this.metricsHistory.slice(-10); // Last 10 measurements

		if (recentMetrics.length === 0)
		{
			return 0;
		}

		// Calculate comprehensive error rate from multiple indicators
		let errorScore = 0;
		let totalWeight = 0;

		// Factor 1: Database latency (weight: 40%)
		const avgLatency = recentMetrics.reduce((sum, m) => sum + m.dbLatency, 0) / recentMetrics.length;
		const latencyErrorRate = Math.min((avgLatency - this.thresholds.dbLatencyWarning)
			/ (this.thresholds.dbLatencyCritical - this.thresholds.dbLatencyWarning), 1) * 100;
		errorScore += Math.max(latencyErrorRate, 0) * 0.4;
		totalWeight += 0.4;

		// Factor 2: Queue depth (weight: 30%)
		const avgQueueDepth = recentMetrics.reduce((sum, m) => sum + m.queueDepth, 0) / recentMetrics.length;
		const queueErrorRate = Math.min(avgQueueDepth / this.thresholds.queueDepthCritical, 1) * 100;
		errorScore += queueErrorRate * 0.3;
		totalWeight += 0.3;

		// Factor 3: Memory pressure (weight: 20%)
		const avgMemoryUsage = recentMetrics.reduce((sum, m) => sum + m.memoryUsage, 0) / recentMetrics.length;
		const memoryErrorRate = Math.max((avgMemoryUsage - 70) / 20, 0) * 100; // Error rate increases after 70% memory usage
		errorScore += Math.min(memoryErrorRate, 100) * 0.2;
		totalWeight += 0.2;

		// Factor 4: Processing time variance (weight: 10%)
		const processingTimes = recentMetrics.map(m => m.responseTime || 0);
		const avgProcessingTime = processingTimes.reduce((sum, t) => sum + t, 0) / processingTimes.length;
		const variance = processingTimes.reduce((sum, t) => sum + (t - avgProcessingTime) ** 2, 0) / processingTimes.length;
		const varianceErrorRate = Math.min(Math.sqrt(variance) / avgProcessingTime, 1) * 100;
		errorScore += varianceErrorRate * 0.1;
		totalWeight += 0.1;

		return totalWeight > 0 ? Math.min(errorScore / totalWeight, 100) : 0;
	}

	private updateAdaptiveRateLimit(metrics: BackpressureMetricsType): void
	{
		const pressureLevel = this.calculatePressureLevel(metrics);

		if (pressureLevel > 0.8)
		{
			// High pressure - reduce rate limit
			this.currentRateLimit = Math.max(
				this.adaptiveConfig.minRate,
				this.currentRateLimit * this.adaptiveConfig.adjustmentFactor,
			);
		}
		else if (pressureLevel < 0.3)
		{
			// Low pressure - increase rate limit
			this.currentRateLimit = Math.min(
				this.adaptiveConfig.maxRate,
				this.currentRateLimit * this.adaptiveConfig.recoveryFactor,
			);
		}

		this.logger.debug('Adaptive rate limit updated', {
			pressureLevel,
			currentRateLimit: this.currentRateLimit,
		});
	}

	private hasOpenCircuitBreakers(): boolean
	{
		for (const [,breaker] of this.circuitBreakers)
		{
			if (breaker.isOpen && Date.now() < breaker.nextRetryTime)
			{
				return true;
			}
		}

		return false;
	}

	private getOrCreateCircuitBreaker(serviceName: string): CircuitBreakerStateType
	{
		if (!this.circuitBreakers.has(serviceName))
		{
			this.circuitBreakers.set(serviceName, {
				isOpen: false,
				failureCount: 0,
				lastFailureTime: 0,
				nextRetryTime: 0,
			});
		}

		return this.circuitBreakers.get(serviceName)!;
	}

	private startMonitoring(): void
	{
		this.monitoringInterval = setInterval(async () =>
		{
			try
			{
				const metrics = await this.collectCurrentMetrics();
				this.updateMetrics(metrics.queueDepth, metrics.dbLatency);
			}
			catch (error)
			{
				this.logger.error('Monitoring error', { error: error instanceof Error ? error.message : String(error) });
			}
		}, 5000); // Monitor every 5 seconds
	}

	private sleep(ms: number): Promise<void>
	{
		// eslint-disable-next-line no-promise-executor-return
		return new Promise(resolve => setTimeout(resolve, ms));
	}
}

export type {
	BackpressureMetricsType,
	BackpressureThresholdsType,
	AdaptiveRateLimitConfigType,
};

export default ComprehensiveBackpressureController;
