/* eslint-disable max-classes-per-file */

/*
Single-file Phase 1 content creator (demo).
Constraints: offline-friendly by default. If env vars for APIs are present, it will use them.
- Gathers a small domain seed set (from data/brand-seeds.json + tlds) and builds prompts
- Optionally fetches public pages (DISABLED BY DEFAULT). Enable with ALLOW_HTTP=1
- Calls LLM provider (OpenRouter or Gemini) if API keys are present
- Writes JSON outputs to data/initial_p1_domain_data/{slug}.json
*/

import fs from 'node:fs';
import path from 'node:path';
import {
	Worker,
	isMainThread,
	workerData,
	parentPort,
} from 'node:worker_threads';
import { fileURLToPath } from 'node:url';

import { curly } from 'node-libcurl';
import { logger } from '@shared';
import DomainAnalyzer from '../services/DomainAnalyzer';

// DUAL-ENVIRONMENT PATH RESOLUTION
// Problem: <PERSON>ript needs to work in both dev (TypeScript with tsx) and prod (compiled JS with node)
// - Dev: tsx runs TS directly, no import.meta available, uses CommonJS-style paths
// - Prod: Compiled JS runs as ESM, import.meta.url available
// Solution: Detect environment and use appropriate path resolution method
const getCurrentFilePath = () =>
{
	// In ESM (production), use import.meta.url
	if (typeof import.meta !== 'undefined' && import.meta.url)
	{
		return fileURLToPath(import.meta.url);
	}
	// In CommonJS (dev with tsx), construct path
	return path.join(process.cwd(), 'src/scripts/examplePhaseOneContentCreator.ts');
};

const __filename = getCurrentFilePath();
const __dirname = path.dirname(__filename);

// Config
const ALLOW_HTTP = process.env.ALLOW_HTTP === '1';
const FETCH_HTTP_PROXY = process.env.FETCH_HTTP_PROXY || '';

// REPOSITORY PATH RESOLUTION
// Problem: Script runs from domain-seeder service directory but needs access to repo root data
// Working directory: services/domain-seeder
// Target data path: data/brand-seeds.json
// Solution: Navigate up 2 levels from service directory to reach repo root
const repoRoot = path.resolve(process.cwd(), '../..');
const dataDir = path.join(repoRoot, 'data');
const brandSeedsPath = path.join(dataDir, 'brand-seeds.json');
const outDir = path.join(repoRoot, 'data', 'initial_p1_domain_data');
const REQUIREMENTS_PATH = process.env.P1_REQUIREMENTS || path.join(process.cwd(), 'src/scripts/requirements.json');

// Helpers
function readJson<T>(p: string): T
{
	return JSON.parse(fs.readFileSync(p, 'utf8')) as T;
}

function ensureDir(p: string)
{
	fs.mkdirSync(p, { recursive: true });
}

// fetchMaybe removed - now using DomainAnalyzer service for web data

type Brand = { name: string; parent: string | null; category_primary: string; tags: string[] };
type Brands = Record<string, Brand>;

type P1Content =
{
	version: number;
	domain: string;
	brandSlug: string;
	brandName: string;
	categoryPrimary: string;
	tags: string[];
	fetched?: { url: string; contentSnippet: string }[];
	generated: {
		summary: string;
		keyFeatures: string[];
		audience: string[];
		useCases: string[];
		keywords: string[];
	};
	createdAt: string;
	source: string;
	model: string;
};

// Initialize domain analyzer instance
const domainAnalyzer = new DomainAnalyzer();

function buildBrandInfo(brand: Brand): string
{
	const info = [];
	info.push(`Brand: ${brand.name}`);
	info.push(`Primary Category: ${brand.category_primary}`);
	if (brand.parent)
	{
		info.push(`Parent Company: ${brand.parent}`);
	}
	if (brand.tags.length > 0)
	{
		info.push(`Tags: ${brand.tags.join(', ')}`);
	}
	return info.join('\n');
}

type Provider = {
	type: string;
	model: string;
	api_key: string;
	proxy?: string;
};

type Requirements = {
	execution?: { limit?: number; concurrency?: number };
	system_prompt: string;
	providers: Record<string, Provider>;
	user_template: string;
	config?: { default_provider?: string };
};

function loadRequirements(): Requirements
{
	const p = REQUIREMENTS_PATH;
	if (!fs.existsSync(p)) throw new Error(`requirements.json not found at ${p}`);
	return JSON.parse(fs.readFileSync(p, 'utf8')) as Requirements;
}


function renderTemplate(tpl: string, ctx: Record<string, string>): string
{
	// Handle simple {{variable}} replacements
	let result = tpl.replace(/\{\{(.*?)\}\}/g, (_, k) => ctx[k.trim()] ?? '');

	// Handle {{#if context}} conditionals - simple implementation
	result = result.replace(/\{\{#if\s+(\w+)\}\}(.*?)\{\{\/if\}\}/gs, (_, condition, content) => (ctx[condition] ? content : ''));

	return result;
}

type LLMResponse = {
	overview: {
		summary: string;
	};
	metadata: {
		category: {
			primary: string;
			secondary: string;
		};
		tags: string[];
	};
};

interface ProviderHandler
{
	call(provider: Provider, systemPrompt: string, userPrompt: string): Promise<LLMResponse>;
}

class OpenRouterHandler implements ProviderHandler
{
	async call(provider: Provider, systemPrompt: string, userPrompt: string): Promise<LLMResponse>
	{
		const url = 'https://api.openrouter.ai/v1/chat/completions';
		const headers = [
			'Content-Type: application/json',
			`Authorization: Bearer ${provider.api_key}`,
		];
		const body = JSON.stringify({
			model: provider.model,
			messages: [
				{ role: 'system', content: systemPrompt },
				{ role: 'user', content: userPrompt },
			],
			response_format: { type: 'json_object' },
		});
		const opts: any = { POSTFIELDS: body, httpHeader: headers, FOLLOWLOCATION: true };
		if (provider.proxy) opts.PROXY = provider.proxy;

		const { data, statusCode } = await curly.post(url, opts);
		if (statusCode >= 200 && statusCode < 300)
		{
			const json = JSON.parse(String(data));
			const content = json?.choices?.[0]?.message?.content;
			if (content)
			{
				try
				{
					return JSON.parse(content);
				}
				catch (e)
				{
					throw new Error(`Invalid JSON response from OpenRouter: ${content}`);
				}
			}
		}
		throw new Error(`OpenRouter API error: ${statusCode}`);
	}
}

class GeminiHandler implements ProviderHandler
{
	async call(provider: Provider, systemPrompt: string, userPrompt: string): Promise<LLMResponse>
	{
		const url = `https://generativelanguage.googleapis.com/v1beta/models/${provider.model}:generateContent?key=${provider.api_key}`;
		const headers = ['Content-Type: application/json'];
		const body = JSON.stringify({
			contents: [{ role: 'user', parts: [{ text: `${systemPrompt}\n\n${userPrompt}` }] }],
			generationConfig: { responseMimeType: 'application/json' },
		});
		const opts: any = { POSTFIELDS: body, httpHeader: headers, FOLLOWLOCATION: true };
		if (provider.proxy) opts.PROXY = provider.proxy;

		const { data, statusCode } = await curly.post(url, opts);
		if (statusCode >= 200 && statusCode < 300)
		{
			const json = JSON.parse(String(data));
			const text = json?.candidates?.[0]?.content?.parts?.[0]?.text;
			if (text)
			{
				try
				{
					return JSON.parse(text);
				}
				catch (e)
				{
					throw new Error(`Invalid JSON response from Gemini: ${text}`);
				}
			}
		}
		throw new Error(`Gemini API error: ${statusCode}`);
	}
}

const providerHandlers: Record<string, ProviderHandler> = {
	openrouter: new OpenRouterHandler(),
	gemini: new GeminiHandler(),
};

async function callLLM(provider: Provider, systemPrompt: string, userPrompt: string): Promise<LLMResponse>
{
	const handler = providerHandlers[provider.type];
	if (!handler)
	{
		throw new Error(`Unsupported provider type: ${provider.type}. Available: ${Object.keys(providerHandlers).join(', ')}`);
	}
	return handler.call(provider, systemPrompt, userPrompt);
}

async function runWorker(slugs: string[], provider: Provider, systemPrompt: string, userTemplate: string)
{
	for (const slug of slugs)
	{
		const brands = readJson<Brands>(brandSeedsPath);
		const brand = brands[slug];
		if (!brand) continue;
		const domain = `${slug}.com`;

		// Prepare enriched context for LLM using DomainAnalyzer service
		const allowWebFetch = process.env.ALLOW_HTTP === '1';
		const webProxy = provider.proxy; // Use proxy from requirements.json
		const analysis = await domainAnalyzer.analyzeDomain(domain, allowWebFetch, webProxy);
		const domainAnalysisText = domainAnalyzer.formatForLLM(analysis);
		const brandInfo = buildBrandInfo(brand);

		// Format web context from analyzer results
		let webContext = '';
		if (analysis.webData?.status === 'success')
		{
			const webInfo = [];
			if (analysis.webData.title) webInfo.push(`Title: ${analysis.webData.title}`);
			if (analysis.webData.description) webInfo.push(`Meta Description: ${analysis.webData.description}`);
			if (analysis.webData.content) webInfo.push(`Content Preview: ${analysis.webData.content.slice(0, 1000)}...`);
			webContext = webInfo.join('\n');
		}

		const userPrompt = renderTemplate(userTemplate, {
			domain,
			domain_analysis: domainAnalysisText,
			brand_info: brandInfo,
			web_context: webContext,
		});

		const gen = await callLLM(provider, systemPrompt, userPrompt);

		const record: P1Content = {
			version: 1,
			domain,
			brandSlug: slug,
			brandName: brand.name,
			categoryPrimary: gen.metadata.category.primary,
			tags: gen.metadata.tags,
			fetched: analysis.webData?.status === 'success' ? [{
				url: `https://www.${domain}`,
				contentSnippet: analysis.webData.content || '',
			}] : undefined,
			generated: gen,
			createdAt: new Date().toISOString(),
			source: provider.type,
			model: provider.model,
		};

		const outPath = path.join(outDir, `${slug}.json`);
		fs.writeFileSync(outPath, `${JSON.stringify(record, null, 2) }\n`, 'utf8');
		if (parentPort) parentPort.postMessage({ slug, outPath });
	}
}

async function main()
{
	if (!isMainThread)
	{
		const {
			slugs, provider, systemPrompt, userTemplate,
		} = (workerData || {}) as any;
		await runWorker(slugs as string[], provider, systemPrompt, userTemplate);
		process.exit(0);
	}

	ensureDir(outDir);
	const brands = readJson<Brands>(brandSeedsPath);
	const req = loadRequirements();

	// Get providers and validate
	const providerKeys = Object.keys(req.providers);
	if (!providerKeys.length) throw new Error('No providers configured');

	// Select provider (environment variable, config default, or first available)
	const selectedProviderId = process.env.P1_PROVIDER_ID || req.config?.default_provider || providerKeys[0];
	const provider = req.providers[selectedProviderId];
	if (!provider) throw new Error(`Provider not found: ${selectedProviderId}. Available: ${providerKeys.join(', ')}`);

	// Use common system prompt and user template
	const systemPrompt = req.system_prompt;
	const userTemplate = req.user_template;

	// Use execution config from requirements.json with environment variable overrides
	const executionLimit = Number(process.env.P1_LIMIT || req.execution?.limit || '100');
	const pool = Number(process.env.P1_CONCURRENCY || req.execution?.concurrency || '4');

	const scriptLogger = logger.getLogger('phase-one-content-creator');
	scriptLogger.info({ provider: selectedProviderId, type: provider.type, executionLimit, concurrency: pool }, 'Phase one content creation starting');

	const demoSlugs = Object.keys(brands).slice(0, executionLimit);

	const size = Math.ceil(demoSlugs.length / pool) || 1;
	const chunks = Array.from({ length: pool }, (_, i) => demoSlugs.slice(i * size, (i + 1) * size)).filter(c => c.length);

	// HYBRID WORKER STRATEGY FOR DEV/PROD COMPATIBILITY
	// Problem: Worker threads + native modules (node-libcurl) + tsx have compatibility issues
	// - Dev (TypeScript): tsx can't properly load TypeScript in workers, native modules fail in worker context
	// - Prod (compiled JS): Workers work perfectly with compiled JavaScript and native modules
	// Attempted solutions that failed:
	//   1. --import tsx/esm/api: Still had native module loading issues
	//   2. --loader tsx: Deprecated and didn't resolve native module issues
	//   3. eval with tsx register: Got further but node-libcurl failed to self-register
	// Final solution: Environment-adaptive execution strategy
	const isDevelopment = __filename.endsWith('.ts');

	if (isDevelopment)
	{
		scriptLogger.info('Running in development mode - sequential processing (native module limitation)');
		// Process chunks sequentially in development to avoid worker + native module issues
		for (const [idx, slugs] of chunks.entries())
		{
			try
			{
				scriptLogger.info({ chunk: idx, domainCount: slugs.length }, 'Processing domain chunk');
				await runWorker(slugs, provider, systemPrompt, userTemplate);
			}
			catch (error)
			{
				scriptLogger.error({ chunk: idx, error: error instanceof Error ? error.message : String(error) }, 'Error in chunk processing');
			}
		}
	}
	else
	{
		scriptLogger.info('Running in production mode - parallel processing');
		// Full worker parallelism in production (compiled JS + native modules work fine in workers)
		await Promise.all(chunks.map((slugs, idx) => new Promise<void>((resolve, reject) =>
		{
			const w = new Worker(__filename, {
				workerData: {
					slugs, provider, systemPrompt, userTemplate,
				},
			});
			w.on('message', (m) =>
			{
				if (m?.outPath) scriptLogger.info({ thread: idx, outPath: m.outPath }, 'Wrote output file');
			});
			w.once('error', reject);
			w.once('exit', code => (code === 0 ? resolve() : reject(new Error(`worker ${idx} exited ${code}`))));
		})));
	}

	scriptLogger.info('Done Phase 1 demo generation.');
}

main().catch((e) =>
{
	scriptLogger.error({ error: e instanceof Error ? e.message : String(e) }, 'Phase 1 generation failed');
	process.exit(1);
});
