{"execution": {"limit": 100, "concurrency": 4}, "system_prompt": "You are a domain content generator. Your goal is to produce high-quality, reader-friendly content for domains without making unverifiable claims. Rules: Be factual and brand-aligned, write in active voice, include domain/brand naturally, weave 2-4 relevant phrases, respect detected language, generate at least 320 words for descriptions.", "providers": {"openrouter_claude": {"type": "openrouter", "model": "openrouter/anthropic/claude-3.5-sonnet", "api_key": "YOUR_OPENROUTER_KEY", "proxy": ""}, "gemini_pro": {"type": "gemini", "model": "gemini-1.5-pro", "api_key": "YOUR_GEMINI_KEY", "proxy": ""}}, "user_template": "Generate comprehensive domain content for: {{domain}}\n\n{{#if domain_analysis}}Domain Analysis:\n{{domain_analysis}}{{/if}}\n\n{{#if brand_info}}Brand Information:\n{{brand_info}}{{/if}}\n\n{{#if web_context}}Web Context:\n{{web_context}}{{/if}}\n\nReturn ONLY valid JSON matching this schema:\n{\n  \"overview\": {\n    \"summary\": \"string (at least 320 words - detailed description for users and SEO)\"\n  },\n  \"metadata\": {\n    \"category\": {\n      \"primary\": \"string (main category)\",\n      \"secondary\": \"string (subcategory)\"\n    },\n    \"tags\": [\"string (5-12 normalized tags)\"]\n  }\n}\n\nRequirements:\n- summary: At least 320 words, first paragraph style, active voice\n- Include: brand/domain, offering, audience, benefits, geo if relevant\n- Categories: Choose appropriate primary and secondary categories\n- Tags: 5-12 relevant, normalized tags\n- Stay factual, avoid unverifiable claims\n- No extra keys beyond the schema", "config": {"default_provider": "openrouter_claude"}}