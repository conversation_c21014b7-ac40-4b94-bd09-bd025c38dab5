# examplePhaseOneContentCreator

Phase 1 content generator using requirements.json (providers + prompts), worker_threads, and node-libcurl with proxy support.

## Inputs

- data/brand-seeds.json: brand seeds
- services/domain-seeder/src/scripts/requirements.json: provider + prompt config (see example)

### requirements.json

```json
{
  "execution": {
    "limit": 100,
    "concurrency": 4
  },
  "system_prompt": "You are a Phase 1 domain content generator. Your goal is to produce high-quality, reader-friendly content for domains without making unverifiable claims. Rules: Be factual and brand-aligned, write in active voice, include domain/brand naturally, weave 2-4 relevant phrases, respect detected language, generate at least 320 words for descriptions.",
  "providers": {
    "openrouter_claude": {
      "type": "openrouter",
      "model": "openrouter/anthropic/claude-3.5-sonnet",
      "api_key": "YOUR_OPENROUTER_KEY",
      "proxy": ""
    },
    "gemini_pro": {
      "type": "gemini",
      "model": "gemini-1.5-pro",
      "api_key": "YOUR_GEMINI_KEY",
      "proxy": ""
    }
  },
  "user_template": "Generate Phase 1 domain content for: {{domain}}\n\n{{#if domain_analysis}}Domain Analysis:\n{{domain_analysis}}{{/if}}\n\n{{#if brand_info}}Brand Information:\n{{brand_info}}{{/if}}\n\n{{#if web_context}}Web Context:\n{{web_context}}{{/if}}\n\nReturn ONLY valid JSON matching this schema:\n{\n  \"overview\": {\n    \"summary\": \"string (at least 320 words - detailed description for users and SEO)\"\n  },\n  \"metadata\": {\n    \"category\": {\n      \"primary\": \"string (main category)\",\n      \"secondary\": \"string (subcategory)\"\n    },\n    \"tags\": [\"string (5-12 normalized tags)\"]\n  }\n}\n\nRequirements:\n- summary: At least 320 words, first paragraph style, active voice\n- Include: brand/domain, offering, audience, benefits, geo if relevant\n- Categories: Choose appropriate primary and secondary categories\n- Tags: 5-12 relevant, normalized tags\n- No unverifiable claims in preGenerated mode\n- No extra keys beyond the schema",
  "config": {
    "default_provider": "openrouter_claude"
  }
}
```

## Env Variables

- ALLOW_HTTP=1: Enable fetching `https://www.{slug}.com/` homepage
- FETCH_HTTP_PROXY: Proxy for web requests (node-libcurl PROXY)
- P1_REQUIREMENTS: Custom path to requirements.json (optional)
- P1_LIMIT: Override execution limit from config (default: 100)
- P1_CONCURRENCY: Override concurrency from config (default: 4)
- P1_PROVIDER_ID: Select specific provider by ID (overrides default_provider)

## How it works

- Main thread loads requirements.json and selects provider (P1_PROVIDER_ID → config.default_provider → first available)
- Uses execution limits and concurrency settings from config (overridable via env vars)
- Splits brand slugs into chunks; spawns worker_threads with enriched context data
- **Data Preparation Agent**: Each worker enriches LLM context before generation:
  - **Domain Analysis**: TLD analysis, pattern matching, structural heuristics
  - **Brand Information**: Name, category, parent company, tags from brand-seeds.json
  - **Web Context**: Optional homepage content (if ALLOW_HTTP=1)
- Template rendering with enriched variables: {{domain}}, {{domain_analysis}}, {{brand_info}}, {{web_context}}
- Common system prompt shared across all LLM providers
- Calls provider using object-oriented handler system (openrouter/gemini)
- **Phase 1 Output**: Generates proper schema with 320+ word summary, primary/secondary categories, 5-12 tags
- Strict JSON validation with detailed error messages for parsing failures
- Writes comprehensive JSON to data/initial_p1_domain_data/{slug}.json with metadata

## Provider Configuration

Providers are now configured as objects with unique IDs:

- **Provider IDs**: Use as keys in providers object (e.g., "openrouter_claude", "gemini_pro")
- **Provider Selection**: Via P1_PROVIDER_ID env var, config.default_provider, or first available
- **Extensible**: Easy to add new provider types by implementing ProviderHandler interface

## Domain Analysis Service

**DomainAnalyzer.ts** - Advanced domain intelligence service:

- **Comprehensive TLD Analysis**: Uses enhanced `data/tld-categories.json` with 20+ categories
- **Business Pattern Recognition**: 21 detailed pattern categories (e-commerce, healthcare, fintech, etc.)
- **Structural Analysis**: Domain length, character patterns, prefix/suffix analysis, readability metrics
- **Real Web Data Fetching**: Optional title, meta description, and content extraction
- **Confidence Scoring**: Algorithmic confidence assessment based on available signals

**Enhanced Data Preparation**:

- **Domain Intelligence**: Advanced heuristics beyond simple TLD mapping
- **Brand Context**: Structured brand information from brand-seeds.json
- **Web Context**: Real-time website data with proxy support and fallback mechanisms
- **Confidence Metrics**: Each analysis includes confidence percentage

**Template System**:

- **Rich Variables**: {{domain}}, {{domain_analysis}}, {{brand_info}}, {{web_context}}
- **Conditional Logic**: {{#if variable}}...{{/if}} for optional content sections
- **LLM-Optimized Format**: Structured data presentation for better AI comprehension

**Domain Content Generation**:

- **Factual Analysis**: No "Phase 1" references - LLM receives context-rich domain intelligence
- **320+ Word Summaries**: SEO-friendly, user-readable descriptions
- **Proper Schema**: Generates `overview.summary`, `metadata.category`, `metadata.tags`
- **Internet-Enhanced**: Optional real-time web data integration for accuracy

## Run

Option A (local tsx):

```
pnpm --filter domain-seeder add -D tsx
pnpm --filter domain-seeder tsx services/domain-seeder/src/scripts/examplePhaseOneContentCreator.ts
```

Option B (global tsx):

```
node --loader $(command -v tsx | sed 's#/bin/tsx#/lib/node_modules/tsx/dist/loader.mjs#') services/domain-seeder/src/scripts/examplePhaseOneContentCreator.ts
```

## Examples

```bash
# Use specific provider
P1_PROVIDER_ID=gemini_pro tsx examplePhaseOneContentCreator.ts

# Override execution limits
P1_LIMIT=50 P1_CONCURRENCY=2 tsx examplePhaseOneContentCreator.ts

# Enable web fetching with proxy
ALLOW_HTTP=1 FETCH_HTTP_PROXY=http://proxy:8080 tsx examplePhaseOneContentCreator.ts
```

## Notes

- Proxies are separated: provider.proxy for AI calls; FETCH_HTTP_PROXY for web fetches
- node-libcurl is installed in services/domain-seeder package; faster and proxy-friendly
- Object-oriented provider system makes adding new LLM providers straightforward
- Execution config in requirements.json provides better defaults than environment-only configuration
- Enhanced error handling with specific provider and parsing error messages
- Development mode runs sequentially to avoid worker thread + native module compatibility issues
- Production mode (compiled JS) runs with full worker parallelism for optimal performance
