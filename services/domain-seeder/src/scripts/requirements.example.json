{"providers": [{"type": "openrouter", "model": "openrouter/anthropic/claude-3.5-sonnet", "api_key": "YOUR_OPENROUTER_KEY", "proxy": "", "prompt_id": "p1_compact_v1"}], "prompts": [{"id": "p1_compact_v1", "system": "You are a Phase 1 domain content generator. Goal: produce neutral, concise seed content. Rules: factual, brand-aligned, short, schema-only.", "user_template": "Return ONLY valid JSON matching this schema: {\n  \"summary\": string (<= 60 words),\n  \"keyFeatures\": string[<=5],\n  \"audience\": string[<=5],\n  \"useCases\": string[<=5],\n  \"keywords\": string[<=10]\n}\nNo extra keys; no code fences.\nDomain: {{domain}}\nBrand: {{brand}}\nPrimaryCategory: {{category}}\nTags: {{tags}}\n{{#if context}}Context:\n{{context}}{{/if}}"}]}