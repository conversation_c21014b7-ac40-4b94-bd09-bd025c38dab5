import type { ManticoreClient } from '@shared';
import { DataSyncService, Logger } from '@shared';

type DomainSyncData =
{
	domain: string;
	discoveryStrategy: string;
	confidence: number;
	firstSource: string;
	seenAt: Date;
	metadata?: Record<string, any>;
};

type SyncMetrics =
{
	totalSynced: number;
	successfulSyncs: number;
	failedSyncs: number;
	lastSyncTime: Date | null;
	averageSyncLatency: number;
};

class SeederDataSyncService extends DataSyncService
{
	private manticoreClient: ManticoreClient;

	private logger: Logger;

	private syncMetrics: SyncMetrics;

	constructor(manticoreClient: ManticoreClient, logger: Logger)
	{
		super();
		this.manticoreClient = manticoreClient;
		this.logger = logger;
		this.syncMetrics = {
			totalSynced: 0,
			successfulSyncs: 0,
			failedSyncs: 0,
			lastSyncTime: null,
			averageSyncLatency: 0,
		};
	}

	async syncDiscoveredDomain(domainData: DomainSyncData): Promise<void>
	{
		const startTime = Date.now();

		try
		{
			// Prepare domain data for Manticore search index
			const searchDocument = this.prepareDomainDocument(domainData);

			// Insert/update in Manticore search index
			await this.manticoreClient.upsertDocument('domain_discoveries', domainData.domain, searchDocument);

			// Update metrics
			this.updateSyncMetrics(true, Date.now() - startTime);

			this.logger.debug('Domain synced to search index', {
				domain: domainData.domain,
				strategy: domainData.discoveryStrategy,
				latency: Date.now() - startTime,
			});
		}
		catch (error)
		{
			this.updateSyncMetrics(false, Date.now() - startTime);
			this.logger.error('Failed to sync domain to search index', error, {
				domain: domainData.domain,
				strategy: domainData.discoveryStrategy,
			});
			throw error;
		}
	}

	async syncDiscoveredDomainsBatch(domainsData: DomainSyncData[]): Promise<void>
	{
		const startTime = Date.now();
		const batchSize = 1000; // Process in batches of 1000

		try
		{
			for (let i = 0; i < domainsData.length; i += batchSize)
			{
				const batch = domainsData.slice(i, i + batchSize);
				const documents = batch.map(domain => this.prepareDomainDocument(domain));

				// Batch insert to Manticore
				await this.manticoreClient.bulkInsert('domain_discoveries', documents);

				this.logger.debug('Batch synced to search index', {
					batchSize: batch.length,
					totalProcessed: Math.min(i + batchSize, domainsData.length),
					totalDomains: domainsData.length,
				});
			}

			// Update metrics for successful batch
			this.updateSyncMetrics(true, Date.now() - startTime, domainsData.length);

			this.logger.info('Batch sync completed successfully', {
				totalDomains: domainsData.length,
				latency: Date.now() - startTime,
			});
		}
		catch (error)
		{
			this.updateSyncMetrics(false, Date.now() - startTime, domainsData.length);
			this.logger.error('Batch sync failed', error, {
				totalDomains: domainsData.length,
			});
			throw error;
		}
	}

	private prepareDomainDocument(domainData: DomainSyncData): any
	{
		return ({
			id: this.generateDocumentId(domainData.domain, domainData.seenAt),
			domain: domainData.domain,
			discovery_strategy: domainData.discoveryStrategy,
			confidence: Math.round(domainData.confidence * 100), // Store as integer 0-100
			first_source: domainData.firstSource,
			seen_at: Math.floor(domainData.seenAt.getTime() / 1000), // Unix timestamp
			discovered_at: Math.floor(Date.now() / 1000),

			// Extract searchable fields from metadata
			tld: this.extractTLD(domainData.domain),
			domain_length: domainData.domain.length,
			has_hyphen: domainData.domain.includes('-') ? 1 : 0,
			has_numbers: /\d/.test(domainData.domain) ? 1 : 0,

			// Metadata as JSON string for storage
			metadata: domainData.metadata ? JSON.stringify(domainData.metadata) : '',
		});
	}

	private generateDocumentId(domain: string, seenAt: Date): string
	{
		// Create unique ID based on domain and discovery time
		const timestamp = seenAt.getTime();
		const hash = this.simpleHash(domain);
		return `${hash}_${timestamp}`;
	}

	private simpleHash(str: string): string
	{
		let hash = 0;
		for (let i = 0; i < str.length; i++)
		{
			const char = str.charCodeAt(i);
			hash = ((hash << 5) - hash) + char;
			hash &= hash; // Convert to 32-bit integer
		}
		return Math.abs(hash).toString(36);
	}

	private extractTLD(domain: string): string
	{
		const parts = domain.split('.');
		return parts[parts.length - 1] || '';
	}

	private updateSyncMetrics(success: boolean, latency: number, count: number = 1): void
	{
		this.syncMetrics.totalSynced += count;
		this.syncMetrics.lastSyncTime = new Date();

		if (success)
		{
			this.syncMetrics.successfulSyncs += count;
		}
		else
		{
			this.syncMetrics.failedSyncs += count;
		}

		// Update average latency (simple moving average)
		const totalOperations = this.syncMetrics.successfulSyncs + this.syncMetrics.failedSyncs;
		this.syncMetrics.averageSyncLatency =
			(this.syncMetrics.averageSyncLatency * (totalOperations - count) + latency) / totalOperations;
	}

	// Search functionality for discovered domains
	async searchDiscoveredDomains(query: {
		domain?: string;
		strategy?: string;
		source?: string;
		confidenceMin?: number;
		confidenceMax?: number;
		seenAfter?: Date;
		seenBefore?: Date;
		limit?: number;
		offset?: number;
	}): Promise<any[]>
	{
		try
		{
			const searchQuery = this.buildSearchQuery(query);
			const results = await this.manticoreClient.searchDomains({
				query: searchQuery,
				limit: query.limit,
				offset: query.offset
			});

			this.logger.debug('Search completed', {
				query,
				resultsCount: results.hits?.length || 0,
			});

			return results.results || [];
		}
		catch (error)
		{
			this.logger.error('Search failed', error, { query });
			throw error;
		}
	}

	private buildSearchQuery(query: any): any
	{
		const conditions: string[] = [];
		const params: any = {};

		// Domain search (partial match)
		if (query.domain)
		{
			conditions.push('MATCH(@domain :domain)');
			params.domain = query.domain;
		}

		// Strategy filter
		if (query.strategy)
		{
			conditions.push('discovery_strategy = :strategy');
			params.strategy = query.strategy;
		}

		// Source filter
		if (query.source)
		{
			conditions.push('first_source = :source');
			params.source = query.source;
		}

		// Confidence range
		if (query.confidenceMin !== undefined)
		{
			conditions.push('confidence >= :confidenceMin');
			params.confidenceMin = Math.round(query.confidenceMin * 100);
		}

		if (query.confidenceMax !== undefined)
		{
			conditions.push('confidence <= :confidenceMax');
			params.confidenceMax = Math.round(query.confidenceMax * 100);
		}

		// Date range
		if (query.seenAfter)
		{
			conditions.push('seen_at >= :seenAfter');
			params.seenAfter = Math.floor(query.seenAfter.getTime() / 1000);
		}

		if (query.seenBefore)
		{
			conditions.push('seen_at <= :seenBefore');
			params.seenBefore = Math.floor(query.seenBefore.getTime() / 1000);
		}

		return ({
			where: conditions.length > 0 ? conditions.join(' AND ') : undefined,
			params,
			limit: query.limit || 100,
			offset: query.offset || 0,
			orderBy: 'discovered_at DESC',
		});
	}

	// Analytics and reporting
	async getDiscoveryAnalytics(timeRange: {
		start: Date;
		end: Date;
	}): Promise<any>
	{
		try
		{
			const startTimestamp = Math.floor(timeRange.start.getTime() / 1000);
			const endTimestamp = Math.floor(timeRange.end.getTime() / 1000);

			// Get strategy breakdown
			const strategyBreakdown = await this.manticoreClient.sql(
				`SELECT discovery_strategy, COUNT(*) as count FROM domain_discoveries WHERE discovered_at >= ${startTimestamp} AND discovered_at <= ${endTimestamp} GROUP BY discovery_strategy`
			);

			// Get source breakdown
			const sourceBreakdown = await this.manticoreClient.sql(
				`SELECT first_source, COUNT(*) as count FROM domain_discoveries WHERE discovered_at >= ${startTimestamp} AND discovered_at <= ${endTimestamp} GROUP BY first_source`
			);

			// Get confidence distribution
			const confidenceDistribution = await this.manticoreClient.sql(
				`SELECT INTERVAL(confidence, 0, 25, 50, 75, 100) as confidence_bucket, COUNT(*) as count FROM domain_discoveries WHERE discovered_at >= ${startTimestamp} AND discovered_at <= ${endTimestamp} GROUP BY confidence_bucket`
			);

			// Get daily discovery counts
			const dailyCounts = await this.manticoreClient.sql(
				`SELECT DAY(FROM_UNIXTIME(discovered_at)) as day, COUNT(*) as count FROM domain_discoveries WHERE discovered_at >= ${startTimestamp} AND discovered_at <= ${endTimestamp} GROUP BY day ORDER BY day ASC`
			);

			return {
				timeRange,
				strategyBreakdown: strategyBreakdown.hits || [],
				sourceBreakdown: sourceBreakdown.hits || [],
				confidenceDistribution: confidenceDistribution.hits || [],
				dailyCounts: dailyCounts.hits || [],
				totalDiscovered: (strategyBreakdown.hits || [])
					.reduce((sum, item) => sum + (item.count || 0), 0),
			};
		}
		catch (error)
		{
			this.logger.error('Failed to get discovery analytics', error, { timeRange });
			throw error;
		}
	}

	// Cleanup old discovery records
	async cleanupOldRecords(retentionDays: number = 90): Promise<void>
	{
		try
		{
			const cutoffTimestamp = Math.floor((Date.now() - (retentionDays * 24 * 60 * 60 * 1000)) / 1000);

			// Use SQL query for bulk delete since ManticoreClient doesn't have a bulk delete with where clause
			const result = await this.manticoreClient.sql(`DELETE FROM domain_discoveries WHERE discovered_at < ${cutoffTimestamp}`);

			this.logger.info('Old discovery records cleaned up', {
				retentionDays,
				deletedRecords: result.deleted || 0,
			});
		}
		catch (error)
		{
			this.logger.error('Failed to cleanup old records', error, { retentionDays });
			throw error;
		}
	}

	// Get sync metrics
	getSyncMetrics(): SyncMetrics
	{
		return { ...this.syncMetrics };
	}

	// Reset metrics
	resetMetrics(): void
	{
		this.syncMetrics = {
			totalSynced: 0,
			successfulSyncs: 0,
			failedSyncs: 0,
			lastSyncTime: null,
			averageSyncLatency: 0,
		};
		this.logger.info('Sync metrics reset');
	}

	// Health check
	async healthCheck(): Promise<boolean>
	{
		try
		{
			// Test connection to Manticore
			await this.manticoreClient.search('domain_discoveries', {
				limit: 1,
			});
			return true;
		}
		catch (error)
		{
			this.logger.error('Data sync service health check failed', error);
			return false;
		}
	}
}

export type { DomainSyncData, SyncMetrics };

export default SeederDataSyncService;
