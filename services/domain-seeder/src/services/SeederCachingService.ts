import type { Redis<PERSON>lient<PERSON><PERSON><PERSON>, Lo<PERSON>, LoggerInstanceType } from '@shared';
import { CachingService, ScyllaClient, ManticoreClient } from '@shared';

type DomainExistenceCache =
{
	domain: string;
	exists: boolean;
	checkedAt: Date;
	source: 'scylla' | 'maria' | 'manticore' | 'bloom';
};

type BloomFilterConfig =
{
	expectedElements: number;
	falsePositiveRate: number;
	hashFunctions: number;
	bitArraySize: number;
};

class SeederCachingService extends CachingService
{
	private redis: RedisClientWrapper;

	private bloomFilter: {
		add(item: string): void;
		test(item: string): boolean;
		clear(): void;
	} | null = null; // Will be initialized with actual bloom filter implementation

	private bloomConfig: BloomFilterConfig;

	constructor(redis: RedisClientWrapper, scylla: ScyllaClient, manticore: ManticoreClient)
	{
		super(redis, scylla, manticore);
		this.redis = redis;
		this.bloomConfig =
		{
			expectedElements: 10000000, // 10M domains
			falsePositiveRate: 0.01, // 1% false positive rate
			hashFunctions: 7,
			bitArraySize: 0, // Will be calculated
		};
	}

	async initialize(): Promise<void>
	{
		await super.initialize();
		await this.initializeBloomFilter();
		this.logger.info('Seeder caching service initialized');
	}

	private async initializeBloomFilter(): Promise<void>
	{
		try
		{
			// Calculate optimal bit array size
			const m = Math.ceil(
				(-this.bloomConfig.expectedElements * Math.log(this.bloomConfig.falsePositiveRate))
				/ (Math.log(2) ** 2),
			);
			this.bloomConfig.bitArraySize = m;

			// Try to load existing bloom filter from Redis
			const existingFilter = await this.redis.get('bloom:domain_existence');

			if (existingFilter)
			{
				// Load existing bloom filter
				this.bloomFilter = this.deserializeBloomFilter(existingFilter);
				this.logger.info('Loaded existing bloom filter from Redis', {
					bitArraySize: this.bloomConfig.bitArraySize,
					expectedElements: this.bloomConfig.expectedElements,
				});
			}
			else
			{
				// Create new bloom filter
				this.bloomFilter = this.createBloomFilter();
				await this.warmBloomFilter();
				this.logger.info('Created new bloom filter', {
					bitArraySize: this.bloomConfig.bitArraySize,
					expectedElements: this.bloomConfig.expectedElements,
				});
			}
		}
		catch (error)
		{
			this.logger.error('Failed to initialize bloom filter', error);
			// Continue without bloom filter - will fall back to Redis cache
		}
	}

	private createBloomFilter(): any
	{
		// Simple bloom filter implementation using bit array
		const bitArray = new Uint8Array(Math.ceil(this.bloomConfig.bitArraySize / 8));
		const hashFunctions = this.bloomConfig.hashFunctions;

		return {
			add: (item: string) =>
			{
				for (let i = 0; i < hashFunctions; i++)
				{
					const hash = this.hash(item, i) % this.bloomConfig.bitArraySize;
					const byteIndex = Math.floor(hash / 8);
					const bitIndex = hash % 8;
					bitArray[byteIndex] |= (1 << bitIndex);
				}
			},
			test: (item: string) =>
			{
				for (let i = 0; i < hashFunctions; i++)
				{
					const hash = this.hash(item, i) % this.bloomConfig.bitArraySize;
					const byteIndex = Math.floor(hash / 8);
					const bitIndex = hash % 8;
					if (!(bitArray[byteIndex] & (1 << bitIndex)))
					{
						return false; // Definitely not in set
					}
				}
				return true; // Might be in set
			},
			serialize: () => JSON.stringify({
				bitArray: Array.from(bitArray),
				config: this.bloomConfig,
			}),
			getBitArray: () => bitArray,
		};
	}

	private hash(str: string, seed: number): number
	{
		// Simple hash function (djb2 variant)
		let hash = 5381 + seed;
		for (let i = 0; i < str.length; i++)
		{
			hash = ((hash << 5) + hash) + str.charCodeAt(i);
		}
		return Math.abs(hash);
	}

	private deserializeBloomFilter(data: string): any
	{
		try
		{
			const parsed = JSON.parse(data);
			const bitArray = new Uint8Array(parsed.bitArray);
			const config = parsed.config;

			// Update config if needed
			this.bloomConfig = { ...this.bloomConfig, ...config };

			return {
				add: (item: string) =>
				{
					for (let i = 0; i < this.bloomConfig.hashFunctions; i++)
					{
						const hash = this.hash(item, i) % this.bloomConfig.bitArraySize;
						const byteIndex = Math.floor(hash / 8);
						const bitIndex = hash % 8;
						bitArray[byteIndex] |= (1 << bitIndex);
					}
				},
				test: (item: string) =>
				{
					for (let i = 0; i < this.bloomConfig.hashFunctions; i++)
					{
						const hash = this.hash(item, i) % this.bloomConfig.bitArraySize;
						const byteIndex = Math.floor(hash / 8);
						const bitIndex = hash % 8;
						if (!(bitArray[byteIndex] & (1 << bitIndex)))
						{
							return false;
						}
					}
					return true;
				},
				serialize: () => JSON.stringify({
					bitArray: Array.from(bitArray),
					config: this.bloomConfig,
				}),
				getBitArray: () => bitArray,
			};
		}
		catch (error)
		{
			this.logger.warn('Failed to deserialize bloom filter, creating new one', error);
			return this.createBloomFilter();
		}
	}

	private async warmBloomFilter(): Promise<void>
	{
		try
		{
			this.logger.info('Warming bloom filter from database snapshots...');

			// This would query the databases to get existing domains
			// and add them to the bloom filter
			// For now, just log the intent

			this.logger.info('Bloom filter warming completed');
		}
		catch (error)
		{
			this.logger.error('Failed to warm bloom filter', error);
		}
	}

	// Domain existence caching
	async cacheDomainExists(
		domain: string,
		exists: boolean,
		source: string,
		ttl: number = 86400,
	): Promise<void>
	{
		try
		{
			const cacheKey = `domain:exists:${domain}`;
			const cacheData: DomainExistenceCache = {
				domain,
				exists,
				checkedAt: new Date(),
				source: source as any,
			};

			await this.redis.setex(cacheKey, ttl, JSON.stringify(cacheData));

			// Update bloom filter for positive results
			if (exists && this.bloomFilter)
			{
				this.bloomFilter.add(domain);
				// Periodically persist bloom filter to Redis
				if (Math.random() < 0.001) // 0.1% chance
				{
					await this.persistBloomFilter();
				}
			}

			this.logger.debug('Domain existence cached', { domain, exists, source });
		}
		catch (error)
		{
			this.logger.error('Failed to cache domain existence', error, { domain, exists, source });
		}
	}

	async getDomainExists(domain: string): Promise<DomainExistenceCache | null>
	{
		try
		{
			const cacheKey = `domain:exists:${domain}`;
			const cached = await this.redis.get(cacheKey);

			if (cached)
			{
				const data = JSON.parse(cached) as DomainExistenceCache;
				data.checkedAt = new Date(data.checkedAt); // Parse date
				return data;
			}

			return null;
		}
		catch (error)
		{
			this.logger.error('Failed to get cached domain existence', error, { domain });
			return null;
		}
	}

	async batchGetDomainExists(domains: string[]): Promise<Map<string, DomainExistenceCache>>
	{
		const results = new Map<string, DomainExistenceCache>();

		try
		{
			const pipeline = this.redis.pipeline();
			const cacheKeys = domains.map(domain => `domain:exists:${domain}`);

			// Add all gets to pipeline
			for (const key of cacheKeys)
			{
				pipeline.get(key);
			}

			const pipelineResults = await pipeline.exec();

			// Process results
			for (let i = 0; i < domains.length; i++)
			{
				const domain = domains[i];
				const result = pipelineResults?.[i];

				if (result && result[1]) // result[0] is error, result[1] is value
				{
					try
					{
						const data = JSON.parse(result[1] as string) as DomainExistenceCache;
						data.checkedAt = new Date(data.checkedAt);
						results.set(domain, data);
					}
					catch (parseError)
					{
						this.logger.warn('Failed to parse cached domain data', { domain, error: parseError });
					}
				}
			}

			this.logger.debug('Batch domain existence cache lookup completed', {
				requested: domains.length,
				found: results.size,
			});

			return results;
		}
		catch (error)
		{
			this.logger.error('Batch domain existence cache lookup failed', error);
			return results;
		}
	}

	// Bloom filter operations
	async bloomFilterTest(domain: string): Promise<boolean>
	{
		if (!this.bloomFilter)
		{
			return true; // If no bloom filter, assume might exist
		}

		try
		{
			return this.bloomFilter.test(domain);
		}
		catch (error)
		{
			this.logger.error('Bloom filter test failed', error, { domain });
			return true; // Fail open
		}
	}

	async bloomFilterBatchTest(domains: string[]): Promise<Map<string, boolean>>
	{
		const results = new Map<string, boolean>();

		if (!this.bloomFilter)
		{
			// If no bloom filter, assume all might exist
			for (const domain of domains)
			{
				results.set(domain, true);
			}
			return results;
		}

		try
		{
			for (const domain of domains)
			{
				results.set(domain, this.bloomFilter.test(domain));
			}

			return results;
		}
		catch (error)
		{
			this.logger.error('Bloom filter batch test failed', error);
			// Fail open - assume all might exist
			for (const domain of domains)
			{
				results.set(domain, true);
			}
			return results;
		}
	}

	private async persistBloomFilter(): Promise<void>
	{
		if (!this.bloomFilter)
		{
			return;
		}

		try
		{
			const serialized = this.bloomFilter.serialize();
			await this.redis.set('bloom:domain_existence', serialized);
			this.logger.debug('Bloom filter persisted to Redis');
		}
		catch (error)
		{
			this.logger.error('Failed to persist bloom filter', error);
		}
	}

	// Snapshot caching for differential analysis
	async cacheSnapshot(
		source: string,
		date: string,
		domains: any[],
		ttl: number = 2592000,
	): Promise<void>
	{
		try
		{
			const cacheKey = `snapshot:${source}:${date}`;
			const snapshotData = {
				source,
				date,
				domains,
				totalCount: domains.length,
				cachedAt: new Date().toISOString(),
			};

			await this.redis.setex(cacheKey, ttl, JSON.stringify(snapshotData));
			this.logger.debug('Snapshot cached', { source, date, domainCount: domains.length });
		}
		catch (error)
		{
			this.logger.error('Failed to cache snapshot', error, { source, date });
		}
	}

	async getSnapshot(source: string, date: string): Promise<any | null>
	{
		try
		{
			const cacheKey = `snapshot:${source}:${date}`;
			const cached = await this.redis.get(cacheKey);

			if (cached)
			{
				return JSON.parse(cached);
			}

			return null;
		}
		catch (error)
		{
			this.logger.error('Failed to get cached snapshot', error, { source, date });
			return null;
		}
	}

	// Idempotency caching
	async setIdempotencyKey(operation: string, key: string, ttl: number = 86400): Promise<boolean>
	{
		try
		{
			const idempotencyKey = `idempotent:${operation}:${key}`;
			const result = await this.redis.setnx(idempotencyKey, Date.now().toString());

			if (result === 1)
			{
				await this.redis.expire(idempotencyKey, ttl);
				return true; // Operation can proceed
			}

			return false; // Operation already in progress or completed
		}
		catch (error)
		{
			this.logger.error('Failed to set idempotency key', error, { operation, key });
			return true; // Fail open to allow operation
		}
	}

	async checkIdempotencyKey(operation: string, key: string): Promise<boolean>
	{
		try
		{
			const idempotencyKey = `idempotent:${operation}:${key}`;
			const exists = await this.redis.exists(idempotencyKey);
			return exists === 1;
		}
		catch (error)
		{
			this.logger.error('Failed to check idempotency key', error, { operation, key });
			return false;
		}
	}

	// Cache statistics
	async getCacheStats(): Promise<any>
	{
		try
		{
			const info = await this.redis.info('memory');
			const keyspace = await this.redis.info('keyspace');

			// Count domain existence cache entries
			const domainCacheKeys = await this.redis.keys('domain:exists:*');
			const snapshotCacheKeys = await this.redis.keys('snapshot:*');
			const idempotencyKeys = await this.redis.keys('idempotent:*');

			return {
				memory: info,
				keyspace,
				cacheEntries: {
					domainExists: domainCacheKeys.length,
					snapshots: snapshotCacheKeys.length,
					idempotency: idempotencyKeys.length,
				},
				bloomFilter: {
					configured: !!this.bloomFilter,
					config: this.bloomConfig,
				},
			};
		}
		catch (error)
		{
			this.logger.error('Failed to get cache stats', error);
			return { error: error.message };
		}
	}

	// Cleanup expired entries
	async cleanup(): Promise<void>
	{
		try
		{
			// Redis handles TTL expiration automatically
			// But we can clean up bloom filter periodically
			if (Math.random() < 0.01) // 1% chance
			{
				await this.persistBloomFilter();
			}

			this.logger.debug('Cache cleanup completed');
		}
		catch (error)
		{
			this.logger.error('Cache cleanup failed', error);
		}
	}
}

export type { DomainExistenceCache };
export { SeederCachingService };
export default SeederCachingService;
