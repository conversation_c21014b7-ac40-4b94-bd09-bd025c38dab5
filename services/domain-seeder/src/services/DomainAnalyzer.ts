import fs from 'node:fs';
import path from 'node:path';
import { curly } from 'node-libcurl';
import { CacheManager } from '@shared/database/CacheManager';
import { RedisClientWrapper } from '@shared';

type CategoryData =
{
	description: string;
	tlds: string[];
	domainPatterns: string[];
};

type TLDCategories =
{
	[category: string]: CategoryData;
};

type DomainAnalysisResult =
{
	domain: string;
	tld: string;
	domainName: string;
	tldCategory?: string;
	tldDescription?: string;
	matchedCategories: string[];
	structuralAnalysis: string[];
	webData?: {
		title?: string;
		description?: string;
		content?: string;
		status: 'success' | 'failed' | 'disabled';
	};
	confidence: number;
};

class DomainAnalyzer
{
	private categories: TLDCategories;
	private cacheManager: CacheManager;
	private redisClient: RedisClientWrapper;

	constructor()
	{
		// Load unified categories from data file
		const categoriesPath = path.resolve(__dirname, '../../../data/tld-categories.json');
		this.categories = JSON.parse(fs.readFileSync(categoriesPath, 'utf8'));

		// Initialize Redis client and cache manager
		this.redisClient = new RedisClientWrapper();
		this.cacheManager = new CacheManager(this.redisClient);
	}

	async initialize(): Promise<void>
	{
		// Connect to Redis if not already connected
		if (!this.redisClient.isReady()) {
			await this.redisClient.connect();
		}
	}

	/**
	 * Analyzes a domain and returns comprehensive analysis
	 */
	async analyzeDomain(
		domain: string,
		fetchWeb: boolean = false,
		webProxy?: string,
	): Promise<DomainAnalysisResult>
	{
		// Check cache first
		const cacheKey = `analysis:${domain}:${fetchWeb ? 'web' : 'basic'}`;
		try {
			const cached = await this.cacheManager.getCachedDomainAnalysis(domain);
			if (cached && typeof cached === 'object') {
				// Ensure cached result matches current analysis structure
				const cachedResult = cached as DomainAnalysisResult;
				if (cachedResult.domain === domain && cachedResult.confidence !== undefined) {
					return cachedResult;
				}
			}
		} catch (error) {
			// Cache miss or error - continue with analysis
		}

		const { tld, domainName } = this.parseDomain(domain);

		// Unified category analysis (TLD + domain patterns)
		const { primaryCategory, matchedCategories } = this.analyzeCategories(tld, domainName);

		// Structural analysis
		const structuralAnalysis = this.analyzeStructure(domainName);

		// Optional web data fetching
		let webData;
		if (fetchWeb)
		{
			webData = await this.fetchWebData(domain, webProxy);
		}

		// Calculate confidence score
		const confidence = this.calculateConfidence(
			primaryCategory,
			matchedCategories,
			structuralAnalysis,
			webData,
		);

		const result: DomainAnalysisResult = {
			domain,
			tld,
			domainName,
			tldCategory: primaryCategory?.category,
			tldDescription: primaryCategory?.description,
			matchedCategories,
			structuralAnalysis,
			webData,
			confidence,
		};

		// Cache the result for future use
		try {
			const cacheExpiryHours = fetchWeb ? 24 : 72; // Web data expires faster than basic analysis
			await this.cacheManager.cacheDomainAnalysis(domain, result, cacheExpiryHours * 3600);
		} catch (error) {
			// Continue even if caching fails
		}

		return result;
	}

	/**
	 * Parses domain into components
	 */
	private parseDomain(domain: string): { tld: string; domainName: string }
	{
		const parts = domain.split('.');
		const tld = parts[parts.length - 1];
		const domainName = parts.slice(0, -1).join('.');
		return { tld, domainName };
	}

	/**
	 * Analyzes domain using unified TLD and domain pattern matching
	 */
	private analyzeCategories(tld: string, domainName: string): {
		primaryCategory?: { category: string; description: string };
		matchedCategories: string[];
	}
	{
		const matchedCategories: string[] = [];
		let primaryCategory: { category: string; description: string } | undefined;

		// Check each category for TLD or domain pattern matches
		for (const [categoryName, categoryData] of Object.entries(this.categories))
		{
			let matched = false;

			// Check TLD match
			if (categoryData.tlds.includes(tld.toLowerCase()))
			{
				matched = true;
				if (!primaryCategory)
				{
					primaryCategory = { category: categoryName, description: categoryData.description };
				}
			}

			// Check domain pattern matches
			for (const pattern of categoryData.domainPatterns)
			{
				const regex = new RegExp(`\\b${pattern}\\b`, 'i');
				if (regex.test(domainName))
				{
					matched = true;
					if (!primaryCategory)
					{
						primaryCategory = { category: categoryName, description: categoryData.description };
					}
				}
			}

			if (matched)
			{
				matchedCategories.push(categoryName);
			}
		}

		return { primaryCategory, matchedCategories };
	}

	/**
	 * Analyzes domain structure and characteristics
	 */
	private analyzeStructure(domainName: string): string[]
	{
		const analysis: string[] = [];

		// Length analysis
		if (domainName.length <= 4)
		{
			analysis.push('Short domain - likely brand name or abbreviation');
		}
		else if (domainName.length >= 20)
		{
			analysis.push('Long domain - descriptive or keyword-rich');
		}

		// Character analysis
		if (domainName.includes('-'))
		{
			analysis.push('Hyphenated domain - likely descriptive or keyword combination');
		}

		if (domainName.includes('_'))
		{
			analysis.push('Underscore domain - technical or specialized use');
		}

		// Number analysis
		if (/\\d/.test(domainName))
		{
			analysis.push('Contains numbers - could be version, year, or branded identifier');
		}

		// Common prefix/suffix analysis
		const prefixes = ['www', 'my', 'get', 'go', 'the', 'best', 'top', 'pro', 'super', 'mega', 'ultra'];
		const suffixes = ['app', 'hub', 'lab', 'pro', 'plus', 'max', 'online', 'digital', 'tech', 'group'];

		prefixes.forEach((prefix) =>
		{
			if (domainName.toLowerCase().startsWith(prefix))
			{
				analysis.push(`Marketing prefix '${prefix}' - likely commercial focus`);
			}
		});

		suffixes.forEach((suffix) =>
		{
			if (domainName.toLowerCase().endsWith(suffix))
			{
				analysis.push(`Business suffix '${suffix}' - indicates ${suffix} service or product`);
			}
		});

		// Vowel/consonant ratio (readability)
		const vowels = (domainName.match(/[aeiou]/gi) || []).length;
		const consonants = (domainName.match(/[bcdfghjklmnpqrstvwxyz]/gi) || []).length;
		const ratio = vowels / (vowels + consonants);

		if (ratio < 0.2)
		{
			analysis.push('Low vowel ratio - may be acronym or abbreviated name');
		}
		else if (ratio > 0.5)
		{
			analysis.push('High vowel ratio - likely pronounceable brand name');
		}

		return analysis.length ? analysis : ['Standard domain structure'];
	}

	/**
	 * Fetches web data from the domain
	 */
	private async fetchWebData(domain: string, proxy?: string): Promise<{
		title?: string;
		description?: string;
		content?: string;
		status: 'success' | 'failed' | 'disabled';
	}>
	{
		// Always allow web fetching when called from DomainAnalyzer
		// The calling code will determine if web fetching should be enabled

		try
		{
			const url = `https://www.${domain}`;
			const opts: any = {
				FOLLOWLOCATION: true,
				TIMEOUT: 10,
				MAXREDIRS: 3,
				USERAGENT: 'Mozilla/5.0 (DomainAnalyzer/1.0)',
			};

			if (proxy)
			{
				opts.PROXY = proxy;
			}

			const { data, statusCode } = await curly.get(url, opts);

			if (statusCode >= 200 && statusCode < 300)
			{
				const html = String(data);

				// Extract title
				const titleMatch = html.match(/<title[^>]*>([^<]+)</i);
				const title = titleMatch?.[1]?.trim();

				// Extract meta description
				const descMatch = html.match(/<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"']+)["\'][^>]*>/i);
				const description = descMatch?.[1]?.trim();

				// Extract main content (basic text extraction)
				const contentMatch = html
					.replace(/<script[^>]*>.*?<\/script>/gi, '')
					.replace(/<style[^>]*>.*?<\/style>/gi, '')
					.replace(/<[^>]+>/g, ' ')
					.replace(/\s+/g, ' ')
					.trim()
					.slice(0, 2000);

				return {
					title,
					description,
					content: contentMatch,
					status: 'success',
				};
			}

			return { status: 'failed' };
		}
		catch (error)
		{
			return { status: 'failed' };
		}
	}

	/**
	 * Calculates confidence score based on available analysis
	 */
	private calculateConfidence(
		primaryCategory?: { category: string; description: string },
		matchedCategories?: string[],
		structuralAnalysis?: string[],
		webData?: any,
	): number
	{
		let confidence = 0.3; // Base confidence

		if (primaryCategory) confidence += 0.2;
		if (matchedCategories && matchedCategories.length > 0) confidence += 0.2;
		if (matchedCategories && matchedCategories.length > 1) confidence += 0.1; // Multiple category matches
		if (structuralAnalysis && structuralAnalysis.length > 1) confidence += 0.1;
		if (webData?.status === 'success')
		{
			if (webData.title) confidence += 0.1;
			if (webData.description) confidence += 0.1;
		}

		return Math.min(1.0, confidence);
	}

	/**
	 * Formats analysis result for LLM consumption
	 */
	formatForLLM(analysis: DomainAnalysisResult): string
	{
		const sections = [];

		// Primary Category
		if (analysis.tldCategory)
		{
			sections.push(`Primary Category: ${analysis.tldCategory} - ${analysis.tldDescription}`);
		}

		// All Matched Categories
		if (analysis.matchedCategories.length > 0)
		{
			sections.push(`Category Matches: ${analysis.matchedCategories.join(', ')}`);
		}

		// Structural Analysis
		if (analysis.structuralAnalysis.length > 0)
		{
			sections.push(`Structure: ${analysis.structuralAnalysis.join('; ')}`);
		}

		// Web Data
		if (analysis.webData?.status === 'success')
		{
			const webInfo = [];
			if (analysis.webData.title) webInfo.push(`Title: "${analysis.webData.title}"`);
			if (analysis.webData.description) webInfo.push(`Description: "${analysis.webData.description}"`);
			if (webInfo.length > 0)
			{
				sections.push(`Web Data: ${webInfo.join(', ')}`);
			}
		}

		// Confidence
		sections.push(`Analysis Confidence: ${Math.round(analysis.confidence * 100)}%`);

		return sections.join('\\n');
	}
}

export default DomainAnalyzer;
