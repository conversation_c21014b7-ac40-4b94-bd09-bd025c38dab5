import type RedisClientWrapper from '@shared/database/RedisClient';
import { logger } from '@shared/utils/Logger';

/**
 * Configuration for Redis operational layer
 */
type RedisOperationalConfig =
{
	/**
	 * TTL for deduplication keys in seconds
	 */
	deduplicationTtl: number;

	/**
	 * TTL for rate limiting tokens in seconds
	 */
	rateLimitTtl: number;

	/**
	 * TTL for recent negative results cache in seconds (short TTL, small memory footprint)
	 */
	negativeResultTtl: number;

	/**
	 * TTL for processing state tracking in seconds
	 */
	processingStateTtl: number;

	/**
	 * Maximum number of concurrent operations to track
	 */
	maxConcurrentOperations: number;

	/**
	 * Key prefix for operational keys
	 */
	keyPrefix: string;

	/**
	 * Enable metrics collection
	 */
	enableMetrics: boolean;
};

/**
 * Default configuration for Redis operational layer
 */
const DEFAULT_OPERATIONAL_CONFIG: RedisOperationalConfig = {
	deduplicationTtl: 300, // 5 minutes
	rateLimitTtl: 3600, // 1 hour
	negativeResultTtl: 1800, // 30 minutes (short TTL)
	processingStateTtl: 600, // 10 minutes
	maxConcurrentOperations: 10000,
	keyPrefix: 'domain:op:',
	enableMetrics: true,
};

/**
 * Processing state for concurrent operations
 */
type ProcessingState =
{
	operationId: string;
	startTime: number;
	domains: string[];
	status: 'pending' | 'processing' | 'completed' | 'failed';
	progress?: number;
};

/**
 * Rate limiting token bucket state
 */
type TokenBucketState =
{
	tokens: number;
	lastRefill: number;
	capacity: number;
	refillRate: number;
};

/**
 * Redis operational layer focused on deduplication, rate limiting, and processing state management
 * Eliminates domain existence caching to reduce memory usage from 5-10GB to <100MB
 */
class RedisOperationalLayer
{
	private readonly logger = logger.getLogger('RedisOperationalLayer');

	private readonly config: RedisOperationalConfig;

	// Performance metrics
	private deduplicationHits = 0;

	private rateLimitChecks = 0;

	private negativeResultCacheHits = 0;

	private processingStateOperations = 0;

	constructor(
		private readonly redisClient: RedisClientWrapper,
		config: Partial<RedisOperationalConfig> = {},
	)
	{
		this.config = { ...DEFAULT_OPERATIONAL_CONFIG, ...config };

		this.logger.info('RedisOperationalLayer initialized', {
			config: this.config,
		});
	}

	/**
	 * Check if operation is already being processed (deduplication)
	 */
	async checkDeduplication(operationKey: string): Promise<{
		isAlreadyProcessing: boolean;
		existingOperationId?: string;
	}>
	{
		try
		{
			const dedupKey = `${this.config.keyPrefix}dedup:${operationKey}`;
			const existingOperationId = await this.redisClient.get(dedupKey);

			if (existingOperationId)
			{
				this.deduplicationHits++;
				return {
					isAlreadyProcessing: true,
					existingOperationId,
				};
			}

			return {
				isAlreadyProcessing: false,
			};
		}
		catch (error)
		{
			this.logger.warn('Deduplication check failed', {
				operationKey,
				error: error instanceof Error ? error.message : String(error),
			});

			// On Redis failure, assume not processing to avoid blocking
			return {
				isAlreadyProcessing: false,
			};
		}
	}

	/**
	 * Mark operation as being processed for deduplication
	 */
	async markOperationInProgress(
		operationKey: string,
		operationId: string,
	): Promise<boolean>
	{
		try
		{
			const dedupKey = `${this.config.keyPrefix}dedup:${operationKey}`;
			await this.redisClient.setex(
				dedupKey,
				this.config.deduplicationTtl,
				operationId,
			);

			return true;
		}
		catch (error)
		{
			this.logger.warn('Failed to mark operation in progress', {
				operationKey,
				operationId,
				error: (error as Error).message,
			});
			return false;
		}
	}

	/**
	 * Remove deduplication lock when operation completes
	 */
	async clearOperationLock(operationKey: string): Promise<void>
	{
		try
		{
			const dedupKey = `${this.config.keyPrefix}dedup:${operationKey}`;
			await this.redisClient.del(dedupKey);
		}
		catch (error)
		{
			this.logger.warn('Failed to clear operation lock', {
				operationKey,
				error: (error as Error).message,
			});
		}
	}

	/**
	 * Check rate limiting token bucket
	 */
	async checkRateLimit(
		bucketKey: string,
		tokensRequested: number = 1,
	): Promise<{
		allowed: boolean;
		tokensRemaining: number;
		retryAfter?: number;
	}>
	{
		try
		{
			this.rateLimitChecks++;

			const rateLimitKey = `${this.config.keyPrefix}rate:${bucketKey}`;
			const bucketData = await this.redisClient.get(rateLimitKey);

			let bucket: TokenBucketState;
			const now = Date.now();

			if (bucketData)
			{
				bucket = JSON.parse(bucketData);

				// Refill tokens based on time elapsed
				const timeSinceRefill = now - bucket.lastRefill;
				const tokensToAdd = Math.floor((timeSinceRefill / 1000) * bucket.refillRate);
				bucket.tokens = Math.min(bucket.capacity, bucket.tokens + tokensToAdd);
				bucket.lastRefill = now;
			}
			else
			{
				// Initialize new bucket
				bucket = {
					tokens: 100, // Default capacity
					lastRefill: now,
					capacity: 100,
					refillRate: 10, // 10 tokens per second
				};
			}

			if (bucket.tokens >= tokensRequested)
			{
				// Allow request and consume tokens
				bucket.tokens -= tokensRequested;

				// Update bucket in Redis
				await this.redisClient.setex(
					rateLimitKey,
					this.config.rateLimitTtl,
					JSON.stringify(bucket),
				);

				return {
					allowed: true,
					tokensRemaining: bucket.tokens,
				};
			}

			// Rate limit exceeded
			const retryAfter = Math.ceil((tokensRequested - bucket.tokens) / bucket.refillRate);

			return {
				allowed: false,
				tokensRemaining: bucket.tokens,
				retryAfter,
			};
		}
		catch (error)
		{
			this.logger.warn('Rate limit check failed', {
				bucketKey,
				tokensRequested,
				error: error instanceof Error ? error.message : String(error),
			});

			// On Redis failure, allow request to avoid blocking
			return {
				allowed: true,
				tokensRemaining: 0,
			};
		}
	}

	/**
	 * Cache recent negative results (short TTL, small memory footprint)
	 */
	async cacheNegativeResults(domains: string[]): Promise<void>
	{
		if (domains.length === 0)
		{
			return;
		}

		try
		{
			const pipeline = this.redisClient.pipeline();

			domains.forEach((domain) =>
			{
				const negativeKey = `${this.config.keyPrefix}negative:${domain}`;
				pipeline.setex(negativeKey, this.config.negativeResultTtl, 'false');
			});

			await pipeline.exec();

			this.logger.debug('Cached negative results', {
				count: domains.length,
				ttl: this.config.negativeResultTtl,
			});
		}
		catch (error)
		{
			this.logger.warn('Failed to cache negative results', {
				domainsCount: domains.length,
				error: (error as Error).message,
			});
		}
	}

	/**
	 * Check recent negative results cache
	 */
	async checkNegativeResults(domains: string[]): Promise<{
		cachedNegatives: Set<string>;
		uncachedDomains: string[];
		cacheHits: number;
	}>
	{
		if (domains.length === 0)
		{
			return {
				cachedNegatives: new Set(),
				uncachedDomains: [],
				cacheHits: 0,
			};
		}

		try
		{
			const negativeKeys = domains.map(domain => `${this.config.keyPrefix}negative:${domain}`);
			const results = await this.redisClient.mget(negativeKeys);

			const cachedNegatives = new Set<string>();
			const uncachedDomains: string[] = [];
			let cacheHits = 0;

			for (let i = 0; i < domains.length; i++)
			{
				const domain = domains[i];
				const cached = results[i];

				if (cached === 'false')
				{
					cachedNegatives.add(domain);
					cacheHits++;
					this.negativeResultCacheHits++;
				}
				else
				{
					uncachedDomains.push(domain);
				}
			}

			this.logger.debug('Checked negative results cache', {
				totalDomains: domains.length,
				cacheHits,
				uncached: uncachedDomains.length,
			});

			return {
				cachedNegatives,
				uncachedDomains,
				cacheHits,
			};
		}
		catch (error)
		{
			this.logger.warn('Failed to check negative results cache', {
				domainsCount: domains.length,
				error: (error as Error).message,
			});

			// On Redis failure, treat all as uncached
			return {
				cachedNegatives: new Set(),
				uncachedDomains: domains,
				cacheHits: 0,
			};
		}
	}

	/**
	 * Create processing state for concurrent operations tracking
	 */
	async createProcessingState(
		operationId: string,
		domains: string[],
	): Promise<boolean>
	{
		try
		{
			this.processingStateOperations++;

			const stateKey = `${this.config.keyPrefix}state:${operationId}`;
			const state: ProcessingState = {
				operationId,
				startTime: Date.now(),
				domains,
				status: 'pending',
				progress: 0,
			};

			await this.redisClient.setex(
				stateKey,
				this.config.processingStateTtl,
				JSON.stringify(state),
			);

			return true;
		}
		catch (error)
		{
			this.logger.warn('Failed to create processing state', {
				operationId,
				domainsCount: domains.length,
				error: (error as Error).message,
			});
			return false;
		}
	}

	/**
	 * Update processing state progress
	 */
	async updateProcessingState(
		operationId: string,
		status: ProcessingState['status'],
		progress?: number,
	): Promise<void>
	{
		try
		{
			const stateKey = `${this.config.keyPrefix}state:${operationId}`;
			const existingState = await this.redisClient.get(stateKey);

			if (existingState)
			{
				const state: ProcessingState = JSON.parse(existingState);
				state.status = status;
				if (progress !== undefined)
				{
					state.progress = progress;
				}

				await this.redisClient.setex(
					stateKey,
					this.config.processingStateTtl,
					JSON.stringify(state),
				);
			}
		}
		catch (error)
		{
			this.logger.warn('Failed to update processing state', {
				operationId,
				status,
				progress,
				error: error instanceof Error ? error.message : String(error),
			});
		}
	}

	/**
	 * Get processing state
	 */
	async getProcessingState(operationId: string): Promise<ProcessingState | null>
	{
		try
		{
			const stateKey = `${this.config.keyPrefix}state:${operationId}`;
			const stateData = await this.redisClient.get(stateKey);

			if (stateData)
			{
				return JSON.parse(stateData);
			}

			return null;
		}
		catch (error)
		{
			this.logger.warn('Failed to get processing state', {
				operationId,
				error: error instanceof Error ? error.message : String(error),
			});
			return null;
		}
	}

	/**
	 * Clean up processing state when operation completes
	 */
	async cleanupProcessingState(operationId: string): Promise<void>
	{
		try
		{
			const stateKey = `${this.config.keyPrefix}state:${operationId}`;
			await this.redisClient.del(stateKey);
		}
		catch (error)
		{
			this.logger.warn('Failed to cleanup processing state', {
				operationId,
				error: (error as Error).message,
			});
		}
	}

	/**
	 * Get operational metrics
	 */
	getMetrics(): {
		deduplicationHits: number;
		rateLimitChecks: number;
		negativeResultCacheHits: number;
		processingStateOperations: number;
		memoryUsageEstimate: string;
	}
	{
		// Estimate memory usage (should be <100MB)
		const estimatedMemoryMB = Math.round(
			(this.deduplicationHits * 50 // ~50 bytes per dedup key
				+ this.rateLimitChecks * 200 // ~200 bytes per rate limit bucket
				+ this.negativeResultCacheHits * 30 // ~30 bytes per negative result
				+ this.processingStateOperations * 500) // ~500 bytes per processing state
				/ (1024 * 1024),
		);

		return {
			deduplicationHits: this.deduplicationHits,
			rateLimitChecks: this.rateLimitChecks,
			negativeResultCacheHits: this.negativeResultCacheHits,
			processingStateOperations: this.processingStateOperations,
			memoryUsageEstimate: `~${estimatedMemoryMB}MB`,
		};
	}

	/**
	 * Health check for Redis operational layer
	 */
	async healthCheck(): Promise<{
		isHealthy: boolean;
		redisConnected: boolean;
		operationalKeysCount: number;
		memoryUsage: string;
		lastError?: string;
	}>
	{
		try
		{
			const isConnected = await this.redisClient.healthCheck();

			if (!isConnected)
			{
				return {
					isHealthy: false,
					redisConnected: false,
					operationalKeysCount: 0,
					memoryUsage: 'unknown',
					lastError: 'Redis connection failed',
				};
			}

			// Count operational keys
			const keyPatterns = [
				`${this.config.keyPrefix}dedup:*`,
				`${this.config.keyPrefix}rate:*`,
				`${this.config.keyPrefix}negative:*`,
				`${this.config.keyPrefix}state:*`,
			];

			let totalKeys = 0;
			for (const pattern of keyPatterns)
			{
				const keys = await this.redisClient.keys(pattern);
				totalKeys += keys.length;
			}

			const metrics = this.getMetrics();

			return {
				isHealthy: true,
				redisConnected: true,
				operationalKeysCount: totalKeys,
				memoryUsage: metrics.memoryUsageEstimate,
			};
		}
		catch (error)
		{
			return {
				isHealthy: false,
				redisConnected: false,
				operationalKeysCount: 0,
				memoryUsage: 'unknown',
				lastError: (error as Error).message,
			};
		}
	}

	/**
	 * Clear all operational caches and state
	 */
	async clearOperationalData(): Promise<{
		deduplicationKeys: number;
		rateLimitKeys: number;
		negativeResultKeys: number;
		processingStateKeys: number;
	}>
	{
		const results = {
			deduplicationKeys: 0,
			rateLimitKeys: 0,
			negativeResultKeys: 0,
			processingStateKeys: 0,
		};

		try
		{
			const keyPatterns = [
				{ pattern: `${this.config.keyPrefix}dedup:*`, type: 'deduplicationKeys' },
				{ pattern: `${this.config.keyPrefix}rate:*`, type: 'rateLimitKeys' },
				{ pattern: `${this.config.keyPrefix}negative:*`, type: 'negativeResultKeys' },
				{ pattern: `${this.config.keyPrefix}state:*`, type: 'processingStateKeys' },
			];

			for (const { pattern, type } of keyPatterns)
			{
				const keys = await this.redisClient.keys(pattern);
				if (keys.length > 0)
				{
					await this.redisClient.del(keys);
					results[type as keyof typeof results] = keys.length;
				}
			}

			this.logger.info('Cleared operational data', results);

			return results;
		}
		catch (error)
		{
			this.logger.error('Failed to clear operational data', {
				error: (error as Error).message,
			});
			throw error;
		}
	}

	/**
	 * Get current configuration
	 */
	getConfig(): RedisOperationalConfig
	{
		return { ...this.config };
	}

	/**
	 * Create operation key hash for deduplication
	 */
	createOperationKey(domains: string[]): string
	{
		// Create a hash of sorted domains for consistent deduplication
		const sortedDomains = [...domains].sort();
		const hash = this.hashString(sortedDomains.join(','));
		return `batch_${hash}`;
	}

	/**
	 * Simple string hash function
	 */
	private hashString(str: string): string
	{
		let hash = 0;
		for (let i = 0; i < str.length; i++)
		{
			const char = str.charCodeAt(i);
			hash = ((hash << 5) - hash) + char;
			hash &= hash; // Convert to 32-bit integer
		}
		return Math.abs(hash).toString(36);
	}
}

export type { RedisOperationalConfig };

export { DEFAULT_OPERATIONAL_CONFIG };

export default RedisOperationalLayer;
