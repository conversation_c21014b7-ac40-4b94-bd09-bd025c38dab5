import type DatabaseManager from '@shared/database/DatabaseManager';
import type RedisClientWrapper from '@shared/database/RedisClient';
import { logger } from '@shared/utils/Logger';
import type { DomainRepositoryType, DomainRepositoryConfigType } from './DomainRepository';
import type { RedisCacheConfig, CacheWarmingSource } from './RedisCacheLayer';
import { DEFAULT_REPOSITORY_CONFIG } from './DomainRepository';
import ScyllaDomainRepository from './ScyllaDomainRepository';
import MariaDomainRepository from './MariaDomainRepository';
import ManticoreDomainRepository from './ManticoreDomainRepository';
import BloomFilter from './BloomFilter';
import { RedisCacheLayer } from './RedisCacheLayer';

/**
 * Configuration specific to CompositeDomainRepository
 */
interface CompositeDomainRepositoryConfig extends DomainRepositoryConfigType
{
	/**
	 * Expected number of domains for bloom filter sizing
	 */
	expectedDomains: number;

	/**
	 * Bloom filter false positive rate
	 */
	bloomFilterFalsePositiveRate: number;

	/**
	 * Whether to enable Redis caching
	 */
	enableRedisCache: boolean;

	/**
	 * Redis cache configuration
	 */
	redisCacheConfig: Partial<RedisCacheConfig>;

	/**
	 * Whether to enable bloom filter fallback
	 */
	enableBloomFilter: boolean;

	/**
	 * Maximum number of concurrent database queries
	 */
	maxConcurrentQueries: number;

	/**
	 * Timeout for individual repository queries in milliseconds
	 */
	repositoryTimeout: number;
}

/**
 * Default configuration for CompositeDomainRepository
 */
const DEFAULT_COMPOSITE_CONFIG: CompositeDomainRepositoryConfig =
{
	...DEFAULT_REPOSITORY_CONFIG,
	expectedDomains: 10_000_000, // 10M domains
	bloomFilterFalsePositiveRate: 0.01, // 1% false positive rate
	enableRedisCache: true,
	redisCacheConfig:
	{
		keyPrefix: 'domain:exists:',
		positiveTtl: 86400, // 24 hours
		negativeTtl: 3600, // 1 hour
		enableWarmup: true,
		enableMetrics: true,
	},
	enableBloomFilter: true,
	maxConcurrentQueries: 3,
	repositoryTimeout: 15000, // 15 seconds per repository
};

/**
 * Composite domain repository that queries multiple databases with fallback mechanisms
 * Provides high availability and performance through bloom filter caching and Redis caching
 */
class CompositeDomainRepository implements DomainRepository
{
	private readonly logger = logger.getLogger('CompositeDomainRepository');

	private readonly config: CompositeDomainRepositoryConfig;

	private readonly scyllaRepo: ScyllaDomainRepository;

	private readonly mariaRepo: MariaDomainRepository;

	private readonly manticoreRepo: ManticoreDomainRepository;

	private readonly redisClient: RedisClientWrapper;

	private readonly cacheLayer: RedisCacheLayer;

	private readonly bloomFilter: BloomFilter;

	private readonly repositories: DomainRepository[];

	// Performance metrics
	private queryCount = 0;

	private errorCount = 0;

	private cacheHits = 0;

	private bloomFilterHits = 0;

	constructor(
		private readonly dbManager: DatabaseManager,
		config: Partial<CompositeDomainRepositoryConfig> = {},
	)
	{
		this.config = { ...DEFAULT_COMPOSITE_CONFIG, ...config };

		// Initialize individual repositories
		this.scyllaRepo = new ScyllaDomainRepository(
			this.dbManager.getScyllaClient(),
			{ batchSize: this.config.batchSize, maxRetries: this.config.maxRetries },
		);

		this.mariaRepo = new MariaDomainRepository(
			this.dbManager.getMariaClient(),
			{ batchSize: this.config.batchSize, maxRetries: this.config.maxRetries },
		);

		this.manticoreRepo = new ManticoreDomainRepository(
			this.dbManager.getManticoreClient(),
			{ batchSize: this.config.batchSize, maxRetries: this.config.maxRetries },
		);

		this.redisClient = this.dbManager.getRedisClient();

		// Initialize Redis cache layer
		this.cacheLayer = new RedisCacheLayer(this.redisClient, this.config.redisCacheConfig);

		// Initialize bloom filter with Redis persistence
		this.bloomFilter = new BloomFilter(
			this.config.expectedDomains,
			this.config.bloomFilterFalsePositiveRate,
			this.redisClient,
			{
				redisKeyPrefix: 'bloom:composite:',
				enablePersistence: true,
				autoSaveInterval: 300000, // 5 minutes
				maxAge: 86400000, // 24 hours
				rebuildThreshold: 0.05, // 5% false positive rate
				enableAutoWarming: true,
				warmingBatchSize: 10000,
			},
		);

		// Repository priority order (fastest to slowest)
		this.repositories = [this.scyllaRepo, this.manticoreRepo, this.mariaRepo];

		this.logger.info('CompositeDomainRepository initialized', {
			repositories: this.repositories.map(repo => repo.getRepositoryType()),
			config: this.config,
		});

		// Initialize bloom filter from Redis if available
		this.initializeBloomFilter();
	}

	/**
	 * Check if a single domain exists across all databases
	 */
	async hasDomain(domain: string): Promise<boolean>
	{
		const startTime = Date.now();
		this.queryCount++;

		try
		{
			// 1. Check Redis cache first
			if (this.config.enableRedisCache)
			{
				const cached = await this.cacheLayer.get(domain);
				if (cached !== null)
				{
					this.cacheHits++;
					this.logger.debug('Cache hit for single domain check', { domain, cached });
					return cached;
				}
			}

			// 2. Check bloom filter for quick negative results
			if (this.config.enableBloomFilter && !this.bloomFilter.test(domain))
			{
				this.bloomFilterHits++;
				this.logger.debug('Bloom filter negative for single domain', { domain });

				// Cache negative result
				if (this.config.enableRedisCache)
				{
					await this.cacheLayer.set(domain, false);
				}

				return false;
			}

			// 3. Query databases in parallel with timeout
			const exists = await this.queryRepositoriesForSingle(domain);

			// 4. Update caches
			if (exists)
			{
				this.bloomFilter.add(domain);
			}

			if (this.config.enableRedisCache)
			{
				await this.cacheLayer.set(domain, exists);
			}

			const executionTime = Date.now() - startTime;
			this.logger.debug('Single domain check completed', {
				domain,
				exists,
				executionTime,
			});

			return exists;
		}
		catch (error)
		{
			this.errorCount++;
			this.logger.error('Single domain check failed', {
				domain,
				error: error instanceof Error ? error.message : String(error),
			});
			throw error;
		}
	}

	/**
	 * Check existence of multiple domains in batch across all databases
	 */
	async batchHasDomains(domains: string[]): Promise<Map<string, boolean>>
	{
		if (domains.length === 0)
		{
			return new Map();
		}

		const startTime = Date.now();
		this.queryCount++;

		try
		{
			const results = new Map<string, boolean>();
			let uncachedDomains = [...domains];

			// 1. Check Redis cache for all domains
			if (this.config.enableRedisCache)
			{
				const cacheResults = await this.cacheLayer.getBatch(domains);
				cacheResults.forEach((exists, domain) =>
				{
					results.set(domain, exists);
					this.cacheHits++;
				});

				uncachedDomains = domains.filter(domain => !results.has(domain));
				this.logger.debug('Cache results', {
					totalDomains: domains.length,
					cacheHits: cacheResults.size,
					uncached: uncachedDomains.length,
				});
			}

			if (uncachedDomains.length === 0)
			{
				return results;
			}

			// 2. Check bloom filter for quick negative results
			let bloomFiltered = uncachedDomains;
			if (this.config.enableBloomFilter)
			{
				const bloomResults = this.bloomFilter.testBatch(uncachedDomains);
				bloomResults.forEach((mightExist, domain) =>
				{
					if (!mightExist)
					{
						results.set(domain, false);
						this.bloomFilterHits++;
					}
				});

				bloomFiltered = uncachedDomains.filter(domain => bloomResults.get(domain) === true);
				this.logger.debug('Bloom filter results', {
					uncached: uncachedDomains.length,
					bloomFiltered: bloomFiltered.length,
					bloomNegatives: uncachedDomains.length - bloomFiltered.length,
				});
			}

			if (bloomFiltered.length === 0)
			{
				// Cache all negative results
				if (this.config.enableRedisCache)
				{
					const negativeResults = new Map(
						uncachedDomains.filter(d => !results.has(d)).map(d => [d, false]),
					);
					await this.cacheLayer.setBatch(negativeResults);
				}
				return results;
			}

			// 3. Query databases for remaining domains
			const dbResults = await this.queryRepositoriesForBatch(bloomFiltered);

			// 4. Merge results
			dbResults.forEach((exists, domain) =>
			{
				results.set(domain, exists);
			});

			// 5. Update bloom filter with positive results
			if (this.config.enableBloomFilter)
			{
				const positiveResults = Array.from(dbResults.entries())
					.filter(([, exists]) => exists)
					.map(([domain]) => domain);

				this.bloomFilter.addBatch(positiveResults);
			}

			// 6. Cache all results
			if (this.config.enableRedisCache)
			{
				await this.cacheLayer.setBatch(dbResults);
			}

			const executionTime = Date.now() - startTime;
			const foundCount = Array.from(results.values()).filter(Boolean).length;

			this.logger.info('Batch domain check completed', {
				totalDomains: domains.length,
				foundCount,
				cacheHits: this.cacheHits,
				bloomFilterHits: this.bloomFilterHits,
				executionTime,
			});

			return results;
		}
		catch (error)
		{
			this.errorCount++;
			this.logger.error('Batch domain check failed', {
				totalDomains: domains.length,
				error: error instanceof Error ? error.message : String(error),
			});
			throw error;
		}
	}

	/**
	 * Health check for all underlying repositories
	 */
	async healthCheck(): Promise<boolean>
	{
		try
		{
			const healthChecks = await Promise.allSettled([
				this.scyllaRepo.healthCheck(),
				this.mariaRepo.healthCheck(),
				this.manticoreRepo.healthCheck(),
				this.redisClient.healthCheck(),
				this.cacheLayer.healthCheck(),
			]);

			const results = {
				scylla: healthChecks[0].status === 'fulfilled' ? healthChecks[0].value : false,
				maria: healthChecks[1].status === 'fulfilled' ? healthChecks[1].value : false,
				manticore: healthChecks[2].status === 'fulfilled' ? healthChecks[2].value : false,
				redis: healthChecks[3].status === 'fulfilled' ? healthChecks[3].value : false,
				cache: healthChecks[4].status === 'fulfilled' ? healthChecks[4].value : false,
			};

			// Consider healthy if at least one database is available
			const isHealthy = results.scylla || results.maria || results.manticore;

			this.logger.debug('Composite health check completed', { results, isHealthy });

			return isHealthy;
		}
		catch (error)
		{
			this.logger.error('Health check failed', { error: error instanceof Error ? error.message : String(error) });
			return false;
		}
	}

	/**
	 * Get repository type identifier
	 */
	getRepositoryType(): string
	{
		return 'composite';
	}

	/**
	 * Get current configuration
	 */
	getConfig(): CompositeDomainRepositoryConfig
	{
		return { ...this.config };
	}

	/**
	 * Get performance metrics
	 */
	getMetrics(): {
		queryCount: number;
		errorCount: number;
		cacheHits: number;
		bloomFilterHits: number;
		cacheHitRate: number;
		errorRate: number;
		bloomFilterStats: ReturnType<BloomFilter['getStats']>;
		cacheMetrics: ReturnType<RedisCacheLayer['getMetrics']>;
	}
	{
		return {
			queryCount: this.queryCount,
			errorCount: this.errorCount,
			cacheHits: this.cacheHits,
			bloomFilterHits: this.bloomFilterHits,
			cacheHitRate: this.queryCount > 0 ? this.cacheHits / this.queryCount : 0,
			errorRate: this.queryCount > 0 ? this.errorCount / this.queryCount : 0,
			bloomFilterStats: this.bloomFilter.getStats(),
			cacheMetrics: this.cacheLayer.getMetrics(),
		};
	}

	/**
	 * Initialize bloom filter from Redis or warm up with database data
	 */
	private async initializeBloomFilter(): Promise<void>
	{
		if (!this.config.enableBloomFilter)
		{
			return;
		}

		try
		{
			// Try to load from Redis first
			const loaded = await this.bloomFilter.loadFromRedis();

			if (loaded)
			{
				const stats = this.bloomFilter.getStats();
				this.logger.info('Bloom filter loaded from Redis', {
					itemCount: stats.itemCount,
					age: stats.age,
					needsRebuild: stats.needsRebuild,
				});

				// Check if maintenance is needed
				if (stats.needsRebuild)
				{
					this.logger.info('Bloom filter needs rebuild, scheduling maintenance');
					// Schedule rebuild in background
					setImmediate(() => this.performBloomFilterMaintenance());
				}
			}
			else
			{
				this.logger.info('No bloom filter found in Redis, starting warmup');
				// No saved filter, warm up from databases
				await this.warmupBloomFilter();
			}
		}
		catch (error)
		{
			this.logger.error('Failed to initialize bloom filter', {
				error: (error as Error).message,
			});
		}
	}

	/**
	 * Warm up the bloom filter with existing domains from databases
	 */
	async warmupBloomFilter(sampleSize: number = 100000): Promise<void>
	{
		if (!this.config.enableBloomFilter)
		{
			return;
		}

		this.logger.info('Starting bloom filter warmup', { sampleSize });

		try
		{
			// Use the enhanced warming functionality
			await this.bloomFilter.warmFromMultipleSources([
				{
					name: 'scylla',
					getDomains: async (offset: number, limit: number) => this.getSampleDomains(this.scyllaRepo, limit, offset),
					weight: 1,
				},
				{
					name: 'maria',
					getDomains: async (offset: number, limit: number) => this.getSampleDomains(this.mariaRepo, limit, offset),
					weight: 1,
				},
				{
					name: 'manticore',
					getDomains: async (offset: number, limit: number) => this.getSampleDomains(this.manticoreRepo, limit, offset),
					weight: 1,
				},
			]);

			this.logger.info('Bloom filter warmup completed', {
				bloomStats: this.bloomFilter.getStats(),
			});
		}
		catch (error)
		{
			this.logger.error('Bloom filter warmup failed', { error: (error as Error).message });
		}
	}

	/**
	 * Perform bloom filter maintenance (rebuild if needed)
	 */
	async performBloomFilterMaintenance(): Promise<void>
	{
		if (!this.config.enableBloomFilter)
		{
			return;
		}

		try
		{
			const getDomains = async (offset: number, limit: number): Promise<string[]> =>
			{
				// Get domains from all repositories
				const allDomains: string[] = [];

				const sources = [
					() => this.getSampleDomains(this.scyllaRepo, limit / 3, offset),
					() => this.getSampleDomains(this.mariaRepo, limit / 3, offset),
					() => this.getSampleDomains(this.manticoreRepo, limit / 3, offset),
				];

				for (const source of sources)
				{
					try
					{
						const domains = await source();
						allDomains.push(...domains);
					}
					catch (error)
					{
						this.logger.warn('Failed to get domains from source during maintenance', {
							error: error instanceof Error ? error.message : String(error),
						});
					}
				}

				return allDomains;
			};

			await this.bloomFilter.performMaintenance(getDomains, this.config.expectedDomains);
		}
		catch (error)
		{
			this.logger.error('Bloom filter maintenance failed', {
				error: error instanceof Error ? error.message : String(error),
			});
		}
	}

	/**
	 * Clear all caches and reset bloom filter
	 */
	async clearCaches(): Promise<void>
	{
		try
		{
			// Clear bloom filter
			this.bloomFilter.clear();

			// Delete bloom filter from Redis
			await this.bloomFilter.deleteFromRedis();

			// Clear Redis cache layer
			if (this.config.enableRedisCache)
			{
				await this.cacheLayer.clear();
			}

			// Reset metrics
			this.cacheHits = 0;
			this.bloomFilterHits = 0;

			this.logger.info('All caches cleared');
		}
		catch (error)
		{
			this.logger.error('Failed to clear caches', { error: error instanceof Error ? error.message : String(error) });
			throw error;
		}
	}

	/**
	 * Warm up the Redis cache with domains from multiple sources
	 * @param sources - Array of cache warming sources
	 */
	async warmupCache(sources: CacheWarmingSource[]): Promise<void>
	{
		if (!this.config.enableRedisCache)
		{
			this.logger.warn('Cache warmup requested but Redis cache is disabled');
			return;
		}

		await this.cacheLayer.warmup(sources);
	}

	/**
	 * Invalidate cache entries using patterns
	 * @param patterns - Invalidation patterns to apply
	 */
	async invalidateCache(patterns: Array<{
		name: string;
		pattern: string;
		ttlOverride?: number;
		deleteKeys: boolean;
	}>): Promise<void>
	{
		if (!this.config.enableRedisCache)
		{
			this.logger.warn('Cache invalidation requested but Redis cache is disabled');
			return;
		}

		await this.cacheLayer.invalidate(patterns);
	}

	/**
	 * Create a backup of the bloom filter
	 */
	async createBloomFilterBackup(): Promise<string>
	{
		if (!this.config.enableBloomFilter)
		{
			throw new Error('Bloom filter not enabled');
		}

		return this.bloomFilter.createBackup();
	}

	/**
	 * Restore bloom filter from backup
	 */
	async restoreBloomFilterFromBackup(backupKey: string): Promise<boolean>
	{
		if (!this.config.enableBloomFilter)
		{
			throw new Error('Bloom filter not enabled');
		}

		return this.bloomFilter.restoreFromBackup(backupKey);
	}

	/**
	 * List available bloom filter backups
	 */
	async listBloomFilterBackups(): Promise<string[]>
	{
		if (!this.config.enableBloomFilter)
		{
			return [];
		}

		return this.bloomFilter.listBackups();
	}

	/**
	 * Get bloom filter configuration and stats
	 */
	getBloomFilterInfo(): {
		config: ReturnType<BloomFilter['getConfig']>;
		stats: ReturnType<BloomFilter['getStats']>;
	}
	{
		return {
			config: this.bloomFilter.getConfig(),
			stats: this.bloomFilter.getStats(),
		};
	}

	/**
	 * Cleanup resources
	 */
	destroy(): void
	{
		this.bloomFilter.destroy();
		this.cacheLayer.destroy();
	}

	/**
	 * Query a single domain across repositories with failover
	 */
	private async queryRepositoriesForSingle(domain: string): Promise<boolean>
	{
		const promises = this.repositories.map(async (repo) =>
		{
			try
			{
				const result = await Promise.race([
					repo.hasDomain(domain),
					this.createTimeout(this.config.repositoryTimeout),
				]);

				return { repo: repo.getRepositoryType(), result, error: null };
			}
			catch (error)
			{
				return { repo: repo.getRepositoryType(), result: false, error: error as Error };
			}
		});

		const results = await Promise.allSettled(promises);

		// Return true if any repository found the domain
		for (const result of results)
		{
			if (result.status === 'fulfilled' && result.value.result === true)
			{
				return true;
			}
		}

		// Log any errors
		results.forEach((result) =>
		{
			if (result.status === 'fulfilled' && result.value.error)
			{
				this.logger.warn('Repository query failed', {
					repo: result.value.repo,
					domain,
					error: result.value.error.message,
				});
			}
		});

		return false;
	}

	/**
	 * Query multiple domains across repositories with failover
	 */
	private async queryRepositoriesForBatch(domains: string[]): Promise<Map<string, boolean>>
	{
		const results = new Map<string, boolean>();

		// Initialize all domains as not found
		domains.forEach((domain) =>
		{
			results.set(domain, false);
		});

		const promises = this.repositories.map(async (repo) =>
		{
			try
			{
				const result = await Promise.race([
					repo.batchHasDomains(domains),
					this.createTimeoutMap(domains, this.config.repositoryTimeout),
				]);

				return { repo: repo.getRepositoryType(), result, error: null };
			}
			catch (error)
			{
				return {
					repo: repo.getRepositoryType(),
					result: new Map<string, boolean>(),
					error: error as Error,
				};
			}
		});

		const repositoryResults = await Promise.allSettled(promises);

		// Merge results - domain exists if found in any repository
		repositoryResults.forEach((result) =>
		{
			if (result.status === 'fulfilled')
			{
				if (result.value.error)
				{
					this.logger.warn('Repository batch query failed', {
						repo: result.value.repo,
						domainsCount: domains.length,
						error: result.value.error.message,
					});
				}
				else if (result.value.result)
				{
					result.value.result.forEach((exists, domain) =>
					{
						if (exists)
						{
							results.set(domain, true);
						}
					});
				}
			}
		});

		return results;
	}


	/**
	 * Get sample domains from a repository for bloom filter warmup
	 */
	private async getSampleDomains(
		repository: DomainRepository,
		sampleSize: number,
		offset: number = 0,
	): Promise<string[]>
	{
		try
		{
			const repositoryType = repository.getRepositoryType();

			// Use repository-specific methods to get sample domains
			switch (repositoryType)
			{
				case 'scylla':
					return await this.getSampleDomainsFromScylla(repository as any, sampleSize, offset);

				case 'maria':
					return await this.getSampleDomainsFromMaria(repository as any, sampleSize, offset);

				case 'manticore':
					return await this.getSampleDomainsFromManticore(repository as any, sampleSize, offset);

				default:
					this.logger.warn('Unknown repository type for sample fetching', { repositoryType });
					return [];
			}
		}
		catch (error)
		{
			this.logger.error('Failed to get sample domains', {
				repository: repository.getRepositoryType(),
				sampleSize,
				offset,
				error: error instanceof Error ? error.message : String(error),
			});
			return [];
		}
	}

	/**
	 * Get sample domains from ScyllaDB
	 */
	private async getSampleDomainsFromScylla(
		repository: any,
		sampleSize: number,
		offset: number,
	): Promise<string[]>
	{
		try
		{
			// Use ScyllaDB's token-based sampling for efficient random sampling
			const query = `
				SELECT domain
				FROM domain_analysis
				WHERE token(domain) >= ?
				LIMIT ?
			`;

			// Generate a random token for sampling
			const randomToken = Math.floor(Math.random() * Number.MAX_SAFE_INTEGER);
			const result = await repository.scyllaClient.execute(query, [randomToken, sampleSize]);

			return result.rows.map((row: any) => row.domain);
		}
		catch (error)
		{
			this.logger.error('Failed to get sample domains from Scylla', {
				error: error instanceof Error ? error.message : String(error),
			});
			return [];
		}
	}

	/**
	 * Get sample domains from MariaDB
	 */
	private async getSampleDomainsFromMaria(
		repository: any,
		sampleSize: number,
		offset: number,
	): Promise<string[]>
	{
		try
		{
			// Use MariaDB's TABLESAMPLE or ORDER BY RAND() for sampling
			const query = `
				SELECT DISTINCT domain
				FROM domain_analysis
				ORDER BY RAND()
				LIMIT ? OFFSET ?
			`;

			const result = await repository.mariaClient.execute(query, [sampleSize, offset]);
			return result.map((row: any) => row.domain);
		}
		catch (error)
		{
			this.logger.error('Failed to get sample domains from Maria', {
				error: error instanceof Error ? error.message : String(error),
			});
			return [];
		}
	}

	/**
	 * Get sample domains from Manticore Search
	 */
	private async getSampleDomainsFromManticore(
		repository: any,
		sampleSize: number,
		offset: number,
	): Promise<string[]>
	{
		try
		{
			// Use Manticore's search with random ordering
			const searchResult = await repository.manticoreClient.searchDomains({
				query: '*',
				limit: sampleSize,
				offset,
				sort: 'RAND()',
			});

			return searchResult.results.map((result: any) => result.domain);
		}
		catch (error)
		{
			this.logger.error('Failed to get sample domains from Manticore', {
				error: error instanceof Error ? error.message : String(error),
			});
			return [];
		}
	}

	/**
	 * Create a timeout promise that rejects after specified milliseconds
	 */
	private createTimeout(ms: number): Promise<never>
	{
		return new Promise((_, reject) =>
		{
			setTimeout(() => reject(new Error(`Query timeout after ${ms}ms`)), ms);
		});
	}

	/**
	 * Create a timeout promise that returns empty map after specified milliseconds
	 */
	private createTimeoutMap(domains: string[], ms: number): Promise<Map<string, boolean>>
	{
		return new Promise((resolve) =>
		{
			setTimeout(() =>
			{
				const timeoutMap = new Map<string, boolean>();
				domains.forEach(domain => timeoutMap.set(domain, false));
				resolve(timeoutMap);
			}, ms);
		});
	}
}

export type { CompositeDomainRepositoryConfig };

export { DEFAULT_COMPOSITE_CONFIG };

export default CompositeDomainRepository;
