import type ManticoreClient from '@shared/database/ManticoreClient';
import { logger } from '@shared/utils/Logger';

/**
 * Configuration for enhanced Manticore domain index operations
 */
type EnhancedManticoreConfig =
{
	/**
	 * Maximum batch size for existence queries (10K-100K domains)
	 */
	maxBatchSize: number;

	/**
	 * Optimal batch size for best performance
	 */
	optimalBatchSize: number;

	/**
	 * Query timeout in milliseconds
	 */
	queryTimeout: number;

	/**
	 * Maximum number of retry attempts
	 */
	maxRetries: number;

	/**
	 * Connection pool size
	 */
	connectionPoolSize: number;

	/**
	 * Enable query result caching at client level
	 */
	enableClientCache: boolean;

	/**
	 * Client cache TTL in milliseconds
	 */
	clientCacheTtl: number;

	/**
	 * Maximum cache size (number of entries)
	 */
	maxCacheSize: number;

	/**
	 * Enable performance monitoring
	 */
	enableMetrics: boolean;

	/**
	 * Index name for domain existence checks
	 */
	indexName: string;
};

/**
 * Default configuration for enhanced Manticore operations
 */
const DEFAULT_ENHANCED_CONFIG: EnhancedManticoreConfig = {
	maxBatchSize: 100000, // 100K domains per query
	optimalBatchSize: 50000, // 50K domains for optimal performance
	queryTimeout: 10000, // 10 seconds
	maxRetries: 3,
	connectionPoolSize: 20,
	enableClientCache: true,
	clientCacheTtl: 300000, // 5 minutes
	maxCacheSize: 100000, // 100K cached results
	enableMetrics: true,
	indexName: 'domains_index',
};

/**
 * Result of batch existence check with enhanced metrics
 */
type EnhancedBatchResult =
{
	/**
	 * Map of domain to existence status
	 */
	results: Map<string, boolean>;

	/**
	 * Domains that had errors during checking
	 */
	errors: Map<string, Error>;

	/**
	 * Enhanced performance metrics
	 */
	metrics: {
		totalDomains: number;
		foundCount: number;
		errorCount: number;
		executionTime: number;
		queryTime: number;
		cacheHits: number;
		batchCount: number;
		averageBatchSize: number;
		retryCount: number;
		connectionPoolUtilization: number;
	};
};

/**
 * Cache entry for client-level result caching
 */
type CacheEntry =
{
	result: boolean;
	timestamp: number;
	hitCount: number;
};

/**
 * Enhanced Manticore domain index optimized for high-performance existence checking
 * Supports batch operations with 10K-100K domains per query with intelligent caching
 */
class EnhancedManticoreDomainIndex
{
	private readonly logger = logger.getLogger('EnhancedManticoreDomainIndex');

	private readonly config: EnhancedManticoreConfig;

	// Client-level cache for query results
	private readonly clientCache = new Map<string, CacheEntry>();

	// Performance metrics
	private totalQueries = 0;

	private totalDomains = 0;

	private totalErrors = 0;

	private totalCacheHits = 0;

	private totalRetries = 0;

	private connectionPoolActive = 0;

	// Connection pool management
	private readonly activeConnections = new Set<string>();

	constructor(
		private readonly manticoreClient: ManticoreClient,
		config: Partial<EnhancedManticoreConfig> = {},
	)
	{
		this.config = { ...DEFAULT_ENHANCED_CONFIG, ...config };

		this.logger.info('EnhancedManticoreDomainIndex initialized', {
			config: this.config,
		});

		// Start cache cleanup interval
		if (this.config.enableClientCache)
		{
			this.startCacheCleanup();
		}
	}

	/**
	 * Check existence of multiple domains using optimized Manticore queries
	 */
	async batchCheckExistence(domains: string[]): Promise<EnhancedBatchResult>
	{
		if (domains.length === 0)
		{
			return this.createEmptyResult();
		}

		const startTime = Date.now();
		const queryId = this.generateQueryId();

		this.totalQueries += 1;
		this.totalDomains += domains.length;

		try
		{
			// Step 1: Check client cache for cached results
			const { uncachedDomains, cacheResults, cacheHits } = this.checkClientCache(domains);

			this.totalCacheHits += cacheHits;

			if (uncachedDomains.length === 0)
			{
				// All domains were in cache
				return this.createCacheOnlyResult(domains, cacheResults, startTime, cacheHits);
			}

			// Step 2: Process uncached domains in optimized batches
			const batchResults = await this.processDomainsInBatches(uncachedDomains, queryId);

			// Step 3: Update client cache with new results
			if (this.config.enableClientCache)
			{
				this.updateClientCache(batchResults.results);
			}

			// Step 4: Combine cache and query results
			const finalResults = this.combineResults(domains, cacheResults, batchResults.results);

			const result: EnhancedBatchResult = {
				results: finalResults,
				errors: batchResults.errors,
				metrics: {
					totalDomains: domains.length,
					foundCount: Array.from(finalResults.values()).filter(Boolean).length,
					errorCount: batchResults.errors.size,
					executionTime: Date.now() - startTime,
					queryTime: batchResults.queryTime,
					cacheHits,
					batchCount: batchResults.batchCount,
					averageBatchSize: batchResults.averageBatchSize,
					retryCount: batchResults.retryCount,
					connectionPoolUtilization: this.getConnectionPoolUtilization(),
				},
			};

			this.logger.info('Enhanced batch existence check completed', {
				queryId,
				...result.metrics,
			});

			return result;
		}
		catch (error)
		{
			this.totalErrors += 1;
			this.logger.error('Enhanced batch existence check failed', {
				queryId,
				domainsCount: domains.length,
				error: (error as Error).message,
			});

			return {
				results: new Map(domains.map(d => [d, false])),
				errors: new Map(domains.map(d => [d, error as Error])),
				metrics: {
					totalDomains: domains.length,
					foundCount: 0,
					errorCount: domains.length,
					executionTime: Date.now() - startTime,
					queryTime: 0,
					cacheHits: 0,
					batchCount: 0,
					averageBatchSize: 0,
					retryCount: 0,
					connectionPoolUtilization: this.getConnectionPoolUtilization(),
				},
			};
		}
	}

	/**
	 * Check existence of a single domain (convenience method)
	 */
	async checkExistence(domain: string): Promise<boolean>
	{
		const result = await this.batchCheckExistence([domain]);
		return result.results.get(domain) || false;
	}

	/**
	 * Optimize Manticore queries using IN operator for batch operations
	 */
	async optimizedBatchQuery(domains: string[]): Promise<{
		results: Map<string, boolean>;
		queryTime: number;
		retryCount: number;
	}>
	{
		const startTime = Date.now();
		let retryCount = 0;
		let lastError: Error | null = null;

		// Ensure batch size doesn't exceed maximum
		if (domains.length > this.config.maxBatchSize)
		{
			throw new Error(`Batch size ${domains.length} exceeds maximum ${this.config.maxBatchSize}`);
		}

		for (let attempt = 0; attempt <= this.config.maxRetries; attempt++)
		{
			try
			{
				const connectionId = this.acquireConnection();

				try
				{
					// Use optimized IN operator query for existence checking
					const searchResult = await Promise.race([
						this.manticoreClient.compareDomains(domains),
						this.createTimeout(this.config.queryTimeout),
					]);

					const results = new Map<string, boolean>();

					// Initialize all domains as not found
					domains.forEach((domain) =>
					{
						results.set(domain, false);
					});

					// Mark found domains as true
					searchResult.results.forEach((result) =>
					{
						const domain = result.domain as string;
						if (results.has(domain))
						{
							results.set(domain, true);
						}
					});

					const actualQueryTime = Date.now() - startTime;

					this.logger.debug('Optimized batch query completed', {
						batchSize: domains.length,
						foundCount: searchResult.results.length,
						queryTime: actualQueryTime,
						actualTime: actualQueryTime,
						retryCount,
					});

					return {
						results,
						queryTime: actualQueryTime,
						retryCount,
					};
				}
				finally
				{
					this.releaseConnection(connectionId);
				}
			}
			catch (error)
			{
				lastError = error as Error;
				retryCount += 1;

				if (attempt < this.config.maxRetries)
				{
					const backoffTime = Math.min(1000 * 2 ** attempt, 5000);
					this.logger.warn('Batch query failed, retrying', {
						attempt: attempt + 1,
						maxRetries: this.config.maxRetries,
						backoffTime,
						error: lastError.message,
					});

					await new Promise((resolve) =>
					{
						setTimeout(resolve, backoffTime);
					});
				}
			}
		}

		this.totalRetries += retryCount;
		throw lastError || new Error('All retry attempts failed');
	}

	/**
	 * Get enhanced performance metrics
	 */
	getMetrics(): {
		totalQueries: number;
		totalDomains: number;
		totalErrors: number;
		totalCacheHits: number;
		totalRetries: number;
		errorRate: number;
		cacheHitRate: number;
		averageDomainsPerQuery: number;
		connectionPoolUtilization: number;
		cacheSize: number;
		cacheEfficiency: number;
	}
	{
		const cacheEfficiency = this.clientCache.size > 0
			? Array.from(this.clientCache.values()).reduce(
				(sum, entry) => sum + entry.hitCount, 0,
			) / this.clientCache.size
			: 0;

		return {
			totalQueries: this.totalQueries,
			totalDomains: this.totalDomains,
			totalErrors: this.totalErrors,
			totalCacheHits: this.totalCacheHits,
			totalRetries: this.totalRetries,
			errorRate: this.totalQueries > 0 ? this.totalErrors / this.totalQueries : 0,
			cacheHitRate: this.totalDomains > 0 ? this.totalCacheHits / this.totalDomains : 0,
			averageDomainsPerQuery: this.totalQueries > 0 ? this.totalDomains / this.totalQueries : 0,
			connectionPoolUtilization: this.getConnectionPoolUtilization(),
			cacheSize: this.clientCache.size,
			cacheEfficiency,
		};
	}

	/**
	 * Health check with enhanced connection monitoring
	 */
	async healthCheck(): Promise<{
		isHealthy: boolean;
		indexExists: boolean;
		connectionPoolStatus: string;
		cacheStatus: string;
		lastError?: string;
	}>
	{
		try
		{
			const [isHealthy, indexExists] = await Promise.all([
				this.manticoreClient.healthCheck(),
				this.manticoreClient.indexExists(this.config.indexName),
			]);

			return {
				isHealthy,
				indexExists,
				connectionPoolStatus: this.getConnectionPoolStatus(),
				cacheStatus: this.getCacheStatus(),
			};
		}
		catch (error)
		{
			return {
				isHealthy: false,
				indexExists: false,
				connectionPoolStatus: 'error',
				cacheStatus: this.getCacheStatus(),
				lastError: (error as Error).message,
			};
		}
	}

	/**
	 * Clear client cache
	 */
	clearCache(): void
	{
		this.clientCache.clear();
		this.logger.info('Client cache cleared');
	}

	/**
	 * Get current configuration
	 */
	getConfig(): EnhancedManticoreConfig
	{
		return { ...this.config };
	}

	/**
	 * Optimize batch size based on performance metrics
	 */
	getOptimalBatchSize(): number
	{
		// Start with configured optimal size
		let optimalSize = this.config.optimalBatchSize;

		// Adjust based on error rate
		if (this.totalQueries > 10)
		{
			const errorRate = this.totalErrors / this.totalQueries;

			if (errorRate > 0.1)
			{
				// High error rate - reduce batch size
				optimalSize = Math.max(1000, Math.floor(optimalSize * 0.8));
			}
			else if (errorRate < 0.01)
			{
				// Low error rate - can increase batch size
				optimalSize = Math.min(this.config.maxBatchSize, Math.floor(optimalSize * 1.2));
			}
		}

		return optimalSize;
	}

	/**
	 * Check client cache for existing results
	 */
	private checkClientCache(domains: string[]): {
		uncachedDomains: string[];
		cacheResults: Map<string, boolean>;
		cacheHits: number;
	}
	{
		if (!this.config.enableClientCache)
		{
			return {
				uncachedDomains: domains,
				cacheResults: new Map(),
				cacheHits: 0,
			};
		}

		const cacheResults = new Map<string, boolean>();
		const uncachedDomains: string[] = [];
		let cacheHits = 0;
		const now = Date.now();

		for (const domain of domains)
		{
			const cacheEntry = this.clientCache.get(domain);

			if (cacheEntry && (now - cacheEntry.timestamp) < this.config.clientCacheTtl)
			{
				cacheResults.set(domain, cacheEntry.result);
				cacheEntry.hitCount += 1;
				cacheHits += 1;
			}
			else
			{
				uncachedDomains.push(domain);
				// Remove expired entry
				if (cacheEntry)
				{
					this.clientCache.delete(domain);
				}
			}
		}

		return { uncachedDomains, cacheResults, cacheHits };
	}

	/**
	 * Process domains in optimized batches
	 */
	private async processDomainsInBatches(domains: string[], queryId: string): Promise<{
		results: Map<string, boolean>;
		errors: Map<string, Error>;
		queryTime: number;
		batchCount: number;
		averageBatchSize: number;
		retryCount: number;
	}>
	{
		const results = new Map<string, boolean>();
		const errors = new Map<string, Error>();
		let totalQueryTime = 0;
		let totalRetries = 0;
		const batchSize = this.getOptimalBatchSize();

		const batches: string[][] = [];
		for (let i = 0; i < domains.length; i += batchSize)
		{
			batches.push(domains.slice(i, i + batchSize));
		}

		this.logger.debug('Processing domains in batches', {
			queryId,
			totalDomains: domains.length,
			batchCount: batches.length,
			batchSize,
		});

		// Process batches with controlled concurrency
		const maxConcurrentBatches = Math.min(3, this.config.connectionPoolSize);
		for (let i = 0; i < batches.length; i += maxConcurrentBatches)
		{
			const concurrentBatches = batches.slice(i, i + maxConcurrentBatches);

			const batchPromises = concurrentBatches.map(async (batch) =>
			{
				try
				{
					const batchResult = await this.optimizedBatchQuery(batch);

					// Merge results
					batchResult.results.forEach((exists, domain) =>
					{
						results.set(domain, exists);
					});

					return {
						queryTime: batchResult.queryTime,
						retryCount: batchResult.retryCount,
						error: null,
						batch,
					};
				}
				catch (error)
				{
					// Mark all domains in failed batch as errors
					batch.forEach((domain) =>
					{
						errors.set(domain, error as Error);
						// Also set result as false for failed domains
						results.set(domain, false);
					});

					return {
						queryTime: 0,
						retryCount: 0,
						error: error as Error,
						batch,
					};
				}
			});

			const batchResults = await Promise.all(batchPromises);

			// Aggregate metrics outside the loop to avoid closure issues
			for (const result of batchResults)
			{
				totalQueryTime += result.queryTime;
				totalRetries += result.retryCount;
			}
		}

		return {
			results,
			errors,
			queryTime: totalQueryTime,
			batchCount: batches.length,
			averageBatchSize: domains.length / batches.length,
			retryCount: totalRetries,
		};
	}

	/**
	 * Update client cache with new results
	 */
	private updateClientCache(results: Map<string, boolean>): void
	{
		const now = Date.now();

		// Check cache size limit before adding new entries
		while (this.clientCache.size + results.size > this.config.maxCacheSize)
		{
			this.evictOldCacheEntries();
		}

		results.forEach((exists, domain) =>
		{
			this.clientCache.set(domain, {
				result: exists,
				timestamp: now,
				hitCount: 0,
			});
		});
	}

	/**
	 * Evict old cache entries to maintain size limit
	 */
	private evictOldCacheEntries(): void
	{
		if (this.clientCache.size === 0)
		{
			return;
		}

		const entries = Array.from(this.clientCache.entries());

		// Sort by timestamp (oldest first) and hit count (least used first)
		entries.sort(([, a], [, b]) =>
		{
			if (a.timestamp !== b.timestamp)
			{
				return a.timestamp - b.timestamp;
			}
			return a.hitCount - b.hitCount;
		});

		// Remove oldest 50% of entries to make room
		const toRemove = Math.max(1, Math.floor(entries.length * 0.5));
		for (let i = 0; i < toRemove; i++)
		{
			this.clientCache.delete(entries[i][0]);
		}

		this.logger.debug('Evicted old cache entries', {
			removed: toRemove,
			remaining: this.clientCache.size,
		});
	}

	/**
	 * Combine cache and query results
	 */
	private combineResults(
		allDomains: string[],
		cacheResults: Map<string, boolean>,
		queryResults: Map<string, boolean>,
	): Map<string, boolean>
	{
		const finalResults = new Map<string, boolean>();

		allDomains.forEach((domain) =>
		{
			if (cacheResults.has(domain))
			{
				finalResults.set(domain, cacheResults.get(domain)!);
			}
			else
			{
				finalResults.set(domain, queryResults.get(domain) || false);
			}
		});

		return finalResults;
	}

	/**
	 * Connection pool management
	 */
	private acquireConnection(): string
	{
		const connectionId = `conn_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
		this.activeConnections.add(connectionId);
		this.connectionPoolActive = Math.max(this.connectionPoolActive, this.activeConnections.size);
		return connectionId;
	}

	private releaseConnection(connectionId: string): void
	{
		this.activeConnections.delete(connectionId);
	}

	private getConnectionPoolUtilization(): number
	{
		return this.config.connectionPoolSize > 0
			? this.activeConnections.size / this.config.connectionPoolSize
			: 0;
	}

	private getConnectionPoolStatus(): string
	{
		const utilization = this.getConnectionPoolUtilization();
		if (utilization < 0.5) return 'low';
		if (utilization < 0.8) return 'normal';
		if (utilization < 0.95) return 'high';
		return 'saturated';
	}

	/**
	 * Cache management
	 */
	private getCacheStatus(): string
	{
		if (!this.config.enableClientCache) return 'disabled';

		const utilization = this.clientCache.size / this.config.maxCacheSize;
		if (utilization < 0.5) return 'low';
		if (utilization < 0.8) return 'normal';
		if (utilization < 0.95) return 'high';
		return 'full';
	}

	private startCacheCleanup(): void
	{
		// Clean up expired cache entries every 5 minutes
		// Don't start interval in test environment to avoid hanging tests
		if (process.env.NODE_ENV === 'test' || process.env.VITEST)
		{
			return;
		}

		setInterval(() =>
		{
			const now = Date.now();
			let removed = 0;

			for (const [domain, entry] of this.clientCache.entries())
			{
				if (now - entry.timestamp > this.config.clientCacheTtl)
				{
					this.clientCache.delete(domain);
					removed += 1;
				}
			}

			if (removed > 0)
			{
				this.logger.debug('Cache cleanup completed', {
					removed,
					remaining: this.clientCache.size,
				});
			}
		}, 300000); // 5 minutes
	}

	/**
	 * Utility methods
	 */
	private createEmptyResult(): EnhancedBatchResult
	{
		return {
			results: new Map(),
			errors: new Map(),
			metrics: {
				totalDomains: 0,
				foundCount: 0,
				errorCount: 0,
				executionTime: 0,
				queryTime: 0,
				cacheHits: 0,
				batchCount: 0,
				averageBatchSize: 0,
				retryCount: 0,
				connectionPoolUtilization: 0,
			},
		};
	}

	private createCacheOnlyResult(
		domains: string[],
		cacheResults: Map<string, boolean>,
		startTime: number,
		cacheHits: number,
	): EnhancedBatchResult
	{
		const results = new Map<string, boolean>();
		domains.forEach((domain) =>
		{
			results.set(domain, cacheResults.get(domain) || false);
		});

		return {
			results,
			errors: new Map(),
			metrics: {
				totalDomains: domains.length,
				foundCount: Array.from(results.values()).filter(Boolean).length,
				errorCount: 0,
				executionTime: Date.now() - startTime,
				queryTime: 0,
				cacheHits,
				batchCount: 0,
				averageBatchSize: 0,
				retryCount: 0,
				connectionPoolUtilization: this.getConnectionPoolUtilization(),
			},
		};
	}

	private generateQueryId(): string
	{
		return `query_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
	}

	private createTimeout(ms: number): Promise<never>
	{
		return new Promise((_, reject) =>
		{
			setTimeout(() => reject(new Error(`Query timeout after ${ms}ms`)), ms);
		});
	}
}

export type { EnhancedManticoreConfig };

export { DEFAULT_ENHANCED_CONFIG };

export default EnhancedManticoreDomainIndex;
