import type ScyllaClient from '@shared/database/ScyllaClient';
import { logger } from '@shared/utils/Logger';
import type { DomainRepository, DomainRepositoryConfig, BatchCheckResult } from './DomainRepository';
import { DEFAULT_REPOSITORY_CONFIG } from './DomainRepository';

/**
 * ScyllaDB implementation of domain repository for high-performance domain existence checking
 * Optimized for batch queries and handles ScyllaDB-specific query patterns
 */
class ScyllaDomainRepository implements DomainRepository
{
	private readonly logger = logger.getLogger('ScyllaDomainRepository');

	private readonly config: DomainRepositoryConfig;

	constructor(
		private readonly scyllaClient: ScyllaClient,
		config: Partial<DomainRepositoryConfig> = {},
	)
	{
		this.config = { ...DEFAULT_REPOSITORY_CONFIG, ...config };
	}


	/**
	 * Check if a single domain exists in ScyllaDB
	 */
	async hasDomain(domain: string): Promise<boolean>
	{
		try
		{
			const startTime = Date.now();

			const query = 'SELECT domain FROM domain_analysis WHERE domain = ? LIMIT 1';
			const result = await this.scyllaClient.execute(query, [domain], {
				maxRetries: this.config.maxRetries,
			});

			const executionTime = Date.now() - startTime;
			this.logger.debug(`Single domain check completed in ${executionTime}ms`, {
				domain,
				found: result.rows.length > 0,
			});

			return result.rows.length > 0;
		}
		catch (error)
		{
			this.logger.error('Failed to check single domain existence in ScyllaDB', {
				domain,
				error: error instanceof Error ? error.message : String(error),
			});
			throw error;
		}
	}

	/**
	 * Check existence of multiple domains in batch with ScyllaDB optimization
	 */
	async batchHasDomains(domains: string[]): Promise<Map<string, boolean>>
	{
		if (domains.length === 0)
		{
			return new Map();
		}

		const startTime = Date.now();
		const results = new Map<string, boolean>();
		const errors = new Map<string, Error>();

		try
		{
			// Process domains in batches to avoid query size limits
			for (let i = 0; i < domains.length; i += this.config.batchSize)
			{
				const batch = domains.slice(i, i + this.config.batchSize);
				const batchResults = await this.processBatch(batch);

				// Merge batch results
				batchResults.results.forEach((exists, domain) =>
				{
					results.set(domain, exists);
				});

				// Merge batch errors
				batchResults.errors.forEach((error, domain) =>
				{
					errors.set(domain, error);
				});
			}

			const executionTime = Date.now() - startTime;
			const foundCount = Array.from(results.values()).filter(Boolean).length;

			this.logger.info('Batch domain check completed', {
				totalDomains: domains.length,
				foundCount,
				errorCount: errors.size,
				executionTime,
				batchSize: this.config.batchSize,
			});

			return results;
		}
		catch (error)
		{
			this.errorCount++;
			this.logger.error('Batch domain check failed', {
				totalDomains: domains.length,
				error: error instanceof Error ? error.message : String(error),
			});
			throw error;
		}
	}

	/**
	 * Process a single batch of domains with ScyllaDB IN query
	 */
	private async processBatch(domains: string[]): Promise<BatchCheckResult>
	{
		const startTime = Date.now();
		const results = new Map<string, boolean>();
		const errors = new Map<string, Error>();

		try
		{
			// Initialize all domains as not found
			domains.forEach((domain) =>
			{
				results.set(domain, false);
			});

			// Use IN query for batch checking - ScyllaDB optimized
			const placeholders = domains.map(() => '?').join(',');
			const query = `SELECT domain FROM domain_analysis WHERE domain IN (${placeholders})`;

			const result = await this.scyllaClient.execute(query, domains, {
				maxRetries: this.config.maxRetries,
			});

			// Mark found domains as true
			result.rows.forEach((row) =>
			{
				const domain = row.domain as string;
				if (results.has(domain))
				{
					results.set(domain, true);
				}
			});

			const executionTime = Date.now() - startTime;
			const foundCount = Array.from(results.values()).filter(Boolean).length;

			this.logger.debug('Batch processed successfully', {
				batchSize: domains.length,
				foundCount,
				executionTime,
			});

			return {
				results,
				errors,
				totalProcessed: domains.length,
				foundCount,
				executionTime,
			};
		}
		catch (error)
		{
			this.logger.error('Batch processing failed', {
				batchSize: domains.length,
				error: error instanceof Error ? error.message : String(error),
			});

			// Mark all domains in this batch as errors
			const errorObj = error instanceof Error ? error : new Error(String(error));
			domains.forEach((domain) =>
			{
				errors.set(domain, errorObj);
				results.delete(domain);
			});

			return {
				results,
				errors,
				totalProcessed: 0,
				foundCount: 0,
				executionTime: Date.now() - startTime,
			};
		}
	}

	/**
	 * Health check for ScyllaDB connection
	 */
	async healthCheck(): Promise<boolean>
	{
		try
		{
			const isHealthy = await this.scyllaClient.healthCheck();
			this.logger.debug('ScyllaDB health check result', { isHealthy });
			return isHealthy;
		}
		catch (error)
		{
			this.logger.error('ScyllaDB health check failed', { error: error instanceof Error ? error.message : String(error) });
			return false;
		}
	}

	/**
	 * Get repository type identifier
	 */
	getRepositoryType(): string
	{
		return 'scylla';
	}

	/**
	 * Get current configuration
	 */
	getConfig(): DomainRepositoryConfig
	{
		return { ...this.config };
	}

	/**
	 * Get performance metrics for monitoring
	 */
	async getMetrics(): Promise<{
		connectionStatus: boolean;
		totalQueries: number;
		errorRate: number;
	}>
	{
		const connectionStatus = await this.healthCheck();

		return {
			connectionStatus,
			totalQueries: 0,
			errorRate: 0,
		};
	}
}

export default ScyllaDomainRepository;
