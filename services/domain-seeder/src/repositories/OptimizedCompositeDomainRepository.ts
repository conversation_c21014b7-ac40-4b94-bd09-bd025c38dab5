import type DatabaseManager from '@shared/database/DatabaseManager';
import { logger } from '@shared/utils/Logger';
import type { DomainRepository, DomainRepositoryConfig } from './DomainRepository';
import type { OptimizedDomainExistenceConfig } from './OptimizedDomainExistenceService';
import type { EnhancedManticoreConfig } from './EnhancedManticoreDomainIndex';
import type { RedisOperationalConfig } from './RedisOperationalLayer';
import { DEFAULT_REPOSITORY_CONFIG } from './DomainRepository';
import OptimizedDomainExistenceService from './OptimizedDomainExistenceService';
import EnhancedManticoreDomainIndex from './EnhancedManticoreDomainIndex';
import RedisOperationalLayer from './RedisOperationalLayer';
import ScyllaDomainRepository from './ScyllaDomainRepository';

/**
 * Configuration for OptimizedCompositeDomainRepository
 */
export interface OptimizedCompositeConfig extends DomainRepositoryConfig
{
	/**
	 * Configuration for the optimized domain existence service
	 */
	existenceServiceConfig: Partial<OptimizedDomainExistenceConfig>;

	/**
	 * Configuration for the enhanced Manticore domain index
	 */
	manticoreConfig: Partial<EnhancedManticoreConfig>;

	/**
	 * Configuration for the Redis operational layer
	 */
	redisOperationalConfig: Partial<RedisOperationalConfig>;

	/**
	 * Enable automatic failover to ScyllaDB when Manticore is unavailable
	 */
	enableAutomaticFailover: boolean;

	/**
	 * Enable performance monitoring and metrics collection
	 */
	enablePerformanceMonitoring: boolean;

	/**
	 * Enable Manticore operations
	 */
	enableManticore: boolean;

	/**
	 * Health check interval in milliseconds
	 */
	healthCheckInterval: number;

	/**
	 * Maximum acceptable error rate before triggering failover (0.0 to 1.0)
	 */
	maxErrorRate: number;
}

/**
 * Default configuration for OptimizedCompositeDomainRepository
 */
const DEFAULT_OPTIMIZED_COMPOSITE_CONFIG: OptimizedCompositeConfig =
{
	...DEFAULT_REPOSITORY_CONFIG,
	existenceServiceConfig: {},
	manticoreConfig: {},
	redisOperationalConfig: {},
	enableAutomaticFailover: true,
	enablePerformanceMonitoring: true,
	enableManticore: true,
	healthCheckInterval: 30000, // 30 seconds
	maxErrorRate: 0.1, // 10% error rate threshold
};

/**
 * Health status for the composite repository
 */
type CompositeHealthStatus =
{
	overall: boolean;
	manticore: {
		healthy: boolean;
		indexExists: boolean;
		connectionPoolStatus: string;
		cacheStatus: string;
		lastError?: string;
	};
	scylla: boolean;
	redis: {
		healthy: boolean;
		operationalKeysCount: number;
		memoryUsage: string;
		lastError?: string;
	};
	existenceService: {
		manticore: boolean;
		scylla: boolean;
		redis: boolean;
		overall: boolean;
	};
};

/**
 * Performance metrics for the composite repository
 */
type CompositeMetrics =
{
	// Overall metrics
	totalQueries: number;
	totalDomains: number;
	totalErrors: number;
	errorRate: number;
	averageResponseTime: number;

	// Manticore metrics
	manticore: {
		totalQueries: number;
		totalDomains: number;
		totalErrors: number;
		totalCacheHits: number;
		totalRetries: number;
		errorRate: number;
		cacheHitRate: number;
		averageDomainsPerQuery: number;
		connectionPoolUtilization: number;
		cacheSize: number;
		cacheEfficiency: number;
	};

	// Redis operational metrics
	redis: {
		deduplicationHits: number;
		rateLimitChecks: number;
		negativeResultCacheHits: number;
		processingStateOperations: number;
		memoryUsageEstimate: string;
	};

	// Existence service metrics
	existenceService: {
		totalQueries: number;
		totalDomains: number;
		totalErrors: number;
		totalCacheHits: number;
		totalDeduplicationHits: number;
		errorRate: number;
		cacheHitRate: number;
		averageDomainsPerQuery: number;
		activeBatches: number;
	};
};

/**
 * Optimized composite domain repository using Manticore-first lookup with ScyllaDB fallback
 * Eliminates Redis cache dependency for existence checks and focuses on operational efficiency
 */
class OptimizedCompositeDomainRepository implements DomainRepository
{
	private readonly logger = logger.getLogger('OptimizedCompositeDomainRepository');

	private readonly config: OptimizedCompositeConfig;

	private readonly existenceService: OptimizedDomainExistenceService;

	private readonly manticoreIndex: EnhancedManticoreDomainIndex;

	private readonly redisOperational: RedisOperationalLayer;

	private readonly scyllaRepo: ScyllaDomainRepository;

	// Performance tracking
	private totalQueries = 0;

	private totalDomains = 0;

	private totalErrors = 0;

	private totalResponseTime = 0;

	// Health monitoring
	private lastHealthCheck = 0;

	private healthStatus: CompositeHealthStatus | null = null;

	// Failover management
	private isInFailoverMode = false;

	private failoverStartTime = 0;

	private failoverRecoveryTimer?: NodeJS.Timeout;

	private repositoryPriorities = {
		scylla: 2,
		manticore: 1,
		maria: 3,
	};

	constructor(
		private readonly dbManager: DatabaseManager,
		config: Partial<OptimizedCompositeConfig> = {},
	)
	{
		this.config = { ...DEFAULT_OPTIMIZED_COMPOSITE_CONFIG, ...config };

		// Initialize components
		this.existenceService = new OptimizedDomainExistenceService(
			this.dbManager,
			this.config.existenceServiceConfig,
		);

		this.manticoreIndex = new EnhancedManticoreDomainIndex(
			this.dbManager.getManticoreClient(),
			this.config.manticoreConfig,
		);

		this.redisOperational = new RedisOperationalLayer(
			this.dbManager.getRedisClient(),
			this.config.redisOperationalConfig,
		);

		this.scyllaRepo = new ScyllaDomainRepository(
			this.dbManager.getScyllaClient(),
			{ batchSize: this.config.batchSize, maxRetries: this.config.maxRetries },
		);

		this.logger.info('OptimizedCompositeDomainRepository initialized', {
			config: this.config,
		});

		// Start health monitoring if enabled
		if (this.config.enablePerformanceMonitoring)
		{
			this.startHealthMonitoring();
		}
	}

	/**
	 * Check if a single domain exists using optimized Manticore-first approach
	 */
	async hasDomain(domain: string): Promise<boolean>
	{
		const startTime = Date.now();
		this.totalQueries++;
		this.totalDomains++;

		try
		{
			const result = await this.existenceService.checkExistence(domain);

			this.updatePerformanceMetrics(startTime, 1, 0);

			return result;
		}
		catch (error)
		{
			this.totalErrors++;
			this.updatePerformanceMetrics(startTime, 1, 1);

			this.logger.error('Single domain check failed', {
				domain,
				error: (error as Error).message,
			});

			throw error;
		}
	}

	/**
	 * Check existence of multiple domains using optimized batch processing
	 */
	async batchHasDomains(domains: string[]): Promise<Map<string, boolean>>
	{
		if (domains.length === 0)
		{
			return new Map();
		}

		const startTime = Date.now();
		this.totalQueries++;
		this.totalDomains += domains.length;

		try
		{
			// Use operational layer for deduplication
			const operationKey = this.redisOperational.createOperationKey(domains);
			const deduplicationCheck = await this.redisOperational.checkDeduplication(operationKey);

			if (deduplicationCheck.isAlreadyProcessing)
			{
				this.logger.debug('Operation already in progress, waiting for result', {
					operationKey,
					existingOperationId: deduplicationCheck.existingOperationId,
				});

				// Wait for existing operation to complete
				return await this.waitForOperationResult(deduplicationCheck.existingOperationId!, domains);
			}

			// Mark operation as in progress
			const operationId = this.generateOperationId();
			await this.redisOperational.markOperationInProgress(operationKey, operationId);

			try
			{
				// Use the optimized existence service
				const result = await this.existenceService.batchCheckExistence(domains);

				this.updatePerformanceMetrics(startTime, domains.length, result.errors.size);

				this.logger.info('Batch domain check completed', {
					operationId,
					totalDomains: domains.length,
					foundCount: result.metrics.foundCount,
					errorCount: result.errors.size,
					executionTime: result.metrics.executionTime,
					cacheHits: result.metrics.cacheHits,
				});

				return result.results;
			}
			finally
			{
				// Clean up operation lock (don't let cleanup failures affect the main operation)
				try
				{
					await this.redisOperational.clearOperationLock(operationKey);
				}
				catch (cleanupError)
				{
					this.logger.warn('Failed to clear operation lock', {
						operationKey,
						error: (cleanupError as Error).message,
					});
				}
			}
		}
		catch (error)
		{
			this.totalErrors++;
			this.updatePerformanceMetrics(startTime, domains.length, domains.length);

			this.logger.error('Batch domain check failed', {
				totalDomains: domains.length,
				error: (error as Error).message,
			});

			throw error;
		}
	}

	/**
	 * Comprehensive health check for all components
	 */
	async healthCheck(): Promise<boolean>
	{
		try
		{
			const health = await this.getDetailedHealthStatus();
			this.healthStatus = health;
			this.lastHealthCheck = Date.now();

			return health.overall;
		}
		catch (error)
		{
			this.logger.error('Health check failed', { error: (error as Error).message });
			return false;
		}
	}

	/**
	 * Get detailed health status for all components
	 */
	async getDetailedHealthStatus(): Promise<CompositeHealthStatus>
	{
		try
		{
			const [manticoreHealth, existenceServiceHealth, redisHealth] = await Promise.allSettled([
				this.manticoreIndex.healthCheck(),
				this.existenceService.healthCheck(),
				this.redisOperational.healthCheck(),
			]);

			const scyllaHealth = await this.scyllaRepo.healthCheck();

			const manticore = manticoreHealth.status === 'fulfilled'
				? {
					healthy: manticoreHealth.value.isHealthy,
					indexExists: manticoreHealth.value.indexExists,
					connectionPoolStatus: manticoreHealth.value.connectionPoolStatus,
					cacheStatus: manticoreHealth.value.cacheStatus,
					lastError: manticoreHealth.value.lastError,
				}
				: {
					healthy: false,
					indexExists: false,
					connectionPoolStatus: 'error',
					cacheStatus: 'error',
					lastError: 'Health check failed',
				};

			const existenceService = existenceServiceHealth.status === 'fulfilled'
				? existenceServiceHealth.value
				: {
					manticore: false,
					scylla: false,
					redis: false,
					overall: false,
				};

			const redis = redisHealth.status === 'fulfilled'
				? {
					healthy: redisHealth.value.isHealthy,
					operationalKeysCount: redisHealth.value.operationalKeysCount,
					memoryUsage: redisHealth.value.memoryUsage,
					lastError: redisHealth.value.lastError,
				}
				: {
					healthy: false,
					operationalKeysCount: 0,
					memoryUsage: 'unknown',
					lastError: 'Health check failed',
				};

			// Overall health: healthy if Manticore is available OR ScyllaDB is available
			const overall = (manticore.healthy && manticore.indexExists) || scyllaHealth;

			return {
				overall,
				manticore,
				scylla: scyllaHealth,
				redis,
				existenceService,
			};
		}
		catch (error)
		{
			this.logger.error('Failed to get detailed health status', {
				error: error instanceof Error ? error.message : String(error),
			});

			return {
				overall: false,
				manticore: {
					healthy: false,
					indexExists: false,
					connectionPoolStatus: 'error',
					cacheStatus: 'error',
					lastError: error instanceof Error ? error.message : String(error),
				},
				scylla: false,
				redis: {
					healthy: false,
					operationalKeysCount: 0,
					memoryUsage: 'unknown',
					lastError: error instanceof Error ? error.message : String(error),
				},
				existenceService: {
					manticore: false,
					scylla: false,
					redis: false,
					overall: false,
				},
			};
		}
	}

	/**
	 * Get comprehensive performance metrics
	 */
	getMetrics(): CompositeMetrics
	{
		const manticoreMetrics = this.manticoreIndex.getMetrics();
		const redisMetrics = this.redisOperational.getMetrics();
		const existenceServiceMetrics = this.existenceService.getMetrics();

		return {
			// Overall metrics
			totalQueries: this.totalQueries,
			totalDomains: this.totalDomains,
			totalErrors: this.totalErrors,
			errorRate: this.totalQueries > 0 ? this.totalErrors / this.totalQueries : 0,
			averageResponseTime: this.totalQueries > 0 ? this.totalResponseTime / this.totalQueries : 0,

			// Component metrics
			manticore: manticoreMetrics,
			redis: redisMetrics,
			existenceService: existenceServiceMetrics,
		};
	}

	/**
	 * Get repository type identifier
	 */
	getRepositoryType(): string
	{
		return 'optimized-composite';
	}

	/**
	 * Get current configuration
	 */
	getConfig(): OptimizedCompositeConfig
	{
		return { ...this.config };
	}

	/**
	 * Clear all operational caches and reset metrics
	 */
	async clearCaches(): Promise<void>
	{
		try
		{
			// Clear operational Redis data
			await this.redisOperational.clearOperationalData();

			// Clear Manticore client cache
			this.manticoreIndex.clearCache();

			// Clear existence service operational caches
			await this.existenceService.clearOperationalCaches();

			// Reset performance metrics
			this.totalQueries = 0;
			this.totalDomains = 0;
			this.totalErrors = 0;
			this.totalResponseTime = 0;

			this.logger.info('All caches and metrics cleared');
		}
		catch (error)
		{
			this.logger.error('Failed to clear caches', { error: (error as Error).message });
			throw error;
		}
	}

	/**
	 * Trigger manual failover to ScyllaDB (for testing/maintenance)
	 */
	async triggerManualFailover(): Promise<void>
	{
		this.logger.warn('Manual failover triggered - switching to ScyllaDB only mode');

		try
		{
			// Set failover mode flag
			this.isInFailoverMode = true;
			this.failoverStartTime = Date.now();

			// Update repository priorities to prefer ScyllaDB
			this.repositoryPriorities = {
				scylla: 1, // Highest priority
				manticore: 10, // Lowest priority (effectively disabled)
				maria: 5, // Medium priority as backup
			};

			// Disable Manticore operations temporarily
			this.config.enableManticore = false;

			// Set automatic recovery timer (30 minutes)
			if (this.failoverRecoveryTimer)
			{
				clearTimeout(this.failoverRecoveryTimer);
			}

			this.failoverRecoveryTimer = setTimeout(() =>
			{
				this.recoverFromFailover();
			}, 30 * 60 * 1000); // 30 minutes

			this.logger.info('Failover mode activated', {
				duration: '30 minutes',
				primaryRepository: 'scylla',
				disabledRepositories: ['manticore'],
			});

			// Emit failover event for monitoring
			this.emit?.('failover', {
				type: 'manual',
				timestamp: new Date(),
				primaryRepository: 'scylla',
			});
		}
		catch (error)
		{
			this.logger.error('Failed to activate failover mode', {
				error: error instanceof Error ? error.message : String(error),
			});
			throw error;
		}
	}

	/**
	 * Get current health status (cached)
	 */
	getCurrentHealthStatus(): CompositeHealthStatus | null
	{
		return this.healthStatus;
	}

	/**
	 * Recover from failover mode
	 */
	private recoverFromFailover(): void
	{
		try
		{
			this.logger.info('Attempting to recover from failover mode');

			// Reset failover flags
			this.isInFailoverMode = false;
			this.failoverStartTime = 0;

			// Restore original repository priorities
			this.repositoryPriorities = {
				scylla: 2,
				manticore: 1,
				maria: 3,
			};

			// Re-enable Manticore operations
			this.config.enableManticore = true;

			// Clear recovery timer
			if (this.failoverRecoveryTimer)
			{
				clearTimeout(this.failoverRecoveryTimer);
				this.failoverRecoveryTimer = undefined;
			}

			this.logger.info('Successfully recovered from failover mode');

			// Emit recovery event for monitoring
			this.emit?.('recovery', {
				type: 'automatic',
				timestamp: new Date(),
				restoredRepositories: ['manticore'],
			});
		}
		catch (error)
		{
			this.logger.error('Failed to recover from failover mode', {
				error: error instanceof Error ? error.message : String(error),
			});

			// Schedule retry in 5 minutes
			this.failoverRecoveryTimer = setTimeout(() =>
			{
				this.recoverFromFailover();
			}, 5 * 60 * 1000);
		}
	}

	/**
	 * Check if automatic failover should be triggered
	 */
	shouldTriggerFailover(): boolean
	{
		if (!this.config.enableAutomaticFailover)
		{
			return false;
		}

		const errorRate = this.totalQueries > 0 ? this.totalErrors / this.totalQueries : 0;
		return errorRate > this.config.maxErrorRate;
	}

	/**
	 * Update performance metrics
	 */
	private updatePerformanceMetrics(startTime: number, domainCount: number, errorCount: number): void
	{
		const responseTime = Date.now() - startTime;
		this.totalResponseTime += responseTime;

		if (this.config.enablePerformanceMonitoring)
		{
			this.logger.debug('Performance metrics updated', {
				responseTime,
				domainCount,
				errorCount,
				totalQueries: this.totalQueries,
				averageResponseTime: this.totalResponseTime / this.totalQueries,
			});
		}
	}

	/**
	 * Wait for existing operation result
	 */
	private async waitForOperationResult(
		operationId: string,
		domains: string[],
	): Promise<Map<string, boolean>>
	{
		const maxWaitTime = 30000; // 30 seconds
		const pollInterval = 100; // 100ms
		const startTime = Date.now();

		while (Date.now() - startTime < maxWaitTime)
		{
			const state = await this.redisOperational.getProcessingState(operationId);

			if (state && state.status === 'completed')
			{
				// Operation completed, but we need to get the actual results
				// This is a simplified implementation - in practice, you'd store results
				this.logger.debug('Operation completed, processing independently');
				break;
			}

			if (state && state.status === 'failed')
			{
				this.logger.warn('Existing operation failed, processing independently');
				break;
			}

			await new Promise(resolve => setTimeout(resolve, pollInterval));
		}

		// Fallback: process independently
		this.logger.warn('Timeout waiting for operation result, processing independently');
		const result = await this.existenceService.batchCheckExistence(domains);
		return result.results;
	}

	/**
	 * Generate unique operation ID
	 */
	private generateOperationId(): string
	{
		return `op_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
	}

	/**
	 * Start health monitoring background process
	 */
	private startHealthMonitoring(): void
	{
		setInterval(async () =>
		{
			try
			{
				await this.healthCheck();

				// Check if failover should be triggered
				if (this.shouldTriggerFailover())
				{
					this.logger.warn('High error rate detected, consider triggering failover', {
						errorRate: this.totalQueries > 0 ? this.totalErrors / this.totalQueries : 0,
						threshold: this.config.maxErrorRate,
					});
				}
			}
			catch (error)
			{
				this.logger.error('Health monitoring failed', {
					error: (error as Error).message,
				});
			}
		}, this.config.healthCheckInterval);

		this.logger.info('Health monitoring started', {
			interval: this.config.healthCheckInterval,
		});
	}
}

export { DEFAULT_OPTIMIZED_COMPOSITE_CONFIG };

export default OptimizedCompositeDomainRepository;
