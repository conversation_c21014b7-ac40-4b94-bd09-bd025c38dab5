/**
 * Base interface for domain repository implementations
 * Provides standardized methods for domain existence checking across different databases
 */
type DomainRepositoryType =
{
	/**
	 * Check if a single domain exists in the database
	 * @param domain - The domain to check
	 * @returns Promise resolving to true if domain exists, false otherwise
	 */
	hasDomain(domain: string): Promise<boolean>;

	/**
	 * Check existence of multiple domains in batch for performance
	 * @param domains - Array of domains to check
	 * @returns Promise resolving to Map with domain as key and existence as boolean value
	 */
	batchHasDomains(domains: string[]): Promise<Map<string, boolean>>;

	/**
	 * Health check for the underlying database connection
	 * @returns Promise resolving to true if database is healthy, false otherwise
	 */
	healthCheck(): Promise<boolean>;

	/**
	 * Get the name/type of this repository for logging and metrics
	 * @returns String identifier for this repository type
	 */
	getRepositoryType(): string;
};

/**
 * Configuration options for domain repositories
 */
type DomainRepositoryConfigType =
{
	/**
	 * Maximum batch size for domain existence queries
	 */
	batchSize: number;

	/**
	 * Maximum number of retry attempts for failed queries
	 */
	maxRetries: number;

	/**
	 * Timeout in milliseconds for database queries
	 */
	queryTimeout: number;

	/**
	 * Whether to enable query result caching
	 */
	enableCaching: boolean;

	/**
	 * Cache TTL in seconds for positive results
	 */
	cacheTtl: number;
};


/**
 * Result of a batch domain existence check
 */
type BatchCheckResultType =
{
	/**
	 * Map of domain to existence status
	 */
	results: Map<string, boolean>;

	/**
	 * Domains that failed to be checked due to errors
	 */
	errors: Map<string, Error>;

	/**
	 * Total number of domains processed
	 */
	totalProcessed: number;

	/**
	 * Number of domains found to exist
	 */
	foundCount: number;

	/**
	 * Query execution time in milliseconds
	 */
	executionTime: number;
};

/**
 * Default configuration for domain repositories
 */
const DEFAULT_REPOSITORY_CONFIG: DomainRepositoryConfigType =
{
	batchSize: 5000,
	maxRetries: 3,
	queryTimeout: 30000,
	enableCaching: true,
	cacheTtl: 86400, // 24 hours
};

export type {
	DomainRepositoryType,
	DomainRepositoryConfigType,
	BatchCheckResultType,
};

export { DEFAULT_REPOSITORY_CONFIG };
