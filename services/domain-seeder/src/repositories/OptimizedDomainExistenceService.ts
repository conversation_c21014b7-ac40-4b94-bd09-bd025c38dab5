import type DatabaseManager from '@shared/database/DatabaseManager';
import type ManticoreClient from '@shared/database/ManticoreClient';
import type ScyllaClient from '@shared/database/ScyllaClient';
import type RedisClientWrapper from '@shared/database/RedisClient';
import { logger } from '@shared/utils/Logger';

/**
 * Configuration for OptimizedDomainExistenceService
 */
type OptimizedDomainExistenceConfig =
{
	/**
	 * Maximum batch size for Manticore queries (10K-100K domains per query)
	 */
	manticoreBatchSize: number;

	/**
	 * Maximum batch size for ScyllaDB operations
	 */
	scyllaBatchSize: number;

	/**
	 * Query timeout in milliseconds
	 */
	queryTimeout: number;

	/**
	 * Maximum number of retry attempts
	 */
	maxRetries: number;

	/**
	 * Connection pool size for Manticore
	 */
	manticoreConnectionPool: number;

	/**
	 * Connection pool size for ScyllaDB
	 */
	scyllaConnectionPool: number;

	/**
	 * TTL for negative results cache in Redis (seconds)
	 */
	negativeResultTtl: number;

	/**
	 * TTL for deduplication keys in Redis (seconds)
	 */
	deduplicationTtl: number;

	/**
	 * Maximum concurrent operations
	 */
	maxConcurrentOperations: number;

	/**
	 * Enable performance monitoring
	 */
	enableMetrics: boolean;
};

/**
 * Default configuration for OptimizedDomainExistenceService
 */
const DEFAULT_OPTIMIZED_CONFIG: OptimizedDomainExistenceConfig = {
	manticoreBatchSize: 50000, // 50K domains per query
	scyllaBatchSize: 10000, // 10K domains per insert
	queryTimeout: 5000, // 5 seconds
	maxRetries: 3,
	manticoreConnectionPool: 20,
	scyllaConnectionPool: 10,
	negativeResultTtl: 3600, // 1 hour
	deduplicationTtl: 300, // 5 minutes
	maxConcurrentOperations: 10000,
	enableMetrics: true,
};

/**
 * Result of batch domain existence check
 */
type BatchExistenceResult =
{
	/**
	 * Map of domain to existence status
	 */
	results: Map<string, boolean>;

	/**
	 * Domains that had errors during checking
	 */
	errors: Map<string, Error>;

	/**
	 * Performance metrics
	 */
	metrics: {
		totalDomains: number;
		foundCount: number;
		errorCount: number;
		executionTime: number;
		manticoreQueryTime: number;
		scyllaUpdateTime?: number;
		cacheHits: number;
		deduplicationHits: number;
	};
};

/**
 * Optimized domain existence service that uses Manticore Search as primary source
 * and ScyllaDB as authoritative storage, eliminating Redis cache layer
 */
class OptimizedDomainExistenceService
{
	private readonly logger = logger.getLogger('OptimizedDomainExistenceService');

	private readonly config: OptimizedDomainExistenceConfig;

	private readonly manticoreClient: ManticoreClient;

	private readonly scyllaClient: ScyllaClient;

	private readonly redisClient: RedisClientWrapper;

	// Performance metrics
	private totalQueries = 0;

	private totalDomains = 0;

	private totalErrors = 0;

	private totalCacheHits = 0;

	private totalDeduplicationHits = 0;

	// Connection pools and concurrency control
	private readonly activeBatches = new Set<string>();

	constructor(
		private readonly dbManager: DatabaseManager,
		config: Partial<OptimizedDomainExistenceConfig> = {},
	)
	{
		this.config = { ...DEFAULT_OPTIMIZED_CONFIG, ...config };

		this.manticoreClient = this.dbManager.getManticoreClient();
		this.scyllaClient = this.dbManager.getScyllaClient();
		this.redisClient = this.dbManager.getRedisClient();

		this.logger.info('OptimizedDomainExistenceService initialized', {
			config: this.config,
		});
	}

	/**
	 * Check existence of multiple domains using Manticore as primary source
	 */
	async batchCheckExistence(domains: string[]): Promise<BatchExistenceResult>
	{
		if (domains.length === 0)
		{
			return this.createEmptyResult();
		}

		const startTime = Date.now();
		const batchId = this.generateBatchId();

		this.totalQueries++;
		this.totalDomains += domains.length;

		try
		{
			// Check if this batch is already being processed (deduplication)
			const deduplicationKey = this.createDeduplicationKey(domains);
			const isAlreadyProcessing = await this.checkDeduplication(deduplicationKey);

			if (isAlreadyProcessing)
			{
				this.totalDeduplicationHits++;
				this.logger.debug('Batch already being processed, waiting for result', {
					batchId,
					domainsCount: domains.length,
				});

				// Wait for the other batch to complete and return cached result
				return await this.waitForBatchResult(deduplicationKey, domains);
			}

			// Mark batch as being processed
			await this.markBatchInProgress(deduplicationKey, batchId);
			this.activeBatches.add(batchId);

			// Step 1: Check negative results cache in Redis
			const { uncachedDomains, cacheResults, cacheHits } = await this.checkNegativeCache(domains);

			this.totalCacheHits += cacheHits;

			if (uncachedDomains.length === 0)
			{
				// All domains were in negative cache
				const result = this.createResultFromCache(domains, cacheResults, startTime);
				await this.markBatchComplete(deduplicationKey, result);
				return result;
			}

			// Step 2: Query Manticore Search for domain existence
			const manticoreStartTime = Date.now();
			let manticoreResults: Map<string, boolean>;
			const queryErrors = new Map<string, Error>();

			try
			{
				manticoreResults = await this.queryManticoreForExistence(uncachedDomains);
			}
			catch (error)
			{
				// If both Manticore and ScyllaDB fail, we need to track errors
				uncachedDomains.forEach((domain) =>
				{
					queryErrors.set(domain, error as Error);
				});
				manticoreResults = new Map(uncachedDomains.map(d => [d, false]));
			}

			const manticoreQueryTime = Date.now() - manticoreStartTime;

			// Step 3: Identify new domains not in Manticore
			const newDomains = this.identifyNewDomains(uncachedDomains, manticoreResults);

			// Step 4: Update ScyllaDB with new domains (if any)
			let scyllaUpdateTime: number | undefined;
			if (newDomains.length > 0)
			{
				const scyllaStartTime = Date.now();
				await this.updateScyllaWithNewDomains(newDomains);
				scyllaUpdateTime = Date.now() - scyllaStartTime;

				// Trigger Manticore index update (async)
				this.triggerManticoreIndexUpdate(newDomains);
			}
			else
			{
				scyllaUpdateTime = 0; // No update needed
			}

			// Step 5: Update negative results cache
			await this.updateNegativeCache(uncachedDomains, manticoreResults);

			// Step 6: Combine all results
			const finalResults = this.combineResults(domains, cacheResults, manticoreResults);

			const result: BatchExistenceResult = {
				results: finalResults,
				errors: queryErrors,
				metrics: {
					totalDomains: domains.length,
					foundCount: Array.from(finalResults.values()).filter(Boolean).length,
					errorCount: queryErrors.size,
					executionTime: Date.now() - startTime,
					manticoreQueryTime,
					scyllaUpdateTime,
					cacheHits,
					deduplicationHits: 0,
				},
			};

			// Mark batch as complete and cache result
			await this.markBatchComplete(deduplicationKey, result);

			this.logger.info('Batch existence check completed', {
				batchId,
				...result.metrics,
				newDomainsFound: newDomains.length,
			});

			return result;
		}
		catch (error)
		{
			this.totalErrors++;
			this.logger.error('Batch existence check failed', {
				batchId,
				domainsCount: domains.length,
				error: (error as Error).message,
			});

			const errorResult: BatchExistenceResult = {
				results: new Map(domains.map(d => [d, false])),
				errors: new Map(domains.map(d => [d, error as Error])),
				metrics: {
					totalDomains: domains.length,
					foundCount: 0,
					errorCount: domains.length,
					executionTime: Date.now() - startTime,
					manticoreQueryTime: 0,
					cacheHits: 0,
					deduplicationHits: 0,
				},
			};

			// Mark batch as complete even on error
			try
			{
				const deduplicationKey = this.createDeduplicationKey(domains);
				await this.markBatchComplete(deduplicationKey, errorResult);
			}
			catch (markError)
			{
				this.logger.warn('Failed to mark failed batch as complete', {
					error: (markError as Error).message,
				});
			}

			return errorResult;
		}
		finally
		{
			this.activeBatches.delete(batchId);
		}
	}

	/**
	 * Check existence of a single domain (convenience method)
	 */
	async checkExistence(domain: string): Promise<boolean>
	{
		const result = await this.batchCheckExistence([domain]);
		return result.results.get(domain) || false;
	}

	/**
	 * Health check for all underlying services
	 */
	async healthCheck(): Promise<{
		manticore: boolean;
		scylla: boolean;
		redis: boolean;
		overall: boolean;
	}>
	{
		try
		{
			const [manticoreHealth, scyllaHealth, redisHealth] = await Promise.allSettled([
				this.manticoreClient.healthCheck(),
				this.scyllaClient.healthCheck(),
				this.redisClient.healthCheck(),
			]);

			const results = {
				manticore: manticoreHealth.status === 'fulfilled' ? manticoreHealth.value : false,
				scylla: scyllaHealth.status === 'fulfilled' ? scyllaHealth.value : false,
				redis: redisHealth.status === 'fulfilled' ? redisHealth.value : false,
				overall: false,
			};

			// Service is healthy if Manticore is available (primary) and at least one of Scylla/Redis
			results.overall = results.manticore && (results.scylla || results.redis);

			this.logger.debug('Health check completed', results);

			return results;
		}
		catch (error)
		{
			this.logger.error('Health check failed', { error: (error as Error).message });
			return {
				manticore: false,
				scylla: false,
				redis: false,
				overall: false,
			};
		}
	}

	/**
	 * Get performance metrics
	 */
	getMetrics(): {
		totalQueries: number;
		totalDomains: number;
		totalErrors: number;
		totalCacheHits: number;
		totalDeduplicationHits: number;
		errorRate: number;
		cacheHitRate: number;
		averageDomainsPerQuery: number;
		activeBatches: number;
	}
	{
		return {
			totalQueries: this.totalQueries,
			totalDomains: this.totalDomains,
			totalErrors: this.totalErrors,
			totalCacheHits: this.totalCacheHits,
			totalDeduplicationHits: this.totalDeduplicationHits,
			errorRate: this.totalQueries > 0 ? this.totalErrors / this.totalQueries : 0,
			cacheHitRate: this.totalDomains > 0 ? this.totalCacheHits / this.totalDomains : 0,
			averageDomainsPerQuery: this.totalQueries > 0 ? this.totalDomains / this.totalQueries : 0,
			activeBatches: this.activeBatches.size,
		};
	}

	/**
	 * Get current configuration
	 */
	getConfig(): OptimizedDomainExistenceConfig
	{
		return { ...this.config };
	}

	/**
	 * Clear operational caches (negative results and deduplication)
	 */
	async clearOperationalCaches(): Promise<void>
	{
		try
		{
			const patterns = [
				'domain:negative:*',
				'domain:dedup:*',
				'domain:batch:*',
			];

			for (const pattern of patterns)
			{
				const keys = await this.redisClient.keys(pattern);
				if (keys.length > 0)
				{
					await this.redisClient.del(keys);
				}
			}

			this.logger.info('Operational caches cleared', { patterns });
		}
		catch (error)
		{
			this.logger.error('Failed to clear operational caches', {
				error: (error as Error).message,
			});
			throw error;
		}
	}

	/**
	 * Check negative results cache in Redis
	 */
	private async checkNegativeCache(domains: string[]): Promise<{
		uncachedDomains: string[];
		cacheResults: Map<string, boolean>;
		cacheHits: number;
	}>
	{
		const cacheResults = new Map<string, boolean>();
		const uncachedDomains: string[] = [];
		let cacheHits = 0;

		try
		{
			// Check cache for all domains in batch
			const cacheKeys = domains.map(domain => `domain:negative:${domain}`);
			const cacheValues = await this.redisClient.mget(cacheKeys);

			for (let i = 0; i < domains.length; i++)
			{
				const domain = domains[i];
				const cached = cacheValues[i];

				if (cached === 'false')
				{
					// Domain is cached as not existing
					cacheResults.set(domain, false);
					cacheHits++;
				}
				else
				{
					// Not in cache or cached as existing (we don't cache positive results here)
					uncachedDomains.push(domain);
				}
			}

			this.logger.debug('Negative cache check completed', {
				totalDomains: domains.length,
				cacheHits,
				uncached: uncachedDomains.length,
			});

			return { uncachedDomains, cacheResults, cacheHits };
		}
		catch (error)
		{
			this.logger.warn('Negative cache check failed, proceeding without cache', {
				error: (error as Error).message,
			});

			// On cache failure, treat all domains as uncached
			return {
				uncachedDomains: domains,
				cacheResults: new Map(),
				cacheHits: 0,
			};
		}
	}

	/**
	 * Query Manticore Search for domain existence in batches
	 */
	private async queryManticoreForExistence(domains: string[]): Promise<Map<string, boolean>>
	{
		const results = new Map<string, boolean>();

		// Initialize all domains as not found
		domains.forEach((domain) =>
		{
			results.set(domain, false);
		});

		try
		{
			// Process domains in batches to respect Manticore limits
			for (let i = 0; i < domains.length; i += this.config.manticoreBatchSize)
			{
				const batch = domains.slice(i, i + this.config.manticoreBatchSize);

				// Use Manticore's batch comparison functionality
				const searchResult = await Promise.race([
					this.manticoreClient.compareDomains(batch),
					this.createTimeout(this.config.queryTimeout),
				]);

				// Mark found domains as true
				searchResult.results.forEach((result) =>
				{
					const domain = result.domain as string;
					if (results.has(domain))
					{
						results.set(domain, true);
					}
				});

				this.logger.debug('Manticore batch query completed', {
					batchSize: batch.length,
					foundCount: searchResult.results.length,
					queryTime: 0, // Remove queryTime property access as it doesn't exist
				});
			}

			return results;
		}
		catch (error)
		{
			this.logger.error('Manticore query failed', {
				domainsCount: domains.length,
				error: (error as Error).message,
			});

			// On Manticore failure, fall back to ScyllaDB
			return this.fallbackToScyllaQuery(domains);
		}
	}

	/**
	 * Fallback to ScyllaDB when Manticore is unavailable
	 */
	private async fallbackToScyllaQuery(domains: string[]): Promise<Map<string, boolean>>
	{
		this.logger.warn('Falling back to ScyllaDB for domain existence check', {
			domainsCount: domains.length,
		});

		const results = new Map<string, boolean>();

		// Initialize all domains as not found
		domains.forEach((domain) =>
		{
			results.set(domain, false);
		});

		try
		{
			// Process domains in ScyllaDB batches
			for (let i = 0; i < domains.length; i += this.config.scyllaBatchSize)
			{
				const batch = domains.slice(i, i + this.config.scyllaBatchSize);
				const placeholders = batch.map(() => '?').join(',');
				const query = `SELECT domain FROM domain_analysis WHERE domain IN (${placeholders})`;

				const result = await this.scyllaClient.execute(query, batch, {
					maxRetries: this.config.maxRetries,
				});

				// Mark found domains as true
				result.rows.forEach((row) =>
				{
					const domain = row.domain as string;
					if (results.has(domain))
					{
						results.set(domain, true);
					}
				});
			}

			return results;
		}
		catch (error)
		{
			this.logger.error('ScyllaDB fallback query failed', {
				domainsCount: domains.length,
				error: (error as Error).message,
			});

			// Re-throw error to be handled by caller
			throw error;
		}
	}

	/**
	 * Identify domains that are new (not found in Manticore)
	 */
	private identifyNewDomains(domains: string[], manticoreResults: Map<string, boolean>): string[]
	{
		const newDomains: string[] = [];

		domains.forEach((domain) =>
		{
			if (!manticoreResults.get(domain))
			{
				newDomains.push(domain);
			}
		});

		return newDomains;
	}

	/**
	 * Update ScyllaDB with new domains that were discovered
	 */
	private async updateScyllaWithNewDomains(newDomains: string[]): Promise<void>
	{
		if (newDomains.length === 0)
		{
			return;
		}

		this.logger.info('Updating ScyllaDB with new domains', {
			newDomainsCount: newDomains.length,
		});

		try
		{
			// Process new domains in batches
			for (let i = 0; i < newDomains.length; i += this.config.scyllaBatchSize)
			{
				const batch = newDomains.slice(i, i + this.config.scyllaBatchSize);

				// Insert new domains into ScyllaDB with comprehensive metadata
				const insertPromises = batch.map((domain) =>
				{
					const query = `
						INSERT INTO domain_analysis (
							domain, first_seen_at, discovery_source,
							tld, domain_length, has_hyphens, has_numbers,
							confidence, status, last_checked_at
						) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
					`;

					// Extract domain metadata
					const parts = domain.split('.');
					const tld = parts[parts.length - 1];

					return this.scyllaClient.execute(query, [
						domain,
						new Date().toISOString(),
						'domain-seeder-optimized',
						tld,
						domain.length,
						domain.includes('-'),
						/\d/.test(domain),
						0.5, // Default confidence
						'discovered',
						new Date().toISOString(),
					], {
						maxRetries: this.config.maxRetries,
					});
				});

				await Promise.allSettled(insertPromises);

				this.logger.debug('ScyllaDB batch insert completed', {
					batchSize: batch.length,
				});
			}
		}
		catch (error)
		{
			this.logger.error('Failed to update ScyllaDB with new domains', {
				newDomainsCount: newDomains.length,
				error: (error as Error).message,
			});
			throw error;
		}
	}

	/**
	 * Trigger Manticore index update (async operation)
	 */
	private triggerManticoreIndexUpdate(newDomains: string[]): void
	{
		// Trigger background job to update the Manticore index asynchronously
		if (newDomains.length === 0)
		{
			return;
		}

		// Use setImmediate to avoid blocking the current operation
		setImmediate(async () =>
		{
			try
			{
				this.logger.info('Starting background Manticore index update', {
					newDomainsCount: newDomains.length,
				});

				// Batch update Manticore index
				const batchSize = 100;
				const batches = [];

				for (let i = 0; i < newDomains.length; i += batchSize)
				{
					const batch = newDomains.slice(i, i + batchSize);
					batches.push(this.updateManticoreIndexBatch(batch));
				}

				// Process batches with limited concurrency
				const concurrency = 3;
				for (let i = 0; i < batches.length; i += concurrency)
				{
					const concurrentBatches = batches.slice(i, i + concurrency);
					await Promise.all(concurrentBatches);
				}

				this.logger.info('Manticore index update completed', {
					domainsUpdated: newDomains.length,
					batchesProcessed: batches.length,
				});
			}
			catch (error)
			{
				this.logger.error('Failed to update Manticore index', {
					error: error.message,
					domainsCount: newDomains.length,
				});
			}
		});
	}

	/**
	 * Update a batch of domains in Manticore index
	 */
	private async updateManticoreIndexBatch(domains: string[]): Promise<void>
	{
		try
		{
			// Prepare domain documents for indexing
			const documents = domains.map(domain => ({
				domain,
				indexed_at: new Date().toISOString(),
				source: 'domain_existence_service',
				exists: true,
			}));

			// Use Manticore's bulk indexing API
			const manticoreClient = this.dbManager.getManticoreClient();
			if (manticoreClient && typeof (manticoreClient as any).bulkIndex === 'function')
			{
				await (manticoreClient as any).bulkIndex('domains_index', documents);
			}
			else
			{
				// Fallback to individual inserts
				for (const doc of documents)
				{
					if (manticoreClient && typeof (manticoreClient as any).query === 'function')
					{
						await (manticoreClient as any).query(
							'INSERT INTO domains_index (domain, indexed_at, source, exists) VALUES (?, ?, ?, ?)',
							[doc.domain, doc.indexed_at, doc.source, doc.exists],
						);
					}
				}
			}

			this.logger.debug('Manticore batch update completed', {
				batchSize: domains.length,
			});
		}
		catch (error)
		{
			this.logger.error('Failed to update Manticore batch', {
				error: (error as Error).message,
				batchSize: domains.length,
			});
			throw error;
		}
	}

	/**
	 * Update negative results cache with domains not found in Manticore
	 */
	private async updateNegativeCache(
		domains: string[],
		manticoreResults: Map<string, boolean>,
	): Promise<void>
	{
		try
		{
			const negativeResults: Array<[string, string]> = [];

			domains.forEach((domain) =>
			{
				if (!manticoreResults.get(domain))
				{
					negativeResults.push([`domain:negative:${domain}`, 'false']);
				}
			});

			if (negativeResults.length > 0)
			{
				// Set negative results with TTL
				const pipeline = this.redisClient.pipeline();
				negativeResults.forEach(([key, value]) =>
				{
					pipeline.setex(key, this.config.negativeResultTtl, value);
				});
				await pipeline.exec();

				this.logger.debug('Updated negative cache', {
					negativeResultsCount: negativeResults.length,
					ttl: this.config.negativeResultTtl,
				});
			}
		}
		catch (error)
		{
			this.logger.warn('Failed to update negative cache', {
				error: (error as Error).message,
			});
			// Don't throw - cache update failure shouldn't fail the main operation
		}
	}

	/**
	 * Combine cache results and Manticore results
	 */
	private combineResults(
		allDomains: string[],
		cacheResults: Map<string, boolean>,
		manticoreResults: Map<string, boolean>,
	): Map<string, boolean>
	{
		const finalResults = new Map<string, boolean>();

		allDomains.forEach((domain) =>
		{
			// Priority: cache results first, then Manticore results
			if (cacheResults.has(domain))
			{
				finalResults.set(domain, cacheResults.get(domain)!);
			}
			else
			{
				finalResults.set(domain, manticoreResults.get(domain) || false);
			}
		});

		return finalResults;
	}

	/**
	 * Check if batch is already being processed (deduplication)
	 */
	private async checkDeduplication(deduplicationKey: string): Promise<boolean>
	{
		try
		{
			const exists = await this.redisClient.exists(deduplicationKey);
			return exists > 0;
		}
		catch (error)
		{
			this.logger.warn('Deduplication check failed', {
				error: (error as Error).message,
			});
			return false;
		}
	}

	/**
	 * Mark batch as in progress for deduplication
	 */
	private async markBatchInProgress(deduplicationKey: string, batchId: string): Promise<void>
	{
		try
		{
			await this.redisClient.setex(deduplicationKey, this.config.deduplicationTtl, batchId);
		}
		catch (error)
		{
			this.logger.warn('Failed to mark batch in progress', {
				error: (error as Error).message,
			});
		}
	}

	/**
	 * Mark batch as complete and cache result
	 */
	private async markBatchComplete(
		deduplicationKey: string,
		result: BatchExistenceResult,
	): Promise<void>
	{
		try
		{
			// Store result for other waiting batches
			const resultKey = `${deduplicationKey}:result`;
			await this.redisClient.setex(
				resultKey,
				this.config.deduplicationTtl,
				JSON.stringify({
					results: Array.from(result.results.entries()),
					metrics: result.metrics,
				}),
			);

			// Remove deduplication lock
			await this.redisClient.del(deduplicationKey);
		}
		catch (error)
		{
			this.logger.warn('Failed to mark batch complete', {
				error: (error as Error).message,
			});
		}
	}

	/**
	 * Wait for batch result from another concurrent operation
	 */
	private async waitForBatchResult(
		deduplicationKey: string,
		domains: string[],
	): Promise<BatchExistenceResult>
	{
		const maxWaitTime = this.config.queryTimeout * 2;
		const pollInterval = 100;
		const startTime = Date.now();

		while (Date.now() - startTime < maxWaitTime)
		{
			try
			{
				const resultKey = `${deduplicationKey}:result`;
				const cachedResult = await this.redisClient.get(resultKey);

				if (cachedResult)
				{
					const parsed = JSON.parse(cachedResult as string);
					return {
						results: new Map(parsed.results),
						errors: new Map(),
						metrics: {
							...parsed.metrics,
							deduplicationHits: domains.length,
						},
					};
				}

				// Check if the original batch is still processing
				const stillProcessing = await this.redisClient.exists(deduplicationKey);
				if (stillProcessing === 0)
				{
					// Original batch completed but no result found, process ourselves
					break;
				}

				await new Promise(resolve => setTimeout(resolve, pollInterval));
			}
			catch (error)
			{
				this.logger.warn('Error while waiting for batch result', {
					error: (error as Error).message,
				});
				break;
			}
		}

		// Fallback: process the batch ourselves
		this.logger.warn('Timeout waiting for batch result, processing independently');
		return this.batchCheckExistence(domains);
	}

	/**
	 * Create deduplication key for a batch of domains
	 */
	private createDeduplicationKey(domains: string[]): string
	{
		// Create a hash of sorted domains for consistent deduplication
		const sortedDomains = [...domains].sort();
		const hash = this.hashString(sortedDomains.join(','));
		return `domain:dedup:${hash}`;
	}

	/**
	 * Generate unique batch ID
	 */
	private generateBatchId(): string
	{
		return `batch_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
	}

	/**
	 * Create empty result for edge cases
	 */
	private createEmptyResult(): BatchExistenceResult
	{
		return {
			results: new Map(),
			errors: new Map(),
			metrics: {
				totalDomains: 0,
				foundCount: 0,
				errorCount: 0,
				executionTime: 0,
				manticoreQueryTime: 0,
				cacheHits: 0,
				deduplicationHits: 0,
			},
		};
	}

	/**
	 * Create result from cache only
	 */
	private createResultFromCache(
		domains: string[],
		cacheResults: Map<string, boolean>,
		startTime: number,
	): BatchExistenceResult
	{
		const results = new Map<string, boolean>();
		domains.forEach((domain) =>
		{
			results.set(domain, cacheResults.get(domain) || false);
		});

		return {
			results,
			errors: new Map(),
			metrics: {
				totalDomains: domains.length,
				foundCount: Array.from(results.values()).filter(Boolean).length,
				errorCount: 0,
				executionTime: Date.now() - startTime,
				manticoreQueryTime: 0,
				cacheHits: domains.length,
				deduplicationHits: 0,
			},
		};
	}

	/**
	 * Create timeout promise
	 */
	private createTimeout(ms: number): Promise<never>
	{
		return new Promise((_, reject) =>
		{
			setTimeout(() => reject(new Error(`Query timeout after ${ms}ms`)), ms);
		});
	}

	/**
	 * Simple string hash function
	 */
	private hashString(str: string): string
	{
		let hash = 0;
		for (let i = 0; i < str.length; i++)
		{
			const char = str.charCodeAt(i);
			hash = ((hash << 5) - hash) + char;
			hash &= hash; // Convert to 32-bit integer
		}
		return Math.abs(hash).toString(36);
	}
}

export type { OptimizedDomainExistenceConfig };

export { DEFAULT_OPTIMIZED_CONFIG };

export default OptimizedDomainExistenceService;
