import { logger, type LoggerInstanceType } from '@shared';
import type {
	StrategyProcessorType,
	DiscoveredDomainInterface,
	SnapshotStoreType,
	DiscoveryStrategyType,
} from '../../interfaces/DiscoveryEngine';
import type { DomainCandidateType } from '../../interfaces/SourceConnector';

/**
 * Base abstract class for strategy processors
 * Provides common functionality and enforces interface compliance
 */
abstract class BaseStrategyProcessor implements StrategyProcessorType
{
	protected readonly logger: LoggerInstanceType;

	protected readonly strategyName: DiscoveryStrategyType;

	constructor(strategyName: DiscoveryStrategyType)
	{
		this.strategyName = strategyName;
		this.logger = logger.getLogger(`${strategyName}StrategyProcessor`);
	}

	/**
	 * Process candidates using the specific strategy
	 * Must be implemented by concrete strategy classes
	 */
	abstract process(
		candidates: DomainCandidateType[],
		snapshotStore: SnapshotStoreType,
	): Promise<DiscoveredDomainInterface[]>;

	/**
	 * Validate candidates before processing
	 */
	protected validateCandidates(candidates: DomainCandidateType[]): DomainCandidateType[]
	{
		const valid = candidates.filter((candidate) =>
		{
			if (!this.isValidCandidate(candidate))
			{
				this.logger.warn({
					domain: candidate.domain,
					source: candidate.source,
					strategy: this.strategyName,
				}, 'Invalid candidate filtered out');
				return false;
			}
			return true;
		});

		this.logger.debug({
			strategy: this.strategyName,
			input: candidates.length,
			valid: valid.length,
			filtered: candidates.length - valid.length,
		}, 'Candidate validation completed');

		return valid;
	}

	/**
	 * Check if a candidate is valid for processing
	 */
	protected isValidCandidate(candidate: DomainCandidateType): boolean
	{
		return !!(
			candidate.domain &&
			typeof candidate.domain === 'string' &&
			candidate.domain.trim().length > 0 &&
			candidate.source &&
			typeof candidate.source === 'string'
		);
	}

	/**
	 * Create a discovered domain result
	 */
	protected createDiscoveredDomain(
		candidate: DomainCandidateType,
		confidence: number,
		discoveryReason: string,
	): DiscoveredDomainInterface
	{
		// Ensure confidence is within valid range
		const normalizedConfidence = Math.max(0, Math.min(1, confidence));

		return {
			...candidate,
			discoveryStrategy: this.strategyName,
			confidence: normalizedConfidence,
			discoveryReason,
		};
	}

	/**
	 * Calculate confidence score based on various factors
	 */
	protected calculateConfidence(factors: ConfidenceFactors): number
	{
		let confidence = factors.baseConfidence || 0.5;

		// Adjust based on rank (lower rank = higher confidence)
		if (factors.rank)
		{
			if (factors.rank <= 10000)
			{
				confidence += 0.2;
			}
			else if (factors.rank <= 100000)
			{
				confidence += 0.1;
			}
			else if (factors.rank > 1000000)
			{
				confidence -= 0.1;
			}
		}

		// Adjust based on source reliability
		if (factors.sourceReliability)
		{
			confidence += (factors.sourceReliability - 0.5) * 0.2;
		}

		// Adjust based on metadata quality
		if (factors.hasMetadata)
		{
			confidence += 0.05;
		}

		// Adjust based on recency
		if (factors.isRecent)
		{
			confidence += 0.1;
		}

		// Ensure confidence stays within bounds
		return Math.max(0, Math.min(1, confidence));
	}

	/**
	 * Log processing results
	 */
	protected logResults(
		inputCount: number,
		outputCount: number,
		processingTime: number,
		additionalInfo?: Record<string, any>,
	): void
	{
		this.logger.info({
			strategy: this.strategyName,
			inputCandidates: inputCount,
			discoveredDomains: outputCount,
			discoveryRate: inputCount > 0 ? `${(outputCount / inputCount * 100).toFixed(2) }%` : '0%',
			processingTime: `${processingTime}ms`,
			...additionalInfo,
		}, 'Strategy processing completed');
	}

	/**
	 * Handle processing errors gracefully
	 */
	protected handleError(error: Error, context: Record<string, any>): never
	{
		this.logger.error({
			strategy: this.strategyName,
			error: error.message,
			stack: error.stack,
			...context,
		}, 'Strategy processing failed');

		throw new Error(`${this.strategyName} strategy failed: ${error.message}`);
	}

	/**
	 * Get strategy-specific metadata
	 */
	protected getStrategyMetadata(): Record<string, any>
	{
		return {
			strategy: this.strategyName,
			timestamp: new Date().toISOString(),
			version: '1.0.0',
		};
	}
}

interface ConfidenceFactors
{
	baseConfidence?: number;
	rank?: number;
	sourceReliability?: number;
	hasMetadata?: boolean;
	isRecent?: boolean;
}

export type { ConfidenceFactors };
export { BaseStrategyProcessor };
