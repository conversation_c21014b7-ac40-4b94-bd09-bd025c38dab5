import type {
	DiscoveredDomainInterface,
	SnapshotStoreType,
} from '../../interfaces/DiscoveryEngine';
import type { DomainCandidateType } from '../../interfaces/SourceConnector';
import { BaseStrategyProcessor } from './BaseStrategyProcessor';

/**
 * Long-tail Exploration Strategy Processor
 * Discovers domains beyond typical top-1M lists by processing
 * Common Crawl and FDNS data starting from rank 1M+
 */
class LongTailProcessor extends BaseStrategyProcessor
{
	private static readonly LONG_TAIL_THRESHOLD = 1000000; // 1M rank threshold

	private static readonly MAX_LONG_TAIL_RANK = 10000000; // 10M max rank

	private static readonly SUPPORTED_SOURCES = new Set([
		'common-crawl',
		'sonar',
		'fdns',
		'rapid7',
	]);

	constructor()
	{
		super('long-tail');
	}

	/**
	 * Process candidates using long-tail exploration strategy
	 * Filters domains ranked beyond 1M and applies confidence scoring
	 */
	async process(
		candidates: DomainCandidateType[],
		snapshotStore: SnapshotStoreType,
	): Promise<DiscoveredDomainInterface[]>
	{
		const startTime = Date.now();

		try
		{
			this.logger.info({
				candidateCount: candidates.length,
				threshold: LongTailProcessor.LONG_TAIL_THRESHOLD,
				msg: 'Starting long-tail exploration',
			});

			// Validate and filter candidates
			const validCandidates = this.validateCandidates(candidates);
			if (validCandidates.length === 0)
			{
				this.logger.warn({ msg: 'No valid candidates for long-tail exploration' });
				return [];
			}

			// Filter for long-tail domains only
			const longTailCandidates = this.filterLongTailCandidates(validCandidates);
			if (longTailCandidates.length === 0)
			{
				this.logger.info({ threshold: LongTailProcessor.LONG_TAIL_THRESHOLD, totalCandidates: validCandidates.length, msg: 'No long-tail candidates found' });
				return [];
			}

			// Process long-tail candidates
			const discovered = await this.processLongTailCandidates(
				longTailCandidates,
				snapshotStore,
			);

			const processingTime = Date.now() - startTime;
			this.logResults(validCandidates.length, discovered.length, processingTime, {
				longTailCandidates: longTailCandidates.length,
				threshold: LongTailProcessor.LONG_TAIL_THRESHOLD,
				averageRank: this.calculateAverageRank(longTailCandidates),
			});

			return discovered;
		}
		catch (error)
		{
			this.handleError(error as Error, {
				candidateCount: candidates.length,
				threshold: LongTailProcessor.LONG_TAIL_THRESHOLD,
			});
		}
	}

	/**
	 * Filter candidates to only include long-tail domains (rank > 1M)
	 */
	private filterLongTailCandidates(candidates: DomainCandidateType[]): DomainCandidateType[]
	{
		const longTailCandidates = candidates.filter((candidate) =>
		{
			// Check if source supports long-tail discovery
			if (!this.isLongTailSource(candidate.source))
			{
				this.logger.debug({ domain: candidate.domain, source: candidate.source, msg: 'Candidate from unsupported source for long-tail' });
				return false;
			}

			// Check rank threshold
			if (!this.isLongTailRank(candidate.rank))
			{
				this.logger.debug({ domain: candidate.domain, rank: candidate.rank, threshold: LongTailProcessor.LONG_TAIL_THRESHOLD, msg: 'Candidate below long-tail threshold' });
				return false;
			}

			// Additional validation for long-tail candidates
			if (!this.isValidLongTailCandidate(candidate))
			{
				return false;
			}

			return true;
		});

		this.logger.debug({ input: candidates.length, longTail: longTailCandidates.length, filtered: candidates.length - longTailCandidates.length, msg: 'Long-tail candidate filtering completed' });

		return longTailCandidates;
	}

	/**
	 * Check if source supports long-tail discovery
	 */
	private isLongTailSource(source: string): boolean
	{
		return LongTailProcessor.SUPPORTED_SOURCES.has(source.toLowerCase());
	}

	/**
	 * Check if rank qualifies as long-tail (> 1M and <= 10M)
	 */
	private isLongTailRank(rank?: number): boolean
	{
		if (!rank || rank <= 0)
		{
			return false;
		}

		return rank > LongTailProcessor.LONG_TAIL_THRESHOLD &&
			rank <= LongTailProcessor.MAX_LONG_TAIL_RANK;
	}

	/**
	 * Additional validation for long-tail candidates
	 */
	private isValidLongTailCandidate(candidate: DomainCandidateType): boolean
	{
		// Check for minimum domain quality indicators
		const domain = candidate.domain.toLowerCase();

		// Skip obviously invalid domains
		if (this.isLikelySpam(domain))
		{
			this.logger.debug({
				domain: candidate.domain,
				msg: 'Candidate appears to be spam',
			});
			return false;
		}

		// Check for minimum metadata quality for long-tail sources
		if (!this.hasMinimumMetadata(candidate))
		{
			this.logger.debug({
				domain: candidate.domain,
				source: candidate.source,
				msg: 'Candidate lacks minimum metadata for long-tail',
			});
			return false;
		}

		return true;
	}

	/**
	 * Process long-tail candidates and create discovered domains
	 */
	private async processLongTailCandidates(
		candidates: DomainCandidateType[],
		snapshotStore: SnapshotStoreType,
	): Promise<DiscoveredDomainInterface[]>
	{
		const discovered: DiscoveredDomainInterface[] = [];

		// Group candidates by source for better processing
		const candidatesBySource = this.groupCandidatesBySource(candidates);

		for (const [source, sourceCandidates] of candidatesBySource)
		{
			try
			{
				const sourceDiscovered = await this.processSourceLongTail(
					source,
					sourceCandidates,
					snapshotStore,
				);
				discovered.push(...sourceDiscovered);
			}
			catch (error)
			{
				this.logger.error({
					source,
					candidateCount: sourceCandidates.length,
					error: error instanceof Error ? error.message : String(error),
					msg: 'Failed to process long-tail candidates for source',
				});
			}
		}

		return discovered;
	}

	/**
	 * Process long-tail candidates for a specific source
	 */
	private async processSourceLongTail(
		source: string,
		candidates: DomainCandidateType[],
		snapshotStore: SnapshotStoreType,
	): Promise<DiscoveredDomainInterface[]>
	{
		const discovered: DiscoveredDomainInterface[] = [];

		// Sort candidates by rank (ascending) to prioritize better-ranked long-tail domains
		const sortedCandidates = candidates.sort((a, b) =>
		{
			const rankA = a.rank || Number.MAX_SAFE_INTEGER;
			const rankB = b.rank || Number.MAX_SAFE_INTEGER;
			return rankA - rankB;
		});

		for (const candidate of sortedCandidates)
		{
			try
			{
				const confidence = this.calculateLongTailConfidence(candidate);
				const discoveryReason = this.buildLongTailDiscoveryReason(candidate);

				// Only include candidates with sufficient confidence
				if (confidence >= this.getMinimumConfidence())
				{
					discovered.push(this.createDiscoveredDomain(
						candidate,
						confidence,
						discoveryReason,
					));
				}
				else
				{
					this.logger.debug({
						domain: candidate.domain,
						confidence,
						minimum: this.getMinimumConfidence(),
						msg: 'Long-tail candidate below confidence threshold',
					});
				}
			}
			catch (error)
			{
				this.logger.warn({
					domain: candidate.domain,
					source: candidate.source,
					error: error instanceof Error ? error.message : String(error),
					msg: 'Failed to process long-tail candidate',
				});
			}
		}

		this.logger.debug({
			source,
			inputCandidates: candidates.length,
			discoveredDomains: discovered.length,
			msg: 'Source long-tail processing completed',
		});

		return discovered;
	}

	/**
	 * Calculate confidence for long-tail discovery
	 */
	private calculateLongTailConfidence(candidate: DomainCandidateType): number
	{
		const factors = {
			baseConfidence: 0.4, // Lower base confidence for long-tail
			rank: candidate.rank,
			sourceReliability: this.getLongTailSourceReliability(candidate.source),
			hasMetadata: this.hasQualityMetadata(candidate),
			isRecent: this.hasRecentActivity(candidate),
		};

		// Additional long-tail specific adjustments
		let confidence = this.calculateConfidence(factors);

		// Boost confidence for domains with better long-tail indicators
		if (this.hasLongTailQualityIndicators(candidate))
		{
			confidence += 0.1;
		}

		// Penalize domains that are too far in the long tail
		if (candidate.rank && candidate.rank > 5000000)
		{
			confidence -= 0.15; // More aggressive penalty
		}

		// Additional penalty for very low frequency
		if (candidate.metadata?.frequency && candidate.metadata.frequency < 5)
		{
			confidence -= 0.1;
		}

		// Additional rank-based adjustment for long-tail
		if (candidate.rank)
		{
			// Better confidence for domains closer to 1M threshold
			const rankFactor = Math.max(0, (10000000 - candidate.rank) / 9000000);
			confidence += rankFactor * 0.1;
		}

		return Math.max(0, Math.min(1, confidence));
	}

	/**
	 * Build discovery reason for long-tail exploration
	 */
	private buildLongTailDiscoveryReason(candidate: DomainCandidateType): string
	{
		const rank = candidate.rank || 'unknown';
		const source = candidate.source;

		let reason = `Long-tail discovery from ${source}`;

		if (typeof rank === 'number')
		{
			reason += ` (rank ${rank.toLocaleString()})`;
		}

		// Add additional context if available
		if (candidate.metadata?.frequency)
		{
			reason += ` with frequency ${candidate.metadata.frequency}`;
		}

		if (candidate.metadata?.category)
		{
			reason += ` in category ${candidate.metadata.category}`;
		}

		return reason;
	}

	/**
	 * Get source reliability for long-tail sources
	 */
	private getLongTailSourceReliability(source: string): number
	{
		const reliabilityMap: Record<string, number> = {
			'common-crawl': 0.75,
			sonar: 0.70,
			fdns: 0.65,
			rapid7: 0.70,
		};

		return reliabilityMap[source.toLowerCase()] || 0.5;
	}

	/**
	 * Check if candidate has quality metadata for long-tail
	 */
	private hasQualityMetadata(candidate: DomainCandidateType): boolean
	{
		if (!candidate.metadata)
		{
			return false;
		}

		// Look for quality indicators in metadata
		const qualityIndicators = [
			'frequency',
			'category',
			'lastSeen',
			'responseCode',
			'contentType',
		];

		return qualityIndicators.some(indicator => candidate.metadata![indicator]);
	}

	/**
	 * Check if candidate has recent activity indicators
	 */
	private hasRecentActivity(candidate: DomainCandidateType): boolean
	{
		if (!candidate.metadata?.lastSeen)
		{
			return false;
		}

		try
		{
			const lastSeen = new Date(candidate.metadata.lastSeen);
			const thirtyDaysAgo = new Date();
			thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

			return lastSeen >= thirtyDaysAgo;
		}
		catch
		{
			return false;
		}
	}

	/**
	 * Check for long-tail quality indicators
	 */
	private hasLongTailQualityIndicators(candidate: DomainCandidateType): boolean
	{
		if (!candidate.metadata)
		{
			return false;
		}

		// Check for indicators that suggest this is a quality long-tail domain
		const qualityChecks = [
			// Has reasonable frequency/traffic
			candidate.metadata.frequency && candidate.metadata.frequency > 10,
			// Has valid HTTP response
			candidate.metadata.responseCode && candidate.metadata.responseCode < 400,
			// Has content type indicating real website
			candidate.metadata.contentType && candidate.metadata.contentType.includes('html'),
			// Has been seen recently
			this.hasRecentActivity(candidate),
		];

		return qualityChecks.filter(Boolean).length >= 2;
	}

	/**
	 * Check if domain appears to be spam or low quality
	 */
	private isLikelySpam(domain: string): boolean
	{
		// Simple heuristics for spam detection
		const spamIndicators = [
			// Excessive numbers
			/\d{8,}/.test(domain),
			// Random character sequences
			/[a-z]{20,}/.test(domain),
			// Known spam patterns
			/^(www\d+|test\d+|temp\d+)\./.test(domain),
			// Suspicious TLDs (basic check)
			/\.(tk|ml|ga|cf)$/.test(domain),
		];

		return spamIndicators.some(indicator => indicator);
	}

	/**
	 * Check if candidate has minimum required metadata
	 */
	private hasMinimumMetadata(candidate: DomainCandidateType): boolean
	{
		// For long-tail sources, we expect at least some metadata
		return !!(candidate.metadata && Object.keys(candidate.metadata).length > 0);
	}

	/**
	 * Group candidates by source
	 */
	private groupCandidatesBySource(
		candidates: DomainCandidateType[],
	): Map<string, DomainCandidateType[]>
	{
		const grouped = new Map<string, DomainCandidateType[]>();

		for (const candidate of candidates)
		{
			const source = candidate.source;
			if (!grouped.has(source))
			{
				grouped.set(source, []);
			}
			grouped.get(source)!.push(candidate);
		}

		return grouped;
	}

	/**
	 * Calculate average rank for candidates
	 */
	private calculateAverageRank(candidates: DomainCandidateType[]): number
	{
		const rankedCandidates = candidates.filter(c => c.rank);
		if (rankedCandidates.length === 0)
		{
			return 0;
		}

		const totalRank = rankedCandidates.reduce((sum, c) => sum + (c.rank || 0), 0);
		return Math.round(totalRank / rankedCandidates.length);
	}

	/**
	 * Get minimum confidence threshold for long-tail discoveries
	 */
	private getMinimumConfidence(): number
	{
		return 0.35; // Slightly higher threshold to filter out very low quality domains
	}
}

export { LongTailProcessor };

export default LongTailProcessor;
