/**
 * Radar Domain Discovery Strategy
 * Discovers NEW domains by comparing current rankings with previous snapshots
 * and checking against our existing database
 */

import { logger as sharedLogger } from '@shared';
import type { DatabaseManager } from '@shared';
import RadarConnector from '../../connectors/RadarConnector';
import type { DomainCandidateType } from '../../interfaces/SourceConnector';

const logger = sharedLogger.getLogger('RadarDomainDiscovery');

export interface DiscoveryOptions {
	limit?: number;
	lookbackDays?: number;
	minRankChange?: number;
}

export class RadarDomainDiscovery {
	private dbManager: DatabaseManager;
	private radarConnector: RadarConnector;

	constructor(dbManager: DatabaseManager) {
		this.dbManager = dbManager;
		this.radarConnector = new RadarConnector({});
	}

	/**
	 * Discover new domains by analyzing current rankings and comparing with our database
	 */
	async discoverNewDomains(options: DiscoveryOptions = {}): Promise<DomainCandidateType[]> {
		const { limit = 1000, lookbackDays = 7 } = options;

		try {
			logger.info('Starting new domain discovery from Radar', { limit, lookbackDays });

			// Step 1: Fetch current top domains from Radar
			const currentDomains: DomainCandidateType[] = [];

			for await (const domain of this.radarConnector.fetchDomains({
				limit: Math.min(limit * 2, 5000), // Fetch more to filter from
				strategy: 'differential'
			})) {
				currentDomains.push(domain);
			}

			logger.info(`Fetched ${currentDomains.length} domains from Radar`);

			// Step 2: Check which domains are NEW (not in our database)
			const newDomains = await this.filterNewDomains(currentDomains);

			logger.info(`Found ${newDomains.length} potentially new domains`);

			// Step 3: Look for domains that recently appeared or moved up significantly
			const risingDomains = await this.findRisingDomains(currentDomains, lookbackDays);

			// Step 4: Combine and deduplicate
			const discoveredDomains = this.combineAndDeduplicate(newDomains, risingDomains);

			// Step 5: Apply final limit
			const finalResults = discoveredDomains.slice(0, limit);

			logger.info(`Discovery complete: ${finalResults.length} new domains discovered`);

			return finalResults;

		} catch (error) {
			logger.error('Failed to discover new domains from Radar', { error: error.message });
			throw error;
		}
	}

	/**
	 * Filter domains that don't exist in our database
	 */
	private async filterNewDomains(domains: DomainCandidateType[]): Promise<DomainCandidateType[]> {
		const newDomains: DomainCandidateType[] = [];

		try {
			// Check against Manticore search index if available
			const manticore = this.dbManager.getManticoreClient();

			for (const domain of domains) {
				try {
					const exists = await this.checkDomainExists(domain.domain, manticore);
					if (!exists) {
						// Mark as new domain discovery
						domain.metadata = {
							...domain.metadata,
							discoveryReason: 'new_domain_not_in_database',
							discoveryConfidence: 0.9
						};
						newDomains.push(domain);
					}
				} catch (error) {
					logger.warn(`Failed to check domain existence: ${domain.domain}`, { error: error.message });
					// If we can't check, assume it might be new
					domain.metadata = {
						...domain.metadata,
						discoveryReason: 'unknown_status_assumed_new',
						discoveryConfidence: 0.5
					};
					newDomains.push(domain);
				}
			}

		} catch (error) {
			logger.error('Failed to filter new domains', { error: error.message });
			// If database check fails, return all domains as potentially new
			return domains.map(domain => ({
				...domain,
				metadata: {
					...domain.metadata,
					discoveryReason: 'database_check_failed',
					discoveryConfidence: 0.3
				}
			}));
		}

		return newDomains;
	}

	/**
	 * Check if a domain exists in our database
	 */
	private async checkDomainExists(domain: string, manticore: any): Promise<boolean> {
		try {
			// Escape single quotes for SQL safety
			const escapedDomain = domain.replace(/'/g, "''");
			const sql = `SELECT 1 FROM domains_index WHERE domain='${escapedDomain}' LIMIT 1`;

			const result = await manticore.sql(sql);
			const data = (result as any).data;

			return Array.isArray(data) ? data.length > 0 : Boolean((result as any).total);
		} catch (error) {
			logger.debug(`Domain existence check failed for ${domain}`, { error: error.message });
			return false; // Assume new if check fails
		}
	}

	/**
	 * Find domains that have risen significantly in rankings
	 * This would ideally compare with historical data
	 */
	private async findRisingDomains(
		currentDomains: DomainCandidateType[],
		lookbackDays: number
	): Promise<DomainCandidateType[]> {
		// For now, we'll identify "rising" domains as those in positions that suggest recent growth
		// In a full implementation, this would compare with historical snapshots

		const risingDomains: DomainCandidateType[] = [];

		// Look for domains in the "rising" range (positions 1000-10000)
		// These are often newer domains that have gained traction
		for (const domain of currentDomains) {
			if (domain.rank && domain.rank >= 1000 && domain.rank <= 10000) {
				// Check if it's in our database
				try {
					const manticore = this.dbManager.getManticoreClient();
					const exists = await this.checkDomainExists(domain.domain, manticore);

					if (!exists) {
						domain.metadata = {
							...domain.metadata,
							discoveryReason: 'rising_domain_mid_tier',
							discoveryConfidence: 0.7
						};
						risingDomains.push(domain);
					}
				} catch (error) {
					// If check fails, include it anyway
					domain.metadata = {
						...domain.metadata,
						discoveryReason: 'rising_domain_unknown_status',
						discoveryConfidence: 0.4
					};
					risingDomains.push(domain);
				}
			}
		}

		logger.info(`Found ${risingDomains.length} rising domains`);
		return risingDomains;
	}

	/**
	 * Combine and deduplicate domain lists
	 */
	private combineAndDeduplicate(
		newDomains: DomainCandidateType[],
		risingDomains: DomainCandidateType[]
	): DomainCandidateType[] {
		const domainMap = new Map<string, DomainCandidateType>();

		// Add new domains first (higher priority)
		for (const domain of newDomains) {
			domainMap.set(domain.domain, domain);
		}

		// Add rising domains (if not already present)
		for (const domain of risingDomains) {
			if (!domainMap.has(domain.domain)) {
				domainMap.set(domain.domain, domain);
			}
		}

		return Array.from(domainMap.values());
	}

	/**
	 * Simulate discovering domains from different ranking positions
	 * This helps find domains that might be new but not in top 100
	 */
	async discoverFromDifferentRanges(): Promise<DomainCandidateType[]> {
		const discoveredDomains: DomainCandidateType[] = [];

		// Different ranking ranges to explore for new domains
		const ranges = [
			{ start: 100, end: 500, reason: 'emerging_top_tier' },
			{ start: 500, end: 2000, reason: 'growing_mid_tier' },
			{ start: 2000, end: 10000, reason: 'rising_lower_tier' }
		];

		for (const range of ranges) {
			try {
				logger.info(`Exploring range ${range.start}-${range.end} for new domains`);

				// Note: Cloudflare Radar API might not support range queries
				// This is a placeholder for when such functionality is available
				// For now, we work with what we can fetch

				const domains: DomainCandidateType[] = [];

				// Fetch domains and filter by range
				for await (const domain of this.radarConnector.fetchDomains({
					limit: 1000,
					strategy: 'differential'
				})) {
					if (domain.rank && domain.rank >= range.start && domain.rank <= range.end) {
						domain.metadata = {
							...domain.metadata,
							discoveryReason: range.reason,
							discoveryConfidence: 0.6
						};
						domains.push(domain);
					}
				}

				// Check which are new
				const newInRange = await this.filterNewDomains(domains);
				discoveredDomains.push(...newInRange);

			} catch (error) {
				logger.warn(`Failed to explore range ${range.start}-${range.end}`, { error: error.message });
			}
		}

		return discoveredDomains;
	}
}