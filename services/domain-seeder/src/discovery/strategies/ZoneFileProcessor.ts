import type {
	DiscoveredDomainInterface,
	SnapshotStoreType,
} from '../../interfaces/DiscoveryEngine';
import type { DomainCandidateType } from '../../interfaces/SourceConnector';
import { BaseStrategyProcessor } from './BaseStrategyProcessor';

/**
 * Zone File Processing Strategy Processor
 * Identifies newly registered domains from CZDS zone files
 * Focuses on domains registered within the last 48 hours
 */
class ZoneFileProcessor extends BaseStrategyProcessor
{
	private static readonly REGISTRATION_CUTOFF_HOURS = 48;

	private static readonly ZONE_FILE_SOURCES = new Set([
		'czds',
		'pir',
		'registry',
	]);

	constructor()
	{
		super('zone-new');
	}

	/**
	 * Process candidates using zone file analysis
	 * Identifies domains that were recently registered based on zone file metadata
	 */
	async process(
		candidates: DomainCandidateType[],
		snapshotStore: SnapshotStoreType,
	): Promise<DiscoveredDomainInterface[]>
	{
		const startTime = Date.now();

		try
		{
			this.logger.info({
				candidateCount: candidates.length,
				msg: 'Starting zone file processing',
			});

			// Validate and filter candidates
			const validCandidates = this.validateCandidates(candidates);
			if (validCandidates.length === 0)
			{
				this.logger.warn({ msg: 'No valid candidates for zone file processing' });
				return [];
			}

			// Filter for zone file sources only
			const zoneFileCandidates = this.filterZoneFileCandidates(validCandidates);
			if (zoneFileCandidates.length === 0)
			{
				this.logger.info({
					totalCandidates: validCandidates.length,
					msg: 'No zone file candidates found',
				});
				return [];
			}

			// Process candidates for recent registrations
			const discovered = this.processRecentRegistrations(zoneFileCandidates);

			const processingTime = Date.now() - startTime;
			this.logResults(validCandidates.length, discovered.length, processingTime, {
				zoneFileCandidates: zoneFileCandidates.length,
				cutoffHours: ZoneFileProcessor.REGISTRATION_CUTOFF_HOURS,
			});

			return discovered;
		}
		catch (error)
		{
			this.handleError(error as Error, {
				candidateCount: candidates.length,
			});
		}
	}

	/**
	 * Filter candidates to only include those from zone file sources
	 */
	private filterZoneFileCandidates(candidates: DomainCandidateType[]): DomainCandidateType[]
	{
		const filtered = candidates.filter(candidate => ZoneFileProcessor.ZONE_FILE_SOURCES.has(candidate.source.toLowerCase()));

		this.logger.debug({
			input: candidates.length,
			output: filtered.length,
			supportedSources: Array.from(ZoneFileProcessor.ZONE_FILE_SOURCES),
			msg: 'Zone file candidates filtered',
		});

		return filtered;
	}

	/**
	 * Process candidates to identify recently registered domains
	 */
	private processRecentRegistrations(candidates: DomainCandidateType[]): DiscoveredDomainInterface[]
	{
		const cutoffDate = this.calculateCutoffDate();
		const discovered: DiscoveredDomainInterface[] = [];

		this.logger.debug({
			candidateCount: candidates.length,
			cutoffDate: cutoffDate.toISOString(),
			msg: 'Processing for recent registrations',
		});

		for (const candidate of candidates)
		{
			const registrationInfo = this.extractRegistrationInfo(candidate);

			if (registrationInfo && this.isRecentRegistration(registrationInfo.date, cutoffDate))
			{
				const confidence = this.calculateZoneFileConfidence(candidate, registrationInfo);
				const discoveryReason = this.buildZoneFileDiscoveryReason(candidate, registrationInfo);

				discovered.push(this.createDiscoveredDomain(
					candidate,
					confidence,
					discoveryReason,
				));

				this.logger.debug({
					domain: candidate.domain,
					source: candidate.source,
					registrationDate: registrationInfo.date.toISOString(),
					confidence,
					msg: 'Recent registration discovered',
				});
			}
		}

		return discovered;
	}

	/**
	 * Extract registration information from candidate metadata
	 */
	private extractRegistrationInfo(candidate: DomainCandidateType): RegistrationInfo | null
	{
		if (!candidate.metadata)
		{
			return null;
		}

		// Try different metadata field names for registration date
		const dateFields = [
			'registrationDate',
			'registration_date',
			'creationDate',
			'creation_date',
			'created',
			'createdAt',
			'created_at',
			'zoneDate',
			'zone_date',
		];

		for (const field of dateFields)
		{
			const dateValue = candidate.metadata[field];
			if (dateValue)
			{
				const parsedDate = this.parseRegistrationDate(dateValue);
				if (parsedDate)
				{
					return {
						date: parsedDate,
						source: field,
						raw: dateValue,
					};
				}
			}
		}

		// Try to extract from zone file metadata
		if (candidate.metadata.zoneFileInfo)
		{
			const zoneInfo = candidate.metadata.zoneFileInfo;
			if (zoneInfo.registrationDate || zoneInfo.creationDate)
			{
				const dateValue = zoneInfo.registrationDate || zoneInfo.creationDate;
				const parsedDate = this.parseRegistrationDate(dateValue);
				if (parsedDate)
				{
					return {
						date: parsedDate,
						source: 'zoneFileInfo',
						raw: dateValue,
					};
				}
			}
		}

		return null;
	}

	/**
	 * Parse registration date from various formats
	 */
	private parseRegistrationDate(dateValue: unknown): Date | null
	{
		if (!dateValue)
		{
			return null;
		}

		try
		{
			// Handle different date formats
			if (dateValue instanceof Date)
			{
				return dateValue;
			}

			if (typeof dateValue === 'string')
			{
				// Try ISO format first
				const isoDate = new Date(dateValue);
				if (!isNaN(isoDate.getTime()))
				{
					return isoDate;
				}

				// Try common date formats
				const formats = [
					/^\d{4}-\d{2}-\d{2}$/, // YYYY-MM-DD
					/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/, // ISO datetime
					/^\d{4}\/\d{2}\/\d{2}$/, // YYYY/MM/DD
					/^\d{2}\/\d{2}\/\d{4}$/, // MM/DD/YYYY
				];

				for (const format of formats)
				{
					if (format.test(dateValue))
					{
						const parsed = new Date(dateValue);
						if (!isNaN(parsed.getTime()))
						{
							return parsed;
						}
					}
				}
			}

			if (typeof dateValue === 'number')
			{
				// Handle Unix timestamps (seconds or milliseconds)
				const timestamp = dateValue < 10000000000 ? dateValue * 1000 : dateValue;
				const date = new Date(timestamp);
				if (!isNaN(date.getTime()))
				{
					return date;
				}
			}
		}
		catch (error)
		{
			this.logger.warn({
				dateValue,
				error: error instanceof Error ? error.message : String(error),
				msg: 'Failed to parse registration date'
			});
		}

		return null;
	}

	/**
	 * Check if registration date is within the cutoff period
	 */
	private isRecentRegistration(registrationDate: Date, cutoffDate: Date): boolean
	{
		return registrationDate >= cutoffDate;
	}

	/**
	 * Calculate cutoff date for recent registrations
	 */
	private calculateCutoffDate(): Date
	{
		const cutoff = new Date();
		cutoff.setHours(cutoff.getHours() - ZoneFileProcessor.REGISTRATION_CUTOFF_HOURS);
		return cutoff;
	}

	/**
	 * Calculate confidence for zone file discoveries
	 */
	private calculateZoneFileConfidence(
		candidate: DomainCandidateType,
		registrationInfo: RegistrationInfo,
	): number
	{
		let baseConfidence = 0.6; // Start with lower base confidence to allow for differentiation

		// Boost confidence for very recent registrations (last 24 hours)
		if (this.isVeryRecentRegistration(registrationInfo.date))
		{
			baseConfidence += 0.1;
		}

		// Boost confidence for authoritative sources
		if (this.isAuthoritativeSource(candidate.source))
		{
			baseConfidence += 0.05;
		}

		const factors = {
			baseConfidence,
			rank: candidate.rank,
			sourceReliability: this.getZoneFileSourceReliability(candidate.source),
			hasMetadata: !!(candidate.metadata && Object.keys(candidate.metadata).length > 0),
			isRecent: this.isVeryRecentRegistration(registrationInfo.date),
		};

		return this.calculateConfidence(factors);
	}

	/**
	 * Build discovery reason for zone file discoveries
	 */
	private buildZoneFileDiscoveryReason(
		candidate: DomainCandidateType,
		registrationInfo: RegistrationInfo,
	): string
	{
		const hoursAgo = Math.floor(
			(Date.now() - registrationInfo.date.getTime()) / (1000 * 60 * 60),
		);

		const timeDescription = hoursAgo < 24
			? `${hoursAgo} hours ago`
			: `${Math.floor(hoursAgo / 24)} days ago`;

		return `Newly registered domain from ${candidate.source} zone file (registered ${timeDescription})`;
	}

	/**
	 * Check if registration is very recent (last 24 hours)
	 */
	private isVeryRecentRegistration(registrationDate: Date): boolean
	{
		const twentyFourHoursAgo = new Date();
		twentyFourHoursAgo.setHours(twentyFourHoursAgo.getHours() - 24);
		return registrationDate >= twentyFourHoursAgo;
	}

	/**
	 * Get source reliability for zone file sources
	 */
	private getZoneFileSourceReliability(source: string): number
	{
		const reliabilityMap: Record<string, number> = {
			czds: 0.85, // CZDS is highly authoritative
			pir: 0.80, // PIR .org registry is authoritative
			registry: 0.70, // Generic registry sources
		};

		return reliabilityMap[source.toLowerCase()] || 0.60;
	}

	/**
	 * Check if source is authoritative (direct from registry)
	 */
	private isAuthoritativeSource(source: string): boolean
	{
		const authoritativeSources = new Set(['czds', 'pir']);
		return authoritativeSources.has(source.toLowerCase());
	}

	/**
	 * Validate candidates specifically for zone file processing
	 */
	protected override validateCandidates(candidates: DomainCandidateType[]): DomainCandidateType[]
	{
		const baseValid = super.validateCandidates(candidates);

		// Additional validation for zone file processing
		const zoneFileValid = baseValid.filter((candidate) =>
		{
			// Must have metadata for registration date extraction
			if (!candidate.metadata)
			{
				this.logger.debug({
					domain: candidate.domain,
					source: candidate.source,
					msg: 'Candidate filtered: no metadata for registration date'
				});
				return false;
			}

			return true;
		});

		this.logger.debug({
			input: candidates.length,
			baseValid: baseValid.length,
			zoneFileValid: zoneFileValid.length,
			msg: 'Zone file validation completed'
		});

		return zoneFileValid;
	}
}

interface RegistrationInfo
{
	date: Date;
	source: string;
	raw: unknown;
}

export { ZoneFileProcessor };

export default ZoneFileProcessor;
