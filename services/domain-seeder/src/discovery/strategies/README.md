# Discovery Strategies

This directory contains the implementation of various domain discovery strategies used by the domain-seeder service.

## Overview

The domain-seeder service uses multiple strategies to discover new domains that are not yet in our database. Each strategy focuses on different aspects of domain discovery to ensure comprehensive coverage.

## Implemented Strategies

### Differential Analysis Strategy

**File**: `DifferentialAnalysisProcessor.ts`

**Purpose**: Identifies domains that newly appear in ranking lists by comparing current snapshots with historical snapshots.

**How it works**:

1. Takes current domain candidates from various sources (Tranco, Cloudflare Radar, etc.)
2. Retrieves historical snapshots from the previous day for each source
3. Compares current candidates against historical data to find new entries
4. Returns domains that weren't present in the historical snapshot
5. Stores current snapshot for future comparisons

**Key Features**:

- **Multi-source support**: Processes different sources independently
- **Case-insensitive comparison**: Handles domain case variations correctly
- **Confidence scoring**: Assigns confidence based on rank, source reliability, and metadata
- **Error resilience**: Continues processing other sources if one fails
- **Configurable retention**: Historical snapshots are managed with configurable retention periods

**Confidence Factors**:

- **Base confidence**: 0.7 for differential analysis
- **Rank bonus**: Higher-ranked domains (lower numbers) get higher confidence
- **Source reliability**: Tranco (0.95), Radar (0.90), Umbrella (0.85), etc.
- **Metadata bonus**: Domains with additional metadata get slight confidence boost
- **Recency bonus**: Recent snapshots contribute to higher confidence

**Example Usage**:

```typescript
const processor = new DifferentialAnalysisProcessor();
const snapshotStore = new RedisSnapshotStore(redisClient);

const candidates = [
  { domain: "example.com", source: "tranco", rank: 1000 },
  { domain: "newdomain.com", source: "tranco", rank: 2000 },
];

const discovered = await processor.process(candidates, snapshotStore);
// Returns domains that weren't in yesterday's snapshot
```

## Base Strategy Processor

**File**: `BaseStrategyProcessor.ts`

All strategy processors extend this base class which provides:

- **Validation**: Common candidate validation logic
- **Confidence calculation**: Standardized confidence scoring
- **Error handling**: Consistent error handling patterns
- **Logging**: Structured logging with strategy context
- **Result creation**: Standardized discovered domain result format

## Snapshot Storage

The differential analysis strategy relies on the `RedisSnapshotStore` for historical data management:

- **Storage**: Snapshots are stored in Redis with 30-day TTL
- **Compression**: Optional compression for large snapshots
- **Metadata**: Source metadata tracking for monitoring
- **Cleanup**: Automatic cleanup of old snapshots
- **Statistics**: Snapshot statistics for operational insights

## Testing

Each strategy has comprehensive test coverage:

- **Unit tests**: Individual strategy logic testing
- **Integration tests**: End-to-end workflow testing
- **Property-based tests**: Edge case and robustness testing
- **Mock data**: Realistic test scenarios with various data patterns

## Performance Considerations

- **Batch processing**: Strategies process candidates in batches for efficiency
- **Parallel processing**: Multiple sources are processed in parallel where possible
- **Memory management**: Large datasets are streamed to avoid memory issues
- **Caching**: Redis-based caching for frequently accessed data

## Error Handling

- **Graceful degradation**: Strategies continue processing despite individual failures
- **Retry logic**: Transient failures are retried with exponential backoff
- **Circuit breakers**: Prevent cascade failures from external dependencies
- **Comprehensive logging**: All errors are logged with context for debugging

## Future Strategies

The framework is designed to support additional strategies:

- **Zone File Processing**: New domain registrations from CZDS
- **Long-tail Exploration**: Domains beyond typical top-1M lists
- **Temporal Analysis**: Rising domains with significant rank improvements

Each new strategy should:

1. Extend `BaseStrategyProcessor`
2. Implement the `process` method
3. Include comprehensive tests
4. Document strategy-specific behavior
5. Follow the established patterns for error handling and logging
