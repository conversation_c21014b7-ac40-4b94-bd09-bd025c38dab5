import { ScyllaClient, logger as sharedLogger } from '@shared';
import { createHash, randomBytes } from 'node:crypto';
import { gzip, gunzip } from 'node:zlib';
import { promisify } from 'node:util';

import type { SnapshotStoreType, DomainSnapshotType } from '../../interfaces/DiscoveryEngine';
import type { DomainCandidateType } from '../../interfaces/SourceConnector';

const gzipAsync = promisify(gzip);
const gunzipAsync = promisify(gunzip);

type ScyllaSnapshotRowType =
{
	source: string;
	date: string;
	data: Buffer;
	hash: string;
	compressed: boolean;
	domain_count: number;
	created_at: Date;
};

type SnapshotStatisticsType =
{
	totalSnapshots: number;
	totalDomains: number;
	sourceCount: number;
	sources: SourceStatisticsType[];
};

type SourceStatisticsType =
{
	source: string;
	snapshotCount: number;
	totalDomains: number;
	latestDate: string;
	oldestDate: string;
};

/**
 * ScyllaDB-based implementation of SnapshotStore
 * Provides persistent storage for domain snapshots with compression
 */
class ScyllaSnapshotStore implements SnapshotStoreType
{
	private readonly scylla: ScyllaClient;

	private readonly logger: ReturnType<typeof sharedLogger.getLogger>;

	private readonly compressionThreshold: number;

	private readonly tableName: string;

	constructor(
		scylla: ScyllaClient,
		options: {
			compressionThreshold?: number;
			tableName?: string;
		} = {},
	)
	{
		this.scylla = scylla;
		this.logger = sharedLogger.getLogger('ScyllaSnapshotStore');
		this.compressionThreshold = options.compressionThreshold || 10000; // 10KB
		this.tableName = options.tableName || 'domain_snapshots';

		this.initializeSchema();
	}

	/**
	 * Initialize database schema
	 */
	private async initializeSchema(): Promise<void>
	{
		try
		{
			const createTableQuery = `
				CREATE TABLE IF NOT EXISTS ${this.tableName} (
					source text,
					date text,
					data blob,
					hash text,
					compressed boolean,
					domain_count int,
					created_at timestamp,
					PRIMARY KEY (source, date)
				) WITH CLUSTERING ORDER BY (date DESC)
				AND gc_grace_seconds = 2592000
				AND compaction = {
					'class': 'TimeWindowCompactionStrategy',
					'compaction_window_unit': 'DAYS',
					'compaction_window_size': 1
				}
			`;

			await this.scylla.execute(createTableQuery);

			// Create index for cleanup operations
			const createIndexQuery = `
				CREATE INDEX IF NOT EXISTS snapshots_created_at_idx
				ON ${this.tableName} (created_at)
			`;

			await this.scylla.execute(createIndexQuery);

			this.logger.debug({
				tableName: this.tableName,
			}, 'ScyllaDB schema initialized');
		}
		catch (error)
		{
			this.logger.error({
				error: error instanceof Error ? error.message : String(error),
			}, 'Failed to initialize ScyllaDB schema');
			throw error;
		}
	}

	/**
	 * Get a snapshot for a specific source and date
	 */
	async getSnapshot(source: string, date: string): Promise<DomainSnapshotType | null>
	{
		try
		{
			const query = `
				SELECT source, date, data, hash, compressed, domain_count, created_at
				FROM ${this.tableName}
				WHERE source = ? AND date = ?
			`;

			const result = await this.scylla.execute(query, [source, date]);

			if (result.rows.length === 0)
			{
				this.logger.debug({ source, date }, 'Snapshot not found');
				return null;
			}

			const row = result.rows[0] as unknown as ScyllaSnapshotRowType;
			const snapshot = await this.deserializeSnapshot(row);

			// Verify data integrity
			if (!this.verifyDataIntegrity(snapshot, row.hash))
			{
				this.logger.error({
					source,
					date,
					expectedHash: row.hash,
				}, 'Snapshot data integrity check failed');
				return null;
			}

			this.logger.debug({
				source,
				date,
				domainCount: snapshot.domains.length,
				compressed: row.compressed,
			}, 'Snapshot retrieved');

			return snapshot;
		}
		catch (error)
		{
			this.logger.error({
				source,
				date,
				error: error instanceof Error ? error.message : String(error),
			}, 'Failed to get snapshot');
			return null;
		}
	}

	/**
	 * Store a snapshot for a source
	 */
	async storeSnapshot(source: string, candidates: DomainCandidateType[]): Promise<void>
	{
		try
		{
			const date = new Date().toISOString().split('T')[0];
			const snapshot: DomainSnapshotType = {
				source,
				date,
				domains: candidates,
				totalCount: candidates.length,
			};

			// Serialize and optionally compress the data
			const { data, compressed } = await this.serializeSnapshot(snapshot);
			const hash = this.calculateHash(snapshot);

			const query = `
				INSERT INTO ${this.tableName}
				(source, date, data, hash, compressed, domain_count, created_at)
				VALUES (?, ?, ?, ?, ?, ?, ?)
			`;

			await this.scylla.execute(query, [
				source,
				date,
				data,
				hash,
				compressed,
				candidates.length,
				new Date(),
			]);

			this.logger.info({
				source,
				date,
				domainCount: candidates.length,
				compressed,
				dataSize: data.length,
				hash,
			}, 'Snapshot stored in ScyllaDB');
		}
		catch (error)
		{
			this.logger.error({
				source,
				candidateCount: candidates.length,
				error: error instanceof Error ? error.message : String(error),
			}, 'Failed to store snapshot');
			throw error;
		}
	}

	/**
	 * Get recent snapshots for a source
	 */
	async getRecentSnapshots(source: string, days: number): Promise<DomainSnapshotType[]>
	{
		try
		{
			const cutoffDate = new Date();
			cutoffDate.setDate(cutoffDate.getDate() - days);
			const cutoffDateStr = cutoffDate.toISOString().split('T')[0];

			const query = `
				SELECT source, date, data, hash, compressed, domain_count, created_at
				FROM ${this.tableName}
				WHERE source = ? AND date >= ?
				ORDER BY date DESC
				LIMIT ?
			`;

			const result = await this.scylla.execute(query, [source, cutoffDateStr, days]);
			const snapshots: DomainSnapshotType[] = [];

			for (const row of result.rows as unknown as ScyllaSnapshotRowType[])
			{
				try
				{
					const snapshot = await this.deserializeSnapshot(row);

					// Verify data integrity
					if (this.verifyDataIntegrity(snapshot, row.hash))
					{
						snapshots.push(snapshot);
					}
					else
					{
						this.logger.warn({
							source: row.source,
							date: row.date,
						}, 'Skipping snapshot with integrity issues');
					}
				}
				catch (error)
				{
					this.logger.warn({
						source: row.source,
						date: row.date,
						error: error instanceof Error ? error.message : String(error),
					}, 'Failed to deserialize snapshot');
				}
			}

			this.logger.debug({
				source,
				requestedDays: days,
				foundSnapshots: snapshots.length,
			}, 'Recent snapshots retrieved');

			return snapshots;
		}
		catch (error)
		{
			this.logger.error({
				source,
				days,
				error: error instanceof Error ? error.message : String(error),
			}, 'Failed to get recent snapshots');
			return [];
		}
	}

	/**
	 * Clean up old snapshots beyond retention period
	 */
	async cleanup(retentionDays: number): Promise<void>
	{
		try
		{
			this.logger.info({ retentionDays }, 'Starting ScyllaDB snapshot cleanup');

			const cutoffDate = new Date();
			cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

			// First, get all snapshots older than retention period
			const selectQuery = `
				SELECT source, date
				FROM ${this.tableName}
				WHERE created_at < ?
				ALLOW FILTERING
			`;

			const result = await this.scylla.execute(selectQuery, [cutoffDate]);
			let deletedCount = 0;

			// Delete old snapshots in batches
			const batchSize = 100;
			for (let i = 0; i < result.rows.length; i += batchSize)
			{
				const batch = result.rows.slice(i, i + batchSize);
				const deletePromises = batch.map(async (row) =>
				{
					try
					{
						const deleteQuery = `
							DELETE FROM ${this.tableName}
							WHERE source = ? AND date = ?
						`;
						const snapshotRow = row as unknown as ScyllaSnapshotRowType;
						await this.scylla.execute(deleteQuery, [snapshotRow.source, snapshotRow.date]);
						deletedCount++;
					}
					catch (error)
					{
						this.logger.warn({
							source: row.source,
							date: row.date,
							error: error instanceof Error ? error.message : String(error),
						}, 'Failed to delete snapshot');
					}
				});

				await Promise.allSettled(deletePromises);
			}

			this.logger.info({
				totalSnapshots: result.rows.length,
				deletedSnapshots: deletedCount,
				retentionDays,
			}, 'ScyllaDB snapshot cleanup completed');
		}
		catch (error)
		{
			this.logger.error({
				retentionDays,
				error: error instanceof Error ? error.message : String(error),
			}, 'ScyllaDB snapshot cleanup failed');
			throw error;
		}
	}

	/**
	 * Get snapshot statistics
	 */
	async getStatistics(): Promise<SnapshotStatisticsType>
	{
		try
		{
			const query = `
				SELECT source, date, domain_count
				FROM ${this.tableName}
			`;

			const result = await this.scylla.execute(query);
			const sourceStats = new Map<string, SourceStatisticsType>();
			let totalSnapshots = 0;
			let totalDomains = 0;

			for (const row of result.rows)
			{
				const snapshotRow = row as unknown as ScyllaSnapshotRowType;
				totalSnapshots++;
				totalDomains += snapshotRow.domain_count;

				if (!sourceStats.has(snapshotRow.source))
				{
					sourceStats.set(snapshotRow.source, {
						source: snapshotRow.source,
						snapshotCount: 0,
						totalDomains: 0,
						latestDate: '',
						oldestDate: '',
					});
				}

				const stats = sourceStats.get(snapshotRow.source)!;
				stats.snapshotCount++;
				stats.totalDomains += snapshotRow.domain_count;

				if (!stats.latestDate || snapshotRow.date > stats.latestDate)
				{
					stats.latestDate = snapshotRow.date;
				}

				if (!stats.oldestDate || snapshotRow.date < stats.oldestDate)
				{
					stats.oldestDate = snapshotRow.date;
				}
			}

			return {
				totalSnapshots,
				totalDomains,
				sourceCount: sourceStats.size,
				sources: Array.from(sourceStats.values()),
			};
		}
		catch (error)
		{
			this.logger.error({
				error: error instanceof Error ? error.message : String(error),
			}, 'Failed to get snapshot statistics');
			throw error;
		}
	}

	/**
	 * Serialize snapshot data with optional compression
	 */
	private async serializeSnapshot(snapshot: DomainSnapshotType): Promise<{
		data: Buffer;
		compressed: boolean;
	}>
	{
		try
		{
			const jsonData = JSON.stringify(snapshot);
			const jsonBuffer = Buffer.from(jsonData, 'utf8');

			// Compress if data is larger than threshold
			if (jsonBuffer.length > this.compressionThreshold)
			{
				const compressedData = await gzipAsync(jsonBuffer);
				return {
					data: compressedData,
					compressed: true,
				};
			}

			return {
				data: jsonBuffer,
				compressed: false,
			};
		}
		catch (error)
		{
			this.logger.error({
				source: snapshot.source,
				date: snapshot.date,
				error: error instanceof Error ? error.message : String(error),
			}, 'Failed to serialize snapshot');
			throw error;
		}
	}

	/**
	 * Deserialize snapshot data with decompression support
	 */
	private async deserializeSnapshot(row: ScyllaSnapshotRowType): Promise<DomainSnapshotType>
	{
		try
		{
			let jsonData: string;

			if (row.compressed)
			{
				const decompressedBuffer = await gunzipAsync(row.data);
				jsonData = decompressedBuffer.toString('utf8');
			}
			else
			{
				jsonData = row.data.toString('utf8');
			}

			return JSON.parse(jsonData);
		}
		catch (error)
		{
			this.logger.error({
				source: row.source,
				date: row.date,
				compressed: row.compressed,
				error: error instanceof Error ? error.message : String(error),
			}, 'Failed to deserialize snapshot');
			throw error;
		}
	}

	/**
	 * Calculate SHA256 hash of snapshot data for integrity verification
	 */
	private calculateHash(snapshot: DomainSnapshotType): string
	{
		const data = JSON.stringify({
			source: snapshot.source,
			date: snapshot.date,
			domains: snapshot.domains,
			totalCount: snapshot.totalCount,
		});

		return createHash('sha256').update(data).digest('hex');
	}

	/**
	 * Verify data integrity using hash comparison
	 */
	private verifyDataIntegrity(snapshot: DomainSnapshotType, expectedHash: string): boolean
	{
		const actualHash = this.calculateHash(snapshot);
		return actualHash === expectedHash;
	}
}

export type { SnapshotStatisticsType, SourceStatisticsType };

export default ScyllaSnapshotStore;
