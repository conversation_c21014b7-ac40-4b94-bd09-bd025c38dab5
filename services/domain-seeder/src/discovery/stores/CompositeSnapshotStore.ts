import { logger as sharedLogger, type LoggerInstanceType } from '@shared';

import type {
	SnapshotStoreType,
	DomainSnapshotType,
} from '../../interfaces/DiscoveryEngine';
import type { DomainCandidateType } from '../../interfaces/SourceConnector';
import type { SnapshotStatisticsType, SourceStatisticsType } from './ScyllaSnapshotStore';

type CompositeSourceStatisticsType = SourceStatisticsType & {
	redisSnapshots: number;
	redisDomains: number;
	scyllaSnapshots: number;
	scyllaDomains: number;
};
import RedisSnapshotStore from './RedisSnapshotStore';
import ScyllaSnapshotStore from './ScyllaSnapshotStore';

/**
 * Composite snapshot store that uses Redis for fast access and ScyllaDB for persistence
 * Implements a write-through cache pattern with fallback capabilities
 */
class CompositeSnapshotStore implements SnapshotStoreType
{
	private readonly redisStore: RedisSnapshotStore;

	private readonly scyllaStore: ScyllaSnapshotStore;

	private readonly logger: LoggerInstanceType;

	private readonly preferRedis: boolean;

	private readonly enableFallback: boolean;

	constructor(
		redisStore: RedisSnapshotStore,
		scyllaStore: ScyllaSnapshotStore,
		options: {
			preferRedis?: boolean;
			enableFallback?: boolean;
		} = {},
	)
	{
		this.redisStore = redisStore;
		this.scyllaStore = scyllaStore;
		this.logger = sharedLogger.getLogger('CompositeSnapshotStore');
		this.preferRedis = options.preferRedis ?? true;
		this.enableFallback = options.enableFallback ?? true;
	}

	/**
	 * Get a snapshot for a specific source and date
	 * Tries preferred store first, falls back to secondary store if enabled
	 */
	async getSnapshot(source: string, date: string): Promise<DomainSnapshotType | null>
	{
		try
		{
			const primaryStore = this.preferRedis ? this.redisStore : this.scyllaStore;
			const secondaryStore = this.preferRedis ? this.scyllaStore : this.redisStore;

			// Try primary store first
			const primarySnapshot = await primaryStore.getSnapshot(source, date);
			if (primarySnapshot)
			{
				this.logger.debug({
					source,
					date,
					domainCount: primarySnapshot.domains.length,
					primaryStore: this.preferRedis ? 'Redis' : 'ScyllaDB',
				}, 'Snapshot retrieved from primary store');
				return primarySnapshot;
			}

			// Try secondary store if fallback is enabled
			if (this.enableFallback)
			{
				const secondarySnapshot = await secondaryStore.getSnapshot(source, date);
				if (secondarySnapshot)
				{
					this.logger.debug({
						source,
						date,
						domainCount: secondarySnapshot.domains.length,
						secondaryStore: this.preferRedis ? 'ScyllaDB' : 'Redis',
					}, 'Snapshot retrieved from secondary store');

					// Warm primary cache if snapshot found in secondary store and primary is Redis
					if (this.preferRedis && primaryStore === this.redisStore)
					{
						try
						{
							await this.redisStore.storeSnapshot(source, secondarySnapshot.domains);
							this.logger.debug({
								source,
								date,
							}, 'Warmed Redis cache from ScyllaDB snapshot');
						}
						catch (error)
						{
							this.logger.warn({
								source,
								date,
								error: error instanceof Error ? error.message : String(error),
							}, 'Failed to warm Redis cache');
						}
					}

					return secondarySnapshot;
				}
			}

			this.logger.debug({ source, date }, 'Snapshot not found in any store');
			return null;
		}
		catch (error)
		{
			this.logger.error({
				source,
				date,
				error: error instanceof Error ? error.message : String(error),
			}, 'Failed to get snapshot from composite store');
			return null;
		}
	}

	/**
	 * Store a snapshot in both Redis and ScyllaDB (write-through)
	 */
	async storeSnapshot(source: string, candidates: DomainCandidateType[]): Promise<void>
	{
		const errors: Error[] = [];

		// Store in both stores concurrently
		const storePromises = [
			this.redisStore.storeSnapshot(source, candidates).catch((error) =>
			{
				this.logger.error({
					source,
					candidateCount: candidates.length,
					error: error instanceof Error ? error.message : String(error),
				}, 'Failed to store snapshot in Redis');
				errors.push(error);
			}),
			this.scyllaStore.storeSnapshot(source, candidates).catch((error) =>
			{
				this.logger.error({
					source,
					candidateCount: candidates.length,
					error: error instanceof Error ? error.message : String(error),
				}, 'Failed to store snapshot in ScyllaDB');
				errors.push(error);
			}),
		];

		await Promise.allSettled(storePromises);

		// If both stores failed, throw error
		if (errors.length === 2)
		{
			throw new Error(`Failed to store snapshot in both stores: ${errors.map(e => e.message).join(', ')}`);
		}

		// Log success with any partial failures
		if (errors.length === 1)
		{
			this.logger.warn({
				source,
				candidateCount: candidates.length,
				failedStores: errors.length,
			}, 'Snapshot stored with partial failure');
		}
		else
		{
			this.logger.info({
				source,
				candidateCount: candidates.length,
			}, 'Snapshot stored in both stores');
		}
	}

	/**
	 * Get recent snapshots from preferred store with fallback
	 */
	async getRecentSnapshots(source: string, days: number): Promise<DomainSnapshotType[]>
	{
		try
		{
			// Try preferred store first
			const primaryStore = this.preferRedis ? this.redisStore : this.scyllaStore;
			const primarySnapshots = await primaryStore.getRecentSnapshots(source, days);

			if (primarySnapshots.length > 0)
			{
				this.logger.debug({
					source,
					days,
					snapshotCount: primarySnapshots.length,
					primaryStore: this.preferRedis ? 'Redis' : 'ScyllaDB',
				}, 'Recent snapshots retrieved from primary store');
				return primarySnapshots;
			}

			// Try fallback store if enabled
			if (this.enableFallback)
			{
				const fallbackStore = this.preferRedis ? this.scyllaStore : this.redisStore;
				const fallbackSnapshots = await fallbackStore.getRecentSnapshots(source, days);

				if (fallbackSnapshots.length > 0)
				{
					this.logger.debug({
						source,
						days,
						snapshotCount: fallbackSnapshots.length,
						fallbackStore: this.preferRedis ? 'ScyllaDB' : 'Redis',
					}, 'Recent snapshots retrieved from fallback store');
				}

				return fallbackSnapshots;
			}

			return [];
		}
		catch (error)
		{
			this.logger.error({
				source,
				days,
				error: error instanceof Error ? error.message : String(error),
			}, 'Failed to get recent snapshots from composite store');
			return [];
		}
	}

	/**
	 * Clean up old snapshots from both stores
	 */
	async cleanup(retentionDays: number): Promise<void>
	{
		const errors: Error[] = [];

		// Clean up both stores concurrently
		const cleanupPromises = [
			this.redisStore.cleanup(retentionDays).catch((error) =>
			{
				this.logger.error({
					retentionDays,
					error: error instanceof Error ? error.message : String(error),
				}, 'Failed to cleanup Redis snapshots');
				errors.push(error);
			}),
			this.scyllaStore.cleanup(retentionDays).catch((error) =>
			{
				this.logger.error({
					retentionDays,
					error: error instanceof Error ? error.message : String(error),
				}, 'Failed to cleanup ScyllaDB snapshots');
				errors.push(error);
			}),
		];

		await Promise.allSettled(cleanupPromises);

		// Log results
		if (errors.length === 0)
		{
			this.logger.info({
				retentionDays,
			}, 'Cleanup completed successfully in both stores');
		}
		else if (errors.length === 1)
		{
			this.logger.warn({
				retentionDays,
				failedStores: errors.length,
			}, 'Cleanup completed with partial failure');
		}
		else
		{
			throw new Error(`Cleanup failed in both stores: ${errors.map(e => e.message).join(', ')}`);
		}
	}

	/**
	 * Get combined statistics from both stores
	 */
	async getStatistics(): Promise<CompositeSnapshotStatistics>
	{
		try
		{
			const [redisStats, scyllaStats] = await Promise.allSettled([
				this.redisStore.getStatistics(),
				this.scyllaStore.getStatistics(),
			]);

			const redis = redisStats.status === 'fulfilled' ? redisStats.value : null;
			const scylla = scyllaStats.status === 'fulfilled' ? scyllaStats.value : null;

			const errors = [
				...(redisStats.status === 'rejected' ? [redisStats.reason] : []),
				...(scyllaStats.status === 'rejected' ? [scyllaStats.reason] : []),
			];

			// If both stores failed, throw error
			if (!redis && !scylla)
			{
				throw new Error(`Failed to get statistics from both stores: ${errors.map(e => e.message).join(', ')}`);
			}

			// Combine statistics
			const totalSnapshots = (redis?.totalSnapshots || 0) + (scylla?.totalSnapshots || 0);
			const totalDomains = (redis?.totalDomains || 0) + (scylla?.totalDomains || 0);

			// Merge source statistics
			const sourceMap = new Map<string, CompositeSourceStatisticsType>();

			if (redis)
			{
				for (const source of redis.sources)
				{
					sourceMap.set(source.source, {
						...source,
						redisSnapshots: source.snapshotCount,
						redisDomains: source.totalDomains,
						scyllaSnapshots: 0,
						scyllaDomains: 0,
					});
				}
			}

			if (scylla)
			{
				for (const source of scylla.sources)
				{
					if (sourceMap.has(source.source))
					{
						const existing = sourceMap.get(source.source);
						if (existing) {
							existing.scyllaSnapshots = source.snapshotCount;
							existing.scyllaDomains = source.totalDomains;
							existing.snapshotCount += source.snapshotCount;
							existing.totalDomains += source.totalDomains;
						}
					}
					else
					{
						sourceMap.set(source.source, {
							...source,
							redisSnapshots: 0,
							redisDomains: 0,
							scyllaSnapshots: source.snapshotCount,
							scyllaDomains: source.totalDomains,
						});
					}
				}
			}

			return {
				totalSnapshots,
				totalDomains,
				sourceCount: sourceMap.size,
				sources: Array.from(sourceMap.values()),
				redis: redis || undefined,
				scylla: scylla || undefined,
				errors,
			};
		}
		catch (error)
		{
			this.logger.error({
				error: error instanceof Error ? error.message : String(error),
			}, 'Failed to get composite statistics');
			throw error;
		}
	}

	/**
	 * Synchronize data between Redis and ScyllaDB
	 * Useful for warming caches or ensuring consistency
	 */
	async synchronize(source?: string, days?: number): Promise<SynchronizationResult>
	{
		try
		{
			this.logger.info({ source, days }, 'Starting snapshot synchronization');

			const result: SynchronizationResult = {
				synchronized: 0,
				skipped: 0,
				errors: 0,
				sources: [],
			};

			// Get all sources if not specified
			let sourcesToSync: string[];
			if (source)
			{
				sourcesToSync = [source];
			}
			else
			{
				const stats = await this.getStatistics();
				sourcesToSync = stats.sources.map(s => s.source);
			}

			for (const sourceToSync of sourcesToSync)
			{
				try
				{
					const sourceResult = await this.synchronizeSource(sourceToSync, days || 30);
					result.synchronized += sourceResult.synchronized;
					result.skipped += sourceResult.skipped;
					result.errors += sourceResult.errors;
					result.sources.push(sourceResult);
				}
				catch (error)
				{
					this.logger.error({
						source: sourceToSync,
						error: error instanceof Error ? error.message : String(error),
					}, 'Failed to synchronize source');
					result.errors++;
				}
			}

			this.logger.info(result, 'Snapshot synchronization completed');
			return result;
		}
		catch (error)
		{
			this.logger.error({
				error: error instanceof Error ? error.message : String(error),
			}, 'Synchronization failed');
			throw error;
		}
	}

	/**
	 * Synchronize snapshots for a specific source
	 */
	private async synchronizeSource(source: string, days: number): Promise<SourceSynchronizationResult>
	{
		const result: SourceSynchronizationResult = {
			source,
			synchronized: 0,
			skipped: 0,
			errors: 0,
		};

		try
		{
			// Get snapshots from both stores
			const [redisSnapshots, scyllaSnapshots] = await Promise.all([
				this.redisStore.getRecentSnapshots(source, days),
				this.scyllaStore.getRecentSnapshots(source, days),
			]);

			// Create maps for easy lookup
			const redisMap = new Map(redisSnapshots.map(s => [s.date, s]));
			const scyllaMap = new Map(scyllaSnapshots.map(s => [s.date, s]));

			// Sync from ScyllaDB to Redis (persistent to cache)
			for (const [date, snapshot] of Array.from(scyllaMap))
			{
				if (!redisMap.has(date))
				{
					try
					{
						await this.redisStore.storeSnapshot(source, snapshot.domains);
						result.synchronized++;
					}
					catch (error)
					{
						this.logger.warn({
							source,
							date,
							error: error instanceof Error ? error.message : String(error),
						}, 'Failed to sync snapshot to Redis');
						result.errors++;
					}
				}
				else
				{
					result.skipped++;
				}
			}

			// Sync from Redis to ScyllaDB (cache to persistent)
			for (const [date, snapshot] of Array.from(redisMap))
			{
				if (!scyllaMap.has(date))
				{
					try
					{
						await this.scyllaStore.storeSnapshot(source, snapshot.domains);
						result.synchronized++;
					}
					catch (error)
					{
						this.logger.warn({
							source,
							date,
							error: error instanceof Error ? error.message : String(error),
						}, 'Failed to sync snapshot to ScyllaDB');
						result.errors++;
					}
				}
			}

			return result;
		}
		catch (error)
		{
			this.logger.error({
				source,
				error: error instanceof Error ? error.message : String(error),
			}, 'Failed to synchronize source snapshots');
			result.errors++;
			return result;
		}
	}
}

interface CompositeSnapshotStatistics extends SnapshotStatisticsType
{
	redis?: SnapshotStatisticsType;
	scylla?: SnapshotStatisticsType;
	errors: Error[];
}

interface SynchronizationResult
{
	synchronized: number;
	skipped: number;
	errors: number;
	sources: SourceSynchronizationResult[];
}

interface SourceSynchronizationResult
{
	source: string;
	synchronized: number;
	skipped: number;
	errors: number;
}

export type {
	CompositeSnapshotStatistics,
	SynchronizationResult,
	SourceSynchronizationResult,
};

export default CompositeSnapshotStore;
