import { RedisClientWrapper, logger as sharedLogger, type LoggerInstanceType } from '@shared';
import { createHash, randomBytes } from 'node:crypto';
import { gzip, gunzip } from 'node:zlib';
import { promisify } from 'node:util';

import type { SnapshotStoreType, DomainSnapshotType } from '../../interfaces/DiscoveryEngine';
import type { DomainCandidateType } from '../../interfaces/SourceConnector';

type SourceStatisticsType =
{
	source: string;
	snapshotCount: number;
	totalDomains: number;
	latestDate: string;
	oldestDate: string;
};

type SnapshotStatisticsType =
{
	totalSnapshots: number;
	totalDomains: number;
	sourceCount: number;
	sources: SourceStatisticsType[];
};

const gzipAsync = promisify(gzip);
const gunzipAsync = promisify(gunzip);

/**
 * Redis-based implementation of SnapshotStore
 * Stores domain snapshots for differential analysis with compression support
 */
class RedisSnapshotStore implements SnapshotStoreType
{
	private readonly redis: RedisClientWrapper;

	private readonly logger: LoggerInstanceType;

	private readonly keyPrefix: string;

	private readonly compressionEnabled: boolean;

	private readonly compressionThreshold: number;

	constructor(
		redis: RedisClientWrapper,
		options: {
			keyPrefix?: string;
			compressionEnabled?: boolean;
			compressionThreshold?: number;
		} = {},
	)
	{
		this.redis = redis;
		this.logger = sharedLogger.getLogger('RedisSnapshotStore');
		this.keyPrefix = options.keyPrefix || 'snapshot';
		this.compressionEnabled = options.compressionEnabled ?? true;
		this.compressionThreshold = options.compressionThreshold || 5000; // 5KB
	}

	/**
	 * Get a snapshot for a specific source and date
	 */
	async getSnapshot(source: string, date: string): Promise<DomainSnapshotType | null>
	{
		try
		{
			const key = this.buildSnapshotKey(source, date);
			const data = await this.redis.get(key);

			if (!data || typeof data !== 'string')
			{
				this.logger.debug({ source, date, key }, 'Snapshot not found');
				return null;
			}

			const snapshot = await this.deserializeSnapshot(data);

			// Verify data integrity if hash is present
			if ('hash' in snapshot && snapshot.hash)
			{
				const expectedHash = this.calculateHash(snapshot);
				if (snapshot.hash !== expectedHash)
				{
					this.logger.error({
						source,
						date,
						expectedHash: snapshot.hash,
						actualHash: expectedHash,
					}, 'Snapshot data integrity check failed');
					return null;
				}
			}

			this.logger.debug({
				source,
				date,
				domainCount: snapshot.domains.length,
				totalCount: snapshot.totalCount,
				compressed: 'compressed' in snapshot ? snapshot.compressed : false,
			}, 'Snapshot retrieved');

			return snapshot;
		}
		catch (error)
		{
			this.logger.error({
				source,
				date,
				error: error instanceof Error ? error.message : String(error),
			}, 'Failed to get snapshot');
			return null;
		}
	}

	/**
	 * Store a snapshot for a source
	 */
	async storeSnapshot(source: string, candidates: DomainCandidateType[]): Promise<void>
	{
		try
		{
			const date = new Date().toISOString().split('T')[0];
			const snapshot: DomainSnapshotType = {
				source,
				date,
				domains: candidates,
				totalCount: candidates.length,
			};

			const key = this.buildSnapshotKey(source, date);
			const { data: serializedData, compressed } = await this.serializeSnapshot(snapshot);
			const hash = this.calculateHash(snapshot);

			// Store snapshot data with compression flag and hash
			const snapshotWithMeta = {
				...snapshot,
				compressed,
				hash,
			};

			// Store with 30-day TTL
			const ttl = 30 * 24 * 60 * 60; // 30 days in seconds
			await this.redis.setex(key, ttl, JSON.stringify(snapshotWithMeta));

			// Update source metadata
			await this.updateSourceMetadata(source, date, candidates.length);

			this.logger.info({
				source,
				date,
				domainCount: candidates.length,
				key,
				dataSize: serializedData.length,
				compressed,
				hash,
			}, 'Snapshot stored');
		}
		catch (error)
		{
			this.logger.error({
				source,
				candidateCount: candidates.length,
				error: error instanceof Error ? error.message : String(error),
			}, 'Failed to store snapshot');
			throw error;
		}
	}

	/**
	 * Get recent snapshots for a source
	 */
	async getRecentSnapshots(source: string, days: number): Promise<DomainSnapshotType[]>
	{
		try
		{
			const snapshots: DomainSnapshotType[] = [];
			const today = new Date();

			// Get snapshots for the last N days
			for (let i = 0; i < days; i++)
			{
				const date = new Date(today);
				date.setDate(date.getDate() - i);
				const dateStr = date.toISOString().split('T')[0];

				const snapshot = await this.getSnapshot(source, dateStr);
				if (snapshot)
				{
					snapshots.push(snapshot);
				}
			}

			// Sort by date (newest first)
			snapshots.sort((a, b) => b.date.localeCompare(a.date));

			this.logger.debug({
				source,
				requestedDays: days,
				foundSnapshots: snapshots.length,
			}, 'Recent snapshots retrieved');

			return snapshots;
		}
		catch (error)
		{
			this.logger.error({
				source,
				days,
				error: error instanceof Error ? error.message : String(error),
			}, 'Failed to get recent snapshots');
			return [];
		}
	}

	/**
	 * Clean up old snapshots beyond retention period
	 */
	async cleanup(retentionDays: number): Promise<void>
	{
		try
		{
			this.logger.info({ retentionDays }, 'Starting snapshot cleanup');

			// Get all snapshot keys
			const pattern = `${this.keyPrefix}:*`;
			const keys = await this.redis.keys(pattern);

			let deletedCount = 0;
			const cutoffDate = new Date();
			cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

			for (const key of keys)
			{
				try
				{
					// Extract date from key
					const keyParts = key.split(':');
					if (keyParts.length >= 3)
					{
						const dateStr = keyParts[keyParts.length - 1];
						const keyDate = new Date(dateStr);

						if (keyDate < cutoffDate)
						{
							await this.redis.del(key);
							deletedCount++;
						}
					}
				}
				catch (error)
				{
					this.logger.warn({
						key,
						error: error instanceof Error ? error.message : String(error),
					}, 'Failed to process key during cleanup');
				}
			}

			this.logger.info({
				totalKeys: keys.length,
				deletedKeys: deletedCount,
				retentionDays,
			}, 'Snapshot cleanup completed');
		}
		catch (error)
		{
			this.logger.error({
				retentionDays,
				error: error instanceof Error ? error.message : String(error),
			}, 'Snapshot cleanup failed');
			throw error;
		}
	}

	/**
	 * Get snapshot statistics
	 */
	async getStatistics(): Promise<SnapshotStatisticsType>
	{
		try
		{
			const pattern = `${this.keyPrefix}:*`;
			const keys = await this.redis.keys(pattern);

			const sourceStats = new Map<string, SourceStatisticsType>();
			let totalSnapshots = 0;
			let totalDomains = 0;

			for (const key of keys)
			{
				try
				{
					const keyParts = key.split(':');
					if (keyParts.length >= 3)
					{
						const source = keyParts[keyParts.length - 2]; // keyPrefix:source:date format
						const data = await this.redis.get(key);

						if (data && typeof data === 'string')
						{
							const snapshot = await this.deserializeSnapshot(data);
							totalSnapshots++;
							totalDomains += snapshot.totalCount;

							if (!sourceStats.has(source))
							{
								sourceStats.set(source, {
									source,
									snapshotCount: 0,
									totalDomains: 0,
									latestDate: '',
									oldestDate: '',
								});
							}

							const stats = sourceStats.get(source)!;
							stats.snapshotCount++;
							stats.totalDomains += snapshot.totalCount;

							if (!stats.latestDate || snapshot.date > stats.latestDate)
							{
								stats.latestDate = snapshot.date;
							}

							if (!stats.oldestDate || snapshot.date < stats.oldestDate)
							{
								stats.oldestDate = snapshot.date;
							}
						}
					}
				}
				catch (error)
				{
					this.logger.warn({
						key,
						error: error instanceof Error ? error.message : String(error),
					}, 'Failed to process key for statistics');
				}
			}

			return {
				totalSnapshots,
				totalDomains,
				sourceCount: sourceStats.size,
				sources: Array.from(sourceStats.values()),
			};
		}
		catch (error)
		{
			this.logger.error({
				error: error instanceof Error ? error.message : String(error),
			}, 'Failed to get snapshot statistics');
			throw error;
		}
	}

	/**
	 * Build Redis key for snapshot
	 */
	private buildSnapshotKey(source: string, date: string): string
	{
		return `${this.keyPrefix}:${source}:${date}`;
	}

	/**
	 * Serialize snapshot data with optional compression
	 */
	private async serializeSnapshot(snapshot: DomainSnapshotType): Promise<{
		data: string;
		compressed: boolean;
	}>
	{
		try
		{
			const jsonData = JSON.stringify(snapshot);

			if (this.compressionEnabled && jsonData.length > this.compressionThreshold)
			{
				const compressedBuffer = await gzipAsync(Buffer.from(jsonData, 'utf8'));
				const compressedData = compressedBuffer.toString('base64');

				return {
					data: compressedData,
					compressed: true,
				};
			}

			return {
				data: jsonData,
				compressed: false,
			};
		}
		catch (error)
		{
			this.logger.error({
				source: snapshot.source,
				date: snapshot.date,
				error: error instanceof Error ? error.message : String(error),
			}, 'Failed to serialize snapshot');
			throw error;
		}
	}

	/**
	 * Deserialize snapshot data with decompression support
	 */
	private async deserializeSnapshot(data: string): Promise<DomainSnapshotType>
	{
		try
		{
			const parsed = JSON.parse(data);

			// Handle legacy format (no compression metadata)
			if (!('compressed' in parsed))
			{
				return parsed as DomainSnapshotType;
			}

			// Handle new format with compression metadata
			if (parsed.compressed)
			{
				const compressedBuffer = Buffer.from(parsed.data || data, 'base64');
				const decompressedBuffer = await gunzipAsync(compressedBuffer);
				const jsonData = decompressedBuffer.toString('utf8');
				return JSON.parse(jsonData);
			}

			// Uncompressed data
			return parsed as DomainSnapshotType;
		}
		catch (error)
		{
			this.logger.error({
				dataLength: data.length,
				error: error instanceof Error ? error.message : String(error),
			}, 'Failed to deserialize snapshot');
			throw error;
		}
	}

	/**
	 * Calculate SHA256 hash of snapshot data for integrity verification
	 */
	private calculateHash(snapshot: DomainSnapshotType): string
	{
		const data = JSON.stringify({
			source: snapshot.source,
			date: snapshot.date,
			domains: snapshot.domains,
			totalCount: snapshot.totalCount,
		});

		return createHash('sha256').update(data).digest('hex');
	}

	/**
	 * Update source metadata
	 */
	private async updateSourceMetadata(
		source: string,
		date: string,
		domainCount: number,
	): Promise<void>
	{
		try
		{
			const metadataKey = `${this.keyPrefix}:meta:${source}`;
			const metadata = {
				source,
				lastUpdate: date,
				lastDomainCount: domainCount,
				updatedAt: new Date().toISOString(),
			};

			await this.redis.setex(metadataKey, 86400 * 30, JSON.stringify(metadata)); // 30 days TTL
		}
		catch (error)
		{
			this.logger.warn({
				source,
				date,
				error: error instanceof Error ? error.message : String(error),
			}, 'Failed to update source metadata');
		}
	}
}

export type { SnapshotStatisticsType, SourceStatisticsType };

export default RedisSnapshotStore;
