// Core discovery engine
export { default as IntelligentDiscoveryEngine } from './IntelligentDiscoveryEngine';

// Factory for creating configured engines
export { DiscoveryEngineFactory, MockSnapshotStore } from './DiscoveryEngineFactory';

// Strategy processors
export { BaseStrategyProcessor } from './strategies/BaseStrategyProcessor';
export { default as DifferentialAnalysisProcessor } from './strategies/DifferentialAnalysisProcessor';

// Snapshot stores
export { default as RedisSnapshotStore } from './stores/RedisSnapshotStore';

// Types
export type { ConfidenceFactors } from './strategies/BaseStrategyProcessor';
export type {
	SnapshotStatisticsType,
	SourceStatisticsType,
} from './stores/RedisSnapshotStore';
export type {
	DiscoveryEngineOptions,
	SnapshotStoreOptions,
	StrategyOptions,
} from './DiscoveryEngineFactory';
