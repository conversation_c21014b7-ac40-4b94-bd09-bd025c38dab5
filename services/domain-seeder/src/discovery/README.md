# Discovery Strategy Framework

This directory contains the implementation of the intelligent discovery strategy framework for the domain-seeder service.

## Overview

The discovery strategy framework provides a pluggable architecture for implementing different domain discovery strategies. It allows the system to identify new domains using various approaches beyond simple top-list fetching.

## Core Components

### IntelligentDiscoveryEngine

- **File**: `IntelligentDiscoveryEngine.ts`
- **Purpose**: Main orchestrator for discovery strategies
- **Features**:
  - Strategy registration and management
  - Multi-strategy processing with result aggregation
  - Comprehensive metrics collection
  - Confidence-based result deduplication
  - Error handling and graceful degradation

### Strategy Processors

#### BaseStrategyProcessor

- **File**: `strategies/BaseStrategyProcessor.ts`
- **Purpose**: Abstract base class for all strategy processors
- **Features**:
  - Common validation logic
  - Confidence calculation framework
  - Error handling patterns
  - Logging and metrics support

#### DifferentialAnalysisProcessor

- **File**: `strategies/DifferentialAnalysisProcessor.ts`
- **Purpose**: Identifies domains that newly appear in ranking lists
- **Method**: Compares current snapshots with historical snapshots
- **Features**:
  - Multi-source processing
  - Case-insensitive domain comparison
  - Source reliability consideration
  - Metadata-based confidence boosting

### Snapshot Storage

#### RedisSnapshotStore

- **File**: `stores/RedisSnapshotStore.ts`
- **Purpose**: Redis-based storage for historical domain snapshots
- **Features**:
  - Efficient snapshot storage with TTL
  - Recent snapshot retrieval
  - Automatic cleanup of old snapshots
  - Comprehensive statistics
  - Data serialization/deserialization

### Factory and Configuration

#### DiscoveryEngineFactory

- **File**: `DiscoveryEngineFactory.ts`
- **Purpose**: Factory for creating configured discovery engines
- **Features**:
  - Automatic strategy registration
  - Configuration management
  - Test engine creation
  - Error handling during setup

## Strategy Types

The framework supports four main discovery strategies:

1. **Differential Analysis** (`differential`) - ✅ Implemented

   - Identifies new entries in ranking lists
   - High confidence for newly appearing domains

2. **Zone File Processing** (`zone-new`) - 🚧 Planned

   - Processes newly registered domains from CZDS zone files
   - Very high confidence for brand-new registrations

3. **Long-tail Exploration** (`long-tail`) - 🚧 Planned

   - Discovers domains beyond typical top-1M lists
   - Medium confidence for long-tail domains

4. **Temporal Analysis** (`temporal`) - 🚧 Planned
   - Identifies rapidly rising domains
   - High confidence for domains with significant upward movement

## Usage Example

```typescript
import { DiscoveryEngineFactory } from "./discovery";
import { RedisClient } from "@shared";

// Create discovery engine
const factory = new DiscoveryEngineFactory();
const redis = new RedisClient();
const engine = await factory.createDiscoveryEngine(redis);

// Process candidates with a specific strategy
const candidates = [{ domain: "example.com", source: "tranco", rank: 1000 }];

const discovered = await engine.processWithStrategy("differential", candidates);

// Process with multiple strategies
const multiDiscovered = await engine.processWithMultipleStrategies(
  ["differential", "temporal"],
  candidates
);

// Get metrics
const metrics = engine.getMetrics();
console.log(`Discovered ${metrics.discoveredDomains} domains`);
```

## Testing

The framework includes comprehensive unit tests:

- **IntelligentDiscoveryEngine.test.ts**: 18 tests covering engine functionality
- **DifferentialAnalysisProcessor.test.ts**: 13 tests covering strategy processing
- **RedisSnapshotStore.test.ts**: 16 tests covering snapshot storage
- **DiscoveryEngineFactory.test.ts**: 16 tests covering factory functionality

Total: **63 tests** with 100% pass rate

## Configuration

The framework supports various configuration options:

```typescript
const options = {
  snapshotStore: {
    keyPrefix: "discovery:snapshot",
    compressionEnabled: true,
  },
  strategies: {
    enabledStrategies: ["differential", "temporal"],
    failOnStrategyError: false,
  },
};

const engine = await factory.createDiscoveryEngine(redis, options);
```

## Metrics and Monitoring

The framework provides comprehensive metrics:

- Total candidates processed
- Total domains discovered
- Strategy-specific breakdown
- Confidence distribution (high/medium/low)
- Processing performance metrics

## Error Handling

The framework implements robust error handling:

- Graceful strategy failures
- Snapshot store error recovery
- Invalid candidate filtering
- Comprehensive logging

## Future Enhancements

1. Implement remaining strategy processors (zone-new, long-tail, temporal)
2. Add compression support for large snapshots
3. Implement strategy effectiveness tracking
4. Add adaptive strategy selection
5. Performance optimizations for large datasets

## Requirements Satisfied

This implementation satisfies the following requirements from the specification:

- **1.1**: Multi-strategy domain discovery with pluggable processors
- **6.1**: Strategy confidence scoring and discovery reason tracking
- **6.2**: Result aggregation and coordination between strategies

The framework provides a solid foundation for implementing the remaining discovery strategies and supports the overall goal of intelligent domain discovery beyond simple top-list processing.
