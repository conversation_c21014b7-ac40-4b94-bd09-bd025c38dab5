import { RedisClientWrapper, logger } from '@shared';
import type {
	DiscoveryEngineType,
	SnapshotStoreType,
	DomainSnapshotType,
	DiscoveryStrategyType
} from '../interfaces/DiscoveryEngine';
import type { DomainCandidateType } from '../interfaces/SourceConnector';
import { IntelligentDiscoveryEngine } from './IntelligentDiscoveryEngine';
import RedisSnapshotStore from './stores/RedisSnapshotStore';
import DifferentialAnalysisProcessor from './strategies/DifferentialAnalysisProcessor';
import ZoneFileProcessor from './strategies/ZoneFileProcessor';
import LongTailProcessor from './strategies/LongTailProcessor';
import TemporalAnalysisProcessor from './strategies/TemporalAnalysisProcessor';

/**
 * Factory class for creating and configuring discovery engines
 * Handles strategy registration and dependency injection
 */
class DiscoveryEngineFactory
{
	private readonly logger: any;

	constructor()
	{
		this.logger = logger.getLogger('DiscoveryEngineFactory');
	}

	/**
	 * Create a fully configured discovery engine with all strategies
	 */
	async createDiscoveryEngine(
		redis: RedisClientWrapper,
		options: DiscoveryEngineOptions = {},
	): Promise<DiscoveryEngineType>
	{
		try
		{
			this.logger.info('Creating discovery engine', { options });

			// Create snapshot store
			const snapshotStore = this.createSnapshotStore(redis, options.snapshotStore);

			// Create discovery engine
			const engine = new IntelligentDiscoveryEngine(snapshotStore);

			// Register strategies
			await this.registerStrategies(engine, options.strategies);

			this.logger.info('Discovery engine created successfully', {
				registeredStrategies: engine.getRegisteredStrategies(),
			});

			return engine;
		}
		catch (error)
		{
			this.logger.error('Failed to create discovery engine', {
				error: (error as Error).message,
			});
			throw error;
		}
	}

	/**
	 * Create snapshot store
	 */
	private createSnapshotStore(
		redis: RedisClientWrapper,
		options: SnapshotStoreOptions = {},
	): SnapshotStoreType
	{
		return new RedisSnapshotStore(redis, {
			keyPrefix: options.keyPrefix || 'discovery:snapshot',
			compressionEnabled: options.compressionEnabled ?? true,
		});
	}

	/**
	 * Register all available strategies
	 */
	private async registerStrategies(
		engine: IntelligentDiscoveryEngine,
		options: StrategyOptions = {},
	): Promise<void>
	{
		const strategies = options.enabledStrategies || [
			'differential',
			'zone-new',
			'long-tail',
			'temporal',
		];

		for (const strategy of strategies)
		{
			try
			{
				const strategyType = strategy as DiscoveryStrategyType;
				const processor = await this.createStrategyProcessor(strategyType, options);
				if (processor)
				{
					engine.registerStrategy(strategyType, processor);
					this.logger.debug('Strategy registered', { strategy: strategyType });
				}
			}
			catch (error)
			{
				this.logger.error('Failed to register strategy', {
					strategy,
					error: (error as Error).message,
				});

				if (options.failOnStrategyError)
				{
					throw error;
				}
			}
		}
	}

	/**
	 * Create strategy processor based on strategy type
	 */
	private async createStrategyProcessor(
		strategy: string,
		options: StrategyOptions,
	): Promise<any>
	{
		switch (strategy)
		{
			case 'differential':
				return new DifferentialAnalysisProcessor();

			case 'zone-new':
				return new ZoneFileProcessor();

			case 'long-tail':
				return new LongTailProcessor();

			case 'temporal':
				return new TemporalAnalysisProcessor();

			default:
				this.logger.warn('Unknown strategy type', { strategy });
				return null;
		}
	}

	/**
	 * Create a minimal discovery engine for testing
	 */
	async createTestDiscoveryEngine(
		mockSnapshotStore?: SnapshotStoreType,
	): Promise<DiscoveryEngineType>
	{
		const snapshotStore = mockSnapshotStore || new MockSnapshotStore();
		const engine = new IntelligentDiscoveryEngine(snapshotStore);

		// Register only differential strategy for testing
		engine.registerStrategy('differential', new DifferentialAnalysisProcessor());

		return engine;
	}
}

/**
 * Mock snapshot store for testing
 */
class MockSnapshotStore implements SnapshotStoreType
{
	private snapshots = new Map<string, unknown>();

	async getSnapshot(source: string, date: string): Promise<DomainSnapshotType | null>
	{
		const snapshot = this.snapshots.get(`${source}:${date}`);
		return snapshot ? (snapshot as DomainSnapshotType) : null;
	}

	async storeSnapshot(source: string, candidates: DomainCandidateType[]): Promise<void>
	{
		const date = new Date().toISOString().split('T')[0];
		this.snapshots.set(`${source}:${date}`, {
			source,
			date,
			domains: candidates,
			totalCount: candidates.length,
		});
	}

	async getRecentSnapshots(source: string, days: number): Promise<DomainSnapshotType[]>
	{
		const snapshots: DomainSnapshotType[] = [];
		const today = new Date();

		for (let i = 0; i < days; i++)
		{
			const date = new Date(today);
			date.setDate(date.getDate() - i);
			const dateStr = date.toISOString().split('T')[0];

			const snapshot = await this.getSnapshot(source, dateStr);
			if (snapshot)
			{
				snapshots.push(snapshot);
			}
		}

		return snapshots;
	}

	async cleanup(retentionDays: number): Promise<void>
	{
		// Simple cleanup implementation for mock store
		const cutoffDate = new Date();
		cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

		const snapshotEntries = Array.from(this.snapshots.entries());
		for (const [key, snapshot] of snapshotEntries)
		{
			const snapshotDate = new Date((snapshot as DomainSnapshotType).date);
			if (snapshotDate < cutoffDate)
			{
				this.snapshots.delete(key);
			}
		}
	}

	// Commented out cleanup method - belongs to MockSnapshotStore, not DiscoveryEngineFactory
	// async cleanup(retentionDays: number): Promise<void>
	// {
	// 	this.logger.info('Starting discovery engine cleanup', { retentionDays });

	// 	try
	// 	{
	// 		// Calculate cutoff date
	// 		const cutoffDate = new Date();
	// 		cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

	// 		// Cleanup snapshot stores
	// 		if (this.snapshotStore)
	// 		{
	// 			await this.snapshotStore.cleanup(cutoffDate);
	// 		}

	// 		// Cleanup strategy-specific data
	// 		for (const [strategyName, strategy] of this.strategies)
	// 		{
	// 			if ('cleanup' in strategy && typeof strategy.cleanup === 'function')
	// 			{
	// 				try
	// 				{
	// 					await strategy.cleanup(cutoffDate);
	// 					this.logger.debug('Strategy cleanup completed', { strategy: strategyName });
	// 				}
	// 				catch (error)
	// 				{
	// 					this.logger.error('Strategy cleanup failed', {
	// 						strategy: strategyName,
	// 						error: (error as Error).message,
	// 					});
	// 				}
	// 			}
	// 		}

	// 		// Cleanup cached engines
	// 		const now = Date.now();
	// 		for (const [key, cachedEngine] of this.engineCache)
	// 		{
	// 			if (now - cachedEngine.createdAt > 24 * 60 * 60 * 1000) // 24 hours
	// 			{
	// 				this.engineCache.delete(key);
	// 			}
	// 		}

	// 		this.logger.info('Discovery engine cleanup completed', {
	// 			retentionDays,
	// 			cutoffDate: cutoffDate.toISOString(),
	// 			remainingCachedEngines: this.engineCache.size,
	// 		});
	// 	}
	// 	catch (error)
	// 	{
	// 		this.logger.error('Discovery engine cleanup failed', {
	// 			retentionDays,
	// 			error: (error as Error).message,
	// 		});
	// 		throw error;
	// 	}
	// }
}

interface DiscoveryEngineOptions
{
	snapshotStore?: SnapshotStoreOptions;
	strategies?: StrategyOptions;
}

interface SnapshotStoreOptions
{
	keyPrefix?: string;
	compressionEnabled?: boolean;
}

interface StrategyOptions
{
	enabledStrategies?: string[];
	failOnStrategyError?: boolean;
}

export type {
	DiscoveryEngineOptions,
	SnapshotStoreOptions,
	StrategyOptions,
};

export { DiscoveryEngineFactory, MockSnapshotStore };
