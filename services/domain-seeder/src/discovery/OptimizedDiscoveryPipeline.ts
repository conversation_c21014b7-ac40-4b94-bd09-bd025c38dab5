import { logger as sharedLogger, type LoggerInstanceType } from '@shared';
import type OptimizedCompositeDomainRepository from '../repositories/OptimizedCompositeDomainRepository';
import type RedisOperationalLayer from '../repositories/RedisOperationalLayer';
import type IntelligentDiscoveryEngine from './IntelligentDiscoveryEngine';
import type PSLDomainNormalizer from '../normalization/PSLDomainNormalizer';
import type RateLimitedDomainEnqueuer from '../enqueuer/RateLimitedDomainEnqueuer';
import type { DiscoveredDomainInterface } from '../interfaces/DiscoveryEngine';
import type IntegratedContentGenerator from '../content/IntegratedContentGenerator';


/**
 * Configuration for optimized discovery pipeline
 */
type OptimizedDiscoveryConfig =
{
	/**
	 * Maximum batch size for domain processing
	 */
	maxBatchSize: number;

	/**
	 * Optimal batch size for best performance
	 */
	optimalBatchSize: number;

	/**
	 * Enable backpressure handling
	 */
	enableBackpressure: boolean;

	/**
	 * Maximum queue depth before applying backpressure
	 */
	maxQueueDepth: number;

	/**
	 * Batch processing timeout in milliseconds
	 */
	batchTimeout: number;

	/**
	 * Maximum concurrent batch operations
	 */
	maxConcurrentBatches: number;

	/**
	 * Enable performance monitoring
	 */
	enableMetrics: boolean;

	/**
	 * Retry configuration for failed batches
	 */
	retryConfig:
	{
		maxRetries: number;
		backoffMs: number;
		exponentialBackoff: boolean;
	};
};

/**
 * Default configuration for optimized discovery pipeline
 */
const DEFAULT_OPTIMIZED_DISCOVERY_CONFIG: OptimizedDiscoveryConfig =
{
	maxBatchSize: 100000, // 100K domains per batch
	optimalBatchSize: 50000, // 50K domains for optimal performance
	enableBackpressure: true,
	maxQueueDepth: 200000, // 200K domains max queue depth
	batchTimeout: 30000, // 30 seconds
	maxConcurrentBatches: 3,
	enableMetrics: true,
	retryConfig:
	{
		maxRetries: 3,
		backoffMs: 1000,
		exponentialBackoff: true,
	},
};

/**
 * Batch processing result
 */
type BatchProcessingResult =
{
	/**
	 * Successfully processed domains
	 */
	processedDomains: string[];

	/**
	 * New domains discovered (not in database)
	 */
	newDomains: string[];

	/**
	 * Domains that failed processing
	 */
	failedDomains: Map<string, Error>;

	/**
	 * Performance metrics
	 */
	metrics:
	{
		totalDomains: number;
		processedCount: number;
		newDomainsCount: number;
		failedCount: number;
		executionTime: number;
		normalizationTime: number;
		existenceCheckTime: number;
		enqueueTime: number;
		batchCount: number;
		averageBatchSize: number;
		backpressureEvents: number;
	};
};

/**
 * Pipeline stage for batch processing
 */
type PipelineStage<T, R> =
{
	name: string;
	process: (input: T) => Promise<R>;
	batchSize?: number;
	timeout?: number;
};

/**
 * Optimized discovery pipeline that uses batch processing for high-throughput domain processing
 * Implements proper backpressure handling and performance monitoring
 */
class OptimizedDiscoveryPipeline
{
	private readonly logger: LoggerInstanceType = sharedLogger.getLogger('OptimizedDiscoveryPipeline');

	private readonly config: OptimizedDiscoveryConfig;

	// Performance metrics
	private totalBatches = 0;

	private totalDomains = 0;

	private totalNewDomains = 0;

	private totalErrors = 0;

	private totalBackpressureEvents = 0;

	private activeBatches = 0;

	// Backpressure control
	private readonly batchQueue: Array<{
		domains: string[];
		resolve: (result: BatchProcessingResult) => void;
		reject: (error: Error) => void;
	}> = [];

	private processingPaused = false;

	constructor(
		private readonly discoveryEngine: IntelligentDiscoveryEngine,
		private readonly domainRepository: OptimizedCompositeDomainRepository,
		private readonly domainNormalizer: PSLDomainNormalizer,
		private readonly domainEnqueuer: RateLimitedDomainEnqueuer,
		private readonly redisOperational: RedisOperationalLayer,
		private readonly contentGenerator?: IntegratedContentGenerator,
		config: Partial<OptimizedDiscoveryConfig> = {},
	)
	{
		this.config = { ...DEFAULT_OPTIMIZED_DISCOVERY_CONFIG, ...config };

		this.logger.info({
			config: this.config,
		}, 'OptimizedDiscoveryPipeline initialized');

		// Start batch processing loop
		this.startBatchProcessingLoop();
	}

	/**
	 * Process discovered domains through optimized batch pipeline
	 */
	async processDiscoveredDomains(discoveredDomains: DiscoveredDomainInterface[]): Promise<BatchProcessingResult>
	{
		if (discoveredDomains.length === 0)
		{
			return this.createEmptyResult();
		}

		const startTime = Date.now();
		const domains = discoveredDomains.map(d => d.domain);

		this.totalBatches++;
		this.totalDomains += domains.length;

		try
		{
			// Check backpressure
			if (this.config.enableBackpressure && this.shouldApplyBackpressure())
			{
				this.totalBackpressureEvents++;
				this.logger.warn({
					batchSize: domains.length,
					queueDepth: this.batchQueue.length,
					activeBatches: this.activeBatches,
				}, 'Backpressure detected, queuing batch for later processing');

				return await this.queueBatchForProcessing(domains);
			}

			// Process batch immediately
			return await this.processBatchInternal(domains, startTime);
		}
		catch (error)
		{
			this.totalErrors++;
			this.logger.error({
				batchSize: domains.length,
				error: (error as Error).message,
			}, 'Batch processing failed');

			return {
				processedDomains: [],
				newDomains: [],
				failedDomains: new Map(domains.map(d => [d, error as Error])),
				metrics: {
					totalDomains: domains.length,
					processedCount: 0,
					newDomainsCount: 0,
					failedCount: domains.length,
					executionTime: Date.now() - startTime,
					normalizationTime: 0,
					existenceCheckTime: 0,
					enqueueTime: 0,
					batchCount: 0,
					averageBatchSize: 0,
					backpressureEvents: 0,
				},
			};
		}
	}

	/**
	 * Process multiple batches concurrently with controlled concurrency
	 */
	async processConcurrentBatches(
		batches: DiscoveredDomainInterface[][],
	): Promise<BatchProcessingResult[]>
	{
		const maxConcurrency = this.config.maxConcurrentBatches;
		const results: BatchProcessingResult[] = [];

		// Process batches in chunks to control concurrency
		for (let i = 0; i < batches.length; i += maxConcurrency)
		{
			const batchChunk = batches.slice(i, i + maxConcurrency);

			const chunkPromises = batchChunk.map(batch => this.processDiscoveredDomains(batch));

			const chunkResults = await Promise.allSettled(chunkPromises);

			chunkResults.forEach((result) =>
			{
				if (result.status === 'fulfilled')
				{
					results.push(result.value);
				}
				else
				{
					this.logger.error({
						error: result.reason,
					}, 'Concurrent batch processing failed');

					// Create error result
					results.push({
						processedDomains: [],
						newDomains: [],
						failedDomains: new Map(),
						metrics: {
							totalDomains: 0,
							processedCount: 0,
							newDomainsCount: 0,
							failedCount: 0,
							executionTime: 0,
							normalizationTime: 0,
							existenceCheckTime: 0,
							enqueueTime: 0,
							batchCount: 0,
							averageBatchSize: 0,
							backpressureEvents: 0,
						},
					});
				}
			});
		}

		return results;
	}

	/**
	 * Get pipeline performance metrics
	 */
	getMetrics(): {
		totalBatches: number;
		totalDomains: number;
		totalNewDomains: number;
		totalErrors: number;
		totalBackpressureEvents: number;
		activeBatches: number;
		queueDepth: number;
		errorRate: number;
		newDomainRate: number;
		averageDomainsPerBatch: number;
		backpressureRate: number;
		isBackpressureActive: boolean;
	}
	{
		return {
			totalBatches: this.totalBatches,
			totalDomains: this.totalDomains,
			totalNewDomains: this.totalNewDomains,
			totalErrors: this.totalErrors,
			totalBackpressureEvents: this.totalBackpressureEvents,
			activeBatches: this.activeBatches,
			queueDepth: this.batchQueue.length,
			errorRate: this.totalBatches > 0 ? this.totalErrors / this.totalBatches : 0,
			newDomainRate: this.totalDomains > 0 ? this.totalNewDomains / this.totalDomains : 0,
			averageDomainsPerBatch: this.totalBatches > 0 ? this.totalDomains / this.totalBatches : 0,
			backpressureRate: this.totalBatches > 0 ? this.totalBackpressureEvents / this.totalBatches : 0,
			isBackpressureActive: this.processingPaused,
		};
	}

	/**
	 * Health check for pipeline components
	 */
	async healthCheck(): Promise<{
		overall: boolean;
		discoveryEngine: boolean;
		domainRepository: boolean;
		domainEnqueuer: boolean;
		redisOperational: boolean;
		backpressureStatus: string;
		queueHealth: string;
	}>
	{
		try
		{
			const [repoHealth, redisHealth] = await Promise.allSettled([
				this.domainRepository.healthCheck(),
				this.redisOperational.healthCheck(),
			]);

			const results = {
				discoveryEngine: true, // Assume healthy if no errors
				domainRepository: repoHealth.status === 'fulfilled' ? repoHealth.value : false,
				domainEnqueuer: true, // Assume healthy - no health check method available
				redisOperational: redisHealth.status === 'fulfilled' ? redisHealth.value.isHealthy : false,
			};

			const overall = results.discoveryEngine && results.domainRepository && results.redisOperational;

			const backpressureStatus = this.processingPaused ? 'active'
				: this.shouldApplyBackpressure() ? 'warning' : 'normal';

			const queueHealth = this.batchQueue.length === 0 ? 'empty'
				: this.batchQueue.length < this.config.maxQueueDepth * 0.5 ? 'normal'
					: this.batchQueue.length < this.config.maxQueueDepth * 0.8 ? 'high' : 'critical';

			return {
				overall,
				...results,
				backpressureStatus,
				queueHealth,
			};
		}
		catch (error)
		{
			this.logger.error({
				error: (error as Error).message,
			}, 'Pipeline health check failed');

			return {
				overall: false,
				discoveryEngine: false,
				domainRepository: false,
				domainEnqueuer: false,
				redisOperational: false,
				backpressureStatus: 'error',
				queueHealth: 'error',
			};
		}
	}

	/**
	 * Get current configuration
	 */
	getConfig(): OptimizedDiscoveryConfig
	{
		return { ...this.config };
	}

	/**
	 * Pause pipeline processing (for maintenance)
	 */
	pauseProcessing(): void
	{
		this.processingPaused = true;
		this.logger.info({}, 'Pipeline processing paused');
	}

	/**
	 * Resume pipeline processing
	 */
	resumeProcessing(): void
	{
		this.processingPaused = false;
		this.logger.info({}, 'Pipeline processing resumed');
	}

	/**
	 * Clear pipeline queue and reset metrics
	 */
	clearQueue(): void
	{
		const queuedBatches = this.batchQueue.length;
		this.batchQueue.length = 0;

		// Reset metrics
		this.totalBatches = 0;
		this.totalDomains = 0;
		this.totalNewDomains = 0;
		this.totalErrors = 0;
		this.totalBackpressureEvents = 0;

		this.logger.info({
			clearedBatches: queuedBatches,
		}, 'Pipeline queue cleared and metrics reset');
	}

	/**
	 * Internal batch processing implementation
	 */
	private async processBatchInternal(
		domains: string[],
		startTime: number,
	): Promise<BatchProcessingResult>
	{
		this.activeBatches++;

		try
		{
			const processedDomains: string[] = [];
			const failedDomains = new Map<string, Error>();
			let normalizationTime = 0;
			let existenceCheckTime = 0;
			let enqueueTime = 0;

			// Split into optimal batch sizes
			const batches = this.splitIntoBatches(domains, this.config.optimalBatchSize);

			for (const batch of batches)
			{
				try
				{
					// Stage 1: Normalize domains
					const normalizationStart = Date.now();
					const normalizationResult = this.normalizeBatch(batch);
					normalizationTime += Date.now() - normalizationStart;

					// Add normalization failures to failed domains
					normalizationResult.failed.forEach((error, domain) =>
					{
						failedDomains.set(domain, error);
					});

					if (normalizationResult.normalized.length === 0)
					{
						// All domains in this batch failed normalization
						continue;
					}

					// Stage 2: Check existence in batch
					const existenceStart = Date.now();
					const existenceResults = await this.domainRepository.batchHasDomains(normalizationResult.normalized);
					existenceCheckTime += Date.now() - existenceStart;

					// Stage 3: Identify new domains
					const newDomains = normalizationResult.normalized.filter(domain => !existenceResults.get(domain));

					// Stage 4: Enqueue new domains (content generation happens inside enqueuer)
					if (newDomains.length > 0)
					{
						const enqueueStart = Date.now();

						// Convert normalized domains back to DiscoveredDomain format for enqueuer
						const discoveredDomainsForEnqueue = newDomains.map((domain) =>
						{
							// Find the original discovered domain that matches this normalized domain
							const originalDomain = batch.find((originalDomain) =>
							{
								try
								{
									const normalized = this.domainNormalizer.normalize(originalDomain);
									return normalized && normalized.isValid && normalized.etld1 === domain;
								}
								catch
								{
									return false;
								}
							});

							// Return a DiscoveredDomain structure
							return {
								domain,
								source: originalDomain || 'unknown',
								discoveryStrategy: 'batch-processing' as any,
								confidence: 0.7,
								discoveryReason: 'Discovered through optimized pipeline batch processing',
								rank: undefined,
								metadata: {},
							};
						});

						await this.domainEnqueuer.enqueueDomains(discoveredDomainsForEnqueue);
						enqueueTime += Date.now() - enqueueStart;

						this.totalNewDomains += newDomains.length;
					}

					processedDomains.push(...normalizationResult.normalized);
				}
				catch (error)
				{
					// Mark all domains in failed batch as errors
					batch.forEach((domain) =>
					{
						failedDomains.set(domain, error as Error);
					});

					this.logger.warn({
						batchSize: batch.length,
						error: (error as Error).message,
					}, 'Batch processing stage failed');
				}
			}

			const newDomainsCount = processedDomains.length - Array.from(failedDomains.keys()).length;

			return {
				processedDomains,
				newDomains: processedDomains.filter(domain => !failedDomains.has(domain)),
				failedDomains,
				metrics: {
					totalDomains: domains.length,
					processedCount: processedDomains.length,
					newDomainsCount,
					failedCount: failedDomains.size,
					executionTime: Date.now() - startTime,
					normalizationTime,
					existenceCheckTime,
					enqueueTime,
					batchCount: batches.length,
					averageBatchSize: domains.length / batches.length,
					backpressureEvents: 0,
				},
			};
		}
		finally
		{
			this.activeBatches--;
		}
	}

	/**
	 * Normalize a batch of domains
	 */
	private normalizeBatch(domains: string[]): { normalized: string[]; failed: Map<string, Error> }
	{
		const normalized: string[] = [];
		const failed = new Map<string, Error>();

		for (const domain of domains)
		{
			try
			{
				const normalizedDomain = this.domainNormalizer.normalize(domain);
				if (normalizedDomain && normalizedDomain.isValid)
				{
					normalized.push(normalizedDomain.etld1);
				}
				else
				{
					failed.set(domain, new Error('Domain normalization failed - invalid domain'));
				}
			}
			catch (error)
			{
				failed.set(domain, error as Error);
				this.logger.debug({
					domain,
					error: (error as Error).message,
				}, 'Domain normalization failed');
			}
		}

		return { normalized, failed };
	}

	/**
	 * Split domains into optimal batch sizes
	 */
	private splitIntoBatches(domains: string[], batchSize: number): string[][]
	{
		const batches: string[][] = [];

		for (let i = 0; i < domains.length; i += batchSize)
		{
			batches.push(domains.slice(i, i + batchSize));
		}

		return batches;
	}

	/**
	 * Check if backpressure should be applied
	 */
	private shouldApplyBackpressure(): boolean
	{
		return (
			this.activeBatches >= this.config.maxConcurrentBatches ||
			this.batchQueue.length >= this.config.maxQueueDepth ||
			this.processingPaused
		);
	}

	/**
	 * Queue batch for later processing when backpressure is active
	 */
	private queueBatchForProcessing(domains: string[]): Promise<BatchProcessingResult>
	{
		return new Promise((resolve, reject) =>
		{
			this.batchQueue.push({
				domains,
				resolve,
				reject,
			});
		});
	}

	/**
	 * Start background batch processing loop
	 */
	private startBatchProcessingLoop(): void
	{
		setInterval(async () =>
		{
			if (this.processingPaused || this.batchQueue.length === 0)
			{
				return;
			}

			// Process queued batches when capacity is available
			while (
				this.batchQueue.length > 0 &&
				this.activeBatches < this.config.maxConcurrentBatches
			)
			{
				const queuedBatch = this.batchQueue.shift();
				if (!queuedBatch)
				{
					break;
				}

				try
				{
					const result = await this.processBatchInternal(
						queuedBatch.domains,
						Date.now(),
					);
					queuedBatch.resolve(result);
				}
				catch (error)
				{
					queuedBatch.reject(error as Error);
				}
			}
		}, 1000); // Check every second

		this.logger.info({}, 'Batch processing loop started');
	}

	/**
	 * Create empty result for edge cases
	 */
	private createEmptyResult(): BatchProcessingResult
	{
		return {
			processedDomains: [],
			newDomains: [],
			failedDomains: new Map(),
			metrics: {
				totalDomains: 0,
				processedCount: 0,
				newDomainsCount: 0,
				failedCount: 0,
				executionTime: 0,
				normalizationTime: 0,
				existenceCheckTime: 0,
				enqueueTime: 0,
				batchCount: 0,
				averageBatchSize: 0,
				backpressureEvents: 0,
			},
		};
	}
}

export type {
	OptimizedDiscoveryConfig,
	BatchProcessingResult,
	PipelineStage,
};

export {
	DEFAULT_OPTIMIZED_DISCOVERY_CONFIG,
	OptimizedDiscoveryPipeline,
};

export default OptimizedDiscoveryPipeline;
