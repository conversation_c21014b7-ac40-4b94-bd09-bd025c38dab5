import { logger, type LoggerInstanceType } from '@shared';
import type {
	DiscoveryEngineType,
	DiscoveryStrategyType,
	DiscoveredDomainInterface,
	DomainSnapshotType,
	StrategyProcessorType,
	SnapshotStoreType,
	DiscoveryMetricsType,
} from '../interfaces/DiscoveryEngine';
import type { DomainCandidateType } from '../interfaces/SourceConnector';

class IntelligentDiscoveryEngine implements DiscoveryEngineType
{
	private readonly snapshotStore: SnapshotStoreType;

	private readonly strategies: Map<DiscoveryStrategyType, StrategyProcessorType>;

	private readonly logger: LoggerInstanceType;

	private readonly metrics: Map<string, number>;

	constructor(snapshotStore: SnapshotStoreType)
	{
		this.snapshotStore = snapshotStore;
		this.strategies = new Map();
		this.logger = logger.getLogger('IntelligentDiscoveryEngine');
		this.metrics = new Map();

		this.initializeMetrics();
	}

	/**
	 * Register a strategy processor for a specific discovery strategy
	 */
	registerStrategy(strategy: DiscoveryStrategyType, processor: StrategyProcessorType): void
	{
		this.strategies.set(strategy, processor);
		this.logger.info({ strategy, msg: 'Strategy processor registered' });
	}

	/**
	 * Process candidates using a specific discovery strategy
	 */
	async processWithStrategy(
		strategy: DiscoveryStrategyType,
		candidates: DomainCandidateType[],
	): Promise<DiscoveredDomainInterface[]>
	{
		const startTime = Date.now();

		try
		{
			this.logger.info({
				strategy,
				candidateCount: candidates.length,
				msg: 'Processing candidates with strategy',
			});

			const processor = this.strategies.get(strategy);
			if (!processor)
			{
				throw new Error(`No processor registered for strategy: ${strategy}`);
			}

			// Validate candidates before processing
			const validCandidates = this.validateCandidates(candidates);
			if (validCandidates.length === 0)
			{
				this.logger.warn({ strategy, msg: 'No valid candidates to process' });
				return [];
			}

			// Process candidates through strategy
			const discovered = await processor.process(validCandidates, this.snapshotStore);

			// Update metrics
			this.updateMetrics(strategy, validCandidates.length, discovered.length);

			// Update confidence metrics for discovered domains
			for (const domain of discovered)
			{
				this.updateConfidenceMetrics(domain.confidence);
			}

			// Log results
			this.logger.info({
				strategy,
				inputCandidates: validCandidates.length,
				discoveredDomains: discovered.length,
				processingTime: Date.now() - startTime,
				msg: 'Strategy processing completed',
			});

			return discovered;
		}
		catch (error)
		{
			this.logger.error({
				strategy,
				error: (error as Error).message,
				candidateCount: candidates.length,
				msg: 'Strategy processing failed',
			});

			// Update error metrics
			this.incrementMetric(`strategy_errors_${strategy}`);

			throw error;
		}
	}

	/**
	 * Get historical snapshot for differential analysis
	 */
	async getHistoricalSnapshot(
		source: string,
		date: string,
	): Promise<DomainSnapshotType | null>
	{
		try
		{
			const snapshot = await this.snapshotStore.getSnapshot(source, date);

			if (snapshot)
			{
				this.logger.debug({
					source,
					date,
					domainCount: snapshot.domains.length,
					msg: 'Historical snapshot retrieved',
				});
			}
			else
			{
				this.logger.debug({ source, date, msg: 'No historical snapshot found' });
			}

			return snapshot;
		}
		catch (error)
		{
			this.logger.error({
				source,
				date,
				error: (error as Error).message,
				msg: 'Failed to retrieve historical snapshot',
			});
			throw error;
		}
	}

	/**
	 * Store snapshot for future differential analysis
	 */
	async storeSnapshot(source: string, domains: DomainCandidateType[]): Promise<void>
	{
		try
		{
			await this.snapshotStore.storeSnapshot(source, domains);

			this.logger.info({
				source,
				domainCount: domains.length,
				date: new Date().toISOString().split('T')[0],
				msg: 'Snapshot stored',
			});

			this.incrementMetric('snapshots_stored');
		}
		catch (error)
		{
			this.logger.error({
				source,
				domainCount: domains.length,
				error: (error as Error).message,
				msg: 'Failed to store snapshot',
			});
			throw error;
		}
	}

	/**
	 * Get comprehensive discovery metrics
	 */
	getMetrics(): DiscoveryMetricsType
	{
		const strategyBreakdown: Record<DiscoveryStrategyType, number> = {
			differential: this.getMetric('discovered_differential'),
			'zone-new': this.getMetric('discovered_zone-new'),
			'long-tail': this.getMetric('discovered_long-tail'),
			temporal: this.getMetric('discovered_temporal'),
		};

		const confidenceDistribution: Record<string, number> = {
			high: this.getMetric('confidence_high'),
			medium: this.getMetric('confidence_medium'),
			low: this.getMetric('confidence_low'),
		};

		return {
			totalCandidates: this.getMetric('total_candidates'),
			discoveredDomains: this.getMetric('total_discovered'),
			strategyBreakdown,
			confidenceDistribution,
		};
	}

	/**
	 * Get list of registered strategies
	 */
	getRegisteredStrategies(): DiscoveryStrategyType[]
	{
		return Array.from(this.strategies.keys());
	}

	/**
	 * Check if a strategy is registered
	 */
	hasStrategy(strategy: DiscoveryStrategyType): boolean
	{
		return this.strategies.has(strategy);
	}

	/**
	 * Process multiple strategies in parallel and aggregate results
	 */
	async processWithMultipleStrategies(
		strategies: DiscoveryStrategyType[],
		candidates: DomainCandidateType[],
	): Promise<DiscoveredDomainInterface[]>
	{
		const startTime = Date.now();

		try
		{
			this.logger.info({
				strategies,
				candidateCount: candidates.length,
				msg: 'Processing with multiple strategies',
			});

			// Process all strategies in parallel
			const strategyPromises = strategies.map(async (strategy) =>
			{
				if (!this.hasStrategy(strategy))
				{
					this.logger.warn({ strategy, msg: 'Strategy not registered, skipping' });
					return [];
				}

				try
				{
					return await this.processWithStrategy(strategy, candidates);
				}
				catch (error)
				{
					this.logger.error({
						strategy,
						error: (error as Error).message,
						msg: 'Strategy failed in parallel processing',
					});
					return [];
				}
			});

			const results = await Promise.all(strategyPromises);

			// Aggregate and deduplicate results
			const aggregated = this.aggregateResults(results.flat());

			this.logger.info({
				strategies,
				totalDiscovered: aggregated.length,
				processingTime: Date.now() - startTime,
				msg: 'Multi-strategy processing completed',
			});

			return aggregated;
		}
		catch (error)
		{
			this.logger.error({
				strategies,
				error: (error as Error).message,
				msg: 'Multi-strategy processing failed',
			});
			throw error;
		}
	}

	/**
	 * Validate candidates before processing
	 */
	private validateCandidates(candidates: DomainCandidateType[]): DomainCandidateType[]
	{
		return candidates.filter((candidate) =>
		{
			if (!candidate.domain || typeof candidate.domain !== 'string')
			{
				this.logger.warn({ candidate, msg: 'Invalid candidate: missing or invalid domain' });
				return false;
			}

			if (!candidate.source || typeof candidate.source !== 'string')
			{
				this.logger.warn({ candidate, msg: 'Invalid candidate: missing or invalid source' });
				return false;
			}

			return true;
		});
	}

	/**
	 * Update discovery metrics
	 */
	private updateMetrics(
		strategy: DiscoveryStrategyType,
		candidateCount: number,
		discoveredCount: number,
	): void
	{
		this.incrementMetric('total_candidates', candidateCount);
		this.incrementMetric('total_discovered', discoveredCount);
		this.incrementMetric(`discovered_${strategy}`, discoveredCount);
		this.incrementMetric(`processed_${strategy}`, candidateCount);
	}

	/**
	 * Aggregate results from multiple strategies and remove duplicates
	 */
	private aggregateResults(results: DiscoveredDomainInterface[]): DiscoveredDomainInterface[]
	{
		const domainMap = new Map<string, DiscoveredDomainInterface>();

		for (const result of results)
		{
			const existing = domainMap.get(result.domain);

			if (!existing || result.confidence > existing.confidence)
			{
				// Keep the result with higher confidence
				domainMap.set(result.domain, result);
			}
			else if (result.confidence === existing.confidence)
			{
				// Merge discovery reasons for same confidence
				const mergedResult = {
					...existing,
					discoveryReason: `${existing.discoveryReason}; ${result.discoveryReason}`,
				};
				domainMap.set(result.domain, mergedResult);
			}

			// Update confidence distribution metrics
			this.updateConfidenceMetrics(result.confidence);
		}

		return Array.from(domainMap.values());
	}

	/**
	 * Update confidence distribution metrics
	 */
	private updateConfidenceMetrics(confidence: number): void
	{
		if (confidence >= 0.8)
		{
			this.incrementMetric('confidence_high');
		}
		else if (confidence >= 0.5)
		{
			this.incrementMetric('confidence_medium');
		}
		else
		{
			this.incrementMetric('confidence_low');
		}
	}

	/**
	 * Initialize metrics counters
	 */
	private initializeMetrics(): void
	{
		const metricKeys = [
			'total_candidates',
			'total_discovered',
			'discovered_differential',
			'discovered_zone-new',
			'discovered_long-tail',
			'discovered_temporal',
			'processed_differential',
			'processed_zone-new',
			'processed_long-tail',
			'processed_temporal',
			'confidence_high',
			'confidence_medium',
			'confidence_low',
			'snapshots_stored',
			'strategy_errors_differential',
			'strategy_errors_zone-new',
			'strategy_errors_long-tail',
			'strategy_errors_temporal',
		];

		for (const key of metricKeys)
		{
			this.metrics.set(key, 0);
		}
	}

	/**
	 * Increment a metric counter
	 */
	private incrementMetric(key: string, value: number = 1): void
	{
		const current = this.metrics.get(key) || 0;
		this.metrics.set(key, current + value);
	}

	/**
	 * Get a metric value
	 */
	private getMetric(key: string): number
	{
		return this.metrics.get(key) || 0;
	}
}

export { IntelligentDiscoveryEngine };

export default IntelligentDiscoveryEngine;
