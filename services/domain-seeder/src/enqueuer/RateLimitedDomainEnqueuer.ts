import type { <PERSON>is<PERSON><PERSON><PERSON><PERSON><PERSON>, DatabaseManager , DomainDescriptionInterface } from '@shared';
import { logger as sharedLogger } from '@shared';

import type TokenBucket from '../ratelimiting/TokenBucket';
import type BackpressureController from '../ratelimiting/BackpressureController';
import type ProvenanceTracker from '../provenance/ProvenanceTracker';
import type IntegratedContentGenerator from '../content/IntegratedContentGenerator';
import type { GeneratedContent } from '../content/IntegratedContentGenerator';
import type { DiscoveryStrategyType } from '../interfaces/DiscoveryEngine';

type DiscoveredDomain =
{
	domain: string;
	source: string;
	discoveryStrategy: DiscoveryStrategyType;
	confidence: number;
	discoveryReason: string;
	rank?: number;
	metadata?: Record<string, unknown>;
};

type EnqueuedDomain =
{
	domain: string;
	firstSource: string;
	seenAt: string;
	preGenerated: boolean;
	content: {
		summary: string;
		category: {
			primary: string;
			secondary?: string;
		};
		tags: string[];
	};
	discoveryStrategy: DiscoveryStrategyType;
	confidence: number;
	priority?: 'high' | 'normal';
};

type EnqueueConfig =
{
	enqueueBatchSize: number;
	enqueueIntervalMs: number;
	maxNewPerDay: number;
	newQueueMaxDepth: number;
	normalStreamName: string;
	highPriorityStreamName: string;
	idempotencyTtlSeconds: number;
};

type EnqueueMetrics =
{
	enqueuedTotal: number;
	rateLimitedTotal: number;
	duplicatesSkipped: number;
	contentGenerationFailures: number;
	backpressureWaits: number;
};

type ContentGenerationResult =
{
	success: boolean;
	content?: GeneratedContent;
	error?: string;
};

const logger = sharedLogger.getLogger('RateLimitedDomainEnqueuer');

/**
 * Rate-limited domain enqueuer with Redis Streams integration,
 * idempotency checking, tiered queuing, and backpressure controls
*/
class RateLimitedDomainEnqueuer
{
	private readonly redis: RedisClientWrapper;

	private readonly dbManager: DatabaseManager;

	private readonly tokenBucket: TokenBucket;

	private readonly backpressureController: BackpressureController;

	private readonly provenanceTracker?: ProvenanceTracker;

	private readonly contentGenerator?: IntegratedContentGenerator;

	private readonly config: EnqueueConfig;

	private readonly metrics: EnqueueMetrics;

	private readonly seenAllTimeKey = 'seen_all_time';

	private isRunning = false;

	constructor(
		redis: RedisClientWrapper,
		dbManager: DatabaseManager,
		tokenBucket: TokenBucket,
		backpressureController: BackpressureController,
		config: Partial<EnqueueConfig> = {},
		provenanceTracker?: ProvenanceTracker,
		contentGenerator?: IntegratedContentGenerator,
	)
	{
		this.redis = redis;
		this.dbManager = dbManager;
		this.tokenBucket = tokenBucket;
		this.backpressureController = backpressureController;
		this.provenanceTracker = provenanceTracker;
		this.contentGenerator = contentGenerator;

		this.config = {
			enqueueBatchSize: config.enqueueBatchSize || 1000,
			enqueueIntervalMs: config.enqueueIntervalMs || 1000,
			maxNewPerDay: config.maxNewPerDay || 500000,
			newQueueMaxDepth: config.newQueueMaxDepth || 200000,
			normalStreamName: config.normalStreamName || 'stream:new:domains',
			highPriorityStreamName: config.highPriorityStreamName || 'stream:new:domains:high',
			idempotencyTtlSeconds: config.idempotencyTtlSeconds || 2592000, // 30 days
		};

		this.metrics = {
			enqueuedTotal: 0,
			rateLimitedTotal: 0,
			duplicatesSkipped: 0,
			contentGenerationFailures: 0,
			backpressureWaits: 0,
		};

		logger.info({
			config: this.config,
		}, 'RateLimitedDomainEnqueuer initialized');
	}

	/**
	 * Start the enqueuer service
	 */
	async start(): Promise<void>
	{
		if (this.isRunning)
		{
			return;
		}

		this.isRunning = true;
		await this.tokenBucket.start();

		logger.info('RateLimitedDomainEnqueuer started');
	}

	/**
	 * Stop the enqueuer service
	 */
	async stop(): Promise<void>
	{
		this.isRunning = false;
		await this.tokenBucket.stop();

		logger.info('RateLimitedDomainEnqueuer stopped');
	}

	/**
	 * Enqueue discovered domains with rate limiting and backpressure controls
	 */
	async enqueueDomains(domains: DiscoveredDomain[]): Promise<number>
	{
		if (!this.isRunning)
		{
			throw new Error('Enqueuer not started');
		}

		if (domains.length === 0)
		{
			return 0;
		}

		logger.info({
			domainCount: domains.length,
		}, 'Starting domain enqueue process');

		let enqueuedCount = 0;
		const batches = this.createBatches(domains, this.config.enqueueBatchSize);

		for (const batch of batches)
		{
			// Check daily limit
			if (await this.isDailyLimitReached())
			{
				logger.warn('Daily enqueue limit reached, stopping enqueue process');
				break;
			}

			// Check queue depth backpressure
			if (await this.isQueueDepthExceeded())
			{
				logger.warn('Queue depth exceeded, waiting for capacity');
				await this.backpressureController.waitForCapacity();
				this.metrics.backpressureWaits++;
			}

			// Wait for token bucket capacity
			const tokensNeeded = batch.length;
			if (!(await this.tokenBucket.waitForTokens(tokensNeeded, 30000)))
			{
				logger.warn('Token bucket timeout, skipping batch');
				this.metrics.rateLimitedTotal += batch.length;
				continue;
			}

			// Process batch with enhanced error handling
			const batchEnqueuedCount = await this.processBatchEnhanced(batch);
			enqueuedCount += batchEnqueuedCount;

			// Wait between batches
			if (batches.indexOf(batch) < batches.length - 1)
			{
				await this.sleep(this.config.enqueueIntervalMs);
			}
		}

		logger.info({
			totalDomains: domains.length,
			enqueuedCount,
			metrics: this.metrics,
		}, 'Domain enqueue process completed');

		return enqueuedCount;
	}

	/**
	 * Get current enqueue metrics
	 */
	getMetrics(): EnqueueMetrics
	{
		return { ...this.metrics };
	}

	/**
	 * Reset metrics counters
	 */
	resetMetrics(): void
	{
		this.metrics.enqueuedTotal = 0;
		this.metrics.rateLimitedTotal = 0;
		this.metrics.duplicatesSkipped = 0;
		this.metrics.contentGenerationFailures = 0;
		this.metrics.backpressureWaits = 0;
	}

	/**
	 * Check if a domain has already been discovered (idempotency check)
	 */
	async isDomainAlreadyDiscovered(domain: string): Promise<boolean>
	{
		const idempotencyKey = `discovered:${domain}`;

		try
		{
			const exists = await this.redis.exists(idempotencyKey);
			return exists;
		}
		catch (error)
		{
			logger.error({
				msg: 'Failed to check domain idempotency',
				domain,
				error: error.message,
			});
			return false; // Fail open to avoid blocking
		}
	}

	/**
	 * Mark domain as discovered for idempotency
	 */
	async markDomainAsDiscovered(domain: string): Promise<void>
	{
		const idempotencyKey = `discovered:${domain}`;

		try
		{
			await this.redis.setex(idempotencyKey, this.config.idempotencyTtlSeconds, '1');
		}
		catch (error)
		{
			logger.error({
				msg: 'Failed to mark domain as discovered',
				domain,
				error: error.message,
			});
		}
	}

	/**
	 * Get current queue depths
	 */
	async getQueueDepths(): Promise<{ normal: number; high: number; total: number }>
	{
		try
		{
			const client = this.redis.getClient();
			if (!client)
			{
				throw new Error('Redis client not available');
			}

			const [normalDepth, highDepth] = await Promise.all([
				client.xLen(this.config.normalStreamName),
				client.xLen(this.config.highPriorityStreamName),
			]);

			return {
				normal: normalDepth,
				high: highDepth,
				total: normalDepth + highDepth,
			};
		}
		catch (error)
		{
			logger.error({ msg: 'Failed to get queue depths', error: error.message });
			return { normal: 0, high: 0, total: 0 };
		}
	}

	/**
	 * Create consumer groups for Redis Streams if they don't exist
	 */
	async ensureConsumerGroups(): Promise<void>
	{
		const client = this.redis.getClient();
		if (!client)
		{
			throw new Error('Redis client not available');
		}

		const streams = [this.config.normalStreamName, this.config.highPriorityStreamName];
		const consumerGroup = 'domain-processors';

		for (const streamName of streams)
		{
			try
			{
				// Try to create consumer group, ignore error if it already exists
				await client.xGroupCreate(streamName, consumerGroup, '0', { MKSTREAM: true });
				logger.info({ msg: 'Consumer group created', streamName, consumerGroup });
			}
			catch (error)
			{
				// Consumer group already exists or stream doesn't exist yet
				if (!error.message.includes('BUSYGROUP'))
				{
					logger.debug({
						msg: 'Consumer group creation skipped',
						streamName,
						consumerGroup,
						reason: error.message,
					});
				}
			}
		}
	}

	/**
	 * Get pending messages count for monitoring
	 */
	async getPendingMessagesCount(): Promise<{ normal: number; high: number; total: number }>
	{
		const client = this.redis.getClient();
		if (!client)
		{
			return { normal: 0, high: 0, total: 0 };
		}

		const consumerGroup = 'domain-processors';

		try
		{
			const [normalPending, highPending] = await Promise.all([
				this.getPendingForStream(this.config.normalStreamName, consumerGroup),
				this.getPendingForStream(this.config.highPriorityStreamName, consumerGroup),
			]);

			return {
				normal: normalPending,
				high: highPending,
				total: normalPending + highPending,
			};
		}
		catch (error)
		{
			logger.error({ msg: 'Failed to get pending messages count', error: error.message });
			return { normal: 0, high: 0, total: 0 };
		}
	}

	private async getPendingForStream(streamName: string, consumerGroup: string): Promise<number>
	{
		try
		{
			const pendingInfo = await this.redis.xpending(streamName, consumerGroup);
			return pendingInfo.pending || 0; // Get pending count from object
		}
		catch (error)
		{
			// Consumer group might not exist yet
			return 0;
		}
	}

	private async processBatch(domains: DiscoveredDomain[]): Promise<number>
	{
		let enqueuedCount = 0;

		// Filter out already discovered domains
		const newDomains: DiscoveredDomain[] = [];
		for (const domain of domains)
		{
			if (await this.isDomainAlreadyDiscovered(domain.domain))
			{
				this.metrics.duplicatesSkipped++;
				continue;
			}
			newDomains.push(domain);
		}

		if (newDomains.length === 0)
		{
			return 0;
		}

		// Generate content for domains using IntegratedContentGenerator
		const domainsWithContent: Array<DiscoveredDomain & { content?: GeneratedContent }> = [];

		if (this.contentGenerator)
		{
			// Use integrated content generator for batch processing
			try
			{
				const contentResults = await this.contentGenerator.generateContentForDiscoveredDomains(
					newDomains as any,
					{
						mode: 'preGenerated',
						minWords: 320,
						maxWords: 500,
						minTags: 5,
						maxTags: 12,
						seoOptimization: true,
					},
				);

				for (const domain of newDomains)
				{
					const content = contentResults.get(domain.domain);
					if (content)
					{
						domainsWithContent.push({
							...domain,
							content,
						});
					}
					else
					{
						this.metrics.contentGenerationFailures++;
						logger.warn({
							msg: 'Content generation failed for domain',
							domain: domain.domain,
							error: 'No content generated by IntegratedContentGenerator',
						});
					}
				}
			}
			catch (error)
			{
				logger.error({
					msg: 'Batch content generation failed, falling back to individual generation',
					error: error.message,
					domainCount: newDomains.length,
				});

				// Fallback to individual content generation
				for (const domain of newDomains)
				{
					const contentResult = await this.generatePreGeneratedContentFallback(domain);
					if (contentResult.success)
					{
						domainsWithContent.push({
							...domain,
							content: contentResult.content,
						});
					}
					else
					{
						this.metrics.contentGenerationFailures++;
						logger.warn({
							msg: 'Fallback content generation failed for domain',
							domain: domain.domain,
							error: contentResult.error,
						});
					}
				}
			}
		}
		else
		{
			// Fallback to legacy content generation if no IntegratedContentGenerator
			for (const domain of newDomains)
			{
				const contentResult = await this.generatePreGeneratedContentFallback(domain);
				if (contentResult.success)
				{
					domainsWithContent.push({
						...domain,
						content: contentResult.content,
					});
				}
				else
				{
					this.metrics.contentGenerationFailures++;
					logger.warn({
						msg: 'Legacy content generation failed for domain',
						domain: domain.domain,
						error: contentResult.error,
					});
				}
			}
		}

		// Enqueue domains with content
		for (const domain of domainsWithContent)
		{
			try
			{
				await this.enqueueSingleDomain(domain);
				await this.markDomainAsDiscovered(domain.domain);
				enqueuedCount++;
				this.metrics.enqueuedTotal++;
			}
			catch (error)
			{
				logger.error({
					msg: 'Failed to enqueue domain',
					domain: domain.domain,
					error: error.message,
				});
			}
		}

		return enqueuedCount;
	}

	private async enqueueSingleDomain(domain: DiscoveredDomain & { content?: GeneratedContent }): Promise<void>
	{
		// Extract content for enqueue message
		const contentForEnqueue = domain.content ? {
			summary: domain.content.summary,
			category: domain.content.category,
			tags: domain.content.tags,
			seoSummary: domain.content.seoSummary,
			wordCount: domain.content.wordCount,
			confidence: domain.content.confidence,
			method: domain.content.method,
			version: domain.content.metadata.contentVersion,
		} : undefined;

		const enqueuedDomain: EnqueuedDomain = {
			domain: domain.domain,
			firstSource: domain.source,
			seenAt: new Date().toISOString(),
			preGenerated: domain.content?.method === 'preGenerated' || true,
			content: contentForEnqueue || {
				summary: `${domain.domain} is a domain discovered through ${domain.discoveryStrategy} analysis.`,
				category: { primary: 'business' },
				tags: ['website', 'domain', 'business'],
			},
			discoveryStrategy: domain.discoveryStrategy,
			confidence: domain.confidence,
			priority: this.determinePriority(domain),
		};

		// Choose stream based on priority
		const streamName = enqueuedDomain.priority === 'high'
			? this.config.highPriorityStreamName
			: this.config.normalStreamName;

		const client = this.redis.getClient();
		if (!client)
		{
			throw new Error('Redis client not available');
		}

		// Ensure consumer groups exist before adding messages
		await this.ensureConsumerGroups();

		// Add to Redis Stream with enhanced metadata for ACK handling and content versioning
		const messageId = await client.xAdd(streamName, '*', {
			domain: enqueuedDomain.domain,
			firstSource: enqueuedDomain.firstSource,
			seenAt: enqueuedDomain.seenAt,
			preGenerated: enqueuedDomain.preGenerated.toString(),
			content: JSON.stringify(enqueuedDomain.content),
			discoveryStrategy: enqueuedDomain.discoveryStrategy,
			confidence: enqueuedDomain.confidence.toString(),
			priority: enqueuedDomain.priority || 'normal',
			// Content versioning metadata
			contentMethod: domain.content?.method || 'preGenerated',
			contentVersion: domain.content?.metadata.contentVersion?.toString() || '1',
			contentConfidence: domain.content?.confidence?.toString() || '0.5',
			scheduledForLiveUpdate: this.shouldScheduleForLiveUpdate(domain).toString(),
			// Additional metadata for reliability
			enqueueId: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
			retryCount: '0',
			maxRetries: '3',
		});

		// Store provenance data for audit trail with content information
		await this.storeProvenanceData(enqueuedDomain, messageId, domain.content);

		logger.debug({
			domain: enqueuedDomain.domain,
			stream: streamName,
			priority: enqueuedDomain.priority,
			messageId,
			msg: 'Domain enqueued successfully'
		});
	}

	/**
	 * Store lightweight provenance data for audit trail using ProvenanceTracker
	 */
	private async storeProvenanceData(domain: EnqueuedDomain, messageId: string, content?: GeneratedContent): Promise<void>
	{
		try
		{
			// Use ProvenanceTracker if available, otherwise fallback to legacy method
			if (this.provenanceTracker)
			{
				await this.provenanceTracker.recordEnqueue(
					domain.domain,
					messageId,
					domain.priority || 'normal',
					domain.preGenerated,
				);

				// Record additional content generation metadata if available
				if (content)
				{
					const additionalMetadata = {
						contentGenerated: true,
						contentMethod: content.method,
						contentVersion: content.metadata.contentVersion,
						contentConfidence: content.confidence,
						contentWordCount: content.wordCount,
						contentTagCount: content.tags.length,
						contentSources: content.sources,
					};

					// This would typically be stored in the discovery record
					// For now, we'll log it for audit purposes
					logger.info({
						domain: domain.domain,
						messageId,
						...additionalMetadata,
						msg: 'Content generation metadata recorded'
					});
				}
			}
			else
			{
				// Legacy fallback method
				const provenanceKey = `provenance:${domain.domain}`;
				const provenanceData = {
					domain: domain.domain,
					firstSeenAt: domain.seenAt,
					firstSource: domain.firstSource,
					discoveryStrategy: domain.discoveryStrategy,
					confidence: domain.confidence,
					priority: domain.priority,
					messageId,
					enqueuedAt: new Date().toISOString(),
					// Add content metadata to legacy provenance
					contentGenerated: !!content,
					contentMethod: content?.method,
					contentVersion: content?.metadata.contentVersion,
					contentConfidence: content?.confidence,
				};

				// Store with 30-day TTL
				await this.redis.setex(provenanceKey, 2592000, JSON.stringify(provenanceData));
			}

			logger.debug({
				domain: domain.domain,
				messageId,
				usingTracker: !!this.provenanceTracker,
				msg: 'Provenance data stored'
			});
		}
		catch (error)
		{
			logger.error({
				domain: domain.domain,
				error: error.message,
				msg: 'Failed to store provenance data'
			});
			// Don't throw - provenance storage failure shouldn't block enqueuing
		}
	}

	/**
	 * Determine if domain should be scheduled for live update
	 */
	private shouldScheduleForLiveUpdate(domain: DiscoveredDomain & { content?: GeneratedContent }): boolean
	{
		// Schedule for live update if:
		// 1. Content confidence is high (>= 0.8) - high value domains
		// 2. Discovery confidence is high (>= 0.8) - likely important domains
		// 3. Domain is from a high-priority source
		const contentConfidence = domain.content?.confidence || 0;
		const discoveryConfidence = domain.confidence || 0;
		const isHighPrioritySource = ['tranco', 'radar', 'umbrella'].includes(domain.source);

		return contentConfidence >= 0.8 || discoveryConfidence >= 0.8 || isHighPrioritySource;
	}

	/**
	 * Fallback content generation method (legacy) - converts GeneratedContent to old format
	 */
	private async generatePreGeneratedContentFallback(domain: DiscoveredDomain): Promise<ContentGenerationResult>
	{
		try
		{
			const legacyResult = await this.generatePreGeneratedContent(domain);

			if (legacyResult.success && legacyResult.content)
			{
				// Convert legacy content format to GeneratedContent format
				const generatedContent: GeneratedContent = {
					summary: legacyResult.content.summary,
					category: legacyResult.content.category,
					tags: legacyResult.content.tags,
					seoSummary: legacyResult.content.summary.substring(0, 160),
					wordCount: this.countWords(legacyResult.content.summary),
					confidence: 0.5, // Default confidence for legacy content
					method: 'preGenerated',
					sources: ['legacy-heuristics'],
					metadata: {
						generatedAt: new Date().toISOString(),
						version: '1.0.0',
						inputs: ['legacy-generation'],
						contentVersion: 1,
						lastUpdated: new Date().toISOString(),
					},
				};

				return {
					success: true,
					content: generatedContent,
				};
			}

			return legacyResult;
		}
		catch (error)
		{
			return {
				success: false,
				error: error.message,
			};
		}
	}

	private async generatePreGeneratedContent(domain: DiscoveredDomain): Promise<ContentGenerationResult>
	{
		try
		{
			// Extract domain parts for heuristic analysis
			const domainParts = domain.domain.split('.');
			const domainName = domainParts[0];
			const tld = domainParts.slice(1).join('.');

			// Generate category based on domain tokens and TLD
			const category = this.generateCategory(domainName, tld, domain.metadata);

			// Generate tags based on domain analysis
			const tags = this.generateTags(domainName, tld, category, domain.metadata);

			// Generate summary (320+ words)
			const summary = this.generateSummary(domain.domain, domainName, tld, category, tags, domain);

			return {
				success: true,
				content: {
					summary,
					category,
					tags,
					seoSummary: summary.substring(0, 160),
					wordCount: this.countWords(summary),
					confidence: 0.7, // Medium confidence for heuristics
					method: 'preGenerated',
					sources: ['domain-analysis'],
					metadata: {
						generatedAt: new Date().toISOString(),
						version: '1.0.0',
						inputs: ['heuristic-generation'],
						contentVersion: 1,
						lastUpdated: new Date().toISOString(),
					},
				},
			};
		}
		catch (error)
		{
			return {
				success: false,
				error: error.message,
			};
		}
	}

	private generateCategory(domainName: string, tld: string, metadata?: Record<string, unknown>): { primary: string; secondary?: string }
	{
		// TLD-based category mapping
		const tldCategories: Record<string, string> = {
			edu: 'Education',
			gov: 'Government',
			org: 'Non-profit',
			mil: 'Government',
			museum: 'Arts & Culture',
			travel: 'Travel & Tourism',
			jobs: 'Employment',
			mobi: 'Technology',
			tel: 'Telecommunications',
			name: 'Personal',
		};

		// Domain name keyword analysis
		const businessKeywords = ['shop', 'store', 'buy', 'sell', 'market', 'commerce', 'trade'];
		const techKeywords = ['tech', 'software', 'app', 'digital', 'cyber', 'web', 'online'];
		const newsKeywords = ['news', 'media', 'press', 'journal', 'times', 'post', 'herald'];
		const financeKeywords = ['bank', 'finance', 'money', 'invest', 'loan', 'credit', 'pay'];
		const healthKeywords = ['health', 'medical', 'doctor', 'clinic', 'hospital', 'care'];

		const lowerDomainName = domainName.toLowerCase();

		// Check TLD first
		if (tldCategories[tld])
		{
			return { primary: tldCategories[tld] };
		}

		// Check domain name keywords
		if (businessKeywords.some(keyword => lowerDomainName.includes(keyword)))
		{
			return { primary: 'Business & Commerce', secondary: 'E-commerce' };
		}

		if (techKeywords.some(keyword => lowerDomainName.includes(keyword)))
		{
			return { primary: 'Technology', secondary: 'Software & Services' };
		}

		if (newsKeywords.some(keyword => lowerDomainName.includes(keyword)))
		{
			return { primary: 'News & Media', secondary: 'Online Publishing' };
		}

		if (financeKeywords.some(keyword => lowerDomainName.includes(keyword)))
		{
			return { primary: 'Finance & Banking', secondary: 'Financial Services' };
		}

		if (healthKeywords.some(keyword => lowerDomainName.includes(keyword)))
		{
			return { primary: 'Health & Medicine', secondary: 'Healthcare Services' };
		}

		// Default category
		return { primary: 'Business & Commerce', secondary: 'General Business' };
	}

	private generateTags(domainName: string, tld: string, category: { primary: string; secondary?: string }, metadata?: Record<string, unknown>): string[]
	{
		const tags: string[] = [];
		const lowerDomainName = domainName.toLowerCase();

		// Add category-based tags
		tags.push(category.primary.toLowerCase().replace(/\s+/g, '-'));
		if (category.secondary)
		{
			tags.push(category.secondary.toLowerCase().replace(/\s+/g, '-'));
		}

		// Add TLD-based tags
		tags.push(`${tld}-domain`);

		// Add domain length-based tags
		if (domainName.length <= 5)
		{
			tags.push('short-domain');
		}
		else if (domainName.length >= 15)
		{
			tags.push('long-domain');
		}

		// Add common business tags based on domain analysis
		if (lowerDomainName.includes('online') || lowerDomainName.includes('web'))
		{
			tags.push('online-presence');
		}

		if (lowerDomainName.includes('service') || lowerDomainName.includes('solution'))
		{
			tags.push('service-provider');
		}

		// Add discovery strategy tag
		if (metadata?.discoveryStrategy)
		{
			tags.push(`discovered-via-${metadata.discoveryStrategy}`);
		}

		// Add generic website tags
		tags.push('website', 'domain', 'internet-presence');

		// Ensure we have at least 5 tags
		while (tags.length < 5)
		{
			const genericTags = ['web-presence', 'digital-platform', 'online-resource', 'internet-site', 'web-service'];
			const missingTag = genericTags.find(tag => !tags.includes(tag));
			if (missingTag)
			{
				tags.push(missingTag);
			}
			else
			{
				break;
			}
		}

		// Limit to 12 tags maximum
		return tags.slice(0, 12);
	}

	private generateSummary(
		fullDomain: string,
		domainName: string,
		tld: string,
		category: { primary: string; secondary?: string },
		tags: string[],
		discoveredDomain: DiscoveredDomain,
	): string
	{
		const categoryDescription = this.getCategoryDescription(category.primary);
		const tldDescription = this.getTldDescription(tld);
		const discoveryContext = this.getDiscoveryContext(discoveredDomain);

		// Generate a comprehensive 320+ word summary
		const summary = `${fullDomain} is a domain in the ${category.primary.toLowerCase()} sector${category.secondary ? `, specifically focused on ${category.secondary.toLowerCase()}` : ''}. ${categoryDescription}

The domain ${domainName}.${tld} ${tldDescription} This positioning suggests a ${category.primary.toLowerCase()} orientation with potential for ${this.generateBusinessPotential(category, tags)}.

${discoveryContext} The domain's discovery through ${discoveredDomain.discoveryStrategy} analysis indicates ${this.getStrategyImplication(discoveredDomain.discoveryStrategy, discoveredDomain.confidence)}.

From a technical perspective, ${fullDomain} operates under the .${tld} top-level domain, which ${this.getTechnicalImplication(tld)}. The domain structure and naming convention suggest ${this.getDomainStructureAnalysis(domainName, tld)}.

The ${category.primary.toLowerCase()} sector typically requires ${this.getSectorRequirements(category.primary)}, and ${fullDomain} appears positioned to address these needs. The domain's characteristics align with modern digital business practices, offering potential for ${this.getDigitalPotential(category, tags)}.

Market analysis suggests that domains in the ${category.primary.toLowerCase()} space benefit from ${this.getMarketAdvantages(category.primary)}. The ${domainName} brand name provides ${this.getBrandingAnalysis(domainName)} for establishing market presence.

${fullDomain} represents an opportunity in the digital landscape, with its ${category.primary.toLowerCase()} focus providing a foundation for ${this.getFutureOpportunities(category, discoveredDomain)}. The domain's discovery metrics and categorization suggest potential for sustainable online presence and business development.

This analysis is based on heuristic evaluation of domain characteristics, TLD properties, and discovery patterns. Further live analysis would provide additional insights into actual content, performance metrics, and market positioning.`;

		return summary;
	}

	private generateBusinessPotential(category: { primary: string; secondary?: string }, tags: string[]): string
	{
		const hasOnlineTag = tags.some(tag => tag.includes('online') || tag.includes('web') || tag.includes('digital'));
		const hasServiceTag = tags.some(tag => tag.includes('service') || tag.includes('solution'));

		let potential = '';

		if (hasOnlineTag)
		{
			potential += 'comprehensive online presence development, digital marketing integration, and web-based service delivery';
		}
		else
		{
			potential += 'digital transformation opportunities, online market expansion, and web presence establishment';
		}

		if (hasServiceTag)
		{
			potential += ', enhanced by service-oriented positioning and solution-focused market approach';
		}

		return potential;
	}

	private getCategoryDescription(category: string): string
	{
		const descriptions: Record<string, string> = {
			'Business & Commerce': 'This sector encompasses a wide range of commercial activities, from traditional business services to modern e-commerce platforms, representing the backbone of digital commerce.',
			Technology: 'The technology sector drives innovation and digital transformation, covering software development, IT services, and emerging technological solutions.',
			Education: 'Educational domains serve learning institutions, online courses, and knowledge-sharing platforms that facilitate learning and skill development.',
			Government: 'Government domains provide official information, public services, and civic engagement platforms for citizens and organizations.',
			'Health & Medicine': 'Healthcare domains focus on medical services, health information, wellness resources, and medical technology solutions.',
			'News & Media': 'Media domains deliver news, information, entertainment, and communication services to diverse audiences.',
		};

		return descriptions[category] || 'This sector represents a specialized area of digital activity with unique characteristics and market opportunities.';
	}

	private getTldDescription(tld: string): string
	{
		const descriptions: Record<string, string> = {
			com: 'utilizes the most popular and trusted commercial top-level domain, providing maximum credibility and user recognition.',
			org: 'employs the organization-focused TLD, typically associated with non-profit entities and community-oriented initiatives.',
			net: 'uses the network-oriented domain extension, often chosen for technology and internet-related services.',
			edu: 'operates under the educational domain reserved for accredited educational institutions.',
			gov: 'functions within the government domain space, indicating official governmental status.',
		};

		return descriptions[tld] || `operates under the .${tld} domain extension, which provides specific market positioning and user expectations.`;
	}

	private getDiscoveryContext(domain: DiscoveredDomain): string
	{
		const contexts: Record<string, string> = {
			differential: 'This domain was identified through differential analysis of ranking changes, suggesting recent emergence or growth in online visibility.',
			'zone-new': 'The domain was discovered through zone file analysis, indicating it is a newly registered domain with fresh market entry potential.',
			'long-tail': 'Discovery occurred through long-tail exploration beyond typical ranking lists, suggesting niche market positioning or emerging relevance.',
			temporal: 'Temporal analysis revealed this domain through ranking velocity changes, indicating dynamic growth or shifting market position.',
		};

		return contexts[domain.discoveryStrategy] || 'The domain was identified through systematic discovery processes designed to find emerging online properties.';
	}

	private getStrategyImplication(strategy: string, confidence: number): string
	{
		const confidenceLevel = confidence > 0.8 ? 'high' : confidence > 0.6 ? 'moderate' : 'emerging';

		const implications: Record<string, string> = {
			differential: `${confidenceLevel} confidence in its recent market entry or visibility changes`,
			'zone-new': `${confidenceLevel} confidence in its status as a newly established online presence`,
			'long-tail': `${confidenceLevel} confidence in its niche market potential and specialized positioning`,
			temporal: `${confidenceLevel} confidence in its growth trajectory and market momentum`,
		};

		return implications[strategy] || `${confidenceLevel} confidence in its market relevance and discovery significance`;
	}

	private getTechnicalImplication(tld: string): string
	{
		const implications: Record<string, string> = {
			com: 'provides global recognition and commercial credibility with broad market appeal',
			org: 'suggests organizational focus with community trust and non-commercial positioning',
			net: 'indicates network or technology orientation with technical service implications',
			edu: 'ensures educational authority and academic credibility within institutional frameworks',
			gov: 'guarantees governmental authenticity and official status recognition',
		};

		return implications[tld] || 'offers specific market positioning with targeted audience expectations and industry associations';
	}

	private getDomainStructureAnalysis(domainName: string, tld: string): string
	{
		const length = domainName.length;
		const hasNumbers = /\d/.test(domainName);
		const hasHyphens = /-/.test(domainName);

		let analysis = '';

		if (length <= 5)
		{
			analysis += 'a concise and memorable brand identity with strong recall potential';
		}
		else if (length <= 10)
		{
			analysis += 'balanced naming that combines memorability with descriptive clarity';
		}
		else
		{
			analysis += 'descriptive naming that prioritizes clarity and keyword relevance';
		}

		if (hasNumbers)
		{
			analysis += ', incorporating numeric elements that may enhance brand distinctiveness';
		}

		if (hasHyphens)
		{
			analysis += ', utilizing hyphenation for improved readability and keyword separation';
		}

		return analysis;
	}

	private getSectorRequirements(category: string): string
	{
		const requirements: Record<string, string> = {
			'Business & Commerce': 'strong customer trust, secure transactions, reliable service delivery, and competitive market positioning',
			Technology: 'innovation leadership, technical expertise, scalability, and cutting-edge solution development',
			Education: 'credible information sources, accessible learning resources, institutional authority, and educational effectiveness',
			Government: 'transparency, accessibility, security, and reliable public service delivery',
			'Health & Medicine': 'regulatory compliance, patient privacy, medical accuracy, and healthcare service quality',
			'News & Media': 'editorial credibility, timely information delivery, audience engagement, and content quality',
		};

		return requirements[category] || 'specialized expertise, market understanding, and targeted service delivery';
	}

	private getDigitalPotential(category: { primary: string; secondary?: string }, tags: string[]): string
	{
		const hasOnlineTag = tags.some(tag => tag.includes('online') || tag.includes('web') || tag.includes('digital'));
		const hasServiceTag = tags.some(tag => tag.includes('service') || tag.includes('solution'));

		let potential = '';

		if (hasOnlineTag)
		{
			potential += 'comprehensive online presence development, digital marketing integration, and web-based service delivery';
		}
		else
		{
			potential += 'digital transformation opportunities, online market expansion, and web presence establishment';
		}

		if (hasServiceTag)
		{
			potential += ', enhanced by service-oriented positioning and solution-focused market approach';
		}

		return potential;
	}

	private getMarketAdvantages(category: string): string
	{
		const advantages: Record<string, string> = {
			'Business & Commerce': 'established customer trust, proven business models, and diverse revenue opportunities',
			Technology: 'rapid innovation cycles, scalable solutions, and high growth potential',
			Education: 'stable demand, institutional partnerships, and knowledge-based value creation',
			Government: 'regulatory authority, public trust, and essential service positioning',
			'Health & Medicine': 'critical service demand, regulatory protection, and specialized expertise requirements',
			'News & Media': 'audience loyalty, content monetization, and information authority',
		};

		return advantages[category] || 'specialized market positioning, targeted audience engagement, and sector-specific opportunities';
	}

	private getBrandingAnalysis(domainName: string): string
	{
		const length = domainName.length;
		const isPronounceable = !/[^a-zA-Z0-9-]/.test(domainName) && !domainName.includes('--');

		if (length <= 5 && isPronounceable)
		{
			return 'excellent branding potential with high memorability and strong market recognition capabilities';
		}
		if (length <= 10 && isPronounceable)
		{
			return 'solid branding foundation with good memorability and professional market positioning';
		}
		if (isPronounceable)
		{
			return 'descriptive branding approach with keyword relevance and clear market communication';
		}

		return 'unique branding characteristics that may require strategic marketing for optimal market penetration';
	}

	private getFutureOpportunities(category: { primary: string; secondary?: string }, domain: DiscoveredDomain): string
	{
		const confidence = domain.confidence > 0.8 ? 'high-confidence' : domain.confidence > 0.6 ? 'moderate-confidence' : 'emerging';
		const strategy = domain.discoveryStrategy;

		let opportunities = '';

		if (strategy === 'zone-new')
		{
			opportunities = 'early market entry advantages, brand establishment opportunities, and first-mover positioning';
		}
		else if (strategy === 'temporal')
		{
			opportunities = 'growth momentum capitalization, market expansion potential, and scaling opportunities';
		}
		else if (strategy === 'differential')
		{
			opportunities = 'competitive positioning, market differentiation, and visibility enhancement';
		}
		else
		{
			opportunities = 'niche market development, specialized positioning, and targeted audience engagement';
		}

		return `${confidence} ${opportunities} within the ${category.primary.toLowerCase()} sector`;
	}

	private determinePriority(domain: DiscoveredDomain): 'high' | 'normal'
	{
		// High priority criteria
		if (domain.confidence > 0.9)
		{
			return 'high';
		}

		if (domain.discoveryStrategy === 'zone-new' && domain.confidence > 0.8)
		{
			return 'high';
		}

		if (domain.rank && domain.rank <= 100000)
		{
			return 'high';
		}

		// Check for premium TLDs
		const premiumTlds = ['com', 'org', 'net', 'edu', 'gov'];
		const tld = domain.domain.split('.').pop();
		if (premiumTlds.includes(tld || '') && domain.confidence > 0.7)
		{
			return 'high';
		}

		return 'normal';
	}

	private async isDailyLimitReached(): Promise<boolean>
	{
		const today = new Date().toISOString().split('T')[0];
		const dailyCountKey = `daily_enqueue_count:${today}`;

		try
		{
			const currentCount = await this.redis.get<number>(dailyCountKey) || 0;
			return currentCount >= this.config.maxNewPerDay;
		}
		catch (error)
		{
			logger.error({ error: error.message, msg: 'Failed to check daily limit' });
			return false; // Fail open
		}
	}

	private async isQueueDepthExceeded(): Promise<boolean>
	{
		const depths = await this.getQueueDepths();
		return depths.total > this.config.newQueueMaxDepth;
	}

	private createBatches<T>(items: T[], batchSize: number): T[][]
	{
		const batches: T[][] = [];
		for (let i = 0; i < items.length; i += batchSize)
		{
			batches.push(items.slice(i, i + batchSize));
		}
		return batches;
	}

	/**
	 * Get stream information for monitoring
	 */
	async getStreamInfo(): Promise<{
		normal: { length: number; pending: number; consumers: number };
		high: { length: number; pending: number; consumers: number };
	}>
	{
		const client = this.redis.getClient();
		if (!client)
		{
			return {
				normal: { length: 0, pending: 0, consumers: 0 },
				high: { length: 0, pending: 0, consumers: 0 },
			};
		}

		const consumerGroup = 'domain-processors';

		try
		{
			const [normalInfo, highInfo] = await Promise.all([
				this.getStreamInfoForStream(this.redis, this.config.normalStreamName, consumerGroup),
				this.getStreamInfoForStream(this.redis, this.config.highPriorityStreamName, consumerGroup),
			]);

			return {
				normal: normalInfo,
				high: highInfo,
			};
		}
		catch (error)
		{
			logger.error({ error: error.message, msg: 'Failed to get stream info' });
			return {
				normal: { length: 0, pending: 0, consumers: 0 },
				high: { length: 0, pending: 0, consumers: 0 },
			};
		}
	}

	private async getStreamInfoForStream(
		redis: RedisClientWrapper,
		streamName: string,
		consumerGroup: string,
	): Promise<{ length: number; pending: number; consumers: number }>
	{
		try
		{
			const client = redis.getClient();
			if (!client) {
				return { length: 0, pending: 0, consumers: 0 };
			}

			const [streamInfo, groupInfo] = await Promise.all([
				client.xInfoStream(streamName).catch(() => null),
				client.xInfoGroups(streamName).catch(() => []),
			]);

			const length = streamInfo?.length || 0;
			const groupData = Array.isArray(groupInfo) ? groupInfo.find(g => g.name === consumerGroup) : null;
			const pending = groupData?.pending || 0;
			const consumers = groupData?.consumers || 0;

			return { length, pending, consumers };
		}
		catch (error)
		{
			return { length: 0, pending: 0, consumers: 0 };
		}
	}

	/**
	 * Clean up old messages from streams (for maintenance)
	 */
	async trimStreams(maxLength: number = 1000000): Promise<void>
	{
		const client = this.redis.getClient();
		if (!client)
		{
			return;
		}

		try
		{
			const streams = [this.config.normalStreamName, this.config.highPriorityStreamName];

			for (const streamName of streams)
			{
				const trimmed = await client.xTrim(streamName, 'MAXLEN', maxLength);
				if (trimmed > 0)
				{
					logger.info({ msg: 'Stream trimmed',
						streamName,
						trimmedMessages: trimmed,
						maxLength,
					});
				}
			}
		}
		catch (error)
		{
			logger.error({ msg: 'Failed to trim streams', error: error.message });
		}
	}

	/**
	 * Get provenance data for a domain using ProvenanceTracker
	 */
	async getProvenanceData(domain: string): Promise<Record<string, unknown> | null>
	{
		try
		{
			// Use ProvenanceTracker if available, otherwise fallback to legacy method
			if (this.provenanceTracker)
			{
				return await this.provenanceTracker.getProvenanceRecord(domain);
			}

			// Legacy fallback method
			const provenanceKey = `provenance:${domain}`;
			const data = await this.redis.get<string>(provenanceKey);

			if (data && typeof data === 'string')
			{
				return JSON.parse(data);
			}

			return null;
		}
		catch (error)
		{
			logger.error({ msg: 'Failed to get provenance data',
				domain,
				error: error.message,
			});
			return null;
		}
	}

	/**
	 * Update daily enqueue count (for rate limiting)
	 */
	private async updateDailyCount(count: number): Promise<void>
	{
		const today = new Date().toISOString().split('T')[0];
		const dailyCountKey = `daily_enqueue_count:${today}`;

		try
		{
			await this.redis.incr(dailyCountKey, count);
			await this.redis.expire(dailyCountKey, 86400); // 24 hours TTL
		}
		catch (error)
		{
			logger.error({ msg: 'Failed to update daily count', error: error.message });
		}
	}

	/**
	 * Count words in a text string
	 */
	private countWords(text: string): number
	{
		if (!text || text.trim().length === 0)
		{
			return 0;
		}
		// Split by whitespace and filter out empty strings
		return text.trim().split(/\s+/).filter(word => word.length > 0).length;
	}

	/**
	 * Enhanced batch processing with better error handling and metrics
	 */
	private async processBatchEnhanced(domains: DiscoveredDomain[]): Promise<number>
	{
		let enqueuedCount = 0;
		const batchStartTime = Date.now();

		// Filter out already discovered domains
		const newDomains: DiscoveredDomain[] = [];
		for (const domain of domains)
		{
			if (await this.isDomainAlreadyDiscovered(domain.domain))
			{
				this.metrics.duplicatesSkipped++;
				continue;
			}
			newDomains.push(domain);
		}

		if (newDomains.length === 0)
		{
			return 0;
		}

		// Generate content for domains in parallel
		const contentPromises = newDomains.map(async (domain) =>
		{
			const contentResult = await this.generatePreGeneratedContent(domain);
			return { domain, contentResult };
		});

		const contentResults = await Promise.allSettled(contentPromises);
		const domainsWithContent: Array<DiscoveredDomain & { content?: GeneratedContent }> = [];

		for (const result of contentResults)
		{
			if (result.status === 'fulfilled')
			{
				const { domain, contentResult } = result.value;
				if (contentResult.success)
				{
					domainsWithContent.push({
						...domain,
						content: contentResult.content,
					});
				}
				else
				{
					this.metrics.contentGenerationFailures++;
					logger.warn({ msg: 'Content generation failed for domain',
						domain: domain.domain,
						error: contentResult.error,
					});
				}
			}
			else
			{
				this.metrics.contentGenerationFailures++;
				logger.error({ msg: 'Content generation promise failed',
					error: result.reason,
				});
			}
		}

		// Enqueue domains with content in parallel
		const enqueuePromises = domainsWithContent.map(async (domain) =>
		{
			try
			{
				await this.enqueueSingleDomain(domain);
				await this.markDomainAsDiscovered(domain.domain);
				return { success: true, domain: domain.domain };
			}
			catch (error)
			{
				logger.error({
					msg: 'Failed to enqueue domain',
					domain: domain.domain,
					error: error.message,
				});
				return { success: false, domain: domain.domain, error };
			}
		});

		const enqueueResults = await Promise.allSettled(enqueuePromises);

		for (const result of enqueueResults)
		{
			if (result.status === 'fulfilled' && result.value.success)
			{
				enqueuedCount++;
				this.metrics.enqueuedTotal++;
			}
		}

		// Update daily count
		if (enqueuedCount > 0)
		{
			await this.updateDailyCount(enqueuedCount);
		}

		const batchDuration = Date.now() - batchStartTime;
		logger.debug({ msg: 'Batch processed',
			totalDomains: domains.length,
			newDomains: newDomains.length,
			enqueuedCount,
			batchDuration,
		});

		return enqueuedCount;
	}

	private sleep(ms: number): Promise<void>
	{
		return new Promise(resolve => setTimeout(resolve, ms));
	}
}

export type {
	DiscoveredDomain,
	EnqueuedDomain,
	EnqueueConfig,
	EnqueueMetrics,
	ContentGenerationResult,
};

export default RateLimitedDomainEnqueuer;
