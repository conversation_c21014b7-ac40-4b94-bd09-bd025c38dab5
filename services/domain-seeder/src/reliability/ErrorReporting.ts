import { logger, type LoggerInstanceType } from '@shared';
import type { ErrorContext, ErrorStats } from './ErrorHandler';

type AlertDataType =
{
	ruleId: string;
	ruleName: string;
	severity: string;
	timestamp: Date;
	context: ErrorContext;
	stats: ErrorStats;
	message: string;
};

type ErrorReportType =
{
	id: string;
	timestamp: Date;
	severity: 'low' | 'medium' | 'high' | 'critical';
	title: string;
	description: string;
	context: ErrorContext;
	error: {
		name: string;
		message: string;
		stack?: string;
	};
	affectedComponents: string[];
	suggestedActions: string[];
	metadata: Record<string, any>;
};

type AlertRuleType =
{
	id: string;
	name: string;
	description: string;
	condition: (stats: ErrorStats, context: ErrorContext) => boolean;
	severity: 'low' | 'medium' | 'high' | 'critical';
	cooldownMs: number;
	lastTriggered?: Date;
};

type AlertChannelType =
{
	name: string;
	type: 'log' | 'webhook' | 'email' | 'slack';
	config: Record<string, any>;
	enabled: boolean;
};

class ErrorReportingSystem
{
	private logger: LoggerInstanceType;

	private reports: Map<string, ErrorReportType>;

	private alertRules: AlertRuleType[];

	private alertChannels: AlertChannelType[];

	private maxReports: number;

	private reportRetentionMs: number;

	constructor(maxReports = 1000, reportRetentionMs = 7 * 24 * 60 * 60 * 1000)
	{
		this.logger = logger.getLogger('ErrorReporting');
		this.reports = new Map();
		this.alertRules = [];
		this.alertChannels = [];
		this.maxReports = maxReports;
		this.reportRetentionMs = reportRetentionMs;

		this.initializeDefaultAlertRules();
		this.initializeDefaultChannels();
		this.startCleanupTimer();
	}

	async generateReport(
		error: Error,
		context: ErrorContext,
		severity: 'low' | 'medium' | 'high' | 'critical' = 'medium',
	): Promise<ErrorReportType>
	{
		const reportId = this.generateReportId(error, context);

		const report: ErrorReportType = {
			id: reportId,
			timestamp: new Date(),
			severity,
			title: this.generateTitle(error, context),
			description: this.generateDescription(error, context),
			context,
			error: {
				name: error.name,
				message: error.message,
				stack: error.stack,
			},
			affectedComponents: this.identifyAffectedComponents(error, context),
			suggestedActions: this.generateSuggestedActions(error, context),
			metadata: this.extractMetadata(error, context),
		};

		this.reports.set(reportId, report);
		this.trimReports();

		this.logger.error('Error report generated', {
			reportId,
			severity,
			title: report.title,
			component: context.component,
			operation: context.operation,
			correlationId: context.correlationId,
		});

		return report;
	}

	async checkAlertRules(stats: ErrorStats, context: ErrorContext): Promise<void>
	{
		const now = new Date();

		for (const rule of this.alertRules)
		{
			// Check cooldown
			if (rule.lastTriggered &&
				now.getTime() - rule.lastTriggered.getTime() < rule.cooldownMs)
			{
				continue;
			}

			// Check condition
			if (rule.condition(stats, context))
			{
				rule.lastTriggered = now;
				await this.triggerAlert(rule, stats, context);
			}
		}
	}

	async triggerAlert(rule: AlertRuleType, stats: ErrorStats, context: ErrorContext): Promise<void>
	{
		const alert = {
			ruleId: rule.id,
			ruleName: rule.name,
			severity: rule.severity,
			timestamp: new Date(),
			context,
			stats,
			message: this.generateAlertMessage(rule, stats, context),
		};

		this.logger.warn('Alert triggered', {
			ruleId: rule.id,
			ruleName: rule.name,
			severity: rule.severity,
			component: context.component,
			correlationId: context.correlationId,
		});

		// Send to all enabled channels
		for (const channel of this.alertChannels.filter(c => c.enabled))
		{
			try
			{
				await this.sendAlert(alert, channel);
			}
			catch (error)
			{
				this.logger.error('Failed to send alert', {
					channel: channel.name,
					error: error instanceof Error ? error.message : String(error),
					ruleId: rule.id,
				});
			}
		}
	}

	getReports(
		filters?: {
			severity?: string;
			component?: string;
			since?: Date;
			limit?: number;
		},
	): ErrorReportType[]
	{
		let reports = Array.from(this.reports.values());

		if (filters)
		{
			if (filters.severity)
			{
				reports = reports.filter(r => r.severity === filters.severity);
			}

			if (filters.component)
			{
				reports = reports.filter(r => r.context.component === filters.component);
			}

			if (filters.since)
			{
				reports = reports.filter(r => r.timestamp >= filters.since!);
			}

			if (filters.limit)
			{
				reports = reports.slice(0, filters.limit);
			}
		}

		return reports.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
	}

	getReport(reportId: string): ErrorReportType | null
	{
		return this.reports.get(reportId) || null;
	}

	addAlertRule(rule: AlertRuleType): void
	{
		this.alertRules.push(rule);
	}

	removeAlertRule(ruleId: string): boolean
	{
		const index = this.alertRules.findIndex(r => r.id === ruleId);
		if (index >= 0)
		{
			this.alertRules.splice(index, 1);
			return true;
		}
		return false;
	}

	addAlertChannel(channel: AlertChannelType): void
	{
		this.alertChannels.push(channel);
	}

	removeAlertChannel(channelName: string): boolean
	{
		const index = this.alertChannels.findIndex(c => c.name === channelName);
		if (index >= 0)
		{
			this.alertChannels.splice(index, 1);
			return true;
		}
		return false;
	}

	private initializeDefaultAlertRules(): void
	{
		this.alertRules = [
			{
				id: 'high-error-rate',
				name: 'High Error Rate',
				description: 'Triggered when error rate exceeds threshold',
				condition: (stats, context) => stats.recentErrors > 10,
				severity: 'high',
				cooldownMs: 5 * 60 * 1000, // 5 minutes
			},
			{
				id: 'critical-component-failure',
				name: 'Critical Component Failure',
				description: 'Triggered when critical components fail',
				condition: (stats, context) =>
				{
					const criticalComponents = ['DatabaseManager', 'RedisClient', 'ScyllaClient'];
					return criticalComponents.some(comp => (stats.errorsByComponent[comp] || 0) > 0);
				},
				severity: 'critical',
				cooldownMs: 2 * 60 * 1000, // 2 minutes
			},
			{
				id: 'validation-failure-spike',
				name: 'Validation Failure Spike',
				description: 'Triggered when validation failures spike',
				condition: (stats, context) => (stats.errorsByType.ValidationError || 0) > 5,
				severity: 'medium',
				cooldownMs: 10 * 60 * 1000, // 10 minutes
			},
			{
				id: 'zero-discoveries',
				name: 'Zero Discoveries',
				description: 'Triggered when no new domains discovered for extended period',
				condition: (stats, context) => context.operation.includes('discover') && stats.recentErrors === 0 &&
					context.metadata?.discoveredCount === 0,
				severity: 'medium',
				cooldownMs: 60 * 60 * 1000, // 1 hour
			},
			{
				id: 'queue-depth-critical',
				name: 'Queue Depth Critical',
				description: 'Triggered when queue depth exceeds critical threshold',
				condition: (stats, context) => context.metadata?.queueDepth && context.metadata.queueDepth > 100000,
				severity: 'high',
				cooldownMs: 15 * 60 * 1000, // 15 minutes
			},
		];
	}

	private initializeDefaultChannels(): void
	{
		this.alertChannels = [
			{
				name: 'console-log',
				type: 'log',
				config: { level: 'error' },
				enabled: true,
			},
			{
				name: 'webhook',
				type: 'webhook',
				config: {
					url: process.env.ALERT_WEBHOOK_URL || '',
					timeout: 5000,
				},
				enabled: Boolean(process.env.ALERT_WEBHOOK_URL),
			},
		];
	}

	private generateReportId(error: Error, context: ErrorContext): string
	{
		const hash = this.simpleHash(`${error.name}-${context.component}-${context.operation}`);
		return `error-${Date.now()}-${hash}`;
	}

	private generateTitle(error: Error, context: ErrorContext): string
	{
		return `${error.name} in ${context.component} during ${context.operation}`;
	}

	private generateDescription(error: Error, context: ErrorContext): string
	{
		return `Error occurred in component "${context.component}" while performing operation "${context.operation}". Error: ${error.message}`;
	}

	private identifyAffectedComponents(error: Error, context: ErrorContext): string[]
	{
		const components = [context.component];

		// Add related components based on context
		if (context.component.includes('Repository'))
		{
			components.push('DatabaseManager');
		}

		if (context.component.includes('Connector'))
		{
			components.push('DiscoveryEngine');
		}

		if (context.operation.includes('enqueue'))
		{
			components.push('RateLimitedDomainEnqueuer');
		}

		return [...new Set(components)];
	}

	private generateSuggestedActions(error: Error, context: ErrorContext): string[]
	{
		const actions: string[] = [];

		// Generic actions based on error type
		if (error.name === 'ValidationError')
		{
			actions.push('Check data format and schema compliance');
			actions.push('Review input validation rules');
		}

		if (error.message.includes('ECONNREFUSED') || error.message.includes('ENOTFOUND'))
		{
			actions.push('Check network connectivity');
			actions.push('Verify service endpoints are accessible');
			actions.push('Check DNS resolution');
		}

		if (error.message.includes('timeout'))
		{
			actions.push('Increase timeout values');
			actions.push('Check service performance');
			actions.push('Consider implementing circuit breaker');
		}

		// Component-specific actions
		if (context.component.includes('Database'))
		{
			actions.push('Check database connection pool');
			actions.push('Verify database service health');
			actions.push('Review query performance');
		}

		if (context.component.includes('Redis'))
		{
			actions.push('Check Redis connection');
			actions.push('Verify Redis memory usage');
			actions.push('Check Redis configuration');
		}

		// Default actions if none specific
		if (actions.length === 0)
		{
			actions.push('Review error logs for more details');
			actions.push('Check system resources');
			actions.push('Verify service configuration');
		}

		return actions;
	}

	private extractMetadata(error: Error, context: ErrorContext): Record<string, any>
	{
		return {
			errorType: error.name,
			hasStack: Boolean(error.stack),
			contextMetadata: context.metadata || {},
			timestamp: context.timestamp,
			correlationId: context.correlationId,
		};
	}

	private generateAlertMessage(rule: AlertRuleType, stats: ErrorStats, context: ErrorContext): string
	{
		return `Alert: ${rule.name} - ${rule.description}. Recent errors: ${stats.recentErrors}, Daily errors: ${stats.dailyErrors}. Component: ${context.component}`;
	}

	private async sendAlert(alert: AlertDataType, channel: AlertChannelType): Promise<void>
	{
		switch (channel.type)
		{
			case 'log':
				this.logger.error('ALERT', alert);
				break;

			case 'webhook':
				if (channel.config.url)
				{
					// In a real implementation, you would use fetch or axios
					// For now, just log that we would send a webhook
					this.logger.info('Would send webhook alert', {
						url: channel.config.url,
						alert: alert.message,
					});
				}
				break;

			case 'email':
				// Email implementation would go here
				this.logger.info('Would send email alert', {
					to: channel.config.to,
					subject: `Alert: ${alert.ruleName}`,
					message: alert.message,
				});
				break;

			case 'slack':
				// Slack implementation would go here
				this.logger.info('Would send Slack alert', {
					channel: channel.config.channel,
					message: alert.message,
				});
				break;

			default:
				this.logger.warn('Unknown alert channel type', { type: channel.type });
		}
	}

	private trimReports(): void
	{
		if (this.reports.size > this.maxReports)
		{
			const sortedReports = Array.from(this.reports.entries())
				.sort(([, a], [, b]) => a.timestamp.getTime() - b.timestamp.getTime());

			const toRemove = sortedReports.slice(0, this.reports.size - this.maxReports);
			for (const [id] of toRemove)
			{
				this.reports.delete(id);
			}
		}
	}

	private startCleanupTimer(): void
	{
		setInterval(() =>
		{
			const now = new Date();
			const cutoff = new Date(now.getTime() - this.reportRetentionMs);

			for (const [id, report] of this.reports.entries())
			{
				if (report.timestamp < cutoff)
				{
					this.reports.delete(id);
				}
			}
		}, 60 * 60 * 1000); // Run cleanup every hour
	}

	private simpleHash(str: string): string
	{
		let hash = 0;
		for (let i = 0; i < str.length; i++)
		{
			const char = str.charCodeAt(i);
			hash = ((hash << 5) - hash) + char;
			hash &= hash; // Convert to 32-bit integer
		}
		return Math.abs(hash).toString(36);
	}
}

export type { ErrorReportType, AlertRuleType, AlertChannelType };

export default ErrorReportingSystem;
