
/* eslint-disable max-classes-per-file */

import { 
	BaseError<PERSON>and<PERSON>, 
	type LoggerInstanceType,
	type ErrorHandlingContextType,
	type ErrorClassificationResultType 
} from '@shared';
import type { ValidationResultType } from '../interfaces/ReliabilityManager';

type ErrorCategoryType =
{
	name: string;
	description: string;
	severity: 'low' | 'medium' | 'high' | 'critical';
	isTransient: boolean;
	maxRetries: number;
	baseDelayMs: number;
	maxDelayMs: number;
};

type ErrorContextType =
{
	operation: string;
	stage: string;
	component: string;
	metadata?: Record<string, any>;
	timestamp: Date;
	correlationId?: string;
};

type RetryConfigType =
{
	maxRetries: number;
	baseDelayMs: number;
	maxDelayMs: number;
	backoffMultiplier: number;
	jitterMs: number;
	retryableErrors: string[];
};

type ErrorHandlingResultType =
{
	success: boolean;
	error?: Error;
	retryCount: number;
	totalDelayMs: number;
	recoveryStrategy?: string;
	degradationApplied?: string;
};

type RecoveryResultType =
{
	bypassCache: true;
	reason: string;
	fallbackMode: true;
} | {
	partialResult: true;
	data: unknown[];
	warning: string;
	totalExpected: number;
	actualReturned: number;
} | {
	fallbackMode: true;
	data: unknown[];
	source: string;
	warning: string;
	originalError: string;
} | {
	skipped: true;
	reason: string;
	operation: string;
	canContinue: boolean;
};

type GracefulDegradationStrategyType =
{
	name: string;
	description: string;
	canApply: (error: Error, context: ErrorContextType) => boolean;
	apply: (error: Error, context: ErrorContextType) => Promise<RecoveryResultType>;
	priority: number;
};

type ErrorRecord =
{
	error: {
		name: string;
		message: string;
		stack?: string;
	};
	context: ErrorContextType;
	timestamp: Date;
	count: number;
};

type ErrorStats =
{
	totalErrors: number;
	recentErrors: number;
	dailyErrors: number;
	errorsByComponent: Record<string, number>;
	errorsByType: Record<string, number>;
	topErrors: Array<{ error: string; count: number }>;
};

type CircuitBreakerConfig =
{
	failureThreshold: number;
	recoveryTimeoutMs: number;
	monitoringPeriodMs: number;
};

class ErrorHandler
{
	private logger: ReturnType<typeof logger.getLogger>;

	private errorCategories: Map<string, ErrorCategoryType>;

	private degradationStrategies: GracefulDegradationStrategyType[];

	private errorAggregator: ErrorAggregator;

	private circuitBreakers: Map<string, CircuitBreaker>;

	constructor(logger: ReturnType<typeof logger.getLogger>)
	{
		this.logger = logger;
		this.errorCategories = new Map();
		this.degradationStrategies = [];
		this.errorAggregator = new ErrorAggregator(logger);
		this.circuitBreakers = new Map();

		this.initializeErrorCategories();
		this.initializeDegradationStrategies();
	}

	async handleError<T>(
		operation: () => Promise<T>,
		context: ErrorContextType,
		retryConfig?: Partial<RetryConfigType>,
	): Promise<ErrorHandlingResultType & { result?: T }>
	{
		const config = this.getRetryConfig(context, retryConfig);
		let lastError: Error | null = null;
		let retryCount = 0;
		let totalDelayMs = 0;

		// Check circuit breaker
		const circuitBreaker = this.getCircuitBreaker(context.component);
		if (circuitBreaker.isOpen())
		{
			const error = new Error(`Circuit breaker open for ${context.component}`);
			await this.errorAggregator.recordError(error, context);
			return {
				success: false,
				error,
				retryCount: 0,
				totalDelayMs: 0,
			};
		}

		while (retryCount <= config.maxRetries)
		{
			try
			{
				const result = await operation();
				circuitBreaker.recordSuccess();

				if (retryCount > 0)
				{
					this.logger.info('Operation succeeded after retries', {
						operation: context.operation,
						retryCount,
						totalDelayMs,
						correlationId: context.correlationId,
					});
				}

				return {
					success: true,
					result,
					retryCount,
					totalDelayMs,
				};
			}
			catch (error)
			{
				lastError = error as Error;
				circuitBreaker.recordFailure();

				// Record error for aggregation
				await this.errorAggregator.recordError(lastError, context);

				// Check if error is retryable
				if (!this.isRetryableError(lastError, config))
				{
					this.logger.error('Non-retryable error encountered', {
						error: lastError.message,
						operation: context.operation,
						correlationId: context.correlationId,
					});
					break;
				}

				// Check if we've exhausted retries
				if (retryCount >= config.maxRetries)
				{
					this.logger.error('Max retries exceeded', {
						error: lastError.message,
						operation: context.operation,
						retryCount,
						correlationId: context.correlationId,
					});
					break;
				}

				// Calculate delay with exponential backoff and jitter
				const delay = this.calculateDelay(retryCount, config);
				totalDelayMs += delay;

				this.logger.warn('Operation failed, retrying', {
					error: lastError.message,
					operation: context.operation,
					retryCount: retryCount + 1,
					delayMs: delay,
					correlationId: context.correlationId,
				});

				await this.sleep(delay);
				retryCount++;
			}
		}

		// All retries exhausted, try graceful degradation only for specific error types
		if (this.shouldAttemptGracefulDegradation(lastError!, context))
		{
			const degradationResult = await this.attemptGracefulDegradation(lastError!, context);

			return {
				success: degradationResult.success,
				error: degradationResult.success ? undefined : lastError!,
				result: degradationResult.result,
				retryCount,
				totalDelayMs,
				degradationApplied: degradationResult.strategyName,
			};
		}

		// No graceful degradation attempted or available
		return ({
			success: false,
			error: lastError!,
			retryCount,
			totalDelayMs,
		});
	}

	async handleValidationError(
		validationResult: ValidationResultType,
		context: ErrorContextType,
	): Promise<ErrorHandlingResultType>
	{
		if (validationResult.isValid)
		{
			return ({
				success: true,
				retryCount: 0,
				totalDelayMs: 0,
			});
		}

		// Create composite error from validation errors
		const error = new ValidationError(validationResult.errors, context);
		await this.errorAggregator.recordError(error, context);

		// Try graceful degradation for validation errors only if appropriate
		if (this.shouldAttemptGracefulDegradation(error, context))
		{
			const degradationResult = await this.attemptGracefulDegradation(error, context);

			return {
				success: degradationResult.success,
				error: degradationResult.success ? undefined : error,
				retryCount: 0,
				totalDelayMs: 0,
				degradationApplied: degradationResult.strategyName,
			};
		}

		return ({
			success: false,
			error,
			retryCount: 0,
			totalDelayMs: 0,
		});
	}

	private initializeErrorCategories(): void
	{
		const categories: ErrorCategoryType[] =
		[
			{
				name: 'network',
				description: 'Network connectivity and timeout errors',
				severity: 'medium',
				isTransient: true,
				maxRetries: 3,
				baseDelayMs: 1000,
				maxDelayMs: 30000,
			},
			{
				name: 'database',
				description: 'Database connection and query errors',
				severity: 'high',
				isTransient: true,
				maxRetries: 5,
				baseDelayMs: 2000,
				maxDelayMs: 60000,
			},
			{
				name: 'validation',
				description: 'Data validation and format errors',
				severity: 'medium',
				isTransient: false,
				maxRetries: 0,
				baseDelayMs: 0,
				maxDelayMs: 0,
			},
			{
				name: 'rate_limit',
				description: 'Rate limiting and quota exceeded errors',
				severity: 'medium',
				isTransient: true,
				maxRetries: 3,
				baseDelayMs: 5000,
				maxDelayMs: 300000,
			},
			{
				name: 'authentication',
				description: 'Authentication and authorization errors',
				severity: 'high',
				isTransient: false,
				maxRetries: 1,
				baseDelayMs: 1000,
				maxDelayMs: 5000,
			},
			{
				name: 'resource',
				description: 'Resource exhaustion and capacity errors',
				severity: 'high',
				isTransient: true,
				maxRetries: 2,
				baseDelayMs: 10000,
				maxDelayMs: 120000,
			},
			{
				name: 'system',
				description: 'System-level errors and crashes',
				severity: 'critical',
				isTransient: false,
				maxRetries: 0,
				baseDelayMs: 0,
				maxDelayMs: 0,
			},
		];

		for (const category of categories)
		{
			this.errorCategories.set(category.name, category);
		}
	}

	private initializeDegradationStrategies(): void
	{
		this.degradationStrategies = [
			new CacheBypassStrategy(),
			new PartialResultStrategy(),
			new FallbackDataStrategy(),
			new SkipNonCriticalStrategy(),
			new EmptyResultStrategy(),
		].sort((a, b) => a.priority - b.priority);
	}

	private getRetryConfig(
		context: ErrorContextType,
		override?: Partial<RetryConfigType>,
	): RetryConfigType
	{
		const category = this.categorizeError(context);
		const baseConfig: RetryConfigType =
		{
			maxRetries: category?.maxRetries ?? 3,
			baseDelayMs: category?.baseDelayMs ?? 1000,
			maxDelayMs: category?.maxDelayMs ?? 30000,
			backoffMultiplier: 2,
			jitterMs: 500,
			retryableErrors: [
				'ECONNRESET',
				'ENOTFOUND',
				'ETIMEDOUT',
				'ECONNREFUSED',
				'NETWORK_ERROR',
				'TIMEOUT',
				'RATE_LIMITED',
				'SERVICE_UNAVAILABLE',
			],
		};

		return ({ ...baseConfig, ...override });
	}

	private categorizeError(context: ErrorContextType): ErrorCategoryType | null
	{
		// Try to categorize based on operation and component
		if (context.operation.includes('database') || context.component.includes('Repository'))
		{
			return this.errorCategories.get('database') || null;
		}

		if (context.operation.includes('fetch') || context.operation.includes('request'))
		{
			return this.errorCategories.get('network') || null;
		}

		if (context.operation.includes('validate'))
		{
			return this.errorCategories.get('validation') || null;
		}

		return null;
	}

	private isRetryableError(error: Error, config: RetryConfigType): boolean
	{
		const errorMessage = error.message.toUpperCase();
		const errorName = error.name.toUpperCase();

		return config
			.retryableErrors
			.some(retryableError => (
				errorMessage.includes(retryableError) ||
				errorName.includes(retryableError)
			));
	}

	private calculateDelay(retryCount: number, config: RetryConfigType): number
	{
		const exponentialDelay = config.baseDelayMs * (config.backoffMultiplier ** retryCount);
		const cappedDelay = Math.min(exponentialDelay, config.maxDelayMs);
		const jitter = Math.random() * config.jitterMs;

		return Math.floor(cappedDelay + jitter);
	}

	private async sleep(ms: number): Promise<void>
	{
		// eslint-disable-next-line no-promise-executor-return
		return new Promise(resolve => setTimeout(resolve, ms));
	}

	private shouldAttemptGracefulDegradation(error: Error, context: ErrorContextType): boolean
	{
		// Only attempt graceful degradation for specific error types and contexts
		const degradableErrors =
		[
			'NETWORK_ERROR',
			'TIMEOUT',
			'SERVICE_UNAVAILABLE',
			'ECONNRESET',
			'ENOTFOUND',
			'ETIMEDOUT',
		];

		const degradableOperations =
		[
			'fetch',
			'cache',
			'batch',
			'bulk',
			'metrics',
			'logging',
			'monitoring',
		];

		const errorMessage = error.message.toUpperCase();
		const errorName = error.name.toUpperCase();
		const operation = context.operation.toLowerCase();

		// Check if error type is degradable
		const isErrorDegradable = degradableErrors
			.some(degradableError => (
				errorMessage.includes(degradableError) || errorName.includes(degradableError)
			));

		// Check if operation is degradable
		const isOperationDegradable = degradableOperations
			.some(degradableOp => operation.includes(degradableOp));

		return isErrorDegradable || isOperationDegradable;
	}

	private async attemptGracefulDegradation(
		error: Error,
		context: ErrorContextType,
	): Promise<{ success: boolean; result?: RecoveryResultType; strategyName?: string }>
	{
		for (const strategy of this.degradationStrategies)
		{
			if (strategy.canApply(error, context))
			{
				try
				{
					this.logger.warn('Applying graceful degradation strategy', {
						strategy: strategy.name,
						error: error.message,
						operation: context.operation,
						correlationId: context.correlationId,
					});

					const result = await strategy.apply(error, context);

					this.logger.info('Graceful degradation successful', {
						strategy: strategy.name,
						operation: context.operation,
						correlationId: context.correlationId,
					});

					return {
						success: true,
						result,
						strategyName: strategy.name,
					};
				}
				catch (degradationError)
				{
					this.logger.error('Graceful degradation strategy failed', {
						strategy: strategy.name,
						error: degradationError.message,
						originalError: error.message,
						operation: context.operation,
						correlationId: context.correlationId,
					});
				}
			}
		}

		return { success: false };
	}

	private getCircuitBreaker(component: string): CircuitBreaker
	{
		if (!this.circuitBreakers.has(component))
		{
			this.circuitBreakers.set(component, new CircuitBreaker(component, {
				failureThreshold: 5,
				recoveryTimeoutMs: 60000,
				monitoringPeriodMs: 10000,
			}));
		}

		return this.circuitBreakers.get(component)!;
	}

	getErrorStats(): ErrorStats
	{
		return this.errorAggregator.getStats();
	}

	addDegradationStrategy(strategy: GracefulDegradationStrategyType): void
	{
		this.degradationStrategies.push(strategy);
		this.degradationStrategies.sort((a, b) => a.priority - b.priority);
	}
}

class ValidationError extends Error
{
	public readonly validationErrors: string[];

	public readonly context: ErrorContextType;

	constructor(validationErrors: string[], context: ErrorContextType)
	{
		super(`Validation failed: ${validationErrors.join(', ')}`);
		this.name = 'ValidationError';
		this.validationErrors = validationErrors;
		this.context = context;
	}
}

class ErrorAggregator
{
	private logger: ReturnType<typeof logger.getLogger>;

	private errorCounts: Map<string, number>;

	private errorHistory: ErrorRecord[];

	private maxHistorySize: number;

	constructor(loggerInstance: ReturnType<typeof logger.getLogger>, maxHistorySize = 1000)
	{
		this.logger = loggerInstance;
		this.errorCounts = new Map();
		this.errorHistory = [];
		this.maxHistorySize = maxHistorySize;
	}

	async recordError(error: Error, context: ErrorContextType): Promise<void>
	{
		const errorKey = `${context.component}:${error.name}`;
		const currentCount = this.errorCounts.get(errorKey) || 0;
		this.errorCounts.set(errorKey, currentCount + 1);

		const record: ErrorRecord = {
			error: {
				name: error.name,
				message: error.message,
				stack: error.stack,
			},
			context,
			timestamp: new Date(),
			count: currentCount + 1,
		};

		this.errorHistory.push(record);

		// Trim history if it gets too large
		if (this.errorHistory.length > this.maxHistorySize)
		{
			this.errorHistory = this.errorHistory.slice(-this.maxHistorySize);
		}

		// Log aggregated error info
		this.logger.error('Error recorded', {
			errorType: error.name,
			message: error.message,
			component: context.component,
			operation: context.operation,
			count: currentCount + 1,
			correlationId: context.correlationId,
		});
	}

	getStats(): ErrorStats
	{
		const now = new Date();
		const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
		const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

		const recentErrors = this.errorHistory.filter(record => record.timestamp > oneHourAgo);
		const dailyErrors = this.errorHistory.filter(record => record.timestamp > oneDayAgo);

		const errorsByComponent = new Map<string, number>();
		const errorsByType = new Map<string, number>();

		for (const record of dailyErrors)
		{
			const component = record.context.component;
			const errorType = record.error.name;

			errorsByComponent.set(component, (errorsByComponent.get(component) || 0) + 1);
			errorsByType.set(errorType, (errorsByType.get(errorType) || 0) + 1);
		}

		return {
			totalErrors: this.errorHistory.length,
			recentErrors: recentErrors.length,
			dailyErrors: dailyErrors.length,
			errorsByComponent: Object.fromEntries(errorsByComponent),
			errorsByType: Object.fromEntries(errorsByType),
			topErrors: this.getTopErrors(10),
		};
	}

	private getTopErrors(limit: number): Array<{ error: string; count: number }>
	{
		return Array.from(this.errorCounts.entries())
			.map(([error, count]) => ({ error, count }))
			.sort((a, b) => b.count - a.count)
			.slice(0, limit);
	}
}

class CircuitBreaker
{
	private component: string;

	private failureCount: number;

	private lastFailureTime: Date | null;

	private state: 'closed' | 'open' | 'half-open';

	private config: CircuitBreakerConfig;

	constructor(component: string, config: CircuitBreakerConfig)
	{
		this.component = component;
		this.failureCount = 0;
		this.lastFailureTime = null;
		this.state = 'closed';
		this.config = config;
	}

	isOpen(): boolean
	{
		if (this.state === 'open')
		{
			// Check if recovery timeout has passed
			if (this.lastFailureTime &&
				Date.now() - this.lastFailureTime.getTime() > this.config.recoveryTimeoutMs)
			{
				this.state = 'half-open';
				return false;
			}
			return true;
		}

		return false;
	}

	recordSuccess(): void
	{
		this.failureCount = 0;
		this.state = 'closed';
		this.lastFailureTime = null;
	}

	recordFailure(): void
	{
		this.failureCount++;
		this.lastFailureTime = new Date();

		if (this.failureCount >= this.config.failureThreshold)
		{
			this.state = 'open';
		}
	}

	getState(): { state: string; failureCount: number; lastFailureTime: Date | null }
	{
		return {
			state: this.state,
			failureCount: this.failureCount,
			lastFailureTime: this.lastFailureTime,
		};
	}
}

// Graceful Degradation Strategies
class CacheBypassStrategy implements GracefulDegradationStrategy
{
	name = 'CacheBypass';

	description = 'Bypass cache and go directly to source';

	priority = 1;

	canApply(error: Error, context: ErrorContextType): boolean
	{
		return context.operation.includes('cache') ||
			context.component.includes('Cache') ||
			error.message.includes('cache');
	}

	async apply(error: Error, context: ErrorContextType): Promise<RecoveryResultType>
	{
		// Return a marker indicating cache should be bypassed
		return {
			bypassCache: true,
			reason: 'Cache error encountered',
			fallbackMode: true,
		};
	}
}

class PartialResultStrategy implements GracefulDegradationStrategy
{
	name = 'PartialResult';

	description = 'Return partial results when full processing fails';

	priority = 2;

	canApply(error: Error, context: ErrorContextType): boolean
	{
		return context.operation.includes('batch') ||
			context.operation.includes('bulk') ||
			context.metadata?.partialDataAvailable === true;
	}

	async apply(error: Error, context: ErrorContextType): Promise<RecoveryResultType>
	{
		// Return partial data if available in context
		return {
			partialResult: true,
			data: context.metadata?.partialData || [],
			warning: 'Partial results due to processing error',
			totalExpected: context.metadata?.totalExpected || 0,
			actualReturned: context.metadata?.partialData?.length || 0,
		};
	}
}

class FallbackDataStrategy implements GracefulDegradationStrategy
{
	name = 'FallbackData';

	description = 'Use fallback data source when primary fails';

	priority = 3;

	canApply(error: Error, context: ErrorContextType): boolean
	{
		return context.operation.includes('fetch') ||
			context.operation.includes('load') ||
			context.component.includes('Connector');
	}

	async apply(error: Error, context: ErrorContextType): Promise<RecoveryResultType>
	{
		// Return fallback data structure
		return {
			fallbackMode: true,
			data: [],
			source: 'fallback',
			warning: 'Using fallback data due to primary source failure',
			originalError: error.message,
		};
	}
}

class SkipNonCriticalStrategy implements GracefulDegradationStrategy
{
	name = 'SkipNonCritical';

	description = 'Skip non-critical operations when they fail';

	priority = 4;

	canApply(error: Error, context: ErrorContextType): boolean
	{
		const nonCriticalOperations = [
			'metrics',
			'logging',
			'monitoring',
			'analytics',
			'cache_warm',
		];

		return nonCriticalOperations.some(op => context.operation.includes(op));
	}

	async apply(error: Error, context: ErrorContextType): Promise<RecoveryResultType>
	{
		return {
			skipped: true,
			reason: 'Non-critical operation failed',
			operation: context.operation,
			canContinue: true,
		};
	}
}

class EmptyResultStrategy implements GracefulDegradationStrategy
{
	name = 'EmptyResult';

	description = 'Return empty result when all else fails';

	priority = 5; // Higher priority than fallback data for testing

	canApply(error: Error, context: ErrorContextType): boolean
	{
		// This strategy can always be applied as a last resort
		return true;
	}

	async apply(error: Error, context: ErrorContextType): Promise<unknown>
	{
		// Return appropriate empty structure based on operation
		if (context.operation.includes('fetch') || context.operation.includes('discover'))
		{
			return {
				candidates: [],
				domains: [],
				totalCount: 0,
				warning: 'Empty result due to system error',
			};
		}

		if (context.operation.includes('check') || context.operation.includes('validate'))
		{
			return ({
				results: new Map(),
				isValid: false,
				warning: 'Empty validation result due to system error',
			});
		}

		return {
			empty: true,
			warning: 'Empty result due to system error',
			originalError: error.message,
		};
	}
}

export type {
	ErrorCategoryType,
	ErrorContextType,
	RetryConfigType,
	ErrorHandlingResultType,
	GracefulDegradationStrategy,
	ErrorStats,
	CircuitBreakerConfig,
};
export {
	ValidationError,
	ErrorAggregator,
	CircuitBreaker,
};

export default ErrorHandler;
