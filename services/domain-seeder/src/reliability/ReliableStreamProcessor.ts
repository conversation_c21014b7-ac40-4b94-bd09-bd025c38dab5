/* eslint-disable max-classes-per-file */

import type { RedisClientWrapper } from '@shared';
import { logger } from '@shared';
import { randomUUID } from 'node:crypto';

import type { StreamProcessor, BackpressureController } from '../interfaces/ReliabilityManager';

type StreamMessage =
{
	id: string;
	fields: Record<string, string>;
};

type StreamProcessingOptions =
{
	maxRetries?: number;
	retryDelayMs?: number;
	processingTimeoutMs?: number;
	batchSize?: number;
	blockTimeMs?: number;
};

type DeadLetterMessage =
{
	originalMessageId: string;
	streamName: string;
	consumerGroup: string;
	error: string;
	retryCount: number;
	timestamp: string;
	originalFields: Record<string, string>;
};

/**
 * Reliable Redis Streams processor with consumer groups, ACK support,
 * retry logic with exponential backoff, and dead letter queue handling
 */
class ReliableStreamProcessor implements StreamProcessor
{
	private redis: RedisClientWrapper;

	private options: Required<StreamProcessingOptions>;

	private isProcessing = false;

	private processingPromises = new Map<string, Promise<void>>();

	private backpressureController?: BackpressureController;

	constructor(
		redis: RedisClientWrapper,
		options: StreamProcessingOptions = {},
		backpressureController?: BackpressureController,
	)
	{
		this.redis = redis;
		this.options = {
			maxRetries: options.maxRetries ?? 3,
			retryDelayMs: options.retryDelayMs ?? 1000,
			processingTimeoutMs: options.processingTimeoutMs ?? 300000, // 5 minutes
			batchSize: options.batchSize ?? 10,
			blockTimeMs: options.blockTimeMs ?? 1000,
		};
		this.backpressureController = backpressureController;
	}

	/**
	 * Process messages from a Redis Stream with consumer group
	 */
	async processStream(
		streamName: string,
		consumerGroup: string,
		messageHandler: (message: StreamMessage) => Promise<void>,
	): Promise<void>
	{
		const consumerName = this.generateConsumerName();
		const processingKey = `${streamName}:${consumerGroup}`;

		if (this.processingPromises.has(processingKey))
		{
			throw new Error(`Already processing stream ${streamName} with consumer group ${consumerGroup}`);
		}

		this.isProcessing = true;

		const processingPromise = this.processStreamInternal(
			streamName,
			consumerGroup,
			consumerName,
			messageHandler,
		);

		this.processingPromises.set(processingKey, processingPromise);

		try
		{
			await processingPromise;
		}
		finally
		{
			this.processingPromises.delete(processingKey);
			this.isProcessing = false;
		}
	}

	/**
	 * Stop processing all streams gracefully
	 */
	async stopProcessing(): Promise<void>
	{
		this.isProcessing = false;

		// Wait for all processing promises to complete
		await Promise.all(this.processingPromises.values());
	}

	/**
	 * Acknowledge a message as successfully processed
	 */
	async acknowledgeMessage(streamName: string, messageId: string): Promise<void>
	{
		const client = this.redis.getClient();
		if (!client)
		{
			throw new Error('Redis client not available');
		}

		try
		{
			await client.xAck(streamName, 'default', messageId);
		}
		catch (error)
		{
			throw new Error(`Failed to acknowledge message ${messageId}: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	/**
	 * Handle a failed message by moving it to dead letter queue or retrying
	 */
	async handleFailedMessage(
		streamName: string,
		messageId: string,
		error: Error,
		consumerGroup: string = 'default',
		originalFields: Record<string, string> = {},
	): Promise<void>
	{
		const retryKey = `retry:${streamName}:${messageId}`;
		const retryCount = await this.getRetryCount(retryKey);

		if (retryCount >= this.options.maxRetries)
		{
			// Move to dead letter queue
			await this.moveToDeadLetterQueue(
				streamName,
				messageId,
				consumerGroup,
				error,
				retryCount,
				originalFields,
			);

			// Clean up retry tracking
			await this.redis.del(retryKey);
		}
		else
		{
			// Schedule retry with exponential backoff
			await this.scheduleRetry(streamName, messageId, retryKey, retryCount, originalFields);
		}
	}

	/**
	 * Recover pending messages that may have been stuck due to consumer failures
	 */
	async recoverPendingMessages(
		streamName: string,
		consumerGroup: string,
		messageHandler: (message: StreamMessage) => Promise<void>,
	): Promise<number>
	{
		const client = this.redis.getClient();
		if (!client)
		{
			throw new Error('Redis client not available');
		}

		let recoveredCount = 0;

		try
		{
			// Get pending messages for all consumers in the group
			const pendingInfo = await client.xPending(streamName, consumerGroup);

			if (pendingInfo.count === 0)
			{
				return 0;
			}

			// Get detailed pending messages
			const pendingMessages = await client.xPendingRange(
				streamName,
				consumerGroup,
				'-',
				'+',
				Math.min(pendingInfo.count, 100), // Process up to 100 at a time
			);

			for (const pendingMessage of pendingMessages)
			{
				const messageId = pendingMessage.id;
				const idleTime = pendingMessage.millisecondsSinceLastDelivery;

				// Only recover messages that have been idle longer than processing timeout
				if (idleTime > this.options.processingTimeoutMs)
				{
					try
					{
						// Claim the message for this consumer
						const consumerName = this.generateConsumerName();
						const claimedMessages = await client.xClaim(
							streamName,
							consumerGroup,
							consumerName,
							this.options.processingTimeoutMs,
							messageId,
						);

						// Process claimed messages
						for (const claimedMessage of claimedMessages)
						{
							const streamMessage: StreamMessage = {
								id: claimedMessage.id,
								fields: claimedMessage.message as Record<string, string>,
							};

							try
							{
								await messageHandler(streamMessage);
								await this.acknowledgeMessage(streamName, claimedMessage.id);
								recoveredCount++;
							}
							catch (error)
							{
								await this.handleFailedMessage(
									streamName,
									claimedMessage.id,
									error instanceof Error ? error : new Error(String(error)),
									consumerGroup,
									streamMessage.fields,
								);
							}
						}
					}
					catch (error)
					{
						// Log error but continue with other messages
						logger.getLogger('ReliableStreamProcessor').error(`Failed to recover message ${messageId}:`, error);
					}
				}
			}
		}
		catch (error)
		{
			throw new Error(`Failed to recover pending messages: ${error instanceof Error ? error.message : String(error)}`);
		}

		return recoveredCount;
	}

	/**
	 * Get dead letter queue messages for inspection
	 */
	async getDeadLetterMessages(streamName: string, limit: number = 100): Promise<DeadLetterMessage[]>
	{
		const dlqKey = this.getDeadLetterQueueKey(streamName);
		const client = this.redis.getClient();

		if (!client)
		{
			throw new Error('Redis client not available');
		}

		try
		{
			const messages = await client.xRevRange(dlqKey, '+', '-', { COUNT: limit });
			return messages.map(msg => JSON.parse(msg.message.data as string) as DeadLetterMessage);
		}
		catch (error)
		{
			throw new Error(`Failed to get dead letter messages: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	/**
	 * Reprocess a message from the dead letter queue
	 */
	async reprocessDeadLetterMessage(
		streamName: string,
		dlqMessageId: string,
		messageHandler: (message: StreamMessage) => Promise<void>,
	): Promise<boolean>
	{
		const dlqKey = this.getDeadLetterQueueKey(streamName);
		const client = this.redis.getClient();

		if (!client)
		{
			throw new Error('Redis client not available');
		}

		try
		{
			// Get the dead letter message
			const messages = await client.xRange(dlqKey, dlqMessageId, dlqMessageId);

			if (messages.length === 0)
			{
				return false;
			}

			const dlqMessage: DeadLetterMessage = JSON.parse(messages[0].message.data as string);

			// Create a new stream message
			const streamMessage: StreamMessage = {
				id: dlqMessage.originalMessageId,
				fields: dlqMessage.originalFields,
			};

			// Try to process the message
			await messageHandler(streamMessage);

			// Remove from dead letter queue on success
			await client.xDel(dlqKey, dlqMessageId);

			return true;
		}
		catch (error)
		{
			throw new Error(`Failed to reprocess dead letter message: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	private async processStreamInternal(
		streamName: string,
		consumerGroup: string,
		consumerName: string,
		messageHandler: (message: StreamMessage) => Promise<void>,
	): Promise<void>
	{
		const client = this.redis.getClient();
		if (!client)
		{
			throw new Error('Redis client not available');
		}

		// Create consumer group if it doesn't exist
		try
		{
			await client.xGroupCreate(streamName, consumerGroup, '$', { MKSTREAM: true });
		}
		catch (error)
		{
			// Group already exists, continue
			if (!String(error).includes('BUSYGROUP'))
			{
				throw error;
			}
		}

		// First, recover any pending messages
		await this.recoverPendingMessages(streamName, consumerGroup, messageHandler);

		// Main processing loop
		while (this.isProcessing)
		{
			try
			{
				// Check backpressure if controller is available
				if (this.backpressureController)
				{
					if (await this.backpressureController.shouldThrottle())
					{
						await this.backpressureController.waitForCapacity();
						continue;
					}
				}

				// Read messages from stream
				const messages = await client.xReadGroup(
					consumerGroup,
					consumerName,
					[{ key: streamName, id: '>' }],
					{
						COUNT: this.options.batchSize,
						BLOCK: this.options.blockTimeMs,
					},
				);

				if (!messages || messages.length === 0)
				{
					continue;
				}

				// Process messages
				for (const stream of messages)
				{
					for (const message of stream.messages)
					{
						if (!this.isProcessing)
						{
							break;
						}

						const streamMessage: StreamMessage = {
							id: message.id,
							fields: message.message as Record<string, string>,
						};

						try
						{
							await messageHandler(streamMessage);
							await this.acknowledgeMessage(streamName, message.id);
						}
						catch (error)
						{
							await this.handleFailedMessage(
								streamName,
								message.id,
								error instanceof Error ? error : new Error(String(error)),
								consumerGroup,
								streamMessage.fields,
							);
						}
					}
				}
			}
			catch (error)
			{
				if (this.isProcessing)
				{
					logger.getLogger('ReliableStreamProcessor').error('Stream processing error:', error);
					// Wait before retrying to avoid tight error loops
					await this.sleep(this.options.retryDelayMs);
				}
			}
		}
	}

	private async getRetryCount(retryKey: string): Promise<number>
	{
		const count = await this.redis.get<number>(retryKey);
		return count ?? 0;
	}

	private async scheduleRetry(
		streamName: string,
		messageId: string,
		retryKey: string,
		currentRetryCount: number,
		originalFields: Record<string, string>,
	): Promise<void>
	{
		const newRetryCount = currentRetryCount + 1;
		const delayMs = this.calculateExponentialBackoff(newRetryCount);

		// Store retry count with TTL
		await this.redis.setex(retryKey, 3600, newRetryCount); // 1 hour TTL

		// Schedule retry by adding to a retry stream
		const retryStreamName = `${streamName}:retry`;
		const client = this.redis.getClient();

		if (!client)
		{
			throw new Error('Redis client not available');
		}

		const retryTime = Date.now() + delayMs;

		await client.xAdd(retryStreamName, '*', {
			originalMessageId: messageId,
			originalStream: streamName,
			retryCount: newRetryCount.toString(),
			retryTime: retryTime.toString(),
			...originalFields,
		});
	}

	private async moveToDeadLetterQueue(
		streamName: string,
		messageId: string,
		consumerGroup: string,
		error: Error,
		retryCount: number,
		originalFields: Record<string, string>,
	): Promise<void>
	{
		const dlqKey = this.getDeadLetterQueueKey(streamName);
		const client = this.redis.getClient();

		if (!client)
		{
			throw new Error('Redis client not available');
		}

		const deadLetterMessage: DeadLetterMessage = {
			originalMessageId: messageId,
			streamName,
			consumerGroup,
			error: error.message,
			retryCount,
			timestamp: new Date().toISOString(),
			originalFields,
		};

		await client.xAdd(dlqKey, '*', {
			data: JSON.stringify(deadLetterMessage),
		});

		// Acknowledge the original message to remove it from pending
		try
		{
			await this.acknowledgeMessage(streamName, messageId);
		}
		catch (ackError)
		{
			// Log but don't throw - message is already in DLQ
			logger.getLogger('ReliableStreamProcessor').error(`Failed to acknowledge message ${messageId} after moving to DLQ:`, ackError);
		}
	}

	private calculateExponentialBackoff(retryCount: number): number
	{
		const baseDelay = this.options.retryDelayMs;
		const maxDelay = 60000; // 1 minute max
		const jitter = Math.random() * 0.1; // 10% jitter

		const delay = Math.min(baseDelay * 2 ** (retryCount - 1), maxDelay);
		return Math.floor(delay * (1 + jitter));
	}

	private generateConsumerName(): string
	{
		const hostname = process.env.HOSTNAME || 'unknown';
		const pid = process.pid;
		const uuid = randomUUID().slice(0, 8);
		return `${hostname}-${pid}-${uuid}`;
	}

	private getDeadLetterQueueKey(streamName: string): string
	{
		return `${streamName}:dlq`;
	}

	private sleep(ms: number): Promise<void>
	{
		return new Promise(resolve => setTimeout(resolve, ms));
	}
}

/**
 * Backpressure controller implementation for Redis Streams
 */
class StreamBackpressureController implements BackpressureController
{
	private redis: RedisClientWrapper;

	private queueDepthThreshold: number;

	private latencyThreshold: number;

	private currentQueueDepth = 0;

	private currentLatency = 0;

	constructor(
		redis: RedisClientWrapper,
		queueDepthThreshold: number = 10000,
		latencyThreshold: number = 5000,
	)
	{
		this.redis = redis;
		this.queueDepthThreshold = queueDepthThreshold;
		this.latencyThreshold = latencyThreshold;
	}

	async shouldThrottle(): Promise<boolean>
	{
		return this.currentQueueDepth > this.queueDepthThreshold ||
			this.currentLatency > this.latencyThreshold;
	}

	async getQueueDepth(): Promise<number>
	{
		return this.currentQueueDepth;
	}

	async waitForCapacity(): Promise<void>
	{
		// eslint-disable-next-line no-await-in-loop
		while (await this.shouldThrottle())
		{
			// eslint-disable-next-line no-await-in-loop
			await this.sleep(1000); // Wait 1 second before checking again
		}
	}

	updateMetrics(queueDepth: number, latency: number): void
	{
		this.currentQueueDepth = queueDepth;
		this.currentLatency = latency;
	}

	private sleep(ms: number): Promise<void>
	{
		// eslint-disable-next-line no-promise-executor-return
		return new Promise(resolve => setTimeout(resolve, ms));
	}
}

export { StreamBackpressureController };

export default ReliableStreamProcessor;
