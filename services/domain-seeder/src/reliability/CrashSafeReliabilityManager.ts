/* eslint-disable max-classes-per-file */
import type { Logger, RedisClientWrapper } from '@shared';
import { randomUUID } from 'node:crypto';

import type {
	ValidationResultType,
	ReliabilityManagerInterface,
	CheckpointStoreInterface,
	CheckpointType,
} from '../interfaces/ReliabilityManager';
import type { ErrorContextTypeType, ErrorHandlingResultType, ErrorStats } from './ErrorHandler';
import ErrorHandler, { ValidationError } from './ErrorHandler';
import { ErrorReportingSystem } from './ErrorReporting';
import type { ErrorReportType } from './ErrorReporting';
import { ErrorRecoveryManager } from '../validation/ErrorRecovery';

type ReliabilityConfig =
{
	checkpointTtlMs: number;
	maxCheckpoints: number;
	enableErrorReporting: boolean;
	enableRecovery: boolean;
	correlationIdHeader: string;
};

type OperationContext =
{
	id: string;
	context: ErrorContextType;
	startTime: Date;
	endTime?: Date;
	success?: boolean;
	error?: Error;
	checkpoints: string[];
};

type HealthStatus =
{
	healthy: boolean;
	errorStats: ErrorStats;
	activeOperations: number;
	checkpointStoreHealthy: boolean;
	uptime: number;
	memoryUsage: NodeJS.MemoryUsage;
};

class CrashSafeReliabilityManager implements ReliabilityManagerInterface
{
	private logger: Logger;

	private redis: RedisClientWrapper;

	private errorHandler: ErrorHandler;

	private errorReporting: ErrorReportingSystem;

	private errorRecovery: ErrorRecoveryManager;

	private checkpointStore: RedisCheckpointStore;

	private config: ReliabilityConfig;

	private activeOperations: Map<string, OperationContext>;

	constructor(
		logger: Logger,
		redis: RedisClientWrapper,
		config: Partial<ReliabilityConfig> = {},
	)
	{
		this.logger = logger;
		this.redis = redis;
		this.config = {
			checkpointTtlMs: 24 * 60 * 60 * 1000, // 24 hours
			maxCheckpoints: 1000,
			enableErrorReporting: true,
			enableRecovery: true,
			correlationIdHeader: 'x-correlation-id',
			...config,
		};

		this.errorHandler = new ErrorHandler(logger);
		this.errorReporting = new ErrorReportingSystem(logger);
		this.errorRecovery = new ErrorRecoveryManager();
		this.checkpointStore = new RedisCheckpointStore(redis, this.config.checkpointTtlMs);
		this.activeOperations = new Map();

		this.setupGracefulShutdown();
	}

	async createCheckpoint(stage: string, data: unknown): Promise<string>
	{
		const context = this.createErrorContextType('createCheckpoint', stage, 'ReliabilityManager');

		return this.errorHandler.handleError(
			async () =>
			{
				const checkpointId = this.generateCheckpointId(stage);
				const checkpoint: CheckpointType =
				{
					id: checkpointId,
					stage,
					timestamp: new Date().toISOString(),
					data: JSON.stringify(data),
					hash: this.calculateHash(data),
				};

				await this.checkpointStore.store(checkpoint);

				this.logger.info('Checkpoint created', {
					checkpointId,
					stage,
					dataSize: checkpoint.data.length,
					correlationId: context.correlationId,
				});

				return checkpointId;
			},
			context,
		).then((result) =>
		{
			if (!result.success)
			{
				throw result.error || new Error('Failed to create checkpoint');
			}
			return result.result!;
		});
	}

	async resumeFromCheckpoint(checkpointId: string): Promise<unknown>
	{
		const context = this.createErrorContextType('resumeFromCheckpoint', 'recovery', 'ReliabilityManager', {
			checkpointId,
		});

		return this.errorHandler.handleError(
			async () =>
			{
				const checkpoint = await this.checkpointStore.retrieve(checkpointId);

				if (!checkpoint)
				{
					throw new Error(`Checkpoint not found: ${checkpointId}`);
				}

				// Verify checkpoint integrity
				const data = JSON.parse(checkpoint.data);
				const expectedHash = this.calculateHash(data);

				if (checkpoint.hash !== expectedHash)
				{
					throw new Error(`Checkpoint corruption detected: ${checkpointId}`);
				}

				this.logger.info('Resuming from checkpoint', {
					checkpointId,
					stage: checkpoint.stage,
					timestamp: checkpoint.timestamp,
					correlationId: context.correlationId,
				});

				return data;
			},
			context,
		).then((result) =>
		{
			if (!result.success)
			{
				throw result.error || new Error('Failed to resume from checkpoint');
			}
			return result.result!;
		});
	}

	async validatePipelineStage(stage: string, data: unknown): Promise<ValidationResultType>
	{
		const context = this.createErrorContextType('validatePipelineStage', stage, 'ValidationPipeline', {
			dataType: typeof data,
			hasData: Boolean(data),
		});

		try
		{
			// Perform stage-specific validation
			const validationResult = await this.performStageValidation(stage, data);

			if (!validationResult.isValid && this.config.enableRecovery)
			{
				// Attempt error recovery
				const recoveryResult = await this.errorRecovery.attemptRecovery(
					stage as any,
					data,
					validationResult,
				);

				if (recoveryResult.success && recoveryResult.recoveredData)
				{
					this.logger.info('Validation errors recovered', {
						stage,
						appliedStrategies: recoveryResult.appliedStrategies,
						remainingErrors: recoveryResult.remainingErrors.length,
						correlationId: context.correlationId,
					});

					return {
						isValid: recoveryResult.remainingErrors.length === 0,
						errors: recoveryResult.remainingErrors,
						warnings: [...validationResult.warnings, ...recoveryResult.warnings],
						sanitizedData: recoveryResult.recoveredData,
					};
				}
			}

			// Generate error report for validation failures
			if (!validationResult.isValid && this.config.enableErrorReporting)
			{
				const validationError = new ValidationError(validationResult.errors, context);
				await this.errorReporting.generateReport(validationError, context, 'medium');
			}

			return validationResult;
		}
		catch (error)
		{
			const validationError = error as Error;

			if (this.config.enableErrorReporting)
			{
				await this.errorReporting.generateReport(validationError, context, 'high');
			}

			return {
				isValid: false,
				errors: [`Validation failed: ${validationError.message}`],
				warnings: [],
			};
		}
	}

	async ensureIdempotency(operation: string, key: string): Promise<boolean>
	{
		const context = this.createErrorContextType('ensureIdempotency', operation, 'IdempotencyManager', {
			key,
		});

		return this.errorHandler.handleError(
			async () =>
			{
				const idempotencyKey = `idempotent:${operation}:${key}`;
				const result = await this.redis.setnx(idempotencyKey, Date.now().toString());

				if (result === 1)
				{
					// Set TTL to prevent key accumulation
					await this.redis.expire(idempotencyKey, 86400); // 24 hours
					return true; // Operation can proceed
				}

				return false; // Operation already in progress or completed
			},
			context,
		).then((result) =>
		{
			if (!result.success)
			{
				// On error, assume operation should not proceed
				this.logger.error('Idempotency check failed', {
					operation,
					key,
					error: result.error?.message,
					correlationId: context.correlationId,
				});
				return false;
			}
			return result.result!;
		});
	}

	async executeWithReliability<T>(
		operation: () => Promise<T>,
		context: ErrorContextType,
		options?: {
			maxRetries?: number;
			enableCheckpoints?: boolean;
			enableRecovery?: boolean;
		},
	): Promise<T>
	{
		const operationId = this.generateOperationId(context);
		const operationContext: OperationContext = {
			id: operationId,
			context,
			startTime: new Date(),
			checkpoints: [],
		};

		this.activeOperations.set(operationId, operationContext);

		try
		{
			// Create initial checkpoint if enabled
			if (options?.enableCheckpoints)
			{
				const checkpointId = await this.createCheckpoint(
					`${context.stage}-start`,
					{ operationId, context },
				);
				operationContext.checkpoints.push(checkpointId);
			}

			const result = await this.errorHandler.handleError(
				operation,
				context,
				{ maxRetries: options?.maxRetries },
			);

			if (!result.success)
			{
				throw result.error || new Error('Operation failed');
			}

			// Create success checkpoint if enabled
			if (options?.enableCheckpoints)
			{
				const checkpointId = await this.createCheckpoint(
					`${context.stage}-success`,
					{ operationId, result: result.result },
				);
				operationContext.checkpoints.push(checkpointId);
			}

			operationContext.endTime = new Date();
			operationContext.success = true;

			return result.result!;
		}
		catch (error)
		{
			operationContext.endTime = new Date();
			operationContext.success = false;
			operationContext.error = error as Error;

			// Generate error report
			if (this.config.enableErrorReporting)
			{
				await this.errorReporting.generateReport(error as Error, context, 'high');
			}

			throw error;
		}
		finally
		{
			this.activeOperations.delete(operationId);
		}
	}

	async getHealthStatus(): Promise<HealthStatus>
	{
		const errorStats = this.errorHandler.getErrorStats();
		const activeOperationsCount = this.activeOperations.size;
		const checkpointStoreHealth = await this.checkpointStore.healthCheck();

		return {
			healthy: checkpointStoreHealth && errorStats.recentErrors < 10,
			errorStats,
			activeOperations: activeOperationsCount,
			checkpointStoreHealthy: checkpointStoreHealth,
			uptime: process.uptime(),
			memoryUsage: process.memoryUsage(),
		};
	}

	async getErrorReports(filters?: {
		severity?: string;
		component?: string;
		since?: Date;
		limit?: number;
	}): Promise<ErrorReportType[]>
	{
		return this.errorReporting.getReports(filters);
	}

	async cleanup(): Promise<void>
	{
		this.logger.info('Starting reliability manager cleanup');

		// Wait for active operations to complete (with timeout)
		const timeout = 30000; // 30 seconds
		const startTime = Date.now();

		while (this.activeOperations.size > 0 && Date.now() - startTime < timeout)
		{
			this.logger.info('Waiting for active operations to complete', {
				activeCount: this.activeOperations.size,
			});
			await this.sleep(1000);
		}

		if (this.activeOperations.size > 0)
		{
			this.logger.warn('Forcing cleanup with active operations', {
				activeCount: this.activeOperations.size,
			});
		}

		// Cleanup checkpoint store
		await this.checkpointStore.cleanup(this.config.checkpointTtlMs);

		this.logger.info('Reliability manager cleanup completed');
	}

	private async performStageValidation(stage: string, data: unknown): Promise<ValidationResultType>
	{
		const validators = {
			'source-fetch': this.validateSourceData,
			normalization: this.validateNormalizedDomains,
			'existence-check': this.validateExistenceResults,
			enqueue: this.validateEnqueueData,
		};

		const validator = validators[stage as keyof typeof validators];
		if (!validator)
		{
			return {
				isValid: false,
				errors: [`Unknown validation stage: ${stage}`],
				warnings: [],
			};
		}

		return validator.call(this, data);
	}

	private validateSourceData(data: unknown): ValidationResultType
	{
		const errors: string[] = [];
		const warnings: string[] = [];

		const obj = data as Record<string, unknown> | null;
		if (!obj || typeof obj !== 'object')
		{
			errors.push('Source data must be an object');
			return { isValid: false, errors, warnings };
		}

		if (!Array.isArray((obj as any).candidates))
		{
			errors.push('Source data must contain candidates array');
		}

		if (!(obj as any).source || typeof (obj as any).source !== 'string')
		{
			errors.push('Source data must contain valid source field');
		}

		if (!(obj as any).fetchedAt || !((obj as any).fetchedAt instanceof Date))
		{
			errors.push('Source data must contain valid fetchedAt timestamp');
		}

		if ((obj as any).candidates && (obj as any).candidates.length === 0)
		{
			warnings.push('No candidates found in source data');
		}

		// Validate individual candidates
		if (Array.isArray((obj as any).candidates))
		{
			for (let i = 0; i < (obj as any).candidates.length; i++)
			{
				const candidate = (obj as any).candidates[i];
				if (!candidate.domain)
				{
					errors.push(`Candidate at index ${i} missing domain field`);
				}
				if (!candidate.source)
				{
					errors.push(`Candidate at index ${i} missing source field`);
				}
			}
		}

		return {
			isValid: errors.length === 0,
			errors,
			warnings,
			sanitizedData: errors.length === 0 ? data : null,
		};
	}

	private validateNormalizedDomains(data: unknown): ValidationResultType
	{
		const errors: string[] = [];
		const warnings: string[] = [];

		const obj = data as Record<string, unknown> | null;
		if (!obj || typeof obj !== 'object')
		{
			errors.push('Normalized data must be an object');
			return { isValid: false, errors, warnings };
		}

		if (!Array.isArray((obj as any).normalizedDomains))
		{
			errors.push('Normalized data must contain normalizedDomains array');
		}

		if (!(obj as any).processedAt || !((obj as any).processedAt instanceof Date))
		{
			errors.push('Normalized data must contain valid processedAt timestamp');
		}

		// Validate individual normalized domains
		if (Array.isArray((obj as any).normalizedDomains))
		{
			for (let i = 0; i < (obj as any).normalizedDomains.length; i++)
			{
				const domain = (obj as any).normalizedDomains[i];
				if (!domain.etld1)
				{
					errors.push(`Domain at index ${i} missing eTLD+1`);
				}
				if (!domain.isValid)
				{
					warnings.push(`Domain at index ${i} marked as invalid: ${domain.original}`);
				}
			}
		}

		return { isValid: errors.length === 0, errors, warnings };
	}

	private validateExistenceResults(data: unknown): ValidationResultType
	{
		const errors: string[] = [];
		const warnings: string[] = [];

		const obj = data as Record<string, unknown> | null;
		if (!obj || typeof obj !== 'object')
		{
			errors.push('Existence results must be an object');
			return { isValid: false, errors, warnings };
		}

		if (!((obj as any).results instanceof Map))
		{
			errors.push('Existence results must contain results Map');
		}

		if (!(obj as any).checkedAt || !((obj as any).checkedAt instanceof Date))
		{
			errors.push('Existence results must contain valid checkedAt timestamp');
		}

		return { isValid: errors.length === 0, errors, warnings };
	}

	private validateEnqueueData(data: unknown): ValidationResultType
	{
		const errors: string[] = [];
		const warnings: string[] = [];

		const obj = data as Record<string, unknown> | null;
		if (!obj || typeof obj !== 'object')
		{
			errors.push('Enqueue data must be an object');
			return { isValid: false, errors, warnings };
		}

		if (!Array.isArray((obj as any).domains))
		{
			errors.push('Enqueue data must contain domains array');
		}

		if (!(obj as any).enqueuedAt || !((obj as any).enqueuedAt instanceof Date))
		{
			errors.push('Enqueue data must contain valid enqueuedAt timestamp');
		}

		return { isValid: errors.length === 0, errors, warnings };
	}

	private createErrorContextType(
		operation: string,
		stage: string,
		component: string,
		metadata?: Record<string, unknown>,
	): ErrorContextType
	{
		return {
			operation,
			stage,
			component,
			metadata,
			timestamp: new Date(),
			correlationId: this.generateCorrelationId(),
		};
	}

	private generateCheckpointId(stage: string): string
	{
		return `checkpoint:${stage}:${Date.now()}:${crypto.randomUUID()}`;
	}

	private generateOperationId(context: ErrorContextType): string
	{
		return `op:${context.component}:${Date.now()}:${crypto.randomUUID().slice(0, 8)}`;
	}

	private generateCorrelationId(): string
	{
		return crypto.randomUUID();
	}

	private calculateHash(data: unknown): string
	{
		return crypto
			.createHash('sha256')
			.update(JSON.stringify(data))
			.digest('hex');
	}

	private async sleep(ms: number): Promise<void>
	{
		// eslint-disable-next-line no-promise-executor-return
		return new Promise(resolve => setTimeout(resolve, ms));
	}

	private setupGracefulShutdown(): void
	{
		const signals = ['SIGTERM', 'SIGINT', 'SIGUSR2'];

		for (const signal of signals)
		{
			process.on(signal, async () =>
			{
				this.logger.info(`Received ${signal}, starting graceful shutdown`);
				await this.cleanup();
				process.exit(0);
			});
		}

		process.on('uncaughtException', async (error) =>
		{
			this.logger.error('Uncaught exception', { error: error.message, stack: error.stack });
			await this.cleanup();
			process.exit(1);
		});

		process.on('unhandledRejection', async (reason, promise) =>
		{
			this.logger.error('Unhandled rejection', { reason, promise });
			await this.cleanup();
			process.exit(1);
		});
	}
}

class RedisCheckpointStore implements CheckpointStoreInterface
{
	private redis: RedisClientWrapper;

	private ttlMs: number;

	constructor(redis: RedisClientWrapper, ttlMs: number)
	{
		this.redis = redis;
		this.ttlMs = ttlMs;
	}

	async store(checkpoint: CheckpointType): Promise<void>
	{
		const key = `checkpoint:${checkpoint.id}`;
		const ttlSeconds = Math.floor(this.ttlMs / 1000);

		await this.redis.setex(key, ttlSeconds, JSON.stringify(checkpoint));

		// Also add to checkpoint index for cleanup
		await this.redis.zadd('checkpoint:index', Date.now(), checkpoint.id);
	}

	async retrieve(checkpointId: string): Promise<CheckpointType | null>
	{
		const key = `checkpoint:${checkpointId}`;
		const data = await this.redis.get(key);

		if (!data)
		{
			return null;
		}

		try
		{
			return JSON.parse(data) as CheckpointType;
		}
		catch (error)
		{
			throw new Error(`Failed to parse checkpoint data: ${checkpointId}`);
		}
	}

	async cleanup(maxAge: number): Promise<void>
	{
		const cutoff = Date.now() - maxAge;
		const expiredIds = await this.redis.zrangebyscore('checkpoint:index', 0, cutoff);

		if (expiredIds.length > 0)
		{
			// Remove expired checkpoints
			const pipeline = this.redis.pipeline();
			for (const id of expiredIds)
			{
				pipeline.del(`checkpoint:${id}`);
			}
			pipeline.zremrangebyscore('checkpoint:index', 0, cutoff);
			await pipeline.exec();
		}
	}

	async healthCheck(): Promise<boolean>
	{
		try
		{
			await this.redis.ping();
			return true;
		}
		catch
		{
			return false;
		}
	}
}

export type { ReliabilityConfig, OperationContext, HealthStatus };

export { RedisCheckpointStore };

export default CrashSafeReliabilityManager;
