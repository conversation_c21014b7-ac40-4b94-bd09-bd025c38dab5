# Comprehensive Error Handling System

This directory contains a comprehensive error handling system for the domain-seeder service that provides categorized error handling, automatic retry logic with exponential backoff, graceful degradation patterns, error reporting and alerting, and crash-safe reliability management.

## Components

### 1. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (`ErrorHandler.ts`)

The main error handling component that provides:

- **Categorized Error Types**: Errors are categorized by type (network, database, validation, rate_limit, authentication, resource, system) with specific retry policies for each category.

- **Automatic Retry Logic**: Implements exponential backoff with jitter for transient failures. Retry behavior is configurable per error category.

- **Circuit Breaker Pattern**: Prevents cascading failures by opening circuits when failure thresholds are exceeded.

- **Graceful Degradation**: Multiple degradation strategies that can be applied when operations fail:
  - `CacheBypassStrategy`: Bypasses cache and goes directly to source
  - `PartialResultStrategy`: Returns partial results when full processing fails
  - `FallbackDataStrategy`: Uses fallback data source when primary fails
  - `SkipNonCriticalStrategy`: Skips non-critical operations
  - `EmptyResultStrategy`: Returns empty result as last resort

#### Usage Example

```typescript
import { Error<PERSON>and<PERSON> } from "./reliability/ErrorHandler";

const errorHandler = new ErrorHandler(logger);

const context = {
  operation: "fetch-domains",
  stage: "data-fetching",
  component: "TrancoConnector",
  timestamp: new Date(),
  correlationId: "req-123",
};

const result = await errorHandler.handleError(
  async () => {
    // Your operation that might fail
    return await fetchDomainsFromSource();
  },
  context,
  {
    maxRetries: 3,
    baseDelayMs: 1000,
    maxDelayMs: 30000,
  }
);

if (result.success) {
  console.log("Operation succeeded:", result.result);
} else {
  console.error("Operation failed:", result.error);
  if (result.degradationApplied) {
    console.log("Graceful degradation applied:", result.degradationApplied);
  }
}
```

### 2. ErrorReportingSystem (`ErrorReporting.ts`)

Provides comprehensive error reporting and alerting:

- **Error Report Generation**: Creates detailed error reports with context, affected components, and suggested actions.

- **Alert Rules**: Configurable alert rules that trigger based on error patterns:

  - High error rate alerts
  - Critical component failure alerts
  - Validation failure spike alerts
  - Queue depth critical alerts
  - Zero discoveries alerts

- **Alert Channels**: Multiple alert delivery channels (log, webhook, email, slack).

- **Report Management**: Automatic report cleanup and retention policies.

#### Usage Example

```typescript
import { ErrorReportingSystem } from "./reliability/ErrorReporting";

const errorReporting = new ErrorReportingSystem(logger);

// Generate error report
const report = await errorReporting.generateReport(
  new Error("Database connection failed"),
  context,
  "high"
);

// Check alert rules
const errorStats = errorHandler.getErrorStats();
await errorReporting.checkAlertRules(errorStats, context);

// Get reports with filtering
const recentReports = errorReporting.getReports({
  severity: "high",
  since: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
  limit: 10,
});
```

### 3. CrashSafeReliabilityManager (`CrashSafeReliabilityManager.ts`)

The main reliability manager that integrates all error handling components:

- **Checkpoint Management**: Creates and manages recovery checkpoints for crash safety.

- **Validation Pipeline**: Validates data at each pipeline stage with automatic recovery.

- **Idempotency Guarantees**: Ensures operations are idempotent across restarts.

- **Operation Reliability**: Wraps operations with comprehensive error handling, checkpointing, and recovery.

- **Health Monitoring**: Provides health status and metrics for the entire system.

#### Usage Example

```typescript
import { CrashSafeReliabilityManager } from "./reliability/CrashSafeReliabilityManager";

const reliabilityManager = new CrashSafeReliabilityManager(logger, redis);

// Execute operation with reliability features
const result = await reliabilityManager.executeWithReliability(
  async () => {
    // Your complex operation
    return await processDomainsWithMultipleSteps();
  },
  context,
  {
    maxRetries: 3,
    enableCheckpoints: true,
    enableRecovery: true,
  }
);

// Validate pipeline stage
const validationResult = await reliabilityManager.validatePipelineStage(
  "source-fetch",
  sourceData
);

if (!validationResult.isValid) {
  console.error("Validation failed:", validationResult.errors);
  // Recovery may have been applied automatically
  if (validationResult.sanitizedData) {
    console.log("Using recovered data");
  }
}

// Ensure idempotency
const canProceed = await reliabilityManager.ensureIdempotency(
  "process-batch",
  "batch-123"
);

if (canProceed) {
  // Process the batch
}
```

### 4. ErrorRecoveryManager (`../validation/ErrorRecovery.ts`)

Provides automatic error recovery strategies:

- **Missing Field Recovery**: Adds missing required fields with default values
- **Invalid Format Recovery**: Attempts to fix invalid data formats
- **Type Conversion Recovery**: Converts data to expected types when possible
- **Partial Data Recovery**: Recovers valid portions while quarantining invalid items
- **Quarantine Recovery**: Quarantines all invalid data and returns empty valid structure

## Error Categories

The system categorizes errors into the following types with specific handling policies:

| Category       | Description                             | Transient | Max Retries | Base Delay | Max Delay |
| -------------- | --------------------------------------- | --------- | ----------- | ---------- | --------- |
| network        | Network connectivity and timeout errors | Yes       | 3           | 1s         | 30s       |
| database       | Database connection and query errors    | Yes       | 5           | 2s         | 60s       |
| validation     | Data validation and format errors       | No        | 0           | 0s         | 0s        |
| rate_limit     | Rate limiting and quota exceeded errors | Yes       | 3           | 5s         | 300s      |
| authentication | Authentication and authorization errors | No        | 1           | 1s         | 5s        |
| resource       | Resource exhaustion and capacity errors | Yes       | 2           | 10s        | 120s      |
| system         | System-level errors and crashes         | No        | 0           | 0s         | 0s        |

## Graceful Degradation Strategies

The system includes several built-in graceful degradation strategies, ordered by priority:

1. **CacheBypassStrategy** (Priority 1): Bypasses cache when cache operations fail
2. **PartialResultStrategy** (Priority 2): Returns partial results for batch operations
3. **FallbackDataStrategy** (Priority 3): Uses fallback data sources
4. **SkipNonCriticalStrategy** (Priority 4): Skips non-critical operations
5. **EmptyResultStrategy** (Priority 5): Returns empty results as last resort

## Alert Rules

Default alert rules are configured for common failure scenarios:

- **High Error Rate**: Triggered when recent errors exceed 10
- **Critical Component Failure**: Triggered when critical components (DatabaseManager, RedisClient, ScyllaClient) fail
- **Validation Failure Spike**: Triggered when validation failures exceed 5
- **Zero Discoveries**: Triggered when no new domains discovered for extended period
- **Queue Depth Critical**: Triggered when queue depth exceeds 100,000

## Configuration

The error handling system can be configured through environment variables and constructor parameters:

```typescript
// Error Handler Configuration
const errorHandler = new ErrorHandler(logger);

// Error Reporting Configuration
const errorReporting = new ErrorReportingSystem(
  logger,
  1000, // maxReports
  7 * 24 * 60 * 60 * 1000 // reportRetentionMs (7 days)
);

// Reliability Manager Configuration
const reliabilityManager = new CrashSafeReliabilityManager(logger, redis, {
  checkpointTtlMs: 24 * 60 * 60 * 1000, // 24 hours
  maxCheckpoints: 1000,
  enableErrorReporting: true,
  enableRecovery: true,
});
```

## Testing

The error handling system includes comprehensive tests:

- **Unit Tests**: Test individual components and strategies
- **Integration Tests**: Test complete error handling workflows
- **Chaos Tests**: Test system behavior under various failure scenarios

Run tests with:

```bash
pnpm test src/__tests__/ErrorHandler.test.ts
pnpm test src/__tests__/ErrorReporting.test.ts
pnpm test src/__tests__/CrashSafeReliabilityManager.test.ts
pnpm test src/__tests__/ErrorHandlingIntegration.test.ts
```

## Monitoring and Observability

The system provides comprehensive monitoring through:

- **Error Statistics**: Aggregated error counts by component and type
- **Health Status**: Overall system health including active operations and checkpoint store health
- **Alert Notifications**: Real-time alerts for critical error conditions
- **Error Reports**: Detailed error reports with context and suggested actions

## Best Practices

1. **Use Appropriate Error Categories**: Categorize errors correctly to get optimal retry behavior
2. **Provide Rich Context**: Include detailed context information for better error reporting
3. **Enable Checkpoints for Critical Operations**: Use checkpoints for operations that are expensive to restart
4. **Monitor Error Patterns**: Set up appropriate alert rules for your specific use cases
5. **Test Failure Scenarios**: Regularly test your error handling with chaos engineering practices
6. **Review Error Reports**: Regularly review error reports to identify systemic issues

## Integration with Domain Seeder

The error handling system is integrated throughout the domain-seeder service:

- **Source Connectors**: Handle network failures and API rate limits
- **Discovery Engine**: Handle validation errors and partial results
- **Domain Normalization**: Handle format errors with automatic recovery
- **Database Operations**: Handle connection failures with retries and circuit breakers
- **Queue Operations**: Handle backpressure and rate limiting
- **Content Generation**: Handle AI provider failures with fallback strategies

This comprehensive error handling system ensures the domain-seeder service can operate reliably in production environments with automatic recovery from transient failures and graceful degradation when necessary.
