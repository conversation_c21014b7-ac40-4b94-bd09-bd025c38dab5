# Domain Content Generation Services

This module provides services for generating domain content including category classification, tag extraction, and SEO-optimized descriptions.

## Services

### CategoryTagger

Classifies domains into categories and extracts relevant tags using centralized data from `/data` directory.

**Features:**

- Automatic data loading from centralized files (`categories.json`, `tags.json`, `brand-seeds.json`, `tld-categories.json`, `stopwords.json`)
- Brand detection using comprehensive brand database
- TLD-based classification with special handling for e-commerce domains
- Both preGenerated (heuristic) and live analysis modes
- Configurable tag limits and confidence thresholds

**Usage:**

```typescript
import { CategoryTagger } from "./CategoryTagger";

const logger = getLogger();
const tagger = new CategoryTagger(logger, {
  minTags: 5,
  maxTags: 12,
  useBrandDetection: true,
});

// Classify domain
const classification = await tagger.classifyCategory(
  "github.com",
  "preGenerated"
);
// Result: { primary: 'technology', confidence: 0.8, reasoning: [...] }

// Extract tags
const tags = await tagger.extractTags(
  "github.com",
  classification,
  "preGenerated"
);
// Result: { tags: ['technology', 'devtools', 'software', ...], confidence: 0.7, sources: [...] }
```

### DomainSummaryService

Generates SEO-optimized domain descriptions with 320+ words.

**Features:**

- Template-based content generation for different categories
- SEO optimization with keyword integration
- Support for both preGenerated and content-based modes
- Configurable word count limits
- Multiple content expansion strategies

**Usage:**

```typescript
import { DomainSummaryService } from "./DomainSummaryService";

const logger = getLogger();
const summaryService = new DomainSummaryService(logger, {
  minWords: 320,
  maxWords: 500,
  seoOptimization: true,
});

// Generate baseline summary
const summary = await summaryService.generateBaseline(
  "github.com",
  classification,
  tags
);
// Result: { summary: '320+ word description...', wordCount: 345, confidence: 0.8 }
```

## Data Sources

The services automatically load data from these centralized files:

- **`/data/categories.json`** - Category taxonomy with hierarchical structure
- **`/data/tags.json`** - Tag taxonomy with display names
- **`/data/brand-seeds.json`** - Brand database with categories and tags
- **`/data/tld-categories.json`** - TLD to category mappings
- **`/data/stopwords.json`** - Stop words for content filtering
- **`/data/tlds.json`** - Complete list of valid TLDs

## Configuration Options

### CategoryTagger Options

- `minConfidence` - Minimum confidence threshold (default: 0.3)
- `maxTags` - Maximum number of tags to extract (default: 12)
- `minTags` - Minimum number of tags required (default: 5)
- `useHeuristics` - Enable heuristic classification (default: true)
- `useBrandDetection` - Enable brand detection (default: true)

### DomainSummaryService Options

- `minWords` - Minimum word count for summaries (default: 320)
- `maxWords` - Maximum word count for summaries (default: 500)
- `includeKeywords` - Additional keywords to include
- `seoOptimization` - Enable SEO optimization (default: true)
- `language` - Content language (default: 'en')
- `country` - Target country (default: 'US')

## Integration

These services are designed to be used together in the domain discovery pipeline:

```typescript
// 1. Classify domain and extract tags
const classification = await tagger.classifyCategory(domain, "preGenerated");
const tags = await tagger.extractTags(domain, classification, "preGenerated");

// 2. Generate SEO-optimized description
const summary = await summaryService.generateBaseline(
  domain,
  classification,
  tags
);

// 3. Use results for domain description
const domainDescription = {
  metadata: {
    domain,
    category: {
      primary: classification.primary,
      secondary: classification.secondary,
    },
    tags: tags.tags,
    preGenerated: true,
  },
  overview: {
    summary: summary.summary,
  },
};
```
