import type { LoggerInstanceType } from '@shared';
import { readFileSync } from 'node:fs';
import { resolve } from 'node:path';

type CategoryClassificationResultType =
{
	primary: string;
	secondary?: string;
	confidence: number;
	reasoning: string[];
};

type TagExtractionResultType =
{
	tags: string[];
	confidence: number;
	sources: string[];
};

type CategoryTaggerOptionsType =
{
	minConfidence?: number;
	maxTags?: number;
	minTags?: number;
	useHeuristics?: boolean;
	useBrandDetection?: boolean;
};

type BrandDataType =
{
	name: string;
	parent: string | null;
	category_primary: string;
	tags: string[];
};

type TldCategoryDataType =
{
	description: string;
	tlds: string[];
	domainPatterns: string[];
};

type StopwordsDataType =
{
	tags: string[];
	categories: string[];
	normalize: Record<string, string>;
};

/**
 * CategoryTagger service for classifying domains into categories and extracting relevant tags
 * Supports both preGenerated (heuristic-based) and live analysis modes
 * Uses centralized data files from /data directory
 */
class CategoryTagger
{
	private logger: LoggerInstanceType;

	private categories: Record<string, any> = {};

	private tags: Record<string, string> = {};

	private brandData: Record<string, BrandDataType> = {};

	private tldCategories: Record<string, TldCategoryDataType> = {};

	private stopwords: StopwordsDataType = { tags: [], categories: [], normalize: {} };

	private tlds: string[] = [];

	private options: Required<CategoryTaggerOptionsType>;

	constructor(logger: LoggerInstanceType, options: CategoryTaggerOptionsType = {})
	{
		this.logger = logger;
		this.options = {
			minConfidence: options.minConfidence ?? 0.3,
			maxTags: options.maxTags ?? 12,
			minTags: options.minTags ?? 5,
			useHeuristics: options.useHeuristics ?? true,
			useBrandDetection: options.useBrandDetection ?? true,
		};

		// Load data from centralized files
		this.loadDataFiles();
	}

	/**
	 * Load all data files from the /data directory
	 */
	private loadDataFiles(): void
	{
		try
		{
			// Use correct path relative to the service directory
			const dataDir = resolve(__dirname, '../../data');

			this.categories = JSON.parse(readFileSync(resolve(dataDir, 'categories.json'), 'utf-8'));
			this.tags = JSON.parse(readFileSync(resolve(dataDir, 'tags.json'), 'utf-8'));
			this.brandData = JSON.parse(readFileSync(resolve(dataDir, 'brand-seeds.json'), 'utf-8'));
			this.tldCategories = JSON.parse(readFileSync(resolve(dataDir, 'tld-categories.json'), 'utf-8'));
			this.stopwords = JSON.parse(readFileSync(resolve(dataDir, 'stopwords.json'), 'utf-8'));
			this.tlds = JSON.parse(readFileSync(resolve(dataDir, 'tlds.json'), 'utf-8'));

			this.logger.info({
				categories: Object.keys(this.categories).length,
				tags: Object.keys(this.tags).length,
				brands: Object.keys(this.brandData).length,
				tldCategories: Object.keys(this.tldCategories).length,
				tlds: this.tlds.length,
			}, 'Successfully loaded data files');
		}
		catch (error)
		{
			this.logger.error({
				error: error.message,
				dataDir: resolve(__dirname, '../../data'),
			}, 'Failed to load data files, using fallback data');

			// Initialize with minimal fallback data
			this.initializeFallbackData();
		}
	}

	/**
	 * Initialize minimal fallback data when JSON files can't be loaded
	 */
	private initializeFallbackData(): void
	{
		this.categories = {
			technology: { name: 'Technology', categories: {} },
			business: { name: 'Business', categories: {} },
			'e-commerce': { name: 'E-commerce', categories: {} },
			education: { name: 'Education', categories: {} },
			health: { name: 'Health', categories: {} },
			finance: { name: 'Finance', categories: {} },
			entertainment: { name: 'Entertainment', categories: {} },
			'news-media': { name: 'News & Media', categories: {} },
			travel: { name: 'Travel', categories: {} },
			'food-drink': { name: 'Food & Drink', categories: {} },
			nonprofit: { name: 'Non-profit', categories: {} },
			government: { name: 'Government', categories: {} },
		};

		this.tags = {
			technology: 'Technology',
			business: 'Business',
			'e-commerce': 'E-commerce',
			software: 'Software',
			web: 'Web',
			mobile: 'Mobile',
			api: 'API',
			saas: 'SaaS',
			platform: 'Platform',
			service: 'Service',
		};

		this.brandData = {};

		this.tldCategories = {
			commercial: { description: 'Commercial', tlds: ['com', 'biz'], domainPatterns: [] },
			technology: { description: 'Technology', tlds: ['tech', 'dev', 'io'], domainPatterns: [] },
			organization: { description: 'Organization', tlds: ['org'], domainPatterns: [] },
			education: { description: 'Education', tlds: ['edu'], domainPatterns: [] },
			government: { description: 'Government', tlds: ['gov'], domainPatterns: [] },
		};

		this.stopwords = {
			tags: ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with'],
			categories: ['general', 'misc'],
			normalize: {
				tech: 'technology',
				dev: 'development',
				app: 'application',
				apps: 'applications',
				biz: 'business',
				corp: 'corporate',
				co: 'company',
				inc: 'incorporated',
				ltd: 'limited',
				llc: 'limited liability company',
				ai: 'artificial intelligence',
				ml: 'machine learning',
				iot: 'internet of things',
				saas: 'software as a service',
				paas: 'platform as a service',
				iaas: 'infrastructure as a service',
				api: 'application programming interface',
				ui: 'user interface',
				ux: 'user experience',
				seo: 'search engine optimization',
				crm: 'customer relationship management',
				erp: 'enterprise resource planning',
				hr: 'human resources',
				it: 'information technology',
				b2b: 'business to business',
				b2c: 'business to consumer',
				ecom: 'ecommerce',
				fintech: 'financial technology',
				healthtech: 'health technology',
				edtech: 'education technology',
				proptech: 'property technology',
				regtech: 'regulatory technology',
				insurtech: 'insurance technology',
				martech: 'marketing technology',
				adtech: 'advertising technology',
				biotech: 'biotechnology',
				nanotech: 'nanotechnology',
				cleantech: 'clean technology',
				greentech: 'green technology',
				cybersec: 'cybersecurity',
				infosec: 'information security',
				devsec: 'development security',
				devops: 'development operations',
				mlops: 'machine learning operations',
				dataops: 'data operations',
				secops: 'security operations',
				gitops: 'git operations',
				cloudops: 'cloud operations',
				aiops: 'artificial intelligence operations',
			},
		};

		this.tlds = ['com', 'org', 'net', 'edu', 'gov', 'io', 'co', 'tech', 'dev'];

		this.logger.info({}, 'Initialized with fallback data');
	}

	/**
	 * Classify domain into primary and secondary categories
	 */
	async classifyCategory(
		domain: string,
		mode: 'preGenerated' | 'live' = 'preGenerated',
		content?: string,
	): Promise<CategoryClassificationResultType>
	{
		try
		{
			const domainTokens = this.tokenizeDomain(domain);
			const tld = this.extractTld(domain);

			let result: CategoryClassificationResultType;

			if (mode === 'preGenerated')
			{
				result = await this.classifyHeuristic(domain, domainTokens, tld);
			}
			else
			{
				result = await this.classifyFromContent(domain, domainTokens, tld, content);
			}

			this.logger.info({
				domain,
				mode,
				primary: result.primary,
				secondary: result.secondary,
				confidence: result.confidence,
			}, 'Domain category classification completed');

			return result;
		}
		catch (error)
		{
			this.logger.error({ domain, mode, error }, 'Category classification failed');
			return this.getFallbackCategory(domain);
		}
	}

	/**
	 * Extract relevant tags from domain analysis
	 */
	async extractTags(
		domain: string,
		category: CategoryClassificationResultType,
		mode: 'preGenerated' | 'live' = 'preGenerated',
		content?: string,
	): Promise<TagExtractionResultType>
	{
		try
		{
			const domainTokens = this.tokenizeDomain(domain);
			const tld = this.extractTld(domain);

			const extractedTags: string[] = [];
			const sources: string[] = [];

			// Base tags from domain tokens
			const domainTags = this.extractTagsFromTokens(domainTokens);
			extractedTags.push(...domainTags);
			if (domainTags.length > 0) sources.push('domain-tokens');

			// Category-based tags
			const categoryTags = this.extractTagsFromCategory(category.primary, category.secondary);
			extractedTags.push(...categoryTags);
			if (categoryTags.length > 0) sources.push('category');

			// TLD-based tags
			const tldTags = this.extractTagsFromTld(tld);
			if (tldTags.length > 0)
			{
				extractedTags.push(...tldTags);
				sources.push('tld');
			}

			// Brand detection tags
			if (this.options.useBrandDetection)
			{
				const brandTags = this.extractBrandTags(domainTokens);
				if (brandTags.length > 0)
				{
					extractedTags.push(...brandTags);
					sources.push('brand-detection');
				}
			}

			// Content-based tags for live mode
			if (mode === 'live' && content)
			{
				const contentTags = this.extractTagsFromContent(content);
				if (contentTags.length > 0)
				{
					extractedTags.push(...contentTags);
					sources.push('content-analysis');
				}
			}

			// Normalize and deduplicate tags
			const normalizedTags = this.normalizeTags(extractedTags);

			// Ensure we meet minimum requirements
			const finalTags = this.ensureTagRequirements(normalizedTags, domain, category);

			const confidence = this.calculateTagConfidence(finalTags, sources);

			this.logger.info({
				domain,
				mode,
				tagCount: finalTags.length,
				confidence,
				sources,
			}, 'Tag extraction completed');

			return {
				tags: finalTags,
				confidence,
				sources,
			};
		}
		catch (error)
		{
			this.logger.error({ domain, mode, error }, 'Tag extraction failed');
			return this.getFallbackTags(domain, category);
		}
	}

	/**
	 * Normalize tags by removing duplicates, fixing case, and validating against taxonomy
	 */
	normalizeTags(tags: string[]): string[]
	{
		const normalized = tags
			.map(tag => tag.toLowerCase().trim())
			.filter(tag => tag.length > 0)
			.filter(tag => tag.length <= 50) // Reasonable tag length limit
			.map(tag => this.normalizeTagFormat(tag));

		// Remove duplicates while preserving order
		const unique = Array.from(new Set(normalized));

		// Validate against known taxonomy
		const validated = unique.filter(tag => this.isValidTag(tag));

		// Sort by relevance/frequency if we have too many
		if (validated.length > this.options.maxTags)
		{
			return this.rankTagsByRelevance(validated).slice(0, this.options.maxTags);
		}

		return validated;
	}

	private async classifyHeuristic(
		domain: string,
		tokens: string[],
		tld: string,
	): Promise<CategoryClassificationResultType>
	{
		const reasoning: string[] = [];
		let primaryCategory = 'business';
		let secondaryCategory: string | undefined;
		let confidence = 0.3;

		// TLD-based classification using loaded data
		for (const [categoryKey, categoryData] of Object.entries(this.tldCategories))
		{
			if (categoryData.tlds.includes(tld))
			{
				// Special handling for e-commerce TLDs
				const ecommerceTlds = ['store', 'shop', 'market', 'buy', 'sale', 'shopping', 'deals'];
				if (ecommerceTlds.includes(tld))
				{
					primaryCategory = 'e-commerce';
					confidence += 0.3;
					reasoning.push(`TLD .${tld} suggests e-commerce`);
				}
				else
				{
					const mappedCategory = this.mapTldCategoryToTaxonomy(categoryKey);
					if (mappedCategory && this.categories[mappedCategory])
					{
						primaryCategory = mappedCategory;
						confidence += 0.2;
						reasoning.push(`TLD .${tld} suggests ${mappedCategory}`);
					}
				}
				break;
			}
		}

		// Token-based classification
		const tokenClassification = this.classifyFromTokens(tokens);
		if (tokenClassification.confidence > confidence)
		{
			primaryCategory = tokenClassification.primary;
			secondaryCategory = tokenClassification.secondary;
			confidence = tokenClassification.confidence;
			reasoning.push(...tokenClassification.reasoning);
		}

		// Brand detection boost
		if (this.options.useBrandDetection && this.detectBrand(tokens))
		{
			confidence += 0.1;
			reasoning.push('Brand detected, likely technology company');
			if (primaryCategory === 'business')
			{
				primaryCategory = 'technology'; // Most brands in our data are tech
			}
		}

		// Ensure we have a valid category
		if (!this.isValidCategory(primaryCategory))
		{
			primaryCategory = 'business';
			reasoning.push('Fallback to business category');
		}

		// Ensure we always have some reasoning
		if (reasoning.length === 0)
		{
			reasoning.push(`Default classification for domain with TLD .${tld}`);
		}

		return {
			primary: primaryCategory,
			secondary: secondaryCategory,
			confidence: Math.min(confidence, 1.0),
			reasoning,
		};
	}

	private async classifyFromContent(
		domain: string,
		tokens: string[],
		tld: string,
		content?: string,
	): Promise<CategoryClassificationResultType>
	{
		// Start with heuristic classification as baseline
		const heuristicResult = await this.classifyHeuristic(domain, tokens, tld);

		if (!content)
		{
			return heuristicResult;
		}

		// Analyze content for better classification
		const contentKeywords = this.extractKeywordsFromContent(content);
		const contentClassification = this.classifyFromKeywords(contentKeywords);

		// Combine heuristic and content-based classification
		if (contentClassification.confidence > heuristicResult.confidence)
		{
			return {
				primary: contentClassification.primary,
				secondary: contentClassification.secondary || heuristicResult.secondary,
				confidence: contentClassification.confidence,
				reasoning: [
					...heuristicResult.reasoning,
					...contentClassification.reasoning,
				],
			};
		}

		return heuristicResult;
	}

	private tokenizeDomain(domain: string): string[]
	{
		// Remove TLD and split by common separators
		const withoutTld = domain.split('.')[0];
		return withoutTld
			.split(/[-_.]/)
			.filter(token => token.length > 1)
			.map(token => token.toLowerCase());
	}

	private extractTld(domain: string): string
	{
		const parts = domain.split('.');
		return parts[parts.length - 1].toLowerCase();
	}

	private classifyFromTokens(tokens: string[]): CategoryClassificationResultType
	{
		const categoryScores: Record<string, number> = {};
		const reasoning: string[] = [];

		// Enhanced token matching with specific patterns - more comprehensive and logical
		const tokenPatterns: Record<string, string[]> = {
			technology: [
				'api', 'dev', 'tech', 'software', 'platform', 'code', 'data', 'cloud', 'ai', 'ml',
				'github', 'git', 'app', 'web', 'mobile', 'digital', 'cyber', 'crypto', 'blockchain',
				'saas', 'paas', 'iaas', 'sdk', 'framework', 'library', 'database', 'server', 'hosting',
			],
			'e-commerce': [
				'shop', 'store', 'buy', 'sell', 'market', 'cart', 'pay', 'commerce', 'retail',
				'marketplace', 'shopping', 'sale', 'deals', 'discount', 'price', 'product', 'order',
			],
			education: [
				'learn', 'course', 'school', 'university', 'edu', 'study', 'teach', 'training',
				'academy', 'college', 'student', 'class', 'lesson', 'tutorial', 'certification',
			],
			health: [
				'health', 'medical', 'doctor', 'care', 'wellness', 'clinic', 'hospital', 'medicine',
				'therapy', 'treatment', 'patient', 'nurse', 'pharmacy', 'drug', 'fitness', 'diet',
			],
			finance: [
				'bank', 'money', 'finance', 'invest', 'pay', 'credit', 'loan', 'insurance',
				'trading', 'forex', 'crypto', 'wallet', 'payment', 'fintech', 'accounting', 'tax',
			],
			entertainment: [
				'game', 'gaming', 'play', 'fun', 'music', 'video', 'movie', 'film', 'tv', 'show',
				'stream', 'media', 'entertainment', 'social', 'community', 'chat', 'forum',
			],
			'news-media': [
				'news', 'media', 'press', 'journal', 'magazine', 'blog', 'article', 'report',
				'broadcast', 'radio', 'tv', 'newspaper', 'publication', 'editorial',
			],
			travel: [
				'travel', 'trip', 'tour', 'hotel', 'flight', 'vacation', 'booking', 'tourism',
				'destination', 'guide', 'adventure', 'explore', 'journey', 'cruise',
			],
			'food-drink': [
				'food', 'restaurant', 'cafe', 'bar', 'kitchen', 'recipe', 'cooking', 'chef',
				'dining', 'menu', 'delivery', 'catering', 'beverage', 'drink', 'wine', 'beer',
			],
			business: [
				'business', 'company', 'corp', 'enterprise', 'consulting', 'service', 'solution',
				'professional', 'management', 'strategy', 'marketing', 'sales', 'crm', 'hr',
			],
		};

		// Score based on exact and partial token matches
		for (const token of tokens)
		{
			const lowerToken = token.toLowerCase();

			for (const [category, patterns] of Object.entries(tokenPatterns))
			{
				// Exact matches get higher score
				if (patterns.includes(lowerToken))
				{
					categoryScores[category] = (categoryScores[category] || 0) + 0.8;
					reasoning.push(`Token "${token}" exactly matches ${category} pattern`);
				}
				// Partial matches get lower score
				else if (patterns.some(pattern => lowerToken.includes(pattern) || pattern.includes(lowerToken)))
				{
					categoryScores[category] = (categoryScores[category] || 0) + 0.3;
					reasoning.push(`Token "${token}" partially matches ${category} pattern`);
				}
			}

			// Also check against category names and descriptions
			for (const [categoryKey, categoryData] of Object.entries(this.categories))
			{
				const categoryName = categoryData.name.toLowerCase();
				if (categoryName.includes(lowerToken) || lowerToken.includes(categoryName))
				{
					categoryScores[categoryKey] = (categoryScores[categoryKey] || 0) + 0.4;
					reasoning.push(`Token "${token}" matches category name ${categoryData.name}`);
				}

				// Check subcategories if they exist
				if (categoryData.categories)
				{
					for (const [subKey, subName] of Object.entries(categoryData.categories))
					{
						if (typeof subName === 'string')
						{
							const subNameLower = subName.toLowerCase();
							if (subNameLower.includes(lowerToken) || lowerToken.includes(subNameLower))
							{
								categoryScores[categoryKey] = (categoryScores[categoryKey] || 0) + 0.2;
								reasoning.push(`Token "${token}" matches subcategory ${subName}`);
							}
						}
					}
				}
			}
		}

		// Find best matches
		const sortedCategories = Object.entries(categoryScores)
			.sort(([, a], [, b]) => b - a);

		if (sortedCategories.length === 0)
		{
			return {
				primary: 'business',
				confidence: 0.1,
				reasoning: ['No token matches found, defaulting to business category'],
			};
		}

		const [primaryKey, primaryScore] = sortedCategories[0];
		const secondaryKey = sortedCategories.length > 1 && sortedCategories[1][1] > 0.2
			? sortedCategories[1][0]
			: undefined;

		// Normalize confidence score
		const confidence = Math.min(primaryScore / 2, 0.9); // Scale down and cap at 0.9

		return {
			primary: primaryKey,
			secondary: secondaryKey,
			confidence,
			reasoning,
		};
	}

	private extractTagsFromTokens(tokens: string[]): string[]
	{
		const extractedTags: string[] = [];

		for (const token of tokens)
		{
			// Direct tag matches
			if (this.tags[token])
			{
				extractedTags.push(token);
			}

			// Partial matches
			for (const [tagKey, tagName] of Object.entries(this.tags))
			{
				if (tagKey.includes(token) || tagName.toLowerCase().includes(token))
				{
					extractedTags.push(tagKey);
				}
			}
		}

		return extractedTags;
	}

	private extractTagsFromCategory(primary: string, secondary?: string): string[]
	{
		const tags: string[] = [];

		// Add primary category as tag (always include even if not in tags taxonomy)
		tags.push(primary);

		// Add secondary category as tag
		if (secondary)
		{
			tags.push(secondary);
		}

		// Add related tags from category data
		const categoryData = this.categories[primary];
		if (categoryData?.categories)
		{
			for (const [subKey] of Object.entries(categoryData.categories))
			{
				if (this.tags[subKey])
				{
					tags.push(subKey);
				}
			}
		}

		return tags.slice(0, 4); // Limit category-based tags
	}

	private extractTagsFromTld(tld: string): string[]
	{
		const tldTags: string[] = [];

		// Find TLD category mappings from data
		for (const [categoryKey, categoryData] of Object.entries(this.tldCategories))
		{
			if (categoryData.tlds.includes(tld))
			{
				// Map category to appropriate tags
				const categoryTags = this.getCategoryTags(categoryKey);
				tldTags.push(...categoryTags);
				break;
			}
		}

		// Filter to only include tags that exist in our taxonomy
		return tldTags.filter(tag => this.tags[tag] || this.isValidCategoryTag(tag));
	}

	private getCategoryTags(categoryKey: string): string[]
	{
		// Map TLD categories to our tag taxonomy
		const categoryTagMap: Record<string, string[]> = {
			commercial: ['business', 'e-commerce'], // Include e-commerce for commercial domains
			technology: ['technology'],
			organization: ['nonprofit'],
			education: ['education'],
			government: ['government'],
			media: ['media'],
			finance: ['finance'],
			health: ['health'],
			travel: ['travel'],
			food: ['food'],
			gaming: ['gaming'],
			real_estate: ['real-estate'],
			legal: ['legal'],
			fashion: ['fashion'],
			sports: ['sports'],
			automotive: ['automotive'],
		};

		return categoryTagMap[categoryKey] || [];
	}

	private isValidCategoryTag(tag: string): boolean
	{
		// Allow certain category-based tags even if not in tags.json
		const validCategoryTags = ['business', 'technology', 'e-commerce', 'nonprofit', 'education', 'government'];
		return validCategoryTags.includes(tag);
	}

	private extractBrandTags(tokens: string[]): string[]
	{
		const brandTags: string[] = [];

		for (const token of tokens)
		{
			// Check if token matches a brand in our brand data
			const brandKey = token.toLowerCase();
			if (this.brandData[brandKey])
			{
				const brand = this.brandData[brandKey];
				brandTags.push('brand');

				// Add brand's category as a tag
				if (brand.category_primary && this.categories[brand.category_primary])
				{
					brandTags.push(brand.category_primary);
				}

				// Add brand's specific tags
				brandTags.push(...brand.tags.filter(tag => this.tags[tag]));
				break;
			}
		}

		return brandTags.filter(tag => this.tags[tag] || this.isValidCategoryTag(tag));
	}

	private extractTagsFromContent(content: string): string[]
	{
		const contentTags: string[] = [];
		const keywords = this.extractKeywordsFromContent(content);

		for (const keyword of keywords.slice(0, 10)) // Limit content keywords
		{
			// Find matching tags
			for (const [tagKey, tagName] of Object.entries(this.tags))
			{
				if (
					tagKey.includes(keyword.toLowerCase()) ||
					tagName.toLowerCase().includes(keyword.toLowerCase())
				)
				{
					contentTags.push(tagKey);
				}
			}
		}

		return contentTags;
	}

	private extractKeywordsFromContent(content: string): string[]
	{
		// Simple keyword extraction - in production, this could use NLP libraries
		const words = content
			.toLowerCase()
			.replace(/[^\w\s]/g, ' ')
			.split(/\s+/)
			.filter(word => word.length > 3)
			.filter(word => !this.isStopWord(word));

		// Count frequency and return top keywords
		const frequency: Record<string, number> = {};
		for (const word of words)
		{
			frequency[word] = (frequency[word] || 0) + 1;
		}

		return Object.entries(frequency)
			.sort(([, a], [, b]) => b - a)
			.slice(0, 20)
			.map(([word]) => word);
	}

	private classifyFromKeywords(keywords: string[]): CategoryClassificationResultType
	{
		const categoryScores: Record<string, number> = {};
		const reasoning: string[] = [];

		for (const keyword of keywords)
		{
			for (const [categoryKey, categoryData] of Object.entries(this.categories))
			{
				if (categoryData.name.toLowerCase().includes(keyword))
				{
					categoryScores[categoryKey] = (categoryScores[categoryKey] || 0) + 0.1;
					reasoning.push(`Keyword "${keyword}" suggests ${categoryData.name}`);
				}
			}
		}

		const sortedCategories = Object.entries(categoryScores)
			.sort(([, a], [, b]) => b - a);

		if (sortedCategories.length === 0)
		{
			return {
				primary: 'business',
				confidence: 0.1,
				reasoning: ['No keyword matches found'],
			};
		}

		const [primaryKey, primaryScore] = sortedCategories[0];
		const secondaryKey = sortedCategories.length > 1 ? sortedCategories[1][0] : undefined;

		return {
			primary: primaryKey,
			secondary: secondaryKey,
			confidence: Math.min(primaryScore * 2, 0.9), // Boost content-based confidence
			reasoning,
		};
	}

	private ensureTagRequirements(
		tags: string[],
		domain: string,
		category: CategoryClassificationResultType,
	): string[]
	{
		let finalTags = [...tags];

		// Ensure minimum tag count
		if (finalTags.length < this.options.minTags)
		{
			const additionalTags = this.generateFallbackTags(domain, category, this.options.minTags - finalTags.length);
			finalTags.push(...additionalTags);
		}

		// Ensure maximum tag count
		if (finalTags.length > this.options.maxTags)
		{
			finalTags = this.rankTagsByRelevance(finalTags).slice(0, this.options.maxTags);
		}

		return finalTags;
	}

	private generateFallbackTags(domain: string, category: CategoryClassificationResultType, needed: number): string[]
	{
		const fallbackTags: string[] = [];
		const tld = this.extractTld(domain);

		// Add category-based fallbacks
		fallbackTags.push(category.primary);
		if (category.secondary) fallbackTags.push(category.secondary);

		// Add common business tags
		const commonTags = ['business', 'website', 'online', 'service', 'platform'];
		fallbackTags.push(...commonTags);

		// Add TLD-based tags
		fallbackTags.push(`${tld}-domain`);

		return fallbackTags
			.filter(tag => this.isValidTag(tag))
			.slice(0, needed);
	}

	private rankTagsByRelevance(tags: string[]): string[]
	{
		// Simple relevance ranking - could be enhanced with ML models
		const tagScores: Record<string, number> = {};

		for (const tag of tags)
		{
			let score = 1;

			// Boost technology and business tags
			if (['technology', 'business', 'software', 'saas', 'api'].includes(tag))
			{
				score += 0.5;
			}

			// Boost specific over generic tags
			if (tag.includes('-') || tag.length > 8)
			{
				score += 0.2;
			}

			tagScores[tag] = score;
		}

		return Object.entries(tagScores)
			.sort(([, a], [, b]) => b - a)
			.map(([tag]) => tag);
	}

	private calculateTagConfidence(tags: string[], sources: string[]): number
	{
		let confidence = 0.3;

		// Boost confidence based on number of sources
		confidence += sources.length * 0.1;

		// Boost confidence based on tag quality
		if (tags.length >= this.options.minTags) confidence += 0.2;
		if (tags.some(tag => tag.includes('-'))) confidence += 0.1; // Specific tags

		return Math.min(confidence, 1.0);
	}

	private normalizeTagFormat(tag: string): string
	{
		return tag
			.toLowerCase()
			.replace(/\s+/g, '-')
			.replace(/[^a-z0-9-]/g, '')
			.replace(/-+/g, '-')
			.replace(/^-|-$/g, '');
	}

	private isValidTag(tag: string): boolean
	{
		return tag.length > 1 && tag.length <= 50 && /^[a-z0-9-]+$/.test(tag);
	}

	private isValidCategory(category: string): boolean
	{
		return category in this.categories;
	}

	private detectBrand(tokens: string[]): boolean
	{
		return tokens.some((token) =>
		{
			const brandKey = token.toLowerCase();
			return this.brandData[brandKey] !== undefined;
		});
	}

	private isStopWord(word: string): boolean
	{
		return this.stopwords.tags.includes(word.toLowerCase());
	}

	private getFallbackCategory(domain: string): CategoryClassificationResultType
	{
		const tld = this.extractTld(domain);

		// Find TLD category from data
		let primaryCategory = 'business';
		let reasoning = ['Fallback classification due to error'];

		for (const [categoryKey, categoryData] of Object.entries(this.tldCategories))
		{
			if (categoryData.tlds.includes(tld))
			{
				// Map TLD category to our category taxonomy
				const mappedCategory = this.mapTldCategoryToTaxonomy(categoryKey);
				if (mappedCategory)
				{
					primaryCategory = mappedCategory;
					reasoning = [`TLD .${tld} suggests ${categoryKey} category`];
				}
				break;
			}
		}

		return {
			primary: primaryCategory,
			confidence: 0.1,
			reasoning,
		};
	}

	private mapTldCategoryToTaxonomy(tldCategory: string): string | null
	{
		const categoryMap: Record<string, string> = {
			commercial: 'technology', // Changed to prioritize technology for commercial domains
			technology: 'technology',
			organization: 'nonprofit',
			education: 'education',
			government: 'government',
			media: 'news-media',
			finance: 'finance',
			health: 'health',
			travel: 'travel',
			food: 'food-drink',
			gaming: 'entertainment',
			real_estate: 'real-estate',
			legal: 'law-legal',
			fashion: 'fashion',
			sports: 'sports',
			automotive: 'automotive',
		};

		return categoryMap[tldCategory] || null;
	}

	private getFallbackTags(domain: string, category: CategoryClassificationResultType): TagExtractionResultType
	{
		const fallbackTags = [
			category.primary,
			'business',
			'website',
			'online',
			'service',
		].filter(tag => this.isValidTag(tag));

		return ({
			tags: fallbackTags.slice(0, this.options.minTags),
			confidence: 0.1,
			sources: ['fallback'],
		});
	}
}

export type {
	CategoryClassificationResultType,
	TagExtractionResultType,
	CategoryTaggerOptionsType,
};

export default CategoryTagger;
