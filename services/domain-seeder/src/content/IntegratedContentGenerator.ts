import type { LoggerInstanceType } from '@shared';
import { logger as sharedLogger } from '@shared';
import type { DiscoveredDomainInterface } from '../interfaces/DiscoveryEngine';
import ProvenanceTracker from '../provenance/ProvenanceTracker';
import PreGeneratedContentGenerator from './PreGeneratedContentGenerator';
import LiveContentAnalyzer from './LiveContentAnalyzer';
import CategoryTagger from './CategoryTagger';
import DomainSummaryService from './DomainSummaryService';

const logger = sharedLogger.getLogger('IntegratedContentGenerator');

type ContentGenerationOptions =
{
	mode: 'preGenerated' | 'live';
	minWords?: number;
	maxWords?: number;
	minTags?: number;
	maxTags?: number;
	seoOptimization?: boolean;
	language?: string;
	country?: string;
	timeout?: number;
};

type GeneratedContent =
{
	summary: string;
	category: {
		primary: string;
		secondary?: string;
	};
	tags: string[];
	seoSummary: string;
	wordCount: number;
	confidence: number;
	method: 'preGenerated' | 'live';
	sources: string[];
	metadata: {
		generatedAt: string;
		version: string;
		inputs: string[];
		processingTime?: number;
		contentVersion: number;
		lastUpdated: string;
		updateReason?: string;
	};
};

type ContentVersionInfo =
{
	domain: string;
	currentVersion: number;
	versions: Array<{
		version: number;
		method: 'preGenerated' | 'live';
		generatedAt: string;
		confidence: number;
		updateReason?: string;
	}>;
	lastPreGenerated?: string;
	lastLive?: string;
	scheduledForLiveUpdate?: boolean;
	liveUpdatePriority?: 'high' | 'normal' | 'low';
};

type ContentUpdateRequest =
{
	domain: string;
	fromMethod: 'preGenerated' | 'live';
	toMethod: 'preGenerated' | 'live';
	reason: string;
	priority?: 'high' | 'normal' | 'low';
	scheduledFor?: string;
};

type IntegratedContentConfig =
{
	enableVersioning: boolean;
	maxVersionsPerDomain: number;
	autoScheduleLiveUpdates: boolean;
	liveUpdateDelayHours: number;
	highPriorityDomainThreshold: number; // Confidence threshold for high priority
	contentUpdateBatchSize: number;
	enableContentMetrics: boolean;
};

/**
 * Integrated content generator that manages both preGenerated and live content generation
 * with versioning, update tracking, and scheduler integration
 */
class IntegratedContentGenerator
{
	private readonly logger: LoggerInstanceType;

	private readonly preGeneratedGenerator: PreGeneratedContentGenerator;

	private readonly liveAnalyzer: LiveContentAnalyzer;

	private readonly categoryTagger: CategoryTagger;

	private readonly summaryService: DomainSummaryService;

	private readonly provenanceTracker?: ProvenanceTracker;

	private readonly config: IntegratedContentConfig;

	// Content versioning storage (in production, this would be Redis/ScyllaDB)
	private readonly contentVersions = new Map<string, ContentVersionInfo>();

	private readonly pendingUpdates = new Map<string, ContentUpdateRequest>();

	// Metrics
	private readonly metrics = {
		preGeneratedCount: 0,
		liveAnalysisCount: 0,
		contentUpdatesCount: 0,
		averageProcessingTime: 0,
		errorCount: 0,
		versioningOperations: 0,
	};

	constructor(
		config: Partial<IntegratedContentConfig> = {},
		provenanceTracker?: ProvenanceTracker,
	)
	{
		this.logger = logger;
		this.provenanceTracker = provenanceTracker;

		this.config = {
			enableVersioning: config.enableVersioning ?? true,
			maxVersionsPerDomain: config.maxVersionsPerDomain ?? 5,
			autoScheduleLiveUpdates: config.autoScheduleLiveUpdates ?? true,
			liveUpdateDelayHours: config.liveUpdateDelayHours ?? 24,
			highPriorityDomainThreshold: config.highPriorityDomainThreshold ?? 0.8,
			contentUpdateBatchSize: config.contentUpdateBatchSize ?? 100,
			enableContentMetrics: config.enableContentMetrics ?? true,
		};

		// Initialize content generation components
		this.preGeneratedGenerator = new PreGeneratedContentGenerator(this.logger);
		this.liveAnalyzer = new LiveContentAnalyzer(this.logger);
		this.categoryTagger = new CategoryTagger(this.logger);
		this.summaryService = new DomainSummaryService(this.logger);

		this.logger.info({
			config: this.config,
		}, 'IntegratedContentGenerator initialized');
	}

	/**
	 * Generate content for discovered domains during the discovery pipeline
	 */
	async generateContentForDiscoveredDomains(
		discoveredDomains: DiscoveredDomainInterface[],
		options: Partial<ContentGenerationOptions> = {},
	): Promise<Map<string, GeneratedContent>>
	{
		const startTime = Date.now();
		const results = new Map<string, GeneratedContent>();

		if (discoveredDomains.length === 0)
		{
			return results;
		}

		const mergedOptions: ContentGenerationOptions = {
			mode: 'preGenerated', // Default to preGenerated for discovery pipeline
			minWords: 320,
			maxWords: 500,
			minTags: 5,
			maxTags: 12,
			seoOptimization: true,
			language: 'en',
			country: 'US',
			timeout: 8000,
			...options,
		};

		this.logger.info({
			domainCount: discoveredDomains.length,
			mode: mergedOptions.mode,
		}, 'Starting content generation for discovered domains');

		// Process domains in batches to avoid overwhelming the system
		const batches = this.createBatches(discoveredDomains, this.config.contentUpdateBatchSize);

		for (const batch of batches)
		{
			const batchResults = await this.processBatch(batch, mergedOptions);

			// Merge batch results
			for (const [domain, content] of batchResults)
			{
				results.set(domain, content);
			}
		}

		// Update metrics
		const processingTime = Date.now() - startTime;
		this.updateMetrics(results.size, processingTime, mergedOptions.mode);

		// Schedule live updates if enabled
		if (this.config.autoScheduleLiveUpdates && mergedOptions.mode === 'preGenerated')
		{
			await this.scheduleLiveUpdates(Array.from(results.keys()));
		}

		this.logger.info({
			domainCount: discoveredDomains.length,
			successCount: results.size,
			processingTime,
			mode: mergedOptions.mode,
		}, 'Content generation completed for discovered domains');

		return results;
	}

	/**
	 * Generate content for a single domain with versioning support
	 */
	async generateContentForDomain(
		domain: string,
		options: ContentGenerationOptions,
		discoveredDomain?: DiscoveredDomainInterface,
	): Promise<GeneratedContent>
	{
		const startTime = Date.now();

		try
		{
			let content: GeneratedContent;

			if (options.mode === 'preGenerated')
			{
				content = await this.generatePreGeneratedContent(domain, options, discoveredDomain);
			}
			else
			{
				content = await this.generateLiveContent(domain, options);
			}

			// Handle versioning if enabled
			if (this.config.enableVersioning)
			{
				await this.updateContentVersion(domain, content, 'automatic');
			}

			// Record in provenance if available
			if (this.provenanceTracker && discoveredDomain)
			{
				await this.provenanceTracker.recordDiscovery(discoveredDomain, {
					contentGenerated: true,
					contentMethod: options.mode,
					contentVersion: content.metadata.contentVersion,
				});
			}

			const processingTime = Date.now() - startTime;
			content.metadata.processingTime = processingTime;

			// Update metrics
			this.updateMetrics(1, processingTime, options.mode);

			this.logger.info({
				domain,
				mode: options.mode,
				confidence: content.confidence,
				wordCount: content.wordCount,
				processingTime,
			}, 'Content generated for domain');

			return content;
		}
		catch (error)
		{
			this.metrics.errorCount++;
			this.logger.error({
				domain,
				mode: options.mode,
				error: error.message,
			}, 'Content generation failed for domain');
			throw error;
		}
	}

	/**
	 * Update existing content from preGenerated to live analysis
	 */
	async upgradeToLiveContent(
		domain: string,
		reason: string = 'scheduled-upgrade',
		priority: 'high' | 'normal' | 'low' = 'normal',
	): Promise<GeneratedContent>
	{
		this.logger.info({
			domain,
			reason,
			priority,
		}, 'Upgrading domain content to live analysis');

		const options: ContentGenerationOptions = {
			mode: 'live',
			minWords: 320,
			maxWords: 500,
			minTags: 5,
			maxTags: 12,
			seoOptimization: true,
			timeout: 8000,
		};

		const content = await this.generateContentForDomain(domain, options);

		// Update versioning with upgrade reason
		if (this.config.enableVersioning)
		{
			await this.updateContentVersion(domain, content, reason);
		}

		// Remove from pending updates if it was scheduled
		this.pendingUpdates.delete(domain);

		this.metrics.contentUpdatesCount++;

		this.logger.info({
			domain,
			confidence: content.confidence,
			wordCount: content.wordCount,
			reason,
		}, 'Domain content upgraded to live analysis');

		return content;
	}

	/**
	 * Get content version information for a domain
	 */
	getContentVersionInfo(domain: string): ContentVersionInfo | null
	{
		return this.contentVersions.get(domain) || null;
	}

	/**
	 * Schedule a domain for live content update
	 */
	async scheduleLiveUpdate(
		domain: string,
		priority: 'high' | 'normal' | 'low' = 'normal',
		scheduledFor?: Date,
	): Promise<void>
	{
		const updateRequest: ContentUpdateRequest = {
			domain,
			fromMethod: 'preGenerated',
			toMethod: 'live',
			reason: 'scheduled-live-update',
			priority,
			scheduledFor: scheduledFor?.toISOString(),
		};

		this.pendingUpdates.set(domain, updateRequest);

		this.logger.info({
			domain,
			priority,
			scheduledFor: scheduledFor?.toISOString(),
		}, 'Domain scheduled for live content update');
	}

	/**
	 * Get pending content updates for scheduler integration
	 */
	getPendingUpdates(
		priority?: 'high' | 'normal' | 'low',
		limit?: number,
	): ContentUpdateRequest[]
	{
		let updates = Array.from(this.pendingUpdates.values());

		if (priority)
		{
			updates = updates.filter(update => update.priority === priority);
		}

		// Sort by priority and scheduled time
		updates.sort((a, b) =>
		{
			const priorityOrder = { high: 3, normal: 2, low: 1 };
			const aPriority = priorityOrder[a.priority || 'normal'];
			const bPriority = priorityOrder[b.priority || 'normal'];

			if (aPriority !== bPriority)
			{
				return bPriority - aPriority;
			}

			// If same priority, sort by scheduled time
			const aTime = a.scheduledFor ? new Date(a.scheduledFor).getTime() : Date.now();
			const bTime = b.scheduledFor ? new Date(b.scheduledFor).getTime() : Date.now();

			return aTime - bTime;
		});

		return limit ? updates.slice(0, limit) : updates;
	}

	/**
	 * Process pending updates (for scheduler integration)
	 */
	async processPendingUpdates(limit: number = 10): Promise<Map<string, GeneratedContent>>
	{
		const updates = this.getPendingUpdates(undefined, limit);
		const results = new Map<string, GeneratedContent>();

		if (updates.length === 0)
		{
			return results;
		}

		this.logger.info({
			updateCount: updates.length,
		}, 'Processing pending content updates');

		for (const update of updates)
		{
			try
			{
				const content = await this.upgradeToLiveContent(
					update.domain,
					update.reason,
					update.priority,
				);
				results.set(update.domain, content);
			}
			catch (error)
			{
				this.logger.error({
					domain: update.domain,
					error: error.message,
				}, 'Failed to process pending update');
			}
		}

		return results;
	}

	/**
	 * Get content generation metrics
	 */
	getMetrics(): typeof this.metrics
	{
		return { ...this.metrics };
	}

	/**
	 * Reset metrics counters
	 */
	resetMetrics(): void
	{
		this.metrics.preGeneratedCount = 0;
		this.metrics.liveAnalysisCount = 0;
		this.metrics.contentUpdatesCount = 0;
		this.metrics.averageProcessingTime = 0;
		this.metrics.errorCount = 0;
		this.metrics.versioningOperations = 0;
	}

	private async processBatch(
		batch: DiscoveredDomainInterface[],
		options: ContentGenerationOptions,
	): Promise<Map<string, GeneratedContent>>
	{
		const results = new Map<string, GeneratedContent>();

		// Process domains concurrently within the batch
		const promises = batch.map(async (discoveredDomain) =>
		{
			try
			{
				const content = await this.generateContentForDomain(
					discoveredDomain.domain,
					options,
					discoveredDomain,
				);
				return { domain: discoveredDomain.domain, content };
			}
			catch (error)
			{
				this.metrics.errorCount++;
				this.logger.error({
					domain: discoveredDomain.domain,
					error: error.message,
				}, 'Failed to generate content for domain in batch');
				return null;
			}
		});

		const batchResults = await Promise.allSettled(promises);

		for (const result of batchResults)
		{
			if (result.status === 'fulfilled' && result.value)
			{
				results.set(result.value.domain, result.value.content);
			}
		}

		return results;
	}

	private async generatePreGeneratedContent(
		domain: string,
		options: ContentGenerationOptions,
		discoveredDomain?: DiscoveredDomainInterface,
	): Promise<GeneratedContent>
	{
		const preGeneratedOptions = {
			minWords: options.minWords,
			maxWords: options.maxWords,
			minTags: options.minTags,
			maxTags: options.maxTags,
			seoOptimization: options.seoOptimization,
			language: options.language,
			country: options.country,
		};

		const result = await this.preGeneratedGenerator.generateContent(
			domain,
			undefined, // Let it generate category
			undefined, // Let it generate tags
			preGeneratedOptions,
		);

		// Get current version number
		const versionInfo = this.contentVersions.get(domain);
		const currentVersion = versionInfo ? versionInfo.currentVersion + 1 : 1;

		return {
			summary: result.summary,
			category: result.category,
			tags: result.tags,
			seoSummary: result.seoSummary,
			wordCount: result.wordCount,
			confidence: result.confidence,
			method: 'preGenerated',
			sources: result.sources,
			metadata: {
				...result.metadata,
				contentVersion: currentVersion,
				lastUpdated: new Date().toISOString(),
			},
		};
	}

	private async generateLiveContent(
		domain: string,
		options: ContentGenerationOptions,
	): Promise<GeneratedContent>
	{
		const liveOptions = {
			timeout: options.timeout,
			minWords: options.minWords,
			maxWords: options.maxWords,
		};

		const result = await this.liveAnalyzer.analyzeDomain(
			domain,
			this.categoryTagger,
			this.summaryService,
			liveOptions,
		);

		// Get current version number
		const versionInfo = this.contentVersions.get(domain);
		const currentVersion = versionInfo ? versionInfo.currentVersion + 1 : 1;

		return {
			summary: result.summary.summary,
			category: {
				primary: result.category.primary,
				secondary: result.category.secondary,
			},
			tags: result.tags.tags,
			seoSummary: result.summary.summary.substring(0, 160),
			wordCount: result.summary.wordCount,
			confidence: result.confidence,
			method: 'live',
			sources: ['live-analysis', 'homepage-content'],
			metadata: {
				generatedAt: result.generatedAt,
				version: '1.0.0',
				inputs: ['live-content', 'readability-analysis'],
				processingTime: result.processingTime,
				contentVersion: currentVersion,
				lastUpdated: new Date().toISOString(),
			},
		};
	}

	private async updateContentVersion(
		domain: string,
		content: GeneratedContent,
		updateReason: string,
	): Promise<void>
	{
		let versionInfo = this.contentVersions.get(domain);

		if (!versionInfo)
		{
			versionInfo = {
				domain,
				currentVersion: 0,
				versions: [],
			};
		}

		// Add new version (only if it's not already the latest version)
		versionInfo.currentVersion = content.metadata.contentVersion;

		// Check if this version already exists
		const existingVersion = versionInfo.versions.find(v => v.version === content.metadata.contentVersion);
		if (!existingVersion)
		{
			versionInfo.versions.push({
				version: content.metadata.contentVersion,
				method: content.method,
				generatedAt: content.metadata.generatedAt,
				confidence: content.confidence,
				updateReason,
			});
		}
		else
		{
			// Update existing version with new reason
			existingVersion.updateReason = updateReason;
		}

		// Update last generation timestamps
		if (content.method === 'preGenerated')
		{
			versionInfo.lastPreGenerated = content.metadata.generatedAt;
		}
		else
		{
			versionInfo.lastLive = content.metadata.generatedAt;
		}

		// Limit version history
		if (versionInfo.versions.length > this.config.maxVersionsPerDomain)
		{
			versionInfo.versions = versionInfo.versions.slice(-this.config.maxVersionsPerDomain);
		}

		this.contentVersions.set(domain, versionInfo);
		this.metrics.versioningOperations++;

		this.logger.debug({
			domain,
			version: content.metadata.contentVersion,
			method: content.method,
			updateReason,
		}, 'Content version updated');
	}

	private async scheduleLiveUpdates(domains: string[]): Promise<void>
	{
		const scheduleTime = new Date();
		scheduleTime.setHours(scheduleTime.getHours() + this.config.liveUpdateDelayHours);

		for (const domain of domains)
		{
			// Determine priority based on confidence or other factors
			const versionInfo = this.contentVersions.get(domain);
			const lastVersion = versionInfo?.versions[versionInfo.versions.length - 1];

			let priority: 'high' | 'normal' | 'low' = 'normal';
			if (lastVersion && lastVersion.confidence >= this.config.highPriorityDomainThreshold)
			{
				priority = 'high';
			}
			else if (lastVersion && lastVersion.confidence < 0.5)
			{
				priority = 'low';
			}

			await this.scheduleLiveUpdate(domain, priority, scheduleTime);
		}

		this.logger.info({
			domainCount: domains.length,
			scheduledFor: scheduleTime.toISOString(),
		}, 'Live updates scheduled for domains');
	}

	private updateMetrics(
		successCount: number,
		processingTime: number,
		mode: 'preGenerated' | 'live',
	): void
	{
		if (!this.config.enableContentMetrics)
		{
			return;
		}

		if (mode === 'preGenerated')
		{
			this.metrics.preGeneratedCount += successCount;
		}
		else
		{
			this.metrics.liveAnalysisCount += successCount;
		}

		// Update average processing time
		const totalOperations = this.metrics.preGeneratedCount + this.metrics.liveAnalysisCount;
		this.metrics.averageProcessingTime =
			(this.metrics.averageProcessingTime * (totalOperations - successCount) + processingTime) / totalOperations;
	}

	private createBatches<T>(items: T[], batchSize: number): T[][]
	{
		const batches: T[][] = [];
		for (let i = 0; i < items.length; i += batchSize)
		{
			batches.push(items.slice(i, i + batchSize));
		}
		return batches;
	}
}

export type {
	ContentGenerationOptions,
	GeneratedContent,
	ContentVersionInfo,
	ContentUpdateRequest,
	IntegratedContentConfig,
};

export default IntegratedContentGenerator;
