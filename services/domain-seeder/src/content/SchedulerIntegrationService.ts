import type { LoggerInstanceType } from '@shared';
import { logger as sharedLogger } from '@shared';

import type IntegratedContentGenerator from './IntegratedContentGenerator';
import ProvenanceTracker from '../provenance/ProvenanceTracker';
import type {
	// ContentUpdateRequest,
	GeneratedContent,
} from './IntegratedContentGenerator';

const logger = sharedLogger.getLogger('SchedulerIntegrationService');

type SchedulerJobConfig =
{
	batchSize: number;
	maxConcurrentJobs: number;
	jobIntervalMs: number;
	priorityWeights: {
		high: number;
		normal: number;
		low: number;
	};
	enableMetrics: boolean;
	retryFailedJobs: boolean;
	maxRetries: number;
};

type SchedulerJob =
{
	id: string;
	domain: string;
	type: 'live-content-upgrade';
	priority: 'high' | 'normal' | 'low';
	scheduledFor: string;
	createdAt: string;
	attempts: number;
	maxAttempts: number;
	status: 'pending' | 'running' | 'completed' | 'failed';
	metadata: {
		fromMethod: 'preGenerated' | 'live';
		toMethod: 'preGenerated' | 'live';
		reason: string;
		originalContentVersion?: number;
	};
};

type JobExecutionResult =
{
	jobId: string;
	domain: string;
	success: boolean;
	content?: GeneratedContent;
	error?: string;
	executionTime: number;
	attempts: number;
};

type SchedulerMetrics =
{
	totalJobsCreated: number;
	totalJobsCompleted: number;
	totalJobsFailed: number;
	averageExecutionTime: number;
	jobsByPriority: {
		high: number;
		normal: number;
		low: number;
	};
	jobsByStatus: {
		pending: number;
		running: number;
		completed: number;
		failed: number;
	};
	contentUpgrades: number;
	retryCount: number;
};

/**
 * Scheduler integration service for managing live content analysis updates
 * Provides job queuing, priority management, and batch processing for content upgrades
 */
class SchedulerIntegrationService
{
	private readonly logger: LoggerInstanceType;

	private readonly contentGenerator: IntegratedContentGenerator;

	private readonly provenanceTracker?: ProvenanceTracker;

	private readonly config: SchedulerJobConfig;

	// Job storage (in production, this would be Redis/ScyllaDB)
	private readonly jobs = new Map<string, SchedulerJob>();

	private readonly runningJobs = new Set<string>();

	// Metrics
	private readonly metrics: SchedulerMetrics = {
		totalJobsCreated: 0,
		totalJobsCompleted: 0,
		totalJobsFailed: 0,
		averageExecutionTime: 0,
		jobsByPriority: { high: 0, normal: 0, low: 0 },
		jobsByStatus: {
			pending: 0, running: 0, completed: 0, failed: 0,
		},
		contentUpgrades: 0,
		retryCount: 0,
	};

	// Job processing state
	private isProcessing = false;

	private processingInterval?: NodeJS.Timeout;

	constructor(
		contentGenerator: IntegratedContentGenerator,
		config: Partial<SchedulerJobConfig> = {},
		provenanceTracker?: ProvenanceTracker,
	)
	{
		this.logger = logger;
		this.contentGenerator = contentGenerator;
		this.provenanceTracker = provenanceTracker;

		this.config = {
			batchSize: config.batchSize ?? 10,
			maxConcurrentJobs: config.maxConcurrentJobs ?? 3,
			jobIntervalMs: config.jobIntervalMs ?? 30000, // 30 seconds
			priorityWeights: {
				high: config.priorityWeights?.high ?? 3,
				normal: config.priorityWeights?.normal ?? 2,
				low: config.priorityWeights?.low ?? 1,
			},
			enableMetrics: config.enableMetrics ?? true,
			retryFailedJobs: config.retryFailedJobs ?? true,
			maxRetries: config.maxRetries ?? 3,
		};

		this.logger.info({
			config: this.config,
		}, 'SchedulerIntegrationService initialized');
	}

	/**
	 * Start the scheduler job processing
	 */
	async start(): Promise<void>
	{
		if (this.isProcessing)
		{
			return;
		}

		this.isProcessing = true;

		// Import pending updates from content generator
		await this.importPendingUpdates();

		// Start job processing interval
		this.processingInterval = setInterval(async () =>
		{
			await this.processJobs();
		}, this.config.jobIntervalMs);

		this.logger.info({
			intervalMs: this.config.jobIntervalMs,
		}, 'Scheduler job processing started');
	}

	/**
	 * Stop the scheduler job processing
	 */
	async stop(): Promise<void>
	{
		this.isProcessing = false;

		if (this.processingInterval)
		{
			clearInterval(this.processingInterval);
			this.processingInterval = undefined;
		}

		// Wait for running jobs to complete
		while (this.runningJobs.size > 0)
		{
			await this.sleep(1000);
		}

		this.logger.info('Scheduler job processing stopped');
	}

	/**
	 * Create a new scheduler job for live content upgrade
	 */
	async createLiveContentUpgradeJob(
		domain: string,
		priority: 'high' | 'normal' | 'low' = 'normal',
		scheduledFor?: Date,
		reason: string = 'scheduled-upgrade',
	): Promise<string>
	{
		const jobId = `live-upgrade-${domain}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

		const job: SchedulerJob = {
			id: jobId,
			domain,
			type: 'live-content-upgrade',
			priority,
			scheduledFor: scheduledFor?.toISOString() || new Date().toISOString(),
			createdAt: new Date().toISOString(),
			attempts: 0,
			maxAttempts: this.config.maxRetries,
			status: 'pending',
			metadata: {
				fromMethod: 'preGenerated',
				toMethod: 'live',
				reason,
			},
		};

		this.jobs.set(jobId, job);
		this.updateMetrics('jobCreated', job);

		this.logger.info({
			jobId,
			domain,
			priority,
			scheduledFor: job.scheduledFor,
			reason,
		}, 'Live content upgrade job created');

		return jobId;
	}

	/**
	 * Get job status
	 */
	getJob(jobId: string): SchedulerJob | null
	{
		return this.jobs.get(jobId) || null;
	}

	/**
	 * Get jobs by status
	 */
	getJobsByStatus(status: SchedulerJob['status']): SchedulerJob[]
	{
		return Array.from(this.jobs.values()).filter(job => job.status === status);
	}

	/**
	 * Get jobs by priority
	 */
	getJobsByPriority(priority: 'high' | 'normal' | 'low'): SchedulerJob[]
	{
		return Array.from(this.jobs.values()).filter(job => job.priority === priority);
	}

	/**
	 * Get pending jobs ready for execution
	 */
	getPendingJobs(limit?: number): SchedulerJob[]
	{
		const now = new Date().toISOString();
		const pendingJobs = Array.from(this.jobs.values())
			.filter(job => job.status === 'pending' && job.scheduledFor <= now);

		// Sort by priority and scheduled time
		pendingJobs.sort((a, b) =>
		{
			const aPriority = this.config.priorityWeights[a.priority];
			const bPriority = this.config.priorityWeights[b.priority];

			if (aPriority !== bPriority)
			{
				return bPriority - aPriority; // Higher priority first
			}

			// If same priority, sort by scheduled time
			return a.scheduledFor.localeCompare(b.scheduledFor);
		});

		return limit ? pendingJobs.slice(0, limit) : pendingJobs;
	}

	/**
	 * Execute a specific job
	 */
	async executeJob(jobId: string): Promise<JobExecutionResult>
	{
		const job = this.jobs.get(jobId);
		if (!job)
		{
			throw new Error(`Job not found: ${jobId}`);
		}

		if (job.status !== 'pending')
		{
			throw new Error(`Job is not pending: ${jobId} (status: ${job.status})`);
		}

		const startTime = Date.now();
		this.runningJobs.add(jobId);

		// Update job status
		job.status = 'running';
		job.attempts++;
		this.updateMetrics('jobStarted', job);

		this.logger.info({
			jobId,
			domain: job.domain,
			type: job.type,
			priority: job.priority,
			attempt: job.attempts,
		}, 'Executing scheduler job');

		try
		{
			let content: GeneratedContent;

			if (job.type === 'live-content-upgrade')
			{
				content = await this.contentGenerator.upgradeToLiveContent(
					job.domain,
					job.metadata.reason,
					job.priority,
				);
			}
			else
			{
				throw new Error(`Unknown job type: ${job.type}`);
			}

			// Mark job as completed
			job.status = 'completed';
			const executionTime = Date.now() - startTime;

			this.updateMetrics('jobCompleted', job, executionTime);

			// Record in provenance if available
			if (this.provenanceTracker)
			{
				// This would typically update the existing provenance record
				this.logger.info({
					jobId,
					domain: job.domain,
					contentMethod: content.method,
					contentVersion: content.metadata.contentVersion,
					msg: 'Job completion recorded in provenance',
				});
			}

			this.logger.info({
				jobId,
				domain: job.domain,
				executionTime,
				contentVersion: content.metadata.contentVersion,
				contentConfidence: content.confidence,
				msg: 'Scheduler job completed successfully',
			});

			return {
				jobId,
				domain: job.domain,
				success: true,
				content,
				executionTime,
				attempts: job.attempts,
			};
		}
		catch (error)
		{
			const executionTime = Date.now() - startTime;

			// Determine if job should be retried
			if (this.config.retryFailedJobs && job.attempts < job.maxAttempts)
			{
				job.status = 'pending';
				// Schedule retry with exponential backoff
				const retryDelay = Math.min(1000 * 2 ** (job.attempts - 1), 300000); // Max 5 minutes
				const retryTime = new Date(Date.now() + retryDelay);
				job.scheduledFor = retryTime.toISOString();

				this.updateMetrics('jobRetried', job);

				this.logger.warn({
					jobId,
					domain: job.domain,
					error: error.message,
					attempt: job.attempts,
					maxAttempts: job.maxAttempts,
					retryAt: job.scheduledFor,
					msg: 'Scheduler job failed, scheduling retry',
				});
			}
			else
			{
				job.status = 'failed';
				this.updateMetrics('jobFailed', job, executionTime);

				this.logger.error({
					jobId,
					domain: job.domain,
					error: error.message,
					attempts: job.attempts,
					msg: 'Scheduler job failed permanently',
				});
			}

			return {
				jobId,
				domain: job.domain,
				success: false,
				error: error.message,
				executionTime,
				attempts: job.attempts,
			};
		}
		finally
		{
			this.runningJobs.delete(jobId);
		}
	}

	/**
	 * Get scheduler metrics
	 */
	getMetrics(): SchedulerMetrics
	{
		// Update current job status counts
		const currentMetrics = { ...this.metrics };

		currentMetrics.jobsByStatus = {
			pending: 0, running: 0, completed: 0, failed: 0,
		};
		for (const job of this.jobs.values())
		{
			currentMetrics.jobsByStatus[job.status]++;
		}

		return currentMetrics;
	}

	/**
	 * Reset metrics counters
	 */
	resetMetrics(): void
	{
		this.metrics.totalJobsCreated = 0;
		this.metrics.totalJobsCompleted = 0;
		this.metrics.totalJobsFailed = 0;
		this.metrics.averageExecutionTime = 0;
		this.metrics.jobsByPriority = { high: 0, normal: 0, low: 0 };
		this.metrics.jobsByStatus = {
			pending: 0, running: 0, completed: 0, failed: 0,
		};
		this.metrics.contentUpgrades = 0;
		this.metrics.retryCount = 0;
	}

	/**
	 * Clean up completed and failed jobs older than specified days
	 */
	async cleanupJobs(olderThanDays: number = 7): Promise<number>
	{
		const cutoffDate = new Date();
		cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
		const cutoffIso = cutoffDate.toISOString();

		let cleanedCount = 0;

		for (const [jobId, job] of this.jobs.entries())
		{
			if ((job.status === 'completed' || job.status === 'failed') && job.createdAt < cutoffIso)
			{
				this.jobs.delete(jobId);
				cleanedCount++;
			}
		}

		this.logger.info({
			cleanedCount,
			olderThanDays,
			cutoffDate: cutoffIso,
			msg: 'Scheduler job cleanup completed',
		});

		return cleanedCount;
	}

	/**
	 * Get health status of scheduler service
	 */
	async getHealthStatus(): Promise<{
		isHealthy: boolean;
		isProcessing: boolean;
		runningJobs: number;
		pendingJobs: number;
		failedJobs: number;
		totalJobs: number;
		averageExecutionTime: number;
	}>
	{
		const metrics = this.getMetrics();

		return {
			isHealthy: this.isProcessing && this.runningJobs.size <= this.config.maxConcurrentJobs,
			isProcessing: this.isProcessing,
			runningJobs: this.runningJobs.size,
			pendingJobs: metrics.jobsByStatus.pending,
			failedJobs: metrics.jobsByStatus.failed,
			totalJobs: this.jobs.size,
			averageExecutionTime: metrics.averageExecutionTime,
		};
	}

	private async processJobs(): Promise<void>
	{
		if (this.runningJobs.size >= this.config.maxConcurrentJobs)
		{
			return; // Already at max concurrency
		}

		const availableSlots = this.config.maxConcurrentJobs - this.runningJobs.size;
		const pendingJobs = this.getPendingJobs(availableSlots);

		if (pendingJobs.length === 0)
		{
			return; // No jobs to process
		}

		this.logger.debug({
			pendingJobs: pendingJobs.length,
			runningJobs: this.runningJobs.size,
			availableSlots,
			msg: 'Processing scheduler jobs',
		});

		// Execute jobs concurrently
		const jobPromises = pendingJobs.map(job => this.executeJob(job.id));

		// Don't await all jobs - let them run in background
		Promise.allSettled(jobPromises).then((results) =>
		{
			const successful = results.filter(r => r.status === 'fulfilled').length;
			const failed = results.filter(r => r.status === 'rejected').length;

			this.logger.debug({
				total: results.length,
				successful,
				failed,
				msg: 'Batch job execution completed',
			});
		});
	}

	private async importPendingUpdates(): Promise<void>
	{
		try
		{
			const pendingUpdates = this.contentGenerator.getPendingUpdates();

			for (const update of pendingUpdates)
			{
				await this.createLiveContentUpgradeJob(
					update.domain,
					update.priority,
					update.scheduledFor ? new Date(update.scheduledFor) : undefined,
					update.reason,
				);
			}

			this.logger.info({
				importedCount: pendingUpdates.length,
				msg: 'Imported pending updates from content generator',
			});
		}
		catch (error)
		{
			this.logger.error({
				error: error.message,
				msg: 'Failed to import pending updates',
			});
		}
	}

	private updateMetrics(
		event: 'jobCreated' | 'jobStarted' | 'jobCompleted' | 'jobFailed' | 'jobRetried',
		job: SchedulerJob,
		executionTime?: number,
	): void
	{
		if (!this.config.enableMetrics)
		{
			return;
		}

		switch (event)
		{
			case 'jobCreated':
				this.metrics.totalJobsCreated++;
				this.metrics.jobsByPriority[job.priority]++;
				break;

			case 'jobCompleted':
				this.metrics.totalJobsCompleted++;
				if (job.type === 'live-content-upgrade')
				{
					this.metrics.contentUpgrades++;
				}
				if (executionTime)
				{
					const totalCompleted = this.metrics.totalJobsCompleted;
					this.metrics.averageExecutionTime =
						(this.metrics.averageExecutionTime * (totalCompleted - 1) + executionTime) / totalCompleted;
				}
				break;

			case 'jobFailed':
				this.metrics.totalJobsFailed++;
				break;

			case 'jobRetried':
				this.metrics.retryCount++;
				break;
		}
	}

	private sleep(ms: number): Promise<void>
	{

		return new Promise(resolve => setTimeout(resolve, ms));
	}
}

export type {
	SchedulerJobConfig,
	SchedulerJob,
	JobExecutionResult,
	SchedulerMetrics,
};

export default SchedulerIntegrationService;
