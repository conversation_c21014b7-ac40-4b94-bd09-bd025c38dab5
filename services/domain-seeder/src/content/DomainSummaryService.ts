import type { LoggerInstanceType } from '@shared';
import type { CategoryClassificationResultType, TagExtractionResultType } from './CategoryTagger';

type SummaryGenerationResultType =
{
	summary: string;
	wordCount: number;
	confidence: number;
	method: 'heuristic' | 'content-based' | 'hybrid';
	sources: string[];
	seoOptimized: boolean;
};

type SummaryGenerationOptionsType =
{
	minWords?: number;
	maxWords?: number;
	includeKeywords?: string[];
	seoOptimization?: boolean;
	language?: string;
	country?: string;
};

type ContentAnalysisType =
{
	title?: string;
	metaDescription?: string;
	ogTitle?: string;
	ogDescription?: string;
	headings: string[];
	mainContent: string;
	keyFeatures: string[];
	keywords: string[];
	language?: string;
	socialLinks?: string[];
	hasRobotsTxt?: boolean;
	hasSitemap?: boolean;
};

/**
 * DomainSummaryService for generating SEO-optimized domain descriptions
 * Supports both preGenerated (heuristic-based) and live analysis modes
 */
class DomainSummaryService
{
	private logger: LoggerInstanceType;

	private options: Required<SummaryGenerationOptionsType>;

	private templates: Record<string, string[]>;

	private seoKeywords: Record<string, string[]>;

	private brandPhrases: string[];

	constructor(logger: LoggerInstanceType, options: SummaryGenerationOptionsType = {})
	{
		this.logger = logger;
		this.options = {
			minWords: options.minWords ?? 320,
			maxWords: options.maxWords ?? 500,
			includeKeywords: options.includeKeywords ?? [],
			seoOptimization: options.seoOptimization ?? true,
			language: options.language ?? 'en',
			country: options.country ?? 'US',
		};

		this.templates = this.initializeTemplates();
		this.seoKeywords = this.initializeSeoKeywords();
		this.brandPhrases = this.initializeBrandPhrases();
	}

	/**
	 * Generate baseline summary using heuristic approach (preGenerated mode)
	 */
	async generateBaseline(
		domain: string,
		category: CategoryClassificationResultType,
		tags: TagExtractionResultType,
		options?: Partial<SummaryGenerationOptionsType>,
	): Promise<SummaryGenerationResultType>
	{
		try
		{
			const mergedOptions = { ...this.options, ...options };
			const domainTokens = this.tokenizeDomain(domain);
			const tld = this.extractTld(domain);

			// Generate summary using heuristic templates
			const summary = await this.generateHeuristicSummary(
				domain,
				domainTokens,
				tld,
				category,
				tags,
				mergedOptions,
			);

			const wordCount = this.countWords(summary);
			const confidence = this.calculateHeuristicConfidence(category, tags, wordCount);

			this.logger.info({
				domain,
				wordCount,
				confidence,
				category: category.primary,
				tagCount: tags.tags.length,
			}, 'Baseline summary generated');

			return {
				summary,
				wordCount,
				confidence,
				method: 'heuristic',
				sources: ['domain-analysis', 'category-classification', 'tag-extraction'],
				seoOptimized: mergedOptions.seoOptimization,
			};
		}
		catch (error)
		{
			this.logger.error({ error, domain }, 'Baseline summary generation failed');
			return this.generateFallbackSummary(domain, category);
		}
	}

	/**
	 * Generate summary from live content analysis
	 */
	async generateFromContent(
		domain: string,
		category: CategoryClassificationResultType,
		tags: TagExtractionResultType,
		contentAnalysis: ContentAnalysisType,
		options?: Partial<SummaryGenerationOptionsType>,
	): Promise<SummaryGenerationResultType>
	{
		try
		{
			const mergedOptions = { ...this.options, ...options };

			// Generate content-based summary
			const summary = await this.generateContentBasedSummary(
				domain,
				category,
				tags,
				contentAnalysis,
				mergedOptions,
			);

			const wordCount = this.countWords(summary);
			const confidence = this.calculateContentConfidence(contentAnalysis, wordCount);

			this.logger.info({
				domain,
				wordCount,
				confidence,
				hasTitle: !!contentAnalysis.title,
				hasMetaDescription: !!contentAnalysis.metaDescription,
				contentLength: contentAnalysis.mainContent.length,
			}, 'Content-based summary generated');

			return {
				summary,
				wordCount,
				confidence,
				method: 'content-based',
				sources: ['live-content', 'meta-data', 'content-analysis'],
				seoOptimized: mergedOptions.seoOptimization,
			};
		}
		catch (error)
		{
			this.logger.error({ error, domain }, 'Content-based summary generation failed');
			// Fallback to baseline generation
			return this.generateBaseline(domain, category, tags, options);
		}
	}

	private async generateHeuristicSummary(
		domain: string,
		tokens: string[],
		tld: string,
		category: CategoryClassificationResultType,
		tags: TagExtractionResultType,
		options: Required<SummaryGenerationOptionsType>,
	): Promise<string>
	{
		const sections: string[] = [];

		// Introduction section
		const intro = this.generateIntroSection(domain, tokens, category);
		sections.push(intro);

		// Service/offering section
		const offerings = this.generateOfferingsSection(domain, category, tags);
		sections.push(offerings);

		// Benefits section
		const benefits = this.generateBenefitsSection(category, tags);
		sections.push(benefits);

		// Features section
		const features = this.generateFeaturesSection(category, tags);
		sections.push(features);

		// Trust/credibility section
		const trust = this.generateTrustSection(domain, tld, category);
		sections.push(trust);

		// Call-to-action section
		const cta = this.generateCtaSection(domain, category);
		sections.push(cta);

		let summary = sections.join(' ');

		// Apply SEO optimization
		if (options.seoOptimization)
		{
			summary = this.applySeoOptimization(summary, domain, category, tags, options);
		}

		// Ensure minimum word count
		if (this.countWords(summary) < options.minWords)
		{
			summary = this.expandSummary(summary, domain, category, tags, options);
		}

		// Ensure maximum word count
		if (this.countWords(summary) > options.maxWords)
		{
			summary = this.truncateSummary(summary, options.maxWords);
		}

		return summary;
	}

	private async generateContentBasedSummary(
		domain: string,
		category: CategoryClassificationResultType,
		tags: TagExtractionResultType,
		content: ContentAnalysisType,
		options: Required<SummaryGenerationOptionsType>,
	): Promise<string>
	{
		const sections: string[] = [];

		// Use actual title and meta description if available
		if (content.title)
		{
			const intro = this.generateContentIntro(domain, content.title, content.metaDescription, category);
			sections.push(intro);
		}
		else
		{
			const tokens = this.tokenizeDomain(domain);
			const intro = this.generateIntroSection(domain, tokens, category);
			sections.push(intro);
		}

		// Extract and describe key features from content
		if (content.keyFeatures.length > 0)
		{
			const features = this.generateContentFeaturesSection(content.keyFeatures);
			sections.push(features);
		}
		else
		{
			const features = this.generateFeaturesSection(category, tags);
			sections.push(features);
		}

		// Generate offerings based on content analysis
		const offerings = this.generateContentOfferingsSection(content, category);
		sections.push(offerings);

		// Benefits section based on content keywords
		const benefits = this.generateContentBenefitsSection(content.keywords, category);
		sections.push(benefits);

		// Trust section with actual content insights
		const trust = this.generateContentTrustSection(domain, content, category);
		sections.push(trust);

		// CTA section
		const cta = this.generateCtaSection(domain, category);
		sections.push(cta);

		let summary = sections.join(' ');

		// Apply SEO optimization with content keywords
		if (options.seoOptimization)
		{
			const contentKeywords = [...content.keywords, ...tags.tags];
			summary = this.applySeoOptimization(summary, domain, category, { ...tags, tags: contentKeywords }, options);
		}

		// Ensure word count requirements
		if (this.countWords(summary) < options.minWords)
		{
			summary = this.expandContentSummary(summary, content, domain, category, options);
		}

		if (this.countWords(summary) > options.maxWords)
		{
			summary = this.truncateSummary(summary, options.maxWords);
		}

		return summary;
	}

	private generateIntroSection(domain: string, tokens: string[], category: CategoryClassificationResultType): string
	{
		const templates = this.templates[category.primary] || this.templates.business;
		const template = this.selectRandomTemplate(templates);

		const brandName = this.extractBrandName(domain, tokens);
		const categoryName = this.getCategoryDisplayName(category.primary);

		return template
			.replace('{domain}', domain)
			.replace('{brandName}', brandName)
			.replace('{category}', categoryName)
			.replace('{primaryCategory}', categoryName);
	}

	private generateContentIntro(
		domain: string,
		title: string,
		metaDescription: string | undefined,
		category: CategoryClassificationResultType,
	): string
	{
		const categoryName = this.getCategoryDisplayName(category.primary);

		if (metaDescription && metaDescription.length > 50)
		{
			return `${title} is a leading ${categoryName} platform that ${metaDescription.toLowerCase()}. The website offers comprehensive solutions designed to meet the diverse needs of users seeking reliable ${categoryName} services.`;
		}

		return `${title}, accessible at ${domain}, represents a prominent ${categoryName} platform. The service provides innovative solutions and services tailored to deliver exceptional user experiences in the ${categoryName} sector.`;
	}

	private generateOfferingsSection(domain: string, category: CategoryClassificationResultType, tags: TagExtractionResultType): string
	{
		const categoryName = this.getCategoryDisplayName(category.primary);
		const relevantTags = tags.tags.slice(0, 4);

		const offerings = [
			`comprehensive ${categoryName} solutions`,
			`innovative ${categoryName} services`,
			`professional ${categoryName} tools`,
			`advanced ${categoryName} features`,
		];

		if (relevantTags.length > 0)
		{
			const tagServices = relevantTags.map(tag => `${tag.replace('-', ' ')} services`);
			offerings.push(...tagServices.slice(0, 2));
		}

		return `The platform specializes in delivering ${offerings.slice(0, 3).join(', ')}, ensuring users have access to cutting-edge technology and reliable support. Through its comprehensive suite of offerings, ${domain} addresses the evolving needs of modern ${categoryName} requirements.`;
	}

	private generateContentOfferingsSection(content: ContentAnalysisType, category: CategoryClassificationResultType): string
	{
		const categoryName = this.getCategoryDisplayName(category.primary);

		if (content.keyFeatures.length > 0)
		{
			const features = content.keyFeatures.slice(0, 3).join(', ');
			return `The platform offers a comprehensive range of services including ${features}. These ${categoryName} solutions are designed to provide users with reliable, efficient, and innovative tools that meet industry standards and exceed expectations.`;
		}

		if (content.keywords.length > 0)
		{
			const keywords = content.keywords.slice(0, 4).join(', ');
			return `Specializing in ${keywords}, the platform delivers professional ${categoryName} services that combine expertise with cutting-edge technology. Users benefit from comprehensive solutions tailored to their specific requirements and industry needs.`;
		}

		return this.generateOfferingsSection('', category, { tags: [], confidence: 0.5, sources: [] });
	}

	private generateBenefitsSection(category: CategoryClassificationResultType, tags: TagExtractionResultType): string
	{
		const categoryName = this.getCategoryDisplayName(category.primary);
		const benefits = this.getCategoryBenefits(category.primary);

		return `Users benefit from ${benefits.join(', ')}, making it an ideal choice for individuals and organizations seeking reliable ${categoryName} solutions. The platform's commitment to excellence ensures consistent performance, security, and user satisfaction across all service offerings.`;
	}

	private generateContentBenefitsSection(keywords: string[], category: CategoryClassificationResultType): string
	{
		const categoryName = this.getCategoryDisplayName(category.primary);
		const keywordBenefits = keywords.slice(0, 3).map(keyword => `enhanced ${keyword} capabilities`);

		if (keywordBenefits.length > 0)
		{
			return `Users experience significant advantages including ${keywordBenefits.join(', ')}, along with improved efficiency and streamlined workflows. The platform's focus on ${categoryName} excellence delivers measurable value through innovative features and reliable performance.`;
		}

		return this.generateBenefitsSection(category, { tags: [], confidence: 0.5, sources: [] });
	}

	private generateFeaturesSection(category: CategoryClassificationResultType, tags: TagExtractionResultType): string
	{
		const features = this.getCategoryFeatures(category.primary);
		const tagFeatures = tags.tags.slice(0, 3).map(tag => `${tag.replace('-', ' ')} integration`);

		const allFeatures = [...features, ...tagFeatures].slice(0, 5);

		return `Key features include ${allFeatures.join(', ')}, providing users with a comprehensive toolkit for success. The platform's robust architecture ensures scalability, reliability, and seamless integration with existing workflows and systems.`;
	}

	private generateContentFeaturesSection(keyFeatures: string[]): string
	{
		const features = keyFeatures.slice(0, 5);
		return `The platform's standout features include ${features.join(', ')}, each designed to enhance user productivity and deliver exceptional results. These capabilities work together to create a comprehensive solution that addresses diverse user requirements and industry challenges.`;
	}

	private generateTrustSection(domain: string, tld: string, category: CategoryClassificationResultType): string
	{
		const trustIndicators = this.getTrustIndicators(tld, category.primary);
		const categoryName = this.getCategoryDisplayName(category.primary);

		return `${domain} maintains high standards of ${trustIndicators.join(', ')}, establishing itself as a trusted resource in the ${categoryName} industry. The platform's commitment to quality and user satisfaction is reflected in its comprehensive approach to service delivery and customer support.`;
	}

	private generateContentTrustSection(domain: string, content: ContentAnalysisType, category: CategoryClassificationResultType): string
	{
		const categoryName = this.getCategoryDisplayName(category.primary);
		const hasRichContent = content.mainContent.length > 1000;
		const hasStructuredContent = content.headings.length > 3;

		if (hasRichContent && hasStructuredContent)
		{
			return `${domain} demonstrates expertise through comprehensive content and well-structured information architecture, establishing credibility in the ${categoryName} sector. The platform's detailed approach to content presentation reflects its commitment to providing valuable, authoritative resources for users.`;
		}

		return this.generateTrustSection(domain, this.extractTld(domain), category);
	}

	private generateCtaSection(domain: string, category: CategoryClassificationResultType): string
	{
		const categoryName = this.getCategoryDisplayName(category.primary);
		const actions = this.getCategoryActions(category.primary);

		return `Whether you're looking to ${actions.join(', ')}, ${domain} provides the tools and expertise needed to achieve your ${categoryName} objectives. Explore the platform today to discover how these comprehensive solutions can transform your approach to ${categoryName} challenges.`;
	}

	private applySeoOptimization(
		summary: string,
		domain: string,
		category: CategoryClassificationResultType,
		tags: TagExtractionResultType,
		options: Required<SummaryGenerationOptionsType>,
	): string
	{
		let optimized = summary;

		// Ensure domain appears early in summary
		if (!optimized.toLowerCase().includes(domain.toLowerCase()))
		{
			// Prepend domain to the beginning
			optimized = `${domain} ${optimized}`;
		}
		else if (!optimized.substring(0, 100).toLowerCase().includes(domain.toLowerCase()))
		{
			// If domain is not in first 100 characters, move it to the beginning
			optimized = optimized.replace(new RegExp(domain, 'gi'), '');
			optimized = `${domain} ${optimized}`;
		}

		// Integrate SEO keywords naturally
		const seoKeywords = this.getSeoKeywords(category.primary);
		const targetKeywords = [...seoKeywords, ...tags.tags.slice(0, 3), ...options.includeKeywords];

		for (const keyword of targetKeywords.slice(0, 4))
		{
			if (!optimized.toLowerCase().includes(keyword.toLowerCase()))
			{
				optimized = this.integrateKeywordNaturally(optimized, keyword, category);
			}
		}

		// Ensure active voice and readability
		optimized = this.improveReadability(optimized);

		return optimized;
	}

	private expandSummary(
		summary: string,
		domain: string,
		category: CategoryClassificationResultType,
		tags: TagExtractionResultType,
		options: Required<SummaryGenerationOptionsType>,
	): string
	{
		const currentWords = this.countWords(summary);
		const wordsNeeded = options.minWords - currentWords;

		if (wordsNeeded <= 0) return summary;

		const expansions: string[] = [];

		// Add industry context
		const industryContext = this.generateIndustryContext(category);
		expansions.push(industryContext);

		// Add technical details
		const technicalDetails = this.generateTechnicalDetails(category, tags);
		expansions.push(technicalDetails);

		// Add user benefits
		const additionalBenefits = this.generateAdditionalBenefits(category);
		expansions.push(additionalBenefits);

		// Add competitive advantages
		const advantages = this.generateCompetitiveAdvantages(domain, category);
		expansions.push(advantages);

		// Add more detailed feature descriptions
		const detailedFeatures = this.generateDetailedFeatures(category, tags);
		expansions.push(detailedFeatures);

		// Add use case examples
		const useCases = this.generateUseCases(category);
		expansions.push(useCases);

		let expanded = summary;
		for (const expansion of expansions)
		{
			expanded += ` ${expansion}`;
			if (this.countWords(expanded) >= options.minWords) break;
		}

		// If still not enough words, repeat some expansions with variations
		if (this.countWords(expanded) < options.minWords)
		{
			const additionalContent = this.generateAdditionalContent(domain, category, wordsNeeded);
			expanded += ` ${additionalContent}`;
		}

		return expanded;
	}

	private expandContentSummary(
		summary: string,
		content: ContentAnalysisType,
		domain: string,
		category: CategoryClassificationResultType,
		options: Required<SummaryGenerationOptionsType>,
	): string
	{
		const currentWords = this.countWords(summary);
		const wordsNeeded = options.minWords - currentWords;

		if (wordsNeeded <= 0) return summary;

		const expansions: string[] = [];

		// Add content-based insights
		if (content.headings.length > 0)
		{
			const headingInsights = this.generateHeadingInsights(content.headings, category);
			expansions.push(headingInsights);
		}

		// Add keyword-based context
		if (content.keywords.length > 0)
		{
			const keywordContext = this.generateKeywordContext(content.keywords, category);
			expansions.push(keywordContext);
		}

		// Add standard expansions
		const industryContext = this.generateIndustryContext(category);
		expansions.push(industryContext);

		// Add technical details
		const technicalDetails = this.generateTechnicalDetails(category, { tags: content.keywords, confidence: 0.7, sources: ['content'] });
		expansions.push(technicalDetails);

		// Add additional benefits
		const additionalBenefits = this.generateAdditionalBenefits(category);
		expansions.push(additionalBenefits);

		// Add competitive advantages
		const advantages = this.generateCompetitiveAdvantages(domain, category);
		expansions.push(advantages);

		let expanded = summary;
		for (const expansion of expansions)
		{
			expanded += ` ${expansion}`;
			if (this.countWords(expanded) >= options.minWords) break;
		}

		// If still not enough words, add more content
		if (this.countWords(expanded) < options.minWords)
		{
			const additionalContent = this.generateAdditionalContent(domain, category, wordsNeeded);
			expanded += ` ${additionalContent}`;
		}

		return expanded;
	}

	private truncateSummary(summary: string, maxWords: number): string
	{
		const words = summary.split(/\s+/);
		if (words.length <= maxWords) return summary;

		// Find the last complete sentence within the word limit
		const truncated = words.slice(0, maxWords).join(' ');
		const lastSentenceEnd = Math.max(
			truncated.lastIndexOf('.'),
			truncated.lastIndexOf('!'),
			truncated.lastIndexOf('?'),
		);

		if (lastSentenceEnd > truncated.length * 0.8)
		{
			return truncated.substring(0, lastSentenceEnd + 1);
		}

		return `${truncated.trim()}.`;
	}

	private calculateHeuristicConfidence(
		category: CategoryClassificationResultType,
		tags: TagExtractionResultType,
		wordCount: number,
	): number
	{
		let confidence = 0.4; // Base confidence for heuristic generation

		// Boost based on category confidence
		confidence += category.confidence * 0.15;

		// Boost based on tag quality and count
		confidence += tags.confidence * 0.1;
		if (tags.tags.length >= 8) confidence += 0.1;
		if (tags.sources.length >= 3) confidence += 0.05;

		// Boost based on word count meeting requirements
		if (wordCount >= this.options.minWords) confidence += 0.15;

		return Math.min(confidence, 0.8); // Cap heuristic confidence
	}

	private calculateContentConfidence(content: ContentAnalysisType, wordCount: number): number
	{
		let confidence = 0.6; // Base confidence for content-based generation

		// Boost based on content richness
		if (content.title) confidence += 0.1;
		if (content.metaDescription) confidence += 0.1;
		if (content.keyFeatures.length > 0) confidence += 0.1;
		if (content.mainContent.length > 1000) confidence += 0.1;

		// Boost based on word count
		if (wordCount >= this.options.minWords) confidence += 0.1;

		return Math.min(confidence, 1.0);
	}

	private generateFallbackSummary(domain: string, category: CategoryClassificationResultType | null): SummaryGenerationResultType
	{
		const categoryName = category ? this.getCategoryDisplayName(category.primary) : 'business';
		const summary = `${domain} is a ${categoryName} website that provides various services and solutions. The platform offers users access to comprehensive tools and resources designed to meet their needs. With a focus on quality and reliability, ${domain} serves as a valuable resource for individuals and organizations seeking ${categoryName} solutions. The website features user-friendly navigation and professional presentation of information. Users can explore various offerings and services through the platform's organized structure. ${domain} maintains standards of service delivery and user experience that reflect industry best practices. The platform continues to evolve and adapt to meet changing user requirements and market demands. Whether for personal or professional use, ${domain} provides accessible solutions in the ${categoryName} sector.`;

		return {
			summary,
			wordCount: this.countWords(summary),
			confidence: 0.2,
			method: 'heuristic',
			sources: ['fallback'],
			seoOptimized: false,
		};
	}

	// Helper methods
	private tokenizeDomain(domain: string): string[]
	{
		const withoutTld = domain.split('.')[0];
		return withoutTld
			.split(/[-_.]/)
			.filter(token => token.length > 1)
			.map(token => token.toLowerCase());
	}

	private extractTld(domain: string): string
	{
		const parts = domain.split('.');
		return parts[parts.length - 1].toLowerCase();
	}

	private extractBrandName(domain: string, tokens: string[]): string
	{
		// Try to identify the main brand name from tokens
		const longestToken = tokens.reduce((a, b) => (a.length > b.length ? a : b), '');
		return longestToken.charAt(0).toUpperCase() + longestToken.slice(1);
	}

	private extractBrandNameFromTitle(title: string): string | null
	{
		// Extract potential brand name from title
		const words = title.split(/\s+/);
		if (words.length > 0)
		{
			return words[0];
		}
		return null;
	}

	private countWords(text: string): number
	{
		return text.trim().split(/\s+/).filter(word => word.length > 0).length;
	}

	private selectRandomTemplate(templates: string[]): string
	{
		return templates[Math.floor(Math.random() * templates.length)];
	}

	private getCategoryDisplayName(category: string): string
	{
		return category.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase());
	}

	private getCategoryBenefits(category: string): string[]
	{
		const benefitMap: Record<string, string[]> = {
			technology: ['enhanced productivity', 'streamlined workflows', 'innovative solutions', 'scalable architecture'],
			business: ['improved efficiency', 'cost-effective solutions', 'professional service', 'reliable support'],
			'e-commerce': ['secure transactions', 'user-friendly interface', 'comprehensive catalog', 'fast delivery'],
			education: ['comprehensive learning', 'expert instruction', 'flexible scheduling', 'practical application'],
			health: ['professional care', 'evidence-based treatment', 'personalized service', 'comprehensive support'],
			finance: ['secure transactions', 'competitive rates', 'expert guidance', 'transparent processes'],
		};

		return benefitMap[category] || benefitMap.business;
	}

	private getCategoryFeatures(category: string): string[]
	{
		const featureMap: Record<string, string[]> = {
			technology: ['advanced analytics', 'cloud integration', 'API access', 'real-time monitoring'],
			business: ['project management', 'team collaboration', 'reporting tools', 'customer support'],
			'e-commerce': ['shopping cart', 'payment processing', 'inventory management', 'order tracking'],
			education: ['course catalog', 'progress tracking', 'interactive content', 'certification'],
			health: ['appointment scheduling', 'patient records', 'treatment planning', 'health monitoring'],
			finance: ['account management', 'transaction history', 'financial planning', 'investment tools'],
		};

		return featureMap[category] || featureMap.business;
	}

	private getTrustIndicators(tld: string, category: string): string[]
	{
		const indicators = ['security', 'reliability', 'professional standards'];

		if (tld === 'org') indicators.push('transparency', 'accountability');
		if (tld === 'edu') indicators.push('academic excellence', 'educational integrity');
		if (tld === 'gov') indicators.push('official authority', 'public service');

		if (category === 'finance') indicators.push('regulatory compliance', 'data protection');
		if (category === 'health') indicators.push('privacy protection', 'medical standards');

		return indicators;
	}

	private getCategoryActions(category: string): string[]
	{
		const actionMap: Record<string, string[]> = {
			technology: ['streamline operations', 'enhance productivity', 'implement solutions'],
			business: ['improve efficiency', 'grow your business', 'optimize processes'],
			'e-commerce': ['increase sales', 'expand market reach', 'enhance customer experience'],
			education: ['advance learning', 'develop skills', 'achieve certification'],
			health: ['improve wellness', 'access care', 'maintain health'],
			finance: ['manage finances', 'plan investments', 'secure future'],
		};

		return actionMap[category] || actionMap.business;
	}

	private getSeoKeywords(category: string): string[]
	{
		return this.seoKeywords[category] || this.seoKeywords.business;
	}

	private integrateKeywordNaturally(summary: string, keyword: string, category: CategoryClassificationResultType): string
	{
		// Simple keyword integration - could be enhanced with NLP
		const sentences = summary.split('. ');
		const targetSentence = Math.floor(sentences.length / 3); // Earlier in content

		if (targetSentence < sentences.length)
		{
			const sentence = sentences[targetSentence];

			// Try different integration approaches
			if (sentence.includes('platform'))
			{
				const enhanced = sentence.replace('platform', `${keyword} platform`);
				sentences[targetSentence] = enhanced;
			}
			else if (sentence.includes('solutions'))
			{
				const enhanced = sentence.replace('solutions', `${keyword} solutions`);
				sentences[targetSentence] = enhanced;
			}
			else if (sentence.includes(category.primary))
			{
				const enhanced = sentence.replace(category.primary, `${keyword} ${category.primary}`);
				sentences[targetSentence] = enhanced;
			}
			else
			{
				// Add keyword at the beginning of the sentence
				sentences[targetSentence] = `${keyword.charAt(0).toUpperCase() + keyword.slice(1)} ${sentence.toLowerCase()}`;
			}
		}

		return sentences.join('. ');
	}

	private improveReadability(text: string): string
	{
		// Basic readability improvements
		return text
			.replace(/\s+/g, ' ')
			.replace(/\.\s*\./g, '.')
			.replace(/,\s*,/g, ',')
			.trim();
	}

	private generateIndustryContext(category: CategoryClassificationResultType): string
	{
		const categoryName = this.getCategoryDisplayName(category.primary);
		return `The ${categoryName} industry continues to evolve with technological advancements and changing user expectations. Modern platforms in this sector focus on delivering comprehensive solutions that combine innovation with reliability, ensuring users receive maximum value from their investments.`;
	}

	private generateTechnicalDetails(category: CategoryClassificationResultType, tags: TagExtractionResultType): string
	{
		const categoryName = this.getCategoryDisplayName(category.primary);
		const techTags = tags.tags.filter(tag => tag.includes('tech') || tag.includes('api') || tag.includes('cloud'));

		if (techTags.length > 0)
		{
			return `The platform leverages advanced ${techTags.slice(0, 2).join(' and ')} technologies to deliver superior performance and scalability. This technical foundation ensures reliable service delivery and supports future growth requirements.`;
		}

		return `Built on modern architecture principles, the platform incorporates industry-standard technologies and best practices to ensure optimal performance, security, and user experience across all ${categoryName} services.`;
	}

	private generateAdditionalBenefits(category: CategoryClassificationResultType): string
	{
		const categoryName = this.getCategoryDisplayName(category.primary);
		return `Additional advantages include comprehensive documentation, responsive customer support, and regular updates that keep the platform current with industry standards. These elements combine to create a superior ${categoryName} experience that exceeds user expectations.`;
	}

	private generateCompetitiveAdvantages(domain: string, category: CategoryClassificationResultType): string
	{
		const categoryName = this.getCategoryDisplayName(category.primary);
		return `${domain} distinguishes itself through its commitment to innovation, user-centric design, and comprehensive approach to ${categoryName} solutions. The platform's focus on quality and continuous improvement ensures it remains at the forefront of industry developments.`;
	}

	private generateHeadingInsights(headings: string[], category: CategoryClassificationResultType): string
	{
		const categoryName = this.getCategoryDisplayName(category.primary);
		const topHeadings = headings.slice(0, 3);

		return `The platform's comprehensive approach is evident through its focus on ${topHeadings.join(', ')}, demonstrating expertise across multiple aspects of ${categoryName} service delivery. This structured presentation ensures users can easily access relevant information and services.`;
	}

	private generateKeywordContext(keywords: string[], category: CategoryClassificationResultType): string
	{
		const categoryName = this.getCategoryDisplayName(category.primary);
		const topKeywords = keywords.slice(0, 4);

		return `With emphasis on ${topKeywords.join(', ')}, the platform addresses key areas of ${categoryName} concern and opportunity. This focused approach ensures users receive targeted solutions that align with their specific requirements and objectives.`;
	}

	private generateDetailedFeatures(category: CategoryClassificationResultType, tags: TagExtractionResultType): string
	{
		const categoryName = this.getCategoryDisplayName(category.primary);
		const features = this.getCategoryFeatures(category.primary);
		const tagFeatures = tags.tags.slice(0, 2).map(tag => `${tag.replace('-', ' ')} capabilities`);

		return `The platform's advanced features encompass ${features.slice(0, 3).join(', ')}, complemented by ${tagFeatures.join(' and ')}. These sophisticated tools work in harmony to deliver comprehensive ${categoryName} solutions that meet the highest industry standards and user expectations.`;
	}

	private generateUseCases(category: CategoryClassificationResultType): string
	{
		const categoryName = this.getCategoryDisplayName(category.primary);
		const useCaseMap: Record<string, string[]> = {
			technology: ['software development', 'system integration', 'digital transformation'],
			business: ['process optimization', 'team collaboration', 'strategic planning'],
			'e-commerce': ['online sales', 'inventory management', 'customer engagement'],
			education: ['skill development', 'knowledge sharing', 'professional training'],
			health: ['patient care', 'health monitoring', 'wellness programs'],
		};

		const useCases = useCaseMap[category.primary] || useCaseMap.business;
		return `Common use cases include ${useCases.join(', ')}, demonstrating the platform's versatility and practical application across diverse ${categoryName} scenarios. Organizations of all sizes benefit from these flexible implementation options.`;
	}

	private generateAdditionalContent(domain: string, category: CategoryClassificationResultType, wordsNeeded: number): string
	{
		const categoryName = this.getCategoryDisplayName(category.primary);
		const content = [
			`The ${categoryName} landscape continues to evolve rapidly, with ${domain} staying at the forefront of innovation and best practices.`,
			`Users consistently report improved outcomes and enhanced productivity when utilizing the platform's comprehensive ${categoryName} capabilities.`,
			`The commitment to continuous improvement ensures that ${domain} remains a valuable long-term partner for ${categoryName} success.`,
			'Integration capabilities allow seamless connectivity with existing systems and workflows, maximizing the value of current technology investments.',
			`Professional support and comprehensive documentation ensure users can quickly realize the full potential of the platform's ${categoryName} features.`,
		];

		let additional = '';
		let wordCount = 0;
		for (const sentence of content)
		{
			additional += ` ${sentence}`;
			wordCount += this.countWords(sentence);
			if (wordCount >= wordsNeeded) break;
		}

		return additional.trim();
	}

	private initializeTemplates(): Record<string, string[]>
	{
		return {
			technology: [
				'{domain} is a cutting-edge {category} platform that delivers innovative solutions for modern businesses and developers.',
				'{brandName} represents a leading {category} service provider, offering comprehensive tools and technologies for digital transformation.',
				'At {domain}, users discover advanced {category} solutions designed to streamline operations and enhance productivity.',
			],
			business: [
				'{domain} serves as a comprehensive {category} platform, providing professional services and solutions for organizations of all sizes.',
				'{brandName} offers reliable {category} services that help businesses achieve their operational and strategic objectives.',
				'Through {domain}, users access professional {category} tools and expertise designed to drive business success.',
			],
			'e-commerce': [
				'{domain} provides a complete {category} solution, enabling businesses to sell products and services online effectively.',
				'{brandName} offers comprehensive {category} tools that help merchants create successful online stores and marketplaces.',
				'At {domain}, users find everything needed to build and manage successful {category} operations.',
			],
			education: [
				'{domain} delivers comprehensive {category} resources and learning opportunities for students and professionals.',
				'{brandName} provides quality {category} services that support learning, development, and academic achievement.',
				'Through {domain}, learners access professional {category} content and tools designed to enhance knowledge and skills.',
			],
			health: [
				'{domain} offers professional {category} services and resources designed to support wellness and medical care.',
				'{brandName} provides comprehensive {category} solutions that prioritize patient care and health outcomes.',
				'At {domain}, users find reliable {category} information and services from qualified professionals.',
			],
			finance: [
				'{domain} delivers professional {category} services and tools for individuals and businesses seeking financial solutions.',
				'{brandName} offers comprehensive {category} services that help users manage money, investments, and financial planning.',
				'Through {domain}, users access secure {category} tools and expert guidance for their financial needs.',
			],
		};
	}

	private initializeSeoKeywords(): Record<string, string[]>
	{
		return ({
			technology: ['software', 'platform', 'solutions', 'tools', 'services', 'innovation'],
			business: ['professional', 'services', 'solutions', 'management', 'consulting', 'expertise'],
			'e-commerce': ['online', 'shopping', 'store', 'products', 'marketplace', 'retail'],
			education: ['learning', 'courses', 'training', 'education', 'skills', 'knowledge'],
			health: ['healthcare', 'medical', 'wellness', 'treatment', 'care', 'health'],
			finance: ['financial', 'money', 'investment', 'banking', 'planning', 'wealth'],
		});
	}

	private initializeBrandPhrases(): string[]
	{
		return ([
			'industry leader',
			'trusted provider',
			'innovative solution',
			'comprehensive platform',
			'professional service',
			'reliable partner',
			'expert team',
			'cutting-edge technology',
			'proven results',
			'customer-focused',
		]);
	}
}

export type {
	SummaryGenerationResultType,
	SummaryGenerationOptionsType,
	ContentAnalysisType,
};

export default DomainSummaryService;
