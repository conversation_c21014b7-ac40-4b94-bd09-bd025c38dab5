import type { LoggerInstanceType } from '@shared';
import type { CategoryClassificationResultType, TagExtractionResultType } from './CategoryTagger';
// import type { SummaryGenerationResult } from './DomainSummaryService';

type PreGeneratedContentOptions =
{
	minWords?: number;
	maxWords?: number;
	minTags?: number;
	maxTags?: number;
	seoOptimization?: boolean;
	language?: string;
	country?: string;
};

type PreGeneratedContent =
{
	summary: string;
	category: {
		primary: string;
		secondary?: string;
	};
	tags: string[];
	seoSummary: string;
	wordCount: number;
	confidence: number;
	method: 'preGenerated';
	sources: string[];
	metadata: {
		generatedAt: string;
		version: string;
		inputs: string[];
	};
};

type DomainAnalysis =
{
	domain: string;
	tokens: string[];
	tld: string;
	brandName: string;
	isInternational: boolean;
	language?: string;
	country?: string;
};

/**
 * PreGeneratedContentGenerator creates comprehensive domain content using heuristic analysis
 * without requiring live website fetching. Generates 320+ word descriptions with SEO optimization.
 */
class PreGeneratedContentGenerator
{
	private logger: LoggerInstanceType;

	private options: Required<PreGeneratedContentOptions>;

	private templates: Record<string, string[]>;

	private seoKeywords: Record<string, string[]>;

	private brandIndicators: Set<string>;

	private tldCategoryPriors: Record<string, string[]>;

	private taxonomySynonyms: Record<string, string[]>;

	private contentExpansions: Record<string, string[]>;

	constructor(
		logger: LoggerInstanceType,
		options: PreGeneratedContentOptions = {},
	)
	{
		this.logger = logger;
		this.options = {
			minWords: options.minWords ?? 320,
			maxWords: options.maxWords ?? 500,
			minTags: options.minTags ?? 5,
			maxTags: options.maxTags ?? 12,
			seoOptimization: options.seoOptimization ?? true,
			language: options.language ?? 'en',
			country: options.country ?? 'US',
		};

		this.templates = this.initializeTemplates();
		this.seoKeywords = this.initializeSeoKeywords();
		this.brandIndicators = this.initializeBrandIndicators();
		this.tldCategoryPriors = this.initializeTldCategoryPriors();
		this.taxonomySynonyms = this.initializeTaxonomySynonyms();
		this.contentExpansions = this.initializeContentExpansions();
	}

	/**
	 * Generate comprehensive preGenerated content for a domain
	 */
	async generateContent(
		domain: string,
		categoryResult?: CategoryClassificationResultType,
		tagResult?: TagExtractionResultType,
		options?: Partial<PreGeneratedContentOptions>,
	): Promise<PreGeneratedContent>
	{
		try
		{
			const mergedOptions = { ...this.options, ...options };
			const analysis = this.analyzeDomain(domain);

			// Use provided results or generate new ones
			const category = categoryResult || await this.classifyDomain(analysis);
			const tags = tagResult || await this.extractDomainTags(analysis, category);

			// Generate comprehensive description
			const summary = await this.generateDescription(analysis, category, tags, mergedOptions);

			// Generate SEO summary (shorter version for meta descriptions)
			const seoSummary = this.generateSeoSummary(analysis, category, tags);

			// Ensure tag requirements are met with merged options
			const finalTags = this.ensureTagRequirementsWithOptions(tags.tags, analysis, category, mergedOptions);

			const wordCount = this.countWords(summary);
			const confidence = this.calculateConfidence(category, tags, wordCount, analysis);

			this.logger.info({
				domain,
				wordCount,
				confidence,
				category: category.primary,
				tagCount: finalTags.length,
			}, 'PreGenerated content generated successfully');

			return {
				summary,
				category: {
					primary: category.primary,
					secondary: category.secondary,
				},
				tags: finalTags,
				seoSummary,
				wordCount,
				confidence,
				method: 'preGenerated',
				sources: ['domain-analysis', 'heuristics', 'taxonomy'],
				metadata: {
					generatedAt: new Date().toISOString(),
					version: '1.0.0',
					inputs: ['domain-tokens', 'tld-analysis', 'brand-detection'],
				},
			};
		}
		catch (error)
		{
			this.logger.error({ domain, error }, 'PreGenerated content generation failed');
			return this.generateFallbackContent(domain, options);
		}
	}

	private analyzeDomain(domain: string): DomainAnalysis
	{
		const tokens = this.tokenizeDomain(domain);
		const tld = this.extractTld(domain);
		const brandName = this.extractBrandName(domain, tokens);
		const isInternational = this.detectInternationalDomain(domain, tld);

		return {
			domain,
			tokens,
			tld,
			brandName,
			isInternational,
			language: isInternational ? this.detectLanguage(domain, tld) : this.options.language,
			country: isInternational ? this.detectCountry(tld) : this.options.country,
		};
	}

	private async classifyDomain(analysis: DomainAnalysis): Promise<CategoryClassificationResultType>
	{
		const reasoning: string[] = [];
		let primaryCategory = 'business';
		let secondaryCategory: string | undefined;
		let confidence = 0.3;

		// TLD-based classification
		const tldCategories = this.tldCategoryPriors[analysis.tld];
		if (tldCategories && tldCategories.length > 0)
		{
			primaryCategory = tldCategories[0];
			if (tldCategories.length > 1) secondaryCategory = tldCategories[1];
			confidence += 0.2;
			reasoning.push(`TLD .${analysis.tld} suggests ${primaryCategory}`);
		}

		// Token-based classification with enhanced patterns
		const tokenClassification = this.classifyFromTokens(analysis.tokens);
		if (tokenClassification.confidence > confidence)
		{
			primaryCategory = tokenClassification.primary;
			secondaryCategory = tokenClassification.secondary;
			confidence = tokenClassification.confidence;
			reasoning.push(...tokenClassification.reasoning);
		}

		// Brand detection boost
		if (this.detectBrand(analysis.tokens))
		{
			confidence += 0.15;
			reasoning.push('Brand indicators detected');
			// Most brands in our data are technology companies
			if (primaryCategory === 'business')
			{
				primaryCategory = 'technology';
				reasoning.push('Brand suggests technology category');
			}
		}

		// International domain adjustments
		if (analysis.isInternational)
		{
			confidence += 0.1;
			reasoning.push('International domain detected');
		}

		return {
			primary: primaryCategory,
			secondary: secondaryCategory,
			confidence: Math.min(confidence, 1.0),
			reasoning,
		};
	}

	private async extractDomainTags(
		analysis: DomainAnalysis,
		category: CategoryClassificationResultType,
	): Promise<TagExtractionResultType>
	{
		const extractedTags: string[] = [];
		const sources: string[] = [];

		// Domain token tags
		const tokenTags = this.extractTagsFromTokens(analysis.tokens);
		if (tokenTags.length > 0)
		{
			extractedTags.push(...tokenTags);
			sources.push('domain-tokens');
		}

		// Category-based tags
		const categoryTags = this.extractTagsFromCategory(category);
		if (categoryTags.length > 0)
		{
			extractedTags.push(...categoryTags);
			sources.push('category');
		}

		// TLD-based tags
		const tldTags = this.extractTagsFromTld(analysis.tld);
		if (tldTags.length > 0)
		{
			extractedTags.push(...tldTags);
			sources.push('tld');
		}

		// Brand-based tags
		if (this.detectBrand(analysis.tokens))
		{
			const brandTags = ['brand', 'commercial', 'enterprise'];
			extractedTags.push(...brandTags);
			sources.push('brand-detection');
		}

		// Taxonomy synonym expansion
		const synonymTags = this.expandWithSynonyms(extractedTags);
		if (synonymTags.length > extractedTags.length)
		{
			extractedTags.push(...synonymTags.slice(extractedTags.length));
			sources.push('taxonomy-synonyms');
		}

		// Always ensure we have some basic tags
		if (extractedTags.length === 0)
		{
			extractedTags.push('business', 'website', 'online');
			sources.push('fallback');
		}

		// Normalize and deduplicate
		const normalizedTags = this.normalizeTags(extractedTags);
		const confidence = this.calculateTagConfidence(normalizedTags, sources);

		return {
			tags: normalizedTags,
			confidence,
			sources,
		};
	}

	private async generateDescription(
		analysis: DomainAnalysis,
		category: CategoryClassificationResultType,
		tags: TagExtractionResultType,
		options: Required<PreGeneratedContentOptions>,
	): Promise<string>
	{
		const sections: string[] = [];

		// 1. Introduction section (80-100 words)
		const intro = this.generateIntroSection(analysis, category);
		sections.push(intro);

		// 2. Core offerings section (60-80 words)
		const offerings = this.generateOfferingsSection(analysis, category, tags);
		sections.push(offerings);

		// 3. Key features section (50-70 words)
		const features = this.generateFeaturesSection(analysis, category, tags);
		sections.push(features);

		// 4. Benefits and value proposition (60-80 words)
		const benefits = this.generateBenefitsSection(analysis, category, tags);
		sections.push(benefits);

		// 5. Trust and credibility section (40-60 words)
		const trust = this.generateTrustSection(analysis, category);
		sections.push(trust);

		// 6. Call-to-action section (30-50 words)
		const cta = this.generateCtaSection(analysis, category);
		sections.push(cta);

		let description = sections.join(' ');

		// Apply SEO optimization
		if (options.seoOptimization)
		{
			description = this.applySeoOptimization(description, analysis, category, tags);
		}

		// Ensure minimum word count with additional content
		if (this.countWords(description) < options.minWords)
		{
			description = this.expandDescription(description, analysis, category, tags, options);
		}

		// Ensure maximum word count
		if (this.countWords(description) > options.maxWords)
		{
			description = this.truncateDescription(description, options.maxWords);
		}

		return description;
	}

	private generateIntroSection(analysis: DomainAnalysis, category: CategoryClassificationResultType): string
	{
		const templates = this.templates[category.primary] || this.templates.business;
		const template = this.selectRandomTemplate(templates);
		const categoryName = this.getCategoryDisplayName(category.primary);

		return template
			.replace('{domain}', analysis.domain)
			.replace('{brandName}', analysis.brandName)
			.replace('{category}', categoryName)
			.replace('{primaryCategory}', categoryName);
	}

	private generateOfferingsSection(
		analysis: DomainAnalysis,
		category: CategoryClassificationResultType,
		tags: TagExtractionResultType,
	): string
	{
		const categoryName = this.getCategoryDisplayName(category.primary);
		const relevantTags = tags.tags.slice(0, 4);

		const offerings = [
			`comprehensive ${categoryName} solutions`,
			`innovative ${categoryName} services`,
			`professional ${categoryName} tools`,
			`advanced ${categoryName} features`,
		];

		// Add tag-based offerings
		if (relevantTags.length > 0)
		{
			const tagServices = relevantTags.map(tag => `${tag.replace('-', ' ')} services`);
			offerings.push(...tagServices.slice(0, 2));
		}

		// Add domain-specific offerings based on tokens
		const tokenOfferings = this.generateTokenBasedOfferings(analysis.tokens, categoryName);
		offerings.push(...tokenOfferings);

		return `The platform specializes in delivering ${offerings.slice(0, 4).join(', ')}, ensuring users have access to cutting-edge technology and reliable support. Through its comprehensive suite of offerings, ${analysis.domain} addresses the evolving needs of modern ${categoryName} requirements while maintaining industry-leading standards.`;
	}

	private generateFeaturesSection(
		analysis: DomainAnalysis,
		category: CategoryClassificationResultType,
		tags: TagExtractionResultType,
	): string
	{
		const categoryFeatures = this.getCategoryFeatures(category.primary);
		const tagFeatures = tags.tags.slice(0, 3).map(tag => `${tag.replace('-', ' ')} integration`);
		const tokenFeatures = this.generateTokenBasedFeatures(analysis.tokens);

		const allFeatures = [...categoryFeatures, ...tagFeatures, ...tokenFeatures].slice(0, 6);

		return `Key features include ${allFeatures.join(', ')}, providing users with a comprehensive toolkit for success. The platform's robust architecture ensures scalability, reliability, and seamless integration with existing workflows and systems, delivering exceptional performance across all service areas.`;
	}

	private generateBenefitsSection(
		analysis: DomainAnalysis,
		category: CategoryClassificationResultType,
		tags: TagExtractionResultType,
	): string
	{
		const categoryName = this.getCategoryDisplayName(category.primary);
		const benefits = this.getCategoryBenefits(category.primary);
		const tagBenefits = tags.tags.slice(0, 2).map(tag => `enhanced ${tag.replace('-', ' ')} capabilities`);

		const allBenefits = [...benefits, ...tagBenefits].slice(0, 4);

		return `Users benefit from ${allBenefits.join(', ')}, making it an ideal choice for individuals and organizations seeking reliable ${categoryName} solutions. The platform's commitment to excellence ensures consistent performance, security, and user satisfaction across all service offerings, while providing measurable value through innovative features.`;
	}

	private generateTrustSection(analysis: DomainAnalysis, category: CategoryClassificationResultType): string
	{
		const trustIndicators = this.getTrustIndicators(analysis.tld, category.primary);
		const categoryName = this.getCategoryDisplayName(category.primary);

		let trustContent = `${analysis.domain} maintains high standards of ${trustIndicators.join(', ')}, establishing itself as a trusted resource in the ${categoryName} industry.`;

		// Add international trust indicators
		if (analysis.isInternational)
		{
			trustContent += ' The platform\'s international presence demonstrates its commitment to serving diverse markets with culturally appropriate solutions.';
		}

		// Add brand trust indicators
		if (this.detectBrand(analysis.tokens))
		{
			trustContent += ' The brand\'s reputation for quality and innovation reinforces its position as a market leader.';
		}

		return trustContent;
	}

	private generateCtaSection(analysis: DomainAnalysis, category: CategoryClassificationResultType): string
	{
		const categoryName = this.getCategoryDisplayName(category.primary);
		const actions = this.getCategoryActions(category.primary);

		return `Whether you're looking to ${actions.join(', ')}, ${analysis.domain} provides the tools and expertise needed to achieve your ${categoryName} objectives. Explore the platform today to discover how these comprehensive solutions can transform your approach to ${categoryName} challenges and drive meaningful results.`;
	}

	private expandDescription(
		description: string,
		analysis: DomainAnalysis,
		category: CategoryClassificationResultType,
		tags: TagExtractionResultType,
		options: Required<PreGeneratedContentOptions>,
	): string
	{
		const currentWords = this.countWords(description);
		const wordsNeeded = options.minWords - currentWords;

		if (wordsNeeded <= 0) return description;

		const expansions: string[] = [];

		// Industry context expansion
		const industryContext = this.generateIndustryContext(category);
		expansions.push(industryContext);

		// Technical details expansion
		const technicalDetails = this.generateTechnicalDetails(category, tags);
		expansions.push(technicalDetails);

		// Use case examples
		const useCases = this.generateUseCases(analysis, category);
		expansions.push(useCases);

		// Competitive advantages
		const advantages = this.generateCompetitiveAdvantages(analysis, category);
		expansions.push(advantages);

		// Target audience details
		const audienceDetails = this.generateAudienceDetails(category, tags);
		expansions.push(audienceDetails);

		// Future roadmap hints
		const roadmapHints = this.generateRoadmapHints(category, tags);
		expansions.push(roadmapHints);

		let expanded = description;
		for (const expansion of expansions)
		{
			expanded += ` ${expansion}`;
			if (this.countWords(expanded) >= options.minWords) break;
		}

		// If still not enough words, add more detailed content
		if (this.countWords(expanded) < options.minWords)
		{
			const additionalContent = this.generateAdditionalContent(analysis, category, wordsNeeded);
			expanded += ` ${additionalContent}`;
		}

		return expanded;
	}

	private applySeoOptimization(
		description: string,
		analysis: DomainAnalysis,
		category: CategoryClassificationResultType,
		tags: TagExtractionResultType,
	): string
	{
		let optimized = description;

		// Ensure domain appears early and frequently
		if (!optimized.substring(0, 100).toLowerCase().includes(analysis.domain.toLowerCase()))
		{
			optimized = `${analysis.domain} ${optimized}`;
		}

		// Integrate primary keywords naturally
		const primaryKeywords = this.getSeoKeywords(category.primary);
		const targetKeywords = [...primaryKeywords.slice(0, 3), ...tags.tags.slice(0, 2)];

		for (const keyword of targetKeywords)
		{
			if (!optimized.toLowerCase().includes(keyword.toLowerCase()))
			{
				optimized = this.integrateKeywordNaturally(optimized, keyword, category);
			}
		}

		// Ensure brand name appears multiple times
		const brandOccurrences = (optimized.toLowerCase().match(new RegExp(analysis.brandName.toLowerCase(), 'g')) || []).length;
		if (brandOccurrences < 2)
		{
			optimized = this.integrateBrandName(optimized, analysis.brandName);
		}

		// Improve readability and flow
		optimized = this.improveReadability(optimized);

		return optimized;
	}

	private generateSeoSummary(
		analysis: DomainAnalysis,
		category: CategoryClassificationResultType,
		tags: TagExtractionResultType,
	): string
	{
		const categoryName = this.getCategoryDisplayName(category.primary);
		const topTags = tags.tags.slice(0, 3).join(', ');

		// Keep SEO summary under 160 characters and include domain
		const summary = `${analysis.domain} offers ${categoryName} solutions including ${topTags}. Professional tools and services.`;

		// Ensure it's under 160 characters
		if (summary.length > 160)
		{
			const shortSummary = `${analysis.domain} provides ${categoryName} solutions with ${topTags.split(', ')[0]} and professional support.`;
			return shortSummary.length > 160 ? `${shortSummary.substring(0, 157) }...` : shortSummary;
		}

		return summary;
	}

	private ensureTagRequirements(
		tags: string[],
		analysis: DomainAnalysis,
		category: CategoryClassificationResultType,
	): string[]
	{
		return this.ensureTagRequirementsWithOptions(tags, analysis, category, this.options);
	}

	private ensureTagRequirementsWithOptions(
		tags: string[],
		analysis: DomainAnalysis,
		category: CategoryClassificationResultType,
		options: Required<PreGeneratedContentOptions>,
	): string[]
	{
		let finalTags = [...tags];

		// Remove duplicates first
		finalTags = [...new Set(finalTags)];

		// Ensure minimum tag count
		if (finalTags.length < options.minTags)
		{
			const needed = options.minTags - finalTags.length;
			const additionalTags = this.generateFallbackTags(analysis, category, needed);

			// Add tags that aren't already in the list
			for (const tag of additionalTags)
			{
				if (!finalTags.includes(tag))
				{
					finalTags.push(tag);
					if (finalTags.length >= options.minTags) break;
				}
			}
		}

		// Ensure maximum tag count
		if (finalTags.length > options.maxTags)
		{
			finalTags = this.rankTagsByRelevance(finalTags, analysis, category).slice(0, options.maxTags);
		}

		return finalTags;
	}

	private calculateConfidence(
		category: CategoryClassificationResultType,
		tags: TagExtractionResultType,
		wordCount: number,
		analysis: DomainAnalysis,
	): number
	{
		let confidence = 0.4; // Base confidence for preGenerated content

		// Category confidence boost
		confidence += category.confidence * 0.2;

		// Tag quality boost
		confidence += tags.confidence * 0.15;
		if (tags.tags.length >= this.options.minTags) confidence += 0.1;

		// Word count boost
		if (wordCount >= this.options.minWords) confidence += 0.15;

		// Brand detection boost
		if (this.detectBrand(analysis.tokens)) confidence += 0.1;

		// Multiple sources boost
		if (tags.sources.length >= 3) confidence += 0.05;

		return Math.min(confidence, 0.85); // Cap preGenerated confidence at 85%
	}

	private generateFallbackContent(domain: string, options?: Partial<PreGeneratedContentOptions>): PreGeneratedContent
	{
		const mergedOptions = { ...this.options, ...options };
		const analysis = this.analyzeDomain(domain);
		const categoryName = 'business';

		const summary = `${domain} is a ${categoryName} website that provides various services and solutions to meet user needs. The platform offers comprehensive tools and resources designed with a focus on quality and reliability. Users can access professional services through the website's organized structure and user-friendly interface. ${domain} maintains industry standards for service delivery and user experience, ensuring consistent performance across all offerings. The platform continues to evolve and adapt to meet changing requirements and market demands. Whether for personal or professional use, ${domain} provides accessible solutions that combine expertise with innovative approaches. The website serves as a valuable resource for individuals and organizations seeking reliable ${categoryName} solutions. Through its commitment to excellence, ${domain} delivers measurable value and supports users in achieving their objectives with comprehensive support and guidance.`;

		// Ensure minimum tags for fallback
		const fallbackTags = ['business', 'website', 'online', 'service', 'platform'];
		while (fallbackTags.length < mergedOptions.minTags)
		{
			const additionalTags = ['professional', 'reliable', 'comprehensive', 'innovative', 'quality'];
			for (const tag of additionalTags)
			{
				if (!fallbackTags.includes(tag))
				{
					fallbackTags.push(tag);
					if (fallbackTags.length >= mergedOptions.minTags) break;
				}
			}
			break; // Prevent infinite loop
		}

		return {
			summary,
			category: { primary: 'business' },
			tags: fallbackTags.slice(0, mergedOptions.maxTags),
			seoSummary: `${domain} provides business solutions and services with professional support.`,
			wordCount: this.countWords(summary),
			confidence: 0.2,
			method: 'preGenerated',
			sources: ['fallback'],
			metadata: {
				generatedAt: new Date().toISOString(),
				version: '1.0.0',
				inputs: ['fallback'],
			},
		};
	}

	// Helper methods
	private tokenizeDomain(domain: string): string[]
	{
		const withoutTld = domain.split('.')[0];
		return withoutTld
			.split(/[-_.]/)
			.filter(token => token.length > 1)
			.map(token => token.toLowerCase());
	}

	private extractTld(domain: string): string
	{
		const parts = domain.split('.');
		return parts[parts.length - 1].toLowerCase();
	}

	private extractBrandName(domain: string, tokens: string[]): string
	{
		// Try to identify the main brand name from tokens
		if (tokens.length === 0) return domain.split('.')[0];

		const longestToken = tokens.reduce((a, b) => (a.length > b.length ? a : b), '');
		return longestToken.charAt(0).toUpperCase() + longestToken.slice(1);
	}

	private detectInternationalDomain(domain: string, tld: string): boolean
	{
		const internationalTlds = new Set([
			'de', 'fr', 'es', 'it', 'nl', 'be', 'ch', 'at', 'dk', 'se', 'no', 'fi',
			'pl', 'cz', 'hu', 'ro', 'bg', 'hr', 'si', 'sk', 'lt', 'lv', 'ee',
			'jp', 'cn', 'kr', 'in', 'au', 'nz', 'ca', 'mx', 'br', 'ar', 'cl',
			'co', 've', 'pe', 'ec', 'uy', 'py', 'bo', 'gf', 'sr', 'gy',
		]);

		return internationalTlds.has(tld) || /[^\x00-\x7F]/.test(domain);
	}

	private detectLanguage(domain: string, tld: string): string
	{
		const tldLanguageMap: Record<string, string> = {
			de: 'de',
			fr: 'fr',
			es: 'es',
			it: 'it',
			nl: 'nl',
			jp: 'ja',
			cn: 'zh',
			kr: 'ko',
			ru: 'ru',
			pt: 'pt',
		};

		return tldLanguageMap[tld] || 'en';
	}

	private detectCountry(tld: string): string
	{
		const tldCountryMap: Record<string, string> = {
			de: 'DE',
			fr: 'FR',
			es: 'ES',
			it: 'IT',
			nl: 'NL',
			jp: 'JP',
			cn: 'CN',
			kr: 'KR',
			au: 'AU',
			ca: 'CA',
			mx: 'MX',
			br: 'BR',
			in: 'IN',
			uk: 'GB',
		};

		return tldCountryMap[tld] || 'US';
	}

	private classifyFromTokens(tokens: string[]): CategoryClassificationResultType
	{
		const categoryScores: Record<string, number> = {};
		const reasoning: string[] = [];

		// Enhanced token patterns with more specific matching
		const tokenPatterns: Record<string, { patterns: string[]; weight: number }> = {
			technology: { patterns: ['github', 'api', 'dev', 'tech', 'software', 'platform', 'code', 'data', 'cloud', 'ai', 'ml', 'app'], weight: 0.6 },
			'e-commerce': { patterns: ['shop', 'store', 'buy', 'sell', 'market', 'cart', 'pay', 'commerce', 'retail'], weight: 0.7 },
			education: { patterns: ['learn', 'course', 'school', 'university', 'edu', 'study', 'teach', 'academy'], weight: 0.8 },
			health: { patterns: ['health', 'medical', 'doctor', 'care', 'wellness', 'clinic', 'hospital'], weight: 0.8 },
			finance: { patterns: ['bank', 'money', 'finance', 'invest', 'pay', 'credit', 'loan', 'fintech'], weight: 0.8 },
			gaming: { patterns: ['game', 'gaming', 'play', 'esports', 'arcade'], weight: 0.9 },
			'news-media': { patterns: ['news', 'media', 'blog', 'journal', 'press'], weight: 0.7 },
		};

		for (const token of tokens)
		{
			for (const [category, { patterns, weight }] of Object.entries(tokenPatterns))
			{
				if (patterns.some(pattern => token.includes(pattern) || pattern.includes(token)))
				{
					categoryScores[category] = (categoryScores[category] || 0) + weight;
					reasoning.push(`Token "${token}" suggests ${category} category`);
				}
			}
		}

		const sortedCategories = Object.entries(categoryScores)
			.sort(([, a], [, b]) => b - a);

		if (sortedCategories.length === 0)
		{
			return {
				primary: 'business',
				confidence: 0.1,
				reasoning: ['No token matches found, using default'],
			};
		}

		const [primaryKey, primaryScore] = sortedCategories[0];
		const secondaryKey = sortedCategories.length > 1 ? sortedCategories[1][0] : undefined;

		return {
			primary: primaryKey,
			secondary: secondaryKey,
			confidence: Math.min(primaryScore, 0.9),
			reasoning,
		};
	}

	private extractTagsFromTokens(tokens: string[]): string[]
	{
		const extractedTags: string[] = [];

		// Direct token to tag mapping
		const tokenTagMap: Record<string, string[]> = {
			api: ['api', 'integration'],
			app: ['mobile', 'software'],
			cloud: ['cloud', 'saas'],
			data: ['analytics', 'data-science'],
			ai: ['ai-foundation-models', 'machine-learning'],
			ml: ['machine-learning', 'ai-foundation-models'],
			dev: ['devtools', 'software'],
			shop: ['e-commerce', 'retail'],
			pay: ['payments', 'fintech'],
			game: ['gaming', 'entertainment'],
			learn: ['education', 'online-learning'],
			health: ['health', 'wellness'],
			news: ['news-media', 'journalism'],
			platform: ['platform', 'saas'],
			gateway: ['gateway', 'api'],
		};

		for (const token of tokens)
		{
			// Direct mapping
			if (tokenTagMap[token])
			{
				extractedTags.push(...tokenTagMap[token]);
			}

			// Partial matching for compound tokens
			for (const [key, tags] of Object.entries(tokenTagMap))
			{
				if (token.includes(key) && token !== key)
				{
					extractedTags.push(...tags);
				}
			}

			// Add the token itself as a tag if it's valid
			if (this.isValidTag(token))
			{
				extractedTags.push(token);
			}
		}

		return [...new Set(extractedTags)]; // Remove duplicates
	}

	private extractTagsFromCategory(category: CategoryClassificationResultType): string[]
	{
		const tags: string[] = [];

		// Add primary category as tag
		tags.push(category.primary);

		// Add secondary category as tag
		if (category.secondary)
		{
			tags.push(category.secondary);
		}

		// Add related tags based on category
		const categoryTagMap: Record<string, string[]> = {
			technology: ['software', 'innovation', 'digital'],
			'e-commerce': ['online-shopping', 'retail', 'marketplace'],
			education: ['learning', 'knowledge', 'academic'],
			health: ['wellness', 'medical', 'healthcare'],
			finance: ['financial-services', 'money-management'],
			gaming: ['entertainment', 'interactive'],
			'news-media': ['information', 'journalism'],
		};

		const relatedTags = categoryTagMap[category.primary];
		if (relatedTags)
		{
			tags.push(...relatedTags);
		}

		return tags.slice(0, 5); // Limit category-based tags
	}

	private extractTagsFromTld(tld: string): string[]
	{
		const tldTagMap: Record<string, string[]> = {
			com: ['commercial', 'business'],
			org: ['organization', 'nonprofit'],
			edu: ['education', 'academic'],
			gov: ['government', 'public-services'],
			io: ['technology', 'startup'],
			ai: ['ai-foundation-models', 'technology'],
			tech: ['technology', 'innovation'],
			app: ['mobile', 'software'],
			dev: ['devtools', 'software'],
			store: ['e-commerce', 'retail'],
			shop: ['e-commerce', 'shopping'],
			news: ['news-media', 'journalism'],
			blog: ['content', 'media'],
		};

		return tldTagMap[tld] || [];
	}

	private expandWithSynonyms(tags: string[]): string[]
	{
		const expandedTags = [...tags];

		for (const tag of tags)
		{
			const synonyms = this.taxonomySynonyms[tag];
			if (synonyms)
			{
				expandedTags.push(...synonyms.slice(0, 2)); // Add up to 2 synonyms per tag
			}
		}

		return [...new Set(expandedTags)]; // Remove duplicates
	}

	private normalizeTags(tags: string[]): string[]
	{
		return tags
			.map(tag => tag.toLowerCase().trim())
			.filter(tag => tag.length > 0 && tag.length <= 50)
			.map(tag => tag.replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '').replace(/-+/g, '-').replace(/^-|-$/g, ''))
			.filter(tag => tag.length > 1)
			.filter((tag, index, arr) => arr.indexOf(tag) === index); // Remove duplicates
	}

	private generateTokenBasedOfferings(tokens: string[], categoryName: string): string[]
	{
		const offerings: string[] = [];

		for (const token of tokens.slice(0, 3))
		{
			offerings.push(`${token} ${categoryName} solutions`);
		}

		return offerings;
	}

	private generateTokenBasedFeatures(tokens: string[]): string[]
	{
		const features: string[] = [];

		for (const token of tokens.slice(0, 2))
		{
			features.push(`${token} integration`, `${token} management`);
		}

		return features;
	}

	private generateIndustryContext(category: CategoryClassificationResultType): string
	{
		const categoryName = this.getCategoryDisplayName(category.primary);
		const contextMap: Record<string, string> = {
			technology: 'The technology industry continues to evolve rapidly with emerging trends in artificial intelligence, cloud computing, and digital transformation driving innovation across all sectors.',
			'e-commerce': 'The e-commerce landscape is experiencing unprecedented growth with mobile commerce, personalization, and omnichannel experiences becoming essential for success.',
			education: 'Educational technology is transforming learning experiences through personalized instruction, virtual classrooms, and adaptive learning platforms.',
			health: 'Healthcare technology is revolutionizing patient care through telemedicine, wearable devices, and AI-powered diagnostic tools.',
			finance: 'Financial services are being transformed by fintech innovations, digital banking, and blockchain technologies.',
		};

		return contextMap[category.primary] || `The ${categoryName} industry continues to evolve with technological advancements and changing user expectations driving innovation and growth.`;
	}

	private generateTechnicalDetails(category: CategoryClassificationResultType, tags: TagExtractionResultType): string
	{
		const techTags = tags.tags.filter(tag => ['api', 'cloud', 'saas', 'ai', 'ml', 'devtools', 'analytics'].some(tech => tag.includes(tech)));

		if (techTags.length > 0)
		{
			return `The platform leverages advanced ${techTags.slice(0, 2).join(' and ')} technologies to deliver superior performance, scalability, and reliability for enterprise-grade applications.`;
		}

		return 'Built on modern architecture principles, the platform incorporates industry-standard technologies and best practices to ensure optimal performance, security, and user experience.';
	}

	private generateUseCases(analysis: DomainAnalysis, category: CategoryClassificationResultType): string
	{
		const categoryName = this.getCategoryDisplayName(category.primary);
		const useCaseMap: Record<string, string[]> = {
			technology: ['software development', 'system integration', 'digital transformation'],
			'e-commerce': ['online retail', 'marketplace operations', 'digital commerce'],
			education: ['online learning', 'skill development', 'knowledge sharing'],
			health: ['patient care', 'wellness management', 'health monitoring'],
			finance: ['financial planning', 'investment management', 'payment processing'],
		};

		const useCases = useCaseMap[category.primary] || ['business operations', 'service delivery', 'customer engagement'];
		return `Common use cases include ${useCases.join(', ')}, making it suitable for diverse ${categoryName} applications and requirements.`;
	}

	private generateCompetitiveAdvantages(analysis: DomainAnalysis, category: CategoryClassificationResultType): string
	{
		const advantages = [
			'innovative approach',
			'comprehensive feature set',
			'reliable performance',
			'user-friendly design',
			'scalable architecture',
		];

		if (this.detectBrand(analysis.tokens))
		{
			advantages.unshift('established brand reputation');
		}

		return `Key competitive advantages include ${advantages.slice(0, 3).join(', ')}, positioning ${analysis.domain} as a leader in the ${this.getCategoryDisplayName(category.primary)} space.`;
	}

	private generateAudienceDetails(category: CategoryClassificationResultType, tags: TagExtractionResultType): string
	{
		const audienceMap: Record<string, string[]> = {
			technology: ['developers', 'IT professionals', 'technology teams'],
			'e-commerce': ['online retailers', 'merchants', 'e-commerce businesses'],
			education: ['students', 'educators', 'learning institutions'],
			health: ['healthcare providers', 'patients', 'wellness professionals'],
			finance: ['financial advisors', 'investors', 'financial institutions'],
		};

		const audiences = audienceMap[category.primary] || ['professionals', 'businesses', 'organizations'];
		return `The platform serves ${audiences.join(', ')} with tailored solutions designed to meet specific industry requirements and user needs.`;
	}

	private generateRoadmapHints(category: CategoryClassificationResultType, tags: TagExtractionResultType): string
	{
		const categoryName = this.getCategoryDisplayName(category.primary);
		return `Future developments focus on expanding ${categoryName} capabilities, enhancing user experience, and integrating emerging technologies to maintain competitive advantage and market leadership.`;
	}

	private generateAdditionalContent(
		analysis: DomainAnalysis,
		category: CategoryClassificationResultType,
		wordsNeeded: number,
	): string
	{
		const categoryName = this.getCategoryDisplayName(category.primary);
		const additionalSections = [
			`The platform's commitment to ${categoryName} excellence is reflected in its comprehensive approach to service delivery and customer satisfaction.`,
			`${analysis.domain} continues to invest in research and development to ensure its solutions remain at the forefront of ${categoryName} innovation.`,
			'Users can expect ongoing improvements and feature enhancements as the platform evolves to meet changing market demands and user expectations.',
			`The combination of technical expertise and industry knowledge makes ${analysis.domain} a valuable partner for ${categoryName} success.`,
		];

		let additional = '';
		let wordsAdded = 0;

		for (const section of additionalSections)
		{
			if (wordsAdded >= wordsNeeded) break;
			additional += ` ${section}`;
			wordsAdded += this.countWords(section);
		}

		return additional.trim();
	}

	private integrateKeywordNaturally(
		text: string,
		keyword: string,
		category: CategoryClassificationResultType,
	): string
	{
		const sentences = text.split('. ');
		const targetSentence = Math.floor(sentences.length / 3);

		if (targetSentence < sentences.length)
		{
			const sentence = sentences[targetSentence];

			// Try different integration approaches
			if (sentence.includes('platform'))
			{
				sentences[targetSentence] = sentence.replace('platform', `${keyword} platform`);
			}
			else if (sentence.includes('solutions'))
			{
				sentences[targetSentence] = sentence.replace('solutions', `${keyword} solutions`);
			}
			else if (sentence.includes('services'))
			{
				sentences[targetSentence] = sentence.replace('services', `${keyword} services`);
			}
			else
			{
				// Add keyword at the beginning of the sentence
				sentences[targetSentence] = `${keyword.charAt(0).toUpperCase() + keyword.slice(1)} ${sentence.toLowerCase()}`;
			}
		}

		return sentences.join('. ');
	}

	private integrateBrandName(text: string, brandName: string): string
	{
		const sentences = text.split('. ');
		const middleSentence = Math.floor(sentences.length / 2);

		if (middleSentence < sentences.length)
		{
			const sentence = sentences[middleSentence];
			if (!sentence.toLowerCase().includes(brandName.toLowerCase()))
			{
				sentences[middleSentence] = sentence.replace('The platform', `${brandName}`);
			}
		}

		return sentences.join('. ');
	}

	private improveReadability(text: string): string
	{
		return text
			.replace(/\s+/g, ' ')
			.replace(/\.\s*\./g, '.')
			.replace(/,\s*,/g, ',')
			.replace(/\s+([.!?])/g, '$1')
			.trim();
	}

	private truncateDescription(description: string, maxWords: number): string
	{
		const words = description.split(/\s+/);
		if (words.length <= maxWords) return description;

		const truncated = words.slice(0, maxWords).join(' ');
		const lastSentenceEnd = Math.max(
			truncated.lastIndexOf('.'),
			truncated.lastIndexOf('!'),
			truncated.lastIndexOf('?'),
		);

		if (lastSentenceEnd > truncated.length * 0.8)
		{
			return truncated.substring(0, lastSentenceEnd + 1);
		}

		return `${truncated.trim()}.`;
	}

	private calculateTagConfidence(tags: string[], sources: string[]): number
	{
		let confidence = 0.3;

		// Boost based on number of sources
		confidence += sources.length * 0.1;

		// Boost based on tag quality
		if (tags.length >= this.options.minTags) confidence += 0.2;
		if (tags.some(tag => tag.includes('-'))) confidence += 0.1; // Specific tags

		return Math.min(confidence, 1.0);
	}

	private generateFallbackTags(
		analysis: DomainAnalysis,
		category: CategoryClassificationResultType,
		needed: number,
	): string[]
	{
		const fallbackTags: string[] = [];

		// Add category-based fallbacks
		fallbackTags.push(category.primary);
		if (category.secondary) fallbackTags.push(category.secondary);

		// Add common tags
		const commonTags = ['business', 'website', 'online', 'service', 'platform', 'professional', 'reliable', 'comprehensive', 'innovative', 'quality', 'solutions', 'tools', 'resources', 'support'];
		fallbackTags.push(...commonTags);

		// Add TLD-based tags
		if (this.isValidTag(analysis.tld))
		{
			fallbackTags.push(analysis.tld);
		}

		// Add token-based tags
		for (const token of analysis.tokens)
		{
			if (this.isValidTag(token))
			{
				fallbackTags.push(token);
			}
		}

		// Remove duplicates and filter valid tags
		const uniqueTags = [...new Set(fallbackTags)]
			.filter(tag => this.isValidTag(tag))
			.slice(0, needed + 5); // Get more than needed to ensure we have enough

		return uniqueTags.slice(0, needed);
	}

	private rankTagsByRelevance(
		tags: string[],
		analysis: DomainAnalysis,
		category: CategoryClassificationResultType,
	): string[]
	{
		const tagScores: Record<string, number> = {};

		for (const tag of tags)
		{
			let score = 1;

			// Boost category-related tags
			if (tag === category.primary) score += 1.0;
			if (tag === category.secondary) score += 0.8;

			// Boost technology and business tags
			if (['technology', 'business', 'software', 'saas', 'api'].includes(tag))
			{
				score += 0.6;
			}

			// Boost specific over generic tags
			if (tag.includes('-') || tag.length > 8)
			{
				score += 0.3;
			}

			// Boost domain token matches
			if (analysis.tokens.some(token => tag.includes(token)))
			{
				score += 0.4;
			}

			tagScores[tag] = score;
		}

		return Object.entries(tagScores)
			.sort(([, a], [, b]) => b - a)
			.map(([tag]) => tag);
	}

	private countWords(text: string): number
	{
		return text.trim().split(/\s+/).filter(word => word.length > 0).length;
	}

	private selectRandomTemplate(templates: string[]): string
	{
		return templates[Math.floor(Math.random() * templates.length)];
	}

	private getCategoryDisplayName(category: string): string
	{
		return category.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase());
	}

	private getCategoryFeatures(category: string): string[]
	{
		const featureMap: Record<string, string[]> = {
			technology: ['advanced analytics', 'cloud integration', 'API access', 'real-time monitoring', 'automated workflows'],
			business: ['project management', 'team collaboration', 'reporting tools', 'customer support', 'workflow automation'],
			'e-commerce': ['shopping cart', 'payment processing', 'inventory management', 'order tracking', 'customer analytics'],
			education: ['course catalog', 'progress tracking', 'interactive content', 'certification', 'learning analytics'],
			health: ['appointment scheduling', 'patient records', 'treatment planning', 'health monitoring', 'telemedicine'],
			finance: ['account management', 'transaction history', 'financial planning', 'investment tools', 'risk assessment'],
		};

		return featureMap[category] || featureMap.business;
	}

	private getCategoryBenefits(category: string): string[]
	{
		const benefitMap: Record<string, string[]> = {
			technology: ['enhanced productivity', 'streamlined workflows', 'innovative solutions', 'scalable architecture'],
			business: ['improved efficiency', 'cost-effective solutions', 'professional service', 'reliable support'],
			'e-commerce': ['secure transactions', 'user-friendly interface', 'comprehensive catalog', 'fast delivery'],
			education: ['comprehensive learning', 'expert instruction', 'flexible scheduling', 'practical application'],
			health: ['professional care', 'evidence-based treatment', 'personalized service', 'comprehensive support'],
			finance: ['secure transactions', 'competitive rates', 'expert guidance', 'transparent processes'],
		};

		return benefitMap[category] || benefitMap.business;
	}

	private getTrustIndicators(tld: string, category: string): string[]
	{
		const indicators = ['security', 'reliability', 'professional standards', 'data protection'];

		if (tld === 'org') indicators.push('transparency', 'accountability');
		if (tld === 'edu') indicators.push('academic excellence', 'educational integrity');
		if (tld === 'gov') indicators.push('official authority', 'public service');

		if (category === 'finance') indicators.push('regulatory compliance', 'financial security');
		if (category === 'health') indicators.push('privacy protection', 'medical standards');

		return indicators.slice(0, 4);
	}

	private getCategoryActions(category: string): string[]
	{
		const actionMap: Record<string, string[]> = {
			technology: ['streamline operations', 'enhance productivity', 'implement solutions', 'drive innovation'],
			business: ['improve efficiency', 'grow your business', 'optimize processes', 'achieve goals'],
			'e-commerce': ['increase sales', 'expand market reach', 'enhance customer experience', 'boost revenue'],
			education: ['advance learning', 'develop skills', 'achieve certification', 'expand knowledge'],
			health: ['improve wellness', 'access care', 'maintain health', 'enhance quality of life'],
			finance: ['manage finances', 'plan investments', 'secure future', 'build wealth'],
		};

		return actionMap[category] || actionMap.business;
	}

	private getSeoKeywords(category: string): string[]
	{
		return this.seoKeywords[category] || this.seoKeywords.business || [];
	}

	private detectBrand(tokens: string[]): boolean
	{
		return tokens.some(token => this.brandIndicators.has(token));
	}

	private isValidTag(tag: string): boolean
	{
		return tag.length > 1 && tag.length <= 50 && /^[a-z0-9-]+$/.test(tag);
	}

	// Initialization methods
	private initializeTemplates(): Record<string, string[]>
	{
		return {
			technology: [
				'{domain} is a leading technology platform that provides {brandName} with comprehensive {category} solutions designed to enhance productivity and drive innovation.',
				'{brandName}, accessible through {domain}, represents an advanced {category} platform offering cutting-edge tools and services for modern technology needs.',
				'The {domain} platform delivers innovative {category} solutions, empowering {brandName} users with professional-grade tools and reliable performance.',
			],
			business: [
				'{domain} serves as a comprehensive business platform where {brandName} delivers professional {category} solutions tailored to meet diverse organizational needs.',
				'{brandName} operates through {domain} to provide reliable {category} services designed for business success and operational excellence.',
				'The {domain} platform offers {brandName} users access to comprehensive {category} solutions with a focus on business growth and efficiency.',
			],
			'e-commerce': [
				'{domain} is an e-commerce platform where {brandName} provides comprehensive online shopping solutions and retail services.',
				'{brandName} operates through {domain} to deliver innovative e-commerce experiences with secure transactions and user-friendly interfaces.',
				'The {domain} marketplace offers {brandName} customers access to diverse products and services with reliable e-commerce functionality.',
			],
			education: [
				'{domain} is an educational platform where {brandName} provides comprehensive learning solutions and academic resources.',
				'{brandName} delivers educational excellence through {domain} with innovative learning tools and expert instruction.',
				'The {domain} platform offers {brandName} learners access to quality education with flexible scheduling and comprehensive support.',
			],
			health: [
				'{domain} is a healthcare platform where {brandName} provides comprehensive medical solutions and wellness services.',
				'{brandName} delivers healthcare excellence through {domain} with professional care and evidence-based treatment.',
				'The {domain} platform offers {brandName} patients access to quality healthcare with personalized service and comprehensive support.',
			],
		};
	}

	private initializeSeoKeywords(): Record<string, string[]>
	{
		return {
			technology: ['software', 'platform', 'solutions', 'innovation', 'digital', 'cloud', 'api', 'development'],
			business: ['professional', 'services', 'solutions', 'management', 'efficiency', 'growth', 'success'],
			'e-commerce': ['online', 'shopping', 'retail', 'marketplace', 'products', 'secure', 'delivery'],
			education: ['learning', 'courses', 'education', 'training', 'knowledge', 'skills', 'academic'],
			health: ['healthcare', 'medical', 'wellness', 'treatment', 'care', 'health', 'professional'],
			finance: ['financial', 'investment', 'banking', 'money', 'planning', 'secure', 'management'],
		};
	}

	private initializeBrandIndicators(): Set<string>
	{
		return new Set([
			'microsoft', 'google', 'apple', 'amazon', 'facebook', 'github', 'shopify',
			'app', 'tech', 'labs', 'systems', 'solutions', 'software', 'digital',
			'cloud', 'data', 'analytics', 'platform', 'service', 'api', 'sdk',
			'inc', 'corp', 'ltd', 'llc', 'company', 'group', 'ventures',
		]);
	}

	private initializeTldCategoryPriors(): Record<string, string[]>
	{
		return {
			com: ['technology', 'business'],
			org: ['nonprofit', 'organization'],
			edu: ['education', 'research'],
			gov: ['government', 'public-services'],
			io: ['technology', 'developer-tools'],
			ai: ['ai-industry', 'technology'],
			tech: ['technology', 'software'],
			app: ['mobile', 'software'],
			dev: ['developer-tools', 'technology'],
			cloud: ['cloud', 'technology'],
			data: ['analytics-bi', 'data-science'],
			news: ['news-media', 'journalism'],
			blog: ['content', 'media'],
			shop: ['e-commerce', 'retail'],
			store: ['e-commerce', 'retail'],
			health: ['health', 'medical'],
			finance: ['finance', 'fintech'],
		};
	}

	private initializeTaxonomySynonyms(): Record<string, string[]>
	{
		return {
			technology: ['tech', 'digital', 'innovation'],
			software: ['application', 'program', 'system'],
			api: ['integration', 'interface', 'service'],
			cloud: ['saas', 'hosting', 'infrastructure'],
			analytics: ['data-science', 'insights', 'reporting'],
			'e-commerce': ['online-shopping', 'retail', 'marketplace'],
			education: ['learning', 'training', 'academic'],
			health: ['healthcare', 'medical', 'wellness'],
			finance: ['financial-services', 'banking', 'fintech'],
		};
	}

	private initializeContentExpansions(): Record<string, string[]>
	{
		return {
			technology: [
				'cutting-edge innovation',
				'scalable architecture',
				'enterprise-grade security',
				'real-time performance',
				'seamless integration',
			],
			business: [
				'operational excellence',
				'strategic growth',
				'competitive advantage',
				'market leadership',
				'customer satisfaction',
			],
			'e-commerce': [
				'user experience optimization',
				'conversion rate improvement',
				'mobile commerce',
				'omnichannel presence',
				'customer retention',
			],
		};
	}
}

export type { PreGeneratedContentOptions, PreGeneratedContent, DomainAnalysis };

export default PreGeneratedContentGenerator;
