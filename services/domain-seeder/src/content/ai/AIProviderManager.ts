import { logger as sharedLogger, type LoggerInstanceType , RedisClientWrapper } from '@shared';
import type {
	AIProviderType,
	AIProviderConfigType,
	AIContentRequestType,
	AIContentResponseType,
	// ProviderSelectionStrategyType,
	FallbackChainType,
} from './types';
import {
	OpenA<PERSON>rov<PERSON>, ClaudeProvider, GeminiProvider, OpenRouterProvider,
} from './providers';
import ProxyManager from './ProxyManager';
import AIRateLimiter, { type RateLimitConfig } from './AIRateLimiter';
import AIRequestQueue, { type BatchConfig } from './AIRequestQueue';

type AIProviderManagerConfigType =
{
	providers: Record<string, AIProviderConfigType>;
	fallbackChain: FallbackChainType;
	batchConfig: BatchConfig;
	costTracking: {
		enabled: boolean;
		dailyBudget?: number;
		monthlyBudget?: number;
	};
	keyRotation: {
		enabled: boolean;
		rotationInterval: number; // minutes
		balanceUsage: boolean;
	};
};

type ProviderStatsType =
{
	name: string;
	enabled: boolean;
	healthy: boolean;
	requestsToday: number;
	tokensUsedToday: number;
	costToday: number;
	successRate: number;
	averageLatency: number;
	lastUsed?: Date;
	activeKeys: number;
	totalKeys: number;
};

type CostTrackingType =
{
	totalCostToday: number;
	totalCostThisMonth: number;
	costByProvider: Record<string, number>;
	budgetExceeded: boolean;
	remainingBudget: number;
};

/**
 * Manages multiple AI providers with intelligent fallback, load balancing, and cost tracking
 */
class AIProviderManager
{
	private logger: LoggerInstanceType;

	private redis: RedisClientWrapper;

	private config: AIProviderManagerConfigType;

	private providers: Map<string, AIProviderType> = new Map();

	private proxyManager?: ProxyManager;

	private rateLimiter: AIRateLimiter;

	private requestQueue: AIRequestQueue;

	private costTracking: CostTrackingType = {
		totalCostToday: 0,
		totalCostThisMonth: 0,
		costByProvider: {},
		budgetExceeded: false,
		remainingBudget: 0,
	};

	private keyRotationTimer?: NodeJS.Timeout;

	constructor(
		config: AIProviderManagerConfigType,
		redis: RedisClientWrapper,
		logger?: LoggerInstanceType,
	)
	{
		this.config = config;
		this.redis = redis;
		this.logger = logger || sharedLogger.getLogger('AIProviderManager');
		this.rateLimiter = new AIRateLimiter(redis, this.logger);
		this.requestQueue = new AIRequestQueue(config.batchConfig, this.logger);

		this.initializeProviders();
		this.setupRateLimiting();
		this.startKeyRotation();
		this.loadCostTracking();
	}

	/**
	 * Generate enhanced content using the best available provider
	 */
	async generateContent(request: AIContentRequestType): Promise<AIContentResponseType>
	{
		// Check budget constraints
		if (this.config.costTracking.enabled && this.costTracking.budgetExceeded)
		{
			return {
				success: false,
				metadata: {
					provider: 'budget-exceeded',
					processingTime: 0,
					requestId: `budget-${Date.now()}`,
				},
				error: {
					code: 'BUDGET_EXCEEDED',
					message: 'Daily or monthly budget exceeded',
					retryable: false,
				},
			};
		}

		// Use request queue for batching if enabled
		if (this.config.batchConfig.enableBatching)
		{
			return this.requestQueue.enqueue(request);
		}

		return this.processRequest(request);
	}

	/**
	 * Process a single request through the fallback chain
	 */
	private async processRequest(request: AIContentRequestType): Promise<AIContentResponseType>
	{
		const providers = this.selectProviders();
		let lastError: string = 'No providers available';

		for (const providerName of providers)
		{
			const provider = this.providers.get(providerName);
			if (!provider || !provider.config.enabled)
			{
				continue;
			}

			try
			{
				// Check rate limits
				const rateLimitResult = await this.rateLimiter.checkRateLimit(
					providerName,
					provider.config.apiKeys[0], // Use first key for rate limit check
					this.estimateTokens(request),
				);

				if (!rateLimitResult.allowed)
				{
					this.logger.debug({ providerName, retryAfter: rateLimitResult.retryAfter, msg: `Provider rate limited: ${providerName}` });
					continue;
				}

				// Check provider health
				const health = await provider.healthCheck();
				if (!health.healthy)
				{
					this.logger.debug({ providerName, error: health.error, msg: `Provider unhealthy: ${providerName}` });
					continue;
				}

				// Generate content
				const response = await provider.generateContent(request);

				if (response.success)
				{
					// Record actual token usage
					if (response.metadata.tokensUsed)
					{
						await this.rateLimiter.recordUsage(
							providerName,
							provider.config.apiKeys[0],
							response.metadata.tokensUsed,
						);
					}

					// Update cost tracking
					if (response.metadata.cost)
					{
						await this.updateCostTracking(providerName, response.metadata.cost);
					}

					this.logger.debug({ providerName, domain: request.domain, tokensUsed: response.metadata.tokensUsed, cost: response.metadata.cost, msg: 'Content generated successfully' });

					return response;
				}

				lastError = response.error?.message || 'Unknown error';

				// Don't retry on non-retryable errors
				if (response.error && !response.error.retryable)
				{
					break;
				}
			}
			catch (error)
			{
				lastError = (error as Error).message;
				this.logger.error({ providerName, error: (error as Error).message, msg: 'Provider error' });
			}
		}

		// All providers failed, try rule-based fallback if enabled
		if (this.config.fallbackChain.fallbackToRuleBased)
		{
			return this.generateRuleBasedContent(request);
		}

		return {
			success: false,
			metadata: {
				provider: 'all-failed',
				processingTime: 0,
				requestId: `failed-${Date.now()}`,
			},
			error: {
				code: 'ALL_PROVIDERS_FAILED',
				message: `All providers failed. Last error: ${lastError}`,
				retryable: true,
			},
		};
	}

	/**
	 * Get statistics for all providers
	 */
	async getProviderStats(): Promise<ProviderStatsType[]>
	{
		const stats: ProviderStatsType[] = [];

		for (const [name, provider] of Array.from(this.providers.entries()))
		{
			const health = await provider.healthCheck();
			const usage = await provider.getUsage();

			stats.push({
				name,
				enabled: provider.config.enabled || false,
				healthy: health.healthy,
				requestsToday: usage.requestsToday,
				tokensUsedToday: usage.tokensUsedToday,
				costToday: usage.costToday,
				successRate: usage.successRate,
				averageLatency: usage.averageLatency,
				lastUsed: usage.lastUsed,
				activeKeys: provider.config.apiKeys.filter(key => key.length > 0).length,
				totalKeys: provider.config.apiKeys.length,
			});
		}

		return stats;
	}

	/**
	 * Get current cost tracking information
	 */
	getCostTracking(): CostTrackingType
	{
		return { ...this.costTracking };
	}

	/**
	 * Get request queue statistics
	 */
	getQueueStats()
	{
		return this.requestQueue.getStats();
	}

	/**
	 * Manually trigger key rotation
	 */
	async rotateKeys(): Promise<void>
	{
		this.logger.info({ msg: 'Manual key rotation triggered' });

		for (const [name, provider] of Array.from(this.providers.entries()))
		{
			if (provider.config.apiKeys.length > 1)
			{
				// Rotate to next key (this is handled internally by BaseAIProvider)
				this.logger.debug({ providerName: name, msg: 'Keys rotated for provider' });
			}
		}
	}

	/**
	 * Update provider configuration
	 */
	updateProviderConfig(providerName: string, config: Partial<AIProviderConfigType>): void
	{
		const provider = this.providers.get(providerName);
		if (!provider)
		{
			throw new Error(`Provider not found: ${providerName}`);
		}

		// Update configuration
		Object.assign(provider.config, config);

		// Update rate limiting if needed
		if (config.apiKeys || config.rateLimitPerMinute)
		{
			this.setupRateLimitingForProvider(providerName, provider.config);
		}

		this.logger.info({ providerName, changes: Object.keys(config), msg: 'Provider configuration updated' });
	}

	/**
	 * Cleanup resources
	 */
	destroy(): void
	{
		if (this.keyRotationTimer)
		{
			clearInterval(this.keyRotationTimer);
		}

		this.requestQueue.destroy();
		this.proxyManager?.destroy();

		this.logger.info({ msg: 'AI Provider Manager destroyed' });
	}

	private initializeProviders(): void
	{
		const providerClasses = {
			openai: OpenAIProvider,
			claude: ClaudeProvider,
			gemini: GeminiProvider,
			openrouter: OpenRouterProvider,
		};

		for (const [name, config] of Object.entries(this.config.providers))
		{
			const ProviderClass = providerClasses[name as keyof typeof providerClasses];
			if (!ProviderClass)
			{
				this.logger.warn({ providerName: name, msg: 'Unknown provider type' });
				continue;
			}

			try
			{
				// Setup proxy if configured
				if (config.proxyConfig?.enabled)
				{
					this.proxyManager = new ProxyManager(config.proxyConfig, sharedLogger);
				}

				const provider = new ProviderClass(config, this.logger);
				this.providers.set(name, provider);

				this.logger.info({ providerName: name, enabled: config.enabled, keyCount: config.apiKeys.length, msg: 'Provider initialized' });
			}
			catch (error)
			{
				this.logger.error({ providerName: name, error: (error as Error).message, msg: 'Failed to initialize provider' });
			}
		}
	}

	private setupRateLimiting(): void
	{
		for (const [name, config] of Object.entries(this.config.providers))
		{
			this.setupRateLimitingForProvider(name, config);
		}
	}

	private setupRateLimitingForProvider(name: string, config: AIProviderConfigType): void
	{
		if (!config.rateLimitPerMinute)
		{
			return;
		}

		for (const apiKey of config.apiKeys)
		{
			const rateLimitConfig: RateLimitConfig = {
				provider: name,
				apiKey,
				requestsPerMinute: config.rateLimitPerMinute,
				tokensPerMinute: config.maxTokens ? config.maxTokens * config.rateLimitPerMinute : undefined,
			};

			this.rateLimiter.addConfig(rateLimitConfig);
		}
	}

	private selectProviders(): string[]
	{
		const availableProviders = Array.from(this.providers.keys())
			.filter(name => this.providers.get(name)?.config.enabled);

		if (this.config.fallbackChain.providers.length > 0)
		{
			// Use configured fallback chain
			return this.config.fallbackChain.providers.filter(name => availableProviders.includes(name));
		}

		// Use all available providers sorted by priority
		return availableProviders.sort((a, b) =>
		{
			const priorityA = this.providers.get(a)?.config.priority || 0;
			const priorityB = this.providers.get(b)?.config.priority || 0;
			return priorityB - priorityA; // Higher priority first
		});
	}

	private estimateTokens(request: AIContentRequestType): number
	{
		// Rough estimation based on request content
		const domainLength = request.domain.length;
		const existingContentLength = request.existingContent?.summary?.length || 0;
		const contextLength = JSON.stringify(request.context || {}).length;

		// Estimate: ~4 characters per token
		return Math.ceil((domainLength + existingContentLength + contextLength + 500) / 4);
	}

	private async updateCostTracking(provider: string, cost: number): Promise<void>
	{
		this.costTracking.totalCostToday += cost;
		this.costTracking.totalCostThisMonth += cost;
		this.costTracking.costByProvider[provider] = (this.costTracking.costByProvider[provider] || 0) + cost;

		// Check budget limits
		const dailyBudget = this.config.costTracking.dailyBudget;
		const monthlyBudget = this.config.costTracking.monthlyBudget;

		if (dailyBudget && this.costTracking.totalCostToday >= dailyBudget)
		{
			this.costTracking.budgetExceeded = true;
			this.costTracking.remainingBudget = 0;

			this.logger.warn({ totalCost: this.costTracking.totalCostToday, budget: dailyBudget, msg: 'Daily budget exceeded' });
		}
		else if (monthlyBudget && this.costTracking.totalCostThisMonth >= monthlyBudget)
		{
			this.costTracking.budgetExceeded = true;
			this.costTracking.remainingBudget = 0;

			this.logger.warn({ totalCost: this.costTracking.totalCostThisMonth, budget: monthlyBudget, msg: 'Monthly budget exceeded' });
		}
		else
		{
			const dailyRemaining = dailyBudget ? dailyBudget - this.costTracking.totalCostToday : Infinity;
			const monthlyRemaining = monthlyBudget ? monthlyBudget - this.costTracking.totalCostThisMonth : Infinity;
			this.costTracking.remainingBudget = Math.min(dailyRemaining, monthlyRemaining);
		}

		// Persist cost tracking to Redis
		await this.saveCostTracking();
	}

	private async loadCostTracking(): Promise<void>
	{
		try
		{
			const data = await this.redis.get('ai_cost_tracking');
			if (data)
			{
				this.costTracking = { ...this.costTracking, ...(data as Partial<CostTrackingType>) };
			}
		}
		catch (error)
		{
			this.logger.warn({ error: (error as Error).message, msg: 'Failed to load cost tracking' });
		}
	}

	private async saveCostTracking(): Promise<void>
	{
		try
		{
			await this.redis.setex('ai_cost_tracking', 86400, JSON.stringify(this.costTracking));
		}
		catch (error)
		{
			this.logger.warn({ error: (error as Error).message, msg: 'Failed to save cost tracking' });
		}
	}

	private startKeyRotation(): void
	{
		if (!this.config.keyRotation.enabled)
		{
			return;
		}

		const intervalMs = this.config.keyRotation.rotationInterval * 60 * 1000;

		this.keyRotationTimer = setInterval(() =>
		{
			this.rotateKeys().catch((error) =>
			{
				this.logger.error({ error: (error as Error).message, msg: 'Key rotation failed' });
			});
		}, intervalMs);

		this.logger.info({ interval: this.config.keyRotation.rotationInterval, msg: 'Key rotation started' });
	}

	private generateRuleBasedContent(request: AIContentRequestType): AIContentResponseType
	{
		// Fallback to simple rule-based content generation
		const domain = request.domain;
		const summary = `${domain} is a domain that provides various services and content. This website offers information and resources related to its domain name and associated services.`;

		return ({
			success: true,
			content: {
				summary,
				category: { primary: 'General' },
				tags: ['website', 'domain', 'services', 'information', 'resources'],
				seoSummary: `${domain} - Domain providing various services and content`,
				wordCount: summary.split(/\s+/).length,
				confidence: 0.3, // Low confidence for rule-based content
			},
			metadata: {
				provider: 'rule-based',
				processingTime: 1,
				requestId: `rule-${Date.now()}`,
			},
		});
	}
}

export type { AIProviderManagerConfigType, ProviderStatsType, CostTrackingType };

export default AIProviderManager;
