import { logger as sharedLogger, type LoggerInstanceType } from '@shared';
import type {
	AIProviderConfigType,
	AIContentRequestType,
	AIContentResponseType,
	AIProviderHealthType,
	AIProviderUsageType,
	AIProviderType,
} from './types';

/**
 * Base implementation for AI providers with common functionality
 */
abstract class BaseAIProvider implements AIProviderType
{
	protected logger: LoggerInstanceType;

	protected currentKeyIndex: number = 0;

	protected health: AIProviderHealthType;

	protected usage: AIProviderUsageType;

	constructor(
		public readonly name: string,
		public readonly config: AIProviderConfigType,
		logger?: LoggerInstanceType,
	)
	{
		this.logger = logger || sharedLogger.getLogger(`AIProvider:${name}`);
		this.health = {
			healthy: true,
			lastChecked: new Date(),
			consecutiveFailures: 0,
		};
		this.usage = {
			requestsToday: 0,
			tokensUsedToday: 0,
			costToday: 0,
			successRate: 1.0,
			averageLatency: 0,
		};
	}

	/**
	 * Generate enhanced content for a domain
	 */
	async generateContent(request: AIContentRequestType): Promise<AIContentResponseType>
	{
		if (!this.config.enabled)
		{
			return this.createErrorResponse('Provider disabled', 'PROVIDER_DISABLED', false);
		}

		if (!this.health.healthy)
		{
			return this.createErrorResponse('Provider unhealthy', 'PROVIDER_UNHEALTHY', true);
		}

		const startTime = Date.now();
		const requestId = this.generateRequestId();

		try
		{
			this.logger.debug({ domain: request.domain, requestId, msg: 'Generating content' });

			const response = await this.performRequest(request, requestId);
			const processingTime = Date.now() - startTime;

			// Update usage statistics
			this.updateUsageStats(true, processingTime, response.metadata.tokensUsed || 0);

			// Reset consecutive failures on success
			this.health.consecutiveFailures = 0;
			this.health.healthy = true;

			return {
				...response,
				metadata: {
					...response.metadata,
					processingTime,
					requestId,
				},
			};
		}
		catch (error)
		{
			const processingTime = Date.now() - startTime;
			this.updateUsageStats(false, processingTime, 0);
			this.handleError(error);

			return this.createErrorResponse(
				(error as Error).message,
				this.getErrorCode(error),
				this.isRetryableError(error),
				requestId,
				processingTime,
			);
		}
	}

	/**
	 * Check if the provider is healthy and available
	 */
	async healthCheck(): Promise<AIProviderHealthType>
	{
		try
		{
			const startTime = Date.now();
			const isHealthy = await this.performHealthCheck();
			const latency = Date.now() - startTime;

			this.health = {
				healthy: isHealthy,
				latency,
				lastChecked: new Date(),
				consecutiveFailures: isHealthy ? 0 : this.health.consecutiveFailures + 1,
			};

			if (!isHealthy)
			{
				this.logger.warn({ provider: this.name, msg: 'Health check failed' });
			}
		}
		catch (error)
		{
			this.health = {
				healthy: false,
				error: (error as Error).message,
				lastChecked: new Date(),
				consecutiveFailures: this.health.consecutiveFailures + 1,
			};

			this.logger.error({ provider: this.name, error: (error as Error).message, msg: 'Health check error' });
		}

		return this.health;
	}

	/**
	 * Get current usage statistics
	 */
	async getUsage(): Promise<AIProviderUsageType>
	{
		return { ...this.usage };
	}

	/**
	 * Test the provider with a simple request
	 */
	async testConnection(): Promise<boolean>
	{
		try
		{
			const testRequest: AIContentRequestType =
			{
				domain: 'example.com',
				requirements: {
					minWords: 50,
					maxWords: 100,
					minTags: 3,
				},
			};

			const response = await this.generateContent(testRequest);
			return response.success;
		}
		catch (error)
		{
			this.logger.error({ provider: this.name, error: (error as Error).message, msg: 'Connection test failed' });
			return false;
		}
	}

	/**
	 * Get the next API key using round-robin rotation
	 */
	protected getNextApiKey(): string
	{
		if (this.config.apiKeys.length === 0)
		{
			throw new Error('No API keys configured');
		}

		const key = this.config.apiKeys[this.currentKeyIndex];
		this.currentKeyIndex = (this.currentKeyIndex + 1) % this.config.apiKeys.length;
		return key;
	}

	/**
	 * Create a standardized error response
	 */
	protected createErrorResponse(
		message: string,
		code: string,
		retryable: boolean,
		requestId?: string,
		processingTime?: number,
	): AIContentResponseType
	{
		return {
			success: false,
			metadata: {
				provider: this.name,
				processingTime: processingTime || 0,
				requestId: requestId || this.generateRequestId(),
			},
			error: {
				code,
				message,
				retryable,
			},
		};
	}

	/**
	 * Generate a unique request ID
	 */
	protected generateRequestId(): string
	{
		return `${this.name}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
	}

	/**
	 * Update usage statistics
	 */
	protected updateUsageStats(success: boolean, latency: number, tokensUsed: number): void
	{
		this.usage.requestsToday++;
		this.usage.tokensUsedToday += tokensUsed;
		this.usage.costToday += (this.config.costPerToken || 0) * tokensUsed;
		this.usage.lastUsed = new Date();

		// Update success rate (exponential moving average)
		const alpha = 0.1;
		this.usage.successRate = success
			? this.usage.successRate * (1 - alpha) + alpha
			: this.usage.successRate * (1 - alpha);

		// Update average latency (exponential moving average)
		this.usage.averageLatency = this.usage.averageLatency * (1 - alpha) + latency * alpha;
	}

	/**
	 * Handle errors and update health status
	 */
	protected handleError(error: Error): void
	{
		this.health.consecutiveFailures++;

		// Mark as unhealthy after 3 consecutive failures
		if (this.health.consecutiveFailures >= 3)
		{
			this.health.healthy = false;
		}

		this.logger.error({
			provider: this.name,
			error: (error as Error).message,
			consecutiveFailures: this.health.consecutiveFailures,
			msg: 'Provider error',
		});
	}

	/**
	 * Abstract methods to be implemented by specific providers
	 */
	protected abstract performRequest(request: AIContentRequestType, requestId: string): Promise<AIContentResponseType>;
	protected abstract performHealthCheck(): Promise<boolean>;
	protected abstract getErrorCode(error: Error): string;
	protected abstract isRetryableError(error: Error): boolean;
}

export default BaseAIProvider;
