import type { LoggerInstanceType } from '@shared';
import HttpClient from '@shared/utils/HttpClient';
import BaseAIProvider from '../BaseAIProvider';
import type {
	AIProviderConfigType,
	AIContentRequestType,
	AIContentResponseType,
} from '../types';

type OpenAIMessage =
{
	role: 'system' | 'user' | 'assistant';
	content: string;
};

type OpenAIRequest =
{
	model: string;
	messages: OpenAIMessage[];
	max_tokens?: number;
	temperature?: number;
	response_format?: { type: 'json_object' };
};

type OpenAIResponse =
{
	id: string;
	object: string;
	created: number;
	model: string;
	choices: Array<{
		index: number;
		message: {
			role: string;
			content: string;
		};
		finish_reason: string;
	}>;
	usage: {
		prompt_tokens: number;
		completion_tokens: number;
		total_tokens: number;
	};
};

/**
 * OpenAI provider implementation for content generation
 */
class OpenAIProvider extends BaseAIProvider
{
	private client: HttpClient;

	private readonly model: string;

	constructor(config: AIProviderConfigType, logger?: LoggerInstanceType)
	{
		super('openai', config, logger);

		this.model = 'gpt-4o-mini'; // Cost-effective model for content generation
		this.client = new HttpClient(this.logger, {
			baseURL: config.baseUrl || 'https://api.openai.com/v1',
			timeout: config.timeout || 30000,
		});
	}

	protected async performRequest(request: AIContentRequestType, requestId: string): Promise<AIContentResponseType>
	{
		const apiKey = this.getNextApiKey();
		const prompt = this.buildPrompt(request);

		const openaiRequest: OpenAIRequest = {
			model: this.model,
			messages: [
				{
					role: 'system',
					content: 'You are an expert content writer specializing in domain descriptions and SEO content. Always respond with valid JSON.',
				},
				{
					role: 'user',
					content: prompt,
				},
			],
			max_tokens: this.config.maxTokens || 1500,
			temperature: this.config.temperature || 0.7,
			response_format: { type: 'json_object' },
		};

		const response = await this.client.post<OpenAIResponse>('/chat/completions', openaiRequest, {
			headers: {
				Authorization: `Bearer ${apiKey}`,
				'Content-Type': 'application/json',
			},
		});

		const responseData = response.data;
		const content = responseData.choices[0]?.message?.content;
		if (!content)
		{
			throw new Error('No content in OpenAI response');
		}

		const parsedContent = JSON.parse(content);
		const wordCount = parsedContent.summary?.split(/\s+/).length || 0;

		return {
			success: true,
			content: {
				summary: parsedContent.summary || '',
				category: {
					primary: parsedContent.category?.primary || 'General',
					secondary: parsedContent.category?.secondary,
				},
				tags: parsedContent.tags || [],
				seoSummary: parsedContent.seoSummary || parsedContent.summary?.substring(0, 160) || '',
				wordCount,
				confidence: parsedContent.confidence || 0.8,
			},
			metadata: {
				provider: this.name,
				model: this.model,
				tokensUsed: responseData.usage.total_tokens,
				cost: (this.config.costPerToken || 0) * responseData.usage.total_tokens,
				processingTime: 0, // Will be set by base class
				requestId,
			},
		};
	}

	protected async performHealthCheck(): Promise<boolean>
	{
		try
		{
			const apiKey = this.getNextApiKey();
			const response = await this.client.get('/models', {
				headers: {
					Authorization: `Bearer ${apiKey}`,
				},
			});

			return response.statusCode === 200;
		}
		catch (error)
		{
			return false;
		}
	}

	protected getErrorCode(error: any): string
	{
		// Check if error has statusCode property (from HttpClient)
		if (error && typeof error === 'object' && 'statusCode' in error)
		{
			const statusCode = error.statusCode;
			switch (statusCode)
			{
				case 401:
					return 'INVALID_API_KEY';
				case 429:
					return 'RATE_LIMITED';
				case 500:
				case 502:
				case 503:
				case 504:
					return 'SERVER_ERROR';
				default:
					return 'REQUEST_FAILED';
			}
		}
		return 'UNKNOWN_ERROR';
	}

	protected isRetryableError(error: any): boolean
	{
		// Check if error has statusCode property (from HttpClient)
		if (error && typeof error === 'object' && 'statusCode' in error)
		{
			const status = error.statusCode;

			// Retry on server errors and rate limits (with backoff)
			return status === 429 || (status >= 500 && status <= 599);
		}
		return false;
	}

	private buildPrompt(request: AIContentRequestType): string
	{
		const {
			domain, existingContent, requirements, context,
		} = request;

		let prompt = `Generate comprehensive content for the domain "${domain}". `;

		if (existingContent?.summary)
		{
			prompt += `Enhance and expand this existing content: "${existingContent.summary}". `;
		}

		prompt += `Requirements:
- Generate a detailed summary of ${requirements.minWords}-${requirements.maxWords} words
- Classify into primary and optional secondary category
- Generate ${requirements.minTags}+ relevant tags
- Create an SEO-optimized summary (150-160 characters)
- Language: ${requirements.language || 'English'}
- Tone: ${requirements.tone || 'professional'}`;

		if (requirements.includeKeywords?.length)
		{
			prompt += `
- Include these keywords naturally: ${requirements.includeKeywords.join(', ')}`;
		}

		if (context?.industry)
		{
			prompt += `
- Industry context: ${context.industry}`;
		}

		if (context?.audience)
		{
			prompt += `
- Target audience: ${context.audience}`;
		}

		prompt += `

Respond with valid JSON in this exact format:
{
  "summary": "Detailed ${requirements.minWords}+ word description...",
  "category": {
    "primary": "Primary category",
    "secondary": "Optional secondary category"
  },
  "tags": ["tag1", "tag2", "tag3", "tag4", "tag5"],
  "seoSummary": "SEO-optimized 150-160 character summary...",
  "confidence": 0.85
}`;

		return prompt;
	}
}

export default OpenAIProvider;
