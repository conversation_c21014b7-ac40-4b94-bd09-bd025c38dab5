import type { LoggerInstanceType } from '@shared';
import HttpClient from '@shared/utils/HttpClient';
import BaseA<PERSON>rovider from '../BaseAIProvider';
import type {
	AIProviderConfigType,
	AIContentRequestType,
	AIContentResponseType,
} from '../types';

type ClaudeMessage =
{
	role: 'user' | 'assistant';
	content: string;
};

type ClaudeRequest =
{
	model: string;
	max_tokens: number;
	messages: ClaudeMessage[];
	temperature?: number;
	system?: string;
};

type ClaudeResponse =
{
	id: string;
	type: string;
	role: string;
	content: Array<{
		type: string;
		text: string;
	}>;
	model: string;
	stop_reason: string;
	stop_sequence: string | null;
	usage: {
		input_tokens: number;
		output_tokens: number;
	};
};

/**
 * Claude (Anthropic) provider implementation for content generation
 */
class Claude<PERSON>rovider extends BaseAIProvider
{
	private client: HttpClient;

	private readonly model: string;

	constructor(config: AIProviderConfigType, logger?: LoggerInstanceType)
	{
		super('claude', config, logger);

		this.model = 'claude-3-haiku-20240307'; // Cost-effective model
		this.client = new HttpClient(this.logger, {
			baseURL: config.baseUrl || 'https://api.anthropic.com/v1',
			timeout: config.timeout || 30000,
		});
	}

	protected async performRequest(request: AIContentRequestType, requestId: string): Promise<AIContentResponseType>
	{
		const apiKey = this.getNextApiKey();
		const prompt = this.buildPrompt(request);

		const claudeRequest: ClaudeRequest = {
			model: this.model,
			max_tokens: this.config.maxTokens || 1500,
			messages: [
				{
					role: 'user',
					content: prompt,
				},
			],
			temperature: this.config.temperature || 0.7,
			system: 'You are an expert content writer specializing in domain descriptions and SEO content. Always respond with valid JSON only, no additional text.',
		};

		const response = await this.client.post<ClaudeResponse>('/messages', claudeRequest, {
			headers: {
				'x-api-key': apiKey,
				'Content-Type': 'application/json',
				'anthropic-version': '2023-06-01',
			},
		});

		const content = response.data.content[0]?.text;
		if (!content)
		{
			throw new Error('No content in Claude response');
		}

		// Claude sometimes adds extra text, extract JSON
		const jsonMatch = content.match(/\{[\s\S]*\}/);
		const jsonContent = jsonMatch ? jsonMatch[0] : content;

		const parsedContent = JSON.parse(jsonContent);
		const wordCount = parsedContent.summary?.split(/\s+/).length || 0;

		return {
			success: true,
			content: {
				summary: parsedContent.summary || '',
				category: {
					primary: parsedContent.category?.primary || 'General',
					secondary: parsedContent.category?.secondary,
				},
				tags: parsedContent.tags || [],
				seoSummary: parsedContent.seoSummary || parsedContent.summary?.substring(0, 160) || '',
				wordCount,
				confidence: parsedContent.confidence || 0.8,
			},
			metadata: {
				provider: this.name,
				model: this.model,
				tokensUsed: response.data.usage.input_tokens + response.data.usage.output_tokens,
				cost: (this.config.costPerToken || 0) * (response.data.usage.input_tokens + response.data.usage.output_tokens),
				processingTime: 0, // Will be set by base class
				requestId,
			},
		};
	}

	protected async performHealthCheck(): Promise<boolean>
	{
		try
		{
			const apiKey = this.getNextApiKey();

			// Claude doesn't have a models endpoint, so we make a minimal request
			const testRequest: ClaudeRequest = {
				model: this.model,
				max_tokens: 10,
				messages: [
					{
						role: 'user',
						content: 'Hello',
					},
				],
			};

			const response = await this.client.post('/messages', testRequest, {
				headers: {
					'x-api-key': apiKey,
					'Content-Type': 'application/json',
					'anthropic-version': '2023-06-01',
				},
			});

			return response.statusCode === 200;
		}
		catch (error)
		{
			return false;
		}
	}

	protected getErrorCode(error: any): string
	{
		// Check if error has statusCode property (from HttpClient)
		if (error && typeof error === 'object' && 'statusCode' in error)
		{
			const statusCode = error.statusCode;
			switch (statusCode)
			{
				case 401:
					return 'INVALID_API_KEY';
				case 429:
					return 'RATE_LIMITED';
				case 500:
				case 502:
				case 503:
				case 504:
					return 'SERVER_ERROR';
				default:
					return 'REQUEST_FAILED';
			}
		}
		return 'UNKNOWN_ERROR';
	}

	protected isRetryableError(error: any): boolean
	{
		// Check if error has statusCode property (from HttpClient)
		if (error && typeof error === 'object' && 'statusCode' in error)
		{
			const status = error.statusCode;

			// Retry on server errors and rate limits (with backoff)
			return status === 429 || (status >= 500 && status <= 599);
		}
		return false;
	}

	private buildPrompt(request: AIContentRequestType): string
	{
		const {
			domain, existingContent, requirements, context,
		} = request;

		let prompt = `Generate comprehensive content for the domain "${domain}". `;

		if (existingContent?.summary)
		{
			prompt += `Enhance and expand this existing content: "${existingContent.summary}". `;
		}

		prompt += `Requirements:
- Generate a detailed summary of ${requirements.minWords}-${requirements.maxWords} words
- Classify into primary and optional secondary category
- Generate ${requirements.minTags}+ relevant tags
- Create an SEO-optimized summary (150-160 characters)
- Language: ${requirements.language || 'English'}
- Tone: ${requirements.tone || 'professional'}`;

		if (requirements.includeKeywords?.length)
		{
			prompt += `
- Include these keywords naturally: ${requirements.includeKeywords.join(', ')}`;
		}

		if (context?.industry)
		{
			prompt += `
- Industry context: ${context.industry}`;
		}

		if (context?.audience)
		{
			prompt += `
- Target audience: ${context.audience}`;
		}

		prompt += `

Respond with valid JSON only in this exact format:
{
  "summary": "Detailed ${requirements.minWords}+ word description...",
  "category": {
    "primary": "Primary category",
    "secondary": "Optional secondary category"
  },
  "tags": ["tag1", "tag2", "tag3", "tag4", "tag5"],
  "seoSummary": "SEO-optimized 150-160 character summary...",
  "confidence": 0.85
}`;

		return prompt;
	}
}

export default ClaudeProvider;
