import type { LoggerInstanceType } from '@shared';
import HttpClient from '@shared/utils/HttpClient';
import type {
	AIProviderConfigType,
	AIContentRequestType,
	AIContentResponseType,
} from '../types';

import BaseAIProvider from '../BaseAIProvider';

type GeminiContentType =
{
	parts: Array<{
		text: string;
	}>;
};

type GeminiRequestType =
{
	contents: Array<{
		role?: string;
		parts: Array<{
			text: string;
		}>;
	}>;
	generationConfig?: {
		temperature?: number;
		maxOutputTokens?: number;
		responseMimeType?: string;
	};
	systemInstruction?: {
		parts: Array<{
			text: string;
		}>;
	};
};

type GeminiResponseType =
{
	candidates: Array<{
		content: GeminiContentType;
		finishReason: string;
		index: number;
		safetyRatings: Array<{
			category: string;
			probability: string;
		}>;
	}>;
	usageMetadata: {
		promptTokenCount: number;
		candidatesTokenCount: number;
		totalTokenCount: number;
	};
};

/**
 * Google Gemini provider implementation for content generation
 */
class GeminiProvider extends BaseAIProvider
{
	private client: HttpClient;

	private readonly model: string;

	constructor(config: AIProviderConfigType, logger?: LoggerInstanceType)
	{
		super('gemini', config, logger);

		this.model = 'gemini-1.5-flash'; // Cost-effective model
		this.client = new HttpClient(this.logger, {
			baseURL: config.baseUrl || 'https://generativelanguage.googleapis.com/v1beta',
			timeout: config.timeout || 30000,
		});
	}

	protected async performRequest(request: AIContentRequestType, requestId: string): Promise<AIContentResponseType>
	{
		const apiKey = this.getNextApiKey();
		const prompt = this.buildPrompt(request);

		const geminiRequest: GeminiRequestType =
		{
			contents: [
				{
					role: 'user',
					parts: [
						{
							text: prompt,
						},
					],
				},
			],
			generationConfig: {
				temperature: this.config.temperature || 0.7,
				maxOutputTokens: this.config.maxTokens || 1500,
				responseMimeType: 'application/json',
			},
			systemInstruction: {
				parts: [
					{
						text: 'You are an expert content writer specializing in domain descriptions and SEO content. Always respond with valid JSON only.',
					},
				],
			},
		};

		const response = await this.client.post<GeminiResponseType>(
			`/models/${this.model}:generateContent?key=${apiKey}`,
			geminiRequest,
			{
				headers: {
					'Content-Type': 'application/json',
				},
			},
		);

		const geminiResponse = response.data;
		const content = geminiResponse.candidates[0]?.content?.parts[0]?.text;
		if (!content)
		{
			throw new Error('No content in Gemini response');
		}

		const parsedContent = JSON.parse(content);
		const wordCount = parsedContent.summary?.split(/\s+/).length || 0;

		return {
			success: true,
			content: {
				summary: parsedContent.summary || '',
				category: {
					primary: parsedContent.category?.primary || 'General',
					secondary: parsedContent.category?.secondary,
				},
				tags: parsedContent.tags || [],
				seoSummary: parsedContent.seoSummary || parsedContent.summary?.substring(0, 160) || '',
				wordCount,
				confidence: parsedContent.confidence || 0.8,
			},
			metadata: {
				provider: this.name,
				model: this.model,
				tokensUsed: geminiResponse.usageMetadata.totalTokenCount,
				cost: (this.config.costPerToken || 0) * geminiResponse.usageMetadata.totalTokenCount,
				processingTime: 0, // Will be set by base class
				requestId,
			},
		};
	}

	protected async performHealthCheck(): Promise<boolean>
	{
		try
		{
			const apiKey = this.getNextApiKey();

			// Make a minimal request to test the API
			const testRequest: GeminiRequestType =
			{
				contents: [
					{
						parts: [
							{
								text: 'Hello',
							},
						],
					},
				],
				generationConfig: {
					maxOutputTokens: 10,
				},
			};

			const response = await this.client.post(
				`/models/${this.model}:generateContent?key=${apiKey}`,
				testRequest,
				{
					headers: {
						'Content-Type': 'application/json',
					},
				},
			);

			return response.statusCode === 200;
		}
		catch (error)
		{
			return false;
		}
	}

	protected getErrorCode(error: any): string
	{
		// Check if error has statusCode property (from HttpClient)
		if (error && typeof error === 'object' && 'statusCode' in error)
		{
			const statusCode = error.statusCode;
			switch (statusCode)
			{
				case 400:
					return 'INVALID_REQUEST';
				case 401:
				case 403:
					return 'INVALID_API_KEY';
				case 429:
					return 'RATE_LIMITED';
				case 500:
				case 502:
				case 503:
				case 504:
					return 'SERVER_ERROR';
				default:
					return 'REQUEST_FAILED';
			}
		}
		return 'UNKNOWN_ERROR';
	}

	protected isRetryableError(error: any): boolean
	{
		// Check if error has statusCode property (from HttpClient)
		if (error && typeof error === 'object' && 'statusCode' in error)
		{
			const status = error.statusCode;

			// Retry on server errors and rate limits (with backoff)
			return status === 429 || (status >= 500 && status <= 599);
		}
		return false;
	}

	private buildPrompt(request: AIContentRequestType): string
	{
		const {
			domain, existingContent, requirements, context,
		} = request;

		let prompt = `Generate comprehensive content for the domain "${domain}". `;

		if (existingContent?.summary)
		{
			prompt += `Enhance and expand this existing content: "${existingContent.summary}". `;
		}

		prompt += `Requirements:
- Generate a detailed summary of ${requirements.minWords}-${requirements.maxWords} words
- Classify into primary and optional secondary category
- Generate ${requirements.minTags}+ relevant tags
- Create an SEO-optimized summary (150-160 characters)
- Language: ${requirements.language || 'English'}
- Tone: ${requirements.tone || 'professional'}`;

		if (requirements.includeKeywords?.length)
		{
			prompt += `
- Include these keywords naturally: ${requirements.includeKeywords.join(', ')}`;
		}

		if (context?.industry)
		{
			prompt += `
- Industry context: ${context.industry}`;
		}

		if (context?.audience)
		{
			prompt += `
- Target audience: ${context.audience}`;
		}

		prompt += `

Respond with valid JSON in this exact format:
{
  "summary": "Detailed ${requirements.minWords}+ word description...",
  "category": {
    "primary": "Primary category",
    "secondary": "Optional secondary category"
  },
  "tags": ["tag1", "tag2", "tag3", "tag4", "tag5"],
  "seoSummary": "SEO-optimized 150-160 character summary...",
  "confidence": 0.85
}`;

		return prompt;
	}
}

export default GeminiProvider;
