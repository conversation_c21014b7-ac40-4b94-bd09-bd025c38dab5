// import type { Logger } from '@shared';

/**
 * Configuration for AI provider authentication and settings
 */
type AIProviderConfigType =
{
	name: string;
	apiKeys: string[];
	baseUrl?: string;
	maxTokens?: number;
	temperature?: number;
	timeout?: number;
	retryAttempts?: number;
	rateLimitPerMinute?: number;
	costPerToken?: number;
	priority?: number;
	enabled?: boolean;
	proxyConfig?: ProxyConfigType;
};

/**
 * Proxy configuration for AI requests
 */
type ProxyConfigType =
{
	enabled: boolean;
	urls: string[];
	rotation: 'round-robin' | 'random' | 'failover';
	timeout?: number;
	maxRetries?: number;
};

/**
 * Request parameters for AI content generation
 */
type AIContentRequestType =
{
	domain: string;
	existingContent?: {
		summary?: string;
		category?: {
			primary: string;
			secondary?: string;
		};
		tags?: string[];
	};
	requirements: {
		minWords: number;
		maxWords: number;
		minTags: number;
		language?: string;
		tone?: 'professional' | 'casual' | 'technical';
		includeKeywords?: string[];
	};
	context?: {
		industry?: string;
		audience?: string;
		purpose?: string;
	};
};

/**
 * Response from AI provider
 */
type AIContentResponseType =
{
	success: boolean;
	content?: {
		summary: string;
		category: {
			primary: string;
			secondary?: string;
		};
		tags: string[];
		seoSummary: string;
		wordCount: number;
		confidence: number;
	};
	metadata: {
		provider: string;
		model?: string;
		tokensUsed?: number;
		cost?: number;
		processingTime: number;
		requestId: string;
	};
	error?: {
		code: string;
		message: string;
		retryable: boolean;
	};
};

/**
 * Health check result for AI provider
 */
type AIProviderHealthType =
{
	healthy: boolean;
	latency?: number;
	error?: string;
	lastChecked: Date;
	consecutiveFailures: number;
};

/**
 * Usage statistics for AI provider
 */
type AIProviderUsageType =
{
	requestsToday: number;
	tokensUsedToday: number;
	costToday: number;
	successRate: number;
	averageLatency: number;
	lastUsed?: Date;
};

/**
 * Abstract interface for AI content providers
 */
type AIProviderType =
{
	readonly name: string;
	readonly config: AIProviderConfigType;

	/**
	 * Generate enhanced content for a domain
	 */
	generateContent(request: AIContentRequestType): Promise<AIContentResponseType>;

	/**
	 * Check if the provider is healthy and available
	 */
	healthCheck(): Promise<AIProviderHealthType>;

	/**
	 * Get current usage statistics
	 */
	getUsage(): Promise<AIProviderUsageType>;

	/**
	 * Test the provider with a simple request
	 */
	testConnection(): Promise<boolean>;
};

/**
 * Provider selection strategy
 */
type ProviderSelectionStrategyType = 'priority' | 'round-robin' | 'least-used' | 'fastest';

/**
 * Fallback chain configuration
 */
type FallbackChainType =
{
	providers: string[];
	strategy: ProviderSelectionStrategyType;
	maxRetries: number;
	fallbackToRuleBased: boolean;
};

export type {
	AIProviderConfigType,
	ProxyConfigType,
	AIContentRequestType,
	AIContentResponseType,
	AIProviderHealthType,
	AIProviderUsageType,
	AIProviderType,
	ProviderSelectionStrategyType,
	FallbackChainType,
};
