import { logger as sharedLogger } from '@shared';
import type { AIProviderConfigType, ProxyConfigType, FallbackChainType, ProviderSelectionStrategyType } from './types';
import type { AIProviderManagerConfigType } from './AIProviderManager';
import type { BatchConfig } from './AIRequestQueue';

interface EnvironmentConfig
{
	// OpenAI Configuration
	OPENAI_API_KEYS?: string;
	OPENAI_BASE_URL?: string;
	OPENAI_MAX_TOKENS?: string;
	OPENAI_TEMPERATURE?: string;
	OPENAI_RATE_LIMIT?: string;
	OPENAI_COST_PER_TOKEN?: string;
	OPENAI_PRIORITY?: string;
	OPENAI_ENABLED?: string;

	// Claude Configuration
	CLAUDE_API_KEYS?: string;
	CLAUDE_BASE_URL?: string;
	CLAUDE_MAX_TOKENS?: string;
	CLAUDE_TEMPERATURE?: string;
	CLAUDE_RATE_LIMIT?: string;
	CLAUDE_COST_PER_TOKEN?: string;
	CLAUDE_PRIORITY?: string;
	CLAUDE_ENABLED?: string;

	// Gemini Configuration
	GEMINI_API_KEYS?: string;
	GEMINI_BASE_URL?: string;
	GEMINI_MAX_TOKENS?: string;
	GEMINI_TEMPERATURE?: string;
	GEMINI_RATE_LIMIT?: string;
	GEMINI_COST_PER_TOKEN?: string;
	GEMINI_PRIORITY?: string;
	GEMINI_ENABLED?: string;

	// OpenRouter Configuration
	OPENROUTER_API_KEYS?: string;
	OPENROUTER_BASE_URL?: string;
	OPENROUTER_MAX_TOKENS?: string;
	OPENROUTER_TEMPERATURE?: string;
	OPENROUTER_RATE_LIMIT?: string;
	OPENROUTER_COST_PER_TOKEN?: string;
	OPENROUTER_PRIORITY?: string;
	OPENROUTER_ENABLED?: string;

	// Proxy Configuration
	AI_PROXY_ENABLED?: string;
	AI_PROXY_URLS?: string;
	AI_PROXY_ROTATION?: string;
	AI_PROXY_TIMEOUT?: string;
	AI_PROXY_MAX_RETRIES?: string;

	// General AI Configuration
	AI_FALLBACK_PROVIDERS?: string;
	AI_FALLBACK_STRATEGY?: string;
	AI_FALLBACK_MAX_RETRIES?: string;
	AI_FALLBACK_TO_RULE_BASED?: string;

	// Batching Configuration
	AI_BATCH_ENABLED?: string;
	AI_BATCH_MAX_SIZE?: string;
	AI_BATCH_MAX_WAIT_TIME?: string;

	// Cost Tracking Configuration
	AI_COST_TRACKING_ENABLED?: string;
	AI_DAILY_BUDGET?: string;
	AI_MONTHLY_BUDGET?: string;

	// Key Rotation Configuration
	AI_KEY_ROTATION_ENABLED?: string;
	AI_KEY_ROTATION_INTERVAL?: string;
	AI_KEY_BALANCE_USAGE?: string;
}

/**
 * Loads and validates AI provider configuration from environment variables
 */
class AIConfigLoader
{
	private logger: ReturnType<typeof sharedLogger.getLogger>;

	private env: EnvironmentConfig;

	constructor(logger?: ReturnType<typeof sharedLogger.getLogger>, env: EnvironmentConfig = process.env as any)
	{
		this.logger = logger || sharedLogger.getLogger('AIConfigLoader');
		this.env = env;
	}

	/**
	 * Load complete AI provider manager configuration
	 */
	loadConfig(): AIProviderManagerConfigType
	{
		const providers = this.loadProviderConfigs();
		const fallbackChain = this.loadFallbackChain();
		const batchConfig = this.loadBatchConfig();
		const costTracking = this.loadCostTrackingConfig();
		const keyRotation = this.loadKeyRotationConfig();

		const config: AIProviderManagerConfigType = {
			providers,
			fallbackChain,
			batchConfig,
			costTracking,
			keyRotation,
		};

		this.validateConfig(config);
		this.logConfigSummary(config);

		return config;
	}

	/**
	 * Load configuration for all providers
	 */
	private loadProviderConfigs(): Record<string, AIProviderConfigType>
	{
		const providers: Record<string, AIProviderConfigType> = {};

		// Load OpenAI configuration
		if (this.env.OPENAI_API_KEYS)
		{
			providers.openai = this.loadProviderConfig('OPENAI', 'openai');
		}

		// Load Claude configuration
		if (this.env.CLAUDE_API_KEYS)
		{
			providers.claude = this.loadProviderConfig('CLAUDE', 'claude');
		}

		// Load Gemini configuration
		if (this.env.GEMINI_API_KEYS)
		{
			providers.gemini = this.loadProviderConfig('GEMINI', 'gemini');
		}

		// Load OpenRouter configuration
		if (this.env.OPENROUTER_API_KEYS)
		{
			providers.openrouter = this.loadProviderConfig('OPENROUTER', 'openrouter');
		}

		return providers;
	}

	/**
	 * Load configuration for a specific provider
	 */
	private loadProviderConfig(envPrefix: string, providerName: string): AIProviderConfigType
	{
		const apiKeysEnv = this.env[`${envPrefix}_API_KEYS` as keyof EnvironmentConfig];
		const apiKeys = apiKeysEnv ? apiKeysEnv.split(',').map(key => key.trim()) : [];

		if (apiKeys.length === 0)
		{
			throw new Error(`No API keys configured for ${providerName}`);
		}

		// Validate API keys format
		for (const key of apiKeys)
		{
			if (!this.isValidApiKey(key, providerName))
			{
				throw new Error(`Invalid API key format for ${providerName}: ${key.substring(0, 10)}...`);
			}
		}

		const config: AIProviderConfigType = {
			name: providerName,
			apiKeys,
			baseUrl: this.env[`${envPrefix}_BASE_URL` as keyof EnvironmentConfig],
			maxTokens: this.parseNumber(this.env[`${envPrefix}_MAX_TOKENS` as keyof EnvironmentConfig], 1500),
			temperature: this.parseNumber(this.env[`${envPrefix}_TEMPERATURE` as keyof EnvironmentConfig], 0.7),
			timeout: 30000,
			retryAttempts: 3,
			rateLimitPerMinute: this.parseNumber(this.env[`${envPrefix}_RATE_LIMIT` as keyof EnvironmentConfig], 60),
			costPerToken: this.parseNumber(this.env[`${envPrefix}_COST_PER_TOKEN` as keyof EnvironmentConfig], 0.001),
			priority: this.parseNumber(this.env[`${envPrefix}_PRIORITY` as keyof EnvironmentConfig], 1),
			enabled: this.parseBoolean(this.env[`${envPrefix}_ENABLED` as keyof EnvironmentConfig], true),
			proxyConfig: this.loadProxyConfig(),
		};

		return config;
	}

	/**
	 * Load proxy configuration
	 */
	private loadProxyConfig(): ProxyConfigType | undefined
	{
		const enabled = this.parseBoolean(this.env.AI_PROXY_ENABLED, false);

		if (!enabled)
		{
			return undefined;
		}

		const urls = this.env.AI_PROXY_URLS ? this.env.AI_PROXY_URLS.split(',').map(url => url.trim()) : [];

		if (urls.length === 0)
		{
			this.logger.warn('Proxy enabled but no URLs configured');
			return undefined;
		}

		return {
			enabled: true,
			urls,
			rotation: (this.env.AI_PROXY_ROTATION as any) || 'round-robin',
			timeout: this.parseNumber(this.env.AI_PROXY_TIMEOUT, 10000),
			maxRetries: this.parseNumber(this.env.AI_PROXY_MAX_RETRIES, 3),
		};
	}

	/**
	 * Load fallback chain configuration
	 */
	private loadFallbackChain(): FallbackChainType
	{
		const providersEnv = this.env.AI_FALLBACK_PROVIDERS;
		const providers = providersEnv ? providersEnv.split(',').map(p => p.trim()) : ['openai', 'claude', 'gemini', 'openrouter'];

		return {
			providers,
			strategy: (this.env.AI_FALLBACK_STRATEGY as ProviderSelectionStrategyType) || 'priority',
			maxRetries: this.parseNumber(this.env.AI_FALLBACK_MAX_RETRIES, 3) || 3,
			fallbackToRuleBased: this.parseBoolean(this.env.AI_FALLBACK_TO_RULE_BASED, true),
		};
	}

	/**
	 * Load batch configuration
	 */
	private loadBatchConfig(): BatchConfig
	{
		return {
			enableBatching: this.parseBoolean(this.env.AI_BATCH_ENABLED, false),
			maxBatchSize: this.parseNumber(this.env.AI_BATCH_MAX_SIZE, 10) || 10,
			maxWaitTime: this.parseNumber(this.env.AI_BATCH_MAX_WAIT_TIME, 5000) || 5000,
		};
	}

	/**
	 * Load cost tracking configuration
	 */
	private loadCostTrackingConfig()
	{
		return {
			enabled: this.parseBoolean(this.env.AI_COST_TRACKING_ENABLED, true),
			dailyBudget: this.parseNumber(this.env.AI_DAILY_BUDGET),
			monthlyBudget: this.parseNumber(this.env.AI_MONTHLY_BUDGET),
		};
	}

	/**
	 * Load key rotation configuration
	 */
	private loadKeyRotationConfig(): { enabled: boolean; rotationInterval: number; balanceUsage: boolean }
	{
		return {
			enabled: this.parseBoolean(this.env.AI_KEY_ROTATION_ENABLED, true),
			rotationInterval: this.parseNumber(this.env.AI_KEY_ROTATION_INTERVAL, 60) || 60, // minutes
			balanceUsage: this.parseBoolean(this.env.AI_KEY_BALANCE_USAGE, true),
		};
	}

	/**
	 * Validate API key format for different providers
	 */
	private isValidApiKey(key: string, provider: string): boolean
	{
		if (!key || key.length < 10)
		{
			return false;
		}

		switch (provider)
		{
			case 'openai':
				return key.startsWith('sk-') && key.length >= 20;
			case 'claude':
				return key.startsWith('sk-ant-') && key.length >= 20;
			case 'gemini':
				return key.length >= 20; // Google API keys don't have a specific prefix
			case 'openrouter':
				return key.startsWith('sk-or-') && key.length >= 20;
			default:
				return key.length >= 10; // Generic validation
		}
	}

	/**
	 * Parse string to number with optional default
	 */
	private parseNumber(value: string | undefined, defaultValue?: number): number | undefined
	{
		if (!value)
		{
			return defaultValue;
		}

		const parsed = parseFloat(value);
		if (isNaN(parsed))
		{
			if (defaultValue !== undefined)
			{
				this.logger.warn({ value, default: defaultValue }, 'Invalid number format, using default');
				return defaultValue;
			}
			throw new Error(`Invalid number format: ${value}`);
		}

		return parsed;
	}

	/**
	 * Parse string to boolean with optional default
	 */
	private parseBoolean(value: string | undefined, defaultValue: boolean = false): boolean
	{
		if (!value)
		{
			return defaultValue;
		}

		const lower = value.toLowerCase();
		return lower === 'true' || lower === '1' || lower === 'yes' || lower === 'on';
	}

	/**
	 * Validate the complete configuration
	 */
	private validateConfig(config: AIProviderManagerConfigType): void
	{
		// Check that at least one provider is configured
		const enabledProviders = Object.values(config.providers).filter(p => p.enabled);
		if (enabledProviders.length === 0)
		{
			throw new Error('No AI providers are enabled');
		}

		// Validate fallback chain references valid providers
		const providerNames = Object.keys(config.providers);
		for (const provider of config.fallbackChain.providers)
		{
			if (!providerNames.includes(provider))
			{
				throw new Error(`Fallback chain references unknown provider: ${provider}`);
			}
		}

		// Validate batch configuration
		if (config.batchConfig.enableBatching)
		{
			if (config.batchConfig.maxBatchSize < 1 || config.batchConfig.maxBatchSize > 100)
			{
				throw new Error('Batch size must be between 1 and 100');
			}

			if (config.batchConfig.maxWaitTime < 100 || config.batchConfig.maxWaitTime > 60000)
			{
				throw new Error('Batch wait time must be between 100ms and 60s');
			}
		}

		// Validate cost tracking
		if (config.costTracking.enabled)
		{
			if (config.costTracking.dailyBudget && config.costTracking.dailyBudget <= 0)
			{
				throw new Error('Daily budget must be positive');
			}

			if (config.costTracking.monthlyBudget && config.costTracking.monthlyBudget <= 0)
			{
				throw new Error('Monthly budget must be positive');
			}
		}

		// Validate key rotation
		if (config.keyRotation.enabled)
		{
			if (config.keyRotation.rotationInterval < 1 || config.keyRotation.rotationInterval > 1440)
			{
				throw new Error('Key rotation interval must be between 1 and 1440 minutes');
			}
		}
	}

	/**
	 * Log configuration summary (without sensitive data)
	 */
	private logConfigSummary(config: AIProviderManagerConfigType): void
	{
		const summary = {
			providers: Object.entries(config.providers).map(([name, cfg]) => ({
				name,
				enabled: cfg.enabled,
				keys: cfg.apiKeys.length,
				priority: cfg.priority,
				rateLimit: cfg.rateLimitPerMinute,
			})),
			fallbackChain: {
				providers: config.fallbackChain.providers,
				strategy: config.fallbackChain.strategy,
				fallbackToRuleBased: config.fallbackChain.fallbackToRuleBased,
			},
			batching: {
				enabled: config.batchConfig.enableBatching,
				maxSize: config.batchConfig.maxBatchSize,
				maxWait: config.batchConfig.maxWaitTime,
			},
			costTracking: {
				enabled: config.costTracking.enabled,
				dailyBudget: config.costTracking.dailyBudget,
				monthlyBudget: config.costTracking.monthlyBudget,
			},
			keyRotation: {
				enabled: config.keyRotation.enabled,
				interval: config.keyRotation.rotationInterval,
			},
		};

		this.logger.info(summary, 'AI configuration loaded');
	}
}

export default AIConfigLoader;
