// import { HttpsProxyAgent } from 'https-proxy-agent';
import type { Logger, LoggerInstanceType } from '@shared';
import HttpClient from '@shared/utils/HttpClient';
import type { ProxyConfigType } from './types';

type ProxyHealthType =
{
	url: string;
	healthy: boolean;
	latency?: number;
	lastChecked: Date;
	consecutiveFailures: number;
	error?: string;
};

type ProxyUsageType =
{
	url: string;
	requestsToday: number;
	successRate: number;
	averageLatency: number;
	lastUsed?: Date;
};

/**
 * Manages proxy rotation and health checking for AI requests
 */
class ProxyManager
{
	private logger: LoggerInstanceType;

	private config: ProxyConfigType;

	private currentProxyIndex: number = 0;

	private proxyHealth: Map<string, ProxyHealthType> = new Map();

	private proxyUsage: Map<string, ProxyUsageType> = new Map();

	private blacklistedProxies: Set<string> = new Set();

	private healthCheckInterval?: NodeJS.Timeout;

	constructor(config: ProxyConfigType, logger: Logger)
	{
		this.config = config;
		this.logger = logger.getLogger('ProxyManager');

		// Initialize proxy health and usage tracking
		this.initializeProxies();

		// Start health check interval if enabled
		if (config.enabled)
		{
			this.startHealthChecking();
		}
	}

	/**
	 * Get the next healthy proxy URL
	 */
	getNextProxy(): string | null
	{
		if (!this.config.enabled || this.config.urls.length === 0)
		{
			return null;
		}

		const healthyProxies = this.config.urls.filter(url => !this.blacklistedProxies.has(url) &&
			this.proxyHealth.get(url)?.healthy !== false);

		if (healthyProxies.length === 0)
		{
			this.logger.warn('No healthy proxies available');
			return null;
		}

		let selectedProxy: string;

		switch (this.config.rotation)
		{
			case 'round-robin':
				selectedProxy = healthyProxies[this.currentProxyIndex % healthyProxies.length];
				this.currentProxyIndex++;
				break;

			case 'random':
				selectedProxy = healthyProxies[Math.floor(Math.random() * healthyProxies.length)];
				break;

			case 'failover':
				// Always use the first healthy proxy
				selectedProxy = healthyProxies[0];
				break;

			default:
				selectedProxy = healthyProxies[0];
		}

		this.updateProxyUsage(selectedProxy);
		return selectedProxy;
	}

	/**
	 * Get HttpClient instance with proxy configuration
	 */
	getProxiedHttpClient(proxyUrl?: string): HttpClient
	{
		const proxy = proxyUrl || this.getNextProxy();

		if (!proxy)
		{
			// Return regular HttpClient instance if no proxy available
			return new HttpClient(this.logger, {
				timeout: this.config.timeout || 30000,
			});
		}

		return new HttpClient(this.logger, {
			timeout: this.config.timeout || 30000,
		});
	}

	/**
	 * Check health of all proxies
	 */
	async checkAllProxiesHealth(): Promise<void>
	{
		const healthChecks = this.config.urls.map(url => this.checkProxyHealth(url));
		await Promise.allSettled(healthChecks);
	}

	/**
	 * Get proxy health status
	 */
	getProxyHealth(): ProxyHealthType[]
	{
		return Array.from(this.proxyHealth.values());
	}

	/**
	 * Get proxy usage statistics
	 */
	getProxyUsage(): ProxyUsageType[]
	{
		return Array.from(this.proxyUsage.values());
	}

	/**
	 * Manually blacklist a proxy
	 */
	blacklistProxy(url: string, reason?: string): void
	{
		this.blacklistedProxies.add(url);
		const health = this.proxyHealth.get(url);
		if (health)
		{
			health.healthy = false;
			health.error = reason || 'Manually blacklisted';
		}

		this.logger.warn({ proxy: url, reason }, 'Proxy blacklisted');
	}

	/**
	 * Remove proxy from blacklist
	 */
	unblacklistProxy(url: string): void
	{
		this.blacklistedProxies.delete(url);
		this.logger.info({ proxy: url }, 'Proxy removed from blacklist');
	}

	/**
	 * Cleanup resources
	 */
	destroy(): void
	{
		if (this.healthCheckInterval)
		{
			clearInterval(this.healthCheckInterval);
		}
	}

	private initializeProxies(): void
	{
		for (const url of this.config.urls)
		{
			this.proxyHealth.set(url, {
				url,
				healthy: true,
				lastChecked: new Date(),
				consecutiveFailures: 0,
			});

			this.proxyUsage.set(url, {
				url,
				requestsToday: 0,
				successRate: 1.0,
				averageLatency: 0,
			});
		}
	}

	private startHealthChecking(): void
	{
		// Check proxy health every 5 minutes
		this.healthCheckInterval = setInterval(() =>
		{
			this.checkAllProxiesHealth().catch((error) =>
			{
				this.logger.error({ error: error.message }, 'Health check failed');
			});
		}, 5 * 60 * 1000);

		// Initial health check
		this.checkAllProxiesHealth().catch((error) =>
		{
			this.logger.error({ error: error.message }, 'Initial health check failed');
		});
	}

	private async checkProxyHealth(proxyUrl: string): Promise<void>
	{
		const startTime = Date.now();
		const health = this.proxyHealth.get(proxyUrl);

		if (!health)
		{
			return;
		}

		try
		{
			// Test proxy with a simple HTTP request
			const testClient = new HttpClient(this.logger, {
				timeout: this.config.timeout || 10000,
			});

			await testClient.get('https://httpbin.org/ip');

			const latency = Date.now() - startTime;

			health.healthy = true;
			health.latency = latency;
			health.lastChecked = new Date();
			health.consecutiveFailures = 0;
			health.error = undefined;

			// Remove from blacklist if it was there
			this.blacklistedProxies.delete(proxyUrl);

			this.logger.debug({ proxy: proxyUrl, latency }, 'Proxy health check passed');
		}
		catch (error)
		{
			health.healthy = false;
			health.lastChecked = new Date();
			health.consecutiveFailures++;
			health.error = error.message;

			// Blacklist after 3 consecutive failures
			if (health.consecutiveFailures >= 3)
			{
				this.blacklistedProxies.add(proxyUrl);
				this.logger.warn({
					proxy: proxyUrl,
					failures: health.consecutiveFailures,
				}, 'Proxy blacklisted due to consecutive failures');
			}

			this.logger.debug({
				proxy: proxyUrl,
				error: error.message,
				failures: health.consecutiveFailures,
			}, 'Proxy health check failed');
		}
	}

	private updateProxyUsage(proxyUrl: string): void
	{
		const usage = this.proxyUsage.get(proxyUrl);
		if (usage)
		{
			usage.requestsToday++;
			usage.lastUsed = new Date();
		}
	}

	private recordProxySuccess(proxyUrl: string, startTime?: number): void
	{
		const usage = this.proxyUsage.get(proxyUrl);
		if (!usage)
		{
			return;
		}

		// Update success rate (exponential moving average)
		const alpha = 0.1;
		usage.successRate = usage.successRate * (1 - alpha) + alpha;

		// Update average latency if start time is available
		if (startTime)
		{
			const latency = Date.now() - startTime;
			usage.averageLatency = usage.averageLatency * (1 - alpha) + latency * alpha;
		}
	}

	private handleProxyError(proxyUrl: string, error: any): void
	{
		const usage = this.proxyUsage.get(proxyUrl);
		if (usage)
		{
			// Update success rate (exponential moving average)
			const alpha = 0.1;
			usage.successRate *= (1 - alpha);
		}

		const health = this.proxyHealth.get(proxyUrl);
		if (health)
		{
			health.consecutiveFailures++;

			// Blacklist after multiple failures
			if (health.consecutiveFailures >= (this.config.maxRetries || 3))
			{
				this.blacklistedProxies.add(proxyUrl);
				health.healthy = false;

				this.logger.warn({
					proxy: proxyUrl,
					failures: health.consecutiveFailures,
					error: error.message,
				}, 'Proxy blacklisted due to errors');
			}
		}

		this.logger.debug({
			proxy: proxyUrl,
			error: error.message,
		}, 'Proxy request failed');
	}
}

export default ProxyManager;
