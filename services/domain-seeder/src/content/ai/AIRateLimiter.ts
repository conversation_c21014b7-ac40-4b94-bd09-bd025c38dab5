import { logger as sharedLogger } from '@shared';
import type { RedisClientWrapper } from '@shared';

type RateLimitConfig =
{
	provider: string;
	apiKey: string;
	requestsPerMinute: number;
	requestsPerHour?: number;
	requestsPerDay?: number;
	tokensPerMinute?: number;
	tokensPerHour?: number;
	tokensPerDay?: number;
	burstCapacity?: number;
};

type RateLimitResult =
{
	allowed: boolean;
	retryAfter?: number; // seconds
	remaining: {
		requests: number;
		tokens?: number;
	};
	resetTime: Date;
};

type RateLimitState =
{
	requests: {
		minute: number;
		hour: number;
		day: number;
	};
	tokens: {
		minute: number;
		hour: number;
		day: number;
	};
	lastReset: {
		minute: number;
		hour: number;
		day: number;
	};
};

/**
 * Advanced rate limiter for AI provider requests with multiple time windows
 */
class AIRateLimiter
{
	private logger: ReturnType<typeof sharedLogger.getLogger>;

	private redis: RedisClientWrapper;

	private configs: Map<string, RateLimitConfig> = new Map();

	constructor(redis: RedisClientWrapper, logger?: ReturnType<typeof sharedLogger.getLogger>)
	{
		this.redis = redis;
		this.logger = logger || sharedLogger.getLogger('AIRateLimiter');
	}

	/**
	 * Add rate limit configuration for a provider/key combination
	 */
	addConfig(config: RateLimitConfig): void
	{
		const key = this.getConfigKey(config.provider, config.apiKey);
		this.configs.set(key, config);

		this.logger.debug({
			provider: config.provider,
			requestsPerMinute: config.requestsPerMinute,
		}, 'Rate limit config added');
	}

	/**
	 * Check if a request is allowed under rate limits
	 */
	async checkRateLimit(
		provider: string,
		apiKey: string,
		tokensRequested: number = 0,
	): Promise<RateLimitResult>
	{
		const configKey = this.getConfigKey(provider, apiKey);
		const config = this.configs.get(configKey);

		if (!config)
		{
			// No rate limit configured, allow request
			return {
				allowed: true,
				remaining: { requests: Infinity },
				resetTime: new Date(Date.now() + 60000),
			};
		}

		const stateKey = this.getStateKey(provider, apiKey);
		const state = await this.getRateLimitState(stateKey);
		const now = Date.now();

		// Reset counters if time windows have passed
		this.resetExpiredWindows(state, now);

		// Check all rate limits
		const violations = this.checkViolations(config, state, tokensRequested);

		if (violations.length > 0)
		{
			const nextReset = Math.min(...violations.map(v => v.resetTime));
			const retryAfter = Math.ceil((nextReset - now) / 1000);

			this.logger.debug({
				provider,
				violations: violations.map(v => v.type),
				retryAfter,
			}, 'Rate limit exceeded');

			return {
				allowed: false,
				retryAfter,
				remaining: this.calculateRemaining(config, state),
				resetTime: new Date(nextReset),
			};
		}

		// Update counters
		state.requests.minute++;
		state.requests.hour++;
		state.requests.day++;

		if (tokensRequested > 0)
		{
			state.tokens.minute += tokensRequested;
			state.tokens.hour += tokensRequested;
			state.tokens.day += tokensRequested;
		}

		// Save updated state
		await this.saveRateLimitState(stateKey, state);

		return {
			allowed: true,
			remaining: this.calculateRemaining(config, state),
			resetTime: new Date(now + 60000), // Next minute reset
		};
	}

	/**
	 * Record actual token usage after request completion
	 */
	async recordUsage(
		provider: string,
		apiKey: string,
		tokensUsed: number,
	): Promise<void>
	{
		if (tokensUsed <= 0)
		{
			return;
		}

		const stateKey = this.getStateKey(provider, apiKey);
		const state = await this.getRateLimitState(stateKey);

		// Add actual token usage
		state.tokens.minute += tokensUsed;
		state.tokens.hour += tokensUsed;
		state.tokens.day += tokensUsed;

		await this.saveRateLimitState(stateKey, state);

		this.logger.debug({
			provider,
			tokensUsed,
		}, 'Token usage recorded');
	}

	/**
	 * Get current rate limit status for a provider/key
	 */
	async getRateLimitStatus(provider: string, apiKey: string): Promise<{
		config: RateLimitConfig | null;
		state: RateLimitState;
		remaining: { requests: number; tokens?: number };
	}>
	{
		const configKey = this.getConfigKey(provider, apiKey);
		const config = this.configs.get(configKey) || null;
		const stateKey = this.getStateKey(provider, apiKey);
		const state = await this.getRateLimitState(stateKey);

		// Reset expired windows
		this.resetExpiredWindows(state, Date.now());

		return {
			config,
			state,
			remaining: config ? this.calculateRemaining(config, state) : { requests: Infinity },
		};
	}

	/**
	 * Reset rate limits for a provider/key (useful for testing)
	 */
	async resetRateLimits(provider: string, apiKey: string): Promise<void>
	{
		const stateKey = this.getStateKey(provider, apiKey);
		await this.redis.del(stateKey);

		this.logger.info({ provider }, 'Rate limits reset');
	}

	private getConfigKey(provider: string, apiKey: string): string
	{
		// Use hash of API key for privacy
		const keyHash = Buffer.from(apiKey).toString('base64').substring(0, 8);
		return `${provider}:${keyHash}`;
	}

	private getStateKey(provider: string, apiKey: string): string
	{
		const keyHash = Buffer.from(apiKey).toString('base64').substring(0, 8);
		return `ai_rate_limit:${provider}:${keyHash}`;
	}

	private async getRateLimitState(stateKey: string): Promise<RateLimitState>
	{
		const stateData = await this.redis.get(stateKey);

		if (stateData && typeof stateData === 'string')
		{
			try
			{
				return JSON.parse(stateData);
			}
			catch (error)
			{
				this.logger.warn({ error: error.message }, 'Failed to parse rate limit state');
			}
		}

		// Return default state
		const now = Date.now();
		return {
			requests: { minute: 0, hour: 0, day: 0 },
			tokens: { minute: 0, hour: 0, day: 0 },
			lastReset: { minute: now, hour: now, day: now },
		};
	}

	private async saveRateLimitState(stateKey: string, state: RateLimitState): Promise<void>
	{
		// Set TTL to 25 hours to ensure cleanup
		await this.redis.setex(stateKey, 25 * 60 * 60, JSON.stringify(state));
	}

	private resetExpiredWindows(state: RateLimitState, now: number): void
	{
		// Reset minute window (every 60 seconds)
		if (now - state.lastReset.minute >= 60000)
		{
			state.requests.minute = 0;
			state.tokens.minute = 0;
			state.lastReset.minute = now;
		}

		// Reset hour window (every 3600 seconds)
		if (now - state.lastReset.hour >= 3600000)
		{
			state.requests.hour = 0;
			state.tokens.hour = 0;
			state.lastReset.hour = now;
		}

		// Reset day window (every 86400 seconds)
		if (now - state.lastReset.day >= 86400000)
		{
			state.requests.day = 0;
			state.tokens.day = 0;
			state.lastReset.day = now;
		}
	}

	private checkViolations(
		config: RateLimitConfig,
		state: RateLimitState,
		tokensRequested: number,
	): Array<{ type: string; resetTime: number }>
	{
		const violations: Array<{ type: string; resetTime: number }> = [];
		const now = Date.now();

		// Check request limits
		if (state.requests.minute >= config.requestsPerMinute)
		{
			violations.push({
				type: 'requests_per_minute',
				resetTime: state.lastReset.minute + 60000,
			});
		}

		if (config.requestsPerHour && state.requests.hour >= config.requestsPerHour)
		{
			violations.push({
				type: 'requests_per_hour',
				resetTime: state.lastReset.hour + 3600000,
			});
		}

		if (config.requestsPerDay && state.requests.day >= config.requestsPerDay)
		{
			violations.push({
				type: 'requests_per_day',
				resetTime: state.lastReset.day + 86400000,
			});
		}

		// Check token limits
		if (config.tokensPerMinute && (state.tokens.minute + tokensRequested) > config.tokensPerMinute)
		{
			violations.push({
				type: 'tokens_per_minute',
				resetTime: state.lastReset.minute + 60000,
			});
		}

		if (config.tokensPerHour && (state.tokens.hour + tokensRequested) > config.tokensPerHour)
		{
			violations.push({
				type: 'tokens_per_hour',
				resetTime: state.lastReset.hour + 3600000,
			});
		}

		if (config.tokensPerDay && (state.tokens.day + tokensRequested) > config.tokensPerDay)
		{
			violations.push({
				type: 'tokens_per_day',
				resetTime: state.lastReset.day + 86400000,
			});
		}

		return violations;
	}

	private calculateRemaining(config: RateLimitConfig, state: RateLimitState): {
		requests: number;
		tokens?: number;
	}
	{
		const remaining =
		{
			requests: Math.max(0, config.requestsPerMinute - state.requests.minute),
			tokens: config.tokensPerMinute
				? Math.max(0, config.tokensPerMinute - state.tokens.minute)
				: undefined,
		};

		// Take the minimum across all time windows
		if (config.requestsPerHour)
		{
			remaining.requests = Math.min(remaining.requests, config.requestsPerHour - state.requests.hour);
		}

		if (config.requestsPerDay)
		{
			remaining.requests = Math.min(remaining.requests, config.requestsPerDay - state.requests.day);
		}

		if (config.tokensPerHour && remaining.tokens !== undefined)
		{
			remaining.tokens = Math.min(remaining.tokens, config.tokensPerHour - state.tokens.hour);
		}

		if (config.tokensPerDay && remaining.tokens !== undefined)
		{
			remaining.tokens = Math.min(remaining.tokens, config.tokensPerDay - state.tokens.day);
		}

		return remaining;
	}
}

export type { RateLimitConfig, RateLimitResult };

export default AIRateLimiter;
