import type { LoggerInstanceType } from '@shared';
import * as cheerio from 'cheerio';
import HttpClient from '@shared/utils/HttpClient';
import type { CategoryClassificationResultType , TagExtractionResultType } from './CategoryTagger';
import type { SummaryGenerationResultType } from './DomainSummaryService';

type LiveContentAnalysisOptionsType =
{
	timeout?: number;
	maxContentLength?: number;
	userAgent?: string;
	followRedirects?: boolean;
	respectRobots?: boolean;
	minWords?: number;
	maxWords?: number;
};

type ContentExtractionResultType =
{
	title?: string;
	metaDescription?: string;
	ogTitle?: string;
	ogDescription?: string;
	headings: string[];
	mainContent: string;
	keyFeatures: string[];
	keywords: string[];
	language?: string;
	socialLinks: string[];
	hasRobotsTxt: boolean;
	hasSitemap: boolean;
	contentLength: number;
	extractionMethod: 'readability' | 'heuristic' | 'fallback';
};

type LiveAnalysisResultType =
{
	domain: string;
	content: ContentExtractionResultType;
	category: CategoryClassificationResultType;
	tags: TagExtractionResultType;
	summary: SummaryGenerationResultType;
	confidence: number;
	analysisMethod: 'live';
	generatedAt: string;
	processingTime: number;
};

type KeywordAnalysisResultType =
{
	keywords: string[];
	entities: string[];
	offerings: string[];
	audience: string[];
	geographic: string[];
	confidence: number;
};

/**
 * LiveContentAnalyzer performs comprehensive analysis of live website content
 * for scheduler integration, generating enhanced domain descriptions
 */
class LiveContentAnalyzer
{
	private logger: LoggerInstanceType;

	private options: Required<LiveContentAnalysisOptionsType>;

	private stopWords: Set<string>;

	private entityPatterns: Record<string, RegExp[]>;

	private offeringPatterns: RegExp[];

	private audiencePatterns: RegExp[];

	private geographicPatterns: RegExp[];

	constructor(
		logger: LoggerInstanceType,
		options: LiveContentAnalysisOptionsType = {},
	)
	{
		this.logger = logger;
		this.options = {
			timeout: options.timeout ?? 8000,
			maxContentLength: options.maxContentLength ?? 1048576, // 1MB
			userAgent: options.userAgent ?? 'Mozilla/5.0 (compatible; DomainSeeder/1.0; +https://example.com/bot)',
			followRedirects: options.followRedirects ?? true,
			respectRobots: options.respectRobots ?? true,
			minWords: options.minWords ?? 320,
			maxWords: options.maxWords ?? 500,
		};

		this.stopWords = this.initializeStopWords();
		this.entityPatterns = this.initializeEntityPatterns();
		this.offeringPatterns = this.initializeOfferingPatterns();
		this.audiencePatterns = this.initializeAudiencePatterns();
		this.geographicPatterns = this.initializeGeographicPatterns();
	}

	/**
	 * Perform comprehensive live content analysis for a domain
	 */
	async analyzeDomain(
		domain: string,
		categoryTagger: any,
		summaryService: any,
		options?: Partial<LiveContentAnalysisOptionsType>,
	): Promise<LiveAnalysisResultType>
	{
		const startTime = Date.now();
		const mergedOptions = { ...this.options, ...options };

		try
		{
			this.logger.info({ domain }, 'Starting live content analysis');

			// Step 1: Fetch and extract content
			const content = await this.extractContent(domain, mergedOptions);

			// Step 2: Perform keyword/NER analysis
			const keywordAnalysis = await this.analyzeKeywords(content);

			// Step 3: Classify category using live content
			const category = await categoryTagger.classifyCategory(
				domain,
				'live',
				content.mainContent,
			);

			// Step 4: Extract tags using live content
			const tags = await categoryTagger.extractTags(
				domain,
				category,
				'live',
				content.mainContent,
			);

			// Step 5: Generate enhanced summary using live content
			const summary = await summaryService.generateFromContent(
				domain,
				category,
				tags,
				{
					title: content.title,
					metaDescription: content.metaDescription,
					headings: content.headings,
					mainContent: content.mainContent,
					keyFeatures: content.keyFeatures,
					keywords: [...content.keywords, ...keywordAnalysis.keywords],
					language: content.language,
				},
				{
					minWords: mergedOptions.minWords,
					maxWords: mergedOptions.maxWords,
				},
			);

			const processingTime = Date.now() - startTime;
			const confidence = this.calculateLiveAnalysisConfidence(content, category, tags, summary);

			this.logger.info({
				domain,
				processingTime,
				confidence,
				contentLength: content.contentLength,
				extractionMethod: content.extractionMethod,
			}, 'Live content analysis completed');

			return {
				domain,
				content,
				category,
				tags,
				summary,
				confidence,
				analysisMethod: 'live',
				generatedAt: new Date().toISOString(),
				processingTime,
			};
		}
		catch (error)
		{
			const processingTime = Date.now() - startTime;
			this.logger.error({ domain, error, processingTime }, 'Live content analysis failed');
			throw error;
		}
	}

	/**
	 * Extract content from domain homepage with readability-based extraction
	 */
	async extractContent(
		domain: string,
		options: Required<LiveContentAnalysisOptionsType>,
	): Promise<ContentExtractionResultType>
	{
		try
		{
			// Check robots.txt if required
			let hasRobotsTxt = false;
			if (options.respectRobots)
			{
				hasRobotsTxt = await this.checkRobotsTxt(domain);
				if (hasRobotsTxt && !(await this.isAllowedByRobots(domain)))
				{
					throw new Error('Crawling disallowed by robots.txt');
				}
			}

			// Fetch homepage content
			const url = `https://${domain}`;
			const client = new HttpClient(this.logger, {
				timeout: options.timeout,
			});
			const response = await client.get<string>(url, {
				headers: {
					'User-Agent': options.userAgent,
					Accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
					'Accept-Language': 'en-US,en;q=0.5',
					'Accept-Encoding': 'gzip, deflate',
					Connection: 'keep-alive',
				},
			});

			const html = response.data;
			const $ = cheerio.load(html);

			// Extract basic metadata
			const title = this.extractTitle($);
			const metaDescription = this.extractMetaDescription($);
			const ogTitle = this.extractOgTitle($);
			const ogDescription = this.extractOgDescription($);
			const language = this.detectLanguage($, domain);

			// Extract headings
			const headings = this.extractHeadings($);

			// Extract social links before removing elements
			const socialLinks = this.extractSocialLinks($);

			// Extract main content using readability-based approach
			const mainContent = this.extractMainContent($);

			// Extract key features from content
			const keyFeatures = this.extractKeyFeatures($, mainContent);

			// Extract keywords from content
			const keywords = this.extractKeywords(mainContent, title, metaDescription);


			// Check for sitemap
			const hasSitemap = await this.checkSitemap(domain);

			const result: ContentExtractionResultType = {
				title,
				metaDescription,
				ogTitle,
				ogDescription,
				headings,
				mainContent,
				keyFeatures,
				keywords,
				language,
				socialLinks,
				hasRobotsTxt,
				hasSitemap,
				contentLength: mainContent.length,
				extractionMethod: mainContent.length > 500 ? 'readability' : 'heuristic',
			};

			this.logger.info({
				domain,
				contentLength: result.contentLength,
				extractionMethod: result.extractionMethod,
				headingCount: headings.length,
				keywordCount: keywords.length,
			}, 'Content extraction completed');

			return result;
		}
		catch (error)
		{
			this.logger.error({ domain, error }, 'Content extraction failed');
			throw error;
		}
	}

	/**
	 * Perform keyword and NER analysis on extracted content
	 */
	async analyzeKeywords(content: ContentExtractionResultType): Promise<KeywordAnalysisResultType>
	{
		try
		{
			const text = [
				content.title || '',
				content.metaDescription || '',
				content.mainContent,
				...content.headings,
			].join(' ');

			// Extract keywords using TF-IDF approach
			const keywords = this.extractKeywordsWithTfIdf(text);

			// Extract entities using pattern matching (simplified NER)
			const entities = this.extractEntities(text);

			// Extract offerings/services
			const offerings = this.extractOfferings(text);

			// Extract audience indicators
			const audience = this.extractAudience(text);

			// Extract geographic indicators
			const geographic = this.extractGeographic(text);

			const confidence = this.calculateKeywordAnalysisConfidence(
				keywords,
				entities,
				offerings,
				audience,
				geographic,
			);

			this.logger.info({
				keywordCount: keywords.length,
				entityCount: entities.length,
				offeringCount: offerings.length,
				audienceCount: audience.length,
				geographicCount: geographic.length,
				confidence,
			}, 'Keyword analysis completed');

			return {
				keywords,
				entities,
				offerings,
				audience,
				geographic,
				confidence,
			};
		}
		catch (error)
		{
			this.logger.error({ error }, 'Keyword analysis failed');
			return {
				keywords: [],
				entities: [],
				offerings: [],
				audience: [],
				geographic: [],
				confidence: 0.1,
			};
		}
	}

	private extractTitle($: cheerio.CheerioAPI): string | undefined
	{
		const title = $('title').first().text().trim();
		return title.length > 0 ? title : undefined;
	}

	private extractMetaDescription($: cheerio.CheerioAPI): string | undefined
	{
		const metaDesc = $('meta[name="description"]').attr('content')?.trim();
		return metaDesc && metaDesc.length > 0 ? metaDesc : undefined;
	}

	private extractOgTitle($: cheerio.CheerioAPI): string | undefined
	{
		const ogTitle = $('meta[property="og:title"]').attr('content')?.trim();
		return ogTitle && ogTitle.length > 0 ? ogTitle : undefined;
	}

	private extractOgDescription($: cheerio.CheerioAPI): string | undefined
	{
		const ogDesc = $('meta[property="og:description"]').attr('content')?.trim();
		return ogDesc && ogDesc.length > 0 ? ogDesc : undefined;
	}

	private detectLanguage($: cheerio.CheerioAPI, domain: string): string | undefined
	{
		// Check HTML lang attribute
		const htmlLang = $('html').attr('lang');
		if (htmlLang)
		{
			return htmlLang.split('-')[0].toLowerCase();
		}

		// Check meta language
		const metaLang = $('meta[http-equiv="content-language"]').attr('content');
		if (metaLang)
		{
			return metaLang.split('-')[0].toLowerCase();
		}

		// Infer from TLD
		const tld = domain.split('.').pop()?.toLowerCase();
		const tldLanguageMap: Record<string, string> = {
			de: 'de',
			fr: 'fr',
			es: 'es',
			it: 'it',
			nl: 'nl',
			jp: 'ja',
			cn: 'zh',
			kr: 'ko',
			ru: 'ru',
			pt: 'pt',
		};

		return tld ? tldLanguageMap[tld] : 'en';
	}

	private extractHeadings($: cheerio.CheerioAPI): string[]
	{
		const headings: string[] = [];

		$('h1, h2, h3, h4, h5, h6').each((_, element) =>
		{
			const text = $(element).text().trim();
			if (text.length > 0 && text.length < 200)
			{
				headings.push(text);
			}
		});

		return headings.slice(0, 20); // Limit to 20 headings
	}

	private extractMainContent($: cheerio.CheerioAPI): string
	{
		// Remove unwanted elements
		$('script, style, nav, header, footer, aside, .sidebar, .menu, .navigation, .ads, .advertisement').remove();

		// Try to find main content using common selectors
		const mainSelectors = [
			'main',
			'[role="main"]',
			'.main-content',
			'.content',
			'.post-content',
			'.entry-content',
			'.article-content',
			'article',
			'.container .row .col',
		];

		let mainContent = '';

		for (const selector of mainSelectors)
		{
			const element = $(selector).first();
			if (element.length > 0)
			{
				const text = element.text().trim();
				if (text.length > mainContent.length)
				{
					mainContent = text;
				}
			}
		}

		// Fallback: extract from body if no main content found
		if (mainContent.length < 200)
		{
			mainContent = $('body').text().trim();
		}

		// Clean up the content
		mainContent = this.cleanText(mainContent);

		// Apply readability-based filtering
		return this.applyReadabilityFilter(mainContent);
	}

	private extractKeyFeatures($: cheerio.CheerioAPI, mainContent: string): string[]
	{
		const features: string[] = [];

		// Extract from lists
		$('ul li, ol li').each((_, element) =>
		{
			const text = $(element).text().trim();
			if (text.length > 10 && text.length < 100)
			{
				features.push(text);
			}
		});

		// Extract from feature-related sections
		$('.features li, .benefits li, .services li, .capabilities li').each((_, element) =>
		{
			const text = $(element).text().trim();
			if (text.length > 10 && text.length < 100)
			{
				features.push(text);
			}
		});

		// Extract from content using patterns
		const featurePatterns = [
			/(?:features?|benefits?|capabilities?|services?|offerings?)[:\s]*([^.!?]+)/gi,
			/(?:we offer|we provide|includes?)[:\s]*([^.!?]+)/gi,
		];

		for (const pattern of featurePatterns)
		{
			const matches = mainContent.match(pattern);
			if (matches)
			{
				for (const match of matches)
				{
					const feature = match.replace(pattern, '$1').trim();
					if (feature.length > 10 && feature.length < 100)
					{
						features.push(feature);
					}
				}
			}
		}

		return Array.from(new Set(features)).slice(0, 10); // Remove duplicates and limit
	}

	private extractKeywords(mainContent: string, title?: string, metaDescription?: string): string[]
	{
		const text = [title || '', metaDescription || '', mainContent].join(' ');
		return this.extractKeywordsWithTfIdf(text);
	}

	private extractSocialLinks($: cheerio.CheerioAPI): string[]
	{
		const socialLinks: string[] = [];
		const socialDomains = ['twitter.com', 'facebook.com', 'linkedin.com', 'instagram.com', 'youtube.com'];

		$('a[href]').each((_, element) =>
		{
			const href = $(element).attr('href');
			if (href)
			{
				// Handle both absolute and relative URLs
				let fullUrl = href;
				if (href.startsWith('//'))
				{
					fullUrl = `https:${href}`;
				}
				else if (href.startsWith('/'))
				{
					// Skip relative URLs for social links
					return;
				}

				for (const domain of socialDomains)
				{
					if (fullUrl.includes(domain))
					{
						socialLinks.push(fullUrl);
						break;
					}
				}
			}
		});

		return Array.from(new Set(socialLinks)); // Remove duplicates
	}

	private async checkRobotsTxt(domain: string): Promise<boolean>
	{
		try
		{
			const client = new HttpClient(this.logger, {
				timeout: 3000,
			});
			const response = await client.get(`https://${domain}/robots.txt`);
			return response.statusCode === 200;
		}
		catch
		{
			return false;
		}
	}

	private async isAllowedByRobots(domain: string): Promise<boolean>
	{
		try
		{
			const client = new HttpClient(this.logger, {
				timeout: 3000,
			});
			const response = await client.get<string>(`https://${domain}/robots.txt`);

			const robotsTxt = response.data.toLowerCase();
			const userAgentSections = robotsTxt.split('user-agent:');

			for (const section of userAgentSections)
			{
				if (section.includes('*') || section.includes('domainseeder'))
				{
					if (section.includes('disallow: /'))
					{
						return false;
					}
				}
			}

			return true;
		}
		catch
		{
			return true; // Allow if robots.txt is not accessible
		}
	}

	private async checkSitemap(domain: string): Promise<boolean>
	{
		try
		{
			const sitemapUrls = [
				`https://${domain}/sitemap.xml`,
				`https://${domain}/sitemap_index.xml`,
				`https://${domain}/sitemaps.xml`,
			];

			for (const url of sitemapUrls)
			{
				try
				{
					const client = new HttpClient(this.logger, {
						timeout: 3000,
					});
					const response = await client.head(url);
					if (response.statusCode === 200)
					{
						return true;
					}
				}
				catch
				{
					continue;
				}
			}

			return false;
		}
		catch
		{
			return false;
		}
	}

	private cleanText(text: string): string
	{
		return text
			.replace(/\s+/g, ' ')
			.replace(/\n+/g, ' ')
			.replace(/\t+/g, ' ')
			.trim();
	}

	private applyReadabilityFilter(content: string): string
	{
		// If content is too short, return as-is
		if (content.length < 50)
		{
			return content;
		}

		// Split into sentences
		const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);

		// Filter sentences by readability criteria
		const filteredSentences = sentences.filter((sentence) =>
		{
			const words = sentence.trim().split(/\s+/);
			return words.length >= 5 && words.length <= 30; // Reasonable sentence length
		});

		// If no sentences pass the filter, return original content
		if (filteredSentences.length === 0)
		{
			return content;
		}

		// Return filtered content
		return `${filteredSentences.join('. ').trim() }.`;
	}

	private extractKeywordsWithTfIdf(text: string): string[]
	{
		// Simple TF-IDF implementation
		const words = text
			.toLowerCase()
			.replace(/[^\w\s]/g, ' ')
			.split(/\s+/)
			.filter(word => word.length > 3)
			.filter(word => !this.stopWords.has(word));

		// Calculate term frequency
		const termFreq: Record<string, number> = {};
		for (const word of words)
		{
			termFreq[word] = (termFreq[word] || 0) + 1;
		}

		// Sort by frequency and return top keywords
		return Object.entries(termFreq)
			.sort(([, a], [, b]) => b - a)
			.slice(0, 20)
			.map(([word]) => word);
	}

	private extractEntities(text: string): string[]
	{
		const entities: string[] = [];

		for (const [entityType, patterns] of Object.entries(this.entityPatterns))
		{
			for (const pattern of patterns)
			{
				const matches = text.match(pattern);
				if (matches)
				{
					entities.push(...matches.map(match => match.trim()));
				}
			}
		}

		return Array.from(new Set(entities)).slice(0, 10);
	}

	private extractOfferings(text: string): string[]
	{
		const offerings: string[] = [];

		for (const pattern of this.offeringPatterns)
		{
			const matches = text.match(pattern);
			if (matches)
			{
				offerings.push(...matches.map(match => match.trim()));
			}
		}

		return Array.from(new Set(offerings)).slice(0, 8);
	}

	private extractAudience(text: string): string[]
	{
		const audience: string[] = [];

		for (const pattern of this.audiencePatterns)
		{
			const matches = text.match(pattern);
			if (matches)
			{
				audience.push(...matches.map(match => match.trim()));
			}
		}

		return Array.from(new Set(audience)).slice(0, 5);
	}

	private extractGeographic(text: string): string[]
	{
		const geographic: string[] = [];

		for (const pattern of this.geographicPatterns)
		{
			const matches = text.match(pattern);
			if (matches)
			{
				geographic.push(...matches.map(match => match.trim()));
			}
		}

		return Array.from(new Set(geographic)).slice(0, 5);
	}

	private calculateLiveAnalysisConfidence(
		content: ContentExtractionResultType,
		category: CategoryClassificationResultType,
		tags: TagExtractionResultType,
		summary: SummaryGenerationResultType,
	): number
	{
		let confidence = 0.6; // Base confidence for live analysis

		// Content quality boost
		if (content.title) confidence += 0.05;
		if (content.metaDescription) confidence += 0.05;
		if (content.mainContent.length > 1000) confidence += 0.1;
		if (content.headings.length > 3) confidence += 0.05;
		if (content.keyFeatures.length > 0) confidence += 0.05;

		// Category confidence boost
		confidence += category.confidence * 0.1;

		// Tag quality boost
		confidence += tags.confidence * 0.05;

		// Summary quality boost
		confidence += summary.confidence * 0.05;

		return Math.min(confidence, 1.0);
	}

	private calculateKeywordAnalysisConfidence(
		keywords: string[],
		entities: string[],
		offerings: string[],
		audience: string[],
		geographic: string[],
	): number
	{
		let confidence = 0.3;

		if (keywords.length > 5) confidence += 0.2;
		if (entities.length > 0) confidence += 0.15;
		if (offerings.length > 0) confidence += 0.15;
		if (audience.length > 0) confidence += 0.1;
		if (geographic.length > 0) confidence += 0.1;

		return Math.min(confidence, 1.0);
	}

	private initializeStopWords(): Set<string>
	{
		return new Set([
			'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
			'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did',
			'will', 'would', 'could', 'should', 'may', 'might', 'can', 'must',
			'this', 'that', 'these', 'those', 'a', 'an', 'as', 'if', 'then', 'than',
			'about', 'into', 'through', 'during', 'before', 'after', 'above', 'below',
			'up', 'down', 'out', 'off', 'over', 'under', 'again', 'further',
			'once', 'here', 'there', 'when', 'where', 'why', 'how', 'all', 'any',
			'both', 'each', 'few', 'more', 'most', 'other', 'some', 'such',
		]);
	}

	private initializeEntityPatterns(): Record<string, RegExp[]>
	{
		return {
			organizations: [
				/\b[A-Z][a-z]+ (?:Inc|Corp|LLC|Ltd|Company|Group|Systems|Solutions|Technologies)\b/g,
				/\b(?:Microsoft|Google|Apple|Amazon|Facebook|Twitter|LinkedIn)\b/gi,
			],
			technologies: [
				/\b(?:API|SDK|SaaS|AI|ML|IoT|VR|AR|blockchain|cloud|database)\b/gi,
				/\b(?:React|Angular|Vue|Node\.js|Python|Java|JavaScript|TypeScript)\b/gi,
			],
			products: [
				/\b[A-Z][a-z]+ (?:Platform|Service|Tool|App|Software|System)\b/g,
			],
		};
	}

	private initializeOfferingPatterns(): RegExp[]
	{
		return [
			/(?:we offer|we provide|our services include|services offered)[:\s]*([^.!?]+)/gi,
			/(?:solutions for|specializing in|expertise in)[:\s]*([^.!?]+)/gi,
			/(?:features include|capabilities include)[:\s]*([^.!?]+)/gi,
		];
	}

	private initializeAudiencePatterns(): RegExp[]
	{
		return [
			/(?:for|designed for|targeted at|serving)[:\s]*([^.!?]*(?:businesses|companies|enterprises|startups|developers|professionals|individuals|students|researchers))/gi,
			/(?:small business|enterprise|startup|developer|professional|individual|student|researcher)/gi,
		];
	}

	private initializeGeographicPatterns(): RegExp[]
	{
		return [
			/\b(?:United States|USA|US|Canada|UK|United Kingdom|Germany|France|Japan|Australia|Brazil|India|China)\b/gi,
			/\b(?:New York|London|San Francisco|Los Angeles|Chicago|Boston|Seattle|Toronto|Berlin|Paris|Tokyo|Sydney)\b/gi,
			/\b(?:global|worldwide|international|local|regional|national)\b/gi,
		];
	}
}

export type {
	LiveContentAnalysisOptionsType as LiveContentAnalysisOptions,
	ContentExtractionResultType as ContentExtractionResult,
	LiveAnalysisResultType as LiveAnalysisResult,
	KeywordAnalysisResultType as KeywordAnalysisResult,
};

export default LiveContentAnalyzer;
