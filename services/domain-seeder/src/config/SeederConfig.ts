import type { LoggerInstanceType } from '@shared';
import { Config as SharedConfig } from '@shared';
import { z } from 'zod';
import * as fs from 'fs';
// import * as path from 'path';
// import * as crypto from 'crypto';

// Configuration schema validation using Zod
const DatabaseConfigSchema = z.object({
	scylla: z.object({
		contactPoints: z.array(z.string()).min(1),
		localDataCenter: z.string().min(1),
		keyspace: z.string().min(1),
		username: z.string().optional(),
		password: z.string().optional(),
		connectTimeout: z.number().positive().default(30000),
		requestTimeout: z.number().positive().default(12000),
		poolConfig: z.object({
			maxConnections: z.number().int().positive().default(100),
			minConnections: z.number().int().positive().default(10),
			maxWaitTime: z.number().positive().default(30000),
			idleTimeout: z.number().positive().default(600000),
		}).optional(),
	}),
	maria: z.object({
		host: z.string().min(1),
		port: z.number().int().min(1).max(65535),
		user: z.string().min(1),
		password: z.string(),
		database: z.string().min(1),
		connectionLimit: z.number().int().positive().default(10),
		acquireTimeout: z.number().positive().default(60000),
		timeout: z.number().positive().default(60000),
	}),
	manticore: z.object({
		host: z.string().min(1),
		port: z.number().int().min(1).max(65535),
		maxConnections: z.number().int().positive().default(10),
		timeout: z.number().positive().default(30000),
	}),
	redis: z.object({
		host: z.string().min(1),
		port: z.number().int().min(1).max(65535),
		password: z.string().optional(),
		db: z.number().int().min(0).max(15)
			.default(0),
		keyPrefix: z.string().optional().default('p4:'),
		maxRetriesPerRequest: z.number().int().positive().default(3),
		retryDelayOnFailover: z.number().positive().default(100),
		connectTimeout: z.number().positive().default(10000),
		commandTimeout: z.number().positive().default(5000),
		poolConfig: z.object({
			maxConnections: z.number().int().positive().default(50),
			minConnections: z.number().int().positive().default(5),
			acquireTimeoutMs: z.number().positive().default(10000),
			idleTimeoutMs: z.number().positive().default(300000),
			maxLifetimeMs: z.number().positive().default(1800000),
		}).optional(),
	}),
});

const SeederConfigSchema = z.object({
	maxNewPerDay: z.number().int().positive().default(500000),
	enqueueBatch: z.number().int().positive().default(1000),
	enqueueIntervalMs: z.number().int().positive().default(1000),
	newQueueMaxDepth: z.number().int().positive().default(200000),
	dbCheckBatch: z.number().int().positive().default(5000),
	bloomFpRate: z.number().min(0.001).max(0.1).default(0.01),
	pslUpdateIntervalDays: z.number().int().positive().default(7),
	discoveryTimeoutMs: z.number().int().positive().default(300000), // 5 minutes
	maxConcurrentSources: z.number().int().positive().default(3),
	rateLimitWindowMs: z.number().int().positive().default(60000), // 1 minute
	rateLimitMaxRequests: z.number().int().positive().default(1000),
	contentGenerationEnabled: z.boolean().default(true),
	contentGenerationMode: z.enum(['preGenerated', 'live', 'both']).default('preGenerated'),
});

const SourceConfigSchema = z.object({
	tranco: z.object({
		enabled: z.boolean().default(true),
		priority: z.number().int().positive().default(1),
		timeout: z.number().positive().default(30000),
		maxRetries: z.number().int().min(0).default(3),
		rateLimit: z.number().int().positive().default(60), // requests per minute
	}),
	radar: z.object({
		enabled: z.boolean().default(true),
		apiKey: z.string().default(''),
		priority: z.number().int().positive().default(2),
		timeout: z.number().positive().default(30000),
		maxRetries: z.number().int().min(0).default(3),
		rateLimit: z.number().int().positive().default(300), // requests per minute
	}),
	umbrella: z.object({
		enabled: z.boolean().default(true),
		apiKey: z.string().default(''),
		priority: z.number().int().positive().default(3),
		timeout: z.number().positive().default(30000),
		maxRetries: z.number().int().min(0).default(3),
		rateLimit: z.number().int().positive().default(100),
	}),
	czds: z.object({
		enabled: z.boolean().default(true),
		username: z.string().default(''),
		password: z.string().default(''),
		priority: z.number().int().positive().default(4),
		timeout: z.number().positive().default(60000),
		maxRetries: z.number().int().min(0).default(3),
		downloadPath: z.string().default('/tmp/czds'),
	}),
	pir: z.object({
		enabled: z.boolean().default(true),
		apiKey: z.string().default(''),
		priority: z.number().int().positive().default(5),
		timeout: z.number().positive().default(30000),
		maxRetries: z.number().int().min(0).default(3),
		rateLimit: z.number().int().positive().default(60),
	}),
	commonCrawl: z.object({
		enabled: z.boolean().default(true),
		priority: z.number().int().positive().default(6),
		timeout: z.number().positive().default(60000),
		maxRetries: z.number().int().min(0).default(3),
		indexUrl: z.string().url().default('https://index.commoncrawl.org/'),
		batchSize: z.number().int().positive().default(10000),
	}),
	sonar: z.object({
		enabled: z.boolean().default(true),
		apiKey: z.string().default(''),
		priority: z.number().int().positive().default(7),
		timeout: z.number().positive().default(30000),
		maxRetries: z.number().int().min(0).default(3),
		rateLimit: z.number().int().positive().default(100),
	}),
});

const AIConfigSchema = z.object({
	providers: z.object({
		openai: z.object({
			enabled: z.boolean().default(false),
			apiKeys: z.array(z.string().min(1)).default([]),
			priority: z.number().int().positive().default(1),
			model: z.string().default('gpt-4'),
			maxTokens: z.number().int().positive().default(4000),
			temperature: z.number().min(0).max(2).default(0.7),
			rateLimit: z.number().int().positive().default(60), // requests per minute
		}),
		claude: z.object({
			enabled: z.boolean().default(false),
			apiKeys: z.array(z.string().min(1)).default([]),
			priority: z.number().int().positive().default(2),
			model: z.string().default('claude-3-sonnet-20240229'),
			maxTokens: z.number().int().positive().default(4000),
			temperature: z.number().min(0).max(1).default(0.7),
			rateLimit: z.number().int().positive().default(50),
		}),
		gemini: z.object({
			enabled: z.boolean().default(false),
			apiKeys: z.array(z.string().min(1)).default([]),
			priority: z.number().int().positive().default(3),
			model: z.string().default('gemini-pro'),
			maxTokens: z.number().int().positive().default(4000),
			temperature: z.number().min(0).max(1).default(0.7),
			rateLimit: z.number().int().positive().default(60),
		}),
		openrouter: z.object({
			enabled: z.boolean().default(false),
			apiKeys: z.array(z.string().min(1)).default([]),
			priority: z.number().int().positive().default(4),
			model: z.string().default('anthropic/claude-3-sonnet'),
			maxTokens: z.number().int().positive().default(4000),
			temperature: z.number().min(0).max(1).default(0.7),
			rateLimit: z.number().int().positive().default(100),
		}),
	}),
	proxy: z.object({
		enabled: z.boolean().default(false),
		ips: z.array(z.string()).default([]),
		rotationIntervalMs: z.number().int().positive().default(300000), // 5 minutes
		healthCheckIntervalMs: z.number().int().positive().default(60000), // 1 minute
	}),
	fallbackEnabled: z.boolean().default(true),
	maxRetries: z.number().int().min(0).default(3),
	timeoutMs: z.number().int().positive().default(30000),
});

const MonitoringConfigSchema = z.object({
	metricsEnabled: z.boolean().default(true),
	metricsPort: z.number().int().min(1).max(65535)
		.default(9090),
	healthCheckEnabled: z.boolean().default(true),
	healthCheckPort: z.number().int().min(1).max(65535)
		.default(8080),
	alertingEnabled: z.boolean().default(true),
	logLevel: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
	logFormat: z.enum(['json', 'text']).default('json'),
	logDir: z.string().default('./logs'),
	maxLogFiles: z.number().int().positive().default(10),
	maxLogSize: z.string().default('100MB'),
});

const SecurityConfigSchema = z.object({
	encryptionKey: z.string().min(32).optional(),
	credentialRotationIntervalDays: z.number().int().positive().default(90),
	credentialExpiryWarningDays: z.number().int().positive().default(7),
	secretsPath: z.string().default('./secrets'),
	tlsEnabled: z.boolean().default(false),
	tlsCertPath: z.string().optional(),
	tlsKeyPath: z.string().optional(),
});

const EnvironmentConfigSchema = z.object({
	nodeEnv: z.enum(['development', 'staging', 'production', 'test']).default('development'),
	serviceName: z.string().default('domain-seeder'),
	serviceVersion: z.string().default('1.0.0'),
	port: z.number().int().min(1).max(65535)
		.default(3000),
	timezone: z.string().default('UTC'),
	gracefulShutdownTimeoutMs: z.number().int().positive().default(30000),
});

// Main configuration schema
const SeederConfigurationSchema = z.object({
	environment: EnvironmentConfigSchema,
	database: DatabaseConfigSchema,
	seeder: SeederConfigSchema,
	sources: SourceConfigSchema,
	ai: AIConfigSchema,
	monitoring: MonitoringConfigSchema,
	security: SecurityConfigSchema,
});

type SeederConfiguration = z.infer<typeof SeederConfigurationSchema>;

type ConfigValidationResult =
{
	isValid: boolean;
	errors: string[];
	warnings: string[];
	config?: SeederConfiguration;
};

type ConfigHotReloadOptions =
{
	enabled: boolean;
	watchPaths: string[];
	debounceMs: number;
	callback?: (newConfig: SeederConfiguration) => void;
};

class SeederConfig extends SharedConfig
{
	private static instance: SeederConfig;

	private configuration: SeederConfiguration;

	private validationErrors: string[] = [];

	private validationWarnings: string[] = [];

	private hotReloadOptions?: ConfigHotReloadOptions;

	private watchers: fs.FSWatcher[] = [];

	private logger?: LoggerInstanceType;

	private constructor()
	{
		super();
		this.configuration = this.loadAndValidateConfiguration();
	}

	static getInstance(): SeederConfig
	{
		if (!SeederConfig.instance)
		{
			SeederConfig.instance = new SeederConfig();
		}
		return SeederConfig.instance;
	}

	setLogger(logger: LoggerInstanceType): void
	{
		this.logger = logger;
	}

	private loadAndValidateConfiguration(): SeederConfiguration
	{
		const rawConfig = this.buildRawConfiguration();
		const validationResult = this.validateConfiguration(rawConfig);

		this.validationErrors = validationResult.errors;
		this.validationWarnings = validationResult.warnings;

		if (!validationResult.isValid)
		{
			const errorMessage = `Configuration validation failed:\n${validationResult.errors.join('\n')}`;
			throw new Error(errorMessage);
		}

		if (validationResult.warnings.length > 0 && this.logger)
		{
			this.logger.warn({
				warnings: validationResult.warnings,
			}, 'Configuration validation warnings:');
		}

		return validationResult.config!;
	}

	private buildRawConfiguration(): any
	{
		return {
			environment: {
				nodeEnv: process.env.NODE_ENV || 'development',
				serviceName: process.env.SERVICE_NAME || 'domain-seeder',
				serviceVersion: process.env.SERVICE_VERSION || '1.0.0',
				port: this.parseNumber(process.env.PORT, 3000),
				timezone: process.env.TIMEZONE || 'UTC',
				gracefulShutdownTimeoutMs: this.parseNumber(process.env.GRACEFUL_SHUTDOWN_TIMEOUT_MS, 30000),
			},
			database: {
				scylla: {
					contactPoints: this.parseArray(process.env.SCYLLA_CONTACT_POINTS, ['127.0.0.1']),
					localDataCenter: process.env.SCYLLA_LOCAL_DC || 'datacenter1',
					keyspace: process.env.SCYLLA_KEYSPACE || 'domain_ranking',
					username: process.env.SCYLLA_USERNAME,
					password: process.env.SCYLLA_PASSWORD,
					connectTimeout: this.parseNumber(process.env.SCYLLA_CONNECT_TIMEOUT, 30000),
					requestTimeout: this.parseNumber(process.env.SCYLLA_REQUEST_TIMEOUT, 12000),
				},
				maria: {
					host: process.env.MARIA_HOST || 'localhost',
					port: this.parseNumber(process.env.MARIA_PORT, 3306),
					user: process.env.MARIA_USER || 'root',
					password: process.env.MARIA_PASSWORD || '',
					database: process.env.MARIA_DATABASE || 'domain_ranking',
					connectionLimit: this.parseNumber(process.env.MARIA_CONNECTION_LIMIT, 10),
					acquireTimeout: this.parseNumber(process.env.MARIA_ACQUIRE_TIMEOUT, 60000),
					timeout: this.parseNumber(process.env.MARIA_TIMEOUT, 60000),
				},
				manticore: {
					host: process.env.MANTICORE_HOST || 'localhost',
					port: this.parseNumber(process.env.MANTICORE_PORT, 9308),
					maxConnections: this.parseNumber(process.env.MANTICORE_MAX_CONNECTIONS, 10),
					timeout: this.parseNumber(process.env.MANTICORE_TIMEOUT, 30000),
				},
				redis: {
					host: process.env.REDIS_HOST || 'localhost',
					port: this.parseNumber(process.env.REDIS_PORT, 6379),
					password: process.env.REDIS_PASSWORD,
					db: this.parseNumber(process.env.REDIS_DB, 0),
					keyPrefix: 'p4:',
					maxRetriesPerRequest: this.parseNumber(process.env.REDIS_MAX_RETRIES, 3),
					retryDelayOnFailover: this.parseNumber(process.env.REDIS_RETRY_DELAY, 100),
					connectTimeout: this.parseNumber(process.env.REDIS_CONNECT_TIMEOUT, 10000),
					commandTimeout: this.parseNumber(process.env.REDIS_COMMAND_TIMEOUT, 5000),
				},
			},
			seeder: {
				maxNewPerDay: this.parseNumber(process.env.MAX_NEW_PER_DAY, 500000),
				enqueueBatch: this.parseNumber(process.env.ENQUEUE_BATCH, 1000),
				enqueueIntervalMs: this.parseNumber(process.env.ENQUEUE_INTERVAL_MS, 1000),
				newQueueMaxDepth: this.parseNumber(process.env.NEW_QUEUE_MAX_DEPTH, 200000),
				dbCheckBatch: this.parseNumber(process.env.DB_CHECK_BATCH, 5000),
				bloomFpRate: this.parseFloat(process.env.BLOOM_FP_RATE, 0.01),
				pslUpdateIntervalDays: this.parseNumber(process.env.PSL_UPDATE_INTERVAL_DAYS, 7),
				discoveryTimeoutMs: this.parseNumber(process.env.DISCOVERY_TIMEOUT_MS, 300000),
				maxConcurrentSources: this.parseNumber(process.env.MAX_CONCURRENT_SOURCES, 3),
				rateLimitWindowMs: this.parseNumber(process.env.RATE_LIMIT_WINDOW_MS, 60000),
				rateLimitMaxRequests: this.parseNumber(process.env.RATE_LIMIT_MAX_REQUESTS, 1000),
				contentGenerationEnabled: this.parseBoolean(process.env.CONTENT_GENERATION_ENABLED, true),
				contentGenerationMode: process.env.CONTENT_GENERATION_MODE || 'preGenerated',
			},
			sources: this.buildSourcesConfiguration(),
			ai: this.buildAIConfiguration(),
			monitoring: {
				metricsEnabled: this.parseBoolean(process.env.METRICS_ENABLED, true),
				metricsPort: this.parseNumber(process.env.METRICS_PORT, 9090),
				healthCheckEnabled: this.parseBoolean(process.env.HEALTH_CHECK_ENABLED, true),
				healthCheckPort: this.parseNumber(process.env.HEALTH_CHECK_PORT, 8080),
				alertingEnabled: this.parseBoolean(process.env.ALERTING_ENABLED, true),
				logLevel: process.env.LOG_LEVEL || 'info',
				logFormat: process.env.LOG_FORMAT || 'json',
				logDir: process.env.LOG_DIR || './logs',
				maxLogFiles: this.parseNumber(process.env.MAX_LOG_FILES, 10),
				maxLogSize: process.env.MAX_LOG_SIZE || '100MB',
			},
			security: {
				encryptionKey: process.env.ENCRYPTION_KEY,
				credentialRotationIntervalDays: this.parseNumber(process.env.CREDENTIAL_ROTATION_INTERVAL_DAYS, 90),
				credentialExpiryWarningDays: this.parseNumber(process.env.CREDENTIAL_EXPIRY_WARNING_DAYS, 7),
				secretsPath: process.env.SECRETS_PATH || './secrets',
				tlsEnabled: this.parseBoolean(process.env.TLS_ENABLED, false),
				tlsCertPath: process.env.TLS_CERT_PATH,
				tlsKeyPath: process.env.TLS_KEY_PATH,
			},
		};
	}

	private buildSourcesConfiguration(): any
	{
		return {
			tranco: {
				enabled: this.parseBoolean(process.env.TRANCO_ENABLED, true),
				priority: this.parseNumber(process.env.TRANCO_PRIORITY, 1),
				timeout: this.parseNumber(process.env.TRANCO_TIMEOUT, 30000),
				maxRetries: this.parseNumber(process.env.TRANCO_MAX_RETRIES, 3),
				rateLimit: this.parseNumber(process.env.TRANCO_RATE_LIMIT, 60),
			},
			radar: {
				enabled: this.parseBoolean(process.env.RADAR_ENABLED, true),
				apiKey: process.env.CLOUDFLARE_API_KEY || '',
				priority: this.parseNumber(process.env.RADAR_PRIORITY, 2),
				timeout: this.parseNumber(process.env.RADAR_TIMEOUT, 30000),
				maxRetries: this.parseNumber(process.env.RADAR_MAX_RETRIES, 3),
				rateLimit: this.parseNumber(process.env.RADAR_RATE_LIMIT, 300),
			},
			umbrella: {
				enabled: this.parseBoolean(process.env.UMBRELLA_ENABLED, true),
				apiKey: process.env.UMBRELLA_API_KEY || '',
				priority: this.parseNumber(process.env.UMBRELLA_PRIORITY, 3),
				timeout: this.parseNumber(process.env.UMBRELLA_TIMEOUT, 30000),
				maxRetries: this.parseNumber(process.env.UMBRELLA_MAX_RETRIES, 3),
				rateLimit: this.parseNumber(process.env.UMBRELLA_RATE_LIMIT, 100),
			},
			czds: {
				enabled: this.parseBoolean(process.env.CZDS_ENABLED, true),
				username: process.env.CZDS_USERNAME || '',
				password: process.env.CZDS_PASSWORD || '',
				priority: this.parseNumber(process.env.CZDS_PRIORITY, 4),
				timeout: this.parseNumber(process.env.CZDS_TIMEOUT, 60000),
				maxRetries: this.parseNumber(process.env.CZDS_MAX_RETRIES, 3),
				downloadPath: process.env.CZDS_DOWNLOAD_PATH || '/tmp/czds',
			},
			pir: {
				enabled: this.parseBoolean(process.env.PIR_ENABLED, true),
				apiKey: process.env.PIR_API_KEY || '',
				priority: this.parseNumber(process.env.PIR_PRIORITY, 5),
				timeout: this.parseNumber(process.env.PIR_TIMEOUT, 30000),
				maxRetries: this.parseNumber(process.env.PIR_MAX_RETRIES, 3),
				rateLimit: this.parseNumber(process.env.PIR_RATE_LIMIT, 60),
			},
			commonCrawl: {
				enabled: this.parseBoolean(process.env.COMMON_CRAWL_ENABLED, true),
				priority: this.parseNumber(process.env.COMMON_CRAWL_PRIORITY, 6),
				timeout: this.parseNumber(process.env.COMMON_CRAWL_TIMEOUT, 60000),
				maxRetries: this.parseNumber(process.env.COMMON_CRAWL_MAX_RETRIES, 3),
				indexUrl: process.env.COMMON_CRAWL_INDEX_URL || 'https://index.commoncrawl.org/',
				batchSize: this.parseNumber(process.env.COMMON_CRAWL_BATCH_SIZE, 10000),
			},
			sonar: {
				enabled: this.parseBoolean(process.env.SONAR_ENABLED, true),
				apiKey: process.env.SONAR_API_KEY || '',
				priority: this.parseNumber(process.env.SONAR_PRIORITY, 7),
				timeout: this.parseNumber(process.env.SONAR_TIMEOUT, 30000),
				maxRetries: this.parseNumber(process.env.SONAR_MAX_RETRIES, 3),
				rateLimit: this.parseNumber(process.env.SONAR_RATE_LIMIT, 100),
			},
		};
	}

	private buildAIConfiguration(): any
	{
		return {
			providers: {
				openai: {
					enabled: this.parseBoolean(process.env.OPENAI_ENABLED, false),
					apiKeys: this.parseArray(process.env.OPENAI_API_KEYS, []),
					priority: this.parseNumber(process.env.OPENAI_PRIORITY, 1),
					model: process.env.OPENAI_MODEL || 'gpt-4',
					maxTokens: this.parseNumber(process.env.OPENAI_MAX_TOKENS, 4000),
					temperature: this.parseFloat(process.env.OPENAI_TEMPERATURE, 0.7),
					rateLimit: this.parseNumber(process.env.OPENAI_RATE_LIMIT, 60),
				},
				claude: {
					enabled: this.parseBoolean(process.env.CLAUDE_ENABLED, false),
					apiKeys: this.parseArray(process.env.CLAUDE_API_KEYS, []),
					priority: this.parseNumber(process.env.CLAUDE_PRIORITY, 2),
					model: process.env.CLAUDE_MODEL || 'claude-3-sonnet-20240229',
					maxTokens: this.parseNumber(process.env.CLAUDE_MAX_TOKENS, 4000),
					temperature: this.parseFloat(process.env.CLAUDE_TEMPERATURE, 0.7),
					rateLimit: this.parseNumber(process.env.CLAUDE_RATE_LIMIT, 50),
				},
				gemini: {
					enabled: this.parseBoolean(process.env.GEMINI_ENABLED, false),
					apiKeys: this.parseArray(process.env.GEMINI_API_KEYS, []),
					priority: this.parseNumber(process.env.GEMINI_PRIORITY, 3),
					model: process.env.GEMINI_MODEL || 'gemini-pro',
					maxTokens: this.parseNumber(process.env.GEMINI_MAX_TOKENS, 4000),
					temperature: this.parseFloat(process.env.GEMINI_TEMPERATURE, 0.7),
					rateLimit: this.parseNumber(process.env.GEMINI_RATE_LIMIT, 60),
				},
				openrouter: {
					enabled: this.parseBoolean(process.env.OPENROUTER_ENABLED, false),
					apiKeys: this.parseArray(process.env.OPENROUTER_API_KEYS, []),
					priority: this.parseNumber(process.env.OPENROUTER_PRIORITY, 4),
					model: process.env.OPENROUTER_MODEL || 'anthropic/claude-3-sonnet',
					maxTokens: this.parseNumber(process.env.OPENROUTER_MAX_TOKENS, 4000),
					temperature: this.parseFloat(process.env.OPENROUTER_TEMPERATURE, 0.7),
					rateLimit: this.parseNumber(process.env.OPENROUTER_RATE_LIMIT, 100),
				},
			},
			proxy: {
				enabled: this.parseBoolean(process.env.AI_PROXY_ENABLED, false),
				ips: this.parseArray(process.env.AI_PROXY_IPS, []),
				rotationIntervalMs: this.parseNumber(process.env.AI_PROXY_ROTATION_INTERVAL_MS, 300000),
				healthCheckIntervalMs: this.parseNumber(process.env.AI_PROXY_HEALTH_CHECK_INTERVAL_MS, 60000),
			},
			fallbackEnabled: this.parseBoolean(process.env.AI_FALLBACK_ENABLED, true),
			maxRetries: this.parseNumber(process.env.AI_MAX_RETRIES, 3),
			timeoutMs: this.parseNumber(process.env.AI_TIMEOUT_MS, 30000),
		};
	}

	private validateConfiguration(rawConfig: any): ConfigValidationResult
	{
		try
		{
			const config = SeederConfigurationSchema.parse(rawConfig);
			const warnings = this.performAdditionalValidation(config);

			return {
				isValid: true,
				errors: [],
				warnings,
				config,
			};
		}
		catch (error)
		{
			if (error instanceof z.ZodError)
			{
				const errors = error.issues.map((err: z.ZodIssue) => `${err.path.join('.')}: ${err.message}`);
				return {
					isValid: false,
					errors,
					warnings: [],
				};
			}

			return {
				isValid: false,
				errors: [error.message || 'Unknown validation error'],
				warnings: [],
			};
		}
	}

	private performAdditionalValidation(config: SeederConfiguration): string[]
	{
		const warnings: string[] = [];

		// Check for missing API keys for enabled sources
		if (config.sources.radar.enabled && !config.sources.radar.apiKey)
		{
			warnings.push('Radar source is enabled but API key is missing');
		}

		if (config.sources.umbrella.enabled && !config.sources.umbrella.apiKey)
		{
			warnings.push('Umbrella source is enabled but API key is missing');
		}

		if (config.sources.czds.enabled && (!config.sources.czds.username || !config.sources.czds.password))
		{
			warnings.push('CZDS source is enabled but credentials are missing');
		}

		if (config.sources.pir.enabled && !config.sources.pir.apiKey)
		{
			warnings.push('PIR source is enabled but API key is missing');
		}

		if (config.sources.sonar.enabled && !config.sources.sonar.apiKey)
		{
			warnings.push('Sonar source is enabled but API key is missing');
		}

		// Check AI provider configuration
		const enabledAIProviders = Object.entries(config.ai.providers)
			.filter(([, provider]) => provider.enabled);

		for (const [name, provider] of enabledAIProviders)
		{
			if (provider.apiKeys.length === 0)
			{
				warnings.push(`AI provider ${name} is enabled but no API keys are configured`);
			}
		}

		// Check proxy configuration
		if (config.ai.proxy.enabled && config.ai.proxy.ips.length === 0)
		{
			warnings.push('AI proxy is enabled but no proxy IPs are configured');
		}

		// Check security configuration
		if (config.environment.nodeEnv === 'production')
		{
			if (!config.security.encryptionKey)
			{
				warnings.push('Production environment detected but encryption key is not configured');
			}

			if (!config.security.tlsEnabled)
			{
				warnings.push('Production environment detected but TLS is not enabled');
			}
		}

		// Check resource limits
		if (config.seeder.maxNewPerDay > 1000000)
		{
			warnings.push('Very high maxNewPerDay setting may impact system performance');
		}

		if (config.seeder.dbCheckBatch > 10000)
		{
			warnings.push('Very high dbCheckBatch setting may impact database performance');
		}

		return warnings;
	}

	private parseNumber(value: string | undefined, defaultValue: number): number
	{
		if (!value) return defaultValue;
		const parsed = parseInt(value, 10);
		return isNaN(parsed) ? defaultValue : parsed;
	}

	private parseFloat(value: string | undefined, defaultValue: number): number
	{
		if (!value) return defaultValue;
		const parsed = parseFloat(value);
		return isNaN(parsed) ? defaultValue : parsed;
	}

	private parseBoolean(value: string | undefined, defaultValue: boolean): boolean
	{
		if (!value) return defaultValue;
		return value.toLowerCase() === 'true';
	}

	private parseArray(value: string | undefined, defaultValue: string[]): string[]
	{
		if (!value) return defaultValue;
		return value.split(',').map(item => item.trim()).filter(Boolean);
	}

	// Public API methods
	getConfiguration(): SeederConfiguration
	{
		return { ...this.configuration };
	}

	getEnvironmentConfig(): SeederConfiguration['environment']
	{
		return { ...this.configuration.environment };
	}

	getDatabaseConfig(): SeederConfiguration['database']
	{
		return { ...this.configuration.database };
	}

	getSeederConfig(): SeederConfiguration['seeder']
	{
		return { ...this.configuration.seeder };
	}

	getSourcesConfig(): SeederConfiguration['sources']
	{
		return { ...this.configuration.sources };
	}

	getAIConfig(): SeederConfiguration['ai']
	{
		return { ...this.configuration.ai };
	}

	getMonitoringConfig(): SeederConfiguration['monitoring']
	{
		return { ...this.configuration.monitoring };
	}

	getSecurityConfig(): SeederConfiguration['security']
	{
		return { ...this.configuration.security };
	}

	getValidationErrors(): string[]
	{
		return [...this.validationErrors];
	}

	getValidationWarnings(): string[]
	{
		return [...this.validationWarnings];
	}

	isValid(): boolean
	{
		return this.validationErrors.length === 0;
	}

	override isDevelopment(): boolean
	{
		return this.configuration.environment.nodeEnv === 'development';
	}

	override isProduction(): boolean
	{
		return this.configuration.environment.nodeEnv === 'production';
	}

	isStaging(): boolean
	{
		return this.configuration.environment.nodeEnv === 'staging';
	}

	override isTest(): boolean
	{
		return this.configuration.environment.nodeEnv === 'test';
	}

	// Hot reload functionality
	enableHotReload(options: ConfigHotReloadOptions): void
	{
		this.hotReloadOptions = options;

		if (!options.enabled) return;

		// Watch environment files and configuration directories
		const watchPaths = [
			'.env',
			'.env.local',
			'.env.development',
			'.env.production',
			'.env.staging',
			...options.watchPaths,
		];

		for (const watchPath of watchPaths)
		{
			if (fs.existsSync(watchPath))
			{
				const watcher = fs.watch(watchPath, { persistent: false }, (eventType) =>
				{
					if (eventType === 'change')
					{
						this.handleConfigChange(options);
					}
				});

				this.watchers.push(watcher);
			}
		}

		if (this.logger)
		{
			this.logger.info({
				watchPaths: watchPaths.filter(p => fs.existsSync(p)),
				debounceMs: options.debounceMs,
			}, 'Configuration hot reload enabled');
		}
	}

	private debounceTimer?: NodeJS.Timeout;

	private handleConfigChange(options: ConfigHotReloadOptions): void
	{
		if (this.debounceTimer)
		{
			clearTimeout(this.debounceTimer);
		}

		this.debounceTimer = setTimeout(() =>
		{
			try
			{
				const newConfiguration = this.loadAndValidateConfiguration();

				if (this.logger)
				{
					this.logger.info({
						warnings: this.validationWarnings.length,
					}, 'Configuration reloaded successfully');
				}

				this.configuration = newConfiguration;

				if (options.callback)
				{
					options.callback(newConfiguration);
				}
			}
			catch (error)
			{
				if (this.logger)
				{
					this.logger.error('Failed to reload configuration', error);
				}
			}
		}, options.debounceMs);
	}

	disableHotReload(): void
	{
		for (const watcher of this.watchers)
		{
			watcher.close();
		}
		this.watchers = [];

		if (this.debounceTimer)
		{
			clearTimeout(this.debounceTimer);
			this.debounceTimer = undefined;
		}

		this.hotReloadOptions = undefined;

		if (this.logger)
		{
			this.logger.info('Configuration hot reload disabled');
		}
	}

	// Utility methods for backward compatibility
	getString(key: string, defaultValue?: string): string
	{
		return process.env[key] || defaultValue || '';
	}

	getNumber(key: string, defaultValue?: number): number
	{
		return this.parseNumber(process.env[key], defaultValue || 0);
	}

	getBoolean(key: string, defaultValue?: boolean): boolean
	{
		return this.parseBoolean(process.env[key], defaultValue || false);
	}

	getArray(key: string, defaultValue?: string[]): string[]
	{
		return this.parseArray(process.env[key], defaultValue || []);
	}
}

export type {
	SeederConfiguration,
	ConfigValidationResult,
	ConfigHotReloadOptions,
};

export {
	SeederConfig,
	SeederConfigurationSchema,
};

export default SeederConfig;
