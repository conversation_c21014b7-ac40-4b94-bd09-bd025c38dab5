import type { LoggerInstanceType } from '@shared';
import { z } from 'zod';
import * as fs from 'fs/promises';
import * as path from 'path';
import type { SeederConfiguration } from './SeederConfig';

// Environment profile schema
const EnvironmentProfileSchema = z.object({
	name: z.string(),
	extends: z.string().optional(), // Profile inheritance
	description: z.string().optional(),
	overrides: z.record(z.string(), z.unknown()), // Configuration overrides
	validation: z.object({
		required: z.array(z.string()).default([]),
		forbidden: z.array(z.string()).default([]),
		warnings: z.array(z.string()).default([]),
	}).default(() => ({
		required: [],
		forbidden: [],
		warnings: [],
	})),
	metadata: z.object({
		createdAt: z.string(),
		updatedAt: z.string(),
		version: z.string().default('1.0.0'),
		author: z.string().optional(),
		tags: z.array(z.string()).default([]),
	}),
});

const ProfileCollectionSchema = z.object({
	version: z.string().default('1.0.0'),
	profiles: z.array(EnvironmentProfileSchema),
	metadata: z.object({
		createdAt: z.string(),
		updatedAt: z.string(),
	}),
});

type EnvironmentProfile = z.infer<typeof EnvironmentProfileSchema>;
type ProfileCollection = z.infer<typeof ProfileCollectionSchema>;

interface ProfileValidationResult
{
	isValid: boolean;
	errors: string[];
	warnings: string[];
	profile?: EnvironmentProfile;
}

interface ConfigMigration
{
	fromVersion: string;
	toVersion: string;
	description: string;
	migrate: (config: any) => any;
}

class EnvironmentProfileManager
{
	private logger: LoggerInstanceType;

	private profilesPath: string;

	private profiles: Map<string, EnvironmentProfile> = new Map();

	private migrations: ConfigMigration[] = [];

	constructor(logger: LoggerInstanceType, profilesPath: string = './config/profiles')
	{
		this.logger = logger;
		this.profilesPath = profilesPath;
		this.setupMigrations();
	}

	async initialize(): Promise<void>
	{
		try
		{
			await this.ensureProfilesDirectory();
			await this.loadProfiles();
			await this.createDefaultProfiles();

			this.logger.info({
				profileCount: this.profiles.size,
				profilesPath: this.profilesPath,
			}, 'Environment profile manager initialized');
		}
		catch (error)
		{
			this.logger.error('Failed to initialize environment profile manager', error);
			throw error;
		}
	}

	private async ensureProfilesDirectory(): Promise<void>
	{
		try
		{
			await fs.access(this.profilesPath);
		}
		catch
		{
			await fs.mkdir(this.profilesPath, { recursive: true });
			this.logger.info({ path: this.profilesPath }, 'Created profiles directory');
		}
	}

	private async loadProfiles(): Promise<void>
	{
		try
		{
			const files = await fs.readdir(this.profilesPath);
			const profileFiles = files.filter(f => f.endsWith('.json'));

			for (const file of profileFiles)
			{
				try
				{
					const filePath = path.join(this.profilesPath, file);
					const data = await fs.readFile(filePath, 'utf-8');
					const profile = EnvironmentProfileSchema.parse(JSON.parse(data));
					this.profiles.set(profile.name, profile);
				}
				catch (error)
				{
					this.logger.warn({ file, error: error.message }, 'Failed to load profile file');
				}
			}

			this.logger.info({
				count: this.profiles.size,
				profiles: Array.from(this.profiles.keys()),
			}, 'Profiles loaded successfully');
		}
		catch (error)
		{
			this.logger.error('Failed to load profiles', error);
		}
	}

	private async createDefaultProfiles(): Promise<void>
	{
		const defaultProfiles: EnvironmentProfile[] = [
			{
				name: 'development',
				description: 'Development environment configuration',
				overrides: {
					'environment.nodeEnv': 'development',
					'monitoring.logLevel': 'debug',
					'monitoring.metricsEnabled': true,
					'monitoring.healthCheckEnabled': true,
					'seeder.maxNewPerDay': 10000,
					'seeder.enqueueBatch': 100,
					'seeder.contentGenerationEnabled': false,
					'sources.tranco.enabled': true,
					'sources.radar.enabled': false,
					'sources.umbrella.enabled': false,
					'sources.czds.enabled': false,
					'sources.pir.enabled': false,
					'sources.commonCrawl.enabled': false,
					'sources.sonar.enabled': false,
					'ai.providers.openai.enabled': false,
					'ai.providers.claude.enabled': false,
					'security.tlsEnabled': false,
				},
				validation: {
					required: ['database.redis.host', 'database.maria.host'],
					forbidden: ['security.encryptionKey'],
					warnings: ['sources.*.apiKey'],
				},
				metadata: {
					createdAt: new Date().toISOString(),
					updatedAt: new Date().toISOString(),
					version: '1.0.0',
					author: 'system',
					tags: ['default', 'development'],
				},
			},
			{
				name: 'staging',
				extends: 'development',
				description: 'Staging environment configuration',
				overrides: {
					'environment.nodeEnv': 'staging',
					'monitoring.logLevel': 'info',
					'seeder.maxNewPerDay': 100000,
					'seeder.enqueueBatch': 500,
					'seeder.contentGenerationEnabled': true,
					'sources.radar.enabled': true,
					'sources.umbrella.enabled': true,
					'ai.providers.openai.enabled': true,
					'security.tlsEnabled': true,
				},
				validation: {
					required: [
						'database.redis.host',
						'database.maria.host',
						'database.scylla.contactPoints',
						'database.manticore.host',
						'security.encryptionKey',
					],
					forbidden: [],
					warnings: [],
				},
				metadata: {
					createdAt: new Date().toISOString(),
					updatedAt: new Date().toISOString(),
					version: '1.0.0',
					author: 'system',
					tags: ['default', 'staging'],
				},
			},
			{
				name: 'production',
				extends: 'staging',
				description: 'Production environment configuration',
				overrides: {
					'environment.nodeEnv': 'production',
					'monitoring.logLevel': 'warn',
					'monitoring.alertingEnabled': true,
					'seeder.maxNewPerDay': 500000,
					'seeder.enqueueBatch': 1000,
					'seeder.contentGenerationEnabled': true,
					'seeder.contentGenerationMode': 'both',
					'sources.tranco.enabled': true,
					'sources.radar.enabled': true,
					'sources.umbrella.enabled': true,
					'sources.czds.enabled': true,
					'sources.pir.enabled': true,
					'sources.commonCrawl.enabled': true,
					'sources.sonar.enabled': true,
					'ai.providers.openai.enabled': true,
					'ai.providers.claude.enabled': true,
					'ai.fallbackEnabled': true,
					'security.tlsEnabled': true,
					'security.credentialRotationIntervalDays': 30,
				},
				validation: {
					required: [
						'database.redis.host',
						'database.maria.host',
						'database.scylla.contactPoints',
						'database.manticore.host',
						'security.encryptionKey',
						'security.tlsCertPath',
						'security.tlsKeyPath',
					],
					forbidden: [],
					warnings: [],
				},
				metadata: {
					createdAt: new Date().toISOString(),
					updatedAt: new Date().toISOString(),
					version: '1.0.0',
					author: 'system',
					tags: ['default', 'production'],
				},
			},
			{
				name: 'test',
				description: 'Test environment configuration',
				overrides: {
					'environment.nodeEnv': 'test',
					'monitoring.logLevel': 'error',
					'monitoring.metricsEnabled': false,
					'monitoring.healthCheckEnabled': false,
					'monitoring.alertingEnabled': false,
					'seeder.maxNewPerDay': 1000,
					'seeder.enqueueBatch': 10,
					'seeder.contentGenerationEnabled': false,
					'sources.tranco.enabled': false,
					'sources.radar.enabled': false,
					'sources.umbrella.enabled': false,
					'sources.czds.enabled': false,
					'sources.pir.enabled': false,
					'sources.commonCrawl.enabled': false,
					'sources.sonar.enabled': false,
					'ai.providers.openai.enabled': false,
					'ai.providers.claude.enabled': false,
					'security.tlsEnabled': false,
				},
				validation: {
					required: [],
					forbidden: ['security.encryptionKey'],
					warnings: [],
				},
				metadata: {
					createdAt: new Date().toISOString(),
					updatedAt: new Date().toISOString(),
					version: '1.0.0',
					author: 'system',
					tags: ['default', 'test'],
				},
			},
		];

		for (const profile of defaultProfiles)
		{
			if (!this.profiles.has(profile.name))
			{
				await this.saveProfile(profile);
				this.profiles.set(profile.name, profile);
			}
		}
	}

	async getProfile(name: string): Promise<EnvironmentProfile | null>
	{
		return this.profiles.get(name) || null;
	}

	async listProfiles(): Promise<EnvironmentProfile[]>
	{
		return Array.from(this.profiles.values());
	}

	async createProfile(profile: Omit<EnvironmentProfile, 'metadata'>): Promise<void>
	{
		const fullProfile: EnvironmentProfile = {
			...profile,
			metadata: {
				createdAt: new Date().toISOString(),
				updatedAt: new Date().toISOString(),
				version: '1.0.0',
				author: 'user',
				tags: [],
			},
		};

		const validation = this.validateProfile(fullProfile);
		if (!validation.isValid)
		{
			throw new Error(`Profile validation failed: ${validation.errors.join(', ')}`);
		}

		await this.saveProfile(fullProfile);
		this.profiles.set(fullProfile.name, fullProfile);

		this.logger.info({ name: fullProfile.name }, 'Profile created successfully');
	}

	async updateProfile(name: string, updates: Partial<EnvironmentProfile>): Promise<void>
	{
		const existing = this.profiles.get(name);
		if (!existing)
		{
			throw new Error(`Profile '${name}' not found`);
		}

		const updated: EnvironmentProfile = {
			...existing,
			...updates,
			metadata: {
				...existing.metadata,
				...updates.metadata,
				updatedAt: new Date().toISOString(),
			},
		};

		const validation = this.validateProfile(updated);
		if (!validation.isValid)
		{
			throw new Error(`Profile validation failed: ${validation.errors.join(', ')}`);
		}

		await this.saveProfile(updated);
		this.profiles.set(name, updated);

		this.logger.info({ name }, 'Profile updated successfully');
	}

	async deleteProfile(name: string): Promise<void>
	{
		if (!this.profiles.has(name))
		{
			throw new Error(`Profile '${name}' not found`);
		}

		const filePath = path.join(this.profilesPath, `${name}.json`);
		await fs.unlink(filePath);
		this.profiles.delete(name);

		this.logger.info({ name }, 'Profile deleted successfully');
	}

	private async saveProfile(profile: EnvironmentProfile): Promise<void>
	{
		const filePath = path.join(this.profilesPath, `${profile.name}.json`);
		const data = JSON.stringify(profile, null, 2);
		await fs.writeFile(filePath, data, 'utf-8');
	}

	private validateProfile(profile: EnvironmentProfile): ProfileValidationResult
	{
		try
		{
			const validated = EnvironmentProfileSchema.parse(profile);
			return {
				isValid: true,
				errors: [],
				warnings: [],
				profile: validated,
			};
		}
		catch (error)
		{
			if (error instanceof z.ZodError)
			{
				const errors = error.issues.map((err: z.ZodIssue) => `${err.path.join('.')}: ${err.message}`);
				return {
					isValid: false,
					errors,
					warnings: [],
				};
			}

			return {
				isValid: false,
				errors: [error.message || 'Unknown validation error'],
				warnings: [],
			};
		}
	}

	async applyProfile(profileName: string, baseConfig: any): Promise<any>
	{
		const profile = await this.getProfile(profileName);
		if (!profile)
		{
			throw new Error(`Profile '${profileName}' not found`);
		}

		let config = { ...baseConfig };

		// Apply inheritance
		if (profile.extends)
		{
			config = await this.applyProfile(profile.extends, config);
		}

		// Apply overrides
		config = this.applyOverrides(config, profile.overrides);

		// Validate profile-specific requirements
		const validationErrors = this.validateConfigAgainstProfile(config, profile);
		if (validationErrors.length > 0)
		{
			throw new Error(`Configuration validation failed for profile '${profileName}': ${validationErrors.join(', ')}`);
		}

		this.logger.debug({
			profileName,
			overridesCount: Object.keys(profile.overrides).length,
		}, 'Profile applied successfully');

		return config;
	}

	private applyOverrides(config: any, overrides: Record<string, any>): any
	{
		const result = { ...config };

		for (const [path, value] of Object.entries(overrides))
		{
			this.setNestedValue(result, path, value);
		}

		return result;
	}

	private setNestedValue(obj: any, path: string, value: any): void
	{
		const keys = path.split('.');
		let current = obj;

		for (let i = 0; i < keys.length - 1; i++)
		{
			const key = keys[i];
			if (!(key in current) || typeof current[key] !== 'object')
			{
				current[key] = {};
			}
			current = current[key];
		}

		current[keys[keys.length - 1]] = value;
	}

	private getNestedValue(obj: any, path: string): any
	{
		const keys = path.split('.');
		let current = obj;

		for (const key of keys)
		{
			if (current === null || current === undefined || !(key in current))
			{
				return undefined;
			}
			current = current[key];
		}

		return current;
	}

	private validateConfigAgainstProfile(config: any, profile: EnvironmentProfile): string[]
	{
		const errors: string[] = [];

		// Check required fields
		for (const requiredPath of profile.validation.required)
		{
			const value = this.getNestedValue(config, requiredPath);
			if (value === undefined || value === null || value === '')
			{
				errors.push(`Required configuration '${requiredPath}' is missing`);
			}
		}

		// Check forbidden fields
		for (const forbiddenPath of profile.validation.forbidden)
		{
			const value = this.getNestedValue(config, forbiddenPath);
			if (value !== undefined)
			{
				errors.push(`Forbidden configuration '${forbiddenPath}' is present`);
			}
		}

		// Log warnings
		for (const warningPath of profile.validation.warnings)
		{
			const value = this.getNestedValue(config, warningPath);
			if (value === undefined || value === null || value === '')
			{
				this.logger.warn({
					profile: profile.name,
				}, `Configuration warning: '${warningPath}' is not set`);
			}
		}

		return errors;
	}

	private setupMigrations(): void
	{
		this.migrations = [
			{
				fromVersion: '1.0.0',
				toVersion: '1.1.0',
				description: 'Add content generation configuration',
				migrate: (config: any) => ({
					...config,
					seeder: {
						...config.seeder,
						contentGenerationEnabled: true,
						contentGenerationMode: 'preGenerated',
					},
				}),
			},
			{
				fromVersion: '1.1.0',
				toVersion: '1.2.0',
				description: 'Add AI proxy configuration',
				migrate: (config: any) => ({
					...config,
					ai: {
						...config.ai,
						proxy: {
							enabled: false,
							ips: [],
							rotationIntervalMs: 300000,
							healthCheckIntervalMs: 60000,
						},
					},
				}),
			},
		];
	}

	async migrateConfig(config: any, fromVersion: string, toVersion: string): Promise<any>
	{
		let currentConfig = { ...config };
		let currentVersion = fromVersion;

		while (currentVersion !== toVersion)
		{
			const migration = this.migrations.find(m => m.fromVersion === currentVersion);
			if (!migration)
			{
				throw new Error(`No migration path found from version ${currentVersion} to ${toVersion}`);
			}

			this.logger.info({
				from: migration.fromVersion,
				to: migration.toVersion,
				description: migration.description,
			}, 'Applying configuration migration');

			currentConfig = migration.migrate(currentConfig);
			currentVersion = migration.toVersion;
		}

		return currentConfig;
	}

	async exportProfile(name: string): Promise<string>
	{
		const profile = await this.getProfile(name);
		if (!profile)
		{
			throw new Error(`Profile '${name}' not found`);
		}

		return JSON.stringify(profile, null, 2);
	}

	async importProfile(profileData: string): Promise<void>
	{
		try
		{
			const profile = EnvironmentProfileSchema.parse(JSON.parse(profileData));
			await this.saveProfile(profile);
			this.profiles.set(profile.name, profile);

			this.logger.info({ name: profile.name }, 'Profile imported successfully');
		}
		catch (error)
		{
			this.logger.error('Failed to import profile', error);
			throw error;
		}
	}
}

export type {
	EnvironmentProfile,
	ProfileCollection,
	ProfileValidationResult,
	ConfigMigration,
};

export { EnvironmentProfileManager };
