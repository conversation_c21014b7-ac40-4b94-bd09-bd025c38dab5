#!/usr/bin/env tsx

/**
 * Test script to verify TRENDING domain discovery with Cloudflare Radar
 */

import { config } from 'dotenv';
import { DiscoveryOperations } from './src/core/DiscoveryOperations';
import { DatabaseManager } from '@shared';
import { logger } from '@shared';
import RadarConnector from './src/connectors/RadarConnector';

// Load environment variables
config();

const testLogger = logger.getLogger('test-trending-discovery');

async function testTrendingDiscovery() {
	try {
		testLogger.info('Starting TRENDING domain discovery test...');

		// Check if API key is configured
		const apiKey = process.env.CLOUDFLARE_API_KEY;
		if (!apiKey) {
			testLogger.error('CLOUDFLARE_API_KEY not configured in .env file');
			process.exit(1);
		}

		// Test 1: Direct Radar connector with TRENDING domains
		testLogger.info('=== Testing Direct Radar Connector for TRENDING domains ===');
		await testRadarTrending();

		// Test 2: Test with different locations
		testLogger.info('=== Testing Geographic TRENDING domains ===');
		await testGeographicTrending();

		// Test 3: Compare TRENDING vs POPULAR
		testLogger.info('=== Comparing TRENDING vs POPULAR domains ===');
		await compareTrendingVsPopular();

		// Test 4: Discovery Operations with trending strategy
		testLogger.info('=== Testing Discovery Operations with trending strategy ===');
		await testDiscoveryOperations();

		testLogger.info('Trending domain discovery test completed successfully!');

	} catch (error) {
		testLogger.error('Trending discovery test failed:', error);
		process.exit(1);
	}
}

async function testRadarTrending() {
	try {
		const radar = new RadarConnector({});

		// Test health check
		const isHealthy = await radar.healthCheck();
		testLogger.info('Radar health check:', { isHealthy });

		if (!isHealthy) {
			testLogger.error('Radar health check failed');
			return;
		}

		// Test fetching TRENDING domains
		testLogger.info('Fetching TRENDING domains (strategy: differential)...');
		const trendingDomains = [];

		for await (const domain of radar.fetchDomains({
			limit: 20,
			strategy: 'differential' // This should trigger TRENDING
		})) {
			trendingDomains.push(domain);
			testLogger.info(`Trending domain:`, {
				domain: domain.domain,
				rank: domain.rank,
				metadata: domain.metadata
			});

			if (trendingDomains.length >= 20) break;
		}

		testLogger.info('TRENDING domains summary:', {
			total: trendingDomains.length,
			domains: trendingDomains.slice(0, 10).map(d => d.domain),
			uniqueCategories: [...new Set(trendingDomains.flatMap(d =>
				(d.metadata?.categories || []).map(c => c.name)
			))]
		});

	} catch (error) {
		testLogger.error('Radar trending test failed:', error);
	}
}

async function testGeographicTrending() {
	try {
		const radar = new RadarConnector({});

		// Test different geographic locations
		const locations = ['US', 'GB', 'DE', 'JP'];

		for (const location of locations) {
			testLogger.info(`Fetching trending domains for location: ${location}`);

			const locationDomains = [];

			for await (const domain of radar.fetchDomains({
				limit: 5,
				strategy: 'differential',
				location: location
			})) {
				locationDomains.push(domain);
			}

			testLogger.info(`Trending in ${location}:`, {
				count: locationDomains.length,
				domains: locationDomains.map(d => d.domain)
			});
		}

	} catch (error) {
		testLogger.error('Geographic trending test failed:', error);
	}
}

async function compareTrendingVsPopular() {
	try {
		const radar = new RadarConnector({});

		// Fetch TRENDING domains
		testLogger.info('Fetching TRENDING domains...');
		const trendingDomains = [];

		for await (const domain of radar.fetchDomains({
			limit: 10,
			strategy: 'differential' // Triggers TRENDING
		})) {
			trendingDomains.push(domain.domain);
		}

		// Fetch POPULAR domains
		testLogger.info('Fetching POPULAR domains...');
		const popularDomains = [];

		for await (const domain of radar.fetchDomains({
			limit: 10,
			strategy: 'baseline' // Should trigger POPULAR
		})) {
			popularDomains.push(domain.domain);
		}

		// Compare the lists
		const uniqueToTrending = trendingDomains.filter(d => !popularDomains.includes(d));
		const uniqueToPopular = popularDomains.filter(d => !trendingDomains.includes(d));
		const common = trendingDomains.filter(d => popularDomains.includes(d));

		testLogger.info('Comparison Results:', {
			trending: {
				total: trendingDomains.length,
				unique: uniqueToTrending.length,
				domains: trendingDomains.slice(0, 5)
			},
			popular: {
				total: popularDomains.length,
				unique: uniqueToPopular.length,
				domains: popularDomains.slice(0, 5)
			},
			common: {
				count: common.length,
				domains: common.slice(0, 5)
			},
			discovery: {
				uniqueTrendingDomains: uniqueToTrending,
				msg: 'These trending domains are NOT in popular list - good for discovery!'
			}
		});

	} catch (error) {
		testLogger.error('Comparison test failed:', error);
	}
}

async function testDiscoveryOperations() {
	try {
		const dbManager = new DatabaseManager();
		await dbManager.initialize();

		const discoveryOps = new DiscoveryOperations(dbManager);

		// Test discovery with differential strategy (should use TRENDING)
		testLogger.info('Testing discovery with differential strategy (TRENDING)...');

		const discoveryResult = await discoveryOps.triggerDiscoveryRun({
			strategies: ['differential', 'temporal'],
			sources: ['radar'],
			limit: 15,
			dryRun: true
		});

		testLogger.info('Discovery run results:', {
			totalCandidates: discoveryResult.totalCandidates,
			totalDiscovered: discoveryResult.totalDiscovered,
			strategies: discoveryResult.strategies
		});

		// Test with top sources
		testLogger.info('Testing top sources with Radar...');

		const topSourcesResult = await discoveryOps.triggerTopSourcesRun({
			sources: ['radar'],
			limit: 10,
			aggregate: false
		});

		testLogger.info('Top sources results:', {
			radar: topSourcesResult.sources.radar
		});

		await dbManager.close();

	} catch (error) {
		testLogger.error('Discovery operations test failed:', error);
	}
}

// Run the test
testTrendingDiscovery().catch(console.error);