# Domain Seeder Service

The Domain Seeder is a high-performance microservice for discovering, analyzing, and seeding domain data into the domain ranking system. It provides comprehensive domain discovery through multiple strategies, real-time content analysis, and robust monitoring capabilities.

## Table of Contents

- [Quick Start](#quick-start)
- [Deployment](#deployment)
- [Architecture](#architecture)
- [Configuration](#configuration)
- [Usage](#usage)
- [CLI Commands and API Reference](#cli-commands-and-api-reference)
- [Discovery Strategies](#discovery-strategies)
- [Monitoring and Alerting](#monitoring-and-alerting)
- [PSL Manager](#psl-manager)
- [Scheduler Integration](#scheduler-integration)
- [Support](#support)
- [License](#license)

## Quick Start

### Prerequisites

- Node.js 18+
- Docker and Docker Compose
- Redis server
- ScyllaDB or Cassandra cluster
- MariaDB instance
- Manticore Search server

### Environment Setup

1. **Clone and install dependencies:**

   ```bash
   cd services/domain-seeder
   pnpm install
   ```

2. **Configure environment:**

   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start dependencies:**

   ```bash
   # From project root
   docker-compose -f docker-compose.dev.yml up -d
   ```

4. **Run the service:**
   ```bash
   pnpm start
   ```

### Docker Deployment

```bash
# Build and run
docker-compose up domain-seeder
```

## Deployment

### Prerequisites

**System Requirements:**

- Linux server (Ubuntu 20.04+ recommended)
- Minimum 4GB RAM, 8GB recommended
- 50GB+ available disk space
- Docker 20.10+ and Docker Compose 2.0+
- Node.js 18+ (for development)

**External Services:**

- Redis 6.0+ (caching and job queue)
- ScyllaDB 4.0+ or Cassandra 3.11+ (primary database)
- MariaDB 10.6+ (relational data)
- Manticore Search 4.0+ (full-text search)

**External APIs (Optional but Recommended):**

- Cloudflare Radar API key
- CZDS access credentials
- Cisco Umbrella API key
- PIR Registry credentials
- Rapid7 API key
- AI Provider keys (OpenAI, Claude, Gemini, or OpenRouter)

### Environment Setup

**1. Clone Repository:**

```bash
git clone <repository-url>
cd domainr/services/domain-seeder
```

**2. Environment Configuration:**

```bash
cp .env.example .env
```

**Edit `.env` with your configuration:**

```bash
# Service Configuration
PORT=3003
NODE_ENV=production
LOG_LEVEL=info

# Database Connections
SCYLLA_HOSTS=localhost:9042
SCYLLA_KEYSPACE=domainr
MARIADB_HOST=localhost
MARIADB_PORT=3306
MARIADB_USER=domainr
MARIADB_PASSWORD=your_password
MARIADB_DATABASE=domainr
MANTICORE_HOST=localhost
MANTICORE_PORT=9308

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_DB=0

# API Keys (Optional)
CLOUDFLARE_API_KEY=your_key
CZDS_USERNAME=your_username
CZDS_PASSWORD=your_password
CISCO_UMBRELLA_KEY=your_key
PIR_USERNAME=your_username
PIR_PASSWORD=your_password
RAPID7_API_KEY=your_key

# AI Providers (Optional)
OPENAI_API_KEY=your_key
CLAUDE_API_KEY=your_key
GEMINI_API_KEY=your_key
OPENROUTER_API_KEY=your_key

# Rate Limiting
MAX_CONCURRENT_DISCOVERIES=10
DISCOVERY_RATE_LIMIT=100
CONTENT_ANALYSIS_RATE_LIMIT=50

# Security
JWT_SECRET=your_jwt_secret_256_bits_minimum
API_KEY=your_api_key_for_http_endpoints
```

### Development Deployment

**Using Docker Compose (Recommended):**

```bash
# From project root
docker-compose -f docker-compose.dev.yml up domain-seeder
```

**Local Development:**

```bash
# Install dependencies
pnpm install

# Start in development mode
pnpm dev

# Or start with specific configuration
NODE_ENV=development pnpm start
```

### Production Deployment

**1. Using Docker (Recommended):**

```bash
# Build production image
docker build -t domain-seeder:latest .

# Run with production configuration
docker run -d \
  --name domain-seeder \
  --env-file .env \
  --network host \
  --restart unless-stopped \
  domain-seeder:latest
```

**2. Using Docker Compose:**

```bash
# From project root
docker-compose up -d domain-seeder
```

```bash
docker build -t domain-seeder .
docker run -d --name domain-seeder \
  --env-file .env \
  --network host \
  domain-seeder
```

3. **Or deploy using PM2:**
   ```bash
   pm2 start ecosystem.config.js
   ```

## Configuration

### Environment Variables

#### Database Configuration

```bash
# ScyllaDB
SCYLLA_HOSTS=localhost:9042
SCYLLA_KEYSPACE=domainr
SCYLLA_USERNAME=cassandra
SCYLLA_PASSWORD=cassandra

# MariaDB
MARIA_HOST=localhost
MARIA_PORT=3306
MARIA_DATABASE=domainr
MARIA_USERNAME=root
MARIA_PASSWORD=password

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Manticore Search
MANTICORE_HOST=localhost
MANTICORE_PORT=9308
```

#### Service Configuration

```bash
# Discovery limits
MAX_NEW_PER_DAY=500000
ENQUEUE_BATCH_SIZE=1000
ENQUEUE_INTERVAL_MS=1000
NEW_QUEUE_MAX_DEPTH=200000

# Rate limiting
BLOOM_FP_RATE=0.01
DB_CHECK_BATCH_SIZE=5000

# Logging
LOG_LEVEL=info
LOG_RETENTION_DAYS=30
ARCHIVE_DIRECTORY=./logs/archives
```

#### External API Credentials

```bash
# Cloudflare Radar
CLOUDFLARE_API_KEY=your_api_key

# CZDS (Centralized Zone Data Service)
CZDS_USERNAME=your_username
CZDS_PASSWORD=your_password

# Cisco Umbrella
UMBRELLA_API_KEY=your_api_key

# AI Providers (optional)
OPENAI_API_KEY=your_openai_key
CLAUDE_API_KEY=your_claude_key
```

### Configuration Profiles

The service supports environment-specific configuration profiles:

- **Development**: `config/development.json`
- **Staging**: `config/staging.json`
- **Production**: `config/production.json`

## Usage

### CLI Commands

#### Discovery Operations

```bash
# Full discovery cycle with all strategies
pnpm seeder:run

# Run specific strategies
pnpm seeder:run --strategies differential,zone-new

# Run with specific sources
pnpm seeder:run --sources tranco,radar --limit 50000

# Dry run (no actual enqueuing)
pnpm seeder:run --dry-run

# Top sources only (Tranco + Radar)
pnpm seeder:top --limit 100000

# Backfill with long-tail sources
pnpm seeder:backfill --sources czds,common-crawl --max-new 100000
```

#### Status and Monitoring

```bash
# Basic status
pnpm seeder:status

# Detailed status with performance metrics
pnpm seeder:status --detailed

# JSON output for programmatic use
pnpm seeder:status --json

# Watch mode (continuous updates)
pnpm seeder:status --watch
```

#### Log Management

```bash
# View recent logs
pnpm seeder logs --limit 50

# Filter by component
pnpm seeder logs --component DiscoveryEngine

# Filter by log level
pnpm seeder logs --level error --since "2025-01-01"

# Show audit events
pnpm seeder logs --audit --operation discover_domains

# Analyze logs for patterns
pnpm seeder logs --analyze --since "2025-01-01"

# Show system metrics
pnpm seeder logs --metrics --since "2025-01-01"

# Follow correlation trail
pnpm seeder logs --correlation-id abc-123-def

# Apply retention policies
pnpm seeder logs --retention

# List archives
pnpm seeder logs --archive list
```

#### Content Generation

```bash
# Generate domain description (preGenerated mode)
pnpm seeder:describe example.com

# Generate with live analysis
pnpm seeder:describe example.com --live

# Batch processing
pnpm seeder:describe --file domains.txt --output results.json

# Analyze live content for existing domain
pnpm seeder:analyze-live example.com
```

### HTTP API

### Quick API Testing

The service exposes HTTP endpoints for monitoring and control:

```bash
# Health check
curl -H "Authorization: Bearer $API_KEY" http://localhost:3004/health

# Detailed status
curl -H "Authorization: Bearer $API_KEY" http://localhost:3004/status

# Trigger discovery run
curl -X POST -H "Authorization: Bearer $API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"strategy":"differential"}' \
  http://localhost:3004/discovery/trigger

# Prometheus metrics (no auth required)
curl http://localhost:3004/metrics
```

## Discovery Strategies

### 1. Differential Analysis Strategy

**Purpose**: Identify domains that newly appear in ranking lists

**How it works**:

- Stores daily snapshots of top-1M from each source
- Computes set differences to find new entries
- High confidence (0.9) for newly appearing domains

**Example**: If yesterday's Tranco had domains 1-1000000, and today has a new domain at position 50000, that's a discovery candidate.

### 2. Zone File Processing Strategy

**Purpose**: Catch newly registered domains from authoritative sources

**How it works**:

- Processes CZDS zone file diffs
- Identifies domains registered in the last 24-48 hours
- Very high confidence (0.95) for newly registered domains

**Example**: New .com registrations from yesterday's zone file that don't exist in our database.

### 3. Long-tail Exploration Strategy

**Purpose**: Discover domains beyond typical top-1M lists

**How it works**:

- Processes Common Crawl host frequency data starting from position 1M+
- Skips domains 1-1M (likely already known)
- Medium confidence (0.7) due to potential noise

**Example**: A domain ranked 2.5M in Common Crawl that we've never seen before.

### 4. Temporal Analysis Strategy

**Purpose**: Identify rapidly rising domains

**How it works**:

- Tracks ranking velocity over time
- Identifies domains with >50% rank improvement week-over-week
- High confidence (0.8) for clear upward trends

**Example**: A domain that moved from position 800k to 200k in Cloudflare Radar over 7 days.

## Monitoring

### Prometheus Metrics

The service exposes comprehensive metrics for monitoring:

```
# Discovery metrics
candidates_fetched_total{source}
candidates_after_normalize
known_in_db_total
new_discovered_total{strategy}

# Content generation metrics
content_generated_total{mode}
content_validation_failures_total
seo_summary_length_hist

# Queue metrics
enqueue_attempts_total
enqueue_success_total
rate_limited_total
queue_depth

# Performance metrics
db_check_latency_ms
source_staleness_seconds
processing_duration_seconds{operation}
```

### Health Checks

The service provides multiple health check endpoints:

- **Liveness**: `/health` - Basic service health
- **Readiness**: `/ready` - Ready to accept traffic
- **Detailed**: `/status` - Comprehensive system status

### Alerting

Key alerts to configure:

1. **Zero discoveries for 24h**: No new domains discovered
2. **High error rate**: >10 errors per minute
3. **Queue depth critical**: >100k domains in queue for >1h
4. **Database connectivity**: Any database connection failures
5. **Source staleness**: Source data >24h old

### Logging

The service implements structured logging with:

- **Correlation IDs**: Track operations across components
- **Audit trails**: Complete record of discovery decisions
- **Performance metrics**: Operation timing and resource usage
- **Retention policies**: Automatic archival and cleanup

## Troubleshooting

### Common Issues

#### 1. No Domains Being Discovered

**Symptoms**: Zero new discoveries for extended periods

**Possible Causes**:

- Source connectivity issues
- Database existence check failures
- Rate limiting too aggressive
- All candidates already in database

**Diagnosis**:

```bash
# Check source connectivity
pnpm seeder:status --detailed

# Check recent logs for errors
pnpm seeder logs --level error --since "1 hour ago"

# Test individual sources
pnpm seeder:run --sources tranco --limit 1000 --dry-run
```

**Solutions**:

- Verify API credentials for external sources
- Check database connectivity
- Review rate limiting configuration
- Examine bloom filter accuracy

#### 2. High Memory Usage

**Symptoms**: Service consuming >5GB RAM

**Possible Causes**:

- Large bloom filter cache
- Memory leaks in processing
- Too many concurrent operations

**Diagnosis**:

```bash
# Check memory metrics
pnpm seeder logs --metrics

# Monitor active operations
pnpm seeder:status --watch
```

**Solutions**:

- Reduce bloom filter size
- Implement batch processing limits
- Restart service to clear memory leaks

#### 3. Database Connection Failures

**Symptoms**: Database health checks failing

**Possible Causes**:

- Network connectivity issues
- Database service down
- Connection pool exhaustion
- Authentication failures

**Diagnosis**:

```bash
# Check database status
pnpm seeder:status --detailed

# Test database connections
docker exec -it scylla cqlsh
docker exec -it mariadb mysql -u root -p
```

**Solutions**:

- Verify database services are running
- Check connection pool configuration
- Validate credentials
- Review network connectivity

#### 4. Queue Depth Growing

**Symptoms**: Queue depth continuously increasing

**Possible Causes**:

- Downstream processing bottleneck
- Rate limiting too restrictive
- Consumer not processing messages

**Diagnosis**:

```bash
# Monitor queue metrics
pnpm seeder logs --metrics --component RateLimitedDomainEnqueuer

# Check consumer status
redis-cli XINFO GROUPS stream:new:domains
```

**Solutions**:

- Increase processing capacity downstream
- Adjust rate limiting parameters
- Verify consumer group configuration

### Recovery Procedures

#### Service Restart

```bash
# Graceful restart
pm2 restart domain-seeder

# Force restart if hung
pm2 delete domain-seeder
pm2 start ecosystem.config.js
```

#### Database Recovery

```bash
# ScyllaDB repair
docker exec scylla nodetool repair

# MariaDB check
docker exec mariadb mysqlcheck --all-databases

# Redis memory cleanup
redis-cli FLUSHDB
```

#### Queue Recovery

```bash
# Check pending messages
redis-cli XPENDING stream:new:domains domain-seeder-group

# Claim stuck messages
redis-cli XCLAIM stream:new:domains domain-seeder-group consumer 3600000 <message-id>
```

### Performance Tuning

#### Database Optimization

```bash
# ScyllaDB
- Increase concurrent reads/writes
- Optimize compaction strategy
- Monitor partition sizes

# MariaDB
- Tune connection pool size
- Optimize query cache
- Review slow query log

# Redis
- Configure appropriate memory policy
- Monitor memory usage
- Optimize data structures
```

#### Application Tuning

```bash
# Batch sizes
DB_CHECK_BATCH_SIZE=5000  # Increase for better throughput
ENQUEUE_BATCH_SIZE=1000   # Adjust based on queue capacity

# Rate limiting
ENQUEUE_INTERVAL_MS=500   # Decrease for higher throughput
MAX_NEW_PER_DAY=1000000   # Increase daily limit

# Memory management
BLOOM_FP_RATE=0.001       # Lower for better accuracy (higher memory)
```

## CLI Commands and API Reference

### CLI Commands Summary

The domain-seeder provides comprehensive CLI commands for domain discovery, content analysis, and system management:

#### Discovery Commands

```bash
# Run all discovery strategies
pnpm run discover

# Run specific strategy
pnpm run discover --strategy differential
pnpm run discover --strategy zone-file
pnpm run discover --strategy long-tail

# Run with specific source
pnpm run discover --strategy differential --source tranco

# Set priority and limits
pnpm run discover --priority high --limit 1000
```

### HTTP API Reference

#### Health Check

```http
GET /health
Authorization: Bearer <token>
```

**Response:**

```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "uptime": 3600,
  "version": "1.0.0"
}
```

## Monitoring and Alerting

### Prometheus Metrics

The service exposes comprehensive metrics at `/metrics` endpoint:

**Discovery Metrics:**

- `domain_seeder_discovery_runs_total` - Total discovery runs by strategy
- `domain_seeder_domains_found_total` - Total domains discovered
- `domain_seeder_discovery_duration_seconds` - Discovery run duration
- `domain_seeder_discovery_success_rate` - Success rate by strategy

**Queue Metrics:**

- `domain_seeder_queue_size` - Current queue sizes by type
- `domain_seeder_queue_processed_total` - Total jobs processed
- `domain_seeder_queue_failed_total` - Total failed jobs
- `domain_seeder_queue_processing_duration_seconds` - Job processing time

**System Metrics:**

- `domain_seeder_memory_usage_bytes` - Memory usage
- `domain_seeder_cpu_usage_percent` - CPU utilization
- `domain_seeder_database_connections` - Active database connections
- `domain_seeder_external_api_requests_total` - External API calls

### Grafana Dashboards

**Main Dashboard includes:**

- Discovery performance overview
- Queue status and throughput
- Error rates and success metrics
- Resource utilization graphs
- External API usage tracking

**Alert Rules:**

- High error rate (>5% over 5 minutes)
- Queue backup (>1000 pending jobs)
- Memory usage (>80% for 10 minutes)
- Discovery failure (no successful runs in 1 hour)
- Database connection issues

### Log Monitoring

**Structured logging with fields:**

- `level`: error, warn, info, debug
- `timestamp`: ISO 8601 format
- `service`: domain-seeder
- `strategy`: discovery strategy name
- `domain`: domain being processed
- `duration`: operation duration
- `error`: error details if applicable

**Key log patterns to monitor:**

- `ERROR` level logs for failures
- Discovery strategy completion logs
- Database connection errors
- External API rate limit warnings
- Memory usage warnings

## Operations Runbook

### Daily Operations

**Morning Checklist:**

1. Check service health: `curl http://localhost:3004/health`
2. Review overnight discovery results
3. Monitor queue sizes and clear any stuck jobs
4. Check error logs for any new issues
5. Verify external API quotas and usage

**Discovery Management:**

```bash
# Check discovery status
curl -H "Authorization: Bearer $API_KEY" http://localhost:3004/status

# Trigger manual discovery if needed
curl -X POST -H "Authorization: Bearer $API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"strategy":"differential","priority":"high"}' \
  http://localhost:3004/discovery/trigger

# Monitor queue status
pnpm run queue:status
```

### Incident Response

**Critical Issues (P0):**

- Service completely down
- Database connection lost
- All discovery strategies failing

**Response Steps:**

1. **Immediate:** Check service status and logs
2. **Assess:** Determine scope and impact
3. **Mitigate:** Restart service if needed
4. **Investigate:** Review logs and metrics
5. **Resolve:** Fix root cause
6. **Follow-up:** Post-incident review

**High Priority Issues (P1):**

- Single strategy failing
- High error rates (>10%)
- Performance degradation
- Queue backup

**Moderate Issues (P2):**

- Individual domain processing failures
- Slow response times
- Memory usage warnings

### Performance Optimization

**Memory Management:**

```bash
# Monitor memory usage
docker stats domain-seeder

# Adjust batch sizes if needed
echo "DISCOVERY_BATCH_SIZE=500" >> .env
echo "ANALYSIS_BATCH_SIZE=100" >> .env

# Restart service to apply changes
docker restart domain-seeder
```

**Queue Optimization:**

```bash
# Clear failed jobs
pnpm run queue:clear-failed

# Adjust concurrency
echo "MAX_CONCURRENT_DISCOVERIES=5" >> .env
echo "MAX_CONCURRENT_ANALYSIS=3" >> .env
```

### Maintenance Tasks

**Weekly:**

- Review and clean old logs
- Check database performance
- Update discovery source configurations
- Review alert thresholds

**Monthly:**

- Update dependencies
- Review and optimize database indexes
- Performance testing
- Backup configuration files

## Troubleshooting

### Service Startup Issues

**Service won't start:**

```bash
# Check logs
docker logs domain-seeder

# Validate configuration
pnpm run validate-config

# Test database connections
pnpm run test-connections
```

**Common causes:**

- Invalid environment variables
- Database connection failures
- Port conflicts
- Missing dependencies

**Resolution:**

1. Verify `.env` file configuration
2. Check database connectivity
3. Ensure required ports are available
4. Run `pnpm install` to update dependencies

### Database Connection Issues

**ScyllaDB/Cassandra Connection:**

```bash
# Test connection
pnpm run test-db scylla

# Check connectivity
telnet scylla-host 9042

# Review connection logs
docker logs domain-seeder | grep -i scylla
```

**MariaDB Connection:**

```bash
# Test connection
pnpm run test-db mariadb

# Check connectivity
mysql -h mariadb-host -u user -p

# Review connection logs
docker logs domain-seeder | grep -i mariadb
```

**Redis Connection:**

```bash
# Test connection
redis-cli -h redis-host ping

# Check logs
docker logs domain-seeder | grep -i redis
```

### Performance Issues

**High Memory Usage:**

```bash
# Monitor memory
docker stats domain-seeder

# Reduce batch sizes
echo "DISCOVERY_BATCH_SIZE=250" >> .env
echo "ANALYSIS_BATCH_SIZE=50" >> .env

# Restart service
docker restart domain-seeder
```

**Slow Discovery Performance:**

```bash
# Check queue status
pnpm run queue:status

# Monitor external API response times
curl -w "@curl-format.txt" -s -o /dev/null https://api.external-service.com

# Adjust rate limits
echo "DISCOVERY_RATE_LIMIT=50" >> .env
```

### Queue Issues

**Queue Backup:**

```bash
# Check queue sizes
pnpm run queue:status

# Clear failed jobs
pnpm run queue:clear-failed

# Pause processing temporarily
pnpm run queue:pause discovery

# Resume after issue resolution
pnpm run queue:resume discovery
```

**Job Failures:**

```bash
# View failed jobs
pnpm run queue:failed

# Retry specific failed jobs
pnpm run queue:retry-failed --limit 100

# Clear old failed jobs
pnpm run queue:clear-failed --older-than 24h
```

### External API Issues

**Rate Limiting:**

- Monitor API usage in logs
- Adjust rate limits in environment variables
- Implement exponential backoff
- Consider upgrading API plans

**API Key Issues:**

```bash
# Test API connectivity
curl -H "Authorization: Bearer $CLOUDFLARE_API_KEY" \
  https://api.cloudflare.com/client/v4/radar/ranking/top

# Validate all API keys
pnpm run validate-api-keys
```

### Content Analysis Problems

**Analysis Failures:**

```bash
# Check analysis logs
docker logs domain-seeder | grep -i analysis

# Test specific domain
pnpm run analyze --domain example.com --debug

# Review AI provider quotas
pnpm run check-ai-quotas
```

**Low Quality Results:**

- Review domain selection criteria
- Adjust confidence thresholds
- Check content extraction filters
- Validate AI model responses

### Emergency Procedures

**Complete Service Recovery:**

```bash
# Stop service
docker stop domain-seeder

# Clear problematic data
redis-cli flushdb

# Restart with clean state
docker start domain-seeder

# Verify health
curl http://localhost:3004/health
```

**Database Recovery:**

```bash
# Backup current state
pnpm run db:backup

# Reset database schema if needed
pnpm run db:reset

# Restore from backup
pnpm run db:restore latest.backup
```

## Recovery Procedures

### Service Recovery

**Automatic Recovery:**

- Docker restart policies handle basic failures
- Health checks trigger automatic restarts
- Queue system retries failed jobs automatically

**Manual Recovery Steps:**

1. **Assess Impact:** Check logs and metrics
2. **Stop Service:** `docker stop domain-seeder`
3. **Clear State:** Remove corrupted data if needed
4. **Restart:** `docker start domain-seeder`
5. **Verify:** Check health and resume operations

### Database Recovery

**ScyllaDB Recovery:**

```bash
# Check cluster status
nodetool status

# Repair specific keyspace
nodetool repair domainr

# Restore from backup if needed
pnpm run db:restore-scylla backup-file.tar.gz
```

**MariaDB Recovery:**

```bash
# Check database status
mysql -e "SHOW PROCESSLIST;"

# Repair tables if needed
mysqlcheck --repair --all-databases

# Restore from backup
mysql domainr < backup.sql
```

### Queue Recovery

**Clear Stuck Jobs:**

```bash
# Clear all active jobs
pnpm run queue:clear-active

# Remove corrupted job data
redis-cli del "bull:discovery:*"

# Restart queue processing
pnpm run queue:restart
```

**Data Recovery:**

```bash
# Export processed domains
pnpm run export:domains --since yesterday

# Restore from backup
pnpm run import:domains backup-domains.json

# Verify data integrity
pnpm run validate:data
```

### Disaster Recovery

**Complete System Restore:**

1. **Stop all services:** `docker-compose down`
2. **Restore databases:** From latest backups
3. **Restore configuration:** From version control
4. **Start services:** `docker-compose up -d`
5. **Verify operation:** Run health checks

**Backup Strategy:**

- **Daily:** Database snapshots
- **Weekly:** Full system backup
- **Monthly:** Archive old data

## Live Content Analysis

### Implementation Overview

The live content analysis feature integrates real-time web content extraction and AI-powered analysis into the domain discovery pipeline.

**Key Components:**

- **Content Extractor:** Fetches and parses web content
- **Keyword Analyzer:** Extracts relevant keywords and topics
- **Quality Assessor:** Evaluates content quality and relevance
- **AI Integration:** Uses multiple AI providers for description generation

### Content Extraction Pipeline

**Process Flow:**

1. **Domain Discovery:** New domains identified through strategies
2. **Content Fetch:** Retrieve homepage content via HTTP
3. **Content Parse:** Extract text, metadata, and structured data
4. **Keyword Analysis:** Identify relevant keywords and topics
5. **Quality Assessment:** Evaluate content quality and confidence
6. **AI Enhancement:** Generate descriptions using AI providers
7. **Storage:** Store analyzed data in database

### Integration Points

**Scheduler Integration:**

- Triggered during discovery runs
- Processes domains in batches
- Respects rate limits and quotas

**Database Integration:**

- Stores analysis results in dedicated tables
- Links to domain records
- Maintains analysis history

**Monitoring Integration:**

- Tracks analysis success rates
- Monitors AI provider usage
- Alerts on analysis failures

### Performance Characteristics

**Throughput:**

- 50-100 domains per minute (depending on content complexity)
- Batch processing for efficiency
- Parallel analysis workers

**Accuracy:**

- 85%+ content extraction success rate
- 90%+ keyword relevance accuracy
- Quality scores from 0.0 to 1.0

**Resource Usage:**

- ~10MB memory per analysis job
- Network bandwidth for content fetching
- AI API quota consumption

## Development

### Project Structure

```
services/domain-seeder/
├── src/
│   ├── commands/           # CLI commands
│   ├── connectors/         # External source connectors
│   ├── content/           # Content generation
│   ├── database/          # Database models and queries
│   ├── discovery/         # Discovery strategies
│   ├── queue/             # Job queue management
│   ├── utils/             # Utility functions
│   └── index.ts           # Main service entry point
├── tests/                 # Test files
├── docs/                  # Additional documentation
├── logs/                  # Log files
├── .env.example           # Environment template
├── package.json           # Dependencies and scripts
├── tsconfig.json          # TypeScript configuration
├── Dockerfile             # Container build
└── README.md              # This file
```

### Development Setup

1. **Install dependencies:**

   ```bash
   pnpm install
   ```

2. **Configure environment:**

   ```bash
   cp .env.example .env
   # Edit .env with your settings
   ```

3. **Start in development mode:**

   ```bash
   pnpm dev
   ```

4. **Run tests:**
   ```bash
   pnpm test
   pnpm test:watch
   pnpm test:coverage
   ```

### Code Quality

**TypeScript:**

- Strict mode enabled
- All code must be typed
- No `any` types allowed

**ESLint & Prettier:**

```bash
pnpm lint
pnpm lint:fix
pnpm format
```

**Testing:**

- Unit tests for all core functions
- Integration tests for external APIs
- End-to-end tests for CLI commands

### Contributing

1. **Fork and clone the repository**
2. **Create a feature branch:** `git checkout -b feature-name`
3. **Make changes and add tests**
4. **Run quality checks:** `pnpm lint && pnpm test`
5. **Commit changes:** Follow conventional commit format
6. **Push and create pull request**

**Commit Format:**

```
type(scope): description

feat(discovery): add new zone file strategy
fix(queue): resolve memory leak in job processing
docs(readme): update API documentation
```

## Support

### Getting Help

**Documentation:**

- This README covers all basic operations
- Check `/docs` directory for detailed guides
- Review code comments for implementation details

**Troubleshooting:**

- Check the [Troubleshooting](#troubleshooting) section above
- Review service logs for error details
- Verify configuration and connectivity

**Community:**

- Create GitHub issues for bugs or feature requests
- Tag issues appropriately (bug, enhancement, question)
- Provide complete environment details and error logs

**Professional Support:**

- Contact development team for production issues
- Include service logs and reproduction steps
- Specify urgency level and business impact

### Monitoring and Alerts

**Health Monitoring:**

- Service health: `GET /health`
- Detailed status: `GET /status`
- Metrics: `GET /metrics` (Prometheus format)

**Key Metrics to Monitor:**

- Discovery success rates
- Queue processing times
- Memory and CPU usage
- External API response times
- Database connection health

**Recommended Alerts:**

- Service down for >2 minutes
- Error rate >5% over 5 minutes
- Queue backup >1000 jobs
- Memory usage >80% for >10 minutes
- No successful discoveries in 1 hour

### Version History

**Current Version:** 1.0.0

**Recent Changes:**

- Added live content analysis
- Improved error handling and recovery
- Enhanced monitoring and alerting
- Optimized database queries
- Added comprehensive documentation

**Upcoming Features:**

- Advanced AI content generation
- Enhanced discovery strategies
- Real-time domain trending analysis
- Improved performance optimizations
  │ ├── discovery/ # Discovery strategies
  │ ├── enqueuer/ # Domain enqueuing
  │ ├── interfaces/ # TypeScript interfaces
  │ ├── logging/ # Comprehensive logging
  │ ├── monitoring/ # Health checks and metrics
  │ ├── normalization/ # Domain normalization
  │ ├── repositories/ # Database repositories
  │ ├── services/ # Core services
  │ └── validation/ # Input validation
  ├── **tests**/ # Test files
  ├── data/ # Static data files
  ├── logs/ # Log files
  └── docs/ # Additional documentation

````

### Testing

```bash
# Run all tests
pnpm test

# Run specific test suite
pnpm test src/__tests__/discovery/

# Run with coverage
pnpm test --coverage

# Run integration tests
pnpm test:integration

# Run chaos tests
pnpm test:chaos
````

### Code Quality

```bash
# Lint code
pnpm lint

# Format code
pnpm format

# Type check
pnpm type-check

# Build
pnpm build
```

### Contributing

1. Fork the repository
2. Create a feature branch
3. Make changes with tests
4. Run quality checks
5. Submit pull request

### Debugging

```bash
# Enable debug logging
LOG_LEVEL=debug pnpm seeder:run

# Run with Node.js debugger
node --inspect-brk dist/cli.js run

# Memory profiling
node --inspect --heap-prof dist/cli.js run
```

[↑ Back to TOC](#table-of-contents)

---

## PSL Manager

The PSL Manager is a comprehensive system for managing the Public Suffix List (PSL) with automatic updates, integrity verification, version tracking, and rollback capabilities.

### Features

- **Automatic PSL Downloads and Updates** (7-day interval)
- **PSL Integrity Verification** with checksum validation
- **PSL Version Tracking and Rollback** capabilities
- **High-Performance Domain Processing** for eTLD+1 extraction
- **Comprehensive Monitoring and Statistics**

### Usage

```typescript
import PSLManagerImpl from "./normalization/PSLManager";

// Create instance with auto-initialization
const pslManager = new PSLManagerImpl("./cache/psl");

// Extract eTLD+1 from domain
const etld1 = pslManager.getDomain("www.example.com"); // 'example.com'

// Get public suffix
const suffix = pslManager.getPublicSuffix("www.example.co.uk"); // 'co.uk'

// Check if TLD is valid
const isValid = pslManager.isValidTLD("com"); // true
```

### Configuration

Environment variables:

- `PSL_UPDATE_INTERVAL`: Update interval in milliseconds (default: 7 days)
- `PSL_MAX_VERSIONS`: Maximum versions to keep in cache (default: 5)
- `PSL_MIN_SIZE`: Minimum expected PSL size in bytes (default: 50,000)
- `PSL_MAX_SIZE`: Maximum expected PSL size in bytes (default: 2,000,000)

### Integrity Verification

Multiple layers of validation ensure PSL integrity:

1. **Size Validation**: Content must be within expected size range
2. **Structure Validation**: Must contain required section markers
3. **TLD Validation**: Must contain essential TLDs (com, org, net, etc.)
4. **Format Validation**: Rules must follow PSL format specifications
5. **Parsing Validation**: Parsed data must meet minimum requirements

[↑ Back to TOC](#table-of-contents)

---

## Scheduler Integration

The scheduler integration provides automated upgrading of preGenerated content to live analysis with priority management, job queuing, and content versioning.

### Key Components

#### LiveContentScheduler

Manages the upgrade of preGenerated domain content to live analysis based on:

- Content age (minimum age before upgrade)
- Confidence thresholds (low confidence = high priority)
- Time between upgrades (prevents frequent re-upgrades)

#### SchedulerOrchestrator

Coordinates all scheduling activities across the domain seeder system:

- Unified management of discovery scheduling and live content upgrades
- Health monitoring and performance management
- Graceful coordination and configuration management

### Priority System

```typescript
// High Priority Triggers
- Content confidence < 0.4 (lowPriorityConfidenceThreshold)
- Content age > 7 days (maxUpgradeAge)
- Manual high-value domain designation

// Normal Priority
- Standard scheduled upgrades
- Confidence between thresholds

// Low Priority
- High confidence content (>= 0.8)
- Maintenance upgrades
```

### Usage

```typescript
// Initialize the orchestrator
const orchestrator = new SchedulerOrchestrator(
  discoveryEngine,
  sourceConnectors,
  {
    liveContentConfig: {
      enableLiveContentUpgrades: true,
      autoScheduleAfterDiscovery: true,
    },
  }
);

// Start all scheduling services
await orchestrator.start();

// Monitor health
const health = await orchestrator.getHealthStatus();
console.log("System health:", health.overall);
```

### Configuration

LiveContentScheduler configuration:

- `upgradeIntervalMs`: How often to check for upgrades (default: 5 minutes)
- `batchSize`: Domains per batch (default: 20)
- `maxConcurrentUpgrades`: Max concurrent upgrades (default: 5)
- `highPriorityConfidenceThreshold`: High confidence threshold (default: 0.8)
- `lowPriorityConfidenceThreshold`: Low confidence threshold (default: 0.4)

[↑ Back to TOC](#table-of-contents)

---

## Support

For issues and questions:

1. Check this documentation
2. Review troubleshooting section
3. Check logs with correlation IDs
4. Create issue with reproduction steps

## License

[Your License Here]
