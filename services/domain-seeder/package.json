{"name": "domain-seeder", "version": "1.0.0", "description": "Domain discovery and seeding service for finding new domains absent from our database", "main": "dist/index.js", "scripts": {"start": "tsx src/index.ts", "dev": "tsx --watch src/index.ts", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "build": "tsc --build", "build:clean": "tsc --build --clean", "example:phase1": "tsx src/scripts/examplePhaseOneContentCreator.ts", "seeder:run": "tsx src/commands/run.ts", "seeder:top": "tsx src/commands/top.ts", "seeder:status": "tsx src/commands/status.ts", "seeder:backfill": "tsx src/commands/backfill.ts", "cli": "tsx src/cli.ts", "describe": "tsx src/commands/describe.ts"}, "dependencies": {"bloom-filters": "^3.0.0", "cheerio": "^1.0.0-rc.12", "commander": "^11.0.0", "csv-parse": "^5.6.0", "dotenv": "^16.3.1", "https-proxy-agent": "^7.0.6", "mysql2": "^3.14.3", "node-libcurl": "^4.1.0", "psl": "^1.9.0", "punycode": "^2.3.0", "shared": "workspace:*", "ultimate-express": "^2.0.9", "yauzl": "^3.0.0", "zod": "^4.0.0"}, "devDependencies": {"@types/cheerio": "^1.0.0", "@types/express": "^4.17.17", "@types/node": "^22.0.0", "@types/supertest": "^2.0.12", "@types/yauzl": "^2.10.0", "@vitest/coverage-v8": "^2.0.5", "@vitest/ui": "^2.0.5", "fast-check": "^4.2.0", "supertest": "^6.3.3", "tsx": "^4.0.0", "typescript": "^5.0.0", "vitest": "^2.0.5"}, "engines": {"node": ">=22.0.0", "pnpm": ">=10.15.0"}, "packageManager": "pnpm@10.15.0"}