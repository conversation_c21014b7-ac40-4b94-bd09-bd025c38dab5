# Multi-stage build for domain-seeder service with security hardening
FROM node:22-bookworm-slim AS base

# Install security updates and required packages
RUN apt-get update && apt-get upgrade -y && \
    apt-get install -y --no-install-recommends \
        dumb-init \
        ca-certificates \
        tzdata \
        curl && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Create non-root user with specific UID/GID for consistency
RUN groupadd -g 1000 nodejs && \
    useradd -r -u 1000 -g nodejs -d /home/<USER>/bin/bash seeder && \
    # Create app directory with proper permissions
    mkdir -p /app && \
    chown -R seeder:nodejs /app

# Set working directory
WORKDIR /app

# Copy package files for dependency installation
COPY --chown=seeder:nodejs package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY --chown=seeder:nodejs shared/package.json ./shared/
COPY --chown=seeder:nodejs services/domain-seeder/package.json ./services/domain-seeder/

# Install pnpm and production dependencies only
RUN npm install -g pnpm@10.15.0 && \
    # Clear npm cache to reduce image size
    npm cache clean --force

# Development stage
FROM base AS development

# Install all dependencies including dev dependencies
RUN pnpm install --frozen-lockfile

# Copy source code
COPY --chown=seeder:nodejs . .

# Switch to non-root user
USER seeder

# Expose port
EXPOSE 3004

# Development command with hot reload
CMD ["dumb-init", "pnpm", "--filter", "domain-seeder", "dev"]

# Production build stage
FROM base AS builder

# Install all dependencies for building
RUN pnpm install --frozen-lockfile

# Copy source code
COPY --chown=seeder:nodejs . .

# Build the application
RUN pnpm --filter domain-seeder build && \
    # Remove dev dependencies after build
    pnpm install --frozen-lockfile --prod && \
    # Clean up build artifacts and caches
    pnpm store prune && \
    rm -rf ~/.pnpm-store && \
    rm -rf node_modules/.cache

# Production stage with minimal footprint
FROM node:22-bookworm-slim AS production

# Security hardening
RUN apt-get update && apt-get upgrade -y && \
    apt-get install -y --no-install-recommends \
        dumb-init \
        ca-certificates \
        tzdata && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN groupadd -g 1000 nodejs && \
    useradd -r -u 1000 -g nodejs -d /home/<USER>/bin/bash seeder && \
    mkdir -p /app && \
    chown -R seeder:nodejs /app

# Set working directory
WORKDIR /app

# Copy only production dependencies and built application
COPY --from=builder --chown=seeder:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=seeder:nodejs /app/shared ./shared
COPY --from=builder --chown=seeder:nodejs /app/services/domain-seeder/dist ./services/domain-seeder/dist
COPY --from=builder --chown=seeder:nodejs /app/services/domain-seeder/package.json ./services/domain-seeder/
COPY --from=builder --chown=seeder:nodejs /app/services/domain-seeder/src/health.ts ./services/domain-seeder/src/

# Set production environment variables
ENV NODE_ENV=production \
    PORT=3004 \
    SERVICE_NAME=domain-seeder \
    # Security settings
    NODE_OPTIONS="--max-old-space-size=1024 --enable-source-maps" \
    # Disable npm update notifications
    NO_UPDATE_NOTIFIER=1 \
    # Set timezone
    TZ=UTC

# Create logs directory
RUN mkdir -p /app/logs && \
    chown -R seeder:nodejs /app/logs

# Health check with proper timeout and retries
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:${PORT}/health/live || exit 1

# Switch to non-root user
USER seeder

# Expose port
EXPOSE 3004

# Use dumb-init to handle signals properly and run the application
CMD ["dumb-init", "node", "--enable-source-maps", "services/domain-seeder/dist/index.js"]

# Add labels for better container management
LABEL maintainer="Domain Ranking Team" \
      service="domain-seeder" \
      version="1.0.0" \
      description="Domain discovery and seeding service" \
      org.opencontainers.image.source="https://github.com/your-org/domain-ranking" \
      org.opencontainers.image.title="Domain Seeder Service" \
      org.opencontainers.image.description="Discovers new domains from external sources" \
      org.opencontainers.image.version="1.0.0"
