# Cloudflare Radar Domain Discovery Implementation Guide

## Overview
Cloudflare Radar provides multiple methods for discovering domains, including **trending domains** (surge in interest) and **popular domains** (consistent high traffic). The key for domain discovery is to focus on **TRENDING** domains which represent domains gaining sudden interest.

## Available Domain Discovery Methods

### 1. Trending Domains (Primary Discovery Source)
**Purpose**: Identify domains with sudden surge in interest - these are likely to be newer or recently popular domains.

```bash
# Global trending domains
GET /radar/ranking/top?rankingType=TRENDING&limit=100

# Location-specific trending domains
GET /radar/ranking/top?rankingType=TRENDING&location=US&limit=50

# Multiple locations
GET /radar/ranking/top?rankingType=TRENDING&location=US,GB,DE&limit=25
```

**Key Parameters:**
- `rankingType`: Must be "TRENDING" for discovery
- `location`: ISO 3166-1 alpha-2 country codes
- `limit`: Maximum results (default 10, max varies)

### 2. Popular Domains (Baseline Comparison)
**Purpose**: Get consistently high-traffic domains for comparison with trending.

```bash
GET /radar/ranking/top?rankingType=POPULAR&limit=100
```

### 3. Domain Datasets (Bulk Discovery)
**Purpose**: Access larger domain lists (up to 1 million domains).

```bash
# List available datasets
GET /radar/datasets

# Download specific dataset (e.g., top 1M)
GET /radar/datasets/ranking_top_1000000
```

**Available Buckets:**
- `ranking_top_1000000` - Top 1 million domains (unordered)
- `ranking_top_500000` - Top 500k domains
- `ranking_top_200000` - Top 200k domains
- `ranking_top_100000` - Top 100k domains
- `ranking_top_50000` - Top 50k domains

### 4. Domain Details
**Purpose**: Get specific information about discovered domains.

```bash
GET /radar/ranking/domain/{domain}
```

## Discovery Strategy Implementation

### Phase 1: Trending Domain Discovery
1. Fetch global trending domains (top 100)
2. Fetch trending domains from key markets (US, GB, DE, JP, etc.)
3. Compare with existing database to find NEW domains
4. Mark domains with high confidence scores

### Phase 2: Geographic Discovery
1. Iterate through different country codes
2. Find regionally trending domains
3. Identify domains popular in specific regions but not globally

### Phase 3: Bulk Dataset Analysis
1. Download larger datasets (100k, 500k, 1M)
2. Compare sequential datasets to find newly appearing domains
3. Identify domains moving up rapidly in rankings

### Phase 4: Temporal Analysis
1. Store trending snapshots daily
2. Compare day-over-day changes
3. Identify domains that appear in trending but weren't there yesterday

## Implementation Priority

1. **TRENDING domains** - Highest value for discovery
2. **Geographic trending** - Regional discoveries
3. **Dataset comparison** - Find domains new to buckets
4. **Popular baseline** - Use for filtering already-known domains

## Response Formats

### Trending Domains Response:
```json
{
  "success": true,
  "result": {
    "trending": [
      {
        "rank": 1,
        "domain": "example.com",
        "categories": [
          {
            "id": 123,
            "name": "Technology",
            "superCategoryId": 26
          }
        ]
      }
    ],
    "meta": {
      "dateRange": [
        {
          "startTime": "2025-09-18T00:00:00Z",
          "endTime": "2025-09-19T00:00:00Z"
        }
      ]
    }
  }
}
```

Note: Response uses `"trending"` key, not `"top_0"` when rankingType=TRENDING

## Update Frequencies
- **Top 100 domains**: Updated daily
- **Ranking buckets**: Updated weekly
- **Trending domains**: Real-time based on traffic patterns

## Best Practices for Discovery

1. **Focus on TRENDING**: These represent domains with sudden interest
2. **Use multiple locations**: Different regions surface different trending domains
3. **Compare temporal snapshots**: Daily comparisons reveal truly new domains
4. **Filter known domains**: Check against existing database
5. **Combine strategies**: Use multiple discovery methods for comprehensive coverage

## Rate Limits
- Be mindful of API rate limits
- Implement exponential backoff
- Cache responses appropriately
- Use batch operations where possible