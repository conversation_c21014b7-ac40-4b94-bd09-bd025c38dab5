#!/usr/bin/env tsx

/**
 * Test script to verify REAL domain discovery with sources that contain NEW domains
 */

import { config } from 'dotenv';
import { DiscoveryOperations } from './src/core/DiscoveryOperations';
import { DatabaseManager } from '@shared';
import { logger } from '@shared';
import CommonCrawlConnector from './src/connectors/CommonCrawlConnector';
import TrancoConnector from './src/connectors/TrancoConnector';

// Load environment variables
config();

const testLogger = logger.getLogger('test-real-discovery');

async function testRealDomainDiscovery() {
	try {
		testLogger.info('Starting REAL domain discovery test...');

		// Test 1: Common Crawl - should have many unique domains
		testLogger.info('=== Testing Common Crawl for unique domains ===');
		await testCommonCrawl();

		// Test 2: Tranco with larger list
		testLogger.info('=== Testing Tranco with more domains ===');
		await testTrancoLarger();

		// Test 3: Discovery Operations with multiple sources
		testLogger.info('=== Testing Discovery Operations ===');
		await testDiscoveryOperations();

		testLogger.info('Real domain discovery test completed!');

	} catch (error) {
		testLogger.error('Real domain discovery test failed:', error);
		process.exit(1);
	}
}

async function testCommonCrawl() {
	try {
		const connector = new CommonCrawlConnector({});

		// Test health check
		const isHealthy = await connector.healthCheck();
		testLogger.info('Common Crawl health check:', { isHealthy });

		if (!isHealthy) {
			testLogger.warn('Common Crawl health check failed, skipping...');
			return;
		}

		// Test fetching domains
		testLogger.info('Fetching domains from Common Crawl...');
		const domains = [];
		let count = 0;

		for await (const domain of connector.fetchDomains({
			limit: 10,
			strategy: 'differential'
		})) {
			domains.push(domain);
			count++;
			testLogger.info(`Common Crawl domain ${count}:`, {
				domain: domain.domain,
				source: domain.source,
				metadata: domain.metadata
			});

			if (count >= 10) break;
		}

		testLogger.info('Common Crawl results:', {
			totalDomains: domains.length,
			uniqueDomains: [...new Set(domains.map(d => d.domain))].length
		});

	} catch (error) {
		testLogger.error('Common Crawl test failed:', error);
	}
}

async function testTrancoLarger() {
	try {
		const connector = new TrancoConnector({});

		// Test health check
		const isHealthy = await connector.healthCheck();
		testLogger.info('Tranco health check:', { isHealthy });

		if (!isHealthy) {
			testLogger.warn('Tranco health check failed, skipping...');
			return;
		}

		// Test fetching MORE domains (beyond top 10)
		testLogger.info('Fetching domains from Tranco (offset 1000)...');
		const domains = [];
		let count = 0;

		for await (const domain of connector.fetchDomains({
			limit: 20,
			offset: 1000, // Start from rank 1000+ to get less common domains
			strategy: 'long-tail'
		})) {
			domains.push(domain);
			count++;
			testLogger.info(`Tranco domain ${count}:`, {
				domain: domain.domain,
				rank: domain.rank,
				source: domain.source
			});

			if (count >= 20) break;
		}

		testLogger.info('Tranco results:', {
			totalDomains: domains.length,
			rankRange: domains.length > 0 ? `${Math.min(...domains.map(d => d.rank || 0))}-${Math.max(...domains.map(d => d.rank || 0))}` : 'none'
		});

	} catch (error) {
		testLogger.error('Tranco test failed:', error);
	}
}

async function testDiscoveryOperations() {
	try {
		const dbManager = new DatabaseManager();
		await dbManager.initialize();

		const discoveryOps = new DiscoveryOperations(dbManager);

		// Test discovery with sources that should have unique domains
		testLogger.info('Testing discovery with Common Crawl...');

		const discoveryResult = await discoveryOps.triggerDiscoveryRun({
			strategies: ['long-tail'], // Look for less common domains
			sources: ['commoncrawl'], // Use Common Crawl
			limit: 10,
			dryRun: true
		});

		testLogger.info('Discovery run results:', discoveryResult);

		// Test top sources with Common Crawl
		testLogger.info('Testing top sources with Common Crawl...');

		const topSourcesResult = await discoveryOps.triggerTopSourcesRun({
			sources: ['commoncrawl'],
			limit: 10,
			aggregate: false
		});

		testLogger.info('Top sources results:', topSourcesResult);

		await dbManager.close();

	} catch (error) {
		testLogger.error('Discovery operations test failed:', error);
	}
}

// Run the test
testRealDomainDiscovery().catch(console.error);