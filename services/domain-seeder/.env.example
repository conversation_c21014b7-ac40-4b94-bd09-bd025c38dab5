# Domain Seeder Service Configuration

# =================================
# Service Configuration
# =================================
NODE_ENV=development
SERVICE_NAME=domain-seeder
SERVICE_VERSION=1.0.0
PORT=3004
TIMEZONE=UTC
GRACEFUL_SHUTDOWN_TIMEOUT_MS=30000

# =================================
# Database Configuration
# =================================

# Scylla/Cassandra Database
SCYLLA_CONTACT_POINTS=127.0.0.1
SCYLLA_LOCAL_DC=datacenter1
SCYLLA_KEYSPACE=domain_ranking
SCYLLA_USERNAME=
SCYLLA_PASSWORD=
SCYLLA_CONNECT_TIMEOUT=30000
SCYLLA_REQUEST_TIMEOUT=12000

# MariaDB/MySQL Database
MARIA_HOST=localhost
MARIA_PORT=3306
MARIA_USER=root
MARIA_PASSWORD=
MARIA_DATABASE=domain_ranking
MARIA_CONNECTION_LIMIT=10
MARIA_ACQUIRE_TIMEOUT=60000
MARIA_TIMEOUT=60000

# Manticore Search
MANTICORE_HOST=localhost
MANTICORE_PORT=9308
MANTICORE_MAX_CONNECTIONS=10
MANTICORE_TIMEOUT=30000

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_MAX_RETRIES=3
REDIS_RETRY_DELAY=100
REDIS_CONNECT_TIMEOUT=10000
REDIS_COMMAND_TIMEOUT=5000

# =================================
# Domain Discovery Configuration
# =================================
MAX_NEW_PER_DAY=500000
ENQUEUE_BATCH=1000
ENQUEUE_INTERVAL_MS=1000
NEW_QUEUE_MAX_DEPTH=200000
DB_CHECK_BATCH=5000
BLOOM_FP_RATE=0.01
PSL_UPDATE_INTERVAL_DAYS=7
DISCOVERY_TIMEOUT_MS=300000
MAX_CONCURRENT_SOURCES=3
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=1000

# =================================
# Content Generation Configuration
# =================================
CONTENT_GENERATION_ENABLED=true
CONTENT_GENERATION_MODE=preGenerated

# =================================
# Monitoring & Health Configuration
# =================================
METRICS_ENABLED=true
METRICS_PORT=9090
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_PORT=8080
ALERTING_ENABLED=true

# =================================
# Logging Configuration
# =================================
LOG_LEVEL=info
LOG_FORMAT=json
LOG_DIR=./logs
MAX_LOG_FILES=10
MAX_LOG_SIZE=100MB

# =================================
# Security Configuration
# =================================
ENCRYPTION_KEY=
CREDENTIAL_ROTATION_INTERVAL_DAYS=90
CREDENTIAL_EXPIRY_WARNING_DAYS=7
SECRETS_PATH=./secrets
TLS_ENABLED=false
TLS_CERT_PATH=
TLS_KEY_PATH=

# =================================
# Data Source API Keys & Configuration
# =================================

# Cloudflare Radar API
CLOUDFLARE_API_KEY=your_cloudflare_api_key_here
RADAR_ENABLED=true
RADAR_PRIORITY=2
RADAR_TIMEOUT=30000
RADAR_MAX_RETRIES=3
RADAR_RATE_LIMIT=300

# Cisco Umbrella API
UMBRELLA_API_KEY=your_umbrella_api_key_here
UMBRELLA_TOS_ACCEPTED=false
UMBRELLA_ENABLED=true
UMBRELLA_PRIORITY=3
UMBRELLA_TIMEOUT=30000
UMBRELLA_MAX_RETRIES=3
UMBRELLA_RATE_LIMIT=100

# Tranco List (No API key required)
TRANCO_ENABLED=true
TRANCO_PRIORITY=1
TRANCO_TIMEOUT=30000
TRANCO_MAX_RETRIES=3
TRANCO_RATE_LIMIT=60

# CZDS (Centralized Zone Data Service)
CZDS_ENABLED=true
CZDS_USERNAME=your_czds_username_here
CZDS_PASSWORD=your_czds_password_here
CZDS_PRIORITY=4
CZDS_TIMEOUT=60000
CZDS_MAX_RETRIES=3
CZDS_DOWNLOAD_PATH=/tmp/czds

# Passive Total/RiskIQ (now part of Microsoft)
PIR_ENABLED=true
PIR_API_KEY=your_pir_api_key_here
PIR_PRIORITY=5
PIR_TIMEOUT=30000
PIR_MAX_RETRIES=3
PIR_RATE_LIMIT=60

# Common Crawl
COMMON_CRAWL_ENABLED=true
COMMON_CRAWL_PRIORITY=6
COMMON_CRAWL_TIMEOUT=60000
COMMON_CRAWL_MAX_RETRIES=3
COMMON_CRAWL_INDEX_URL=https://index.commoncrawl.org/
COMMON_CRAWL_BATCH_SIZE=10000

# Rapid7 Sonar
SONAR_ENABLED=true
SONAR_API_KEY=your_rapid7_api_key_here
SONAR_PRIORITY=7
SONAR_TIMEOUT=30000
SONAR_MAX_RETRIES=3
SONAR_RATE_LIMIT=100

# =================================
# AI Content Generation APIs
# =================================

# OpenAI Configuration
OPENAI_ENABLED=false
OPENAI_API_KEYS=your_openai_api_key_here
OPENAI_PRIORITY=1
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=4000
OPENAI_TEMPERATURE=0.7
OPENAI_RATE_LIMIT=60

# Claude API Configuration
CLAUDE_ENABLED=false
CLAUDE_API_KEYS=your_claude_api_key_here
CLAUDE_PRIORITY=2
CLAUDE_MODEL=claude-3-sonnet-20240229
CLAUDE_MAX_TOKENS=4000
CLAUDE_TEMPERATURE=0.7
CLAUDE_RATE_LIMIT=50

# Google Gemini Configuration
GEMINI_ENABLED=false
GEMINI_API_KEYS=your_gemini_api_key_here
GEMINI_PRIORITY=3
GEMINI_MODEL=gemini-pro
GEMINI_MAX_TOKENS=4000
GEMINI_TEMPERATURE=0.7
GEMINI_RATE_LIMIT=60

# =================================
# Quick Start Configuration
# =================================
# For basic functionality, you need at minimum:
# 1. CLOUDFLARE_API_KEY for Radar connector
# 2. Database connections (Redis is required, others optional)
# 3. Basic service configuration above
#
# Optional but recommended:
# - UMBRELLA_API_KEY for additional domain sources
# - AI API keys if you want AI-generated content
# - Monitoring configuration for production use