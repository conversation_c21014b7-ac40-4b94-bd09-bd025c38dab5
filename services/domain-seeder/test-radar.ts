#!/usr/bin/env tsx

/**
 * Test script to verify Cloudflare Radar connector functionality
 */

import { config } from 'dotenv';
import { DiscoveryOperations } from './src/core/DiscoveryOperations';
import { DatabaseManager } from '@shared';
import { logger } from '@shared';
import RadarConnector from './src/connectors/RadarConnector';

// Load environment variables
config();

const testLogger = logger.getLogger('test-radar');

async function testRadarConnector() {
	try {
		testLogger.info('Starting Radar connector test...');

		// Check if API key is configured
		const apiKey = process.env.CLOUDFLARE_API_KEY;
		if (!apiKey) {
			testLogger.error('CLOUDFLARE_API_KEY not configured in .env file');
			testLogger.info('Please add your Cloudflare API key to the .env file:');
			testLogger.info('CLOUDFLARE_API_KEY=your_actual_key_here');
			process.exit(1);
		}

		testLogger.info('API key configured, testing Radar connector...');

		// Test direct connector
		const radar = new RadarConnector({});

		// Test health check first
		testLogger.info('Testing Radar health check...');
		const isHealthy = await radar.healthCheck();
		testLogger.info('Radar health check result:', { isHealthy });

		if (!isHealthy) {
			testLogger.error('Radar health check failed - API key might be invalid');
			return;
		}

		// Test fetching a small number of domains
		testLogger.info('Testing domain fetching (limit: 5)...');
		const domains = [];
		let count = 0;

		for await (const domain of radar.fetchDomains({
			limit: 5,
			strategy: 'differential'
		})) {
			domains.push(domain);
			count++;
			testLogger.info(`Found domain ${count}:`, {
				domain: domain.domain,
				rank: domain.rank,
				source: domain.source
			});

			if (count >= 5) break; // Safety limit
		}

		testLogger.info('Radar connector test results:', {
			totalDomains: domains.length,
			domains: domains.map(d => ({ domain: d.domain, rank: d.rank }))
		});

		// Now test through DiscoveryOperations
		testLogger.info('Testing through DiscoveryOperations...');

		const dbManager = new DatabaseManager();
		await dbManager.initialize();

		const discoveryOps = new DiscoveryOperations(dbManager);

		const discoveryResult = await discoveryOps.triggerTopSourcesRun({
			sources: ['radar'],
			limit: 3,
			aggregate: false
		});

		testLogger.info('Discovery operations test result:', discoveryResult);

		await dbManager.close();
		testLogger.info('Radar connector test completed successfully!');

	} catch (error) {
		testLogger.error('Radar connector test failed:', error);

		if (error.message?.includes('401') || error.message?.includes('403')) {
			testLogger.error('Authentication error - please check your Cloudflare API key');
		}

		process.exit(1);
	}
}

// Run the test
testRadarConnector().catch(console.error);