/* eslint-disable no-console */
/**
 * Security Headers Configuration for Admin Panel
 * Implements comprehensive security headers and policies
 */

const securityHeaders =
{
	// Strict Transport Security
	'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',

	// Content Security Policy
	'Content-Security-Policy': [
		"default-src 'self'",
		"script-src 'self' 'unsafe-inline' 'unsafe-eval'",
		"style-src 'self' 'unsafe-inline'",
		"img-src 'self' data: https:",
		"font-src 'self' data:",
		"connect-src 'self' ws: wss:",
		"frame-ancestors 'none'",
		"base-uri 'self'",
		"form-action 'self'",
	].join('; '),

	// X-Frame-Options
	'X-Frame-Options': 'DENY',

	// X-Content-Type-Options
	'X-Content-Type-Options': 'nosniff',

	// X-XSS-Protection
	'X-XSS-Protection': '1; mode=block',

	// Referrer Policy
	'Referrer-Policy': 'strict-origin-when-cross-origin',

	// Permissions Policy
	'Permissions-Policy': [
		'geolocation=()',
		'microphone=()',
		'camera=()',
		'payment=()',
		'usb=()',
		'magnetometer=()',
		'gyroscope=()',
		'speaker=()',
	].join(', '),

	// Cross-Origin Policies
	'Cross-Origin-Embedder-Policy': 'require-corp',
	'Cross-Origin-Opener-Policy': 'same-origin',
	'Cross-Origin-Resource-Policy': 'same-origin',

	// Cache Control for sensitive pages
	'Cache-Control': 'no-cache, no-store, must-revalidate, private',
	Pragma: 'no-cache',
	Expires: '0',
};

// Environment-specific CSP adjustments
const getEnvironmentCSP = (environment) =>
{
	const baseCSP = securityHeaders['Content-Security-Policy'];

	switch (environment)
	{
		case 'development':
			return baseCSP.replace(
				"script-src 'self' 'unsafe-inline' 'unsafe-eval'",
				"script-src 'self' 'unsafe-inline' 'unsafe-eval' localhost:* 127.0.0.1:*"
			);

		case 'staging':
			return baseCSP.replace(
				"connect-src 'self' ws: wss:",
				"connect-src 'self' ws: wss: *.staging.yourdomain.com"
			);

		case 'production':
		default:
			return baseCSP;
	}
};

// Security middleware factory
const createSecurityMiddleware = (environment = 'production') =>
{
	const headers = {
		...securityHeaders,
		'Content-Security-Policy': getEnvironmentCSP(environment),
	};

	return (req, res, next) =>
	{
		// Apply security headers
		Object.entries(headers).forEach(([key, value]) =>
		{
			res.setHeader(key, value);
		});

		// Remove server information
		res.removeHeader('X-Powered-By');
		res.removeHeader('Server');

		// Add custom security headers
		res.setHeader('X-Admin-Panel-Version', process.env.VERSION || 'unknown');
		res.setHeader('X-Request-ID', req.headers['x-request-id'] || generateRequestId());

		next();
	};
};

// Rate limiting configuration
const rateLimitConfig =
{
	// General API rate limiting
	api: {
		windowMs: 15 * 60 * 1000, // 15 minutes
		max: 100, // limit each IP to 100 requests per windowMs
		message: 'Too many requests from this IP, please try again later.',
		standardHeaders: true,
		legacyHeaders: false,
	},

	// Authentication rate limiting
	auth: {
		windowMs: 15 * 60 * 1000, // 15 minutes
		max: 5, // limit each IP to 5 login attempts per windowMs
		message: 'Too many login attempts, please try again later.',
		standardHeaders: true,
		legacyHeaders: false,
		skipSuccessfulRequests: true,
	},

	// Strict rate limiting for sensitive operations
	sensitive: {
		windowMs: 60 * 60 * 1000, // 1 hour
		max: 10, // limit each IP to 10 requests per hour
		message: 'Rate limit exceeded for sensitive operations.',
		standardHeaders: true,
		legacyHeaders: false,
	},
};

// Input validation rules
const validationRules =
{
	// User input validation
	username: {
		minLength: 3,
		maxLength: 50,
		pattern: /^[a-zA-Z0-9_-]+$/,
		required: true,
	},

	email: {
		maxLength: 255,
		pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
		required: true,
	},

	password: {
		minLength: 12,
		maxLength: 128,
		requireUppercase: true,
		requireLowercase: true,
		requireNumbers: true,
		requireSpecialChars: true,
		required: true,
	},

	// API parameter validation
	id: {
		pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
		required: true,
	},

	limit: {
		min: 1,
		max: 100,
		type: 'integer',
	},

	offset: {
		min: 0,
		type: 'integer',
	},
};

// Security event logging
const securityEvents =
{
	FAILED_LOGIN: 'failed_login',
	SUCCESSFUL_LOGIN: 'successful_login',
	LOGOUT: 'logout',
	RATE_LIMIT_EXCEEDED: 'rate_limit_exceeded',
	INVALID_TOKEN: 'invalid_token',
	SUSPICIOUS_ACTIVITY: 'suspicious_activity',
	PRIVILEGE_ESCALATION: 'privilege_escalation',
	DATA_ACCESS: 'data_access',
	CONFIGURATION_CHANGE: 'configuration_change',
};

// Utility functions
const generateRequestId = () => Math.random().toString(36).substring(2, 15)
         + Math.random().toString(36).substring(2, 15);

const logSecurityEvent = (event, details = {}) =>
{
	const logEntry =
	{
		timestamp: new Date().toISOString(),
		event,
		details,
		ip: details.ip || 'unknown',
		userAgent: details.userAgent || 'unknown',
		userId: details.userId || null,
	};

	// In production, this would send to a security monitoring system
	console.log('[SECURITY]', JSON.stringify(logEntry));
};

// Export configuration
module.exports =
{
	securityHeaders,
	createSecurityMiddleware,
	rateLimitConfig,
	validationRules,
	securityEvents,
	logSecurityEvent,
	generateRequestId,
};
