import { getIronSession } from 'iron-session';
import { NextRequest, NextResponse } from 'next/server';

import { sessionOptions } from '@/lib/auth/config';
import type { IronSessionDataType } from '@/lib/auth';
import { authLogger } from '@/lib/logger';

const publicPaths = ['/login', '/api/auth/login', '/api/auth/logout'];

async function middleware(request: NextRequest)
{
	const { pathname } = request.nextUrl;

	// Skip middleware for static assets
	if (pathname.startsWith('/_next/') || pathname.startsWith('/favicon.ico'))
	{
		return NextResponse.next();
	}

	// Allow public paths
	if (publicPaths.some(path => pathname.startsWith(path)))
	{
		return NextResponse.next();
	}

	try
	{
		// Get session from iron-session
		const response = new NextResponse();
		const session = await getIronSession<IronSessionDataType>(request, response, sessionOptions);

		// Check if user is authenticated
		if (!session.isLoggedIn || !session.sessionId)
		{
			// For API routes, return 401
			if (pathname.startsWith('/api/'))
			{
				return NextResponse.json(
					{ error: 'Authentication required' },
					{ status: 401 },
				);
			}

			// For pages, redirect to login
			return NextResponse.redirect(new URL('/login', request.url));
		}

		// Add user info to request headers for API routes
		if (pathname.startsWith('/api/'))
		{
			const requestHeaders = new Headers(request.headers);
			requestHeaders.set('x-user-id', session.username || '');
			requestHeaders.set('x-user-role', session.role || '');
			requestHeaders.set('x-user-permissions', JSON.stringify(session.permissions || []));

			return NextResponse.next({
				request: {
					headers: requestHeaders,
				},
			});
		}

		return NextResponse.next();
	}
	catch (error)
	{
		authLogger.error({ error: error instanceof Error ? error.message : String(error), pathname }, 'Middleware authentication error');

		// For API routes, return 500
		if (pathname.startsWith('/api/'))
		{
			return NextResponse.json(
				{ error: 'Authentication error' },
				{ status: 500 },
			);
		}

		// For pages, redirect to login
		return NextResponse.redirect(new URL('/login', request.url));
	}
}

const config = {
	matcher: [
		/*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - api routes (handled separately)
     */
		'/((?!_next|favicon.ico|api).*)',
	],
};

export { middleware, config };
