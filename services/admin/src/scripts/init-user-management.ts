#!/usr/bin/env tsx

/**
 * Initialize User Management Database Schema
 *
 * This script sets up the database tables and initial data for the user management system.
 * Run this script after setting up the basic admin panel to enable user management features.
 */

import { readFileSync } from 'node:fs';
import { join } from 'node:path';
import { randomUUID } from 'node:crypto';

import { DatabaseManager, logger } from '@shared';
import { hashPassword } from '@/utils/passwordValidation';

const scriptLogger = logger.getLogger('UserManagementInit');

async function initializeUserManagement()
{
	const db = await DatabaseManager.getInstance();

	try
	{
		console.log('🚀 Initializing User Management Database Schema...');

			// Read and execute the schema
		const schemaPath = join(process.cwd(), 'src', 'lib', 'database', 'user-management-schema.sql');
		const schemaSql = readFileSync(schemaPath, 'utf8');

		// Split by semicolon and execute each statement
		const statements = schemaSql
			.split(';')
			.map(stmt => stmt.trim())
			.filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

		console.log(`📝 Executing ${statements.length} SQL statements...`);

		for (const statement of statements)
		{
			try
			{
				await db.mariadb.query(statement);
			}
			catch (error: unknown)
			{
				// Ignore "table already exists" errors
				const message = error instanceof Error ? error.message : String(error);
				if (!message.includes('already exists'))
				{
					scriptLogger.error({ error: message, statement: statement.substring(0, 100) }, 'Error executing statement');
					throw error;
				}
			}
		}

		console.log('✅ Database schema created successfully');

		// Check if we need to create a default super admin user
		const existingUsers = await db.mariadb.query('SELECT COUNT(*) as count FROM admin_users');
		const userCount = (existingUsers as Array<{ count: number }>)[0]?.count || 0;

		if (userCount === 0)
		{
			console.log('👤 Creating default super admin user...');

			const defaultPassword = 'AdminPassword123!';
			const hashedPassword = await hashPassword(defaultPassword);
			const userId = randomUUID();

			const superAdminPermissions =
			[
				'users.view', 'users.create', 'users.edit', 'users.delete', 'users.manage_permissions',
				'services.view', 'services.restart', 'services.configure',
				'domains.view', 'domains.edit', 'domains.delete', 'domains.bulk_operations',
				'crawl.view', 'crawl.create', 'crawl.manage', 'crawl.configure',
				'seeder.view', 'seeder.manage', 'seeder.configure',
				'analytics.view', 'analytics.export',
				'config.view', 'config.edit', 'config.deploy',
				'logs.view', 'logs.export',
				'ai.view', 'ai.configure', 'ai.manage',
			];

			await db.mariadb.query(`
				INSERT INTO admin_users (
					id, username, password_hash, role, permissions,
					created_at, password_last_changed, must_change_password,
					is_active, full_name
				) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
			`, [
				userId,
				'admin',
				hashedPassword,
				'super_admin',
				JSON.stringify(superAdminPermissions),
				new Date(),
				new Date(),
				1, // Must change password on first login
				1, // Active
				'Default Administrator',
			]);

			console.log('✅ Default super admin user created');
			console.log('📋 Login credentials:');
			console.log('   Username: admin');
			console.log('   Password: AdminPassword123!');
			console.log('   ⚠️  Please change this password immediately after first login!');
		}
		else
		{
			console.log(`ℹ️  Found ${userCount} existing users, skipping default user creation`);
		}

		// Create some sample permission templates if they don't exist
		const existingTemplates = await db.mariadb.query('SELECT COUNT(*) as count FROM permission_templates');
		const templateCount = (existingTemplates as Array<{ count: number }>)[0]?.count || 0;

		if (templateCount === 0)
		{
			console.log('📋 Creating default permission templates...');

			const templates =
			[
				{
					name: 'Content Manager',
					description: 'Manage domains and crawling operations',
					permissions: [
						'domains.view', 'domains.edit', 'domains.bulk_operations',
						'crawl.view', 'crawl.create', 'crawl.manage',
						'seeder.view', 'seeder.manage',
						'analytics.view',
					],
				}, {
					name: 'System Monitor',
					description: 'Monitor system health and performance',
					permissions: [
						'services.view',
						'analytics.view', 'analytics.export',
						'logs.view',
						'database.view',
					],
				}, {
					name: 'AI Manager',
					description: 'Manage AI services and content generation',
					permissions: [
						'ai.view', 'ai.configure', 'ai.manage',
						'seeder.view', 'seeder.manage',
						'analytics.view',
					],
				},
			];

			const adminUserId = await db.mariadb.query('SELECT id FROM admin_users WHERE role = "super_admin" LIMIT 1');
			const createdBy = (adminUserId as Array<{ id: string }>)[0]?.id || 'system';

			for (const template of templates)
			{
				await db.mariadb.query(`
					INSERT INTO permission_templates (id, name, description, permissions, created_by)
					VALUES (?, ?, ?, ?, ?)
				`, [
					randomUUID(),
                    template.name,
                    template.description,
                    JSON.stringify(template.permissions),
                    createdBy,
                ]);
			}

			console.log('✅ Permission templates created');
		}

		console.log('🎉 User Management initialization completed successfully!');
		console.log('');
		console.log('Next steps:');
		console.log('1. Start the admin panel server');
		console.log('2. Login with the default admin credentials');
		console.log('3. Change the default password');
			console.log('4. Create additional users as needed');
			console.log('5. Configure user roles and permissions');
    }
    catch (error)
		{
			scriptLogger.error({ error }, 'Error initializing user management');
			console.error('❌ Error initializing user management:', error instanceof Error ? error.message : String(error));
			process.exit(1);
		}
    finally
    {
			// Close database connections if available
			try
			{
				if (db?.mariadb)
				{
					await db.mariadb.disconnect();
				}
			}
			catch (error)
			{
				console.error('Error closing database:', error);
			}
    }
}

// Run the initialization if this script is executed directly
if (require.main === module)
{
	initializeUserManagement().catch(console.error);
}

export { initializeUserManagement };
