import { config } from '@/lib/config';

type LogLevelType = 'debug' | 'info' | 'warn' | 'error';

interface LogEntryType
{
	timestamp: string;
	level: LogLevelType;
	message: string;
	meta?: Record<string, unknown>;
	service: string;
}

class Logger
{
	private serviceName: string;

	constructor(serviceName: string = 'admin-panel')
	{
		this.serviceName = serviceName;
	}

	private log(level: LogLevelType, message: string, meta?: Record<string, unknown>): void
	{
		const logEntry: LogEntryType = {
			timestamp: new Date().toISOString(),
			level,
			message,
			service: this.serviceName,
			...(meta && { meta }),
		};

		if (this.shouldLog(level))
		{
			if (config.logging.format === 'json')
			{
				console.log(JSON.stringify(logEntry));
			}
			else
			{
				console.log(`[${logEntry.timestamp}] ${level.toUpperCase()}: ${message}`, meta || '');
			}
		}
	}

	private shouldLog(level: LogLevelType): boolean
	{
		const levels: Record<LogLevelType, number> = {
			debug: 0,
			info: 1,
			warn: 2,
			error: 3,
		};

		return levels[level] >= levels[config.logging.level];
	}

	debug(message: string, meta?: Record<string, unknown>): void
	{
		this.log('debug', message, meta);
	}

	info(message: string, meta?: Record<string, unknown>): void
	{
		this.log('info', message, meta);
	}

	warn(message: string, meta?: Record<string, unknown>): void
	{
		this.log('warn', message, meta);
	}

	error(message: string, meta?: Record<string, unknown>): void
	{
		this.log('error', message, meta);
	}
}

export type { LogLevelType, LogEntryType };

const logger = new Logger();
export { logger };
export default logger;
