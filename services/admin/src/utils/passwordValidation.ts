import bcrypt from 'bcrypt';

type PasswordValidationResultType =
{
	isValid: boolean;
	errors: string[];
	strength: 'weak' | 'medium' | 'strong';
	score: number;
};

type PasswordPolicyType =
{
	minLength: number;
	requireUppercase: boolean;
	requireLowercase: boolean;
	requireNumbers: boolean;
	requireSpecialChars: boolean;
	maxAge: number; // days
};

const DEFAULT_POLICY: PasswordPolicyType =
{
	minLength: 8,
	requireUppercase: true,
	requireLowercase: true,
	requireNumbers: true,
	requireSpecialChars: true,
	maxAge: 90,
};

function validatePassword(password: string, policy: PasswordPolicyType = DEFAULT_POLICY): PasswordValidationResultType
{
	const errors: string[] = [];
	let score = 0;

	// Length check
	if (password.length < policy.minLength)
	{
		errors.push(`Password must be at least ${policy.minLength} characters long`);
	}
	else
	{
		score += Math.min(password.length * 2, 20); // Max 20 points for length
	}

	// Uppercase check
	if (policy.requireUppercase && !/[A-Z]/.test(password))
	{
		errors.push('Password must contain at least one uppercase letter');
	}
	else if (/[A-Z]/.test(password))
	{
		score += 10;
	}

	// Lowercase check
	if (policy.requireLowercase && !/[a-z]/.test(password))
	{
		errors.push('Password must contain at least one lowercase letter');
	}
	else if (/[a-z]/.test(password))
	{
		score += 10;
	}

	// Numbers check
	if (policy.requireNumbers && !/\d/.test(password))
	{
		errors.push('Password must contain at least one number');
	}
	else if (/\d/.test(password))
	{
		score += 10;
	}

	// Special characters check
	if (policy.requireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(password))
	{
		errors.push('Password must contain at least one special character');
	}
	else if (/[!@#$%^&*(),.?":{}|<>]/.test(password))
	{
		score += 10;
	}

	// Additional complexity checks
	const hasRepeatingChars = /(.)\1{2,}/.test(password);
	if (hasRepeatingChars)
	{
		score -= 10;
		errors.push('Password should not contain repeating characters');
	}

	const hasSequentialChars = /(?:abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz|123|234|345|456|567|678|789)/i.test(password);
	if (hasSequentialChars)
	{
		score -= 5;
	}

	// Common patterns check
	const commonPatterns = [
		/password/i,
		/admin/i,
		/123456/,
		/qwerty/i,
		/letmein/i,
	];

	for (const pattern of commonPatterns)
	{
		if (pattern.test(password))
		{
			score -= 15;
			errors.push('Password contains common patterns and is not secure');
			break;
		}
	}

	// Determine strength
	let strength: 'weak' | 'medium' | 'strong';
	if (score < 30)
	{
		strength = 'weak';
	}
	else if (score < 60)
	{
		strength = 'medium';
	}
	else
	{
		strength = 'strong';
	}

	return {
		isValid: errors.length === 0,
		errors,
		strength,
		score: Math.max(0, Math.min(100, score)),
	};
}

function generateSecurePassword(length: number = 16): string
{
	const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
	const lowercase = 'abcdefghijklmnopqrstuvwxyz';
	const numbers = '0123456789';
	const specialChars = '!@#$%^&*(),.?":{}|<>';

	const allChars = uppercase + lowercase + numbers + specialChars;

	let password = '';

	// Ensure at least one character from each category
	password += uppercase[Math.floor(Math.random() * uppercase.length)];
	password += lowercase[Math.floor(Math.random() * lowercase.length)];
	password += numbers[Math.floor(Math.random() * numbers.length)];
	password += specialChars[Math.floor(Math.random() * specialChars.length)];

	// Fill the rest randomly
	for (let i = 4; i < length; i++)
	{
		password += allChars[Math.floor(Math.random() * allChars.length)];
	}

	// Shuffle the password
	return password.split('').sort(() => Math.random() - 0.5).join('');
}

function isPasswordExpired(passwordLastChanged: Date, maxAge: number): boolean
{
	const maxAgeMs = maxAge * 24 * 60 * 60 * 1000; // Convert days to milliseconds
	return Date.now() - passwordLastChanged.getTime() > maxAgeMs;
}

function getPasswordStrengthColor(strength: 'weak' | 'medium' | 'strong'): string
{
	switch (strength)
	{
		case 'weak':
			return 'red';
		case 'medium':
			return 'yellow';
		case 'strong':
			return 'green';
		default:
			return 'gray';
	}
}

async function hashPassword(password: string): Promise<string>
{
	const saltRounds = 12;
	return await bcrypt.hash(password, saltRounds);
}

async function verifyPassword(password: string, hash: string): Promise<boolean>
{
	return await bcrypt.compare(password, hash);
}

export type { PasswordValidationResultType, PasswordPolicyType };

export {
	validatePassword,
	generateSecurePassword,
	isPasswordExpired,
	getPasswordStrengthColor,
	hashPassword,
	verifyPassword,
	DEFAULT_POLICY,
};
