import { z } from 'zod';

const loginSchema = z.object({
	username: z.string().min(1, 'Username is required').max(50, 'Username too long'),
	password: z.string().min(1, 'Password is required').max(100, 'Password too long'),
});

const sessionSchema = z.object({
	sessionId: z.string().uuid(),
	username: z.string(),
	role: z.enum(['super_admin', 'admin', 'viewer']),
	permissions: z.array(z.string()),
	createdAt: z.date(),
	lastActivity: z.date(),
	expiresAt: z.date(),
});

const serviceHealthSchema = z.object({
	name: z.string(),
	status: z.enum(['healthy', 'unhealthy', 'degraded']),
	uptime: z.number().min(0),
	responseTime: z.number().min(0),
	lastCheck: z.date(),
	version: z.string(),
	metrics: z.object({
		cpu: z.number().min(0).max(100),
		memory: z.number().min(0).max(100),
		requests: z.number().min(0),
		errors: z.number().min(0),
	}),
});

function validateInput<T>(schema: z.ZodSchema<T>, data: unknown): { success: true; data: T } | { success: false; error: string }
{
	try
	{
		const result = schema.parse(data);
		return { success: true, data: result };
	}
	catch (error)
	{
		if (error instanceof z.ZodError)
		{
			const errorMessage = error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ');
			return { success: false, error: errorMessage };
		}
		return { success: false, error: 'Validation failed' };
	}
}

export {
	loginSchema,
	sessionSchema,
	serviceHealthSchema,
	validateInput,
};
