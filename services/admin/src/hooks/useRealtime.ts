import {
	useEffect,
	useRef,
	useCallback,
	useState,
} from 'react';

import type { RealtimeMessageType, ConnectionInfoType, RealtimeStatsType } from '@/lib/realtime/types';
import { getRealtimeManager } from '@/lib/realtime/RealtimeManager';
import { clientLogger } from '@/lib/logger';

type UseRealtimeOptionsType =
{
	autoConnect?: boolean;
	reconnectOnMount?: boolean;
};

function useRealtime(options: UseRealtimeOptionsType = {})
{
	const { autoConnect = true, reconnectOnMount = true } = options;
	const [isConnected, setIsConnected] = useState(false);
	const [connectionInfo, setConnectionInfo] = useState<Record<string, ConnectionInfoType>>({});
	const [stats, setStats] = useState<RealtimeStatsType | null>(null);
	const managerRef = useRef(getRealtimeManager());
	const subscriptionsRef = useRef<Set<string>>(new Set());

	const updateConnectionStatus = useCallback(() =>
	{
		const manager = managerRef.current;
		setIsConnected(manager.isConnected());
		setConnectionInfo(manager.getConnectionInfo());
		setStats(manager.getStats());
	}, []);

	useEffect(() =>
	{
		const manager = managerRef.current;

		if (autoConnect)
		{
			manager.initialize().then(() =>
			{
				updateConnectionStatus();
			}).catch((error) =>
			{
				clientLogger.error('Failed to initialize realtime connection:', { error });
			});
		}

		// Set up periodic status updates
		const statusInterval = setInterval(updateConnectionStatus, 5000);

		return () =>
		{
			clearInterval(statusInterval);

			// Clean up subscriptions
			subscriptionsRef.current.forEach((subscriptionId) =>
			{
				manager.unsubscribe(subscriptionId);
			});
			subscriptionsRef.current.clear();

			if (reconnectOnMount)
			{
				manager.destroy();
			}
		};
	}, [autoConnect, reconnectOnMount, updateConnectionStatus]);

	const subscribe = useCallback((
		type: string,
		callback: (message: RealtimeMessageType) => void,
		filters?: Record<string, any>,
		preferredTransport?: 'sse' | 'websocket' | 'auto',
	) =>
	{
		const manager = managerRef.current;
		const subscriptionId = manager.subscribe(type, callback, filters, preferredTransport);
		subscriptionsRef.current.add(subscriptionId);
		return subscriptionId;
	}, []);

	const unsubscribe = useCallback((subscriptionId: string) =>
	{
		const manager = managerRef.current;
		manager.unsubscribe(subscriptionId);
		subscriptionsRef.current.delete(subscriptionId);
	}, []);

	const sendMessage = useCallback((message: any) =>
	{
		const manager = managerRef.current;
		manager.sendMessage(message);
	}, []);

	return ({
		isConnected,
		connectionInfo,
		stats,
		subscribe,
		unsubscribe,
		sendMessage,
		updateConnectionStatus,
	});
}

export default useRealtime;
