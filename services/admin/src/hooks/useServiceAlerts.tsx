import { notifications } from '@mantine/notifications';
import { IconAlertTriangle, IconCheck, IconX } from '@tabler/icons-react';
import { useEffect, useRef } from 'react';

type ServiceStatusType =
{
	name: string;
	status: 'healthy' | 'unhealthy' | 'degraded';
	uptime: number;
	responseTime: number;
	lastCheck: Date;
	version: string;
	metrics: {
		cpu: number;
		memory: number;
		requests: number;
		errors: number;
	};
};

type ServiceHealthDataType =
{
	services: ServiceStatusType[];
	timestamp: Date;
	summary: {
		total: number;
		healthy: number;
		degraded: number;
		unhealthy: number;
	};
};

type UseServiceAlertsOptionsType =
{
	enableNotifications?: boolean;
	cpuThreshold?: number;
	memoryThreshold?: number;
	responseTimeThreshold?: number;
};

function useServiceAlerts(
	healthData: ServiceHealthDataType | null,
	options: UseServiceAlertsOptionsType = {},
)
{
	const {
		enableNotifications = true,
		cpuThreshold = 80,
		memoryThreshold = 80,
		responseTimeThreshold = 5000,
	} = options;

	const previousHealthData = useRef<ServiceHealthDataType | null>(null);
	const notifiedServices = useRef<Set<string>>(new Set());

	useEffect(() =>
	{
		if (!healthData || !enableNotifications)
		{
			return;
		}

		const previous = previousHealthData.current;

		// Check for service status changes
		healthData.services.forEach((service) =>
		{
			const previousService = previous?.services.find(s => s.name === service.name);

			// Service became unhealthy
			if (service.status === 'unhealthy' && previousService?.status !== 'unhealthy')
			{
				if (!notifiedServices.current.has(`${service.name}-unhealthy`))
				{
					notifications.show({
						id: `${service.name}-unhealthy`,
						title: 'Service Down',
						message: `${service.name} service is unhealthy`,
						color: 'red',
						icon: <IconX size={16} />,
						autoClose: false,
					});
					notifiedServices.current.add(`${service.name}-unhealthy`);
				}
			}

			// Service became degraded
			else if (service.status === 'degraded' && previousService?.status === 'healthy')
			{
				if (!notifiedServices.current.has(`${service.name}-degraded`))
				{
					notifications.show({
						id: `${service.name}-degraded`,
						title: 'Service Degraded',
						message: `${service.name} service is experiencing issues`,
						color: 'yellow',
						icon: <IconAlertTriangle size={16} />,
						autoClose: 10000,
					});
					notifiedServices.current.add(`${service.name}-degraded`);
				}
			}

			// Service recovered
			else if (service.status === 'healthy' && previousService?.status !== 'healthy')
			{
				// Clear previous notifications
				notifications.hide(`${service.name}-unhealthy`);
				notifications.hide(`${service.name}-degraded`);
				notifiedServices.current.delete(`${service.name}-unhealthy`);
				notifiedServices.current.delete(`${service.name}-degraded`);

				if (previousService?.status === 'unhealthy' || previousService?.status === 'degraded')
				{
					notifications.show({
						id: `${service.name}-recovered`,
						title: 'Service Recovered',
						message: `${service.name} service is now healthy`,
						color: 'green',
						icon: <IconCheck size={16} />,
						autoClose: 5000,
					});
				}
			}

			// Check performance thresholds
			if (service.status === 'healthy')
			{
				// High CPU usage
				if (service.metrics.cpu > cpuThreshold)
				{
					const notificationId = `${service.name}-high-cpu`;
					if (!notifiedServices.current.has(notificationId))
					{
						notifications.show({
							id: notificationId,
							title: 'High CPU Usage',
							message: `${service.name} CPU usage is ${service.metrics.cpu.toFixed(1)}%`,
							color: 'orange',
							icon: <IconAlertTriangle size={16} />,
							autoClose: 10000,
						});
						notifiedServices.current.add(notificationId);

						// Auto-clear after some time
						setTimeout(() =>
						{
							notifiedServices.current.delete(notificationId);
						}, 60000);
					}
				}

				// High memory usage
				if (service.metrics.memory > memoryThreshold)
				{
					const notificationId = `${service.name}-high-memory`;
					if (!notifiedServices.current.has(notificationId))
					{
						notifications.show({
							id: notificationId,
							title: 'High Memory Usage',
							message: `${service.name} memory usage is ${service.metrics.memory.toFixed(1)}%`,
							color: 'orange',
							icon: <IconAlertTriangle size={16} />,
							autoClose: 10000,
						});
						notifiedServices.current.add(notificationId);

						// Auto-clear after some time
						setTimeout(() =>
						{
							notifiedServices.current.delete(notificationId);
						}, 60000);
					}
				}

				// High response time
				if (service.responseTime > responseTimeThreshold)
				{
					const notificationId = `${service.name}-slow-response`;
					if (!notifiedServices.current.has(notificationId))
					{
						notifications.show({
							id: notificationId,
							title: 'Slow Response Time',
							message: `${service.name} response time is ${service.responseTime}ms`,
							color: 'orange',
							icon: <IconAlertTriangle size={16} />,
							autoClose: 10000,
						});
						notifiedServices.current.add(notificationId);

						// Auto-clear after some time
						setTimeout(() =>
						{
							notifiedServices.current.delete(notificationId);
						}, 60000);
					}
				}
			}
		});

		previousHealthData.current = healthData;
	}, [healthData, enableNotifications, cpuThreshold, memoryThreshold, responseTimeThreshold]);

	const clearAllNotifications = () =>
	{
		notifiedServices.current.forEach((notificationId) =>
		{
			notifications.hide(notificationId);
		});
		notifiedServices.current.clear();
	};

	return {
		clearAllNotifications,
	};
}

export default useServiceAlerts;
