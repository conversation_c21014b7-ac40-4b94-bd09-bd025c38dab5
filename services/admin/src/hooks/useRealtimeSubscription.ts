import {
	useEffect, useRef, useCallback, useState,
} from 'react';

import type { RealtimeMessageType } from '@/lib/realtime/types';
import useRealtime from './useRealtime';


export type UseRealtimeSubscriptionOptionsType<T = any> =
{
	type: string;
	filters?: Record<string, any>;
	preferredTransport?: 'sse' | 'websocket' | 'auto';
	enabled?: boolean;
	onMessage?: (message: RealtimeMessageType) => void;
	onError?: (error: Error) => void;
	transform?: (data: any) => T;
	bufferSize?: number;
	bufferTimeout?: number;
};

export function useRealtimeSubscription<T = any>(
	options: UseRealtimeSubscriptionOptionsType<T>,
)
{
	const {
		type,
		filters,
		preferredTransport,
		enabled = true,
		onMessage,
		onError,
		transform,
		bufferSize = 1,
		bufferTimeout = 0,
	} = options;

	const { subscribe, unsubscribe, isConnected } = useRealtime();
	const [data, setData] = useState<T | null>(null);
	const [lastMessage, setLastMessage] = useState<RealtimeMessageType | null>(null);
	const [messageCount, setMessageCount] = useState(0);
	const [error, setError] = useState<Error | null>(null);

	const subscriptionIdRef = useRef<string | null>(null);
	const bufferRef = useRef<RealtimeMessageType[]>([]);
	const bufferTimeoutRef = useRef<NodeJS.Timeout | null>(null);

	const processBuffer = useCallback(() =>
	{
		if (bufferRef.current.length === 0) return;

		const messages = [...bufferRef.current];
		bufferRef.current = [];

		if (bufferTimeoutRef.current)
		{
			clearTimeout(bufferTimeoutRef.current);
			bufferTimeoutRef.current = null;
		}

		try
		{
			// Process messages in batch
			const latestMessage = messages[messages.length - 1];
			setLastMessage(latestMessage);
			setMessageCount(prev => prev + messages.length);

			if (transform)
			{
				const transformedData = transform(latestMessage.data);
				setData(transformedData);
			}
			else
			{
				setData(latestMessage.data);
			}

			// Call onMessage for each message
			if (onMessage)
			{
				messages.forEach(onMessage);
			}

			setError(null);
		}
		catch (err)
		{
			const error = err instanceof Error ? err : new Error('Unknown error processing messages');
			setError(error);
			onError?.(error);
		}
	}, [transform, onMessage, onError]);

	const handleMessage = useCallback((message: RealtimeMessageType) =>
	{
		bufferRef.current.push(message);

		if (bufferSize <= 1)
		{
			// Process immediately
			processBuffer();
		}
		else if (bufferRef.current.length >= bufferSize)
		{
			// Buffer is full, process now
			processBuffer();
		}
		else if (bufferTimeout > 0 && !bufferTimeoutRef.current)
		{
			// Start buffer timeout
			bufferTimeoutRef.current = setTimeout(processBuffer, bufferTimeout);
		}
	}, [bufferSize, bufferTimeout, processBuffer]);

	useEffect(() =>
	{
		if (!enabled || !isConnected)
		{
			return undefined;
		}

		try
		{
			const subscriptionId = subscribe(type, handleMessage, filters, preferredTransport);
			subscriptionIdRef.current = subscriptionId;

			return () =>
			{
				if (subscriptionIdRef.current)
				{
					unsubscribe(subscriptionIdRef.current);
					subscriptionIdRef.current = null;
				}

				if (bufferTimeoutRef.current)
				{
					clearTimeout(bufferTimeoutRef.current);
					bufferTimeoutRef.current = null;
				}
			};
		}
		catch (err)
		{
			const error = err instanceof Error ? err : new Error('Failed to subscribe');
			setError(error);
			onError?.(error);
			return undefined;
		}
	}, [enabled, isConnected, type, filters, preferredTransport, subscribe, unsubscribe, handleMessage, onError]);

	// Clean up on unmount
	useEffect(() => () =>
	{
		if (bufferTimeoutRef.current)
		{
			clearTimeout(bufferTimeoutRef.current);
		}
	}, []);

	const reset = useCallback(() =>
	{
		setData(null);
		setLastMessage(null);
		setMessageCount(0);
		setError(null);
		bufferRef.current = [];

		if (bufferTimeoutRef.current)
		{
			clearTimeout(bufferTimeoutRef.current);
			bufferTimeoutRef.current = null;
		}
	}, []);

	return {
		data,
		lastMessage,
		messageCount,
		error,
		isConnected,
		reset,
	};
}

export default useRealtimeSubscription;
