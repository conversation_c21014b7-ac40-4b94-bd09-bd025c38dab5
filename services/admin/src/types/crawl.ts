import type { AdminCrawlJob } from '@/lib/database/types';

// Crawl job types
export type CrawlJobType = 'basic' | 'standard' | 'full' | 'priority' | 'light' | 'visual' | 'advanced';
export type CrawlJobPriority = 'low' | 'medium' | 'high';
export type CrawlJobStatus = 'pending' | 'running' | 'completed' | 'failed' | 'cancelled' | 'paused';

// Extended crawl job for admin interface
export type CrawlJob = AdminCrawlJob & {
	estimatedDuration?: number;
	resourceUsage?: {
		cpu: number;
		memory: number;
		network: number;
	};
	modules?: string[];
	settings?: CrawlJobSettings;
};

// Crawl job creation request
export type CrawlJobCreateRequest = {
	domain: string;
	crawlType: CrawlJobType;
	priority: CrawlJobPriority;
	scheduledAt?: Date;
	settings?: Partial<CrawlJobSettings>;
	metadata?: Record<string, unknown>;
};

// Crawl job settings
export type CrawlJobSettings = {
	rateLimit: number; // requests per minute
	concurrentRequests: number;
	timeout: number; // milliseconds
	retryAttempts: number;
	retryBackoff: number; // milliseconds
	userAgent: string;
	followRedirects: boolean;
	maxDepth: number;
	respectRobots: boolean;
	modules: string[];
	customHeaders?: Record<string, string>;
};

// Crawl job queue status
export type CrawlJobQueue = {
	pending: CrawlJob[];
	running: CrawlJob[];
	completed: CrawlJob[];
	failed: CrawlJob[];
	cancelled: CrawlJob[];
	paused: CrawlJob[];
	totalJobs: number;
	averageProcessingTime: number;
	successRate: number;
	queueDepth: number;
	estimatedWaitTime: number;
};

// Crawl job filters
export type CrawlJobFilters = {
	search: string;
	status: CrawlJobStatus[];
	crawlType: CrawlJobType[];
	priority: CrawlJobPriority[];
	dateRange: [Date | null, Date | null];
	domain: string;
	requestedBy: string;
	hasErrors: boolean;
	duration: [number | null, number | null]; // in seconds
};

// Crawl job sorting
export type CrawlJobSortField =
  | 'scheduledAt'
  | 'startedAt'
  | 'completedAt'
  | 'duration'
  | 'priority'
  | 'status'
  | 'domain'
  | 'crawlType'
  | 'progress';

export type CrawlJobSort = {
	field: CrawlJobSortField;
	direction: 'asc' | 'desc';
};

// Crawl job search result
export type CrawlJobSearchResult = {
	jobs: CrawlJob[];
	pagination: {
		page: number;
		pageSize: number;
		total: number;
	};
	filters: CrawlJobFilters;
	sort: CrawlJobSort;
};

// Crawl job bulk operations
export type CrawlJobBulkAction =
  | 'cancel'
  | 'pause'
  | 'resume'
  | 'retry'
  | 'delete'
  | 'changePriority'
  | 'reschedule';

export type CrawlJobBulkOperationRequest = {
	action: CrawlJobBulkAction;
	jobIds: string[];
	parameters?: {
		priority?: CrawlJobPriority;
		scheduledAt?: Date;
		reason?: string;
	};
};

export type CrawlJobBulkOperationResult = {
	success: boolean;
	processedCount: number;
	failedCount: number;
	errors: Array<{
		jobId: string;
		error: string;
	}>;
};

// Crawl job history and analytics
export type CrawlJobHistoryEntry = {
	id: string;
	jobId: string;
	timestamp: Date;
	event: 'created' | 'started' | 'paused' | 'resumed' | 'completed' | 'failed' | 'cancelled' | 'retry';
	details: Record<string, unknown>;
	userId?: string;
};

export type CrawlJobHistory = {
	jobId: string;
	entries: CrawlJobHistoryEntry[];
	pagination: {
		page: number;
		pageSize: number;
		total: number;
	};
};

// Crawl job analytics
export type CrawlJobAnalytics = {
	timeframe: 'hour' | 'day' | 'week' | 'month';
	totalJobs: number;
	completedJobs: number;
	failedJobs: number;
	cancelledJobs: number;
	successRate: number;
	averageDuration: number;
	averageWaitTime: number;
	jobsByType: Record<CrawlJobType, number>;
	jobsByPriority: Record<CrawlJobPriority, number>;
	errorCategories: Record<string, number>;
	performanceTrends: Array<{
		timestamp: Date;
		completionRate: number;
		averageDuration: number;
		errorRate: number;
	}>;
};

// Crawl job templates
export type CrawlJobTemplate = {
	id: string;
	name: string;
	description: string;
	crawlType: CrawlJobType;
	priority: CrawlJobPriority;
	settings: CrawlJobSettings;
	metadata: Record<string, unknown>;
	isDefault: boolean;
	createdBy: string;
	createdAt: Date;
	usageCount: number;
};

// Crawl job error analysis
export type CrawlJobError = {
	jobId: string;
	domain: string;
	errorType: 'network' | 'timeout' | 'parsing' | 'validation' | 'system' | 'rate_limit' | 'blocked';
	errorCode: string;
	errorMessage: string;
	stackTrace?: string;
	timestamp: Date;
	retryable: boolean;
	resolution?: string;
	context: Record<string, unknown>;
};

export type CrawlJobErrorAnalysis = {
	totalErrors: number;
	errorsByType: Record<string, number>;
	errorsByCode: Record<string, number>;
	topFailingDomains: Array<{
		domain: string;
		errorCount: number;
		lastError: Date;
	}>;
	resolutionSuggestions: Array<{
		errorType: string;
		suggestion: string;
		priority: 'high' | 'medium' | 'low';
	}>;
	trends: Array<{
		timestamp: Date;
		errorCount: number;
		errorRate: number;
	}>;
};

// Crawler service controls
export type CrawlerServiceStatus = {
	status: 'running' | 'paused' | 'stopped' | 'emergency_stop';
	activeJobs: number;
	queuedJobs: number;
	systemLoad: {
		cpu: number;
		memory: number;
		network: number;
		disk: number;
	};
	resourceLimits: {
		maxConcurrentJobs: number;
		maxMemoryUsage: number;
		maxCpuUsage: number;
	};
	lastHealthCheck: Date;
	emergencyStopReason?: string;
};

export type CrawlerServiceControl = {
	action: 'pause' | 'resume' | 'stop' | 'emergency_stop' | 'restart';
	reason?: string;
	userId: string;
	timestamp: Date;
};

// Real-time updates
export type CrawlJobUpdate = {
	type: 'status_change' | 'progress_update' | 'log_entry' | 'error' | 'completion';
	jobId: string;
	timestamp: Date;
	data: Record<string, unknown>;
};

export type CrawlJobLogEntry = {
	jobId: string;
	timestamp: Date;
	level: 'debug' | 'info' | 'warn' | 'error';
	message: string;
	module?: string;
	context?: Record<string, unknown>;
};

// Global crawl settings
export type GlobalCrawlSettings = {
	rateLimit: {
		global: number; // requests per minute globally
		perDomain: number; // requests per minute per domain
		burstLimit: number; // max burst requests
	};
	concurrency: {
		maxConcurrentJobs: number;
		maxConcurrentRequestsPerJob: number;
		maxConcurrentRequestsGlobal: number;
	};
	timeouts: {
		connectionTimeout: number;
		requestTimeout: number;
		jobTimeout: number;
	};
	retries: {
		maxRetryAttempts: number;
		baseBackoffMs: number;
		maxBackoffMs: number;
		backoffMultiplier: number;
	};
	userAgents: string[];
	defaultSettings: CrawlJobSettings;
	resourceLimits: {
		maxMemoryPerJob: number;
		maxCpuPerJob: number;
		maxDiskSpacePerJob: number;
	};
	maintenance: {
		enabled: boolean;
		message?: string;
		allowedUsers?: string[];
	};
};
