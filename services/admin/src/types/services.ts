type ServiceStatusType = 'healthy' | 'unhealthy' | 'degraded';

type ServiceStatusDataType =
{
	name: string;
	status: ServiceStatusType;
	uptime: number;
	responseTime: number;
	lastCheck: Date;
	version: string;
	metrics: {
		cpu: number;
		memory: number;
		requests: number;
		errors: number;
	};
};

type DatabaseStatusType =
{
	name: 'scylla' | 'mariadb' | 'redis' | 'manticore';
	connected: boolean;
	responseTime: number;
	connectionPool: {
		active: number;
		idle: number;
		total: number;
	};
	metrics: {
		queries: number;
		errors: number;
		slowQueries: number;
	};
};

type ServiceHealthCheckType =
{
	checkAllServices(): Promise<ServiceStatusDataType[]>;
	checkService(serviceName: string): Promise<ServiceStatusDataType>;
	getServiceMetrics(serviceName: string): Promise<ServiceStatusDataType['metrics']>;
};

export type {
	ServiceStatusType,
	ServiceStatusDataType,
	DatabaseStatusType,
	ServiceHealthCheckType,
};
