import type { ReactNode } from 'react';

// Base configuration types
type ConfigCategoryType =
  | 'database'
  | 'crawling'
  | 'ranking'
  | 'alerts'
  | 'ai_services'
  | 'seeder';

type ConfigValueType =
  | 'string'
  | 'number'
  | 'boolean'
  | 'array'
  | 'object'
  | 'encrypted_string'
  | 'json';

type ConfigEnvironmentType = 'development' | 'staging' | 'production';

// Configuration field definition
type ConfigFieldType =
{
	key: string;
	label: string;
	description: string;
	type: ConfigValueType;
	required: boolean;
	defaultValue: unknown;
	validation?: {
		min?: number;
		max?: number;
		pattern?: string;
		enum?: string[];
		custom?: (value: unknown) => string | null;
	};
	dependencies?: string[];
	sensitive?: boolean;
	restartRequired?: boolean;
	category: ConfigCategoryType;
	subcategory?: string;
};

// Database configuration
type DatabaseConfigType =
{
	scylla: {
		hosts: string[];
		keyspace: string;
		username: string;
		password: string;
		poolSize: number;
		timeout: number;
		retryAttempts: number;
		ssl: boolean;
		compression: boolean;
		healthCheckInterval: number;
		failoverEnabled: boolean;
		failoverHosts: string[];
	};
	mariadb: {
		host: string;
		port: number;
		database: string;
		username: string;
		password: string;
		poolSize: number;
		timeout: number;
		retryAttempts: number;
		ssl: boolean;
		charset: string;
		timezone: string;
		healthCheckInterval: number;
	};
	redis: {
		host: string;
		port: number;
		password: string;
		database: number;
		poolSize: number;
		timeout: number;
		retryAttempts: number;
		ssl: boolean;
		keyPrefix: string;
		healthCheckInterval: number;
		cluster: boolean;
		clusterNodes: string[];
	};
	manticore: {
		host: string;
		port: number;
		timeout: number;
		retryAttempts: number;
		ssl: boolean;
		healthCheckInterval: number;
		indexPrefix: string;
		maxQueryTime: number;
	};
};

// Crawling configuration
type CrawlingConfigType =
{
	rateLimit: {
		global: number;
		perDomain: number;
		burstLimit: number;
		windowSize: number;
	};
	userAgents: {
		rotation: boolean;
		agents: string[];
		customAgent: string;
	};
	intervals: {
		priority: number;
		standard: number;
		full: number;
		light: number;
		visual: number;
		advanced: number;
	};
	concurrent: {
		maxRequests: number;
		maxPerDomain: number;
		queueSize: number;
	};
	timeouts: {
		request: number;
		dns: number;
		connect: number;
		response: number;
	};
	retries: {
		maxAttempts: number;
		backoffMultiplier: number;
		maxBackoff: number;
	};
	resources: {
		maxMemory: number;
		maxCpu: number;
		diskSpace: number;
	};
};

// Ranking configuration
type RankingConfigType =
{
	weights: {
		performance: number;
		security: number;
		seo: number;
		technical: number;
		backlinks: number;
	};
	calculation: {
		updateInterval: number;
		batchSize: number;
		parallelProcessing: boolean;
		cacheResults: boolean;
	};
	thresholds: {
		minScore: number;
		maxScore: number;
		qualityThreshold: number;
	};
	categories: {
		autoAssignment: boolean;
		confidenceThreshold: number;
		maxCategories: number;
	};
};

// Alert configuration
type AlertConfigType =
{
	id: string;
	name: string;
	description: string;
	enabled: boolean;
	category: string;
	severity: 'low' | 'medium' | 'high' | 'critical';
	conditions: {
		metric: string;
		operator: '>' | '<' | '=' | '>=' | '<=' | '!=' | 'contains' | 'not_contains';
		threshold: number | string;
		duration: number;
		aggregation?: 'avg' | 'sum' | 'min' | 'max' | 'count';
	}[];
	notifications: {
		email: {
			enabled: boolean;
			recipients: string[];
			template: string;
		};
		sms: {
			enabled: boolean;
			recipients: string[];
			provider: string;
		};
		webhook: {
			enabled: boolean;
			urls: string[];
			method: 'POST' | 'PUT';
			headers: Record<string, string>;
		};
		slack: {
			enabled: boolean;
			webhook: string;
			channel: string;
		};
	};
	escalation: {
		enabled: boolean;
		levels: {
			level: number;
			delay: number;
			recipients: string[];
			actions: string[];
		}[];
	};
	suppression: {
		enabled: boolean;
		duration: number;
		conditions: string[];
	};
};

// AI Services configuration
type AIServicesConfigType =
{
	providers: {
		openai: {
			enabled: boolean;
			apiKey: string;
			baseUrl: string;
			models: string[];
			defaultModel: string;
			maxTokens: number;
			temperature: number;
			timeout: number;
			rateLimit: number;
			costPerToken: number;
		};
		claude: {
			enabled: boolean;
			apiKey: string;
			baseUrl: string;
			models: string[];
			defaultModel: string;
			maxTokens: number;
			temperature: number;
			timeout: number;
			rateLimit: number;
			costPerToken: number;
		};
		google: {
			enabled: boolean;
			apiKey: string;
			baseUrl: string;
			models: string[];
			defaultModel: string;
			maxTokens: number;
			temperature: number;
			timeout: number;
			rateLimit: number;
			costPerToken: number;
		};
	};
	failover: {
		enabled: boolean;
		strategy: 'round_robin' | 'priority' | 'load_based';
		retryAttempts: number;
		backoffMultiplier: number;
	};
	content: {
		maxLength: number;
		qualityThreshold: number;
		moderationEnabled: boolean;
		cacheResults: boolean;
		cacheDuration: number;
	};
	usage: {
		dailyLimit: number;
		monthlyLimit: number;
		costLimit: number;
		alertThreshold: number;
	};
};

// Seeder configuration
type SeederConfigType =
{
	discovery: {
		strategies: {
			differential: {
				enabled: boolean;
				priority: number;
				batchSize: number;
				interval: number;
			};
			zoneNew: {
				enabled: boolean;
				priority: number;
				batchSize: number;
				interval: number;
			};
			longTail: {
				enabled: boolean;
				priority: number;
				batchSize: number;
				interval: number;
			};
			temporal: {
				enabled: boolean;
				priority: number;
				batchSize: number;
				interval: number;
			};
		};
	};
	connectors: {
		tranco: {
			enabled: boolean;
			apiKey: string;
			rateLimit: number;
			timeout: number;
			priority: number;
		};
		radar: {
			enabled: boolean;
			apiKey: string;
			rateLimit: number;
			timeout: number;
			priority: number;
		};
		commoncrawl: {
			enabled: boolean;
			rateLimit: number;
			timeout: number;
			priority: number;
			maxPages: number;
		};
		czds: {
			enabled: boolean;
			username: string;
			password: string;
			rateLimit: number;
			timeout: number;
			priority: number;
		};
		umbrella: {
			enabled: boolean;
			apiKey: string;
			rateLimit: number;
			timeout: number;
			priority: number;
		};
		sonar: {
			enabled: boolean;
			rateLimit: number;
			timeout: number;
			priority: number;
		};
	};
	processing: {
		maxDomainsPerDay: number;
		batchSize: number;
		concurrentProcessing: number;
		queueDepthLimit: number;
		retryAttempts: number;
		backoffMultiplier: number;
	};
	contentGeneration: {
		enabled: boolean;
		mode: 'pregenerated' | 'live' | 'hybrid';
		qualityThreshold: number;
		maxRetries: number;
		cacheResults: boolean;
		cacheDuration: number;
	};
};

// Complete system configuration
type SystemConfigType =
{
	database: DatabaseConfigType;
	crawling: CrawlingConfigType;
	ranking: RankingConfigType;
	alerts: AlertConfigType[];
	aiServices: AIServicesConfigType;
	seeder: SeederConfigType;
	metadata: {
		version: string;
		lastModified: Date;
		modifiedBy: string;
		environment: ConfigEnvironmentType;
		deploymentId: string;
	};
};

// Configuration validation result
type ConfigValidationResultType =
{
	valid: boolean;
	errors: {
		field: string;
		message: string;
		severity: 'error' | 'warning';
	}[];
	warnings: {
		field: string;
		message: string;
	}[];
	dependencies: {
		field: string;
		dependsOn: string[];
		satisfied: boolean;
	}[];
};

// Configuration change tracking
type ConfigChangeType =
{
	id: string;
	timestamp: Date;
	userId: string;
	username: string;
	category: ConfigCategoryType;
	field: string;
	oldValue: unknown;
	newValue: unknown;
	reason: string;
	approved: boolean;
	approvedBy?: string;
	approvedAt?: Date;
	deployed: boolean;
	deployedAt?: Date;
	rollbackId?: string;
};

// Configuration template
type ConfigTemplateType =
{
	id: string;
	name: string;
	description: string;
	environment: ConfigEnvironmentType;
	category: ConfigCategoryType;
	config: Partial<SystemConfigType>;
	createdAt: Date;
	createdBy: string;
	isDefault: boolean;
	tags: string[];
};

// Configuration deployment
type ConfigDeploymentType =
{
	id: string;
	timestamp: Date;
	userId: string;
	username: string;
	changes: ConfigChangeType[];
	strategy: 'immediate' | 'staged' | 'scheduled';
	status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'rolled_back';
	stages?: {
		name: string;
		services: string[];
		status: 'pending' | 'in_progress' | 'completed' | 'failed';
		startedAt?: Date;
		completedAt?: Date;
		error?: string;
	}[];
	rollbackDeploymentId?: string;
	scheduledAt?: Date;
	completedAt?: Date;
	error?: string;
};

// Configuration impact analysis
type ConfigImpactAnalysisType =
{
	affectedServices: string[];
	restartRequired: boolean;
	estimatedDowntime: number;
	riskLevel: 'low' | 'medium' | 'high' | 'critical';
	dependencies: {
		service: string;
		impact: string;
		mitigation: string;
	}[];
	recommendations: string[];
	rollbackPlan: string[];
};

// Configuration preview
type ConfigPreviewType =
{
	changes: {
		field: string;
		category: ConfigCategoryType;
		oldValue: unknown;
		newValue: unknown;
		impact: string;
	}[];
	validation: ConfigValidationResultType;
	impactAnalysis: ConfigImpactAnalysisType;
	estimatedDeploymentTime: number;
	requiredApprovals: string[];
};

// Configuration form props
type ConfigFormPropsType =
{
	category: ConfigCategoryType;
	config: Partial<SystemConfigType>;
	onChange: (category: ConfigCategoryType, field: string, value: unknown) => void;
	onValidate: (category: ConfigCategoryType) => ConfigValidationResultType;
	readonly?: boolean;
	showAdvanced?: boolean;
};

// Configuration component props
type ConfigComponentPropsType =
{
	title: string;
	description?: string;
	icon?: ReactNode;
	children: ReactNode;
	collapsible?: boolean;
	defaultExpanded?: boolean;
	actions?: ReactNode;
};

export type {
	ConfigCategoryType,
	ConfigValueType,
	ConfigEnvironmentType,
	ConfigFieldType,
	DatabaseConfigType,
	CrawlingConfigType,
	RankingConfigType,
	AlertConfigType,
	AIServicesConfigType,
	SeederConfigType,
	SystemConfigType,
	ConfigValidationResultType,
	ConfigChangeType,
	ConfigTemplateType,
	ConfigDeploymentType,
	ConfigImpactAnalysisType,
	ConfigPreviewType,
	ConfigFormPropsType,
	ConfigComponentPropsType,
};
