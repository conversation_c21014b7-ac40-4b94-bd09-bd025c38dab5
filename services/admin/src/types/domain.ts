import type { AdminDomainAnalysis, AdminCrawlJob, SeederQueueItem } from '@/lib/database/types';

// Domain list item for table display
export type DomainListItem = {
	domain: string;
	globalRank: number | null;
	categoryRank: number | null;
	category: string;
	overallScore: number;
	crawlStatus: 'pending' | 'in_progress' | 'completed' | 'failed';
	lastCrawled: Date | null;
	isPublic: boolean;
	seederStatus: 'queued' | 'processing' | 'completed' | 'failed';
	queuePosition?: number;
	performance: {
		score: number;
		loadTime: number;
	};
	security: {
		score: number;
		sslGrade: string;
	};
	seo: {
		score: number;
	};
	technical: {
		score: number;
	};
	createdAt: Date;
	updatedAt: Date;
};

// Domain search and filter types
export type DomainFilters = {
	search: string;
	category: string[];
	crawlStatus: string[];
	seederStatus: string[];
	rankRange: [number, number];
	scoreRange: [number, number];
	isPublic: boolean | null;
	registrationDateRange: [Date | null, Date | null];
	expirationDateRange: [Date | null, Date | null];
	sslGrades: string[];
	performanceScoreRange: [number, number];
	securityScoreRange: [number, number];
	seoScoreRange: [number, number];
	technicalScoreRange: [number, number];
};

// Domain sorting options
export type DomainSortField =
  | 'domain'
  | 'globalRank'
  | 'categoryRank'
  | 'overallScore'
  | 'lastCrawled'
  | 'createdAt'
  | 'updatedAt'
  | 'performance.score'
  | 'security.score'
  | 'seo.score'
  | 'technical.score';

export type DomainSort = {
	field: DomainSortField;
	direction: 'asc' | 'desc';
};

// Pagination options
export type DomainPagination = {
	page: number;
	pageSize: number;
	total: number;
};

// Domain search result
export type DomainSearchResult = {
	domains: DomainListItem[];
	pagination: DomainPagination;
	filters: DomainFilters;
	sort: DomainSort;
};

// Bulk operation types
export type DomainBulkAction =
  | 'updateCategory'
  | 'updateVisibility'
  | 'triggerCrawl'
  | 'recalculateRanking'
  | 'delete';

export type DomainBulkOperationRequest = {
	action: DomainBulkAction;
	domains: string[];
	parameters?: {
		category?: string;
		isPublic?: boolean;
		crawlType?: string;
		priority?: 'low' | 'medium' | 'high';
	};
};

export type DomainBulkOperationResult = {
	success: boolean;
	processedCount: number;
	failedCount: number;
	errors: Array<{
		domain: string;
		error: string;
	}>;
	jobId?: string; // For async operations like crawling
};

// Domain editing types
export type DomainEditRequest = {
	domain: string;
	updates: {
		category?: string;
		isPublic?: boolean;
		manualRankAdjustment?: number;
		metadata?: Record<string, unknown>;
		tags?: string[];
	};
};

// Domain history types
export type DomainHistoryEntry = {
	id: string;
	domain: string;
	type: 'ranking_change' | 'crawl_completed' | 'config_updated' | 'category_changed' | 'visibility_changed';
	timestamp: Date;
	oldValue?: unknown;
	newValue?: unknown;
	userId?: string;
	details: Record<string, unknown>;
};

export type DomainHistory = {
	domain: string;
	entries: DomainHistoryEntry[];
	pagination: {
		page: number;
		pageSize: number;
		total: number;
	};
};

// Domain relationship types
export type DomainRelationship = {
	domain: string;
	relatedDomain: string;
	relationshipType: 'subdomain' | 'parent_domain' | 'similar_content' | 'shared_hosting' | 'backlink';
	strength: number; // 0-1 confidence score
	discoveredAt: Date;
	metadata: Record<string, unknown>;
};

export type DomainRelationships = {
	domain: string;
	subdomains: DomainRelationship[];
	parentDomains: DomainRelationship[];
	relatedDomains: DomainRelationship[];
	backlinks: DomainRelationship[];
};

// Export/Import types
export type DomainExportFormat = 'csv' | 'json' | 'excel';

export type DomainExportRequest = {
	format: DomainExportFormat;
	filters: Partial<DomainFilters>;
	fields: string[];
	includeAnalysisData: boolean;
	includeHistory: boolean;
};

export type DomainExportResult = {
	success: boolean;
	fileUrl?: string;
	fileName?: string;
	recordCount?: number;
	error?: string;
};

export type DomainImportRequest = {
	file: File;
	format: DomainExportFormat;
	options: {
		skipDuplicates: boolean;
		updateExisting: boolean;
		validateDomains: boolean;
	};
};

export type DomainImportResult = {
	success: boolean;
	importedCount: number;
	skippedCount: number;
	errorCount: number;
	errors: Array<{
		row: number;
		domain?: string;
		error: string;
	}>;
	jobId?: string; // For async processing
};

// Domain categories
export type DomainCategory = {
	id: string;
	name: string;
	description: string;
	parentId?: string;
	level: 'primary' | 'secondary';
	domainCount: number;
	isActive: boolean;
};

// Column customization
export type DomainTableColumn = {
	key: string;
	label: string;
	sortable: boolean;
	filterable: boolean;
	visible: boolean;
	width?: number;
	type: 'text' | 'number' | 'date' | 'badge' | 'score' | 'boolean';
};

export type DomainTableConfig = {
	columns: DomainTableColumn[];
	defaultSort: DomainSort;
	defaultPageSize: number;
};
