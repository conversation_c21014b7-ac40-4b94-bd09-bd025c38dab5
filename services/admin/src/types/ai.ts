import type { ReactNode } from 'react';

export type AIProviderType = 'openai' | 'claude' | 'google-ai';

export type AIModelType =
{
	id: string;
	name: string;
	provider: AIProviderType;
	maxTokens: number;
	costPerToken: number;
	capabilities: string[];
	isActive: boolean;
};

export type AIProviderConfigType =
{
	id: string;
	name: string;
	type: AIProviderType;
	apiKey: string;
	baseUrl?: string;
	isActive: boolean;
	priority: number;
	rateLimit: {
		requestsPerMinute: number;
		tokensPerMinute: number;
	};
	models: AIModelType[];
	healthCheck: {
		lastCheck: Date | null;
		status: 'healthy' | 'unhealthy' | 'degraded';
		responseTime: number;
		errorMessage?: string;
	};
	usage: {
		totalRequests: number;
		totalTokens: number;
		totalCost: number;
		successRate: number;
	};
};

export type AIPromptTemplateType =
{
	id: string;
	name: string;
	description: string;
	category: 'domain-description' | 'categorization' | 'tagging' | 'seo-summary';
	template: string;
	variables: string[];
	isActive: boolean;
	performance: {
		averageResponseTime: number;
		successRate: number;
		qualityScore: number;
		usageCount: number;
	};
	createdAt: Date;
	updatedAt: Date;
};

export type AIContentGenerationJobType =
{
	id: string;
	domain: string;
	type: 'description' | 'categorization' | 'tagging' | 'seo-summary';
	status: 'pending' | 'processing' | 'completed' | 'failed';
	provider: AIProviderType;
	model: string;
	promptTemplate: string;
	input: Record<string, any>;
	output?: {
		content: string;
		confidence: number;
		metadata: Record<string, any>;
	};
	metrics: {
		startTime: Date;
		endTime?: Date;
		duration?: number;
		tokenCount: number;
		cost: number;
	};
	error?: {
		code: string;
		message: string;
		details?: Record<string, any>;
	};
	qualityAssessment?: {
		score: number;
		issues: string[];
		suggestions: string[];
	};
};

export type AIUsageAnalyticsType =
{
	timeframe: {
		start: Date;
		end: Date;
	};
	providers: Record<AIProviderType, {
		requests: number;
		tokens: number;
		cost: number;
		successRate: number;
		averageResponseTime: number;
		errorRate: number;
	}>;
	models: Record<string, {
		requests: number;
		tokens: number;
		cost: number;
		successRate: number;
		averageResponseTime: number;
	}>;
	contentTypes: Record<string, {
		requests: number;
		successRate: number;
		averageQualityScore: number;
	}>;
	trends: {
		timestamp: Date;
		requests: number;
		tokens: number;
		cost: number;
		successRate: number;
	}[];
};

export type AIServiceConfigType =
{
	enabled: boolean;
	defaultProvider: AIProviderType;
	fallbackProviders: AIProviderType[];
	loadBalancing: {
		strategy: 'round-robin' | 'least-loaded' | 'priority';
		healthCheckInterval: number;
	};
	contentGeneration: {
		batchSize: number;
		maxRetries: number;
		timeoutMs: number;
		qualityThreshold: number;
	};
	moderation: {
		enabled: boolean;
		filters: string[];
		blockThreshold: number;
	};
	caching: {
		enabled: boolean;
		ttlSeconds: number;
		maxSize: number;
	};
};

export type AIProviderStatusType =
{
	provider: AIProviderType;
	status: 'healthy' | 'unhealthy' | 'degraded';
	availability: number;
	responseTime: number;
	rateLimit: {
		remaining: number;
		resetTime: Date;
	};
	usage: {
		current: number;
		quota: number;
		percentage: number;
	};
	lastCheck: Date;
	errors: {
		count: number;
		lastError?: string;
	};
};

export type AIContentQualityMetricsType =
{
	overall: {
		averageScore: number;
		totalAssessments: number;
		passRate: number;
	};
	byProvider: Record<AIProviderType, {
		averageScore: number;
		assessments: number;
		passRate: number;
	}>;
	byContentType: Record<string, {
		averageScore: number;
		assessments: number;
		passRate: number;
	}>;
	commonIssues: {
		issue: string;
		frequency: number;
		impact: 'low' | 'medium' | 'high';
	}[];
	trends: {
		timestamp: Date;
		averageScore: number;
		passRate: number;
	}[];
};

export type AIPromptTestResultType =
{
	id: string;
	templateId: string;
	testInput: Record<string, any>;
	provider: AIProviderType;
	model: string;
	result: {
		content: string;
		confidence: number;
		responseTime: number;
		tokenCount: number;
		cost: number;
	};
	qualityScore: number;
	issues: string[];
	timestamp: Date;
};

export type AIServiceManagementPropsType =
{
	children?: ReactNode;
};

export type AIProviderConfigProps =
{
	providers: AIProviderConfigType[];
	onUpdate: (provider: AIProviderConfigType) => void;
	onDelete: (providerId: string) => void;
	onAdd: (provider: Omit<AIProviderConfigType, 'id'>) => void;
};

export type AIUsageAnalyticsProps =
{
	analytics: AIUsageAnalyticsType;
	timeframe: {
		start: Date;
		end: Date;
	};
	onTimeframeChange: (start: Date, end: Date) => void;
};

export type AIContentQualityProps =
{
	metrics: AIContentQualityMetricsType;
	onRefresh: () => void;
};

export type AIPromptManagementProps =
{
	templates: AIPromptTemplateType[];
	onUpdate: (template: AIPromptTemplateType) => void;
	onDelete: (templateId: string) => void;
	onAdd: (template: Omit<AIPromptTemplateType, 'id' | 'createdAt' | 'updatedAt'>) => void;
	onTest: (templateId: string, testData: Record<string, any>) => Promise<AIPromptTestResultType>;
};
