
type AlertSeverityType =
	| 'critical'
	| 'high'
	| 'medium'
	| 'low'
	| 'info'
;

type AlertStatusType =
	| 'active'
	| 'acknowledged'
	| 'resolved'
	| 'silenced'
;

type NotificationChannelType =
	| 'email'
	| 'sms'
	| 'webhook'
	| 'slack'
	| 'teams'
	| 'pagerduty'
	| 'jira'
;

type AlertOperatorType =
	| '>'
	| '<'
	| '='
	| '>='
	| '<='
	| '!='
	| 'contains'
	| 'not_contains'
;

type AlertMetricType =
  | 'service_health'
  | 'response_time'
  | 'error_rate'
  | 'cpu_usage'
  | 'memory_usage'
  | 'disk_usage'
  | 'queue_depth'
  | 'database_connections'
  | 'crawl_success_rate'
  | 'domain_discovery_rate';

type AlertConditionType =
{
	id: string;
	metric: AlertMetricType;
	operator: AlertOperatorType;
	threshold: number;
	duration: number; // minutes
	service?: string;
	resource?: string;
};

type AlertRuleType =
{
	id: string;
	name: string;
	description: string;
	enabled: boolean;
	severity: AlertSeverityType;
	conditions: AlertConditionType[];
	logicalOperator: 'AND' | 'OR';
	timeBasedConditions?: {
		activeHours?: { start: string; end: string };
		activeDays?: number[]; // 0-6, Sunday = 0
		timezone?: string;
	};
	dependencies?: string[]; // other alert rule IDs
	suppressionRules?: {
		maintenanceWindows?: boolean;
		dependencyBased?: boolean;
		smartGrouping?: boolean;
	};
	createdAt: Date;
	updatedAt: Date;
	createdBy: string;
	tags: string[];
};

type NotificationTemplateType =
{
	id: string;
	name: string;
	channel: NotificationChannelType;
	subject: string;
	body: string;
	variables: string[];
	isDefault: boolean;
	createdAt: Date;
	updatedAt: Date;
};

type EscalationLevelType =
{
	level: number;
	delay: number; // minutes
	channels: NotificationChannelType[];
	recipients: string[];
	template?: string;
};

type AlertEscalationPolicyType =
{
	id: string;
	name: string;
	description: string;
	levels: EscalationLevelType[];
	onCallRotation?: {
		enabled: boolean;
		schedule: Array<{
			user: string;
			startTime: string;
			endTime: string;
			days: number[];
		}>;
	};
	createdAt: Date;
	updatedAt: Date;
};

type AlertInstanceType =
{
	id: string;
	ruleId: string;
	ruleName: string;
	severity: AlertSeverityType;
	status: AlertStatusType;
	message: string;
	details: Record<string, unknown>;
	triggeredAt: Date;
	acknowledgedAt?: Date;
	acknowledgedBy?: string;
	resolvedAt?: Date;
	resolvedBy?: string;
	escalationLevel: number;
	notificationsSent: Array<{
		channel: NotificationChannelType;
		recipient: string;
		sentAt: Date;
		success: boolean;
		error?: string;
	}>;
	tags: string[];
	correlationId?: string;
	affectedServices: string[];
	metrics: Record<string, number>;
};

type AlertHistoryType =
{
	id: string;
	alertId: string;
	action: 'triggered' | 'acknowledged' | 'resolved' | 'escalated' | 'silenced' | 'notification_sent';
	timestamp: Date;
	userId?: string;
	details: Record<string, unknown>;
	previousState?: AlertStatusType;
	newState?: AlertStatusType;
};

type AlertSilenceType =
{
	id: string;
	alertRuleIds: string[];
	reason: string;
	startTime: Date;
	endTime: Date;
	createdBy: string;
	isActive: boolean;
	tags?: string[];
	services?: string[];
};

type MaintenanceWindowType =
{
	id: string;
	name: string;
	description: string;
	startTime: Date;
	endTime: Date;
	affectedServices: string[];
	suppressAlerts: boolean;
	createdBy: string;
	isActive: boolean;
	recurring?: {
		pattern: 'daily' | 'weekly' | 'monthly';
		interval: number;
		endDate?: Date;
	};
};

type AlertAnalyticsType =
{
	totalAlerts: number;
	alertsByStatus: Record<AlertStatusType, number>;
	alertsBySeverity: Record<AlertSeverityType, number>;
	alertsByService: Record<string, number>;
	mttr: number; // Mean Time To Resolution in minutes
	mtbf: number; // Mean Time Between Failures in minutes
	escalationRate: number;
	falsePositiveRate: number;
	topAlertRules: Array<{
		ruleId: string;
		ruleName: string;
		count: number;
		avgResolutionTime: number;
	}>;
	alertTrends: Array<{
		timestamp: Date;
		count: number;
		severity: AlertSeverityType;
	}>;
	notificationEffectiveness: Record<NotificationChannelType, {
		sent: number;
		delivered: number;
		failed: number;
		responseTime: number;
	}>;
};

type AlertTestResultType =
{
	id: string;
	ruleId: string;
	testType: 'mock_trigger' | 'delivery_test' | 'end_to_end';
	status: 'running' | 'passed' | 'failed';
	startedAt: Date;
	completedAt?: Date;
	results: Array<{
		step: string;
		status: 'passed' | 'failed';
		message: string;
		duration: number;
		details?: Record<string, unknown>;
	}>;
	notifications: Array<{
		channel: NotificationChannelType;
		recipient: string;
		delivered: boolean;
		responseTime: number;
		error?: string;
	}>;
};

type ExternalIntegrationType =
{
	id: string;
	type: 'email' | 'sms' | 'pagerduty' | 'slack' | 'teams' | 'jira' | 'webhook';
	name: string;
	config: Record<string, unknown>;
	enabled: boolean;
	lastSync?: Date;
	status: 'connected' | 'disconnected' | 'error';
	errorMessage?: string;
};

type AlertDashboardStatsType =
{
	activeAlerts: number;
	criticalAlerts: number;
	acknowledgedAlerts: number;
	resolvedToday: number;
	avgResolutionTime: number;
	escalatedAlerts: number;
	silencedAlerts: number;
	maintenanceWindows: number;
	recentAlerts: AlertInstanceType[];
	topServices: Array<{
		service: string;
		alertCount: number;
		criticalCount: number;
	}>;
};

// API Response Types
type AlertRuleCreateRequestType = Omit<AlertRuleType, 'id' | 'createdAt' | 'updatedAt'>;
type AlertRuleUpdateRequestType = Partial<AlertRuleCreateRequestType>;

type NotificationTemplateCreateRequestType = Omit<NotificationTemplateType, 'id' | 'createdAt' | 'updatedAt'>;
type NotificationTemplateUpdateRequestType = Partial<NotificationTemplateCreateRequestType>;

type EscalationPolicyCreateRequestType = Omit<AlertEscalationPolicyType, 'id' | 'createdAt' | 'updatedAt'>;
type EscalationPolicyUpdateRequestType = Partial<EscalationPolicyCreateRequestType>;

type AlertSilenceCreateRequestType = Omit<AlertSilenceType, 'id' | 'isActive'>;

type MaintenanceWindowCreateRequestType = Omit<MaintenanceWindowType, 'id' | 'isActive'>;
type MaintenanceWindowUpdateRequestType = Partial<MaintenanceWindowCreateRequestType>;

type AlertAcknowledgeRequestType =
{
	alertIds: string[];
	reason?: string;
};

type AlertResolveRequestType =
{
	alertIds: string[];
	reason?: string;
	resolution?: string;
};

type AlertTestRequestType =
{
	ruleId: string;
	testType: 'mock_trigger' | 'delivery_test' | 'end_to_end';
	mockData?: Record<string, unknown>;
};

// Filter and Search Types
type AlertFilterType =
{
	status?: AlertStatusType[];
	severity?: AlertSeverityType[];
	services?: string[];
	tags?: string[];
	dateRange?: {
		start: Date;
		end: Date;
	};
	search?: string;
	ruleIds?: string[];
};

type AlertSortType =
{
	field: 'triggeredAt' | 'severity' | 'status' | 'ruleName' | 'service';
	direction: 'asc' | 'desc';
};

type PaginationType =
{
	page: number;
	limit: number;
	total?: number;
};

// Webhook and Integration Types
type WebhookPayloadType =
{
	alert: AlertInstanceType;
	rule: AlertRuleType;
	action: 'triggered' | 'acknowledged' | 'resolved' | 'escalated';
	timestamp: Date;
	metadata: Record<string, unknown>;
};

type SlackMessageType =
{
	channel: string;
	text: string;
	attachments?: Array<{
		color: string;
		title: string;
		text: string;
		fields: Array<{
			title: string;
			value: string;
			short: boolean;
		}>;
	}>;
};

type PagerDutyEventType =
{
	routing_key: string;
	event_action: 'trigger' | 'acknowledge' | 'resolve';
	dedup_key: string;
	payload: {
		summary: string;
		severity: 'critical' | 'error' | 'warning' | 'info';
		source: string;
		component?: string;
		group?: string;
		class?: string;
		custom_details?: Record<string, unknown>;
	};
};

export type {
	AlertSeverityType,
	AlertStatusType,
	NotificationChannelType,
	AlertOperatorType,
	AlertMetricType,
	AlertConditionType,
	AlertRuleType,
	NotificationTemplateType,
	EscalationLevelType,
	AlertEscalationPolicyType,
	AlertInstanceType,
	AlertHistoryType,
	AlertSilenceType,
	MaintenanceWindowType,
	AlertAnalyticsType,
	AlertTestResultType,
	ExternalIntegrationType,
	AlertDashboardStatsType,
	AlertRuleCreateRequestType,
	AlertRuleUpdateRequestType,
	NotificationTemplateCreateRequestType,
	NotificationTemplateUpdateRequestType,
	EscalationPolicyCreateRequestType,
	EscalationPolicyUpdateRequestType,
	AlertSilenceCreateRequestType,
	MaintenanceWindowCreateRequestType,
	MaintenanceWindowUpdateRequestType,
	AlertAcknowledgeRequestType,
	AlertResolveRequestType,
	AlertTestRequestType,
	AlertFilterType,
	AlertSortType,
	PaginationType,
	WebhookPayloadType,
	SlackMessageType,
	PagerDutyEventType,
};
