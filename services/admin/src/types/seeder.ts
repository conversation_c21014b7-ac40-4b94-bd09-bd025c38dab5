import type { ReactNode } from 'react';

export type SeederQueueStatusType = {
	totalQueued: number;
	processing: number;
	completed: number;
	failed: number;
	averageProcessingTime: number;
	estimatedTimeRemaining: number;
	queueDepth: number;
	throughputPerHour: number;
	backpressureLevel: number;
};

export type SeederConnectorType = {
	name: 'CommonCrawl' | 'CZDS' | 'Tranco' | 'Radar' | 'Umbrella' | 'Sonar';
	status: 'active' | 'inactive' | 'error' | 'degraded';
	lastSync: Date | null;
	domainsDiscovered: number;
	errorRate: number;
	rateLimit: number;
	nextSync: Date | null;
	dataFreshness: number; // seconds since last update
	syncDuration: number; // milliseconds
	totalSyncs: number;
	consecutiveErrors: number;
	configuration: {
		enabled: boolean;
		priority: number;
		batchSize: number;
		syncInterval: number; // hours
		maxRetries: number;
	};
};

export type SeederStrategyType = {
	name: 'differential' | 'zone-new' | 'long-tail' | 'temporal';
	displayName: string;
	enabled: boolean;
	priority: number;
	lastRun: Date | null;
	domainsProcessed: number;
	successRate: number;
	averageConfidence: number;
	estimatedNextRun: Date | null;
	configuration: {
		batchSize: number;
		confidenceThreshold: number;
		maxDomainsPerRun: number;
		cooldownPeriod: number; // minutes
		enabledSources: string[];
	};
	metrics: {
		totalRuns: number;
		averageRunTime: number;
		lastRunDuration: number;
		peakDomainsPerHour: number;
	};
};

export type SeederMetricsType = {
	candidatesFetched: Record<string, number>;
	candidatesAfterNormalize: number;
	knownInDb: number;
	newDiscovered: Record<string, number>;
	contentGenerated: Record<string, number>;
	enqueueAttempts: number;
	enqueueSuccess: number;
	rateLimited: number;
	queueDepth: number;
	dbCheckLatency: number;
	sourceStaleness: Record<string, number>;
	processingRates: {
		domainsPerHour: number;
		contentGenerationPerHour: number;
		validationPerHour: number;
	};
	errorAnalysis: {
		validationErrors: number;
		contentGenerationErrors: number;
		databaseErrors: number;
		networkErrors: number;
	};
};

export type SeederDiscoveryRunType = {
	id: string;
	startTime: Date;
	endTime: Date | null;
	status: 'running' | 'completed' | 'failed' | 'cancelled';
	strategiesUsed: string[];
	totalCandidates: number;
	totalDiscovered: number;
	duration: number;
	triggeredBy: 'scheduled' | 'manual' | 'api';
	runType: 'full' | 'incremental' | 'backfill' | 'strategy-specific';
	strategyResults: Record<string, {
		candidates: number;
		discovered: number;
		confidence: number;
		error?: string;
		duration: number;
	}>;
	performance: {
		peakMemoryUsage: number;
		averageCpuUsage: number;
		networkRequests: number;
		databaseQueries: number;
	};
};

export type SeederContentGenerationType = {
	preGeneratedCount: number;
	liveCount: number;
	validationFailures: number;
	averageSummaryLength: number;
	categoriesAssigned: Record<string, number>;
	tagsGenerated: number;
	seoSummariesCreated: number;
	qualityMetrics: {
		averageQualityScore: number;
		highQualityCount: number;
		mediumQualityCount: number;
		lowQualityCount: number;
	};
	generationModes: {
		selfGenerated: number;
		aiGenerated: number;
		templateBased: number;
		hybridGenerated: number;
	};
	processingTimes: {
		averageGenerationTime: number;
		averageValidationTime: number;
		averageCategoryAssignmentTime: number;
	};
};

export type SeederSettingsType = {
	maxNewDomainsPerDay: number;
	contentGenerationEnabled: boolean;
	processingMode: 'preGenerated' | 'live' | 'hybrid';
	batchSizes: {
		discovery: number;
		validation: number;
		contentGeneration: number;
		enqueue: number;
	};
	rateLimits: {
		globalRequestsPerMinute: number;
		perConnectorRequestsPerMinute: number;
		databaseQueriesPerSecond: number;
		contentGenerationPerHour: number;
	};
	bloomFilter: {
		expectedElements: number;
		falsePositiveRate: number;
		enabled: boolean;
		rebuildInterval: number; // hours
	};
	processingPriorities: {
		topTier: number;
		midTier: number;
		longTail: number;
	};
	retrySettings: {
		maxRetries: number;
		backoffMultiplier: number;
		maxBackoffTime: number; // seconds
	};
	monitoring: {
		metricsRetentionDays: number;
		alertThresholds: {
			queueDepthWarning: number;
			queueDepthCritical: number;
			errorRateWarning: number;
			errorRateCritical: number;
			processingDelayWarning: number; // minutes
			processingDelayCritical: number; // minutes
		};
	};
};

export type SeederAnalyticsType = {
	discoveryRates: Record<string, {
		hourly: number[];
		daily: number[];
		weekly: number[];
		trend: 'increasing' | 'decreasing' | 'stable';
	}>;
	contentGenerationSuccess: {
		successRate: number;
		failureReasons: Record<string, number>;
		qualityDistribution: Record<string, number>;
		averageProcessingTime: number;
	};
	processingThroughput: {
		current: number;
		peak: number;
		average: number;
		bottlenecks: string[];
	};
	queuePerformance: {
		averageWaitTime: number;
		peakQueueDepth: number;
		processingEfficiency: number;
		backpressureEvents: number;
	};
	sourceEffectiveness: Record<string, {
		uniqueDomainsContributed: number;
		qualityScore: number;
		reliability: number;
		costEffectiveness: number;
	}>;
};

export type SeederTroubleshootingType = {
	logAnalysis: {
		errorPatterns: Array<{
			pattern: string;
			frequency: number;
			severity: 'low' | 'medium' | 'high' | 'critical';
			suggestedAction: string;
			affectedComponents: string[];
		}>;
		performanceBottlenecks: Array<{
			component: string;
			bottleneckType: 'cpu' | 'memory' | 'network' | 'database';
			severity: number;
			impact: string;
			recommendation: string;
		}>;
	};
	systemHealth: {
		overallScore: number;
		componentScores: Record<string, number>;
		criticalIssues: string[];
		warnings: string[];
		recommendations: string[];
	};
	diagnostics: {
		lastDiagnosticRun: Date;
		diagnosticResults: Record<string, {
			status: 'pass' | 'fail' | 'warning';
			message: string;
			details?: any;
		}>;
	};
};

export type SeederOperationType =
  | 'full-discovery'
  | 'top-sources-only'
  | 'backfill'
  | 'strategy-specific'
  | 'queue-cleanup'
  | 'stuck-job-recovery'
  | 'bloom-filter-rebuild'
  | 'metrics-reset';

export type SeederManualTriggerType = {
	operation: SeederOperationType;
	parameters?: Record<string, any>;
	scheduledFor?: Date;
	priority: 'low' | 'medium' | 'high';
	estimatedDuration?: number;
	description?: string;
};

export type SeederQueueItemType = {
	domain: string;
	queuePosition: number;
	estimatedProcessingTime: Date;
	priority: 'low' | 'medium' | 'high';
	source: string;
	discoveryStrategy: string;
	contentGenerationMode: 'preGenerated' | 'live';
	retryCount: number;
	lastError?: string;
	metadata: Record<string, any>;
};

export type SeederDashboardDataType = {
	queueStatus: SeederQueueStatusType;
	connectors: SeederConnectorType[];
	strategies: SeederStrategyType[];
	metrics: SeederMetricsType;
	discoveryRuns: SeederDiscoveryRunType[];
	contentGeneration: SeederContentGenerationType;
	settings: SeederSettingsType;
	analytics: SeederAnalyticsType;
	troubleshooting: SeederTroubleshootingType;
	queueItems: SeederQueueItemType[];
	lastUpdated: Date;
};

export type SeederFilterType = {
	connectorStatus: string[];
	strategyStatus: string[];
	timeRange: {
		start: Date;
		end: Date;
	};
	discoveryRunStatus: string[];
	queuePriority: string[];
	contentGenerationMode: string[];
};

export type SeederChartDataType = {
	timestamp: Date;
	value: number;
	label?: string;
	category?: string;
};

export type SeederAlertType = {
	id: string;
	type: 'queue_depth' | 'error_rate' | 'processing_delay' | 'connector_failure' | 'strategy_failure';
	severity: 'low' | 'medium' | 'high' | 'critical';
	message: string;
	timestamp: Date;
	acknowledged: boolean;
	resolvedAt?: Date;
	component: string;
	details: Record<string, any>;
};

export type SeederComponentPropsType = {
	data?: SeederDashboardDataType;
	loading?: boolean;
	error?: string | null;
	onRefresh?: () => void;
	onTriggerOperation?: (operation: SeederManualTriggerType) => Promise<void>;
	onUpdateSettings?: (settings: Partial<SeederSettingsType>) => Promise<void>;
	onAcknowledgeAlert?: (alertId: string) => Promise<void>;
	children?: ReactNode;
};
