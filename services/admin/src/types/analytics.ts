import type { ReactNode } from 'react';

export type TimeframeType = 'hour' | 'day' | 'week' | 'month' | 'quarter' | 'year';
export type MetricType = 'count' | 'rate' | 'percentage' | 'duration' | 'bytes' | 'score';
export type ChartType = 'line' | 'bar' | 'pie' | 'area' | 'scatter' | 'radar';
export type ExportFormat = 'csv' | 'json' | 'pdf' | 'xlsx';
export type ReportType = 'system' | 'crawling' | 'domains' | 'performance' | 'seeder' | 'custom';

export type AnalyticsTimeframe = {
	period: TimeframeType;
	start: Date;
	end: Date;
	label: string;
};

export type ChartDataPoint = {
	timestamp: Date;
	value: number;
	label?: string;
	category?: string;
	metadata?: Record<string, any>;
};

export type MetricDefinition = {
	id: string;
	name: string;
	description: string;
	type: MetricType;
	unit: string;
	category: string;
	isRealTime: boolean;
	refreshInterval?: number;
	alertThresholds?: {
		warning: number;
		critical: number;
	};
};

export type SystemMetrics = {
	domains: {
		total: number;
		public: number;
		private: number;
		byCategory: Record<string, number>;
		byStatus: Record<string, number>;
		byTLD: Record<string, number>;
		growth: {
			daily: number;
			weekly: number;
			monthly: number;
		};
	};
	crawling: {
		totalJobs: number;
		activeJobs: number;
		completedJobs: number;
		failedJobs: number;
		successRate: number;
		averageTime: number;
		jobsPerHour: number;
		errorRate: number;
		byType: Record<string, {
			count: number;
			successRate: number;
			averageTime: number;
		}>;
		throughput: {
			current: number;
			peak: number;
			average: number;
		};
	};
	seeding: {
		queueSize: number;
		processingRate: number;
		discoveryRate: number;
		contentGenerationRate: number;
		bySource: Record<string, {
			discovered: number;
			processed: number;
			errorRate: number;
		}>;
		strategies: Record<string, {
			enabled: boolean;
			successRate: number;
			averageConfidence: number;
		}>;
	};
	performance: {
		averageResponseTime: number;
		uptime: number;
		errorRate: number;
		throughput: number;
		resourceUsage: {
			cpu: number;
			memory: number;
			disk: number;
			network: number;
			loadAverage: number;
			heapTotal: number;
			rss: number;
		};
		services: Record<string, {
			responseTime: number;
			uptime: number;
			errorRate: number;
			requestCount: number;
		}>;
	};
	database: {
		connections: Record<string, {
			active: number;
			idle: number;
			total: number;
			maxConnections: number;
		}>;
		performance: Record<string, {
			queryTime: number;
			slowQueries: number;
			errorRate: number;
			throughput: number;
		}>;
		storage: Record<string, {
			used: number;
			available: number;
			total: number;
			growth: number;
		}>;
		indexes: Record<string, {
			efficiency: number;
			usage: number;
			size: number;
		}>;
	};
	users: {
		totalUsers: number;
		activeUsers: number;
		loginAttempts: {
			successful: number;
			failed: number;
			blocked: number;
		};
		sessions: {
			active: number;
			total: number;
			averageDuration: number;
		};
		activity: Record<string, {
			actions: number;
			lastActivity: Date;
			ipAddresses: string[];
		}>;
	};
};

export type AnalyticsChart = {
	id: string;
	title: string;
	description: string;
	type: ChartType;
	data: ChartDataPoint[];
	config: {
		xAxis: string;
		yAxis: string;
		color?: string;
		colors?: string[];
		showGrid?: boolean;
		showLegend?: boolean;
		showTooltip?: boolean;
		height?: number;
	};
	metadata: {
		lastUpdated: Date;
		dataSource: string;
		refreshInterval: number;
	};
};

export type ReportConfig = {
	id?: string;
	name: string;
	description: string;
	type: ReportType;
	timeframe: AnalyticsTimeframe;
	metrics: string[];
	charts: string[];
	filters: Record<string, any>;
	format: ExportFormat;
	includeCharts: boolean;
	includeRawData: boolean;
	schedule?: {
		enabled: boolean;
		frequency: 'daily' | 'weekly' | 'monthly';
		time: string;
		timezone: string;
		recipients: string[];
	};
	template?: {
		header: string;
		footer: string;
		customSections: Array<{
			title: string;
			content: string;
			type: 'text' | 'chart' | 'table';
		}>;
	};
};

export type GeneratedReport = {
	id: string;
	config: ReportConfig;
	generatedAt: Date;
	generatedBy: string;
	status: 'generating' | 'completed' | 'failed' | 'expired';
	fileUrl?: string;
	fileName?: string;
	fileSize?: number;
	downloadCount: number;
	expiresAt: Date;
	error?: string;
	metadata: {
		recordCount: number;
		chartCount: number;
		processingTime: number;
	};
};

export type AnalyticsDashboard = {
	id: string;
	name: string;
	description: string;
	isDefault: boolean;
	layout: Array<{
		id: string;
		chartId: string;
		position: {
			x: number;
			y: number;
			width: number;
			height: number;
		};
		config: Record<string, any>;
	}>;
	filters: Record<string, any>;
	refreshInterval: number;
	createdBy: string;
	createdAt: Date;
	updatedAt: Date;
};

export type MetricsAlert = {
	id: string;
	name: string;
	description: string;
	metricId: string;
	condition: {
		operator: '>' | '<' | '=' | '>=' | '<=' | '!=' | 'contains' | 'not_contains';
		value: number | string;
		duration: number; // minutes
	};
	severity: 'info' | 'warning' | 'critical';
	enabled: boolean;
	notifications: {
		email: string[];
		webhook: string[];
		inApp: boolean;
	};
	lastTriggered?: Date;
	triggerCount: number;
	createdAt: Date;
	updatedAt: Date;
};

export type AnalyticsFilter = {
	field: string;
	operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than' | 'between' | 'in' | 'not_in';
	value: any;
	label: string;
};

export type AnalyticsQuery = {
	metrics: string[];
	timeframe: AnalyticsTimeframe;
	filters: AnalyticsFilter[];
	groupBy?: string[];
	orderBy?: {
		field: string;
		direction: 'asc' | 'desc';
	};
	limit?: number;
	offset?: number;
};

export type AnalyticsResponse = {
	data: ChartDataPoint[];
	metadata: {
		totalRecords: number;
		queryTime: number;
		cacheHit: boolean;
		lastUpdated: Date;
	};
	aggregations?: Record<string, {
		sum: number;
		avg: number;
		min: number;
		max: number;
		count: number;
	}>;
};

export type TrendAnalysis = {
	metric: string;
	timeframe: AnalyticsTimeframe;
	trend: 'increasing' | 'decreasing' | 'stable' | 'volatile';
	changePercent: number;
	changeValue: number;
	confidence: number;
	prediction?: {
		nextValue: number;
		confidence: number;
		timeframe: string;
	};
	anomalies: Array<{
		timestamp: Date;
		value: number;
		expectedValue: number;
		severity: 'low' | 'medium' | 'high';
		description: string;
	}>;
};

export type BusinessIntelligence = {
	domainGrowth: {
		trends: TrendAnalysis[];
		forecasts: Array<{
			period: string;
			predicted: number;
			confidence: number;
		}>;
		seasonality: {
			detected: boolean;
			pattern: string;
			strength: number;
		};
	};
	marketAnalysis: {
		topCategories: Array<{
			category: string;
			count: number;
			growth: number;
			marketShare: number;
		}>;
		emergingTrends: Array<{
			trend: string;
			strength: number;
			domains: string[];
			timeframe: string;
		}>;
		competitiveInsights: Array<{
			metric: string;
			ourValue: number;
			industryAverage: number;
			percentile: number;
		}>;
	};
	performanceInsights: {
		bottlenecks: Array<{
			component: string;
			severity: number;
			impact: string;
			recommendation: string;
		}>;
		optimization: Array<{
			area: string;
			currentValue: number;
			potentialImprovement: number;
			effort: 'low' | 'medium' | 'high';
		}>;
		resourceUtilization: {
			efficiency: number;
			waste: number;
			recommendations: string[];
		};
	};
};

export type AnalyticsComponentProps = {
	className?: string;
	children?: ReactNode;
	loading?: boolean;
	error?: string;
	onRefresh?: () => void;
	refreshInterval?: number;
};

export type ChartComponentProps = AnalyticsComponentProps & {
	chart: AnalyticsChart;
	height?: number;
	interactive?: boolean;
	showControls?: boolean;
	onDataPointClick?: (point: ChartDataPoint) => void;
};

export type DashboardComponentProps = AnalyticsComponentProps & {
	dashboard: AnalyticsDashboard;
	editable?: boolean;
	onLayoutChange?: (layout: AnalyticsDashboard['layout']) => void;
	onChartAdd?: (chartId: string) => void;
	onChartRemove?: (chartId: string) => void;
};

export type ReportComponentProps = AnalyticsComponentProps & {
	config: ReportConfig;
	onConfigChange?: (config: ReportConfig) => void;
	onGenerate?: (config: ReportConfig) => void;
	onSchedule?: (config: ReportConfig) => void;
};
