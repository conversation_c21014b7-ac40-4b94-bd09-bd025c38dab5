type UserRoleType = 'super_admin' | 'admin' | 'viewer';

type PermissionType =
  | 'users.view'
  | 'users.create'
  | 'users.edit'
  | 'users.delete'
  | 'users.manage_permissions'
  | 'services.view'
  | 'services.restart'
  | 'services.configure'
  | 'domains.view'
  | 'domains.edit'
  | 'domains.delete'
  | 'domains.bulk_operations'
  | 'crawl.view'
  | 'crawl.create'
  | 'crawl.manage'
  | 'crawl.configure'
  | 'seeder.view'
  | 'seeder.manage'
  | 'seeder.configure'
  | 'analytics.view'
  | 'analytics.export'
  | 'config.view'
  | 'config.edit'
  | 'config.deploy'
  | 'logs.view'
  | 'logs.export'
  | 'ai.view'
  | 'ai.configure'
  | 'ai.manage';

type AdminUserType =
{
	id: string;
	username: string;
	role: UserRoleType;
	permissions: PermissionType[];
	createdAt: Date;
	lastLogin: Date | null;
	isActive: boolean;
	sessionCount: number;
	passwordLastChanged: Date;
	mustChangePassword: boolean;
	email?: string;
	fullName?: string;
	loginAttempts: number;
	lockedUntil: Date | null;
	passwordHistory: string[];
};

type UserActivityType =
{
	id: string;
	userId: string;
	username: string;
	action: string;
	resource: string;
	resourceId?: string;
	timestamp: Date;
	ipAddress: string;
	userAgent: string;
	success: boolean;
	details: Record<string, any>;
	sessionId: string;
};

type UserSessionType =
{
	sessionId: string;
	userId: string;
	username: string;
	role: UserRoleType;
	permissions: PermissionType[];
	createdAt: Date;
	lastActivity: Date;
	expiresAt: Date;
	ipAddress: string;
	userAgent: string;
	isActive: boolean;
	details?: Record<string, any>;
};

type CreateUserRequestType =
{
	username: string;
	password?: string;
	role: UserRoleType;
	permissions: PermissionType[];
	email?: string;
	fullName?: string;
	mustChangePassword?: boolean;
	generatePassword?: boolean;
};

type UpdateUserRequestType =
{
	role?: UserRoleType;
	permissions?: PermissionType[];
	isActive?: boolean;
	email?: string;
	fullName?: string;
	mustChangePassword?: boolean;
};

type ResetPasswordRequestType =
{
	email: string;
	token: string;
	newPassword: string;
	confirmPassword: string;
};

type PasswordPolicyType =
{
	minLength: number;
	requireUppercase: boolean;
	requireLowercase: boolean;
	requireNumbers: boolean;
	requireSpecialChars: boolean;
	maxAge: number; // days
	historyCount: number;
	maxLoginAttempts: number;
	lockoutDuration: number; // minutes
};

type UserAnalyticsType =
{
	totalUsers: number;
	activeUsers: number;
	lockedUsers: number;
	usersByRole: Record<UserRoleType, number>;
	loginStats: {
		totalLogins: number;
		failedLogins: number;
		uniqueUsers: number;
		averageSessionDuration: number;
	};
	securityMetrics: {
		passwordExpired: number;
		accountsLocked: number;
		suspiciousActivity: number;
	};
	featureUsage: Record<string, number>;
};

type AuditLogType =
{
	id: string;
	userId: string;
	username: string;
	action: string;
	resource: string;
	resourceId?: string;
	oldValues?: Record<string, any>;
	newValues?: Record<string, any>;
	timestamp: Date;
	ipAddress: string;
	userAgent: string;
	success: boolean;
	errorMessage?: string;
	severity: 'low' | 'medium' | 'high' | 'critical';
};

type SecurityAlertType =
{
	id: string;
	type: 'failed_login' | 'account_locked' | 'permission_escalation' | 'suspicious_activity' | 'password_breach';
	userId?: string;
	username?: string;
	description: string;
	severity: 'low' | 'medium' | 'high' | 'critical';
	timestamp: Date;
	ipAddress: string;
	userAgent: string;
	resolved: boolean;
	resolvedBy?: string;
	resolvedAt?: Date;
	metadata: Record<string, any>;
};

// Role-based permission mappings
const ROLE_PERMISSIONS: Record<UserRoleType, PermissionType[]> =
{
	super_admin: [
		'users.view', 'users.create', 'users.edit', 'users.delete', 'users.manage_permissions',
		'services.view', 'services.restart', 'services.configure',
		'domains.view', 'domains.edit', 'domains.delete', 'domains.bulk_operations',
		'crawl.view', 'crawl.create', 'crawl.manage', 'crawl.configure',
		'seeder.view', 'seeder.manage', 'seeder.configure',
		'analytics.view', 'analytics.export',
		'config.view', 'config.edit', 'config.deploy',
		'logs.view', 'logs.export',
		'ai.view', 'ai.configure', 'ai.manage',
	],
	admin: [
		'users.view',
		'services.view', 'services.configure',
		'domains.view', 'domains.edit', 'domains.bulk_operations',
		'crawl.view', 'crawl.create', 'crawl.manage',
		'seeder.view', 'seeder.manage',
		'analytics.view', 'analytics.export',
		'config.view', 'config.edit',
		'logs.view',
		'ai.view', 'ai.manage',
	],
	viewer: [
		'users.view',
		'services.view',
		'domains.view',
		'crawl.view',
		'seeder.view',
		'analytics.view',
		'config.view',
		'logs.view',
		'ai.view',
	],
};

const DEFAULT_PASSWORD_POLICY: PasswordPolicyType =
{
	minLength: 12,
	requireUppercase: true,
	requireLowercase: true,
	requireNumbers: true,
	requireSpecialChars: true,
	maxAge: 90,
	historyCount: 5,
	maxLoginAttempts: 5,
	lockoutDuration: 30,
};

export type {
	UserRoleType,
	PermissionType,
	AdminUserType,
	UserActivityType,
	UserSessionType,
	CreateUserRequestType,
	UpdateUserRequestType,
	ResetPasswordRequestType,
	PasswordPolicyType,
	UserAnalyticsType,
	AuditLogType,
	SecurityAlertType,
};

export {
	ROLE_PERMISSIONS,
	DEFAULT_PASSWORD_POLICY,
};
