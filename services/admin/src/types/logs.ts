
type LogLevelType =
	| 'debug'
	| 'info'
	| 'warn'
	| 'error'
	| 'fatal'
;

type ServiceNameType =
	| 'web-app'
	| 'worker'
	| 'domain-seeder'
	| 'admin'
;

type LogEntryType =
{
	id: string;
	timestamp: Date;
	level: LogLevelType;
	service: ServiceNameType;
	message: string;
	correlationId?: string;
	userId?: string;
	action?: string;
	metadata?: Record<string, unknown>;
	stackTrace?: string;
	errorType?: string;
	requestId?: string;
	duration?: number;
	statusCode?: number;
};

type LogFilterType =
{
	services: ServiceNameType[];
	levels: LogLevelType[];
	startTime?: Date;
	endTime?: Date;
	correlationId?: string;
	userId?: string;
	action?: string;
	errorType?: string;
	searchQuery?: string;
	useRegex?: boolean;
};

type SavedSearchType =
{
	id: string;
	name: string;
	description?: string;
	filter: LogFilterType;
	createdAt: Date;
	createdBy: string;
	isPublic: boolean;
};

type LogAnalyticsType =
{
	totalLogs: number;
	errorRate: number;
	topErrors: Array<{
		type: string;
		count: number;
		percentage: number;
	}>;
	serviceDistribution: Record<ServiceNameType, number>;
	levelDistribution: Record<LogLevelType, number>;
	trendsOverTime: Array<{
		timestamp: Date;
		count: number;
		errorCount: number;
	}>;
	averageResponseTime: number;
	slowestRequests: Array<{
		correlationId: string;
		duration: number;
		service: ServiceNameType;
		endpoint: string;
	}>;
};

type ErrorPatternType =
{
	id: string;
	pattern: string;
	description: string;
	severity: 'low' | 'medium' | 'high' | 'critical';
	occurrences: number;
	firstSeen: Date;
	lastSeen: Date;
	services: ServiceNameType[];
	suggestedResolution?: string;
	isResolved: boolean;
};

type LogAlertType =
{
	id: string;
	name: string;
	description: string;
	enabled: boolean;
	conditions: {
		services: ServiceNameType[];
		levels: LogLevelType[];
		pattern?: string;
		threshold: number;
		timeWindow: number; // minutes
		operator: 'greater_than' | 'less_than' | 'equals';
	};
	actions: {
		email?: string[];
		webhook?: string;
		createIncident?: boolean;
	};
	lastTriggered?: Date;
	triggerCount: number;
	createdAt: Date;
	createdBy: string;
};

type LogExportConfigType =
{
	format: 'json' | 'csv' | 'txt';
	filter: LogFilterType;
	includeMetadata: boolean;
	includeStackTrace: boolean;
	maxRecords?: number;
	compression?: 'gzip' | 'zip';
};

type LogArchivePolicyType =
{
	id: string;
	name: string;
	retentionDays: number;
	compressionEnabled: boolean;
	services: ServiceNameType[];
	levels: LogLevelType[];
	archiveLocation: string;
	isActive: boolean;
	lastRun?: Date;
	nextRun: Date;
};

type PerformanceMetricType =
{
	timestamp: Date;
	service: ServiceNameType;
	endpoint?: string;
	responseTime: number;
	statusCode: number;
	memoryUsage?: number;
	cpuUsage?: number;
	dbQueryTime?: number;
	cacheHitRate?: number;
};

type LogLevelConfigType =
{
	service: ServiceNameType;
	currentLevel: LogLevelType;
	temporaryLevel?: LogLevelType;
	temporaryUntil?: Date;
	lastModified: Date;
	modifiedBy: string;
};

type CorrelationTraceType =
{
	correlationId: string;
	startTime: Date;
	endTime?: Date;
	totalDuration?: number;
	services: Array<{
		service: ServiceNameType;
		startTime: Date;
		endTime?: Date;
		duration?: number;
		logs: LogEntryType[];
	}>;
	status: 'in_progress' | 'completed' | 'failed';
	errorCount: number;
};

export type {
	LogLevelType,
	ServiceNameType,
	LogEntryType,
	LogFilterType,
	SavedSearchType,
	LogAnalyticsType,
	ErrorPatternType,
	LogAlertType,
	LogExportConfigType,
	LogArchivePolicyType,
	PerformanceMetricType,
	LogLevelConfigType,
	CorrelationTraceType,
};
