
export type DatabaseType = 'scylla' | 'mariadb' | 'redis' | 'manticore';

export type DatabaseStatus = 'healthy' | 'degraded' | 'unhealthy' | 'disconnected';

export type MaintenanceOperationType = 'cleanup' | 'optimize' | 'archive' | 'repair' | 'backup' | 'restore';

export type AlertSeverity = 'low' | 'medium' | 'high' | 'critical';

export type DatabaseConnectionInfo =
{
	database: DatabaseType;
	host: string;
	port: number;
	status: DatabaseStatus;
	connected: boolean;
	responseTime: number;
	lastCheck: Date;
	version: string;
	uptime: number;
};

export type ConnectionPoolMetrics =
{
	active: number;
	idle: number;
	total: number;
	maxSize: number;
	waitingRequests: number;
	averageWaitTime: number;
};

export type DatabasePerformanceMetrics =
{
	queryResponseTime:
	{
		average: number;
		p50: number;
		p95: number;
		p99: number;
	};
	transactionMetrics:
	{
		total: number;
		successful: number;
		failed: number;
		averageDuration: number;
	};
	connectionMetrics:
	{
		total: number;
		percentage: number;
	};
	cacheMetrics:
	{
		hitRatio: number;
		missRatio: number;
		totalQueries: number;
	};
	throughput: {
		queriesPerSecond: number;
		readsPerSecond: number;
		writesPerSecond: number;
	};
	errorRate: number;
	slowQueries: number;
};

export type DatabaseMaintenanceTask =
{
	id: string;
	database: DatabaseType;
	operation: MaintenanceOperationType;
	status: 'pending' | 'running' | 'completed' | 'failed';
	progress: number;
	startedAt: Date | null;
	completedAt: Date | null;
	duration: number | null;
	errorMessage: string | null;
	parameters: Record<string, any>;
	safetyChecks: {
		backupCreated: boolean;
		resourcesAvailable: boolean;
		noActiveTransactions: boolean;
	};
};

export type DataIntegrityCheck =
{
	id: string;
	database: DatabaseType;
	checkType: 'consistency' | 'corruption' | 'foreign_keys' | 'indexes' | 'schema';
	status: 'running' | 'completed' | 'failed';
	issues: Array<{
		severity: 'low' | 'medium' | 'high' | 'critical';
		description: string;
		table: string;
		affectedRows: number;
		recommendation: string;
	}>;
	startedAt: Date;
	completedAt: Date | null;
	summary: {
		totalChecks: number;
		issuesFound: number;
		criticalIssues: number;
		repairableIssues: number;
	};
};

export type BackupInfo =
{
	id: string;
	database: DatabaseType;
	type: 'full' | 'incremental' | 'differential';
	status: 'running' | 'completed' | 'failed';
	size: number;
	location: string;
	createdAt: Date;
	completedAt: Date | null;
	retentionPolicy: {
		keepDays: number;
		maxBackups: number;
	};
	metadata: {
		tables: string[];
		recordCount: number;
		compression: string;
		encryption: boolean;
	};
};

export type RestoreOperation =
{
	id: string;
	backupId: string;
	database: DatabaseType;
	status: 'pending' | 'running' | 'completed' | 'failed';
	progress: number;
	pointInTime: Date | null;
	targetTables: string[];
	startedAt: Date;
	completedAt: Date | null;
	errorMessage: string | null;
	preRestoreBackupId: string | null;
};

export type SlowQuery =
{
	id: string;
	database: DatabaseType;
	query: string;
	executionTime: number;
	timestamp: Date;
	table: string;
	rowsExamined: number;
	rowsReturned: number;
	indexesUsed: string[];
	optimizationSuggestions: string[];
	executionPlan: any;
};

export type DatabaseIndex =
{
	name: string;
	table: string;
	columns: string[];
	type: 'primary' | 'unique' | 'index' | 'fulltext';
	size: number;
	cardinality: number;
	usage: {
		reads: number;
		writes: number;
		lastUsed: Date | null;
	};
	efficiency: number;
	recommendations: string[];
};

export type SchemaMigration =
{
	id: string;
	version: string;
	database: DatabaseType;
	description: string;
	status: 'pending' | 'running' | 'completed' | 'failed' | 'rolled_back';
	upScript: string;
	downScript: string;
	appliedAt: Date | null;
	rolledBackAt: Date | null;
	dependencies: string[];
	checksum: string;
};

export type DatabaseAlert =
{
	id: string;
	database: DatabaseType;
	type: 'performance' | 'connection' | 'capacity' | 'security' | 'integrity';
	severity: AlertSeverity;
	title: string;
	description: string;
	metric: string;
	threshold: number;
	currentValue: number;
	triggeredAt: Date;
	acknowledgedAt: Date | null;
	resolvedAt: Date | null;
	actions: string[];
};

export type CapacityPlan =
{
	database: DatabaseType;
	currentUsage: {
		storage: number;
		connections: number;
		cpu: number;
		memory: number;
	};
	projections: {
		timeframe: '1month' | '3months' | '6months' | '1year';
		storage: number;
		connections: number;
		cpu: number;
		memory: number;
	}[];
	recommendations: {
		type: 'scale_up' | 'scale_out' | 'optimize' | 'archive';
		priority: 'low' | 'medium' | 'high';
		description: string;
		estimatedCost: number;
		timeline: string;
	}[];
};

export type SecurityAudit =
{
	database: DatabaseType;
	timestamp: Date;
	accessPatterns: {
		user: string;
		queries: number;
		tables: string[];
		suspiciousActivity: boolean;
		lastAccess: Date;
	}[];
	suspiciousQueries: {
		query: string;
		user: string;
		timestamp: Date;
		riskLevel: 'low' | 'medium' | 'high';
		reason: string;
	}[];
	complianceChecks: {
		check: string;
		status: 'pass' | 'fail' | 'warning';
		details: string;
	}[];
};

export type DatabaseOperationsConfig =
{
	maintenance: {
		autoCleanup: boolean;
		cleanupSchedule: string;
		optimizationSchedule: string;
		maxMaintenanceTime: number;
	};
	backup: {
		enabled: boolean;
		schedule: string;
		retentionDays: number;
		compression: boolean;
		encryption: boolean;
		location: string;
	};
	monitoring: {
		alertThresholds: {
			responseTime: number;
			errorRate: number;
			connectionUsage: number;
			storageUsage: number;
		};
		checkInterval: number;
		enableSlowQueryLog: boolean;
		slowQueryThreshold: number;
	};
	security: {
		auditEnabled: boolean;
		auditRetentionDays: number;
		suspiciousActivityThreshold: number;
		complianceChecks: string[];
	};
};
