'use client';

import {
	Card,
	Title,
	Text,
	Group,
	Stack,
	Badge,
	Progress,
	SimpleGrid,
	ActionIcon,
	RingProgress,
	Alert,
	Tabs,
} from '@mantine/core';
import {
	IconRefresh,
	IconAlertTriangle,
	IconInfoCircle,
	IconFileText,
	IconRobot,
	IconTemplate,
	IconBrandOpenai,
	IconCheck,
	IconX,
} from '@tabler/icons-react';
import { useState } from 'react';
import {
	PieChart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, LineChart, Line,
} from 'recharts';

import type { SeederComponentPropsType } from '@/types/seeder';

export function SeederContentGeneration({
	data, loading, error, onRefresh,
}: SeederComponentPropsType)
{
	const [activeTab, setActiveTab] = useState<string>('overview');

	if (!data)
	{
		return (
			<Card withBorder>
				<Title order={3} mb="md">Content Generation</Title>
				<Text>Loading content generation data...</Text>
			</Card>
		);
	}

	const contentGen = data.contentGeneration;
	const totalGenerated = contentGen.preGeneratedCount + contentGen.liveCount;
	const successRate = totalGenerated / (totalGenerated + contentGen.validationFailures);

	// Prepare chart data
	const generationModeData = [
		{ name: 'Self Generated', value: contentGen.generationModes.selfGenerated, color: '#228be6' },
		{ name: 'AI Generated', value: contentGen.generationModes.aiGenerated, color: '#40c057' },
		{ name: 'Template Based', value: contentGen.generationModes.templateBased, color: '#fab005' },
		{ name: 'Hybrid', value: contentGen.generationModes.hybridGenerated, color: '#fd7e14' },
	];

	const qualityDistributionData = [
		{ name: 'High Quality', value: contentGen.qualityMetrics.highQualityCount, color: '#40c057' },
		{ name: 'Medium Quality', value: contentGen.qualityMetrics.mediumQualityCount, color: '#fab005' },
		{ name: 'Low Quality', value: contentGen.qualityMetrics.lowQualityCount, color: '#fd7e14' },
	];

	const categoryData = Object.entries(contentGen.categoriesAssigned).map(([category, count]) => ({
		category,
		count,
	}));

	const processingTimeData = [
		{ name: 'Generation', time: contentGen.processingTimes.averageGenerationTime, color: '#228be6' },
		{ name: 'Validation', time: contentGen.processingTimes.averageValidationTime, color: '#40c057' },
		{ name: 'Category Assignment', time: contentGen.processingTimes.averageCategoryAssignmentTime, color: '#fab005' },
	];

	// Generate mock trend data
	const trendData = Array.from({ length: 24 }, (_, i) =>
	{
		const hour = new Date();
		hour.setHours(hour.getHours() - (23 - i));
		return {
			time: hour.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
			generated: Math.floor(Math.random() * 500) + 200,
			validated: Math.floor(Math.random() * 450) + 180,
			failed: Math.floor(Math.random() * 50) + 10,
		};
	});

	return (
		<Stack gap="md">
			{/* Content Generation Overview */}
			<Card withBorder>
				<Group justify="space-between" mb="md">
					<Title order={3}>Content Generation</Title>
					<Group>
						<Badge color={successRate > 0.9 ? 'green' : successRate > 0.8 ? 'yellow' : 'red'} variant="filled">
							{Math.round(successRate * 100)}% Success Rate
						</Badge>
						<ActionIcon variant="outline" onClick={onRefresh} loading={loading}>
							<IconRefresh size={16} />
						</ActionIcon>
					</Group>
				</Group>

				<SimpleGrid cols={{ base: 2, sm: 4 }} mb="md">
					<div>
						<Text size="xs" tt="uppercase" fw={700} c="dimmed">Total Generated</Text>
						<Text fw={700} size="xl">{totalGenerated.toLocaleString()}</Text>
					</div>
					<div>
						<Text size="xs" tt="uppercase" fw={700} c="dimmed">Pre-generated</Text>
						<Text fw={700} size="xl" c="teal">{contentGen.preGeneratedCount.toLocaleString()}</Text>
					</div>
					<div>
						<Text size="xs" tt="uppercase" fw={700} c="dimmed">Live Generated</Text>
						<Text fw={700} size="xl" c="orange">{contentGen.liveCount.toLocaleString()}</Text>
					</div>
					<div>
						<Text size="xs" tt="uppercase" fw={700} c="dimmed">Validation Failures</Text>
						<Text fw={700} size="xl" c="red">{contentGen.validationFailures.toLocaleString()}</Text>
					</div>
				</SimpleGrid>

				<SimpleGrid cols={{ base: 1, sm: 3 }} mb="md">
					<div>
						<Text size="sm" c="dimmed">Avg Quality Score</Text>
						<Group>
							<Text fw={600} size="lg">{Math.round(contentGen.qualityMetrics.averageQualityScore * 100)}%</Text>
							<Progress
								value={contentGen.qualityMetrics.averageQualityScore * 100}
								color={contentGen.qualityMetrics.averageQualityScore > 0.8 ? 'green' : contentGen.qualityMetrics.averageQualityScore > 0.6 ? 'yellow' : 'red'}
								size="sm"
								style={{ flex: 1 }}
							/>
						</Group>
					</div>
					<div>
						<Text size="sm" c="dimmed">Avg Summary Length</Text>
						<Text fw={600} size="lg">{contentGen.averageSummaryLength} chars</Text>
					</div>
					<div>
						<Text size="sm" c="dimmed">Tags Generated</Text>
						<Text fw={600} size="lg">{contentGen.tagsGenerated.toLocaleString()}</Text>
					</div>
				</SimpleGrid>

				{successRate < 0.8 && (
					<Alert
						icon={<IconAlertTriangle size={16} />}
						title="Content Generation Performance Warning"
						color="yellow"
						mb="md"
					>
						Content generation success rate is below 80%. Consider reviewing validation rules or generation parameters.
					</Alert>
				)}
			</Card>

			<Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'overview')}>
				<Tabs.List>
					<Tabs.Tab value="overview">Overview</Tabs.Tab>
					<Tabs.Tab value="quality">Quality Analysis</Tabs.Tab>
					<Tabs.Tab value="categories">Categories</Tabs.Tab>
					<Tabs.Tab value="performance">Performance</Tabs.Tab>
				</Tabs.List>

				<Tabs.Panel value="overview" pt="md">
					<SimpleGrid cols={{ base: 1, lg: 2 }}>
						{/* Generation Mode Distribution */}
						<Card withBorder>
							<Title order={4} mb="md">Generation Mode Distribution</Title>
							<div style={{ height: 250 }}>
								<ResponsiveContainer width="100%" height="100%">
									<PieChart>
										<Pie
											data={generationModeData}
											cx="50%"
											cy="50%"
											innerRadius={40}
											outerRadius={100}
											paddingAngle={5}
											dataKey="value"
										>
											{generationModeData.map((entry, index) => (
												<Cell key={`cell-${index}`} fill={entry.color} />
											))}
										</Pie>
										<RechartsTooltip />
									</PieChart>
								</ResponsiveContainer>
							</div>
							<SimpleGrid cols={2} mt="md">
								{generationModeData.map(mode => (
									<Group key={mode.name}>
										<div
											style={{
												width: 12,
												height: 12,
												backgroundColor: mode.color,
												borderRadius: 2,
											}}
										/>
										<div>
											<Text size="sm" fw={500}>{mode.name}</Text>
											<Text size="xs" c="dimmed">{mode.value.toLocaleString()}</Text>
										</div>
									</Group>
								))}
							</SimpleGrid>
						</Card>

						{/* Content Generation Trends */}
						<Card withBorder>
							<Title order={4} mb="md">Generation Trends (24h)</Title>
							<div style={{ height: 250 }}>
								<ResponsiveContainer width="100%" height="100%">
									<LineChart data={trendData}>
										<CartesianGrid strokeDasharray="3 3" />
										<XAxis dataKey="time" />
										<YAxis />
										<RechartsTooltip />
										<Line type="monotone" dataKey="generated" stroke="#228be6" strokeWidth={2} name="Generated" />
										<Line type="monotone" dataKey="validated" stroke="#40c057" strokeWidth={2} name="Validated" />
										<Line type="monotone" dataKey="failed" stroke="#fa5252" strokeWidth={2} name="Failed" />
									</LineChart>
								</ResponsiveContainer>
							</div>
						</Card>
					</SimpleGrid>
				</Tabs.Panel>

				<Tabs.Panel value="quality" pt="md">
					<SimpleGrid cols={{ base: 1, lg: 2 }}>
						{/* Quality Distribution */}
						<Card withBorder>
							<Title order={4} mb="md">Quality Distribution</Title>
							<div style={{ height: 250 }}>
								<ResponsiveContainer width="100%" height="100%">
									<PieChart>
										<Pie
											data={qualityDistributionData}
											cx="50%"
											cy="50%"
											innerRadius={40}
											outerRadius={100}
											paddingAngle={5}
											dataKey="value"
										>
											{qualityDistributionData.map((entry, index) => (
												<Cell key={`cell-${index}`} fill={entry.color} />
											))}
										</Pie>
										<RechartsTooltip />
									</PieChart>
								</ResponsiveContainer>
							</div>
							<SimpleGrid cols={1} mt="md">
								{qualityDistributionData.map(quality => (
									<Group key={quality.name} justify="space-between">
										<Group>
											<div
												style={{
													width: 12,
													height: 12,
													backgroundColor: quality.color,
													borderRadius: 2,
												}}
											/>
											<Text size="sm" fw={500}>{quality.name}</Text>
										</Group>
										<Text size="sm" c="dimmed">{quality.value.toLocaleString()}</Text>
									</Group>
								))}
							</SimpleGrid>
						</Card>

						{/* Quality Metrics */}
						<Card withBorder>
							<Title order={4} mb="md">Quality Metrics</Title>
							<Stack gap="md">
								<div>
									<Group justify="space-between" mb="xs">
										<Text size="sm">Overall Quality Score</Text>
										<Text size="sm" fw={600}>{Math.round(contentGen.qualityMetrics.averageQualityScore * 100)}%</Text>
									</Group>
									<Progress
										value={contentGen.qualityMetrics.averageQualityScore * 100}
										color={contentGen.qualityMetrics.averageQualityScore > 0.8 ? 'green' : contentGen.qualityMetrics.averageQualityScore > 0.6 ? 'yellow' : 'red'}
										size="lg"
									/>
								</div>

								<SimpleGrid cols={2}>
									<div>
										<Text size="xs" c="dimmed">High Quality</Text>
										<Text fw={600}>{contentGen.qualityMetrics.highQualityCount.toLocaleString()}</Text>
										<Text size="xs" c="green">
											{Math.round((contentGen.qualityMetrics.highQualityCount / totalGenerated) * 100)}%
										</Text>
									</div>
									<div>
										<Text size="xs" c="dimmed">Medium Quality</Text>
										<Text fw={600}>{contentGen.qualityMetrics.mediumQualityCount.toLocaleString()}</Text>
										<Text size="xs" c="yellow">
											{Math.round((contentGen.qualityMetrics.mediumQualityCount / totalGenerated) * 100)}%
										</Text>
									</div>
								</SimpleGrid>

								<div>
									<Text size="sm" fw={600} mb="xs">Content Validation</Text>
									<Group>
										<RingProgress
											size={80}
											thickness={8}
											sections={[
												{
													value: (totalGenerated / (totalGenerated + contentGen.validationFailures)) * 100,
													color: 'green',
												},
											]}
											label={(
												<Text size="xs" ta="center">
													{Math.round((totalGenerated / (totalGenerated + contentGen.validationFailures)) * 100)}%
												</Text>
											)}
										/>
										<div>
											<Text size="sm">Validation Success Rate</Text>
											<Text size="xs" c="dimmed">
												{totalGenerated.toLocaleString()} passed, {contentGen.validationFailures.toLocaleString()} failed
											</Text>
										</div>
									</Group>
								</div>
							</Stack>
						</Card>
					</SimpleGrid>
				</Tabs.Panel>

				<Tabs.Panel value="categories" pt="md">
					<Card withBorder>
						<Title order={4} mb="md">Category Assignment Distribution</Title>
						<div style={{ height: 400 }}>
							<ResponsiveContainer width="100%" height="100%">
								<BarChart data={categoryData} layout="horizontal">
									<CartesianGrid strokeDasharray="3 3" />
									<XAxis type="number" />
									<YAxis dataKey="category" type="category" width={100} />
									<RechartsTooltip />
									<Bar dataKey="count" fill="#228be6" />
								</BarChart>
							</ResponsiveContainer>
						</div>

						<SimpleGrid cols={{ base: 2, sm: 3, lg: 6 }} mt="md">
							{Object.entries(contentGen.categoriesAssigned).map(([category, count]) => (
								<div key={category}>
									<Text size="sm" fw={500}>{category}</Text>
									<Text size="xl" fw={700}>{count.toLocaleString()}</Text>
									<Text size="xs" c="dimmed">
										{Math.round((count / Object.values(contentGen.categoriesAssigned).reduce((a, b) => a + b, 0)) * 100)}%
									</Text>
								</div>
							))}
						</SimpleGrid>
					</Card>
				</Tabs.Panel>

				<Tabs.Panel value="performance" pt="md">
					<SimpleGrid cols={{ base: 1, lg: 2 }}>
						{/* Processing Times */}
						<Card withBorder>
							<Title order={4} mb="md">Processing Times</Title>
							<div style={{ height: 250 }}>
								<ResponsiveContainer width="100%" height="100%">
									<BarChart data={processingTimeData}>
										<CartesianGrid strokeDasharray="3 3" />
										<XAxis dataKey="name" />
										<YAxis />
										<RechartsTooltip formatter={value => [`${value}ms`, 'Time']} />
										<Bar dataKey="time" fill="#228be6" />
									</BarChart>
								</ResponsiveContainer>
							</div>
							<SimpleGrid cols={1} mt="md">
								{processingTimeData.map(item => (
									<Group key={item.name} justify="space-between">
										<Text size="sm">{item.name}</Text>
										<Text size="sm" fw={600}>{item.time}ms</Text>
									</Group>
								))}
							</SimpleGrid>
						</Card>

						{/* Performance Metrics */}
						<Card withBorder>
							<Title order={4} mb="md">Performance Metrics</Title>
							<Stack gap="md">
								<div>
									<Text size="sm" fw={600} mb="xs">Generation Efficiency</Text>
									<Group>
										<RingProgress
											size={80}
											thickness={8}
											sections={[
												{
													value: 85, // Mock efficiency percentage
													color: 'blue',
												},
											]}
											label={
												<Text size="xs" ta="center">85%</Text>
											}
										/>
										<div>
											<Text size="sm">Overall Efficiency</Text>
											<Text size="xs" c="dimmed">Based on processing time vs. quality</Text>
										</div>
									</Group>
								</div>

								<SimpleGrid cols={2}>
									<div>
										<Text size="xs" c="dimmed">Avg Generation Time</Text>
										<Text fw={600}>{contentGen.processingTimes.averageGenerationTime}ms</Text>
									</div>
									<div>
										<Text size="xs" c="dimmed">Avg Validation Time</Text>
										<Text fw={600}>{contentGen.processingTimes.averageValidationTime}ms</Text>
									</div>
									<div>
										<Text size="xs" c="dimmed">SEO Summaries</Text>
										<Text fw={600}>{contentGen.seoSummariesCreated.toLocaleString()}</Text>
									</div>
									<div>
										<Text size="xs" c="dimmed">Tags Generated</Text>
										<Text fw={600}>{contentGen.tagsGenerated.toLocaleString()}</Text>
									</div>
								</SimpleGrid>

								<div>
									<Text size="sm" fw={600} mb="xs">Content Types</Text>
									<Stack gap="xs">
										<Group justify="space-between">
											<Group>
												<IconFileText size={16} />
												<Text size="sm">Descriptions</Text>
											</Group>
											<Text size="sm" fw={600}>{totalGenerated.toLocaleString()}</Text>
										</Group>
										<Group justify="space-between">
											<Group>
												<IconBrandOpenai size={16} />
												<Text size="sm">SEO Summaries</Text>
											</Group>
											<Text size="sm" fw={600}>{contentGen.seoSummariesCreated.toLocaleString()}</Text>
										</Group>
										<Group justify="space-between">
											<Group>
												<IconTemplate size={16} />
												<Text size="sm">Tags</Text>
											</Group>
											<Text size="sm" fw={600}>{contentGen.tagsGenerated.toLocaleString()}</Text>
										</Group>
									</Stack>
								</div>
							</Stack>
						</Card>
					</SimpleGrid>
				</Tabs.Panel>
			</Tabs>
		</Stack>
	);
}

export default SeederContentGeneration;
