'use client';

import {
	Container,
	Grid,
	Card,
	Title,
	Text,
	Badge,
	Group,
	Stack,
	Button,
	Tabs,
	Alert,
	LoadingOverlay,
	ActionIcon,
	Tooltip,
	Progress,
	RingProgress,
	SimpleGrid,
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import {
	IconRefresh,
	IconAlertTriangle,
	IconInfoCircle,
	IconTrendingUp,
	IconTrendingDown,
	IconMinus,
	IconSettings,
	IconPlayerPlay,
	IconPlayerPause,
	IconBrush,
} from '@tabler/icons-react';
import { useState, useEffect, useCallback } from 'react';


import type {
	SeederDashboardDataType,
	SeederManualTriggerType,
	SeederSettingsType,
	SeederComponentPropsType,
} from '@/types/seeder';
import { SeederAnalytics } from './SeederAnalytics';
import { SeederConnectorStatus } from './SeederConnectorStatus';
import { SeederContentGeneration } from './SeederContentGeneration';
import { SeederManualTrigger } from './SeederManualTrigger';
import { SeederQueueStatus } from './SeederQueueStatus';
import { SeederSettings } from './SeederSettings';
import { SeederStrategyManagement } from './SeederStrategyManagement';
import { SeederTroubleshooting } from './SeederTroubleshooting';


export function SeederDashboard()
{
	const [data, setData] = useState<SeederDashboardDataType | null>(null);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [activeTab, setActiveTab] = useState<string>('overview');
	const [autoRefresh, setAutoRefresh] = useState(true);
	const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);

	const fetchSeederData = useCallback(async () =>
	{
		try
		{
			const response = await fetch('/api/seeder/status');
			if (!response.ok)
			{
				throw new Error(`Failed to fetch seeder data: ${response.statusText}`);
			}

			const seederData = await response.json();
			setData(seederData);
			setError(null);
		}
		catch (err)
		{
			const errorMessage = err instanceof Error ? err.message : 'Failed to fetch seeder data';
			setError(errorMessage);
			notifications.show({
				title: 'Error',
				message: errorMessage,
				color: 'red',
				icon: <IconAlertTriangle size={16} />,
			});
		}
		finally
		{
			setLoading(false);
		}
	}, []);

	const handleRefresh = useCallback(() =>
	{
		setLoading(true);
		fetchSeederData();
	}, [fetchSeederData]);

	const handleTriggerOperation = useCallback(async (operation: SeederManualTriggerType) =>
	{
		try
		{
			const response = await fetch('/api/seeder/trigger', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify(operation),
			});

			if (!response.ok)
			{
				const errorData = await response.json();
				throw new Error(errorData.error || 'Failed to trigger operation');
			}

			const result = await response.json();

			notifications.show({
				title: 'Operation Triggered',
				message: `${operation.operation} operation started successfully`,
				color: 'green',
				icon: <IconPlayerPlay size={16} />,
			});

			// Refresh data after triggering operation
			setTimeout(() => fetchSeederData(), 2000);

			return result;
		}
		catch (err)
		{
			const errorMessage = err instanceof Error ? err.message : 'Failed to trigger operation';
			notifications.show({
				title: 'Operation Failed',
				message: errorMessage,
				color: 'red',
				icon: <IconAlertTriangle size={16} />,
			});
			throw err;
		}
	}, [fetchSeederData]);

	const handleUpdateSettings = useCallback(async (settings: Partial<SeederSettingsType>) =>
	{
		try
		{
			const response = await fetch('/api/seeder/settings', {
				method: 'PUT',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify(settings),
			});

			if (!response.ok)
			{
				const errorData = await response.json();
				throw new Error(errorData.error || 'Failed to update settings');
			}

			const result = await response.json();

			notifications.show({
				title: 'Settings Updated',
				message: 'Seeder settings have been updated successfully',
				color: 'green',
				icon: <IconSettings size={16} />,
			});

			// Refresh data after updating settings
			setTimeout(() => fetchSeederData(), 1000);

			return result;
		}
		catch (err)
		{
			const errorMessage = err instanceof Error ? err.message : 'Failed to update settings';
			notifications.show({
				title: 'Settings Update Failed',
				message: errorMessage,
				color: 'red',
				icon: <IconAlertTriangle size={16} />,
			});
			throw err;
		}
	}, [fetchSeederData]);

	// Set up auto-refresh
	useEffect(() =>
	{
		if (autoRefresh)
		{
			const interval = setInterval(() =>
			{
				if (!loading)
				{
					fetchSeederData();
				}
			}, 30000); // Refresh every 30 seconds

			setRefreshInterval(interval);
			return () => clearInterval(interval);
		}
		if (refreshInterval)
		{
			clearInterval(refreshInterval);
			setRefreshInterval(null);
		}
		return undefined;
	}, [autoRefresh, loading, fetchSeederData]);

	// Initial data fetch
	useEffect(() =>
	{
		fetchSeederData();
	}, [fetchSeederData]);

	// Cleanup on unmount
	useEffect(() => () =>
	{
		if (refreshInterval)
		{
			clearInterval(refreshInterval);
		}
	}, [refreshInterval]);

	const getOverallHealthStatus = () =>
	{
		if (!data) return 'unknown';

		const activeConnectors = data.connectors.filter(c => c.status === 'active').length;
		const totalConnectors = data.connectors.length;
		const enabledStrategies = data.strategies.filter(s => s.enabled).length;
		const totalStrategies = data.strategies.length;

		const connectorHealth = activeConnectors / totalConnectors;
		const strategyHealth = enabledStrategies / totalStrategies;
		const queueHealth = data.queueStatus.backpressureLevel < 0.8 ? 1 : 0.5;

		const overallHealth = (connectorHealth + strategyHealth + queueHealth) / 3;

		if (overallHealth >= 0.9) return 'healthy';
		if (overallHealth >= 0.7) return 'degraded';
		return 'unhealthy';
	};

	const getHealthStatusColor = (status: string) =>
	{
		switch (status)
		{
			case 'healthy': return 'green';
			case 'degraded': return 'yellow';
			case 'unhealthy': return 'red';
			default: return 'gray';
		}
	};

	const getTrendIcon = (trend: 'increasing' | 'decreasing' | 'stable') =>
	{
		switch (trend)
		{
			case 'increasing': return <IconTrendingUp size={16} color="green" />;
			case 'decreasing': return <IconTrendingDown size={16} color="red" />;
			default: return <IconMinus size={16} color="gray" />;
		}
	};

	if (loading && !data)
	{
		return (
			<Container size="xl" py="md">
				<LoadingOverlay visible />
				<Title order={2} mb="md">Domain Seeder Management</Title>
				<Text>Loading seeder dashboard...</Text>
			</Container>
		);
	}

	if (error && !data)
	{
		return (
			<Container size="xl" py="md">
				<Title order={2} mb="md">Domain Seeder Management</Title>
				<Alert
					icon={<IconAlertTriangle size={16} />}
					title="Error Loading Dashboard"
					color="red"
					mb="md"
				>
					{error}
				</Alert>
				<Button onClick={handleRefresh} leftSection={<IconRefresh size={16} />}>
					Retry
				</Button>
			</Container>
		);
	}

	const overallHealth = getOverallHealthStatus();
	const healthColor = getHealthStatusColor(overallHealth);

	return (
		<Container size="xl" py="md">
			<Group justify="space-between" mb="md">
				<div>
					<Title order={2}>Domain Seeder Management</Title>
					<Text size="sm" c="dimmed">
						Monitor and manage domain discovery, content generation, and queue processing
					</Text>
				</div>
				<Group>
					<Badge
						color={healthColor}
						variant="filled"
						size="lg"
						leftSection={<IconInfoCircle size={14} />}
					>
						{overallHealth.toUpperCase()}
					</Badge>
					<Tooltip label={autoRefresh ? 'Disable auto-refresh' : 'Enable auto-refresh'}>
						<ActionIcon
							variant={autoRefresh ? 'filled' : 'outline'}
							color="blue"
							onClick={() => setAutoRefresh(!autoRefresh)}
						>
							{autoRefresh ? <IconPlayerPause size={16} /> : <IconPlayerPlay size={16} />}
						</ActionIcon>
					</Tooltip>
					<Tooltip label="Refresh data">
						<ActionIcon
							variant="outline"
							onClick={handleRefresh}
							loading={loading}
						>
							<IconRefresh size={16} />
						</ActionIcon>
					</Tooltip>
				</Group>
			</Group>

			{error && (
				<Alert
					icon={<IconAlertTriangle size={16} />}
					title="Warning"
					color="yellow"
					mb="md"
				>
					Some data may be outdated: {error}
				</Alert>
			)}

			{/* Overview Cards */}
			{activeTab === 'overview' && data && (
				<SimpleGrid cols={{ base: 1, sm: 2, lg: 4 }} mb="xl">
					<Card withBorder>
						<Group justify="space-between">
							<div>
								<Text size="xs" tt="uppercase" fw={700} c="dimmed">
									Queue Depth
								</Text>
								<Text fw={700} size="xl">
									{data.queueStatus.totalQueued.toLocaleString()}
								</Text>
							</div>
							<RingProgress
								size={60}
								thickness={8}
								sections={[
									{
										value: Math.min(100, (data.queueStatus.totalQueued / data.settings.monitoring.alertThresholds.queueDepthCritical) * 100),
										color: data.queueStatus.totalQueued > data.settings.monitoring.alertThresholds.queueDepthWarning ? 'red' : 'blue',
									},
								]}
							/>
						</Group>
						<Text size="xs" c="dimmed" mt="md">
							Processing: {data.queueStatus.processing} | Failed: {data.queueStatus.failed}
						</Text>
					</Card>

					<Card withBorder>
						<Group justify="space-between">
							<div>
								<Text size="xs" tt="uppercase" fw={700} c="dimmed">
									Throughput
								</Text>
								<Text fw={700} size="xl">
									{data.queueStatus.throughputPerHour.toLocaleString()}/h
								</Text>
							</div>
							<RingProgress
								size={60}
								thickness={8}
								sections={[
									{
										value: Math.min(100, (data.queueStatus.throughputPerHour / 5000) * 100),
										color: 'green',
									},
								]}
							/>
						</Group>
						<Text size="xs" c="dimmed" mt="md">
							Avg processing: {Math.round(data.queueStatus.averageProcessingTime / 1000)}s
						</Text>
					</Card>

					<Card withBorder>
						<Group justify="space-between">
							<div>
								<Text size="xs" tt="uppercase" fw={700} c="dimmed">
									Active Connectors
								</Text>
								<Text fw={700} size="xl">
									{data.connectors.filter(c => c.status === 'active').length}/{data.connectors.length}
								</Text>
							</div>
							<RingProgress
								size={60}
								thickness={8}
								sections={[
									{
										value: (data.connectors.filter(c => c.status === 'active').length / data.connectors.length) * 100,
										color: 'blue',
									},
								]}
							/>
						</Group>
						<Text size="xs" c="dimmed" mt="md">
							{data.connectors.filter(c => c.status === 'error').length} errors | {data.connectors.filter(c => c.status === 'degraded').length} degraded
						</Text>
					</Card>

					<Card withBorder>
						<Group justify="space-between">
							<div>
								<Text size="xs" tt="uppercase" fw={700} c="dimmed">
									Content Generated
								</Text>
								<Text fw={700} size="xl">
									{(data.contentGeneration.preGeneratedCount + data.contentGeneration.liveCount).toLocaleString()}
								</Text>
							</div>
							<RingProgress
								size={60}
								thickness={8}
								sections={[
									{
										value: (data.contentGeneration.preGeneratedCount / (data.contentGeneration.preGeneratedCount + data.contentGeneration.liveCount)) * 100,
										color: 'teal',
									},
									{
										value: (data.contentGeneration.liveCount / (data.contentGeneration.preGeneratedCount + data.contentGeneration.liveCount)) * 100,
										color: 'orange',
									},
								]}
							/>
						</Group>
						<Text size="xs" c="dimmed" mt="md">
							Quality: {Math.round(data.contentGeneration.qualityMetrics.averageQualityScore * 100)}% avg
						</Text>
					</Card>
				</SimpleGrid>
			)}

			<Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'overview')}>
				<Tabs.List>
					<Tabs.Tab value="overview">Overview</Tabs.Tab>
					<Tabs.Tab value="queue">Queue Management</Tabs.Tab>
					<Tabs.Tab value="connectors">Data Connectors</Tabs.Tab>
					<Tabs.Tab value="strategies">Discovery Strategies</Tabs.Tab>
					<Tabs.Tab value="content">Content Generation</Tabs.Tab>
					<Tabs.Tab value="analytics">Analytics</Tabs.Tab>
					<Tabs.Tab value="settings">Settings</Tabs.Tab>
					<Tabs.Tab value="troubleshooting">Troubleshooting</Tabs.Tab>
					<Tabs.Tab value="manual">Manual Operations</Tabs.Tab>
				</Tabs.List>

				<Tabs.Panel value="overview" pt="md">
					<Grid>
						<Grid.Col span={{ base: 12, lg: 8 }}>
							<SeederQueueStatus
								data={data || undefined}
								loading={loading}
								error={error}
								onRefresh={handleRefresh}
							/>
						</Grid.Col>
						<Grid.Col span={{ base: 12, lg: 4 }}>
							<SeederConnectorStatus
								data={data || undefined}
								loading={loading}
								error={error}
								onRefresh={handleRefresh}
							/>
						</Grid.Col>
					</Grid>
				</Tabs.Panel>

				<Tabs.Panel value="queue" pt="md">
					<SeederQueueStatus
						data={data || undefined}
						loading={loading}
						error={error}
						onRefresh={handleRefresh}
					/>
				</Tabs.Panel>

				<Tabs.Panel value="connectors" pt="md">
					<SeederConnectorStatus
						data={data || undefined}
						loading={loading}
						error={error}
						onRefresh={handleRefresh}
					/>
				</Tabs.Panel>

				<Tabs.Panel value="strategies" pt="md">
					<SeederStrategyManagement
						data={data || undefined}
						loading={loading}
						error={error}
						onRefresh={handleRefresh}
						onUpdateSettings={handleUpdateSettings}
					/>
				</Tabs.Panel>

				<Tabs.Panel value="content" pt="md">
					<SeederContentGeneration
						data={data || undefined}
						loading={loading}
						error={error}
						onRefresh={handleRefresh}
					/>
				</Tabs.Panel>

				<Tabs.Panel value="analytics" pt="md">
					<SeederAnalytics
						data={data || undefined}
						loading={loading}
						error={error}
						onRefresh={handleRefresh}
					/>
				</Tabs.Panel>

				<Tabs.Panel value="settings" pt="md">
					<SeederSettings
						data={data || undefined}
						loading={loading}
						error={error}
						onRefresh={handleRefresh}
						onUpdateSettings={handleUpdateSettings}
					/>
				</Tabs.Panel>

				<Tabs.Panel value="troubleshooting" pt="md">
					<SeederTroubleshooting
						data={data || undefined}
						loading={loading}
						error={error}
						onRefresh={handleRefresh}
					/>
				</Tabs.Panel>

				<Tabs.Panel value="manual" pt="md">
					<SeederManualTrigger
						data={data || undefined}
						loading={loading}
						error={error}
						onRefresh={handleRefresh}
						onTriggerOperation={handleTriggerOperation}
					/>
				</Tabs.Panel>
			</Tabs>

			{data && (
				<Text size="xs" c="dimmed" ta="center" mt="xl">
					Last updated: {data.lastUpdated.toLocaleString()} |
					Auto-refresh: {autoRefresh ? 'Enabled' : 'Disabled'}
				</Text>
			)}
		</Container>
	);
}

export default SeederDashboard;
