'use client';

import {
	Card,
	Title,
	Text,
	Group,
	Stack,
	Button,
	Select,
	NumberInput,
	Textarea,
	Modal,
	Alert,
	Badge,
	SimpleGrid,
	Divider,
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import {
	IconPlayerPlay,
	IconAlertTriangle,
	IconInfoCircle,
	IconSettings,
	IconBrush,
	IconReload,
	IconDatabase,
	IconChartBar,
} from '@tabler/icons-react';
import { useState, useCallback } from 'react';

import type {
	SeederComponentPropsType,
	SeederManualTriggerType,
	SeederOperationType,
} from '@/types/seeder';

const riskColorMap =
{
    low: 'green',
    medium: 'yellow',
    high: 'red',
};

export function SeederManualTrigger({
	data,
	loading,
	error,
	onRefresh,
	onTriggerOperation,
}: SeederComponentPropsType)
{
	const [selectedOperation, setSelectedOperation] = useState<SeederOperationType | ''>('');
	const [parameters, setParameters] = useState<Record<string, any>>({});
	const [priority, setPriority] = useState<'low' | 'medium' | 'high'>('medium');
	const [description, setDescription] = useState('');
	const [confirmModalOpen, setConfirmModalOpen] = useState(false);
	const [triggering, setTriggering] = useState(false);

	const operations = [
		{
			value: 'full-discovery',
			label: 'Full Discovery Run',
			description: 'Trigger a complete discovery run using all available sources and strategies',
			icon: <IconDatabase size={16} />,
			estimatedDuration: '2-4 hours',
			risk: 'medium' as const,
		},
		{
			value: 'top-sources-only',
			label: 'Top Sources Discovery',
			description: 'Run discovery using only the most reliable and high-quality sources',
			icon: <IconChartBar size={16} />,
			estimatedDuration: '30-60 minutes',
			risk: 'low' as const,
		},
		{
			value: 'backfill',
			label: 'Historical Backfill',
			description: 'Backfill historical data for a specific time period',
			icon: <IconReload size={16} />,
			estimatedDuration: '1-6 hours',
			risk: 'medium' as const,
		},
		{
			value: 'strategy-specific',
			label: 'Strategy-Specific Run',
			description: 'Run a specific discovery strategy in isolation',
			icon: <IconSettings size={16} />,
			estimatedDuration: '15-90 minutes',
			risk: 'low' as const,
		},
		{
			value: 'queue-cleanup',
			label: 'Queue Cleanup',
			description: 'Clean up stuck, failed, or old jobs from processing queues',
			icon: <IconBrush size={16} />,
			estimatedDuration: '5-15 minutes',
			risk: 'low' as const,
		},
		{
			value: 'stuck-job-recovery',
			label: 'Stuck Job Recovery',
			description: 'Attempt to recover and retry stuck processing jobs',
			icon: <IconReload size={16} />,
			estimatedDuration: '10-30 minutes',
			risk: 'low' as const,
		},
		{
			value: 'bloom-filter-rebuild',
			label: 'Bloom Filter Rebuild',
			description: 'Rebuild the bloom filter for duplicate detection',
			icon: <IconDatabase size={16} />,
			estimatedDuration: '20-45 minutes',
			risk: 'medium' as const,
		},
		{
			value: 'metrics-reset',
			label: 'Metrics Reset',
			description: 'Reset accumulated metrics and counters',
			icon: <IconChartBar size={16} />,
			estimatedDuration: '1-2 minutes',
			risk: 'low' as const,
		},
	];

	const handleTrigger = useCallback(async () =>
	{
		if (!selectedOperation || !onTriggerOperation) return;

		const operation: SeederManualTriggerType = {
			operation: selectedOperation as SeederOperationType,
			parameters,
			priority,
			description: description || undefined,
		};

		setTriggering(true);
		try
		{
			await onTriggerOperation(operation);
			setConfirmModalOpen(false);
			setSelectedOperation('');
			setParameters({});
			setDescription('');
		}
		catch (err)
		{
			// Error handling is done in the parent component
		}
		finally
		{
			setTriggering(false);
		}
	}, [selectedOperation, parameters, priority, description, onTriggerOperation]);

	const selectedOperationData = operations.find(op => op.value === selectedOperation);

	const renderParameterInputs = () =>
	{
		switch (selectedOperation)
		{
			case 'strategy-specific':
				return (
					<Select
						label="Strategy"
						placeholder="Select strategy to run"
						data={[
							{ value: 'differential', label: 'Differential Analysis' },
							{ value: 'zone-new', label: 'Zone-New Processing' },
							{ value: 'long-tail', label: 'Long-Tail Exploration' },
							{ value: 'temporal', label: 'Temporal Analysis' },
						]}
						value={parameters.strategy || ''}
						onChange={value => setParameters(prev => ({ ...prev, strategy: value }))}
						required
					/>
				);
			case 'backfill':
				return (
					<Stack gap="sm">
						<Group grow>
							<NumberInput
								label="Days Back"
								placeholder="Number of days to backfill"
								value={parameters.daysBack || ''}
								onChange={value => setParameters(prev => ({ ...prev, daysBack: value }))}
								min={1}
								max={30}
							/>
							<NumberInput
								label="Batch Size"
								placeholder="Processing batch size"
								value={parameters.batchSize || 1000}
								onChange={value => setParameters(prev => ({ ...prev, batchSize: value }))}
								min={100}
								max={10000}
							/>
						</Group>
					</Stack>
				);
			case 'full-discovery':
			case 'top-sources-only':
				return (
					<NumberInput
						label="Max Domains"
						placeholder="Maximum domains to discover"
						value={parameters.maxDomains || ''}
						onChange={value => setParameters(prev => ({ ...prev, maxDomains: value }))}
						min={1000}
						max={1000000}
					/>
				);
			default:
				return null;
		}
	};

	return (
		<Stack gap="md">
			<Card withBorder>
				<Group justify="space-between" mb="md">
					<Title order={3}>Manual Operations</Title>
					<Badge color="blue" variant="outline">
						{data?.queueStatus.processing || 0} operations running
					</Badge>
				</Group>

				<Alert
					icon={<IconInfoCircle size={16} />}
					title="Manual Operations"
					color="blue"
					mb="md"
				>
					Use these controls to manually trigger seeder operations. Some operations may impact system performance.
				</Alert>

				<SimpleGrid cols={{ base: 1, md: 2, lg: 3 }}>
					{operations.map(operation => (
						<Card key={operation.value} withBorder p="md" style={{ cursor: 'pointer' }}>
							<Group mb="sm">
								{operation.icon}
								<div style={{ flex: 1 }}>
									<Text fw={600} size="sm">{operation.label}</Text>
									<Badge
										color={riskColorMap[operation.risk]}
										variant="outline"
										size="xs"
									>
										{operation.risk} risk
									</Badge>
								</div>
							</Group>

							<Text size="xs" c="dimmed" mb="sm">
								{operation.description}
							</Text>

							<Text size="xs" c="dimmed" mb="md">
								Est. duration: {operation.estimatedDuration}
							</Text>

							<Button
								variant="outline"
								size="xs"
								fullWidth
								onClick={() =>
								{
									setSelectedOperation(operation.value as SeederOperationType);
									setConfirmModalOpen(true);
								}}
								leftSection={<IconPlayerPlay size={14} />}
							>
								Trigger
							</Button>
						</Card>
					))}
				</SimpleGrid>
			</Card>

			{/* Confirmation Modal */}
			<Modal
				opened={confirmModalOpen}
				onClose={() => setConfirmModalOpen(false)}
				title="Confirm Operation"
				size="md"
			>
				{selectedOperationData && (
					<Stack gap="md">
						<Group>
							{selectedOperationData.icon}
							<div>
								<Text fw={600}>{selectedOperationData.label}</Text>
								<Text size="sm" c="dimmed">{selectedOperationData.description}</Text>
							</div>
						</Group>

						<Alert
							icon={<IconAlertTriangle size={16} />}
							title="Operation Details"
							color={riskColorMap[selectedOperationData.risk]}
						>
							<Text size="sm">
								Risk Level: {selectedOperationData.risk.toUpperCase()}<br />
								Estimated Duration: {selectedOperationData.estimatedDuration}
							</Text>
						</Alert>

						<Divider />

						<Select
							label="Priority"
							description="Operation priority level"
							value={priority}
							onChange={value => setPriority(value as 'low' | 'medium' | 'high')}
							data={[
								{ value: 'low', label: 'Low Priority' },
								{ value: 'medium', label: 'Medium Priority' },
								{ value: 'high', label: 'High Priority' },
							]}
						/>

						{renderParameterInputs()}

						<Textarea
							label="Description (Optional)"
							placeholder="Add a description for this operation..."
							value={description}
							onChange={e => setDescription(e.currentTarget.value)}
							rows={3}
						/>

						<Group justify="flex-end">
							<Button
								variant="outline"
								onClick={() => setConfirmModalOpen(false)}
								disabled={triggering}
							>
								Cancel
							</Button>
							<Button
								onClick={handleTrigger}
								loading={triggering}
								leftSection={<IconPlayerPlay size={16} />}
								color={riskColorMap[selectedOperationData.risk]}
							>
								Trigger Operation
							</Button>
						</Group>
					</Stack>
				)}
			</Modal>
		</Stack>
	);
}

export default SeederManualTrigger;
