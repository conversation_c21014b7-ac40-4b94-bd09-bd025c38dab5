'use client';

import {
	Card,
	Title,
	Text,
	Group,
	Stack,
	Button,
	Badge,
	Progress,
	SimpleGrid,
	ActionIcon,
	Tooltip,
	Modal,
	Switch,
	NumberInput,
	MultiSelect,
	Slider,
	Divider,
	Alert,
	ThemeIcon,
	RingProgress,
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import {
	IconRefresh,
	IconSettings,
	IconPlayerPlay,
	IconPlayerPause,
	IconAlertTriangle,
	IconInfoCircle,
	IconTrendingUp,
	IconTrendingDown,
	IconMinus,
	IconBrain,
	IconClock,
	IconTarget,
	IconCheck,
	IconX,
} from '@tabler/icons-react';
import { useState, useCallback } from 'react';
import {
	LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer, BarChart, Bar,
} from 'recharts';

import type {
	SeederComponentPropsType,
	SeederStrategyType,
	SeederSettingsType,
} from '@/types/seeder';

export function SeederStrategyManagement({
	data,
	loading,
	error,
	onRefresh,
	onUpdateSettings,
}: SeederComponentPropsType)
{
	const [selectedStrategy, setSelectedStrategy] = useState<SeederStrategyType | null>(null);
	const [configModalOpen, setConfigModalOpen] = useState(false);
	const [operationLoading, setOperationLoading] = useState(false);

	const handleStrategyToggle = useCallback(async (strategyName: string, enabled: boolean) =>
	{
		if (!onUpdateSettings) return;

		setOperationLoading(true);
		try
		{
			// Update strategy enabled status
			await onUpdateSettings({
				// This would update the specific strategy configuration
				// For now, we'll simulate the update
			});

			notifications.show({
				title: 'Strategy Updated',
				message: `${strategyName} has been ${enabled ? 'enabled' : 'disabled'}`,
				color: 'green',
				icon: <IconCheck size={16} />,
			});

			onRefresh?.();
		}
		catch (err)
		{
			notifications.show({
				title: 'Error',
				message: 'Failed to update strategy configuration',
				color: 'red',
				icon: <IconAlertTriangle size={16} />,
			});
		}
		finally
		{
			setOperationLoading(false);
		}
	}, [onUpdateSettings, onRefresh]);

	const handleStrategyTrigger = useCallback(async (strategyName: string) =>
	{
		setOperationLoading(true);
		try
		{
			// This would typically call the trigger API with strategy-specific operation
			const response = await fetch('/api/seeder/trigger', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					operation: 'strategy-specific',
					parameters: {
						strategy: strategyName.toLowerCase().replace(' ', '-'),
					},
					priority: 'medium',
				}),
			});

			if (!response.ok)
			{
				throw new Error('Failed to trigger strategy');
			}

			notifications.show({
				title: 'Strategy Triggered',
				message: `${strategyName} strategy run has been started`,
				color: 'blue',
				icon: <IconPlayerPlay size={16} />,
			});

			onRefresh?.();
		}
		catch (err)
		{
			notifications.show({
				title: 'Error',
				message: 'Failed to trigger strategy run',
				color: 'red',
				icon: <IconAlertTriangle size={16} />,
			});
		}
		finally
		{
			setOperationLoading(false);
		}
	}, [onRefresh]);

	const getStrategyStatusIcon = (enabled: boolean, successRate: number) =>
	{
		if (!enabled) return <IconPlayerPause size={16} color="gray" />;
		if (successRate > 0.9) return <IconCheck size={16} color="green" />;
		if (successRate > 0.7) return <IconAlertTriangle size={16} color="orange" />;
		return <IconX size={16} color="red" />;
	};

	const getStrategyStatusColor = (enabled: boolean, successRate: number) =>
	{
		if (!enabled) return 'gray';
		if (successRate > 0.9) return 'green';
		if (successRate > 0.7) return 'yellow';
		return 'red';
	};

	const formatDuration = (seconds: number) =>
	{
		if (seconds < 60) return `${seconds}s`;
		if (seconds < 3600) return `${Math.round(seconds / 60)}m`;
		return `${Math.round(seconds / 3600)}h`;
	};

	const getStrategyDescription = (name: string) =>
	{
		switch (name)
		{
			case 'differential':
				return 'Compares current data with historical snapshots to identify new domains';
			case 'zone-new':
				return 'Processes newly registered domains from zone file updates';
			case 'long-tail':
				return 'Discovers less popular domains through deep crawling and analysis';
			case 'temporal':
				return 'Analyzes time-based patterns to predict domain registration trends';
			default:
				return 'Advanced domain discovery strategy';
		}
	};

	if (!data)
	{
		return (
			<Card withBorder>
				<Title order={3} mb="md">Discovery Strategies</Title>
				<Text>Loading strategy data...</Text>
			</Card>
		);
	}

	const strategies = data.strategies;
	const enabledStrategies = strategies.filter(s => s.enabled).length;
	const totalDomainsProcessed = strategies.reduce((sum, s) => sum + s.domainsProcessed, 0);
	const averageSuccessRate = strategies.reduce((sum, s) => sum + s.successRate, 0) / strategies.length;

	// Prepare chart data
	const strategyPerformanceData = strategies.map(strategy => ({
		name: strategy.displayName,
		domains: strategy.domainsProcessed,
		successRate: Math.round(strategy.successRate * 100),
		confidence: Math.round(strategy.averageConfidence * 100),
	}));

	const strategyTrendsData = Array.from({ length: 24 }, (_, i) =>
	{
		const hour = new Date();
		hour.setHours(hour.getHours() - (23 - i));
		return {
			time: hour.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
			differential: Math.floor(Math.random() * 1000) + 500,
			'zone-new': Math.floor(Math.random() * 1500) + 800,
			'long-tail': Math.floor(Math.random() * 800) + 300,
			temporal: Math.floor(Math.random() * 500) + 200,
		};
	});

	return (
		<Stack gap="md">
			{/* Strategy Overview */}
			<Card withBorder>
				<Group justify="space-between" mb="md">
					<Title order={3}>Discovery Strategies</Title>
					<Group>
						<Badge color={enabledStrategies === strategies.length ? 'green' : 'yellow'} variant="filled">
							{enabledStrategies}/{strategies.length} Active
						</Badge>
						<ActionIcon variant="outline" onClick={onRefresh} loading={loading}>
							<IconRefresh size={16} />
						</ActionIcon>
					</Group>
				</Group>

				<SimpleGrid cols={{ base: 1, sm: 2, lg: 4 }} mb="md">
					<div>
						<Text size="xs" tt="uppercase" fw={700} c="dimmed">Total Processed</Text>
						<Text fw={700} size="xl">{totalDomainsProcessed.toLocaleString()}</Text>
					</div>
					<div>
						<Text size="xs" tt="uppercase" fw={700} c="dimmed">Active Strategies</Text>
						<Text fw={700} size="xl" c="green">{enabledStrategies}</Text>
					</div>
					<div>
						<Text size="xs" tt="uppercase" fw={700} c="dimmed">Avg Success Rate</Text>
						<Text fw={700} size="xl" c={averageSuccessRate > 0.9 ? 'green' : averageSuccessRate > 0.7 ? 'yellow' : 'red'}>
							{Math.round(averageSuccessRate * 100)}%
						</Text>
					</div>
					<div>
						<Text size="xs" tt="uppercase" fw={700} c="dimmed">Peak Performance</Text>
						<Text fw={700} size="xl">
							{Math.max(...strategies.map(s => s.metrics.peakDomainsPerHour)).toLocaleString()}/h
						</Text>
					</div>
				</SimpleGrid>

				{averageSuccessRate < 0.8 && (
					<Alert
						icon={<IconAlertTriangle size={16} />}
						title="Strategy Performance Warning"
						color="yellow"
						mb="md"
					>
						Overall strategy success rate is below 80%. Consider reviewing strategy configurations or data source quality.
					</Alert>
				)}
			</Card>

			{/* Strategy Performance Chart */}
			<Card withBorder>
				<Title order={4} mb="md">Strategy Performance Comparison</Title>
				<div style={{ height: 300 }}>
					<ResponsiveContainer width="100%" height="100%">
						<BarChart data={strategyPerformanceData}>
							<CartesianGrid strokeDasharray="3 3" />
							<XAxis dataKey="name" />
							<YAxis yAxisId="left" />
							<YAxis yAxisId="right" orientation="right" />
							<RechartsTooltip />
							<Bar yAxisId="left" dataKey="domains" fill="#228be6" name="Domains Processed" />
							<Bar yAxisId="right" dataKey="successRate" fill="#40c057" name="Success Rate %" />
						</BarChart>
					</ResponsiveContainer>
				</div>
			</Card>

			{/* Strategy Trends Chart */}
			<Card withBorder>
				<Title order={4} mb="md">Strategy Processing Trends (24h)</Title>
				<div style={{ height: 300 }}>
					<ResponsiveContainer width="100%" height="100%">
						<LineChart data={strategyTrendsData}>
							<CartesianGrid strokeDasharray="3 3" />
							<XAxis dataKey="time" />
							<YAxis />
							<RechartsTooltip />
							<Line type="monotone" dataKey="differential" stroke="#228be6" strokeWidth={2} name="Differential" />
							<Line type="monotone" dataKey="zone-new" stroke="#40c057" strokeWidth={2} name="Zone-New" />
							<Line type="monotone" dataKey="long-tail" stroke="#fab005" strokeWidth={2} name="Long-Tail" />
							<Line type="monotone" dataKey="temporal" stroke="#fd7e14" strokeWidth={2} name="Temporal" />
						</LineChart>
					</ResponsiveContainer>
				</div>
			</Card>

			{/* Individual Strategy Cards */}
			<SimpleGrid cols={{ base: 1, md: 2 }}>
				{strategies.map(strategy => (
					<Card key={strategy.name} withBorder>
						<Group justify="space-between" mb="md">
							<Group>
								<ThemeIcon
									color={getStrategyStatusColor(strategy.enabled, strategy.successRate)}
									variant="light"
									size="lg"
								>
									<IconBrain size={20} />
								</ThemeIcon>
								<div>
									<Text fw={600}>{strategy.displayName}</Text>
									<Badge
										color={getStrategyStatusColor(strategy.enabled, strategy.successRate)}
										variant="filled"
										size="sm"
										leftSection={getStrategyStatusIcon(strategy.enabled, strategy.successRate)}
									>
										{strategy.enabled ? 'Active' : 'Disabled'}
									</Badge>
								</div>
							</Group>
							<Group>
								<ActionIcon
									variant="outline"
									onClick={() => handleStrategyTrigger(strategy.displayName)}
									loading={operationLoading}
									disabled={!strategy.enabled}
								>
									<IconPlayerPlay size={16} />
								</ActionIcon>
								<ActionIcon
									variant="subtle"
									onClick={() =>
									{
										setSelectedStrategy(strategy);
										setConfigModalOpen(true);
									}}
								>
									<IconSettings size={16} />
								</ActionIcon>
							</Group>
						</Group>

						<Text size="sm" c="dimmed" mb="md">
							{getStrategyDescription(strategy.name)}
						</Text>

						<SimpleGrid cols={2} mb="md">
							<div>
								<Text size="xs" c="dimmed">Domains Processed</Text>
								<Text fw={600}>{strategy.domainsProcessed.toLocaleString()}</Text>
							</div>
							<div>
								<Text size="xs" c="dimmed">Success Rate</Text>
								<Text fw={600} c={strategy.successRate > 0.9 ? 'green' : strategy.successRate > 0.7 ? 'yellow' : 'red'}>
									{Math.round(strategy.successRate * 100)}%
								</Text>
							</div>
							<div>
								<Text size="xs" c="dimmed">Avg Confidence</Text>
								<Text fw={600}>{Math.round(strategy.averageConfidence * 100)}%</Text>
							</div>
							<div>
								<Text size="xs" c="dimmed">Priority</Text>
								<Text fw={600}>{strategy.priority}</Text>
							</div>
						</SimpleGrid>

						<Group mb="md">
							<div style={{ flex: 1 }}>
								<Text size="sm" mb={4}>Performance</Text>
								<Progress
									value={strategy.successRate * 100}
									color={getStrategyStatusColor(strategy.enabled, strategy.successRate)}
									size="sm"
								/>
							</div>
							<RingProgress
								size={60}
								thickness={6}
								sections={[
									{
										value: strategy.averageConfidence * 100,
										color: strategy.averageConfidence > 0.8 ? 'green' : strategy.averageConfidence > 0.6 ? 'yellow' : 'red',
									},
								]}
								label={(
									<Text size="xs" ta="center">
										{Math.round(strategy.averageConfidence * 100)}%
									</Text>
								)}
							/>
						</Group>

						<Group justify="space-between" mb="md">
							<div>
								<Text size="xs" c="dimmed">Last Run</Text>
								<Text size="sm" fw={500}>
									{strategy.lastRun ? strategy.lastRun.toLocaleString() : 'Never'}
								</Text>
							</div>
							<div>
								<Text size="xs" c="dimmed">Next Run</Text>
								<Text size="sm" fw={500}>
									{strategy.estimatedNextRun ? strategy.estimatedNextRun.toLocaleTimeString() : 'Not scheduled'}
								</Text>
							</div>
						</Group>

						<Group>
							<Switch
								size="sm"
								checked={strategy.enabled}
								onChange={e => handleStrategyToggle(strategy.name, e.currentTarget.checked)}
								disabled={operationLoading}
								label="Enabled"
							/>
							<Button
								variant="outline"
								size="xs"
								onClick={() => handleStrategyTrigger(strategy.displayName)}
								loading={operationLoading}
								disabled={!strategy.enabled}
								leftSection={<IconPlayerPlay size={14} />}
							>
								Run Now
							</Button>
						</Group>

						<SimpleGrid cols={3} mt="md" pt="md" style={{ borderTop: '1px solid var(--mantine-color-gray-3)' }}>
							<div>
								<Text size="xs" c="dimmed">Total Runs</Text>
								<Text size="sm" fw={500}>{strategy.metrics.totalRuns}</Text>
							</div>
							<div>
								<Text size="xs" c="dimmed">Avg Runtime</Text>
								<Text size="sm" fw={500}>{formatDuration(strategy.metrics.averageRunTime)}</Text>
							</div>
							<div>
								<Text size="xs" c="dimmed">Peak Rate</Text>
								<Text size="sm" fw={500}>{strategy.metrics.peakDomainsPerHour.toLocaleString()}/h</Text>
							</div>
						</SimpleGrid>
					</Card>
				))}
			</SimpleGrid>

			{/* Strategy Configuration Modal */}
			<Modal
				opened={configModalOpen}
				onClose={() => setConfigModalOpen(false)}
				title={`Configure ${selectedStrategy?.displayName}`}
				size="lg"
			>
				{selectedStrategy && (
					<Stack gap="md">
						<Group>
							<Switch
								label="Enabled"
								checked={selectedStrategy.enabled}
								onChange={e => handleStrategyToggle(selectedStrategy.name, e.currentTarget.checked)}
								disabled={operationLoading}
							/>
							<Badge color={getStrategyStatusColor(selectedStrategy.enabled, selectedStrategy.successRate)} variant="filled">
								{selectedStrategy.enabled ? 'Active' : 'Disabled'}
							</Badge>
						</Group>

						<Text size="sm" c="dimmed">
							{getStrategyDescription(selectedStrategy.name)}
						</Text>

						<Divider />

						<SimpleGrid cols={2}>
							<NumberInput
								label="Priority"
								description="Higher priority strategies run first"
								value={selectedStrategy.priority}
								min={1}
								max={10}
							/>

							<NumberInput
								label="Batch Size"
								description="Domains to process in each batch"
								value={selectedStrategy.configuration.batchSize}
								min={100}
								max={10000}
								step={100}
							/>
						</SimpleGrid>

						<div>
							<Text size="sm" fw={600} mb="xs">Confidence Threshold</Text>
							<Slider
								value={selectedStrategy.configuration.confidenceThreshold * 100}
								min={50}
								max={100}
								step={5}
								marks={[
									{ value: 50, label: '50%' },
									{ value: 70, label: '70%' },
									{ value: 90, label: '90%' },
								]}
								mb="md"
							/>
							<Text size="xs" c="dimmed">
								Minimum confidence level required for domain discovery
							</Text>
						</div>

						<NumberInput
							label="Max Domains Per Run"
							description="Maximum domains to process in a single run"
							value={selectedStrategy.configuration.maxDomainsPerRun}
							min={1000}
							max={100000}
							step={1000}
						/>

						<NumberInput
							label="Cooldown Period (minutes)"
							description="Minimum time between strategy runs"
							value={selectedStrategy.configuration.cooldownPeriod}
							min={5}
							max={1440}
							step={5}
						/>

						<MultiSelect
							label="Enabled Sources"
							description="Data sources this strategy can use"
							data={[
								{ value: 'CommonCrawl', label: 'CommonCrawl' },
								{ value: 'CZDS', label: 'CZDS' },
								{ value: 'Tranco', label: 'Tranco' },
								{ value: 'Radar', label: 'Radar' },
								{ value: 'Umbrella', label: 'Umbrella' },
								{ value: 'Sonar', label: 'Sonar' },
							]}
							value={selectedStrategy.configuration.enabledSources}
						/>

						<Divider />

						<Group justify="space-between">
							<div>
								<Text size="sm" fw={600}>Performance Metrics</Text>
								<Text size="xs" c="dimmed">Current strategy performance</Text>
							</div>
						</Group>

						<SimpleGrid cols={3}>
							<div>
								<Text size="xs" c="dimmed">Total Runs</Text>
								<Text fw={600}>{selectedStrategy.metrics.totalRuns}</Text>
							</div>
							<div>
								<Text size="xs" c="dimmed">Avg Runtime</Text>
								<Text fw={600}>{formatDuration(selectedStrategy.metrics.averageRunTime)}</Text>
							</div>
							<div>
								<Text size="xs" c="dimmed">Last Duration</Text>
								<Text fw={600}>{formatDuration(selectedStrategy.metrics.lastRunDuration)}</Text>
							</div>
						</SimpleGrid>

						<Group justify="flex-end" mt="md">
							<Button variant="outline" onClick={() => setConfigModalOpen(false)}>
								Cancel
							</Button>
							<Button onClick={() => setConfigModalOpen(false)}>
								Save Changes
							</Button>
						</Group>
					</Stack>
				)}
			</Modal>
		</Stack>
	);
}

export default SeederStrategyManagement;
