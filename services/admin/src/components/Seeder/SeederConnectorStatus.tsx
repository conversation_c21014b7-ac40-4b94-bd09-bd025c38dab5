'use client';

import {
	Card,
	Title,
	Text,
	Group,
	Stack,
	Button,
	Badge,
	Progress,
	SimpleGrid,
	ActionIcon,
	Tooltip,
	Modal,
	Alert,
	Switch,
	NumberInput,
	Select,
	Divider,
	RingProgress,
	ThemeIcon,
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import {
	IconRefresh,
	IconSettings,
	IconAlertTriangle,
	IconInfoCircle,
	IconTrendingUp,
	IconTrendingDown,
	IconMinus,
	IconDatabase,
	IconNetwork,
	IconClock,
	IconCheck,
	IconX,
	IconExclamationMark,
} from '@tabler/icons-react';
import { useState, useCallback } from 'react';
import {
	BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer, PieChart, Pie, Cell,
} from 'recharts';

import type {
	SeederComponentPropsType,
	SeederConnectorType,
} from '@/types/seeder';

export function SeederConnectorStatus({
	data, loading, error, onRefresh,
}: SeederComponentPropsType)
{
	const [selectedConnector, setSelectedConnector] = useState<SeederConnectorType | null>(null);
	const [configModalOpen, setConfigModalOpen] = useState(false);
	const [operationLoading, setOperationLoading] = useState(false);

	const handleConnectorToggle = useCallback(async (connectorName: string, enabled: boolean) =>
	{
		setOperationLoading(true);
		try
		{
			// This would typically call an API to enable/disable the connector
			await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call

			notifications.show({
				title: 'Connector Updated',
				message: `${connectorName} has been ${enabled ? 'enabled' : 'disabled'}`,
				color: 'green',
				icon: <IconCheck size={16} />,
			});

			onRefresh?.();
		}
		catch (err)
		{
			notifications.show({
				title: 'Error',
				message: 'Failed to update connector configuration',
				color: 'red',
				icon: <IconAlertTriangle size={16} />,
			});
		}
		finally
		{
			setOperationLoading(false);
		}
	}, [onRefresh]);

	const handleSyncTrigger = useCallback(async (connectorName: string) =>
	{
		setOperationLoading(true);
		try
		{
			// This would typically call an API to trigger a manual sync
			await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API call

			notifications.show({
				title: 'Sync Triggered',
				message: `Manual sync started for ${connectorName}`,
				color: 'blue',
				icon: <IconRefresh size={16} />,
			});

			onRefresh?.();
		}
		catch (err)
		{
			notifications.show({
				title: 'Error',
				message: 'Failed to trigger connector sync',
				color: 'red',
				icon: <IconAlertTriangle size={16} />,
			});
		}
		finally
		{
			setOperationLoading(false);
		}
	}, [onRefresh]);

	const getConnectorStatusIcon = (status: string) =>
	{
		switch (status)
		{
			case 'active':
				return <IconCheck size={16} color="green" />;
			case 'degraded':
				return <IconExclamationMark size={16} color="orange" />;
			case 'error':
				return <IconX size={16} color="red" />;
			default:
				return <IconMinus size={16} color="gray" />;
		}
	};

	const getConnectorStatusColor = (status: string) =>
	{
		switch (status)
		{
			case 'active': return 'green';
			case 'degraded': return 'yellow';
			case 'error': return 'red';
			default: return 'gray';
		}
	};

	const getDataFreshnessColor = (freshness: number) =>
	{
		if (freshness < 3600) return 'green'; // < 1 hour
		if (freshness < 7200) return 'yellow'; // < 2 hours
		return 'red'; // > 2 hours
	};

	const formatDuration = (seconds: number) =>
	{
		if (seconds < 60) return `${seconds}s`;
		if (seconds < 3600) return `${Math.round(seconds / 60)}m`;
		return `${Math.round(seconds / 3600)}h`;
	};

	if (!data)
	{
		return (
			<Card withBorder>
				<Title order={3} mb="md">Data Connectors</Title>
				<Text>Loading connector data...</Text>
			</Card>
		);
	}

	const connectors = data.connectors;
	const activeConnectors = connectors.filter(c => c.status === 'active').length;
	const totalDomains = connectors.reduce((sum, c) => sum + c.domainsDiscovered, 0);

	// Prepare chart data
	const connectorChartData = connectors.map(connector => ({
		name: connector.name,
		domains: connector.domainsDiscovered,
		errorRate: Math.round(connector.errorRate * 100),
	}));

	const statusDistribution = [
		{ name: 'Active', value: connectors.filter(c => c.status === 'active').length, color: '#40c057' },
		{ name: 'Degraded', value: connectors.filter(c => c.status === 'degraded').length, color: '#fab005' },
		{ name: 'Error', value: connectors.filter(c => c.status === 'error').length, color: '#fa5252' },
		{ name: 'Inactive', value: connectors.filter(c => c.status === 'inactive').length, color: '#868e96' },
	].filter(item => item.value > 0);

	return (
		<Stack gap="md">
			{/* Connector Overview */}
			<Card withBorder>
				<Group justify="space-between" mb="md">
					<Title order={3}>Data Connectors</Title>
					<Group>
						<Badge color={activeConnectors === connectors.length ? 'green' : 'yellow'} variant="filled">
							{activeConnectors}/{connectors.length} Active
						</Badge>
						<ActionIcon variant="outline" onClick={onRefresh} loading={loading}>
							<IconRefresh size={16} />
						</ActionIcon>
					</Group>
				</Group>

				<SimpleGrid cols={{ base: 1, sm: 2, lg: 4 }} mb="md">
					<div>
						<Text size="xs" tt="uppercase" fw={700} c="dimmed">Total Domains</Text>
						<Text fw={700} size="xl">{totalDomains.toLocaleString()}</Text>
					</div>
					<div>
						<Text size="xs" tt="uppercase" fw={700} c="dimmed">Active Sources</Text>
						<Text fw={700} size="xl" c="green">{activeConnectors}</Text>
					</div>
					<div>
						<Text size="xs" tt="uppercase" fw={700} c="dimmed">Avg Error Rate</Text>
						<Text fw={700} size="xl" c={connectors.reduce((sum, c) => sum + c.errorRate, 0) / connectors.length > 0.05 ? 'red' : 'green'}>
							{Math.round((connectors.reduce((sum, c) => sum + c.errorRate, 0) / connectors.length) * 100)}%
						</Text>
					</div>
					<div>
						<Text size="xs" tt="uppercase" fw={700} c="dimmed">Last Sync</Text>
						<Text fw={700} size="xl">
							{Math.min(...connectors.map(c => (c.lastSync ? (Date.now() - c.lastSync.getTime()) / 1000 : Infinity))) < 3600
								? 'Recent' : 'Stale'}
						</Text>
					</div>
				</SimpleGrid>

				{/* Status Distribution Chart */}
				<Group align="flex-start" mb="md">
					<div style={{ flex: 1 }}>
						<Text size="sm" fw={600} mb="xs">Connector Status Distribution</Text>
						<div style={{ height: 200 }}>
							<ResponsiveContainer width="100%" height="100%">
								<PieChart>
									<Pie
										data={statusDistribution}
										cx="50%"
										cy="50%"
										innerRadius={40}
										outerRadius={80}
										paddingAngle={5}
										dataKey="value"
									>
										{statusDistribution.map((entry, index) => (
											<Cell key={`cell-${index}`} fill={entry.color} />
										))}
									</Pie>
									<RechartsTooltip />
								</PieChart>
							</ResponsiveContainer>
						</div>
					</div>
					<div style={{ flex: 1 }}>
						<Text size="sm" fw={600} mb="xs">Domains Discovered by Source</Text>
						<div style={{ height: 200 }}>
							<ResponsiveContainer width="100%" height="100%">
								<BarChart data={connectorChartData}>
									<CartesianGrid strokeDasharray="3 3" />
									<XAxis dataKey="name" angle={-45} textAnchor="end" height={80} />
									<YAxis />
									<RechartsTooltip />
									<Bar dataKey="domains" fill="#228be6" />
								</BarChart>
							</ResponsiveContainer>
						</div>
					</div>
				</Group>
			</Card>

			{/* Individual Connector Cards */}
			<SimpleGrid cols={{ base: 1, md: 2, lg: 3 }}>
				{connectors.map(connector => (
					<Card key={connector.name} withBorder>
						<Group justify="space-between" mb="md">
							<Group>
								<ThemeIcon
									color={getConnectorStatusColor(connector.status)}
									variant="light"
									size="lg"
								>
									<IconDatabase size={20} />
								</ThemeIcon>
								<div>
									<Text fw={600}>{connector.name}</Text>
									<Badge
										color={getConnectorStatusColor(connector.status)}
										variant="filled"
										size="sm"
										leftSection={getConnectorStatusIcon(connector.status)}
									>
										{connector.status}
									</Badge>
								</div>
							</Group>
							<ActionIcon
								variant="subtle"
								onClick={() =>
								{
									setSelectedConnector(connector);
									setConfigModalOpen(true);
								}}
							>
								<IconSettings size={16} />
							</ActionIcon>
						</Group>

						<Stack gap="xs" mb="md">
							<Group justify="space-between">
								<Text size="sm" c="dimmed">Domains Discovered</Text>
								<Text size="sm" fw={600}>{connector.domainsDiscovered.toLocaleString()}</Text>
							</Group>
							<Group justify="space-between">
								<Text size="sm" c="dimmed">Error Rate</Text>
								<Text size="sm" fw={600} c={connector.errorRate > 0.05 ? 'red' : 'green'}>
									{Math.round(connector.errorRate * 100)}%
								</Text>
							</Group>
							<Group justify="space-between">
								<Text size="sm" c="dimmed">Last Sync</Text>
								<Text size="sm" fw={600}>
									{connector.lastSync ? connector.lastSync.toLocaleTimeString() : 'Never'}
								</Text>
							</Group>
							<Group justify="space-between">
								<Text size="sm" c="dimmed">Data Freshness</Text>
								<Badge
									color={getDataFreshnessColor(connector.dataFreshness)}
									variant="outline"
									size="sm"
								>
									{formatDuration(connector.dataFreshness)}
								</Badge>
							</Group>
						</Stack>

						<Progress
							value={Math.min(100, (connector.domainsDiscovered / Math.max(...connectors.map(c => c.domainsDiscovered))) * 100)}
							color={getConnectorStatusColor(connector.status)}
							size="sm"
							mb="md"
						/>

						<Group>
							<Switch
								size="sm"
								checked={connector.configuration.enabled}
								onChange={e => handleConnectorToggle(connector.name, e.currentTarget.checked)}
								disabled={operationLoading}
								label="Enabled"
							/>
							<Button
								variant="outline"
								size="xs"
								onClick={() => handleSyncTrigger(connector.name)}
								loading={operationLoading}
								leftSection={<IconRefresh size={14} />}
							>
								Sync Now
							</Button>
						</Group>

						{connector.consecutiveErrors > 0 && (
							<Alert
								icon={<IconAlertTriangle size={16} />}
								color="yellow"
								mt="xs"
							>
								{connector.consecutiveErrors} consecutive errors
							</Alert>
						)}
					</Card>
				))}
			</SimpleGrid>

			{/* Connector Configuration Modal */}
			<Modal
				opened={configModalOpen}
				onClose={() => setConfigModalOpen(false)}
				title={`Configure ${selectedConnector?.name}`}
				size="md"
			>
				{selectedConnector && (
					<Stack gap="md">
						<Group>
							<Switch
								label="Enabled"
								checked={selectedConnector.configuration.enabled}
								onChange={e => handleConnectorToggle(selectedConnector.name, e.currentTarget.checked)}
								disabled={operationLoading}
							/>
							<Badge color={getConnectorStatusColor(selectedConnector.status)} variant="filled">
								{selectedConnector.status}
							</Badge>
						</Group>

						<Divider />

						<NumberInput
							label="Priority"
							description="Higher priority connectors are processed first"
							value={selectedConnector.configuration.priority}
							min={1}
							max={10}
						/>

						<NumberInput
							label="Batch Size"
							description="Number of domains to process in each batch"
							value={selectedConnector.configuration.batchSize}
							min={100}
							max={10000}
							step={100}
						/>

						<NumberInput
							label="Sync Interval (hours)"
							description="How often to sync data from this source"
							value={selectedConnector.configuration.syncInterval}
							min={1}
							max={168}
						/>

						<NumberInput
							label="Max Retries"
							description="Maximum number of retry attempts for failed operations"
							value={selectedConnector.configuration.maxRetries}
							min={0}
							max={10}
						/>

						<Divider />

						<Group justify="space-between">
							<div>
								<Text size="sm" fw={600}>Performance Metrics</Text>
								<Text size="xs" c="dimmed">Current connector performance</Text>
							</div>
						</Group>

						<SimpleGrid cols={2}>
							<div>
								<Text size="xs" c="dimmed">Total Syncs</Text>
								<Text fw={600}>{selectedConnector.totalSyncs.toLocaleString()}</Text>
							</div>
							<div>
								<Text size="xs" c="dimmed">Sync Duration</Text>
								<Text fw={600}>{formatDuration(selectedConnector.syncDuration / 1000)}</Text>
							</div>
							<div>
								<Text size="xs" c="dimmed">Rate Limit</Text>
								<Text fw={600}>{selectedConnector.rateLimit}/min</Text>
							</div>
							<div>
								<Text size="xs" c="dimmed">Next Sync</Text>
								<Text fw={600}>
									{selectedConnector.nextSync ? selectedConnector.nextSync.toLocaleTimeString() : 'Not scheduled'}
								</Text>
							</div>
						</SimpleGrid>

						<Group justify="flex-end" mt="md">
							<Button variant="outline" onClick={() => setConfigModalOpen(false)}>
								Cancel
							</Button>
							<Button onClick={() => setConfigModalOpen(false)}>
								Save Changes
							</Button>
						</Group>
					</Stack>
				)}
			</Modal>
		</Stack>
	);
}

export default SeederConnectorStatus;
