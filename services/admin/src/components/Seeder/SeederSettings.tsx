'use client';

import {
	Card,
	Title,
	Text,
	Group,
	Stack,
	Button,
	NumberInput,
	Select,
	Switch,
	Divider,
	Alert,
	Tabs,
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import {
	IconSettings,
	IconDeviceFloppy,
	IconAlertTriangle,
	IconInfoCircle,
} from '@tabler/icons-react';
import { useState, useCallback } from 'react';

import type { SeederComponentPropsType, SeederSettingsType } from '@/types/seeder';

export function SeederSettings({
	data,
	loading,
	error,
	onRefresh,
	onUpdateSettings,
}: SeederComponentPropsType)
{
	const [settings, setSettings] = useState<SeederSettingsType | null>(data?.settings || null);
	const [saving, setSaving] = useState(false);
	const [activeTab, setActiveTab] = useState('general');

	const handleSave = useCallback(async () =>
	{
		if (!settings || !onUpdateSettings) return;

		setSaving(true);
		try
		{
			await onUpdateSettings(settings);
			notifications.show({
				title: 'Settings Saved',
				message: 'Seeder settings have been updated successfully',
				color: 'green',
				icon: <IconDeviceFloppy size={16} />,
			});
		}
		catch (err)
		{
			notifications.show({
				title: 'Save Failed',
				message: 'Failed to save seeder settings',
				color: 'red',
				icon: <IconAlertTriangle size={16} />,
			});
		}
		finally
		{
			setSaving(false);
		}
	}, [settings, onUpdateSettings]);

	const updateSetting = useCallback((path: string, value: any) =>
	{
		if (!settings) return;

		const keys = path.split('.');
		const newSettings = { ...settings };
		let current: any = newSettings;

		for (let i = 0; i < keys.length - 1; i++)
		{
			current[keys[i]] = { ...current[keys[i]] };
			current = current[keys[i]];
		}

		current[keys[keys.length - 1]] = value;
		setSettings(newSettings);
	}, [settings]);

	if (!settings)
	{
		return (
			<Card withBorder>
				<Title order={3} mb="md">Seeder Settings</Title>
				<Text>Loading settings...</Text>
			</Card>
		);
	}

	return (
		<Stack gap="md">
			<Card withBorder>
				<Group justify="space-between" mb="md">
					<Title order={3}>Seeder Settings</Title>
					<Button
						leftSection={<IconDeviceFloppy size={16} />}
						onClick={handleSave}
						loading={saving}
					>
						Save Changes
					</Button>
				</Group>

				<Tabs value={activeTab} onChange={setActiveTab}>
					<Tabs.List>
						<Tabs.Tab value="general">General</Tabs.Tab>
						<Tabs.Tab value="processing">Processing</Tabs.Tab>
						<Tabs.Tab value="limits">Rate Limits</Tabs.Tab>
						<Tabs.Tab value="monitoring">Monitoring</Tabs.Tab>
					</Tabs.List>

					<Tabs.Panel value="general" pt="md">
						<Stack gap="md">
							<NumberInput
								label="Max New Domains Per Day"
								description="Maximum number of new domains to process daily"
								value={settings.maxNewDomainsPerDay}
								onChange={value => updateSetting('maxNewDomainsPerDay', value)}
								min={1000}
								max={1000000}
								step={1000}
							/>

							<Switch
								label="Content Generation Enabled"
								description="Enable automatic content generation for discovered domains"
								checked={settings.contentGenerationEnabled}
								onChange={e => updateSetting('contentGenerationEnabled', e.currentTarget.checked)}
							/>

							<Select
								label="Processing Mode"
								description="How content should be generated"
								value={settings.processingMode}
								onChange={value => updateSetting('processingMode', value)}
								data={[
									{ value: 'preGenerated', label: 'Pre-generated Only' },
									{ value: 'live', label: 'Live Generation Only' },
									{ value: 'hybrid', label: 'Hybrid Mode' },
								]}
							/>
						</Stack>
					</Tabs.Panel>

					<Tabs.Panel value="processing" pt="md">
						<Stack gap="md">
							<Title order={5}>Batch Sizes</Title>
							<Group grow>
								<NumberInput
									label="Discovery Batch Size"
									value={settings.batchSizes.discovery}
									onChange={value => updateSetting('batchSizes.discovery', value)}
									min={100}
									max={10000}
								/>
								<NumberInput
									label="Validation Batch Size"
									value={settings.batchSizes.validation}
									onChange={value => updateSetting('batchSizes.validation', value)}
									min={50}
									max={5000}
								/>
							</Group>

							<Divider />

							<Title order={5}>Processing Priorities</Title>
							<Group grow>
								<NumberInput
									label="Top Tier Priority"
									value={settings.processingPriorities.topTier}
									onChange={value => updateSetting('processingPriorities.topTier', value)}
									min={1}
									max={100}
								/>
								<NumberInput
									label="Mid Tier Priority"
									value={settings.processingPriorities.midTier}
									onChange={value => updateSetting('processingPriorities.midTier', value)}
									min={1}
									max={100}
								/>
								<NumberInput
									label="Long Tail Priority"
									value={settings.processingPriorities.longTail}
									onChange={value => updateSetting('processingPriorities.longTail', value)}
									min={1}
									max={100}
								/>
							</Group>
						</Stack>
					</Tabs.Panel>

					<Tabs.Panel value="limits" pt="md">
						<Stack gap="md">
							<NumberInput
								label="Global Requests Per Minute"
								description="Maximum requests per minute across all connectors"
								value={settings.rateLimits.globalRequestsPerMinute}
								onChange={value => updateSetting('rateLimits.globalRequestsPerMinute', value)}
								min={100}
								max={10000}
							/>

							<NumberInput
								label="Per-Connector Requests Per Minute"
								description="Maximum requests per minute per connector"
								value={settings.rateLimits.perConnectorRequestsPerMinute}
								onChange={value => updateSetting('rateLimits.perConnectorRequestsPerMinute', value)}
								min={10}
								max={1000}
							/>

							<NumberInput
								label="Database Queries Per Second"
								description="Maximum database queries per second"
								value={settings.rateLimits.databaseQueriesPerSecond}
								onChange={value => updateSetting('rateLimits.databaseQueriesPerSecond', value)}
								min={10}
								max={1000}
							/>
						</Stack>
					</Tabs.Panel>

					<Tabs.Panel value="monitoring" pt="md">
						<Stack gap="md">
							<Alert
								icon={<IconInfoCircle size={16} />}
								title="Alert Thresholds"
								color="blue"
							>
								Configure when alerts should be triggered based on system metrics
							</Alert>

							<Group grow>
								<NumberInput
									label="Queue Depth Warning"
									value={settings.monitoring.alertThresholds.queueDepthWarning}
									onChange={value => updateSetting('monitoring.alertThresholds.queueDepthWarning', value)}
									min={1000}
									max={100000}
								/>
								<NumberInput
									label="Queue Depth Critical"
									value={settings.monitoring.alertThresholds.queueDepthCritical}
									onChange={value => updateSetting('monitoring.alertThresholds.queueDepthCritical', value)}
									min={5000}
									max={200000}
								/>
							</Group>

							<NumberInput
								label="Metrics Retention (Days)"
								description="How long to keep historical metrics data"
								value={settings.monitoring.metricsRetentionDays}
								onChange={value => updateSetting('monitoring.metricsRetentionDays', value)}
								min={7}
								max={365}
							/>
						</Stack>
					</Tabs.Panel>
				</Tabs>
			</Card>
		</Stack>
	);
}

export default SeederSettings;
