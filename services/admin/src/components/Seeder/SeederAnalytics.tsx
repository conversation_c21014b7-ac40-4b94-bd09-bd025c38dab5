'use client';

import {
	Card,
	Title,
	Text,
	Group,
	Stack,
	Button,
	Select,
	SimpleGrid,
	ActionIcon,
	Tabs,
} from '@mantine/core';
import {
	IconRefresh,
	IconDownload,
	IconChartBar,
} from '@tabler/icons-react';
import { useState } from 'react';
import {
	LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer, BarChart, Bar,
} from 'recharts';

import type { SeederComponentPropsType } from '@/types/seeder';

export function SeederAnalytics({
	data, loading, error, onRefresh,
}: SeederComponentPropsType)
{
	const [timeRange, setTimeRange] = useState('24h');
	const [activeTab, setActiveTab] = useState('overview');

	if (!data)
	{
		return (
			<Card withBorder>
				<Title order={3} mb="md">Analytics</Title>
				<Text>Loading analytics data...</Text>
			</Card>
		);
	}

	// Mock analytics data
	const mockData = Array.from({ length: 24 }, (_, i) => ({
		time: `${i}:00`,
		discovery: Math.floor(Math.random() * 1000) + 500,
		processing: Math.floor(Math.random() * 800) + 400,
		errors: Math.floor(Math.random() * 50) + 10,
	}));

	return (
		<Stack gap="md">
			<Card withBorder>
				<Group justify="space-between" mb="md">
					<Title order={3}>Seeder Analytics</Title>
					<Group>
						<Select
							value={timeRange}
							onChange={(value, _option) => setTimeRange(value || '24h')}
							data={[
								{ value: '1h', label: 'Last Hour' },
								{ value: '24h', label: 'Last 24 Hours' },
								{ value: '7d', label: 'Last 7 Days' },
								{ value: '30d', label: 'Last 30 Days' },
							]}
						/>
						<Button variant="outline" leftSection={<IconDownload size={16} />}>
							Export
						</Button>
						<ActionIcon variant="outline" onClick={onRefresh} loading={loading}>
							<IconRefresh size={16} />
						</ActionIcon>
					</Group>
				</Group>

				<Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'overview')}>
					<Tabs.List>
						<Tabs.Tab value="overview">Overview</Tabs.Tab>
						<Tabs.Tab value="discovery">Discovery Rates</Tabs.Tab>
						<Tabs.Tab value="performance">Performance</Tabs.Tab>
						<Tabs.Tab value="sources">Source Analysis</Tabs.Tab>
					</Tabs.List>

					<Tabs.Panel value="overview" pt="md">
						<div style={{ height: 400 }}>
							<ResponsiveContainer width="100%" height="100%">
								<LineChart data={mockData}>
									<CartesianGrid strokeDasharray="3 3" />
									<XAxis dataKey="time" />
									<YAxis />
									<RechartsTooltip />
									<Line type="monotone" dataKey="discovery" stroke="#228be6" strokeWidth={2} name="Discovery" />
									<Line type="monotone" dataKey="processing" stroke="#40c057" strokeWidth={2} name="Processing" />
									<Line type="monotone" dataKey="errors" stroke="#fa5252" strokeWidth={2} name="Errors" />
								</LineChart>
							</ResponsiveContainer>
						</div>
					</Tabs.Panel>

					<Tabs.Panel value="discovery" pt="md">
						<Text c="dimmed">Discovery rate analytics would be displayed here</Text>
					</Tabs.Panel>

					<Tabs.Panel value="performance" pt="md">
						<Text c="dimmed">Performance analytics would be displayed here</Text>
					</Tabs.Panel>

					<Tabs.Panel value="sources" pt="md">
						<Text c="dimmed">Source analysis would be displayed here</Text>
					</Tabs.Panel>
				</Tabs>
			</Card>
		</Stack>
	);
}

export default SeederAnalytics;
