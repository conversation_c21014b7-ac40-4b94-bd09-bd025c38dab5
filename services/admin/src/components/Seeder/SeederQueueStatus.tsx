'use client';

import {
	Card,
	Title,
	Text,
	Group,
	Stack,
	Button,
	Badge,
	Progress,
	SimpleGrid,
	Table,
	Pagination,
	TextInput,
	Select,
	ActionIcon,
	Tooltip,
	Modal,
	Alert,
	LoadingOverlay,
	Menu,
	Checkbox,
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import {
	IconSearch,
	IconFilter,
	IconRefresh,
	IconPlayerPlay,
	IconPlayerPause,
	IconTrash,
	IconSettings,
	IconAlertTriangle,
	IconInfoCircle,
	IconDots,
	IconBrush,
	IconReload,
} from '@tabler/icons-react';
import { useState, useEffect, useCallback } from 'react';
import {
	LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer,
} from 'recharts';

import type {
	SeederComponentPropsType,
	SeederQueueItemType,
	SeederQueueStatusType,
} from '@/types/seeder';

interface QueueFilters
{
	search: string;
	priority: string;
	source: string;
	strategy: string;
	mode: string;
}

export function SeederQueueStatus({
	data, loading, error, onRefresh,
}: SeederComponentPropsType)
{
	const [queueItems, setQueueItems] = useState<SeederQueueItemType[]>([]);
	const [queueLoading, setQueueLoading] = useState(false);
	const [selectedItems, setSelectedItems] = useState<string[]>([]);
	const [filters, setFilters] = useState<QueueFilters>({
		search: '',
		priority: '',
		source: '',
		strategy: '',
		mode: '',
	});
	const [currentPage, setCurrentPage] = useState(1);
	const [totalPages, setTotalPages] = useState(1);
	const [showBulkActions, setShowBulkActions] = useState(false);
	const [operationLoading, setOperationLoading] = useState(false);

	const fetchQueueItems = useCallback(async (page: number = 1) =>
	{
		setQueueLoading(true);
		try
		{
			const params = new URLSearchParams({
				page: page.toString(),
				limit: '20',
				...(filters.search && { search: filters.search }),
				...(filters.priority && { priority: filters.priority }),
				...(filters.source && { source: filters.source }),
				...(filters.strategy && { strategy: filters.strategy }),
				...(filters.mode && { mode: filters.mode }),
			});

			const response = await fetch(`/api/seeder/queue?${params}`);
			if (!response.ok)
			{
				throw new Error('Failed to fetch queue items');
			}

			const queueData = await response.json();
			setQueueItems(queueData.items || []);
			setTotalPages(queueData.pagination?.totalPages || 1);
			setCurrentPage(page);
		}
		catch (err)
		{
			notifications.show({
				title: 'Error',
				message: 'Failed to fetch queue items',
				color: 'red',
				icon: <IconAlertTriangle size={16} />,
			});
		}
		finally
		{
			setQueueLoading(false);
		}
	}, [filters]);

	const handleQueueOperation = useCallback(async (operation: string, domains?: string[]) =>
	{
		setOperationLoading(true);
		try
		{
			const response = await fetch('/api/seeder/queue', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					operation,
					domains: domains || selectedItems,
				}),
			});

			if (!response.ok)
			{
				const errorData = await response.json();
				throw new Error(errorData.error || 'Operation failed');
			}

			const result = await response.json();

			notifications.show({
				title: 'Operation Successful',
				message: `Queue ${operation} completed successfully`,
				color: 'green',
				icon: <IconSettings size={16} />,
			});

			// Clear selection and refresh
			setSelectedItems([]);
			setShowBulkActions(false);
			await fetchQueueItems(currentPage);
			onRefresh?.();
		}
		catch (err)
		{
			const errorMessage = err instanceof Error ? err.message : 'Operation failed';
			notifications.show({
				title: 'Operation Failed',
				message: errorMessage,
				color: 'red',
				icon: <IconAlertTriangle size={16} />,
			});
		}
		finally
		{
			setOperationLoading(false);
		}
	}, [selectedItems, currentPage, fetchQueueItems, onRefresh]);

	const handleFilterChange = useCallback((field: keyof QueueFilters, value: string) =>
	{
		setFilters(prev => ({ ...prev, [field]: value }));
		setCurrentPage(1);
	}, []);

	const handleSelectItem = useCallback((domain: string, checked: boolean) =>
	{
		setSelectedItems(prev => (checked
			? [...prev, domain]
			: prev.filter(item => item !== domain)));
	}, []);

	const handleSelectAll = useCallback((checked: boolean) =>
	{
		setSelectedItems(checked ? queueItems.map(item => item.domain) : []);
	}, [queueItems]);

	// Fetch queue items when filters change
	useEffect(() =>
	{
		const timeoutId = setTimeout(() =>
		{
			fetchQueueItems(1);
		}, 500);

		return () => clearTimeout(timeoutId);
	}, [fetchQueueItems]);

	// Show bulk actions when items are selected
	useEffect(() =>
	{
		setShowBulkActions(selectedItems.length > 0);
	}, [selectedItems]);

	if (!data)
	{
		return (
			<Card withBorder>
				<LoadingOverlay visible={loading} />
				<Title order={3} mb="md">Queue Status</Title>
				<Text>Loading queue data...</Text>
			</Card>
		);
	}

	const queueStatus = data.queueStatus;
	const queueHealthColor = queueStatus.backpressureLevel > 0.8 ? 'red'
		: queueStatus.backpressureLevel > 0.5 ? 'yellow' : 'green';

	// Generate mock chart data for queue depth over time
	const queueChartData = Array.from({ length: 24 }, (_, i) =>
	{
		const hour = new Date();
		hour.setHours(hour.getHours() - (23 - i));
		return {
			time: hour.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
			depth: Math.floor(Math.random() * 10000) + queueStatus.totalQueued * 0.8,
			processing: Math.floor(Math.random() * 500) + queueStatus.processing * 0.8,
		};
	});

	return (
		<Stack gap="md">
			{/* Queue Overview */}
			<Card withBorder>
				<Group justify="space-between" mb="md">
					<Title order={3}>Queue Status</Title>
					<Group>
						<Badge color={queueHealthColor} variant="filled">
							{queueStatus.backpressureLevel > 0.8 ? 'High Load'
								: queueStatus.backpressureLevel > 0.5 ? 'Moderate Load' : 'Normal'}
						</Badge>
						<ActionIcon variant="outline" onClick={onRefresh} loading={loading}>
							<IconRefresh size={16} />
						</ActionIcon>
					</Group>
				</Group>

				<SimpleGrid cols={{ base: 2, sm: 4 }} mb="md">
					<div>
						<Text size="xs" tt="uppercase" fw={700} c="dimmed">Total Queued</Text>
						<Text fw={700} size="xl">{queueStatus.totalQueued.toLocaleString()}</Text>
					</div>
					<div>
						<Text size="xs" tt="uppercase" fw={700} c="dimmed">Processing</Text>
						<Text fw={700} size="xl" c="blue">{queueStatus.processing.toLocaleString()}</Text>
					</div>
					<div>
						<Text size="xs" tt="uppercase" fw={700} c="dimmed">Completed</Text>
						<Text fw={700} size="xl" c="green">{queueStatus.completed.toLocaleString()}</Text>
					</div>
					<div>
						<Text size="xs" tt="uppercase" fw={700} c="dimmed">Failed</Text>
						<Text fw={700} size="xl" c="red">{queueStatus.failed.toLocaleString()}</Text>
					</div>
				</SimpleGrid>

				<Group mb="md">
					<div style={{ flex: 1 }}>
						<Text size="sm" mb={4}>Queue Depth Progress</Text>
						<Progress
							value={Math.min(100, (queueStatus.totalQueued / data.settings.monitoring.alertThresholds.queueDepthCritical) * 100)}
							color={queueHealthColor}
							size="lg"
						/>
						<Text size="xs" c="dimmed" mt={4}>
							{queueStatus.totalQueued.toLocaleString()} / {data.settings.monitoring.alertThresholds.queueDepthCritical.toLocaleString()} (critical threshold)
						</Text>
					</div>
				</Group>

				<SimpleGrid cols={{ base: 1, sm: 3 }} mb="md">
					<div>
						<Text size="sm" c="dimmed">Throughput</Text>
						<Text fw={600}>{queueStatus.throughputPerHour.toLocaleString()} domains/hour</Text>
					</div>
					<div>
						<Text size="sm" c="dimmed">Avg Processing Time</Text>
						<Text fw={600}>{Math.round(queueStatus.averageProcessingTime / 1000)}s</Text>
					</div>
					<div>
						<Text size="sm" c="dimmed">Est. Time Remaining</Text>
						<Text fw={600}>{Math.round(queueStatus.estimatedTimeRemaining / 3600)}h</Text>
					</div>
				</SimpleGrid>

				{queueStatus.backpressureLevel > 0.7 && (
					<Alert
						icon={<IconAlertTriangle size={16} />}
						title="High Queue Backpressure"
						color="yellow"
						mb="md"
					>
						Queue processing is experiencing high backpressure ({Math.round(queueStatus.backpressureLevel * 100)}%).
						Consider increasing processing capacity or reducing input rate.
					</Alert>
				)}
			</Card>

			{/* Queue Depth Chart */}
			<Card withBorder>
				<Title order={4} mb="md">Queue Depth Over Time (24h)</Title>
				<div style={{ height: 300 }}>
					<ResponsiveContainer width="100%" height="100%">
						<LineChart data={queueChartData}>
							<CartesianGrid strokeDasharray="3 3" />
							<XAxis dataKey="time" />
							<YAxis />
							<RechartsTooltip />
							<Line
								type="monotone"
								dataKey="depth"
								stroke="#228be6"
								strokeWidth={2}
								name="Queue Depth"
							/>
							<Line
								type="monotone"
								dataKey="processing"
								stroke="#40c057"
								strokeWidth={2}
								name="Processing"
							/>
						</LineChart>
					</ResponsiveContainer>
				</div>
			</Card>

			{/* Queue Items Table */}
			<Card withBorder>
				<Group justify="space-between" mb="md">
					<Title order={4}>Queue Items</Title>
					<Group>
						{showBulkActions && (
							<Menu shadow="md" width={200}>
								<Menu.Target>
									<Button
										variant="outline"
										size="sm"
										loading={operationLoading}
										leftSection={<IconSettings size={16} />}
									>
										Bulk Actions ({selectedItems.length})
									</Button>
								</Menu.Target>
								<Menu.Dropdown>
									<Menu.Item
										leftSection={<IconPlayerPlay size={14} />}
										onClick={() => handleQueueOperation('prioritize')}
									>
										Prioritize Selected
									</Menu.Item>
									<Menu.Item
										leftSection={<IconReload size={14} />}
										onClick={() => handleQueueOperation('requeue')}
									>
										Requeue Selected
									</Menu.Item>
									<Menu.Item
										leftSection={<IconTrash size={14} />}
										onClick={() => handleQueueOperation('remove')}
										color="red"
									>
										Remove Selected
									</Menu.Item>
								</Menu.Dropdown>
							</Menu>
						)}
						<Button
							variant="outline"
							size="sm"
							onClick={() => fetchQueueItems(currentPage)}
							loading={queueLoading}
							leftSection={<IconRefresh size={16} />}
						>
							Refresh
						</Button>
					</Group>
				</Group>

				{/* Filters */}
				<Group mb="md">
					<TextInput
						placeholder="Search domains..."
						leftSection={<IconSearch size={16} />}
						value={filters.search}
						onChange={e => handleFilterChange('search', e.target.value)}
						style={{ flex: 1 }}
					/>
					<Select
						placeholder="Priority"
						data={[
							{ value: '', label: 'All Priorities' },
							{ value: 'high', label: 'High' },
							{ value: 'medium', label: 'Medium' },
							{ value: 'low', label: 'Low' },
						]}
						value={filters.priority}
						onChange={value => handleFilterChange('priority', value || '')}
						clearable
					/>
					<Select
						placeholder="Source"
						data={[
							{ value: '', label: 'All Sources' },
							{ value: 'CommonCrawl', label: 'CommonCrawl' },
							{ value: 'CZDS', label: 'CZDS' },
							{ value: 'Tranco', label: 'Tranco' },
							{ value: 'Radar', label: 'Radar' },
							{ value: 'Umbrella', label: 'Umbrella' },
							{ value: 'Sonar', label: 'Sonar' },
						]}
						value={filters.source}
						onChange={value => handleFilterChange('source', value || '')}
						clearable
					/>
					<Select
						placeholder="Mode"
						data={[
							{ value: '', label: 'All Modes' },
							{ value: 'preGenerated', label: 'Pre-generated' },
							{ value: 'live', label: 'Live' },
						]}
						value={filters.mode}
						onChange={value => handleFilterChange('mode', value || '')}
						clearable
					/>
				</Group>

				<div style={{ position: 'relative' }}>
					<LoadingOverlay visible={queueLoading} />

					<Table striped highlightOnHover>
						<Table.Thead>
							<Table.Tr>
								<Table.Th>
									<Checkbox
										checked={selectedItems.length === queueItems.length && queueItems.length > 0}
										indeterminate={selectedItems.length > 0 && selectedItems.length < queueItems.length}
										onChange={e => handleSelectAll(e.currentTarget.checked)}
									/>
								</Table.Th>
								<Table.Th>Domain</Table.Th>
								<Table.Th>Position</Table.Th>
								<Table.Th>Priority</Table.Th>
								<Table.Th>Source</Table.Th>
								<Table.Th>Strategy</Table.Th>
								<Table.Th>Mode</Table.Th>
								<Table.Th>Est. Processing</Table.Th>
								<Table.Th>Retries</Table.Th>
								<Table.Th>Actions</Table.Th>
							</Table.Tr>
						</Table.Thead>
						<Table.Tbody>
							{queueItems.map(item => (
								<Table.Tr key={item.domain}>
									<Table.Td>
										<Checkbox
											checked={selectedItems.includes(item.domain)}
											onChange={e => handleSelectItem(item.domain, e.currentTarget.checked)}
										/>
									</Table.Td>
									<Table.Td>
										<div>
											<Text fw={500}>{item.domain}</Text>
											{item.lastError && (
												<Text size="xs" c="red" truncate>
													{item.lastError}
												</Text>
											)}
										</div>
									</Table.Td>
									<Table.Td>
										<Text size="sm">#{item.queuePosition}</Text>
									</Table.Td>
									<Table.Td>
										<Badge
											color={item.priority === 'high' ? 'red' : item.priority === 'medium' ? 'yellow' : 'gray'}
											variant="filled"
											size="sm"
										>
											{item.priority}
										</Badge>
									</Table.Td>
									<Table.Td>
										<Text size="sm">{item.source}</Text>
									</Table.Td>
									<Table.Td>
										<Text size="sm">{item.discoveryStrategy}</Text>
									</Table.Td>
									<Table.Td>
										<Badge
											color={item.contentGenerationMode === 'live' ? 'orange' : 'teal'}
											variant="outline"
											size="sm"
										>
											{item.contentGenerationMode}
										</Badge>
									</Table.Td>
									<Table.Td>
										<Text size="sm">
											{item.estimatedProcessingTime.toLocaleTimeString()}
										</Text>
									</Table.Td>
									<Table.Td>
										<Text size="sm" c={item.retryCount > 0 ? 'orange' : 'dimmed'}>
											{item.retryCount}
										</Text>
									</Table.Td>
									<Table.Td>
										<Menu shadow="md" width={150}>
											<Menu.Target>
												<ActionIcon variant="subtle" size="sm">
													<IconDots size={16} />
												</ActionIcon>
											</Menu.Target>
											<Menu.Dropdown>
												<Menu.Item
													leftSection={<IconPlayerPlay size={14} />}
													onClick={() => handleQueueOperation('prioritize', [item.domain])}
												>
													Prioritize
												</Menu.Item>
												<Menu.Item
													leftSection={<IconReload size={14} />}
													onClick={() => handleQueueOperation('requeue', [item.domain])}
												>
													Requeue
												</Menu.Item>
												<Menu.Item
													leftSection={<IconTrash size={14} />}
													onClick={() => handleQueueOperation('remove', [item.domain])}
													color="red"
												>
													Remove
												</Menu.Item>
											</Menu.Dropdown>
										</Menu>
									</Table.Td>
								</Table.Tr>
							))}
						</Table.Tbody>
					</Table>

					{queueItems.length === 0 && !queueLoading && (
						<div style={{ textAlign: 'center', padding: '2rem' }}>
							<Text c="dimmed">No queue items found matching the current filters.</Text>
						</div>
					)}
				</div>

				{totalPages > 1 && (
					<Group justify="center" mt="md">
						<Pagination
							value={currentPage}
							onChange={page => fetchQueueItems(page)}
							total={totalPages}
							size="sm"
						/>
					</Group>
				)}
			</Card>

			{/* Queue Management Actions */}
			<Card withBorder>
				<Title order={4} mb="md">Queue Management</Title>
				<Group>
					<Button
						variant="outline"
						leftSection={<IconPlayerPause size={16} />}
						onClick={() => handleQueueOperation('pause')}
						loading={operationLoading}
					>
						Pause Queue
					</Button>
					<Button
						variant="outline"
						leftSection={<IconPlayerPlay size={16} />}
						onClick={() => handleQueueOperation('resume')}
						loading={operationLoading}
					>
						Resume Queue
					</Button>
					<Button
						variant="outline"
						leftSection={<IconBrush size={16} />}
						onClick={() => handleQueueOperation('clear')}
						loading={operationLoading}
						color="orange"
					>
						Clear Failed
					</Button>
					<Button
						variant="outline"
						leftSection={<IconReload size={16} />}
						onClick={() => handleQueueOperation('retry-failed')}
						loading={operationLoading}
						color="blue"
					>
						Retry Failed
					</Button>
				</Group>
			</Card>
		</Stack>
	);
}

export default SeederQueueStatus;
