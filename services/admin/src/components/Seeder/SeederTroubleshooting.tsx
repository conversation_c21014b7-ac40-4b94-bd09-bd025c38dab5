'use client';

import {
	Card,
	Title,
	Text,
	Group,
	Stack,
	Button,
	Badge,
	Alert,
	Tabs,
	Table,
	Progress,
	ActionIcon,
} from '@mantine/core';
import {
	IconRefresh,
	IconAlertTriangle,
	IconInfoCircle,
	IconBug,
	IconTool,
	IconCheck,
	IconX,
} from '@tabler/icons-react';
import { useState } from 'react';

import type { SeederComponentPropsType } from '@/types/seeder';

export function SeederTroubleshooting({
	data, loading, error, onRefresh,
}: SeederComponentPropsType)
{
	const [activeTab, setActiveTab] = useState('overview');

	if (!data)
	{
		return (
			<Card withBorder>
				<Title order={3} mb="md">Troubleshooting</Title>
				<Text>Loading troubleshooting data...</Text>
			</Card>
		);
	}

	// Mock troubleshooting data
	const mockIssues = [
		{
			id: '1',
			severity: 'high' as const,
			component: 'CommonCrawl Connector',
			issue: 'High error rate detected',
			description: 'Error rate has exceeded 10% in the last hour',
			recommendation: 'Check network connectivity and API rate limits',
			status: 'active' as const,
		},
		{
			id: '2',
			severity: 'medium' as const,
			component: 'Queue Manager',
			issue: 'Queue depth increasing',
			description: 'Processing queue depth has been steadily increasing',
			recommendation: 'Consider increasing processing capacity',
			status: 'investigating' as const,
		},
	];

	const mockDiagnostics = [
		{ name: 'Database Connectivity', status: 'pass' as const, message: 'All database connections healthy' },
		{ name: 'Connector Health', status: 'warning' as const, message: '1 connector experiencing issues' },
		{ name: 'Queue Performance', status: 'pass' as const, message: 'Queue processing within normal parameters' },
		{ name: 'Memory Usage', status: 'pass' as const, message: 'Memory usage at 65%' },
	];

	return (
		<Stack gap="md">
			<Card withBorder>
				<Group justify="space-between" mb="md">
					<Title order={3}>System Troubleshooting</Title>
					<Group>
						<Badge color="green" variant="filled">
							System Health: 85%
						</Badge>
						<ActionIcon variant="outline" onClick={onRefresh} loading={loading}>
							<IconRefresh size={16} />
						</ActionIcon>
					</Group>
				</Group>

				<Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'overview')}>
					<Tabs.List>
						<Tabs.Tab value="overview">Overview</Tabs.Tab>
						<Tabs.Tab value="issues">Active Issues</Tabs.Tab>
						<Tabs.Tab value="diagnostics">Diagnostics</Tabs.Tab>
						<Tabs.Tab value="logs">Log Analysis</Tabs.Tab>
					</Tabs.List>

					<Tabs.Panel value="overview" pt="md">
						<Stack gap="md">
							<Alert
								icon={<IconInfoCircle size={16} />}
								title="System Status"
								color="blue"
							>
								Overall system health is good with {mockIssues.length} active issues requiring attention.
							</Alert>

							<div>
								<Text size="sm" fw={600} mb="xs">System Health Score</Text>
								<Progress value={85} color="green" size="lg" />
								<Text size="xs" c="dimmed" mt="xs">
									Based on connector health, queue performance, and error rates
								</Text>
							</div>
						</Stack>
					</Tabs.Panel>

					<Tabs.Panel value="issues" pt="md">
						<Stack gap="md">
							{mockIssues.map(issue => (
								<Alert
									key={issue.id}
									icon={<IconAlertTriangle size={16} />}
									title={issue.issue}
									color={issue.severity === 'high' ? 'red' : issue.severity === 'medium' ? 'yellow' : 'blue'}
								>
									<Text size="sm" mb="xs">{issue.description}</Text>
									<Text size="sm" fw={600}>Recommendation:</Text>
									<Text size="sm">{issue.recommendation}</Text>
									<Group mt="xs">
										<Badge color={issue.severity === 'high' ? 'red' : 'yellow'} variant="filled">
											{issue.severity} severity
										</Badge>
										<Badge color="blue" variant="outline">
											{issue.component}
										</Badge>
									</Group>
								</Alert>
							))}
						</Stack>
					</Tabs.Panel>

					<Tabs.Panel value="diagnostics" pt="md">
						<Table striped>
							<Table.Thead>
								<Table.Tr>
									<Table.Th>Diagnostic</Table.Th>
									<Table.Th>Status</Table.Th>
									<Table.Th>Message</Table.Th>
									<Table.Th>Actions</Table.Th>
								</Table.Tr>
							</Table.Thead>
							<Table.Tbody>
								{mockDiagnostics.map(diagnostic => (
									<Table.Tr key={diagnostic.name}>
										<Table.Td>{diagnostic.name}</Table.Td>
										<Table.Td>
											<Badge
												color={diagnostic.status === 'pass' ? 'green' : diagnostic.status === 'warning' ? 'yellow' : 'red'}
												variant="filled"
												leftSection={diagnostic.status === 'pass' ? <IconCheck size={12} /> : <IconX size={12} />}
											>
												{diagnostic.status}
											</Badge>
										</Table.Td>
										<Table.Td>{diagnostic.message}</Table.Td>
										<Table.Td>
											<Button variant="outline" size="xs">
												Details
											</Button>
										</Table.Td>
									</Table.Tr>
								))}
							</Table.Tbody>
						</Table>
					</Tabs.Panel>

					<Tabs.Panel value="logs" pt="md">
						<Text c="dimmed">Log analysis and error pattern detection would be displayed here</Text>
					</Tabs.Panel>
				</Tabs>
			</Card>
		</Stack>
	);
}

export default SeederTroubleshooting;
