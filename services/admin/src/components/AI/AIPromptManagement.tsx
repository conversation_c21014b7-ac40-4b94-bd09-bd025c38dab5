'use client';

import {
	Stack,
	Group,
	Card,
	Text,
	Button,
	ActionIcon,
	Modal,
	TextInput,
	Select,
	Textarea,
	Switch,
	Badge,
	Table,
	Tabs,
	Paper,
	JsonInput,
	Alert,
	Code,
	Progress,
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { clientLogger } from '@/lib/logger';
import { modals } from '@mantine/modals';
import { notifications } from '@mantine/notifications';
import {
	IconPlus,
	IconEdit,
	IconTrash,
	IconTestPipe,
	IconCopy,
	IconTemplate,
	IconChartBar,
	IconAlertCircle,
} from '@tabler/icons-react';
import { useState } from 'react';

import type { AIPromptTemplateType, AIPromptManagementProps, AIPromptTestResultType } from '@/types/ai';

export function AIPromptManagement({
	templates, onUpdate, onDelete, onAdd, onTest,
}: AIPromptManagementProps)
{
	const [selectedTemplate, setSelectedTemplate] = useState<AIPromptTemplateType | null>(null);
	const [modalOpened, setModalOpened] = useState(false);
	const [testModalOpened, setTestModalOpened] = useState(false);
	const [testResult, setTestResult] = useState<AIPromptTestResultType | null>(null);
	const [testing, setTesting] = useState(false);

	const form = useForm<Partial<AIPromptTemplateType>>({
		initialValues: {
			name: '',
			description: '',
			category: 'domain-description',
			template: '',
			variables: [],
			isActive: true,
		},
	});

	const testForm = useForm({
		initialValues: {
			testInput: '{}',
			provider: 'openai',
			model: 'gpt-4',
		},
	});

	const handleEdit = (template: AIPromptTemplateType) =>
	{
		setSelectedTemplate(template);
		form.setValues(template);
		setModalOpened(true);
	};

	const handleAdd = () =>
	{
		setSelectedTemplate(null);
		form.reset();
		setModalOpened(true);
	};

	const handleSubmit = (values: Partial<AIPromptTemplateType>) =>
	{
		if (selectedTemplate)
		{
			onUpdate({ ...selectedTemplate, ...values } as AIPromptTemplateType);
		}
		else
		{
			onAdd(values as Omit<AIPromptTemplateType, 'id' | 'createdAt' | 'updatedAt'>);
		}
		setModalOpened(false);
	};

	const handleDelete = (template: AIPromptTemplateType) =>
	{
		modals.openConfirmModal({
			title: 'Delete Prompt Template',
			children: (
				<Text size="sm">
					Are you sure you want to delete the template "{template.name}"? This action cannot be undone.
				</Text>
			),
			labels: { confirm: 'Delete', cancel: 'Cancel' },
			confirmProps: { color: 'red' },
			onConfirm: () => onDelete(template.id),
		});
	};

	const handleTest = async (template: AIPromptTemplateType) =>
	{
		setSelectedTemplate(template);
		setTestResult(null);
		testForm.reset();
		setTestModalOpened(true);
	};

	const runTest = async () =>
	{
		if (!selectedTemplate) return;

		setTesting(true);
		try
		{
			const testInput = JSON.parse(testForm.values.testInput);
			const result = await onTest(selectedTemplate.id, testInput);
			setTestResult(result);
		}
		catch (error)
		{
			clientLogger.error('Test failed:', error);
			notifications.show({
				title: 'Test Failed',
				message: 'Failed to run prompt test',
				color: 'red',
			});
		}
		finally
		{
			setTesting(false);
		}
	};

	const copyTemplate = (template: AIPromptTemplateType) =>
	{
		navigator.clipboard.writeText(template.template);
		notifications.show({
			title: 'Copied',
			message: 'Template copied to clipboard',
			color: 'green',
		});
	};

	const extractVariables = (template: string): string[] =>
	{
		const matches = template.match(/\{\{(\w+)\}\}/g);
		return matches ? [...new Set(matches.map(match => match.slice(2, -2)))] : [];
	};

	const getCategoryColor = (category: string) =>
	{
		switch (category)
		{
			case 'domain-description': return 'blue';
			case 'categorization': return 'green';
			case 'tagging': return 'orange';
			case 'seo-summary': return 'purple';
			default: return 'gray';
		}
	};

	const getPerformanceColor = (score: number) =>
	{
		if (score >= 8) return 'green';
		if (score >= 6) return 'yellow';
		return 'red';
	};

	return (
		<Stack gap="md">
			<Group justify="space-between">
				<Text size="lg" fw={600}>AI Prompt Management</Text>
				<Button leftSection={<IconPlus size={16} />} onClick={handleAdd}>
					Add Template
				</Button>
			</Group>

			<Stack gap="md">
				{templates.map(template => (
					<Card key={template.id} shadow="sm" padding="md" withBorder>
						<Group justify="space-between" mb="md">
							<Group>
								<Text fw={600} size="lg">{template.name}</Text>
								<Badge color={getCategoryColor(template.category)} variant="filled">
									{template.category.replace('-', ' ')}
								</Badge>
								<Badge color={template.isActive ? 'green' : 'gray'} variant="light">
									{template.isActive ? 'Active' : 'Inactive'}
								</Badge>
							</Group>

							<Group gap="xs">
								<ActionIcon variant="light" color="blue" onClick={() => copyTemplate(template)}>
									<IconCopy size={16} />
								</ActionIcon>
								<ActionIcon variant="light" color="green" onClick={() => handleTest(template)}>
									<IconTestPipe size={16} />
								</ActionIcon>
								<ActionIcon variant="light" color="blue" onClick={() => handleEdit(template)}>
									<IconEdit size={16} />
								</ActionIcon>
								<ActionIcon variant="light" color="red" onClick={() => handleDelete(template)}>
									<IconTrash size={16} />
								</ActionIcon>
							</Group>
						</Group>

						<Text size="sm" c="dimmed" mb="md">{template.description}</Text>

						<Tabs defaultValue="template">
							<Tabs.List>
								<Tabs.Tab value="template" leftSection={<IconTemplate size={16} />}>
									Template
								</Tabs.Tab>
								<Tabs.Tab value="performance" leftSection={<IconChartBar size={16} />}>
									Performance
								</Tabs.Tab>
							</Tabs.List>

							<Tabs.Panel value="template" pt="md">
								<Stack gap="sm">
									<Group>
										<Text size="sm" fw={500}>Variables:</Text>
										<Group gap="xs">
											{template.variables.map(variable => (
												<Badge key={variable} variant="outline" size="sm">
													{variable}
												</Badge>
											))}
										</Group>
									</Group>

									<Paper p="sm" withBorder>
										<Code block style={{ whiteSpace: 'pre-wrap', fontSize: '12px' }}>
											{template.template}
										</Code>
									</Paper>
								</Stack>
							</Tabs.Panel>

							<Tabs.Panel value="performance" pt="md">
								<Group grow>
									<div>
										<Text size="xs" c="dimmed" tt="uppercase" fw={700}>
											Usage Count
										</Text>
										<Text size="lg" fw={700}>
											{template.performance.usageCount.toLocaleString()}
										</Text>
									</div>

									<div>
										<Text size="xs" c="dimmed" tt="uppercase" fw={700}>
											Success Rate
										</Text>
										<Group gap="xs">
											<Progress
												value={template.performance.successRate}
												size="sm"
												color={template.performance.successRate > 95 ? 'green' : 'yellow'}
												style={{ width: 60 }}
											/>
											<Text size="sm">{template.performance.successRate.toFixed(1)}%</Text>
										</Group>
									</div>

									<div>
										<Text size="xs" c="dimmed" tt="uppercase" fw={700}>
											Quality Score
										</Text>
										<Badge
											color={getPerformanceColor(template.performance.qualityScore)}
											variant="light"
										>
											{template.performance.qualityScore.toFixed(1)}/10
										</Badge>
									</div>

									<div>
										<Text size="xs" c="dimmed" tt="uppercase" fw={700}>
											Avg Response Time
										</Text>
										<Text size="lg" fw={700}>
											{template.performance.averageResponseTime.toFixed(1)}s
										</Text>
									</div>
								</Group>
							</Tabs.Panel>
						</Tabs>
					</Card>
				))}
			</Stack>

			{/* Add/Edit Modal */}
			<Modal
				opened={modalOpened}
				onClose={() => setModalOpened(false)}
				title={selectedTemplate ? 'Edit Prompt Template' : 'Add Prompt Template'}
				size="xl"
			>
				<form onSubmit={form.onSubmit(handleSubmit)}>
					<Stack gap="md">
						<TextInput
							label="Template Name"
							placeholder="Domain Description Generator"
							required
							{...form.getInputProps('name')}
						/>

						<Textarea
							label="Description"
							placeholder="Generates comprehensive domain descriptions..."
							required
							{...form.getInputProps('description')}
						/>

						<Select
							label="Category"
							data={[
								{ value: 'domain-description', label: 'Domain Description' },
								{ value: 'categorization', label: 'Categorization' },
								{ value: 'tagging', label: 'Tagging' },
								{ value: 'seo-summary', label: 'SEO Summary' },
							]}
							required
							{...form.getInputProps('category')}
						/>

						<Textarea
							label="Template"
							placeholder="Generate a description for {{domain}}..."
							required
							minRows={8}
							{...form.getInputProps('template')}
							onChange={(event) =>
							{
								form.setFieldValue('template', event.currentTarget.value);
								const variables = extractVariables(event.currentTarget.value);
								form.setFieldValue('variables', variables);
							}}
						/>

						<Group>
							<Text size="sm" fw={500}>Detected Variables:</Text>
							<Group gap="xs">
								{form.values.variables?.map(variable => (
									<Badge key={variable} variant="outline" size="sm">
										{variable}
									</Badge>
								))}
							</Group>
						</Group>

						<Switch
							label="Active"
							{...form.getInputProps('isActive', { type: 'checkbox' })}
						/>

						<Group justify="flex-end" mt="md">
							<Button variant="light" onClick={() => setModalOpened(false)}>
								Cancel
							</Button>
							<Button type="submit">
								{selectedTemplate ? 'Update' : 'Add'} Template
							</Button>
						</Group>
					</Stack>
				</form>
			</Modal>

			{/* Test Modal */}
			<Modal
				opened={testModalOpened}
				onClose={() => setTestModalOpened(false)}
				title={`Test Template: ${selectedTemplate?.name}`}
				size="xl"
			>
				<Stack gap="md">
					<Group grow>
						<Select
							label="Provider"
							data={[
								{ value: 'openai', label: 'OpenAI' },
								{ value: 'claude', label: 'Claude' },
								{ value: 'google-ai', label: 'Google AI' },
							]}
							{...testForm.getInputProps('provider')}
						/>
						<Select
							label="Model"
							data={[
								{ value: 'gpt-4', label: 'GPT-4' },
								{ value: 'gpt-3.5-turbo', label: 'GPT-3.5 Turbo' },
								{ value: 'claude-3-opus', label: 'Claude 3 Opus' },
								{ value: 'claude-3-sonnet', label: 'Claude 3 Sonnet' },
							]}
							{...testForm.getInputProps('model')}
						/>
					</Group>

					<JsonInput
						label="Test Input"
						placeholder='{"domain": "example.com", "title": "Example Site"}'
						minRows={4}
						{...testForm.getInputProps('testInput')}
					/>

					<Group justify="flex-end">
						<Button variant="light" onClick={() => setTestModalOpened(false)}>
							Cancel
						</Button>
						<Button onClick={runTest} loading={testing}>
							Run Test
						</Button>
					</Group>

					{testResult && (
						<Paper p="md" withBorder mt="md">
							<Text fw={600} mb="md">Test Result</Text>

							<Stack gap="sm">
								<Group>
									<Text size="sm" fw={500}>Quality Score:</Text>
									<Badge color={getPerformanceColor(testResult.qualityScore)}>
										{testResult.qualityScore.toFixed(1)}/10
									</Badge>
								</Group>

								<Group>
									<Text size="sm" fw={500}>Response Time:</Text>
									<Text size="sm">{testResult.result.responseTime}ms</Text>
								</Group>

								<Group>
									<Text size="sm" fw={500}>Token Count:</Text>
									<Text size="sm">{testResult.result.tokenCount}</Text>
								</Group>

								<Group>
									<Text size="sm" fw={500}>Cost:</Text>
									<Text size="sm">${testResult.result.cost.toFixed(4)}</Text>
								</Group>

								{testResult.issues.length > 0 && (
									<Alert icon={<IconAlertCircle size={16} />} color="yellow">
										<Text size="sm" fw={500}>Issues:</Text>
										<ul style={{ margin: 0, paddingLeft: 20 }}>
											{testResult.issues.map((issue, index) => (
												<li key={index}>{issue}</li>
											))}
										</ul>
									</Alert>
								)}

								<div>
									<Text size="sm" fw={500} mb="xs">Generated Content:</Text>
									<Paper p="sm" withBorder>
										<Text size="sm" style={{ whiteSpace: 'pre-wrap' }}>
											{testResult.result.content}
										</Text>
									</Paper>
								</div>
							</Stack>
						</Paper>
					)}
				</Stack>
			</Modal>
		</Stack>
	);
}

export default AIPromptManagement;
