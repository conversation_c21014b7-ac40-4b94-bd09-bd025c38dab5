'use client';

import {
	Container,
	Title,
	Tabs,
	Paper,
	Group,
	Badge,
	Switch,
	Alert,
	LoadingOverlay,
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { clientLogger } from '@/lib/logger';
import {
	IconRobot, IconSettings, IconChartBar, IconTemplate, IconShield, IconBrain,
} from '@tabler/icons-react';
import { useState, useEffect } from 'react';


import type {
	AIProviderConfigType,
	AIServiceConfigType,
	AIUsageAnalyticsType,
	AIPromptTemplateType,
	AIContentQualityMetricsType,
} from '@/types/ai';
import { AIContentQuality } from './AIContentQuality';
import { AIPromptManagement } from './AIPromptManagement';
import { AIProviderConfig } from './AIProviderConfig';
import { AIServiceConfig } from './AIServiceConfig';
import { AIUsageAnalytics } from './AIUsageAnalytics';

export function AIServiceManagement()
{
	const [loading, setLoading] = useState(true);
	const [providers, setProviders] = useState<AIProviderConfigType[]>([]);
	const [serviceConfig, setServiceConfig] = useState<AIServiceConfigType | null>(null);
	const [analytics, setAnalytics] = useState<AIUsageAnalyticsType | null>(null);
	const [prompts, setPrompts] = useState<AIPromptTemplateType[]>([]);
	const [qualityMetrics, setQualityMetrics] = useState<AIContentQualityMetricsType | null>(null);
	const [activeTab, setActiveTab] = useState<string>('providers');

	useEffect(() =>
	{
		loadData();
	}, []);

	const loadData = async () =>
	{
		try
		{
			setLoading(true);

			const [providersRes, configRes, analyticsRes, promptsRes, qualityRes] = await Promise.all([
				fetch('/api/ai/providers'),
				fetch('/api/ai/config'),
				fetch('/api/ai/analytics'),
				fetch('/api/ai/prompts'),
				fetch('/api/ai/quality'),
			]);

			const [providersData, configData, analyticsData, promptsData, qualityData] = await Promise.all([
				providersRes.json(),
				configRes.json(),
				analyticsRes.json(),
				promptsRes.json(),
				qualityRes.json(),
			]);

			if (providersData.success) setProviders(providersData.data);
			if (configData.success) setServiceConfig(configData.data);
			if (analyticsData.success) setAnalytics(analyticsData.data);
			if (promptsData.success) setPrompts(promptsData.data);
			if (qualityData.success) setQualityMetrics(qualityData.data);
		}
		catch (error)
		{
			clientLogger.error('Error loading AI service data:', error);
			notifications.show({
				title: 'Error',
				message: 'Failed to load AI service data',
				color: 'red',
			});
		}
		finally
		{
			setLoading(false);
		}
	};

	const handleProviderUpdate = async (provider: AIProviderConfigType) =>
	{
		try
		{
			const response = await fetch(`/api/ai/providers/${provider.id}`, {
				method: 'PUT',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(provider),
			});

			const data = await response.json();
			if (data.success)
			{
				setProviders(prev => prev.map(p => (p.id === provider.id ? data.data : p)));
				notifications.show({
					title: 'Success',
					message: 'Provider updated successfully',
					color: 'green',
				});
			}
		}
		catch (error)
		{
			clientLogger.error('Error updating provider:', error);
			notifications.show({
				title: 'Error',
				message: 'Failed to update provider',
				color: 'red',
			});
		}
	};

	const handleProviderDelete = async (providerId: string) =>
	{
		try
		{
			const response = await fetch(`/api/ai/providers/${providerId}`, {
				method: 'DELETE',
			});

			const data = await response.json();
			if (data.success)
			{
				setProviders(prev => prev.filter(p => p.id !== providerId));
				notifications.show({
					title: 'Success',
					message: 'Provider deleted successfully',
					color: 'green',
				});
			}
		}
		catch (error)
		{
			clientLogger.error('Error deleting provider:', error);
			notifications.show({
				title: 'Error',
				message: 'Failed to delete provider',
				color: 'red',
			});
		}
	};

	const handleProviderAdd = async (provider: Omit<AIProviderConfigType, 'id'>) =>
	{
		try
		{
			const response = await fetch('/api/ai/providers', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(provider),
			});

			const data = await response.json();
			if (data.success)
			{
				setProviders(prev => [...prev, data.data]);
				notifications.show({
					title: 'Success',
					message: 'Provider added successfully',
					color: 'green',
				});
			}
		}
		catch (error)
		{
			clientLogger.error('Error adding provider:', error);
			notifications.show({
				title: 'Error',
				message: 'Failed to add provider',
				color: 'red',
			});
		}
	};

	const handleServiceConfigUpdate = async (config: AIServiceConfigType) =>
	{
		try
		{
			const response = await fetch('/api/ai/config', {
				method: 'PUT',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(config),
			});

			const data = await response.json();
			if (data.success)
			{
				setServiceConfig(data.data);
				notifications.show({
					title: 'Success',
					message: 'Service configuration updated successfully',
					color: 'green',
				});
			}
		}
		catch (error)
		{
			clientLogger.error('Error updating service config:', error);
			notifications.show({
				title: 'Error',
				message: 'Failed to update service configuration',
				color: 'red',
			});
		}
	};

	const activeProviders = providers.filter(p => p.isActive);
	const totalRequests = providers.reduce((sum, p) => sum + p.usage.totalRequests, 0);
	const averageSuccessRate = providers.length > 0
		? providers.reduce((sum, p) => sum + p.usage.successRate, 0) / providers.length
		: 0;

	return (
		<Container size="xl" py="md">
			<LoadingOverlay visible={loading} />

			<Group justify="space-between" mb="lg">
				<div>
					<Title order={2} mb="xs">
						<IconRobot size={28} style={{ marginRight: 8, verticalAlign: 'middle' }} />
						AI Service Management
					</Title>
					<Group gap="md">
						<Badge color={serviceConfig?.enabled ? 'green' : 'red'} variant="filled">
							{serviceConfig?.enabled ? 'Enabled' : 'Disabled'}
						</Badge>
						<Badge color="blue" variant="light">
							{activeProviders.length} Active Providers
						</Badge>
						<Badge color="cyan" variant="light">
							{totalRequests.toLocaleString()} Total Requests
						</Badge>
						<Badge color="teal" variant="light">
							{averageSuccessRate.toFixed(1)}% Success Rate
						</Badge>
					</Group>
				</div>

				{serviceConfig && (
					<Switch
						label="AI Services"
						checked={serviceConfig.enabled}
						onChange={(event) =>
						{
							const newConfig = { ...serviceConfig, enabled: event.currentTarget.checked };
							handleServiceConfigUpdate(newConfig);
						}}
						size="lg"
					/>
				)}
			</Group>

			{!serviceConfig?.enabled && (
				<Alert color="yellow" mb="lg" icon={<IconShield size={16} />}>
					AI services are currently disabled. Enable them to start using AI-powered content generation and analysis.
				</Alert>
			)}

			<Paper shadow="sm" p="md">
				<Tabs value={activeTab} onChange={setActiveTab}>
					<Tabs.List>
						<Tabs.Tab value="providers" leftSection={<IconBrain size={16} />}>
							Providers
						</Tabs.Tab>
						<Tabs.Tab value="analytics" leftSection={<IconChartBar size={16} />}>
							Analytics
						</Tabs.Tab>
						<Tabs.Tab value="prompts" leftSection={<IconTemplate size={16} />}>
							Prompts
						</Tabs.Tab>
						<Tabs.Tab value="quality" leftSection={<IconShield size={16} />}>
							Quality
						</Tabs.Tab>
						<Tabs.Tab value="config" leftSection={<IconSettings size={16} />}>
							Configuration
						</Tabs.Tab>
					</Tabs.List>

					<Tabs.Panel value="providers" pt="md">
						<AIProviderConfig
							providers={providers}
							onUpdate={handleProviderUpdate}
							onDelete={handleProviderDelete}
							onAdd={handleProviderAdd}
						/>
					</Tabs.Panel>

					<Tabs.Panel value="analytics" pt="md">
						{analytics && (
							<AIUsageAnalytics
								analytics={analytics}
								timeframe={analytics.timeframe}
								onTimeframeChange={async (start, end) =>
								{
									try
									{
										const params = new URLSearchParams({
											start: start.toISOString(),
											end: end.toISOString(),
										});

										const response = await fetch(`/api/ai/analytics?${params}`);
										const data = await response.json();

										if (data.success)
										{
											setAnalytics(data.data);
											notifications.show({
												title: 'Analytics Updated',
												message: `Analytics updated for ${start.toLocaleDateString()} - ${end.toLocaleDateString()}`,
												color: 'blue',
											});
										}
										else
										{
											throw new Error(data.error || 'Failed to update analytics timeframe');
										}
									}
									catch (error)
									{
										clientLogger.error('Error updating analytics timeframe:', error);
										notifications.show({
											title: 'Error',
											message: 'Failed to update analytics timeframe',
											color: 'red',
										});
									}
								}}
							/>
						)}
					</Tabs.Panel>

					<Tabs.Panel value="prompts" pt="md">
						<AIPromptManagement
							templates={prompts}
							onUpdate={async (template) =>
							{
								try
								{
									const response = await fetch(`/api/ai/prompts/${template.id}`, {
										method: 'PUT',
										headers: { 'Content-Type': 'application/json' },
										body: JSON.stringify(template),
									});

									const data = await response.json();
									if (data.success)
									{
										setPrompts(prev => prev.map(p => (p.id === template.id ? data.data : p)));
										notifications.show({
											title: 'Success',
											message: 'Prompt template updated successfully',
											color: 'green',
										});
									}
									else
									{
										throw new Error(data.error || 'Failed to update template');
									}
								}
								catch (error)
								{
									clientLogger.error('Error updating template:', error);
									notifications.show({
										title: 'Error',
										message: 'Failed to update prompt template',
										color: 'red',
									});
								}
							}}
							onDelete={async (templateId) =>
							{
								try
								{
									const response = await fetch(`/api/ai/prompts/${templateId}`, {
										method: 'DELETE',
									});

									const data = await response.json();
									if (data.success)
									{
										setPrompts(prev => prev.filter(p => p.id !== templateId));
										notifications.show({
											title: 'Success',
											message: 'Prompt template deleted successfully',
											color: 'green',
										});
									}
									else
									{
										throw new Error(data.error || 'Failed to delete template');
									}
								}
								catch (error)
								{
									clientLogger.error('Error deleting template:', error);
									notifications.show({
										title: 'Error',
										message: 'Failed to delete prompt template',
										color: 'red',
									});
								}
							}}
							onAdd={async (template) =>
							{
								try
								{
									const response = await fetch('/api/ai/prompts', {
										method: 'POST',
										headers: { 'Content-Type': 'application/json' },
										body: JSON.stringify(template),
									});

									const data = await response.json();
									if (data.success)
									{
										setPrompts(prev => [...prev, data.data]);
										notifications.show({
											title: 'Success',
											message: 'Prompt template created successfully',
											color: 'green',
										});
									}
									else
									{
										throw new Error(data.error || 'Failed to create template');
									}
								}
								catch (error)
								{
									clientLogger.error('Error creating template:', error);
									notifications.show({
										title: 'Error',
										message: 'Failed to create prompt template',
										color: 'red',
									});
								}
							}}
							onTest={async (templateId, testData) =>
							{
								try
								{
									const response = await fetch(`/api/ai/prompts/${templateId}/test`, {
										method: 'POST',
										headers: { 'Content-Type': 'application/json' },
										body: JSON.stringify(testData),
									});

									const data = await response.json();
									if (data.success)
									{
										notifications.show({
											title: 'Success',
											message: 'Template test completed successfully',
											color: 'green',
										});
										return data.data;
									}
									else
									{
										throw new Error(data.error || 'Failed to test template');
									}
								}
								catch (error)
								{
									clientLogger.error('Error testing template:', error);
									notifications.show({
										title: 'Error',
										message: 'Failed to test prompt template',
										color: 'red',
									});

									// Return mock data as fallback for UI
									return {
										id: `test-${Date.now()}`,
										templateId,
										testInput: testData,
										provider: 'mock',
										model: 'mock-model',
										result: {
											content: 'Test failed - mock result',
											confidence: 0,
											responseTime: 0,
											tokenCount: 0,
											cost: 0,
										},
										qualityScore: 0,
										issues: ['Test execution failed'],
										timestamp: new Date(),
									};
								}
							}}
						/>
					</Tabs.Panel>

					<Tabs.Panel value="quality" pt="md">
						{qualityMetrics && (
							<AIContentQuality
								metrics={qualityMetrics}
								onRefresh={loadData}
							/>
						)}
					</Tabs.Panel>

					<Tabs.Panel value="config" pt="md">
						{serviceConfig && (
							<AIServiceConfig
								config={serviceConfig}
								onUpdate={handleServiceConfigUpdate}
							/>
						)}
					</Tabs.Panel>
				</Tabs>
			</Paper>
		</Container>
	);
}

export default AIServiceManagement;
