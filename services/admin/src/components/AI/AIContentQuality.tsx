'use client';

import {
	Stack,
	Group,
	Card,
	Text,
	Grid,
	Paper,
	Table,
	Progress,
	Badge,
	Button,
	Select,
	Alert,
	ActionIcon,
	Tooltip,
} from '@mantine/core';
import {
	IconRefresh,
	IconTrendingUp,
	IconAlertTriangle,
	IconCheck,
	IconX,
	IconInfoCircle,
} from '@tabler/icons-react';
import { useState } from 'react';
import {
	LineChart,
	Line,
	BarChart,
	Bar,
	PieChart,
	Pie,
	Cell,
	XAxis,
	YAxis,
	CartesianGrid,
	Tooltip as RechartsTooltip,
	Legend,
	ResponsiveContainer,
} from 'recharts';

import type { AIContentQualityProps } from '@/types/ai';

const COLORS = ['#228be6', '#40c057', '#fab005', '#fd7e14', '#e03131'];

export function AIContentQuality({ metrics, onRefresh }: AIContentQualityProps)
{
	const [selectedTimeframe, setSelectedTimeframe] = useState('30d');

	const providerData = Object.entries(metrics.byProvider).map(([provider, data]) => ({
		name: provider.charAt(0).toUpperCase() + provider.slice(1),
		score: data.averageScore,
		assessments: data.assessments,
		passRate: data.passRate,
	}));

	const contentTypeData = Object.entries(metrics.byContentType).map(([type, data]) => ({
		name: type.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase()),
		score: data.averageScore,
		assessments: data.assessments,
		passRate: data.passRate,
	}));

	const getScoreColor = (score: number) =>
	{
		if (score >= 8) return 'green';
		if (score >= 6) return 'yellow';
		return 'red';
	};

	const getImpactColor = (impact: string) =>
	{
		switch (impact)
		{
			case 'high': return 'red';
			case 'medium': return 'yellow';
			case 'low': return 'blue';
			default: return 'gray';
		}
	};

	return (
		<Stack gap="md">
			<Group justify="space-between">
				<Text size="lg" fw={600}>AI Content Quality Assessment</Text>
				<Group>
					<Select
						value={selectedTimeframe}
						onChange={value => setSelectedTimeframe(value || '30d')}
						data={[
							{ value: '7d', label: 'Last 7 days' },
							{ value: '30d', label: 'Last 30 days' },
							{ value: '90d', label: 'Last 90 days' },
						]}
						size="sm"
					/>
					<Button leftSection={<IconRefresh size={16} />} onClick={onRefresh} variant="light">
						Refresh
					</Button>
				</Group>
			</Group>

			{/* Overall Quality Summary */}
			<Grid>
				<Grid.Col span={3}>
					<Card shadow="sm" padding="md" withBorder>
						<Group justify="space-between">
							<div>
								<Text size="xs" c="dimmed" tt="uppercase" fw={700}>
									Overall Score
								</Text>
								<Text size="xl" fw={700} c={getScoreColor(metrics.overall.averageScore)}>
									{metrics.overall.averageScore.toFixed(1)}/10
								</Text>
							</div>
							<IconTrendingUp size={24} color={getScoreColor(metrics.overall.averageScore)} />
						</Group>
					</Card>
				</Grid.Col>

				<Grid.Col span={3}>
					<Card shadow="sm" padding="md" withBorder>
						<Group justify="space-between">
							<div>
								<Text size="xs" c="dimmed" tt="uppercase" fw={700}>
									Total Assessments
								</Text>
								<Text size="xl" fw={700}>
									{metrics.overall.totalAssessments.toLocaleString()}
								</Text>
							</div>
							<IconCheck size={24} color="blue" />
						</Group>
					</Card>
				</Grid.Col>

				<Grid.Col span={3}>
					<Card shadow="sm" padding="md" withBorder>
						<Group justify="space-between">
							<div>
								<Text size="xs" c="dimmed" tt="uppercase" fw={700}>
									Pass Rate
								</Text>
								<Text size="xl" fw={700} c={metrics.overall.passRate > 90 ? 'green' : 'orange'}>
									{metrics.overall.passRate.toFixed(1)}%
								</Text>
							</div>
							<IconCheck size={24} color={metrics.overall.passRate > 90 ? 'green' : 'orange'} />
						</Group>
					</Card>
				</Grid.Col>

				<Grid.Col span={3}>
					<Card shadow="sm" padding="md" withBorder>
						<Group justify="space-between">
							<div>
								<Text size="xs" c="dimmed" tt="uppercase" fw={700}>
									Issues Found
								</Text>
								<Text size="xl" fw={700} c="orange">
									{metrics.commonIssues.reduce((sum, issue) => sum + issue.frequency, 0)}
								</Text>
							</div>
							<IconAlertTriangle size={24} color="orange" />
						</Group>
					</Card>
				</Grid.Col>
			</Grid>

			{/* Quality Trends */}
			<Paper shadow="sm" p="md" withBorder>
				<Text fw={600} mb="md">Quality Trends</Text>
				<ResponsiveContainer width="100%" height={300}>
					<LineChart data={metrics.trends}>
						<CartesianGrid strokeDasharray="3 3" />
						<XAxis
							dataKey="timestamp"
							tickFormatter={value => new Date(value).toLocaleDateString()}
						/>
						<YAxis domain={[0, 10]} />
						<RechartsTooltip
							labelFormatter={value => new Date(value).toLocaleString()}
							formatter={(value: number, name: string) => [
								name === 'averageScore' ? `${value.toFixed(1)}/10` : `${value.toFixed(1)}%`,
								name === 'averageScore' ? 'Quality Score' : 'Pass Rate',
							]}
						/>
						<Legend />
						<Line
							type="monotone"
							dataKey="averageScore"
							stroke="#228be6"
							strokeWidth={2}
							name="Quality Score"
						/>
						<Line
							type="monotone"
							dataKey="passRate"
							stroke="#40c057"
							strokeWidth={2}
							name="Pass Rate"
						/>
					</LineChart>
				</ResponsiveContainer>
			</Paper>

			{/* Provider and Content Type Performance */}
			<Grid>
				<Grid.Col span={6}>
					<Paper shadow="sm" p="md" withBorder>
						<Text fw={600} mb="md">Provider Quality Comparison</Text>
						<ResponsiveContainer width="100%" height={300}>
							<BarChart data={providerData}>
								<CartesianGrid strokeDasharray="3 3" />
								<XAxis dataKey="name" />
								<YAxis domain={[0, 10]} />
								<RechartsTooltip
									formatter={(value: number) => [`${value.toFixed(1)}/10`, 'Quality Score']}
								/>
								<Bar dataKey="score" fill="#228be6" />
							</BarChart>
						</ResponsiveContainer>
					</Paper>
				</Grid.Col>

				<Grid.Col span={6}>
					<Paper shadow="sm" p="md" withBorder>
						<Text fw={600} mb="md">Content Type Quality</Text>
						<ResponsiveContainer width="100%" height={300}>
							<PieChart>
								<Pie
									data={contentTypeData}
									cx="50%"
									cy="50%"
									labelLine={false}
									label={({ name, score }) => `${name}: ${score.toFixed(1)}`}
									outerRadius={80}
									fill="#8884d8"
									dataKey="score"
								>
									{contentTypeData.map((entry, index) => (
										<Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
									))}
								</Pie>
								<RechartsTooltip formatter={(value: number) => [`${value.toFixed(1)}/10`, 'Quality Score']} />
							</PieChart>
						</ResponsiveContainer>
					</Paper>
				</Grid.Col>
			</Grid>

			{/* Provider Performance Table */}
			<Paper shadow="sm" p="md" withBorder>
				<Text fw={600} mb="md">Provider Performance Details</Text>
				<Table>
					<Table.Thead>
						<Table.Tr>
							<Table.Th>Provider</Table.Th>
							<Table.Th>Quality Score</Table.Th>
							<Table.Th>Assessments</Table.Th>
							<Table.Th>Pass Rate</Table.Th>
							<Table.Th>Status</Table.Th>
						</Table.Tr>
					</Table.Thead>
					<Table.Tbody>
						{providerData.map(provider => (
							<Table.Tr key={provider.name}>
								<Table.Td>
									<Text fw={500}>{provider.name}</Text>
								</Table.Td>
								<Table.Td>
									<Badge color={getScoreColor(provider.score)} variant="light">
										{provider.score.toFixed(1)}/10
									</Badge>
								</Table.Td>
								<Table.Td>{provider.assessments.toLocaleString()}</Table.Td>
								<Table.Td>
									<Group gap="xs">
										<Progress
											value={provider.passRate}
											size="sm"
											color={provider.passRate > 90 ? 'green' : provider.passRate > 80 ? 'yellow' : 'red'}
											style={{ width: 60 }}
										/>
										<Text size="sm">{provider.passRate.toFixed(1)}%</Text>
									</Group>
								</Table.Td>
								<Table.Td>
									{provider.score >= 8 ? (
										<IconCheck size={16} color="green" />
									) : provider.score >= 6 ? (
										<IconAlertTriangle size={16} color="orange" />
									) : (
										<IconX size={16} color="red" />
									)}
								</Table.Td>
							</Table.Tr>
						))}
					</Table.Tbody>
				</Table>
			</Paper>

			{/* Content Type Performance Table */}
			<Paper shadow="sm" p="md" withBorder>
				<Text fw={600} mb="md">Content Type Performance</Text>
				<Table>
					<Table.Thead>
						<Table.Tr>
							<Table.Th>Content Type</Table.Th>
							<Table.Th>Quality Score</Table.Th>
							<Table.Th>Assessments</Table.Th>
							<Table.Th>Pass Rate</Table.Th>
						</Table.Tr>
					</Table.Thead>
					<Table.Tbody>
						{contentTypeData.map(type => (
							<Table.Tr key={type.name}>
								<Table.Td>
									<Text fw={500}>{type.name}</Text>
								</Table.Td>
								<Table.Td>
									<Badge color={getScoreColor(type.score)} variant="light">
										{type.score.toFixed(1)}/10
									</Badge>
								</Table.Td>
								<Table.Td>{type.assessments.toLocaleString()}</Table.Td>
								<Table.Td>
									<Group gap="xs">
										<Progress
											value={type.passRate}
											size="sm"
											color={type.passRate > 90 ? 'green' : type.passRate > 80 ? 'yellow' : 'red'}
											style={{ width: 60 }}
										/>
										<Text size="sm">{type.passRate.toFixed(1)}%</Text>
									</Group>
								</Table.Td>
							</Table.Tr>
						))}
					</Table.Tbody>
				</Table>
			</Paper>

			{/* Common Issues */}
			<Paper shadow="sm" p="md" withBorder>
				<Text fw={600} mb="md">Common Quality Issues</Text>
				<Stack gap="sm">
					{metrics.commonIssues.map((issue, index) => (
						<Alert
							key={index}
							icon={<IconInfoCircle size={16} />}
							color={getImpactColor(issue.impact)}
							variant="light"
						>
							<Group justify="space-between">
								<div>
									<Text fw={500}>{issue.issue}</Text>
									<Text size="sm" c="dimmed">
										Impact: {issue.impact} • Frequency: {issue.frequency} occurrences
									</Text>
								</div>
								<Badge color={getImpactColor(issue.impact)} variant="filled">
									{issue.impact}
								</Badge>
							</Group>
						</Alert>
					))}
				</Stack>
			</Paper>
		</Stack>
	);
}

export default AIContentQuality;
