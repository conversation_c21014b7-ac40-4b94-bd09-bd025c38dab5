'use client';

import {
	Stack,
	Group,
	Card,
	Text,
	Badge,
	Button,
	ActionIcon,
	Modal,
	TextInput,
	Select,
	NumberInput,
	Switch,
	Textarea,
	Table,
	Progress,
	Tooltip,
	Alert,
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { clientLogger } from '@/lib/logger';
import { modals } from '@mantine/modals';
import { notifications } from '@mantine/notifications';
import {
	IconPlus,
	IconEdit,
	IconTrash,
	IconRefresh,
	IconEye,
	IconEyeOff,
	IconAlertCircle,
	IconCheck,
	IconX,
} from '@tabler/icons-react';
import { useState } from 'react';

import type { AIProviderConfigType, AIProviderConfigProps } from '@/types/ai';

export function AIProviderConfig({
	providers, onUpdate, onDelete, onAdd,
}: AIProviderConfigProps)
{
	const [selectedProvider, setSelectedProvider] = useState<AIProviderConfigType | null>(null);
	const [modalOpened, setModalOpened] = useState(false);
	const [showApiKeys, setShowApiKeys] = useState<Record<string, boolean>>({});
	const [healthChecking, setHealthChecking] = useState<Record<string, boolean>>({});

	const form = useForm<Partial<AIProviderConfigType>>({
		initialValues: {
			name: '',
			type: 'openai',
			apiKey: '',
			baseUrl: '',
			isActive: true,
			priority: 1,
			rateLimit: {
				requestsPerMinute: 60,
				tokensPerMinute: 150000,
			},
		},
	});

	const handleEdit = (provider: AIProviderConfigType) =>
	{
		setSelectedProvider(provider);
		form.setValues(provider);
		setModalOpened(true);
	};

	const handleAdd = () =>
	{
		setSelectedProvider(null);
		form.reset();
		setModalOpened(true);
	};

	const handleSubmit = (values: Partial<AIProviderConfigType>) =>
	{
		if (selectedProvider)
		{
			onUpdate({ ...selectedProvider, ...values } as AIProviderConfigType);
		}
		else
		{
			onAdd(values as Omit<AIProviderConfigType, 'id'>);
		}
		setModalOpened(false);
	};

	const handleDelete = (provider: AIProviderConfigType) =>
	{
		modals.openConfirmModal({
			title: 'Delete AI Provider',
			children: (
				<Text size="sm">
					Are you sure you want to delete the provider "{provider.name}"? This action cannot be undone.
				</Text>
			),
			labels: { confirm: 'Delete', cancel: 'Cancel' },
			confirmProps: { color: 'red' },
			onConfirm: () => onDelete(provider.id),
		});
	};

	const handleHealthCheck = async (provider: AIProviderConfigType) =>
	{
		setHealthChecking(prev => ({ ...prev, [provider.id]: true }));

		try
		{
			const response = await fetch(`/api/ai/providers/${provider.id}/health`, {
				method: 'POST',
			});

			const data = await response.json();
			if (data.success)
			{
				notifications.show({
					title: 'Health Check Complete',
					message: `Provider ${provider.name} is ${data.data.status}`,
					color: data.data.status === 'healthy' ? 'green' : 'red',
				});
			}
		}
		catch (error)
		{
			clientLogger.error('Health check failed:', error);
			notifications.show({
				title: 'Health Check Failed',
				message: 'Failed to check provider health',
				color: 'red',
			});
		}
		finally
		{
			setHealthChecking(prev => ({ ...prev, [provider.id]: false }));
		}
	};

	const toggleApiKeyVisibility = (providerId: string) =>
	{
		setShowApiKeys(prev => ({ ...prev, [providerId]: !prev[providerId] }));
	};

	const maskApiKey = (apiKey: string) =>
	{
		if (apiKey.length <= 8) return '*'.repeat(apiKey.length);
		return apiKey.substring(0, 4) + '*'.repeat(apiKey.length - 8) + apiKey.substring(apiKey.length - 4);
	};

	const getStatusColor = (status: string) =>
	{
		switch (status)
		{
			case 'healthy': return 'green';
			case 'degraded': return 'yellow';
			case 'unhealthy': return 'red';
			default: return 'gray';
		}
	};

	return (
		<Stack gap="md">
			<Group justify="space-between">
				<Text size="lg" fw={600}>AI Provider Configuration</Text>
				<Button leftSection={<IconPlus size={16} />} onClick={handleAdd}>
					Add Provider
				</Button>
			</Group>

			<Stack gap="md">
				{providers.map(provider => (
					<Card key={provider.id} shadow="sm" padding="md" withBorder>
						<Group justify="space-between" mb="md">
							<Group>
								<Text fw={600} size="lg">{provider.name}</Text>
								<Badge color={provider.isActive ? 'green' : 'gray'} variant="filled">
									{provider.isActive ? 'Active' : 'Inactive'}
								</Badge>
								<Badge color={getStatusColor(provider.healthCheck.status)} variant="light">
									{provider.healthCheck.status}
								</Badge>
								<Badge color="blue" variant="outline">
									Priority {provider.priority}
								</Badge>
							</Group>

							<Group gap="xs">
								<Tooltip label="Health Check">
									<ActionIcon
										variant="light"
										color="blue"
										loading={healthChecking[provider.id]}
										onClick={() => handleHealthCheck(provider)}
									>
										<IconRefresh size={16} />
									</ActionIcon>
								</Tooltip>
								<Tooltip label="Edit">
									<ActionIcon variant="light" color="blue" onClick={() => handleEdit(provider)}>
										<IconEdit size={16} />
									</ActionIcon>
								</Tooltip>
								<Tooltip label="Delete">
									<ActionIcon variant="light" color="red" onClick={() => handleDelete(provider)}>
										<IconTrash size={16} />
									</ActionIcon>
								</Tooltip>
							</Group>
						</Group>

						<Stack gap="sm">
							<Group>
								<Text size="sm" c="dimmed" w={120}>Type:</Text>
								<Badge variant="outline">{provider.type}</Badge>
							</Group>

							<Group>
								<Text size="sm" c="dimmed" w={120}>API Key:</Text>
								<Group gap="xs">
									<Text size="sm" ff="monospace">
										{showApiKeys[provider.id] ? provider.apiKey : maskApiKey(provider.apiKey)}
									</Text>
									<ActionIcon
										size="sm"
										variant="subtle"
										onClick={() => toggleApiKeyVisibility(provider.id)}
									>
										{showApiKeys[provider.id] ? <IconEyeOff size={14} /> : <IconEye size={14} />}
									</ActionIcon>
								</Group>
							</Group>

							{provider.baseUrl && (
								<Group>
									<Text size="sm" c="dimmed" w={120}>Base URL:</Text>
									<Text size="sm" ff="monospace">{provider.baseUrl}</Text>
								</Group>
							)}

							<Group>
								<Text size="sm" c="dimmed" w={120}>Rate Limits:</Text>
								<Text size="sm">
									{provider.rateLimit.requestsPerMinute} req/min, {provider.rateLimit.tokensPerMinute.toLocaleString()} tokens/min
								</Text>
							</Group>

							<Group>
								<Text size="sm" c="dimmed" w={120}>Usage:</Text>
								<Group gap="md">
									<Text size="sm">{provider.usage.totalRequests.toLocaleString()} requests</Text>
									<Text size="sm">{provider.usage.totalTokens.toLocaleString()} tokens</Text>
									<Text size="sm">${provider.usage.totalCost.toFixed(2)} cost</Text>
									<Text size="sm">{provider.usage.successRate.toFixed(1)}% success</Text>
								</Group>
							</Group>

							{provider.healthCheck.lastCheck && (
								<Group>
									<Text size="sm" c="dimmed" w={120}>Last Check:</Text>
									<Group gap="md">
										<Text size="sm">{provider.healthCheck.lastCheck.toLocaleString()}</Text>
										<Text size="sm">{provider.healthCheck.responseTime}ms</Text>
										{provider.healthCheck.errorMessage && (
											<Text size="sm" c="red">{provider.healthCheck.errorMessage}</Text>
										)}
									</Group>
								</Group>
							)}

							{provider.models.length > 0 && (
								<div>
									<Text size="sm" c="dimmed" mb="xs">Models:</Text>
									<Table size="sm">
										<Table.Thead>
											<Table.Tr>
												<Table.Th>Model</Table.Th>
												<Table.Th>Max Tokens</Table.Th>
												<Table.Th>Cost/Token</Table.Th>
												<Table.Th>Status</Table.Th>
											</Table.Tr>
										</Table.Thead>
										<Table.Tbody>
											{provider.models.map(model => (
												<Table.Tr key={model.id}>
													<Table.Td>{model.name}</Table.Td>
													<Table.Td>{model.maxTokens.toLocaleString()}</Table.Td>
													<Table.Td>${model.costPerToken.toFixed(6)}</Table.Td>
													<Table.Td>
														{model.isActive ? (
															<IconCheck size={16} color="green" />
														) : (
															<IconX size={16} color="red" />
														)}
													</Table.Td>
												</Table.Tr>
											))}
										</Table.Tbody>
									</Table>
								</div>
							)}
						</Stack>
					</Card>
				))}
			</Stack>

			<Modal
				opened={modalOpened}
				onClose={() => setModalOpened(false)}
				title={selectedProvider ? 'Edit AI Provider' : 'Add AI Provider'}
				size="lg"
			>
				<form onSubmit={form.onSubmit(handleSubmit)}>
					<Stack gap="md">
						<TextInput
							label="Provider Name"
							placeholder="OpenAI GPT-4"
							required
							{...form.getInputProps('name')}
						/>

						<Select
							label="Provider Type"
							data={[
								{ value: 'openai', label: 'OpenAI' },
								{ value: 'claude', label: 'Anthropic Claude' },
								{ value: 'google-ai', label: 'Google AI' },
							]}
							required
							{...form.getInputProps('type')}
						/>

						<TextInput
							label="API Key"
							placeholder="sk-..."
							required
							type="password"
							{...form.getInputProps('apiKey')}
						/>

						<TextInput
							label="Base URL"
							placeholder="https://api.openai.com/v1"
							{...form.getInputProps('baseUrl')}
						/>

						<Group grow>
							<NumberInput
								label="Priority"
								placeholder="1"
								min={1}
								max={10}
								{...form.getInputProps('priority')}
							/>
							<Switch
								label="Active"
								{...form.getInputProps('isActive', { type: 'checkbox' })}
							/>
						</Group>

						<Group grow>
							<NumberInput
								label="Requests per Minute"
								placeholder="60"
								min={1}
								{...form.getInputProps('rateLimit.requestsPerMinute')}
							/>
							<NumberInput
								label="Tokens per Minute"
								placeholder="150000"
								min={1}
								{...form.getInputProps('rateLimit.tokensPerMinute')}
							/>
						</Group>

						<Group justify="flex-end" mt="md">
							<Button variant="light" onClick={() => setModalOpened(false)}>
								Cancel
							</Button>
							<Button type="submit">
								{selectedProvider ? 'Update' : 'Add'} Provider
							</Button>
						</Group>
					</Stack>
				</form>
			</Modal>
		</Stack>
	);
}

export default AIProviderConfig;
