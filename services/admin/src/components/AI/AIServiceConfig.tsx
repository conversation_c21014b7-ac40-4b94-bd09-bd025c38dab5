'use client';

import {
	<PERSON>ack,
	Group,
	Card,
	Text,
	Switch,
	Select,
	NumberInput,
	Button,
	Divider,
	Alert,
	MultiSelect,
	Slider,
	Paper,
	Grid,
	Badge,
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import {
	IconSettings, IconShield, IconDatabase, IconAlertCircle,
} from '@tabler/icons-react';
import { useState } from 'react';

import type { AIServiceConfigType } from '@/types/ai';

interface AIServiceConfigProps
{
	config: AIServiceConfigType;
	onUpdate: (config: AIServiceConfigType) => void;
}

export function AIServiceConfig({ config, onUpdate }: AIServiceConfigProps)
{
	const [hasChanges, setHasChanges] = useState(false);

	const form = useForm<AIServiceConfigType>({
		initialValues: config,
		onValuesChange: () => setHasChanges(true),
	});

	const handleSubmit = (values: AIServiceConfigType) =>
	{
		onUpdate(values);
		setHasChanges(false);
		notifications.show({
			title: 'Configuration Updated',
			message: 'AI service configuration has been updated successfully',
			color: 'green',
		});
	};

	const handleReset = () =>
	{
		form.setValues(config);
		setHasChanges(false);
	};

	return (
		<form onSubmit={form.onSubmit(handleSubmit)}>
			<Stack gap="lg">
				<Group justify="space-between">
					<Text size="lg" fw={600}>AI Service Configuration</Text>
					<Group>
						<Button variant="light" onClick={handleReset} disabled={!hasChanges}>
							Reset
						</Button>
						<Button type="submit" disabled={!hasChanges}>
							Save Changes
						</Button>
					</Group>
				</Group>

				{hasChanges && (
					<Alert icon={<IconAlertCircle size={16} />} color="blue">
						You have unsaved changes. Click "Save Changes" to apply them.
					</Alert>
				)}

				{/* General Settings */}
				<Card shadow="sm" padding="md" withBorder>
					<Group mb="md">
						<IconSettings size={20} />
						<Text fw={600}>General Settings</Text>
					</Group>

					<Stack gap="md">
						<Switch
							label="Enable AI Services"
							description="Master switch for all AI-powered features"
							{...form.getInputProps('enabled', { type: 'checkbox' })}
						/>

						<Select
							label="Default Provider"
							description="Primary AI provider for content generation"
							data={[
								{ value: 'openai', label: 'OpenAI' },
								{ value: 'claude', label: 'Anthropic Claude' },
								{ value: 'google-ai', label: 'Google AI' },
							]}
							{...form.getInputProps('defaultProvider')}
						/>

						<MultiSelect
							label="Fallback Providers"
							description="Providers to use when the default provider fails"
							data={[
								{ value: 'openai', label: 'OpenAI' },
								{ value: 'claude', label: 'Anthropic Claude' },
								{ value: 'google-ai', label: 'Google AI' },
							]}
							{...form.getInputProps('fallbackProviders')}
						/>
					</Stack>
				</Card>

				{/* Load Balancing */}
				<Card shadow="sm" padding="md" withBorder>
					<Group mb="md">
						<IconDatabase size={20} />
						<Text fw={600}>Load Balancing & Health Checks</Text>
					</Group>

					<Grid>
						<Grid.Col span={6}>
							<Select
								label="Load Balancing Strategy"
								description="How to distribute requests across providers"
								data={[
									{ value: 'round-robin', label: 'Round Robin' },
									{ value: 'least-loaded', label: 'Least Loaded' },
									{ value: 'priority', label: 'Priority Based' },
								]}
								{...form.getInputProps('loadBalancing.strategy')}
							/>
						</Grid.Col>

						<Grid.Col span={6}>
							<NumberInput
								label="Health Check Interval"
								description="Seconds between health checks"
								min={60}
								max={3600}
								suffix=" seconds"
								{...form.getInputProps('loadBalancing.healthCheckInterval')}
							/>
						</Grid.Col>
					</Grid>
				</Card>

				{/* Content Generation */}
				<Card shadow="sm" padding="md" withBorder>
					<Group mb="md">
						<IconSettings size={20} />
						<Text fw={600}>Content Generation Settings</Text>
					</Group>

					<Grid>
						<Grid.Col span={6}>
							<NumberInput
								label="Batch Size"
								description="Number of items to process in parallel"
								min={1}
								max={50}
								{...form.getInputProps('contentGeneration.batchSize')}
							/>
						</Grid.Col>

						<Grid.Col span={6}>
							<NumberInput
								label="Max Retries"
								description="Maximum retry attempts for failed requests"
								min={0}
								max={10}
								{...form.getInputProps('contentGeneration.maxRetries')}
							/>
						</Grid.Col>

						<Grid.Col span={6}>
							<NumberInput
								label="Timeout"
								description="Request timeout in milliseconds"
								min={5000}
								max={120000}
								suffix=" ms"
								{...form.getInputProps('contentGeneration.timeoutMs')}
							/>
						</Grid.Col>

						<Grid.Col span={6}>
							<div>
								<Text size="sm" fw={500} mb="xs">
									Quality Threshold: {form.values.contentGeneration.qualityThreshold.toFixed(1)}/10
								</Text>
								<Text size="xs" c="dimmed" mb="sm">
									Minimum quality score to accept generated content
								</Text>
								<Slider
									min={1}
									max={10}
									step={0.1}
									marks={[
										{ value: 1, label: '1' },
										{ value: 5, label: '5' },
										{ value: 10, label: '10' },
									]}
									{...form.getInputProps('contentGeneration.qualityThreshold')}
								/>
							</div>
						</Grid.Col>
					</Grid>
				</Card>

				{/* Content Moderation */}
				<Card shadow="sm" padding="md" withBorder>
					<Group mb="md">
						<IconShield size={20} />
						<Text fw={600}>Content Moderation & Safety</Text>
					</Group>

					<Stack gap="md">
						<Switch
							label="Enable Content Moderation"
							description="Automatically filter inappropriate content"
							{...form.getInputProps('moderation.enabled', { type: 'checkbox' })}
						/>

						<MultiSelect
							label="Content Filters"
							description="Types of content to filter out"
							data={[
								{ value: 'hate', label: 'Hate Speech' },
								{ value: 'violence', label: 'Violence' },
								{ value: 'sexual', label: 'Sexual Content' },
								{ value: 'harassment', label: 'Harassment' },
								{ value: 'self-harm', label: 'Self-harm' },
								{ value: 'illegal', label: 'Illegal Activities' },
							]}
							disabled={!form.values.moderation.enabled}
							{...form.getInputProps('moderation.filters')}
						/>

						<div>
							<Text size="sm" fw={500} mb="xs">
								Block Threshold: {form.values.moderation.blockThreshold.toFixed(1)}
							</Text>
							<Text size="xs" c="dimmed" mb="sm">
								Confidence threshold for blocking content (0.0 - 1.0)
							</Text>
							<Slider
								min={0}
								max={1}
								step={0.1}
								marks={[
									{ value: 0, label: '0.0' },
									{ value: 0.5, label: '0.5' },
									{ value: 1, label: '1.0' },
								]}
								disabled={!form.values.moderation.enabled}
								{...form.getInputProps('moderation.blockThreshold')}
							/>
						</div>
					</Stack>
				</Card>

				{/* Caching */}
				<Card shadow="sm" padding="md" withBorder>
					<Group mb="md">
						<IconDatabase size={20} />
						<Text fw={600}>Caching Configuration</Text>
					</Group>

					<Stack gap="md">
						<Switch
							label="Enable Response Caching"
							description="Cache AI responses to reduce costs and improve performance"
							{...form.getInputProps('caching.enabled', { type: 'checkbox' })}
						/>

						<Grid>
							<Grid.Col span={6}>
								<NumberInput
									label="Cache TTL"
									description="Time to live in seconds"
									min={300}
									max={86400}
									suffix=" seconds"
									disabled={!form.values.caching.enabled}
									{...form.getInputProps('caching.ttlSeconds')}
								/>
							</Grid.Col>

							<Grid.Col span={6}>
								<NumberInput
									label="Max Cache Size"
									description="Maximum number of cached responses"
									min={1000}
									max={100000}
									disabled={!form.values.caching.enabled}
									{...form.getInputProps('caching.maxSize')}
								/>
							</Grid.Col>
						</Grid>
					</Stack>
				</Card>

				{/* Configuration Summary */}
				<Paper shadow="sm" p="md" withBorder>
					<Text fw={600} mb="md">Configuration Summary</Text>
					<Grid>
						<Grid.Col span={6}>
							<Stack gap="xs">
								<Group justify="space-between">
									<Text size="sm">AI Services:</Text>
									<Badge color={form.values.enabled ? 'green' : 'red'}>
										{form.values.enabled ? 'Enabled' : 'Disabled'}
									</Badge>
								</Group>
								<Group justify="space-between">
									<Text size="sm">Default Provider:</Text>
									<Badge variant="outline">{form.values.defaultProvider}</Badge>
								</Group>
								<Group justify="space-between">
									<Text size="sm">Load Balancing:</Text>
									<Badge variant="outline">{form.values.loadBalancing.strategy}</Badge>
								</Group>
								<Group justify="space-between">
									<Text size="sm">Batch Size:</Text>
									<Badge variant="outline">{form.values.contentGeneration.batchSize}</Badge>
								</Group>
							</Stack>
						</Grid.Col>

						<Grid.Col span={6}>
							<Stack gap="xs">
								<Group justify="space-between">
									<Text size="sm">Content Moderation:</Text>
									<Badge color={form.values.moderation.enabled ? 'green' : 'gray'}>
										{form.values.moderation.enabled ? 'Enabled' : 'Disabled'}
									</Badge>
								</Group>
								<Group justify="space-between">
									<Text size="sm">Response Caching:</Text>
									<Badge color={form.values.caching.enabled ? 'green' : 'gray'}>
										{form.values.caching.enabled ? 'Enabled' : 'Disabled'}
									</Badge>
								</Group>
								<Group justify="space-between">
									<Text size="sm">Quality Threshold:</Text>
									<Badge variant="outline">{form.values.contentGeneration.qualityThreshold.toFixed(1)}/10</Badge>
								</Group>
								<Group justify="space-between">
									<Text size="sm">Timeout:</Text>
									<Badge variant="outline">{form.values.contentGeneration.timeoutMs / 1000}s</Badge>
								</Group>
							</Stack>
						</Grid.Col>
					</Grid>
				</Paper>
			</Stack>
		</form>
	);
}

export default AIServiceConfig;
