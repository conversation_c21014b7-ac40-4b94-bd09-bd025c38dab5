'use client';

import {
	Stack,
	Group,
	Card,
	Text,
	Grid,
	Select,
	Paper,
	Table,
	Progress,
	Badge,
	Tooltip,
	TextInput,
} from '@mantine/core';
import {
	IconTrendingUp, IconCoin, IconClock, IconCheck,
} from '@tabler/icons-react';
import { useState } from 'react';
import {
	LineChart,
	Line,
	BarChart,
	Bar,
	PieChart,
	Pie,
	Cell,
	XAxis,
	YAxis,
	CartesianGrid,
	Tooltip as RechartsTooltip,
	Legend,
	ResponsiveContainer,
} from 'recharts';

import type { AIUsageAnalyticsProps } from '@/types/ai';

const COLORS = ['#228be6', '#40c057', '#fab005', '#fd7e14', '#e03131', '#be4bdb'];

export function AIUsageAnalytics({ analytics, timeframe, onTimeframeChange }: AIUsageAnalyticsProps)
{
	const [selectedMetric, setSelectedMetric] = useState('requests');

	const providerData = Object.entries(analytics.providers).map(([provider, data]) => ({
		name: provider.charAt(0).toUpperCase() + provider.slice(1),
		requests: data.requests,
		tokens: data.tokens,
		cost: data.cost,
		successRate: data.successRate,
		responseTime: data.averageResponseTime,
		errorRate: data.errorRate,
	}));

	const modelData = Object.entries(analytics.models).map(([model, data]) => ({
		name: model,
		requests: data.requests,
		tokens: data.tokens,
		cost: data.cost,
		successRate: data.successRate,
		responseTime: data.averageResponseTime,
	}));

	const contentTypeData = Object.entries(analytics.contentTypes).map(([type, data]) => ({
		name: type.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase()),
		requests: data.requests,
		successRate: data.successRate,
		qualityScore: data.averageQualityScore,
	}));

	const totalRequests = providerData.reduce((sum, p) => sum + p.requests, 0);
	const totalCost = providerData.reduce((sum, p) => sum + p.cost, 0);
	const totalTokens = providerData.reduce((sum, p) => sum + p.tokens, 0);
	const averageSuccessRate = providerData.length > 0
		? providerData.reduce((sum, p) => sum + p.successRate, 0) / providerData.length
		: 0;

	return (
		<Stack gap="md">
			<Group justify="space-between">
				<Text size="lg" fw={600}>AI Usage Analytics</Text>
				<Group>
					<TextInput
						label="Start Date"
						value={timeframe.start.toISOString().split('T')[0]}
						onChange={(event) =>
						{
							const date = new Date(event.currentTarget.value);
							if (!isNaN(date.getTime()))
							{
								onTimeframeChange(date, timeframe.end);
							}
						}}
						type="date"
						size="sm"
					/>
					<TextInput
						label="End Date"
						value={timeframe.end.toISOString().split('T')[0]}
						onChange={(event) =>
						{
							const date = new Date(event.currentTarget.value);
							if (!isNaN(date.getTime()))
							{
								onTimeframeChange(timeframe.start, date);
							}
						}}
						type="date"
						size="sm"
					/>
				</Group>
			</Group>

			{/* Summary Cards */}
			<Grid>
				<Grid.Col span={3}>
					<Card shadow="sm" padding="md" withBorder>
						<Group justify="space-between">
							<div>
								<Text size="xs" c="dimmed" tt="uppercase" fw={700}>
									Total Requests
								</Text>
								<Text size="xl" fw={700}>
									{totalRequests.toLocaleString()}
								</Text>
							</div>
							<IconTrendingUp size={24} color="blue" />
						</Group>
					</Card>
				</Grid.Col>

				<Grid.Col span={3}>
					<Card shadow="sm" padding="md" withBorder>
						<Group justify="space-between">
							<div>
								<Text size="xs" c="dimmed" tt="uppercase" fw={700}>
									Total Cost
								</Text>
								<Text size="xl" fw={700}>
									${totalCost.toFixed(2)}
								</Text>
							</div>
							<IconCoin size={24} color="green" />
						</Group>
					</Card>
				</Grid.Col>

				<Grid.Col span={3}>
					<Card shadow="sm" padding="md" withBorder>
						<Group justify="space-between">
							<div>
								<Text size="xs" c="dimmed" tt="uppercase" fw={700}>
									Total Tokens
								</Text>
								<Text size="xl" fw={700}>
									{totalTokens.toLocaleString()}
								</Text>
							</div>
							<IconClock size={24} color="orange" />
						</Group>
					</Card>
				</Grid.Col>

				<Grid.Col span={3}>
					<Card shadow="sm" padding="md" withBorder>
						<Group justify="space-between">
							<div>
								<Text size="xs" c="dimmed" tt="uppercase" fw={700}>
									Success Rate
								</Text>
								<Text size="xl" fw={700}>
									{averageSuccessRate.toFixed(1)}%
								</Text>
							</div>
							<IconCheck size={24} color="teal" />
						</Group>
					</Card>
				</Grid.Col>
			</Grid>

			{/* Charts */}
			<Grid>
				<Grid.Col span={8}>
					<Paper shadow="sm" p="md" withBorder>
						<Group justify="space-between" mb="md">
							<Text fw={600}>Usage Trends</Text>
							<Select
								value={selectedMetric}
								onChange={value => setSelectedMetric(value || 'requests')}
								data={[
									{ value: 'requests', label: 'Requests' },
									{ value: 'tokens', label: 'Tokens' },
									{ value: 'cost', label: 'Cost' },
									{ value: 'successRate', label: 'Success Rate' },
								]}
								size="sm"
							/>
						</Group>
						<ResponsiveContainer width="100%" height={300}>
							<LineChart data={analytics.trends}>
								<CartesianGrid strokeDasharray="3 3" />
								<XAxis
									dataKey="timestamp"
									tickFormatter={value => new Date(value).toLocaleDateString()}
								/>
								<YAxis />
								<RechartsTooltip
									labelFormatter={value => new Date(value).toLocaleString()}
								/>
								<Legend />
								<Line
									type="monotone"
									dataKey={selectedMetric}
									stroke="#228be6"
									strokeWidth={2}
								/>
							</LineChart>
						</ResponsiveContainer>
					</Paper>
				</Grid.Col>

				<Grid.Col span={4}>
					<Paper shadow="sm" p="md" withBorder>
						<Text fw={600} mb="md">Provider Distribution</Text>
						<ResponsiveContainer width="100%" height={300}>
							<PieChart>
								<Pie
									data={providerData}
									cx="50%"
									cy="50%"
									labelLine={false}
									label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
									outerRadius={80}
									fill="#8884d8"
									dataKey="requests"
								>
									{providerData.map((entry, index) => (
										<Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
									))}
								</Pie>
								<RechartsTooltip />
							</PieChart>
						</ResponsiveContainer>
					</Paper>
				</Grid.Col>
			</Grid>

			{/* Provider Performance Table */}
			<Paper shadow="sm" p="md" withBorder>
				<Text fw={600} mb="md">Provider Performance</Text>
				<Table>
					<Table.Thead>
						<Table.Tr>
							<Table.Th>Provider</Table.Th>
							<Table.Th>Requests</Table.Th>
							<Table.Th>Tokens</Table.Th>
							<Table.Th>Cost</Table.Th>
							<Table.Th>Success Rate</Table.Th>
							<Table.Th>Avg Response Time</Table.Th>
							<Table.Th>Error Rate</Table.Th>
						</Table.Tr>
					</Table.Thead>
					<Table.Tbody>
						{providerData.map(provider => (
							<Table.Tr key={provider.name}>
								<Table.Td>
									<Text fw={500}>{provider.name}</Text>
								</Table.Td>
								<Table.Td>{provider.requests.toLocaleString()}</Table.Td>
								<Table.Td>{provider.tokens.toLocaleString()}</Table.Td>
								<Table.Td>${provider.cost.toFixed(2)}</Table.Td>
								<Table.Td>
									<Group gap="xs">
										<Progress
											value={provider.successRate}
											size="sm"
											color={provider.successRate > 95 ? 'green' : provider.successRate > 90 ? 'yellow' : 'red'}
											style={{ width: 60 }}
										/>
										<Text size="sm">{provider.successRate.toFixed(1)}%</Text>
									</Group>
								</Table.Td>
								<Table.Td>{provider.responseTime}ms</Table.Td>
								<Table.Td>
									<Badge
										color={provider.errorRate < 2 ? 'green' : provider.errorRate < 5 ? 'yellow' : 'red'}
										variant="light"
									>
										{provider.errorRate.toFixed(1)}%
									</Badge>
								</Table.Td>
							</Table.Tr>
						))}
					</Table.Tbody>
				</Table>
			</Paper>

			{/* Model Performance */}
			<Paper shadow="sm" p="md" withBorder>
				<Text fw={600} mb="md">Model Performance</Text>
				<ResponsiveContainer width="100%" height={300}>
					<BarChart data={modelData}>
						<CartesianGrid strokeDasharray="3 3" />
						<XAxis dataKey="name" />
						<YAxis />
						<RechartsTooltip />
						<Legend />
						<Bar dataKey="requests" fill="#228be6" name="Requests" />
						<Bar dataKey="cost" fill="#40c057" name="Cost ($)" />
					</BarChart>
				</ResponsiveContainer>
			</Paper>

			{/* Content Type Performance */}
			<Paper shadow="sm" p="md" withBorder>
				<Text fw={600} mb="md">Content Type Performance</Text>
				<Table>
					<Table.Thead>
						<Table.Tr>
							<Table.Th>Content Type</Table.Th>
							<Table.Th>Requests</Table.Th>
							<Table.Th>Success Rate</Table.Th>
							<Table.Th>Quality Score</Table.Th>
						</Table.Tr>
					</Table.Thead>
					<Table.Tbody>
						{contentTypeData.map(type => (
							<Table.Tr key={type.name}>
								<Table.Td>
									<Text fw={500}>{type.name}</Text>
								</Table.Td>
								<Table.Td>{type.requests.toLocaleString()}</Table.Td>
								<Table.Td>
									<Group gap="xs">
										<Progress
											value={type.successRate}
											size="sm"
											color={type.successRate > 95 ? 'green' : type.successRate > 90 ? 'yellow' : 'red'}
											style={{ width: 60 }}
										/>
										<Text size="sm">{type.successRate.toFixed(1)}%</Text>
									</Group>
								</Table.Td>
								<Table.Td>
									<Tooltip label="Quality score out of 10">
										<Badge
											color={type.qualityScore > 8 ? 'green' : type.qualityScore > 6 ? 'yellow' : 'red'}
											variant="light"
										>
											{type.qualityScore.toFixed(1)}/10
										</Badge>
									</Tooltip>
								</Table.Td>
							</Table.Tr>
						))}
					</Table.Tbody>
				</Table>
			</Paper>
		</Stack>
	);
}

export default AIUsageAnalytics;
