'use client';

import {
	Paper,
	TextInput,
	PasswordInput,
	Button,
	Title,
	Text,
	Alert,
	Stack,
	Container,
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { clientLogger } from '@/lib/logger';
import { IconAlertCircle } from '@tabler/icons-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

import { sanitizeInput } from '@/utils/validation';

interface LoginFormData
{
	username: string;
	password: string;
}

interface LoginResponse
{
	success: boolean;
	user?: {
		username: string;
		role: string;
		permissions: string[];
	};
	error?: string;
}

function LoginForm()
{
	const router = useRouter();
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [isLocked, setIsLocked] = useState(false);

	const form = useForm<LoginFormData>({
		initialValues: {
			username: '',
			password: '',
		},
		validate: {
			username: (value) =>
			{
				if (!value.trim()) return 'Username is required';
				if (value.length < 3) return 'Username must be at least 3 characters';
				if (!/^[a-zA-Z0-9_-]+$/.test(value)) return 'Username contains invalid characters';
				return null;
			},
			password: (value) =>
			{
				if (!value) return 'Password is required';
				if (value.length < 8) return 'Password must be at least 8 characters';
				return null;
			},
		},
	});

	const handleSubmit = async (values: LoginFormData) =>
	{
		if (isLocked) return;

		setLoading(true);
		setError(null);

		try
		{
			// Sanitize input values
			const sanitizedValues = {
				username: sanitizeInput(values.username.trim()),
				password: values.password,
			};

			const response = await fetch('/api/auth/login', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify(sanitizedValues),
			});

			const data: LoginResponse = await response.json();

			if (data.success)
			{
				// Clear password for security
				form.setFieldValue('password', '');

				// Redirect to dashboard
				router.push('/dashboard');
			}
			else
			{
				setError(data.error || 'Login failed');

				// Handle account lockout
				if (response.status === 429)
				{
					setIsLocked(true);
					// Auto-unlock after 15 minutes (for demo purposes)
					setTimeout(() => setIsLocked(false), 15 * 60 * 1000);
				}
			}
		}
		catch (err)
		{
			clientLogger.error('Login error:', err);
			setError(err instanceof Error ? err.message : 'Network error occurred');
		}
		finally
		{
			setLoading(false);
		}
	};

	return (
		<Container size={420} my={40}>
			<Title ta="center" mb="md">
				Admin Panel
			</Title>
			<Text c="dimmed" size="sm" ta="center" mb="xl">
				Sign in to access the administration dashboard
			</Text>

			<Paper withBorder shadow="md" p={30} mt={30} radius="md">
				<form onSubmit={form.onSubmit(handleSubmit)}>
					<Stack>
						{error && (
							<Alert
								icon={<IconAlertCircle size="1rem" />}
								title="Authentication Error"
								color="red"
								variant="light"
							>
								{error}
							</Alert>
						)}

						<TextInput
							label="Username"
							placeholder="Enter your username"
							required
							disabled={loading || isLocked}
							{...form.getInputProps('username')}
						/>

						<PasswordInput
							label="Password"
							placeholder="Enter your password"
							required
							disabled={loading || isLocked}
							{...form.getInputProps('password')}
						/>

						<Button
							type="submit"
							fullWidth
							loading={loading}
							disabled={isLocked}
							loaderProps={{ type: 'dots' }}
						>
							{loading ? 'Signing in...' : 'Sign In'}
						</Button>

						{isLocked && (
							<Text size="sm" c="red" ta="center">
								Account temporarily locked. Please try again later.
							</Text>
						)}
					</Stack>
				</form>
			</Paper>
		</Container>
	);
}

export default LoginForm;
