'use client';

import {
	<PERSON>,
	<PERSON>,
	Text,
	<PERSON><PERSON>,
	Stack,
	Group,
	Badge,
	Alert,
	Code,
	Divider,
} from '@mantine/core';
import {
	IconCheck, IconX, IconUser, IconClock,
} from '@tabler/icons-react';
import { useState, useEffect } from 'react';
import { clientLogger } from '@/lib/logger';

interface UserInfoType
{
	username: string;
	role: string;
	permissions: string[];
}

interface SessionInfoType
{
	sessionId: string;
	username: string;
	role: string;
	permissions: string[];
	createdAt: string;
	lastActivity: string;
	expiresAt: string;
	ipAddress: string;
	userAgent: string;
}

function AuthTest()
{
	const [userInfo, setUserInfo] = useState<UserInfoType | null>(null);
	const [sessions, setSessions] = useState<SessionInfoType[]>([]);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const fetchUserInfo = async () =>
	{
		try
		{
			const response = await fetch('/api/auth/session');
			if (response.ok)
			{
				const data = await response.json();
				setUserInfo(data.user);
			}
			else
			{
				setUserInfo(null);
			}
		}
		catch (err)
		{
			clientLogger.error('Failed to fetch user info:', err);
			setUserInfo(null);
		}
	};

	const fetchSessions = async () =>
	{
		try
		{
			const response = await fetch('/api/auth/sessions');
			if (response.ok)
			{
				const data = await response.json();
				setSessions(data.sessions);
			}
		}
		catch (err)
		{
			clientLogger.error('Failed to fetch sessions:', err);
		}
	};

	const testPermission = async (permission: string) =>
	{
		setLoading(true);
		setError(null);

		try
		{
			const response = await fetch('/api/users');
			if (response.ok)
			{
				setError(null);
				return true;
			}

			const data = await response.json();
			setError(data.error);
			return false;
		}
		catch (err)
		{
			setError('Network error');
			return false;
		}
		finally
		{
			setLoading(false);
		}
	};

	const destroySession = async (sessionId: string) =>
	{
		try
		{
			const response = await fetch('/api/auth/sessions', {
				method: 'DELETE',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ sessionId }),
			});

			if (response.ok)
			{
				await fetchSessions();
			}
		}
		catch (err)
		{
			clientLogger.error('Failed to destroy session:', err);
		}
	};

	useEffect(() =>
	{
		fetchUserInfo();
		fetchSessions();
	}, []);

	const getRoleColor = (role: string) =>
	{
		switch (role)
		{
			case 'super_admin':
				return 'yellow';
			case 'admin':
				return 'blue';
			case 'viewer':
				return 'gray';
			default:
				return 'gray';
		}
	};

	return (
		<Stack gap="md">
			<Title order={2}>Authentication System Test</Title>

			{/* Current User Info */}
			<Paper withBorder p="md">
				<Title order={3} mb="md">Current User</Title>
				{userInfo ? (
					<Stack gap="sm">
						<Group>
							<IconUser size={16} />
							<Text fw={500}>{userInfo.username}</Text>
							<Badge color={getRoleColor(userInfo.role)}>
								{userInfo.role}
							</Badge>
						</Group>

						<div>
							<Text size="sm" c="dimmed" mb="xs">Permissions:</Text>
							<Group gap="xs">
								{userInfo.permissions.map((permission, index) => (
									<Badge key={index} variant="light" size="sm">
										{permission}
									</Badge>
								))}
							</Group>
						</div>
					</Stack>
				) : (
					<Alert color="red" icon={<IconX size={16} />}>
						Not authenticated
					</Alert>
				)}
			</Paper>

			{/* Permission Test */}
			<Paper withBorder p="md">
				<Title order={3} mb="md">Permission Test</Title>
				<Group>
					<Button
						onClick={() => testPermission('users.view')}
						loading={loading}
					>
						Test Users API Access
					</Button>
					{error && (
						<Alert color="red" icon={<IconX size={16} />}>
							{error}
						</Alert>
					)}
				</Group>
			</Paper>

			{/* Active Sessions */}
			<Paper withBorder p="md">
				<Group justify="space-between" mb="md">
					<Title order={3}>Active Sessions</Title>
					<Button size="sm" variant="light" onClick={fetchSessions}>
						Refresh
					</Button>
				</Group>

				{sessions.length > 0 ? (
					<Stack gap="sm">
						{sessions.map(session => (
							<Paper key={session.sessionId} withBorder p="sm" bg="gray.0">
								<Group justify="space-between" align="flex-start">
									<div>
										<Group gap="xs" mb="xs">
											<IconClock size={14} />
											<Text size="sm" fw={500}>
												Session {session.sessionId.slice(0, 8)}...
											</Text>
											<Badge size="sm" color={getRoleColor(session.role)}>
												{session.role}
											</Badge>
										</Group>

										<Stack gap={4}>
											<Text size="xs" c="dimmed">
												Created: {new Date(session.createdAt).toLocaleString()}
											</Text>
											<Text size="xs" c="dimmed">
												Last Activity: {new Date(session.lastActivity).toLocaleString()}
											</Text>
											<Text size="xs" c="dimmed">
												Expires: {new Date(session.expiresAt).toLocaleString()}
											</Text>
											<Text size="xs" c="dimmed">
												IP: {session.ipAddress}
											</Text>
										</Stack>
									</div>

									<Button
										size="xs"
										color="red"
										variant="light"
										onClick={() => destroySession(session.sessionId)}
									>
										Destroy
									</Button>
								</Group>
							</Paper>
						))}
					</Stack>
				) : (
					<Text c="dimmed">No active sessions found</Text>
				)}
			</Paper>

			{/* Authentication Flow Test */}
			<Paper withBorder p="md">
				<Title order={3} mb="md">Test Credentials</Title>
				<Stack gap="sm">
					<div>
						<Text fw={500} mb="xs">Available Test Users:</Text>
						<Code block>
							superadmin / AdminPass123! (Super Admin - All Permissions)
							admin / AdminPass123! (Admin - Limited Permissions)
							viewer / AdminPass123! (Viewer - Read Only)
						</Code>
					</div>

					<Divider />

					<div>
						<Text fw={500} mb="xs">Authentication Features:</Text>
						<Stack gap={4}>
							<Group gap="xs">
								<IconCheck size={14} color="green" />
								<Text size="sm">Argon2 password hashing</Text>
							</Group>
							<Group gap="xs">
								<IconCheck size={14} color="green" />
								<Text size="sm">Iron-session with Redis storage</Text>
							</Group>
							<Group gap="xs">
								<IconCheck size={14} color="green" />
								<Text size="sm">Role-based access control</Text>
							</Group>
							<Group gap="xs">
								<IconCheck size={14} color="green" />
								<Text size="sm">8-hour session timeout</Text>
							</Group>
							<Group gap="xs">
								<IconCheck size={14} color="green" />
								<Text size="sm">Concurrent session support</Text>
							</Group>
							<Group gap="xs">
								<IconCheck size={14} color="green" />
								<Text size="sm">Rate limiting & account lockout</Text>
							</Group>
							<Group gap="xs">
								<IconCheck size={14} color="green" />
								<Text size="sm">Password strength validation</Text>
							</Group>
						</Stack>
					</div>
				</Stack>
			</Paper>
		</Stack>
	);
}

export { AuthTest };
