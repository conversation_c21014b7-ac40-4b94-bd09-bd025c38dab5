'use client';

import {
	AppShell,
	Burger,
	Group,
	Text,
	NavLink,
	Avatar,
	Menu,
	UnstyledButton,
	Badge,
	Loader,
	ActionIcon,
	Tooltip,
	Box,
	Stack,
	Divider,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { clientLogger } from '@/lib/logger';
import { notifications } from '@mantine/notifications';
import {
	IconDashboard,
	IconServer,
	IconWorld,
	IconRobot,
	IconUsers,
	IconSettings,
	IconChartBar,
	IconFileText,
	IconDatabase,
	IconBell,
	IconLogout,
	IconUser,
	IconCrown,
	IconShield,
	IconEye,
	IconSeeding,
	IconBrain,
	IconActivity,
	IconContrast,
	IconAccessible,
	IconAlertTriangle,
} from '@tabler/icons-react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { useState, useEffect, useCallback } from 'react';

import RealtimeAlertNotifications from '@/components/Realtime/RealtimeAlertNotifications';
import { RealtimeIndicator } from '@/components/Realtime/RealtimeIndicator';
import { useAccessibility } from '@/components/UI/AccessibilityProvider/AccessibilityProvider';
import {
	ResponsiveLayout,
	useBreakpoint,
} from '@/components/UI/ResponsiveLayout/ResponsiveLayout';
import StatusBadge from '@/components/UI/StatusBadge/StatusBadge';
import { ColorSchemeToggle } from '@/components/UI/ThemeProvider/ThemeProvider';

interface AppShellLayoutProps
{
	children: React.ReactNode;
}

interface UserInfoType
{
	username: string;
	role: string;
	permissions: string[];
}

const navigationItems = [
	{
		icon: IconDashboard, label: 'Dashboard', href: '/dashboard', description: 'System overview and metrics',
	},
	{
		icon: IconServer, label: 'Service Health', href: '/services', description: 'Monitor microservices health',
	},
	{
		icon: IconWorld, label: 'Domain Management', href: '/domains', description: 'Manage domain database',
	},
	{
		icon: IconRobot, label: 'Crawl Management', href: '/crawl', description: 'Control crawling operations',
	},
	{
		icon: IconSeeding, label: 'Domain Seeder', href: '/seeder', description: 'Manage domain discovery',
	},
	{
		icon: IconBrain, label: 'AI Services', href: '/ai', description: 'Configure AI providers',
	},
	{
		icon: IconChartBar, label: 'Analytics', href: '/analytics', description: 'View system analytics',
	},
	{
		icon: IconUsers, label: 'User Management', href: '/users', description: 'User management',
	},
	{
		icon: IconSettings, label: 'Configuration', href: '/config', description: 'System configuration',
	},
	{
		icon: IconFileText, label: 'System Logs', href: '/logging', description: 'System logs and debugging',
	},
	{
		icon: IconDatabase, label: 'Database Tools', href: '/database', description: 'Database operations',
	},
];

const alertsNavigation = [
	{ label: 'Dashboard', href: '/alerts' },
	{ label: 'Alert Rules', href: '/alerts/rules' },
	{ label: 'Notifications', href: '/alerts/notifications' },
	{ label: 'Test Framework', href: '/alerts/test' },
	{ label: 'Analytics', href: '/alerts/analytics' },
];

function AppShellLayout({ children }: AppShellLayoutProps)
{
	const [opened, { toggle }] = useDisclosure();
	const [navbarCollapsed, setNavbarCollapsed] = useState(false);
	const [userInfo, setUserInfo] = useState<UserInfoType | null>(null);
	const [loading, setLoading] = useState(true);
	const [sessionCheckFailed, setSessionCheckFailed] = useState(false);
	const pathname = usePathname();
	const router = useRouter();
	const { isMobile, isTablet } = useBreakpoint();
	const {
		toggleHighContrast, toggleReducedMotion, isHighContrast, isReducedMotion,
	} = useAccessibility();

	const fetchUserInfo = useCallback(async (retryCount = 0) =>
	{
		try
		{
			const response = await fetch('/api/auth/session', {
				credentials: 'include',
				cache: 'no-cache',
			});

			if (response.ok)
			{
				const data = await response.json();
				setUserInfo(data.user);
				setSessionCheckFailed(false);
			}
			else if (response.status === 401)
			{
				// Only redirect on explicit 401 (unauthorized)
				setSessionCheckFailed(true);
				router.push('/login');
			}
			else if (response.status >= 500 && retryCount < 2)
			{
				// Retry on server errors (up to 2 times)
				clientLogger.warn(`Session validation failed with ${response.status}, retrying... (${retryCount + 1}/2)`);
				setTimeout(() => fetchUserInfo(retryCount + 1), 1000 * (retryCount + 1));
				return;
			}
			else
			{
				// Other errors - log but don't redirect immediately
				clientLogger.error(`Session validation failed with status ${response.status}`);
				setSessionCheckFailed(true);
			}
		}
		catch (error)
		{
			if (retryCount < 2)
			{
				// Retry on network errors (up to 2 times)
				clientLogger.warn(`Session validation network error, retrying... (${retryCount + 1}/2):`, error);
				setTimeout(() => fetchUserInfo(retryCount + 1), 1000 * (retryCount + 1));
				return;
			}
			else
			{
				// After retries failed, log error but don't redirect
				clientLogger.error('Failed to fetch user info after retries:', error);
				setSessionCheckFailed(true);
			}
		}
		finally
		{
			setLoading(false);
		}
	}, [router]);

	useEffect(() =>
	{
		fetchUserInfo();
	}, [fetchUserInfo]);

	// Set up periodic session refresh (every 30 minutes)
	useEffect(() =>
	{
		if (!userInfo || sessionCheckFailed) return;

		const refreshSession = async () =>
		{
			try
			{
				clientLogger.debug('Refreshing session...');
				const response = await fetch('/api/auth/refresh', {
					method: 'POST',
					credentials: 'include',
				});

				if (response.ok)
				{
					clientLogger.debug('Session refreshed successfully');
				}
				else
				{
					clientLogger.warn('Session refresh failed, checking session status...');
					// If refresh fails, check session status
					fetchUserInfo();
				}
			}
			catch (error)
			{
				clientLogger.error('Session refresh error:', error);
				// If refresh fails, check session status
				fetchUserInfo();
			}
		};

		const refreshInterval = setInterval(refreshSession, 30 * 60 * 1000); // 30 minutes

		return () => clearInterval(refreshInterval);
	}, [userInfo, sessionCheckFailed, fetchUserInfo]);

	const handleLogout = async () =>
	{
		try
		{
			const response = await fetch('/api/auth/logout', {
				method: 'POST',
				credentials: 'include',
			});

			if (response.ok)
			{
				notifications.show({
					title: 'Logged out',
					message: 'You have been successfully logged out',
					color: 'green',
				});

				setTimeout(() =>
				{
					window.location.href = '/login';
				}, 1000);
			}
			else
			{
				throw new Error('Logout failed');
			}
		}
		catch (error)
		{
			clientLogger.error('Logout failed:', error);
			notifications.show({
				title: 'Logout failed',
				message: 'An error occurred during logout',
				color: 'red',
			});
		}
	};

	const getRoleIcon = (role: string) =>
	{
		switch (role)
		{
			case 'super_admin':
				return <IconCrown size={14} />;
			case 'admin':
				return <IconShield size={14} />;
			case 'viewer':
				return <IconEye size={14} />;
			default:
				return <IconUser size={14} />;
		}
	};

	const getRoleColor = (role: string) =>
	{
		switch (role)
		{
			case 'super_admin':
				return 'yellow';
			case 'admin':
				return 'blue';
			case 'viewer':
				return 'gray';
			default:
				return 'gray';
		}
	};

	const getRoleLabel = (role: string) =>
	{
		switch (role)
		{
			case 'super_admin':
				return 'Super Admin';
			case 'admin':
				return 'Admin';
			case 'viewer':
				return 'Viewer';
			default:
				return role;
		}
	};

	const renderNavigation = () => (
		<Stack gap="xs">
			{navigationItems.map(item => (
				<Tooltip
					key={item.href}
					label={item.description}
					position="right"
					disabled={!navbarCollapsed}
					withArrow
				>
					<NavLink
						component={Link}
						href={item.href}
						label={navbarCollapsed ? undefined : item.label}
						leftSection={<item.icon size={18} />}
						active={pathname === item.href}
						variant="subtle"
						style={{
							borderRadius: 'var(--mantine-radius-md)',
						}}
						rightSection={
							item.href === '/services' ? (
								<StatusBadge status="healthy" size="xs" showIcon={false} />
							) : undefined
						}
					/>
				</Tooltip>
			))}

			<Tooltip
				label="Alert management and notifications"
				position="right"
				disabled={!navbarCollapsed}
				withArrow
			>
				<NavLink
					label={navbarCollapsed ? undefined : 'Alerts & Notifications'}
					leftSection={<IconBell size={18} />}
					active={pathname.startsWith('/alerts')}
					childrenOffset={navbarCollapsed ? 0 : 28}
					variant="subtle"
					style={{
						borderRadius: 'var(--mantine-radius-md)',
					}}
					rightSection={(
						<Badge size="xs" color="red" variant="filled">
							3
						</Badge>
					)}
				>
					{!navbarCollapsed && alertsNavigation.map(item => (
						<NavLink
							key={item.href}
							component={Link}
							href={item.href}
							label={item.label}
							active={pathname === item.href}
							variant="subtle"
							style={{
								borderRadius: 'var(--mantine-radius-sm)',
							}}
						/>
					))}
				</NavLink>
			</Tooltip>
		</Stack>
	);

	const renderHeader = () => (
		<Group justify="space-between" h="100%" px="md">
			<Group>
				<Burger opened={opened} onClick={toggle} hiddenFrom="sm" size="sm" />
				<Text size="lg" fw={600} visibleFrom="sm">
					Domain Ranking Admin
				</Text>
				<Text size="md" fw={600} hiddenFrom="sm">
					Admin
				</Text>
			</Group>

			<Group gap="xs">
				<RealtimeIndicator />
				<RealtimeAlertNotifications />

				<Tooltip label="Toggle high contrast">
					<ActionIcon
						size="lg"
						variant={isHighContrast ? 'filled' : 'subtle'}
						onClick={toggleHighContrast}
						aria-label="Toggle high contrast"
						visibleFrom="md"
					>
						<IconContrast size={18} />
					</ActionIcon>
				</Tooltip>

				<Tooltip label="Toggle reduced motion">
					<ActionIcon
						size="lg"
						variant={isReducedMotion ? 'filled' : 'subtle'}
						onClick={toggleReducedMotion}
						aria-label="Toggle reduced motion"
						visibleFrom="md"
					>
						<IconAccessible size={18} />
					</ActionIcon>
				</Tooltip>

				<ColorSchemeToggle size="lg" />

				<Menu shadow="md" width={250} position="bottom-end">
					<Menu.Target>
						<UnstyledButton
							style={{
								padding: 'var(--mantine-spacing-xs)',
								borderRadius: 'var(--mantine-radius-md)',
							}}
						>
							<Group gap="xs">
								<Avatar size="sm" radius="xl" color={getRoleColor(userInfo?.role || '')} />
								<Box visibleFrom="sm">
									<Text size="sm" fw={500}>
										{userInfo?.username || 'Unknown User'}
									</Text>
									<Badge
										size="xs"
										color={getRoleColor(userInfo?.role || '')}
										leftSection={getRoleIcon(userInfo?.role || '')}
									>
										{getRoleLabel(userInfo?.role || '')}
									</Badge>
								</Box>
							</Group>
						</UnstyledButton>
					</Menu.Target>

					<Menu.Dropdown>
						<Menu.Label>Account</Menu.Label>
						<Menu.Item leftSection={<IconUser size={14} />}>
							Profile Settings
						</Menu.Item>
						<Menu.Divider />
						<Menu.Label>Accessibility</Menu.Label>
						<Menu.Item
							leftSection={<IconContrast size={16} />}
							onClick={toggleHighContrast}
							rightSection={isHighContrast ? <Badge size="xs">ON</Badge> : undefined}
						>
							High Contrast
						</Menu.Item>
						<Menu.Item
							leftSection={<IconAccessible size={16} />}
							onClick={toggleReducedMotion}
							rightSection={isReducedMotion ? <Badge size="xs">ON</Badge> : undefined}
						>
							Reduced Motion
						</Menu.Item>
						<Menu.Divider />
						<Menu.Label>Session</Menu.Label>
						<Menu.Item
							leftSection={<IconLogout size={14} />}
							color="red"
							onClick={handleLogout}
						>
							Logout
						</Menu.Item>
					</Menu.Dropdown>
				</Menu>
			</Group>
		</Group>
	);

	const renderNavbarFooter = () => (
		<>
			<Divider my="sm" />
			<UnstyledButton
				onClick={handleLogout}
				style={{
					display: 'block',
					width: '100%',
					padding: 'var(--mantine-spacing-xs)',
					borderRadius: 'var(--mantine-radius-md)',
					color: 'var(--mantine-color-red-6)',
					transition: 'background-color 0.2s ease',
				}}
				onMouseEnter={(e) =>
				{
					e.currentTarget.style.backgroundColor = 'var(--mantine-color-red-0)';
				}}
				onMouseLeave={(e) =>
				{
					e.currentTarget.style.backgroundColor = 'transparent';
				}}
			>
				<Group>
					<IconLogout size={18} />
					{!navbarCollapsed && (
						<Text size="sm" fw={500}>
							Logout
						</Text>
					)}
				</Group>
			</UnstyledButton>
		</>
	);

	if (loading)
	{
		return (
			<Group justify="center" style={{ height: '100vh' }}>
				<Loader size="lg" />
			</Group>
		);
	}

	return (
		<ResponsiveLayout
			header={renderHeader()}
			navbar={(
				<Stack justify="space-between" h="100%">
					<Box>
						{renderNavigation()}
					</Box>
					<Box>
						{renderNavbarFooter()}
					</Box>
				</Stack>
			)}
			navbarWidth={{
				base: navbarCollapsed ? 80 : 280,
				sm: navbarCollapsed ? 80 : 280,
				md: navbarCollapsed ? 80 : 300,
				lg: navbarCollapsed ? 80 : 320,
				xl: navbarCollapsed ? 80 : 340,
			}}
			headerHeight={60}
			navbarBreakpoint="sm"
			navbarCollapsed={navbarCollapsed}
			onNavbarToggle={setNavbarCollapsed}
			padding="md"
			withBorder
		>
			<Box
				id="main-content"
				tabIndex={-1}
				style={{
					outline: 'none',
					minHeight: 'calc(100vh - 120px)',
				}}
			>
				{children}
			</Box>
		</ResponsiveLayout>
	);
}

export default AppShellLayout;
