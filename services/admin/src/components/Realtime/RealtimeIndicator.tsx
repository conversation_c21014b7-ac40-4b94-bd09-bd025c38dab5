'use client';

import {
	Badge,
	Group,
	Text,
	Tooltip,
	ActionIcon,
	Modal,
	Stack,
	Card,
	Grid,
} from '@mantine/core';
import { IconActivity, IconSettings, IconRefresh } from '@tabler/icons-react';
import { useState, useEffect } from 'react';

import type { RealtimeStatsType } from '@/lib/realtime/types';
import useRealtime from '@/hooks/useRealtime';
import { ConnectionStatus } from './ConnectionStatus';


interface RealtimeIndicatorProps
{
	showDetails?: boolean;
	className?: string;
}

function formatBytes(bytes: number): string
{
	if (bytes === 0) return '0 B';

	const k = 1024;
	const sizes = ['B', 'KB', 'MB', 'GB'];
	const i = Math.floor(Math.log(bytes) / Math.log(k));

	return `${parseFloat((bytes / k ** i).toFixed(1))} ${sizes[i]}`;
}

function formatNumber(num: number): string
{
	if (num >= 1000000)
	{
		return `${(num / 1000000).toFixed(1)}M`;
	}
	if (num >= 1000)
	{
		return `${(num / 1000).toFixed(1)}K`;
	}

	return num.toString();
}

export function RealtimeIndicator({ showDetails = false, className }: RealtimeIndicatorProps)
{
	const {
		isConnected, connectionInfo, stats, updateConnectionStatus,
	} = useRealtime();
	const [showModal, setShowModal] = useState(false);
	const [lastStats, setLastStats] = useState<RealtimeStatsType | null>(null);

	useEffect(() =>
	{
		if (stats)
		{
			setLastStats(stats);
		}
	}, [stats]);

	const handleRefresh = () =>
	{
		updateConnectionStatus();
	};

	const getStatusColor = () =>
	{
		if (!isConnected) return 'red';

		const connections = Object.values(connectionInfo);
		if (connections.some(conn => conn.quality === 'critical')) return 'red';
		if (connections.some(conn => conn.quality === 'poor')) return 'yellow';
		if (connections.some(conn => conn.quality === 'good')) return 'blue';
		return 'green';
	};

	const getStatusText = () =>
	{
		if (!isConnected) return 'Disconnected';

		const connections = Object.values(connectionInfo);
		const connectedCount = connections.filter(conn => conn.status === 'connected').length;

		if (connectedCount === 0) return 'Connecting...';
		if (connectedCount === connections.length) return 'Connected';
		return `${connectedCount}/${connections.length} Connected`;
	};

	return (
		<>
			<Group gap="xs" className={className}>
				<Tooltip
					label={(
						<Stack gap="xs">
							<Text size="sm" fw={500}>Real-time Connection</Text>
							<Text size="xs">{getStatusText()}</Text>
							{lastStats && (
								<>
									<Text size="xs">Messages: {formatNumber(lastStats.messagesReceived)}</Text>
									<Text size="xs">Latency: {Math.round(lastStats.averageLatency)}ms</Text>
									<Text size="xs">Errors: {lastStats.errorCount}</Text>
								</>
							)}
						</Stack>
					)}
					position="bottom"
				>
					<Group gap="xs">
						<IconActivity
							size={16}
							color={`var(--mantine-color-${getStatusColor()}-6)`}
						/>

						<Badge
							size="sm"
							color={getStatusColor()}
							variant="light"
						>
							{isConnected ? 'Live' : 'Offline'}
						</Badge>
					</Group>
				</Tooltip>

				{showDetails && (
					<ConnectionStatus connections={connectionInfo} />
				)}

				<ActionIcon
					size="sm"
					variant="subtle"
					onClick={handleRefresh}
					title="Refresh connection status"
				>
					<IconRefresh size={14} />
				</ActionIcon>

				<ActionIcon
					size="sm"
					variant="subtle"
					onClick={() => setShowModal(true)}
					title="Connection details"
				>
					<IconSettings size={14} />
				</ActionIcon>
			</Group>

			<Modal
				opened={showModal}
				onClose={() => setShowModal(false)}
				title="Real-time Connection Details"
				size="lg"
			>
				<Stack gap="md">
					<Card withBorder>
						<Stack gap="sm">
							<Text fw={500}>Connection Status</Text>
							<ConnectionStatus connections={connectionInfo} />
						</Stack>
					</Card>

					{lastStats && (
						<Card withBorder>
							<Stack gap="sm">
								<Text fw={500}>Statistics</Text>
								<Grid>
									<Grid.Col span={6}>
										<Stack gap="xs">
											<Text size="sm" c="dimmed">Messages Received</Text>
											<Text fw={500}>{formatNumber(lastStats.messagesReceived)}</Text>
										</Stack>
									</Grid.Col>
									<Grid.Col span={6}>
										<Stack gap="xs">
											<Text size="sm" c="dimmed">Messages Processed</Text>
											<Text fw={500}>{formatNumber(lastStats.messagesProcessed)}</Text>
										</Stack>
									</Grid.Col>
									<Grid.Col span={6}>
										<Stack gap="xs">
											<Text size="sm" c="dimmed">Average Latency</Text>
											<Text fw={500}>{Math.round(lastStats.averageLatency)}ms</Text>
										</Stack>
									</Grid.Col>
									<Grid.Col span={6}>
										<Stack gap="xs">
											<Text size="sm" c="dimmed">Reconnects</Text>
											<Text fw={500}>{lastStats.reconnectCount}</Text>
										</Stack>
									</Grid.Col>
									<Grid.Col span={6}>
										<Stack gap="xs">
											<Text size="sm" c="dimmed">Data Received</Text>
											<Text fw={500}>{formatBytes(lastStats.bytesReceived)}</Text>
										</Stack>
									</Grid.Col>
									<Grid.Col span={6}>
										<Stack gap="xs">
											<Text size="sm" c="dimmed">Errors</Text>
											<Text fw={500} c={lastStats.errorCount > 0 ? 'red' : undefined}>
												{lastStats.errorCount}
											</Text>
										</Stack>
									</Grid.Col>
								</Grid>
							</Stack>
						</Card>
					)}

					<Card withBorder>
						<Stack gap="sm">
							<Text fw={500}>Connection Details</Text>
							{Object.entries(connectionInfo).map(([type, connection]) => (
								<Stack key={type} gap="xs">
									<Text size="sm" fw={500}>{type.toUpperCase()}</Text>
									<Grid>
										<Grid.Col span={4}>
											<Text size="xs" c="dimmed">Status</Text>
											<Badge size="xs" color={getStatusColor()}>
												{connection.status}
											</Badge>
										</Grid.Col>
										<Grid.Col span={4}>
											<Text size="xs" c="dimmed">Quality</Text>
											<Text size="xs">{connection.quality}</Text>
										</Grid.Col>
										<Grid.Col span={4}>
											<Text size="xs" c="dimmed">Latency</Text>
											<Text size="xs">{connection.latency}ms</Text>
										</Grid.Col>
									</Grid>
								</Stack>
							))}
						</Stack>
					</Card>
				</Stack>
			</Modal>
		</>
	);
}

export default RealtimeIndicator;
