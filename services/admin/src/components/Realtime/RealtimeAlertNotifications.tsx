'use client';

import {
	Card,
	Text,
	Badge,
	Group,
	Stack,
	ActionIcon,
	Tooltip,
	// Alert,
	Button,
	Modal,
	ScrollArea,
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { clientLogger } from '@/lib/logger';
import {
	IconBell,
	IconBellOff,
	IconVolume,
	IconVolumeOff,
	IconAlertTriangle,
	IconCheck,
	IconX,
	IconClock,
} from '@tabler/icons-react';
import { useState, useEffect, useRef } from 'react';

import { useRealtimeSubscription } from '@/hooks/useRealtimeSubscription';
import type { AlertInstanceType } from '@/types/alerts';
import type { AlertUpdateType } from '@/lib/realtime/types';

interface RealtimeAlertNotificationsProps
{
	className?: string;
	enableBrowserNotifications?: boolean;
	enableSoundAlerts?: boolean;
	maxAlerts?: number;
}

function RealtimeAlertNotifications({
	className,
	enableBrowserNotifications = true,
	enableSoundAlerts = true,
	maxAlerts = 50,
}: RealtimeAlertNotificationsProps)
{
	const [alerts, setAlerts] = useState<(
		AlertInstanceType & { id: string; receivedAt: Date }
	)[]>([]);
	const [notificationsEnabled, setNotificationsEnabled] = useState(enableBrowserNotifications);
	const [soundEnabled, setSoundEnabled] = useState(enableSoundAlerts);
	const [showModal, setShowModal] = useState(false);
	const [unreadCount, setUnreadCount] = useState(0);

	const audioRef = useRef<HTMLAudioElement | null>(null);

	// Initialize audio for sound alerts
	useEffect(() =>
	{
		if (soundEnabled)
		{
			audioRef.current = new Audio('/sounds/alert.mp3'); // You'd need to add this sound file
			audioRef.current.volume = 0.5;
		}
	}, [soundEnabled]);

	// Request browser notification permission
	useEffect(() =>
	{
		if (notificationsEnabled && 'Notification' in window && Notification.permission === 'default')
		{
			Notification.requestPermission();
		}
	}, [notificationsEnabled]);

	// Real-time alert subscription
	const { messageCount } = useRealtimeSubscription<AlertUpdateType>({
		type: 'alert_notification',
		onMessage: (message) =>
		{
			const update = message.data as AlertUpdateType['data'];
			const alertWithId = {
				...update.alert,
				id: `${update.alert.ruleId}-${Date.now()}`,
				receivedAt: new Date(),
			};

			setAlerts((prev) =>
			{
				const newAlerts = [alertWithId, ...prev].slice(0, maxAlerts);
				return newAlerts;
			});

			// Handle different alert actions
			switch (update.action)
			{
				case 'triggered':
					handleAlertTriggered(alertWithId);
					setUnreadCount(prev => prev + 1);
					break;
				case 'resolved':
					handleAlertResolved(alertWithId);
					break;
				case 'acknowledged':
					handleAlertAcknowledged(alertWithId);
					break;
				case 'escalated':
					handleAlertEscalated(alertWithId);
					setUnreadCount(prev => prev + 1);
					break;
			}
		},
	});

	const handleAlertTriggered = (alert: AlertInstanceType & { id: string; receivedAt: Date }) =>
	{
		// Show toast notification
		notifications.show({
			id: alert.id,
			title: `Alert: ${alert.ruleName}`,
			message: alert.message,
			color: getSeverityColor(alert.severity),
			icon: getAlertIcon(alert.severity),
			autoClose: alert.severity === 'critical' ? false : 5000,
		});

		// Browser notification
		if (notificationsEnabled && 'Notification' in window && Notification.permission === 'granted')
		{
			new Notification(`Alert: ${alert.ruleName}`, {
				body: alert.message,
				icon: '/favicon.ico',
				tag: alert.id,
			});
		}

		// Sound alert
		if (soundEnabled && audioRef.current)
		{
			audioRef.current.play().catch((error) => {
				clientLogger.warn('Failed to play alert sound:', { error });
			});
		}
	};

	const handleAlertResolved = (alert: AlertInstanceType & { id: string; receivedAt: Date }) =>
	{
		notifications.show({
			id: `${alert.id}-resolved`,
			title: `Resolved: ${alert.ruleName}`,
			message: 'Alert has been resolved',
			color: 'green',
			icon: <IconCheck size={16} />,
			autoClose: 3000,
		});
	};

	const handleAlertAcknowledged = (alert: AlertInstanceType & { id: string; receivedAt: Date }) =>
	{
		notifications.update({
			id: alert.id,
			title: `Acknowledged: ${alert.ruleName}`,
			message: 'Alert has been acknowledged',
			color: 'blue',
			icon: <IconCheck size={16} />,
			autoClose: 3000,
		});
	};

	const handleAlertEscalated = (alert: AlertInstanceType & { id: string; receivedAt: Date }) =>
	{
		notifications.show({
			id: `${alert.id}-escalated`,
			title: `Escalated: ${alert.ruleName}`,
			message: 'Alert has been escalated',
			color: 'red',
			icon: <IconAlertTriangle size={16} />,
			autoClose: false,
		});
	};

	const getSeverityColor = (severity: string) =>
	{
		switch (severity)
		{
			case 'critical':
				return 'red';
			case 'high':
				return 'orange';
			case 'medium':
				return 'yellow';
			case 'low':
				return 'blue';
			default:
				return 'gray';
		}
	};

	const getAlertIcon = (severity: string) =>
	{
		switch (severity)
		{
			case 'critical':
				return <IconX size={16} />;
			case 'high':
			case 'medium':
				return <IconAlertTriangle size={16} />;
			case 'low':
				return <IconClock size={16} />;
			default:
				return <IconBell size={16} />;
		}
	};

	const getActionColor = (action: string) =>
	{
		switch (action)
		{
			case 'triggered':
				return 'red';
			case 'resolved':
				return 'green';
			case 'acknowledged':
				return 'blue';
			case 'escalated':
				return 'orange';
			default:
				return 'gray';
		}
	};

	const formatTimeAgo = (date: Date) =>
	{
		const now = new Date();
		const diffMs = now.getTime() - date.getTime();
		const diffMins = Math.floor(diffMs / 60000);
		const diffHours = Math.floor(diffMins / 60);
		const diffDays = Math.floor(diffHours / 24);

		if (diffMins < 1) return 'Just now';
		if (diffMins < 60) return `${diffMins}m ago`;
		if (diffHours < 24) return `${diffHours}h ago`;
		return `${diffDays}d ago`;
	};

	const clearAllAlerts = () =>
	{
		setAlerts([]);
		setUnreadCount(0);
	};

	const markAllAsRead = () =>
	{
		setUnreadCount(0);
	};

	const acknowledgeAlert = async (alertId: string) =>
	{
		try
		{
			await fetch(`/api/alerts/${alertId}/acknowledge`, {
				method: 'POST',
			});
		}
		catch (error)
		{
			clientLogger.error('Failed to acknowledge alert:', error);
		}
	};

	return (
		<>
			<Group gap="xs" className={className}>
				{/* Alert Bell with Badge */}
				<div style={{ position: 'relative' }}>
					<ActionIcon
						variant="light"
						color={unreadCount > 0 ? 'red' : 'gray'}
						onClick={() => setShowModal(true)}
					>
						<IconBell size={16} />
					</ActionIcon>
					{unreadCount > 0 && (
						<Badge
							size="xs"
							color="red"
							style={{
								position: 'absolute',
								top: -8,
								right: -8,
								minWidth: 18,
								height: 18,
								padding: 0,
								display: 'flex',
								alignItems: 'center',
								justifyContent: 'center',
							}}
						>
							{unreadCount > 99 ? '99+' : unreadCount}
						</Badge>
					)}
				</div>

				{/* Notification Toggle */}
				<Tooltip label={notificationsEnabled ? 'Disable Notifications' : 'Enable Notifications'}>
					<ActionIcon
						variant="light"
						color={notificationsEnabled ? 'blue' : 'gray'}
						onClick={() => setNotificationsEnabled(!notificationsEnabled)}
					>
						{notificationsEnabled ? <IconBell size={16} /> : <IconBellOff size={16} />}
					</ActionIcon>
				</Tooltip>

				{/* Sound Toggle */}
				<Tooltip label={soundEnabled ? 'Disable Sound' : 'Enable Sound'}>
					<ActionIcon
						variant="light"
						color={soundEnabled ? 'blue' : 'gray'}
						onClick={() => setSoundEnabled(!soundEnabled)}
					>
						{soundEnabled ? <IconVolume size={16} /> : <IconVolumeOff size={16} />}
					</ActionIcon>
				</Tooltip>

				{/* Message Count */}
				<Text size="sm" c="dimmed">
					{messageCount} alerts
				</Text>
			</Group>

			{/* Alert Modal */}
			<Modal
				opened={showModal}
				onClose={() => setShowModal(false)}
				title="Real-time Alerts"
				size="lg"
			>
				<Stack gap="md">
					{/* Controls */}
					<Group justify="space-between">
						<Group gap="sm">
							<Button
								size="xs"
								variant="light"
								onClick={markAllAsRead}
								disabled={unreadCount === 0}
							>
								Mark All Read
							</Button>
							<Button
								size="xs"
								variant="light"
								color="red"
								onClick={clearAllAlerts}
								disabled={alerts.length === 0}
							>
								Clear All
							</Button>
						</Group>

						<Text size="sm" c="dimmed">
							{alerts.length} alerts
						</Text>
					</Group>

					{/* Alert List */}
					<ScrollArea h={400}>
						<Stack gap="xs">
							{alerts.length === 0 ? (
								<Text size="sm" c="dimmed" ta="center" py="xl">
									No alerts received
								</Text>
							) : (
								alerts.map(alert => (
									<Card key={alert.id} withBorder p="sm">
										<Stack gap="xs">
											<Group justify="space-between">
												<Group gap="sm">
													<Badge
														size="sm"
														color={getSeverityColor(alert.severity)}
														variant="light"
													>
														{alert.severity.toUpperCase()}
													</Badge>
													<Text fw={500} size="sm">
														{alert.ruleName}
													</Text>
												</Group>

												<Text size="xs" c="dimmed">
													{formatTimeAgo(alert.receivedAt)}
												</Text>
											</Group>

											<Text size="sm" c="dimmed">
												{alert.message}
											</Text>

											<Group justify="space-between">
												<Group gap="xs">
													<Badge size="xs" variant="outline">
														{alert.affectedServices?.[0] ?? 'system'}
													</Badge>
													{alert.tags?.map(tag => (
														<Badge key={tag} size="xs" variant="dot">
															{tag}
														</Badge>
													))}
												</Group>

												<Button
													size="xs"
													variant="light"
													onClick={() => acknowledgeAlert(alert.id)}
												>
													Acknowledge
												</Button>
											</Group>
										</Stack>
									</Card>
								))
							)}
						</Stack>
					</ScrollArea>
				</Stack>
			</Modal>
		</>
	);
}

export default RealtimeAlertNotifications;
