'use client';

import {
	Badge, Group, Text, Tooltip, Progress, Stack,
} from '@mantine/core';
import { IconWifi, IconWifiOff, IconRefresh } from '@tabler/icons-react';

import type { ConnectionInfoType, ConnectionQualityType } from '@/lib/realtime/types';

interface ConnectionStatusProps
{
	connections: Record<string, ConnectionInfoType>;
	className?: string;
}

function getQualityColor(quality: ConnectionQualityType): string
{
	switch (quality)
	{
		case 'excellent':
			return 'green';
		case 'good':
			return 'blue';
		case 'poor':
			return 'yellow';
		case 'critical':
			return 'red';
		default:
			return 'gray';
	}
}

function getStatusColor(status: string): string
{
	switch (status)
	{
		case 'connected':
			return 'green';
		case 'connecting':
		case 'reconnecting':
			return 'yellow';
		case 'disconnected':
		case 'error':
			return 'red';
		default:
			return 'gray';
	}
}

function formatUptime(uptime: number): string
{
	if (uptime === 0) return '0s';

	const seconds = Math.floor(uptime / 1000);
	const minutes = Math.floor(seconds / 60);
	const hours = Math.floor(minutes / 60);

	if (hours > 0)
	{
		return `${hours}h ${minutes % 60}m`;
	}
	if (minutes > 0)
	{
		return `${minutes}m ${seconds % 60}s`;
	}

	return `${seconds}s`;
}

export function ConnectionStatus({ connections, className }: ConnectionStatusProps)
{
	const connectionEntries = Object.entries(connections);
	const hasConnections = connectionEntries.length > 0;
	const allConnected = connectionEntries.every(([, conn]) => conn.status === 'connected');

	if (!hasConnections)
	{
		return (
			<Group gap="xs" className={className}>
				<IconWifiOff size={16} color="var(--mantine-color-gray-6)" />
				<Text size="sm" c="dimmed">No connections</Text>
			</Group>
		);
	}

	return (
		<Group gap="md" className={className}>
			{connectionEntries.map(([type, connection]) => (
				<Tooltip
					key={type}
					label={(
						<Stack gap="xs">
							<Text size="sm" fw={500}>{type.toUpperCase()} Connection</Text>
							<Text size="xs">Status: {connection.status}</Text>
							<Text size="xs">Quality: {connection.quality}</Text>
							<Text size="xs">Latency: {connection.latency}ms</Text>
							<Text size="xs">Uptime: {formatUptime(connection.uptime)}</Text>
							{connection.reconnectAttempts > 0 && (
								<Text size="xs">Reconnect attempts: {connection.reconnectAttempts}</Text>
							)}
						</Stack>
					)}
					position="bottom"
				>
					<Group gap="xs">
						{connection.status === 'connected' ? (
							<IconWifi size={16} color={`var(--mantine-color-${getQualityColor(connection.quality)}-6)`} />
						) : connection.status === 'connecting' || connection.status === 'reconnecting' ? (
							<IconRefresh size={16} color="var(--mantine-color-yellow-6)" />
						) : (
							<IconWifiOff size={16} color="var(--mantine-color-red-6)" />
						)}

						<Badge
							size="sm"
							color={getStatusColor(connection.status)}
							variant="light"
						>
							{type.toUpperCase()}
						</Badge>

						{connection.status === 'connected' && (
							<Text size="xs" c="dimmed">
								{connection.latency}ms
							</Text>
						)}
					</Group>
				</Tooltip>
			))}

			{connectionEntries.some(([, conn]) => conn.status === 'reconnecting') && (
				<Progress
					size="xs"
					value={100}
					animated
					color="yellow"
					style={{ width: 50 }}
				/>
			)}
		</Group>
	);
}

export default ConnectionStatus;
