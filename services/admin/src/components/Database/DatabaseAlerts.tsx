'use client';

import {
	// Grid,
	Card,
	Text,
	// Button,
	Select,
	Group,
	Stack,
	Title,
	Badge,
	Table,
	ActionIcon,
	Tooltip,
	Alert,
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import {
	// IconAlertTriangle,
	IconCheck,
	IconRefresh,
	IconBell,
} from '@tabler/icons-react';
import { useState, useEffect } from 'react';

import type { DatabaseAlert } from '@/types/database';

function DatabaseAlerts()
{
	const [alerts, setAlerts] = useState<DatabaseAlert[]>([]);
	const [loading, setLoading] = useState(false);
	const [selectedDatabase, setSelectedDatabase] = useState<string>('');
	const [selectedSeverity, setSelectedSeverity] = useState<string>('');

	const databases = ['scylla', 'mariadb', 'redis', 'manticore'];
	const severities = ['low', 'medium', 'high', 'critical'];

	const fetchAlerts = async () =>
	{
		try
		{
			setLoading(true);
			const params = new URLSearchParams();
			if (selectedDatabase) params.append('database', selectedDatabase);
			if (selectedSeverity) params.append('severity', selectedSeverity);
			params.append('active', 'true');

			const response = await fetch(`/api/database/alerts?${params}`);
			if (!response.ok) throw new Error('Failed to fetch alerts');

			const data = await response.json();
			setAlerts(data);
		}
		catch (error)
		{
			notifications.show({
				title: 'Error',
				message: 'Failed to fetch database alerts',
				color: 'red',
			});
		}
		finally
		{
			setLoading(false);
		}
	};

	useEffect(() =>
	{
		fetchAlerts();
	}, [selectedDatabase, selectedSeverity]);

	const acknowledgeAlert = async (alertId: string) =>
	{
		try
		{
			const response = await fetch('/api/database/alerts', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					action: 'acknowledge',
					alertId,
				}),
			});

			if (!response.ok) throw new Error('Failed to acknowledge alert');

			notifications.show({
				title: 'Success',
				message: 'Alert acknowledged',
				color: 'green',
			});

			fetchAlerts();
		}
		catch (error)
		{
			notifications.show({
				title: 'Error',
				message: 'Failed to acknowledge alert',
				color: 'red',
			});
		}
	};

	const resolveAlert = async (alertId: string) =>
	{
		try
		{
			const response = await fetch('/api/database/alerts', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					action: 'resolve',
					alertId,
				}),
			});

			if (!response.ok) throw new Error('Failed to resolve alert');

			notifications.show({
				title: 'Success',
				message: 'Alert resolved',
				color: 'green',
			});

			fetchAlerts();
		}
		catch (error)
		{
			notifications.show({
				title: 'Error',
				message: 'Failed to resolve alert',
				color: 'red',
			});
		}
	};

	const getSeverityColor = (severity: string) =>
	{
		switch (severity)
		{
			case 'critical': return 'red';
			case 'high': return 'orange';
			case 'medium': return 'yellow';
			case 'low': return 'blue';
			default: return 'gray';
		}
	};

	return (
		<Stack gap="md">
			<Group justify="space-between">
				<Title order={2}>Database Alerts</Title>
				<Group>
					<Select
						placeholder="Filter by database"
						data={databases.map(db => ({ value: db, label: db.toUpperCase() }))}
						value={selectedDatabase}
						onChange={value => setSelectedDatabase(value || '')}
						clearable
					/>
					<Select
						placeholder="Filter by severity"
						data={severities.map(s => ({ value: s, label: s.toUpperCase() }))}
						value={selectedSeverity}
						onChange={value => setSelectedSeverity(value || '')}
						clearable
					/>
					<Tooltip label="Refresh alerts">
						<ActionIcon variant="light" onClick={fetchAlerts} loading={loading}>
							<IconRefresh size={16} />
						</ActionIcon>
					</Tooltip>
				</Group>
			</Group>

			{alerts.length === 0 ? (
				<Alert color="green" icon={<IconCheck size={16} />}>
					No active database alerts found. All systems are operating normally.
				</Alert>
			) : (
				<Card>
					<Title order={3} mb="md">Active Alerts</Title>

					<Table>
						<Table.Thead>
							<Table.Tr>
								<Table.Th>Database</Table.Th>
								<Table.Th>Type</Table.Th>
								<Table.Th>Severity</Table.Th>
								<Table.Th>Title</Table.Th>
								<Table.Th>Metric</Table.Th>
								<Table.Th>Value</Table.Th>
								<Table.Th>Triggered</Table.Th>
								<Table.Th>Actions</Table.Th>
							</Table.Tr>
						</Table.Thead>
						<Table.Tbody>
							{alerts.map(alert => (
								<Table.Tr key={alert.id}>
									<Table.Td>
										<Text fw={500}>{alert.database.toUpperCase()}</Text>
									</Table.Td>
									<Table.Td>
										<Badge variant="light" tt="capitalize">
											{alert.type.replace('_', ' ')}
										</Badge>
									</Table.Td>
									<Table.Td>
										<Badge color={getSeverityColor(alert.severity)} variant="light">
											{alert.severity.toUpperCase()}
										</Badge>
									</Table.Td>
									<Table.Td>
										<Text fw={500}>{alert.title}</Text>
										<Text size="sm" c="dimmed">{alert.description}</Text>
									</Table.Td>
									<Table.Td>
										<Text size="sm">{alert.metric}</Text>
									</Table.Td>
									<Table.Td>
										<Text size="sm" c={alert.currentValue > alert.threshold ? 'red' : 'green'}>
											{alert.currentValue} / {alert.threshold}
										</Text>
									</Table.Td>
									<Table.Td>
										<Text size="sm">
											{new Date(alert.triggeredAt).toLocaleString()}
										</Text>
									</Table.Td>
									<Table.Td>
										<Group gap="xs">
											{!alert.acknowledgedAt && (
												<Tooltip label="Acknowledge alert">
													<ActionIcon
														variant="light"
														color="blue"
														onClick={() => acknowledgeAlert(alert.id)}
													>
														<IconBell size={16} />
													</ActionIcon>
												</Tooltip>
											)}
											<Tooltip label="Resolve alert">
												<ActionIcon
													variant="light"
													color="green"
													onClick={() => resolveAlert(alert.id)}
												>
													<IconCheck size={16} />
												</ActionIcon>
											</Tooltip>
										</Group>
									</Table.Td>
								</Table.Tr>
							))}
						</Table.Tbody>
					</Table>
				</Card>
			)}

			{alerts.length > 0 && (
				<Card>
					<Title order={3} mb="md">Recommended Actions</Title>
					<Stack gap="sm">
						{alerts.slice(0, 3).map(alert => (
							<Alert key={alert.id} color={getSeverityColor(alert.severity)} variant="light">
								<Stack gap="xs">
									<Text fw={500}>{alert.title}</Text>
									<Text size="sm">{alert.description}</Text>
									{alert.actions.length > 0 && (
										<Stack gap="xs">
											<Text size="sm" fw={500}>Recommended actions:</Text>
											{alert.actions.map((action, index) => (
												<Text key={index} size="sm">• {action}</Text>
											))}
										</Stack>
									)}
								</Stack>
							</Alert>
						))}
					</Stack>
				</Card>
			)}
		</Stack>
	);
}

export default DatabaseAlerts;
