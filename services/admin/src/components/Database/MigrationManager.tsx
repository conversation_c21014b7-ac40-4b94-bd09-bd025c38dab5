'use client';

import {
	<PERSON>rid,
	Card,
	Text,
	Button,
	Select,
	Group,
	Stack,
	Title,
	Badge,
	Table,
	ActionIcon,
	Tooltip,
	Modal,
	TextInput,
	Textarea,
	Alert,
	Code,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';
import {
	// IconDatabase,
	IconPlus,
	IconPlayerPlay,
	IconPlayerTrackPrev,
	IconRefresh,
	IconEye,
} from '@tabler/icons-react';
import { useState, useEffect } from 'react';

import type { SchemaMigration } from '@/types/database';

function MigrationManager()
{
	const [migrations, setMigrations] = useState<SchemaMigration[]>([]);
	const [loading, setLoading] = useState(false);
	const [selectedDatabase, setSelectedDatabase] = useState<string>('');
	const [createModalOpened, { open: openCreateModal, close: closeCreateModal }] = useDisclosure(false);
	const [detailsModalOpened, { open: openDetailsModal, close: closeDetailsModal }] = useDisclosure(false);
	const [selectedMigration, setSelectedMigration] = useState<SchemaMigration | null>(null);
	const [newMigration, setNewMigration] = useState({
		version: '',
		description: '',
		upScript: '',
		downScript: '',
	});

	const databases = ['scylla', 'mariadb', 'redis', 'manticore'];

	const fetchMigrations = async () =>
	{
		try
		{
			setLoading(true);
			const params = new URLSearchParams();
			if (selectedDatabase) params.append('database', selectedDatabase);

			const response = await fetch(`/api/database/migrations?${params}`);
			if (!response.ok) throw new Error('Failed to fetch migrations');

			const data = await response.json();
			setMigrations(data);
		}
		catch (error)
		{
			notifications.show({
				title: 'Error',
				message: 'Failed to fetch migrations',
				color: 'red',
			});
		}
		finally
		{
			setLoading(false);
		}
	};

	useEffect(() =>
	{
		fetchMigrations();
	}, [selectedDatabase]);

	const createMigration = async () =>
	{
		if (
			!selectedDatabase ||
			!newMigration.version ||
			!newMigration.description ||
			!newMigration.upScript
		)
		{
			notifications.show({
				title: 'Error',
				message: 'Please fill in all required fields',
				color: 'red',
			});
			return;
		}

		try
		{
			const response = await fetch('/api/database/migrations', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					action: 'create',
					database: selectedDatabase,
					...newMigration,
				}),
			});

			if (!response.ok) throw new Error('Failed to create migration');

			const result = await response.json();

			notifications.show({
				title: 'Success',
				message: `Migration ${newMigration.version} created successfully`,
				color: 'green',
			});

			closeCreateModal();
			setNewMigration({
				version: '', description: '', upScript: '', downScript: '',
			});
			fetchMigrations();
		}
		catch
		{
			notifications.show({
				title: 'Error',
				message: 'Failed to create migration',
				color: 'red',
			});
		}
	};

	const applyMigration = async (migrationId: string) =>
	{
		try
		{
			const response = await fetch('/api/database/migrations', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					action: 'apply',
					database: selectedDatabase,
					migrationId,
				}),
			});

			if (!response.ok) throw new Error('Failed to apply migration');

			const result = await response.json();

			notifications.show({
				title: 'Success',
				message: result.message,
				color: 'green',
			});

			fetchMigrations();
		}
		catch
		{
			notifications.show({
				title: 'Error',
				message: 'Failed to apply migration',
				color: 'red',
			});
		}
	};

	const rollbackMigration = async (migrationId: string) =>
	{
		try
		{
			const response = await fetch('/api/database/migrations', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					action: 'rollback',
					database: selectedDatabase,
					migrationId,
				}),
			});

			if (!response.ok) throw new Error('Failed to rollback migration');

			const result = await response.json();

			notifications.show({
				title: 'Success',
				message: result.message,
				color: 'green',
			});

			fetchMigrations();
		}
		catch
		{
			notifications.show({
				title: 'Error',
				message: 'Failed to rollback migration',
				color: 'red',
			});
		}
	};

	const viewMigrationDetails = (migration: SchemaMigration) =>
	{
		setSelectedMigration(migration);
		openDetailsModal();
	};

	const getStatusColor = (status: string) =>
	{
		switch (status)
		{
			case 'completed': return 'green';
			case 'running': return 'blue';
			case 'pending': return 'yellow';
			case 'failed': return 'red';
			case 'rolled_back': return 'orange';
			default: return 'gray';
		}
	};

	return (
		<Stack gap="md">
			<Group justify="space-between">
				<Title order={2}>Schema Migration Manager</Title>
				<Group>
					<Select
						placeholder="Filter by database"
						data={databases.map(db => ({ value: db, label: db.toUpperCase() }))}
						value={selectedDatabase}
						onChange={value => setSelectedDatabase(value || '')}
						clearable
					/>
					<Button onClick={openCreateModal} leftSection={<IconPlus size={16} />}>
						Create Migration
					</Button>
					<Tooltip label="Refresh migrations">
						<ActionIcon variant="light" onClick={fetchMigrations} loading={loading}>
							<IconRefresh size={16} />
						</ActionIcon>
					</Tooltip>
				</Group>
			</Group>

			<Card>
				<Title order={3} mb="md">Database Migrations</Title>

				{migrations.length === 0 ? (
					<Text c="dimmed" ta="center" py="xl">
						No migrations found
					</Text>
				) : (
					<Table>
						<Table.Thead>
							<Table.Tr>
								<Table.Th>Version</Table.Th>
								<Table.Th>Database</Table.Th>
								<Table.Th>Description</Table.Th>
								<Table.Th>Status</Table.Th>
								<Table.Th>Applied</Table.Th>
								<Table.Th>Dependencies</Table.Th>
								<Table.Th>Actions</Table.Th>
							</Table.Tr>
						</Table.Thead>
						<Table.Tbody>
							{migrations.map(migration => (
								<Table.Tr key={migration.id}>
									<Table.Td>
										<Text fw={500}>{migration.version}</Text>
									</Table.Td>
									<Table.Td>
										<Text>{migration.database.toUpperCase()}</Text>
									</Table.Td>
									<Table.Td>
										<Text size="sm">{migration.description}</Text>
									</Table.Td>
									<Table.Td>
										<Badge color={getStatusColor(migration.status)} variant="light">
											{migration.status.replace('_', ' ')}
										</Badge>
									</Table.Td>
									<Table.Td>
										<Text size="sm">
											{migration.appliedAt ? new Date(migration.appliedAt).toLocaleString() : 'Not applied'}
										</Text>
									</Table.Td>
									<Table.Td>
										<Text size="sm">{migration.dependencies.length > 0 ? migration.dependencies.join(', ') : 'None'}</Text>
									</Table.Td>
									<Table.Td>
										<Group gap="xs">
											<Tooltip label="View details">
												<ActionIcon
													variant="light"
													onClick={() => viewMigrationDetails(migration)}
												>
													<IconEye size={16} />
												</ActionIcon>
											</Tooltip>
											{migration.status === 'pending' && (
												<Tooltip label="Apply migration">
													<ActionIcon
														variant="light"
														color="green"
														onClick={() => applyMigration(migration.id)}
													>
														<IconPlayerPlay size={16} />
													</ActionIcon>
												</Tooltip>
											)}
											{migration.status === 'completed' && migration.downScript && (
												<Tooltip label="Rollback migration">
													<ActionIcon
														variant="light"
														color="orange"
														onClick={() => rollbackMigration(migration.id)}
													>
														<IconPlayerTrackPrev size={16} />
													</ActionIcon>
												</Tooltip>
											)}
										</Group>
									</Table.Td>
								</Table.Tr>
							))}
						</Table.Tbody>
					</Table>
				)}
			</Card>

			<Modal
				opened={createModalOpened}
				onClose={closeCreateModal}
				title="Create Schema Migration"
				size="lg"
			>
				<Stack gap="md">
					<Select
						label="Database"
						placeholder="Select database"
						data={databases.map(db => ({ value: db, label: db.toUpperCase() }))}
						value={selectedDatabase}
						onChange={value => setSelectedDatabase(value || '')}
						required
					/>

					<TextInput
						label="Version"
						placeholder="e.g., 001_add_user_table"
						value={newMigration.version}
						onChange={event => setNewMigration(prev => ({ ...prev, version: event.currentTarget.value }))}
						required
					/>

					<TextInput
						label="Description"
						placeholder="Brief description of the migration"
						value={newMigration.description}
						onChange={event => setNewMigration(prev => ({ ...prev, description: event.currentTarget.value }))}
						required
					/>

					<Textarea
						label="Up Script (SQL)"
						placeholder="SQL statements to apply the migration"
						value={newMigration.upScript}
						onChange={event => setNewMigration(prev => ({ ...prev, upScript: event.currentTarget.value }))}
						minRows={6}
						required
					/>

					<Textarea
						label="Down Script (SQL) - Optional"
						placeholder="SQL statements to rollback the migration"
						value={newMigration.downScript}
						onChange={event => setNewMigration(prev => ({ ...prev, downScript: event.currentTarget.value }))}
						minRows={4}
					/>

					<Alert color="blue">
						Migrations will be applied in version order.
						{' '}
						Use dependencies in comments (-- @depends: version) if needed.
					</Alert>

					<Group justify="flex-end">
						<Button variant="light" onClick={closeCreateModal}>
							Cancel
						</Button>
						<Button onClick={createMigration}>
							Create Migration
						</Button>
					</Group>
				</Stack>
			</Modal>

			<Modal
				opened={detailsModalOpened}
				onClose={closeDetailsModal}
				title={`Migration Details - ${selectedMigration?.version}`}
				size="xl"
			>
				{selectedMigration && (
					<Stack gap="md">
						<Group>
							<Badge color={getStatusColor(selectedMigration.status)}>
								{selectedMigration.status.replace('_', ' ')}
							</Badge>
							<Text size="sm" c="dimmed">
								{selectedMigration.database.toUpperCase()}
							</Text>
						</Group>

						<div>
							<Text fw={500} mb="xs">Description</Text>
							<Text>{selectedMigration.description}</Text>
						</div>

						{selectedMigration.dependencies.length > 0 && (
							<div>
								<Text fw={500} mb="xs">Dependencies</Text>
								<Text>{selectedMigration.dependencies.join(', ')}</Text>
							</div>
						)}

						<div>
							<Text fw={500} mb="xs">Up Script</Text>
							<Code block>{selectedMigration.upScript}</Code>
						</div>

						{selectedMigration.downScript && (
							<div>
								<Text fw={500} mb="xs">Down Script</Text>
								<Code block>{selectedMigration.downScript}</Code>
							</div>
						)}

						<Grid>
							<Grid.Col span={6}>
								<Text size="sm" c="dimmed">Applied At</Text>
								<Text>{selectedMigration.appliedAt ? new Date(selectedMigration.appliedAt).toLocaleString() : 'Not applied'}</Text>
							</Grid.Col>
							<Grid.Col span={6}>
								<Text size="sm" c="dimmed">Rolled Back At</Text>
								<Text>{selectedMigration.rolledBackAt ? new Date(selectedMigration.rolledBackAt).toLocaleString() : 'Not rolled back'}</Text>
							</Grid.Col>
						</Grid>
					</Stack>
				)}
			</Modal>
		</Stack>
	);
}

export default MigrationManager;
