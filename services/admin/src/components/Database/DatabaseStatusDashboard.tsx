'use client';

import {
	<PERSON><PERSON>,
	Card,
	Text,
	Badge,
	Progress,
	Group,
	Stack,
	Title,
	Button,
	Select,
	Alert,
	Loader,
	ActionIcon,
	Tooltip,
} from '@mantine/core';
import {
	IconRefresh, IconDatabase, IconAlertTriangle, IconCheck,
} from '@tabler/icons-react';
import { useState, useEffect } from 'react';
import {
	LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer,
} from 'recharts';

import type { DatabaseConnectionInfo, DatabasePerformanceMetrics } from '@/types/database';

interface DatabaseStatusWithMetrics extends DatabaseConnectionInfo
{
	metrics: DatabasePerformanceMetrics;
}

function DatabaseStatusDashboard()
{
	const [databases, setDatabases] = useState<DatabaseStatusWithMetrics[]>([]);
	const [selectedDatabase, setSelectedDatabase] = useState<string>('');
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [refreshing, setRefreshing] = useState(false);

	const fetchDatabaseStatus = async (database?: string) =>
	{
		try
		{
			setRefreshing(true);
			const url = database ? `/api/database/status?database=${database}` : '/api/database/status';
			const response = await fetch(url);

			if (!response.ok)
			{
				throw new Error('Failed to fetch database status');
			}

			const data = await response.json();

			if (database)
			{
				setDatabases(prev => prev.map(db => (db.database === database ? data : db)));
			}
			else
			{
				setDatabases(Array.isArray(data) ? data : [data]);
			}

			setError(null);
		}
		catch (err)
		{
			setError(err instanceof Error ? err.message : 'Unknown error');
		}
		finally
		{
			setLoading(false);
			setRefreshing(false);
		}
	};

	useEffect(() =>
	{
		fetchDatabaseStatus();

		// Auto-refresh every 30 seconds
		const interval = setInterval(() =>
		{
			fetchDatabaseStatus();
		}, 30000);

		return () => clearInterval(interval);
	}, []);

	const handleRefresh = () =>
	{
		if (selectedDatabase)
		{
			fetchDatabaseStatus(selectedDatabase);
		}
		else
		{
			fetchDatabaseStatus();
		}
	};

	const getStatusColor = (status: string) =>
	{
		switch (status)
		{
			case 'healthy': return 'green';
			case 'degraded': return 'yellow';
			case 'unhealthy': return 'red';
			case 'disconnected': return 'gray';
			default: return 'gray';
		}
	};

	const getStatusIcon = (status: string) =>
	{
		switch (status)
		{
			case 'healthy': return <IconCheck size={16} />;
			case 'degraded': return <IconAlertTriangle size={16} />;
			case 'unhealthy': return <IconAlertTriangle size={16} />;
			case 'disconnected': return <IconDatabase size={16} />;
			default: return <IconDatabase size={16} />;
		}
	};

	const formatBytes = (bytes: number) =>
	{
		const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
		if (bytes === 0) return '0 Bytes';
		const i = Math.floor(Math.log(bytes) / Math.log(1024));
		return `${Math.round(bytes / 1024 ** i * 100) / 100 } ${ sizes[i]}`;
	};

	const formatUptime = (seconds: number) =>
	{
		const days = Math.floor(seconds / 86400);
		const hours = Math.floor((seconds % 86400) / 3600);
		const minutes = Math.floor((seconds % 3600) / 60);

		if (days > 0) return `${days}d ${hours}h ${minutes}m`;
		if (hours > 0) return `${hours}h ${minutes}m`;
		return `${minutes}m`;
	};

	if (loading)
	{
		return (
			<Card>
				<Group justify="center" p="xl">
					<Loader size="lg" />
					<Text>Loading database status...</Text>
				</Group>
			</Card>
		);
	}

	if (error)
	{
		return (
			<Alert color="red" title="Error" icon={<IconAlertTriangle size={16} />}>
				{error}
				<Button variant="light" size="xs" mt="sm" onClick={handleRefresh}>
					Retry
				</Button>
			</Alert>
		);
	}

	const selectedDb = selectedDatabase ? databases.find(db => db.database === selectedDatabase) : null;

	return (
		<Stack gap="md">
			<Group justify="space-between">
				<Title order={2}>Database Status Dashboard</Title>
				<Group>
					<Select
						placeholder="Select database for details"
						data={databases.map(db => ({ value: db.database, label: db.database.toUpperCase() }))}
						value={selectedDatabase}
						onChange={value => setSelectedDatabase(value || '')}
						clearable
					/>
					<Tooltip label="Refresh status">
						<ActionIcon
							variant="light"
							onClick={handleRefresh}
							loading={refreshing}
						>
							<IconRefresh size={16} />
						</ActionIcon>
					</Tooltip>
				</Group>
			</Group>

			<Grid>
				{databases.map(db => (
					<Grid.Col key={db.database} span={{ base: 12, md: 6, lg: 3 }}>
						<Card>
							<Stack gap="sm">
								<Group justify="space-between">
									<Text fw={500}>{db.database.toUpperCase()}</Text>
									<Badge
										color={getStatusColor(db.status)}
										variant="light"
										leftSection={getStatusIcon(db.status)}
									>
										{db.status}
									</Badge>
								</Group>

								<Text size="sm" c="dimmed">
									{db.host}:{db.port}
								</Text>

								<Group justify="space-between">
									<Text size="sm">Response Time</Text>
									<Text size="sm" fw={500}>{db.responseTime}ms</Text>
								</Group>

								<Group justify="space-between">
									<Text size="sm">Uptime</Text>
									<Text size="sm" fw={500}>{formatUptime(db.uptime)}</Text>
								</Group>

								<Group justify="space-between">
									<Text size="sm">Version</Text>
									<Text size="sm" fw={500}>{db.version}</Text>
								</Group>
							</Stack>
						</Card>
					</Grid.Col>
				))}
			</Grid>

			{selectedDb && (
				<Card>
					<Title order={3} mb="md">{selectedDb.database.toUpperCase()} Performance Metrics</Title>

					<Grid>
						<Grid.Col span={{ base: 12, md: 6 }}>
							<Card withBorder>
								<Stack gap="sm">
									<Text fw={500}>Query Performance</Text>
									<Group justify="space-between">
										<Text size="sm">Average Response</Text>
										<Text size="sm" fw={500}>{selectedDb.metrics.queryResponseTime.average.toFixed(1)}ms</Text>
									</Group>
									<Group justify="space-between">
										<Text size="sm">95th Percentile</Text>
										<Text size="sm" fw={500}>{selectedDb.metrics.queryResponseTime.p95.toFixed(1)}ms</Text>
									</Group>
									<Group justify="space-between">
										<Text size="sm">99th Percentile</Text>
										<Text size="sm" fw={500}>{selectedDb.metrics.queryResponseTime.p99.toFixed(1)}ms</Text>
									</Group>
								</Stack>
							</Card>
						</Grid.Col>

						<Grid.Col span={{ base: 12, md: 6 }}>
							<Card withBorder>
								<Stack gap="sm">
									<Text fw={500}>Connection Pool</Text>
									<Group justify="space-between">
										<Text size="sm">Active</Text>
										<Text size="sm" fw={500}>{selectedDb.metrics.connectionPool.active}</Text>
									</Group>
									<Group justify="space-between">
										<Text size="sm">Idle</Text>
										<Text size="sm" fw={500}>{selectedDb.metrics.connectionPool.idle}</Text>
									</Group>
									<Progress
										value={(selectedDb.metrics.connectionPool.total / selectedDb.metrics.connectionPool.maxSize) * 100}
										label={`${selectedDb.metrics.connectionPool.total}/${selectedDb.metrics.connectionPool.maxSize}`}
										size="lg"
										radius="md"
									/>
								</Stack>
							</Card>
						</Grid.Col>

						<Grid.Col span={{ base: 12, md: 6 }}>
							<Card withBorder>
								<Stack gap="sm">
									<Text fw={500}>Storage Utilization</Text>
									<Group justify="space-between">
										<Text size="sm">Used</Text>
										<Text size="sm" fw={500}>{formatBytes(selectedDb.metrics.storageUtilization.used)}</Text>
									</Group>
									<Group justify="space-between">
										<Text size="sm">Total</Text>
										<Text size="sm" fw={500}>{formatBytes(selectedDb.metrics.storageUtilization.total)}</Text>
									</Group>
									<Progress
										value={selectedDb.metrics.storageUtilization.percentage}
										label={`${selectedDb.metrics.storageUtilization.percentage.toFixed(1)}%`}
										size="lg"
										radius="md"
										color={selectedDb.metrics.storageUtilization.percentage > 80 ? 'red' : 'blue'}
									/>
								</Stack>
							</Card>
						</Grid.Col>

						<Grid.Col span={{ base: 12, md: 6 }}>
							<Card withBorder>
								<Stack gap="sm">
									<Text fw={500}>Throughput</Text>
									<Group justify="space-between">
										<Text size="sm">Queries/sec</Text>
										<Text size="sm" fw={500}>{selectedDb.metrics.throughput.queriesPerSecond}</Text>
									</Group>
									<Group justify="space-between">
										<Text size="sm">Reads/sec</Text>
										<Text size="sm" fw={500}>{selectedDb.metrics.throughput.readsPerSecond}</Text>
									</Group>
									<Group justify="space-between">
										<Text size="sm">Writes/sec</Text>
										<Text size="sm" fw={500}>{selectedDb.metrics.throughput.writesPerSecond}</Text>
									</Group>
								</Stack>
							</Card>
						</Grid.Col>
					</Grid>
				</Card>
			)}
		</Stack>
	);
}

export default DatabaseStatusDashboard;
