'use client';

import {
	<PERSON>rid,
	Card,
	Text,
	Button,
	Select,
	Group,
	Stack,
	Title,
	Badge,
	Progress,
	Modal,
	TextInput,
	Textarea,
	Alert,
	Table,
	ActionIcon,
	Tooltip,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';
import {
	IconTool, IconPlayerPlay, IconCheck, IconX, IconAlertTriangle, IconRefresh,
} from '@tabler/icons-react';
import { useState, useEffect } from 'react';

import type { DatabaseMaintenanceTask, MaintenanceOperationType } from '@/types/database';

function DatabaseMaintenanceTools()
{
	const [tasks, setTasks] = useState<DatabaseMaintenanceTask[]>([]);
	const [loading, setLoading] = useState(false);
	const [selectedDatabase, setSelectedDatabase] = useState<string>('');
	const [createModalOpened, { open: openCreateModal, close: closeCreateModal }] = useDisclosure(false);
	const [newTask, setNewTask] = useState({
		database: '',
		operation: '' as MaintenanceOperationType,
		parameters: {} as Record<string, any>,
	});

	const databases = ['scylla', 'mariadb', 'redis', 'manticore'];
	const operations: { value: MaintenanceOperationType; label: string }[] = [
		{ value: 'cleanup', label: 'Database Cleanup' },
		{ value: 'optimize', label: 'Optimize Tables' },
		{ value: 'archive', label: 'Archive Old Data' },
		{ value: 'repair', label: 'Repair Tables' },
		{ value: 'backup', label: 'Create Backup' },
		{ value: 'restore', label: 'Restore from Backup' },
	];

	const fetchTasks = async () =>
	{
		try
		{
			setLoading(true);
			const url = selectedDatabase
				? `/api/database/maintenance?database=${selectedDatabase}`
				: '/api/database/maintenance';

			const response = await fetch(url);
			if (!response.ok) throw new Error('Failed to fetch maintenance tasks');

			const data = await response.json();
			setTasks(data);
		}
		catch (error)
		{
			notifications.show({
				title: 'Error',
				message: 'Failed to fetch maintenance tasks',
				color: 'red',
			});
		}
		finally
		{
			setLoading(false);
		}
	};

	useEffect(() =>
	{
		fetchTasks();
	}, [selectedDatabase]);

	const createMaintenanceTask = async () =>
	{
		if (!newTask.database || !newTask.operation)
		{
			notifications.show({
				title: 'Error',
				message: 'Please select database and operation',
				color: 'red',
			});
			return;
		}

		try
		{
			const response = await fetch('/api/database/maintenance', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(newTask),
			});

			if (!response.ok) throw new Error('Failed to create maintenance task');

			const result = await response.json();

			notifications.show({
				title: 'Success',
				message: `Maintenance task created: ${result.taskId}`,
				color: 'green',
			});

			closeCreateModal();
			setNewTask({ database: '', operation: '' as MaintenanceOperationType, parameters: {} });
			fetchTasks();
		}
		catch (error)
		{
			notifications.show({
				title: 'Error',
				message: 'Failed to create maintenance task',
				color: 'red',
			});
		}
	};

	const getStatusColor = (status: string) =>
	{
		switch (status)
		{
			case 'completed': return 'green';
			case 'running': return 'blue';
			case 'pending': return 'yellow';
			case 'failed': return 'red';
			default: return 'gray';
		}
	};

	const getStatusIcon = (status: string) =>
	{
		switch (status)
		{
			case 'completed': return <IconCheck size={16} />;
			case 'running': return <IconPlayerPlay size={16} />;
			case 'pending': return <IconTool size={16} />;
			case 'failed': return <IconX size={16} />;
			default: return <IconTool size={16} />;
		}
	};

	const formatDuration = (duration: number | null) =>
	{
		if (!duration) return 'N/A';
		const seconds = Math.floor(duration / 1000);
		const minutes = Math.floor(seconds / 60);
		const hours = Math.floor(minutes / 60);

		if (hours > 0) return `${hours}h ${minutes % 60}m`;
		if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
		return `${seconds}s`;
	};

	return (
		<Stack gap="md">
			<Group justify="space-between">
				<Title order={2}>Database Maintenance Tools</Title>
				<Group>
					<Select
						placeholder="Filter by database"
						data={databases.map(db => ({ value: db, label: db.toUpperCase() }))}
						value={selectedDatabase}
						onChange={value => setSelectedDatabase(value || '')}
						clearable
					/>
					<Button onClick={openCreateModal} leftSection={<IconTool size={16} />}>
						New Maintenance Task
					</Button>
					<Tooltip label="Refresh tasks">
						<ActionIcon variant="light" onClick={fetchTasks} loading={loading}>
							<IconRefresh size={16} />
						</ActionIcon>
					</Tooltip>
				</Group>
			</Group>

			<Grid>
				<Grid.Col span={12}>
					<Card>
						<Title order={3} mb="md">Active Maintenance Tasks</Title>

						{tasks.length === 0 ? (
							<Text c="dimmed" ta="center" py="xl">
								No maintenance tasks found
							</Text>
						) : (
							<Table>
								<Table.Thead>
									<Table.Tr>
										<Table.Th>Database</Table.Th>
										<Table.Th>Operation</Table.Th>
										<Table.Th>Status</Table.Th>
										<Table.Th>Progress</Table.Th>
										<Table.Th>Duration</Table.Th>
										<Table.Th>Started</Table.Th>
										<Table.Th>Safety Checks</Table.Th>
									</Table.Tr>
								</Table.Thead>
								<Table.Tbody>
									{tasks.map(task => (
										<Table.Tr key={task.id}>
											<Table.Td>
												<Text fw={500}>{task.database.toUpperCase()}</Text>
											</Table.Td>
											<Table.Td>
												<Text tt="capitalize">{task.operation.replace('_', ' ')}</Text>
											</Table.Td>
											<Table.Td>
												<Badge
													color={getStatusColor(task.status)}
													variant="light"
													leftSection={getStatusIcon(task.status)}
												>
													{task.status}
												</Badge>
											</Table.Td>
											<Table.Td>
												<Progress
													value={task.progress}
													size="sm"
													radius="md"
													label={`${task.progress}%`}
												/>
											</Table.Td>
											<Table.Td>
												<Text size="sm">{formatDuration(task.duration)}</Text>
											</Table.Td>
											<Table.Td>
												<Text size="sm">
													{task.startedAt ? new Date(task.startedAt).toLocaleString() : 'Not started'}
												</Text>
											</Table.Td>
											<Table.Td>
												<Group gap="xs">
													<Tooltip label="Backup created">
														<Badge
															size="xs"
															color={task.safetyChecks.backupCreated ? 'green' : 'red'}
															variant="dot"
														>
															Backup
														</Badge>
													</Tooltip>
													<Tooltip label="Resources available">
														<Badge
															size="xs"
															color={task.safetyChecks.resourcesAvailable ? 'green' : 'red'}
															variant="dot"
														>
															Resources
														</Badge>
													</Tooltip>
													<Tooltip label="No active transactions">
														<Badge
															size="xs"
															color={task.safetyChecks.noActiveTransactions ? 'green' : 'red'}
															variant="dot"
														>
															Transactions
														</Badge>
													</Tooltip>
												</Group>
											</Table.Td>
										</Table.Tr>
									))}
								</Table.Tbody>
							</Table>
						)}
					</Card>
				</Grid.Col>
			</Grid>

			<Modal
				opened={createModalOpened}
				onClose={closeCreateModal}
				title="Create Maintenance Task"
				size="md"
			>
				<Stack gap="md">
					<Select
						label="Database"
						placeholder="Select database"
						data={databases.map(db => ({ value: db, label: db.toUpperCase() }))}
						value={newTask.database}
						onChange={value => setNewTask(prev => ({ ...prev, database: value || '' }))}
						required
					/>

					<Select
						label="Operation"
						placeholder="Select maintenance operation"
						data={operations}
						value={newTask.operation}
						onChange={value => setNewTask(prev => ({ ...prev, operation: value as MaintenanceOperationType || '' }))}
						required
					/>

					{newTask.operation === 'cleanup' && (
						<Alert color="blue" icon={<IconAlertTriangle size={16} />}>
							This will clean up temporary files, expired data, and optimize storage.
						</Alert>
					)}

					{newTask.operation === 'optimize' && (
						<Alert color="blue" icon={<IconAlertTriangle size={16} />}>
							This will optimize table structures and rebuild indexes for better performance.
						</Alert>
					)}

					{newTask.operation === 'archive' && (
						<Alert color="yellow" icon={<IconAlertTriangle size={16} />}>
							This will move old data to archive storage. Specify retention period in parameters.
						</Alert>
					)}

					{newTask.operation === 'repair' && (
						<Alert color="orange" icon={<IconAlertTriangle size={16} />}>
							This will attempt to repair corrupted tables. A backup will be created first.
						</Alert>
					)}

					{newTask.operation === 'backup' && (
						<Alert color="green" icon={<IconAlertTriangle size={16} />}>
							This will create a full backup of the selected database.
						</Alert>
					)}

					{newTask.operation === 'restore' && (
						<Alert color="red" icon={<IconAlertTriangle size={16} />}>
							This will restore from a backup. Current data may be overwritten.
						</Alert>
					)}

					<Group justify="flex-end">
						<Button variant="light" onClick={closeCreateModal}>
							Cancel
						</Button>
						<Button onClick={createMaintenanceTask}>
							Create Task
						</Button>
					</Group>
				</Stack>
			</Modal>
		</Stack>
	);
}

export default DatabaseMaintenanceTools;
