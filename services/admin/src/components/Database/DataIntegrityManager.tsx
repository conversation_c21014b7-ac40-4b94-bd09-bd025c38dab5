'use client';

import {
	<PERSON>rid,
	Card,
	Text,
	Button,
	Select,
	Group,
	Stack,
	Title,
	Badge,
	Alert,
	Table,
	ActionIcon,
	Tooltip,
	Checkbox,
	Modal,
	List,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';
import {
	IconShield, IconAlertTriangle, IconCheck, IconX, IconRefresh, IconEye,
} from '@tabler/icons-react';
import { useState, useEffect } from 'react';

import type { DataIntegrityCheck } from '@/types/database';

function DataIntegrityManager()
{
	const [checks, setChecks] = useState<DataIntegrityCheck[]>([]);
	const [loading, setLoading] = useState(false);
	const [selectedDatabase, setSelectedDatabase] = useState<string>('');
	const [selectedCheckTypes, setSelectedCheckTypes] = useState<string[]>(['consistency', 'corruption']);
	const [detailsModalOpened, { open: openDetailsModal, close: closeDetailsModal }] = useDisclosure(false);
	const [selectedCheck, setSelectedCheck] = useState<DataIntegrityCheck | null>(null);

	const databases = ['scylla', 'mariadb', 'redis', 'manticore'];
	const checkTypes = [
		{ value: 'consistency', label: 'Data Consistency' },
		{ value: 'corruption', label: 'Corruption Detection' },
		{ value: 'foreign_keys', label: 'Foreign Key Integrity' },
		{ value: 'indexes', label: 'Index Integrity' },
		{ value: 'schema', label: 'Schema Validation' },
	];

	const fetchChecks = async () =>
	{
		try
		{
			setLoading(true);
			const url = selectedDatabase
				? `/api/database/integrity?database=${selectedDatabase}`
				: '/api/database/integrity';

			const response = await fetch(url);
			if (!response.ok) throw new Error('Failed to fetch integrity checks');

			const data = await response.json();
			setChecks(data);
		}
		catch (error)
		{
			notifications.show({
				title: 'Error',
				message: 'Failed to fetch integrity checks',
				color: 'red',
			});
		}
		finally
		{
			setLoading(false);
		}
	};

	useEffect(() =>
	{
		fetchChecks();
	}, [selectedDatabase]);

	const runIntegrityCheck = async () =>
	{
		if (!selectedDatabase || selectedCheckTypes.length === 0)
		{
			notifications.show({
				title: 'Error',
				message: 'Please select database and check types',
				color: 'red',
			});
			return;
		}

		try
		{
			const response = await fetch('/api/database/integrity', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					database: selectedDatabase,
					checkTypes: selectedCheckTypes,
				}),
			});

			if (!response.ok) throw new Error('Failed to start integrity check');

			const result = await response.json();

			notifications.show({
				title: 'Success',
				message: `Started ${result.checks.length} integrity checks`,
				color: 'green',
			});

			fetchChecks();
		}
		catch (error)
		{
			notifications.show({
				title: 'Error',
				message: 'Failed to start integrity check',
				color: 'red',
			});
		}
	};

	const viewCheckDetails = (check: DataIntegrityCheck) =>
	{
		setSelectedCheck(check);
		openDetailsModal();
	};

	const getStatusColor = (status: string) =>
	{
		switch (status)
		{
			case 'completed': return 'green';
			case 'running': return 'blue';
			case 'failed': return 'red';
			default: return 'gray';
		}
	};

	const getStatusIcon = (status: string) =>
	{
		switch (status)
		{
			case 'completed': return <IconCheck size={16} />;
			case 'running': return <IconRefresh size={16} />;
			case 'failed': return <IconX size={16} />;
			default: return <IconShield size={16} />;
		}
	};

	const getSeverityColor = (severity: string) =>
	{
		switch (severity)
		{
			case 'critical': return 'red';
			case 'high': return 'orange';
			case 'medium': return 'yellow';
			case 'low': return 'blue';
			default: return 'gray';
		}
	};

	return (
		<Stack gap="md">
			<Group justify="space-between">
				<Title order={2}>Data Integrity Manager</Title>
				<Group>
					<Select
						placeholder="Filter by database"
						data={databases.map(db => ({ value: db, label: db.toUpperCase() }))}
						value={selectedDatabase}
						onChange={value => setSelectedDatabase(value || '')}
						clearable
					/>
					<Tooltip label="Refresh checks">
						<ActionIcon variant="light" onClick={fetchChecks} loading={loading}>
							<IconRefresh size={16} />
						</ActionIcon>
					</Tooltip>
				</Group>
			</Group>

			<Grid>
				<Grid.Col span={{ base: 12, md: 4 }}>
					<Card>
						<Title order={3} mb="md">Run New Check</Title>

						<Stack gap="md">
							<Select
								label="Database"
								placeholder="Select database"
								data={databases.map(db => ({ value: db, label: db.toUpperCase() }))}
								value={selectedDatabase}
								onChange={value => setSelectedDatabase(value || '')}
								required
							/>

							<Text size="sm" fw={500}>Check Types</Text>
							<Stack gap="xs">
								{checkTypes.map(type => (
									<Checkbox
										key={type.value}
										label={type.label}
										checked={selectedCheckTypes.includes(type.value)}
										onChange={(event) =>
										{
											if (event.currentTarget.checked)
											{
												setSelectedCheckTypes(prev => [...prev, type.value]);
											}
											else
											{
												setSelectedCheckTypes(prev => prev.filter(t => t !== type.value));
											}
										}}
									/>
								))}
							</Stack>

							<Button
								onClick={runIntegrityCheck}
								leftSection={<IconShield size={16} />}
								disabled={!selectedDatabase || selectedCheckTypes.length === 0}
								fullWidth
							>
								Run Integrity Check
							</Button>
						</Stack>
					</Card>
				</Grid.Col>

				<Grid.Col span={{ base: 12, md: 8 }}>
					<Card>
						<Title order={3} mb="md">Recent Integrity Checks</Title>

						{checks.length === 0 ? (
							<Text c="dimmed" ta="center" py="xl">
								No integrity checks found
							</Text>
						) : (
							<Table>
								<Table.Thead>
									<Table.Tr>
										<Table.Th>Database</Table.Th>
										<Table.Th>Check Type</Table.Th>
										<Table.Th>Status</Table.Th>
										<Table.Th>Issues Found</Table.Th>
										<Table.Th>Critical Issues</Table.Th>
										<Table.Th>Started</Table.Th>
										<Table.Th>Actions</Table.Th>
									</Table.Tr>
								</Table.Thead>
								<Table.Tbody>
									{checks.map(check => (
										<Table.Tr key={check.id}>
											<Table.Td>
												<Text fw={500}>{check.database.toUpperCase()}</Text>
											</Table.Td>
											<Table.Td>
												<Text tt="capitalize">{check.checkType.replace('_', ' ')}</Text>
											</Table.Td>
											<Table.Td>
												<Badge
													color={getStatusColor(check.status)}
													variant="light"
													leftSection={getStatusIcon(check.status)}
												>
													{check.status}
												</Badge>
											</Table.Td>
											<Table.Td>
												<Text fw={500} c={check.summary.issuesFound > 0 ? 'red' : 'green'}>
													{check.summary.issuesFound}
												</Text>
											</Table.Td>
											<Table.Td>
												<Text fw={500} c={check.summary.criticalIssues > 0 ? 'red' : 'green'}>
													{check.summary.criticalIssues}
												</Text>
											</Table.Td>
											<Table.Td>
												<Text size="sm">
													{new Date(check.startedAt).toLocaleString()}
												</Text>
											</Table.Td>
											<Table.Td>
												<Tooltip label="View details">
													<ActionIcon
														variant="light"
														onClick={() => viewCheckDetails(check)}
													>
														<IconEye size={16} />
													</ActionIcon>
												</Tooltip>
											</Table.Td>
										</Table.Tr>
									))}
								</Table.Tbody>
							</Table>
						)}
					</Card>
				</Grid.Col>
			</Grid>

			<Modal
				opened={detailsModalOpened}
				onClose={closeDetailsModal}
				title={`Integrity Check Details - ${selectedCheck?.checkType.replace('_', ' ')}`}
				size="lg"
			>
				{selectedCheck && (
					<Stack gap="md">
						<Group>
							<Badge color={getStatusColor(selectedCheck.status)} variant="light">
								{selectedCheck.status}
							</Badge>
							<Text size="sm" c="dimmed">
								{selectedCheck.database.toUpperCase()} • {new Date(selectedCheck.startedAt).toLocaleString()}
							</Text>
						</Group>

						<Grid>
							<Grid.Col span={6}>
								<Text size="sm" c="dimmed">Total Checks</Text>
								<Text fw={500}>{selectedCheck.summary.totalChecks}</Text>
							</Grid.Col>
							<Grid.Col span={6}>
								<Text size="sm" c="dimmed">Issues Found</Text>
								<Text fw={500} c={selectedCheck.summary.issuesFound > 0 ? 'red' : 'green'}>
									{selectedCheck.summary.issuesFound}
								</Text>
							</Grid.Col>
							<Grid.Col span={6}>
								<Text size="sm" c="dimmed">Critical Issues</Text>
								<Text fw={500} c={selectedCheck.summary.criticalIssues > 0 ? 'red' : 'green'}>
									{selectedCheck.summary.criticalIssues}
								</Text>
							</Grid.Col>
							<Grid.Col span={6}>
								<Text size="sm" c="dimmed">Repairable Issues</Text>
								<Text fw={500}>{selectedCheck.summary.repairableIssues}</Text>
							</Grid.Col>
						</Grid>

						{selectedCheck.issues.length > 0 && (
							<>
								<Title order={4}>Issues Found</Title>
								<Stack gap="sm">
									{selectedCheck.issues.map((issue, index) => (
										<Alert
											key={index}
											color={getSeverityColor(issue.severity)}
											icon={<IconAlertTriangle size={16} />}
										>
											<Stack gap="xs">
												<Group justify="space-between">
													<Text fw={500}>{issue.description}</Text>
													<Badge color={getSeverityColor(issue.severity)} size="sm">
														{issue.severity}
													</Badge>
												</Group>
												<Text size="sm">
													Table: {issue.table} • Affected rows: {issue.affectedRows}
												</Text>
												<Text size="sm" c="dimmed">
													Recommendation: {issue.recommendation}
												</Text>
											</Stack>
										</Alert>
									))}
								</Stack>
							</>
						)}

						{selectedCheck.issues.length === 0 && selectedCheck.status === 'completed' && (
							<Alert color="green" icon={<IconCheck size={16} />}>
								No integrity issues found. Database is healthy.
							</Alert>
						)}
					</Stack>
				)}
			</Modal>
		</Stack>
	);
}

export default DataIntegrityManager;
