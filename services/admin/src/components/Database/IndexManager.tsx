'use client';

import {
	<PERSON>rid,
	Card,
	Text,
	Button,
	Select,
	Group,
	Stack,
	Title,
	Badge,
	Table,
	ActionIcon,
	Tooltip,
	Modal,
	TextInput,
	MultiSelect,
	Alert,
	Progress,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';
import {
	// IconDatabase,
	IconPlus,
	IconTrash,
	IconRefresh,
	IconTool,
	// IconChartLine,
} from '@tabler/icons-react';
import { useState, useEffect } from 'react';

import type { DatabaseIndex } from '@/types/database';

function IndexManager()
{
	const [indexes, setIndexes] = useState<DatabaseIndex[]>([]);
	const [loading, setLoading] = useState(false);
	const [selectedDatabase, setSelectedDatabase] = useState<string>('');
	const [selectedTable, setSelectedTable] = useState<string>('');
	const [createModalOpened, { open: openCreateModal, close: closeCreateModal }] = useDisclosure(false);
	const [newIndex, setNewIndex] = useState({
		name: '',
		table: '',
		columns: [] as string[],
		type: 'index' as 'index' | 'unique' | 'primary' | 'fulltext',
	});

	const databases = ['scylla', 'mariadb', 'redis', 'manticore'];
	const tables = ['domain_analysis', 'domain_rankings', 'domain_crawl_jobs'];
	const columns = ['id', 'domain', 'category', 'global_rank', 'created_at', 'updated_at', 'status'];

	const fetchIndexes = async () =>
	{
		try
		{
			setLoading(true);
			const params = new URLSearchParams();
			if (selectedTable) params.append('table', selectedTable);

			const response = await fetch(`/api/database/indexes?${params}`);
			if (!response.ok) throw new Error('Failed to fetch indexes');

			const data = await response.json();
			setIndexes(data);
		}
		catch (error)
		{
			notifications.show({
				title: 'Error',
				message: 'Failed to fetch indexes',
				color: 'red',
			});
		}
		finally
		{
			setLoading(false);
		}
	};

	useEffect(() =>
	{
		fetchIndexes();
	}, [selectedTable]);

	const createIndex = async () =>
	{
		if (
			!selectedDatabase ||
			!newIndex.name ||
			!newIndex.table ||
			newIndex.columns.length === 0
		)
		{
			notifications.show({
				title: 'Error',
				message: 'Please fill in all required fields',
				color: 'red',
			});
			return;
		}

		try
		{
			const response = await fetch('/api/database/indexes', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					action: 'create',
					database: selectedDatabase,
					...newIndex,
				}),
			});

			if (!response.ok) throw new Error('Failed to create index');

			const result = await response.json();

			notifications.show({
				title: 'Success',
				message: `Index ${newIndex.name} created successfully`,
				color: 'green',
			});

			closeCreateModal();
			setNewIndex({
				name: '', table: '', columns: [], type: 'index',
			});
			fetchIndexes();
		}
		catch (error)
		{
			notifications.show({
				title: 'Error',
				message: 'Failed to create index',
				color: 'red',
			});
		}
	};

	const deleteIndex = async (indexName: string, table: string) =>
	{
		if (!selectedDatabase)
		{
			notifications.show({
				title: 'Error',
				message: 'Please select a database',
				color: 'red',
			});
			return;
		}

		try
		{
			const response = await fetch(`/api/database/indexes?database=${selectedDatabase}&table=${table}&indexName=${indexName}`, {
				method: 'DELETE',
			});

			if (!response.ok) throw new Error('Failed to delete index');

			notifications.show({
				title: 'Success',
				message: `Index ${indexName} deleted successfully`,
				color: 'green',
			});

			fetchIndexes();
		}
		catch
		{
			notifications.show({
				title: 'Error',
				message: 'Failed to delete index',
				color: 'red',
			});
		}
	};

	const optimizeIndexes = async () =>
	{
		if (!selectedDatabase || !selectedTable)
		{
			notifications.show({
				title: 'Error',
				message: 'Please select database and table',
				color: 'red',
			});
			return;
		}

		try
		{
			const response = await fetch('/api/database/indexes', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					action: 'optimize',
					database: selectedDatabase,
					table: selectedTable,
				}),
			});

			if (!response.ok) throw new Error('Failed to optimize indexes');

			const result = await response.json();

			notifications.show({
				title: 'Success',
				message: `Optimized ${result.optimized} indexes`,
				color: 'green',
			});

			fetchIndexes();
		}
		catch (error)
		{
			notifications.show({
				title: 'Error',
				message: 'Failed to optimize indexes',
				color: 'red',
			});
		}
	};

	const formatBytes = (bytes: number) =>
	{
		const sizes = ['Bytes', 'KB', 'MB', 'GB'];
		if (bytes === 0) return '0 Bytes';
		const i = Math.floor(Math.log(bytes) / Math.log(1024));
		return `${Math.round(bytes / 1024 ** i * 100) / 100 } ${ sizes[i]}`;
	};

	const getEfficiencyColor = (efficiency: number) =>
	{
		if (efficiency >= 0.8) return 'green';
		if (efficiency >= 0.6) return 'yellow';
		return 'red';
	};

	return (
		<Stack gap="md">
			<Group justify="space-between">
				<Title order={2}>Index Management</Title>
				<Group>
					<Select
						placeholder="Filter by table"
						data={tables.map(table => ({ value: table, label: table }))}
						value={selectedTable}
						onChange={value => setSelectedTable(value || '')}
						clearable
					/>
					<Button
						onClick={optimizeIndexes}
						leftSection={<IconTool size={16} />}
						disabled={!selectedDatabase || !selectedTable}
					>
						Optimize Indexes
					</Button>
					<Button onClick={openCreateModal} leftSection={<IconPlus size={16} />}>
						Create Index
					</Button>
					<Tooltip label="Refresh indexes">
						<ActionIcon variant="light" onClick={fetchIndexes} loading={loading}>
							<IconRefresh size={16} />
						</ActionIcon>
					</Tooltip>
				</Group>
			</Group>

			<Card>
				<Title order={3} mb="md">Database Indexes</Title>

				{indexes.length === 0 ? (
					<Text c="dimmed" ta="center" py="xl">
						No indexes found
					</Text>
				) : (
					<Table>
						<Table.Thead>
							<Table.Tr>
								<Table.Th>Name</Table.Th>
								<Table.Th>Table</Table.Th>
								<Table.Th>Columns</Table.Th>
								<Table.Th>Type</Table.Th>
								<Table.Th>Size</Table.Th>
								<Table.Th>Efficiency</Table.Th>
								<Table.Th>Usage</Table.Th>
								<Table.Th>Actions</Table.Th>
							</Table.Tr>
						</Table.Thead>
						<Table.Tbody>
							{indexes.map(index => (
								<Table.Tr key={`${index.table}.${index.name}`}>
									<Table.Td>
										<Text fw={500}>{index.name}</Text>
									</Table.Td>
									<Table.Td>
										<Text>{index.table}</Text>
									</Table.Td>
									<Table.Td>
										<Text size="sm">{index.columns.join(', ')}</Text>
									</Table.Td>
									<Table.Td>
										<Badge variant="light" tt="capitalize">
											{index.type}
										</Badge>
									</Table.Td>
									<Table.Td>
										<Text size="sm">{formatBytes(index.size)}</Text>
									</Table.Td>
									<Table.Td>
										<Group gap="xs">
											<Progress
												value={index.efficiency * 100}
												size="sm"
												color={getEfficiencyColor(index.efficiency)}
												w={60}
											/>
											<Text size="sm">{(index.efficiency * 100).toFixed(1)}%</Text>
										</Group>
									</Table.Td>
									<Table.Td>
										<Stack gap="xs">
											<Text size="xs">Reads: {index.usage.reads}</Text>
											<Text size="xs">Writes: {index.usage.writes}</Text>
										</Stack>
									</Table.Td>
									<Table.Td>
										<Group gap="xs">
											{index.type !== 'primary' && (
												<Tooltip label="Delete index">
													<ActionIcon
														variant="light"
														color="red"
														onClick={() => deleteIndex(index.name, index.table)}
													>
														<IconTrash size={16} />
													</ActionIcon>
												</Tooltip>
											)}
										</Group>
									</Table.Td>
								</Table.Tr>
							))}
						</Table.Tbody>
					</Table>
				)}
			</Card>

			<Modal
				opened={createModalOpened}
				onClose={closeCreateModal}
				title="Create Database Index"
				size="md"
			>
				<Stack gap="md">
					<Select
						label="Database"
						placeholder="Select database"
						data={databases.map(db => ({ value: db, label: db.toUpperCase() }))}
						value={selectedDatabase}
						onChange={value => setSelectedDatabase(value || '')}
						required
					/>

					<TextInput
						label="Index Name"
						placeholder="Enter index name"
						value={newIndex.name}
						onChange={event => setNewIndex(prev => ({ ...prev, name: event.currentTarget.value }))}
						required
					/>

					<Select
						label="Table"
						placeholder="Select table"
						data={tables.map(table => ({ value: table, label: table }))}
						value={newIndex.table}
						onChange={value => setNewIndex(prev => ({ ...prev, table: value || '' }))}
						required
					/>

					<MultiSelect
						label="Columns"
						placeholder="Select columns"
						data={columns.map(col => ({ value: col, label: col }))}
						value={newIndex.columns}
						onChange={value => setNewIndex(prev => ({ ...prev, columns: value }))}
						required
					/>

					<Select
						label="Index Type"
						data={[
							{ value: 'index', label: 'Regular Index' },
							{ value: 'unique', label: 'Unique Index' },
							{ value: 'fulltext', label: 'Full-text Index' },
						]}
						value={newIndex.type}
						onChange={value => setNewIndex(prev => ({ ...prev, type: value as any || 'index' }))}
					/>

					<Alert color="blue">
						Creating an index will improve query performance but may slow down write operations.
					</Alert>

					<Group justify="flex-end">
						<Button variant="light" onClick={closeCreateModal}>
							Cancel
						</Button>
						<Button onClick={createIndex}>
							Create Index
						</Button>
					</Group>
				</Stack>
			</Modal>
		</Stack>
	);
}

export default IndexManager;
