'use client';

import {
	<PERSON>rid,
	Card,
	Text,
	Button,
	Select,
	Group,
	Stack,
	Title,
	Badge,
	Table,
	ActionIcon,
	Tooltip,
	Modal,
	NumberInput,
	Checkbox,
	Alert,
	Progress,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { clientLogger } from '@/lib/logger';
import { notifications } from '@mantine/notifications';
import {
	IconDatabase,
	IconDownload,
	IconTrash,
	IconRefresh,
	IconRestore,
} from '@tabler/icons-react';
import { useState, useEffect } from 'react';

import type { BackupInfo, RestoreOperation } from '@/types/database';

function BackupManager()
{
	const [backups, setBackups] = useState<BackupInfo[]>([]);
	const [restoreOps, setRestoreOps] = useState<RestoreOperation[]>([]);
	const [loading, setLoading] = useState(false);
	const [selectedDatabase, setSelectedDatabase] = useState<string>('');
	const [backupModalOpened, { open: openBackupModal, close: closeBackupModal }] = useDisclosure(false);
	const [restoreModalOpened, { open: openRestoreModal, close: closeRestoreModal }] = useDisclosure(false);
	const [selectedBackup, setSelectedBackup] = useState<BackupInfo | null>(null);
	const [newBackup, setNewBackup] = useState({
		database: '',
		type: 'full' as 'full' | 'incremental' | 'differential',
		retentionDays: 30,
		maxBackups: 10,
		compression: true,
		encryption: false,
	});

	const databases = ['scylla', 'mariadb', 'redis', 'manticore'];

	const fetchBackups = async () =>
	{
		try
		{
			setLoading(true);
			const url = selectedDatabase
				? `/api/database/backups?database=${selectedDatabase}`
				: '/api/database/backups';

			const response = await fetch(url);
			if (!response.ok) throw new Error('Failed to fetch backups');

			const data = await response.json();
			setBackups(data);
		}
		catch (error)
		{
			notifications.show({
				title: 'Error',
				message: 'Failed to fetch backups',
				color: 'red',
			});
		}
		finally
		{
			setLoading(false);
		}
	};

	const fetchRestoreOperations = async () =>
	{
		try
		{
			const url = selectedDatabase
				? `/api/database/restore?database=${selectedDatabase}`
				: '/api/database/restore';

			const response = await fetch(url);
			if (!response.ok) throw new Error('Failed to fetch restore operations');

			const data = await response.json();
			setRestoreOps(data);
		}
		catch (error)
		{
			clientLogger.error('Failed to fetch restore operations:', error);
		}
	};

	useEffect(() =>
	{
		fetchBackups();
		fetchRestoreOperations();
	}, [selectedDatabase]);

	const createBackup = async () =>
	{
		if (!newBackup.database)
		{
			notifications.show({
				title: 'Error',
				message: 'Please select a database',
				color: 'red',
			});
			return;
		}

		try
		{
			const response = await fetch('/api/database/backups', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(newBackup),
			});

			if (!response.ok) throw new Error('Failed to create backup');

			const result = await response.json();

			notifications.show({
				title: 'Success',
				message: `Backup started: ${result.backupId}`,
				color: 'green',
			});

			closeBackupModal();
			setNewBackup({
				database: '',
				type: 'full',
				retentionDays: 30,
				maxBackups: 10,
				compression: true,
				encryption: false,
			});
			fetchBackups();
		}
		catch (error)
		{
			notifications.show({
				title: 'Error',
				message: 'Failed to create backup',
				color: 'red',
			});
		}
	};

	const deleteBackup = async (backupId: string) =>
	{
		try
		{
			const response = await fetch(`/api/database/backups?backupId=${backupId}`, {
				method: 'DELETE',
			});

			if (!response.ok) throw new Error('Failed to delete backup');

			notifications.show({
				title: 'Success',
				message: 'Backup deleted successfully',
				color: 'green',
			});

			fetchBackups();
		}
		catch (error)
		{
			notifications.show({
				title: 'Error',
				message: 'Failed to delete backup',
				color: 'red',
			});
		}
	};

	const startRestore = (backup: BackupInfo) =>
	{
		setSelectedBackup(backup);
		openRestoreModal();
	};

	const performRestore = async () =>
	{
		if (!selectedBackup)
		{
			return;
		}

		try
		{
			const response = await fetch('/api/database/restore', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					backupId: selectedBackup.id,
					database: selectedBackup.database,
					createPreRestoreBackup: true,
				}),
			});

			if (!response.ok) throw new Error('Failed to start restore');

			const result = await response.json();

			notifications.show({
				title: 'Success',
				message: `Restore started: ${result.restoreId}`,
				color: 'green',
			});

			closeRestoreModal();
			setSelectedBackup(null);
			fetchRestoreOperations();
		}
		catch (error)
		{
			notifications.show({
				title: 'Error',
				message: 'Failed to start restore',
				color: 'red',
			});
		}
	};

	const getStatusColor = (status: string) =>
	{
		switch (status)
		{
			case 'completed': return 'green';
			case 'running': return 'blue';
			case 'failed': return 'red';
			default: return 'gray';
		}
	};

	const formatBytes = (bytes: number) =>
	{
		const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
		if (bytes === 0) return '0 Bytes';
		const i = Math.floor(Math.log(bytes) / Math.log(1024));
		return `${Math.round(bytes / 1024 ** i * 100) / 100 } ${ sizes[i]}`;
	};

	return (
		<Stack gap="md">
			<Group justify="space-between">
				<Title order={2}>Backup & Restore Manager</Title>
				<Group>
					<Select
						placeholder="Filter by database"
						data={databases.map(db => ({ value: db, label: db.toUpperCase() }))}
						value={selectedDatabase}
						onChange={value => setSelectedDatabase(value || '')}
						clearable
					/>
					<Button onClick={openBackupModal} leftSection={<IconDatabase size={16} />}>
						Create Backup
					</Button>
					<Tooltip label="Refresh">
						<ActionIcon variant="light" onClick={fetchBackups} loading={loading}>
							<IconRefresh size={16} />
						</ActionIcon>
					</Tooltip>
				</Group>
			</Group>

			<Grid>
				<Grid.Col span={{ base: 12, lg: 8 }}>
					<Card>
						<Title order={3} mb="md">Available Backups</Title>

						{backups.length === 0 ? (
							<Text c="dimmed" ta="center" py="xl">
								No backups found
							</Text>
						) : (
							<Table>
								<Table.Thead>
									<Table.Tr>
										<Table.Th>Database</Table.Th>
										<Table.Th>Type</Table.Th>
										<Table.Th>Status</Table.Th>
										<Table.Th>Size</Table.Th>
										<Table.Th>Created</Table.Th>
										<Table.Th>Retention</Table.Th>
										<Table.Th>Actions</Table.Th>
									</Table.Tr>
								</Table.Thead>
								<Table.Tbody>
									{backups.map(backup => (
										<Table.Tr key={backup.id}>
											<Table.Td>
												<Text fw={500}>{backup.database.toUpperCase()}</Text>
											</Table.Td>
											<Table.Td>
												<Badge variant="light" tt="capitalize">
													{backup.type}
												</Badge>
											</Table.Td>
											<Table.Td>
												<Badge color={getStatusColor(backup.status)} variant="light">
													{backup.status}
												</Badge>
											</Table.Td>
											<Table.Td>
												<Text size="sm">{formatBytes(backup.size)}</Text>
											</Table.Td>
											<Table.Td>
												<Text size="sm">
													{new Date(backup.createdAt).toLocaleString()}
												</Text>
											</Table.Td>
											<Table.Td>
												<Text size="sm">{backup.retentionPolicy.keepDays} days</Text>
											</Table.Td>
											<Table.Td>
												<Group gap="xs">
													<Tooltip label="Restore from backup">
														<ActionIcon
															variant="light"
															color="blue"
															onClick={() => startRestore(backup)}
															disabled={backup.status !== 'completed'}
														>
															<IconRestore size={16} />
														</ActionIcon>
													</Tooltip>
													<Tooltip label="Delete backup">
														<ActionIcon
															variant="light"
															color="red"
															onClick={() => deleteBackup(backup.id)}
														>
															<IconTrash size={16} />
														</ActionIcon>
													</Tooltip>
												</Group>
											</Table.Td>
										</Table.Tr>
									))}
								</Table.Tbody>
							</Table>
						)}
					</Card>
				</Grid.Col>

				<Grid.Col span={{ base: 12, lg: 4 }}>
					<Card>
						<Title order={3} mb="md">Recent Restore Operations</Title>

						{restoreOps.length === 0 ? (
							<Text c="dimmed" ta="center" py="md">
								No restore operations
							</Text>
						) : (
							<Stack gap="sm">
								{restoreOps.slice(0, 5).map(restore => (
									<Card key={restore.id} withBorder>
										<Stack gap="xs">
											<Group justify="space-between">
												<Text size="sm" fw={500}>{restore.database.toUpperCase()}</Text>
												<Badge color={getStatusColor(restore.status)} size="sm">
													{restore.status}
												</Badge>
											</Group>
											{restore.status === 'running' && (
												<Progress value={restore.progress} size="sm" />
											)}
											<Text size="xs" c="dimmed">
												{new Date(restore.startedAt).toLocaleString()}
											</Text>
										</Stack>
									</Card>
								))}
							</Stack>
						)}
					</Card>
				</Grid.Col>
			</Grid>

			<Modal
				opened={backupModalOpened}
				onClose={closeBackupModal}
				title="Create Database Backup"
				size="md"
			>
				<Stack gap="md">
					<Select
						label="Database"
						placeholder="Select database"
						data={databases.map(db => ({ value: db, label: db.toUpperCase() }))}
						value={newBackup.database}
						onChange={value => setNewBackup(prev => ({ ...prev, database: value || '' }))}
						required
					/>

					<Select
						label="Backup Type"
						data={[
							{ value: 'full', label: 'Full Backup' },
							{ value: 'incremental', label: 'Incremental Backup' },
							{ value: 'differential', label: 'Differential Backup' },
						]}
						value={newBackup.type}
						onChange={value => setNewBackup(prev => ({ ...prev, type: value as any || 'full' }))}
					/>

					<NumberInput
						label="Retention Period (days)"
						value={newBackup.retentionDays}
						onChange={value => setNewBackup(prev => ({ ...prev, retentionDays: Number(value) || 30 }))}
						min={1}
						max={365}
					/>

					<NumberInput
						label="Maximum Backups to Keep"
						value={newBackup.maxBackups}
						onChange={value => setNewBackup(prev => ({ ...prev, maxBackups: Number(value) || 10 }))}
						min={1}
						max={100}
					/>

					<Checkbox
						label="Enable Compression"
						checked={newBackup.compression}
						onChange={event => setNewBackup(prev => ({ ...prev, compression: event.currentTarget.checked }))}
					/>

					<Checkbox
						label="Enable Encryption"
						checked={newBackup.encryption}
						onChange={event => setNewBackup(prev => ({ ...prev, encryption: event.currentTarget.checked }))}
					/>

					<Group justify="flex-end">
						<Button variant="light" onClick={closeBackupModal}>
							Cancel
						</Button>
						<Button onClick={createBackup}>
							Create Backup
						</Button>
					</Group>
				</Stack>
			</Modal>

			<Modal
				opened={restoreModalOpened}
				onClose={closeRestoreModal}
				title="Restore from Backup"
				size="md"
			>
				{selectedBackup && (
					<Stack gap="md">
						<Alert color="orange" title="Warning">
							This operation will restore data from the selected backup. Current data may be overwritten.
							A pre-restore backup will be created automatically.
						</Alert>

						<Card withBorder>
							<Stack gap="sm">
								<Group justify="space-between">
									<Text fw={500}>Backup Details</Text>
									<Badge color={getStatusColor(selectedBackup.status)}>
										{selectedBackup.status}
									</Badge>
								</Group>
								<Text size="sm">Database: {selectedBackup.database.toUpperCase()}</Text>
								<Text size="sm">Type: {selectedBackup.type}</Text>
								<Text size="sm">Size: {formatBytes(selectedBackup.size)}</Text>
								<Text size="sm">Created: {new Date(selectedBackup.createdAt).toLocaleString()}</Text>
							</Stack>
						</Card>

						<Group justify="flex-end">
							<Button variant="light" onClick={closeRestoreModal}>
								Cancel
							</Button>
							<Button color="orange" onClick={performRestore}>
								Start Restore
							</Button>
						</Group>
					</Stack>
				)}
			</Modal>
		</Stack>
	);
}

export default BackupManager;
