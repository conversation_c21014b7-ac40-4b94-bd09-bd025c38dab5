'use client';

import {
	<PERSON><PERSON>,
	Card,
	Text,
	// Button,
	Select,
	Group,
	Stack,
	Title,
	Badge,
	Progress,
	Alert,
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import {
	IconChartLine, IconTrendingUp, IconDatabase, IconCpu,
} from '@tabler/icons-react';
import { useState, useEffect } from 'react';
import {
	LineChart,
	Line,
	XAxis,
	YAxis,
	CartesianGrid,
	Tooltip,
	ResponsiveContainer,
	BarChart,
	Bar,
} from 'recharts';

import type { CapacityPlan } from '@/types/database';

function CapacityPlanner()
{
	const [capacityPlan, setCapacityPlan] = useState<CapacityPlan | null>(null);
	const [, setLoading] = useState(false);
	const [selectedDatabase, setSelectedDatabase] = useState<string>('mariadb');

	const databases = ['scylla', 'mariadb', 'redis', 'manticore'];

	const fetchCapacityPlan = async () =>
	{
		if (!selectedDatabase) return;

		try
		{
			setLoading(true);
			const response = await fetch(`/api/database/capacity?database=${selectedDatabase}`);
			if (!response.ok) throw new Error('Failed to fetch capacity plan');

			const data = await response.json();
			setCapacityPlan(data);
		}
		catch (error)
		{
			notifications.show({
				title: 'Error',
				message: 'Failed to fetch capacity plan',
				color: 'red',
			});
		}
		finally
		{
			setLoading(false);
		}
	};

	useEffect(() =>
	{
		fetchCapacityPlan();
	}, [selectedDatabase]);

	const formatBytes = (bytes: number) =>
	{
		const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
		if (bytes === 0) return '0 Bytes';
		const i = Math.floor(Math.log(bytes) / Math.log(1024));
		return `${Math.round(bytes / 1024 ** i * 100) / 100 } ${ sizes[i]}`;
	};

	const getPriorityColor = (priority: string) =>
	{
		switch (priority)
		{
			case 'high': return 'red';
			case 'medium': return 'yellow';
			case 'low': return 'blue';
			default: return 'gray';
		}
	};

	const getTypeIcon = (type: string) =>
	{
		switch (type)
		{
			case 'scale_up': return <IconTrendingUp size={16} />;
			case 'scale_out': return <IconDatabase size={16} />;
			case 'optimize': return <IconCpu size={16} />;
			default: return <IconChartLine size={16} />;
		}
	};

	if (!capacityPlan)
	{
		return (
			<Stack gap="md">
				<Group justify="space-between">
					<Title order={2}>Capacity Planning</Title>
					<Select
						placeholder="Select database"
						data={databases.map(db => ({ value: db, label: db.toUpperCase() }))}
						value={selectedDatabase}
						onChange={value => setSelectedDatabase(value || '')}
					/>
				</Group>
				<Card>
					<Text c="dimmed" ta="center" py="xl">
						Select a database to view capacity planning
					</Text>
				</Card>
			</Stack>
		);
	}

	const projectionData = capacityPlan.projections.map(p => ({
		timeframe: p.timeframe,
		storage: p.storage / (1024 * 1024 * 1024), // Convert to GB
		connections: p.connections,
		cpu: p.cpu,
		memory: p.memory,
	}));

	return (
		<Stack gap="md">
			<Group justify="space-between">
				<Title order={2}>Capacity Planning - {selectedDatabase.toUpperCase()}</Title>
				<Select
					data={databases.map(db => ({ value: db, label: db.toUpperCase() }))}
					value={selectedDatabase}
					onChange={value => setSelectedDatabase(value || '')}
				/>
			</Group>

			<Grid>
				<Grid.Col span={{ base: 12, md: 6 }}>
					<Card>
						<Title order={3} mb="md">Current Usage</Title>
						<Stack gap="md">
							<div>
								<Group justify="space-between" mb="xs">
									<Text size="sm">Storage</Text>
									<Text size="sm" fw={500}>
										{formatBytes(capacityPlan.currentUsage.storage)}
									</Text>
								</Group>
								<Progress value={75} color="blue" />
							</div>

							<div>
								<Group justify="space-between" mb="xs">
									<Text size="sm">Connections</Text>
									<Text size="sm" fw={500}>
										{capacityPlan.currentUsage.connections}
									</Text>
								</Group>
								<Progress value={60} color="green" />
							</div>

							<div>
								<Group justify="space-between" mb="xs">
									<Text size="sm">CPU Usage</Text>
									<Text size="sm" fw={500}>
										{capacityPlan.currentUsage.cpu.toFixed(1)}%
									</Text>
								</Group>
								<Progress
									value={capacityPlan.currentUsage.cpu}
									color={capacityPlan.currentUsage.cpu > 80 ? 'red' : 'blue'}
								/>
							</div>

							<div>
								<Group justify="space-between" mb="xs">
									<Text size="sm">Memory Usage</Text>
									<Text size="sm" fw={500}>
										{capacityPlan.currentUsage.memory.toFixed(1)}%
									</Text>
								</Group>
								<Progress
									value={capacityPlan.currentUsage.memory}
									color={capacityPlan.currentUsage.memory > 85 ? 'red' : 'blue'}
								/>
							</div>
						</Stack>
					</Card>
				</Grid.Col>

				<Grid.Col span={{ base: 12, md: 6 }}>
					<Card>
						<Title order={3} mb="md">Growth Projections</Title>
						<ResponsiveContainer width="100%" height={300}>
							<LineChart data={projectionData}>
								<CartesianGrid strokeDasharray="3 3" />
								<XAxis dataKey="timeframe" />
								<YAxis />
								<Tooltip />
								<Line type="monotone" dataKey="storage" stroke="#8884d8" name="Storage (GB)" />
								<Line type="monotone" dataKey="connections" stroke="#82ca9d" name="Connections" />
							</LineChart>
						</ResponsiveContainer>
					</Card>
				</Grid.Col>
			</Grid>

			<Card>
				<Title order={3} mb="md">Capacity Recommendations</Title>

				{capacityPlan.recommendations.length === 0 ? (
					<Alert color="green">
						No immediate capacity concerns. Current resources are sufficient for projected growth.
					</Alert>
				) : (
					<Stack gap="md">
						{capacityPlan.recommendations.map((rec, index) => (
							<Alert
								key={index.toString()}
								color={getPriorityColor(rec.priority)}
								icon={getTypeIcon(rec.type)}
							>
								<Group justify="space-between" align="flex-start">
									<Stack gap="xs" style={{ flex: 1 }}>
										<Group>
											<Badge color={getPriorityColor(rec.priority)} size="sm">
												{rec.priority.toUpperCase()}
											</Badge>
											<Badge variant="light" size="sm" tt="capitalize">
												{rec.type.replace('_', ' ')}
											</Badge>
										</Group>
										<Text>{rec.description}</Text>
										<Group>
											<Text size="sm" c="dimmed">
												Timeline: {rec.timeline}
											</Text>
											<Text size="sm" c="dimmed">
												Est. Cost: ${rec.estimatedCost}/month
											</Text>
										</Group>
									</Stack>
								</Group>
							</Alert>
						))}
					</Stack>
				)}
			</Card>

			<Grid>
				<Grid.Col span={{ base: 12, md: 6 }}>
					<Card>
						<Title order={3} mb="md">Resource Utilization Trends</Title>
						<ResponsiveContainer width="100%" height={250}>
							<BarChart data={projectionData}>
								<CartesianGrid strokeDasharray="3 3" />
								<XAxis dataKey="timeframe" />
								<YAxis />
								<Tooltip />
								<Bar dataKey="cpu" fill="#8884d8" name="CPU %" />
								<Bar dataKey="memory" fill="#82ca9d" name="Memory %" />
							</BarChart>
						</ResponsiveContainer>
					</Card>
				</Grid.Col>

				<Grid.Col span={{ base: 12, md: 6 }}>
					<Card>
						<Title order={3} mb="md">Capacity Summary</Title>
						<Stack gap="md">
							<Group justify="space-between">
								<Text>Total Recommendations</Text>
								<Badge>{capacityPlan.recommendations.length}</Badge>
							</Group>

							<Group justify="space-between">
								<Text>High Priority Items</Text>
								<Badge color="red">
									{capacityPlan.recommendations.filter(r => r.priority === 'high').length}
								</Badge>
							</Group>

							<Group justify="space-between">
								<Text>Estimated Monthly Cost Impact</Text>
								<Text fw={500}>
									${capacityPlan.recommendations.reduce((sum, r) => sum + r.estimatedCost, 0)}
								</Text>
							</Group>

							<Group justify="space-between">
								<Text>Recommended Action Timeline</Text>
								<Text fw={500}>
									{
										capacityPlan.recommendations.length > 0
											? `${Math.min(...capacityPlan.recommendations.map(r => parseInt(r.timeline.split('-')[0], 10) || 12)) } months`
											: 'No immediate action needed'
									}
								</Text>
							</Group>
						</Stack>
					</Card>
				</Grid.Col>
			</Grid>
		</Stack>
	);
}

export default CapacityPlanner;
