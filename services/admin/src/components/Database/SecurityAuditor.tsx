'use client';

import {
	<PERSON><PERSON>,
	Card,
	Text,
	Button,
	Select,
	Group,
	Stack,
	Title,
	Badge,
	Table,
	Alert,
	Progress,
	Tabs,
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import {
	IconShield,
	IconAlertTriangle,
	IconCheck,
	IconEye,
	// IconRefresh,
} from '@tabler/icons-react';
import { useState, useEffect } from 'react';

import type { SecurityAudit } from '@/types/database';

function SecurityAuditor()
{
	const [audit, setAudit] = useState<SecurityAudit | null>(null);
	const [loading, setLoading] = useState(false);
	const [selectedDatabase, setSelectedDatabase] = useState<string>('mariadb');

	const databases = ['scylla', 'mariadb', 'redis', 'manticore'];

	const performSecurityAudit = async () =>
	{
		if (!selectedDatabase) return;

		try
		{
			setLoading(true);
			const response = await fetch('/api/database/security', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					action: 'audit',
					database: selectedDatabase,
				}),
			});

			if (!response.ok) throw new Error('Failed to perform security audit');

			const result = await response.json();
			setAudit(result.audit);

			notifications.show({
				title: 'Success',
				message: 'Security audit completed',
				color: 'green',
			});
		}
		catch (error)
		{
			notifications.show({
				title: 'Error',
				message: 'Failed to perform security audit',
				color: 'red',
			});
		}
		finally
		{
			setLoading(false);
		}
	};

	useEffect(() =>
	{
		if (selectedDatabase)
		{
			performSecurityAudit();
		}
	}, [selectedDatabase]);

	const getRiskColor = (riskLevel: string) =>
	{
		switch (riskLevel)
		{
			case 'high': return 'red';
			case 'medium': return 'yellow';
			case 'low': return 'blue';
			default: return 'gray';
		}
	};

	const getComplianceColor = (status: string) =>
	{
		switch (status)
		{
			case 'pass': return 'green';
			case 'warning': return 'yellow';
			case 'fail': return 'red';
			default: return 'gray';
		}
	};

	const getComplianceIcon = (status: string) =>
	{
		switch (status)
		{
			case 'pass': return <IconCheck size={16} />;
			case 'warning': return <IconAlertTriangle size={16} />;
			case 'fail': return <IconAlertTriangle size={16} />;
			default: return <IconEye size={16} />;
		}
	};

	return (
		<Stack gap="md">
			<Group justify="space-between">
				<Title order={2}>Database Security Auditor</Title>
				<Group>
					<Select
						data={databases.map(db => ({ value: db, label: db.toUpperCase() }))}
						value={selectedDatabase}
						onChange={value => setSelectedDatabase(value || '')}
					/>
					<Button
						onClick={performSecurityAudit}
						leftSection={<IconShield size={16} />}
						loading={loading}
					>
						Run Security Audit
					</Button>
				</Group>
			</Group>

			{!audit ? (
				<Card>
					<Text c="dimmed" ta="center" py="xl">
						Run a security audit to view results
					</Text>
				</Card>
			) : (
				<Tabs defaultValue="overview">
					<Tabs.List>
						<Tabs.Tab value="overview">Overview</Tabs.Tab>
						<Tabs.Tab value="access">Access Patterns</Tabs.Tab>
						<Tabs.Tab value="suspicious">Suspicious Activity</Tabs.Tab>
						<Tabs.Tab value="compliance">Compliance</Tabs.Tab>
					</Tabs.List>

					<Tabs.Panel value="overview" pt="md">
						<Grid>
							<Grid.Col span={{ base: 12, md: 6 }}>
								<Card>
									<Title order={3} mb="md">Security Overview</Title>
									<Stack gap="md">
										<Group justify="space-between">
											<Text>Database</Text>
											<Badge>{audit.database.toUpperCase()}</Badge>
										</Group>

										<Group justify="space-between">
											<Text>Audit Date</Text>
											<Text size="sm">{new Date(audit.timestamp).toLocaleString()}</Text>
										</Group>

										<Group justify="space-between">
											<Text>Total Users</Text>
											<Text fw={500}>{audit.accessPatterns.length}</Text>
										</Group>

										<Group justify="space-between">
											<Text>Suspicious Users</Text>
											<Text fw={500} c={audit.accessPatterns.filter(p => p.suspiciousActivity).length > 0 ? 'red' : 'green'}>
												{audit.accessPatterns.filter(p => p.suspiciousActivity).length}
											</Text>
										</Group>

										<Group justify="space-between">
											<Text>Suspicious Queries</Text>
											<Text fw={500} c={audit.suspiciousQueries.length > 0 ? 'red' : 'green'}>
												{audit.suspiciousQueries.length}
											</Text>
										</Group>

										<Group justify="space-between">
											<Text>Compliance Issues</Text>
											<Text fw={500} c={audit.complianceChecks.filter(c => c.status === 'fail').length > 0 ? 'red' : 'green'}>
												{audit.complianceChecks.filter(c => c.status === 'fail').length}
											</Text>
										</Group>
									</Stack>
								</Card>
							</Grid.Col>

							<Grid.Col span={{ base: 12, md: 6 }}>
								<Card>
									<Title order={3} mb="md">Risk Assessment</Title>
									<Stack gap="md">
										<div>
											<Group justify="space-between" mb="xs">
												<Text>Overall Risk Level</Text>
												<Badge color={getRiskColor('medium')} size="lg">
													MEDIUM
												</Badge>
											</Group>
											<Text size="sm" c="dimmed">
												Based on access patterns, suspicious activity, and compliance status
											</Text>
										</div>

										<div>
											<Text size="sm" mb="xs">Security Score</Text>
											<Progress value={75} color="blue" size="lg" />
											<Text size="xs" c="dimmed" mt="xs">75/100</Text>
										</div>

										<Alert color="yellow" icon={<IconAlertTriangle size={16} />}>
											Review suspicious activity and address compliance issues to improve security posture.
										</Alert>
									</Stack>
								</Card>
							</Grid.Col>
						</Grid>
					</Tabs.Panel>

					<Tabs.Panel value="access" pt="md">
						<Card>
							<Title order={3} mb="md">User Access Patterns</Title>

							<Table>
								<Table.Thead>
									<Table.Tr>
										<Table.Th>User</Table.Th>
										<Table.Th>Queries</Table.Th>
										<Table.Th>Tables Accessed</Table.Th>
										<Table.Th>Last Access</Table.Th>
										<Table.Th>Status</Table.Th>
									</Table.Tr>
								</Table.Thead>
								<Table.Tbody>
									{audit.accessPatterns.map((pattern, index) => (
										<Table.Tr key={index}>
											<Table.Td>
												<Text fw={500}>{pattern.user}</Text>
											</Table.Td>
											<Table.Td>
												<Text>{pattern.queries.toLocaleString()}</Text>
											</Table.Td>
											<Table.Td>
												<Text size="sm">{pattern.tables.join(', ')}</Text>
											</Table.Td>
											<Table.Td>
												<Text size="sm">{new Date(pattern.lastAccess).toLocaleString()}</Text>
											</Table.Td>
											<Table.Td>
												<Badge color={pattern.suspiciousActivity ? 'red' : 'green'} variant="light">
													{pattern.suspiciousActivity ? 'Suspicious' : 'Normal'}
												</Badge>
											</Table.Td>
										</Table.Tr>
									))}
								</Table.Tbody>
							</Table>
						</Card>
					</Tabs.Panel>

					<Tabs.Panel value="suspicious" pt="md">
						<Card>
							<Title order={3} mb="md">Suspicious Activity</Title>

							{audit.suspiciousQueries.length === 0 ? (
								<Alert color="green" icon={<IconCheck size={16} />}>
									No suspicious queries detected in the audit period.
								</Alert>
							) : (
								<Stack gap="md">
									{audit.suspiciousQueries.map((query, index) => (
										<Alert
											key={index}
											color={query.riskLevel === 'high' ? 'red' : 'yellow'}
											icon={<IconAlertTriangle size={16} />}
										>
											<Stack gap="xs">
												<Group justify="space-between">
													<Text fw={500}>Suspicious Query Detected</Text>
													<Badge color={query.riskLevel === 'high' ? 'red' : 'yellow'}>
														{query.riskLevel.toUpperCase()} RISK
													</Badge>
												</Group>
												<Text size="sm" style={{ fontFamily: 'monospace' }}>
													{query.query}
												</Text>
												<Group>
													<Text size="sm" c="dimmed">User: {query.user}</Text>
													<Text size="sm" c="dimmed">
														Time: {new Date(query.timestamp).toLocaleString()}
													</Text>
												</Group>
												<Text size="sm">
													<strong>Reason:</strong> {query.reason}
												</Text>
											</Stack>
										</Alert>
									))}
								</Stack>
							)}
						</Card>
					</Tabs.Panel>

					<Tabs.Panel value="compliance" pt="md">
						<Card>
							<Title order={3} mb="md">Compliance Checks</Title>

							<Table>
								<Table.Thead>
									<Table.Tr>
										<Table.Th>Check</Table.Th>
										<Table.Th>Status</Table.Th>
										<Table.Th>Details</Table.Th>
									</Table.Tr>
								</Table.Thead>
								<Table.Tbody>
									{audit.complianceChecks.map((check, index) => (
										<Table.Tr key={index}>
											<Table.Td>
												<Text fw={500}>{check.check}</Text>
											</Table.Td>
											<Table.Td>
												<Badge
													color={getComplianceColor(check.status)}
													variant="light"
													leftSection={getComplianceIcon(check.status)}
												>
													{check.status.toUpperCase()}
												</Badge>
											</Table.Td>
											<Table.Td>
												<Text size="sm">{check.details}</Text>
											</Table.Td>
										</Table.Tr>
									))}
								</Table.Tbody>
							</Table>

							<div style={{ marginTop: '1rem' }}>
								<Text fw={500} mb="sm">Compliance Summary</Text>
								<Grid>
									<Grid.Col span={4}>
										<Text size="sm" c="dimmed">Passed</Text>
										<Text fw={500} c="green">
											{audit.complianceChecks.filter(c => c.status === 'pass').length}
										</Text>
									</Grid.Col>
									<Grid.Col span={4}>
										<Text size="sm" c="dimmed">Warnings</Text>
										<Text fw={500} c="yellow">
											{audit.complianceChecks.filter(c => c.status === 'warning').length}
										</Text>
									</Grid.Col>
									<Grid.Col span={4}>
										<Text size="sm" c="dimmed">Failed</Text>
										<Text fw={500} c="red">
											{audit.complianceChecks.filter(c => c.status === 'fail').length}
										</Text>
									</Grid.Col>
								</Grid>
							</div>
						</Card>
					</Tabs.Panel>
				</Tabs>
			)}
		</Stack>
	);
}

export default SecurityAuditor;
