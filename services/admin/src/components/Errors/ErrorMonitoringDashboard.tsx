'use client';

import {
	Container,
	Grid,
	Card,
	Title,
	Text,
	Badge,
	Group,
	Stack,
	Button,
	Select,
	Tabs,
	Alert,
	ActionIcon,
	Tooltip,
	// Progress,
	RingProgress,
	Table,
	ScrollArea,
	Modal,
	Code,
	Divider,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { clientLogger } from '@/lib/logger';
import { notifications } from '@mantine/notifications';
import {
	IconAlertTriangle,
	IconTrendingUp,
	IconBug,
	IconRefresh,
	IconDownload,
	IconEye,
	IconChartLine,
	IconShield,
	IconClock,
	IconUsers,
} from '@tabler/icons-react';
import React, { useState, useEffect } from 'react';


import { ErrorSeverityEnum, ErrorCategoryEnum } from '../../lib/errors/ErrorCodes';

import type {
	ErrorAnalyticsType,
	ErrorRateMetricsType,
	ErrorDebugInfoType,
} from '../../lib/errors/types';

interface ErrorMonitoringDashboardProps
{
	refreshInterval?: number;
}

type ErrorPairType =
{
	error1: string;
	error2: string;
	correlation: number;
};

type UserErrorPatternType =
{
	userId: string;
	errorCodes: string[];
	frequency: number;
};

type ErrorPatternsType =
{
	frequentPairs: ErrorPairType[];
	userPatterns: UserErrorPatternType[];
} | null;

export function ErrorMonitoringDashboard({
	refreshInterval = 30000,
}: ErrorMonitoringDashboardProps)
{
	const [analytics, setAnalytics] = useState<ErrorAnalyticsType[]>([]);
	const [metrics, setMetrics] = useState<ErrorRateMetricsType | null>(null);
	type TrendPointType = Record<string, unknown>;
	const [trends, setTrends] = useState<TrendPointType[]>([]);
	const [loading, setLoading] = useState(true);
	const [selectedTimeRange, setSelectedTimeRange] = useState('24h');
	const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
	const [selectedSeverity, setSelectedSeverity] = useState<string | null>(null);
	const [debugModalOpened, { open: openDebugModal, close: closeDebugModal }] = useDisclosure(false);
	const [selectedErrorForDebug, setSelectedErrorForDebug] = useState<string | null>(null);
	const [debugInfo, setDebugInfo] = useState<ErrorDebugInfoType | null>(null);
	const [patterns, setPatterns] = useState<ErrorPatternsType>(null);

	// Fetch error analytics
	const fetchAnalytics = async () =>
	{
		try
		{
			const params = new URLSearchParams();
			if (selectedCategory) params.append('category', selectedCategory);
			if (selectedSeverity) params.append('severity', selectedSeverity);

			// Add time range
			const now = new Date();
			const timeRanges = {
				'1h': 1 * 60 * 60 * 1000,
				'24h': 24 * 60 * 60 * 1000,
				'7d': 7 * 24 * 60 * 60 * 1000,
				'30d': 30 * 24 * 60 * 60 * 1000,
			};

			const rangeMs = timeRanges[selectedTimeRange as keyof typeof timeRanges] || timeRanges['24h'];
			const startDate = new Date(now.getTime() - rangeMs);

			params.append('startDate', startDate.toISOString());
			params.append('endDate', now.toISOString());

			const response = await fetch(`/api/errors/analytics?${params}`);
			const data = await response.json();

			if (data.success)
			{
				setAnalytics(data.data);
			}
		}
		catch (error)
		{
			clientLogger.error('Failed to fetch analytics:', error);
			notifications.show({
				title: 'Error',
				message: 'Failed to fetch error analytics',
				color: 'red',
			});
		}
	};

	// Fetch error metrics
	const fetchMetrics = async () =>
	{
		try
		{
			const response = await fetch('/api/errors/metrics');
			const data = await response.json();

			if (data.success)
			{
				setMetrics(data.data);
			}
		}
		catch (error)
		{
			clientLogger.error('Failed to fetch metrics:', error);
		}
	};

	// Fetch error trends
	const fetchTrends = async () =>
	{
		try
		{
			const now = new Date();
			const timeRanges =
			{
				'1h': { ms: 1 * 60 * 60 * 1000, granularity: 'hour' },
				'24h': { ms: 24 * 60 * 60 * 1000, granularity: 'hour' },
				'7d': { ms: 7 * 24 * 60 * 60 * 1000, granularity: 'day' },
				'30d': { ms: 30 * 24 * 60 * 60 * 1000, granularity: 'day' },
			};

			const range = timeRanges[selectedTimeRange as keyof typeof timeRanges] || timeRanges['24h'];
			const startDate = new Date(now.getTime() - range.ms);

			const params = new URLSearchParams({
				startDate: startDate.toISOString(),
				endDate: now.toISOString(),
				granularity: range.granularity,
			});

			const response = await fetch(`/api/errors/trends?${params}`);
			const data = await response.json();

			if (data.success)
			{
				setTrends(data.data);
			}
		}
		catch (error)
		{
			clientLogger.error('Failed to fetch trends:', error);
		}
	};

	// Fetch error patterns
	const fetchPatterns = async () =>
	{
		try
		{
			const response = await fetch('/api/errors/patterns');
			const data = await response.json();

			if (data.success)
			{
				const parsed: ErrorPatternsType = data.data as ErrorPatternsType;
				setPatterns(parsed);
			}
		}
		catch (error)
		{
			clientLogger.error('Failed to fetch patterns:', error);
		}
	};

	// Fetch debug information
	const fetchDebugInfo = async (errorId: string) =>
	{
		try
		{
			const response = await fetch(`/api/errors/debug/${errorId}`);
			const data = await response.json();

			if (data.success)
			{
				setDebugInfo(data.data);
			}
			else
			{
				notifications.show({
					title: 'Error',
					message: 'Failed to fetch debug information',
					color: 'red',
				});
			}
		}
		catch (error)
		{
			clientLogger.error('Failed to fetch debug info:', error);
			notifications.show({
				title: 'Error',
				message: 'Failed to fetch debug information',
				color: 'red',
			});
		}
	};

	// Load all data
	const loadData = async () =>
	{
		setLoading(true);
		await Promise.all([
			fetchAnalytics(),
			fetchMetrics(),
			fetchTrends(),
			fetchPatterns(),
		]);
		setLoading(false);
	};

	// Handle debug modal
	const handleViewDebug = async (errorId: string) =>
	{
		setSelectedErrorForDebug(errorId);
		setDebugInfo(null);
		openDebugModal();
		await fetchDebugInfo(errorId);
	};

	// Export analytics data
	const handleExportData = async (format: 'json' | 'csv') =>
	{
		try
		{
			const params = new URLSearchParams();
			if (selectedCategory) params.append('category', selectedCategory);
			if (selectedSeverity) params.append('severity', selectedSeverity);
			params.append('format', format);

			const response = await fetch(`/api/errors/analytics/export?${params}`);
			const blob = await response.blob();

			const url = window.URL.createObjectURL(blob);
			const a = document.createElement('a');
			a.href = url;
			a.download = `error-analytics.${format}`;
			document.body.appendChild(a);
			a.click();
			window.URL.revokeObjectURL(url);
			document.body.removeChild(a);

			notifications.show({
				title: 'Success',
				message: `Analytics exported as ${format.toUpperCase()}`,
				color: 'green',
			});
		}
		catch (error)
		{
			notifications.show({
				title: 'Error',
				message: 'Failed to export analytics data',
				color: 'red',
			});
		}
	};

	// Get severity color
	const getSeverityColor = (severity: ErrorSeverityEnum): string =>
	{
		switch (severity)
		{
			case ErrorSeverityEnum.CRITICAL: return 'red';
			case ErrorSeverityEnum.HIGH: return 'orange';
			case ErrorSeverityEnum.MEDIUM: return 'yellow';
			case ErrorSeverityEnum.LOW: return 'green';
			default: return 'gray';
		}
	};

	// Calculate summary statistics
	const summaryStats = React.useMemo(() =>
	{
		const totalErrors = analytics.reduce((sum, a) => sum + a.frequency, 0);
		const criticalErrors = analytics
			.filter(a => a.severity === ErrorSeverityEnum.CRITICAL)
			.reduce((sum, a) => sum + a.frequency, 0);
		const openErrors = analytics.filter(a => a.resolutionStatus === 'open').length;
		const uniqueUsers = new Set(analytics.flatMap(a => a.affectedUsers)).size;

		return {
			totalErrors,
			criticalErrors,
			openErrors,
			uniqueUsers,
		};
	}, [analytics]);

	useEffect(() =>
	{
		loadData();
	}, [selectedTimeRange, selectedCategory, selectedSeverity]);

	useEffect(() =>
	{
		const interval = setInterval(loadData, refreshInterval);
		return () => clearInterval(interval);
	}, [refreshInterval]);

	return (
		<Container size="xl" py="md">
			<Stack gap="lg">
				{/* Header */}
				<Group justify="space-between">
					<div>
						<Title order={2}>Error Monitoring Dashboard</Title>
						<Text c="dimmed">Real-time error tracking and analytics</Text>
					</div>

					<Group>
						<Select
							placeholder="Time Range"
							value={selectedTimeRange}
							onChange={value => setSelectedTimeRange(value || '24h')}
							data={[
								{ value: '1h', label: 'Last Hour' },
								{ value: '24h', label: 'Last 24 Hours' },
								{ value: '7d', label: 'Last 7 Days' },
								{ value: '30d', label: 'Last 30 Days' },
							]}
							w={150}
						/>

						<Select
							placeholder="Category"
							value={selectedCategory}
							onChange={setSelectedCategory}
							data={Object.values(ErrorCategoryEnum).map(cat => ({ value: cat, label: cat }))}
							clearable
							w={150}
						/>

						<Select
							placeholder="Severity"
							value={selectedSeverity}
							onChange={setSelectedSeverity}
							data={Object.values(ErrorSeverityEnum).map(sev => ({ value: sev, label: sev }))}
							clearable
							w={150}
						/>

						<Button
							leftSection={<IconRefresh size={16} />}
							onClick={loadData}
							loading={loading}
							variant="light"
						>
							Refresh
						</Button>
					</Group>
				</Group>

				{/* Summary Cards */}
				<Grid>
					<Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
						<Card withBorder>
							<Group justify="space-between">
								<div>
									<Text size="sm" c="dimmed">Total Errors</Text>
									<Text size="xl" fw={700}>{summaryStats.totalErrors.toLocaleString()}</Text>
								</div>
								<IconAlertTriangle size={24} color="var(--mantine-color-red-6)" />
							</Group>
						</Card>
					</Grid.Col>

					<Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
						<Card withBorder>
							<Group justify="space-between">
								<div>
									<Text size="sm" c="dimmed">Critical Errors</Text>
									<Text size="xl" fw={700} c="red">{summaryStats.criticalErrors.toLocaleString()}</Text>
								</div>
								<IconShield size={24} color="var(--mantine-color-red-6)" />
							</Group>
						</Card>
					</Grid.Col>

					<Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
						<Card withBorder>
							<Group justify="space-between">
								<div>
									<Text size="sm" c="dimmed">Open Issues</Text>
									<Text size="xl" fw={700}>{summaryStats.openErrors.toLocaleString()}</Text>
								</div>
								<IconClock size={24} color="var(--mantine-color-orange-6)" />
							</Group>
						</Card>
					</Grid.Col>

					<Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
						<Card withBorder>
							<Group justify="space-between">
								<div>
									<Text size="sm" c="dimmed">Affected Users</Text>
									<Text size="xl" fw={700}>{summaryStats.uniqueUsers.toLocaleString()}</Text>
								</div>
								<IconUsers size={24} color="var(--mantine-color-blue-6)" />
							</Group>
						</Card>
					</Grid.Col>
				</Grid>

				{/* Error Rate Metrics */}
				{metrics && (
					<Card withBorder>
						<Title order={4} mb="md">Error Rate Metrics</Title>
						<Grid>
							<Grid.Col span={{ base: 12, md: 6 }}>
								<Group>
									<RingProgress
										size={120}
										thickness={12}
										sections={[
											{ value: metrics.errorRate, color: metrics.errorRate > 5 ? 'red' : 'green' },
										]}
										label={(
											<Text ta="center" fw={700} size="lg">
												{metrics.errorRate.toFixed(2)}%
											</Text>
										)}
									/>
									<div>
										<Text size="sm" c="dimmed">Error Rate</Text>
										<Text size="lg" fw={600}>{metrics.errorRate.toFixed(2)}%</Text>
										<Text size="xs" c="dimmed">
											{metrics.totalErrors} errors / {metrics.totalRequests} requests
										</Text>
									</div>
								</Group>
							</Grid.Col>

							<Grid.Col span={{ base: 12, md: 6 }}>
								<Stack gap="xs">
									<Group justify="space-between">
										<Text size="sm">Average Resolution Time</Text>
										<Text size="sm" fw={600}>{Math.round(metrics.averageResolutionTime)} min</Text>
									</Group>
									<Group justify="space-between">
										<Text size="sm">SLA Violations</Text>
										<Text size="sm" fw={600} c="red">{metrics.slaViolations}</Text>
									</Group>
									<Group justify="space-between">
										<Text size="sm">Avg Response Time</Text>
										<Text size="sm" fw={600}>{metrics.performanceImpact.averageResponseTime}ms</Text>
									</Group>
								</Stack>
							</Grid.Col>
						</Grid>
					</Card>
				)}

				{/* Main Content Tabs */}
				<Tabs defaultValue="analytics">
					<Tabs.List>
						<Tabs.Tab value="analytics" leftSection={<IconChartLine size={16} />}>
							Analytics
						</Tabs.Tab>
						<Tabs.Tab value="trends" leftSection={<IconTrendingUp size={16} />}>
							Trends
						</Tabs.Tab>
						<Tabs.Tab value="patterns" leftSection={<IconBug size={16} />}>
							Patterns
						</Tabs.Tab>
					</Tabs.List>

					<Tabs.Panel value="analytics" pt="md">
						<Card withBorder>
							<Group justify="space-between" mb="md">
								<Title order={4}>Error Analytics</Title>
								<Group>
									<Button
										size="xs"
										variant="light"
										leftSection={<IconDownload size={14} />}
										onClick={() => handleExportData('json')}
									>
										Export JSON
									</Button>
									<Button
										size="xs"
										variant="light"
										leftSection={<IconDownload size={14} />}
										onClick={() => handleExportData('csv')}
									>
										Export CSV
									</Button>
								</Group>
							</Group>

							<ScrollArea>
								<Table striped highlightOnHover>
									<Table.Thead>
										<Table.Tr>
											<Table.Th>Error Code</Table.Th>
											<Table.Th>Category</Table.Th>
											<Table.Th>Severity</Table.Th>
											<Table.Th>Frequency</Table.Th>
											<Table.Th>Affected Users</Table.Th>
											<Table.Th>Status</Table.Th>
											<Table.Th>Last Occurrence</Table.Th>
											<Table.Th>Actions</Table.Th>
										</Table.Tr>
									</Table.Thead>
									<Table.Tbody>
										{analytics.map(error => (
											<Table.Tr key={error.errorId}>
												<Table.Td>
													<Code>{error.code}</Code>
												</Table.Td>
												<Table.Td>
													<Badge variant="light" color="blue">
														{error.category}
													</Badge>
												</Table.Td>
												<Table.Td>
													<Badge variant="light" color={getSeverityColor(error.severity)}>
														{error.severity}
													</Badge>
												</Table.Td>
												<Table.Td>{error.frequency}</Table.Td>
												<Table.Td>{error.affectedUsers.length}</Table.Td>
												<Table.Td>
													<Badge
														variant="light"
														color={error.resolutionStatus === 'resolved' ? 'green' : 'orange'}
													>
														{error.resolutionStatus}
													</Badge>
												</Table.Td>
												<Table.Td>
													<Text size="sm">
														{error.lastOccurrence.toLocaleString()}
													</Text>
												</Table.Td>
												<Table.Td>
													<Tooltip label="View Debug Info">
														<ActionIcon
															variant="light"
															size="sm"
															onClick={() => handleViewDebug(error.errorId)}
														>
															<IconEye size={14} />
														</ActionIcon>
													</Tooltip>
												</Table.Td>
											</Table.Tr>
										))}
									</Table.Tbody>
								</Table>
							</ScrollArea>
						</Card>
					</Tabs.Panel>

					<Tabs.Panel value="trends" pt="md">
						<Card withBorder>
							<Title order={4} mb="md">Error Trends</Title>
							{trends.length > 0 ? (
								<div>
									{/* This would contain a chart component */}
									<Alert icon={<IconChartLine size={16} />} color="blue">
										Chart visualization would be implemented here using a charting library like Recharts or Chart.js
									</Alert>
								</div>
							) : (
								<Text c="dimmed" ta="center" py="xl">
									No trend data available for the selected time range
								</Text>
							)}
						</Card>
					</Tabs.Panel>

					<Tabs.Panel value="patterns" pt="md">
						<Grid>
							<Grid.Col span={{ base: 12, md: 6 }}>
								<Card withBorder>
									<Title order={5} mb="md">Frequent Error Pairs</Title>
									{(patterns?.frequentPairs?.length ?? 0) > 0 ? (
										<Stack gap="xs">
											{(patterns?.frequentPairs ?? []).slice(0, 5).map((pair: ErrorPairType, index: number) => (
												<Group key={index} justify="space-between">
													<Group>
														<Code>{pair.error1}</Code>
														<Text>+</Text>
														<Code>{pair.error2}</Code>
													</Group>
													<Badge size="sm">{pair.correlation}</Badge>
												</Group>
											))}
										</Stack>
									) : (
										<Text c="dimmed" size="sm">No patterns detected</Text>
									)}
								</Card>
							</Grid.Col>

							<Grid.Col span={{ base: 12, md: 6 }}>
								<Card withBorder>
									<Title order={5} mb="md">User Error Patterns</Title>
									{(patterns?.userPatterns?.length ?? 0) > 0 ? (
										<Stack gap="xs">
											{(patterns?.userPatterns ?? []).slice(0, 5).map((pattern: UserErrorPatternType, index: number) => (
												<Group key={index} justify="space-between">
													<div>
														<Text size="sm" fw={500}>User {pattern.userId}</Text>
														<Text size="xs" c="dimmed">
															{pattern.errorCodes.length} error types
														</Text>
													</div>
													<Badge size="sm">{pattern.frequency}</Badge>
												</Group>
											))}
										</Stack>
									) : (
										<Text c="dimmed" size="sm">No user patterns detected</Text>
									)}
								</Card>
							</Grid.Col>
						</Grid>
					</Tabs.Panel>
				</Tabs>

				{/* Debug Modal */}
				<Modal
					opened={debugModalOpened}
					onClose={closeDebugModal}
					title="Error Debug Information"
					size="xl"
				>
					{debugInfo ? (
						<Stack gap="md">
							<Group justify="space-between">
								<Text fw={500}>Error ID: {debugInfo.errorId}</Text>
								<Button
									size="xs"
									variant="light"
									leftSection={<IconDownload size={14} />}
									onClick={() =>
									{
										if (selectedErrorForDebug)
										{
											window.open(`/api/errors/debug/${selectedErrorForDebug}?format=text`);
										}
									}}
								>
									Download Report
								</Button>
							</Group>

							<Divider />

							{debugInfo.reproductionSteps && (
								<div>
									<Text fw={500} mb="xs">Reproduction Steps:</Text>
									<Stack gap="xs">
										{debugInfo.reproductionSteps.map((step, index) => (
											<Text key={index} size="sm">
												{step}
											</Text>
										))}
									</Stack>
								</div>
							)}

							<div>
								<Text fw={500} mb="xs">Environment Information:</Text>
								<Code block>
									{JSON.stringify(debugInfo.environmentInfo, null, 2)}
								</Code>
							</div>

							{debugInfo.serviceStates && (
								<div>
									<Text fw={500} mb="xs">Service States:</Text>
									<Table>
										<Table.Thead>
											<Table.Tr>
												<Table.Th>Service</Table.Th>
												<Table.Th>Status</Table.Th>
												<Table.Th>Response Time</Table.Th>
												<Table.Th>Last Check</Table.Th>
											</Table.Tr>
										</Table.Thead>
										<Table.Tbody>
											{debugInfo.serviceStates.map((service, index) => (
												<Table.Tr key={index}>
													<Table.Td>{service.serviceName}</Table.Td>
													<Table.Td>
														<Badge
															color={service.status === 'healthy' ? 'green' : 'red'}
															size="sm"
														>
															{service.status}
														</Badge>
													</Table.Td>
													<Table.Td>{service.responseTime}ms</Table.Td>
													<Table.Td>
														<Text size="xs">
															{service.lastHealthCheck.toLocaleString()}
														</Text>
													</Table.Td>
												</Table.Tr>
											))}
										</Table.Tbody>
									</Table>
								</div>
							)}
						</Stack>
					) : (
						<Text>Loading debug information...</Text>
					)}
				</Modal>
			</Stack>
		</Container>
	);
}

export default ErrorMonitoringDashboard;
