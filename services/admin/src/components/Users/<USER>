'use client';

import {
	Modal,
	TextInput,
	Select,
	Button,
	Group,
	Stack,
	Switch,
	MultiSelect,
	Alert,
	Text,
	PasswordInput,
	Divider,
	Code,
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { clientLogger } from '@/lib/logger';
import { notifications } from '@mantine/notifications';
import { IconAlertCircle, IconKey } from '@tabler/icons-react';
import { useState } from 'react';

import { ROLE_PERMISSIONS } from '@/types/auth';
import type { CreateUserRequestType, PermissionType, UserRoleType } from '@/types/auth';

interface UserCreateModalProps
{
	opened: boolean;
	onClose: () => void;
	onSuccess: () => void;
}

const PERMISSION_OPTIONS = [
	{ value: 'users.view', label: 'View Users' },
	{ value: 'users.create', label: 'Create Users' },
	{ value: 'users.edit', label: 'Edit Users' },
	{ value: 'users.delete', label: 'Delete Users' },
	{ value: 'users.manage_permissions', label: 'Manage Permissions' },
	{ value: 'services.view', label: 'View Services' },
	{ value: 'services.restart', label: 'Restart Services' },
	{ value: 'services.configure', label: 'Configure Services' },
	{ value: 'domains.view', label: 'View Domains' },
	{ value: 'domains.edit', label: 'Edit Domains' },
	{ value: 'domains.delete', label: 'Delete Domains' },
	{ value: 'domains.bulk_operations', label: 'Bulk Domain Operations' },
	{ value: 'crawl.view', label: 'View Crawl Jobs' },
	{ value: 'crawl.create', label: 'Create Crawl Jobs' },
	{ value: 'crawl.manage', label: 'Manage Crawl Jobs' },
	{ value: 'crawl.configure', label: 'Configure Crawler' },
	{ value: 'seeder.view', label: 'View Seeder' },
	{ value: 'seeder.manage', label: 'Manage Seeder' },
	{ value: 'seeder.configure', label: 'Configure Seeder' },
	{ value: 'analytics.view', label: 'View Analytics' },
	{ value: 'analytics.export', label: 'Export Analytics' },
	{ value: 'config.view', label: 'View Configuration' },
	{ value: 'config.edit', label: 'Edit Configuration' },
	{ value: 'config.deploy', label: 'Deploy Configuration' },
	{ value: 'logs.view', label: 'View Logs' },
	{ value: 'logs.export', label: 'Export Logs' },
	{ value: 'ai.view', label: 'View AI Services' },
	{ value: 'ai.configure', label: 'Configure AI Services' },
	{ value: 'ai.manage', label: 'Manage AI Services' },
];

export function UserCreateModal({ opened, onClose, onSuccess }: UserCreateModalProps)
{
	const [loading, setLoading] = useState(false);
	const [generatedPassword, setGeneratedPassword] = useState<string | null>(null);

	const form = useForm<CreateUserRequestType>({
		initialValues: {
			username: '',
			password: '',
			role: 'viewer',
			permissions: [],
			email: '',
			fullName: '',
			mustChangePassword: false,
			generatePassword: true,
		},
		validate: {
			username: (value) =>
			{
				if (!value) return 'Username is required';
				if (value.length < 3) return 'Username must be at least 3 characters';
				if (!/^[a-zA-Z0-9_-]+$/.test(value)) return 'Username can only contain letters, numbers, hyphens, and underscores';
				return null;
			},
			password: (value, values) =>
			{
				if (!values.generatePassword && !value) return 'Password is required when not generating';
				if (!values.generatePassword && value && value.length < 8) return 'Password must be at least 8 characters';
				return null;
			},
			role: value => (!value ? 'Role is required' : null),
			email: (value) =>
			{
				if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) return 'Invalid email format';
				return null;
			},
		},
	});

	const handleRoleChange = (role: string) =>
	{
		const roleValue = (role as UserRoleType) || 'viewer';
		form.setFieldValue('role', roleValue);
		// Auto-set permissions based on role
		const rolePermissions = ROLE_PERMISSIONS[roleValue] || [];
		form.setFieldValue('permissions', rolePermissions);
	};

	const handleSubmit = async (values: CreateUserRequestType) =>
	{
		try
		{
			setLoading(true);
			setGeneratedPassword(null);

			const response = await fetch('/api/users', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(values),
			});

			const data = await response.json();

			if (!response.ok)
			{
				throw new Error(data.error || 'Failed to create user');
			}

			if (data.generatedPassword)
			{
				setGeneratedPassword(data.generatedPassword);
				notifications.show({
					title: 'User Created',
					message: 'User created successfully. Please save the generated password.',
					color: 'green',
				});
			}
			else
			{
				notifications.show({
					title: 'Success',
					message: 'User created successfully',
					color: 'green',
				});
				onSuccess();
			}
		}
		catch (error: any)
		{
			clientLogger.error('Error creating user:', error);
			notifications.show({
				title: 'Error',
				message: error.message || 'Failed to create user',
				color: 'red',
			});
		}
		finally
		{
			setLoading(false);
		}
	};

	const handleClose = () =>
	{
		if (generatedPassword)
		{
			onSuccess();
		}
		form.reset();
		setGeneratedPassword(null);
		onClose();
	};

	return (
		<Modal
			opened={opened}
			onClose={handleClose}
			title="Create New User"
			size="lg"
			centered
		>
			{generatedPassword ? (
				<Stack gap="md">
					<Alert color="green" icon={<IconKey size={16} />}>
						<Text fw={500} mb="xs">User created successfully!</Text>
						<Text size="sm" mb="md">
							The user has been created with a generated password. Please save this password securely
							as it will not be shown again.
						</Text>
						<Code block>{generatedPassword}</Code>
					</Alert>
					<Group justify="flex-end">
						<Button onClick={handleClose}>
							Close
						</Button>
					</Group>
				</Stack>
			) : (
				<form onSubmit={form.onSubmit(handleSubmit)}>
					<Stack gap="md">
						<Group grow>
							<TextInput
								label="Username"
								placeholder="Enter username"
								required
								{...form.getInputProps('username')}
							/>
							<TextInput
								label="Full Name"
								placeholder="Enter full name"
								{...form.getInputProps('fullName')}
							/>
						</Group>

						<TextInput
							label="Email"
							placeholder="Enter email address"
							type="email"
							{...form.getInputProps('email')}
						/>

						<Divider label="Role & Permissions" labelPosition="left" />

						<Select
							label="Role"
							placeholder="Select role"
							required
							data={[
								{ value: 'viewer', label: 'Viewer - Read-only access' },
								{ value: 'admin', label: 'Admin - Limited configuration access' },
								{ value: 'super_admin', label: 'Super Admin - Full access' },
							]}
							value={form.values.role}
							onChange={value => handleRoleChange(value || 'viewer')}
						/>

						<MultiSelect
							label="Custom Permissions"
							placeholder="Select additional permissions"
							data={PERMISSION_OPTIONS}
							value={form.values.permissions}
							onChange={value => form.setFieldValue('permissions', value as PermissionType[])}
							searchable
							clearable
							description="Role-based permissions are automatically selected. You can add or remove specific permissions here."
						/>

						<Divider label="Password Settings" labelPosition="left" />

						<Switch
							label="Generate secure password automatically"
							description="If enabled, a secure password will be generated and shown after creation"
							checked={form.values.generatePassword}
							onChange={event => form.setFieldValue('generatePassword', event.currentTarget.checked)}
						/>

						{!form.values.generatePassword && (
							<PasswordInput
								label="Password"
								placeholder="Enter password"
								required
								description="Password must be at least 8 characters long"
								{...form.getInputProps('password')}
							/>
						)}

						<Switch
							label="Require password change on first login"
							description="User will be forced to change password on their first login"
							checked={form.values.mustChangePassword}
							onChange={event => form.setFieldValue('mustChangePassword', event.currentTarget.checked)}
						/>

						<Group justify="flex-end" mt="md">
							<Button variant="default" onClick={handleClose}>
								Cancel
							</Button>
							<Button type="submit" loading={loading}>
								Create User
							</Button>
						</Group>
					</Stack>
				</form>
			)}
		</Modal>
	);
}
