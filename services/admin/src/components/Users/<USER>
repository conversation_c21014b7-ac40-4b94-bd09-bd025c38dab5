'use client';

import {
	Container,
	Title,
	Paper,
	Group,
	Button,
	TextInput,
	Select,
	Table,
	Badge,
	ActionIcon,
	Menu,
	Modal,
	Text,
	Pagination,
	Flex,
	Alert,
	Loader,
	Stack,
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { clientLogger } from '@/lib/logger';
import {
	IconPlus,
	IconSearch,
	IconDots,
	IconEdit,
	IconTrash,
	IconKey,
	IconShield,
	IconEye,
	IconUserOff,
	IconUserCheck,
	IconAlertTriangle,
} from '@tabler/icons-react';
import { useState, useEffect } from 'react';


import type { AdminUserType } from '@/types/auth';
import { UserCreateModal } from './UserCreateModal';
import { UserDetailsModal } from './UserDetailsModal';
import { UserEditModal } from './UserEditModal';
import { UserPasswordModal } from './UserPasswordModal';

type UserManagementInterfacePropsType = Record<string, never>;

function UserManagementInterface({}: UserManagementInterfacePropsType)
{
	const [users, setUsers] = useState<AdminUserType[]>([]);
	const [loading, setLoading] = useState(true);
	const [searchQuery, setSearchQuery] = useState('');
	const [roleFilter, setRoleFilter] = useState<string>('');
	const [statusFilter, setStatusFilter] = useState<string>('');
	const [page, setPage] = useState(1);
	const [totalPages, setTotalPages] = useState(1);
	const [selectedUser, setSelectedUser] = useState<AdminUserType | null>(null);

	// Modal states
	const [createModalOpen, setCreateModalOpen] = useState(false);
	const [editModalOpen, setEditModalOpen] = useState(false);
	const [detailsModalOpen, setDetailsModalOpen] = useState(false);
	const [passwordModalOpen, setPasswordModalOpen] = useState(false);
	const [deleteModalOpen, setDeleteModalOpen] = useState(false);

	const fetchUsers = async () =>
	{
		try
		{
			setLoading(true);
			const params = new URLSearchParams({
				page: page.toString(),
				limit: '20',
				search: searchQuery,
				role: roleFilter,
				status: statusFilter,
			});

			const response = await fetch(`/api/users?${params}`);
			if (!response.ok) throw new Error('Failed to fetch users');

			const data = await response.json();
			setUsers(data.users);
			setTotalPages(data.pagination.pages);
		}
		catch (error)
		{
			clientLogger.error('Error fetching users:', error);
			notifications.show({
				title: 'Error',
				message: 'Failed to fetch users',
				color: 'red',
			});
		}
		finally
		{
			setLoading(false);
		}
	};

	useEffect(() =>
	{
		fetchUsers();
	}, [page, searchQuery, roleFilter, statusFilter]);

	const handleDeleteUser = async () =>
	{
		if (!selectedUser) return;

		try
		{
			const response = await fetch(`/api/users/${selectedUser.id}`, {
				method: 'DELETE',
			});

			if (!response.ok) throw new Error('Failed to delete user');

			notifications.show({
				title: 'Success',
				message: 'User deleted successfully',
				color: 'green',
			});

			setDeleteModalOpen(false);
			setSelectedUser(null);
			fetchUsers();
		}
		catch (error)
		{
			clientLogger.error('Error deleting user:', error);
			notifications.show({
				title: 'Error',
				message: 'Failed to delete user',
				color: 'red',
			});
		}
	};

	const handleToggleUserStatus = async (user: AdminUserType) =>
	{
		try
		{
			const response = await fetch(`/api/users/${user.id}`, {
				method: 'PUT',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ isActive: !user.isActive }),
			});

			if (!response.ok) throw new Error('Failed to update user status');

			notifications.show({
				title: 'Success',
				message: `User ${user.isActive ? 'deactivated' : 'activated'} successfully`,
				color: 'green',
			});

			fetchUsers();
		}
		catch (error)
		{
			clientLogger.error('Error updating user status:', error);
			notifications.show({
				title: 'Error',
				message: 'Failed to update user status',
				color: 'red',
			});
		}
	};

	const handleUnlockUser = async (user: AdminUserType) =>
	{
		try
		{
			const response = await fetch(`/api/users/${user.id}/password`, {
				method: 'PUT',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ unlock: true }),
			});

			if (!response.ok) throw new Error('Failed to unlock user');

			notifications.show({
				title: 'Success',
				message: 'User account unlocked successfully',
				color: 'green',
			});

			fetchUsers();
		}
		catch (error)
		{
			clientLogger.error('Error unlocking user:', error);
			notifications.show({
				title: 'Error',
				message: 'Failed to unlock user account',
				color: 'red',
			});
		}
	};

	const getUserStatusBadge = (user: AdminUserType) =>
	{
		if (user.lockedUntil && new Date(user.lockedUntil) > new Date())
		{
			return <Badge color="red" variant="filled">Locked</Badge>;
		}
		if (!user.isActive)
		{
			return <Badge color="gray" variant="filled">Inactive</Badge>;
		}
		if (user.sessionCount > 0)
		{
			return <Badge color="green" variant="filled">Online</Badge>;
		}
		return <Badge color="blue" variant="filled">Active</Badge>;
	};

	const getRoleBadge = (role: string) =>
	{
		const colors = {
			super_admin: 'red',
			admin: 'orange',
			viewer: 'blue',
		};
		return <Badge color={colors[role as keyof typeof colors] || 'gray'}>{role}</Badge>;
	};

	const isUserLocked = (user: AdminUserType) => user.lockedUntil && new Date(user.lockedUntil) > new Date();

	return (
		<Container size="xl" py="md">
			<Stack gap="md">
				<Group justify="space-between">
					<Title order={2}>User Management</Title>
					<Button
						leftSection={<IconPlus size={16} />}
						onClick={() => setCreateModalOpen(true)}
					>
						Create User
					</Button>
				</Group>

				<Paper p="md" withBorder>
					<Group gap="md" mb="md">
						<TextInput
							placeholder="Search users..."
							leftSection={<IconSearch size={16} />}
							value={searchQuery}
							onChange={e => setSearchQuery(e.target.value)}
							style={{ flex: 1 }}
						/>
						<Select
							placeholder="Filter by role"
							data={[
								{ value: '', label: 'All Roles' },
								{ value: 'super_admin', label: 'Super Admin' },
								{ value: 'admin', label: 'Admin' },
								{ value: 'viewer', label: 'Viewer' },
							]}
							value={roleFilter}
							onChange={value => setRoleFilter(value || '')}
							clearable
						/>
						<Select
							placeholder="Filter by status"
							data={[
								{ value: '', label: 'All Status' },
								{ value: 'active', label: 'Active' },
								{ value: 'inactive', label: 'Inactive' },
								{ value: 'locked', label: 'Locked' },
							]}
							value={statusFilter}
							onChange={value => setStatusFilter(value || '')}
							clearable
						/>
					</Group>

					{loading ? (
						<Flex justify="center" py="xl">
							<Loader />
						</Flex>
					) : (
						<>
							<Table striped highlightOnHover>
								<Table.Thead>
									<Table.Tr>
										<Table.Th>Username</Table.Th>
										<Table.Th>Role</Table.Th>
										<Table.Th>Status</Table.Th>
										<Table.Th>Last Login</Table.Th>
										<Table.Th>Sessions</Table.Th>
										<Table.Th>Created</Table.Th>
										<Table.Th>Actions</Table.Th>
									</Table.Tr>
								</Table.Thead>
								<Table.Tbody>
									{users.map(user => (
										<Table.Tr key={user.id}>
											<Table.Td>
												<div>
													<Text fw={500}>{user.username}</Text>
													{user.fullName && (
														<Text size="sm" c="dimmed">{user.fullName}</Text>
													)}
													{user.email && (
														<Text size="sm" c="dimmed">{user.email}</Text>
													)}
												</div>
											</Table.Td>
											<Table.Td>{getRoleBadge(user.role)}</Table.Td>
											<Table.Td>
												<Group gap="xs">
													{getUserStatusBadge(user)}
													{user.mustChangePassword && (
														<Badge color="yellow" size="sm">Password Reset Required</Badge>
													)}
													{isUserLocked(user) && (
														<IconAlertTriangle size={16} color="red" />
													)}
												</Group>
											</Table.Td>
											<Table.Td>
												{user.lastLogin ? (
													<Text size="sm">
														{new Date(user.lastLogin).toLocaleDateString()}
													</Text>
												) : (
													<Text size="sm" c="dimmed">Never</Text>
												)}
											</Table.Td>
											<Table.Td>
												<Badge variant="light" color={user.sessionCount > 0 ? 'green' : 'gray'}>
													{user.sessionCount}
												</Badge>
											</Table.Td>
											<Table.Td>
												<Text size="sm">
													{new Date(user.createdAt).toLocaleDateString()}
												</Text>
											</Table.Td>
											<Table.Td>
												<Menu shadow="md" width={200}>
													<Menu.Target>
														<ActionIcon variant="subtle">
															<IconDots size={16} />
														</ActionIcon>
													</Menu.Target>
													<Menu.Dropdown>
														<Menu.Item
															leftSection={<IconEye size={14} />}
															onClick={() =>
															{
																setSelectedUser(user);
																setDetailsModalOpen(true);
															}}
														>
															View Details
														</Menu.Item>
														<Menu.Item
															leftSection={<IconEdit size={14} />}
															onClick={() =>
															{
																setSelectedUser(user);
																setEditModalOpen(true);
															}}
														>
															Edit User
														</Menu.Item>
														<Menu.Item
															leftSection={<IconKey size={14} />}
															onClick={() =>
															{
																setSelectedUser(user);
																setPasswordModalOpen(true);
															}}
														>
															Reset Password
														</Menu.Item>
														<Menu.Divider />
														{isUserLocked(user) ? (
															<Menu.Item
																leftSection={<IconUserCheck size={14} />}
																onClick={() => handleUnlockUser(user)}
																color="green"
															>
																Unlock Account
															</Menu.Item>
														) : (
															<Menu.Item
																leftSection={user.isActive ? <IconUserOff size={14} /> : <IconUserCheck size={14} />}
																onClick={() => handleToggleUserStatus(user)}
																color={user.isActive ? 'orange' : 'green'}
															>
																{user.isActive ? 'Deactivate' : 'Activate'}
															</Menu.Item>
														)}
														<Menu.Divider />
														<Menu.Item
															leftSection={<IconTrash size={14} />}
															color="red"
															onClick={() =>
															{
																setSelectedUser(user);
																setDeleteModalOpen(true);
															}}
														>
															Delete User
														</Menu.Item>
													</Menu.Dropdown>
												</Menu>
											</Table.Td>
										</Table.Tr>
									))}
								</Table.Tbody>
							</Table>

							{totalPages > 1 && (
								<Group justify="center" mt="md">
									<Pagination
										value={page}
										onChange={setPage}
										total={totalPages}
									/>
								</Group>
							)}
						</>
					)}
				</Paper>
			</Stack>

			{/* Modals */}
			<UserCreateModal
				opened={createModalOpen}
				onClose={() => setCreateModalOpen(false)}
				onSuccess={() =>
				{
					setCreateModalOpen(false);
					fetchUsers();
				}}
			/>

			{selectedUser && (
				<>
					<UserEditModal
						opened={editModalOpen}
						onClose={() =>
						{
							setEditModalOpen(false);
							setSelectedUser(null);
						}}
						user={selectedUser}
						onSuccess={() =>
						{
							setEditModalOpen(false);
							setSelectedUser(null);
							fetchUsers();
						}}
					/>

					<UserDetailsModal
						opened={detailsModalOpen}
						onClose={() =>
						{
							setDetailsModalOpen(false);
							setSelectedUser(null);
						}}
						user={selectedUser}
					/>

					<UserPasswordModal
						opened={passwordModalOpen}
						onClose={() =>
						{
							setPasswordModalOpen(false);
							setSelectedUser(null);
						}}
						user={selectedUser}
						onSuccess={() =>
						{
							setPasswordModalOpen(false);
							setSelectedUser(null);
							fetchUsers();
						}}
					/>

					<Modal
						opened={deleteModalOpen}
						onClose={() =>
						{
							setDeleteModalOpen(false);
							setSelectedUser(null);
						}}
						title="Delete User"
						centered
					>
						<Stack gap="md">
							<Alert color="red" icon={<IconAlertTriangle size={16} />}>
								<Text>
									Are you sure you want to delete user <strong>{selectedUser.username}</strong>?
									This action cannot be undone.
								</Text>
							</Alert>
							<Group justify="flex-end">
								<Button
									variant="default"
									onClick={() =>
									{
										setDeleteModalOpen(false);
										setSelectedUser(null);
									}}
								>
									Cancel
								</Button>
								<Button color="red" onClick={handleDeleteUser}>
									Delete User
								</Button>
							</Group>
						</Stack>
					</Modal>
				</>
			)}
		</Container>
	);
}

export default UserManagementInterface;
