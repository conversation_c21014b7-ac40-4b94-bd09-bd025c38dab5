'use client';

import {
	Mo<PERSON>,
	Text,
	Badge,
	Group,
	Stack,
	Divider,
	Table,
	Tabs,
	Paper,
	ActionIcon,
	Button,
	Alert,
	Loader,
	ScrollArea,
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { clientLogger } from '@/lib/logger';
import {
	IconUser,
	IconActivity,
	IconShield,
	IconDevices,
	IconTrash,
	IconAlertTriangle,
} from '@tabler/icons-react';
import { useState, useEffect } from 'react';

import type { AdminUserType, UserSessionType, UserActivityType } from '@/types/auth';

interface UserDetailsModalProps
{
	opened: boolean;
	onClose: () => void;
	user: AdminUserType;
}

interface UserDetails extends AdminUserType
{
	sessions: UserSessionType[];
	recentActivity: UserActivityType[];
}

export function UserDetailsModal({ opened, onClose, user }: UserDetailsModalProps)
{
	const [userDetails, setUserDetails] = useState<UserDetails | null>(null);
	const [loading, setLoading] = useState(true);
	const [terminatingSession, setTerminatingSession] = useState<string | null>(null);

	const fetchUserDetails = async () =>
	{
		try
		{
			setLoading(true);
			const response = await fetch(`/api/users/${user.id}`);
			if (!response.ok) throw new Error('Failed to fetch user details');

			const data = await response.json();
			setUserDetails(data);
		}
		catch (error)
		{
			clientLogger.error('Error fetching user details:', error);
			notifications.show({
				title: 'Error',
				message: 'Failed to fetch user details',
				color: 'red',
			});
		}
		finally
		{
			setLoading(false);
		}
	};

	useEffect(() =>
	{
		if (opened)
		{
			fetchUserDetails();
		}
	}, [opened, user.id]);

	const handleTerminateSession = async (sessionId: string) =>
	{
		try
		{
			setTerminatingSession(sessionId);
			const response = await fetch(`/api/users/${user.id}/sessions?sessionId=${sessionId}`, {
				method: 'DELETE',
			});

			if (!response.ok) throw new Error('Failed to terminate session');

			notifications.show({
				title: 'Success',
				message: 'Session terminated successfully',
				color: 'green',
			});

			fetchUserDetails(); // Refresh data
		}
		catch (error)
		{
			clientLogger.error('Error terminating session:', error);
			notifications.show({
				title: 'Error',
				message: 'Failed to terminate session',
				color: 'red',
			});
		}
		finally
		{
			setTerminatingSession(null);
		}
	};

	const handleTerminateAllSessions = async () =>
	{
		try
		{
			const response = await fetch(`/api/users/${user.id}/sessions`, {
				method: 'DELETE',
			});

			if (!response.ok) throw new Error('Failed to terminate sessions');

			notifications.show({
				title: 'Success',
				message: 'All sessions terminated successfully',
				color: 'green',
			});

			fetchUserDetails(); // Refresh data
		}
		catch (error)
		{
			clientLogger.error('Error terminating sessions:', error);
			notifications.show({
				title: 'Error',
				message: 'Failed to terminate sessions',
				color: 'red',
			});
		}
	};

	const getUserStatusBadge = (user: AdminUserType) =>
	{
		if (user.lockedUntil && new Date(user.lockedUntil) > new Date())
		{
			return <Badge color="red" variant="filled">Locked</Badge>;
		}
		if (!user.isActive)
		{
			return <Badge color="gray" variant="filled">Inactive</Badge>;
		}
		if (user.sessionCount > 0)
		{
			return <Badge color="green" variant="filled">Online</Badge>;
		}
		return <Badge color="blue" variant="filled">Active</Badge>;
	};

	const getRoleBadge = (role: string) =>
	{
		const colors = {
			super_admin: 'red',
			admin: 'orange',
			viewer: 'blue',
		};
		return <Badge color={colors[role as keyof typeof colors] || 'gray'}>{role}</Badge>;
	};

	const formatDeviceInfo = (session: UserSessionType) =>
	{
		return session.userAgent || 'Unknown Device';
	};

	return (
		<Modal
			opened={opened}
			onClose={onClose}
			title={`User Details: ${user.username}`}
			size="xl"
			centered
		>
			{loading ? (
				<Group justify="center" py="xl">
					<Loader />
				</Group>
			) : userDetails ? (
				<Tabs defaultValue="profile">
					<Tabs.List>
						<Tabs.Tab value="profile" leftSection={<IconUser size={14} />}>
							Profile
						</Tabs.Tab>
						<Tabs.Tab value="sessions" leftSection={<IconDevices size={14} />}>
							Sessions ({userDetails.sessions.filter(s => s.isActive).length})
						</Tabs.Tab>
						<Tabs.Tab value="activity" leftSection={<IconActivity size={14} />}>
							Activity
						</Tabs.Tab>
						<Tabs.Tab value="permissions" leftSection={<IconShield size={14} />}>
							Permissions
						</Tabs.Tab>
					</Tabs.List>

					<Tabs.Panel value="profile" pt="md">
						<Stack gap="md">
							<Paper p="md" withBorder>
								<Stack gap="sm">
									<Group justify="space-between">
										<Text fw={500} size="lg">{userDetails.username}</Text>
										{getUserStatusBadge(userDetails)}
									</Group>

									{userDetails.fullName && (
										<Group>
											<Text c="dimmed" size="sm">Full Name:</Text>
											<Text size="sm">{userDetails.fullName}</Text>
										</Group>
									)}

									{userDetails.email && (
										<Group>
											<Text c="dimmed" size="sm">Email:</Text>
											<Text size="sm">{userDetails.email}</Text>
										</Group>
									)}

									<Group>
										<Text c="dimmed" size="sm">Role:</Text>
										{getRoleBadge(userDetails.role)}
									</Group>

									<Group>
										<Text c="dimmed" size="sm">Created:</Text>
										<Text size="sm">{new Date(userDetails.createdAt).toLocaleString()}</Text>
									</Group>

									<Group>
										<Text c="dimmed" size="sm">Last Login:</Text>
										<Text size="sm">
											{userDetails.lastLogin
												? new Date(userDetails.lastLogin).toLocaleString()
												: 'Never'}
										</Text>
									</Group>

									<Group>
										<Text c="dimmed" size="sm">Password Last Changed:</Text>
										<Text size="sm">{new Date(userDetails.passwordLastChanged).toLocaleString()}</Text>
									</Group>

									{userDetails.mustChangePassword && (
										<Alert color="yellow" icon={<IconAlertTriangle size={16} />}>
											User must change password on next login
										</Alert>
									)}

									{userDetails.lockedUntil && new Date(userDetails.lockedUntil) > new Date() && (
										<Alert color="red" icon={<IconAlertTriangle size={16} />}>
											Account is locked until {new Date(userDetails.lockedUntil).toLocaleString()}
										</Alert>
									)}
								</Stack>
							</Paper>
						</Stack>
					</Tabs.Panel>

					<Tabs.Panel value="sessions" pt="md">
						<Stack gap="md">
							<Group justify="space-between">
								<Text fw={500}>Active Sessions</Text>
								{userDetails.sessions.filter(s => s.isActive).length > 0 && (
									<Button
										size="sm"
										color="red"
										variant="light"
										onClick={handleTerminateAllSessions}
									>
										Terminate All Sessions
									</Button>
								)}
							</Group>

							<ScrollArea h={400}>
								<Table striped>
									<Table.Thead>
										<Table.Tr>
											<Table.Th>Device</Table.Th>
											<Table.Th>IP Address</Table.Th>
											<Table.Th>Created</Table.Th>
											<Table.Th>Last Activity</Table.Th>
											<Table.Th>Status</Table.Th>
											<Table.Th>Actions</Table.Th>
										</Table.Tr>
									</Table.Thead>
									<Table.Tbody>
										{userDetails.sessions.map(session => (
											<Table.Tr key={session.sessionId}>
												<Table.Td>
													<Text size="sm">{formatDeviceInfo(session)}</Text>
												</Table.Td>
												<Table.Td>
													<Text size="sm" ff="monospace">{session.ipAddress}</Text>
												</Table.Td>
												<Table.Td>
													<Text size="sm">{new Date(session.createdAt).toLocaleString()}</Text>
												</Table.Td>
												<Table.Td>
													<Text size="sm">{new Date(session.lastActivity).toLocaleString()}</Text>
												</Table.Td>
												<Table.Td>
													<Badge color={session.isActive ? 'green' : 'gray'}>
														{session.isActive ? 'Active' : 'Expired'}
													</Badge>
												</Table.Td>
												<Table.Td>
													{session.isActive && (
														<ActionIcon
															color="red"
															variant="light"
															size="sm"
															loading={terminatingSession === session.sessionId}
															onClick={() => handleTerminateSession(session.sessionId)}
														>
															<IconTrash size={14} />
														</ActionIcon>
													)}
												</Table.Td>
											</Table.Tr>
										))}
									</Table.Tbody>
								</Table>
							</ScrollArea>
						</Stack>
					</Tabs.Panel>

					<Tabs.Panel value="activity" pt="md">
						<Stack gap="md">
							<Text fw={500}>Recent Activity</Text>
							<ScrollArea h={400}>
								<Table striped>
									<Table.Thead>
										<Table.Tr>
											<Table.Th>Action</Table.Th>
											<Table.Th>Resource</Table.Th>
											<Table.Th>Timestamp</Table.Th>
											<Table.Th>IP Address</Table.Th>
											<Table.Th>Status</Table.Th>
										</Table.Tr>
									</Table.Thead>
									<Table.Tbody>
										{userDetails.recentActivity.map((activity, index) => (
											<Table.Tr key={index}>
												<Table.Td>
													<Text size="sm">{activity.action}</Text>
												</Table.Td>
												<Table.Td>
													<Text size="sm">{activity.resource}</Text>
												</Table.Td>
												<Table.Td>
													<Text size="sm">{new Date(activity.timestamp).toLocaleString()}</Text>
												</Table.Td>
												<Table.Td>
													<Text size="sm" ff="monospace">{activity.ipAddress}</Text>
												</Table.Td>
												<Table.Td>
													<Badge color={activity.success ? 'green' : 'red'} size="sm">
														{activity.success ? 'Success' : 'Failed'}
													</Badge>
												</Table.Td>
											</Table.Tr>
										))}
									</Table.Tbody>
								</Table>
							</ScrollArea>
						</Stack>
					</Tabs.Panel>

					<Tabs.Panel value="permissions" pt="md">
						<Stack gap="md">
							<Group justify="space-between">
								<Text fw={500}>Permissions</Text>
								{getRoleBadge(userDetails.role)}
							</Group>

							<Paper p="md" withBorder>
								<Stack gap="xs">
									{userDetails.permissions.map(permission => (
										<Group key={permission} gap="xs">
											<Badge variant="light" size="sm">{permission}</Badge>
										</Group>
									))}
								</Stack>
							</Paper>
						</Stack>
					</Tabs.Panel>
				</Tabs>
			) : (
				<Text>Failed to load user details</Text>
			)}
		</Modal>
	);
}
