'use client';

import {
	Paper,
	Title,
	Group,
	TextInput,
	Select,
	Button,
	Table,
	Badge,
	Pagination,
	Stack,
	Loader,
	Alert,
	Modal,
	Text,
	ScrollArea,
	ActionIcon,
	Menu,
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { clientLogger } from '@/lib/logger';
import {
	IconSearch,
	IconDownload,
	IconEye,
	IconDots,
	IconCalendar,
} from '@tabler/icons-react';
import { useState, useEffect } from 'react';

import type { AuditLogType } from '@/types/auth';

type AuditLogViewerPropsType = Record<string, never>;

function AuditLogViewer({}: AuditLogViewerPropsType)
{
	const [auditLogs, setAuditLogs] = useState<AuditLogType[]>([]);
	const [loading, setLoading] = useState(true);
	const [page, setPage] = useState(1);
	const [totalPages, setTotalPages] = useState(1);
	const [selectedLog, setSelectedLog] = useState<AuditLogType | null>(null);
	const [detailsModalOpen, setDetailsModalOpen] = useState(false);

	// Filters
	const [filters, setFilters] = useState({
		userId: '',
		action: '',
		resource: '',
		severity: '',
		startDate: null as Date | null,
		endDate: null as Date | null,
	});

	const fetchAuditLogs = async () =>
	{
		try
		{
			setLoading(true);
			const params = new URLSearchParams({
				page: page.toString(),
				limit: '50',
				...(filters.userId && { userId: filters.userId }),
				...(filters.action && { action: filters.action }),
				...(filters.resource && { resource: filters.resource }),
				...(filters.severity && { severity: filters.severity }),
				...(filters.startDate && { startDate: filters.startDate.toISOString() }),
				...(filters.endDate && { endDate: filters.endDate.toISOString() }),
			});

			const response = await fetch(`/api/users/audit?${params}`);
			if (!response.ok) throw new Error('Failed to fetch audit logs');

			const data = await response.json();
			setAuditLogs(data.auditLogs);
			setTotalPages(data.pagination.pages);
		}
		catch (error)
		{
			clientLogger.error('Error fetching audit logs:', error);
			notifications.show({
				title: 'Error',
				message: 'Failed to fetch audit logs',
				color: 'red',
			});
		}
		finally
		{
			setLoading(false);
		}
	};

	useEffect(() =>
	{
		fetchAuditLogs();
	}, [page, filters]);

	const handleExport = async (format: 'csv' | 'json') =>
	{
		try
		{
			const response = await fetch('/api/users/audit', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					exportFormat: format,
					filters,
				}),
			});

			if (!response.ok) throw new Error('Failed to export audit logs');

			// Handle file download
			const blob = await response.blob();
			const url = window.URL.createObjectURL(blob);
			const a = document.createElement('a');
			a.href = url;
			a.download = `audit-logs-${new Date().toISOString().split('T')[0]}.${format}`;
			document.body.appendChild(a);
			a.click();
			window.URL.revokeObjectURL(url);
			document.body.removeChild(a);

			notifications.show({
				title: 'Success',
				message: 'Audit logs exported successfully',
				color: 'green',
			});
		}
		catch (error)
		{
			clientLogger.error('Error exporting audit logs:', error);
			notifications.show({
				title: 'Error',
				message: 'Failed to export audit logs',
				color: 'red',
			});
		}
	};

	const getSeverityBadge = (severity: string) =>
	{
		const colors = {
			low: 'blue',
			medium: 'yellow',
			high: 'orange',
			critical: 'red',
		};
		return <Badge color={colors[severity as keyof typeof colors] || 'gray'}>{severity}</Badge>;
	};

	const clearFilters = () =>
	{
		setFilters({
			userId: '',
			action: '',
			resource: '',
			severity: '',
			startDate: null,
			endDate: null,
		});
		setPage(1);
	};

	return (
		<Stack gap="md">
			<Group justify="space-between">
				<Title order={3}>Audit Logs</Title>
				<Menu shadow="md" width={200}>
					<Menu.Target>
						<Button leftSection={<IconDownload size={16} />}>
							Export
						</Button>
					</Menu.Target>
					<Menu.Dropdown>
						<Menu.Item onClick={() => handleExport('csv')}>
							Export as CSV
						</Menu.Item>
						<Menu.Item onClick={() => handleExport('json')}>
							Export as JSON
						</Menu.Item>
					</Menu.Dropdown>
				</Menu>
			</Group>

			{/* Filters */}
			<Paper p="md" withBorder>
				<Stack gap="md">
					<Group gap="md">
						<TextInput
							placeholder="Search by user ID..."
							leftSection={<IconSearch size={16} />}
							value={filters.userId}
							onChange={e => setFilters(prev => ({ ...prev, userId: e.target.value }))}
							style={{ flex: 1 }}
						/>
						<TextInput
							placeholder="Search by action..."
							value={filters.action}
							onChange={e => setFilters(prev => ({ ...prev, action: e.target.value }))}
							style={{ flex: 1 }}
						/>
						<Select
							placeholder="Filter by resource"
							data={[
								{ value: '', label: 'All Resources' },
								{ value: 'admin_users', label: 'Users' },
								{ value: 'admin_sessions', label: 'Sessions' },
								{ value: 'system_config', label: 'Configuration' },
								{ value: 'domains', label: 'Domains' },
								{ value: 'crawl_jobs', label: 'Crawl Jobs' },
							]}
							value={filters.resource}
							onChange={value => setFilters(prev => ({ ...prev, resource: value || '' }))}
							clearable
						/>
					</Group>

					<Group gap="md">
						<Select
							placeholder="Filter by severity"
							data={[
								{ value: '', label: 'All Severities' },
								{ value: 'low', label: 'Low' },
								{ value: 'medium', label: 'Medium' },
								{ value: 'high', label: 'High' },
								{ value: 'critical', label: 'Critical' },
							]}
							value={filters.severity}
							onChange={value => setFilters(prev => ({ ...prev, severity: value || '' }))}
							clearable
						/>
						<TextInput
							placeholder="Start date (YYYY-MM-DD)"
							leftSection={<IconCalendar size={16} />}
							value={filters.startDate ? filters.startDate.toISOString().split('T')[0] : ''}
							onChange={(e) =>
							{
								const date = e.target.value ? new Date(e.target.value) : null;
								setFilters(prev => ({ ...prev, startDate: date }));
							}}
						/>
						<TextInput
							placeholder="End date (YYYY-MM-DD)"
							leftSection={<IconCalendar size={16} />}
							value={filters.endDate ? filters.endDate.toISOString().split('T')[0] : ''}
							onChange={(e) =>
							{
								const date = e.target.value ? new Date(e.target.value) : null;
								setFilters(prev => ({ ...prev, endDate: date }));
							}}
						/>
						<Button variant="light" onClick={clearFilters}>
							Clear Filters
						</Button>
					</Group>
				</Stack>
			</Paper>

			{/* Audit Logs Table */}
			<Paper p="md" withBorder>
				{loading ? (
					<Group justify="center" py="xl">
						<Loader />
					</Group>
				) : auditLogs.length === 0 ? (
					<Alert>
						No audit logs found matching the current filters.
					</Alert>
				) : (
					<>
						<ScrollArea>
							<Table striped highlightOnHover>
								<Table.Thead>
									<Table.Tr>
										<Table.Th>Timestamp</Table.Th>
										<Table.Th>User</Table.Th>
										<Table.Th>Action</Table.Th>
										<Table.Th>Resource</Table.Th>
										<Table.Th>Severity</Table.Th>
										<Table.Th>IP Address</Table.Th>
										<Table.Th>Status</Table.Th>
										<Table.Th>Actions</Table.Th>
									</Table.Tr>
								</Table.Thead>
								<Table.Tbody>
									{auditLogs.map(log => (
										<Table.Tr key={log.id}>
											<Table.Td>
												<Text size="sm">
													{new Date(log.timestamp).toLocaleString()}
												</Text>
											</Table.Td>
											<Table.Td>
												<div>
													<Text size="sm" fw={500}>{log.username}</Text>
													<Text size="xs" c="dimmed">{log.userId}</Text>
												</div>
											</Table.Td>
											<Table.Td>
												<Text size="sm">{log.action}</Text>
											</Table.Td>
											<Table.Td>
												<div>
													<Text size="sm">{log.resource}</Text>
													{log.resourceId && (
														<Text size="xs" c="dimmed">{log.resourceId}</Text>
													)}
												</div>
											</Table.Td>
											<Table.Td>
												{getSeverityBadge(log.severity)}
											</Table.Td>
											<Table.Td>
												<Text size="sm" ff="monospace">{log.ipAddress}</Text>
											</Table.Td>
											<Table.Td>
												<Badge color={log.success ? 'green' : 'red'} size="sm">
													{log.success ? 'Success' : 'Failed'}
												</Badge>
											</Table.Td>
											<Table.Td>
												<ActionIcon
													variant="subtle"
													onClick={() =>
													{
														setSelectedLog(log);
														setDetailsModalOpen(true);
													}}
												>
													<IconEye size={16} />
												</ActionIcon>
											</Table.Td>
										</Table.Tr>
									))}
								</Table.Tbody>
							</Table>
						</ScrollArea>

						{totalPages > 1 && (
							<Group justify="center" mt="md">
								<Pagination
									value={page}
									onChange={setPage}
									total={totalPages}
								/>
							</Group>
						)}
					</>
				)}
			</Paper>

			{/* Details Modal */}
			{selectedLog && (
				<Modal
					opened={detailsModalOpen}
					onClose={() =>
					{
						setDetailsModalOpen(false);
						setSelectedLog(null);
					}}
					title="Audit Log Details"
					size="lg"
					centered
				>
					<Stack gap="md">
						<Group>
							<Text fw={500}>Timestamp:</Text>
							<Text>{new Date(selectedLog.timestamp).toLocaleString()}</Text>
						</Group>

						<Group>
							<Text fw={500}>User:</Text>
							<Text>{selectedLog.username} ({selectedLog.userId})</Text>
						</Group>

						<Group>
							<Text fw={500}>Action:</Text>
							<Text>{selectedLog.action}</Text>
						</Group>

						<Group>
							<Text fw={500}>Resource:</Text>
							<Text>{selectedLog.resource}</Text>
							{selectedLog.resourceId && (
								<Text c="dimmed">({selectedLog.resourceId})</Text>
							)}
						</Group>

						<Group>
							<Text fw={500}>Severity:</Text>
							{getSeverityBadge(selectedLog.severity)}
						</Group>

						<Group>
							<Text fw={500}>IP Address:</Text>
							<Text ff="monospace">{selectedLog.ipAddress}</Text>
						</Group>

						<Group>
							<Text fw={500}>User Agent:</Text>
							<Text size="sm" c="dimmed">{selectedLog.userAgent}</Text>
						</Group>

						{selectedLog.oldValues && (
							<div>
								<Text fw={500} mb="xs">Old Values:</Text>
								<ScrollArea h={100}>
									<Text size="sm" ff="monospace" style={{ whiteSpace: 'pre-wrap' }}>
										{JSON.stringify(selectedLog.oldValues, null, 2)}
									</Text>
								</ScrollArea>
							</div>
						)}

						{selectedLog.newValues && (
							<div>
								<Text fw={500} mb="xs">New Values:</Text>
								<ScrollArea h={100}>
									<Text size="sm" ff="monospace" style={{ whiteSpace: 'pre-wrap' }}>
										{JSON.stringify(selectedLog.newValues, null, 2)}
									</Text>
								</ScrollArea>
							</div>
						)}

						{selectedLog.errorMessage && (
							<div>
								<Text fw={500} mb="xs">Error Message:</Text>
								<Text size="sm" c="red">{selectedLog.errorMessage}</Text>
							</div>
						)}
					</Stack>
				</Modal>
			)}
		</Stack>
	);
}

export default AuditLogViewer;
