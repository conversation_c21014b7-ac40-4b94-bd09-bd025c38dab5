'use client';

import {
	Modal,
	TextInput,
	Select,
	Button,
	Group,
	Stack,
	Switch,
	MultiSelect,
	Divider,
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { clientLogger } from '@/lib/logger';
import { notifications } from '@mantine/notifications';
import { useState, useEffect } from 'react';

import { ROLE_PERMISSIONS } from '@/types/auth';
import type { AdminUserType, UpdateUserRequestType, PermissionType, UserRoleType } from '@/types/auth';

interface UserEditModalProps
{
	opened: boolean;
	onClose: () => void;
	onSuccess: () => void;
	user: AdminUserType;
}

const PERMISSION_OPTIONS = [
	{ value: 'users.view', label: 'View Users' },
	{ value: 'users.create', label: 'Create Users' },
	{ value: 'users.edit', label: 'Edit Users' },
	{ value: 'users.delete', label: 'Delete Users' },
	{ value: 'users.manage_permissions', label: 'Manage Permissions' },
	{ value: 'services.view', label: 'View Services' },
	{ value: 'services.restart', label: 'Restart Services' },
	{ value: 'services.configure', label: 'Configure Services' },
	{ value: 'domains.view', label: 'View Domains' },
	{ value: 'domains.edit', label: 'Edit Domains' },
	{ value: 'domains.delete', label: 'Delete Domains' },
	{ value: 'domains.bulk_operations', label: 'Bulk Domain Operations' },
	{ value: 'crawl.view', label: 'View Crawl Jobs' },
	{ value: 'crawl.create', label: 'Create Crawl Jobs' },
	{ value: 'crawl.manage', label: 'Manage Crawl Jobs' },
	{ value: 'crawl.configure', label: 'Configure Crawler' },
	{ value: 'seeder.view', label: 'View Seeder' },
	{ value: 'seeder.manage', label: 'Manage Seeder' },
	{ value: 'seeder.configure', label: 'Configure Seeder' },
	{ value: 'analytics.view', label: 'View Analytics' },
	{ value: 'analytics.export', label: 'Export Analytics' },
	{ value: 'config.view', label: 'View Configuration' },
	{ value: 'config.edit', label: 'Edit Configuration' },
	{ value: 'config.deploy', label: 'Deploy Configuration' },
	{ value: 'logs.view', label: 'View Logs' },
	{ value: 'logs.export', label: 'Export Logs' },
	{ value: 'ai.view', label: 'View AI Services' },
	{ value: 'ai.configure', label: 'Configure AI Services' },
	{ value: 'ai.manage', label: 'Manage AI Services' },
];

export function UserEditModal({
	opened, onClose, onSuccess, user,
}: UserEditModalProps)
{
	const [loading, setLoading] = useState(false);

	const form = useForm<UpdateUserRequestType>({
		initialValues: {
			role: user.role,
			permissions: user.permissions,
			isActive: user.isActive,
			email: user.email || '',
			fullName: user.fullName || '',
			mustChangePassword: user.mustChangePassword,
		},
		validate: {
			role: value => (!value ? 'Role is required' : null),
			email: (value) =>
			{
				if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) return 'Invalid email format';
				return null;
			},
		},
	});

	// Reset form when user changes
	useEffect(() =>
	{
		form.setValues({
			role: user.role,
			permissions: user.permissions,
			isActive: user.isActive,
			email: user.email || '',
			fullName: user.fullName || '',
			mustChangePassword: user.mustChangePassword,
		});
	}, [user]);

	const handleRoleChange = (role: string) =>
	{
		const roleValue = (role as UserRoleType) || 'viewer';
		form.setFieldValue('role', roleValue);
		// Auto-set permissions based on role
		const rolePermissions = ROLE_PERMISSIONS[roleValue] || [];
		form.setFieldValue('permissions', rolePermissions);
	};

	const handleSubmit = async (values: UpdateUserRequestType) =>
	{
		try
		{
			setLoading(true);

			const response = await fetch(`/api/users/${user.id}`, {
				method: 'PUT',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(values),
			});

			const data = await response.json();

			if (!response.ok)
			{
				throw new Error(data.error || 'Failed to update user');
			}

			notifications.show({
				title: 'Success',
				message: 'User updated successfully',
				color: 'green',
			});

			onSuccess();
		}
		catch (error: any)
		{
			clientLogger.error('Error updating user:', error);
			notifications.show({
				title: 'Error',
				message: error.message || 'Failed to update user',
				color: 'red',
			});
		}
		finally
		{
			setLoading(false);
		}
	};

	const handleClose = () =>
	{
		form.reset();
		onClose();
	};

	return (
		<Modal
			opened={opened}
			onClose={handleClose}
			title={`Edit User: ${user.username}`}
			size="lg"
			centered
		>
			<form onSubmit={form.onSubmit(handleSubmit)}>
				<Stack gap="md">
					<Group grow>
						<TextInput
							label="Username"
							value={user.username}
							disabled
							description="Username cannot be changed"
						/>
						<TextInput
							label="Full Name"
							placeholder="Enter full name"
							{...form.getInputProps('fullName')}
						/>
					</Group>

					<TextInput
						label="Email"
						placeholder="Enter email address"
						type="email"
						{...form.getInputProps('email')}
					/>

					<Divider label="Role & Permissions" labelPosition="left" />

					<Select
						label="Role"
						placeholder="Select role"
						required
						data={[
							{ value: 'viewer', label: 'Viewer - Read-only access' },
							{ value: 'admin', label: 'Admin - Limited configuration access' },
							{ value: 'super_admin', label: 'Super Admin - Full access' },
						]}
						value={form.values.role}
						onChange={value => handleRoleChange(value || 'viewer')}
					/>

					<MultiSelect
						label="Custom Permissions"
						placeholder="Select additional permissions"
						data={PERMISSION_OPTIONS}
						value={form.values.permissions}
						onChange={value => form.setFieldValue('permissions', value as PermissionType[])}
						searchable
						clearable
						description="Role-based permissions are automatically selected. You can add or remove specific permissions here."
					/>

					<Divider label="Account Settings" labelPosition="left" />

					<Switch
						label="Account Active"
						description="Inactive users cannot log in"
						checked={form.values.isActive}
						onChange={event => form.setFieldValue('isActive', event.currentTarget.checked)}
					/>

					<Switch
						label="Require password change on next login"
						description="User will be forced to change password on their next login"
						checked={form.values.mustChangePassword}
						onChange={event => form.setFieldValue('mustChangePassword', event.currentTarget.checked)}
					/>

					<Group justify="flex-end" mt="md">
						<Button variant="default" onClick={handleClose}>
							Cancel
						</Button>
						<Button type="submit" loading={loading}>
							Update User
						</Button>
					</Group>
				</Stack>
			</form>
		</Modal>
	);
}
