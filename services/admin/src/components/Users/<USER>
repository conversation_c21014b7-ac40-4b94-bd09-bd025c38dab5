'use client';

import {
	<PERSON><PERSON>,
	Button,
	Group,
	Stack,
	Alert,
	Text,
	PasswordInput,
	Radio,
	Code,
	Divider,
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { clientLogger } from '@/lib/logger';
import { notifications } from '@mantine/notifications';
import { <PERSON>con<PERSON>ey, IconAlertTriangle } from '@tabler/icons-react';
import { useState } from 'react';

import type { AdminUserType } from '@/types/auth';

interface UserPasswordModalProps
{
	opened: boolean;
	onClose: () => void;
	onSuccess: () => void;
	user: AdminUserType;
}

export function UserPasswordModal({
	opened, onClose, onSuccess, user,
}: UserPasswordModalProps)
{
	const [loading, setLoading] = useState(false);
	const [generatedPassword, setGeneratedPassword] = useState<string | null>(null);

	const form = useForm({
		initialValues: {
			action: 'generate',
			newPassword: '',
		},
		validate: {
			newPassword: (value, values) =>
			{
				if (values.action === 'set' && !value) return 'Password is required';
				if (values.action === 'set' && value && value.length < 8) return 'Password must be at least 8 characters';
				return null;
			},
		},
	});

	const handleSubmit = async (values: { action: string; newPassword: string }) =>
	{
		try
		{
			setLoading(true);
			setGeneratedPassword(null);

			const response = await fetch(`/api/users/${user.id}/password`, {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(values),
			});

			const data = await response.json();

			if (!response.ok)
			{
				throw new Error(data.error || 'Failed to reset password');
			}

			if (data.generatedPassword)
			{
				setGeneratedPassword(data.generatedPassword);
				notifications.show({
					title: 'Password Reset',
					message: 'Password reset successfully. Please save the generated password.',
					color: 'green',
				});
			}
			else
			{
				notifications.show({
					title: 'Success',
					message: 'Password reset successfully',
					color: 'green',
				});
				onSuccess();
			}
		}
		catch (error: any)
		{
			clientLogger.error('Error resetting password:', error);
			notifications.show({
				title: 'Error',
				message: error.message || 'Failed to reset password',
				color: 'red',
			});
		}
		finally
		{
			setLoading(false);
		}
	};

	const handleClose = () =>
	{
		if (generatedPassword)
		{
			onSuccess();
		}
		form.reset();
		setGeneratedPassword(null);
		onClose();
	};

	return (
		<Modal
			opened={opened}
			onClose={handleClose}
			title={`Reset Password: ${user.username}`}
			size="md"
			centered
		>
			{generatedPassword ? (
				<Stack gap="md">
					<Alert color="green" icon={<IconKey size={16} />}>
						<Text fw={500} mb="xs">Password reset successfully!</Text>
						<Text size="sm" mb="md">
							The user's password has been reset. Please provide this password to the user securely
							as it will not be shown again. The user will be required to change this password on their next login.
						</Text>
						<Code block>{generatedPassword}</Code>
					</Alert>
					<Group justify="flex-end">
						<Button onClick={handleClose}>
							Close
						</Button>
					</Group>
				</Stack>
			) : (
				<form onSubmit={form.onSubmit(handleSubmit)}>
					<Stack gap="md">
						<Alert color="orange" icon={<IconAlertTriangle size={16} />}>
							<Text size="sm">
								Resetting the password will:
							</Text>
							<ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
								<li>Terminate all active sessions for this user</li>
								<li>Force the user to log in again</li>
								<li>Require the user to change the password on next login</li>
							</ul>
						</Alert>

						<Divider label="Password Reset Options" labelPosition="left" />

						<Radio.Group
							value={form.values.action}
							onChange={value => form.setFieldValue('action', value)}
							label="Choose reset method"
							required
						>
							<Stack gap="xs" mt="xs">
								<Radio
									value="generate"
									label="Generate secure password automatically"
									description="A secure password will be generated and shown after reset"
								/>
								<Radio
									value="set"
									label="Set custom password"
									description="Specify a custom password for the user"
								/>
							</Stack>
						</Radio.Group>

						{form.values.action === 'set' && (
							<PasswordInput
								label="New Password"
								placeholder="Enter new password"
								required
								description="Password must be at least 8 characters long"
								{...form.getInputProps('newPassword')}
							/>
						)}

						<Group justify="flex-end" mt="md">
							<Button variant="default" onClick={handleClose}>
								Cancel
							</Button>
							<Button type="submit" loading={loading} color="orange">
								Reset Password
							</Button>
						</Group>
					</Stack>
				</form>
			)}
		</Modal>
	);
}
