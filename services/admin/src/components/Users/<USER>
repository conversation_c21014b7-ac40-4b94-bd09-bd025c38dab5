# User Management System

This directory contains the comprehensive user management and role-based access control system for the Domain Ranking Admin Panel.

## Overview

The user management system provides:

- **User Account Management**: Create, edit, delete, and manage administrator accounts
- **Role-Based Access Control (RBAC)**: Three-tier permission system with granular controls
- **Session Management**: Monitor and control user sessions across devices
- **Password Security**: Enforced password policies with history tracking
- **Audit Logging**: Complete audit trail of all administrative actions
- **Security Monitoring**: Real-time security alerts and suspicious activity detection
- **User Analytics**: Comprehensive reporting on user activity and system usage

## Components

### Core Components

- **`UserManagementInterface.tsx`**: Main user management dashboard with search, filtering, and bulk operations
- **`UserCreateModal.tsx`**: Modal for creating new users with role assignment and permission customization
- **`UserEditModal.tsx`**: Modal for editing existing user accounts and permissions
- **`UserDetailsModal.tsx`**: Detailed view of user information, sessions, and activity history
- **`UserPasswordModal.tsx`**: Password reset functionality with secure password generation

### Analytics & Monitoring

- **`UserAnalytics.tsx`**: User analytics dashboard with charts and metrics
- **`AuditLogViewer.tsx`**: Comprehensive audit log viewer with filtering and export capabilities

## Features

### User Account Management

#### User Creation

- Username validation and uniqueness checking
- Secure password generation or custom password setting
- Role assignment with automatic permission mapping
- Custom permission overrides
- Email and full name support
- Force password change on first login option

#### User Editing

- Role changes with permission updates
- Account activation/deactivation
- Profile information updates
- Permission customization
- Password reset requirements

#### User Details

- Complete user profile information
- Active session monitoring with device details
- Recent activity history
- Permission overview
- Account status and security information

### Role-Based Access Control

#### Roles

1. **Super Admin** (`super_admin`)

   - Full system access
   - User management capabilities
   - System configuration access
   - All permissions granted

2. **Admin** (`admin`)

   - Limited administrative access
   - Cannot manage users or deploy configurations
   - Most operational permissions

3. **Viewer** (`viewer`)
   - Read-only access to system information
   - Cannot modify any system settings
   - Basic monitoring capabilities

#### Permissions

The system uses granular permissions organized by feature area:

- **Users**: `users.view`, `users.create`, `users.edit`, `users.delete`, `users.manage_permissions`
- **Services**: `services.view`, `services.restart`, `services.configure`
- **Domains**: `domains.view`, `domains.edit`, `domains.delete`, `domains.bulk_operations`
- **Crawling**: `crawl.view`, `crawl.create`, `crawl.manage`, `crawl.configure`
- **Seeder**: `seeder.view`, `seeder.manage`, `seeder.configure`
- **Analytics**: `analytics.view`, `analytics.export`
- **Configuration**: `config.view`, `config.edit`, `config.deploy`
- **Logs**: `logs.view`, `logs.export`
- **AI Services**: `ai.view`, `ai.configure`, `ai.manage`

### Session Management

#### Features

- Real-time session monitoring
- Device and browser identification
- IP address tracking
- Session termination (individual or all)
- Concurrent session limits
- Automatic session expiration

#### Security

- Session hijacking protection
- IP address validation
- Device fingerprinting
- Suspicious activity detection

### Password Security

#### Password Policy

- Minimum length: 12 characters
- Required character types: uppercase, lowercase, numbers, special characters
- Password history: 5 previous passwords remembered
- Maximum age: 90 days
- Account lockout: 5 failed attempts, 30-minute lockout

#### Features

- Secure password generation
- Password strength validation
- Password expiration tracking
- Breach detection (common password patterns)
- Password history enforcement

### Audit Logging

#### Tracked Events

- User creation, modification, deletion
- Permission changes
- Login/logout events
- Password resets
- Session management
- Configuration changes
- Security events

#### Log Details

- User identification
- Action performed
- Resource affected
- Timestamp
- IP address and user agent
- Before/after values for changes
- Success/failure status
- Severity level

### Security Monitoring

#### Alert Types

- **Failed Login**: Multiple failed login attempts
- **Account Locked**: Account lockout events
- **Permission Escalation**: Role or permission changes
- **Suspicious Activity**: Unusual access patterns
- **Password Breach**: Weak or compromised passwords

#### Alert Severity

- **Low**: Informational events
- **Medium**: Standard security events
- **High**: Potential security issues
- **Critical**: Immediate security threats

### User Analytics

#### Metrics Tracked

- Total users by role
- Active vs inactive users
- Login statistics and trends
- Session duration analytics
- Feature usage patterns
- Security metrics
- Failed login attempts
- Password expiration tracking

#### Reporting

- Real-time dashboards
- Historical trend analysis
- Export capabilities (CSV, JSON)
- Custom date ranges
- Drill-down capabilities

## API Endpoints

### User Management

- `GET /api/users` - List users with filtering and pagination
- `POST /api/users` - Create new user
- `GET /api/users/[userId]` - Get user details
- `PUT /api/users/[userId]` - Update user
- `DELETE /api/users/[userId]` - Delete user

### Session Management

- `GET /api/users/[userId]/sessions` - Get user sessions
- `DELETE /api/users/[userId]/sessions` - Terminate sessions

### Password Management

- `POST /api/users/[userId]/password` - Reset password
- `PUT /api/users/[userId]/password` - Unlock account

### Analytics & Audit

- `GET /api/users/analytics` - Get user analytics
- `GET /api/users/audit` - Get audit logs
- `POST /api/users/audit` - Export audit logs

## Database Schema

### Tables

- **`admin_users`**: User account information
- **`admin_sessions`**: Active user sessions
- **`user_activity`**: User activity log
- **`audit_logs`**: Comprehensive audit trail
- **`security_alerts`**: Security monitoring alerts
- **`password_policies`**: Password policy configuration
- **`permission_templates`**: Reusable permission sets

### Indexes

Optimized indexes for:

- User lookups by username and role
- Session queries by user and activity
- Activity logs by user and timestamp
- Audit logs by action and resource
- Security alerts by type and severity

## Security Considerations

### Authentication

- Secure session management with iron-session
- CSRF protection
- Session timeout enforcement
- Concurrent session limits

### Authorization

- Permission-based access control
- Resource-level permissions
- Operation-specific controls
- Privilege escalation prevention

### Data Protection

- Password hashing with bcrypt
- Sensitive data encryption
- Audit trail integrity
- Secure password generation

### Monitoring

- Real-time security alerts
- Suspicious activity detection
- Failed login tracking
- Account lockout protection

## Usage Examples

### Creating a User

```typescript
const newUser = {
  username: "john.doe",
  role: "admin",
  permissions: ["domains.view", "domains.edit", "crawl.view"],
  email: "<EMAIL>",
  fullName: "John Doe",
  generatePassword: true,
  mustChangePassword: true,
};

const response = await fetch("/api/users", {
  method: "POST",
  headers: { "Content-Type": "application/json" },
  body: JSON.stringify(newUser),
});
```

### Checking Permissions

```typescript
const hasPermission = (userPermissions: string[], required: string) => {
  return userPermissions.includes(required);
};

if (hasPermission(user.permissions, "users.create")) {
  // Allow user creation
}
```

### Monitoring User Activity

```typescript
const analytics = await fetch("/api/users/analytics?timeframe=30d");
const data = await analytics.json();

console.log(`Active users: ${data.analytics.activeUsers}`);
console.log(`Failed logins: ${data.analytics.loginStats.failedLogins}`);
```

## Installation & Setup

1. **Install Dependencies**

   ```bash
   cd services/admin
   pnpm install
   ```

2. **Initialize Database**

   ```bash
   pnpm run init:users
   ```

3. **Start Development Server**

   ```bash
   pnpm run dev
   ```

4. **Login with Default Credentials**
   - Username: `admin`
   - Password: `AdminPassword123!`
   - **Important**: Change this password immediately after first login

## Best Practices

### User Management

- Use strong, unique passwords
- Assign minimal required permissions
- Regularly review user accounts and permissions
- Monitor user activity for anomalies
- Implement regular password rotation

### Security

- Enable account lockout policies
- Monitor failed login attempts
- Review audit logs regularly
- Investigate security alerts promptly
- Keep user permissions up to date

### Maintenance

- Regular database cleanup of old sessions
- Archive old audit logs
- Update password policies as needed
- Review and update permission templates
- Monitor system performance

## Troubleshooting

### Common Issues

1. **User Cannot Login**

   - Check if account is active
   - Verify account is not locked
   - Check password expiration
   - Review recent security alerts

2. **Permission Denied Errors**

   - Verify user has required permissions
   - Check role assignments
   - Review permission inheritance
   - Check for recent permission changes

3. **Session Issues**
   - Check session expiration settings
   - Verify session storage (Redis)
   - Review concurrent session limits
   - Check for session hijacking alerts

### Debugging

- Enable debug logging in development
- Check browser console for client-side errors
- Review server logs for API errors
- Use audit logs to trace user actions
- Monitor database performance

## Contributing

When adding new features to the user management system:

1. Follow the existing component structure
2. Add appropriate permissions for new features
3. Include audit logging for all actions
4. Add security monitoring where appropriate
5. Update documentation and types
6. Include comprehensive error handling
7. Add appropriate tests

## License

This user management system is part of the Domain Ranking Admin Panel and follows the same licensing terms as the main project.
