'use client';

import {
	Paper,
	Title,
	Group,
	Select,
	Grid,
	Text,
	Card,
	Badge,
	Stack,
	Loader,
	Alert,
	Progress,
} from '@mantine/core';
import {
	IconUsers,
	IconUserCheck,
	IconUserX,
	IconLogin,
	IconShield,
	IconActivity,
} from '@tabler/icons-react';
import { useState, useEffect } from 'react';
import { clientLogger } from '@/lib/logger';
import {
	LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, BarChart, Bar,
} from 'recharts';

import type { UserAnalyticsType } from '@/types/auth';

type UserAnalyticsPropsType = Record<string, never>;

const COLORS = ['#228be6', '#40c057', '#fd7e14', '#e03131', '#7c2d12'];

type TrendDataType =
{
	date: string;
	logins: number;
	uniqueUsers: number;
};

function UserAnalytics({}: UserAnalyticsPropsType)
{
	const [analytics, setAnalytics] = useState<UserAnalyticsType | null>(null);
	const [trends, setTrends] = useState<TrendDataType[]>([]);
	const [loading, setLoading] = useState(true);
	const [timeframe, setTimeframe] = useState('30d');

	const fetchAnalytics = async () =>
	{
		try
		{
			setLoading(true);
			const response = await fetch(`/api/users/analytics?timeframe=${timeframe}`);
			if (!response.ok) throw new Error('Failed to fetch analytics');

			const data = await response.json();
			setAnalytics(data.analytics);
			setTrends(data.trends);
		}
		catch (error)
		{
			clientLogger.error('Error fetching user analytics:', error);
		}
		finally
		{
			setLoading(false);
		}
	};

	useEffect(() =>
	{
		fetchAnalytics();
	}, [timeframe]);

	if (loading)
	{
		return (
			<Paper p="md" withBorder>
				<Group justify="center" py="xl">
					<Loader />
				</Group>
			</Paper>
		);
	}

	if (!analytics)
	{
		return (
			<Alert color="red">
				Failed to load user analytics
			</Alert>
		);
	}

	const roleData = Object.entries(analytics.usersByRole).map(([role, count]) => ({
		name: role.replace('_', ' ').toUpperCase(),
		value: count,
	}));

	const featureUsageData = Object.entries(analytics.featureUsage)
		.sort(([, a], [, b]) => b - a)
		.slice(0, 10)
		.map(([feature, count]) => ({
			name: feature.replace('_', ' ').toUpperCase(),
			usage: count,
		}));

	const loginTrendsData = trends.map(trend => ({
		date: new Date(trend.date).toLocaleDateString(),
		logins: trend.logins,
		uniqueUsers: trend.uniqueUsers,
	}));

	return (
		<Stack gap="md">
			<Group justify="space-between">
				<Title order={3}>User Analytics</Title>
				<Select
					value={timeframe}
					onChange={value => setTimeframe(value || '30d')}
					data={[
						{ value: '7d', label: 'Last 7 days' },
						{ value: '30d', label: 'Last 30 days' },
						{ value: '90d', label: 'Last 90 days' },
					]}
				/>
			</Group>

			{/* Overview Cards */}
			<Grid>
				<Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
					<Card withBorder>
						<Group justify="space-between">
							<div>
								<Text size="sm" c="dimmed">Total Users</Text>
								<Text size="xl" fw={700}>{analytics.totalUsers}</Text>
							</div>
							<IconUsers size={32} color="var(--mantine-color-blue-6)" />
						</Group>
					</Card>
				</Grid.Col>

				<Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
					<Card withBorder>
						<Group justify="space-between">
							<div>
								<Text size="sm" c="dimmed">Active Users</Text>
								<Text size="xl" fw={700}>{analytics.activeUsers}</Text>
								<Progress
									value={(analytics.activeUsers / analytics.totalUsers) * 100}
									size="sm"
									color="green"
									mt="xs"
								/>
							</div>
							<IconUserCheck size={32} color="var(--mantine-color-green-6)" />
						</Group>
					</Card>
				</Grid.Col>

				<Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
					<Card withBorder>
						<Group justify="space-between">
							<div>
								<Text size="sm" c="dimmed">Locked Users</Text>
								<Text size="xl" fw={700}>{analytics.lockedUsers}</Text>
								{analytics.lockedUsers > 0 && (
									<Badge color="red" size="sm" mt="xs">
										Requires Attention
									</Badge>
								)}
							</div>
							<IconUserX size={32} color="var(--mantine-color-red-6)" />
						</Group>
					</Card>
				</Grid.Col>

				<Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
					<Card withBorder>
						<Group justify="space-between">
							<div>
								<Text size="sm" c="dimmed">Total Logins</Text>
								<Text size="xl" fw={700}>{analytics.loginStats.totalLogins}</Text>
								<Text size="sm" c="dimmed">
									{analytics.loginStats.uniqueUsers} unique users
								</Text>
							</div>
							<IconLogin size={32} color="var(--mantine-color-blue-6)" />
						</Group>
					</Card>
				</Grid.Col>
			</Grid>

			{/* Charts */}
			<Grid>
				<Grid.Col span={{ base: 12, md: 8 }}>
					<Paper p="md" withBorder>
						<Title order={4} mb="md">Login Trends</Title>
						<ResponsiveContainer width="100%" height={300}>
							<LineChart data={loginTrendsData}>
								<CartesianGrid strokeDasharray="3 3" />
								<XAxis dataKey="date" />
								<YAxis />
								<Tooltip />
								<Line
									type="monotone"
									dataKey="logins"
									stroke="var(--mantine-color-blue-6)"
									strokeWidth={2}
									name="Total Logins"
								/>
								<Line
									type="monotone"
									dataKey="uniqueUsers"
									stroke="var(--mantine-color-green-6)"
									strokeWidth={2}
									name="Unique Users"
								/>
							</LineChart>
						</ResponsiveContainer>
					</Paper>
				</Grid.Col>

				<Grid.Col span={{ base: 12, md: 4 }}>
					<Paper p="md" withBorder>
						<Title order={4} mb="md">Users by Role</Title>
						<ResponsiveContainer width="100%" height={300}>
							<PieChart>
								<Pie
									data={roleData}
									cx="50%"
									cy="50%"
									labelLine={false}
									label={({ name, value }) => `${name}: ${value}`}
									outerRadius={80}
									fill="#8884d8"
									dataKey="value"
								>
									{roleData.map((entry, index) => (
										<Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
									))}
								</Pie>
								<Tooltip />
							</PieChart>
						</ResponsiveContainer>
					</Paper>
				</Grid.Col>
			</Grid>

			{/* Security Metrics */}
			<Paper p="md" withBorder>
				<Title order={4} mb="md">Security Metrics</Title>
				<Grid>
					<Grid.Col span={{ base: 12, sm: 4 }}>
						<Card withBorder>
							<Group justify="space-between">
								<div>
									<Text size="sm" c="dimmed">Password Expired</Text>
									<Text size="lg" fw={600}>{analytics.securityMetrics.passwordExpired}</Text>
								</div>
								<IconShield size={24} color="var(--mantine-color-yellow-6)" />
							</Group>
						</Card>
					</Grid.Col>

					<Grid.Col span={{ base: 12, sm: 4 }}>
						<Card withBorder>
							<Group justify="space-between">
								<div>
									<Text size="sm" c="dimmed">Failed Logins</Text>
									<Text size="lg" fw={600}>{analytics.loginStats.failedLogins}</Text>
								</div>
								<IconUserX size={24} color="var(--mantine-color-red-6)" />
							</Group>
						</Card>
					</Grid.Col>

					<Grid.Col span={{ base: 12, sm: 4 }}>
						<Card withBorder>
							<Group justify="space-between">
								<div>
									<Text size="sm" c="dimmed">Suspicious Activity</Text>
									<Text size="lg" fw={600}>{analytics.securityMetrics.suspiciousActivity}</Text>
								</div>
								<IconActivity size={24} color="var(--mantine-color-orange-6)" />
							</Group>
						</Card>
					</Grid.Col>
				</Grid>
			</Paper>

			{/* Feature Usage */}
			<Paper p="md" withBorder>
				<Title order={4} mb="md">Feature Usage (Top 10)</Title>
				<ResponsiveContainer width="100%" height={400}>
					<BarChart data={featureUsageData} layout="horizontal">
						<CartesianGrid strokeDasharray="3 3" />
						<XAxis type="number" />
						<YAxis dataKey="name" type="category" width={100} />
						<Tooltip />
						<Bar dataKey="usage" fill="var(--mantine-color-blue-6)" />
					</BarChart>
				</ResponsiveContainer>
			</Paper>

			{/* Session Statistics */}
			<Paper p="md" withBorder>
				<Title order={4} mb="md">Session Statistics</Title>
				<Grid>
					<Grid.Col span={{ base: 12, sm: 6 }}>
						<Stack gap="xs">
							<Group justify="space-between">
								<Text size="sm">Average Session Duration</Text>
								<Text size="sm" fw={500}>
									{Math.round(analytics.loginStats.averageSessionDuration)} minutes
								</Text>
							</Group>
							<Group justify="space-between">
								<Text size="sm">Success Rate</Text>
								<Text size="sm" fw={500}>
									{analytics.loginStats.totalLogins > 0
										? Math.round(((analytics.loginStats.totalLogins - analytics.loginStats.failedLogins) / analytics.loginStats.totalLogins) * 100)
										: 0}%
								</Text>
							</Group>
						</Stack>
					</Grid.Col>
				</Grid>
			</Paper>
		</Stack>
	);
}

export default UserAnalytics;
