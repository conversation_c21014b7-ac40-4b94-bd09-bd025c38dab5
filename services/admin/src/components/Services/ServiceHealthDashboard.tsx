'use client';

import {
	Container,
	Title,
	Grid,
	Paper,
	Text,
	Badge,
	Group,
	Stack,
	ActionIcon,
	Tooltip,
	Alert,
	LoadingOverlay,
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { clientLogger } from '@/lib/logger';
import {
	IconRefresh,
	IconAlertTriangle,
	IconCheck,
	IconX,
	IconServer,
	IconActivity,
} from '@tabler/icons-react';
import { useEffect, useState, useCallback } from 'react';

import { RealtimeIndicator } from '@/components/Realtime/RealtimeIndicator';
import { useRealtimeSubscription } from '@/hooks/useRealtimeSubscription';
import useServiceAlerts from '@/hooks/useServiceAlerts';
import type { ServiceHealthUpdateType, DatabaseHealthUpdateType } from '@/lib/realtime/types';
import { ServiceStatusCard } from './ServiceStatusCard';

interface ServiceMetricsType
{
	cpu: number;
	memory: number;
	requests: number;
	errors: number;
}

interface ServiceDependencyType
{
	name: string;
	status: 'connected' | 'disconnected' | 'error';
	responseTime: number;
}

interface ServiceStatusType
{
	name: string;
	status: 'healthy' | 'unhealthy' | 'degraded';
	uptime: number;
	responseTime: number;
	lastCheck: Date;
	version: string;
	metrics: ServiceMetricsType;
	dependencies?: ServiceDependencyType[];
}

interface ServiceHealthSummaryType
{
	total: number;
	healthy: number;
	degraded: number;
	unhealthy: number;
}

interface DatabaseStatusType
{
	name: 'scylla' | 'mariadb' | 'redis' | 'manticore';
	connected: boolean;
	responseTime: number;
	connectionPool: {
		active: number;
		idle: number;
		total: number;
	};
	metrics: {
		queries: number;
		errors: number;
		slowQueries: number;
	};
}

interface ServiceHealthDataType
{
	services: ServiceStatusType[];
	databases?: DatabaseStatusType[];
	timestamp: Date;
	summary: ServiceHealthSummaryType;
}

function ServiceHealthDashboard()
{
	const [healthData, setHealthData] = useState<ServiceHealthDataType | null>(null);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
	const [realtimeUpdates, setRealtimeUpdates] = useState(0);

	const getDependentServices = (databaseName: string): string[] =>
	{
		const dependencies: Record<string, string[]> = {
			scylla: ['web-app', 'worker', 'domain-seeder'],
			mariadb: ['worker', 'domain-seeder'],
			redis: ['web-app', 'worker', 'domain-seeder'],
			manticore: ['web-app'],
		};

		return dependencies[databaseName] || [];
	};

	// Set up service alerts
	useServiceAlerts(healthData, {
		enableNotifications: true,
		cpuThreshold: 80,
		memoryThreshold: 80,
		responseTimeThreshold: 5000,
	});

	// Real-time service health updates
	const { messageCount: serviceMessageCount } = useRealtimeSubscription<ServiceHealthUpdateType>({
		type: 'service_health',
		onMessage: (message) =>
		{
			const update = message.data as ServiceHealthUpdateType['data'];
			setHealthData((prev) =>
			{
				if (!prev) return prev;

				const updatedServices = prev.services.map(service => (service.name === update.serviceName
					? update.status
					: service));

				// Recalculate summary
				const summary = {
					total: updatedServices.length,
					healthy: updatedServices.filter(s => s.status === 'healthy').length,
					degraded: updatedServices.filter(s => s.status === 'degraded').length,
					unhealthy: updatedServices.filter(s => s.status === 'unhealthy').length,
				};

				return {
					...prev,
					services: updatedServices,
					summary,
					timestamp: new Date(),
				};
			});
			setRealtimeUpdates(prev => prev + 1);
			setLastUpdate(new Date());
		},
	});

	// Real-time database health updates
	const { messageCount: databaseMessageCount } = useRealtimeSubscription<DatabaseHealthUpdateType>({
		type: 'database_health',
		onMessage: (message) =>
		{
			const update = message.data as DatabaseHealthUpdateType['data'];
			setHealthData((prev) =>
			{
				if (!prev || !prev.databases) return prev;

				const updatedDatabases = prev.databases.map(database => (database.name === update.databaseName
					? update.status
					: database));

				return {
					...prev,
					databases: updatedDatabases,
					timestamp: new Date(),
				};
			});
			setRealtimeUpdates(prev => prev + 1);
			setLastUpdate(new Date());
		},
	});

	const fetchHealthData = useCallback(async () =>
	{
		try
		{
			const [servicesResponse, databaseResponse] = await Promise.all([
				fetch('/api/services/health'),
				fetch('/api/database/health'),
			]);

			if (!servicesResponse.ok)
			{
				throw new Error('Failed to fetch service health data');
			}

			const servicesData = await servicesResponse.json();
			let databaseData = null;

			if (databaseResponse.ok)
			{
				databaseData = await databaseResponse.json();
			}

			setHealthData({
				...servicesData,
				databases: databaseData?.databases || [],
				timestamp: new Date(servicesData.timestamp),
				services: servicesData.services.map((service: ServiceStatusType) => ({
					...service,
					lastCheck: new Date(service.lastCheck),
				})),
			});
			setLastUpdate(new Date());
			setError(null);
		}
		catch (err)
		{
			setError(err instanceof Error ? err.message : 'Unknown error occurred');
			notifications.show({
				title: 'Health Check Failed',
				message: 'Failed to fetch service health data',
				color: 'red',
				icon: <IconX size={16} />,
			});
		}
		finally
		{
			setLoading(false);
		}
	}, []);

	const handleServiceRestart = async (serviceName: string, force: boolean) =>
	{
		try
		{
			const response = await fetch('/api/services/restart', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ serviceName, force }),
			});

			if (!response.ok)
			{
				throw new Error('Failed to restart service');
			}

			// Refresh health data after restart
			setTimeout(() => fetchHealthData(), 2000);
		}
		catch (err)
		{
			throw new Error(err instanceof Error ? err.message : 'Restart failed');
		}
	};

	// Set up SSE connection for real-time updates
	useEffect(() =>
	{
		let eventSource: EventSource | null = null;

		// Real-time updates disabled - use polling instead
		const fetchHealthData = async () => {
			try {
				const response = await fetch('/api/services/health');
				if (!response.ok) {
					throw new Error(`HTTP ${response.status}`);
				}
				const data = await response.json();

				setHealthData({
					...data,
					timestamp: new Date(data.timestamp),
					services: data.services.map((service: ServiceStatusType) => ({
						...service,
						lastCheck: new Date(service.lastCheck),
					})),
				});
				setLastUpdate(new Date());
				setError(null);
				setLoading(false);
			} catch (err) {
				setError('Failed to fetch health data');
				clientLogger.error('Error fetching health data:', err);
			}
		};

		// Initial fetch
		fetchHealthData();

		// Poll every 10 seconds instead of real-time updates
		const interval = setInterval(fetchHealthData, 10000);

		return () => {
			clearInterval(interval);
		};
	}, []);

	// Initial data fetch
	useEffect(() =>
	{
		fetchHealthData();
	}, [fetchHealthData]);

	const getOverallStatus = () =>
	{
		if (!healthData) return 'unknown';

		const { summary } = healthData;
		if (summary.unhealthy > 0) return 'critical';
		if (summary.degraded > 0) return 'warning';
		if (summary.healthy === summary.total) return 'healthy';
		return 'unknown';
	};

	const getOverallStatusColor = () =>
	{
		switch (getOverallStatus())
		{
			case 'healthy':
				return 'green';
			case 'warning':
				return 'yellow';
			case 'critical':
				return 'red';
			default:
				return 'gray';
		}
	};

	const getOverallStatusIcon = () =>
	{
		switch (getOverallStatus())
		{
			case 'healthy':
				return <IconCheck size={16} />;
			case 'warning':
				return <IconAlertTriangle size={16} />;
			case 'critical':
				return <IconX size={16} />;
			default:
				return <IconServer size={16} />;
		}
	};

	return (
		<Container size="xl" py="md">
			<Group justify="space-between" mb="lg">
				<Title order={1}>Service Health Monitor</Title>
				<Group gap="sm">
					{lastUpdate && (
						<Group gap="xs">
							<Text size="sm" c="dimmed">
								Last updated: {lastUpdate.toLocaleTimeString()}
							</Text>
							{realtimeUpdates > 0 && (
								<Badge size="xs" color="blue" variant="light">
									+{realtimeUpdates} live
								</Badge>
							)}
						</Group>
					)}
					<Tooltip label="Refresh All Services">
						<ActionIcon
							variant="light"
							color="blue"
							onClick={fetchHealthData}
							loading={loading}
						>
							<IconRefresh size={16} />
						</ActionIcon>
					</Tooltip>
				</Group>
			</Group>

			{/* Real-time Status Bar */}
			<Alert
				icon={<IconActivity size={16} />}
				title="Real-time Monitoring Active"
				color="blue"
				variant="light"
				mb="md"
			>
				<Group justify="space-between">
					<Text size="sm">
						Receiving live updates from all services and databases
					</Text>
					<Group gap="md">
						<Text size="sm" c="dimmed">
							Updates received: {serviceMessageCount + databaseMessageCount}
						</Text>
						<RealtimeIndicator showDetails />
					</Group>
				</Group>
			</Alert>

			{error && (
				<Alert
					icon={<IconAlertTriangle size={16} />}
					title="Connection Error"
					color="red"
					mb="md"
				>
					{error}
				</Alert>
			)}

			<Paper withBorder p="md" mb="xl" pos="relative">
				<LoadingOverlay visible={loading} />

				<Group justify="space-between" mb="md">
					<Title order={2}>System Overview</Title>
					<Badge
						size="lg"
						color={getOverallStatusColor()}
						variant="light"
						leftSection={getOverallStatusIcon()}
					>
						{getOverallStatus().toUpperCase()}
					</Badge>
				</Group>

				{healthData && (
					<Grid>
						<Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
							<Stack gap="xs" align="center">
								<Text size="xl" fw={700} c="blue">
									{healthData.summary.total}
								</Text>
								<Text size="sm" c="dimmed" ta="center">
									Total Services
								</Text>
							</Stack>
						</Grid.Col>

						<Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
							<Stack gap="xs" align="center">
								<Text size="xl" fw={700} c="green">
									{healthData.summary.healthy}
								</Text>
								<Text size="sm" c="dimmed" ta="center">
									Healthy
								</Text>
							</Stack>
						</Grid.Col>

						<Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
							<Stack gap="xs" align="center">
								<Text size="xl" fw={700} c="yellow">
									{healthData.summary.degraded}
								</Text>
								<Text size="sm" c="dimmed" ta="center">
									Degraded
								</Text>
							</Stack>
						</Grid.Col>

						<Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
							<Stack gap="xs" align="center">
								<Text size="xl" fw={700} c="red">
									{healthData.summary.unhealthy}
								</Text>
								<Text size="sm" c="dimmed" ta="center">
									Unhealthy
								</Text>
							</Stack>
						</Grid.Col>
					</Grid>
				)}
			</Paper>

			<Title order={2} mb="md">
				Service Status
			</Title>

			<Grid>
				{healthData?.services.map(service => (
					<Grid.Col key={service.name} span={{ base: 12, md: 6, lg: 4 }}>
						<ServiceStatusCard
							{...service}
							onRestart={handleServiceRestart}
						/>
					</Grid.Col>
				))}
			</Grid>

			{/* ServiceDependencyMap is temporarily disabled post-consolidation; re-enable when component is restored healthData && healthData.databases && (
				<div style={{ marginTop: '2rem' }}>
					<ServiceDependencyMap
						services={healthData.services.map(s => ({ name: s.name, status: s.status }))}
						databases={healthData.databases.map(db => ({
							name: db.name,
							status: db.connected ? 'connected' : 'disconnected',
							dependentServices: getDependentServices(db.name),
						}))}
					/>
				</div>
			)}*/}

			{!healthData && !loading && (
				<Paper withBorder p="xl" ta="center">
					<Stack gap="md" align="center">
						<IconServer size={48} color="gray" />
						<Text size="lg" c="dimmed">
							No service data available
						</Text>
						<Text size="sm" c="dimmed">
							Click refresh to check service status
						</Text>
					</Stack>
				</Paper>
			)}
		</Container>
	);
}

export default ServiceHealthDashboard;
