'use client';

import {
	Card,
	Text,
	Badge,
	Group,
	Stack,
	Progress,
	ActionIcon,
	Tooltip,
	Button,
	Modal,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';
import {
	IconRefresh,
	IconAlertTriangle,
	IconCheck,
	IconX,
	IconCpu,
	IconDatabase,
	IconClock,
} from '@tabler/icons-react';
import { useState } from 'react';

interface ServiceMetrics
{
	cpu: number;
	memory: number;
	requests: number;
	errors: number;
}

interface ServiceDependency
{
	name: string;
	status: 'connected' | 'disconnected' | 'error';
	responseTime: number;
}

interface ServiceStatusCardProps
{
	name: string;
	status: 'healthy' | 'unhealthy' | 'degraded';
	uptime: number;
	responseTime: number;
	lastCheck: Date;
	version: string;
	metrics: ServiceMetrics;
	dependencies?: ServiceDependency[];
	onRestart?: (serviceName: string, force: boolean) => Promise<void>;
}

function ServiceStatusCard({
	name,
	status,
	uptime,
	responseTime,
	lastCheck,
	version,
	metrics,
	dependencies = [],
	onRestart,
}: ServiceStatusCardProps)
{
	const [restartModalOpened, { open: openRestartModal, close: closeRestartModal }] = useDisclosure(false);
	const [isRestarting, setIsRestarting] = useState(false);

	const getStatusColor = (serviceStatus: string) =>
	{
		switch (serviceStatus)
		{
			case 'healthy':
				return 'green';
			case 'degraded':
				return 'yellow';
			case 'unhealthy':
				return 'red';
			default:
				return 'gray';
		}
	};

	const getStatusIcon = (serviceStatus: string) =>
	{
		switch (serviceStatus)
		{
			case 'healthy':
				return <IconCheck size={16} />;
			case 'degraded':
				return <IconAlertTriangle size={16} />;
			case 'unhealthy':
				return <IconX size={16} />;
			default:
				return null;
		}
	};

	const formatUptime = (uptimeSeconds: number) =>
	{
		const hours = Math.floor(uptimeSeconds / 3600);
		const minutes = Math.floor((uptimeSeconds % 3600) / 60);
		const seconds = uptimeSeconds % 60;

		if (hours > 0)
		{
			return `${hours}h ${minutes}m`;
		}
		if (minutes > 0)
		{
			return `${minutes}m ${seconds}s`;
		}
		return `${seconds}s`;
	};

	const handleRestart = async (force: boolean) =>
	{
		if (!onRestart) return;

		setIsRestarting(true);
		try
		{
			await onRestart(name, force);
			notifications.show({
				title: 'Service Restart',
				message: `${name} service restart initiated successfully`,
				color: 'green',
				icon: <IconCheck size={16} />,
			});
			closeRestartModal();
		}
		catch (error)
		{
			notifications.show({
				title: 'Restart Failed',
				message: `Failed to restart ${name} service`,
				color: 'red',
				icon: <IconX size={16} />,
			});
		}
		finally
		{
			setIsRestarting(false);
		}
	};

	return (
		<>
			<Card withBorder radius="md" p="md">
				<Group justify="space-between" mb="xs">
					<Group gap="xs">
						<Text fw={600} size="lg" tt="capitalize">
							{name.replace('-', ' ')}
						</Text>
						<Badge
							color={getStatusColor(status)}
							variant="light"
							leftSection={getStatusIcon(status)}
						>
							{status}
						</Badge>
					</Group>

					<Group gap="xs">
						<Tooltip label="Restart Service">
							<ActionIcon
								variant="light"
								color="blue"
								onClick={openRestartModal}
								disabled={isRestarting}
							>
								<IconRefresh size={16} />
							</ActionIcon>
						</Tooltip>
					</Group>
				</Group>

				<Stack gap="sm">
					<Group justify="space-between">
						<Group gap="xs">
							<IconClock size={14} />
							<Text size="sm" c="dimmed">
								Uptime: {formatUptime(uptime)}
							</Text>
						</Group>
						<Text size="sm" c="dimmed">
							v{version}
						</Text>
					</Group>

					<Group justify="space-between">
						<Text size="sm" c="dimmed">
							Response Time: {responseTime}ms
						</Text>
						<Text size="sm" c="dimmed">
							Last Check: {lastCheck.toLocaleTimeString()}
						</Text>
					</Group>

					<Stack gap="xs">
						<Group justify="space-between">
							<Group gap="xs">
								<IconCpu size={14} />
								<Text size="sm">CPU Usage</Text>
							</Group>
							<Text size="sm" fw={500}>
								{metrics.cpu.toFixed(1)}%
							</Text>
						</Group>
						<Progress value={metrics.cpu} color={metrics.cpu > 80 ? 'red' : metrics.cpu > 60 ? 'yellow' : 'green'} />
					</Stack>

					<Stack gap="xs">
						<Group justify="space-between">
							<Group gap="xs">
								<IconDatabase size={14} />
								<Text size="sm">Memory Usage</Text>
							</Group>
							<Text size="sm" fw={500}>
								{metrics.memory.toFixed(1)}%
							</Text>
						</Group>
						<Progress value={metrics.memory} color={metrics.memory > 80 ? 'red' : metrics.memory > 60 ? 'yellow' : 'green'} />
					</Stack>

					<Group justify="space-between">
						<Text size="sm" c="dimmed">
							Requests: {metrics.requests.toLocaleString()}
						</Text>
						<Text size="sm" c={metrics.errors > 0 ? 'red' : 'dimmed'}>
							Errors: {metrics.errors.toLocaleString()}
						</Text>
					</Group>

					{dependencies.length > 0 && (
						<Stack gap="xs" mt="sm">
							<Text size="sm" fw={500}>
								Dependencies
							</Text>
							{dependencies.map(dep => (
								<Group key={dep.name} justify="space-between">
									<Text size="sm" c="dimmed">
										{dep.name}
									</Text>
									<Group gap="xs">
										<Badge
											size="xs"
											color={dep.status === 'connected' ? 'green' : 'red'}
											variant="light"
										>
											{dep.status}
										</Badge>
										<Text size="xs" c="dimmed">
											{dep.responseTime}ms
										</Text>
									</Group>
								</Group>
							))}
						</Stack>
					)}
				</Stack>
			</Card>

			<Modal
				opened={restartModalOpened}
				onClose={closeRestartModal}
				title={`Restart ${name} Service`}
				centered
			>
				<Stack gap="md">
					<Text>
						Are you sure you want to restart the {name} service? This will temporarily interrupt service operations.
					</Text>

					<Group justify="flex-end" gap="sm">
						<Button variant="default" onClick={closeRestartModal}>
							Cancel
						</Button>
						<Button
							color="orange"
							onClick={() => handleRestart(false)}
							loading={isRestarting}
						>
							Graceful Restart
						</Button>
						<Button
							color="red"
							onClick={() => handleRestart(true)}
							loading={isRestarting}
						>
							Force Restart
						</Button>
					</Group>
				</Stack>
			</Modal>
		</>
	);
}

export { ServiceStatusCard };
