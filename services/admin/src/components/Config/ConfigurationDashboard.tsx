'use client';

import {
	Settings,
	Database,
	Globe,
	TrendingUp,
	Bell,
	Brain,
	Seedling,
	Save,
	Eye,
	History,
	Template,
	Rocket,
} from 'lucide-react';
import { useState, useEffect } from 'react';
import { clientLogger } from '@/lib/logger';


import type {
	SystemConfigType,
	ConfigCategoryType,
	ConfigValidationResultType,
	ConfigPreviewType,
} from '@/types/config';
import { AIServicesConfigForm } from './AIServicesConfigForm';
import { AlertsConfigForm } from './AlertsConfigForm';
import { ConfigAuditLog } from './ConfigAuditLog';
import { ConfigPreview } from './ConfigPreview';
import { ConfigTemplates } from './ConfigTemplates';
import { CrawlingConfigForm } from './CrawlingConfigForm';
import { DatabaseConfigForm } from './DatabaseConfigForm';
import { RankingConfigForm } from './RankingConfigForm';
import { SeederConfigForm } from './SeederConfigForm';

const CONFIG_CATEGORIES = [
	{
		key: 'database' as ConfigCategoryType,
		label: 'Database',
		icon: Database,
		description: 'Database connections and settings',
	},
	{
		key: 'crawling' as ConfigCategoryType,
		label: 'Crawling',
		icon: Globe,
		description: 'Web crawling configuration',
	},
	{
		key: 'ranking' as ConfigCategoryType,
		label: 'Ranking',
		icon: TrendingUp,
		description: 'Domain ranking algorithms',
	},
	{
		key: 'alerts' as ConfigCategoryType,
		label: 'Alerts',
		icon: Bell,
		description: 'Monitoring and notifications',
	},
	{
		key: 'ai_services' as ConfigCategoryType,
		label: 'AI Services',
		icon: Brain,
		description: 'AI provider configuration',
	},
	{
		key: 'seeder' as ConfigCategoryType,
		label: 'Seeder',
		icon: Seedling,
		description: 'Domain discovery settings',
	},
];

export function ConfigurationDashboard()
{
	const [activeCategory, setActiveCategory] = useState<ConfigCategoryType>('database');
	const [activeTab, setActiveTab] = useState<'config' | 'preview' | 'audit' | 'templates'>('config');
	const [config, setConfig] = useState<Partial<SystemConfigType>>({});
	const [originalConfig, setOriginalConfig] = useState<Partial<SystemConfigType>>({});
	const [validation, setValidation] = useState<ConfigValidationResultType | null>(null);
	const [preview, setPreview] = useState<ConfigPreviewType | null>(null);
	const [loading, setLoading] = useState(true);
	const [saving, setSaving] = useState(false);
	const [hasChanges, setHasChanges] = useState(false);

	useEffect(() =>
	{
		loadConfiguration();
	}, []);

	useEffect(() =>
	{
		// Check if there are changes
		const hasConfigChanges = JSON.stringify(config) !== JSON.stringify(originalConfig);
		setHasChanges(hasConfigChanges);
	}, [config, originalConfig]);

	const loadConfiguration = async () =>
	{
		try
		{
			setLoading(true);
			const response = await fetch('/api/config');
			const data = await response.json();

			if (data.success)
			{
				setConfig(data.data);
				setOriginalConfig(data.data);
			}
		}
		catch (error)
		{
			clientLogger.error('Failed to load configuration:', error);
		}
		finally
		{
			setLoading(false);
		}
	};

	const handleConfigChange = (category: ConfigCategoryType, field: string, value: any) =>
	{
		setConfig(prev => ({
			...prev,
			[category]: {
				...prev[category],
				[field]: value,
			},
		}));
	};

	const validateConfiguration = async (category: ConfigCategoryType) =>
	{
		try
		{
			const response = await fetch('/api/config/validate', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					category,
					config: config[category],
					currentConfig: originalConfig[category],
				}),
			});

			const data = await response.json();

			if (data.success)
			{
				setValidation(data.data.validation);
				return data.data.validation;
			}
		}
		catch (error)
		{
			clientLogger.error('Failed to validate configuration:', error);
		}

		return null;
	};

	const generatePreview = async () =>
	{
		try
		{
			const response = await fetch('/api/config/preview', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					newConfig: config,
					currentConfig: originalConfig,
				}),
			});

			const data = await response.json();

			if (data.success)
			{
				setPreview(data.data);
				setActiveTab('preview');
			}
		}
		catch (error)
		{
			clientLogger.error('Failed to generate preview:', error);
		}
	};

	const saveConfiguration = async () =>
	{
		try
		{
			setSaving(true);

			// Validate all categories with changes
			const categoriesToValidate = Object.keys(config).filter(category => JSON.stringify(config[category as ConfigCategoryType])
        !== JSON.stringify(originalConfig[category as ConfigCategoryType])) as ConfigCategoryType[];

			for (const category of categoriesToValidate)
			{
				const validation = await validateConfiguration(category);
				if (!validation?.valid)
				{
					alert(`Configuration validation failed for ${category}. Please fix errors before saving.`);
					return;
				}
			}

			// Save each changed category
			for (const category of categoriesToValidate)
			{
				const response = await fetch('/api/config', {
					method: 'PUT',
					headers: { 'Content-Type': 'application/json' },
					body: JSON.stringify({
						category,
						config: config[category],
						reason: `Updated ${category} configuration`,
					}),
				});

				if (!response.ok)
				{
					throw new Error(`Failed to save ${category} configuration`);
				}
			}

			// Reload configuration
			await loadConfiguration();
			alert('Configuration saved successfully!');
		}
		catch (error)
		{
			clientLogger.error('Failed to save configuration:', error);
			alert('Failed to save configuration. Please try again.');
		}
		finally
		{
			setSaving(false);
		}
	};

	const renderConfigForm = () =>
	{
		const categoryConfig = config[activeCategory] || {};

		switch (activeCategory)
		{
			case 'database':
				return (
					<DatabaseConfigForm
						config={categoryConfig}
						onChange={(field, value) => handleConfigChange(activeCategory, field, value)}
						onValidate={() => validateConfiguration(activeCategory)}
					/>
				);
			case 'crawling':
				return (
					<CrawlingConfigForm
						config={categoryConfig}
						onChange={(field, value) => handleConfigChange(activeCategory, field, value)}
						onValidate={() => validateConfiguration(activeCategory)}
					/>
				);
			case 'ranking':
				return (
					<RankingConfigForm
						config={categoryConfig}
						onChange={(field, value) => handleConfigChange(activeCategory, field, value)}
						onValidate={() => validateConfiguration(activeCategory)}
					/>
				);
			case 'alerts':
				return (
					<AlertsConfigForm
						config={categoryConfig}
						onChange={(field, value) => handleConfigChange(activeCategory, field, value)}
						onValidate={() => validateConfiguration(activeCategory)}
					/>
				);
			case 'ai_services':
				return (
					<AIServicesConfigForm
						config={categoryConfig}
						onChange={(field, value) => handleConfigChange(activeCategory, field, value)}
						onValidate={() => validateConfiguration(activeCategory)}
					/>
				);
			case 'seeder':
				return (
					<SeederConfigForm
						config={categoryConfig}
						onChange={(field, value) => handleConfigChange(activeCategory, field, value)}
						onValidate={() => validateConfiguration(activeCategory)}
					/>
				);
			default:
				return <div>Configuration form not implemented</div>;
		}
	};

	const renderTabContent = () =>
	{
		switch (activeTab)
		{
			case 'config':
				return renderConfigForm();
			case 'preview':
				return <ConfigPreview preview={preview} />;
			case 'audit':
				return <ConfigAuditLog category={activeCategory} />;
			case 'templates':
				return <ConfigTemplates category={activeCategory} onApplyTemplate={setConfig} />;
			default:
				return null;
		}
	};

	if (loading)
	{
		return (
			<div className="flex items-center justify-center h-64">
				<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600" />
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-2xl font-bold text-gray-900">System Configuration</h1>
					<p className="text-gray-600">Manage system-wide configuration settings</p>
				</div>

				<div className="flex items-center space-x-3">
					{hasChanges && (
						<span className="text-sm text-amber-600 bg-amber-50 px-3 py-1 rounded-full">
							Unsaved changes
						</span>
					)}

					<button
						onClick={generatePreview}
						disabled={!hasChanges}
						className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
					>
						<Eye className="w-4 h-4" />
						<span>Preview</span>
					</button>

					<button
						onClick={saveConfiguration}
						disabled={!hasChanges || saving}
						className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50"
					>
						{saving ? (
							<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white" />
						) : (
							<Save className="w-4 h-4" />
						)}
						<span>{saving ? 'Saving...' : 'Save Changes'}</span>
					</button>
				</div>
			</div>

			<div className="flex space-x-6">
				{/* Sidebar */}
				<div className="w-64 space-y-1">
					{CONFIG_CATEGORIES.map((category) =>
					{
						const Icon = category.icon;
						const isActive = activeCategory === category.key;

						return (
							<button
								key={category.key}
								onClick={() => setActiveCategory(category.key)}
								className={`w-full flex items-center space-x-3 px-3 py-2 text-left rounded-md transition-colors ${
                  isActive
                  	? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600'
                  	: 'text-gray-700 hover:bg-gray-50'
		}`}
							>
								<Icon className="w-5 h-5" />
								<div>
									<div className="font-medium">{category.label}</div>
									<div className="text-xs text-gray-500">{category.description}</div>
								</div>
							</button>
						);
					})}
				</div>

				{/* Main Content */}
				<div className="flex-1">
					{/* Tabs */}
					<div className="border-b border-gray-200 mb-6">
						<nav className="-mb-px flex space-x-8">
							{[
								{ key: 'config', label: 'Configuration', icon: Settings },
								{ key: 'preview', label: 'Preview', icon: Eye },
								{ key: 'audit', label: 'Audit Log', icon: History },
								{ key: 'templates', label: 'Templates', icon: Template },
							].map((tab) =>
							{
								const Icon = tab.icon;
								const isActive = activeTab === tab.key;

								return (
									<button
										key={tab.key}
										onClick={() => setActiveTab(tab.key as any)}
										className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${
                      isActive
                      	? 'border-blue-500 text-blue-600'
                      	: 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
		}`}
									>
										<Icon className="w-4 h-4" />
										<span>{tab.label}</span>
									</button>
								);
							})}
						</nav>
					</div>

					{/* Tab Content */}
					<div className="bg-white rounded-lg border border-gray-200 p-6">
						{renderTabContent()}
					</div>

					{/* Validation Results */}
					{validation && (
						<div className="mt-6 bg-white rounded-lg border border-gray-200 p-6">
							<h3 className="text-lg font-medium text-gray-900 mb-4">Validation Results</h3>

							{validation.errors.length > 0 && (
								<div className="mb-4">
									<h4 className="text-sm font-medium text-red-800 mb-2">Errors</h4>
									<ul className="space-y-1">
										{validation.errors.map((error, index) => (
											<li key={index} className="text-sm text-red-600">
												<strong>{error.field}:</strong> {error.message}
											</li>
										))}
									</ul>
								</div>
							)}

							{validation.warnings.length > 0 && (
								<div className="mb-4">
									<h4 className="text-sm font-medium text-amber-800 mb-2">Warnings</h4>
									<ul className="space-y-1">
										{validation.warnings.map((warning, index) => (
											<li key={index} className="text-sm text-amber-600">
												<strong>{warning.field}:</strong> {warning.message}
											</li>
										))}
									</ul>
								</div>
							)}

							{validation.valid && validation.errors.length === 0 && (
								<div className="text-sm text-green-600">
									✓ Configuration is valid
								</div>
							)}
						</div>
					)}
				</div>
			</div>
		</div>
	);
}

export default ConfigurationDashboard;
