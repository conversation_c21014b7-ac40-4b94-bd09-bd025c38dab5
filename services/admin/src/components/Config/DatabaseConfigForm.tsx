'use client';

import {
	Database,
	Server,
	Shield,
	Clock,
	Users,
	Alert<PERSON>riangle,
	CheckCircle,
	Plus,
	Minus,
} from 'lucide-react';
import { useState } from 'react';

import type { DatabaseConfigType, ConfigValidationResultType } from '@/types/config';

type DatabaseConfigFormPropsType = {
	config: Partial<DatabaseConfigType>;
	onChange: (field: string, value: unknown) => void;
	onValidate: () => Promise<ConfigValidationResultType | null>;
};

export function DatabaseConfigForm({ config, onChange, onValidate }: DatabaseConfigFormPropsType)
{
	const [activeSection, setActiveSection] = useState<'scylla' | 'mariadb' | 'redis' | 'manticore'>('scylla');
	const [validation, setValidation] = useState<ConfigValidationResultType | null>(null);
	const [validating, setValidating] = useState(false);

	const handleValidate = async () =>
	{
		setValidating(true);
		const result = await onValidate();
		setValidation(result);
		setValidating(false);
	};

	const isRecord = (value: unknown): value is Record<string, unknown> =>
	{
		return typeof value === 'object' && value !== null && !Array.isArray(value);
	};

	const getProp = (obj: unknown, key: string): unknown =>
	{
		if (typeof obj === 'object' && obj !== null && key in obj)
		{
			return (obj as Record<string, unknown>)[key];
		}
		return undefined;
	};

	const updateNestedField = (section: keyof DatabaseConfigType, field: string, value: unknown) =>
	{
		const sectionValue = config[section];
		const existing = isRecord(sectionValue) ? sectionValue : {};
		onChange(section, {
			...existing,
			[field]: value,
		});
	};

	const getStringArray = (section: keyof DatabaseConfigType, field: string): string[] =>
	{
		const sectionValue = config[section];
		const arr = getProp(sectionValue, field);
		if (Array.isArray(arr))
		{
			return arr.filter((v): v is string => typeof v === 'string');
		}
		return [];
	};

	const addArrayItem = (section: keyof DatabaseConfigType, field: string, defaultValue: string = '') =>
	{
		const currentArray = getStringArray(section, field);
		updateNestedField(section, field, [...currentArray, defaultValue]);
	};

	const removeArrayItem = (section: keyof DatabaseConfigType, field: string, index: number) =>
	{
		const currentArray = getStringArray(section, field);
		updateNestedField(section, field, currentArray.filter((_, i) => i !== index));
	};

	const updateArrayItem = (section: keyof DatabaseConfigType, field: string, index: number, value: string) =>
	{
		const currentArray = getStringArray(section, field);
		const newArray = [...currentArray];
		newArray[index] = value;
		updateNestedField(section, field, newArray);
	};

	const renderScyllaConfig = () => (
		<div className="space-y-6">
			<div className="flex items-center space-x-2 mb-4">
				<Database className="w-5 h-5 text-blue-600" />
				<h3 className="text-lg font-medium text-gray-900">ScyllaDB Configuration</h3>
			</div>

			{/* Hosts */}
			<div>
				<label className="block text-sm font-medium text-gray-700 mb-2">
					Hosts
				</label>
				<div className="space-y-2">
					{(config.scylla?.hosts || []).map((host, index) => (
						<div key={index} className="flex items-center space-x-2">
							<input
								type="text"
								value={host}
								onChange={e => updateArrayItem('scylla', 'hosts', index, e.target.value)}
								placeholder="hostname:port"
								className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
							/>
							<button
								onClick={() => removeArrayItem('scylla', 'hosts', index)}
								className="p-2 text-red-600 hover:bg-red-50 rounded-md"
							>
								<Minus className="w-4 h-4" />
							</button>
						</div>
					))}
					<button
						onClick={() => addArrayItem('scylla', 'hosts', 'localhost:9042')}
						className="flex items-center space-x-2 px-3 py-2 text-sm text-blue-600 border border-blue-300 rounded-md hover:bg-blue-50"
					>
						<Plus className="w-4 h-4" />
						<span>Add Host</span>
					</button>
				</div>
			</div>

			<div className="grid grid-cols-2 gap-4">
				{/* Keyspace */}
				<div>
					<label className="block text-sm font-medium text-gray-700 mb-2">
						Keyspace
					</label>
					<input
						type="text"
						value={config.scylla?.keyspace || ''}
						onChange={e => updateNestedField('scylla', 'keyspace', e.target.value)}
						className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
					/>
				</div>

				{/* Username */}
				<div>
					<label className="block text-sm font-medium text-gray-700 mb-2">
						Username
					</label>
					<input
						type="text"
						value={config.scylla?.username || ''}
						onChange={e => updateNestedField('scylla', 'username', e.target.value)}
						className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
					/>
				</div>

				{/* Password */}
				<div>
					<label className="block text-sm font-medium text-gray-700 mb-2">
						Password
					</label>
					<input
						type="password"
						value={config.scylla?.password || ''}
						onChange={e => updateNestedField('scylla', 'password', e.target.value)}
						className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
					/>
				</div>

				{/* Pool Size */}
				<div>
					<label className="block text-sm font-medium text-gray-700 mb-2">
						Pool Size
					</label>
					<input
						type="number"
						min="1"
						max="100"
						value={config.scylla?.poolSize || 10}
						onChange={e => updateNestedField('scylla', 'poolSize', parseInt(e.target.value))}
						className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
					/>
				</div>

				{/* Timeout */}
				<div>
					<label className="block text-sm font-medium text-gray-700 mb-2">
						Timeout (ms)
					</label>
					<input
						type="number"
						min="1000"
						value={config.scylla?.timeout || 30000}
						onChange={e => updateNestedField('scylla', 'timeout', parseInt(e.target.value))}
						className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
					/>
				</div>

				{/* Retry Attempts */}
				<div>
					<label className="block text-sm font-medium text-gray-700 mb-2">
						Retry Attempts
					</label>
					<input
						type="number"
						min="0"
						max="10"
						value={config.scylla?.retryAttempts || 3}
						onChange={e => updateNestedField('scylla', 'retryAttempts', parseInt(e.target.value))}
						className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
					/>
				</div>
			</div>

			{/* Checkboxes */}
			<div className="space-y-3">
				<label className="flex items-center">
					<input
						type="checkbox"
						checked={config.scylla?.ssl || false}
						onChange={e => updateNestedField('scylla', 'ssl', e.target.checked)}
						className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
					/>
					<span className="ml-2 text-sm text-gray-700">Enable SSL</span>
				</label>

				<label className="flex items-center">
					<input
						type="checkbox"
						checked={config.scylla?.compression || false}
						onChange={e => updateNestedField('scylla', 'compression', e.target.checked)}
						className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
					/>
					<span className="ml-2 text-sm text-gray-700">Enable Compression</span>
				</label>

				<label className="flex items-center">
					<input
						type="checkbox"
						checked={config.scylla?.failoverEnabled || false}
						onChange={e => updateNestedField('scylla', 'failoverEnabled', e.target.checked)}
						className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
					/>
					<span className="ml-2 text-sm text-gray-700">Enable Failover</span>
				</label>
			</div>

			{/* Failover Hosts */}
			{config.scylla?.failoverEnabled && (
				<div>
					<label className="block text-sm font-medium text-gray-700 mb-2">
						Failover Hosts
					</label>
					<div className="space-y-2">
						{(config.scylla?.failoverHosts || []).map((host, index) => (
							<div key={index} className="flex items-center space-x-2">
								<input
									type="text"
									value={host}
									onChange={e => updateArrayItem('scylla', 'failoverHosts', index, e.target.value)}
									placeholder="hostname:port"
									className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
								/>
								<button
									onClick={() => removeArrayItem('scylla', 'failoverHosts', index)}
									className="p-2 text-red-600 hover:bg-red-50 rounded-md"
								>
									<Minus className="w-4 h-4" />
								</button>
							</div>
						))}
						<button
							onClick={() => addArrayItem('scylla', 'failoverHosts', 'localhost:9042')}
							className="flex items-center space-x-2 px-3 py-2 text-sm text-blue-600 border border-blue-300 rounded-md hover:bg-blue-50"
						>
							<Plus className="w-4 h-4" />
							<span>Add Failover Host</span>
						</button>
					</div>
				</div>
			)}
		</div>
	);

	const renderMariaDBConfig = () => (
		<div className="space-y-6">
			<div className="flex items-center space-x-2 mb-4">
				<Server className="w-5 h-5 text-green-600" />
				<h3 className="text-lg font-medium text-gray-900">MariaDB Configuration</h3>
			</div>

			<div className="grid grid-cols-2 gap-4">
				{/* Host */}
				<div>
					<label className="block text-sm font-medium text-gray-700 mb-2">
						Host
					</label>
					<input
						type="text"
						value={config.mariadb?.host || ''}
						onChange={e => updateNestedField('mariadb', 'host', e.target.value)}
						className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
					/>
				</div>

				{/* Port */}
				<div>
					<label className="block text-sm font-medium text-gray-700 mb-2">
						Port
					</label>
					<input
						type="number"
						min="1"
						max="65535"
						value={config.mariadb?.port || 3306}
						onChange={e => updateNestedField('mariadb', 'port', parseInt(e.target.value))}
						className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
					/>
				</div>

				{/* Database */}
				<div>
					<label className="block text-sm font-medium text-gray-700 mb-2">
						Database
					</label>
					<input
						type="text"
						value={config.mariadb?.database || ''}
						onChange={e => updateNestedField('mariadb', 'database', e.target.value)}
						className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
					/>
				</div>

				{/* Username */}
				<div>
					<label className="block text-sm font-medium text-gray-700 mb-2">
						Username
					</label>
					<input
						type="text"
						value={config.mariadb?.username || ''}
						onChange={e => updateNestedField('mariadb', 'username', e.target.value)}
						className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
					/>
				</div>

				{/* Password */}
				<div>
					<label className="block text-sm font-medium text-gray-700 mb-2">
						Password
					</label>
					<input
						type="password"
						value={config.mariadb?.password || ''}
						onChange={e => updateNestedField('mariadb', 'password', e.target.value)}
						className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
					/>
				</div>

				{/* Pool Size */}
				<div>
					<label className="block text-sm font-medium text-gray-700 mb-2">
						Pool Size
					</label>
					<input
						type="number"
						min="1"
						max="200"
						value={config.mariadb?.poolSize || 10}
						onChange={e => updateNestedField('mariadb', 'poolSize', parseInt(e.target.value))}
						className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
					/>
				</div>

				{/* Timeout */}
				<div>
					<label className="block text-sm font-medium text-gray-700 mb-2">
						Timeout (ms)
					</label>
					<input
						type="number"
						min="1000"
						value={config.mariadb?.timeout || 30000}
						onChange={e => updateNestedField('mariadb', 'timeout', parseInt(e.target.value))}
						className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
					/>
				</div>

				{/* Retry Attempts */}
				<div>
					<label className="block text-sm font-medium text-gray-700 mb-2">
						Retry Attempts
					</label>
					<input
						type="number"
						min="0"
						max="10"
						value={config.mariadb?.retryAttempts || 3}
						onChange={e => updateNestedField('mariadb', 'retryAttempts', parseInt(e.target.value))}
						className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
					/>
				</div>

				{/* Charset */}
				<div>
					<label className="block text-sm font-medium text-gray-700 mb-2">
						Charset
					</label>
					<select
						value={config.mariadb?.charset || 'utf8mb4'}
						onChange={e => updateNestedField('mariadb', 'charset', e.target.value)}
						className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
					>
						<option value="utf8mb4">utf8mb4</option>
						<option value="utf8">utf8</option>
						<option value="latin1">latin1</option>
					</select>
				</div>

				{/* Timezone */}
				<div>
					<label className="block text-sm font-medium text-gray-700 mb-2">
						Timezone
					</label>
					<input
						type="text"
						value={config.mariadb?.timezone || 'UTC'}
						onChange={e => updateNestedField('mariadb', 'timezone', e.target.value)}
						className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
					/>
				</div>

				{/* Health Check Interval */}
				<div>
					<label className="block text-sm font-medium text-gray-700 mb-2">
						Health Check Interval (ms)
					</label>
					<input
						type="number"
						min="1000"
						value={config.mariadb?.healthCheckInterval || 30000}
						onChange={e => updateNestedField('mariadb', 'healthCheckInterval', parseInt(e.target.value))}
						className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
					/>
				</div>
			</div>

			{/* SSL Checkbox */}
			<div>
				<label className="flex items-center">
					<input
						type="checkbox"
						checked={config.mariadb?.ssl || false}
						onChange={e => updateNestedField('mariadb', 'ssl', e.target.checked)}
						className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
					/>
					<span className="ml-2 text-sm text-gray-700">Enable SSL</span>
				</label>
			</div>
		</div>
	);

	const renderRedisConfig = () => (
		<div className="space-y-6">
			<div className="flex items-center space-x-2 mb-4">
				<Database className="w-5 h-5 text-red-600" />
				<h3 className="text-lg font-medium text-gray-900">Redis Configuration</h3>
			</div>

			<div className="grid grid-cols-2 gap-4">
				{/* Host */}
				<div>
					<label className="block text-sm font-medium text-gray-700 mb-2">
						Host
					</label>
					<input
						type="text"
						value={config.redis?.host || ''}
						onChange={e => updateNestedField('redis', 'host', e.target.value)}
						className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
					/>
				</div>

				{/* Port */}
				<div>
					<label className="block text-sm font-medium text-gray-700 mb-2">
						Port
					</label>
					<input
						type="number"
						min="1"
						max="65535"
						value={config.redis?.port || 6379}
						onChange={e => updateNestedField('redis', 'port', parseInt(e.target.value))}
						className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
					/>
				</div>

				{/* Password */}
				<div>
					<label className="block text-sm font-medium text-gray-700 mb-2">
						Password
					</label>
					<input
						type="password"
						value={config.redis?.password || ''}
						onChange={e => updateNestedField('redis', 'password', e.target.value)}
						className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
					/>
				</div>

				{/* Database */}
				<div>
					<label className="block text-sm font-medium text-gray-700 mb-2">
						Database
					</label>
					<input
						type="number"
						min="0"
						max="15"
						value={config.redis?.database || 0}
						onChange={e => updateNestedField('redis', 'database', parseInt(e.target.value))}
						className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
					/>
				</div>

				{/* Key Prefix */}
				<div>
					<label className="block text-sm font-medium text-gray-700 mb-2">
						Key Prefix
					</label>
					<input
						type="text"
						value={config.redis?.keyPrefix || ''}
						onChange={e => updateNestedField('redis', 'keyPrefix', e.target.value)}
						className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
					/>
				</div>

				{/* Pool Size */}
				<div>
					<label className="block text-sm font-medium text-gray-700 mb-2">
						Pool Size
					</label>
					<input
						type="number"
						min="1"
						max="100"
						value={config.redis?.poolSize || 10}
						onChange={e => updateNestedField('redis', 'poolSize', parseInt(e.target.value))}
						className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
					/>
				</div>
			</div>

			{/* Checkboxes */}
			<div className="space-y-3">
				<label className="flex items-center">
					<input
						type="checkbox"
						checked={config.redis?.ssl || false}
						onChange={e => updateNestedField('redis', 'ssl', e.target.checked)}
						className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
					/>
					<span className="ml-2 text-sm text-gray-700">Enable SSL</span>
				</label>

				<label className="flex items-center">
					<input
						type="checkbox"
						checked={config.redis?.cluster || false}
						onChange={e => updateNestedField('redis', 'cluster', e.target.checked)}
						className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
					/>
					<span className="ml-2 text-sm text-gray-700">Cluster Mode</span>
				</label>
			</div>

			{/* Cluster Nodes */}
			{config.redis?.cluster && (
				<div>
					<label className="block text-sm font-medium text-gray-700 mb-2">
						Cluster Nodes
					</label>
					<div className="space-y-2">
						{(config.redis?.clusterNodes || []).map((node, index) => (
							<div key={index} className="flex items-center space-x-2">
								<input
									type="text"
									value={node}
									onChange={e => updateArrayItem('redis', 'clusterNodes', index, e.target.value)}
									placeholder="hostname:port"
									className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
								/>
								<button
									onClick={() => removeArrayItem('redis', 'clusterNodes', index)}
									className="p-2 text-red-600 hover:bg-red-50 rounded-md"
								>
									<Minus className="w-4 h-4" />
								</button>
							</div>
						))}
						<button
							onClick={() => addArrayItem('redis', 'clusterNodes', 'localhost:6379')}
							className="flex items-center space-x-2 px-3 py-2 text-sm text-blue-600 border border-blue-300 rounded-md hover:bg-blue-50"
						>
							<Plus className="w-4 h-4" />
							<span>Add Cluster Node</span>
						</button>
					</div>
				</div>
			)}
		</div>
	);

	const renderManticoreConfig = () => (
		<div className="space-y-6">
			<div className="flex items-center space-x-2 mb-4">
				<Database className="w-5 h-5 text-purple-600" />
				<h3 className="text-lg font-medium text-gray-900">Manticore Search Configuration</h3>
			</div>

			<div className="grid grid-cols-2 gap-4">
				{/* Host */}
				<div>
					<label className="block text-sm font-medium text-gray-700 mb-2">
						Host
					</label>
					<input
						type="text"
						value={config.manticore?.host || ''}
						onChange={e => updateNestedField('manticore', 'host', e.target.value)}
						className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
					/>
				</div>

				{/* Port */}
				<div>
					<label className="block text-sm font-medium text-gray-700 mb-2">
						Port
					</label>
					<input
						type="number"
						min="1"
						max="65535"
						value={config.manticore?.port || 9308}
						onChange={e => updateNestedField('manticore', 'port', parseInt(e.target.value))}
						className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
					/>
				</div>

				{/* Index Prefix */}
				<div>
					<label className="block text-sm font-medium text-gray-700 mb-2">
						Index Prefix
					</label>
					<input
						type="text"
						value={config.manticore?.indexPrefix || ''}
						onChange={e => updateNestedField('manticore', 'indexPrefix', e.target.value)}
						className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
					/>
				</div>

				{/* Max Query Time */}
				<div>
					<label className="block text-sm font-medium text-gray-700 mb-2">
						Max Query Time (ms)
					</label>
					<input
						type="number"
						min="1000"
						value={config.manticore?.maxQueryTime || 10000}
						onChange={e => updateNestedField('manticore', 'maxQueryTime', parseInt(e.target.value))}
						className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
					/>
				</div>

				{/* Timeout */}
				<div>
					<label className="block text-sm font-medium text-gray-700 mb-2">
						Timeout (ms)
					</label>
					<input
						type="number"
						min="1000"
						value={config.manticore?.timeout || 30000}
						onChange={e => updateNestedField('manticore', 'timeout', parseInt(e.target.value))}
						className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
					/>
				</div>

				{/* Retry Attempts */}
				<div>
					<label className="block text-sm font-medium text-gray-700 mb-2">
						Retry Attempts
					</label>
					<input
						type="number"
						min="0"
						max="10"
						value={config.manticore?.retryAttempts || 3}
						onChange={e => updateNestedField('manticore', 'retryAttempts', parseInt(e.target.value))}
						className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
					/>
				</div>

				{/* Health Check Interval */}
				<div>
					<label className="block text-sm font-medium text-gray-700 mb-2">
						Health Check Interval (ms)
					</label>
					<input
						type="number"
						min="1000"
						value={config.manticore?.healthCheckInterval || 30000}
						onChange={e => updateNestedField('manticore', 'healthCheckInterval', parseInt(e.target.value))}
						className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
					/>
				</div>
			</div>

			{/* SSL Checkbox */}
			<div>
				<label className="flex items-center">
					<input
						type="checkbox"
						checked={config.manticore?.ssl || false}
						onChange={e => updateNestedField('manticore', 'ssl', e.target.checked)}
						className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
					/>
					<span className="ml-2 text-sm text-gray-700">Enable SSL</span>
				</label>
			</div>
		</div>
	);

	return (
		<div className="space-y-6">
			{/* Section Tabs */}
			<div className="border-b border-gray-200">
				<nav className="-mb-px flex space-x-8">
					{([
						{ key: 'scylla', label: 'ScyllaDB', icon: Database },
						{ key: 'mariadb', label: 'MariaDB', icon: Server },
						{ key: 'redis', label: 'Redis', icon: Database },
						{ key: 'manticore', label: 'Manticore', icon: Database },
					] as const).map((section) =>
					{
						const Icon = section.icon;
						const isActive = activeSection === section.key;

						return (
							<button
								key={section.key}
								onClick={() => setActiveSection(section.key)}
								className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${
                  isActive
                   	? 'border-blue-500 text-blue-600'
                   	: 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
		}`}
							>
								<Icon className="w-4 h-4" />
								<span>{section.label}</span>
							</button>
						);
					})}
				</nav>
			</div>

			{/* Section Content */}
			<div>
				{activeSection === 'scylla' && renderScyllaConfig()}
				{activeSection === 'mariadb' && renderMariaDBConfig()}
				{activeSection === 'redis' && renderRedisConfig()}
				{activeSection === 'manticore' && renderManticoreConfig()}
			</div>

			{/* Validate Button */}
			<div className="flex justify-end">
				<button
					onClick={handleValidate}
					disabled={validating}
					className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50"
				>
					{validating ? (
						<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white" />
					) : (
						<CheckCircle className="w-4 h-4" />
					)}
					<span>{validating ? 'Validating...' : 'Validate Configuration'}</span>
				</button>
			</div>
		</div>
	);
}

export default DatabaseConfigForm;
