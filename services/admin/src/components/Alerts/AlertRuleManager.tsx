'use client';

import {
	<PERSON><PERSON>er,
	Card,
	Text,
	Button,
	Group,
	Stack,
	Badge,
	ActionIcon,
	Modal,
	TextInput,
	Textarea,
	Select,
	Switch,
	NumberInput,
	MultiSelect,
	Divider,
	Alert,
	Loader,
	Center,
	Title,
	Table,
	Pagination,
	Menu,
	Checkbox,
	Paper,
	SimpleGrid,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { clientLogger } from '@/lib/logger';
import { notifications } from '@mantine/notifications';
import {
	IconPlus,
	IconEdit,
	IconTrash,
	IconDots,
	IconCopy,
	IconPlay,
	IconPause,
	IconTestPipe,
	IconAlertTriangle,
	IconBell,
	IconEye,
	IconExclamationMark,
	IconSettings,
} from '@tabler/icons-react';
import { useState, useEffect } from 'react';

import type {
	AlertRuleType,
	AlertRuleCreateRequestType,
	AlertConditionType,
	AlertMetricType,
	AlertOperatorType,
	AlertSeverityType,
} from '@/types/alerts';

const SEVERITY_OPTIONS = [
	{ value: 'critical', label: 'Critical' },
	{ value: 'high', label: 'High' },
	{ value: 'medium', label: 'Medium' },
	{ value: 'low', label: 'Low' },
	{ value: 'info', label: 'Info' },
];

const METRIC_OPTIONS = [
	{ value: 'service_health', label: 'Service Health' },
	{ value: 'response_time', label: 'Response Time' },
	{ value: 'error_rate', label: 'Error Rate' },
	{ value: 'cpu_usage', label: 'CPU Usage' },
	{ value: 'memory_usage', label: 'Memory Usage' },
	{ value: 'disk_usage', label: 'Disk Usage' },
	{ value: 'queue_depth', label: 'Queue Depth' },
	{ value: 'database_connections', label: 'Database Connections' },
	{ value: 'crawl_success_rate', label: 'Crawl Success Rate' },
	{ value: 'domain_discovery_rate', label: 'Domain Discovery Rate' },
];

const OPERATOR_OPTIONS = [
	{ value: '>', label: 'Greater than' },
	{ value: '<', label: 'Less than' },
	{ value: '>=', label: 'Greater than or equal' },
	{ value: '<=', label: 'Less than or equal' },
	{ value: '=', label: 'Equal to' },
	{ value: '!=', label: 'Not equal to' },
	{ value: 'contains', label: 'Contains' },
	{ value: 'not_contains', label: 'Does not contain' },
];

const SERVICE_OPTIONS = [
	{ value: 'web-app', label: 'Web App' },
	{ value: 'crawler', label: 'Crawler' },
	{ value: 'ranking-engine', label: 'Ranking Engine' },
	{ value: 'scheduler', label: 'Scheduler' },
	{ value: 'domain-seeder', label: 'Domain Seeder' },
];

export default function AlertRuleManager()
{
	const [rules, setRules] = useState<AlertRuleType[]>([]);
	const [loading, setLoading] = useState(true);
	const [selectedRule, setSelectedRule] = useState<AlertRuleType | null>(null);
	const [isEditing, setIsEditing] = useState(false);
	const [currentPage, setCurrentPage] = useState(1);
	const [totalPages, setTotalPages] = useState(1);
	const [filters, setFilters] = useState({
		enabled: '',
		severity: '',
		tags: '',
	});

	const [opened, { open, close }] = useDisclosure(false);

	useEffect(() =>
	{
		fetchRules();
	}, [currentPage, filters]);

	const fetchRules = async () =>
	{
		try
		{
			setLoading(true);
			const params = new URLSearchParams();

			if (filters.enabled) params.append('enabled', filters.enabled);
			if (filters.severity) params.append('severity', filters.severity);
			if (filters.tags) params.append('tags', filters.tags);

			const response = await fetch(`/api/alerts/rules?${params}`);
			if (response.ok)
			{
				const data = await response.json();
				setRules(data.rules);
				setTotalPages(Math.ceil(data.total / 20));
			}
		}
		catch (error)
		{
			clientLogger.error('Error fetching alert rules:', error);
			notifications.show({
				title: 'Error',
				message: 'Failed to fetch alert rules',
				color: 'red',
			});
		}
		finally
		{
			setLoading(false);
		}
	};

	const handleCreateRule = () =>
	{
		setSelectedRule(null);
		setIsEditing(false);
		open();
	};

	const handleEditRule = (rule: AlertRuleType) =>
	{
		setSelectedRule(rule);
		setIsEditing(true);
		open();
	};

	const handleDeleteRule = async (ruleId: string) =>
	{
		try
		{
			const response = await fetch(`/api/alerts/rules/${ruleId}`, {
				method: 'DELETE',
			});

			if (response.ok)
			{
				notifications.show({
					title: 'Success',
					message: 'Alert rule deleted successfully',
					color: 'green',
				});
				fetchRules();
			}
		}
		catch (error)
		{
			clientLogger.error('Error deleting alert rule:', error);
			notifications.show({
				title: 'Error',
				message: 'Failed to delete alert rule',
				color: 'red',
			});
		}
	};

	const handleToggleRule = async (ruleId: string, enabled: boolean) =>
	{
		try
		{
			const response = await fetch(`/api/alerts/rules/${ruleId}`, {
				method: 'PUT',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ enabled }),
			});

			if (response.ok)
			{
				notifications.show({
					title: 'Success',
					message: `Alert rule ${enabled ? 'enabled' : 'disabled'} successfully`,
					color: 'green',
				});
				fetchRules();
			}
		}
		catch (error)
		{
			clientLogger.error('Error toggling alert rule:', error);
			notifications.show({
				title: 'Error',
				message: 'Failed to update alert rule',
				color: 'red',
			});
		}
	};

	const getSeverityIcon = (severity: AlertSeverityType) =>
	{
		switch (severity)
		{
			case 'critical':
				return <IconExclamationMark size={16} />;
			case 'high':
				return <IconAlertTriangle size={16} />;
			case 'medium':
				return <IconBell size={16} />;
			case 'low':
				return <IconEye size={16} />;
			default:
				return <IconBell size={16} />;
		}
	};

	const getSeverityColor = (severity: AlertSeverityType) =>
	{
		switch (severity)
		{
			case 'critical':
				return 'red';
			case 'high':
				return 'orange';
			case 'medium':
				return 'yellow';
			case 'low':
				return 'green';
			case 'info':
				return 'blue';
			default:
				return 'gray';
		}
	};

	if (loading)
	{
		return (
			<Center h={400}>
				<Stack align="center">
					<Loader size="lg" />
					<Text>Loading alert rules...</Text>
				</Stack>
			</Center>
		);
	}

	return (
		<Container size="xl" py="md">
			<Group justify="space-between" mb="lg">
				<Title order={2}>Alert Rules</Title>
				<Button
					leftSection={<IconPlus size={16} />}
					onClick={handleCreateRule}
				>
					Create Rule
				</Button>
			</Group>

			{/* Filters */}
			<Card withBorder mb="lg">
				<SimpleGrid cols={{ base: 1, sm: 3 }}>
					<Select
						label="Status"
						placeholder="All statuses"
						value={filters.enabled}
						onChange={value => setFilters(prev => ({ ...prev, enabled: value || '' }))}
						data={[
							{ value: 'true', label: 'Enabled' },
							{ value: 'false', label: 'Disabled' },
						]}
						clearable
					/>
					<Select
						label="Severity"
						placeholder="All severities"
						value={filters.severity}
						onChange={value => setFilters(prev => ({ ...prev, severity: value || '' }))}
						data={SEVERITY_OPTIONS}
						clearable
					/>
					<TextInput
						label="Tags"
						placeholder="Filter by tags..."
						value={filters.tags}
						onChange={event => setFilters(prev => ({ ...prev, tags: event.currentTarget.value }))}
					/>
				</SimpleGrid>
			</Card>

			{/* Rules Table */}
			<Card withBorder>
				<Table>
					<Table.Thead>
						<Table.Tr>
							<Table.Th>Rule Name</Table.Th>
							<Table.Th>Severity</Table.Th>
							<Table.Th>Status</Table.Th>
							<Table.Th>Conditions</Table.Th>
							<Table.Th>Tags</Table.Th>
							<Table.Th>Actions</Table.Th>
						</Table.Tr>
					</Table.Thead>
					<Table.Tbody>
						{rules.map(rule => (
							<Table.Tr key={rule.id}>
								<Table.Td>
									<div>
										<Text fw={600}>{rule.name}</Text>
										<Text size="xs" c="dimmed">{rule.description}</Text>
									</div>
								</Table.Td>
								<Table.Td>
									<Badge
										color={getSeverityColor(rule.severity)}
										variant="light"
										leftSection={getSeverityIcon(rule.severity)}
									>
										{rule.severity}
									</Badge>
								</Table.Td>
								<Table.Td>
									<Switch
										checked={rule.enabled}
										onChange={event => handleToggleRule(rule.id, event.currentTarget.checked)}
										size="sm"
									/>
								</Table.Td>
								<Table.Td>
									<Text size="sm">
										{rule.conditions.length} condition{rule.conditions.length !== 1 ? 's' : ''}
									</Text>
								</Table.Td>
								<Table.Td>
									<Group gap="xs">
										{rule.tags.slice(0, 2).map(tag => (
											<Badge key={tag} size="xs" variant="outline">
												{tag}
											</Badge>
										))}
										{rule.tags.length > 2 && (
											<Badge size="xs" variant="outline">
												+{rule.tags.length - 2}
											</Badge>
										)}
									</Group>
								</Table.Td>
								<Table.Td>
									<Group gap="xs">
										<ActionIcon
											variant="light"
											onClick={() => handleEditRule(rule)}
										>
											<IconEdit size={16} />
										</ActionIcon>
										<Menu>
											<Menu.Target>
												<ActionIcon variant="light">
													<IconDots size={16} />
												</ActionIcon>
											</Menu.Target>
											<Menu.Dropdown>
												<Menu.Item leftSection={<IconCopy size={16} />}>
													Duplicate
												</Menu.Item>
												<Menu.Item leftSection={<IconTestPipe size={16} />}>
													Test Rule
												</Menu.Item>
												<Menu.Divider />
												<Menu.Item
													leftSection={<IconTrash size={16} />}
													color="red"
													onClick={() => handleDeleteRule(rule.id)}
												>
													Delete
												</Menu.Item>
											</Menu.Dropdown>
										</Menu>
									</Group>
								</Table.Td>
							</Table.Tr>
						))}
					</Table.Tbody>
				</Table>

				{rules.length === 0 && (
					<Center py="xl">
						<Stack align="center">
							<IconSettings size={48} color="gray" />
							<Text c="dimmed">No alert rules found</Text>
							<Button variant="light" onClick={handleCreateRule}>
								Create your first rule
							</Button>
						</Stack>
					</Center>
				)}

				{totalPages > 1 && (
					<Group justify="center" mt="md">
						<Pagination
							value={currentPage}
							onChange={setCurrentPage}
							total={totalPages}
						/>
					</Group>
				)}
			</Card>

			{/* Create/Edit Modal */}
			<AlertRuleModal
				opened={opened}
				onClose={close}
				rule={selectedRule}
				isEditing={isEditing}
				onSuccess={() =>
				{
					close();
					fetchRules();
				}}
			/>
		</Container>
	);
}

interface AlertRuleModalProps
{
	opened: boolean;
	onClose: () => void;
	rule: AlertRuleType | null;
	isEditing: boolean;
	onSuccess: () => void;
}

function AlertRuleModal({
	opened, onClose, rule, isEditing, onSuccess,
}: AlertRuleModalProps)
{
	const [formData, setFormData] = useState<Partial<AlertRuleCreateRequestType>>({
		name: '',
		description: '',
		enabled: true,
		severity: 'medium',
		conditions: [],
		logicalOperator: 'AND',
		dependencies: [],
		suppressionRules: {
			maintenanceWindows: true,
			dependencyBased: false,
			smartGrouping: false,
		},
		createdBy: 'admin',
		tags: [],
	});
	const [loading, setLoading] = useState(false);

	useEffect(() =>
	{
		if (rule && isEditing)
		{
			setFormData({
				name: rule.name,
				description: rule.description,
				enabled: rule.enabled,
				severity: rule.severity,
				conditions: rule.conditions,
				logicalOperator: rule.logicalOperator,
				timeBasedConditions: rule.timeBasedConditions,
				dependencies: rule.dependencies,
				suppressionRules: rule.suppressionRules,
				createdBy: rule.createdBy,
				tags: rule.tags,
			});
		}
		else
		{
			setFormData({
				name: '',
				description: '',
				enabled: true,
				severity: 'medium',
				conditions: [],
				logicalOperator: 'AND',
				dependencies: [],
				suppressionRules: {
					maintenanceWindows: true,
					dependencyBased: false,
					smartGrouping: false,
				},
				createdBy: 'admin',
				tags: [],
			});
		}
	}, [rule, isEditing, opened]);

	const handleSubmit = async () =>
	{
		try
		{
			setLoading(true);

			const url = isEditing ? `/api/alerts/rules/${rule!.id}` : '/api/alerts/rules';
			const method = isEditing ? 'PUT' : 'POST';

			const response = await fetch(url, {
				method,
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(formData),
			});

			if (response.ok)
			{
				notifications.show({
					title: 'Success',
					message: `Alert rule ${isEditing ? 'updated' : 'created'} successfully`,
					color: 'green',
				});
				onSuccess();
			}
		}
		catch (error)
		{
			clientLogger.error('Error saving alert rule:', error);
			notifications.show({
				title: 'Error',
				message: 'Failed to save alert rule',
				color: 'red',
			});
		}
		finally
		{
			setLoading(false);
		}
	};

	const addCondition = () =>
	{
		const newCondition: AlertConditionType = {
			id: `cond-${Date.now()}`,
			metric: 'response_time',
			operator: '>',
			threshold: 1000,
			duration: 5,
		};

		setFormData(prev => ({
			...prev,
			conditions: [...(prev.conditions || []), newCondition],
		}));
	};

	const updateCondition = (index: number, updates: Partial<AlertConditionType>) =>
	{
		setFormData(prev => ({
			...prev,
			conditions: prev.conditions?.map((condition, i) => (i === index ? { ...condition, ...updates } : condition)) || [],
		}));
	};

	const removeCondition = (index: number) =>
	{
		setFormData(prev => ({
			...prev,
			conditions: prev.conditions?.filter((_, i) => i !== index) || [],
		}));
	};

	return (
		<Modal
			opened={opened}
			onClose={onClose}
			title={isEditing ? 'Edit Alert Rule' : 'Create Alert Rule'}
			size="lg"
		>
			<Stack>
				<TextInput
					label="Rule Name"
					placeholder="Enter rule name..."
					value={formData.name}
					onChange={event => setFormData(prev => ({ ...prev, name: event.currentTarget.value }))}
					required
				/>

				<Textarea
					label="Description"
					placeholder="Describe what this rule monitors..."
					value={formData.description}
					onChange={event => setFormData(prev => ({ ...prev, description: event.currentTarget.value }))}
					rows={3}
				/>

				<Group grow>
					<Select
						label="Severity"
						value={formData.severity}
						onChange={value => setFormData(prev => ({ ...prev, severity: value as AlertSeverityType }))}
						data={SEVERITY_OPTIONS}
						required
					/>
					<Select
						label="Logical Operator"
						value={formData.logicalOperator}
						onChange={value => setFormData(prev => ({ ...prev, logicalOperator: value as 'AND' | 'OR' }))}
						data={[
							{ value: 'AND', label: 'AND (all conditions must be true)' },
							{ value: 'OR', label: 'OR (any condition can be true)' },
						]}
					/>
				</Group>

				<Divider label="Conditions" labelPosition="left" />

				{formData.conditions?.map((condition, index) => (
					<Paper key={condition.id} p="md" withBorder>
						<Group justify="space-between" mb="sm">
							<Text fw={600} size="sm">Condition {index + 1}</Text>
							<ActionIcon
								color="red"
								variant="light"
								onClick={() => removeCondition(index)}
							>
								<IconTrash size={16} />
							</ActionIcon>
						</Group>

						<SimpleGrid cols={2}>
							<Select
								label="Metric"
								value={condition.metric}
								onChange={value => updateCondition(index, { metric: value as AlertMetricType })}
								data={METRIC_OPTIONS}
							/>
							<Select
								label="Operator"
								value={condition.operator}
								onChange={value => updateCondition(index, { operator: value as AlertOperatorType })}
								data={OPERATOR_OPTIONS}
							/>
							<NumberInput
								label="Threshold"
								value={condition.threshold}
								onChange={value => updateCondition(index, { threshold: Number(value) })}
							/>
							<NumberInput
								label="Duration (minutes)"
								value={condition.duration}
								onChange={value => updateCondition(index, { duration: Number(value) })}
								min={1}
							/>
							<Select
								label="Service (optional)"
								value={condition.service || ''}
								onChange={value => updateCondition(index, { service: value || undefined })}
								data={SERVICE_OPTIONS}
								clearable
							/>
							<TextInput
								label="Resource (optional)"
								value={condition.resource || ''}
								onChange={event => updateCondition(index, { resource: event.currentTarget.value || undefined })}
								placeholder="e.g., scylla, redis..."
							/>
						</SimpleGrid>
					</Paper>
				))}

				<Button variant="light" onClick={addCondition}>
					Add Condition
				</Button>

				<Divider label="Settings" labelPosition="left" />

				<MultiSelect
					label="Tags"
					placeholder="Add tags..."
					value={formData.tags}
					onChange={value => setFormData(prev => ({ ...prev, tags: value }))}
					data={[
						{ value: 'production', label: 'Production' },
						{ value: 'critical', label: 'Critical' },
						{ value: 'database', label: 'Database' },
						{ value: 'performance', label: 'Performance' },
						{ value: 'security', label: 'Security' },
					]}
					searchable
					creatable
					getCreateLabel={query => `+ Create ${query}`}
				/>

				<Stack gap="xs">
					<Text fw={600} size="sm">Suppression Rules</Text>
					<Checkbox
						label="Suppress during maintenance windows"
						checked={formData.suppressionRules?.maintenanceWindows}
						onChange={event => setFormData(prev => ({
							...prev,
							suppressionRules: {
								...prev.suppressionRules,
								maintenanceWindows: event.currentTarget.checked,
							},
						}))}
					/>
					<Checkbox
						label="Suppress based on dependencies"
						checked={formData.suppressionRules?.dependencyBased}
						onChange={event => setFormData(prev => ({
							...prev,
							suppressionRules: {
								...prev.suppressionRules,
								dependencyBased: event.currentTarget.checked,
							},
						}))}
					/>
					<Checkbox
						label="Enable smart grouping"
						checked={formData.suppressionRules?.smartGrouping}
						onChange={event => setFormData(prev => ({
							...prev,
							suppressionRules: {
								...prev.suppressionRules,
								smartGrouping: event.currentTarget.checked,
							},
						}))}
					/>
				</Stack>

				<Switch
					label="Enable this rule"
					checked={formData.enabled}
					onChange={event => setFormData(prev => ({ ...prev, enabled: event.currentTarget.checked }))}
				/>

				<Group justify="flex-end">
					<Button variant="light" onClick={onClose}>
						Cancel
					</Button>
					<Button
						onClick={handleSubmit}
						loading={loading}
						disabled={!formData.name || !formData.conditions?.length}
					>
						{isEditing ? 'Update Rule' : 'Create Rule'}
					</Button>
				</Group>
			</Stack>
		</Modal>
	);
}
