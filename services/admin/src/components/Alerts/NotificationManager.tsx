'use client';

import {
	Con<PERSON>er,
	Card,
	Text,
	Button,
	Group,
	Stack,
	Badge,
	ActionIcon,
	Modal,
	TextInput,
	Textarea,
	Select,
	Switch,
	Alert,
	Loader,
	Center,
	Title,
	Tabs,
	Code,
	Paper,
	SimpleGrid,
	Chip,
	Progress,
	ThemeIcon,
	JsonInput,
	NumberInput,
	Accordion,
	Timeline,
	RingProgress,
	Menu,
	Checkbox,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { clientLogger } from '@/lib/logger';
import { notifications } from '@mantine/notifications';
import {
	IconPlus,
	IconEdit,
	IconTrash,
	IconMail,
	IconBrandSlack,
	IconPhone,
	IconWebhook,
	IconBrandMicrosoft,
	IconBug,
	IconTicket,
	IconEye,
	IconCopy,
	IconTestPipe,
	IconRefresh,
	IconChartBar,
	IconTrendingUp,
	IconTrendingDown,
	IconUsers,
	IconSend,
	IconAlertCircle,
	IconShield,
	IconClock,
	IconCheck,
	IconX,
	IconDots,
	IconSettings,
} from '@tabler/icons-react';
import React, { useState, useEffect } from 'react';
import {
	LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell,
} from 'recharts';

import type {
	NotificationTemplateType,
	NotificationTemplateCreateRequestType,
	NotificationChannelType,
	ExternalIntegrationType,
	AlertEscalationPolicyType,
	AlertAnalyticsType,
	AlertTestResultType,
} from '@/types/alerts';

const CHANNEL_ICONS = {
	email: IconMail,
	slack: IconBrandSlack,
	sms: IconPhone,
	webhook: IconWebhook,
	teams: IconBrandMicrosoft,
	pagerduty: IconBug,
	jira: IconTicket,
};

const CHANNEL_COLORS = {
	email: 'blue',
	slack: 'green',
	sms: 'orange',
	webhook: 'violet',
	teams: 'indigo',
	pagerduty: 'red',
	jira: 'cyan',
};

const TEMPLATE_VARIABLES = [
	'ruleName', 'severity', 'serviceName', 'message', 'triggeredAt',
	'details', 'alertId', 'correlationId', 'escalationLevel',
];

export default function NotificationManager()
{
	const [templates, setTemplates] = useState<NotificationTemplateType[]>([]);
	const [integrations, setIntegrations] = useState<ExternalIntegrationType[]>([]);
	const [analytics, setAnalytics] = useState<AlertAnalyticsType | null>(null);
	const [loading, setLoading] = useState(true);
	const [selectedTemplate, setSelectedTemplate] = useState<NotificationTemplateType | null>(null);
	const [selectedIntegration, setSelectedIntegration] = useState<ExternalIntegrationType | null>(null);
	const [isEditing, setIsEditing] = useState(false);
	const [activeTab, setActiveTab] = useState('templates');

	const [templateModalOpened, { open: openTemplateModal, close: closeTemplateModal }] = useDisclosure(false);
	const [integrationModalOpened, { open: openIntegrationModal, close: closeIntegrationModal }] = useDisclosure(false);
	const [previewModalOpened, { open: openPreviewModal, close: closePreviewModal }] = useDisclosure(false);

	useEffect(() =>
	{
		fetchData();
	}, []);

	const fetchData = async () =>
	{
		try
		{
			setLoading(true);
			const [templatesResponse, integrationsResponse, analyticsResponse] = await Promise.all([
				fetch('/api/alerts/notifications'),
				fetch('/api/alerts/integrations'),
				fetch('/api/alerts/analytics?timeframe=24h'),
			]);

			if (templatesResponse.ok && integrationsResponse.ok)
			{
				const templatesData = await templatesResponse.json();
				const integrationsData = await integrationsResponse.json();

				setTemplates(templatesData.templates);
				setIntegrations(integrationsData.integrations);
			}

			if (analyticsResponse.ok)
			{
				const analyticsData = await analyticsResponse.json();
				setAnalytics(analyticsData);
			}
		}
		catch (error)
		{
			clientLogger.error('Error fetching notification data:', error);
			notifications.show({
				title: 'Error',
				message: 'Failed to fetch notification data',
				color: 'red',
			});
		}
		finally
		{
			setLoading(false);
		}
	};

	const handleCreateTemplate = () =>
	{
		setSelectedTemplate(null);
		setIsEditing(false);
		openTemplateModal();
	};

	const handleEditTemplate = (template: NotificationTemplateType) =>
	{
		setSelectedTemplate(template);
		setIsEditing(true);
		openTemplateModal();
	};

	const handleDeleteTemplate = async (templateId: string) =>
	{
		try
		{
			const response = await fetch(`/api/alerts/notifications/${templateId}`, {
				method: 'DELETE',
			});

			if (response.ok)
			{
				notifications.show({
					title: 'Success',
					message: 'Template deleted successfully',
					color: 'green',
				});
				fetchData();
			}
		}
		catch (error)
		{
			clientLogger.error('Error deleting template:', error);
			notifications.show({
				title: 'Error',
				message: 'Failed to delete template',
				color: 'red',
			});
		}
	};

	const handleTestTemplate = async (template: NotificationTemplateType) =>
	{
		try
		{
			const response = await fetch('/api/alerts/test', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					templateId: template.id,
					testType: 'delivery_test',
				}),
			});

			if (response.ok)
			{
				notifications.show({
					title: 'Test Started',
					message: 'Template test notification sent',
					color: 'blue',
				});
			}
		}
		catch (error)
		{
			clientLogger.error('Error testing template:', error);
			notifications.show({
				title: 'Error',
				message: 'Failed to test template',
				color: 'red',
			});
		}
	};

	const getChannelIcon = (channel: NotificationChannelType) =>
	{
		const Icon = CHANNEL_ICONS[channel];
		return Icon ? <Icon size={16} /> : null;
	};

	const getChannelColor = (channel: NotificationChannelType) => CHANNEL_COLORS[channel] || 'gray';

	const renderTemplatePreview = (template: NotificationTemplateType) =>
	{
		const mockData = {
			ruleName: 'High Error Rate',
			severity: 'critical',
			serviceName: 'crawler',
			message: 'Error rate exceeded 5% threshold',
			triggeredAt: new Date().toISOString(),
			details: JSON.stringify({ errorRate: 7.2, threshold: 5.0 }, null, 2),
			alertId: 'alert-123',
			correlationId: 'corr-456',
			escalationLevel: 1,
		};

		let renderedBody = template.body;
		Object.entries(mockData).forEach(([key, value]) =>
		{
			const regex = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
			renderedBody = renderedBody.replace(regex, String(value));
		});

		return renderedBody;
	};

	if (loading)
	{
		return (
			<Center h={400}>
				<Stack align="center">
					<Loader size="lg" />
					<Text>Loading notification settings...</Text>
				</Stack>
			</Center>
		);
	}

	return (
		<Container size="xl" py="md">
			<Group justify="space-between" mb="lg">
				<Title order={2}>Notification Management</Title>
			</Group>

			<Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'templates')}>
				<Tabs.List>
					<Tabs.Tab value="templates">Templates</Tabs.Tab>
					<Tabs.Tab value="integrations">Integrations</Tabs.Tab>
					<Tabs.Tab value="channels">Channels</Tabs.Tab>
					<Tabs.Tab value="analytics">Analytics</Tabs.Tab>
					<Tabs.Tab value="testing">Testing</Tabs.Tab>
				</Tabs.List>

				<Tabs.Panel value="templates" pt="md">
					<Group justify="space-between" mb="md">
						<Text size="lg" fw={600}>Notification Templates</Text>
						<Button
							leftSection={<IconPlus size={16} />}
							onClick={handleCreateTemplate}
						>
							Create Template
						</Button>
					</Group>

					<SimpleGrid cols={{ base: 1, md: 2, lg: 3 }}>
						{templates.map(template => (
							<Card key={template.id} withBorder>
								<Group justify="space-between" mb="sm">
									<Group>
										<Badge
											color={getChannelColor(template.channel)}
											variant="light"
											leftSection={getChannelIcon(template.channel)}
										>
											{template.channel}
										</Badge>
										{template.isDefault && (
											<Badge size="xs" color="blue">Default</Badge>
										)}
									</Group>
									<Group gap="xs">
										<ActionIcon
											variant="light"
											onClick={() =>
											{
												setSelectedTemplate(template);
												openPreviewModal();
											}}
										>
											<IconEye size={16} />
										</ActionIcon>
										<ActionIcon
											variant="light"
											onClick={() => handleEditTemplate(template)}
										>
											<IconEdit size={16} />
										</ActionIcon>
										<ActionIcon
											variant="light"
											color="blue"
											onClick={() => handleTestTemplate(template)}
										>
											<IconTestPipe size={16} />
										</ActionIcon>
										<ActionIcon
											variant="light"
											color="red"
											onClick={() => handleDeleteTemplate(template.id)}
										>
											<IconTrash size={16} />
										</ActionIcon>
									</Group>
								</Group>

								<Text fw={600} mb="xs">{template.name}</Text>

								{template.subject && (
									<Text size="sm" c="dimmed" mb="xs">
										Subject: {template.subject}
									</Text>
								)}

								<Text size="sm" c="dimmed" lineClamp={3} mb="sm">
									{template.body}
								</Text>

								<Group gap="xs">
									{template.variables.slice(0, 3).map(variable => (
										<Chip key={variable} size="xs" variant="outline">
											{variable}
										</Chip>
									))}
									{template.variables.length > 3 && (
										<Text size="xs" c="dimmed">
											+{template.variables.length - 3} more
										</Text>
									)}
								</Group>
							</Card>
						))}
					</SimpleGrid>

					{templates.length === 0 && (
						<Center py="xl">
							<Stack align="center">
								<IconMail size={48} color="gray" />
								<Text c="dimmed">No notification templates found</Text>
								<Button variant="light" onClick={handleCreateTemplate}>
									Create your first template
								</Button>
							</Stack>
						</Center>
					)}
				</Tabs.Panel>

				<Tabs.Panel value="integrations" pt="md">
					<Group justify="space-between" mb="md">
						<Text size="lg" fw={600}>External Integrations</Text>
						<Button
							leftSection={<IconPlus size={16} />}
							onClick={() =>
							{
								setSelectedIntegration(null);
								setIsEditing(false);
								openIntegrationModal();
							}}
						>
							Add Integration
						</Button>
					</Group>

					<SimpleGrid cols={{ base: 1, md: 2 }}>
						{integrations.map(integration => (
							<Card key={integration.id} withBorder>
								<Group justify="space-between" mb="sm">
									<Group>
										<Badge
											color={getChannelColor(integration.type as NotificationChannelType)}
											variant="light"
											leftSection={getChannelIcon(integration.type as NotificationChannelType)}
										>
											{integration.type}
										</Badge>
										<Badge
											color={integration.status === 'connected' ? 'green' : 'red'}
											variant="light"
										>
											{integration.status}
										</Badge>
									</Group>
									<Switch
										checked={integration.enabled}
										onChange={(event) =>
										{
											// Handle toggle integration
											clientLogger.info('Toggle integration:', { integrationId: integration.id, enabled: event.currentTarget.checked });
										}}
										size="sm"
									/>
								</Group>

								<Text fw={600} mb="xs">{integration.name}</Text>

								{integration.lastSync && (
									<Text size="sm" c="dimmed" mb="sm">
										Last sync: {new Date(integration.lastSync).toLocaleString()}
									</Text>
								)}

								{integration.errorMessage && (
									<Alert color="red" size="sm" mb="sm">
										{integration.errorMessage}
									</Alert>
								)}

								<Group justify="flex-end">
									<ActionIcon
										variant="light"
										onClick={() =>
										{
											setSelectedIntegration(integration);
											setIsEditing(true);
											openIntegrationModal();
										}}
									>
										<IconEdit size={16} />
									</ActionIcon>
									<ActionIcon
										variant="light"
										color="blue"
										onClick={() =>
										{
											// Handle test integration
											clientLogger.info('Test integration:', { integrationId: integration.id });
										}}
									>
										<IconTestPipe size={16} />
									</ActionIcon>
								</Group>
							</Card>
						))}
					</SimpleGrid>

					{integrations.length === 0 && (
						<Center py="xl">
							<Stack align="center">
								<IconWebhook size={48} color="gray" />
								<Text c="dimmed">No integrations configured</Text>
								<Button
									variant="light"
									onClick={() =>
									{
										setSelectedIntegration(null);
										setIsEditing(false);
										openIntegrationModal();
									}}
								>
									Add your first integration
								</Button>
							</Stack>
						</Center>
					)}
				</Tabs.Panel>

				<Tabs.Panel value="channels" pt="md">
					<Text size="lg" fw={600} mb="md">Notification Channels</Text>

					<SimpleGrid cols={{ base: 1, sm: 2, md: 3 }}>
						{Object.entries(CHANNEL_ICONS).map(([channel, Icon]) => (
							<Paper key={channel} p="md" withBorder>
								<Group mb="sm">
									<Icon size={24} />
									<Text fw={600} tt="capitalize">{channel}</Text>
								</Group>

								<Text size="sm" c="dimmed" mb="sm">
									{getChannelDescription(channel as NotificationChannelType)}
								</Text>

								<Group justify="space-between">
									<Badge
										color={getChannelColor(channel as NotificationChannelType)}
										variant="light"
									>
										{templates.filter(t => t.channel === channel).length} templates
									</Badge>
									<Badge
										color={integrations.some(i => i.type === channel && i.enabled) ? 'green' : 'gray'}
										variant="light"
									>
										{integrations.some(i => i.type === channel && i.enabled) ? 'Configured' : 'Not configured'}
									</Badge>
								</Group>
							</Paper>
						))}
					</SimpleGrid>
				</Tabs.Panel>

				<Tabs.Panel value="analytics" pt="md">
					<NotificationAnalytics analytics={analytics} />
				</Tabs.Panel>

				<Tabs.Panel value="testing" pt="md">
					<NotificationTesting integrations={integrations} onTest={handleTestIntegration} />
				</Tabs.Panel>
			</Tabs>

			{/* Template Modal */}
			<NotificationTemplateModal
				opened={templateModalOpened}
				onClose={closeTemplateModal}
				template={selectedTemplate}
				isEditing={isEditing}
				onSuccess={() =>
				{
					closeTemplateModal();
					fetchData();
				}}
			/>

			{/* Integration Modal */}
			<IntegrationModal
				opened={integrationModalOpened}
				onClose={closeIntegrationModal}
				integration={selectedIntegration}
				isEditing={isEditing}
				onSuccess={() =>
				{
					closeIntegrationModal();
					fetchData();
				}}
			/>

			{/* Preview Modal */}
			<Modal
				opened={previewModalOpened}
				onClose={closePreviewModal}
				title="Template Preview"
				size="lg"
			>
				{selectedTemplate && (
					<Stack>
						<Group>
							<Badge
								color={getChannelColor(selectedTemplate.channel)}
								variant="light"
								leftSection={getChannelIcon(selectedTemplate.channel)}
							>
								{selectedTemplate.channel}
							</Badge>
							<Text fw={600}>{selectedTemplate.name}</Text>
						</Group>

						{selectedTemplate.subject && (
							<div>
								<Text size="sm" fw={600} mb="xs">Subject:</Text>
								<Code block>{selectedTemplate.subject}</Code>
							</div>
						)}

						<div>
							<Text size="sm" fw={600} mb="xs">Body:</Text>
							<Code block>{selectedTemplate.body}</Code>
						</div>

						<div>
							<Text size="sm" fw={600} mb="xs">Preview with sample data:</Text>
							<Paper p="md" withBorder>
								<pre style={{ whiteSpace: 'pre-wrap', margin: 0 }}>
									{renderTemplatePreview(selectedTemplate)}
								</pre>
							</Paper>
						</div>

						<div>
							<Text size="sm" fw={600} mb="xs">Available variables:</Text>
							<Group gap="xs">
								{TEMPLATE_VARIABLES.map(variable => (
									<Chip key={variable} size="xs" variant="outline">
										{`{{${variable}}}`}
									</Chip>
								))}
							</Group>
						</div>
					</Stack>
				)}
			</Modal>
		</Container>
	);
}

function getChannelDescription(channel: NotificationChannelType): string
{
	switch (channel)
	{
		case 'email':
			return 'Send notifications via email with rich formatting support';
		case 'slack':
			return 'Post messages to Slack channels with interactive elements';
		case 'sms':
			return 'Send text messages for urgent alerts';
		case 'webhook':
			return 'HTTP POST requests to custom endpoints';
		case 'teams':
			return 'Microsoft Teams channel notifications';
		case 'pagerduty':
			return 'Create incidents in PagerDuty for on-call management';
		case 'jira':
			return 'Automatically create JIRA tickets for tracking';
		default:
			return 'Notification channel';
	}
}

// Template Modal Component
interface NotificationTemplateModalProps
{
	opened: boolean;
	onClose: () => void;
	template: NotificationTemplateType | null;
	isEditing: boolean;
	onSuccess: () => void;
}

function NotificationTemplateModal({
	opened, onClose, template, isEditing, onSuccess,
}: NotificationTemplateModalProps)
{
	const [formData, setFormData] = useState<Partial<NotificationTemplateCreateRequestType>>({
		name: '',
		channel: 'email',
		subject: '',
		body: '',
		isDefault: false,
	});
	const [loading, setLoading] = useState(false);

	useEffect(() =>
	{
		if (template && isEditing)
		{
			setFormData({
				name: template.name,
				channel: template.channel,
				subject: template.subject,
				body: template.body,
				isDefault: template.isDefault,
			});
		}
		else
		{
			setFormData({
				name: '',
				channel: 'email',
				subject: '',
				body: '',
				isDefault: false,
			});
		}
	}, [template, isEditing, opened]);

	const handleSubmit = async () =>
	{
		try
		{
			setLoading(true);

			const url = isEditing ? `/api/alerts/notifications/${template!.id}` : '/api/alerts/notifications';
			const method = isEditing ? 'PUT' : 'POST';

			const response = await fetch(url, {
				method,
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(formData),
			});

			if (response.ok)
			{
				notifications.show({
					title: 'Success',
					message: `Template ${isEditing ? 'updated' : 'created'} successfully`,
					color: 'green',
				});
				onSuccess();
			}
		}
		catch (error)
		{
			clientLogger.error('Error saving template:', error);
			notifications.show({
				title: 'Error',
				message: 'Failed to save template',
				color: 'red',
			});
		}
		finally
		{
			setLoading(false);
		}
	};

	return (
		<Modal
			opened={opened}
			onClose={onClose}
			title={isEditing ? 'Edit Template' : 'Create Template'}
			size="lg"
		>
			<Stack>
				<TextInput
					label="Template Name"
					placeholder="Enter template name..."
					value={formData.name}
					onChange={event => setFormData(prev => ({ ...prev, name: event.currentTarget.value }))}
					required
				/>

				<Select
					label="Channel"
					value={formData.channel}
					onChange={value => setFormData(prev => ({ ...prev, channel: value as NotificationChannelType }))}
					data={[
						{ value: 'email', label: 'Email' },
						{ value: 'slack', label: 'Slack' },
						{ value: 'sms', label: 'SMS' },
						{ value: 'webhook', label: 'Webhook' },
						{ value: 'teams', label: 'Microsoft Teams' },
						{ value: 'pagerduty', label: 'PagerDuty' },
						{ value: 'jira', label: 'JIRA' },
					]}
					required
				/>

				{(formData.channel === 'email' || formData.channel === 'teams') && (
					<TextInput
						label="Subject"
						placeholder="Enter subject template..."
						value={formData.subject}
						onChange={event => setFormData(prev => ({ ...prev, subject: event.currentTarget.value }))}
					/>
				)}

				<Textarea
					label="Body Template"
					placeholder="Enter notification body template..."
					value={formData.body}
					onChange={event => setFormData(prev => ({ ...prev, body: event.currentTarget.value }))}
					rows={8}
					required
				/>

				<Alert color="blue" title="Available Variables">
					<Group gap="xs">
						{TEMPLATE_VARIABLES.map(variable => (
							<Chip key={variable} size="xs" variant="outline">
								{`{{${variable}}}`}
							</Chip>
						))}
					</Group>
				</Alert>

				<Switch
					label="Set as default template for this channel"
					checked={formData.isDefault}
					onChange={event => setFormData(prev => ({ ...prev, isDefault: event.currentTarget.checked }))}
				/>

				<Group justify="flex-end">
					<Button variant="light" onClick={onClose}>
						Cancel
					</Button>
					<Button
						onClick={handleSubmit}
						loading={loading}
						disabled={!formData.name || !formData.body}
					>
						{isEditing ? 'Update Template' : 'Create Template'}
					</Button>
				</Group>
			</Stack>
		</Modal>
	);
}

// Integration Modal Component
interface IntegrationModalProps
{
	opened: boolean;
	onClose: () => void;
	integration: ExternalIntegrationType | null;
	isEditing: boolean;
	onSuccess: () => void;
}

function IntegrationModal({
	opened, onClose, integration, isEditing, onSuccess,
}: IntegrationModalProps)
{
	const [formData, setFormData] = useState({
		type: 'slack',
		name: '',
		config: {},
		enabled: true,
	});
	const [loading, setLoading] = useState(false);

	useEffect(() =>
	{
		if (integration && isEditing)
		{
			setFormData({
				type: integration.type,
				name: integration.name,
				config: integration.config,
				enabled: integration.enabled,
			});
		}
		else
		{
			setFormData({
				type: 'slack',
				name: '',
				config: {},
				enabled: true,
			});
		}
	}, [integration, isEditing, opened]);

	const handleSubmit = async () =>
	{
		try
		{
			setLoading(true);

			const url = isEditing ? `/api/alerts/integrations/${integration!.id}` : '/api/alerts/integrations';
			const method = isEditing ? 'PUT' : 'POST';

			const response = await fetch(url, {
				method,
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(formData),
			});

			if (response.ok)
			{
				notifications.show({
					title: 'Success',
					message: `Integration ${isEditing ? 'updated' : 'created'} successfully`,
					color: 'green',
				});
				onSuccess();
			}
		}
		catch (error)
		{
			clientLogger.error('Error saving integration:', error);
			notifications.show({
				title: 'Error',
				message: 'Failed to save integration',
				color: 'red',
			});
		}
		finally
		{
			setLoading(false);
		}
	};

	return (
		<Modal
			opened={opened}
			onClose={onClose}
			title={isEditing ? 'Edit Integration' : 'Add Integration'}
			size="lg"
		>
			<Stack>
				<Select
					label="Integration Type"
					value={formData.type}
					onChange={value => setFormData(prev => ({ ...prev, type: value || 'slack' }))}
					data={[
						{ value: 'slack', label: 'Slack' },
						{ value: 'teams', label: 'Microsoft Teams' },
						{ value: 'pagerduty', label: 'PagerDuty' },
						{ value: 'jira', label: 'JIRA' },
						{ value: 'webhook', label: 'Webhook' },
					]}
					required
				/>

				<TextInput
					label="Integration Name"
					placeholder="Enter integration name..."
					value={formData.name}
					onChange={event => setFormData(prev => ({ ...prev, name: event.currentTarget.value }))}
					required
				/>

				{/* Configuration fields based on integration type */}
				{formData.type === 'slack' && (
					<Stack>
						<TextInput
							label="Webhook URL"
							placeholder="https://hooks.slack.com/services/..."
							value={formData.config.webhookUrl || ''}
							onChange={event => setFormData(prev => ({
								...prev,
								config: { ...prev.config, webhookUrl: event.currentTarget.value },
							}))}
							required
						/>
						<TextInput
							label="Channel"
							placeholder="#alerts"
							value={formData.config.channel || ''}
							onChange={event => setFormData(prev => ({
								...prev,
								config: { ...prev.config, channel: event.currentTarget.value },
							}))}
						/>
					</Stack>
				)}

				{formData.type === 'pagerduty' && (
					<TextInput
						label="Integration Key"
						placeholder="Enter PagerDuty integration key..."
						value={formData.config.integrationKey || ''}
						onChange={event => setFormData(prev => ({
							...prev,
							config: { ...prev.config, integrationKey: event.currentTarget.value },
						}))}
						required
					/>
				)}

				<Switch
					label="Enable this integration"
					checked={formData.enabled}
					onChange={event => setFormData(prev => ({ ...prev, enabled: event.currentTarget.checked }))}
				/>

				<Group justify="flex-end">
					<Button variant="light" onClick={onClose}>
						Cancel
					</Button>
					<Button
						onClick={handleSubmit}
						loading={loading}
						disabled={!formData.name}
					>
						{isEditing ? 'Update Integration' : 'Add Integration'}
					</Button>
				</Group>
			</Stack>
		</Modal>
	);
}

// Notification Analytics Component
interface NotificationAnalyticsProps
{
	analytics: AlertAnalyticsType | null;
}

function NotificationAnalytics({ analytics }: NotificationAnalyticsProps)
{
	if (!analytics)
	{
		return (
			<Center h={300}>
				<Stack align="center">
					<Loader size="lg" />
					<Text>Loading analytics...</Text>
				</Stack>
			</Center>
		);
	}

	const effectivenessData = Object.entries(analytics.notificationEffectiveness).map(([channel, stats]) => ({
		channel,
		effectiveness: (stats.delivered / stats.sent) * 100,
		sent: stats.sent,
		delivered: stats.delivered,
		failed: stats.failed,
		responseTime: stats.responseTime,
	}));

	const COLORS = ['#339af0', '#51cf66', '#ffd43b', '#ff6b6b', '#9775fa', '#ff8cc8', '#69db7c'];

	return (
		<Stack>
			{/* Key Metrics */}
			<SimpleGrid cols={{ base: 2, sm: 4 }}>
				<Paper p="md" withBorder>
					<Group justify="space-between">
						<div>
							<Text size="xs" c="dimmed" tt="uppercase" fw={700}>
								Total Sent
							</Text>
							<Text fw={700} size="xl">
								{Object.values(analytics.notificationEffectiveness).reduce((sum, stats) => sum + stats.sent, 0)}
							</Text>
						</div>
						<ThemeIcon color="blue" variant="light" size="lg">
							<IconSend size={18} />
						</ThemeIcon>
					</Group>
				</Paper>

				<Paper p="md" withBorder>
					<Group justify="space-between">
						<div>
							<Text size="xs" c="dimmed" tt="uppercase" fw={700}>
								Delivered
							</Text>
							<Text fw={700} size="xl">
								{Object.values(analytics.notificationEffectiveness).reduce((sum, stats) => sum + stats.delivered, 0)}
							</Text>
						</div>
						<ThemeIcon color="green" variant="light" size="lg">
							<IconCheck size={18} />
						</ThemeIcon>
					</Group>
				</Paper>

				<Paper p="md" withBorder>
					<Group justify="space-between">
						<div>
							<Text size="xs" c="dimmed" tt="uppercase" fw={700}>
								Failed
							</Text>
							<Text fw={700} size="xl">
								{Object.values(analytics.notificationEffectiveness).reduce((sum, stats) => sum + stats.failed, 0)}
							</Text>
						</div>
						<ThemeIcon color="red" variant="light" size="lg">
							<IconX size={18} />
						</ThemeIcon>
					</Group>
				</Paper>

				<Paper p="md" withBorder>
					<Group justify="space-between">
						<div>
							<Text size="xs" c="dimmed" tt="uppercase" fw={700}>
								Avg Response
							</Text>
							<Text fw={700} size="xl">
								{Math.round(Object.values(analytics.notificationEffectiveness).reduce((sum, stats) => sum + stats.responseTime, 0) / Object.keys(analytics.notificationEffectiveness).length)}ms
							</Text>
						</div>
						<ThemeIcon color="violet" variant="light" size="lg">
							<IconClock size={18} />
						</ThemeIcon>
					</Group>
				</Paper>
			</SimpleGrid>

			{/* Charts */}
			<SimpleGrid cols={{ base: 1, lg: 2 }}>
				<Card withBorder>
					<Card.Section p="md" withBorder>
						<Text fw={600}>Channel Effectiveness</Text>
					</Card.Section>
					<Card.Section p="md" h={300}>
						<ResponsiveContainer width="100%" height="100%">
							<BarChart data={effectivenessData}>
								<CartesianGrid strokeDasharray="3 3" />
								<XAxis dataKey="channel" />
								<YAxis />
								<Tooltip formatter={(value, name) => [`${Number(value).toFixed(1)}%`, 'Effectiveness']} />
								<Bar dataKey="effectiveness" fill="#339af0" />
							</BarChart>
						</ResponsiveContainer>
					</Card.Section>
				</Card>

				<Card withBorder>
					<Card.Section p="md" withBorder>
						<Text fw={600}>Response Times by Channel</Text>
					</Card.Section>
					<Card.Section p="md" h={300}>
						<ResponsiveContainer width="100%" height="100%">
							<BarChart data={effectivenessData}>
								<CartesianGrid strokeDasharray="3 3" />
								<XAxis dataKey="channel" />
								<YAxis />
								<Tooltip formatter={(value, name) => [`${value}ms`, 'Response Time']} />
								<Bar dataKey="responseTime" fill="#51cf66" />
							</BarChart>
						</ResponsiveContainer>
					</Card.Section>
				</Card>
			</SimpleGrid>

			{/* Detailed Channel Stats */}
			<Card withBorder>
				<Card.Section p="md" withBorder>
					<Text fw={600}>Channel Performance Details</Text>
				</Card.Section>
				<Card.Section p="md">
					<Stack gap="md">
						{effectivenessData.map((channel, index) => (
							<Paper key={channel.channel} p="md" withBorder>
								<Group justify="space-between" mb="sm">
									<Group>
										<ThemeIcon color={COLORS[index % COLORS.length]} variant="light">
											{React.createElement(CHANNEL_ICONS[channel.channel as keyof typeof CHANNEL_ICONS] || IconSend, { size: 16 })}
										</ThemeIcon>
										<Text fw={600} tt="capitalize">{channel.channel}</Text>
									</Group>
									<Badge color={channel.effectiveness > 95 ? 'green' : channel.effectiveness > 85 ? 'yellow' : 'red'} variant="light">
										{channel.effectiveness.toFixed(1)}% effective
									</Badge>
								</Group>

								<SimpleGrid cols={4}>
									<div>
										<Text size="xs" c="dimmed">Sent</Text>
										<Text fw={600}>{channel.sent}</Text>
									</div>
									<div>
										<Text size="xs" c="dimmed">Delivered</Text>
										<Text fw={600} c="green">{channel.delivered}</Text>
									</div>
									<div>
										<Text size="xs" c="dimmed">Failed</Text>
										<Text fw={600} c="red">{channel.failed}</Text>
									</div>
									<div>
										<Text size="xs" c="dimmed">Avg Response</Text>
										<Text fw={600}>{channel.responseTime}ms</Text>
									</div>
								</SimpleGrid>

								<Progress
									value={channel.effectiveness}
									color={channel.effectiveness > 95 ? 'green' : channel.effectiveness > 85 ? 'yellow' : 'red'}
									size="sm"
									mt="sm"
								/>
							</Paper>
						))}
					</Stack>
				</Card.Section>
			</Card>
		</Stack>
	);
}

// Notification Testing Component
interface NotificationTestingProps
{
	integrations: ExternalIntegrationType[];
	onTest: (integration: ExternalIntegrationType) => void;
}

function NotificationTesting({ integrations, onTest }: NotificationTestingProps)
{
	const [testResults, setTestResults] = useState<Map<string, AlertTestResultType>>(new Map());
	const [runningTests, setRunningTests] = useState<Set<string>>(new Set());
	const [selectedIntegrations, setSelectedIntegrations] = useState<string[]>([]);

	const handleBulkTest = async () =>
	{
		const testIds = selectedIntegrations.length > 0 ? selectedIntegrations : integrations.filter(i => i.enabled).map(i => i.id);

		setRunningTests(new Set(testIds));

		for (const integrationId of testIds)
		{
			try
			{
				const response = await fetch('/api/alerts/test', {
					method: 'POST',
					headers: { 'Content-Type': 'application/json' },
					body: JSON.stringify({
						integrationId,
						testType: 'delivery_test',
					}),
				});

				if (response.ok)
				{
					const result = await response.json();
					setTestResults(prev => new Map(prev.set(integrationId, result)));
				}
			}
			catch (error)
			{
				clientLogger.error('Error testing integration:', error);
			}
		}

		setRunningTests(new Set());
	};

	const handleSingleTest = async (integration: ExternalIntegrationType) =>
	{
		setRunningTests(prev => new Set(prev.add(integration.id)));

		try
		{
			const response = await fetch('/api/alerts/test', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					integrationId: integration.id,
					testType: 'end_to_end',
				}),
			});

			if (response.ok)
			{
				const result = await response.json();
				setTestResults(prev => new Map(prev.set(integration.id, result)));
			}
		}
		catch (error)
		{
			clientLogger.error('Error testing integration:', error);
		}
		finally
		{
			setRunningTests((prev) =>
			{
				const newSet = new Set(prev);
				newSet.delete(integration.id);
				return newSet;
			});
		}
	};

	const getTestStatusColor = (status: string) =>
	{
		switch (status)
		{
			case 'passed':
				return 'green';
			case 'failed':
				return 'red';
			case 'running':
				return 'blue';
			default:
				return 'gray';
		}
	};

	return (
		<Stack>
			<Card withBorder>
				<Card.Section p="md" withBorder>
					<Group justify="space-between">
						<Text fw={600}>Test Framework</Text>
						<Group>
							<Button
								variant="light"
								leftSection={<IconTestPipe size={16} />}
								onClick={handleBulkTest}
								disabled={runningTests.size > 0}
							>
								Test Selected ({selectedIntegrations.length || integrations.filter(i => i.enabled).length})
							</Button>
						</Group>
					</Group>
				</Card.Section>

				<Card.Section p="md">
					<Stack gap="md">
						{integrations.map(integration => (
							<Paper key={integration.id} p="md" withBorder>
								<Group justify="space-between" mb="sm">
									<Group>
										<Checkbox
											checked={selectedIntegrations.includes(integration.id)}
											onChange={(event) =>
											{
												if (event.currentTarget.checked)
												{
													setSelectedIntegrations(prev => [...prev, integration.id]);
												}
												else
												{
													setSelectedIntegrations(prev => prev.filter(id => id !== integration.id));
												}
											}}
										/>
										<ThemeIcon variant="light">
											{React.createElement(CHANNEL_ICONS[integration.type as keyof typeof CHANNEL_ICONS] || IconSend, { size: 16 })}
										</ThemeIcon>
										<div>
											<Text fw={600}>{integration.name}</Text>
											<Text size="xs" c="dimmed" tt="capitalize">{integration.type}</Text>
										</div>
									</Group>

									<Group>
										{testResults.has(integration.id) && (
											<Badge color={getTestStatusColor(testResults.get(integration.id)!.status)} variant="light">
												{testResults.get(integration.id)!.status}
											</Badge>
										)}
										<Button
											size="xs"
											variant="light"
											leftSection={<IconTestPipe size={14} />}
											onClick={() => handleSingleTest(integration)}
											loading={runningTests.has(integration.id)}
											disabled={!integration.enabled}
										>
											Test
										</Button>
									</Group>
								</Group>

								{testResults.has(integration.id) && (
									<Accordion variant="contained">
										<Accordion.Item value="results">
											<Accordion.Control>
												<Text size="sm">Test Results</Text>
											</Accordion.Control>
											<Accordion.Panel>
												<Timeline>
													{testResults.get(integration.id)!.results.map((result, index) => (
														<Timeline.Item
															key={index}
															bullet={(
																<ThemeIcon
																	size="sm"
																	color={result.status === 'passed' ? 'green' : 'red'}
																	variant="light"
																>
																	{result.status === 'passed' ? <IconCheck size={12} /> : <IconX size={12} />}
																</ThemeIcon>
															)}
															title={result.step}
														>
															<Text size="sm" c="dimmed">{result.message}</Text>
															<Text size="xs" c="dimmed">Duration: {result.duration}ms</Text>
														</Timeline.Item>
													))}
												</Timeline>

												{testResults.get(integration.id)!.notifications.length > 0 && (
													<div>
														<Text size="sm" fw={600} mt="md" mb="xs">Notification Results:</Text>
														<Stack gap="xs">
															{testResults.get(integration.id)!.notifications.map((notification, index) => (
																<Group key={index} justify="space-between">
																	<Text size="sm">{notification.recipient}</Text>
																	<Group gap="xs">
																		<Badge
																			color={notification.delivered ? 'green' : 'red'}
																			variant="light"
																			size="sm"
																		>
																			{notification.delivered ? 'Delivered' : 'Failed'}
																		</Badge>
																		<Text size="xs" c="dimmed">{notification.responseTime}ms</Text>
																	</Group>
																</Group>
															))}
														</Stack>
													</div>
												)}
											</Accordion.Panel>
										</Accordion.Item>
									</Accordion>
								)}
							</Paper>
						))}
					</Stack>
				</Card.Section>
			</Card>

			{/* Test Templates */}
			<Card withBorder>
				<Card.Section p="md" withBorder>
					<Text fw={600}>Test Templates</Text>
				</Card.Section>
				<Card.Section p="md">
					<SimpleGrid cols={{ base: 1, md: 3 }}>
						<Paper p="md" withBorder>
							<Group mb="sm">
								<ThemeIcon color="blue" variant="light">
									<IconAlertCircle size={16} />
								</ThemeIcon>
								<Text fw={600}>Mock Alert</Text>
							</Group>
							<Text size="sm" c="dimmed" mb="sm">
								Test with a simulated critical alert
							</Text>
							<Button size="xs" variant="light" fullWidth>
								Run Mock Test
							</Button>
						</Paper>

						<Paper p="md" withBorder>
							<Group mb="sm">
								<ThemeIcon color="green" variant="light">
									<IconSend size={16} />
								</ThemeIcon>
								<Text fw={600}>Delivery Test</Text>
							</Group>
							<Text size="sm" c="dimmed" mb="sm">
								Test notification delivery only
							</Text>
							<Button size="xs" variant="light" fullWidth>
								Run Delivery Test
							</Button>
						</Paper>

						<Paper p="md" withBorder>
							<Group mb="sm">
								<ThemeIcon color="violet" variant="light">
									<IconShield size={16} />
								</ThemeIcon>
								<Text fw={600}>End-to-End</Text>
							</Group>
							<Text size="sm" c="dimmed" mb="sm">
								Complete alert lifecycle test
							</Text>
							<Button size="xs" variant="light" fullWidth>
								Run E2E Test
							</Button>
						</Paper>
					</SimpleGrid>
				</Card.Section>
			</Card>
		</Stack>
	);
}
