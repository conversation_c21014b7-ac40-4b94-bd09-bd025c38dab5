'use client';

import {
	Container,
	Card,
	Text,
	Group,
	Stack,
	Badge,
	Select,
	Button,
	SimpleGrid,
	Paper,
	Progress,
	Title,
	Tabs,
	Table,
	Center,
	Loader,
	Alert,
	ThemeIcon,
	RingProgress,
} from '@mantine/core';
import {
	IconTrendingUp,
	IconTrendingDown,
	IconClock,
	IconAlertTriangle,
	IconCheck,
	IconX,
	IconRefresh,
	IconDownload,
	IconChartBar,
	IconChartLine,
	IconChartPie,
} from '@tabler/icons-react';
import { useState, useEffect } from 'react';
import { clientLogger } from '@/lib/logger';
import {
	LineChart,
	Line,
	XAxis,
	YAxis,
	CartesianGrid,
	Tooltip,
	ResponsiveContainer,
	BarChart,
	Bar,
	PieChart,
	Pie,
	Cell,
	AreaChart,
	Area,
} from 'recharts';

import type { AlertAnalyticsType } from '@/types/alerts';

const SEVERITY_COLORS = {
	critical: '#fa5252',
	high: '#fd7e14',
	medium: '#fab005',
	low: '#51cf66',
	info: '#339af0',
};

const STATUS_COLORS = {
	active: '#fa5252',
	acknowledged: '#fab005',
	resolved: '#51cf66',
	silenced: '#868e96',
};

export default function AlertAnalytics()
{
	const [analytics, setAnalytics] = useState<AlertAnalyticsType | null>(null);
	const [loading, setLoading] = useState(true);
	const [timeframe, setTimeframe] = useState('24h');
	const [selectedServices, setSelectedServices] = useState<string[]>([]);
	const [activeTab, setActiveTab] = useState('overview');

	useEffect(() =>
	{
		fetchAnalytics();
	}, [timeframe, selectedServices]);

	const fetchAnalytics = async () =>
	{
		try
		{
			setLoading(true);
			const params = new URLSearchParams();
			params.append('timeframe', timeframe);

			if (selectedServices.length > 0)
			{
				params.append('services', selectedServices.join(','));
			}

			const response = await fetch(`/api/alerts/analytics?${params}`);
			if (response.ok)
			{
				const data = await response.json();
				setAnalytics(data);
			}
		}
		catch (error)
		{
			clientLogger.error('Error fetching analytics:', error);
		}
		finally
		{
			setLoading(false);
		}
	};

	const formatDuration = (minutes: number): string =>
	{
		if (minutes < 60)
		{
			return `${minutes}m`;
		}
		const hours = Math.floor(minutes / 60);
		const remainingMinutes = minutes % 60;
		return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
	};

	const formatPercentage = (value: number): string => `${(value * 100).toFixed(1)}%`;

	const getTrendIcon = (current: number, previous: number) =>
	{
		if (current > previous)
		{
			return <IconTrendingUp size={16} color="red" />;
		}
		if (current < previous)
		{
			return <IconTrendingDown size={16} color="green" />;
		}
		return null;
	};

	if (loading)
	{
		return (
			<Center h={400}>
				<Stack align="center">
					<Loader size="lg" />
					<Text>Loading analytics...</Text>
				</Stack>
			</Center>
		);
	}

	if (!analytics)
	{
		return (
			<Alert color="red" title="Error">
				Failed to load analytics data. Please try refreshing the page.
			</Alert>
		);
	}

	return (
		<Container size="xl" py="md">
			<Group justify="space-between" mb="lg">
				<Title order={2}>Alert Analytics</Title>
				<Group>
					<Select
						value={timeframe}
						onChange={value => setTimeframe(value || '24h')}
						data={[
							{ value: '1h', label: 'Last Hour' },
							{ value: '24h', label: 'Last 24 Hours' },
							{ value: '7d', label: 'Last 7 Days' },
							{ value: '30d', label: 'Last 30 Days' },
						]}
					/>
					<Button
						variant="light"
						leftSection={<IconRefresh size={16} />}
						onClick={fetchAnalytics}
					>
						Refresh
					</Button>
					<Button
						variant="light"
						leftSection={<IconDownload size={16} />}
					>
						Export
					</Button>
				</Group>
			</Group>

			{/* Key Metrics */}
			<SimpleGrid cols={{ base: 2, sm: 3, lg: 6 }} mb="xl">
				<Paper p="md" withBorder>
					<Group justify="space-between">
						<div>
							<Text size="xs" c="dimmed" tt="uppercase" fw={700}>
								Total Alerts
							</Text>
							<Text fw={700} size="xl">
								{analytics.totalAlerts}
							</Text>
						</div>
						<ThemeIcon color="blue" variant="light" size="lg">
							<IconChartBar size={18} />
						</ThemeIcon>
					</Group>
				</Paper>

				<Paper p="md" withBorder>
					<Group justify="space-between">
						<div>
							<Text size="xs" c="dimmed" tt="uppercase" fw={700}>
								MTTR
							</Text>
							<Text fw={700} size="xl">
								{formatDuration(analytics.mttr)}
							</Text>
						</div>
						<ThemeIcon color="orange" variant="light" size="lg">
							<IconClock size={18} />
						</ThemeIcon>
					</Group>
				</Paper>

				<Paper p="md" withBorder>
					<Group justify="space-between">
						<div>
							<Text size="xs" c="dimmed" tt="uppercase" fw={700}>
								MTBF
							</Text>
							<Text fw={700} size="xl">
								{formatDuration(analytics.mtbf)}
							</Text>
						</div>
						<ThemeIcon color="green" variant="light" size="lg">
							<IconTrendingUp size={18} />
						</ThemeIcon>
					</Group>
				</Paper>

				<Paper p="md" withBorder>
					<Group justify="space-between">
						<div>
							<Text size="xs" c="dimmed" tt="uppercase" fw={700}>
								Escalation Rate
							</Text>
							<Text fw={700} size="xl">
								{formatPercentage(analytics.escalationRate)}
							</Text>
						</div>
						<ThemeIcon
							color={analytics.escalationRate > 0.2 ? 'red' : 'green'}
							variant="light"
							size="lg"
						>
							<IconAlertTriangle size={18} />
						</ThemeIcon>
					</Group>
				</Paper>

				<Paper p="md" withBorder>
					<Group justify="space-between">
						<div>
							<Text size="xs" c="dimmed" tt="uppercase" fw={700}>
								False Positive Rate
							</Text>
							<Text fw={700} size="xl">
								{formatPercentage(analytics.falsePositiveRate)}
							</Text>
						</div>
						<ThemeIcon
							color={analytics.falsePositiveRate > 0.1 ? 'orange' : 'green'}
							variant="light"
							size="lg"
						>
							<IconX size={18} />
						</ThemeIcon>
					</Group>
				</Paper>

				<Paper p="md" withBorder>
					<Group justify="space-between">
						<div>
							<Text size="xs" c="dimmed" tt="uppercase" fw={700}>
								Active Alerts
							</Text>
							<Text fw={700} size="xl">
								{analytics.alertsByStatus.active}
							</Text>
						</div>
						<ThemeIcon color="red" variant="light" size="lg">
							<IconAlertTriangle size={18} />
						</ThemeIcon>
					</Group>
				</Paper>
			</SimpleGrid>

			<Tabs value={activeTab} onChange={setActiveTab}>
				<Tabs.List>
					<Tabs.Tab value="overview" leftSection={<IconChartLine size={16} />}>
						Overview
					</Tabs.Tab>
					<Tabs.Tab value="trends" leftSection={<IconTrendingUp size={16} />}>
						Trends
					</Tabs.Tab>
					<Tabs.Tab value="performance" leftSection={<IconClock size={16} />}>
						Performance
					</Tabs.Tab>
					<Tabs.Tab value="notifications" leftSection={<IconCheck size={16} />}>
						Notifications
					</Tabs.Tab>
				</Tabs.List>

				<Tabs.Panel value="overview" pt="md">
					<SimpleGrid cols={{ base: 1, lg: 2 }} mb="lg">
						<Card withBorder h={350}>
							<Card.Section p="md" withBorder>
								<Text fw={600}>Alerts by Status</Text>
							</Card.Section>
							<Card.Section p="md" h={280}>
								<ResponsiveContainer width="100%" height="100%">
									<PieChart>
										<Pie
											data={Object.entries(analytics.alertsByStatus).map(([status, count]) => ({
												name: status,
												value: count,
												color: STATUS_COLORS[status as keyof typeof STATUS_COLORS],
											}))}
											cx="50%"
											cy="50%"
											outerRadius={80}
											dataKey="value"
											label={({ name, value }) => `${name}: ${value}`}
										>
											{Object.entries(analytics.alertsByStatus).map(([status], index) => (
												<Cell
													key={`cell-${index}`}
													fill={STATUS_COLORS[status as keyof typeof STATUS_COLORS]}
												/>
											))}
										</Pie>
										<Tooltip />
									</PieChart>
								</ResponsiveContainer>
							</Card.Section>
						</Card>

						<Card withBorder h={350}>
							<Card.Section p="md" withBorder>
								<Text fw={600}>Alerts by Severity</Text>
							</Card.Section>
							<Card.Section p="md" h={280}>
								<ResponsiveContainer width="100%" height="100%">
									<BarChart data={Object.entries(analytics.alertsBySeverity).map(([severity, count]) => ({
										severity,
										count,
										color: SEVERITY_COLORS[severity as keyof typeof SEVERITY_COLORS],
									}))}
									>
										<CartesianGrid strokeDasharray="3 3" />
										<XAxis dataKey="severity" />
										<YAxis />
										<Tooltip />
										<Bar dataKey="count" fill="#339af0" />
									</BarChart>
								</ResponsiveContainer>
							</Card.Section>
						</Card>
					</SimpleGrid>

					<Card withBorder>
						<Card.Section p="md" withBorder>
							<Text fw={600}>Alerts by Service</Text>
						</Card.Section>
						<Card.Section p="md">
							<Stack gap="sm">
								{Object.entries(analytics.alertsByService)
									.sort(([,a], [,b]) => b - a)
									.map(([service, count]) =>
									{
										const percentage = (count / analytics.totalAlerts) * 100;
										return (
											<div key={service}>
												<Group justify="space-between" mb={5}>
													<Text size="sm" tt="capitalize">{service}</Text>
													<Text size="sm" fw={600}>{count} alerts</Text>
												</Group>
												<Progress value={percentage} color="blue" />
											</div>
										);
									})}
							</Stack>
						</Card.Section>
					</Card>
				</Tabs.Panel>

				<Tabs.Panel value="trends" pt="md">
					<Card withBorder h={500}>
						<Card.Section p="md" withBorder>
							<Text fw={600}>Alert Trends Over Time</Text>
						</Card.Section>
						<Card.Section p="md" h={430}>
							<ResponsiveContainer width="100%" height="100%">
								<AreaChart data={analytics.alertTrends}>
									<CartesianGrid strokeDasharray="3 3" />
									<XAxis
										dataKey="timestamp"
										tickFormatter={value => new Date(value).toLocaleTimeString([], {
											hour: '2-digit',
											minute: '2-digit',
										})}
									/>
									<YAxis />
									<Tooltip
										labelFormatter={value => new Date(value).toLocaleString()}
										formatter={(value, name) => [value, `${name} alerts`]}
									/>
									<Area
										type="monotone"
										dataKey="count"
										stackId="1"
										stroke="#339af0"
										fill="#339af0"
										fillOpacity={0.6}
									/>
								</AreaChart>
							</ResponsiveContainer>
						</Card.Section>
					</Card>
				</Tabs.Panel>

				<Tabs.Panel value="performance" pt="md">
					<SimpleGrid cols={{ base: 1, lg: 2 }} mb="lg">
						<Card withBorder>
							<Card.Section p="md" withBorder>
								<Text fw={600}>Top Alert Rules by Frequency</Text>
							</Card.Section>
							<Card.Section p="md">
								<Stack gap="sm">
									{analytics.topAlertRules.map((rule, index) => (
										<Paper key={rule.ruleId} p="sm" withBorder>
											<Group justify="space-between" mb="xs">
												<Text fw={600} size="sm">{rule.ruleName}</Text>
												<Badge variant="light">{rule.count} alerts</Badge>
											</Group>
											<Group justify="space-between">
												<Text size="xs" c="dimmed">
													Avg resolution: {formatDuration(rule.avgResolutionTime)}
												</Text>
												<Text size="xs" c="dimmed">
													Rank #{index + 1}
												</Text>
											</Group>
										</Paper>
									))}
								</Stack>
							</Card.Section>
						</Card>

						<Card withBorder>
							<Card.Section p="md" withBorder>
								<Text fw={600}>Performance Metrics</Text>
							</Card.Section>
							<Card.Section p="md">
								<Stack gap="lg">
									<div>
										<Group justify="space-between" mb="sm">
											<Text fw={600}>Mean Time to Resolution (MTTR)</Text>
											<Text fw={600}>{formatDuration(analytics.mttr)}</Text>
										</Group>
										<Progress
											value={Math.min((analytics.mttr / 120) * 100, 100)}
											color={analytics.mttr > 60 ? 'red' : analytics.mttr > 30 ? 'orange' : 'green'}
										/>
										<Text size="xs" c="dimmed" mt="xs">
											Target: &lt; 30 minutes
										</Text>
									</div>

									<div>
										<Group justify="space-between" mb="sm">
											<Text fw={600}>Mean Time Between Failures (MTBF)</Text>
											<Text fw={600}>{formatDuration(analytics.mtbf)}</Text>
										</Group>
										<Progress
											value={Math.min((analytics.mtbf / 480) * 100, 100)}
											color={analytics.mtbf < 120 ? 'red' : analytics.mtbf < 240 ? 'orange' : 'green'}
										/>
										<Text size="xs" c="dimmed" mt="xs">
											Target: &gt; 4 hours
										</Text>
									</div>

									<div>
										<Group justify="space-between" mb="sm">
											<Text fw={600}>Escalation Rate</Text>
											<Text fw={600}>{formatPercentage(analytics.escalationRate)}</Text>
										</Group>
										<Progress
											value={analytics.escalationRate * 100}
											color={analytics.escalationRate > 0.2 ? 'red' : analytics.escalationRate > 0.1 ? 'orange' : 'green'}
										/>
										<Text size="xs" c="dimmed" mt="xs">
											Target: &lt; 10%
										</Text>
									</div>

									<div>
										<Group justify="space-between" mb="sm">
											<Text fw={600}>False Positive Rate</Text>
											<Text fw={600}>{formatPercentage(analytics.falsePositiveRate)}</Text>
										</Group>
										<Progress
											value={analytics.falsePositiveRate * 100}
											color={analytics.falsePositiveRate > 0.1 ? 'red' : analytics.falsePositiveRate > 0.05 ? 'orange' : 'green'}
										/>
										<Text size="xs" c="dimmed" mt="xs">
											Target: &lt; 5%
										</Text>
									</div>
								</Stack>
							</Card.Section>
						</Card>
					</SimpleGrid>
				</Tabs.Panel>

				<Tabs.Panel value="notifications" pt="md">
					<Card withBorder>
						<Card.Section p="md" withBorder>
							<Text fw={600}>Notification Channel Effectiveness</Text>
						</Card.Section>
						<Card.Section>
							<Table>
								<Table.Thead>
									<Table.Tr>
										<Table.Th>Channel</Table.Th>
										<Table.Th>Sent</Table.Th>
										<Table.Th>Delivered</Table.Th>
										<Table.Th>Failed</Table.Th>
										<Table.Th>Success Rate</Table.Th>
										<Table.Th>Avg Response Time</Table.Th>
									</Table.Tr>
								</Table.Thead>
								<Table.Tbody>
									{Object.entries(analytics.notificationEffectiveness).map(([channel, stats]) =>
									{
										const successRate = (stats.delivered / stats.sent) * 100;
										return (
											<Table.Tr key={channel}>
												<Table.Td>
													<Text tt="capitalize" fw={600}>{channel}</Text>
												</Table.Td>
												<Table.Td>{stats.sent}</Table.Td>
												<Table.Td>{stats.delivered}</Table.Td>
												<Table.Td>
													<Text c={stats.failed > 0 ? 'red' : 'dimmed'}>
														{stats.failed}
													</Text>
												</Table.Td>
												<Table.Td>
													<Group gap="xs">
														<RingProgress
															size={30}
															thickness={4}
															sections={[
																{
																	value: successRate,
																	color: successRate > 95 ? 'green' : successRate > 90 ? 'orange' : 'red',
																},
															]}
														/>
														<Text size="sm" fw={600}>
															{successRate.toFixed(1)}%
														</Text>
													</Group>
												</Table.Td>
												<Table.Td>
													<Text size="sm">{stats.responseTime}ms</Text>
												</Table.Td>
											</Table.Tr>
										);
									})}
								</Table.Tbody>
							</Table>
						</Card.Section>
					</Card>
				</Tabs.Panel>
			</Tabs>
		</Container>
	);
}
