'use client';

import {
	Con<PERSON>er,
	Card,
	Text,
	Button,
	Group,
	Stack,
	Badge,
	Select,
	Textarea,
	JsonInput,
	Alert,
	Loader,
	Center,
	Title,
	Table,
	Progress,
	Timeline,
	Paper,
	SimpleGrid,
	Tabs,
	Code,
	ActionIcon,
	Modal,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { clientLogger } from '@/lib/logger';
import { notifications } from '@mantine/notifications';
import {
	IconTestPipe,
	IconPlayerPlay,
	IconCheck,
	IconX,
	IconClock,
	IconRefresh,
	IconEye,
	IconAlertTriangle,
	IconMail,
	IconBrandSlack,
	IconBrandMicrosoftTeams,
	IconPhone,
} from '@tabler/icons-react';
import { useState, useEffect } from 'react';

import type {
	AlertTestRequestType,
	AlertTestResultType,
	AlertRuleType,
	NotificationChannelType,
} from '@/types/alerts';

const TEST_TYPES = [
	{
		value: 'mock_trigger',
		label: 'Mock Trigger Test',
		description: 'Test alert rule evaluation with mock data',
	},
	{
		value: 'delivery_test',
		label: 'Delivery Test',
		description: 'Test notification delivery to all channels',
	},
	{
		value: 'end_to_end',
		label: 'End-to-End Test',
		description: 'Complete test from trigger to notification delivery',
	},
];

const CHANNEL_ICONS = {
	email: IconMail,
	slack: IconBrandSlack,
	sms: IconPhone,
	webhook: IconTestPipe,
	teams: IconBrandMicrosoftTeams,
	pagerduty: IconAlertTriangle,
	jira: IconTestPipe,
};

export default function AlertTestFramework()
{
	const [rules, setRules] = useState<AlertRuleType[]>([]);
	const [testResults, setTestResults] = useState<AlertTestResultType[]>([]);
	const [loading, setLoading] = useState(true);
	const [testInProgress, setTestInProgress] = useState(false);
	const [selectedRule, setSelectedRule] = useState<string>('');
	const [testType, setTestType] = useState<string>('mock_trigger');
	const [mockData, setMockData] = useState('{}');
	const [selectedResult, setSelectedResult] = useState<AlertTestResultType | null>(null);
	const [activeTab, setActiveTab] = useState('new-test');

	const [detailsModalOpened, { open: openDetailsModal, close: closeDetailsModal }] = useDisclosure(false);

	useEffect(() =>
	{
		fetchData();
	}, []);

	const fetchData = async () =>
	{
		try
		{
			setLoading(true);
			const [rulesResponse, resultsResponse] = await Promise.all([
				fetch('/api/alerts/rules'),
				fetch('/api/alerts/test'),
			]);

			if (rulesResponse.ok && resultsResponse.ok)
			{
				const rulesData = await rulesResponse.json();
				const resultsData = await resultsResponse.json();

				setRules(rulesData.rules);
				setTestResults(resultsData.testResults);
			}
		}
		catch (error)
		{
			clientLogger.error('Error fetching test data:', error);
			notifications.show({
				title: 'Error',
				message: 'Failed to fetch test data',
				color: 'red',
			});
		}
		finally
		{
			setLoading(false);
		}
	};

	const handleRunTest = async () =>
	{
		if (!selectedRule)
		{
			notifications.show({
				title: 'Error',
				message: 'Please select an alert rule to test',
				color: 'red',
			});
			return;
		}

		try
		{
			setTestInProgress(true);

			const testRequest: AlertTestRequestType = {
				ruleId: selectedRule,
				testType: testType as any,
				mockData: testType === 'mock_trigger' ? JSON.parse(mockData) : undefined,
			};

			const response = await fetch('/api/alerts/test', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(testRequest),
			});

			if (response.ok)
			{
				const result = await response.json();
				notifications.show({
					title: 'Test Started',
					message: 'Alert test is running...',
					color: 'blue',
				});

				// Poll for test completion
				pollTestResult(result.testResult.id);
			}
		}
		catch (error)
		{
			clientLogger.error('Error running test:', error);
			notifications.show({
				title: 'Error',
				message: 'Failed to run alert test',
				color: 'red',
			});
			setTestInProgress(false);
		}
	};

	const pollTestResult = async (testId: string) =>
	{
		const maxAttempts = 30; // 30 seconds max
		let attempts = 0;

		const poll = async () =>
		{
			try
			{
				const response = await fetch(`/api/alerts/test?testId=${testId}`);
				if (response.ok)
				{
					const data = await response.json();
					const result = data.testResult;

					if (result.status === 'running' && attempts < maxAttempts)
					{
						attempts++;
						setTimeout(poll, 1000);
					}
					else
					{
						setTestInProgress(false);
						fetchData(); // Refresh results

						if (result.status === 'passed')
						{
							notifications.show({
								title: 'Test Completed',
								message: 'Alert test passed successfully',
								color: 'green',
							});
						}
						else if (result.status === 'failed')
						{
							notifications.show({
								title: 'Test Failed',
								message: 'Alert test failed - check results for details',
								color: 'red',
							});
						}
					}
				}
			}
			catch (error)
			{
				clientLogger.error('Error polling test result:', error);
				setTestInProgress(false);
			}
		};

		poll();
	};

	const getStatusColor = (status: string) =>
	{
		switch (status)
		{
			case 'passed':
				return 'green';
			case 'failed':
				return 'red';
			case 'running':
				return 'blue';
			default:
				return 'gray';
		}
	};

	const getStatusIcon = (status: string) =>
	{
		switch (status)
		{
			case 'passed':
				return <IconCheck size={16} />;
			case 'failed':
				return <IconX size={16} />;
			case 'running':
				return <IconClock size={16} />;
			default:
				return <IconClock size={16} />;
		}
	};

	const formatDuration = (startTime: Date, endTime?: Date) =>
	{
		const end = endTime || new Date();
		const duration = end.getTime() - startTime.getTime();
		return `${(duration / 1000).toFixed(1)}s`;
	};

	if (loading)
	{
		return (
			<Center h={400}>
				<Stack align="center">
					<Loader size="lg" />
					<Text>Loading test framework...</Text>
				</Stack>
			</Center>
		);
	}

	return (
		<Container size="xl" py="md">
			<Group justify="space-between" mb="lg">
				<Title order={2}>Alert Test Framework</Title>
				<Button
					leftSection={<IconRefresh size={16} />}
					variant="light"
					onClick={fetchData}
				>
					Refresh
				</Button>
			</Group>

			<Tabs value={activeTab} onChange={setActiveTab}>
				<Tabs.List>
					<Tabs.Tab value="new-test">New Test</Tabs.Tab>
					<Tabs.Tab value="results">Test Results</Tabs.Tab>
					<Tabs.Tab value="history">Test History</Tabs.Tab>
				</Tabs.List>

				<Tabs.Panel value="new-test" pt="md">
					<Card withBorder>
						<Stack>
							<Text size="lg" fw={600}>Run Alert Test</Text>

							<Select
								label="Alert Rule"
								placeholder="Select an alert rule to test"
								value={selectedRule}
								onChange={value => setSelectedRule(value || '')}
								data={rules.map(rule => ({
									value: rule.id,
									label: `${rule.name} (${rule.severity})`,
								}))}
								required
							/>

							<Select
								label="Test Type"
								value={testType}
								onChange={value => setTestType(value || 'mock_trigger')}
								data={TEST_TYPES}
								required
							/>

							<Alert color="blue" title="Test Type Description">
								{TEST_TYPES.find(t => t.value === testType)?.description}
							</Alert>

							{testType === 'mock_trigger' && (
								<JsonInput
									label="Mock Data"
									placeholder="Enter mock metric data..."
									value={mockData}
									onChange={setMockData}
									validationError="Invalid JSON"
									formatOnBlur
									autosize
									minRows={4}
								/>
							)}

							<Group justify="flex-end">
								<Button
									leftSection={<IconPlayerPlay size={16} />}
									onClick={handleRunTest}
									loading={testInProgress}
									disabled={!selectedRule}
								>
									Run Test
								</Button>
							</Group>
						</Stack>
					</Card>
				</Tabs.Panel>

				<Tabs.Panel value="results" pt="md">
					<SimpleGrid cols={{ base: 1, md: 2, lg: 3 }}>
						{testResults.slice(0, 6).map(result => (
							<Card key={result.id} withBorder>
								<Group justify="space-between" mb="sm">
									<Badge
										color={getStatusColor(result.status)}
										variant="light"
										leftSection={getStatusIcon(result.status)}
									>
										{result.status}
									</Badge>
									<Text size="xs" c="dimmed">
										{formatDuration(result.startedAt, result.completedAt)}
									</Text>
								</Group>

								<Text fw={600} size="sm" mb="xs">
									{rules.find(r => r.id === result.ruleId)?.name || 'Unknown Rule'}
								</Text>

								<Badge size="xs" variant="outline" mb="sm">
									{result.testType.replace('_', ' ')}
								</Badge>

								<Stack gap="xs" mb="sm">
									{result.results.map((step, index) => (
										<Group key={index} justify="space-between">
											<Text size="xs">{step.step}</Text>
											<Badge
												size="xs"
												color={step.status === 'passed' ? 'green' : 'red'}
												variant="light"
											>
												{step.status}
											</Badge>
										</Group>
									))}
								</Stack>

								<Group justify="space-between">
									<Text size="xs" c="dimmed">
										{result.notifications.length} notifications
									</Text>
									<ActionIcon
										variant="light"
										onClick={() =>
										{
											setSelectedResult(result);
											openDetailsModal();
										}}
									>
										<IconEye size={16} />
									</ActionIcon>
								</Group>
							</Card>
						))}
					</SimpleGrid>

					{testResults.length === 0 && (
						<Center py="xl">
							<Stack align="center">
								<IconTestPipe size={48} color="gray" />
								<Text c="dimmed">No test results available</Text>
								<Button variant="light" onClick={() => setActiveTab('new-test')}>
									Run your first test
								</Button>
							</Stack>
						</Center>
					)}
				</Tabs.Panel>

				<Tabs.Panel value="history" pt="md">
					<Card withBorder>
						<Table>
							<Table.Thead>
								<Table.Tr>
									<Table.Th>Rule</Table.Th>
									<Table.Th>Test Type</Table.Th>
									<Table.Th>Status</Table.Th>
									<Table.Th>Duration</Table.Th>
									<Table.Th>Started</Table.Th>
									<Table.Th>Actions</Table.Th>
								</Table.Tr>
							</Table.Thead>
							<Table.Tbody>
								{testResults.map(result => (
									<Table.Tr key={result.id}>
										<Table.Td>
											<Text size="sm" fw={600}>
												{rules.find(r => r.id === result.ruleId)?.name || 'Unknown Rule'}
											</Text>
										</Table.Td>
										<Table.Td>
											<Badge size="sm" variant="outline">
												{result.testType.replace('_', ' ')}
											</Badge>
										</Table.Td>
										<Table.Td>
											<Badge
												color={getStatusColor(result.status)}
												variant="light"
												leftSection={getStatusIcon(result.status)}
											>
												{result.status}
											</Badge>
										</Table.Td>
										<Table.Td>
											<Text size="sm">
												{formatDuration(result.startedAt, result.completedAt)}
											</Text>
										</Table.Td>
										<Table.Td>
											<Text size="sm">
												{new Date(result.startedAt).toLocaleString()}
											</Text>
										</Table.Td>
										<Table.Td>
											<ActionIcon
												variant="light"
												onClick={() =>
												{
													setSelectedResult(result);
													openDetailsModal();
												}}
											>
												<IconEye size={16} />
											</ActionIcon>
										</Table.Td>
									</Table.Tr>
								))}
							</Table.Tbody>
						</Table>

						{testResults.length === 0 && (
							<Center py="xl">
								<Stack align="center">
									<IconTestPipe size={48} color="gray" />
									<Text c="dimmed">No test history available</Text>
								</Stack>
							</Center>
						)}
					</Card>
				</Tabs.Panel>
			</Tabs>

			{/* Test Details Modal */}
			<Modal
				opened={detailsModalOpened}
				onClose={closeDetailsModal}
				title="Test Result Details"
				size="lg"
			>
				{selectedResult && (
					<Stack>
						<Group justify="space-between">
							<div>
								<Text fw={600}>
									{rules.find(r => r.id === selectedResult.ruleId)?.name || 'Unknown Rule'}
								</Text>
								<Text size="sm" c="dimmed">
									{selectedResult.testType.replace('_', ' ')} test
								</Text>
							</div>
							<Badge
								color={getStatusColor(selectedResult.status)}
								variant="light"
								leftSection={getStatusIcon(selectedResult.status)}
							>
								{selectedResult.status}
							</Badge>
						</Group>

						<SimpleGrid cols={2}>
							<Paper p="sm" withBorder>
								<Text size="xs" c="dimmed" mb="xs">Started</Text>
								<Text size="sm">{new Date(selectedResult.startedAt).toLocaleString()}</Text>
							</Paper>
							<Paper p="sm" withBorder>
								<Text size="xs" c="dimmed" mb="xs">Duration</Text>
								<Text size="sm">
									{formatDuration(selectedResult.startedAt, selectedResult.completedAt)}
								</Text>
							</Paper>
						</SimpleGrid>

						<div>
							<Text fw={600} mb="sm">Test Steps</Text>
							<Timeline>
								{selectedResult.results.map((step, index) => (
									<Timeline.Item
										key={index}
										bullet={step.status === 'passed' ? <IconCheck size={12} /> : <IconX size={12} />}
										color={step.status === 'passed' ? 'green' : 'red'}
									>
										<Text size="sm" fw={600}>{step.step}</Text>
										<Text size="xs" c="dimmed">{step.message}</Text>
										<Text size="xs" c="dimmed">Duration: {step.duration}ms</Text>
										{step.details && (
											<Code block mt="xs">
												{JSON.stringify(step.details, null, 2)}
											</Code>
										)}
									</Timeline.Item>
								))}
							</Timeline>
						</div>

						{selectedResult.notifications.length > 0 && (
							<div>
								<Text fw={600} mb="sm">Notifications</Text>
								<Stack gap="xs">
									{selectedResult.notifications.map((notification, index) =>
									{
										const Icon = CHANNEL_ICONS[notification.channel] || IconTestPipe;
										return (
											<Paper key={index} p="sm" withBorder>
												<Group justify="space-between">
													<Group>
														<Icon size={16} />
														<div>
															<Text size="sm" fw={600}>
																{notification.channel} - {notification.recipient}
															</Text>
															<Text size="xs" c="dimmed">
																Response time: {notification.responseTime}ms
															</Text>
														</div>
													</Group>
													<Badge
														color={notification.delivered ? 'green' : 'red'}
														variant="light"
													>
														{notification.delivered ? 'Delivered' : 'Failed'}
													</Badge>
												</Group>
												{notification.error && (
													<Text size="xs" c="red" mt="xs">
														Error: {notification.error}
													</Text>
												)}
											</Paper>
										);
									})}
								</Stack>
							</div>
						)}
					</Stack>
				)}
			</Modal>
		</Container>
	);
}
