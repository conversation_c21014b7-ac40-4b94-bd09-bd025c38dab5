'use client';

import {
	Con<PERSON><PERSON>,
	<PERSON>rid,
	Card,
	Text,
	Badge,
	Group,
	Stack,
	Button,
	ActionIcon,
	Tabs,
	Alert,
	Loader,
	Center,
	Title,
	Paper,
	SimpleGrid,
	Progress,
	ThemeIcon,
	Divider,
} from '@mantine/core';
import {
	IconAlertTriangle,
	IconBell,
	IconCheck,
	IconClock,
	IconEye,
	IconSettings,
	IconRefresh,
	IconExclamationMark,
	IconShield,
	IconTrendingUp,
	IconUsers,
} from '@tabler/icons-react';
import { useState, useEffect } from 'react';
import { clientLogger } from '@/lib/logger';
import {
	LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell,
} from 'recharts';

import type {
	AlertDashboardStatsType,
	AlertInstanceType,
	AlertAnalyticsType,
} from '@/types/alerts';

const SEVERITY_COLORS = {
	critical: '#fa5252',
	high: '#fd7e14',
	medium: '#fab005',
	low: '#51cf66',
	info: '#339af0',
};

const STATUS_COLORS = {
	active: '#fa5252',
	acknowledged: '#fab005',
	resolved: '#51cf66',
	silenced: '#868e96',
};

export default function AlertDashboard()
{
	const [stats, setStats] = useState<AlertDashboardStatsType | null>(null);
	const [analytics, setAnalytics] = useState<AlertAnalyticsType | null>(null);
	const [loading, setLoading] = useState(true);
	const [refreshing, setRefreshing] = useState(false);
	const [activeTab, setActiveTab] = useState('overview');

	useEffect(() =>
	{
		fetchDashboardData();
	}, []);

	const fetchDashboardData = async () =>
	{
		try
		{
			setRefreshing(true);

			const [statsResponse, analyticsResponse] = await Promise.all([
				fetch('/api/alerts?action=stats'),
				fetch('/api/alerts/analytics?timeframe=24h'),
			]);

			if (statsResponse.ok && analyticsResponse.ok)
			{
				const statsData = await statsResponse.json();
				const analyticsData = await analyticsResponse.json();

				setStats(statsData);
				setAnalytics(analyticsData);
			}
		}
		catch (error)
		{
			clientLogger.error('Error fetching dashboard data:', error);
		}
		finally
		{
			setLoading(false);
			setRefreshing(false);
		}
	};

	const getSeverityIcon = (severity: string) =>
	{
		switch (severity)
		{
			case 'critical':
				return <IconExclamationMark size={16} />;
			case 'high':
				return <IconAlertTriangle size={16} />;
			case 'medium':
				return <IconBell size={16} />;
			case 'low':
				return <IconEye size={16} />;
			default:
				return <IconBell size={16} />;
		}
	};

	const formatDuration = (minutes: number): string =>
	{
		if (minutes < 60)
		{
			return `${minutes}m`;
		}
		const hours = Math.floor(minutes / 60);
		const remainingMinutes = minutes % 60;
		return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
	};

	if (loading)
	{
		return (
			<Center h={400}>
				<Stack align="center">
					<Loader size="lg" />
					<Text>Loading alert dashboard...</Text>
				</Stack>
			</Center>
		);
	}

	if (!stats || !analytics)
	{
		return (
			<Alert color="red" title="Error">
				Failed to load dashboard data. Please try refreshing the page.
			</Alert>
		);
	}

	return (
		<Container size="xl" py="md">
			<Group justify="space-between" mb="lg">
				<Title order={2}>Alert Dashboard</Title>
				<Group>
					<Button
						variant="light"
						leftSection={<IconRefresh size={16} />}
						loading={refreshing}
						onClick={fetchDashboardData}
					>
						Refresh
					</Button>
					<Button
						leftSection={<IconSettings size={16} />}
						component="a"
						href="/alerts/rules"
					>
						Manage Rules
					</Button>
				</Group>
			</Group>

			{/* Key Metrics */}
			<SimpleGrid cols={{ base: 2, sm: 4, lg: 6 }} mb="xl">
				<Paper p="md" withBorder>
					<Group justify="space-between">
						<div>
							<Text size="xs" c="dimmed" tt="uppercase" fw={700}>
								Active Alerts
							</Text>
							<Text fw={700} size="xl">
								{stats.activeAlerts}
							</Text>
						</div>
						<ThemeIcon color="red" variant="light" size="lg">
							<IconAlertTriangle size={18} />
						</ThemeIcon>
					</Group>
				</Paper>

				<Paper p="md" withBorder>
					<Group justify="space-between">
						<div>
							<Text size="xs" c="dimmed" tt="uppercase" fw={700}>
								Critical
							</Text>
							<Text fw={700} size="xl">
								{stats.criticalAlerts}
							</Text>
						</div>
						<ThemeIcon color="red" variant="light" size="lg">
							<IconExclamationMark size={18} />
						</ThemeIcon>
					</Group>
				</Paper>

				<Paper p="md" withBorder>
					<Group justify="space-between">
						<div>
							<Text size="xs" c="dimmed" tt="uppercase" fw={700}>
								Acknowledged
							</Text>
							<Text fw={700} size="xl">
								{stats.acknowledgedAlerts}
							</Text>
						</div>
						<ThemeIcon color="yellow" variant="light" size="lg">
							<IconCheck size={18} />
						</ThemeIcon>
					</Group>
				</Paper>

				<Paper p="md" withBorder>
					<Group justify="space-between">
						<div>
							<Text size="xs" c="dimmed" tt="uppercase" fw={700}>
								Resolved Today
							</Text>
							<Text fw={700} size="xl">
								{stats.resolvedToday}
							</Text>
						</div>
						<ThemeIcon color="green" variant="light" size="lg">
							<IconShield size={18} />
						</ThemeIcon>
					</Group>
				</Paper>

				<Paper p="md" withBorder>
					<Group justify="space-between">
						<div>
							<Text size="xs" c="dimmed" tt="uppercase" fw={700}>
								Avg Resolution
							</Text>
							<Text fw={700} size="xl">
								{formatDuration(stats.avgResolutionTime)}
							</Text>
						</div>
						<ThemeIcon color="blue" variant="light" size="lg">
							<IconClock size={18} />
						</ThemeIcon>
					</Group>
				</Paper>

				<Paper p="md" withBorder>
					<Group justify="space-between">
						<div>
							<Text size="xs" c="dimmed" tt="uppercase" fw={700}>
								MTTR
							</Text>
							<Text fw={700} size="xl">
								{formatDuration(analytics.mttr)}
							</Text>
						</div>
						<ThemeIcon color="violet" variant="light" size="lg">
							<IconTrendingUp size={18} />
						</ThemeIcon>
					</Group>
				</Paper>
			</SimpleGrid>

			<Tabs value={activeTab} onChange={setActiveTab}>
				<Tabs.List>
					<Tabs.Tab value="overview">Overview</Tabs.Tab>
					<Tabs.Tab value="recent">Recent Alerts</Tabs.Tab>
					<Tabs.Tab value="analytics">Analytics</Tabs.Tab>
					<Tabs.Tab value="services">By Service</Tabs.Tab>
				</Tabs.List>

				<Tabs.Panel value="overview" pt="md">
					<Grid>
						<Grid.Col span={{ base: 12, md: 8 }}>
							<Card withBorder h={400}>
								<Card.Section p="md" withBorder>
									<Text fw={600}>Alert Trends (24h)</Text>
								</Card.Section>
								<Card.Section p="md" h={340}>
									<ResponsiveContainer width="100%" height="100%">
										<LineChart data={analytics.alertTrends}>
											<CartesianGrid strokeDasharray="3 3" />
											<XAxis
												dataKey="timestamp"
												tickFormatter={value => new Date(value).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
											/>
											<YAxis />
											<Tooltip
												labelFormatter={value => new Date(value).toLocaleString()}
												formatter={(value, name) => [value, name]}
											/>
											<Line
												type="monotone"
												dataKey="count"
												stroke="#339af0"
												strokeWidth={2}
												dot={{ fill: '#339af0', strokeWidth: 2, r: 4 }}
											/>
										</LineChart>
									</ResponsiveContainer>
								</Card.Section>
							</Card>
						</Grid.Col>

						<Grid.Col span={{ base: 12, md: 4 }}>
							<Stack>
								<Card withBorder>
									<Card.Section p="md" withBorder>
										<Text fw={600}>Alerts by Severity</Text>
									</Card.Section>
									<Card.Section p="md">
										<Stack gap="xs">
											{Object.entries(analytics.alertsBySeverity).map(([severity, count]) => (
												<Group key={severity} justify="space-between">
													<Group gap="xs">
														<Badge
															color={SEVERITY_COLORS[severity as keyof typeof SEVERITY_COLORS]}
															variant="light"
															leftSection={getSeverityIcon(severity)}
														>
															{severity}
														</Badge>
													</Group>
													<Text fw={600}>{count}</Text>
												</Group>
											))}
										</Stack>
									</Card.Section>
								</Card>

								<Card withBorder>
									<Card.Section p="md" withBorder>
										<Text fw={600}>System Health</Text>
									</Card.Section>
									<Card.Section p="md">
										<Stack gap="sm">
											<div>
												<Group justify="space-between" mb={5}>
													<Text size="sm">Escalation Rate</Text>
													<Text size="sm" fw={600}>
														{(analytics.escalationRate * 100).toFixed(1)}%
													</Text>
												</Group>
												<Progress
													value={analytics.escalationRate * 100}
													color={analytics.escalationRate > 0.2 ? 'red' : 'green'}
												/>
											</div>

											<div>
												<Group justify="space-between" mb={5}>
													<Text size="sm">False Positive Rate</Text>
													<Text size="sm" fw={600}>
														{(analytics.falsePositiveRate * 100).toFixed(1)}%
													</Text>
												</Group>
												<Progress
													value={analytics.falsePositiveRate * 100}
													color={analytics.falsePositiveRate > 0.1 ? 'orange' : 'green'}
												/>
											</div>
										</Stack>
									</Card.Section>
								</Card>
							</Stack>
						</Grid.Col>
					</Grid>
				</Tabs.Panel>

				<Tabs.Panel value="recent" pt="md">
					<Card withBorder>
						<Card.Section p="md" withBorder>
							<Group justify="space-between">
								<Text fw={600}>Recent Alerts</Text>
								<Button size="xs" variant="light" component="a" href="/alerts">
									View All
								</Button>
							</Group>
						</Card.Section>
						<Card.Section>
							<Stack gap={0}>
								{stats.recentAlerts.map((alert, index) => (
									<div key={alert.id}>
										<Group p="md" justify="space-between">
											<Group>
												<Badge
													color={SEVERITY_COLORS[alert.severity]}
													variant="light"
													leftSection={getSeverityIcon(alert.severity)}
												>
													{alert.severity}
												</Badge>
												<div>
													<Text fw={600} size="sm">{alert.ruleName}</Text>
													<Text size="xs" c="dimmed">{alert.message}</Text>
												</div>
											</Group>
											<Group>
												<Badge
													color={STATUS_COLORS[alert.status]}
													variant="light"
												>
													{alert.status}
												</Badge>
												<Text size="xs" c="dimmed">
													{new Date(alert.triggeredAt).toLocaleString()}
												</Text>
											</Group>
										</Group>
										{index < stats.recentAlerts.length - 1 && <Divider />}
									</div>
								))}
							</Stack>
						</Card.Section>
					</Card>
				</Tabs.Panel>

				<Tabs.Panel value="analytics" pt="md">
					<Grid>
						<Grid.Col span={{ base: 12, md: 6 }}>
							<Card withBorder h={400}>
								<Card.Section p="md" withBorder>
									<Text fw={600}>Top Alert Rules</Text>
								</Card.Section>
								<Card.Section p="md">
									<Stack gap="sm">
										{analytics.topAlertRules.map((rule, index) => (
											<Group key={rule.ruleId} justify="space-between">
												<div>
													<Text fw={600} size="sm">{rule.ruleName}</Text>
													<Text size="xs" c="dimmed">
														Avg resolution: {formatDuration(rule.avgResolutionTime)}
													</Text>
												</div>
												<Badge variant="light">{rule.count} alerts</Badge>
											</Group>
										))}
									</Stack>
								</Card.Section>
							</Card>
						</Grid.Col>

						<Grid.Col span={{ base: 12, md: 6 }}>
							<Card withBorder h={400}>
								<Card.Section p="md" withBorder>
									<Text fw={600}>Notification Effectiveness</Text>
								</Card.Section>
								<Card.Section p="md">
									<Stack gap="sm">
										{Object.entries(analytics.notificationEffectiveness).map(([channel, stats]) => (
											<div key={channel}>
												<Group justify="space-between" mb={5}>
													<Text size="sm" tt="capitalize">{channel}</Text>
													<Text size="sm" fw={600}>
														{((stats.delivered / stats.sent) * 100).toFixed(1)}%
													</Text>
												</Group>
												<Progress
													value={(stats.delivered / stats.sent) * 100}
													color={stats.delivered / stats.sent > 0.95 ? 'green' : 'orange'}
												/>
												<Text size="xs" c="dimmed">
													{stats.delivered}/{stats.sent} delivered, avg {stats.responseTime}ms
												</Text>
											</div>
										))}
									</Stack>
								</Card.Section>
							</Card>
						</Grid.Col>
					</Grid>
				</Tabs.Panel>

				<Tabs.Panel value="services" pt="md">
					<Card withBorder>
						<Card.Section p="md" withBorder>
							<Text fw={600}>Alerts by Service</Text>
						</Card.Section>
						<Card.Section p="md">
							<SimpleGrid cols={{ base: 1, sm: 2, md: 3 }}>
								{stats.topServices.map(service => (
									<Paper key={service.service} p="md" withBorder>
										<Group justify="space-between" mb="sm">
											<Text fw={600} tt="capitalize">{service.service}</Text>
											<Badge variant="light">{service.alertCount} total</Badge>
										</Group>
										<Group justify="space-between">
											<Text size="sm" c="dimmed">Critical alerts:</Text>
											<Badge
												color={service.criticalCount > 0 ? 'red' : 'green'}
												variant="light"
											>
												{service.criticalCount}
											</Badge>
										</Group>
									</Paper>
								))}
							</SimpleGrid>
						</Card.Section>
					</Card>
				</Tabs.Panel>
			</Tabs>
		</Container>
	);
}
