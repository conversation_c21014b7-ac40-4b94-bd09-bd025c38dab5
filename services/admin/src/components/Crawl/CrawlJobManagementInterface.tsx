'use client';

import {
	Container,
	Title,
	Tabs,
	Paper,
	Group,
	Button,
	Badge,
	Text,
	LoadingOverlay,
	Alert,
	ActionIcon,
	Tooltip,
} from '@mantine/core';
import { modals } from '@mantine/modals';
import { clientLogger } from '@/lib/logger';
import { notifications } from '@mantine/notifications';
import {
	IconRefresh,
	IconPlus,
	IconSettings,
	IconChartBar,
	IconAlertTriangle,
	IconPlayerPause,
	IconPlayerPlay,
	IconSquare,
} from '@tabler/icons-react';
import { useState, useEffect, useCallback } from 'react';


import type {
	CrawlJob,
	CrawlJobSearchResult,
	CrawlJobFilters as CrawlJobFiltersType,
	CrawlJobSort,
	CrawlerServiceStatus,
	GlobalCrawlSettings,
} from '@/types/crawl';
import { CrawlerServiceControls } from './CrawlerServiceControls';
import { CrawlJobAnalytics } from './CrawlJobAnalytics';
import { CrawlJobCreateModal } from './CrawlJobCreateModal';
import { CrawlJobFilters } from './CrawlJobFilters';
import { CrawlJobQueue } from './CrawlJobQueue';
import { CrawlJobSettings } from './CrawlJobSettings';
import { CrawlJobTable } from './CrawlJobTable';


export function CrawlJobManagementInterface()
{
	const [activeTab, setActiveTab] = useState<string>('queue');
	const [jobs, setJobs] = useState<CrawlJob[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [filters, setFilters] = useState<CrawlJobFiltersType>({
		search: '',
		status: [],
		crawlType: [],
		priority: [],
		dateRange: [null, null],
		domain: '',
		requestedBy: '',
		hasErrors: false,
		duration: [null, null],
	});
	const [sort, setSort] = useState<CrawlJobSort>({
		field: 'scheduledAt',
		direction: 'desc',
	});
	const [pagination, setPagination] = useState({
		page: 1,
		pageSize: 20,
		total: 0,
	});
	const [selectedJobs, setSelectedJobs] = useState<string[]>([]);
	const [createModalOpen, setCreateModalOpen] = useState(false);
	const [settingsModalOpen, setSettingsModalOpen] = useState(false);
	const [analyticsModalOpen, setAnalyticsModalOpen] = useState(false);
	const [serviceStatus, setServiceStatus] = useState<CrawlerServiceStatus | null>(null);
	const [settings, setSettings] = useState<GlobalCrawlSettings | null>(null);

	// Fetch jobs data
	const fetchJobs = useCallback(async () =>
	{
		try
		{
			setLoading(true);
			setError(null);

			const params = new URLSearchParams({
				page: pagination.page.toString(),
				pageSize: pagination.pageSize.toString(),
				sortField: sort.field,
				sortDirection: sort.direction,
				search: filters.search,
				domain: filters.domain,
				requestedBy: filters.requestedBy,
				hasErrors: filters.hasErrors.toString(),
			});

			if (filters.status.length > 0)
			{
				params.append('status', filters.status.join(','));
			}
			if (filters.crawlType.length > 0)
			{
				params.append('crawlType', filters.crawlType.join(','));
			}
			if (filters.priority.length > 0)
			{
				params.append('priority', filters.priority.join(','));
			}
			if (filters.dateRange[0])
			{
				params.append('startDate', filters.dateRange[0].toISOString());
			}
			if (filters.dateRange[1])
			{
				params.append('endDate', filters.dateRange[1].toISOString());
			}
			if (filters.duration[0] !== null)
			{
				params.append('minDuration', filters.duration[0].toString());
			}
			if (filters.duration[1] !== null)
			{
				params.append('maxDuration', filters.duration[1].toString());
			}

			const response = await fetch(`/api/crawl/jobs?${params}`);
			if (!response.ok)
			{
				throw new Error('Failed to fetch crawl jobs');
			}

			const result: CrawlJobSearchResult = await response.json();
			setJobs(result.jobs);
			setPagination(result.pagination);
		}
		catch (error)
		{
			clientLogger.error('Error fetching jobs:', error);
			setError(error instanceof Error ? error.message : 'Failed to fetch jobs');
			notifications.show({
				title: 'Error',
				message: 'Failed to fetch crawl jobs',
				color: 'red',
			});
		}
		finally
		{
			setLoading(false);
		}
	}, [filters, sort, pagination.page, pagination.pageSize]);

	// Fetch service status
	const fetchServiceStatus = useCallback(async () =>
	{
		try
		{
			const response = await fetch('/api/crawl/service');
			if (response.ok)
			{
				const status: CrawlerServiceStatus = await response.json();
				setServiceStatus(status);
			}
		}
		catch (error)
		{
			clientLogger.error('Error fetching service status:', error);
		}
	}, []);

	// Fetch settings
	const fetchSettings = useCallback(async () =>
	{
		try
		{
			const response = await fetch('/api/crawl/settings');
			if (response.ok)
			{
				const settingsData: GlobalCrawlSettings = await response.json();
				setSettings(settingsData);
			}
		}
		catch (error)
		{
			clientLogger.error('Error fetching settings:', error);
		}
	}, []);

	// Initial data fetch
	useEffect(() =>
	{
		fetchJobs();
		fetchServiceStatus();
		fetchSettings();
	}, [fetchJobs, fetchServiceStatus, fetchSettings]);

	// Auto-refresh service status
	useEffect(() =>
	{
		const interval = setInterval(fetchServiceStatus, 10000); // Every 10 seconds
		return () => clearInterval(interval);
	}, [fetchServiceStatus]);

	// Handle filter changes
	const handleFiltersChange = useCallback((newFilters: CrawlJobFiltersType) =>
	{
		setFilters(newFilters);
		setPagination(prev => ({ ...prev, page: 1 }));
	}, []);

	// Handle sort changes
	const handleSortChange = useCallback((newSort: CrawlJobSort) =>
	{
		setSort(newSort);
		setPagination(prev => ({ ...prev, page: 1 }));
	}, []);

	// Handle pagination changes
	const handlePaginationChange = useCallback((page: number, pageSize: number) =>
	{
		setPagination(prev => ({ ...prev, page, pageSize }));
	}, []);

	// Handle job selection
	const handleJobSelection = useCallback((jobIds: string[]) =>
	{
		setSelectedJobs(jobIds);
	}, []);

	// Handle job creation
	const handleJobCreated = useCallback((job: CrawlJob) =>
	{
		setJobs(prev => [job, ...prev]);
		setCreateModalOpen(false);
		notifications.show({
			title: 'Success',
			message: 'Crawl job created successfully',
			color: 'green',
		});
	}, []);

	// Handle settings update
	const handleSettingsUpdate = useCallback((newSettings: GlobalCrawlSettings) =>
	{
		setSettings(newSettings);
		setSettingsModalOpen(false);
		notifications.show({
			title: 'Success',
			message: 'Crawl settings updated successfully',
			color: 'green',
		});
	}, []);

	// Handle service control
	const handleServiceControl = useCallback(async (action: string, reason?: string) =>
	{
		try
		{
			const response = await fetch('/api/crawl/service', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ action, reason }),
			});

			if (!response.ok)
			{
				throw new Error('Failed to control service');
			}

			const result = await response.json();
			notifications.show({
				title: 'Success',
				message: result.message,
				color: 'green',
			});

			// Refresh service status
			await fetchServiceStatus();
		}
		catch (error)
		{
			clientLogger.error('Error controlling service:', error);
			notifications.show({
				title: 'Error',
				message: error instanceof Error ? error.message : 'Failed to control service',
				color: 'red',
			});
		}
	}, [fetchServiceStatus]);

	// Handle emergency stop
	const handleEmergencyStop = useCallback(() =>
	{
		modals.openConfirmModal({
			title: 'Emergency Stop',
			children: (
				<Text size="sm">
					This will immediately stop all running crawl jobs and pause the crawler service.
					This action should only be used in emergency situations.
				</Text>
			),
			labels: { confirm: 'Emergency Stop', cancel: 'Cancel' },
			confirmProps: { color: 'red' },
			onConfirm: () => handleServiceControl('emergency_stop', 'Emergency stop initiated by admin'),
		});
	}, [handleServiceControl]);

	// Get service status color
	const getServiceStatusColor = (status: string) =>
	{
		switch (status)
		{
			case 'running':
				return 'green';
			case 'paused':
				return 'yellow';
			case 'stopped':
				return 'gray';
			case 'emergency_stop':
				return 'red';
			default:
				return 'gray';
		}
	};

	return (
		<Container size="xl" py="md">
			<Group justify="space-between" mb="lg">
				<div>
					<Title order={2}>Crawl Job Management</Title>
					<Text size="sm" c="dimmed">
						Manage and monitor domain crawling operations
					</Text>
				</div>
				<Group>
					{serviceStatus && (
						<Group gap="xs">
							<Badge color={getServiceStatusColor(serviceStatus.status)} variant="filled">
								{serviceStatus.status.replace('_', ' ').toUpperCase()}
							</Badge>
							<Text size="xs" c="dimmed">
								{serviceStatus.activeJobs} active, {serviceStatus.queuedJobs} queued
							</Text>
						</Group>
					)}
					<Tooltip label="Refresh">
						<ActionIcon variant="light" onClick={fetchJobs}>
							<IconRefresh size={16} />
						</ActionIcon>
					</Tooltip>
					<Button
						leftSection={<IconPlus size={16} />}
						onClick={() => setCreateModalOpen(true)}
					>
						Create Job
					</Button>
					<Button
						variant="light"
						leftSection={<IconSettings size={16} />}
						onClick={() => setSettingsModalOpen(true)}
					>
						Settings
					</Button>
					<Button
						variant="light"
						leftSection={<IconChartBar size={16} />}
						onClick={() => setAnalyticsModalOpen(true)}
					>
						Analytics
					</Button>
					{serviceStatus?.status === 'running' && (
						<Button
							color="red"
							variant="light"
							leftSection={<IconAlertTriangle size={16} />}
							onClick={handleEmergencyStop}
						>
							Emergency Stop
						</Button>
					)}
				</Group>
			</Group>

			{/* Service Status Alert */}
			{serviceStatus?.status === 'emergency_stop' && (
				<Alert
					icon={<IconAlertTriangle size={16} />}
					title="Emergency Stop Active"
					color="red"
					mb="md"
				>
					The crawler service is in emergency stop mode.
					{serviceStatus.emergencyStopReason && ` Reason: ${serviceStatus.emergencyStopReason}`}
				</Alert>
			)}

			{serviceStatus?.status === 'stopped' && (
				<Alert
					icon={<IconSquare size={16} />}
					title="Service Stopped"
					color="yellow"
					mb="md"
				>
					The crawler service is currently stopped. New jobs will not be processed.
				</Alert>
			)}

			<Tabs value={activeTab} onChange={setActiveTab}>
				<Tabs.List>
					<Tabs.Tab value="queue">Job Queue</Tabs.Tab>
					<Tabs.Tab value="history">Job History</Tabs.Tab>
					<Tabs.Tab value="analytics">Analytics</Tabs.Tab>
					<Tabs.Tab value="service">Service Control</Tabs.Tab>
				</Tabs.List>

				<Tabs.Panel value="queue" pt="md">
					<Paper p="md" mb="md">
						<CrawlJobFilters
							filters={filters}
							onChange={handleFiltersChange}
						/>
					</Paper>

					<Paper p="md">
						<LoadingOverlay visible={loading} />
						{error ? (
							<Alert color="red" mb="md">
								{error}
							</Alert>
						) : (
							<>
								<CrawlJobQueue
									jobs={jobs}
									serviceStatus={serviceStatus}
									onRefresh={fetchJobs}
								/>
								<CrawlJobTable
									jobs={jobs}
									loading={loading}
									sort={sort}
									pagination={pagination}
									selectedJobs={selectedJobs}
									onSortChange={handleSortChange}
									onPaginationChange={handlePaginationChange}
									onSelectionChange={handleJobSelection}
									onRefresh={fetchJobs}
								/>
							</>
						)}
					</Paper>
				</Tabs.Panel>

				<Tabs.Panel value="history" pt="md">
					<Paper p="md">
						<CrawlJobTable
							jobs={jobs}
							loading={loading}
							sort={sort}
							pagination={pagination}
							selectedJobs={selectedJobs}
							onSortChange={handleSortChange}
							onPaginationChange={handlePaginationChange}
							onSelectionChange={handleJobSelection}
							onRefresh={fetchJobs}
							showHistory
						/>
					</Paper>
				</Tabs.Panel>

				<Tabs.Panel value="analytics" pt="md">
					<CrawlJobAnalytics />
				</Tabs.Panel>

				<Tabs.Panel value="service" pt="md">
					<CrawlerServiceControls
						serviceStatus={serviceStatus}
						onServiceControl={handleServiceControl}
						onRefresh={fetchServiceStatus}
					/>
				</Tabs.Panel>
			</Tabs>

			{/* Modals */}
			<CrawlJobCreateModal
				opened={createModalOpen}
				onClose={() => setCreateModalOpen(false)}
				onJobCreated={handleJobCreated}
				settings={settings}
			/>

			<CrawlJobSettings
				opened={settingsModalOpen}
				onClose={() => setSettingsModalOpen(false)}
				settings={settings}
				onSettingsUpdate={handleSettingsUpdate}
			/>
		</Container>
	);
}
