'use client';

import {
	Modal,
	Tabs,
	Text,
	Badge,
	Group,
	Stack,
	Code,
	ScrollArea,
	LoadingOverlay,
	Alert,
	Button,
	Progress,
	Timeline,
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { clientLogger } from '@/lib/logger';
import {
	IconInfoCircle,
	IconSettings,
	IconHistory,
	IconFileText,
	IconRefresh,
} from '@tabler/icons-react';
import { useState, useEffect, useCallback } from 'react';

import type { CrawlJob, CrawlJobHistory } from '@/types/crawl';

interface CrawlJobDetailsModalProps
{
	job: CrawlJob;
	opened: boolean;
	onClose: () => void;
	onJobUpdate: () => void;
}

export function CrawlJobDetailsModal({
	job,
	opened,
	onClose,
	onJobUpdate,
}: CrawlJobDetailsModalProps)
{
	const [loading, setLoading] = useState(false);
	const [history, setHistory] = useState<CrawlJobHistory | null>(null);
	const [logs, setLogs] = useState<string[]>([]);

	// Fetch job details with history and logs
	const fetchJobDetails = useCallback(async () =>
	{
		try
		{
			setLoading(true);
			const response = await fetch(`/api/crawl/jobs/${job.jobId}?includeHistory=true&includeLogs=true`);

			if (response.ok)
			{
				const data = await response.json();
				if (data.history)
				{
					setHistory(data.history);
				}
				if (data.job.logs)
				{
					setLogs(data.job.logs);
				}
			}
		}
		catch (error)
		{
			clientLogger.error('Error fetching job details:', error);
			notifications.show({
				title: 'Error',
				message: 'Failed to fetch job details',
				color: 'red',
			});
		}
		finally
		{
			setLoading(false);
		}
	}, [job.jobId]);

	useEffect(() =>
	{
		if (opened)
		{
			fetchJobDetails();
		}
	}, [opened, fetchJobDetails]);

	// Get status color
	const getStatusColor = (status: string) =>
	{
		switch (status)
		{
			case 'running':
				return 'blue';
			case 'completed':
				return 'green';
			case 'failed':
				return 'red';
			case 'cancelled':
				return 'gray';
			case 'paused':
				return 'yellow';
			case 'pending':
			default:
				return 'orange';
		}
	};

	// Format duration
	const formatDuration = (duration: number | null) =>
	{
		if (!duration) return 'N/A';
		const seconds = Math.floor(duration / 1000);
		const minutes = Math.floor(seconds / 60);
		const hours = Math.floor(minutes / 60);

		if (hours > 0)
		{
			return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
		}
		if (minutes > 0)
		{
			return `${minutes}m ${seconds % 60}s`;
		}

		return `${seconds}s`;
	};

	return (
		<Modal
			opened={opened}
			onClose={onClose}
			title={`Crawl Job Details - ${job.domain}`}
			size="xl"
		>
			<LoadingOverlay visible={loading} />

			<Tabs defaultValue="overview">
				<Tabs.List>
					<Tabs.Tab value="overview" leftSection={<IconInfoCircle size={16} />}>
						Overview
					</Tabs.Tab>
					<Tabs.Tab value="settings" leftSection={<IconSettings size={16} />}>
						Settings
					</Tabs.Tab>
					<Tabs.Tab value="history" leftSection={<IconHistory size={16} />}>
						History
					</Tabs.Tab>
					<Tabs.Tab value="logs" leftSection={<IconFileText size={16} />}>
						Logs
					</Tabs.Tab>
				</Tabs.List>

				<Tabs.Panel value="overview" pt="md">
					<Stack gap="md">
						<Group justify="space-between">
							<Text fw={500} size="lg">{job.domain}</Text>
							<Group>
								<Badge color={getStatusColor(job.status)} variant="filled">
									{job.status.toUpperCase()}
								</Badge>
								<Button
									size="xs"
									variant="light"
									leftSection={<IconRefresh size={14} />}
									onClick={() =>
									{
										fetchJobDetails();
										onJobUpdate();
									}}
								>
									Refresh
								</Button>
							</Group>
						</Group>

						<Group grow>
							<div>
								<Text size="xs" tt="uppercase" fw={700} c="dimmed">Job ID</Text>
								<Code>{job.jobId}</Code>
							</div>
							<div>
								<Text size="xs" tt="uppercase" fw={700} c="dimmed">Crawl Type</Text>
								<Badge variant="light">{job.crawlType}</Badge>
							</div>
							<div>
								<Text size="xs" tt="uppercase" fw={700} c="dimmed">Priority</Text>
								<Badge variant="light">{job.priority}</Badge>
							</div>
						</Group>

						{job.status === 'running' && (
							<div>
								<Text size="xs" tt="uppercase" fw={700} c="dimmed" mb="xs">Progress</Text>
								<Progress value={job.progress} size="lg" />
								<Text size="sm" c="dimmed" mt="xs">{job.progress}% complete</Text>
							</div>
						)}

						<Group grow>
							<div>
								<Text size="xs" tt="uppercase" fw={700} c="dimmed">Scheduled</Text>
								<Text size="sm">{job.scheduledAt.toLocaleString()}</Text>
							</div>
							<div>
								<Text size="xs" tt="uppercase" fw={700} c="dimmed">Started</Text>
								<Text size="sm">{job.startedAt ? job.startedAt.toLocaleString() : 'Not started'}</Text>
							</div>
							<div>
								<Text size="xs" tt="uppercase" fw={700} c="dimmed">Duration</Text>
								<Text size="sm">{formatDuration(job.duration)}</Text>
							</div>
						</Group>

						<Group grow>
							<div>
								<Text size="xs" tt="uppercase" fw={700} c="dimmed">Requested By</Text>
								<Text size="sm">{job.requestedBy}</Text>
							</div>
							<div>
								<Text size="xs" tt="uppercase" fw={700} c="dimmed">Retry Count</Text>
								<Text size="sm">{job.retryCount}</Text>
							</div>
						</Group>

						{job.errorMessage && (
							<Alert color="red" title="Error">
								{job.errorMessage}
							</Alert>
						)}
					</Stack>
				</Tabs.Panel>

				<Tabs.Panel value="settings" pt="md">
					<Stack gap="md">
						<Text fw={500}>Job Settings</Text>
						{job.settings ? (
							<Code block>
								{JSON.stringify(job.settings, null, 2)}
							</Code>
						) : (
							<Text c="dimmed">No custom settings configured</Text>
						)}

						<Text fw={500}>Metadata</Text>
						{Object.keys(job.metadata).length > 0 ? (
							<Code block>
								{JSON.stringify(job.metadata, null, 2)}
							</Code>
						) : (
							<Text c="dimmed">No metadata available</Text>
						)}
					</Stack>
				</Tabs.Panel>

				<Tabs.Panel value="history" pt="md">
					<Stack gap="md">
						<Text fw={500}>Job History</Text>
						{history && history.entries.length > 0 ? (
							<Timeline>
								{history.entries.map(entry => (
									<Timeline.Item key={entry.id} title={entry.event}>
										<Text size="sm" c="dimmed">
											{entry.timestamp.toLocaleString()}
											{entry.userId && ` by ${entry.userId}`}
										</Text>
										{Object.keys(entry.details).length > 0 && (
											<Code block mt="xs">
												{JSON.stringify(entry.details, null, 2)}
											</Code>
										)}
									</Timeline.Item>
								))}
							</Timeline>
						) : (
							<Text c="dimmed">No history available</Text>
						)}
					</Stack>
				</Tabs.Panel>

				<Tabs.Panel value="logs" pt="md">
					<Stack gap="md">
						<Text fw={500}>Job Logs</Text>
						{logs.length > 0 ? (
							<ScrollArea h={400}>
								<Code block>
									{logs.join('\n')}
								</Code>
							</ScrollArea>
						) : (
							<Text c="dimmed">No logs available</Text>
						)}
					</Stack>
				</Tabs.Panel>
			</Tabs>
		</Modal>
	);
}
