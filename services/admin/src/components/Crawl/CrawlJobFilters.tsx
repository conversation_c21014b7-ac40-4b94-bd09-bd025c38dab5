'use client';

import {
	Grid,
	TextInput,
	MultiSelect,
	NumberInput,
	Switch,
	Button,
	Group,
	Collapse,
	ActionIcon,
	Tooltip,
} from '@mantine/core';
import { DatePickerInput, DateValue } from '@mantine/dates';
import {
	IconSearch, IconFilter, IconFilterOff, IconChevronDown, IconChevronUp,
} from '@tabler/icons-react';
import { useState, useCallback } from 'react';

import type { CrawlJobFilters as CrawlJobFiltersType } from '@/types/crawl';

interface CrawlJobFiltersProps
{
	filters: CrawlJobFiltersType;
	onChange: (filters: CrawlJobFiltersType) => void;
}

export function CrawlJobFilters({ filters, onChange }: CrawlJobFiltersProps)
{
	const [expanded, setExpanded] = useState(false);

	// Status options
	const statusOptions = [
		{ value: 'pending', label: 'Pending' },
		{ value: 'running', label: 'Running' },
		{ value: 'completed', label: 'Completed' },
		{ value: 'failed', label: 'Failed' },
		{ value: 'cancelled', label: 'Cancelled' },
		{ value: 'paused', label: 'Paused' },
	];

	// Crawl type options
	const crawlTypeOptions = [
		{ value: 'basic', label: 'Basic' },
		{ value: 'standard', label: 'Standard' },
		{ value: 'full', label: 'Full' },
		{ value: 'priority', label: 'Priority' },
		{ value: 'light', label: 'Light' },
		{ value: 'visual', label: 'Visual' },
		{ value: 'advanced', label: 'Advanced' },
	];

	// Priority options
	const priorityOptions = [
		{ value: 'low', label: 'Low' },
		{ value: 'medium', label: 'Medium' },
		{ value: 'high', label: 'High' },
	];

	// Handle filter changes
	const handleFilterChange = useCallback((field: keyof CrawlJobFiltersType, value: unknown) =>
	{
		onChange({
			...filters,
			[field]: value,
		});
	}, [filters, onChange]);

	// Handle date range change
	const handleDateRangeChange = useCallback((dates: [DateValue, DateValue]) =>
	{
		handleFilterChange('dateRange', dates);
	}, [handleFilterChange]);

	// Handle duration range change
	const handleDurationChange = useCallback((field: 'min' | 'max', value: number | string) =>
	{
		const numValue = typeof value === 'string' ? (value === '' ? null : parseInt(value)) : value;
		const newDuration: [number | null, number | null] = [...filters.duration];

		if (field === 'min')
		{
			newDuration[0] = numValue;
		}
		else
		{
			newDuration[1] = numValue;
		}

		handleFilterChange('duration', newDuration);
	}, [filters.duration, handleFilterChange]);

	// Clear all filters
	const clearFilters = useCallback(() =>
	{
		onChange({
			search: '',
			status: [],
			crawlType: [],
			priority: [],
			dateRange: [null, null],
			domain: '',
			requestedBy: '',
			hasErrors: false,
			duration: [null, null],
		});
	}, [onChange]);

	// Check if any filters are active
	const hasActiveFilters =
    filters.search !== '' ||
    filters.status.length > 0 ||
    filters.crawlType.length > 0 ||
    filters.priority.length > 0 ||
    filters.dateRange[0] !== null ||
    filters.dateRange[1] !== null ||
    filters.domain !== '' ||
    filters.requestedBy !== '' ||
    filters.hasErrors ||
    filters.duration[0] !== null ||
    filters.duration[1] !== null;

	return (
		<div>
			<Group justify="space-between" mb="md">
				<Group>
					<TextInput
						placeholder="Search jobs..."
						leftSection={<IconSearch size={16} />}
						value={filters.search}
						onChange={event => handleFilterChange('search', event.currentTarget.value)}
						style={{ minWidth: 250 }}
					/>
					<Button
						variant="light"
						leftSection={expanded ? <IconChevronUp size={16} /> : <IconChevronDown size={16} />}
						onClick={() => setExpanded(!expanded)}
					>
						{expanded ? 'Hide' : 'Show'} Filters
					</Button>
				</Group>
				<Group>
					{hasActiveFilters && (
						<Tooltip label="Clear all filters">
							<ActionIcon variant="light" color="gray" onClick={clearFilters}>
								<IconFilterOff size={16} />
							</ActionIcon>
						</Tooltip>
					)}
					<Tooltip label={hasActiveFilters ? 'Filters active' : 'No filters active'}>
						<ActionIcon variant={hasActiveFilters ? 'filled' : 'light'} color="blue">
							<IconFilter size={16} />
						</ActionIcon>
					</Tooltip>
				</Group>
			</Group>

			<Collapse in={expanded}>
				<Grid>
					<Grid.Col span={{ base: 12, sm: 6, md: 4 }}>
						<MultiSelect
							label="Status"
							placeholder="Select status"
							data={statusOptions}
							value={filters.status}
							onChange={value => handleFilterChange('status', value)}
							clearable
						/>
					</Grid.Col>

					<Grid.Col span={{ base: 12, sm: 6, md: 4 }}>
						<MultiSelect
							label="Crawl Type"
							placeholder="Select crawl type"
							data={crawlTypeOptions}
							value={filters.crawlType}
							onChange={value => handleFilterChange('crawlType', value)}
							clearable
						/>
					</Grid.Col>

					<Grid.Col span={{ base: 12, sm: 6, md: 4 }}>
						<MultiSelect
							label="Priority"
							placeholder="Select priority"
							data={priorityOptions}
							value={filters.priority}
							onChange={value => handleFilterChange('priority', value)}
							clearable
						/>
					</Grid.Col>

					<Grid.Col span={{ base: 12, sm: 6, md: 4 }}>
						<TextInput
							label="Domain"
							placeholder="Filter by domain"
							value={filters.domain}
							onChange={event => handleFilterChange('domain', event.currentTarget.value)}
						/>
					</Grid.Col>

					<Grid.Col span={{ base: 12, sm: 6, md: 4 }}>
						<TextInput
							label="Requested By"
							placeholder="Filter by user"
							value={filters.requestedBy}
							onChange={event => handleFilterChange('requestedBy', event.currentTarget.value)}
						/>
					</Grid.Col>

					<Grid.Col span={{ base: 12, sm: 6, md: 4 }}>
						<Switch
							label="Has Errors"
							description="Show only jobs with errors"
							checked={filters.hasErrors}
							onChange={event => handleFilterChange('hasErrors', event.currentTarget.checked)}
							mt="lg"
						/>
					</Grid.Col>

					<Grid.Col span={{ base: 12, md: 6 }}>
						<DatePickerInput
							type="range"
							label="Date Range"
							placeholder="Select date range"
							value={filters.dateRange}
							onChange={handleDateRangeChange}
							clearable
						/>
					</Grid.Col>

					<Grid.Col span={{ base: 12, md: 6 }}>
						<Group grow>
							<NumberInput
								label="Min Duration (seconds)"
								placeholder="Min"
								value={filters.duration[0] || ''}
								onChange={value => handleDurationChange('min', value)}
								min={0}
							/>
							<NumberInput
								label="Max Duration (seconds)"
								placeholder="Max"
								value={filters.duration[1] || ''}
								onChange={value => handleDurationChange('max', value)}
								min={0}
							/>
						</Group>
					</Grid.Col>
				</Grid>
			</Collapse>
		</div>
	);
}
