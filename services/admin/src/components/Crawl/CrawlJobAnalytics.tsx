'use client';

import {
	Paper,
	Title,
	Text,
	Group,
	Select,
	Grid,
	Card,
	RingProgress,
	Center,
	LoadingOverlay,
	Alert,
	Table,
} from '@mantine/core';
import { IconTrendingUp, IconClock, IconAlertTriangle } from '@tabler/icons-react';
import { clientLogger } from '@/lib/logger';
import { useState, useEffect, useCallback } from 'react';

import type { CrawlJobAnalytics as CrawlJobAnalyticsType } from '@/types/crawl';

export function CrawlJobAnalytics()
{
	const [analytics, setAnalytics] = useState<CrawlJobAnalyticsType | null>(null);
	const [loading, setLoading] = useState(true);
	const [timeframe, setTimeframe] = useState<'hour' | 'day' | 'week' | 'month'>('day');

	// Fetch analytics data
	const fetchAnalytics = useCallback(async () =>
	{
		try
		{
			setLoading(true);
			const response = await fetch(`/api/crawl/analytics?timeframe=${timeframe}`);

			if (response.ok)
			{
				const data: CrawlJobAnalyticsType = await response.json();
				setAnalytics(data);
			}
		}
		catch (error)
		{
			clientLogger.error('Error fetching analytics:', error);
		}
		finally
		{
			setLoading(false);
		}
	}, [timeframe]);

	useEffect(() =>
	{
		fetchAnalytics();
	}, [fetchAnalytics]);

	if (loading)
	{
		return (
			<Paper p="md" style={{ position: 'relative', minHeight: 400 }}>
				<LoadingOverlay visible />
			</Paper>
		);
	}

	if (!analytics)
	{
		return (
			<Alert icon={<IconAlertTriangle size={16} />} color="red">
				Failed to load analytics data
			</Alert>
		);
	}

	return (
		<div>
			<Group justify="space-between" mb="lg">
				<Title order={3}>Crawl Job Analytics</Title>
				<Select
					value={timeframe}
					onChange={value => setTimeframe(value as any)}
					data={[
						{ value: 'hour', label: 'Last Hour' },
						{ value: 'day', label: 'Last Day' },
						{ value: 'week', label: 'Last Week' },
						{ value: 'month', label: 'Last Month' },
					]}
					w={150}
				/>
			</Group>

			{/* Summary Cards */}
			<Grid mb="lg">
				<Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
					<Card padding="lg" radius="md" withBorder>
						<Group justify="space-between">
							<div>
								<Text size="xs" tt="uppercase" fw={700} c="dimmed">
									Total Jobs
								</Text>
								<Text fw={700} size="xl">
									{analytics.totalJobs}
								</Text>
							</div>
							<IconTrendingUp size={24} color="blue" />
						</Group>
					</Card>
				</Grid.Col>

				<Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
					<Card padding="lg" radius="md" withBorder>
						<Group justify="space-between">
							<div>
								<Text size="xs" tt="uppercase" fw={700} c="dimmed">
									Success Rate
								</Text>
								<Text fw={700} size="xl">
									{analytics.successRate.toFixed(1)}%
								</Text>
							</div>
							<Center>
								<RingProgress
									size={60}
									thickness={6}
									sections={[
										{ value: analytics.successRate, color: analytics.successRate >= 80 ? 'green' : 'yellow' },
									]}
								/>
							</Center>
						</Group>
					</Card>
				</Grid.Col>

				<Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
					<Card padding="lg" radius="md" withBorder>
						<Group justify="space-between">
							<div>
								<Text size="xs" tt="uppercase" fw={700} c="dimmed">
									Avg Duration
								</Text>
								<Text fw={700} size="xl">
									{Math.round(analytics.averageDuration)}s
								</Text>
							</div>
							<IconClock size={24} color="orange" />
						</Group>
					</Card>
				</Grid.Col>

				<Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
					<Card padding="lg" radius="md" withBorder>
						<Group justify="space-between">
							<div>
								<Text size="xs" tt="uppercase" fw={700} c="dimmed">
									Avg Wait Time
								</Text>
								<Text fw={700} size="xl">
									{Math.round(analytics.averageWaitTime)}s
								</Text>
							</div>
							<IconClock size={24} color="gray" />
						</Group>
					</Card>
				</Grid.Col>
			</Grid>

			{/* Data Tables */}
			<Grid>
				<Grid.Col span={{ base: 12, md: 6 }}>
					<Paper p="md" withBorder>
						<Title order={4} mb="md">Jobs by Type</Title>
						<Table>
							<Table.Thead>
								<Table.Tr>
									<Table.Th>Type</Table.Th>
									<Table.Th>Count</Table.Th>
								</Table.Tr>
							</Table.Thead>
							<Table.Tbody>
								{Object.entries(analytics.jobsByType).map(([type, count]) => (
									<Table.Tr key={type}>
										<Table.Td>{type}</Table.Td>
										<Table.Td>{count}</Table.Td>
									</Table.Tr>
								))}
							</Table.Tbody>
						</Table>
					</Paper>
				</Grid.Col>

				<Grid.Col span={{ base: 12, md: 6 }}>
					<Paper p="md" withBorder>
						<Title order={4} mb="md">Jobs by Priority</Title>
						<Table>
							<Table.Thead>
								<Table.Tr>
									<Table.Th>Priority</Table.Th>
									<Table.Th>Count</Table.Th>
								</Table.Tr>
							</Table.Thead>
							<Table.Tbody>
								{Object.entries(analytics.jobsByPriority).map(([priority, count]) => (
									<Table.Tr key={priority}>
										<Table.Td>{priority}</Table.Td>
										<Table.Td>{count}</Table.Td>
									</Table.Tr>
								))}
							</Table.Tbody>
						</Table>
					</Paper>
				</Grid.Col>

				{Object.keys(analytics.errorCategories).length > 0 && (
					<Grid.Col span={12}>
						<Paper p="md" withBorder>
							<Title order={4} mb="md">Error Categories</Title>
							<Table>
								<Table.Thead>
									<Table.Tr>
										<Table.Th>Category</Table.Th>
										<Table.Th>Count</Table.Th>
									</Table.Tr>
								</Table.Thead>
								<Table.Tbody>
									{Object.entries(analytics.errorCategories).map(([category, count]) => (
										<Table.Tr key={category}>
											<Table.Td>{category}</Table.Td>
											<Table.Td>{count}</Table.Td>
										</Table.Tr>
									))}
								</Table.Tbody>
							</Table>
						</Paper>
					</Grid.Col>
				)}
			</Grid>
		</div>
	);
}
