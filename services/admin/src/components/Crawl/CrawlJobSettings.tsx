'use client';

import {
	Modal,
	Tabs,
	NumberInput,
	TextInput,
	Switch,
	Button,
	Group,
	Stack,
	Alert,
	Textarea,
	LoadingOverlay,
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { clientLogger } from '@/lib/logger';
import { notifications } from '@mantine/notifications';
import {
	IconSettings, IconServer, IconShield, IconAlertTriangle,
} from '@tabler/icons-react';
import { useState, useCallback } from 'react';

import type { GlobalCrawlSettings } from '@/types/crawl';

interface CrawlJobSettingsProps
{
	opened: boolean;
	onClose: () => void;
	settings: GlobalCrawlSettings | null;
	onSettingsUpdate: (settings: GlobalCrawlSettings) => void;
}

export function CrawlJobSettings({
	opened,
	onClose,
	settings,
	onSettingsUpdate,
}: CrawlJobSettingsProps)
{
	const [loading, setLoading] = useState(false);

	// Form for settings
	const form = useForm<GlobalCrawlSettings>({
		initialValues: settings || {
			rateLimit: {
				global: 1000,
				perDomain: 60,
				burstLimit: 100,
			},
			concurrency: {
				maxConcurrentJobs: 10,
				maxConcurrentRequestsPerJob: 5,
				maxConcurrentRequestsGlobal: 50,
			},
			timeouts: {
				connectionTimeout: 30000,
				requestTimeout: 60000,
				jobTimeout: 3600000,
			},
			retries: {
				maxRetryAttempts: 3,
				baseBackoffMs: 1000,
				maxBackoffMs: 30000,
				backoffMultiplier: 2,
			},
			userAgents: [
				'Mozilla/5.0 (compatible; DomainRankingBot/1.0; +https://domainranking.com/bot)',
			],
			defaultSettings: {
				rateLimit: 60,
				concurrentRequests: 3,
				timeout: 30000,
				retryAttempts: 3,
				retryBackoff: 1000,
				userAgent: 'Mozilla/5.0 (compatible; DomainRankingBot/1.0)',
				followRedirects: true,
				maxDepth: 3,
				respectRobots: true,
				modules: ['homepage', 'performance', 'security', 'seo'],
			},
			resourceLimits: {
				maxMemoryPerJob: 512 * 1024 * 1024,
				maxCpuPerJob: 80,
				maxDiskSpacePerJob: 100 * 1024 * 1024,
			},
			maintenance: {
				enabled: false,
				message: 'Crawler is under maintenance',
				allowedUsers: [],
			},
		},
	});

	// Update form when settings change
	useState(() =>
	{
		if (settings)
		{
			form.setValues(settings);
		}
	});

	// Handle form submission
	const handleSubmit = useCallback(async (values: GlobalCrawlSettings) =>
	{
		try
		{
			setLoading(true);

			const response = await fetch('/api/crawl/settings', {
				method: 'PUT',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(values),
			});

			if (!response.ok)
			{
				const error = await response.json();
				throw new Error(error.error || 'Failed to update settings');
			}

			onSettingsUpdate(values);
			notifications.show({
				title: 'Success',
				message: 'Crawl settings updated successfully',
				color: 'green',
			});
		}
		catch (error)
		{
			clientLogger.error('Error updating settings:', error);
			notifications.show({
				title: 'Error',
				message: error instanceof Error ? error.message : 'Failed to update settings',
				color: 'red',
			});
		}
		finally
		{
			setLoading(false);
		}
	}, [onSettingsUpdate]);

	return (
		<Modal
			opened={opened}
			onClose={onClose}
			title="Crawl Settings"
			size="lg"
			closeOnClickOutside={!loading}
			closeOnEscape={!loading}
		>
			<LoadingOverlay visible={loading} />

			<form onSubmit={form.onSubmit(handleSubmit)}>
				<Tabs defaultValue="rate-limits">
					<Tabs.List>
						<Tabs.Tab value="rate-limits" leftSection={<IconSettings size={16} />}>
							Rate Limits
						</Tabs.Tab>
						<Tabs.Tab value="concurrency" leftSection={<IconServer size={16} />}>
							Concurrency
						</Tabs.Tab>
						<Tabs.Tab value="timeouts">
							Timeouts
						</Tabs.Tab>
						<Tabs.Tab value="retries">
							Retries
						</Tabs.Tab>
						<Tabs.Tab value="resources" leftSection={<IconShield size={16} />}>
							Resources
						</Tabs.Tab>
						<Tabs.Tab value="maintenance" leftSection={<IconAlertTriangle size={16} />}>
							Maintenance
						</Tabs.Tab>
					</Tabs.List>

					<Tabs.Panel value="rate-limits" pt="md">
						<Stack gap="md">
							<Alert>
								Configure rate limiting to prevent overwhelming target servers and respect their resources.
							</Alert>
							<NumberInput
								label="Global Rate Limit (requests/minute)"
								description="Maximum requests per minute across all crawl jobs"
								min={1}
								max={10000}
								{...form.getInputProps('rateLimit.global')}
							/>
							<NumberInput
								label="Per-Domain Rate Limit (requests/minute)"
								description="Maximum requests per minute per domain"
								min={1}
								max={1000}
								{...form.getInputProps('rateLimit.perDomain')}
							/>
							<NumberInput
								label="Burst Limit"
								description="Maximum burst requests allowed"
								min={1}
								max={1000}
								{...form.getInputProps('rateLimit.burstLimit')}
							/>
						</Stack>
					</Tabs.Panel>

					<Tabs.Panel value="concurrency" pt="md">
						<Stack gap="md">
							<Alert>
								Control concurrent job execution to balance performance and resource usage.
							</Alert>
							<NumberInput
								label="Max Concurrent Jobs"
								description="Maximum number of jobs running simultaneously"
								min={1}
								max={100}
								{...form.getInputProps('concurrency.maxConcurrentJobs')}
							/>
							<NumberInput
								label="Max Concurrent Requests per Job"
								description="Maximum concurrent requests within a single job"
								min={1}
								max={50}
								{...form.getInputProps('concurrency.maxConcurrentRequestsPerJob')}
							/>
							<NumberInput
								label="Max Concurrent Requests Global"
								description="Maximum concurrent requests across all jobs"
								min={1}
								max={500}
								{...form.getInputProps('concurrency.maxConcurrentRequestsGlobal')}
							/>
						</Stack>
					</Tabs.Panel>

					<Tabs.Panel value="timeouts" pt="md">
						<Stack gap="md">
							<Alert>
								Configure timeout values to handle slow or unresponsive servers.
							</Alert>
							<NumberInput
								label="Connection Timeout (ms)"
								description="Time to wait for connection establishment"
								min={1000}
								max={300000}
								step={1000}
								{...form.getInputProps('timeouts.connectionTimeout')}
							/>
							<NumberInput
								label="Request Timeout (ms)"
								description="Time to wait for request completion"
								min={5000}
								max={600000}
								step={1000}
								{...form.getInputProps('timeouts.requestTimeout')}
							/>
							<NumberInput
								label="Job Timeout (ms)"
								description="Maximum time for entire job completion"
								min={60000}
								max={86400000}
								step={60000}
								{...form.getInputProps('timeouts.jobTimeout')}
							/>
						</Stack>
					</Tabs.Panel>

					<Tabs.Panel value="retries" pt="md">
						<Stack gap="md">
							<Alert>
								Configure retry behavior for failed requests and jobs.
							</Alert>
							<NumberInput
								label="Max Retry Attempts"
								description="Maximum number of retry attempts"
								min={0}
								max={10}
								{...form.getInputProps('retries.maxRetryAttempts')}
							/>
							<NumberInput
								label="Base Backoff (ms)"
								description="Initial delay before first retry"
								min={100}
								max={60000}
								step={100}
								{...form.getInputProps('retries.baseBackoffMs')}
							/>
							<NumberInput
								label="Max Backoff (ms)"
								description="Maximum delay between retries"
								min={1000}
								max={300000}
								step={1000}
								{...form.getInputProps('retries.maxBackoffMs')}
							/>
							<NumberInput
								label="Backoff Multiplier"
								description="Multiplier for exponential backoff"
								min={1.1}
								max={5}
								step={0.1}
								precision={1}
								{...form.getInputProps('retries.backoffMultiplier')}
							/>
						</Stack>
					</Tabs.Panel>

					<Tabs.Panel value="resources" pt="md">
						<Stack gap="md">
							<Alert>
								Set resource limits to prevent jobs from consuming excessive system resources.
							</Alert>
							<NumberInput
								label="Max Memory per Job (MB)"
								description="Maximum memory usage per crawl job"
								min={64}
								max={4096}
								{...form.getInputProps('resourceLimits.maxMemoryPerJob')}
								parser={value => (value ? parseInt(value) * 1024 * 1024 : 0)}
								formatter={value => (value ? Math.round(parseInt(value) / (1024 * 1024)).toString() : '')}
							/>
							<NumberInput
								label="Max CPU per Job (%)"
								description="Maximum CPU usage per crawl job"
								min={10}
								max={100}
								{...form.getInputProps('resourceLimits.maxCpuPerJob')}
							/>
							<NumberInput
								label="Max Disk Space per Job (MB)"
								description="Maximum disk space usage per crawl job"
								min={10}
								max={1024}
								{...form.getInputProps('resourceLimits.maxDiskSpacePerJob')}
								parser={value => (value ? parseInt(value) * 1024 * 1024 : 0)}
								formatter={value => (value ? Math.round(parseInt(value) / (1024 * 1024)).toString() : '')}
							/>
						</Stack>
					</Tabs.Panel>

					<Tabs.Panel value="maintenance" pt="md">
						<Stack gap="md">
							<Alert color="yellow">
								Maintenance mode will pause all crawl job processing. Use with caution.
							</Alert>
							<Switch
								label="Maintenance Mode"
								description="Enable maintenance mode to pause all crawling"
								{...form.getInputProps('maintenance.enabled', { type: 'checkbox' })}
							/>
							<Textarea
								label="Maintenance Message"
								description="Message to display during maintenance"
								rows={3}
								{...form.getInputProps('maintenance.message')}
							/>
							<TextInput
								label="Allowed Users (comma-separated)"
								description="Users who can still create jobs during maintenance"
								{...form.getInputProps('maintenance.allowedUsers')}
								parser={value => value.split(',').map(u => u.trim()).filter(Boolean)}
								formatter={value => (Array.isArray(value) ? value.join(', ') : '')}
							/>
						</Stack>
					</Tabs.Panel>
				</Tabs>

				<Group justify="flex-end" mt="xl">
					<Button variant="light" onClick={onClose} disabled={loading}>
						Cancel
					</Button>
					<Button type="submit" loading={loading}>
						Save Settings
					</Button>
				</Group>
			</form>
		</Modal>
	);
}
