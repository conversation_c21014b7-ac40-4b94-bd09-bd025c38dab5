'use client';

import {
	Card,
	Text,
	Badge,
	Group,
	Stack,
	Progress,
	Alert,
	ActionIcon,
	Tooltip,
} from '@mantine/core';
import {
	IconActivity,
	// IconRefresh,
	IconPlayerPause,
	IconPlayerPlay,
} from '@tabler/icons-react';
import { useState, useEffect } from 'react';
import { clientLogger } from '@/lib/logger';

import { useRealtimeSubscription } from '@/hooks/useRealtimeSubscription';
import type { CrawlJobUpdate, QueueUpdate } from '@/lib/realtime/types';
import type { CrawlJob } from '@/types/crawl';

type RealtimeCrawlJobQueueProps =
{
	className?: string;
};

type QueueStatsType =
{
	pending: number;
	running: number;
	completed: number;
	failed: number;
	totalJobs: number;
	averageProcessingTime: number;
	successRate: number;
	throughput: number;
};

function RealtimeCrawlJobQueue({ className }: RealtimeCrawlJobQueueProps)
{
	const [queueStats, setQueueStats] = useState<QueueStatsType>({
		pending: 0,
		running: 0,
		completed: 0,
		failed: 0,
		totalJobs: 0,
		averageProcessingTime: 0,
		successRate: 0,
		throughput: 0,
	});
	const [recentJobs, setRecentJobs] = useState<CrawlJob[]>([]);
	const [isQueuePaused, setIsQueuePaused] = useState(false);
	const [realtimeUpdates, setRealtimeUpdates] = useState(0);

	// Real-time crawl job updates
	const { messageCount: jobMessageCount } = useRealtimeSubscription<CrawlJobUpdate>({
		type: 'crawl_job_update',
		onMessage: (message) =>
		{
			const update = message.data as CrawlJobUpdate['data'];

			// Update recent jobs list
			setRecentJobs((prev) =>
			{
				const filtered = prev.filter(job => job.jobId !== update.job.jobId);
				return [update.job, ...filtered].slice(0, 10); // Keep last 10 jobs
			});

			// Update queue stats based on job status changes
			if (update.previousStatus && update.previousStatus !== update.job.status)
			{
				setQueueStats((prev) =>
				{
					const newStats = { ...prev };

					// Decrement previous status count
					switch (update.previousStatus)
					{
						case 'pending':
							newStats.pending = Math.max(0, newStats.pending - 1);
							break;
						case 'running':
							newStats.running = Math.max(0, newStats.running - 1);
							break;
						case 'completed':
							newStats.completed = Math.max(0, newStats.completed - 1);
							break;
						case 'failed':
							newStats.failed = Math.max(0, newStats.failed - 1);
							break;
					}

					// Increment new status count
					switch (update.job.status)
					{
						case 'pending':
							newStats.pending += 1;
							break;
						case 'running':
							newStats.running += 1;
							break;
						case 'completed':
							newStats.completed += 1;
							break;
						case 'failed':
							newStats.failed += 1;
							break;
					}

					// Recalculate success rate
					const totalCompleted = newStats.completed + newStats.failed;
					newStats.successRate = totalCompleted > 0 ? (newStats.completed / totalCompleted) * 100 : 0;

					return newStats;
				});
			}

			setRealtimeUpdates(prev => prev + 1);
		},
	});

	// Real-time queue updates
	const { messageCount: queueMessageCount } = useRealtimeSubscription<QueueUpdate>({
		type: 'queue_update',
		filters: { 'data.queueName': 'crawl_jobs' },
		onMessage: (message) =>
		{
			const update = message.data as QueueUpdate['data'];

			setQueueStats(prev => ({
				...prev,
				pending: update.depth,
				throughput: update.throughput,
				averageProcessingTime: update.processingRate > 0 ? 1000 / update.processingRate : 0,
			}));

			setRealtimeUpdates(prev => prev + 1);
		},
	});

	// Fetch initial queue stats
	useEffect(() =>
	{
		const fetchQueueStats = async () =>
		{
			try
			{
				const response = await fetch('/api/crawl/jobs?summary=true');
				const data = await response.json();

				setQueueStats({
					pending: data.summary?.pending || 0,
					running: data.summary?.running || 0,
					completed: data.summary?.completed || 0,
					failed: data.summary?.failed || 0,
					totalJobs: data.summary?.total || 0,
					averageProcessingTime: data.summary?.averageProcessingTime || 0,
					successRate: data.summary?.successRate || 0,
					throughput: data.summary?.throughput || 0,
				});

				setRecentJobs(data.recentJobs || []);
			}
			catch (error)
			{
				clientLogger.error('Failed to fetch queue stats:', error);
			}
		};

		fetchQueueStats();
	}, []);

	const handleQueueToggle = async () =>
	{
		try
		{
			const action = isQueuePaused ? 'resume' : 'pause';
			const response = await fetch('/api/crawl/queue', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ action }),
			});

			if (response.ok)
			{
				setIsQueuePaused(!isQueuePaused);
			}
		}
		catch (error)
		{
			clientLogger.error('Failed to toggle queue:', error);
		}
	};

	const getStatusColor = (status: string) =>
	{
		switch (status)
		{
			case 'completed':
				return 'green';
			case 'running':
				return 'blue';
			case 'pending':
				return 'yellow';
			case 'failed':
			case 'cancelled':
				return 'red';
			default:
				return 'gray';
		}
	};

	const formatDuration = (ms: number) =>
	{
		if (ms < 1000) return `${ms}ms`;
		if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
		return `${(ms / 60000).toFixed(1)}m`;
	};

	const totalRealtimeMessages = jobMessageCount + queueMessageCount;

	return (
		<Stack gap="md" className={className}>
			{/* Real-time Status */}
			<Alert
				icon={<IconActivity size={16} />}
				title="Real-time Queue Monitoring"
				color="blue"
				variant="light"
			>
				<Group justify="space-between">
					<Text size="sm">
						Live updates from crawl job queue and individual jobs
					</Text>
					<Group gap="md">
						<Text size="sm" c="dimmed">
							Updates: {totalRealtimeMessages}
						</Text>
						<Badge size="sm" color="blue" variant="light">
							+{realtimeUpdates} live
						</Badge>
					</Group>
				</Group>
			</Alert>

			{/* Queue Controls */}
			<Card withBorder>
				<Group justify="space-between">
					<Text fw={500}>Queue Controls</Text>
					<Group gap="xs">
						<Tooltip label={isQueuePaused ? 'Resume Queue' : 'Pause Queue'}>
							<ActionIcon
								color={isQueuePaused ? 'green' : 'yellow'}
								variant="light"
								onClick={handleQueueToggle}
							>
								{isQueuePaused ? <IconPlayerPlay size={16} /> : <IconPlayerPause size={16} />}
							</ActionIcon>
						</Tooltip>
						<Badge
							color={isQueuePaused ? 'red' : 'green'}
							variant="light"
						>
							{isQueuePaused ? 'Paused' : 'Active'}
						</Badge>
					</Group>
				</Group>
			</Card>

			{/* Queue Statistics */}
			<Card withBorder>
				<Stack gap="md">
					<Group justify="space-between">
						<Text fw={500}>Queue Statistics</Text>
						<Text size="sm" c="dimmed">
							Throughput: {queueStats.throughput.toFixed(1)} jobs/min
						</Text>
					</Group>

					<Group grow>
						<Stack gap="xs" align="center">
							<Text size="xl" fw={700} c="yellow">
								{queueStats.pending}
							</Text>
							<Text size="sm" c="dimmed">Pending</Text>
						</Stack>

						<Stack gap="xs" align="center">
							<Text size="xl" fw={700} c="blue">
								{queueStats.running}
							</Text>
							<Text size="sm" c="dimmed">Running</Text>
						</Stack>

						<Stack gap="xs" align="center">
							<Text size="xl" fw={700} c="green">
								{queueStats.completed}
							</Text>
							<Text size="sm" c="dimmed">Completed</Text>
						</Stack>

						<Stack gap="xs" align="center">
							<Text size="xl" fw={700} c="red">
								{queueStats.failed}
							</Text>
							<Text size="sm" c="dimmed">Failed</Text>
						</Stack>
					</Group>

					<Group justify="space-between">
						<Text size="sm" c="dimmed">
							Success Rate: {queueStats.successRate.toFixed(1)}%
						</Text>
						<Text size="sm" c="dimmed">
							Avg Processing: {formatDuration(queueStats.averageProcessingTime)}
						</Text>
					</Group>

					<Progress
						value={queueStats.successRate}
						color={queueStats.successRate > 80 ? 'green' : queueStats.successRate > 60 ? 'yellow' : 'red'}
						size="sm"
					/>
				</Stack>
			</Card>

			{/* Recent Jobs */}
			<Card withBorder>
				<Stack gap="md">
					<Group justify="space-between">
						<Text fw={500}>Recent Job Updates</Text>
						<Text size="sm" c="dimmed">
							Last {recentJobs.length} jobs
						</Text>
					</Group>

					<Stack gap="xs">
						{recentJobs.length === 0 ? (
							<Text size="sm" c="dimmed" ta="center" py="md">
								No recent job updates
							</Text>
						) : (
							recentJobs.map(job => (
								<Group
									key={job.jobId}
									justify="space-between"
									p="xs"
									style={{
										borderRadius: 4,
										backgroundColor: 'var(--mantine-color-gray-0)',
									}}
								>
									<Group gap="sm">
										<Badge
											size="sm"
											color={getStatusColor(job.status)}
											variant="light"
										>
											{job.status}
										</Badge>
										<Text size="sm" fw={500}>
											{job.domain}
										</Text>
										<Text size="xs" c="dimmed">
											{job.crawlType}
										</Text>
									</Group>

									<Group gap="sm">
										{job.status === 'running' && (
											<Progress
												value={job.progress}
												size="sm"
												style={{ width: 60 }}
											/>
										)}
										<Text size="xs" c="dimmed">
											{job.startedAt ? new Date(job.startedAt).toLocaleTimeString() : 'Not started'}
										</Text>
									</Group>
								</Group>
							))
						)}
					</Stack>
				</Stack>
			</Card>
		</Stack>
	);
}

export default RealtimeCrawlJobQueue;
