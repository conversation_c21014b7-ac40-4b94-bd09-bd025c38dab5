'use client';

import {
	Modal,
	TextInput,
	Select,
	Button,
	Group,
	Stack,
	Alert,
	Tabs,
	NumberInput,
	Switch,
	MultiSelect,
	Textarea,
	LoadingOverlay,
} from '@mantine/core';
import { DateTimePicker } from '@mantine/dates';
import { clientLogger } from '@/lib/logger';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { IconAlertCircle, IconSettings, IconTemplate } from '@tabler/icons-react';
import { useState, useCallback, useEffect } from 'react';

import type {
	CrawlJobCreateRequest,
	CrawlJob,
	GlobalCrawlSettings,
	CrawlJobTemplate,
	CrawlJobSettings,
} from '@/types/crawl';

interface CrawlJobCreateModalProps
{
	opened: boolean;
	onClose: () => void;
	onJobCreated: (job: CrawlJob) => void;
	settings: GlobalCrawlSettings | null;
}

export function CrawlJobCreateModal({
	opened,
	onClose,
	onJobCreated,
	settings,
}: CrawlJobCreateModalProps)
{
	const [loading, setLoading] = useState(false);
	const [templates, setTemplates] = useState<CrawlJobTemplate[]>([]);
	const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
	const [activeTab, setActiveTab] = useState<string>('basic');

	// Form for job creation
	const form = useForm<CrawlJobCreateRequest & { customSettings: CrawlJobSettings }>({
		initialValues: {
			domain: '',
			crawlType: 'standard',
			priority: 'medium',
			scheduledAt: undefined,
			metadata: {},
			customSettings: {
				rateLimit: settings?.defaultSettings.rateLimit || 60,
				concurrentRequests: settings?.defaultSettings.concurrentRequests || 3,
				timeout: settings?.defaultSettings.timeout || 30000,
				retryAttempts: settings?.defaultSettings.retryAttempts || 3,
				retryBackoff: settings?.defaultSettings.retryBackoff || 1000,
				userAgent: settings?.defaultSettings.userAgent || 'Mozilla/5.0 (compatible; DomainRankingBot/1.0)',
				followRedirects: settings?.defaultSettings.followRedirects || true,
				maxDepth: settings?.defaultSettings.maxDepth || 3,
				respectRobots: settings?.defaultSettings.respectRobots || true,
				modules: settings?.defaultSettings.modules || ['homepage', 'performance', 'security', 'seo'],
			},
		},
		validate: {
			domain: (value) =>
			{
				if (!value) return 'Domain is required';
				const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9])*$/;
				if (!domainRegex.test(value)) return 'Invalid domain format';
				return null;
			},
			crawlType: value => (!value ? 'Crawl type is required' : null),
			priority: value => (!value ? 'Priority is required' : null),
		},
	});

	// Fetch templates
	const fetchTemplates = useCallback(async () =>
	{
		try
		{
			const response = await fetch('/api/crawl/templates');
			if (response.ok)
			{
				const templatesData: CrawlJobTemplate[] = await response.json();
				setTemplates(templatesData);
			}
		}
		catch (error)
		{
			clientLogger.error('Error fetching templates:', error);
		}
	}, []);

	// Load templates on modal open
	useEffect(() =>
	{
		if (opened)
		{
			fetchTemplates();
		}
	}, [opened, fetchTemplates]);

	// Apply template
	const applyTemplate = useCallback((templateId: string) =>
	{
		const template = templates.find(t => t.id === templateId);
		if (template)
		{
			form.setValues({
				...form.values,
				crawlType: template.crawlType,
				priority: template.priority,
				customSettings: template.settings,
				metadata: template.metadata,
			});
			setSelectedTemplate(templateId);
		}
	}, [templates, form]);

	// Handle form submission
	const handleSubmit = useCallback(async (values: typeof form.values) =>
	{
		try
		{
			setLoading(true);

			const jobData: CrawlJobCreateRequest = {
				domain: values.domain.toLowerCase().trim(),
				crawlType: values.crawlType,
				priority: values.priority,
				scheduledAt: values.scheduledAt,
				settings: values.customSettings,
				metadata: values.metadata,
			};

			const response = await fetch('/api/crawl/jobs', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(jobData),
			});

			if (!response.ok)
			{
				const error = await response.json();
				throw new Error(error.error || 'Failed to create job');
			}

			const createdJob: CrawlJob = await response.json();

			// Increment template usage if one was used
			if (selectedTemplate)
			{
				try
				{
					await fetch(`/api/crawl/templates/${selectedTemplate}`, {
						method: 'POST',
						headers: { 'Content-Type': 'application/json' },
						body: JSON.stringify({ action: 'use' }),
					});
				}
				catch (error)
				{
					clientLogger.warn('Failed to increment template usage:', error);
				}
			}

			onJobCreated(createdJob);
			form.reset();
			setSelectedTemplate(null);
			setActiveTab('basic');
		}
		catch (error)
		{
			clientLogger.error('Error creating job:', error);
			notifications.show({
				title: 'Error',
				message: error instanceof Error ? error.message : 'Failed to create job',
				color: 'red',
			});
		}
		finally
		{
			setLoading(false);
		}
	}, [form, selectedTemplate, onJobCreated]);

	// Handle modal close
	const handleClose = useCallback(() =>
	{
		form.reset();
		setSelectedTemplate(null);
		setActiveTab('basic');
		onClose();
	}, [form, onClose]);

	// Module options
	const moduleOptions = [
		{ value: 'homepage', label: 'Homepage Analysis' },
		{ value: 'performance', label: 'Performance Metrics' },
		{ value: 'security', label: 'Security Analysis' },
		{ value: 'seo', label: 'SEO Analysis' },
		{ value: 'technical', label: 'Technical Analysis' },
		{ value: 'screenshots', label: 'Screenshots' },
		{ value: 'dns', label: 'DNS Analysis' },
		{ value: 'robots', label: 'Robots.txt Analysis' },
		{ value: 'ssl', label: 'SSL Analysis' },
	];

	return (
		<Modal
			opened={opened}
			onClose={handleClose}
			title="Create Crawl Job"
			size="lg"
			closeOnClickOutside={!loading}
			closeOnEscape={!loading}
		>
			<LoadingOverlay visible={loading} />

			<form onSubmit={form.onSubmit(handleSubmit)}>
				<Tabs value={activeTab} onChange={setActiveTab}>
					<Tabs.List>
						<Tabs.Tab value="basic">Basic Settings</Tabs.Tab>
						<Tabs.Tab value="advanced" leftSection={<IconSettings size={16} />}>
							Advanced Settings
						</Tabs.Tab>
						<Tabs.Tab value="template" leftSection={<IconTemplate size={16} />}>
							Templates
						</Tabs.Tab>
					</Tabs.List>

					<Tabs.Panel value="basic" pt="md">
						<Stack gap="md">
							<TextInput
								label="Domain"
								placeholder="example.com"
								required
								{...form.getInputProps('domain')}
							/>

							<Select
								label="Crawl Type"
								placeholder="Select crawl type"
								required
								data={[
									{ value: 'basic', label: 'Basic - Quick analysis' },
									{ value: 'standard', label: 'Standard - Comprehensive analysis' },
									{ value: 'full', label: 'Full - Complete deep analysis' },
									{ value: 'priority', label: 'Priority - High priority processing' },
									{ value: 'light', label: 'Light - Minimal resource usage' },
									{ value: 'visual', label: 'Visual - Focus on visual elements' },
									{ value: 'advanced', label: 'Advanced - All modules enabled' },
								]}
								{...form.getInputProps('crawlType')}
							/>

							<Select
								label="Priority"
								placeholder="Select priority"
								required
								data={[
									{ value: 'low', label: 'Low' },
									{ value: 'medium', label: 'Medium' },
									{ value: 'high', label: 'High' },
								]}
								{...form.getInputProps('priority')}
							/>

							<DateTimePicker
								label="Scheduled Time"
								placeholder="Schedule for later (optional)"
								clearable
								{...form.getInputProps('scheduledAt')}
							/>

							<Textarea
								label="Notes"
								placeholder="Optional notes about this crawl job"
								rows={3}
								{...form.getInputProps('metadata.notes')}
							/>
						</Stack>
					</Tabs.Panel>

					<Tabs.Panel value="advanced" pt="md">
						<Stack gap="md">
							<NumberInput
								label="Rate Limit (requests per minute)"
								min={1}
								max={1000}
								{...form.getInputProps('customSettings.rateLimit')}
							/>

							<NumberInput
								label="Concurrent Requests"
								min={1}
								max={20}
								{...form.getInputProps('customSettings.concurrentRequests')}
							/>

							<NumberInput
								label="Timeout (milliseconds)"
								min={5000}
								max={300000}
								step={1000}
								{...form.getInputProps('customSettings.timeout')}
							/>

							<NumberInput
								label="Retry Attempts"
								min={0}
								max={10}
								{...form.getInputProps('customSettings.retryAttempts')}
							/>

							<NumberInput
								label="Retry Backoff (milliseconds)"
								min={100}
								max={60000}
								step={100}
								{...form.getInputProps('customSettings.retryBackoff')}
							/>

							<TextInput
								label="User Agent"
								placeholder="Custom user agent string"
								{...form.getInputProps('customSettings.userAgent')}
							/>

							<NumberInput
								label="Max Depth"
								min={1}
								max={10}
								{...form.getInputProps('customSettings.maxDepth')}
							/>

							<Group grow>
								<Switch
									label="Follow Redirects"
									{...form.getInputProps('customSettings.followRedirects', { type: 'checkbox' })}
								/>
								<Switch
									label="Respect Robots.txt"
									{...form.getInputProps('customSettings.respectRobots', { type: 'checkbox' })}
								/>
							</Group>

							<MultiSelect
								label="Modules"
								placeholder="Select modules to run"
								data={moduleOptions}
								{...form.getInputProps('customSettings.modules')}
							/>
						</Stack>
					</Tabs.Panel>

					<Tabs.Panel value="template" pt="md">
						<Stack gap="md">
							{templates.length === 0 ? (
								<Alert icon={<IconAlertCircle size={16} />}>
									No templates available. Create templates to quickly set up common crawl configurations.
								</Alert>
							) : (
								<>
									<Select
										label="Select Template"
										placeholder="Choose a template to apply"
										data={templates.map(template => ({
											value: template.id,
											label: `${template.name} (${template.crawlType}, ${template.priority})`,
										}))}
										value={selectedTemplate}
										onChange={value => value && applyTemplate(value)}
										clearable
									/>

									{selectedTemplate && (
										<Alert color="blue">
											Template applied! You can modify the settings in the other tabs if needed.
										</Alert>
									)}

									{templates.map(template => (
										<div key={template.id} style={{ display: selectedTemplate === template.id ? 'block' : 'none' }}>
											<Alert>
												<strong>{template.name}</strong>
												<br />
												{template.description}
												<br />
												<small>
													Type: {template.crawlType}, Priority: {template.priority},
													Used: {template.usageCount} times
												</small>
											</Alert>
										</div>
									))}
								</>
							)}
						</Stack>
					</Tabs.Panel>
				</Tabs>

				<Group justify="flex-end" mt="xl">
					<Button variant="light" onClick={handleClose} disabled={loading}>
						Cancel
					</Button>
					<Button type="submit" loading={loading}>
						Create Job
					</Button>
				</Group>
			</form>
		</Modal>
	);
}
