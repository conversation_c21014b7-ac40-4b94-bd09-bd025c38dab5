'use client';

import {
	<PERSON><PERSON>,
	Card,
	Text,
	Badge,
	Group,
	Progress,
	ActionIcon,
	Tooltip,
	Stack,
	Alert,
	RingProgress,
	Center,
} from '@mantine/core';
import {
	IconRefresh,
	IconClock,
	IconPlayerPlay,
	IconCheck,
	IconX,
	IconPlayerPause as IconPause,
	IconAlertTriangle,
} from '@tabler/icons-react';
import { useState, useEffect } from 'react';

import type { CrawlJob, CrawlerServiceStatus } from '@/types/crawl';

interface CrawlJobQueueProps
{
	jobs: CrawlJob[];
	serviceStatus: CrawlerServiceStatus | null;
	onRefresh: () => void;
}

export function CrawlJobQueue({ jobs, serviceStatus, onRefresh }: CrawlJobQueueProps)
{
	const [queueStats, setQueueStats] = useState({
		pending: 0,
		running: 0,
		completed: 0,
		failed: 0,
		cancelled: 0,
		paused: 0,
	});

	// Calculate queue statistics
	useEffect(() =>
	{
		const stats = jobs.reduce((acc, job) =>
		{
			acc[job.status as keyof typeof acc]++;
			return acc;
		}, {
			pending: 0,
			running: 0,
			completed: 0,
			failed: 0,
			cancelled: 0,
			paused: 0,
		});

		setQueueStats(stats);
	}, [jobs]);

	// Get status color
	const getStatusColor = (status: string) =>
	{
		switch (status)
		{
			case 'running':
				return 'blue';
			case 'completed':
				return 'green';
			case 'failed':
				return 'red';
			case 'cancelled':
				return 'gray';
			case 'paused':
				return 'yellow';
			case 'pending':
			default:
				return 'orange';
		}
	};

	// Get status icon
	const getStatusIcon = (status: string) =>
	{
		switch (status)
		{
			case 'running':
				return <IconPlayerPlay size={16} />;
			case 'completed':
				return <IconCheck size={16} />;
			case 'failed':
				return <IconX size={16} />;
			case 'cancelled':
				return <IconX size={16} />;
			case 'paused':
				return <IconPause size={16} />;
			case 'pending':
			default:
				return <IconClock size={16} />;
		}
	};

	// Calculate success rate
	const totalCompleted = queueStats.completed + queueStats.failed;
	const successRate = totalCompleted > 0 ? (queueStats.completed / totalCompleted) * 100 : 0;

	// Calculate queue health
	const totalJobs = Object.values(queueStats).reduce((sum, count) => sum + count, 0);
	const healthyJobs = queueStats.completed + queueStats.running + queueStats.pending;
	const queueHealth = totalJobs > 0 ? (healthyJobs / totalJobs) * 100 : 100;

	return (
		<Stack gap="md" mb="lg">
			{/* Service Status Alert */}
			{serviceStatus && serviceStatus.status !== 'running' && (
				<Alert
					icon={<IconAlertTriangle size={16} />}
					title={`Crawler Service ${serviceStatus.status.replace('_', ' ').toUpperCase()}`}
					color={serviceStatus.status === 'emergency_stop' ? 'red' : 'yellow'}
				>
					{serviceStatus.status === 'emergency_stop' && serviceStatus.emergencyStopReason && (
						<Text size="sm">Reason: {serviceStatus.emergencyStopReason}</Text>
					)}
					{serviceStatus.status === 'paused' && (
						<Text size="sm">Job processing is paused. Resume to continue processing jobs.</Text>
					)}
					{serviceStatus.status === 'stopped' && (
						<Text size="sm">Service is stopped. Start the service to process jobs.</Text>
					)}
				</Alert>
			)}

			{/* Queue Overview Cards */}
			<Grid>
				<Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
					<Card padding="lg" radius="md" withBorder>
						<Group justify="space-between">
							<div>
								<Text size="xs" tt="uppercase" fw={700} c="dimmed">
									Pending Jobs
								</Text>
								<Text fw={700} size="xl">
									{queueStats.pending}
								</Text>
							</div>
							<Badge color="orange" variant="light" size="lg">
								{getStatusIcon('pending')}
							</Badge>
						</Group>
					</Card>
				</Grid.Col>

				<Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
					<Card padding="lg" radius="md" withBorder>
						<Group justify="space-between">
							<div>
								<Text size="xs" tt="uppercase" fw={700} c="dimmed">
									Running Jobs
								</Text>
								<Text fw={700} size="xl">
									{queueStats.running}
								</Text>
							</div>
							<Badge color="blue" variant="light" size="lg">
								{getStatusIcon('running')}
							</Badge>
						</Group>
					</Card>
				</Grid.Col>

				<Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
					<Card padding="lg" radius="md" withBorder>
						<Group justify="space-between">
							<div>
								<Text size="xs" tt="uppercase" fw={700} c="dimmed">
									Completed Jobs
								</Text>
								<Text fw={700} size="xl">
									{queueStats.completed}
								</Text>
							</div>
							<Badge color="green" variant="light" size="lg">
								{getStatusIcon('completed')}
							</Badge>
						</Group>
					</Card>
				</Grid.Col>

				<Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
					<Card padding="lg" radius="md" withBorder>
						<Group justify="space-between">
							<div>
								<Text size="xs" tt="uppercase" fw={700} c="dimmed">
									Failed Jobs
								</Text>
								<Text fw={700} size="xl">
									{queueStats.failed}
								</Text>
							</div>
							<Badge color="red" variant="light" size="lg">
								{getStatusIcon('failed')}
							</Badge>
						</Group>
					</Card>
				</Grid.Col>
			</Grid>

			{/* Queue Health and Performance */}
			<Grid>
				<Grid.Col span={{ base: 12, md: 6 }}>
					<Card padding="lg" radius="md" withBorder>
						<Group justify="space-between" mb="md">
							<Text fw={500}>Success Rate</Text>
							<Tooltip label="Refresh">
								<ActionIcon variant="light" size="sm" onClick={onRefresh}>
									<IconRefresh size={14} />
								</ActionIcon>
							</Tooltip>
						</Group>
						<Center>
							<RingProgress
								size={120}
								thickness={12}
								sections={[
									{ value: successRate, color: successRate >= 80 ? 'green' : successRate >= 60 ? 'yellow' : 'red' },
								]}
								label={(
									<Center>
										<Text fw={700} size="lg">
											{successRate.toFixed(1)}%
										</Text>
									</Center>
								)}
							/>
						</Center>
						<Text size="xs" c="dimmed" ta="center" mt="sm">
							{queueStats.completed} completed, {queueStats.failed} failed
						</Text>
					</Card>
				</Grid.Col>

				<Grid.Col span={{ base: 12, md: 6 }}>
					<Card padding="lg" radius="md" withBorder>
						<Group justify="space-between" mb="md">
							<Text fw={500}>Queue Health</Text>
							<Badge
								color={queueHealth >= 80 ? 'green' : queueHealth >= 60 ? 'yellow' : 'red'}
								variant="light"
							>
								{queueHealth >= 80 ? 'Healthy' : queueHealth >= 60 ? 'Warning' : 'Critical'}
							</Badge>
						</Group>
						<Center>
							<RingProgress
								size={120}
								thickness={12}
								sections={[
									{ value: queueHealth, color: queueHealth >= 80 ? 'green' : queueHealth >= 60 ? 'yellow' : 'red' },
								]}
								label={(
									<Center>
										<Text fw={700} size="lg">
											{queueHealth.toFixed(1)}%
										</Text>
									</Center>
								)}
							/>
						</Center>
						<Text size="xs" c="dimmed" ta="center" mt="sm">
							{healthyJobs} healthy, {queueStats.failed + queueStats.cancelled} problematic
						</Text>
					</Card>
				</Grid.Col>
			</Grid>

			{/* System Load (if available) */}
			{serviceStatus && (
				<Card padding="lg" radius="md" withBorder>
					<Text fw={500} mb="md">System Resources</Text>
					<Grid>
						<Grid.Col span={3}>
							<Text size="xs" tt="uppercase" fw={700} c="dimmed" mb="xs">
								CPU Usage
							</Text>
							<Progress
								value={serviceStatus.systemLoad.cpu}
								color={serviceStatus.systemLoad.cpu > 80 ? 'red' : serviceStatus.systemLoad.cpu > 60 ? 'yellow' : 'green'}
								size="lg"
							/>
							<Text size="xs" c="dimmed" mt="xs">
								{serviceStatus.systemLoad.cpu.toFixed(1)}%
							</Text>
						</Grid.Col>

						<Grid.Col span={3}>
							<Text size="xs" tt="uppercase" fw={700} c="dimmed" mb="xs">
								Memory Usage
							</Text>
							<Progress
								value={serviceStatus.systemLoad.memory}
								color={serviceStatus.systemLoad.memory > 80 ? 'red' : serviceStatus.systemLoad.memory > 60 ? 'yellow' : 'green'}
								size="lg"
							/>
							<Text size="xs" c="dimmed" mt="xs">
								{serviceStatus.systemLoad.memory.toFixed(1)}%
							</Text>
						</Grid.Col>

						<Grid.Col span={3}>
							<Text size="xs" tt="uppercase" fw={700} c="dimmed" mb="xs">
								Network Usage
							</Text>
							<Progress
								value={serviceStatus.systemLoad.network}
								color={serviceStatus.systemLoad.network > 80 ? 'red' : serviceStatus.systemLoad.network > 60 ? 'yellow' : 'green'}
								size="lg"
							/>
							<Text size="xs" c="dimmed" mt="xs">
								{serviceStatus.systemLoad.network.toFixed(1)}%
							</Text>
						</Grid.Col>

						<Grid.Col span={3}>
							<Text size="xs" tt="uppercase" fw={700} c="dimmed" mb="xs">
								Disk Usage
							</Text>
							<Progress
								value={serviceStatus.systemLoad.disk}
								color={serviceStatus.systemLoad.disk > 80 ? 'red' : serviceStatus.systemLoad.disk > 60 ? 'yellow' : 'green'}
								size="lg"
							/>
							<Text size="xs" c="dimmed" mt="xs">
								{serviceStatus.systemLoad.disk.toFixed(1)}%
							</Text>
						</Grid.Col>
					</Grid>

					<Group justify="space-between" mt="md">
						<Text size="sm" c="dimmed">
							Resource Limits: {serviceStatus.resourceLimits.maxConcurrentJobs} max jobs,
							{serviceStatus.resourceLimits.maxCpuUsage}% max CPU,
							{serviceStatus.resourceLimits.maxMemoryUsage}% max memory
						</Text>
						<Text size="xs" c="dimmed">
							Last check: {serviceStatus.lastHealthCheck.toLocaleTimeString()}
						</Text>
					</Group>
				</Card>
			)}
		</Stack>
	);
}
