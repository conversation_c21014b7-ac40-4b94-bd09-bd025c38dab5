'use client';

import {
	Paper,
	Title,
	Text,
	Group,
	Button,
	Badge,
	Stack,
	Alert,
	Progress,
	Grid,
	Card,
	Modal,
	Textarea,
} from '@mantine/core';
import { modals } from '@mantine/modals';
import { clientLogger } from '@/lib/logger';
import { notifications } from '@mantine/notifications';
import {
	IconPlayerPlay,
	IconPlayerPause,
	IconSquare,
	IconRefresh,
	IconAlertTriangle,
	IconServer,
	IconCpu,
	IconDeviceDesktop as IconMemory,
	IconNetwork,
	IconDatabase,
} from '@tabler/icons-react';
import { useState, useCallback } from 'react';

import type { CrawlerServiceStatus } from '@/types/crawl';

interface CrawlerServiceControlsProps
{
	serviceStatus: CrawlerServiceStatus | null;
	onServiceControl: (action: string, reason?: string) => Promise<void>;
	onRefresh: () => void;
}

export function CrawlerServiceControls({
	serviceStatus,
	onServiceControl,
	onRefresh,
}: CrawlerServiceControlsProps)
{
	const [loading, setLoading] = useState(false);
	const [reasonModalOpen, setReasonModalOpen] = useState(false);
	const [pendingAction, setPendingAction] = useState<string>('');
	const [reason, setReason] = useState('');

	// Handle service control with reason
	const handleServiceControl = useCallback(async (action: string, requiresReason = false) =>
	{
		if (requiresReason)
		{
			setPendingAction(action);
			setReasonModalOpen(true);
			return;
		}

		try
		{
			setLoading(true);
			await onServiceControl(action);
		}
		catch (error)
		{
			clientLogger.error('Error controlling service:', error);
		}
		finally
		{
			setLoading(false);
		}
	}, [onServiceControl]);

	// Execute action with reason
	const executeActionWithReason = useCallback(async () =>
	{
		try
		{
			setLoading(true);
			await onServiceControl(pendingAction, reason);
			setReasonModalOpen(false);
			setReason('');
			setPendingAction('');
		}
		catch (error)
		{
			clientLogger.error('Error controlling service:', error);
		}
		finally
		{
			setLoading(false);
		}
	}, [onServiceControl, pendingAction, reason]);

	// Handle emergency stop
	const handleEmergencyStop = useCallback(() =>
	{
		modals.openConfirmModal({
			title: 'Emergency Stop',
			children: (
				<Stack gap="md">
					<Text size="sm">
						This will immediately stop all running crawl jobs and halt the crawler service.
						This action should only be used in emergency situations.
					</Text>
					<Alert icon={<IconAlertTriangle size={16} />} color="red">
						All active crawl jobs will be cancelled and the service will be stopped.
					</Alert>
				</Stack>
			),
			labels: { confirm: 'Emergency Stop', cancel: 'Cancel' },
			confirmProps: { color: 'red' },
			onConfirm: () => handleServiceControl('emergency_stop', true),
		});
	}, [handleServiceControl]);

	// Get status color
	const getStatusColor = (status: string) =>
	{
		switch (status)
		{
			case 'running':
				return 'green';
			case 'paused':
				return 'yellow';
			case 'stopped':
				return 'gray';
			case 'emergency_stop':
				return 'red';
			default:
				return 'gray';
		}
	};

	// Get resource color
	const getResourceColor = (usage: number) =>
	{
		if (usage > 80) return 'red';
		if (usage > 60) return 'yellow';
		return 'green';
	};

	if (!serviceStatus)
	{
		return (
			<Alert icon={<IconAlertTriangle size={16} />} color="red">
				Unable to connect to crawler service
			</Alert>
		);
	}

	return (
		<div>
			<Group justify="space-between" mb="lg">
				<Title order={3}>Crawler Service Control</Title>
				<Group>
					<Badge color={getStatusColor(serviceStatus.status)} variant="filled" size="lg">
						{serviceStatus.status.replace('_', ' ').toUpperCase()}
					</Badge>
					<Button
						variant="light"
						leftSection={<IconRefresh size={16} />}
						onClick={onRefresh}
						loading={loading}
					>
						Refresh
					</Button>
				</Group>
			</Group>

			{/* Service Status Alert */}
			{serviceStatus.status === 'emergency_stop' && (
				<Alert
					icon={<IconAlertTriangle size={16} />}
					title="Emergency Stop Active"
					color="red"
					mb="md"
				>
					The crawler service is in emergency stop mode.
					{serviceStatus.emergencyStopReason && ` Reason: ${serviceStatus.emergencyStopReason}`}
				</Alert>
			)}

			{/* Service Overview */}
			<Grid mb="lg">
				<Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
					<Card padding="lg" radius="md" withBorder>
						<Group justify="space-between">
							<div>
								<Text size="xs" tt="uppercase" fw={700} c="dimmed">
									Active Jobs
								</Text>
								<Text fw={700} size="xl">
									{serviceStatus.activeJobs}
								</Text>
							</div>
							<IconServer size={24} color="blue" />
						</Group>
					</Card>
				</Grid.Col>

				<Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
					<Card padding="lg" radius="md" withBorder>
						<Group justify="space-between">
							<div>
								<Text size="xs" tt="uppercase" fw={700} c="dimmed">
									Queued Jobs
								</Text>
								<Text fw={700} size="xl">
									{serviceStatus.queuedJobs}
								</Text>
							</div>
							<IconDatabase size={24} color="orange" />
						</Group>
					</Card>
				</Grid.Col>

				<Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
					<Card padding="lg" radius="md" withBorder>
						<Group justify="space-between">
							<div>
								<Text size="xs" tt="uppercase" fw={700} c="dimmed">
									Max Jobs
								</Text>
								<Text fw={700} size="xl">
									{serviceStatus.resourceLimits.maxConcurrentJobs}
								</Text>
							</div>
							<IconServer size={24} color="gray" />
						</Group>
					</Card>
				</Grid.Col>

				<Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
					<Card padding="lg" radius="md" withBorder>
						<Group justify="space-between">
							<div>
								<Text size="xs" tt="uppercase" fw={700} c="dimmed">
									Last Check
								</Text>
								<Text fw={700} size="sm">
									{serviceStatus.lastHealthCheck.toLocaleTimeString()}
								</Text>
							</div>
							<IconRefresh size={24} color="gray" />
						</Group>
					</Card>
				</Grid.Col>
			</Grid>

			{/* System Resources */}
			<Paper p="md" withBorder mb="lg">
				<Title order={4} mb="md">System Resources</Title>
				<Grid>
					<Grid.Col span={{ base: 12, md: 6 }}>
						<Stack gap="md">
							<div>
								<Group justify="space-between" mb="xs">
									<Group gap="xs">
										<IconCpu size={16} />
										<Text size="sm" fw={500}>CPU Usage</Text>
									</Group>
									<Text size="sm">{serviceStatus.systemLoad.cpu.toFixed(1)}%</Text>
								</Group>
								<Progress
									value={serviceStatus.systemLoad.cpu}
									color={getResourceColor(serviceStatus.systemLoad.cpu)}
									size="lg"
								/>
							</div>

							<div>
								<Group justify="space-between" mb="xs">
									<Group gap="xs">
										<IconMemory size={16} />
										<Text size="sm" fw={500}>Memory Usage</Text>
									</Group>
									<Text size="sm">{serviceStatus.systemLoad.memory.toFixed(1)}%</Text>
								</Group>
								<Progress
									value={serviceStatus.systemLoad.memory}
									color={getResourceColor(serviceStatus.systemLoad.memory)}
									size="lg"
								/>
							</div>
						</Stack>
					</Grid.Col>

					<Grid.Col span={{ base: 12, md: 6 }}>
						<Stack gap="md">
							<div>
								<Group justify="space-between" mb="xs">
									<Group gap="xs">
										<IconNetwork size={16} />
										<Text size="sm" fw={500}>Network Usage</Text>
									</Group>
									<Text size="sm">{serviceStatus.systemLoad.network.toFixed(1)}%</Text>
								</Group>
								<Progress
									value={serviceStatus.systemLoad.network}
									color={getResourceColor(serviceStatus.systemLoad.network)}
									size="lg"
								/>
							</div>

							<div>
								<Group justify="space-between" mb="xs">
									<Group gap="xs">
										<IconDatabase size={16} />
										<Text size="sm" fw={500}>Disk Usage</Text>
									</Group>
									<Text size="sm">{serviceStatus.systemLoad.disk.toFixed(1)}%</Text>
								</Group>
								<Progress
									value={serviceStatus.systemLoad.disk}
									color={getResourceColor(serviceStatus.systemLoad.disk)}
									size="lg"
								/>
							</div>
						</Stack>
					</Grid.Col>
				</Grid>

				<Text size="xs" c="dimmed" mt="md">
					Resource Limits: CPU {serviceStatus.resourceLimits.maxCpuUsage}%,
					Memory {serviceStatus.resourceLimits.maxMemoryUsage}%
				</Text>
			</Paper>

			{/* Service Controls */}
			<Paper p="md" withBorder>
				<Title order={4} mb="md">Service Controls</Title>
				<Group>
					{serviceStatus.status === 'stopped' && (
						<Button
							leftSection={<IconPlayerPlay size={16} />}
							onClick={() => handleServiceControl('resume')}
							loading={loading}
							color="green"
						>
							Start Service
						</Button>
					)}

					{serviceStatus.status === 'running' && (
						<Button
							leftSection={<IconPlayerPause size={16} />}
							onClick={() => handleServiceControl('pause', true)}
							loading={loading}
							color="yellow"
						>
							Pause Service
						</Button>
					)}

					{serviceStatus.status === 'paused' && (
						<Button
							leftSection={<IconPlayerPlay size={16} />}
							onClick={() => handleServiceControl('resume')}
							loading={loading}
							color="green"
						>
							Resume Service
						</Button>
					)}

					{(serviceStatus.status === 'running' || serviceStatus.status === 'paused') && (
						<Button
							leftSection={<IconSquare size={16} />}
							onClick={() => handleServiceControl('stop', true)}
							loading={loading}
							color="gray"
						>
							Stop Service
						</Button>
					)}

					<Button
						leftSection={<IconRefresh size={16} />}
						onClick={() => handleServiceControl('restart', true)}
						loading={loading}
						variant="light"
					>
						Restart Service
					</Button>

					<Button
						leftSection={<IconAlertTriangle size={16} />}
						onClick={handleEmergencyStop}
						loading={loading}
						color="red"
						variant="light"
					>
						Emergency Stop
					</Button>
				</Group>
			</Paper>

			{/* Reason Modal */}
			<Modal
				opened={reasonModalOpen}
				onClose={() =>
				{
					setReasonModalOpen(false);
					setReason('');
					setPendingAction('');
				}}
				title={`${pendingAction.replace('_', ' ').toUpperCase()} Service`}
				size="sm"
			>
				<Stack gap="md">
					<Text size="sm">
						Please provide a reason for this action:
					</Text>
					<Textarea
						placeholder="Enter reason for service control action..."
						value={reason}
						onChange={event => setReason(event.currentTarget.value)}
						rows={3}
						required
					/>
					<Group justify="flex-end">
						<Button
							variant="light"
							onClick={() =>
							{
								setReasonModalOpen(false);
								setReason('');
								setPendingAction('');
							}}
						>
							Cancel
						</Button>
						<Button
							onClick={executeActionWithReason}
							loading={loading}
							disabled={!reason.trim()}
							color={pendingAction === 'emergency_stop' ? 'red' : 'blue'}
						>
							{pendingAction.replace('_', ' ').toUpperCase()}
						</Button>
					</Group>
				</Stack>
			</Modal>
		</div>
	);
}
