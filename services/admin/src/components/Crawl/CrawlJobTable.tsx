'use client';

import {
	Table,
	Checkbox,
	Badge,
	Group,
	ActionIcon,
	Text,
	Progress,
	Tooltip,
	Menu,
	Button,
	Pagination,
	Select,
	LoadingOverlay,
	Alert,
} from '@mantine/core';
import { modals } from '@mantine/modals';
import { clientLogger } from '@/lib/logger';
import { notifications } from '@mantine/notifications';
import {
	IconDots,
	IconPlayerPlay,
	IconPlayerPause,
	IconSquare,
	IconRefresh,
	IconTrash,
	IconClock,
	IconEye,
	IconChevronUp,
	IconChevronDown,
	IconCalendar,
	IconUser,
} from '@tabler/icons-react';
import { useState, useCallback } from 'react';


import type {
	CrawlJob,
	CrawlJobSort,
	CrawlJobSortField,
} from '@/types/crawl';
import { CrawlJobBulkActions } from './CrawlJobBulkActions';
import { CrawlJobDetailsModal } from './CrawlJobDetailsModal';

interface CrawlJobTableProps
{
	jobs: CrawlJob[];
	loading: boolean;
	sort: CrawlJobSort;
	pagination: {
		page: number;
		pageSize: number;
		total: number;
	};
	selectedJobs: string[];
	onSortChange: (sort: CrawlJobSort) => void;
	onPaginationChange: (page: number, pageSize: number) => void;
	onSelectionChange: (jobIds: string[]) => void;
	onRefresh: () => void;
	showHistory?: boolean;
}

export function CrawlJobTable({
	jobs,
	loading,
	sort,
	pagination,
	selectedJobs,
	onSortChange,
	onPaginationChange,
	onSelectionChange,
	onRefresh,
	showHistory = false,
}: CrawlJobTableProps)
{
	const [detailsModalJob, setDetailsModalJob] = useState<CrawlJob | null>(null);

	// Handle sort change
	const handleSort = useCallback((field: CrawlJobSortField) =>
	{
		const direction = sort.field === field && sort.direction === 'asc' ? 'desc' : 'asc';
		onSortChange({ field, direction });
	}, [sort, onSortChange]);

	// Handle select all
	const handleSelectAll = useCallback((checked: boolean) =>
	{
		if (checked)
		{
			onSelectionChange(jobs.map(job => job.jobId));
		}
		else
		{
			onSelectionChange([]);
		}
	}, [jobs, onSelectionChange]);

	// Handle individual selection
	const handleSelectJob = useCallback((jobId: string, checked: boolean) =>
	{
		if (checked)
		{
			onSelectionChange([...selectedJobs, jobId]);
		}
		else
		{
			onSelectionChange(selectedJobs.filter(id => id !== jobId));
		}
	}, [selectedJobs, onSelectionChange]);

	// Handle job action
	const handleJobAction = useCallback(async (job: CrawlJob, action: string) =>
	{
		try
		{
			const response = await fetch(`/api/crawl/jobs/${job.jobId}`, {
				method: 'PATCH',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ action }),
			});

			if (!response.ok)
			{
				throw new Error('Failed to perform action');
			}

			notifications.show({
				title: 'Success',
				message: `Job ${action} completed successfully`,
				color: 'green',
			});

			onRefresh();
		}
		catch (error)
		{
			clientLogger.error('Error performing job action:', error);
			notifications.show({
				title: 'Error',
				message: error instanceof Error ? error.message : 'Failed to perform action',
				color: 'red',
			});
		}
	}, [onRefresh]);

	// Handle job deletion
	const handleDeleteJob = useCallback((job: CrawlJob) =>
	{
		modals.openConfirmModal({
			title: 'Delete Job',
			children: (
				<Text size="sm">
					Are you sure you want to delete the crawl job for <strong>{job.domain}</strong>?
					This action cannot be undone.
				</Text>
			),
			labels: { confirm: 'Delete', cancel: 'Cancel' },
			confirmProps: { color: 'red' },
			onConfirm: async () =>
			{
				try
				{
					const response = await fetch(`/api/crawl/jobs/${job.jobId}`, {
						method: 'DELETE',
					});

					if (!response.ok)
					{
						throw new Error('Failed to delete job');
					}

					notifications.show({
						title: 'Success',
						message: 'Job deleted successfully',
						color: 'green',
					});

					onRefresh();
				}
				catch (error)
				{
					clientLogger.error('Error deleting job:', error);
					notifications.show({
						title: 'Error',
						message: error instanceof Error ? error.message : 'Failed to delete job',
						color: 'red',
					});
				}
			},
		});
	}, [onRefresh]);

	// Get status color
	const getStatusColor = (status: string) =>
	{
		switch (status)
		{
			case 'running':
				return 'blue';
			case 'completed':
				return 'green';
			case 'failed':
				return 'red';
			case 'cancelled':
				return 'gray';
			case 'paused':
				return 'yellow';
			case 'pending':
			default:
				return 'orange';
		}
	};

	// Get priority color
	const getPriorityColor = (priority: string) =>
	{
		switch (priority)
		{
			case 'high':
				return 'red';
			case 'medium':
				return 'yellow';
			case 'low':
			default:
				return 'green';
		}
	};

	// Format duration
	const formatDuration = (duration: number | null) =>
	{
		if (!duration) return '-';
		const seconds = Math.floor(duration / 1000);
		const minutes = Math.floor(seconds / 60);
		const hours = Math.floor(minutes / 60);

		if (hours > 0)
		{
			return `${hours}h ${minutes % 60}m`;
		}
		if (minutes > 0)
		{
			return `${minutes}m ${seconds % 60}s`;
		}

		return `${seconds}s`;
	};

	// Render sort header
	const renderSortHeader = (field: CrawlJobSortField, label: string) => (
		<Table.Th
			style={{ cursor: 'pointer', userSelect: 'none' }}
			onClick={() => handleSort(field)}
		>
			<Group gap="xs">
				<Text fw={500}>{label}</Text>
				{sort.field === field && (
					sort.direction === 'asc' ? <IconChevronUp size={14} /> : <IconChevronDown size={14} />
				)}
			</Group>
		</Table.Th>
	);

	const allSelected = jobs.length > 0 && selectedJobs.length === jobs.length;
	const indeterminate = selectedJobs.length > 0 && selectedJobs.length < jobs.length;

	return (
		<div style={{ position: 'relative' }}>
			<LoadingOverlay visible={loading} />

			{/* Bulk Actions */}
			{selectedJobs.length > 0 && (
				<CrawlJobBulkActions
					selectedJobs={selectedJobs}
					onActionComplete={() =>
					{
						onSelectionChange([]);
						onRefresh();
					}}
				/>
			)}

			{/* Table */}
			<Table striped highlightOnHover>
				<Table.Thead>
					<Table.Tr>
						<Table.Th>
							<Checkbox
								checked={allSelected}
								indeterminate={indeterminate}
								onChange={event => handleSelectAll(event.currentTarget.checked)}
							/>
						</Table.Th>
						{renderSortHeader('domain', 'Domain')}
						{renderSortHeader('crawlType', 'Type')}
						{renderSortHeader('priority', 'Priority')}
						{renderSortHeader('status', 'Status')}
						{renderSortHeader('progress', 'Progress')}
						{renderSortHeader('scheduledAt', 'Scheduled')}
						{renderSortHeader('startedAt', 'Started')}
						{renderSortHeader('duration', 'Duration')}
						{renderSortHeader('requestedBy', 'Requested By')}
						<Table.Th>Actions</Table.Th>
					</Table.Tr>
				</Table.Thead>
				<Table.Tbody>
					{jobs.length === 0 ? (
						<Table.Tr>
							<Table.Td colSpan={11}>
								<Text ta="center" c="dimmed" py="xl">
									No crawl jobs found
								</Text>
							</Table.Td>
						</Table.Tr>
					) : (
						jobs.map(job => (
							<Table.Tr key={job.jobId}>
								<Table.Td>
									<Checkbox
										checked={selectedJobs.includes(job.jobId)}
										onChange={event => handleSelectJob(job.jobId, event.currentTarget.checked)}
									/>
								</Table.Td>
								<Table.Td>
									<Text fw={500}>{job.domain}</Text>
								</Table.Td>
								<Table.Td>
									<Badge variant="light" size="sm">
										{job.crawlType}
									</Badge>
								</Table.Td>
								<Table.Td>
									<Badge color={getPriorityColor(job.priority)} variant="light" size="sm">
										{job.priority}
									</Badge>
								</Table.Td>
								<Table.Td>
									<Badge color={getStatusColor(job.status)} variant="filled" size="sm">
										{job.status}
									</Badge>
									{job.errorMessage && (
										<Tooltip label={job.errorMessage} multiline>
											<ActionIcon size="xs" color="red" variant="subtle" ml="xs">
												<IconEye size={12} />
											</ActionIcon>
										</Tooltip>
									)}
								</Table.Td>
								<Table.Td>
									{job.status === 'running' ? (
										<Progress value={job.progress} size="sm" />
									) : (
										<Text size="sm" c="dimmed">
											{job.progress}%
										</Text>
									)}
								</Table.Td>
								<Table.Td>
									<Group gap="xs">
										<IconCalendar size={14} />
										<Text size="sm">
											{job.scheduledAt.toLocaleDateString()} {job.scheduledAt.toLocaleTimeString()}
										</Text>
									</Group>
								</Table.Td>
								<Table.Td>
									{job.startedAt ? (
										<Group gap="xs">
											<IconClock size={14} />
											<Text size="sm">
												{job.startedAt.toLocaleDateString()} {job.startedAt.toLocaleTimeString()}
											</Text>
										</Group>
									) : (
										<Text size="sm" c="dimmed">-</Text>
									)}
								</Table.Td>
								<Table.Td>
									<Text size="sm">{formatDuration(job.duration)}</Text>
								</Table.Td>
								<Table.Td>
									<Group gap="xs">
										<IconUser size={14} />
										<Text size="sm">{job.requestedBy}</Text>
									</Group>
								</Table.Td>
								<Table.Td>
									<Group gap="xs">
										<Tooltip label="View Details">
											<ActionIcon
												variant="light"
												size="sm"
												onClick={() => setDetailsModalJob(job)}
											>
												<IconEye size={14} />
											</ActionIcon>
										</Tooltip>
										<Menu shadow="md" width={200}>
											<Menu.Target>
												<ActionIcon variant="light" size="sm">
													<IconDots size={14} />
												</ActionIcon>
											</Menu.Target>
											<Menu.Dropdown>
												{job.status === 'pending' && (
													<Menu.Item
														leftSection={<IconPlayerPlay size={14} />}
														onClick={() => handleJobAction(job, 'resume')}
													>
														Start Now
													</Menu.Item>
												)}
												{job.status === 'running' && (
													<Menu.Item
														leftSection={<IconPlayerPause size={14} />}
														onClick={() => handleJobAction(job, 'pause')}
													>
														Pause
													</Menu.Item>
												)}
												{job.status === 'paused' && (
													<Menu.Item
														leftSection={<IconPlayerPlay size={14} />}
														onClick={() => handleJobAction(job, 'resume')}
													>
														Resume
													</Menu.Item>
												)}
												{(job.status === 'running' || job.status === 'pending') && (
													<Menu.Item
														leftSection={<IconSquare size={14} />}
														onClick={() => handleJobAction(job, 'cancel')}
													>
														Cancel
													</Menu.Item>
												)}
												{job.status === 'failed' && (
													<Menu.Item
														leftSection={<IconRefresh size={14} />}
														onClick={() => handleJobAction(job, 'retry')}
													>
														Retry
													</Menu.Item>
												)}
												<Menu.Divider />
												<Menu.Item
													leftSection={<IconTrash size={14} />}
													color="red"
													onClick={() => handleDeleteJob(job)}
													disabled={job.status === 'running'}
												>
													Delete
												</Menu.Item>
											</Menu.Dropdown>
										</Menu>
									</Group>
								</Table.Td>
							</Table.Tr>
						))
					)}
				</Table.Tbody>
			</Table>

			{/* Pagination */}
			<Group justify="space-between" mt="md">
				<Group>
					<Text size="sm" c="dimmed">
						Showing {((pagination.page - 1) * pagination.pageSize) + 1} to{' '}
						{Math.min(pagination.page * pagination.pageSize, pagination.total)} of{' '}
						{pagination.total} jobs
					</Text>
					<Select
						size="sm"
						value={pagination.pageSize.toString()}
						onChange={value => onPaginationChange(1, parseInt(value || '20'))}
						data={[
							{ value: '10', label: '10 per page' },
							{ value: '20', label: '20 per page' },
							{ value: '50', label: '50 per page' },
							{ value: '100', label: '100 per page' },
						]}
						w={120}
					/>
				</Group>
				<Pagination
					value={pagination.page}
					onChange={page => onPaginationChange(page, pagination.pageSize)}
					total={Math.ceil(pagination.total / pagination.pageSize)}
					size="sm"
				/>
			</Group>

			{/* Details Modal */}
			{detailsModalJob && (
				<CrawlJobDetailsModal
					job={detailsModalJob}
					opened={!!detailsModalJob}
					onClose={() => setDetailsModalJob(null)}
					onJobUpdate={onRefresh}
				/>
			)}
		</div>
	);
}
