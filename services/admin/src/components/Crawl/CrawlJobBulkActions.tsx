'use client';

import {
	Group,
	Button,
	Select,
	Modal,
	Text,
	Stack,
	Alert,
	Badge,
} from '@mantine/core';
import { DateTimePicker } from '@mantine/dates';
import { clientLogger } from '@/lib/logger';
import { modals } from '@mantine/modals';
import { notifications } from '@mantine/notifications';
import {
	IconPlayerPlay,
	IconPlayerPause,
	IconSquare,
	IconRefresh,
	IconTrash,
	IconClock,
	IconArrowUp,
	IconAlertTriangle,
} from '@tabler/icons-react';
import { useState, useCallback } from 'react';

import type { CrawlJobBulkOperationRequest } from '@/types/crawl';

interface CrawlJobBulkActionsProps
{
	selectedJobs: string[];
	onActionComplete: () => void;
}

export function CrawlJobBulkActions({ selectedJobs, onActionComplete }: CrawlJobBulkActionsProps)
{
	const [loading, setLoading] = useState(false);
	const [priorityModalOpen, setPriorityModalOpen] = useState(false);
	const [rescheduleModalOpen, setRescheduleModalOpen] = useState(false);
	const [selectedPriority, setSelectedPriority] = useState<string>('medium');
	const [scheduledDate, setScheduledDate] = useState<Date | null>(null);

	// Handle bulk action
	const handleBulkAction = useCallback(async (
		action: string,
		parameters?: any,
		confirmMessage?: string,
	) =>
	{
		const executeAction = async () =>
		{
			try
			{
				setLoading(true);

				const request: CrawlJobBulkOperationRequest = {
					action: action as unknown,
					jobIds: selectedJobs,
					parameters,
				};

				const response = await fetch('/api/crawl/jobs/bulk', {
					method: 'POST',
					headers: { 'Content-Type': 'application/json' },
					body: JSON.stringify(request),
				});

				if (!response.ok)
				{
					throw new Error('Failed to perform bulk action');
				}

				const result = await response.json();

				if (result.success)
				{
					notifications.show({
						title: 'Success',
						message: `Bulk ${action} completed. ${result.processedCount} jobs processed.`,
						color: 'green',
					});
				}
				else
				{
					notifications.show({
						title: 'Partial Success',
						message: `${result.processedCount} jobs processed, ${result.failedCount} failed.`,
						color: 'yellow',
					});
				}

				onActionComplete();
			}
			catch (error)
			{
				clientLogger.error('Error performing bulk action:', error);
				notifications.show({
					title: 'Error',
					message: error instanceof Error ? error.message : 'Failed to perform bulk action',
					color: 'red',
				});
			}
			finally
			{
				setLoading(false);
			}
		};

		if (confirmMessage)
		{
			modals.openConfirmModal({
				title: 'Confirm Bulk Action',
				children: (
					<Stack gap="md">
						<Text size="sm">{confirmMessage}</Text>
						<Alert icon={<IconAlertTriangle size={16} />} color="yellow">
							This action will affect {selectedJobs.length} job(s).
						</Alert>
					</Stack>
				),
				labels: { confirm: 'Confirm', cancel: 'Cancel' },
				confirmProps: { color: action === 'delete' ? 'red' : 'blue' },
				onConfirm: executeAction,
			});
		}
		else
		{
			await executeAction();
		}
	}, [selectedJobs, onActionComplete]);

	// Handle priority change
	const handlePriorityChange = useCallback(async () =>
	{
		await handleBulkAction('changePriority', { priority: selectedPriority });
		setPriorityModalOpen(false);
	}, [selectedPriority, handleBulkAction]);

	// Handle reschedule
	const handleReschedule = useCallback(async () =>
	{
		if (!scheduledDate)
		{
			notifications.show({
				title: 'Error',
				message: 'Please select a date and time',
				color: 'red',
			});
			return;
		}

		await handleBulkAction('reschedule', { scheduledAt: scheduledDate.toISOString() });
		setRescheduleModalOpen(false);
		setScheduledDate(null);
	}, [scheduledDate, handleBulkAction]);

	return (
		<>
			<Alert mb="md" color="blue">
				<Group justify="space-between">
					<Group>
						<Text size="sm" fw={500}>
							{selectedJobs.length} job(s) selected
						</Text>
						<Badge variant="light">{selectedJobs.length}</Badge>
					</Group>
					<Group>
						<Button
							size="xs"
							variant="light"
							leftSection={<IconPlayerPlay size={14} />}
							onClick={() => handleBulkAction('resume')}
							loading={loading}
						>
							Resume
						</Button>
						<Button
							size="xs"
							variant="light"
							leftSection={<IconPlayerPause size={14} />}
							onClick={() => handleBulkAction('pause')}
							loading={loading}
						>
							Pause
						</Button>
						<Button
							size="xs"
							variant="light"
							leftSection={<IconSquare size={14} />}
							onClick={() => handleBulkAction(
								'cancel',
								undefined,
								'Are you sure you want to cancel the selected jobs?',
							)}
							loading={loading}
						>
							Cancel
						</Button>
						<Button
							size="xs"
							variant="light"
							leftSection={<IconRefresh size={14} />}
							onClick={() => handleBulkAction(
								'retry',
								undefined,
								'Are you sure you want to retry the selected jobs?',
							)}
							loading={loading}
						>
							Retry
						</Button>
						<Button
							size="xs"
							variant="light"
							leftSection={<IconArrowUp size={14} />}
							onClick={() => setPriorityModalOpen(true)}
							loading={loading}
						>
							Priority
						</Button>
						<Button
							size="xs"
							variant="light"
							leftSection={<IconClock size={14} />}
							onClick={() => setRescheduleModalOpen(true)}
							loading={loading}
						>
							Reschedule
						</Button>
						<Button
							size="xs"
							variant="light"
							color="red"
							leftSection={<IconTrash size={14} />}
							onClick={() => handleBulkAction(
								'delete',
								undefined,
								'Are you sure you want to delete the selected jobs? This action cannot be undone.',
							)}
							loading={loading}
						>
							Delete
						</Button>
					</Group>
				</Group>
			</Alert>

			{/* Priority Change Modal */}
			<Modal
				opened={priorityModalOpen}
				onClose={() => setPriorityModalOpen(false)}
				title="Change Priority"
				size="sm"
			>
				<Stack gap="md">
					<Text size="sm">
						Change priority for {selectedJobs.length} selected job(s):
					</Text>
					<Select
						label="New Priority"
						value={selectedPriority}
						onChange={value => setSelectedPriority(value || 'medium')}
						data={[
							{ value: 'low', label: 'Low' },
							{ value: 'medium', label: 'Medium' },
							{ value: 'high', label: 'High' },
						]}
					/>
					<Group justify="flex-end">
						<Button variant="light" onClick={() => setPriorityModalOpen(false)}>
							Cancel
						</Button>
						<Button onClick={handlePriorityChange} loading={loading}>
							Change Priority
						</Button>
					</Group>
				</Stack>
			</Modal>

			{/* Reschedule Modal */}
			<Modal
				opened={rescheduleModalOpen}
				onClose={() => setRescheduleModalOpen(false)}
				title="Reschedule Jobs"
				size="sm"
			>
				<Stack gap="md">
					<Text size="sm">
						Reschedule {selectedJobs.length} selected job(s):
					</Text>
					<DateTimePicker
						label="New Scheduled Time"
						placeholder="Select date and time"
						value={scheduledDate}
						onChange={setScheduledDate}
						minDate={new Date()}
					/>
					<Group justify="flex-end">
						<Button
							variant="light"
							onClick={() =>
							{
								setRescheduleModalOpen(false);
								setScheduledDate(null);
							}}
						>
							Cancel
						</Button>
						<Button onClick={handleReschedule} loading={loading}>
							Reschedule
						</Button>
					</Group>
				</Stack>
			</Modal>
		</>
	);
}
