'use client';

import {
	Container,
	Paper,
	Group,
	Button,
	Text,
	Badge,
	Table,
	ScrollArea,
	ActionIcon,
	Tooltip,
	Modal,
	Stack,
	Code,
	Divider,
	Alert,
	Loader,
	Switch,
	NumberInput,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { clientLogger } from '@/lib/logger';
import { notifications } from '@mantine/notifications';
import {
	IconRefresh,
	IconDownload,
	IconSearch,
	IconFilter,
	IconEye,
	IconAlertTriangle,
	IconBug,
	IconClock,
	IconDatabase,
	IconActivity,
} from '@tabler/icons-react';
import { useState, useEffect, useCallback, useMemo } from 'react';
import type { FC } from 'react';
import { LogDetailsModal } from './LogDetailsModal';
import { LogExportModal } from './LogExportModal';
import { LogFilters } from './LogFilters';
import { LogSearch } from './LogSearch';
import type { LogEntryType, LogFilterType, LogAnalyticsType } from '../../types/logs';

type LogsViewerProps = {
	initialFilter?: Partial<LogFilterType>;
};

const LogsViewer: FC<LogsViewerProps> = ({ initialFilter = {} }) =>
{
	const [logs, setLogs] = useState<LogEntryType[]>([]);
	const [filteredLogs, setFilteredLogs] = useState<LogEntryType[]>([]);
	const [analytics, setAnalytics] = useState<LogAnalyticsType | null>(null);
	const [selectedLog, setSelectedLog] = useState<LogEntryType | null>(null);
	const [filter, setFilter] = useState<LogFilterType>({
		services: [],
		levels: [],
		...initialFilter,
	});
	const [loading, setLoading] = useState(false);
	const [realTimeEnabled, setRealTimeEnabled] = useState(false);
	const [autoRefreshInterval, setAutoRefreshInterval] = useState(5);
	const [eventSource, setEventSource] = useState<EventSource | null>(null);

	const [filtersOpened, { open: openFilters, close: closeFilters }] = useDisclosure(false);
	const [searchOpened, { open: openSearch, close: closeSearch }] = useDisclosure(false);
	const [exportOpened, { open: openExport, close: closeExport }] = useDisclosure(false);
	const [detailsOpened, { open: openDetails, close: closeDetails }] = useDisclosure(false);

	const fetchLogs = useCallback(async () =>
	{
		try
		{
			setLoading(true);
			const params = new URLSearchParams();

			if (filter.services.length > 0)
			{
				params.append('services', filter.services.join(','));
			}
			if (filter.levels.length > 0)
			{
				params.append('levels', filter.levels.join(','));
			}
			if (filter.startTime)
			{
				params.append('startTime', filter.startTime.toISOString());
			}
			if (filter.endTime)
			{
				params.append('endTime', filter.endTime.toISOString());
			}
			if (filter.correlationId)
			{
				params.append('correlationId', filter.correlationId);
			}
			if (filter.userId)
			{
				params.append('userId', filter.userId);
			}
			if (filter.action)
			{
				params.append('action', filter.action);
			}
			if (filter.errorType)
			{
				params.append('errorType', filter.errorType);
			}
			if (filter.searchQuery)
			{
				params.append('searchQuery', filter.searchQuery);
				params.append('useRegex', filter.useRegex?.toString() || 'false');
			}

			const response = await fetch(`/api/logs?${params.toString()}`);
			if (!response.ok)
			{
				throw new Error('Failed to fetch logs');
			}

			const data = await response.json();
			setLogs(data.logs);
			setAnalytics(data.analytics);
		}
		catch (error)
		{
			clientLogger.error('Error fetching logs:', error);
			notifications.show({
				title: 'Error',
				message: 'Failed to fetch logs',
				color: 'red',
			});
		}
		finally
		{
			setLoading(false);
		}
	}, [filter]);

	const setupRealTimeUpdates = useCallback(() =>
	{
		if (realTimeEnabled && !eventSource)
		{
			const params = new URLSearchParams();
			if (filter.services.length > 0)
			{
				params.append('services', filter.services.join(','));
			}
			if (filter.levels.length > 0)
			{
				params.append('levels', filter.levels.join(','));
			}

			const es = new EventSource(`/api/logs/stream?${params.toString()}`);

			es.onmessage = (event) =>
			{
				const newLog: LogEntryType = JSON.parse(event.data);
				setLogs(prev => [newLog, ...prev.slice(0, 999)]); // Keep last 1000 logs
			};

			es.onerror = () =>
			{
				notifications.show({
					title: 'Connection Error',
					message: 'Real-time log streaming disconnected',
					color: 'orange',
				});
			};

			setEventSource(es);
		}
		else if (!realTimeEnabled && eventSource)
		{
			eventSource.close();
			setEventSource(null);
		}
	}, [realTimeEnabled, eventSource, filter.services, filter.levels]);

	useEffect(() =>
	{
		setupRealTimeUpdates();
		return () =>
		{
			if (eventSource)
			{
				eventSource.close();
			}
		};
	}, [setupRealTimeUpdates]);

	useEffect(() =>
	{
		if (!realTimeEnabled)
		{
			fetchLogs();
		}
	}, [fetchLogs, realTimeEnabled]);

	useEffect(() =>
	{
		let interval: NodeJS.Timeout;
		if (!realTimeEnabled && autoRefreshInterval > 0)
		{
			interval = setInterval(fetchLogs, autoRefreshInterval * 1000);
		}
		return () =>
		{
			if (interval)
			{
				clearInterval(interval);
			}
		};
	}, [fetchLogs, realTimeEnabled, autoRefreshInterval]);

	const filteredAndSearchedLogs = useMemo(() =>
	{
		let result = logs;

		// Apply search filter
		if (filter.searchQuery)
		{
			const query = filter.searchQuery.toLowerCase();
			result = result.filter((log) =>
			{
				if (filter.useRegex)
				{
					try
					{
						const regex = new RegExp(filter.searchQuery!, 'i');
						return regex.test(log.message) ||
                   regex.test(log.service) ||
                   (log.action && regex.test(log.action)) ||
                   (log.errorType && regex.test(log.errorType));
					}
					catch
					{
						return false;
					}
				}
				else
				{
					return log.message.toLowerCase().includes(query) ||
                 log.service.toLowerCase().includes(query) ||
                 (log.action && log.action.toLowerCase().includes(query)) ||
                 (log.errorType && log.errorType.toLowerCase().includes(query));
				}
			});
		}

		return result;
	}, [logs, filter.searchQuery, filter.useRegex]);

	const getLevelColor = (level: string) =>
	{
		switch (level)
		{
			case 'error':
			case 'fatal':
				return 'red';
			case 'warn':
				return 'orange';
			case 'info':
				return 'blue';
			case 'debug':
				return 'gray';
			default:
				return 'gray';
		}
	};

	const getServiceColor = (service: string) =>
	{
		const colors = {
			'web-app': 'blue',
			'crawler': 'green',
			'ranking-engine': 'purple',
			'scheduler': 'orange',
			'domain-seeder': 'teal',
			'admin': 'indigo',
		};
		return colors[service as keyof typeof colors] || 'gray';
	};

	const handleLogClick = (log: LogEntryType) =>
	{
		setSelectedLog(log);
		openDetails();
	};

	const handleExport = () =>
	{
		openExport();
	};

	return (
		<Container size="xl" py="md">
			<Paper shadow="sm" p="md">
				<Group justify="space-between" mb="md">
					<Group>
						<Text size="lg" fw={600}>System Logs</Text>
						{analytics && (
							<Group gap="xs">
								<Badge color="blue" variant="light">
									{analytics.totalLogs.toLocaleString()} logs
								</Badge>
								<Badge color={analytics.errorRate > 10 ? 'red' : 'green'} variant="light">
									{analytics.errorRate.toFixed(1)}% errors
								</Badge>
							</Group>
						)}
					</Group>

					<Group>
						<Switch
							label="Real-time"
							checked={realTimeEnabled}
							onChange={event => setRealTimeEnabled(event.currentTarget.checked)}
						/>

						{!realTimeEnabled && (
							<NumberInput
								label="Auto-refresh (seconds)"
								value={autoRefreshInterval}
								onChange={value => setAutoRefreshInterval(Number(value) || 0)}
								min={0}
								max={60}
								w={150}
								size="xs"
							/>
						)}

						<Tooltip label="Search Logs">
							<ActionIcon variant="light" onClick={openSearch}>
								<IconSearch size={16} />
							</ActionIcon>
						</Tooltip>

						<Tooltip label="Filter Logs">
							<ActionIcon variant="light" onClick={openFilters}>
								<IconFilter size={16} />
							</ActionIcon>
						</Tooltip>

						<Tooltip label="Export Logs">
							<ActionIcon variant="light" onClick={handleExport}>
								<IconDownload size={16} />
							</ActionIcon>
						</Tooltip>

						<Tooltip label="Refresh">
							<ActionIcon
								variant="light"
								onClick={fetchLogs}
								loading={loading}
							>
								<IconRefresh size={16} />
							</ActionIcon>
						</Tooltip>
					</Group>
				</Group>

				{analytics && (
					<Group mb="md" gap="md">
						<Paper p="xs" withBorder>
							<Group gap="xs">
								<IconActivity size={16} />
								<Text size="sm">Avg Response: {analytics.averageResponseTime}ms</Text>
							</Group>
						</Paper>

						<Paper p="xs" withBorder>
							<Group gap="xs">
								<IconAlertTriangle size={16} />
								<Text size="sm">Top Error: {analytics.topErrors[0]?.type || 'None'}</Text>
							</Group>
						</Paper>
					</Group>
				)}

				<ScrollArea h={600}>
					<Table striped highlightOnHover>
						<Table.Thead>
							<Table.Tr>
								<Table.Th>Timestamp</Table.Th>
								<Table.Th>Level</Table.Th>
								<Table.Th>Service</Table.Th>
								<Table.Th>Message</Table.Th>
								<Table.Th>Correlation ID</Table.Th>
								<Table.Th>Actions</Table.Th>
							</Table.Tr>
						</Table.Thead>
						<Table.Tbody>
							{loading && (
								<Table.Tr>
									<Table.Td colSpan={6}>
										<Group justify="center" p="md">
											<Loader size="sm" />
											<Text>Loading logs...</Text>
										</Group>
									</Table.Td>
								</Table.Tr>
							)}

							{!loading && filteredAndSearchedLogs.length === 0 && (
								<Table.Tr>
									<Table.Td colSpan={6}>
										<Text ta="center" c="dimmed" p="md">
											No logs found matching the current filters
										</Text>
									</Table.Td>
								</Table.Tr>
							)}

							{filteredAndSearchedLogs.map(log => (
								<Table.Tr
									key={log.id}
									style={{ cursor: 'pointer' }}
									onClick={() => handleLogClick(log)}
								>
									<Table.Td>
										<Text size="xs" c="dimmed">
											{new Date(log.timestamp).toLocaleString()}
										</Text>
									</Table.Td>
									<Table.Td>
										<Badge
											color={getLevelColor(log.level)}
											variant="light"
											size="sm"
										>
											{log.level.toUpperCase()}
										</Badge>
									</Table.Td>
									<Table.Td>
										<Badge
											color={getServiceColor(log.service)}
											variant="outline"
											size="sm"
										>
											{log.service}
										</Badge>
									</Table.Td>
									<Table.Td>
										<Text size="sm" lineClamp={2}>
											{log.message}
										</Text>
										{log.errorType && (
											<Badge color="red" variant="light" size="xs" mt={2}>
												{log.errorType}
											</Badge>
										)}
									</Table.Td>
									<Table.Td>
										{log.correlationId && (
											<Code>{log.correlationId.slice(0, 8)}...</Code>
										)}
									</Table.Td>
									<Table.Td>
										<Group gap="xs">
											<Tooltip label="View Details">
												<ActionIcon
													size="sm"
													variant="subtle"
													onClick={(e) =>
													{
														e.stopPropagation();
														handleLogClick(log);
													}}
												>
													<IconEye size={14} />
												</ActionIcon>
											</Tooltip>

											{log.stackTrace && (
												<Tooltip label="Has Stack Trace">
													<ActionIcon size="sm" variant="subtle" color="orange">
														<IconBug size={14} />
													</ActionIcon>
												</Tooltip>
											)}

											{log.duration && (
												<Tooltip label={`Duration: ${log.duration}ms`}>
													<ActionIcon size="sm" variant="subtle" color="blue">
														<IconClock size={14} />
													</ActionIcon>
												</Tooltip>
											)}
										</Group>
									</Table.Td>
								</Table.Tr>
							))}
						</Table.Tbody>
					</Table>
				</ScrollArea>
			</Paper>

			<Modal
				opened={filtersOpened}
				onClose={closeFilters}
				title="Log Filters"
				size="lg"
			>
				<LogFilters
					filter={filter}
					onChange={setFilter}
					onApply={() =>
					{
						closeFilters();
						fetchLogs();
					}}
				/>
			</Modal>

			<Modal
				opened={searchOpened}
				onClose={closeSearch}
				title="Search Logs"
				size="md"
			>
				<LogSearch
					filter={filter}
					onChange={setFilter}
					onSearch={() =>
					{
						closeSearch();
						fetchLogs();
					}}
				/>
			</Modal>

			<Modal
				opened={exportOpened}
				onClose={closeExport}
				title="Export Logs"
				size="md"
			>
				<LogExportModal
					filter={filter}
					totalLogs={filteredAndSearchedLogs.length}
					onClose={closeExport}
				/>
			</Modal>

			<Modal
				opened={detailsOpened}
				onClose={closeDetails}
				title="Log Details"
				size="xl"
			>
				{selectedLog && (
					<LogDetailsModal
						log={selectedLog}
						onClose={closeDetails}
					/>
				)}
			</Modal>
		</Container>
	);
};

export default LogsViewer;
