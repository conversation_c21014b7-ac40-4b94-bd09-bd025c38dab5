'use client';

import {
	Card,
	Text,
	Badge,
	Group,
	Stack,
	ScrollArea,
	ActionIcon,
	Tooltip,
	Select,
	TextInput,
	Switch,
	Alert,
	Code,
} from '@mantine/core';
import {
	IconActivity,
	IconPlayerPause,
	IconPlayerPlay,
	IconTrash,
	IconDownload,
	IconSearch,
} from '@tabler/icons-react';
import { useState, useEffect, useRef } from 'react';
import { useRealtimeSubscription } from '@/hooks/useRealtimeSubscription';
import type { LogStreamUpdateType } from '@/lib/realtime/types';
import type { LogEntryType } from '@/types/logs';

interface RealtimeLogStreamProps
{
	className?: string;
	maxEntries?: number;
	autoScroll?: boolean;
}

export function RealtimeLogStream({
	className,
	maxEntries = 1000,
	autoScroll = true
}: RealtimeLogStreamProps)
{
	const [logEntries, setLogEntries] = useState<LogEntryType[]>([]);
	const [isPaused, setIsPaused] = useState(false);
	const [selectedService, setSelectedService] = useState<string>('');
	const [selectedLevel, setSelectedLevel] = useState<string>('');
	const [searchQuery, setSearchQuery] = useState('');
	const [autoScrollEnabled, setAutoScrollEnabled] = useState(autoScroll);
	const [messageCount, setMessageCount] = useState(0);

	const scrollAreaRef = useRef<HTMLDivElement>(null);
	const shouldAutoScroll = useRef(true);

	// Real-time log stream subscription
	const { messageCount: streamMessageCount } = useRealtimeSubscription<LogStreamUpdateType>({
		type: 'log_stream',
		filters: {
			...(selectedService && { 'data.service': selectedService }),
			...(selectedLevel && { 'data.level': selectedLevel }),
		},
		enabled: !isPaused,
		bufferSize: 10, // Buffer up to 10 log entries
		bufferTimeout: 500, // Flush buffer every 500ms
		onMessage: (message) =>
		{
			const update = message.data as LogStreamUpdateType['data'];

			if (!isPaused)
			{
				setLogEntries((prev) =>
				{
					const newEntries = [...prev, ...update.entries];

					// Apply search filter if active
					const filteredEntries = searchQuery
						? newEntries.filter(entry =>
							entry.message.toLowerCase().includes(searchQuery.toLowerCase()) ||
                entry.service.toLowerCase().includes(searchQuery.toLowerCase()))
						: newEntries;

					// Keep only the most recent entries
					return filteredEntries.slice(-maxEntries);
				});

				setMessageCount(prev => prev + update.entries.length);

				// Auto-scroll to bottom if enabled and user hasn't scrolled up
				if (autoScrollEnabled && shouldAutoScroll.current)
				{
					setTimeout(() =>
					{
						scrollAreaRef.current?.scrollTo({ top: scrollAreaRef.current.scrollHeight });
					}, 50);
				}
			}
		},
	});

	// Handle scroll to detect if user scrolled up
	const handleScroll = (position: { x: number; y: number }) =>
	{
		if (scrollAreaRef.current)
		{
			const { scrollTop, scrollHeight, clientHeight } = scrollAreaRef.current;
			const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10;
			shouldAutoScroll.current = isAtBottom;
		}
	};

	const togglePause = () =>
	{
		setIsPaused(!isPaused);
	};

	const clearLogs = () =>
	{
		setLogEntries([]);
		setMessageCount(0);
	};

	const exportLogs = () =>
	{
		const logsText = logEntries
			.map(entry => `[${entry.timestamp}] [${entry.level.toUpperCase()}] [${entry.service}] ${entry.message}`)
			.join('\n');

		const blob = new Blob([logsText], { type: 'text/plain' });
		const url = URL.createObjectURL(blob);
		const a = document.createElement('a');
		a.href = url;
		a.download = `logs-${new Date().toISOString().slice(0, 19)}.txt`;
		document.body.appendChild(a);
		a.click();
		document.body.removeChild(a);
		URL.revokeObjectURL(url);
	};

	const getLogLevelColor = (level: string) =>
	{
		switch (level.toLowerCase())
		{
			case 'error':
				return 'red';
			case 'warn':
			case 'warning':
				return 'yellow';
			case 'info':
				return 'blue';
			case 'debug':
				return 'gray';
			default:
				return 'gray';
		}
	};

	const formatTimestamp = (timestamp: Date | string) =>
		new Date(timestamp).toLocaleTimeString();

	// Filter logs based on search query
	const filteredLogs = searchQuery
		? logEntries.filter(entry =>
			entry.message.toLowerCase().includes(searchQuery.toLowerCase()) ||
        entry.service.toLowerCase().includes(searchQuery.toLowerCase()))
		: logEntries;

	const services = ['web-app', 'crawler', 'ranking-engine', 'scheduler', 'domain-seeder'];
	const levels = ['error', 'warn', 'info', 'debug'];

	return (
		<Stack gap="md" className={className}>
			{/* Real-time Status */}
			<Alert
				icon={<IconActivity size={16} />}
				title="Real-time Log Streaming"
				color="blue"
				variant="light"
			>
				<Group justify="space-between">
					<Text size="sm">
						{isPaused ? 'Log streaming paused' : 'Streaming live logs from all services'}
					</Text>
					<Group gap="md">
						<Text size="sm" c="dimmed">
							Messages: {messageCount}
						</Text>
						<Badge
							size="sm"
							color={isPaused ? 'red' : 'green'}
							variant="light"
						>
							{isPaused ? 'Paused' : 'Live'}
						</Badge>
					</Group>
				</Group>
			</Alert>

			{/* Controls */}
			<Card withBorder>
				<Stack gap="md">
					<Group justify="space-between">
						<Text fw={500}>Stream Controls</Text>
						<Group gap="xs">
							<Tooltip label={isPaused ? 'Resume Stream' : 'Pause Stream'}>
								<ActionIcon
									color={isPaused ? 'green' : 'yellow'}
									variant="light"
									onClick={togglePause}
								>
									{isPaused ? <IconPlayerPlay size={16} /> : <IconPlayerPause size={16} />}
								</ActionIcon>
							</Tooltip>

							<Tooltip label="Clear Logs">
								<ActionIcon
									color="red"
									variant="light"
									onClick={clearLogs}
								>
									<IconTrash size={16} />
								</ActionIcon>
							</Tooltip>

							<Tooltip label="Export Logs">
								<ActionIcon
									color="blue"
									variant="light"
									onClick={exportLogs}
									disabled={logEntries.length === 0}
								>
									<IconDownload size={16} />
								</ActionIcon>
							</Tooltip>
						</Group>
					</Group>

					<Group grow>
						<Select
							placeholder="All Services"
							data={services.map(service => ({ value: service, label: service }))}
							value={selectedService}
							onChange={value => setSelectedService(value || '')}
							clearable
						/>

						<Select
							placeholder="All Levels"
							data={levels.map(level => ({ value: level, label: level.toUpperCase() }))}
							value={selectedLevel}
							onChange={value => setSelectedLevel(value || '')}
							clearable
						/>

						<TextInput
							placeholder="Search logs..."
							value={searchQuery}
							onChange={event => setSearchQuery(event.currentTarget.value)}
							leftSection={<IconSearch size={16} />}
						/>
					</Group>

					<Group justify="space-between">
						<Switch
							label="Auto-scroll"
							checked={autoScrollEnabled}
							onChange={event => setAutoScrollEnabled(event.currentTarget.checked)}
						/>

						<Text size="sm" c="dimmed">
							Showing {filteredLogs.length} of {logEntries.length} entries
						</Text>
					</Group>
				</Stack>
			</Card>

			{/* Log Stream */}
			<Card withBorder style={{ height: 600 }}>
				<ScrollArea
					h="100%"
					ref={scrollAreaRef}
					onScrollPositionChange={handleScroll}
				>
					<Stack gap="xs">
						{filteredLogs.length === 0 ? (
							<Text size="sm" c="dimmed" ta="center" py="xl">
								{isPaused ? 'Log streaming is paused' : 'No log entries yet'}
							</Text>
						) : (
							filteredLogs.map((entry, index) => (
								<Group
									key={`${entry.timestamp}-${index}`}
									gap="sm"
									align="flex-start"
									wrap="nowrap"
									style={{
										padding: '8px',
										borderRadius: '4px',
										backgroundColor: entry.level === 'error'
											? 'var(--mantine-color-red-0)'
											: entry.level === 'warn'
												? 'var(--mantine-color-yellow-0)'
												: 'var(--mantine-color-gray-0)',
										fontFamily: 'monospace',
										fontSize: '12px',
									}}
								>
									<Text size="xs" c="dimmed" style={{ minWidth: '80px' }}>
										{formatTimestamp(entry.timestamp)}
									</Text>

									<Badge
										size="xs"
										color={getLogLevelColor(entry.level)}
										variant="light"
										style={{ minWidth: '50px' }}
									>
										{entry.level.toUpperCase()}
									</Badge>

									<Badge
										size="xs"
										color="blue"
										variant="outline"
										style={{ minWidth: '80px' }}
									>
										{entry.service}
									</Badge>

									<Code
										style={{
											flex: 1,
											whiteSpace: 'pre-wrap',
											wordBreak: 'break-word',
										}}
									>
										{entry.message}
									</Code>
								</Group>
							))
						)}
					</Stack>
				</ScrollArea>
			</Card>
		</Stack>
	);
}

export default RealtimeLogStream;
