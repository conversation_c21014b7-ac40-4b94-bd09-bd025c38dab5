'use client';

import {
	Stack,
	Group,
	Button,
	TextInput,
	Switch,
	Paper,
	Text,
	Alert,
	ActionIcon,
	Tooltip,
	Select,
	Textarea,
} from '@mantine/core';
import {
	IconSearch,
	IconRegex,
	IconInfoCircle,
	IconBookmark,
	IconTrash,
} from '@tabler/icons-react';
import { useState, useEffect } from 'react';
import { clientLogger } from '@/lib/logger';
import type { FC } from 'react';
import type { LogFilterType, SavedSearchType } from '../../types/logs';

type LogSearchProps = {
	filter: LogFilterType;
	onChange: (filter: LogFilterType) => void;
	onSearch: () => void;
};

const LogSearch: FC<LogSearchProps> = ({ filter, onChange, onSearch }) =>
{
	const [searchQuery, setSearchQuery] = useState(filter.searchQuery || '');
	const [useRegex, setUseRegex] = useState(filter.useRegex || false);
	const [savedSearches, setSavedSearches] = useState<SavedSearchType[]>([]);
	const [selectedSavedSearch, setSelectedSavedSearch] = useState<string>('');
	const [saveSearchName, setSaveSearchName] = useState('');
	const [saveSearchDescription, setSaveSearchDescription] = useState('');
	const [showSaveForm, setShowSaveForm] = useState(false);
	const [regexError, setRegexError] = useState<string>('');

	useEffect(() =>
	{
		fetchSavedSearches();
	}, []);

	const fetchSavedSearches = async () =>
	{
		try
		{
			const response = await fetch('/api/logs/saved-searches');
			if (response.ok)
			{
				const data = await response.json();
				setSavedSearches(data);
			}
		}
		catch (error)
		{
			clientLogger.error('Error fetching saved searches:', error);
		}
	};

	const validateRegex = (pattern: string) =>
	{
		if (!useRegex || !pattern)
		{
			setRegexError('');
			return true;
		}

		try
		{
			new RegExp(pattern);
			setRegexError('');
			return true;
		}
		catch (error)
		{
			setRegexError('Invalid regular expression');
			return false;
		}
	};

	const handleSearch = () =>
	{
		if (!validateRegex(searchQuery))
		{
			return;
		}

		onChange({
			...filter,
			searchQuery: searchQuery || undefined,
			useRegex,
		});
		onSearch();
	};

	const handleSaveSearch = async () =>
	{
		if (!saveSearchName.trim())
		{
			return;
		}

		try
		{
			const response = await fetch('/api/logs/saved-searches', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					name: saveSearchName,
					description: saveSearchDescription,
					filter: {
						...filter,
						searchQuery: searchQuery || undefined,
						useRegex,
					},
				}),
			});

			if (response.ok)
			{
				setSaveSearchName('');
				setSaveSearchDescription('');
				setShowSaveForm(false);
				fetchSavedSearches();
			}
		}
		catch (error)
		{
			clientLogger.error('Error saving search:', error);
		}
	};

	const handleLoadSavedSearch = (searchId: string) =>
	{
		const savedSearch = savedSearches.find(s => s.id === searchId);
		if (savedSearch)
		{
			setSearchQuery(savedSearch.filter.searchQuery || '');
			setUseRegex(savedSearch.filter.useRegex || false);
			onChange(savedSearch.filter);
		}
	};

	const handleDeleteSavedSearch = async (searchId: string) =>
	{
		try
		{
			const response = await fetch(`/api/logs/saved-searches/${searchId}`, {
				method: 'DELETE',
			});

			if (response.ok)
			{
				fetchSavedSearches();
				if (selectedSavedSearch === searchId)
				{
					setSelectedSavedSearch('');
				}
			}
		}
		catch (error)
		{
			clientLogger.error('Error deleting saved search:', error);
		}
	};

	const searchExamples = [
		{
			title: 'Error Messages',
			pattern: 'error|exception|failed',
			description: 'Find logs containing error-related keywords',
		},
		{
			title: 'Database Queries',
			pattern: 'SELECT|INSERT|UPDATE|DELETE',
			description: 'Find database operation logs',
		},
		{
			title: 'HTTP Status Codes',
			pattern: '\\b[45]\\d{2}\\b',
			description: 'Find 4xx and 5xx HTTP status codes',
		},
		{
			title: 'IP Addresses',
			pattern: '\\b(?:[0-9]{1,3}\\.){3}[0-9]{1,3}\\b',
			description: 'Find logs containing IP addresses',
		},
	];

	return (
		<Stack gap="md">
			<Paper p="md" withBorder>
				<Stack gap="md">
					<Group justify="space-between">
						<Text fw={500}>Search Query</Text>
						<Group>
							<Switch
								label="Use Regex"
								checked={useRegex}
								onChange={(event) =>
								{
									setUseRegex(event.currentTarget.checked);
									validateRegex(searchQuery);
								}}
							/>
							<IconRegex size={16} />
						</Group>
					</Group>

					<TextInput
						placeholder={useRegex ? 'Enter regular expression...' : 'Enter search terms...'}
						value={searchQuery}
						onChange={(event) =>
						{
							const value = event.currentTarget.value;
							setSearchQuery(value);
							validateRegex(value);
						}}
						error={regexError}
						leftSection={<IconSearch size={16} />}
					/>

					{regexError && (
						<Alert color="red" icon={<IconInfoCircle size={16} />}>
							{regexError}
						</Alert>
					)}

					{useRegex && (
						<Alert color="blue" icon={<IconInfoCircle size={16} />}>
							<Text size="sm">
								Regular expressions allow powerful pattern matching.
								Use <code>\b</code> for word boundaries, <code>|</code> for OR,
								and <code>.*</code> for any characters.
							</Text>
						</Alert>
					)}
				</Stack>
			</Paper>

			{useRegex && (
				<Paper p="md" withBorder>
					<Stack gap="sm">
						<Text size="sm" fw={500} c="dimmed">Common Regex Examples</Text>
						{searchExamples.map((example, index) => (
							<Group key={index} justify="space-between">
								<Stack gap={2}>
									<Text size="sm" fw={500}>{example.title}</Text>
									<Text size="xs" c="dimmed">{example.description}</Text>
								</Stack>
								<Button
									size="xs"
									variant="light"
									onClick={() => setSearchQuery(example.pattern)}
								>
									Use Pattern
								</Button>
							</Group>
						))}
					</Stack>
				</Paper>
			)}

			<Paper p="md" withBorder>
				<Stack gap="md">
					<Group justify="space-between">
						<Text fw={500}>Saved Searches</Text>
						<Button
							size="xs"
							variant="light"
							onClick={() => setShowSaveForm(!showSaveForm)}
							leftSection={<IconBookmark size={14} />}
						>
							Save Current Search
						</Button>
					</Group>

					{showSaveForm && (
						<Stack gap="sm">
							<TextInput
								label="Search Name"
								placeholder="Enter a name for this search"
								value={saveSearchName}
								onChange={event => setSaveSearchName(event.currentTarget.value)}
							/>
							<Textarea
								label="Description (optional)"
								placeholder="Describe what this search finds"
								value={saveSearchDescription}
								onChange={event => setSaveSearchDescription(event.currentTarget.value)}
								rows={2}
							/>
							<Group>
								<Button size="xs" onClick={handleSaveSearch}>
									Save Search
								</Button>
								<Button size="xs" variant="subtle" onClick={() => setShowSaveForm(false)}>
									Cancel
								</Button>
							</Group>
						</Stack>
					)}

					{savedSearches.length > 0 && (
						<Select
							label="Load Saved Search"
							placeholder="Select a saved search"
							data={savedSearches.map(search => ({
								value: search.id,
								label: search.name,
							}))}
							value={selectedSavedSearch}
							onChange={(value) =>
							{
								setSelectedSavedSearch(value || '');
								if (value)
								{
									handleLoadSavedSearch(value);
								}
							}}
							clearable
						/>
					)}

					{savedSearches.length > 0 && (
						<Stack gap="xs">
							{savedSearches.map(search => (
								<Group key={search.id} justify="space-between" p="xs">
									<Stack gap={2}>
										<Text size="sm" fw={500}>{search.name}</Text>
										{search.description && (
											<Text size="xs" c="dimmed">{search.description}</Text>
										)}
										<Text size="xs" c="dimmed">
											Created: {new Date(search.createdAt).toLocaleDateString()}
										</Text>
									</Stack>
									<Group gap="xs">
										<Button
											size="xs"
											variant="light"
											onClick={() => handleLoadSavedSearch(search.id)}
										>
											Load
										</Button>
										<Tooltip label="Delete saved search">
											<ActionIcon
												size="sm"
												color="red"
												variant="subtle"
												onClick={() => handleDeleteSavedSearch(search.id)}
											>
												<IconTrash size={14} />
											</ActionIcon>
										</Tooltip>
									</Group>
								</Group>
							))}
						</Stack>
					)}
				</Stack>
			</Paper>

			<Group justify="flex-end">
				<Button variant="subtle" onClick={() =>
				{
					setSearchQuery('');
					setUseRegex(false);
					onChange({ ...filter, searchQuery: undefined, useRegex: false });
				}}>
					Clear Search
				</Button>
				<Button onClick={handleSearch} disabled={!!regexError}>
					Search Logs
				</Button>
			</Group>
		</Stack>
	);
};

export { LogSearch };
