'use client';

import {
	Container,
	Paper,
	Group,
	Text,
	Badge,
	Grid,
	Stack,
	Select,
	Button,
	Table,
	ScrollArea,
	Alert,
	ActionIcon,
	Tooltip,
} from '@mantine/core';
import {
	IconTrendingUp,
	IconAlertTriangle,
	IconActivity,
	IconClock,
	IconRefresh,
	IconBug,
} from '@tabler/icons-react';
import { useState, useEffect } from 'react';
import { clientLogger } from '@/lib/logger';
import {
	LineChart,
	Line,
	AreaChart,
	Area,
	BarChart,
	Bar,
	PieChart,
	Pie,
	Cell,
	XAxis,
	YAxis,
	CartesianGrid,
	Tooltip as RechartsTooltip,
	Legend,
	ResponsiveContainer,
} from 'recharts';
import type { FC } from 'react';
import type { LogAnalyticsType as LogAnalyticsType, ErrorPatternType } from '../../types/logs';

const LogAnalytics: FC = () =>
{
	const [analytics, setAnalytics] = useState<LogAnalyticsType | null>(null);
	const [errorPatterns, setErrorPatterns] = useState<ErrorPatternType[]>([]);
	const [timeRange, setTimeRange] = useState('24h');
	const [loading, setLoading] = useState(false);

	const timeRangeOptions = [
		{ value: '1h', label: 'Last Hour' },
		{ value: '6h', label: 'Last 6 Hours' },
		{ value: '24h', label: 'Last 24 Hours' },
		{ value: '7d', label: 'Last 7 Days' },
		{ value: '30d', label: 'Last 30 Days' },
	];

	const fetchAnalytics = async () =>
	{
		try
		{
			setLoading(true);
			const [analyticsResponse, patternsResponse] = await Promise.all([
				fetch(`/api/logs/analytics?timeRange=${timeRange}`),
				fetch(`/api/logs/error-patterns?timeRange=${timeRange}`),
			]);

			if (analyticsResponse.ok)
			{
				const analyticsData = await analyticsResponse.json();
				setAnalytics(analyticsData);
			}

			if (patternsResponse.ok)
			{
				const patternsData = await patternsResponse.json();
				setErrorPatterns(patternsData);
			}
		}
		catch (error)
		{
			clientLogger.error('Error fetching analytics:', error);
		}
		finally
		{
			setLoading(false);
		}
	};

	useEffect(() =>
	{
		fetchAnalytics();
	}, [timeRange]);

	const COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#8dd1e1', '#d084d0'];

	const formatTrendData = (trends: any[]) =>
		trends.map(trend => ({
			...trend,
			timestamp: new Date(trend.timestamp).toLocaleTimeString(),
			errorRate: ((trend.errorCount / trend.count) * 100).toFixed(1),
		}));

	const formatServiceData = (distribution: Record<string, number>) =>
		Object.entries(distribution).map(([service, count]) => ({
			name: service,
			value: count,
		}));

	const formatLevelData = (distribution: Record<string, number>) =>
		Object.entries(distribution).map(([level, count]) => ({
			name: level.toUpperCase(),
			value: count,
		}));

	const getSeverityColor = (severity: string) =>
	{
		switch (severity)
		{
			case 'critical':
				return 'red';
			case 'high':
				return 'orange';
			case 'medium':
				return 'yellow';
			case 'low':
				return 'blue';
			default:
				return 'gray';
		}
	};

	if (!analytics)
	{
		return (
			<Container size="xl" py="md">
				<Paper shadow="sm" p="md">
					<Group justify="center">
						<Text>Loading analytics...</Text>
					</Group>
				</Paper>
			</Container>
		);
	}

	return (
		<Container size="xl" py="md">
			<Stack gap="md">
				<Paper shadow="sm" p="md">
					<Group justify="space-between" mb="md">
						<Text size="lg" fw={600}>Log Analytics</Text>
						<Group>
							<Select
								data={timeRangeOptions}
								value={timeRange}
								onChange={value => setTimeRange(value || '24h')}
								w={150}
							/>
							<Button
								variant="light"
								onClick={fetchAnalytics}
								loading={loading}
								leftSection={<IconRefresh size={16} />}
							>
								Refresh
							</Button>
						</Group>
					</Group>

					<Grid>
						<Grid.Col span={3}>
							<Paper p="md" withBorder>
								<Group>
									<IconActivity size={24} color="blue" />
									<Stack gap={0}>
										<Text size="xl" fw={700}>
											{analytics.totalLogs.toLocaleString()}
										</Text>
										<Text size="sm" c="dimmed">Total Logs</Text>
									</Stack>
								</Group>
							</Paper>
						</Grid.Col>

						<Grid.Col span={3}>
							<Paper p="md" withBorder>
								<Group>
									<IconAlertTriangle
										size={24}
										color={analytics.errorRate > 10 ? 'red' : 'green'}
									/>
									<Stack gap={0}>
										<Text size="xl" fw={700}>
											{analytics.errorRate.toFixed(1)}%
										</Text>
										<Text size="sm" c="dimmed">Error Rate</Text>
									</Stack>
								</Group>
							</Paper>
						</Grid.Col>

						<Grid.Col span={3}>
							<Paper p="md" withBorder>
								<Group>
									<IconClock size={24} color="orange" />
									<Stack gap={0}>
										<Text size="xl" fw={700}>
											{analytics.averageResponseTime}ms
										</Text>
										<Text size="sm" c="dimmed">Avg Response</Text>
									</Stack>
								</Group>
							</Paper>
						</Grid.Col>

						<Grid.Col span={3}>
							<Paper p="md" withBorder>
								<Group>
									<IconBug size={24} color="red" />
									<Stack gap={0}>
										<Text size="xl" fw={700}>
											{analytics.topErrors.length}
										</Text>
										<Text size="sm" c="dimmed">Error Types</Text>
									</Stack>
								</Group>
							</Paper>
						</Grid.Col>
					</Grid>
				</Paper>

				<Grid>
					<Grid.Col span={8}>
						<Paper shadow="sm" p="md">
							<Text size="md" fw={600} mb="md">Log Trends Over Time</Text>
							<ResponsiveContainer width="100%" height={300}>
								<AreaChart data={formatTrendData(analytics.trendsOverTime)}>
									<CartesianGrid strokeDasharray="3 3" />
									<XAxis dataKey="timestamp" />
									<YAxis />
									<RechartsTooltip />
									<Legend />
									<Area
										type="monotone"
										dataKey="count"
										stackId="1"
										stroke="#8884d8"
										fill="#8884d8"
										name="Total Logs"
									/>
									<Area
										type="monotone"
										dataKey="errorCount"
										stackId="2"
										stroke="#ff7300"
										fill="#ff7300"
										name="Errors"
									/>
								</AreaChart>
							</ResponsiveContainer>
						</Paper>
					</Grid.Col>

					<Grid.Col span={4}>
						<Paper shadow="sm" p="md">
							<Text size="md" fw={600} mb="md">Service Distribution</Text>
							<ResponsiveContainer width="100%" height={300}>
								<PieChart>
									<Pie
										data={formatServiceData(analytics.serviceDistribution)}
										cx="50%"
										cy="50%"
										labelLine={false}
										label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
										outerRadius={80}
										fill="#8884d8"
										dataKey="value"
									>
										{formatServiceData(analytics.serviceDistribution).map((entry, index) => (
											<Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
										))}
									</Pie>
									<RechartsTooltip />
								</PieChart>
							</ResponsiveContainer>
						</Paper>
					</Grid.Col>
				</Grid>

				<Grid>
					<Grid.Col span={6}>
						<Paper shadow="sm" p="md">
							<Text size="md" fw={600} mb="md">Log Level Distribution</Text>
							<ResponsiveContainer width="100%" height={250}>
								<BarChart data={formatLevelData(analytics.levelDistribution)}>
									<CartesianGrid strokeDasharray="3 3" />
									<XAxis dataKey="name" />
									<YAxis />
									<RechartsTooltip />
									<Bar dataKey="value" fill="#8884d8" />
								</BarChart>
							</ResponsiveContainer>
						</Paper>
					</Grid.Col>

					<Grid.Col span={6}>
						<Paper shadow="sm" p="md">
							<Text size="md" fw={600} mb="md">Top Errors</Text>
							<ScrollArea h={250}>
								<Stack gap="xs">
									{analytics.topErrors.map((error, index) => (
										<Paper key={index} p="sm" withBorder>
											<Group justify="space-between">
												<Stack gap={2}>
													<Text size="sm" fw={500}>{error.type}</Text>
													<Text size="xs" c="dimmed">
														{error.count} occurrences ({error.percentage.toFixed(1)}%)
													</Text>
												</Stack>
												<Badge color="red" variant="light">
													{error.count}
												</Badge>
											</Group>
										</Paper>
									))}
								</Stack>
							</ScrollArea>
						</Paper>
					</Grid.Col>
				</Grid>

				<Paper shadow="sm" p="md">
					<Group justify="space-between" mb="md">
						<Text size="md" fw={600}>Slowest Requests</Text>
						<Text size="sm" c="dimmed">
							Requests taking longer than average response time
						</Text>
					</Group>

					<ScrollArea>
						<Table striped highlightOnHover>
							<Table.Thead>
								<Table.Tr>
									<Table.Th>Correlation ID</Table.Th>
									<Table.Th>Service</Table.Th>
									<Table.Th>Endpoint</Table.Th>
									<Table.Th>Duration</Table.Th>
									<Table.Th>Actions</Table.Th>
								</Table.Tr>
							</Table.Thead>
							<Table.Tbody>
								{analytics.slowestRequests.map((request, index) => (
									<Table.Tr key={index}>
										<Table.Td>
											<Text size="sm" ff="monospace">
												{request.correlationId.slice(0, 8)}...
											</Text>
										</Table.Td>
										<Table.Td>
											<Badge variant="outline">
												{request.service}
											</Badge>
										</Table.Td>
										<Table.Td>
											<Text size="sm">{request.endpoint}</Text>
										</Table.Td>
										<Table.Td>
											<Badge
												color={request.duration > 5000 ? 'red' : 'orange'}
												variant="light"
											>
												{request.duration}ms
											</Badge>
										</Table.Td>
										<Table.Td>
											<Tooltip label="View correlation trace">
												<ActionIcon size="sm" variant="subtle">
													<IconTrendingUp size={14} />
												</ActionIcon>
											</Tooltip>
										</Table.Td>
									</Table.Tr>
								))}
							</Table.Tbody>
						</Table>
					</ScrollArea>
				</Paper>

				<Paper shadow="sm" p="md">
					<Text size="md" fw={600} mb="md">Error Patterns</Text>

					{errorPatterns.length === 0 ? (
						<Alert color="green">
							No error patterns detected in the selected time range.
						</Alert>
					) : (
						<ScrollArea>
							<Table striped highlightOnHover>
								<Table.Thead>
									<Table.Tr>
										<Table.Th>Pattern</Table.Th>
										<Table.Th>Severity</Table.Th>
										<Table.Th>Occurrences</Table.Th>
										<Table.Th>Services</Table.Th>
										<Table.Th>Last Seen</Table.Th>
										<Table.Th>Status</Table.Th>
									</Table.Tr>
								</Table.Thead>
								<Table.Tbody>
									{errorPatterns.map(pattern => (
										<Table.Tr key={pattern.id}>
											<Table.Td>
												<Stack gap={2}>
													<Text size="sm" fw={500}>{pattern.description}</Text>
													<Text size="xs" c="dimmed" ff="monospace">
														{pattern.pattern}
													</Text>
												</Stack>
											</Table.Td>
											<Table.Td>
												<Badge
													color={getSeverityColor(pattern.severity)}
													variant="light"
												>
													{pattern.severity.toUpperCase()}
												</Badge>
											</Table.Td>
											<Table.Td>
												<Text fw={500}>{pattern.occurrences}</Text>
											</Table.Td>
											<Table.Td>
												<Group gap="xs">
													{pattern.services.map(service => (
														<Badge key={service} size="xs" variant="outline">
															{service}
														</Badge>
													))}
												</Group>
											</Table.Td>
											<Table.Td>
												<Text size="sm" c="dimmed">
													{new Date(pattern.lastSeen).toLocaleString()}
												</Text>
											</Table.Td>
											<Table.Td>
												<Badge
													color={pattern.isResolved ? 'green' : 'red'}
													variant="light"
												>
													{pattern.isResolved ? 'Resolved' : 'Active'}
												</Badge>
											</Table.Td>
										</Table.Tr>
									))}
								</Table.Tbody>
							</Table>
						</ScrollArea>
					)}
				</Paper>
			</Stack>
		</Container>
	);
};

export default LogAnalytics;
