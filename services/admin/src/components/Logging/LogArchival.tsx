'use client';

import {
	Container,
	Paper,
	Group,
	Text,
	Badge,
	Button,
	Table,
	ScrollArea,
	Modal,
	Stack,
	TextInput,
	NumberInput,
	Select,
	MultiSelect,
	Switch,
	ActionIcon,
	Tooltip,
	Progress,
	Alert,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { clientLogger } from '@/lib/logger';
import { notifications } from '@mantine/notifications';
import {
	IconPlus,
	IconEdit,
	IconTrash,
	IconPlayerPlay,
	IconPlayerPause,
	IconArchive,
	IconDownload,
	IconCalendar,
	IconDatabase,
	IconCheck,
	IconX,
} from '@tabler/icons-react';
import { useState, useEffect } from 'react';
import type { FC } from 'react';
import type { LogArchivePolicyType, ServiceNameType, LogLevelType } from '../../types/logs';

const LogArchival: FC = () =>
{
	const [policies, setPolicies] = useState<LogArchivePolicyType[]>([]);
	const [selectedPolicy, setSelectedPolicy] = useState<LogArchivePolicyType | null>(null);
	const [loading, setLoading] = useState(false);
	const [modalOpened, { open: openModal, close: closeModal }] = useDisclosure(false);
	const [isEditing, setIsEditing] = useState(false);

	const fetchPolicies = async () =>
	{
		try
		{
			setLoading(true);
			const response = await fetch('/api/logs/archival');
			if (response.ok)
			{
				const data = await response.json();
				setPolicies(data);
			}
		}
		catch (error)
		{
			clientLogger.error('Error fetching archival policies:', error);
			notifications.show({
				title: 'Error',
				message: 'Failed to fetch archival policies',
				color: 'red',
			});
		}
		finally
		{
			setLoading(false);
		}
	};

	useEffect(() =>
	{
		fetchPolicies();
	}, []);

	const handleCreatePolicy = () =>
	{
		setSelectedPolicy(null);
		setIsEditing(false);
		openModal();
	};

	const handleEditPolicy = (policy: LogArchivePolicyType) =>
	{
		setSelectedPolicy(policy);
		setIsEditing(true);
		openModal();
	};

	const handleDeletePolicy = async (policyId: string) =>
	{
		try
		{
			const response = await fetch(`/api/logs/archival/${policyId}`, {
				method: 'DELETE',
			});

			if (response.ok)
			{
				notifications.show({
					title: 'Success',
					message: 'Archival policy deleted successfully',
					color: 'green',
				});
				fetchPolicies();
			}
		}
		catch (error)
		{
			clientLogger.error('Error deleting policy:', error);
			notifications.show({
				title: 'Error',
				message: 'Failed to delete archival policy',
				color: 'red',
			});
		}
	};

	const handleTogglePolicy = async (policyId: string, isActive: boolean) =>
	{
		try
		{
			const response = await fetch(`/api/logs/archival/${policyId}/toggle`, {
				method: 'PUT',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ isActive }),
			});

			if (response.ok)
			{
				notifications.show({
					title: 'Success',
					message: `Policy ${isActive ? 'activated' : 'deactivated'} successfully`,
					color: 'green',
				});
				fetchPolicies();
			}
		}
		catch (error)
		{
			clientLogger.error('Error toggling policy:', error);
			notifications.show({
				title: 'Error',
				message: 'Failed to toggle policy',
				color: 'red',
			});
		}
	};

	const handleRunPolicy = async (policyId: string) =>
	{
		try
		{
			const response = await fetch(`/api/logs/archival/${policyId}/run`, {
				method: 'POST',
			});

			if (response.ok)
			{
				notifications.show({
					title: 'Success',
					message: 'Archival policy started successfully',
					color: 'green',
				});
				fetchPolicies();
			}
		}
		catch (error)
		{
			clientLogger.error('Error running policy:', error);
			notifications.show({
				title: 'Error',
				message: 'Failed to run archival policy',
				color: 'red',
			});
		}
	};

	const handleSavePolicy = async (policyData: Partial<LogArchivePolicyType>) =>
	{
		try
		{
			const url = isEditing ? `/api/logs/archival/${selectedPolicy?.id}` : '/api/logs/archival';
			const method = isEditing ? 'PUT' : 'POST';

			const response = await fetch(url, {
				method,
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify(policyData),
			});

			if (response.ok)
			{
				notifications.show({
					title: 'Success',
					message: `Policy ${isEditing ? 'updated' : 'created'} successfully`,
					color: 'green',
				});
				fetchPolicies();
				closeModal();
			}
		}
		catch (error)
		{
			clientLogger.error('Error saving policy:', error);
			notifications.show({
				title: 'Error',
				message: 'Failed to save policy',
				color: 'red',
			});
		}
	};

	const getStatusColor = (policy: LogArchivePolicyType) =>
	{
		if (!policy.isActive) return 'gray';
		if (policy.lastRun && new Date(policy.lastRun) > new Date(Date.now() - 86400000))
		{
			return 'green'; // Ran in last 24 hours
		}
		return 'orange'; // Active but hasn't run recently
	};

	const getStatusLabel = (policy: LogArchivePolicyType) =>
	{
		if (!policy.isActive) return 'Inactive';
		if (policy.lastRun && new Date(policy.lastRun) > new Date(Date.now() - 86400000))
		{
			return 'Active';
		}
		return 'Pending';
	};

	return (
		<Container size="xl" py="md">
			<Paper shadow="sm" p="md">
				<Group justify="space-between" mb="md">
					<Group>
						<Text size="lg" fw={600}>Log Archival Policies</Text>
						<Badge color="blue" variant="light">
							{policies.length} policies
						</Badge>
						<Badge color="green" variant="light">
							{policies.filter(p => p.isActive).length} active
						</Badge>
					</Group>

					<Button
						onClick={handleCreatePolicy}
						leftSection={<IconPlus size={16} />}
					>
						Create Policy
					</Button>
				</Group>

				<Alert color="blue" icon={<IconArchive size={16} />} mb="md">
					<Text size="sm">
						Archival policies automatically compress and store old logs based on retention rules.
						Configure policies to manage disk space and comply with data retention requirements.
					</Text>
				</Alert>

				<ScrollArea>
					<Table striped highlightOnHover>
						<Table.Thead>
							<Table.Tr>
								<Table.Th>Name</Table.Th>
								<Table.Th>Status</Table.Th>
								<Table.Th>Retention</Table.Th>
								<Table.Th>Services</Table.Th>
								<Table.Th>Compression</Table.Th>
								<Table.Th>Last Run</Table.Th>
								<Table.Th>Next Run</Table.Th>
								<Table.Th>Actions</Table.Th>
							</Table.Tr>
						</Table.Thead>
						<Table.Tbody>
							{policies.map(policy => (
								<Table.Tr key={policy.id}>
									<Table.Td>
										<Stack gap={2}>
											<Text size="sm" fw={500}>{policy.name}</Text>
											<Text size="xs" c="dimmed">{policy.archiveLocation}</Text>
										</Stack>
									</Table.Td>
									<Table.Td>
										<Badge
											color={getStatusColor(policy)}
											variant="light"
											leftSection={policy.isActive ? <IconPlayerPlay size={12} /> : <IconPlayerPause size={12} />}
										>
											{getStatusLabel(policy)}
										</Badge>
									</Table.Td>
									<Table.Td>
										<Group gap="xs">
											<Text size="sm">{policy.retentionDays} days</Text>
											<Badge size="xs" variant="outline">
												{policy.levels.length} levels
											</Badge>
										</Group>
									</Table.Td>
									<Table.Td>
										<Group gap="xs">
											{policy.services.slice(0, 2).map(service => (
												<Badge key={service} size="xs" variant="outline">
													{service}
												</Badge>
											))}
											{policy.services.length > 2 && (
												<Badge size="xs" variant="light">
													+{policy.services.length - 2}
												</Badge>
											)}
										</Group>
									</Table.Td>
									<Table.Td>
										<Badge
											color={policy.compressionEnabled ? 'green' : 'gray'}
											variant="light"
											size="sm"
										>
											{policy.compressionEnabled ? 'Enabled' : 'Disabled'}
										</Badge>
									</Table.Td>
									<Table.Td>
										{policy.lastRun ? (
											<Text size="sm" c="dimmed">
												{new Date(policy.lastRun).toLocaleString()}
											</Text>
										) : (
											<Text size="sm" c="dimmed">Never</Text>
										)}
									</Table.Td>
									<Table.Td>
										<Text size="sm" c="dimmed">
											{new Date(policy.nextRun).toLocaleString()}
										</Text>
									</Table.Td>
									<Table.Td>
										<Group gap="xs">
											<Tooltip label={policy.isActive ? 'Deactivate policy' : 'Activate policy'}>
												<ActionIcon
													size="sm"
													variant="light"
													color={policy.isActive ? 'orange' : 'green'}
													onClick={() => handleTogglePolicy(policy.id, !policy.isActive)}
												>
													{policy.isActive ? <IconPlayerPause size={14} /> : <IconPlayerPlay size={14} />}
												</ActionIcon>
											</Tooltip>

											<Tooltip label="Run now">
												<ActionIcon
													size="sm"
													variant="light"
													color="blue"
													onClick={() => handleRunPolicy(policy.id)}
													disabled={!policy.isActive}
												>
													<IconArchive size={14} />
												</ActionIcon>
											</Tooltip>

											<Tooltip label="Edit policy">
												<ActionIcon
													size="sm"
													variant="light"
													onClick={() => handleEditPolicy(policy)}
												>
													<IconEdit size={14} />
												</ActionIcon>
											</Tooltip>

											<Tooltip label="Delete policy">
												<ActionIcon
													size="sm"
													variant="light"
													color="red"
													onClick={() => handleDeletePolicy(policy.id)}
												>
													<IconTrash size={14} />
												</ActionIcon>
											</Tooltip>
										</Group>
									</Table.Td>
								</Table.Tr>
							))}
						</Table.Tbody>
					</Table>
				</ScrollArea>
			</Paper>

			<Modal
				opened={modalOpened}
				onClose={closeModal}
				title={isEditing ? 'Edit Archival Policy' : 'Create Archival Policy'}
				size="lg"
			>
				<LogArchivalPolicyForm
					policy={selectedPolicy}
					onSave={handleSavePolicy}
					onClose={closeModal}
				/>
			</Modal>
		</Container>
	);
};

type LogArchivalPolicyFormProps = {
	policy: LogArchivePolicyType | null;
	onSave: (policyData: Partial<LogArchivePolicyType>) => void;
	onClose: () => void;
};

const LogArchivalPolicyForm: FC<LogArchivalPolicyFormProps> = ({ policy, onSave, onClose }) =>
{
	const [formData, setFormData] = useState({
		name: policy?.name || '',
		retentionDays: policy?.retentionDays || 30,
		compressionEnabled: policy?.compressionEnabled ?? true,
		services: policy?.services || [],
		levels: policy?.levels || [],
		archiveLocation: policy?.archiveLocation || '/var/log/archives',
		isActive: policy?.isActive ?? true,
	});

	const serviceOptions = [
		{ value: 'web-app', label: 'Web App' },
		{ value: 'crawler', label: 'Crawler' },
		{ value: 'ranking-engine', label: 'Ranking Engine' },
		{ value: 'scheduler', label: 'Scheduler' },
		{ value: 'domain-seeder', label: 'Domain Seeder' },
		{ value: 'admin', label: 'Admin Panel' },
	];

	const levelOptions = [
		{ value: 'debug', label: 'Debug' },
		{ value: 'info', label: 'Info' },
		{ value: 'warn', label: 'Warning' },
		{ value: 'error', label: 'Error' },
		{ value: 'fatal', label: 'Fatal' },
	];

	const handleSubmit = () =>
	{
		if (!formData.name.trim())
		{
			notifications.show({
				title: 'Validation Error',
				message: 'Policy name is required',
				color: 'red',
			});
			return;
		}

		if (formData.services.length === 0)
		{
			notifications.show({
				title: 'Validation Error',
				message: 'At least one service must be selected',
				color: 'red',
			});
			return;
		}

		if (formData.levels.length === 0)
		{
			notifications.show({
				title: 'Validation Error',
				message: 'At least one log level must be selected',
				color: 'red',
			});
			return;
		}

		if (formData.retentionDays < 1)
		{
			notifications.show({
				title: 'Validation Error',
				message: 'Retention period must be at least 1 day',
				color: 'red',
			});
			return;
		}

		onSave(formData);
	};

	return (
		<Stack gap="md">
			<TextInput
				label="Policy Name"
				placeholder="Enter policy name"
				value={formData.name}
				onChange={event => setFormData(prev => ({
					...prev,
					name: event.currentTarget.value,
				}))}
				required
			/>

			<NumberInput
				label="Retention Period (days)"
				description="How long to keep logs before archiving"
				value={formData.retentionDays}
				onChange={value => setFormData(prev => ({
					...prev,
					retentionDays: Number(value) || 1,
				}))}
				min={1}
				max={3650}
				required
			/>

			<TextInput
				label="Archive Location"
				placeholder="Enter archive storage path"
				value={formData.archiveLocation}
				onChange={event => setFormData(prev => ({
					...prev,
					archiveLocation: event.currentTarget.value,
				}))}
				required
			/>

			<MultiSelect
				label="Services"
				placeholder="Select services to archive"
				data={serviceOptions}
				value={formData.services}
				onChange={value => setFormData(prev => ({
					...prev,
					services: value as ServiceNameType[],
				}))}
				required
			/>

			<MultiSelect
				label="Log Levels"
				placeholder="Select log levels to archive"
				data={levelOptions}
				value={formData.levels}
				onChange={value => setFormData(prev => ({
					...prev,
					levels: value as LogLevelType[],
				}))}
				required
			/>

			<Switch
				label="Enable Compression"
				description="Compress archived logs to save storage space"
				checked={formData.compressionEnabled}
				onChange={event => setFormData(prev => ({
					...prev,
					compressionEnabled: event.currentTarget.checked,
				}))}
			/>

			<Switch
				label="Active Policy"
				description="Enable this policy to run automatically"
				checked={formData.isActive}
				onChange={event => setFormData(prev => ({
					...prev,
					isActive: event.currentTarget.checked,
				}))}
			/>

			<Alert color="blue" icon={<IconDatabase size={16} />}>
				<Text size="sm">
					<strong>Estimated storage impact:</strong> Archiving logs older than {formData.retentionDays} days
					{formData.compressionEnabled && ' with compression'} can reduce storage usage by 60-80%.
				</Text>
			</Alert>

			<Group justify="flex-end">
				<Button variant="subtle" onClick={onClose}>
					Cancel
				</Button>
				<Button onClick={handleSubmit} leftSection={<IconCheck size={16} />}>
					{policy ? 'Update Policy' : 'Create Policy'}
				</Button>
			</Group>
		</Stack>
	);
};

export default LogArchival;
