'use client';

import {
	Container,
	Paper,
	Group,
	Text,
	Badge,
	Table,
	Select,
	Button,
	Modal,
	Stack,

	Alert,
	ActionIcon,
	Tooltip,
	ScrollArea,
	TextInput,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { clientLogger } from '@/lib/logger';
import { notifications } from '@mantine/notifications';
import {
	IconSettings,
	IconClock,
	IconRefresh,
	IconAlertTriangle,
	IconCheck,
	IconX,
} from '@tabler/icons-react';
import { useState, useEffect } from 'react';
import type { FC, ChangeEvent } from 'react';
import type { LogLevelConfigType, ServiceNameType, LogLevelType } from '../../types/logs';

const LogLevelManager: FC = () =>
{
	const [configs, setConfigs] = useState<LogLevelConfigType[]>([]);
	const [selectedConfig, setSelectedConfig] = useState<LogLevelConfigType | null>(null);
	const [loading, setLoading] = useState(false);
	const [modalOpened, { open: openModal, close: closeModal }] = useDisclosure(false);

	const levelOptions = [
		{ value: 'debug', label: 'Debug', color: 'gray' },
		{ value: 'info', label: 'Info', color: 'blue' },
		{ value: 'warn', label: 'Warning', color: 'orange' },
		{ value: 'error', label: 'Error', color: 'red' },
		{ value: 'fatal', label: 'Fatal', color: 'red' },
	];

	const fetchConfigs = async () =>
	{
		try
		{
			setLoading(true);
			const response = await fetch('/api/logs/levels');
			if (response.ok)
			{
				const data = await response.json();
				setConfigs(data);
			}
		}
		catch (error)
		{
			clientLogger.error('Error fetching log level configs:', error);
			notifications.show({
				title: 'Error',
				message: 'Failed to fetch log level configurations',
				color: 'red',
			});
		}
		finally
		{
			setLoading(false);
		}
	};

	useEffect(() =>
	{
		fetchConfigs();
	}, []);

	const handleUpdateLevel = async (
		service: ServiceNameType,
		level: LogLevelType,
		temporary?: boolean,
		temporaryUntil?: Date
	) =>
	{
		try
		{
			const response = await fetch('/api/logs/levels', {
				method: 'PUT',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					service,
					level,
					temporary,
					temporaryUntil,
				}),
			});

			if (response.ok)
			{
				notifications.show({
					title: 'Success',
					message: `Log level updated for ${service}`,
					color: 'green',
				});
				fetchConfigs();
				closeModal();
			}
			else
			{
				throw new Error('Failed to update log level');
			}
		}
		catch (error)
		{
			clientLogger.error('Error updating log level:', error);
			notifications.show({
				title: 'Error',
				message: 'Failed to update log level',
				color: 'red',
			});
		}
	};

	const handleResetTemporary = async (service: ServiceNameType) =>
	{
		try
		{
			const response = await fetch(`/api/logs/levels/${service}/reset`, {
				method: 'POST',
			});

			if (response.ok)
			{
				notifications.show({
					title: 'Success',
					message: `Temporary log level reset for ${service}`,
					color: 'green',
				});
				fetchConfigs();
			}
		}
		catch (error)
		{
			clientLogger.error('Error resetting temporary log level:', error);
			notifications.show({
				title: 'Error',
				message: 'Failed to reset temporary log level',
				color: 'red',
			});
		}
	};

	const getLevelColor = (level: LogLevelType) =>
	{
		const levelOption = levelOptions.find(opt => opt.value === level);
		return levelOption?.color || 'gray';
	};

	const isTemporaryExpired = (config: LogLevelConfigType) =>
		config.temporaryUntil && new Date(config.temporaryUntil) < new Date();

	const getEffectiveLevel = (config: LogLevelConfigType) =>
	{
		if (config.temporaryLevel && !isTemporaryExpired(config))
		{
			return config.temporaryLevel;
		}
		return config.currentLevel;
	};

	return (
		<Container size="xl" py="md">
			<Paper shadow="sm" p="md">
				<Group justify="space-between" mb="md">
					<Group>
						<Text size="lg" fw={600}>Log Level Management</Text>
						<Badge color="blue" variant="light">
							{configs.length} services
						</Badge>
					</Group>

					<Button
						variant="light"
						onClick={fetchConfigs}
						loading={loading}
						leftSection={<IconRefresh size={16} />}
					>
						Refresh
					</Button>
				</Group>

				<Alert color="blue" icon={<IconAlertTriangle size={16} />} mb="md">
					<Text size="sm">
						Changing log levels affects system performance and disk usage.
						Debug level generates significantly more logs than other levels.
					</Text>
				</Alert>

				<ScrollArea>
					<Table striped highlightOnHover>
						<Table.Thead>
							<Table.Tr>
								<Table.Th>Service</Table.Th>
								<Table.Th>Current Level</Table.Th>
								<Table.Th>Effective Level</Table.Th>
								<Table.Th>Temporary Until</Table.Th>
								<Table.Th>Last Modified</Table.Th>
								<Table.Th>Modified By</Table.Th>
								<Table.Th>Actions</Table.Th>
							</Table.Tr>
						</Table.Thead>
						<Table.Tbody>
							{configs.map((config) =>
							{
								const effectiveLevel = getEffectiveLevel(config);
								const hasTemporary = config.temporaryLevel && !isTemporaryExpired(config);

								return (
									<Table.Tr key={config.service}>
										<Table.Td>
											<Badge variant="outline">
												{config.service}
											</Badge>
										</Table.Td>
										<Table.Td>
											<Badge
												color={getLevelColor(config.currentLevel)}
												variant="light"
											>
												{config.currentLevel.toUpperCase()}
											</Badge>
										</Table.Td>
										<Table.Td>
											<Group gap="xs">
												<Badge
													color={getLevelColor(effectiveLevel)}
													variant={hasTemporary ? 'filled' : 'light'}
												>
													{effectiveLevel.toUpperCase()}
												</Badge>
												{hasTemporary && (
													<Tooltip label="Temporary override active">
														<IconClock size={14} color="orange" />
													</Tooltip>
												)}
											</Group>
										</Table.Td>
										<Table.Td>
											{config.temporaryUntil ? (
												<Text
													size="sm"
													c={isTemporaryExpired(config) ? 'red' : 'dimmed'}
												>
													{new Date(config.temporaryUntil).toLocaleString()}
													{isTemporaryExpired(config) && ' (Expired)'}
												</Text>
											) : (
												<Text size="sm" c="dimmed">-</Text>
											)}
										</Table.Td>
										<Table.Td>
											<Text size="sm" c="dimmed">
												{new Date(config.lastModified).toLocaleString()}
											</Text>
										</Table.Td>
										<Table.Td>
											<Text size="sm" c="dimmed">
												{config.modifiedBy}
											</Text>
										</Table.Td>
										<Table.Td>
											<Group gap="xs">
												<Tooltip label="Change log level">
													<ActionIcon
														size="sm"
														variant="light"
														onClick={() =>
														{
															setSelectedConfig(config);
															openModal();
														}}
													>
														<IconSettings size={14} />
													</ActionIcon>
												</Tooltip>

												{hasTemporary && (
													<Tooltip label="Reset temporary level">
														<ActionIcon
															size="sm"
															variant="light"
															color="orange"
															onClick={() => handleResetTemporary(config.service)}
														>
															<IconX size={14} />
														</ActionIcon>
													</Tooltip>
												)}
											</Group>
										</Table.Td>
									</Table.Tr>
								);
							})}
						</Table.Tbody>
					</Table>
				</ScrollArea>
			</Paper>

			<Modal
				opened={modalOpened}
				onClose={closeModal}
				title={`Update Log Level - ${selectedConfig?.service}`}
				size="md"
			>
				{selectedConfig && (
					<LogLevelUpdateForm
						config={selectedConfig}
						onUpdate={handleUpdateLevel}
						onClose={closeModal}
					/>
				)}
			</Modal>
		</Container>
	);
};

type LogLevelUpdateFormProps = {
	config: LogLevelConfigType;
	onUpdate: (
		service: ServiceNameType,
		level: LogLevelType,
		temporary?: boolean,
		temporaryUntil?: Date
	) => void;
	onClose: () => void;
};

const LogLevelUpdateForm: FC<LogLevelUpdateFormProps> = ({
	config,
	onUpdate,
	onClose,
}) =>
{
	const [newLevel, setNewLevel] = useState<LogLevelType>(config.currentLevel);
	const [isTemporary, setIsTemporary] = useState(false);
	const [temporaryUntil, setTemporaryUntil] = useState<Date | null>(null);

	const levelOptions = [
		{ value: 'debug', label: 'Debug' },
		{ value: 'info', label: 'Info' },
		{ value: 'warn', label: 'Warning' },
		{ value: 'error', label: 'Error' },
		{ value: 'fatal', label: 'Fatal' },
	];

	const handleSubmit = () =>
	{
		onUpdate(
			config.service,
			newLevel,
			isTemporary,
			isTemporary ? temporaryUntil || undefined : undefined
		);
	};

	return (
		<Stack gap="md">
			<Select
				label="New Log Level"
				data={levelOptions}
				value={newLevel}
				onChange={value => setNewLevel(value as LogLevelType)}
			/>

			<Group>
				<Button
					variant={isTemporary ? 'filled' : 'light'}
					onClick={() => setIsTemporary(!isTemporary)}
					leftSection={<IconClock size={16} />}
				>
					Temporary Change
				</Button>
			</Group>

			{isTemporary && (
				<TextInput
					label="Revert At"
					description="When to automatically revert to the permanent level (YYYY-MM-DD HH:MM)"
					placeholder="YYYY-MM-DD HH:MM"
					value={temporaryUntil ? temporaryUntil.toISOString().slice(0, 16) : ''}
					onChange={(event: ChangeEvent<HTMLInputElement>) =>
					{
						const value = event.currentTarget.value;
						setTemporaryUntil(value ? new Date(value) : null);
					}}
					required
				/>
			)}

			<Alert color="blue" icon={<IconAlertTriangle size={16} />}>
				<Text size="sm">
					{isTemporary ? (
						<>
							This will temporarily change the log level to <strong>{newLevel}</strong>
							{temporaryUntil && (
								<> until {temporaryUntil.toLocaleString()}</>
							)}.
							The level will automatically revert to <strong>{config.currentLevel}</strong> afterwards.
						</>
					) : (
						<>
							This will permanently change the log level from <strong>{config.currentLevel}</strong>
							to <strong>{newLevel}</strong> for the {config.service} service.
						</>
					)}
				</Text>
			</Alert>

			<Group justify="flex-end">
				<Button variant="subtle" onClick={onClose}>
					Cancel
				</Button>
				<Button
					onClick={handleSubmit}
					disabled={isTemporary && !temporaryUntil}
					leftSection={<IconCheck size={16} />}
				>
					Update Level
				</Button>
			</Group>
		</Stack>
	);
};

export default LogLevelManager;
