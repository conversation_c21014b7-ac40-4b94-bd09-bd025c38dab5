'use client';

import {
	Stack,
	Group,
	Text,
	Badge,
	Code,
	Paper,
	Divider,
	Button,
	Tabs,
	ScrollArea,
	Table,
	Alert,
	ActionIcon,
	Tooltip,
	CopyButton,
	JsonInput,
} from '@mantine/core';
import {
	IconCopy,
	IconExternalLink,
	IconBug,
	IconClock,
	IconUser,
	IconActivity,
	IconDatabase,
	IconCheck,
} from '@tabler/icons-react';
import { useState, useEffect } from 'react';
import { clientLogger } from '@/lib/logger';
import type { FC } from 'react';
import type { LogEntryType, CorrelationTraceType } from '../../types/logs';

type LogDetailsModalProps = {
	log: LogEntryType;
	onClose: () => void;
};

const LogDetailsModal: FC<LogDetailsModalProps> = ({ log, onClose }) =>
{
	const [correlationTrace, setCorrelationTrace] = useState<CorrelationTraceType | null>(null);
	const [loadingTrace, setLoadingTrace] = useState(false);

	useEffect(() =>
	{
		if (log.correlationId)
		{
			fetchCorrelationTrace();
		}
	}, [log.correlationId]);

	const fetchCorrelationTrace = async () =>
	{
		if (!log.correlationId) return;

		try
		{
			setLoadingTrace(true);
			const response = await fetch(`/api/logs/correlation/${log.correlationId}`);
			if (response.ok)
			{
				const trace = await response.json();
				setCorrelationTrace(trace);
			}
		}
		catch (error)
		{
			clientLogger.error('Error fetching correlation trace:', error);
		}
		finally
		{
			setLoadingTrace(false);
		}
	};

	const getLevelColor = (level: string) =>
	{
		switch (level)
		{
			case 'error':
			case 'fatal':
				return 'red';
			case 'warn':
				return 'orange';
			case 'info':
				return 'blue';
			case 'debug':
				return 'gray';
			default:
				return 'gray';
		}
	};

	const getServiceColor = (service: string) =>
	{
		const colors = {
			'web-app': 'blue',
			'crawler': 'green',
			'ranking-engine': 'purple',
			'scheduler': 'orange',
			'domain-seeder': 'teal',
			'admin': 'indigo',
		};
		return colors[service as keyof typeof colors] || 'gray';
	};

	const formatMetadata = (metadata: Record<string, any>) =>
		JSON.stringify(metadata, null, 2);

	return (
		<Stack gap="md">
			<Group justify="space-between">
				<Group>
					<Badge color={getLevelColor(log.level)} variant="light">
						{log.level.toUpperCase()}
					</Badge>
					<Badge color={getServiceColor(log.service)} variant="outline">
						{log.service}
					</Badge>
					{log.errorType && (
						<Badge color="red" variant="light">
							{log.errorType}
						</Badge>
					)}
				</Group>

				<Group>
					<CopyButton value={JSON.stringify(log, null, 2)}>
						{({ copied, copy }) => (
							<Tooltip label={copied ? 'Copied!' : 'Copy log as JSON'}>
								<ActionIcon variant="light" onClick={copy}>
									{copied ? <IconCheck size={16} /> : <IconCopy size={16} />}
								</ActionIcon>
							</Tooltip>
						)}
					</CopyButton>
				</Group>
			</Group>

			<Paper p="md" withBorder>
				<Stack gap="sm">
					<Group>
						<IconClock size={16} />
						<Text fw={500}>Timestamp</Text>
					</Group>
					<Text size="sm" c="dimmed">
						{new Date(log.timestamp).toLocaleString()}
					</Text>
				</Stack>
			</Paper>

			<Paper p="md" withBorder>
				<Stack gap="sm">
					<Text fw={500}>Message</Text>
					<ScrollArea h={200}>
						<Text size="sm" style={{ whiteSpace: 'pre-wrap' }}>
							{log.message}
						</Text>
					</ScrollArea>
				</Stack>
			</Paper>

			<Tabs defaultValue="context">
				<Tabs.List>
					<Tabs.Tab value="context" leftSection={<IconActivity size={16} />}>
						Context
					</Tabs.Tab>
					{log.stackTrace && (
						<Tabs.Tab value="stacktrace" leftSection={<IconBug size={16} />}>
							Stack Trace
						</Tabs.Tab>
					)}
					{log.metadata && (
						<Tabs.Tab value="metadata" leftSection={<IconDatabase size={16} />}>
							Metadata
						</Tabs.Tab>
					)}
					{log.correlationId && (
						<Tabs.Tab value="correlation" leftSection={<IconExternalLink size={16} />}>
							Correlation Trace
						</Tabs.Tab>
					)}
				</Tabs.List>

				<Tabs.Panel value="context" pt="md">
					<Stack gap="md">
						<Table>
							<Table.Tbody>
								<Table.Tr>
									<Table.Td fw={500}>Log ID</Table.Td>
									<Table.Td>
										<Code>{log.id}</Code>
									</Table.Td>
								</Table.Tr>
								<Table.Tr>
									<Table.Td fw={500}>Service</Table.Td>
									<Table.Td>
										<Badge color={getServiceColor(log.service)} variant="outline">
											{log.service}
										</Badge>
									</Table.Td>
								</Table.Tr>
								<Table.Tr>
									<Table.Td fw={500}>Level</Table.Td>
									<Table.Td>
										<Badge color={getLevelColor(log.level)} variant="light">
											{log.level.toUpperCase()}
										</Badge>
									</Table.Td>
								</Table.Tr>
								{log.correlationId && (
									<Table.Tr>
										<Table.Td fw={500}>Correlation ID</Table.Td>
										<Table.Td>
											<Code>{log.correlationId}</Code>
										</Table.Td>
									</Table.Tr>
								)}
								{log.userId && (
									<Table.Tr>
										<Table.Td fw={500}>User ID</Table.Td>
										<Table.Td>
											<Group>
												<IconUser size={16} />
												<Code>{log.userId}</Code>
											</Group>
										</Table.Td>
									</Table.Tr>
								)}
								{log.action && (
									<Table.Tr>
										<Table.Td fw={500}>Action</Table.Td>
										<Table.Td>
											<Badge variant="light">{log.action}</Badge>
										</Table.Td>
									</Table.Tr>
								)}
								{log.requestId && (
									<Table.Tr>
										<Table.Td fw={500}>Request ID</Table.Td>
										<Table.Td>
											<Code>{log.requestId}</Code>
										</Table.Td>
									</Table.Tr>
								)}
								{log.duration !== undefined && (
									<Table.Tr>
										<Table.Td fw={500}>Duration</Table.Td>
										<Table.Td>
											<Group>
												<IconClock size={16} />
												<Text>{log.duration}ms</Text>
											</Group>
										</Table.Td>
									</Table.Tr>
								)}
								{log.statusCode && (
									<Table.Tr>
										<Table.Td fw={500}>Status Code</Table.Td>
										<Table.Td>
											<Badge
												color={log.statusCode >= 400 ? 'red' : 'green'}
												variant="light"
											>
												{log.statusCode}
											</Badge>
										</Table.Td>
									</Table.Tr>
								)}
								{log.errorType && (
									<Table.Tr>
										<Table.Td fw={500}>Error Type</Table.Td>
										<Table.Td>
											<Badge color="red" variant="light">
												{log.errorType}
											</Badge>
										</Table.Td>
									</Table.Tr>
								)}
							</Table.Tbody>
						</Table>
					</Stack>
				</Tabs.Panel>

				{log.stackTrace && (
					<Tabs.Panel value="stacktrace" pt="md">
						<Paper p="md" withBorder>
							<ScrollArea h={400}>
								<Code block>
									{log.stackTrace}
								</Code>
							</ScrollArea>
						</Paper>
					</Tabs.Panel>
				)}

				{log.metadata && (
					<Tabs.Panel value="metadata" pt="md">
						<JsonInput
							value={formatMetadata(log.metadata)}
							readOnly
							autosize
							minRows={4}
							maxRows={20}
							formatOnBlur
						/>
					</Tabs.Panel>
				)}

				{log.correlationId && (
					<Tabs.Panel value="correlation" pt="md">
						{loadingTrace ? (
							<Text>Loading correlation trace...</Text>
						) : correlationTrace ? (
							<Stack gap="md">
								<Group justify="space-between">
									<Text fw={500}>Request Trace</Text>
									<Group>
										<Badge
											color={correlationTrace.status === 'completed' ? 'green'
												: correlationTrace.status === 'failed' ? 'red' : 'orange'}
											variant="light"
										>
											{correlationTrace.status}
										</Badge>
										{correlationTrace.totalDuration && (
											<Badge variant="outline">
												{correlationTrace.totalDuration}ms total
											</Badge>
										)}
									</Group>
								</Group>

								{correlationTrace.errorCount > 0 && (
									<Alert color="red" icon={<IconBug size={16} />}>
										This request had {correlationTrace.errorCount} error(s)
									</Alert>
								)}

								<Stack gap="sm">
									{correlationTrace.services.map((service, index) => (
										<Paper key={index} p="md" withBorder>
											<Group justify="space-between" mb="sm">
												<Badge color={getServiceColor(service.service)} variant="outline">
													{service.service}
												</Badge>
												<Group gap="xs">
													<Text size="xs" c="dimmed">
														{new Date(service.startTime).toLocaleTimeString()}
													</Text>
													{service.duration && (
														<Badge size="xs" variant="light">
															{service.duration}ms
														</Badge>
													)}
												</Group>
											</Group>

											<Stack gap="xs">
												{service.logs.slice(0, 3).map(serviceLog => (
													<Group key={serviceLog.id} gap="xs">
														<Badge
															size="xs"
															color={getLevelColor(serviceLog.level)}
															variant="light"
														>
															{serviceLog.level}
														</Badge>
														<Text size="xs" lineClamp={1}>
															{serviceLog.message}
														</Text>
													</Group>
												))}
												{service.logs.length > 3 && (
													<Text size="xs" c="dimmed">
														... and {service.logs.length - 3} more logs
													</Text>
												)}
											</Stack>
										</Paper>
									))}
								</Stack>
							</Stack>
						) : (
							<Text c="dimmed">No correlation trace found</Text>
						)}
					</Tabs.Panel>
				)}
			</Tabs>
		</Stack>
	);
};

export { LogDetailsModal };
