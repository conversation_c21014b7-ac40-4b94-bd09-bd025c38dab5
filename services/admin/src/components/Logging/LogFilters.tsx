'use client';

import {
	Stack,
	Group,
	Button,
	MultiSelect,

	TextInput,
	Paper,
	Text,
	Divider,
	ActionIcon,
	Tooltip,
	Badge,
} from '@mantine/core';
import { IconX, IconCalendar, IconFilter } from '@tabler/icons-react';
import { useState, useEffect } from 'react';
import type { FC } from 'react';
import type { LogFilterType, ServiceNameType, LogLevelType } from '../../types/logs';

type LogFiltersProps = {
	filter: LogFilterType;
	onChange: (filter: LogFilterType) => void;
	onApply: () => void;
};

const serviceOptions = [
	{ value: 'web-app', label: 'Web App' },
	{ value: 'crawler', label: 'Crawler' },
	{ value: 'ranking-engine', label: 'Ranking Engine' },
	{ value: 'scheduler', label: 'Scheduler' },
	{ value: 'domain-seeder', label: 'Domain Seeder' },
	{ value: 'admin', label: 'Admin Panel' },
];

const levelOptions = [
	{ value: 'debug', label: 'Debug' },
	{ value: 'info', label: 'Info' },
	{ value: 'warn', label: 'Warning' },
	{ value: 'error', label: 'Error' },
	{ value: 'fatal', label: 'Fatal' },
];

const LogFilters: FC<LogFiltersProps> = ({ filter, onChange, onApply }) =>
{
	const [localFilter, setLocalFilter] = useState<LogFilterType>(filter);

	useEffect(() =>
	{
		setLocalFilter(filter);
	}, [filter]);

	const updateFilter = (updates: Partial<LogFilterType>) =>
	{
		const newFilter = { ...localFilter, ...updates };
		setLocalFilter(newFilter);
	};

	const handleApply = () =>
	{
		onChange(localFilter);
		onApply();
	};

	const handleReset = () =>
	{
		const resetFilter: LogFilterType = {
			services: [],
			levels: [],
		};
		setLocalFilter(resetFilter);
		onChange(resetFilter);
	};

	const getActiveFiltersCount = () =>
	{
		let count = 0;
		if (localFilter.services.length > 0) count++;
		if (localFilter.levels.length > 0) count++;
		if (localFilter.startTime) count++;
		if (localFilter.endTime) count++;
		if (localFilter.correlationId) count++;
		if (localFilter.userId) count++;
		if (localFilter.action) count++;
		if (localFilter.errorType) count++;
		return count;
	};

	return (
		<Stack gap="md">
			<Group justify="space-between">
				<Group>
					<IconFilter size={16} />
					<Text fw={500}>Filter Options</Text>
					{getActiveFiltersCount() > 0 && (
						<Badge color="blue" variant="light" size="sm">
							{getActiveFiltersCount()} active
						</Badge>
					)}
				</Group>

				<Tooltip label="Reset all filters">
					<ActionIcon variant="subtle" onClick={handleReset}>
						<IconX size={16} />
					</ActionIcon>
				</Tooltip>
			</Group>

			<Paper p="md" withBorder>
				<Stack gap="md">
					<Text size="sm" fw={500} c="dimmed">Service & Level Filters</Text>

					<MultiSelect
						label="Services"
						placeholder="Select services to filter"
						data={serviceOptions}
						value={localFilter.services}
						onChange={value => updateFilter({ services: value as ServiceNameType[] })}
						clearable
						searchable
					/>

					<MultiSelect
						label="Log Levels"
						placeholder="Select log levels to filter"
						data={levelOptions}
						value={localFilter.levels}
						onChange={value => updateFilter({ levels: value as LogLevelType[] })}
						clearable
						searchable
					/>
				</Stack>
			</Paper>

			<Paper p="md" withBorder>
				<Stack gap="md">
					<Text size="sm" fw={500} c="dimmed">Time Range</Text>

					<Group grow>
						<TextInput
							label="Start Time"
							placeholder="YYYY-MM-DD HH:MM"
							value={localFilter.startTime ? localFilter.startTime.toISOString().slice(0, 16) : ''}
							onChange={(event) =>
							{
								const value = event.currentTarget.value;
								updateFilter({ startTime: value ? new Date(value) : undefined });
							}}
							leftSection={<IconCalendar size={16} />}
						/>

						<TextInput
							label="End Time"
							placeholder="YYYY-MM-DD HH:MM"
							value={localFilter.endTime ? localFilter.endTime.toISOString().slice(0, 16) : ''}
							onChange={(event) =>
							{
								const value = event.currentTarget.value;
								updateFilter({ endTime: value ? new Date(value) : undefined });
							}}
							leftSection={<IconCalendar size={16} />}
						/>
					</Group>
				</Stack>
			</Paper>

			<Paper p="md" withBorder>
				<Stack gap="md">
					<Text size="sm" fw={500} c="dimmed">Context Filters</Text>

					<Group grow>
						<TextInput
							label="Correlation ID"
							placeholder="Enter correlation ID"
							value={localFilter.correlationId || ''}
							onChange={event => updateFilter({
								correlationId: event.currentTarget.value || undefined
							})}
						/>

						<TextInput
							label="User ID"
							placeholder="Enter user ID"
							value={localFilter.userId || ''}
							onChange={event => updateFilter({
								userId: event.currentTarget.value || undefined
							})}
						/>
					</Group>

					<Group grow>
						<TextInput
							label="Action"
							placeholder="Enter action name"
							value={localFilter.action || ''}
							onChange={event => updateFilter({
								action: event.currentTarget.value || undefined
							})}
						/>

						<TextInput
							label="Error Type"
							placeholder="Enter error type"
							value={localFilter.errorType || ''}
							onChange={event => updateFilter({
								errorType: event.currentTarget.value || undefined
							})}
						/>
					</Group>
				</Stack>
			</Paper>

			<Divider />

			<Group justify="flex-end">
				<Button variant="subtle" onClick={handleReset}>
					Reset Filters
				</Button>
				<Button onClick={handleApply}>
					Apply Filters
				</Button>
			</Group>
		</Stack>
	);
};

export { LogFilters };
