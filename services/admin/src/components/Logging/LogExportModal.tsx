'use client';

import {
	<PERSON>ack,
	Group,
	Button,
	Select,
	Switch,
	NumberInput,
	Text,
	Paper,
	Progress,
	Alert,
	Divider,
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { clientLogger } from '@/lib/logger';
import { IconDownload, IconInfoCircle } from '@tabler/icons-react';
import { useState } from 'react';
import type { FC } from 'react';
import type { LogFilterType, LogExportConfigType } from '../../types/logs';

type LogExportModalProps = {
	filter: LogFilterType;
	totalLogs: number;
	onClose: () => void;
};

const LogExportModal: FC<LogExportModalProps> = ({ filter, totalLogs, onClose }) =>
{
	const [exportConfig, setExportConfig] = useState<LogExportConfigType>({
		format: 'json',
		filter,
		includeMetadata: true,
		includeStackTrace: true,
		maxRecords: Math.min(totalLogs, 10000),
	});
	const [exporting, setExporting] = useState(false);
	const [exportProgress, setExportProgress] = useState(0);

	const formatOptions = [
		{ value: 'json', label: 'JSON' },
		{ value: 'csv', label: 'CSV' },
		{ value: 'txt', label: 'Plain Text' },
	];

	const compressionOptions = [
		{ value: '', label: 'No Compression' },
		{ value: 'gzip', label: 'GZIP' },
		{ value: 'zip', label: 'ZIP' },
	];

	const updateConfig = (updates: Partial<LogExportConfigType>) =>
	{
		setExportConfig(prev => ({ ...prev, ...updates }));
	};

	const handleExport = async () =>
	{
		try
		{
			setExporting(true);
			setExportProgress(0);

			const response = await fetch('/api/logs/export', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify(exportConfig),
			});

			if (!response.ok)
			{
				throw new Error('Export failed');
			}

			// Handle streaming response for progress updates
			const reader = response.body?.getReader();
			if (!reader)
			{
				throw new Error('No response body');
			}

			let downloadUrl = '';
			const decoder = new TextDecoder();

			while (true)
			{
				const { done, value } = await reader.read();
				if (done) break;

				const chunk = decoder.decode(value);
				const lines = chunk.split('\n').filter(line => line.trim());

				for (const line of lines)
				{
					try
					{
						const data = JSON.parse(line);
						if (data.type === 'progress')
						{
							setExportProgress(data.progress);
						}
						else if (data.type === 'complete')
						{
							downloadUrl = data.downloadUrl;
						}
					}
					catch
					{
						// Ignore invalid JSON lines
					}
				}
			}

			if (downloadUrl)
			{
				// Trigger download
				const link = document.createElement('a');
				link.href = downloadUrl;
				link.download = `logs-export-${new Date().toISOString().split('T')[0]}.${exportConfig.format}${exportConfig.compression ? `.${exportConfig.compression}` : ''}`;
				document.body.appendChild(link);
				link.click();
				document.body.removeChild(link);

				notifications.show({
					title: 'Export Complete',
					message: 'Logs have been exported successfully',
					color: 'green',
				});

				onClose();
			}
		}
		catch (error)
		{
			clientLogger.error('Export error:', error);
			notifications.show({
				title: 'Export Failed',
				message: 'Failed to export logs',
				color: 'red',
			});
		}
		finally
		{
			setExporting(false);
			setExportProgress(0);
		}
	};

	const getEstimatedFileSize = () =>
	{
		const recordCount = exportConfig.maxRecords || totalLogs;
		let avgRecordSize = 500; // Base size in bytes

		if (exportConfig.includeMetadata) avgRecordSize += 200;
		if (exportConfig.includeStackTrace) avgRecordSize += 1000;

		const totalSize = recordCount * avgRecordSize;

		if (totalSize < 1024 * 1024)
		{
			return `${Math.round(totalSize / 1024)} KB`;
		}
		return `${Math.round(totalSize / (1024 * 1024))} MB`;

	};

	return (
		<Stack gap="md">
			<Paper p="md" withBorder>
				<Stack gap="md">
					<Group justify="space-between">
						<Text fw={500}>Export Configuration</Text>
						<Text size="sm" c="dimmed">
							{totalLogs.toLocaleString()} logs available
						</Text>
					</Group>

					<Select
						label="Export Format"
						data={formatOptions}
						value={exportConfig.format}
						onChange={(value) =>
						{
							updateConfig({
								format: (value === 'json' || value === 'csv' || value === 'txt') ? value : exportConfig.format,
							});
						}}
					/>

					<Select
						label="Compression"
						data={compressionOptions}
						value={exportConfig.compression || ''}
						onChange={(value) =>
						{
							updateConfig({
								compression: (value === 'gzip' || value === 'zip') ? value : undefined,
							});
						}}
					/>

					<NumberInput
						label="Maximum Records"
						description="Limit the number of records to export (0 = no limit)"
						value={exportConfig.maxRecords || 0}
						onChange={value => updateConfig({ maxRecords: Number(value) || undefined })}
						min={0}
						max={100000}
						step={1000}
					/>
				</Stack>
			</Paper>

			<Paper p="md" withBorder>
				<Stack gap="md">
					<Text fw={500}>Include Options</Text>

					<Switch
						label="Include Metadata"
						description="Include additional metadata fields in the export"
						checked={exportConfig.includeMetadata}
						onChange={event => updateConfig({ includeMetadata: event.currentTarget.checked })}
					/>

					<Switch
						label="Include Stack Traces"
						description="Include full stack traces for error logs"
						checked={exportConfig.includeStackTrace}
						onChange={event => updateConfig({ includeStackTrace: event.currentTarget.checked })}
					/>
				</Stack>
			</Paper>

			<Alert icon={<IconInfoCircle size={16} />} color="blue">
				<Stack gap="xs">
					<Text size="sm">
						<strong>Estimated file size:</strong> {getEstimatedFileSize()}
					</Text>
					<Text size="sm">
						<strong>Records to export:</strong> {(exportConfig.maxRecords || totalLogs).toLocaleString()}
					</Text>
					{exportConfig.format === 'csv' && (
						<Text size="sm" c="orange">
							CSV format may not preserve complex metadata structures
						</Text>
					)}
				</Stack>
			</Alert>

			{exporting && (
				<Paper p="md" withBorder>
					<Stack gap="sm">
						<Group justify="space-between">
							<Text size="sm">Exporting logs...</Text>
							<Text size="sm">{exportProgress}%</Text>
						</Group>
						<Progress value={exportProgress} animated />
					</Stack>
				</Paper>
			)}

			<Divider />

			<Group justify="flex-end">
				<Button variant="subtle" onClick={onClose} disabled={exporting}>
					Cancel
				</Button>
				<Button
					onClick={handleExport}
					loading={exporting}
					leftSection={<IconDownload size={16} />}
				>
					Export Logs
				</Button>
			</Group>
		</Stack>
	);
};

export { LogExportModal };
