'use client';

import {
	Container,
	Paper,
	Group,
	Text,
	Badge,
	Button,
	Table,
	ScrollArea,
	Modal,
	Stack,
	TextInput,
	Textarea,
	Select,
	MultiSelect,
	NumberInput,
	Switch,
	ActionIcon,
	Tooltip,
	Alert,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { clientLogger } from '@/lib/logger';
import { notifications } from '@mantine/notifications';
import {
	IconPlus,
	IconEdit,
	IconTrash,
	IconBell,
	IconBellOff,
	IconAlertTriangle,
	IconCheck,
	IconX,
} from '@tabler/icons-react';
import { useState, useEffect } from 'react';
import type { FC } from 'react';
import type { LogAlertType, ServiceNameType, LogLevelType } from '../../types/logs';

const LogAlerts: FC = () =>
{
	const [alerts, setAlerts] = useState<LogAlertType[]>([]);
	const [selectedAlert, setSelectedAlert] = useState<LogAlertType | null>(null);
	const [loading, setLoading] = useState(false);
	const [modalOpened, { open: openModal, close: closeModal }] = useDisclosure(false);
	const [isEditing, setIsEditing] = useState(false);

	const fetchAlerts = async () =>
	{
		try
		{
			setLoading(true);
			const response = await fetch('/api/logs/alerts');
			if (response.ok)
			{
				const data = await response.json();
				setAlerts(data);
			}
		}
		catch (error)
		{
			clientLogger.error('Error fetching log alerts:', error);
			notifications.show({
				title: 'Error',
				message: 'Failed to fetch log alerts',
				color: 'red',
			});
		}
		finally
		{
			setLoading(false);
		}
	};

	useEffect(() =>
	{
		fetchAlerts();
	}, []);

	const handleCreateAlert = () =>
	{
		setSelectedAlert(null);
		setIsEditing(false);
		openModal();
	};

	const handleEditAlert = (alert: LogAlertType) =>
	{
		setSelectedAlert(alert);
		setIsEditing(true);
		openModal();
	};

	const handleDeleteAlert = async (alertId: string) =>
	{
		try
		{
			const response = await fetch(`/api/logs/alerts/${alertId}`, {
				method: 'DELETE',
			});

			if (response.ok)
			{
				notifications.show({
					title: 'Success',
					message: 'Alert deleted successfully',
					color: 'green',
				});
				fetchAlerts();
			}
		}
		catch (error)
		{
			clientLogger.error('Error deleting alert:', error);
			notifications.show({
				title: 'Error',
				message: 'Failed to delete alert',
				color: 'red',
			});
		}
	};

	const handleToggleAlert = async (alertId: string, enabled: boolean) =>
	{
		try
		{
			const response = await fetch(`/api/logs/alerts/${alertId}/toggle`, {
				method: 'PUT',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ enabled }),
			});

			if (response.ok)
			{
				notifications.show({
					title: 'Success',
					message: `Alert ${enabled ? 'enabled' : 'disabled'} successfully`,
					color: 'green',
				});
				fetchAlerts();
			}
		}
		catch (error)
		{
			clientLogger.error('Error toggling alert:', error);
			notifications.show({
				title: 'Error',
				message: 'Failed to toggle alert',
				color: 'red',
			});
		}
	};

	const handleSaveAlert = async (alertData: Partial<LogAlertType>) =>
	{
		try
		{
			const url = isEditing ? `/api/logs/alerts/${selectedAlert?.id}` : '/api/logs/alerts';
			const method = isEditing ? 'PUT' : 'POST';

			const response = await fetch(url, {
				method,
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify(alertData),
			});

			if (response.ok)
			{
				notifications.show({
					title: 'Success',
					message: `Alert ${isEditing ? 'updated' : 'created'} successfully`,
					color: 'green',
				});
				fetchAlerts();
				closeModal();
			}
		}
		catch (error)
		{
			clientLogger.error('Error saving alert:', error);
			notifications.show({
				title: 'Error',
				message: 'Failed to save alert',
				color: 'red',
			});
		}
	};

	return (
		<Container size="xl" py="md">
			<Paper shadow="sm" p="md">
				<Group justify="space-between" mb="md">
					<Group>
						<Text size="lg" fw={600}>Log Alerts</Text>
						<Badge color="blue" variant="light">
							{alerts.length} alerts
						</Badge>
						<Badge color="green" variant="light">
							{alerts.filter(a => a.enabled).length} active
						</Badge>
					</Group>

					<Button
						onClick={handleCreateAlert}
						leftSection={<IconPlus size={16} />}
					>
						Create Alert
					</Button>
				</Group>

				<Alert color="blue" icon={<IconAlertTriangle size={16} />} mb="md">
					<Text size="sm">
						Log alerts monitor your system logs in real-time and trigger notifications
						when specific conditions are met. Configure thresholds carefully to avoid alert fatigue.
					</Text>
				</Alert>

				<ScrollArea>
					<Table striped highlightOnHover>
						<Table.Thead>
							<Table.Tr>
								<Table.Th>Name</Table.Th>
								<Table.Th>Status</Table.Th>
								<Table.Th>Conditions</Table.Th>
								<Table.Th>Services</Table.Th>
								<Table.Th>Triggers</Table.Th>
								<Table.Th>Last Triggered</Table.Th>
								<Table.Th>Actions</Table.Th>
							</Table.Tr>
						</Table.Thead>
						<Table.Tbody>
							{alerts.map(alert => (
								<Table.Tr key={alert.id}>
									<Table.Td>
										<Stack gap={2}>
											<Text size="sm" fw={500}>{alert.name}</Text>
											{alert.description && (
												<Text size="xs" c="dimmed">{alert.description}</Text>
											)}
										</Stack>
									</Table.Td>
									<Table.Td>
										<Group gap="xs">
											<Badge
												color={alert.enabled ? 'green' : 'gray'}
												variant="light"
												leftSection={alert.enabled ? <IconBell size={12} /> : <IconBellOff size={12} />}
											>
												{alert.enabled ? 'Active' : 'Disabled'}
											</Badge>
										</Group>
									</Table.Td>
									<Table.Td>
										<Stack gap={2}>
											<Group gap="xs">
												{alert.conditions.levels.map(level => (
													<Badge key={level} size="xs" variant="outline">
														{level.toUpperCase()}
													</Badge>
												))}
											</Group>
											<Text size="xs" c="dimmed">
												{alert.conditions.threshold} {alert.conditions.operator} in {alert.conditions.timeWindow}min
											</Text>
											{alert.conditions.pattern && (
												<Text size="xs" c="dimmed" ff="monospace">
													Pattern: {alert.conditions.pattern}
												</Text>
											)}
										</Stack>
									</Table.Td>
									<Table.Td>
										<Group gap="xs">
											{alert.conditions.services.slice(0, 2).map(service => (
												<Badge key={service} size="xs" variant="outline">
													{service}
												</Badge>
											))}
											{alert.conditions.services.length > 2 && (
												<Badge size="xs" variant="light">
													+{alert.conditions.services.length - 2}
												</Badge>
											)}
										</Group>
									</Table.Td>
									<Table.Td>
										<Text fw={500}>{alert.triggerCount}</Text>
									</Table.Td>
									<Table.Td>
										{alert.lastTriggered ? (
											<Text size="sm" c="dimmed">
												{new Date(alert.lastTriggered).toLocaleString()}
											</Text>
										) : (
											<Text size="sm" c="dimmed">Never</Text>
										)}
									</Table.Td>
									<Table.Td>
										<Group gap="xs">
											<Tooltip label={alert.enabled ? 'Disable alert' : 'Enable alert'}>
												<ActionIcon
													size="sm"
													variant="light"
													color={alert.enabled ? 'orange' : 'green'}
													onClick={() => handleToggleAlert(alert.id, !alert.enabled)}
												>
													{alert.enabled ? <IconBellOff size={14} /> : <IconBell size={14} />}
												</ActionIcon>
											</Tooltip>

											<Tooltip label="Edit alert">
												<ActionIcon
													size="sm"
													variant="light"
													onClick={() => handleEditAlert(alert)}
												>
													<IconEdit size={14} />
												</ActionIcon>
											</Tooltip>

											<Tooltip label="Delete alert">
												<ActionIcon
													size="sm"
													variant="light"
													color="red"
													onClick={() => handleDeleteAlert(alert.id)}
												>
													<IconTrash size={14} />
												</ActionIcon>
											</Tooltip>
										</Group>
									</Table.Td>
								</Table.Tr>
							))}
						</Table.Tbody>
					</Table>
				</ScrollArea>
			</Paper>

			<Modal
				opened={modalOpened}
				onClose={closeModal}
				title={isEditing ? 'Edit Alert' : 'Create Alert'}
				size="lg"
			>
				<LogAlertForm
					alert={selectedAlert}
					onSave={handleSaveAlert}
					onClose={closeModal}
				/>
			</Modal>
		</Container>
	);
};

type LogAlertFormProps = {
	alert: LogAlertType | null;
	onSave: (alertData: Partial<LogAlertType>) => void;
	onClose: () => void;
};

const LogAlertForm: FC<LogAlertFormProps> = ({ alert, onSave, onClose }) =>
{
	const [formData, setFormData] = useState({
		name: alert?.name || '',
		description: alert?.description || '',
		enabled: alert?.enabled ?? true,
		conditions: {
			services: alert?.conditions.services || [],
			levels: alert?.conditions.levels || [],
			pattern: alert?.conditions.pattern || '',
			threshold: alert?.conditions.threshold || 10,
			timeWindow: alert?.conditions.timeWindow || 5,
			operator: alert?.conditions.operator || 'greater_than' as const,
		},
		actions: {
			email: alert?.actions.email || [],
			webhook: alert?.actions.webhook || '',
			createIncident: alert?.actions.createIncident || false,
		},
	});

	const serviceOptions = [
		{ value: 'web-app', label: 'Web App' },
		{ value: 'crawler', label: 'Crawler' },
		{ value: 'ranking-engine', label: 'Ranking Engine' },
		{ value: 'scheduler', label: 'Scheduler' },
		{ value: 'domain-seeder', label: 'Domain Seeder' },
		{ value: 'admin', label: 'Admin Panel' },
	];

	const levelOptions = [
		{ value: 'debug', label: 'Debug' },
		{ value: 'info', label: 'Info' },
		{ value: 'warn', label: 'Warning' },
		{ value: 'error', label: 'Error' },
		{ value: 'fatal', label: 'Fatal' },
	];

	const operatorOptions = [
		{ value: 'greater_than', label: 'Greater Than' },
		{ value: 'less_than', label: 'Less Than' },
		{ value: 'equals', label: 'Equals' },
	];

	const handleSubmit = () =>
	{
		if (!formData.name.trim())
		{
			notifications.show({
				title: 'Validation Error',
				message: 'Alert name is required',
				color: 'red',
			});
			return;
		}

		if (formData.conditions.services.length === 0)
		{
			notifications.show({
				title: 'Validation Error',
				message: 'At least one service must be selected',
				color: 'red',
			});
			return;
		}

		if (formData.conditions.levels.length === 0)
		{
			notifications.show({
				title: 'Validation Error',
				message: 'At least one log level must be selected',
				color: 'red',
			});
			return;
		}

		onSave(formData);
	};

	return (
		<Stack gap="md">
			<TextInput
				label="Alert Name"
				placeholder="Enter alert name"
				value={formData.name}
				onChange={event => setFormData(prev => ({
					...prev,
					name: event.currentTarget.value,
				}))}
				required
			/>

			<Textarea
				label="Description"
				placeholder="Describe what this alert monitors"
				value={formData.description}
				onChange={event => setFormData(prev => ({
					...prev,
					description: event.currentTarget.value,
				}))}
				rows={2}
			/>

			<Switch
				label="Enable Alert"
				checked={formData.enabled}
				onChange={event => setFormData(prev => ({
					...prev,
					enabled: event.currentTarget.checked,
				}))}
			/>

			<Paper p="md" withBorder>
				<Text fw={500} mb="md">Conditions</Text>

				<Stack gap="md">
					<MultiSelect
						label="Services"
						placeholder="Select services to monitor"
						data={serviceOptions}
						value={formData.conditions.services}
						onChange={value => setFormData(prev => ({
							...prev,
							conditions: {
								...prev.conditions,
								services: value as ServiceNameType[],
							},
						}))}
						required
					/>

					<MultiSelect
						label="Log Levels"
						placeholder="Select log levels to monitor"
						data={levelOptions}
						value={formData.conditions.levels}
						onChange={value => setFormData(prev => ({
							...prev,
							conditions: {
								...prev.conditions,
								levels: value as LogLevelType[],
							},
						}))}
						required
					/>

					<TextInput
						label="Pattern (Optional)"
						placeholder="Regular expression pattern to match"
						value={formData.conditions.pattern}
						onChange={event => setFormData(prev => ({
							...prev,
							conditions: {
								...prev.conditions,
								pattern: event.currentTarget.value,
							},
						}))}
					/>

					<Group grow>
						<NumberInput
							label="Threshold"
							value={formData.conditions.threshold}
							onChange={value => setFormData(prev => ({
								...prev,
								conditions: {
									...prev.conditions,
									threshold: Number(value) || 0,
								},
							}))}
							min={1}
						/>

						<Select
							label="Operator"
							data={operatorOptions}
							value={formData.conditions.operator}
							onChange={value => setFormData(prev => ({
								...prev,
								conditions: {
									...prev.conditions,
									operator: value as any,
								},
							}))}
						/>

						<NumberInput
							label="Time Window (minutes)"
							value={formData.conditions.timeWindow}
							onChange={value => setFormData(prev => ({
								...prev,
								conditions: {
									...prev.conditions,
									timeWindow: Number(value) || 1,
								},
							}))}
							min={1}
							max={1440}
						/>
					</Group>
				</Stack>
			</Paper>

			<Paper p="md" withBorder>
				<Text fw={500} mb="md">Actions</Text>

				<Stack gap="md">
					<TextInput
						label="Email Recipients"
						placeholder="<EMAIL>,<EMAIL>"
						value={formData.actions.email?.join(',') || ''}
						onChange={(event) =>
						{
							const emails = event.currentTarget.value
								.split(',')
								.map(email => email.trim())
								.filter(email => email);
							setFormData(prev => ({
								...prev,
								actions: {
									...prev.actions,
									email: emails,
								},
							}));
						}}
					/>

					<TextInput
						label="Webhook URL"
						placeholder="https://your-webhook-url.com"
						value={formData.actions.webhook}
						onChange={event => setFormData(prev => ({
							...prev,
							actions: {
								...prev.actions,
								webhook: event.currentTarget.value,
							},
						}))}
					/>

					<Switch
						label="Create Incident"
						description="Automatically create an incident when this alert triggers"
						checked={formData.actions.createIncident}
						onChange={event => setFormData(prev => ({
							...prev,
							actions: {
								...prev.actions,
								createIncident: event.currentTarget.checked,
							},
						}))}
					/>
				</Stack>
			</Paper>

			<Group justify="flex-end">
				<Button variant="subtle" onClick={onClose}>
					Cancel
				</Button>
				<Button onClick={handleSubmit} leftSection={<IconCheck size={16} />}>
					{alert ? 'Update Alert' : 'Create Alert'}
				</Button>
			</Group>
		</Stack>
	);
};

export default LogAlerts;
