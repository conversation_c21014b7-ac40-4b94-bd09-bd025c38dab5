'use client';

import {
	Paper,
	Group,
	Button,
	Select,
	Modal,
	Stack,
	Text,
	Alert,
	Switch,
	Badge,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { clientLogger } from '@/lib/logger';
import {
	IconTrash,
	IconEdit,
	IconRefresh,
	IconEye,
	IconAlertTriangle,
	IconX,
} from '@tabler/icons-react';
import { useState } from 'react';

import type { DomainBulkOperationRequest, DomainBulkAction } from '@/types/domain';

interface DomainBulkActionsProps
{
	selectedDomains: string[];
	onBulkOperation: (request: DomainBulkOperationRequest) => Promise<void>;
	onClearSelection: () => void;
}

function DomainBulkActions({ selectedDomains, onBulkOperation, onClearSelection }: DomainBulkActionsProps)
{
	const [confirmOpened, { open: openConfirm, close: closeConfirm }] = useDisclosure(false);
	const [pendingAction, setPendingAction] = useState<DomainBulkAction | null>(null);
	const [actionParameters, setActionParameters] = useState<Record<string, unknown>>({});
	const [loading, setLoading] = useState(false);

	const bulkActions = [
		{
			value: 'updateCategory',
			label: 'Update Category',
			icon: IconEdit,
			color: 'blue',
			requiresParams: true,
		},
		{
			value: 'updateVisibility',
			label: 'Update Visibility',
			icon: IconEye,
			color: 'green',
			requiresParams: true,
		},
		{
			value: 'triggerCrawl',
			label: 'Trigger Crawl',
			icon: IconRefresh,
			color: 'orange',
			requiresParams: true,
		},
		{
			value: 'recalculateRanking',
			label: 'Recalculate Ranking',
			icon: IconRefresh,
			color: 'purple',
			requiresParams: false,
		},
		{
			value: 'delete',
			label: 'Delete Domains',
			icon: IconTrash,
			color: 'red',
			requiresParams: false,
			dangerous: true,
		},
	];

	const handleActionSelect = (action: DomainBulkAction) =>
	{
		setPendingAction(action);
		setActionParameters({});
		openConfirm();
	};

	const handleConfirmAction = async () =>
	{
		if (!pendingAction) return;

		setLoading(true);

		try
		{
			const request: DomainBulkOperationRequest = {
				action: pendingAction,
				domains: selectedDomains,
				parameters: Object.keys(actionParameters).length > 0 ? actionParameters : undefined,
			};

			await onBulkOperation(request);
			closeConfirm();
			setPendingAction(null);
			setActionParameters({});
		}
		catch (error)
		{
			clientLogger.error('Bulk operation failed:', error);
		}
		finally
		{
			setLoading(false);
		}
	};

	const renderActionParameters = () =>
	{
		if (!pendingAction) return null;

		switch (pendingAction)
		{
			case 'updateCategory':
				return (
					<Select
						label="New Category"
						placeholder="Select category..."
						data={[
							{ value: 'tech', label: 'Technology' },
							{ value: 'edu', label: 'Education' },
							{ value: 'business', label: 'Business' },
							{ value: 'ecommerce', label: 'E-commerce' },
							{ value: 'media', label: 'Media & Entertainment' },
							{ value: 'health', label: 'Health & Medical' },
							{ value: 'finance', label: 'Finance' },
							{ value: 'travel', label: 'Travel & Tourism' },
							{ value: 'food', label: 'Food & Beverage' },
							{ value: 'sports', label: 'Sports & Recreation' },
						]}
						value={actionParameters.category as string || ''}
						onChange={value => setActionParameters(prev => ({ ...prev, category: value }))}
						required
					/>
				);

			case 'updateVisibility':
				return (
					<Switch
						label="Make domains public"
						description="Toggle to make domains visible to end users"
						checked={actionParameters.isPublic as boolean || false}
						onChange={event => setActionParameters(prev => ({
							...prev,
							isPublic: event.currentTarget.checked,
						}))}
					/>
				);

			case 'triggerCrawl':
				return (
					<Stack gap="md">
						<Select
							label="Crawl Type"
							placeholder="Select crawl type..."
							data={[
								{ value: 'basic', label: 'Basic Crawl' },
								{ value: 'standard', label: 'Standard Crawl' },
								{ value: 'full', label: 'Full Crawl' },
								{ value: 'priority', label: 'Priority Crawl' },
								{ value: 'light', label: 'Light Crawl' },
								{ value: 'visual', label: 'Visual Crawl' },
								{ value: 'advanced', label: 'Advanced Crawl' },
							]}
							value={actionParameters.crawlType as string || ''}
							onChange={value => setActionParameters(prev => ({ ...prev, crawlType: value }))}
							required
						/>
						<Select
							label="Priority"
							placeholder="Select priority..."
							data={[
								{ value: 'low', label: 'Low Priority' },
								{ value: 'medium', label: 'Medium Priority' },
								{ value: 'high', label: 'High Priority' },
							]}
							value={actionParameters.priority as string || 'medium'}
							onChange={value => setActionParameters(prev => ({ ...prev, priority: value }))}
						/>
					</Stack>
				);

			default:
				return null;
		}
	};

	const getActionDescription = () =>
	{
		if (!pendingAction) return '';

		switch (pendingAction)
		{
			case 'updateCategory':
				return 'This will update the category for all selected domains. The change will be reflected in search results and rankings.';
			case 'updateVisibility':
				return 'This will change the visibility status of all selected domains. Public domains are visible to end users, while private domains are admin-only.';
			case 'triggerCrawl':
				return 'This will queue crawl jobs for all selected domains. The crawl will be executed based on the selected type and priority.';
			case 'recalculateRanking':
				return 'This will trigger a ranking recalculation for all selected domains. This may take some time to complete.';
			case 'delete':
				return 'This will permanently delete all selected domains and their associated data. This action cannot be undone.';
			default:
				return '';
		}
	};

	const selectedAction = bulkActions.find(action => action.value === pendingAction);

	return (
		<>
			<Paper withBorder p="md" bg="blue.0">
				<Group justify="space-between">
					<Group>
						<Badge color="blue" size="lg">
							{selectedDomains.length} domains selected
						</Badge>
						<Text size="sm" c="dimmed">
							Choose a bulk action to apply to all selected domains
						</Text>
					</Group>

					<Group>
						{bulkActions.map((action) =>
						{
							const Icon = action.icon;
							return (
								<Button
									key={action.value}
									leftSection={<Icon size={16} />}
									variant={action.dangerous ? 'filled' : 'light'}
									color={action.color}
									size="sm"
									onClick={() => handleActionSelect(action.value as DomainBulkAction)}
								>
									{action.label}
								</Button>
							);
						})}

						<Button
							leftSection={<IconX size={16} />}
							variant="subtle"
							color="gray"
							size="sm"
							onClick={onClearSelection}
						>
							Clear Selection
						</Button>
					</Group>
				</Group>
			</Paper>

			{/* Confirmation Modal */}
			<Modal
				opened={confirmOpened}
				onClose={closeConfirm}
				title={(
					<Group>
						{selectedAction && <selectedAction.icon size={20} color={selectedAction.color} />}
						<Text fw={600}>
							Confirm Bulk Action: {selectedAction?.label}
						</Text>
					</Group>
				)}
				size="md"
				centered
			>
				<Stack gap="md">
					{selectedAction?.dangerous && (
						<Alert
							icon={<IconAlertTriangle size={16} />}
							title="Warning"
							color="red"
						>
							This is a destructive action that cannot be undone. Please proceed with caution.
						</Alert>
					)}

					<Text size="sm">
						You are about to perform this action on <strong>{selectedDomains.length}</strong> domains:
					</Text>

					<Paper withBorder p="sm" bg="gray.0">
						<Text size="xs" c="dimmed" mb="xs">Selected domains:</Text>
						<Group gap="xs">
							{selectedDomains.slice(0, 5).map(domain => (
								<Badge key={domain} variant="light" size="sm">
									{domain}
								</Badge>
							))}
							{selectedDomains.length > 5 && (
								<Badge variant="light" size="sm" color="gray">
									+{selectedDomains.length - 5} more
								</Badge>
							)}
						</Group>
					</Paper>

					<Text size="sm" c="dimmed">
						{getActionDescription()}
					</Text>

					{renderActionParameters()}

					<Group justify="flex-end" mt="md">
						<Button
							variant="light"
							onClick={closeConfirm}
							disabled={loading}
						>
							Cancel
						</Button>
						<Button
							color={selectedAction?.color}
							onClick={handleConfirmAction}
							loading={loading}
							disabled={
								selectedAction?.requiresParams &&
                Object.keys(actionParameters).length === 0
							}
						>
							{selectedAction?.dangerous ? 'Delete Domains' : 'Confirm Action'}
						</Button>
					</Group>
				</Stack>
			</Modal>
		</>
	);
}

export { DomainBulkActions };
