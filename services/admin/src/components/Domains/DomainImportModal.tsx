'use client';

import {
	<PERSON><PERSON>,
	<PERSON><PERSON>,
	Group,
	Button,
	Select,
	Switch,
	Text,
	Paper,
	Progress,
	Alert,
	Divider,
	FileInput,
	List,
	Badge,
	Table,
	Grid,
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import {
	IconUpload,
	IconFile,
	IconAlertCircle,
	IconCheck,
	IconX,
	IconInfoCircle,
} from '@tabler/icons-react';
import { useState, useRef } from 'react';

import type { DomainExportFormat, DomainImportRequest, DomainImportResult } from '@/types/domain';

interface DomainImportModalProps
{
	opened: boolean;
	onClose: () => void;
	onImportComplete: () => void;
}

function DomainImportModal({ opened, onClose, onImportComplete }: DomainImportModalProps)
{
	const [selectedFile, setSelectedFile] = useState<File | null>(null);
	const [importFormat, setImportFormat] = useState<DomainExportFormat>('csv');
	const [skipDuplicates, setSkipDuplicates] = useState(true);
	const [updateExisting, setUpdateExisting] = useState(false);
	const [validateDomains, setValidateDomains] = useState(true);
	const [importing, setImporting] = useState(false);
	const [importProgress, setImportProgress] = useState(0);
	const [importResult, setImportResult] = useState<DomainImportResult | null>(null);
	const fileInputRef = useRef<HTMLButtonElement>(null);

	const formatOptions = [
		{
			value: 'csv',
			label: 'CSV (Comma Separated Values)',
			accept: '.csv',
			description: 'Standard CSV format with headers',
		},
		{
			value: 'json',
			label: 'JSON (JavaScript Object Notation)',
			accept: '.json',
			description: 'JSON array of domain objects',
		},
		{
			value: 'excel',
			label: 'Excel Workbook (.xlsx)',
			accept: '.xlsx',
			description: 'Excel workbook with domain data',
		},
	];

	const handleFileSelect = (file: File | null) =>
	{
		setSelectedFile(file);
		setImportResult(null);
	};

	const handleImport = async () =>
	{
		if (!selectedFile)
		{
			notifications.show({
				title: 'Error',
				message: 'Please select a file to import',
				color: 'red',
				icon: <IconAlertCircle size={16} />,
			});
			return;
		}

		setImporting(true);
		setImportProgress(0);
		setImportResult(null);

		try
		{
			const formData = new FormData();
			formData.append('file', selectedFile);
			formData.append('format', importFormat);
			formData.append('skipDuplicates', skipDuplicates.toString());
			formData.append('updateExisting', updateExisting.toString());
			formData.append('validateDomains', validateDomains.toString());

			// Simulate progress updates
			const progressInterval = setInterval(() =>
			{
				setImportProgress((prev) =>
				{
					if (prev >= 90) return prev;
					return prev + Math.random() * 15;
				});
			}, 500);

			const response = await fetch('/api/domains/import', {
				method: 'POST',
				body: formData,
			});

			clearInterval(progressInterval);
			setImportProgress(100);

			if (!response.ok)
			{
				throw new Error('Import failed');
			}

			const result: DomainImportResult = await response.json();
			setImportResult(result);

			if (result.success)
			{
				notifications.show({
					title: 'Import Successful',
					message: `${result.importedCount} domains imported successfully`,
					color: 'green',
					icon: <IconCheck size={16} />,
				});

				// Refresh the domain list
				onImportComplete();
			}
			else
			{
				notifications.show({
					title: 'Import Completed with Errors',
					message: `${result.importedCount} imported, ${result.errorCount} errors`,
					color: 'yellow',
					icon: <IconAlertCircle size={16} />,
				});
			}
		}
		catch (error)
		{
			const errorMessage = error instanceof Error ? error.message : 'Import failed';
			notifications.show({
				title: 'Import Failed',
				message: errorMessage,
				color: 'red',
				icon: <IconAlertCircle size={16} />,
			});
		}
		finally
		{
			setImporting(false);
			setImportProgress(0);
		}
	};

	const handleClose = () =>
	{
		if (!importing)
		{
			setSelectedFile(null);
			setImportResult(null);
			onClose();
		}
	};

	const selectedFormat = formatOptions.find(f => f.value === importFormat);

	return (
		<Modal
			opened={opened}
			onClose={handleClose}
			title="Import Domains"
			size="lg"
			centered
			closeOnClickOutside={!importing}
			closeOnEscape={!importing}
		>
			<Stack gap="md">
				{/* Import Format Selection */}
				<Stack gap="xs">
					<Text fw={500}>Import Format</Text>
					<Select
						data={formatOptions.map(f => ({
							value: f.value,
							label: f.label,
						}))}
						value={importFormat}
						onChange={value => value && setImportFormat(value as DomainExportFormat)}
						disabled={importing}
					/>
					{selectedFormat && (
						<Text size="sm" c="dimmed">
							{selectedFormat.description}
						</Text>
					)}
				</Stack>

				<Divider />

				{/* File Selection */}
				<Stack gap="xs">
					<Text fw={500}>Select File</Text>
					<FileInput
						ref={fileInputRef}
						placeholder="Choose file to import..."
						leftSection={<IconFile size={16} />}
						accept={selectedFormat?.accept}
						value={selectedFile}
						onChange={handleFileSelect}
						disabled={importing}
					/>
					{selectedFile && (
						<Paper withBorder p="sm" bg="blue.0">
							<Group justify="space-between">
								<Group gap="xs">
									<IconFile size={16} />
									<Stack gap={0}>
										<Text size="sm" fw={500}>{selectedFile.name}</Text>
										<Text size="xs" c="dimmed">
											{(selectedFile.size / 1024).toFixed(1)} KB
										</Text>
									</Stack>
								</Group>
								<Button
									size="xs"
									variant="subtle"
									color="red"
									onClick={() => setSelectedFile(null)}
									disabled={importing}
								>
									Remove
								</Button>
							</Group>
						</Paper>
					)}
				</Stack>

				<Divider />

				{/* Import Options */}
				<Stack gap="md">
					<Text fw={500}>Import Options</Text>

					<Switch
						label="Skip duplicate domains"
						description="Skip domains that already exist in the database"
						checked={skipDuplicates}
						onChange={event => setSkipDuplicates(event.currentTarget.checked)}
						disabled={importing}
					/>

					<Switch
						label="Update existing domains"
						description="Update existing domains with new data from the import file"
						checked={updateExisting}
						onChange={event => setUpdateExisting(event.currentTarget.checked)}
						disabled={importing || skipDuplicates}
					/>

					<Switch
						label="Validate domain names"
						description="Validate that domain names are properly formatted before importing"
						checked={validateDomains}
						onChange={event => setValidateDomains(event.currentTarget.checked)}
						disabled={importing}
					/>
				</Stack>

				{/* Import Guidelines */}
				<Alert icon={<IconInfoCircle size={16} />} title="Import Guidelines" color="blue">
					<List size="sm">
						<List.Item>Ensure your file contains a 'domain' column with valid domain names</List.Item>
						<List.Item>CSV files should have headers in the first row</List.Item>
						<List.Item>JSON files should contain an array of domain objects</List.Item>
						<List.Item>Large files may take several minutes to process</List.Item>
					</List>
				</Alert>

				{/* Import Progress */}
				{importing && (
					<Stack gap="xs">
						<Group justify="space-between">
							<Text size="sm" fw={500}>Importing domains...</Text>
							<Text size="sm" c="dimmed">{Math.round(importProgress)}%</Text>
						</Group>
						<Progress value={importProgress} animated />
					</Stack>
				)}

				{/* Import Results */}
				{importResult && (
					<Stack gap="md">
						<Divider />
						<Text fw={500}>Import Results</Text>

						<Paper withBorder p="md">
							<Grid gutter="md">
								<Grid.Col span={3}>
									<Stack gap="xs" align="center">
										<Badge color="green" size="lg" variant="light">
											{importResult.importedCount}
										</Badge>
										<Text size="sm" ta="center">Imported</Text>
									</Stack>
								</Grid.Col>
								<Grid.Col span={3}>
									<Stack gap="xs" align="center">
										<Badge color="yellow" size="lg" variant="light">
											{importResult.skippedCount}
										</Badge>
										<Text size="sm" ta="center">Skipped</Text>
									</Stack>
								</Grid.Col>
								<Grid.Col span={3}>
									<Stack gap="xs" align="center">
										<Badge color="red" size="lg" variant="light">
											{importResult.errorCount}
										</Badge>
										<Text size="sm" ta="center">Errors</Text>
									</Stack>
								</Grid.Col>
								<Grid.Col span={3}>
									<Stack gap="xs" align="center">
										<Badge
											color={importResult.success ? 'green' : 'red'}
											size="lg"
											variant="light"
										>
											{importResult.success ? <IconCheck size={16} /> : <IconX size={16} />}
										</Badge>
										<Text size="sm" ta="center">
											{importResult.success ? 'Success' : 'Failed'}
										</Text>
									</Stack>
								</Grid.Col>
							</Grid>
						</Paper>

						{/* Error Details */}
						{importResult.errors && importResult.errors.length > 0 && (
							<Stack gap="xs">
								<Text fw={500} c="red">Import Errors</Text>
								<Paper withBorder p="sm" mah={200} style={{ overflow: 'auto' }}>
									<Table>
										<Table.Thead>
											<Table.Tr>
												<Table.Th>Row</Table.Th>
												<Table.Th>Domain</Table.Th>
												<Table.Th>Error</Table.Th>
											</Table.Tr>
										</Table.Thead>
										<Table.Tbody>
											{importResult.errors.map((error, index) => (
												<Table.Tr key={index}>
													<Table.Td>{error.row}</Table.Td>
													<Table.Td>{error.domain || 'N/A'}</Table.Td>
													<Table.Td>
														<Text size="sm" c="red">{error.error}</Text>
													</Table.Td>
												</Table.Tr>
											))}
										</Table.Tbody>
									</Table>
								</Paper>
							</Stack>
						)}
					</Stack>
				)}

				{/* Action Buttons */}
				<Group justify="flex-end" mt="md">
					<Button
						variant="light"
						onClick={handleClose}
						disabled={importing}
					>
						{importResult ? 'Close' : 'Cancel'}
					</Button>
					{!importResult && (
						<Button
							leftSection={<IconUpload size={16} />}
							onClick={handleImport}
							loading={importing}
							disabled={!selectedFile}
						>
							{importing ? 'Importing...' : 'Import Domains'}
						</Button>
					)}
				</Group>
			</Stack>
		</Modal>
	);
}

export { DomainImportModal };
