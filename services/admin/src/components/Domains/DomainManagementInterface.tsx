'use client';

import {
	Paper,
	Stack,
	Group,
	Button,
	Badge,
	Text,
	Loader,
	Alert,
	Modal,
	ActionIcon,
	Tooltip,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';
import {
	IconSearch,
	IconFilter,
	IconDownload,
	IconUpload,
	IconRefresh,
	IconEye,
	IconEdit,
	IconTrash,
	IconAlertCircle,
} from '@tabler/icons-react';
import { useState, useEffect, useCallback } from 'react';


import type {
	DomainSearchResult,
	DomainFilters,
	DomainSort,
	DomainListItem,
	DomainBulkOperationRequest,
} from '@/types/domain';
import { DomainBulkActions } from './DomainBulkActions';
import { DomainDetailsModal } from './DomainDetailsModal';
import { DomainExportModal } from './DomainExportModal';
import { DomainImportModal } from './DomainImportModal';
import { DomainSearchFilters } from './DomainSearchFilters';
import { DomainTable } from './DomainTable';

function DomainManagementInterface()
{
	// State management
	const [searchResult, setSearchResult] = useState<DomainSearchResult | null>(null);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [selectedDomains, setSelectedDomains] = useState<string[]>([]);
	const [selectedDomain, setSelectedDomain] = useState<string | null>(null);

	// Modal states
	const [filtersOpened, { open: openFilters, close: closeFilters }] = useDisclosure(false);
	const [detailsOpened, { open: openDetails, close: closeDetails }] = useDisclosure(false);
	const [exportOpened, { open: openExport, close: closeExport }] = useDisclosure(false);
	const [importOpened, { open: openImport, close: closeImport }] = useDisclosure(false);

	// Default filters and sorting
	const [filters, setFilters] = useState<Partial<DomainFilters>>({
		search: '',
		category: [],
		crawlStatus: [],
		seederStatus: [],
		isPublic: null,
		rankRange: [1, 1000000],
		scoreRange: [0, 100],
		sslGrades: [],
	});

	const [sort, setSort] = useState<DomainSort>({
		field: 'domain',
		direction: 'asc',
	});

	const [pagination, setPagination] = useState({
		page: 1,
		pageSize: 25,
	});

	// Fetch domains data
	const fetchDomains = useCallback(async () =>
	{
		setLoading(true);
		setError(null);

		try
		{
			const params = new URLSearchParams();

			// Add pagination
			params.append('page', pagination.page.toString());
			params.append('pageSize', pagination.pageSize.toString());

			// Add sorting
			params.append('sortField', sort.field);
			params.append('sortDirection', sort.direction);

			// Add filters
			if (filters.search)
			{
				params.append('search', filters.search);
			}

			if (filters.category && filters.category.length > 0)
			{
				filters.category.forEach(cat => params.append('category', cat));
			}

			if (filters.crawlStatus && filters.crawlStatus.length > 0)
			{
				filters.crawlStatus.forEach(status => params.append('crawlStatus', status));
			}

			if (filters.seederStatus && filters.seederStatus.length > 0)
			{
				filters.seederStatus.forEach(status => params.append('seederStatus', status));
			}

			if (filters.isPublic !== null && filters.isPublic !== undefined)
			{
				params.append('isPublic', filters.isPublic.toString());
			}

			if (filters.rankRange)
			{
				params.append('rankMin', filters.rankRange[0].toString());
				params.append('rankMax', filters.rankRange[1].toString());
			}

			if (filters.scoreRange)
			{
				params.append('scoreMin', filters.scoreRange[0].toString());
				params.append('scoreMax', filters.scoreRange[1].toString());
			}

			if (filters.sslGrades && filters.sslGrades.length > 0)
			{
				filters.sslGrades.forEach(grade => params.append('sslGrade', grade));
			}

			const response = await fetch(`/api/domains?${params.toString()}`);

			if (!response.ok)
			{
				throw new Error('Failed to fetch domains');
			}

			const result: DomainSearchResult = await response.json();
			setSearchResult(result);
		}
		catch (err)
		{
			const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
			setError(errorMessage);
			notifications.show({
				title: 'Error',
				message: errorMessage,
				color: 'red',
				icon: <IconAlertCircle size={16} />,
			});
		}
		finally
		{
			setLoading(false);
		}
	}, [filters, sort, pagination]);

	// Initial load
	useEffect(() =>
	{
		fetchDomains();
	}, [fetchDomains]);

	// Handle filter changes
	const handleFiltersChange = useCallback((newFilters: Partial<DomainFilters>) =>
	{
		setFilters(newFilters);
		setPagination(prev => ({ ...prev, page: 1 })); // Reset to first page
	}, []);

	// Handle sort changes
	const handleSortChange = useCallback((newSort: DomainSort) =>
	{
		setSort(newSort);
		setPagination(prev => ({ ...prev, page: 1 })); // Reset to first page
	}, []);

	// Handle pagination changes
	const handlePaginationChange = useCallback((page: number, pageSize?: number) =>
	{
		setPagination(prev => ({
			page,
			pageSize: pageSize ?? prev.pageSize,
		}));
	}, []);

	// Handle domain selection
	const handleDomainSelect = useCallback((domain: string, selected: boolean) =>
	{
		setSelectedDomains((prev) =>
		{
			if (selected)
			{
				return [...prev, domain];
			}

			return prev.filter(d => d !== domain);
		});
	}, []);

	// Handle select all
	const handleSelectAll = useCallback((selected: boolean) =>
	{
		if (selected && searchResult)
		{
			setSelectedDomains(searchResult.domains.map(d => d.domain));
		}
		else
		{
			setSelectedDomains([]);
		}
	}, [searchResult]);

	// Handle domain details view
	const handleViewDetails = useCallback((domain: string) =>
	{
		setSelectedDomain(domain);
		openDetails();
	}, [openDetails]);

	// Handle bulk operations
	const handleBulkOperation = useCallback(async (request: DomainBulkOperationRequest) =>
	{
		try
		{
			const response = await fetch('/api/domains/bulk', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify(request),
			});

			if (!response.ok)
			{
				throw new Error('Bulk operation failed');
			}

			const result = await response.json();

			if (result.success)
			{
				notifications.show({
					title: 'Success',
					message: `Bulk operation completed: ${result.processedCount} domains processed`,
					color: 'green',
				});

				// Clear selection and refresh data
				setSelectedDomains([]);
				await fetchDomains();
			}
			else
			{
				notifications.show({
					title: 'Partial Success',
					message: `${result.processedCount} domains processed, ${result.failedCount} failed`,
					color: 'yellow',
				});
			}
		}
		catch (err)
		{
			const errorMessage = err instanceof Error ? err.message : 'Bulk operation failed';
			notifications.show({
				title: 'Error',
				message: errorMessage,
				color: 'red',
				icon: <IconAlertCircle size={16} />,
			});
		}
	}, [fetchDomains]);

	// Handle refresh
	const handleRefresh = useCallback(() =>
	{
		fetchDomains();
	}, [fetchDomains]);

	return (
		<Stack gap="md">
			{/* Header with actions */}
			<Paper withBorder p="md">
				<Group justify="space-between">
					<Group>
						<Button
							leftSection={<IconFilter size={16} />}
							variant="light"
							onClick={openFilters}
						>
							Filters
						</Button>

						<Button
							leftSection={<IconRefresh size={16} />}
							variant="light"
							onClick={handleRefresh}
							loading={loading}
						>
							Refresh
						</Button>
					</Group>

					<Group>
						<Button
							leftSection={<IconUpload size={16} />}
							variant="light"
							onClick={openImport}
						>
							Import
						</Button>

						<Button
							leftSection={<IconDownload size={16} />}
							variant="light"
							onClick={openExport}
						>
							Export
						</Button>
					</Group>
				</Group>

				{/* Summary stats */}
				{searchResult && (
					<Group mt="md" gap="lg">
						<Text size="sm" c="dimmed">
							Total: <strong>{searchResult.pagination.total}</strong> domains
						</Text>

						{selectedDomains.length > 0 && (
							<Badge color="blue" variant="light">
								{selectedDomains.length} selected
							</Badge>
						)}
					</Group>
				)}
			</Paper>

			{/* Bulk actions */}
			{selectedDomains.length > 0 && (
				<DomainBulkActions
					selectedDomains={selectedDomains}
					onBulkOperation={handleBulkOperation}
					onClearSelection={() => setSelectedDomains([])}
				/>
			)}

			{/* Error display */}
			{error && (
				<Alert
					icon={<IconAlertCircle size={16} />}
					title="Error"
					color="red"
					withCloseButton
					onClose={() => setError(null)}
				>
					{error}
				</Alert>
			)}

			{/* Loading state */}
			{loading && (
				<Paper withBorder p="xl">
					<Group justify="center">
						<Loader size="md" />
						<Text>Loading domains...</Text>
					</Group>
				</Paper>
			)}

			{/* Domain table */}
			{searchResult && !loading && (
				<DomainTable
					searchResult={searchResult}
					selectedDomains={selectedDomains}
					sort={sort}
					onDomainSelect={handleDomainSelect}
					onSelectAll={handleSelectAll}
					onSortChange={handleSortChange}
					onPaginationChange={handlePaginationChange}
					onViewDetails={handleViewDetails}
				/>
			)}

			{/* Modals */}
			<Modal
				opened={filtersOpened}
				onClose={closeFilters}
				title="Domain Filters"
				size="lg"
			>
				<DomainSearchFilters
					filters={filters}
					onFiltersChange={handleFiltersChange}
					onClose={closeFilters}
				/>
			</Modal>

			<DomainDetailsModal
				opened={detailsOpened}
				onClose={closeDetails}
				domain={selectedDomain}
			/>

			<DomainExportModal
				opened={exportOpened}
				onClose={closeExport}
				filters={filters}
			/>

			<DomainImportModal
				opened={importOpened}
				onClose={closeImport}
				onImportComplete={fetchDomains}
			/>
		</Stack>
	);
}

export { DomainManagementInterface };
