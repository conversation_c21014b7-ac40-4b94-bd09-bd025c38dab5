'use client';

import {
	Modal,
	Stack,
	Group,
	Text,
	Badge,
	Paper,
	Grid,
	Tabs,
	Progress,
	Anchor,
	List,
	Table,
	Loader,
	Alert,
	ActionIcon,
	Tooltip,
	Image,
	Code,
	JsonInput,
} from '@mantine/core';
import {
	IconInfoCircle,
	IconGauge,
	IconShield,
	IconSeo,
	IconCode,
	IconHistory,
	IconNetwork,
	IconExternalLink,
	IconAlertCircle,
	IconRefresh,
} from '@tabler/icons-react';
import { format } from 'date-fns';
import { useState, useEffect } from 'react';

import type { AdminDomainAnalysis } from '@/lib/database/types';

interface DomainDetailsModalProps
{
	opened: boolean;
	onClose: () => void;
	domain: string | null;
}

function DomainDetailsModal({ opened, onClose, domain }: DomainDetailsModalProps)
{
	const [domainData, setDomainData] = useState<AdminDomainAnalysis | null>(null);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	// Fetch domain details when modal opens
	useEffect(() =>
	{
		if (opened && domain)
		{
			fetchDomainDetails();
		}
	}, [opened, domain]);

	const fetchDomainDetails = async () =>
	{
		if (!domain) return;

		setLoading(true);
		setError(null);

		try
		{
			const response = await fetch(`/api/domains/${encodeURIComponent(domain)}`);

			if (!response.ok)
			{
				throw new Error('Failed to fetch domain details');
			}

			const data: AdminDomainAnalysis = await response.json();
			setDomainData(data);
		}
		catch (err)
		{
			const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
			setError(errorMessage);
		}
		finally
		{
			setLoading(false);
		}
	};

	const getScoreColor = (score: number): string =>
	{
		if (score >= 90) return 'green';
		if (score >= 75) return 'blue';
		if (score >= 60) return 'yellow';
		if (score >= 40) return 'orange';
		return 'red';
	};

	const getStatusColor = (status: string): string =>
	{
		switch (status)
		{
			case 'completed':
				return 'green';
			case 'in_progress':
			case 'processing':
				return 'blue';
			case 'pending':
			case 'queued':
				return 'yellow';
			case 'failed':
				return 'red';
			default:
				return 'gray';
		}
	};

	const getCertField = (key: string): string | null =>
	{
		const info = domainData?.security?.certificateInfo;
		if (typeof info === 'object' && info !== null)
		{
			const value = (info as Record<string, unknown>)[key];
			return typeof value === 'string' ? value : null;
		}
		return null;
	};

	if (!domain) return null;

	return (
		<Modal
			opened={opened}
			onClose={onClose}
			title={(
				<Group>
					<Text fw={600} size="lg">
						Domain Details: {domain}
					</Text>
					<ActionIcon
						variant="subtle"
						component="a"
						href={`https://${domain}`}
						target="_blank"
						rel="noopener noreferrer"
					>
						<IconExternalLink size={16} />
					</ActionIcon>
					<Tooltip label="Refresh Data">
						<ActionIcon
							variant="subtle"
							onClick={fetchDomainDetails}
							loading={loading}
						>
							<IconRefresh size={16} />
						</ActionIcon>
					</Tooltip>
				</Group>
			)}
			size="xl"
			centered
		>
			{loading && (
				<Group justify="center" py="xl">
					<Loader size="md" />
					<Text>Loading domain details...</Text>
				</Group>
			)}

			{error && (
				<Alert
					icon={<IconAlertCircle size={16} />}
					title="Error"
					color="red"
					mb="md"
				>
					{error}
				</Alert>
			)}

			{domainData && (
				<Tabs defaultValue="overview">
					<Tabs.List>
						<Tabs.Tab value="overview" leftSection={<IconInfoCircle size={14} />}>
							Overview
						</Tabs.Tab>
						<Tabs.Tab value="performance" leftSection={<IconGauge size={14} />}>
							Performance
						</Tabs.Tab>
						<Tabs.Tab value="security" leftSection={<IconShield size={14} />}>
							Security
						</Tabs.Tab>
						<Tabs.Tab value="seo" leftSection={<IconSeo size={14} />}>
							SEO
						</Tabs.Tab>
						<Tabs.Tab value="technical" leftSection={<IconCode size={14} />}>
							Technical
						</Tabs.Tab>
						<Tabs.Tab value="relationships" leftSection={<IconNetwork size={14} />}>
							Relationships
						</Tabs.Tab>
					</Tabs.List>

					{/* Overview Tab */}
					<Tabs.Panel value="overview" pt="md">
						<Stack gap="md">
							{/* Basic Info */}
							<Paper withBorder p="md">
								<Text fw={600} mb="md">Basic Information</Text>
								<Grid>
									<Grid.Col span={6}>
										<Stack gap="xs">
											<Group justify="space-between">
												<Text size="sm" c="dimmed">Domain:</Text>
												<Text size="sm" fw={500}>{domainData.domain}</Text>
											</Group>
											<Group justify="space-between">
												<Text size="sm" c="dimmed">Global Rank:</Text>
												<Text size="sm" fw={500}>
													{domainData.globalRank ? `#${domainData.globalRank.toLocaleString()}` : 'N/A'}
												</Text>
											</Group>
											<Group justify="space-between">
												<Text size="sm" c="dimmed">Category Rank:</Text>
												<Text size="sm" fw={500}>
													{domainData.categoryRank ? `#${domainData.categoryRank.toLocaleString()}` : 'N/A'}
												</Text>
											</Group>
											<Group justify="space-between">
												<Text size="sm" c="dimmed">Category:</Text>
												<Badge variant="light">{domainData.category}</Badge>
											</Group>
										</Stack>
									</Grid.Col>
									<Grid.Col span={6}>
										<Stack gap="xs">
											<Group justify="space-between">
												<Text size="sm" c="dimmed">Overall Score:</Text>
												<Group gap="xs">
													<Progress
														value={domainData.overallScore}
														size="sm"
														color={getScoreColor(domainData.overallScore)}
														style={{ width: 80 }}
													/>
													<Text size="sm" fw={500}>
														{domainData.overallScore.toFixed(1)}
													</Text>
												</Group>
											</Group>
											<Group justify="space-between">
												<Text size="sm" c="dimmed">Crawl Status:</Text>
												<Badge color={getStatusColor(domainData.crawlStatus)} variant="light">
													{domainData.crawlStatus.replace('_', ' ')}
												</Badge>
											</Group>
											<Group justify="space-between">
												<Text size="sm" c="dimmed">Seeder Status:</Text>
												<Badge color={getStatusColor(domainData.seederStatus)} variant="light">
													{domainData.seederStatus}
												</Badge>
											</Group>
											<Group justify="space-between">
												<Text size="sm" c="dimmed">Visibility:</Text>
												<Badge color={domainData.isPublic ? 'green' : 'gray'} variant="light">
													{domainData.isPublic ? 'Public' : 'Private'}
												</Badge>
											</Group>
										</Stack>
									</Grid.Col>
								</Grid>
							</Paper>

							{/* Domain Info */}
							<Paper withBorder p="md">
								<Text fw={600} mb="md">Domain Registration</Text>
								<Grid>
									<Grid.Col span={6}>
										<Stack gap="xs">
											<Group justify="space-between">
												<Text size="sm" c="dimmed">Age:</Text>
												<Text size="sm" fw={500}>
													{Math.floor(domainData.domainInfo.age / 365)} years
												</Text>
											</Group>
											<Group justify="space-between">
												<Text size="sm" c="dimmed">Registered:</Text>
												<Text size="sm" fw={500}>
													{domainData.domainInfo.registrationDate
														? format(new Date(domainData.domainInfo.registrationDate), 'MMM dd, yyyy')
														: 'N/A'}
												</Text>
											</Group>
										</Stack>
									</Grid.Col>
									<Grid.Col span={6}>
										<Stack gap="xs">
											<Group justify="space-between">
												<Text size="sm" c="dimmed">Expires:</Text>
												<Text size="sm" fw={500}>
													{domainData.domainInfo.expirationDate
														? format(new Date(domainData.domainInfo.expirationDate), 'MMM dd, yyyy')
														: 'N/A'}
												</Text>
											</Group>
											<Group justify="space-between">
												<Text size="sm" c="dimmed">Registrar:</Text>
												<Text size="sm" fw={500}>{domainData.domainInfo.registrar}</Text>
											</Group>
										</Stack>
									</Grid.Col>
								</Grid>
							</Paper>

							{/* Score Breakdown */}
							<Paper withBorder p="md">
								<Text fw={600} mb="md">Score Breakdown</Text>
								<Stack gap="md">
									<Group justify="space-between">
										<Text size="sm">Performance</Text>
										<Group gap="xs">
											<Progress
												value={domainData.performance.score}
												size="sm"
												color="blue"
												style={{ width: 120 }}
											/>
											<Text size="sm" fw={500} style={{ minWidth: 35 }}>
												{domainData.performance.score.toFixed(1)}
											</Text>
										</Group>
									</Group>
									<Group justify="space-between">
										<Text size="sm">Security</Text>
										<Group gap="xs">
											<Progress
												value={domainData.security.score}
												size="sm"
												color="green"
												style={{ width: 120 }}
											/>
											<Text size="sm" fw={500} style={{ minWidth: 35 }}>
												{domainData.security.score.toFixed(1)}
											</Text>
										</Group>
									</Group>
									<Group justify="space-between">
										<Text size="sm">SEO</Text>
										<Group gap="xs">
											<Progress
												value={domainData.seo.score}
												size="sm"
												color="orange"
												style={{ width: 120 }}
											/>
											<Text size="sm" fw={500} style={{ minWidth: 35 }}>
												{domainData.seo.score.toFixed(1)}
											</Text>
										</Group>
									</Group>
									<Group justify="space-between">
										<Text size="sm">Technical</Text>
										<Group gap="xs">
											<Progress
												value={domainData.technical.score}
												size="sm"
												color="purple"
												style={{ width: 120 }}
											/>
											<Text size="sm" fw={500} style={{ minWidth: 35 }}>
												{domainData.technical.score.toFixed(1)}
											</Text>
										</Group>
									</Group>
								</Stack>
							</Paper>

							{/* Screenshots */}
							{domainData.screenshots && domainData.screenshots.length > 0 && (
								<Paper withBorder p="md">
									<Text fw={600} mb="md">Screenshots</Text>
									<Group>
										{domainData.screenshots.map((screenshot, index) => (
											<Image
												key={index}
												src={screenshot}
												alt={`Screenshot ${index + 1}`}
												width={200}
												height={150}
												fit="cover"
												radius="md"
											/>
										))}
									</Group>
								</Paper>
							)}
						</Stack>
					</Tabs.Panel>

					{/* Performance Tab */}
					<Tabs.Panel value="performance" pt="md">
						<Paper withBorder p="md">
							<Text fw={600} mb="md">Performance Metrics</Text>
							<Grid>
								<Grid.Col span={6}>
									<Stack gap="md">
										<Group justify="space-between">
											<Text size="sm">Load Time:</Text>
											<Text size="sm" fw={500}>{domainData.performance.loadTime}ms</Text>
										</Group>
										<Group justify="space-between">
											<Text size="sm">First Contentful Paint:</Text>
											<Text size="sm" fw={500}>{domainData.performance.firstContentfulPaint}ms</Text>
										</Group>
										<Group justify="space-between">
											<Text size="sm">Largest Contentful Paint:</Text>
											<Text size="sm" fw={500}>{domainData.performance.largestContentfulPaint}ms</Text>
										</Group>
									</Stack>
								</Grid.Col>
								<Grid.Col span={6}>
									<Stack gap="md">
										<Group justify="space-between">
											<Text size="sm">Cumulative Layout Shift:</Text>
											<Text size="sm" fw={500}>{domainData.performance.cumulativeLayoutShift}</Text>
										</Group>
										<Group justify="space-between">
											<Text size="sm">First Input Delay:</Text>
											<Text size="sm" fw={500}>{domainData.performance.firstInputDelay}ms</Text>
										</Group>
										<Group justify="space-between">
											<Text size="sm">Speed Index:</Text>
											<Text size="sm" fw={500}>{domainData.performance.speedIndex}</Text>
										</Group>
									</Stack>
								</Grid.Col>
							</Grid>
						</Paper>
					</Tabs.Panel>

					{/* Security Tab */}
					<Tabs.Panel value="security" pt="md">
						<Stack gap="md">
							<Paper withBorder p="md">
								<Text fw={600} mb="md">SSL Certificate</Text>
								<Grid>
									<Grid.Col span={6}>
										<Stack gap="xs">
											<Group justify="space-between">
												<Text size="sm" c="dimmed">SSL Grade:</Text>
												<Badge color={domainData.security.sslGrade === 'A+' ? 'green' : 'blue'}>
													{domainData.security.sslGrade}
												</Badge>
											</Group>
											<Group justify="space-between">
												<Text size="sm" c="dimmed">Issuer:</Text>
												<Text size="sm" fw={500}>
													{getCertField('issuer') || 'N/A'}
												</Text>
											</Group>
										</Stack>
									</Grid.Col>
									<Grid.Col span={6}>
										<Stack gap="xs">
											<Group justify="space-between">
												<Text size="sm" c="dimmed">Valid From:</Text>
												<Text size="sm" fw={500}>
													{getCertField('validFrom') || 'N/A'}
												</Text>
											</Group>
											<Group justify="space-between">
												<Text size="sm" c="dimmed">Valid To:</Text>
												<Text size="sm" fw={500}>
													{getCertField('validTo') || 'N/A'}
												</Text>
											</Group>
										</Stack>
									</Grid.Col>
								</Grid>
							</Paper>

							<Paper withBorder p="md">
								<Text fw={600} mb="md">Security Headers</Text>
								<Code block>
									{JSON.stringify(domainData.security.securityHeaders, null, 2)}
								</Code>
							</Paper>

							{domainData.security.vulnerabilities.length > 0 && (
								<Paper withBorder p="md">
									<Text fw={600} mb="md">Vulnerabilities</Text>
									<List>
										{domainData.security.vulnerabilities.map((vuln, index) => (
											<List.Item key={index}>{vuln}</List.Item>
										))}
									</List>
								</Paper>
							)}
						</Stack>
					</Tabs.Panel>

					{/* SEO Tab */}
					<Tabs.Panel value="seo" pt="md">
						<Stack gap="md">
							<Paper withBorder p="md">
								<Text fw={600} mb="md">Meta Tags</Text>
								<Stack gap="xs">
									<Group justify="space-between">
										<Text size="sm" c="dimmed">Title:</Text>
										<Text size="sm" fw={500}>{domainData.seo.metaTags.title}</Text>
									</Group>
									<Group justify="space-between">
										<Text size="sm" c="dimmed">Description:</Text>
										<Text size="sm" fw={500}>{domainData.seo.metaTags.description}</Text>
									</Group>
								</Stack>
							</Paper>

							<Paper withBorder p="md">
								<Text fw={600} mb="md">Structured Data</Text>
								<Code block>
									{JSON.stringify(domainData.seo.structuredData, null, 2)}
								</Code>
							</Paper>

							<Paper withBorder p="md">
								<Text fw={600} mb="md">Sitemap & Robots</Text>
								<Grid>
									<Grid.Col span={6}>
										<Text size="sm" fw={500} mb="xs">Sitemap</Text>
										<Code block>
											{JSON.stringify(domainData.seo.sitemap, null, 2)}
										</Code>
									</Grid.Col>
									<Grid.Col span={6}>
										<Text size="sm" fw={500} mb="xs">Robots.txt</Text>
										<Code block>
											{JSON.stringify(domainData.seo.robotsTxt, null, 2)}
										</Code>
									</Grid.Col>
								</Grid>
							</Paper>
						</Stack>
					</Tabs.Panel>

					{/* Technical Tab */}
					<Tabs.Panel value="technical" pt="md">
						<Stack gap="md">
							<Paper withBorder p="md">
								<Text fw={600} mb="md">Technologies</Text>
								<Group>
									{domainData.technical.technologies.map((tech, index) => (
										<Badge key={index} variant="light">
											{tech}
										</Badge>
									))}
								</Group>
							</Paper>

							<Paper withBorder p="md">
								<Text fw={600} mb="md">Server Information</Text>
								<Code block>
									{JSON.stringify(domainData.technical.serverInfo, null, 2)}
								</Code>
							</Paper>

							<Paper withBorder p="md">
								<Text fw={600} mb="md">Resource Count</Text>
								<Grid>
									{Object.entries(domainData.technical.resourceCount).map(([type, count]) => (
										<Grid.Col span={3} key={type}>
											<Group justify="space-between">
												<Text size="sm" c="dimmed" tt="capitalize">{type}:</Text>
												<Text size="sm" fw={500}>{count}</Text>
											</Group>
										</Grid.Col>
									))}
								</Grid>
							</Paper>

							<Paper withBorder p="md">
								<Text fw={600} mb="md">DNS Records</Text>
								<Code block>
									{JSON.stringify(domainData.domainInfo.dnsRecords, null, 2)}
								</Code>
							</Paper>
						</Stack>
					</Tabs.Panel>

					{/* Relationships Tab */}
					<Tabs.Panel value="relationships" pt="md">
						<Paper withBorder p="md">
							<Text fw={600} mb="md">Subdomains</Text>
							{domainData.subdomains.length > 0 ? (
								<List>
									{domainData.subdomains.map((subdomain, index) => (
										<List.Item key={index}>
											<Anchor
												href={`https://${subdomain}`}
												target="_blank"
												rel="noopener noreferrer"
											>
												{subdomain}
											</Anchor>
										</List.Item>
									))}
								</List>
							) : (
								<Text size="sm" c="dimmed">No subdomains found</Text>
							)}
						</Paper>
					</Tabs.Panel>
				</Tabs>
			)}
		</Modal>
	);
}

export { DomainDetailsModal };
