'use client';

import {
	Table,
	Paper,
	Checkbox,
	Group,
	Text,
	Badge,
	ActionIcon,
	Tooltip,
	Pagination,
	Select,
	Progress,
	Anchor,
	Stack,
	UnstyledButton,
	Center,
} from '@mantine/core';
import {
	IconEye,
	IconEdit,
	IconTrash,
	IconChevronUp,
	IconChevronDown,
	IconSelector,
	IconExternalLink,
} from '@tabler/icons-react';
import { format } from 'date-fns';
import { useState } from 'react';

import type { DomainSearchResult, DomainSort, DomainListItem } from '@/types/domain';

interface DomainTableProps
{
	searchResult: DomainSearchResult;
	selectedDomains: string[];
	sort: DomainSort;
	onDomainSelect: (domain: string, selected: boolean) => void;
	onSelectAll: (selected: boolean) => void;
	onSortChange: (sort: DomainSort) => void;
	onPaginationChange: (page: number, pageSize?: number) => void;
	onViewDetails: (domain: string) => void;
}

interface SortableHeaderProps
{
	field: DomainSort['field'];
	label: string;
	currentSort: DomainSort;
	onSortChange: (sort: DomainSort) => void;
}

function SortableHeader({
	field, label, currentSort, onSortChange,
}: SortableHeaderProps)
{
	const isActive = currentSort.field === field;
	const direction = isActive ? currentSort.direction : 'asc';

	const handleClick = () =>
	{
		if (isActive)
		{
			// Toggle direction if already active
			onSortChange({
				field,
				direction: direction === 'asc' ? 'desc' : 'asc',
			});
		}
		else
		{
			// Set new field with ascending direction
			onSortChange({
				field,
				direction: 'asc',
			});
		}
	};

	return (
		<UnstyledButton onClick={handleClick}>
			<Group gap="xs" wrap="nowrap">
				<Text fw={500} size="sm">
					{label}
				</Text>
				<Center>
					{isActive ? (
						direction === 'asc' ? (
							<IconChevronUp size={14} />
						) : (
							<IconChevronDown size={14} />
						)
					) : (
						<IconSelector size={14} opacity={0.5} />
					)}
				</Center>
			</Group>
		</UnstyledButton>
	);
}

function getStatusColor(status: string): string
{
	switch (status)
	{
		case 'completed':
			return 'green';
		case 'in_progress':
		case 'processing':
			return 'blue';
		case 'pending':
		case 'queued':
			return 'yellow';
		case 'failed':
			return 'red';
		default:
			return 'gray';
	}
}

function getScoreColor(score: number): string
{
	if (score >= 90) return 'green';
	if (score >= 75) return 'blue';
	if (score >= 60) return 'yellow';
	if (score >= 40) return 'orange';
	return 'red';
}

function getSSLGradeColor(grade: string): string
{
	switch (grade)
	{
		case 'A+':
		case 'A':
			return 'green';
		case 'A-':
		case 'B':
			return 'blue';
		case 'C':
			return 'yellow';
		case 'D':
			return 'orange';
		case 'F':
			return 'red';
		default:
			return 'gray';
	}
}

function DomainTable({
	searchResult,
	selectedDomains,
	sort,
	onDomainSelect,
	onSelectAll,
	onSortChange,
	onPaginationChange,
	onViewDetails,
}: DomainTableProps)
{
	const { domains, pagination } = searchResult;
	const [pageSize, setPageSize] = useState(pagination.pageSize);

	const allSelected = domains.length > 0 && domains.every(domain => selectedDomains.includes(domain.domain));
	const indeterminate = selectedDomains.length > 0 && !allSelected;

	const handleSelectAll = (checked: boolean) =>
	{
		onSelectAll(checked);
	};

	const handlePageSizeChange = (newPageSize: string) =>
	{
		const size = parseInt(newPageSize, 10);
		setPageSize(size);
		onPaginationChange(1, size);
	};

	const rows = domains.map(domain => (
		<Table.Tr key={domain.domain}>
			<Table.Td>
				<Checkbox
					checked={selectedDomains.includes(domain.domain)}
					onChange={event => onDomainSelect(domain.domain, event.currentTarget.checked)}
				/>
			</Table.Td>

			<Table.Td>
				<Group gap="xs">
					<Anchor
						href={`https://${domain.domain}`}
						target="_blank"
						rel="noopener noreferrer"
						size="sm"
					>
						{domain.domain}
					</Anchor>
					<ActionIcon
						size="xs"
						variant="subtle"
						component="a"
						href={`https://${domain.domain}`}
						target="_blank"
						rel="noopener noreferrer"
					>
						<IconExternalLink size={12} />
					</ActionIcon>
				</Group>
			</Table.Td>

			<Table.Td>
				<Text size="sm">
					{domain.globalRank ? `#${domain.globalRank.toLocaleString()}` : 'N/A'}
				</Text>
			</Table.Td>

			<Table.Td>
				<Text size="sm">
					{domain.categoryRank ? `#${domain.categoryRank.toLocaleString()}` : 'N/A'}
				</Text>
			</Table.Td>

			<Table.Td>
				<Badge variant="light" size="sm">
					{domain.category}
				</Badge>
			</Table.Td>

			<Table.Td>
				<Group gap="xs">
					<Progress
						value={domain.overallScore}
						size="sm"
						color={getScoreColor(domain.overallScore)}
						style={{ flex: 1, minWidth: 60 }}
					/>
					<Text size="xs" c="dimmed" style={{ minWidth: 35 }}>
						{domain.overallScore.toFixed(1)}
					</Text>
				</Group>
			</Table.Td>

			<Table.Td>
				<Badge
					color={getStatusColor(domain.crawlStatus)}
					variant="light"
					size="sm"
				>
					{domain.crawlStatus.replace('_', ' ')}
				</Badge>
			</Table.Td>

			<Table.Td>
				<Badge
					color={getStatusColor(domain.seederStatus)}
					variant="light"
					size="sm"
				>
					{domain.seederStatus}
					{domain.queuePosition && ` (#${domain.queuePosition})`}
				</Badge>
			</Table.Td>

			<Table.Td>
				<Badge
					color={domain.isPublic ? 'green' : 'gray'}
					variant="light"
					size="sm"
				>
					{domain.isPublic ? 'Public' : 'Private'}
				</Badge>
			</Table.Td>

			<Table.Td>
				<Stack gap={2}>
					<Group gap="xs">
						<Text size="xs" c="dimmed">P:</Text>
						<Progress
							value={domain.performance.score}
							size="xs"
							color="blue"
							style={{ flex: 1, minWidth: 40 }}
						/>
						<Text size="xs" c="dimmed" style={{ minWidth: 25 }}>
							{domain.performance.score.toFixed(0)}
						</Text>
					</Group>
					<Group gap="xs">
						<Text size="xs" c="dimmed">S:</Text>
						<Progress
							value={domain.security.score}
							size="xs"
							color="green"
							style={{ flex: 1, minWidth: 40 }}
						/>
						<Text size="xs" c="dimmed" style={{ minWidth: 25 }}>
							{domain.security.score.toFixed(0)}
						</Text>
					</Group>
				</Stack>
			</Table.Td>

			<Table.Td>
				<Badge
					color={getSSLGradeColor(domain.security.sslGrade)}
					variant="light"
					size="sm"
				>
					{domain.security.sslGrade}
				</Badge>
			</Table.Td>

			<Table.Td>
				<Text size="sm" c="dimmed">
					{domain.lastCrawled
						? format(new Date(domain.lastCrawled), 'MMM dd, HH:mm')
						: 'Never'}
				</Text>
			</Table.Td>

			<Table.Td>
				<Group gap="xs">
					<Tooltip label="View Details">
						<ActionIcon
							variant="subtle"
							size="sm"
							onClick={() => onViewDetails(domain.domain)}
						>
							<IconEye size={16} />
						</ActionIcon>
					</Tooltip>

					<Tooltip label="Edit Domain">
						<ActionIcon
							variant="subtle"
							size="sm"
							color="blue"
						>
							<IconEdit size={16} />
						</ActionIcon>
					</Tooltip>

					<Tooltip label="Delete Domain">
						<ActionIcon
							variant="subtle"
							size="sm"
							color="red"
						>
							<IconTrash size={16} />
						</ActionIcon>
					</Tooltip>
				</Group>
			</Table.Td>
		</Table.Tr>
	));

	return (
		<Paper withBorder>
			<Table.ScrollContainer minWidth={1200}>
				<Table striped highlightOnHover>
					<Table.Thead>
						<Table.Tr>
							<Table.Th>
								<Checkbox
									checked={allSelected}
									indeterminate={indeterminate}
									onChange={event => handleSelectAll(event.currentTarget.checked)}
								/>
							</Table.Th>
							<Table.Th>
								<SortableHeader
									field="domain"
									label="Domain"
									currentSort={sort}
									onSortChange={onSortChange}
								/>
							</Table.Th>
							<Table.Th>
								<SortableHeader
									field="globalRank"
									label="Global Rank"
									currentSort={sort}
									onSortChange={onSortChange}
								/>
							</Table.Th>
							<Table.Th>
								<SortableHeader
									field="categoryRank"
									label="Category Rank"
									currentSort={sort}
									onSortChange={onSortChange}
								/>
							</Table.Th>
							<Table.Th>Category</Table.Th>
							<Table.Th>
								<SortableHeader
									field="overallScore"
									label="Overall Score"
									currentSort={sort}
									onSortChange={onSortChange}
								/>
							</Table.Th>
							<Table.Th>Crawl Status</Table.Th>
							<Table.Th>Seeder Status</Table.Th>
							<Table.Th>Visibility</Table.Th>
							<Table.Th>Scores</Table.Th>
							<Table.Th>SSL Grade</Table.Th>
							<Table.Th>
								<SortableHeader
									field="lastCrawled"
									label="Last Crawled"
									currentSort={sort}
									onSortChange={onSortChange}
								/>
							</Table.Th>
							<Table.Th>Actions</Table.Th>
						</Table.Tr>
					</Table.Thead>
					<Table.Tbody>{rows}</Table.Tbody>
				</Table>
			</Table.ScrollContainer>

			{/* Pagination */}
			<Group justify="space-between" p="md">
				<Group>
					<Text size="sm" c="dimmed">
						Showing {((pagination.page - 1) * pagination.pageSize) + 1} to{' '}
						{Math.min(pagination.page * pagination.pageSize, pagination.total)} of{' '}
						{pagination.total} domains
					</Text>

					<Select
						value={pageSize.toString()}
						onChange={value => value && handlePageSizeChange(value)}
						data={[
							{ value: '10', label: '10 per page' },
							{ value: '25', label: '25 per page' },
							{ value: '50', label: '50 per page' },
							{ value: '100', label: '100 per page' },
						]}
						size="sm"
						style={{ width: 120 }}
					/>
				</Group>

				<Pagination
					value={pagination.page}
					onChange={page => onPaginationChange(page)}
					total={Math.ceil(pagination.total / pagination.pageSize)}
					size="sm"
				/>
			</Group>
		</Paper>
	);
}

export { DomainTable };
