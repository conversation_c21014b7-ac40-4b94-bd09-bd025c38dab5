'use client';

import {
	<PERSON><PERSON>,
	<PERSON>ack,
	Group,
	Button,
	Select,
	MultiSelect,
	Switch,
	Text,
	Paper,
	Progress,
	Alert,
	Divider,
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import {
	IconDownload,
	IconFileText,
	IconFileSpreadsheet,
	IconAlertCircle,
	IconCheck,
} from '@tabler/icons-react';
import { useState } from 'react';

import type { DomainFilters, DomainExportFormat, DomainExportRequest } from '@/types/domain';

interface DomainExportModalProps
{
	opened: boolean;
	onClose: () => void;
	filters: Partial<DomainFilters>;
}

function DomainExportModal({ opened, onClose, filters }: DomainExportModalProps)
{
	const [exportFormat, setExportFormat] = useState<DomainExportFormat>('csv');
	const [selectedFields, setSelectedFields] = useState<string[]>([
		'domain',
		'globalRank',
		'categoryRank',
		'category',
		'overallScore',
		'crawlStatus',
		'isPublic',
	]);
	const [includeAnalysisData, setIncludeAnalysisData] = useState(false);
	const [includeHistory, setIncludeHistory] = useState(false);
	const [exporting, setExporting] = useState(false);
	const [exportProgress, setExportProgress] = useState(0);

	const formatOptions = [
		{
			value: 'csv',
			label: 'CSV (Comma Separated Values)',
			icon: IconFileText,
			description: 'Best for spreadsheet applications like Excel',
		},
		{
			value: 'json',
			label: 'JSON (JavaScript Object Notation)',
			icon: IconFileText,
			description: 'Best for programmatic use and data processing',
		},
		{
			value: 'excel',
			label: 'Excel Workbook (.xlsx)',
			icon: IconFileSpreadsheet,
			description: 'Native Excel format with multiple sheets',
		},
	];

	const fieldOptions = [
		{ value: 'domain', label: 'Domain Name', group: 'Basic' },
		{ value: 'globalRank', label: 'Global Rank', group: 'Basic' },
		{ value: 'categoryRank', label: 'Category Rank', group: 'Basic' },
		{ value: 'category', label: 'Category', group: 'Basic' },
		{ value: 'overallScore', label: 'Overall Score', group: 'Basic' },
		{ value: 'crawlStatus', label: 'Crawl Status', group: 'Basic' },
		{ value: 'seederStatus', label: 'Seeder Status', group: 'Basic' },
		{ value: 'isPublic', label: 'Visibility (Public/Private)', group: 'Basic' },
		{ value: 'lastCrawled', label: 'Last Crawled Date', group: 'Basic' },
		{ value: 'createdAt', label: 'Created Date', group: 'Basic' },
		{ value: 'updatedAt', label: 'Updated Date', group: 'Basic' },

		{ value: 'performance.score', label: 'Performance Score', group: 'Performance' },
		{ value: 'performance.loadTime', label: 'Load Time', group: 'Performance' },
		{ value: 'performance.firstContentfulPaint', label: 'First Contentful Paint', group: 'Performance' },
		{ value: 'performance.largestContentfulPaint', label: 'Largest Contentful Paint', group: 'Performance' },
		{ value: 'performance.cumulativeLayoutShift', label: 'Cumulative Layout Shift', group: 'Performance' },
		{ value: 'performance.firstInputDelay', label: 'First Input Delay', group: 'Performance' },
		{ value: 'performance.speedIndex', label: 'Speed Index', group: 'Performance' },

		{ value: 'security.score', label: 'Security Score', group: 'Security' },
		{ value: 'security.sslGrade', label: 'SSL Grade', group: 'Security' },

		{ value: 'seo.score', label: 'SEO Score', group: 'SEO' },
		{ value: 'seo.metaTags.title', label: 'Meta Title', group: 'SEO' },
		{ value: 'seo.metaTags.description', label: 'Meta Description', group: 'SEO' },

		{ value: 'technical.score', label: 'Technical Score', group: 'Technical' },
		{ value: 'technical.pageSize', label: 'Page Size', group: 'Technical' },
		{ value: 'technical.technologies', label: 'Technologies', group: 'Technical' },

		{ value: 'domainInfo.age', label: 'Domain Age', group: 'Domain Info' },
		{ value: 'domainInfo.registrationDate', label: 'Registration Date', group: 'Domain Info' },
		{ value: 'domainInfo.expirationDate', label: 'Expiration Date', group: 'Domain Info' },
		{ value: 'domainInfo.registrar', label: 'Registrar', group: 'Domain Info' },
	];

	const handleExport = async () =>
	{
		if (selectedFields.length === 0)
		{
			notifications.show({
				title: 'Error',
				message: 'Please select at least one field to export',
				color: 'red',
				icon: <IconAlertCircle size={16} />,
			});
			return;
		}

		setExporting(true);
		setExportProgress(0);

		try
		{
			const exportRequest: DomainExportRequest = {
				format: exportFormat,
				filters,
				fields: selectedFields,
				includeAnalysisData,
				includeHistory,
			};

			// Simulate progress updates
			const progressInterval = setInterval(() =>
			{
				setExportProgress((prev) =>
				{
					if (prev >= 90) return prev;
					return prev + Math.random() * 20;
				});
			}, 500);

			const response = await fetch('/api/domains/export', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify(exportRequest),
			});

			clearInterval(progressInterval);
			setExportProgress(100);

			if (!response.ok)
			{
				throw new Error('Export failed');
			}

			const result = await response.json();

			if (result.success)
			{
				// Trigger download
				const link = document.createElement('a');
				link.href = result.fileUrl;
				link.download = result.fileName;
				document.body.appendChild(link);
				link.click();
				document.body.removeChild(link);

				notifications.show({
					title: 'Export Successful',
					message: `${result.recordCount} domains exported successfully`,
					color: 'green',
					icon: <IconCheck size={16} />,
				});

				onClose();
			}
			else
			{
				throw new Error(result.error || 'Export failed');
			}
		}
		catch (error)
		{
			const errorMessage = error instanceof Error ? error.message : 'Export failed';
			notifications.show({
				title: 'Export Failed',
				message: errorMessage,
				color: 'red',
				icon: <IconAlertCircle size={16} />,
			});
		}
		finally
		{
			setExporting(false);
			setExportProgress(0);
		}
	};

	const handleClose = () =>
	{
		if (!exporting)
		{
			onClose();
		}
	};

	const selectedFormat = formatOptions.find(f => f.value === exportFormat);

	return (
		<Modal
			opened={opened}
			onClose={handleClose}
			title="Export Domains"
			size="lg"
			centered
			closeOnClickOutside={!exporting}
			closeOnEscape={!exporting}
		>
			<Stack gap="md">
				{/* Export Format Selection */}
				<Stack gap="xs">
					<Text fw={500}>Export Format</Text>
					<Select
						data={formatOptions.map(f => ({
							value: f.value,
							label: f.label,
						}))}
						value={exportFormat}
						onChange={value => value && setExportFormat(value as DomainExportFormat)}
						disabled={exporting}
					/>
					{selectedFormat && (
						<Text size="sm" c="dimmed">
							{selectedFormat.description}
						</Text>
					)}
				</Stack>

				<Divider />

				{/* Field Selection */}
				<Stack gap="xs">
					<Group justify="space-between">
						<Text fw={500}>Fields to Export</Text>
						<Group gap="xs">
							<Button
								size="xs"
								variant="light"
								onClick={() => setSelectedFields(fieldOptions.map(f => f.value))}
								disabled={exporting}
							>
								Select All
							</Button>
							<Button
								size="xs"
								variant="light"
								onClick={() => setSelectedFields([])}
								disabled={exporting}
							>
								Clear All
							</Button>
						</Group>
					</Group>

					<MultiSelect
						data={fieldOptions}
						value={selectedFields}
						onChange={setSelectedFields}
						placeholder="Select fields to include in export..."
						searchable
						clearable
						disabled={exporting}
						maxDropdownHeight={300}
					/>

					<Text size="sm" c="dimmed">
						Selected {selectedFields.length} of {fieldOptions.length} available fields
					</Text>
				</Stack>

				<Divider />

				{/* Additional Options */}
				<Stack gap="md">
					<Text fw={500}>Additional Options</Text>

					<Switch
						label="Include detailed analysis data"
						description="Include comprehensive performance, security, SEO, and technical analysis data"
						checked={includeAnalysisData}
						onChange={event => setIncludeAnalysisData(event.currentTarget.checked)}
						disabled={exporting}
					/>

					<Switch
						label="Include domain history"
						description="Include ranking changes, crawl history, and configuration changes over time"
						checked={includeHistory}
						onChange={event => setIncludeHistory(event.currentTarget.checked)}
						disabled={exporting}
					/>
				</Stack>

				{/* Current Filters Info */}
				<Paper withBorder p="sm" bg="gray.0">
					<Text size="sm" fw={500} mb="xs">Current Filters Applied:</Text>
					<Group gap="xs">
						{filters.search && (
							<Text size="xs" c="dimmed">Search: "{filters.search}"</Text>
						)}
						{filters.category && filters.category.length > 0 && (
							<Text size="xs" c="dimmed">Categories: {filters.category.length}</Text>
						)}
						{filters.crawlStatus && filters.crawlStatus.length > 0 && (
							<Text size="xs" c="dimmed">Crawl Status: {filters.crawlStatus.length}</Text>
						)}
						{filters.isPublic !== null && (
							<Text size="xs" c="dimmed">
								Visibility: {filters.isPublic ? 'Public only' : 'Private only'}
							</Text>
						)}
						{(!filters.search &&
              (!filters.category || filters.category.length === 0) &&
              (!filters.crawlStatus || filters.crawlStatus.length === 0) &&
              filters.isPublic === null) && (
							<Text size="xs" c="dimmed">No filters applied - exporting all domains</Text>
						)}
					</Group>
				</Paper>

				{/* Export Progress */}
				{exporting && (
					<Stack gap="xs">
						<Group justify="space-between">
							<Text size="sm" fw={500}>Exporting domains...</Text>
							<Text size="sm" c="dimmed">{Math.round(exportProgress)}%</Text>
						</Group>
						<Progress value={exportProgress} animated />
					</Stack>
				)}

				{/* Action Buttons */}
				<Group justify="flex-end" mt="md">
					<Button
						variant="light"
						onClick={handleClose}
						disabled={exporting}
					>
						Cancel
					</Button>
					<Button
						leftSection={<IconDownload size={16} />}
						onClick={handleExport}
						loading={exporting}
						disabled={selectedFields.length === 0}
					>
						{exporting ? 'Exporting...' : 'Export Domains'}
					</Button>
				</Group>
			</Stack>
		</Modal>
	);
}

export { DomainExportModal };
