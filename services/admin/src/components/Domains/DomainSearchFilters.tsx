'use client';

import {
	Stack,
	Group,
	TextInput,
	MultiSelect,
	Select,
	RangeSlider,
	Switch,
	Button,
	Text,
	Divider,
	Grid,
	NumberInput,
} from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import { clientLogger } from '@/lib/logger';
import { IconSearch, IconFilter, IconX } from '@tabler/icons-react';
import { useState, useEffect } from 'react';

import type { DomainFilters, DomainCategory } from '@/types/domain';

interface DomainSearchFiltersProps
{
	filters: Partial<DomainFilters>;
	onFiltersChange: (filters: Partial<DomainFilters>) => void;
	onClose: () => void;
}

function DomainSearchFilters({ filters, onFiltersChange, onClose }: DomainSearchFiltersProps)
{
	const [localFilters, setLocalFilters] = useState<Partial<DomainFilters>>(filters);
	const [categories, setCategories] = useState<DomainCategory[]>([]);
	const [loadingCategories, setLoadingCategories] = useState(false);

	// Load categories
	useEffect(() =>
	{
		const fetchCategories = async () =>
		{
			setLoadingCategories(true);
			try
			{
				const response = await fetch('/api/domains/categories?activeOnly=true');
				if (response.ok)
				{
					const data = await response.json();
					setCategories(data);
				}
			}
			catch (error)
			{
				clientLogger.error('Failed to load categories:', error);
			}
			finally
			{
				setLoadingCategories(false);
			}
		};

		fetchCategories();
	}, []);

	// Category options for MultiSelect
	const categoryOptions = categories.map(cat => ({
		value: cat.id,
		label: `${cat.name} (${cat.domainCount.toLocaleString()})`,
		group: cat.level === 'primary' ? 'Primary Categories' : 'Secondary Categories',
	}));

	// Crawl status options
	const crawlStatusOptions = [
		{ value: 'pending', label: 'Pending' },
		{ value: 'in_progress', label: 'In Progress' },
		{ value: 'completed', label: 'Completed' },
		{ value: 'failed', label: 'Failed' },
	];

	// Seeder status options
	const seederStatusOptions = [
		{ value: 'queued', label: 'Queued' },
		{ value: 'processing', label: 'Processing' },
		{ value: 'completed', label: 'Completed' },
		{ value: 'failed', label: 'Failed' },
	];

	// SSL grade options
	const sslGradeOptions = [
		{ value: 'A+', label: 'A+' },
		{ value: 'A', label: 'A' },
		{ value: 'A-', label: 'A-' },
		{ value: 'B', label: 'B' },
		{ value: 'C', label: 'C' },
		{ value: 'D', label: 'D' },
		{ value: 'F', label: 'F' },
	];

	const handleApplyFilters = () =>
	{
		onFiltersChange(localFilters);
		onClose();
	};

	const handleResetFilters = () =>
	{
		const resetFilters: Partial<DomainFilters> = {
			search: '',
			category: [],
			crawlStatus: [],
			seederStatus: [],
			isPublic: null,
			rankRange: [1, 1000000],
			scoreRange: [0, 100],
			sslGrades: [],
			performanceScoreRange: [0, 100],
			securityScoreRange: [0, 100],
			seoScoreRange: [0, 100],
			technicalScoreRange: [0, 100],
			registrationDateRange: [null, null],
			expirationDateRange: [null, null],
		};
		setLocalFilters(resetFilters);
	};

	return (
		<Stack gap="md">
			{/* Search */}
			<TextInput
				label="Search Domains"
				placeholder="Enter domain name or keyword..."
				leftSection={<IconSearch size={16} />}
				value={localFilters.search || ''}
				onChange={event => setLocalFilters(prev => ({
					...prev,
					search: event.currentTarget.value,
				}))}
			/>

			<Divider />

			{/* Categories */}
			<MultiSelect
				label="Categories"
				placeholder="Select categories..."
				data={categoryOptions}
				value={localFilters.category || []}
				onChange={value => setLocalFilters(prev => ({ ...prev, category: value }))}
				searchable
				clearable
				disabled={loadingCategories}
			/>

			{/* Status filters */}
			<Grid>
				<Grid.Col span={6}>
					<MultiSelect
						label="Crawl Status"
						placeholder="Select crawl status..."
						data={crawlStatusOptions}
						value={localFilters.crawlStatus || []}
						onChange={value => setLocalFilters(prev => ({ ...prev, crawlStatus: value }))}
						clearable
					/>
				</Grid.Col>

				<Grid.Col span={6}>
					<MultiSelect
						label="Seeder Status"
						placeholder="Select seeder status..."
						data={seederStatusOptions}
						value={localFilters.seederStatus || []}
						onChange={value => setLocalFilters(prev => ({ ...prev, seederStatus: value }))}
						clearable
					/>
				</Grid.Col>
			</Grid>

			{/* Visibility */}
			<Group>
				<Text size="sm" fw={500}>Visibility</Text>
				<Group gap="xl">
					<Switch
						label="Public only"
						checked={localFilters.isPublic === true}
						onChange={event => setLocalFilters(prev => ({
							...prev,
							isPublic: event.currentTarget.checked ? true : null,
						}))}
					/>
					<Switch
						label="Private only"
						checked={localFilters.isPublic === false}
						onChange={event => setLocalFilters(prev => ({
							...prev,
							isPublic: event.currentTarget.checked ? false : null,
						}))}
					/>
				</Group>
			</Group>

			<Divider />

			{/* Rank range */}
			<Stack gap="xs">
				<Text size="sm" fw={500}>Global Rank Range</Text>
				<Group>
					<NumberInput
						placeholder="Min rank"
						value={localFilters.rankRange?.[0] || 1}
						onChange={value => setLocalFilters(prev => ({
							...prev,
							rankRange: [Number(value) || 1, prev.rankRange?.[1] || 1000000],
						}))}
						min={1}
						max={1000000}
						style={{ flex: 1 }}
					/>
					<Text c="dimmed">to</Text>
					<NumberInput
						placeholder="Max rank"
						value={localFilters.rankRange?.[1] || 1000000}
						onChange={value => setLocalFilters(prev => ({
							...prev,
							rankRange: [prev.rankRange?.[0] || 1, Number(value) || 1000000],
						}))}
						min={1}
						max={1000000}
						style={{ flex: 1 }}
					/>
				</Group>
			</Stack>

			{/* Overall score range */}
			<Stack gap="xs">
				<Text size="sm" fw={500}>Overall Score Range</Text>
				<RangeSlider
					value={localFilters.scoreRange || [0, 100]}
					onChange={value => setLocalFilters(prev => ({ ...prev, scoreRange: value }))}
					min={0}
					max={100}
					step={1}
					marks={[
						{ value: 0, label: '0' },
						{ value: 25, label: '25' },
						{ value: 50, label: '50' },
						{ value: 75, label: '75' },
						{ value: 100, label: '100' },
					]}
				/>
			</Stack>

			{/* Performance score range */}
			<Stack gap="xs">
				<Text size="sm" fw={500}>Performance Score Range</Text>
				<RangeSlider
					value={localFilters.performanceScoreRange || [0, 100]}
					onChange={value => setLocalFilters(prev => ({ ...prev, performanceScoreRange: value }))}
					min={0}
					max={100}
					step={1}
					color="blue"
				/>
			</Stack>

			{/* Security score range */}
			<Stack gap="xs">
				<Text size="sm" fw={500}>Security Score Range</Text>
				<RangeSlider
					value={localFilters.securityScoreRange || [0, 100]}
					onChange={value => setLocalFilters(prev => ({ ...prev, securityScoreRange: value }))}
					min={0}
					max={100}
					step={1}
					color="green"
				/>
			</Stack>

			{/* SEO score range */}
			<Stack gap="xs">
				<Text size="sm" fw={500}>SEO Score Range</Text>
				<RangeSlider
					value={localFilters.seoScoreRange || [0, 100]}
					onChange={value => setLocalFilters(prev => ({ ...prev, seoScoreRange: value }))}
					min={0}
					max={100}
					step={1}
					color="orange"
				/>
			</Stack>

			{/* Technical score range */}
			<Stack gap="xs">
				<Text size="sm" fw={500}>Technical Score Range</Text>
				<RangeSlider
					value={localFilters.technicalScoreRange || [0, 100]}
					onChange={value => setLocalFilters(prev => ({ ...prev, technicalScoreRange: value }))}
					min={0}
					max={100}
					step={1}
					color="purple"
				/>
			</Stack>

			{/* SSL grades */}
			<MultiSelect
				label="SSL Grades"
				placeholder="Select SSL grades..."
				data={sslGradeOptions}
				value={localFilters.sslGrades || []}
				onChange={value => setLocalFilters(prev => ({ ...prev, sslGrades: value }))}
				clearable
			/>

			{/* Date ranges */}
			<Grid>
				<Grid.Col span={6}>
					<Stack gap="xs">
						<Text size="sm" fw={500}>Registration Date Range</Text>
						<DatePickerInput
							placeholder="From date"
							value={localFilters.registrationDateRange?.[0]}
							onChange={value => setLocalFilters(prev => ({
								...prev,
								registrationDateRange: [value, prev.registrationDateRange?.[1] || null],
							}))}
							clearable
						/>
						<DatePickerInput
							placeholder="To date"
							value={localFilters.registrationDateRange?.[1]}
							onChange={value => setLocalFilters(prev => ({
								...prev,
								registrationDateRange: [prev.registrationDateRange?.[0] || null, value],
							}))}
							clearable
						/>
					</Stack>
				</Grid.Col>

				<Grid.Col span={6}>
					<Stack gap="xs">
						<Text size="sm" fw={500}>Expiration Date Range</Text>
						<DatePickerInput
							placeholder="From date"
							value={localFilters.expirationDateRange?.[0]}
							onChange={value => setLocalFilters(prev => ({
								...prev,
								expirationDateRange: [value, prev.expirationDateRange?.[1] || null],
							}))}
							clearable
						/>
						<DatePickerInput
							placeholder="To date"
							value={localFilters.expirationDateRange?.[1]}
							onChange={value => setLocalFilters(prev => ({
								...prev,
								expirationDateRange: [prev.expirationDateRange?.[0] || null, value],
							}))}
							clearable
						/>
					</Stack>
				</Grid.Col>
			</Grid>

			<Divider />

			{/* Action buttons */}
			<Group justify="space-between">
				<Button
					variant="light"
					leftSection={<IconX size={16} />}
					onClick={handleResetFilters}
				>
					Reset Filters
				</Button>

				<Group>
					<Button variant="light" onClick={onClose}>
						Cancel
					</Button>
					<Button
						leftSection={<IconFilter size={16} />}
						onClick={handleApplyFilters}
					>
						Apply Filters
					</Button>
				</Group>
			</Group>
		</Stack>
	);
}

export { DomainSearchFilters };
