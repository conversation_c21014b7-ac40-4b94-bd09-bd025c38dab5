'use client';

import {
	<PERSON><PERSON>,
	<PERSON><PERSON>,
	Stack,
	Text,
	Group,
	Code,
	Collapse,
	ActionIcon,
	Box,
	Container,
	Title,
	Paper,
} from '@mantine/core';
import {
	IconAlertTriangle,
	IconRefresh,
	IconChevronDown,
	IconChevronUp,
	IconBug,
	IconHome,
	IconReportAnalytics,
} from '@tabler/icons-react';
import { Component, type ReactNode } from 'react';
import { clientLogger } from '@/lib/logger';
import { useState } from 'react';

import type {
	ErrorBoundaryProps, ErrorBoundaryState, ErrorInfo, ErrorFallbackProps,
} from './types';

// Generate unique error ID
function generateErrorId(): string
{
	return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// Default error fallback component
function DefaultErrorFallback({
	error,
	errorInfo,
	retry,
	showDetails = false,
	showStack = false,
	enableRetry = true,
	retryText = 'Try Again',
	level = 'component',
	className,
	style,
}: ErrorFallbackProps)
{
	const [detailsOpened, setDetailsOpened] = useState(false);
	const [stackOpened, setStackOpened] = useState(false);

	const getTitle = () =>
	{
		switch (level)
		{
			case 'page':
				return 'Page Error';
			case 'section':
				return 'Section Error';
			default:
				return 'Component Error';
		}
	};

	const getDescription = () =>
	{
		switch (level)
		{
			case 'page':
				return 'An error occurred while loading this page. Please try refreshing or contact support if the problem persists.';
			case 'section':
				return 'An error occurred in this section. You can try reloading this section or continue using other parts of the application.';
			default:
				return 'An error occurred in this component. You can try reloading or continue using the application.';
		}
	};

	const getIcon = () =>
	{
		switch (level)
		{
			case 'page':
				return <IconHome size={24} />;
			case 'section':
				return <IconBug size={20} />;
			default:
				return <IconAlertTriangle size={16} />;
		}
	};

	if (level === 'page')
	{
		return (
			<Container size="sm" py="xl" className={className} style={style}>
				<Paper shadow="md" radius="md" p="xl" withBorder>
					<Stack align="center" gap="lg">
						<Box c="red">{getIcon()}</Box>
						<Stack align="center" gap="sm">
							<Title order={2} ta="center">
								{getTitle()}
							</Title>
							<Text ta="center" c="dimmed">
								{getDescription()}
							</Text>
						</Stack>

						<Group>
							{enableRetry && (
								<Button
									leftSection={<IconRefresh size={16} />}
									onClick={retry}
									variant="filled"
								>
									{retryText}
								</Button>
							)}
							<Button
								variant="light"
								leftSection={<IconHome size={16} />}
								onClick={() => window.location.href = '/'}
							>
								Go Home
							</Button>
						</Group>

						{showDetails && (
							<Stack w="100%" gap="sm">
								<Group justify="space-between">
									<Text size="sm" fw={500}>
										Error Details
									</Text>
									<ActionIcon
										variant="subtle"
										onClick={() => setDetailsOpened(!detailsOpened)}
									>
										{detailsOpened ? <IconChevronUp size={16} /> : <IconChevronDown size={16} />}
									</ActionIcon>
								</Group>
								<Collapse in={detailsOpened}>
									<Code block>{error.message}</Code>
								</Collapse>
							</Stack>
						)}

						{showStack && (
							<Stack w="100%" gap="sm">
								<Group justify="space-between">
									<Text size="sm" fw={500}>
										Stack Trace
									</Text>
									<ActionIcon
										variant="subtle"
										onClick={() => setStackOpened(!stackOpened)}
									>
										{stackOpened ? <IconChevronUp size={16} /> : <IconChevronDown size={16} />}
									</ActionIcon>
								</Group>
								<Collapse in={stackOpened}>
									<Code block style={{ fontSize: '11px', maxHeight: '200px', overflow: 'auto' }}>
										{error.stack}
									</Code>
								</Collapse>
							</Stack>
						)}
					</Stack>
				</Paper>
			</Container>
		);
	}

	return (
		<Alert
			icon={getIcon()}
			title={getTitle()}
			color="red"
			variant="light"
			className={className}
			style={style}
		>
			<Stack gap="sm">
				<Text size="sm">{getDescription()}</Text>

				{enableRetry && (
					<Group>
						<Button
							size="xs"
							variant="light"
							leftSection={<IconRefresh size={14} />}
							onClick={retry}
						>
							{retryText}
						</Button>
					</Group>
				)}

				{showDetails && (
					<Stack gap="xs">
						<Group justify="space-between">
							<Text size="xs" fw={500}>
								Error Details
							</Text>
							<ActionIcon
								size="xs"
								variant="subtle"
								onClick={() => setDetailsOpened(!detailsOpened)}
							>
								{detailsOpened ? <IconChevronUp size={12} /> : <IconChevronDown size={12} />}
							</ActionIcon>
						</Group>
						<Collapse in={detailsOpened}>
							<Code block>
								{error.message}
							</Code>
						</Collapse>
					</Stack>
				)}

				{showStack && (
					<Stack gap="xs">
						<Group justify="space-between">
							<Text size="xs" fw={500}>
								Stack Trace
							</Text>
							<ActionIcon
								size="xs"
								variant="subtle"
								onClick={() => setStackOpened(!stackOpened)}
							>
								{stackOpened ? <IconChevronUp size={12} /> : <IconChevronDown size={12} />}
							</ActionIcon>
						</Group>
						<Collapse in={stackOpened}>
							<Code block style={{ maxHeight: '150px', overflow: 'auto' }}>
								{error.stack}
							</Code>
						</Collapse>
					</Stack>
				)}
			</Stack>
		</Alert>
	);
}

class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState>
{
	private resetTimeoutId: number | null = null;

	constructor(props: ErrorBoundaryProps)
	{
		super(props);
		this.state = {
			hasError: false,
			error: null,
			errorInfo: null,
			errorId: null,
			retryCount: 0,
			showDetails: props.showDetails ?? (process.env.NODE_ENV === 'development'),
		};
	}

	static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState>
	{
		return {
			hasError: true,
			error,
			errorId: generateErrorId(),
		};
	}

	override componentDidCatch(error: Error, errorInfo: ErrorInfo)
	{
		const errorId = this.state.errorId || generateErrorId();

		this.setState({
			errorInfo,
			errorId,
		});

		// Call error handler
		this.props.onError?.(error, errorInfo, errorId);

		// Report error to external service
		if (this.props.reportError && this.props.errorReportingService)
		{
			this.props.errorReportingService(error, errorInfo, errorId);
		}

		// Log error to console in development
		if (process.env.NODE_ENV === 'development')
		{
			clientLogger.error('ErrorBoundary caught an error', { error, errorInfo });
		}
	}

	override componentDidUpdate(prevProps: ErrorBoundaryProps)
	{
		const { resetKeys, resetOnPropsChange } = this.props;
		const { hasError } = this.state;

		if (hasError && prevProps.resetKeys !== resetKeys)
		{
			if (resetKeys?.some((key, idx) => prevProps.resetKeys?.[idx] !== key))
			{
				this.resetErrorBoundary();
			}
		}

		if (hasError && resetOnPropsChange && prevProps.children !== this.props.children)
		{
			this.resetErrorBoundary();
		}
	}

	resetErrorBoundary = () =>
	{
		if (this.resetTimeoutId)
		{
			clearTimeout(this.resetTimeoutId);
		}

		this.resetTimeoutId = window.setTimeout(() =>
		{
			this.setState({
				hasError: false,
				error: null,
				errorInfo: null,
				errorId: null,
			});
		}, 0);
	};

	override render()
	{
		const { hasError, error, errorInfo } = this.state;
		const {
			children,
			fallback,
			isolate = false,
			showDetails = process.env.NODE_ENV === 'development',
			showStack = process.env.NODE_ENV === 'development',
			enableRetry = true,
			retryText = 'Try Again',
			className,
			style,
			level = 'component',
		} = this.props;

		if (hasError && error && errorInfo)
		{
			// Custom fallback
			if (typeof fallback === 'function')
			{
				return fallback(error, errorInfo, this.resetErrorBoundary);
			}

			if (fallback)
			{
				return fallback;
			}

			// Default fallback
			return (
				<DefaultErrorFallback
					error={error}
					errorInfo={errorInfo}
					retry={this.resetErrorBoundary}
					showDetails={showDetails}
					showStack={showStack}
					enableRetry={enableRetry}
					retryText={retryText}
					level={level}
					className={className}
					style={style}
				/>
			);
		}

		// Isolate errors to prevent propagation
		if (isolate)
		{
			return <div style={{ isolation: 'isolate' }}>{children}</div>;
		}

		return children;
	}
}

// HOC for wrapping components with error boundary
function withErrorBoundary<P extends object>(
	Component: React.ComponentType<P>,
	errorBoundaryProps?: Omit<ErrorBoundaryProps, 'children'>,
)
{
	const WrappedComponent = (props: P) => (
		<ErrorBoundary {...errorBoundaryProps}>
			<Component {...props} />
		</ErrorBoundary>
	);

	WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;

	return WrappedComponent;
}

export { ErrorBoundary, DefaultErrorFallback, withErrorBoundary };
