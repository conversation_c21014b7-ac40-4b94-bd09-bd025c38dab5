import type { ReactNode, CSSProperties } from 'react';

export interface ErrorInfo
{
	componentStack: string;
	errorBoundary?: string;
	errorBoundaryStack?: string;
}

export interface ErrorBoundaryState
{
	hasError: boolean;
	error: Error | null;
	errorInfo: ErrorInfo | null;
	errorId: string | null;
	retryCount: number;
	showDetails: boolean;
}

export interface ErrorBoundaryProps
{
	children: ReactNode;
	fallback?: ReactNode | ((error: Error, errorInfo: ErrorInfo, retry: () => void) => ReactNode);
	onError?: (error: Error, errorInfo: ErrorInfo, errorId: string) => void;
	isolate?: boolean;
	resetKeys?: Array<string | number>;
	resetOnPropsChange?: boolean;
	showDetails?: boolean;
	showStack?: boolean;
	enableRetry?: boolean;
	retryText?: string;
	className?: string;
	style?: CSSProperties;
	level?: 'page' | 'section' | 'component';
	reportError?: boolean;
	errorReportingService?: (error: Error, errorInfo: ErrorInfo, errorId: string) => void;
	showNotifications?: boolean;
	name?: string;
	maxRetries?: number;
}

export interface ErrorFallbackProps
{
	error: Error;
	errorInfo: ErrorInfo;
	retry: () => void;
	showDetails?: boolean;
	showStack?: boolean;
	enableRetry?: boolean;
	retryText?: string;
	level?: 'page' | 'section' | 'component';
	className?: string;
	style?: CSSProperties;
}

export interface ErrorRecoveryMechanismType
{
	type: 'retry' | 'fallback' | 'graceful_degradation';
	enabled: boolean;
	config: {
		maxAttempts?: number;
		backoffStrategy?: 'linear' | 'exponential' | 'fixed';
		baseDelay?: number;
		maxDelay?: number;
		fallbackComponent?: ReactNode;
		degradedFeatures?: string[];
	};
}

export interface ClientErrorContextType
{
	url: string;
	userAgent: string;
	timestamp: Date;
	userId?: string;
	sessionId?: string;
	componentName?: string;
	componentStack?: string;
	retryCount: number;
	previousErrors?: string[];
}

export type ErrorBoundaryPropsType = ErrorBoundaryProps;
export type ErrorBoundaryStateType = ErrorBoundaryState;
