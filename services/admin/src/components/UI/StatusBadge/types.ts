import type { MantineColor, MantineSize } from '@mantine/core';
import type { CSSProperties } from 'react';

type StatusType =
  | 'success'
  | 'error'
  | 'warning'
  | 'info'
  | 'pending'
  | 'processing'
  | 'idle'
  | 'healthy'
  | 'unhealthy'
  | 'degraded'
  | 'active'
  | 'inactive'
  | 'completed'
  | 'failed'
  | 'cancelled'
  | 'running'
  | 'paused'
;

type StatusBadgeProps =
{
	status: StatusType | string;
	label?: string;
	size?: MantineSize;
	variant?: 'filled' | 'light' | 'outline' | 'dot' | 'gradient';
	color?: MantineColor;
	animated?: boolean;
	pulse?: boolean;
	showIcon?: boolean;
	tooltip?: string;
	onClick?: () => void;
	className?: string;
	style?: CSSProperties;
	customColors?: Record<string, MantineColor>;
	customIcons?: Record<string, React.ReactNode>;
};

export type {
	StatusType,
	StatusBadgeProps,
};
