'use client';

import { <PERSON><PERSON>, Tooltip, Group } from '@mantine/core';
import {
	IconCheck,
	IconX,
	IconAlertTriangle,
	IconInfoCircle,
	IconClock,
	IconLoader,
	IconMinus,
	IconHeart,
	IconHeartBroken,
	IconAlertCircle,
	IconPlayerPlay,
	IconPlayerPause,
	IconCircleCheck,
	IconCircleX,
	IconBan,
} from '@tabler/icons-react';

import type { StatusBadgeProps, StatusType } from './types';

const defaultStatusConfig: Record<StatusType, { color: string; icon: React.ReactNode }> =
{
	success: { color: 'green', icon: <IconCheck size={12} /> },
	error: { color: 'red', icon: <IconX size={12} /> },
	warning: { color: 'yellow', icon: <IconAlertTriangle size={12} /> },
	info: { color: 'blue', icon: <IconInfoCircle size={12} /> },
	pending: { color: 'gray', icon: <IconClock size={12} /> },
	processing: { color: 'blue', icon: <IconLoader size={12} /> },
	idle: { color: 'gray', icon: <IconMinus size={12} /> },
	healthy: { color: 'green', icon: <IconHeart size={12} /> },
	unhealthy: { color: 'red', icon: <IconHeartBroken size={12} /> },
	degraded: { color: 'yellow', icon: <IconAlertCircle size={12} /> },
	active: { color: 'green', icon: <IconPlayerPlay size={12} /> },
	inactive: { color: 'gray', icon: <IconPlayerPause size={12} /> },
	completed: { color: 'green', icon: <IconCircleCheck size={12} /> },
	failed: { color: 'red', icon: <IconCircleX size={12} /> },
	cancelled: { color: 'gray', icon: <IconBan size={12} /> },
	running: { color: 'blue', icon: <IconPlayerPlay size={12} /> },
	paused: { color: 'yellow', icon: <IconPlayerPause size={12} /> },
};

function StatusBadge({
	status,
	label,
	size = 'sm',
	variant = 'light',
	color,
	showIcon = true,
	tooltip,
	onClick,
	className,
	style,
	customColors = {},
	customIcons = {},
}: StatusBadgeProps)
{
	const statusKey = status as StatusType;
	const config = defaultStatusConfig[statusKey] || { color: 'gray', icon: <IconMinus size={12} /> };

	const badgeColor = color || customColors[status] || config.color;
	const icon = customIcons[status] || config.icon;

	const displayLabel = label || status.charAt(0).toUpperCase() + status.slice(1);

	const badgeContent = (
		<Group gap={4} wrap="nowrap">
			{showIcon && <div>{icon}</div>}
			{displayLabel}
		</Group>
	);

	const badge = (
		<Badge
			color={badgeColor}
			variant={variant}
			size={size}
			onClick={onClick}
			className={className}
			style={{
				cursor: onClick ? 'pointer' : undefined,
				...style,
			}}
		>
			{badgeContent}
		</Badge>
	);

	if (tooltip)
	{
		return (
			<Tooltip label={tooltip} withArrow>
				{badge}
			</Tooltip>
		);
	}

	return badge;
}

export default StatusBadge;
