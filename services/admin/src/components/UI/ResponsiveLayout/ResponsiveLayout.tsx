'use client';

import {
	AppShell,
	Grid,
	Container,
	Box,
	Burger,
	Group,
	ActionIcon,
	Flex,
	rem,
} from '@mantine/core';
import { useDisclosure, useMediaQuery } from '@mantine/hooks';
import { IconMenu2, IconX } from '@tabler/icons-react';
import { useState, useEffect } from 'react';

import type {
	ResponsiveLayoutProps,
	ResponsiveGridProps,
	ResponsiveContainerProps,
	BreakpointProps,
} from './types';

function ResponsiveLayout({
	children,
	navbar,
	header,
	footer,
	aside,
	navbarWidth = {
		base: 300, sm: 300, md: 300, lg: 300, xl: 300,
	},
	asideWidth = {
		base: 300, sm: 300, md: 300, lg: 300, xl: 300,
	},
	headerHeight = 60,
	footerHeight = 60,
	padding = 'md',
	navbarBreakpoint = 'sm',
	asideBreakpoint = 'md',
	navbarCollapsed: controlledNavbarCollapsed,
	asideCollapsed: controlledAsideCollapsed,
	onNavbarToggle,
	onAsideToggle,
	className,
	style,
	withBorder = true,
	zIndex = 200,
}: ResponsiveLayoutProps)
{
	const [navbarOpened, { toggle: toggleNavbar, close: closeNavbar }] = useDisclosure(false);
	const [asideOpened, { toggle: toggleAside, close: closeAside }] = useDisclosure(false);

	const isMobile = useMediaQuery(`(max-width: ${navbarBreakpoint === 'xs' ? '576px' : navbarBreakpoint === 'sm' ? '768px' : navbarBreakpoint === 'md' ? '992px' : navbarBreakpoint === 'lg' ? '1200px' : '1400px'})`);
	const isTablet = useMediaQuery(`(max-width: ${asideBreakpoint === 'xs' ? '576px' : asideBreakpoint === 'sm' ? '768px' : asideBreakpoint === 'md' ? '992px' : asideBreakpoint === 'lg' ? '1200px' : '1400px'})`);

	// Handle controlled state
	const navbarCollapsed = controlledNavbarCollapsed ?? (isMobile ? !navbarOpened : false);
	const asideCollapsed = controlledAsideCollapsed ?? (isTablet ? !asideOpened : false);

	// Close mobile menus when screen size changes
	useEffect(() =>
	{
		if (!isMobile)
		{
			closeNavbar();
		}
	}, [isMobile, closeNavbar]);

	useEffect(() =>
	{
		if (!isTablet)
		{
			closeAside();
		}
	}, [isTablet, closeAside]);

	const handleNavbarToggle = () =>
	{
		if (onNavbarToggle)
		{
			onNavbarToggle(!navbarCollapsed);
		}
		else
		{
			toggleNavbar();
		}
	};

	const handleAsideToggle = () =>
	{
		if (onAsideToggle)
		{
			onAsideToggle(!asideCollapsed);
		}
		else
		{
			toggleAside();
		}
	};

	return (
		<AppShell
			header={header ? { height: headerHeight } : undefined}
			navbar={
				navbar
					? {
						width: navbarWidth,
						breakpoint: navbarBreakpoint,
						collapsed: { mobile: !navbarOpened, desktop: navbarCollapsed },
					}
					: undefined
			}
			aside={
				aside
					? {
						width: asideWidth,
						breakpoint: asideBreakpoint,
						collapsed: { mobile: !asideOpened, desktop: asideCollapsed },
					}
					: undefined
			}
			footer={footer ? { height: footerHeight } : undefined}
			padding={padding}
			withBorder={withBorder}
			zIndex={zIndex}
			className={className}
			style={style}
		>
			{header && (
				<AppShell.Header>
					<Group h="100%" px="md" justify="space-between">
						<Group>
							{navbar && (
								<Burger
									opened={navbarOpened}
									onClick={handleNavbarToggle}
									hiddenFrom={navbarBreakpoint}
									size="sm"
								/>
							)}
						</Group>
						<Box style={{ flex: 1 }}>{header}</Box>
						<Group>
							{aside && (
								<ActionIcon
									variant="subtle"
									onClick={handleAsideToggle}
									hiddenFrom={asideBreakpoint}
									size="sm"
								>
									{asideOpened ? <IconX size={16} /> : <IconMenu2 size={16} />}
								</ActionIcon>
							)}
						</Group>
					</Group>
				</AppShell.Header>
			)}

			{navbar && (
				<AppShell.Navbar p="md">
					{navbar}
				</AppShell.Navbar>
			)}

			{aside && (
				<AppShell.Aside p="md">
					{aside}
				</AppShell.Aside>
			)}

			<AppShell.Main>
				{children}
			</AppShell.Main>

			{footer && (
				<AppShell.Footer p="md">
					{footer}
				</AppShell.Footer>
			)}
		</AppShell>
	);
}

function ResponsiveGrid({
	children,
	columns = {
		base: 1, sm: 2, md: 3, lg: 4, xl: 5,
	},
	spacing = 'md',
	align = 'stretch',
	justify = 'flex-start',
	className,
	style,
}: ResponsiveGridProps)
{
	const getColumns = () =>
	{
		if (typeof columns === 'number')
		{
			return columns;
		}
		return columns;
	};

	return (
		<Grid
			columns={typeof columns === 'number' ? columns : 12}
			align={align}
			justify={justify}
			className={className}
			style={style}
			gutter={spacing}
		>
			{children}
		</Grid>
	);
}

function ResponsiveContainer({
	children,
	size = 'lg',
	fluid = false,
	className,
	style,
}: ResponsiveContainerProps)
{
	return (
		<Container
			size={size}
			fluid={fluid}
			className={className}
			style={style}
		>
			{children}
		</Container>
	);
}

function BreakpointVisibility({
	children,
	largerThan,
	smallerThan,
	hiddenFrom,
	visibleFrom,
}: BreakpointProps)
{
	const getMediaQuery = (breakpoint: string, type: 'min' | 'max') =>
	{
		const breakpoints = {
			xs: '576px',
			sm: '768px',
			md: '992px',
			lg: '1200px',
			xl: '1400px',
		};

		const size = breakpoints[breakpoint as keyof typeof breakpoints] || breakpoint;
		return `(${type}-width: ${size})`;
	};

	const shouldShow = () =>
	{
		if (largerThan)
		{
			return useMediaQuery(getMediaQuery(largerThan, 'min'));
		}

		if (smallerThan)
		{
			return useMediaQuery(getMediaQuery(smallerThan, 'max'));
		}

		if (hiddenFrom)
		{
			return !useMediaQuery(getMediaQuery(hiddenFrom, 'min'));
		}

		if (visibleFrom)
		{
			return useMediaQuery(getMediaQuery(visibleFrom, 'min'));
		}

		return true;
	};

	const isVisible = shouldShow();

	if (!isVisible)
	{
		return null;
	}

	return <>{children}</>;
}

// Responsive utilities
const useBreakpoint = () =>
{
	const isXs = useMediaQuery('(max-width: 576px)');
	const isSm = useMediaQuery('(max-width: 768px)');
	const isMd = useMediaQuery('(max-width: 992px)');
	const isLg = useMediaQuery('(max-width: 1200px)');
	const isXl = useMediaQuery('(min-width: 1400px)');

	const getCurrentBreakpoint = () =>
	{
		if (isXs) return 'xs';
		if (isSm) return 'sm';
		if (isMd) return 'md';
		if (isLg) return 'lg';
		return 'xl';
	};

	return {
		isXs,
		isSm,
		isMd,
		isLg,
		isXl,
		current: getCurrentBreakpoint(),
		isMobile: isSm,
		isTablet: isMd && !isSm,
		isDesktop: !isMd,
	};
};

// Responsive flex component
function ResponsiveFlex({
	children,
	direction = { base: 'column', sm: 'row' },
	gap = 'md',
	align = 'stretch',
	justify = 'flex-start',
	wrap = 'wrap',
	className,
	style,
}: {
	children: React.ReactNode;
	direction?: 'row' | 'column' | { base: 'row' | 'column'; sm?: 'row' | 'column'; md?: 'row' | 'column'; lg?: 'row' | 'column'; xl?: 'row' | 'column' };
	gap?: string | number;
	align?: 'stretch' | 'center' | 'flex-start' | 'flex-end';
	justify?: 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around' | 'space-evenly';
	wrap?: 'wrap' | 'nowrap' | 'wrap-reverse';
	className?: string;
	style?: React.CSSProperties;
})
{
	return (
		<Flex
			direction={direction}
			gap={gap}
			align={align}
			justify={justify}
			wrap={wrap}
			className={className}
			style={style}
		>
			{children}
		</Flex>
	);
}

export {
	ResponsiveLayout,
	ResponsiveGrid,
	ResponsiveContainer,
	BreakpointVisibility,
	ResponsiveFlex,
	useBreakpoint,
};
