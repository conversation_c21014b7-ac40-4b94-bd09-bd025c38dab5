import type { MantineSize, MantineBreakpoint } from '@mantine/core';
import type { ReactNode, CSSProperties } from 'react';

type ResponsiveLayoutProps =
{
	children: ReactNode;
	navbar?: ReactNode;
	header?: ReactNode;
	footer?: ReactNode;
	aside?: ReactNode;
	navbarWidth?: number | { base: number; sm?: number; md?: number; lg?: number; xl?: number };
	asideWidth?: number | { base: number; sm?: number; md?: number; lg?: number; xl?: number };
	headerHeight?: number;
	footerHeight?: number;
	padding?: MantineSize;
	navbarBreakpoint?: MantineBreakpoint;
	asideBreakpoint?: MantineBreakpoint;
	navbarCollapsed?: boolean;
	asideCollapsed?: boolean;
	onNavbarToggle?: (collapsed: boolean) => void;
	onAsideToggle?: (collapsed: boolean) => void;
	className?: string;
	style?: CSSProperties;
	withBorder?: boolean;
	zIndex?: number;
};

type ResponsiveGridProps =
{
	children: ReactNode;
	columns?: number | {
		base: number; xs?: number; sm?: number;
		md?: number;
		lg?: number;
		xl?: number;
	};
	spacing?: MantineSize;
	align?: 'stretch' | 'center' | 'flex-start' | 'flex-end';
	justify?: 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around' | 'space-evenly';
	className?: string;
	style?: CSSProperties;
};

type ResponsiveContainerProps =
{
	children: ReactNode;
	size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | number;
	fluid?: boolean;
	className?: string;
	style?: CSSProperties;
};

type BreakpointProps =
{
	children: ReactNode;
	largerThan?: MantineBreakpoint;
	smallerThan?: MantineBreakpoint;
	hiddenFrom?: MantineBreakpoint;
	visibleFrom?: MantineBreakpoint;
};

export type {
	ResponsiveLayoutProps,
	ResponsiveGridProps,
	ResponsiveContainerProps,
	BreakpointProps,
};
