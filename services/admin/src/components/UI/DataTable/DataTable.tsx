'use client';

import {
	Table,
	ScrollArea,
	Checkbox,
	ActionIcon,
	Group,
	Text,
	TextInput,
	Select,
	NumberInput,
	Pagination,
	Button,
	Menu,
	Tooltip,
	Box,
	Stack,
	Paper,
	Skeleton,
	Alert,
	Badge,
	Flex,
	rem,
} from '@mantine/core';
import { useMediaQuery } from '@mantine/hooks';
import {
	IconChevronUp,
	IconChevronDown,
	IconFilter,
	IconSearch,
	IconDownload,
	IconRefresh,
	IconSettings,
	IconSortAscending,
	IconSortDescending,
	IconX,
	IconAlertCircle,
} from '@tabler/icons-react';
import {
	useState, useMemo, useCallback, forwardRef, useImperativeHandle, useRef,
} from 'react';

import type { ReactNode } from 'react';
import type {
	DataTablePropsType as DataTableProps,
	DataTableRefType as DataTableRef,
	DataTableColumnType as DataTableColumn
} from './types';

const DataTable = forwardRef<DataTableRef, DataTableProps>(
	(
		{
			data,
			columns,
			loading = false,
			pagination,
			selection,
			sorting,
			filtering,
			rowKey = 'id',
			size = 'sm',
			striped = true,
			highlightOnHover = true,
			withBorder = true,
			withColumnBorders = false,
			captionSide = 'bottom',
			caption,
			emptyState,
			onRowClick,
			onRowDoubleClick,
			className,
			style,
			responsive = true,
			stickyHeader = false,
			maxHeight,
			exportable = false,
			onExport,
		},
		ref,
	) =>
	{
		const [localFilters, setLocalFilters] = useState<Record<string, any>>({});
		const [showFilters, setShowFilters] = useState(false);
		const [columnVisibility, setColumnVisibility] = useState<Record<string, boolean>>({});
		const tableRef = useRef<HTMLDivElement>(null);

		const isMobile = useMediaQuery('(max-width: 768px)');
		const isTablet = useMediaQuery('(max-width: 1024px)');

		// Get row key value
		const getRowKey = useCallback(
			(record: any, index: number): string =>
			{
				if (typeof rowKey === 'function')
				{
					return rowKey(record);
				}
				return record[rowKey] || index.toString();
			},
			[rowKey],
		);

		// Filter visible columns based on responsive settings and visibility
		const visibleColumns = useMemo(() => columns.filter((column: DataTableColumn) =>
		{
			// Check column visibility toggle
			if (columnVisibility[column.key] === false)
			{
				return false;
			}

			// Check responsive settings
			if (column.responsive && responsive)
			{
				if (isMobile && !column.responsive.includes('xs'))
				{
					return false;
				}
				if (isTablet && !isMobile && !column.responsive.includes('sm'))
				{
					return false;
				}
			}

			return true;
		}), [columns, columnVisibility, responsive, isMobile, isTablet]);

		// Apply local and external filters
		const filteredData = useMemo(() =>
		{
			let result = [...data];

			// Apply external filters
			if (filtering?.filters)
			{
				Object.entries(filtering.filters).forEach(([key, value]) =>
				{
					if (value !== undefined && value !== null && value !== '')
					{
						result = result.filter((item) =>
						{
							const itemValue = item[key];
							if (typeof value === 'string')
							{
								return String(itemValue).toLowerCase().includes(value.toLowerCase());
							}
							return itemValue === value;
						});
					}
				});
			}

			// Apply local filters
			Object.entries(localFilters).forEach(([key, value]) =>
			{
				if (value !== undefined && value !== null && value !== '')
				{
					result = result.filter((item) =>
					{
						const itemValue = item[key];
						if (typeof value === 'string')
						{
							return String(itemValue).toLowerCase().includes(value.toLowerCase());
						}
						return itemValue === value;
					});
				}
			});

			return result;
		}, [data, filtering?.filters, localFilters]);

		// Apply sorting
		const sortedData = useMemo(() =>
		{
			if (!sorting?.sortBy || !sorting?.sortOrder)
			{
				return filteredData;
			}

			return [...filteredData].sort((a, b) =>
			{
				const aValue = a[sorting.sortBy!];
				const bValue = b[sorting.sortBy!];

				if (aValue === bValue) return 0;

				const comparison = aValue < bValue ? -1 : 1;
				return sorting.sortOrder === 'desc' ? -comparison : comparison;
			});
		}, [filteredData, sorting?.sortBy, sorting?.sortOrder]);

		// Handle column sort
		const handleSort = useCallback(
			(columnKey: string) =>
			{
				if (!sorting?.onChange) return;

				const newOrder =
          sorting.sortBy === columnKey && sorting.sortOrder === 'asc' ? 'desc' : 'asc';
				sorting.onChange(columnKey, newOrder);
			},
			[sorting],
		);

		// Handle filter change
		const handleFilterChange = useCallback(
			(columnKey: string, value: any) =>
			{
				const newFilters = { ...localFilters, [columnKey]: value };
				setLocalFilters(newFilters);
				filtering?.onChange?.(newFilters);
			},
			[localFilters, filtering],
		);

		// Handle selection
		const handleRowSelection = useCallback(
			(recordKey: string, checked: boolean) =>
			{
				if (!selection?.onChange) return;

				const newSelectedKeys = checked
                    ? [...selection.selectedRowKeys, recordKey]
                    : selection.selectedRowKeys.filter((key: string) => key !== recordKey);

				const selectedRecords = sortedData.filter(record => newSelectedKeys.includes(getRowKey(record, 0)));

				selection.onChange(newSelectedKeys, selectedRecords);
			},
			[selection, sortedData, getRowKey],
		);

		// Handle select all
		const handleSelectAll = useCallback(
			(checked: boolean) =>
			{
				if (!selection?.onChange) return;

				const newSelectedKeys = checked
					? sortedData.map((record, index) => getRowKey(record, index))
					: [];

				const selectedRecords = checked ? [...sortedData] : [];
				selection.onChange(newSelectedKeys, selectedRecords);
			},
			[selection, sortedData, getRowKey],
		);

		// Export functionality
		const handleExport = useCallback(
			(format: 'csv' | 'json' | 'excel') =>
			{
				if (onExport)
				{
					onExport(sortedData, format);
				}
			},
			[onExport, sortedData],
		);

		// Imperative handle for ref
		useImperativeHandle(ref, () => ({
			scrollToTop: () =>
			{
				tableRef.current?.scrollTo({ top: 0, behavior: 'smooth' });
			},
			scrollToRow: (index: number) =>
			{
				const rowElement = tableRef.current?.querySelector(`[data-row-index="${index}"]`);
				rowElement?.scrollIntoView({ behavior: 'smooth', block: 'center' });
			},
			clearSelection: () =>
			{
				selection?.onChange?.([], []);
			},
			selectAll: () =>
			{
				handleSelectAll(true);
			},
			exportData: handleExport,
		}));

		// Render filter input for column
		const renderFilterInput = (column: DataTableColumn) =>
		{
			const value = localFilters[column.key] || '';

			switch (column.filterType)
			{
				case 'select':
					return (
						<Select
							size="xs"
							placeholder={`Filter ${column.title}`}
							data={(column.filterOptions || []).map((opt) => ({ label: opt.label, value: String(opt.value ?? '') }))}
							value={value}
							onChange={(val: string | null) => handleFilterChange(column.key, val)}
							clearable
						/>
					);

				case 'number':
					return (
						<NumberInput
							size="xs"
							placeholder={`Filter ${column.title}`}
							value={value}
							onChange={(val: number | string | null) => handleFilterChange(column.key, val)}
						/>
					);

				case 'date':
					return (
						<TextInput
							size="xs"
							type="date"
							placeholder={`Filter ${column.title}`}
							value={value}
							onChange={(e) => handleFilterChange(column.key, e.target.value)}
						/>
					);

				default:
					return (
						<TextInput
							size="xs"
							placeholder={`Filter ${column.title}`}
							value={value}
							onChange={(e) => handleFilterChange(column.key, e.target.value)}
							leftSection={<IconSearch size={14} />}
						/>
					);
			}
		};
		const someSelected = !!selection && selection.selectedRowKeys.length > 0;
		const allSelected = !!selection && sortedData.length > 0 && selection.selectedRowKeys.length === sortedData.length;

		// Render table header
		const renderHeader = () =>
		{
			return (
			<Table.Thead>
				<Table.Tr>
					{selection && (
						<Table.Th style={{ width: rem(40) }}>
							<Checkbox
								checked={allSelected}
								indeterminate={someSelected && !allSelected}
								onChange={e => handleSelectAll(e.currentTarget.checked)}
								aria-label="Select all rows"
							/>
						</Table.Th>
					)}
					{visibleColumns.map((column: DataTableColumn) => (
						<Table.Th
							key={column.key}
							style={{
								width: column.width,
								textAlign: column.align || 'left',
								position: column.fixed ? 'sticky' : undefined,
								left: column.fixed === 'left' ? 0 : undefined,
								right: column.fixed === 'right' ? 0 : undefined,
								zIndex: column.fixed ? 1 : undefined,
							}}
						>
							<Group gap="xs" wrap="nowrap">
								<Text size="sm" fw={600}>
									{column.title}
								</Text>
								{column.sortable && (
									<ActionIcon
										variant="subtle"
										size="sm"
										onClick={() => handleSort(column.key)}
										aria-label={`Sort by ${column.title}`}
									>
										{sorting?.sortBy === column.key ? (
											sorting.sortOrder === 'asc' ? (
												<IconSortAscending size={14} />
											) : (
												<IconSortDescending size={14} />
											)
										) : (
											<IconChevronUp size={14} />
										)}
									</ActionIcon>
								)}
								{column.filterable && (
									<ActionIcon
										variant="subtle"
										size="sm"
										onClick={() => setShowFilters(!showFilters)}
										aria-label={`Filter ${column.title}`}
									>
										<IconFilter size={14} />
									</ActionIcon>
								)}
							</Group>
						</Table.Th>
					))}
				</Table.Tr>
				{showFilters && (
					<Table.Tr>
						{selection && <Table.Th />}
						{visibleColumns.map((column: DataTableColumn) => (
							<Table.Th key={`filter-${column.key}`}>
								{column.filterable && renderFilterInput(column)}
							</Table.Th>
						))}
					</Table.Tr>
				)}
			</Table.Thead>
		);
	};

	// Render table body
	const renderBody = () =>
	{
		if (loading)
		{
			return (
				<Table.Tbody>
					{Array.from({ length: 5 }).map((_, index) => (
						<Table.Tr key={index}>
							{selection && (
								<Table.Td>
									<Skeleton height={20} width={20} />
								</Table.Td>
							)}
							{visibleColumns.map((column: DataTableColumn) => (
								<Table.Td key={column.key}>
									<Skeleton height={20} />
								</Table.Td>
							))}
						</Table.Tr>
					))}
				</Table.Tbody>
			);
		}

		if (sortedData.length === 0)
		{
			return (
				<Table.Tbody>
					<Table.Tr>
						<Table.Td colSpan={visibleColumns.length + (selection ? 1 : 0)}>
							{emptyState || (
								<Stack align="center" gap="md" py="xl">
									<IconAlertCircle size={48} color="gray" />
									<Text c="dimmed">No data available</Text>
								</Stack>
							)}
						</Table.Td>
					</Table.Tr>
				</Table.Tbody>
			);
		}

		return (
			<Table.Tbody>
				{sortedData.map((record, index) =>
				{
					const recordKey = getRowKey(record, index);
					const isSelected = selection?.selectedRowKeys.includes(recordKey);
					const checkboxProps = selection?.getCheckboxProps?.(record) || {};

					return (
						<Table.Tr
							key={recordKey}
							data-row-index={index}
							onClick={() => onRowClick?.(record, index)}
							onDoubleClick={() => onRowDoubleClick?.(record, index)}
							style={{
								cursor: onRowClick ? 'pointer' : undefined,
								backgroundColor: isSelected ? 'var(--mantine-color-blue-0)' : undefined,
							}}
						>
							{selection && (
								<Table.Td>
									<Checkbox
										checked={isSelected}
										onChange={e => handleRowSelection(recordKey, e.currentTarget.checked)}
										{...checkboxProps}
										aria-label={`Select row ${index + 1}`}
									/>
								</Table.Td>
							)}
							{visibleColumns.map((column: DataTableColumn) =>
							{
								const value = record[column.dataIndex];
								const renderedValue = column.render
									? column.render(value, record, index)
									: String(value || '');

								return (
									<Table.Td
										key={column.key}
										style={{
											textAlign: column.align || 'left',
											position: column.fixed ? 'sticky' : undefined,
											left: column.fixed === 'left' ? 0 : undefined,
											right: column.fixed === 'right' ? 0 : undefined,
											zIndex: column.fixed ? 1 : undefined,
										}}
									>
										{renderedValue}
									</Table.Td>
								);
							})}
						</Table.Tr>
					);
				})}
			</Table.Tbody>
		);
	};

	// Render table controls
	const renderControls = () =>
	{
		if (!exportable && !selection) return null;

		return (
			<Group justify="space-between" mb="md">
				<Group>
					{selection && selection.selectedRowKeys.length > 0 && (
						<Badge variant="light" color="blue">
							{selection.selectedRowKeys.length} selected
						</Badge>
					)}
				</Group>
				<Group>
					<Button
						variant="subtle"
						size="sm"
						leftSection={<IconRefresh size={16} />}
						onClick={() => setLocalFilters({})}
					>
						Clear Filters
					</Button>
					<Menu>
						<Menu.Target>
							<ActionIcon variant="subtle" aria-label="Table settings">
								<IconSettings size={16} />
							</ActionIcon>
						</Menu.Target>
						<Menu.Dropdown>
							<Menu.Label>Column Visibility</Menu.Label>
							{columns.map(column => (
								<Menu.Item
									key={column.key}
									onClick={() => setColumnVisibility(prev => ({
										...prev,
										[column.key]: !prev[column.key],
									}))}
								>
									<Checkbox
										checked={columnVisibility[column.key] !== false}
										onChange={() => {}}
										label={column.title}
									/>
								</Menu.Item>
							))}
						</Menu.Dropdown>
					</Menu>
					{exportable && (
						<Menu>
							<Menu.Target>
								<Button
									variant="subtle"
									size="sm"
									leftSection={<IconDownload size={16} />}
								>
									Export
								</Button>
							</Menu.Target>
							<Menu.Dropdown>
								<Menu.Item onClick={() => handleExport('csv')}>Export as CSV</Menu.Item>
								<Menu.Item onClick={() => handleExport('json')}>Export as JSON</Menu.Item>
								<Menu.Item onClick={() => handleExport('excel')}>Export as Excel</Menu.Item>
							</Menu.Dropdown>
						</Menu>
					)}
				</Group>
			</Group>
		);
	};

	return (
		<Paper className={className} style={style} withBorder={withBorder}>
			{renderControls()}
			<ScrollArea
				ref={tableRef}
				style={{ maxHeight }}
				scrollbarSize={8}
				scrollHideDelay={1000}
			>
				<Table
					striped={striped}
					highlightOnHover={highlightOnHover}
					withColumnBorders={withColumnBorders}
					stickyHeader={stickyHeader}
					captionSide={captionSide}
				>
					{caption && <Table.Caption>{caption}</Table.Caption>}
					{renderHeader()}
					{renderBody()}
				</Table>
			</ScrollArea>
			{pagination && (
				<Flex justify="center" mt="md">
					<Pagination
						total={Math.ceil(pagination.total / pagination.pageSize)}
						value={pagination.current}
						onChange={page => pagination.onChange(page, pagination.pageSize)}
						size="sm"
					/>
				</Flex>
			)}
		</Paper>
	);
},
);

DataTable.displayName = 'DataTable';

export default DataTable;
