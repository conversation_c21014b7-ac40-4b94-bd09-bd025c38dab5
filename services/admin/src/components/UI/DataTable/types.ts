import type { ReactNode, CSSProperties } from 'react';

type DataTableColumnType<T = Record<string, any>> =
{
	key: string;
	title: string;
	dataIndex: keyof T;
	width?: number | string;
	sortable?: boolean;
	filterable?: boolean;
	filterType?: 'text' | 'select' | 'date' | 'number';
	filterOptions?: Array<{ label: string; value: string | number | boolean | null }>;
	render?: (value: unknown, record: T, index: number) => ReactNode;
	align?: 'left' | 'center' | 'right';
	fixed?: 'left' | 'right';
	responsive?: Array<'xs' | 'sm' | 'md' | 'lg' | 'xl'>;
};

type DataTablePropsType<T = Record<string, any>> =
{
	data: T[];
	columns: DataTableColumnType<T>[];
	loading?: boolean;
	pagination?: {
		current: number;
		pageSize: number;
		total: number;
		showSizeChanger?: boolean;
		pageSizeOptions?: string[];
		onChange: (page: number, pageSize: number) => void;
	};
	selection?: {
		selectedRowKeys: string[];
		onChange: (selectedRowKeys: string[], selectedRows: T[]) => void;
		getCheckboxProps?: (record: T) => { disabled?: boolean };
	};
	sorting?: {
		sortBy?: string;
		sortOrder?: 'asc' | 'desc';
		onChange: (sortBy: string, sortOrder: 'asc' | 'desc') => void;
	};
	filtering?: {
		filters: Record<string, unknown>;
		onChange: (filters: Record<string, unknown>) => void;
	};
	rowKey?: string | ((record: T) => string);
	size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
	striped?: boolean;
	highlightOnHover?: boolean;
	withBorder?: boolean;
	withColumnBorders?: boolean;
	captionSide?: 'top' | 'bottom';
	caption?: ReactNode;
	emptyState?: ReactNode;
	onRowClick?: (record: T, index: number) => void;
	onRowDoubleClick?: (record: T, index: number) => void;
	className?: string;
	style?: CSSProperties;
	responsive?: boolean;
	stickyHeader?: boolean;
	maxHeight?: number | string;
	exportable?: boolean;
	onExport?: (data: T[], format: 'csv' | 'json' | 'excel') => void;
};

type DataTableRefType =
{
	scrollToTop: () => void;
	scrollToRow: (index: number) => void;
	clearSelection: () => void;
	selectAll: () => void;
	exportData: (format: 'csv' | 'json' | 'excel') => void;
};

export type {
	DataTableColumnType,
	DataTablePropsType,
	DataTableRefType,
};

// Backward-compatible aliases without the Type suffix
export type DataTableColumn<T = unknown> = DataTableColumnType<T>;
export type DataTableProps<T = unknown> = DataTablePropsType<T>;
export type DataTableRef = DataTableRefType;
