import type { MantineColorScheme, MantineTheme, MantineThemeOverride } from '@mantine/core';
import type { ReactNode, CSSProperties } from 'react';

type ThemeProviderProps =
{
	children: ReactNode;
	defaultColorScheme?: MantineColorScheme;
	forceColorScheme?: MantineColorScheme;
	theme?: MantineThemeOverride;
	withCSSVariables?: boolean;
	withGlobalStyles?: boolean;
	withNormalizeCSS?: boolean;
	emotionOptions?: Record<string, unknown>;
	cssVariablesSelector?: string;
	deduplicateCssVariables?: boolean;
};

type ThemeContextType =
{
	colorScheme: MantineColorScheme;
	setColorScheme: (scheme: MantineColorScheme) => void;
	toggleColorScheme: () => void;
	theme: MantineTheme;
	isDark: boolean;
	isLight: boolean;
	isAuto: boolean;
};

type ColorSchemeToggleProps =
{
	size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
	variant?: 'filled' | 'light' | 'outline' | 'subtle' | 'default';
	className?: string;
	style?: CSSProperties;
};

export type {
	ThemeProviderProps,
	ThemeContextType,
	ColorSchemeToggleProps,
};
