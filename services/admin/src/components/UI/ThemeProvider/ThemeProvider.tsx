'use client';

import {
	MantineProvider,
	ColorSchemeScript,
	createTheme,
	ActionIcon,
	Tooltip,
	useComputedColorScheme,
	useMantineColorScheme,
} from '@mantine/core';
import { useLocalStorage, useMediaQuery } from '@mantine/hooks';
import { IconSun, IconMoon, IconDeviceDesktop } from '@tabler/icons-react';
import {
	createContext, useContext, useEffect, useState,
} from 'react';

import type { ThemeProviderProps, ThemeContextType, ColorSchemeToggleProps } from './types';

// Custom theme configuration
const customTheme = createTheme({
	primaryColor: 'blue',
	defaultRadius: 'md',
	fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif',
	fontFamilyMonospace: 'JetBrains Mono, Consolas, Monaco, Courier New, monospace',
	headings: {
		fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif',
		fontWeight: '600',
	},
	colors: {
		// Custom color palette
		brand: [
			'#e3f2fd',
			'#bbdefb',
			'#90caf9',
			'#64b5f6',
			'#42a5f5',
			'#2196f3',
			'#1e88e5',
			'#1976d2',
			'#1565c0',
			'#0d47a1',
		],
	},
	spacing: {
		xs: '0.5rem',
		sm: '0.75rem',
		md: '1rem',
		lg: '1.5rem',
		xl: '2rem',
	},
	radius: {
		xs: '0.25rem',
		sm: '0.375rem',
		md: '0.5rem',
		lg: '0.75rem',
		xl: '1rem',
	},
	shadows: {
		xs: '0 1px 3px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.1)',
		sm: '0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06)',
		md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
		lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
		xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
	},
	components: {
		Button: {
			defaultProps: {
				radius: 'md',
			},
		},
		Card: {
			defaultProps: {
				radius: 'md',
				shadow: 'sm',
			},
		},
		Paper: {
			defaultProps: {
				radius: 'md',
			},
		},
		Modal: {
			defaultProps: {
				radius: 'md',
				shadow: 'lg',
			},
		},
		Table: {
			defaultProps: {
				striped: true,
				highlightOnHover: true,
			},
		},
	},
	breakpoints: {
		xs: '320px',
		sm: '768px',
		md: '1024px',
		lg: '1440px',
		xl: '1920px',
	},
});

// Theme context
const ThemeContext = createContext<ThemeContextType | null>(null);

function useTheme()
{
	const context = useContext(ThemeContext);
	if (!context)
	{
		throw new Error('useTheme must be used within a ThemeProvider');
	}
	return context;
}

function ThemeProvider({
	children,
	defaultColorScheme = 'auto',
	forceColorScheme,
	theme,
	withCSSVariables = true,
	// withGlobalStyles = true,
	// withNormalizeCSS = true,
	// emotionOptions,
	cssVariablesSelector = ':root',
	// deduplicateCssVariables = true,
}: ThemeProviderProps)
{
	const [colorScheme, setColorScheme] = useLocalStorage({
		key: 'admin-color-scheme',
		defaultValue: defaultColorScheme,
	});

	const computedColorScheme = useComputedColorScheme(
		colorScheme === 'dark' ? 'dark' : 'light',
		{ getInitialValueInEffect: true },
	);

	const toggleColorScheme = () =>
	{
		const schemes = ['light', 'dark', 'auto'] as const;
		const currentIndex = schemes.indexOf(colorScheme);
		const nextIndex = (currentIndex + 1) % schemes.length;
		setColorScheme(schemes[nextIndex]);
	};

	const appliedTheme = theme ? createTheme(theme) : customTheme;

	const contextValue: ThemeContextType =
	{
		colorScheme: forceColorScheme || colorScheme,
		setColorScheme,
		toggleColorScheme,
		theme: appliedTheme,
		isDark: computedColorScheme === 'dark',
		isLight: computedColorScheme === 'light',
		isAuto: colorScheme === 'auto',
	};

	return (
		<>
			<ColorSchemeScript defaultColorScheme={defaultColorScheme} />
			<ThemeContext.Provider value={contextValue}>
				<MantineProvider
					theme={appliedTheme}
					defaultColorScheme={computedColorScheme}
					forceColorScheme={
						forceColorScheme === 'dark' || forceColorScheme === 'light'
							? forceColorScheme
							: undefined
					}
					withCssVariables={withCSSVariables}
					cssVariablesSelector={cssVariablesSelector}
				>
					{children}
				</MantineProvider>
			</ThemeContext.Provider>
		</>
	);
}

function ColorSchemeToggle({
	size = 'md',
	variant = 'subtle',
	className,
	style,
}: ColorSchemeToggleProps)
{
	const { setColorScheme, colorScheme } = useMantineColorScheme();
	const computedColorScheme = useComputedColorScheme('light');
	const [mounted, setMounted] = useState(false);

	// Prevent hydration mismatch by only rendering after mount
	useEffect(() =>
	{
		setMounted(true);
	}, []);

	const toggleColorScheme = () =>
	{
		const nextScheme = colorScheme === 'light' ? 'dark' : 'light';
		setColorScheme(nextScheme);
	};

	const getIcon = () =>
	{
		if (!mounted)
		{
			// Return default icon during SSR to prevent hydration mismatch
			return <IconSun size={16} />;
		}

		if (colorScheme === 'dark')
		{
			return <IconMoon size={16} />;
		}
		return <IconSun size={16} />;
	};

	const getTooltipLabel = () =>
	{
		if (!mounted)
		{
			return 'Toggle color scheme';
		}

		if (colorScheme === 'dark')
		{
			return 'Switch to light mode';
		}
		return 'Switch to dark mode';
	};

	return (
		<Tooltip label={getTooltipLabel()} withArrow>
			<ActionIcon
				onClick={toggleColorScheme}
				variant={variant}
				size={size}
				className={className}
				style={style}
				aria-label="Toggle color scheme"
			>
				{getIcon()}
			</ActionIcon>
		</Tooltip>
	);
}

// Print styles component
function PrintStyles()
{
	useEffect(() =>
	{
		const printStyles = `
      @media print {
        /* Hide navigation and non-essential elements */
        .mantine-AppShell-navbar,
        .mantine-AppShell-aside,
        .mantine-AppShell-header,
        .mantine-AppShell-footer,
        .no-print {
          display: none !important;
        }

        /* Optimize layout for print */
        .mantine-AppShell-main {
          padding: 0 !important;
          margin: 0 !important;
        }

        /* Ensure good contrast */
        * {
          color: black !important;
          background: white !important;
        }

        /* Preserve table borders */
        .mantine-Table-table,
        .mantine-Table-th,
        .mantine-Table-td {
          border: 1px solid #000 !important;
        }

        /* Page breaks */
        .page-break {
          page-break-before: always;
        }

        .no-page-break {
          page-break-inside: avoid;
        }

        /* Font sizes */
        h1 { font-size: 18pt !important; }
        h2 { font-size: 16pt !important; }
        h3 { font-size: 14pt !important; }
        h4 { font-size: 12pt !important; }
        h5 { font-size: 11pt !important; }
        h6 { font-size: 10pt !important; }

        body, p, div, span {
          font-size: 10pt !important;
          line-height: 1.4 !important;
        }

        /* Charts and images */
        canvas, svg, img {
          max-width: 100% !important;
          height: auto !important;
        }

        /* Remove shadows and effects */
        * {
          box-shadow: none !important;
          text-shadow: none !important;
        }
      }
    `;

		const styleElement = document.createElement('style');
		styleElement.textContent = printStyles;
		document.head.appendChild(styleElement);

		return () =>
		{
			document.head.removeChild(styleElement);
		};
	}, []);

	return null;
}

export {
	ColorSchemeToggle,
	PrintStyles,
	useTheme,
	customTheme,
};

export default ThemeProvider;
