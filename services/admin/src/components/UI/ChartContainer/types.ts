import type { MantineColor, MantineSize } from '@mantine/core';
import type { ReactNode, CSSProperties } from 'react';

export interface ChartContainerProps
{
	title?: string;
	subtitle?: string;
	children: ReactNode;
	height?: number | string;
	loading?: boolean;
	error?: string;
	actions?: ReactNode;
	legend?: ReactNode;
	toolbar?: ReactNode;
	footer?: ReactNode;
	withBorder?: boolean;
	shadow?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
	radius?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
	padding?: MantineSize;
	className?: string;
	style?: CSSProperties;
	responsive?: boolean;
	exportable?: boolean;
	onExport?: (format: 'png' | 'svg' | 'pdf') => void;
	fullscreen?: boolean;
	onFullscreenToggle?: (isFullscreen: boolean) => void;
	refreshable?: boolean;
	onRefresh?: () => void;
	lastUpdated?: Date;
	autoRefresh?: boolean;
	refreshInterval?: number;
	theme?: 'light' | 'dark' | 'auto';
	colorScheme?: MantineColor[];
	interactive?: boolean;
	zoomable?: boolean;
	downloadable?: boolean;
}
