'use client';

import {
	Card,
	Group,
	Text,
	Stack,
	ActionIcon,
	Skeleton,
	Alert,
	Menu,
	Tooltip,
	Badge,
	Box,
	LoadingOverlay,
	Button,
	Modal,
} from '@mantine/core';
import { useDisclosure, useInterval } from '@mantine/hooks';
import {
	IconDownload,
	IconRefresh,
	IconMaximize,
	IconMinimize,
	IconDots,
	IconPhoto,
	IconFileTypePdf,
	IconFileTypeSvg,
	IconAlertCircle,
	IconClock,
} from '@tabler/icons-react';
import { useState, useEffect, useRef } from 'react';

import type { ChartContainerProps } from './types';

function ChartContainer({
	title,
	subtitle,
	children,
	height = 300,
	loading = false,
	error,
	actions,
	legend,
	toolbar,
	footer,
	withBorder = true,
	shadow = 'sm',
	radius = 'md',
	padding = 'md',
	className,
	style,
	responsive = true,
	exportable = false,
	onExport,
	fullscreen = false,
	onFullscreenToggle,
	refreshable = false,
	onRefresh,
	lastUpdated,
	autoRefresh = false,
	refreshInterval = 30000,
	theme = 'auto',
	colorScheme,
	interactive = true,
	zoomable = false,
	downloadable = false,
}: ChartContainerProps)
{
	const [isFullscreen, setIsFullscreen] = useState(fullscreen);
	const [isRefreshing, setIsRefreshing] = useState(false);
	const [opened, { open, close }] = useDisclosure(false);
	const chartRef = useRef<HTMLDivElement>(null);

	// Auto refresh interval
	const interval = useInterval(
		async () =>
		{
			if (onRefresh && !isRefreshing)
			{
				setIsRefreshing(true);
				try
				{
					await onRefresh();
				}
				finally
				{
					setIsRefreshing(false);
				}
			}
		},
		autoRefresh ? refreshInterval : 0,
	);

	useEffect(() =>
	{
		if (autoRefresh)
		{
			interval.start();
			return interval.stop;
		}

		return undefined;
	}, [autoRefresh, interval]);

	const handleRefresh = async () =>
	{
		if (onRefresh && !isRefreshing)
		{
			setIsRefreshing(true);
			try
			{
				await onRefresh();
			}
			finally
			{
				setIsRefreshing(false);
			}
		}
	};

	const handleFullscreenToggle = () =>
	{
		const newFullscreen = !isFullscreen;
		setIsFullscreen(newFullscreen);
		onFullscreenToggle?.(newFullscreen);
		if (newFullscreen)
		{
			open();
		}
		else
		{
			close();
		}
	};

	const handleExport = (format: 'png' | 'svg' | 'pdf') =>
	{
		onExport?.(format);
	};

	const formatLastUpdated = (date: Date) =>
	{
		const now = new Date();
		const diff = now.getTime() - date.getTime();
		const minutes = Math.floor(diff / 60000);
		const hours = Math.floor(diff / 3600000);
		const days = Math.floor(diff / 86400000);

		if (minutes < 1) return 'Just now';
		if (minutes < 60) return `${minutes}m ago`;
		if (hours < 24) return `${hours}h ago`;
		return `${days}d ago`;
	};

	const renderToolbar = () =>
	{
		if (!refreshable && !exportable && !onFullscreenToggle && !toolbar)
		{
			return null;
		}

		return (
			<Group gap="xs">
				{toolbar}
				{refreshable && (
					<Tooltip label="Refresh data">
						<ActionIcon
							variant="subtle"
							size="sm"
							onClick={handleRefresh}
							loading={isRefreshing}
							disabled={loading}
						>
							<IconRefresh size={16} />
						</ActionIcon>
					</Tooltip>
				)}
				{onFullscreenToggle && (
					<Tooltip label={isFullscreen ? 'Exit fullscreen' : 'Fullscreen'}>
						<ActionIcon
							variant="subtle"
							size="sm"
							onClick={handleFullscreenToggle}
						>
							{isFullscreen ? <IconMinimize size={16} /> : <IconMaximize size={16} />}
						</ActionIcon>
					</Tooltip>
				)}
				{exportable && (
					<Menu>
						<Menu.Target>
							<Tooltip label="Export chart">
								<ActionIcon variant="subtle" size="sm">
									<IconDownload size={16} />
								</ActionIcon>
							</Tooltip>
						</Menu.Target>
						<Menu.Dropdown>
							<Menu.Label>Export Format</Menu.Label>
							<Menu.Item
								leftSection={<IconPhoto size={16} />}
								onClick={() => handleExport('png')}
							>
								PNG Image
							</Menu.Item>
							<Menu.Item
								leftSection={<IconFileTypeSvg size={16} />}
								onClick={() => handleExport('svg')}
							>
								SVG Vector
							</Menu.Item>
							<Menu.Item
								leftSection={<IconFileTypePdf size={16} />}
								onClick={() => handleExport('pdf')}
							>
								PDF Document
							</Menu.Item>
						</Menu.Dropdown>
					</Menu>
				)}
				{actions}
			</Group>
		);
	};

	const renderHeader = () =>
	{
		if (!title && !subtitle && !renderToolbar())
		{
			return null;
		}

		return (
			<Group justify="space-between" align="flex-start" mb="md">
				<Stack gap={4}>
					{title && (
						<Text size="lg" fw={600}>
							{title}
						</Text>
					)}
					{subtitle && (
						<Text size="sm" c="dimmed">
							{subtitle}
						</Text>
					)}
					{lastUpdated && (
						<Group gap="xs">
							<IconClock size={12} />
							<Text size="xs" c="dimmed">
								Updated {formatLastUpdated(lastUpdated)}
							</Text>
							{autoRefresh && (
								<Badge size="xs" variant="dot" color="green">
									Auto-refresh
								</Badge>
							)}
						</Group>
					)}
				</Stack>
				{renderToolbar()}
			</Group>
		);
	};

	const renderContent = () =>
	{
		if (error)
		{
			return (
				<Alert
					icon={<IconAlertCircle size={16} />}
					title="Error loading chart"
					color="red"
					variant="light"
				>
					{error}
				</Alert>
			);
		}

		if (loading)
		{
			return (
				<Stack gap="md">
					<Skeleton height={20} width="60%" />
					<Skeleton height={height} />
					<Group>
						<Skeleton height={16} width={80} />
						<Skeleton height={16} width={100} />
						<Skeleton height={16} width={90} />
					</Group>
				</Stack>
			);
		}

		return (
			<Box
				ref={chartRef}
				style={{
					height: typeof height === 'number' ? `${height}px` : height,
					position: 'relative',
				}}
			>
				<LoadingOverlay visible={isRefreshing} />
				{children}
			</Box>
		);
	};

	const chartCard = (
		<Card
			withBorder={withBorder}
			shadow={shadow}
			radius={radius}
			padding={padding}
			className={className}
			style={{
				...style,
				...(responsive && {
					'@media (max-width: 768px)': {
						padding: 'sm',
					},
				}),
			}}
		>
			<Stack gap="md">
				{renderHeader()}
				{legend && <Box>{legend}</Box>}
				{renderContent()}
				{footer && (
					<Box pt="md" style={{ borderTop: '1px solid var(--mantine-color-gray-2)' }}>
						{footer}
					</Box>
				)}
			</Stack>
		</Card>
	);

	// Fullscreen modal
	if (onFullscreenToggle)
	{
		return (
			<>
				{chartCard}
				<Modal
					opened={opened}
					onClose={() =>
					{
						close();
						setIsFullscreen(false);
						onFullscreenToggle?.(false);
					}}
					size="calc(100vw - 3rem)"
					title={title}
					fullScreen
				>
					<Box style={{ height: 'calc(100vh - 8rem)' }}>
						{renderContent()}
					</Box>
				</Modal>
			</>
		);
	}

	return chartCard;
}

export default ChartContainer;
