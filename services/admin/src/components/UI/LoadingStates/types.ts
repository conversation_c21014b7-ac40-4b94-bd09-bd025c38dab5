import type { MantineSize } from '@mantine/core';
import type { ReactNode, CSSProperties } from 'react';

type LoadingStatesProps =
{
	type?: 'skeleton' | 'spinner' | 'dots' | 'bars' | 'oval' | 'progress';
	size?: MantineSize;
	color?: string;
	text?: string;
	overlay?: boolean;
	visible?: boolean;
	children?: ReactNode;
	className?: string;
	style?: CSSProperties;
	rows?: number;
	animate?: boolean;
	progress?: number;
	indeterminate?: boolean;
};

type SkeletonLoaderProps =
{
	rows?: number;
	height?: number;
	animate?: boolean;
	className?: string;
};

type SpinnerLoaderProps =
{
	size?: MantineSize;
	color?: string;
	text?: string;
	className?: string;
};

type ProgressLoaderProps =
{
	progress?: number;
	indeterminate?: boolean;
	text?: string;
	color?: string;
	className?: string;
};

export type {
	LoadingStatesProps,
	SkeletonLoaderProps,
	SpinnerLoaderProps,
	ProgressLoaderProps,
};
