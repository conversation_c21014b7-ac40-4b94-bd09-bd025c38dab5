'use client';

import {
	Skeleton,
	Loader,
	LoadingOverlay,
	Progress,
	Stack,
	Group,
	Text,
	Box,
	Center,
} from '@mantine/core';
import { Suspense } from 'react';

import type {
	LoadingStatesProps, SkeletonLoaderProps, SpinnerLoaderProps, ProgressLoaderProps,
} from './types';

function SkeletonLoader({
	rows = 3, height = 20, animate = true, className,
}: SkeletonLoaderProps)
{
	return (
		<Stack gap="sm" className={className}>
			{Array.from({ length: rows }).map((_, index) => (
				<Skeleton
					key={index}
					height={height}
					animate={animate}
					width={index === rows - 1 ? '70%' : '100%'}
				/>
			))}
		</Stack>
	);
}

function SpinnerLoader({
	size = 'md', color = 'blue', text, className,
}: SpinnerLoaderProps)
{
	return (
		<Center className={className}>
			<Stack align="center" gap="md">
				<Loader size={size} color={color} />
				{text && (
					<Text size="sm" c="dimmed">
						{text}
					</Text>
				)}
			</Stack>
		</Center>
	);
}

function ProgressLoader({
	progress = 0,
	indeterminate = false,
	text,
	color = 'blue',
	className,
}: ProgressLoaderProps)
{
	return (
		<Stack gap="sm" className={className}>
			{text && (
				<Group justify="space-between">
					<Text size="sm">{text}</Text>
					{!indeterminate && (
						<Text size="sm" c="dimmed">
							{Math.round(progress)}%
						</Text>
					)}
				</Group>
			)}
			<Progress
				value={indeterminate ? 100 : progress}
				color={color}
				animated={indeterminate}
				striped={indeterminate}
			/>
		</Stack>
	);
}

function LoadingStates({
	type = 'skeleton',
	size = 'md',
	color = 'blue',
	text,
	overlay = false,
	visible = true,
	children,
	className,
	style,
	rows = 3,
	animate = true,
	progress = 0,
	indeterminate = false,
}: LoadingStatesProps)
{
	if (!visible) return children || null;

	const renderLoader = () =>
	{
		switch (type)
		{
			case 'spinner':
				return <SpinnerLoader size={size} color={color} text={text} className={className} />;

			case 'progress':
				return (
					<ProgressLoader
						progress={progress}
						indeterminate={indeterminate}
						text={text}
						color={color}
						className={className}
					/>
				);

			case 'dots':
				return (
					<Center className={className}>
						<Stack align="center" gap="md">
							<Loader size={size} color={color} type="dots" />
							{text && (
								<Text size="sm" c="dimmed">
									{text}
								</Text>
							)}
						</Stack>
					</Center>
				);

			case 'bars':
				return (
					<Center className={className}>
						<Stack align="center" gap="md">
							<Loader size={size} color={color} type="bars" />
							{text && (
								<Text size="sm" c="dimmed">
									{text}
								</Text>
							)}
						</Stack>
					</Center>
				);

			case 'oval':
				return (
					<Center className={className}>
						<Stack align="center" gap="md">
							<Loader size={size} color={color} type="oval" />
							{text && (
								<Text size="sm" c="dimmed">
									{text}
								</Text>
							)}
						</Stack>
					</Center>
				);

			case 'skeleton':
			default:
				return <SkeletonLoader rows={rows} animate={animate} className={className} />;
		}
	};

	if (overlay && children)
	{
		return (
			<Box style={{ position: 'relative', ...style }}>
				{children}
				<LoadingOverlay visible={visible} loaderProps={{ size, color, type }} />
			</Box>
		);
	}

	return (
		<Box className={className} style={style}>
			{renderLoader()}
		</Box>
	);
}

// Suspense wrapper component
function SuspenseLoader({
	fallback,
	children,
	...props
}: LoadingStatesProps & { fallback?: React.ReactNode })
{
	const defaultFallback = fallback || <LoadingStates {...props} />;

	return <Suspense fallback={defaultFallback}>{children}</Suspense>;
}

export {
	LoadingStates, SuspenseLoader, SkeletonLoader, SpinnerLoader, ProgressLoader,
};
