'use client';

import { Box } from '@mantine/core';
import { useEffect } from 'react';

import type { PrintStylesProps, PrintableComponentProps } from './types';

function PrintStyles({
	hideElements = [
		'.mantine-AppShell-navbar',
		'.mantine-AppShell-aside',
		'.mantine-AppShell-header',
		'.mantine-AppShell-footer',
		'.no-print',
		'button:not(.print-button)',
		'.mantine-ActionIcon',
		'.mantine-Menu',
		'.mantine-Tooltip',
	],
	showElements = [],
	customStyles = '',
	pageBreaks = {},
	margins = {
		top: '1in',
		right: '0.75in',
		bottom: '1in',
		left: '0.75in',
	},
	orientation = 'portrait',
	paperSize = 'A4',
}: PrintStylesProps)
{
	useEffect(() =>
	{
		const getPaperSize = () =>
		{
			switch (paperSize)
			{
				case 'A3':
					return '297mm 420mm';
				case 'A4':
					return '210mm 297mm';
				case 'letter':
					return '8.5in 11in';
				case 'legal':
					return '8.5in 14in';
				default:
					return '210mm 297mm';
			}
		};

		const printStyles = `
      @page {
        size: ${getPaperSize()} ${orientation};
        margin: ${margins.top} ${margins.right} ${margins.bottom} ${margins.left};
      }

      @media print {
        /* Reset and base styles */
        * {
          -webkit-print-color-adjust: exact !important;
          color-adjust: exact !important;
          print-color-adjust: exact !important;
        }

        html, body {
          width: 100% !important;
          height: 100% !important;
          margin: 0 !important;
          padding: 0 !important;
          overflow: visible !important;
        }

        /* Hide elements */
        ${hideElements.map(selector => `${selector} { display: none !important; }`).join('\n        ')}

        /* Show elements */
        ${showElements.map(selector => `${selector} { display: block !important; }`).join('\n        ')}

        /* Layout adjustments */
        .mantine-AppShell-main {
          padding: 0 !important;
          margin: 0 !important;
          width: 100% !important;
          max-width: none !important;
        }

        .mantine-Container-root {
          max-width: none !important;
          padding: 0 !important;
        }

        /* Typography */
        h1 {
          font-size: 24pt !important;
          margin: 0 0 12pt 0 !important;
          page-break-after: avoid !important;
        }
        h2 {
          font-size: 20pt !important;
          margin: 12pt 0 8pt 0 !important;
          page-break-after: avoid !important;
        }
        h3 {
          font-size: 16pt !important;
          margin: 8pt 0 6pt 0 !important;
          page-break-after: avoid !important;
        }
        h4 {
          font-size: 14pt !important;
          margin: 6pt 0 4pt 0 !important;
          page-break-after: avoid !important;
        }
        h5 {
          font-size: 12pt !important;
          margin: 4pt 0 2pt 0 !important;
          page-break-after: avoid !important;
        }
        h6 {
          font-size: 11pt !important;
          margin: 4pt 0 2pt 0 !important;
          page-break-after: avoid !important;
        }

        body, p, div, span, td, th {
          font-size: 11pt !important;
          line-height: 1.4 !important;
        }

        /* Tables */
        table {
          width: 100% !important;
          border-collapse: collapse !important;
          margin: 8pt 0 !important;
          page-break-inside: auto !important;
        }

        .mantine-Table-table {
          border: 1px solid #000 !important;
        }

        .mantine-Table-th,
        .mantine-Table-td {
          border: 1px solid #000 !important;
          padding: 4pt 6pt !important;
          font-size: 10pt !important;
          line-height: 1.2 !important;
        }

        .mantine-Table-th {
          background-color: #f5f5f5 !important;
          font-weight: bold !important;
        }

        thead {
          display: table-header-group !important;
        }

        tbody {
          display: table-row-group !important;
        }

        tr {
          page-break-inside: avoid !important;
        }

        /* Cards and Papers */
        .mantine-Card-root,
        .mantine-Paper-root {
          border: 1px solid #ddd !important;
          box-shadow: none !important;
          margin: 8pt 0 !important;
          padding: 8pt !important;
          page-break-inside: avoid !important;
        }

        /* Charts and images */
        canvas, svg, img {
          max-width: 100% !important;
          height: auto !important;
          page-break-inside: avoid !important;
        }

        /* Remove interactive elements */
        button, input, select, textarea {
          display: none !important;
        }

        .print-button {
          display: inline-block !important;
        }

        /* Remove shadows and effects */
        * {
          box-shadow: none !important;
          text-shadow: none !important;
          background-image: none !important;
        }

        /* Page breaks */
        ${pageBreaks.before?.map(selector => `${selector} { page-break-before: always !important; }`).join('\n        ') || ''}
        ${pageBreaks.after?.map(selector => `${selector} { page-break-after: always !important; }`).join('\n        ') || ''}
        ${pageBreaks.avoid?.map(selector => `${selector} { page-break-inside: avoid !important; }`).join('\n        ') || ''}

        .page-break-before { page-break-before: always !important; }
        .page-break-after { page-break-after: always !important; }
        .page-break-avoid { page-break-inside: avoid !important; }
        .no-page-break { page-break-inside: avoid !important; }

        /* Print-specific utilities */
        .print-only { display: block !important; }
        .no-print { display: none !important; }

        /* Badges and status indicators */
        .mantine-Badge-root {
          border: 1px solid #000 !important;
          background: white !important;
          color: black !important;
          padding: 2pt 4pt !important;
          font-size: 9pt !important;
        }

        /* Progress bars */
        .mantine-Progress-root {
          border: 1px solid #000 !important;
          height: 8pt !important;
        }

        .mantine-Progress-bar {
          background: #000 !important;
        }

        /* Alerts */
        .mantine-Alert-root {
          border: 2px solid #000 !important;
          background: white !important;
          padding: 8pt !important;
          margin: 8pt 0 !important;
        }

        /* Code blocks */
        code, pre {
          font-family: 'Courier New', monospace !important;
          font-size: 9pt !important;
          border: 1px solid #000 !important;
          padding: 4pt !important;
          background: #f9f9f9 !important;
          page-break-inside: avoid !important;
        }

        /* Links */
        a {
          color: black !important;
          text-decoration: underline !important;
        }

        a[href]:after {
          content: " (" attr(href) ")" !important;
          font-size: 9pt !important;
          color: #666 !important;
        }

        /* Custom styles */
        ${customStyles}
      }

      /* Print preview styles (when not actually printing) */
      @media screen {
        .print-preview {
          background: white;
          box-shadow: 0 0 10px rgba(0,0,0,0.1);
          margin: 20px auto;
          padding: 1in 0.75in;
          width: 8.5in;
          min-height: 11in;
        }

        .print-preview * {
          color: black !important;
          background: white !important;
        }
      }
    `;

		const styleElement = document.createElement('style');
		styleElement.textContent = printStyles;
		styleElement.setAttribute('data-print-styles', 'true');
		document.head.appendChild(styleElement);

		return () =>
		{
			const existingStyle = document.querySelector('[data-print-styles="true"]');
			if (existingStyle)
			{
				document.head.removeChild(existingStyle);
			}
		};
	}, [hideElements, showElements, customStyles, pageBreaks, margins, orientation, paperSize]);

	return null;
}

function PrintableComponent({
	children,
	className = '',
	pageBreak = 'auto',
	printOnly = false,
	noPrint = false,
}: PrintableComponentProps)
{
	const getPageBreakClass = () =>
	{
		switch (pageBreak)
		{
			case 'before':
				return 'page-break-before';
			case 'after':
				return 'page-break-after';
			case 'avoid':
				return 'page-break-avoid';
			default:
				return '';
		}
	};

	const getPrintClass = () =>
	{
		if (printOnly) return 'print-only';
		if (noPrint) return 'no-print';
		return '';
	};

	const combinedClassName = [
		className,
		getPageBreakClass(),
		getPrintClass(),
	].filter(Boolean).join(' ');

	return (
		<Box className={combinedClassName}>
			{children}
		</Box>
	);
}

// Hook for print functionality
function usePrint()
{
	const print = (element?: HTMLElement | string) =>
	{
		if (element)
		{
			const targetElement = typeof element === 'string'
				? document.querySelector(element) as HTMLElement
				: element;

			if (targetElement)
			{
				const printWindow = window.open('', '_blank');
				if (printWindow)
				{
					printWindow.document.write(`
            <!DOCTYPE html>
            <html>
              <head>
                <title>Print</title>
                <style>
                  ${document.querySelector('[data-print-styles="true"]')?.textContent || ''}
                </style>
              </head>
              <body>
                ${targetElement.outerHTML}
              </body>
            </html>
          `);
					printWindow.document.close();
					printWindow.focus();
					printWindow.print();
					printWindow.close();
				}
			}
		}
		else
		{
			window.print();
		}
	};

	const printPreview = (element?: HTMLElement | string) =>
	{
		const targetElement = typeof element === 'string'
			? document.querySelector(element) as HTMLElement
			: element || document.body;

		const previewWindow = window.open('', '_blank', 'width=900,height=700');
		if (previewWindow)
		{
			previewWindow.document.write(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>Print Preview</title>
            <style>
              body { margin: 0; padding: 20px; background: #f0f0f0; }
              .print-preview {
                background: white;
                box-shadow: 0 0 10px rgba(0,0,0,0.1);
                margin: 0 auto;
                padding: 1in 0.75in;
                width: 8.5in;
                min-height: 11in;
              }
              ${document.querySelector('[data-print-styles="true"]')?.textContent || ''}
            </style>
          </head>
          <body>
            <div class="print-preview">
              ${targetElement.innerHTML}
            </div>
            <div style="text-align: center; margin: 20px;">
              <button onclick="window.print()" style="padding: 10px 20px; font-size: 16px;">
                Print
              </button>
              <button onclick="window.close()" style="padding: 10px 20px; font-size: 16px; margin-left: 10px;">
                Close
              </button>
            </div>
          </body>
        </html>
      `);
			previewWindow.document.close();
		}
	};

	return { print, printPreview };
}

export { PrintStyles, PrintableComponent, usePrint };
