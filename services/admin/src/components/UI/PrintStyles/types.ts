export interface PrintStylesProps
{
	hideElements?: string[];
	showElements?: string[];
	customStyles?: string;
	pageBreaks?: {
		before?: string[];
		after?: string[];
		avoid?: string[];
	};
	margins?: {
		top?: string;
		right?: string;
		bottom?: string;
		left?: string;
	};
	orientation?: 'portrait' | 'landscape';
	paperSize?: 'A4' | 'A3' | 'letter' | 'legal';
}

export interface PrintableComponentProps
{
	children: React.ReactNode;
	className?: string;
	pageBreak?: 'before' | 'after' | 'avoid' | 'auto';
	printOnly?: boolean;
	noPrint?: boolean;
}
