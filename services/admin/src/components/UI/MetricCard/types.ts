import type { ReactNode, CSSProperties } from 'react';
import type { MantineColor, MantineSize } from '@mantine/core';

type MetricTrendType =
{
	value: number;
	percentage: number;
	direction: 'up' | 'down' | 'neutral';
	period?: string;
	isGood?: boolean;
};

type MetricCardProps =
{
	title: string;
	value: string | number;
	subtitle?: string;
	description?: string;
	icon?: ReactNode;
	color?: MantineColor;
	size?: MantineSize;
	trend?: MetricTrendType;
	loading?: boolean;
	error?: string;
	onClick?: () => void;
	actions?: ReactNode;
	chart?: ReactNode;
	footer?: ReactNode;
	variant?: 'default' | 'gradient' | 'filled' | 'outline';
	withBorder?: boolean;
	shadow?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
	radius?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
	className?: string;
	style?: CSSProperties;
	animated?: boolean;
	sparkline?: Array<{ x: string | number; y: number }>;
	status?: 'success' | 'warning' | 'error' | 'info';
	precision?: number;
	prefix?: string;
	suffix?: string;
	format?: 'number' | 'currency' | 'percentage' | 'bytes' | 'duration';
	locale?: string;
};

export type {
	MetricTrendType,
	MetricCardProps,
};
