'use client';

import {
	Card,
	Group,
	Text,
	Stack,
	ActionIcon,
	Skeleton,
	Alert,
	Badge,
	Tooltip,
	ThemeIcon,
	Progress,
	Box,
	Flex,
} from '@mantine/core';
import {
	IconTrendingUp,
	IconTrendingDown,
	IconMinus,
	IconAlertCircle,
	IconInfoCircle,
	IconCheck,
	IconX,
} from '@tabler/icons-react';
import { LineChart, Line, ResponsiveContainer } from 'recharts';

import type { MetricCardProps } from './types';

function formatValue(
	value: string | number,
	format?: string,
	precision?: number,
	prefix?: string,
	suffix?: string,
	locale = 'en-US',
): string
{
	if (typeof value === 'string') return value;

	let formattedValue: string;

	switch (format)
	{
		case 'currency':
			formattedValue = new Intl.NumberFormat(locale, {
				style: 'currency',
				currency: 'USD',
				minimumFractionDigits: precision ?? 2,
				maximumFractionDigits: precision ?? 2,
			}).format(value);
			break;

		case 'percentage':
			formattedValue = new Intl.NumberFormat(locale, {
				style: 'percent',
				minimumFractionDigits: precision ?? 1,
				maximumFractionDigits: precision ?? 1,
			}).format(value / 100);
			break;

		case 'bytes':
			const units = ['B', 'KB', 'MB', 'GB', 'TB'];
			let size = value;
			let unitIndex = 0;
			while (size >= 1024 && unitIndex < units.length - 1)
			{
				size /= 1024;
				unitIndex++;
			}
			formattedValue = `${size.toFixed(precision ?? 1)} ${units[unitIndex]}`;
			break;

		case 'duration':
			const hours = Math.floor(value / 3600);
			const minutes = Math.floor((value % 3600) / 60);
			const seconds = Math.floor(value % 60);
			if (hours > 0)
			{
				formattedValue = `${hours}h ${minutes}m ${seconds}s`;
			}
			else if (minutes > 0)
			{
				formattedValue = `${minutes}m ${seconds}s`;
			}
			else
			{
				formattedValue = `${seconds}s`;
			}
			break;

		case 'number':
		default:
			formattedValue = new Intl.NumberFormat(locale, {
				minimumFractionDigits: precision ?? 0,
				maximumFractionDigits: precision ?? 2,
			}).format(value);
			break;
	}

	return `${prefix || ''}${formattedValue}${suffix || ''}`;
}

function MetricCard({
	title,
	value,
	subtitle,
	description,
	icon,
	color = 'blue',
	size = 'md',
	trend,
	loading = false,
	error,
	onClick,
	actions,
	chart,
	footer,
	variant = 'default',
	withBorder = true,
	shadow = 'sm',
	radius = 'md',
	className,
	style,
	animated = false,
	sparkline,
	status,
	precision,
	prefix,
	suffix,
	format,
	locale,
}: MetricCardProps)
{
	const getTrendIcon = () =>
	{
		if (!trend) return null;

		switch (trend.direction)
		{
			case 'up':
				return <IconTrendingUp size={16} />;
			case 'down':
				return <IconTrendingDown size={16} />;
			default:
				return <IconMinus size={16} />;
		}
	};

	const getTrendColor = () =>
	{
		if (!trend) return 'gray';

		if (trend.isGood === undefined)
		{
			return trend.direction === 'up' ? 'green' : trend.direction === 'down' ? 'red' : 'gray';
		}

		return trend.isGood ? 'green' : 'red';
	};

	const getStatusIcon = () =>
	{
		switch (status)
		{
			case 'success':
				return <IconCheck size={16} />;
			case 'warning':
				return <IconAlertCircle size={16} />;
			case 'error':
				return <IconX size={16} />;
			case 'info':
				return <IconInfoCircle size={16} />;
			default:
				return null;
		}
	};

	const getStatusColor = () =>
	{
		switch (status)
		{
			case 'success':
				return 'green';
			case 'warning':
				return 'yellow';
			case 'error':
				return 'red';
			case 'info':
				return 'blue';
			default:
				return color;
		}
	};

	if (loading)
	{
		return (
			<Card
				withBorder={withBorder}
				shadow={shadow}
				radius={radius}
				className={className}
				style={style}
			>
				<Stack gap="sm">
					<Group justify="space-between">
						<Skeleton height={20} width="60%" />
						<Skeleton height={24} width={24} circle />
					</Group>
					<Skeleton height={32} width="40%" />
					<Skeleton height={16} width="80%" />
				</Stack>
			</Card>
		);
	}

	if (error)
	{
		return (
			<Card
				withBorder={withBorder}
				shadow={shadow}
				radius={radius}
				className={className}
				style={style}
			>
				<Alert
					icon={<IconAlertCircle size={16} />}
					title="Error loading metric"
					color="red"
					variant="light"
				>
					{error}
				</Alert>
			</Card>
		);
	}

	const formattedValue = formatValue(value, format, precision, prefix, suffix, locale);

	return (
		<Card
			withBorder={withBorder}
			shadow={shadow}
			radius={radius}
			className={className}
			style={{
				cursor: onClick ? 'pointer' : undefined,
				transition: 'transform 0.2s ease, box-shadow 0.2s ease, opacity 0.2s ease',
				opacity: animated ? 1 : 1,
				...style,
			}}
			onClick={onClick}
			onMouseEnter={(e) =>
			{
				if (onClick)
				{
					e.currentTarget.style.transform = 'translateY(-2px)';
					e.currentTarget.style.boxShadow = 'var(--mantine-shadow-md)';
				}
			}}
			onMouseLeave={(e) =>
			{
				if (onClick)
				{
					e.currentTarget.style.transform = 'translateY(0)';
					e.currentTarget.style.boxShadow = `var(--mantine-shadow-${shadow})`;
				}
			}}
		>
			<Stack gap="sm">
				{/* Header */}
				<Group justify="space-between" align="flex-start">
					<Stack gap={4} style={{ flex: 1 }}>
						<Group gap="xs">
							<Text size="sm" c="dimmed" fw={500}>
								{title}
							</Text>
							{status && (
								<ThemeIcon size="xs" color={getStatusColor()} variant="light">
									{getStatusIcon()}
								</ThemeIcon>
							)}
						</Group>
						{subtitle && (
							<Text size="xs" c="dimmed">
								{subtitle}
							</Text>
						)}
					</Stack>
					<Group gap="xs">
						{icon && (
							<ThemeIcon
								size="lg"
								color={getStatusColor()}
								variant="light"
							>
								{icon}
							</ThemeIcon>
						)}
						{actions}
					</Group>
				</Group>

				{/* Value */}
				<Group justify="space-between" align="flex-end">
					<Text
						size={size === 'xs' ? 'lg' : size === 'sm' ? 'xl' : '2xl'}
						fw={700}
						c={getStatusColor()}
					>
						{formattedValue}
					</Text>
					{trend && (
						<Tooltip
							label={`${trend.direction === 'up' ? '+' : trend.direction === 'down' ? '-' : ''}${Math.abs(trend.percentage)}% ${trend.period || 'vs previous period'}`}
							withArrow
						>
							<Badge
								color={getTrendColor()}
								variant="light"
								size="sm"
								leftSection={getTrendIcon()}
							>
								{Math.abs(trend.percentage)}%
							</Badge>
						</Tooltip>
					)}
				</Group>

				{/* Description */}
				{description && (
					<Text size="xs" c="dimmed">
						{description}
					</Text>
				)}

				{/* Sparkline Chart */}
				{sparkline && sparkline.length > 0 && (
					<Box h={40}>
						<ResponsiveContainer width="100%" height="100%">
							<LineChart data={sparkline}>
								<Line
									type="monotone"
									dataKey="y"
									stroke={`var(--mantine-color-${getStatusColor()}-6)`}
									strokeWidth={2}
									dot={false}
								/>
							</LineChart>
						</ResponsiveContainer>
					</Box>
				)}

				{/* Custom Chart */}
				{chart && <Box>{chart}</Box>}

				{/* Footer */}
				{footer && (
					<Box pt="xs" style={{ borderTop: '1px solid var(--mantine-color-gray-2)' }}>
						{footer}
					</Box>
				)}
			</Stack>
		</Card>
	);
}

export default MetricCard;
