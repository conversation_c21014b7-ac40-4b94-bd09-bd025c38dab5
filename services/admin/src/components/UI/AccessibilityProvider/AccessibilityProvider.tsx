'use client';

import { Box, VisuallyHidden } from '@mantine/core';
import { useLocalStorage, useMediaQuery, useFocusTrap } from '@mantine/hooks';
import {
	createContext, useContext, useEffect, useState, useRef,
} from 'react';

import type {
	AccessibilityProviderProps,
	AccessibilityContextType,
	SkipLinkProps,
	AriaLiveRegionProps,
	FocusTrapProps,
} from './types';

// Accessibility context
const AccessibilityContext = createContext<AccessibilityContextType | null>(null);

function useAccessibility()
{
	const context = useContext(AccessibilityContext);
	if (!context)
	{
		throw new Error('useAccessibility must be used within an AccessibilityProvider');
	}
	return context;
}

function AccessibilityProvider({
	children,
	announcePageChanges = true,
	focusManagement = true,
	keyboardNavigation = true,
	screenReaderSupport = true,
	highContrastMode = true,
	reducedMotion = true,
	skipLinks = true,
	ariaLiveRegion = true,
}: AccessibilityProviderProps)
{
	const [isHighContrast, setIsHighContrast] = useLocalStorage({
		key: 'admin-high-contrast',
		defaultValue: false,
	});

	const [isReducedMotion, setIsReducedMotion] = useLocalStorage({
		key: 'admin-reduced-motion',
		defaultValue: false,
	});

	const prefersReducedMotionRaw = useMediaQuery('(prefers-reduced-motion: reduce)');
	const prefersHighContrastRaw = useMediaQuery('(prefers-contrast: high)');
	const prefersReducedMotion = !!prefersReducedMotionRaw;
	const prefersHighContrast = !!prefersHighContrastRaw;

	const liveRegionRef = useRef<HTMLDivElement>(null);
	const [liveMessage, setLiveMessage] = useState('');

	// Apply accessibility styles
	useEffect(() =>
	{
		const root = document.documentElement;

		// High contrast mode
		if (isHighContrast || prefersHighContrast)
		{
			root.classList.add('high-contrast');
		}
		else
		{
			root.classList.remove('high-contrast');
		}

		// Reduced motion
		if (isReducedMotion || prefersReducedMotion)
		{
			root.classList.add('reduced-motion');
		}
		else
		{
			root.classList.remove('reduced-motion');
		}

		// Add CSS for accessibility features
		const accessibilityStyles = `
      .high-contrast {
        --mantine-color-text: #000000;
        --mantine-color-body: #ffffff;
        --mantine-color-dimmed: #333333;
        filter: contrast(150%);
      }

      .reduced-motion * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
      }

      /* Focus indicators */
      *:focus-visible {
        outline: 2px solid var(--mantine-color-blue-6) !important;
        outline-offset: 2px !important;
      }

      /* Skip links */
      .skip-link {
        position: absolute;
        top: -40px;
        left: 6px;
        background: var(--mantine-color-blue-6);
        color: white;
        padding: 8px;
        text-decoration: none;
        border-radius: 4px;
        z-index: 1000;
        transition: top 0.3s;
      }

      .skip-link:focus {
        top: 6px;
      }

      /* Keyboard navigation */
      .keyboard-navigation {
        outline: 2px solid var(--mantine-color-blue-6);
        outline-offset: 2px;
      }

      /* Screen reader only content */
      .sr-only {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0, 0, 0, 0);
        white-space: nowrap;
        border: 0;
      }
    `;

		const styleElement = document.createElement('style');
		styleElement.textContent = accessibilityStyles;
		document.head.appendChild(styleElement);

		return () =>
		{
			document.head.removeChild(styleElement);
		};
	}, [isHighContrast, isReducedMotion, prefersHighContrast, prefersReducedMotion]);

	// Keyboard navigation handler
	useEffect(() =>
	{
		if (!keyboardNavigation) return undefined;

		const handleKeyDown = (event: KeyboardEvent) =>
		{
			// Tab navigation
			if (event.key === 'Tab')
			{
				document.body.classList.add('keyboard-navigation');
			}

			// Escape key to close modals/dropdowns
			if (event.key === 'Escape')
			{
				const activeElement = document.activeElement as HTMLElement;
				if (activeElement && activeElement.blur)
				{
					activeElement.blur();
				}
			}

			// Arrow key navigation for menus and lists
			if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(event.key))
			{
				const activeElement = document.activeElement as HTMLElement;
				if (activeElement && activeElement.getAttribute('role') === 'menuitem')
				{
					event.preventDefault();
					// Handle menu navigation
					const menu = activeElement.closest('[role="menu"]');
					if (menu)
					{
						const items = Array.from(menu.querySelectorAll('[role="menuitem"]')) as HTMLElement[];
						const currentIndex = items.indexOf(activeElement);

						let nextIndex = currentIndex;
						if (event.key === 'ArrowDown')
						{
							nextIndex = (currentIndex + 1) % items.length;
						}
						else if (event.key === 'ArrowUp')
						{
							nextIndex = currentIndex === 0 ? items.length - 1 : currentIndex - 1;
						}

						items[nextIndex]?.focus();
					}
				}
			}
		};

		const handleMouseDown = () =>
		{
			document.body.classList.remove('keyboard-navigation');
		};

		document.addEventListener('keydown', handleKeyDown);
		document.addEventListener('mousedown', handleMouseDown);

		return () =>
		{
			document.removeEventListener('keydown', handleKeyDown);
			document.removeEventListener('mousedown', handleMouseDown);
		};
	}, [keyboardNavigation]);

	const announcePageChange = (message: string) =>
	{
		if (!announcePageChanges) return;
		setLiveMessage(message);
		setTimeout(() => setLiveMessage(''), 1000);
	};

	const setFocus = (element: HTMLElement | string) =>
	{
		if (!focusManagement) return;

		let targetElement: HTMLElement | null = null;

		if (typeof element === 'string')
		{
			targetElement = document.querySelector(element);
		}
		else
		{
			targetElement = element;
		}

		if (targetElement)
		{
			targetElement.focus();
		}
	};

	const skipToContent = () =>
	{
		const mainContent = document.querySelector('main') || document.querySelector('[role="main"]');
		if (mainContent)
		{
			(mainContent as HTMLElement).focus();
		}
	};

	const toggleHighContrast = () =>
	{
		setIsHighContrast(!isHighContrast);
	};

	const toggleReducedMotion = () =>
	{
		setIsReducedMotion(!isReducedMotion);
	};

	const contextValue: AccessibilityContextType =
	{
		announcePageChange,
		setFocus,
		skipToContent,
		isHighContrast: Boolean(isHighContrast || prefersHighContrast),
		isReducedMotion: Boolean(isReducedMotion || prefersReducedMotion),
		toggleHighContrast,
		toggleReducedMotion,
	};

	return (
		<AccessibilityContext.Provider value={contextValue}>
			{skipLinks && (
				<SkipLink href="#main-content">
					Skip to main content
				</SkipLink>
			)}

			{ariaLiveRegion && (
				<AriaLiveRegion message={liveMessage} politeness="polite" />
			)}

			{children}
		</AccessibilityContext.Provider>
	);
}

function SkipLink({
	href, children, className, style,
}: SkipLinkProps)
{
	return (
		<a
			href={href}
			className={`skip-link ${className || ''}`}
			style={style}
			onClick={(e) =>
			{
				e.preventDefault();
				const target = document.querySelector(href);
				if (target)
				{
					(target as HTMLElement).focus();
				}
			}}
		>
			{children}
		</a>
	);
}

function AriaLiveRegion({
	message,
	politeness = 'polite',
	atomic = true,
	relevant = 'all',
}: AriaLiveRegionProps)
{
	return (
		<VisuallyHidden>
			<div
				aria-live={politeness}
				aria-atomic={atomic}
				aria-relevant={relevant}
				role="status"
			>
				{message}
			</div>
		</VisuallyHidden>
	);
}

function FocusTrap({
	children,
	active = true,
	initialFocus,
	onActivate,
	onDeactivate,
}: FocusTrapProps)
{
	const focusTrapRef = useFocusTrap(active);

	useEffect(() =>
	{
		if (active)
		{
			onActivate?.();

			// Set initial focus
			if (initialFocus)
			{
				const element = typeof initialFocus === 'string'
					? document.querySelector(initialFocus)
					: document.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])')[initialFocus];

				if (element)
				{
					(element as HTMLElement).focus();
				}
			}
		}
		else
		{
			onDeactivate?.();
		}
	}, [active, initialFocus, onActivate, onDeactivate]);

	return (
		<Box ref={focusTrapRef}>
			{children}
		</Box>
	);
}

// Screen reader only text component
function ScreenReaderOnly({ children }: { children: React.ReactNode })
{
	return (
		<VisuallyHidden>
			{children}
		</VisuallyHidden>
	);
}

// Accessible heading component with proper hierarchy
function AccessibleHeading({
	level,
	children,
	id,
	className,
	style,
}: {
	level: 1 | 2 | 3 | 4 | 5 | 6;
	children: React.ReactNode;
	id?: string;
	className?: string;
	style?: React.CSSProperties;
})
{
	const Tag = `h${level}` as keyof JSX.IntrinsicElements;

	return (
		<Tag
			id={id}
			className={className}
			style={style}
			tabIndex={-1}
		>
			{children}
		</Tag>
	);
}

export {
	AccessibilityProvider,
	SkipLink,
	AriaLiveRegion,
	FocusTrap,
	ScreenReaderOnly,
	AccessibleHeading,
	useAccessibility,
};
