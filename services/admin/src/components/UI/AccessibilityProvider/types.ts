import type { ReactNode, CSSProperties } from 'react';

type AccessibilityProviderProps =
{
	children: ReactNode;
	announcePageChanges?: boolean;
	focusManagement?: boolean;
	keyboardNavigation?: boolean;
	screenReaderSupport?: boolean;
	highContrastMode?: boolean;
	reducedMotion?: boolean;
	skipLinks?: boolean;
	ariaLiveRegion?: boolean;
};

type AccessibilityContextType =
{
	announcePageChange: (message: string) => void;
	setFocus: (element: HTMLElement | string) => void;
	skipToContent: () => void;
	isHighContrast: boolean;
	isReducedMotion: boolean;
	toggleHighContrast: () => void;
	toggleReducedMotion: () => void;
};

type SkipLinkProps =
{
	href: string;
	children: ReactNode;
	className?: string;
	style?: CSSProperties;
};

type AriaLiveRegionProps =
{
	message: string;
	politeness?: 'polite' | 'assertive' | 'off';
	atomic?: boolean;
	relevant?: 'additions' | 'removals' | 'text' | 'all';
};

type FocusTrapProps =
{
	children: ReactNode;
	active?: boolean;
	initialFocus?: number | string;
	onActivate?: () => void;
	onDeactivate?: () => void;
};

export type {
	AccessibilityProviderProps,
	AccessibilityContextType,
	SkipLinkProps,
	AriaLiveRegionProps,
	FocusTrapProps,
};
