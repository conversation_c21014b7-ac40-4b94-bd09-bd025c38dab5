type ConfigType =
{
	port: number;
	nodeEnv: 'development' | 'production' | 'test';
	sessionSecret: string;
	sessionTimeout: number;
	databases: {
		scylla: {
			hosts: string[];
			keyspace: string;
			username?: string;
			password?: string;
		};
		mariadb: {
			host: string;
			port: number;
			database: string;
			username: string;
			password: string;
		};
		redis: {
			host: string;
			port: number;
			password?: string;
			db: number;
			keyPrefix?: string;
		};
		manticore: {
			host: string;
			port: number;
		};
	};
	services: {
		webApp: string;
		worker: string;
		domainSeeder: string;
	};
	auth: {
		bcryptRounds: number;
		maxLoginAttempts: number;
		lockoutDuration: number;
	};
	logging: {
		level: 'debug' | 'info' | 'warn' | 'error';
		format: 'json' | 'text';
	};
	notifications: {
		email: {
			adminEmail: string;
			supportEmail: string;
		};
		webhook?: {
			url: string;
			secret?: string;
		};
		sms?: {
			accountSid: string;
			authToken: string;
			fromNumber: string;
		};
	};
};

const config: ConfigType =
{
	port: parseInt(process.env.PORT || '3003', 10),
	nodeEnv: (process.env.NODE_ENV as ConfigType['nodeEnv']) || 'development',
	sessionSecret: process.env.SESSION_SECRET || 'dev-session-secret',
	sessionTimeout: parseInt(process.env.SESSION_TIMEOUT || '********', 10),
	databases: {
		scylla: {
			hosts: (process.env.SCYLLA_HOSTS || 'localhost:9042').split(','),
			keyspace: process.env.SCYLLA_KEYSPACE || 'domainr',
			...(process.env.SCYLLA_USERNAME && { username: process.env.SCYLLA_USERNAME }),
			...(process.env.SCYLLA_PASSWORD && { password: process.env.SCYLLA_PASSWORD }),
		},
		mariadb: {
			host: process.env.MARIADB_HOST || 'localhost',
			port: parseInt(process.env.MARIADB_PORT || '3306', 10),
			database: process.env.MARIADB_DATABASE || 'domainr',
			username: process.env.MARIADB_USERNAME || 'root',
			password: process.env.MARIADB_PASSWORD || '',
		},
		redis: {
			host: process.env.REDIS_HOST || 'localhost',
			port: parseInt(process.env.REDIS_PORT || '6379', 10),
			...(process.env.REDIS_PASSWORD && { password: process.env.REDIS_PASSWORD }),
			db: parseInt(process.env.REDIS_DB || '0', 10),
			keyPrefix: 'p4:',
		},
		manticore: {
			host: process.env.MANTICORE_HOST || 'localhost',
			port: parseInt(process.env.MANTICORE_PORT || '9308', 10),
		},
	},
	services: {
		webApp: process.env.WEB_APP_URL || 'http://localhost:3000',
		worker: process.env.WORKER_URL || 'http://localhost:3001',
		domainSeeder: process.env.DOMAIN_SEEDER_URL || 'http://localhost:3005',
	},
	auth: {
		bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS || '12', 10),
		maxLoginAttempts: parseInt(process.env.MAX_LOGIN_ATTEMPTS || '5', 10),
		lockoutDuration: parseInt(process.env.LOCKOUT_DURATION || '900000', 10),
	},
	logging: {
		level: (process.env.LOG_LEVEL as ConfigType['logging']['level']) || 'info',
		format: (process.env.LOG_FORMAT as ConfigType['logging']['format']) || 'json',
	},
	notifications: {
		email: {
			adminEmail: process.env.ADMIN_EMAIL || '<EMAIL>',
			supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>',
		},
		...(process.env.WEBHOOK_URL && {
			webhook: {
				url: process.env.WEBHOOK_URL,
				...(process.env.WEBHOOK_SECRET && { secret: process.env.WEBHOOK_SECRET }),
			},
		}),
		...(process.env.SMS_ACCOUNT_SID && process.env.SMS_AUTH_TOKEN && process.env.SMS_FROM_NUMBER && {
			sms: {
				accountSid: process.env.SMS_ACCOUNT_SID,
				authToken: process.env.SMS_AUTH_TOKEN,
				fromNumber: process.env.SMS_FROM_NUMBER,
			},
		}),
	},
};

export type { ConfigType };

export { config };
