import { createTheme, MantineColorsTuple } from '@mantine/core';

const primaryColor: MantineColorsTuple = [
	'#e3f2fd',
	'#bbdefb',
	'#90caf9',
	'#64b5f6',
	'#42a5f5',
	'#2196f3',
	'#1e88e5',
	'#1976d2',
	'#1565c0',
	'#0d47a1',
];

const theme = createTheme({
	primaryColor: 'blue',
	colors: {
		blue: primaryColor,
	},
	fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif',
	headings: {
		fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif',
	},
	defaultRadius: 'md',
	cursorType: 'pointer',
	focusRing: 'auto',
	activeClassName: 'mantine-active',
	components: {
		Button: {
			defaultProps: {
				fw: 500,
			},
		},
		Paper: {
			defaultProps: {
				shadow: 'xs',
			},
		},
		Card: {
			defaultProps: {
				shadow: 'sm',
				withBorder: true,
			},
		},
		Table: {
			defaultProps: {
				striped: true,
				highlightOnHover: true,
			},
		},
		Badge: {
			defaultProps: {
				variant: 'light',
			},
		},
	},
	breakpoints: {
		xs: '36em',
		sm: '48em',
		md: '62em',
		lg: '75em',
		xl: '88em',
	},
});

export { theme };
