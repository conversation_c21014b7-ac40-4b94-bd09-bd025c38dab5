import { verify } from 'argon2';
// import { createClient } from 'redis';
import { v4 as uuidv4 } from 'uuid';
import fs from 'fs';
import path from 'path';
import { logger } from '@/lib/logger';

import { AUTH_CONFIG } from './config';

import type { AdminUserType, SessionDataType } from './config';

interface AuthResultType
{
	success: boolean;
	user?: {
		username: string;
		role: string;
		permissions: string[];
	};
	sessionId?: string;
	error?: string;
}

interface LoginAttemptType
{
	username: string;
	ipAddress: string;
	timestamp: Date;
	success: boolean;
}

class AuthService
{
	private sessionStore: Map<string, SessionDataType> = new Map();

	private users: Map<string, AdminUserType>;

	private loginAttempts: Map<string, LoginAttemptType[]> = new Map();

	private sessionFilePath: string;

	constructor()
	{
		// Initialize users map
		this.users = new Map();
		AUTH_CONFIG.users.forEach((user) =>
		{
			this.users.set(user.username, { ...user });
		});

		// Initialize persistent session storage
		this.sessionFilePath = path.join(process.cwd(), '.next', 'sessions.json');
		this.loadSessions();
	}

	private loadSessions(): void
	{
		try
		{
			if (fs.existsSync(this.sessionFilePath))
			{
				const data = fs.readFileSync(this.sessionFilePath, 'utf8');
				const sessions = JSON.parse(data);

				// Convert back to Map and validate sessions
				Object.entries(sessions).forEach(([sessionId, sessionData]: [string, any]) =>
				{
					// Check if session is still valid (not expired)
					const expiresAt = new Date(sessionData.expiresAt);
					if (expiresAt > new Date())
					{
						this.sessionStore.set(sessionId, {
							...sessionData,
							expiresAt,
							lastActivity: new Date(sessionData.lastActivity),
						});
					}
				});

				logger.debug('Loaded sessions from persistent storage', {
					service: 'admin',
					sessionCount: this.sessionStore.size,
				});
			}
		}
		catch (error)
		{
			logger.error('Failed to load sessions from persistent storage', {
				service: 'admin',
				error: error instanceof Error ? error.message : 'Unknown error',
			});
		}
	}

	private saveSessions(): void
	{
		try
		{
			// Ensure .next directory exists
			const nextDir = path.dirname(this.sessionFilePath);
			if (!fs.existsSync(nextDir))
			{
				fs.mkdirSync(nextDir, { recursive: true });
			}

			// Convert Map to plain object for JSON serialization
			const sessions: Record<string, any> = {};
			this.sessionStore.forEach((sessionData, sessionId) =>
			{
				sessions[sessionId] = {
					...sessionData,
					expiresAt: sessionData.expiresAt.toISOString(),
					lastActivity: sessionData.lastActivity.toISOString(),
				};
			});

			fs.writeFileSync(this.sessionFilePath, JSON.stringify(sessions, null, 2));
		}
		catch (error)
		{
			logger.error('Failed to save sessions to persistent storage', {
				service: 'admin',
				error: error instanceof Error ? error.message : 'Unknown error',
			});
		}
	}

	async authenticate(username: string, password: string, ipAddress: string, userAgent: string): Promise<AuthResultType>
	{
		try
		{
			const user = this.users.get(username);

			if (!user)
			{
				this.recordLoginAttempt(username, ipAddress, false);
				return { success: false, error: 'Invalid credentials' };
			}

			// Check if user is active
			if (!user.isActive)
			{
				return { success: false, error: 'Account is disabled' };
			}

			// Check if account is locked
			if (user.lockedUntil && user.lockedUntil > new Date())
			{
				const unlockTime = user.lockedUntil.toLocaleString();
				return { success: false, error: `Account is locked until ${unlockTime}` };
			}

			// Check rate limiting
			if (this.isRateLimited(username, ipAddress))
			{
				return { success: false, error: 'Too many login attempts. Please try again later.' };
			}

			// Verify password
			const isValidPassword = await verify(user.password, password);

			if (!isValidPassword)
			{
				this.handleFailedLogin(username);
				this.recordLoginAttempt(username, ipAddress, false);
				return { success: false, error: 'Invalid credentials' };
			}

			// Check if password needs to be changed
			if (user.mustChangePassword || this.isPasswordExpired(user))
			{
				return { success: false, error: 'Password must be changed' };
			}

			// Reset failed attempts on successful login
			this.resetFailedAttempts(username);
			this.recordLoginAttempt(username, ipAddress, true);

			// Update last login
			user.lastLogin = new Date();
			this.users.set(username, user);

			// Create session
			const sessionId = uuidv4();
			const sessionData: SessionDataType =
			{
				sessionId,
				username: user.username,
				role: user.role,
				permissions: user.permissions,
				createdAt: new Date(),
				lastActivity: new Date(),
				expiresAt: new Date(Date.now() + AUTH_CONFIG.sessionTimeout * 1000),
				ipAddress,
				userAgent,
			};

			await this.storeSession(sessionId, sessionData);

			return {
				success: true,
				user: {
					username: user.username,
					role: user.role,
					permissions: user.permissions,
				},
				sessionId,
			};
		}
		catch (error)
		{
			logger.error(`Authentication error: ${error instanceof Error ? error.message : String(error)}`);
			return { success: false, error: 'Authentication failed' };
		}
	}

	async validateSession(sessionId: string): Promise<SessionDataType | null>
	{
		try
		{
			const session = this.sessionStore.get(sessionId);

			if (!session)
			{
				return null;
			}

			// Check if session is expired
			if (new Date() > new Date(session.expiresAt))
			{
				await this.destroySession(sessionId);
				return null;
			}

			// Update last activity
			session.lastActivity = new Date();
			await this.storeSession(sessionId, session);

			return session;
		}
		catch (error)
		{
			logger.error(`Session validation error: ${error instanceof Error ? error.message : String(error)}`);
			return null;
		}
	}

	async refreshSession(sessionId: string): Promise<void>
	{
		try
		{
			const session = await this.validateSession(sessionId);

			if (session)
			{
				session.expiresAt = new Date(Date.now() + AUTH_CONFIG.sessionTimeout * 1000);
				await this.storeSession(sessionId, session);
			}
		}
		catch (error)
		{
			logger.error(`Session refresh error: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	async logout(sessionId: string): Promise<void>
	{
		await this.destroySession(sessionId);
	}

	async getPermissions(sessionId: string): Promise<string[]>
	{
		const session = await this.validateSession(sessionId);
		return session?.permissions || [];
	}

	async getUserSessions(username: string): Promise<SessionDataType[]>
	{
		try
		{
			const sessions: SessionDataType[] = [];

			for (const [_, session] of this.sessionStore.entries())
			{
				if (session.username === username)
				{
					sessions.push(session);
				}
			}

			return sessions;
		}
		catch (error)
		{
			logger.error(`Error getting user sessions: ${error instanceof Error ? error.message : String(error)}`);
			return [];
		}
	}

	async destroyAllUserSessions(username: string): Promise<void>
	{
		const sessions = await this.getUserSessions(username);

		for (const session of sessions)
		{
			await this.destroySession(session.sessionId);
		}
	}

	validatePassword(password: string): { valid: boolean; errors: string[] }
	{
		const errors: string[] = [];
		const policy = AUTH_CONFIG.passwordPolicy;

		if (password.length < policy.minLength)
		{
			errors.push(`Password must be at least ${policy.minLength} characters long`);
		}

		if (policy.requireUppercase && !/[A-Z]/.test(password))
		{
			errors.push('Password must contain at least one uppercase letter');
		}

		if (policy.requireLowercase && !/[a-z]/.test(password))
		{
			errors.push('Password must contain at least one lowercase letter');
		}

		if (policy.requireNumbers && !/\d/.test(password))
		{
			errors.push('Password must contain at least one number');
		}

		if (policy.requireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(password))
		{
			errors.push('Password must contain at least one special character');
		}

		return {
			valid: errors.length === 0,
			errors,
		};
	}

	private async storeSession(sessionId: string, sessionData: SessionDataType): Promise<void>
	{
		try
		{
			// Store in memory for fast access
			this.sessionStore.set(sessionId, sessionData);

			// Persist to file system for durability across restarts
			this.saveSessions();

			logger.debug(`Session stored persistently: ${sessionId} for user ${sessionData.username}`, {
				service: 'admin',
			});
		}
		catch (error)
		{
			logger.error(`Error storing session: ${error instanceof Error ? error.message : String(error)}`, {
				service: 'admin',
			});
		}
	}

	private async destroySession(sessionId: string): Promise<void>
	{
		try
		{
			this.sessionStore.delete(sessionId);

			// Update persistent storage
			this.saveSessions();

			logger.debug(`Session destroyed persistently: ${sessionId}`, {
				service: 'admin',
			});
		}
		catch (error)
		{
			logger.error(`Error destroying session: ${error instanceof Error ? error.message : String(error)}`, {
				service: 'admin',
			});
		}
	}

	private handleFailedLogin(username: string): void
	{
		const user = this.users.get(username);

		if (user)
		{
			user.failedLoginAttempts += 1;

			if (user.failedLoginAttempts >= AUTH_CONFIG.maxLoginAttempts)
			{
				user.lockedUntil = new Date(Date.now() + AUTH_CONFIG.lockoutDuration * 60 * 1000);
			}

			this.users.set(username, user);
		}
	}

	private resetFailedAttempts(username: string): void
	{
		const user = this.users.get(username);

		if (user)
		{
			user.failedLoginAttempts = 0;
			user.lockedUntil = null;
			this.users.set(username, user);
		}
	}

	private isPasswordExpired(user: AdminUserType): boolean
	{
		const maxAge = AUTH_CONFIG.passwordPolicy.maxAge * 24 * 60 * 60 * 1000; // Convert days to milliseconds
		return Date.now() - user.passwordLastChanged.getTime() > maxAge;
	}

	private recordLoginAttempt(username: string, ipAddress: string, success: boolean): void
	{
		const key = `${username}:${ipAddress}`;
		const attempts = this.loginAttempts.get(key) || [];

		attempts.push({
			username,
			ipAddress,
			timestamp: new Date(),
			success,
		});

		// Keep only last 10 attempts
		if (attempts.length > 10)
		{
			attempts.splice(0, attempts.length - 10);
		}

		this.loginAttempts.set(key, attempts);
	}

	private isRateLimited(username: string, ipAddress: string): boolean
	{
		const key = `${username}:${ipAddress}`;
		const attempts = this.loginAttempts.get(key) || [];

		// Check failed attempts in the last 15 minutes
		const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000);
		const recentFailedAttempts = attempts.filter(
			attempt => !attempt.success && attempt.timestamp > fifteenMinutesAgo,
		);

		return recentFailedAttempts.length >= AUTH_CONFIG.maxLoginAttempts;
	}
}

// Singleton instance
const authService = new AuthService();

export type { AuthResultType, LoginAttemptType };

export { AuthService, authService };
