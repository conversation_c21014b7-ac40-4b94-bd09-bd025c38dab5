export type { AdminUserType, AuthConfigType, SessionDataType } from './config';
export type { AuthResultType, LoginAttemptType } from './service';
export type { IronSessionDataType } from './session';
export type { AuthMiddlewareOptionsType } from './middleware';

export { AUTH_CONFIG, sessionOptions } from './config';
export { AuthService, authService } from './service';
export {
	getSession,
	getSessionFromRequest,
	createSession,
	destroySession,
	isAuthenticated,
	getCurrentUser,
	hasPermission,
	requirePermission,
} from './session';
export {
	authMiddleware,
	requireAuth,
	requirePermission as requirePermissionMiddleware,
	requireRole,
} from './middleware';
