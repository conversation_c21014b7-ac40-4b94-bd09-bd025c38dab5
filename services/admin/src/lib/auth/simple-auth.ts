/**
 * Simple file-based authentication for admin panel
 * No Redis, no complex session management - just basic auth
 */

import { compare, hash } from 'bcrypt';

// Simple in-memory users (in production, this could be a config file)
const ADMIN_USERS =
[
	{
		id: '1',
		username: 'admin',
		// Password: 'admin123' (change this!)
		passwordHash: '$2b$12$LQv3c1yqBWVHxkd0LQ1Gv.6FdRd0Q6adHcOHAHHFwO6oRur2WGdeS',
		role: 'admin',
	},
];

type UserType =
{
	id: string;
	username: string;
	role: string;
};

async function validateUser(username: string, password: string): Promise<UserType | null>
{
	const user = ADMIN_USERS.find(u => u.username === username);
	if (!user) return null;

	const isValid = await compare(password, user.passwordHash);
	if (!isValid) return null;

	return {
		id: user.id,
		username: user.username,
		role: user.role,
	};
}

async function hashPassword(password: string): Promise<string>
{
	return hash(password, 12);
}

// Simple session storage (in-memory, resets on restart)
const sessions = new Map<string, { user: UserType; expires: number }>();

function createSession(user: UserType): string
{
	const sessionId = Math.random().toString(36).substring(2, 15)
                   + Math.random().toString(36).substring(2, 15);

	sessions.set(sessionId, {
		user,
		expires: Date.now() + (8 * 60 * 60 * 1000), // 8 hours
	});

	return sessionId;
}

function getSession(sessionId: string): UserType | null
{
	const session = sessions.get(sessionId);
	if (!session) return null;

	if (Date.now() > session.expires)
	{
		sessions.delete(sessionId);
		return null;
	}

	return session.user;
}

function deleteSession(sessionId: string): void
{
	sessions.delete(sessionId);
}

export {
	validateUser,
	hashPassword,
	createSession,
	getSession,
	deleteSession,
};
