# Authentication System Documentation

## Overview

The admin panel uses a comprehensive authentication system with the following features:

- **Static user configuration** with argon2 hashed passwords
- **Iron-session** for secure session management with Redis storage
- **Role-based access control** (RBAC) with granular permissions
- **Session timeout** (8 hours) with automatic logout
- **Concurrent session support** for multiple administrators
- **Login attempt rate limiting** and account lockout protection
- **Password strength validation** and periodic password change requirements

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Login Page    │───▶│  Auth Service   │───▶│  Redis Session │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │  Iron Session   │
                       └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   Middleware    │
                       └─────────────────┘
```

## User Roles and Permissions

### Super Admin (`super_admin`)

- **Permissions**: `['*']` (all permissions)
- **Access**: Complete system access
- **Default User**: `superadmin`

### Admin (`admin`)

- **Permissions**:
  - `dashboard.view`
  - `services.view`
  - `domains.view`
  - `domains.edit`
  - `crawl.view`
  - `crawl.manage`
  - `analytics.view`
  - `logs.view`
  - `database.view`
- **Access**: Most administrative functions except user management
- **Default User**: `admin`

### Viewer (`viewer`)

- **Permissions**:
  - `dashboard.view`
  - `services.view`
  - `domains.view`
  - `analytics.view`
  - `logs.view`
- **Access**: Read-only access to system information
- **Default User**: `viewer`

## Default Credentials

All default users use the password: `AdminPass123!`

- **superadmin** / AdminPass123!
- **admin** / AdminPass123!
- **viewer** / AdminPass123!

⚠️ **Security Note**: Change these passwords in production!

## API Endpoints

### Authentication Endpoints

#### POST `/api/auth/login`

Authenticate a user and create a session.

**Request Body:**

```json
{
  "username": "superadmin",
  "password": "AdminPass123!"
}
```

**Response (Success):**

```json
{
  "success": true,
  "user": {
    "username": "superadmin",
    "role": "super_admin",
    "permissions": ["*"]
  }
}
```

**Response (Error):**

```json
{
  "message": "Invalid credentials"
}
```

#### POST `/api/auth/logout`

Destroy the current session.

**Response:**

```json
{
  "success": true
}
```

#### GET `/api/auth/session`

Validate the current session and get user info.

**Response (Authenticated):**

```json
{
  "authenticated": true,
  "user": {
    "username": "superadmin",
    "role": "super_admin",
    "permissions": ["*"]
  }
}
```

**Response (Not Authenticated):**

```json
{
  "authenticated": false
}
```

#### GET `/api/auth/sessions`

Get user sessions (requires authentication).

**Query Parameters:**

- `username` (optional): Get sessions for specific user (super_admin only)

**Response:**

```json
{
  "sessions": [
    {
      "sessionId": "uuid",
      "username": "admin",
      "role": "admin",
      "permissions": ["dashboard.view", "..."],
      "createdAt": "2024-01-01T00:00:00.000Z",
      "lastActivity": "2024-01-01T01:00:00.000Z",
      "expiresAt": "2024-01-01T08:00:00.000Z",
      "ipAddress": "127.0.0.1",
      "userAgent": "Mozilla/5.0..."
    }
  ]
}
```

#### DELETE `/api/auth/sessions`

Destroy user sessions (requires authentication).

**Request Body:**

```json
{
  "sessionId": "uuid-to-destroy",
  "targetUsername": "user-to-target"
}
```

#### POST `/api/auth/change-password`

Change user password (requires authentication).

**Request Body:**

```json
{
  "currentPassword": "AdminPass123!",
  "newPassword": "NewSecurePass456!",
  "confirmPassword": "NewSecurePass456!"
}
```

## Security Features

### Password Policy

- **Minimum Length**: 8 characters
- **Uppercase**: Required
- **Lowercase**: Required
- **Numbers**: Required
- **Special Characters**: Required
- **Maximum Age**: 90 days
- **Common Patterns**: Rejected
- **Repeating Characters**: Discouraged

### Rate Limiting

- **Max Login Attempts**: 5 per IP/username combination
- **Lockout Duration**: 30 minutes
- **Rate Limit Window**: 15 minutes

### Session Security

- **Session Timeout**: 8 hours
- **Secure Cookies**: Enabled in production
- **HTTP Only**: Always enabled
- **Same Site**: Strict
- **Redis Storage**: Sessions stored in Redis for scalability

### Account Lockout

- **Failed Attempts**: 5 attempts trigger lockout
- **Lockout Duration**: 30 minutes
- **Automatic Reset**: On successful login

## Usage Examples

### Basic Authentication Check

```typescript
import { isAuthenticated, getCurrentUser } from "@/lib/auth";

export default async function ProtectedPage() {
  const authenticated = await isAuthenticated();

  if (!authenticated) {
    redirect("/login");
  }

  const user = await getCurrentUser();

  return (
    <div>
      <h1>Welcome, {user?.username}!</h1>
      <p>Role: {user?.role}</p>
    </div>
  );
}
```

### Permission-Based Access

```typescript
import { hasPermission, requirePermission } from "@/lib/auth";

export async function GET(request: NextRequest) {
  try {
    await requirePermission("domains.view");

    // User has permission, proceed with request
    return NextResponse.json({ data: "protected data" });
  } catch (error) {
    return NextResponse.json(
      { error: "Insufficient permissions" },
      { status: 403 }
    );
  }
}
```

### Middleware Usage

```typescript
import { authMiddleware, requireRole } from "@/lib/auth/middleware";

// Protect API route with role requirement
export const middleware = requireRole("admin", "super_admin");

// Or with custom permission
export const middleware = requireAuth({
  requiredPermission: "domains.edit",
});
```

### Manual Session Management

```typescript
import { authService } from "@/lib/auth";

// Get user sessions
const sessions = await authService.getUserSessions("username");

// Destroy specific session
await authService.logout("session-id");

// Destroy all user sessions
await authService.destroyAllUserSessions("username");
```

## Configuration

### Environment Variables

```bash
# Session Configuration
SESSION_SECRET=your-super-secret-session-key-change-in-production
SESSION_TIMEOUT=28800000

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Authentication Configuration
BCRYPT_ROUNDS=12
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900000
```

### Static User Configuration

Users are configured in `src/lib/auth/config.ts`:

```typescript
const AUTH_CONFIG: AuthConfigType = {
  users: [
    {
      username: "superadmin",
      password: "$argon2id$v=19$m=65536,t=3,p=4$...", // hashed
      role: "super_admin",
      permissions: ["*"],
      // ... other properties
    },
    // ... more users
  ],
  sessionTimeout: 8 * 60 * 60, // 8 hours
  maxConcurrentSessions: 5,
  maxLoginAttempts: 5,
  lockoutDuration: 30, // 30 minutes
  passwordPolicy: {
    minLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: true,
    maxAge: 90, // 90 days
  },
};
```

## Security Best Practices

1. **Change Default Passwords**: Always change default passwords in production
2. **Use Strong Session Secrets**: Generate a strong, random session secret
3. **Enable HTTPS**: Always use HTTPS in production
4. **Monitor Login Attempts**: Set up alerts for suspicious login activity
5. **Regular Password Changes**: Enforce periodic password changes
6. **Session Monitoring**: Monitor active sessions and terminate suspicious ones
7. **Audit Logging**: Log all authentication events for security auditing

## Troubleshooting

### Common Issues

1. **Session Not Persisting**

   - Check Redis connection
   - Verify session secret is set
   - Ensure cookies are enabled

2. **Login Fails with Correct Credentials**

   - Check if account is locked
   - Verify password hash is correct
   - Check rate limiting

3. **Permission Denied Errors**
   - Verify user role and permissions
   - Check middleware configuration
   - Ensure session is valid

### Debug Mode

Enable debug logging by setting `LOG_LEVEL=debug` in environment variables.

### Health Checks

The authentication system includes health checks:

```typescript
// Check Redis connection
const isRedisHealthy = await authService.checkRedisHealth();

// Check session store
const sessionCount = await authService.getActiveSessionCount();
```

## Migration Guide

### From Simple Auth to Full Auth System

1. Update imports:

   ```typescript
   // Old
   import { authenticate } from "./simple-auth";

   // New
   import { authService } from "@/lib/auth";
   ```

2. Update authentication calls:

   ```typescript
   // Old
   const user = await authenticate(username, password);

   // New
   const result = await authService.authenticate(
     username,
     password,
     ip,
     userAgent
   );
   ```

3. Update session handling:

   ```typescript
   // Old
   const session = getSession();

   // New
   const user = await getCurrentUser();
   const hasAccess = await hasPermission("permission.name");
   ```

## Testing

The authentication system includes comprehensive tests covering:

- Password validation
- User authentication
- Session management
- Role-based access control
- Rate limiting
- Concurrent sessions

Run tests with:

```bash
pnpm test src/lib/auth/__tests__/auth.test.ts
```

## Performance Considerations

- **Redis Connection Pooling**: Sessions are stored in Redis for scalability
- **Session Caching**: Iron-session provides efficient session caching
- **Rate Limiting**: In-memory rate limiting for performance
- **Password Hashing**: Argon2 with optimized parameters

## Security Audit Checklist

- [ ] Default passwords changed
- [ ] Strong session secret configured
- [ ] HTTPS enabled in production
- [ ] Rate limiting configured
- [ ] Account lockout enabled
- [ ] Password policy enforced
- [ ] Session timeout configured
- [ ] Audit logging enabled
- [ ] Redis secured
- [ ] Regular security reviews scheduled
