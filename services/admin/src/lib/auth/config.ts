import type { SessionOptions } from 'iron-session';

interface AdminUserType
{
	username: string;
	password: string; // argon2 hashed
	role: 'super_admin' | 'admin' | 'viewer';
	permissions: string[];
	createdAt: Date;
	lastLogin: Date | null;
	isActive: boolean;
	mustChangePassword: boolean;
	passwordLastChanged: Date;
	failedLoginAttempts: number;
	lockedUntil: Date | null;
}

interface AuthConfigType
{
	users: AdminUserType[];
	sessionTimeout: number; // 8 hours in seconds
	maxConcurrentSessions: number;
	maxLoginAttempts: number;
	lockoutDuration: number; // in minutes
	passwordPolicy: {
		minLength: number;
		requireUppercase: boolean;
		requireLowercase: boolean;
		requireNumbers: boolean;
		requireSpecialChars: boolean;
		maxAge: number; // days
	};
}

// Static user configuration with argon2 hashed passwords
// Default password for all users is "AdminPass123!" - should be changed in production
const AUTH_CONFIG: AuthConfigType = {
	users: [
		{
			username: 'superadmin',
			password: '$argon2id$v=19$m=65536,t=3,p=4$01Vgi9PbxFUPFRDxDjnJ2A$S56sDBplOkMJevlwNT/8iyihPEtkTLkVpscoW7B7aHU', // AdminPass123!
			role: 'super_admin',
			permissions: ['*'],
			createdAt: new Date('2024-01-01'),
			lastLogin: null,
			isActive: true,
			mustChangePassword: false,
			passwordLastChanged: new Date(), // Current date
			failedLoginAttempts: 0,
			lockedUntil: null,
		},
		{
			username: 'admin',
			password: '$argon2id$v=19$m=65536,t=3,p=4$01Vgi9PbxFUPFRDxDjnJ2A$S56sDBplOkMJevlwNT/8iyihPEtkTLkVpscoW7B7aHU', // AdminPass123!
			role: 'admin',
			permissions: [
				'dashboard.view',
				'services.view',
				'domains.view',
				'domains.edit',
				'crawl.view',
				'crawl.manage',
				'analytics.view',
				'logs.view',
				'database.view',
			],
			createdAt: new Date('2024-01-01'),
			lastLogin: null,
			isActive: true,
			mustChangePassword: false,
			passwordLastChanged: new Date(), // Current date
			failedLoginAttempts: 0,
			lockedUntil: null,
		},
		{
			username: 'viewer',
			password: '$argon2id$v=19$m=65536,t=3,p=4$01Vgi9PbxFUPFRDxDjnJ2A$S56sDBplOkMJevlwNT/8iyihPEtkTLkVpscoW7B7aHU', // AdminPass123!
			role: 'viewer',
			permissions: [
				'dashboard.view',
				'services.view',
				'domains.view',
				'analytics.view',
				'logs.view',
			],
			createdAt: new Date('2024-01-01'),
			lastLogin: null,
			isActive: true,
			mustChangePassword: false,
			passwordLastChanged: new Date(), // Current date
			failedLoginAttempts: 0,
			lockedUntil: null,
		},
	],
	sessionTimeout: 8 * 60 * 60, // 8 hours
	maxConcurrentSessions: 5,
	maxLoginAttempts: 5,
	lockoutDuration: 30, // 30 minutes
	passwordPolicy: {
		minLength: 8,
		requireUppercase: true,
		requireLowercase: true,
		requireNumbers: true,
		requireSpecialChars: true,
		maxAge: 90, // 90 days
	},
};

const sessionOptions: SessionOptions = {
	password: process.env.SESSION_SECRET || 'complex_password_at_least_32_characters_long_for_security',
	cookieName: 'admin-session',
	cookieOptions: {
		secure: process.env.NODE_ENV === 'production',
		httpOnly: true,
		sameSite: 'strict',
		maxAge: AUTH_CONFIG.sessionTimeout,
	},
};

interface SessionDataType
{
	sessionId: string;
	username: string;
	role: 'super_admin' | 'admin' | 'viewer';
	permissions: string[];
	createdAt: Date;
	lastActivity: Date;
	expiresAt: Date;
	ipAddress: string;
	userAgent: string;
}

export type { AdminUserType, AuthConfigType, SessionDataType };
export { AUTH_CONFIG, sessionOptions };
