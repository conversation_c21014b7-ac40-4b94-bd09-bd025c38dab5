import { getIronSession } from 'iron-session';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';

import { sessionOptions } from './config';

import type { SessionDataType } from './config';

interface IronSessionDataType
{
	sessionId?: string;
	username?: string;
	role?: string;
	permissions?: string[];
	isLoggedIn?: boolean;
}

async function getSession()
{
	const cookieStore = await cookies();
	const session = await getIronSession<IronSessionDataType>(cookieStore, sessionOptions);
	return session;
}

async function getSessionFromRequest(request: NextRequest)
{
	const response = new NextResponse();
	const cookieStore = await cookies();
	const session = await getIronSession<IronSessionDataType>(request, response, sessionOptions);
	return { session, response };
}

async function createSession(sessionData: SessionDataType)
{
	const session = await getSession();

	session.sessionId = sessionData.sessionId;
	session.username = sessionData.username;
	session.role = sessionData.role;
	session.permissions = sessionData.permissions;
	session.isLoggedIn = true;

	await session.save();
}

async function destroySession()
{
	const session = await getSession();
	session.destroy();
}

async function isAuthenticated(): Promise<boolean>
{
	const session = await getSession();
	return !!session.isLoggedIn && !!session.sessionId;
}

async function getCurrentUser(): Promise<{
	username: string;
	role: string;
	permissions: string[];
} | null>
{
	const session = await getSession();

	if (!session.isLoggedIn || !session.username)
	{
		return null;
	}

	return {
		username: session.username,
		role: session.role || 'viewer',
		permissions: session.permissions || [],
	};
}

async function hasPermission(permission: string): Promise<boolean>
{
	const user = await getCurrentUser();

	if (!user)
	{
		return false;
	}

	// Super admin has all permissions
	if (user.permissions.includes('*'))
	{
		return true;
	}

	return user.permissions.includes(permission);
}

async function requirePermission(permission: string): Promise<void>
{
	const hasAccess = await hasPermission(permission);

	if (!hasAccess)
	{
		throw new Error(`Insufficient permissions. Required: ${permission}`);
	}
}

async function validateSession(request?: NextRequest): Promise<{
	username: string;
	role: string;
	permissions: string[];
} | null>
{
	// If a request is provided, resolve session from request for backward compatibility
	if (request)
	{
		const { session } = await getSessionFromRequest(request);
		if (!session.isLoggedIn || !session.username)
		{
				return null;
		}

		return ({
			username: session.username,
			role: session.role || 'viewer',
			permissions: session.permissions || [],
		});
	}

	// Default path: read from server cookies
	return getCurrentUser();
}

export type { IronSessionDataType };

export {
	getSession,
	getSessionFromRequest,
	createSession,
	destroySession,
	isAuthenticated,
	getCurrentUser,
	hasPermission,
	requirePermission,
	validateSession,
};
