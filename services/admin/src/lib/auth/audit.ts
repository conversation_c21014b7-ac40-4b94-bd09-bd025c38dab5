import db from '@/lib/database/client';
import { createIsomorphicLogger } from '@shared/client';
import AlertService from '@/lib/alerts/AlertService';
import type { AuditLogType, SecurityAlertType } from '@/types/auth';
import type { AlertInstanceType, ExternalIntegrationType, NotificationTemplateType } from '@/types/alerts';


const logger = createIsomorphicLogger('auth-audit');

export async function logUserActivity(
	userId: string,
	action: string,
	resource: string,
	success: boolean,
	details: Record<string, any> = {},
	ipAddress?: string,
	userAgent?: string,
): Promise<void>
{
	try
	{
		const activityId = crypto.randomUUID();

		await db.mariadb.query(`
      INSERT INTO user_activity (
        id, user_id, action, resource, timestamp, ip_address,
        user_agent, success, details, session_id
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
			activityId,
			userId,
			action,
			resource,
			new Date(),
			ipAddress || 'unknown',
			userAgent || 'unknown',
			success ? 1 : 0,
			JSON.stringify(details),
			details.sessionId || null,
		]);
	}
	catch (error)
	{
		logger.error({ error, userId, action, resource }, 'Error logging user activity');
		// Don't throw - logging failures shouldn't break the main operation
	}
}

export async function logAuditEvent(
	userId: string,
	action: string,
	resource: string,
	resourceId: string | null,
	oldValues: Record<string, any> | null,
	newValues: Record<string, any> | null,
	ipAddress?: string,
	userAgent?: string,
	severity: 'low' | 'medium' | 'high' | 'critical' = 'medium',
): Promise<void>
{
	try
	{
		// Get username
		const userResult = await db.mariadb.query(
			'SELECT username FROM admin_users WHERE id = ?',
			[userId],
		);
		const username = (userResult as any)[0]?.username || 'unknown';

		const auditId = crypto.randomUUID();

		await db.mariadb.query(`
      INSERT INTO audit_logs (
        id, user_id, username, action, resource, resource_id,
        old_values, new_values, timestamp, ip_address, user_agent,
        success, severity
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
			auditId,
			userId,
			username,
			action,
			resource,
			resourceId,
			oldValues ? JSON.stringify(oldValues) : null,
			newValues ? JSON.stringify(newValues) : null,
			new Date(),
			ipAddress || 'unknown',
			userAgent || 'unknown',
			1, // success - we only log successful audit events
			severity,
		]);

		// Check if this action should trigger security alerts
		await checkSecurityAlerts(userId, username, action, resource, ipAddress, userAgent);
	}
	catch (error)
	{
		logger.error({ error, userId, action, resource }, 'Error logging audit event');
		// Don't throw - logging failures shouldn't break the main operation
	}
}

export async function logSecurityAlert(
	type: SecurityAlertType['type'],
	description: string,
	severity: SecurityAlertType['severity'],
	userId?: string,
	username?: string,
	ipAddress?: string,
	userAgent?: string,
	metadata: Record<string, any> = {},
): Promise<void>
{
	try
	{
		const alertId = crypto.randomUUID();

		await db.mariadb.query(`
      INSERT INTO security_alerts (
        id, type, user_id, username, description, severity,
        timestamp, ip_address, user_agent, resolved, metadata
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
			alertId,
			type,
			userId || null,
			username || null,
			description,
			severity,
			new Date(),
			ipAddress || 'unknown',
			userAgent || 'unknown',
			0, // not resolved
			JSON.stringify(metadata),
		]);

		// For critical alerts, we might want to send immediate notifications
		if (severity === 'critical')
		{
			// Immediate notification for critical security alerts
			logger.warn({ description, metadata }, 'CRITICAL SECURITY ALERT');

			try
			{
				const alertService = AlertService.getInstance();

				// Create alert instance for critical security event (AlertInstanceType)
				const criticalAlert: AlertInstanceType = {
					id: `security-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
					ruleId: 'security-critical-alert',
					ruleName: 'Critical Security Alert',
					severity: 'critical',
					status: 'active',
					message: `Critical Security Alert: ${type} - ${description}`,
					details: {
						userId,
						alertType: type,
						timestamp: new Date().toISOString(),
						...metadata,
					},
					triggeredAt: new Date(),
					escalationLevel: 0,
					notificationsSent: [],
					tags: ['security', 'critical', type],
					correlationId: crypto.randomUUID(),
					affectedServices: ['admin-panel'],
					metrics: {},
				};

				// Create notification template for critical security alerts
				const template: NotificationTemplateType = {
					id: 'critical-security-template',
					name: 'Critical Security Alert',
					channel: 'email',
					subject: 'CRITICAL SECURITY ALERT: {{message}}',
					body: [
						'Severity: {{severity}}',
						'Triggered: {{triggeredAt}}',
						'Message: {{message}}',
						'Services: {{serviceName}}',
						'Alert ID: {{alertId}}',
						'Correlation: {{correlationId}}',
					].join('\n'),
					variables: ['severity', 'triggeredAt', 'message', 'serviceName', 'alertId', 'correlationId'],
					isDefault: false,
					createdAt: new Date(),
					updatedAt: new Date(),
				};

				// Send to security team via email (mock integration)
				const emailIntegration: ExternalIntegrationType = {
					id: 'security-email',
					type: 'email',
					name: 'Security Team Email',
					enabled: true,
					status: 'connected',
					config: {
						recipients: ['<EMAIL>'],
						from: '<EMAIL>',
						priority: 'high',
					},
				};

				// Send immediate notification
				await alertService.sendNotification(criticalAlert, template, emailIntegration);

				logger.info({
					alertId: criticalAlert.id,
					type,
					userId
				}, 'Critical security alert notification sent');
			}
			catch (notificationError)
			{
				logger.error({
					error: notificationError instanceof Error ? notificationError.message : String(notificationError),
					type,
					userId
				}, 'Failed to send critical security alert notification');
			}
		}
	}
	catch (error)
	{
		logger.error({ error, userId, type, description }, 'Error logging security alert');
	}
}

async function checkSecurityAlerts(
	userId: string,
	username: string,
	action: string,
	resource: string,
	ipAddress?: string,
	userAgent?: string,
): Promise<void>
{
	try
	{
		// Check for permission escalation attempts
		if (action.includes('permission') || action.includes('role'))
		{
			await logSecurityAlert(
				'permission_escalation',
        `User ${username} modified permissions or roles`,
        'high',
        userId,
        username,
        ipAddress,
        userAgent,
        { action, resource },
			);
		}

		// Check for bulk user operations
		if (action.includes('bulk') || action.includes('delete'))
		{
			await logSecurityAlert(
				'suspicious_activity',
        `User ${username} performed bulk operation: ${action}`,
        'medium',
        userId,
        username,
        ipAddress,
        userAgent,
        { action, resource },
			);
		}

		// Check for unusual login patterns (multiple IPs in short time)
		if (action === 'auth.login')
		{
			const recentLogins = await db.mariadb.query(`
        SELECT DISTINCT ip_address
        FROM user_activity
        WHERE user_id = ? AND action = 'auth.login'
        AND timestamp > DATE_SUB(NOW(), INTERVAL 1 HOUR)
        AND success = 1
      `, [userId]);

			if (recentLogins.length > 3)
			{
				await logSecurityAlert(
					'suspicious_activity',
          `User ${username} logged in from multiple IP addresses within 1 hour`,
          'medium',
          userId,
          username,
          ipAddress,
          userAgent,
          { recentIPs: recentLogins.map((r: any) => r.ip_address) },
				);
			}
		}
	}
	catch (error)
	{
		logger.error({ error }, 'Error checking security alerts');
	}
}

export async function getRecentActivity(
	userId: string,
	limit: number = 50,
): Promise<any[]>
{
	try
	{
		const activities = await db.mariadb.query(`
      SELECT action, resource, timestamp, ip_address, success, details
      FROM user_activity
      WHERE user_id = ?
      ORDER BY timestamp DESC
      LIMIT ?
    `, [userId, limit]);

		return activities.map((activity: any) => ({
			action: activity.action,
			resource: activity.resource,
			timestamp: new Date(activity.timestamp),
			ipAddress: activity.ip_address,
			success: Boolean(activity.success),
			details: JSON.parse(activity.details || '{}'),
		}));
	}
	catch (error)
	{
		logger.error({ error, userId }, 'Error fetching recent activity');
		return [];
	}
}

export async function getSecurityAlerts(
	resolved: boolean = false,
	limit: number = 100,
): Promise<SecurityAlertType[]>
{
	try
	{
		const alerts = await db.mariadb.query(`
      SELECT
        id, type, user_id, username, description, severity,
        timestamp, ip_address, user_agent, resolved,
        resolved_by, resolved_at, metadata
      FROM security_alerts
      WHERE resolved = ?
      ORDER BY timestamp DESC
      LIMIT ?
    `, [resolved ? 1 : 0, limit]);

		return alerts.map((alert: any) => ({
			id: alert.id,
			type: alert.type,
			userId: alert.user_id,
			username: alert.username,
			description: alert.description,
			severity: alert.severity,
			timestamp: new Date(alert.timestamp),
			ipAddress: alert.ip_address,
			userAgent: alert.user_agent,
			resolved: Boolean(alert.resolved),
			resolvedBy: alert.resolved_by,
			resolvedAt: alert.resolved_at ? new Date(alert.resolved_at) : undefined,
			metadata: JSON.parse(alert.metadata || '{}'),
		}));
	}
	catch (error)
	{
		logger.error({ error }, 'Error fetching security alerts');
		return [];
	}
}

export async function resolveSecurityAlert(
	alertId: string,
	resolvedBy: string,
): Promise<void>
{
	try
	{
		await db.mariadb.query(`
      UPDATE security_alerts
      SET resolved = 1, resolved_by = ?, resolved_at = ?
      WHERE id = ?
    `, [resolvedBy, new Date(), alertId]);
	}
	catch (error)
	{
		logger.error({ error, alertId }, 'Error resolving security alert');
		throw error;
	}
}
