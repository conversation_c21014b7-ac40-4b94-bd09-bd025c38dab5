import { NextRequest, NextResponse } from 'next/server';
import { createIsomorphicLogger } from '@shared/client';

import { authService } from './service';
import { getSessionFromRequest } from './session';

const logger = createIsomorphicLogger('admin-auth');

interface AuthMiddlewareOptionsType
{
	requiredPermission?: string;
	allowedRoles?: string[];
}

async function authMiddleware(
	request: NextRequest,
	options: AuthMiddlewareOptionsType = {},
): Promise<NextResponse | null>
{
	try
	{
		const { session, response } = await getSessionFromRequest(request);

		// Check if user is logged in
		if (!session.isLoggedIn || !session.sessionId)
		{
			return NextResponse.json(
				{ error: 'Authentication required' },
				{ status: 401 },
			);
		}

		// Validate session with auth service
		const sessionData = await authService.validateSession(session.sessionId);

		if (!sessionData)
		{
			// Session is invalid, destroy it
			session.destroy();
			await session.save();

			return NextResponse.json(
				{ error: 'Session expired' },
				{ status: 401 },
			);
		}

		// Check role-based access
		if (options.allowedRoles && !options.allowedRoles.includes(sessionData.role))
		{
			return NextResponse.json(
				{ error: 'Insufficient permissions' },
				{ status: 403 },
			);
		}

		// Check permission-based access
		if (options.requiredPermission)
		{
			const hasPermission = sessionData.permissions.includes('*') ||
                           sessionData.permissions.includes(options.requiredPermission);

			if (!hasPermission)
			{
				return NextResponse.json(
					{ error: `Permission required: ${options.requiredPermission}` },
					{ status: 403 },
				);
			}
		}

		// Add user info to request headers for API routes
		const requestHeaders = new Headers(request.headers);
		requestHeaders.set('x-user-id', sessionData.username);
		requestHeaders.set('x-user-role', sessionData.role);
		requestHeaders.set('x-user-permissions', JSON.stringify(sessionData.permissions));

		return NextResponse.next({
			request: {
				headers: requestHeaders,
			},
		});
	}
	catch (error)
	{
		logger.error('Auth middleware error', {
			error: error instanceof Error ? error.message : String(error),
			path: request.nextUrl.pathname,
			method: request.method,
		});
		return NextResponse.json(
			{ error: 'Authentication error' },
			{ status: 500 },
		);
	}
}

function requireAuth(options: AuthMiddlewareOptionsType = {})
{
	return (request: NextRequest) => authMiddleware(request, options);
}

function requirePermission(permission: string)
{
	return requireAuth({ requiredPermission: permission });
}

function requireRole(...roles: string[])
{
	return requireAuth({ allowedRoles: roles });
}

export type { AuthMiddlewareOptionsType };
export {
	authMiddleware, requireAuth, requirePermission, requireRole,
};
