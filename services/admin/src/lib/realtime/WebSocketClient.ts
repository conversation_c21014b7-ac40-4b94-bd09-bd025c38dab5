import { ConnectionManager } from './ConnectionManager';
import { realtimeLogger } from '../logger';

import type {
	RealtimeMessageType,
	RealtimeSubscriptionType,
	RealtimeConfigType,
	ConnectionInfoType,
} from './types';

export class WebSocketClient
{
	private websocket: WebSocket | null = null;

	private connectionManager: ConnectionManager;

	private subscriptions: Map<string, RealtimeSubscriptionType> = new Map();

	private config: RealtimeConfigType;

	private connectionId: string;

	private messageBuffer: RealtimeMessageType[] = [];

	private batchTimeout: NodeJS.Timeout | null = null;

	private pingInterval: NodeJS.Timeout | null = null;

	constructor(config: RealtimeConfigType, connectionManager: ConnectionManager)
	{
		this.config = config;
		this.connectionManager = connectionManager;
		this.connectionId = `ws-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
	}

	public async connect(endpoint: string): Promise<void>
	{
		if (this.websocket?.readyState === WebSocket.OPEN)
		{
			return;
		}

		this.disconnect();

		const connection = this.connectionManager.createConnection(this.connectionId);

		try
		{
			this.websocket = new WebSocket(endpoint);
			this.setupEventListeners();

			return await new Promise((resolve, reject) =>
			{
				const timeout = setTimeout(() =>
				{
					reject(new Error('WebSocket connection timeout'));
				}, 10000);

				this.websocket!.onopen = () =>
				{
					clearTimeout(timeout);
					this.connectionManager.updateConnectionStatus(this.connectionId, 'connected');
					this.startPingInterval();
					resolve();
				};

				this.websocket!.onerror = (error) =>
				{
					clearTimeout(timeout);
					this.connectionManager.updateConnectionStatus(this.connectionId, 'error');
					this.connectionManager.incrementErrorCount();
					reject(error);
				};
			});
		}
		catch (error)
		{
			this.connectionManager.updateConnectionStatus(this.connectionId, 'error');
			throw error;
		}
	}

	public disconnect(): void
	{
		if (this.websocket)
		{
			this.websocket.close();
			this.websocket = null;
		}

		if (this.batchTimeout)
		{
			clearTimeout(this.batchTimeout);
			this.batchTimeout = null;
		}

		if (this.pingInterval)
		{
			clearInterval(this.pingInterval);
			this.pingInterval = null;
		}

		this.connectionManager.updateConnectionStatus(this.connectionId, 'disconnected');
		this.flushMessageBuffer();
	}

	public subscribe(
		type: string,
		callback: (message: RealtimeMessageType) => void,
		filters?: Record<string, any>,
	): string
	{
		const subscriptionId = `${type}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

		const subscription: RealtimeSubscriptionType = {
			id: subscriptionId,
			type,
			filters,
			callback,
			active: true,
			createdAt: new Date(),
		};

		this.subscriptions.set(subscriptionId, subscription);

		// Send subscription message to server
		this.sendMessage({
			type: 'subscribe',
			data: {
				subscriptionId,
				messageType: type,
				filters,
			},
		});

		return subscriptionId;
	}

	public unsubscribe(subscriptionId: string): void
	{
		const subscription = this.subscriptions.get(subscriptionId);
		if (subscription)
		{
			subscription.active = false;
			this.subscriptions.delete(subscriptionId);

			// Send unsubscribe message to server
			this.sendMessage({
				type: 'unsubscribe',
				data: {
					subscriptionId,
				},
			});
		}
	}

	public sendMessage(message: any): void
	{
		if (this.websocket?.readyState === WebSocket.OPEN)
		{
			const messageStr = JSON.stringify(message);
			this.websocket.send(messageStr);
			this.connectionManager.incrementMessageCount('sent', messageStr.length);
		}
	}

	public getConnectionInfo(): ConnectionInfoType | undefined
	{
		return this.connectionManager.getConnection(this.connectionId);
	}

	public isConnected(): boolean
	{
		return this.websocket?.readyState === WebSocket.OPEN;
	}

	private setupEventListeners(): void
	{
		if (!this.websocket) return;

		this.websocket.onmessage = (event) =>
		{
			this.handleMessage(event);
		};

		this.websocket.onerror = () =>
		{
			this.connectionManager.updateConnectionStatus(this.connectionId, 'error');
			this.connectionManager.incrementErrorCount();
		};

		this.websocket.onopen = () =>
		{
			this.connectionManager.updateConnectionStatus(this.connectionId, 'connected');
		};

		this.websocket.onclose = () =>
		{
			this.connectionManager.updateConnectionStatus(this.connectionId, 'disconnected');
			this.stopPingInterval();
		};
	}

	private handleMessage(event: MessageEvent): void
	{
		try
		{
			const rawMessage = JSON.parse(event.data);

			// Handle ping/pong for latency measurement
			if (rawMessage.type === 'pong')
			{
				const latency = Date.now() - rawMessage.timestamp;
				this.connectionManager.updateLatency(this.connectionId, latency);
				return;
			}

			const message: RealtimeMessageType = {
				type: rawMessage.type,
				timestamp: new Date(rawMessage.timestamp || Date.now()),
				data: rawMessage.data,
				correlationId: rawMessage.correlationId,
				source: rawMessage.source || 'websocket',
			};

			this.connectionManager.incrementMessageCount('received', event.data.length);

			if (this.config.batchInterval > 0)
			{
				this.bufferMessage(message);
			}
			else
			{
				this.processMessage(message);
			}
		}
		catch (error)
		{
			realtimeLogger.error({ error }, 'Failed to parse WebSocket message');
			this.connectionManager.incrementErrorCount();
		}
	}

	private bufferMessage(message: RealtimeMessageType): void
	{
		this.messageBuffer.push(message);

		if (this.messageBuffer.length >= this.config.bufferSize)
		{
			this.flushMessageBuffer();
		}
		else if (!this.batchTimeout)
		{
			this.batchTimeout = setTimeout(() =>
			{
				this.flushMessageBuffer();
			}, this.config.batchInterval);
		}
	}

	private flushMessageBuffer(): void
	{
		if (this.batchTimeout)
		{
			clearTimeout(this.batchTimeout);
			this.batchTimeout = null;
		}

		const messages = [...this.messageBuffer];
		this.messageBuffer = [];

		messages.forEach(message => this.processMessage(message));
	}

	private processMessage(message: RealtimeMessageType): void
	{
		this.connectionManager.incrementMessageCount('processed');

		// Find matching subscriptions
		const matchingSubscriptions = Array.from(this.subscriptions.values())
			.filter(sub => sub.active && sub.type === message.type)
			.filter(sub => this.matchesFilters(message, sub.filters));

		// Deliver message to subscribers
		matchingSubscriptions.forEach((subscription) =>
		{
			try
			{
				subscription.callback(message);
			}
			catch (error)
			{
				realtimeLogger.error({ error }, 'Error in subscription callback');
				this.connectionManager.incrementErrorCount();
			}
		});
	}

	private matchesFilters(message: RealtimeMessageType, filters?: Record<string, any>): boolean
	{
		if (!filters) return true;

		return Object.entries(filters).every(([key, value]) =>
		{
			const messageValue = this.getNestedValue(message.data, key);

			if (Array.isArray(value))
			{
				return value.includes(messageValue);
			}

			return messageValue === value;
		});
	}

	private getNestedValue(obj: any, path: string): any
	{
		return path.split('.').reduce((current, key) => current?.[key], obj);
	}

	private startPingInterval(): void
	{
		this.pingInterval = setInterval(() =>
		{
			if (this.websocket?.readyState === WebSocket.OPEN)
			{
				this.sendMessage({
					type: 'ping',
					timestamp: Date.now(),
				});
			}
		}, this.config.heartbeatInterval);
	}

	private stopPingInterval(): void
	{
		if (this.pingInterval)
		{
			clearInterval(this.pingInterval);
			this.pingInterval = null;
		}
	}
}

export default WebSocketClient;
