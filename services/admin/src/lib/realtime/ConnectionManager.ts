import type {
	ConnectionInfoType,
	ConnectionStatusType,
	ConnectionQualityType,
	RealtimeConfigType,
	RealtimeStatsType,
} from './types';

export class ConnectionManager
{
	private connections: Map<string, ConnectionInfoType> = new Map();

	private stats: RealtimeStatsType;

	private config: RealtimeConfigType;

	private heartbeatIntervals: Map<string, NodeJS.Timeout> = new Map();

	private reconnectTimeouts: Map<string, NodeJS.Timeout> = new Map();

	constructor(config: RealtimeConfigType)
	{
		this.config = config;
		this.stats = {
			messagesReceived: 0,
			messagesProcessed: 0,
			messagesSent: 0,
			bytesReceived: 0,
			bytesSent: 0,
			averageLatency: 0,
			connectionUptime: 0,
			reconnectCount: 0,
			errorCount: 0,
		};
	}

	public createConnection(connectionId: string): ConnectionInfoType
	{
		const connection: ConnectionInfoType = {
			status: 'connecting',
			quality: 'good',
			latency: 0,
			lastPing: null,
			reconnectAttempts: 0,
			maxReconnectAttempts: this.config.maxReconnectAttempts,
			uptime: 0,
		};

		this.connections.set(connectionId, connection);
		this.startHeartbeat(connectionId);

		return connection;
	}

	public updateConnectionStatus(connectionId: string, status: ConnectionStatusType): void
	{
		const connection = this.connections.get(connectionId);
		if (!connection) return;

		const previousStatus = connection.status;
		connection.status = status;

		if (status === 'connected' && previousStatus !== 'connected')
		{
			connection.reconnectAttempts = 0;
			connection.uptime = Date.now();
		}
		else if (status === 'disconnected' || status === 'error')
		{
			this.scheduleReconnect(connectionId);
		}

		this.updateConnectionQuality(connectionId);
	}

	public updateLatency(connectionId: string, latency: number): void
	{
		const connection = this.connections.get(connectionId);
		if (!connection) return;

		connection.latency = latency;
		connection.lastPing = new Date();
		this.updateConnectionQuality(connectionId);
		this.updateAverageLatency(latency);
	}

	public getConnection(connectionId: string): ConnectionInfoType | undefined
	{
		return this.connections.get(connectionId);
	}

	public getAllConnections(): Map<string, ConnectionInfoType>
	{
		return new Map(this.connections);
	}

	public getStats(): RealtimeStatsType
	{
		return { ...this.stats };
	}

	public incrementMessageCount(type: 'received' | 'processed' | 'sent', bytes: number = 0): void
	{
		switch (type)
		{
			case 'received':
				this.stats.messagesReceived++;
				this.stats.bytesReceived += bytes;
				break;
			case 'processed':
				this.stats.messagesProcessed++;
				break;
			case 'sent':
				this.stats.messagesSent++;
				this.stats.bytesSent += bytes;
				break;
		}
	}

	public incrementErrorCount(): void
	{
		this.stats.errorCount++;
	}

	public destroy(): void
	{
		// Clear all intervals and timeouts
		this.heartbeatIntervals.forEach(interval => clearInterval(interval));
		this.reconnectTimeouts.forEach(timeout => clearTimeout(timeout));

		this.heartbeatIntervals.clear();
		this.reconnectTimeouts.clear();
		this.connections.clear();
	}

	private startHeartbeat(connectionId: string): void
	{
		const interval = setInterval(() =>
		{
			this.sendHeartbeat(connectionId);
		}, this.config.heartbeatInterval);

		this.heartbeatIntervals.set(connectionId, interval);
	}

	private sendHeartbeat(connectionId: string): void
	{
		const connection = this.connections.get(connectionId);
		if (!connection || connection.status !== 'connected') return;

		const startTime = Date.now();

		// Simulate heartbeat - in real implementation, this would ping the server
		setTimeout(() =>
		{
			const latency = Date.now() - startTime;
			this.updateLatency(connectionId, latency);
		}, Math.random() * 50); // Simulate network delay
	}

	private scheduleReconnect(connectionId: string): void
	{
		const connection = this.connections.get(connectionId);
		if (!connection) return;

		if (connection.reconnectAttempts >= connection.maxReconnectAttempts)
		{
			connection.status = 'error';
			return;
		}

		connection.status = 'reconnecting';
		connection.reconnectAttempts++;
		this.stats.reconnectCount++;

		const delay = Math.min(
			1000 * 2 ** connection.reconnectAttempts, // Exponential backoff
			30000, // Max 30 seconds
		);

		const timeout = setTimeout(() =>
		{
			this.attemptReconnect(connectionId);
		}, delay);

		this.reconnectTimeouts.set(connectionId, timeout);
	}

	private attemptReconnect(connectionId: string): void
	{
		const connection = this.connections.get(connectionId);
		if (!connection) return;

		// In real implementation, this would attempt to reconnect
		// For now, we'll simulate a successful reconnection
		if (Math.random() > 0.3) // 70% success rate
		{
			this.updateConnectionStatus(connectionId, 'connected');
		}
		else
		{
			this.scheduleReconnect(connectionId);
		}
	}

	private updateConnectionQuality(connectionId: string): void
	{
		const connection = this.connections.get(connectionId);
		if (!connection) return;

		if (connection.status !== 'connected')
		{
			connection.quality = 'critical';
			return;
		}

		if (connection.latency < 100)
		{
			connection.quality = 'excellent';
		}
		else if (connection.latency < 300)
		{
			connection.quality = 'good';
		}
		else if (connection.latency < 1000)
		{
			connection.quality = 'poor';
		}
		else
		{
			connection.quality = 'critical';
		}
	}

	private updateAverageLatency(newLatency: number): void
	{
		const alpha = 0.1; // Smoothing factor
		this.stats.averageLatency =
      this.stats.averageLatency * (1 - alpha) + newLatency * alpha;
	}
}

export default ConnectionManager;
