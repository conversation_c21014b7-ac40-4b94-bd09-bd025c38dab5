import { getServiceHealthMonitor } from './ServiceHealthMonitor';
import { realtimeLogger } from '../logger';

// Initialize real-time monitoring on server startup
function initializeRealtimeMonitoring()
{
    realtimeLogger.info('Initializing real-time monitoring...');

	try
	{
		// Start service health monitoring
		const healthMonitor = getServiceHealthMonitor();
		realtimeLogger.info('Service health monitor started');

		// Set up cleanup on process exit
		process.on('SIGINT', () =>
		{
			realtimeLogger.info('Shutting down real-time monitoring...');
			healthMonitor.stop();
			process.exit(0);
		});

		process.on('SIGTERM', () =>
		{
			realtimeLogger.info('Shutting down real-time monitoring...');
			healthMonitor.stop();
			process.exit(0);
		});

		realtimeLogger.info('Real-time monitoring initialized successfully');
	}
	catch (error)
	{
		realtimeLogger.error({ error }, 'Failed to initialize real-time monitoring');
	}
}

export default initializeRealtimeMonitoring;
