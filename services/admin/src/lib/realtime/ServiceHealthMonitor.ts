import { createIsomorphicLogger } from '@shared/client';
import BrowserHttpClient from '@/lib/utils/BrowserHttpClient';

import type { ServiceStatusDataType, DatabaseStatusType } from '@/types/services';

export interface ServiceHealthConfigType
{
	services: Array<{
		name: string;
		healthEndpoint: string;
		port: number;
		checkInterval: number;
	}>;
	databases: Array<{
		name: string;
		type: 'scylla' | 'mariadb' | 'redis' | 'manticore';
		checkInterval: number;
	}>;
}

export class ServiceHealthMonitor
{
	private config: ServiceHealthConfigType;

	private intervals: Map<string, NodeJS.Timeout> = new Map();

	private lastStatuses: Map<string, ServiceStatusDataType | DatabaseStatusType> = new Map();

	private isRunning: boolean = false;

	private httpClient: BrowserHttpClient;
	private logger = createIsomorphicLogger('ServiceHealthMonitor');

	constructor(config: ServiceHealthConfigType)
	{
		this.config = config;
		this.httpClient = new BrowserHttpClient({ timeout: 5000 });
	}

	public start(): void
	{
		if (this.isRunning) return;

		this.isRunning = true;

		// Start monitoring services
		this.config.services.forEach((service) =>
		{
			this.startServiceMonitoring(service);
		});

		// Start monitoring databases
		this.config.databases.forEach((database) =>
		{
			this.startDatabaseMonitoring(database);
		});
	}

	public stop(): void
	{
		if (!this.isRunning) return;

		this.isRunning = false;

		// Clear all intervals
		this.intervals.forEach(interval => clearInterval(interval));
		this.intervals.clear();
	}

	private startServiceMonitoring(service: {
		name: string;
		healthEndpoint: string;
		port: number;
		checkInterval: number;
	}): void
	{
		type ServiceHealthResponse = {
			uptime?: number;
			version?: string;
			metrics?: {
				cpu?: number;
				memory?: number;
				requests?: number;
				errors?: number;
			};
		};

		const checkHealth = async () =>
		{
			try
			{
				const startTime = Date.now();
				const response = await this.httpClient.get(`http://localhost:${service.port}${service.healthEndpoint}`);

				const responseTime = Date.now() - startTime;
				const data = response.data as ServiceHealthResponse;

				const status: ServiceStatusDataType =
				{
					name: service.name,
					status: (response.status >= 200 && response.status < 300) ? 'healthy' : 'unhealthy',
					uptime: data.uptime || 0,
					responseTime,
					lastCheck: new Date(),
					version: data.version || 'unknown',
					metrics: {
						cpu: data.metrics?.cpu ?? 0,
						memory: data.metrics?.memory ?? 0,
						requests: data.metrics?.requests ?? 0,
						errors: data.metrics?.errors ?? 0,
					},
				};

				const previousStatus = this.lastStatuses.get(service.name) as ServiceStatusDataType;

				// Only broadcast if status changed
				if (!previousStatus || previousStatus.status !== status.status)
				{
					this.logger.info('Service health status changed', {
						serviceName: service.name,
						status: status.status,
						previousStatus: previousStatus?.status,
					});
				}

				this.lastStatuses.set(service.name, status);
			}
			catch (error)
			{
				const status: ServiceStatusDataType =
				{
					name: service.name,
					status: 'unhealthy',
					uptime: 0,
					responseTime: 0,
					lastCheck: new Date(),
					version: 'unknown',
					metrics: {
						cpu: 0,
						memory: 0,
						requests: 0,
						errors: 0,
					},
				};

				const previousStatus = this.lastStatuses.get(service.name) as ServiceStatusDataType;

				if (!previousStatus || previousStatus.status !== status.status)
				{
					this.logger.info('Service health status changed', {
						serviceName: service.name,
						status: status.status,
						previousStatus: previousStatus?.status,
					});
				}

				this.lastStatuses.set(service.name, status);
			}
		};

		// Initial check
		checkHealth();

		// Set up interval
		const interval = setInterval(checkHealth, service.checkInterval);
		this.intervals.set(`service-${service.name}`, interval);
	}

	private startDatabaseMonitoring(database: {
		name: string;
		type: 'scylla' | 'mariadb' | 'redis' | 'manticore';
		checkInterval: number;
	}): void
	{
		type DatabaseHealthResponse = {
			connected?: boolean;
			connectionPool?: {
				active?: number;
				idle?: number;
				total?: number;
			};
			metrics?: {
				queries?: number;
				errors?: number;
				slowQueries?: number;
			};
		};

		const checkHealth = async () =>
		{
			try
			{
				const startTime = Date.now();

				// Make request to database health endpoint
				const response = await this.httpClient.get(`/api/database/health?db=${database.name}`);

				const responseTime = Date.now() - startTime;
				const data = response.data as DatabaseHealthResponse;

				const status: DatabaseStatusType =
				{
					name: database.type,
					connected: data.connected || false,
					responseTime,
					connectionPool: {
						active: data.connectionPool?.active ?? 0,
						idle: data.connectionPool?.idle ?? 0,
						total: data.connectionPool?.total ?? 0,
					},
					metrics: {
						queries: data.metrics?.queries ?? 0,
						errors: data.metrics?.errors ?? 0,
						slowQueries: data.metrics?.slowQueries ?? 0,
					},
				};

				const previousStatus = this.lastStatuses.get(database.name) as DatabaseStatusType;

				// Only broadcast if connection status changed
				if (!previousStatus || previousStatus.connected !== status.connected)
				{
					this.logger.info('Database health status changed', {
						databaseName: database.name,
						connected: status.connected,
						previousConnected: previousStatus?.connected,
					});
				}

				this.lastStatuses.set(database.name, status);
			}
			catch (error)
			{
				const status: DatabaseStatusType =
				{
					name: database.type,
					connected: false,
					responseTime: 0,
					connectionPool: {
						active: 0,
						idle: 0,
						total: 0,
					},
					metrics: {
						queries: 0,
						errors: 0,
						slowQueries: 0,
					},
				};

				const previousStatus = this.lastStatuses.get(database.name) as DatabaseStatusType;

				if (!previousStatus || previousStatus.connected !== status.connected)
				{
					this.logger.info('Database health status changed', {
						databaseName: database.name,
						connected: status.connected,
						previousConnected: previousStatus?.connected,
					});
				}

				this.lastStatuses.set(database.name, status);
			}
		};

		// Initial check
		checkHealth();

		// Set up interval
		const interval = setInterval(checkHealth, database.checkInterval);
		this.intervals.set(`database-${database.name}`, interval);
	}

	public getCurrentStatuses(): Map<string, ServiceStatusDataType | DatabaseStatusType>
	{
		return new Map(this.lastStatuses);
	}

	public forceCheck(serviceName?: string): void
	{
		if (serviceName)
		{
			const service = this.config.services.find(s => s.name === serviceName);
			if (service)
			{
				this.startServiceMonitoring(service);
			}

			const database = this.config.databases.find(d => d.name === serviceName);
			if (database)
			{
				this.startDatabaseMonitoring(database);
			}
		}
		else
		{
			// Force check all services and databases
			this.config.services.forEach(service => this.startServiceMonitoring(service));
			this.config.databases.forEach(database => this.startDatabaseMonitoring(database));
		}
	}
}

// Global instance
let serviceHealthMonitor: ServiceHealthMonitor | null = null;

export function getServiceHealthMonitor(): ServiceHealthMonitor
{
	if (!serviceHealthMonitor)
	{
		const config: ServiceHealthConfigType = {
			services: [
				{
					name: 'web-app',
					healthEndpoint: '/health',
					port: 3000,
					checkInterval: 30000, // 30 seconds
				},
				{
					name: 'crawler',
					healthEndpoint: '/health',
					port: 3001,
					checkInterval: 30000,
				},
				{
					name: 'ranking-engine',
					healthEndpoint: '/health',
					port: 3002,
					checkInterval: 30000,
				},
				{
					name: 'scheduler',
					healthEndpoint: '/health',
					port: 3003,
					checkInterval: 30000,
				},
				{
					name: 'domain-seeder',
					healthEndpoint: '/health',
					port: 3005,
					checkInterval: 30000,
				},
			],
			databases: [
				{
					name: 'scylla',
					type: 'scylla',
					checkInterval: 60000, // 1 minute
				},
				{
					name: 'mariadb',
					type: 'mariadb',
					checkInterval: 60000,
				},
				{
					name: 'redis',
					type: 'redis',
					checkInterval: 60000,
				},
				{
					name: 'manticore',
					type: 'manticore',
					checkInterval: 60000,
				},
			],
		};

		serviceHealthMonitor = new ServiceHealthMonitor(config);
		serviceHealthMonitor.start();
	}

	return serviceHealthMonitor;
}

export default ServiceHealthMonitor;
