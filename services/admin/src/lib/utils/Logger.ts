import { createIsomorphicLogger } from '@shared/client';
import { v4 as uuidv4 } from 'uuid';

export enum LogLevelEnum
{
	DEBUG = 0,
	INFO = 1,
	WARN = 2,
	ERROR = 3,
	CRITICAL = 4,
}

type LogContextType =
{
    correlationId?: string;
    userId?: string;
    sessionId?: string;
    requestId?: string;
} & Record<string, unknown>;

export interface LogEntryType
{
	id: string;
	timestamp: Date;
	level: LogLevelEnum;
	levelName: string;
	service: string;
	message: string;
	data?: unknown;
	correlationId?: string;
	userId?: string;
	sessionId?: string;
	requestId?: string;
	stackTrace?: string;
	context?: LogContextType;
}

export interface LoggerConfigType
{
	level: LogLevelEnum;
	format: 'json' | 'text';
	includeStackTrace: boolean;
	maxLogSize: number;
	rotationSize: number;
	retentionDays: number;
}

/**
 * Enhanced Logger utility for structured logging
 */
export class Logger
{
	private serviceName: string;

	private config: LoggerConfigType;

	private logs: LogEntryType[] = [];

	private isomorphicLogger = createIsomorphicLogger(this.serviceName);

	constructor(
		serviceName: string,
		config: Partial<LoggerConfigType> = {},
	)
	{
		this.serviceName = serviceName;
		this.config = {
			level: LogLevelEnum.INFO,
			format: 'json',
			includeStackTrace: true,
			maxLogSize: 10000,
			rotationSize: 1000000, // 1MB
			retentionDays: 30,
			...config,
		};

		// Use isomorphic logger
		this.isomorphicLogger = createIsomorphicLogger(serviceName);
	}

	/**
   * Log debug message
   */
	public debug(message: string, data?: unknown, context?: LogContextType): void
	{
		this.log(LogLevelEnum.DEBUG, message, data, context);
	}

	/**
   * Log info message
   */
	public info(message: string, data?: unknown, context?: LogContextType): void
	{
		this.log(LogLevelEnum.INFO, message, data, context);
	}

	/**
   * Log warning message
   */
	public warn(message: string, data?: unknown, context?: LogContextType): void
	{
		this.log(LogLevelEnum.WARN, message, data, context);
	}

	/**
   * Log error message
   */
	public error(message: string, data?: unknown, context?: LogContextType): void
	{
		this.log(LogLevelEnum.ERROR, message, data, context);
	}

	/**
   * Log critical message
   */
	public critical(message: string, data?: unknown, context?: LogContextType): void
	{
		this.log(LogLevelEnum.CRITICAL, message, data, context);
	}

	/**
   * Core logging method
   */
	private log(
		level: LogLevelEnum,
		message: string,
		data?: unknown,
		context?: LogContextType,
	): void
	{
		// Skip if log level is below configured level
		if (level < this.config.level)
		{
			return;
		}

		const logEntry: LogEntryType = {
			id: uuidv4(),
			timestamp: new Date(),
			level,
			levelName: LogLevelEnum[level],
			service: this.serviceName,
			message,
			data,
			context,
			correlationId: context?.correlationId,
			userId: context?.userId,
			sessionId: context?.sessionId,
			requestId: context?.requestId,
		};

		// Add stack trace for errors if configured
		if (
			this.config.includeStackTrace &&
      (level === LogLevelEnum.ERROR || level === LogLevelEnum.CRITICAL)
		)
		{
			logEntry.stackTrace = new Error().stack;
		}

		// Store log entry
		this.logs.push(logEntry);

		// Rotate logs if needed
		if (this.logs.length > this.config.maxLogSize)
		{
			this.logs = this.logs.slice(-Math.floor(this.config.maxLogSize * 0.8));
		}

		// Output to console
		this.outputToConsole(logEntry);
	}

	/**
   * Output log entry to console using Pino
   */
	private outputToConsole(logEntry: LogEntryType): void
	{
		const logData = {
			data: logEntry.data,
			context: logEntry.context,
			correlationId: logEntry.correlationId,
			userId: logEntry.userId,
			sessionId: logEntry.sessionId,
			requestId: logEntry.requestId,
			stackTrace: logEntry.stackTrace,
		};

		// Map our custom levels to isomorphic logger levels
		switch (logEntry.level)
		{
			case LogLevelEnum.DEBUG:
				this.isomorphicLogger.debug(logEntry.message, logData);
				break;
			case LogLevelEnum.INFO:
				this.isomorphicLogger.info(logEntry.message, logData);
				break;
			case LogLevelEnum.WARN:
				this.isomorphicLogger.warn(logEntry.message, logData);
				break;
			case LogLevelEnum.ERROR:
				this.isomorphicLogger.error(logEntry.message, logData);
				break;
			case LogLevelEnum.CRITICAL:
				this.isomorphicLogger.fatal(logEntry.message, logData);
				break;
		}
	}

	/**
   * Get recent log entries
   */
	public getLogs(limit?: number, level?: LogLevelEnum): LogEntryType[]
	{
		let filteredLogs = [...this.logs];

		if (level !== undefined)
		{
			filteredLogs = filteredLogs.filter(log => log.level >= level);
		}

		// Sort by timestamp descending (most recent first)
		filteredLogs.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

		return limit ? filteredLogs.slice(0, limit) : filteredLogs;
	}

	/**
   * Search logs by message or data content
   */
	public searchLogs(
		query: string,
		options?: {
			level?: LogLevelEnum;
			startDate?: Date;
			endDate?: Date;
			limit?: number;
		},
	): LogEntryType[]
	{
		let filteredLogs = [...this.logs];

		// Filter by level
		if (options?.level !== undefined)
		{
			filteredLogs = filteredLogs.filter(log => log.level >= options.level!);
		}

		// Filter by date range
		if (options?.startDate)
		{
			filteredLogs = filteredLogs.filter(log => log.timestamp >= options.startDate!);
		}
		if (options?.endDate)
		{
			filteredLogs = filteredLogs.filter(log => log.timestamp <= options.endDate!);
		}

		// Search in message and data
		const lowerQuery = query.toLowerCase();
		filteredLogs = filteredLogs.filter((log) =>
		{
			const messageMatch = log.message.toLowerCase().includes(lowerQuery);
			const dataMatch = log.data &&
        JSON.stringify(log.data).toLowerCase().includes(lowerQuery);
			return messageMatch || dataMatch;
		});

		// Sort by timestamp descending
		filteredLogs.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

		return options?.limit ? filteredLogs.slice(0, options.limit) : filteredLogs;
	}

	/**
   * Get log statistics
   */
	public getLogStats(): {
		total: number;
		byLevel: Record<string, number>;
		byService: Record<string, number>;
		recentErrors: number;
		oldestLog?: Date;
		newestLog?: Date;
	}
	{
		const stats = {
			total: this.logs.length,
			byLevel: {} as Record<string, number>,
			byService: {} as Record<string, number>,
			recentErrors: 0,
			oldestLog: undefined as Date | undefined,
			newestLog: undefined as Date | undefined,
		};

		if (this.logs.length === 0)
		{
			return stats;
		}

		// Calculate statistics
		const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);

		for (const log of this.logs)
		{
			// Count by level
			const levelName = LogLevelEnum[log.level];
			stats.byLevel[levelName] = (stats.byLevel[levelName] || 0) + 1;

			// Count by service
			stats.byService[log.service] = (stats.byService[log.service] || 0) + 1;

			// Count recent errors
			if (
				(log.level === LogLevelEnum.ERROR || log.level === LogLevelEnum.CRITICAL) &&
        log.timestamp >= oneHourAgo
			)
			{
				stats.recentErrors++;
			}

			// Track oldest and newest
			if (!stats.oldestLog || log.timestamp < stats.oldestLog)
			{
				stats.oldestLog = log.timestamp;
			}
			if (!stats.newestLog || log.timestamp > stats.newestLog)
			{
				stats.newestLog = log.timestamp;
			}
		}

		return stats;
	}

	/**
   * Clear all logs
   */
	public clearLogs(): void
	{
		this.logs = [];
	}

	/**
   * Update logger configuration
   */
	public updateConfig(config: Partial<LoggerConfigType>): void
	{
		this.config = {
			...this.config,
			...config,
		};
	}

	/**
   * Create child logger with additional context
   */
	public child(
		childName: string,
		context?: LogContextType,
	): Logger
	{
		const childLogger = new Logger(`${this.serviceName}:${childName}`, this.config);

		// Override log method to include parent context
		const originalLog = childLogger.log.bind(childLogger);
		childLogger.log = (level: LogLevelEnum, message: string, data?: unknown, logContext?: LogContextType) =>
		{
			const mergedContext = {
				...context,
				...logContext,
			};
			return originalLog(level, message, data, mergedContext);
		};

		return childLogger;
	}

	/**
   * Security logging methods
   */
	public security = {
		logFailedLogin: (ip: string, username: string, reason: string) =>
		{
			this.isomorphicLogger.warn('Failed login attempt', {
				event: 'failed_login',
				ip,
				username,
				reason,
			});
		},

		logSuccessfulLogin: (ip: string, userId: string, username: string) =>
		{
			this.isomorphicLogger.info('Successful login', {
				event: 'successful_login',
				ip,
				userId,
				username,
			});
		},

		logLogout: (ip: string, userId: string) =>
		{
			this.isomorphicLogger.info('User logout', {
				event: 'logout',
				ip,
				userId,
			});
		},

		logSuspiciousActivity: (ip: string, userId: string, activity: string, details: unknown) =>
		{
			this.isomorphicLogger.warn('Suspicious activity detected', {
				event: 'suspicious_activity',
				ip,
				userId,
				activity,
				details,
			});
		},

		logRateLimitExceeded: (ip: string, endpoint: string, limit: number) =>
		{
			this.isomorphicLogger.warn('Rate limit exceeded', {
				event: 'rate_limit_exceeded',
				ip,
				endpoint,
				limit,
			});
		},
	};

	/**
   * Audit logging methods
   */
	public audit = {
		logDataAccess: (userId: string, resource: string, action: string, details: unknown) =>
		{
			this.isomorphicLogger.info('Data access', {
				event: 'data_access',
				userId,
				resource,
				action,
				details,
			});
		},

		logConfigurationChange: (userId: string, setting: string, oldValue: unknown, newValue: unknown) =>
		{
			this.isomorphicLogger.info('Configuration change', {
				event: 'configuration_change',
				userId,
				setting,
				oldValue,
				newValue,
			});
		},

		logUserAction: (userId: string, action: string, resource: string, details: unknown) =>
		{
			this.isomorphicLogger.info('User action', {
				event: 'user_action',
				userId,
				action,
				resource,
				details,
			});
		},

		logSystemEvent: (event: string, details: unknown) =>
		{
			this.isomorphicLogger.info('System event', {
				event: 'system_event',
				systemEvent: event,
				details,
			});
		},
	};

	/**
   * Export logs in various formats
   */
	public exportLogs(
		format: 'json' | 'csv' | 'text',
		options?: {
			level?: LogLevelEnum;
			startDate?: Date;
			endDate?: Date;
			limit?: number;
		},
	): string
	{
		let logs = this.getLogs(options?.limit, options?.level);

		// Filter by date range
		if (options?.startDate)
		{
			logs = logs.filter(log => log.timestamp >= options.startDate!);
		}
		if (options?.endDate)
		{
			logs = logs.filter(log => log.timestamp <= options.endDate!);
		}

		switch (format)
		{
			case 'json':
				return JSON.stringify(logs, null, 2);

			case 'csv':
				const headers = 'Timestamp,Level,Service,Message,Data,CorrelationId,UserId';
				const rows = logs.map(log => [
					log.timestamp.toISOString(),
					LogLevelEnum[log.level],
					log.service,
          `"${log.message.replace(/"/g, '""')}"`,
          log.data ? `"${JSON.stringify(log.data).replace(/"/g, '""')}"` : '',
          log.correlationId || '',
          log.userId || '',
				].join(','));
				return [headers, ...rows].join('\n');

			case 'text':
				return logs.map((log) =>
				{
					const timestamp = log.timestamp.toISOString();
					const level = LogLevelEnum[log.level];
					const data = log.data ? ` | Data: ${JSON.stringify(log.data)}` : '';
					return `[${timestamp}] ${level} ${log.service}: ${log.message}${data}`;
				}).join('\n');

			default:
				throw new Error(`Unsupported export format: ${format}`);
		}
	}
}

export default Logger;
