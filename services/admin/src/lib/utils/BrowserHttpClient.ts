/**
 * Browser-compatible HTTP client for admin service
 * Uses fetch API instead of node-libcurl to avoid native dependencies in Next.js environment
 */

interface HttpResponse<T = unknown> {
	data: T;
	status: number;
	statusText: string;
	headers: Record<string, string>;
}

interface HttpClientOptions {
	timeout?: number;
	headers?: Record<string, string>;
}

export class BrowserHttpClient {
	private timeout: number;
	private defaultHeaders: Record<string, string>;

	constructor(options: HttpClientOptions = {}) {
		this.timeout = options.timeout || 10000;
		this.defaultHeaders = options.headers || {};
	}

	async get<T = unknown>(url: string, options: HttpClientOptions = {}): Promise<HttpResponse<T>> {
		return this.request<T>('GET', url, undefined, options);
	}

	async post<T = unknown>(url: string, data?: unknown, options: HttpClientOptions = {}): Promise<HttpResponse<T>> {
		return this.request<T>('POST', url, data, options);
	}

	async put<T = unknown>(url: string, data?: unknown, options: HttpClientOptions = {}): Promise<HttpResponse<T>> {
		return this.request<T>('PUT', url, data, options);
	}

	async delete<T = unknown>(url: string, options: HttpClientOptions = {}): Promise<HttpResponse<T>> {
		return this.request<T>('DELETE', url, undefined, options);
	}

	private async request<T = unknown>(
		method: string,
		url: string,
		data?: unknown,
		options: HttpClientOptions = {}
	): Promise<HttpResponse<T>> {
		const controller = new AbortController();
		const timeoutId = setTimeout(() => controller.abort(), options.timeout || this.timeout);

		try {
			const headers = {
				...this.defaultHeaders,
				...options.headers,
			};

			if (data && !headers['Content-Type']) {
				headers['Content-Type'] = 'application/json';
			}

			const response = await fetch(url, {
				method,
				headers,
				body: data ? JSON.stringify(data) : undefined,
				signal: controller.signal,
			});

			clearTimeout(timeoutId);

			const responseHeaders: Record<string, string> = {};
			response.headers.forEach((value, key) => {
				responseHeaders[key] = value;
			});

			let responseData: T;
			const contentType = response.headers.get('content-type');
			if (contentType && contentType.includes('application/json')) {
				responseData = await response.json();
			} else {
				responseData = (await response.text()) as T;
			}

			return {
				data: responseData,
				status: response.status,
				statusText: response.statusText,
				headers: responseHeaders,
			};
		} catch (error) {
			clearTimeout(timeoutId);
			if (error instanceof Error && error.name === 'AbortError') {
				throw new Error(`Request timeout after ${options.timeout || this.timeout}ms`);
			}
			throw error;
		}
	}
}

export default BrowserHttpClient;
