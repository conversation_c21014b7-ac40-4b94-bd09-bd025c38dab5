# Comprehensive Error Handling and Logging System

This directory contains a complete error handling and logging system for the admin panel, providing centralized error management, analytics, notifications, debugging tools, and recovery mechanisms.

## Overview

The error handling system consists of several interconnected services:

- **ErrorHandlingService**: Central error processing and management
- **ErrorAnalyticsService**: Error frequency tracking and trend analysis
- **ErrorNotificationService**: Automated error notifications and escalations
- **ErrorDebugService**: Debugging tools and error documentation
- **ErrorRecoveryService**: Automated error recovery mechanisms
- **AdminPanelError**: Custom error class with structured information

## Features

### ✅ Centralized Error Handling

- Structured error codes and categorization
- User-friendly error messages
- Comprehensive error context capture
- Automatic error correlation and tracking

### ✅ Client-Side Error Boundaries

- React error boundaries with recovery mechanisms
- Automatic error reporting to server
- User feedback and retry functionality
- Graceful degradation handling

### ✅ Server-Side Error Logging

- Structured logging with correlation IDs
- Context preservation across requests
- Automatic stack trace capture
- Performance impact tracking

### ✅ Intelligent Error Reporting

- Automatic error categorization
- Impact assessment and severity classification
- Resolution suggestions and documentation
- Escalation procedures

### ✅ Error Recovery Mechanisms

- Retry logic with exponential backoff
- Fallback options and graceful degradation
- Circuit breaker patterns
- Automatic service recovery

### ✅ Comprehensive Audit Logging

- All administrative actions logged
- Security events tracking
- System changes with correlation IDs
- User activity monitoring

### ✅ Error Analytics

- Frequency tracking and trend analysis
- Root cause analysis assistance
- Prevention recommendations
- Performance impact metrics

### ✅ Error Notification System

- Critical error alerts
- Escalation procedures
- Multi-channel notifications (email, webhook, SMS)
- Stakeholder communication

### ✅ Error Debugging Tools

- Stack trace analysis
- Context reconstruction
- Reproduction assistance
- Environment information capture

### ✅ Error Rate Monitoring

- Threshold-based alerting
- SLA tracking and violation detection
- Performance impact analysis
- Real-time metrics dashboard

### ✅ Error Documentation

- Common issues database
- Troubleshooting guides
- Resolution procedures
- Prevention measures

## Quick Start

### Basic Usage

```typescript
import { ErrorHandlingService, ErrorCodes } from "@/lib/errors";

const errorService = ErrorHandlingService.getInstance();

// Create and handle an error
try {
  // Some operation that might fail
  throw new Error("Database connection failed");
} catch (error) {
  const adminError = errorService.createError(
    ErrorCodes.DATABASE_CONNECTION_FAILED,
    error.message,
    "Unable to connect to the database. Please try again later.",
    {
      userId: "user123",
      url: "/api/domains",
      method: "GET",
    }
  );

  await errorService.handleError(adminError);
}
```

### Using Error Boundaries

```tsx
import { ErrorBoundary } from "@/components/UI/ErrorBoundary";

function MyComponent() {
  return (
    <ErrorBoundary
      name="DomainManagement"
      showNotifications={true}
      maxRetries={3}
    >
      <DomainManagementInterface />
    </ErrorBoundary>
  );
}
```

### API Route Error Handling

```typescript
import { wrapApiHandler, ErrorCodes } from "@/lib/errors/middleware";

export const GET = wrapApiHandler(async (request: NextRequest) => {
  // Your API logic here
  const data = await fetchDomains();

  return NextResponse.json({ success: true, data });
});
```

## Architecture

### Error Flow

1. **Error Occurs**: Application error or exception
2. **Error Creation**: Convert to structured AdminPanelError
3. **Error Handling**: Process through ErrorHandlingService
4. **Analytics Recording**: Track in ErrorAnalyticsService
5. **Notification**: Send alerts via ErrorNotificationService
6. **Recovery Attempt**: Try recovery via ErrorRecoveryService
7. **Debug Info**: Capture context via ErrorDebugService

### Service Dependencies

```
ErrorHandlingService (Central Hub)
├── ErrorAnalyticsService (Analytics & Trends)
├── ErrorNotificationService (Alerts & Escalation)
├── ErrorDebugService (Debugging & Documentation)
├── ErrorRecoveryService (Recovery Mechanisms)
└── Logger (Structured Logging)
```

## Configuration

### Error Reporting Configuration

```typescript
import { ErrorNotificationService } from "@/lib/errors";

const notificationService = ErrorNotificationService.getInstance();

notificationService.updateConfig({
  automatic: true,
  includeStackTrace: true,
  includeUserData: false,
  includeSystemInfo: true,
  notificationChannels: ["email", "webhook"],
  escalationRules: [
    {
      severity: ErrorSeverity.CRITICAL,
      escalateAfter: 5, // minutes
      recipients: ["<EMAIL>"],
    },
    {
      severity: ErrorSeverity.HIGH,
      escalateAfter: 15,
      recipients: ["<EMAIL>"],
    },
  ],
});
```

### Recovery Options

```typescript
import { ErrorRecoveryService } from "@/lib/errors";

const recoveryService = ErrorRecoveryService.getInstance();

const recoveryOptions = {
  type: "retry" as const,
  enabled: true,
  config: {
    maxAttempts: 3,
    backoffStrategy: "exponential" as const,
    baseDelay: 1000,
    maxDelay: 30000,
  },
};

await recoveryService.attemptRecovery(error, recoveryOptions);
```

## Error Codes

The system uses structured error codes with prefixes for easy categorization:

- `AUTH_xxx`: Authentication and authorization errors
- `DB_xxx`: Database-related errors
- `SVC_xxx`: Service availability and communication errors
- `REQ_xxx`: Request validation and format errors
- `RATE_xxx`: Rate limiting and quota errors
- `CFG_xxx`: Configuration errors
- `VAL_xxx`: Data validation errors
- `DOM_xxx`: Domain management errors
- `CRAWL_xxx`: Crawl job errors
- `FILE_xxx`: File operation errors
- `NET_xxx`: Network communication errors
- `SEC_xxx`: Security-related errors
- `INT_xxx`: Internal system errors

## API Endpoints

### Error Analytics

- `GET /api/errors/analytics` - Get error analytics with filters
- `DELETE /api/errors/analytics` - Clear analytics data

### Error Trends

- `GET /api/errors/trends` - Get error trends over time

### Error Patterns

- `GET /api/errors/patterns` - Get error patterns and correlations

### Error Metrics

- `GET /api/errors/metrics` - Get current error rate metrics

### Error Documentation

- `GET /api/errors/documentation` - Get error documentation

### Debug Information

- `GET /api/errors/debug/{errorId}` - Get debug info for specific error

## Monitoring Dashboard

The system includes a comprehensive monitoring dashboard at `/errors` that provides:

- Real-time error metrics and statistics
- Error analytics with filtering and search
- Trend visualization and pattern analysis
- Debug information viewer
- Export functionality for analytics data

## Best Practices

### Error Creation

- Always use appropriate error codes
- Provide user-friendly messages
- Include relevant context information
- Add suggestions for resolution when possible

### Error Handling

- Handle errors as close to the source as possible
- Use error boundaries for React components
- Implement proper fallback mechanisms
- Log errors with sufficient context

### Recovery Strategies

- Implement retry logic for transient errors
- Use circuit breakers for external services
- Provide fallback data when possible
- Implement graceful degradation

### Monitoring

- Set up alerts for critical errors
- Monitor error rates and trends
- Review error patterns regularly
- Update documentation based on common issues

## Testing

The error handling system includes comprehensive test coverage:

```bash
# Run error handling tests
npm test -- --testPathPattern=errors

# Run specific service tests
npm test -- ErrorHandlingService.test.ts
npm test -- ErrorAnalyticsService.test.ts
```

## Performance Considerations

- Error analytics data is stored in memory with configurable limits
- Automatic cleanup of old error data
- Efficient error pattern analysis algorithms
- Minimal performance impact on normal operations

## Security

- Error messages sanitized to prevent information leakage
- User data inclusion configurable
- Audit logging for all error-related activities
- Secure error reporting channels

## Troubleshooting

### Common Issues

1. **High Memory Usage**: Adjust analytics retention settings
2. **Missing Notifications**: Check notification service configuration
3. **Recovery Failures**: Review recovery strategy configuration
4. **Performance Impact**: Optimize error handling frequency

### Debug Mode

Enable debug mode for detailed error information:

```typescript
process.env.NODE_ENV = "development";
```

## Contributing

When adding new error types:

1. Add error code to `ErrorCodes.ts`
2. Update error documentation in `ErrorDebugService.ts`
3. Add appropriate recovery strategies
4. Update tests and documentation

## License

This error handling system is part of the admin panel and follows the same license terms.
