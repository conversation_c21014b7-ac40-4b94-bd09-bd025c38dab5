import { NextRequest, NextResponse } from 'next/server';
import { createIsomorphicLogger } from '@shared/client';

import ErrorAnalyticsService from './ErrorAnalyticsService';
import ErrorCodesEnum from './ErrorCodes';
import ErrorHandlingService from './ErrorHandlingService';
import ErrorNotificationService from './ErrorNotificationService';
import db from '@/lib/database/client';

import type { ErrorContextType } from './types';

const logger = createIsomorphicLogger('errors-middleware');
const auditLogger = createIsomorphicLogger('admin-audit');

/**
 * Middleware for integrating error handling with Next.js requests
 */
export class ErrorMiddleware
{
	private static instance: ErrorMiddleware;

	private errorHandlingService: ErrorHandlingService;

	private analyticsService: ErrorAnalyticsService;

	private notificationService: ErrorNotificationService;

	private constructor()
	{
		this.errorHandlingService = ErrorHandlingService.getInstance();
		this.analyticsService = ErrorAnalyticsService.getInstance();
		this.notificationService = ErrorNotificationService.getInstance();
	}

	public static getInstance(): ErrorMiddleware
	{
		if (!ErrorMiddleware.instance)
		{
			ErrorMiddleware.instance = new ErrorMiddleware();
		}
		return ErrorMiddleware.instance;
	}

	/**
   * Wrap API route handler with error handling
   */
	public wrapApiHandler<T extends any[]>(
		handler: (...args: T) => Promise<NextResponse> | NextResponse,
	)
	{
		return async (...args: T): Promise<NextResponse> =>
		{
			try
			{
				const result = await handler(...args);
				return result;
			}
			catch (error)
			{
				return await this.handleApiError(error, args[0] as NextRequest);
			}
		};
	}

	/**
   * Handle API errors
   */
	private async handleApiError(
		error: unknown,
		request: NextRequest,
	): Promise<NextResponse>
	{
		// Extract context from request
		const context: Partial<ErrorContextType> = {
			url: request.url,
			method: request.method,
			userAgent: request.headers.get('user-agent') || undefined,
			ipAddress: this.getClientIP(request),
			timestamp: new Date(),
			requestId: request.headers.get('x-request-id') || undefined,
		};

		// Determine error code based on error type
		let errorCode = ErrorCodesEnum.INTERNAL_SERVER_ERROR;
		let statusCode = 500;

		if (error instanceof Error)
		{
			// Map common error types to specific error codes
			if (error.message.includes('database') || error.message.includes('connection'))
			{
				errorCode = ErrorCodesEnum.DATABASE_CONNECTION_FAILED;
				statusCode = 503;
			}
			else if (error.message.includes('timeout'))
			{
				errorCode = ErrorCodesEnum.SERVICE_TIMEOUT;
				statusCode = 504;
			}
			else if (error.message.includes('validation'))
			{
				errorCode = ErrorCodesEnum.VALIDATION_ERROR;
				statusCode = 400;
			}
			else if (error.message.includes('unauthorized') || error.message.includes('authentication'))
			{
				errorCode = ErrorCodesEnum.AUTHENTICATION_FAILED;
				statusCode = 401;
			}
			else if (error.message.includes('forbidden') || error.message.includes('permission'))
			{
				errorCode = ErrorCodesEnum.INSUFFICIENT_PERMISSIONS;
				statusCode = 403;
			}
		}

		// Create structured error
		const adminError = this.errorHandlingService.createError(
			errorCode,
			error instanceof Error ? error.message : 'Unknown error occurred',
			undefined,
			context,
			{
				cause: error instanceof Error ? error : undefined,
			},
		);

		// Handle the error through the centralized system
		const structuredError = await this.errorHandlingService.handleError(adminError, context);

		// Record analytics
		this.analyticsService.recordError(structuredError);

		// Send notifications if needed
		await this.notificationService.sendErrorNotification(structuredError);

		// Return error response
		return NextResponse.json(
			{
				success: false,
				error: structuredError.userMessage,
				code: structuredError.code,
				correlationId: structuredError.context.correlationId,
				timestamp: structuredError.context.timestamp,
				...(process.env.NODE_ENV === 'development' && {
					details: structuredError.message,
					suggestions: structuredError.suggestions,
				}),
			},
			{ status: statusCode },
		);
	}

	/**
   * Create error response for client-side errors
   */
	public createErrorResponse(
		errorCode: ErrorCodesEnum,
		message: string,
		userMessage?: string,
		statusCode: number = 500,
		context?: Partial<ErrorContextType>,
	): NextResponse
	{
		const adminError = this.errorHandlingService.createError(
			errorCode,
			message,
			userMessage,
			context,
		);

		return NextResponse.json(
			{
				success: false,
				error: adminError.userMessage,
				code: adminError.code,
				correlationId: adminError.context.correlationId,
				timestamp: adminError.context.timestamp,
				...(process.env.NODE_ENV === 'development' && {
					details: adminError.message,
					suggestions: adminError.suggestions,
				}),
			},
			{ status: statusCode },
		);
	}

	/**
   * Validate request and return error response if invalid
   */
	public validateRequest(
		request: NextRequest,
		validations: {
			method?: string[];
			contentType?: string[];
			requiredHeaders?: string[];
			requiredParams?: string[];
		},
	): NextResponse | null
	{
		const context: Partial<ErrorContextType> = {
			url: request.url,
			method: request.method,
			userAgent: request.headers.get('user-agent') || undefined,
			ipAddress: this.getClientIP(request),
			timestamp: new Date(),
		};

		// Validate HTTP method
		if (validations.method && !validations.method.includes(request.method))
		{
			return this.createErrorResponse(
				ErrorCodesEnum.INVALID_REQUEST,
        `Method ${request.method} not allowed`,
        `This endpoint only accepts ${validations.method.join(', ')} requests`,
        405,
        context,
			);
		}

		// Validate content type
		if (validations.contentType)
		{
			const contentType = request.headers.get('content-type');
			const isValidContentType = validations.contentType.some(type => contentType?.includes(type));

			if (!isValidContentType)
			{
				return this.createErrorResponse(
					ErrorCodesEnum.UNSUPPORTED_MEDIA_TYPE,
          `Unsupported content type: ${contentType}`,
          `This endpoint requires one of: ${validations.contentType.join(', ')}`,
          415,
          context,
				);
			}
		}

		// Validate required headers
		if (validations.requiredHeaders)
		{
			for (const header of validations.requiredHeaders)
			{
				if (!request.headers.get(header))
				{
					return this.createErrorResponse(
						ErrorCodesEnum.MISSING_REQUIRED_FIELD,
            `Missing required header: ${header}`,
            `The ${header} header is required for this request`,
            400,
            context,
					);
				}
			}
		}

		// Validate required URL parameters
		if (validations.requiredParams)
		{
			const { searchParams } = new URL(request.url);

			for (const param of validations.requiredParams)
			{
				if (!searchParams.get(param))
				{
					return this.createErrorResponse(
						ErrorCodesEnum.MISSING_REQUIRED_FIELD,
            `Missing required parameter: ${param}`,
            `The ${param} parameter is required for this request`,
            400,
            context,
					);
				}
			}
		}

		return null; // No validation errors
	}

	/**
   * Handle rate limiting
   */
	public checkRateLimit(
		request: NextRequest,
		limit: number,
		windowMs: number,
		identifier?: string,
	): NextResponse | null
	{
		// This is a simplified rate limiting implementation
		// In production, you'd use Redis or a proper rate limiting service

		const key = identifier || this.getClientIP(request) || 'anonymous';
		const now = Date.now();

		// For demo purposes, we'll just check a simple in-memory store
		// In production, implement proper rate limiting with Redis

		return null; // No rate limit exceeded
	}

	/**
   * Extract client IP address from request
   */
	private getClientIP(request: NextRequest): string | undefined
	{
		// Check various headers for client IP
		const forwarded = request.headers.get('x-forwarded-for');
		if (forwarded)
		{
			return forwarded.split(',')[0].trim();
		}

		const realIP = request.headers.get('x-real-ip');
		if (realIP)
		{
			return realIP;
		}

		const cfConnectingIP = request.headers.get('cf-connecting-ip');
		if (cfConnectingIP)
		{
			return cfConnectingIP;
		}

		return undefined;
	}

	/**
   * Resolve username from userId using database lookup
   */
	private async resolveUsername(userId?: string): Promise<string | undefined>
	{
		if (!userId)
		{
			return undefined;
		}

		try
		{
			const result = await db.mariadb.query<{ username: string }>(
				'SELECT username FROM admin_users WHERE id = ? AND is_active = TRUE LIMIT 1',
				[userId]
			);

			return result[0]?.username;
		}
		catch (error)
		{
			logger.warn('Failed to resolve username from userId', { userId, error });
			return userId; // Fallback to userId if lookup fails
		}
	}

	/**
   * Log request for audit purposes
   */
	public async logRequest(
		request: NextRequest,
		response: NextResponse,
		duration: number,
		userId?: string,
	): Promise<void>
	{
		const username = await this.resolveUsername(userId);

		const auditEntry = {
			id: crypto.randomUUID(),
			correlationId: request.headers.get('x-correlation-id') || crypto.randomUUID(),
			timestamp: new Date(),
			userId,
			username: username || userId || 'anonymous',
			action: 'API_REQUEST',
			resource: new URL(request.url).pathname,
			method: request.method,
			url: request.url,
			ipAddress: this.getClientIP(request) || 'unknown',
			userAgent: request.headers.get('user-agent') || 'unknown',
			success: response.status < 400,
			duration,
			statusCode: response.status,
			metadata: {
				contentType: request.headers.get('content-type'),
				contentLength: request.headers.get('content-length'),
				referer: request.headers.get('referer'),
			},
		};

		// Log audit entry to proper audit trail
		auditLogger.info('Security audit log', auditEntry);
	}
}

// Export singleton instance
export const errorMiddleware = ErrorMiddleware.getInstance();

// Export utility functions
export const wrapApiHandler = errorMiddleware.wrapApiHandler.bind(errorMiddleware);
export const createErrorResponse = errorMiddleware.createErrorResponse.bind(errorMiddleware);
export const validateRequest = errorMiddleware.validateRequest.bind(errorMiddleware);
export const checkRateLimit = errorMiddleware.checkRateLimit.bind(errorMiddleware);

export default ErrorMiddleware;
