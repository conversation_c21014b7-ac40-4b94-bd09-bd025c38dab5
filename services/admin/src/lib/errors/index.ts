// Local imports for building default export and using in helper functions
import AdminPanelError from './AdminPanelError';
import ErrorHandlingService from './ErrorHandlingService';
import ErrorAnalyticsService from './ErrorAnalyticsService';
import ErrorNotificationService from './ErrorNotificationService';
import ErrorDebugService from './ErrorDebugService';
import ErrorRecoveryService from './ErrorRecoveryService';
import ErrorCodesEnum, { ErrorSeverityEnum, ErrorCategoryEnum } from './ErrorCodes';
import type { ErrorContextType } from './types';

// Utility functions
const createError = (
  code: ErrorCodesEnum,
  message: string,
  userMessage?: string,
  context?: Partial<ErrorContextType>,
) =>
{
  const errorHandlingService = ErrorHandlingService.getInstance();
  return errorHandlingService.createError(code, message, userMessage, context);
};

const handleError = async (
  error: AdminPanelError | Error,
  context?: Partial<ErrorContextType>,
) =>
{
  const errorHandlingService = ErrorHandlingService.getInstance();
  return await errorHandlingService.handleError(error, context);
};

const getErrorAnalytics = () =>
{
  const analyticsService = ErrorAnalyticsService.getInstance();
  return analyticsService.getErrorAnalytics();
};

const getErrorDocumentation = (errorCode: ErrorCodesEnum) =>
{
  const debugService = ErrorDebugService.getInstance();
  return debugService.getErrorDocumentation(errorCode);
};

// Types
export type {
	AdminPanelErrorType,
	ErrorContextType,
	ErrorRecoveryOptionsType,
	ErrorReportingConfigType,
	ErrorAnalyticsType,
	ErrorNotificationType,
	ErrorDebugInfoType,
	AuditLogEntryType,
	ErrorRateMetricsType,
	ErrorDocumentationType,
} from './types';

// Error handling system exports (declaration-then-export pattern)
export {
	AdminPanelError,
	ErrorHandlingService,
	ErrorAnalyticsService,
	ErrorNotificationService,
	ErrorDebugService,
	ErrorRecoveryService,
	// Error codes and enums
	ErrorCodesEnum,
	ErrorSeverityEnum,
	ErrorCategoryEnum,
	// Functions
	createError,
	handleError,
	getErrorAnalytics,
	getErrorDocumentation,
};

// Default export for convenience
export default {
  AdminPanelError,
  ErrorHandlingService,
  ErrorAnalyticsService,
  ErrorNotificationService,
  ErrorDebugService,
  ErrorRecoveryService,
  ErrorCodesEnum,
  ErrorSeverityEnum,
  ErrorCategoryEnum,
  createError,
  handleError,
  getErrorAnalytics,
  getErrorDocumentation,
};
