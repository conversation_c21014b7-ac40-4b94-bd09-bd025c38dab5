import { v4 as uuidv4 } from 'uuid';

import { Logger } from '../utils/Logger';
import db from '@/lib/database/client';
import { config } from '@/lib/config';

import AdminPanelError from './AdminPanelError';
import ErrorCodesEnum, { ErrorSeverityEnum, ErrorCategoryEnum } from './ErrorCodes';

import type {
	AdminPanelErrorType,
	ErrorContextType,
	ErrorRecoveryOptionsType,
	ErrorReportingConfigType,
	ErrorAnalyticsType,
	ErrorNotificationType,
	// ErrorDebugInfoType,
	AuditLogEntryType,
} from './types';

/**
 * Centralized error handling service for the admin panel
 */
class ErrorHandlingService
{
	private static instance: ErrorHandlingService;

	private logger: Logger;

	private errorAnalytics: Map<string, ErrorAnalyticsType> = new Map();

	private auditLogs: AuditLogEntryType[] = [];

	private errorReportingConfig: ErrorReportingConfigType;

	private constructor()
	{
		this.logger = new Logger('ErrorHandlingService');
		this.errorReportingConfig = {
			automatic: true,
			includeStackTrace: true,
			includeUserData: false,
			includeSystemInfo: true,
			notificationChannels: ['email'],
			escalationRules: [
				{
					severity: ErrorSeverityEnum.CRITICAL,
					escalateAfter: 5,
					recipients: [config.notifications.email.adminEmail],
				},
				{
					severity: ErrorSeverityEnum.HIGH,
					escalateAfter: 15,
					recipients: [config.notifications.email.supportEmail],
				},
			],
		};
	}

	public static getInstance(): ErrorHandlingService
	{
		if (!ErrorHandlingService.instance)
		{
			ErrorHandlingService.instance = new ErrorHandlingService();
		}
		return ErrorHandlingService.instance;
	}

	/**
   * Handle an error with comprehensive processing
   */
	public async handleError(
		error: Error | AdminPanelError,
		context?: Partial<ErrorContextType>,
		recoveryOptions?: ErrorRecoveryOptionsType,
	): Promise<AdminPanelErrorType>
	{
		let adminError: AdminPanelError;

		// Convert regular Error to AdminPanelError if needed
		if (!(error instanceof AdminPanelError))
		{
			adminError = new AdminPanelError(
				ErrorCodesEnum.UNEXPECTED_ERROR,
				error.message,
				undefined,
				context,
				{ cause: error },
			);
		}
		else
		{
			adminError = error;
		}

		const structuredError = adminError.toStructured();

		// Log the error
		await this.logError(structuredError);

		// Update analytics
		await this.updateErrorAnalytics(structuredError);

		// Create audit log entry if context includes user information
		if (context?.userId)
		{
			await this.createAuditLogEntry(structuredError, context);
		}

		// Handle error reporting and notifications
		if (this.shouldReportError(structuredError))
		{
			await this.reportError(structuredError);
		}

		// Attempt recovery if options provided
		if (recoveryOptions && structuredError.recoverable)
		{
			await this.attemptRecovery(structuredError, recoveryOptions);
		}

		return structuredError;
	}

	/**
   * Create a new AdminPanelError with context
   */
	public createError(
		code: ErrorCodesEnum,
		message: string,
		userMessage?: string,
		context?: Partial<ErrorContextType>,
		options?: {
			severity?: ErrorSeverityEnum;
			category?: ErrorCategoryEnum;
			recoverable?: boolean;
			retryable?: boolean;
			suggestions?: string[];
			documentation?: string;
			cause?: Error;
		},
	): AdminPanelError
	{
		return new AdminPanelError(code, message, userMessage, context, options);
	}

	/**
   * Log error with structured format
   */
	private async logError(error: AdminPanelErrorType): Promise<void>
	{
		const logLevel = this.getLogLevel(error.severity);
		const logData =
		{
			errorId: error.context.correlationId,
			code: error.code,
			category: error.category,
			severity: error.severity,
			message: error.message,
			userMessage: error.userMessage,
			context: error.context,
			recoverable: error.recoverable,
			retryable: error.retryable,
			suggestions: error.suggestions,
			impactAssessment: error.impactAssessment,
		};

		switch (logLevel)
		{
			case 'error':
				this.logger.error('Admin Panel Error', logData);
				break;
			case 'warn':
				this.logger.warn('Admin Panel Warning', logData);
				break;
			case 'info':
				this.logger.info('Admin Panel Info', logData);
				break;
			default:
				this.logger.debug('Admin Panel Debug', logData);
		}
	}

	/**
   * Update error analytics
   */
	private async updateErrorAnalytics(error: AdminPanelErrorType): Promise<void>
	{
		const errorKey = `${error.code}_${error.category}`;
		const existing = this.errorAnalytics.get(errorKey);

		if (existing)
		{
			existing.frequency += 1;
			existing.lastOccurrence = new Date();
			if (error.context.userId)
			{
				existing.affectedUsers.push(error.context.userId);
			}
		}
		else
		{
			const analytics: ErrorAnalyticsType = {
				errorId: uuidv4(),
				code: error.code,
				category: error.category,
				severity: error.severity,
				timestamp: new Date(),
				frequency: 1,
				firstOccurrence: new Date(),
				lastOccurrence: new Date(),
				affectedUsers: error.context.userId ? [error.context.userId] : [],
				resolutionStatus: 'open',
			};
			this.errorAnalytics.set(errorKey, analytics);
		}
	}

	/**
	 * Resolve username from userId using database lookup
	 */
	private async resolveUsername(userId?: string): Promise<string | undefined>
	{
		if (!userId)
		{
			return undefined;
		}

		try
		{
			const result = await db.mariadb.query<{ username: string }>(
				'SELECT username FROM admin_users WHERE id = ? AND is_active = TRUE LIMIT 1',
				[userId]
			);

			return result[0]?.username;
		}
		catch (error)
		{
			this.logger.warn('Failed to resolve username from userId', { userId, error });
			return userId; // Fallback to userId if lookup fails
		}
	}

	/**
   * Create audit log entry
   */
	private async createAuditLogEntry(
		error: AdminPanelErrorType,
		context: Partial<ErrorContextType>,
	): Promise<void>
	{
		const username = await this.resolveUsername(context.userId);

		const auditEntry: AuditLogEntryType = {
			id: uuidv4(),
			correlationId: error.context.correlationId || uuidv4(),
			timestamp: new Date(),
			userId: context.userId,
			username: username || context.userId || 'anonymous',
			action: 'ERROR_OCCURRED',
			resource: 'SYSTEM',
			method: context.method || 'UNKNOWN',
			url: context.url || 'UNKNOWN',
			ipAddress: context.ipAddress || 'UNKNOWN',
			userAgent: context.userAgent || 'UNKNOWN',
			success: false,
			duration: 0,
			statusCode: this.getHttpStatusCode(error.code),
			errorCode: error.code,
			errorMessage: error.message,
			metadata: {
				severity: error.severity,
				category: error.category,
				recoverable: error.recoverable,
				retryable: error.retryable,
			},
		};

		this.auditLogs.push(auditEntry);

		// Keep only last 10000 audit logs in memory
		if (this.auditLogs.length > 10000)
		{
			this.auditLogs = this.auditLogs.slice(-10000);
		}
	}

	/**
   * Report error based on configuration
   */
	private async reportError(error: AdminPanelErrorType): Promise<void>
	{
		if (!this.errorReportingConfig.automatic)
		{
			return;
		}

		// Check if error should trigger escalation
		const escalationRule = this.errorReportingConfig.escalationRules.find(
			rule => rule.severity === error.severity,
		);

		if (escalationRule)
		{
			// Schedule escalation (in a real implementation, this would use a job queue)
			setTimeout(async () =>
			{
				await this.escalateError(error, escalationRule.recipients);
			}, escalationRule.escalateAfter * 60 * 1000);
		}

		// Send immediate notifications for critical errors
		if (error.severity === ErrorSeverityEnum.CRITICAL)
		{
			await this.sendErrorNotifications(error);
		}
	}

	/**
   * Send error notifications
   */
	private async sendErrorNotifications(error: AdminPanelErrorType): Promise<void>
	{
		for (const channel of this.errorReportingConfig.notificationChannels)
		{
			const notification: ErrorNotificationType = {
				id: uuidv4(),
				errorId: error.context.correlationId || uuidv4(),
				channel,
				recipient: config.notifications.email.adminEmail,
				status: 'pending',
				retryCount: 0,
			};

			try
			{
				await this.sendNotification(notification, error);
				notification.status = 'sent';
				notification.sentAt = new Date();
			}
			catch (notificationError)
			{
				notification.status = 'failed';
				notification.failureReason = notificationError instanceof Error
					? notificationError.message
					: 'Unknown error';

				this.logger.error('Failed to send error notification', {
					notificationId: notification.id,
					channel,
					error: notificationError,
				});
			}
		}
	}

	/**
   * Send individual notification
   */
	private async sendNotification(
		notification: ErrorNotificationType,
		error: AdminPanelErrorType,
	): Promise<void>
	{
		switch (notification.channel)
		{
			case 'email':
				await this.sendEmailNotification(notification, error);
				break;

			case 'webhook':
				await this.sendWebhookNotification(notification, error);
				break;


			case 'browser':
				// Browser notifications are handled client-side
				break;
		}
	}

	/**
	 * Send webhook notification
	 */
	private async sendWebhookNotification(
		notification: ErrorNotificationType,
		error: AdminPanelErrorType,
	): Promise<void>
	{
		if (!config.notifications.webhook?.url)
		{
			this.logger.warn('Webhook notification requested but no webhook URL configured');
			return;
		}

		const payload = {
			type: 'error_notification',
			timestamp: new Date().toISOString(),
			error: {
				id: error.context.correlationId,
				code: error.code,
				severity: error.severity,
				message: error.message,
				userMessage: error.userMessage,
				category: error.category,
				recoverable: error.recoverable,
				context: error.context,
				suggestions: error.suggestions,
			},
			notification: {
				id: notification.id,
				channel: notification.channel,
				recipient: notification.recipient,
			},
		};

		try
		{
			const response = await fetch(config.notifications.webhook.url, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					...(config.notifications.webhook.secret && {
						'X-Webhook-Secret': config.notifications.webhook.secret,
					}),
				},
				body: JSON.stringify(payload),
			});

			if (!response.ok)
			{
				throw new Error(`Webhook returned ${response.status}: ${response.statusText}`);
			}

			this.logger.info('Webhook notification sent', {
				url: config.notifications.webhook.url,
				errorCode: error.code,
				severity: error.severity,
				responseStatus: response.status,
			});
		}
		catch (webhookError)
		{
			this.logger.error('Failed to send webhook notification', {
				url: config.notifications.webhook.url,
				error: webhookError instanceof Error ? webhookError.message : String(webhookError),
				payload,
			});
			throw webhookError;
		}
	}

	/**
	 * Check if error should be escalated based on severity and frequency
	 */
	private shouldEscalateError(error: AdminPanelErrorType): boolean
	{
		// Escalate critical errors immediately
		if (error.severity === ErrorSeverityEnum.CRITICAL)
		{
			return true;
		}

		// Escalate high severity errors with high frequency
		if (error.severity === ErrorSeverityEnum.HIGH)
		{
			const errorKey = `${error.code}-${error.category}`;
			const analytics = this.errorAnalytics.get(errorKey);

			// Escalate if error has occurred more than 5 times in the last hour
			if (analytics && analytics.frequency > 5)
			{
				const timeDiff = Date.now() - analytics.firstOccurrence.getTime();
				if (timeDiff < 3600000)
				{
					return true;
				}
			}
		}

		return false;
	}

	/**
	 * Execute retry logic with exponential backoff
	 */
	private async executeRetryLogic(
		error: AdminPanelErrorType,
		retryConfig: { maxAttempts?: number; backoffStrategy?: string; baseDelayMs?: number },
	): Promise<void>
	{
		const maxAttempts = retryConfig.maxAttempts || 3;
		const baseDelay = retryConfig.baseDelayMs || 1000;

		this.logger.info('Executing retry logic', {
			errorCode: error.code,
			maxAttempts,
			backoffStrategy: retryConfig.backoffStrategy || 'exponential',
		});

		// Store retry attempt information for monitoring
		const retryKey = `retry-${error.context.correlationId}`;
		void retryKey; // placeholder to avoid unused var

		// In a real implementation, this would trigger the actual retry mechanism
		for (let attempt = 1; attempt <= maxAttempts; attempt++)
		{
			const delay = baseDelay * Math.pow(2, attempt - 1);
			this.logger.debug('Retry attempt scheduled', {
				errorCode: error.code,
				attempt,
				delayMs: delay,
				correlationId: error.context.correlationId,
			});
		}
	}

	/**
	 * Execute fallback logic
	 */
	private async executeFallbackLogic(
		error: AdminPanelErrorType,
		fallbackConfig: { fallbackAction?: string; degradedService?: string },
	): Promise<void>
	{
		this.logger.info('Executing fallback logic', {
			errorCode: error.code,
			fallbackAction: fallbackConfig.fallbackAction,
			degradedService: fallbackConfig.degradedService,
		});

		// Mark as being investigated due to fallback activation
		const errorKey = `${error.code}-${error.category}`;
		const analytics = this.errorAnalytics.get(errorKey);
		if (analytics)
		{
			analytics.resolutionStatus = 'investigating';
			this.errorAnalytics.set(errorKey, analytics);
		}
	}

	/**
	 * Execute graceful degradation
	 */
	private async executeGracefulDegradation(
		error: AdminPanelErrorType,
		degradationConfig: { degradedFeatures?: string[]; alternativeActions?: string[]; userMessage?: string },
	): Promise<void>
	{
		this.logger.warn('Executing graceful degradation', {
			errorCode: error.code,
			degradedFeatures: degradationConfig.degradedFeatures,
			alternativeActions: degradationConfig.alternativeActions,
		});

		// Track as investigating while degraded
		const errorKey = `${error.code}-${error.category}`;
		const analytics = this.errorAnalytics.get(errorKey);
		if (analytics)
		{
			analytics.resolutionStatus = 'investigating';
			this.errorAnalytics.set(errorKey, analytics);
		}
	}

	/**
	 * Escalate error to higher level support
	 */
	private async escalateError(
		error: AdminPanelErrorType,
		recipients: string[],
	): Promise<void>
	{
		this.logger.warn('Error escalation triggered', {
			errorCode: error.code,
			severity: error.severity,
			recipients,
			errorId: error.context.correlationId,
		});

		try
		{
			if (this.shouldEscalateError(error))
			{
				const escalationId = uuidv4();
				this.logger.warn('Escalating error due to severity or frequency', {
					escalationId,
					errorCode: error.code,
					severity: error.severity,
					recipients: recipients.length,
				});

				for (const recipientEmail of recipients)
				{
					await this.sendNotification({
						id: uuidv4(),
						errorId: error.context.correlationId || uuidv4(),
						channel: 'email',
						recipient: recipientEmail,
						status: 'pending',
						retryCount: 0,
					}, error);
				}

				const errorKey = `${error.code}-${error.category}`;
				const analytics = this.errorAnalytics.get(errorKey);
				if (analytics)
				{
					analytics.resolutionStatus = 'investigating';
					this.errorAnalytics.set(errorKey, analytics);
				}
			}
		}
		catch (escalationError)
		{
			this.logger.error('Failed to escalate error', {
				originalError: error.code,
				escalationError: escalationError instanceof Error ? escalationError.message : String(escalationError),
			});
		}
	}

	/**
 * Attempt error recovery
 */
	private async attemptRecovery(
    error: AdminPanelErrorType,
    options: ErrorRecoveryOptionsType,
): Promise<void>
{
    this.logger.info('Attempting error recovery', {
        errorCode: error.code,
        recoveryOptions: options,
    });

    // Retry logic
    if (options.retry?.enabled && error.retryable)
    {
        // Implement retry logic with exponential backoff
        await this.executeRetryLogic(error, options.retry);
    }

    // Fallback logic
    if (options.fallback?.enabled)
    {
        // Implement fallback logic
        await this.executeFallbackLogic(error, options.fallback);
    }

    // Graceful degradation
    if (options.gracefulDegradation?.enabled)
    {
        // Implement graceful degradation
        await this.executeGracefulDegradation(error, options.gracefulDegradation);
    }
}

/**
 * Get error analytics
 */
public getErrorAnalytics(): ErrorAnalyticsType[]
{
    return Array.from(this.errorAnalytics.values());
}

/**
 * Get audit logs
 */
public getAuditLogs(limit?: number): AuditLogEntryType[]
{
    const logs = [...this.auditLogs].reverse(); // Most recent first
    return limit ? logs.slice(0, limit) : logs;
}

/**
 * Clear error analytics
 */
public clearErrorAnalytics(): void
{
    this.errorAnalytics.clear();
}

/**
 * Update error reporting configuration
 */
public updateReportingConfig(config: Partial<ErrorReportingConfigType>): void
{
    this.errorReportingConfig = {
        ...this.errorReportingConfig,
        ...config,
    };
}

/**
 * Check if error should be reported
 */
private shouldReportError(error: AdminPanelErrorType): boolean
{
    // Always report critical and high severity errors
    if (error.severity === ErrorSeverityEnum.CRITICAL || error.severity === ErrorSeverityEnum.HIGH)
    {
        return true;
    }

    // Report medium severity errors if they occur frequently
    if (error.severity === ErrorSeverityEnum.MEDIUM)
    {
        const analytics = this.errorAnalytics.get(`${error.code}_${error.category}`);
        return analytics ? analytics.frequency > 5 : false;
    }

    return false;
}

/**
 * Get appropriate log level for error severity
 */
private getLogLevel(severity: ErrorSeverityEnum): string
{
    switch (severity)
    {
        case ErrorSeverityEnum.CRITICAL:
        case ErrorSeverityEnum.HIGH:
            return 'error';
        case ErrorSeverityEnum.MEDIUM:
            return 'warn';
        case ErrorSeverityEnum.LOW:
            return 'info';
        default:
            return 'debug';
    }
}

/**
 * Get HTTP status code for error code
 */
private getHttpStatusCode(errorCode: ErrorCodesEnum): number
{
    const statusCodes: Record<string, number> = {
        // Authentication errors
        AUTH_001: 401, // AUTHENTICATION_FAILED
        AUTH_002: 403, // INSUFFICIENT_PERMISSIONS
        AUTH_003: 401, // SESSION_EXPIRED
        AUTH_004: 401, // INVALID_CREDENTIALS
        AUTH_005: 423, // ACCOUNT_LOCKED
        AUTH_006: 401, // PASSWORD_EXPIRED
        AUTH_007: 429, // CONCURRENT_SESSION_LIMIT
        AUTH_008: 401, // INVALID_SESSION

        // Service errors
        SVC_001: 503, // SERVICE_UNAVAILABLE
        SVC_002: 504, // SERVICE_TIMEOUT
        SVC_003: 502, // SERVICE_CONNECTION_FAILED
        SVC_004: 503, // SERVICE_DEGRADED
        SVC_005: 503, // SERVICE_OVERLOADED
        SVC_006: 503, // SERVICE_MAINTENANCE

        // Database errors
        DB_001: 503, // DATABASE_CONNECTION_FAILED
        DB_002: 500, // DATABASE_QUERY_FAILED
        DB_003: 504, // DATABASE_TIMEOUT
        DB_004: 400, // DATABASE_CONSTRAINT_VIOLATION
        DB_005: 409, // DATABASE_DEADLOCK
        DB_006: 500, // DATABASE_CORRUPTION
        DB_007: 500, // DATABASE_MIGRATION_FAILED
        DB_008: 500, // DATABASE_BACKUP_FAILED

        // Request errors
        REQ_001: 400, // INVALID_REQUEST
        REQ_002: 400, // MISSING_REQUIRED_FIELD
        REQ_003: 400, // INVALID_FIELD_FORMAT
        REQ_004: 413, // REQUEST_TOO_LARGE
        REQ_005: 415, // UNSUPPORTED_MEDIA_TYPE
        REQ_006: 400, // MALFORMED_JSON

        // Rate limiting errors
        RATE_001: 429, // RATE_LIMIT_EXCEEDED
        RATE_002: 429, // QUOTA_EXCEEDED
        RATE_003: 429, // CONCURRENT_REQUEST_LIMIT

        // Configuration errors
        CFG_001: 500, // CONFIGURATION_ERROR
        CFG_002: 400, // INVALID_CONFIGURATION
        CFG_003: 500, // MISSING_CONFIGURATION
        CFG_004: 400, // CONFIGURATION_VALIDATION_FAILED
        CFG_005: 500, // CONFIGURATION_DEPLOYMENT_FAILED

        // Validation errors
        VAL_001: 400, // VALIDATION_ERROR
        VAL_002: 400, // SCHEMA_VALIDATION_FAILED
        VAL_003: 400, // BUSINESS_RULE_VIOLATION
        VAL_004: 500, // DATA_INTEGRITY_ERROR

        // Domain errors
        DOM_001: 404, // DOMAIN_NOT_FOUND
        DOM_002: 409, // DOMAIN_ALREADY_EXISTS
        DOM_003: 400, // DOMAIN_INVALID_FORMAT
        DOM_004: 500, // DOMAIN_CRAWL_FAILED
        DOM_005: 500, // DOMAIN_RANKING_FAILED
        DOM_006: 500, // DOMAIN_BULK_OPERATION_FAILED

        // File errors
        FILE_001: 404, // FILE_NOT_FOUND
        FILE_002: 500, // FILE_UPLOAD_FAILED
        FILE_003: 500, // FILE_EXPORT_FAILED
        FILE_004: 500, // FILE_IMPORT_FAILED
        FILE_005: 413, // FILE_SIZE_EXCEEDED
        FILE_006: 400, // FILE_FORMAT_INVALID

        // Security errors
        SEC_001: 403, // SECURITY_VIOLATION
        SEC_002: 403, // SUSPICIOUS_ACTIVITY
        SEC_003: 429, // BRUTE_FORCE_DETECTED
        SEC_004: 403, // UNAUTHORIZED_ACCESS_ATTEMPT
        SEC_005: 500, // DATA_BREACH_DETECTED
        SEC_006: 500, // ENCRYPTION_FAILED
    };

    return statusCodes[errorCode] || 500;
}

/**
 * Send email notification
 */
private async sendEmailNotification(
    notification: ErrorNotificationType,
    error: AdminPanelErrorType,
): Promise<void>
{
    // Basic email notification implementation
    // In production, this would integrate with an email service like SendGrid, SES, etc.

    const emailContent = {
        to: notification.recipient,
        subject: `Error Alert: ${error.code} - ${error.severity}`,
        body: `
Error ID: ${error.context.correlationId}
Code: ${error.code}
Severity: ${error.severity}
Message: ${error.message}
User Message: ${error.userMessage || 'N/A'}
Timestamp: ${new Date().toISOString()}
Category: ${error.category}
Recoverable: ${error.recoverable ? 'Yes' : 'No'}

Context:
- User ID: ${error.context.userId || 'N/A'}
- URL: ${error.context.url || 'N/A'}
- Method: ${error.context.method || 'N/A'}
- IP: ${error.context.ipAddress || 'N/A'}

${error.suggestions?.length ? `Suggestions:\n${error.suggestions.map(s => `- ${s}`).join('\n')}` : ''}
        `.trim(),
    };

    this.logger.info('Email notification sent', {
        recipient: notification.recipient,
        errorCode: error.code,
        severity: error.severity,
        emailContent,
    });

    // Integrate with actual email service
    // This is a placeholder implementation - in production, integrate with:
    // - SendGrid: await sgMail.send(emailContent)
    // - AWS SES: await ses.sendEmail(emailParams).promise()
    // - Nodemailer: await transporter.sendMail(mailOptions)

    // For now, we simulate email sending by logging the attempt
    try
    {
        // In production, replace this with actual email service call
        // await this.emailService.send(emailContent);

        this.logger.info('Email notification would be sent', {
            recipient: emailContent.to,
            subject: emailContent.subject,
        });
    }
    catch (emailError)
    {
			this.logger.error('Failed to send email notification', {
				recipient: emailContent.to,
				error: emailError instanceof Error ? emailError.message : String(emailError),
				errorCode: error.code,
			});
    }
		// - Redirect traffic from failing components
	}
}

export default ErrorHandlingService;
