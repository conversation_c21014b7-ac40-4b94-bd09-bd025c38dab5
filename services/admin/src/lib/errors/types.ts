import ErrorCodesEnum, { ErrorSeverityEnum, ErrorCategoryEnum } from './ErrorCodes';

type ErrorContextType =
{
	userId?: string;
	sessionId?: string;
	correlationId?: string;
	requestId?: string;
	userAgent?: string;
	ipAddress?: string;
	url?: string;
	method?: string;
	timestamp: Date;
	stackTrace?: string;
	additionalData?: Record<string, unknown>;
};

type AdminPanelErrorType =
{
	code: ErrorCodesEnum;
	message: string;
	userMessage: string;
	severity: ErrorSeverityEnum;
	category: ErrorCategoryEnum;
	context: ErrorContextType;
	recoverable: boolean;
	retryable: boolean;
	suggestions?: string[];
	documentation?: string;
	impactAssessment?: {
		affectedUsers: number;
		affectedServices: string[];
		businessImpact: 'low' | 'medium' | 'high' | 'critical';
		estimatedResolutionTime?: string;
	};
};

type ErrorRecoveryOptionsType =
{
	// High-level strategy selection used by ErrorRecoveryService
	type: 'retry' | 'fallback' | 'graceful_degradation';
	config:
	{
		// Retry config
		maxAttempts?: number;
		backoffStrategy?: 'linear' | 'exponential' | 'fixed';
		baseDelay?: number;
		maxDelay?: number;
		// Fallback config
		fallbackAction?: string;
		fallbackData?: unknown;
		// Graceful degradation config
		degradedFeatures?: string[];
		alternativeActions?: string[];
	};
	// Sectioned strategy toggles (alternative usage pattern)
	retry?:
	{
		enabled: boolean;
		maxAttempts: number;
		backoffStrategy: 'linear' | 'exponential' | 'fixed';
		baseDelay: number;
		maxDelay: number;
	};
	fallback?:
	{
		enabled: boolean;
		fallbackAction: string;
		fallbackData?: unknown;
	};
	gracefulDegradation?:
	{
		enabled: boolean;
		degradedFeatures: string[];
		alternativeActions: string[];
	};
};

type ErrorReportingConfigType =
{
	automatic: boolean;
	includeStackTrace: boolean;
	includeUserData: boolean;
	includeSystemInfo: boolean;
	notificationChannels: ('email' | 'webhook' | 'sms')[];
	escalationRules: {
		severity: ErrorSeverityEnum;
		escalateAfter: number; // minutes
		recipients: string[];
	}[];
};

type ErrorAnalyticsType =
{
	errorId: string;
	code: ErrorCodesEnum;
	category: ErrorCategoryEnum;
	severity: ErrorSeverityEnum;
	timestamp: Date;
	frequency: number;
	firstOccurrence: Date;
	lastOccurrence: Date;
	affectedUsers: string[];
	resolutionStatus: 'open' | 'investigating' | 'resolved' | 'ignored';
	resolutionTime?: number; // minutes
	rootCause?: string;
	preventionMeasures?: string[];
};

type ErrorNotificationType =
{
	id: string;
	errorId: string;
	channel: 'email' | 'webhook' | 'sms' | 'browser';
	recipient: string;
	status: 'pending' | 'sent' | 'failed' | 'delivered';
	sentAt?: Date;
	deliveredAt?: Date;
	failureReason?: string;
	retryCount: number;
};

type ErrorDebugInfoType =
{
	errorId: string;
	reproductionSteps?: string[];
	environmentInfo:
    {
        nodeVersion: string;
        platform: string;
        memory:
        {
            used: number;
            total: number;
        };
        cpu:
        {
            usage: number;
            cores: number;
        };
        additionalContext?: Record<string, unknown>;
    };
    requestInfo?:
    {
        headers: Record<string, string>;
        body?: unknown;
        query?: Record<string, string>;
        params?: Record<string, string>;
    };
    databaseState?:
    {
        connections: number;
        activeQueries: number;
        slowQueries: unknown[];
    };
    serviceStates?:
    {
        serviceName: string;
        status: string;
        responseTime: number;
        lastHealthCheck: Date;
    }[];
};

type AuditLogEntryType =
{
	id: string;
	correlationId: string;
	timestamp: Date;
	userId?: string;
	username?: string;
	action: string;
	resource: string;
	resourceId?: string;
	method: string;
	url: string;
	ipAddress: string;
	userAgent: string;
	success: boolean;
	duration: number;
	statusCode: number;
	errorCode?: ErrorCodesEnum;
	errorMessage?: string;
	requestBody?: unknown;
	responseBody?: unknown;
	metadata?: Record<string, unknown>;
};

type ErrorRateMetricsType =
{
	timeWindow: string;
	totalRequests: number;
	totalErrors: number;
	errorRate: number;
	errorsByCode: Record<string, number>;
	errorsByCategory: Record<string, number>;
	errorsBySeverity: Record<string, number>;
	averageResolutionTime: number;
	slaViolations: number;
	performanceImpact: {
		averageResponseTime: number;
		slowestEndpoints: {
			endpoint: string;
			averageTime: number;
			errorRate: number;
		}[];
	};
};

type ErrorDocumentationType =
{
	errorCode: ErrorCodesEnum;
	title: string;
	description: string;
	commonCauses: string[];
	troubleshootingSteps: string[];
	resolutionProcedures: string[];
	preventionMeasures: string[];
	relatedErrors: ErrorCodesEnum[];
	lastUpdated: Date;
	tags: string[];
};

// Export all types
export type {
	ErrorContextType,
	AdminPanelErrorType,
	ErrorRecoveryOptionsType,
	ErrorReportingConfigType,
	ErrorAnalyticsType,
	ErrorNotificationType,
	ErrorDebugInfoType,
	AuditLogEntryType,
	ErrorRateMetricsType,
	ErrorDocumentationType,
};
