import { v4 as uuidv4 } from 'uuid';

import ErrorCodesEnum, { ErrorSeverityEnum, ErrorCategoryEnum } from './ErrorCodes';

import type {
	AdminPanelErrorType,
	ErrorContextType,
	// ErrorRecoveryOptionsType,
} from './types';

/**
 * Custom error class for the admin panel with structured error handling
 */
class AdminPanelError extends Error
{
	public readonly id: string;

	public readonly code: ErrorCodesEnum;

	public readonly userMessage: string;

	public readonly severity: ErrorSeverityEnum;

	public readonly category: ErrorCategoryEnum;

	public readonly context: ErrorContextType;

	public readonly recoverable: boolean;

	public readonly retryable: boolean;

	public readonly suggestions: string[];

	public readonly documentation?: string;

	public readonly impactAssessment?: AdminPanelErrorType['impactAssessment'];

	public readonly timestamp: Date;

	constructor(
		code: ErrorCodesEnum,
		message: string,
		userMessage?: string,
		context?: Partial<ErrorContextType>,
		options?: {
			severity?: ErrorSeverityEnum;
			category?: ErrorCategoryEnum;
			recoverable?: boolean;
			retryable?: boolean;
			suggestions?: string[];
			documentation?: string;
			impactAssessment?: AdminPanelErrorType['impactAssessment'];
			cause?: Error;
		},
	)
	{
		super(message);

		this.name = 'AdminPanelError';
		this.id = uuidv4();
		this.code = code;
		this.userMessage = userMessage || this.getDefaultUserMessage(code);
		this.severity = options?.severity || this.getDefaultSeverity(code);
		this.category = options?.category || this.getDefaultCategory(code);
		this.recoverable = options?.recoverable ?? this.getDefaultRecoverable(code);
		this.retryable = options?.retryable ?? this.getDefaultRetryable(code);
		this.suggestions = options?.suggestions || this.getDefaultSuggestions(code);
		this.documentation = options?.documentation;
		this.impactAssessment = options?.impactAssessment;
		this.timestamp = new Date();

		this.context = {
			timestamp: this.timestamp,
			correlationId: context?.correlationId || uuidv4(),
			stackTrace: this.stack,
			...context,
		};

		// Maintain proper stack trace
		if (Error.captureStackTrace)
		{
			Error.captureStackTrace(this, AdminPanelError);
		}

		// Chain the cause if provided
		if (options?.cause)
		{
			this.cause = options.cause;
		}
	}

	/**
   * Convert error to structured format for logging and reporting
   */
	public toStructured(): AdminPanelErrorType
	{
		return {
			code: this.code,
			message: this.message,
			userMessage: this.userMessage,
			severity: this.severity,
			category: this.category,
			context: this.context,
			recoverable: this.recoverable,
			retryable: this.retryable,
			suggestions: this.suggestions,
			documentation: this.documentation,
			impactAssessment: this.impactAssessment,
		};
	}

	/**
   * Convert error to JSON for API responses
   */
	public toJSON(): Record<string, any>
	{
		return ({
			id: this.id,
			code: this.code,
			message: this.userMessage,
			severity: this.severity,
			category: this.category,
			recoverable: this.recoverable,
			retryable: this.retryable,
			suggestions: this.suggestions,
			timestamp: this.timestamp.toISOString(),
			correlationId: this.context.correlationId,
		});
	}

	/**
   * Get default user-friendly message for error code
   */
	private getDefaultUserMessage(code: ErrorCodesEnum): string
	{
		const messages: Record<ErrorCodesEnum, string> =
		{
			[ErrorCodesEnum.AUTHENTICATION_FAILED]: 'Invalid username or password. Please try again.',
			[ErrorCodesEnum.INSUFFICIENT_PERMISSIONS]: 'You do not have permission to perform this action.',
			[ErrorCodesEnum.SESSION_EXPIRED]: 'Your session has expired. Please log in again.',
			[ErrorCodesEnum.INVALID_CREDENTIALS]: 'The provided credentials are invalid.',
			[ErrorCodesEnum.ACCOUNT_LOCKED]: 'Your account has been locked due to multiple failed login attempts.',
			[ErrorCodesEnum.PASSWORD_EXPIRED]: 'Your password has expired. Please change your password.',
			[ErrorCodesEnum.CONCURRENT_SESSION_LIMIT]: 'Maximum number of concurrent sessions reached.',
			[ErrorCodesEnum.INVALID_SESSION]: 'Your session is invalid. Please log in again.',

			[ErrorCodesEnum.SERVICE_UNAVAILABLE]: 'The requested service is currently unavailable. Please try again later.',
			[ErrorCodesEnum.SERVICE_TIMEOUT]: 'The service request timed out. Please try again.',
			[ErrorCodesEnum.SERVICE_CONNECTION_FAILED]: 'Unable to connect to the service. Please check your connection.',
			[ErrorCodesEnum.SERVICE_DEGRADED]: 'The service is experiencing performance issues.',
			[ErrorCodesEnum.SERVICE_OVERLOADED]: 'The service is currently overloaded. Please try again later.',
			[ErrorCodesEnum.SERVICE_MAINTENANCE]: 'The service is under maintenance. Please try again later.',

			[ErrorCodesEnum.DATABASE_CONNECTION_FAILED]: 'Database connection failed. Please try again later.',
			[ErrorCodesEnum.DATABASE_QUERY_FAILED]: 'Database query failed. Please contact support.',
			[ErrorCodesEnum.DATABASE_TIMEOUT]: 'Database operation timed out. Please try again.',
			[ErrorCodesEnum.DATABASE_CONSTRAINT_VIOLATION]: 'Data validation failed. Please check your input.',
			[ErrorCodesEnum.DATABASE_DEADLOCK]: 'Database deadlock detected. Please try again.',
			[ErrorCodesEnum.DATABASE_CORRUPTION]: 'Database corruption detected. Please contact support immediately.',
			[ErrorCodesEnum.DATABASE_MIGRATION_FAILED]: 'Database migration failed. Please contact support.',
			[ErrorCodesEnum.DATABASE_BACKUP_FAILED]: 'Database backup failed. Please contact support.',

			[ErrorCodesEnum.INVALID_REQUEST]: 'The request is invalid. Please check your input.',
			[ErrorCodesEnum.MISSING_REQUIRED_FIELD]: 'Required field is missing. Please provide all required information.',
			[ErrorCodesEnum.INVALID_FIELD_FORMAT]: 'Invalid field format. Please check your input.',
			[ErrorCodesEnum.REQUEST_TOO_LARGE]: 'Request is too large. Please reduce the size of your request.',
			[ErrorCodesEnum.UNSUPPORTED_MEDIA_TYPE]: 'Unsupported media type. Please use a supported format.',
			[ErrorCodesEnum.MALFORMED_JSON]: 'Invalid JSON format. Please check your request format.',

			[ErrorCodesEnum.RATE_LIMIT_EXCEEDED]: 'Rate limit exceeded. Please wait before making another request.',
			[ErrorCodesEnum.QUOTA_EXCEEDED]: 'Quota exceeded. Please contact support to increase your limits.',
			[ErrorCodesEnum.CONCURRENT_REQUEST_LIMIT]: 'Too many concurrent requests. Please wait and try again.',

			[ErrorCodesEnum.CONFIGURATION_ERROR]: 'Configuration error detected. Please contact support.',
			[ErrorCodesEnum.INVALID_CONFIGURATION]: 'Invalid configuration. Please check your settings.',
			[ErrorCodesEnum.MISSING_CONFIGURATION]: 'Required configuration is missing.',
			[ErrorCodesEnum.CONFIGURATION_VALIDATION_FAILED]: 'Configuration validation failed.',
			[ErrorCodesEnum.CONFIGURATION_DEPLOYMENT_FAILED]: 'Configuration deployment failed.',

			[ErrorCodesEnum.VALIDATION_ERROR]: 'Validation error. Please check your input.',
			[ErrorCodesEnum.SCHEMA_VALIDATION_FAILED]: 'Schema validation failed. Please check your data format.',
			[ErrorCodesEnum.BUSINESS_RULE_VIOLATION]: 'Business rule violation. Please check your request.',
			[ErrorCodesEnum.DATA_INTEGRITY_ERROR]: 'Data integrity error detected.',

			[ErrorCodesEnum.DOMAIN_NOT_FOUND]: 'Domain not found. Please check the domain name.',
			[ErrorCodesEnum.DOMAIN_ALREADY_EXISTS]: 'Domain already exists in the system.',
			[ErrorCodesEnum.DOMAIN_INVALID_FORMAT]: 'Invalid domain format. Please check the domain name.',
			[ErrorCodesEnum.DOMAIN_CRAWL_FAILED]: 'Domain crawl failed. Please try again later.',
			[ErrorCodesEnum.DOMAIN_RANKING_FAILED]: 'Domain ranking calculation failed.',
			[ErrorCodesEnum.DOMAIN_BULK_OPERATION_FAILED]: 'Bulk domain operation failed.',

			[ErrorCodesEnum.CRAWL_JOB_NOT_FOUND]: 'Crawl job not found.',
			[ErrorCodesEnum.CRAWL_JOB_CREATION_FAILED]: 'Failed to create crawl job.',
			[ErrorCodesEnum.CRAWL_JOB_EXECUTION_FAILED]: 'Crawl job execution failed.',
			[ErrorCodesEnum.CRAWL_JOB_TIMEOUT]: 'Crawl job timed out.',
			[ErrorCodesEnum.CRAWL_JOB_CANCELLED]: 'Crawl job was cancelled.',
			[ErrorCodesEnum.CRAWL_QUEUE_FULL]: 'Crawl queue is full. Please try again later.',

			[ErrorCodesEnum.SEEDER_QUEUE_ERROR]: 'Seeder queue error occurred.',
			[ErrorCodesEnum.SEEDER_CONNECTOR_FAILED]: 'Seeder connector failed.',
			[ErrorCodesEnum.SEEDER_STRATEGY_FAILED]: 'Seeder strategy execution failed.',
			[ErrorCodesEnum.SEEDER_CONTENT_GENERATION_FAILED]: 'Content generation failed.',
			[ErrorCodesEnum.SEEDER_VALIDATION_FAILED]: 'Seeder validation failed.',

			[ErrorCodesEnum.ALERT_CREATION_FAILED]: 'Failed to create alert.',
			[ErrorCodesEnum.ALERT_NOTIFICATION_FAILED]: 'Alert notification failed.',
			[ErrorCodesEnum.ALERT_ESCALATION_FAILED]: 'Alert escalation failed.',
			[ErrorCodesEnum.ALERT_RULE_VALIDATION_FAILED]: 'Alert rule validation failed.',

			[ErrorCodesEnum.FILE_NOT_FOUND]: 'File not found.',
			[ErrorCodesEnum.FILE_UPLOAD_FAILED]: 'File upload failed.',
			[ErrorCodesEnum.FILE_EXPORT_FAILED]: 'File export failed.',
			[ErrorCodesEnum.FILE_IMPORT_FAILED]: 'File import failed.',
			[ErrorCodesEnum.FILE_SIZE_EXCEEDED]: 'File size exceeds the maximum allowed limit.',
			[ErrorCodesEnum.FILE_FORMAT_INVALID]: 'Invalid file format.',

			[ErrorCodesEnum.INTERNAL_SERVER_ERROR]: 'An internal server error occurred. Please try again later.',
			[ErrorCodesEnum.MEMORY_ALLOCATION_FAILED]: 'Memory allocation failed. Please contact support.',
			[ErrorCodesEnum.THREAD_POOL_EXHAUSTED]: 'System resources exhausted. Please try again later.',
			[ErrorCodesEnum.RESOURCE_EXHAUSTED]: 'System resources exhausted. Please try again later.',
			[ErrorCodesEnum.UNEXPECTED_ERROR]: 'An unexpected error occurred. Please contact support.',

			[ErrorCodesEnum.NETWORK_ERROR]: 'Network error occurred. Please check your connection.',
			[ErrorCodesEnum.CONNECTION_TIMEOUT]: 'Connection timed out. Please try again.',
			[ErrorCodesEnum.DNS_RESOLUTION_FAILED]: 'DNS resolution failed.',
			[ErrorCodesEnum.SSL_HANDSHAKE_FAILED]: 'SSL handshake failed.',
			[ErrorCodesEnum.PROXY_ERROR]: 'Proxy error occurred.',

			[ErrorCodesEnum.SECURITY_VIOLATION]: 'Security violation detected.',
			[ErrorCodesEnum.SUSPICIOUS_ACTIVITY]: 'Suspicious activity detected.',
			[ErrorCodesEnum.BRUTE_FORCE_DETECTED]: 'Brute force attack detected.',
			[ErrorCodesEnum.UNAUTHORIZED_ACCESS_ATTEMPT]: 'Unauthorized access attempt detected.',
			[ErrorCodesEnum.DATA_BREACH_DETECTED]: 'Potential data breach detected.',
			[ErrorCodesEnum.ENCRYPTION_FAILED]: 'Encryption operation failed.',
		};

		return messages[code] || 'An error occurred. Please try again or contact support.';
	}

	/**
   * Get default severity for error code
   */
	private getDefaultSeverity(code: ErrorCodesEnum): ErrorSeverityEnum
	{
		const criticalCodes =
		[
			ErrorCodesEnum.DATABASE_CORRUPTION,
			ErrorCodesEnum.DATA_BREACH_DETECTED,
			ErrorCodesEnum.SECURITY_VIOLATION,
			ErrorCodesEnum.MEMORY_ALLOCATION_FAILED,
		];

		const highCodes =
		[
			ErrorCodesEnum.DATABASE_CONNECTION_FAILED,
			ErrorCodesEnum.SERVICE_UNAVAILABLE,
			ErrorCodesEnum.INTERNAL_SERVER_ERROR,
			ErrorCodesEnum.BRUTE_FORCE_DETECTED,
			ErrorCodesEnum.UNAUTHORIZED_ACCESS_ATTEMPT,
		];

		const mediumCodes =
		[
			ErrorCodesEnum.AUTHENTICATION_FAILED,
			ErrorCodesEnum.INSUFFICIENT_PERMISSIONS,
			ErrorCodesEnum.VALIDATION_ERROR,
			ErrorCodesEnum.CONFIGURATION_ERROR,
		];

		if (criticalCodes.includes(code)) return ErrorSeverityEnum.CRITICAL;
		if (highCodes.includes(code)) return ErrorSeverityEnum.HIGH;
		if (mediumCodes.includes(code)) return ErrorSeverityEnum.MEDIUM;
		return ErrorSeverityEnum.LOW;
	}

	/**
   * Get default category for error code
   */
	private getDefaultCategory(code: ErrorCodesEnum): ErrorCategoryEnum
	{
		const codeString = code.toString();

		if (codeString.startsWith('AUTH_')) return ErrorCategoryEnum.AUTHENTICATION;
		if (codeString.startsWith('DB_')) return ErrorCategoryEnum.DATABASE;
		if (codeString.startsWith('SVC_')) return ErrorCategoryEnum.SERVICE;
		if (codeString.startsWith('NET_')) return ErrorCategoryEnum.NETWORK;
		if (codeString.startsWith('CFG_')) return ErrorCategoryEnum.CONFIGURATION;
		if (codeString.startsWith('VAL_')) return ErrorCategoryEnum.VALIDATION;
		if (codeString.startsWith('SEC_')) return ErrorCategoryEnum.SECURITY;
		if (codeString.startsWith('INT_')) return ErrorCategoryEnum.SYSTEM;

		return ErrorCategoryEnum.SYSTEM;
	}

	/**
   * Get default recoverable status for error code
   */
	private getDefaultRecoverable(code: ErrorCodesEnum): boolean
	{
		const nonRecoverableCodes =
		[
			ErrorCodesEnum.DATABASE_CORRUPTION,
			ErrorCodesEnum.DATA_BREACH_DETECTED,
			ErrorCodesEnum.MEMORY_ALLOCATION_FAILED,
			ErrorCodesEnum.THREAD_POOL_EXHAUSTED,
		];

		return !nonRecoverableCodes.includes(code);
	}

	/**
   * Get default retryable status for error code
   */
	private getDefaultRetryable(code: ErrorCodesEnum): boolean
	{
		const retryableCodes =
		[
			ErrorCodesEnum.SERVICE_TIMEOUT,
			ErrorCodesEnum.SERVICE_UNAVAILABLE,
			ErrorCodesEnum.DATABASE_TIMEOUT,
			ErrorCodesEnum.DATABASE_DEADLOCK,
			ErrorCodesEnum.CONNECTION_TIMEOUT,
			ErrorCodesEnum.NETWORK_ERROR,
		];

		return retryableCodes.includes(code);
	}

	/**
   * Get default suggestions for error code
   */
	private getDefaultSuggestions(code: ErrorCodesEnum): string[]
	{
		const suggestions: Partial<Record<ErrorCodesEnum, string[]>> =
		{
			[ErrorCodesEnum.AUTHENTICATION_FAILED]: [
				'Check your username and password',
				'Ensure caps lock is not enabled',
				'Contact administrator if problem persists',
			],
			[ErrorCodesEnum.SESSION_EXPIRED]: [
				'Log in again to continue',
				'Save your work before the session expires',
			],
			[ErrorCodesEnum.SERVICE_UNAVAILABLE]: [
				'Try again in a few minutes',
				'Check service status page',
				'Contact support if issue persists',
			],
			[ErrorCodesEnum.DATABASE_CONNECTION_FAILED]: [
				'Check database connectivity',
				'Verify database credentials',
				'Contact database administrator',
			],
			[ErrorCodesEnum.RATE_LIMIT_EXCEEDED]: [
				'Wait before making another request',
				'Reduce request frequency',
				'Contact support to increase limits',
			],
			// Add more suggestions as needed
		};

		return suggestions[code] || ['Contact support for assistance'];
	}
}

export default AdminPanelError;
