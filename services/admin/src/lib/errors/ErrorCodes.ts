/**
 * Centralized error codes for the admin panel
 * Structured with prefixes for easy categorization
 */

enum ErrorCodesEnum
{
	// Authentication Errors (AUTH_xxx)
	AUTHENTICATION_FAILED = 'AUTH_001',
	INSUFFICIENT_PERMISSIONS = 'AUTH_002',
	SESSION_EXPIRED = 'AUTH_003',
	INVALID_CREDENTIALS = 'AUTH_004',
	ACCOUNT_LOCKED = 'AUTH_005',
	PASSWORD_EXPIRED = 'AUTH_006',
	CONCURRENT_SESSION_LIMIT = 'AUTH_007',
	INVALID_SESSION = 'AUTH_008',

	// Service Errors (SVC_xxx)
	SERVICE_UNAVAILABLE = 'SVC_001',
	SERVICE_TIMEOUT = 'SVC_002',
	SERVICE_CONNECTION_FAILED = 'SVC_003',
	SERVICE_DEGRADED = 'SVC_004',
	SERVICE_OVERLOADED = 'SVC_005',
	SERVICE_MAINTENANCE = 'SVC_006',

	// Database Errors (DB_xxx)
	DATABASE_CONNECTION_FAILED = 'DB_001',
	DATABASE_QUERY_FAILED = 'DB_002',
	DATABASE_TIMEOUT = 'DB_003',
	DATABASE_CONSTRAINT_VIOLATION = 'DB_004',
	DATABASE_DEADLOCK = 'DB_005',
	DATABASE_CORRUPTION = 'DB_006',
	DATABASE_MIGRATION_FAILED = 'DB_007',
	DATABASE_BACKUP_FAILED = 'DB_008',

	// Request Errors (REQ_xxx)
	INVALID_REQUEST = 'REQ_001',
	MISSING_REQUIRED_FIELD = 'REQ_002',
	INVALID_FIELD_FORMAT = 'REQ_003',
	REQUEST_TOO_LARGE = 'REQ_004',
	UNSUPPORTED_MEDIA_TYPE = 'REQ_005',
	MALFORMED_JSON = 'REQ_006',

	// Rate Limiting Errors (RATE_xxx)
	RATE_LIMIT_EXCEEDED = 'RATE_001',
	QUOTA_EXCEEDED = 'RATE_002',
	CONCURRENT_REQUEST_LIMIT = 'RATE_003',

	// Configuration Errors (CFG_xxx)
	CONFIGURATION_ERROR = 'CFG_001',
	INVALID_CONFIGURATION = 'CFG_002',
	MISSING_CONFIGURATION = 'CFG_003',
	CONFIGURATION_VALIDATION_FAILED = 'CFG_004',
	CONFIGURATION_DEPLOYMENT_FAILED = 'CFG_005',

	// Validation Errors (VAL_xxx)
	VALIDATION_ERROR = 'VAL_001',
	SCHEMA_VALIDATION_FAILED = 'VAL_002',
	BUSINESS_RULE_VIOLATION = 'VAL_003',
	DATA_INTEGRITY_ERROR = 'VAL_004',

	// Domain Management Errors (DOM_xxx)
	DOMAIN_NOT_FOUND = 'DOM_001',
	DOMAIN_ALREADY_EXISTS = 'DOM_002',
	DOMAIN_INVALID_FORMAT = 'DOM_003',
	DOMAIN_CRAWL_FAILED = 'DOM_004',
	DOMAIN_RANKING_FAILED = 'DOM_005',
	DOMAIN_BULK_OPERATION_FAILED = 'DOM_006',

	// Crawl Job Errors (CRAWL_xxx)
	CRAWL_JOB_NOT_FOUND = 'CRAWL_001',
	CRAWL_JOB_CREATION_FAILED = 'CRAWL_002',
	CRAWL_JOB_EXECUTION_FAILED = 'CRAWL_003',
	CRAWL_JOB_TIMEOUT = 'CRAWL_004',
	CRAWL_JOB_CANCELLED = 'CRAWL_005',
	CRAWL_QUEUE_FULL = 'CRAWL_006',

	// Seeder Errors (SEED_xxx)
	SEEDER_QUEUE_ERROR = 'SEED_001',
	SEEDER_CONNECTOR_FAILED = 'SEED_002',
	SEEDER_STRATEGY_FAILED = 'SEED_003',
	SEEDER_CONTENT_GENERATION_FAILED = 'SEED_004',
	SEEDER_VALIDATION_FAILED = 'SEED_005',

	// Alert Errors (ALERT_xxx)
	ALERT_CREATION_FAILED = 'ALERT_001',
	ALERT_NOTIFICATION_FAILED = 'ALERT_002',
	ALERT_ESCALATION_FAILED = 'ALERT_003',
	ALERT_RULE_VALIDATION_FAILED = 'ALERT_004',

	// File/Export Errors (FILE_xxx)
	FILE_NOT_FOUND = 'FILE_001',
	FILE_UPLOAD_FAILED = 'FILE_002',
	FILE_EXPORT_FAILED = 'FILE_003',
	FILE_IMPORT_FAILED = 'FILE_004',
	FILE_SIZE_EXCEEDED = 'FILE_005',
	FILE_FORMAT_INVALID = 'FILE_006',

	// Internal System Errors (INT_xxx)
	INTERNAL_SERVER_ERROR = 'INT_001',
	MEMORY_ALLOCATION_FAILED = 'INT_002',
	THREAD_POOL_EXHAUSTED = 'INT_003',
	RESOURCE_EXHAUSTED = 'INT_004',
	UNEXPECTED_ERROR = 'INT_005',

	// Network Errors (NET_xxx)
	NETWORK_ERROR = 'NET_001',
	CONNECTION_TIMEOUT = 'NET_002',
	DNS_RESOLUTION_FAILED = 'NET_003',
	SSL_HANDSHAKE_FAILED = 'NET_004',
	PROXY_ERROR = 'NET_005',

	// Security Errors (SEC_xxx)
	SECURITY_VIOLATION = 'SEC_001',
	SUSPICIOUS_ACTIVITY = 'SEC_002',
	BRUTE_FORCE_DETECTED = 'SEC_003',
	UNAUTHORIZED_ACCESS_ATTEMPT = 'SEC_004',
	DATA_BREACH_DETECTED = 'SEC_005',
	ENCRYPTION_FAILED = 'SEC_006',
}

enum ErrorSeverityEnum
{
	LOW = 'low',
	MEDIUM = 'medium',
	HIGH = 'high',
	CRITICAL = 'critical',
}

enum ErrorCategoryEnum
{
	AUTHENTICATION = 'authentication',
	AUTHORIZATION = 'authorization',
	VALIDATION = 'validation',
	DATABASE = 'database',
	NETWORK = 'network',
	SERVICE = 'service',
	CONFIGURATION = 'configuration',
	BUSINESS_LOGIC = 'business_logic',
	SYSTEM = 'system',
	SECURITY = 'security',
}

export {
	ErrorSeverityEnum,
	ErrorCategoryEnum,
};

export default ErrorCodesEnum;
