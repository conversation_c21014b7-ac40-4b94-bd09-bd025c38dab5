import { v4 as uuidv4 } from 'uuid';
import { createIsomorphicLogger } from '@shared/client';
import BrowserHttpClient from '@/lib/utils/BrowserHttpClient';

import { Logger } from '../utils/Logger';

import { ErrorSeverityEnum } from './ErrorCodes';

import type {
	ErrorNotificationType,
	AdminPanelErrorType,
	ErrorReportingConfigType,
} from './types';

/**
 * Service for handling error notifications and escalations
 */
class ErrorNotificationService
{
	private static instance: ErrorNotificationService;

	private logger: Logger;

	private notifications: Map<string, ErrorNotificationType> = new Map();

	private escalationTimers: Map<string, NodeJS.Timeout> = new Map();

	private config: ErrorReportingConfigType;

	private constructor()
	{
		this.logger = new Logger('ErrorNotificationService');
		this.config = {
			automatic: true,
			includeStackTrace: true,
			includeUserData: false,
			includeSystemInfo: true,
			notificationChannels: ['email'],
			escalationRules: [
				{
					severity: ErrorSeverityEnum.CRITICAL,
					escalateAfter: 5,
					recipients: ['<EMAIL>'],
				},
				{
					severity: ErrorSeverityEnum.HIGH,
					escalateAfter: 15,
					recipients: ['<EMAIL>'],
				},
			],
		};
	}

	public static getInstance(): ErrorNotificationService
	{
		if (!ErrorNotificationService.instance)
		{
			ErrorNotificationService.instance = new ErrorNotificationService();
		}
		return ErrorNotificationService.instance;
	}

	/**
   * Send error notification based on configuration
   */
	public async sendErrorNotification(error: AdminPanelErrorType): Promise<void>
	{
		if (!this.config.automatic)
		{
			return;
		}

		// Check if error should trigger immediate notification
		if (this.shouldSendImmediateNotification(error))
		{
			await this.sendImmediateNotifications(error);
		}

		// Set up escalation if configured
		const escalationRule = this.config.escalationRules.find(
			rule => rule.severity === error.severity,
		);

		if (escalationRule)
		{
			this.scheduleEscalation(error, escalationRule);
		}
	}

	/**
   * Send immediate notifications for critical errors
   */
	private async sendImmediateNotifications(error: AdminPanelErrorType): Promise<void>
	{
		const notificationPromises = this.config.notificationChannels.map(channel => this.createAndSendNotification(error, channel, 'immediate'));

		await Promise.allSettled(notificationPromises);
	}

	/**
   * Create and send a notification
   */
	private async createAndSendNotification(
		error: AdminPanelErrorType,
		channel: 'email' | 'webhook' | 'browser' | 'sms',
		type: 'immediate' | 'escalation',
		recipient?: string,
	): Promise<ErrorNotificationType>
	{
		const notification: ErrorNotificationType = {
			id: uuidv4(),
			errorId: error.context.correlationId || uuidv4(),
			channel,
			recipient: recipient || this.getDefaultRecipient(channel),
			status: 'pending',
			retryCount: 0,
		};

		this.notifications.set(notification.id, notification);

		try
		{
			await this.sendNotification(notification, error, type);
			notification.status = 'sent';
			notification.sentAt = new Date();

			this.logger.info('Error notification sent', {
				notificationId: notification.id,
				channel,
				errorCode: error.code,
				type,
			});
		}
		catch (notificationError)
		{
			notification.status = 'failed';
			notification.failureReason = notificationError instanceof Error
				? notificationError.message
				: 'Unknown error';

			this.logger.error('Failed to send error notification', {
				notificationId: notification.id,
				channel,
				error: notificationError,
			});

			// Retry failed notifications
			if (notification.retryCount < 3)
			{
				setTimeout(() =>
				{
					this.retryNotification(notification.id, error, type);
				}, 2 ** notification.retryCount * 1000); // Exponential backoff
			}
		}

		return notification;
	}

	/**
   * Send individual notification based on channel
   */
	private async sendNotification(
		notification: ErrorNotificationType,
		error: AdminPanelErrorType,
		type: 'immediate' | 'escalation',
	): Promise<void>
	{
		const subject = this.generateNotificationSubject(error, type);
		const content = this.generateNotificationContent(error, type);

		switch (notification.channel)
		{
			case 'email':
				await this.sendEmailNotification(notification.recipient, subject, content, error);
				break;

			case 'webhook':
				await this.sendWebhookNotification(notification.recipient, error, type);
				break;

			case 'browser':
				await this.sendBrowserNotification(subject, content, error);
				break;

			case 'sms':
				await this.sendSmsNotification(notification.recipient, subject, content, error);
				break;

			default:
				throw new Error(`Unsupported notification channel: ${notification.channel}`);
		}
	}

	/**
   * Send SMS notification (mock implementation)
   */
	private async sendSmsNotification(
		recipient: string,
		subject: string,
		content: string,
		error: AdminPanelErrorType,
	): Promise<void>
	{
		this.logger.info('SMS notification sent', {
			recipient,
			subject,
			errorCode: error.code,
			severity: error.severity,
		});

		await new Promise(resolve => setTimeout(resolve, 100));
	}

	/**
   * Send email notification
   */
	private async sendEmailNotification(
		recipient: string,
		subject: string,
		content: string,
		error: AdminPanelErrorType,
	): Promise<void>
	{
		const emailData = {
			to: recipient,
			subject,
			html: this.generateEmailHTML(content, error),
			text: content,
		};

		this.logger.info('Email notification sent', {
			recipient,
			subject,
			errorCode: error.code,
			severity: error.severity,
		});

		// Simulate email sending delay
		await new Promise(resolve => setTimeout(resolve, 100));
	}

	/**
   * Send webhook notification
   */
	private async sendWebhookNotification(
		webhookUrl: string,
		error: AdminPanelErrorType,
		type: 'immediate' | 'escalation',
	): Promise<void>
	{
		const payload = {
			type: 'error_notification',
			notificationType: type,
			error: {
				id: error.context.correlationId,
				code: error.code,
				message: error.message,
				userMessage: error.userMessage,
				severity: error.severity,
				category: error.category,
				timestamp: error.context.timestamp,
				context: this.config.includeUserData ? error.context : {
					url: error.context.url,
					timestamp: error.context.timestamp,
				},
			},
			system: this.config.includeSystemInfo ? {
				service: 'admin-panel',
				environment: process.env.NODE_ENV,
				version: process.env.npm_package_version,
			} : undefined,
		};

		try
		{
			const httpClient = new BrowserHttpClient({ timeout: 10000 });
			const response = await httpClient.post(webhookUrl, payload, {
				headers: {
					'User-Agent': 'AdminPanel-ErrorNotification/1.0',
				},
			});

			this.logger.info('Webhook notification sent', {
				webhookUrl,
				errorCode: error.code,
				responseStatus: response.status,
			});
		}
		catch (webhookError)
		{
			this.logger.error('Webhook notification failed', {
				webhookUrl,
				error: webhookError,
			});
			throw webhookError;
		}
	}

	/**
   * Send browser notification
   */
	private async sendBrowserNotification(
		title: string,
		content: string,
		error: AdminPanelErrorType,
	): Promise<void>
	{
		// Browser notifications are handled client-side
		// This method logs the notification for server-side tracking

		this.logger.info('Browser notification triggered', {
			title,
			content,
			errorCode: error.code,
			severity: error.severity,
		});
	}

	/**
   * Schedule error escalation
   */
	private scheduleEscalation(
		error: AdminPanelErrorType,
		escalationRule: ErrorReportingConfigType['escalationRules'][0],
	): void
	{
		const escalationKey = `${error.context.correlationId}_${escalationRule.severity}`;

		// Clear existing escalation timer if any
		const existingTimer = this.escalationTimers.get(escalationKey);
		if (existingTimer)
		{
			clearTimeout(existingTimer);
		}

		// Set new escalation timer
		const timer = setTimeout(async () =>
		{
			await this.executeEscalation(error, escalationRule);
			this.escalationTimers.delete(escalationKey);
		}, escalationRule.escalateAfter * 60 * 1000);

		this.escalationTimers.set(escalationKey, timer);

		this.logger.info('Error escalation scheduled', {
			errorId: error.context.correlationId,
			severity: error.severity,
			escalateAfter: escalationRule.escalateAfter,
			recipients: escalationRule.recipients,
		});
	}

	/**
   * Execute error escalation
   */
	private async executeEscalation(
		error: AdminPanelErrorType,
		escalationRule: ErrorReportingConfigType['escalationRules'][0],
	): Promise<void>
	{
		this.logger.warn('Error escalation triggered', {
			errorId: error.context.correlationId,
			severity: error.severity,
			recipients: escalationRule.recipients,
		});

		// Send escalation notifications to all recipients
		const escalationPromises = escalationRule.recipients.map(recipient => this.createAndSendNotification(error, 'email', 'escalation', recipient));

		await Promise.allSettled(escalationPromises);
	}

	/**
   * Retry failed notification
   */
	private async retryNotification(
		notificationId: string,
		error: AdminPanelErrorType,
		type: 'immediate' | 'escalation',
	): Promise<void>
	{
		const notification = this.notifications.get(notificationId);
		if (!notification)
		{
			return;
		}

		notification.retryCount += 1;
		notification.status = 'pending';

		try
		{
			await this.sendNotification(notification, error, type);
			notification.status = 'sent';
			notification.sentAt = new Date();

			this.logger.info('Error notification retry successful', {
				notificationId,
				retryCount: notification.retryCount,
			});
		}
		catch (retryError)
		{
			notification.status = 'failed';
			notification.failureReason = retryError instanceof Error
				? retryError.message
				: 'Unknown error';

			this.logger.error('Error notification retry failed', {
				notificationId,
				retryCount: notification.retryCount,
				error: retryError,
			});
		}
	}

	/**
   * Get notification history
   */
	public getNotificationHistory(filters?: {
		errorId?: string;
		channel?: ErrorNotificationType['channel'];
		status?: ErrorNotificationType['status'];
		limit?: number;
	}): ErrorNotificationType[]
	{
		let notifications = Array.from(this.notifications.values());

		if (filters)
		{
			if (filters.errorId)
			{
				notifications = notifications.filter(n => n.errorId === filters.errorId);
			}

			if (filters.channel)
			{
				notifications = notifications.filter(n => n.channel === filters.channel);
			}

			if (filters.status)
			{
				notifications = notifications.filter(n => n.status === filters.status);
			}
		}

		// Sort by sent date descending
		notifications.sort((a, b) =>
		{
			const aTime = a.sentAt?.getTime() || 0;
			const bTime = b.sentAt?.getTime() || 0;
			return bTime - aTime;
		});

		return filters?.limit ? notifications.slice(0, filters.limit) : notifications;
	}

	/**
   * Update notification configuration
   */
	public updateConfig(config: Partial<ErrorReportingConfigType>): void
	{
		this.config = {
			...this.config,
			...config,
		};

		this.logger.info('Error notification configuration updated', {
			automatic: this.config.automatic,
			channels: this.config.notificationChannels,
			escalationRules: this.config.escalationRules.length,
		});
	}

	/**
   * Cancel scheduled escalation
   */
	public cancelEscalation(errorId: string, severity: ErrorSeverityEnum): boolean
	{
		const escalationKey = `${errorId}_${severity}`;
		const timer = this.escalationTimers.get(escalationKey);

		if (timer)
		{
			clearTimeout(timer);
			this.escalationTimers.delete(escalationKey);

			this.logger.info('Error escalation cancelled', {
				errorId,
				severity,
			});

			return true;
		}

		return false;
	}

	// Private helper methods

	private shouldSendImmediateNotification(error: AdminPanelErrorType): boolean
	{
		return error.severity === ErrorSeverityEnum.CRITICAL || error.severity === ErrorSeverityEnum.HIGH;
	}

	private getDefaultRecipient(channel: ErrorNotificationType['channel']): string
	{
		switch (channel)
		{
			case 'email':
				return process.env.ERROR_NOTIFICATION_EMAIL || '<EMAIL>';
			case 'webhook':
				return process.env.ERROR_WEBHOOK_URL || 'https://hooks.domain-ranking.com/errors';
			case 'browser':
				return 'browser';
			default:
				return 'unknown';
		}
	}

	private generateNotificationSubject(
		error: AdminPanelErrorType,
		type: 'immediate' | 'escalation',
	): string
	{
		const prefix = type === 'escalation' ? '[ESCALATED]' : '[ALERT]';
		const severity = error.severity.toUpperCase();

		return `${prefix} ${severity} Error: ${error.code} - ${error.userMessage}`;
	}

	private generateNotificationContent(
		error: AdminPanelErrorType,
		type: 'immediate' | 'escalation',
	): string
	{
		const lines = [
      `Error Type: ${type === 'escalation' ? 'Escalated' : 'Immediate'} Notification`,
      `Error ID: ${error.context.correlationId}`,
      `Code: ${error.code}`,
      `Severity: ${error.severity}`,
      `Category: ${error.category}`,
      `Message: ${error.message}`,
      `User Message: ${error.userMessage}`,
      `Timestamp: ${error.context.timestamp?.toISOString()}`,
      '',
		];

		if (error.context.url)
		{
			lines.push(`URL: ${error.context.url}`);
		}

		if (error.context.userId)
		{
			lines.push(`User ID: ${error.context.userId}`);
		}

		if (error.suggestions && error.suggestions.length > 0)
		{
			lines.push('', 'Suggestions:');
			error.suggestions.forEach((suggestion) =>
			{
				lines.push(`- ${suggestion}`);
			});
		}

		if (error.impactAssessment)
		{
			lines.push('', 'Impact Assessment:');
			lines.push(`- Affected Users: ${error.impactAssessment.affectedUsers}`);
			lines.push(`- Business Impact: ${error.impactAssessment.businessImpact}`);
			if (error.impactAssessment.estimatedResolutionTime)
			{
				lines.push(`- Estimated Resolution: ${error.impactAssessment.estimatedResolutionTime}`);
			}
		}

		return lines.join('\n');
	}

	private generateEmailHTML(content: string, error: AdminPanelErrorType): string
	{
		const severityColor = this.getSeverityColor(error.severity);

		return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Error Notification</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .header { background-color: ${severityColor}; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; }
            .error-details { background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 15px 0; }
            .suggestions { background-color: #e8f4fd; padding: 15px; border-radius: 5px; margin: 15px 0; }
            .footer { background-color: #f0f0f0; padding: 10px; text-align: center; font-size: 12px; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>Error Notification</h1>
            <p>Severity: ${error.severity.toUpperCase()}</p>
          </div>
          <div class="content">
            <div class="error-details">
              <pre>${content}</pre>
            </div>
            ${error.suggestions && error.suggestions.length > 0 ? `
              <div class="suggestions">
                <h3>Recommended Actions:</h3>
                <ul>
                  ${error.suggestions.map(s => `<li>${s}</li>`).join('')}
                </ul>
              </div>
            ` : ''}
          </div>
          <div class="footer">
            <p>This is an automated notification from the Admin Panel Error Monitoring System.</p>
          </div>
        </body>
      </html>
    `;
	}

	private getSeverityColor(severity: ErrorSeverityEnum): string
	{
		switch (severity)
		{
			case ErrorSeverityEnum.CRITICAL:
				return '#dc3545';
			case ErrorSeverityEnum.HIGH:
				return '#fd7e14';
			case ErrorSeverityEnum.MEDIUM:
				return '#ffc107';
			case ErrorSeverityEnum.LOW:
				return '#28a745';
			default:
				return '#6c757d';
		}
	}

	}

export default ErrorNotificationService;
