import { v4 as uuidv4 } from 'uuid';

import { Logger } from '../utils/Logger';

import ErrorCodesEnum, { ErrorSeverityEnum, ErrorCategoryEnum } from './ErrorCodes';

import type {
	ErrorAnalyticsType,
	ErrorRateMetricsType,
	AdminPanelErrorType,
} from './types';

/**
 * Service for error analytics, frequency tracking, and trend analysis
 */
class ErrorAnalyticsService
{
	private static instance: ErrorAnalyticsService;

	private logger: Logger;

	private errorAnalytics: Map<string, ErrorAnalyticsType> = new Map();

	private errorRateMetrics: ErrorRateMetricsType[] = [];

	private metricsWindow: number = 60 * 60 * 1000; // 1 hour in milliseconds

	private constructor()
	{
		this.logger = new Logger('ErrorAnalyticsService');

		// Start periodic metrics collection
		this.startMetricsCollection();
	}

	public static getInstance(): ErrorAnalyticsService
	{
		if (!ErrorAnalyticsService.instance)
		{
			ErrorAnalyticsService.instance = new ErrorAnalyticsService();
		}
		return ErrorAnalyticsService.instance;
	}

	/**
   * Record an error occurrence for analytics
   */
	public recordError(error: AdminPanelErrorType): void
	{
		const errorKey = this.generateErrorKey(error);
		const existing = this.errorAnalytics.get(errorKey);

		if (existing)
		{
			// Update existing error analytics
			existing.frequency += 1;
			existing.lastOccurrence = new Date();

			if (error.context.userId && !existing.affectedUsers.includes(error.context.userId))
			{
				existing.affectedUsers.push(error.context.userId);
			}
		}
		else
		{
			// Create new error analytics entry
			const analytics: ErrorAnalyticsType = {
				errorId: uuidv4(),
				code: error.code,
				category: error.category,
				severity: error.severity,
				timestamp: new Date(),
				frequency: 1,
				firstOccurrence: new Date(),
				lastOccurrence: new Date(),
				affectedUsers: error.context.userId ? [error.context.userId] : [],
				resolutionStatus: 'open',
			};

			this.errorAnalytics.set(errorKey, analytics);
		}

		// Log analytics update
		this.logger.debug('Error analytics updated', {
			errorKey,
			frequency: this.errorAnalytics.get(errorKey)?.frequency,
			affectedUsers: this.errorAnalytics.get(errorKey)?.affectedUsers.length,
		});
	}

	/**
   * Get error analytics by various filters
   */
	public getErrorAnalytics(filters?: {
		category?: ErrorCategoryEnum;
		severity?: ErrorSeverityEnum;
		timeRange?: { start: Date; end: Date };
		minFrequency?: number;
		resolutionStatus?: ErrorAnalyticsType['resolutionStatus'];
	}): ErrorAnalyticsType[]
	{
		let analytics = Array.from(this.errorAnalytics.values());

		if (filters)
		{
			if (filters.category)
			{
				analytics = analytics.filter(a => a.category === filters.category);
			}

			if (filters.severity)
			{
				analytics = analytics.filter(a => a.severity === filters.severity);
			}

			if (filters.timeRange)
			{
				analytics = analytics.filter(a => a.timestamp >= filters.timeRange!.start &&
          a.timestamp <= filters.timeRange!.end);
			}

			if (filters.minFrequency)
			{
				analytics = analytics.filter(a => a.frequency >= filters.minFrequency!);
			}

			if (filters.resolutionStatus)
			{
				analytics = analytics.filter(a => a.resolutionStatus === filters.resolutionStatus);
			}
		}

		// Sort by frequency descending, then by last occurrence
		return analytics.sort((a, b) =>
		{
			if (a.frequency !== b.frequency)
			{
				return b.frequency - a.frequency;
			}
			return b.lastOccurrence.getTime() - a.lastOccurrence.getTime();
		});
	}

	/**
   * Get error trends over time
   */
	public getErrorTrends(
		timeRange: { start: Date; end: Date },
		granularity: 'hour' | 'day' | 'week' = 'hour',
	): {
		timestamp: Date;
		totalErrors: number;
		errorsByCategory: Record<string, number>;
		errorsBySeverity: Record<string, number>;
		uniqueUsers: number;
	}[]
	{
		const analytics = this.getErrorAnalytics({ timeRange });
		const trends: Map<string, any> = new Map();

		// Calculate time bucket size
		const bucketSize = this.getBucketSize(granularity);

		for (const error of analytics)
		{
			const bucketKey = this.getBucketKey(error.lastOccurrence, granularity);

			if (!trends.has(bucketKey))
			{
				trends.set(bucketKey, {
					timestamp: this.getBucketTimestamp(bucketKey, granularity),
					totalErrors: 0,
					errorsByCategory: {} as Record<string, number>,
					errorsBySeverity: {} as Record<string, number>,
					uniqueUsers: new Set<string>(),
				});
			}

			const bucket = trends.get(bucketKey);
			bucket.totalErrors += error.frequency;
			bucket.errorsByCategory[error.category] = (bucket.errorsByCategory[error.category] || 0) + error.frequency;
			bucket.errorsBySeverity[error.severity] = (bucket.errorsBySeverity[error.severity] || 0) + error.frequency;

			// Add unique users
			error.affectedUsers.forEach(userId => bucket.uniqueUsers.add(userId));
		}

		// Convert to array and process unique users
		return Array.from(trends.values())
			.map(bucket => ({
				...bucket,
				uniqueUsers: bucket.uniqueUsers.size,
			}))
			.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
	}

	/**
   * Get top errors by frequency
   */
	public getTopErrors(limit: number = 10): ErrorAnalyticsType[]
	{
		return this.getErrorAnalytics()
			.slice(0, limit);
	}

	/**
   * Get error patterns and correlations
   */
	public getErrorPatterns(): {
		frequentPairs: { error1: ErrorCodesEnum; error2: ErrorCodesEnum; correlation: number }[];
		userPatterns: { userId: string; errorCodes: ErrorCodesEnum[]; frequency: number }[];
		timePatterns: { hour: number; errorCount: number; commonErrors: ErrorCodesEnum[] }[];
	}
	{
		const analytics = Array.from(this.errorAnalytics.values());

		// Find frequent error pairs (errors that occur together)
		const frequentPairs = this.findFrequentErrorPairs(analytics);

		// Find user error patterns
		const userPatterns = this.findUserErrorPatterns(analytics);

		// Find time-based patterns
		const timePatterns = this.findTimePatterns(analytics);

		return {
			frequentPairs,
			userPatterns,
			timePatterns,
		};
	}

	/**
   * Update error resolution status
   */
	public updateErrorResolution(
		errorId: string,
		status: ErrorAnalyticsType['resolutionStatus'],
		resolutionTime?: number,
		rootCause?: string,
		preventionMeasures?: string[],
	): boolean
	{
		for (const [key, analytics] of this.errorAnalytics.entries())
		{
			if (analytics.errorId === errorId)
			{
				analytics.resolutionStatus = status;
				if (resolutionTime) analytics.resolutionTime = resolutionTime;
				if (rootCause) analytics.rootCause = rootCause;
				if (preventionMeasures) analytics.preventionMeasures = preventionMeasures;

				this.logger.info('Error resolution updated', {
					errorId,
					status,
					resolutionTime,
					rootCause,
				});

				return true;
			}
		}

		return false;
	}

	/**
   * Get error rate metrics for the current time window
   */
	public getCurrentErrorRateMetrics(): ErrorRateMetricsType
	{
		const now = new Date();
		const windowStart = new Date(now.getTime() - this.metricsWindow);

		const recentAnalytics = this.getErrorAnalytics({
			timeRange: { start: windowStart, end: now },
		});

		const totalErrors = recentAnalytics.reduce((sum, a) => sum + a.frequency, 0);
		const totalRequests = this.estimateTotalRequests(); // This would need to be tracked separately

		const errorsByCode: Record<string, number> = {};
		const errorsByCategory: Record<string, number> = {};
		const errorsBySeverity: Record<string, number> = {};

		for (const analytics of recentAnalytics)
		{
			errorsByCode[analytics.code] = (errorsByCode[analytics.code] || 0) + analytics.frequency;
			errorsByCategory[analytics.category] = (errorsByCategory[analytics.category] || 0) + analytics.frequency;
			errorsBySeverity[analytics.severity] = (errorsBySeverity[analytics.severity] || 0) + analytics.frequency;
		}

		const resolvedErrors = recentAnalytics.filter(a => a.resolutionStatus === 'resolved');
		const averageResolutionTime = resolvedErrors.length > 0
			? resolvedErrors.reduce((sum, a) => sum + (a.resolutionTime || 0), 0) / resolvedErrors.length
			: 0;

		return {
			timeWindow: `${this.metricsWindow / 1000 / 60} minutes`,
			totalRequests,
			totalErrors,
			errorRate: totalRequests > 0 ? (totalErrors / totalRequests) * 100 : 0,
			errorsByCode,
			errorsByCategory,
			errorsBySeverity,
			averageResolutionTime,
			slaViolations: this.calculateSLAViolations(recentAnalytics),
			performanceImpact: this.calculatePerformanceImpact(recentAnalytics),
		};
	}

	/**
   * Get error prevention recommendations
   */
	public getPreventionRecommendations(): {
		errorCode: ErrorCodesEnum;
		frequency: number;
		recommendations: string[];
		priority: 'high' | 'medium' | 'low';
	}[]
	{
		const topErrors = this.getTopErrors(20);

		return topErrors.map(error => ({
			errorCode: error.code,
			frequency: error.frequency,
			recommendations: this.generateRecommendations(error),
			priority: this.calculateRecommendationPriority(error),
		}));
	}

	/**
   * Clear analytics data
   */
	public clearAnalytics(olderThan?: Date): void
	{
		if (olderThan)
		{
			for (const [key, analytics] of this.errorAnalytics.entries())
			{
				if (analytics.timestamp < olderThan)
				{
					this.errorAnalytics.delete(key);
				}
			}
		}
		else
		{
			this.errorAnalytics.clear();
		}

		this.logger.info('Error analytics cleared', {
			olderThan: olderThan?.toISOString(),
			remainingEntries: this.errorAnalytics.size,
		});
	}

	/**
   * Export analytics data
   */
	public exportAnalytics(format: 'json' | 'csv'): string
	{
		const analytics = this.getErrorAnalytics();

		if (format === 'json')
		{
			return JSON.stringify(analytics, null, 2);
		}

		// CSV format
		const headers = [
			'Error ID',
			'Code',
			'Category',
			'Severity',
			'Frequency',
			'First Occurrence',
			'Last Occurrence',
			'Affected Users',
			'Resolution Status',
			'Resolution Time',
			'Root Cause',
		];

		const rows = analytics.map(a => [
			a.errorId,
			a.code,
			a.category,
			a.severity,
			a.frequency.toString(),
			a.firstOccurrence.toISOString(),
			a.lastOccurrence.toISOString(),
			a.affectedUsers.length.toString(),
			a.resolutionStatus,
			(a.resolutionTime || '').toString(),
			a.rootCause || '',
		]);

		return [headers.join(','), ...rows.map(row => row.join(','))].join('\n');
	}

	// Private helper methods

	private generateErrorKey(error: AdminPanelErrorType): string
	{
		return `${error.code}_${error.category}_${error.severity}`;
	}

	private getBucketSize(granularity: 'hour' | 'day' | 'week'): number
	{
		switch (granularity)
		{
			case 'hour': return 60 * 60 * 1000;
			case 'day': return 24 * 60 * 60 * 1000;
			case 'week': return 7 * 24 * 60 * 60 * 1000;
		}
	}

	private getBucketKey(date: Date, granularity: 'hour' | 'day' | 'week'): string
	{
		switch (granularity)
		{
			case 'hour':
				return `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}-${date.getHours()}`;
			case 'day':
				return `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}`;
			case 'week':
				const weekStart = new Date(date);
				weekStart.setDate(date.getDate() - date.getDay());
				return `${weekStart.getFullYear()}-W${Math.ceil(weekStart.getDate() / 7)}`;
		}
	}

	private getBucketTimestamp(bucketKey: string, granularity: 'hour' | 'day' | 'week'): Date
	{
		const parts = bucketKey.split('-');

		switch (granularity)
		{
			case 'hour':
				return new Date(parseInt(parts[0]), parseInt(parts[1]), parseInt(parts[2]), parseInt(parts[3]));
			case 'day':
				return new Date(parseInt(parts[0]), parseInt(parts[1]), parseInt(parts[2]));
			case 'week':
				const year = parseInt(parts[0]);
				const week = parseInt(parts[1].substring(1));
				const date = new Date(year, 0, 1 + (week - 1) * 7);
				return date;
		}
	}

	private findFrequentErrorPairs(analytics: ErrorAnalyticsType[]): {
		error1: ErrorCodesEnum;
		error2: ErrorCodesEnum;
		correlation: number;
	}[]
	{
		// This is a simplified implementation
		// In a real system, you'd analyze temporal correlations
		const pairs: Map<string, number> = new Map();

		for (let i = 0; i < analytics.length; i++)
		{
			for (let j = i + 1; j < analytics.length; j++)
			{
				const error1 = analytics[i];
				const error2 = analytics[j];

				// Check if errors have overlapping affected users
				const commonUsers = error1.affectedUsers.filter(u => error2.affectedUsers.includes(u));
				if (commonUsers.length > 0)
				{
					const pairKey = `${error1.code}_${error2.code}`;
					pairs.set(pairKey, (pairs.get(pairKey) || 0) + commonUsers.length);
				}
			}
		}

		return Array.from(pairs.entries())
			.map(([key, correlation]) =>
			{
				const [error1, error2] = key.split('_') as [ErrorCodesEnum, ErrorCodesEnum];
				return { error1, error2, correlation };
			})
			.sort((a, b) => b.correlation - a.correlation)
			.slice(0, 10);
	}

	private findUserErrorPatterns(analytics: ErrorAnalyticsType[]): {
		userId: string;
		errorCodes: ErrorCodesEnum[];
		frequency: number;
	}[]
	{
		const userPatterns: Map<string, { errorCodes: Set<ErrorCodesEnum>; frequency: number }> = new Map();

		for (const error of analytics)
		{
			for (const userId of error.affectedUsers)
			{
				if (!userPatterns.has(userId))
				{
					userPatterns.set(userId, { errorCodes: new Set(), frequency: 0 });
				}

				const pattern = userPatterns.get(userId)!;
				pattern.errorCodes.add(error.code);
				pattern.frequency += error.frequency;
			}
		}

		return Array.from(userPatterns.entries())
			.map(([userId, pattern]) => ({
				userId,
				errorCodes: Array.from(pattern.errorCodes),
				frequency: pattern.frequency,
			}))
			.sort((a, b) => b.frequency - a.frequency)
			.slice(0, 20);
	}

	private findTimePatterns(analytics: ErrorAnalyticsType[]): {
		hour: number;
		errorCount: number;
		commonErrors: ErrorCodesEnum[];
	}[]
	{
		const hourlyPatterns: Map<number, { count: number; errors: Map<ErrorCodesEnum, number> }> = new Map();

		for (const error of analytics)
		{
			const hour = error.lastOccurrence.getHours();

			if (!hourlyPatterns.has(hour))
			{
				hourlyPatterns.set(hour, { count: 0, errors: new Map() });
			}

			const pattern = hourlyPatterns.get(hour)!;
			pattern.count += error.frequency;
			pattern.errors.set(error.code, (pattern.errors.get(error.code) || 0) + error.frequency);
		}

		return Array.from(hourlyPatterns.entries())
			.map(([hour, pattern]) => ({
				hour,
				errorCount: pattern.count,
				commonErrors: Array.from(pattern.errors.entries())
					.sort((a, b) => b[1] - a[1])
					.slice(0, 3)
					.map(([code]) => code),
			}))
			.sort((a, b) => b.errorCount - a.errorCount);
	}

	private estimateTotalRequests(): number
	{
		// This would need to be implemented based on actual request tracking
		// For now, return a placeholder value
		return 10000;
	}

	private calculateSLAViolations(analytics: ErrorAnalyticsType[]): number
	{
		// Count critical and high severity errors as SLA violations
		return analytics
			.filter(a => a.severity === ErrorSeverityEnum.CRITICAL || a.severity === ErrorSeverityEnum.HIGH)
			.reduce((sum, a) => sum + a.frequency, 0);
	}

	private calculatePerformanceImpact(analytics: ErrorAnalyticsType[]): ErrorRateMetricsType['performanceImpact']
	{
		// This would need actual performance data
		// For now, return placeholder data
		return {
			averageResponseTime: 250,
			slowestEndpoints: [
				{ endpoint: '/api/domains', averageTime: 500, errorRate: 2.5 },
				{ endpoint: '/api/crawl/jobs', averageTime: 350, errorRate: 1.8 },
				{ endpoint: '/api/analytics', averageTime: 300, errorRate: 1.2 },
			],
		};
	}

	private generateRecommendations(error: ErrorAnalyticsType): string[]
	{
		const recommendations: string[] = [];

		// Generate recommendations based on error code and patterns
		switch (error.code)
		{
			case ErrorCodesEnum.DATABASE_CONNECTION_FAILED:
				recommendations.push('Implement connection pooling');
				recommendations.push('Add database health checks');
				recommendations.push('Configure connection retry logic');
				break;

			case ErrorCodesEnum.SERVICE_TIMEOUT:
				recommendations.push('Increase timeout values');
				recommendations.push('Implement circuit breaker pattern');
				recommendations.push('Add request queuing');
				break;

			case ErrorCodesEnum.AUTHENTICATION_FAILED:
				recommendations.push('Implement rate limiting for login attempts');
				recommendations.push('Add CAPTCHA for repeated failures');
				recommendations.push('Improve password validation feedback');
				break;

			default:
				recommendations.push('Monitor error frequency');
				recommendations.push('Implement proper error handling');
				recommendations.push('Add user-friendly error messages');
		}

		// Add frequency-based recommendations
		if (error.frequency > 100)
		{
			recommendations.push('High frequency error - prioritize immediate fix');
		}

		if (error.affectedUsers.length > 10)
		{
			recommendations.push('Multiple users affected - implement broadcast notification');
		}

		return recommendations;
	}

	private calculateRecommendationPriority(error: ErrorAnalyticsType): 'high' | 'medium' | 'low'
	{
		if (error.severity === ErrorSeverityEnum.CRITICAL || error.frequency > 50)
		{
			return 'high';
		}

		if (error.severity === ErrorSeverityEnum.HIGH || error.frequency > 10)
		{
			return 'medium';
		}

		return 'low';
	}

	private startMetricsCollection(): void
	{
		// Collect metrics every 5 minutes
		setInterval(() =>
		{
			const metrics = this.getCurrentErrorRateMetrics();
			this.errorRateMetrics.push(metrics);

			// Keep only last 24 hours of metrics
			const cutoff = new Date(Date.now() - 24 * 60 * 60 * 1000);
			this.errorRateMetrics = this.errorRateMetrics.filter(m => new Date(m.timeWindow) >= cutoff);

			this.logger.debug('Error rate metrics collected', {
				totalErrors: metrics.totalErrors,
				errorRate: metrics.errorRate,
			});
		}, 5 * 60 * 1000);
	}
}

export default ErrorAnalyticsService;
