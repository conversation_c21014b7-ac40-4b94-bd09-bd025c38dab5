import {
	BaseError<PERSON>and<PERSON>,
	type ErrorHandlingContextType,
	type ErrorClassificationResultType,
	type LoggerInstanceType,
	IdGenerator
} from '@shared';

/**
 * Admin service error handler that extends the shared BaseErrorHandler
 * Adds admin-specific error handling features like UI error formatting
 */
export class AdminErrorHandler extends BaseErrorHandler
{
	constructor(logger: LoggerInstanceType, config?: any)
	{
		super(logger, {
			classification: {
				enabled: true,
				confidenceThreshold: 0.7,
			},
			retry: {
				enabled: true,
				defaultStrategy: {
					maxAttempts: 2, // Fewer retries for UI operations
					baseDelay: 500,
					maxDelay: 5000,
					backoffMultiplier: 2,
					jitterEnabled: true,
				},
			},
			circuitBreaker: {
				enabled: true,
				defaultConfig: {
					failureThreshold: 3, // Lower threshold for UI
					timeout: 30000,
					volumeThreshold: 5,
				},
			},
			...config,
		});
	}

	protected async reportError(
		error: Error,
		classification: ErrorClassificationResultType,
		context: ErrorHandlingContextType
	): Promise<string | undefined>
	{
		// Admin-specific error reporting
		const errorId = IdGenerator.adminId();

		// Log error details
		this.logger.error({
			errorId,
			error: error.message,
			stack: error.stack,
			classification,
			context,
			environment: process.env.NODE_ENV,
		}, 'Admin Error Handler');

		// Send to monitoring service
		this.logger.error({
			errorId,
			error: error.message,
			classification,
			context,
		}, 'Admin service error');

		return errorId;
	}

	protected async handleServiceSpecificError(
		error: Error,
		classification: ErrorClassificationResultType,
		context: ErrorHandlingContextType
	): Promise<{ actions: string[]; recoveryAttempted?: boolean; degradationTriggered?: boolean } | null>
	{
		const actions: string[] = [];

		// Handle UI-specific errors
		if (context.metadata?.isUIError)
		{
			actions.push('UI error handled');

			// Format error for user display
			const userMessage = this.formatUserError(error, classification);
			context.metadata.userMessage = userMessage;
		}

		// Handle API errors
		if (context.metadata?.isAPIError)
		{
			actions.push('API error handled');

			// Check if we should fallback to cached data
			if (classification.classification.severity !== 'critical')
			{
				context.metadata.useCachedData = true;
				actions.push('Falling back to cached data');
			}
		}

		return ({
			actions,
			recoveryAttempted: false,
			degradationTriggered: false,
		});
	}

	private formatUserError(error: Error, classification: ErrorClassificationResultType): string
	{
		// Format error message for user display based on classification
		switch (classification.classification.category)
		{
			case 'network':
				return 'Network connection issue. Please check your connection and try again.';
			case 'authorization':
				return 'You are not authorized to perform this action.';
			case 'validation':
				return 'Please check your input and try again.';
			case 'database':
				return 'We are experiencing technical difficulties. Please try again later.';
			default:
				return 'An unexpected error occurred. Please try again.';
		}
	}

	protected onShutdown(): void
	{
		this.logger.info('Admin error handler shutdown completed');
	}

	/**
	 * Get user-friendly error message
	 */
	getUserErrorMessage(error: Error): string
	{
		const classification = this.classifyError(error, { operationName: 'unknown', metadata: {} });
		return this.formatUserError(error, classification);
	}

	/**
	 * Handle Next.js API route errors
	 */
	async handleAPIRouteError(
		error: Error,
		req: any,
		res: any
	): Promise<void>
	{
		const context: ErrorHandlingContextType =
		{
			operationName: `${req.method} ${req.url}`,
			requestId: req.headers['x-request-id'] || this.generateOperationId(),
			metadata: {
				isAPIError: true,
				method: req.method,
				url: req.url,
				headers: req.headers,
			},
		};

		const result = await this.handleError(error, context);

		// Send appropriate response
		const statusCode = this.getStatusCodeFromClassification(result.classification);
		const userMessage = this.formatUserError(error, result.classification);

		res.status(statusCode).json({
			error: userMessage,
			errorId: result.reportId,
			timestamp: new Date().toISOString(),
		});
	}

	private getStatusCodeFromClassification(classification: ErrorClassificationResultType): number
	{
		switch (classification.classification.category)
		{
			case 'validation':
				return 400;
			case 'authorization':
				return 403;
			case 'network':
			case 'external_service':
				return 502;
			case 'database':
			case 'system':
				return 503;
			default:
				return 500;
		}
	}
}

export default AdminErrorHandler;
