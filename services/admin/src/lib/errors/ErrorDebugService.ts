import { v4 as uuidv4 } from 'uuid';

import { Logger } from '../utils/Logger';

import ErrorCodesEnum from './ErrorCodes';

import type {
	ErrorDebugInfoType,
	AdminPanelErrorType,
	ErrorDocumentationType,
} from './types';

/**
 * Service for error debugging, documentation, and troubleshooting assistance
 */
class ErrorDebugService
{
	private static instance: ErrorDebugService;

	private logger: Logger;

	private debugInfo: Map<string, ErrorDebugInfoType> = new Map();

	private errorDocumentation: Map<ErrorCodesEnum, ErrorDocumentationType> = new Map();

	private constructor()
	{
		this.logger = new Logger('ErrorDebugService');
		this.initializeErrorDocumentation();
	}

	public static getInstance(): ErrorDebugService
	{
		if (!ErrorDebugService.instance)
		{
			ErrorDebugService.instance = new ErrorDebugService();
		}
		return ErrorDebugService.instance;
	}

	/**
   * Capture comprehensive debug information for an error
   */
	public async captureDebugInfo(
		error: AdminPanelErrorType,
		additionalContext?: Record<string, any>,
	): Promise<ErrorDebugInfoType>
	{
		const debugInfo: ErrorDebugInfoType = {
			errorId: error.context.correlationId || uuidv4(),
			reproductionSteps: await this.generateReproductionSteps(error),
			environmentInfo: await this.captureEnvironmentInfo(),
			requestInfo: this.extractRequestInfo(error),
			databaseState: await this.captureDatabaseState(),
			serviceStates: await this.captureServiceStates(),
		};

		// Add additional context if provided
		if (additionalContext)
		{
			debugInfo.environmentInfo = {
				...debugInfo.environmentInfo,
				additionalContext,
			};
		}

		this.debugInfo.set(debugInfo.errorId, debugInfo);

		this.logger.info('Debug information captured', {
			errorId: debugInfo.errorId,
			errorCode: error.code,
			hasReproductionSteps: debugInfo.reproductionSteps ? debugInfo.reproductionSteps.length > 0 : false,
			hasRequestInfo: !!debugInfo.requestInfo,
		});

		return debugInfo;
	}

	/**
   * Get debug information for a specific error
   */
	public getDebugInfo(errorId: string): ErrorDebugInfoType | null
	{
		return this.debugInfo.get(errorId) || null;
	}

	/**
   * Generate reproduction steps for an error
   */
	private async generateReproductionSteps(error: AdminPanelErrorType): Promise<string[]>
	{
		const steps: string[] = [];

		// Add basic reproduction steps based on context
		if (error.context.url)
		{
			steps.push(`1. Navigate to: ${error.context.url}`);
		}

		if (error.context.method && error.context.method !== 'GET')
		{
			steps.push(`2. Perform ${error.context.method} request`);
		}

		// Add error-specific reproduction steps
		switch (error.code)
		{
			case ErrorCodesEnum.AUTHENTICATION_FAILED:
				steps.push('3. Enter invalid credentials');
				steps.push('4. Click login button');
				break;

			case ErrorCodesEnum.DATABASE_CONNECTION_FAILED:
				steps.push('3. Attempt database operation');
				steps.push('4. Check database connectivity');
				break;

			case ErrorCodesEnum.SERVICE_TIMEOUT:
				steps.push('3. Wait for service response');
				steps.push('4. Observe timeout behavior');
				break;

			case ErrorCodesEnum.VALIDATION_ERROR:
				steps.push('3. Submit form with invalid data');
				steps.push('4. Observe validation failure');
				break;

			default:
				steps.push('3. Perform the action that triggered the error');
				steps.push('4. Observe error behavior');
		}

		// Add environment-specific steps
		if (error.context.userAgent)
		{
			steps.push(`5. Use browser: ${this.extractBrowserInfo(error.context.userAgent)}`);
		}

		steps.push('6. Check browser console for additional errors');
		steps.push('7. Check network tab for failed requests');

		return steps;
	}

	/**
   * Capture current environment information
   */
	private async captureEnvironmentInfo(): Promise<ErrorDebugInfoType['environmentInfo']>
	{
		const memoryUsage = process.memoryUsage();
		const cpuUsage = process.cpuUsage();

		return {
			nodeVersion: process.version,
			platform: process.platform,
			memory: {
				used: memoryUsage.heapUsed,
				total: memoryUsage.heapTotal,
			},
			cpu: {
				usage: (cpuUsage.user + cpuUsage.system) / 1000000, // Convert to seconds
				cores: require('os').cpus().length,
			},
		};
	}

	/**
   * Extract request information from error context
   */
	private extractRequestInfo(error: AdminPanelErrorType): ErrorDebugInfoType['requestInfo']
	{
		if (!error.context.additionalData)
		{
			return undefined;
		}

		const additionalData = error.context.additionalData;

		return {
			headers: additionalData.headers || {},
			body: additionalData.body,
			query: additionalData.query || {},
			params: additionalData.params || {},
		};
	}

	/**
   * Capture current database state
   */
	private async captureDatabaseState(): Promise<ErrorDebugInfoType['databaseState']>
	{
		try
		{
			// This would integrate with your actual database monitoring
			// For now, return mock data
			return {
				connections: 10,
				activeQueries: 3,
				slowQueries: [
					{
						query: 'SELECT * FROM domains WHERE ranking > 1000',
						duration: 2500,
						timestamp: new Date(),
					},
				],
			};
		}
		catch (dbError)
		{
			this.logger.warn('Failed to capture database state', { error: dbError });
			return undefined;
		}
	}

	/**
   * Capture current service states
   */
	private async captureServiceStates(): Promise<ErrorDebugInfoType['serviceStates']>
	{
		const services = [
			'crawler',
			'scheduler',
			'domain-seeder',
			'ranking-engine',
			'web-app',
		];

		const serviceStates = await Promise.allSettled(
			services.map(async (serviceName) =>
			{
				try
				{
					// This would make actual health check requests
					// For now, return mock data
					return {
						serviceName,
						status: 'healthy',
						responseTime: Math.floor(Math.random() * 200) + 50,
						lastHealthCheck: new Date(),
					};
				}
				catch (serviceError)
				{
					return {
						serviceName,
						status: 'unhealthy',
						responseTime: 0,
						lastHealthCheck: new Date(),
					};
				}
			}),
		);

		return serviceStates
			.filter((result): result is PromiseFulfilledResult<any> => result.status === 'fulfilled')
			.map(result => result.value);
	}

	/**
   * Get error documentation
   */
	public getErrorDocumentation(errorCode: ErrorCodesEnum): ErrorDocumentationType | null
	{
		return this.errorDocumentation.get(errorCode) || null;
	}

	/**
   * Search error documentation
   */
	public searchDocumentation(query: string): ErrorDocumentationType[]
	{
		const lowerQuery = query.toLowerCase();
		const results: ErrorDocumentationType[] = [];

		for (const doc of this.errorDocumentation.values())
		{
			const searchableText = [
				doc.title,
				doc.description,
				...doc.commonCauses,
				...doc.troubleshootingSteps,
				...doc.resolutionProcedures,
				...doc.tags,
			].join(' ').toLowerCase();

			if (searchableText.includes(lowerQuery))
			{
				results.push(doc);
			}
		}

		return results;
	}

	/**
   * Generate troubleshooting guide for an error
   */
	public generateTroubleshootingGuide(error: AdminPanelErrorType): {
		steps: string[];
		commonSolutions: string[];
		escalationPath: string[];
		relatedErrors: ErrorCodesEnum[];
	}
	{
		const documentation = this.getErrorDocumentation(error.code);

		const guide = {
			steps: documentation?.troubleshootingSteps || this.getDefaultTroubleshootingSteps(error),
			commonSolutions: documentation?.resolutionProcedures || this.getDefaultSolutions(error),
			escalationPath: this.getEscalationPath(error),
			relatedErrors: documentation?.relatedErrors || [],
		};

		return guide;
	}

	/**
   * Analyze error patterns and suggest fixes
   */
	public analyzeErrorPattern(errors: AdminPanelErrorType[]): {
		pattern: string;
		rootCause: string;
		suggestedFix: string;
		preventionMeasures: string[];
	}
	{
		// Group errors by code
		const errorGroups = new Map<ErrorCodesEnum, AdminPanelErrorType[]>();

		for (const error of errors)
		{
			if (!errorGroups.has(error.code))
			{
				errorGroups.set(error.code, []);
			}
			errorGroups.get(error.code)!.push(error);
		}

		// Find the most frequent error
		let mostFrequentError: ErrorCodesEnum | null = null;
		let maxCount = 0;

		for (const [code, errorList] of errorGroups.entries())
		{
			if (errorList.length > maxCount)
			{
				maxCount = errorList.length;
				mostFrequentError = code;
			}
		}

		if (!mostFrequentError)
		{
			return {
				pattern: 'No clear pattern detected',
				rootCause: 'Insufficient data',
				suggestedFix: 'Monitor for more occurrences',
				preventionMeasures: ['Implement comprehensive logging'],
			};
		}

		// Analyze the pattern
		const errorList = errorGroups.get(mostFrequentError)!;
		const timePattern = this.analyzeTimePattern(errorList);
		const userPattern = this.analyzeUserPattern(errorList);

		return {
			pattern: `${mostFrequentError} occurring ${maxCount} times. ${timePattern} ${userPattern}`,
			rootCause: this.identifyRootCause(mostFrequentError, errorList),
			suggestedFix: this.suggestFix(mostFrequentError, errorList),
			preventionMeasures: this.getPreventionMeasures(mostFrequentError),
		};
	}

	/**
   * Export debug information
   */
	public exportDebugInfo(errorId: string, format: 'json' | 'text'): string | null
	{
		const debugInfo = this.getDebugInfo(errorId);
		if (!debugInfo)
		{
			return null;
		}

		if (format === 'json')
		{
			return JSON.stringify(debugInfo, null, 2);
		}

		// Text format
		const lines = [
			'Error Debug Information',
			'========================',
      `Error ID: ${debugInfo.errorId}`,
      '',
      'Environment Information:',
      `- Node Version: ${debugInfo.environmentInfo.nodeVersion}`,
      `- Platform: ${debugInfo.environmentInfo.platform}`,
      `- Memory Used: ${Math.round(debugInfo.environmentInfo.memory.used / 1024 / 1024)}MB`,
      `- Memory Total: ${Math.round(debugInfo.environmentInfo.memory.total / 1024 / 1024)}MB`,
      `- CPU Cores: ${debugInfo.environmentInfo.cpu.cores}`,
      '',
		];

		if (debugInfo.reproductionSteps && debugInfo.reproductionSteps.length > 0)
		{
			lines.push('Reproduction Steps:');
			debugInfo.reproductionSteps.forEach((step) =>
			{
				lines.push(`- ${step}`);
			});
			lines.push('');
		}

		if (debugInfo.requestInfo)
		{
			lines.push('Request Information:');
			lines.push(`- Headers: ${JSON.stringify(debugInfo.requestInfo.headers, null, 2)}`);
			if (debugInfo.requestInfo.body)
			{
				lines.push(`- Body: ${JSON.stringify(debugInfo.requestInfo.body, null, 2)}`);
			}
			lines.push('');
		}

		if (debugInfo.databaseState)
		{
			lines.push('Database State:');
			lines.push(`- Connections: ${debugInfo.databaseState.connections}`);
			lines.push(`- Active Queries: ${debugInfo.databaseState.activeQueries}`);
			lines.push(`- Slow Queries: ${debugInfo.databaseState.slowQueries.length}`);
			lines.push('');
		}

		if (debugInfo.serviceStates && debugInfo.serviceStates.length > 0)
		{
			lines.push('Service States:');
			debugInfo.serviceStates.forEach((service) =>
			{
				lines.push(`- ${service.serviceName}: ${service.status} (${service.responseTime}ms)`);
			});
		}

		return lines.join('\n');
	}

	// Private helper methods

	private initializeErrorDocumentation(): void
	{
		// Initialize documentation for common errors
		const commonErrors: Array<[ErrorCodesEnum, Omit<ErrorDocumentationType, 'errorCode' | 'lastUpdated'>]> = [
			[ErrorCodesEnum.AUTHENTICATION_FAILED, {
				title: 'Authentication Failed',
				description: 'User authentication attempt failed due to invalid credentials or authentication system issues.',
				commonCauses: [
					'Invalid username or password',
					'Account locked or disabled',
					'Authentication service unavailable',
					'Session expired',
					'Network connectivity issues',
				],
				troubleshootingSteps: [
					'Verify username and password are correct',
					'Check if account is locked or disabled',
					'Verify authentication service is running',
					'Check network connectivity',
					'Clear browser cache and cookies',
					'Try different browser or incognito mode',
				],
				resolutionProcedures: [
					'Reset password if forgotten',
					'Unlock account if locked',
					'Restart authentication service if down',
					'Check and fix network issues',
					'Update authentication configuration',
				],
				preventionMeasures: [
					'Implement proper password policies',
					'Add account lockout protection',
					'Monitor authentication service health',
					'Implement proper error handling',
					'Add logging for authentication attempts',
				],
				relatedErrors: [
					ErrorCodesEnum.INSUFFICIENT_PERMISSIONS,
					ErrorCodesEnum.SESSION_EXPIRED,
					ErrorCodesEnum.ACCOUNT_LOCKED,
				],
				tags: ['authentication', 'login', 'credentials', 'security'],
			}],

			[ErrorCodesEnum.DATABASE_CONNECTION_FAILED, {
				title: 'Database Connection Failed',
				description: 'Unable to establish connection to the database server.',
				commonCauses: [
					'Database server is down',
					'Network connectivity issues',
					'Invalid connection credentials',
					'Connection pool exhausted',
					'Firewall blocking connection',
					'Database server overloaded',
				],
				troubleshootingSteps: [
					'Check database server status',
					'Verify network connectivity',
					'Test database credentials',
					'Check connection pool configuration',
					'Verify firewall rules',
					'Monitor database server resources',
				],
				resolutionProcedures: [
					'Restart database server if down',
					'Fix network connectivity issues',
					'Update database credentials',
					'Increase connection pool size',
					'Update firewall rules',
					'Scale database resources',
				],
				preventionMeasures: [
					'Implement database health monitoring',
					'Set up connection pooling',
					'Configure proper timeouts',
					'Implement retry logic',
					'Monitor database performance',
				],
				relatedErrors: [
					ErrorCodesEnum.DATABASE_QUERY_FAILED,
					ErrorCodesEnum.DATABASE_TIMEOUT,
					ErrorCodesEnum.SERVICE_UNAVAILABLE,
				],
				tags: ['database', 'connection', 'network', 'infrastructure'],
			}],

			[ErrorCodesEnum.SERVICE_TIMEOUT, {
				title: 'Service Timeout',
				description: 'Service request timed out before completion.',
				commonCauses: [
					'Service is overloaded',
					'Network latency issues',
					'Long-running operations',
					'Resource contention',
					'Inefficient code or queries',
				],
				troubleshootingSteps: [
					'Check service response times',
					'Monitor service resource usage',
					'Analyze network latency',
					'Review recent code changes',
					'Check for resource contention',
				],
				resolutionProcedures: [
					'Increase timeout values',
					'Optimize service performance',
					'Scale service resources',
					'Implement caching',
					'Optimize database queries',
				],
				preventionMeasures: [
					'Implement proper timeout configuration',
					'Add performance monitoring',
					'Implement circuit breaker pattern',
					'Use asynchronous processing',
					'Regular performance testing',
				],
				relatedErrors: [
					ErrorCodesEnum.SERVICE_UNAVAILABLE,
					ErrorCodesEnum.SERVICE_OVERLOADED,
					ErrorCodesEnum.DATABASE_TIMEOUT,
				],
				tags: ['timeout', 'performance', 'service', 'latency'],
			}],
		];

		for (const [errorCode, docData] of commonErrors)
		{
			const documentation: ErrorDocumentationType = {
				errorCode,
				lastUpdated: new Date(),
				...docData,
			};

			this.errorDocumentation.set(errorCode, documentation);
		}
	}

	private extractBrowserInfo(userAgent: string): string
	{
		if (userAgent.includes('Chrome')) return 'Chrome';
		if (userAgent.includes('Firefox')) return 'Firefox';
		if (userAgent.includes('Safari')) return 'Safari';
		if (userAgent.includes('Edge')) return 'Edge';
		return 'Unknown';
	}

	private getDefaultTroubleshootingSteps(error: AdminPanelErrorType): string[]
	{
		return [
			'Check the error message and code',
			'Review recent changes or deployments',
			'Check system logs for related errors',
			'Verify service dependencies are running',
			'Test with different user accounts or data',
			'Check network connectivity',
			'Review system resource usage',
			'Contact support if issue persists',
		];
	}

	private getDefaultSolutions(error: AdminPanelErrorType): string[]
	{
		return [
			'Restart the affected service',
			'Clear cache and temporary files',
			'Check and update configuration',
			'Verify database connectivity',
			'Review and fix recent code changes',
			'Scale system resources if needed',
			'Contact technical support',
		];
	}

	private getEscalationPath(error: AdminPanelErrorType): string[]
	{
		const path = ['Level 1: Self-service troubleshooting'];

		if (error.severity === 'high' || error.severity === 'critical')
		{
			path.push('Level 2: Contact technical support immediately');
			path.push('Level 3: Escalate to senior engineers');
		}
		else
		{
			path.push('Level 2: Contact technical support');
			path.push('Level 3: Escalate if unresolved within 24 hours');
		}

		if (error.severity === 'critical')
		{
			path.push('Level 4: Emergency escalation to management');
		}

		return path;
	}

	private analyzeTimePattern(errors: AdminPanelErrorType[]): string
	{
		if (errors.length < 2) return '';

		const timestamps = errors.map(e => e.context.timestamp?.getTime() || 0);
		const intervals = [];

		for (let i = 1; i < timestamps.length; i++)
		{
			intervals.push(timestamps[i] - timestamps[i - 1]);
		}

		const avgInterval = intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length;
		const avgMinutes = Math.round(avgInterval / 1000 / 60);

		if (avgMinutes < 1)
		{
			return 'Errors occurring in rapid succession.';
		} if (avgMinutes < 60)
		{
			return `Errors occurring approximately every ${avgMinutes} minutes.`;
		}
		return `Errors occurring approximately every ${Math.round(avgMinutes / 60)} hours.`;
	}

	private analyzeUserPattern(errors: AdminPanelErrorType[]): string
	{
		const userIds = errors
			.map(e => e.context.userId)
			.filter(Boolean) as string[];

		const uniqueUsers = new Set(userIds);

		if (uniqueUsers.size === 0)
		{
			return 'No user information available.';
		} if (uniqueUsers.size === 1)
		{
			return 'All errors from single user.';
		}
		return `Errors affecting ${uniqueUsers.size} different users.`;
	}

	private identifyRootCause(errorCode: ErrorCodesEnum, errors: AdminPanelErrorType[]): string
	{
		// This is a simplified root cause analysis
		// In a real system, this would be more sophisticated

		const documentation = this.getErrorDocumentation(errorCode);
		if (documentation && documentation.commonCauses.length > 0)
		{
			return `Most likely: ${documentation.commonCauses[0]}`;
		}

		return 'Root cause analysis requires manual investigation';
	}

	private suggestFix(errorCode: ErrorCodesEnum, errors: AdminPanelErrorType[]): string
	{
		const documentation = this.getErrorDocumentation(errorCode);
		if (documentation && documentation.resolutionProcedures.length > 0)
		{
			return documentation.resolutionProcedures[0];
		}

		return 'Manual investigation and fix required';
	}

	private getPreventionMeasures(errorCode: ErrorCodesEnum): string[]
	{
		const documentation = this.getErrorDocumentation(errorCode);
		if (documentation)
		{
			return documentation.preventionMeasures;
		}

		return [
			'Implement proper error handling',
			'Add comprehensive logging',
			'Monitor system health',
			'Regular testing and validation',
		];
	}
}

export default ErrorDebugService;
