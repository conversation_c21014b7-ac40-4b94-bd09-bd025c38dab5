import { createIsomorphicLogger, type IsomorphicLogger } from '@shared/client';

// Create isomorphic loggers that work in both server and client
export const logger: IsomorphicLogger = createIsomorphicLogger('admin');
export const adminLogger: IsomorphicLogger = createIsomorphicLogger('admin-service');
export const apiLogger: IsomorphicLogger = createIsomorphicLogger('admin-api');
export const dbLogger: IsomorphicLogger = createIsomorphicLogger('admin-db');
export const alertLogger: IsomorphicLogger = createIsomorphicLogger('admin-alerts');
export const realtimeLogger: IsomorphicLogger = createIsomorphicLogger('admin-realtime');
export const authLogger: IsomorphicLogger = createIsomorphicLogger('admin-auth');

// Helper function for client-side logging with additional features
export const clientLogger = {
	info: (message: string, data?: unknown) => {
		if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
			console.info(`[Admin] ${message}`, data || '');
		}
	},
	warn: (message: string, data?: unknown) => {
		if (typeof window !== 'undefined') {
			console.warn(`[Admin] ${message}`, data || '');
		}
	},
	error: (message: string, data?: unknown) => {
		if (typeof window !== 'undefined') {
			console.error(`[Admin] ${message}`, data || '');
		}
	},
	debug: (message: string, data?: unknown) => {
		if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
			console.debug(`[Admin] ${message}`, data || '');
		}
	}
};

export default adminLogger;