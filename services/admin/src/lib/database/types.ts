import type { types as CassandraTypes } from 'cassandra-driver';
import type { RowDataPacket, ResultSetHeader } from 'mysql2/promise';
import type {
	ScyllaClient as ScyllaClientType,
	MariaClient as MariaClientType,
	RedisClientWrapper as RedisClientWrapperType,
} from '@shared';

// Database connection status types
type DatabaseConnectionStatus =
{
	name: string;
	connected: boolean;
	responseTime: number;
	lastCheck: Date;
	error?: string;
	connectionPool?:
	{
		active: number;
		idle: number;
		total: number;
		waiting?: number;
	};
	metrics?:
	{
		queries: number;
		errors: number;
		slowQueries: number;
		averageResponseTime: number;
	};
};

// Database health check result
type DatabaseHealthResult =
{
	healthy: boolean;
	databases:
	{
		scylla: DatabaseConnectionStatus;
		mariadb: DatabaseConnectionStatus;
		redis: DatabaseConnectionStatus;
		manticore: DatabaseConnectionStatus;
	};
	overallStatus: 'healthy' | 'degraded' | 'unhealthy';
	timestamp: Date;
};

// Query execution metrics
type QueryMetrics =
{
	queryId: string;
	database: 'scylla' | 'mariadb' | 'redis' | 'manticore';
	query: string;
	executionTime: number;
	success: boolean;
	error?: string;
	timestamp: Date;
	rowsAffected?: number;
	resultSize?: number;
};

// Database performance metrics
type DatabasePerformanceMetrics =
{
	database: string;
	totalQueries: number;
	successfulQueries: number;
	failedQueries: number;
	averageResponseTime: number;
	slowQueries: number;
	connectionPoolUtilization: number;
	lastUpdated: Date;
};

// Domain analysis data structure for admin operations
type AdminDomainAnalysis =
{
	domain: string;
	globalRank: number | null;
	categoryRank: number | null;
	category: string;
	overallScore: number;
	crawlStatus: 'pending' | 'in_progress' | 'completed' | 'failed';
	lastCrawled: Date | null;
	isPublic: boolean;
	seederStatus: 'queued' | 'processing' | 'completed' | 'failed';
	queuePosition?: number;
	performance:
	{
		loadTime: number;
		firstContentfulPaint: number;
		largestContentfulPaint: number;
		cumulativeLayoutShift: number;
		firstInputDelay: number;
		speedIndex: number;
		score: number;
	};
	security:
	{
		sslGrade: string;
		securityHeaders: Record<string, unknown>;
		vulnerabilities: string[];
		certificateInfo: Record<string, unknown>;
		score: number;
	};
	seo:
	{
		metaTags:
		{
			title: string;
			description: string;
		};
		structuredData: Record<string, unknown>[];
		sitemap: Record<string, unknown>;
		robotsTxt: Record<string, unknown>;
		score: number;
	};
	technical:
	{
		technologies: string[];
		serverInfo: Record<string, unknown>;
		httpHeaders: Record<string, unknown>;
		pageSize: number;
		resourceCount: Record<string, number>;
		score: number;
	};
	domainInfo:
	{
		age: number;
		registrationDate: Date | null;
		expirationDate: Date | null;
		registrar: string;
		dnsRecords: Record<string, unknown>;
	};
	screenshots: string[];
	subdomains: string[];
	createdAt: Date;
	updatedAt: Date;
};

// Crawl job data structure for admin operations
type AdminCrawlJob =
{
	jobId: string;
	domain: string;
	crawlType: 'basic' | 'standard' | 'full' | 'priority' | 'light' | 'visual' | 'advanced';
	priority: 'low' | 'medium' | 'high';
	status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
	progress: number;
	scheduledAt: Date;
	startedAt: Date | null;
	completedAt: Date | null;
	duration: number | null;
	errorMessage: string | null;
	retryCount: number;
	requestedBy: string;
	metadata: Record<string, unknown>;
	logs: string[];
};

// Domain seeder queue item
type SeederQueueItem =
{
	domain: string;
	priority: number;
	queuePosition: number;
	estimatedProcessingTime: number;
	source: string;
	strategy: string;
	confidence: number;
	metadata: Record<string, unknown>;
	addedAt: Date;
};

// System metrics for admin dashboard
type SystemMetrics =
{
	domains:
	{
		total: number;
		public: number;
		private: number;
		byCategory: Record<string, number>;
		byStatus: Record<string, number>;
	};
	crawling:
	{
		totalJobs: number;
		successRate: number;
		averageTime: number;
		jobsPerHour: number;
		errorRate: number;
	};
	seeding:
	{
		queueSize: number;
		processingRate: number;
		discoveryRate: number;
		contentGenerationRate: number;
	};
	performance:
	{
		averageResponseTime: number;
		uptime: number;
		errorRate: number;
		throughput: number;
	};
	timestamp: Date;
};

// Database query builder types
type QueryFilter =
{
	field: string;
	operator: '=' | '!=' | '>' | '<' | '>=' | '<=' | 'IN' | 'NOT IN' | 'LIKE' | 'NOT LIKE';
	value: unknown;
};

type QuerySort =
{
	field: string;
	direction: 'ASC' | 'DESC';
};

type QueryOptions =
{
	filters?: QueryFilter[];
	sort?: QuerySort[];
	limit?: number;
	offset?: number;
	select?: string[];
};

// Bulk operation types
type BulkOperation =
{
	operation: 'update' | 'delete' | 'insert';
	table: string;
	data: Record<string, unknown>[];
	conditions?: QueryFilter[];
};

type BulkOperationResult =
{
	success: boolean;
	affectedRows: number;
	errors: string[];
	executionTime: number;
};

// Database transaction types
type TransactionCallback<T> = (clients:
{
	scylla: ScyllaClientType;
	mariadb: MariaClientType;
	redis: RedisClientWrapperType;
}) => Promise<T>;

// Configuration validation types
type DatabaseConfigValidation =
{
	valid: boolean;
	errors: string[];
	warnings: string[];
	recommendations: string[];
};


export type {
	DatabaseConnectionStatus,
	DatabaseHealthResult,
	QueryMetrics,
	DatabasePerformanceMetrics,
	AdminDomainAnalysis,
	AdminCrawlJob,
	SeederQueueItem,
	SystemMetrics,
	QueryFilter,
	QuerySort,
	QueryOptions,
	BulkOperation,
	BulkOperationResult,
	TransactionCallback,
	DatabaseConfigValidation,
	RowDataPacket,
	ResultSetHeader,
	CassandraTypes,
};
