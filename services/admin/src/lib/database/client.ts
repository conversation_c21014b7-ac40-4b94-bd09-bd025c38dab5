import 'server-only';

/**
 * Server-side only database client for admin service.
 * This should ONLY be imported in API routes and server-side code.
 */

import type { DatabaseManager } from '@/lib/database';

type DatabaseInstance = Awaited<ReturnType<typeof DatabaseManager.getInstance>>;

declare global {
	// eslint-disable-next-line no-var
	var __admin_db: DatabaseInstance | undefined;
	// eslint-disable-next-line no-var
	var __admin_db_promise: Promise<DatabaseInstance> | undefined;
}

// Create a proxy that lazily initializes the database on first access
const db = new Proxy({} as DatabaseInstance, {
	get(_target, prop: string | symbol) {
		if (typeof window !== 'undefined') {
			throw new Error('Database cannot be accessed from client-side code');
		}

		// Return a function that will lazy-load and call the actual method
		return async (...args: unknown[]) => {
			if (!globalThis.__admin_db) {
				if (!globalThis.__admin_db_promise) {
					globalThis.__admin_db_promise = (async () => {
						const { DatabaseManager } = await import('@/lib/database');
						const instance = await DatabaseManager.getInstance();
						globalThis.__admin_db = instance;
						return instance;
					})();
				}
				await globalThis.__admin_db_promise;
			}

			const dbInstance = globalThis.__admin_db;
			const method = dbInstance[prop as keyof DatabaseInstance];
			if (typeof method === 'function') {
				return method.apply(dbInstance, args);
			}
			return method;
		};
	}
});

export default db;