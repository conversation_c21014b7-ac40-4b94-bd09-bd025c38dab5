// Database integration layer exports for admin panel

// Main database manager and clients (re-exported from shared)
export {
	DatabaseManager,
	ScyllaClient,
	MariaClient,
	ManticoreClient,
	RedisClientWrapper,
	CacheManager,
} from '@shared/server';

// Repositories (disabled for service health monitoring implementation)
// export { default as DomainRepository } from './repositories/DomainRepository';
// export { default as CrawlJobRepository } from './repositories/CrawlJobRepository';
// export { default as SystemMetricsRepository } from './repositories/SystemMetricsRepository';

// Health checking (disabled for service health monitoring implementation)
// export { default as <PERSON>Health<PERSON><PERSON><PERSON> } from './healthCheck';

// Database service
// Database service (disabled for service health monitoring implementation)
// export {
//   default as DatabaseService,
//   databaseService,
//   initializeDatabaseService,
//   getDatabaseManager,
//   getRepositories,
//   getHealthChecker,
//   closeDatabaseService,
// } from './service';

// Types
export type {
	DatabaseConnectionStatus,
	DatabaseHealthResult,
	QueryMetrics,
	DatabasePerformanceMetrics,
	AdminDomainAnalysis,
	AdminCrawlJob,
	SeederQueueItem,
	SystemMetrics,
	QueryFilter,
	QuerySort,
	QueryOptions,
	BulkOperation,
	BulkOperationResult,
	TransactionCallback,
	DatabaseConfigValidation,
	RowDataPacket,
	ResultSetHeader,
	CassandraTypes,
} from './types';

// Utility functions
// Disabled for service health monitoring implementation
// export const createRepositories = (dbManager: any) => ({
//   domains: new (require('./repositories/DomainRepository').default)(dbManager),
//   crawlJobs: new (require('./repositories/CrawlJobRepository').default)(dbManager),
//   systemMetrics: new (require('./repositories/SystemMetricsRepository').default)(dbManager),
// });

// export const createHealthChecker = (dbManager: any) => new (require('./healthCheck').default)(dbManager);
