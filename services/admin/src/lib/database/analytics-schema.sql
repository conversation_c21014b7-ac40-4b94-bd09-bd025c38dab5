-- Analytics Reports Table for ScyllaDB
CREATE TABLE IF NOT EXISTS admin_reports (
    id UUID PRIMARY KEY,
    config TEXT,
    generated_at TIMESTAMP,
    generated_by TEXT,
    status TEXT,
    file_url TEXT,
    file_name TEXT,
    file_size BIGINT,
    download_count INT,
    expires_at TIMESTAMP,
    error TEXT,
    metadata TEXT
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS admin_reports_status_idx ON admin_reports (status);
CREATE INDEX IF NOT EXISTS admin_reports_generated_at_idx ON admin_reports (generated_at);
CREATE INDEX IF NOT EXISTS admin_reports_generated_by_idx ON admin_reports (generated_by);
CREATE INDEX IF NOT EXISTS admin_reports_expires_at_idx ON admin_reports (expires_at);

-- Analytics Metrics Cache Table (for performance optimization)
CREATE TABLE IF NOT EXISTS analytics_metrics_cache (
    cache_key TEXT PRIMARY KEY,
    data TEXT,
    created_at TIMESTAMP,
    expires_at TIMESTAMP
);

-- Create index for cache expiration cleanup
CREATE INDEX IF NOT EXISTS analytics_metrics_cache_expires_at_idx ON analytics_metrics_cache (expires_at);

-- User Activity Tracking Table
CREATE TABLE IF NOT EXISTS admin_user_activity (
    id UUID PRIMARY KEY,
    user_id TEXT,
    username TEXT,
    action TEXT,
    resource TEXT,
    resource_id TEXT,
    old_values TEXT,
    new_values TEXT,
    timestamp TIMESTAMP,
    ip_address TEXT,
    user_agent TEXT,
    success BOOLEAN,
    error_message TEXT
);

-- Create indexes for user activity queries
CREATE INDEX IF NOT EXISTS admin_user_activity_user_id_idx ON admin_user_activity (user_id);
CREATE INDEX IF NOT EXISTS admin_user_activity_timestamp_idx ON admin_user_activity (timestamp);
CREATE INDEX IF NOT EXISTS admin_user_activity_action_idx ON admin_user_activity (action);
CREATE INDEX IF NOT EXISTS admin_user_activity_success_idx ON admin_user_activity (success);

-- System Metrics History Table (for trend analysis)
CREATE TABLE IF NOT EXISTS system_metrics_history (
    id UUID PRIMARY KEY,
    metric_name TEXT,
    metric_value DOUBLE,
    metric_type TEXT,
    category TEXT,
    timestamp TIMESTAMP,
    metadata TEXT
);

-- Create indexes for metrics queries
CREATE INDEX IF NOT EXISTS system_metrics_history_metric_name_idx ON system_metrics_history (metric_name);
CREATE INDEX IF NOT EXISTS system_metrics_history_timestamp_idx ON system_metrics_history (timestamp);
CREATE INDEX IF NOT EXISTS system_metrics_history_category_idx ON system_metrics_history (category);

-- Alert Rules Table
CREATE TABLE IF NOT EXISTS analytics_alert_rules (
    id UUID PRIMARY KEY,
    name TEXT,
    description TEXT,
    metric_id TEXT,
    condition_operator TEXT,
    condition_value DOUBLE,
    condition_duration INT,
    severity TEXT,
    enabled BOOLEAN,
    notifications TEXT, -- JSON array
    last_triggered TIMESTAMP,
    trigger_count INT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

-- Create indexes for alert rules
CREATE INDEX IF NOT EXISTS analytics_alert_rules_enabled_idx ON analytics_alert_rules (enabled);
CREATE INDEX IF NOT EXISTS analytics_alert_rules_metric_id_idx ON analytics_alert_rules (metric_id);
CREATE INDEX IF NOT EXISTS analytics_alert_rules_severity_idx ON analytics_alert_rules (severity);

-- Alert History Table
CREATE TABLE IF NOT EXISTS analytics_alert_history (
    id UUID PRIMARY KEY,
    rule_id UUID,
    metric_name TEXT,
    metric_value DOUBLE,
    threshold_value DOUBLE,
    severity TEXT,
    triggered_at TIMESTAMP,
    resolved_at TIMESTAMP,
    duration INT,
    acknowledged BOOLEAN,
    acknowledged_by TEXT,
    acknowledged_at TIMESTAMP,
    notes TEXT
);

-- Create indexes for alert history
CREATE INDEX IF NOT EXISTS analytics_alert_history_rule_id_idx ON analytics_alert_history (rule_id);
CREATE INDEX IF NOT EXISTS analytics_alert_history_triggered_at_idx ON analytics_alert_history (triggered_at);
CREATE INDEX IF NOT EXISTS analytics_alert_history_severity_idx ON analytics_alert_history (severity);
CREATE INDEX IF NOT EXISTS analytics_alert_history_acknowledged_idx ON analytics_alert_history (acknowledged);
