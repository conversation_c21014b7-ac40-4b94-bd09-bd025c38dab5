-- User Management Database Schema
-- This schema extends the existing admin panel database with user management tables

-- Admin Users table (extends existing)
CREATE TABLE IF NOT EXISTS admin_users (
    id VARCHAR(36) PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role ENUM('super_admin', 'admin', 'viewer') NOT NULL DEFAULT 'viewer',
    permissions JSO<PERSON>,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    is_active BOOLEAN DEFAULT TRUE,
    password_last_changed TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    must_change_password BOOLEAN DEFAULT FALSE,
    email VARCHAR(255) NULL,
    full_name <PERSON><PERSON><PERSON><PERSON>(255) NULL,
    login_attempts INT DEFAULT 0,
    locked_until TIMESTA<PERSON> NULL,
    password_history JSON, -- Array of previous password hashes
    created_by <PERSON><PERSON><PERSON><PERSON>(36) NULL,
    updated_by VA<PERSON>HA<PERSON>(36) NULL,
    deleted_at TIMESTAMP NULL,
    deleted_by VARCHAR(36) NULL,

    INDEX idx_username (username),
    INDEX idx_role (role),
    INDEX idx_is_active (is_active),
    INDEX idx_locked_until (locked_until),
    INDEX idx_last_login (last_login),
    INDEX idx_created_at (created_at),

    FOREIGN KEY (created_by) REFERENCES admin_users(id) ON DELETE SET NULL,
    FOREIGN KEY (updated_by) REFERENCES admin_users(id) ON DELETE SET NULL,
    FOREIGN KEY (deleted_by) REFERENCES admin_users(id) ON DELETE SET NULL
);

-- Admin Sessions table (extends existing)
CREATE TABLE IF NOT EXISTS admin_sessions (
    session_id VARCHAR(128) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    device_info JSON, -- Browser, OS, device type

    INDEX idx_user_id (user_id),
    INDEX idx_is_active (is_active),
    INDEX idx_expires_at (expires_at),
    INDEX idx_last_activity (last_activity),
    INDEX idx_ip_address (ip_address),

    FOREIGN KEY (user_id) REFERENCES admin_users(id) ON DELETE CASCADE
);

-- User Activity Log table
CREATE TABLE IF NOT EXISTS user_activity (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    session_id VARCHAR(128) NULL,
    action VARCHAR(100) NOT NULL,
    resource VARCHAR(100) NOT NULL,
    resource_id VARCHAR(36) NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    success BOOLEAN NOT NULL,
    details JSON, -- Additional context data

    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_resource (resource),
    INDEX idx_timestamp (timestamp),
    INDEX idx_success (success),
    INDEX idx_ip_address (ip_address),
    INDEX idx_user_timestamp (user_id, timestamp),

    FOREIGN KEY (user_id) REFERENCES admin_users(id) ON DELETE CASCADE,
    FOREIGN KEY (session_id) REFERENCES admin_sessions(session_id) ON DELETE SET NULL
);

-- Audit Logs table
CREATE TABLE IF NOT EXISTS audit_logs (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    username VARCHAR(50) NOT NULL,
    action VARCHAR(100) NOT NULL,
    resource VARCHAR(100) NOT NULL,
    resource_id VARCHAR(36) NULL,
    old_values JSON NULL, -- Previous state
    new_values JSON NULL, -- New state
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    success BOOLEAN DEFAULT TRUE,
    error_message TEXT NULL,
    severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',

    INDEX idx_user_id (user_id),
    INDEX idx_username (username),
    INDEX idx_action (action),
    INDEX idx_resource (resource),
    INDEX idx_timestamp (timestamp),
    INDEX idx_severity (severity),
    INDEX idx_success (success),
    INDEX idx_user_timestamp (user_id, timestamp),
    INDEX idx_resource_timestamp (resource, timestamp),

    FOREIGN KEY (user_id) REFERENCES admin_users(id) ON DELETE CASCADE
);

-- Security Alerts table
CREATE TABLE IF NOT EXISTS security_alerts (
    id VARCHAR(36) PRIMARY KEY,
    type ENUM('failed_login', 'account_locked', 'permission_escalation', 'suspicious_activity', 'password_breach') NOT NULL,
    user_id VARCHAR(36) NULL,
    username VARCHAR(50) NULL,
    description TEXT NOT NULL,
    severity ENUM('low', 'medium', 'high', 'critical') NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    resolved BOOLEAN DEFAULT FALSE,
    resolved_by VARCHAR(36) NULL,
    resolved_at TIMESTAMP NULL,
    metadata JSON, -- Additional alert-specific data

    INDEX idx_type (type),
    INDEX idx_user_id (user_id),
    INDEX idx_severity (severity),
    INDEX idx_timestamp (timestamp),
    INDEX idx_resolved (resolved),
    INDEX idx_type_timestamp (type, timestamp),
    INDEX idx_severity_timestamp (severity, timestamp),

    FOREIGN KEY (user_id) REFERENCES admin_users(id) ON DELETE SET NULL,
    FOREIGN KEY (resolved_by) REFERENCES admin_users(id) ON DELETE SET NULL
);

-- Password Policy Configuration table
CREATE TABLE IF NOT EXISTS password_policies (
    id INT PRIMARY KEY AUTO_INCREMENT,
    min_length INT DEFAULT 12,
    require_uppercase BOOLEAN DEFAULT TRUE,
    require_lowercase BOOLEAN DEFAULT TRUE,
    require_numbers BOOLEAN DEFAULT TRUE,
    require_special_chars BOOLEAN DEFAULT TRUE,
    max_age_days INT DEFAULT 90,
    history_count INT DEFAULT 5,
    max_login_attempts INT DEFAULT 5,
    lockout_duration_minutes INT DEFAULT 30,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

-- Insert default password policy
INSERT INTO password_policies (
    min_length, require_uppercase, require_lowercase, require_numbers,
    require_special_chars, max_age_days, history_count, max_login_attempts,
    lockout_duration_minutes, is_active
) VALUES (
    12, TRUE, TRUE, TRUE, TRUE, 90, 5, 5, 30, TRUE
) ON DUPLICATE KEY UPDATE id = id;

-- User Permission Templates table
CREATE TABLE IF NOT EXISTS permission_templates (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    permissions JSON NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHAR(36) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,

    INDEX idx_name (name),
    INDEX idx_is_active (is_active),

    FOREIGN KEY (created_by) REFERENCES admin_users(id) ON DELETE CASCADE
);

-- Insert default permission templates
INSERT INTO permission_templates (id, name, description, permissions, created_by) VALUES
(
    UUID(),
    'Super Administrator',
    'Full system access with all permissions',
    JSON_ARRAY(
        'users.view', 'users.create', 'users.edit', 'users.delete', 'users.manage_permissions',
        'services.view', 'services.restart', 'services.configure',
        'domains.view', 'domains.edit', 'domains.delete', 'domains.bulk_operations',
        'crawl.view', 'crawl.create', 'crawl.manage', 'crawl.configure',
        'seeder.view', 'seeder.manage', 'seeder.configure',
        'analytics.view', 'analytics.export',
        'config.view', 'config.edit', 'config.deploy',
        'logs.view', 'logs.export',
        'ai.view', 'ai.configure', 'ai.manage'
    ),
    (SELECT id FROM admin_users WHERE role = 'super_admin' LIMIT 1)
),
(
    UUID(),
    'Administrator',
    'Limited administrative access',
    JSON_ARRAY(
        'users.view',
        'services.view', 'services.configure',
        'domains.view', 'domains.edit', 'domains.bulk_operations',
        'crawl.view', 'crawl.create', 'crawl.manage',
        'seeder.view', 'seeder.manage',
        'analytics.view', 'analytics.export',
        'config.view', 'config.edit',
        'logs.view',
        'ai.view', 'ai.manage'
    ),
    (SELECT id FROM admin_users WHERE role = 'super_admin' LIMIT 1)
),
(
    UUID(),
    'Viewer',
    'Read-only access to system information',
    JSON_ARRAY(
        'users.view',
        'services.view',
        'domains.view',
        'crawl.view',
        'seeder.view',
        'analytics.view',
        'config.view',
        'logs.view',
        'ai.view'
    ),
    (SELECT id FROM admin_users WHERE role = 'super_admin' LIMIT 1)
) ON DUPLICATE KEY UPDATE id = id;

-- Create indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_user_activity_composite ON user_activity (user_id, timestamp, action);
CREATE INDEX IF NOT EXISTS idx_audit_logs_composite ON audit_logs (user_id, timestamp, action);
CREATE INDEX IF NOT EXISTS idx_security_alerts_composite ON security_alerts (type, severity, timestamp);
CREATE INDEX IF NOT EXISTS idx_admin_sessions_composite ON admin_sessions (user_id, is_active, last_activity);

-- Create views for common queries
CREATE OR REPLACE VIEW active_users AS
SELECT
    u.*,
    (SELECT COUNT(*) FROM admin_sessions s WHERE s.user_id = u.id AND s.is_active = 1) as active_sessions,
    (SELECT MAX(s.last_activity) FROM admin_sessions s WHERE s.user_id = u.id) as last_activity
FROM admin_users u
WHERE u.is_active = 1 AND u.deleted_at IS NULL;

CREATE OR REPLACE VIEW user_login_stats AS
SELECT
    u.id,
    u.username,
    COUNT(s.session_id) as total_sessions,
    COUNT(CASE WHEN s.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as sessions_last_30_days,
    MAX(s.created_at) as last_login,
    AVG(TIMESTAMPDIFF(MINUTE, s.created_at, s.last_activity)) as avg_session_duration
FROM admin_users u
LEFT JOIN admin_sessions s ON u.id = s.user_id
WHERE u.deleted_at IS NULL
GROUP BY u.id, u.username;

CREATE OR REPLACE VIEW security_summary AS
SELECT
    COUNT(CASE WHEN resolved = 0 THEN 1 END) as unresolved_alerts,
    COUNT(CASE WHEN resolved = 0 AND severity = 'critical' THEN 1 END) as critical_alerts,
    COUNT(CASE WHEN resolved = 0 AND severity = 'high' THEN 1 END) as high_alerts,
    COUNT(CASE WHEN type = 'failed_login' AND timestamp >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as failed_logins_24h,
    COUNT(CASE WHEN type = 'account_locked' AND resolved = 0 THEN 1 END) as locked_accounts
FROM security_alerts;
