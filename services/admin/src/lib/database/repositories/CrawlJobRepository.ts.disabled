import { v4 as uuidv4 } from 'uuid';

import { logger } from '../../../utils/logger';

import type AdminDatabaseManager from '../AdminDatabaseManager';
import type {
  AdminCrawlJob,
  QueryOptions,
  BulkOperationResult,
} from '../types';

/**
 * Repository for crawl job database operations in admin panel
 */
class CrawlJobRepository
{
  constructor(private dbManager: AdminDatabaseManager) {}

  /**
   * Get paginated list of crawl jobs with filtering
   */
  async getCrawlJobs(options: QueryOptions = {}): Promise<{
    jobs: AdminCrawlJob[];
    total: number;
    page: number;
    limit: number;
  }>
  {
    const {
 limit = 50, offset = 0, filters = [], sort = [],
} = options;
    const page = Math.floor(offset / limit) + 1;

    return this.dbManager.executeWithMetrics('scylla', 'getCrawlJobs', async () =>
    {
      const scylla = this.dbManager.getScyllaClient();

      // Build query with filters
      let query = 'SELECT * FROM domain_crawl_jobs';
      const params: (string | number | boolean | null)[] = [];

      if (filters.length > 0)
      {
        const whereConditions = this.buildWhereConditions(filters, params);
        if (whereConditions)
        {
          query += ` WHERE ${whereConditions}`;
        }
      }

      // Add ALLOW FILTERING for non-partition key filters
      if (filters.some(f => f.field !== 'job_id'))
      {
        query += ' ALLOW FILTERING';
      }

      // Add sorting
      if (sort.length > 0)
      {
        const orderBy = sort.map(s => `${s.field} ${s.direction}`).join(', ');
        query += ` ORDER BY ${orderBy}`;
      }
      else
      {
        query += ' ORDER BY scheduled_at DESC';
      }

      // Add pagination
      query += ` LIMIT ${limit}`;

      const result = await scylla.execute(query, params);
      const jobs = result.rows.map((row: any) => this.mapRowToCrawlJob(row));

      // Get total count (simplified)
      const countQuery = 'SELECT COUNT(*) as total FROM domain_crawl_jobs';
      const countResult = await scylla.execute(countQuery);
      const total = parseInt(String(countResult.first()?.total || 0), 10);

      return {
        jobs,
        total,
        page,
        limit,
      };
    });
  }

  /**
   * Get crawl job by ID
   */
  async getCrawlJobById(jobId: string): Promise<AdminCrawlJob | null>
  {
    return this.dbManager.executeWithMetrics('scylla', 'getCrawlJobById', async () =>
    {
      const scylla = this.dbManager.getScyllaClient();
      const result = await scylla.execute(
        'SELECT * FROM domain_crawl_jobs WHERE job_id = ?',
        [jobId],
      );

      if (result.rows.length === 0)
      {
        return null;
      }

      return this.mapRowToCrawlJob(result.first());
    });
  }

  /**
   * Get crawl jobs by domain
   */
  async getCrawlJobsByDomain(domain: string, limit = 20): Promise<AdminCrawlJob[]>
  {
    return this.dbManager.executeWithMetrics('scylla', 'getCrawlJobsByDomain', async () =>
    {
      const scylla = this.dbManager.getScyllaClient();
      const result = await scylla.execute(
        'SELECT * FROM domain_crawl_jobs WHERE domain = ? ORDER BY scheduled_at DESC LIMIT ? ALLOW FILTERING',
        [domain, limit],
      );

      return result.rows.map((row: any) => this.mapRowToCrawlJob(row));
    });
  }

  /**
   * Get crawl jobs by status
   */
  async getCrawlJobsByStatus(
    status: AdminCrawlJob['status'],
    limit = 100,
  ): Promise<AdminCrawlJob[]>
  {
    return this.dbManager.executeWithMetrics('scylla', 'getCrawlJobsByStatus', async () =>
    {
      const scylla = this.dbManager.getScyllaClient();
      const result = await scylla.execute(
        'SELECT * FROM domain_crawl_jobs WHERE status = ? ORDER BY scheduled_at DESC LIMIT ? ALLOW FILTERING',
        [status, limit],
      );

      return result.rows.map((row: any) => this.mapRowToCrawlJob(row));
    });
  }

  /**
   * Create new crawl job
   */
  async createCrawlJob(jobData: {
    domain: string;
    crawlType: AdminCrawlJob['crawlType'];
    priority: AdminCrawlJob['priority'];
    requestedBy: string;
    metadata?: Record<string, unknown>;
  }): Promise<AdminCrawlJob>
  {
    return this.dbManager.executeWithMetrics('scylla', 'createCrawlJob', async () =>
    {
      const scylla = this.dbManager.getScyllaClient();
      const jobId = uuidv4();
      const now = new Date();

      const job: AdminCrawlJob = {
        jobId,
        domain: jobData.domain,
        crawlType: jobData.crawlType,
        priority: jobData.priority,
        status: 'pending',
        progress: 0,
        scheduledAt: now,
        startedAt: null,
        completedAt: null,
        duration: null,
        errorMessage: null,
        retryCount: 0,
        requestedBy: jobData.requestedBy,
        metadata: jobData.metadata || {},
        logs: [],
      };

      const query = `
        INSERT INTO domain_crawl_jobs (
          job_id, domain, crawl_type, priority, status, progress,
          scheduled_at, started_at, completed_at, duration, error_message,
          retry_count, requested_by, metadata
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const params = [
        jobId,
        job.domain,
        job.crawlType,
        job.priority,
        job.status,
        job.progress,
        job.scheduledAt.toISOString(),
        job.startedAt?.toISOString() || null,
        job.completedAt?.toISOString() || null,
        job.duration,
        job.errorMessage,
        job.retryCount,
        job.requestedBy,
        JSON.stringify(job.metadata),
      ];

      await scylla.execute(query, params);

      logger.info(`Crawl job created: ${jobId} for domain: ${job.domain}`);
      return job;
    });
  }

  /**
   * Update crawl job status and progress
   */
  async updateCrawlJobStatus(
    jobId: string,
    updates: {
      status?: AdminCrawlJob['status'];
      progress?: number;
      startedAt?: Date;
      completedAt?: Date;
      errorMessage?: string;
      retryCount?: number;
    },
  ): Promise<void>
  {
    return this.dbManager.executeWithMetrics('scylla', 'updateCrawlJobStatus', async () =>
    {
      const scylla = this.dbManager.getScyllaClient();

      const fields: string[] = [];
      const params: unknown[] = [];

      if (updates.status !== undefined)
      {
        fields.push('status = ?');
        params.push(updates.status);
      }

      if (updates.progress !== undefined)
      {
        fields.push('progress = ?');
        params.push(updates.progress);
      }

      if (updates.startedAt !== undefined)
      {
        fields.push('started_at = ?');
        params.push(updates.startedAt?.toISOString() || null);
      }

      if (updates.completedAt !== undefined)
      {
        fields.push('completed_at = ?');
        params.push(updates.completedAt?.toISOString() || null);

        // Calculate duration if both started and completed times are available
        if (updates.startedAt && updates.completedAt)
        {
          const duration = updates.completedAt.getTime() - updates.startedAt.getTime();
          fields.push('duration = ?');
          params.push(duration);
        }
      }

      if (updates.errorMessage !== undefined)
      {
        fields.push('error_message = ?');
        params.push(updates.errorMessage);
      }

      if (updates.retryCount !== undefined)
      {
        fields.push('retry_count = ?');
        params.push(updates.retryCount);
      }

      if (fields.length === 0)
      {
        return;
      }

      params.push(jobId);
      const query = `UPDATE domain_crawl_jobs SET ${fields.join(', ')} WHERE job_id = ?`;

      await scylla.execute(query, params);

      logger.info(`Crawl job updated: ${jobId}`);
    });
  }

  /**
   * Cancel crawl job
   */
  async cancelCrawlJob(jobId: string): Promise<void>
  {
    return this.updateCrawlJobStatus(jobId, {
      status: 'cancelled',
      completedAt: new Date(),
    });
  }

  /**
   * Retry failed crawl job
   */
  async retryCrawlJob(jobId: string): Promise<void>
  {
    return this.dbManager.executeWithMetrics('scylla', 'retryCrawlJob', async () =>
    {
      const job = await this.getCrawlJobById(jobId);
      if (!job)
      {
        throw new Error(`Crawl job not found: ${jobId}`);
      }

      await this.updateCrawlJobStatus(jobId, {
        status: 'pending',
        progress: 0,
        startedAt: undefined,
        completedAt: undefined,
        errorMessage: undefined,
        retryCount: job.retryCount + 1,
      });

      logger.info(`Crawl job retry initiated: ${jobId}`);
    });
  }

  /**
   * Bulk cancel crawl jobs
   */
  async bulkCancelJobs(jobIds: string[]): Promise<BulkOperationResult>
  {
    const startTime = Date.now();
    const errors: string[] = [];
    let affectedRows = 0;

    try
    {
      const scylla = this.dbManager.getScyllaClient();
      const now = new Date();
      const queries = jobIds.map(jobId => ({
        query: 'UPDATE domain_crawl_jobs SET status = ?, completed_at = ? WHERE job_id = ?',
        params: ['cancelled', now, jobId],
      }));

      await scylla.batch(queries);
      affectedRows = jobIds.length;

      logger.info(`Bulk job cancellation completed: ${jobIds.length} jobs`);
    }
    catch (error)
    {
      const errorMessage = error instanceof Error ? error.message : String(error);
      errors.push(errorMessage);
      logger.error('Bulk job cancellation failed:', error as any);
    }

    return {
      success: errors.length === 0,
      affectedRows,
      errors,
      executionTime: Date.now() - startTime,
    };
  }

  /**
   * Delete old completed crawl jobs
   */
  async cleanupOldJobs(olderThanDays = 30): Promise<number>
  {
    return this.dbManager.executeWithMetrics('scylla', 'cleanupOldJobs', async () =>
    {
      const scylla = this.dbManager.getScyllaClient();
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

      // First, get the jobs to delete
      const selectResult = await scylla.execute(
        'SELECT job_id FROM domain_crawl_jobs WHERE completed_at < ? AND status IN (?, ?) ALLOW FILTERING',
        [cutoffDate.toISOString(), 'completed', 'failed'],
      );

      if (selectResult.rows.length === 0)
      {
        return 0;
      }

      // Delete the jobs
      const deleteQueries = selectResult.rows.map((row: any) => ({
        query: 'DELETE FROM domain_crawl_jobs WHERE job_id = ?',
        params: [row.job_id],
      }));

      await scylla.batch(deleteQueries);

      const deletedCount = selectResult.rows.length;
      logger.info(`Cleaned up ${deletedCount} old crawl jobs (older than ${olderThanDays} days)`);

      return deletedCount;
    });
  }

  /**
   * Get crawl job statistics
   */
  async getCrawlJobStatistics(): Promise<{
    totalJobs: number;
    byStatus: Record<string, number>;
    byType: Record<string, number>;
    byPriority: Record<string, number>;
    successRate: number;
    averageTime: number;
  }>
  {
    return this.dbManager.executeWithMetrics('scylla', 'getCrawlJobStatistics', async () =>
    {
      const scylla = this.dbManager.getScyllaClient();

      // Get total jobs
      const totalResult = await scylla.execute('SELECT COUNT(*) as total FROM domain_crawl_jobs');
      const totalJobs = parseInt(String(totalResult.first()?.total || 0), 10);

      // Get status distribution
      const statusResult = await scylla.execute(
        'SELECT status, COUNT(*) as count FROM domain_crawl_jobs GROUP BY status ALLOW FILTERING',
      );
      const byStatus: Record<string, number> = {};
      statusResult.rows.forEach((row: any) =>
      {
        byStatus[row.status] = parseInt(String(row.count), 10);
      });

      // Get type distribution
      const typeResult = await scylla.execute(
        'SELECT crawl_type, COUNT(*) as count FROM domain_crawl_jobs GROUP BY crawl_type ALLOW FILTERING',
      );
      const byType: Record<string, number> = {};
      typeResult.rows.forEach((row: any) =>
      {
        byType[row.crawl_type] = parseInt(String(row.count), 10);
      });

      // Get priority distribution
      const priorityResult = await scylla.execute(
        'SELECT priority, COUNT(*) as count FROM domain_crawl_jobs GROUP BY priority ALLOW FILTERING',
      );
      const byPriority: Record<string, number> = {};
      priorityResult.rows.forEach((row: any) =>
      {
        byPriority[row.priority] = parseInt(String(row.count), 10);
      });

      // Calculate success rate
      const completedJobs = byStatus.completed || 0;
      const failedJobs = byStatus.failed || 0;
      const successRate = completedJobs + failedJobs > 0
        ? (completedJobs / (completedJobs + failedJobs)) * 100
        : 0;

      // Calculate average completion time
      const avgTimeResult = await scylla.execute(
        'SELECT AVG(duration) as avg_time FROM domain_crawl_jobs WHERE duration IS NOT NULL ALLOW FILTERING',
      );
      const averageTime = parseFloat(String(avgTimeResult.first()?.avg_time || 0));

      return {
        totalJobs,
        byStatus,
        byType,
        byPriority,
        successRate,
        averageTime,
      };
    });
  }

  /**
   * Get recent crawl job activity
   */
  async getRecentActivity(hours = 24, limit = 100): Promise<AdminCrawlJob[]>
  {
    return this.dbManager.executeWithMetrics('scylla', 'getRecentActivity', async () =>
    {
      const scylla = this.dbManager.getScyllaClient();
      const cutoffTime = new Date();
      cutoffTime.setHours(cutoffTime.getHours() - hours);

      const result = await scylla.execute(
        'SELECT * FROM domain_crawl_jobs WHERE scheduled_at >= ? ORDER BY scheduled_at DESC LIMIT ? ALLOW FILTERING',
        [cutoffTime.toISOString(), limit],
      );

      return result.rows.map((row: any) => this.mapRowToCrawlJob(row));
    });
  }

  // Private helper methods

  private buildWhereConditions(filters: any[], params: (string | number | boolean | null)[]): string
  {
    const conditions: string[] = [];

    filters.forEach((filter) =>
    {
      switch (filter.operator)
      {
        case '=':
          conditions.push(`${filter.field} = ?`);
          params.push(filter.value);
          break;
        case '!=':
          conditions.push(`${filter.field} != ?`);
          params.push(filter.value);
          break;
        case 'IN':
          if (Array.isArray(filter.value))
          {
            const placeholders = filter.value.map(() => '?').join(',');
            conditions.push(`${filter.field} IN (${placeholders})`);
            params.push(...filter.value);
          }
          break;
        default:
          logger.warn(`Unsupported filter operator: ${filter.operator}`);
      }
    });

    return conditions.join(' AND ');
  }

  private mapRowToCrawlJob(row: any): AdminCrawlJob
  {
    return {
      jobId: row.job_id.toString(),
      domain: row.domain,
      crawlType: row.crawl_type,
      priority: row.priority,
      status: row.status,
      progress: row.progress || 0,
      scheduledAt: row.scheduled_at,
      startedAt: row.started_at,
      completedAt: row.completed_at,
      duration: row.duration,
      errorMessage: row.error_message,
      retryCount: row.retry_count || 0,
      requestedBy: row.requested_by || 'system',
      metadata: row.metadata ? JSON.parse(row.metadata) : {},
      logs: [], // Logs would be stored separately or in metadata
    };
  }
}

export default CrawlJobRepository;
