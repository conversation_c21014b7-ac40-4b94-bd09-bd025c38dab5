import { logger } from '../../../utils/logger';

import type AdminDatabaseManager from '../AdminDatabaseManager';
import type {
  AdminDomainAnalysis,
  QueryOptions,
  QueryFilter,
  BulkOperationResult,
} from '../types';

/**
 * Repository for domain-related database operations in admin panel
 */
class DomainRepository
{
  constructor(private dbManager: AdminDatabaseManager) {}

  /**
   * Get paginated list of domains with filtering and sorting
   */
  async getDomains(options: QueryOptions = {}): Promise<{
    domains: AdminDomainAnalysis[];
    total: number;
    page: number;
    limit: number;
  }>
  {
    const {
 limit = 50, offset = 0, filters = [], sort = [],
} = options;
    const page = Math.floor(offset / limit) + 1;

    return this.dbManager.executeWithMetrics('scylla', 'getDomains', async () =>
    {
      const scylla = this.dbManager.getScyllaClient();

      // Build query with filters
      let query = 'SELECT * FROM domain_analysis';
      const params: (string | number | boolean | null)[] = [];

      if (filters.length > 0)
      {
        const whereConditions = this.buildWhereConditions(filters, params);
        if (whereConditions)
        {
          query += ` WHERE ${whereConditions}`;
        }
      }

      // Add sorting
      if (sort.length > 0)
      {
        const orderBy = sort.map(s => `${s.field} ${s.direction}`).join(', ');
        query += ` ORDER BY ${orderBy}`;
      }
      else
      {
        query += ' ORDER BY global_rank ASC';
      }

      // Add pagination
      query += ` LIMIT ${limit}`;
      if (offset > 0)
      {
        query += ` OFFSET ${offset}`;
      }

      const result = await scylla.execute(query, params);
      const domains = result.rows.map((row: any) => this.mapRowToDomainAnalysis(row));

      // Get total count (simplified - in production, use a separate count query)
      const countQuery = 'SELECT COUNT(*) as total FROM domain_analysis';
      const countResult = await scylla.execute(countQuery);
      const total = countResult.first()?.total || 0;

      return {
        domains,
        total: parseInt(String(total), 10),
        page,
        limit,
      };
    });
  }

  /**
   * Get domain by name with full analysis data
   */
  async getDomainByName(domain: string): Promise<AdminDomainAnalysis | null>
  {
    return this.dbManager.executeWithMetrics('scylla', 'getDomainByName', async () =>
    {
      const scylla = this.dbManager.getScyllaClient();
      const result = await scylla.execute(
        'SELECT * FROM domain_analysis WHERE domain = ?',
        [domain],
      );

      if (result.rows.length === 0)
      {
        return null;
      }

      return this.mapRowToDomainAnalysis(result.first());
    });
  }

  /**
   * Search domains using Manticore Search
   */
  async searchDomains(params: {
    query?: string;
    filters?: Record<string, unknown>;
    sort?: string;
    limit?: number;
    offset?: number;
  }): Promise<{
    domains: AdminDomainAnalysis[];
    total: number;
    facets: Record<string, unknown>;
  }>
  {
    return this.dbManager.executeWithMetrics('manticore', 'searchDomains', async () =>
    {
      const manticore = this.dbManager.getManticoreClient();
      const searchResult = await manticore.searchDomains(params);

      // Convert search results to AdminDomainAnalysis format
      const domains = await Promise.all(
        searchResult.results.map(async (result: any) =>
        {
          // Get full domain data from ScyllaDB
          const fullDomain = await this.getDomainByName(result.domain);
          return fullDomain || this.createMinimalDomainFromSearch(result);
        }),
      );

      return {
        domains: domains.filter(Boolean) as AdminDomainAnalysis[],
        total: searchResult.total,
        facets: searchResult.facets,
      };
    });
  }

  /**
   * Update domain visibility (public/private)
   */
  async updateDomainVisibility(domain: string, isPublic: boolean): Promise<void>
  {
    return this.dbManager.executeWithMetrics('scylla', 'updateDomainVisibility', async () =>
    {
      const scylla = this.dbManager.getScyllaClient();
      await scylla.execute(
        'UPDATE domain_analysis SET is_public = ? WHERE domain = ?',
        [isPublic, domain],
      );

      logger.info(`Domain visibility updated: ${domain} -> ${isPublic ? 'public' : 'private'}`);
    });
  }

  /**
   * Update domain category
   */
  async updateDomainCategory(domain: string, category: string): Promise<void>
  {
    return this.dbManager.executeWithMetrics('scylla', 'updateDomainCategory', async () =>
    {
      const scylla = this.dbManager.getScyllaClient();
      await scylla.execute(
        'UPDATE domain_analysis SET category = ? WHERE domain = ?',
        [category, domain],
      );

      logger.info(`Domain category updated: ${domain} -> ${category}`);
    });
  }

  /**
   * Bulk update domain visibility
   */
  async bulkUpdateVisibility(domains: string[], isPublic: boolean): Promise<BulkOperationResult>
  {
    const startTime = Date.now();
    const errors: string[] = [];
    let affectedRows = 0;

    try
    {
      const scylla = this.dbManager.getScyllaClient();
      const queries = domains.map(domain => ({
        query: 'UPDATE domain_analysis SET is_public = ? WHERE domain = ?',
        params: [isPublic, domain],
      }));

      await scylla.batch(queries);
      affectedRows = domains.length;

      logger.info(`Bulk visibility update completed: ${domains.length} domains -> ${isPublic ? 'public' : 'private'}`);
    }
    catch (error)
    {
      const errorMessage = error instanceof Error ? error.message : String(error);
      errors.push(errorMessage);
      logger.error('Bulk visibility update failed:', error as any);
    }

    return {
      success: errors.length === 0,
      affectedRows,
      errors,
      executionTime: Date.now() - startTime,
    };
  }

  /**
   * Bulk update domain categories
   */
  async bulkUpdateCategory(domains: string[], category: string): Promise<BulkOperationResult>
  {
    const startTime = Date.now();
    const errors: string[] = [];
    let affectedRows = 0;

    try
    {
      const scylla = this.dbManager.getScyllaClient();
      const queries = domains.map(domain => ({
        query: 'UPDATE domain_analysis SET category = ? WHERE domain = ?',
        params: [category, domain],
      }));

      await scylla.batch(queries);
      affectedRows = domains.length;

      logger.info(`Bulk category update completed: ${domains.length} domains -> ${category}`);
    }
    catch (error)
    {
      const errorMessage = error instanceof Error ? error.message : String(error);
      errors.push(errorMessage);
      logger.error('Bulk category update failed:', error as any);
    }

    return {
      success: errors.length === 0,
      affectedRows,
      errors,
      executionTime: Date.now() - startTime,
    };
  }

  /**
   * Delete domain and all related data
   */
  async deleteDomain(domain: string): Promise<void>
  {
    return this.dbManager.executeTransaction(async ({ scylla, mariadb, redis }) =>
    {
      // Delete from ScyllaDB
      await scylla.execute('DELETE FROM domain_analysis WHERE domain = ?', [domain]);
      await scylla.execute('DELETE FROM domain_rankings WHERE domain = ?', [domain]);
      await scylla.execute('DELETE FROM domain_crawl_jobs WHERE domain = ? ALLOW FILTERING', [domain]);

      // Delete from MariaDB
      await mariadb.execute('DELETE FROM domain_category_mapping WHERE domain = ?', [domain]);
      await mariadb.execute('DELETE FROM domain_whois WHERE domain = ?', [domain]);
      await mariadb.execute('DELETE FROM backlinks WHERE target_domain = ? OR source_domain = ?', [domain, domain]);

      // Clear Redis cache
      await redis.invalidatePattern(`*${domain}*`);

      logger.info(`Domain deleted: ${domain}`);
    });
  }

  /**
   * Get domain statistics for dashboard
   */
  async getDomainStatistics(): Promise<{
    total: number;
    public: number;
    private: number;
    byCategory: Record<string, number>;
    byStatus: Record<string, number>;
  }>
  {
    return this.dbManager.executeWithMetrics('scylla', 'getDomainStatistics', async () =>
    {
      const scylla = this.dbManager.getScyllaClient();

      // Get total and visibility counts
      const totalResult = await scylla.execute('SELECT COUNT(*) as total FROM domain_analysis');
      const total = parseInt(String(totalResult.first()?.total || 0), 10);

      const publicResult = await scylla.execute(
        'SELECT COUNT(*) as count FROM domain_analysis WHERE is_public = true ALLOW FILTERING',
      );
      const publicCount = parseInt(String(publicResult.first()?.count || 0), 10);

      // Get category distribution
      const categoryResult = await scylla.execute(
        'SELECT category, COUNT(*) as count FROM domain_analysis GROUP BY category ALLOW FILTERING',
      );
      const byCategory: Record<string, number> = {};
      categoryResult.rows.forEach((row: any) =>
      {
        byCategory[row.category || 'uncategorized'] = parseInt(String(row.count), 10);
      });

      // Get status distribution
      const statusResult = await scylla.execute(
        'SELECT crawl_status, COUNT(*) as count FROM domain_analysis GROUP BY crawl_status ALLOW FILTERING',
      );
      const byStatus: Record<string, number> = {};
      statusResult.rows.forEach((row: any) =>
      {
        byStatus[row.crawl_status || 'unknown'] = parseInt(String(row.count), 10);
      });

      return {
        total,
        public: publicCount,
        private: total - publicCount,
        byCategory,
        byStatus,
      };
    });
  }

  /**
   * Get domains pending in seeder queue
   */
  async getSeederQueueDomains(limit = 100): Promise<{
    domains: string[];
    queueSize: number;
  }>
  {
    return this.dbManager.executeWithMetrics('redis', 'getSeederQueueDomains', async () =>
    {
      const redis = this.dbManager.getRedisClient();

      // Get queue size
      const queueSize = await redis.llen('new:domains');

      // Get domains from queue (without removing them)
      const client = redis.getClient();
      if (!client)
      {
        throw new Error('Redis client not available');
      }

      const domains = await client.lRange('new:domains', 0, limit - 1);

      return {
        domains,
        queueSize,
      };
    });
  }

  // Private helper methods

  private buildWhereConditions(filters: QueryFilter[], params: (string | number | boolean | null)[]): string
  {
    const conditions: string[] = [];

    filters.forEach((filter) =>
    {
      switch (filter.operator)
      {
        case '=':
          conditions.push(`${filter.field} = ?`);
          params.push(filter.value as string | number | boolean | null);
          break;
        case '!=':
          conditions.push(`${filter.field} != ?`);
          params.push(filter.value as string | number | boolean | null);
          break;
        case '>':
          conditions.push(`${filter.field} > ?`);
          params.push(filter.value as string | number | boolean | null);
          break;
        case '<':
          conditions.push(`${filter.field} < ?`);
          params.push(filter.value as string | number | boolean | null);
          break;
        case '>=':
          conditions.push(`${filter.field} >= ?`);
          params.push(filter.value as string | number | boolean | null);
          break;
        case '<=':
          conditions.push(`${filter.field} <= ?`);
          params.push(filter.value as string | number | boolean | null);
          break;
        case 'IN':
          if (Array.isArray(filter.value))
          {
            const placeholders = filter.value.map(() => '?').join(',');
            conditions.push(`${filter.field} IN (${placeholders})`);
            params.push(...filter.value);
          }
          break;
        case 'LIKE':
          conditions.push(`${filter.field} LIKE ?`);
          params.push(filter.value as string | number | boolean | null);
          break;
        default:
          logger.warn(`Unsupported filter operator: ${filter.operator}`);
      }
    });

    return conditions.join(' AND ');
  }

  private mapRowToDomainAnalysis(row: any): AdminDomainAnalysis
  {
    const performanceMetrics = row.performance_metrics || new Map();
    const securityMetrics = row.security_metrics || new Map();
    const seoMetrics = row.seo_metrics || new Map();
    const technicalMetrics = row.technical_metrics || new Map();

    return {
      domain: row.domain,
      globalRank: row.global_rank,
      categoryRank: row.category_rank,
      category: row.category || 'uncategorized',
      overallScore: parseFloat(String(row.overall_score || 0)),
      crawlStatus: row.crawl_status || 'pending',
      lastCrawled: row.last_crawled,
      isPublic: row.is_public || false,
      seederStatus: 'completed', // Default for existing domains
      performance: {
        loadTime: parseFloat(String(performanceMetrics.get?.('load_time') || 0)),
        firstContentfulPaint: parseFloat(String(performanceMetrics.get?.('fcp') || 0)),
        largestContentfulPaint: parseFloat(String(performanceMetrics.get?.('lcp') || 0)),
        cumulativeLayoutShift: parseFloat(String(performanceMetrics.get?.('cls') || 0)),
        firstInputDelay: parseFloat(String(performanceMetrics.get?.('fid') || 0)),
        speedIndex: parseFloat(String(performanceMetrics.get?.('speed_index') || 0)),
        score: parseFloat(String(performanceMetrics.get?.('score') || 0)),
      },
      security: {
        sslGrade: String(securityMetrics.get?.('ssl_grade') || ''),
        securityHeaders: {},
        vulnerabilities: [],
        certificateInfo: {},
        score: parseFloat(String(securityMetrics.get?.('score') || 0)),
      },
      seo: {
        metaTags: {
          title: String(seoMetrics.get?.('title') || ''),
          description: String(seoMetrics.get?.('description') || ''),
        },
        structuredData: [],
        sitemap: {},
        robotsTxt: {},
        score: parseFloat(String(seoMetrics.get?.('score') || 0)),
      },
      technical: {
        technologies: Array.from(row.technologies || []),
        serverInfo: Object.fromEntries(row.server_info || new Map()),
        httpHeaders: {},
        pageSize: parseInt(String(technicalMetrics.get?.('page_size') || 0), 10),
        resourceCount: {},
        score: parseFloat(String(technicalMetrics.get?.('score') || 0)),
      },
      domainInfo: {
        age: row.domain_age_days || 0,
        registrationDate: row.registration_date,
        expirationDate: row.expiration_date,
        registrar: row.registrar || '',
        dnsRecords: Object.fromEntries(row.dns_records || new Map()),
      },
      screenshots: Array.from(row.screenshot_urls || []),
      subdomains: Array.from(row.subdomains || []),
      createdAt: row.created_at || new Date(),
      updatedAt: row.updated_at || new Date(),
    };
  }

  private createMinimalDomainFromSearch(searchResult: any): AdminDomainAnalysis
  {
    return {
      domain: searchResult.domain,
      globalRank: searchResult.globalRank,
      categoryRank: null,
      category: searchResult.category || 'uncategorized',
      overallScore: searchResult.scores?.overall || 0,
      crawlStatus: 'pending',
      lastCrawled: null,
      isPublic: true,
      seederStatus: 'completed',
      performance: {
        loadTime: 0,
        firstContentfulPaint: 0,
        largestContentfulPaint: 0,
        cumulativeLayoutShift: 0,
        firstInputDelay: 0,
        speedIndex: 0,
        score: searchResult.scores?.performance || 0,
      },
      security: {
        sslGrade: searchResult.sslGrade || '',
        securityHeaders: {},
        vulnerabilities: [],
        certificateInfo: {},
        score: searchResult.scores?.security || 0,
      },
      seo: {
        metaTags: {
          title: searchResult.title || '',
          description: searchResult.description || '',
        },
        structuredData: [],
        sitemap: {},
        robotsTxt: {},
        score: searchResult.scores?.seo || 0,
      },
      technical: {
        technologies: searchResult.technologies || [],
        serverInfo: {},
        httpHeaders: {},
        pageSize: 0,
        resourceCount: {},
        score: searchResult.scores?.technical || 0,
      },
      domainInfo: {
        age: searchResult.domainAge || 0,
        registrationDate: null,
        expirationDate: null,
        registrar: searchResult.registrar || '',
        dnsRecords: {},
      },
      screenshots: [],
      subdomains: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }
}

export default DomainRepository;
