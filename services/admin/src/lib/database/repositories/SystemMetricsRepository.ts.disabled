import { logger } from '../../../utils/logger';

import type AdminDatabaseManager from '../AdminDatabaseManager';
import type { SystemMetrics } from '../types';

/**
 * Repository for system metrics and analytics in admin panel
 */
class SystemMetricsRepository
{
  constructor(private dbManager: AdminDatabaseManager) {}

  /**
   * Get comprehensive system metrics for dashboard
   */
  async getSystemMetrics(): Promise<SystemMetrics>
  {
    return this.dbManager.executeWithMetrics('scylla', 'getSystemMetrics', async () =>
    {
      const [domainStats, crawlStats, seederStats, performanceStats] = await Promise.all([
        this.getDomainMetrics(),
        this.getCrawlMetrics(),
        this.getSeederMetrics(),
        this.getPerformanceMetrics(),
      ]);

      return {
        domains: domainStats,
        crawling: crawlStats,
        seeding: seederStats,
        performance: performanceStats,
        timestamp: new Date(),
      };
    });
  }

  /**
   * Get domain-related metrics
   */
  private async getDomainMetrics(): Promise<SystemMetrics['domains']>
  {
    const scylla = this.dbManager.getScyllaClient();

    // Get total domain count
    const totalResult = await scylla.execute('SELECT COUNT(*) as total FROM domain_analysis');
    const total = parseInt(String(totalResult.first()?.total || 0), 10);

    // Get public/private distribution
    const publicResult = await scylla.execute(
      'SELECT COUNT(*) as count FROM domain_analysis WHERE is_public = true ALLOW FILTERING',
    );
    const publicCount = parseInt(String(publicResult.first()?.count || 0), 10);

    // Get category distribution
    const categoryResult = await scylla.execute(
      'SELECT category, COUNT(*) as count FROM domain_analysis GROUP BY category ALLOW FILTERING',
    );
    const byCategory: Record<string, number> = {};
    categoryResult.rows.forEach((row: any) =>
    {
      byCategory[row.category || 'uncategorized'] = parseInt(String(row.count), 10);
    });

    // Get status distribution
    const statusResult = await scylla.execute(
      'SELECT crawl_status, COUNT(*) as count FROM domain_analysis GROUP BY crawl_status ALLOW FILTERING',
    );
    const byStatus: Record<string, number> = {};
    statusResult.rows.forEach((row: any) =>
    {
      byStatus[row.crawl_status || 'unknown'] = parseInt(String(row.count), 10);
    });

    return {
      total,
      public: publicCount,
      private: total - publicCount,
      byCategory,
      byStatus,
    };
  }

  /**
   * Get crawling-related metrics
   */
  private async getCrawlMetrics(): Promise<SystemMetrics['crawling']>
  {
    const scylla = this.dbManager.getScyllaClient();

    // Get total jobs
    const totalResult = await scylla.execute('SELECT COUNT(*) as total FROM domain_crawl_jobs');
    const totalJobs = parseInt(String(totalResult.first()?.total || 0), 10);

    // Get completed and failed jobs for success rate
    const statusResult = await scylla.execute(
      'SELECT status, COUNT(*) as count FROM domain_crawl_jobs GROUP BY status ALLOW FILTERING',
    );

    let completedJobs = 0;
    let failedJobs = 0;
    statusResult.rows.forEach((row: any) =>
    {
      if (row.status === 'completed')
      {
        completedJobs = parseInt(String(row.count), 10);
      }
      else if (row.status === 'failed')
      {
        failedJobs = parseInt(String(row.count), 10);
      }
    });

    const successRate = completedJobs + failedJobs > 0
      ? (completedJobs / (completedJobs + failedJobs)) * 100
      : 0;

    // Get average completion time
    const avgTimeResult = await scylla.execute(
      'SELECT AVG(duration) as avg_time FROM domain_crawl_jobs WHERE duration IS NOT NULL ALLOW FILTERING',
    );
    const averageTime = parseFloat(String(avgTimeResult.first()?.avg_time || 0));

    // Get jobs per hour (last 24 hours)
    const oneDayAgo = new Date();
    oneDayAgo.setHours(oneDayAgo.getHours() - 24);
    const recentJobsResult = await scylla.execute(
      'SELECT COUNT(*) as count FROM domain_crawl_jobs WHERE scheduled_at >= ? ALLOW FILTERING',
      [oneDayAgo.toISOString()],
    );
    const recentJobs = parseInt(String(recentJobsResult.first()?.count || 0), 10);
    const jobsPerHour = recentJobs / 24;

    // Calculate error rate
    const errorRate = totalJobs > 0 ? (failedJobs / totalJobs) * 100 : 0;

    return {
      totalJobs,
      successRate,
      averageTime,
      jobsPerHour,
      errorRate,
    };
  }

  /**
   * Get seeder-related metrics
   */
  private async getSeederMetrics(): Promise<SystemMetrics['seeding']>
  {
    const redis = this.dbManager.getRedisClient();

    // Get queue size
    const queueSize = await redis.llen('new:domains');

    // Get processing rate (estimated from recent activity)
    // This would typically come from seeder service metrics
    const processingRate = 100; // domains per hour - placeholder

    // Get discovery rate (estimated)
    const discoveryRate = 50; // new domains per hour - placeholder

    // Get content generation rate (estimated)
    const contentGenerationRate = 80; // domains with content per hour - placeholder

    return {
      queueSize,
      processingRate,
      discoveryRate,
      contentGenerationRate,
    };
  }

  /**
   * Get performance-related metrics
   */
  private async getPerformanceMetrics(): Promise<SystemMetrics['performance']>
  {
    // Get database performance metrics
    const dbMetrics = this.dbManager.getPerformanceMetrics();

    let totalQueries = 0;
    let totalResponseTime = 0;
    let totalErrors = 0;

    dbMetrics.forEach((metric) =>
    {
      totalQueries += metric.totalQueries;
      totalResponseTime += metric.averageResponseTime * metric.totalQueries;
      totalErrors += metric.failedQueries;
    });

    const averageResponseTime = totalQueries > 0 ? totalResponseTime / totalQueries : 0;
    const errorRate = totalQueries > 0 ? (totalErrors / totalQueries) * 100 : 0;

    // Calculate uptime (simplified - would typically come from service monitoring)
    const uptime = 99.9; // percentage - placeholder

    // Calculate throughput (requests per second)
    const throughput = totalQueries / 3600; // assuming metrics are for last hour

    return {
      averageResponseTime,
      uptime,
      errorRate,
      throughput,
    };
  }

  /**
   * Get historical metrics for trending
   */
  async getHistoricalMetrics(
    hours = 24,
    interval = 'hour',
  ): Promise<Array<{
    timestamp: Date;
    domains: number;
    crawlJobs: number;
    successRate: number;
    averageResponseTime: number;
  }>>
  {
    return this.dbManager.executeWithMetrics('scylla', 'getHistoricalMetrics', async () =>
    {
      const scylla = this.dbManager.getScyllaClient();
      const metrics: Array<{
        timestamp: Date;
        domains: number;
        crawlJobs: number;
        successRate: number;
        averageResponseTime: number;
      }> = [];

      // This is a simplified implementation
      // In a real system, you'd have time-series data stored
      const now = new Date();
      for (let i = hours; i >= 0; i--)
      {
        const timestamp = new Date(now.getTime() - i * 60 * 60 * 1000);

        // Get domain count at this time (simplified)
        const domainResult = await scylla.execute(
          'SELECT COUNT(*) as count FROM domain_analysis WHERE created_at <= ? ALLOW FILTERING',
          [timestamp.toISOString()],
        );
        const domains = parseInt(String(domainResult.first()?.count || 0), 10);

        // Get crawl jobs at this time
        const jobResult = await scylla.execute(
          'SELECT COUNT(*) as count FROM domain_crawl_jobs WHERE scheduled_at <= ? ALLOW FILTERING',
          [timestamp.toISOString()],
        );
        const crawlJobs = parseInt(String(jobResult.first()?.count || 0), 10);

        metrics.push({
          timestamp,
          domains,
          crawlJobs,
          successRate: 95, // placeholder
          averageResponseTime: 150, // placeholder
        });
      }

      return metrics;
    });
  }

  /**
   * Get service-specific metrics
   */
  async getServiceMetrics(): Promise<Record<string, {
    responseTime: number;
    errorRate: number;
    requestCount: number;
    uptime: number;
  }>>
  {
    // This would typically query service health endpoints
    // For now, return mock data based on database performance
    const dbMetrics = this.dbManager.getPerformanceMetrics();
    const serviceMetrics: Record<string, any> = {};

    // Map database metrics to service metrics
    dbMetrics.forEach((metric) =>
    {
      serviceMetrics[metric.database] = {
        responseTime: metric.averageResponseTime,
        errorRate: metric.totalQueries > 0
          ? (metric.failedQueries / metric.totalQueries) * 100
          : 0,
        requestCount: metric.totalQueries,
        uptime: 99.5, // placeholder
      };
    });

    return serviceMetrics;
  }

  /**
   * Get top domains by various metrics
   */
  async getTopDomains(
    metric: 'rank' | 'score' | 'traffic',
    limit = 10,
  ): Promise<Array<{
    domain: string;
    value: number;
    category: string;
  }>>
  {
    return this.dbManager.executeWithMetrics('scylla', 'getTopDomains', async () =>
    {
      const scylla = this.dbManager.getScyllaClient();
      let query: string;
      let orderBy: string;

      switch (metric)
      {
        case 'rank':
          query = 'SELECT domain, global_rank as value, category FROM domain_analysis WHERE global_rank IS NOT NULL';
          orderBy = 'ORDER BY global_rank ASC';
          break;
        case 'score':
          query = 'SELECT domain, overall_score as value, category FROM domain_analysis WHERE overall_score IS NOT NULL';
          orderBy = 'ORDER BY overall_score DESC';
          break;
        case 'traffic':
          query = 'SELECT domain, traffic_estimate as value, category FROM domain_rankings WHERE traffic_estimate IS NOT NULL';
          orderBy = 'ORDER BY traffic_estimate DESC';
          break;
        default:
          throw new Error(`Unsupported metric: ${metric}`);
      }

      const result = await scylla.execute(
        `${query} ${orderBy} LIMIT ? ALLOW FILTERING`,
        [limit],
      );

      return result.rows.map((row: any) => ({
        domain: row.domain,
        value: parseFloat(String(row.value)),
        category: row.category || 'uncategorized',
      }));
    });
  }

  /**
   * Get database storage usage statistics
   */
  async getStorageMetrics(): Promise<Record<string, {
    totalSize: number;
    tableCount: number;
    indexCount: number;
    estimatedGrowth: number;
  }>>
  {
    // This would typically query database system tables
    // For now, return estimated values
    return {
      scylla: {
        totalSize: 1024 * 1024 * 1024 * 50, // 50GB
        tableCount: 15,
        indexCount: 25,
        estimatedGrowth: 1024 * 1024 * 1024 * 2, // 2GB per day
      },
      mariadb: {
        totalSize: 1024 * 1024 * 1024 * 10, // 10GB
        tableCount: 8,
        indexCount: 15,
        estimatedGrowth: 1024 * 1024 * 100, // 100MB per day
      },
      redis: {
        totalSize: 1024 * 1024 * 1024 * 2, // 2GB
        tableCount: 0, // Redis doesn't have tables
        indexCount: 0,
        estimatedGrowth: 1024 * 1024 * 50, // 50MB per day
      },
      manticore: {
        totalSize: 1024 * 1024 * 1024 * 5, // 5GB
        tableCount: 3,
        indexCount: 10,
        estimatedGrowth: 1024 * 1024 * 200, // 200MB per day
      },
    };
  }

  /**
   * Get alert-worthy metrics
   */
  async getAlertMetrics(): Promise<Array<{
    type: 'warning' | 'error' | 'critical';
    message: string;
    value: number;
    threshold: number;
    timestamp: Date;
  }>>
  {
    const alerts: Array<{
      type: 'warning' | 'error' | 'critical';
      message: string;
      value: number;
      threshold: number;
      timestamp: Date;
    }> = [];

    // Check database health
    const healthCheck = await this.dbManager.performHealthCheck();
    if (!healthCheck.healthy)
    {
      Object.entries(healthCheck.databases).forEach(([name, status]) =>
      {
        if (!status.connected)
        {
          alerts.push({
            type: 'critical',
            message: `Database ${name} is not connected`,
            value: 0,
            threshold: 1,
            timestamp: new Date(),
          });
        }
        else if (status.responseTime > 1000)
        {
          alerts.push({
            type: 'warning',
            message: `Database ${name} response time is high`,
            value: status.responseTime,
            threshold: 1000,
            timestamp: new Date(),
          });
        }
      });
    }

    // Check performance metrics
    const performanceMetrics = this.dbManager.getPerformanceMetrics();
    performanceMetrics.forEach((metric) =>
    {
      const errorRate = metric.totalQueries > 0
        ? (metric.failedQueries / metric.totalQueries) * 100
        : 0;

      if (errorRate > 5)
      {
        alerts.push({
          type: 'error',
          message: `High error rate in ${metric.database}`,
          value: errorRate,
          threshold: 5,
          timestamp: new Date(),
        });
      }

      if (metric.averageResponseTime > 2000)
      {
        alerts.push({
          type: 'warning',
          message: `Slow queries in ${metric.database}`,
          value: metric.averageResponseTime,
          threshold: 2000,
          timestamp: new Date(),
        });
      }
    });

    return alerts;
  }
}

export default SystemMetricsRepository;
