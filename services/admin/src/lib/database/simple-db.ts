/**
 * Simple database connections - just basic connectivity
 * No complex pooling, no Redis, just what we need
 */

import { Client as CassandraClient } from 'cassandra-driver';
import mysql from 'mysql2/promise';
import { HttpClient, logger } from '@shared';

// Simple MySQL connection
let mysqlConnection: mysql.Connection | null = null;

async function getMySQLConnection()
{
	if (!mysqlConnection)
	{
		mysqlConnection = await mysql.createConnection({
			host: process.env.MARIADB_HOST || 'localhost',
			port: parseInt(process.env.MARIADB_PORT || '3306', 10),
			user: process.env.MARIADB_USERNAME || 'root',
			password: process.env.MARIADB_PASSWORD || '',
			database: process.env.MARIADB_DATABASE || 'domainr',
		});
	}
	return mysqlConnection;
}

// Simple Cassandra connection
let cassandraClient: CassandraClient | null = null;

export function getCassandraClient()
{
	if (!cassandraClient)
	{
		cassandraClient = new CassandraClient({
			contactPoints: (process.env.SCYLLA_HOSTS || 'localhost:9042').split(','),
			localDataCenter: 'datacenter1',
			keyspace: process.env.SCYLLA_KEYSPACE || 'domainr',
		});
	}
	return cassandraClient;
}

// Simple health check functions
async function checkMySQLHealth(): Promise<boolean>
{
	try
	{
		const connection = await getMySQLConnection();
		await connection.ping();
		return true;
	}
	catch
	{
		return false;
	}
}

async function checkCassandraHealth(): Promise<boolean>
{
	try
	{
		const client = getCassandraClient();
		await client.execute('SELECT now() FROM system.local');
		return true;
	}
	catch
	{
		return false;
	}
}

// Simple service health check
async function checkServiceHealth(url: string): Promise<boolean>
{
	try
	{
		const httpClient = new HttpClient(logger.getLogger('SimpleDB'), { timeout: 5000 });
		const response = await httpClient.get(`${url}/health`);
		return response.statusCode >= 200 && response.statusCode < 300;
	}
	catch
	{
		return false;
	}
}

export {
	getMySQLConnection,
	checkMySQLHealth,
	checkCassandraHealth,
	checkServiceHealth,
};
