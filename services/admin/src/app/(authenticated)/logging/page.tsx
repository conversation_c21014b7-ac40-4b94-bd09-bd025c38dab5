'use client';

import { Container, Tabs } from '@mantine/core';
import {
	IconFileText,
	IconChartLine,
	IconSettings,
	IconBell,
	IconArchive,
} from '@tabler/icons-react';
import { useState } from 'react';
import {
	LogsViewer,
	LogAnalytics,
	LogLevelManager,
	LogAlerts,
	LogArchival,
} from '../../../components/Logging';

function LogsPage()
{
	const [activeTab, setActiveTab] = useState<string>('viewer');

	return (
		<Container size="xl" py="md">
			<Tabs value={activeTab} onChange={value => setActiveTab(value || 'viewer')}>
				<Tabs.List>
					<Tabs.Tab value="viewer" leftSection={<IconFileText size={16} />}>
						Log Viewer
					</Tabs.Tab>
					<Tabs.Tab value="analytics" leftSection={<IconChartLine size={16} />}>
						Analytics
					</Tabs.Tab>

					<Tabs.Tab value="levels" leftSection={<IconSettings size={16} />}>
						Log Levels
					</Tabs.Tab>
					<Tabs.Tab value="alerts" leftSection={<IconBell size={16} />}>
						Alerts
					</Tabs.Tab>
					<Tabs.Tab value="archival" leftSection={<IconArchive size={16} />}>
						Archival
					</Tabs.Tab>
				</Tabs.List>

				<Tabs.Panel value="viewer" pt="md">
					<LogsViewer />
				</Tabs.Panel>

				<Tabs.Panel value="analytics" pt="md">
					<LogAnalytics />
				</Tabs.Panel>



				<Tabs.Panel value="levels" pt="md">
					<LogLevelManager />
				</Tabs.Panel>

				<Tabs.Panel value="alerts" pt="md">
					<LogAlerts />
				</Tabs.Panel>

				<Tabs.Panel value="archival" pt="md">
					<LogArchival />
				</Tabs.Panel>
			</Tabs>
		</Container>
	);
}

export default LogsPage;
