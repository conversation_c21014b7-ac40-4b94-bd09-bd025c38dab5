'use client';

import {
	Container,
	Title,
	Tabs,
	Card,
	Text,
	Group,
	Button,
	SimpleGrid,
	Paper,
	Badge,
	ThemeIcon,
	Stack,
} from '@mantine/core';
import {
	IconAlertTriangle,
	IconBell,
	IconSettings,
	IconTestPipe,
	IconChartBar,
	IconShield,
	IconUsers,
	IconMail,
} from '@tabler/icons-react';
import { useState } from 'react';

import AlertAnalytics from '@/components/Alerts/AlertAnalytics';
import AlertDashboard from '@/components/Alerts/AlertDashboard';
import AlertRuleManager from '@/components/Alerts/AlertRuleManager';
import AlertTestFramework from '@/components/Alerts/AlertTestFramework';
import NotificationManager from '@/components/Alerts/NotificationManager';

export default function AlertsPage()
{
	const [activeTab, setActiveTab] = useState('dashboard');

	const features = [
		{
			icon: IconAlertTriangle,
			title: 'Real-time Monitoring',
			description: 'Monitor all services with real-time alert processing and intelligent correlation',
			color: 'red',
		},
		{
			icon: IconBell,
			title: 'Multi-channel Notifications',
			description: 'Send alerts via email, Slack, SMS, PagerDuty, Teams, JIRA, and webhooks',
			color: 'blue',
		},
		{
			icon: IconSettings,
			title: 'Advanced Rule Engine',
			description: 'Create complex alert rules with logical operators, time-based conditions, and dependencies',
			color: 'green',
		},
		{
			icon: IconTestPipe,
			title: 'Testing Framework',
			description: 'Test alert rules and notification delivery with comprehensive end-to-end testing',
			color: 'violet',
		},
		{
			icon: IconChartBar,
			title: 'Analytics & Insights',
			description: 'Track MTTR, MTBF, escalation rates, and notification effectiveness',
			color: 'orange',
		},
		{
			icon: IconShield,
			title: 'Smart Suppression',
			description: 'Intelligent alert silencing with maintenance windows and dependency-based suppression',
			color: 'teal',
		},
	];

	return (
		<Container size="xl" py="md">
			<Group justify="space-between" mb="lg">
				<div>
					<Title order={1}>Alert Management System</Title>
					<Text c="dimmed" size="lg">
						Comprehensive alerting with intelligent notifications and analytics
					</Text>
				</div>
			</Group>

			{activeTab === 'overview' && (
				<Stack>
					<Card withBorder p="xl">
						<Title order={2} mb="md">System Overview</Title>
						<Text mb="lg">
							The Alert Management System provides comprehensive monitoring and notification capabilities
							for the Domain Ranking System. It features real-time alert processing, multi-channel
							notifications, advanced rule management, and detailed analytics.
						</Text>

						<SimpleGrid cols={{ base: 1, md: 2, lg: 3 }}>
							{features.map((feature, index) => (
								<Paper key={index} p="md" withBorder>
									<Group mb="sm">
										<ThemeIcon color={feature.color} variant="light" size="lg">
											<feature.icon size={20} />
										</ThemeIcon>
										<Text fw={600}>{feature.title}</Text>
									</Group>
									<Text size="sm" c="dimmed">
										{feature.description}
									</Text>
								</Paper>
							))}
						</SimpleGrid>
					</Card>

					<SimpleGrid cols={{ base: 1, md: 2 }}>
						<Card withBorder>
							<Group justify="space-between" mb="md">
								<Text fw={600}>Quick Actions</Text>
							</Group>
							<Stack gap="sm">
								<Button
									variant="light"
									leftSection={<IconAlertTriangle size={16} />}
									onClick={() => setActiveTab('dashboard')}
									fullWidth
								>
									View Alert Dashboard
								</Button>
								<Button
									variant="light"
									leftSection={<IconSettings size={16} />}
									onClick={() => setActiveTab('rules')}
									fullWidth
								>
									Manage Alert Rules
								</Button>
								<Button
									variant="light"
									leftSection={<IconMail size={16} />}
									onClick={() => setActiveTab('notifications')}
									fullWidth
								>
									Configure Notifications
								</Button>
								<Button
									variant="light"
									leftSection={<IconTestPipe size={16} />}
									onClick={() => setActiveTab('testing')}
									fullWidth
								>
									Test Alert System
								</Button>
							</Stack>
						</Card>

						<Card withBorder>
							<Group justify="space-between" mb="md">
								<Text fw={600}>System Status</Text>
								<Badge color="green" variant="light">All Systems Operational</Badge>
							</Group>
							<Stack gap="sm">
								<Group justify="space-between">
									<Text size="sm">Active Alerts</Text>
									<Badge color="red" variant="light">3</Badge>
								</Group>
								<Group justify="space-between">
									<Text size="sm">Alert Rules</Text>
									<Badge variant="light">24</Badge>
								</Group>
								<Group justify="space-between">
									<Text size="sm">Notification Channels</Text>
									<Badge color="blue" variant="light">7</Badge>
								</Group>
								<Group justify="space-between">
									<Text size="sm">Integrations</Text>
									<Badge color="green" variant="light">5 Connected</Badge>
								</Group>
							</Stack>
						</Card>
					</SimpleGrid>
				</Stack>
			)}

			<Tabs value={activeTab} onChange={value => setActiveTab(value || 'overview')}>
				<Tabs.List>
					<Tabs.Tab value="overview" leftSection={<IconShield size={16} />}>
						Overview
					</Tabs.Tab>
					<Tabs.Tab value="dashboard" leftSection={<IconAlertTriangle size={16} />}>
						Dashboard
					</Tabs.Tab>
					<Tabs.Tab value="rules" leftSection={<IconSettings size={16} />}>
						Rules
					</Tabs.Tab>
					<Tabs.Tab value="notifications" leftSection={<IconBell size={16} />}>
						Notifications
					</Tabs.Tab>
					<Tabs.Tab value="testing" leftSection={<IconTestPipe size={16} />}>
						Testing
					</Tabs.Tab>
					<Tabs.Tab value="analytics" leftSection={<IconChartBar size={16} />}>
						Analytics
					</Tabs.Tab>
				</Tabs.List>

				<Tabs.Panel value="overview" pt="md">
					<div>Overview content is rendered above</div>
				</Tabs.Panel>

				<Tabs.Panel value="dashboard" pt="md">
					<AlertDashboard />
				</Tabs.Panel>

				<Tabs.Panel value="rules" pt="md">
					<AlertRuleManager />
				</Tabs.Panel>

				<Tabs.Panel value="notifications" pt="md">
					<NotificationManager />
				</Tabs.Panel>

				<Tabs.Panel value="testing" pt="md">
					<AlertTestFramework />
				</Tabs.Panel>

				<Tabs.Panel value="analytics" pt="md">
					<AlertAnalytics />
				</Tabs.Panel>
			</Tabs>
		</Container>
	);
}
