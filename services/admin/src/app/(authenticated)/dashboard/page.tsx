'use client';

import {
	Container, Title, Text, Paper, Grid, Badge, Tabs,
} from '@mantine/core';
import { IconChartBar, IconShield, IconServer } from '@tabler/icons-react';

import { AuthTest } from '@/components/Auth/AuthTest';

import ServiceHealthDashboard from '@/components/Services/ServiceHealthDashboard';

function DashboardPage()
{
	return (
		<Container size="xl" py="md">
			<Title order={1} mb="lg">
				Dashboard
			</Title>

			<Tabs defaultValue="overview">
				<Tabs.List>
					<Tabs.Tab value="overview" leftSection={<IconChartBar size={14} />}>
						Overview
					</Tabs.Tab>
					<Tabs.Tab value="services" leftSection={<IconServer size={14} />}>
						Service Health
					</Tabs.Tab>

					<Tabs.Tab value="auth-test" leftSection={<IconShield size={14} />}>
						Authentication Test
					</Tabs.Tab>
				</Tabs.List>

				<Tabs.Panel value="overview" pt="md">
					<Grid>
						<Grid.Col span={{ base: 12, md: 6, lg: 4 }}>
							<Paper withBorder p="md" radius="md">
								<Text size="sm" c="dimmed" mb="xs">
									System Status
								</Text>
								<Badge color="green" variant="light">
									All Systems Operational
								</Badge>
							</Paper>
						</Grid.Col>

						<Grid.Col span={{ base: 12, md: 6, lg: 4 }}>
							<Paper withBorder p="md" radius="md">
								<Text size="sm" c="dimmed" mb="xs">
									Total Domains
								</Text>
								<Text size="xl" fw={700}>
									Loading...
								</Text>
							</Paper>
						</Grid.Col>

						<Grid.Col span={{ base: 12, md: 6, lg: 4 }}>
							<Paper withBorder p="md" radius="md">
								<Text size="sm" c="dimmed" mb="xs">
									Active Crawl Jobs
								</Text>
								<Text size="xl" fw={700}>
									Loading...
								</Text>
							</Paper>
						</Grid.Col>
					</Grid>

					<Paper withBorder p="md" mt="xl">
						<Title order={2} mb="md">
							Authentication System Status
						</Title>
						<Text mb="md">
							The admin panel now includes a comprehensive authentication system with the
							following features:
						</Text>
						<Grid>
							<Grid.Col span={{ base: 12, md: 6 }}>
								<Badge color="green" variant="light" mb="xs">✓ Argon2 Password Hashing</Badge><br />
								<Badge color="green" variant="light" mb="xs">✓ Iron-Session with Redis</Badge><br />
								<Badge color="green" variant="light" mb="xs">✓ Role-Based Access Control</Badge><br />
								<Badge color="green" variant="light" mb="xs">✓ 8-Hour Session Timeout</Badge>
							</Grid.Col>
							<Grid.Col span={{ base: 12, md: 6 }}>
								<Badge color="green" variant="light" mb="xs">✓ Concurrent Session Support</Badge><br />
								<Badge color="green" variant="light" mb="xs">✓ Rate Limiting & Account Lockout</Badge><br />
								<Badge color="green" variant="light" mb="xs">✓ Password Strength Validation</Badge><br />
								<Badge color="green" variant="light" mb="xs">✓ Comprehensive Audit Logging</Badge>
							</Grid.Col>
						</Grid>
					</Paper>
				</Tabs.Panel>

				<Tabs.Panel value="services" pt="md">
					<ServiceHealthDashboard />
				</Tabs.Panel>



				<Tabs.Panel value="auth-test" pt="md">
					<AuthTest />
				</Tabs.Panel>
			</Tabs>
		</Container>
	);
}

export default DashboardPage;
