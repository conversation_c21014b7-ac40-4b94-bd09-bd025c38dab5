'use client';

import { Container, Tabs } from '@mantine/core';
import { IconUsers, IconActivity, IconChartBar } from '@tabler/icons-react';

import { AuditLogViewer, UserAnalytics, UserManagementInterface } from '@/components/Users';

export default function UsersPage()
{
	return (
		<Container size="xl" py="md">
			<Tabs defaultValue="management">
				<Tabs.List>
					<Tabs.Tab value="management" leftSection={<IconUsers size={16} />}>
						User Management
					</Tabs.Tab>
					<Tabs.Tab value="analytics" leftSection={<IconChartBar size={16} />}>
						Analytics
					</Tabs.Tab>
					<Tabs.Tab value="audit" leftSection={<IconActivity size={16} />}>
						Audit Logs
					</Tabs.Tab>
				</Tabs.List>

				<Tabs.Panel value="management" pt="md">
					<UserManagementInterface />
				</Tabs.Panel>

				<Tabs.Panel value="analytics" pt="md">
					<UserAnalytics />
				</Tabs.Panel>

				<Tabs.Panel value="audit" pt="md">
					<AuditLogViewer />
				</Tabs.Panel>
			</Tabs>
		</Container>
	);
}
