'use client';

import {
	Container,
	Title,
	Text,
	Stack,
	Group,
	Button,
	Card,
	Grid,
	Badge,
	Alert,
	Progress,
	Tabs,
	Paper,
	ActionIcon,
	Menu,
	Modal,
	TextInput,
	Select,
	Switch,
	Slider,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import {
	IconRefresh,
	IconDownload,
	IconSettings,
	IconChartBar,
	IconDatabase,
	IconServer,
	IconUsers,
	IconAlertTriangle,
	IconCheck,
	IconX,
	IconInfoCircle,
	IconBell,
	IconHeart,
	IconTrendingUp,
	IconTrendingDown,
} from '@tabler/icons-react';
import { useState } from 'react';
import {
	LineChart, Line, AreaChart, Area, BarChart, Bar, PieChart, Pie, Cell, ResponsiveContainer as RechartsResponsiveContainer,
} from 'recharts';

import ChartContainer from '@/components/UI/ChartContainer/ChartContainer';
import DataTable from '@/components/UI/DataTable/DataTable';
import { ErrorBoundary } from '@/components/UI/ErrorBoundary/ErrorBoundary';
import { LoadingStates } from '@/components/UI/LoadingStates/LoadingStates';
import MetricCard from '@/components/UI/MetricCard/MetricCard';
import { PrintableComponent, usePrint } from '@/components/UI/PrintStyles/PrintStyles';
import { ResponsiveGrid, ResponsiveContainer, BreakpointVisibility } from '@/components/UI/ResponsiveLayout/ResponsiveLayout';
import StatusBadge from '@/components/UI/StatusBadge/StatusBadge';

// Sample data
const sampleTableData = [
	{
		id: 1, domain: 'example.com', status: 'healthy', rank: 1, score: 95.2, category: 'Technology',
	},
	{
		id: 2, domain: 'test.org', status: 'warning', rank: 2, score: 87.5, category: 'Education',
	},
	{
		id: 3, domain: 'demo.net', status: 'error', rank: 3, score: 72.1, category: 'Business',
	},
	{
		id: 4, domain: 'sample.io', status: 'healthy', rank: 4, score: 91.8, category: 'Technology',
	},
	{
		id: 5, domain: 'placeholder.co', status: 'pending', rank: 5, score: 68.9, category: 'Design',
	},
];

const sampleChartData = [
	{ name: 'Jan', value: 400, growth: 24 },
	{ name: 'Feb', value: 300, growth: -13 },
	{ name: 'Mar', value: 600, growth: 18 },
	{ name: 'Apr', value: 800, growth: 39 },
	{ name: 'May', value: 500, growth: -12 },
	{ name: 'Jun', value: 900, growth: 48 },
];

const pieData = [
	{ name: 'Technology', value: 400, color: '#8884d8' },
	{ name: 'Business', value: 300, color: '#82ca9d' },
	{ name: 'Education', value: 200, color: '#ffc658' },
	{ name: 'Design', value: 100, color: '#ff7c7c' },
];

function UIDemo()
{
	const [loading, setLoading] = useState(false);
	const [selectedRows, setSelectedRows] = useState<string[]>([]);
	const [sortBy, setSortBy] = useState<string>('');
	const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
	const [filters, setFilters] = useState<Record<string, any>>({});
	const [opened, { open, close }] = useDisclosure(false);
	const { print, printPreview } = usePrint();

	const tableColumns = [
		{
			key: 'domain',
			title: 'Domain',
			dataIndex: 'domain' as keyof typeof sampleTableData[0],
			sortable: true,
			filterable: true,
			filterType: 'text' as const,
		},
		{
			key: 'status',
			title: 'Status',
			dataIndex: 'status' as keyof typeof sampleTableData[0],
			sortable: true,
			filterable: true,
			filterType: 'select' as const,
			filterOptions: [
				{ label: 'Healthy', value: 'healthy' },
				{ label: 'Warning', value: 'warning' },
				{ label: 'Error', value: 'error' },
				{ label: 'Pending', value: 'pending' },
			],
			render: (value: unknown) => <StatusBadge status={String(value)} />,
		},
		{
			key: 'rank',
			title: 'Rank',
			dataIndex: 'rank' as keyof typeof sampleTableData[0],
			sortable: true,
			align: 'center' as const,
		},
		{
			key: 'score',
			title: 'Score',
			dataIndex: 'score' as keyof typeof sampleTableData[0],
			sortable: true,
			filterable: true,
			filterType: 'number' as const,
			render: (value: unknown) => `${Number(value)}%`,
		},
		{
			key: 'category',
			title: 'Category',
			dataIndex: 'category' as keyof typeof sampleTableData[0],
			filterable: true,
			filterType: 'select' as const,
			filterOptions: [
				{ label: 'Technology', value: 'Technology' },
				{ label: 'Business', value: 'Business' },
				{ label: 'Education', value: 'Education' },
				{ label: 'Design', value: 'Design' },
			],
			render: (value: unknown) => <Badge variant="light">{String(value)}</Badge>,
		},
	];

	const handleRefresh = async () =>
	{
		setLoading(true);
		await new Promise(resolve => setTimeout(resolve, 2000));
		setLoading(false);
	};

	const handleExport = (data: any[], format: string) =>
	{
		console.log('Exporting data:', { data, format });
	};

	return (
		<ResponsiveContainer size="xl">
			<Stack gap="xl">
				{/* Header */}
				<Group justify="space-between">
					<div>
						<Title order={1}>UI Components Demo</Title>
						<Text c="dimmed">Showcase of responsive UI components and layouts</Text>
					</div>
					<Group>
						<Button onClick={() => printPreview()} variant="outline" leftSection={<IconDownload size={16} />}>
							Print Preview
						</Button>
						<Button onClick={open} leftSection={<IconSettings size={16} />}>
							Settings
						</Button>
					</Group>
				</Group>

				{/* Status Badges */}
				<Card withBorder>
					<Title order={3} mb="md">Status Badges</Title>
					<Group>
						<StatusBadge status="success" />
						<StatusBadge status="error" />
						<StatusBadge status="warning" />
						<StatusBadge status="info" />
						<StatusBadge status="pending" />
						<StatusBadge status="processing" animated />
						<StatusBadge status="healthy" pulse />
						<StatusBadge status="unhealthy" />
						<StatusBadge status="degraded" />
						<StatusBadge status="active" />
						<StatusBadge status="inactive" />
					</Group>
				</Card>

				{/* Metric Cards */}
				<ResponsiveGrid columns={{
					base: 1, sm: 2, md: 3, lg: 4,
				}}
				>
					<MetricCard
						title="Total Domains"
						value={12543}
						icon={<IconDatabase size={24} />}
						trend={{
							value: 1250, percentage: 12.5, direction: 'up', isGood: true,
						}}
						status="success"
						animated
					/>
					<MetricCard
						title="Active Services"
						value="8/10"
						subtitle="2 services degraded"
						icon={<IconServer size={24} />}
						trend={{
							value: -2, percentage: 5.2, direction: 'down', isGood: false,
						}}
						status="warning"
					/>
					<MetricCard
						title="Response Time"
						value={245}
						format="duration"
						icon={<IconTrendingUp size={24} />}
						sparkline={sampleChartData.map(d => ({ x: d.name, y: d.value }))}
						status="info"
					/>
					<MetricCard
						title="Error Rate"
						value={0.02}
						format="percentage"
						icon={<IconAlertTriangle size={24} />}
						trend={{
							value: -0.01, percentage: 15.3, direction: 'down', isGood: true,
						}}
						status="success"
					/>
				</ResponsiveGrid>

				{/* Charts */}
				<ResponsiveGrid columns={{ base: 1, lg: 2 }}>
					<ChartContainer
						title="Domain Growth"
						subtitle="Monthly domain additions"
						height={300}
						refreshable
						onRefresh={handleRefresh}
						exportable
						onExport={format => console.log('Export chart:', format)}
					>
						<RechartsResponsiveContainer width="100%" height="100%">
							<AreaChart data={sampleChartData}>
								<Area
									type="monotone"
									dataKey="value"
									stroke="#8884d8"
									fill="#8884d8"
									fillOpacity={0.3}
								/>
							</AreaChart>
						</RechartsResponsiveContainer>
					</ChartContainer>

					<ChartContainer
						title="Category Distribution"
						subtitle="Domains by category"
						height={300}
					>
						<RechartsResponsiveContainer width="100%" height="100%">
							<PieChart>
								<Pie
									data={pieData}
									cx="50%"
									cy="50%"
									outerRadius={80}
									dataKey="value"
								>
									{pieData.map((entry, index) => (
										<Cell key={`cell-${index}`} fill={entry.color} />
									))}
								</Pie>
							</PieChart>
						</RechartsResponsiveContainer>
					</ChartContainer>
				</ResponsiveGrid>

				{/* Data Table */}
				<Card withBorder>
					<Title order={3} mb="md">Data Table</Title>
					<DataTable
						data={sampleTableData}
						columns={tableColumns}
						loading={loading}
						selection={{
							selectedRowKeys: selectedRows,
							onChange: setSelectedRows,
						}}
						sorting={{
							sortBy,
							sortOrder,
							onChange: setSortBy,
						}}
						filtering={{
							filters,
							onChange: setFilters,
						}}
						pagination={{
							current: 1,
							pageSize: 10,
							total: sampleTableData.length,
							onChange: (page, pageSize) => console.log('Pagination:', { page, pageSize }),
						}}
						exportable
						onExport={handleExport}
						responsive
						stickyHeader
					/>
				</Card>

				{/* Loading States */}
				<Card withBorder>
					<Title order={3} mb="md">Loading States</Title>
					<ResponsiveGrid columns={{ base: 1, sm: 2, md: 4 }}>
						<div>
							<Text size="sm" mb="xs">Skeleton</Text>
							<LoadingStates type="skeleton" rows={3} />
						</div>
						<div>
							<Text size="sm" mb="xs">Spinner</Text>
							<LoadingStates type="spinner" text="Loading..." />
						</div>
						<div>
							<Text size="sm" mb="xs">Progress</Text>
							<LoadingStates type="progress" progress={65} text="Processing..." />
						</div>
						<div>
							<Text size="sm" mb="xs">Dots</Text>
							<LoadingStates type="dots" />
						</div>
					</ResponsiveGrid>
				</Card>

				{/* Error Boundary Demo */}
				<Card withBorder>
					<Title order={3} mb="md">Error Boundary</Title>
					<ErrorBoundary level="component" showDetails>
						<Button
							onClick={() =>
							{
								throw new Error('Demo error for testing error boundary');
							}}
							color="red"
							variant="outline"
						>
							Trigger Error
						</Button>
					</ErrorBoundary>
				</Card>

				{/* Responsive Visibility */}
				<Card withBorder>
					<Title order={3} mb="md">Responsive Visibility</Title>
					<Stack gap="sm">
						<BreakpointVisibility visibleFrom="md">
							<Alert color="blue" icon={<IconInfoCircle size={16} />}>
								This alert is only visible on medium screens and larger
							</Alert>
						</BreakpointVisibility>
						<BreakpointVisibility hiddenFrom="md">
							<Alert color="orange" icon={<IconBell size={16} />}>
								This alert is only visible on small screens
							</Alert>
						</BreakpointVisibility>
						<BreakpointVisibility largerThan="sm" smallerThan="lg">
							<Alert color="green" icon={<IconCheck size={16} />}>
								This alert is only visible on medium screens (between sm and lg)
							</Alert>
						</BreakpointVisibility>
					</Stack>
				</Card>

				{/* Print-only content */}
				<PrintableComponent printOnly>
					<Card withBorder>
						<Title order={3}>Print-Only Content</Title>
						<Text>This content will only appear when printing the page.</Text>
					</Card>
				</PrintableComponent>

				{/* No-print content */}
				<PrintableComponent noPrint>
					<Card withBorder>
						<Title order={3}>Interactive Controls (No Print)</Title>
						<Text mb="md">These controls won't appear in print version.</Text>
						<Group>
							<Button leftSection={<IconRefresh size={16} />} onClick={handleRefresh} loading={loading}>
								Refresh Data
							</Button>
							<Menu>
								<Menu.Target>
									<Button variant="outline" rightSection={<IconSettings size={16} />}>
										Actions
									</Button>
								</Menu.Target>
								<Menu.Dropdown>
									<Menu.Item leftSection={<IconDownload size={16} />}>Export</Menu.Item>
									<Menu.Item leftSection={<IconSettings size={16} />}>Settings</Menu.Item>
								</Menu.Dropdown>
							</Menu>
						</Group>
					</Card>
				</PrintableComponent>
			</Stack>

			{/* Settings Modal */}
			<Modal opened={opened} onClose={close} title="Demo Settings" size="md">
				<Stack gap="md">
					<TextInput label="Sample Input" placeholder="Enter some text" />
					<Select
						label="Sample Select"
						placeholder="Choose an option"
						data={[
							{ value: 'option1', label: 'Option 1' },
							{ value: 'option2', label: 'Option 2' },
							{ value: 'option3', label: 'Option 3' },
						]}
					/>
					<Switch label="Enable notifications" />
					<div>
						<Text size="sm" mb="xs">Sample Slider</Text>
						<Slider defaultValue={50} />
					</div>
					<Group justify="flex-end">
						<Button variant="outline" onClick={close}>Cancel</Button>
						<Button onClick={close}>Save</Button>
					</Group>
				</Stack>
			</Modal>
		</ResponsiveContainer>
	);
}

export default UIDemo;
