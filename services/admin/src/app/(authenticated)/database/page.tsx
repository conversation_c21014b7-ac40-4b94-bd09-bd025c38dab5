'use client';

import { Container, Title, Tabs } from '@mantine/core';

import BackupManager from '@/components/Database/BackupManager';
import CapacityPlanner from '@/components/Database/CapacityPlanner';
import DatabaseAlerts from '@/components/Database/DatabaseAlerts';
import DatabaseMaintenanceTools from '@/components/Database/DatabaseMaintenanceTools';
import DatabaseStatusDashboard from '@/components/Database/DatabaseStatusDashboard';
import DataIntegrityManager from '@/components/Database/DataIntegrityManager';
import IndexManager from '@/components/Database/IndexManager';
import MigrationManager from '@/components/Database/MigrationManager';

import SecurityAuditor from '@/components/Database/SecurityAuditor';

function DatabasePage()
{
	return (
		<Container size="xl" py="md">
			<Title order={1} mb="lg">Database Operations & Maintenance</Title>

			<Tabs defaultValue="status" variant="outline">
				<Tabs.List>
					<Tabs.Tab value="status">Status Dashboard</Tabs.Tab>
					<Tabs.Tab value="maintenance">Maintenance</Tabs.Tab>
					<Tabs.Tab value="integrity">Data Integrity</Tabs.Tab>
					<Tabs.Tab value="backups">Backups & Restore</Tabs.Tab>

					<Tabs.Tab value="indexes">Index Management</Tabs.Tab>
					<Tabs.Tab value="migrations">Schema Migrations</Tabs.Tab>
					<Tabs.Tab value="alerts">Alerts</Tabs.Tab>
					<Tabs.Tab value="capacity">Capacity Planning</Tabs.Tab>
					<Tabs.Tab value="security">Security Audit</Tabs.Tab>
				</Tabs.List>

				<Tabs.Panel value="status" pt="md">
					<DatabaseStatusDashboard />
				</Tabs.Panel>

				<Tabs.Panel value="maintenance" pt="md">
					<DatabaseMaintenanceTools />
				</Tabs.Panel>

				<Tabs.Panel value="integrity" pt="md">
					<DataIntegrityManager />
				</Tabs.Panel>

				<Tabs.Panel value="backups" pt="md">
					<BackupManager />
				</Tabs.Panel>



				<Tabs.Panel value="indexes" pt="md">
					<IndexManager />
				</Tabs.Panel>

				<Tabs.Panel value="migrations" pt="md">
					<MigrationManager />
				</Tabs.Panel>

				<Tabs.Panel value="alerts" pt="md">
					<DatabaseAlerts />
				</Tabs.Panel>

				<Tabs.Panel value="capacity" pt="md">
					<CapacityPlanner />
				</Tabs.Panel>

				<Tabs.Panel value="security" pt="md">
					<SecurityAuditor />
				</Tabs.Panel>
			</Tabs>
		</Container>
	);
}

export default DatabasePage;
