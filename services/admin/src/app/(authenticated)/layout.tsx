'use client';

import { useEffect } from 'react';

import AppShellLayout from '@/components/Layout/AppShell';
import { AccessibilityProvider } from '@/components/UI/AccessibilityProvider/AccessibilityProvider';
// import initializeRealtimeMonitoring from '@/lib/realtime/server';

type AuthenticatedLayoutProps =
{
	children: React.ReactNode;
};

function AuthenticatedLayout({ children }: AuthenticatedLayoutProps)
{
	useEffect(() =>
	{
		// Initialize real-time monitoring on client side
		// Note: In a real implementation, this would be done on the server side
		// This is a placeholder for client-side initialization
		if (process.env.NODE_ENV === 'development')
		{
			// Only log in development for debugging
			// eslint-disable-next-line no-console
			console.log('Initializing client-side real-time connections...');
		}
	}, []);

	return (
		<AccessibilityProvider>
			<AppShellLayout>
				{children}
			</AppShellLayout>
		</AccessibilityProvider>
	);
}

export default AuthenticatedLayout;
