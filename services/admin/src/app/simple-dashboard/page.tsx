'use client';

import {
	Container,
	Title,
	Text,
	Paper,
	Grid,
	Badge,
	Button,
	TextInput,
	PasswordInput,
	Stack,
	Alert,
} from '@mantine/core';
import {
	IconLogin,
	IconServer,
	IconDatabase,
	IconCheck,
	IconX,
} from '@tabler/icons-react';
import { useState, useEffect } from 'react';

type HealthStatusType =
{
	services?: Array<{ name: string; healthy: boolean; url: string }>;
	databases?: { mysql?: boolean; cassandra?: boolean };
	overall?: boolean;
};

function SimpleDashboard()
{
	const [isLoggedIn, setIsLoggedIn] = useState(false);
	const [username, setUsername] = useState('');
	const [password, setPassword] = useState('');
	const [loginError, setLoginError] = useState('');
	const [health, setHealth] = useState<HealthStatusType>({});

	// Simple login function
	const handleLogin = async () =>
	{
		if (username === 'admin' && password === 'admin123')
		{
			setIsLoggedIn(true);
			setLoginError('');
			localStorage.setItem('admin-logged-in', 'true');
		}
		else
		{
			setLoginError('Invalid credentials. Try admin/admin123');
		}
	};

	// Check if already logged in
	useEffect(() =>
	{
		if (localStorage.getItem('admin-logged-in') === 'true')
		{
			setIsLoggedIn(true);
		}
	}, []);

	// Fetch health status (optional - works even if services are down)
	useEffect(() =>
	{
		if (isLoggedIn)
		{
			fetch('/api/simple-health')
				.then(res => res.json())
				.then(data => setHealth(data))
				.catch(() => setHealth({ overall: false }));
		}
	}, [isLoggedIn]);

	const handleLogout = () =>
	{
		setIsLoggedIn(false);
		localStorage.removeItem('admin-logged-in');
	};

	if (!isLoggedIn)
	{
		return (
			<Container size="sm" py="xl">
				<Paper withBorder shadow="md" p={30} mt={30} radius="md">
					<Title ta="center" mb="md">
						Admin Panel Login
					</Title>

					<Stack>
						<TextInput
							label="Username"
							placeholder="admin"
							value={username}
							onChange={e => setUsername(e.target.value)}
						/>

						<PasswordInput
							label="Password"
							placeholder="admin123"
							value={password}
							onChange={e => setPassword(e.target.value)}
						/>

						{loginError && (
							<Alert color="red" icon={<IconX size={16} />}>
								{loginError}
							</Alert>
						)}

						<Button
							fullWidth
							leftSection={<IconLogin size={16} />}
							onClick={handleLogin}
						>
							Sign In
						</Button>

						<Text size="sm" c="dimmed" ta="center">
							Default credentials: admin / admin123
						</Text>
					</Stack>
				</Paper>
			</Container>
		);
	}

	return (
		<Container size="xl" py="md">
			<div style={{
				display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '2rem',
			}}
			>
				<Title order={1}>
					Simple Admin Dashboard
				</Title>
				<Button variant="outline" onClick={handleLogout}>
					Logout
				</Button>
			</div>

			<Grid>
				<Grid.Col span={{ base: 12, md: 6 }}>
					<Paper withBorder p="md" radius="md">
						<Text size="sm" c="dimmed" mb="xs">
							Admin Panel Status
						</Text>
						<Badge color="green" variant="light" leftSection={<IconCheck size={12} />}>
							Running Successfully
						</Badge>
						<Text size="sm" mt="xs">
							This admin panel is working independently of other services.
						</Text>
					</Paper>
				</Grid.Col>

				<Grid.Col span={{ base: 12, md: 6 }}>
					<Paper withBorder p="md" radius="md">
						<Text size="sm" c="dimmed" mb="xs">
							System Overview
						</Text>
						<Text size="xl" fw={700}>
							{health.overall ? 'All Systems OK' : 'Some Services Offline'}
						</Text>
						<Text size="sm" c="dimmed">
							{health.overall ? 'All connected services are healthy' : 'Admin panel works independently'}
						</Text>
					</Paper>
				</Grid.Col>
			</Grid>

			{/* Service Status */}
			<Paper withBorder p="md" mt="xl">
				<Title order={2} mb="md" leftSection={<IconServer size={20} />}>
					Service Status
				</Title>

				{health.services ? (
					<Grid>
						{health.services.map(service => (
							<Grid.Col span={{ base: 12, sm: 6, md: 4 }} key={service.name}>
								<Paper withBorder p="sm" radius="sm">
									<div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
										<Text size="sm" fw={500}>{service.name}</Text>
										<Badge
											color={service.healthy ? 'green' : 'red'}
											variant="light"
											leftSection={service.healthy ? <IconCheck size={12} /> : <IconX size={12} />}
										>
											{service.healthy ? 'Online' : 'Offline'}
										</Badge>
									</div>
									<Text size="xs" c="dimmed" mt="xs">{service.url}</Text>
								</Paper>
							</Grid.Col>
						))}
					</Grid>
				) : (
					<Alert color="blue">
						Service health checking is optional. The admin panel works without external services.
					</Alert>
				)}
			</Paper>

			{/* Database Status */}
			<Paper withBorder p="md" mt="xl">
				<Title order={2} mb="md" leftSection={<IconDatabase size={20} />}>
					Database Status
				</Title>

				{health.databases ? (
					<Grid>
						<Grid.Col span={{ base: 12, sm: 6 }}>
							<Paper withBorder p="sm" radius="sm">
								<div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
									<Text size="sm" fw={500}>MariaDB</Text>
									<Badge
										color={health.databases.mysql ? 'green' : 'red'}
										variant="light"
										leftSection={health.databases.mysql ? <IconCheck size={12} /> : <IconX size={12} />}
									>
										{health.databases.mysql ? 'Connected' : 'Disconnected'}
									</Badge>
								</div>
							</Paper>
						</Grid.Col>

						<Grid.Col span={{ base: 12, sm: 6 }}>
							<Paper withBorder p="sm" radius="sm">
								<div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
									<Text size="sm" fw={500}>ScyllaDB</Text>
									<Badge
										color={health.databases.cassandra ? 'green' : 'red'}
										variant="light"
										leftSection={health.databases.cassandra ? <IconCheck size={12} /> : <IconX size={12} />}
									>
										{health.databases.cassandra ? 'Connected' : 'Disconnected'}
									</Badge>
								</div>
							</Paper>
						</Grid.Col>
					</Grid>
				) : (
					<Alert color="blue">
						Database connectivity is optional. Configure environment
						{' '}
						variables to enable database monitoring.
					</Alert>
				)}
			</Paper>

			{/* Quick Actions */}
			<Paper withBorder p="md" mt="xl">
				<Title order={2} mb="md">
					Quick Actions
				</Title>

				<Grid>
					<Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
						<Button fullWidth variant="light">
							View Domains
						</Button>
					</Grid.Col>

					<Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
						<Button fullWidth variant="light">
							Crawl Jobs
						</Button>
					</Grid.Col>

					<Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
						<Button fullWidth variant="light">
							System Logs
						</Button>
					</Grid.Col>

					<Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
						<Button fullWidth variant="light">
							Settings
						</Button>
					</Grid.Col>
				</Grid>
			</Paper>
		</Container>
	);
}

export default SimpleDashboard;
