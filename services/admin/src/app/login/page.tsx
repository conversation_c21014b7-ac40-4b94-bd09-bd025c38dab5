'use client';

import {
	Paper,
	TextInput,
	PasswordInput,
	Button,
	Title,
	Text,
	Container,
	Alert,
	Stack,
	Box,
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { IconAlertCircle, IconInfoCircle } from '@tabler/icons-react';
import { useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';

interface LoginFormType
{
	username: string;
	password: string;
}

function LoginPage()
{
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [info, setInfo] = useState<string | null>(null);
	const router = useRouter();

	const form = useForm<LoginFormType>({
		initialValues: {
			username: '',
			password: '',
		},
		validate: {
			username: value => (!value ? 'Username is required' : null),
			password: value => (!value ? 'Password is required' : null),
		},
	});

	useEffect(() =>
	{
		// Check if user is already authenticated
		const checkAuth = async () =>
		{
			try
			{
				const response = await fetch('/api/auth/session');
				if (response.ok)
				{
					router.push('/dashboard');
				}
			}
			catch (error)
			{
				// User is not authenticated, stay on login page
			}
		};

		checkAuth();
	}, [router]);

	const handleSubmit = async (values: LoginFormType) =>
	{
		setLoading(true);
		setError(null);
		setInfo(null);

		try
		{
			const response = await fetch('/api/auth/login', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify(values),
				credentials: 'include',
			});

			const data = await response.json();

			if (response.ok)
			{
				setInfo('Login successful! Redirecting...');
				setTimeout(() =>
				{
					router.push('/dashboard');
				}, 1000);
			}
			else
			{
				setError(data.message || 'Login failed');
			}
		}
		catch (err)
		{
			setError('An error occurred during login. Please try again.');
		}
		finally
		{
			setLoading(false);
		}
	};

	return (
		<Container size={420} my={40}>
			<Title ta="center" mb="md">
				Admin Panel
			</Title>
			<Text c="dimmed" size="sm" ta="center" mb="xl">
				Domain Ranking System Administration
			</Text>

			<Paper withBorder shadow="md" p={30} mt={30} radius="md">
				<form onSubmit={form.onSubmit(handleSubmit)}>
					<Stack>
						{error && (
							<Alert icon={<IconAlertCircle size="1rem" />} color="red">
								{error}
							</Alert>
						)}

						{info && (
							<Alert icon={<IconInfoCircle size="1rem" />} color="green">
								{info}
							</Alert>
						)}

						<TextInput
							label="Username"
							placeholder="Enter your username"
							required
							disabled={loading}
							{...form.getInputProps('username')}
						/>

						<PasswordInput
							label="Password"
							placeholder="Enter your password"
							required
							disabled={loading}
							{...form.getInputProps('password')}
						/>

						<Button type="submit" fullWidth loading={loading}>
							Sign in
						</Button>
					</Stack>
				</form>

				<Box mt="md">
					<Text size="sm" c="dimmed" ta="center">
						Default credentials for testing:
					</Text>
					<Text size="xs" c="dimmed" ta="center">
						superadmin / admin / viewer : AdminPass123!
					</Text>
				</Box>
			</Paper>
		</Container>
	);
}

export default LoginPage;
