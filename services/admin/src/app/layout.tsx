import { ColorSchemeScript, MantineProvider, createTheme } from '@mantine/core';
import { ModalsProvider } from '@mantine/modals';
import { Notifications } from '@mantine/notifications';

import type { Metadata } from 'next';

import '@mantine/core/styles.css';
import '@mantine/notifications/styles.css';
import '@mantine/dates/styles.css';
import '@mantine/spotlight/styles.css';

const theme = createTheme({
	primaryColor: 'blue',
});

export const metadata: Metadata = {
	title: 'Domain Ranking System - Admin Panel',
	description: 'Administrative interface for managing the Domain Ranking System',
	robots: 'noindex, nofollow',
};

interface RootLayoutProps
{
	children: React.ReactNode;
}

function RootLayout({ children }: RootLayoutProps)
{
	return (
		<html lang="en" suppressHydrationWarning>
			<head>
				<ColorSchemeScript defaultColorScheme="light" />
			</head>
			<body suppressHydrationWarning>
				<MantineProvider theme={theme} defaultColorScheme="light" forceColorScheme="light">
					<ModalsProvider>
						<Notifications />
						{children}
					</ModalsProvider>
				</MantineProvider>
			</body>
		</html>
	);
}

export default RootLayout;
