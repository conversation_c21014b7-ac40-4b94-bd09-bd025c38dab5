import { NextResponse } from 'next/server';
import { apiLogger } from '@/lib/logger';

import { logUserActivity } from '@/lib/auth/audit';
import { getSession } from '@/lib/auth/session';
import db from '@/lib/database/client';

import type { NextRequest } from 'next/server';


export async function GET(request: NextRequest)
{
	try
	{
		const session = await getSession();
		if (!session || !Array.isArray(session.permissions) || !session.permissions.includes('users.view'))
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
		}

		const { searchParams } = new URL(request.url);
		const page = parseInt(searchParams.get('page') || '1');
		const limit = parseInt(searchParams.get('limit') || '50');
		const userId = searchParams.get('userId');
		const action = searchParams.get('action');
		const resource = searchParams.get('resource');
		const startDate = searchParams.get('startDate');
		const endDate = searchParams.get('endDate');
		const severity = searchParams.get('severity');

		const offset = (page - 1) * limit;

		// Build query conditions
		const conditions: string[] = [];
		const params: any[] = [];

		if (userId)
		{
			conditions.push('user_id = ?');
			params.push(userId);
		}

		if (action)
		{
			conditions.push('action LIKE ?');
			params.push(`%${action}%`);
		}

		if (resource)
		{
			conditions.push('resource = ?');
			params.push(resource);
		}

		if (startDate && endDate)
		{
			conditions.push('timestamp BETWEEN ? AND ?');
			params.push(new Date(startDate), new Date(endDate));
		}

		if (severity)
		{
			conditions.push('severity = ?');
			params.push(severity);
		}

		const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

		// Get total count
		const countQuery = `
      SELECT COUNT(*) as total
      FROM audit_logs
      ${whereClause}
    `;
		const countResult = await db.mariadb.query(countQuery, params);
		const total = countResult[0]?.total || 0;

		// Get audit logs with pagination
		const auditQuery = `
      SELECT
        al.id, al.user_id, al.username, al.action, al.resource, al.resource_id,
        al.old_values, al.new_values, al.timestamp, al.ip_address, al.user_agent,
        al.success, al.error_message, al.severity
      FROM audit_logs al
      ${whereClause}
      ORDER BY al.timestamp DESC
      LIMIT ? OFFSET ?
    `;

		const auditLogs = await db.mariadb.query(auditQuery, [...params, limit, offset]);

		// Transform data
		const transformedLogs = auditLogs.map((log: any) => ({
			id: log.id,
			userId: log.user_id,
			username: log.username,
			action: log.action,
			resource: log.resource,
			resourceId: log.resource_id,
			oldValues: log.old_values ? JSON.parse(log.old_values) : null,
			newValues: log.new_values ? JSON.parse(log.new_values) : null,
			timestamp: new Date(log.timestamp),
			ipAddress: log.ip_address,
			userAgent: log.user_agent,
			success: Boolean(log.success),
			errorMessage: log.error_message,
			severity: log.severity,
		}));

		// Resolve actor id by username
		let actorId = session.username ?? 'unknown';
		try
		{
			const idRows = await db.mariadb.query<{ id: string }>('SELECT id FROM admin_users WHERE username = ? LIMIT 1', [session.username ?? '']);
			if (idRows[0]?.id)
			{
				actorId = idRows[0].id;
			}
		}
		catch {}

		await logUserActivity(actorId, 'users.audit.view', 'audit_logs', true, {
			page,
			limit,
			filters: {
				userId, action, resource, startDate, endDate, severity,
			},
			total,
		});

		return NextResponse.json({
			auditLogs: transformedLogs,
			pagination: {
				page,
				limit,
				total,
				pages: Math.ceil(total / limit),
			},
		});
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error fetching audit logs:');
		return NextResponse.json(
			{ error: 'Failed to fetch audit logs' },
			{ status: 500 },
		);
	}
}

export async function POST(request: NextRequest)
{
	try
	{
		const session = await getSession();
		if (!session || !Array.isArray(session.permissions) || !session.permissions.includes('users.view'))
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
		}

		const body = await request.json();
		const { exportFormat, filters } = body;

		// Build query with filters
		const conditions: string[] = [];
		const params: any[] = [];

		if (filters.userId)
		{
			conditions.push('user_id = ?');
			params.push(filters.userId);
		}

		if (filters.action)
		{
			conditions.push('action LIKE ?');
			params.push(`%${filters.action}%`);
		}

		if (filters.resource)
		{
			conditions.push('resource = ?');
			params.push(filters.resource);
		}

		if (filters.startDate && filters.endDate)
		{
			conditions.push('timestamp BETWEEN ? AND ?');
			params.push(new Date(filters.startDate), new Date(filters.endDate));
		}

		if (filters.severity)
		{
			conditions.push('severity = ?');
			params.push(filters.severity);
		}

		const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

		// Get audit logs for export (limit to 10000 records)
		const exportQuery = `
      SELECT
        username, action, resource, resource_id, timestamp,
        ip_address, success, severity, error_message
      FROM audit_logs
      ${whereClause}
      ORDER BY timestamp DESC
      LIMIT 10000
    `;

		const auditLogs = await db.mariadb.query(exportQuery, params);

		if (exportFormat === 'csv')
		{
			// Generate CSV
			const headers = ['Username', 'Action', 'Resource', 'Resource ID', 'Timestamp', 'IP Address', 'Success', 'Severity', 'Error Message'];
			const csvRows = [headers.join(',')];

			auditLogs.forEach((log: any) =>
			{
				const row = [
					log.username || '',
					log.action || '',
					log.resource || '',
					log.resource_id || '',
					log.timestamp ? new Date(log.timestamp).toISOString() : '',
					log.ip_address || '',
					log.success ? 'Yes' : 'No',
					log.severity || '',
					(log.error_message || '').replace(/"/g, '""'), // Escape quotes
				];
				csvRows.push(row.map(field => `"${field}"`).join(','));
			});

			const csvContent = csvRows.join('\n');

			// Resolve actor id by username
			let actorId = session.username ?? 'unknown';
			try
			{
				const idRows = await db.mariadb.query<{ id: string }>('SELECT id FROM admin_users WHERE username = ? LIMIT 1', [session.username ?? '']);
				if (idRows[0]?.id)
				{
					actorId = idRows[0].id;
				}
			}
			catch {}

			await logUserActivity(actorId, 'users.audit.export', 'audit_logs', true, {
				format: 'csv',
				recordCount: auditLogs.length,
				filters,
			});

			return new NextResponse(csvContent, {
				headers: {
					'Content-Type': 'text/csv',
					'Content-Disposition': `attachment; filename="audit-logs-${new Date().toISOString().split('T')[0]}.csv"`,
				},
			});
		}
		if (exportFormat === 'json')
		{
			const jsonData = {
				exportedAt: new Date().toISOString(),
				filters,
				recordCount: auditLogs.length,
				auditLogs: auditLogs.map((log: any) => ({
					username: log.username,
					action: log.action,
					resource: log.resource,
					resourceId: log.resource_id,
					timestamp: log.timestamp,
					ipAddress: log.ip_address,
					success: Boolean(log.success),
					severity: log.severity,
					errorMessage: log.error_message,
				})),
			};

			// Resolve actor id by username (reuse same logic if not already resolved)
			let actorIdJson = session.username ?? 'unknown';
			try
			{
				const idRows = await db.mariadb.query<{ id: string }>('SELECT id FROM admin_users WHERE username = ? LIMIT 1', [session.username ?? '']);
				if (idRows[0]?.id)
				{
					actorIdJson = idRows[0].id;
				}
			}
			catch {}

			await logUserActivity(actorIdJson, 'users.audit.export', 'audit_logs', true, {
				format: 'json',
				recordCount: auditLogs.length,
				filters,
			});

			return NextResponse.json(jsonData, {
				headers: {
					'Content-Disposition': `attachment; filename="audit-logs-${new Date().toISOString().split('T')[0]}.json"`,
				},
			});
		}

		return NextResponse.json(
			{ error: 'Invalid export format' },
			{ status: 400 },
		);
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error exporting audit logs:');

		return NextResponse.json(
			{ error: 'Failed to export audit logs' },
			{ status: 500 },
		);
	}
}
