import { NextResponse } from 'next/server';
import { randomUUID } from 'node:crypto';
import { apiLogger } from '@/lib/logger';

import { logUserActivity, logAuditEvent } from '@/lib/auth/audit';
import { getSession } from '@/lib/auth/session';
import db from '@/lib/database/client';
import { generateSecurePassword, hashPassword, validatePassword } from '@/utils/passwordValidation';

import type { NextRequest } from 'next/server';
import type {
	AdminUserType,
	CreateUserRequestType,
	UpdateUserRequestType,
	UserAnalyticsType,
	UserRoleType,
	PermissionType,
} from '@/types/auth';

type CountRowType =
{
	total: number;
};

type AdminUserRowType =
{
	id: string;
	username: string;
	role: string;
	permissions: string;
	created_at: string | Date;
	last_login: string | Date | null;
	is_active: number | boolean;
	password_last_changed: string | Date;
	must_change_password: number | boolean;
	email: string | null;
	full_name: string | null;
	login_attempts: number;
	locked_until: string | Date | null;
	session_count: number;
};

type CreateUserResponseType =
{
	success: boolean;
	userId: string;
	message: string;
	generatedPassword?: string;
	warning?: string;
};

function isUserRoleType(value: unknown): value is UserRoleType
{
	return value === 'super_admin' || value === 'admin' || value === 'viewer';
}

function parsePermissions(raw: string | null | undefined): PermissionType[]
{
	try
	{
		const parsed = raw ? JSON.parse(raw) : [];
		if (Array.isArray(parsed) && parsed.every(x => typeof x === 'string'))
		{
			return parsed as PermissionType[];
		}

		return ([]);
	}
	catch
	{
		return ([]);
	}
}

export async function GET(request: NextRequest)
{
	try
	{
		const session = await getSession();
		if (!session || !Array.isArray(session.permissions) || !session.permissions.includes('users.view'))
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
		}

		const { searchParams } = new URL(request.url);
		const page = parseInt(searchParams.get('page') || '1');
		const limit = parseInt(searchParams.get('limit') || '20');
		const search = searchParams.get('search') || '';
		const role = searchParams.get('role') || '';
		const status = searchParams.get('status') || '';
		const sortByParam = searchParams.get('sortBy') || 'createdAt';
		const sortOrderParam = searchParams.get('sortOrder') || 'desc';

		const sortByMap: Record<string, string> =
		{
			createdAt: 'created_at',
			lastLogin: 'last_login',
			username: 'username',
			role: 'role',
		};

		const sortBy = sortByMap[sortByParam] ?? 'created_at';
		const sortOrder = sortOrderParam.toLowerCase() === 'asc' ? 'ASC' : 'DESC';

		const offset = (page - 1) * limit;

		// Build query conditions
		const conditions: string[] = [];
		const params: Array<string | number | Date | null> = [];

		if (search)
		{
			conditions.push('(username LIKE ? OR email LIKE ? OR full_name LIKE ?)');
			params.push(`%${search}%`, `%${search}%`, `%${search}%`);
		}

		if (role)
		{
			conditions.push('role = ?');
			params.push(role);
		}

		if (status === 'active')
		{
			conditions.push('is_active = 1 AND (locked_until IS NULL OR locked_until < NOW())');
		}
		else if (status === 'inactive')
		{
			conditions.push('is_active = 0');
		}
		else if (status === 'locked')
		{
			conditions.push('locked_until > NOW()');
		}

		const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

		// Get total count
		const countQuery = `
      SELECT COUNT(*) as total
      FROM admin_users
      ${whereClause}
    `;
		const countResult = await db.mariadb.query<CountRowType>(countQuery, params);
		const total = countResult[0]?.total || 0;

		// Get users with pagination
		const usersQuery = `
      SELECT
        id, username, role, permissions, created_at, last_login,
        is_active, password_last_changed, must_change_password,
        email, full_name, login_attempts, locked_until,
        (SELECT COUNT(*) FROM admin_sessions WHERE user_id = admin_users.id AND is_active = 1) as session_count
      FROM admin_users
      ${whereClause}
      ORDER BY ${sortBy} ${sortOrder}
      LIMIT ? OFFSET ?
    `;
		const users = await db.mariadb.query<AdminUserRowType>(usersQuery, [...params, limit, offset]);

		// Transform
		const transformedUsers: AdminUserType[] = users.map((user) => ({
			id: user.id,
			username: user.username,
			role: isUserRoleType(user.role) ? user.role : 'viewer',
			permissions: parsePermissions(user.permissions),
			createdAt: new Date(user.created_at as string),
			lastLogin: user.last_login ? new Date(user.last_login as string) : null,
			isActive: user.is_active === 1 || user.is_active === true,
			sessionCount: user.session_count || 0,
			passwordLastChanged: new Date(user.password_last_changed as string),
			mustChangePassword: user.must_change_password === 1 || user.must_change_password === true,
			email: user.email ?? undefined,
			fullName: user.full_name ?? undefined,
			loginAttempts: user.login_attempts || 0,
			lockedUntil: user.locked_until ? new Date(user.locked_until as string) : null,
			passwordHistory: [] as string[],
		}));

		// Resolve actor id by username
		let actorId = session.username ?? 'unknown';
		try
		{
			const idRows = await db.mariadb.query<{ id: string }>('SELECT id FROM admin_users WHERE username = ? LIMIT 1', [session.username]);
			if (idRows[0]?.id)
			{
				actorId = idRows[0].id;
			}
		}
		catch
		{}

		await logUserActivity(actorId, 'users.list', 'users', true, {
			page,
			limit,
			search,
			role,
			status,
			total,
		});

		return NextResponse.json({
			users: transformedUsers,
			pagination: {
				page,
				limit,
				total,
				pages: Math.ceil(total / limit),
			},
		});
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error fetching users:');
		return NextResponse.json(
			{ error: 'Failed to fetch users' },
			{ status: 500 },
		);
	}
}

export async function POST(request: NextRequest)
{
	try
	{
		const session = await getSession();
		if (!session || !Array.isArray(session.permissions) || !session.permissions.includes('users.create'))
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
		}

		const body: CreateUserRequestType = await request.json();
		const {
			username,
			password,
			role,
			permissions,
			email,
			fullName,
			mustChangePassword,
			generatePassword,
		} = body;

		// Validate required fields
		if (!username || !role) {
			return NextResponse.json(
				{ error: 'Username and role are required' },
				{ status: 400 },
			);
		}

		// Check if username already exists
		const existingUser = await db.mariadb.query(
			'SELECT id FROM admin_users WHERE username = ?',
			[username],
		);

		if (existingUser.length > 0) {
			return NextResponse.json(
				{ error: 'Username already exists' },
				{ status: 409 },
			);
		}

		// Generate or validate password
		let finalPassword: string;
		let generatedPassword: string | null = null;

		if (generatePassword || !password) {
			generatedPassword = generateSecurePassword();
			finalPassword = generatedPassword;
		} else {
			const passwordValidation = validatePassword(password);
			if (!passwordValidation.isValid) {
				return NextResponse.json(
					{ error: 'Password does not meet policy requirements', details: passwordValidation.errors },
					{ status: 400 },
				);
			}
			finalPassword = password as string;
		}

		const hashedPassword = await hashPassword(finalPassword);

		// Create user
		const userId = randomUUID();
		const now = new Date();

		// Resolve actor id by username (creator)
		let actorIdCreate = session.username ?? 'unknown';
		try {
			const idRowsCreate = await db.mariadb.query<{ id: string }>('SELECT id FROM admin_users WHERE username = ? LIMIT 1', [session.username]);
			if (idRowsCreate[0]?.id) {
				actorIdCreate = idRowsCreate[0].id;
			}
		} catch {}

		await db.mariadb.query(`
      INSERT INTO admin_users (
        id, username, password_hash, role, permissions, created_at,
        password_last_changed, must_change_password, email, full_name,
        is_active, login_attempts, created_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
			userId,
			username,
			hashedPassword,
			role,
			JSON.stringify(permissions),
			now,
			now,
			mustChangePassword || generatePassword ? 1 : 0,
			email || null,
			fullName || null,
			1,
			0,
			actorIdCreate,
		]);

		await logAuditEvent(actorIdCreate, 'user.create', 'admin_users', userId, null, {
			username,
			role,
			permissions,
			email,
			fullName,
		});

		await logUserActivity(actorIdCreate, 'users.create', 'users', true, {
			createdUserId: userId,
			username,
			role,
		});

		const response: CreateUserResponseType =
		{
			success: true,
			userId,
			message: 'User created successfully',
		};

		if (generatedPassword) {
			response.generatedPassword = generatedPassword;
			response.warning = 'This is the only time the generated password will be shown. Please save it securely.';
		}

		return NextResponse.json(response);
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error creating user:');
		return NextResponse.json(
			{ error: 'Failed to create user' },
			{ status: 500 },
		);
	}
}
