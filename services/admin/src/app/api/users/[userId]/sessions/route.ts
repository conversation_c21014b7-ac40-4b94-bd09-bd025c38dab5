import { NextResponse } from 'next/server';
import { api<PERSON>ogger } from '@/lib/logger';

import { logUserActivity, logAuditEvent } from '@/lib/auth/audit';
import { getSession } from '@/lib/auth/session';
import db from '@/lib/database/client';

import type { NextRequest } from 'next/server';


export async function GET(
	request: NextRequest,
	{ params }: { params: { userId: string } },
)
{
	try
	{
		const session = await getSession();
		if (!session || !Array.isArray(session.permissions) || !session.permissions.includes('users.view'))
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
		}

		const { userId } = params;

		const sessionsQuery = `
      SELECT
        session_id, created_at, last_activity, expires_at,
        ip_address, user_agent, is_active
      FROM admin_sessions
      WHERE user_id = ?
      ORDER BY created_at DESC
      LIMIT 100
    `;

		const sessions = await db.mariadb.query(sessionsQuery, [userId]);

		const transformedSessions = sessions.map((s: any) => ({
			sessionId: s.session_id,
			createdAt: new Date(s.created_at),
			lastActivity: new Date(s.last_activity),
			expiresAt: new Date(s.expires_at),
			ipAddress: s.ip_address,
			userAgent: s.user_agent,
			isActive: Boolean(s.is_active),
			deviceInfo: parseUserAgent(s.user_agent),
		}));

		// Resolve actor id by username
		let actorId = session.username ?? 'unknown';
		try
		{
			const idRows = await db.mariadb.query<{ id: string }>('SELECT id FROM admin_users WHERE username = ? LIMIT 1', [session.username ?? '']);
			if (idRows[0]?.id)
			{
				actorId = idRows[0].id;
			}
		}
		catch {}

		await logUserActivity(actorId, 'users.sessions.view', 'users', true, {
			viewedUserId: userId,
		});

		return NextResponse.json({ sessions: transformedSessions });
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error fetching user sessions:');
		return NextResponse.json(
			{ error: 'Failed to fetch user sessions' },
			{ status: 500 },
		);
	}
}

export async function DELETE(
	request: NextRequest,
	{ params }: { params: { userId: string } },
)
{
	try
	{
		const session = await getSession();
		if (!session || !Array.isArray(session.permissions) || !session.permissions.includes('users.edit'))
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
		}

		const { userId } = params;
		const { searchParams } = new URL(request.url);
		const sessionId = searchParams.get('sessionId');

		if (sessionId)
		{
			// Terminate specific session
			await db.mariadb.query(
				'UPDATE admin_sessions SET is_active = 0 WHERE session_id = ? AND user_id = ?',
				[sessionId, userId],
			);

			// Resolve actor id by username
			let actorIdOne = session.username ?? 'unknown';
			try
			{
				const idRowsOne = await db.mariadb.query<{ id: string }>('SELECT id FROM admin_users WHERE username = ? LIMIT 1', [session.username ?? '']);
				if (idRowsOne[0]?.id)
				{
					actorIdOne = idRowsOne[0].id;
				}
			}
			catch {}

			await logAuditEvent(actorIdOne, 'user.session.terminate', 'admin_sessions', sessionId, null, {
				terminatedUserId: userId,
				sessionId,
			});

			await logUserActivity(actorIdOne, 'users.sessions.terminate', 'users', true, {
				terminatedUserId: userId,
				sessionId,
			});

			return NextResponse.json({ success: true, message: 'Session terminated successfully' });
		}

		// Terminate all sessions for user
		const result = await db.mariadb.query(
			'UPDATE admin_sessions SET is_active = 0 WHERE user_id = ? AND is_active = 1',
			[userId],
		);

		// Resolve actor id
		let actorIdAll = session.username ?? 'unknown';
		try
		{
			const idRows2 = await db.mariadb.query<{ id: string }>('SELECT id FROM admin_users WHERE username = ? LIMIT 1', [session.username ?? '']);
			if (idRows2[0]?.id)
			{
				actorIdAll = idRows2[0].id;
			}
		}
		catch {}

		await logAuditEvent(actorIdAll, 'user.sessions.terminate_all', 'admin_sessions', null, null, {
			terminatedUserId: userId,
			sessionsTerminated: updateRes.rows.affectedRows || 0,
		});

		await logUserActivity(actorIdAll, 'users.sessions.terminate_all', 'users', true, {
			terminatedUserId: userId,
			sessionsTerminated: updateRes.rows.affectedRows || 0,
		});

		return NextResponse.json({
			success: true,
			message: `${updateRes.rows.affectedRows || 0} sessions terminated successfully`,
		});
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error terminating user sessions:');
		return NextResponse.json(
			{ error: 'Failed to terminate sessions' },
			{ status: 500 },
		);
	}
}

function parseUserAgent(userAgent: string)
{
	// Simple user agent parsing - in production, use a proper library
	const browser = userAgent.includes('Chrome') ? 'Chrome'
		: userAgent.includes('Firefox') ? 'Firefox'
			: userAgent.includes('Safari') ? 'Safari'
				: userAgent.includes('Edge') ? 'Edge' : 'Unknown';

	const os = userAgent.includes('Windows') ? 'Windows'
		: userAgent.includes('Mac') ? 'macOS'
			: userAgent.includes('Linux') ? 'Linux'
				: userAgent.includes('Android') ? 'Android'
					: userAgent.includes('iOS') ? 'iOS' : 'Unknown';

	const device = userAgent.includes('Mobile') ? 'Mobile'
		: userAgent.includes('Tablet') ? 'Tablet' : 'Desktop';

	return { browser, os, device };
}
