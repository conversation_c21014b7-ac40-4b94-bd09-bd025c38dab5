import { NextResponse } from 'next/server';
import { api<PERSON>ogger } from '@/lib/logger';

import { logUserActivity, logAuditEvent } from '@/lib/auth/audit';
import { getSession } from '@/lib/auth/session';
import db from '@/lib/database/client';
import { generateSecurePassword, hashPassword, validatePassword, verifyPassword } from '@/utils/passwordValidation';

import type { NextRequest } from 'next/server';


export async function POST(
	request: NextRequest,
	{ params }: { params: { userId: string } },
)
{
	try
	{
		const session = await getSession();
		if (!session || !Array.isArray(session.permissions) || !session.permissions.includes('users.edit'))
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
		}

		const { userId } = params;
		const body = await request.json();
		const { action, newPassword } = body;

		// Get current user data
		const userData = await db.mariadb.query(
			'SELECT username, password_hash FROM admin_users WHERE id = ?',
			[userId],
		);

		if (userData.length === 0)
		{
			return NextResponse.json({ error: 'User not found' }, { status: 404 });
		}

		let finalPassword: string;
		let generatedPassword: string | null = null;

		if (action === 'generate')
		{
			// Generate new password
			generatedPassword = generateSecurePassword();
			finalPassword = generatedPassword;
		}
		else if (action === 'set' && newPassword)
		{
			// Validate provided password
			const passwordValidation = validatePassword(newPassword);
			if (!passwordValidation.isValid)
			{
					return NextResponse.json(
							{ error: 'Password does not meet policy requirements', details: passwordValidation.errors },
							{ status: 400 },
					);
			}
			finalPassword = newPassword;
		}
		else
		{
			return NextResponse.json(
				{ error: 'Invalid action or missing password' },
				{ status: 400 },
			);
		}

		// Get password history to prevent reuse
		const historyResult = await db.mariadb.query(
			'SELECT password_history FROM admin_users WHERE id = ?',
			[userId],
		);

		const passwordHistory = JSON.parse(historyResult[0]?.password_history || '[]');
		const hashedNewPassword = await hashPassword(finalPassword);

		// Check against password history
		for (const oldHash of passwordHistory)
		{
			if (await verifyPassword(finalPassword, oldHash))
			{
				return NextResponse.json(
					{ error: 'Password has been used recently. Please choose a different password.' },
					{ status: 400 },
				);
			}
		}

		// Update password history
		const updatedHistory = [hashedNewPassword, ...passwordHistory].slice(0, 5); // Keep last 5 passwords

		// Update user password
		await db.mariadb.query(`
		UPDATE admin_users
		SET password_hash = ?, password_last_changed = ?, must_change_password = 1,
			password_history = ?, login_attempts = 0, locked_until = NULL
		WHERE id = ?
		`, [hashedNewPassword, new Date(), JSON.stringify(updatedHistory), userId]);

		// Terminate all existing sessions to force re-login
		await db.mariadb.query(
			'UPDATE admin_sessions SET is_active = 0 WHERE user_id = ?',
			[userId],
		);

				// Log audit event
		// Resolve actor id by username
		let actorId = session.username ?? 'unknown';
		try
		{
			const idRows = await db.mariadb.query<{ id: string }>('SELECT id FROM admin_users WHERE username = ? LIMIT 1', [session.username ?? '']);
			if (idRows[0]?.id)
			{
					actorId = idRows[0].id;
			}
		}
		catch {}

		await logAuditEvent(actorId, 'user.password.reset', 'admin_users', userId, null, {
			action,
			resetBy: session.username,
		});

		await logUserActivity(actorId, 'users.password.reset', 'users', true, {
			resetUserId: userId,
			resetUsername: userData[0].username,
			action,
		});

		const response: any = {
			success: true,
			message: 'Password updated successfully. User must log in again.',
		};

		if (generatedPassword)
		{
			response.generatedPassword = generatedPassword;
			response.warning = 'This is the only time the generated password will be shown. Please save it securely.';
		}

		return NextResponse.json(response);
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error resetting password:');

		return NextResponse.json(
			{ error: 'Failed to reset password' },
			{ status: 500 },
		);
	}
}

export async function PUT(
	request: NextRequest,
	{ params }: { params: { userId: string } },
)
{
	try
	{
		const session = await getSession();
		if (!session || !Array.isArray(session.permissions) || !session.permissions.includes('users.edit'))
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
		}

		const { userId } = params;
		const body = await request.json();
		const { unlock } = body;

		if (unlock)
		{
			// Unlock user account
			await db.mariadb.query(`
			UPDATE admin_users
			SET login_attempts = 0, locked_until = NULL
			WHERE id = ?
			`, [userId]);

			// Get username for logging
			const userData = await db.mariadb.query(
				'SELECT username FROM admin_users WHERE id = ?',
				[userId],
			);

			// Resolve actor id by username
			let actorIdUnlock = session.username ?? 'unknown';
			try
			{
				const idRowsUnlock = await db.mariadb.query<{ id: string }>('SELECT id FROM admin_users WHERE username = ? LIMIT 1', [session.username ?? '']);
				if (idRowsUnlock[0]?.id)
				{
					actorIdUnlock = idRowsUnlock[0].id;
				}
			}
			catch {}

			await logAuditEvent(actorIdUnlock, 'user.account.unlock', 'admin_users', userId, null, {
				unlockedBy: session.username,
			});

			await logUserActivity(actorIdUnlock, 'users.unlock', 'users', true, {
				unlockedUserId: userId,
				unlockedUsername: userData[0]?.username,
			});

			return NextResponse.json({
				success: true,
				message: 'User account unlocked successfully',
			});
		}

		return NextResponse.json(
				{ error: 'Invalid action' },
				{ status: 400 },
			);
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error updating user account:');

		return NextResponse.json(
				{ error: 'Failed to update user account' },
				{ status: 500 },
			);
		}
}
