import { NextResponse } from 'next/server';
import { api<PERSON>ogger } from '@/lib/logger';

import { logUserActivity, logAuditEvent } from '@/lib/auth/audit';
import { getSession } from '@/lib/auth/session';
import db from '@/lib/database/client';
import type { NextRequest } from 'next/server';
import type { UpdateUserRequestType } from '@/types/auth';

/**
 * GET /api/users/:userId
 *
 * Fetches user details by ID.
 */
export async function GET(
	request: NextRequest,
	{ params }: { params: { userId: string } },
)
{
	try
	{
		const session = await getSession();
		if (
			!session || !Array.isArray(session.permissions)
				|| !session.permissions.includes('users.view')
		)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
		}

		const { userId } = params;

		const userQuery = `
		SELECT
			id, username, role, permissions, created_at, last_login,
			is_active, password_last_changed, must_change_password,
			email, full_name, login_attempts, locked_until, created_by
		FROM admin_users
		WHERE id = ?`;

		const users = await db.mariadb.query(userQuery, [userId]);

		if (users.length === 0)
		{
			return NextResponse.json({ error: 'User not found' }, { status: 404 });
		}

		const user = users[0];

		// Get active sessions
		const sessionsQuery = `
		SELECT session_id, created_at, last_activity, expires_at, ip_address, user_agent
		FROM admin_sessions
		WHERE user_id = ? AND is_active = 1
		ORDER BY last_activity DESC`;

		const sessions = await db.mariadb.query(sessionsQuery, [userId]);

		// Get recent activity
		const activityQuery = `
		SELECT action, resource, timestamp, ip_address, success, details
		FROM user_activity
		WHERE user_id = ?
		ORDER BY timestamp DESC
		LIMIT 50`;

		const activities = await db.mariadb.query(activityQuery, [userId]);

		const userDetails =
		{
			id: user.id,
			username: user.username,
			role: user.role,
			permissions: JSON.parse(user.permissions || '[]'),
			createdAt: new Date(user.created_at),
			lastLogin: user.last_login ? new Date(user.last_login) : null,
			isActive: Boolean(user.is_active),
			passwordLastChanged: new Date(user.password_last_changed),
			mustChangePassword: Boolean(user.must_change_password),
			email: user.email,
			fullName: user.full_name,
			loginAttempts: user.login_attempts || 0,
			lockedUntil: user.locked_until ? new Date(user.locked_until) : null,
			createdBy: user.created_by,
			sessions: sessions.map((s: any) => ({
				sessionId: s.session_id,
				createdAt: new Date(s.created_at),
				lastActivity: new Date(s.last_activity),
				expiresAt: new Date(s.expires_at),
				ipAddress: s.ip_address,
				userAgent: s.user_agent,
			})),
			recentActivity: activities.map((a: any) => ({
				action: a.action,
				resource: a.resource,
				timestamp: new Date(a.timestamp),
				ipAddress: a.ip_address,
				success: Boolean(a.success),
				details: JSON.parse(a.details || '{}'),
			})),
		};

		// Resolve actor id by username when possible
		let actorId = session.username ?? 'unknown';
		try
		{
			const idRows = await db.mariadb.query<{ id: string }>('SELECT id FROM admin_users WHERE username = ? LIMIT 1', [session.username]);
			if (idRows[0]?.id)
			{
				actorId = idRows[0].id;
			}
		}
		catch {}

		await logUserActivity(actorId, 'users.view', 'users', true, {
			viewedUserId: userId,
		});

		return NextResponse.json(userDetails);
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error fetching user details:');
		return NextResponse.json(
			{ error: 'Failed to fetch user details' },
			{ status: 500 },
		);
	}
}

/**
 * PUT /api/users/:userId
 *
 * Updates user details by ID.
 */
export async function PUT(
	request: NextRequest,
	{ params }: { params: { userId: string } },
)
{
	try
	{
		const session = await getSession();
		if (
			!session || !Array.isArray(session.permissions)
				|| !session.permissions.includes('users.edit')
		)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
		}

		const { userId } = params;
		const body: UpdateUserRequestType = await request.json();

		// Get current user data for audit log
		const currentUser = await db.mariadb.query(
			'SELECT * FROM admin_users WHERE id = ?',
			[userId],
		);

		if (currentUser.length === 0)
		{
			return NextResponse.json({ error: 'User not found' }, { status: 404 });
		}

		const oldValues = currentUser[0];

		// Build update query
		const updateFields: string[] = [];
		const updateValues: any[] = [];

		if (body.role !== undefined)
		{
			updateFields.push('role = ?');
			updateValues.push(body.role);
		}

		if (body.permissions !== undefined)
		{
			updateFields.push('permissions = ?');
			updateValues.push(JSON.stringify(body.permissions));
		}

		if (body.isActive !== undefined)
		{
			updateFields.push('is_active = ?');
			updateValues.push(body.isActive ? 1 : 0);

			// If deactivating, also clear locked status
			if (!body.isActive)
			{
				updateFields.push('locked_until = NULL');
			}
		}

		if (body.email !== undefined)
		{
			updateFields.push('email = ?');
			updateValues.push(body.email);
		}

		if (body.fullName !== undefined)
		{
			updateFields.push('full_name = ?');
			updateValues.push(body.fullName);
		}

		if (body.mustChangePassword !== undefined)
		{
			updateFields.push('must_change_password = ?');
			updateValues.push(body.mustChangePassword ? 1 : 0);
		}

		if (updateFields.length === 0)
		{
			return NextResponse.json({ error: 'No fields to update' }, { status: 400 });
		}

		updateFields.push('updated_at = ?', 'updated_by = ?');

		// Resolve actor id
		let actorId = session.username ?? 'unknown';
		try
		{
			const idRows = await db.mariadb.query<{ id: string }>('SELECT id FROM admin_users WHERE username = ? LIMIT 1', [session.username]);
			if (idRows[0]?.id)
			{
				actorId = idRows[0].id;
			}
		}
		catch {}
		updateValues.push(new Date(), actorId);

		const updateQuery = `
			UPDATE admin_users
			SET ${updateFields.join(', ')}
			WHERE id = ?
		`;

		await db.mariadb.query(updateQuery, [...updateValues, userId]);

		// If user was deactivated, terminate all their sessions
		if (body.isActive === false)
		{
			await db.mariadb.query(
				'UPDATE admin_sessions SET is_active = 0 WHERE user_id = ?',
				[userId],
			);
		}

		// Log audit event
		await logAuditEvent(actorId, 'user.update', 'admin_users', userId, oldValues, body);

		await logUserActivity(actorId, 'users.edit', 'users', true, {
			updatedUserId: userId,
			changes: body,
		});

		return NextResponse.json({ success: true, message: 'User updated successfully' });
	}
	catch (error)
	{

		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error updating user:');
		return NextResponse.json(
			{ error: 'Failed to update user' },
			{ status: 500 },
		);
	}
}

/**
 * DELETE /api/users/:userId
 *
 * Deletes user by ID.
 */
export async function DELETE(
	request: NextRequest,
	{ params }: { params: { userId: string } },
)
{
	try
	{
		const session = await getSession();
		if (
			!session || !Array.isArray(session.permissions)
				|| !session.permissions.includes('users.delete')
		)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
		}

		const { userId } = params;

		// Prevent self-deletion
		if (userId === (session.username || ''))
		{
			return NextResponse.json(
				{ error: 'Cannot delete your own account' },
				{ status: 400 },
			);
		}

		// Get user data for audit log
		const userData = await db.mariadb.query(
			'SELECT * FROM admin_users WHERE id = ?',
			[userId],
		);

		if (userData.length === 0)
		{
			return NextResponse.json({ error: 'User not found' }, { status: 404 });
		}

		// Start transaction
		await db.mariadb.query('START TRANSACTION');

		try
		{
			// Deactivate all sessions
			await db.mariadb.query(
				'UPDATE admin_sessions SET is_active = 0 WHERE user_id = ?',
				[userId],
			);

			// Soft delete user (mark as inactive and add deletion timestamp) and Resolve actor id
			let actorIdDelete = session.username ?? 'unknown';
			try
			{
				const idRowsDel = await db.mariadb.query<{ id: string }>('SELECT id FROM admin_users WHERE username = ? LIMIT 1', [session.username]);
				if (idRowsDel[0]?.id)
				{
					actorIdDelete = idRowsDel[0].id;
				}
			}
			catch {}

			await db.mariadb.query(`
				UPDATE admin_users
				SET is_active = 0, deleted_at = ?, deleted_by = ?
				WHERE id = ?
			`, [new Date(), actorIdDelete, userId]);

			// Log audit event
			await logAuditEvent(actorIdDelete, 'user.delete', 'admin_users', userId, userData[0], null);

			await logUserActivity(actorIdDelete, 'users.delete', 'users', true, {
				deletedUserId: userId,
				deletedUsername: userData[0].username,
			});

			await db.mariadb.query('COMMIT');

			return NextResponse.json({ success: true, message: 'User deleted successfully' });
		}
		catch (error)
		{
			await db.mariadb.query('ROLLBACK');
			throw error;
		}
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error deleting user:');

		return NextResponse.json(
			{ error: 'Failed to delete user' },
			{ status: 500 },
		);
	}
}
