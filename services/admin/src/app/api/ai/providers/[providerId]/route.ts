import { NextRequest, NextResponse } from 'next/server';
import { apiLogger } from '@/lib/logger';

import type { AIProviderConfigType } from '@/types/ai';

// Mock data - in production this would come from database
const mockProviders: AIProviderConfigType[] = [];

export async function GET(
	request: NextRequest,
	{ params }: { params: { providerId: string } },
): Promise<NextResponse>
{
	try
	{
		const { providerId } = params;
		const provider = mockProviders.find(p => p.id === providerId);

		if (!provider)
		{
			return NextResponse.json(
				{
					success: false,
					error: 'Provider not found',
				},
				{ status: 404 },
			);
		}

		return NextResponse.json({
			success: true,
			data: provider,
		});
	}
	catch (error)
	{
		logger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error fetching AI provider:');
		return NextResponse.json(
			{
				success: false,
				error: 'Failed to fetch AI provider',
			},
			{ status: 500 },
		);
	}
}

export async function PUT(
	request: NextRequest,
	{ params }: { params: { providerId: string } },
): Promise<NextResponse>
{
	try
	{
		const { providerId } = params;
		const body = await request.json();

		const providerIndex = mockProviders.findIndex(p => p.id === providerId);
		if (providerIndex === -1)
		{
			return NextResponse.json(
				{
					success: false,
					error: 'Provider not found',
				},
				{ status: 404 },
			);
		}

		// Update provider
		mockProviders[providerIndex] = {
			...mockProviders[providerIndex],
			...body,
			id: providerId, // Ensure ID doesn't change
		};

		return NextResponse.json({
			success: true,
			data: mockProviders[providerIndex],
		});
	}
	catch (error)
	{
		logger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error updating AI provider:');
		return NextResponse.json(
			{
				success: false,
				error: 'Failed to update AI provider',
			},
			{ status: 500 },
		);
	}
}

export async function DELETE(
	request: NextRequest,
	{ params }: { params: { providerId: string } },
): Promise<NextResponse>
{
	try
	{
		const { providerId } = params;
		const providerIndex = mockProviders.findIndex(p => p.id === providerId);

		if (providerIndex === -1)
		{
			return NextResponse.json(
				{
					success: false,
					error: 'Provider not found',
				},
				{ status: 404 },
			);
		}

		// Remove provider
		mockProviders.splice(providerIndex, 1);

		return NextResponse.json({
			success: true,
			message: 'Provider deleted successfully',
		});
	}
	catch (error)
	{
		logger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error deleting AI provider:');
		return NextResponse.json(
			{
				success: false,
				error: 'Failed to delete AI provider',
			},
			{ status: 500 },
		);
	}
}
