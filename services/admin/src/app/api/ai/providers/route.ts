import { NextRequest, NextResponse } from 'next/server';
import { apiLogger } from '@/lib/logger';

import type { AIProviderConfigType } from '@/types/ai';

// Mock data for development - in production this would come from database
const mockProviders: AIProviderConfigType[] = [
	{
		id: 'openai-1',
		name: 'OpenAI GPT-4',
		type: 'openai',
		apiKey: 'sk-***',
		baseUrl: 'https://api.openai.com/v1',
		isActive: true,
		priority: 1,
		rateLimit: {
			requestsPerMinute: 60,
			tokensPerMinute: 150000,
		},
		models: [
			{
				id: 'gpt-4',
				name: 'GPT-4',
				provider: 'openai',
				maxTokens: 8192,
				costPerToken: 0.00003,
				capabilities: ['text-generation', 'analysis', 'summarization'],
				isActive: true,
			},
			{
				id: 'gpt-3.5-turbo',
				name: 'GPT-3.5 Turbo',
				provider: 'openai',
				maxTokens: 4096,
				costPerToken: 0.000002,
				capabilities: ['text-generation', 'analysis'],
				isActive: true,
			},
		],
		healthCheck: {
			lastCheck: new Date(),
			status: 'healthy',
			responseTime: 250,
		},
		usage: {
			totalRequests: 15420,
			totalTokens: 2840000,
			totalCost: 85.2,
			successRate: 98.5,
		},
	},
	{
		id: 'claude-1',
		name: 'Anthropic Claude',
		type: 'claude',
		apiKey: 'sk-ant-***',
		baseUrl: 'https://api.anthropic.com/v1',
		isActive: true,
		priority: 2,
		rateLimit: {
			requestsPerMinute: 50,
			tokensPerMinute: 100000,
		},
		models: [
			{
				id: 'claude-3-opus',
				name: 'Claude 3 Opus',
				provider: 'claude',
				maxTokens: 200000,
				costPerToken: 0.000015,
				capabilities: ['text-generation', 'analysis', 'reasoning'],
				isActive: true,
			},
			{
				id: 'claude-3-sonnet',
				name: 'Claude 3 Sonnet',
				provider: 'claude',
				maxTokens: 200000,
				costPerToken: 0.000003,
				capabilities: ['text-generation', 'analysis'],
				isActive: true,
			},
		],
		healthCheck: {
			lastCheck: new Date(),
			status: 'healthy',
			responseTime: 180,
		},
		usage: {
			totalRequests: 8920,
			totalTokens: 1650000,
			totalCost: 24.75,
			successRate: 99.2,
		},
	},
	{
		id: 'google-ai-1',
		name: 'Google AI Gemini',
		type: 'google-ai',
		apiKey: 'AIza***',
		baseUrl: 'https://generativelanguage.googleapis.com/v1',
		isActive: false,
		priority: 3,
		rateLimit: {
			requestsPerMinute: 60,
			tokensPerMinute: 120000,
		},
		models: [
			{
				id: 'gemini-pro',
				name: 'Gemini Pro',
				provider: 'google-ai',
				maxTokens: 30720,
				costPerToken: 0.0000005,
				capabilities: ['text-generation', 'analysis', 'multimodal'],
				isActive: false,
			},
		],
		healthCheck: {
			lastCheck: new Date(Date.now() - 300000),
			status: 'unhealthy',
			responseTime: 0,
			errorMessage: 'API key invalid',
		},
		usage: {
			totalRequests: 0,
			totalTokens: 0,
			totalCost: 0,
			successRate: 0,
		},
	},
];

export async function GET(): Promise<NextResponse>
{
	try
	{
		return NextResponse.json({
			success: true,
			data: mockProviders,
		});
	}
	catch (error)
	{
		logger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error fetching AI providers:');
		return NextResponse.json(
			{
				success: false,
				error: 'Failed to fetch AI providers',
			},
			{ status: 500 },
		);
	}
}

export async function POST(request: NextRequest): Promise<NextResponse>
{
	try
	{
		const body = await request.json();
		const newProvider: AIProviderConfigType = {
			id: `${body.type}-${Date.now()}`,
			...body,
			healthCheck: {
				lastCheck: null,
				status: 'unhealthy' as const,
				responseTime: 0,
			},
			usage: {
				totalRequests: 0,
				totalTokens: 0,
				totalCost: 0,
				successRate: 0,
			},
		};

		// In production, save to database
		mockProviders.push(newProvider);

		return NextResponse.json({
			success: true,
			data: newProvider,
		});
	}
	catch (error)
	{
		logger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error creating AI provider:');
		return NextResponse.json(
			{
				success: false,
				error: 'Failed to create AI provider',
			},
			{ status: 500 },
		);
	}
}
