import { NextRequest, NextResponse } from 'next/server';
import { apiLogger } from '@/lib/logger';

import type { AIContentGenerationJobType } from '@/types/ai';

// Mock data for development - in production this would come from database
const mockJobs: AIContentGenerationJobType[] = [
	{
		id: 'job-1',
		domain: 'example.com',
		type: 'description',
		status: 'completed',
		provider: 'openai',
		model: 'gpt-4',
		promptTemplate: 'domain-desc-1',
		input: {
			domain: 'example.com',
			title: 'Example Domain',
			metaDescription: 'This is an example domain',
			contentSummary: 'A simple example website',
			technologies: ['HTML', 'CSS', 'JavaScript'],
			category: 'Technology',
		},
		output: {
			content: 'Example.com is a demonstration website showcasing web development best practices and modern technologies.',
			confidence: 0.92,
			metadata: {
				wordCount: 15,
				readabilityScore: 8.5,
				keywordDensity: 0.12,
			},
		},
		metrics: {
			startTime: new Date(Date.now() - 3600000),
			endTime: new Date(Date.now() - 3580000),
			duration: 20000,
			tokenCount: 150,
			cost: 0.0045,
		},
		qualityAssessment: {
			score: 8.7,
			issues: [],
			suggestions: ['Consider adding more specific technical details'],
		},
	},
	{
		id: 'job-2',
		domain: 'test-site.org',
		type: 'categorization',
		status: 'processing',
		provider: 'claude',
		model: 'claude-3-sonnet',
		promptTemplate: 'categorization-1',
		input: {
			domain: 'test-site.org',
			title: 'Test Site Organization',
			content: 'Non-profit organization focused on testing methodologies',
			technologies: ['React', 'Node.js'],
			keywords: ['testing', 'quality assurance', 'non-profit'],
		},
		metrics: {
			startTime: new Date(Date.now() - 300000),
			tokenCount: 0,
			cost: 0,
		},
	},
	{
		id: 'job-3',
		domain: 'failed-domain.net',
		type: 'seo-summary',
		status: 'failed',
		provider: 'openai',
		model: 'gpt-3.5-turbo',
		promptTemplate: 'seo-summary-1',
		input: {
			domain: 'failed-domain.net',
			title: 'Failed Domain',
			description: 'This domain failed processing',
		},
		metrics: {
			startTime: new Date(Date.now() - 1800000),
			endTime: new Date(Date.now() - 1790000),
			duration: 10000,
			tokenCount: 0,
			cost: 0,
		},
		error: {
			code: 'RATE_LIMIT_EXCEEDED',
			message: 'API rate limit exceeded',
			details: {
				retryAfter: 60,
				requestsRemaining: 0,
			},
		},
	},
];

export async function GET(request: NextRequest): Promise<NextResponse>
{
	try
	{
		const { searchParams } = new URL(request.url);
		const status = searchParams.get('status');
		const type = searchParams.get('type');
		const provider = searchParams.get('provider');
		const limit = parseInt(searchParams.get('limit') || '50');
		const offset = parseInt(searchParams.get('offset') || '0');

		let filteredJobs = [...mockJobs];

		// Apply filters
		if (status)
		{
			filteredJobs = filteredJobs.filter(job => job.status === status);
		}
		if (type)
		{
			filteredJobs = filteredJobs.filter(job => job.type === type);
		}
		if (provider)
		{
			filteredJobs = filteredJobs.filter(job => job.provider === provider);
		}

		// Apply pagination
		const paginatedJobs = filteredJobs.slice(offset, offset + limit);

		return NextResponse.json({
			success: true,
			data: {
				jobs: paginatedJobs,
				total: filteredJobs.length,
				limit,
				offset,
			},
		});
	}
	catch (error)
	{
		logger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error fetching AI jobs:');
		return NextResponse.json(
			{
				success: false,
				error: 'Failed to fetch AI jobs',
			},
			{ status: 500 },
		);
	}
}

export async function POST(request: NextRequest): Promise<NextResponse>
{
	try
	{
		const body = await request.json();
		const newJob: AIContentGenerationJobType = {
			id: `job-${Date.now()}`,
			status: 'pending',
			metrics: {
				startTime: new Date(),
				tokenCount: 0,
				cost: 0,
			},
			...body,
		};

		// In production, save to database and queue for processing
		mockJobs.push(newJob);

		return NextResponse.json({
			success: true,
			data: newJob,
		});
	}
	catch (error)
	{
		logger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error creating AI job:');
		return NextResponse.json(
			{
				success: false,
				error: 'Failed to create AI job',
			},
			{ status: 500 },
		);
	}
}
