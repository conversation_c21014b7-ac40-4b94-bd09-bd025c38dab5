import { NextRequest, NextResponse } from 'next/server';
import { apiLogger } from '@/lib/logger';

import type { AIContentGenerationJobType } from '@/types/ai';

// Mock data - in production this would come from database
const mockJobs: AIContentGenerationJobType[] = [];

export async function GET(
	request: NextRequest,
	{ params }: { params: { jobId: string } },
): Promise<NextResponse>
{
	try
	{
		const { jobId } = params;
		const job = mockJobs.find(j => j.id === jobId);

		if (!job)
		{
			return NextResponse.json(
				{
					success: false,
					error: 'Job not found',
				},
				{ status: 404 },
			);
		}

		return NextResponse.json({
			success: true,
			data: job,
		});
	}
	catch (error)
	{
		logger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error fetching AI job:');
		return NextResponse.json(
			{
				success: false,
				error: 'Failed to fetch AI job',
			},
			{ status: 500 },
		);
	}
}

export async function PUT(
	request: NextRequest,
	{ params }: { params: { jobId: string } },
): Promise<NextResponse>
{
	try
	{
		const { jobId } = params;
		const body = await request.json();

		const jobIndex = mockJobs.findIndex(j => j.id === jobId);
		if (jobIndex === -1)
		{
			return NextResponse.json(
				{
					success: false,
					error: 'Job not found',
				},
				{ status: 404 },
			);
		}

		// Update job
		mockJobs[jobIndex] = {
			...mockJobs[jobIndex],
			...body,
			id: jobId, // Ensure ID doesn't change
		};

		return NextResponse.json({
			success: true,
			data: mockJobs[jobIndex],
		});
	}
	catch (error)
	{
		logger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error updating AI job:');
		return NextResponse.json(
			{
				success: false,
				error: 'Failed to update AI job',
			},
			{ status: 500 },
		);
	}
}

export async function DELETE(
	request: NextRequest,
	{ params }: { params: { jobId: string } },
): Promise<NextResponse>
{
	try
	{
		const { jobId } = params;
		const jobIndex = mockJobs.findIndex(j => j.id === jobId);

		if (jobIndex === -1)
		{
			return NextResponse.json(
				{
					success: false,
					error: 'Job not found',
				},
				{ status: 404 },
			);
		}

		// Remove job
		mockJobs.splice(jobIndex, 1);

		return NextResponse.json({
			success: true,
			message: 'Job deleted successfully',
		});
	}
	catch (error)
	{
		logger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error deleting AI job:');
		return NextResponse.json(
			{
				success: false,
				error: 'Failed to delete AI job',
			},
			{ status: 500 },
		);
	}
}
