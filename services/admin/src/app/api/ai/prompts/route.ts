import { NextRequest, NextResponse } from 'next/server';
import { apiLogger } from '@/lib/logger';

import type { AIPromptTemplateType } from '@/types/ai';

// Mock data for development - in production this would come from database
const mockPromptTemplates: AIPromptTemplateType[] = [
	{
		id: 'domain-desc-1',
		name: 'Domain Description Generator',
		description: 'Generates comprehensive domain descriptions based on website content',
		category: 'domain-description',
		template: `Generate a concise, informative description for the domain {{domain}} based on the following information:

Website Title: {{title}}
Meta Description: {{metaDescription}}
Content Summary: {{contentSummary}}
Technologies: {{technologies}}
Category: {{category}}

Requirements:
- Keep description between 150-300 characters
- Focus on the primary purpose and value proposition
- Use professional, neutral tone
- Avoid marketing jargon
- Include relevant keywords naturally`,
		variables: ['domain', 'title', 'metaDescription', 'contentSummary', 'technologies', 'category'],
		isActive: true,
		performance: {
			averageResponseTime: 2.3,
			successRate: 98.5,
			qualityScore: 8.4,
			usageCount: 12500,
		},
		createdAt: new Date('2024-01-15'),
		updatedAt: new Date('2024-02-01'),
	},
	{
		id: 'categorization-1',
		name: 'Domain Categorization',
		description: 'Categorizes domains based on content and purpose',
		category: 'categorization',
		template: `Analyze the following domain and assign it to the most appropriate category:

Domain: {{domain}}
Title: {{title}}
Content: {{content}}
Technologies: {{technologies}}
Keywords: {{keywords}}

Available Categories:
- Technology & Software
- E-commerce & Shopping
- News & Media
- Education & Learning
- Business & Finance
- Entertainment & Gaming
- Health & Medical
- Travel & Tourism
- Social & Community
- Government & Organizations

Provide:
1. Primary category (required)
2. Secondary category (if applicable)
3. Confidence score (0-100)
4. Brief reasoning`,
		variables: ['domain', 'title', 'content', 'technologies', 'keywords'],
		isActive: true,
		performance: {
			averageResponseTime: 1.8,
			successRate: 99.1,
			qualityScore: 9.1,
			usageCount: 8200,
		},
		createdAt: new Date('2024-01-10'),
		updatedAt: new Date('2024-01-25'),
	},
	{
		id: 'tagging-1',
		name: 'Domain Tagging',
		description: 'Generates relevant tags for domains based on content analysis',
		category: 'tagging',
		template: `Generate relevant tags for the domain {{domain}} based on:

Content: {{content}}
Category: {{category}}
Technologies: {{technologies}}
Industry: {{industry}}

Requirements:
- Generate 5-10 relevant tags
- Use lowercase, hyphen-separated format
- Focus on functionality, industry, and key features
- Avoid generic terms
- Include technology stack if relevant

Example format: e-commerce, payment-processing, mobile-app, saas-platform`,
		variables: ['domain', 'content', 'category', 'technologies', 'industry'],
		isActive: true,
		performance: {
			averageResponseTime: 1.5,
			successRate: 98.5,
			qualityScore: 8.7,
			usageCount: 6800,
		},
		createdAt: new Date('2024-01-20'),
		updatedAt: new Date('2024-02-05'),
	},
	{
		id: 'seo-summary-1',
		name: 'SEO Summary Generator',
		description: 'Creates SEO-optimized summaries for domain listings',
		category: 'seo-summary',
		template: `Create an SEO-optimized summary for {{domain}}:

Title: {{title}}
Description: {{description}}
Keywords: {{keywords}}
Category: {{category}}
Features: {{features}}

Requirements:
- 120-160 characters for meta description
- Include primary keyword naturally
- Compelling call-to-action or value proposition
- Readable and engaging for users
- Optimized for search engines`,
		variables: ['domain', 'title', 'description', 'keywords', 'category', 'features'],
		isActive: true,
		performance: {
			averageResponseTime: 2.1,
			successRate: 96.9,
			qualityScore: 8.2,
			usageCount: 4840,
		},
		createdAt: new Date('2024-01-25'),
		updatedAt: new Date('2024-02-10'),
	},
];

export async function GET(): Promise<NextResponse>
{
	try
	{
		return NextResponse.json({
			success: true,
			data: mockPromptTemplates,
		});
	}
	catch (error)
	{
		logger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error fetching AI prompt templates:');
		return NextResponse.json(
			{
				success: false,
				error: 'Failed to fetch AI prompt templates',
			},
			{ status: 500 },
		);
	}
}

export async function POST(request: NextRequest): Promise<NextResponse>
{
	try
	{
		const body = await request.json();
		const newTemplate: AIPromptTemplateType = {
			id: `template-${Date.now()}`,
			...body,
			performance: {
				averageResponseTime: 0,
				successRate: 0,
				qualityScore: 0,
				usageCount: 0,
			},
			createdAt: new Date(),
			updatedAt: new Date(),
		};

		// In production, save to database
		mockPromptTemplates.push(newTemplate);

		return NextResponse.json({
			success: true,
			data: newTemplate,
		});
	}
	catch (error)
	{
		logger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error creating AI prompt template:');
		return NextResponse.json(
			{
				success: false,
				error: 'Failed to create AI prompt template',
			},
			{ status: 500 },
		);
	}
}
