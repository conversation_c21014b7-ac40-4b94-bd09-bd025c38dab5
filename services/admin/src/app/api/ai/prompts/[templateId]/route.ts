import { NextRequest, NextResponse } from 'next/server';
import { apiLogger } from '@/lib/logger';

import type { AIPromptTemplateType } from '@/types/ai';

// Mock data - in production this would come from database
const mockPromptTemplates: AIPromptTemplateType[] = [];

export async function GET(
	request: NextRequest,
	{ params }: { params: { templateId: string } },
): Promise<NextResponse>
{
	try
	{
		const { templateId } = params;
		const template = mockPromptTemplates.find(t => t.id === templateId);

		if (!template)
		{
			return NextResponse.json(
				{
					success: false,
					error: 'Template not found',
				},
				{ status: 404 },
			);
		}

		return NextResponse.json({
			success: true,
			data: template,
		});
	}
	catch (error)
	{
		logger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error fetching AI prompt template:');
		return NextResponse.json(
			{
				success: false,
				error: 'Failed to fetch AI prompt template',
			},
			{ status: 500 },
		);
	}
}

export async function PUT(
	request: NextRequest,
	{ params }: { params: { templateId: string } },
): Promise<NextResponse>
{
	try
	{
		const { templateId } = params;
		const body = await request.json();

		const templateIndex = mockPromptTemplates.findIndex(t => t.id === templateId);
		if (templateIndex === -1)
		{
			return NextResponse.json(
				{
					success: false,
					error: 'Template not found',
				},
				{ status: 404 },
			);
		}

		// Update template
		mockPromptTemplates[templateIndex] = {
			...mockPromptTemplates[templateIndex],
			...body,
			id: templateId, // Ensure ID doesn't change
			updatedAt: new Date(),
		};

		return NextResponse.json({
			success: true,
			data: mockPromptTemplates[templateIndex],
		});
	}
	catch (error)
	{
		logger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error updating AI prompt template:');
		return NextResponse.json(
			{
				success: false,
				error: 'Failed to update AI prompt template',
			},
			{ status: 500 },
		);
	}
}

export async function DELETE(
	request: NextRequest,
	{ params }: { params: { templateId: string } },
): Promise<NextResponse>
{
	try
	{
		const { templateId } = params;
		const templateIndex = mockPromptTemplates.findIndex(t => t.id === templateId);

		if (templateIndex === -1)
		{
			return NextResponse.json(
				{
					success: false,
					error: 'Template not found',
				},
				{ status: 404 },
			);
		}

		// Remove template
		mockPromptTemplates.splice(templateIndex, 1);

		return NextResponse.json({
			success: true,
			message: 'Template deleted successfully',
		});
	}
	catch (error)
	{
		logger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error deleting AI prompt template:');
		return NextResponse.json(
			{
				success: false,
				error: 'Failed to delete AI prompt template',
			},
			{ status: 500 },
		);
	}
}
