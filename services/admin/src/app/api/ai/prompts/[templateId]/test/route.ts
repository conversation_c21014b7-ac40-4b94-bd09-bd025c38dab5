import { NextRequest, NextResponse } from 'next/server';
import { apiLogger } from '@/lib/logger';

import type { AIPromptTestResultType } from '@/types/ai';

export async function POST(
	request: NextRequest,
	{ params }: { params: { templateId: string } },
): Promise<NextResponse>
{
	try
	{
		const { templateId } = params;
		const body = await request.json();
		const { testInput, provider, model } = body;

		// Mock test result - in production this would make actual AI API calls
		const mockTestResult: AIPromptTestResultType = {
			id: `test-${Date.now()}`,
			templateId,
			testInput,
			provider: provider || 'openai',
			model: model || 'gpt-4',
			result: {
				content: `Mock generated content for template ${templateId} with input: ${JSON.stringify(testInput)}`,
				confidence: Math.random() * 0.3 + 0.7, // 0.7-1.0
				responseTime: Math.floor(Math.random() * 2000) + 500, // 500-2500ms
				tokenCount: Math.floor(Math.random() * 500) + 100, // 100-600 tokens
				cost: Math.random() * 0.05 + 0.01, // $0.01-$0.06
			},
			qualityScore: Math.random() * 3 + 7, // 7-10
			issues: Math.random() > 0.7 ? ['Minor grammatical issue', 'Could be more specific'] : [],
			timestamp: new Date(),
		};

		return NextResponse.json({
			success: true,
			data: mockTestResult,
		});
	}
	catch (error)
	{
		logger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error testing AI prompt template:');
		return NextResponse.json(
			{
				success: false,
				error: 'Failed to test AI prompt template',
			},
			{ status: 500 },
		);
	}
}
