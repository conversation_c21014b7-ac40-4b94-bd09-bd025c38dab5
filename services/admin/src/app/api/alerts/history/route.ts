import { NextRequest, NextResponse } from 'next/server';
import { alertLogger } from '@/lib/logger';

import type { AlertHistoryType } from '@/types/alerts';

// Mock data for development
const mockAlertHistory: AlertHistoryType[] = [
	{
		id: 'hist-1',
		alertId: 'alert-1',
		action: 'triggered',
		timestamp: new Date(Date.now() - 30 * 60 * 1000),
		details: {
			ruleName: 'High Error Rate',
			severity: 'critical',
			conditions: [
				{ metric: 'error_rate', value: 7.2, threshold: 5.0 },
			],
		},
		newState: 'active',
	},
	{
		id: 'hist-2',
		alertId: 'alert-1',
		action: 'notification_sent',
		timestamp: new Date(Date.now() - 25 * 60 * 1000),
		details: {
			channel: 'email',
			recipient: '<EMAIL>',
			success: true,
		},
	},
	{
		id: 'hist-3',
		alertId: 'alert-2',
		action: 'acknowledged',
		timestamp: new Date(Date.now() - 45 * 60 * 1000),
		userId: 'admin',
		details: {
			reason: 'Investigating database connection issues',
		},
		previousState: 'active',
		newState: 'acknowledged',
	},
	{
		id: 'hist-4',
		alertId: 'alert-2',
		action: 'resolved',
		timestamp: new Date(Date.now() - 15 * 60 * 1000),
		userId: 'admin',
		details: {
			reason: 'Database connection pool increased',
			resolution: 'Increased max connections from 100 to 150',
		},
		previousState: 'acknowledged',
		newState: 'resolved',
	},
];

export async function GET(request: NextRequest)
{
	try
	{
		const { searchParams } = new URL(request.url);
		const alertId = searchParams.get('alertId');
		const action = searchParams.get('action');
		const userId = searchParams.get('userId');
		const limit = parseInt(searchParams.get('limit') || '50');

		let filteredHistory = [...mockAlertHistory];

		if (alertId)
		{
			filteredHistory = filteredHistory.filter(h => h.alertId === alertId);
		}

		if (action)
		{
			filteredHistory = filteredHistory.filter(h => h.action === action);
		}

		if (userId)
		{
			filteredHistory = filteredHistory.filter(h => h.userId === userId);
		}

		// Sort by most recent first
		filteredHistory.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

		// Apply limit
		filteredHistory = filteredHistory.slice(0, limit);

		return NextResponse.json({
			history: filteredHistory,
			total: filteredHistory.length,
		});
	}
	catch (error)
	{
		logger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error fetching alert history:');
		return NextResponse.json(
			{ error: 'Failed to fetch alert history' },
			{ status: 500 },
		);
	}
}
