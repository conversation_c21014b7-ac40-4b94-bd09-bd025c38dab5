import { NextRequest, NextResponse } from 'next/server';
import { alertLogger } from '@/lib/logger';

import type {
	NotificationTemplateType,
	NotificationTemplateCreateRequestType,
	NotificationChannelType,
} from '@/types/alerts';

// Mock data for development
const mockNotificationTemplates: NotificationTemplateType[] = [
	{
		id: 'template-1',
		name: 'Critical Alert Email',
		channel: 'email',
		subject: 'CRITICAL: {{ruleName}} - {{serviceName}}',
		body: `
Alert: {{ruleName}}
Severity: {{severity}}
Service: {{serviceName}}
Message: {{message}}
Triggered: {{triggeredAt}}
Details: {{details}}

Please investigate immediately.
    `.trim(),
		variables: ['ruleName', 'severity', 'serviceName', 'message', 'triggeredAt', 'details'],
		isDefault: true,
		createdAt: new Date('2024-01-01'),
		updatedAt: new Date('2024-01-01'),
	},
	{
		id: 'template-2',
		name: 'Slack Alert',
		channel: 'slack',
		subject: '',
		body: `
🚨 *{{severity | upper}}*: {{ruleName}}
*Service*: {{serviceName}}
*Message*: {{message}}
*Time*: {{triggeredAt | format_date}}
{{#if details}}
*Details*: \`\`\`{{details | json}}\`\`\`
{{/if}}
    `.trim(),
		variables: ['severity', 'ruleName', 'serviceName', 'message', 'triggeredAt', 'details'],
		isDefault: true,
		createdAt: new Date('2024-01-01'),
		updatedAt: new Date('2024-01-01'),
	},
	{
		id: 'template-3',
		name: 'SMS Alert',
		channel: 'sms',
		subject: '',
		body: 'ALERT: {{ruleName}} - {{serviceName}}. {{message}}. Check dashboard for details.',
		variables: ['ruleName', 'serviceName', 'message'],
		isDefault: true,
		createdAt: new Date('2024-01-01'),
		updatedAt: new Date('2024-01-01'),
	},
];

export async function GET(request: NextRequest)
{
	try
	{
		const { searchParams } = new URL(request.url);
		const channel = searchParams.get('channel') as NotificationChannelType;
		const isDefault = searchParams.get('isDefault');

		let filteredTemplates = [...mockNotificationTemplates];

		if (channel)
		{
			filteredTemplates = filteredTemplates.filter(template => template.channel === channel);
		}

		if (isDefault !== null)
		{
			filteredTemplates = filteredTemplates.filter(template => template.isDefault === (isDefault === 'true'));
		}

		return NextResponse.json({
			templates: filteredTemplates,
			total: filteredTemplates.length,
		});
	}
	catch (error)
	{
		logger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error fetching notification templates:');
		return NextResponse.json(
			{ error: 'Failed to fetch notification templates' },
			{ status: 500 },
		);
	}
}

export async function POST(request: NextRequest)
{
	try
	{
		const templateData: NotificationTemplateCreateRequestType = await request.json();

		// Validate required fields
		if (!templateData.name || !templateData.channel || !templateData.body)
		{
			return NextResponse.json(
				{ error: 'Name, channel, and body are required' },
				{ status: 400 },
			);
		}

		// Extract variables from template body
		const variableRegex = /\{\{([^}]+)\}\}/g;
		const variables: string[] = [];
		let match;

		while ((match = variableRegex.exec(templateData.body)) !== null)
		{
			const variable = match[1].split('|')[0].trim(); // Remove filters
			if (!variables.includes(variable))
			{
				variables.push(variable);
			}
		}

		// If this is set as default, unset other defaults for the same channel
		if (templateData.isDefault)
		{
			mockNotificationTemplates.forEach((template) =>
			{
				if (template.channel === templateData.channel)
				{
					template.isDefault = false;
				}
			});
		}

		// Create new template (mock implementation)
		const newTemplate: NotificationTemplateType = {
			id: `template-${Date.now()}`,
			...templateData,
			variables,
			createdAt: new Date(),
			updatedAt: new Date(),
		};

		mockNotificationTemplates.push(newTemplate);

		return NextResponse.json({
			success: true,
			template: newTemplate,
			message: 'Notification template created successfully',
		});
	}
	catch (error)
	{
		logger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error creating notification template:');
		return NextResponse.json(
			{ error: 'Failed to create notification template' },
			{ status: 500 },
		);
	}
}

export async function PUT(request: NextRequest)
{
	try
	{
		const { searchParams } = new URL(request.url);
		const templateId = searchParams.get('templateId');

		if (!templateId)
		{
			return NextResponse.json(
				{ error: 'Template ID is required' },
				{ status: 400 },
			);
		}

		const templateIndex = mockNotificationTemplates.findIndex(t => t.id === templateId);
		if (templateIndex === -1)
		{
			return NextResponse.json(
				{ error: 'Template not found' },
				{ status: 404 },
			);
		}

		const updateData = await request.json();
		const existingTemplate = mockNotificationTemplates[templateIndex];

		// Extract variables if body is updated
		let variables = existingTemplate.variables;
		if (updateData.body)
		{
			const variableRegex = /\{\{([^}]+)\}\}/g;
			variables = [];
			let match;

			while ((match = variableRegex.exec(updateData.body)) !== null)
			{
				const variable = match[1].split('|')[0].trim();
				if (!variables.includes(variable))
				{
					variables.push(variable);
				}
			}
		}

		// If this is set as default, unset other defaults for the same channel
		if (updateData.isDefault && updateData.isDefault !== existingTemplate.isDefault)
		{
			mockNotificationTemplates.forEach((template) =>
			{
				if (template.channel === existingTemplate.channel && template.id !== templateId)
				{
					template.isDefault = false;
				}
			});
		}

		const updatedTemplate: NotificationTemplateType = {
			...existingTemplate,
			...updateData,
			variables,
			updatedAt: new Date(),
		};

		mockNotificationTemplates[templateIndex] = updatedTemplate;

		return NextResponse.json({
			success: true,
			template: updatedTemplate,
			message: 'Notification template updated successfully',
		});
	}
	catch (error)
	{
		logger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error updating notification template:');
		return NextResponse.json(
			{ error: 'Failed to update notification template' },
			{ status: 500 },
		);
	}
}

export async function DELETE(request: NextRequest)
{
	try
	{
		const { searchParams } = new URL(request.url);
		const templateId = searchParams.get('templateId');

		if (!templateId)
		{
			return NextResponse.json(
				{ error: 'Template ID is required' },
				{ status: 400 },
			);
		}

		const templateIndex = mockNotificationTemplates.findIndex(t => t.id === templateId);
		if (templateIndex === -1)
		{
			return NextResponse.json(
				{ error: 'Template not found' },
				{ status: 404 },
			);
		}

		mockNotificationTemplates.splice(templateIndex, 1);

		return NextResponse.json({
			success: true,
			message: 'Notification template deleted successfully',
		});
	}
	catch (error)
	{
		logger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error deleting notification template:');
		return NextResponse.json(
			{ error: 'Failed to delete notification template' },
			{ status: 500 },
		);
	}
}
