import { NextRequest, NextResponse } from 'next/server';
import { alertLogger } from '@/lib/logger';

import type {
	AlertEscalationPolicyType,
	EscalationPolicyCreateRequestType,
	EscalationPolicyUpdateRequestType,
} from '@/types/alerts';

// Mock data for development
const mockEscalationPolicies: AlertEscalationPolicyType[] =
[
	{
		id: 'policy-1',
		name: 'Critical Alert Escalation',
		description: 'Escalation policy for critical severity alerts',
		levels: [
			{
				level: 1,
				delay: 0,
				channels: ['slack', 'email'],
				recipients: ['<EMAIL>', '#critical-alerts'],
			},
			{
				level: 2,
				delay: 15,
				channels: ['sms', 'pagerduty'],
				recipients: ['<EMAIL>', '<EMAIL>'],
			},
			{
				level: 3,
				delay: 30,
				channels: ['pagerduty', 'jira'],
				recipients: ['<EMAIL>'],
			},
		],
		onCallRotation: {
			enabled: true,
			schedule: [
				{
					user: '<EMAIL>',
					startTime: '09:00',
					endTime: '17:00',
					days: [1, 2, 3, 4, 5],
				},
				{
					user: '<EMAIL>',
					startTime: '17:00',
					endTime: '09:00',
					days: [1, 2, 3, 4, 5, 6, 0],
				},
			],
		},
		createdAt: new Date('2024-01-15'),
		updatedAt: new Date('2024-01-20'),
	},
	{
		id: 'policy-2',
		name: 'Standard Alert Escalation',
		description: 'Default escalation policy for high and medium severity alerts',
		levels: [
			{
				level: 1,
				delay: 0,
				channels: ['slack'],
				recipients: ['#alerts'],
			},
			{
				level: 2,
				delay: 30,
				channels: ['email'],
				recipients: ['<EMAIL>'],
			},
		],
		createdAt: new Date('2024-01-10'),
		updatedAt: new Date('2024-01-15'),
	},
];

export async function GET(request: NextRequest)
{
	try
	{
		const { searchParams } = new URL(request.url);
		const policyId = searchParams.get('policyId');

		if (policyId)
		{
			const policy = mockEscalationPolicies.find(p => p.id === policyId);
			if (!policy)
			{
				return NextResponse.json(
					{ error: 'Escalation policy not found' },
					{ status: 404 },
				);
			}

			return NextResponse.json({ policy });
		}

		return NextResponse.json(mockEscalationPolicies);
	}
	catch (error)
	{
		logger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error fetching escalation policies:');
		return NextResponse.json(
			{ error: 'Failed to fetch escalation policies' },
			{ status: 500 },
		);
	}
}

export async function POST(request: NextRequest)
{
	try
	{
		const policyData: EscalationPolicyCreateRequestType = await request.json();

		// Validate required fields
		if (!policyData.name || !policyData.levels || policyData.levels.length === 0)
		{
			return NextResponse.json(
				{ error: 'Policy name and at least one escalation level are required' },
				{ status: 400 },
			);
		}

		const newPolicy: AlertEscalationPolicyType = {
			...policyData,
			id: `policy-${Date.now()}`,
			createdAt: new Date(),
			updatedAt: new Date(),
		};

		mockEscalationPolicies.push(newPolicy);

		return NextResponse.json({
			success: true,
			policy: newPolicy,
			message: 'Escalation policy created successfully',
		});
	}
	catch (error)
	{
		logger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error creating escalation policy:');
		return NextResponse.json(
			{ error: 'Failed to create escalation policy' },
			{ status: 500 },
		);
	}
}

export async function PUT(request: NextRequest)
{
	try
	{
		const { searchParams } = new URL(request.url);
		const policyId = searchParams.get('policyId');

		if (!policyId)
		{
			return NextResponse.json(
				{ error: 'Policy ID is required' },
				{ status: 400 },
			);
		}

		const policyIndex = mockEscalationPolicies.findIndex(p => p.id === policyId);
		if (policyIndex === -1)
		{
			return NextResponse.json(
				{ error: 'Escalation policy not found' },
				{ status: 404 },
			);
		}

		const updateData: EscalationPolicyUpdateRequestType = await request.json();
		const existingPolicy = mockEscalationPolicies[policyIndex];

		const updatedPolicy: AlertEscalationPolicyType = {
			...existingPolicy,
			...updateData,
			updatedAt: new Date(),
		};

		mockEscalationPolicies[policyIndex] = updatedPolicy;

		return NextResponse.json({
			success: true,
			policy: updatedPolicy,
			message: 'Escalation policy updated successfully',
		});
	}
	catch (error)
	{
		logger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error updating escalation policy:');
		return NextResponse.json(
			{ error: 'Failed to update escalation policy' },
			{ status: 500 },
		);
	}
}

export async function DELETE(request: NextRequest)
{
	try
	{
		const { searchParams } = new URL(request.url);
		const policyId = searchParams.get('policyId');

		if (!policyId)
		{
			return NextResponse.json(
				{ error: 'Policy ID is required' },
				{ status: 400 },
			);
		}

		const policyIndex = mockEscalationPolicies.findIndex(p => p.id === policyId);
		if (policyIndex === -1)
		{
			return NextResponse.json(
				{ error: 'Escalation policy not found' },
				{ status: 404 },
			);
		}

		mockEscalationPolicies.splice(policyIndex, 1);

		return NextResponse.json({
			success: true,
			message: 'Escalation policy deleted successfully',
		});
	}
	catch (error)
	{
		logger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error deleting escalation policy:');
		return NextResponse.json(
			{ error: 'Failed to delete escalation policy' },
			{ status: 500 },
		);
	}
}
