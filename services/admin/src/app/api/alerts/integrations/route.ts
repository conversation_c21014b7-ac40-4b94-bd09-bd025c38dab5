import { NextRequest, NextResponse } from 'next/server';
import { alertLogger } from '@/lib/logger';

import type { ExternalIntegrationType } from '@/types/alerts';

// Mock data for development
const mockIntegrations: ExternalIntegrationType[] = [
	{
		id: 'integration-1',
		type: 'slack',
		name: 'Main Alerts Channel',
		config: {
			webhookUrl: process.env.SLACK_WEBHOOK_URL || process.env.NEXT_PUBLIC_SLACK_WEBHOOK_URL,
			channel: process.env.SLACK_CHANNEL || '#alerts',
		},
		enabled: true,
		lastSync: new Date('2024-01-20T10:30:00Z'),
		status: 'connected',
	},
	{
		id: 'integration-2',
		type: 'email',
		name: 'Admin Email Notifications',
		config: {
			smtpServer: process.env.SMTP_SERVER || 'smtp.example.com',
			port: parseInt(process.env.SMTP_PORT || '587', 10),
			username: process.env.SMTP_USERNAME || '<EMAIL>',
			recipients: process.env.ALERT_RECIPIENTS ? process.env.ALERT_RECIPIENTS.split(',') : ['<EMAIL>', '<EMAIL>'],
		},
		enabled: true,
		lastSync: new Date('2024-01-20T09:15:00Z'),
		status: 'connected',
	},
	{
		id: 'integration-3',
		type: 'pagerduty',
		name: 'Critical Alerts PagerDuty',
		config: {
			integrationKey: process.env.PAGERDUTY_INTEGRATION_KEY || '',
			serviceId: process.env.PAGERDUTY_SERVICE_ID || '',
		},
		enabled: true,
		lastSync: new Date('2024-01-20T08:45:00Z'),
		status: 'connected',
	},
	{
		id: 'integration-4',
		type: 'webhook',
		name: 'Custom Webhook',
		config: {
			url: process.env.WEBHOOK_URL || process.env.NEXT_PUBLIC_WEBHOOK_URL,
			method: 'POST',
			headers: {
				Authorization: process.env.WEBHOOK_AUTH_HEADER || process.env.NEXT_PUBLIC_WEBHOOK_AUTH_HEADER,
				'Content-Type': 'application/json',
			},
		},
		enabled: false,
		lastSync: new Date('2024-01-19T16:20:00Z'),
		status: 'disconnected',
	},
];

export async function GET(request: NextRequest)
{
	try
	{
		const { searchParams } = new URL(request.url);
		const type = searchParams.get('type');
		const enabled = searchParams.get('enabled');

		let filteredIntegrations = [...mockIntegrations];

		if (type)
		{
			filteredIntegrations = filteredIntegrations.filter(integration => integration.type === type);
		}

		if (enabled !== null)
		{
			filteredIntegrations = filteredIntegrations.filter(integration => integration.enabled === (enabled === 'true'));
		}

		return NextResponse.json({
			integrations: filteredIntegrations,
			total: filteredIntegrations.length,
		});
	}
	catch (error)
	{
		logger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error fetching integrations:');
		return NextResponse.json(
			{ error: 'Failed to fetch integrations' },
			{ status: 500 },
		);
	}
}

export async function POST(request: NextRequest)
{
	try
	{
		const integrationData = await request.json();

		// Validate required fields
		if (!integrationData.type || !integrationData.name || !integrationData.config)
		{
			return NextResponse.json(
				{ error: 'Type, name, and config are required' },
				{ status: 400 },
			);
		}

		// Validate config based on integration type
		const configValidation = validateIntegrationConfig(integrationData.type, integrationData.config);
		if (!configValidation.valid)
		{
			return NextResponse.json(
				{ error: configValidation.error },
				{ status: 400 },
			);
		}

		const newIntegration: ExternalIntegrationType = {
			id: `integration-${Date.now()}`,
			type: integrationData.type,
			name: integrationData.name,
			config: integrationData.config,
			enabled: integrationData.enabled ?? true,
			status: 'connected', // Would test connection in real implementation
		};

		mockIntegrations.push(newIntegration);

		return NextResponse.json({
			success: true,
			integration: newIntegration,
			message: 'Integration created successfully',
		});
	}
	catch (error)
	{
		logger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error creating integration:');
		return NextResponse.json(
			{ error: 'Failed to create integration' },
			{ status: 500 },
		);
	}
}

export async function PUT(request: NextRequest)
{
	try
	{
		const { searchParams } = new URL(request.url);
		const integrationId = searchParams.get('integrationId');

		if (!integrationId)
		{
			return NextResponse.json(
				{ error: 'Integration ID is required' },
				{ status: 400 },
			);
		}

		const integrationIndex = mockIntegrations.findIndex(i => i.id === integrationId);
		if (integrationIndex === -1)
		{
			return NextResponse.json(
				{ error: 'Integration not found' },
				{ status: 404 },
			);
		}

		const updateData = await request.json();
		const existingIntegration = mockIntegrations[integrationIndex];

		// Validate config if it's being updated
		if (updateData.config)
		{
			const configValidation = validateIntegrationConfig(existingIntegration.type, updateData.config);
			if (!configValidation.valid)
			{
				return NextResponse.json(
					{ error: configValidation.error },
					{ status: 400 },
				);
			}
		}

		const updatedIntegration: ExternalIntegrationType = {
			...existingIntegration,
			...updateData,
			lastSync: new Date(), // Update sync time when modified
		};

		mockIntegrations[integrationIndex] = updatedIntegration;

		return NextResponse.json({
			success: true,
			integration: updatedIntegration,
			message: 'Integration updated successfully',
		});
	}
	catch (error)
	{
		logger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error updating integration:');
		return NextResponse.json(
			{ error: 'Failed to update integration' },
			{ status: 500 },
		);
	}
}

export async function DELETE(request: NextRequest)
{
	try
	{
		const { searchParams } = new URL(request.url);
		const integrationId = searchParams.get('integrationId');

		if (!integrationId)
		{
			return NextResponse.json(
				{ error: 'Integration ID is required' },
				{ status: 400 },
			);
		}

		const integrationIndex = mockIntegrations.findIndex(i => i.id === integrationId);
		if (integrationIndex === -1)
		{
			return NextResponse.json(
				{ error: 'Integration not found' },
				{ status: 404 },
			);
		}

		mockIntegrations.splice(integrationIndex, 1);

		return NextResponse.json({
			success: true,
			message: 'Integration deleted successfully',
		});
	}
	catch (error)
	{
		logger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error deleting integration:');
		return NextResponse.json(
			{ error: 'Failed to delete integration' },
			{ status: 500 },
		);
	}
}

function validateIntegrationConfig(type: string, config: any): { valid: boolean; error?: string }
{
	switch (type)
	{
		case 'slack':
			if (!config.webhookUrl)
			{
				return { valid: false, error: 'Webhook URL is required for Slack integration' };
			}
			if (!config.webhookUrl.startsWith('https://hooks.slack.com/'))
			{
				return { valid: false, error: 'Invalid Slack webhook URL format' };
			}
			break;

		case 'email':
			if (!config.smtpServer || !config.recipients || !Array.isArray(config.recipients))
			{
				return { valid: false, error: 'SMTP server and recipients array are required for email integration' };
			}
			break;

		case 'pagerduty':
			if (!config.integrationKey)
			{
				return { valid: false, error: 'Integration key is required for PagerDuty integration' };
			}
			break;

		case 'webhook':
			if (!config.url)
			{
				return { valid: false, error: 'URL is required for webhook integration' };
			}
			try
			{
				new URL(config.url);
			}
			catch
			{
				return { valid: false, error: 'Invalid webhook URL format' };
			}
			break;

		case 'teams':
			if (!config.webhookUrl)
			{
				return { valid: false, error: 'Webhook URL is required for Teams integration' };
			}
			break;

		case 'jira':
			if (!config.serverUrl || !config.username || !config.apiToken)
			{
				return { valid: false, error: 'Server URL, username, and API token are required for JIRA integration' };
			}
			break;

		default:
			return { valid: false, error: 'Unsupported integration type' };
	}

	return { valid: true };
}
