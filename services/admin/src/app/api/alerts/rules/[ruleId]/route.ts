import { NextRequest, NextResponse } from 'next/server';
import { alertLogger } from '@/lib/logger';

import type { AlertRuleType, AlertRuleUpdateRequestType } from '@/types/alerts';

// Mock data - in real implementation, this would come from database
const mockAlertRules: AlertRuleType[] = [
	{
		id: 'rule-1',
		name: 'High Error Rate',
		description: 'Triggers when service error rate exceeds threshold',
		enabled: true,
		severity: 'critical',
		conditions: [
			{
				id: 'cond-1',
				metric: 'error_rate',
				operator: '>',
				threshold: 5.0,
				duration: 5,
				service: 'crawler',
			},
		],
		logicalOperator: 'AND',
		timeBasedConditions: {
			activeHours: { start: '00:00', end: '23:59' },
			activeDays: [0, 1, 2, 3, 4, 5, 6],
			timezone: 'UTC',
		},
		dependencies: [],
		suppressionRules: {
			maintenanceWindows: true,
			dependencyBased: true,
			smartGrouping: false,
		},
		createdAt: new Date('2024-01-01'),
		updatedAt: new Date('2024-01-15'),
		createdBy: 'admin',
		tags: ['production', 'critical'],
	},
];

export async function GET(
	request: NextRequest,
	{ params }: { params: { ruleId: string } },
)
{
	try
	{
		const { ruleId } = params;
		const rule = mockAlertRules.find(r => r.id === ruleId);

		if (!rule)
		{
			return NextResponse.json(
				{ error: 'Alert rule not found' },
				{ status: 404 },
			);
		}

		return NextResponse.json({ rule });
	}
	catch (error)
	{
		logger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error fetching alert rule:');
		return NextResponse.json(
			{ error: 'Failed to fetch alert rule' },
			{ status: 500 },
		);
	}
}

export async function PUT(
	request: NextRequest,
	{ params }: { params: { ruleId: string } },
)
{
	try
	{
		const { ruleId } = params;
		const updateData: AlertRuleUpdateRequestType = await request.json();

		const ruleIndex = mockAlertRules.findIndex(r => r.id === ruleId);
		if (ruleIndex === -1)
		{
			return NextResponse.json(
				{ error: 'Alert rule not found' },
				{ status: 404 },
			);
		}

		// Update rule (mock implementation)
		const updatedRule: AlertRuleType = {
			...mockAlertRules[ruleIndex],
			...updateData,
			updatedAt: new Date(),
		};

		mockAlertRules[ruleIndex] = updatedRule;

		return NextResponse.json({
			success: true,
			rule: updatedRule,
			message: 'Alert rule updated successfully',
		});
	}
	catch (error)
	{
		logger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error updating alert rule:');
		return NextResponse.json(
			{ error: 'Failed to update alert rule' },
			{ status: 500 },
		);
	}
}

export async function DELETE(
	request: NextRequest,
	{ params }: { params: { ruleId: string } },
)
{
	try
	{
		const { ruleId } = params;
		const ruleIndex = mockAlertRules.findIndex(r => r.id === ruleId);

		if (ruleIndex === -1)
		{
			return NextResponse.json(
				{ error: 'Alert rule not found' },
				{ status: 404 },
			);
		}

		// Delete rule (mock implementation)
		mockAlertRules.splice(ruleIndex, 1);

		return NextResponse.json({
			success: true,
			message: 'Alert rule deleted successfully',
		});
	}
	catch (error)
	{
		logger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error deleting alert rule:');
		return NextResponse.json(
			{ error: 'Failed to delete alert rule' },
			{ status: 500 },
		);
	}
}
