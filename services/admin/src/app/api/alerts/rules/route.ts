import { NextRequest, NextResponse } from 'next/server';
import { alertLogger } from '@/lib/logger';

import type {
	AlertRuleType,
	AlertRuleCreateRequestType,
	AlertRuleUpdateRequestType,
} from '@/types/alerts';

// Mock data for development - replace with actual database queries
const mockAlertRules: AlertRuleType[] = [
	{
		id: 'rule-1',
		name: 'High Error Rate',
		description: 'Triggers when service error rate exceeds threshold',
		enabled: true,
		severity: 'critical',
		conditions: [
			{
				id: 'cond-1',
				metric: 'error_rate',
				operator: '>',
				threshold: 5.0,
				duration: 5,
				service: 'crawler',
			},
		],
		logicalOperator: 'AND',
		timeBasedConditions: {
			activeHours: { start: '00:00', end: '23:59' },
			activeDays: [0, 1, 2, 3, 4, 5, 6],
			timezone: 'UTC',
		},
		dependencies: [],
		suppressionRules: {
			maintenanceWindows: true,
			dependencyBased: true,
			smartGrouping: false,
		},
		createdAt: new Date('2024-01-01'),
		updatedAt: new Date('2024-01-15'),
		createdBy: 'admin',
		tags: ['production', 'critical'],
	},
	{
		id: 'rule-2',
		name: 'Database Connection Pool Exhaustion',
		description: 'Monitors database connection pool usage',
		enabled: true,
		severity: 'high',
		conditions: [
			{
				id: 'cond-2',
				metric: 'database_connections',
				operator: '>',
				threshold: 90,
				duration: 3,
				resource: 'scylla',
			},
		],
		logicalOperator: 'AND',
		dependencies: [],
		suppressionRules: {
			maintenanceWindows: true,
			dependencyBased: false,
			smartGrouping: true,
		},
		createdAt: new Date('2024-01-02'),
		updatedAt: new Date('2024-01-10'),
		createdBy: 'admin',
		tags: ['database', 'infrastructure'],
	},
	{
		id: 'rule-3',
		name: 'Slow Response Time',
		description: 'Alerts when service response time is too high',
		enabled: false,
		severity: 'medium',
		conditions: [
			{
				id: 'cond-3',
				metric: 'response_time',
				operator: '>',
				threshold: 1000,
				duration: 10,
			},
		],
		logicalOperator: 'AND',
		dependencies: [],
		suppressionRules: {
			maintenanceWindows: true,
			dependencyBased: false,
			smartGrouping: false,
		},
		createdAt: new Date('2024-01-03'),
		updatedAt: new Date('2024-01-03'),
		createdBy: 'admin',
		tags: ['performance'],
	},
];

export async function GET(request: NextRequest)
{
	try
	{
		const { searchParams } = new URL(request.url);
		const enabled = searchParams.get('enabled');
		const severity = searchParams.get('severity');
		const tags = searchParams.get('tags');

		let filteredRules = [...mockAlertRules];

		if (enabled !== null)
		{
			filteredRules = filteredRules.filter(rule => rule.enabled === (enabled === 'true'));
		}

		if (severity)
		{
			const severityList = severity.split(',');
			filteredRules = filteredRules.filter(rule => severityList.includes(rule.severity));
		}

		if (tags)
		{
			const tagList = tags.split(',');
			filteredRules = filteredRules.filter(rule => rule.tags.some(tag => tagList.includes(tag)));
		}

		return NextResponse.json({
			rules: filteredRules,
			total: filteredRules.length,
		});
	}
	catch (error)
	{
		logger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error fetching alert rules:');
		return NextResponse.json(
			{ error: 'Failed to fetch alert rules' },
			{ status: 500 },
		);
	}
}

export async function POST(request: NextRequest)
{
	try
	{
		const ruleData: AlertRuleCreateRequestType = await request.json();

		// Validate required fields
		if (!ruleData.name || !ruleData.conditions || ruleData.conditions.length === 0)
		{
			return NextResponse.json(
				{ error: 'Name and at least one condition are required' },
				{ status: 400 },
			);
		}

		// Create new rule (mock implementation)
		const newRule: AlertRuleType = {
			id: `rule-${Date.now()}`,
			...ruleData,
			createdAt: new Date(),
			updatedAt: new Date(),
		};

		// In a real implementation, save to database
		mockAlertRules.push(newRule);

		return NextResponse.json({
			success: true,
			rule: newRule,
			message: 'Alert rule created successfully',
		});
	}
	catch (error)
	{
		logger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error creating alert rule:');
		return NextResponse.json(
			{ error: 'Failed to create alert rule' },
			{ status: 500 },
		);
	}
}
