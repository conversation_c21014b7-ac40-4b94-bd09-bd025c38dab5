import { NextRequest, NextResponse } from 'next/server';
import { alertLogger } from '@/lib/logger';

import type {
	AlertInstanceType,
	AlertFilterType,
	AlertSortType,
	PaginationType,
	AlertDashboardStatsType,
	AlertStatusType,
	AlertSeverityType,
} from '@/types/alerts';

// Mock data for development - replace with actual database queries
const mockAlerts: AlertInstanceType[] = [
	{
		id: '1',
		ruleId: 'rule-1',
		ruleName: 'High Error Rate',
		severity: 'critical',
		status: 'active',
		message: 'Error rate exceeded 5% threshold',
		details: {
			service: 'crawler',
			errorRate: 7.2,
			threshold: 5.0,
			duration: 15,
		},
		triggeredAt: new Date(Date.now() - 30 * 60 * 1000),
		escalationLevel: 1,
		notificationsSent: [
			{
				channel: 'email',
				recipient: '<EMAIL>',
				sentAt: new Date(Date.now() - 25 * 60 * 1000),
				success: true,
			},
		],
		tags: ['production', 'crawler'],
		affectedServices: ['crawler'],
		metrics: {
			errorRate: 7.2,
			requestCount: 1000,
		},
	},
	{
		id: '2',
		ruleId: 'rule-2',
		ruleName: 'Database Connection Issues',
		severity: 'high',
		status: 'acknowledged',
		message: 'ScyllaDB connection pool exhausted',
		details: {
			service: 'database',
			activeConnections: 95,
			maxConnections: 100,
			threshold: 90,
		},
		triggeredAt: new Date(Date.now() - 60 * 60 * 1000),
		acknowledgedAt: new Date(Date.now() - 45 * 60 * 1000),
		acknowledgedBy: 'admin',
		escalationLevel: 0,
		notificationsSent: [
			{
				channel: 'slack',
				recipient: '#alerts',
				sentAt: new Date(Date.now() - 55 * 60 * 1000),
				success: true,
			},
		],
		tags: ['database', 'scylla'],
		affectedServices: ['web-app', 'crawler'],
		metrics: {
			activeConnections: 95,
			responseTime: 250,
		},
	},
];

export async function GET(request: NextRequest)
{
	try
	{
		const { searchParams } = new URL(request.url);
		const action = searchParams.get('action');

		if (action === 'stats')
		{
			const stats: AlertDashboardStatsType = {
				activeAlerts: mockAlerts.filter(a => a.status === 'active').length,
				criticalAlerts: mockAlerts.filter(a => a.severity === 'critical').length,
				acknowledgedAlerts: mockAlerts.filter(a => a.status === 'acknowledged').length,
				resolvedToday: 5,
				avgResolutionTime: 45,
				escalatedAlerts: mockAlerts.filter(a => a.escalationLevel > 0).length,
				silencedAlerts: 0,
				maintenanceWindows: 1,
				recentAlerts: mockAlerts.slice(0, 5),
				topServices: [
					{ service: 'crawler', alertCount: 3, criticalCount: 1 },
					{ service: 'database', alertCount: 2, criticalCount: 0 },
					{ service: 'ranking-engine', alertCount: 1, criticalCount: 0 },
				],
			};

			return NextResponse.json(stats);
		}

		// Parse filters
		const filters: AlertFilterType = {};
		const status = searchParams.get('status');
		if (status)
		{
			const parts = status.split(',');
			const allowed = ['active', 'acknowledged', 'resolved', 'silenced'];
			const parsed = parts.filter(s => allowed.includes(s)) as AlertStatusType[];
			if (parsed.length > 0)
			{
				filters.status = parsed;
			}
		}

		const severity = searchParams.get('severity');
		if (severity)
		{
			const parts = severity.split(',');
			const allowed = ['low', 'medium', 'high', 'critical'];
			const parsed = parts.filter(s => allowed.includes(s)) as AlertSeverityType[];
			if (parsed.length > 0)
			{
				filters.severity = parsed;
			}
		}

		const services = searchParams.get('services');
		if (services)
		{
			filters.services = services.split(',');
		}

		const search = searchParams.get('search');
		if (search)
		{
			filters.search = search;
		}

		// Parse sorting
		const sortFieldParam = searchParams.get('sortField');
		const sortDirParam = searchParams.get('sortDirection');
		const allowedSortFields = ['triggeredAt', 'severity', 'status', 'ruleName', 'service'] as const;
		const sortField = (sortFieldParam && (allowedSortFields as readonly string[]).includes(sortFieldParam))
			? (sortFieldParam as AlertSortType['field'])
			: 'triggeredAt';
		const sort: AlertSortType = {
			field: sortField,
			direction: (sortDirParam === 'asc' || sortDirParam === 'desc') ? sortDirParam : 'desc',
		};

		// Parse pagination
		const pagination: PaginationType = {
			page: parseInt(searchParams.get('page') || '1'),
			limit: parseInt(searchParams.get('limit') || '20'),
		};

		// Apply filters (mock implementation)
		let filteredAlerts = [...mockAlerts];

		if (filters.status?.length)
		{
			filteredAlerts = filteredAlerts.filter(alert => filters.status!.includes(alert.status));
		}

		if (filters.severity?.length)
		{
			filteredAlerts = filteredAlerts.filter(alert => filters.severity!.includes(alert.severity));
		}

		if (filters.services?.length)
		{
			filteredAlerts = filteredAlerts.filter(alert => alert.affectedServices.some(service => filters.services!.includes(service)));
		}

		if (filters.search)
		{
			const searchLower = filters.search.toLowerCase();
			filteredAlerts = filteredAlerts.filter(alert => alert.message.toLowerCase().includes(searchLower) ||
        alert.ruleName.toLowerCase().includes(searchLower));
		}

		// Apply sorting
		const getSortValue = (alert: AlertInstanceType, field: AlertSortType['field']): string | number | Date =>
		{
			switch (field)
			{
				case 'triggeredAt':
					return alert.triggeredAt;
				case 'severity':
					return alert.severity;
				case 'status':
					return alert.status;
				case 'ruleName':
					return alert.ruleName;
				case 'service':
					return alert.affectedServices[0] || '';
				default:
					return '';
			}
		};

		filteredAlerts.sort((a, b) =>
		{
			const aValue = getSortValue(a, sort.field);
			const bValue = getSortValue(b, sort.field);

			if (sort.direction === 'asc')
			{
				return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
			}

			return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
		});

		// Apply pagination
		const total = filteredAlerts.length;
		const startIndex = (pagination.page - 1) * pagination.limit;
		const endIndex = startIndex + pagination.limit;
		const paginatedAlerts = filteredAlerts.slice(startIndex, endIndex);

		return NextResponse.json({
			alerts: paginatedAlerts,
			pagination: {
				...pagination,
				total,
			},
		});
	}
	catch (error)
	{
		alertLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error fetching alerts');
		return NextResponse.json(
			{ error: 'Failed to fetch alerts' },
			{ status: 500 },
		);
	}
}

export async function POST(request: NextRequest)
{
	try
	{
		const body = await request.json();
		const { action } = body;

		switch (action)
		{
			case 'acknowledge':
			{
				const { alertIds, reason } = body;
				// Mock implementation - acknowledge alerts
				alertLogger.info({ alertIds, reason }, 'Acknowledging alerts');

				// Update alert status and record history
				for (const alertId of alertIds)
				{
					const alert = mockAlerts.find(a => a.id === alertId);
					if (alert)
					{
						alert.status = 'acknowledged';
						alert.acknowledgedAt = new Date();
						alert.acknowledgedBy = 'admin'; // Would get from session
					}
				}

				return NextResponse.json({
					success: true,
					message: `Acknowledged ${alertIds.length} alerts`,
				});
			}

			case 'resolve':
			{
				const { alertIds, reason, resolution } = body;
				// Mock implementation - resolve alerts
				alertLogger.info({ alertIds, reason, resolution }, 'Resolving alerts');

				// Update alert status and record history
				for (const alertId of alertIds)
				{
					const alert = mockAlerts.find(a => a.id === alertId);
					if (alert)
					{
						alert.status = 'resolved';
						alert.resolvedAt = new Date();
						alert.resolvedBy = 'admin'; // Would get from session
					}
				}

				return NextResponse.json({
					success: true,
					message: `Resolved ${alertIds.length} alerts`,
				});
			}

			case 'silence':
			{
				const { alertIds, duration, reason } = body;
				// Mock implementation - silence alerts
				alertLogger.info({ alertIds, duration, reason }, 'Silencing alerts');

				// Update alert status
				for (const alertId of alertIds)
				{
					const alert = mockAlerts.find(a => a.id === alertId);
					if (alert)
					{
						alert.status = 'silenced';
					}
				}

				return NextResponse.json({
					success: true,
					message: `Silenced ${alertIds.length} alerts for ${duration} minutes`,
				});
			}

			case 'bulk_test':
			{
				const { integrationIds, testType } = body;
				// Mock implementation - bulk test integrations
				alertLogger.info({ integrationIds, testType }, 'Running bulk test');

				return NextResponse.json({
					success: true,
					message: `Started bulk test for ${integrationIds.length} integrations`,
					testId: `bulk-test-${Date.now()}`,
				});
			}

			default:
				return NextResponse.json(
					{ error: 'Invalid action' },
					{ status: 400 },
				);
		}
	}
	catch (error)
	{
		alertLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error processing alert action');
		return NextResponse.json(
			{ error: 'Failed to process alert action' },
			{ status: 500 },
		);
	}
}
