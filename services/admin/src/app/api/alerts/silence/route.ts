import { NextRequest, NextResponse } from 'next/server';
import { alertLogger } from '@/lib/logger';

import type {
	AlertSilenceType,
	AlertSilenceCreateRequestType,
	MaintenanceWindowType,
	MaintenanceWindowCreateRequestType,
} from '@/types/alerts';

// Mock data for development
const mockSilences: AlertSilenceType[] = [];
const mockMaintenanceWindows: MaintenanceWindowType[] = [];

export async function GET(request: NextRequest)
{
	try
	{
		const { searchParams } = new URL(request.url);
		const type = searchParams.get('type') || 'silences';

		if (type === 'maintenance')
		{
			const activeWindows = mockMaintenanceWindows.filter((window) =>
			{
				const now = new Date();
				return window.isActive && now >= window.startTime && now <= window.endTime;
			});

			return NextResponse.json({
				maintenanceWindows: mockMaintenanceWindows,
				activeWindows,
			});
		}

		// Return silences
		const activeSilences = mockSilences.filter((silence) =>
		{
			const now = new Date();
			return silence.isActive && now >= silence.startTime && now <= silence.endTime;
		});

		return NextResponse.json({
			silences: mockSilences,
			activeSilences,
		});
	}
	catch (error)
	{
		logger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error fetching silences:');
		return NextResponse.json(
			{ error: 'Failed to fetch silences' },
			{ status: 500 },
		);
	}
}

export async function POST(request: NextRequest)
{
	try
	{
		const body = await request.json();
		const { type } = body;

		if (type === 'maintenance')
		{
			const windowData: MaintenanceWindowCreateRequestType = body;

			const newWindow: MaintenanceWindowType = {
				...windowData,
				id: `maint-${Date.now()}`,
				isActive: true,
			};

			mockMaintenanceWindows.push(newWindow);

			return NextResponse.json({
				success: true,
				maintenanceWindow: newWindow,
				message: 'Maintenance window created successfully',
			});
		}

		// Create silence
		const silenceData: AlertSilenceCreateRequestType = body;

		const newSilence: AlertSilenceType = {
			...silenceData,
			id: `silence-${Date.now()}`,
			isActive: true,
		};

		mockSilences.push(newSilence);

		return NextResponse.json({
			success: true,
			silence: newSilence,
			message: 'Alert silence created successfully',
		});
	}
	catch (error)
	{
		logger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error creating silence:');
		return NextResponse.json(
			{ error: 'Failed to create silence' },
			{ status: 500 },
		);
	}
}

export async function DELETE(request: NextRequest)
{
	try
	{
		const { searchParams } = new URL(request.url);
		const silenceId = searchParams.get('silenceId');
		const maintenanceId = searchParams.get('maintenanceId');

		if (maintenanceId)
		{
			const windowIndex = mockMaintenanceWindows.findIndex(w => w.id === maintenanceId);
			if (windowIndex === -1)
			{
				return NextResponse.json(
					{ error: 'Maintenance window not found' },
					{ status: 404 },
				);
			}

			mockMaintenanceWindows[windowIndex].isActive = false;

			return NextResponse.json({
				success: true,
				message: 'Maintenance window ended successfully',
			});
		}

		if (silenceId)
		{
			const silenceIndex = mockSilences.findIndex(s => s.id === silenceId);
			if (silenceIndex === -1)
			{
				return NextResponse.json(
					{ error: 'Silence not found' },
					{ status: 404 },
				);
			}

			mockSilences[silenceIndex].isActive = false;

			return NextResponse.json({
				success: true,
				message: 'Alert silence removed successfully',
			});
		}

		return NextResponse.json(
			{ error: 'Silence ID or Maintenance ID required' },
			{ status: 400 },
		);
	}
	catch (error)
	{
		logger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error removing silence:');
		return NextResponse.json(
			{ error: 'Failed to remove silence' },
			{ status: 500 },
		);
	}
}
