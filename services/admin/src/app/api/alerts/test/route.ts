import { NextRequest, NextResponse } from 'next/server';
import { alertLogger } from '@/lib/logger';

import type {
	AlertTestRequestType,
	AlertTestResultType,
	NotificationChannelType,
} from '@/types/alerts';

// Mock data for development
const mockTestResults: AlertTestResultType[] = [];

export async function POST(request: NextRequest)
{
	try
	{
		const testRequest: AlertTestRequestType = await request.json();

		// Validate required fields
		if (!testRequest.ruleId || !testRequest.testType)
		{
			return NextResponse.json(
				{ error: 'Rule ID and test type are required' },
				{ status: 400 },
			);
		}

		// Create test result (mock implementation)
		const testResult: AlertTestResultType = {
			id: `test-${Date.now()}`,
			ruleId: testRequest.ruleId,
			testType: testRequest.testType,
			status: 'running',
			startedAt: new Date(),
			results: [],
			notifications: [],
		};

		mockTestResults.push(testResult);

		// Simulate test execution
		setTimeout(() =>
		{
			const result = mockTestResults.find(r => r.id === testResult.id);
			if (result)
			{
				result.status = 'passed';
				result.completedAt = new Date();

				switch (testRequest.testType)
				{
					case 'mock_trigger':
						result.results = [
							{
								step: 'Rule Evaluation',
								status: 'passed',
								message: 'Alert rule conditions evaluated successfully',
								duration: 50,
							},
							{
								step: 'Alert Generation',
								status: 'passed',
								message: 'Alert instance created successfully',
								duration: 25,
							},
						];
						break;

					case 'delivery_test':
						result.results = [
							{
								step: 'Template Rendering',
								status: 'passed',
								message: 'Notification template rendered successfully',
								duration: 30,
							},
							{
								step: 'Channel Delivery',
								status: 'passed',
								message: 'Notifications sent to all channels',
								duration: 200,
							},
						];
						result.notifications = [
							{
								channel: 'email' as NotificationChannelType,
								recipient: '<EMAIL>',
								delivered: true,
								responseTime: 150,
							},
							{
								channel: 'slack' as NotificationChannelType,
								recipient: '#alerts',
								delivered: true,
								responseTime: 100,
							},
						];
						break;

					case 'end_to_end':
						result.results = [
							{
								step: 'Rule Evaluation',
								status: 'passed',
								message: 'Alert rule conditions evaluated successfully',
								duration: 50,
							},
							{
								step: 'Alert Generation',
								status: 'passed',
								message: 'Alert instance created successfully',
								duration: 25,
							},
							{
								step: 'Escalation Processing',
								status: 'passed',
								message: 'Escalation policy applied successfully',
								duration: 75,
							},
							{
								step: 'Notification Delivery',
								status: 'passed',
								message: 'All notifications delivered successfully',
								duration: 300,
							},
						];
						result.notifications = [
							{
								channel: 'email' as NotificationChannelType,
								recipient: '<EMAIL>',
								delivered: true,
								responseTime: 150,
							},
							{
								channel: 'slack' as NotificationChannelType,
								recipient: '#alerts',
								delivered: true,
								responseTime: 100,
							},
							{
								channel: 'sms' as NotificationChannelType,
								recipient: '+1234567890',
								delivered: true,
								responseTime: 250,
							},
						];
						break;
				}
			}
		}, 2000);

		return NextResponse.json({
			success: true,
			testResult,
			message: 'Alert test started successfully',
		});
	}
	catch (error)
	{
		logger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error starting alert test:');
		return NextResponse.json(
			{ error: 'Failed to start alert test' },
			{ status: 500 },
		);
	}
}

export async function GET(request: NextRequest)
{
	try
	{
		const { searchParams } = new URL(request.url);
		const testId = searchParams.get('testId');
		const ruleId = searchParams.get('ruleId');

		if (testId)
		{
			const testResult = mockTestResults.find(r => r.id === testId);
			if (!testResult)
			{
				return NextResponse.json(
					{ error: 'Test result not found' },
					{ status: 404 },
				);
			}

			return NextResponse.json({ testResult });
		}

		// Return all test results, optionally filtered by rule ID
		let filteredResults = [...mockTestResults];

		if (ruleId)
		{
			filteredResults = filteredResults.filter(r => r.ruleId === ruleId);
		}

		// Sort by most recent first
		filteredResults.sort((a, b) => new Date(b.startedAt).getTime() - new Date(a.startedAt).getTime());

		return NextResponse.json({
			testResults: filteredResults,
			total: filteredResults.length,
		});
	}
	catch (error)
	{
		logger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error fetching test results:');
		return NextResponse.json(
			{ error: 'Failed to fetch test results' },
			{ status: 500 },
		);
	}
}
