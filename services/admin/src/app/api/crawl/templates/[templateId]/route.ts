import { NextRequest, NextResponse } from 'next/server';
import { apiLogger } from '@/lib/logger';

import { validateSession } from '@/lib/auth/session';
import { DatabaseManager } from '@/lib/database';
import type { CrawlJobTemplate } from '@/types/crawl';

const db = await DatabaseManager.getInstance();

export async function GET(
	request: NextRequest,
	{ params }: { params: { templateId: string } },
)
{
	try
	{
		const session = await validateSession(request);
		if (!session)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		const { templateId } = params;

		const templateQuery = `
      SELECT
        id,
        name,
        description,
        crawl_type,
        priority,
        settings,
        metadata,
        is_default,
        created_by,
        created_at,
        usage_count
      FROM crawl_job_templates
      WHERE id = ?
    `;

		const { rows: templateRows } = await db.mariadb.execute(templateQuery, [templateId]);

		if ((templateRows as RowDataPacket[]).length === 0)
        {
            return NextResponse.json({ error: 'Template not found' }, { status: 404 });
        }

		const row = (templateRows as RowDataPacket[])[0] as RowDataPacket & Record<string, any>;
		const template: CrawlJobTemplate =
		{
			id: row.id,
			name: row.name,
			description: row.description,
			crawlType: row.crawl_type,
			priority: row.priority,
			settings: JSON.parse(row.settings),
			metadata: JSON.parse(row.metadata || '{}'),
			isDefault: Boolean(row.is_default),
			createdBy: row.created_by,
			createdAt: row.created_at,
			usageCount: row.usage_count || 0,
		};

		return NextResponse.json(template);
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error fetching crawl job template:');

		return NextResponse.json(
			{ error: 'Failed to fetch crawl job template' },
			{ status: 500 },
		);
	}
}

export async function PUT(
	request: NextRequest,
	{ params }: { params: { templateId: string } },
)
{
	try
	{
		const session = await validateSession(request);
		if (!session)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		// Check permissions
		if (!session.permissions.includes('crawl:manage'))
		{
			return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
		}

		const { templateId } = params;
		const body = await request.json();

		// Check if template exists
		const existsQuery = 'SELECT created_by FROM crawl_job_templates WHERE id = ?';
		const { rows: existsRows } = await db.mariadb.execute(existsQuery, [templateId]);

		if ((existsRows as RowDataPacket[]).length === 0)
		{
			return NextResponse.json({ error: 'Template not found' }, { status: 404 });
		}

		const createdBy = (existsRows as RowDataPacket[])[0].created_by as string;

		// Check if user can edit this template (creator or admin)
		if (createdBy !== session.username && !session.permissions.includes('crawl:admin'))
		{
			return NextResponse.json({ error: 'Cannot edit template created by another user' }, { status: 403 });
		}

		// Validate settings if provided
		if (body.settings)
		{
			const validationErrors = validateTemplateSettings(body.settings);
			if (validationErrors.length > 0)
			{
				return NextResponse.json(
					{ error: 'Invalid settings', details: validationErrors },
					{ status: 400 },
				);
			}
		}

		// Build update query
		const updateFields: string[] = [];
		const updateValues: any[] = [];

		if (body.name !== undefined)
		{
			updateFields.push('name = ?');
			updateValues.push(body.name);
		}

		if (body.description !== undefined)
		{
			updateFields.push('description = ?');
			updateValues.push(body.description);
		}

		if (body.crawlType !== undefined)
		{
			updateFields.push('crawl_type = ?');
			updateValues.push(body.crawlType);
		}

		if (body.priority !== undefined)
		{
			updateFields.push('priority = ?');
			updateValues.push(body.priority);
		}

		if (body.settings !== undefined)
		{
			updateFields.push('settings = ?');
			updateValues.push(JSON.stringify(body.settings));
		}

		if (body.metadata !== undefined)
		{
			updateFields.push('metadata = ?');
			updateValues.push(JSON.stringify(body.metadata));
		}

		if (body.isDefault !== undefined)
		{
			updateFields.push('is_default = ?');
			updateValues.push(Boolean(body.isDefault));
		}

		if (updateFields.length === 0)
		{
			return NextResponse.json({ error: 'No fields to update' }, { status: 400 });
		}

		updateFields.push('updated_at = NOW()');
		updateValues.push(templateId);

        const updateQuery = `
      UPDATE crawl_job_templates
      SET ${updateFields.join(', ')}
      WHERE id = ?
    `;

		await db.mariadb.execute(updateQuery, updateValues);

		// If this is set as default, unset other defaults for the same crawl type
		if (body.isDefault && body.crawlType)
		{
			await db.mariadb.execute(
				'UPDATE crawl_job_templates SET is_default = FALSE WHERE crawl_type = ? AND id != ?',
				[body.crawlType, templateId],
			);
		}

		return NextResponse.json({ success: true });
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error updating crawl job template:');
		return NextResponse.json(
			{ error: 'Failed to update crawl job template' },
			{ status: 500 },
		);
	}
}

export async function DELETE(
	request: NextRequest,
	{ params }: { params: { templateId: string } },
)
{
	try
	{
		const session = await validateSession(request);
		if (!session)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		// Check permissions
		if (!session.permissions.includes('crawl:manage'))
		{
			return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
		}

		const { templateId } = params;

		// Check if template exists and get details
		const templateQuery = 'SELECT created_by, usage_count FROM crawl_job_templates WHERE id = ?';
		const { rows: delRows } = await db.mariadb.execute(templateQuery, [templateId]);

		if ((delRows as RowDataPacket[]).length === 0)
		{
			return NextResponse.json({ error: 'Template not found' }, { status: 404 });
		}

		const { created_by, usage_count } = (delRows as RowDataPacket[])[0] as RowDataPacket & { created_by: string; usage_count: number };

		// Check if user can delete this template (creator or admin)
		if (created_by !== session.username && !session.permissions.includes('crawl:admin'))
		{
			return NextResponse.json({ error: 'Cannot delete template created by another user' }, { status: 403 });
		}

		// Warn if template is heavily used
		if (usage_count > 10)
		{
			const { searchParams } = new URL(request.url);
			const force = searchParams.get('force') === 'true';

			if (!force)
			{
				return NextResponse.json(
					{
						error: 'Template is heavily used',
						details: `This template has been used ${usage_count} times. Add ?force=true to delete anyway.`,
						usageCount: usage_count,
					},
					{ status: 409 },
				);
			}
		}

		// Delete template
		await db.mariadb.execute('DELETE FROM crawl_job_templates WHERE id = ?', [templateId]);

		return NextResponse.json({ success: true });
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error deleting crawl job template:');
		return NextResponse.json(
			{ error: 'Failed to delete crawl job template' },
			{ status: 500 },
		);
	}
}

export async function POST(
	request: NextRequest,
	{ params }: { params: { templateId: string } },
)
{
	try
	{
		const session = await validateSession(request);
		if (!session)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		const { templateId } = params;
		const body = await request.json();

		if (body.action === 'use')
		{
			// Increment usage count
			await db.mariadb.execute(
				'UPDATE crawl_job_templates SET usage_count = usage_count + 1 WHERE id = ?',
				[templateId],
			);

			return NextResponse.json({ success: true });
		}
		if (body.action === 'duplicate')
		{
			// Check permissions
			if (!session.permissions.includes('crawl:manage'))
			{
				return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
			}

			// Get original template
			const originalQuery = `
        SELECT name, description, crawl_type, priority, settings, metadata
        FROM crawl_job_templates
        WHERE id = ?
      `;
			const { rows: origRows } = await db.mariadb.execute(originalQuery, [templateId]);

			if ((origRows as RowDataPacket[]).length === 0)
			{
				return NextResponse.json({ error: 'Template not found' }, { status: 404 });
			}

			const original = (origRows as RowDataPacket[])[0] as RowDataPacket & Record<string, any>;

			// Create duplicate
			const duplicateId = `template_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
			const duplicateName = body.name || `${original.name} (Copy)`;

			const insertQuery = `
        INSERT INTO crawl_job_templates (
          id,
          name,
          description,
          crawl_type,
          priority,
          settings,
          metadata,
          is_default,
          created_by,
          created_at,
          usage_count
        ) VALUES (?, ?, ?, ?, ?, ?, ?, FALSE, ?, NOW(), 0)
      `;

			await db.mariadb.execute(insertQuery, [
				duplicateId,
				duplicateName,
				original.description,
				original.crawl_type,
				original.priority,
				original.settings,
				original.metadata,
				session.username,
			]);

			const duplicate: CrawlJobTemplate = {
				id: duplicateId,
				name: duplicateName,
				description: original.description,
				crawlType: original.crawl_type,
				priority: original.priority,
				settings: JSON.parse(original.settings),
				metadata: JSON.parse(original.metadata || '{}'),
				isDefault: false,
				createdBy: session.username,
				createdAt: new Date(),
				usageCount: 0,
			};

			return NextResponse.json(duplicate, { status: 201 });
		}

		return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error processing template action:');
		return NextResponse.json(
			{ error: 'Failed to process template action' },
			{ status: 500 },
		);
	}
}

function validateTemplateSettings(settings: any): string[]
{
	const errors: string[] = [];

	if (typeof settings !== 'object' || settings === null)
	{
		errors.push('Settings must be an object');
		return errors;
	}

	// Validate required fields
	if (typeof settings.rateLimit !== 'number' || settings.rateLimit <= 0)
	{
		errors.push('Rate limit must be a positive number');
	}

	if (typeof settings.concurrentRequests !== 'number' || settings.concurrentRequests <= 0)
	{
		errors.push('Concurrent requests must be a positive number');
	}

	if (typeof settings.timeout !== 'number' || settings.timeout <= 0)
	{
		errors.push('Timeout must be a positive number');
	}

	if (typeof settings.retryAttempts !== 'number' || settings.retryAttempts < 0)
	{
		errors.push('Retry attempts must be a non-negative number');
	}

	if (typeof settings.retryBackoff !== 'number' || settings.retryBackoff <= 0)
	{
		errors.push('Retry backoff must be a positive number');
	}

	if (typeof settings.userAgent !== 'string' || settings.userAgent.trim() === '')
	{
		errors.push('User agent must be a non-empty string');
	}

	if (typeof settings.followRedirects !== 'boolean')
	{
		errors.push('Follow redirects must be a boolean');
	}

	if (typeof settings.maxDepth !== 'number' || settings.maxDepth < 0)
	{
		errors.push('Max depth must be a non-negative number');
	}

	if (typeof settings.respectRobots !== 'boolean')
	{
		errors.push('Respect robots must be a boolean');
	}

	if (!Array.isArray(settings.modules))
	{
		errors.push('Modules must be an array');
	}
	else
	{
		const validModules = ['homepage', 'performance', 'security', 'seo', 'technical', 'screenshots', 'dns', 'robots', 'ssl'];
		for (const module of settings.modules)
		{
			if (typeof module !== 'string' || !validModules.includes(module))
			{
				errors.push(`Invalid module: ${module}`);
			}
		}
	}

	return errors;
}
