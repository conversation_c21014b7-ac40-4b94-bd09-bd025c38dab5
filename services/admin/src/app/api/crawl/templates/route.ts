import { NextRequest, NextResponse } from 'next/server';
import { api<PERSON>ogger } from '@/lib/logger';

import { validateSession } from '@/lib/auth/session';
import db from '@/lib/database/client';
import type { CrawlJobTemplate } from '@/types/crawl';

// Using shared singleton database client

export async function GET(request: NextRequest)
{
	try
	{
		const session = await validateSession(request);
		if (!session)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		const { searchParams } = new URL(request.url);
		const includeUsage = searchParams.get('includeUsage') === 'true';

		// Get templates from database
		const templatesQuery = `
      SELECT
        id,
        name,
        description,
        crawl_type,
        priority,
        settings,
        metadata,
        is_default,
        created_by,
        created_at,
        ${includeUsage ? 'usage_count' : '0 as usage_count'}
      FROM crawl_job_templates
      ORDER BY is_default DESC, usage_count DESC, name ASC
    `;

		const templateRows = await db.mariadb.query<{
			id: string;
			name: string;
			description: string;
			crawl_type: string;
			priority: string;
			settings: string;
			metadata: string | null;
			is_default: number | boolean;
			created_by: string;
			created_at: Date;
			usage_count: number;
		}>(templatesQuery);
		const templates: CrawlJobTemplate[] = templateRows.map((row) => ({
			id: row.id,
			name: row.name,
			description: row.description,
			crawlType: row.crawl_type as import('@/types/crawl').CrawlJobType,
			priority: row.priority as import('@/types/crawl').CrawlJobPriority,
			settings: JSON.parse(row.settings) as import('@/types/crawl').CrawlJobSettings,
			metadata: JSON.parse(row.metadata || '{}') as Record<string, unknown>,
			isDefault: Boolean(row.is_default),
			createdBy: row.created_by,
			createdAt: row.created_at,
			usageCount: row.usage_count || 0,
		}));

		return NextResponse.json(templates);
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error fetching crawl job templates:');
		return NextResponse.json(
			{ error: 'Failed to fetch crawl job templates' },
			{ status: 500 },
		);
	}
}

export async function POST(request: NextRequest)
{
	try
	{
		const session = await validateSession(request);
		if (!session)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		// Check permissions
		if (!session.permissions.includes('crawl:manage'))
		{
			return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
		}

		const body = await request.json();

		// Validate required fields
		if (!body.name || !body.crawlType || !body.priority || !body.settings)
		{
			return NextResponse.json(
				{ error: 'Missing required fields: name, crawlType, priority, settings' },
				{ status: 400 },
			);
		}

		// Validate settings structure
		const validationErrors = validateTemplateSettings(body.settings);
		if (validationErrors.length > 0)
		{
			return NextResponse.json(
				{ error: 'Invalid settings', details: validationErrors },
				{ status: 400 },
			);
		}

		// Generate template ID
		const templateId = `template_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

		// Create template
		const insertQuery = `
      INSERT INTO crawl_job_templates (
        id,
        name,
        description,
        crawl_type,
        priority,
        settings,
        metadata,
        is_default,
        created_by,
        created_at,
        usage_count
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), 0)
    `;

		await db.mariadb.execute(insertQuery, [
			templateId,
			body.name,
			body.description || '',
			body.crawlType,
			body.priority,
			JSON.stringify(body.settings),
			JSON.stringify(body.metadata || {}),
			Boolean(body.isDefault),
			session.username,
		]);

		// If this is set as default, unset other defaults for the same crawl type
		if (body.isDefault)
		{
			await db.mariadb.execute(
				'UPDATE crawl_job_templates SET is_default = FALSE WHERE crawl_type = ? AND id != ?',
				[body.crawlType, templateId],
			);
		}

		const template: CrawlJobTemplate = {
			id: templateId,
			name: body.name,
			description: body.description || '',
			crawlType: body.crawlType,
			priority: body.priority,
			settings: body.settings,
			metadata: body.metadata || {},
			isDefault: Boolean(body.isDefault),
			createdBy: session.username,
			createdAt: new Date(),
			usageCount: 0,
		};

		return NextResponse.json(template, { status: 201 });
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error creating crawl job template:');
		return NextResponse.json(
			{ error: 'Failed to create crawl job template' },
			{ status: 500 },
		);
	}
}

function validateTemplateSettings(settings: any): string[]
{
	const errors: string[] = [];

	if (typeof settings !== 'object' || settings === null)
	{
		errors.push('Settings must be an object');
		return errors;
	}

	// Validate required fields
	if (typeof settings.rateLimit !== 'number' || settings.rateLimit <= 0)
	{
		errors.push('Rate limit must be a positive number');
	}

	if (typeof settings.concurrentRequests !== 'number' || settings.concurrentRequests <= 0)
	{
		errors.push('Concurrent requests must be a positive number');
	}

	if (typeof settings.timeout !== 'number' || settings.timeout <= 0)
	{
		errors.push('Timeout must be a positive number');
	}

	if (typeof settings.retryAttempts !== 'number' || settings.retryAttempts < 0)
	{
		errors.push('Retry attempts must be a non-negative number');
	}

	if (typeof settings.retryBackoff !== 'number' || settings.retryBackoff <= 0)
	{
		errors.push('Retry backoff must be a positive number');
	}

	if (typeof settings.userAgent !== 'string' || settings.userAgent.trim() === '')
	{
		errors.push('User agent must be a non-empty string');
	}

	if (typeof settings.followRedirects !== 'boolean')
	{
		errors.push('Follow redirects must be a boolean');
	}

	if (typeof settings.maxDepth !== 'number' || settings.maxDepth < 0)
	{
		errors.push('Max depth must be a non-negative number');
	}

	if (typeof settings.respectRobots !== 'boolean')
	{
		errors.push('Respect robots must be a boolean');
	}

	if (!Array.isArray(settings.modules))
	{
		errors.push('Modules must be an array');
	}
	else
	{
		const validModules = ['homepage', 'performance', 'security', 'seo', 'technical', 'screenshots', 'dns', 'robots', 'ssl'];
		for (const module of settings.modules)
		{
			if (typeof module !== 'string' || !validModules.includes(module))
			{
				errors.push(`Invalid module: ${module}`);
			}
		}
	}

	return errors;
}
