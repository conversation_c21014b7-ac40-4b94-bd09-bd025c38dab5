import { NextRequest, NextResponse } from 'next/server';
import { apiLogger } from '@/lib/logger';

import { validateSession } from '@/lib/auth/session';
import db from '@/lib/database/client';
import type { GlobalCrawlSettings } from '@/types/crawl';


export async function GET(request: NextRequest)
{
	try
	{
		const session = await validateSession(request);
		if (!session)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		// Get current crawl settings from database
		const settingsQuery = `
      SELECT setting_key, setting_value
      FROM system_config
      WHERE setting_key LIKE 'crawl_%'
    `;

		const settingsRows = await db.mariadb.query<{ setting_key: string; setting_value: string }>(settingsQuery);
		const settingsMap = new Map<string, any>();

		for (const row of settingsRows)
		{
			settingsMap.set(row.setting_key, JSON.parse(row.setting_value));
		}

		// Build settings object with defaults
		const settings: GlobalCrawlSettings = {
			rateLimit: {
				global: settingsMap.get('crawl_rate_limit_global') || 1000,
				perDomain: settingsMap.get('crawl_rate_limit_per_domain') || 60,
				burstLimit: settingsMap.get('crawl_rate_limit_burst') || 100,
			},
			concurrency: {
				maxConcurrentJobs: settingsMap.get('crawl_max_concurrent_jobs') || 10,
				maxConcurrentRequestsPerJob: settingsMap.get('crawl_max_concurrent_requests_per_job') || 5,
				maxConcurrentRequestsGlobal: settingsMap.get('crawl_max_concurrent_requests_global') || 50,
			},
			timeouts: {
				connectionTimeout: settingsMap.get('crawl_connection_timeout') || 30000,
				requestTimeout: settingsMap.get('crawl_request_timeout') || 60000,
				jobTimeout: settingsMap.get('crawl_job_timeout') || 3600000,
			},
			retries: {
				maxRetryAttempts: settingsMap.get('crawl_max_retry_attempts') || 3,
				baseBackoffMs: settingsMap.get('crawl_base_backoff_ms') || 1000,
				maxBackoffMs: settingsMap.get('crawl_max_backoff_ms') || 30000,
				backoffMultiplier: settingsMap.get('crawl_backoff_multiplier') || 2,
			},
			userAgents: settingsMap.get('crawl_user_agents') || [
				'Mozilla/5.0 (compatible; DomainRankingBot/1.0; +https://domainranking.com/bot)',
				'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
				'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
			],
			defaultSettings: {
				rateLimit: settingsMap.get('crawl_default_rate_limit') || 60,
				concurrentRequests: settingsMap.get('crawl_default_concurrent_requests') || 3,
				timeout: settingsMap.get('crawl_default_timeout') || 30000,
				retryAttempts: settingsMap.get('crawl_default_retry_attempts') || 3,
				retryBackoff: settingsMap.get('crawl_default_retry_backoff') || 1000,
				userAgent: settingsMap.get('crawl_default_user_agent') || 'Mozilla/5.0 (compatible; DomainRankingBot/1.0; +https://domainranking.com/bot)',
				followRedirects: settingsMap.get('crawl_default_follow_redirects') || true,
				maxDepth: settingsMap.get('crawl_default_max_depth') || 3,
				respectRobots: settingsMap.get('crawl_default_respect_robots') || true,
				modules: settingsMap.get('crawl_default_modules') || ['homepage', 'performance', 'security', 'seo'],
			},
			resourceLimits: {
				maxMemoryPerJob: settingsMap.get('crawl_max_memory_per_job') || 512 * 1024 * 1024, // 512MB
				maxCpuPerJob: settingsMap.get('crawl_max_cpu_per_job') || 80, // 80%
				maxDiskSpacePerJob: settingsMap.get('crawl_max_disk_space_per_job') || 100 * 1024 * 1024, // 100MB
			},
			maintenance: {
				enabled: settingsMap.get('crawl_maintenance_enabled') || false,
				message: settingsMap.get('crawl_maintenance_message') || 'Crawler is under maintenance',
				allowedUsers: settingsMap.get('crawl_maintenance_allowed_users') || [],
			},
		};

		return NextResponse.json(settings);
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error fetching crawl settings:');
		return NextResponse.json(
			{ error: 'Failed to fetch crawl settings' },
			{ status: 500 },
		);
	}
}

/**
 * Update crawl settings
 */
export async function PUT(request: NextRequest)
{
	try
	{
		const session = await validateSession(request);
		if (!session)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		// Check permissions
		if (!session.permissions.includes('config:update'))
		{
			return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
		}

		const settings: GlobalCrawlSettings = await request.json();

		// Validate settings
		const validationErrors = validateCrawlSettings(settings);
		if (validationErrors.length > 0)
		{
			return NextResponse.json(
				{ error: 'Invalid settings', details: validationErrors },
				{ status: 400 },
			);
		}

		// Update settings in database
		const settingsToUpdate =
		[
			['crawl_rate_limit_global', settings.rateLimit.global],
			['crawl_rate_limit_per_domain', settings.rateLimit.perDomain],
			['crawl_rate_limit_burst', settings.rateLimit.burstLimit],
			['crawl_max_concurrent_jobs', settings.concurrency.maxConcurrentJobs],
			['crawl_max_concurrent_requests_per_job', settings.concurrency.maxConcurrentRequestsPerJob],
			['crawl_max_concurrent_requests_global', settings.concurrency.maxConcurrentRequestsGlobal],
			['crawl_connection_timeout', settings.timeouts.connectionTimeout],
			['crawl_request_timeout', settings.timeouts.requestTimeout],
			['crawl_job_timeout', settings.timeouts.jobTimeout],
			['crawl_max_retry_attempts', settings.retries.maxRetryAttempts],
			['crawl_base_backoff_ms', settings.retries.baseBackoffMs],
			['crawl_max_backoff_ms', settings.retries.maxBackoffMs],
			['crawl_backoff_multiplier', settings.retries.backoffMultiplier],
			['crawl_user_agents', settings.userAgents],
			['crawl_default_rate_limit', settings.defaultSettings.rateLimit],
			['crawl_default_concurrent_requests', settings.defaultSettings.concurrentRequests],
			['crawl_default_timeout', settings.defaultSettings.timeout],
			['crawl_default_retry_attempts', settings.defaultSettings.retryAttempts],
			['crawl_default_retry_backoff', settings.defaultSettings.retryBackoff],
			['crawl_default_user_agent', settings.defaultSettings.userAgent],
			['crawl_default_follow_redirects', settings.defaultSettings.followRedirects],
			['crawl_default_max_depth', settings.defaultSettings.maxDepth],
			['crawl_default_respect_robots', settings.defaultSettings.respectRobots],
			['crawl_default_modules', settings.defaultSettings.modules],
			['crawl_max_memory_per_job', settings.resourceLimits.maxMemoryPerJob],
			['crawl_max_cpu_per_job', settings.resourceLimits.maxCpuPerJob],
			['crawl_max_disk_space_per_job', settings.resourceLimits.maxDiskSpacePerJob],
			['crawl_maintenance_enabled', settings.maintenance.enabled],
			['crawl_maintenance_message', settings.maintenance.message],
			['crawl_maintenance_allowed_users', settings.maintenance.allowedUsers],
		];

		// Use transaction to update all settings
		await db.mariadb.transaction(async (conn) =>
		{
			for (const [key, value] of settingsToUpdate)
			{
				await conn.execute(
					`INSERT INTO system_config (setting_key, setting_value, updated_by, updated_at)
                     VALUES (?, ?, ?, NOW())
                     ON DUPLICATE KEY UPDATE
                     setting_value = VALUES(setting_value),
                     updated_by = VALUES(updated_by),
                     updated_at = VALUES(updated_at)`,
					[key, JSON.stringify(value), session.username],
				);
			}
		});

		// Notify crawler service of settings update
		try
		{
			await fetch('http://worker:3001/api/settings/reload', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ updatedBy: session.username }),
			});
		}
		catch (error)
		{
			apiLogger.warn({ data: error }, 'Failed to notify crawler service of settings update:');
		}

		// Log the configuration change
		await logConfigurationChange('crawl_settings', settings, session.username);

		return NextResponse.json({ success: true });
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error updating crawl settings:');
		return NextResponse.json(
			{ error: 'Failed to update crawl settings' },
			{ status: 500 },
		);
	}
}

/**
 * Validate crawl settings
 */
function validateCrawlSettings(settings: GlobalCrawlSettings): string[]
{
	const errors: string[] = [];

	// Validate rate limits
	if (settings.rateLimit.global <= 0)
	{
		errors.push('Global rate limit must be positive');
	}
	if (settings.rateLimit.perDomain <= 0)
	{
		errors.push('Per-domain rate limit must be positive');
	}
	if (settings.rateLimit.burstLimit <= 0)
	{
		errors.push('Burst limit must be positive');
	}

	// Validate concurrency
	if (settings.concurrency.maxConcurrentJobs <= 0)
	{
		errors.push('Max concurrent jobs must be positive');
	}
	if (settings.concurrency.maxConcurrentRequestsPerJob <= 0)
	{
		errors.push('Max concurrent requests per job must be positive');
	}
	if (settings.concurrency.maxConcurrentRequestsGlobal <= 0)
	{
		errors.push('Max concurrent requests global must be positive');
	}

	// Validate timeouts
	if (settings.timeouts.connectionTimeout <= 0)
	{
		errors.push('Connection timeout must be positive');
	}
	if (settings.timeouts.requestTimeout <= 0)
	{
		errors.push('Request timeout must be positive');
	}
	if (settings.timeouts.jobTimeout <= 0)
	{
		errors.push('Job timeout must be positive');
	}

	// Validate retries
	if (settings.retries.maxRetryAttempts < 0)
	{
		errors.push('Max retry attempts cannot be negative');
	}
	if (settings.retries.baseBackoffMs <= 0)
	{
		errors.push('Base backoff must be positive');
	}
	if (settings.retries.maxBackoffMs <= 0)
	{
		errors.push('Max backoff must be positive');
	}
	if (settings.retries.backoffMultiplier <= 1)
	{
		errors.push('Backoff multiplier must be greater than 1');
	}

	// Validate user agents
	if (!Array.isArray(settings.userAgents) || settings.userAgents.length === 0)
	{
		errors.push('At least one user agent is required');
	}

	// Validate resource limits
	if (settings.resourceLimits.maxMemoryPerJob <= 0)
	{
		errors.push('Max memory per job must be positive');
	}
	if (settings.resourceLimits.maxCpuPerJob <= 0 || settings.resourceLimits.maxCpuPerJob > 100)
	{
		errors.push('Max CPU per job must be between 1 and 100');
	}
	if (settings.resourceLimits.maxDiskSpacePerJob <= 0)
	{
		errors.push('Max disk space per job must be positive');
	}

	return errors;
}

/**
 * Log configuration change
 */
async function logConfigurationChange(configType: string, newConfig: any, userId: string)
{
	try
	{
		const logId = `config_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
		await db.scylla.execute(
			'INSERT INTO configuration_audit (id, config_type, new_config, changed_by, changed_at) VALUES (?, ?, ?, ?, ?)',
			[logId, configType, JSON.stringify(newConfig), userId, new Date()],
		);
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Failed to log configuration change:');
	}
}
