import { NextRequest, NextResponse } from 'next/server';
import { createIsomorphicLogger } from '@shared/client';
import BrowserHttpClient from '@/lib/utils/BrowserHttpClient';

import { apiLogger } from '@/lib/logger';

import { validateSession } from '@/lib/auth/session';
import db from '@/lib/database/client';
import type { CrawlJob, CrawlJobHistory } from '@/types/crawl';

// Using shared singleton database client

export async function GET(
	request: NextRequest,
	{ params }: { params: { jobId: string } },
)
{
	try
	{
		const session = await validateSession(request);
		if (!session)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		const { jobId } = params;
		const { searchParams } = new URL(request.url);
		const includeHistory = searchParams.get('includeHistory') === 'true';
		const includeLogs = searchParams.get('includeLogs') === 'true';

		// Get job details
		const jobQuery = `
      SELECT
        job_id,
        domain,
        crawl_type,
        priority,
        status,
        progress,
        scheduled_at,
        started_at,
        completed_at,
        duration,
        error_message,
        retry_count,
        requested_by,
        metadata
      FROM domain_crawl_jobs
      WHERE job_id = ?
    `;

		const jobResult = await db.scylla.execute(jobQuery, [jobId]);

		if (jobResult.rows.length === 0)
		{
			return NextResponse.json({ error: 'Job not found' }, { status: 404 });
		}

		const row = jobResult.rows[0];
		const job: CrawlJob = {
			jobId: row.job_id,
			domain: row.domain,
			crawlType: row.crawl_type,
			priority: row.priority,
			status: row.status,
			progress: row.progress || 0,
			scheduledAt: row.scheduled_at,
			startedAt: row.started_at,
			completedAt: row.completed_at,
			duration: row.duration,
			errorMessage: row.error_message,
			retryCount: row.retry_count || 0,
			requestedBy: row.requested_by,
			metadata: row.metadata || {},
			logs: [],
		};

		// Get logs if requested
		if (includeLogs)
		{
			try
			{
				const logsQuery = `
          SELECT message, timestamp, level, module
          FROM crawl_job_logs
          WHERE job_id = ?
          ORDER BY timestamp ASC
        `;
				const logsResult = await db.scylla.execute(logsQuery, [jobId]);
				job.logs = logsResult.rows.map((logRow: any) => logRow.message);
			}
			catch (error)
			{
				apiLogger.warn({ error }, 'Failed to fetch job logs');
			}
		}

		const response: any = { job };

		// Get history if requested
		if (includeHistory)
		{
			try
			{
				const historyQuery = `
          SELECT id, timestamp, event, details, user_id
          FROM crawl_job_history
          WHERE job_id = ?
          ORDER BY timestamp DESC
          LIMIT 50
        `;
				const historyResult = await db.scylla.execute(historyQuery, [jobId]);

				const history: CrawlJobHistory = {
					jobId,
					entries: historyResult.rows.map((historyRow: any) => ({
						id: historyRow.id,
						jobId,
						timestamp: historyRow.timestamp,
						event: historyRow.event,
						details: historyRow.details || {},
						userId: historyRow.user_id,
					})),
					pagination: {
						page: 1,
						pageSize: 50,
						total: historyResult.rows.length,
					},
				};

				response.history = history;
			}
			catch (error)
			{
				apiLogger.warn({ error }, 'Failed to fetch job history');
			}
		}

		return NextResponse.json(response);
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error fetching crawl job');
		return NextResponse.json(
			{ error: 'Failed to fetch crawl job' },
			{ status: 500 },
		);
	}
}

export async function PATCH(
	request: NextRequest,
	{ params }: { params: { jobId: string } },
)
{
	try
	{
		const session = await validateSession(request);
		if (!session)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		// Check permissions
		if (!session.permissions.includes('crawl:manage'))
		{
			return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
		}

		const { jobId } = params;
		const body = await request.json();
		const { action, ...updateData } = body;

		// Handle different actions
		switch (action)
		{
			case 'cancel':
				await cancelJob(jobId, session.username);
				break;
			case 'pause':
				await pauseJob(jobId, session.username);
				break;
			case 'resume':
				await resumeJob(jobId, session.username);
				break;
			case 'retry':
				await retryJob(jobId, session.username);
				break;
			case 'update_priority':
				if (!updateData.priority)
				{
					return NextResponse.json({ error: 'Priority is required' }, { status: 400 });
				}
				await updateJobPriority(jobId, updateData.priority, session.username);
				break;
			case 'reschedule':
				if (!updateData.scheduledAt)
				{
					return NextResponse.json({ error: 'Scheduled time is required' }, { status: 400 });
				}
				await rescheduleJob(jobId, new Date(updateData.scheduledAt), session.username);
				break;
			default:
				return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
		}

		// Log the action
		await logJobAction(jobId, action, session.username, updateData);

		return NextResponse.json({ success: true });
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error updating crawl job');
		return NextResponse.json(
			{ error: 'Failed to update crawl job' },
			{ status: 500 },
		);
	}
}

export async function DELETE(
	request: NextRequest,
	{ params }: { params: { jobId: string } },
)
{
	try
	{
		const session = await validateSession(request);
		if (!session)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		// Check permissions
		if (!session.permissions.includes('crawl:delete'))
		{
			return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
		}

		const { jobId } = params;

		// Check if job can be deleted (not running)
		const statusQuery = 'SELECT status FROM domain_crawl_jobs WHERE job_id = ?';
		const statusResult = await db.scylla.execute(statusQuery, [jobId]);

		if (statusResult.rows.length === 0)
		{
			return NextResponse.json({ error: 'Job not found' }, { status: 404 });
		}

		const status = statusResult.rows[0].status;
		if (status === 'running')
		{
			return NextResponse.json(
				{ error: 'Cannot delete running job. Cancel it first.' },
				{ status: 400 },
			);
		}

		// Delete job and related data
		await db.scylla.execute('DELETE FROM domain_crawl_jobs WHERE job_id = ?', [jobId]);
		await db.scylla.execute('DELETE FROM crawl_job_logs WHERE job_id = ?', [jobId]);
		await db.scylla.execute('DELETE FROM crawl_job_history WHERE job_id = ?', [jobId]);

		// Log the deletion
		await logJobAction(jobId, 'delete', session.username, {});

		return NextResponse.json({ success: true });
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error deleting crawl job');
		return NextResponse.json(
			{ error: 'Failed to delete crawl job' },
			{ status: 500 },
		);
	}
}

// Helper functions
async function cancelJob(jobId: string, userId: string)
{
	// Update job status
	await db.scylla.execute(
		'UPDATE domain_crawl_jobs SET status = ? WHERE job_id = ?',
		['cancelled', jobId],
	);

	// Notify crawler service
	try
	{
		const httpClient = new BrowserHttpClient({ timeout: 10000 });
		await httpClient.post(`http://worker:3001/api/crawl/jobs/${jobId}/cancel`, { userId });
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Failed to notify crawler service');
	}
}

async function pauseJob(jobId: string, userId: string)
{
	await db.scylla.execute(
		'UPDATE domain_crawl_jobs SET status = ? WHERE job_id = ?',
		['paused', jobId],
	);

	try
	{
		const httpClient = new BrowserHttpClient({ timeout: 10000 });
		await httpClient.post(`http://worker:3001/api/crawl/jobs/${jobId}/pause`, { userId });
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Failed to notify crawler service');
	}
}

async function resumeJob(jobId: string, userId: string)
{
	await db.scylla.execute(
		'UPDATE domain_crawl_jobs SET status = ? WHERE job_id = ?',
		['pending', jobId],
	);

	try
	{
		const httpClient = new BrowserHttpClient({ timeout: 10000 });
		await httpClient.post(`http://worker:3001/api/crawl/jobs/${jobId}/resume`, { userId });
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Failed to notify crawler service');
	}
}

async function retryJob(jobId: string, userId: string)
{
	// Increment retry count and reset status
	await db.scylla.execute(
		'UPDATE domain_crawl_jobs SET status = ?, retry_count = retry_count + 1, error_message = NULL WHERE job_id = ?',
		['pending', jobId],
	);

	try
	{
		const httpClient = new BrowserHttpClient({ timeout: 10000 });
		await httpClient.post(`http://worker:3001/api/crawl/jobs/${jobId}/retry`, { userId });
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Failed to notify crawler service');
	}
}

async function updateJobPriority(jobId: string, priority: string, userId: string)
{
	await db.scylla.execute(
		'UPDATE domain_crawl_jobs SET priority = ? WHERE job_id = ?',
		[priority, jobId],
	);

	try
	{
		const httpClient = new BrowserHttpClient({ timeout: 10000 });
		await httpClient.post(`http://worker:3001/api/crawl/jobs/${jobId}/priority`, { priority, userId });
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Failed to notify crawler service');
	}
}

async function rescheduleJob(jobId: string, scheduledAt: Date, userId: string)
{
	await db.scylla.execute(
		'UPDATE domain_crawl_jobs SET scheduled_at = ?, status = ? WHERE job_id = ?',
		[scheduledAt, 'pending', jobId],
	);
}

async function logJobAction(jobId: string, action: string, userId: string, details: any)
{
	try
	{
		const historyId = `hist_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
		await db.scylla.execute(
			'INSERT INTO crawl_job_history (id, job_id, timestamp, event, details, user_id) VALUES (?, ?, ?, ?, ?, ?)',
			[historyId, jobId, new Date(), action, JSON.stringify(details), userId],
		);
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Failed to log job action');
	}
}
