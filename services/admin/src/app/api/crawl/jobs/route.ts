import { NextRequest, NextResponse } from 'next/server';
import { apiLogger } from '@/lib/logger';

import { validateSession } from '@/lib/auth/session';
import db from '@/lib/database/client';
import type {
	CrawlJobSearchResult,
	CrawlJobFilters,
	CrawlJobSort,
	CrawlJobCreateRequest,
	CrawlJob,
	CrawlJobStatus,
	CrawlJobType,
	CrawlJobPriority,
	CrawlJobSortField,
} from '@/types/crawl';

export async function GET(request: NextRequest)
{
	try
	{
		const session = await validateSession(request);
		if (!session)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		const { searchParams } = new URL(request.url);

		// Parse filters
		const filters: CrawlJobFilters =
		{
			search: searchParams.get('search') || '',
			status: (searchParams.get('status')?.split(',') as CrawlJobStatus[]) || [],
			crawlType: (searchParams.get('crawlType')?.split(',') as CrawlJobType[]) || [],
			priority: (searchParams.get('priority')?.split(',') as CrawlJobPriority[]) || [],
			dateRange: [
				searchParams.get('startDate') ? new Date(searchParams.get('startDate')!) : null,
				searchParams.get('endDate') ? new Date(searchParams.get('endDate')!) : null,
			],
			domain: searchParams.get('domain') || '',
			requestedBy: searchParams.get('requestedBy') || '',
			hasErrors: searchParams.get('hasErrors') === 'true',
			duration: [
				searchParams.get('minDuration') ? parseInt(searchParams.get('minDuration')!) : null,
				searchParams.get('maxDuration') ? parseInt(searchParams.get('maxDuration')!) : null,
			],
		};

			// Parse sorting
		const sortFieldParam = searchParams.get('sortField');
		const sortDirParam = searchParams.get('sortDirection');
		const allowedSortFields: CrawlJobSortField[] =
		[
			'scheduledAt', 'startedAt', 'completedAt', 'duration', 'priority', 'status', 'domain', 'crawlType', 'progress',
		];
		const sort: CrawlJobSort =
		{
			field: (allowedSortFields as string[]).includes(String(sortFieldParam))
				? (sortFieldParam as CrawlJobSortField)
				: 'scheduledAt',
			direction: (sortDirParam === 'asc' || sortDirParam === 'desc') ? sortDirParam : 'desc',
		};

		// Parse pagination
		const page = parseInt(searchParams.get('page') || '1');
		const pageSize = parseInt(searchParams.get('pageSize') || '20');

		// Build query conditions
		const conditions: string[] = [];
		const params: unknown[] = [];

		if (filters.search)
		{
			conditions.push('(domain LIKE ? OR job_id LIKE ?)');
			params.push(`%${filters.search}%`, `%${filters.search}%`);
		}

		if (filters.status.length > 0)
		{
			conditions.push(`status IN (${filters.status.map(() => '?').join(',')})`);
			params.push(...filters.status);
		}

		if (filters.crawlType.length > 0)
		{
			conditions.push(`crawl_type IN (${filters.crawlType.map(() => '?').join(',')})`);
			params.push(...filters.crawlType);
		}

		if (filters.priority.length > 0)
		{
			conditions.push(`priority IN (${filters.priority.map(() => '?').join(',')})`);
			params.push(...filters.priority);
		}

		if (filters.domain)
		{
			conditions.push('domain = ?');
			params.push(filters.domain);
		}

		if (filters.requestedBy)
		{
			conditions.push('requested_by = ?');
			params.push(filters.requestedBy);
		}

		if (filters.hasErrors)
		{
			conditions.push('error_message IS NOT NULL');
		}

		if (filters.dateRange[0])
		{
			conditions.push('scheduled_at >= ?');
			params.push(filters.dateRange[0]);
		}

		if (filters.dateRange[1])
		{
			conditions.push('scheduled_at <= ?');
			params.push(filters.dateRange[1]);
		}

		if (filters.duration[0] !== null)
		{
			conditions.push('duration >= ?');
			params.push(filters.duration[0] * 1000); // Convert to milliseconds
		}

		if (filters.duration[1] !== null)
		{
			conditions.push('duration <= ?');
			params.push(filters.duration[1] * 1000); // Convert to milliseconds
		}

		const whereClause = conditions.length > 0
			? `WHERE ${conditions.join(' AND ')}`
			: '';

		const countQuery = `
      SELECT COUNT(*) as total
      FROM domain_crawl_jobs
      ${whereClause}
    `;

		const countResult = await db.scylla.execute(countQuery, params);
		const total = countResult.rows[0]?.total || 0;

		const offset = (page - 1) * pageSize;
		const jobsQuery = `
      SELECT
        job_id,
        domain,
        crawl_type,
        priority,
        status,
        progress,
        scheduled_at,
        started_at,
        completed_at,
        duration,
        error_message,
        retry_count,
        requested_by,
        metadata
      FROM domain_crawl_jobs
      ${whereClause}
      ORDER BY ${sort.field} ${sort.direction.toUpperCase()}
      LIMIT ? OFFSET ?
    `;

		const jobsResult = await db.scylla.execute(jobsQuery, [...params, pageSize, offset]);

		const ADMIN_STATUSES = ['pending', 'running', 'completed', 'failed', 'cancelled'] as const;
		type AdminStatus = typeof ADMIN_STATUSES[number];

		const isAdminStatus = (s: unknown): s is AdminStatus =>
		{
			return typeof s === 'string' && (ADMIN_STATUSES as readonly string[]).includes(s);
		};

		const normalizeStatus = (s: unknown): AdminStatus =>
		{
			return isAdminStatus(s) ? s : 'pending';
		};

		const jobs: CrawlJob[] = jobsResult.rows.map((row: Record<string, unknown>) => ({
			jobId: row.job_id as string,
			domain: row.domain as string,
			crawlType: row.crawl_type as CrawlJobType,
			priority: row.priority as CrawlJobPriority,
			status: normalizeStatus(row.status),
			progress: (row.progress as number | null) ?? 0,
			scheduledAt: row.scheduled_at as Date,
			startedAt: row.started_at as Date | null,
			completedAt: row.completed_at as Date | null,
			duration: row.duration as number | null,
			errorMessage: row.error_message as string | null,
			retryCount: (row.retry_count as number | null) ?? 0,
			requestedBy: row.requested_by as string,
			metadata: (row.metadata as Record<string, unknown> | null) ?? {},
			logs: [], // Will be loaded separately if needed
		}));

		const result: CrawlJobSearchResult =
		{
			jobs,
			pagination: { page, pageSize, total },
			filters,
			sort,
		};

		return NextResponse.json(result);
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error fetching crawl jobs:');

		return NextResponse.json(
			{ error: 'Failed to fetch crawl jobs' },
			{ status: 500 },
		);
	}
}

export async function POST(request: NextRequest)
{
	try
	{
		const session = await validateSession(request);
		if (!session)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		// Check permissions
		if (!session.permissions.includes('crawl:create'))
		{
			return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
		}

		const body: CrawlJobCreateRequest = await request.json();

		// Validate required fields
		if (!body.domain || !body.crawlType || !body.priority)
		{
			return NextResponse.json(
				{ error: 'Missing required fields: domain, crawlType, priority' },
				{ status: 400 },
			);
		}

		// Validate domain format
		const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9])*$/;

		if (!domainRegex.test(body.domain))
		{
			return NextResponse.json(
				{ error: 'Invalid domain format' },
				{ status: 400 },
			);
		}

		// Generate job ID
		const jobId = `crawl_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

		// Create crawl job record
		const insertQuery = `
      INSERT INTO domain_crawl_jobs (
        job_id,
        domain,
        crawl_type,
        priority,
        status,
        progress,
        scheduled_at,
        requested_by,
        metadata,
        retry_count
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

		const scheduledAt = body.scheduledAt || new Date();
		const metadata = {
			...body.metadata,
			settings: body.settings,
			createdViaAdmin: true,
		};

		await db.scylla.execute(insertQuery, [
			jobId,
			body.domain,
			body.crawlType,
			body.priority,
			'pending',
			0,
			scheduledAt,
			session.username,
			JSON.stringify(metadata),
			0,
		]);

		// Trigger crawl job via crawler service
		try
		{
			const crawlerResponse = await fetch('http://worker:3001/api/crawl/trigger', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					jobId,
					domain: body.domain,
					crawlType: body.crawlType,
					priority: body.priority,
					settings: body.settings,
				}),
			});

			if (!crawlerResponse.ok)
			{
				const respText = await crawlerResponse.text();
				apiLogger.error({ error: respText }, 'Failed to trigger crawl job:');
				// Update job status to failed
				await db.scylla.execute(
					'UPDATE domain_crawl_jobs SET status = ?, error_message = ? WHERE job_id = ?',
					['failed', 'Failed to trigger crawl job', jobId],
				);
			}
		}
		catch (error)
		{
			apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error triggering crawl job:');
			// Update job status to failed
			await db.scylla.execute(
				'UPDATE domain_crawl_jobs SET status = ?, error_message = ? WHERE job_id = ?',
				['failed', 'Crawler service unavailable', jobId],
			);
		}

		// Return created job
		const createdJob: CrawlJob =
		{
			jobId,
			domain: body.domain,
			crawlType: body.crawlType,
			priority: body.priority,
			status: 'pending',
			progress: 0,
			scheduledAt,
			startedAt: null,
			completedAt: null,
			duration: null,
			errorMessage: null,
			retryCount: 0,
			requestedBy: session.username,
			metadata,
			logs: [],
		};

		return NextResponse.json(createdJob, { status: 201 });
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error creating crawl job:');
		return NextResponse.json(
			{ error: 'Failed to create crawl job' },
			{ status: 500 },
		);
	}
}
