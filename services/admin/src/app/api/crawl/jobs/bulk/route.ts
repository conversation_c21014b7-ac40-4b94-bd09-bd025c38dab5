import { NextRequest, NextResponse } from 'next/server';
import { apiLogger } from '@/lib/logger';

import { validateSession } from '@/lib/auth/session';
import db from '@/lib/database/client';
import type {
	CrawlJobBulkOperationRequest,
	CrawlJobBulkOperationResult,
} from '@/types/crawl';

// Using shared singleton database client

export async function POST(request: NextRequest)
{
	try
	{
		const session = await validateSession(request);
		if (!session)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		// Check permissions
		if (!session.permissions.includes('crawl:manage'))
		{
			return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
		}

		const body: CrawlJobBulkOperationRequest = await request.json();

		if (!body.action || !body.jobIds || body.jobIds.length === 0)
		{
			return NextResponse.json(
				{ error: 'Missing required fields: action, jobIds' },
				{ status: 400 },
			);
		}

		const result: CrawlJobBulkOperationResult = {
			success: true,
			processedCount: 0,
			failedCount: 0,
			errors: [],
		};

		// Process each job
		for (const jobId of body.jobIds)
		{
			try
			{
				await processBulkAction(body.action, jobId, body.parameters, session.username);
				result.processedCount++;
			}
			catch (error)
			{
				result.failedCount++;
				result.errors.push({
					jobId,
					error: error instanceof Error ? error.message : 'Unknown error',
				});
			}
		}

		// Update overall success status
		result.success = result.failedCount === 0;

		return NextResponse.json(result);
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error processing bulk operation:');
		return NextResponse.json(
			{ error: 'Failed to process bulk operation' },
			{ status: 500 },
		);
	}
}

async function processBulkAction(
	action: string,
	jobId: string,
	parameters: any,
	userId: string,
)
{
	switch (action)
	{
		case 'cancel':
			await cancelJob(jobId, userId);
			break;
		case 'pause':
			await pauseJob(jobId, userId);
			break;
		case 'resume':
			await resumeJob(jobId, userId);
			break;
		case 'retry':
			await retryJob(jobId, userId);
			break;
		case 'delete':
			await deleteJob(jobId, userId);
			break;
		case 'changePriority':
			if (!parameters?.priority)
			{
				throw new Error('Priority parameter is required');
			}
			await updateJobPriority(jobId, parameters.priority, userId);
			break;
		case 'reschedule':
			if (!parameters?.scheduledAt)
			{
				throw new Error('Scheduled time parameter is required');
			}
			await rescheduleJob(jobId, new Date(parameters.scheduledAt), userId);
			break;
		default:
			throw new Error(`Unknown action: ${action}`);
	}

	// Log the action
	await logJobAction(jobId, action, userId, parameters || {});
}

// Helper functions (similar to individual job management)
async function cancelJob(jobId: string, userId: string)
{
	// Check if job can be cancelled
	const statusQuery = 'SELECT status FROM domain_crawl_jobs WHERE job_id = ?';
	const statusResult = await db.scylla.execute(statusQuery, [jobId]);

	if (statusResult.rows.length === 0)
	{
		throw new Error('Job not found');
	}

	const status = statusResult.rows[0].status;
	if (status === 'completed' || status === 'cancelled')
	{
		throw new Error(`Cannot cancel job with status: ${status}`);
	}

	await db.scylla.execute(
		'UPDATE domain_crawl_jobs SET status = ? WHERE job_id = ?',
		['cancelled', jobId],
	);

	// Notify crawler service
	try
	{
		await fetch(`http://worker:3001/api/crawl/jobs/${jobId}/cancel`, {
			method: 'POST',
			headers: { 'Content-Type': 'application/json' },
			body: JSON.stringify({ userId }),
		});
	}
	catch (error)
	{
		apiLogger.warn({ data: error }, 'Failed to notify crawler service:');
	}
}

async function pauseJob(jobId: string, userId: string)
{
	const statusQuery = 'SELECT status FROM domain_crawl_jobs WHERE job_id = ?';
	const statusResult = await db.scylla.execute(statusQuery, [jobId]);

	if (statusResult.rows.length === 0)
	{
		throw new Error('Job not found');
	}

	const status = statusResult.rows[0].status;
	if (status !== 'running' && status !== 'pending')
	{
		throw new Error(`Cannot pause job with status: ${status}`);
	}

	await db.scylla.execute(
		'UPDATE domain_crawl_jobs SET status = ? WHERE job_id = ?',
		['paused', jobId],
	);

	try
	{
		await fetch(`http://worker:3001/api/crawl/jobs/${jobId}/pause`, {
			method: 'POST',
			headers: { 'Content-Type': 'application/json' },
			body: JSON.stringify({ userId }),
		});
	}
	catch (error)
	{
		apiLogger.warn({ data: error }, 'Failed to notify crawler service:');
	}
}

async function resumeJob(jobId: string, userId: string)
{
	const statusQuery = 'SELECT status FROM domain_crawl_jobs WHERE job_id = ?';
	const statusResult = await db.scylla.execute(statusQuery, [jobId]);

	if (statusResult.rows.length === 0)
	{
		throw new Error('Job not found');
	}

	const status = statusResult.rows[0].status;
	if (status !== 'paused')
	{
		throw new Error(`Cannot resume job with status: ${status}`);
	}

	await db.scylla.execute(
		'UPDATE domain_crawl_jobs SET status = ? WHERE job_id = ?',
		['pending', jobId],
	);

	try
	{
		await fetch(`http://worker:3001/api/crawl/jobs/${jobId}/resume`, {
			method: 'POST',
			headers: { 'Content-Type': 'application/json' },
			body: JSON.stringify({ userId }),
		});
	}
	catch (error)
	{
		apiLogger.warn({ data: error }, 'Failed to notify crawler service:');
	}
}

async function retryJob(jobId: string, userId: string)
{
	const statusQuery = 'SELECT status, retry_count FROM domain_crawl_jobs WHERE job_id = ?';
	const statusResult = await db.scylla.execute(statusQuery, [jobId]);

	if (statusResult.rows.length === 0)
	{
		throw new Error('Job not found');
	}

	const { status, retry_count } = statusResult.rows[0];
	if (status !== 'failed')
	{
		throw new Error(`Cannot retry job with status: ${status}`);
	}

	if (retry_count >= 5)
	{
		throw new Error('Maximum retry attempts reached');
	}

	await db.scylla.execute(
		'UPDATE domain_crawl_jobs SET status = ?, retry_count = retry_count + 1, error_message = NULL WHERE job_id = ?',
		['pending', jobId],
	);

	try
	{
		await fetch(`http://worker:3001/api/crawl/jobs/${jobId}/retry`, {
			method: 'POST',
			headers: { 'Content-Type': 'application/json' },
			body: JSON.stringify({ userId }),
		});
	}
	catch (error)
	{
		apiLogger.warn({ data: error }, 'Failed to notify crawler service:');
	}
}

async function deleteJob(jobId: string, userId: string)
{
	const statusQuery = 'SELECT status FROM domain_crawl_jobs WHERE job_id = ?';
	const statusResult = await db.scylla.execute(statusQuery, [jobId]);

	if (statusResult.rows.length === 0)
	{
		throw new Error('Job not found');
	}

	const status = statusResult.rows[0].status;
	if (status === 'running')
	{
		throw new Error('Cannot delete running job. Cancel it first.');
	}

	// Delete job and related data
	await db.scylla.execute('DELETE FROM domain_crawl_jobs WHERE job_id = ?', [jobId]);
	await db.scylla.execute('DELETE FROM crawl_job_logs WHERE job_id = ?', [jobId]);
	await db.scylla.execute('DELETE FROM crawl_job_history WHERE job_id = ?', [jobId]);
}

async function updateJobPriority(jobId: string, priority: string, userId: string)
{
	const validPriorities = ['low', 'medium', 'high'];
	if (!validPriorities.includes(priority))
	{
		throw new Error(`Invalid priority: ${priority}`);
	}

	await db.scylla.execute(
		'UPDATE domain_crawl_jobs SET priority = ? WHERE job_id = ?',
		[priority, jobId],
	);

	try
	{
		await fetch(`http://worker:3001/api/crawl/jobs/${jobId}/priority`, {
			method: 'POST',
			headers: { 'Content-Type': 'application/json' },
			body: JSON.stringify({ priority, userId }),
		});
	}
	catch (error)
	{
		apiLogger.warn({ data: error }, 'Failed to notify crawler service:');
	}
}

async function rescheduleJob(jobId: string, scheduledAt: Date, userId: string)
{
	const statusQuery = 'SELECT status FROM domain_crawl_jobs WHERE job_id = ?';
	const statusResult = await db.scylla.execute(statusQuery, [jobId]);

	if (statusResult.rows.length === 0)
	{
		throw new Error('Job not found');
	}

	const status = statusResult.rows[0].status;
	if (status === 'running')
	{
		throw new Error('Cannot reschedule running job');
	}

	await db.scylla.execute(
		'UPDATE domain_crawl_jobs SET scheduled_at = ?, status = ? WHERE job_id = ?',
		[scheduledAt, 'pending', jobId],
	);
}

async function logJobAction(jobId: string, action: string, userId: string, details: any)
{
	try
	{
		const historyId = `hist_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
		await db.scylla.execute(
			'INSERT INTO crawl_job_history (id, job_id, timestamp, event, details, user_id) VALUES (?, ?, ?, ?, ?, ?)',
			[historyId, jobId, new Date(), action, JSON.stringify(details), userId],
		);
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Failed to log job action:');
	}
}
