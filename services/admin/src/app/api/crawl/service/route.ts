import { NextRequest, NextResponse } from 'next/server';
import { apiLogger } from '@/lib/logger';

import { validateSession } from '@/lib/auth/session';
import db from '@/lib/database/client';
import type { CrawlerServiceStatus, CrawlerServiceControl } from '@/types/crawl';

// Using shared singleton database client

export async function GET(request: NextRequest)
{
	try
	{
		const session = await validateSession(request);
		if (!session)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

			// Try worker service health first and map it to crawler-like status
		let crawlerStatus: CrawlerServiceStatus;

		try
		{
			const statusResponse = await fetch('http://worker:3001/health', {
				method: 'GET',
				headers: { 'Content-Type': 'application/json' },
			});

			if (statusResponse.ok)
			{
				const health = await statusResponse.json();
				crawlerStatus = {
					status: health.status === 'healthy' ? 'running' : 'stopped',
					activeJobs: health.activeJobs ?? 0,
					queuedJobs: health.queuedJobs ?? 0,
					systemLoad: {
						cpu: health.cpuUsage ?? 0,
						memory: health.memoryUsage ?? 0,
						network: 0,
						disk: health.diskUsage ?? 0,
					},
					resourceLimits: {
						maxConcurrentJobs: 10,
						maxMemoryUsage: 80,
						maxCpuUsage: 80,
					},
					lastHealthCheck: new Date(health.lastHealthCheck ?? Date.now()),
					emergencyStopReason: undefined,
				};
			}
			else
			{
				throw new Error('Worker health endpoint unavailable');
			}
		}
		catch (error)
		{
			// Fallback to database information
			const jobCountQuery = `
        SELECT
          status,
          COUNT(*) as count
        FROM domain_crawl_jobs
        WHERE scheduled_at >= NOW() - INTERVAL 1 HOUR
        GROUP BY status
      `;

			const jobCountResult = await db.scylla.execute(jobCountQuery);
			let activeJobs = 0;
			let queuedJobs = 0;

			for (const row of jobCountResult.rows)
			{
				if (row.status === 'running')
				{
					activeJobs += parseInt(row.count);
				}
				else if (row.status === 'pending')
				{
					queuedJobs += parseInt(row.count);
				}
			}

			crawlerStatus = {
				status: 'stopped',
				activeJobs,
				queuedJobs,
				systemLoad: {
					cpu: 0,
					memory: 0,
					network: 0,
					disk: 0,
				},
				resourceLimits: {
					maxConcurrentJobs: 10,
					maxMemoryUsage: 80,
					maxCpuUsage: 80,
				},
				lastHealthCheck: new Date(),
				emergencyStopReason: 'Service unavailable',
			};
		}

		return NextResponse.json(crawlerStatus);
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error fetching crawler service status:');
		return NextResponse.json(
			{ error: 'Failed to fetch crawler service status' },
			{ status: 500 },
		);
	}
}

export async function POST(request: NextRequest)
{
	try
	{
		const session = await validateSession(request);
		if (!session)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		// Check permissions
		if (!session.permissions.includes('crawl:admin'))
		{
			return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
		}

		const body = await request.json();
		const { action, reason } = body;

		if (!action)
		{
			return NextResponse.json({ error: 'Action is required' }, { status: 400 });
		}

		const validActions = ['pause', 'resume', 'stop', 'emergency_stop', 'restart'];
		if (!validActions.includes(action))
		{
			return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
		}

		// Log the control action
		const control: CrawlerServiceControl = {
			action,
			reason: reason || `Action performed by ${session.username}`,
			userId: session.username,
			timestamp: new Date(),
		};

		await logServiceControl(control);

		// Consolidated worker has no direct control endpoint yet; handle actions via DB updates
		try
		{
			switch (action)
			{
				case 'emergency_stop':
				case 'pause':
					await handleEmergencyAction(action, control.reason || '', session.username);
					break;
				case 'resume':
					await db.scylla.execute(
						'UPDATE domain_crawl_jobs SET status = ? WHERE status = ?',
						['pending', 'paused'],
					);
					break;
				case 'stop':
					await db.scylla.execute(
						'UPDATE domain_crawl_jobs SET status = ?, error_message = ? WHERE status IN (?, ?)',
						['cancelled', control.reason || `Stopped by ${session.username}`, 'running', 'pending'],
					);
					break;
				case 'restart':
					await db.scylla.execute(
						'UPDATE domain_crawl_jobs SET status = ?, error_message = ? WHERE status IN (?, ?)',
						['cancelled', control.reason || `Restart requested by ${session.username}`, 'running', 'pending'],
					);
					await db.scylla.execute(
						'UPDATE domain_crawl_jobs SET status = ? WHERE status = ?',
						['pending', 'paused'],
					);
					break;
				default:
					break;
			}

			return NextResponse.json({
				success: true,
				message: `Service ${action} completed successfully`,
				timestamp: control.timestamp,
			});
		}
		catch (error)
		{
			apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error controlling worker service via DB actions:');
			return NextResponse.json(
				{ error: `Failed to ${action} worker service: ${error instanceof Error ? error.message : 'Unknown error'}` },
				{ status: 500 },
			);
		}
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error controlling crawler service:');
		return NextResponse.json(
			{ error: 'Failed to control crawler service' },
			{ status: 500 },
		);
	}
}

async function logServiceControl(control: CrawlerServiceControl)
{
	try
	{
		const logId = `control_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
		await db.scylla.execute(
			'INSERT INTO crawler_service_controls (id, action, reason, user_id, timestamp) VALUES (?, ?, ?, ?, ?)',
			[logId, control.action, control.reason, control.userId, control.timestamp],
		);
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Failed to log service control:');
	}
}

async function handleEmergencyAction(action: string, reason: string, userId: string)
{
	try
	{
		if (action === 'emergency_stop' || action === 'pause')
		{
			// Cancel all running jobs
			await db.scylla.execute(
				'UPDATE domain_crawl_jobs SET status = ?, error_message = ? WHERE status = ?',
				['cancelled', `Emergency ${action}: ${reason}`, 'running'],
			);

			// Pause all pending jobs
			await db.scylla.execute(
				'UPDATE domain_crawl_jobs SET status = ? WHERE status = ?',
				['paused', 'pending'],
			);

			// Log emergency action for each affected job
			const affectedJobsQuery = 'SELECT job_id FROM domain_crawl_jobs WHERE status IN (?, ?)';
			const affectedJobsResult = await db.scylla.execute(affectedJobsQuery, ['cancelled', 'paused']);

			for (const row of affectedJobsResult.rows)
			{
				const historyId = `hist_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
				await db.scylla.execute(
					'INSERT INTO crawl_job_history (id, job_id, timestamp, event, details, user_id) VALUES (?, ?, ?, ?, ?, ?)',
					[
						historyId,
						row.job_id,
						new Date(),
						`emergency_${action}`,
						JSON.stringify({ reason, automatic: true }),
						userId,
					],
				);
			}
		}
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Failed to handle emergency action:');
		throw error;
	}
}
