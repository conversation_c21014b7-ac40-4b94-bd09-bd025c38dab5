import { NextRequest, NextResponse } from 'next/server';
import { authLogger } from '@/lib/logger';

import { validateUser, createSession } from '@/lib/auth/simple-auth';

export async function POST(request: NextRequest)
{
	try
	{
		const { username, password } = await request.json();

		if (!username || !password)
		{
			return NextResponse.json(
				{ error: 'Username and password required' },
				{ status: 400 },
			);
		}

		const user = await validateUser(username, password);
		if (!user)
		{
			return NextResponse.json(
				{ error: 'Invalid credentials' },
				{ status: 401 },
			);
		}

		const sessionId = createSession(user);

		const response = NextResponse.json({
			success: true,
			user: { id: user.id, username: user.username, role: user.role },
		});

		// Set session cookie
		response.cookies.set('admin-session', sessionId, {
			httpOnly: true,
			secure: process.env.NODE_ENV === 'production',
			sameSite: 'lax',
			maxAge: 8 * 60 * 60, // 8 hours
		});

		return response;
	}
	catch (error)
	{
		authLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Login error:');
		return NextResponse.json(
			{ error: 'Internal server error' },
			{ status: 500 },
		);
	}
}
