import { NextRequest, NextResponse } from 'next/server';

import { getCurrentUser, authService, getSession } from '@/lib/auth';
import { logger } from '@/utils';

async function GET(request: NextRequest)
{
	try
	{
		const user = await getCurrentUser();

		if (!user)
		{
			return NextResponse.json(
				{ authenticated: false },
				{ status: 401 },
			);
		}

		const session = await getSession();

		// Validate session with auth service
		if (session.sessionId)
		{
			const sessionData = await authService.validateSession(session.sessionId);

			if (!sessionData)
			{
				return NextResponse.json(
					{ authenticated: false },
					{ status: 401 },
				);
			}
		}

		return NextResponse.json({
			authenticated: true,
			user,
		});
	}
	catch (error)
	{
		logger.error('Session validation error', { error: error instanceof Error ? error.message : 'Unknown error' });
		return NextResponse.json(
			{ authenticated: false },
			{ status: 500 },
		);
	}
}

export { GET };
