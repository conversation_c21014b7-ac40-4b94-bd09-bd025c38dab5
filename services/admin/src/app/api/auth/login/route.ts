import { NextRequest, NextResponse } from 'next/server';

import { authService, createSession } from '@/lib/auth';
import { validateInput, loginSchema, logger } from '@/utils';

async function POST(request: NextRequest)
{
	try
	{
		const body = await request.json();
		const validation = validateInput(loginSchema, body);

		if (!validation.success)
		{
			logger.warn('Login validation failed', { error: validation.error });
			return NextResponse.json(
				{ message: validation.error },
				{ status: 400 },
			);
		}

		const { username, password } = validation.data;

		// Get client IP and user agent
		const ipAddress = request.headers.get('x-forwarded-for') ||
                     request.headers.get('x-real-ip') ||
                     'unknown';
		const userAgent = request.headers.get('user-agent') || 'unknown';

		// Authenticate user
		const authResult = await authService.authenticate(username, password, ipAddress, userAgent);

		if (!authResult.success)
		{
			logger.warn('Login failed', { username, error: authResult.error, ipAddress });
			return NextResponse.json(
				{ message: authResult.error || 'Authentication failed' },
				{ status: 401 },
			);
		}

		if (!authResult.sessionId)
		{
			logger.error('Session creation failed', { username });
			return NextResponse.json(
				{ message: 'Session creation failed' },
				{ status: 500 },
			);
		}

		// Get session data for iron-session
		const sessionData = await authService.validateSession(authResult.sessionId);

		if (!sessionData)
		{
			logger.error('Session validation failed after creation', { username, sessionId: authResult.sessionId });
			return NextResponse.json(
				{ message: 'Session validation failed' },
				{ status: 500 },
			);
		}

		// Create iron-session
		await createSession(sessionData);

		logger.info('User logged in successfully', {
			username,
			sessionId: authResult.sessionId,
			role: authResult.user?.role,
			ipAddress,
		});

		return NextResponse.json({
			success: true,
			user: authResult.user,
		});
	}
	catch (error)
	{
		logger.error('Login error', { error: error instanceof Error ? error.message : 'Unknown error' });
		return NextResponse.json(
			{ message: 'Internal server error' },
			{ status: 500 },
		);
	}
}

export { POST };
