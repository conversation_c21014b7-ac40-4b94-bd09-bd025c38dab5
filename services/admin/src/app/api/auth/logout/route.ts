import { NextRequest, NextResponse } from 'next/server';

import { getSession, destroySession, authService } from '@/lib/auth';
import { logger } from '@/utils';

async function POST(request: NextRequest)
{
	try
	{
		const session = await getSession();

		if (session.sessionId)
		{
			// Destroy session in Redis
			await authService.logout(session.sessionId);

			logger.info('User logged out', {
				username: session.username,
				sessionId: session.sessionId,
			});
		}

		// Destroy iron-session
		await destroySession();

		return NextResponse.json({ success: true });
	}
	catch (error)
	{
		logger.error('Logout error', { error: error instanceof Error ? error.message : 'Unknown error' });
		return NextResponse.json(
			{ message: 'Logout failed' },
			{ status: 500 },
		);
	}
}

export { POST };
