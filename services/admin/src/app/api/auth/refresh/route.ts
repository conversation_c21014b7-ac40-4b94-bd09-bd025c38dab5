import { NextRequest, NextResponse } from 'next/server';

import { getCurrentUser, authService, getSession } from '@/lib/auth';
import { logger } from '@/utils';

async function POST(request: NextRequest)
{
	try
	{
		const user = await getCurrentUser();

		if (!user)
		{
			return NextResponse.json(
				{ error: 'Authentication required' },
				{ status: 401 },
			);
		}

		const session = await getSession();

		// Refresh session with auth service
		if (session.sessionId)
		{
			await authService.refreshSession(session.sessionId);

			logger.debug('Session refreshed successfully', {
				sessionId: session.sessionId,
				username: user.username,
			});

			return NextResponse.json({
				success: true,
				message: 'Session refreshed',
			});
		}

		return NextResponse.json(
			{ error: 'No active session' },
			{ status: 400 },
		);
	}
	catch (error)
	{
		logger.error('Session refresh error', { error: error instanceof Error ? error.message : 'Unknown error' });
		return NextResponse.json(
			{ error: 'Session refresh failed' },
			{ status: 500 },
		);
	}
}

export { POST };
