import { NextRequest, NextResponse } from 'next/server';

import { authService } from '@/lib/auth';
import { logger } from '@/utils';

async function GET(request: NextRequest)
{
	try
	{
		const username = request.headers.get('x-user-id');
		const role = request.headers.get('x-user-role');
		const permissions = JSON.parse(request.headers.get('x-user-permissions') || '[]');

		if (!username)
		{
			return NextResponse.json(
				{ error: 'Authentication required' },
				{ status: 401 },
			);
		}

		// Only super_admin can view all sessions, others can only view their own
		const targetUsername = request.nextUrl.searchParams.get('username');

		if (targetUsername && targetUsername !== username && !permissions.includes('*'))
		{
			return NextResponse.json(
				{ error: 'Insufficient permissions' },
				{ status: 403 },
			);
		}

		const sessions = await authService.getUserSessions(targetUsername || username);

		logger.info('Sessions requested', {
			requestedBy: username,
			targetUser: targetUsername || username,
			sessionCount: sessions.length,
		});

		return NextResponse.json({ sessions });
	}
	catch (error)
	{
		logger.error('Error fetching sessions', { error: error instanceof Error ? error.message : 'Unknown error' });
		return NextResponse.json(
			{ error: 'Failed to fetch sessions' },
			{ status: 500 },
		);
	}
}

async function DELETE(request: NextRequest)
{
	try
	{
		const username = request.headers.get('x-user-id');
		const permissions = JSON.parse(request.headers.get('x-user-permissions') || '[]');

		if (!username)
		{
			return NextResponse.json(
				{ error: 'Authentication required' },
				{ status: 401 },
			);
		}

		const body = await request.json();
		const { sessionId, targetUsername } = body;

		// Only super_admin can destroy other users' sessions
		if (targetUsername && targetUsername !== username && !permissions.includes('*'))
		{
			return NextResponse.json(
				{ error: 'Insufficient permissions' },
				{ status: 403 },
			);
		}

		if (sessionId)
		{
			await authService.logout(sessionId);
			logger.info('Session destroyed', { destroyedBy: username, sessionId });
		}
		else if (targetUsername)
		{
			await authService.destroyAllUserSessions(targetUsername);
			logger.info('All user sessions destroyed', { destroyedBy: username, targetUser: targetUsername });
		}

		return NextResponse.json({ success: true });
	}
	catch (error)
	{
		logger.error('Error destroying sessions', { error: error instanceof Error ? error.message : 'Unknown error' });
		return NextResponse.json(
			{ error: 'Failed to destroy sessions' },
			{ status: 500 },
		);
	}
}

export { GET, DELETE };
