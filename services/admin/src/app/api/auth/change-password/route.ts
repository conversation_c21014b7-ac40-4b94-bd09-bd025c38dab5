import { hash, argon2id } from 'argon2';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { authService } from '@/lib/auth';
import { validateInput, logger } from '@/utils';
import { validatePassword } from '@/utils/passwordValidation';

const changePasswordSchema = z.object({
	currentPassword: z.string().min(1, 'Current password is required'),
	newPassword: z.string().min(8, 'New password must be at least 8 characters'),
	confirmPassword: z.string().min(1, 'Password confirmation is required'),
}).refine(data => data.newPassword === data.confirmPassword, {
	message: "Passwords don't match",
	path: ['confirmPassword'],
});

async function POST(request: NextRequest)
{
	try
	{
		const username = request.headers.get('x-user-id');

		if (!username)
		{
			return NextResponse.json(
				{ error: 'Authentication required' },
				{ status: 401 },
			);
		}

		const body = await request.json();
		const validation = validateInput(changePasswordSchema, body);

		if (!validation.success)
		{
			return NextResponse.json(
				{ error: validation.error },
				{ status: 400 },
			);
		}

		const { currentPassword, newPassword } = validation.data;

		// Validate new password strength
		const passwordValidation = validatePassword(newPassword);

		if (!passwordValidation.valid)
		{
			return NextResponse.json(
				{
					error: 'Password does not meet security requirements',
					details: passwordValidation.errors,
				},
				{ status: 400 },
			);
		}

		// 1. Verify current password against stored hash
		const currentUser = await authService.validateCredentials(username, currentPassword);
		if (!currentUser.success)
		{
			logger.warn('Invalid current password during password change', { username });
			return NextResponse.json(
				{ error: 'Current password is incorrect' },
				{ status: 401 },
			);
		}

		// 2. Hash new password securely
		const newPasswordHash = await hash(newPassword, {
			type: argon2id,
			memoryCost: 2 ** 16,
			timeCost: 3,
			parallelism: 4,
		});

		// 3. Update password in user store
		const updateResult = await authService.updateUserPassword(username, newPasswordHash);
		if (!updateResult.success)
		{
			logger.error('Failed to update password in store', { username });
			return NextResponse.json(
				{ error: 'Failed to update password' },
				{ status: 500 },
			);
		}

		// 4. Invalidate all existing sessions except current one
		const currentSessionId = request.headers.get('x-session-id');
		await authService.destroyAllUserSessionsExcept(username, currentSessionId);

		// 5. Log the password change event for audit trail
		logger.info('Password changed successfully', {
			username,
			passwordStrength: passwordValidation.strength,
			timestamp: new Date().toISOString(),
			ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
			userAgent: request.headers.get('user-agent'),
		});

		return NextResponse.json({
			success: true,
			message: 'Password changed successfully',
			passwordStrength: passwordValidation.strength,
		});
	}
	catch (error)
	{
		logger.error('Password change error', {
			error: error instanceof Error ? error.message : 'Unknown error',
		});
		return NextResponse.json(
			{ error: 'Failed to change password' },
			{ status: 500 },
		);
	}
}

export { POST };
