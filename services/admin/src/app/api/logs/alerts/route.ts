import { NextRequest, NextResponse } from 'next/server';
import { alertLogger } from '@/lib/logger';
import { validateSession } from '@/lib/auth/session';
import type { LogAlertType, ServiceNameType, LogLevelType } from '../../../../types/logs';

// Mock log alerts storage (legacy services removed as requested)
let mockLogAlerts: LogAlertType[] =
[
	{
		id: 'alert_1',
		name: 'High Error Rate',
		description: 'Alert when error rate exceeds 5% in 5 minutes',
		enabled: true,
		conditions: {
			services: ['web-app', 'admin'],
			levels: ['error', 'fatal'],
			threshold: 10,
			timeWindow: 5,
			operator: 'greater_than',
		},
		actions: {
			email: [process.env.ADMIN_EMAIL || '<EMAIL>'],
			webhook: process.env.ALERT_WEBHOOK_URL || process.env.NEXT_PUBLIC_ALERT_WEBHOOK_URL,
			createIncident: true,
		},
		lastTriggered: new Date(Date.now() - 3600000),
		triggerCount: 23,
		createdAt: new Date(Date.now() - 86400000 * 7),
		createdBy: 'admin',
	},
	{
		id: 'alert_2',
		name: 'Database Connection Failures',
		description: 'Alert on database connection issues',
		enabled: true,
		conditions: {
			services: ['web-app', 'worker', 'domain-seeder'],
			levels: ['error'],
			pattern: 'database.*connection.*failed',
			threshold: 3,
			timeWindow: 10,
			operator: 'greater_than',
		},
		actions: {
			email: ['<EMAIL>', '<EMAIL>'],
			createIncident: true,
		},
		lastTriggered: new Date(Date.now() - 7200000),
		triggerCount: 8,
		createdAt: new Date(Date.now() - 86400000 * 5),
		createdBy: 'admin',
	},
	{
		id: 'alert_3',
		name: 'Crawler Service Down',
		description: 'Alert when crawler service stops logging',
		enabled: false,
		conditions: {
			services: ['crawler'],
			levels: ['debug', 'info', 'warn', 'error', 'fatal'],
			threshold: 1,
			timeWindow: 15,
			operator: 'less_than',
		},
		actions: {
			email: [process.env.OPS_EMAIL || '<EMAIL>'],
			webhook: process.env.ALERT_WEBHOOK_URL || process.env.NEXT_PUBLIC_ALERT_WEBHOOK_URL,
			createIncident: false,
		},
		triggerCount: 0,
		createdAt: new Date(Date.now() - 86400000 * 2),
		createdBy: 'admin',
	},
];

export async function GET(request: NextRequest)
{
	try
	{
		const session = await validateSession(request);
		if (!session)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		return NextResponse.json(mockLogAlerts);
	}
	catch (error)
	{
		alertLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error fetching log alerts:');
		return NextResponse.json(
			{ error: 'Failed to fetch log alerts' },
			{ status: 500 }
		);
	}
}

export async function POST(request: NextRequest)
{
	try
	{
		const session = await validateSession(request);
		if (!session)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		const body = await request.json();
		const { name, description, enabled, conditions, actions } = body;

		// Validate input
		if (!name || !name.trim())
		{
			return NextResponse.json(
				{ error: 'Alert name is required' },
				{ status: 400 }
			);
		}

		if (!conditions || !conditions.services || conditions.services.length === 0)
		{
			return NextResponse.json(
				{ error: 'At least one service must be selected' },
				{ status: 400 }
			);
		}

		if (!conditions.levels || conditions.levels.length === 0)
		{
			return NextResponse.json(
				{ error: 'At least one log level must be selected' },
				{ status: 400 }
			);
		}

		if (!conditions.threshold || conditions.threshold < 1)
		{
			return NextResponse.json(
				{ error: 'Threshold must be at least 1' },
				{ status: 400 }
			);
		}

		if (!conditions.timeWindow || conditions.timeWindow < 1)
		{
			return NextResponse.json(
				{ error: 'Time window must be at least 1 minute' },
				{ status: 400 }
			);
		}

		// Check for duplicate names
		const existingAlert = mockLogAlerts.find(
			alert => alert.name.toLowerCase() === name.toLowerCase()
		);

		if (existingAlert)
		{
			return NextResponse.json(
				{ error: 'An alert with this name already exists' },
				{ status: 409 }
			);
		}

		// Create new alert
		const newAlert: LogAlertType =
		{
			id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
			name: name.trim(),
			description: description?.trim() || '',
			enabled: enabled ?? true,
			conditions,
			actions: actions || {},
			triggerCount: 0,
			createdAt: new Date(),
			createdBy: session.username,
		};

		mockLogAlerts.push(newAlert);

		return NextResponse.json(newAlert, { status: 201 });
	}
	catch (error)
	{
		alertLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error creating log alert:');
		return NextResponse.json(
			{ error: 'Failed to create log alert' },
			{ status: 500 }
		);
	}
}
