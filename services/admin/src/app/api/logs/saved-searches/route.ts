import { NextRequest, NextResponse } from 'next/server';
import { apiLogger } from '@/lib/logger';
import { validateSession } from '@/lib/auth/session';
import type { SavedSearchType, LogFilterType } from '../../../../types/logs';

// Mock saved searches storage
let mockSavedSearches: SavedSearchType[] = [
	{
		id: 'search_1',
		name: 'Critical Errors',
		description: 'All critical and fatal errors across services',
		filter: {
			services: [],
			levels: ['error', 'fatal'],
		},
		createdAt: new Date(Date.now() - 86400000 * 7),
		createdBy: 'admin',
		isPublic: true,
	},
	{
		id: 'search_2',
		name: 'Database Issues',
		description: 'Database connection and query errors',
		filter: {
			services: [],
			levels: ['error', 'warn'],
			searchQuery: 'database|connection|query',
			useRegex: true,
		},
		createdAt: new Date(Date.now() - 86400000 * 3),
		createdBy: 'admin',
		isPublic: true,
	},
	{
		id: 'search_3',
		name: 'Seeder Service Logs',
		description: 'All logs from the domain-seeder service',
		filter: {
			services: ['domain-seeder'],
			levels: [],
		},
		createdAt: new Date(Date.now() - 86400000 * 1),
		createdBy: 'admin',
		isPublic: false,
	},
];

export async function GET(request: NextRequest)
{
	try
	{
		const session = await validateSession(request);
		if (!session)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		// In production, filter by user permissions and public searches
		return NextResponse.json(mockSavedSearches);
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error fetching saved searches:');
		return NextResponse.json(
			{ error: 'Failed to fetch saved searches' },
			{ status: 500 }
		);
	}
}

export async function POST(request: NextRequest)
{
	try
	{
		const session = await validateSession(request);
		if (!session)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		const body = await request.json();
		const { name, description, filter } = body;

		// Validate input
		if (!name || !name.trim())
		{
			return NextResponse.json(
				{ error: 'Search name is required' },
				{ status: 400 }
			);
		}

		if (!filter)
		{
			return NextResponse.json(
				{ error: 'Search filter is required' },
				{ status: 400 }
			);
		}

		// Check for duplicate names
		const existingSearch = mockSavedSearches.find(
			search => search.name.toLowerCase() === name.toLowerCase()
		);

		if (existingSearch)
		{
			return NextResponse.json(
				{ error: 'A search with this name already exists' },
				{ status: 409 }
			);
		}

		// Create new saved search
		const newSearch: SavedSearchType = {
			id: `search_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
			name: name.trim(),
			description: description?.trim() || '',
			filter: filter as LogFilterType,
			createdAt: new Date(),
			createdBy: session.username,
			isPublic: false, // Default to private
		};

		mockSavedSearches.push(newSearch);

		return NextResponse.json(newSearch, { status: 201 });
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error creating saved search:');
		return NextResponse.json(
			{ error: 'Failed to create saved search' },
			{ status: 500 }
		);
	}
}
