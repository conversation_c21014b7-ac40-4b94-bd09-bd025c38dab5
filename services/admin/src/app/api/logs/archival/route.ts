import { NextRequest, NextResponse } from 'next/server';
import { apiLogger } from '@/lib/logger';
import type { LogArchivePolicyType, ServiceNameType, LogLevelType } from '../../../../types/logs';

// Mock archival policies storage
let mockArchivalPolicies: LogArchivePolicyType[] = [
	{
		id: 'policy_1',
		name: 'Standard Log Archival',
		retentionDays: 30,
		compressionEnabled: true,
		services: ['web-app', 'crawler', 'ranking-engine'],
		levels: ['debug', 'info', 'warn', 'error', 'fatal'],
		archiveLocation: '/var/log/archives/standard',
		isActive: true,
		lastRun: new Date(Date.now() - 86400000), // 1 day ago
		nextRun: new Date(Date.now() + 86400000), // 1 day from now
	},
	{
		id: 'policy_2',
		name: 'Error Logs Long-term',
		retentionDays: 90,
		compressionEnabled: true,
		services: ['web-app', 'crawler', 'ranking-engine', 'scheduler', 'domain-seeder'],
		levels: ['error', 'fatal'],
		archiveLocation: '/var/log/archives/errors',
		isActive: true,
		lastRun: new Date(Date.now() - 172800000), // 2 days ago
		nextRun: new Date(Date.now() + 604800000), // 7 days from now
	},
	{
		id: 'policy_3',
		name: 'Debug Logs Cleanup',
		retentionDays: 7,
		compressionEnabled: false,
		services: ['admin'],
		levels: ['debug'],
		archiveLocation: '/var/log/archives/debug',
		isActive: false,
		nextRun: new Date(Date.now() + 86400000), // 1 day from now
	},
];

export async function GET(request: NextRequest)
{
	try
	{
		const session = await validateSession(request);
		if (!session)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		return NextResponse.json(mockArchivalPolicies);
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error fetching archival policies:');
		return NextResponse.json(
			{ error: 'Failed to fetch archival policies' },
			{ status: 500 }
		);
	}
}

export async function POST(request: NextRequest)
{
	try
	{
		const session = await validateSession(request);
		if (!session)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		const body = await request.json();
		const { name, retentionDays, compressionEnabled, services, levels, archiveLocation, isActive } = body;

		// Validate input
		if (!name || !name.trim())
		{
			return NextResponse.json(
				{ error: 'Policy name is required' },
				{ status: 400 }
			);
		}

		if (!services || services.length === 0)
		{
			return NextResponse.json(
				{ error: 'At least one service must be selected' },
				{ status: 400 }
			);
		}

		if (!levels || levels.length === 0)
		{
			return NextResponse.json(
				{ error: 'At least one log level must be selected' },
				{ status: 400 }
			);
		}

		if (!retentionDays || retentionDays < 1)
		{
			return NextResponse.json(
				{ error: 'Retention period must be at least 1 day' },
				{ status: 400 }
			);
		}

		if (!archiveLocation || !archiveLocation.trim())
		{
			return NextResponse.json(
				{ error: 'Archive location is required' },
				{ status: 400 }
			);
		}

		// Check for duplicate names
		const existingPolicy = mockArchivalPolicies.find(
			policy => policy.name.toLowerCase() === name.toLowerCase()
		);

		if (existingPolicy)
		{
			return NextResponse.json(
				{ error: 'A policy with this name already exists' },
				{ status: 409 }
			);
		}

		// Create new policy
		const newPolicy: LogArchivePolicyType = {
			id: `policy_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
			name: name.trim(),
			retentionDays,
			compressionEnabled: compressionEnabled ?? true,
			services,
			levels,
			archiveLocation: archiveLocation.trim(),
			isActive: isActive ?? true,
			nextRun: new Date(Date.now() + 86400000), // Next day
		};

		mockArchivalPolicies.push(newPolicy);

		return NextResponse.json(newPolicy, { status: 201 });
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error creating archival policy:');
		return NextResponse.json(
			{ error: 'Failed to create archival policy' },
			{ status: 500 }
		);
	}
}
