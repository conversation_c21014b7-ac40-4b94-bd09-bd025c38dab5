import { NextRequest, NextResponse } from 'next/server';
import { apiLogger } from '@/lib/logger';
import { validateSession } from '@/lib/auth/session';
import type { ErrorPatternType } from '../../../../types/logs';

// Mock error patterns generation
const generateMockErrorPatterns = (timeRange: string): ErrorPatternType[] =>
{
	const patterns: ErrorPatternType[] = [
		{
			id: 'pattern_1',
			pattern: 'Database connection.*failed',
			description: 'Database connection failures',
			severity: 'critical',
			occurrences: 45,
			firstSeen: new Date(Date.now() - 86400000 * 3), // 3 days ago
			lastSeen: new Date(Date.now() - 3600000), // 1 hour ago
			services: ['web-app', 'crawler', 'ranking-engine'],
			suggestedResolution: 'Check database server status and connection pool configuration',
			isResolved: false,
		},
		{
			id: 'pattern_2',
			pattern: 'Rate limit exceeded.*IP:\\s*(\\d+\\.\\d+\\.\\d+\\.\\d+)',
			description: 'Rate limiting violations',
			severity: 'medium',
			occurrences: 128,
			firstSeen: new Date(Date.now() - 86400000 * 7), // 7 days ago
			lastSeen: new Date(Date.now() - 1800000), // 30 minutes ago
			services: ['web-app', 'admin'],
			suggestedResolution: 'Review rate limiting configuration and consider IP whitelisting',
			isResolved: false,
		},
		{
			id: 'pattern_3',
			pattern: 'Crawl job.*timeout',
			description: 'Crawl job timeouts',
			severity: 'high',
			occurrences: 23,
			firstSeen: new Date(Date.now() - 86400000 * 2), // 2 days ago
			lastSeen: new Date(Date.now() - 7200000), // 2 hours ago
			services: ['crawler', 'scheduler'],
			suggestedResolution: 'Increase crawl timeout values or optimize crawl performance',
			isResolved: false,
		},
		{
			id: 'pattern_4',
			pattern: 'Authentication.*failed.*user:\\s*(\\w+)',
			description: 'Authentication failures',
			severity: 'medium',
			occurrences: 67,
			firstSeen: new Date(Date.now() - 86400000 * 5), // 5 days ago
			lastSeen: new Date(Date.now() - 900000), // 15 minutes ago
			services: ['admin', 'web-app'],
			suggestedResolution: 'Monitor for brute force attacks and review user credentials',
			isResolved: false,
		},
		{
			id: 'pattern_5',
			pattern: 'Memory usage.*exceeded.*threshold',
			description: 'Memory usage alerts',
			severity: 'high',
			occurrences: 12,
			firstSeen: new Date(Date.now() - 86400000 * 1), // 1 day ago
			lastSeen: new Date(Date.now() - 10800000), // 3 hours ago
			services: ['ranking-engine', 'domain-seeder'],
			suggestedResolution: 'Optimize memory usage or increase available memory',
			isResolved: true,
		},
		{
			id: 'pattern_6',
			pattern: 'SSL certificate.*expired',
			description: 'SSL certificate expiration',
			severity: 'critical',
			occurrences: 3,
			firstSeen: new Date(Date.now() - 86400000 * 1), // 1 day ago
			lastSeen: new Date(Date.now() - 14400000), // 4 hours ago
			services: ['web-app'],
			suggestedResolution: 'Renew SSL certificates immediately',
			isResolved: true,
		},
		{
			id: 'pattern_7',
			pattern: 'Queue.*backup.*threshold',
			description: 'Queue backup warnings',
			severity: 'medium',
			occurrences: 89,
			firstSeen: new Date(Date.now() - 86400000 * 4), // 4 days ago
			lastSeen: new Date(Date.now() - 600000), // 10 minutes ago
			services: ['scheduler', 'domain-seeder'],
			suggestedResolution: 'Increase queue processing capacity or optimize job processing',
			isResolved: false,
		},
		{
			id: 'pattern_8',
			pattern: 'External API.*unavailable',
			description: 'External API failures',
			severity: 'low',
			occurrences: 156,
			firstSeen: new Date(Date.now() - 86400000 * 6), // 6 days ago
			lastSeen: new Date(Date.now() - 1200000), // 20 minutes ago
			services: ['domain-seeder', 'crawler'],
			suggestedResolution: 'Implement retry logic and fallback mechanisms',
			isResolved: false,
		},
	];

	// Filter patterns based on time range
	const getTimeRangeMs = (range: string): number =>
	{
		switch (range)
		{
			case '1h': return 3600000;
			case '6h': return 21600000;
			case '24h': return 86400000;
			case '7d': return 604800000;
			case '30d': return 2592000000;
			default: return 86400000;
		}
	};

	const timeRangeMs = getTimeRangeMs(timeRange);
	const cutoffTime = new Date(Date.now() - timeRangeMs);

	return patterns.filter(pattern =>
		pattern.lastSeen >= cutoffTime).sort((a, b) =>
	{
		// Sort by severity (critical > high > medium > low) then by occurrences
		const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
		const severityDiff = severityOrder[b.severity] - severityOrder[a.severity];
		if (severityDiff !== 0) return severityDiff;
		return b.occurrences - a.occurrences;
	});
};

export async function GET(request: NextRequest)
{
	try
	{
		const session = await validateSession(request);
		if (!session)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		const { searchParams } = new URL(request.url);
		const timeRange = searchParams.get('timeRange') || '24h';

		// Validate time range
		const validRanges = ['1h', '6h', '24h', '7d', '30d'];
		if (!validRanges.includes(timeRange))
		{
			return NextResponse.json(
				{ error: 'Invalid time range' },
				{ status: 400 }
			);
		}

		const patterns = generateMockErrorPatterns(timeRange);

		return NextResponse.json(patterns);
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error fetching error patterns:');
		return NextResponse.json(
			{ error: 'Failed to fetch error patterns' },
			{ status: 500 }
		);
	}
}
