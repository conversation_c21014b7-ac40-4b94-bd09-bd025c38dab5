import { NextRequest, NextResponse } from 'next/server';
import type { LogLevelConfigType, ServiceNameType, LogLevelType } from '../../../../types/logs';
import { apiLogger } from '@/lib/logger';
import { validateSession } from '@/lib/auth/session';

// Mock log level configurations
let mockLogLevels: LogLevelConfigType[] = [
	{
		service: 'web-app',
		currentLevel: 'info',
		lastModified: new Date(Date.now() - 86400000),
		modifiedBy: 'admin',
	},
	{
		service: 'worker',
		currentLevel: 'debug',
		temporaryLevel: 'info',
		temporaryUntil: new Date(Date.now() + 3600000), // 1 hour from now
		lastModified: new Date(Date.now() - 3600000),
		modifiedBy: 'admin',
	},
	{
		service: 'domain-seeder',
		currentLevel: 'warn',
		lastModified: new Date(Date.now() - 172800000),
		modifiedBy: 'system',
	},
	{
		service: 'admin',
		currentLevel: 'info',
		lastModified: new Date(Date.now() - 259200000),
		modifiedBy: 'admin',
	},
	{
		service: 'domain-seeder',
		currentLevel: 'error',
		lastModified: new Date(Date.now() - 432000000),
		modifiedBy: 'admin',
	},
	{
		service: 'admin',
		currentLevel: 'info',
		lastModified: new Date(Date.now() - 86400000),
		modifiedBy: 'system',
	},
];

export async function GET(request: NextRequest)
{
	try
	{
		const session = await validateSession(request);
		if (!session)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		return NextResponse.json(mockLogLevels);
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error fetching log levels:');
		return NextResponse.json(
			{ error: 'Failed to fetch log levels' },
			{ status: 500 }
		);
	}
}

export async function PUT(request: NextRequest)
{
	try
	{
		const session = await validateSession(request);
		if (!session)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		const body = await request.json();
		const { service, level, temporary, temporaryUntil } = body;

		// Validate input
		if (!service || !level)
		{
			apiLogger.error({ error: 'Service and level are required' }, 'Invalid request body:');
			return NextResponse.json(
				{ error: 'Service and level are required' },
				{ status: 400 }
			);
		}

		const validServices: readonly ServiceNameType[] = ['web-app', 'worker', 'domain-seeder', 'admin'] as const;
    const validLevels: readonly LogLevelType[] = ['debug', 'info', 'warn', 'error', 'fatal'] as const;

		if (!validServices.includes(service))
		{
			apiLogger.error({ error: 'Invalid service' }, 'Invalid service:');
			return NextResponse.json(
				{ error: 'Invalid service' },
				{ status: 400 }
			);
		}

		if (!validLevels.includes(level))
		{
			apiLogger.error({ error: 'Invalid log level' }, 'Invalid log level:');
			return NextResponse.json(
				{ error: 'Invalid log level' },
				{ status: 400 }
			);
		}

		if (temporary && !temporaryUntil)
		{
			apiLogger.error({ error: 'temporaryUntil is required for temporary changes' }, 'Invalid request body:');
			return NextResponse.json(
				{ error: 'temporaryUntil is required for temporary changes' },
				{ status: 400 }
			);
		}

		// Find and update the configuration
		const configIndex = mockLogLevels.findIndex(config => config.service === service);

		if (configIndex === -1)
		{
			apiLogger.error({ error: 'Service configuration not found' }, 'Service configuration not found:');
			return NextResponse.json(
				{ error: 'Service configuration not found' },
				{ status: 404 }
			);
		}

		const updatedConfig: LogLevelConfigType = {
			...mockLogLevels[configIndex],
			lastModified: new Date(),
			modifiedBy: session.username,
		};

		if (temporary)
		{
			updatedConfig.temporaryLevel = level;
			updatedConfig.temporaryUntil = new Date(temporaryUntil);
		}
		else
		{
			updatedConfig.currentLevel = level;
			updatedConfig.temporaryLevel = undefined;
			updatedConfig.temporaryUntil = undefined;
		}

		mockLogLevels[configIndex] = updatedConfig;

		// In production, this would update the actual service configuration
		// For now, just simulate the update
		apiLogger.info({
			service,
			level,
			temporary: temporary || false,
			msg: `Updated log level for ${service} to ${level}${temporary ? ' (temporary)' : ''}`
		});

		return NextResponse.json({ success: true, config: updatedConfig });
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error updating log level:');
		return NextResponse.json(
			{ error: 'Failed to update log level' },
			{ status: 500 }
		);
	}
}
