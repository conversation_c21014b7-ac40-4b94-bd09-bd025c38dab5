import { NextRequest, NextResponse } from 'next/server';
import { apiLogger } from '@/lib/logger';
import type { ServiceNameType } from '../../../../../../types/logs';

// This would normally import the same mock data as the main levels route
// For simplicity, we'll simulate the reset operation

export async function POST(
	request: NextRequest,
	{ params }: { params: { service: string } }
)
{
	try
	{
		// In production, verify authentication and permissions
		// const session = await getServerSession();
		// if (!session) {
		//   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		// }

		const { service } = params;

		// Validate service
		const validServices: ServiceNameType[] = ['web-app', 'crawler', 'ranking-engine', 'scheduler', 'domain-seeder', 'admin'];

		if (!validServices.includes(service as ServiceNameType))
		{
			return NextResponse.json(
				{ error: 'Invalid service' },
				{ status: 400 }
			);
		}

		// In production, this would:
		// 1. Find the service configuration
		// 2. Remove the temporary log level settings
		// 3. Update the configuration in the database/config system
		// 4. Notify the service to reload its log level configuration

		apiLogger.info({ service }, 'Reset temporary log level for service');

		return NextResponse.json({
			success: true,
			message: `Temporary log level reset for ${service}`
		});
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error), service: params.service }, 'Error resetting temporary log level');
		return NextResponse.json(
			{ error: 'Failed to reset temporary log level' },
			{ status: 500 }
		);
	}
}
