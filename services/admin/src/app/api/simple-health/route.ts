import { NextResponse } from 'next/server';
import { apiLogger } from '@/lib/logger';
import db from '@/lib/database/client';

async function GET()
{
	try
	{
		const services =
		[
			{ name: 'Web App', url: process.env.WEB_APP_URL || 'http://localhost:3000' },
			{ name: 'Worker', url: process.env.WORKER_URL || 'http://localhost:3001' },
			{ name: 'Domain Seeder', url: process.env.DOMAIN_SEEDER_URL || 'http://localhost:3005' },
		];

		// Use shared DatabaseManager health
		const base = await db.getHealthStatus();
		const mysqlHealth = Boolean(base.maria);
		const cassandraHealth = Boolean(base.scylla);

		// Service health via fetch
		const serviceHealths = await Promise.all(
			services.map(async (service) =>
			{
				try
				{
					const res = await fetch(`${service.url}/health`, { method: 'GET' });
					return res.ok;
				}
				catch
				{
					return false;
				}
			}),
		);

		const healthStatus =
		{
			databases: {
				mysql: mysqlHealth,
				cassandra: cassandraHealth,
			},
			services: services.map((service, index) => ({
				name: service.name,
				url: service.url,
				healthy: serviceHealths[index],
			})),
			overall: mysqlHealth && cassandraHealth && serviceHealths.every(Boolean),
			timestamp: new Date().toISOString(),
		};

		return NextResponse.json(healthStatus);
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Health check error:');
		return NextResponse.json(
			{ error: 'Health check failed', timestamp: new Date().toISOString() },
			{ status: 500 },
		);
	}
}

export { GET };
