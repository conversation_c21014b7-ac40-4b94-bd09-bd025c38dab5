import { NextRequest, NextResponse } from 'next/server';

import { validateSession } from '@/lib/auth/session';
import { logger } from '@/utils/logger';
import type { SeederSettingsType } from '@/types/seeder';

/**
 * GET /api/seeder/settings
 * Get current seeder configuration settings
 */
export async function GET(request: NextRequest)
{
	try
	{
		const session = await validateSession(request);
		if (!session)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		// Check permissions
		if (!session.permissions.includes('seeder:read'))
		{
			return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
		}

		const domainSeederUrl = process.env.DOMAIN_SEEDER_URL || 'http://localhost:3005';

		try
		{
			// Fetch settings from domain seeder service
			const response = await fetch(`${domainSeederUrl}/settings`, {
				method: 'GET',
				headers: { 'Content-Type': 'application/json' },
			});

			if (response.ok)
			{
				const settings = await response.json();
				return NextResponse.json(settings);
			}
		}
		catch (error)
		{
			logger.warn('Failed to fetch settings from seeder service, using defaults:', error);
		}

		// Return default settings if service is unavailable
		const defaultSettings: SeederSettingsType = {
			maxNewDomainsPerDay: 50000,
			contentGenerationEnabled: true,
			processingMode: 'hybrid',
			batchSizes: {
				discovery: 1000,
				validation: 500,
				contentGeneration: 100,
				enqueue: 200,
			},
			rateLimits: {
				globalRequestsPerMinute: 1000,
				perConnectorRequestsPerMinute: 200,
				databaseQueriesPerSecond: 100,
				contentGenerationPerHour: 5000,
			},
			bloomFilter: {
				expectedElements: 10000000,
				falsePositiveRate: 0.01,
				enabled: true,
				rebuildInterval: 24,
			},
			processingPriorities: {
				topTier: 10,
				midTier: 5,
				longTail: 1,
			},
			retrySettings: {
				maxRetries: 3,
				backoffMultiplier: 2,
				maxBackoffTime: 300,
			},
			monitoring: {
				metricsRetentionDays: 30,
				alertThresholds: {
					queueDepthWarning: 10000,
					queueDepthCritical: 25000,
					errorRateWarning: 0.05,
					errorRateCritical: 0.1,
					processingDelayWarning: 60,
					processingDelayCritical: 180,
				},
			},
		};

		return NextResponse.json(defaultSettings);
	}
	catch (error)
	{
		logger.error('Failed to get seeder settings:', error);
		return NextResponse.json(
			{ error: 'Failed to retrieve seeder settings' },
			{ status: 500 },
		);
	}
}

/**
 * PUT /api/seeder/settings
 * Update seeder configuration settings
 */
export async function PUT(request: NextRequest)
{
	try
	{
		const session = await validateSession(request);
		if (!session)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		// Check permissions
		if (!session.permissions.includes('seeder:write'))
		{
			return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
		}

		const body = await request.json();
		const settings: Partial<SeederSettingsType> = body;

		// Validate settings
		const validationErrors = validateSeederSettings(settings);
		if (validationErrors.length > 0)
		{
			return NextResponse.json(
				{ error: 'Invalid settings', details: validationErrors },
				{ status: 400 },
			);
		}

		const domainSeederUrl = process.env.DOMAIN_SEEDER_URL || 'http://localhost:3005';

		// Send settings update to domain seeder service
		try
		{
			const response = await fetch(`${domainSeederUrl}/settings`, {
				method: 'PUT',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					...settings,
					updatedBy: session.username,
					updatedAt: new Date().toISOString(),
				}),
			});

			if (!response.ok)
			{
				const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
				logger.error('Failed to update seeder settings:', {
					status: response.status,
					error: errorData,
					userId: session.username,
				});

				return NextResponse.json(
					{ error: errorData.error || 'Failed to update settings' },
					{ status: response.status },
				);
			}

			const updatedSettings = await response.json();

			logger.info('Seeder settings updated successfully', {
				userId: session.username,
				updatedFields: Object.keys(settings),
			});

			return NextResponse.json({
				success: true,
				settings: updatedSettings,
				updatedAt: new Date().toISOString(),
				updatedBy: session.username,
			});
		}
		catch (error)
		{
			logger.error('Failed to communicate with seeder service:', error);
			return NextResponse.json(
				{ error: 'Failed to communicate with seeder service' },
				{ status: 503 },
			);
		}
	}
	catch (error)
	{
		logger.error('Failed to update seeder settings:', error);
		return NextResponse.json(
			{ error: 'Failed to update seeder settings' },
			{ status: 500 },
		);
	}
}

/**
 * Validate seeder settings
 */
function validateSeederSettings(settings: Partial<SeederSettingsType>): string[]
{
	const errors: string[] = [];

	if (settings.maxNewDomainsPerDay !== undefined)
	{
		if (typeof settings.maxNewDomainsPerDay !== 'number' || settings.maxNewDomainsPerDay < 0)
		{
			errors.push('maxNewDomainsPerDay must be a non-negative number');
		}
		if (settings.maxNewDomainsPerDay > 1000000)
		{
			errors.push('maxNewDomainsPerDay cannot exceed 1,000,000');
		}
	}

	if (settings.processingMode !== undefined)
	{
		const validModes = ['preGenerated', 'live', 'hybrid'];
		if (!validModes.includes(settings.processingMode))
		{
			errors.push(`processingMode must be one of: ${validModes.join(', ')}`);
		}
	}

	if (settings.batchSizes !== undefined)
	{
		const { batchSizes } = settings;
		if (batchSizes.discovery !== undefined && (batchSizes.discovery < 1 || batchSizes.discovery > 10000))
		{
			errors.push('batchSizes.discovery must be between 1 and 10,000');
		}
		if (batchSizes.validation !== undefined && (batchSizes.validation < 1 || batchSizes.validation > 5000))
		{
			errors.push('batchSizes.validation must be between 1 and 5,000');
		}
		if (batchSizes.contentGeneration !== undefined && (batchSizes.contentGeneration < 1 || batchSizes.contentGeneration > 1000))
		{
			errors.push('batchSizes.contentGeneration must be between 1 and 1,000');
		}
		if (batchSizes.enqueue !== undefined && (batchSizes.enqueue < 1 || batchSizes.enqueue > 2000))
		{
			errors.push('batchSizes.enqueue must be between 1 and 2,000');
		}
	}

	if (settings.rateLimits !== undefined)
	{
		const { rateLimits } = settings;
		if (rateLimits.globalRequestsPerMinute !== undefined && (rateLimits.globalRequestsPerMinute < 1 || rateLimits.globalRequestsPerMinute > 10000))
		{
			errors.push('rateLimits.globalRequestsPerMinute must be between 1 and 10,000');
		}
		if (rateLimits.perConnectorRequestsPerMinute !== undefined && (rateLimits.perConnectorRequestsPerMinute < 1 || rateLimits.perConnectorRequestsPerMinute > 1000))
		{
			errors.push('rateLimits.perConnectorRequestsPerMinute must be between 1 and 1,000');
		}
		if (rateLimits.databaseQueriesPerSecond !== undefined && (rateLimits.databaseQueriesPerSecond < 1 || rateLimits.databaseQueriesPerSecond > 1000))
		{
			errors.push('rateLimits.databaseQueriesPerSecond must be between 1 and 1,000');
		}
		if (rateLimits.contentGenerationPerHour !== undefined && (rateLimits.contentGenerationPerHour < 1 || rateLimits.contentGenerationPerHour > 50000))
		{
			errors.push('rateLimits.contentGenerationPerHour must be between 1 and 50,000');
		}
	}

	if (settings.bloomFilter !== undefined)
	{
		const { bloomFilter } = settings;
		if (bloomFilter.expectedElements !== undefined && (bloomFilter.expectedElements < 1000 || bloomFilter.expectedElements > 100000000))
		{
			errors.push('bloomFilter.expectedElements must be between 1,000 and 100,000,000');
		}
		if (bloomFilter.falsePositiveRate !== undefined && (bloomFilter.falsePositiveRate <= 0 || bloomFilter.falsePositiveRate >= 1))
		{
			errors.push('bloomFilter.falsePositiveRate must be between 0 and 1 (exclusive)');
		}
		if (bloomFilter.rebuildInterval !== undefined && (bloomFilter.rebuildInterval < 1 || bloomFilter.rebuildInterval > 168))
		{
			errors.push('bloomFilter.rebuildInterval must be between 1 and 168 hours');
		}
	}

	if (settings.processingPriorities !== undefined)
	{
		const { processingPriorities } = settings;
		if (processingPriorities.topTier !== undefined && (processingPriorities.topTier < 1 || processingPriorities.topTier > 100))
		{
			errors.push('processingPriorities.topTier must be between 1 and 100');
		}
		if (processingPriorities.midTier !== undefined && (processingPriorities.midTier < 1 || processingPriorities.midTier > 100))
		{
			errors.push('processingPriorities.midTier must be between 1 and 100');
		}
		if (processingPriorities.longTail !== undefined && (processingPriorities.longTail < 1 || processingPriorities.longTail > 100))
		{
			errors.push('processingPriorities.longTail must be between 1 and 100');
		}
	}

	if (settings.retrySettings !== undefined)
	{
		const { retrySettings } = settings;
		if (retrySettings.maxRetries !== undefined && (retrySettings.maxRetries < 0 || retrySettings.maxRetries > 10))
		{
			errors.push('retrySettings.maxRetries must be between 0 and 10');
		}
		if (retrySettings.backoffMultiplier !== undefined && (retrySettings.backoffMultiplier < 1 || retrySettings.backoffMultiplier > 10))
		{
			errors.push('retrySettings.backoffMultiplier must be between 1 and 10');
		}
		if (retrySettings.maxBackoffTime !== undefined && (retrySettings.maxBackoffTime < 1 || retrySettings.maxBackoffTime > 3600))
		{
			errors.push('retrySettings.maxBackoffTime must be between 1 and 3,600 seconds');
		}
	}

	return errors;
}
