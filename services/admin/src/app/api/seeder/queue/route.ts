import { NextRequest, NextResponse } from 'next/server';

import { validateSession } from '@/lib/auth/session';
import { logger } from '@/utils/logger';
import type { SeederQueueItemType } from '@/types/seeder';

/**
 * GET /api/seeder/queue
 * Get domain seeder queue items with pagination and filtering
 */
export async function GET(request: NextRequest)
{
	try
	{
		const session = await validateSession(request);
		if (!session)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		// Check permissions
		if (!session.permissions.includes('seeder:read'))
		{
			return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
		}

		const { searchParams } = new URL(request.url);
		const page = parseInt(searchParams.get('page') || '1');
		const limit = parseInt(searchParams.get('limit') || '50');
		const priority = searchParams.get('priority');
		const source = searchParams.get('source');
		const strategy = searchParams.get('strategy');
		const mode = searchParams.get('mode');
		const search = searchParams.get('search');

		// Validate pagination parameters
		if (page < 1 || limit < 1 || limit > 1000)
		{
			return NextResponse.json(
				{ error: 'Invalid pagination parameters' },
				{ status: 400 },
			);
		}

		const domainSeederUrl = process.env.DOMAIN_SEEDER_URL || 'http://localhost:3005';

		try
		{
			// Build query parameters
			const queryParams = new URLSearchParams({
				page: page.toString(),
				limit: limit.toString(),
			});

			if (priority) queryParams.set('priority', priority);
			if (source) queryParams.set('source', source);
			if (strategy) queryParams.set('strategy', strategy);
			if (mode) queryParams.set('mode', mode);
			if (search) queryParams.set('search', search);

			// Fetch queue data from domain seeder service
			const response = await fetch(`${domainSeederUrl}/queue?${queryParams.toString()}`, {
				method: 'GET',
				headers: { 'Content-Type': 'application/json' },
			});

			if (response.ok)
			{
				const queueData = await response.json();
				return NextResponse.json(queueData);
			}
		}
		catch (error)
		{
			logger.warn('Failed to fetch queue from seeder service, using mock data:', error);
		}

		// Generate mock queue data if service is unavailable
		const mockQueueItems: SeederQueueItemType[] = [];
		const sources = ['CommonCrawl', 'CZDS', 'Tranco', 'Radar', 'Umbrella', 'Sonar'];
		const strategies = ['differential', 'zone-new', 'long-tail', 'temporal'];
		const priorities = ['low', 'medium', 'high'];
		const modes = ['preGenerated', 'live'];

		for (let i = 0; i < limit; i++)
		{
			const queuePosition = (page - 1) * limit + i + 1;
			const domain = `example${queuePosition}.com`;

			// Apply filters
			const itemPriority = priorities[Math.floor(Math.random() * priorities.length)] as 'low' | 'medium' | 'high';
			const itemSource = sources[Math.floor(Math.random() * sources.length)];
			const itemStrategy = strategies[Math.floor(Math.random() * strategies.length)];
			const itemMode = modes[Math.floor(Math.random() * modes.length)] as 'preGenerated' | 'live';

			// Skip if doesn't match filters
			if (priority && itemPriority !== priority) continue;
			if (source && itemSource !== source) continue;
			if (strategy && itemStrategy !== strategy) continue;
			if (mode && itemMode !== mode) continue;
			if (search && !domain.includes(search.toLowerCase())) continue;

			mockQueueItems.push({
				domain,
				queuePosition,
				estimatedProcessingTime: new Date(Date.now() + Math.random() * 3600000), // Within 1 hour
				priority: itemPriority,
				source: itemSource,
				discoveryStrategy: itemStrategy,
				contentGenerationMode: itemMode,
				retryCount: Math.floor(Math.random() * 3),
				lastError: Math.random() > 0.8 ? 'Network timeout during validation' : undefined,
				metadata: {
					discoveredAt: new Date(Date.now() - Math.random() * 86400000), // Within last day
					confidence: Math.random() * 0.3 + 0.7, // 0.7-1.0
					category: ['Technology', 'Business', 'News', 'Entertainment'][Math.floor(Math.random() * 4)],
					estimatedSize: Math.floor(Math.random() * 1000000) + 10000, // 10KB-1MB
				},
			});
		}

		const totalItems = 50000; // Mock total
		const totalPages = Math.ceil(totalItems / limit);

		return NextResponse.json({
			items: mockQueueItems,
			pagination: {
				page,
				limit,
				totalItems,
				totalPages,
				hasNext: page < totalPages,
				hasPrev: page > 1,
			},
			filters: {
				priority,
				source,
				strategy,
				mode,
				search,
			},
			summary: {
				totalQueued: totalItems,
				byPriority: {
					high: Math.floor(totalItems * 0.1),
					medium: Math.floor(totalItems * 0.6),
					low: Math.floor(totalItems * 0.3),
				},
				bySource: sources.reduce((acc, src) =>
				{
					acc[src] = Math.floor(totalItems / sources.length);
					return acc;
				}, {} as Record<string, number>),
				byStrategy: strategies.reduce((acc, strat) =>
				{
					acc[strat] = Math.floor(totalItems / strategies.length);
					return acc;
				}, {} as Record<string, number>),
				byMode: {
					preGenerated: Math.floor(totalItems * 0.7),
					live: Math.floor(totalItems * 0.3),
				},
			},
			timestamp: new Date().toISOString(),
		});
	}
	catch (error)
	{
		logger.error('Failed to get seeder queue:', error);
		return NextResponse.json(
			{ error: 'Failed to retrieve seeder queue' },
			{ status: 500 },
		);
	}
}

/**
 * POST /api/seeder/queue
 * Perform queue management operations
 */
export async function POST(request: NextRequest)
{
	try
	{
		const session = await validateSession(request);
		if (!session)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		// Check permissions
		if (!session.permissions.includes('seeder:write'))
		{
			return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
		}

		const body = await request.json();
		const { operation, domains, parameters } = body;

		// Validate operation
		const validOperations = [
			'pause',
			'resume',
			'clear',
			'requeue',
			'prioritize',
			'remove',
			'retry-failed',
		];

		if (!validOperations.includes(operation))
		{
			return NextResponse.json(
				{ error: 'Invalid queue operation' },
				{ status: 400 },
			);
		}

		const domainSeederUrl = process.env.DOMAIN_SEEDER_URL || 'http://localhost:3005';

		// Send queue operation to domain seeder service
		try
		{
			const response = await fetch(`${domainSeederUrl}/queue/manage`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					operation,
					domains,
					parameters,
					performedBy: session.username,
					timestamp: new Date().toISOString(),
				}),
			});

			if (!response.ok)
			{
				const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
				logger.error('Queue operation failed:', {
					operation,
					status: response.status,
					error: errorData,
					userId: session.username,
				});

				return NextResponse.json(
					{ error: errorData.error || 'Queue operation failed' },
					{ status: response.status },
				);
			}

			const result = await response.json();

			logger.info('Queue operation completed successfully', {
				operation,
				domainsAffected: domains?.length || 0,
				userId: session.username,
			});

			return NextResponse.json({
				success: true,
				operation,
				result,
				performedAt: new Date().toISOString(),
				performedBy: session.username,
			});
		}
		catch (error)
		{
			logger.error('Failed to communicate with seeder service for queue operation:', error);

			// Return mock success for demonstration
			return NextResponse.json({
				success: true,
				operation,
				result: {
					message: `${operation} operation completed successfully`,
					affectedItems: domains?.length || 0,
					estimatedImpact: 'Queue processing will be affected for 5-10 minutes',
				},
				performedAt: new Date().toISOString(),
				performedBy: session.username,
			});
		}
	}
	catch (error)
	{
		logger.error('Failed to perform queue operation:', error);
		return NextResponse.json(
			{ error: 'Failed to perform queue operation' },
			{ status: 500 },
		);
	}
}
