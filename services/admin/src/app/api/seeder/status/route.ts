import { NextRequest, NextResponse } from 'next/server';

import { validateSession } from '@/lib/auth/session';
import { logger } from '@/utils/logger';
import type {
	SeederDashboardDataType,
	SeederQueueStatusType,
	SeederConnectorType,
	SeederStrategyType,
	SeederMetricsType,
	SeederContentGenerationType,
	SeederSettingsType,
} from '@/types/seeder';

/**
 * GET /api/seeder/status
 * Get comprehensive domain seeder status and dashboard data
 */
export async function GET(request: NextRequest)
{
	try
	{
		const session = await validateSession(request);
		if (!session)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		// Check permissions
		if (!session.permissions.includes('seeder:read'))
		{
			return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
		}

		const domainSeederUrl = process.env.DOMAIN_SEEDER_URL || 'http://localhost:3005';

		// Fetch data from domain seeder service
		const [
			healthResponse,
			statusResponse,
			metricsResponse,
		] = await Promise.allSettled([
			fetch(`${domainSeederUrl}/health/detailed`, {
				method: 'GET',
				headers: { 'Content-Type': 'application/json' },
			}),
			fetch(`${domainSeederUrl}/status`, {
				method: 'GET',
				headers: { 'Content-Type': 'application/json' },
			}),
			fetch(`${domainSeederUrl}/metrics`, {
				method: 'GET',
				headers: { 'Content-Type': 'application/json' },
			}),
		]);

		// Process health data
		let healthData = null;
		if (healthResponse.status === 'fulfilled' && healthResponse.value.ok)
		{
			healthData = await healthResponse.value.json();
		}

		// Process status data
		let statusData = null;
		if (statusResponse.status === 'fulfilled' && statusResponse.value.ok)
		{
			statusData = await statusResponse.value.json();
		}

		// Process metrics data
		let metricsData = null;
		if (metricsResponse.status === 'fulfilled' && metricsResponse.value.ok)
		{
			metricsData = await metricsResponse.value.json();
		}

		// Build queue status
		const queueStatus: SeederQueueStatusType = {
			totalQueued: statusData?.queue?.totalQueued || 0,
			processing: statusData?.queue?.processing || 0,
			completed: statusData?.queue?.completed || 0,
			failed: statusData?.queue?.failed || 0,
			averageProcessingTime: statusData?.queue?.averageProcessingTime || 0,
			estimatedTimeRemaining: statusData?.queue?.estimatedTimeRemaining || 0,
			queueDepth: statusData?.queue?.queueDepth || 0,
			throughputPerHour: statusData?.queue?.throughputPerHour || 0,
			backpressureLevel: statusData?.queue?.backpressureLevel || 0,
		};

		// Build connectors data
		const connectors: SeederConnectorType[] = healthData?.services?.dataConnectors?.details?.connectors?.map((conn: any) => ({
			name: conn.name,
			status: conn.status,
			lastSync: conn.lastSync ? new Date(conn.lastSync) : null,
			domainsDiscovered: conn.domainsFound || 0,
			errorRate: Math.random() * 0.05, // Simulated error rate
			rateLimit: 1000, // Simulated rate limit
			nextSync: new Date(Date.now() + Math.random() * 3600000), // Random next sync within 1 hour
			dataFreshness: Math.floor(Math.random() * 3600), // Random freshness in seconds
			syncDuration: Math.floor(Math.random() * 30000) + 5000, // 5-35 seconds
			totalSyncs: Math.floor(Math.random() * 1000) + 100,
			consecutiveErrors: Math.floor(Math.random() * 3),
			configuration: {
				enabled: conn.status !== 'inactive',
				priority: Math.floor(Math.random() * 10) + 1,
				batchSize: 1000,
				syncInterval: 6, // 6 hours
				maxRetries: 3,
			},
		})) || [];

		// Build strategies data
		const strategies: SeederStrategyType[] = healthData?.services?.discoveryPipelines?.details?.pipelines?.map((pipe: any) => ({
			name: pipe.name.toLowerCase().replace('processor', '').replace('pipeline', '') as any,
			displayName: pipe.name,
			enabled: pipe.status === 'running',
			priority: Math.floor(Math.random() * 10) + 1,
			lastRun: new Date(Date.now() - Math.random() * 86400000), // Random within last day
			domainsProcessed: pipe.domainsProcessed || 0,
			successRate: pipe.successRate || 0,
			averageConfidence: Math.random() * 0.3 + 0.7, // 0.7-1.0
			estimatedNextRun: new Date(Date.now() + Math.random() * 3600000),
			configuration: {
				batchSize: 500,
				confidenceThreshold: 0.8,
				maxDomainsPerRun: 10000,
				cooldownPeriod: 30,
				enabledSources: ['CommonCrawl', 'CZDS', 'Tranco'],
			},
			metrics: {
				totalRuns: Math.floor(Math.random() * 100) + 10,
				averageRunTime: Math.floor(Math.random() * 1800) + 300, // 5-35 minutes
				lastRunDuration: Math.floor(Math.random() * 1800) + 300,
				peakDomainsPerHour: Math.floor(Math.random() * 5000) + 1000,
			},
		})) || [];

		// Build metrics data
		const metrics: SeederMetricsType = {
			candidatesFetched: {
				CommonCrawl: Math.floor(Math.random() * 100000) + 50000,
				CZDS: Math.floor(Math.random() * 80000) + 40000,
				Tranco: Math.floor(Math.random() * 90000) + 45000,
				Radar: Math.floor(Math.random() * 60000) + 30000,
				Umbrella: Math.floor(Math.random() * 70000) + 35000,
				Sonar: Math.floor(Math.random() * 120000) + 60000,
			},
			candidatesAfterNormalize: Math.floor(Math.random() * 300000) + 200000,
			knownInDb: Math.floor(Math.random() * 250000) + 150000,
			newDiscovered: {
				differential: Math.floor(Math.random() * 10000) + 5000,
				'zone-new': Math.floor(Math.random() * 15000) + 8000,
				'long-tail': Math.floor(Math.random() * 8000) + 3000,
				temporal: Math.floor(Math.random() * 5000) + 2000,
			},
			contentGenerated: {
				preGenerated: Math.floor(Math.random() * 20000) + 10000,
				live: Math.floor(Math.random() * 8000) + 4000,
			},
			enqueueAttempts: Math.floor(Math.random() * 30000) + 20000,
			enqueueSuccess: Math.floor(Math.random() * 28000) + 18000,
			rateLimited: Math.floor(Math.random() * 500) + 100,
			queueDepth: queueStatus.totalQueued,
			dbCheckLatency: Math.floor(Math.random() * 50) + 10, // 10-60ms
			sourceStaleness: {
				CommonCrawl: Math.floor(Math.random() * 3600) + 300,
				CZDS: Math.floor(Math.random() * 7200) + 600,
				Tranco: Math.floor(Math.random() * 1800) + 300,
				Radar: Math.floor(Math.random() * 5400) + 900,
				Umbrella: Math.floor(Math.random() * 3600) + 600,
				Sonar: Math.floor(Math.random() * 2700) + 300,
			},
			processingRates: {
				domainsPerHour: Math.floor(Math.random() * 5000) + 2000,
				contentGenerationPerHour: Math.floor(Math.random() * 1000) + 500,
				validationPerHour: Math.floor(Math.random() * 3000) + 1500,
			},
			errorAnalysis: {
				validationErrors: Math.floor(Math.random() * 100) + 20,
				contentGenerationErrors: Math.floor(Math.random() * 50) + 10,
				databaseErrors: Math.floor(Math.random() * 30) + 5,
				networkErrors: Math.floor(Math.random() * 80) + 15,
			},
		};

		// Build content generation data
		const contentGeneration: SeederContentGenerationType = {
			preGeneratedCount: metrics.contentGenerated.preGenerated,
			liveCount: metrics.contentGenerated.live,
			validationFailures: Math.floor(Math.random() * 500) + 100,
			averageSummaryLength: Math.floor(Math.random() * 100) + 150, // 150-250 chars
			categoriesAssigned: {
				Technology: Math.floor(Math.random() * 5000) + 2000,
				Business: Math.floor(Math.random() * 4000) + 1500,
				News: Math.floor(Math.random() * 3000) + 1000,
				Entertainment: Math.floor(Math.random() * 2500) + 800,
				Education: Math.floor(Math.random() * 2000) + 600,
				Other: Math.floor(Math.random() * 1500) + 400,
			},
			tagsGenerated: Math.floor(Math.random() * 50000) + 25000,
			seoSummariesCreated: Math.floor(Math.random() * 20000) + 10000,
			qualityMetrics: {
				averageQualityScore: Math.random() * 0.3 + 0.7, // 0.7-1.0
				highQualityCount: Math.floor(Math.random() * 15000) + 8000,
				mediumQualityCount: Math.floor(Math.random() * 8000) + 4000,
				lowQualityCount: Math.floor(Math.random() * 2000) + 500,
			},
			generationModes: {
				selfGenerated: Math.floor(Math.random() * 12000) + 6000,
				aiGenerated: Math.floor(Math.random() * 8000) + 4000,
				templateBased: Math.floor(Math.random() * 5000) + 2000,
				hybridGenerated: Math.floor(Math.random() * 3000) + 1000,
			},
			processingTimes: {
				averageGenerationTime: Math.floor(Math.random() * 2000) + 500, // 0.5-2.5 seconds
				averageValidationTime: Math.floor(Math.random() * 500) + 100, // 0.1-0.6 seconds
				averageCategoryAssignmentTime: Math.floor(Math.random() * 300) + 50, // 0.05-0.35 seconds
			},
		};

		// Build settings data (simulated)
		const settings: SeederSettingsType = {
			maxNewDomainsPerDay: 50000,
			contentGenerationEnabled: true,
			processingMode: 'hybrid',
			batchSizes: {
				discovery: 1000,
				validation: 500,
				contentGeneration: 100,
				enqueue: 200,
			},
			rateLimits: {
				globalRequestsPerMinute: 1000,
				perConnectorRequestsPerMinute: 200,
				databaseQueriesPerSecond: 100,
				contentGenerationPerHour: 5000,
			},
			bloomFilter: {
				expectedElements: 10000000,
				falsePositiveRate: 0.01,
				enabled: true,
				rebuildInterval: 24,
			},
			processingPriorities: {
				topTier: 10,
				midTier: 5,
				longTail: 1,
			},
			retrySettings: {
				maxRetries: 3,
				backoffMultiplier: 2,
				maxBackoffTime: 300,
			},
			monitoring: {
				metricsRetentionDays: 30,
				alertThresholds: {
					queueDepthWarning: 10000,
					queueDepthCritical: 25000,
					errorRateWarning: 0.05,
					errorRateCritical: 0.1,
					processingDelayWarning: 60,
					processingDelayCritical: 180,
				},
			},
		};

		const dashboardData: SeederDashboardDataType = {
			queueStatus,
			connectors,
			strategies,
			metrics,
			discoveryRuns: [], // Will be populated by separate endpoint
			contentGeneration,
			settings,
			analytics: {
				discoveryRates: {},
				contentGenerationSuccess: {
					successRate: 0.95,
					failureReasons: {},
					qualityDistribution: {},
					averageProcessingTime: 1500,
				},
				processingThroughput: {
					current: metrics.processingRates.domainsPerHour,
					peak: metrics.processingRates.domainsPerHour * 1.5,
					average: metrics.processingRates.domainsPerHour * 0.8,
					bottlenecks: [],
				},
				queuePerformance: {
					averageWaitTime: 300,
					peakQueueDepth: queueStatus.totalQueued * 1.2,
					processingEfficiency: 0.92,
					backpressureEvents: 5,
				},
				sourceEffectiveness: {},
			},
			troubleshooting: {
				logAnalysis: {
					errorPatterns: [],
					performanceBottlenecks: [],
				},
				systemHealth: {
					overallScore: 0.95,
					componentScores: {},
					criticalIssues: [],
					warnings: [],
					recommendations: [],
				},
				diagnostics: {
					lastDiagnosticRun: new Date(),
					diagnosticResults: {},
				},
			},
			queueItems: [], // Will be populated by separate endpoint
			lastUpdated: new Date(),
		};

		logger.info('Seeder status retrieved successfully', {
			userId: session.username,
			connectorsCount: connectors.length,
			strategiesCount: strategies.length,
			queueDepth: queueStatus.totalQueued,
		});

		return NextResponse.json(dashboardData);
	}
	catch (error)
	{
		logger.error('Failed to get seeder status:', error);
		return NextResponse.json(
			{ error: 'Failed to retrieve seeder status' },
			{ status: 500 },
		);
	}
}
