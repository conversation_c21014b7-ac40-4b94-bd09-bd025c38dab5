import { NextRequest, NextResponse } from 'next/server';

import { validateSession } from '@/lib/auth/session';
import { logger } from '@/utils/logger';
import type { SeederManualTriggerType, SeederOperationType } from '@/types/seeder';

/**
 * POST /api/seeder/trigger
 * Trigger manual seeder operations
 */
export async function POST(request: NextRequest)
{
	try
	{
		const session = await validateSession(request);
		if (!session)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		// Check permissions
		if (!session.permissions.includes('seeder:write'))
		{
			return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
		}

		const body = await request.json();
		const {
			operation, parameters, priority, description,
		}: SeederManualTriggerType = body;

		// Validate operation type
		const validOperations: SeederOperationType[] = [
			'full-discovery',
			'top-sources-only',
			'backfill',
			'strategy-specific',
			'queue-cleanup',
			'stuck-job-recovery',
			'bloom-filter-rebuild',
			'metrics-reset',
		];

		if (!validOperations.includes(operation))
		{
			return NextResponse.json(
				{ error: 'Invalid operation type' },
				{ status: 400 },
			);
		}

		const domainSeederUrl = process.env.DOMAIN_SEEDER_URL || 'http://localhost:3005';

		// Build request payload based on operation type
		const requestPayload: any = {
			operation,
			priority: priority || 'medium',
			triggeredBy: session.username,
			timestamp: new Date().toISOString(),
			parameters: parameters || {},
		};

		// Add operation-specific parameters
		switch (operation)
		{
			case 'full-discovery':
				requestPayload.parameters = {
					includeAllSources: true,
					enableAllStrategies: true,
					maxDomains: parameters?.maxDomains || 100000,
					...parameters,
				};
				break;

			case 'top-sources-only':
				requestPayload.parameters = {
					sources: parameters?.sources || ['CommonCrawl', 'CZDS', 'Tranco'],
					maxDomainsPerSource: parameters?.maxDomainsPerSource || 10000,
					...parameters,
				};
				break;

			case 'backfill':
				requestPayload.parameters = {
					startDate: parameters?.startDate,
					endDate: parameters?.endDate,
					sources: parameters?.sources || [],
					batchSize: parameters?.batchSize || 1000,
					...parameters,
				};
				break;

			case 'strategy-specific':
				if (!parameters?.strategy)
				{
					return NextResponse.json(
						{ error: 'Strategy parameter is required for strategy-specific operation' },
						{ status: 400 },
					);
				}
				requestPayload.parameters = {
					strategy: parameters.strategy,
					maxDomains: parameters?.maxDomains || 50000,
					...parameters,
				};
				break;

			case 'queue-cleanup':
				requestPayload.parameters = {
					removeStuckJobs: parameters?.removeStuckJobs !== false,
					removeFailedJobs: parameters?.removeFailedJobs !== false,
					maxAge: parameters?.maxAge || 86400, // 24 hours
					...parameters,
				};
				break;

			case 'stuck-job-recovery':
				requestPayload.parameters = {
					maxStuckTime: parameters?.maxStuckTime || 3600, // 1 hour
					retryFailedJobs: parameters?.retryFailedJobs !== false,
					...parameters,
				};
				break;

			case 'bloom-filter-rebuild':
				requestPayload.parameters = {
					preserveExisting: parameters?.preserveExisting !== false,
					expectedElements: parameters?.expectedElements || 10000000,
					falsePositiveRate: parameters?.falsePositiveRate || 0.01,
					...parameters,
				};
				break;

			case 'metrics-reset':
				requestPayload.parameters = {
					resetCounters: parameters?.resetCounters !== false,
					resetHistograms: parameters?.resetHistograms !== false,
					preserveHistory: parameters?.preserveHistory !== false,
					...parameters,
				};
				break;
		}

		// Send trigger request to domain seeder service
		const response = await fetch(`${domainSeederUrl}/trigger`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify(requestPayload),
		});

		if (!response.ok)
		{
			const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
			logger.error('Domain seeder trigger failed:', {
				operation,
				status: response.status,
				error: errorData,
				userId: session.username,
			});

			return NextResponse.json(
				{ error: errorData.error || 'Failed to trigger operation' },
				{ status: response.status },
			);
		}

		const result = await response.json();

		logger.info('Seeder operation triggered successfully', {
			operation,
			priority,
			userId: session.username,
			jobId: result.jobId,
			estimatedDuration: result.estimatedDuration,
		});

		return NextResponse.json({
			success: true,
			operation,
			jobId: result.jobId,
			status: result.status,
			estimatedDuration: result.estimatedDuration,
			message: result.message || `${operation} operation triggered successfully`,
			triggeredAt: new Date().toISOString(),
			triggeredBy: session.username,
		});
	}
	catch (error)
	{
		logger.error('Failed to trigger seeder operation:', error);
		return NextResponse.json(
			{ error: 'Failed to trigger seeder operation' },
			{ status: 500 },
		);
	}
}

/**
 * GET /api/seeder/trigger
 * Get available trigger operations and their descriptions
 */
export async function GET(request: NextRequest)
{
	try
	{
		const session = await validateSession(request);
		if (!session)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		// Check permissions
		if (!session.permissions.includes('seeder:read'))
		{
			return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
		}

		const operations = [
			{
				operation: 'full-discovery',
				name: 'Full Discovery Run',
				description: 'Trigger a complete discovery run using all available sources and strategies',
				estimatedDuration: '2-4 hours',
				parameters: [
					{
						name: 'maxDomains', type: 'number', description: 'Maximum domains to discover', default: 100000,
					},
					{
						name: 'includeAllSources', type: 'boolean', description: 'Include all data sources', default: true,
					},
					{
						name: 'enableAllStrategies', type: 'boolean', description: 'Enable all discovery strategies', default: true,
					},
				],
			},
			{
				operation: 'top-sources-only',
				name: 'Top Sources Discovery',
				description: 'Run discovery using only the most reliable and high-quality sources',
				estimatedDuration: '30-60 minutes',
				parameters: [
					{
						name: 'sources', type: 'array', description: 'List of sources to use', default: ['CommonCrawl', 'CZDS', 'Tranco'],
					},
					{
						name: 'maxDomainsPerSource', type: 'number', description: 'Maximum domains per source', default: 10000,
					},
				],
			},
			{
				operation: 'backfill',
				name: 'Historical Backfill',
				description: 'Backfill historical data for a specific time period',
				estimatedDuration: '1-6 hours',
				parameters: [
					{
						name: 'startDate', type: 'string', description: 'Start date (ISO format)', required: true,
					},
					{
						name: 'endDate', type: 'string', description: 'End date (ISO format)', required: true,
					},
					{
						name: 'sources', type: 'array', description: 'Sources to backfill from', default: [],
					},
					{
						name: 'batchSize', type: 'number', description: 'Processing batch size', default: 1000,
					},
				],
			},
			{
				operation: 'strategy-specific',
				name: 'Strategy-Specific Run',
				description: 'Run a specific discovery strategy in isolation',
				estimatedDuration: '15-90 minutes',
				parameters: [
					{
						name: 'strategy', type: 'string', description: 'Strategy to run', required: true, options: ['differential', 'zone-new', 'long-tail', 'temporal'],
					},
					{
						name: 'maxDomains', type: 'number', description: 'Maximum domains to process', default: 50000,
					},
				],
			},
			{
				operation: 'queue-cleanup',
				name: 'Queue Cleanup',
				description: 'Clean up stuck, failed, or old jobs from processing queues',
				estimatedDuration: '5-15 minutes',
				parameters: [
					{
						name: 'removeStuckJobs', type: 'boolean', description: 'Remove stuck jobs', default: true,
					},
					{
						name: 'removeFailedJobs', type: 'boolean', description: 'Remove failed jobs', default: true,
					},
					{
						name: 'maxAge', type: 'number', description: 'Maximum age in seconds', default: 86400,
					},
				],
			},
			{
				operation: 'stuck-job-recovery',
				name: 'Stuck Job Recovery',
				description: 'Attempt to recover and retry stuck processing jobs',
				estimatedDuration: '10-30 minutes',
				parameters: [
					{
						name: 'maxStuckTime', type: 'number', description: 'Maximum stuck time in seconds', default: 3600,
					},
					{
						name: 'retryFailedJobs', type: 'boolean', description: 'Retry previously failed jobs', default: true,
					},
				],
			},
			{
				operation: 'bloom-filter-rebuild',
				name: 'Bloom Filter Rebuild',
				description: 'Rebuild the bloom filter for duplicate detection',
				estimatedDuration: '20-45 minutes',
				parameters: [
					{
						name: 'preserveExisting', type: 'boolean', description: 'Preserve existing filter during rebuild', default: true,
					},
					{
						name: 'expectedElements', type: 'number', description: 'Expected number of elements', default: 10000000,
					},
					{
						name: 'falsePositiveRate', type: 'number', description: 'Target false positive rate', default: 0.01,
					},
				],
			},
			{
				operation: 'metrics-reset',
				name: 'Metrics Reset',
				description: 'Reset accumulated metrics and counters',
				estimatedDuration: '1-2 minutes',
				parameters: [
					{
						name: 'resetCounters', type: 'boolean', description: 'Reset counter metrics', default: true,
					},
					{
						name: 'resetHistograms', type: 'boolean', description: 'Reset histogram metrics', default: true,
					},
					{
						name: 'preserveHistory', type: 'boolean', description: 'Preserve historical data', default: true,
					},
				],
			},
		];

		return NextResponse.json({
			operations,
			timestamp: new Date().toISOString(),
		});
	}
	catch (error)
	{
		logger.error('Failed to get trigger operations:', error);
		return NextResponse.json(
			{ error: 'Failed to retrieve trigger operations' },
			{ status: 500 },
		);
	}
}
