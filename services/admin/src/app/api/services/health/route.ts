import { NextResponse } from 'next/server';
import { createIsomorphicLogger } from '@shared/client';
import BrowserHttpClient from '@/lib/utils/BrowserHttpClient';

import type { NextRequest } from 'next/server';

const logger = createIsomorphicLogger('ServiceHealth');

interface ServiceEndpoint
{
	name: string;
	url: string;
	port: number;
}

interface ServiceHealthResponse
{
	name: string;
	status: 'healthy' | 'unhealthy' | 'degraded';
	uptime: number;
	responseTime: number;
	lastCheck: Date;
	version: string;
	metrics: {
		cpu: number;
		memory: number;
		requests: number;
		errors: number;
	};
	dependencies?: Array<{
		name: string;
		status: 'connected' | 'disconnected' | 'error';
		responseTime: number;
	}>;
}

const SERVICES: ServiceEndpoint[] = [
	{ name: 'web-app', url: 'http://localhost:3000', port: 3000 },
	{ name: 'worker', url: 'http://localhost:3001', port: 3001 },
	{ name: 'domain-seeder', url: 'http://localhost:3005', port: 3005 },
];

async function checkServiceHealth(service: ServiceEndpoint): Promise<ServiceHealthResponse>
{
	const startTime = Date.now();
	const httpClient = new BrowserHttpClient({ timeout: 5000 });

	try
	{
		const response = await httpClient.get(`${service.url}/health`);
		const responseTime = Date.now() - startTime;

		if (response.status >= 200 && response.status < 300)
		{
			const healthData = response.data;

			return {
				name: service.name,
				status: 'healthy',
				uptime: healthData.uptime || 0,
				responseTime,
				lastCheck: new Date(),
				version: healthData.version || '1.0.0',
				metrics: {
					cpu: healthData.metrics?.cpu || 0,
					memory: healthData.metrics?.memory || 0,
					requests: healthData.metrics?.requests || 0,
					errors: healthData.metrics?.errors || 0,
				},
				dependencies: healthData.dependencies || [],
			};
		}

		return {
			name: service.name,
			status: 'degraded',
			uptime: 0,
			responseTime,
			lastCheck: new Date(),
			version: 'unknown',
			metrics: {
				cpu: 0,
				memory: 0,
				requests: 0,
				errors: 1,
			},
		};
	}
	catch (error)
	{
		const responseTime = Date.now() - startTime;

		logger.error(`Service health check failed for ${service.name}:`, error as Error);

		return {
			name: service.name,
			status: 'unhealthy',
			uptime: 0,
			responseTime,
			lastCheck: new Date(),
			version: 'unknown',
			metrics: {
				cpu: 0,
				memory: 0,
				requests: 0,
				errors: 1,
			},
		};
	}
}

async function GET(request: NextRequest)
{
	try
	{
		const { searchParams } = new URL(request.url);
		const serviceName = searchParams.get('service');

		if (serviceName)
		{
			const service = SERVICES.find(s => s.name === serviceName);
			if (!service)
			{
				return NextResponse.json(
					{ error: 'Service not found' },
					{ status: 404 },
				);
			}

			const healthStatus = await checkServiceHealth(service);
			return NextResponse.json(healthStatus);
		}

		// Check all services
		const healthPromises = SERVICES.map(service => checkServiceHealth(service));
		const healthStatuses = await Promise.all(healthPromises);

		return NextResponse.json({
			services: healthStatuses,
			timestamp: new Date(),
			summary: {
				total: healthStatuses.length,
				healthy: healthStatuses.filter(s => s.status === 'healthy').length,
				degraded: healthStatuses.filter(s => s.status === 'degraded').length,
				unhealthy: healthStatuses.filter(s => s.status === 'unhealthy').length,
			},
		});
	}
	catch (error)
	{
		logger.error('Failed to check service health:', error as Error);
		return NextResponse.json(
			{ error: 'Internal server error' },
			{ status: 500 },
		);
	}
}

export { GET };
