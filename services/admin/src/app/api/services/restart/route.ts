import { NextResponse } from 'next/server';
import { createIsomorphicLogger } from '@shared/client';
import BrowserHttpClient from '@/lib/utils/BrowserHttpClient';

import { logger } from '@/utils/logger';

import type { NextRequest } from 'next/server';

interface RestartRequest
{
	serviceName: string;
	force?: boolean;
	reason?: string;
}

interface ServiceConfig
{
	name: string;
	port: number;
	healthEndpoint: string;
	dependencies: string[];
	criticalService: boolean;
}

const SERVICE_CONFIGS: Record<string, ServiceConfig> =
{
	'web-app':
	{
		name: 'web-app',
		port: 3000,
		healthEndpoint: 'http://localhost:3000/health',
		dependencies: ['redis', 'manticore'],
		criticalService: true,
	},
	worker:
	{
		name: 'worker',
		port: 3001,
		healthEndpoint: 'http://localhost:3001/health',
		dependencies: ['scylla', 'mariadb', 'redis', 'manticore'],
		criticalService: true,
	},
	'domain-seeder':
	{
		name: 'domain-seeder',
		port: 3005,
		healthEndpoint: 'http://localhost:3005/health',
		dependencies: ['scylla', 'mariadb', 'redis'],
		criticalService: false,
	},
};

async function checkServiceHealth(serviceConfig: ServiceConfig): Promise<boolean>
{
	try
	{
		const httpClient = new BrowserHttpClient({ timeout: 3000 });
		const response = await httpClient.get(serviceConfig.healthEndpoint);
		return response.status >= 200 && response.status < 300;
	}
	catch (error)
	{
		return false;
	}
}

async function performServiceRestart(serviceName: string, force: boolean): Promise<{ success: boolean; message: string }>
{
	const serviceConfig = SERVICE_CONFIGS[serviceName];

	if (!force && serviceConfig.criticalService)
	{
		// Check if service is currently healthy before restarting
		const isHealthy = await checkServiceHealth(serviceConfig);
		if (isHealthy)
		{
			logger.warn(`Attempting to restart healthy critical service: ${serviceName}`);
		}
	}

	// In a real implementation, this would:
	// 1. Send graceful shutdown signal to the service
	// 2. Wait for connections to drain (if not force)
	// 3. Kill the process if force or timeout
	// 4. Start the service again
	// 5. Wait for health check to pass

	logger.info(`${force ? 'Force' : 'Graceful'} restart initiated for service: ${serviceName}`);

	// Simulate restart process
	const restartDelay = force ? 2000 : 5000; // Force restart is faster
	await new Promise(resolve => setTimeout(resolve, restartDelay));

	// Simulate potential restart failure (5% chance)
	if (Math.random() < 0.05)
	{
		throw new Error(`Service ${serviceName} failed to restart - process did not respond`);
	}

	return {
		success: true,
		message: `Service ${serviceName} ${force ? 'force' : 'gracefully'} restarted successfully`,
	};
}

async function POST(request: NextRequest)
{
	try
	{
		const body: RestartRequest = await request.json();
		const { serviceName, force = false, reason } = body;

		if (!serviceName)
		{
			return NextResponse.json(
				{ error: 'Service name is required' },
				{ status: 400 },
			);
		}

		if (!SERVICE_CONFIGS[serviceName])
		{
			return NextResponse.json(
				{ error: `Invalid service name. Valid services: ${Object.keys(SERVICE_CONFIGS).join(', ')}` },
				{ status: 400 },
			);
		}

		const serviceConfig = SERVICE_CONFIGS[serviceName];

		// Safety checks for critical services
		if (serviceConfig.criticalService && !force)
		{
			// Check if there are dependent services that might be affected
			const dependentServices = Object.values(SERVICE_CONFIGS).filter(config => config.dependencies.includes(serviceName));

			if (dependentServices.length > 0)
			{
				logger.warn(`Restarting critical service ${serviceName} may affect: ${dependentServices.map(s => s.name).join(', ')}`);
			}
		}

		// Log restart request with reason
		logger.info('Service restart requested', {
			serviceName,
			force,
			reason: reason || 'No reason provided',
			timestamp: new Date().toISOString(),
			criticalService: serviceConfig.criticalService,
		});

		// Perform the restart
		const result = await performServiceRestart(serviceName, force);

		// Wait a moment then verify service is back up
		await new Promise(resolve => setTimeout(resolve, 2000));
		const isHealthyAfterRestart = await checkServiceHealth(serviceConfig);

		if (!isHealthyAfterRestart)
		{
			logger.error(`Service ${serviceName} is not healthy after restart`);
			return NextResponse.json({
				success: false,
				error: `Service ${serviceName} restarted but failed health check`,
				serviceName,
				timestamp: new Date(),
				force,
				healthCheck: false,
			}, { status: 500 });
		}

		return NextResponse.json({
			success: true,
			message: result.message,
			serviceName,
			timestamp: new Date(),
			force,
			reason,
			healthCheck: true,
			restartDuration: force ? '2s' : '5s',
		});
	}
	catch (error)
	{
		const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
		logger.error('Failed to restart service:', error as Error);

		return NextResponse.json(
			{
				success: false,
				error: `Service restart failed: ${errorMessage}`,
				timestamp: new Date(),
			},
			{ status: 500 },
		);
	}
}

export { POST };
