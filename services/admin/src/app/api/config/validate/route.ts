import { NextRequest, NextResponse } from 'next/server';
import { validateSession } from '@/lib/auth/session';
import { apiLogger } from '@/lib/logger';
import type {
	ConfigCategoryType,
	ConfigValidationResultType,
	ConfigImpactAnalysisType,
} from '@/types/config';


export async function POST(request: NextRequest)
{
	try
	{
		const session = await validateSession(request);
		if (!session)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		const body = await request.json();
		const { category, config, currentConfig } = body;

		// Validate the configuration
		const validation = await validateConfiguration(category, config);

		// Perform impact analysis
		const impactAnalysis = await analyzeConfigImpact(category, config, currentConfig);

		return NextResponse.json({
			success: true,
			data: {
				validation,
				impactAnalysis,
			},
		});
	}
	catch (error)
	{
		apiLogger.error({ error }, 'Failed to validate configuration');
		return NextResponse.json(
			{ error: 'Failed to validate configuration' },
			{ status: 500 },
		);
	}
}

async function validateConfiguration(
	category: ConfigCategoryType,
	config: unknown,
): Promise<ConfigValidationResultType>
{
	const errors: { field: string; message: string; severity: 'error' | 'warning' }[] = [];
	const warnings: { field: string; message: string }[] = [];
	const dependencies: { field: string; dependsOn: string[]; satisfied: boolean }[] = [];

	// Perform comprehensive validation based on category
	switch (category)
	{
		case 'database':
			await validateDatabaseConfiguration(config, errors, warnings, dependencies);
			break;
		case 'crawling':
			await validateCrawlingConfiguration(config, errors, warnings, dependencies);
			break;
		case 'ranking':
			await validateRankingConfiguration(config, errors, warnings, dependencies);
			break;
		case 'alerts':
			await validateAlertsConfiguration(config, errors, warnings, dependencies);
			break;
		case 'ai_services':
			await validateAIServicesConfiguration(config, errors, warnings, dependencies);
			break;
		case 'seeder':
			await validateSeederConfiguration(config, errors, warnings, dependencies);
			break;
	}

	return {
		valid: errors.filter(e => e.severity === 'error').length === 0,
		errors,
		warnings,
		dependencies,
	};
}

async function validateDatabaseConfiguration(
	config: any,
	errors: { field: string; message: string; severity: 'error' | 'warning' }[],
	warnings: { field: string; message: string }[],
	// dependencies: { field: string; dependsOn: string[]; satisfied: boolean }[],
)
{
	// ScyllaDB validation
	if (config.scylla)
	{
		if (!config.scylla.hosts || config.scylla.hosts.length === 0)
		{
			errors.push({
				field: 'scylla.hosts',
				message: 'At least one ScyllaDB host is required',
				severity: 'error',
			});
		}

		// Validate host format
		config.scylla.hosts?.forEach((host: string, index: number) =>
		{
			if (!/^[\w.-]+:\d+$/.test(host))
			{
				errors.push({
					field: `scylla.hosts[${index}]`,
					message: 'Host must be in format hostname:port',
					severity: 'error',
				});
			}
		});

		if (config.scylla.poolSize < 1 || config.scylla.poolSize > 100)
		{
			warnings.push({
				field: 'scylla.poolSize',
				message: 'Pool size should be between 1 and 100 for optimal performance',
			});
		}

		if (config.scylla.timeout < 1000)
		{
			warnings.push({
				field: 'scylla.timeout',
				message: 'Timeout should be at least 1000ms to avoid connection issues',
			});
		}

		// Check failover configuration
		if (config.scylla.failoverEnabled && (!config.scylla.failoverHosts || config.scylla.failoverHosts.length === 0))
		{
			errors.push({
				field: 'scylla.failoverHosts',
				message: 'Failover hosts are required when failover is enabled',
				severity: 'error',
			});
		}
	}

	// MariaDB validation
	if (config.mariadb)
	{
		if (!config.mariadb.host)
		{
			errors.push({
				field: 'mariadb.host',
				message: 'MariaDB host is required',
				severity: 'error',
			});
		}

		if (config.mariadb.port < 1 || config.mariadb.port > 65535)
		{
			errors.push({
				field: 'mariadb.port',
				message: 'Port must be between 1 and 65535',
				severity: 'error',
			});
		}

		if (!config.mariadb.database)
		{
			errors.push({
				field: 'mariadb.database',
				message: 'Database name is required',
				severity: 'error',
			});
		}

		if (config.mariadb.poolSize < 1 || config.mariadb.poolSize > 200)
		{
			warnings.push({
				field: 'mariadb.poolSize',
				message: 'Pool size should be between 1 and 200 for optimal performance',
			});
		}
	}

	// Redis validation
	if (config.redis)
	{
		if (!config.redis.host)
		{
			errors.push({
				field: 'redis.host',
				message: 'Redis host is required',
				severity: 'error',
			});
		}

		if (config.redis.port < 1 || config.redis.port > 65535)
		{
			errors.push({
				field: 'redis.port',
				message: 'Port must be between 1 and 65535',
				severity: 'error',
			});
		}

		if (config.redis.database < 0 || config.redis.database > 15)
		{
			warnings.push({
				field: 'redis.database',
				message: 'Database should be between 0 and 15',
			});
		}

		// Check cluster configuration
		if (config.redis.cluster && (!config.redis.clusterNodes || config.redis.clusterNodes.length === 0))
		{
			errors.push({
				field: 'redis.clusterNodes',
				message: 'Cluster nodes are required when cluster mode is enabled',
				severity: 'error',
			});
		}
	}

	// Manticore validation
	if (config.manticore)
	{
		if (!config.manticore.host)
		{
			errors.push({
				field: 'manticore.host',
				message: 'Manticore host is required',
				severity: 'error',
			});
		}

		if (config.manticore.port < 1 || config.manticore.port > 65535)
		{
			errors.push({
				field: 'manticore.port',
				message: 'Port must be between 1 and 65535',
				severity: 'error',
			});
		}

		if (config.manticore.maxQueryTime < 1000)
		{
			warnings.push({
				field: 'manticore.maxQueryTime',
				message: 'Max query time should be at least 1000ms',
			});
		}
	}
}

async function validateCrawlingConfiguration(
	config: any,
	errors: { field: string; message: string; severity: 'error' | 'warning' }[],
	warnings: { field: string; message: string }[],
	// dependencies: { field: string; dependsOn: string[]; satisfied: boolean }[],
)
{
	// Rate limit validation
	if (config.rateLimit)
	{
		if (config.rateLimit.global < 1)
		{
			errors.push({
				field: 'rateLimit.global',
				message: 'Global rate limit must be at least 1',
				severity: 'error',
			});
		}

		if (config.rateLimit.perDomain > config.rateLimit.global)
		{
			warnings.push({
				field: 'rateLimit.perDomain',
				message: 'Per-domain rate limit should not exceed global rate limit',
			});
		}

		if (config.rateLimit.burstLimit < config.rateLimit.global)
		{
			warnings.push({
				field: 'rateLimit.burstLimit',
				message: 'Burst limit should be at least equal to global rate limit',
			});
		}

		if (config.rateLimit.windowSize < 1)
		{
			errors.push({
				field: 'rateLimit.windowSize',
				message: 'Window size must be at least 1 second',
				severity: 'error',
			});
		}
	}

	// User agents validation
	if (config.userAgents)
	{
		if (config.userAgents.rotation && (!config.userAgents.agents || config.userAgents.agents.length === 0))
		{
			errors.push({
				field: 'userAgents.agents',
				message: 'At least one user agent is required when rotation is enabled',
				severity: 'error',
			});
		}

		config.userAgents.agents?.forEach((agent: string, index: number) =>
		{
			if (!agent || agent.trim().length === 0)
			{
				errors.push({
					field: `userAgents.agents[${index}]`,
					message: 'User agent cannot be empty',
					severity: 'error',
				});
			}
		});
	}

	// Intervals validation
	if (config.intervals)
	{
		Object.entries(config.intervals).forEach(([type, interval]) =>
		{
			if (typeof interval !== 'number' || interval < 0.1)
			{
				errors.push({
					field: `intervals.${type}`,
					message: 'Interval must be at least 0.1 hours',
					severity: 'error',
				});
			}
		});

		// Logical validation
		if (config.intervals.priority > config.intervals.standard)
		{
			warnings.push({
				field: 'intervals.priority',
				message: 'Priority interval should be shorter than standard interval',
			});
		}
	}

	// Concurrent requests validation
	if (config.concurrent)
	{
		if (config.concurrent.maxRequests < 1)
		{
			errors.push({
				field: 'concurrent.maxRequests',
				message: 'Maximum concurrent requests must be at least 1',
				severity: 'error',
			});
		}

		if (config.concurrent.maxPerDomain > config.concurrent.maxRequests)
		{
			warnings.push({
				field: 'concurrent.maxPerDomain',
				message: 'Max per domain should not exceed max requests',
			});
		}

		if (config.concurrent.queueSize < config.concurrent.maxRequests)
		{
			warnings.push({
				field: 'concurrent.queueSize',
				message: 'Queue size should be at least equal to max requests',
			});
		}
	}

	// Timeouts validation
	if (config.timeouts)
	{
		Object.entries(config.timeouts).forEach(([type, timeout]) =>
		{
			if (typeof timeout !== 'number' || timeout < 1000)
			{
				errors.push({
					field: `timeouts.${type}`,
					message: 'Timeout must be at least 1000ms',
					severity: 'error',
				});
			}
		});

		// Logical validation
		if (config.timeouts.dns > config.timeouts.connect)
		{
			warnings.push({
				field: 'timeouts.dns',
				message: 'DNS timeout should be less than connect timeout',
			});
		}

		if (config.timeouts.connect > config.timeouts.response)
		{
			warnings.push({
				field: 'timeouts.connect',
				message: 'Connect timeout should be less than response timeout',
			});
		}
	}

	// Resource limits validation
	if (config.resources)
	{
		if (config.resources.maxMemory < 512)
		{
			warnings.push({
				field: 'resources.maxMemory',
				message: 'Memory limit should be at least 512MB',
			});
		}

		if (config.resources.maxCpu < 10 || config.resources.maxCpu > 100)
		{
			warnings.push({
				field: 'resources.maxCpu',
				message: 'CPU limit should be between 10% and 100%',
			});
		}

		if (config.resources.diskSpace < 1024)
		{
			warnings.push({
				field: 'resources.diskSpace',
				message: 'Disk space should be at least 1GB',
			});
		}
	}
}

async function validateRankingConfiguration(
	config: any,
	errors: { field: string; message: string; severity: 'error' | 'warning' }[],
	warnings: { field: string; message: string }[],
	// dependencies: { field: string; dependsOn: string[]; satisfied: boolean }[],
)
{
	// Weights validation
	if (config.weights)
	{
		const weights = Object.values(config.weights) as number[];
		const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);

		if (Math.abs(totalWeight - 1.0) > 0.001)
		{
			errors.push({
				field: 'weights',
				message: `Ranking weights must sum to 1.0 (current sum: ${totalWeight.toFixed(3)})`,
				severity: 'error',
			});
		}

		Object.entries(config.weights).forEach(([key, weight]) =>
		{
			if (typeof weight !== 'number' || weight < 0 || weight > 1)
			{
				errors.push({
					field: `weights.${key}`,
					message: 'Weight must be a number between 0 and 1',
					severity: 'error',
				});
			}
		});

		// Check for zero weights
		const zeroWeights = Object.entries(config.weights).filter(([, weight]) => weight === 0);
		if (zeroWeights.length > 0)
		{
			warnings.push({
				field: 'weights',
				message: `Some weights are set to 0: ${zeroWeights.map(([key]) => key).join(', ')}`,
			});
		}
	}

	// Calculation settings validation
	if (config.calculation)
	{
		if (config.calculation.updateInterval < 1)
		{
			errors.push({
				field: 'calculation.updateInterval',
				message: 'Update interval must be at least 1 hour',
				severity: 'error',
			});
		}

		if (config.calculation.batchSize < 1)
		{
			errors.push({
				field: 'calculation.batchSize',
				message: 'Batch size must be at least 1',
				severity: 'error',
			});
		}

		if (config.calculation.batchSize > 10000)
		{
			warnings.push({
				field: 'calculation.batchSize',
				message: 'Large batch sizes may impact performance',
			});
		}
	}

	// Thresholds validation
	if (config.thresholds)
	{
		if (config.thresholds.minScore >= config.thresholds.maxScore)
		{
			errors.push({
				field: 'thresholds.minScore',
				message: 'Minimum score must be less than maximum score',
				severity: 'error',
			});
		}

		if (config.thresholds.qualityThreshold < config.thresholds.minScore ||
        config.thresholds.qualityThreshold > config.thresholds.maxScore)
		{
			errors.push({
				field: 'thresholds.qualityThreshold',
				message: 'Quality threshold must be between min and max score',
				severity: 'error',
			});
		}
	}

	// Categories validation
	if (config.categories)
	{
		if (config.categories.confidenceThreshold < 0 || config.categories.confidenceThreshold > 1)
		{
			errors.push({
				field: 'categories.confidenceThreshold',
				message: 'Confidence threshold must be between 0 and 1',
				severity: 'error',
			});
		}

		if (config.categories.maxCategories < 1)
		{
			errors.push({
				field: 'categories.maxCategories',
				message: 'Maximum categories must be at least 1',
				severity: 'error',
			});
		}
	}
}

async function validateAlertsConfiguration(
	config: any,
	errors: { field: string; message: string; severity: 'error' | 'warning' }[],
	warnings: { field: string; message: string }[],
	// dependencies: { field: string; dependsOn: string[]; satisfied: boolean }[],
)
{
	if (!Array.isArray(config))
	{
		errors.push({
			field: 'alerts',
			message: 'Alerts configuration must be an array',
			severity: 'error',
		});
		return;
	}

	config.forEach((alert, index) =>
	{
		// Basic validation
		if (!alert.name || alert.name.trim().length === 0)
		{
			errors.push({
				field: `alerts[${index}].name`,
				message: 'Alert name is required',
				severity: 'error',
			});
		}

		if (!alert.conditions || !Array.isArray(alert.conditions) || alert.conditions.length === 0)
		{
			errors.push({
				field: `alerts[${index}].conditions`,
				message: 'At least one condition is required',
				severity: 'error',
			});
		}

		// Validate conditions
		alert.conditions?.forEach((condition: any, condIndex: number) =>
		{
			if (!condition.metric)
			{
				errors.push({
					field: `alerts[${index}].conditions[${condIndex}].metric`,
					message: 'Condition metric is required',
					severity: 'error',
				});
			}

			if (!['>', '<', '=', '>=', '<=', '!=', 'contains', 'not_contains'].includes(condition.operator))
			{
				errors.push({
					field: `alerts[${index}].conditions[${condIndex}].operator`,
					message: 'Invalid operator',
					severity: 'error',
				});
			}

			if (condition.threshold === undefined || condition.threshold === null)
			{
				errors.push({
					field: `alerts[${index}].conditions[${condIndex}].threshold`,
					message: 'Condition threshold is required',
					severity: 'error',
				});
			}

			if (condition.duration < 0)
			{
				errors.push({
					field: `alerts[${index}].conditions[${condIndex}].duration`,
					message: 'Duration must be non-negative',
					severity: 'error',
				});
			}
		});

		// Validate notifications
		if (alert.notifications)
		{
			if (alert.notifications.email?.enabled && (!alert.notifications.email.recipients || alert.notifications.email.recipients.length === 0))
			{
				warnings.push({
					field: `alerts[${index}].notifications.email.recipients`,
					message: 'Email recipients are required when email notifications are enabled',
				});
			}

			if (alert.notifications.webhook?.enabled && (!alert.notifications.webhook.urls || alert.notifications.webhook.urls.length === 0))
			{
				warnings.push({
					field: `alerts[${index}].notifications.webhook.urls`,
					message: 'Webhook URLs are required when webhook notifications are enabled',
				});
			}
		}

		// Validate escalation
		if (alert.escalation?.enabled && (!alert.escalation.levels || alert.escalation.levels.length === 0))
		{
			warnings.push({
				field: `alerts[${index}].escalation.levels`,
				message: 'Escalation levels are required when escalation is enabled',
			});
		}
	});
}

async function validateAIServicesConfiguration(
	config: any,
	errors: { field: string; message: string; severity: 'error' | 'warning' }[],
	warnings: { field: string; message: string }[],
	// dependencies: { field: string; dependsOn: string[]; satisfied: boolean }[],
)
{
	// Providers validation
	if (config.providers)
	{
		const enabledProviders = Object.entries(config.providers).filter(([, settings]: [string, any]) => settings.enabled);

		if (enabledProviders.length === 0)
		{
			warnings.push({
				field: 'providers',
				message: 'No AI providers are enabled',
			});
		}

		Object.entries(config.providers).forEach(([provider, settings]: [string, any]) =>
		{
			if (settings.enabled)
			{
				if (!settings.apiKey || settings.apiKey.trim().length === 0)
				{
					errors.push({
						field: `providers.${provider}.apiKey`,
						message: `API key is required for enabled provider ${provider}`,
						severity: 'error',
					});
				}

				if (!settings.baseUrl || settings.baseUrl.trim().length === 0)
				{
					errors.push({
						field: `providers.${provider}.baseUrl`,
						message: `Base URL is required for provider ${provider}`,
						severity: 'error',
					});
				}

				if (settings.maxTokens < 1)
				{
					errors.push({
						field: `providers.${provider}.maxTokens`,
						message: 'Max tokens must be at least 1',
						severity: 'error',
					});
				}

				if (settings.temperature < 0 || settings.temperature > 2)
				{
					warnings.push({
						field: `providers.${provider}.temperature`,
						message: 'Temperature should be between 0 and 2',
					});
				}

				if (settings.rateLimit < 1)
				{
					errors.push({
						field: `providers.${provider}.rateLimit`,
						message: 'Rate limit must be at least 1',
						severity: 'error',
					});
				}

				if (!settings.models || settings.models.length === 0)
				{
					errors.push({
						field: `providers.${provider}.models`,
						message: 'At least one model must be specified',
						severity: 'error',
					});
				}

				if (settings.defaultModel && !settings.models.includes(settings.defaultModel))
				{
					errors.push({
						field: `providers.${provider}.defaultModel`,
						message: 'Default model must be in the models list',
						severity: 'error',
					});
				}
			}
		});
	}

	// Failover validation
	if (config.failover)
	{
		if (!['round_robin', 'priority', 'load_based'].includes(config.failover.strategy))
		{
			errors.push({
				field: 'failover.strategy',
				message: 'Invalid failover strategy',
				severity: 'error',
			});
		}

		if (config.failover.retryAttempts < 0)
		{
			errors.push({
				field: 'failover.retryAttempts',
				message: 'Retry attempts must be non-negative',
				severity: 'error',
			});
		}

		if (config.failover.backoffMultiplier < 1)
		{
			errors.push({
				field: 'failover.backoffMultiplier',
				message: 'Backoff multiplier must be at least 1',
				severity: 'error',
			});
		}
	}

	// Content validation
	if (config.content)
	{
		if (config.content.maxLength < 1)
		{
			errors.push({
				field: 'content.maxLength',
				message: 'Max length must be at least 1',
				severity: 'error',
			});
		}

		if (config.content.qualityThreshold < 0 || config.content.qualityThreshold > 1)
		{
			errors.push({
				field: 'content.qualityThreshold',
				message: 'Quality threshold must be between 0 and 1',
				severity: 'error',
			});
		}

		if (config.content.cacheDuration < 0)
		{
			errors.push({
				field: 'content.cacheDuration',
				message: 'Cache duration must be non-negative',
				severity: 'error',
			});
		}
	}

	// Usage limits validation
	if (config.usage)
	{
		if (config.usage.dailyLimit < 0)
		{
			errors.push({
				field: 'usage.dailyLimit',
				message: 'Daily limit must be non-negative',
				severity: 'error',
			});
		}

		if (config.usage.monthlyLimit < config.usage.dailyLimit * 30)
		{
			warnings.push({
				field: 'usage.monthlyLimit',
				message: 'Monthly limit should be at least 30 times the daily limit',
			});
		}

		if (config.usage.costLimit < 0)
		{
			errors.push({
				field: 'usage.costLimit',
				message: 'Cost limit must be non-negative',
				severity: 'error',
			});
		}

		if (config.usage.alertThreshold < 0 || config.usage.alertThreshold > 1)
		{
			errors.push({
				field: 'usage.alertThreshold',
				message: 'Alert threshold must be between 0 and 1',
				severity: 'error',
			});
		}
	}
}

async function validateSeederConfiguration(
	config: any,
	errors: { field: string; message: string; severity: 'error' | 'warning' }[],
	warnings: { field: string; message: string }[],
	// dependencies: { field: string; dependsOn: string[]; satisfied: boolean }[],
)
{
	// Discovery strategies validation
	if (config.discovery?.strategies)
	{
		const enabledStrategies = Object.entries(config.discovery.strategies).filter(([, settings]: [string, any]) => settings.enabled);

		if (enabledStrategies.length === 0)
		{
			warnings.push({
				field: 'discovery.strategies',
				message: 'No discovery strategies are enabled',
			});
		}

		Object.entries(config.discovery.strategies).forEach(([strategy, settings]: [string, any]) =>
		{
			if (settings.enabled)
			{
				if (settings.priority < 1)
				{
					errors.push({
						field: `discovery.strategies.${strategy}.priority`,
						message: 'Priority must be at least 1',
						severity: 'error',
					});
				}

				if (settings.batchSize < 1)
				{
					errors.push({
						field: `discovery.strategies.${strategy}.batchSize`,
						message: 'Batch size must be at least 1',
						severity: 'error',
					});
				}

				if (settings.interval < 60)
				{
					warnings.push({
						field: `discovery.strategies.${strategy}.interval`,
						message: 'Interval should be at least 60 seconds to avoid overloading',
					});
				}
			}
		});
	}

	// Connectors validation
	if (config.connectors)
	{
		const enabledConnectors = Object.entries(config.connectors).filter(([, settings]: [string, any]) => settings.enabled);

		if (enabledConnectors.length === 0)
		{
			warnings.push({
				field: 'connectors',
				message: 'No connectors are enabled',
			});
		}

		Object.entries(config.connectors).forEach(([connector, settings]: [string, any]) =>
		{
			if (settings.enabled)
			{
				// API key validation for connectors that require it
				if (['tranco', 'radar', 'umbrella'].includes(connector) && (!settings.apiKey || settings.apiKey.trim().length === 0))
				{
					errors.push({
						field: `connectors.${connector}.apiKey`,
						message: `API key is required for enabled connector ${connector}`,
						severity: 'error',
					});
				}

				// Username/password validation for CZDS
				if (connector === 'czds')
				{
					if (!settings.username || settings.username.trim().length === 0)
					{
						errors.push({
							field: `connectors.${connector}.username`,
							message: 'Username is required for CZDS connector',
							severity: 'error',
						});
					}

					if (!settings.password || settings.password.trim().length === 0)
					{
						errors.push({
							field: `connectors.${connector}.password`,
							message: 'Password is required for CZDS connector',
							severity: 'error',
						});
					}
				}

				if (settings.rateLimit < 1)
				{
					errors.push({
						field: `connectors.${connector}.rateLimit`,
						message: 'Rate limit must be at least 1',
						severity: 'error',
					});
				}

				if (settings.timeout < 1000)
				{
					warnings.push({
						field: `connectors.${connector}.timeout`,
						message: 'Timeout should be at least 1000ms',
					});
				}

				if (settings.priority < 1)
				{
					errors.push({
						field: `connectors.${connector}.priority`,
						message: 'Priority must be at least 1',
						severity: 'error',
					});
				}

				// CommonCrawl specific validation
				if (connector === 'commoncrawl' && settings.maxPages < 1)
				{
					errors.push({
						field: `connectors.${connector}.maxPages`,
						message: 'Max pages must be at least 1',
						severity: 'error',
					});
				}
			}
		});
	}

	// Processing validation
	if (config.processing)
	{
		if (config.processing.maxDomainsPerDay < 1)
		{
			errors.push({
				field: 'processing.maxDomainsPerDay',
				message: 'Maximum domains per day must be at least 1',
				severity: 'error',
			});
		}

		if (config.processing.batchSize < 1)
		{
			errors.push({
				field: 'processing.batchSize',
				message: 'Batch size must be at least 1',
				severity: 'error',
			});
		}

		if (config.processing.concurrentProcessing < 1)
		{
			errors.push({
				field: 'processing.concurrentProcessing',
				message: 'Concurrent processing must be at least 1',
				severity: 'error',
			});
		}

		if (config.processing.queueDepthLimit < config.processing.batchSize)
		{
			warnings.push({
				field: 'processing.queueDepthLimit',
				message: 'Queue depth limit should be at least equal to batch size',
			});
		}

		if (config.processing.retryAttempts < 0)
		{
			errors.push({
				field: 'processing.retryAttempts',
				message: 'Retry attempts must be non-negative',
				severity: 'error',
			});
		}

		if (config.processing.backoffMultiplier < 1)
		{
			errors.push({
				field: 'processing.backoffMultiplier',
				message: 'Backoff multiplier must be at least 1',
				severity: 'error',
			});
		}
	}

	// Content generation validation
	if (config.contentGeneration)
	{
		if (!['pregenerated', 'live', 'hybrid'].includes(config.contentGeneration.mode))
		{
			errors.push({
				field: 'contentGeneration.mode',
				message: 'Invalid content generation mode',
				severity: 'error',
			});
		}

		if (config.contentGeneration.qualityThreshold < 0 || config.contentGeneration.qualityThreshold > 1)
		{
			errors.push({
				field: 'contentGeneration.qualityThreshold',
				message: 'Quality threshold must be between 0 and 1',
				severity: 'error',
			});
		}

		if (config.contentGeneration.maxRetries < 0)
		{
			errors.push({
				field: 'contentGeneration.maxRetries',
				message: 'Max retries must be non-negative',
				severity: 'error',
			});
		}

		if (config.contentGeneration.cacheDuration < 0)
		{
			errors.push({
				field: 'contentGeneration.cacheDuration',
				message: 'Cache duration must be non-negative',
				severity: 'error',
			});
		}
	}
}

async function analyzeConfigImpact(
	category: ConfigCategoryType,
	// newConfig: unknown,
	// currentConfig: unknown,
): Promise<ConfigImpactAnalysisType>
{
	const affectedServices: string[] = [];
	let restartRequired = false;
	let estimatedDowntime = 0;
	let riskLevel: 'low' | 'medium' | 'high' | 'critical' = 'low';
	const dependencies: { service: string; impact: string; mitigation: string }[] = [];
	const recommendations: string[] = [];
	const rollbackPlan: string[] = [];

	// Analyze impact based on category
	switch (category)
	{
		case 'database':
			affectedServices.push('web-app', 'crawler', 'ranking-engine', 'scheduler', 'domain-seeder', 'admin');
			restartRequired = true;
			estimatedDowntime = 30; // seconds
			riskLevel = 'high';

			dependencies.push({
				service: 'all',
				impact: 'Database connection changes require service restart',
				mitigation: 'Use staged deployment with health checks',
			});

			recommendations.push('Test database connections before deployment');
			recommendations.push('Schedule deployment during low-traffic period');

			rollbackPlan.push('Revert database configuration');
			rollbackPlan.push('Restart all services with previous configuration');
			break;

		case 'crawling':
			affectedServices.push('crawler', 'scheduler');
			restartRequired = false;
			estimatedDowntime = 0;
			riskLevel = 'medium';

			dependencies.push({
				service: 'crawler',
				impact: 'Rate limit changes affect crawling performance',
				mitigation: 'Monitor crawl queue and adjust limits gradually',
			});

			recommendations.push('Monitor crawl performance after changes');
			recommendations.push('Adjust rate limits gradually to avoid queue backup');

			rollbackPlan.push('Revert crawling configuration');
			rollbackPlan.push('Clear any backed-up crawl queues');
			break;

		case 'ranking':
			affectedServices.push('ranking-engine', 'web-app');
			restartRequired = false;
			estimatedDowntime = 0;
			riskLevel = 'medium';

			dependencies.push({
				service: 'ranking-engine',
				impact: 'Weight changes affect ranking calculations',
				mitigation: 'Recalculate rankings after deployment',
			});

			recommendations.push('Trigger ranking recalculation after deployment');
			recommendations.push('Monitor ranking changes for unexpected results');

			rollbackPlan.push('Revert ranking configuration');
			rollbackPlan.push('Recalculate rankings with previous weights');
			break;

		case 'alerts':
			affectedServices.push('admin');
			restartRequired = false;
			estimatedDowntime = 0;
			riskLevel = 'low';

			recommendations.push('Test alert notifications after deployment');

			rollbackPlan.push('Revert alert configuration');
			break;

		case 'ai_services':
			affectedServices.push('domain-seeder');
			restartRequired = false;
			estimatedDowntime = 0;
			riskLevel = 'medium';

			dependencies.push({
				service: 'domain-seeder',
				impact: 'AI provider changes affect content generation',
				mitigation: 'Test AI providers before enabling',
			});

			recommendations.push('Test AI provider connections');
			recommendations.push('Monitor content generation quality');

			rollbackPlan.push('Revert AI services configuration');
			rollbackPlan.push('Clear any failed content generation jobs');
			break;

		case 'seeder':
			affectedServices.push('domain-seeder');
			restartRequired = false;
			estimatedDowntime = 0;
			riskLevel = 'medium';

			dependencies.push({
				service: 'domain-seeder',
				impact: 'Connector and strategy changes affect domain discovery',
				mitigation: 'Monitor discovery rates and queue depth',
			});

			recommendations.push('Monitor domain discovery rates');
			recommendations.push('Check connector health after changes');

			rollbackPlan.push('Revert seeder configuration');
			rollbackPlan.push('Clear any backed-up discovery queues');
			break;
	}

	return {
		affectedServices,
		restartRequired,
		estimatedDowntime,
		riskLevel,
		dependencies,
		recommendations,
		rollbackPlan,
	};
}
