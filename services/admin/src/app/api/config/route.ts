import { NextRequest, NextResponse } from 'next/server';
import { validateSession } from '@/lib/auth/session';

import db from '@/lib/database/client';
import { apiLogger } from '@/lib/logger';
import type {
	SystemConfigType,
	ConfigCategoryType,
	ConfigValidationResultType,
} from '@/types/config';

// Local precise types to avoid 'any' in validation helpers
type ErrorEntryType = { field: string; message: string; severity: 'error' | 'warning' };
type WarningEntryType = { field: string; message: string };
type DependencyEntryType = { field: string; dependsOn: string[]; satisfied: boolean };

type DatabaseConfigLocalType =
{
	scylla?: { hosts?: string[]; poolSize?: number };
	mariadb?: { host?: string; port?: number };
};

type CrawlingConfigLocalType =
{
	rateLimit?: { global?: number; perDomain?: number };
	concurrent?: { maxRequests?: number };
};

type RankingConfigLocalType =
{
	weights?: Record<string, number>;
};

type AlertItemLocalType = { name?: string; conditions?: unknown[] };
type AlertsConfigLocalType = AlertItemLocalType[];

type AIProvidersLocalType = Record<string, { enabled?: boolean; apiKey?: string; maxTokens?: number }>;
type AIServicesConfigLocalType = { providers?: AIProvidersLocalType };

type SeederConnectorsLocalType = Record<string, { enabled?: boolean; apiKey?: string }>;
type SeederConfigLocalType = { processing?: { maxDomainsPerDay?: number }; connectors?: SeederConnectorsLocalType };

const CategoryToPropKey: Record<ConfigCategoryType, keyof SystemConfigType> =
{
	database: 'database',
	crawling: 'crawling',
	ranking: 'ranking',
	alerts: 'alerts',
	ai_services: 'aiServices',
	seeder: 'seeder',
};

function assignCategory<K extends keyof SystemConfigType>(
	target: Partial<SystemConfigType>,
	key: K,
	value: unknown,
): void
{
	(target as Record<K, SystemConfigType[K]>)[key] = value as SystemConfigType[K];
}

export async function GET(request: NextRequest)
{
	try
	{
		const session = await validateSession(request);
		if (!session)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		const { searchParams } = new URL(request.url);
		const category = searchParams.get('category') as ConfigCategoryType;
		const environment = searchParams.get('environment') || 'production';

		let config: Partial<SystemConfigType>;

		if (category)
		{
			// Get specific category configuration
			config = await getConfigByCategory(category, environment);
		}
		else
		{
			// Get all configuration
			config = await getAllConfig(environment);
		}

		return NextResponse.json({
			success: true,
			data: config,
			metadata: {
				environment,
				lastModified: new Date(),
				version: '1.0.0',
			},
		});
	}
	catch (error)
	{
		apiLogger.error({ error }, 'Failed to get configuration');
		return NextResponse.json(
			{ error: 'Failed to get configuration' },
			{ status: 500 },
		);
	}
}

export async function PUT(request: NextRequest)
{
	try
	{
		const session = await validateSession(request);
		if (!session)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		const body = await request.json();
		const {
			category,
			config,
			reason,
			environment = 'production',
		} = body;

		// Validate the configuration
		const validation = await validateConfig(category, config);
		if (!validation.valid)
		{
			return NextResponse.json({
				success: false,
				error: 'Configuration validation failed',
				validation,
			}, { status: 400 });
		}

		// Save configuration changes
		const changeId = await saveConfigChanges(
			category,
			config,
			session.username,
			session.username,
			reason,
			environment,
		);

		// Log the configuration change
		await logConfigChange(changeId, category, config, session.username);

		return NextResponse.json({
			success: true,
			data: {
				changeId,
				validation,
				message: 'Configuration updated successfully',
			},
		});
	}
	catch (error)
	{
		apiLogger.error({ error }, 'Failed to update configuration');
		return NextResponse.json(
				{ error: 'Failed to update configuration' },
				{ status: 500 },
		);
	}
}

async function getConfigByCategory(
	category: ConfigCategoryType,
	environment: string,
): Promise<Partial<SystemConfigType>>
{
	const query = `
	SELECT config_data
	FROM system_config
	WHERE category = ? AND environment = ? AND active = true
	ORDER BY version DESC
	LIMIT 1
	`;

	const result = await db.mariadb.query<{ config_data: string }>(query, [category, environment]);

	if (result.length === 0)
	{
		const def = getDefaultConfig(category);
		return { [CategoryToPropKey[category]]: def } as Partial<SystemConfigType>;
	}

	const parsed = JSON.parse(result[0].config_data);
	return { [CategoryToPropKey[category]]: parsed } as Partial<SystemConfigType>;
}

async function getAllConfig(environment: string): Promise<Partial<SystemConfigType>>
{
	const query = `
		SELECT category, config_data
		FROM system_config
		WHERE environment = ? AND active = true
		GROUP BY category
		HAVING version = MAX(version)
		`;

	const results = await db.mariadb.query<{ category: string; config_data: string }>(query, [environment]);

	const config: Partial<SystemConfigType> = {};

	for (const row of results)
	{
		const cat = row.category as ConfigCategoryType;
		const key = CategoryToPropKey[cat];
		const value = JSON.parse(row.config_data) as unknown;
		assignCategory(config, key, value);
	}

	// Fill in missing categories with defaults
	const categories: ConfigCategoryType[] = [
		'database', 'crawling', 'ranking', 'alerts', 'ai_services', 'seeder',
	];

	for (const cat of categories)
	{
		const key = CategoryToPropKey[cat];
		if (!(key in config))
		{
			const def = getDefaultConfig(cat);
			assignCategory(config, key, def);
		}
	}

	return config;
}

async function validateConfig(
	category: ConfigCategoryType,
	config: unknown,
): Promise<ConfigValidationResultType>
{
	const errors: ErrorEntryType[] = [];
	const warnings: WarningEntryType[] = [];
	const dependencies: DependencyEntryType[] = [];

	// Category-specific validation
	switch (category)
	{
		case 'database':
			validateDatabaseConfig(config as DatabaseConfigLocalType, errors, warnings, dependencies);
		break;
		case 'crawling':
			validateCrawlingConfig(config as CrawlingConfigLocalType, errors, warnings, dependencies);
		break;
		case 'ranking':
			validateRankingConfig(config as RankingConfigLocalType, errors, warnings, dependencies);
		break;
		case 'alerts':
			validateAlertsConfig(config as AlertsConfigLocalType, errors, warnings, dependencies);
		break;
		case 'ai_services':
			validateAIServicesConfig(config as AIServicesConfigLocalType, errors, warnings, dependencies);
		break;
		case 'seeder':
			validateSeederConfig(config as SeederConfigLocalType, errors, warnings, dependencies);
		break;
    }

    return ({
			valid: errors.filter(e => e.severity === 'error').length === 0,
			errors,
			warnings,
			dependencies,
    });
}

function validateDatabaseConfig(
	config: DatabaseConfigLocalType,
	errors: ErrorEntryType[],
	warnings: WarningEntryType[],
	// dependencies: DependencyEntryType[],
)
{
    // Validate ScyllaDB configuration
    if (config.scylla)
    {
			if (!config.scylla.hosts || config.scylla.hosts.length === 0)
			{
				errors.push({
					field: 'scylla.hosts',
					message: 'At least one ScyllaDB host is required',
					severity: 'error',
				});
			}

			if ((config.scylla.poolSize ?? 0) < 1 || (config.scylla.poolSize ?? 0) > 100)
			{
				warnings.push({
					field: 'scylla.poolSize',
					message: 'Pool size should be between 1 and 100',
				});
			}
    }

    // Validate MariaDB configuration
    if (config.mariadb)
    {
			if (!config.mariadb.host)
			{
				errors.push({
					field: 'mariadb.host',
					message: 'MariaDB host is required',
					severity: 'error',
				});
			}

			if ((config.mariadb.port ?? 0) < 1 || (config.mariadb.port ?? 0) > 65535)
			{
				errors.push({
					field: 'mariadb.port',
					message: 'Port must be between 1 and 65535',
					severity: 'error',
				});
			}
    }

    // Add more database validation rules...
}

function validateCrawlingConfig(
	config: CrawlingConfigLocalType,
	errors: ErrorEntryType[],
	warnings: WarningEntryType[],
	// dependencies: DependencyEntryType[],
)
{
	if (config.rateLimit)
	{
		if ((config.rateLimit.global ?? 0) < 1)
		{
			errors.push({
				field: 'rateLimit.global',
				message: 'Global rate limit must be at least 1',
				severity: 'error',
			});
		}

		if ((config.rateLimit.perDomain ?? 0) > (config.rateLimit.global ?? 0))
		{
			warnings.push({
				field: 'rateLimit.perDomain',
				message: 'Per-domain rate limit should not exceed global rate limit',
			});
		}
	}

	if (config.concurrent)
	{
		if ((config.concurrent.maxRequests ?? 0) < 1)
		{
			errors.push({
				field: 'concurrent.maxRequests',
				message: 'Maximum concurrent requests must be at least 1',
				severity: 'error',
			});
		}
	}

	// Add more crawling validation rules...
}

function validateRankingConfig(
	config: RankingConfigLocalType,
	errors: ErrorEntryType[],
	// warnings: WarningEntryType[],
	// dependencies: DependencyEntryType[],
)
{
	if (config.weights)
	{
		const totalWeight = Object.values(config.weights).reduce((sum: number, weight: number) => sum + weight, 0);

		if (Math.abs(totalWeight - 1.0) > 0.001)
		{
			errors.push({
				field: 'weights',
				message: 'Ranking weights must sum to 1.0',
				severity: 'error',
			});
		}

		Object.entries(config.weights).forEach(([key, weight]) =>
		{
			if (typeof weight !== 'number' || weight < 0 || weight > 1)
			{
				errors.push({
					field: `weights.${key}`,
					message: 'Weight must be a number between 0 and 1',
					severity: 'error',
				});
			}
		});
	}

	// Add more ranking validation rules...
}

function validateAlertsConfig(
	config: AlertsConfigLocalType,
	errors: ErrorEntryType[],
	// warnings: WarningEntryType[],
	// dependencies: DependencyEntryType[],
)
{
	if (Array.isArray(config))
	{
		config.forEach((alert, index) =>
		{
			if (!alert.name)
			{
				errors.push({
					field: `alerts[${index}].name`,
					message: 'Alert name is required',
					severity: 'error',
				});
			}

			if (!alert.conditions || alert.conditions.length === 0)
			{
				errors.push({
					field: `alerts[${index}].conditions`,
					message: 'At least one condition is required',
					severity: 'error',
				});
			}
		});
	}

	// Add more alerts validation rules...
}

function validateAIServicesConfig(
	config: AIServicesConfigLocalType,
	errors: ErrorEntryType[],
	// warnings: WarningEntryType[],
	// dependencies: DependencyEntryType[],
)
{
	if (config.providers)
	{
		Object.entries(config.providers).forEach(([provider, settings]: [string, { enabled?: boolean; apiKey?: string; maxTokens?: number }]) =>
		{
			if (settings.enabled && !settings.apiKey)
			{
				errors.push({
					field: `providers.${provider}.apiKey`,
					message: `API key is required for enabled provider ${provider}`,
					severity: 'error',
				});
			}

			if ((settings.maxTokens ?? 0) < 1)
			{
				errors.push({
					field: `providers.${provider}.maxTokens`,
					message: 'Max tokens must be at least 1',
					severity: 'error',
				});
			}
		});
	}

	// Add more AI services validation rules...
}

function validateSeederConfig(
	config: SeederConfigLocalType,
	errors: ErrorEntryType[],
	// warnings: WarningEntryType[],
	// dependencies: DependencyEntryType[],
)
{
	if (config.processing)
	{
		if ((config.processing.maxDomainsPerDay ?? 0) < 1)
		{
			errors.push({
				field: 'processing.maxDomainsPerDay',
				message: 'Maximum domains per day must be at least 1',
				severity: 'error',
			});
		}
	}

	if (config.connectors)
	{
		Object.entries(config.connectors).forEach(([connector, settings]: [string, { enabled?: boolean; apiKey?: string }]) =>
		{
			if (settings.enabled && settings.apiKey === undefined && ['tranco', 'radar', 'umbrella'].includes(connector))
			{
				errors.push({
					field: `connectors.${connector}.apiKey`,
					message: `API key is required for enabled connector ${connector}`,
					severity: 'error',
				});
			}
		});
	}

	// Add more seeder validation rules...
}

async function saveConfigChanges(
	category: ConfigCategoryType,
	config: unknown,
	userId: string,
	username: string,
	reason: string,
	environment: string,
): Promise<string>
{
	const changeId = `change_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

	const query = `
	INSERT INTO config_changes (
	id, category, config_data, user_id, username, reason,
	environment, created_at, approved, deployed
	) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), false, false)
	`;

	await db.mariadb.query(query, [
		changeId,
		category,
		JSON.stringify(config),
		userId,
		username,
		reason,
		environment,
	]);

	return changeId;
}

async function logConfigChange(
    changeId: string,
    category: ConfigCategoryType,
    config: unknown,
    username: string,
): Promise<void>
{
    apiLogger.info({
        changeId,
        category,
        username,
        timestamp: new Date(),
    }, 'Configuration change recorded');
}

function getDefaultConfig(category: ConfigCategoryType): unknown
{
	const defaults =
	{
		database:
		{
			scylla:
			{
				hosts: ['localhost:9042'],
				keyspace: 'domainr',
				username: '',
				password: '',
				poolSize: 10,
				timeout: 30000,
				retryAttempts: 3,
				ssl: false,
				compression: true,
				healthCheckInterval: 30000,
				failoverEnabled: false,
				failoverHosts: [],
			},
            mariadb: {
                host: 'localhost',
                port: 3306,
                database: 'domainr',
                username: 'root',
                password: '',
                poolSize: 10,
                timeout: 30000,
                retryAttempts: 3,
                ssl: false,
                charset: 'utf8mb4',
                timezone: 'UTC',
                healthCheckInterval: 30000,
            },
            redis: {
                host: 'localhost',
                port: 6379,
                password: '',
                database: 0,
                poolSize: 10,
                timeout: 30000,
                retryAttempts: 3,
                ssl: false,
                keyPrefix: 'domainr:',
                healthCheckInterval: 30000,
                cluster: false,
                clusterNodes: [],
            },
            manticore: {
                host: 'localhost',
                port: 9308,
                timeout: 30000,
                retryAttempts: 3,
                ssl: false,
                healthCheckInterval: 30000,
                indexPrefix: 'domainr_',
                maxQueryTime: 10000,
            },
        },
        crawling: {
            rateLimit: {
                global: 100,
                perDomain: 10,
                burstLimit: 200,
                windowSize: 60,
            },
            userAgents: {
                rotation: true,
                agents: [
                    'Mozilla/5.0 (compatible; DomainrBot/1.0)',
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                ],
                customAgent: '',
            },
            intervals: {
                priority: 1,
                standard: 24,
                full: 168,
                light: 12,
                visual: 72,
                advanced: 336,
            },
            concurrent: {
                maxRequests: 50,
                maxPerDomain: 5,
                queueSize: 1000,
            },
            timeouts: {
                request: 30000,
                dns: 5000,
                connect: 10000,
                response: 30000,
            },
            retries: {
                maxAttempts: 3,
                backoffMultiplier: 2,
                maxBackoff: 60000,
            },
            resources: {
                maxMemory: 2048,
                maxCpu: 80,
                diskSpace: 10240,
            },
        },
        ranking: {
            weights: {
                performance: 0.3,
                security: 0.2,
                seo: 0.2,
                technical: 0.2,
                backlinks: 0.1,
            },
            calculation: {
                updateInterval: 24,
                batchSize: 1000,
                parallelProcessing: true,
                cacheResults: true,
            },
            thresholds: {
                minScore: 0,
                maxScore: 100,
                qualityThreshold: 50,
            },
            categories: {
                autoAssignment: true,
                confidenceThreshold: 0.8,
                maxCategories: 3,
            },
        },
        alerts: [],
        ai_services: {
            providers: {
                openai: {
                    enabled: false,
                    apiKey: '',
                    baseUrl: 'https://api.openai.com/v1',
                    models: ['gpt-4', 'gpt-3.5-turbo'],
                    defaultModel: 'gpt-3.5-turbo',
                    maxTokens: 2048,
                    temperature: 0.7,
                    timeout: 30000,
                    rateLimit: 60,
                    costPerToken: 0.002,
                },
                claude: {
                    enabled: false,
                    apiKey: '',
                    baseUrl: 'https://api.anthropic.com/v1',
                    models: ['claude-3-sonnet', 'claude-3-haiku'],
                    defaultModel: 'claude-3-haiku',
                    maxTokens: 2048,
                    temperature: 0.7,
                    timeout: 30000,
                    rateLimit: 60,
                    costPerToken: 0.0015,
                },
                google: {
                    enabled: false,
                    apiKey: '',
                    baseUrl: 'https://generativelanguage.googleapis.com/v1',
                    models: ['gemini-pro', 'gemini-pro-vision'],
                    defaultModel: 'gemini-pro',
                    maxTokens: 2048,
                    temperature: 0.7,
                    timeout: 30000,
                    rateLimit: 60,
                    costPerToken: 0.001,
                },
            },
            failover: {
                enabled: true,
                strategy: 'priority',
                retryAttempts: 3,
                backoffMultiplier: 2,
            },
            content: {
                maxLength: 500,
                qualityThreshold: 0.8,
                moderationEnabled: true,
                cacheResults: true,
                cacheDuration: 86400,
            },
            usage: {
                dailyLimit: 10000,
                monthlyLimit: 300000,
                costLimit: 100,
                alertThreshold: 0.8,
            },
        },
        seeder: {
            discovery: {
                strategies: {
                    differential: {
											enabled: true,
											priority: 1,
											batchSize: 1000,
											interval: 3600,
                    },
                    zoneNew: {
											enabled: true,
											priority: 2,
											batchSize: 500,
											interval: 7200,
                    },
                    longTail: {
											enabled: false,
											priority: 3,
											batchSize: 100,
											interval: 14400,
                    },
                    temporal: {
											enabled: false,
											priority: 4,
											batchSize: 200,
											interval: 21600,
                    },
                },
            },
            connectors: {
                tranco: {
									enabled: true,
									apiKey: '',
									rateLimit: 100,
									timeout: 30000,
									priority: 1,
                },
                radar: {
									enabled: false,
									apiKey: '',
									rateLimit: 100,
									timeout: 30000,
									priority: 2,
                },
                commoncrawl: {
									enabled: true,
									rateLimit: 50,
									timeout: 60000,
									priority: 3,
									maxPages: 1000,
                },
                czds: {
									enabled: false,
									username: '',
									password: '',
									rateLimit: 10,
									timeout: 60000,
									priority: 4,
                },
                umbrella: {
									enabled: false,
									apiKey: '',
									rateLimit: 100,
									timeout: 30000,
									priority: 5,
                },
                sonar: {
									enabled: true,
									rateLimit: 50,
									timeout: 30000,
									priority: 6,
                },
            },
            processing: {
							maxDomainsPerDay: 10000,
							batchSize: 100,
							concurrentProcessing: 5,
							queueDepthLimit: 50000,
							retryAttempts: 3,
							backoffMultiplier: 2,
            },
            contentGeneration: {
							enabled: true,
							mode: 'hybrid',
							qualityThreshold: 0.7,
							maxRetries: 3,
							cacheResults: true,
							cacheDuration: 86400,
            },
        },
    } as const;

	return defaults[category];
}
