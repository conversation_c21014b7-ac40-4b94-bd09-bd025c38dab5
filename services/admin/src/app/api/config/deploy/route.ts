import { NextRequest, NextResponse } from 'next/server';
import { validateSession } from '@/lib/auth/session';

import db from '@/lib/database/client';
import { apiLogger } from '@/lib/logger';
import type {
	ConfigDeploymentType,
	ConfigChangeType,
} from '@/types/config';

/**
 * POST /api/config/deploy
 * Deploy configuration changes
 */
export async function POST(request: NextRequest)
{
	try
	{
		const session = await validateSession(request);
		if (!session)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		const body = await request.json();
		const { changeIds, strategy = 'immediate', scheduledAt } = body;

		// Get the changes to deploy
		const changes = await getConfigChanges(changeIds);

		if (changes.length === 0)
		{
			return NextResponse.json(
				{ error: 'No valid changes found to deploy' },
				{ status: 400 },
			);
		}

		// Create deployment record
		const deploymentId = await createDeployment({
			userId: session.username,
			username: session.username,
			changes,
			strategy,
			scheduledAt: scheduledAt ? new Date(scheduledAt) : undefined,
		});

		// Execute deployment based on strategy
		if (strategy === 'immediate')
		{
			await executeDeployment(deploymentId);
		}
		else if (strategy === 'staged')
		{
			await executeStagedDeployment(deploymentId);
		}

		return NextResponse.json({
			success: true,
			data: { deploymentId },
			message: `Configuration deployment ${strategy === 'immediate' ? 'completed' : 'initiated'} successfully`,
		});
	}
	catch (error)
	{
		apiLogger.error({ error }, 'Failed to deploy configuration');
		return NextResponse.json(
			{ error: 'Failed to deploy configuration' },
			{ status: 500 },
		);
	}
}

/**
 * GET /api/config/deploy
 * Get configuration deployments
 */
export async function GET(request: NextRequest)
{
	try
	{
		const session = await validateSession(request);
		if (!session)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		const { searchParams } = new URL(request.url);
		const limit = parseInt(searchParams.get('limit') || '50');
		const offset = parseInt(searchParams.get('offset') || '0');
		const statusParam = searchParams.get('status') || undefined;

		const deployments = await getDeployments(limit, offset, statusParam);

		return NextResponse.json({
			success: true,
			data: deployments,
		});
	}
	catch (error)
	{
		apiLogger.error({ error }, 'Failed to get deployments');
		return NextResponse.json(
			{ error: 'Failed to get deployments' },
			{ status: 500 },
		);
	}
}

async function getConfigChanges(changeIds: string[]): Promise<ConfigChangeType[]>
{
	if (changeIds.length === 0) return [];

	const placeholders = changeIds.map(() => '?').join(',');
	const query = `
    SELECT
      id, category, config_data, user_id, username, reason,
      environment, created_at, approved, deployed
    FROM config_changes
    WHERE id IN (${placeholders}) AND approved = true AND deployed = false
  `;

	const results = await db.mariadb.query(query, changeIds);

	return results.map((row: any) => ({
		id: row.id,
		timestamp: new Date(row.created_at),
		userId: row.user_id,
		username: row.username,
		category: row.category,
		field: 'bulk', // This would need to be more specific in a real implementation
		oldValue: null, // Would need to be tracked
		newValue: JSON.parse(row.config_data),
		reason: row.reason,
		approved: Boolean(row.approved),
		deployed: Boolean(row.deployed),
	}));
}

async function createDeployment(deployment: {
	userId: string;
	username: string;
	changes: ConfigChangeType[];
	strategy: string;
	scheduledAt?: Date;
}): Promise<string>
{
	const deploymentId = `deploy_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

	const query = `
    INSERT INTO config_deployments (
      id, user_id, username, changes, strategy, status,
      created_at, scheduled_at
    ) VALUES (?, ?, ?, ?, ?, 'pending', NOW(), ?)
  `;

	await db.mariadb.query(query, [
		deploymentId,
		deployment.userId,
		deployment.username,
		JSON.stringify(deployment.changes),
		deployment.strategy,
		deployment.scheduledAt,
	]);

	return deploymentId;
}

async function executeDeployment(deploymentId: string): Promise<void>
{
	try
	{
		// Update deployment status
		await updateDeploymentStatus(deploymentId, 'in_progress');

		// Get deployment details
		const deployment = await getDeploymentById(deploymentId);
		if (!deployment)
		{
			throw new Error('Deployment not found');
		}

		// Apply configuration changes
		for (const change of deployment.changes)
		{
			await applyConfigChange(change);
		}

		// Mark changes as deployed
		const changeIds = deployment.changes.map(c => c.id);
		await markChangesAsDeployed(changeIds);

		// Update deployment status
		await updateDeploymentStatus(deploymentId, 'completed');

		apiLogger.info({ deploymentId }, 'Configuration deployment completed');
	}
	catch (error)
	{
		await updateDeploymentStatus(deploymentId, 'failed', error.message);
		apiLogger.error({ deploymentId, error }, 'Configuration deployment failed');
		throw error;
	}
}

async function executeStagedDeployment(deploymentId: string): Promise<void>
{
	try
	{
		// Update deployment status
		await updateDeploymentStatus(deploymentId, 'in_progress');

		// Get deployment details
		const deployment = await getDeploymentById(deploymentId);
		if (!deployment)
		{
			throw new Error('Deployment not found');
		}

		// Define deployment stages
		const stages = [
			{
				name: 'Database Services',
				services: ['admin'],
				changes: deployment.changes.filter(c => c.category === 'database'),
			},
			{
				name: 'Core Services',
				services: ['crawler', 'ranking-engine', 'scheduler'],
				changes: deployment.changes.filter(c => ['crawling', 'ranking'].includes(c.category)),
			},
			{
				name: 'AI and Seeder Services',
				services: ['domain-seeder'],
				changes: deployment.changes.filter(c => ['ai_services', 'seeder'].includes(c.category)),
			},
			{
				name: 'Monitoring and Alerts',
				services: ['admin'],
				changes: deployment.changes.filter(c => c.category === 'alerts'),
			},
		];

		// Execute stages sequentially
		for (const stage of stages)
		{
			if (stage.changes.length === 0) continue;

			await updateStageStatus(deploymentId, stage.name, 'in_progress');

			try
			{
				// Apply changes for this stage
				for (const change of stage.changes)
				{
					await applyConfigChange(change);
				}

				// Wait for services to stabilize
				await new Promise(resolve => setTimeout(resolve, 5000));

				// Check service health
				const healthCheck = await checkServicesHealth(stage.services);
				if (!healthCheck.healthy)
				{
					throw new Error(`Health check failed for services: ${healthCheck.unhealthyServices.join(', ')}`);
				}

				await updateStageStatus(deploymentId, stage.name, 'completed');
			}
			catch (error)
			{
				await updateStageStatus(deploymentId, stage.name, 'failed', error.message);
				throw error;
			}
		}

		// Mark all changes as deployed
		const changeIds = deployment.changes.map(c => c.id);
		await markChangesAsDeployed(changeIds);

		// Update deployment status
		await updateDeploymentStatus(deploymentId, 'completed');

		apiLogger.info({ deploymentId }, 'Staged configuration deployment completed');
	}
	catch (error)
	{
		await updateDeploymentStatus(deploymentId, 'failed', error.message);
		apiLogger.error({ deploymentId, error }, 'Staged configuration deployment failed');
		throw error;
	}
}

async function applyConfigChange(change: ConfigChangeType): Promise<void>
{
	// Update the active configuration
	const query = `
    INSERT INTO system_config (category, config_data, environment, version, active, created_at)
    SELECT ?, ?, environment, COALESCE(MAX(version), 0) + 1, true, NOW()
    FROM system_config
    WHERE category = ? AND environment = 'production'
    GROUP BY environment
  `;

	await db.mariadb.query(query, [
		change.category,
		JSON.stringify(change.newValue),
		change.category,
	]);

	// Deactivate previous versions
	await db.mariadb.query(
		'UPDATE system_config SET active = false WHERE category = ? AND environment = ? AND active = true AND created_at < NOW()',
		[change.category, 'production'],
	);
}

async function updateDeploymentStatus(
	deploymentId: string,
	status: string,
	error?: string,
): Promise<void>
{
	const query = `
    UPDATE config_deployments
    SET status = ?, error = ?, completed_at = CASE WHEN ? IN ('completed', 'failed') THEN NOW() ELSE completed_at END
    WHERE id = ?
  `;

	await db.mariadb.query(query, [status, error, status, deploymentId]);
}

async function updateStageStatus(
	deploymentId: string,
	stageName: string,
	status: string,
	error?: string,
): Promise<void>
{
	// This would update the stages JSON in the deployment record
	// For simplicity, we'll log it here
	apiLogger.info({
		deploymentId,
		stageName,
		status,
		error,
	}, 'Stage status updated');
}

async function checkServicesHealth(services: string[]): Promise<{
	healthy: boolean;
	unhealthyServices: string[];
}>
{
	const unhealthyServices: string[] = [];

	// This would make actual health check requests to services
	// For now, we'll simulate it
	for (const service of services)
	{
		try
		{
			// Simulate health check
			const isHealthy = Math.random() > 0.1; // 90% success rate
			if (!isHealthy)
			{
				unhealthyServices.push(service);
			}
		}
		catch (error)
		{
			unhealthyServices.push(service);
		}
	}

	return {
		healthy: unhealthyServices.length === 0,
		unhealthyServices,
	};
}

async function markChangesAsDeployed(changeIds: string[]): Promise<void>
{
	if (changeIds.length === 0) return;

	const placeholders = changeIds.map(() => '?').join(',');
	const query = `UPDATE config_changes SET deployed = true WHERE id IN (${placeholders})`;

	await db.mariadb.query(query, changeIds);
}

async function getDeploymentById(deploymentId: string): Promise<ConfigDeploymentType | null>
{
	const query = `
    SELECT
      id, user_id, username, changes, strategy, status,
      created_at, scheduled_at, completed_at, error
    FROM config_deployments
    WHERE id = ?
  `;

	const results = await db.mariadb.query(query, [deploymentId]);

	if (results.length === 0) return null;

	const row = results[0];
	return {
		id: row.id,
		timestamp: new Date(row.created_at),
		userId: row.user_id,
		username: row.username,
		changes: JSON.parse(row.changes),
		strategy: row.strategy,
		status: row.status,
		scheduledAt: row.scheduled_at ? new Date(row.scheduled_at) : undefined,
		completedAt: row.completed_at ? new Date(row.completed_at) : undefined,
		error: row.error,
	};
}

async function getDeployments(
	limit: number,
	offset: number,
	status?: string,
): Promise<ConfigDeploymentType[]>
{
	let query = `
    SELECT
      id, user_id, username, changes, strategy, status,
      created_at, scheduled_at, completed_at, error
    FROM config_deployments
  `;

	const params: any[] = [];

	if (status)
	{
		query += ' WHERE status = ?';
		params.push(status);
	}

	query += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
	params.push(limit, offset);

	const results = await db.mariadb.query(query, params);

	return results.map((row: any) => ({
		id: row.id,
		timestamp: new Date(row.created_at),
		userId: row.user_id,
		username: row.username,
		changes: JSON.parse(row.changes),
		strategy: row.strategy,
		status: row.status,
		scheduledAt: row.scheduled_at ? new Date(row.scheduled_at) : undefined,
		completedAt: row.completed_at ? new Date(row.completed_at) : undefined,
		error: row.error,
	}));
}
