import { NextRequest, NextResponse } from 'next/server';
import { validateSession } from '@/lib/auth/session';

import db from '@/lib/database/client';
import { apiLogger } from '@/lib/logger';
import type {
	ConfigChangeType,
	ConfigCategoryType,
} from '@/types/config';

export async function GET(request: NextRequest)
{
	try
	{
		const session = await validateSession(request);
		if (!session)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		const { searchParams } = new URL(request.url);
		const categoryParam = (searchParams.get('category') as ConfigCategoryType | null) || undefined;
		const userIdParam = searchParams.get('userId') || undefined;
		const startDateParam = searchParams.get('startDate');
		const endDateParam = searchParams.get('endDate');
		const limit = parseInt(searchParams.get('limit') || '100');
		const offset = parseInt(searchParams.get('offset') || '0');

		const auditLog = await getConfigAuditLog({
			category: categoryParam,
			userId: userIdParam,
			startDate: startDateParam ? new Date(startDateParam) : undefined,
			endDate: endDateParam ? new Date(endDateParam) : undefined,
			limit,
			offset,
		});

		const totalCount = await getConfigAuditCount({
			category: categoryParam,
			userId: userIdParam,
			startDate: startDateParam ? new Date(startDateParam) : undefined,
			endDate: endDateParam ? new Date(endDateParam) : undefined,
		});

		return NextResponse.json({
			success: true,
			data: {
				changes: auditLog,
				totalCount,
				hasMore: offset + limit < totalCount,
			},
		});
	}
	catch (error)
	{
		apiLogger.error({ error }, 'Failed to get configuration audit log');
		return NextResponse.json(
			{ error: 'Failed to get configuration audit log' },
			{ status: 500 },
		);
	}
}

export async function POST(request: NextRequest)
{
	try
	{
		const session = await validateSession(request);
		if (!session)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		const body = await request.json();
		const { changeId, action, reason } = body;

		if (action === 'approve')
		{
			await approveConfigChange(changeId, session.username, session.username, reason);
		}
		else if (action === 'reject')
		{
			await rejectConfigChange(changeId, session.username, session.username, reason);
		}
		else
		{
			return NextResponse.json(
				{ error: 'Invalid action. Must be "approve" or "reject"' },
				{ status: 400 },
			);
		}

		return NextResponse.json({
			success: true,
			message: `Configuration change ${action}d successfully`,
		});
	}
	catch (error)
	{
		apiLogger.error({ error }, 'Failed to process configuration change approval');
		return NextResponse.json(
			{ error: 'Failed to process configuration change approval' },
			{ status: 500 },
		);
	}
}

async function getConfigAuditLog(filters: {
	category?: ConfigCategoryType;
	userId?: string;
	startDate?: Date;
	endDate?: Date;
	limit: number;
	offset: number;
}): Promise<ConfigChangeType[]>
{
	let query = `
    SELECT
      cc.id, cc.category, cc.config_data, cc.user_id, cc.username,
      cc.reason, cc.environment, cc.created_at, cc.approved,
      cc.approved_by, cc.approved_at, cc.deployed, cc.rollback_id,
      u1.name as user_name,
      u2.name as approver_name
    FROM config_changes cc
    LEFT JOIN users u1 ON cc.user_id = u1.id
    LEFT JOIN users u2 ON cc.approved_by = u2.id
    WHERE 1=1
  `;

	const params: any[] = [];

	if (filters.category)
	{
		query += ' AND cc.category = ?';
		params.push(filters.category);
	}

	if (filters.userId)
	{
		query += ' AND cc.user_id = ?';
		params.push(filters.userId);
	}

	if (filters.startDate)
	{
		query += ' AND cc.created_at >= ?';
		params.push(filters.startDate);
	}

	if (filters.endDate)
	{
		query += ' AND cc.created_at <= ?';
		params.push(filters.endDate);
	}

	query += ' ORDER BY cc.created_at DESC LIMIT ? OFFSET ?';
	params.push(filters.limit, filters.offset);

	const results = await db.mariadb.query(query, params);

	return results.map((row: any) => ({
		id: row.id,
		timestamp: new Date(row.created_at),
		userId: row.user_id,
		username: row.user_name || row.username,
		category: row.category,
		field: 'bulk', // This would need to be more specific
		oldValue: null, // Would need to be tracked
		newValue: JSON.parse(row.config_data),
		reason: row.reason,
		approved: Boolean(row.approved),
		approvedBy: row.approved_by,
		approvedByName: row.approver_name,
		approvedAt: row.approved_at ? new Date(row.approved_at) : undefined,
		deployed: Boolean(row.deployed),
		rollbackId: row.rollback_id,
	}));
}

async function getConfigAuditCount(filters: {
	category?: ConfigCategoryType;
	userId?: string;
	startDate?: Date;
	endDate?: Date;
}): Promise<number>
{
	let query = 'SELECT COUNT(*) as count FROM config_changes WHERE 1=1';
	const params: any[] = [];

	if (filters.category)
	{
		query += ' AND category = ?';
		params.push(filters.category);
	}

	if (filters.userId)
	{
		query += ' AND user_id = ?';
		params.push(filters.userId);
	}

	if (filters.startDate)
	{
		query += ' AND created_at >= ?';
		params.push(filters.startDate);
	}

	if (filters.endDate)
	{
		query += ' AND created_at <= ?';
		params.push(filters.endDate);
	}

	const results = await db.mariadb.query(query, params);
	return results[0].count;
}

async function approveConfigChange(
	changeId: string,
	approverId: string,
	approverName: string,
	reason?: string,
): Promise<void>
{
	const query = `
    UPDATE config_changes
    SET approved = true, approved_by = ?, approved_at = NOW(), approval_reason = ?
    WHERE id = ? AND approved = false
  `;

	const exec = await db.mariadb.execute<import('mysql2/promise').ResultSetHeader>(query, [approverId, reason, changeId]);

	if ((exec.rows as import('mysql2/promise').ResultSetHeader).affectedRows === 0)
	{
		throw new Error('Configuration change not found or already approved');
	}

	// Log the approval
	await logConfigAction(changeId, 'approved', approverId, approverName, reason);
}

async function rejectConfigChange(
	changeId: string,
	rejectorId: string,
	rejectorName: string,
	reason?: string,
): Promise<void>
{
	const query = `
    UPDATE config_changes
    SET rejected = true, rejected_by = ?, rejected_at = NOW(), rejection_reason = ?
    WHERE id = ? AND approved = false AND rejected = false
  `;

	const exec = await db.mariadb.execute<import('mysql2/promise').ResultSetHeader>(query, [rejectorId, reason, changeId]);

	if ((exec.rows as import('mysql2/promise').ResultSetHeader).affectedRows === 0)
	{
		throw new Error('Configuration change not found or already processed');
	}

	// Log the rejection
	await logConfigAction(changeId, 'rejected', rejectorId, rejectorName, reason);
}

async function logConfigAction(
	changeId: string,
	action: string,
	userId: string,
	username: string,
	reason?: string,
): Promise<void>
{
	const query = `
    INSERT INTO config_audit_log (
      change_id, action, user_id, username, reason, created_at
    ) VALUES (?, ?, ?, ?, ?, NOW())
  `;

	await db.mariadb.execute(query, [changeId, action, userId, username, reason]);

	apiLogger.info({
		changeId,
		action,
		userId,
		username,
		reason,
	}, 'Configuration action logged');
}
