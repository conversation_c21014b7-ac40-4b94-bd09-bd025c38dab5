import { NextRequest, NextResponse } from 'next/server';
import { validateSession } from '@/lib/auth/session';

import db from '@/lib/database/client';
import { apiLogger } from '@/lib/logger';
import type {
	ConfigTemplateType,
	ConfigCategoryType,
	ConfigEnvironmentType,
} from '@/types/config';


export async function GET(request: NextRequest)
{
	try
	{
		const session = await validateSession(request);
		if (!session)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		const { searchParams } = new URL(request.url);
		const category = searchParams.get('category') as ConfigCategoryType;
		const environment = searchParams.get('environment') as ConfigEnvironmentType;

		const templates = await getConfigTemplates(category, environment);

		return NextResponse.json({
			success: true,
			data: templates,
		});
	}
	catch (error)
	{
		apiLogger.error({ error }, 'Failed to get configuration templates');
		return NextResponse.json(
			{ error: 'Failed to get configuration templates' },
			{ status: 500 },
		);
	}
}

export async function POST(request: NextRequest)
{
	try
	{
		const session = await validateSession(request);
		if (!session)
		{
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		const body = await request.json();
		const {
			name, description, environment, category, config, tags, isDefault,
		} = body;

		const templateId = await createConfigTemplate({
			name,
			description,
			environment,
			category,
			config,
			tags: tags || [],
			isDefault: isDefault || false,
			createdBy: session.username,
		});

		return NextResponse.json({
			success: true,
			data: { templateId },
			message: 'Configuration template created successfully',
		});
	}
	catch (error)
	{
		apiLogger.error({ error }, 'Failed to create configuration template');
		return NextResponse.json(
			{ error: 'Failed to create configuration template' },
			{ status: 500 },
		);
	}
}

async function getConfigTemplates(
	category?: ConfigCategoryType,
	environment?: ConfigEnvironmentType,
): Promise<ConfigTemplateType[]>
{
	let query = `
    SELECT
      id, name, description, environment, category, config,
      created_at, created_by, is_default, tags,
      u.name as creator_name
    FROM config_templates ct
    LEFT JOIN users u ON ct.created_by = u.id
    WHERE 1=1
  `;

	const params: any[] = [];

	if (category)
	{
		query += ' AND category = ?';
		params.push(category);
	}

	if (environment)
	{
		query += ' AND environment = ?';
		params.push(environment);
	}

	query += ' ORDER BY is_default DESC, created_at DESC';

	const results = await db.mariadb.query(query, params);

	return results.map((row: any) => ({
		id: row.id,
		name: row.name,
		description: row.description,
		environment: row.environment,
		category: row.category,
		config: JSON.parse(row.config),
		createdAt: new Date(row.created_at),
		createdBy: row.created_by,
		createdByName: row.creator_name,
		isDefault: Boolean(row.is_default),
		tags: JSON.parse(row.tags || '[]'),
	}));
}

async function createConfigTemplate(template: {
	name: string;
	description: string;
	environment: ConfigEnvironmentType;
	category: ConfigCategoryType;
	config: any;
	tags: string[];
	isDefault: boolean;
	createdBy: string;
}): Promise<string>
{
	const templateId = `template_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

	// If this is set as default, unset other defaults for the same category/environment
	if (template.isDefault)
	{
		await db.mariadb.query(
			'UPDATE config_templates SET is_default = false WHERE category = ? AND environment = ?',
			[template.category, template.environment],
		);
	}

	const query = `
    INSERT INTO config_templates (
      id, name, description, environment, category, config,
      created_at, created_by, is_default, tags
    ) VALUES (?, ?, ?, ?, ?, ?, NOW(), ?, ?, ?)
  `;

	await db.mariadb.query(query, [
		templateId,
		template.name,
		template.description,
		template.environment,
		template.category,
		JSON.stringify(template.config),
		template.createdBy,
		template.isDefault,
		JSON.stringify(template.tags),
	]);

	return templateId;
}
