import { NextRequest, NextResponse } from 'next/server';

import { logger } from '@/utils/logger';
import type { DomainCategory } from '@/types/domain';

// Mock categories data - replace with actual database queries
const mockCategories: DomainCategory[] = [
	{
		id: 'tech',
		name: 'Technology',
		description: 'Technology companies, software, hardware, and IT services',
		level: 'primary',
		domainCount: 15420,
		isActive: true,
	},
	{
		id: 'edu',
		name: 'Education',
		description: 'Educational institutions, online learning, and academic resources',
		level: 'primary',
		domainCount: 8930,
		isActive: true,
	},
	{
		id: 'business',
		name: 'Business',
		description: 'Business services, consulting, and corporate websites',
		level: 'primary',
		domainCount: 12750,
		isActive: true,
	},
	{
		id: 'ecommerce',
		name: 'E-commerce',
		description: 'Online stores, marketplaces, and retail websites',
		level: 'primary',
		domainCount: 9680,
		isActive: true,
	},
	{
		id: 'media',
		name: 'Media & Entertainment',
		description: 'News, entertainment, streaming, and media companies',
		level: 'primary',
		domainCount: 7340,
		isActive: true,
	},
	{
		id: 'health',
		name: 'Health & Medical',
		description: 'Healthcare providers, medical information, and wellness',
		level: 'primary',
		domainCount: 5620,
		isActive: true,
	},
	{
		id: 'finance',
		name: 'Finance',
		description: 'Banks, financial services, and investment companies',
		level: 'primary',
		domainCount: 4890,
		isActive: true,
	},
	{
		id: 'travel',
		name: 'Travel & Tourism',
		description: 'Travel agencies, hotels, and tourism websites',
		level: 'primary',
		domainCount: 3450,
		isActive: true,
	},
	{
		id: 'food',
		name: 'Food & Beverage',
		description: 'Restaurants, food delivery, and culinary websites',
		level: 'primary',
		domainCount: 2780,
		isActive: true,
	},
	{
		id: 'sports',
		name: 'Sports & Recreation',
		description: 'Sports teams, fitness, and recreational activities',
		level: 'primary',
		domainCount: 2340,
		isActive: true,
	},
	// Secondary categories
	{
		id: 'tech-saas',
		name: 'Software as a Service',
		description: 'SaaS platforms and cloud-based software solutions',
		parentId: 'tech',
		level: 'secondary',
		domainCount: 3420,
		isActive: true,
	},
	{
		id: 'tech-hardware',
		name: 'Hardware & Electronics',
		description: 'Computer hardware, electronics, and device manufacturers',
		parentId: 'tech',
		level: 'secondary',
		domainCount: 2890,
		isActive: true,
	},
	{
		id: 'edu-university',
		name: 'Universities & Colleges',
		description: 'Higher education institutions and universities',
		parentId: 'edu',
		level: 'secondary',
		domainCount: 4560,
		isActive: true,
	},
	{
		id: 'edu-online',
		name: 'Online Learning',
		description: 'Online courses, MOOCs, and e-learning platforms',
		parentId: 'edu',
		level: 'secondary',
		domainCount: 2340,
		isActive: true,
	},
];

export async function GET(request: NextRequest): Promise<NextResponse>
{
	try
	{
		const { searchParams } = new URL(request.url);
		const level = searchParams.get('level') as 'primary' | 'secondary' | null;
		const parentId = searchParams.get('parentId');
		const activeOnly = searchParams.get('activeOnly') === 'true';

		let filteredCategories = [...mockCategories];

		// Filter by level
		if (level)
		{
			filteredCategories = filteredCategories.filter(cat => cat.level === level);
		}

		// Filter by parent ID
		if (parentId)
		{
			filteredCategories = filteredCategories.filter(cat => cat.parentId === parentId);
		}

		// Filter by active status
		if (activeOnly)
		{
			filteredCategories = filteredCategories.filter(cat => cat.isActive);
		}

		// Sort by name
		filteredCategories.sort((a, b) => a.name.localeCompare(b.name));

		logger.info(`Categories retrieved: ${filteredCategories.length} categories`);

		return NextResponse.json(filteredCategories);
	}
	catch (error)
	{
		logger.error('Error retrieving categories:', error as Error);
		return NextResponse.json(
			{ error: 'Failed to retrieve categories' },
			{ status: 500 },
		);
	}
}
