import { NextRequest, NextResponse } from 'next/server';

import { logger } from '@/utils/logger';
import type {
	DomainSearchResult, DomainFilters, DomainSort, DomainListItem,
} from '@/types/domain';

// Mock data for development - replace with actual database queries
const mockDomains: DomainListItem[] = [
	{
		domain: 'example.com',
		globalRank: 1,
		categoryRank: 1,
		category: 'Technology',
		overallScore: 95.5,
		crawlStatus: 'completed',
		lastCrawled: new Date('2024-01-15T10:30:00Z'),
		isPublic: true,
		seederStatus: 'completed',
		performance: {
			score: 92.3,
			loadTime: 1.2,
		},
		security: {
			score: 98.1,
			sslGrade: 'A+',
		},
		seo: {
			score: 94.7,
		},
		technical: {
			score: 96.2,
		},
		createdAt: new Date('2024-01-01T00:00:00Z'),
		updatedAt: new Date('2024-01-15T10:30:00Z'),
	},
	{
		domain: 'test-site.org',
		globalRank: 2,
		categoryRank: 1,
		category: 'Education',
		overallScore: 87.3,
		crawlStatus: 'in_progress',
		lastCrawled: new Date('2024-01-14T15:45:00Z'),
		isPublic: false,
		seederStatus: 'processing',
		queuePosition: 5,
		performance: {
			score: 85.1,
			loadTime: 2.1,
		},
		security: {
			score: 89.5,
			sslGrade: 'A',
		},
		seo: {
			score: 88.2,
		},
		technical: {
			score: 86.4,
		},
		createdAt: new Date('2024-01-02T00:00:00Z'),
		updatedAt: new Date('2024-01-14T15:45:00Z'),
	},
	{
		domain: 'sample-domain.net',
		globalRank: null,
		categoryRank: null,
		category: 'Business',
		overallScore: 72.8,
		crawlStatus: 'failed',
		lastCrawled: new Date('2024-01-13T08:20:00Z'),
		isPublic: true,
		seederStatus: 'failed',
		performance: {
			score: 68.9,
			loadTime: 3.5,
		},
		security: {
			score: 76.7,
			sslGrade: 'B',
		},
		seo: {
			score: 74.1,
		},
		technical: {
			score: 71.5,
		},
		createdAt: new Date('2024-01-03T00:00:00Z'),
		updatedAt: new Date('2024-01-13T08:20:00Z'),
	},
];

function applyFilters(domains: DomainListItem[], filters: Partial<DomainFilters>): DomainListItem[]
{
	let filtered = [...domains];

	if (filters.search)
	{
		const searchLower = filters.search.toLowerCase();
		filtered = filtered.filter(domain => domain.domain.toLowerCase().includes(searchLower) ||
      domain.category.toLowerCase().includes(searchLower));
	}

	if (filters.category && filters.category.length > 0)
	{
		filtered = filtered.filter(domain => filters.category!.includes(domain.category));
	}

	if (filters.crawlStatus && filters.crawlStatus.length > 0)
	{
		filtered = filtered.filter(domain => filters.crawlStatus!.includes(domain.crawlStatus));
	}

	if (filters.seederStatus && filters.seederStatus.length > 0)
	{
		filtered = filtered.filter(domain => filters.seederStatus!.includes(domain.seederStatus));
	}

	if (filters.isPublic !== null && filters.isPublic !== undefined)
	{
		filtered = filtered.filter(domain => domain.isPublic === filters.isPublic);
	}

	if (filters.rankRange)
	{
		const [min, max] = filters.rankRange;
		filtered = filtered.filter((domain) =>
		{
			if (domain.globalRank === null) return false;
			return domain.globalRank >= min && domain.globalRank <= max;
		});
	}

	if (filters.scoreRange)
	{
		const [min, max] = filters.scoreRange;
		filtered = filtered.filter(domain => domain.overallScore >= min && domain.overallScore <= max);
	}

	if (filters.sslGrades && filters.sslGrades.length > 0)
	{
		filtered = filtered.filter(domain => filters.sslGrades!.includes(domain.security.sslGrade));
	}

	return filtered;
}

function applySorting(domains: DomainListItem[], sort: DomainSort): DomainListItem[]
{
	return [...domains].sort((a, b) =>
	{
		let aValue: unknown;
		let bValue: unknown;

		switch (sort.field)
		{
			case 'domain':
				aValue = a.domain;
				bValue = b.domain;
				break;
			case 'globalRank':
				aValue = a.globalRank ?? Number.MAX_SAFE_INTEGER;
				bValue = b.globalRank ?? Number.MAX_SAFE_INTEGER;
				break;
			case 'categoryRank':
				aValue = a.categoryRank ?? Number.MAX_SAFE_INTEGER;
				bValue = b.categoryRank ?? Number.MAX_SAFE_INTEGER;
				break;
			case 'overallScore':
				aValue = a.overallScore;
				bValue = b.overallScore;
				break;
			case 'lastCrawled':
				aValue = a.lastCrawled?.getTime() ?? 0;
				bValue = b.lastCrawled?.getTime() ?? 0;
				break;
			case 'createdAt':
				aValue = a.createdAt.getTime();
				bValue = b.createdAt.getTime();
				break;
			case 'updatedAt':
				aValue = a.updatedAt.getTime();
				bValue = b.updatedAt.getTime();
				break;
			case 'performance.score':
				aValue = a.performance.score;
				bValue = b.performance.score;
				break;
			case 'security.score':
				aValue = a.security.score;
				bValue = b.security.score;
				break;
			case 'seo.score':
				aValue = a.seo.score;
				bValue = b.seo.score;
				break;
			case 'technical.score':
				aValue = a.technical.score;
				bValue = b.technical.score;
				break;
			default:
				aValue = a.domain;
				bValue = b.domain;
		}

		if (typeof aValue === 'string' && typeof bValue === 'string')
		{
			const comparison = aValue.localeCompare(bValue);
			return sort.direction === 'asc' ? comparison : -comparison;
		}

		if (typeof aValue === 'number' && typeof bValue === 'number')
		{
			const comparison = aValue - bValue;
			return sort.direction === 'asc' ? comparison : -comparison;
		}

		return 0;
	});
}

export async function GET(request: NextRequest): Promise<NextResponse>
{
	try
	{
		const { searchParams } = new URL(request.url);

		// Parse query parameters
		const page = parseInt(searchParams.get('page') ?? '1', 10);
		const pageSize = parseInt(searchParams.get('pageSize') ?? '25', 10);
		const search = searchParams.get('search') ?? '';
		const category = searchParams.getAll('category');
		const crawlStatus = searchParams.getAll('crawlStatus');
		const seederStatus = searchParams.getAll('seederStatus');
		const isPublic = searchParams.get('isPublic') === 'true' ? true
			: searchParams.get('isPublic') === 'false' ? false : null;

		// Parse sort parameters
		const sortField = (searchParams.get('sortField') ?? 'domain') as DomainSort['field'];
		const sortDirection = (searchParams.get('sortDirection') ?? 'asc') as DomainSort['direction'];

		// Parse range filters
		const rankMin = searchParams.get('rankMin') ? parseInt(searchParams.get('rankMin')!, 10) : 1;
		const rankMax = searchParams.get('rankMax') ? parseInt(searchParams.get('rankMax')!, 10) : 1000000;
		const scoreMin = searchParams.get('scoreMin') ? parseFloat(searchParams.get('scoreMin')!) : 0;
		const scoreMax = searchParams.get('scoreMax') ? parseFloat(searchParams.get('scoreMax')!) : 100;

		const sslGrades = searchParams.getAll('sslGrade');

		const filters: Partial<DomainFilters> = {
			search,
			category: category.length > 0 ? category : undefined,
			crawlStatus: crawlStatus.length > 0 ? crawlStatus : undefined,
			seederStatus: seederStatus.length > 0 ? seederStatus : undefined,
			isPublic,
			rankRange: [rankMin, rankMax],
			scoreRange: [scoreMin, scoreMax],
			sslGrades: sslGrades.length > 0 ? sslGrades : undefined,
		};

		const sort: DomainSort = {
			field: sortField,
			direction: sortDirection,
		};

		// Apply filters and sorting
		let filteredDomains = applyFilters(mockDomains, filters);
		filteredDomains = applySorting(filteredDomains, sort);

		// Apply pagination
		const total = filteredDomains.length;
		const startIndex = (page - 1) * pageSize;
		const endIndex = startIndex + pageSize;
		const paginatedDomains = filteredDomains.slice(startIndex, endIndex);

		const result: DomainSearchResult = {
			domains: paginatedDomains,
			pagination: {
				page,
				pageSize,
				total,
			},
			filters: filters as DomainFilters,
			sort,
		};

		logger.info(`Domain search completed: ${paginatedDomains.length} domains returned`);

		return NextResponse.json(result);
	}
	catch (error)
	{
		logger.error('Error in domain search API:', error as Error);
		return NextResponse.json(
			{ error: 'Failed to search domains' },
			{ status: 500 },
		);
	}
}
