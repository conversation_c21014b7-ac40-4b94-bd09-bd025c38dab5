import { NextRequest, NextResponse } from 'next/server';
import { apiLogger } from '@/lib/logger';

import { ErrorAnalyticsService } from '../../../../lib/errors/ErrorAnalyticsService';

const analyticsService = ErrorAnalyticsService.getInstance();

export async function GET(request: NextRequest)
{
	try
	{
		const { searchParams } = new URL(request.url);

		// Parse time range (required)
		const startDateParam = searchParams.get('startDate');
		const endDateParam = searchParams.get('endDate');

		if (!startDateParam || !endDateParam)
		{
			return NextResponse.json(
				{
					success: false,
					error: 'Start date and end date are required',
				},
				{ status: 400 },
			);
		}

		const startDate = new Date(startDateParam);
		const endDate = new Date(endDateParam);

		if (isNaN(startDate.getTime()) || isNaN(endDate.getTime()))
		{
			return NextResponse.json(
				{
					success: false,
					error: 'Invalid date format',
				},
				{ status: 400 },
			);
		}

		// Parse granularity
		const granularity = (searchParams.get('granularity') as 'hour' | 'day' | 'week') || 'hour';

		if (!['hour', 'day', 'week'].includes(granularity))
		{
			return NextResponse.json(
				{
					success: false,
					error: 'Invalid granularity. Must be hour, day, or week',
				},
				{ status: 400 },
			);
		}

		// Get error trends
		const trends = analyticsService.getErrorTrends(
			{ start: startDate, end: endDate },
			granularity,
		);

		return NextResponse.json({
			success: true,
			data: trends,
			timeRange: {
				start: startDate.toISOString(),
				end: endDate.toISOString(),
				granularity,
			},
			total: trends.length,
		});
	}
	catch (error)
	{
		logger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error fetching trends:');
		return NextResponse.json(
			{
				success: false,
				error: 'Failed to fetch error trends',
				details: error instanceof Error ? error.message : 'Unknown error',
			},
			{ status: 500 },
		);
	}
}
