import { NextRequest, NextResponse } from 'next/server';
import { apiLogger } from '@/lib/logger';

import { ErrorCodesEnum } from '../../../../lib/errors/ErrorCodes';
import { ErrorDebugService } from '../../../../lib/errors/ErrorDebugService';

const debugService = ErrorDebugService.getInstance();

async function GET(request: NextRequest)
{
	try
	{
		const { searchParams } = new URL(request.url);
		const errorCode = searchParams.get('errorCode') as ErrorCodesEnum;
		const query = searchParams.get('query');

		if (errorCode)
		{
			// Get documentation for specific error code
			const documentation = debugService.getErrorDocumentation(errorCode);

			if (!documentation)
			{
				return NextResponse.json(
					{
						success: false,
						error: 'Documentation not found for error code',
					},
					{ status: 404 },
				);
			}

			return NextResponse.json({
				success: true,
				data: documentation,
			});
		} if (query)
		{
			// Search documentation
			const results = debugService.searchDocumentation(query);

			return NextResponse.json({
				success: true,
				data: results,
				query,
				total: results.length,
			});
		}
		return NextResponse.json(
			{
				success: false,
				error: 'Either errorCode or query parameter is required',
			},
			{ status: 400 },
		);
	}
	catch (error)
	{
		logger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error fetching documentation:');
		return NextResponse.json(
			{
				success: false,
				error: 'Failed to fetch error documentation',
				details: error instanceof Error ? error.message : 'Unknown error',
			},
			{ status: 500 },
		);
	}
}

// eslint-disable-next-line import/prefer-default-export
export { GET };
