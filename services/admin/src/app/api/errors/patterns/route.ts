import { NextRequest, NextResponse } from 'next/server';
import { apiLogger } from '@/lib/logger';

import { ErrorAnalyticsService } from '../../../../lib/errors/ErrorAnalyticsService';

const analyticsService = ErrorAnalyticsService.getInstance();

export async function GET(request: NextRequest)
{
	try
	{
		// Get error patterns and correlations
		const patterns = analyticsService.getErrorPatterns();

		return NextResponse.json({
			success: true,
			data: patterns,
		});
	}
	catch (error)
	{
		logger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error fetching patterns:');
		return NextResponse.json(
			{
				success: false,
				error: 'Failed to fetch error patterns',
				details: error instanceof Error ? error.message : 'Unknown error',
			},
			{ status: 500 },
		);
	}
}
