import { NextRequest, NextResponse } from 'next/server';
import { apiLogger } from '@/lib/logger';

import ErrorDebugService from '../../../../../lib/errors/ErrorDebugService';

const debugService = ErrorDebugService.getInstance();

async function GET(
	request: NextRequest,
	{ params }: { params: { errorId: string } },
)
{
	try
	{
		const { errorId } = params;
		const { searchParams } = new URL(request.url);
		const format = searchParams.get('format') as 'json' | 'text' || 'json';

		if (!['json', 'text'].includes(format))
		{
			return NextResponse.json(
				{
					success: false,
					error: 'Invalid format. Must be json or text',
				},
				{ status: 400 },
			);
		}

		// Get debug information
		const debugInfo = debugService.getDebugInfo(errorId);

		if (!debugInfo)
		{
			return NextResponse.json(
				{
					success: false,
					error: 'Debug information not found for error ID',
				},
				{ status: 404 },
			);
		}

		if (format === 'text')
		{
			// Export as text format
			const textExport = debugService.exportDebugInfo(errorId, 'text');

			return new NextResponse(textExport, {
				headers: {
					'Content-Type': 'text/plain',
					'Content-Disposition': `attachment; filename="debug-${errorId}.txt"`,
				},
			});
		}

		// Return JSON format
		return NextResponse.json({
			success: true,
			data: debugInfo,
		});
	}
	catch (error)
	{
		apiLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error fetching debug info:');
		return NextResponse.json(
			{
				success: false,
				error: 'Failed to fetch debug information',
				details: error instanceof Error ? error.message : 'Unknown error',
			},
			{ status: 500 },
		);
	}
}

// eslint-disable-next-line import/prefer-default-export
export { GET };
