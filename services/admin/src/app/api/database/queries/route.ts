import { NextResponse } from 'next/server';
import { db<PERSON>ogger } from '@/lib/logger';

import db from '@/lib/database/client';
import type { NextRequest } from 'next/server';
import type { SlowQuery } from '@/types/database';
import { IdGenerator } from 'shared/src/utils/IdGenerator';


// In-memory storage for slow queries (in production, use database)
const slowQueries = new Map<string, SlowQuery>();

// Initialize with some sample data
initializeSampleSlowQueries();

export async function GET(request: NextRequest)
{
	try
	{
		const searchParams = request.nextUrl.searchParams;
		const database = searchParams.get('database');
		const limit = parseInt(searchParams.get('limit') || '50');
		const offset = parseInt(searchParams.get('offset') || '0');
		const minExecutionTime = parseFloat(searchParams.get('minExecutionTime') || '0');

		let queries = Array.from(slowQueries.values());

		if (database)
		{
			queries = queries.filter(query => query.database === database);
		}

		if (minExecutionTime > 0)
		{
			queries = queries.filter(query => query.executionTime >= minExecutionTime);
		}

		// Sort by execution time (slowest first)
		queries.sort((a, b) => b.executionTime - a.executionTime);

		// Apply pagination
		const paginatedQueries = queries.slice(offset, offset + limit);

		return NextResponse.json({
			queries: paginatedQueries,
			total: queries.length,
			limit,
			offset,
		});
	}
	catch (error)
	{
		dbLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Slow queries fetch error:');
		return NextResponse.json(
			{ error: 'Failed to fetch slow queries' },
			{ status: 500 },
		);
	}
}

export async function POST(request: NextRequest)
{
	try
	{
		const body = await request.json();
		const { database, action = 'analyze' } = body;

		if (!database)
		{
			return NextResponse.json(
				{ error: 'Database is required' },
				{ status: 400 },
			);
		}

		switch (action)
		{
			case 'analyze':
				const analysis = await analyzeQueryPerformance(database);
				return NextResponse.json(analysis);

			case 'optimize':
				const optimizations = await generateOptimizationSuggestions(database);
				return NextResponse.json(optimizations);

			case 'explain':
				const queryId = body.queryId;
				if (!queryId)
				{
					return NextResponse.json(
						{ error: 'Query ID is required for explain action' },
						{ status: 400 },
					);
				}
				const explanation = await explainQuery(queryId);
				return NextResponse.json(explanation);

			default:
				return NextResponse.json(
					{ error: 'Invalid action' },
					{ status: 400 },
				);
		}
	}
	catch (error)
	{
		dbLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Query analysis error:');
		return NextResponse.json(
			{ error: 'Failed to analyze queries' },
			{ status: 500 },
		);
	}
}

async function analyzeQueryPerformance(database: string)
{
	const queries = Array.from(slowQueries.values()).filter(q => q.database === database);

	if (queries.length === 0)
	{
		return {
			summary: {
				totalQueries: 0,
				averageExecutionTime: 0,
				slowestQuery: null,
				mostFrequentTable: null,
			},
			recommendations: [],
		};
	}

	const totalExecutionTime = queries.reduce((sum, q) => sum + q.executionTime, 0);
	const averageExecutionTime = totalExecutionTime / queries.length;
	const slowestQuery = queries.reduce((slowest, current) => (current.executionTime > slowest.executionTime ? current : slowest));

	// Find most frequent table
	const tableCounts = queries.reduce((counts, query) =>
	{
		counts[query.table] = (counts[query.table] || 0) + 1;
		return counts;
	}, {} as Record<string, number>);

	const mostFrequentTable = Object.entries(tableCounts).reduce((most, [table, count]) => (count > most.count ? { table, count } : most),
		{ table: '', count: 0 });

	const recommendations = generatePerformanceRecommendations(queries);

	return {
		summary: {
			totalQueries: queries.length,
			averageExecutionTime,
			slowestQuery: {
				id: slowestQuery.id,
				executionTime: slowestQuery.executionTime,
				query: `${slowestQuery.query.substring(0, 100) }...`,
			},
			mostFrequentTable: mostFrequentTable.table,
		},
		recommendations,
	};
}

async function generateOptimizationSuggestions(database: string)
{
	const queries = Array.from(slowQueries.values()).filter(q => q.database === database);

	const suggestions = [];

	// Analyze patterns in slow queries
	const tableQueries = queries.reduce((groups, query) =>
	{
		if (!groups[query.table])
		{
			groups[query.table] = [];
		}
		groups[query.table].push(query);
		return groups;
	}, {} as Record<string, SlowQuery[]>);

	for (const [table, tableQueryList] of Object.entries(tableQueries))
	{
		if (tableQueryList.length > 5)
		{
			suggestions.push({
				type: 'index',
				priority: 'high',
				table,
				description: `Consider adding indexes to ${table} - ${tableQueryList.length} slow queries detected`,
				estimatedImprovement: '50-80%',
				effort: 'medium',
			});
		}

		const avgRowsExamined = tableQueryList.reduce((sum, q) => sum + q.rowsExamined, 0) / tableQueryList.length;
		const avgRowsReturned = tableQueryList.reduce((sum, q) => sum + q.rowsReturned, 0) / tableQueryList.length;

		if (avgRowsExamined > avgRowsReturned * 10)
		{
			suggestions.push({
				type: 'query_optimization',
				priority: 'medium',
				table,
				description: `Queries on ${table} examine too many rows (${Math.round(avgRowsExamined)} examined vs ${Math.round(avgRowsReturned)} returned)`,
				estimatedImprovement: '30-60%',
				effort: 'high',
			});
		}
	}

	// Check for missing indexes
	const commonPatterns = findCommonQueryPatterns(queries);
	for (const pattern of commonPatterns)
	{
		suggestions.push({
			type: 'index',
			priority: 'medium',
			table: pattern.table,
			description: `Create composite index on ${pattern.columns.join(', ')} for ${pattern.table}`,
			estimatedImprovement: '40-70%',
			effort: 'low',
		});
	}

	return {
		suggestions,
		totalPotentialImprovement: suggestions.length > 0 ? '25-65%' : '0%',
	};
}

async function explainQuery(queryId: string)
{
	const query = slowQueries.get(queryId);
	if (!query)
	{
		throw new Error('Query not found');
	}

	try
	{
		let executionPlan;

		switch (query.database)
		{
			case 'mariadb':
				const explainResult = await db.mariadb.execute(`EXPLAIN ${query.query}`);
				executionPlan = explainResult.rows;
				break;

			case 'scylla':
				// ScyllaDB doesn't have EXPLAIN, provide simulated plan
				executionPlan = generateSimulatedExecutionPlan(query);
				break;

			default:
				executionPlan = generateSimulatedExecutionPlan(query);
		}

		return {
			query: query.query,
			executionPlan,
			analysis: {
				bottlenecks: identifyBottlenecks(query, executionPlan),
				suggestions: query.optimizationSuggestions,
				estimatedCost: calculateQueryCost(query),
			},
		};
	}
	catch (error)
	{
		throw new Error(`Failed to explain query: ${error instanceof Error ? error.message : 'Unknown error'}`);
	}
}

function generatePerformanceRecommendations(queries: SlowQuery[]): string[]
{
	const recommendations = [];

	const avgExecutionTime = queries.reduce((sum, q) => sum + q.executionTime, 0) / queries.length;

	if (avgExecutionTime > 1000)
	{
		recommendations.push('Consider adding database indexes for frequently queried columns');
	}

	if (avgExecutionTime > 5000)
	{
		recommendations.push('Review query structure and consider query optimization');
	}

	const tablesWithManySlowQueries = queries.reduce((counts, query) =>
	{
		counts[query.table] = (counts[query.table] || 0) + 1;
		return counts;
	}, {} as Record<string, number>);

	for (const [table, count] of Object.entries(tablesWithManySlowQueries))
	{
		if (count > 10)
		{
			recommendations.push(`Table ${table} has ${count} slow queries - consider schema optimization`);
		}
	}

	return recommendations;
}

function findCommonQueryPatterns(queries: SlowQuery[]): Array<{ table: string; columns: string[] }>
{
	// Simplified pattern detection
	const patterns = [];

	const tableGroups = queries.reduce((groups, query) =>
	{
		if (!groups[query.table])
		{
			groups[query.table] = [];
		}
		groups[query.table].push(query);
		return groups;
	}, {} as Record<string, SlowQuery[]>);

	for (const [table, tableQueries] of Object.entries(tableGroups))
	{
		if (tableQueries.length > 3)
		{
			patterns.push({
				table,
				columns: ['id', 'created_at'], // Simplified - in production, analyze actual query patterns
			});
		}
	}

	return patterns;
}

function generateSimulatedExecutionPlan(query: SlowQuery)
{
	return {
		steps: [
			{
				operation: 'Table Scan',
				table: query.table,
				rows: query.rowsExamined,
				cost: query.executionTime * 0.6,
			},
			{
				operation: 'Filter',
				condition: 'WHERE clause',
				rows: query.rowsReturned,
				cost: query.executionTime * 0.3,
			},
			{
				operation: 'Sort',
				method: 'filesort',
				rows: query.rowsReturned,
				cost: query.executionTime * 0.1,
			},
		],
		totalCost: query.executionTime,
		indexesUsed: query.indexesUsed,
	};
}

function identifyBottlenecks(query: SlowQuery, executionPlan: any): string[]
{
	const bottlenecks = [];

	if (query.rowsExamined > query.rowsReturned * 5)
	{
		bottlenecks.push('High row examination ratio - consider adding indexes');
	}

	if (query.executionTime > 5000)
	{
		bottlenecks.push('Very slow execution time - review query structure');
	}

	if (query.indexesUsed.length === 0)
	{
		bottlenecks.push('No indexes used - consider adding appropriate indexes');
	}

	return bottlenecks;
}

function calculateQueryCost(query: SlowQuery): number
{
	// Simplified cost calculation
	return Math.round(query.executionTime * 0.001 + query.rowsExamined * 0.0001);
}

function initializeSampleSlowQueries(): void
{
	const sampleQueries: Omit<SlowQuery, 'id'>[] = [
		{
			database: 'mariadb',
			query: 'SELECT * FROM domain_analysis WHERE category = "technology" ORDER BY global_rank',
			executionTime: 2500,
			timestamp: new Date(Date.now() - 3600000),
			table: 'domain_analysis',
			rowsExamined: 50000,
			rowsReturned: 1000,
			indexesUsed: [],
			optimizationSuggestions: ['Add index on (category, global_rank)', 'Consider limiting result set'],
			executionPlan: null,
		},
		{
			database: 'scylla',
			query: 'SELECT domain, crawl_status FROM domain_crawl_jobs WHERE status = "pending"',
			executionTime: 1800,
			timestamp: new Date(Date.now() - 7200000),
			table: 'domain_crawl_jobs',
			rowsExamined: 25000,
			rowsReturned: 500,
			indexesUsed: ['status_idx'],
			optimizationSuggestions: ['Query is already optimized with index'],
			executionPlan: null,
		},
		{
			database: 'mariadb',
			query: 'SELECT COUNT(*) FROM domain_rankings WHERE category_rank < 1000 GROUP BY category',
			executionTime: 3200,
			timestamp: new Date(Date.now() - 10800000),
			table: 'domain_rankings',
			rowsExamined: 75000,
			rowsReturned: 50,
			indexesUsed: ['category_idx'],
			optimizationSuggestions: ['Add composite index on (category, category_rank)'],
			executionPlan: null,
		},
	];

	sampleQueries.forEach((queryData) =>
	{
		const id = IdGenerator.queryId();
		slowQueries.set(id, { id, ...queryData });
	});
}
