import { NextResponse } from 'next/server';

import { BrowserHttpClient } from '@/lib/utils/BrowserHttpClient';
import { dbLogger } from '@/lib/logger';

import type { NextRequest } from 'next/server';

type DatabaseHealthStatusType =
{
	scylla: boolean;
	maria: boolean;
	redis: boolean;
	manticore: boolean;
};

type DatabaseStatusType =
{
	name: 'scylla' | 'mariadb' | 'redis' | 'manticore';
	connected: boolean;
	responseTime: number;
	connectionPool: {
		active: number;
		idle: number;
		total: number;
	};
	metrics: {
		queries: number;
		errors: number;
		slowQueries: number;
	};
	lastCheck: Date;
	version?: string;
	error?: string;
}

async function checkDatabaseHealth(dbName: keyof DatabaseHealthStatusType): Promise<DatabaseStatusType>
{
	const startTime = Date.now();

	try
	{
		// For now, return mock data since we can't directly connect to databases from Next.js
		// In a real implementation, this would make HTTP requests to other services
		// that have direct database access

		const responseTime = Date.now() - startTime;

		// Mock successful connection for demonstration
		// In production, this should call actual health check endpoints
		const connected = false; // Set to false since databases aren't accessible from admin service

		return {
			name: dbName,
			connected,
			responseTime,
			connectionPool: { active: 0, idle: 0, total: 0 },
			metrics: { queries: 0, errors: 0, slowQueries: 0 },
			lastCheck: new Date(),
			version: 'unknown',
			error: `${dbName} not accessible from admin service - requires service-to-service communication`,
		};
	}
	catch (error)
	{
		const responseTime = Date.now() - startTime;
		dbLogger.error({ error: error instanceof Error ? error.message : String(error) }, `Database health check failed for ${dbName}`);

		return {
			name: dbName,
			connected: false,
			responseTime,
			connectionPool: { active: 0, idle: 0, total: 0 },
			metrics: { queries: 0, errors: 1, slowQueries: 0 },
			lastCheck: new Date(),
			error: error instanceof Error ? error.message : 'Unknown error',
		};
	}
}

async function GET(request: NextRequest)
{
	try
	{
		const { searchParams } = new URL(request.url);
		const dbName = searchParams.get('database') as 'scylla' | 'mariadb' | 'redis' | 'manticore' | null;

		if (dbName)
		{
			if (!['scylla', 'mariadb', 'redis', 'manticore'].includes(dbName))
			{
				return NextResponse.json(
					{ error: 'Invalid database name' },
					{ status: 400 },
				);
			}

			const healthStatus = await checkDatabaseHealth(dbName);
			return NextResponse.json(healthStatus);
		}

		// Check all databases (merge shared base health with detailed checks)
		const databases: Array<'scylla' | 'mariadb' | 'redis' | 'manticore'> = [
			'scylla',
			'mariadb',
			'redis',
			'manticore',
		];

		// Get detailed health status for each database
		const detailed = await Promise.all(databases.map(d => checkDatabaseHealth(d)));

		return NextResponse.json({
			databases: detailed,
			timestamp: new Date(),
			summary: {
				total: detailed.length,
				connected: detailed.filter(db => db.connected).length,
				disconnected: detailed.filter(db => !db.connected).length,
				averageResponseTime: detailed.reduce((sum, db) => sum + db.responseTime, 0) / detailed.length,
			},
		});
	}
	catch (error)
	{
		dbLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Failed to check database health');
		return NextResponse.json(
			{ error: 'Internal server error' },
			{ status: 500 },
		);
	}
}

export { GET };
