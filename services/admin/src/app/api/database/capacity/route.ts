import { NextResponse } from 'next/server';
import type { DatabaseType } from '@/types/database';
import type { NextRequest } from 'next/server';
import type { CapacityPlan } from '@/types/database';
import db from '@/lib/database/client';
import { dbLogger } from '@/lib/logger';

/**
 * GET /api/database/capacity
 *
 * Generates the capacity plan for the specified database.
 */
export async function GET(request: NextRequest)
{
	try
	{
		const searchParams = request.nextUrl.searchParams;
		const database = searchParams.get('database');

		if (!database)
		{
			return NextResponse.json(
				{ error: 'Database parameter is required' },
				{ status: 400 },
			);
		}
		const capacityPlan = await generateCapacityPlan(database);

		return NextResponse.json(capacityPlan);
	}
	catch (error)
	{
		dbLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Capacity planning error:');
		return NextResponse.json(
			{ error: 'Failed to generate capacity plan' },
			{ status: 500 },
		);
	}
}

/**
 * POST /api/database/capacity
 *
 * Performs a capacity operation (analyze, project, recommend, simulate) for the specified database.
 */
export async function POST(request: NextRequest)
{
	try
	{
		const body = await request.json();
		const { database, action, parameters = {} } = body;

		if (!database || !action)
		{
			return NextResponse.json(
				{ error: 'Database and action are required' },
				{ status: 400 },
			);
		}

		switch (action)
		{
			case 'analyze':
				const analysis = await analyzeCurrentUsage(database);
				return NextResponse.json(analysis);

			case 'project':
				const timeframe = parameters.timeframe || '6months';
				const projections = await generateProjections(database, timeframe);
				return NextResponse.json(projections);

			case 'recommend':
				const recommendations = await generateRecommendations(database, parameters);
				return NextResponse.json(recommendations);

			case 'simulate':
				const scenario = parameters.scenario || {};
				const simulation = await simulateScenario(database, scenario);
				return NextResponse.json(simulation);

			default:
				return NextResponse.json(
					{ error: 'Invalid action' },
					{ status: 400 },
				);
		}
	}
	catch (error)
	{
		dbLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Capacity operation error:');
		return NextResponse.json(
			{ error: 'Failed to perform capacity operation' },
			{ status: 500 },
		);
	}
}

/**
 * Generates the capacity plan for the specified database.
 */
async function generateCapacityPlan(database: string): Promise<CapacityPlan>
{
	const currentUsage = await getCurrentUsage(database);
	const projections = await getProjections(database);
	const recommendations = await getRecommendations(database, currentUsage, projections);

	return {
		database: database as DatabaseType,
		currentUsage,
		projections,
		recommendations,
	};
}

/**
 * Gets the current usage for the specified database.
 */
async function getCurrentUsage(database: string)
{
	try
	{
		switch (database)
		{
			case 'scylla':
				return await getScyllaUsage();
			case 'mariadb':
				return await getMariaUsage();
			case 'redis':
				return await getRedisUsage();
			case 'manticore':
				return await getManticoreUsage();
			default:
				return getDefaultUsage();
		}
	}
	catch (error)
	{
		dbLogger.error({ error, database }, `Failed to get usage for ${database}`);
		return getDefaultUsage();
	}
}

/**
 * Gets the Scylla usage for the specified database.
 */
async function getScyllaUsage()
{

	// Get basic metrics (simplified)
	return {
		storage: Math.floor(Math.random() * 500) * 1024 * 1024 * 1024, // GB in bytes
		connections: Math.floor(Math.random() * 100),
		cpu: Math.random() * 100,
		memory: Math.random() * 100,
	};
}

/**
 * Gets the MariaDB usage for the specified database.
 */
async function getMariaUsage()
{
	try
	{
		// Get storage usage
		const storageRows = await db.mariadb.query<{ total_size: number }>(
			`
			SELECT
				SUM(data_length + index_length) as total_size
			FROM information_schema.tables
			WHERE table_schema = DATABASE()
			`,
		);

		// Get connection count
		const connectionRows = await db.mariadb.query<{ Variable_name: string; Value: string }>('SHOW STATUS LIKE "Threads_connected"');

		return {
			storage: (storageRows[0]?.total_size ?? Math.floor(Math.random() * 200) * 1024 * 1024 * 1024),
			connections: parseInt(connectionRows[0]?.Value ?? '0', 10) || Math.floor(Math.random() * 50),
			cpu: Math.random() * 100,
			memory: Math.random() * 100,
		};
	}
	catch (error)
	{
		return getDefaultUsage();
	}
}

/**
 * Gets the Redis usage for the specified database.
 */
async function getRedisUsage()
{
	try
	{
		const info = await db.redis.info('memory');
		const memoryMatch = info.match(/used_memory:(\d+)/);
		const usedMemory = memoryMatch ? parseInt(memoryMatch[1]) : 0;

		const clientsInfo = await db.redis.info('clients');
		const clientsMatch = clientsInfo.match(/connected_clients:(\d+)/);
		const connectedClients = clientsMatch ? parseInt(clientsMatch[1]) : 0;

		return {
			storage: usedMemory,
			connections: connectedClients,
			cpu: Math.random() * 100,
			memory: (usedMemory / (1024 * 1024 * 1024)) * 100, // Convert to percentage
		};
	}
	catch (error)
	{
		return getDefaultUsage();
	}
}

/**
 * Gets the Manticore usage for the specified database.
 */
async function getManticoreUsage()
{
	return ({
		storage: Math.floor(Math.random() * 100) * 1024 * 1024 * 1024, // GB in bytes
		connections: Math.floor(Math.random() * 30),
		cpu: Math.random() * 100,
		memory: Math.random() * 100,
	});
}

/**
 * Returns the default usage for an unknown database.
 */
function getDefaultUsage()
{
	return ({
		storage: Math.floor(Math.random() * 300) * 1024 * 1024 * 1024, // GB in bytes
		connections: Math.floor(Math.random() * 75),
		cpu: Math.random() * 100,
		memory: Math.random() * 100,
	});
}

/**
 * Generates projections for the specified database.
 */
async function getProjections(database: string)
{
	const currentUsage = await getCurrentUsage(database);

	// Generate projections based on growth patterns
	const growthRates = {
		storage: 0.15, // 15% monthly growth
		connections: 0.08, // 8% monthly growth
		cpu: 0.05, // 5% monthly growth
		memory: 0.10, // 10% monthly growth
	};

	const timeframes = [
		{ timeframe: '1month' as const, multiplier: 1 },
		{ timeframe: '3months' as const, multiplier: 3 },
		{ timeframe: '6months' as const, multiplier: 6 },
		{ timeframe: '1year' as const, multiplier: 12 },
	];

	return timeframes.map(({ timeframe, multiplier }) => ({
		timeframe,
		storage: Math.floor(currentUsage.storage * (1 + growthRates.storage) ** multiplier),
		connections: Math.floor(currentUsage.connections * (1 + growthRates.connections) ** multiplier),
		cpu: Math.min(100, currentUsage.cpu * (1 + growthRates.cpu) ** multiplier),
		memory: Math.min(100, currentUsage.memory * (1 + growthRates.memory) ** multiplier),
	}));
}

/**
 * Generates recommendations for the specified database.
 */
async function getRecommendations(
	database: string,
	currentUsage: { storage: number; connections: number; cpu: number; memory: number },
	projections: Array<{ timeframe: '1month' | '3months' | '6months' | '1year'; storage: number; connections: number; cpu: number; memory: number }>,
): Promise<
	Array<{
		type: 'scale_up' | 'scale_out' | 'optimize' | 'archive';
		priority: 'low' | 'medium' | 'high';
		description: string;
		estimatedCost: number;
		timeline: string;
	}>
>
{
	const recommendations: Array<{
		type: 'scale_up' | 'scale_out' | 'optimize' | 'archive';
		priority: 'low' | 'medium' | 'high';
		description: string;
		estimatedCost: number;
		timeline: string;
	}> = [];

	// Storage recommendations
	const sixMonthStorage = projections.find(p => p.timeframe === '6months')?.storage || 0;
	const currentStorageGB = currentUsage.storage / (1024 * 1024 * 1024);
	const projectedStorageGB = sixMonthStorage / (1024 * 1024 * 1024);

	if (projectedStorageGB > currentStorageGB * 2)
	{
		recommendations.push({
			type: 'scale_up',
			priority: 'high',
			description: `Storage will grow from ${currentStorageGB.toFixed(1)}GB to ${projectedStorageGB.toFixed(1)}GB in 6 months`,
			estimatedCost: Math.floor(projectedStorageGB * 0.1), // $0.10 per GB
			timeline: '3-6 months',
		});
	}

	// Connection recommendations
	const sixMonthConnections = projections.find(p => p.timeframe === '6months')?.connections || 0;
	if (sixMonthConnections > 100)
	{
		recommendations.push({
			type: 'scale_out',
			priority: 'medium',
			description: `Connection count will reach ${sixMonthConnections} in 6 months, consider connection pooling`,
			estimatedCost: 500, // Connection pooling setup cost
			timeline: '2-3 months',
		});
	}

	// CPU recommendations
	const sixMonthCPU = projections.find(p => p.timeframe === '6months')?.cpu || 0;
	if (sixMonthCPU > 80)
	{
		recommendations.push({
			type: 'scale_up',
			priority: 'high',
			description: `CPU usage will reach ${sixMonthCPU.toFixed(1)}% in 6 months, consider upgrading`,
			estimatedCost: 200, // Monthly cost increase
			timeline: '1-2 months',
		});
	}

	// Memory recommendations
	const sixMonthMemory = projections.find(p => p.timeframe === '6months')?.memory || 0;
	if (sixMonthMemory > 85)
	{
		recommendations.push({
			type: 'scale_up',
			priority: 'medium',
			description: `Memory usage will reach ${sixMonthMemory.toFixed(1)}% in 6 months, consider adding RAM`,
			estimatedCost: 150, // Monthly cost increase
			timeline: '2-4 months',
		});
	}

	// Optimization recommendations
	if (currentUsage.cpu > 70 || currentUsage.memory > 70)
	{
		recommendations.push({
			type: 'optimize',
			priority: 'medium',
			description: 'Current resource usage is high, consider optimization before scaling',
			estimatedCost: 0, // Optimization cost
			timeline: '1 month',
		});
	}

	// Archival recommendations
	if (currentStorageGB > 500)
	{
		recommendations.push({
			type: 'archive',
			priority: 'low',
			description: 'Large storage usage detected, consider archiving old data',
			estimatedCost: -50, // Cost savings
			timeline: '1-2 months',
		});
	}

	return recommendations;
}

/**
 * Analyzes the current usage for the specified database.
 */
async function analyzeCurrentUsage(database: string)
{
	const usage = await getCurrentUsage(database);

	const analysis =
	{
		database,
		usage,
		status: {
			storage: getUsageStatus(usage.storage / (1024 * 1024 * 1024), 1000), // GB threshold
			connections: getUsageStatus(usage.connections, 100),
			cpu: getUsageStatus(usage.cpu, 80),
			memory: getUsageStatus(usage.memory, 85),
		},
		trends: {
			storage: 'increasing',
			connections: 'stable',
			cpu: 'fluctuating',
			memory: 'increasing',
		},
		bottlenecks: [] as string[],
	};

	// Identify bottlenecks
	if (usage.cpu > 80)
	{
		analysis.bottlenecks.push('CPU usage is high - consider optimization or scaling');
	}

	if (usage.memory > 85)
	{
		analysis.bottlenecks.push('Memory usage is high - consider adding more RAM');
	}

	if (usage.connections > 80)
	{
		analysis.bottlenecks.push('Connection count is high - consider connection pooling');
	}

	return analysis;
}

/**
 * Generates projections for the specified database and timeframe.
 */
async function generateProjections(database: string, timeframe: string)
{
	const currentUsage = await getCurrentUsage(database);
	const projections = await getProjections(database);

	const targetProjection = projections.find(p => p.timeframe === timeframe);

	if (!targetProjection)
	{
		throw new Error(`Invalid timeframe: ${timeframe}`);
	}

	return {
		current: currentUsage,
		projected: targetProjection,
		growth: {
			storage: `${((targetProjection.storage - currentUsage.storage) / currentUsage.storage * 100).toFixed(1) }%`,
			connections: `${((targetProjection.connections - currentUsage.connections) / currentUsage.connections * 100).toFixed(1) }%`,
			cpu: `${((targetProjection.cpu - currentUsage.cpu) / currentUsage.cpu * 100).toFixed(1) }%`,
			memory: `${((targetProjection.memory - currentUsage.memory) / currentUsage.memory * 100).toFixed(1) }%`,
		},
		timeframe,
	};
}

/**
 * Generates recommendations for the specified database and parameters.
 */
async function generateRecommendations(
	database: string,
	parameters: { priority?: 'low' | 'medium' | 'high'; type?: 'scale_up' | 'scale_out' | 'optimize' | 'archive'; maxCost?: number },
)
{
	const currentUsage = await getCurrentUsage(database);
	const projections = await getProjections(database);
	const recommendations = await getRecommendations(database, currentUsage, projections);

	// Filter recommendations based on parameters
	let filteredRecommendations = recommendations;

	if (parameters.priority)
	{
		filteredRecommendations = filteredRecommendations.filter(r => r.priority === parameters.priority);
	}

	if (parameters.type)
	{
		filteredRecommendations = filteredRecommendations.filter(r => r.type === parameters.type);
	}

	if (typeof parameters.maxCost === 'number')
	{
		const maxCost: number = parameters.maxCost;
		filteredRecommendations = filteredRecommendations.filter(r => r.estimatedCost <= maxCost);
	}

	// Compute implementation timeline safely
	const timelineMonths = filteredRecommendations.reduce((max, r) =>
	{
		const months = Number.parseInt(String(r.timeline).split('-')[0], 10) || 1;
		return Math.max(max, months);
	}, 1);

	return {
		recommendations: filteredRecommendations,
		totalEstimatedCost: filteredRecommendations.reduce((sum, r) => sum + r.estimatedCost, 0),
		implementationTimeline: `${timelineMonths} months`,
	};
}

/**
 * Simulates a scenario for the specified database and scenario.
 */
async function simulateScenario(
	database: string,
	scenario: { storageMultiplier?: number; connectionMultiplier?: number; cpuMultiplier?: number; memoryMultiplier?: number; name?: string; timelineMultiplier?: number },
)
{
	const currentUsage = await getCurrentUsage(database);

	// Apply scenario modifications
	const simulatedUsage = {
		storage: currentUsage.storage * (scenario.storageMultiplier || 1),
		connections: currentUsage.connections * (scenario.connectionMultiplier || 1),
		cpu: Math.min(100, currentUsage.cpu * (scenario.cpuMultiplier || 1)),
		memory: Math.min(100, currentUsage.memory * (scenario.memoryMultiplier || 1)),
	};

	// Generate projections for simulated usage
	const projections = await getProjections(database);
	const simulatedProjections = projections.map(p => ({
		...p,
		storage: p.storage * (scenario.storageMultiplier || 1),
		connections: p.connections * (scenario.connectionMultiplier || 1),
		cpu: Math.min(100, p.cpu * (scenario.cpuMultiplier || 1)),
		memory: Math.min(100, p.memory * (scenario.memoryMultiplier || 1)),
	}));

	const recommendations = await getRecommendations(database, simulatedUsage, simulatedProjections);

	return {
		scenario: scenario.name || 'Custom Scenario',
		currentUsage: simulatedUsage,
		projections: simulatedProjections,
		recommendations,
		impact: {
			costIncrease: recommendations.reduce((sum, r) => sum + Math.max(0, r.estimatedCost), 0),
			timelineAcceleration: scenario.timelineMultiplier || 1,
			riskLevel: calculateRiskLevel(simulatedUsage, simulatedProjections),
		},
	};
}

/**
 * Calculates the usage status based on the current value and threshold.
 */
function getUsageStatus(current: number, threshold: number): 'low' | 'medium' | 'high' | 'critical'
{
	const percentage = (current / threshold) * 100;

	if (percentage < 50) return 'low';
	if (percentage < 75) return 'medium';
	if (percentage < 90) return 'high';
	return 'critical';
}

/**
 * Calculates the risk level based on the usage and projections.
 */
function calculateRiskLevel(
	usage: { cpu: number; memory: number },
	projections: Array<{ timeframe: '1month' | '3months' | '6months' | '1year'; connections: number; cpu: number; memory: number }>,
): 'low' | 'medium' | 'high'
{
	const sixMonthProjection = projections.find(p => p.timeframe === '6months');
	if (!sixMonthProjection) return 'low';

	let riskScore = 0;

	if (sixMonthProjection.cpu > 90) riskScore += 3;
	else if (sixMonthProjection.cpu > 80) riskScore += 2;
	else if (sixMonthProjection.cpu > 70) riskScore += 1;

	if (sixMonthProjection.memory > 90) riskScore += 3;
	else if (sixMonthProjection.memory > 85) riskScore += 2;
	else if (sixMonthProjection.memory > 75) riskScore += 1;

	if (sixMonthProjection.connections > 150) riskScore += 2;
	else if (sixMonthProjection.connections > 100) riskScore += 1;

	if (riskScore >= 6) return 'high';
	if (riskScore >= 3) return 'medium';
	return 'low';
}
