import { NextResponse } from 'next/server';
import { db<PERSON>ogger } from '@/lib/logger';

import db from '@/lib/database/client';
import type { NextRequest } from 'next/server';
import type { DatabaseAlert, AlertSeverity } from '@/types/database';

// In-memory storage for alerts (in production, use database)
const alerts = new Map<string, DatabaseAlert>();

// Initialize with sample data
initializeSampleAlerts();

export async function GET(request: NextRequest)
{
	try
	{
		const searchParams = request.nextUrl.searchParams;
		const database = searchParams.get('database');
		const severity = searchParams.get('severity');
		const active = searchParams.get('active') === 'true';
		const limit = parseInt(searchParams.get('limit') || '50');

		let alertList = Array.from(alerts.values());

		if (database)
		{
			alertList = alertList.filter(alert => alert.database === database);
		}

		if (severity)
		{
			alertList = alertList.filter(alert => alert.severity === severity);
		}

		if (active)
		{
			alertList = alertList.filter(alert => !alert.resolvedAt);
		}

		// Sort by triggered date (most recent first)
		alertList.sort((a, b) => b.triggeredAt.getTime() - a.triggeredAt.getTime());

		// Apply limit
		alertList = alertList.slice(0, limit);

		return NextResponse.json(alertList);
	}
	catch (error)
	{
		dbLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Database alerts fetch error:');
		return NextResponse.json(
			{ error: 'Failed to fetch database alerts' },
			{ status: 500 },
		);
	}
}

export async function POST(request: NextRequest)
{
	try
	{
		const body = await request.json();
		const { action, alertId, database } = body;

		if (!action)
		{
			return NextResponse.json(
				{ error: 'Action is required' },
				{ status: 400 },
			);
		}

		switch (action)
		{
			case 'acknowledge':
				if (!alertId)
				{
					return NextResponse.json(
						{ error: 'Alert ID is required for acknowledge action' },
						{ status: 400 },
					);
				}
				const ackResult = await acknowledgeAlert(alertId);
				return NextResponse.json(ackResult);

			case 'resolve':
				if (!alertId)
				{
					return NextResponse.json(
						{ error: 'Alert ID is required for resolve action' },
						{ status: 400 },
					);
				}
				const resolveResult = await resolveAlert(alertId);
				return NextResponse.json(resolveResult);

			case 'check':
				if (!database)
				{
					return NextResponse.json(
						{ error: 'Database is required for check action' },
						{ status: 400 },
					);
				}
				const checkResult = await checkDatabaseAlerts(database);
				return NextResponse.json(checkResult);

			case 'test':
				const testResult = await testAlertSystem();
				return NextResponse.json(testResult);

			default:
				return NextResponse.json(
					{ error: 'Invalid action' },
					{ status: 400 },
				);
		}
	}
	catch (error)
	{
		dbLogger.error({ error: error instanceof Error ? error.message : String(error) }, 'Database alert operation error:');
		return NextResponse.json(
			{ error: 'Failed to perform alert operation' },
			{ status: 500 },
		);
	}
}

async function acknowledgeAlert(alertId: string)
{
	const alert = alerts.get(alertId);
	if (!alert)
	{
		throw new Error('Alert not found');
	}

	if (alert.acknowledgedAt)
	{
		throw new Error('Alert already acknowledged');
	}

	alert.acknowledgedAt = new Date();
	alerts.set(alertId, alert);

	return {
		success: true,
		message: 'Alert acknowledged successfully',
		alert,
	};
}

async function resolveAlert(alertId: string)
{
	const alert = alerts.get(alertId);
	if (!alert)
	{
		throw new Error('Alert not found');
	}

	if (alert.resolvedAt)
	{
		throw new Error('Alert already resolved');
	}

	alert.resolvedAt = new Date();
	if (!alert.acknowledgedAt)
	{
		alert.acknowledgedAt = new Date();
	}

	alerts.set(alertId, alert);

	return {
		success: true,
		message: 'Alert resolved successfully',
		alert,
	};
}

async function checkDatabaseAlerts(database: string)
{
	const newAlerts = [];

	try
	{
		// Check connection health
		const connectionAlert = await checkConnectionHealth(database);
		if (connectionAlert)
		{
			newAlerts.push(connectionAlert);
		}

		// Check performance metrics
		const performanceAlerts = await checkPerformanceMetrics(database);
		newAlerts.push(...performanceAlerts);

		// Check capacity
		const capacityAlert = await checkCapacity(database);
		if (capacityAlert)
		{
			newAlerts.push(capacityAlert);
		}

		// Check security
		const securityAlerts = await checkSecurity(database);
		newAlerts.push(...securityAlerts);

		// Store new alerts
		newAlerts.forEach((alert) =>
		{
			alerts.set(alert.id, alert);
		});

		return {
			newAlerts: newAlerts.length,
			alerts: newAlerts,
		};
	}
	catch (error)
	{
		throw new Error(`Failed to check alerts for ${database}: ${error instanceof Error ? error.message : 'Unknown error'}`);
	}
}

async function checkConnectionHealth(database: string): Promise<DatabaseAlert | null>
{
	const startTime = Date.now();

	try
	{
		switch (database)
		{
			case 'scylla':
				await db.scylla.execute('SELECT * FROM system.local LIMIT 1');
				break;

			case 'mariadb':
				await db.mariadb.execute('SELECT 1');
				break;

			case 'redis':
				await db.redis.ping();
				break;

			case 'manticore':
				await db.manticore.sql('SHOW STATUS LIMIT 1');
				break;
		}

		const responseTime = Date.now() - startTime;

		// Check if response time is too high
		if (responseTime > 5000)
		{
			return createAlert(
				database,
				'performance',
				'high',
				'High Database Response Time',
				`Database response time is ${responseTime}ms, exceeding threshold of 5000ms`,
				'response_time',
				5000,
				responseTime,
				['Check database load', 'Review slow queries', 'Consider scaling'],
			);
		}

		return null;
	}
	catch (error)
	{
		return createAlert(
			database,
			'connection',
			'critical',
			'Database Connection Failed',
			`Unable to connect to ${database}: ${error instanceof Error ? error.message : 'Unknown error'}`,
			'connection_status',
			1,
			0,
			['Check database service status', 'Verify network connectivity', 'Review configuration'],
		);
	}
}

async function checkPerformanceMetrics(database: string): Promise<DatabaseAlert[]>
{
	const alerts = [];

	// Simulate performance checks
	const cpuUsage = Math.random() * 100;
	const memoryUsage = Math.random() * 100;
	const diskUsage = Math.random() * 100;

	if (cpuUsage > 90)
	{
		alerts.push(createAlert(
			database,
			'performance',
			'high',
			'High CPU Usage',
			`Database CPU usage is ${cpuUsage.toFixed(1)}%, exceeding threshold of 90%`,
			'cpu_usage',
			90,
			cpuUsage,
			['Identify resource-intensive queries', 'Consider scaling up', 'Review indexing strategy'],
		));
	}

	if (memoryUsage > 85)
	{
		alerts.push(createAlert(
			database,
			'performance',
			'medium',
			'High Memory Usage',
			`Database memory usage is ${memoryUsage.toFixed(1)}%, exceeding threshold of 85%`,
			'memory_usage',
			85,
			memoryUsage,
			['Review memory configuration', 'Optimize queries', 'Consider adding more RAM'],
		));
	}

	if (diskUsage > 80)
	{
		alerts.push(createAlert(
			database,
			'capacity',
			'high',
			'High Disk Usage',
			`Database disk usage is ${diskUsage.toFixed(1)}%, exceeding threshold of 80%`,
			'disk_usage',
			80,
			diskUsage,
			['Clean up old data', 'Archive historical records', 'Add more storage'],
		));
	}

	return alerts;
}

async function checkCapacity(database: string): Promise<DatabaseAlert | null>
{
	// Simulate capacity check
	const connectionCount = Math.floor(Math.random() * 200);
	const maxConnections = 150;

	if (connectionCount > maxConnections * 0.9)
	{
		return createAlert(
			database,
			'capacity',
			'medium',
			'High Connection Count',
			`Database has ${connectionCount} active connections, approaching limit of ${maxConnections}`,
			'connection_count',
			maxConnections * 0.9,
			connectionCount,
			['Review connection pooling', 'Optimize application connections', 'Increase connection limit'],
		);
	}

	return null;
}

async function checkSecurity(database: string): Promise<DatabaseAlert[]>
{
	const alerts = [];

	// Simulate security checks
	const failedLogins = Math.floor(Math.random() * 20);
	const suspiciousQueries = Math.floor(Math.random() * 10);

	if (failedLogins > 10)
	{
		alerts.push(createAlert(
			database,
			'security',
			'medium',
			'High Failed Login Attempts',
      `${failedLogins} failed login attempts detected in the last hour`,
      'failed_logins',
      10,
      failedLogins,
      ['Review access logs', 'Check for brute force attacks', 'Consider IP blocking'],
		));
	}

	if (suspiciousQueries > 5)
	{
		alerts.push(createAlert(
			database,
			'security',
			'high',
			'Suspicious Query Activity',
      `${suspiciousQueries} suspicious queries detected`,
      'suspicious_queries',
      5,
      suspiciousQueries,
      ['Review query logs', 'Check for SQL injection attempts', 'Audit user permissions'],
		));
	}

	return alerts;
}

async function testAlertSystem()
{
	const testAlert = createAlert(
		'mariadb',
		'performance',
		'low',
		'Test Alert',
		'This is a test alert to verify the alerting system is working',
		'test_metric',
		100,
		150,
		['This is a test alert - no action required'],
	);

	alerts.set(testAlert.id, testAlert);

	return {
		success: true,
		message: 'Test alert created successfully',
		alert: testAlert,
	};
}

function createAlert(
	database: string,
	type: DatabaseAlert['type'],
	severity: AlertSeverity,
	title: string,
	description: string,
	metric: string,
	threshold: number,
	currentValue: number,
	actions: string[],
): DatabaseAlert
{
	return {
		id: generateAlertId(),
		database: database as any,
		type,
		severity,
		title,
		description,
		metric,
		threshold,
		currentValue,
		triggeredAt: new Date(),
		acknowledgedAt: null,
		resolvedAt: null,
		actions,
	};
}

function generateAlertId(): string
{
	return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

function initializeSampleAlerts(): void
{
	const sampleAlerts: DatabaseAlert[] = [
		{
			id: 'alert_001',
			database: 'mariadb',
			type: 'performance',
			severity: 'high',
			title: 'Slow Query Performance',
			description: 'Average query response time has exceeded 2 seconds',
			metric: 'avg_query_time',
			threshold: 2000,
			currentValue: 2500,
			triggeredAt: new Date(Date.now() - 3600000),
			acknowledgedAt: null,
			resolvedAt: null,
			actions: ['Review slow query log', 'Optimize indexes', 'Consider query rewriting'],
		},
		{
			id: 'alert_002',
			database: 'redis',
			type: 'capacity',
			severity: 'medium',
			title: 'High Memory Usage',
			description: 'Redis memory usage is at 87% of available memory',
			metric: 'memory_usage',
			threshold: 85,
			currentValue: 87,
			triggeredAt: new Date(Date.now() - 1800000),
			acknowledgedAt: new Date(Date.now() - 1200000),
			resolvedAt: null,
			actions: ['Review memory configuration', 'Clean up expired keys', 'Consider scaling'],
		},
		{
			id: 'alert_003',
			database: 'scylla',
			type: 'connection',
			severity: 'critical',
			title: 'Connection Pool Exhausted',
			description: 'All connection pool slots are in use',
			metric: 'connection_pool_usage',
			threshold: 95,
			currentValue: 100,
			triggeredAt: new Date(Date.now() - 900000),
			acknowledgedAt: new Date(Date.now() - 600000),
			resolvedAt: new Date(Date.now() - 300000),
			actions: ['Increase pool size', 'Review connection leaks', 'Optimize connection usage'],
		},
	];

	sampleAlerts.forEach((alert) =>
	{
		alerts.set(alert.id, alert);
	});
}
