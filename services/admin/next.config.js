/** @type {import('next').NextConfig} */
const nextConfig =
{
	experimental: {
		optimizePackageImports: ['@mantine/core', '@mantine/hooks', 'lucide-react'],
	},
	env:
	{
		CUSTOM_PORT: '3004',
	},
	eslint:
	{
		// Disable ESLint during build - will be handled by root ESLint config
		ignoreDuringBuilds: true,
	},
	webpack: (config, { isServer }) =>
	{
		// Handle node: URI scheme for built-in modules
		config.resolve.alias =
		{
			...config.resolve.alias,
			'node:crypto': 'crypto',
			'node:fs': 'fs',
			'node:path': 'path',
			'node:os': 'os',
			'node:url': 'url',
			'node:util': 'util',
			'node:stream': 'stream',
			'node:buffer': 'buffer',
			'node:events': 'events',
		};

		// Externalize native modules and server-only dependencies
		if (isServer)
		{
			config.externals = config.externals || [];
			config.externals.push(
				// Native modules
				'node-libcurl',
				// Database drivers
				'cassandra-driver',
				'mysql2',
				'redis',
				// Other native modules
				'@redis/client',
				'sharp',
				'canvas'
			);
		}

		return config;
	},
	assetPrefix: '',
	basePath: '',
	trailingSlash: false,
	async redirects()
	{
		return ([
			{
				source: '/',
				destination: '/login',
				permanent: false,
			},
		]);
	},
	async headers()
	{
		return ([
			{
				source: '/(.*)',
				headers: [
					{
						key: 'X-Frame-Options',
						value: 'DENY',
					},
					// {
					// 	key: 'X-Content-Type-Options',
					// 	value: 'nosniff',
					// },
					// {
					// 	key: 'Referrer-Policy',
					// 	value: 'strict-origin-when-cross-origin',
					// },
				],
			},
		]);
	},
};

module.exports = nextConfig;
