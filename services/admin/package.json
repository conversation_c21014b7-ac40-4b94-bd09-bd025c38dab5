{"name": "@domainr/admin", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev -p 3003", "build": "next build", "start": "next start -p 3003", "lint": "echo 'Use root ESLint: pnpm lint from project root'", "type-check": "tsc --noEmit", "init:users": "tsx src/scripts/init-user-management.ts"}, "packageManager": "pnpm@10.15.0", "dependencies": {"@mantine/core": "^7.12.0", "@mantine/dates": "^7.17.8", "@mantine/form": "^7.12.0", "@mantine/hooks": "^7.12.0", "@mantine/modals": "^7.12.0", "@mantine/notifications": "^7.12.0", "@mantine/spotlight": "^7.12.0", "@tabler/icons-react": "^3.11.0", "argon2": "^0.40.3", "bcrypt": "^5.1.1", "buffer": "^6.0.3", "cassandra-driver": "^4.7.2", "crypto-browserify": "^3.12.1", "date-fns": "^4.1.0", "events": "^3.3.0", "iron-session": "^8.0.2", "lucide-react": "^0.544.0", "mysql2": "^3.11.0", "next": "^15.4.6", "next-auth": "^4.24.0", "pino": "^8.17.2", "pino-pretty": "^10.3.1", "react": "^18.3.0", "react-dom": "^18.3.0", "recharts": "^2.15.4", "redis": "^4.7.0", "redis-smq": "^8.3.1", "redis-smq-common": "^8.3.1", "shared": "workspace:*", "stream-browserify": "^3.0.0", "swr": "^2.2.5", "typescript": "^5.5.0", "util": "^0.12.5", "uuid": "^10.0.0", "zod": "^3.23.8"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/node": "^22.0.0", "@types/react": "^18.3.0", "@types/react-dom": "^18.3.0", "@types/uuid": "^10.0.0", "postcss": "^8.4.39", "postcss-preset-mantine": "^1.17.0", "postcss-simple-vars": "^7.0.1", "tsx": "^4.7.0", "ws": "^8.18.0"}, "engines": {"node": ">=22.0.0", "pnpm": ">=10.15.0"}}