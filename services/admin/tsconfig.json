{"extends": "../../_config/tsconfig.browser.json", "compilerOptions": {"lib": ["dom", "dom.iterable", "ES2022"], "rootDir": null, "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/lib/*": ["./src/lib/*"], "@/types/*": ["./src/types/*"], "@/utils/*": ["./src/utils/*"], "@/middleware/*": ["./src/middleware/*"], "@/api/*": ["./src/api/*"], "@shared": ["../../shared/src/index.ts"], "@shared/*": ["../../shared/src/*"]}, "jsx": "preserve"}, "include": ["next-env.d.ts", "src/**/*.ts", "src/**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", "tests", "**/*.test.ts", "**/*.test.tsx", "playwright-*.config.ts", ".next/types"]}