#!/usr/bin/env node
 
 
/* eslint-disable no-await-in-loop */
 

/**
 * Database Migration Script for Admin Panel
 * Handles database schema migrations and data updates
 */

const fs = require('fs').promises;
const path = require('path');
const { Client: ScyllaClient } = require('cassandra-driver');
const mysql = require('mysql2/promise');

// Database connection configuration
const config =
{
	mariadb: {
		host: process.env.MARIADB_HOST || 'localhost',
		port: parseInt(process.env.MARIADB_PORT, 10) || 3306,
		database: process.env.MARIADB_DATABASE || 'admin_panel',
		username: process.env.MARIADB_USERNAME || 'admin',
		password: process.env.MARIADB_PASSWORD || 'password',
	},
	scylla: {
		hosts: (process.env.SCYLLA_HOSTS || 'localhost').split(','),
		keyspace: process.env.SCYLLA_KEYSPACE || 'admin_panel',
		username: process.env.SCYLLA_USERNAME,
		password: process.env.SCYLLA_PASSWORD,
	},
};

// Import shared logger
const { logger } = require('@shared');

const log = logger.getLogger('admin-migrations');

// Module-scope DB connections
let mariadbConn = null;
let scyllaClient = null;

// Migration tracking
class MigrationTracker
{
	constructor()
	{
		this.migrations = new Map();
	}

	async loadMigrations()
	{
		const migrationsDir = path.join(__dirname, '..', 'migrations');

		try
		{
			const files = await fs.readdir(migrationsDir);
			const migrationFiles = files
				.filter(file => file.endsWith('.sql') || file.endsWith('.js') || file.endsWith('.cql'))
				.sort();

			for (const file of migrationFiles)
			{
				const filePath = path.join(migrationsDir, file);
				const content = await fs.readFile(filePath, 'utf8');

				this.migrations.set(file, {
					path: filePath,
					content,
					executed: false,
				});
			}

			log.info(`Loaded ${this.migrations.size} migration files`);
		}
		catch (error)
		{
			log.warn(`Migrations directory not found: ${error.message}`);
		}
	}

	async checkExecutedMigrations()
	{
		log.info('Checking previously executed migrations...');
		await ensureMigrationsTable();

		const [rows] = await mariadbConn.query('SELECT name FROM admin_migrations');
		const executed = new Set(rows.map(r => r.name));

		for (const [name, migration] of this.migrations)
		{
			if (executed.has(name))
			{
				migration.executed = true;
			}
		}
	}

	async executeMigration(name, migration)
	{
		log.info(`Executing migration: ${name}`);

		try
		{
			if (name.endsWith('.sql'))
			{
				await this.executeSQLMigration(migration.content);
			}
			else if (name.endsWith('.cql'))
			{
				await this.executeCQLMigration(migration.content);
			}
			else if (name.endsWith('.js'))
			{
				await this.executeJSMigration(migration.path);
			}

			migration.executed = true;
			await recordExecutedMigration(name);
			log.info(`Migration ${name} completed successfully`);
		}
		catch (error)
		{
			log.error(`Migration ${name} failed: ${error.message}`);
			throw error;
		}
	}

	// Execute SQL migration statements against MariaDB
	async executeSQLMigration(sql)
	{
		if (!mariadbConn)
		{
			throw new Error('MariaDB connection not initialized');
		}

		log.info('Executing SQL migration...');

		// Remove SQL comments and split by semicolon
		const statements = sql
			.split('\n')
			.filter(line => !line.trim().startsWith('--'))
			.join('\n')
			.split(';')
			.map(s => s.trim())
			.filter(s => s.length > 0);

		await mariadbConn.beginTransaction();
		try
		{
			for (const stmt of statements)
			{
				 
				await mariadbConn.query(stmt);
			}
			await mariadbConn.commit();
		}
		catch (err)
		{
			await mariadbConn.rollback();
			throw err;
		}
	}

	// Execute CQL migration statements against ScyllaDB
	async executeCQLMigration(cql)
	{
		if (!scyllaClient)
		{
			throw new Error('ScyllaDB client not initialized');
		}

		log.info('Executing CQL migration...');

		const statements = cql
			.split('\n')
			.filter(line => !line.trim().startsWith('//') && !line.trim().startsWith('--'))
			.join('\n')
			.split(';')
			.map(s => s.trim())
			.filter(s => s.length > 0);

		for (const stmt of statements)
		{
			 
			await scyllaClient.execute(stmt);
		}
	}

	async executeJSMigration(filePath)
	{
		// This would execute a JavaScript migration file
		log.info('Executing JavaScript migration...');
		const migration = require(filePath);
		if (typeof migration.up === 'function')
		{
			await migration.up();
		}
	}
}

// Database initialization
async function initializeDatabases()
{
	log.info('Initializing database connections...');

	// MariaDB initialization
	try
	{
		log.info('Connecting to MariaDB...');
		mariadbConn = await mysql.createConnection({
			host: config.mariadb.host,
			port: config.mariadb.port,
			user: config.mariadb.username,
			password: config.mariadb.password,
			database: config.mariadb.database,
			multipleStatements: true,
		});
		log.info('MariaDB connection established');
	}
	catch (error)
	{
		log.error(`MariaDB connection failed: ${error.message}`);
		throw error;
	}

	// ScyllaDB initialization
	try
	{
		log.info('Connecting to ScyllaDB...');
		scyllaClient = new ScyllaClient({
			contactPoints: config.scylla.hosts,
			localDataCenter: process.env.SCYLLA_LOCAL_DC || 'datacenter1',
			keyspace: config.scylla.keyspace,
			credentials: config.scylla.username && config.scylla.password
				? { username: config.scylla.username, password: config.scylla.password }
				: undefined,
		});
		await scyllaClient.connect();
		log.info('ScyllaDB connection established');
	}
	catch (error)
	{
		log.error(`ScyllaDB connection failed: ${error.message}`);
		throw error;
	}
}

// Schema validation
async function validateSchema()
{
	log.info('Validating database schema...');

	const requiredTables = [
		'users',
		'sessions',
		'audit_logs',
		'configurations',
		'alerts',
		'metrics',
	];

	// Check if all required tables exist in MariaDB
	for (const table of requiredTables)
	{
		 
		const [rows] = await mariadbConn.query(
			'SELECT COUNT(*) AS cnt FROM information_schema.tables WHERE table_schema = ? AND table_name = ? LIMIT 1',
			[config.mariadb.database, table],
		);
		const exists = rows[0]?.cnt > 0;
		if (!exists)
		{
			log.warn(`Missing required table: ${table}`);
		}
		else
		{
			log.info(`Found table: ${table}`);
		}
	}

	log.info('Schema validation completed');
}

// Data integrity checks
async function checkDataIntegrity()
{
	log.info('Running data integrity checks...');

	// Check for orphaned records
	log.info('Checking for orphaned records...');

	// Check for data consistency
	log.info('Checking data consistency...');

	// Check for required data
	log.info('Checking required data...');

	log.info('Data integrity checks completed');
}

// Seed initial data
async function seedInitialData()
{
	log.info('Seeding initial data...');

	// Create default admin user if not exists
	log.info('Creating default admin user...');
	try
	{
		const [rows] = await mariadbConn.query('SELECT id FROM users WHERE email = ? LIMIT 1', ['<EMAIL>']);
		if (rows.length === 0)
		{
			const bcrypt = require('bcrypt');
			const passwordHash = await bcrypt.hash(process.env.DEFAULT_ADMIN_PASSWORD || 'admin123', 10);
			await mariadbConn.query(
				'INSERT INTO users (email, password_hash, role, created_at) VALUES (?, ?, ?, NOW())',
				['<EMAIL>', passwordHash, 'admin'],
			);
			log.info('Default admin user created');
		}
		else
		{
			log.info('Default admin user already exists');
		}
	}
	catch (e)
	{
		log.warn(`Skipping default admin user creation: ${e.message}`);
	}

	// Create default configurations
	log.info('Creating default configurations...');
	try
	{
		await mariadbConn.query(
			'INSERT IGNORE INTO configurations (`key`, `value`, updated_at) VALUES (?, ?, NOW())',
			['ui.theme', 'light'],
		);
	}
	catch (e)
	{
		log.warn(`Skipping default configuration creation: ${e.message}`);
	}

	// Create default alert rules
	log.info('Creating default alert rules...');
	try
	{
		await mariadbConn.query(
			'INSERT IGNORE INTO alerts (name, severity, enabled, created_at) VALUES (?, ?, ?, NOW())',
			['High Error Rate', 'high', 0],
		);
	}
	catch (e)
	{
		log.warn(`Skipping default alert rule creation: ${e.message}`);
	}

	log.info('Initial data seeding completed');
}

// Main migration function
async function runMigrations()
{
	const tracker = new MigrationTracker();

	try
	{
		log.info('Starting database migrations...');

		// Initialize database connections
		await initializeDatabases();

		// Load and check migrations
		await tracker.loadMigrations();
		await tracker.checkExecutedMigrations();

		// Execute pending migrations
		for (const [name, migration] of tracker.migrations)
		{
			if (!migration.executed)
			{
				await tracker.executeMigration(name, migration);
			}
		}

		// Validate schema
		await validateSchema();

		// Check data integrity
		await checkDataIntegrity();

		// Seed initial data if needed
		if (process.env.SEED_INITIAL_DATA === 'true')
		{
			await seedInitialData();
		}

		log.info('All migrations completed successfully');
	}
	catch (error)
	{
		log.error(`Migration failed: ${error.message}`);
		try
		{
			if (mariadbConn)
			{
				await mariadbConn.end();
			}
			if (scyllaClient)
			{
				await scyllaClient.shutdown();
			}
		}
		catch (e)
		{
			log.warn(`Error during shutdown: ${e.message}`);
		}
		process.exit(1);
	}
}

// Handle script execution
if (require.main === module)
{
	runMigrations()
		.then(() =>
		{
			log.info('Migration script completed');
			process.exit(0);
		})
		.catch((error) =>
		{
			log.error(`Migration script failed: ${error.message}`);
			process.exit(1);
		});
}

module.exports =
{
	runMigrations,
	MigrationTracker,
};

// Helpers
async function ensureMigrationsTable()
{
	await mariadbConn.query(`
		CREATE TABLE IF NOT EXISTS admin_migrations
		(
			name VARCHAR(255) NOT NULL PRIMARY KEY,
			executed_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
	`);
}

async function recordExecutedMigration(name)
{
	await ensureMigrationsTable();
	await mariadbConn.query('INSERT IGNORE INTO admin_migrations (name) VALUES (?)', [name]);
}
