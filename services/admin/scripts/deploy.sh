#!/bin/bash

# Simple Admin Panel Deployment Script
set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
ENVIRONMENT="${1:-production}"
VERSION="${2:-latest}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Validate environment
validate_environment() {
    log_info "Validating deployment environment: $ENVIRONMENT"

    case $ENVIRONMENT in
        production|staging|development)
            log_info "Environment '$ENVIRONMENT' is valid"
            ;;
        *)
            log_error "Invalid environment: $ENVIRONMENT. Must be one of: production, staging, development"
            exit 1
            ;;
    esac
}

# Pre-deployment checks
pre_deployment_checks() {
    log_info "Running pre-deployment checks..."

    # Check if Docker is running
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker is not running"
        exit 1
    fi

    # Check if required environment files exist
    if [[ ! -f "$PROJECT_DIR/.env.$ENVIRONMENT" ]]; then
        log_error "Environment file .env.$ENVIRONMENT not found"
        exit 1
    fi

    log_info "Pre-deployment checks completed"
}

# Build application
build_application() {
    log_info "Building application for $ENVIRONMENT..."

    cd "$PROJECT_DIR"

    # Copy environment-specific configuration
    cp ".env.$ENVIRONMENT" .env.local

    # Build Docker image
    docker build \
        --build-arg NODE_ENV="$ENVIRONMENT" \
        --build-arg VERSION="$VERSION" \
        -t "admin-panel:$VERSION" \
        -t "admin-panel:latest" \
        .

    log_info "Application build completed"
}

# Run database migrations
run_migrations() {
    log_info "Running database migrations..."

    # Create a temporary container to run migrations
    docker run --rm \
        --env-file "$PROJECT_DIR/.env.$ENVIRONMENT" \
        --network host \
        "admin-panel:$VERSION" \
        node scripts/migrate.js

    log_info "Database migrations completed"
}

# Deploy application
deploy_application() {
    log_info "Deploying application..."

    # Stop existing container if running
    if docker ps -q -f name=admin-panel >/dev/null; then
        log_info "Stopping existing admin-panel container..."
        docker stop admin-panel || true
        docker rm admin-panel || true
    fi

    # Start new container
    docker run -d \
        --name admin-panel \
        --restart unless-stopped \
        --env-file "$PROJECT_DIR/.env.$ENVIRONMENT" \
        --network host \
        --health-cmd="node healthcheck.js" \
        --health-interval=30s \
        --health-timeout=10s \
        --health-retries=3 \
        "admin-panel:$VERSION"

    log_info "Application deployment completed"
}

# Wait for application to be ready
wait_for_health() {
    log_info "Waiting for application to be ready..."

    local max_attempts=10
    local attempt=1

    while [[ $attempt -le $max_attempts ]]; do
        if curl -f "http://localhost:3004/api/health" >/dev/null 2>&1; then
            log_info "Application is ready"
            return 0
        fi

        log_info "Health check attempt $attempt/$max_attempts failed, waiting..."
        sleep 5
        ((attempt++))
    done

    log_error "Application failed to become ready within timeout"
    return 1
}

# Post-deployment verification
post_deployment_verification() {
    log_info "Running post-deployment verification..."

    # Check if container is running
    if ! docker ps -q -f name=admin-panel >/dev/null; then
        log_error "Admin panel container is not running"
        return 1
    fi

    # Check application endpoints
    local base_url="http://localhost:3004"

    # Health check
    if ! curl -f "$base_url/api/health" >/dev/null 2>&1; then
        log_error "Health check endpoint failed"
        return 1
    fi

    log_info "Post-deployment verification completed successfully"
}

# Rollback function
rollback() {
    log_warn "Rolling back deployment..."

    # Stop current container
    docker stop admin-panel || true
    docker rm admin-panel || true

    # Start previous version (assuming it's tagged as 'previous')
    if docker images -q admin-panel:previous >/dev/null; then
        docker run -d \
            --name admin-panel \
            --restart unless-stopped \
            --env-file "$PROJECT_DIR/.env.$ENVIRONMENT" \
            --network host \
            admin-panel:previous

        log_info "Rollback completed"
    else
        log_error "No previous version found for rollback"
        exit 1
    fi
}

# Cleanup old images
cleanup() {
    log_info "Cleaning up old Docker images..."

    # Keep only the latest 3 versions
    docker images admin-panel --format "table {{.Tag}}\t{{.ID}}" | \
        grep -v latest | \
        tail -n +4 | \
        awk '{print $2}' | \
        xargs -r docker rmi || true

    log_info "Cleanup completed"
}

# Main deployment function
main() {
    log_info "Starting deployment of Admin Panel"
    log_info "Environment: $ENVIRONMENT"
    log_info "Version: $VERSION"

    # Tag current version as previous for rollback
    if docker images -q admin-panel:latest >/dev/null; then
        docker tag admin-panel:latest admin-panel:previous
    fi

    validate_environment
    pre_deployment_checks
    build_application

    # Run migrations only in production/staging
    if [[ "$ENVIRONMENT" != "development" ]]; then
        run_migrations
    fi

    deploy_application

    if wait_for_health; then
        post_deployment_verification
        cleanup
        log_info "Deployment completed successfully!"
    else
        log_error "Deployment failed - initiating rollback"
        rollback
        exit 1
    fi
}

# Handle script interruption
trap 'log_error "Deployment interrupted"; exit 1' INT TERM

# Run main function
main "$@"
