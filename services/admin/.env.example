# Environment Configuration
NODE_ENV=development
PORT=3004

# Session Configuration
SESSION_SECRET=your-super-secret-session-key-change-in-production
SESSION_TIMEOUT=28800000

# Database Configuration
SCYLLA_HOSTS=localhost:9042
SCYLLA_KEYSPACE=domainr
SCYLLA_USERNAME=
SCYLLA_PASSWORD=

MARIADB_HOST=localhost
MARIADB_PORT=3306
MARIADB_DATABASE=domainr
MARIADB_USERNAME=root
MARIADB_PASSWORD=

REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
# Redis keys will use "p4:" prefix automatically

MANTICORE_HOST=localhost
MANTICORE_PORT=9308

# Service URLs
WEB_APP_URL=http://localhost:3000
DOMAIN_SEEDER_URL=http://localhost:3005

# Authentication Configuration
BCRYPT_ROUNDS=12
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900000

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json
