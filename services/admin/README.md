# Domain Ranking System - Admin Panel

A comprehensive Next.js web application that provides system administrators with complete control and monitoring capabilities for the Domain Ranking System.

## Table of Contents

- [Quick Start](#quick-start)
- [Architecture Overview](#architecture-overview)
- [Deployment Guide](#deployment-guide)
- [Configuration Reference](#configuration-reference)
- [Developer Guide](#developer-guide)
- [Security Guide](#security-guide)
- [Monitoring and Alerting](#monitoring-and-alerting)
- [Operational Procedures](#operational-procedures)
- [Troubleshooting Guide](#troubleshooting-guide)
- [API Reference](#api-reference)

## Features

- **Service Health Monitoring**: Real-time status of all microservices
- **Domain Management**: CRUD operations for domain data
- **Crawl Job Management**: Control and monitor crawling operations
- **User Management**: Role-based access control for administrators
- **System Configuration**: Manage system settings and parameters
- **Analytics Dashboard**: Comprehensive system metrics and reporting
- **System Logs**: Advanced log viewing and debugging tools
- **Database Tools**: Database operations and maintenance
- **Alert Management**: Configure and manage system alerts

## Quick Start

## Technology Stack

- **Framework**: Next.js 14 with App Router
- **UI Library**: Mantine UI v7
- **Language**: TypeScript
- **Authentication**: Static users with iron-session
- **State Management**: React Context + useReducer
- **Data Fetching**: SWR for real-time updates
- **Charts**: Recharts
- **Icons**: Tabler Icons

## Getting Started

### Prerequisites

- Node.js 20+
- Access to the Domain Ranking System databases
- Running microservices (web-app, crawler, ranking-engine, scheduler, domain-seeder)

### Installation

1. Install dependencies:

   ```bash
   cd services/admin
   pnpm install
   ```

2. Configure environment variables:

   ```bash
   cp .env.example .env.local
   # Edit .env.local with your configuration
   ```

3. Start the development server:
   ```bash
   pnpm run dev
   ```

The admin panel will be available at `http://localhost:3004`

### Production Build

```bash
pnpm run build
pnpm start
```

### Docker Deployment

```bash
docker build -t domainr-admin .
docker run -p 3004:3004 domainr-admin
```

## Configuration

### Environment Variables

- `PORT`: Server port (default: 3004)
- `NODE_ENV`: Environment (development/production/test)
- `SESSION_SECRET`: Secret key for session encryption
- `SESSION_TIMEOUT`: Session timeout in milliseconds
- Database connection strings for ScyllaDB, MariaDB, Redis, Manticore
- Service URLs for all microservices

### Authentication

The admin panel uses static user authentication with the following default credentials for development:

- Username: `admin`
- Password: `admin123`

**Important**: Change these credentials in production and use proper password hashing.

## Project Structure

```
services/admin/
├── src/
│   ├── app/                 # Next.js App Router pages
│   │   ├── dashboard/       # Dashboard page
│   │   ├── login/          # Login page
│   │   └── api/            # API routes
│   ├── components/         # Reusable UI components
│   │   └── Layout/         # Layout components
│   ├── lib/               # Core libraries and configurations
│   │   ├── config.ts      # Environment configuration
│   │   └── theme.ts       # Mantine theme configuration
│   ├── types/             # TypeScript type definitions
│   ├── utils/             # Utility functions
│   └── middleware.ts      # Next.js middleware for authentication
├── public/                # Static assets
├── Dockerfile            # Docker configuration
├── next.config.js        # Next.js configuration
├── postcss.config.js     # PostCSS configuration
└── tsconfig.json         # TypeScript configuration
```

## Development

### Code Style

- Follow the project's ESLint configuration
- Use TypeScript strict mode
- Follow Allman-style braces
- Use descriptive variable and function names
- Add JSDoc comments for complex logic

### Testing

```bash
pnpm run type-check  # TypeScript type checking
pnpm run lint        # ESLint checking
```

## Integration

The admin panel integrates with:

- **ScyllaDB**: Domain analysis data, crawl jobs, rankings
- **MariaDB**: Categories, configuration, user data
- **Redis**: Queues, sessions, caching
- **Manticore**: Search indexes
- **All Microservices**: Health monitoring, control operations

## Security

- Session-based authentication with secure cookies
- Role-based access control (RBAC)
- Input validation and sanitization
- CSRF protection
- Secure headers (HSTS, CSP, etc.)
- Rate limiting on API endpoints

## Monitoring

- Application performance monitoring
- Error tracking and alerting
- User activity logging
- Resource usage tracking
- Real-time system health monitoring

---

## Architecture Overview

### System Architecture

The Domain Ranking System Admin Panel is a comprehensive web-based administrative interface designed to manage and monitor a sophisticated domain analysis and ranking platform. The system provides real-time oversight of multiple microservices, databases, and external integrations while maintaining high security, performance, and reliability standards.

#### Core Objectives

- **Centralized Management**: Single interface for all system administration tasks
- **Real-time Monitoring**: Live updates on system health and performance
- **Scalable Architecture**: Support for growing data volumes and user base
- **Security First**: Comprehensive security measures and access controls
- **High Availability**: Minimal downtime with robust failover mechanisms
- **Extensibility**: Modular design for easy feature additions

#### Key Components

- **Multi-database integration**: ScyllaDB, MariaDB, Redis, Manticore
- **Real-time service health monitoring**
- **Comprehensive domain management with bulk operations**
- **Advanced crawl job scheduling and monitoring**
- **AI-powered content generation management**
- **Role-based access control with audit logging**
- **Advanced analytics and reporting**
- **Real-time alerts and notifications**

#### Integration Points

The admin panel integrates with:

- **ScyllaDB**: Domain analysis data, crawl jobs, rankings
- **MariaDB**: Categories, configuration, user data
- **Redis**: Queues, sessions, caching
- **Manticore**: Search indexes
- **All Microservices**: Health monitoring, control operations

[↑ Back to TOC](#table-of-contents)

---

## Deployment Guide

### Prerequisites

#### System Requirements

- **Node.js**: 20+ with pnpm package manager
- **Docker**: 20.10+ and Docker Compose 2.0+
- **Git**: For version control
- **Hardware**: Minimum 8GB RAM, 4 CPU cores, 50GB available disk space
- **Network**: Access to all required databases and microservices

#### External Dependencies

- **Databases**: ScyllaDB, MariaDB, Redis, Manticore Search
- **Microservices**: web-app, worker, domain-seeder services
- **Monitoring**: Prometheus, Grafana (optional but recommended)

### Development Environment

```bash
# Clone repository
git clone <repository-url>
cd domainr/services/admin

# Install dependencies
pnpm install

# Environment configuration
cp .env.example .env.local
# Edit .env.local with development settings

# Start development server
pnpm run dev
```

The admin panel will be available at `http://localhost:3004`

### Production Deployment

#### Using Docker (Recommended)

```bash
# Build production image
docker build -t domainr-admin .

# Run with production configuration
docker run -d \
  --name domainr-admin \
  --env-file .env \
  -p 3004:3004 \
  --restart unless-stopped \
  domainr-admin
```

#### Using Docker Compose

```bash
# From project root
docker-compose up -d admin
```

#### Manual Deployment

```bash
# Build for production
pnpm run build

# Start production server
pnpm start
```

### Environment Configuration

Create `.env.local` (development) or `.env` (production) with required variables:

```bash
# Core Application
NODE_ENV=production
PORT=3004
APP_URL=https://admin.yourdomain.com

# Security
SESSION_SECRET=your_256_bit_secret_key
SESSION_TIMEOUT=3600000

# Database Connections
SCYLLA_HOSTS=scylla1:9042,scylla2:9042
SCYLLA_KEYSPACE=domainr
MARIADB_URL=mysql://user:pass@mariadb:3306/domainr
REDIS_URL=redis://redis:6379
MANTICORE_HOST=manticore:9308

# Service URLs
WEBAPP_URL=http://webapp:3000
WORKER_URL=http://worker:3001
DOMAIN_SEEDER_URL=http://domain-seeder:3004
```

### Health Checks and Monitoring

```bash
# Application health
curl http://localhost:3004/api/health

# Service monitoring endpoints
curl http://localhost:3004/api/services/status
```

[↑ Back to TOC](#table-of-contents)

---

## Configuration Reference

### Environment Variables

#### Core Application Settings

| Variable   | Type   | Default                 | Description              | Required |
| ---------- | ------ | ----------------------- | ------------------------ | -------- |
| `NODE_ENV` | string | `development`           | Application environment  | Yes      |
| `PORT`     | number | `3004`                  | Server port              | No       |
| `APP_URL`  | string | `http://localhost:3004` | Application base URL     | Yes      |
| `APP_NAME` | string | `Admin Panel`           | Application display name | No       |

#### Authentication Configuration

| Variable          | Type   | Description                                 | Required |
| ----------------- | ------ | ------------------------------------------- | -------- |
| `SESSION_SECRET`  | string | Secret key for session encryption (256-bit) | Yes      |
| `SESSION_TIMEOUT` | number | Session timeout in milliseconds             | No       |
| `BCRYPT_ROUNDS`   | number | Password hashing rounds (default: 12)       | No       |

#### Database Configuration

```bash
# ScyllaDB/Cassandra
SCYLLA_HOSTS=localhost:9042
SCYLLA_KEYSPACE=domainr
SCYLLA_USERNAME=admin
SCYLLA_PASSWORD=password

# MariaDB
MARIADB_HOST=localhost
MARIADB_PORT=3306
MARIADB_USER=admin
MARIADB_PASSWORD=password
MARIADB_DATABASE=domainr

# Redis
REDIS_URL=redis://localhost:6379
REDIS_DB=0

# Manticore Search
MANTICORE_HOST=localhost
MANTICORE_PORT=9308
```

#### Security Settings

```bash
# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com

# Rate Limiting
RATE_LIMIT_WINDOW=900000  # 15 minutes
RATE_LIMIT_MAX=100        # requests per window

# Security Headers
HSTS_MAX_AGE=31536000
CSP_POLICY=default-src 'self'
```

[↑ Back to TOC](#table-of-contents)

---

## Developer Guide

### Development Environment Setup

```bash
# Prerequisites check
node --version  # Should be 20+
pnpm --version  # Should be 9+
docker --version

# Project setup
git clone <repository-url>
cd domainr/services/admin
pnpm install
```

### Project Structure

```
services/admin/
├── src/
│   ├── app/                 # Next.js App Router pages
│   │   ├── dashboard/       # Dashboard components
│   │   ├── login/          # Authentication
│   │   └── api/            # API routes
│   ├── components/         # Reusable UI components
│   │   ├── Layout/         # Layout components
│   │   ├── Forms/          # Form components
│   │   └── Charts/         # Data visualization
│   ├── lib/               # Core libraries
│   │   ├── auth.ts        # Authentication logic
│   │   ├── database.ts    # Database connections
│   │   └── api-client.ts  # API client
│   ├── types/             # TypeScript definitions
│   └── utils/             # Utility functions
├── public/                # Static assets
└── docs/                  # Documentation (archived)
```

### Development Scripts

```bash
# Development
pnpm dev          # Start development server
pnpm build        # Build for production
pnpm start        # Start production server
pnpm lint         # Run ESLint
pnpm type-check   # TypeScript checking
```

### API Development

API routes follow Next.js 14 App Router conventions:

```typescript
// src/app/api/health/route.ts
export async function GET() {
  return Response.json({
    status: "healthy",
    timestamp: new Date().toISOString(),
  });
}
```

[↑ Back to TOC](#table-of-contents)

---

## Security Guide

### Authentication & Authorization

- **Session-based authentication** with secure cookies
- **Role-based access control (RBAC)**
- **Multi-factor authentication** support
- **Session timeout** and management
- **Audit logging** for all admin actions

### Security Headers

```typescript
// Implemented security headers
const securityHeaders = {
  "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
  "Content-Security-Policy": "default-src 'self'",
  "X-Frame-Options": "DENY",
  "X-Content-Type-Options": "nosniff",
  "Referrer-Policy": "strict-origin-when-cross-origin",
};
```

### Input Validation

- **Server-side validation** for all inputs
- **SQL injection** protection
- **XSS prevention** with sanitization
- **CSRF protection** with tokens
- **Rate limiting** on sensitive endpoints

[↑ Back to TOC](#table-of-contents)

---

## Monitoring and Alerting

### Application Metrics

- **Performance monitoring** with response times
- **Error rate tracking** and alerting
- **User activity monitoring**
- **Resource usage** (CPU, memory, disk)
- **Database connection health**

### Health Endpoints

```bash
# Application health
GET /api/health
# Response: { "status": "healthy", "timestamp": "..." }

# Detailed system status
GET /api/status
# Response: { "services": {...}, "databases": {...} }

# Service monitoring
GET /api/services/status
# Response: { "webapp": "healthy", "worker": "healthy" }
```

### Logging

- **Structured logging** with correlation IDs
- **Different log levels** (error, warn, info, debug)
- **User action audit trails**
- **Performance metrics** logging
- **Security event** logging

[↑ Back to TOC](#table-of-contents)

---

## Operational Procedures

### Daily Operations

1. **Morning Health Check**

   - Verify all services are running
   - Check overnight error logs
   - Review system metrics
   - Validate database connections

2. **User Management**

   - Review user access logs
   - Handle access requests
   - Monitor failed login attempts
   - Update user permissions

3. **System Monitoring**
   - Check resource usage trends
   - Review performance metrics
   - Monitor queue sizes
   - Verify backup status

### Incident Response

**P0 - Critical Issues**

- Admin panel completely inaccessible
- Data loss or corruption
- Security breaches

**P1 - High Priority**

- Performance degradation
- Feature unavailability
- Database connection issues

**Response Steps:**

1. Assess impact and severity
2. Implement immediate mitigation
3. Investigate root cause
4. Apply permanent fix
5. Post-incident review

[↑ Back to TOC](#table-of-contents)

---

## Troubleshooting Guide

### Common Issues

**Service Won't Start**

```bash
# Check logs
docker logs domainr-admin

# Verify environment variables
grep -E "^[A-Z]" .env

# Test database connections
pnpm run test-connections
```

**Authentication Issues**

```bash
# Clear sessions
redis-cli del "sessions:*"

# Reset user passwords
pnpm run reset-password admin

# Verify session configuration
echo $SESSION_SECRET | wc -c  # Should be 64+ characters
```

**Performance Issues**

```bash
# Monitor resource usage
docker stats domainr-admin

# Check database query performance
pnpm run analyze-queries

# Review slow request logs
grep "slow" logs/application.log
```

**Database Connection Errors**

```bash
# Test individual connections
pnpm run test-db scylla
pnpm run test-db mariadb
pnpm run test-db redis

# Check network connectivity
telnet mariadb-host 3306
```

[↑ Back to TOC](#table-of-contents)

---

## API Reference

### Authentication Endpoints

```
POST /api/auth/login
POST /api/auth/logout
GET  /api/auth/me
```

### System Management

```
GET  /api/health
GET  /api/status
GET  /api/services/status
POST /api/services/{service}/restart
```

### Domain Management

```
GET    /api/domains
POST   /api/domains
PUT    /api/domains/{id}
DELETE /api/domains/{id}
POST   /api/domains/bulk-import
```

### User Management

```
GET    /api/users
POST   /api/users
PUT    /api/users/{id}
DELETE /api/users/{id}
POST   /api/users/{id}/reset-password
```

### System Configuration

```
GET  /api/config
PUT  /api/config
POST /api/config/backup
POST /api/config/restore
```

For detailed API specifications, see the OpenAPI documentation in `docs/api/openapi.yaml`.

[↑ Back to TOC](#table-of-contents)

---

## License

Private - Domain Ranking System
