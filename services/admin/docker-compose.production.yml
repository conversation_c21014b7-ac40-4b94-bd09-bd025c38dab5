name: pr__domainr
# ----- NETWORKS ----- #
networks:
  mynet_internal:
    name: mynet_internal
    driver: bridge
    internal: true
  mynet_external:
    name: mynet_external
    driver: bridge
    external: true
# ----- SERVICES ----- #
services:
  # Admin Panel Application
  admin-panel:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        NODE_ENV: production
    container_name: admin-panel
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - PORT=3003
    env_file:
      - .env.production
    ports:
      - "3003:3003"
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads

    networks:
      - admin-network
      - database-network
    depends_on:
      - redis
      - prometheus
    healthcheck:
      test: ["CMD", "node", "healthcheck.js"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 512M
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"
        labels: "service=admin-panel"
