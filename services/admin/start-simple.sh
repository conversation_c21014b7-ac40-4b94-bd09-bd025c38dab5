#!/bin/bash

echo "🚀 Starting Simple Admin Panel..."

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Please run this from the services/admin directory"
    exit 1
fi

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    pnpm install
fi

# Create simple env if it doesn't exist
if [ ! -f ".env.local" ]; then
    echo "⚙️ Creating simple environment file..."
    cat > .env.local << EOF
NODE_ENV=development
PORT=3004
SESSION_SECRET=simple-dev-secret-change-in-production

# Optional - only if you want to connect to existing services
WEB_APP_URL=http://localhost:3000
WORKER_URL=http://localhost:3001
DOMAIN_SEEDER_URL=http://localhost:3005

# Optional - only if you want database connectivity
MARIADB_HOST=localhost
MARIADB_PORT=3306
MARIADB_DATABASE=domainr
MARIADB_USERNAME=root
MARIADB_PASSWORD=

SCYLLA_HOSTS=localhost:9042
SCYLLA_KEYSPACE=domainr
EOF
fi

echo "🎯 Starting admin panel on http://localhost:3004"
echo "📝 Default login: admin / admin123"
echo ""

# Start the development server
pnpm dev
