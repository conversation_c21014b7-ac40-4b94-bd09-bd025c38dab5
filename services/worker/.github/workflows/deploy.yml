name: Worker Service CI/CD Pipeline

on:
  push:
    branches: [main, develop]
    paths: ['services/worker/**']
  pull_request:
    branches: [main]
    paths: ['services/worker/**']
  workflow_dispatch:
    inputs:
      environment:
        description: 'Target environment'
        required: true
        default: 'staging'
        type: choice
        options:
        - staging
        - production
      image_tag:
        description: 'Docker image tag'
        required: false
        default: 'latest'

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: domainr/worker-service
  WORKING_DIRECTORY: services/worker

jobs:
  # Code Quality and Testing
  test:
    name: Test and Quality Checks
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ${{ env.WORKING_DIRECTORY }}

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'pnpm'
        cache-dependency-path: '${{ env.WORKING_DIRECTORY }}/pnpm-lock.yaml'

    - name: Install pnpm
      uses: pnpm/action-setup@v2
      with:
        version: latest

    - name: Install dependencies
      run: pnpm install --frozen-lockfile

    - name: Run linting
      run: pnpm run lint

    - name: Run type checking
      run: pnpm run type-check

    - name: Run unit tests
      run: pnpm run test:unit --coverage

    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./services/worker/coverage/lcov.info
        flags: worker-service
        name: worker-service-coverage

    - name: Run security audit
      run: pnpm audit --audit-level moderate

  # Build and Push Docker Image
  build:
    name: Build Docker Image
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name != 'pull_request'
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}
          type=raw,value=${{ github.event.inputs.image_tag }},enable=${{ github.event_name == 'workflow_dispatch' }}

    - name: Build and push Docker image
      id: build
      uses: docker/build-push-action@v5
      with:
        context: ${{ env.WORKING_DIRECTORY }}
        file: ${{ env.WORKING_DIRECTORY }}/Dockerfile
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        platforms: linux/amd64,linux/arm64

    - name: Generate SBOM
      uses: anchore/sbom-action@v0
      with:
        image: ${{ steps.meta.outputs.tags }}
        format: spdx-json
        output-file: sbom.spdx.json

    - name: Upload SBOM
      uses: actions/upload-artifact@v3
      with:
        name: sbom
        path: sbom.spdx.json

  # Security Scanning
  security:
    name: Security Scanning
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name != 'pull_request'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: ${{ needs.build.outputs.image-tag }}
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

    - name: Run Snyk security scan
      uses: snyk/actions/docker@master
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
      with:
        image: ${{ needs.build.outputs.image-tag }}
        args: --severity-threshold=high

  # Integration Testing
  integration-test:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name != 'pull_request'

    services:
      scylla:
        image: scylladb/scylla:6.2
        options: >-
          --health-cmd "cqlsh -e 'describe cluster'"
          --health-interval 30s
          --health-timeout 10s
          --health-retries 5
        ports:
          - 9042:9042

      mariadb:
        image: mariadb:11.2
        env:
          MYSQL_ROOT_PASSWORD: test_password
          MYSQL_DATABASE: domainr_test
          MYSQL_USER: worker
          MYSQL_PASSWORD: worker_password
        options: >-
          --health-cmd "mysqladmin ping"
          --health-interval 30s
          --health-timeout 10s
          --health-retries 5
        ports:
          - 3306:3306

      redis:
        image: redis:7.2-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 30s
          --health-timeout 10s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'pnpm'
        cache-dependency-path: '${{ env.WORKING_DIRECTORY }}/pnpm-lock.yaml'

    - name: Install pnpm
      uses: pnpm/action-setup@v2
      with:
        version: latest

    - name: Install dependencies
      working-directory: ${{ env.WORKING_DIRECTORY }}
      run: pnpm install --frozen-lockfile

    - name: Wait for services
      run: |
        timeout 300 bash -c 'until nc -z localhost 9042; do sleep 5; done'
        timeout 300 bash -c 'until nc -z localhost 3306; do sleep 5; done'
        timeout 300 bash -c 'until nc -z localhost 6379; do sleep 5; done'

    - name: Run integration tests
      working-directory: ${{ env.WORKING_DIRECTORY }}
      run: pnpm run test:integration
      env:
        SCYLLA_HOSTS: localhost:9042
        MARIA_HOST: localhost
        MARIA_PORT: 3306
        MARIA_DATABASE: domainr_test
        MARIA_USER: worker
        MARIA_PASSWORD: worker_password
        REDIS_URL: redis://localhost:6379
        NODE_ENV: test

  # Deploy to Staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build, security, integration-test]
    if: |
      github.ref == 'refs/heads/develop' ||
      (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'staging')
    environment:
      name: staging
      url: https://worker-staging.domainr.com

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Configure kubectl
      uses: azure/k8s-set-context@v3
      with:
        method: kubeconfig
        kubeconfig: ${{ secrets.KUBE_CONFIG_STAGING }}

    - name: Deploy to staging
      working-directory: ${{ env.WORKING_DIRECTORY }}
      run: |
        chmod +x scripts/deploy.sh
        ./scripts/deploy.sh \
          --environment staging \
          --tag ${{ github.sha }} \
          --wait-timeout 600
      env:
        IMAGE_TAG: ${{ github.sha }}

    - name: Run smoke tests
      run: |
        kubectl wait --for=condition=ready pod -l app=worker-service -n domainr-staging --timeout=300s
        kubectl exec deployment/worker-service -n domainr-staging -- \
          curl -f http://localhost:3000/health

    - name: Notify deployment
      uses: 8398a7/action-slack@v3
      if: always()
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        text: |
          Worker Service deployment to staging: ${{ job.status }}
          Commit: ${{ github.sha }}
          Branch: ${{ github.ref_name }}
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  # Deploy to Production
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [build, security, integration-test]
    if: |
      github.ref == 'refs/heads/main' ||
      (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'production')
    environment:
      name: production
      url: https://worker.domainr.com

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Configure kubectl
      uses: azure/k8s-set-context@v3
      with:
        method: kubeconfig
        kubeconfig: ${{ secrets.KUBE_CONFIG_PRODUCTION }}

    - name: Pre-deployment checks
      run: |
        # Check cluster health
        kubectl get nodes
        kubectl get pods -n domainr-production -l app=worker-service

        # Check database connectivity
        kubectl exec deployment/worker-service -n domainr-production -- \
          nc -zv scylla-service 9042 || echo "ScyllaDB check failed"

    - name: Deploy to production
      working-directory: ${{ env.WORKING_DIRECTORY }}
      run: |
        chmod +x scripts/deploy.sh
        ./scripts/deploy.sh \
          --environment production \
          --tag ${{ github.sha }} \
          --wait-timeout 900
      env:
        IMAGE_TAG: ${{ github.sha }}

    - name: Post-deployment verification
      run: |
        # Wait for rollout to complete
        kubectl rollout status deployment/worker-service -n domainr-production --timeout=600s

        # Verify health
        kubectl exec deployment/worker-service -n domainr-production -- \
          curl -f http://localhost:3000/health

        # Check metrics
        kubectl exec deployment/worker-service -n domainr-production -- \
          curl -s http://localhost:3000/metrics | grep worker_jobs_total

    - name: Run production smoke tests
      run: |
        # Test job processing
        kubectl exec deployment/worker-service -n domainr-production -- \
          curl -X POST http://localhost:3000/test/job \
          -H "Content-Type: application/json" \
          -d '{"domain":"example.com","type":"test"}'

    - name: Create GitHub release
      if: github.ref == 'refs/heads/main'
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: worker-v${{ github.run_number }}
        release_name: Worker Service v${{ github.run_number }}
        body: |
          ## Changes
          - Deployed commit: ${{ github.sha }}
          - Docker image: ${{ needs.build.outputs.image-tag }}

          ## Deployment
          - Environment: Production
          - Deployed at: ${{ github.event.head_commit.timestamp }}
        draft: false
        prerelease: false

    - name: Notify deployment
      uses: 8398a7/action-slack@v3
      if: always()
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        text: |
          🚀 Worker Service deployment to production: ${{ job.status }}
          Commit: ${{ github.sha }}
          Image: ${{ needs.build.outputs.image-tag }}
          Release: worker-v${{ github.run_number }}
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  # Rollback on Failure
  rollback:
    name: Rollback on Failure
    runs-on: ubuntu-latest
    needs: [deploy-production]
    if: failure() && github.ref == 'refs/heads/main'
    environment:
      name: production

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Configure kubectl
      uses: azure/k8s-set-context@v3
      with:
        method: kubeconfig
        kubeconfig: ${{ secrets.KUBE_CONFIG_PRODUCTION }}

    - name: Rollback deployment
      working-directory: ${{ env.WORKING_DIRECTORY }}
      run: |
        chmod +x scripts/rollback.sh
        ./scripts/rollback.sh --environment production

    - name: Verify rollback
      run: |
        kubectl rollout status deployment/worker-service -n domainr-production --timeout=300s
        kubectl exec deployment/worker-service -n domainr-production -- \
          curl -f http://localhost:3000/health

    - name: Notify rollback
      uses: 8398a7/action-slack@v3
      with:
        status: 'warning'
        channel: '#alerts'
        text: |
          ⚠️ Worker Service production deployment failed and was rolled back
          Failed commit: ${{ github.sha }}
          Please investigate and fix the issue before redeploying
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
