import { defineConfig } from 'vitest/config';

export default defineConfig(async () =>
{
  const tsconfigPaths = (await import('vite-tsconfig-paths')).default;
  return {
    plugins: [tsconfigPaths()],
    test: {
      environment: 'node',
      include: [
        'src/__tests__/integration/**/*.test.ts',
        'src/__tests__/performance/**/*.test.ts',
        'src/__tests__/chaos/**/*.test.ts',
        'src/__tests__/database/**/*.test.ts',
      ],
      setupFiles: ['src/__tests__/setup.ts'],
      testTimeout: 300000, // 5 minutes for integration tests
      hookTimeout: 60000,  // 1 minute for setup/teardown
      coverage: {
        provider: 'v8',
        reporter: ['text', 'lcov', 'html', 'json'],
        reportsDirectory: 'coverage/integration',
        include: ['src/**/*.ts'],
        exclude: [
          'src/**/*.d.ts',
          'src/**/__tests__/**',
          'src/**/index.ts',
          'src/__tests__/**',
        ],
        thresholds: {
          global: {
            branches: 70,
            functions: 70,
            lines: 70,
            statements: 70,
          },
        },
      },
      pool: 'threads',
      poolOptions: {
        threads: {
          singleThread: false,
          maxThreads: 4,
          minThreads: 1,
        },
      },
      sequence: {
        concurrent: false, // Run integration tests sequentially to avoid conflicts
      },
    },
  };
});
