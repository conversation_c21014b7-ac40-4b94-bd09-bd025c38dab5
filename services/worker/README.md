# Worker Service

The Worker Service is a **consolidated, horizontally scalable domain processing worker** that completely replaces three existing services (ranking-engine, crawler, and scheduler) by extracting and consolidating ALL their functionality into a single, independent service.

## Table of Contents

- [Overview](#overview)
- [Key Features](#key-features)
- [Architecture](#architecture)
- [Quick Start](#quick-start)
  - [Prerequisites](#prerequisites)
  - [Environment Variables](#environment-variables)
- [Installation](#installation)
- [Usage](#usage)
  - [Service Mode (Daemon)](#service-mode-daemon)
  - [CLI Mode](#cli-mode)
  - [Available CLI Commands](#available-cli-commands)
- [Domain Processing Pipeline](#domain-processing-pipeline)
- [Deployment Guide](#deployment-guide)
  - [Docker](#docker)
  - [Kubernetes](#kubernetes)
  - [Horizontal Pod Autoscaler](#horizontal-pod-autoscaler)
- [Configuration Reference](#configuration-reference)
- [Monitoring and Observability](#monitoring-and-observability)
- [Operational Procedures](#operational-procedures)
- [Troubleshooting Guide](#troubleshooting-guide)
- [Migration Guide](#migration-guide)
- [Development](#development)
- [Support](#support)
- [License](#license)

## Overview

Each worker instance processes domains from a shared job queue, executing a complete sequential pipeline:
**crawling → analysis → ranking → indexing**

The worker service contains all the crawling logic, analysis algorithms, ranking calculations, and indexing operations extracted directly from the source services. Multiple worker instances can run in parallel, each processing different domains concurrently without conflicts through a distributed locking mechanism.

## Key Features

### Complete Functionality Migration

- **From Crawler Service**: All analyzer classes, module registry system, analysis logic, external integrations
- **From Ranking-Engine Service**: Scoring algorithms, ranking system, database operations, backlink analysis
- **From Scheduler Service**: Job management, cron operations, queue management, database maintenance

### Horizontal Scalability

- Deploy multiple worker instances that coordinate through Redis-based domain locking
- Configurable concurrency per worker instance
- Automatic workload distribution across workers

### Production Ready

- Robust error handling and retry mechanisms
- Comprehensive monitoring and alerting
- Graceful startup and shutdown procedures
- Health checks and readiness probes

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Worker 1      │    │   Worker 2      │    │   Worker N      │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ Job Consumer│ │    │ │ Job Consumer│ │    │ │ Job Consumer│ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │  Pipeline   │ │    │ │  Pipeline   │ │    │ │  Pipeline   │ │
│ │ Coordinator │ │    │ │ Coordinator │ │    │ │ Coordinator │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │  Shared Queue   │
                    │   & Database    │
                    └─────────────────┘
```

## Quick Start

### Prerequisites

- **Node.js 20+**: JavaScript runtime environment
- **Redis** (for job queues and locking)
- **ScyllaDB** (for domain data storage)
- **MariaDB** (for relational data)
- **Manticore Search** (for search indexing)
- **Docker** (v20.10+): For containerization
- **kubectl** (v1.25+): Kubernetes command-line tool (for K8s deployment)

### System Requirements

#### Minimum Requirements (per worker instance)

- **CPU**: 500m (0.5 cores)
- **Memory**: 1GB RAM
- **Storage**: 10GB available disk space
- **Network**: Stable internet connection for external API calls

#### Recommended Requirements (per worker instance)

- **CPU**: 2 cores
- **Memory**: 4GB RAM
- **Storage**: 50GB available disk space
- **Network**: High-bandwidth connection for optimal performance

[↑ Back to TOC](#table-of-contents)

---

## Installation

### Environment Variables

```bash
# Worker Configuration
WORKER_INSTANCE_ID=worker-1           # Unique identifier for this worker
MAX_CONCURRENT_TASKS=10               # Domains processed simultaneously

# Database Connections
SCYLLA_HOSTS=scylla:9042             # ScyllaDB cluster hosts
SCYLLA_USERNAME=username             # Optional ScyllaDB username
SCYLLA_PASSWORD=password             # Optional ScyllaDB password
MARIA_HOST=mariadb                   # MariaDB host
MARIA_PORT=3306                      # MariaDB port
MARIA_USER=root                      # MariaDB username
MARIA_PASSWORD=password              # MariaDB password
MARIA_DATABASE=domain_ranking        # MariaDB database name
REDIS_URL=redis://redis:6379         # Redis connection URL
REDIS_PASSWORD=password              # Optional Redis password
MANTICORE_HOST=manticore             # Manticore Search host
MANTICORE_PORT=9308                  # Manticore Search port

# External Services
BROWSERLESS_URL=http://browserless:3000    # Browserless service for screenshots
IMAGE_PROXY_URL=https://images.weserv.nl   # Image optimization service

# Processing Configuration
CRAWL_TIMEOUT=30000                  # Crawl timeout in milliseconds
RANKING_UPDATE_BATCH_SIZE=100        # Batch size for ranking updates
JOB_RETRY_ATTEMPTS=3                 # Number of retry attempts for failed jobs
JOB_RETRY_DELAY=5000                 # Delay between retries in milliseconds

# Monitoring
LOG_LEVEL=info                       # Log level (debug, info, warn, error)
METRICS_ENABLED=true                 # Enable metrics collection
HEALTH_CHECK_INTERVAL=30000          # Health check interval in milliseconds
```

[↑ Back to TOC](#table-of-contents)

---

## Usage

### Service Mode (Daemon)

Start the worker service in daemon mode:

```bash
# Using npm scripts
npm start

# Using CLI
npm run worker:start

# Direct execution
tsx src/index.ts
```

### CLI Mode

The worker service includes a comprehensive CLI for management and debugging:

```bash
# Show help
npm run cli -- --help

# Start worker service
npm run cli start --max-concurrent 10

# Check service status
npm run cli status --detailed

# Check health
npm run cli health --check-dependencies

# Process a single domain
npm run cli process domain example.com --wait

# Show queue statistics
npm run cli queue stats

# Show metrics
npm run cli monitor metrics --json

# Show active alerts
npm run cli monitor alerts

# Debug domain locks
npm run cli debug locks

# Show configuration
npm run cli config show
```

### Available CLI Commands

#### Service Management

- `start` - Start the worker service
- `stop` - Stop the worker service gracefully
- `status` - Show worker service status
- `health` - Check worker service health

#### Domain Processing

- `process domain <domain>` - Process a single domain
- `process batch <file>` - Process domains from file

#### Queue Management

- `queue stats` - Show queue statistics
- `queue pause <queue>` - Pause queue consumption
- `queue resume <queue>` - Resume queue consumption
- `queue clear <queue>` - Clear queue (remove all jobs)

#### Database Operations

- `db status` - Check database connections
- `db migrate` - Run database migrations
- `db health` - Check database health

#### Monitoring

- `monitor metrics` - Show current metrics
- `monitor alerts` - Show active alerts
- `monitor logs` - Show recent logs

#### Debugging

- `debug locks` - Show domain locks information
- `debug pipeline` - Show pipeline status
- `debug errors` - Show recent errors

#### Configuration

- `config show` - Show current configuration
- `config validate` - Validate configuration
- `config reload` - Reload configuration

[↑ Back to TOC](#table-of-contents)

---

## Domain Processing Pipeline

Each domain goes through a complete processing pipeline:

### 1. Crawling Phase

- **DNS Analysis**: A, AAAA, MX, CNAME, TXT, NS, SOA records
- **Robots.txt Analysis**: Parsing, compliance checking, sitemap extraction
- **SSL Analysis**: Certificate validation, security headers, SSL grading
- **Homepage Analysis**: HTML parsing, meta tags, content analysis, SEO metrics
- **Favicon Collection**: Detection, download, processing, storage
- **Screenshot Capture**: Visual capture using Browserless integration
- **Performance Audit**: Core Web Vitals measurement, performance metrics
- **Content Analysis**: Quality assessment, readability, language detection

### 2. Ranking Phase

- **Performance Scoring**: Core Web Vitals, load times, performance metrics
- **Security Scoring**: SSL grading, security headers, vulnerability assessment
- **SEO Scoring**: Meta tags, robots.txt compliance, content quality
- **Technical Scoring**: DNS configuration, hosting quality, infrastructure
- **Backlink Scoring**: Domain authority, link quality, referral analysis
- **Composite Ranking**: Weighted score aggregation and grade assignment

### 3. Indexing Phase

- **ScyllaDB Updates**: Store crawl results and analysis data
- **MariaDB Updates**: Store relational data and rankings
- **Manticore Sync**: Update search indexes for full-text search
- **Cache Invalidation**: Clear cached data across Redis and application caches

[↑ Back to TOC](#table-of-contents)

---

## Deployment Guide

### Local Development

```bash
# Build the Docker image
docker build -t worker-service:latest .

# Run locally with environment file
docker run --env-file .env -p 3000:3000 worker-service:latest

# Run with individual environment variables
docker run -e WORKER_INSTANCE_ID=worker-dev \
           -e MAX_CONCURRENT_TASKS=5 \
           -p 3000:3000 worker-service:latest
```

### Production Deployment

#### Multi-stage Docker Build

```dockerfile
# Build stage
FROM node:20-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

# Production stage
FROM node:20-alpine AS production
WORKDIR /app
COPY --from=builder /app/node_modules ./node_modules
COPY src/ ./src/
COPY tsconfig.json ./
COPY package*.json ./

EXPOSE 3000
HEALTHCHEK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD node src/health.js

USER node
CMD ["npm", "start"]
```

### Advanced Kubernetes Configuration

#### Service Configuration

```yaml
apiVersion: v1
kind: Service
metadata:
  name: worker-service
spec:
  selector:
    app: worker-service
  ports:
    - port: 80
      targetPort: 3000
      name: http
  type: ClusterIP
```

#### ConfigMap for Environment Variables

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: worker-config
data:
  MAX_CONCURRENT_TASKS: "10"
  LOG_LEVEL: "info"
  METRICS_ENABLED: "true"
  CRAWL_TIMEOUT: "30000"
  JOB_RETRY_ATTEMPTS: "3"
```

#### Secret for Sensitive Data

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: worker-secrets
type: Opaque
data:
  SCYLLA_PASSWORD: <base64-encoded-password>
  MARIA_PASSWORD: <base64-encoded-password>
  REDIS_PASSWORD: <base64-encoded-password>
```

[↑ Back to TOC](#table-of-contents)

---

## Configuration Reference

### Queue Configuration

The worker service supports multiple job queues:

- **Domain Crawl Queue**: Main queue for domain processing jobs

  - Default concurrency: 10 domains per worker
  - Retry attempts: 3
  - Timeout: 5 minutes per domain

- **Ranking Update Queue**: Queue for ranking recalculation jobs

  - Default concurrency: 20 rankings per worker
  - Retry attempts: 2
  - Timeout: 2 minutes per batch

- **Maintenance Queue**: Queue for system maintenance tasks
  - Default concurrency: 1 task per worker
  - Retry attempts: 1
  - Timeout: 10 minutes per task

### Concurrency Configuration

- `MAX_CONCURRENT_TASKS`: Maximum domains processed simultaneously per worker
- Queue-specific concurrency can be configured per queue type
- Automatic backpressure and throttling to prevent system overload

### Error Handling Configuration

- Configurable retry attempts and delays
- Circuit breaker patterns for external services
- Graceful degradation for partial system failures
- Comprehensive error classification and reporting

### Performance Tuning Options

```bash
# Database Connection Pooling
MARIA_CONNECTION_LIMIT=20
SCYLLA_CONNECTION_POOL_SIZE=10
REDIS_CONNECTION_POOL_SIZE=5

# Processing Timeouts
CRAWL_TIMEOUT=60000              # 60 seconds for domain crawling
RANKING_TIMEOUT=30000            # 30 seconds for ranking calculation
INDEXING_TIMEOUT=15000           # 15 seconds for indexing operations

# Memory Management
NODE_OPTIONS="--max-old-space-size=2048"  # 2GB heap limit
GC_INTERVAL=300000               # Garbage collection every 5 minutes

# External Service Timeouts
BROWSERLESS_TIMEOUT=45000        # 45 seconds for screenshot capture
IMAGE_PROXY_TIMEOUT=10000        # 10 seconds for image optimization
```

[↑ Back to TOC](#table-of-contents)

---

## Monitoring and Observability

### Health Endpoints

The worker service exposes comprehensive health endpoints:

- `GET /health` - Basic health check
- `GET /health/detailed` - Detailed health information
- `GET /health/ready` - Readiness probe for Kubernetes
- `GET /health/live` - Liveness probe for Kubernetes
- `GET /health/metrics` - Performance metrics
- `GET /health/alerts` - Active alerts

### Metrics Collection

The service collects comprehensive metrics:

- **Service Metrics**: Uptime, restart count, job processing statistics
- **System Metrics**: CPU usage, memory usage, disk usage
- **Database Metrics**: Connection pool status, query performance
- **Performance Metrics**: Response times, throughput, error rates
- **Queue Metrics**: Queue depths, processing rates, backlog

### Alerting System

Built-in alerting for critical conditions:

- High error rates (>10%)
- High memory usage (>90%)
- High queue depths (>1000 jobs)
- Slow response times (>30s p95)
- Database connection failures
- External service unavailability

### Advanced Monitoring Setup

#### Prometheus Metrics

```yaml
# prometheus-config.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: "worker-service"
    static_configs:
      - targets: ["worker-service:3000"]
    metrics_path: "/health/metrics"
    scrape_interval: 10s
```

#### Grafana Dashboard

Key metrics to monitor:

- **Service Health**: Uptime, restart count, error rates
- **Performance**: Response times, throughput, queue depths
- **Resources**: CPU usage, memory usage, disk I/O
- **Dependencies**: Database connection status, external service availability

#### Log Aggregation

```yaml
# fluentd-config for log collection
<source>
@type tail
path /var/log/worker/*.log
pos_file /var/log/fluentd/worker.log.pos
tag worker.*
format json
</source>

<match worker.**>
@type elasticsearch
host elasticsearch
port 9200
index_name worker-logs
</match>
```

[↑ Back to TOC](#table-of-contents)

---

## Operational Procedures

### Daily Operations

#### Morning Health Check Routine

1. **Service Status Verification**

   ```bash
   # Check all worker instances
   kubectl get pods -l app=worker -o wide

   # Verify service health
   for pod in $(kubectl get pods -l app=worker -o name); do
     kubectl exec $pod -- curl -s http://localhost:3000/health | jq '.status'
   done
   ```

2. **Queue Health Monitoring**

   ```bash
   # Check queue depths and processing rates
   kubectl exec deployment/worker-service -- npm run cli queue stats

   # Monitor for stuck jobs
   kubectl exec deployment/worker-service -- npm run cli monitor alerts
   ```

3. **Database Connection Verification**
   ```bash
   # Test all database connections
   kubectl exec deployment/worker-service -- npm run cli db health
   ```

#### Weekly Maintenance

- **Performance Review**: Analyze metrics and identify optimization opportunities
- **Capacity Planning**: Review resource usage trends and scaling requirements
- **Configuration Updates**: Apply configuration changes and restart services if needed
- **Security Updates**: Apply security patches and update dependencies

### Incident Response

#### Severity Levels

**P0 - Critical (< 15 minutes response)**

- Complete service outage
- Data corruption or loss
- Security breaches

**P1 - High (< 1 hour response)**

- Significant performance degradation (>50% slower)
- Single database unavailable
- High error rates (>10%)

**P2 - Medium (< 4 hours response)**

- Minor performance issues
- Non-critical feature unavailable
- Monitoring alerts

**P3 - Low (< 24 hours response)**

- Enhancement requests
- Documentation updates
- Non-urgent configuration changes

#### Response Procedures

1. **Immediate Assessment**

   - Identify scope and impact
   - Check service status and metrics
   - Review recent changes

2. **Mitigation Steps**

   - Implement immediate workaround if available
   - Scale resources if needed
   - Rollback recent deployments if necessary

3. **Resolution**

   - Apply permanent fix
   - Test functionality
   - Monitor for stability

4. **Post-Incident**
   - Document lessons learned
   - Update procedures if needed
   - Schedule follow-up improvements

[↑ Back to TOC](#table-of-contents)

---

## Troubleshooting Guide

### Quick Diagnostics

```bash
# Quick service health check
kubectl get pods -n production -l app=worker-service

# Detailed pod information
kubectl describe pods -n production -l app=worker-service

# Recent events
kubectl get events -n production --sort-by='.lastTimestamp' | tail -20
```

### Container Issues

**Pod Won't Start**

```bash
# Check pod events
kubectl describe pod <pod-name>

# Check container logs
kubectl logs <pod-name> -c worker

# Check resource constraints
kubectl top pods -n production
```

**Container Crashes**

```bash
# Check previous container logs
kubectl logs <pod-name> --previous

# Check memory usage
kubectl exec <pod-name> -- cat /proc/meminfo

# Check disk usage
kubectl exec <pod-name> -- df -h
```

### Database Connectivity Issues

**ScyllaDB Connection Problems**

```bash
# Test ScyllaDB connectivity
kubectl exec deployment/worker-service -- npm run cli db status scylla

# Check ScyllaDB cluster status
cqlsh -h scylla-host -e "DESCRIBE CLUSTER;"

# Verify keyspace exists
cqlsh -h scylla-host -e "DESCRIBE KEYSPACES;"
```

**MariaDB Connection Problems**

```bash
# Test MariaDB connectivity
kubectl exec deployment/worker-service -- npm run cli db status mariadb

# Check MariaDB status
mysql -h mariadb-host -u root -p -e "SHOW STATUS;"

# Verify database exists
mysql -h mariadb-host -u root -p -e "SHOW DATABASES;"
```

**Redis Connection Problems**

```bash
# Test Redis connectivity
kubectl exec deployment/worker-service -- npm run cli db status redis

# Check Redis info
redis-cli -h redis-host INFO

# Check memory usage
redis-cli -h redis-host INFO memory
```

### Performance Issues

**High Memory Usage**

```bash
# Monitor memory usage
kubectl top pods -n production

# Check for memory leaks
kubectl exec <pod-name> -- node --inspect src/index.ts

# Analyze heap usage
kubectl exec <pod-name> -- npm run cli monitor metrics --memory
```

**Slow Processing**

```bash
# Check queue statistics
kubectl exec deployment/worker-service -- npm run cli queue stats

# Monitor processing times
kubectl exec deployment/worker-service -- npm run cli monitor metrics --performance

# Check external service response times
kubectl exec deployment/worker-service -- npm run cli debug external-services
```

### Job Processing Issues

**Jobs Stuck in Queue**

```bash
# Check queue depths
kubectl exec deployment/worker-service -- npm run cli queue stats

# Check for locked domains
kubectl exec deployment/worker-service -- npm run cli debug locks

# Clear expired locks
kubectl exec deployment/worker-service -- npm run cli debug locks --clear-expired
```

**High Error Rates**

```bash
# Check recent errors
kubectl exec deployment/worker-service -- npm run cli debug errors --count 100

# Analyze error patterns
kubectl logs -n production -l app=worker-service | grep -i error | sort | uniq -c

# Check external service status
kubectl exec deployment/worker-service -- npm run cli health --check-dependencies
```

[↑ Back to TOC](#table-of-contents)

---

## Migration Guide

### Migration from Original Services

When migrating from the original three services (crawler, ranking-engine, scheduler):

#### Pre-Migration Checklist

1. **Backup Current State**

   - Export current job queues
   - Backup database schemas
   - Document current configuration

2. **Validate Dependencies**
   - Verify all external services are accessible
   - Test database connections
   - Confirm resource availability

#### Migration Steps

1. **Phase 1: Preparation**

   ```bash
   # Stop job creation in original services
   kubectl scale deployment crawler --replicas=0
   kubectl scale deployment ranking-engine --replicas=0
   kubectl scale deployment scheduler --replicas=0

   # Allow existing jobs to complete
   sleep 300
   ```

2. **Phase 2: Worker Deployment**

   ```bash
   # Deploy worker service with minimal replicas
   kubectl apply -f worker-service-deployment.yaml

   # Wait for readiness
   kubectl wait --for=condition=ready pod -l app=worker-service
   ```

3. **Phase 3: Functionality Verification**

   ```bash
   # Test basic functionality
   kubectl exec deployment/worker-service -- npm run cli process domain example.com --wait

   # Verify all pipeline stages
   kubectl exec deployment/worker-service -- npm run cli health --check-dependencies
   ```

4. **Phase 4: Full Scaling**

   ```bash
   # Scale to production capacity
   kubectl scale deployment worker-service --replicas=3

   # Monitor performance
   kubectl exec deployment/worker-service -- npm run cli monitor metrics
   ```

5. **Phase 5: Cleanup**

   ```bash
   # Remove original service deployments
   kubectl delete deployment crawler ranking-engine scheduler

   # Clean up unused resources
   kubectl delete service crawler-service ranking-engine-service scheduler-service
   ```

#### Post-Migration Validation

- Monitor error rates for 24 hours
- Verify job processing throughput
- Check data consistency across databases
- Validate all monitoring and alerting

### Rollback Procedures

If issues arise during migration:

1. **Immediate Rollback**

   ```bash
   # Scale down worker service
   kubectl scale deployment worker-service --replicas=0

   # Restore original services
   kubectl apply -f original-services/
   ```

2. **Data Recovery**
   - Restore database backups if needed
   - Replay missed jobs from queue backups
   - Verify data integrity

[↑ Back to TOC](#table-of-contents)

---

## Docker

### Docker

```dockerfile
FROM node:20-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY src/ ./src/
COPY tsconfig.json ./

EXPOSE 3000
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD node src/health.js

CMD ["npm", "start"]
```

### Kubernetes

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: worker-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: worker-service
  template:
    metadata:
      labels:
        app: worker-service
    spec:
      containers:
        - name: worker
          image: worker-service:latest
          env:
            - name: WORKER_INSTANCE_ID
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: MAX_CONCURRENT_TASKS
              value: "10"
          resources:
            requests:
              memory: "512Mi"
              cpu: "500m"
            limits:
              memory: "2Gi"
              cpu: "2000m"
          livenessProbe:
            httpGet:
              path: /health/live
              port: 3000
            initialDelaySeconds: 60
            periodSeconds: 30
          readinessProbe:
            httpGet:
              path: /health/ready
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 10
```

### Horizontal Pod Autoscaler

```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: worker-service-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: worker-service
  minReplicas: 2
  maxReplicas: 10
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 70
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: 80
```

## Development

### Running Tests

```bash
# Run all tests
npm test

# Run unit tests only
npm run test:unit

# Run integration tests
npm run test:integration

# Run performance tests
npm run test:performance

# Run chaos engineering tests
npm run test:chaos

# Run with coverage
npm run test:coverage
```

### Development Mode

```bash
# Start in development mode with hot reload
npm run dev

# Start with debugging
DEBUG=* npm run dev
```

### Building

```bash
# Build TypeScript
npm run build

# Clean build artifacts
npm run clean
```

## Configuration

### Queue Configuration

The worker service supports multiple job queues:

- **Domain Crawl Queue**: Main queue for domain processing jobs
- **Ranking Update Queue**: Queue for ranking recalculation jobs
- **Maintenance Queue**: Queue for system maintenance tasks

### Concurrency Configuration

- `MAX_CONCURRENT_TASKS`: Maximum domains processed simultaneously per worker
- Queue-specific concurrency can be configured per queue type
- Automatic backpressure and throttling to prevent system overload

### Error Handling Configuration

- Configurable retry attempts and delays
- Circuit breaker patterns for external services
- Graceful degradation for partial system failures
- Comprehensive error classification and reporting

## Troubleshooting

### Common Issues

1. **Database Connection Failures**

   ```bash
   # Check database status
   npm run cli db status

   # Check health with dependencies
   npm run cli health --check-dependencies
   ```

2. **High Memory Usage**

   ```bash
   # Check current metrics
   npm run cli monitor metrics

   # Check active alerts
   npm run cli monitor alerts
   ```

3. **Job Processing Failures**

   ```bash
   # Check queue statistics
   npm run cli queue stats

   # Check recent errors
   npm run cli debug errors --count 50
   ```

4. **Domain Lock Issues**

   ```bash
   # Check active locks
   npm run cli debug locks

   # Check expired locks
   npm run cli debug locks --expired
   ```

### Debugging

Enable debug logging:

```bash
LOG_LEVEL=debug npm start
```

Check service logs:

```bash
# Show recent logs
npm run cli monitor logs --lines 100 --follow

# Filter by log level
npm run cli monitor logs --level error
```

### Performance Tuning

1. **Adjust Concurrency**

   ```bash
   # Reduce concurrency for resource-constrained environments
   MAX_CONCURRENT_TASKS=5 npm start
   ```

2. **Optimize Database Connections**

   ```bash
   # Increase connection pool sizes in environment variables
   MARIA_CONNECTION_LIMIT=20
   SCYLLA_CONNECTION_POOL_SIZE=10
   ```

3. **Tune Timeouts**
   ```bash
   # Increase timeouts for slow networks
   CRAWL_TIMEOUT=60000
   ```

## Migration from Original Services

When migrating from the original three services:

1. **Stop Original Services**: Ensure crawler, ranking-engine, and scheduler services are stopped
2. **Deploy Worker Service**: Deploy worker service instances with appropriate scaling
3. **Verify Functionality**: Use CLI commands to verify all functionality is working
4. **Monitor Performance**: Monitor metrics and alerts to ensure performance meets requirements
5. **Decommission Original Services**: Remove original service deployments once worker service is stable

## Support

For issues and questions:

1. Check the troubleshooting section above
2. Use CLI debugging commands to gather information
3. Check service logs and metrics
4. Review configuration and environment variables

## License

This project is part of the domain ranking system and follows the same licensing terms.

[↑ Back to TOC](#table-of-contents)
