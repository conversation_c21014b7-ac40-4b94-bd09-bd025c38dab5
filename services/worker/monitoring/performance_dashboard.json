{"dashboard": {"id": null, "title": "Worker Service Performance Dashboard", "tags": ["worker-service", "performance", "optimization"], "style": "dark", "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "Performance Overview", "type": "stat", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}, "targets": [{"expr": "worker:domain_processing_rate", "legendFormat": "Processing Rate (domains/min)", "refId": "A"}, {"expr": "histogram_quantile(0.95, worker_domain_processing_duration_seconds_bucket)", "legendFormat": "95th Percentile Latency (s)", "refId": "B"}, {"expr": "worker:domain_processing_success_rate * 100", "legendFormat": "Success Rate (%)", "refId": "C"}, {"expr": "worker:memory_utilization", "legendFormat": "Memory Utilization (%)", "refId": "D"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"displayMode": "list", "orientation": "horizontal"}, "mappings": [], "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}}}, {"id": 2, "title": "Domain Processing Throughput", "type": "graph", "gridPos": {"h": 9, "w": 12, "x": 0, "y": 8}, "targets": [{"expr": "worker:domain_processing_rate", "legendFormat": "Current Rate", "refId": "A"}, {"expr": "100", "legendFormat": "Target Rate", "refId": "B"}], "yAxes": [{"label": "Domains per Minute", "min": 0}], "alert": {"conditions": [{"evaluator": {"params": [50], "type": "lt"}, "operator": {"type": "and"}, "query": {"params": ["A", "5m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "alerting", "for": "5m", "frequency": "10s", "handler": 1, "name": "Low Processing Throughput", "noDataState": "no_data", "notifications": []}}, {"id": 3, "title": "Response Time Distribution", "type": "heatmap", "gridPos": {"h": 9, "w": 12, "x": 12, "y": 8}, "targets": [{"expr": "increase(worker_domain_processing_duration_seconds_bucket[5m])", "legendFormat": "{{le}}", "refId": "A"}], "heatmap": {"xAxis": {"show": true}, "yAxis": {"show": true, "logBase": 1, "min": "auto", "max": "auto"}, "yBucketBound": "auto"}}, {"id": 4, "title": "Resource Utilization", "type": "graph", "gridPos": {"h": 9, "w": 12, "x": 0, "y": 17}, "targets": [{"expr": "rate(process_cpu_seconds_total[5m]) * 100", "legendFormat": "CPU Usage (%)", "refId": "A"}, {"expr": "worker:memory_utilization", "legendFormat": "Memory Usage (%)", "refId": "B"}, {"expr": "worker:heap_utilization", "legendFormat": "Heap Usage (%)", "refId": "C"}], "yAxes": [{"label": "Percentage", "min": 0, "max": 100}], "thresholds": [{"value": 80, "colorMode": "critical", "op": "gt"}]}, {"id": 5, "title": "Database Performance", "type": "graph", "gridPos": {"h": 9, "w": 12, "x": 12, "y": 17}, "targets": [{"expr": "worker:database_query_rate", "legendFormat": "Query Rate (qps)", "refId": "A"}, {"expr": "worker:database_query_duration_avg * 1000", "legendFormat": "Avg Query Time (ms)", "refId": "B"}, {"expr": "worker:database_connection_utilization * 100", "legendFormat": "Connection Pool Usage (%)", "refId": "C"}], "yAxes": [{"label": "Rate / Time / Percentage", "min": 0}]}, {"id": 6, "title": "<PERSON><PERSON>", "type": "stat", "gridPos": {"h": 8, "w": 8, "x": 0, "y": 26}, "targets": [{"expr": "worker:cache_hit_rate * 100", "legendFormat": "Hit Rate (%)", "refId": "A"}, {"expr": "worker:cache_eviction_rate", "legendFormat": "Evictions/min", "refId": "B"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 70}, {"color": "green", "value": 85}]}}}}, {"id": 7, "title": "Queue Metrics", "type": "graph", "gridPos": {"h": 8, "w": 8, "x": 8, "y": 26}, "targets": [{"expr": "worker:queue_length_avg", "legendFormat": "Queue Length", "refId": "A"}, {"expr": "worker:queue_processing_rate", "legendFormat": "Processing Rate", "refId": "B"}, {"expr": "worker:queue_wait_time_avg", "legendFormat": "Avg Wait Time (s)", "refId": "C"}]}, {"id": 8, "title": "Error Rates", "type": "graph", "gridPos": {"h": 8, "w": 8, "x": 16, "y": 26}, "targets": [{"expr": "(1 - worker:domain_processing_success_rate) * 100", "legendFormat": "Processing Error Rate (%)", "refId": "A"}, {"expr": "rate(worker_database_errors_total[5m])", "legendFormat": "Database Errors/min", "refId": "B"}, {"expr": "rate(worker_external_service_errors_total[5m])", "legendFormat": "External Service Errors/min", "refId": "C"}], "yAxes": [{"label": "Error Rate", "min": 0}], "thresholds": [{"value": 1, "colorMode": "critical", "op": "gt"}]}, {"id": 9, "title": "Performance Benchmarks vs Baseline", "type": "bargauge", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 34}, "targets": [{"expr": "(worker:domain_processing_rate / 100) * 100", "legendFormat": "Throughput vs Target", "refId": "A"}, {"expr": "(5 / histogram_quantile(0.95, worker_domain_processing_duration_seconds_bucket)) * 100", "legendFormat": "Latency vs Target", "refId": "B"}, {"expr": "worker:domain_processing_success_rate * 100", "legendFormat": "Success Rate vs Target", "refId": "C"}, {"expr": "(worker:cache_hit_rate / 0.85) * 100", "legendFormat": "<PERSON><PERSON> Hit Rate vs Target", "refId": "D"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 80}, {"color": "green", "value": 100}]}, "unit": "percent", "min": 0, "max": 150}}}, {"id": 10, "title": "Auto-Scaling Status", "type": "stat", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 34}, "targets": [{"expr": "worker_instances_current", "legendFormat": "Current Instances", "refId": "A"}, {"expr": "worker_instances_target", "legendFormat": "Target Instances", "refId": "B"}, {"expr": "increase(worker_scaling_events_total[1h])", "legendFormat": "Scaling Events (1h)", "refId": "C"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"displayMode": "list", "orientation": "horizontal"}}}}, {"id": 11, "title": "Memory Management", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 42}, "targets": [{"expr": "nodejs_heap_size_used_bytes / 1024 / 1024", "legendFormat": "Heap Used (MB)", "refId": "A"}, {"expr": "nodejs_heap_size_total_bytes / 1024 / 1024", "legendFormat": "Heap Total (MB)", "refId": "B"}, {"expr": "rate(nodejs_gc_duration_seconds_count[5m]) * 60", "legendFormat": "GC Events/min", "refId": "C"}, {"expr": "nodejs_external_memory_bytes / 1024 / 1024", "legendFormat": "External Memory (MB)", "refId": "D"}], "yAxes": [{"label": "Memory (MB) / Events", "min": 0}]}, {"id": 12, "title": "Network and I/O Performance", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 42}, "targets": [{"expr": "rate(node_network_receive_bytes_total[5m]) / 1024 / 1024", "legendFormat": "Network In (MB/s)", "refId": "A"}, {"expr": "rate(node_network_transmit_bytes_total[5m]) / 1024 / 1024", "legendFormat": "Network Out (MB/s)", "refId": "B"}, {"expr": "rate(node_disk_read_bytes_total[5m]) / 1024 / 1024", "legendFormat": "Disk Read (MB/s)", "refId": "C"}, {"expr": "rate(node_disk_written_bytes_total[5m]) / 1024 / 1024", "legendFormat": "Disk Write (MB/s)", "refId": "D"}], "yAxes": [{"label": "Throughput (MB/s)", "min": 0}]}, {"id": 13, "title": "Production Readiness Score", "type": "gauge", "gridPos": {"h": 8, "w": 8, "x": 0, "y": 50}, "targets": [{"expr": "worker_production_readiness_score", "legendFormat": "Readiness Score", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 70}, {"color": "green", "value": 90}]}, "unit": "percent", "min": 0, "max": 100}}}, {"id": 14, "title": "Optimization Impact", "type": "table", "gridPos": {"h": 8, "w": 16, "x": 8, "y": 50}, "targets": [{"expr": "worker_optimization_improvement_percent", "legendFormat": "{{category}}", "refId": "A", "format": "table"}], "fieldConfig": {"defaults": {"custom": {"displayMode": "color-background", "align": "center"}, "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 0}, {"color": "green", "value": 10}]}, "unit": "percent"}}, "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "__name__": true, "job": true, "instance": true}, "indexByName": {}, "renameByName": {"category": "Optimization Category", "Value": "Improvement %"}}}]}], "templating": {"list": [{"name": "instance", "type": "query", "query": "label_values(up{job=\"worker-service\"}, instance)", "refresh": 1, "includeAll": true, "allValue": ".*"}, {"name": "time_range", "type": "interval", "query": "1m,5m,15m,30m,1h,6h,12h,1d", "current": {"text": "5m", "value": "5m"}}]}, "annotations": {"list": [{"name": "Deployments", "datasource": "prometheus", "expr": "worker_deployment_timestamp", "titleFormat": "Deployment", "textFormat": "Version: {{version}}", "iconColor": "blue"}, {"name": "Scaling Events", "datasource": "prometheus", "expr": "increase(worker_scaling_events_total[1m]) > 0", "titleFormat": "Auto-scaling", "textFormat": "Scaled to {{instances}} instances", "iconColor": "green"}, {"name": "Optimization Events", "datasource": "prometheus", "expr": "worker_optimization_timestamp", "titleFormat": "Performance Optimization", "textFormat": "{{type}} optimization completed", "iconColor": "purple"}]}}}