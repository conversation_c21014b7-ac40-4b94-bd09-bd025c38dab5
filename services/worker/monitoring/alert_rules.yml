# Alert Rules for Worker Service Performance Monitoring

groups:
  # Critical Performance Alerts
  - name: worker_service_critical
    rules:
      - alert: WorkerServiceDown
        expr: up{job="worker-service"} == 0
        for: 1m
        labels:
          severity: critical
          service: worker-service
        annotations:
          summary: "Worker service is down"
          description: "Worker service has been down for more than 1 minute"
          runbook_url: "https://docs.company.com/runbooks/worker-service-down"

      - alert: HighErrorRate
        expr: (1 - worker:domain_processing_success_rate) * 100 > 5
        for: 5m
        labels:
          severity: critical
          service: worker-service
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }}% for more than 5 minutes"
          runbook_url: "https://docs.company.com/runbooks/high-error-rate"

      - alert: DatabaseConnectionFailure
        expr: worker_database_connections_failed_total > 0
        for: 2m
        labels:
          severity: critical
          service: worker-service
        annotations:
          summary: "Database connection failures detected"
          description: "{{ $value }} database connection failures in the last 2 minutes"
          runbook_url: "https://docs.company.com/runbooks/database-connection-failure"

      - alert: MemoryLeakDetected
        expr: increase(process_resident_memory_bytes[1h]) > *********  # 100MB
        for: 30m
        labels:
          severity: critical
          service: worker-service
        annotations:
          summary: "Memory leak detected"
          description: "Memory usage increased by {{ $value | humanizeBytes }} in the last hour"
          runbook_url: "https://docs.company.com/runbooks/memory-leak"

      - alert: QueueBacklogCritical
        expr: worker:queue_length_avg > 5000
        for: 10m
        labels:
          severity: critical
          service: worker-service
        annotations:
          summary: "Critical queue backlog"
          description: "Queue length is {{ $value }} for more than 10 minutes"
          runbook_url: "https://docs.company.com/runbooks/queue-backlog"

  # High Severity Performance Alerts
  - name: worker_service_high
    rules:
      - alert: HighLatency
        expr: histogram_quantile(0.95, worker_domain_processing_duration_seconds_bucket) > 5
        for: 10m
        labels:
          severity: high
          service: worker-service
        annotations:
          summary: "High processing latency"
          description: "95th percentile latency is {{ $value }}s for more than 10 minutes"
          runbook_url: "https://docs.company.com/runbooks/high-latency"

      - alert: LowThroughput
        expr: worker:domain_processing_rate < 50
        for: 15m
        labels:
          severity: high
          service: worker-service
        annotations:
          summary: "Low processing throughput"
          description: "Processing rate is {{ $value }} domains/min for more than 15 minutes"
          runbook_url: "https://docs.company.com/runbooks/low-throughput"

      - alert: HighCPUUsage
        expr: rate(process_cpu_seconds_total[5m]) * 100 > 85
        for: 10m
        labels:
          severity: high
          service: worker-service
        annotations:
          summary: "High CPU usage"
          description: "CPU usage is {{ $value }}% for more than 10 minutes"
          runbook_url: "https://docs.company.com/runbooks/high-cpu"

      - alert: HighMemoryUsage
        expr: worker:memory_utilization > 90
        for: 5m
        labels:
          severity: high
          service: worker-service
        annotations:
          summary: "High memory usage"
          description: "Memory usage is {{ $value }}% for more than 5 minutes"
          runbook_url: "https://docs.company.com/runbooks/high-memory"

      - alert: DatabaseSlowQueries
        expr: worker:database_query_duration_avg > 0.5
        for: 10m
        labels:
          severity: high
          service: worker-service
        annotations:
          summary: "Slow database queries detected"
          description: "Average query time is {{ $value }}s for more than 10 minutes"
          runbook_url: "https://docs.company.com/runbooks/slow-queries"

      - alert: LowCacheHitRate
        expr: worker:cache_hit_rate * 100 < 70
        for: 15m
        labels:
          severity: high
          service: worker-service
        annotations:
          summary: "Low cache hit rate"
          description: "Cache hit rate is {{ $value }}% for more than 15 minutes"
          runbook_url: "https://docs.company.com/runbooks/low-cache-hit-rate"

  # Medium Severity Performance Alerts
  - name: worker_service_medium
    rules:
      - alert: ModerateLatencyIncrease
        expr: histogram_quantile(0.95, worker_domain_processing_duration_seconds_bucket) > 3
        for: 20m
        labels:
          severity: medium
          service: worker-service
        annotations:
          summary: "Moderate latency increase"
          description: "95th percentile latency is {{ $value }}s for more than 20 minutes"
          runbook_url: "https://docs.company.com/runbooks/moderate-latency"

      - alert: QueueLengthIncreasing
        expr: worker:queue_length_avg > 1000
        for: 20m
        labels:
          severity: medium
          service: worker-service
        annotations:
          summary: "Queue length increasing"
          description: "Queue length is {{ $value }} for more than 20 minutes"
          runbook_url: "https://docs.company.com/runbooks/queue-length-increase"

      - alert: HighGCFrequency
        expr: worker:gc_rate > 20
        for: 15m
        labels:
          severity: medium
          service: worker-service
        annotations:
          summary: "High garbage collection frequency"
          description: "GC rate is {{ $value }} per minute for more than 15 minutes"
          runbook_url: "https://docs.company.com/runbooks/high-gc-frequency"

      - alert: DatabaseConnectionPoolExhaustion
        expr: worker:database_connection_utilization > 90
        for: 10m
        labels:
          severity: medium
          service: worker-service
        annotations:
          summary: "Database connection pool near exhaustion"
          description: "Connection pool utilization is {{ $value }}% for more than 10 minutes"
          runbook_url: "https://docs.company.com/runbooks/connection-pool-exhaustion"

      - alert: HighCacheEvictionRate
        expr: worker:cache_eviction_rate > 100
        for: 15m
        labels:
          severity: medium
          service: worker-service
        annotations:
          summary: "High cache eviction rate"
          description: "Cache eviction rate is {{ $value }} per minute for more than 15 minutes"
          runbook_url: "https://docs.company.com/runbooks/high-cache-eviction"

  # Capacity and Scaling Alerts
  - name: worker_service_capacity
    rules:
      - alert: AutoScalingTriggered
        expr: increase(worker_scaling_events_total[5m]) > 0
        for: 0m
        labels:
          severity: info
          service: worker-service
        annotations:
          summary: "Auto-scaling event triggered"
          description: "{{ $value }} scaling events in the last 5 minutes"
          runbook_url: "https://docs.company.com/runbooks/auto-scaling"

      - alert: MaxInstancesReached
        expr: worker_instances_current >= worker_instances_max
        for: 5m
        labels:
          severity: high
          service: worker-service
        annotations:
          summary: "Maximum instances reached"
          description: "Running at maximum capacity with {{ $value }} instances"
          runbook_url: "https://docs.company.com/runbooks/max-instances"

      - alert: FrequentScaling
        expr: increase(worker_scaling_events_total[1h]) > 10
        for: 0m
        labels:
          severity: medium
          service: worker-service
        annotations:
          summary: "Frequent scaling events"
          description: "{{ $value }} scaling events in the last hour"
          runbook_url: "https://docs.company.com/runbooks/frequent-scaling"

      - alert: ScalingCooldownViolation
        expr: worker_scaling_cooldown_violations_total > 0
        for: 1m
        labels:
          severity: medium
          service: worker-service
        annotations:
          summary: "Scaling cooldown violations detected"
          description: "{{ $value }} cooldown violations detected"
          runbook_url: "https://docs.company.com/runbooks/scaling-cooldown"

  # Business Logic Alerts
  - name: worker_service_business
    rules:
      - alert: DomainProcessingStalled
        expr: rate(worker_domains_processed_total[10m]) == 0
        for: 10m
        labels:
          severity: high
          service: worker-service
        annotations:
          summary: "Domain processing has stalled"
          description: "No domains processed in the last 10 minutes"
          runbook_url: "https://docs.company.com/runbooks/processing-stalled"

      - alert: HighDomainFailureRate
        expr: rate(worker_domains_failed_total[5m]) / rate(worker_domains_processed_total[5m]) > 0.1
        for: 10m
        labels:
          severity: high
          service: worker-service
        annotations:
          summary: "High domain processing failure rate"
          description: "{{ $value | humanizePercentage }} of domains are failing"
          runbook_url: "https://docs.company.com/runbooks/high-failure-rate"

      - alert: CrawlingServiceUnavailable
        expr: worker_external_service_availability{service="worker"} < 0.95
        for: 5m
        labels:
          severity: high
          service: worker-service
        annotations:
          summary: "Crawling service availability low"
          description: "Crawler service availability is {{ $value | humanizePercentage }}"
          runbook_url: "https://docs.company.com/runbooks/crawler-unavailable"

      - alert: RankingServiceUnavailable
        expr: worker_external_service_availability{service="ranking"} < 0.95
        for: 5m
        labels:
          severity: high
          service: worker-service
        annotations:
          summary: "Ranking service availability low"
          description: "Ranking service availability is {{ $value | humanizePercentage }}"
          runbook_url: "https://docs.company.com/runbooks/ranking-unavailable"

  # Data Quality Alerts
  - name: worker_service_data_quality
    rules:
      - alert: IncompleteDataProcessing
        expr: worker_data_completeness_score < 0.9
        for: 15m
        labels:
          severity: medium
          service: worker-service
        annotations:
          summary: "Data processing completeness low"
          description: "Data completeness score is {{ $value | humanizePercentage }}"
          runbook_url: "https://docs.company.com/runbooks/incomplete-data"

      - alert: DataValidationFailures
        expr: rate(worker_data_validation_failures_total[5m]) > 10
        for: 10m
        labels:
          severity: medium
          service: worker-service
        annotations:
          summary: "High data validation failure rate"
          description: "{{ $value }} validation failures per minute"
          runbook_url: "https://docs.company.com/runbooks/validation-failures"

      - alert: StaleDataDetected
        expr: worker_data_staleness_hours > 24
        for: 30m
        labels:
          severity: medium
          service: worker-service
        annotations:
          summary: "Stale data detected"
          description: "Data is {{ $value }} hours old"
          runbook_url: "https://docs.company.com/runbooks/stale-data"

  # Security Alerts
  - name: worker_service_security
    rules:
      - alert: UnauthorizedAccess
        expr: increase(worker_unauthorized_requests_total[5m]) > 10
        for: 2m
        labels:
          severity: high
          service: worker-service
        annotations:
          summary: "Unauthorized access attempts detected"
          description: "{{ $value }} unauthorized requests in 5 minutes"
          runbook_url: "https://docs.company.com/runbooks/unauthorized-access"

      - alert: SecurityVulnerabilityDetected
        expr: worker_security_vulnerabilities_total > 0
        for: 1m
        labels:
          severity: critical
          service: worker-service
        annotations:
          summary: "Security vulnerabilities detected"
          description: "{{ $value }} security vulnerabilities found"
          runbook_url: "https://docs.company.com/runbooks/security-vulnerability"

      - alert: SuspiciousActivity
        expr: worker_suspicious_activity_score > 0.8
        for: 5m
        labels:
          severity: high
          service: worker-service
        annotations:
          summary: "Suspicious activity detected"
          description: "Suspicious activity score is {{ $value }}"
          runbook_url: "https://docs.company.com/runbooks/suspicious-activity"

  # Infrastructure Alerts
  - name: worker_service_infrastructure
    rules:
      - alert: DiskSpaceLow
        expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) * 100 < 10
        for: 5m
        labels:
          severity: high
          service: worker-service
        annotations:
          summary: "Low disk space"
          description: "Disk space is {{ $value }}% available"
          runbook_url: "https://docs.company.com/runbooks/low-disk-space"

      - alert: NetworkLatencyHigh
        expr: worker_network_latency_seconds > 0.1
        for: 10m
        labels:
          severity: medium
          service: worker-service
        annotations:
          summary: "High network latency"
          description: "Network latency is {{ $value }}s"
          runbook_url: "https://docs.company.com/runbooks/high-network-latency"

      - alert: FileDescriptorExhaustion
        expr: process_open_fds / process_max_fds > 0.9
        for: 5m
        labels:
          severity: high
          service: worker-service
        annotations:
          summary: "File descriptor exhaustion"
          description: "{{ $value | humanizePercentage }} of file descriptors in use"
          runbook_url: "https://docs.company.com/runbooks/fd-exhaustion"
