{"dashboard": {"id": null, "title": "Worker Service - Production Dashboard", "tags": ["worker", "domainr", "production"], "style": "dark", "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "Service Health Overview", "type": "stat", "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}, "targets": [{"expr": "up{job=\"worker-service\"}", "legendFormat": "Instances Up", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 1}, {"color": "green", "value": 3}]}, "unit": "short"}}}, {"id": 2, "title": "Active Jobs", "type": "stat", "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0}, "targets": [{"expr": "sum(worker_jobs_active)", "legendFormat": "Active Jobs", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 50}, {"color": "red", "value": 100}]}, "unit": "short"}}}, {"id": 3, "title": "Queue Length", "type": "stat", "gridPos": {"h": 8, "w": 6, "x": 12, "y": 0}, "targets": [{"expr": "worker_queue_length", "legendFormat": "Queue Length", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 500}, {"color": "red", "value": 1000}]}, "unit": "short"}}}, {"id": 4, "title": "Success Rate", "type": "stat", "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}, "targets": [{"expr": "rate(worker_jobs_completed_total[5m]) / (rate(worker_jobs_completed_total[5m]) + rate(worker_jobs_failed_total[5m])) * 100", "legendFormat": "Success Rate %", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 90}, {"color": "green", "value": 95}]}, "unit": "percent", "min": 0, "max": 100}}}, {"id": 5, "title": "Job Processing Rate", "type": "graph", "gridPos": {"h": 9, "w": 12, "x": 0, "y": 8}, "targets": [{"expr": "rate(worker_jobs_completed_total[5m])", "legendFormat": "Jobs Completed/sec - {{instance}}", "refId": "A"}, {"expr": "rate(worker_jobs_failed_total[5m])", "legendFormat": "Jobs Failed/sec - {{instance}}", "refId": "B"}, {"expr": "rate(worker_jobs_total[5m])", "legendFormat": "Total Jobs/sec - {{instance}}", "refId": "C"}], "yAxes": [{"label": "Jobs/sec", "min": 0}, {"show": false}], "legend": {"show": true, "values": true, "current": true}}, {"id": 6, "title": "Resource Usage", "type": "graph", "gridPos": {"h": 9, "w": 12, "x": 12, "y": 8}, "targets": [{"expr": "rate(container_cpu_usage_seconds_total{pod=~\"worker-service-.*\"}[5m]) * 100", "legendFormat": "CPU Usage % - {{pod}}", "refId": "A"}, {"expr": "container_memory_usage_bytes{pod=~\"worker-service-.*\"} / 1024 / 1024 / 1024", "legendFormat": "Memory Usage GB - {{pod}}", "refId": "B"}], "yAxes": [{"label": "CPU % / Memory GB", "min": 0}, {"show": false}]}, {"id": 7, "title": "Database Operations", "type": "graph", "gridPos": {"h": 9, "w": 12, "x": 0, "y": 17}, "targets": [{"expr": "rate(worker_database_operations_total{database=\"scylla\"}[5m])", "legendFormat": "ScyllaDB Ops/sec", "refId": "A"}, {"expr": "rate(worker_database_operations_total{database=\"mariadb\"}[5m])", "legendFormat": "MariaDB Ops/sec", "refId": "B"}, {"expr": "rate(worker_database_operations_total{database=\"redis\"}[5m])", "legendFormat": "Redis Ops/sec", "refId": "C"}, {"expr": "rate(worker_database_operations_total{database=\"manticore\"}[5m])", "legendFormat": "Manticore Ops/sec", "refId": "D"}], "yAxes": [{"label": "Operations/sec", "min": 0}, {"show": false}]}, {"id": 8, "title": "Error Rates", "type": "graph", "gridPos": {"h": 9, "w": 12, "x": 12, "y": 17}, "targets": [{"expr": "rate(worker_errors_total{type=\"worker\"}[5m])", "legendFormat": "Crawler Errors/sec", "refId": "A"}, {"expr": "rate(worker_errors_total{type=\"ranking\"}[5m])", "legendFormat": "Ranking Errors/sec", "refId": "B"}, {"expr": "rate(worker_errors_total{type=\"indexing\"}[5m])", "legendFormat": "Indexing Errors/sec", "refId": "C"}, {"expr": "rate(worker_database_connection_errors_total[5m])", "legendFormat": "Database Errors/sec", "refId": "D"}], "yAxes": [{"label": "Errors/sec", "min": 0}, {"show": false}]}], "templating": {"list": [{"name": "instance", "type": "query", "query": "label_values(up{job=\"worker-service\"}, instance)", "refresh": 1, "includeAll": true, "allValue": ".*"}]}, "annotations": {"list": [{"name": "Deployments", "datasource": "Prometheus", "expr": "changes(worker_build_info[1h]) > 0", "titleFormat": "Deployment", "textFormat": "New version deployed"}]}}}