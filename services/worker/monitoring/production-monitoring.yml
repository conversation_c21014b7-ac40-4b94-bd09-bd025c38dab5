# Production Monitoring Configuration for Worker Service
# This configuration defines comprehensive monitoring, alerting, and performance dashboards

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    service: 'worker-service'
    environment: 'production'

# Alerting rules configuration
rule_files:
  - "alert_rules.yml"
  - "performance_rules.yml"
  - "capacity_rules.yml"

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# Scrape configurations
scrape_configs:
  # Worker service metrics
  - job_name: 'worker-service'
    static_configs:
      - targets: ['worker:3000']
    metrics_path: '/metrics'
    scrape_interval: 10s
    scrape_timeout: 5s

  # Database metrics
  - job_name: 'scylla'
    static_configs:
      - targets: ['scylla:9180']
    scrape_interval: 30s

  - job_name: 'mariadb'
    static_configs:
      - targets: ['mariadb:9104']
    scrape_interval: 30s

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:9121']
    scrape_interval: 15s

  - job_name: 'manticore'
    static_configs:
      - targets: ['manticore:9308']
    scrape_interval: 30s

  # System metrics
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 15s

  # Container metrics
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 15s

# Remote write configuration for long-term storage
remote_write:
  - url: "http://prometheus-remote-storage:9201/write"
    queue_config:
      max_samples_per_send: 10000
      max_shards: 200
      capacity: 20000

# Recording rules for performance optimization
recording_rules:
  # Domain processing performance
  - name: domain_processing_performance
    rules:
      - record: worker:domain_processing_rate
        expr: rate(worker_domains_processed_total[5m])

      - record: worker:domain_processing_duration_avg
        expr: rate(worker_domain_processing_duration_seconds_sum[5m]) / rate(worker_domain_processing_duration_seconds_count[5m])

      - record: worker:domain_processing_success_rate
        expr: rate(worker_domains_processed_success_total[5m]) / rate(worker_domains_processed_total[5m])

  # Database performance
  - name: database_performance
    rules:
      - record: worker:database_query_rate
        expr: rate(worker_database_queries_total[5m])

      - record: worker:database_query_duration_avg
        expr: rate(worker_database_query_duration_seconds_sum[5m]) / rate(worker_database_query_duration_seconds_count[5m])

      - record: worker:database_connection_utilization
        expr: worker_database_connections_active / worker_database_connections_max

  # Memory performance
  - name: memory_performance
    rules:
      - record: worker:memory_utilization
        expr: (process_resident_memory_bytes / worker_memory_limit_bytes) * 100

      - record: worker:heap_utilization
        expr: (nodejs_heap_size_used_bytes / nodejs_heap_size_total_bytes) * 100

      - record: worker:gc_rate
        expr: rate(nodejs_gc_duration_seconds_count[5m])

  # Cache performance
  - name: cache_performance
    rules:
      - record: worker:cache_hit_rate
        expr: rate(worker_cache_hits_total[5m]) / (rate(worker_cache_hits_total[5m]) + rate(worker_cache_misses_total[5m]))

      - record: worker:cache_eviction_rate
        expr: rate(worker_cache_evictions_total[5m])

  # Queue performance
  - name: queue_performance
    rules:
      - record: worker:queue_processing_rate
        expr: rate(worker_queue_jobs_processed_total[5m])

      - record: worker:queue_length_avg
        expr: avg_over_time(worker_queue_length[5m])

      - record: worker:queue_wait_time_avg
        expr: rate(worker_queue_wait_time_seconds_sum[5m]) / rate(worker_queue_wait_time_seconds_count[5m])

# Performance thresholds and SLOs
performance_targets:
  domain_processing:
    throughput_target: 100  # domains per minute
    latency_p95_target: 5000  # milliseconds
    success_rate_target: 99.5  # percentage

  database:
    query_latency_p95_target: 100  # milliseconds
    connection_utilization_target: 80  # percentage
    transaction_success_rate_target: 99.9  # percentage

  memory:
    heap_utilization_target: 80  # percentage
    gc_frequency_target: 10  # per minute
    memory_leak_threshold: 100  # MB growth per hour

  cache:
    hit_rate_target: 85  # percentage
    eviction_rate_threshold: 100  # per minute

  queue:
    processing_rate_target: 50  # jobs per minute
    queue_length_threshold: 1000  # jobs
    wait_time_p95_target: 30000  # milliseconds

# Capacity planning thresholds
capacity_thresholds:
  cpu_utilization_warning: 70
  cpu_utilization_critical: 85
  memory_utilization_warning: 80
  memory_utilization_critical: 90
  disk_utilization_warning: 80
  disk_utilization_critical: 90
  network_utilization_warning: 70
  network_utilization_critical: 85

# Auto-scaling configuration
auto_scaling:
  enabled: true
  min_instances: 2
  max_instances: 20
  target_cpu_utilization: 70
  target_memory_utilization: 75
  scale_up_cooldown: 300  # seconds
  scale_down_cooldown: 600  # seconds

  # Custom metrics for scaling
  custom_metrics:
    - name: queue_length
      target_value: 500
      weight: 0.3

    - name: domain_processing_rate
      target_value: 80
      weight: 0.4

    - name: response_time_p95
      target_value: 2000
      weight: 0.3

# Dashboard configuration
dashboards:
  - name: "Worker Service Overview"
    panels:
      - title: "Domain Processing Rate"
        type: "graph"
        targets:
          - expr: "worker:domain_processing_rate"
          - expr: "worker:domain_processing_success_rate"

      - title: "Response Time Distribution"
        type: "heatmap"
        targets:
          - expr: "worker_domain_processing_duration_seconds_bucket"

      - title: "Resource Utilization"
        type: "stat"
        targets:
          - expr: "worker:memory_utilization"
          - expr: "rate(process_cpu_seconds_total[5m]) * 100"

      - title: "Database Performance"
        type: "graph"
        targets:
          - expr: "worker:database_query_rate"
          - expr: "worker:database_query_duration_avg"

      - title: "Cache Performance"
        type: "stat"
        targets:
          - expr: "worker:cache_hit_rate * 100"
          - expr: "worker:cache_eviction_rate"

  - name: "Performance Optimization"
    panels:
      - title: "Throughput vs Baseline"
        type: "graph"
        targets:
          - expr: "worker:domain_processing_rate"
          - expr: "100"  # baseline target

      - title: "Latency Percentiles"
        type: "graph"
        targets:
          - expr: "histogram_quantile(0.50, worker_domain_processing_duration_seconds_bucket)"
          - expr: "histogram_quantile(0.95, worker_domain_processing_duration_seconds_bucket)"
          - expr: "histogram_quantile(0.99, worker_domain_processing_duration_seconds_bucket)"

      - title: "Error Rate"
        type: "stat"
        targets:
          - expr: "(1 - worker:domain_processing_success_rate) * 100"

      - title: "Resource Efficiency"
        type: "graph"
        targets:
          - expr: "worker:domain_processing_rate / (rate(process_cpu_seconds_total[5m]) * 100)"
          - expr: "worker:domain_processing_rate / (worker:memory_utilization / 100)"

  - name: "Capacity Planning"
    panels:
      - title: "Current vs Target Capacity"
        type: "gauge"
        targets:
          - expr: "worker_instances_current"
          - expr: "worker_instances_target"

      - title: "Queue Length Trend"
        type: "graph"
        targets:
          - expr: "worker:queue_length_avg"
          - expr: "1000"  # threshold

      - title: "Scaling Events"
        type: "table"
        targets:
          - expr: "increase(worker_scaling_events_total[1h])"

      - title: "Resource Forecast"
        type: "graph"
        targets:
          - expr: "predict_linear(worker:memory_utilization[1h], 3600)"
          - expr: "predict_linear(rate(process_cpu_seconds_total[5m])[1h], 3600)"

# Notification channels
notification_channels:
  - name: "slack-alerts"
    type: "slack"
    settings:
      webhook_url: "${SLACK_WEBHOOK_URL}"
      channel: "#worker-service-alerts"
      title: "Worker Service Alert"
      text: "{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}"

  - name: "email-critical"
    type: "email"
    settings:
      addresses: ["<EMAIL>", "<EMAIL>"]
      subject: "CRITICAL: Worker Service Alert"

  - name: "pagerduty-critical"
    type: "pagerduty"
    settings:
      integration_key: "${PAGERDUTY_INTEGRATION_KEY}"
      severity: "critical"

# Log aggregation configuration
logging:
  level: "info"
  format: "json"

  # Log shipping configuration
  shipping:
    enabled: true
    endpoint: "http://elasticsearch:9200"
    index_pattern: "worker-service-logs-%{+YYYY.MM.dd}"

  # Log retention
  retention:
    debug: "1d"
    info: "7d"
    warn: "30d"
    error: "90d"

  # Structured logging fields
  fields:
    service: "worker-service"
    version: "${SERVICE_VERSION}"
    environment: "${ENVIRONMENT}"
    instance_id: "${INSTANCE_ID}"

# Health check configuration
health_checks:
  - name: "service_health"
    endpoint: "/health"
    interval: "30s"
    timeout: "5s"

  - name: "database_health"
    endpoint: "/health/database"
    interval: "60s"
    timeout: "10s"

  - name: "queue_health"
    endpoint: "/health/queue"
    interval: "30s"
    timeout: "5s"

# Performance testing configuration
performance_testing:
  enabled: true
  schedule: "0 2 * * *"  # Daily at 2 AM

  tests:
    - name: "throughput_test"
      type: "load"
      duration: "10m"
      concurrent_users: 50
      ramp_up: "2m"

    - name: "stress_test"
      type: "stress"
      duration: "5m"
      concurrent_users: 200
      ramp_up: "1m"

    - name: "endurance_test"
      type: "endurance"
      duration: "1h"
      concurrent_users: 20
      ramp_up: "5m"

# Backup and recovery monitoring
backup_monitoring:
  enabled: true

  checks:
    - name: "database_backup"
      schedule: "0 1 * * *"  # Daily at 1 AM
      retention: "30d"

    - name: "configuration_backup"
      schedule: "0 0 * * 0"  # Weekly on Sunday
      retention: "90d"

# Security monitoring
security_monitoring:
  enabled: true

  checks:
    - name: "vulnerability_scan"
      schedule: "0 3 * * 1"  # Weekly on Monday at 3 AM

    - name: "dependency_audit"
      schedule: "0 4 * * 1"  # Weekly on Monday at 4 AM

    - name: "security_headers"
      endpoint: "/security-headers"
      interval: "1h"
