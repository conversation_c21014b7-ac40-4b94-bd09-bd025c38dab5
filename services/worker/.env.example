# Worker Service Configuration
# Copy this file to .env and configure for your environment

# ============================================================================
# Worker Settings
# ============================================================================

# Unique identifier for this worker instance
WORKER_INSTANCE_ID=worker-1

# Maximum number of domains processed simultaneously per worker
MAX_CONCURRENT_TASKS=10

# ============================================================================
# Database Connections
# ============================================================================

# ScyllaDB cluster hosts (comma-separated)
SCYLLA_HOSTS=localhost:9042

# MariaDB connection
MARIA_HOST=localhost
MARIA_PORT=3306
MARIA_USER=root
MARIA_PASSWORD=
MARIA_DATABASE=domain_ranking

# Redis connection
REDIS_URL=redis://localhost:6379

# Manticore Search connection
MANTICORE_HOST=localhost
MANTICORE_PORT=9308

# ============================================================================
# External Services
# ============================================================================

# Browserless service for screenshots and performance audits
BROWSERLESS_URL=http://localhost:3000

# Image proxy service for image optimization
IMAGE_PROXY_URL=https://images.weserv.nl

# ============================================================================
# Processing Configuration
# ============================================================================

# Timeout for crawling operations (milliseconds)
CRAWL_TIMEOUT=30000

# Batch size for ranking updates
RANKING_UPDATE_BATCH_SIZE=100

# Number of retry attempts for failed jobs
JOB_RETRY_ATTEMPTS=3

# Delay between retry attempts (milliseconds)
JOB_RETRY_DELAY=5000

# ============================================================================
# Logging and Monitoring
# ============================================================================

# Log level (error, warn, info, debug)
LOG_LEVEL=info

# Enable metrics collection
METRICS_ENABLED=true

# Health check interval (milliseconds)
HEALTH_CHECK_INTERVAL=30000

# ============================================================================
# Development Settings
# ============================================================================

# Node environment
NODE_ENV=development

# Enable debug logging for specific modules
DEBUG=worker:*

# ============================================================================
# AI Services Configuration
# ============================================================================

# OpenAI Configuration
OPENAI_API_KEY=sk-your-openai-api-key
OPENAI_MODEL=gpt-4o-mini
OPENAI_TIMEOUT=30000
OPENAI_MAX_RETRIES=3
OPENAI_ENABLED=true

# Anthropic Configuration
ANTHROPIC_API_KEY=sk-ant-your-anthropic-api-key
ANTHROPIC_MODEL=claude-3-haiku-20240307
ANTHROPIC_TIMEOUT=30000
ANTHROPIC_MAX_RETRIES=3
ANTHROPIC_ENABLED=true

# Google AI Configuration
GOOGLE_API_KEY=your-google-ai-api-key
GOOGLE_MODEL=gemini-1.5-flash
GOOGLE_TIMEOUT=30000
GOOGLE_MAX_RETRIES=3
GOOGLE_ENABLED=true

# OpenRouter Configuration
OPENROUTER_API_KEY=sk-or-your-openrouter-api-key
OPENROUTER_MODEL=openai/gpt-4o-mini
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
OPENROUTER_TIMEOUT=30000
OPENROUTER_MAX_RETRIES=3
OPENROUTER_ENABLED=false

# AI Fallback Configuration
AI_FALLBACK_ENABLED=true
AI_FALLBACK_MAX_RETRIES=3
AI_FALLBACK_RETRY_DELAY=1000
AI_FALLBACK_ERROR_THRESHOLD=0.1

# AI Cost Tracking Configuration
AI_COST_TRACKING_ENABLED=true
AI_DAILY_BUDGET=100
AI_MONTHLY_BUDGET=2000

# AI Alert Configuration
AI_ALERTS_ENABLED=true
AI_DAILY_COST_THRESHOLD=80
AI_HOURLY_COST_THRESHOLD=10
AI_ERROR_RATE_THRESHOLD=0.1
AI_LATENCY_THRESHOLD=30000

# AI Key Rotation Configuration
AI_KEY_ROTATION_ENABLED=true
AI_KEY_ROTATION_INTERVAL=60
AI_KEY_BALANCE_USAGE=true
