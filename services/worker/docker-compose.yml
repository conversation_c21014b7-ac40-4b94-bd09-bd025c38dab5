# Docker Compose for Worker Service Local Development
version: '3.8'

services:
  worker:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: worker-service
    restart: unless-stopped
    environment:
      # Worker Configuration
      - NODE_ENV=development
      - WORKER_INSTANCE_ID=worker-dev-1
      - MAX_CONCURRENT_TASKS=5
      - HEALTH_CHECK_PORT=3000

      # Database Configuration
      - SCYLLA_HOSTS=scylla:9042
      - SCYLLA_KEYSPACE=domainr_dev
      - MARIA_HOST=mariadb
      - MARIA_PORT=3306
      - MARIA_DATABASE=domainr_dev
      - MARIA_USER=worker
      - MARIA_PASSWORD=worker_dev_password
      - REDIS_URL=redis://redis:6379/0
      - MANTICORE_HOST=manticore
      - MANTICORE_PORT=9308

      # External Services
      - BROWSERLESS_URL=http://browserless:3000
      - IMAGE_PROXY_URL=https://images.weserv.nl

      # Processing Configuration
      - CRAWL_TIMEOUT=30000
      - RANKING_UPDATE_BATCH_SIZE=50
      - JOB_RETRY_ATTEMPTS=3
      - JOB_RETRY_DELAY=5000

      # Logging
      - LOG_LEVEL=debug
      - LOG_FORMAT=json

      # AI Configuration (optional for development)
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY:-}
      - GOOGLE_AI_API_KEY=${GOOGLE_AI_API_KEY:-}

    ports:
      - "3000:3000"  # Health check port

    volumes:
      - ./logs:/app/logs
      - ./tmp:/app/tmp

    depends_on:
      - scylla
      - mariadb
      - redis
      - manticore
      - browserless

    networks:
      - worker-network

    healthcheck:
      test: ["CMD", "node", "healthcheck.js"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 512M

  # Database Services
  scylla:
    image: scylladb/scylla:6.2
    container_name: worker-scylla
    restart: unless-stopped
    command: --seeds=scylla --smp 1 --memory 1G --overprovisioned 1 --api-address 0.0.0.0
    ports:
      - "9042:9042"
    volumes:
      - scylla_data:/var/lib/scylla
      - ./database/scylla/init.cql:/docker-entrypoint-initdb.d/init.cql
    networks:
      - worker-network

  mariadb:
    image: mariadb:11.2
    container_name: worker-mariadb
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=root_password
      - MYSQL_DATABASE=domainr_dev
      - MYSQL_USER=worker
      - MYSQL_PASSWORD=worker_dev_password
    ports:
      - "3306:3306"
    volumes:
      - mariadb_data:/var/lib/mysql
      - ./database/mariadb/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - worker-network

  redis:
    image: redis:7.2-alpine
    container_name: worker-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - worker-network

  manticore:
    image: manticoresearch/manticore:6.2.12
    container_name: worker-manticore
    restart: unless-stopped
    ports:
      - "9306:9306"
      - "9308:9308"
    volumes:
      - manticore_data:/var/lib/manticore
      - ./database/manticore/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - worker-network

  browserless:
    image: browserless/chrome:latest
    container_name: worker-browserless
    restart: unless-stopped
    environment:
      - CONCURRENT=5
      - TOKEN=dev_token
      - MAX_CONCURRENT_SESSIONS=5
      - PREBOOT_CHROME=true
      - KEEP_ALIVE=true
    ports:
      - "3001:3000"
    networks:
      - worker-network

  # Monitoring Services (optional for development)
  prometheus:
    image: prom/prometheus:latest
    container_name: worker-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - worker-network
    profiles:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    container_name: worker-grafana
    restart: unless-stopped
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    ports:
      - "3002:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana:/etc/grafana/provisioning
    networks:
      - worker-network
    profiles:
      - monitoring

volumes:
  scylla_data:
  mariadb_data:
  redis_data:
  manticore_data:
  prometheus_data:
  grafana_data:

networks:
  worker-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
