{"name": "domain-ranking-worker", "version": "1.0.0", "description": "Consolidated worker service for domain processing - replaces crawler, ranking-engine, and scheduler services", "main": "src/index.ts", "bin": {"worker-service": "src/cli.ts"}, "scripts": {"start": "tsx src/index.ts", "dev": "tsx watch src/index.ts", "cli": "tsx src/cli.ts", "worker:start": "tsx src/cli.ts start", "worker:stop": "tsx src/cli.ts stop", "worker:status": "tsx src/cli.ts status", "worker:health": "tsx src/cli.ts health", "worker:metrics": "tsx src/cli.ts monitor metrics", "test": "vitest run --run", "test:watch": "vitest", "test:coverage": "vitest run --coverage --run", "test:unit": "vitest run --run src/**/__tests__/**/*.test.ts --exclude src/__tests__/integration/** --exclude src/__tests__/performance/** --exclude src/__tests__/chaos/**", "test:integration": "vitest run --run src/__tests__/integration/**/*.test.ts --testTimeout=120000", "test:performance": "vitest run --run src/__tests__/performance/**/*.test.ts --testTimeout=300000", "test:chaos": "vitest run --run src/__tests__/chaos/**/*.test.ts --testTimeout=600000", "test:database": "vitest run --run src/__tests__/database/**/*.test.ts --testTimeout=90000", "test:all": "tsx src/__tests__/run-all-tests.ts", "test:ci": "vitest run --run --coverage --reporter=junit --outputFile=test-results.xml", "build": "tsc --build", "clean": "rm -rf dist coverage .tsbuildinfo test-results test-logs"}, "dependencies": {"@ai-sdk/anthropic": "^2.0.1", "@ai-sdk/google": "^2.0.3", "@ai-sdk/openai": "^2.0.5", "ai": "^5.0.8", "cassandra-driver": "^4.7.2", "cheerio": "^1.0.0-rc.12", "commander": "^12.0.0", "cron": "^3.1.7", "dotenv": "^16.3.1", "lodash": "^4.17.21", "mysql2": "^3.6.0", "node-cron": "^3.0.2", "node-libcurl": "^4.1.0", "redis": "^4.6.8", "redis-smq": "^8.3.1", "robots-parser": "^3.0.1", "shared": "workspace:*", "ultimate-express": "^1.0.0", "user-agents": "^1.0.1274", "uuid": "^9.0.0"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/preset-env": "^7.22.9", "@types/babel__core": "^7", "@types/lodash": "^4.14.195", "@types/node": "^22.0.0", "@types/node-cron": "^3.0.8", "@types/uuid": "^9.0.0", "@vitest/coverage-v8": "^2.0.5", "@vitest/ui": "^2.0.5", "tsx": "^4.0.0", "typescript": "^5.0.0", "vite-tsconfig-paths": "^5.0.1", "vitest": "^2.0.5"}, "engines": {"node": ">=22.0.0", "pnpm": ">=10.15.0"}, "packageManager": "pnpm@10.15.0"}