# Docker Compose Override for Testing Environment
version: '3.8'

services:
  worker:
    build:
      target: builder  # Use builder stage for testing with dev dependencies
    environment:
      - NODE_ENV=test
      - WORKER_INSTANCE_ID=worker-test-1
      - MAX_CONCURRENT_TASKS=2
      - LOG_LEVEL=debug
      - TEST_MODE=true

      # Test Database Configuration
      - SCYLLA_KEYSPACE=domainr_test
      - MARIA_DATABASE=domainr_test
      - REDIS_URL=redis://redis:6379/1  # Use different Redis DB for tests

      # Reduced timeouts for faster tests
      - CRAWL_TIMEOUT=10000
      - JOB_RETRY_ATTEMPTS=1
      - HEALTH_CHECK_TIMEOUT=3000

    command: ["pnpm", "test"]

    volumes:
      - .:/app
      - /app/node_modules  # Anonymous volume to preserve node_modules
      - ./test-results:/app/test-results

    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G

  # Test-specific database configurations
  scylla:
    command: --seeds=scylla --smp 1 --memory 512M --overprovisioned 1 --api-address 0.0.0.0
    volumes:
      - ./database/scylla/test-init.cql:/docker-entrypoint-initdb.d/init.cql

  mariadb:
    environment:
      - MYSQL_DATABASE=domainr_test
    volumes:
      - ./database/mariadb/test-init.sql:/docker-entrypoint-initdb.d/init.sql

  redis:
    command: redis-server --appendonly no --maxmemory 256mb

  # Disable monitoring services in test environment
  prometheus:
    profiles:
      - disabled

  grafana:
    profiles:
      - disabled
