# Multi-stage production-ready Dockerfile for Worker Service
# Optimized for security, performance, and minimal attack surface

# Build stage
FROM node:22-alpine AS builder

# Install build dependencies
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git \
    && rm -rf /var/cache/apk/*

# Create app directory
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY pnpm-lock.yaml ./

# Install pnpm and dependencies
RUN npm install -g pnpm@10.15.0
RUN pnpm install --frozen-lockfile --production=false

# Copy source code
COPY . .

# Build the application
RUN pnpm run build

# Remove dev dependencies
RUN pnpm prune --production

# Production stage
FROM node:22-alpine AS production

# Security: Create non-root user
RUN addgroup -g 1001 -S worker && \
    adduser -S worker -u 1001 -G worker

# Install runtime dependencies and security updates
RUN apk add --no-cache \
    chromium \
    nss \
    freetype \
    freetype-dev \
    harfbuzz \
    ca-certificates \
    ttf-freefont \
    curl \
    dumb-init \
    && rm -rf /var/cache/apk/*

# Set Chromium path for Puppeteer
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

# Create app directory
WORKDIR /app

# Copy built application from builder stage
COPY --from=builder --chown=worker:worker /app/dist ./dist
COPY --from=builder --chown=worker:worker /app/node_modules ./node_modules
COPY --from=builder --chown=worker:worker /app/package.json ./package.json

# Copy health check script
COPY --chown=worker:worker healthcheck.js ./

# Create necessary directories with proper permissions
RUN mkdir -p /app/logs /app/tmp && \
    chown -R worker:worker /app

# Security hardening
RUN chmod -R 755 /app && \
    chmod 644 /app/package.json

# Switch to non-root user
USER worker

# Expose health check port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD node healthcheck.js || exit 1

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Start the application
CMD ["node", "dist/index.js"]

# Labels for metadata
LABEL maintainer="DevOps Team" \
      version="1.0.0" \
      description="Worker Service - Domain Processing Pipeline" \
      org.opencontainers.image.source="https://github.com/company/domainr" \
      org.opencontainers.image.documentation="https://docs.company.com/worker-service" \
      org.opencontainers.image.vendor="Company Name"
