#!/usr/bin/env node

/**
 * Health Check Script for Worker Service Container
 * Performs comprehensive health checks for container orchestration
 */

const http = require('http');
const process = require('process');

const HEALTH_CHECK_PORT = process.env.HEALTH_CHECK_PORT || 3000;
const HEALTH_CHECK_TIMEOUT = parseInt(process.env.HEALTH_CHECK_TIMEOUT || '5000');
const MAX_RETRIES = parseInt(process.env.HEALTH_CHECK_RETRIES || '3');

/**
 * Performs HTTP health check
 */
function performHealthCheck()
{
	return new Promise((resolve, reject) =>
	{
		const options = {
			hostname: 'localhost',
			port: HEALTH_CHECK_PORT,
			path: '/health',
			method: 'GET',
			timeout: HEALTH_CHECK_TIMEOUT,
		};

		const req = http.request(options, (res) =>
		{
			let data = '';

			res.on('data', (chunk) =>
			{
				data += chunk;
			});

			res.on('end', () =>
			{
				try
				{
					const healthData = JSON.parse(data);

					if (res.statusCode === 200 && healthData.status === 'healthy')
					{
						resolve(healthData);
					}
					else
					{
						reject(new Error(`Health check failed: ${healthData.status || 'unknown'}`));
					}
				}
				catch (error)
				{
					reject(new Error(`Invalid health check response: ${error.message}`));
				}
			});
		});

		req.on('error', (error) =>
		{
			reject(new Error(`Health check request failed: ${error.message}`));
		});

		req.on('timeout', () =>
		{
			req.destroy();
			reject(new Error('Health check timeout'));
		});

		req.end();
	});
}

/**
 * Performs health check with retries
 */
async function healthCheckWithRetries()
{
	let lastError;

	for (let attempt = 1; attempt <= MAX_RETRIES; attempt++)
	{
		try
		{
			const result = await performHealthCheck();
			console.log(`Health check passed on attempt ${attempt}:`, result);
			return true;
		}
		catch (error)
		{
			lastError = error;
			console.error(`Health check attempt ${attempt} failed:`, error.message);

			if (attempt < MAX_RETRIES)
			{
				// Wait before retry (exponential backoff)
				const delay = Math.min(1000 * 2**(attempt - 1), 5000);
				await new Promise(resolve => setTimeout(resolve, delay));
			}
		}
	}

	console.error(`All ${MAX_RETRIES} health check attempts failed. Last error:`, lastError.message);
	return false;
}

/**
 * Main health check execution
 */
async function main()
{
	try
	{
		console.log(`Starting health check for worker service on port ${HEALTH_CHECK_PORT}`);

		const isHealthy = await healthCheckWithRetries();

		if (isHealthy)
		{
			console.log('Worker service is healthy');
			process.exit(0);
		}
		else
		{
			console.error('Worker service is unhealthy');
			process.exit(1);
		}
	}
	catch (error)
	{
		console.error('Health check script error:', error.message);
		process.exit(1);
	}
}

// Handle process signals
process.on('SIGTERM', () =>
{
	console.log('Health check received SIGTERM, exiting...');
	process.exit(1);
});

process.on('SIGINT', () =>
{
	console.log('Health check received SIGINT, exiting...');
	process.exit(1);
});

// Run health check
main();
