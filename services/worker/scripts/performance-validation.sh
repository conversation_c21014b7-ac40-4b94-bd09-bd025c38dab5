#!/bin/bash

# Performance Validation Script for Worker Service
# Validates system performance meets or exceeds original three services combined performance

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
RESULTS_DIR="$PROJECT_ROOT/performance-results"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
REPORT_FILE="$RESULTS_DIR/performance_validation_$TIMESTAMP.json"

# Performance targets (baseline from original three services)
THROUGHPUT_TARGET=100  # domains per minute
LATENCY_P95_TARGET=5000  # milliseconds
SUCCESS_RATE_TARGET=99.5  # percentage
MEMORY_EFFICIENCY_TARGET=10  # MB per domain
CPU_EFFICIENCY_TARGET=5  # % CPU per domain
ERROR_RATE_TARGET=0.1  # percentage

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Create results directory
mkdir -p "$RESULTS_DIR"

# Initialize results JSON
cat > "$REPORT_FILE" << EOF
{
  "timestamp": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "validation_type": "performance",
  "baseline_targets": {
    "throughput": $THROUGHPUT_TARGET,
    "latency_p95": $LATENCY_P95_TARGET,
    "success_rate": $SUCCESS_RATE_TARGET,
    "memory_efficiency": $MEMORY_EFFICIENCY_TARGET,
    "cpu_efficiency": $CPU_EFFICIENCY_TARGET,
    "error_rate": $ERROR_RATE_TARGET
  },
  "tests": [],
  "summary": {}
}
EOF

# Function to add test result to JSON
add_test_result() {
    local test_name="$1"
    local status="$2"
    local metrics="$3"
    local recommendations="$4"

    # Create temporary file with test result
    local temp_file=$(mktemp)
    cat > "$temp_file" << EOF
{
  "name": "$test_name",
  "status": "$status",
  "timestamp": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "metrics": $metrics,
  "recommendations": $recommendations
}
EOF

    # Add to results file
    jq ".tests += [$(cat "$temp_file")]" "$REPORT_FILE" > "${REPORT_FILE}.tmp" && mv "${REPORT_FILE}.tmp" "$REPORT_FILE"
    rm "$temp_file"
}

# Function to check if service is running
check_service_health() {
    log_info "Checking worker service health..."

    local health_url="http://localhost:3000/health"
    local max_attempts=30
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        if curl -s -f "$health_url" > /dev/null 2>&1; then
            log_success "Worker service is healthy"
            return 0
        fi

        log_info "Attempt $attempt/$max_attempts: Service not ready, waiting..."
        sleep 2
        ((attempt++))
    done

    log_error "Worker service is not responding after $max_attempts attempts"
    return 1
}

# Function to run throughput test
run_throughput_test() {
    log_info "Running throughput performance test..."

    local test_duration=300  # 5 minutes
    local concurrent_workers=10
    local start_time=$(date +%s)
    local domains_processed=0
    local successful_domains=0
    local failed_domains=0

    # Create test domains list
    local test_domains=()
    for i in $(seq 1 1000); do
        test_domains+=("test-domain-$i.com")
    done

    # Function to process a single domain
    process_domain() {
        local domain="$1"
        local start_time=$(date +%s%3N)

        # Simulate domain processing API call
        local response=$(curl -s -w "%{http_code}" -X POST \
            -H "Content-Type: application/json" \
            -d "{\"domain\": \"$domain\", \"priority\": \"high\"}" \
            "http://localhost:3000/api/domains/process" 2>/dev/null || echo "000")

        local end_time=$(date +%s%3N)
        local duration=$((end_time - start_time))

        if [[ "$response" =~ 200$ ]]; then
            echo "SUCCESS,$duration"
        else
            echo "FAILED,$duration"
        fi
    }

    # Run concurrent processing
    local pids=()
    local results_file=$(mktemp)

    for worker in $(seq 1 $concurrent_workers); do
        {
            local worker_start=$(date +%s)
            while [ $(($(date +%s) - worker_start)) -lt $test_duration ]; do
                for domain in "${test_domains[@]}"; do
                    if [ $(($(date +%s) - worker_start)) -ge $test_duration ]; then
                        break
                    fi

                    result=$(process_domain "$domain")
                    echo "$result" >> "$results_file"

                    # Small delay to prevent overwhelming
                    sleep 0.1
                done
            done
        } &
        pids+=($!)
    done

    # Wait for all workers to complete
    for pid in "${pids[@]}"; do
        wait "$pid"
    done

    # Calculate results
    local total_time=$(($(date +%s) - start_time))
    domains_processed=$(wc -l < "$results_file")
    successful_domains=$(grep -c "SUCCESS" "$results_file" || echo 0)
    failed_domains=$(grep -c "FAILED" "$results_file" || echo 0)

    local throughput=$(echo "scale=2; $successful_domains * 60 / $total_time" | bc)
    local success_rate=$(echo "scale=2; $successful_domains * 100 / $domains_processed" | bc)
    local error_rate=$(echo "scale=2; $failed_domains * 100 / $domains_processed" | bc)

    # Calculate latency statistics
    local latencies=$(grep "SUCCESS" "$results_file" | cut -d',' -f2 | sort -n)
    local p95_latency=$(echo "$latencies" | awk 'BEGIN{c=0} {a[c++]=$1} END{print a[int(c*0.95)]}')
    local avg_latency=$(echo "$latencies" | awk '{sum+=$1; count++} END{print sum/count}')

    rm "$results_file"

    # Determine test status
    local status="PASS"
    local recommendations='[]'

    if (( $(echo "$throughput < $THROUGHPUT_TARGET" | bc -l) )); then
        status="FAIL"
        recommendations='["Throughput below target", "Optimize processing pipeline", "Increase concurrency"]'
    elif (( $(echo "$p95_latency > $LATENCY_P95_TARGET" | bc -l) )); then
        status="WARNING"
        recommendations='["High latency detected", "Optimize slow operations", "Review database performance"]'
    elif (( $(echo "$success_rate < $SUCCESS_RATE_TARGET" | bc -l) )); then
        status="FAIL"
        recommendations='["Low success rate", "Improve error handling", "Review system stability"]'
    fi

    local metrics=$(cat << EOF
{
  "throughput": $throughput,
  "latency_p95": $p95_latency,
  "latency_avg": $avg_latency,
  "success_rate": $success_rate,
  "error_rate": $error_rate,
  "domains_processed": $domains_processed,
  "test_duration": $total_time
}
EOF
)

    add_test_result "throughput_test" "$status" "$metrics" "$recommendations"

    log_info "Throughput test completed:"
    log_info "  - Throughput: $throughput domains/min (target: $THROUGHPUT_TARGET)"
    log_info "  - P95 Latency: $p95_latency ms (target: $LATENCY_P95_TARGET)"
    log_info "  - Success Rate: $success_rate% (target: $SUCCESS_RATE_TARGET)"
    log_info "  - Status: $status"
}

# Function to run memory efficiency test
run_memory_test() {
    log_info "Running memory efficiency test..."

    local initial_memory=$(curl -s "http://localhost:3000/metrics" | grep "process_resident_memory_bytes" | awk '{print $2}')
    local test_domains=50

    # Process test domains
    for i in $(seq 1 $test_domains); do
        curl -s -X POST \
            -H "Content-Type: application/json" \
            -d "{\"domain\": \"memory-test-$i.com\", \"priority\": \"high\"}" \
            "http://localhost:3000/api/domains/process" > /dev/null

        sleep 0.5  # Allow processing to complete
    done

    # Wait for processing to complete
    sleep 30

    local final_memory=$(curl -s "http://localhost:3000/metrics" | grep "process_resident_memory_bytes" | awk '{print $2}')
    local memory_increase=$((final_memory - initial_memory))
    local memory_per_domain=$(echo "scale=2; $memory_increase / 1024 / 1024 / $test_domains" | bc)

    # Check for memory leaks
    local heap_used=$(curl -s "http://localhost:3000/metrics" | grep "nodejs_heap_size_used_bytes" | awk '{print $2}')
    local heap_total=$(curl -s "http://localhost:3000/metrics" | grep "nodejs_heap_size_total_bytes" | awk '{print $2}')
    local heap_utilization=$(echo "scale=2; $heap_used * 100 / $heap_total" | bc)

    local status="PASS"
    local recommendations='[]'

    if (( $(echo "$memory_per_domain > $MEMORY_EFFICIENCY_TARGET" | bc -l) )); then
        status="WARNING"
        recommendations='["High memory usage per domain", "Implement object pooling", "Review for memory leaks"]'
    fi

    if (( $(echo "$heap_utilization > 80" | bc -l) )); then
        status="FAIL"
        recommendations='["High heap utilization", "Optimize memory allocation", "Implement garbage collection tuning"]'
    fi

    local metrics=$(cat << EOF
{
  "memory_per_domain_mb": $memory_per_domain,
  "heap_utilization_percent": $heap_utilization,
  "memory_increase_bytes": $memory_increase,
  "domains_tested": $test_domains
}
EOF
)

    add_test_result "memory_efficiency_test" "$status" "$metrics" "$recommendations"

    log_info "Memory test completed:"
    log_info "  - Memory per domain: $memory_per_domain MB (target: $MEMORY_EFFICIENCY_TARGET)"
    log_info "  - Heap utilization: $heap_utilization%"
    log_info "  - Status: $status"
}

# Function to run CPU efficiency test
run_cpu_test() {
    log_info "Running CPU efficiency test..."

    local initial_cpu=$(curl -s "http://localhost:3000/metrics" | grep "process_cpu_seconds_total" | awk '{print $2}')
    local start_time=$(date +%s)
    local test_domains=20

    # Process test domains
    for i in $(seq 1 $test_domains); do
        curl -s -X POST \
            -H "Content-Type: application/json" \
            -d "{\"domain\": \"cpu-test-$i.com\", \"priority\": \"high\"}" \
            "http://localhost:3000/api/domains/process" > /dev/null
    done

    # Wait for processing to complete
    sleep 60

    local final_cpu=$(curl -s "http://localhost:3000/metrics" | grep "process_cpu_seconds_total" | awk '{print $2}')
    local total_time=$(($(date +%s) - start_time))
    local cpu_increase=$(echo "$final_cpu - $initial_cpu" | bc)
    local cpu_percent=$(echo "scale=2; $cpu_increase * 100 / $total_time" | bc)
    local cpu_per_domain=$(echo "scale=2; $cpu_percent / $test_domains" | bc)

    local status="PASS"
    local recommendations='[]'

    if (( $(echo "$cpu_per_domain > $CPU_EFFICIENCY_TARGET" | bc -l) )); then
        status="WARNING"
        recommendations='["High CPU usage per domain", "Optimize algorithms", "Implement caching"]'
    fi

    if (( $(echo "$cpu_percent > 80" | bc -l) )); then
        status="FAIL"
        recommendations='["High CPU utilization", "Review computational complexity", "Optimize hot paths"]'
    fi

    local metrics=$(cat << EOF
{
  "cpu_per_domain_percent": $cpu_per_domain,
  "total_cpu_percent": $cpu_percent,
  "cpu_increase_seconds": $cpu_increase,
  "domains_tested": $test_domains,
  "test_duration": $total_time
}
EOF
)

    add_test_result "cpu_efficiency_test" "$status" "$metrics" "$recommendations"

    log_info "CPU test completed:"
    log_info "  - CPU per domain: $cpu_per_domain% (target: $CPU_EFFICIENCY_TARGET)"
    log_info "  - Total CPU usage: $cpu_percent%"
    log_info "  - Status: $status"
}

# Function to run database performance test
run_database_test() {
    log_info "Running database performance test..."

    local query_count=1000
    local start_time=$(date +%s%3N)
    local successful_queries=0
    local failed_queries=0
    local total_latency=0

    # Run database queries
    for i in $(seq 1 $query_count); do
        local query_start=$(date +%s%3N)

        # Test database query endpoint
        local response=$(curl -s -w "%{http_code}" \
            "http://localhost:3000/api/health/database" 2>/dev/null || echo "000")

        local query_end=$(date +%s%3N)
        local query_latency=$((query_end - query_start))

        if [[ "$response" =~ 200$ ]]; then
            ((successful_queries++))
            total_latency=$((total_latency + query_latency))
        else
            ((failed_queries++))
        fi

        # Small delay between queries
        sleep 0.01
    done

    local end_time=$(date +%s%3N)
    local total_time=$((end_time - start_time))
    local avg_latency=$(echo "scale=2; $total_latency / $successful_queries" | bc)
    local query_rate=$(echo "scale=2; $successful_queries * 1000 / $total_time" | bc)
    local success_rate=$(echo "scale=2; $successful_queries * 100 / $query_count" | bc)

    local status="PASS"
    local recommendations='[]'

    if (( $(echo "$avg_latency > 100" | bc -l) )); then
        status="WARNING"
        recommendations='["High database latency", "Optimize queries", "Review indexes"]'
    fi

    if (( $(echo "$success_rate < 99" | bc -l) )); then
        status="FAIL"
        recommendations='["Database connection issues", "Review connection pooling", "Check database health"]'
    fi

    local metrics=$(cat << EOF
{
  "average_latency_ms": $avg_latency,
  "query_rate_qps": $query_rate,
  "success_rate_percent": $success_rate,
  "successful_queries": $successful_queries,
  "failed_queries": $failed_queries,
  "total_queries": $query_count
}
EOF
)

    add_test_result "database_performance_test" "$status" "$metrics" "$recommendations"

    log_info "Database test completed:"
    log_info "  - Average latency: $avg_latency ms"
    log_info "  - Query rate: $query_rate qps"
    log_info "  - Success rate: $success_rate%"
    log_info "  - Status: $status"
}

# Function to run concurrent load test
run_concurrent_test() {
    log_info "Running concurrent load test..."

    local concurrent_users=50
    local test_duration=180  # 3 minutes
    local results_file=$(mktemp)

    # Function for concurrent user simulation
    simulate_user() {
        local user_id="$1"
        local user_start=$(date +%s)
        local requests=0
        local successful_requests=0

        while [ $(($(date +%s) - user_start)) -lt $test_duration ]; do
            local request_start=$(date +%s%3N)

            # Simulate various API calls
            local endpoints=("/health" "/metrics" "/api/domains/stats")
            local endpoint=${endpoints[$((RANDOM % ${#endpoints[@]}))]}

            local response=$(curl -s -w "%{http_code}" \
                "http://localhost:3000$endpoint" 2>/dev/null || echo "000")

            local request_end=$(date +%s%3N)
            local latency=$((request_end - request_start))

            ((requests++))
            if [[ "$response" =~ 200$ ]]; then
                ((successful_requests++))
            fi

            echo "$user_id,$requests,$successful_requests,$latency" >> "$results_file"

            # Random delay between requests
            sleep $(echo "scale=2; $RANDOM / 32767 * 2" | bc)
        done
    }

    # Start concurrent users
    local pids=()
    for user in $(seq 1 $concurrent_users); do
        simulate_user "$user" &
        pids+=($!)
    done

    # Wait for all users to complete
    for pid in "${pids[@]}"; do
        wait "$pid"
    done

    # Calculate results
    local total_requests=$(wc -l < "$results_file")
    local successful_requests=$(awk -F',' '{sum+=$3} END{print sum}' "$results_file")
    local avg_latency=$(awk -F',' '{sum+=$4; count++} END{print sum/count}' "$results_file")
    local success_rate=$(echo "scale=2; $successful_requests * 100 / $total_requests" | bc)
    local throughput=$(echo "scale=2; $successful_requests / $test_duration" | bc)

    rm "$results_file"

    local status="PASS"
    local recommendations='[]'

    if (( $(echo "$avg_latency > 2000" | bc -l) )); then
        status="WARNING"
        recommendations='["High latency under load", "Optimize for concurrency", "Review resource limits"]'
    fi

    if (( $(echo "$success_rate < 95" | bc -l) )); then
        status="FAIL"
        recommendations='["High failure rate under load", "Implement rate limiting", "Review error handling"]'
    fi

    local metrics=$(cat << EOF
{
  "concurrent_users": $concurrent_users,
  "total_requests": $total_requests,
  "successful_requests": $successful_requests,
  "average_latency_ms": $avg_latency,
  "success_rate_percent": $success_rate,
  "throughput_rps": $throughput,
  "test_duration": $test_duration
}
EOF
)

    add_test_result "concurrent_load_test" "$status" "$metrics" "$recommendations"

    log_info "Concurrent test completed:"
    log_info "  - Concurrent users: $concurrent_users"
    log_info "  - Total requests: $total_requests"
    log_info "  - Success rate: $success_rate%"
    log_info "  - Average latency: $avg_latency ms"
    log_info "  - Status: $status"
}

# Function to generate final summary
generate_summary() {
    log_info "Generating performance validation summary..."

    local total_tests=$(jq '.tests | length' "$REPORT_FILE")
    local passed_tests=$(jq '[.tests[] | select(.status == "PASS")] | length' "$REPORT_FILE")
    local warning_tests=$(jq '[.tests[] | select(.status == "WARNING")] | length' "$REPORT_FILE")
    local failed_tests=$(jq '[.tests[] | select(.status == "FAIL")] | length' "$REPORT_FILE")

    local overall_status="PASS"
    if [ "$failed_tests" -gt 0 ]; then
        overall_status="FAIL"
    elif [ "$warning_tests" -gt 2 ]; then
        overall_status="WARNING"
    fi

    local score=$(echo "scale=2; ($passed_tests + $warning_tests * 0.5) * 100 / $total_tests" | bc)

    # Update summary in JSON
    local summary=$(cat << EOF
{
  "overall_status": "$overall_status",
  "score": $score,
  "total_tests": $total_tests,
  "passed_tests": $passed_tests,
  "warning_tests": $warning_tests,
  "failed_tests": $failed_tests,
  "production_ready": $([ "$overall_status" = "PASS" ] && echo "true" || echo "false")
}
EOF
)

    jq ".summary = $summary" "$REPORT_FILE" > "${REPORT_FILE}.tmp" && mv "${REPORT_FILE}.tmp" "$REPORT_FILE"

    # Display summary
    echo
    log_info "=== PERFORMANCE VALIDATION SUMMARY ==="
    log_info "Overall Status: $overall_status"
    log_info "Score: $score/100"
    log_info "Tests: $passed_tests passed, $warning_tests warnings, $failed_tests failed"

    if [ "$overall_status" = "PASS" ]; then
        log_success "✓ System is PRODUCTION READY"
        log_success "✓ Performance meets or exceeds baseline targets"
    elif [ "$overall_status" = "WARNING" ]; then
        log_warning "⚠ System needs attention before production"
        log_warning "⚠ Address warning-level issues"
    else
        log_error "✗ System is NOT production ready"
        log_error "✗ Critical issues must be resolved"
    fi

    log_info "Detailed report saved to: $REPORT_FILE"
}

# Main execution
main() {
    log_info "Starting Worker Service Performance Validation"
    log_info "Timestamp: $(date)"
    log_info "Results will be saved to: $REPORT_FILE"
    echo

    # Check prerequisites
    if ! command -v curl &> /dev/null; then
        log_error "curl is required but not installed"
        exit 1
    fi

    if ! command -v jq &> /dev/null; then
        log_error "jq is required but not installed"
        exit 1
    fi

    if ! command -v bc &> /dev/null; then
        log_error "bc is required but not installed"
        exit 1
    fi

    # Check service health
    if ! check_service_health; then
        log_error "Worker service is not healthy. Cannot proceed with validation."
        exit 1
    fi

    # Run performance tests
    run_throughput_test
    run_memory_test
    run_cpu_test
    run_database_test
    run_concurrent_test

    # Generate summary
    generate_summary

    # Exit with appropriate code
    local overall_status=$(jq -r '.summary.overall_status' "$REPORT_FILE")
    if [ "$overall_status" = "PASS" ]; then
        exit 0
    elif [ "$overall_status" = "WARNING" ]; then
        exit 1
    else
        exit 2
    fi
}

# Run main function
main "$@"
