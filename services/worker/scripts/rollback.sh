#!/bin/bash

# Worker Service Rollback Script
# Provides safe rollback capabilities for failed deployments

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"

# Default values
ENVIRONMENT="${ENVIRONMENT:-staging}"
NAMESPACE="${NAMESPACE:-domainr-${ENVIRONMENT}}"
REVISION="${REVISION:-}"
DRY_RUN="${DRY_RUN:-false}"
WAIT_TIMEOUT="${WAIT_TIMEOUT:-300}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Help function
show_help() {
    cat << EOF
Worker Service Rollback Script

Usage: $0 [OPTIONS]

Options:
    -e, --environment ENVIRONMENT    Target environment (staging, production) [default: staging]
    -n, --namespace NAMESPACE        Kubernetes namespace [default: domainr-ENVIRONMENT]
    -r, --revision REVISION          Specific revision to rollback to (optional)
    -d, --dry-run                   Show what would be done without executing
    -w, --wait-timeout SECONDS      Timeout for rollback wait [default: 300]
    -h, --help                      Show this help message

Examples:
    $0 --environment production                    # Rollback to previous revision
    $0 --environment staging --revision 5          # Rollback to specific revision
    $0 --dry-run --environment production          # Show rollback plan

EOF
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--environment)
                ENVIRONMENT="$2"
                shift 2
                ;;
            -n|--namespace)
                NAMESPACE="$2"
                shift 2
                ;;
            -r|--revision)
                REVISION="$2"
                shift 2
                ;;
            -d|--dry-run)
                DRY_RUN="true"
                shift
                ;;
            -w|--wait-timeout)
                WAIT_TIMEOUT="$2"
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# Validate prerequisites
validate_prerequisites() {
    log_info "Validating prerequisites..."

    # Check kubectl
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed"
        exit 1
    fi

    # Check Kubernetes connection
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Cannot connect to Kubernetes cluster"
        exit 1
    fi

    # Check if deployment exists
    if ! kubectl get deployment worker-service --namespace="$NAMESPACE" &> /dev/null; then
        log_error "Worker service deployment not found in namespace: $NAMESPACE"
        exit 1
    fi

    log_success "Prerequisites validated"
}

# Show rollout history
show_rollout_history() {
    log_info "Deployment rollout history:"
    kubectl rollout history deployment/worker-service --namespace="$NAMESPACE"
}

# Get current revision
get_current_revision() {
    kubectl get deployment worker-service \
        --namespace="$NAMESPACE" \
        -o jsonpath='{.metadata.annotations.deployment\.kubernetes\.io/revision}'
}

# Perform rollback
perform_rollback() {
    local current_revision
    current_revision=$(get_current_revision)

    log_info "Current deployment revision: $current_revision"

    if [[ "$DRY_RUN" == "true" ]]; then
        if [[ -n "$REVISION" ]]; then
            log_info "[DRY RUN] Would rollback to revision: $REVISION"
        else
            log_info "[DRY RUN] Would rollback to previous revision"
        fi
        return 0
    fi

    # Perform rollback
    local rollback_cmd="kubectl rollout undo deployment/worker-service --namespace=$NAMESPACE"

    if [[ -n "$REVISION" ]]; then
        rollback_cmd="$rollback_cmd --to-revision=$REVISION"
        log_info "Rolling back to revision: $REVISION"
    else
        log_info "Rolling back to previous revision"
    fi

    if eval "$rollback_cmd"; then
        log_success "Rollback initiated"
    else
        log_error "Rollback failed"
        return 1
    fi
}

# Wait for rollback to complete
wait_for_rollback() {
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] Would wait for rollback to complete"
        return 0
    fi

    log_info "Waiting for rollback to complete (timeout: ${WAIT_TIMEOUT}s)..."

    if kubectl rollout status deployment/worker-service \
        --namespace="$NAMESPACE" \
        --timeout="${WAIT_TIMEOUT}s"; then
        log_success "Rollback completed successfully"
    else
        log_error "Rollback failed or timed out"
        return 1
    fi
}

# Verify rollback
verify_rollback() {
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] Would verify rollback"
        return 0
    fi

    log_info "Verifying rollback..."

    # Check pod status
    local ready_pods
    ready_pods=$(kubectl get pods -l app=worker-service \
        --namespace="$NAMESPACE" \
        --field-selector=status.phase=Running \
        --no-headers | wc -l)

    if [[ "$ready_pods" -gt 0 ]]; then
        log_success "$ready_pods worker service pods are running after rollback"
    else
        log_error "No worker service pods are running after rollback"
        return 1
    fi

    # Perform health check
    local health_check_pod
    health_check_pod=$(kubectl get pods -l app=worker-service \
        --namespace="$NAMESPACE" \
        --field-selector=status.phase=Running \
        -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || echo "")

    if [[ -n "$health_check_pod" ]]; then
        if kubectl exec "$health_check_pod" --namespace="$NAMESPACE" -- \
            curl -f http://localhost:3000/health &> /dev/null; then
            log_success "Health check passed after rollback"
        else
            log_warning "Health check failed after rollback"
        fi
    fi

    # Show new revision
    local new_revision
    new_revision=$(get_current_revision)
    log_info "New deployment revision: $new_revision"
}

# Show post-rollback status
show_rollback_status() {
    log_info "Rollback Status Summary:"
    echo "======================="
    echo "Environment: $ENVIRONMENT"
    echo "Namespace: $NAMESPACE"
    echo "Target Revision: ${REVISION:-previous}"
    echo "Dry Run: $DRY_RUN"
    echo ""

    if [[ "$DRY_RUN" != "true" ]]; then
        echo "Current Pods:"
        kubectl get pods -l app=worker-service --namespace="$NAMESPACE" || true
        echo ""

        echo "Deployment Status:"
        kubectl get deployment worker-service --namespace="$NAMESPACE" || true
    fi
}

# Main rollback function
main() {
    log_info "Starting Worker Service rollback..."
    log_info "Environment: $ENVIRONMENT"
    log_info "Namespace: $NAMESPACE"
    log_info "Target Revision: ${REVISION:-previous}"
    log_info "Dry Run: $DRY_RUN"

    # Execute rollback steps
    validate_prerequisites
    show_rollout_history
    perform_rollback
    wait_for_rollback
    verify_rollback
    show_rollback_status

    log_success "Worker Service rollback completed successfully!"
}

# Parse arguments and run main function
parse_args "$@"
main
