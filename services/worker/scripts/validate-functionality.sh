#!/bin/bash

# Worker Service Functionality Validation Script
# This script validates that all functionality from the three source services
# has been successfully migrated to the worker service

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
WORKER_URL="${WORKER_URL:-http://localhost:3000}"
REDIS_URL="${REDIS_URL:-redis://localhost:6379}"
SCYLLA_HOST="${SCYLLA_HOST:-localhost:9042}"
MARIA_HOST="${MARIA_HOST:-localhost:3306}"
MANTICORE_HOST="${MANTICORE_HOST:-localhost:9308}"

# Test results
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
    ((PASSED_TESTS++))
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $1"
    ((FAILED_TESTS++))
}

log_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# Test execution function
run_test() {
    local test_name="$1"
    local test_command="$2"

    ((TOTAL_TESTS++))
    log_info "Running test: $test_name"

    if eval "$test_command" >/dev/null 2>&1; then
        log_success "$test_name"
        return 0
    else
        log_error "$test_name"
        return 1
    fi
}

# Validation functions
validate_service_health() {
    log_info "=== Validating Service Health ==="

    run_test "Worker service is running" \
        "curl -f -s $WORKER_URL/health"

    run_test "Health endpoint returns valid JSON" \
        "curl -s $WORKER_URL/health | jq -e '.status'"

    run_test "All databases are connected" \
        "curl -s $WORKER_URL/health | jq -e '.databases.scylla.status == \"connected\"'"

    run_test "Redis connection is healthy" \
        "curl -s $WORKER_URL/health | jq -e '.databases.redis.status == \"connected\"'"

    run_test "External services are accessible" \
        "curl -s $WORKER_URL/health | jq -e '.externalServices'"
}

validate_crawler_functionality() {
    log_info "=== Validating Crawler Functionality ==="

    # Test domain analysis endpoints
    run_test "DNS analyzer is available" \
        "curl -s $WORKER_URL/health | jq -e '.components.crawler.analyzers.dns == true'"

    run_test "Robots analyzer is available" \
        "curl -s $WORKER_URL/health | jq -e '.components.crawler.analyzers.robots == true'"

    run_test "SSL analyzer is available" \
        "curl -s $WORKER_URL/health | jq -e '.components.crawler.analyzers.ssl == true'"

    run_test "Homepage analyzer is available" \
        "curl -s $WORKER_URL/health | jq -e '.components.crawler.analyzers.homepage == true'"

    run_test "Favicon collector is available" \
        "curl -s $WORKER_URL/health | jq -e '.components.crawler.analyzers.favicon == true'"

    run_test "Screenshot analyzer is available" \
        "curl -s $WORKER_URL/health | jq -e '.components.crawler.analyzers.screenshot == true'"

    run_test "Performance auditor is available" \
        "curl -s $WORKER_URL/health | jq -e '.components.crawler.analyzers.performance == true'"

    run_test "Advanced content analyzer is available" \
        "curl -s $WORKER_URL/health | jq -e '.components.crawler.analyzers.content == true'"

    # Test module registry
    run_test "Module registry is initialized" \
        "curl -s $WORKER_URL/health | jq -e '.components.crawler.moduleRegistry.status == \"ready\"'"

    run_test "Data collection orchestrator is ready" \
        "curl -s $WORKER_URL/health | jq -e '.components.crawler.orchestrator.status == \"ready\"'"

    run_test "Selective data collection service is available" \
        "curl -s $WORKER_URL/health | jq -e '.components.crawler.selectiveCollection.status == \"ready\"'"
}

validate_ranking_functionality() {
    log_info "=== Validating Ranking Functionality ==="

    # Test scoring algorithms
    run_test "Performance scorer is available" \
        "curl -s $WORKER_URL/health | jq -e '.components.ranking.scorers.performance == true'"

    run_test "Security scorer is available" \
        "curl -s $WORKER_URL/health | jq -e '.components.ranking.scorers.security == true'"

    run_test "SEO scorer is available" \
        "curl -s $WORKER_URL/health | jq -e '.components.ranking.scorers.seo == true'"

    run_test "Technical scorer is available" \
        "curl -s $WORKER_URL/health | jq -e '.components.ranking.scorers.technical == true'"

    run_test "Backlink scorer is available" \
        "curl -s $WORKER_URL/health | jq -e '.components.ranking.scorers.backlinks == true'"

    run_test "Composite ranker is available" \
        "curl -s $WORKER_URL/health | jq -e '.components.ranking.compositeRanker.status == \"ready\"'"

    run_test "Ranking update service is ready" \
        "curl -s $WORKER_URL/health | jq -e '.components.ranking.updateService.status == \"ready\"'"

    run_test "Ranking calculator is initialized" \
        "curl -s $WORKER_URL/health | jq -e '.components.ranking.calculator.status == \"ready\"'"
}

validate_scheduler_functionality() {
    log_info "=== Validating Scheduler Functionality ==="

    # Test job scheduling components
    run_test "Job scheduler is available" \
        "curl -s $WORKER_URL/health | jq -e '.components.scheduler.jobScheduler.status == \"ready\"'"

    run_test "Crawl job manager is ready" \
        "curl -s $WORKER_URL/health | jq -e '.components.scheduler.crawlJobManager.status == \"ready\"'"

    run_test "Scheduler service is initialized" \
        "curl -s $WORKER_URL/health | jq -e '.components.scheduler.schedulerService.status == \"ready\"'"

    run_test "Cron jobs are configured" \
        "curl -s $WORKER_URL/health | jq -e '.components.scheduler.cronJobs | length > 0'"

    # Test job queue integration
    run_test "Job queue consumer is active" \
        "curl -s $WORKER_URL/health | jq -e '.components.queue.consumer.status == \"active\"'"

    run_test "Job statistics collector is running" \
        "curl -s $WORKER_URL/health | jq -e '.components.queue.statisticsCollector.status == \"running\"'"
}

validate_ai_functionality() {
    log_info "=== Validating AI Functionality ==="

    # Test AI providers
    run_test "OpenAI provider is configured" \
        "curl -s $WORKER_URL/health | jq -e '.components.ai.providers.openai.configured == true'"

    run_test "Anthropic provider is configured" \
        "curl -s $WORKER_URL/health | jq -e '.components.ai.providers.anthropic.configured == true'"

    run_test "Google provider is configured" \
        "curl -s $WORKER_URL/health | jq -e '.components.ai.providers.google.configured == true'"

    # Test AI services
    run_test "Domain description generator is ready" \
        "curl -s $WORKER_URL/health | jq -e '.components.ai.descriptionGenerator.status == \"ready\"'"

    run_test "Content analyzer is available" \
        "curl -s $WORKER_URL/health | jq -e '.components.ai.contentAnalyzer.status == \"ready\"'"

    run_test "Content quality validator is ready" \
        "curl -s $WORKER_URL/health | jq -e '.components.ai.qualityValidator.status == \"ready\"'"
}

validate_database_operations() {
    log_info "=== Validating Database Operations ==="

    # Test ScyllaDB operations
    run_test "ScyllaDB connection is active" \
        "curl -s $WORKER_URL/health/databases | jq -e '.scylla.connected == true'"

    run_test "ScyllaDB keyspace is accessible" \
        "curl -s $WORKER_URL/health/databases | jq -e '.scylla.keyspace != null'"

    run_test "Domain analysis table is accessible" \
        "curl -s $WORKER_URL/health/databases | jq -e '.scylla.tables.domain_analysis == true'"

    run_test "Robots analysis table is accessible" \
        "curl -s $WORKER_URL/health/databases | jq -e '.scylla.tables.robots_analysis == true'"

    run_test "DNS analysis table is accessible" \
        "curl -s $WORKER_URL/health/databases | jq -e '.scylla.tables.dns_analysis == true'"

    run_test "SSL analysis table is accessible" \
        "curl -s $WORKER_URL/health/databases | jq -e '.scylla.tables.ssl_analysis == true'"

    run_test "Homepage analysis table is accessible" \
        "curl -s $WORKER_URL/health/databases | jq -e '.scylla.tables.homepage_analysis == true'"

    run_test "Domain rankings table is accessible" \
        "curl -s $WORKER_URL/health/databases | jq -e '.scylla.tables.domain_rankings == true'"

    # Test MariaDB operations
    run_test "MariaDB connection is active" \
        "curl -s $WORKER_URL/health/databases | jq -e '.mariadb.connected == true'"

    run_test "Backlinks table is accessible" \
        "curl -s $WORKER_URL/health/databases | jq -e '.mariadb.tables.backlinks == true'"

    # Test Redis operations
    run_test "Redis connection is active" \
        "curl -s $WORKER_URL/health/databases | jq -e '.redis.connected == true'"

    run_test "Redis job queues are accessible" \
        "curl -s $WORKER_URL/health/databases | jq -e '.redis.queues != null'"

    # Test Manticore operations
    run_test "Manticore connection is active" \
        "curl -s $WORKER_URL/health/databases | jq -e '.manticore.connected == true'"

    run_test "Search indexes are accessible" \
        "curl -s $WORKER_URL/health/databases | jq -e '.manticore.indexes != null'"
}

validate_job_processing() {
    log_info "=== Validating Job Processing ==="

    # Test job queue operations
    run_test "Job consumer is running" \
        "curl -s $WORKER_URL/metrics | grep 'job_consumer_active 1'"

    run_test "Job processing pipeline is ready" \
        "curl -s $WORKER_URL/health | jq -e '.components.pipeline.status == \"ready\"'"

    run_test "Task executor pool is initialized" \
        "curl -s $WORKER_URL/health | jq -e '.components.pipeline.executorPool.status == \"ready\"'"

    run_test "Domain lock manager is active" \
        "curl -s $WORKER_URL/health | jq -e '.components.locking.domainLockManager.status == \"active\"'"

    # Test job statistics
    run_test "Job statistics are being collected" \
        "curl -s $WORKER_URL/metrics | grep 'jobs_processed_total'"

    run_test "Job success rate is tracked" \
        "curl -s $WORKER_URL/metrics | grep 'job_success_rate'"

    run_test "Job processing time is measured" \
        "curl -s $WORKER_URL/metrics | grep 'job_processing_duration'"
}

validate_configuration() {
    log_info "=== Validating Configuration ==="

    # Test configuration management
    run_test "Configuration manager is initialized" \
        "curl -s $WORKER_URL/health | jq -e '.components.config.manager.status == \"ready\"'"

    run_test "Configuration validation is active" \
        "curl -s $WORKER_URL/health | jq -e '.components.config.validator.status == \"ready\"'"

    run_test "Environment variables are loaded" \
        "curl -s $WORKER_URL/health | jq -e '.components.config.environment.loaded == true'"

    # Test critical configuration values
    run_test "Max concurrent tasks is configured" \
        "curl -s $WORKER_URL/health | jq -e '.configuration.maxConcurrentTasks != null'"

    run_test "Database URLs are configured" \
        "curl -s $WORKER_URL/health | jq -e '.configuration.databases != null'"

    run_test "External service URLs are configured" \
        "curl -s $WORKER_URL/health | jq -e '.configuration.externalServices != null'"
}

validate_error_handling() {
    log_info "=== Validating Error Handling ==="

    # Test error handling components
    run_test "Error handler is initialized" \
        "curl -s $WORKER_URL/health | jq -e '.components.errors.handler.status == \"ready\"'"

    run_test "Retry manager is active" \
        "curl -s $WORKER_URL/health | jq -e '.components.errors.retryManager.status == \"active\"'"

    run_test "Circuit breaker is configured" \
        "curl -s $WORKER_URL/health | jq -e '.components.errors.circuitBreaker.status == \"ready\"'"

    run_test "Graceful degradation is available" \
        "curl -s $WORKER_URL/health | jq -e '.components.errors.gracefulDegradation.status == \"ready\"'"

    # Test error metrics
    run_test "Error rates are tracked" \
        "curl -s $WORKER_URL/metrics | grep 'error_rate'"

    run_test "Retry attempts are measured" \
        "curl -s $WORKER_URL/metrics | grep 'retry_attempts_total'"
}

validate_monitoring() {
    log_info "=== Validating Monitoring ==="

    # Test monitoring components
    run_test "Health service is running" \
        "curl -s $WORKER_URL/health | jq -e '.components.monitoring.healthService.status == \"running\"'"

    run_test "Metrics collector is active" \
        "curl -s $WORKER_URL/health | jq -e '.components.monitoring.metricsCollector.status == \"active\"'"

    run_test "Alerting system is ready" \
        "curl -s $WORKER_URL/health | jq -e '.components.monitoring.alertingSystem.status == \"ready\"'"

    # Test metrics endpoints
    run_test "Prometheus metrics are available" \
        "curl -s $WORKER_URL/metrics | grep -q 'worker_'"

    run_test "Health metrics are exposed" \
        "curl -s $WORKER_URL/metrics | grep 'worker_health_status'"

    run_test "Performance metrics are tracked" \
        "curl -s $WORKER_URL/metrics | grep 'worker_performance_'"
}

validate_external_services() {
    log_info "=== Validating External Services ==="

    # Test external service connectivity
    run_test "Browserless service is accessible" \
        "curl -s $WORKER_URL/health | jq -e '.externalServices.browserless.status == \"connected\"'"

    run_test "Image proxy service is accessible" \
        "curl -s $WORKER_URL/health | jq -e '.externalServices.imageProxy.status == \"connected\"'"

    # Test AI service connectivity (if configured)
    if curl -s $WORKER_URL/health | jq -e '.externalServices.openai' >/dev/null 2>&1; then
        run_test "OpenAI service is accessible" \
            "curl -s $WORKER_URL/health | jq -e '.externalServices.openai.status == \"connected\"'"
    fi

    if curl -s $WORKER_URL/health | jq -e '.externalServices.anthropic' >/dev/null 2>&1; then
        run_test "Anthropic service is accessible" \
            "curl -s $WORKER_URL/health | jq -e '.externalServices.anthropic.status == \"connected\"'"
    fi
}

validate_performance() {
    log_info "=== Validating Performance ==="

    # Test performance metrics
    run_test "Response time is within acceptable range" \
        "curl -w '%{time_total}' -s $WORKER_URL/health -o /dev/null | awk '{exit (\$1 > 5.0)}'"

    run_test "Memory usage is reasonable" \
        "curl -s $WORKER_URL/metrics | grep 'process_resident_memory_bytes' | awk '{exit (\$2 > **********)}'" # 1GB limit

    run_test "CPU usage is within limits" \
        "curl -s $WORKER_URL/metrics | grep 'process_cpu_usage_percent' | awk '{exit (\$2 > 80)}'" # 80% limit

    # Test concurrency
    run_test "Concurrent task limit is respected" \
        "curl -s $WORKER_URL/health | jq -e '.performance.activeTasks <= .configuration.maxConcurrentTasks'"

    run_test "Queue processing rate is positive" \
        "curl -s $WORKER_URL/metrics | grep 'job_processing_rate' | awk '{exit (\$2 <= 0)}'"
}

validate_security() {
    log_info "=== Validating Security ==="

    # Test security headers
    run_test "Security headers are present" \
        "curl -I -s $WORKER_URL/health | grep -q 'X-Content-Type-Options'"

    run_test "CORS headers are configured" \
        "curl -I -s $WORKER_URL/health | grep -q 'Access-Control-Allow-Origin'"

    # Test authentication (if enabled)
    if curl -s $WORKER_URL/health | jq -e '.security.authentication.enabled' >/dev/null 2>&1; then
        run_test "Authentication is properly configured" \
            "curl -s $WORKER_URL/health | jq -e '.security.authentication.status == \"active\"'"
    fi

    # Test SSL/TLS (if HTTPS is used)
    if [[ $WORKER_URL == https://* ]]; then
        run_test "SSL certificate is valid" \
            "curl -s --fail $WORKER_URL/health"
    fi
}

# Test end-to-end functionality
validate_end_to_end() {
    log_info "=== Validating End-to-End Functionality ==="

    # Test complete domain processing pipeline
    local test_domain="example.com"

    # Submit a test job (if test endpoint is available)
    if curl -s $WORKER_URL/test >/dev/null 2>&1; then
        run_test "Test job submission works" \
            "curl -X POST -s $WORKER_URL/test/submit -d '{\"domain\":\"$test_domain\",\"type\":\"test\"}' -H 'Content-Type: application/json'"

        # Wait a moment for processing
        sleep 5

        run_test "Test job processing completes" \
            "curl -s $WORKER_URL/test/status/$test_domain | jq -e '.status == \"completed\"'"
    fi

    # Test metrics collection
    run_test "Metrics are being updated" \
        "curl -s $WORKER_URL/metrics | grep 'worker_uptime_seconds' | awk '{exit (\$2 <= 0)}'"

    # Test health check responsiveness
    run_test "Health checks are responsive" \
        "timeout 10s curl -s $WORKER_URL/health >/dev/null"
}

# Generate validation report
generate_report() {
    log_info "=== Validation Report ==="
    echo
    echo "Total Tests: $TOTAL_TESTS"
    echo "Passed: $PASSED_TESTS"
    echo "Failed: $FAILED_TESTS"
    echo "Success Rate: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%"
    echo

    if [ $FAILED_TESTS -eq 0 ]; then
        log_success "All functionality validation tests passed!"
        echo
        log_info "The worker service has successfully migrated all functionality from:"
        echo "  ✓ Crawler Service"
        echo "  ✓ Ranking Engine Service"
        echo "  ✓ Scheduler Service"
        echo
        log_info "The service is ready for production deployment."
        return 0
    else
        log_error "Some validation tests failed!"
        echo
        log_warning "Please review the failed tests and resolve issues before deployment."
        return 1
    fi
}

# Main execution
main() {
    log_info "Starting Worker Service Functionality Validation"
    log_info "Worker URL: $WORKER_URL"
    echo

    # Check prerequisites
    if ! command -v curl >/dev/null 2>&1; then
        log_error "curl is required but not installed"
        exit 1
    fi

    if ! command -v jq >/dev/null 2>&1; then
        log_error "jq is required but not installed"
        exit 1
    fi

    # Wait for service to be ready
    log_info "Waiting for worker service to be ready..."
    local retries=30
    while [ $retries -gt 0 ]; do
        if curl -f -s $WORKER_URL/health >/dev/null 2>&1; then
            log_success "Worker service is ready"
            break
        fi
        ((retries--))
        sleep 2
    done

    if [ $retries -eq 0 ]; then
        log_error "Worker service is not responding after 60 seconds"
        exit 1
    fi

    # Run validation tests
    validate_service_health
    validate_crawler_functionality
    validate_ranking_functionality
    validate_scheduler_functionality
    validate_ai_functionality
    validate_database_operations
    validate_job_processing
    validate_configuration
    validate_error_handling
    validate_monitoring
    validate_external_services
    validate_performance
    validate_security
    validate_end_to_end

    # Generate final report
    generate_report
}

# Run main function
main "$@"
