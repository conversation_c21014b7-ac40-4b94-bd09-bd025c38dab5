#!/bin/bash

# Worker Service Deployment Script
# Supports multiple environments and deployment strategies

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
K8S_DIR="${PROJECT_ROOT}/k8s"

# Default values
ENVIRONMENT="${ENVIRONMENT:-staging}"
NAMESPACE="${NAMESPACE:-domainr-${ENVIRONMENT}}"
IMAGE_TAG="${IMAGE_TAG:-latest}"
DRY_RUN="${DRY_RUN:-false}"
WAIT_TIMEOUT="${WAIT_TIMEOUT:-600}"
ROLLBACK_ON_FAILURE="${ROLLBACK_ON_FAILURE:-true}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Help function
show_help() {
    cat << EOF
Worker Service Deployment Script

Usage: $0 [OPTIONS]

Options:
    -e, --environment ENVIRONMENT    Target environment (staging, production) [default: staging]
    -n, --namespace NAMESPACE        Kubernetes namespace [default: domainr-ENVIRONMENT]
    -t, --tag IMAGE_TAG             Docker image tag [default: latest]
    -d, --dry-run                   Perform dry run without applying changes
    -w, --wait-timeout SECONDS      Timeout for deployment wait [default: 600]
    --no-rollback                   Disable automatic rollback on failure
    -h, --help                      Show this help message

Examples:
    $0 --environment staging --tag v1.2.3
    $0 --environment production --tag v1.0.0 --wait-timeout 900
    $0 --dry-run --environment staging

Environment Variables:
    ENVIRONMENT                     Same as --environment
    NAMESPACE                       Same as --namespace
    IMAGE_TAG                       Same as --tag
    DRY_RUN                        Same as --dry-run (true/false)
    WAIT_TIMEOUT                   Same as --wait-timeout
    ROLLBACK_ON_FAILURE            Enable/disable rollback (true/false)

EOF
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--environment)
                ENVIRONMENT="$2"
                shift 2
                ;;
            -n|--namespace)
                NAMESPACE="$2"
                shift 2
                ;;
            -t|--tag)
                IMAGE_TAG="$2"
                shift 2
                ;;
            -d|--dry-run)
                DRY_RUN="true"
                shift
                ;;
            -w|--wait-timeout)
                WAIT_TIMEOUT="$2"
                shift 2
                ;;
            --no-rollback)
                ROLLBACK_ON_FAILURE="false"
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# Validate prerequisites
validate_prerequisites() {
    log_info "Validating prerequisites..."

    # Check required tools
    local required_tools=("kubectl" "kustomize" "docker")
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            log_error "Required tool '$tool' is not installed"
            exit 1
        fi
    done

    # Check Kubernetes connection
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Cannot connect to Kubernetes cluster"
        exit 1
    fi

    # Validate environment
    if [[ ! "$ENVIRONMENT" =~ ^(staging|production)$ ]]; then
        log_error "Invalid environment: $ENVIRONMENT. Must be 'staging' or 'production'"
        exit 1
    fi

    # Check if overlay exists
    local overlay_dir="${K8S_DIR}/overlays/${ENVIRONMENT}"
    if [[ ! -d "$overlay_dir" ]]; then
        log_error "Overlay directory not found: $overlay_dir"
        exit 1
    fi

    log_success "Prerequisites validated"
}

# Create namespace if it doesn't exist
ensure_namespace() {
    log_info "Ensuring namespace '$NAMESPACE' exists..."

    if kubectl get namespace "$NAMESPACE" &> /dev/null; then
        log_info "Namespace '$NAMESPACE' already exists"
    else
        log_info "Creating namespace '$NAMESPACE'..."
        if [[ "$DRY_RUN" == "true" ]]; then
            log_info "[DRY RUN] Would create namespace: $NAMESPACE"
        else
            kubectl create namespace "$NAMESPACE"
            log_success "Namespace '$NAMESPACE' created"
        fi
    fi
}

# Build and push Docker image
build_and_push_image() {
    log_info "Building and pushing Docker image..."

    local image_name="domainr/worker-service:${IMAGE_TAG}"

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] Would build and push image: $image_name"
        return 0
    fi

    # Build image
    log_info "Building Docker image: $image_name"
    docker build -t "$image_name" "$PROJECT_ROOT"

    # Push image
    log_info "Pushing Docker image: $image_name"
    docker push "$image_name"

    log_success "Docker image built and pushed: $image_name"
}

# Deploy using Kustomize
deploy_with_kustomize() {
    log_info "Deploying worker service to $ENVIRONMENT environment..."

    local overlay_dir="${K8S_DIR}/overlays/${ENVIRONMENT}"
    local kustomize_args=()

    # Set image tag
    kustomize_args+=("edit" "set" "image" "domainr/worker-service:${IMAGE_TAG}")

    # Change to overlay directory
    cd "$overlay_dir"

    # Update image tag in kustomization
    if [[ "$DRY_RUN" != "true" ]]; then
        kustomize "${kustomize_args[@]}"
    fi

    # Generate and apply manifests
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] Generated Kubernetes manifests:"
        kustomize build .
    else
        log_info "Applying Kubernetes manifests..."
        kustomize build . | kubectl apply -f -
        log_success "Kubernetes manifests applied"
    fi

    # Return to project root
    cd "$PROJECT_ROOT"
}

# Wait for deployment to complete
wait_for_deployment() {
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] Would wait for deployment to complete"
        return 0
    fi

    log_info "Waiting for deployment to complete (timeout: ${WAIT_TIMEOUT}s)..."

    if kubectl rollout status deployment/worker-service \
        --namespace="$NAMESPACE" \
        --timeout="${WAIT_TIMEOUT}s"; then
        log_success "Deployment completed successfully"
    else
        log_error "Deployment failed or timed out"

        if [[ "$ROLLBACK_ON_FAILURE" == "true" ]]; then
            log_warning "Rolling back deployment..."
            kubectl rollout undo deployment/worker-service --namespace="$NAMESPACE"
            log_info "Rollback initiated"
        fi

        return 1
    fi
}

# Verify deployment health
verify_deployment() {
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] Would verify deployment health"
        return 0
    fi

    log_info "Verifying deployment health..."

    # Check pod status
    local ready_pods
    ready_pods=$(kubectl get pods -l app=worker-service \
        --namespace="$NAMESPACE" \
        --field-selector=status.phase=Running \
        --no-headers | wc -l)

    if [[ "$ready_pods" -gt 0 ]]; then
        log_success "$ready_pods worker service pods are running"
    else
        log_error "No worker service pods are running"
        return 1
    fi

    # Check service endpoints
    local endpoints
    endpoints=$(kubectl get endpoints worker-service \
        --namespace="$NAMESPACE" \
        -o jsonpath='{.subsets[*].addresses[*].ip}' 2>/dev/null || echo "")

    if [[ -n "$endpoints" ]]; then
        log_success "Service endpoints are available: $endpoints"
    else
        log_warning "No service endpoints found"
    fi

    # Perform health check
    log_info "Performing health check..."
    local health_check_pod
    health_check_pod=$(kubectl get pods -l app=worker-service \
        --namespace="$NAMESPACE" \
        --field-selector=status.phase=Running \
        -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || echo "")

    if [[ -n "$health_check_pod" ]]; then
        if kubectl exec "$health_check_pod" --namespace="$NAMESPACE" -- \
            curl -f http://localhost:3000/health &> /dev/null; then
            log_success "Health check passed"
        else
            log_warning "Health check failed"
        fi
    else
        log_warning "No pods available for health check"
    fi
}

# Show deployment status
show_deployment_status() {
    log_info "Deployment Status Summary:"
    echo "=========================="
    echo "Environment: $ENVIRONMENT"
    echo "Namespace: $NAMESPACE"
    echo "Image Tag: $IMAGE_TAG"
    echo "Dry Run: $DRY_RUN"
    echo ""

    if [[ "$DRY_RUN" != "true" ]]; then
        echo "Pods:"
        kubectl get pods -l app=worker-service --namespace="$NAMESPACE" || true
        echo ""

        echo "Services:"
        kubectl get services -l app=worker-service --namespace="$NAMESPACE" || true
        echo ""

        echo "HPA Status:"
        kubectl get hpa worker-service-hpa --namespace="$NAMESPACE" || true
    fi
}

# Cleanup function
cleanup() {
    local exit_code=$?
    if [[ $exit_code -ne 0 ]]; then
        log_error "Deployment failed with exit code: $exit_code"
    fi
    exit $exit_code
}

# Main deployment function
main() {
    # Set up error handling
    trap cleanup EXIT

    log_info "Starting Worker Service deployment..."
    log_info "Environment: $ENVIRONMENT"
    log_info "Namespace: $NAMESPACE"
    log_info "Image Tag: $IMAGE_TAG"
    log_info "Dry Run: $DRY_RUN"

    # Execute deployment steps
    validate_prerequisites
    ensure_namespace
    build_and_push_image
    deploy_with_kustomize
    wait_for_deployment
    verify_deployment
    show_deployment_status

    log_success "Worker Service deployment completed successfully!"
}

# Parse arguments and run main function
parse_args "$@"
main
