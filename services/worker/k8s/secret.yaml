apiVersion: v1
kind: Secret
metadata:
  name: worker-secrets
  namespace: domainr
  labels:
    app: worker-service
    component: secrets
type: Opaque
data:
  # Database Credentials (base64 encoded)
  # Note: In production, these should be managed by external secret management
  maria-user: d29ya2Vy  # worker
  maria-password: cGFzc3dvcmQ=  # password (change in production)
  redis-url: cmVkaXM6Ly9yZWRpcy1zZXJ2aWNlOjYzNzk=  # redis://redis-service:6379

  # AI Service API Keys (base64 encoded - replace with actual keys)
  openai-api-key: ""  # Add your OpenAI API key here
  anthropic-api-key: ""  # Add your Anthropic API key here
  google-ai-api-key: ""  # Add your Google AI API key here

  # External Service Tokens
  browserless-token: ZGV2X3Rva2Vu  # dev_token (change in production)

  # Monitoring and Observability
  prometheus-token: ""  # Add if using authenticated Prometheus
  grafana-token: ""  # Add if using Grafana API

  # Security Keys
  jwt-secret: ""  # Add JWT secret for internal authentication
  encryption-key: ""  # Add encryption key for sensitive data

---
# Example of how to create secrets from files (commented out)
# apiVersion: v1
# kind: Secret
# metadata:
#   name: worker-tls-certs
#   namespace: domainr
# type: kubernetes.io/tls
# data:
#   tls.crt: |
#     # Base64 encoded TLS certificate
#   tls.key: |
#     # Base64 encoded TLS private key
