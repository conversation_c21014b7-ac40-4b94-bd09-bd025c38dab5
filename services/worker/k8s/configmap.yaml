apiVersion: v1
kind: ConfigMap
metadata:
  name: worker-config
  namespace: domainr
  labels:
    app: worker-service
    component: config
data:
  # Worker Configuration
  max-concurrent-tasks: "10"

  # Database Configuration
  scylla-hosts: "scylla-service:9042"
  scylla-keyspace: "domainr"
  maria-host: "mariadb-service"
  maria-port: "3306"
  maria-database: "domainr"
  manticore-host: "manticore-service"
  manticore-port: "9308"

  # External Services
  browserless-url: "http://browserless-service:3000"
  image-proxy-url: "https://images.weserv.nl"

  # Processing Configuration
  crawl-timeout: "30000"
  ranking-batch-size: "100"
  job-retry-attempts: "3"
  job-retry-delay: "5000"

  # Logging Configuration
  log-level: "info"

  # Health Check Configuration
  health-check-port: "3000"
  health-check-timeout: "5000"

  # Performance Tuning
  node-options: "--max-old-space-size=3072"

  # Feature Flags
  enable-ai-content-generation: "true"
  enable-advanced-analytics: "true"
  enable-performance-monitoring: "true"

  # Rate Limiting
  crawl-rate-limit: "100"
  ranking-rate-limit: "50"

  # Cache Configuration
  cache-ttl: "3600"
  cache-max-size: "1000"

  # Monitoring Configuration
  metrics-enabled: "true"
  metrics-port: "3000"
  metrics-path: "/metrics"

  # Security Configuration
  security-headers-enabled: "true"
  cors-enabled: "false"

  # Application Configuration
  app.json: |
    {
      "name": "worker-service",
      "version": "1.0.0",
      "description": "Domain Processing Worker Service",
      "environment": "production",
      "features": {
        "crawling": {
          "enabled": true,
          "modules": [
            "dns",
            "robots",
            "ssl",
            "homepage",
            "favicon",
            "screenshot",
            "performance",
            "content"
          ]
        },
        "ranking": {
          "enabled": true,
          "algorithms": [
            "performance",
            "security",
            "seo",
            "technical",
            "backlink"
          ]
        },
        "indexing": {
          "enabled": true,
          "targets": [
            "scylla",
            "maria",
            "manticore",
            "cache"
          ]
        }
      },
      "monitoring": {
        "health_checks": true,
        "metrics": true,
        "logging": true,
        "tracing": false
      }
    }

  # Crawler Configuration
  crawler.json: |
    {
      "timeout": 30000,
      "retries": 3,
      "concurrency": 5,
      "user_agent": "DomainR-Worker/1.0",
      "modules": {
        "dns": {
          "enabled": true,
          "timeout": 5000,
          "servers": ["8.8.8.8", "1.1.1.1"]
        },
        "robots": {
          "enabled": true,
          "timeout": 10000,
          "respect_crawl_delay": true
        },
        "ssl": {
          "enabled": true,
          "timeout": 10000,
          "check_chain": true
        },
        "homepage": {
          "enabled": true,
          "timeout": 15000,
          "max_size": "5MB"
        },
        "favicon": {
          "enabled": true,
          "timeout": 10000,
          "formats": ["ico", "png", "svg"]
        },
        "screenshot": {
          "enabled": true,
          "timeout": 20000,
          "viewport": {
            "width": 1920,
            "height": 1080
          }
        },
        "performance": {
          "enabled": true,
          "timeout": 30000,
          "metrics": ["fcp", "lcp", "cls", "fid"]
        }
      }
    }

  # Ranking Configuration
  ranking.json: |
    {
      "weights": {
        "performance": 0.25,
        "security": 0.20,
        "seo": 0.20,
        "technical": 0.15,
        "backlink": 0.20
      },
      "thresholds": {
        "performance": {
          "excellent": 90,
          "good": 70,
          "fair": 50,
          "poor": 0
        },
        "security": {
          "excellent": 95,
          "good": 80,
          "fair": 60,
          "poor": 0
        }
      },
      "update_frequency": "daily",
      "batch_size": 100
    }
