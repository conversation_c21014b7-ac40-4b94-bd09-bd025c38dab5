apiVersion: v1
kind: Service
metadata:
  name: worker-service
  namespace: domainr
  labels:
    app: worker-service
    component: service
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "3000"
    prometheus.io/path: "/metrics"
spec:
  type: ClusterIP
  ports:
  - name: health
    port: 3000
    targetPort: health
    protocol: TCP
  - name: metrics
    port: 9090
    targetPort: health
    protocol: TCP
  selector:
    app: worker-service
    component: worker
  sessionAffinity: None

---
apiVersion: v1
kind: Service
metadata:
  name: worker-service-headless
  namespace: domainr
  labels:
    app: worker-service
    component: service-headless
spec:
  type: ClusterIP
  clusterIP: None
  ports:
  - name: health
    port: 3000
    targetPort: health
    protocol: TCP
  selector:
    app: worker-service
    component: worker
  publishNotReadyAddresses: true
