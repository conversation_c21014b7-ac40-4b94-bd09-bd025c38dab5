apiVersion: v1
kind: ConfigMap
metadata:
  name: worker-config
data:
  # Production-optimized settings
  max-concurrent-tasks: "10"
  log-level: "info"
  crawl-timeout: "30000"
  ranking-batch-size: "100"
  job-retry-attempts: "3"

  # Production database connections
  scylla-hosts: "scylla-prod-cluster:9042"
  scylla-keyspace: "domainr"
  maria-host: "mariadb-prod-cluster"
  maria-database: "domainr"

  # Production external services
  browserless-url: "http://browserless-prod-service:3000"

  # Production performance tuning
  node-options: "--max-old-space-size=6144"

  # Production security settings
  security-headers-enabled: "true"
  cors-enabled: "false"

  # Production monitoring
  metrics-enabled: "true"
  health-check-timeout: "5000"

  # Production rate limiting
  crawl-rate-limit: "200"
  ranking-rate-limit: "100"
