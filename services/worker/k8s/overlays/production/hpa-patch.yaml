apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: worker-service-hpa
spec:
  minReplicas: 5
  maxReplicas: 50

  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 60  # More conservative for production

  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 70  # More conservative for production

  behavior:
    scaleDown:
      stabilizationWindowSeconds: 600  # 10 minutes for production stability
      policies:
      - type: Percent
        value: 5  # More conservative scale down
        periodSeconds: 60
      - type: Pods
        value: 1
        periodSeconds: 60
      selectPolicy: Min

    scaleUp:
      stabilizationWindowSeconds: 120  # 2 minutes
      policies:
      - type: Percent
        value: 25  # More conservative scale up
        periodSeconds: 60
      - type: Pods
        value: 2
        periodSeconds: 60
      selectPolicy: Max
