apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

metadata:
  name: worker-service-production
  namespace: domainr-production

# Base configuration
resources:
- ../../

# Override namespace
namespace: domainr-production

# Override common labels
commonLabels:
  environment: production
  app.kubernetes.io/version: v1.0.0

# Override images with specific version
images:
- name: domainr/worker-service
  newTag: v1.0.0

# Production replicas
replicas:
- name: worker-service
  count: 5

# Production-specific patches
patchesStrategicMerge:
- deployment-patch.yaml
- configmap-patch.yaml
- hpa-patch.yaml

# Production-specific ConfigMap
configMapGenerator:
- name: worker-env-config
  behavior: merge
  literals:
  - NODE_ENV=production
  - LOG_LEVEL=info
  - MAX_CONCURRENT_TASKS=10
  - METRICS_ENABLED=true
  - SECURITY_HEADERS_ENABLED=true
