apiVersion: apps/v1
kind: Deployment
metadata:
  name: worker-service
spec:
  replicas: 2
  template:
    spec:
      containers:
      - name: worker
        resources:
          requests:
            cpu: 250m
            memory: 512Mi
          limits:
            cpu: 1000m
            memory: 2Gi
        env:
        - name: NODE_ENV
          value: "staging"
        - name: LOG_LEVEL
          value: "debug"
        - name: MAX_CONCURRENT_TASKS
          value: "5"
