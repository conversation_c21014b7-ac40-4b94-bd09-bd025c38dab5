apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

metadata:
  name: worker-service-staging
  namespace: domainr-staging

# Base configuration
resources:
- ../../

# Override namespace
namespace: domainr-staging

# Override common labels
commonLabels:
  environment: staging
  app.kubernetes.io/version: staging

# Override images
images:
- name: domainr/worker-service
  newTag: staging

# Override replicas for staging
replicas:
- name: worker-service
  count: 2

# Staging-specific patches
patchesStrategicMerge:
- deployment-patch.yaml
- configmap-patch.yaml

# Staging-specific ConfigMap
configMapGenerator:
- name: worker-env-config
  behavior: merge
  literals:
  - NODE_ENV=staging
  - LOG_LEVEL=debug
  - MAX_CONCURRENT_TASKS=5
  - CRAWL_TIMEOUT=20000
