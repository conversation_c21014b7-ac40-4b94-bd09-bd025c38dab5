apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: worker-service-hpa
  namespace: domainr
  labels:
    app: worker-service
    component: autoscaling
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: worker-service

  minReplicas: 3
  maxReplicas: 20

  metrics:
  # CPU-based scaling
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70

  # Memory-based scaling
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80

  # Custom metrics (if available)
  - type: Pods
    pods:
      metric:
        name: active_jobs_per_pod
      target:
        type: AverageValue
        averageValue: "8"

  - type: Pods
    pods:
      metric:
        name: queue_length_per_pod
      target:
        type: AverageValue
        averageValue: "50"

  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300  # 5 minutes
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
      - type: Pods
        value: 2
        periodSeconds: 60
      selectPolicy: Min

    scaleUp:
      stabilizationWindowSeconds: 60  # 1 minute
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
      - type: Pods
        value: 4
        periodSeconds: 60
      selectPolicy: Max

---
# PodDisruptionBudget to ensure availability during scaling
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: worker-service-pdb
  namespace: domainr
  labels:
    app: worker-service
    component: availability
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: worker-service
      component: worker
