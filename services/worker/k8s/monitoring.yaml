apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: worker-service-monitor
  namespace: domainr
  labels:
    app: worker-service
    component: monitoring
    prometheus: kube-prometheus
spec:
  selector:
    matchLabels:
      app: worker-service
      component: service
  endpoints:
  - port: health
    path: /metrics
    interval: 30s
    scrapeTimeout: 10s
    honorLabels: true
    metricRelabelings:
    - sourceLabels: [__name__]
      regex: 'worker_.*'
      targetLabel: service
      replacement: 'worker-service'

---
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: worker-service-alerts
  namespace: domainr
  labels:
    app: worker-service
    component: monitoring
    prometheus: kube-prometheus
spec:
  groups:
  - name: worker-service.rules
    interval: 30s
    rules:

    # High-level service health
    - alert: WorkerServiceDown
      expr: up{job="worker-service"} == 0
      for: 1m
      labels:
        severity: critical
        service: worker-service
      annotations:
        summary: "Worker service instance is down"
        description: "Worker service instance {{ $labels.instance }} has been down for more than 1 minute"

    # Resource utilization alerts
    - alert: WorkerHighCPUUsage
      expr: rate(container_cpu_usage_seconds_total{pod=~"worker-service-.*"}[5m]) * 100 > 80
      for: 5m
      labels:
        severity: warning
        service: worker-service
      annotations:
        summary: "Worker service high CPU usage"
        description: "Worker service pod {{ $labels.pod }} CPU usage is above 80% for more than 5 minutes"

    - alert: WorkerHighMemoryUsage
      expr: container_memory_usage_bytes{pod=~"worker-service-.*"} / container_spec_memory_limit_bytes * 100 > 85
      for: 5m
      labels:
        severity: warning
        service: worker-service
      annotations:
        summary: "Worker service high memory usage"
        description: "Worker service pod {{ $labels.pod }} memory usage is above 85% for more than 5 minutes"

    # Job processing alerts
    - alert: WorkerJobProcessingStalled
      expr: increase(worker_jobs_completed_total[10m]) == 0 and worker_jobs_active > 0
      for: 10m
      labels:
        severity: warning
        service: worker-service
      annotations:
        summary: "Worker job processing appears stalled"
        description: "No jobs have been completed in the last 10 minutes despite active jobs"

    - alert: WorkerJobFailureRateHigh
      expr: rate(worker_jobs_failed_total[5m]) / rate(worker_jobs_total[5m]) > 0.1
      for: 5m
      labels:
        severity: warning
        service: worker-service
      annotations:
        summary: "Worker job failure rate is high"
        description: "Worker job failure rate is above 10% for the last 5 minutes"

    # Queue alerts
    - alert: WorkerQueueBacklog
      expr: worker_queue_length > 1000
      for: 5m
      labels:
        severity: warning
        service: worker-service
      annotations:
        summary: "Worker queue has significant backlog"
        description: "Worker queue length is {{ $value }}, indicating processing backlog"

    # Database connectivity alerts
    - alert: WorkerDatabaseConnectionFailure
      expr: worker_database_connection_errors_total > 0
      for: 1m
      labels:
        severity: critical
        service: worker-service
      annotations:
        summary: "Worker service database connection failures"
        description: "Worker service is experiencing database connection failures"

    # Health check alerts
    - alert: WorkerHealthCheckFailure
      expr: worker_health_check_failures_total > 3
      for: 2m
      labels:
        severity: warning
        service: worker-service
      annotations:
        summary: "Worker service health check failures"
        description: "Worker service has failed health checks {{ $value }} times"

---
# Grafana Dashboard ConfigMap
apiVersion: v1
kind: ConfigMap
metadata:
  name: worker-service-dashboard
  namespace: domainr
  labels:
    app: worker-service
    component: monitoring
    grafana_dashboard: "1"
data:
  worker-service-dashboard.json: |
    {
      "dashboard": {
        "id": null,
        "title": "Worker Service Dashboard",
        "tags": ["worker", "domainr"],
        "style": "dark",
        "timezone": "browser",
        "panels": [
          {
            "id": 1,
            "title": "Service Overview",
            "type": "stat",
            "targets": [
              {
                "expr": "up{job=\"worker-service\"}",
                "legendFormat": "Instances Up"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "thresholds"
                },
                "thresholds": {
                  "steps": [
                    {"color": "red", "value": 0},
                    {"color": "yellow", "value": 1},
                    {"color": "green", "value": 3}
                  ]
                }
              }
            }
          },
          {
            "id": 2,
            "title": "Job Processing Rate",
            "type": "graph",
            "targets": [
              {
                "expr": "rate(worker_jobs_completed_total[5m])",
                "legendFormat": "Jobs Completed/sec"
              },
              {
                "expr": "rate(worker_jobs_failed_total[5m])",
                "legendFormat": "Jobs Failed/sec"
              }
            ]
          },
          {
            "id": 3,
            "title": "Resource Usage",
            "type": "graph",
            "targets": [
              {
                "expr": "rate(container_cpu_usage_seconds_total{pod=~\"worker-service-.*\"}[5m]) * 100",
                "legendFormat": "CPU Usage %"
              },
              {
                "expr": "container_memory_usage_bytes{pod=~\"worker-service-.*\"} / 1024 / 1024",
                "legendFormat": "Memory Usage MB"
              }
            ]
          }
        ],
        "time": {
          "from": "now-1h",
          "to": "now"
        },
        "refresh": "30s"
      }
    }
