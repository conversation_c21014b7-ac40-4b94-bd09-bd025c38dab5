apiVersion: v1
kind: ServiceAccount
metadata:
  name: worker-service
  namespace: domainr
  labels:
    app: worker-service
    component: rbac
automountServiceAccountToken: true

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: worker-service
  namespace: domainr
  labels:
    app: worker-service
    component: rbac
rules:
# Allow reading ConfigMaps and Secrets
- apiGroups: [""]
  resources: ["configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
# Allow reading own pod information for instance identification
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]
  resourceNames: []
# Allow creating events for monitoring and debugging
- apiGroups: [""]
  resources: ["events"]
  verbs: ["create", "patch"]
# Allow reading services for service discovery
- apiGroups: [""]
  resources: ["services", "endpoints"]
  verbs: ["get", "list", "watch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: worker-service
  namespace: domainr
  labels:
    app: worker-service
    component: rbac
subjects:
- kind: ServiceAccount
  name: worker-service
  namespace: domainr
roleRef:
  kind: Role
  name: worker-service
  apiGroup: rbac.authorization.k8s.io

---
# ClusterRole for cross-namespace operations (if needed)
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: worker-service-cluster
  labels:
    app: worker-service
    component: rbac
rules:
# Allow reading nodes for resource monitoring
- apiGroups: [""]
  resources: ["nodes"]
  verbs: ["get", "list", "watch"]
# Allow reading metrics for monitoring
- apiGroups: ["metrics.k8s.io"]
  resources: ["nodes", "pods"]
  verbs: ["get", "list"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: worker-service-cluster
  labels:
    app: worker-service
    component: rbac
subjects:
- kind: ServiceAccount
  name: worker-service
  namespace: domainr
roleRef:
  kind: ClusterRole
  name: worker-service-cluster
  apiGroup: rbac.authorization.k8s.io
