apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

metadata:
  name: worker-service
  namespace: domainr

# Common labels applied to all resources
commonLabels:
  app.kubernetes.io/name: worker-service
  app.kubernetes.io/component: worker
  app.kubernetes.io/part-of: domainr
  app.kubernetes.io/managed-by: kustomize

# Common annotations
commonAnnotations:
  deployment.kubernetes.io/revision: "1"
  kubernetes.io/managed-by: "kustomize"

# Namespace for all resources
namespace: domainr

# Resources to include
resources:
- rbac.yaml
- configmap.yaml
- secret.yaml
- deployment.yaml
- service.yaml
- hpa.yaml
- monitoring.yaml
- network-policy.yaml

# Images to use (can be overridden per environment)
images:
- name: domainr/worker-service
  newTag: latest

# ConfigMap generator for environment-specific configs
configMapGenerator:
- name: worker-env-config
  literals:
  - NODE_ENV=production
  - LOG_LEVEL=info
  - METRICS_ENABLED=true

# Secret generator (for development only - use external secret management in production)
secretGenerator:
- name: worker-env-secrets
  literals:
  - EXAMPLE_SECRET=changeme
  type: Opaque

# Patches for different environments
patchesStrategicMerge:
- |-
  apiVersion: apps/v1
  kind: Deployment
  metadata:
    name: worker-service
  spec:
    template:
      spec:
        containers:
        - name: worker
          resources:
            requests:
              cpu: 500m
              memory: 1Gi
            limits:
              cpu: 2000m
              memory: 4Gi

# Replicas (can be overridden per environment)
replicas:
- name: worker-service
  count: 3

# Transformers
transformers: []

# Generators
generators: []
