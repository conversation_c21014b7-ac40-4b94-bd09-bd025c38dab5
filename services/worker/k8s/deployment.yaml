apiVersion: apps/v1
kind: Deployment
metadata:
  name: worker-service
  namespace: domainr
  labels:
    app: worker-service
    component: worker
    version: v1.0.0
  annotations:
    deployment.kubernetes.io/revision: "1"
    kubernetes.io/change-cause: "Initial deployment of worker service"
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: worker-service
      component: worker
  template:
    metadata:
      labels:
        app: worker-service
        component: worker
        version: v1.0.0
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "3000"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: worker-service
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        runAsGroup: 1001
        fsGroup: 1001

      initContainers:
      - name: wait-for-databases
        image: busybox:1.35
        command: ['sh', '-c']
        args:
        - |
          echo "Waiting for databases to be ready..."
          until nc -z scylla-service 9042; do
            echo "Waiting for ScyllaDB..."
            sleep 2
          done
          until nc -z mariadb-service 3306; do
            echo "Waiting for MariaDB..."
            sleep 2
          done
          until nc -z redis-service 6379; do
            echo "Waiting for Redis..."
            sleep 2
          done
          until nc -z manticore-service 9308; do
            echo "Waiting for Manticore..."
            sleep 2
          done
          echo "All databases are ready!"

      containers:
      - name: worker
        image: domainr/worker-service:latest
        imagePullPolicy: Always

        ports:
        - name: health
          containerPort: 3000
          protocol: TCP

        env:
        - name: NODE_ENV
          value: "production"
        - name: WORKER_INSTANCE_ID
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: MAX_CONCURRENT_TASKS
          valueFrom:
            configMapKeyRef:
              name: worker-config
              key: max-concurrent-tasks

        # Database Configuration
        - name: SCYLLA_HOSTS
          valueFrom:
            configMapKeyRef:
              name: worker-config
              key: scylla-hosts
        - name: SCYLLA_KEYSPACE
          valueFrom:
            configMapKeyRef:
              name: worker-config
              key: scylla-keyspace
        - name: MARIA_HOST
          valueFrom:
            configMapKeyRef:
              name: worker-config
              key: maria-host
        - name: MARIA_PORT
          valueFrom:
            configMapKeyRef:
              name: worker-config
              key: maria-port
        - name: MARIA_DATABASE
          valueFrom:
            configMapKeyRef:
              name: worker-config
              key: maria-database
        - name: MARIA_USER
          valueFrom:
            secretKeyRef:
              name: worker-secrets
              key: maria-user
        - name: MARIA_PASSWORD
          valueFrom:
            secretKeyRef:
              name: worker-secrets
              key: maria-password
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: worker-secrets
              key: redis-url
        - name: MANTICORE_HOST
          valueFrom:
            configMapKeyRef:
              name: worker-config
              key: manticore-host
        - name: MANTICORE_PORT
          valueFrom:
            configMapKeyRef:
              name: worker-config
              key: manticore-port

        # External Services
        - name: BROWSERLESS_URL
          valueFrom:
            configMapKeyRef:
              name: worker-config
              key: browserless-url
        - name: IMAGE_PROXY_URL
          valueFrom:
            configMapKeyRef:
              name: worker-config
              key: image-proxy-url

        # AI Configuration
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: worker-secrets
              key: openai-api-key
              optional: true
        - name: ANTHROPIC_API_KEY
          valueFrom:
            secretKeyRef:
              name: worker-secrets
              key: anthropic-api-key
              optional: true
        - name: GOOGLE_AI_API_KEY
          valueFrom:
            secretKeyRef:
              name: worker-secrets
              key: google-ai-api-key
              optional: true

        # Processing Configuration
        - name: CRAWL_TIMEOUT
          valueFrom:
            configMapKeyRef:
              name: worker-config
              key: crawl-timeout
        - name: RANKING_UPDATE_BATCH_SIZE
          valueFrom:
            configMapKeyRef:
              name: worker-config
              key: ranking-batch-size
        - name: JOB_RETRY_ATTEMPTS
          valueFrom:
            configMapKeyRef:
              name: worker-config
              key: job-retry-attempts

        # Logging
        - name: LOG_LEVEL
          valueFrom:
            configMapKeyRef:
              name: worker-config
              key: log-level
        - name: LOG_FORMAT
          value: "json"

        resources:
          requests:
            cpu: 500m
            memory: 1Gi
          limits:
            cpu: 2000m
            memory: 4Gi

        livenessProbe:
          httpGet:
            path: /health
            port: health
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
          successThreshold: 1

        readinessProbe:
          httpGet:
            path: /health/ready
            port: health
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
          successThreshold: 1

        startupProbe:
          httpGet:
            path: /health/startup
            port: health
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
          successThreshold: 1

        volumeMounts:
        - name: logs
          mountPath: /app/logs
        - name: tmp
          mountPath: /app/tmp
        - name: config
          mountPath: /app/config
          readOnly: true

        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 1001
          capabilities:
            drop:
            - ALL

      volumes:
      - name: logs
        emptyDir:
          sizeLimit: 1Gi
      - name: tmp
        emptyDir:
          sizeLimit: 500Mi
      - name: config
        configMap:
          name: worker-config
          defaultMode: 0644

      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - worker-service
              topologyKey: kubernetes.io/hostname

      tolerations:
      - key: "worker-node"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"

      terminationGracePeriodSeconds: 60

      restartPolicy: Always
