apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: worker-service-network-policy
  namespace: domainr
  labels:
    app: worker-service
    component: security
spec:
  podSelector:
    matchLabels:
      app: worker-service
      component: worker

  policyTypes:
  - Ingress
  - Egress

  ingress:
  # Allow health checks from Kubernetes system
  - from:
    - namespaceSelector:
        matchLabels:
          name: kube-system
    ports:
    - protocol: TCP
      port: 3000

  # Allow monitoring from Prometheus
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    - podSelector:
        matchLabels:
          app: prometheus
    ports:
    - protocol: TCP
      port: 3000

  # Allow traffic from admin panel
  - from:
    - podSelector:
        matchLabels:
          app: admin-service
    ports:
    - protocol: TCP
      port: 3000

  # Allow inter-worker communication (if needed)
  - from:
    - podSelector:
        matchLabels:
          app: worker-service
    ports:
    - protocol: TCP
      port: 3000

  egress:
  # Allow DNS resolution
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53

  # Allow HTTPS traffic for external APIs
  - to: []
    ports:
    - protocol: TCP
      port: 443

  # Allow HTTP traffic for external APIs
  - to: []
    ports:
    - protocol: TCP
      port: 80

  # Allow database connections
  - to:
    - podSelector:
        matchLabels:
          app: scylla
    ports:
    - protocol: TCP
      port: 9042

  - to:
    - podSelector:
        matchLabels:
          app: mariadb
    ports:
    - protocol: TCP
      port: 3306

  - to:
    - podSelector:
        matchLabels:
          app: redis
    ports:
    - protocol: TCP
      port: 6379

  - to:
    - podSelector:
        matchLabels:
          app: manticore
    ports:
    - protocol: TCP
      port: 9308

  # Allow Browserless connections
  - to:
    - podSelector:
        matchLabels:
          app: browserless
    ports:
    - protocol: TCP
      port: 3000
