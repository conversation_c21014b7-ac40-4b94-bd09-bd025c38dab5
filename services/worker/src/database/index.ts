/**
 * Database Module Index
 *
 * Exports all database-related functionality for the worker service
 */

import WorkerDatabaseManager from './WorkerDatabaseManager';

// Main database manager
export { default as WorkerDatabaseManager } from './WorkerDatabaseManager';

// Individual database clients (re-exported from shared)
export { default as ScyllaClient } from '@shared/database/ScyllaClient';
export { default as MariaClient } from '@shared/database/MariaClient';
export { default as RedisClient } from '@shared/database/RedisClient';
export { default as ManticoreClient } from '@shared/database/ManticoreClient';

// Migration manager
export { DatabaseMigrationManager } from './DatabaseMigrationManager';

// Schema definitions
export * from './schemas/ScyllaSchema';
export * from './schemas/MariaSchema';
export * from './schemas/ManticoreSchema';

// Type definitions
export * from '../types/DatabaseTypes';

// Default export
export default WorkerDatabaseManager;
