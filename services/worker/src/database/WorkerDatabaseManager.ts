/**
 * Worker Database Manager
 *
 * Consolidates ALL database management functionality from the three source services:
 * - crawler service: ScyllaDB operations for crawl data, analysis results
 * - ranking-engine service: ScyllaDB + MariaDB operations for rankings, backlinks
 * - scheduler service: ScyllaDB operations for job management
 *
 * This manager provides unified access to all databases with connection pooling,
 * health monitoring, and error recovery.
 */

import type { types as CassandraTypes } from 'cassandra-driver';
import type { RowDataPacket } from 'mysql2/promise';
import type ScyllaClient from '@shared/database/ScyllaClient';
import type MariaClient from '@shared/database/MariaClient';
import type RedisClient from '@shared/database/RedisClient';
import type Man<PERSON>oreClient from '@shared/database/ManticoreClient';
import { logger as loggerFactory } from '@shared/utils/Logger';
import type {
	DatabaseConnectionStatusType,
	WorkerDatabaseConfigType,
	DomainAnalysisDataType,
	DomainRankingDataType,
	CrawlJobDataType,
	BacklinkDataType,
	DomainCategoryDataType,
	SearchIndexDataType,
} from '../types/DatabaseTypes';
import DatabaseMigrationManager from './DatabaseMigrationManager';
import getDatabase from '@/database/client';

const logger = loggerFactory.getLogger('WorkerDatabaseManager');

class WorkerDatabaseManager
{
	private config: WorkerDatabaseConfigType;
	private scyllaClient: ScyllaClient | null = null;
	private mariaClient: MariaClient | null = null;
	private redisClient: RedisClient | null = null;
	private manticoreClient: ManticoreClient | null = null;
	private migrationManager: DatabaseMigrationManager | null = null;
	private isInitialized: boolean = false;
	private healthCheckInterval: NodeJS.Timeout | null = null;

	constructor(config: WorkerDatabaseConfigType)
	{
		this.config = config;
	}

	/**
	 * Initialize all database connections
	 */
	async initialize(): Promise<void>
	{
		if (this.isInitialized)
		{
			logger.warn('Database manager already initialized');
			return;
		}

		try
		{
			logger.info('Initializing database connections...');

			// Bind to shared DatabaseManager singleton clients
			const db = await getDatabase();
			try { this.redisClient = db.redis; }
			catch (e) { logger.warn('Redis client not available from shared DatabaseManager'); }
			try { this.scyllaClient = (db as any).scylla; }
			catch (e) { logger.warn('ScyllaDB client not available from shared DatabaseManager'); }
			try { this.mariaClient = (db as any).mariadb; }
			catch (e) { logger.warn('MariaDB client not available from shared DatabaseManager'); }
			try { this.manticoreClient = (db as any).manticore; }
			catch (e) { logger.warn('Manticore client not available from shared DatabaseManager'); }
			logger.info('Shared DatabaseManager clients are ready');

			// Run database migrations only when enabled and all clients exist
			if (this.config.runMigrationsOnStartup !== false && this.scyllaClient && this.mariaClient && this.manticoreClient)
			{
				logger.info('Running database migrations...');
				this.migrationManager = new DatabaseMigrationManager(
					this.scyllaClient,
					this.mariaClient,
					this.manticoreClient,
				);
				await this.migrationManager.runAllMigrations();
			}
			else
			{
				logger.info('Skipping database migrations on startup (disabled or missing clients)');
			}

			// Start health monitoring
			this.startHealthMonitoring();

			this.isInitialized = true;
			logger.info('All database connections initialized successfully');
		}
		catch (error)
		{
			logger.error('Failed to initialize database connections:', error);
			await this.cleanup();
			throw error;
		}
	}

	/**
	 * Close all database connections
	 */
	async close(): Promise<void>
	{
		try
		{
			logger.info('Closing database connections...');

			// Stop health monitoring
			if (this.healthCheckInterval)
			{
				clearInterval(this.healthCheckInterval);
				this.healthCheckInterval = null;
			}

			await this.cleanup();

			this.isInitialized = false;
			logger.info('All database connections closed');
		}
		catch (error)
		{
			logger.error('Error closing database connections:', error);
			throw error;
		}
	}

	/**
	 * Get ScyllaDB client
	 */
	getScyllaClient(): ScyllaClient
	{
		if (!this.scyllaClient)
		{
			throw new Error('ScyllaDB client not initialized');
		}
		return this.scyllaClient;
	}

	/**
	 * Get MariaDB client
	 */
	getMariaClient(): MariaClient
	{
		if (!this.mariaClient)
		{
			throw new Error('MariaDB client not initialized');
		}
		return this.mariaClient;
	}

	/**
	 * Get Redis client
	 */
	getRedisClient(): RedisClient
	{
		if (!this.redisClient)
		{
			throw new Error('Redis client not initialized');
		}
		return this.redisClient;
	}

	/**
	 * Get Manticore client
	 */
	getManticoreClient(): ManticoreClient
	{
		if (!this.manticoreClient)
		{
			throw new Error('Manticore client not initialized');
		}
		return this.manticoreClient;
	}

	/**
	 * Get migration manager
	 */
	getMigrationManager(): DatabaseMigrationManager
	{
		if (!this.migrationManager)
		{
			throw new Error('Migration manager not initialized');
		}
		return this.migrationManager;
	}

	/**
	 * Health check for all database connections
	 */
	async healthCheck(): Promise<{
		scylla: DatabaseConnectionStatusType;
		maria: DatabaseConnectionStatusType;
		redis: DatabaseConnectionStatusType;
		manticore: DatabaseConnectionStatusType;
	}>
	{
		const results = {
			scylla: { connected: false, lastCheck: new Date(), responseTime: 0 },
			maria: { connected: false, lastCheck: new Date(), responseTime: 0 },
			redis: { connected: false, lastCheck: new Date(), responseTime: 0 },
			manticore: { connected: false, lastCheck: new Date(), responseTime: 0 },
		};

		// Check ScyllaDB
		if (this.scyllaClient)
		{
			const start = Date.now();
			try
			{
				results.scylla.connected = await this.scyllaClient.healthCheck();
				results.scylla.responseTime = Date.now() - start;
			}
			catch (error)
			{
				logger.error('ScyllaDB health check failed:', error);
				results.scylla.responseTime = Date.now() - start;
			}
		}

		// Check MariaDB
		if (this.mariaClient)
		{
			const start = Date.now();
			try
			{
				results.maria.connected = await this.mariaClient.healthCheck();
				results.maria.responseTime = Date.now() - start;
			}
			catch (error)
			{
				logger.error('MariaDB health check failed:', error);
				results.maria.responseTime = Date.now() - start;
			}
		}

		// Check Redis
		if (this.redisClient)
		{
			const start = Date.now();
			try
			{
				results.redis.connected = await this.redisClient.healthCheck();
				results.redis.responseTime = Date.now() - start;
			}
			catch (error)
			{
				logger.error('Redis health check failed:', error);
				results.redis.responseTime = Date.now() - start;
			}
		}

		// Check Manticore
		if (this.manticoreClient)
		{
			const start = Date.now();
			try
			{
				results.manticore.connected = await this.manticoreClient.healthCheck();
				results.manticore.responseTime = Date.now() - start;
			}
			catch (error)
			{
				logger.error('Manticore health check failed:', error);
				results.manticore.responseTime = Date.now() - start;
			}
		}

		return results;
	}

	// ===== SCYLLA DB OPERATIONS =====
	// Extracted from crawler, ranking-engine, and scheduler services

	/**
	 * Domain Analysis Operations (from crawler service)
	 */
	async upsertDomainAnalysis(data: DomainAnalysisDataType): Promise<void>
	{
		const scylla = this.getScyllaClient();

		const query = `
			INSERT INTO domain_analysis (
				domain, global_rank, category, category_rank,
				performance_metrics, security_metrics, seo_metrics, technical_metrics,
				technologies, server_info, domain_age_days, registration_date,
				expiration_date, registrar, dns_records, screenshot_urls,
				subdomains, last_crawled, crawl_status, robots_analysis,
				ssl_analysis, homepage_analysis
			) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
		`;

		const params = [
			data.domain,
			data.globalRank,
			data.category,
			data.categoryRank,
			JSON.stringify(data.performanceMetrics),
			JSON.stringify(data.securityMetrics),
			JSON.stringify(data.seoMetrics),
			JSON.stringify(data.technicalMetrics),
			data.technologies,
			JSON.stringify(data.serverInfo),
			data.domainAgeDays,
			data.registrationDate,
			data.expirationDate,
			data.registrar,
			JSON.stringify(data.dnsRecords),
			data.screenshotUrls,
			data.subdomains,
			data.lastCrawled,
			data.crawlStatus,
			JSON.stringify(data.robotsAnalysis),
			JSON.stringify(data.sslAnalysis),
			JSON.stringify(data.homepageAnalysis),
		];

		await scylla.execute(query, params);
		logger.debug(`Domain analysis upserted for: ${data.domain}`);
	}

	async getDomainAnalysis(domain: string): Promise<DomainAnalysisDataType | null>
	{
		const scylla = this.getScyllaClient();

		const query = 'SELECT * FROM domain_analysis WHERE domain = ?';
		const result = await scylla.execute(query, [domain]);

		if (result.rows.length === 0)
		{
			return null;
		}

		const row = result.rows[0];
		return this.mapRowToDomainAnalysis(row as unknown as CassandraTypes.Row);
	}

	/**
	 * Domain Rankings Operations (from ranking-engine service)
	 */
	async upsertDomainRanking(data: DomainRankingDataType): Promise<void>
	{
		const scylla = this.getScyllaClient();

		const query = `
			INSERT INTO domain_rankings (
				ranking_type, rank, domain, overall_score, performance_score,
				security_score, seo_score, technical_score, backlink_score,
				traffic_estimate, last_updated, grade, category
			) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
		`;

		const params = [
			data.rankingType,
			data.rank,
			data.domain,
			data.overallScore,
			data.performanceScore,
			data.securityScore,
			data.seoScore,
			data.technicalScore,
			data.backlinkScore,
			data.trafficEstimate,
			data.lastUpdated,
			data.grade,
			data.category,
		];

		await scylla.execute(query, params);
		logger.debug(`Domain ranking upserted for: ${data.domain} (${data.rankingType})`);
	}

	async getDomainRankings(rankingType: string, limit: number = 100): Promise<DomainRankingDataType[]>
	{
		const scylla = this.getScyllaClient();

		const query = `
			SELECT * FROM domain_rankings
			WHERE ranking_type = ?
			ORDER BY rank ASC
			LIMIT ?
		`;

		const result = await scylla.execute(query, [rankingType, limit]);
		return result.rows.map(row => this.mapRowToDomainRanking(row as unknown as CassandraTypes.Row));
	}

	/**
	 * Domain Ranking History Operations (from ranking-engine service)
	 */
	async insertRankingHistory(data: {
		domain: string;
		rankingType: string;
		rank: number;
		overallScore: number;
		date: Date;
		previousRank?: number;
		rankChange?: number;
	}): Promise<void>
	{
		const scylla = this.getScyllaClient();

		const query = `
			INSERT INTO domain_ranking_history (
				domain, ranking_type, date, rank, overall_score,
				previous_rank, rank_change
			) VALUES (?, ?, ?, ?, ?, ?, ?)
		`;

		const params = [
			data.domain,
			data.rankingType,
			data.date.toISOString().split('T')[0], // Date only
			data.rank,
			data.overallScore,
			data.previousRank,
			data.rankChange,
		];

		await scylla.execute(query, params);
		logger.debug(`Ranking history inserted for: ${data.domain}`);
	}

	/**
	 * Crawl Jobs Operations (from scheduler service)
	 */
	async insertCrawlJob(data: CrawlJobDataType): Promise<void>
	{
		const scylla = this.getScyllaClient();

		const query = `
			INSERT INTO domain_crawl_jobs (
				job_id, domain, crawl_type, priority, status, retry_count,
				scheduled_at, started_at, completed_at, error_message,
				user_agent, pages_to_crawl, metadata
			) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
		`;

		const params = [
			data.jobId,
			data.domain,
			data.crawlType,
			data.priority,
			data.status,
			data.retryCount,
			data.scheduledAt,
			data.startedAt,
			data.completedAt,
			data.errorMessage,
			data.userAgent,
			data.pagesToCrawl,
			JSON.stringify(data.metadata),
		];

		await scylla.execute(query, params);
		logger.debug(`Crawl job inserted: ${data.jobId} for domain: ${data.domain}`);
	}

	async updateCrawlJobStatus(jobId: string, status: string, errorMessage?: string): Promise<void>
	{
		const scylla = this.getScyllaClient();

		const query = `
			UPDATE domain_crawl_jobs
			SET status = ?, completed_at = ?, error_message = ?
			WHERE job_id = ?
		`;

		const params = [
			status,
			new Date(),
			errorMessage || null,
			jobId,
		];

		await scylla.execute(query, params);
		logger.debug(`Crawl job status updated: ${jobId} -> ${status}`);
	}

	// ===== MARIA DB OPERATIONS =====
	// Extracted from ranking-engine service

	/**
	 * Backlinks Operations (from ranking-engine service)
	 */
	async insertBacklink(data: BacklinkDataType): Promise<void>
	{
		const maria = this.getMariaClient();

		const query = `
			INSERT INTO backlinks (
				id, source_domain, target_domain, link_quality_score,
				anchor_text, link_type, discovered_at, last_verified, is_active
			) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
			ON DUPLICATE KEY UPDATE
				link_quality_score = VALUES(link_quality_score),
				last_verified = VALUES(last_verified),
				is_active = VALUES(is_active)
		`;

		const params = [
			data.id,
			data.sourceDomain,
			data.targetDomain,
			data.linkQualityScore,
			data.anchorText,
			data.linkType,
			data.discoveredAt,
			data.lastVerified,
			data.isActive,
		];

		await maria.execute(query, params);
		logger.debug(`Backlink inserted/updated: ${data.id}`);
	}

	async getBacklinksByDomain(domain: string, limit: number = 100): Promise<BacklinkDataType[]>
	{
		const maria = this.getMariaClient();

		const query = `
			SELECT * FROM backlinks
			WHERE target_domain = ? AND is_active = TRUE
			ORDER BY link_quality_score DESC, discovered_at DESC
			LIMIT ?
		`;

		const rows = await maria.query<RowDataPacket>(query, [domain, limit]);
		return rows.map(row => this.mapRowToBacklink(row));
	}

	/**
	 * Domain Categories Operations (from ranking-engine service)
	 */
	async assignDomainToCategory(data: DomainCategoryDataType): Promise<void>
	{
		const maria = this.getMariaClient();

		const query = `
			INSERT INTO domain_category_mapping (domain, category_id, confidence_score)
			VALUES (?, ?, ?)
			ON DUPLICATE KEY UPDATE
				confidence_score = VALUES(confidence_score),
				assigned_at = CURRENT_TIMESTAMP
		`;

		const params = [data.domain, data.categoryId, data.confidenceScore];
		await maria.execute(query, params);
		logger.debug(`Domain category assigned: ${data.domain} -> ${data.categoryId}`);
	}

	// ===== REDIS OPERATIONS =====
	// Extracted from all three services

	/**
	 * Caching Operations (from all services)
	 */
	async cacheDomainAnalysis(domain: string, data: DomainAnalysisDataType, ttl: number = 3600): Promise<void>
	{
		const redis = this.getRedisClient();
		const key = `domain:analysis:${domain}`;
		await redis.setex(key, ttl, data);
		logger.debug(`Domain analysis cached: ${domain}`);
	}

	async getCachedDomainAnalysis(domain: string): Promise<DomainAnalysisDataType | null>
	{
		const redis = this.getRedisClient();
		const key = `domain:analysis:${domain}`;
		const cached = await redis.get<DomainAnalysisDataType>(key);
		return cached;
	}

	async cacheDomainRanking(domain: string, data: DomainRankingDataType, ttl: number = 1800): Promise<void>
	{
		const redis = this.getRedisClient();
		const key = `domain:ranking:${domain}`;
		await redis.setex(key, ttl, data);
		logger.debug(`Domain ranking cached: ${domain}`);
	}

	/**
	 * Domain Locking Operations (for multi-worker coordination)
	 */
	async acquireDomainLock(domain: string, workerId: string, ttl: number = 300): Promise<boolean>
	{
		const redis = this.getRedisClient();
		const key = `lock:domain:${domain}`;
		// Store JSON string to be compatible with wrapper get() JSON parsing
		const result = await redis.setnx(key, JSON.stringify(workerId));

		if (result)
		{
			await redis.expire(key, ttl);
			logger.debug(`Domain lock acquired: ${domain} by ${workerId}`);
			return true;
		}

		return false;
	}

	async releaseDomainLock(domain: string, workerId: string): Promise<void>
	{
		const redis = this.getRedisClient();
		const key = `lock:domain:${domain}`;

		// Only release if we own the lock
		const owner = await redis.get<string>(key);
		if (owner === workerId)
		{
			await redis.del(key);
			logger.debug(`Domain lock released: ${domain} by ${workerId}`);
		}
	}

	/**
	 * Job Queue Operations (from scheduler service)
	 */
	async enqueueJob(queueName: string, jobData: Record<string, unknown>): Promise<void>
	{
		const redis = this.getRedisClient();
		await redis.lpush(`queue:${queueName}`, JSON.stringify(jobData));
		logger.debug(`Job enqueued to ${queueName}`);
	}

	async dequeueJob(queueName: string): Promise<Record<string, unknown> | null>
	{
		const redis = this.getRedisClient();
		const job = await redis.rpop(`queue:${queueName}`);
		return job ? JSON.parse(job) : null;
	}

	// ===== MANTICORE OPERATIONS =====
	// Extracted from all services for search indexing

	/**
	 * Search Index Operations (from all services)
	 */
	async indexDomain(data: SearchIndexDataType): Promise<void>
	{
		const manticore = this.getManticoreClient();

		await manticore.upsertDocument('domains_index', data.domain, {
			domain: data.domain,
			title: data.title,
			description: data.description,
			category: data.category,
			country: data.country,
			language: data.language,
			technologies: data.technologies,
			registrar: data.registrar,
			domain_age_days: data.domainAgeDays,
			global_rank: data.globalRank,
			overall_score: data.overallScore,
			performance_score: data.performanceScore,
			security_score: data.securityScore,
			seo_score: data.seoScore,
			technical_score: data.technicalScore,
			backlink_score: data.backlinkScore,
			traffic_estimate: data.trafficEstimate,
			ssl_grade: data.sslGrade,
			last_updated: data.lastUpdated,
		});

		logger.debug(`Domain indexed in search: ${data.domain}`);
	}

	async searchDomains(query: string, filters: Record<string, unknown> = {}, limit: number = 20): Promise<{
		results: Array<Record<string, unknown>>;
		total: number;
	}>
	{
		const manticore = this.getManticoreClient();

		return await manticore.searchDomains({
			query,
			filters,
			limit,
		});
	}

	// ===== UTILITY METHODS =====

	/**
	 * Start periodic health monitoring
	 */
	private startHealthMonitoring(): void
	{
		const interval = this.config.healthCheckInterval || 30000;

		this.healthCheckInterval = setInterval(async () =>
		{
			try
			{
				const health = await this.healthCheck();
				const unhealthyDbs = Object.entries(health)
					.filter(([, status]) => !status.connected)
					.map(([name]) => name);

				if (unhealthyDbs.length > 0)
				{
					logger.warn(`Unhealthy database connections detected: ${unhealthyDbs.join(', ')}`);
				}
			}
			catch (error)
			{
				logger.error('Health check failed:', error);
			}
		}, interval);
	}

	/**
	 * Cleanup all database connections
	 */
	private async cleanup(): Promise<void>
	{
		// Shutdown shared DatabaseManager
		try
		{
			const db = await getDatabase();
			await db.shutdown();
		}
		catch (error)
		{
			const errMsg = error instanceof Error ? error.message : String(error);
			logger.warn(`Error during shared DB shutdown (ignored during cleanup): ${errMsg}`);
		}

		this.scyllaClient = null;
		this.mariaClient = null;
		this.redisClient = null;
		this.manticoreClient = null;
	}

	/**
	 * Map database row to domain analysis data
	 */
	private mapRowToDomainAnalysis(row: CassandraTypes.Row): DomainAnalysisDataType
	{
		return ({
			domain: row.domain,
			globalRank: row.global_rank,
			category: row.category,
			categoryRank: row.category_rank,
			performanceMetrics: JSON.parse(row.performance_metrics || '{}'),
			securityMetrics: JSON.parse(row.security_metrics || '{}'),
			seoMetrics: JSON.parse(row.seo_metrics || '{}'),
			technicalMetrics: JSON.parse(row.technical_metrics || '{}'),
			technologies: row.technologies || [],
			serverInfo: JSON.parse(row.server_info || '{}'),
			domainAgeDays: row.domain_age_days,
			registrationDate: row.registration_date,
			expirationDate: row.expiration_date,
			registrar: row.registrar,
			dnsRecords: JSON.parse(row.dns_records || '{}'),
			screenshotUrls: row.screenshot_urls || [],
			subdomains: row.subdomains || [],
			lastCrawled: row.last_crawled,
			crawlStatus: row.crawl_status,
			robotsAnalysis: JSON.parse(row.robots_analysis || '{}'),
			sslAnalysis: JSON.parse(row.ssl_analysis || '{}'),
			homepageAnalysis: JSON.parse(row.homepage_analysis || '{}'),
		});
	}

	/**
	 * Map database row to domain ranking data
	 */
	private mapRowToDomainRanking(row: CassandraTypes.Row): DomainRankingDataType
	{
		return {
			domain: row.domain,
			rankingType: row.ranking_type,
			rank: row.rank,
			overallScore: row.overall_score,
			performanceScore: row.performance_score,
			securityScore: row.security_score,
			seoScore: row.seo_score,
			technicalScore: row.technical_score,
			backlinkScore: row.backlink_score,
			trafficEstimate: row.traffic_estimate,
			lastUpdated: row.last_updated,
			grade: row.grade,
			category: row.category,
		};
	}

	/**
	 * Map database row to backlink data
	 */
	private mapRowToBacklink(row: RowDataPacket): BacklinkDataType
	{
		return ({
			id: row.id,
			sourceDomain: row.source_domain,
			targetDomain: row.target_domain,
			linkQualityScore: row.link_quality_score,
			anchorText: row.anchor_text,
			linkType: row.link_type,
			discoveredAt: row.discovered_at,
			lastVerified: row.last_verified,
			isActive: row.is_active,
		});
	}

	/**
	 * Check if database manager is initialized
	 */
	isManagerInitialized(): boolean
	{
		return this.isInitialized;
	}
}

export default WorkerDatabaseManager;
