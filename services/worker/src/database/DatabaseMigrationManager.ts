/**
 * Database Migration Manager
 *
 * Handles database schema migrations for all database systems:
 * - ScyllaDB: Tables, indexes, materialized views
 * - MariaDB: Tables, indexes, initial data
 * - Manticore: Search indexes and optimization
 */

import ScyllaClient from '@shared/database/ScyllaClient';
import Maria<PERSON><PERSON> from '@shared/database/MariaClient';
import ManticoreClient from '@shared/database/ManticoreClient';
import { logger as loggerFactory } from '@shared/utils/Logger';
import { SCYLLA_MIGRATION_SCRIPTS } from './schemas/ScyllaSchema';
import { MARIA_MIGRATION_SCRIPTS } from './schemas/MariaSchema';
import { MANTICORE_MIGRATION_SCRIPTS } from './schemas/ManticoreSchema';

const logger = loggerFactory.getLogger('DatabaseMigrationManager');

type MigrationScriptType =
{
	version: string;
	description: string;
	up: string[];
	down: string[];
};

type MigrationResultType =
{
	database: string;
	version: string;
	success: boolean;
	error?: string;
	executionTime: number;
};

class DatabaseMigrationManager
{
	private scyllaClient: ScyllaClient;
	private mariaClient: MariaClient;
	private manticoreClient: ManticoreClient;

	constructor(
		scyllaClient: ScyllaClient,
		mariaClient: MariaClient,
		manticoreClient: ManticoreClient
	)
	{
		this.scyllaClient = scyllaClient;
		this.mariaClient = mariaClient;
		this.manticoreClient = manticoreClient;
	}

	/**
	 * Run all pending migrations for all databases
	 */
	async runAllMigrations(): Promise<MigrationResult[]>
	{
		logger.info('Starting database migrations...');

		const results: MigrationResult[] = [];

		try
		{
			// Run ScyllaDB migrations
			const scyllaResults = await this.runScyllaMigrations();
			results.push(...scyllaResults);

			// Run MariaDB migrations
			const mariaResults = await this.runMariaMigrations();
			results.push(...mariaResults);

			// Run Manticore migrations
			const manticoreResults = await this.runManticoreMigrations();
			results.push(...manticoreResults);

			const successCount = results.filter(r => r.success).length;
			const failureCount = results.filter(r => !r.success).length;

			logger.info('Database migrations completed', {
				total: results.length,
				successful: successCount,
				failed: failureCount,
			});

			if (failureCount > 0)
			{
				const failures = results.filter(r => !r.success);
				logger.error('Migration failures:', failures);
			}

			return results;
		}
		catch (error)
		{
			logger.error('Migration process failed:', error);
			throw error;
		}
	}

	/**
	 * Run ScyllaDB migrations
	 */
	async runScyllaMigrations(): Promise<MigrationResult[]>
	{
		logger.info('Running ScyllaDB migrations...');

		const results: MigrationResult[] = [];

		for (const migration of SCYLLA_MIGRATION_SCRIPTS)
		{
			const startTime = Date.now();

			try
			{
				logger.info(`Running ScyllaDB migration ${migration.version}: ${migration.description}`);

				// Check if migration already applied
				const isApplied = await this.isScyllaMigrationApplied(migration.version);
				if (isApplied)
				{
					logger.info(`ScyllaDB migration ${migration.version} already applied, skipping`);
					continue;
				}

				// Execute migration scripts
				for (const script of migration.up)
				{
					await this.scyllaClient.execute(script);
				}

				// Record migration as applied
				await this.recordScyllaMigration(migration.version, migration.description);

				const executionTime = Date.now() - startTime;

				results.push({
					database: 'ScyllaDB',
					version: migration.version,
					success: true,
					executionTime,
				});

				logger.info(`ScyllaDB migration ${migration.version} completed successfully`, {
					executionTime,
				});
			}
			catch (error)
			{
				const executionTime = Date.now() - startTime;

				results.push({
					database: 'ScyllaDB',
					version: migration.version,
					success: false,
					error: error.message,
					executionTime,
				});

				logger.error(`ScyllaDB migration ${migration.version} failed:`, error);
			}
		}

		return results;
	}

	/**
	 * Run MariaDB migrations
	 */
	async runMariaMigrations(): Promise<MigrationResult[]>
	{
		logger.info('Running MariaDB migrations...');

		const results: MigrationResult[] = [];

		// Ensure migrations table exists
		await this.createMariaMigrationsTable();

		for (const migration of MARIA_MIGRATION_SCRIPTS)
		{
			const startTime = Date.now();

			try
			{
				logger.info(`Running MariaDB migration ${migration.version}: ${migration.description}`);

				// Check if migration already applied
				const isApplied = await this.isMariaMigrationApplied(migration.version);
				if (isApplied)
				{
					logger.info(`MariaDB migration ${migration.version} already applied, skipping`);
					continue;
				}

				// Execute migration scripts in transaction
				await this.mariaClient.transaction(async (connection) =>
				{
					for (const script of migration.up)
					{
						await connection.execute(script);
					}

					// Record migration as applied
					await connection.execute(
						'INSERT INTO migrations (version, description, applied_at) VALUES (?, ?, NOW())',
						[migration.version, migration.description]
					);
				});

				const executionTime = Date.now() - startTime;

				results.push({
					database: 'MariaDB',
					version: migration.version,
					success: true,
					executionTime,
				});

				logger.info(`MariaDB migration ${migration.version} completed successfully`, {
					executionTime,
				});
			}
			catch (error)
			{
				const executionTime = Date.now() - startTime;

				results.push({
					database: 'MariaDB',
					version: migration.version,
					success: false,
					error: error.message,
					executionTime,
				});

				logger.error(`MariaDB migration ${migration.version} failed:`, error);
			}
		}

		return results;
	}

	/**
	 * Run Manticore migrations
	 */
	async runManticoreMigrations(): Promise<MigrationResult[]>
	{
		logger.info('Running Manticore migrations...');

		const results: MigrationResult[] = [];

		for (const migration of MANTICORE_MIGRATION_SCRIPTS)
		{
			const startTime = Date.now();

			try
			{
				logger.info(`Running Manticore migration ${migration.version}: ${migration.description}`);

				// Execute migration scripts
				for (const script of migration.up)
				{
					// Manticore doesn't have a direct execute method, so we'll use the client's HTTP interface
					await this.executeManticoreScript(script);
				}

				const executionTime = Date.now() - startTime;

				results.push({
					database: 'Manticore',
					version: migration.version,
					success: true,
					executionTime,
				});

				logger.info(`Manticore migration ${migration.version} completed successfully`, {
					executionTime,
				});
			}
			catch (error)
			{
				const executionTime = Date.now() - startTime;

				results.push({
					database: 'Manticore',
					version: migration.version,
					success: false,
					error: error.message,
					executionTime,
				});

				logger.error(`Manticore migration ${migration.version} failed:`, error);
			}
		}

		return results;
	}

	/**
	 * Check if ScyllaDB migration is already applied
	 */
	private async isScyllaMigrationApplied(version: string): Promise<boolean>
	{
		try
		{
			// Create migrations table if it doesn't exist
			await this.createScyllaMigrationsTable();

			const result = await this.scyllaClient.execute(
				'SELECT version FROM migrations WHERE version = ?',
				[version]
			);

			return result.rows.length > 0;
		}
		catch (error)
		{
			logger.warn('Error checking ScyllaDB migration status:', error);
			return false;
		}
	}

	/**
	 * Check if MariaDB migration is already applied
	 */
	private async isMariaMigrationApplied(version: string): Promise<boolean>
	{
		try
		{
			const result = await this.mariaClient.execute(
				'SELECT version FROM migrations WHERE version = ?',
				[version]
			);

			return result.rows.length > 0;
		}
		catch (error)
		{
			logger.warn('Error checking MariaDB migration status:', error);
			return false;
		}
	}

	/**
	 * Create ScyllaDB migrations table
	 */
	private async createScyllaMigrationsTable(): Promise<void>
	{
		const createTableQuery = `
			CREATE TABLE IF NOT EXISTS migrations (
				version text PRIMARY KEY,
				description text,
				applied_at timestamp
			);
		`;

		await this.scyllaClient.execute(createTableQuery);
	}

	/**
	 * Create MariaDB migrations table
	 */
	private async createMariaMigrationsTable(): Promise<void>
	{
		const createTableQuery = `
			CREATE TABLE IF NOT EXISTS migrations (
				version VARCHAR(50) PRIMARY KEY,
				description TEXT,
				applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
			) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
		`;

		await this.mariaClient.execute(createTableQuery);
	}

	/**
	 * Record ScyllaDB migration as applied
	 */
	private async recordScyllaMigration(version: string, description: string): Promise<void>
	{
		await this.scyllaClient.execute(
			'INSERT INTO migrations (version, description, applied_at) VALUES (?, ?, ?)',
			[version, description, new Date()]
		);
	}

	/**
	 * Execute Manticore script
	 */
	private async executeManticoreScript(script: string): Promise<void>
	{
		// For now, we'll use a simple approach to execute Manticore scripts
		// In a production environment, you might want to use the Manticore client's HTTP interface
		const client = this.manticoreClient.getClient();
		if (!client)
		{
			throw new Error('Manticore client not available');
		}

		// Parse and execute the script
		// This is a simplified implementation - in production, you'd want more robust SQL parsing
		const statements = script.split(';').filter(s => s.trim());

		for (const statement of statements)
		{
			if (statement.trim())
			{
				try
				{
					// Use the Manticore client's HTTP interface to execute the statement
					await client.post('/sql', { query: statement.trim() });
				}
				catch (error)
				{
					// Some statements might fail if they already exist, which is okay
					logger.warn('Manticore statement execution warning:', { statement: statement.substring(0, 100), error: error.message });
				}
			}
		}
	}

	/**
	 * Rollback migration for a specific database
	 */
	async rollbackMigration(database: 'ScyllaDB' | 'MariaDB' | 'Manticore', version: string): Promise<MigrationResult>
	{
		logger.info(`Rolling back ${database} migration ${version}...`);

		const startTime = Date.now();

		try
		{
			let migration: MigrationScript | undefined;

			switch (database)
			{
				case 'ScyllaDB':
					migration = SCYLLA_MIGRATION_SCRIPTS.find(m => m.version === version);
					break;
				case 'MariaDB':
					migration = MARIA_MIGRATION_SCRIPTS.find(m => m.version === version);
					break;
				case 'Manticore':
					migration = MANTICORE_MIGRATION_SCRIPTS.find(m => m.version === version);
					break;
			}

			if (!migration)
			{
				throw new Error(`Migration ${version} not found for ${database}`);
			}

			// Execute rollback scripts
			for (const script of migration.down)
			{
				switch (database)
				{
					case 'ScyllaDB':
						await this.scyllaClient.execute(script);
						break;
					case 'MariaDB':
						await this.mariaClient.execute(script);
						break;
					case 'Manticore':
						await this.executeManticoreScript(script);
						break;
				}
			}

			// Remove migration record
			if (database === 'ScyllaDB')
			{
				await this.scyllaClient.execute('DELETE FROM migrations WHERE version = ?', [version]);
			}
			else if (database === 'MariaDB')
			{
				await this.mariaClient.execute('DELETE FROM migrations WHERE version = ?', [version]);
			}

			const executionTime = Date.now() - startTime;

			logger.info(`${database} migration ${version} rolled back successfully`, {
				executionTime,
			});

			return {
				database,
				version,
				success: true,
				executionTime,
			};
		}
		catch (error)
		{
			const executionTime = Date.now() - startTime;

			logger.error(`${database} migration ${version} rollback failed:`, error);

			return {
				database,
				version,
				success: false,
				error: error.message,
				executionTime,
			};
		}
	}

	/**
	 * Get migration status for all databases
	 */
	async getMigrationStatus(): Promise<{
		scylla: Array<{ version: string; description: string; appliedAt: Date }>;
		maria: Array<{ version: string; description: string; appliedAt: Date }>;
		manticore: Array<{ version: string; description: string }>;
	}>
	{
		const status = {
			scylla: [],
			maria: [],
			manticore: [],
		};

		try
		{
			// Get ScyllaDB migration status
			await this.createScyllaMigrationsTable();
			const scyllaResult = await this.scyllaClient.execute('SELECT * FROM migrations ORDER BY version');
			status.scylla = scyllaResult.rows.map(row => ({
				version: row.version,
				description: row.description,
				appliedAt: row.applied_at,
			}));
		}
		catch (error)
		{
			logger.warn('Error getting ScyllaDB migration status:', error);
		}

		try
		{
			// Get MariaDB migration status
			await this.createMariaMigrationsTable();
			const mariaResult = await this.mariaClient.execute('SELECT * FROM migrations ORDER BY version');
			status.maria = mariaResult.rows.map(row => ({
				version: row.version,
				description: row.description,
				appliedAt: row.applied_at,
			}));
		}
		catch (error)
		{
			logger.warn('Error getting MariaDB migration status:', error);
		}

		// Manticore doesn't have a migrations table, so we'll just list available migrations
		status.manticore = MANTICORE_MIGRATION_SCRIPTS.map(m => ({
			version: m.version,
			description: m.description,
		}));

		return status;
	}

	/**
	 * Validate database schemas
	 */
	async validateSchemas(): Promise<{
		scylla: boolean;
		maria: boolean;
		manticore: boolean;
		errors: string[];
	}>
	{
		const result = {
			scylla: false,
			maria: false,
			manticore: false,
			errors: [],
		};

		// Validate ScyllaDB schema
		try
		{
			await this.scyllaClient.execute('SELECT * FROM domain_analysis LIMIT 1');
			await this.scyllaClient.execute('SELECT * FROM domain_rankings LIMIT 1');
			await this.scyllaClient.execute('SELECT * FROM domain_crawl_jobs LIMIT 1');
			result.scylla = true;
		}
		catch (error)
		{
			result.errors.push(`ScyllaDB schema validation failed: ${error.message}`);
		}

		// Validate MariaDB schema
		try
		{
			await this.mariaClient.execute('SELECT * FROM backlinks LIMIT 1');
			await this.mariaClient.execute('SELECT * FROM domain_categories LIMIT 1');
			await this.mariaClient.execute('SELECT * FROM domain_whois LIMIT 1');
			result.maria = true;
		}
		catch (error)
		{
			result.errors.push(`MariaDB schema validation failed: ${error.message}`);
		}

		// Validate Manticore schema
		try
		{
			await this.manticoreClient.searchDomains({ query: 'test', limit: 1 });
			result.manticore = true;
		}
		catch (error)
		{
			result.errors.push(`Manticore schema validation failed: ${error.message}`);
		}

		return result;
	}
}

export type {
	MigrationScriptType,
	MigrationResultType,
};

export default DatabaseMigrationManager;
