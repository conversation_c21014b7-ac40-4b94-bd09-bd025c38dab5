/**
 * Tests for WorkerDatabaseManager
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { WorkerDatabaseManager } from '../WorkerDatabaseManager';
import type { WorkerDatabaseConfig } from '../../types/DatabaseTypes';

// Mock the database clients
vi.mock('../../../../../shared/src/database/ScyllaClient');
vi.mock('../../../../../shared/src/database/MariaClient');
vi.mock('../../../../../shared/src/database/RedisClient');
vi.mock('../../../../../shared/src/database/ManticoreClient');

describe('WorkerDatabaseManager', () =>
{
	let databaseManager: WorkerDatabaseManager;
	let mockConfig: WorkerDatabaseConfig;

	beforeEach(() =>
	{
		mockConfig = {
			scylla: {
				hosts: ['localhost:9042'],
				keyspace: 'test_keyspace',
				localDataCenter: 'datacenter1',
			},
			maria: {
				host: 'localhost',
				port: 3306,
				user: 'test',
				password: 'test',
				database: 'test_db',
				connectionLimit: 5,
			},
			redis: {
				url: 'redis://localhost:6379',
				database: 0,
				maxRetries: 3,
			},
			manticore: {
				host: 'localhost',
				port: 9308,
				timeout: 30000,
			},
		};

		databaseManager = new WorkerDatabaseManager(mockConfig);
	});

	afterEach(async () =>
	{
		if (databaseManager.isManagerInitialized())
		{
			await databaseManager.close();
		}
	});

	it('should create database manager instance', () =>
	{
		expect(databaseManager).toBeDefined();
		expect(databaseManager.isManagerInitialized()).toBe(false);
	});

	it('should throw error when accessing clients before initialization', () =>
	{
		expect(() => databaseManager.getScyllaClient()).toThrow('ScyllaDB client not initialized');
		expect(() => databaseManager.getMariaClient()).toThrow('MariaDB client not initialized');
		expect(() => databaseManager.getRedisClient()).toThrow('Redis client not initialized');
		expect(() => databaseManager.getManticoreClient()).toThrow('Manticore client not initialized');
	});
});
