import { DatabaseManager } from '@shared';

/**
 * Worker service: shared DatabaseManager singleton access.
 * Uses globalThis cache in dev to survive HMR without creating extra connections.
 */
declare global
{
	// eslint-disable-next-line no-var
	var __worker_db: Awaited<ReturnType<typeof DatabaseManager.getInstance>> | undefined;
}

let db: Awaited<ReturnType<typeof DatabaseManager.getInstance>> | null = null;

async function getDatabase()
{
	if (db) return db;

	db = globalThis.__worker_db ?? await DatabaseManager.getInstance();

	if (process.env.NODE_ENV !== 'production')
	{
		globalThis.__worker_db = db;
	}

	return db;
}

export default getDatabase;
