/**
 * ScyllaDB Schema Definitions
 *
 * Contains all table schemas extracted from the three source services:
 * - crawler service: domain_analysis table
 * - ranking-engine service: domain_rankings, domain_ranking_history tables
 * - scheduler service: domain_crawl_jobs table
 */

export const SCYLLA_KEYSPACE_SCHEMA = `
CREATE KEYSPACE IF NOT EXISTS domain_ranking
WITH REPLICATION = {
  'class': 'SimpleStrategy',
  'replication_factor': 3
};
`;

export const SCYLLA_TABLE_SCHEMAS = {
	// From crawler service
	domain_analysis: `
		CREATE TABLE IF NOT EXISTS domain_analysis (
			domain text PRIMARY KEY,
			global_rank int,
			category text,
			category_rank int,
			performance_metrics text,
			security_metrics text,
			seo_metrics text,
			technical_metrics text,
			technologies set<text>,
			server_info text,
			domain_age_days int,
			registration_date timestamp,
			expiration_date timestamp,
			registrar text,
			dns_records text,
			screenshot_urls list<text>,
			subdomains set<text>,
			last_crawled timestamp,
			crawl_status text,
			robots_analysis text,
			ssl_analysis text,
			homepage_analysis text,
			created_at timestamp,
			updated_at timestamp
		);
	`,

	// From ranking-engine service
	domain_rankings: `
		CREATE TABLE IF NOT EXISTS domain_rankings (
			ranking_type text,
			rank int,
			domain text,
			overall_score double,
			performance_score double,
			security_score double,
			seo_score double,
			technical_score double,
			backlink_score double,
			traffic_estimate bigint,
			last_updated timestamp,
			grade text,
			category text,
			PRIMARY KEY (ranking_type, rank)
		) WITH CLUSTERING ORDER BY (rank ASC);
	`,

	domain_ranking_history: `
		CREATE TABLE IF NOT EXISTS domain_ranking_history (
			domain text,
			ranking_type text,
			date text,
			rank int,
			overall_score double,
			previous_rank int,
			rank_change int,
			PRIMARY KEY ((domain, ranking_type), date)
		) WITH CLUSTERING ORDER BY (date DESC);
	`,

	// From scheduler service
	domain_crawl_jobs: `
		CREATE TABLE IF NOT EXISTS domain_crawl_jobs (
			job_id uuid PRIMARY KEY,
			domain text,
			crawl_type text,
			priority text,
			status text,
			retry_count int,
			scheduled_at timestamp,
			started_at timestamp,
			completed_at timestamp,
			error_message text,
			user_agent text,
			pages_to_crawl list<text>,
			metadata text,
			created_at timestamp,
			updated_at timestamp
		);
	`,

	// Additional indexes for better query performance
	domain_analysis_by_category: `
		CREATE INDEX IF NOT EXISTS domain_analysis_category_idx
		ON domain_analysis (category);
	`,

	domain_analysis_by_rank: `
		CREATE INDEX IF NOT EXISTS domain_analysis_rank_idx
		ON domain_analysis (global_rank);
	`,

	domain_analysis_by_crawl_status: `
		CREATE INDEX IF NOT EXISTS domain_analysis_crawl_status_idx
		ON domain_analysis (crawl_status);
	`,

	domain_rankings_by_domain: `
		CREATE INDEX IF NOT EXISTS domain_rankings_domain_idx
		ON domain_rankings (domain);
	`,

	domain_crawl_jobs_by_domain: `
		CREATE INDEX IF NOT EXISTS domain_crawl_jobs_domain_idx
		ON domain_crawl_jobs (domain);
	`,

	domain_crawl_jobs_by_status: `
		CREATE INDEX IF NOT EXISTS domain_crawl_jobs_status_idx
		ON domain_crawl_jobs (status);
	`,

	domain_crawl_jobs_by_scheduled_at: `
		CREATE INDEX IF NOT EXISTS domain_crawl_jobs_scheduled_at_idx
		ON domain_crawl_jobs (scheduled_at);
	`,
};

export const SCYLLA_MATERIALIZED_VIEWS = {
	// View for getting domains by ranking type and domain
	domain_rankings_by_domain_and_type: `
		CREATE MATERIALIZED VIEW IF NOT EXISTS domain_rankings_by_domain_and_type AS
		SELECT ranking_type, domain, rank, overall_score, performance_score,
		       security_score, seo_score, technical_score, backlink_score,
		       traffic_estimate, last_updated, grade, category
		FROM domain_rankings
		WHERE ranking_type IS NOT NULL AND domain IS NOT NULL AND rank IS NOT NULL
		PRIMARY KEY (domain, ranking_type, rank);
	`,

	// View for getting pending crawl jobs
	pending_crawl_jobs: `
		CREATE MATERIALIZED VIEW IF NOT EXISTS pending_crawl_jobs AS
		SELECT job_id, domain, crawl_type, priority, status, retry_count,
		       scheduled_at, started_at, completed_at, error_message,
		       user_agent, pages_to_crawl, metadata, created_at, updated_at
		FROM domain_crawl_jobs
		WHERE status IS NOT NULL AND job_id IS NOT NULL AND scheduled_at IS NOT NULL
		PRIMARY KEY (status, scheduled_at, job_id);
	`,

	// View for getting jobs by domain
	crawl_jobs_by_domain: `
		CREATE MATERIALIZED VIEW IF NOT EXISTS crawl_jobs_by_domain AS
		SELECT job_id, domain, crawl_type, priority, status, retry_count,
		       scheduled_at, started_at, completed_at, error_message,
		       user_agent, pages_to_crawl, metadata, created_at, updated_at
		FROM domain_crawl_jobs
		WHERE domain IS NOT NULL AND job_id IS NOT NULL AND scheduled_at IS NOT NULL
		PRIMARY KEY (domain, scheduled_at, job_id);
	`,
};

export const SCYLLA_MIGRATION_SCRIPTS = [
	{
		version: '001',
		description: 'Create keyspace and initial tables',
		up: [
			SCYLLA_KEYSPACE_SCHEMA,
			SCYLLA_TABLE_SCHEMAS.domain_analysis,
			SCYLLA_TABLE_SCHEMAS.domain_rankings,
			SCYLLA_TABLE_SCHEMAS.domain_ranking_history,
			SCYLLA_TABLE_SCHEMAS.domain_crawl_jobs,
		],
		down: [
			'DROP TABLE IF EXISTS domain_crawl_jobs;',
			'DROP TABLE IF EXISTS domain_ranking_history;',
			'DROP TABLE IF EXISTS domain_rankings;',
			'DROP TABLE IF EXISTS domain_analysis;',
			'DROP KEYSPACE IF EXISTS domain_ranking;',
		],
	},
	{
		version: '002',
		description: 'Add indexes for better query performance',
		up: [
			SCYLLA_TABLE_SCHEMAS.domain_analysis_by_category,
			SCYLLA_TABLE_SCHEMAS.domain_analysis_by_rank,
			SCYLLA_TABLE_SCHEMAS.domain_analysis_by_crawl_status,
			SCYLLA_TABLE_SCHEMAS.domain_rankings_by_domain,
			SCYLLA_TABLE_SCHEMAS.domain_crawl_jobs_by_domain,
			SCYLLA_TABLE_SCHEMAS.domain_crawl_jobs_by_status,
			SCYLLA_TABLE_SCHEMAS.domain_crawl_jobs_by_scheduled_at,
		],
		down: [
			'DROP INDEX IF EXISTS domain_crawl_jobs_scheduled_at_idx;',
			'DROP INDEX IF EXISTS domain_crawl_jobs_status_idx;',
			'DROP INDEX IF EXISTS domain_crawl_jobs_domain_idx;',
			'DROP INDEX IF EXISTS domain_rankings_domain_idx;',
			'DROP INDEX IF EXISTS domain_analysis_crawl_status_idx;',
			'DROP INDEX IF EXISTS domain_analysis_rank_idx;',
			'DROP INDEX IF EXISTS domain_analysis_category_idx;',
		],
	},
	{
		version: '003',
		description: 'Add materialized views for optimized queries',
		up: [
			SCYLLA_MATERIALIZED_VIEWS.domain_rankings_by_domain_and_type,
			SCYLLA_MATERIALIZED_VIEWS.pending_crawl_jobs,
			SCYLLA_MATERIALIZED_VIEWS.crawl_jobs_by_domain,
		],
		down: [
			'DROP MATERIALIZED VIEW IF EXISTS crawl_jobs_by_domain;',
			'DROP MATERIALIZED VIEW IF EXISTS pending_crawl_jobs;',
			'DROP MATERIALIZED VIEW IF EXISTS domain_rankings_by_domain_and_type;',
		],
	},
];

export default {
	SCYLLA_KEYSPACE_SCHEMA,
	SCYLLA_TABLE_SCHEMAS,
	SCYLLA_MATERIALIZED_VIEWS,
	SCYLLA_MIGRATION_SCRIPTS,
};
