/**
 * Manticore Search Schema Definitions
 *
 * Contains search index schemas extracted from all three services for:
 * - Domain search and filtering
 * - Full-text search capabilities
 * - Faceted search and analytics
 */

export const MANTICORE_INDEX_SCHEMAS = {
	// Main domains search index
	domains_index: {
		settings: {
			type: 'rt',
			path: '/var/lib/manticore/domains_index',
		},
		mappings: {
			properties: {
				// Basic domain information
				domain: {
					type: 'text',
					stored: true,
				},
				title: {
					type: 'text',
					stored: true,
				},
				description: {
					type: 'text',
					stored: true,
				},
				content: {
					type: 'text',
					stored: false, // Full page content for search, not stored
				},

				// Classification fields
				category: {
					type: 'string',
					stored: true,
					attribute: true,
				},
				country: {
					type: 'string',
					stored: true,
					attribute: true,
				},
				language: {
					type: 'string',
					stored: true,
					attribute: true,
				},

				// Technology stack
				technologies: {
					type: 'multi',
					stored: true,
					attribute: true,
				},

				// Domain metadata
				registrar: {
					type: 'string',
					stored: true,
					attribute: true,
				},
				domain_age_days: {
					type: 'integer',
					stored: true,
					attribute: true,
				},

				// Ranking and scoring
				global_rank: {
					type: 'integer',
					stored: true,
					attribute: true,
				},
				overall_score: {
					type: 'float',
					stored: true,
					attribute: true,
				},
				performance_score: {
					type: 'float',
					stored: true,
					attribute: true,
				},
				security_score: {
					type: 'float',
					stored: true,
					attribute: true,
				},
				seo_score: {
					type: 'float',
					stored: true,
					attribute: true,
				},
				technical_score: {
					type: 'float',
					stored: true,
					attribute: true,
				},
				backlink_score: {
					type: 'float',
					stored: true,
					attribute: true,
				},

				// Traffic and engagement
				traffic_estimate: {
					type: 'bigint',
					stored: true,
					attribute: true,
				},

				// Security and quality indicators
				ssl_grade: {
					type: 'string',
					stored: true,
					attribute: true,
				},
				mobile_friendly_score: {
					type: 'float',
					stored: true,
					attribute: true,
				},
				accessibility_score: {
					type: 'float',
					stored: true,
					attribute: true,
				},

				// Timestamps
				last_updated: {
					type: 'timestamp',
					stored: true,
					attribute: true,
				},
			},
		},
	},

	// Categories index for category-specific searches
	categories_index: {
		settings: {
			type: 'rt',
			path: '/var/lib/manticore/categories_index',
		},
		mappings: {
			properties: {
				category_id: {
					type: 'integer',
					stored: true,
					attribute: true,
				},
				category_name: {
					type: 'text',
					stored: true,
				},
				category_description: {
					type: 'text',
					stored: true,
				},
				parent_category_id: {
					type: 'integer',
					stored: true,
					attribute: true,
				},
				domain_count: {
					type: 'integer',
					stored: true,
					attribute: true,
				},
				average_score: {
					type: 'float',
					stored: true,
					attribute: true,
				},
			},
		},
	},

	// Technologies index for technology-based searches
	technologies_index: {
		settings: {
			type: 'rt',
			path: '/var/lib/manticore/technologies_index',
		},
		mappings: {
			properties: {
				technology_name: {
					type: 'text',
					stored: true,
				},
				technology_type: {
					type: 'string',
					stored: true,
					attribute: true,
				},
				domain_count: {
					type: 'integer',
					stored: true,
					attribute: true,
				},
				average_score: {
					type: 'float',
					stored: true,
					attribute: true,
				},
				popularity_rank: {
					type: 'integer',
					stored: true,
					attribute: true,
				},
			},
		},
	},
};

export const MANTICORE_INDEX_CREATION_SCRIPTS = {
	domains_index: `
		CREATE TABLE domains_index (
			id bigint,
			domain text indexed stored,
			title text indexed stored,
			description text indexed stored,
			content text indexed,
			category string attribute stored,
			country string attribute stored,
			language string attribute stored,
			technologies multi attribute stored,
			registrar string attribute stored,
			domain_age_days integer attribute stored,
			global_rank integer attribute stored,
			overall_score float attribute stored,
			performance_score float attribute stored,
			security_score float attribute stored,
			seo_score float attribute stored,
			technical_score float attribute stored,
			backlink_score float attribute stored,
			traffic_estimate bigint attribute stored,
			ssl_grade string attribute stored,
			mobile_friendly_score float attribute stored,
			accessibility_score float attribute stored,
			last_updated timestamp attribute stored
		) morphology='stem_en' html_strip='1' index_exact_words='1';
	`,

	categories_index: `
		CREATE TABLE categories_index (
			id bigint,
			category_id integer attribute stored,
			category_name text indexed stored,
			category_description text indexed stored,
			parent_category_id integer attribute stored,
			domain_count integer attribute stored,
			average_score float attribute stored
		) morphology='stem_en' html_strip='1';
	`,

	technologies_index: `
		CREATE TABLE technologies_index (
			id bigint,
			technology_name text indexed stored,
			technology_type string attribute stored,
			domain_count integer attribute stored,
			average_score float attribute stored,
			popularity_rank integer attribute stored
		) morphology='stem_en' html_strip='1';
	`,
};

export const MANTICORE_COMMON_QUERIES = {
	// Search domains by text query
	searchDomains: `
		SELECT *, WEIGHT() as relevance_score
		FROM domains_index
		WHERE MATCH('@(domain,title,description) :query')
		ORDER BY relevance_score DESC, overall_score DESC
		LIMIT :limit OFFSET :offset;
	`,

	// Get top domains by category
	topDomainsByCategory: `
		SELECT *
		FROM domains_index
		WHERE category = ':category'
		ORDER BY overall_score DESC, global_rank ASC
		LIMIT :limit;
	`,

	// Search domains by technology
	domainsByTechnology: `
		SELECT *
		FROM domains_index
		WHERE ANY(technologies) = ':technology'
		ORDER BY overall_score DESC
		LIMIT :limit;
	`,

	// Get domains with high security scores
	secureDomainsQuery: `
		SELECT *
		FROM domains_index
		WHERE security_score >= :min_score AND ssl_grade IN ('A+', 'A', 'A-')
		ORDER BY security_score DESC
		LIMIT :limit;
	`,

	// Get fast-loading domains
	fastDomainsQuery: `
		SELECT *
		FROM domains_index
		WHERE performance_score >= :min_score
		ORDER BY performance_score DESC
		LIMIT :limit;
	`,

	// Faceted search with aggregations
	facetedSearch: `
		SELECT *, COUNT(*) as total_count
		FROM domains_index
		WHERE MATCH(':query')
		GROUP BY category, country, ssl_grade
		ORDER BY total_count DESC;
	`,
};

export const MANTICORE_OPTIMIZATION_SETTINGS = {
	// RT index settings for better performance
	rt_mem_limit: '512M',
	rt_flush_period: '3600',
	max_matches: '10000',

	// Search settings
	max_query_time: '30000',
	subtree_docs_cache: '8M',
	subtree_hits_cache: '16M',

	// Indexing settings
	morphology: 'stem_en',
	html_strip: '1',
	index_exact_words: '1',
	expand_keywords: '1',
};

export const MANTICORE_MIGRATION_SCRIPTS = [
	{
		version: '001',
		description: 'Create main domains search index',
		up: [
			MANTICORE_INDEX_CREATION_SCRIPTS.domains_index,
		],
		down: [
			'DROP TABLE IF EXISTS domains_index;',
		],
	},
	{
		version: '002',
		description: 'Create categories and technologies indexes',
		up: [
			MANTICORE_INDEX_CREATION_SCRIPTS.categories_index,
			MANTICORE_INDEX_CREATION_SCRIPTS.technologies_index,
		],
		down: [
			'DROP TABLE IF EXISTS technologies_index;',
			'DROP TABLE IF EXISTS categories_index;',
		],
	},
	{
		version: '003',
		description: 'Optimize index settings for production',
		up: [
			'ALTER TABLE domains_index rt_mem_limit=\'512M\';',
			'ALTER TABLE domains_index rt_flush_period=\'3600\';',
			'ALTER TABLE categories_index rt_mem_limit=\'128M\';',
			'ALTER TABLE technologies_index rt_mem_limit=\'128M\';',
		],
		down: [
			// Revert to default settings
			'ALTER TABLE domains_index rt_mem_limit=\'128M\';',
			'ALTER TABLE domains_index rt_flush_period=\'900\';',
			'ALTER TABLE categories_index rt_mem_limit=\'32M\';',
			'ALTER TABLE technologies_index rt_mem_limit=\'32M\';',
		],
	},
];

export default {
	MANTICORE_INDEX_SCHEMAS,
	MANTICORE_INDEX_CREATION_SCRIPTS,
	MANTICORE_COMMON_QUERIES,
	MANTICORE_OPTIMIZATION_SETTINGS,
	MANTICORE_MIGRATION_SCRIPTS,
};
