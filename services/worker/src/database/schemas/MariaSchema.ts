/**
 * MariaDB Schema Definitions
 *
 * Contains all table schemas extracted from the ranking-engine service:
 * - backlinks table for link quality analysis
 * - domain_categories and domain_category_mapping for categorization
 * - domain_whois for registration information
 * - domain_reviews for user feedback data
 */

export const MARIA_TABLE_SCHEMAS = {
	// Backlinks table for link quality analysis
	backlinks: `
		CREATE TABLE IF NOT EXISTS backlinks (
			id VARCHAR(255) PRIMARY KEY,
			source_domain VARCHAR(255) NOT NULL,
			target_domain VARCHAR(255) NOT NULL,
			link_quality_score DECIMAL(5,2) NOT NULL DEFAULT 0.00,
			anchor_text TEXT,
			link_type ENUM('follow', 'nofollow', 'sponsored', 'ugc') NOT NULL DEFAULT 'follow',
			discovered_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
			last_verified TIMESTAMP NULL,
			is_active BOOLEAN NOT NULL DEFAULT TRUE,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

			INDEX idx_source_domain (source_domain),
			INDEX idx_target_domain (target_domain),
			INDEX idx_link_quality (link_quality_score DESC),
			INDEX idx_discovered_at (discovered_at DESC),
			INDEX idx_is_active (is_active),
			INDEX idx_target_active (target_domain, is_active),
			INDEX idx_source_active (source_domain, is_active)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
	`,

	// Domain categories for classification
	domain_categories: `
		CREATE TABLE IF NOT EXISTS domain_categories (
			id INT AUTO_INCREMENT PRIMARY KEY,
			name VARCHAR(255) NOT NULL UNIQUE,
			description TEXT,
			parent_category_id INT NULL,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

			FOREIGN KEY (parent_category_id) REFERENCES domain_categories(id) ON DELETE SET NULL,
			INDEX idx_parent_category (parent_category_id),
			INDEX idx_name (name)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
	`,

	// Domain to category mapping
	domain_category_mapping: `
		CREATE TABLE IF NOT EXISTS domain_category_mapping (
			domain VARCHAR(255) NOT NULL,
			category_id INT NOT NULL,
			confidence_score DECIMAL(5,2) NOT NULL DEFAULT 0.00,
			assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

			PRIMARY KEY (domain, category_id),
			FOREIGN KEY (category_id) REFERENCES domain_categories(id) ON DELETE CASCADE,
			INDEX idx_category_confidence (category_id, confidence_score DESC),
			INDEX idx_domain_confidence (domain, confidence_score DESC)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
	`,

	// Domain WHOIS information
	domain_whois: `
		CREATE TABLE IF NOT EXISTS domain_whois (
			domain VARCHAR(255) PRIMARY KEY,
			registrar VARCHAR(255),
			registration_date DATE,
			expiration_date DATE,
			name_servers TEXT,
			registrant_country VARCHAR(2),
			registrant_organization VARCHAR(255),
			privacy_protected BOOLEAN NOT NULL DEFAULT FALSE,
			last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

			INDEX idx_registrar (registrar),
			INDEX idx_expiration_date (expiration_date),
			INDEX idx_registration_date (registration_date),
			INDEX idx_registrant_country (registrant_country),
			INDEX idx_privacy_protected (privacy_protected)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
	`,

	// Domain reviews and ratings
	domain_reviews: `
		CREATE TABLE IF NOT EXISTS domain_reviews (
			id VARCHAR(255) PRIMARY KEY,
			domain VARCHAR(255) NOT NULL,
			source VARCHAR(100) NOT NULL,
			rating DECIMAL(3,2) NOT NULL,
			review_text TEXT,
			review_date TIMESTAMP NOT NULL,
			sentiment_score DECIMAL(3,2),
			verified BOOLEAN NOT NULL DEFAULT FALSE,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

			INDEX idx_domain (domain),
			INDEX idx_source (source),
			INDEX idx_rating (rating DESC),
			INDEX idx_review_date (review_date DESC),
			INDEX idx_verified (verified),
			INDEX idx_domain_rating (domain, rating DESC),
			INDEX idx_domain_date (domain, review_date DESC)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
	`,

	// User management tables (from admin service)
	users: `
		CREATE TABLE IF NOT EXISTS users (
			id INT AUTO_INCREMENT PRIMARY KEY,
			username VARCHAR(255) NOT NULL UNIQUE,
			email VARCHAR(255) NOT NULL UNIQUE,
			password_hash VARCHAR(255) NOT NULL,
			role ENUM('admin', 'user', 'viewer') NOT NULL DEFAULT 'user',
			is_active BOOLEAN NOT NULL DEFAULT TRUE,
			last_login TIMESTAMP NULL,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

			INDEX idx_username (username),
			INDEX idx_email (email),
			INDEX idx_role (role),
			INDEX idx_is_active (is_active)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
	`,

	// User sessions
	user_sessions: `
		CREATE TABLE IF NOT EXISTS user_sessions (
			id VARCHAR(255) PRIMARY KEY,
			user_id INT NOT NULL,
			ip_address VARCHAR(45),
			user_agent TEXT,
			expires_at TIMESTAMP NOT NULL,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

			FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
			INDEX idx_user_id (user_id),
			INDEX idx_expires_at (expires_at)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
	`,

	// Configuration settings
	configuration: `
		CREATE TABLE IF NOT EXISTS configuration (
			key_name VARCHAR(255) PRIMARY KEY,
			value_data TEXT NOT NULL,
			description TEXT,
			category VARCHAR(100) NOT NULL DEFAULT 'general',
			is_encrypted BOOLEAN NOT NULL DEFAULT FALSE,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

			INDEX idx_category (category)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
	`,
};

export const MARIA_INITIAL_DATA = {
	// Default domain categories
	domain_categories: [
		{ name: 'Technology', description: 'Technology and software companies' },
		{ name: 'E-commerce', description: 'Online retail and shopping' },
		{ name: 'News & Media', description: 'News websites and media outlets' },
		{ name: 'Social Media', description: 'Social networking platforms' },
		{ name: 'Entertainment', description: 'Entertainment and gaming' },
		{ name: 'Education', description: 'Educational institutions and resources' },
		{ name: 'Finance', description: 'Financial services and banking' },
		{ name: 'Healthcare', description: 'Healthcare and medical services' },
		{ name: 'Travel', description: 'Travel and tourism' },
		{ name: 'Business', description: 'Business and professional services' },
		{ name: 'Government', description: 'Government and public services' },
		{ name: 'Non-profit', description: 'Non-profit organizations' },
		{ name: 'Personal', description: 'Personal websites and blogs' },
		{ name: 'Other', description: 'Uncategorized domains' },
	],

	// Default configuration
	configuration: [
		{
			key_name: 'crawl_batch_size',
			value_data: '100',
			description: 'Number of domains to process in each crawl batch',
			category: 'crawler',
		},
		{
			key_name: 'ranking_update_interval',
			value_data: '3600',
			description: 'Interval in seconds between ranking updates',
			category: 'ranking',
		},
		{
			key_name: 'cache_ttl_domain_analysis',
			value_data: '3600',
			description: 'TTL in seconds for domain analysis cache',
			category: 'cache',
		},
		{
			key_name: 'cache_ttl_domain_ranking',
			value_data: '1800',
			description: 'TTL in seconds for domain ranking cache',
			category: 'cache',
		},
		{
			key_name: 'max_concurrent_crawls',
			value_data: '10',
			description: 'Maximum number of concurrent crawl operations',
			category: 'crawler',
		},
		{
			key_name: 'backlink_quality_threshold',
			value_data: '5.0',
			description: 'Minimum quality score for backlinks to be considered',
			category: 'ranking',
		},
	],
};

export const MARIA_MIGRATION_SCRIPTS = [
	{
		version: '001',
		description: 'Create initial tables',
		up: [
			MARIA_TABLE_SCHEMAS.users,
			MARIA_TABLE_SCHEMAS.user_sessions,
			MARIA_TABLE_SCHEMAS.configuration,
			MARIA_TABLE_SCHEMAS.domain_categories,
			MARIA_TABLE_SCHEMAS.domain_category_mapping,
			MARIA_TABLE_SCHEMAS.backlinks,
			MARIA_TABLE_SCHEMAS.domain_whois,
			MARIA_TABLE_SCHEMAS.domain_reviews,
		],
		down: [
			'DROP TABLE IF EXISTS domain_reviews;',
			'DROP TABLE IF EXISTS domain_whois;',
			'DROP TABLE IF EXISTS backlinks;',
			'DROP TABLE IF EXISTS domain_category_mapping;',
			'DROP TABLE IF EXISTS domain_categories;',
			'DROP TABLE IF EXISTS configuration;',
			'DROP TABLE IF EXISTS user_sessions;',
			'DROP TABLE IF EXISTS users;',
		],
	},
	{
		version: '002',
		description: 'Insert initial data',
		up: [
			// Insert default categories
			`INSERT IGNORE INTO domain_categories (name, description) VALUES
				('Technology', 'Technology and software companies'),
				('E-commerce', 'Online retail and shopping'),
				('News & Media', 'News websites and media outlets'),
				('Social Media', 'Social networking platforms'),
				('Entertainment', 'Entertainment and gaming'),
				('Education', 'Educational institutions and resources'),
				('Finance', 'Financial services and banking'),
				('Healthcare', 'Healthcare and medical services'),
				('Travel', 'Travel and tourism'),
				('Business', 'Business and professional services'),
				('Government', 'Government and public services'),
				('Non-profit', 'Non-profit organizations'),
				('Personal', 'Personal websites and blogs'),
				('Other', 'Uncategorized domains');`,

			// Insert default configuration
			`INSERT IGNORE INTO configuration (key_name, value_data, description, category) VALUES
				('crawl_batch_size', '100', 'Number of domains to process in each crawl batch', 'crawler'),
				('ranking_update_interval', '3600', 'Interval in seconds between ranking updates', 'ranking'),
				('cache_ttl_domain_analysis', '3600', 'TTL in seconds for domain analysis cache', 'cache'),
				('cache_ttl_domain_ranking', '1800', 'TTL in seconds for domain ranking cache', 'cache'),
				('max_concurrent_crawls', '10', 'Maximum number of concurrent crawl operations', 'crawler'),
				('backlink_quality_threshold', '5.0', 'Minimum quality score for backlinks to be considered', 'ranking');`,
		],
		down: [
			'DELETE FROM configuration WHERE category IN ("crawler", "ranking", "cache");',
			'DELETE FROM domain_categories WHERE name IN ("Technology", "E-commerce", "News & Media", "Social Media", "Entertainment", "Education", "Finance", "Healthcare", "Travel", "Business", "Government", "Non-profit", "Personal", "Other");',
		],
	},
	{
		version: '003',
		description: 'Add performance optimization indexes',
		up: [
			'CREATE INDEX idx_backlinks_composite ON backlinks (target_domain, is_active, link_quality_score DESC);',
			'CREATE INDEX idx_domain_whois_expiring ON domain_whois (expiration_date) WHERE expiration_date >= CURDATE();',
			'CREATE INDEX idx_reviews_composite ON domain_reviews (domain, verified, rating DESC);',
		],
		down: [
			'DROP INDEX idx_reviews_composite ON domain_reviews;',
			'DROP INDEX idx_domain_whois_expiring ON domain_whois;',
			'DROP INDEX idx_backlinks_composite ON backlinks;',
		],
	},
];

export default {
	MARIA_TABLE_SCHEMAS,
	MARIA_INITIAL_DATA,
	MARIA_MIGRATION_SCRIPTS,
};
