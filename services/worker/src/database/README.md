# Database Module

This directory will contain all database management functionality extracted from all three source services.

## Planned Structure

- `managers/` - Database managers (ScyllaClient, MariaClient, RedisClient, ManticoreClient)
- `operations/` - Database operations and queries
- `schemas/` - Database schemas and migrations
- `utils/` - Database utilities and helpers

## Extraction Tasks

This functionality will be extracted in tasks 2, 12:

- Task 2: Extract and consolidate database management functionality
- Task 12: Extract search indexing and data synchronization functionality

All database operations from the three source services will be consolidated here.
