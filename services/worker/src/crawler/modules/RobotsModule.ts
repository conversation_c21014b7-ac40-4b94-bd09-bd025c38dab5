import { DataCollectionModule, DataCollectionModuleConfig } from '../core/DataCollectionModule';
import RobotsAnalyzer, { RobotsAnalysisResult } from '../analyzers/RobotsAnalyzer';

/**
 * Robots.txt Data Collection Module
 */
class RobotsModule extends DataCollectionModule<RobotsAnalysisResult>
{
	private analyzer: RobotsAnalyzer;

	constructor()
	{
		const config: DataCollectionModuleConfig = {
			name: 'robots',
			priority: 100, // High priority - needed for crawl compliance
			timeout: 10000, // 10 seconds
			retryAttempts: 2,
			retryDelay: 1000, // 1 second
			dependencies: [], // No dependencies
		};

		super(config);
		this.analyzer = new RobotsAnalyzer();
	}

	protected async collect(domain: string): Promise<RobotsAnalysisResult>
	{
		return this.analyzer.analyzeRobotsTxt(domain);
	}
}

export { RobotsModule };

export default RobotsModule;
