import { RedisClientWrapper, ScyllaClient, Logger } from '@shared';
import { DataCollectionModule, DataCollectionModuleConfig } from '../core/DataCollectionModule';
import ScreenshotAnalyzer, { ScreenshotResult } from '../analyzers/ScreenshotAnalyzer';

const logger = Logger.getLogger('ScreenshotModule');

/**
 * Screenshot Capture Data Collection Module with caching and storage
 */
class ScreenshotModule extends DataCollectionModule<ScreenshotResult>
{
	private analyzer: ScreenshotAnalyzer;

	private redisClient: RedisClientWrapper;

	private scyllaClient: ScyllaClient;

	constructor(redisClient?: RedisClientWrapper, scyllaClient?: ScyllaClient)
	{
		const config: DataCollectionModuleConfig = {
			name: 'screenshot',
			priority: 30, // Medium priority - resource intensive but valuable
			timeout: 45000, // 45 seconds - screenshots can take time
			retryAttempts: 2,
			retryDelay: 5000, // 5 seconds between retries
			dependencies: ['dns', 'ssl'], // Ensure domain is accessible and secure
		};

		super(config);

		// Initialize database clients
		this.redisClient = redisClient || new RedisClientWrapper();
		this.scyllaClient = scyllaClient || new ScyllaClient();

		// Initialize analyzer
		this.analyzer = new ScreenshotAnalyzer();
	}

	/**
	 * Initialize the module and database connections
	 */
	async initialize(): Promise<void>
	{
		try
		{
			// Connect to Redis if not already connected
			if (!this.redisClient.getClient())
			{
				await this.redisClient.connect();
				logger.info('Redis client connected for ScreenshotModule');
			}

			// Connect to ScyllaDB if not already connected
			if (!this.scyllaClient.getClient())
			{
				await this.scyllaClient.connect();
				logger.info('ScyllaDB client connected for ScreenshotModule');
			}

			// Validate screenshot capture capability
			const capability = await this.analyzer.validateCapability();
			if (!capability.browserlessAvailable)
			{
				logger.warn('Browserless service not available - screenshots will fail');
			}
			if (!capability.imageProxyAvailable)
			{
				logger.warn('Image proxy service not available - optimization will be skipped');
			}
		}
		catch (error)
		{
			logger.error('Failed to initialize ScreenshotModule:', error);
			throw error;
		}
	}

	/**
	 * Cleanup database connections
	 */
	async cleanup(): Promise<void>
	{
		try
		{
			await this.redisClient.disconnect();
			await this.scyllaClient.disconnect();
			logger.info('ScreenshotModule database connections closed');
		}
		catch (error)
		{
			logger.warn('Error during ScreenshotModule cleanup:', error);
		}
	}

	protected async collect(domain: string): Promise<ScreenshotResult>
	{
		// Check cache first
		const cached = await this.getCachedScreenshot(domain);
		if (cached && this.isScreenshotFresh(cached))
		{
			logger.debug(`Using cached screenshot for domain: ${domain}`);
			return cached;
		}

		// Capture fresh screenshots
		const result = await this.analyzer.captureScreenshots(domain);

		// Store in cache and database
		await this.storeScreenshot(result);

		return result;
	}

	/**
	 * Capture screenshots without using cache (force fresh capture)
	 */
	async captureFresh(domain: string): Promise<ScreenshotResult>
	{
		const result = await this.analyzer.captureScreenshots(domain);
		await this.storeScreenshot(result);
		return result;
	}

	/**
	 * Get cached screenshot result
	 */
	async getCachedScreenshot(domain: string): Promise<ScreenshotResult | null>
	{
		try
		{
			const cacheKey = `screenshot:${domain}`;
			return await this.redisClient.get<ScreenshotResult>(cacheKey);
		}
		catch (error)
		{
			logger.warn(`Failed to get cached screenshot for ${domain}:`, error);
			return null;
		}
	}

	/**
	 * Store screenshot result in cache and database
	 */
	private async storeScreenshot(result: ScreenshotResult): Promise<void>
	{
		try
		{
			// Store in Redis cache (24 hour TTL)
			const cacheKey = `screenshot:${result.domain}`;
			await this.redisClient.set(cacheKey, result, 86400);

			// Store in ScyllaDB for long-term storage
			await this.storeInDatabase(result);

			logger.debug(`Screenshot stored for domain: ${result.domain}`);
		}
		catch (error)
		{
			logger.error(`Failed to store screenshot for ${result.domain}:`, error);
		}
	}

	/**
	 * Store screenshot data in ScyllaDB
	 */
	private async storeInDatabase(result: ScreenshotResult): Promise<void>
	{
		try
		{
			const query = `
				UPDATE domain_analysis
				SET screenshot_urls = ?,
					last_crawled = ?
				WHERE domain = ?
			`;

			const screenshotUrls = [];
			if (result.screenshots.desktop.url)
			{
				screenshotUrls.push(`desktop:${result.screenshots.desktop.url}`);
			}
			if (result.screenshots.mobile.url)
			{
				screenshotUrls.push(`mobile:${result.screenshots.mobile.url}`);
			}

			await this.scyllaClient.execute(query, [
				screenshotUrls,
				new Date(),
				result.domain,
			]);

			// Also store detailed screenshot metadata with enhanced fields
			const metadataQuery = `
				INSERT INTO domain_screenshots (
					domain,
					capture_date,
					desktop_url,
					desktop_optimized_url,
					desktop_size,
					desktop_compression_ratio,
					desktop_error,
					mobile_url,
					mobile_optimized_url,
					mobile_size,
					mobile_compression_ratio,
					mobile_error,
					capture_time_ms,
					browserless_version,
					capture_settings,
					optimization_settings,
					last_updated
				) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
			`;

			await this.scyllaClient.execute(metadataQuery, [
				result.domain,
				new Date(),
				result.screenshots.desktop.url || null,
				result.screenshots.desktop.optimizedUrl || null,
				result.screenshots.desktop.size || null,
				result.screenshots.desktop.compressionRatio || null,
				result.screenshots.desktop.error || null,
				result.screenshots.mobile.url || null,
				result.screenshots.mobile.optimizedUrl || null,
				result.screenshots.mobile.size || null,
				result.screenshots.mobile.compressionRatio || null,
				result.screenshots.mobile.error || null,
				result.captureTime,
				result.metadata.browserlessVersion || null,
				JSON.stringify(result.metadata.captureSettings),
				JSON.stringify(result.metadata.optimization),
				new Date(),
			]);
		}
		catch (error)
		{
			logger.error(`Failed to store screenshot in database for ${result.domain}:`, error);
			throw error;
		}
	}

	/**
	 * Check if cached screenshot is still fresh (within 24 hours)
	 */
	private isScreenshotFresh(result: ScreenshotResult): boolean
	{
		const captureTime = new Date(result.lastCaptured);
		const now = new Date();
		const hoursDiff = (now.getTime() - captureTime.getTime()) / (1000 * 60 * 60);
		return hoursDiff < 24;
	}

	/**
	 * Get screenshot statistics
	 */
	async getScreenshotStats(): Promise<{
		totalCaptured: number;
		successfulDesktop: number;
		successfulMobile: number;
		averageCaptureTime: number;
	}>
	{
		try
		{
			const query = `
				SELECT
					COUNT(*) as total_captured,
					COUNT(desktop_url) as successful_desktop,
					COUNT(mobile_url) as successful_mobile,
					AVG(capture_time_ms) as avg_capture_time
				FROM domain_screenshots
				WHERE capture_date >= ?
			`;

			const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
			const result = await this.scyllaClient.execute(query, [oneDayAgo]);

			const row = result.rows[0];
			return {
				totalCaptured: parseInt(row.total_captured) || 0,
				successfulDesktop: parseInt(row.successful_desktop) || 0,
				successfulMobile: parseInt(row.successful_mobile) || 0,
				averageCaptureTime: parseFloat(row.avg_capture_time) || 0,
			};
		}
		catch (error)
		{
			logger.error('Failed to get screenshot statistics:', error);
			return {
				totalCaptured: 0,
				successfulDesktop: 0,
				successfulMobile: 0,
				averageCaptureTime: 0,
			};
		}
	}

	/**
	 * Invalidate screenshot cache for domain
	 */
	async invalidateCache(domain: string): Promise<void>
	{
		try
		{
			const cacheKey = `screenshot:${domain}`;
			await this.redisClient.del(cacheKey);
			logger.debug(`Screenshot cache invalidated for domain: ${domain}`);
		}
		catch (error)
		{
			logger.warn(`Failed to invalidate screenshot cache for ${domain}:`, error);
		}
	}

	/**
	 * Batch capture screenshots for multiple domains
	 */
	async captureBatch(domains: string[], useCache: boolean = true): Promise<ScreenshotResult[]>
	{
		const results: ScreenshotResult[] = [];

		// Process domains in parallel with concurrency limit (lower for screenshots due to resource usage)
		const concurrencyLimit = 2;
		const chunks = [];

		for (let i = 0; i < domains.length; i += concurrencyLimit)
		{
			chunks.push(domains.slice(i, i + concurrencyLimit));
		}

		for (const chunk of chunks)
		{
			const chunkPromises = chunk.map(async (domain) =>
			{
				try
				{
					if (useCache)
					{
						const cached = await this.getCachedScreenshot(domain);
						if (cached && this.isScreenshotFresh(cached))
						{
							return cached;
						}
					}

					return await this.analyzer.captureScreenshots(domain);
				}
				catch (error)
				{
					logger.error(`Batch screenshot capture failed for ${domain}:`, error);
					return {
						domain,
						screenshots: {
							desktop: {
								width: 1920,
								height: 1080,
								format: 'webp',
								error: (error as Error).message,
							},
							mobile: {
								width: 375,
								height: 667,
								format: 'webp',
								error: (error as Error).message,
							},
						},
						metadata: {
							captureSettings: {
								timeout: 30000,
								waitUntil: 'networkidle2',
								quality: 90,
							},
							optimization: {
								enabled: false,
								service: 'weserv',
								quality: 80,
							},
						},
						captureTime: 0,
						lastCaptured: new Date().toISOString(),
						error: (error as Error).message,
					} as ScreenshotResult;
				}
			});

			const chunkResults = await Promise.all(chunkPromises);
			results.push(...chunkResults);

			// Store all successful results
			for (const result of chunkResults)
			{
				if (!result.error)
				{
					await this.storeScreenshot(result);
				}
			}
		}

		return results;
	}

	/**
	 * Validate screenshot capture capability
	 */
	async validateCapability(): Promise<{
		browserlessAvailable: boolean;
		imageProxyAvailable: boolean;
		error?: string;
	}>
	{
		return this.analyzer.validateCapability();
	}
}

export { ScreenshotModule };

export default ScreenshotModule;
