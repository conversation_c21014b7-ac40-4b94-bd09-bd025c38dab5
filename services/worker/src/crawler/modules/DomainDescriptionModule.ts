/**
 * Domain Description Module - AI-powered domain description generation
 * Extracted and consolidated from crawler service for worker consolidation
 */

import type { Logger } from '@shared';
import type { DataCollectionModuleConfigType } from '../core/DataCollectionModule';
import type { AIProvidersConfigType, AIFallbackConfigType } from '../../ai/types';
import type { DomainContent, GeneratedDescription } from '../../ai/DomainDescriptionGenerator';
import DataCollectionModule from '../core/DataCollectionModule';
import DomainDescriptionGenerator from '../../ai/DomainDescriptionGenerator';

type DomainDescriptionDataType =
{
	domain: string;
	description: GeneratedDescription;
	processingTime: number;
	aiProvider: string;
	success: boolean;
	error?: string;
};

type DomainDescriptionConfigType = DataCollectionModuleConfigType & {
	providers: AIProvidersConfigType;
	fallbackConfig?: Partial<AIFallbackConfigType>;
	defaultProvider?: string;
	enableBatchProcessing?: boolean;
	batchSize?: number;
	retryAttempts?: number;
	fallbackEnabled?: boolean;
};

/**
 * Module for generating AI-powered domain descriptions and company information
 * Integrates with multiple AI providers for content analysis and summarization
 */
class DomainDescriptionModule extends DataCollectionModule<DomainDescriptionDataType>
{
	private readonly descriptionGenerator: DomainDescriptionGenerator;

	override private readonly config: DomainDescriptionConfigType;

	constructor(logger: Logger, config: DomainDescriptionConfigType)
	{
		super('domain-description', logger);

		this.config =
		{
			enableBatchProcessing: true,
			batchSize: 3,
			retryAttempts: 2,
			fallbackEnabled: true,
			...config,
		};

		if (!config.providers || Object.keys(config.providers).length === 0)
		{
			throw new Error('At least one AI provider must be configured');
		}

		this.descriptionGenerator = new DomainDescriptionGenerator(
			config.providers,
			config.fallbackConfig,
		);
	}

	/**
	 * Collect domain description and company information
	 */
	override async collect(domain: string): Promise<DomainDescriptionDataType>
	{
		this.logger.info('Starting domain description generation', { domain });

		const startTime = Date.now();

		try
		{
			// Gather domain content from other modules or direct analysis
			const domainContent = await this.gatherDomainContent(domain);

			// Generate description using AI
			const description = await this.generateWithRetry(domainContent);

			const processingTime = Date.now() - startTime;

			this.logger.info('Domain description generated successfully', {
				domain,
				processingTime,
				confidence: description.confidence,
			});

			return {
				domain,
				description,
				processingTime,
				aiProvider: this.config.defaultProvider || 'default',
				success: true,
			};
		}
		catch (error)
		{
			const processingTime = Date.now() - startTime;
			const errorMessage = error instanceof Error ? error.message : 'Unknown error';

			this.logger.error('Domain description generation failed', {
				domain,
				error: errorMessage,
				processingTime,
			});

			// Return fallback if enabled
			if (this.config.fallbackEnabled)
			{
				const fallbackDescription = await this.generateFallbackDescription(domain);

				return {
					domain,
					description: fallbackDescription,
					processingTime,
					aiProvider: 'fallback',
					success: false,
					error: errorMessage,
				};
			}

			throw error;
		}
	}

	/**
	 * Generate description for a single domain with custom content
	 */
	async generateDescription(
		domain: string,
		content: Partial<DomainContent>,
	): Promise<GeneratedDescription>
	{
		const domainContent: DomainContent = {
			domain,
			...content,
		};

		return this.descriptionGenerator.generateDescription(domainContent);
	}

	/**
	 * Extract company information only
	 */
	async extractCompanyInfo(
		domain: string,
		content: Partial<DomainContent>,
	): Promise<GeneratedDescription['companyInfo']>
	{
		const domainContent: DomainContent = {
			domain,
			...content,
		};

		return this.descriptionGenerator.extractCompanyInfo(domainContent);
	}

	/**
	 * Batch process multiple domains
	 */
	async batchProcess(domains: string[]): Promise<Map<string, DomainDescriptionDataType>>
	{
		if (!this.config.enableBatchProcessing)
		{
			throw new Error('Batch processing is disabled');
		}

		this.logger.info('Starting batch domain description generation', {
			domainCount: domains.length,
			batchSize: this.config.batchSize,
		});

		const results = new Map<string, DomainDescriptionDataType>();
		const batches = this.createBatches(domains, this.config.batchSize || 3);

		for (const batch of batches)
		{
			const batchPromises = batch.map(async (domain) =>
			{
				try
				{
					const result = await this.collect(domain);
					results.set(domain, result);
				}
				catch (error)
				{
					this.logger.error('Batch processing failed for domain', {
						domain,
						error: error instanceof Error ? error.message : 'Unknown error',
					});

					if (this.config.fallbackEnabled)
					{
						const fallbackDescription = await this.generateFallbackDescription(domain);
						results.set(domain, {
							domain,
							description: fallbackDescription,
							processingTime: 0,
							aiProvider: 'fallback',
							success: false,
							error: error instanceof Error ? error.message : 'Unknown error',
						});
					}
				}
			});

			await Promise.all(batchPromises);
		}

		this.logger.info('Batch domain description generation completed', {
			totalDomains: domains.length,
			successfullyProcessed: Array.from(results.values()).filter(r => r.success).length,
			failed: Array.from(results.values()).filter(r => !r.success).length,
		});

		return results;
	}

	/**
	 * Check if AI services are available
	 */
	async isAvailable(): Promise<boolean>
	{
		try
		{
			return await this.descriptionGenerator.isAvailable();
		}
		catch (error)
		{
			this.logger.warn('AI service availability check failed', {
				error: error instanceof Error ? error.message : 'Unknown error',
			});

			return false;
		}
	}

	/**
	 * Get available AI providers
	 */
	getAvailableProviders(): string[]
	{
		return Object.keys(this.config.providers);
	}

	/**
	 * Generate description with retry logic
	 */
	private async generateWithRetry(
		domainContent: DomainContent,
		attempt: number = 1,
	): Promise<GeneratedDescription>
	{
		try
		{
			return await this.descriptionGenerator.generateDescription(domainContent);
		}
		catch (error)
		{
			if (attempt < (this.config.retryAttempts || 2))
			{
				this.logger.warn('Retrying domain description generation', {
					domain: domainContent.domain,
					attempt: attempt + 1,
					error: error instanceof Error ? error.message : 'Unknown error',
				});

				// Wait before retry
				await this.delay(1000 * attempt);

				return this.generateWithRetry(domainContent, attempt + 1);
			}

			throw error;
		}
	}

	/**
	 * Gather domain content from various sources
	 * This integrates with other modules to get comprehensive content
	 */
	private async gatherDomainContent(domain: string): Promise<DomainContent>
	{
		// This is a placeholder implementation that should be enhanced
		// to integrate with other crawler modules:
		// 1. Get homepage content from HomepageModule
		// 2. Get about page content from AdvancedContentModule
		// 3. Get contact info from contact page analysis
		// 4. Get technology stack from technology detection
		// 5. Get industry classification from category analysis

		this.logger.debug('Gathering domain content for AI analysis', { domain });

		// For now, return basic structure
		// This should be enhanced to integrate with other modules
		return {
			domain,
			title: `${domain} - Website`,
			description: 'Website description not available',
			content: 'Website content not available',
			aboutContent: 'About page content not available',
			contactInfo: 'Contact information not available',
			technologies: [],
			language: 'en',
		};
	}

	/**
	 * Generate fallback description when AI fails
	 */
	private async generateFallbackDescription(domain: string): Promise<GeneratedDescription>
	{
		const domainContent = await this.gatherDomainContent(domain);

		return {
			shortDescription: `${domain} is a website providing online services.`,
			longDescription: `${domain} operates as a web-based platform offering various services to its users. The website serves as a digital presence for the organization.`,
			companyInfo: {
				name: domain,
			},
			keyFeatures: ['Web-based platform', 'Online services'],
			targetAudience: 'General internet users',
			confidence: 0.1,
			generatedAt: new Date(),
		};
	}

	/**
	 * Create batches for processing
	 */
	private createBatches<T>(items: T[], batchSize: number): T[][]
	{
		const batches: T[][] = [];
		for (let i = 0; i < items.length; i += batchSize)
		{
			batches.push(items.slice(i, i + batchSize));
		}
		return batches;
	}

	/**
	 * Delay execution
	 */
	private delay(ms: number): Promise<void>
	{
		 
		return new Promise(resolve => setTimeout(resolve, ms));
	}

	/**
	 * Get generation metrics
	 */
	getMetrics(): any
	{
		return this.descriptionGenerator.getMetrics();
	}

	/**
	 * Test AI providers
	 */
	async testProviders(): Promise<Map<string, boolean>>
	{
		return this.descriptionGenerator.testProviders();
	}

	/**
	 * Shutdown the module
	 */
	async shutdown(): Promise<void>
	{
		await this.descriptionGenerator.shutdown();
	}
}

export type { DomainDescriptionDataType, DomainDescriptionConfigType };

export default DomainDescriptionModule;
