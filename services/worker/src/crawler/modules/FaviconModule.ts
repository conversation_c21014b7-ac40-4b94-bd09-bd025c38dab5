import { RedisClientWrapper, ScyllaClient, Logger } from '@shared';
import { DataCollectionModule, DataCollectionModuleConfig } from '../core/DataCollectionModule';
import FaviconCollector, { FaviconCollectionResult } from '../analyzers/FaviconCollector';

const logger = Logger.getLogger('FaviconModule');

/**
 * Favicon Collection Data Collection Module with caching and storage
 */
class FaviconModule extends DataCollectionModule<FaviconCollectionResult>
{
	private collector: FaviconCollector;

	private redisClient: RedisClientWrapper;

	private scyllaClient: ScyllaClient;

	constructor(redisClient?: RedisClientWrapper, scyllaClient?: ScyllaClient)
	{
		const config: DataCollectionModuleConfig = {
			name: 'favicon',
			priority: 50, // Lower priority - nice to have
			timeout: 15000, // 15 seconds
			retryAttempts: 1,
			retryDelay: 1000, // 1 second
			dependencies: ['dns'], // Depends on DNS to know if domain is accessible
		};

		super(config);

		// Initialize database clients
		this.redisClient = redisClient || new RedisClientWrapper();
		this.scyllaClient = scyllaClient || new ScyllaClient();

		// Initialize collector with database clients
		this.collector = new FaviconCollector(this.redisClient, this.scyllaClient);
	}

	/**
   * Initialize the module and database connections
   */
	async initialize(): Promise<void>
	{
		try
		{
			// Connect to Redis if not already connected
			if (!this.redisClient.getClient())
			{
				await this.redisClient.connect();
				logger.info('Redis client connected for FaviconModule');
			}

			// Connect to ScyllaDB if not already connected
			if (!this.scyllaClient.getClient())
			{
				await this.scyllaClient.connect();
				logger.info('ScyllaDB client connected for FaviconModule');
			}
		}
		catch (error)
		{
			logger.error('Failed to initialize FaviconModule database connections:', error);
			throw error;
		}
	}

	/**
   * Cleanup database connections
   */
	async cleanup(): Promise<void>
	{
		try
		{
			await this.redisClient.disconnect();
			await this.scyllaClient.disconnect();
			logger.info('FaviconModule database connections closed');
		}
		catch (error)
		{
			logger.warn('Error during FaviconModule cleanup:', error);
		}
	}

	protected async collect(domain: string): Promise<FaviconCollectionResult>
	{
		return this.collector.collectFavicon(domain, true); // Use cache by default
	}

	/**
   * Collect favicon without using cache (force fresh collection)
   */
	async collectFresh(domain: string): Promise<FaviconCollectionResult>
	{
		return this.collector.collectFavicon(domain, false);
	}

	/**
   * Get cached favicon result
   */
	async getCachedFavicon(domain: string): Promise<FaviconCollectionResult | null>
	{
		try
		{
			const cacheKey = `favicon:${domain}`;
			return await this.redisClient.get<FaviconCollectionResult>(cacheKey);
		}
		catch (error)
		{
			logger.warn(`Failed to get cached favicon for ${domain}:`, error);
			return null;
		}
	}

	/**
   * Get stored favicon from database
   */
	async getStoredFavicon(domain: string): Promise<FaviconCollectionResult | null>
	{
		return this.collector.getStoredFavicon(domain);
	}

	/**
   * Invalidate favicon cache for domain
   */
	async invalidateCache(domain: string): Promise<void>
	{
		await this.collector.invalidateFaviconCache(domain);
	}

	/**
   * Get favicon cache statistics
   */
	async getCacheStats(): Promise<{
		totalCached: number;
		successfulCached: number;
		failedCached: number;
	}>
	{
		return this.collector.getFaviconCacheStats();
	}

	/**
   * Batch collect favicons for multiple domains
   */
	async collectBatch(domains: string[], useCache: boolean = true): Promise<FaviconCollectionResult[]>
	{
		const results: FaviconCollectionResult[] = [];

		// Process domains in parallel with concurrency limit
		const concurrencyLimit = 5;
		const chunks = [];

		for (let i = 0; i < domains.length; i += concurrencyLimit)
		{
			chunks.push(domains.slice(i, i + concurrencyLimit));
		}

		for (const chunk of chunks)
		{
			const chunkPromises = chunk.map(domain => this.collector.collectFavicon(domain, useCache)
				.catch((error) =>
				{
					logger.error(`Batch favicon collection failed for ${domain}:`, error);
					return {
						domain,
						faviconFound: false,
						sources: { duckduckgo: false, direct: false, html: false },
						fallbackUsed: null,
						lastCollected: new Date().toISOString(),
						error: error.message,
					} as FaviconCollectionResult;
				}));

			const chunkResults = await Promise.all(chunkPromises);
			results.push(...chunkResults);
		}

		return results;
	}
}

export { FaviconModule };

export default FaviconModule;
