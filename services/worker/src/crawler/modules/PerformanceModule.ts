import { Logger } from '@shared';
import { PerformanceMetrics } from '@shared/models/DomainModels';
import { DataCollectionModule } from '../core/DataCollectionModule';
import PerformanceAuditor, { PerformanceAuditResult } from '../analyzers/PerformanceAuditor';

const logger = Logger.getLogger('PerformanceModule');

/**
 * Performance Module
 * Handles performance auditing and Core Web Vitals measurement
 */
class PerformanceModule extends DataCollectionModule<PerformanceAuditResult>
{
	private performanceAuditor: PerformanceAuditor;

	constructor()
	{
		super({
			name: 'performance',
			priority: 3, // Medium-high priority
			timeout: 90000, // 90 seconds for performance audits
			retryAttempts: 2,
			retryDelay: 5000,
			dependencies: [], // No dependencies
		});

		this.performanceAuditor = new PerformanceAuditor();
	}

	/**
	 * Implement the abstract collect method
	 */
	protected async collect(domain: string): Promise<PerformanceAuditResult>
	{
		// Check if browserless is available before starting
		const browserlessStatus = await this.performanceAuditor.getBrowserlessStatus();
		if (!browserlessStatus.available)
		{
			throw new Error('Browserless service is not available for performance auditing');
		}

		// Perform performance audit
		const auditResult = await this.performanceAuditor.auditPerformance(domain);

		if (auditResult.error)
		{
			throw new Error(auditResult.error);
		}

		return auditResult;
	}

	/**
	 * Collect performance data with metadata (wrapper for execute method)
	 */
	async collectData(domain: string): Promise<{
		success: boolean;
		data?: PerformanceAuditResult;
		error?: string;
		metadata: {
			module: string;
			executionTime: number;
			timestamp: string;
			resourceUsage: {
				memoryUsed: number;
				cpuTime: number;
			};
		};
	}>
	{
		const startTime = Date.now();
		const startMemory = process.memoryUsage().heapUsed;

		try
		{
			const result = await this.execute(domain);
			const endTime = Date.now();
			const endMemory = process.memoryUsage().heapUsed;
			const executionTime = Math.max(1, endTime - startTime); // Ensure at least 1ms

			return {
				success: result.success,
				data: result.data,
				error: result.error,
				metadata: {
					module: this.config.name,
					executionTime,
					timestamp: new Date().toISOString(),
					resourceUsage: {
						memoryUsed: Math.max(0, endMemory - startMemory),
						cpuTime: result.executionTime,
					},
				},
			};
		}
		catch (error)
		{
			const endTime = Date.now();
			const endMemory = process.memoryUsage().heapUsed;
			const executionTime = Math.max(1, endTime - startTime); // Ensure at least 1ms

			return {
				success: false,
				error: (error as Error).message,
				metadata: {
					module: this.config.name,
					executionTime,
					timestamp: new Date().toISOString(),
					resourceUsage: {
						memoryUsed: Math.max(0, endMemory - startMemory),
						cpuTime: 0,
					},
				},
			};
		}
	}

	/**
	 * Check if performance data collection is available
	 */
	async isAvailable(): Promise<boolean>
	{
		try
		{
			const capability = await this.performanceAuditor.validateCapability();
			return capability.browserlessAvailable && capability.performanceAPISupported;
		}
		catch (error)
		{
			logger.warn('Performance module availability check failed:', error);
			return false;
		}
	}

	/**
	 * Collect mobile performance data for a domain
	 */
	async collectMobileData(domain: string): Promise<{
		success: boolean;
		data?: PerformanceAuditResult;
		error?: string;
		metadata: {
			module: string;
			executionTime: number;
			timestamp: string;
			deviceType: string;
		};
	}>
	{
		const startTime = Date.now();
		logger.info(`Starting mobile performance data collection for domain: ${domain}`);

		try
		{
			// Check browserless availability
			const browserlessStatus = await this.performanceAuditor.getBrowserlessStatus();
			if (!browserlessStatus.available)
			{
				throw new Error('Browserless service is not available for mobile performance auditing');
			}

			// Perform mobile performance audit
			const auditResult = await this.performanceAuditor.auditMobilePerformance(domain);

			if (auditResult.error)
			{
				throw new Error(auditResult.error);
			}

			const executionTime = Date.now() - startTime;

			logger.info(`Mobile performance data collection completed for domain: ${domain}`, {
				executionTime,
				performanceScore: auditResult.metrics.score,
				loadTime: auditResult.metrics.loadTime,
				viewport: auditResult.auditDetails.viewport,
			});

			return {
				success: true,
				data: auditResult,
				metadata: {
					module: this.config.name,
					executionTime,
					timestamp: new Date().toISOString(),
					deviceType: 'mobile',
				},
			};
		}
		catch (error)
		{
			const executionTime = Date.now() - startTime;
			const errorMessage = (error as Error).message;

			logger.error(`Mobile performance data collection failed for domain: ${domain}:`, error);

			return {
				success: false,
				error: errorMessage,
				metadata: {
					module: this.config.name,
					executionTime,
					timestamp: new Date().toISOString(),
					deviceType: 'mobile',
				},
			};
		}
	}

	/**
	 * Get performance data summary for storage
	 */
	getDataSummary(data: PerformanceAuditResult): Record<string, any>
	{
		return {
			// Core Web Vitals
			loadTime: data.metrics.loadTime,
			firstContentfulPaint: data.metrics.firstContentfulPaint,
			largestContentfulPaint: data.metrics.largestContentfulPaint,
			cumulativeLayoutShift: data.metrics.cumulativeLayoutShift,
			firstInputDelay: data.metrics.firstInputDelay,
			speedIndex: data.metrics.speedIndex,
			performanceScore: data.metrics.score,

			// Resource analysis
			totalRequests: data.resourceAnalysis.totalRequests,
			totalSize: data.resourceAnalysis.totalSize,
			resourceTypes: data.resourceAnalysis.resourceTypes,
			largestResourcesCount: data.resourceAnalysis.largestResources.length,

			// Network analysis
			connectionType: data.networkAnalysis.connectionType,
			rtt: data.networkAnalysis.rtt,
			downlink: data.networkAnalysis.downlink,
			effectiveType: data.networkAnalysis.effectiveType,

			// Audit metadata
			auditTime: data.auditDetails.auditTime,
			viewport: data.auditDetails.viewport,
			recommendationsCount: data.recommendations.length,

			// Status
			lastAudited: data.lastAudited,
			hasError: !!data.error,
		};
	}

	/**
	 * Transform performance data for ScyllaDB storage
	 */
	transformForStorage(data: PerformanceAuditResult): Record<string, any>
	{
		return {
			// Performance metrics map for ScyllaDB
			performance_metrics: new Map(Object.entries({
				load_time: data.metrics.loadTime.toString(),
				fcp: data.metrics.firstContentfulPaint.toString(),
				lcp: data.metrics.largestContentfulPaint.toString(),
				cls: data.metrics.cumulativeLayoutShift.toString(),
				fid: data.metrics.firstInputDelay.toString(),
				speed_index: data.metrics.speedIndex.toString(),
				score: data.metrics.score.toString(),
				total_requests: data.resourceAnalysis.totalRequests.toString(),
				total_size: data.resourceAnalysis.totalSize.toString(),
				audit_time: data.auditDetails.auditTime.toString(),
			})),

			// Resource analysis
			resource_analysis: new Map(Object.entries({
				total_requests: data.resourceAnalysis.totalRequests.toString(),
				total_size: data.resourceAnalysis.totalSize.toString(),
				resource_types: JSON.stringify(data.resourceAnalysis.resourceTypes),
				largest_resources: JSON.stringify(data.resourceAnalysis.largestResources.slice(0, 5)), // Top 5 only
			})),

			// Network analysis
			network_analysis: new Map(Object.entries({
				connection_type: data.networkAnalysis.connectionType,
				rtt: data.networkAnalysis.rtt.toString(),
				downlink: data.networkAnalysis.downlink.toString(),
				effective_type: data.networkAnalysis.effectiveType,
			})),

			// Recommendations
			performance_recommendations: data.recommendations,

			// Audit metadata
			audit_metadata: new Map(Object.entries({
				viewport_width: data.auditDetails.viewport.width.toString(),
				viewport_height: data.auditDetails.viewport.height.toString(),
				user_agent: data.auditDetails.userAgent,
				audit_time: data.auditDetails.auditTime.toString(),
				browser_version: data.auditDetails.browserVersion || 'unknown',
			})),

			// Timestamps
			last_performance_audit: data.lastAudited,
			performance_audit_error: data.error || null,
		};
	}

	/**
	 * Validate collected performance data
	 */
	validateData(data: PerformanceAuditResult): {
		isValid: boolean;
		errors: string[];
		warnings: string[];
	}
	{
		const errors: string[] = [];
		const warnings: string[] = [];

		// Check required metrics
		if (!data.domain)
		{
			errors.push('Domain is required');
		}

		if (!data.metrics)
		{
			errors.push('Performance metrics are required');
		}
		else
		{
			// Validate Core Web Vitals ranges
			if (data.metrics.largestContentfulPaint < 0 || data.metrics.largestContentfulPaint > 60000)
			{
				warnings.push('LCP value seems unrealistic');
			}

			if (data.metrics.cumulativeLayoutShift < 0 || data.metrics.cumulativeLayoutShift > 5)
			{
				warnings.push('CLS value seems unrealistic');
			}

			if (data.metrics.firstInputDelay < 0 || data.metrics.firstInputDelay > 10000)
			{
				warnings.push('FID value seems unrealistic');
			}

			if (data.metrics.score < 0 || data.metrics.score > 1)
			{
				errors.push('Performance score must be between 0 and 1');
			}
		}

		// Check resource analysis
		if (!data.resourceAnalysis)
		{
			warnings.push('Resource analysis is missing');
		}
		else
		{
			if (data.resourceAnalysis.totalRequests > 1000)
			{
				warnings.push('Unusually high number of requests detected');
			}

			if (data.resourceAnalysis.totalSize > 50 * 1024 * 1024) // 50MB
			{
				warnings.push('Unusually large page size detected');
			}
		}

		// Check audit details
		if (!data.auditDetails || data.auditDetails.auditTime <= 0)
		{
			warnings.push('Audit details are incomplete');
		}

		return {
			isValid: errors.length === 0,
			errors,
			warnings,
		};
	}

	/**
	 * Get module health status
	 */
	async getHealthStatus(): Promise<{
		healthy: boolean;
		status: string;
		details: Record<string, any>;
	}>
	{
		try
		{
			const capability = await this.performanceAuditor.validateCapability();
			const browserlessStatus = await this.performanceAuditor.getBrowserlessStatus();

			const healthy = capability.browserlessAvailable && capability.performanceAPISupported;

			return {
				healthy,
				status: healthy ? 'operational' : 'degraded',
				details: {
					browserlessAvailable: capability.browserlessAvailable,
					performanceAPISupported: capability.performanceAPISupported,
					browserlessStats: browserlessStatus.stats,
					browserlessPressure: browserlessStatus.pressure,
					lastHealthCheck: new Date().toISOString(),
				},
			};
		}
		catch (error)
		{
			logger.error('Performance module health check failed:', error);
			return {
				healthy: false,
				status: 'error',
				details: {
					error: (error as Error).message,
					lastHealthCheck: new Date().toISOString(),
				},
			};
		}
	}

	/**
	 * Get performance module metrics
	 */
	getMetrics(): {
		avgAuditTime: number;
		successRate: number;
		resourceIntensiveOperations: number;
	}
	{
		// In a real implementation, these would be tracked over time
		// For now, return default values
		return {
			avgAuditTime: 5000, // 5 seconds average
			successRate: 0.95, // 95% success rate
			resourceIntensiveOperations: 1, // Performance audits are resource intensive
		};
	}

	/**
	 * Store performance data with historical tracking
	 */
	async storePerformanceData(
		domain: string,
		auditResult: PerformanceAuditResult,
		databaseManager?: any,
	): Promise<{
		stored: boolean;
		error?: string;
	}>
	{
		try
		{
			const storeResult = await this.performanceAuditor.storePerformanceData(
				domain,
				auditResult,
				databaseManager,
			);

			if (storeResult.stored)
			{
				logger.info(`Performance data stored successfully for domain: ${domain}`, {
					performanceScore: auditResult.metrics.score,
					loadTime: auditResult.metrics.loadTime,
					auditTime: auditResult.auditDetails.auditTime,
				});
			}

			return storeResult;
		}
		catch (error)
		{
			logger.error(`Failed to store performance data for domain: ${domain}:`, error);
			return {
				stored: false,
				error: (error as Error).message,
			};
		}
	}

	/**
	 * Get performance history for a domain
	 */
	async getPerformanceHistory(
		domain: string,
		databaseManager?: any,
		days: number = 30,
	): Promise<{
		success: boolean;
		history?: Array<{
			date: string;
			metrics: PerformanceMetrics;
			auditTime: number;
		}>;
		error?: string;
	}>
	{
		try
		{
			return await this.performanceAuditor.getPerformanceHistory(domain, databaseManager, days);
		}
		catch (error)
		{
			logger.error(`Failed to get performance history for domain: ${domain}:`, error);
			return {
				success: false,
				error: (error as Error).message,
			};
		}
	}
}

export default PerformanceModule;
