import {
	describe, expect, beforeEach, vi, it,
} from 'vitest';
import RobotsModule from '../RobotsModule';

// Mock the Logger
vi.mock('../../utils/Logger', () => ({
	Logger: {
		getLogger: () => ({
			info: vi.fn(),
			error: vi.fn(),
			warn: vi.fn(),
			debug: vi.fn(),
		}),
	},
}));

describe('RobotsModule', () =>
{
	let module: RobotsModule;

	beforeEach(() =>
	{
		module = new RobotsModule();
	});

	describe('configuration', () =>
	{
		it('should have correct module configuration', () =>
		{
			const config = module.getConfig();

			expect(config.name).toBe('robots');
			expect(config.priority).toBe(100);
			expect(config.timeout).toBe(10000);
			expect(config.retryAttempts).toBe(2);
			expect(config.retryDelay).toBe(1000);
			expect(config.dependencies).toEqual([]);
		});

		it('should be able to run without dependencies', () =>
		{
			const canRun = module.canRun([]);

			expect(canRun).toBe(true);
		});
	});

	describe('execute', () =>
	{
		it('should execute robots.txt analysis successfully', async () =>
		{
			const result = await module.execute('google.com');

			expect(result).toHaveProperty('success');
			expect(result).toHaveProperty('executionTime');
			expect(result).toHaveProperty('retryCount');

			if (result.success)
			{
				expect(result.data).toBeDefined();
				expect(result.data?.domain).toBe('google.com');
				expect(result.data?.robotsTxtUrl).toBe('https://google.com/robots.txt');
			}
		});

		it('should handle domains without robots.txt', async () =>
		{
			const result = await module.execute('nonexistent-domain-test-12345.com');

			expect(result).toHaveProperty('success');
			expect(result).toHaveProperty('executionTime');
			expect(result).toHaveProperty('retryCount');

			if (result.success)
			{
				expect(result.data?.exists).toBe(false);
				expect(result.data?.accessible).toBe(false);
			}
		});

		it('should return execution timing information', async () =>
		{
			const result = await module.execute('google.com');

			expect(typeof result.executionTime).toBe('number');
			expect(result.executionTime).toBeGreaterThan(0);
			expect(typeof result.retryCount).toBe('number');
			expect(result.retryCount).toBeGreaterThanOrEqual(0);
		});
	});
});
