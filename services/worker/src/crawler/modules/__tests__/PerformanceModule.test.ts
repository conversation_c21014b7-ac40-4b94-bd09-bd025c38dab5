import { describe, it, expect, beforeEach, vi } from 'vitest';
import PerformanceModule from '../PerformanceModule';
import PerformanceAuditor from '../../analyzers/PerformanceAuditor';

// Mock the PerformanceAuditor
vi.mock('../../analyzers/PerformanceAuditor');

describe('PerformanceModule', () =>
{
	let performanceModule: PerformanceModule;
	let mockAuditor: any;

	beforeEach(() =>
	{
		mockAuditor = {
			getBrowserlessStatus: vi.fn(),
			auditPerformance: vi.fn(),
			auditMobilePerformance: vi.fn(),
			validateCapability: vi.fn(),
			storePerformanceData: vi.fn(),
			getPerformanceHistory: vi.fn(),
		};
		(PerformanceAuditor as any).mockImplementation(() => mockAuditor);
		performanceModule = new PerformanceModule();
	});

	describe('configuration', () =>
	{
		it('should have correct module configuration', () =>
		{
			const config = performanceModule.getConfig();

			expect(config.name).toBe('performance');
			expect(config.priority).toBe(3);
			expect(config.timeout).toBe(90000);
			expect(config.retryAttempts).toBe(2);
			expect(config.retryDelay).toBe(5000);
			expect(config.dependencies).toEqual([]);
		});
	});

	describe('data collection', () =>
	{
		it('should collect performance data successfully', async () =>
		{
			const mockBrowserlessStatus = {
				available: true,
				stats: {},
				pressure: 'low',
			};

			const mockAuditResult = {
				domain: 'example.com',
				metrics: {
					loadTime: 1500,
					firstContentfulPaint: 800,
					largestContentfulPaint: 1200,
					cumulativeLayoutShift: 0.1,
					firstInputDelay: 50,
					speedIndex: 1000,
					score: 0.85,
				},
				resourceAnalysis: {
					totalRequests: 25,
					totalSize: 1024000,
					resourceTypes: { js: 5, css: 3, images: 10 },
					largestResources: [],
				},
				networkAnalysis: {
					connectionType: '4g',
					rtt: 50,
					downlink: 10,
					effectiveType: '4g',
				},
				auditDetails: {
					auditTime: 5000,
					viewport: { width: 1920, height: 1080 },
					userAgent: 'test-agent',
					browserVersion: 'Chrome/91.0',
				},
				recommendations: [],
				lastAudited: new Date().toISOString(),
			};

			mockAuditor.getBrowserlessStatus.mockResolvedValue(mockBrowserlessStatus);
			mockAuditor.auditPerformance.mockResolvedValue(mockAuditResult);

			const result = await performanceModule.execute('example.com');

			expect(result.success).toBe(true);
			expect(result.data).toEqual(mockAuditResult);
			expect(mockAuditor.getBrowserlessStatus).toHaveBeenCalled();
			expect(mockAuditor.auditPerformance).toHaveBeenCalledWith('example.com');
		});

		it('should fail when browserless is not available', async () =>
		{
			const mockBrowserlessStatus = {
				available: false,
				stats: {},
				pressure: 'high',
			};

			mockAuditor.getBrowserlessStatus.mockResolvedValue(mockBrowserlessStatus);

			const result = await performanceModule.execute('example.com');

			expect(result.success).toBe(false);
			expect(result.error).toContain('Browserless service is not available');
		});

		it('should handle audit errors', async () =>
		{
			const mockBrowserlessStatus = { available: true };
			const mockAuditResult = {
				domain: 'example.com',
				error: 'Performance audit failed',
			};

			mockAuditor.getBrowserlessStatus.mockResolvedValue(mockBrowserlessStatus);
			mockAuditor.auditPerformance.mockResolvedValue(mockAuditResult);

			const result = await performanceModule.execute('example.com');

			expect(result.success).toBe(false);
			expect(result.error).toBe('Performance audit failed');
		});
	});

	describe('mobile performance collection', () =>
	{
		it('should collect mobile performance data successfully', async () =>
		{
			const mockBrowserlessStatus = { available: true };
			const mockMobileResult = {
				domain: 'example.com',
				metrics: {
					loadTime: 2000,
					score: 0.75,
				},
				auditDetails: {
					viewport: { width: 375, height: 667 },
				},
			};

			mockAuditor.getBrowserlessStatus.mockResolvedValue(mockBrowserlessStatus);
			mockAuditor.auditMobilePerformance.mockResolvedValue(mockMobileResult);

			const result = await performanceModule.collectMobileData('example.com');

			expect(result.success).toBe(true);
			expect(result.data).toEqual(mockMobileResult);
			expect(result.metadata.deviceType).toBe('mobile');
		});
	});

	describe('availability check', () =>
	{
		it('should check if performance auditing is available', async () =>
		{
			const mockCapability = {
				browserlessAvailable: true,
				performanceAPISupported: true,
			};

			mockAuditor.validateCapability.mockResolvedValue(mockCapability);

			const isAvailable = await performanceModule.isAvailable();

			expect(isAvailable).toBe(true);
			expect(mockAuditor.validateCapability).toHaveBeenCalled();
		});

		it('should return false when browserless is not available', async () =>
		{
			const mockCapability = {
				browserlessAvailable: false,
				performanceAPISupported: true,
			};

			mockAuditor.validateCapability.mockResolvedValue(mockCapability);

			const isAvailable = await performanceModule.isAvailable();

			expect(isAvailable).toBe(false);
		});
	});

	describe('data validation', () =>
	{
		it('should validate performance data correctly', () =>
		{
			const validData = {
				domain: 'example.com',
				metrics: {
					largestContentfulPaint: 1200,
					cumulativeLayoutShift: 0.1,
					firstInputDelay: 50,
					score: 0.85,
				},
				resourceAnalysis: {
					totalRequests: 25,
					totalSize: 1024000,
				},
				auditDetails: {
					auditTime: 5000,
				},
			};

			const validation = performanceModule.validateData(validData as any);

			expect(validation.isValid).toBe(true);
			expect(validation.errors).toEqual([]);
		});

		it('should detect invalid performance data', () =>
		{
			const invalidData = {
				domain: '',
				metrics: {
					score: 1.5, // Invalid score > 1
				},
			};

			const validation = performanceModule.validateData(invalidData as any);

			expect(validation.isValid).toBe(false);
			expect(validation.errors.length).toBeGreaterThan(0);
		});
	});

	describe('health status', () =>
	{
		it('should return healthy status when all systems are operational', async () =>
		{
			const mockCapability = {
				browserlessAvailable: true,
				performanceAPISupported: true,
			};
			const mockBrowserlessStatus = {
				available: true,
				stats: {},
				pressure: 'low',
			};

			mockAuditor.validateCapability.mockResolvedValue(mockCapability);
			mockAuditor.getBrowserlessStatus.mockResolvedValue(mockBrowserlessStatus);

			const health = await performanceModule.getHealthStatus();

			expect(health.healthy).toBe(true);
			expect(health.status).toBe('operational');
		});

		it('should return degraded status when browserless is unavailable', async () =>
		{
			const mockCapability = {
				browserlessAvailable: false,
				performanceAPISupported: true,
			};

			mockAuditor.validateCapability.mockResolvedValue(mockCapability);

			const health = await performanceModule.getHealthStatus();

			expect(health.healthy).toBe(false);
			expect(health.status).toBe('degraded');
		});
	});
});
