import {
	describe, expect, beforeEach, vi, it,
} from 'vitest';
import DomainInfoModule from '../DomainInfoModule';

// Mock the Logger
vi.mock('../../utils/Logger', () => ({
	Logger: {
		getLogger: () => ({
			info: vi.fn(),
			error: vi.fn(),
			warn: vi.fn(),
			debug: vi.fn(),
		}),
	},
}));

describe('DomainInfoModule', () =>
{
	let module: DomainInfoModule;

	beforeEach(() =>
	{
		module = new DomainInfoModule();
	});

	describe('configuration', () =>
	{
		it('should have correct module configuration', () =>
		{
			const config = module.getConfig();

			expect(config.name).toBe('domainInfo');
			expect(config.priority).toBe(85);
			expect(config.timeout).toBe(20000);
			expect(config.retryAttempts).toBe(2);
			expect(config.retryDelay).toBe(3000);
			expect(config.dependencies).toEqual([]);
		});

		it('should be able to run without dependencies', () =>
		{
			const canRun = module.canRun([]);

			expect(canRun).toBe(true);
		});
	});

	describe('execute', () =>
	{
		it('should execute domain info analysis successfully', async () =>
		{
			const result = await module.execute('google.com');

			expect(result).toHaveProperty('success');
			expect(result).toHaveProperty('executionTime');
			expect(result).toHaveProperty('retryCount');

			if (result.success)
			{
				expect(result.data).toBeDefined();
				expect(result.data?.domain).toBe('google.com');
				expect(result.data?.validation).toBeDefined();
			}
		});

		it('should handle invalid domains', async () =>
		{
			const result = await module.execute('invalid..domain');

			expect(result).toHaveProperty('success');
			expect(result).toHaveProperty('executionTime');
			expect(result).toHaveProperty('retryCount');

			if (!result.success)
			{
				expect(result.error).toBeDefined();
			}
		});

		it('should return execution timing information', async () =>
		{
			const result = await module.execute('google.com');

			expect(typeof result.executionTime).toBe('number');
			expect(result.executionTime).toBeGreaterThan(0);
			expect(typeof result.retryCount).toBe('number');
			expect(result.retryCount).toBeGreaterThanOrEqual(0);
		});
	});
});
