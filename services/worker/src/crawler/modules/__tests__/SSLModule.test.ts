import {
	describe, expect, beforeEach, vi, it,
} from 'vitest';
import SSLModule from '../SSLModule';

// Mock the Logger
vi.mock('../../utils/Logger', () => ({
	Logger: {
		getLogger: () => ({
			info: vi.fn(),
			error: vi.fn(),
			warn: vi.fn(),
			debug: vi.fn(),
		}),
	},
}));

describe('SSLModule', () =>
{
	let module: SSLModule;

	beforeEach(() =>
	{
		module = new SSLModule();
	});

	describe('configuration', () =>
	{
		it('should have correct module configuration', () =>
		{
			const config = module.getConfig();

			expect(config.name).toBe('ssl');
			expect(config.priority).toBe(80);
			expect(config.timeout).toBe(12000);
			expect(config.retryAttempts).toBe(2);
			expect(config.retryDelay).toBe(1500);
			expect(config.dependencies).toEqual(['dns']);
		});

		it('should require DNS dependency', () =>
		{
			const canRunWithoutDNS = module.canRun([]);
			const canRunWithDNS = module.canRun(['dns']);

			expect(canRunWithoutDNS).toBe(false);
			expect(canRunWithDNS).toBe(true);
		});
	});

	describe('execute', () =>
	{
		it('should execute SSL analysis successfully', async () =>
		{
			const result = await module.execute('google.com');

			expect(result).toHaveProperty('success');
			expect(result).toHaveProperty('executionTime');
			expect(result).toHaveProperty('retryCount');

			if (result.success)
			{
				expect(result.data).toBeDefined();
				expect(result.data?.domain).toBe('google.com');
				expect(typeof result.data?.hasSSL).toBe('boolean');
			}
		});

		it('should handle domains without SSL', async () =>
		{
			const result = await module.execute('nonexistent-domain-test-12345.com');

			expect(result).toHaveProperty('success');
			expect(result).toHaveProperty('executionTime');
			expect(result).toHaveProperty('retryCount');

			if (result.success)
			{
				expect(result.data?.hasSSL).toBe(false);
				expect(result.data?.grade).toBe('F');
			}
		});

		it('should return execution timing information', async () =>
		{
			const result = await module.execute('google.com');

			expect(typeof result.executionTime).toBe('number');
			expect(result.executionTime).toBeGreaterThan(0);
			expect(typeof result.retryCount).toBe('number');
			expect(result.retryCount).toBeGreaterThanOrEqual(0);
		});
	});
});
