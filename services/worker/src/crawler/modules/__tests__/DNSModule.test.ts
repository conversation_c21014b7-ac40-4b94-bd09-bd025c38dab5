import { describe, it, expect, beforeEach, vi } from 'vitest';
import { DNSModule } from '../DNSModule';
import DNSAnalyzer from '../../analyzers/DNSAnalyzer';

// Mock the DNSAnalyzer
vi.mock('../../analyzers/DNSAnalyzer');

describe('DNSModule', () =>
{
	let dnsModule: DNSModule;
	let mockAnalyzer: any;

	beforeEach(() =>
	{
		mockAnalyzer = {
			analyzeDNSRecords: vi.fn(),
		};
		(DNSAnalyzer as any).mockImplementation(() => mockAnalyzer);
		dnsModule = new DNSModule();
	});

	describe('configuration', () =>
	{
		it('should have correct module configuration', () =>
		{
			const config = dnsModule.getConfig();

			expect(config.name).toBe('dns');
			expect(config.priority).toBe(95);
			expect(config.timeout).toBe(15000);
			expect(config.retryAttempts).toBe(3);
			expect(config.retryDelay).toBe(2000);
			expect(config.dependencies).toEqual([]);
		});

		it('should be able to run without dependencies', () =>
		{
			const canRun = dnsModule.canRun([]);
			expect(canRun).toBe(true);
		});
	});

	describe('data collection', () =>
	{
		it('should collect DNS data successfully', async () =>
		{
			const mockResult = {
				domain: 'example.com',
				records: {
					A: ['192.168.1.1'],
					AAAA: [],
					MX: [],
					NS: ['ns1.example.com'],
				},
				lastAnalyzed: new Date().toISOString(),
			};

			mockAnalyzer.analyzeDNSRecords.mockResolvedValue(mockResult);

			const result = await dnsModule.execute('example.com');

			expect(result.success).toBe(true);
			expect(result.data).toEqual(mockResult);
			expect(result.error).toBeUndefined();
			expect(mockAnalyzer.analyzeDNSRecords).toHaveBeenCalledWith('example.com');
		});

		it('should handle DNS analysis failures', async () =>
		{
			const error = new Error('DNS resolution failed');
			mockAnalyzer.analyzeDNSRecords.mockRejectedValue(error);

			const result = await dnsModule.execute('invalid.domain');

			expect(result.success).toBe(false);
			expect(result.data).toBeUndefined();
			expect(result.error).toBe('DNS resolution failed');
		});

		it('should track execution time', async () =>
		{
			const mockResult = {
				domain: 'example.com',
				records: { A: ['192.168.1.1'] },
			};

			mockAnalyzer.analyzeDNSRecords.mockResolvedValue(mockResult);

			const result = await dnsModule.execute('example.com');

			expect(result.executionTime).toBeGreaterThan(0);
		});

		it('should retry on failures', async () =>
		{
			const error = new Error('Temporary DNS failure');
			mockAnalyzer.analyzeDNSRecords
				.mockRejectedValueOnce(error)
				.mockRejectedValueOnce(error)
				.mockResolvedValue({
					domain: 'example.com',
					records: { A: ['192.168.1.1'] },
				});

			const result = await dnsModule.execute('example.com');

			expect(result.success).toBe(true);
			expect(result.retryCount).toBe(2);
			expect(mockAnalyzer.analyzeDNSRecords).toHaveBeenCalledTimes(3);
		});

		it('should fail after max retry attempts', async () =>
		{
			const error = new Error('Persistent DNS failure');
			mockAnalyzer.analyzeDNSRecords.mockRejectedValue(error);

			const result = await dnsModule.execute('example.com');

			expect(result.success).toBe(false);
			expect(result.retryCount).toBe(3); // Max retry attempts
			expect(mockAnalyzer.analyzeDNSRecords).toHaveBeenCalledTimes(4); // Initial + 3 retries
		});
	});
});
