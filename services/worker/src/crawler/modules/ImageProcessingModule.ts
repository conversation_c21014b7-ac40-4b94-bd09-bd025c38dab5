import { Logger } from '@shared';
import type { ImageProcessingOptions, ProcessedImage } from '../analyzers/ImageProcessor';
import { DataCollectionModule } from '../core/DataCollectionModule';
import { ImageProcessor } from '../analyzers/ImageProcessor';

const logger = Logger.getLogger('ImageProcessingModule');

type ImageProcessingDataType =
{
	screenshots: ProcessedImage[];
	favicons: ProcessedImage[];
	contentImages: ProcessedImage[];
	processingStats: {
		totalImages: number;
		successfullyProcessed: number;
		failed: number;
		totalSizeSaved: number;
	};
};

type ImageProcessingConfigType =
{
	weservUrl?: string;
	screenshotOptions?: ImageProcessingOptions;
	faviconOptions?: ImageProcessingOptions;
	contentImageOptions?: ImageProcessingOptions;
	maxConcurrentProcessing?: number;
};

/**
 * Module for processing domain-related images using weserv.nl proxy
 * Handles screenshots, favicons, and content images with optimization
 */
class ImageProcessingModule extends DataCollectionModule<ImageProcessingDataType>
{
	private readonly imageProcessor: ImageProcessor;

	private readonly config: ImageProcessingConfigType;

	constructor(config: ImageProcessingConfigType = {})
	{
		super({
			name: 'image-processing',
			priority: 25, // Lower priority - optimization task
			timeout: 30000, // 30 seconds
			retryAttempts: 2,
			retryDelay: 2000,
			dependencies: [], // No dependencies - can process any images
		});

		this.config = {
			maxConcurrentProcessing: 5,
			screenshotOptions: {
				format: 'webp',
				quality: 90,
				compression: true,
			},
			faviconOptions: {
				format: 'png',
				quality: 100,
				fallback: true,
			},
			contentImageOptions: {
				format: 'webp',
				quality: 80,
				compression: true,
			},
			...config,
		};

		this.imageProcessor = new ImageProcessor(logger, config.weservUrl);
	}

	/**
	 * Collect and process all images for a domain
	 */
	protected async collect(domain: string): Promise<ImageProcessingDataType>
	{
		logger.info('Starting image processing collection', { domain });

		try
		{
			// Get existing image URLs from other modules (screenshots, favicons, etc.)
			const imageUrls = await this.gatherImageUrls(domain);

			const result: ImageProcessingDataType = {
				screenshots: [],
				favicons: [],
				contentImages: [],
				processingStats: {
					totalImages: imageUrls.length,
					successfullyProcessed: 0,
					failed: 0,
					totalSizeSaved: 0,
				},
			};

			// Process images in batches to avoid overwhelming the service
			const batches = this.createBatches(imageUrls, this.config.maxConcurrentProcessing || 5);

			for (const batch of batches)
			{
				const batchResults = await Promise.allSettled(
					batch.map(async ({ url, type }) =>
					{
						const options = this.getOptionsForType(type);
						return {
							type,
							result: await this.imageProcessor.processImage(url, type, options),
						};
					}),
				);

				// Process batch results
				for (const batchResult of batchResults)
				{
					if (batchResult.status === 'fulfilled')
					{
						const { type, result } = batchResult.value;

						if (Array.isArray(result))
						{
							// Multiple results (e.g., favicon with fallbacks)
							result.forEach((img) =>
							{
								this.addToResult(result, type, img);
								if (img.optimized)
								{
									result.processingStats.successfullyProcessed++;
								}
								else
								{
									result.processingStats.failed++;
								}
							});
						}
						else
						{
							// Single result
							this.addToResult(result, type, result);
							if (result.optimized)
							{
								result.processingStats.successfullyProcessed++;
							}
							else
							{
								result.processingStats.failed++;
							}
						}
					}
					else
					{
						result.processingStats.failed++;
						logger.error('Image processing failed in batch', {
							error: batchResult.reason,
						});
					}
				}
			}

			logger.info('Image processing collection completed', {
				domain,
				stats: result.processingStats,
			});

			return result;
		}
		catch (error)
		{
			logger.error('Image processing collection failed', {
				domain,
				error: error instanceof Error ? error.message : 'Unknown error',
			});

			throw error;
		}
	}

	/**
	 * Process a single image URL
	 */
	async processImage(
		imageUrl: string,
		imageType: 'screenshot' | 'favicon' | 'content',
		options?: ImageProcessingOptions,
	): Promise<ProcessedImage | ProcessedImage[]>
	{
		const processingOptions = options || this.getOptionsForType(imageType);
		return this.imageProcessor.processImage(imageUrl, imageType, processingOptions);
	}

	/**
	 * Validate if image processing is available
	 */
	async isAvailable(): Promise<boolean>
	{
		try
		{
			// Test with a simple image URL to check if weserv.nl is accessible
			const testUrl = 'https://httpbin.org/image/png';
			return await this.imageProcessor.validateImageUrl(testUrl);
		}
		catch (error)
		{
			logger.warn('Image processing service not available', {
				error: error instanceof Error ? error.message : 'Unknown error',
			});
			return false;
		}
	}

	/**
	 * Gather image URLs from domain (this would typically integrate with other modules)
	 */
	private async gatherImageUrls(domain: string): Promise<Array<{
		url: string;
		type: 'screenshot' | 'favicon' | 'content';
	}>>
	{
		const imageUrls: Array<{ url: string; type: 'screenshot' | 'favicon' | 'content' }> = [];

		// This is a placeholder - in a real implementation, this would:
		// 1. Get screenshot URLs from ScreenshotModule
		// 2. Get favicon URLs from FaviconModule
		// 3. Get content image URLs from AdvancedContentModule
		// 4. Integrate with the ModuleRegistry to access other module data

		// For now, we'll return an empty array and let other modules call processImage directly
		logger.debug('Image URL gathering not yet integrated with other modules', { domain });

		return imageUrls;
	}

	/**
	 * Get processing options for specific image type
	 */
	private getOptionsForType(type: 'screenshot' | 'favicon' | 'content'): ImageProcessingOptions
	{
		switch (type)
		{
			case 'screenshot':
				return this.config.screenshotOptions || {};
			case 'favicon':
				return this.config.faviconOptions || {};
			case 'content':
				return this.config.contentImageOptions || {};
			default:
				return {};
		}
	}

	/**
	 * Add processed image to result based on type
	 */
	private addToResult(
		result: ImageProcessingDataType,
		type: 'screenshot' | 'favicon' | 'content',
		processedImage: ProcessedImage,
	): void
	{
		switch (type)
		{
			case 'screenshot':
				result.screenshots.push(processedImage);
				break;
			case 'favicon':
				result.favicons.push(processedImage);
				break;
			case 'content':
				result.contentImages.push(processedImage);
				break;
		}
	}

	/**
	 * Create batches for concurrent processing
	 */
	private createBatches<T>(items: T[], batchSize: number): T[][]
	{
		const batches: T[][] = [];
		for (let i = 0; i < items.length; i += batchSize)
		{
			batches.push(items.slice(i, i + batchSize));
		}
		return batches;
	}
}

export type { ImageProcessingDataType, ImageProcessingConfigType };

export default ImageProcessingModule;
