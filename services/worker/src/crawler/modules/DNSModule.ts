import { DataCollectionModule, DataCollectionModuleConfig } from '../core/DataCollectionModule';
import DNSAnalyzer, { DNSAnalysisResult } from '../analyzers/DNSAnalyzer';

/**
 * DNS Records Data Collection Module
 */
class DNSModule extends DataCollectionModule<DNSAnalysisResult>
{
	private analyzer: DNSAnalyzer;

	constructor()
	{
		const config: DataCollectionModuleConfig = {
			name: 'dns',
			priority: 95, // High priority - fundamental domain info
			timeout: 15000, // 15 seconds
			retryAttempts: 3,
			retryDelay: 2000, // 2 seconds
			dependencies: [], // No dependencies
		};

		super(config);
		this.analyzer = new DNSAnalyzer();
	}

	protected async collect(domain: string): Promise<DNSAnalysisResult>
	{
		return this.analyzer.analyzeDNSRecords(domain);
	}
}

export { DNSModule };

export default DNSModule;
