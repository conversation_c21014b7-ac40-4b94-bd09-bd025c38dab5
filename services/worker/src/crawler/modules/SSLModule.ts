import { DataCollectionModule, DataCollectionModuleConfig } from '../core/DataCollectionModule';
import type { SSLAnalysisResultType } from '../analyzers/SSLAnalyzer';
import SSLAnalyzer from '../analyzers/SSLAnalyzer';

/**
 * SSL Certificate Data Collection Module
 */
class SSLModule extends DataCollectionModule<SSLAnalysisResultType>
{
	private analyzer: SSLAnalyzer;

	constructor()
	{
		const config: DataCollectionModuleConfig =
		{
			name: 'ssl',
			priority: 80, // Medium-high priority
			timeout: 12000, // 12 seconds
			retryAttempts: 2,
			retryDelay: 1500, // 1.5 seconds
			dependencies: ['dns'], // Depends on DNS to know if domain is accessible
		};

		super(config);
		this.analyzer = new SSLAnalyzer();
	}

	protected async collect(domain: string): Promise<SSLAnalysisResultType>
	{
		return this.analyzer.analyzeSSL(domain);
	}
}

export default SSLModule;
