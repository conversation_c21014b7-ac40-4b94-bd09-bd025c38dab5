import { DataCollectionModule, DataCollectionModuleConfig } from '../core/DataCollectionModule';
import HomepageAnalyzer, { HomepageAnalysisResult } from '../analyzers/HomepageAnalyzer';

/**
 * Homepage Analysis Data Collection Module
 */
class HomepageModule extends DataCollectionModule<HomepageAnalysisResult>
{
	private analyzer: HomepageAnalyzer;

	constructor()
	{
		const config: DataCollectionModuleConfig = {
			name: 'homepage',
			priority: 70, // Medium priority
			timeout: 20000, // 20 seconds
			retryAttempts: 2,
			retryDelay: 2000, // 2 seconds
			dependencies: ['dns', 'robots'], // Depends on DNS and robots.txt for compliance
		};

		super(config);
		this.analyzer = new HomepageAnalyzer();
	}

	protected async collect(domain: string): Promise<HomepageAnalysisResult>
	{
		return this.analyzer.analyzeHomepage(domain);
	}
}

export { HomepageModule };

export default HomepageModule;
