import DataCollectionModule, { DataCollectionModuleConfigType } from '../core/DataCollectionModule';
import AdvancedContentAnalyzer, { AdvancedContentAnalysisResult } from '../analyzers/AdvancedContentAnalyzer';

/**
 * Advanced Content Analysis Data Collection Module
 * Performs comprehensive multi-page content analysis including quality scoring,
 * readability analysis, and language detection
 */
class AdvancedContentModule extends DataCollectionModule<AdvancedContentAnalysisResult>
{
	private analyzer: AdvancedContentAnalyzer;

	constructor()
	{
		const config: DataCollectionModuleConfigType =
		{
			name: 'advanced-content',
			priority: 40, // Lower priority due to resource intensity
			timeout: 60000, // 60 seconds for multi-page analysis
			retryAttempts: 2,
			retryDelay: 5000, // 5 seconds
			dependencies: ['dns', 'robots', 'homepage'], // Depends on basic checks first
		};

		super(config);
		this.analyzer = new AdvancedContentAnalyzer();
	}

	protected async collect(domain: string): Promise<AdvancedContentAnalysisResult>
	{
		return this.analyzer.analyzeContent(domain);
	}
}

export { AdvancedContentModule };

export default AdvancedContentModule;
