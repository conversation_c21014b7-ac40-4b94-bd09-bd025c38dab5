import { DataCollectionModule, DataCollectionModuleConfig } from '../core/DataCollectionModule';
import DomainInfoAnalyzer, { DomainInfoAnalysisResultType } from '../analyzers/DomainInfoAnalyzer';

/**
 * Domain Information Data Collection Module
 */
class DomainInfoModule extends DataCollectionModule<DomainInfoAnalysisResultType>
{
	private analyzer: DomainInfoAnalyzer;

	constructor()
	{
		const config: DataCollectionModuleConfig = {
			name: 'domainInfo',
			priority: 85, // Medium-high priority
			timeout: 20000, // 20 seconds (WHOIS can be slow)
			retryAttempts: 2,
			retryDelay: 3000, // 3 seconds
			dependencies: [], // No dependencies
		};

		super(config);
		this.analyzer = new DomainInfoAnalyzer();
	}

	protected async collect(domain: string): Promise<DomainInfoAnalysisResultType>
	{
		return this.analyzer.analyzeDomainInfo(domain);
	}
}

export { DomainInfoModule };

export default DomainInfoModule;
