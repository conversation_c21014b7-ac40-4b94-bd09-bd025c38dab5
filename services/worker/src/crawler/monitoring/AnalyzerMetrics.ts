import { logger } from '@shared/utils/Logger';

const metricsLogger = logger.getLogger('AnalyzerMetrics');

type AnalyzerPerformanceMetricType =
{
	analyzerName: string;
	domain: string;
	startTime: number;
	endTime: number;
	duration: number;
	success: boolean;
	errorType?: string;
	retryCount: number;
	memoryUsage?: {
		heapUsed: number;
		heapTotal: number;
		external: number;
	};
};

type AnalyzerStatisticsType =
{
	totalExecutions: number;
	successfulExecutions: number;
	failedExecutions: number;
	averageDuration: number;
	minDuration: number;
	maxDuration: number;
	errorRate: number;
	commonErrors: Record<string, number>;
};

/**
 * Performance monitoring and metrics collection for analyzers
 */
class AnalyzerMetrics
{
	private metrics: Map<string, AnalyzerPerformanceMetricType[]> = new Map();

	private readonly maxMetricsPerAnalyzer: number = 1000;

	/**
	 * Record the start of an analyzer execution
	 */
	startExecution(analyzerName: string, domain: string): string
	{
		const executionId = `${analyzerName}-${domain}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

		metricsLogger.debug(`Starting execution tracking for ${analyzerName}`, {
			executionId,
			domain,
			analyzerName,
		});

		return executionId;
	}

	/**
	 * Record the completion of an analyzer execution
	 */
	recordExecution(
		analyzerName: string,
		domain: string,
		startTime: number,
		success: boolean,
		retryCount: number = 0,
		errorType?: string,
	): void
	{
		const endTime = Date.now();
		const duration = endTime - startTime;
		const memoryUsage = process.memoryUsage();

		const metric: AnalyzerPerformanceMetricType =
		{
			analyzerName,
			domain,
			startTime,
			endTime,
			duration,
			success,
			errorType,
			retryCount,
			memoryUsage: {
				heapUsed: memoryUsage.heapUsed,
				heapTotal: memoryUsage.heapTotal,
				external: memoryUsage.external,
			},
		};

		// Store metric
		if (!this.metrics.has(analyzerName))
		{
			this.metrics.set(analyzerName, []);
		}

		const analyzerMetrics = this.metrics.get(analyzerName)!;
		analyzerMetrics.push(metric);

		// Keep only the most recent metrics to prevent memory leaks
		if (analyzerMetrics.length > this.maxMetricsPerAnalyzer)
		{
			analyzerMetrics.shift();
		}

		// Log performance metric
		metricsLogger.info('Analyzer execution completed', {
			analyzerName,
			domain,
			duration,
			success,
			retryCount,
			errorType,
			memoryUsage: metric.memoryUsage,
		});

		// Log warning for slow executions
		if (duration > 30000)
		{ // 30 seconds
			metricsLogger.warn('Slow analyzer execution detected', {
				analyzerName,
				domain,
				duration,
			});
		}
	}

	/**
	 * Get statistics for a specific analyzer
	 */
	getAnalyzerStatistics(analyzerName: string): AnalyzerStatisticsType | null
	{
		const analyzerMetrics = this.metrics.get(analyzerName);
		if (!analyzerMetrics || analyzerMetrics.length === 0)
		{
			return null;
		}

		const totalExecutions = analyzerMetrics.length;
		const successfulExecutions = analyzerMetrics.filter(m => m.success).length;
		const failedExecutions = totalExecutions - successfulExecutions;

		const durations = analyzerMetrics.map(m => m.duration);
		const averageDuration = durations.reduce((sum, d) => sum + d, 0) / durations.length;
		const minDuration = Math.min(...durations);
		const maxDuration = Math.max(...durations);

		const errorRate = (failedExecutions / totalExecutions) * 100;

		// Count common errors
		const commonErrors: Record<string, number> = {};
		analyzerMetrics
			.filter(m => !m.success && m.errorType)
			.forEach((m) =>
			{
				const errorType = m.errorType!;
				commonErrors[errorType] = (commonErrors[errorType] || 0) + 1;
			});

		return {
			totalExecutions,
			successfulExecutions,
			failedExecutions,
			averageDuration,
			minDuration,
			maxDuration,
			errorRate,
			commonErrors,
		};
	}

	/**
	 * Get statistics for all analyzers
	 */
	getAllStatistics(): Record<string, AnalyzerStatisticsType>
	{
		const allStats: Record<string, AnalyzerStatisticsType> = {};

		for (const analyzerName of this.metrics.keys())
		{
			const stats = this.getAnalyzerStatistics(analyzerName);
			if (stats)
			{
				allStats[analyzerName] = stats;
			}
		}

		return allStats;
	}

	/**
	 * Get recent metrics for an analyzer
	 */
	getRecentMetrics(analyzerName: string, limit: number = 10): AnalyzerPerformanceMetricType[]
	{
		const analyzerMetrics = this.metrics.get(analyzerName);
		if (!analyzerMetrics)
		{
			return [];
		}

		return analyzerMetrics.slice(-limit);
	}

	/**
	 * Clear metrics for an analyzer
	 */
	clearAnalyzerMetrics(analyzerName: string): void
	{
		this.metrics.delete(analyzerName);
		metricsLogger.info(`Cleared metrics for analyzer: ${analyzerName}`);
	}

	/**
	 * Clear all metrics
	 */
	clearAllMetrics(): void
	{
		this.metrics.clear();
		metricsLogger.info('Cleared all analyzer metrics');
	}

	/**
	 * Get health status based on recent performance
	 */
	getHealthStatus(analyzerName: string): {
		status: 'healthy' | 'degraded' | 'unhealthy';
		reason?: string;
	}
	{
		const stats = this.getAnalyzerStatistics(analyzerName);
		if (!stats)
		{
			return { status: 'healthy', reason: 'No metrics available' };
		}

		// Check error rate
		if (stats.errorRate > 50)
		{
			return { status: 'unhealthy', reason: `High error rate: ${stats.errorRate.toFixed(1)}%` };
		}

		if (stats.errorRate > 20)
		{
			return { status: 'degraded', reason: `Elevated error rate: ${stats.errorRate.toFixed(1)}%` };
		}

		// Check average duration
		if (stats.averageDuration > 60000)
		{ // 1 minute
			return { status: 'degraded', reason: `Slow average response time: ${stats.averageDuration}ms` };
		}

		return { status: 'healthy' };
	}

	/**
	 * Generate performance report
	 */
	generateReport(): string
	{
		const allStats = this.getAllStatistics();
		const report: string[] = [];

		report.push('=== Analyzer Performance Report ===');
		report.push(`Generated at: ${new Date().toISOString()}`);
		report.push('');

		for (const [analyzerName, stats] of Object.entries(allStats))
		{
			const health = this.getHealthStatus(analyzerName);

			report.push(`Analyzer: ${analyzerName}`);
			report.push(`  Status: ${health.status}${health.reason ? ` (${health.reason})` : ''}`);
			report.push(`  Total Executions: ${stats.totalExecutions}`);
			report.push(`  Success Rate: ${((stats.successfulExecutions / stats.totalExecutions) * 100).toFixed(1)}%`);
			report.push(`  Average Duration: ${stats.averageDuration.toFixed(0)}ms`);
			report.push(`  Duration Range: ${stats.minDuration}ms - ${stats.maxDuration}ms`);

			if (Object.keys(stats.commonErrors).length > 0)
			{
				report.push('  Common Errors:');
				for (const [errorType, count] of Object.entries(stats.commonErrors))
				{
					report.push(`    ${errorType}: ${count}`);
				}
			}

			report.push('');
		}

		return report.join('\n');
	}
}

// Singleton instance
const analyzerMetrics = new AnalyzerMetrics();

export { analyzerMetrics };

export default AnalyzerMetrics;
