import { Curl } from 'node-libcurl';
import { logger as sharedLogger, config } from '@shared';

const logger = sharedLogger.getLogger('ScreenshotAnalyzer');

type ScreenshotResultType =
{
	domain: string;
	screenshots: {
		desktop: {
			url?: string;
			optimizedUrl?: string;
			width: number;
			height: number;
			format: string;
			size?: number;
			compressionRatio?: number;
			error?: string;
		};
		mobile: {
			url?: string;
			optimizedUrl?: string;
			width: number;
			height: number;
			format: string;
			size?: number;
			compressionRatio?: number;
			error?: string;
		};
	};
	metadata: {
		browserlessVersion?: string;
		captureSettings: {
			timeout: number;
			waitUntil: string;
			quality: number;
		};
		optimization: {
			enabled: boolean;
			service: string;
			quality: number;
		};
	};
	captureTime: number;
	lastCaptured: string;
	error?: string;
};

/**
 * Screenshot Analyzer
 * Captures homepage screenshots using browserless and optimizes them using weserv image proxy
 */
class ScreenshotAnalyzer
{
	private readonly BROWSERLESS_URL: string;

	private readonly IMAGE_PROXY_URL: string;

	private readonly TIMEOUT = 30000;

	private readonly DESKTOP_VIEWPORT = { width: 1920, height: 1080 };

	private readonly MOBILE_VIEWPORT = { width: 375, height: 667 };

	constructor()
	{
		this.BROWSERLESS_URL = process.env.BROWSERLESS_URL || 'http://browserless:3000';
		this.IMAGE_PROXY_URL = config.get('IMAGE_PROXY_URL');
	}

	/**
	 * Capture screenshots for both desktop and mobile viewports
	 */
	async captureScreenshots(domain: string): Promise<ScreenshotResultType>
	{
		logger.info(`Capturing screenshots for domain: ${domain}`);

		const startTime = Date.now();
		const url = `https://${domain}`;

		const result: ScreenshotResultType = {
			domain,
			screenshots: {
				desktop: {
					width: this.DESKTOP_VIEWPORT.width,
					height: this.DESKTOP_VIEWPORT.height,
					format: 'webp',
				},
				mobile: {
					width: this.MOBILE_VIEWPORT.width,
					height: this.MOBILE_VIEWPORT.height,
					format: 'webp',
				},
			},
			metadata: {
				captureSettings: {
					timeout: this.TIMEOUT,
					waitUntil: 'networkidle2',
					quality: 90,
				},
				optimization: {
					enabled: !!this.IMAGE_PROXY_URL,
					service: 'weserv',
					quality: 80,
				},
			},
			captureTime: 0,
			lastCaptured: new Date().toISOString(),
		};

		try
		{
			// Capture both desktop and mobile screenshots in parallel
			const [desktopResult, mobileResult] = await Promise.allSettled([
				this.captureScreenshot(url, this.DESKTOP_VIEWPORT, 'desktop'),
				this.captureScreenshot(url, this.MOBILE_VIEWPORT, 'mobile'),
			]);

			// Process desktop screenshot result
			if (desktopResult.status === 'fulfilled')
			{
				result.screenshots.desktop = {
					...result.screenshots.desktop,
					...desktopResult.value,
				};
			}
			else
			{
				result.screenshots.desktop.error = desktopResult.reason?.message || 'Desktop screenshot failed';
				logger.warn(`Desktop screenshot failed for ${domain}:`, desktopResult.reason);
			}

			// Process mobile screenshot result
			if (mobileResult.status === 'fulfilled')
			{
				result.screenshots.mobile =
				{
					...result.screenshots.mobile,
					...mobileResult.value,
				};
			}
			else
			{
				result.screenshots.mobile.error = mobileResult.reason?.message || 'Mobile screenshot failed';
				logger.warn(`Mobile screenshot failed for ${domain}:`, mobileResult.reason);
			}

			result.captureTime = Date.now() - startTime;

			// If both screenshots failed, set overall error
			if (result.screenshots.desktop.error && result.screenshots.mobile.error)
			{
				result.error = 'Both desktop and mobile screenshot capture failed';
			}

			logger.info(`Screenshot capture completed for domain: ${domain}`, {
				captureTime: result.captureTime,
				desktopSuccess: !result.screenshots.desktop.error,
				mobileSuccess: !result.screenshots.mobile.error,
			});

			return result;
		}
		catch (error)
		{
			logger.error(`Screenshot capture failed for domain: ${domain}:`, error);
			result.error = (error as Error).message;
			result.captureTime = Date.now() - startTime;

			// Ensure both screenshots have error messages
			result.screenshots.desktop.error = result.screenshots.desktop.error || (error as Error).message;
			result.screenshots.mobile.error = result.screenshots.mobile.error || (error as Error).message;

			return result;
		}
	}

	/**
	 * Capture a single screenshot with specified viewport
	 */
	private async captureScreenshot(
		url: string,
		viewport: { width: number; height: number },
		type: 'desktop' | 'mobile',
	): Promise<{
		url?: string;
		optimizedUrl?: string;
		size?: number;
		compressionRatio?: number;
		error?: string;
	}>
	{
		try
		{
			logger.debug(`Capturing ${type} screenshot for: ${url}`);

			// Prepare enhanced browserless screenshot request
			const screenshotRequest = {
				url,
				options: {
					viewport,
					fullPage: false,
					type: 'png', // Capture as PNG first for quality
					quality: 90,
					clip: {
						x: 0,
						y: 0,
						width: viewport.width,
						height: Math.min(viewport.height, 1200), // Limit height for performance
					},
					// Enhanced capture settings
					omitBackground: false,
					captureBeyondViewport: false,
				},
				gotoOptions: {
					waitUntil: 'networkidle2',
					timeout: this.TIMEOUT,
				},
				// Additional browserless options for better screenshots
				setJavaScriptEnabled: true,
				setUserAgent: this.getUserAgent(type),
				blockAds: true, // Block ads for cleaner screenshots
				stealth: true, // Use stealth mode to avoid detection
			};

			// Capture screenshot using browserless with node-libcurl
			const response = await new Promise<{ status: number; data: Buffer }>((resolve, reject) =>
			{
				const curl = new Curl();
				const url = `${this.BROWSERLESS_URL}/screenshot`;

				curl.setOpt('URL', url);
				curl.setOpt('POSTFIELDS', JSON.stringify(screenshotRequest));
				curl.setOpt('HTTPHEADER', [
					'Content-Type: application/json',
					`User-Agent: ${this.getUserAgent(type)}`
				]);
				curl.setOpt('FOLLOWLOCATION', true);
				curl.setOpt('TIMEOUT_MS', this.TIMEOUT);
				curl.setOpt('ACCEPT_ENCODING', ''); // Disable compression

				// Set to receive binary data
				curl.setOpt('BINARYTRANSFER', true);

				curl.on('end', (statusCode, data) =>
				{
					try
					{
						curl.close();
						resolve({
							status: statusCode,
							data: Buffer.from(data),
						});
					}
					catch (error)
					{
						curl.close();
						reject(error);
					}
				});

				curl.on('error', (error) =>
				{
					curl.close();
					reject(error);
				});

				curl.perform();
			});

			if (response.status !== 200)
			{
				throw new Error(`Browserless returned status ${response.status}`);
			}

			const screenshotBuffer = Buffer.from(response.data);
			const originalSize = screenshotBuffer.length;

			// Convert screenshot to base64 for processing
			const base64Screenshot = screenshotBuffer.toString('base64');
			const dataUrl = `data:image/png;base64,${base64Screenshot}`;

			// Generate optimized WebP version using weserv image proxy
			const optimizationResult = await this.optimizeScreenshot(dataUrl, viewport.width, type);

			// Calculate compression ratio if optimization was successful
			let compressionRatio: number | undefined;
			if (optimizationResult?.optimizedSize)
			{
				compressionRatio = Math.round(((originalSize - optimizationResult.optimizedSize) / originalSize) * 100);
			}

			logger.debug(`${type} screenshot captured successfully`, {
				originalSize,
				optimizedSize: optimizationResult?.optimizedSize,
				compressionRatio,
				optimizedUrl: !!optimizationResult?.optimizedUrl,
			});

			return {
				url: dataUrl,
				optimizedUrl: optimizationResult?.optimizedUrl,
				size: originalSize,
				compressionRatio,
			};
		}
		catch (error)
		{
			logger.error(`Failed to capture ${type} screenshot:`, error);
			throw error;
		}
	}

	/**
	 * Optimize screenshot using weserv image proxy
	 */
	private async optimizeScreenshot(
		dataUrl: string,
		width: number,
		type: 'desktop' | 'mobile',
	): Promise<{
		optimizedUrl?: string;
		optimizedSize?: number;
	}>
	{
		try
		{
			if (!this.IMAGE_PROXY_URL)
			{
				logger.debug('Image proxy URL not configured, skipping optimization');
				return {};
			}

			// Create weserv URL for WebP conversion with compression
			const weservParams = new URLSearchParams({
				url: dataUrl,
				output: 'webp',
				q: '80', // Quality 80%
				w: width.toString(),
				fit: 'inside',
				we: '1', // Enable WebP encoding
				// Additional optimization parameters
				af: '1', // Auto-format (choose best format)
				il: '1', // Interlace/progressive
				n: '-1', // Normalize image
			});

			const optimizedUrl = `${this.IMAGE_PROXY_URL}/?${weservParams.toString()}`;

			// Test optimization by fetching the optimized image with node-libcurl
			const testResponse = await new Promise<{ status: number; data: Buffer }>((resolve, reject) =>
			{
				const curl = new Curl();

				curl.setOpt('URL', optimizedUrl);
				curl.setOpt('FOLLOWLOCATION', true);
				curl.setOpt('TIMEOUT_MS', 15000);
				curl.setOpt('ACCEPT_ENCODING', ''); // Disable compression
				curl.setOpt('MAXFILESIZE', 5 * 1024 * 1024); // 5MB limit

				// Set to receive binary data
				curl.setOpt('BINARYTRANSFER', true);

				curl.on('end', (statusCode, data) =>
				{
					try
					{
						curl.close();
						resolve({
							status: statusCode,
							data: Buffer.from(data),
						});
					}
					catch (error)
					{
						curl.close();
						reject(error);
					}
				});

				curl.on('error', (error) =>
				{
					curl.close();
					reject(error);
				});

				curl.perform();
			});

			if (testResponse.status === 200)
			{
				const optimizedSize = Buffer.from(testResponse.data).length;

				logger.debug(`Screenshot optimization successful for ${type}`, {
					originalUrl: `${dataUrl.substring(0, 50) }...`,
					optimizedUrl: `${optimizedUrl.substring(0, 100) }...`,
					optimizedSize,
				});

				return {
					optimizedUrl,
					optimizedSize,
				};
			}

			logger.warn('Image proxy optimization test failed - invalid response');
			return {};
		}
		catch (error)
		{
			logger.warn(`Failed to optimize ${type} screenshot with image proxy:`, error);
			return {};
		}
	}

	/**
	 * Get appropriate user agent for screenshot capture
	 */
	private getUserAgent(type: 'desktop' | 'mobile'): string
	{
		if (type === 'mobile')
		{
			return 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1';
		}

		return 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
	}

	/**
	 * Get screenshot storage path for a domain
	 */
	private getScreenshotPath(domain: string, type: 'desktop' | 'mobile'): string
	{
		const timestamp = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
		return `screenshots/${domain}/${timestamp}/${type}.webp`;
	}

	/**
	 * Capture screenshot with custom viewport settings
	 */
	async captureCustomScreenshot(
		domain: string,
		viewport: { width: number; height: number },
		options: {
			fullPage?: boolean;
			quality?: number;
			format?: 'png' | 'jpeg' | 'webp';
			timeout?: number;
		} = {},
	): Promise<{
		url?: string;
		optimizedUrl?: string;
		size?: number;
		compressionRatio?: number;
		error?: string;
	}>
	{
		const url = `https://${domain}`;
		const screenshotOptions = {
			fullPage: options.fullPage || false,
			quality: options.quality || 90,
			format: options.format || 'png',
			timeout: options.timeout || this.TIMEOUT,
		};

		try
		{
			logger.debug(`Capturing custom screenshot for: ${url}`, { viewport, options: screenshotOptions });

			const screenshotRequest = {
				url,
				options: {
					viewport,
					fullPage: screenshotOptions.fullPage,
					type: screenshotOptions.format,
					quality: screenshotOptions.quality,
					omitBackground: false,
					captureBeyondViewport: false,
				},
				gotoOptions: {
					waitUntil: 'networkidle2',
					timeout: screenshotOptions.timeout,
				},
				setJavaScriptEnabled: true,
				setUserAgent: this.getUserAgent('desktop'),
				blockAds: true,
				stealth: true,
			};

			const response = await new Promise<{ status: number; data: Buffer }>((resolve, reject) =>
			{
				const curl = new Curl();
				const requestUrl = `${this.BROWSERLESS_URL}/screenshot`;

				curl.setOpt('URL', requestUrl);
				curl.setOpt('POSTFIELDS', JSON.stringify(screenshotRequest));
				curl.setOpt('HTTPHEADER', [
					'Content-Type: application/json',
				]);
				curl.setOpt('FOLLOWLOCATION', true);
				curl.setOpt('TIMEOUT_MS', screenshotOptions.timeout);
				curl.setOpt('ACCEPT_ENCODING', ''); // Disable compression

				// Set to receive binary data
				curl.setOpt('NOBODY', false);

				curl.on('end', (statusCode, data) =>
				{
					try
					{
						curl.close();
						resolve({
							status: statusCode,
							data: Buffer.from(data),
						});
					}
					catch (error)
					{
						curl.close();
						reject(error);
					}
				});

				curl.on('error', (error) =>
				{
					curl.close();
					reject(error);
				});

				curl.perform();
			});

			if (response.status !== 200)
			{
				throw new Error(`Browserless returned status ${response.status}`);
			}

			const screenshotBuffer = Buffer.from(response.data);
			const originalSize = screenshotBuffer.length;
			const base64Screenshot = screenshotBuffer.toString('base64');
			const dataUrl = `data:image/${screenshotOptions.format};base64,${base64Screenshot}`;

			// Optimize if WebP format is requested
			let optimizationResult: { optimizedUrl?: string; optimizedSize?: number } = {};
			if (screenshotOptions.format === 'png')
			{
				optimizationResult = await this.optimizeScreenshot(dataUrl, viewport.width, 'desktop');
			}

			let compressionRatio: number | undefined;
			if (optimizationResult?.optimizedSize)
			{
				compressionRatio = Math.round(((originalSize - optimizationResult.optimizedSize) / originalSize) * 100);
			}

			return {
				url: dataUrl,
				optimizedUrl: optimizationResult?.optimizedUrl,
				size: originalSize,
				compressionRatio,
			};
		}
		catch (error)
		{
			logger.error(`Failed to capture custom screenshot for ${domain}:`, error);
			throw error;
		}
	}

	/**
	 * Get browserless service status and metrics
	 */
	async getBrowserlessStatus(): Promise<{
		available: boolean;
		pressure?: any;
		stats?: any;
		version?: string;
	}>
	{
		try
		{
			const [pressureResponse, statsResponse] = await Promise.allSettled([
				new Promise<{ status: number; data: any }>((resolve, reject) =>
				{
					const curl = new Curl();

					curl.setOpt('URL', `${this.BROWSERLESS_URL}/pressure`);
					curl.setOpt('TIMEOUT_MS', 5000);

					curl.on('end', (statusCode, data) =>
					{
						try
						{
							curl.close();
							resolve({
								status: statusCode,
								data: JSON.parse(data.toString()),
							});
						}
						catch (error)
						{
							curl.close();
							reject(error);
						}
					});

					curl.on('error', (error) =>
					{
						curl.close();
						reject(error);
					});

					curl.perform();
				}),
				new Promise<{ status: number; data: any }>((resolve, reject) =>
				{
					const curl = new Curl();

					curl.setOpt('URL', `${this.BROWSERLESS_URL}/stats`);
					curl.setOpt('TIMEOUT_MS', 5000);

					curl.on('end', (statusCode, data) =>
					{
						try
						{
							curl.close();
							resolve({
								status: statusCode,
								data: JSON.parse(data.toString()),
							});
						}
						catch (error)
						{
							curl.close();
							reject(error);
						}
					});

					curl.on('error', (error) =>
					{
						curl.close();
						reject(error);
					});

					curl.perform();
				}),
			]);

			const result: any = {
				available: false,
			};

			if (pressureResponse.status === 'fulfilled' && pressureResponse.value.status === 200)
			{
				result.available = true;
				result.pressure = pressureResponse.value.data;
			}

			if (statsResponse.status === 'fulfilled' && statsResponse.value.status === 200)
			{
				result.stats = statsResponse.value.data;
			}

			return result;
		}
		catch (error)
		{
			logger.warn('Failed to get browserless status:', error);
			return { available: false };
		}
	}

	/**
	 * Validate screenshot capture capability
	 */
	async validateCapability(): Promise<{
		browserlessAvailable: boolean;
		imageProxyAvailable: boolean;
		browserlessStatus?: any;
		error?: string;
	}>
	{
		const result = {
			browserlessAvailable: false,
			imageProxyAvailable: false,
		};

		try
		{
			// Test browserless availability and get detailed status
			const browserlessStatus = await this.getBrowserlessStatus();
			result.browserlessAvailable = browserlessStatus.available;
			(result as any).browserlessStatus = browserlessStatus;
		}
		catch (error)
		{
			logger.warn('Browserless not available:', error);
		}

		try
		{
			// Test image proxy availability
			if (this.IMAGE_PROXY_URL)
			{
				const imageProxyResponse = await axios.head(
					this.IMAGE_PROXY_URL,
					{ timeout: 5000 },
				);
				result.imageProxyAvailable = imageProxyResponse.status === 200;
			}
		}
		catch (error)
		{
			logger.warn('Image proxy not available:', error);
		}

		return result;
	}

	/**
	 * Batch capture screenshots for multiple domains
	 */
	async batchCaptureScreenshots(
		domains: string[],
		options: {
			concurrency?: number;
			includeDesktop?: boolean;
			includeMobile?: boolean;
		} = {},
	): Promise<ScreenshotResultType[]>
	{
		const concurrency = options.concurrency || 2; // Lower concurrency for resource-intensive screenshots
		const includeDesktop = options.includeDesktop !== false;
		const includeMobile = options.includeMobile !== false;

		logger.info(`Starting batch screenshot capture for ${domains.length} domains`, {
			concurrency,
			includeDesktop,
			includeMobile,
		});

		const results: ScreenshotResultType[] = [];
		const chunks = [];

		// Split domains into chunks for controlled concurrency
		for (let i = 0; i < domains.length; i += concurrency)
		{
			chunks.push(domains.slice(i, i + concurrency));
		}

		for (const chunk of chunks)
		{
			const chunkPromises = chunk.map(async (domain) =>
			{
				try
				{
					return await this.captureScreenshots(domain);
				}
				catch (error)
				{
					logger.error(`Batch screenshot capture failed for ${domain}:`, error);
					return {
						domain,
						screenshots: {
							desktop: {
								width: this.DESKTOP_VIEWPORT.width,
								height: this.DESKTOP_VIEWPORT.height,
								format: 'webp',
								error: (error as Error).message,
							},
							mobile: {
								width: this.MOBILE_VIEWPORT.width,
								height: this.MOBILE_VIEWPORT.height,
								format: 'webp',
								error: (error as Error).message,
							},
						},
						metadata: {
							captureSettings: {
								timeout: this.TIMEOUT,
								waitUntil: 'networkidle2',
								quality: 90,
							},
							optimization: {
								enabled: !!this.IMAGE_PROXY_URL,
								service: 'weserv',
								quality: 80,
							},
						},
						captureTime: 0,
						lastCaptured: new Date().toISOString(),
						error: (error as Error).message,
					} as ScreenshotResultType;
				}
			});

			const chunkResults = await Promise.all(chunkPromises);
			results.push(...chunkResults);

			// Add small delay between chunks to avoid overwhelming the browserless service
			if (chunks.indexOf(chunk) < chunks.length - 1)
			{
				await new Promise(resolve => setTimeout(resolve, 1000));
			}
		}

		logger.info('Batch screenshot capture completed', {
			totalDomains: domains.length,
			successfulCaptures: results.filter(r => !r.error).length,
			failedCaptures: results.filter(r => r.error).length,
		});

		return results;
	}
}

export { ScreenshotResultType as ScreenshotResult };
export default ScreenshotAnalyzer;
