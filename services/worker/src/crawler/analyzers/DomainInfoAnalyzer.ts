import { exec } from 'child_process';
import { promisify } from 'util';
import { Curl } from 'node-libcurl';
import { logger } from '@shared/utils/Logger';

const execAsync = promisify(exec);
const domainInfoLogger = logger.getLogger('DomainInfoAnalyzer');

type WHOISApiResponseType =
{
	registrar?: string;
	creation_date?: string;
	created_date?: string;
	expiration_date?: string;
	expires_date?: string;
	updated_date?: string;
	last_updated?: string;
	name_servers?: string[];
	status?: string | string[];
	registrant_country?: string;
	registrant_organization?: string;
	registrant_org?: string;
	admin_email?: string;
	tech_email?: string;
};

type DomainInfoAnalysisResultType =
{
	domain: string;
	whoisData?: {
		registrar?: string;
		registrationDate?: string;
		expirationDate?: string;
		lastUpdated?: string;
		nameServers: string[];
		status: string[];
		registrantCountry?: string;
		registrantOrganization?: string;
		adminEmail?: string;
		techEmail?: string;
		daysUntilExpiry?: number;
		domainAge?: number;
	};
	validation: {
		isValid: boolean;
		isTLD: boolean;
		isSubdomain: boolean;
		normalizedDomain: string;
	};
	lastAnalyzed: string;
	error?: string;
};

/**
 * Domain Info Analyzer
 * Collects WHOIS data and basic domain information
 */
class DomainInfoAnalyzer
{
	private readonly WHOIS_TIMEOUT = 15000;

	/**
	 * Analyze domain information including WHOIS data
	 */
	async analyzeDomainInfo(domain: string): Promise<DomainInfoAnalysisResultType>
	{
		domainInfoLogger.info(`Analyzing domain info for: ${domain}`);

		const result: DomainInfoAnalysisResultType =
		{
			domain,
			validation: {
				isValid: false,
				isTLD: false,
				isSubdomain: false,
				normalizedDomain: domain.toLowerCase().trim(),
			},
			lastAnalyzed: new Date().toISOString(),
		};

		try
		{
			// Validate and normalize domain
			result.validation = this.validateDomain(domain);

			if (!result.validation.isValid)
			{
				throw new Error(`Invalid domain: ${domain}`);
			}

			// Get WHOIS data
			result.whoisData = await this.getWhoisData(result.validation.normalizedDomain);

			domainInfoLogger.info(`Domain info analysis completed for: ${domain}`, {
				hasWhoisData: !!result.whoisData,
				domainAge: result.whoisData?.domainAge,
			});

			return result;
		}
		catch (error)
		{
			domainInfoLogger.error(`Domain info analysis failed for: ${domain}:`, error);
			result.error = (error as Error).message;
			return result;
		}
	}

	/**
	 * Validate and normalize domain
	 */
	private validateDomain(domain: string): DomainInfoAnalysisResultType['validation']
	{
		const validation = {
			isValid: false,
			isTLD: false,
			isSubdomain: false,
			normalizedDomain: domain.toLowerCase().trim(),
		};

		try
		{
			// Remove protocol and path, but keep the full domain for subdomain detection
			const fullDomain = validation.normalizedDomain
				.replace(/^https?:\/\//, '')
				.replace(/\/.*$/, '');

			// For normalization (WHOIS lookup), remove www
			const cleanDomain = fullDomain.replace(/^www\./, '');

			validation.normalizedDomain = cleanDomain;

			// Basic domain validation using the full domain
			const domainRegex = /^[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?(\.[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i;

			if (!domainRegex.test(fullDomain))
			{
				return validation;
			}

			// Check if it's a valid domain structure using the full domain
			const parts = fullDomain.split('.');

			if (parts.length < 2)
			{
				return validation;
			}

			// Check if it's a subdomain (more than 2 parts for most TLDs)
			validation.isSubdomain = parts.length > 2;

			// Check if it's a TLD (single part)
			validation.isTLD = parts.length === 1;

			validation.isValid = true;
		}
		catch (error)
		{
			domainInfoLogger.debug(`Domain validation error for ${domain}:`, error);
		}

		return validation;
	}

	/**
	 * Get WHOIS data for domain
	 */
	private async getWhoisData(domain: string): Promise<DomainInfoAnalysisResultType['whoisData'] | undefined>
	{
		// Try system whois command first
		try
		{
			const { stdout } = await execAsync(`whois ${domain}`, {
				timeout: this.WHOIS_TIMEOUT,
			});

			const whoisData = this.parseWhoisData(stdout, domain);
			if (whoisData && (whoisData.registrar || whoisData.registrationDate))
			{
				return whoisData;
			}
		}
		catch (error)
		{
			domainInfoLogger.debug(`System WHOIS lookup failed for ${domain}:`, error);
		}

		// Fallback to WHOIS API service
		try
		{
			return await this.getWhoisDataFromAPI(domain);
		}
		catch (error)
		{
			domainInfoLogger.debug(`API WHOIS lookup failed for ${domain}:`, error);
		}

		// Final fallback to basic domain info without WHOIS
		return {
			nameServers: [],
			status: [],
			domainAge: undefined,
			daysUntilExpiry: undefined,
		};
	}

	/**
	 * Get WHOIS data from API service (fallback)
	 */
	private async getWhoisDataFromAPI(domain: string): Promise<DomainInfoAnalysisResultType['whoisData'] | undefined>
	{
		return new Promise((resolve, reject) =>
		{
			try
			{
				// Using whois.freeapi.app as a free WHOIS API service
				const curl = new Curl();
				const url = `https://whois.freeapi.app/api/whois?domainName=${domain}`;

				curl.setOpt('URL', url);
				curl.setOpt('FOLLOWLOCATION', true);
				curl.setOpt('TIMEOUT_MS', this.WHOIS_TIMEOUT);
				curl.setOpt('USERAGENT', 'DomainRankingBot/1.0');

				curl.on('end', (statusCode, data) =>
				{
					try
					{
						curl.close();

						if (statusCode !== 200)
						{
							reject(new Error(`HTTP ${statusCode}: ${data.toString()}`));
							return;
						}

						const response = JSON.parse(data.toString());

						if (response && response.status === 'success' && response.data)
						{
							resolve(this.parseAPIWhoisData(response.data, domain));
							return;
						}

						resolve(undefined);
					}
					catch (error)
					{
						curl.close();
						reject(error);
					}
				});

				curl.on('error', (error) =>
				{
					curl.close();
					domainInfoLogger.debug(`WHOIS API request failed for ${domain}:`, error);
					reject(error);
				});

				curl.perform();
			}
			catch (error)
			{
				reject(error);
			}
		});
	}

	/**
	 * Parse WHOIS data from API response
	 */
	private parseAPIWhoisData(apiData: WHOISApiResponseType, domain: string): DomainInfoAnalysisResultType['whoisData']
	{
		const whoisData: DomainInfoAnalysisResultType['whoisData'] =
		{
			nameServers: [],
			status: [],
		};

		try
		{
			// Extract registrar
			if (apiData.registrar)
			{
				whoisData.registrar = apiData.registrar;
			}

			// Extract dates
			const creationDateStr = apiData.creation_date || apiData.created_date;
			if (creationDateStr)
			{
				const date = new Date(creationDateStr);
				if (!Number.isNaN(date.getTime()))
				{
					whoisData.registrationDate = date.toISOString();
				}
			}

			const expirationDateStr = apiData.expiration_date || apiData.expires_date;
			if (expirationDateStr)
			{
				const date = new Date(expirationDateStr);
				if (!Number.isNaN(date.getTime()))
				{
					whoisData.expirationDate = date.toISOString();
				}
			}

			const updatedDateStr = apiData.updated_date || apiData.last_updated;
			if (updatedDateStr)
			{
				const date = new Date(updatedDateStr);
				if (!Number.isNaN(date.getTime()))
				{
					whoisData.lastUpdated = date.toISOString();
				}
			}

			// Extract name servers
			if (apiData.name_servers && Array.isArray(apiData.name_servers))
			{
				whoisData.nameServers = apiData.name_servers.filter((ns: string) => ns && typeof ns === 'string');
			}

			// Extract status
			if (apiData.status)
			{
				if (Array.isArray(apiData.status))
				{
					whoisData.status = apiData.status.filter((s: string) => s && typeof s === 'string');
				}
				else if (typeof apiData.status === 'string')
				{
					whoisData.status = [apiData.status];
				}
			}

			// Extract registrant info
			if (apiData.registrant_country)
			{
				whoisData.registrantCountry = apiData.registrant_country;
			}

			if (apiData.registrant_organization || apiData.registrant_org)
			{
				whoisData.registrantOrganization = apiData.registrant_organization || apiData.registrant_org;
			}

			if (apiData.admin_email)
			{
				whoisData.adminEmail = apiData.admin_email;
			}

			if (apiData.tech_email)
			{
				whoisData.techEmail = apiData.tech_email;
			}

			// Calculate domain age and days until expiry
			if (whoisData.registrationDate)
			{
				const regDate = new Date(whoisData.registrationDate);
				const now = new Date();
				whoisData.domainAge = Math.floor((now.getTime() - regDate.getTime()) / (1000 * 60 * 60 * 24));
			}

			if (whoisData.expirationDate)
			{
				const expDate = new Date(whoisData.expirationDate);
				const now = new Date();
				whoisData.daysUntilExpiry = Math.floor((expDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
			}
		}
		catch (error)
		{
			domainInfoLogger.debug(`Error parsing API WHOIS data for ${domain}:`, error);
		}

		return whoisData;
	}

	/**
	 * Parse WHOIS data from raw output
	 */
	private parseWhoisData(whoisOutput: string, domain: string): DomainInfoAnalysisResultType['whoisData']
	{
		const whoisData: DomainInfoAnalysisResultType['whoisData'] =
		{
			nameServers: [],
			status: [],
		};

		try
		{
			const lines = whoisOutput.split('\n');

			for (const line of lines)
			{
				const cleanLine = line.trim();

				if (!cleanLine || cleanLine.startsWith('%') || cleanLine.startsWith('#'))
				{
					continue;
				}

				// Registrar
				if (/registrar:/i.test(cleanLine))
				{
					whoisData.registrar = this.extractValue(cleanLine);
				}

				// Registration date
				if (/creation date|registered|registration time/i.test(cleanLine))
				{
					const date = this.extractDate(cleanLine);
					if (date) whoisData.registrationDate = date;
				}

				// Expiration date
				if (/expir|renewal/i.test(cleanLine))
				{
					const date = this.extractDate(cleanLine);
					if (date) whoisData.expirationDate = date;
				}

				// Last updated
				if (/updated|modified/i.test(cleanLine))
				{
					const date = this.extractDate(cleanLine);
					if (date) whoisData.lastUpdated = date;
				}

				// Name servers
				if (/name server|nserver|dns/i.test(cleanLine))
				{
					const nameServer = this.extractValue(cleanLine);
					if (nameServer && !whoisData.nameServers.includes(nameServer))
					{
						whoisData.nameServers.push(nameServer);
					}
				}

				// Status
				if (/status/i.test(cleanLine))
				{
					const status = this.extractValue(cleanLine);
					if (status && !whoisData.status.includes(status))
					{
						whoisData.status.push(status);
					}
				}

				// Registrant country
				if (/registrant.*country|country.*registrant/i.test(cleanLine))
				{
					whoisData.registrantCountry = this.extractValue(cleanLine);
				}

				// Registrant organization
				if (/registrant.*org|org.*registrant/i.test(cleanLine))
				{
					whoisData.registrantOrganization = this.extractValue(cleanLine);
				}

				// Admin email
				if (/admin.*email|email.*admin/i.test(cleanLine))
				{
					whoisData.adminEmail = this.extractValue(cleanLine);
				}

				// Tech email
				if (/tech.*email|email.*tech/i.test(cleanLine))
				{
					whoisData.techEmail = this.extractValue(cleanLine);
				}
			}

			// Calculate domain age and days until expiry
			if (whoisData.registrationDate)
			{
				const regDate = new Date(whoisData.registrationDate);
				const now = new Date();
				whoisData.domainAge = Math.floor(
					(now.getTime() - regDate.getTime())
					/ (1000 * 60 * 60 * 24)
				);
			}

			if (whoisData.expirationDate)
			{
				const expDate = new Date(whoisData.expirationDate);
				const now = new Date();
				whoisData.daysUntilExpiry = Math.floor(
					(expDate.getTime() - now.getTime())
					/ (1000 * 60 * 60 * 24)
				);
			}
		}
		catch (error)
		{
			domainInfoLogger.debug(`Error parsing WHOIS data for ${domain}:`, error);
		}

		return whoisData;
	}

	/**
	 * Extract value from WHOIS line
	 */
	private extractValue(line: string): string
	{
		const colonIndex = line.indexOf(':');
		if (colonIndex === -1) return '';

		return line.substring(colonIndex + 1).trim();
	}

	/**
	 * Extract date from WHOIS line
	 */
	private extractDate(line: string): string | null
	{
		try
		{
			const value = this.extractValue(line);

			// Try to parse various date formats
			const datePatterns = [
				/(\d{4}-\d{2}-\d{2})/,
				/(\d{2}\/\d{2}\/\d{4})/,
				/(\d{2}-\d{2}-\d{4})/,
				/(\d{4}\.\d{2}\.\d{2})/,
			];

			for (const pattern of datePatterns)
			{
				const match = value.match(pattern);
				if (match)
				{
					const date = new Date(match[1]);
					if (!Number.isNaN(date.getTime()))
					{
						return date.toISOString();
					}
				}
			}

			// Try direct parsing
			const date = new Date(value);
			if (!Number.isNaN(date.getTime()))
			{
				return date.toISOString();
			}

			return null;
		}
		catch
		{
			return null;
		}
	}
}

export default DomainInfoAnalyzer;
