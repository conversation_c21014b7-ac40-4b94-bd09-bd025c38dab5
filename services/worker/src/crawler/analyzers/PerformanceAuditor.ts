import axios from 'axios';
import { Logger, Config } from '@shared';
import { PerformanceMetrics } from '@shared/models/DomainModels';

const logger = Logger.getLogger('PerformanceAuditor');

type ResourceAnalysis = {
	totalRequests: number;
	totalSize: number;
	resourceTypes: Record<string, { count: number; size: number }>;
	largestResources: Array<{
		url: string;
		type: string;
		size: number;
		loadTime: number;
	}>;
};

type PerformanceAuditResult = {
	domain: string;
	metrics: PerformanceMetrics;
	resourceAnalysis: ResourceAnalysis;
	networkAnalysis: {
		connectionType: string;
		rtt: number;
		downlink: number;
		effectiveType: string;
	};
	auditDetails: {
		viewport: { width: number; height: number };
		userAgent: string;
		connectionSettings: any;
		auditTime: number;
		browserVersion?: string;
	};
	recommendations: string[];
	lastAudited: string;
	error?: string;
};

/**
 * Performance Auditor
 * Measures Core Web Vitals and performance metrics using browserless
 */
class PerformanceAuditor
{
	private readonly BROWSERLESS_URL: string;

	private readonly TIMEOUT = 60000; // 60 seconds for performance audits

	private readonly DEFAULT_VIEWPORT = { width: 1920, height: 1080 };

	constructor()
	{
		this.BROWSERLESS_URL = process.env.BROWSERLESS_URL || 'http://browserless:3000';
	}

	/**
	 * Perform comprehensive performance audit
	 */
	async auditPerformance(domain: string): Promise<PerformanceAuditResult>
	{
		logger.info(`Starting performance audit for domain: ${domain}`);

		const startTime = Date.now();
		const url = `https://${domain}`;

		const result: PerformanceAuditResult = {
			domain,
			metrics: {
				loadTime: 0,
				firstContentfulPaint: 0,
				largestContentfulPaint: 0,
				cumulativeLayoutShift: 0,
				firstInputDelay: 0,
				speedIndex: 0,
				score: 0,
			},
			resourceAnalysis: {
				totalRequests: 0,
				totalSize: 0,
				resourceTypes: {},
				largestResources: [],
			},
			networkAnalysis: {
				connectionType: 'unknown',
				rtt: 0,
				downlink: 0,
				effectiveType: 'unknown',
			},
			auditDetails: {
				viewport: this.DEFAULT_VIEWPORT,
				userAgent: this.getDesktopUserAgent(),
				connectionSettings: this.getConnectionSettings(),
				auditTime: 0,
				browserVersion: undefined,
			},
			recommendations: [],
			lastAudited: new Date().toISOString(),
		};

		try
		{
			let hasErrors = false;
			const errors: string[] = [];

			// Perform Core Web Vitals measurement
			try
			{
				const coreWebVitals = await this.measureCoreWebVitals(url);
				result.metrics = { ...result.metrics, ...coreWebVitals };
			}
			catch (error)
			{
				hasErrors = true;
				errors.push(`Core Web Vitals measurement failed: ${(error as Error).message}`);
				logger.warn(`Core Web Vitals measurement failed for ${domain}:`, error);
			}

			// Perform resource analysis
			try
			{
				const resourceAnalysis = await this.analyzeResources(url);
				result.resourceAnalysis = resourceAnalysis;
			}
			catch (error)
			{
				hasErrors = true;
				errors.push(`Resource analysis failed: ${(error as Error).message}`);
				logger.warn(`Resource analysis failed for ${domain}:`, error);
			}

			// Perform network analysis
			try
			{
				const networkAnalysis = await this.analyzeNetwork(url);
				result.networkAnalysis = networkAnalysis;
			}
			catch (error)
			{
				hasErrors = true;
				errors.push(`Network analysis failed: ${(error as Error).message}`);
				logger.warn(`Network analysis failed for ${domain}:`, error);
			}

			// Calculate performance score
			result.metrics.score = this.calculatePerformanceScore(result.metrics);

			// Generate recommendations
			result.recommendations = this.generateRecommendations(result);

			result.auditDetails.auditTime = Date.now() - startTime;

			// If there were errors but we got some data, set error but continue
			if (hasErrors)
			{
				result.error = errors.join('; ');
			}

			logger.info(`Performance audit completed for domain: ${domain}`, {
				auditTime: result.auditDetails.auditTime,
				performanceScore: result.metrics.score,
				loadTime: result.metrics.loadTime,
				lcp: result.metrics.largestContentfulPaint,
				cls: result.metrics.cumulativeLayoutShift,
				hasErrors,
			});

			return result;
		}
		catch (error)
		{
			logger.error(`Performance audit failed for domain: ${domain}:`, error);
			result.error = (error as Error).message;
			result.auditDetails.auditTime = Date.now() - startTime;

			// Ensure metrics are initialized when overall audit fails
			if (!result.metrics.loadTime)
			{
				result.metrics = {
					loadTime: 0,
					firstContentfulPaint: 0,
					largestContentfulPaint: 0,
					cumulativeLayoutShift: 0,
					firstInputDelay: 0,
					speedIndex: 0,
					score: 0,
				};
			}

			return result;
		}
	}

	/**
	 * Measure Core Web Vitals using browserless
	 */
	private async measureCoreWebVitals(url: string): Promise<Partial<PerformanceMetrics>>
	{
		logger.debug(`Measuring Core Web Vitals for: ${url}`);

		try
		{
			// Enhanced performance measurement script
			const performanceScript = `
				async () => {
					// Wait for page to be fully loaded
					await new Promise(resolve => {
						if (document.readyState === 'complete') {
							resolve();
						} else {
							window.addEventListener('load', resolve);
						}
					});

					// Additional wait for dynamic content
					await new Promise(resolve => setTimeout(resolve, 2000));

					const metrics = {};

					// Get Navigation Timing API data
					const navigation = performance.getEntriesByType('navigation')[0];
					if (navigation) {
						metrics.loadTime = navigation.loadEventEnd - navigation.fetchStart;
						metrics.domContentLoaded = navigation.domContentLoadedEventEnd - navigation.fetchStart;
						metrics.firstByte = navigation.responseStart - navigation.fetchStart;
					}

					// Get Paint Timing API data
					const paintEntries = performance.getEntriesByType('paint');
					paintEntries.forEach(entry => {
						if (entry.name === 'first-contentful-paint') {
							metrics.firstContentfulPaint = entry.startTime;
						}
					});

					// Get Largest Contentful Paint (LCP)
					try {
						const lcpEntries = performance.getEntriesByType('largest-contentful-paint');
						if (lcpEntries.length > 0) {
							metrics.largestContentfulPaint = lcpEntries[lcpEntries.length - 1].startTime;
						}
					} catch (e) {
						logger.warn('LCP not available:', e);
					}

					// Get Cumulative Layout Shift (CLS)
					try {
						let clsValue = 0;
						const observer = new PerformanceObserver((list) => {
							for (const entry of list.getEntries()) {
								if (!entry.hadRecentInput) {
									clsValue += entry.value;
								}
							}
						});
						observer.observe({ type: 'layout-shift', buffered: true });

						// Wait a bit for layout shifts to be captured
						await new Promise(resolve => setTimeout(resolve, 1000));
						observer.disconnect();

						metrics.cumulativeLayoutShift = clsValue;
					} catch (e) {
						logger.warn('CLS measurement failed:', e);
						metrics.cumulativeLayoutShift = 0;
					}

					// Get First Input Delay (FID) - approximation using event timing
					try {
						const fidEntries = performance.getEntriesByType('first-input');
						if (fidEntries.length > 0) {
							metrics.firstInputDelay = fidEntries[0].processingStart - fidEntries[0].startTime;
						} else {
							// Fallback: measure event handler delay
							const eventStart = performance.now();
							document.addEventListener('click', () => {
								metrics.firstInputDelay = performance.now() - eventStart;
							}, { once: true });
						}
					} catch (e) {
						logger.warn('FID measurement failed:', e);
						metrics.firstInputDelay = 0;
					}

					// Calculate Speed Index approximation
					try {
						const resourceEntries = performance.getEntriesByType('resource');
						let totalResourceTime = 0;
						let resourceCount = 0;

						resourceEntries.forEach(entry => {
							if (entry.initiatorType === 'img' || entry.initiatorType === 'css' || entry.initiatorType === 'script') {
								totalResourceTime += entry.responseEnd - entry.startTime;
								resourceCount++;
							}
						});

						metrics.speedIndex = resourceCount > 0 ? totalResourceTime / resourceCount : metrics.firstContentfulPaint || 0;
					} catch (e) {
						logger.warn('Speed Index calculation failed:', e);
						metrics.speedIndex = metrics.firstContentfulPaint || 0;
					}

					// Get additional performance data
					metrics.resourceCount = performance.getEntriesByType('resource').length;
					metrics.memoryUsage = performance.memory ? {
						used: performance.memory.usedJSHeapSize,
						total: performance.memory.totalJSHeapSize,
						limit: performance.memory.jsHeapSizeLimit
					} : null;

					return metrics;
				}
			`;

			const auditRequest = {
				url,
				options: {
					viewport: this.DEFAULT_VIEWPORT,
					waitUntil: 'networkidle2',
				},
				gotoOptions: {
					waitUntil: 'networkidle2',
					timeout: this.TIMEOUT,
				},
				// Execute performance measurement script
				code: performanceScript,
				setJavaScriptEnabled: true,
				setUserAgent: this.getDesktopUserAgent(),
				// Network throttling for more realistic measurements
				throttle: this.getConnectionSettings(),
			};

			const response = await axios.post(
				`${this.BROWSERLESS_URL}/function`,
				auditRequest,
				{
					timeout: this.TIMEOUT,
					headers: {
						'Content-Type': 'application/json',
					},
				},
			);

			if (response.status !== 200)
			{
				throw new Error(`Browserless returned status ${response.status}`);
			}

			const metrics = response.data;

			logger.debug('Core Web Vitals measured successfully', {
				loadTime: metrics.loadTime,
				fcp: metrics.firstContentfulPaint,
				lcp: metrics.largestContentfulPaint,
				cls: metrics.cumulativeLayoutShift,
				fid: metrics.firstInputDelay,
				speedIndex: metrics.speedIndex,
			});

			return {
				loadTime: Math.round(metrics.loadTime || 0),
				firstContentfulPaint: Math.round(metrics.firstContentfulPaint || 0),
				largestContentfulPaint: Math.round(metrics.largestContentfulPaint || 0),
				cumulativeLayoutShift: Number((metrics.cumulativeLayoutShift || 0).toFixed(3)),
				firstInputDelay: Math.round(metrics.firstInputDelay || 0),
				speedIndex: Math.round(metrics.speedIndex || 0),
			};
		}
		catch (error)
		{
			logger.error('Failed to measure Core Web Vitals:', error);
			throw error;
		}
	}

	/**
	 * Analyze resource loading and performance
	 */
	private async analyzeResources(url: string): Promise<ResourceAnalysis>
	{
		logger.debug(`Analyzing resources for: ${url}`);

		try
		{
			const resourceScript = `
				async () => {
					// Wait for page to be fully loaded
					await new Promise(resolve => {
						if (document.readyState === 'complete') {
							resolve();
						} else {
							window.addEventListener('load', resolve);
						}
					});

					const resourceEntries = performance.getEntriesByType('resource');
					const analysis = {
						totalRequests: resourceEntries.length,
						totalSize: 0,
						resourceTypes: {},
						largestResources: []
					};

					const resourceDetails = [];

					resourceEntries.forEach(entry => {
						const type = entry.initiatorType || 'other';
						const size = entry.transferSize || entry.encodedBodySize || 0;
						const loadTime = entry.responseEnd - entry.startTime;

						// Aggregate by type
						if (!analysis.resourceTypes[type]) {
							analysis.resourceTypes[type] = { count: 0, size: 0 };
						}
						analysis.resourceTypes[type].count++;
						analysis.resourceTypes[type].size += size;
						analysis.totalSize += size;

						// Collect resource details
						resourceDetails.push({
							url: entry.name,
							type: type,
							size: size,
							loadTime: loadTime
						});
					});

					// Sort by size and get largest resources
					analysis.largestResources = resourceDetails
						.sort((a, b) => b.size - a.size)
						.slice(0, 10);

					return analysis;
				}
			`;

			const response = await axios.post(
				`${this.BROWSERLESS_URL}/function`,
				{
					url,
					options: {
						viewport: this.DEFAULT_VIEWPORT,
						waitUntil: 'networkidle2',
					},
					gotoOptions: {
						waitUntil: 'networkidle2',
						timeout: this.TIMEOUT,
					},
					code: resourceScript,
					setJavaScriptEnabled: true,
					setUserAgent: this.getDesktopUserAgent(),
				},
				{
					timeout: this.TIMEOUT,
					headers: {
						'Content-Type': 'application/json',
					},
				},
			);

			if (response.status !== 200)
			{
				throw new Error(`Resource analysis failed with status ${response.status}`);
			}

			const analysis = response.data;

			logger.debug('Resource analysis completed', {
				totalRequests: analysis.totalRequests,
				totalSize: analysis.totalSize,
				resourceTypes: Object.keys(analysis.resourceTypes),
			});

			return analysis;
		}
		catch (error)
		{
			logger.error('Failed to analyze resources:', error);
			return {
				totalRequests: 0,
				totalSize: 0,
				resourceTypes: {},
				largestResources: [],
			};
		}
	}

	/**
	 * Analyze network conditions and connection quality
	 */
	private async analyzeNetwork(url: string): Promise<PerformanceAuditResult['networkAnalysis']>
	{
		logger.debug(`Analyzing network for: ${url}`);

		try
		{
			const networkScript = `
				async () => {
					const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;

					const networkInfo = {
						connectionType: 'unknown',
						rtt: 0,
						downlink: 0,
						effectiveType: 'unknown'
					};

					if (connection) {
						networkInfo.connectionType = connection.type || 'unknown';
						networkInfo.rtt = connection.rtt || 0;
						networkInfo.downlink = connection.downlink || 0;
						networkInfo.effectiveType = connection.effectiveType || 'unknown';
					}

					// Fallback: measure actual connection speed
					if (networkInfo.rtt === 0) {
						const start = performance.now();
						try {
							await fetch(location.origin + '/favicon.ico', { cache: 'no-cache' });
							networkInfo.rtt = performance.now() - start;
						} catch (e) {
							// Ignore fetch errors
						}
					}

					return networkInfo;
				}
			`;

			const response = await axios.post(
				`${this.BROWSERLESS_URL}/function`,
				{
					url,
					options: {
						viewport: this.DEFAULT_VIEWPORT,
					},
					gotoOptions: {
						waitUntil: 'domcontentloaded',
						timeout: 30000,
					},
					code: networkScript,
					setJavaScriptEnabled: true,
					setUserAgent: this.getDesktopUserAgent(),
				},
				{
					timeout: 30000,
					headers: {
						'Content-Type': 'application/json',
					},
				},
			);

			if (response.status !== 200)
			{
				throw new Error(`Network analysis failed with status ${response.status}`);
			}

			const networkInfo = response.data;

			logger.debug('Network analysis completed', networkInfo);

			return networkInfo;
		}
		catch (error)
		{
			logger.warn('Failed to analyze network conditions:', error);
			return {
				connectionType: 'unknown',
				rtt: 0,
				downlink: 0,
				effectiveType: 'unknown',
			};
		}
	}

	/**
	 * Calculate overall performance score based on Core Web Vitals
	 */
	private calculatePerformanceScore(metrics: PerformanceMetrics): number
	{
		let score = 0;
		let totalWeight = 0;

		// Largest Contentful Paint (LCP) - 25% weight
		if (metrics.largestContentfulPaint > 0)
		{
			const lcpScore = this.scoreLCP(metrics.largestContentfulPaint);
			score += lcpScore * 0.25;
			totalWeight += 0.25;
		}

		// First Input Delay (FID) - 25% weight
		if (metrics.firstInputDelay >= 0)
		{
			const fidScore = this.scoreFID(metrics.firstInputDelay);
			score += fidScore * 0.25;
			totalWeight += 0.25;
		}

		// Cumulative Layout Shift (CLS) - 25% weight
		if (metrics.cumulativeLayoutShift >= 0)
		{
			const clsScore = this.scoreCLS(metrics.cumulativeLayoutShift);
			score += clsScore * 0.25;
			totalWeight += 0.25;
		}

		// First Contentful Paint (FCP) - 15% weight
		if (metrics.firstContentfulPaint > 0)
		{
			const fcpScore = this.scoreFCP(metrics.firstContentfulPaint);
			score += fcpScore * 0.15;
			totalWeight += 0.15;
		}

		// Speed Index - 10% weight
		if (metrics.speedIndex > 0)
		{
			const siScore = this.scoreSpeedIndex(metrics.speedIndex);
			score += siScore * 0.10;
			totalWeight += 0.10;
		}

		// Normalize score based on available metrics
		return totalWeight > 0 ? Math.round((score / totalWeight) * 100) / 100 : 0;
	}

	/**
	 * Score Largest Contentful Paint (LCP)
	 * Good: ≤ 2.5s, Needs Improvement: 2.5s - 4.0s, Poor: > 4.0s
	 */
	private scoreLCP(lcp: number): number
	{
		const lcpSeconds = lcp / 1000;
		if (lcpSeconds <= 2.5) return 1.0;
		if (lcpSeconds <= 4.0) return Math.max(0.5, 1.0 - ((lcpSeconds - 2.5) / 1.5) * 0.5);
		return Math.max(0.0, 0.5 - ((lcpSeconds - 4.0) / 6.0) * 0.5);
	}

	/**
	 * Score First Input Delay (FID)
	 * Good: ≤ 100ms, Needs Improvement: 100ms - 300ms, Poor: > 300ms
	 */
	private scoreFID(fid: number): number
	{
		if (fid <= 100) return 1.0;
		if (fid <= 300) return Math.max(0.5, 1.0 - ((fid - 100) / 200) * 0.5);
		return Math.max(0.0, 0.5 - ((fid - 300) / 700) * 0.5);
	}

	/**
	 * Score Cumulative Layout Shift (CLS)
	 * Good: ≤ 0.1, Needs Improvement: 0.1 - 0.25, Poor: > 0.25
	 */
	private scoreCLS(cls: number): number
	{
		if (cls <= 0.1) return 1.0;
		if (cls <= 0.25) return Math.max(0.5, 1.0 - ((cls - 0.1) / 0.15) * 0.5);
		return Math.max(0.0, 0.5 - ((cls - 0.25) / 0.75) * 0.5);
	}

	/**
	 * Score First Contentful Paint (FCP)
	 * Good: ≤ 1.8s, Needs Improvement: 1.8s - 3.0s, Poor: > 3.0s
	 */
	private scoreFCP(fcp: number): number
	{
		const fcpSeconds = fcp / 1000;
		if (fcpSeconds <= 1.8) return 1.0;
		if (fcpSeconds <= 3.0) return Math.max(0.5, 1.0 - ((fcpSeconds - 1.8) / 1.2) * 0.5);
		return Math.max(0.0, 0.5 - ((fcpSeconds - 3.0) / 7.0) * 0.5);
	}

	/**
	 * Score Speed Index
	 * Good: ≤ 3.4s, Needs Improvement: 3.4s - 5.8s, Poor: > 5.8s
	 */
	private scoreSpeedIndex(si: number): number
	{
		const siSeconds = si / 1000;
		if (siSeconds <= 3.4) return 1.0;
		if (siSeconds <= 5.8) return Math.max(0.5, 1.0 - ((siSeconds - 3.4) / 2.4) * 0.5);
		return Math.max(0.0, 0.5 - ((siSeconds - 5.8) / 4.2) * 0.5);
	}

	/**
	 * Generate performance recommendations
	 */
	private generateRecommendations(result: PerformanceAuditResult): string[]
	{
		const recommendations: string[] = [];
		const metrics = result.metrics;
		const resources = result.resourceAnalysis;

		// Generate different types of recommendations
		recommendations.push(...this.generateCoreWebVitalsRecommendations(metrics));
		recommendations.push(...this.generateResourceOptimizationRecommendations(resources));
		recommendations.push(...this.generateGeneralPerformanceRecommendations(metrics));

		return recommendations;
	}

	/**
	 * Generate Core Web Vitals specific recommendations
	 */
	private generateCoreWebVitalsRecommendations(metrics: PerformanceMetrics): string[]
	{
		const recommendations: string[] = [];

		// LCP recommendations
		if (metrics.largestContentfulPaint > 4000)
		{
			recommendations.push('Optimize Largest Contentful Paint: Consider optimizing images, removing unused CSS, and improving server response times');
		}
		else if (metrics.largestContentfulPaint > 2500)
		{
			recommendations.push('Improve Largest Contentful Paint: Optimize critical resources and consider using a CDN');
		}

		// FID recommendations
		if (metrics.firstInputDelay > 300)
		{
			recommendations.push('Reduce First Input Delay: Minimize JavaScript execution time and consider code splitting');
		}
		else if (metrics.firstInputDelay > 100)
		{
			recommendations.push('Optimize First Input Delay: Review JavaScript performance and reduce main thread blocking');
		}

		// CLS recommendations
		if (metrics.cumulativeLayoutShift > 0.25)
		{
			recommendations.push('Fix Cumulative Layout Shift: Add size attributes to images and reserve space for dynamic content');
		}
		else if (metrics.cumulativeLayoutShift > 0.1)
		{
			recommendations.push('Improve Cumulative Layout Shift: Ensure images and ads have defined dimensions');
		}

		// FCP recommendations
		if (metrics.firstContentfulPaint > 3000)
		{
			recommendations.push('Optimize First Contentful Paint: Reduce server response time and eliminate render-blocking resources');
		}

		return recommendations;
	}

	/**
	 * Generate resource optimization recommendations
	 */
	private generateResourceOptimizationRecommendations(resources: ResourceAnalysis): string[]
	{
		const recommendations: string[] = [];

		// Overall resource recommendations
		if (resources.totalSize > 3 * 1024 * 1024) // 3MB
		{
			recommendations.push('Reduce page size: Consider compressing images and minifying CSS/JavaScript');
		}

		if (resources.totalRequests > 100)
		{
			recommendations.push('Reduce HTTP requests: Combine CSS/JS files and use image sprites where appropriate');
		}

		// Specific resource type recommendations
		const imageResources = resources.resourceTypes.img || { count: 0, size: 0 };
		if (imageResources.size > 1024 * 1024) // 1MB
		{
			recommendations.push('Optimize images: Use modern formats like WebP and implement responsive images');
		}

		const scriptResources = resources.resourceTypes.script || { count: 0, size: 0 };
		if (scriptResources.size > 512 * 1024) // 512KB
		{
			recommendations.push('Optimize JavaScript: Consider code splitting and removing unused JavaScript');
		}

		const cssResources = resources.resourceTypes.css || { count: 0, size: 0 };
		if (cssResources.size > 256 * 1024) // 256KB
		{
			recommendations.push('Optimize CSS: Remove unused CSS and consider critical CSS inlining');
		}

		return recommendations;
	}

	/**
	 * Generate general performance recommendations
	 */
	private generateGeneralPerformanceRecommendations(metrics: PerformanceMetrics): string[]
	{
		const recommendations: string[] = [];

		// General performance recommendations
		if (metrics.loadTime > 5000)
		{
			recommendations.push('Improve overall load time: Consider using a CDN and optimizing server performance');
		}

		return recommendations;
	}

	/**
	 * Get desktop user agent
	 */
	private getDesktopUserAgent(): string
	{
		return 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
	}

	/**
	 * Get connection settings for network throttling
	 */
	private getConnectionSettings(): any
	{
		return {
			offline: false,
			downloadThroughput: 1.5 * 1024 * 1024 / 8, // 1.5 Mbps
			uploadThroughput: 750 * 1024 / 8, // 750 Kbps
			latency: 40, // 40ms RTT
		};
	}

	/**
	 * Get browserless service status for performance auditing
	 */
	async getBrowserlessStatus(): Promise<{
		available: boolean;
		pressure?: any;
		stats?: any;
		version?: string;
	}>
	{
		try
		{
			const [pressureResponse, statsResponse] = await Promise.allSettled([
				axios.get(`${this.BROWSERLESS_URL}/pressure`, { timeout: 5000 }),
				axios.get(`${this.BROWSERLESS_URL}/stats`, { timeout: 5000 }),
			]);

			const result: any = {
				available: false,
			};

			if (pressureResponse.status === 'fulfilled' && pressureResponse.value.status === 200)
			{
				result.available = true;
				result.pressure = pressureResponse.value.data;
			}

			if (statsResponse.status === 'fulfilled' && statsResponse.value.status === 200)
			{
				result.stats = statsResponse.value.data;
			}

			return result;
		}
		catch (error)
		{
			logger.warn('Failed to get browserless status for performance auditing:', error);
			return { available: false };
		}
	}

	/**
	 * Validate performance auditing capability
	 */
	async validateCapability(): Promise<{
		browserlessAvailable: boolean;
		performanceAPISupported: boolean;
		error?: string;
	}>
	{
		try
		{
			const browserlessStatus = await this.getBrowserlessStatus();

			// Test if browserless supports performance measurement
			const testScript = `
				() => {
					return {
						navigationAPI: !!performance.getEntriesByType,
						paintAPI: !!performance.getEntriesByType('paint'),
						resourceAPI: !!performance.getEntriesByType('resource'),
						memoryAPI: !!performance.memory
					};
				}
			`;

			const testResponse = await axios.post(
				`${this.BROWSERLESS_URL}/function`,
				{
					url: 'https://example.com',
					code: testScript,
				},
				{ timeout: 10000 },
			);

			const apiSupport = testResponse.data;

			return {
				browserlessAvailable: browserlessStatus.available,
				performanceAPISupported: apiSupport.navigationAPI && apiSupport.paintAPI,
				...apiSupport,
			};
		}
		catch (error)
		{
			logger.error('Failed to validate performance auditing capability:', error);
			return {
				browserlessAvailable: false,
				performanceAPISupported: false,
				error: (error as Error).message,
			};
		}
	}
}

export default PerformanceAuditor;
export type { PerformanceAuditResult, ResourceAnalysis };
