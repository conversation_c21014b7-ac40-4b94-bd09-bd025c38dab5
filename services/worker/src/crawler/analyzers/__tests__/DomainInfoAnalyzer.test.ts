import {
	describe, expect, beforeEach, vi, it,
} from 'vitest';
import DomainInfoAnalyzer from '../DomainInfoAnalyzer';

// Mock the Logger
vi.mock('../../utils/Logger', () => ({
	Logger: {
		getLogger: () => ({
			info: vi.fn(),
			error: vi.fn(),
			warn: vi.fn(),
			debug: vi.fn(),
		}),
	},
}));

describe('DomainInfoAnalyzer', () =>
{
	let analyzer: DomainInfoAnalyzer;

	beforeEach(() =>
	{
		analyzer = new DomainInfoAnalyzer();
	});

	describe('analyzeDomainInfo', () =>
	{
		it('should analyze domain info for a valid domain', async () =>
		{
			const result = await analyzer.analyzeDomainInfo('google.com');

			expect(result).toHaveProperty('domain', 'google.com');
			expect(result).toHaveProperty('validation');
			expect(result).toHaveProperty('lastAnalyzed');
			expect(result.validation).toHaveProperty('isValid');
			expect(result.validation).toHaveProperty('normalizedDomain');
		});

		it('should validate domain structure correctly', async () =>
		{
			const result = await analyzer.analyzeDomainInfo('google.com');

			expect(result.validation.isValid).toBe(true);
			expect(result.validation.normalizedDomain).toBe('google.com');
			expect(result.validation.isTLD).toBe(false);
			expect(result.validation.isSubdomain).toBe(false);
		});

		it('should detect subdomains correctly', async () =>
		{
			const result = await analyzer.analyzeDomainInfo('www.google.com');

			expect(result.validation.isValid).toBe(true);
			expect(result.validation.isSubdomain).toBe(true);
		});

		it('should handle invalid domains', async () =>
		{
			const result = await analyzer.analyzeDomainInfo('invalid..domain');

			expect(result.validation.isValid).toBe(false);
			expect(result.error).toBeDefined();
		});

		it('should normalize domain names correctly', async () =>
		{
			const result1 = await analyzer.analyzeDomainInfo('https://www.example.com/path');
			const result2 = await analyzer.analyzeDomainInfo('EXAMPLE.COM');

			expect(result1.validation.normalizedDomain).toBe('example.com');
			expect(result2.validation.normalizedDomain).toBe('example.com');
		});

		it('should handle domains with WHOIS data', async () =>
		{
			const result = await analyzer.analyzeDomainInfo('google.com');

			if (result.whoisData)
			{
				expect(result.whoisData).toHaveProperty('nameServers');
				expect(result.whoisData).toHaveProperty('status');
				expect(Array.isArray(result.whoisData.nameServers)).toBe(true);
				expect(Array.isArray(result.whoisData.status)).toBe(true);
			}
		});

		it('should handle domains without WHOIS data gracefully', async () =>
		{
			const result = await analyzer.analyzeDomainInfo('nonexistent-domain-test-12345.com');

			// Should still have basic structure even if WHOIS fails
			expect(result).toHaveProperty('domain');
			expect(result).toHaveProperty('validation');
			expect(result).toHaveProperty('lastAnalyzed');
		});
	});

	describe('domain validation', () =>
	{
		it('should validate simple domains', async () =>
		{
			const result = await analyzer.analyzeDomainInfo('example.com');

			expect(result.validation.isValid).toBe(true);
			expect(result.validation.isTLD).toBe(false);
			expect(result.validation.isSubdomain).toBe(false);
		});

		it('should validate subdomains', async () =>
		{
			const result = await analyzer.analyzeDomainInfo('sub.example.com');

			expect(result.validation.isValid).toBe(true);
			expect(result.validation.isSubdomain).toBe(true);
		});

		it('should reject invalid characters', async () =>
		{
			const result = await analyzer.analyzeDomainInfo('invalid_domain.com');

			expect(result.validation.isValid).toBe(false);
		});

		it('should reject domains with consecutive dots', async () =>
		{
			const result = await analyzer.analyzeDomainInfo('invalid..domain.com');

			expect(result.validation.isValid).toBe(false);
		});

		it('should reject single part domains', async () =>
		{
			const result = await analyzer.analyzeDomainInfo('localhost');

			expect(result.validation.isValid).toBe(false);
		});
	});

	describe('WHOIS data structure', () =>
	{
		it('should have correct WHOIS data structure when available', async () =>
		{
			const result = await analyzer.analyzeDomainInfo('google.com');

			if (result.whoisData)
			{
				// Check required arrays
				expect(Array.isArray(result.whoisData.nameServers)).toBe(true);
				expect(Array.isArray(result.whoisData.status)).toBe(true);

				// Check optional fields are correct types when present
				if (result.whoisData.registrar)
				{
					expect(typeof result.whoisData.registrar).toBe('string');
				}

				if (result.whoisData.domainAge)
				{
					expect(typeof result.whoisData.domainAge).toBe('number');
				}

				if (result.whoisData.daysUntilExpiry)
				{
					expect(typeof result.whoisData.daysUntilExpiry).toBe('number');
				}
			}
		});
	});
});
