/**
 * Homepage Analyzer Tests
 *
 * Tests for homepage content analysis functionality extracted from crawler service.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { HomepageAnalyzer } from '../HomepageAnalyzer';
import type { HomepageAnalysisResultType } from '../HomepageAnalyzer';

// Mock dependencies
const mockAxios = {
	get: vi.fn(),
};

const mockCheerio = {
	load: vi.fn(),
};

vi.mock('axios', () => ({ default: mockAxios }));
vi.mock('cheerio', () => mockCheerio);

describe('HomepageAnalyzer', () =>
{
	let analyzer: HomepageAnalyzer;
	let mockDocument: any;

	beforeEach(() =>
	{
		analyzer = new HomepageAnalyzer();
		vi.clearAllMocks();

		// Setup mock cheerio document
		mockDocument = {
			find: vi.fn().mockReturnThis(),
			text: vi.fn().mockReturnValue(''),
			attr: vi.fn().mockReturnValue(''),
			length: 0,
			each: vi.fn(),
			map: vi.fn().mockReturnValue({ get: vi.fn().mockReturnValue([]) }),
		};

		mockCheerio.load.mockReturnValue(() => mockDocument);
	});

	afterEach(() =>
	{
		vi.restoreAllMocks();
	});

	describe('HTML Content Fetching', () =>
	{
		it('should fetch homepage HTML successfully', async () =>
		{
			const htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
	<title>Example Domain</title>
	<meta name="description" content="This is an example domain for testing purposes.">
	<meta name="keywords" content="example, test, domain">
</head>
<body>
	<h1>Welcome to Example Domain</h1>
	<p>This is a test page with some content.</p>
</body>
</html>
			`.trim();

			mockAxios.get.mockResolvedValue({
				status: 200,
				data: htmlContent,
				headers: {
					'content-type': 'text/html; charset=utf-8',
					'content-length': htmlContent.length.toString(),
				},
			});

			const result = await analyzer.fetchHomepage('https://example.com');

			expect(result.success).toBe(true);
			expect(result.html).toBe(htmlContent);
			expect(result.statusCode).toBe(200);
			expect(result.contentType).toBe('text/html; charset=utf-8');
		});

		it('should handle HTTP errors gracefully', async () =>
		{
			mockAxios.get.mockRejectedValue({
				response: { status: 404, statusText: 'Not Found' },
			});

			const result = await analyzer.fetchHomepage('https://notfound.com');

			expect(result.success).toBe(false);
			expect(result.statusCode).toBe(404);
			expect(result.error).toContain('Not Found');
		});

		it('should handle network timeouts', async () =>
		{
			mockAxios.get.mockRejectedValue(new Error('timeout'));

			const result = await analyzer.fetchHomepage('https://timeout.com');

			expect(result.success).toBe(false);
			expect(result.error).toContain('timeout');
		});

		it('should follow redirects and track them', async () =>
		{
			mockAxios.get.mockResolvedValue({
				status: 200,
				data: '<html><body>Final page</body></html>',
				request: {
					res: {
						responseUrl: 'https://example.com/final',
					},
				},
			});

			const result = await analyzer.fetchHomepage('https://example.com');

			expect(result.success).toBe(true);
			expect(result.finalUrl).toBe('https://example.com/final');
		});
	});

	describe('Meta Tags Analysis', () =>
	{
		beforeEach(() =>
		{
			mockDocument.find.mockImplementation((selector: string) =>
			{
				const mockElement = {
					attr: vi.fn(),
					text: vi.fn(),
					length: 1,
				};

				switch (selector)
				{
					case 'title':
						mockElement.text.mockReturnValue('Example Domain - Technology Solutions');
						break;
					case 'meta[name="description"]':
						mockElement.attr.mockReturnValue('Leading technology solutions provider with innovative products and services.');
						break;
					case 'meta[name="keywords"]':
						mockElement.attr.mockReturnValue('technology, solutions, innovation, software');
						break;
					case 'link[rel="canonical"]':
						mockElement.attr.mockReturnValue('https://example.com/');
						break;
					case 'meta[name="robots"]':
						mockElement.attr.mockReturnValue('index,follow');
						break;
					default:
						mockElement.length = 0;
				}

				return mockElement;
			});
		});

		it('should extract meta tags correctly', async () =>
		{
			const result = await analyzer.analyzeMetaTags('<html>...</html>');

			expect(result.title).toBe('Example Domain - Technology Solutions');
			expect(result.description).toBe('Leading technology solutions provider with innovative products and services.');
			expect(result.keywords).toEqual(['technology', 'solutions', 'innovation', 'software']);
			expect(result.canonical).toBe('https://example.com/');
			expect(result.robots).toBe('index,follow');
		});

		it('should validate meta tag quality', async () =>
		{
			const result = await analyzer.analyzeMetaTags('<html>...</html>');

			expect(result.titleLength).toBe(42);
			expect(result.descriptionLength).toBe(85);
			expect(result.hasTitle).toBe(true);
			expect(result.hasDescription).toBe(true);
			expect(result.titleOptimal).toBe(true); // Between 30-60 characters
			expect(result.descriptionOptimal).toBe(true); // Between 120-160 characters would be false
		});

		it('should detect missing meta tags', async () =>
		{
			mockDocument.find.mockImplementation(() => ({
				attr: vi.fn().mockReturnValue(''),
				text: vi.fn().mockReturnValue(''),
				length: 0,
			}));

			const result = await analyzer.analyzeMetaTags('<html>...</html>');

			expect(result.hasTitle).toBe(false);
			expect(result.hasDescription).toBe(false);
			expect(result.recommendations).toContain('Add a title tag');
			expect(result.recommendations).toContain('Add a meta description');
		});

		it('should detect duplicate meta tags', async () =>
		{
			mockDocument.find.mockImplementation((selector: string) =>
			{
				if (selector === 'title')
				{
					return { length: 2, text: vi.fn().mockReturnValue('Duplicate Title') };
				}
				return { length: 0, attr: vi.fn(), text: vi.fn() };
			});

			const result = await analyzer.analyzeMetaTags('<html>...</html>');

			expect(result.hasDuplicateTitle).toBe(true);
			expect(result.recommendations).toContain('Remove duplicate title tags');
		});
	});

	describe('Content Structure Analysis', () =>
	{
		beforeEach(() =>
		{
			mockDocument.find.mockImplementation((selector: string) =>
			{
				const mockElements = {
					length: 0,
					each: vi.fn(),
					text: vi.fn().mockReturnValue(''),
					attr: vi.fn().mockReturnValue(''),
				};

				switch (selector)
				{
					case 'h1':
						mockElements.length = 1;
						mockElements.text.mockReturnValue('Main Heading');
						break;
					case 'h2':
						mockElements.length = 3;
						break;
					case 'h3':
						mockElements.length = 5;
						break;
					case 'p':
						mockElements.length = 8;
						mockElements.text.mockReturnValue('This is a paragraph with some content for testing word count.');
						break;
					case 'img':
						mockElements.length = 6;
						mockElements.each.mockImplementation((callback: Function) =>
						{
							// Simulate 4 images with alt text, 2 without
							for (let i = 0; i < 6; i++)
							{
								const hasAlt = i < 4;
								callback.call({ getAttribute: () => hasAlt ? 'Alt text' : null });
							}
						});
						break;
					case 'a':
						mockElements.length = 12;
						mockElements.each.mockImplementation((callback: Function) =>
						{
							// Simulate mix of internal and external links
							for (let i = 0; i < 12; i++)
							{
								const isExternal = i < 4;
								const href = isExternal ? 'https://external.com' : '/internal-page';
								callback.call({ getAttribute: () => href });
							}
						});
						break;
				}

				return mockElements;
			});

			// Mock text content for word count
			mockDocument.text.mockReturnValue('This is sample content with multiple words for testing the word count functionality. '.repeat(50));
		});

		it('should analyze heading structure correctly', async () =>
		{
			const result = await analyzer.analyzeContentStructure('<html>...</html>');

			expect(result.headingStructure.h1Count).toBe(1);
			expect(result.headingStructure.h2Count).toBe(3);
			expect(result.headingStructure.h3Count).toBe(5);
			expect(result.headingStructure.hasProperHierarchy).toBe(true);
		});

		it('should count words accurately', async () =>
		{
			const result = await analyzer.analyzeContentStructure('<html>...</html>');

			expect(result.wordCount).toBeGreaterThan(500); // 50 repetitions of ~15 words
		});

		it('should analyze image optimization', async () =>
		{
			const result = await analyzer.analyzeContentStructure('<html>...</html>');

			expect(result.imageCount).toBe(6);
			expect(result.imagesWithAlt).toBe(4);
			expect(result.imageAltTextRatio).toBe(4 / 6);
		});

		it('should analyze link structure', async () =>
		{
			const result = await analyzer.analyzeContentStructure('<html>...</html>');

			expect(result.totalLinks).toBe(12);
			expect(result.internalLinks).toBe(8);
			expect(result.externalLinks).toBe(4);
		});

		it('should detect content quality issues', async () =>
		{
			// Mock thin content
			mockDocument.text.mockReturnValue('Short content');
			mockDocument.find.mockImplementation((selector: string) =>
			{
				if (selector === 'p')
				{
					return { length: 1, text: vi.fn().mockReturnValue('Short') };
				}
				return { length: 0, each: vi.fn(), text: vi.fn(), attr: vi.fn() };
			});

			const result = await analyzer.analyzeContentStructure('<html>...</html>');

			expect(result.wordCount).toBeLessThan(100);
			expect(result.recommendations).toContain('Increase content length');
		});
	});

	describe('Technology Detection', () =>
	{
		it('should detect JavaScript frameworks', async () =>
		{
			const htmlWithReact = `
<html>
<head>
	<script src="https://unpkg.com/react@17/umd/react.production.min.js"></script>
</head>
<body>
	<div id="react-root"></div>
</body>
</html>
			`;

			mockDocument.find.mockImplementation((selector: string) =>
			{
				if (selector.includes('script'))
				{
					return {
						length: 1,
						each: vi.fn((callback: Function) =>
						{
							callback.call({ getAttribute: () => 'https://unpkg.com/react@17/umd/react.production.min.js' });
						}),
					};
				}
				return { length: 0, each: vi.fn() };
			});

			const result = await analyzer.detectTechnologies(htmlWithReact);

			expect(result.frameworks).toContain('React');
		});

		it('should detect CSS frameworks', async () =>
		{
			mockDocument.find.mockImplementation((selector: string) =>
			{
				if (selector.includes('link'))
				{
					return {
						length: 1,
						each: vi.fn((callback: Function) =>
						{
							callback.call({ getAttribute: () => 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' });
						}),
					};
				}
				return { length: 0, each: vi.fn() };
			});

			const result = await analyzer.detectTechnologies('<html>...</html>');

			expect(result.cssFrameworks).toContain('Bootstrap');
		});

		it('should detect analytics tools', async () =>
		{
			const htmlWithGA = `
<script>
	gtag('config', 'GA_MEASUREMENT_ID');
</script>
			`;

			const result = await analyzer.detectTechnologies(htmlWithGA);

			expect(result.analytics).toContain('Google Analytics');
		});

		it('should detect content management systems', async () =>
		{
			const htmlWithWP = `
<meta name="generator" content="WordPress 5.8" />
			`;

			mockDocument.find.mockImplementation((selector: string) =>
			{
				if (selector.includes('meta[name="generator"]'))
				{
					return {
						attr: vi.fn().mockReturnValue('WordPress 5.8'),
						length: 1,
					};
				}
				return { length: 0, attr: vi.fn() };
			});

			const result = await analyzer.detectTechnologies(htmlWithWP);

			expect(result.cms).toBe('WordPress');
			expect(result.cmsVersion).toBe('5.8');
		});
	});

	describe('SEO Analysis', () =>
	{
		it('should calculate SEO score based on multiple factors', async () =>
		{
			// Mock well-optimized page
			mockDocument.find.mockImplementation((selector: string) =>
			{
				const mockElement = {
					attr: vi.fn(),
					text: vi.fn(),
					length: 1,
				};

				switch (selector)
				{
					case 'title':
						mockElement.text.mockReturnValue('Optimal Title Length for SEO Testing');
						break;
					case 'meta[name="description"]':
						mockElement.attr.mockReturnValue('This is an optimal meta description that provides a good summary of the page content and is within the recommended length range.');
						break;
					case 'h1':
						mockElement.length = 1;
						break;
					case 'img':
						mockElement.length = 5;
						mockElement.each = vi.fn((callback: Function) =>
						{
							// All images have alt text
							for (let i = 0; i < 5; i++)
							{
								callback.call({ getAttribute: () => 'Descriptive alt text' });
							}
						});
						break;
				}

				return mockElement;
			});

			mockDocument.text.mockReturnValue('This is substantial content with many words that provides value to users and search engines. '.repeat(30));

			const result = await analyzer.calculateSEOScore('<html>...</html>');

			expect(result.overallScore).toBeGreaterThan(80);
			expect(result.factors.hasTitle).toBe(true);
			expect(result.factors.hasDescription).toBe(true);
			expect(result.factors.hasH1).toBe(true);
			expect(result.factors.sufficientContent).toBe(true);
			expect(result.factors.imagesOptimized).toBe(true);
		});

		it('should penalize poor SEO practices', async () =>
		{
			// Mock poorly optimized page
			mockDocument.find.mockImplementation(() => ({
				attr: vi.fn().mockReturnValue(''),
				text: vi.fn().mockReturnValue(''),
				length: 0,
				each: vi.fn(),
			}));

			mockDocument.text.mockReturnValue('Short content');

			const result = await analyzer.calculateSEOScore('<html>...</html>');

			expect(result.overallScore).toBeLessThan(40);
			expect(result.factors.hasTitle).toBe(false);
			expect(result.factors.hasDescription).toBe(false);
			expect(result.factors.hasH1).toBe(false);
			expect(result.factors.sufficientContent).toBe(false);
		});
	});

	describe('Comprehensive Homepage Analysis', () =>
	{
		beforeEach(() =>
		{
			const htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
	<title>Example Domain - Technology Solutions</title>
	<meta name="description" content="Leading technology solutions provider with innovative products and services for modern businesses.">
	<meta name="keywords" content="technology, solutions, innovation">
	<link rel="canonical" href="https://example.com/">
</head>
<body>
	<h1>Welcome to Example Domain</h1>
	<h2>Our Services</h2>
	<p>We provide comprehensive technology solutions...</p>
	<img src="hero.jpg" alt="Technology solutions">
	<a href="/about">About Us</a>
	<a href="https://partner.com">Partner Site</a>
</body>
</html>
			`;

			mockAxios.get.mockResolvedValue({
				status: 200,
				data: htmlContent,
				headers: { 'content-type': 'text/html' },
			});

			// Setup comprehensive mocks
			mockDocument.find.mockImplementation((selector: string) =>
			{
				const mockElement = {
					attr: vi.fn(),
					text: vi.fn(),
					length: 1,
					each: vi.fn(),
				};

				switch (selector)
				{
					case 'title':
						mockElement.text.mockReturnValue('Example Domain - Technology Solutions');
						break;
					case 'meta[name="description"]':
						mockElement.attr.mockReturnValue('Leading technology solutions provider with innovative products and services for modern businesses.');
						break;
					case 'h1':
						mockElement.length = 1;
						break;
					case 'h2':
						mockElement.length = 1;
						break;
					case 'p':
						mockElement.length = 1;
						break;
					case 'img':
						mockElement.length = 1;
						mockElement.each.mockImplementation((callback: Function) =>
						{
							callback.call({ getAttribute: () => 'Technology solutions' });
						});
						break;
					case 'a':
						mockElement.length = 2;
						mockElement.each.mockImplementation((callback: Function) =>
						{
							callback.call({ getAttribute: () => '/about' });
							callback.call({ getAttribute: () => 'https://partner.com' });
						});
						break;
				}

				return mockElement;
			});

			mockDocument.text.mockReturnValue('Welcome to Example Domain Our Services We provide comprehensive technology solutions for modern businesses with innovative approaches.');
		});

		it('should perform complete homepage analysis', async () =>
		{
			const result: HomepageAnalysisResultType = await analyzer.analyzeDomain('example.com');

			expect(result.domain).toBe('example.com');
			expect(result.success).toBe(true);
			expect(result.fetchResult).toBeDefined();
			expect(result.metaTags).toBeDefined();
			expect(result.contentStructure).toBeDefined();
			expect(result.technologies).toBeDefined();
			expect(result.seoAnalysis).toBeDefined();
			expect(result.overallScore).toBeGreaterThan(0);
			expect(result.overallScore).toBeLessThanOrEqual(100);
		});

		it('should provide actionable recommendations', async () =>
		{
			const result = await analyzer.analyzeDomain('example.com');

			expect(Array.isArray(result.recommendations)).toBe(true);
			// Well-optimized page should have fewer recommendations
		});

		it('should measure analysis performance', async () =>
		{
			const result = await analyzer.analyzeDomain('example.com');

			expect(result.analysisTime).toBeGreaterThan(0);
			expect(result.analysisTime).toBeLessThan(5000); // Should complete within 5 seconds
		});
	});

	describe('Error Handling and Edge Cases', () =>
	{
		it('should handle malformed HTML gracefully', async () =>
		{
			const malformedHtml = '<html><head><title>Test</head><body><p>Unclosed paragraph</body>';

			mockAxios.get.mockResolvedValue({
				status: 200,
				data: malformedHtml,
			});

			const result = await analyzer.analyzeDomain('malformed.com');

			expect(result.success).toBe(true); // Should still analyze what it can
			expect(result.contentStructure).toBeDefined();
		});

		it('should handle empty HTML documents', async () =>
		{
			mockAxios.get.mockResolvedValue({
				status: 200,
				data: '',
			});

			const result = await analyzer.analyzeDomain('empty.com');

			expect(result.success).toBe(true);
			expect(result.contentStructure.wordCount).toBe(0);
			expect(result.recommendations.length).toBeGreaterThan(0);
		});

		it('should handle non-HTML content types', async () =>
		{
			mockAxios.get.mockResolvedValue({
				status: 200,
				data: '{"message": "This is JSON"}',
				headers: { 'content-type': 'application/json' },
			});

			const result = await analyzer.analyzeDomain('json.com');

			expect(result.success).toBe(false);
			expect(result.error).toContain('content type');
		});

		it('should handle very large HTML documents', async () =>
		{
			const largeHtml = `<html><body>${  'Large content '.repeat(100000)  }</body></html>`;

			mockAxios.get.mockResolvedValue({
				status: 200,
				data: largeHtml,
			});

			const result = await analyzer.analyzeDomain('large.com', { maxContentSize: 1024 * 1024 });

			expect(result.success).toBe(true);
			// Should handle large content without timing out
		});
	});

	describe('Performance Optimization', () =>
	{
		it('should respect timeout configurations', async () =>
		{
			mockAxios.get.mockImplementation(() =>
				new Promise(resolve =>
					setTimeout(() => resolve({
						status: 200,
						data: '<html><body>Slow response</body></html>',
					}), 2000)));

			const startTime = Date.now();
			const result = await analyzer.analyzeDomain('slow.com', { timeout: 1000 });
			const endTime = Date.now();

			expect(endTime - startTime).toBeLessThan(1500); // Should timeout before 2 seconds
			expect(result.success).toBe(false);
			expect(result.error).toContain('timeout');
		});

		it('should cache analysis results', async () =>
		{
			mockAxios.get.mockResolvedValue({
				status: 200,
				data: '<html><body>Cached content</body></html>',
			});

			// First call
			await analyzer.analyzeDomain('cached.com');
			// Second call
			await analyzer.analyzeDomain('cached.com');

			// Should only fetch once due to caching
			expect(mockAxios.get).toHaveBeenCalledTimes(1);
		});
	});
});
