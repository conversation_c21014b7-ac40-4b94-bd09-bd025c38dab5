import { describe, it, expect, beforeEach, vi } from 'vitest';
import AdvancedContentAnalyzer from '../AdvancedContentAnalyzer';

describe('AdvancedContentAnalyzer', () =>
{
	let analyzer: AdvancedContentAnalyzer;

	beforeEach(() =>
	{
		analyzer = new AdvancedContentAnalyzer();
	});

	describe('analyzeContent', () =>
	{
		it('should perform comprehensive content analysis', async () =>
		{
			const domain = 'example.com';
			const result = await analyzer.analyzeContent(domain);

			expect(result).toBeDefined();
			expect(result.domain).toBe(domain);
			expect(result.pages).toBeDefined();
			expect(result.contentQuality).toBeDefined();
			expect(result.languageDetection).toBeDefined();
			expect(result.contentMetrics).toBeDefined();
			expect(result.technicalSEO).toBeDefined();
			expect(result.lastAnalyzed).toBeDefined();
		});

		it('should analyze content quality metrics', async () =>
		{
			const domain = 'example.com';
			const result = await analyzer.analyzeContent(domain);

			expect(result.contentQuality).toBeDefined();
			expect(typeof result.contentQuality.overallScore).toBe('number');
			expect(typeof result.contentQuality.contentLength).toBe('number');
			expect(typeof result.contentQuality.readabilityScore).toBe('number');
			expect(typeof result.contentQuality.mediaRichness).toBe('number');
			expect(typeof result.contentQuality.structuralQuality).toBe('number');
			expect(typeof result.contentQuality.uniqueness).toBe('number');
			expect(typeof result.contentQuality.freshness).toBe('number');
		});

		it('should detect primary language', async () =>
		{
			const domain = 'example.com';
			const result = await analyzer.analyzeContent(domain);

			expect(result.languageDetection).toBeDefined();
			expect(typeof result.languageDetection.primary).toBe('string');
			expect(typeof result.languageDetection.confidence).toBe('number');
			expect(Array.isArray(result.languageDetection.alternatives)).toBe(true);
		});

		it('should calculate content metrics', async () =>
		{
			const domain = 'example.com';
			const result = await analyzer.analyzeContent(domain);

			expect(result.contentMetrics).toBeDefined();
			expect(typeof result.contentMetrics.totalWordCount).toBe('number');
			expect(typeof result.contentMetrics.averageReadability).toBe('number');
			expect(typeof result.contentMetrics.duplicateContentPercentage).toBe('number');
			expect(typeof result.contentMetrics.mediaToTextRatio).toBe('number');
			expect(typeof result.contentMetrics.headingStructureScore).toBe('number');
			expect(typeof result.contentMetrics.internalLinkDensity).toBe('number');
		});

		it('should analyze technical SEO aspects', async () =>
		{
			const domain = 'example.com';
			const result = await analyzer.analyzeContent(domain);

			expect(result.technicalSEO).toBeDefined();
			expect(typeof result.technicalSEO.hasStructuredData).toBe('boolean');
			expect(Array.isArray(result.technicalSEO.structuredDataTypes)).toBe(true);
			expect(typeof result.technicalSEO.hasCanonicalTags).toBe('boolean');
			expect(typeof result.technicalSEO.hasMetaDescriptions).toBe('boolean');
			expect(typeof result.technicalSEO.hasAltTags).toBe('boolean');
			expect(typeof result.technicalSEO.imageOptimization).toBe('number');
		});

		it('should analyze multiple pages', async () =>
		{
			const domain = 'example.com';
			const result = await analyzer.analyzeContent(domain);

			expect(result.pages).toBeDefined();
			expect(typeof result.pages).toBe('object');

			// Should at least have homepage
			if (result.pages.homepage)
			{
				expect(result.pages.homepage.url).toBeDefined();
				expect(result.pages.homepage.title).toBeDefined();
				expect(typeof result.pages.homepage.wordCount).toBe('number');
				expect(typeof result.pages.homepage.readabilityScore).toBe('number');
				expect(result.pages.homepage.language).toBeDefined();
			}
		});

		it('should handle domains with limited content', async () =>
		{
			const domain = 'nonexistent-domain-12345.com';
			const result = await analyzer.analyzeContent(domain);

			expect(result).toBeDefined();
			expect(result.domain).toBe(domain);

			// Should handle errors gracefully
			if (result.error)
			{
				expect(typeof result.error).toBe('string');
			}
		});

		it('should calculate readability scores', async () =>
		{
			const domain = 'example.com';
			const result = await analyzer.analyzeContent(domain);

			// If we have pages analyzed, check readability
			const pages = Object.values(result.pages);
			if (pages.length > 0)
			{
				for (const page of pages)
				{
					if (page)
					{
						expect(typeof page.readabilityScore).toBe('number');
						expect(page.readabilityScore).toBeGreaterThanOrEqual(0);
						expect(page.readabilityScore).toBeLessThanOrEqual(100);
					}
				}
			}
		});
	});
});
