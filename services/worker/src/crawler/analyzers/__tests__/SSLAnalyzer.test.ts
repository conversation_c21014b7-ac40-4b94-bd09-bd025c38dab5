/**
 * SSL Analyzer Tests
 *
 * Tests for SSL/TLS analysis functionality extracted from crawler service.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { SSLAnalyzer } from '../SSLAnalyzer';
import type { SSLAnalysisResultType } from '../SSLAnalyzer';

// Mock TLS and HTTPS modules
const mockTls = {
	connect: vi.fn(),
};

const mockHttps = {
	get: vi.fn(),
};

vi.mock('tls', () => mockTls);
vi.mock('https', () => mockHttps);

describe('SSLAnalyzer', () =>
{
	let analyzer: SSLAnalyzer;

	beforeEach(() =>
	{
		analyzer = new SSLAnalyzer();
		vi.clearAllMocks();
	});

	afterEach(() =>
	{
		vi.restoreAllMocks();
	});

	describe('Certificate Analysis', () =>
	{
		it('should analyze SSL certificate successfully', async () =>
		{
			const mockCertificate = {
				subject: {
					CN: 'example.com',
					O: 'Example Organization',
					C: 'US',
				},
				issuer: {
					CN: 'Let\'s Encrypt Authority X3',
					O: 'Let\'s Encrypt',
					C: 'US',
				},
				valid_from: 'Dec  1 00:00:00 2023 GMT',
				valid_to: 'Mar  1 23:59:59 2024 GMT',
				fingerprint: 'AA:BB:CC:DD:EE:FF:00:11:22:33:44:55:66:77:88:99:AA:BB:CC:DD',
				serialNumber: '03A1B2C3D4E5F6',
				subjectaltname: 'DNS:example.com, DNS:www.example.com',
			};

			const mockSocket = {
				getPeerCertificate: vi.fn().mockReturnValue(mockCertificate),
				getCipher: vi.fn().mockReturnValue({
					name: 'ECDHE-RSA-AES256-GCM-SHA384',
					version: 'TLSv1.3',
				}),
				getProtocol: vi.fn().mockReturnValue('TLSv1.3'),
				destroy: vi.fn(),
				on: vi.fn(),
			};

			mockTls.connect.mockImplementation((options, callback) =>
			{
				setTimeout(() => callback(), 10);
				return mockSocket;
			});

			const result = await analyzer.analyzeCertificate('example.com', 443);

			expect(result.success).toBe(true);
			expect(result.certificate.subject.commonName).toBe('example.com');
			expect(result.certificate.issuer.organization).toBe('Let\'s Encrypt');
			expect(result.certificate.isValid).toBe(true);
			expect(result.tlsVersion).toBe('TLSv1.3');
		});

		it('should detect expired certificates', async () =>
		{
			const expiredCertificate = {
				subject: { CN: 'expired.com' },
				issuer: { CN: 'Test CA' },
				valid_from: 'Jan  1 00:00:00 2022 GMT',
				valid_to: 'Jan  1 23:59:59 2022 GMT', // Expired
				fingerprint: 'AA:BB:CC:DD:EE:FF:00:11:22:33:44:55:66:77:88:99:AA:BB:CC:DD',
				serialNumber: '03A1B2C3D4E5F6',
			};

			const mockSocket = {
				getPeerCertificate: vi.fn().mockReturnValue(expiredCertificate),
				getCipher: vi.fn().mockReturnValue({ name: 'AES256-SHA', version: 'TLSv1.2' }),
				getProtocol: vi.fn().mockReturnValue('TLSv1.2'),
				destroy: vi.fn(),
				on: vi.fn(),
			};

			mockTls.connect.mockImplementation((options, callback) =>
			{
				setTimeout(() => callback(), 10);
				return mockSocket;
			});

			const result = await analyzer.analyzeCertificate('expired.com', 443);

			expect(result.certificate.isExpired).toBe(true);
			expect(result.certificate.isValid).toBe(false);
			expect(result.securityIssues).toContain('Certificate has expired');
		});

		it('should detect self-signed certificates', async () =>
		{
			const selfSignedCert = {
				subject: { CN: 'selfsigned.com' },
				issuer: { CN: 'selfsigned.com' }, // Same as subject
				valid_from: 'Jan  1 00:00:00 2023 GMT',
				valid_to: 'Jan  1 23:59:59 2025 GMT',
				fingerprint: 'AA:BB:CC:DD:EE:FF:00:11:22:33:44:55:66:77:88:99:AA:BB:CC:DD',
				serialNumber: '03A1B2C3D4E5F6',
			};

			const mockSocket = {
				getPeerCertificate: vi.fn().mockReturnValue(selfSignedCert),
				getCipher: vi.fn().mockReturnValue({ name: 'AES256-SHA', version: 'TLSv1.2' }),
				getProtocol: vi.fn().mockReturnValue('TLSv1.2'),
				destroy: vi.fn(),
				on: vi.fn(),
			};

			mockTls.connect.mockImplementation((options, callback) =>
			{
				setTimeout(() => callback(), 10);
				return mockSocket;
			});

			const result = await analyzer.analyzeCertificate('selfsigned.com', 443);

			expect(result.certificate.isSelfSigned).toBe(true);
			expect(result.securityIssues).toContain('Self-signed certificate');
		});

		it('should validate certificate chain', async () =>
		{
			const validCertificate = {
				subject: { CN: 'secure.com' },
				issuer: { CN: 'DigiCert SHA2 Secure Server CA', O: 'DigiCert Inc' },
				valid_from: 'Jan  1 00:00:00 2023 GMT',
				valid_to: 'Jan  1 23:59:59 2025 GMT',
				fingerprint: 'AA:BB:CC:DD:EE:FF:00:11:22:33:44:55:66:77:88:99:AA:BB:CC:DD',
				serialNumber: '03A1B2C3D4E5F6',
			};

			const mockSocket = {
				getPeerCertificate: vi.fn().mockReturnValue(validCertificate),
				getCipher: vi.fn().mockReturnValue({
					name: 'ECDHE-RSA-AES256-GCM-SHA384',
					version: 'TLSv1.3',
				}),
				getProtocol: vi.fn().mockReturnValue('TLSv1.3'),
				destroy: vi.fn(),
				on: vi.fn(),
			};

			mockTls.connect.mockImplementation((options, callback) =>
			{
				setTimeout(() => callback(), 10);
				return mockSocket;
			});

			const result = await analyzer.analyzeCertificate('secure.com', 443);

			expect(result.certificate.chainValid).toBe(true);
			expect(result.certificate.issuer.organization).toBe('DigiCert Inc');
		});
	});

	describe('Security Headers Analysis', () =>
	{
		it('should analyze security headers successfully', async () =>
		{
			const mockResponse = {
				statusCode: 200,
				headers: {
					'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
					'content-security-policy': 'default-src \'self\'; script-src \'self\' \'unsafe-inline\'',
					'x-frame-options': 'DENY',
					'x-content-type-options': 'nosniff',
					'x-xss-protection': '1; mode=block',
					'referrer-policy': 'strict-origin-when-cross-origin',
					'permissions-policy': 'geolocation=(), microphone=(), camera=()',
				},
			};

			mockHttps.get.mockImplementation((url, callback) =>
			{
				setTimeout(() => callback(mockResponse), 10);
				return { on: vi.fn(), end: vi.fn() };
			});

			const result = await analyzer.analyzeSecurityHeaders('https://secure.com');

			expect(result.success).toBe(true);
			expect(result.headers.hsts.present).toBe(true);
			expect(result.headers.hsts.maxAge).toBe(31536000);
			expect(result.headers.hsts.includeSubDomains).toBe(true);
			expect(result.headers.hsts.preload).toBe(true);
			expect(result.headers.csp.present).toBe(true);
			expect(result.headers.xFrameOptions.present).toBe(true);
			expect(result.headers.xFrameOptions.value).toBe('DENY');
			expect(result.securityScore).toBeGreaterThan(90);
		});

		it('should detect missing security headers', async () =>
		{
			const mockResponse = {
				statusCode: 200,
				headers: {
					'content-type': 'text/html',
				},
			};

			mockHttps.get.mockImplementation((url, callback) =>
			{
				setTimeout(() => callback(mockResponse), 10);
				return { on: vi.fn(), end: vi.fn() };
			});

			const result = await analyzer.analyzeSecurityHeaders('https://insecure.com');

			expect(result.success).toBe(true);
			expect(result.headers.hsts.present).toBe(false);
			expect(result.headers.csp.present).toBe(false);
			expect(result.headers.xFrameOptions.present).toBe(false);
			expect(result.securityScore).toBeLessThan(30);
			expect(result.recommendations.length).toBeGreaterThan(0);
		});

		it('should validate HSTS configuration', async () =>
		{
			const mockResponse = {
				statusCode: 200,
				headers: {
					'strict-transport-security': 'max-age=300', // Too short
				},
			};

			mockHttps.get.mockImplementation((url, callback) =>
			{
				setTimeout(() => callback(mockResponse), 10);
				return { on: vi.fn(), end: vi.fn() };
			});

			const result = await analyzer.analyzeSecurityHeaders('https://weakhsts.com');

			expect(result.headers.hsts.present).toBe(true);
			expect(result.headers.hsts.maxAge).toBe(300);
			expect(result.recommendations).toContain(
				expect.stringContaining('HSTS max-age'),
			);
		});

		it('should analyze Content Security Policy', async () =>
		{
			const mockResponse = {
				statusCode: 200,
				headers: {
					'content-security-policy': 'default-src \'unsafe-eval\' \'unsafe-inline\' *',
				},
			};

			mockHttps.get.mockImplementation((url, callback) =>
			{
				setTimeout(() => callback(mockResponse), 10);
				return { on: vi.fn(), end: vi.fn() };
			});

			const result = await analyzer.analyzeSecurityHeaders('https://weakcsp.com');

			expect(result.headers.csp.present).toBe(true);
			expect(result.headers.csp.hasUnsafeInline).toBe(true);
			expect(result.headers.csp.hasUnsafeEval).toBe(true);
			expect(result.headers.csp.allowsAllSources).toBe(true);
			expect(result.recommendations).toContain(
				expect.stringContaining('Content Security Policy'),
			);
		});
	});

	describe('TLS Configuration Analysis', () =>
	{
		it('should detect strong TLS configuration', async () =>
		{
			const mockSocket = {
				getPeerCertificate: vi.fn().mockReturnValue({
					subject: { CN: 'strong.com' },
					issuer: { CN: 'Strong CA' },
					valid_from: 'Jan  1 00:00:00 2023 GMT',
					valid_to: 'Jan  1 23:59:59 2025 GMT',
				}),
				getCipher: vi.fn().mockReturnValue({
					name: 'ECDHE-RSA-AES256-GCM-SHA384',
					version: 'TLSv1.3',
				}),
				getProtocol: vi.fn().mockReturnValue('TLSv1.3'),
				destroy: vi.fn(),
				on: vi.fn(),
			};

			mockTls.connect.mockImplementation((options, callback) =>
			{
				setTimeout(() => callback(), 10);
				return mockSocket;
			});

			const result = await analyzer.analyzeTLSConfiguration('strong.com', 443);

			expect(result.tlsVersion).toBe('TLSv1.3');
			expect(result.cipherSuite.name).toBe('ECDHE-RSA-AES256-GCM-SHA384');
			expect(result.cipherSuite.strength).toBe('strong');
			expect(result.supportsForwardSecrecy).toBe(true);
		});

		it('should detect weak TLS configuration', async () =>
		{
			const mockSocket = {
				getPeerCertificate: vi.fn().mockReturnValue({
					subject: { CN: 'weak.com' },
					issuer: { CN: 'Weak CA' },
					valid_from: 'Jan  1 00:00:00 2023 GMT',
					valid_to: 'Jan  1 23:59:59 2025 GMT',
				}),
				getCipher: vi.fn().mockReturnValue({
					name: 'RC4-MD5',
					version: 'TLSv1.0',
				}),
				getProtocol: vi.fn().mockReturnValue('TLSv1.0'),
				destroy: vi.fn(),
				on: vi.fn(),
			};

			mockTls.connect.mockImplementation((options, callback) =>
			{
				setTimeout(() => callback(), 10);
				return mockSocket;
			});

			const result = await analyzer.analyzeTLSConfiguration('weak.com', 443);

			expect(result.tlsVersion).toBe('TLSv1.0');
			expect(result.cipherSuite.strength).toBe('weak');
			expect(result.vulnerabilities).toContain('Weak TLS version');
			expect(result.vulnerabilities).toContain('Weak cipher suite');
		});

		it('should test for common vulnerabilities', async () =>
		{
			const mockSocket = {
				getPeerCertificate: vi.fn().mockReturnValue({
					subject: { CN: 'vulnerable.com' },
					issuer: { CN: 'Test CA' },
					valid_from: 'Jan  1 00:00:00 2023 GMT',
					valid_to: 'Jan  1 23:59:59 2025 GMT',
				}),
				getCipher: vi.fn().mockReturnValue({
					name: 'ECDHE-RSA-AES128-CBC-SHA',
					version: 'TLSv1.2',
				}),
				getProtocol: vi.fn().mockReturnValue('TLSv1.2'),
				destroy: vi.fn(),
				on: vi.fn(),
			};

			mockTls.connect.mockImplementation((options, callback) =>
			{
				setTimeout(() => callback(), 10);
				return mockSocket;
			});

			const result = await analyzer.analyzeTLSConfiguration('vulnerable.com', 443);

			// Test for specific vulnerabilities
			expect(result.vulnerabilityTests.heartbleed).toBeDefined();
			expect(result.vulnerabilityTests.poodle).toBeDefined();
			expect(result.vulnerabilityTests.beast).toBeDefined();
		});
	});

	describe('Comprehensive SSL Analysis', () =>
	{
		beforeEach(() =>
		{
			// Setup comprehensive mocks
			const mockCertificate = {
				subject: { CN: 'example.com', O: 'Example Corp' },
				issuer: { CN: 'DigiCert SHA2 Secure Server CA', O: 'DigiCert Inc' },
				valid_from: 'Jan  1 00:00:00 2023 GMT',
				valid_to: 'Jan  1 23:59:59 2025 GMT',
				fingerprint: 'AA:BB:CC:DD:EE:FF:00:11:22:33:44:55:66:77:88:99:AA:BB:CC:DD',
				serialNumber: '03A1B2C3D4E5F6',
				subjectaltname: 'DNS:example.com, DNS:www.example.com',
			};

			const mockSocket = {
				getPeerCertificate: vi.fn().mockReturnValue(mockCertificate),
				getCipher: vi.fn().mockReturnValue({
					name: 'ECDHE-RSA-AES256-GCM-SHA384',
					version: 'TLSv1.3',
				}),
				getProtocol: vi.fn().mockReturnValue('TLSv1.3'),
				destroy: vi.fn(),
				on: vi.fn(),
			};

			mockTls.connect.mockImplementation((options, callback) =>
			{
				setTimeout(() => callback(), 10);
				return mockSocket;
			});

			const mockResponse = {
				statusCode: 200,
				headers: {
					'strict-transport-security': 'max-age=31536000; includeSubDomains',
					'content-security-policy': 'default-src \'self\'',
					'x-frame-options': 'SAMEORIGIN',
				},
			};

			mockHttps.get.mockImplementation((url, callback) =>
			{
				setTimeout(() => callback(mockResponse), 10);
				return { on: vi.fn(), end: vi.fn() };
			});
		});

		it('should perform complete SSL analysis', async () =>
		{
			const result: SSLAnalysisResultType = await analyzer.analyzeDomain('example.com');

			expect(result.domain).toBe('example.com');
			expect(result.success).toBe(true);
			expect(result.certificate).toBeDefined();
			expect(result.securityHeaders).toBeDefined();
			expect(result.tlsConfiguration).toBeDefined();
			expect(result.overallGrade).toMatch(/^[A-F][+-]?$/);
			expect(result.overallScore).toBeGreaterThan(0);
			expect(result.overallScore).toBeLessThanOrEqual(100);
		});

		it('should calculate overall SSL grade correctly', async () =>
		{
			const result = await analyzer.analyzeDomain('example.com');

			expect(result.overallGrade).toBe('A');
			expect(result.overallScore).toBeGreaterThan(85);
		});

		it('should provide security recommendations', async () =>
		{
			const result = await analyzer.analyzeDomain('example.com');

			expect(Array.isArray(result.recommendations)).toBe(true);
			// Should have minimal recommendations for a well-configured site
		});

		it('should handle HTTPS redirect analysis', async () =>
		{
			const result = await analyzer.analyzeDomain('example.com');

			expect(result.httpsRedirect).toBeDefined();
			expect(result.httpsRedirect.redirectsToHttps).toBe(true);
		});
	});

	describe('Error Handling', () =>
	{
		it('should handle connection timeouts', async () =>
		{
			mockTls.connect.mockImplementation(() =>
			{
				const mockSocket = {
					on: vi.fn((event, callback) =>
					{
						if (event === 'timeout')
						{
							setTimeout(callback, 10);
						}
					}),
					destroy: vi.fn(),
				};
				return mockSocket;
			});

			const result = await analyzer.analyzeDomain('timeout.com');

			expect(result.success).toBe(false);
			expect(result.error).toContain('timeout');
		});

		it('should handle connection refused', async () =>
		{
			mockTls.connect.mockImplementation(() =>
			{
				const mockSocket = {
					on: vi.fn((event, callback) =>
					{
						if (event === 'error')
						{
							setTimeout(() => callback(new Error('ECONNREFUSED')), 10);
						}
					}),
					destroy: vi.fn(),
				};
				return mockSocket;
			});

			const result = await analyzer.analyzeDomain('refused.com');

			expect(result.success).toBe(false);
			expect(result.error).toContain('ECONNREFUSED');
		});

		it('should handle invalid certificates gracefully', async () =>
		{
			const mockSocket = {
				getPeerCertificate: vi.fn().mockReturnValue(null),
				getCipher: vi.fn().mockReturnValue(null),
				getProtocol: vi.fn().mockReturnValue(null),
				destroy: vi.fn(),
				on: vi.fn(),
			};

			mockTls.connect.mockImplementation((options, callback) =>
			{
				setTimeout(() => callback(), 10);
				return mockSocket;
			});

			const result = await analyzer.analyzeDomain('nocert.com');

			expect(result.success).toBe(false);
			expect(result.error).toContain('certificate');
		});

		it('should handle malformed domain names', async () =>
		{
			const result = await analyzer.analyzeDomain('invalid..domain..com');

			expect(result.success).toBe(false);
			expect(result.error).toContain('Invalid domain');
		});
	});

	describe('Performance and Caching', () =>
	{
		it('should measure SSL handshake time', async () =>
		{
			const mockSocket = {
				getPeerCertificate: vi.fn().mockReturnValue({
					subject: { CN: 'performance.com' },
					issuer: { CN: 'Test CA' },
					valid_from: 'Jan  1 00:00:00 2023 GMT',
					valid_to: 'Jan  1 23:59:59 2025 GMT',
				}),
				getCipher: vi.fn().mockReturnValue({
					name: 'ECDHE-RSA-AES256-GCM-SHA384',
					version: 'TLSv1.3',
				}),
				getProtocol: vi.fn().mockReturnValue('TLSv1.3'),
				destroy: vi.fn(),
				on: vi.fn(),
			};

			mockTls.connect.mockImplementation((options, callback) =>
			{
				setTimeout(() => callback(), 100);
				return mockSocket;
			});

			const result = await analyzer.analyzeDomain('performance.com');

			expect(result.handshakeTime).toBeGreaterThan(90);
			expect(result.handshakeTime).toBeLessThan(200);
		});

		it('should cache SSL analysis results', async () =>
		{
			const mockSocket = {
				getPeerCertificate: vi.fn().mockReturnValue({
					subject: { CN: 'cached.com' },
					issuer: { CN: 'Test CA' },
					valid_from: 'Jan  1 00:00:00 2023 GMT',
					valid_to: 'Jan  1 23:59:59 2025 GMT',
				}),
				getCipher: vi.fn().mockReturnValue({
					name: 'ECDHE-RSA-AES256-GCM-SHA384',
					version: 'TLSv1.3',
				}),
				getProtocol: vi.fn().mockReturnValue('TLSv1.3'),
				destroy: vi.fn(),
				on: vi.fn(),
			};

			mockTls.connect.mockImplementation((options, callback) =>
			{
				setTimeout(() => callback(), 10);
				return mockSocket;
			});

			// First call
			await analyzer.analyzeDomain('cached.com');
			// Second call
			await analyzer.analyzeDomain('cached.com');

			// Should only connect once due to caching
			expect(mockTls.connect).toHaveBeenCalledTimes(1);
		});
	});
});
