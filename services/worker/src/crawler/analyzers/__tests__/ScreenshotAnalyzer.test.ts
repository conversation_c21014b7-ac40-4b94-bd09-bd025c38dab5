import { describe, it, expect, beforeEach, vi } from 'vitest';
import Screenshot<PERSON>nalyzer from '../ScreenshotAnalyzer';

describe('ScreenshotAnalyzer', () =>
{
	let analyzer: ScreenshotAnalyzer;

	beforeEach(() =>
	{
		analyzer = new ScreenshotAnalyzer();
	});

	describe('captureScreenshots', () =>
	{
		it('should capture screenshots for both desktop and mobile', async () =>
		{
			const domain = 'example.com';
			const result = await analyzer.captureScreenshots(domain);

			expect(result).toBeDefined();
			expect(result.domain).toBe(domain);
			expect(result.screenshots).toBeDefined();
			expect(result.screenshots.desktop).toBeDefined();
			expect(result.screenshots.mobile).toBeDefined();
			expect(result.metadata).toBeDefined();
			expect(result.lastCaptured).toBeDefined();
			expect(typeof result.captureTime).toBe('number');
		});

		it('should handle browserless service unavailability', async () =>
		{
			const domain = 'example.com';
			const result = await analyzer.captureScreenshots(domain);

			expect(result).toBeDefined();
			expect(result.domain).toBe(domain);

			// Should handle errors gracefully
			if (result.error)
			{
				expect(typeof result.error).toBe('string');
				expect(result.screenshots.desktop.error).toBeDefined();
				expect(result.screenshots.mobile.error).toBeDefined();
			}
		});

		it('should include proper metadata', async () =>
		{
			const domain = 'example.com';
			const result = await analyzer.captureScreenshots(domain);

			expect(result.metadata).toBeDefined();
			expect(result.metadata.captureSettings).toBeDefined();
			expect(result.metadata.optimization).toBeDefined();
			expect(typeof result.metadata.captureSettings.timeout).toBe('number');
			expect(typeof result.metadata.captureSettings.quality).toBe('number');
			expect(typeof result.metadata.optimization.enabled).toBe('boolean');
		});
	});

	describe('getBrowserlessStatus', () =>
	{
		it('should return browserless service status', async () =>
		{
			const status = await analyzer.getBrowserlessStatus();

			expect(status).toBeDefined();
			expect(typeof status.available).toBe('boolean');
		});
	});

	describe('validateCapability', () =>
	{
		it('should validate screenshot capture capability', async () =>
		{
			const capability = await analyzer.validateCapability();

			expect(capability).toBeDefined();
			expect(typeof capability.browserlessAvailable).toBe('boolean');
			expect(typeof capability.imageProxyAvailable).toBe('boolean');
		});
	});

	describe('captureCustomScreenshot', () =>
	{
		it('should capture screenshot with custom settings', async () =>
		{
			const domain = 'example.com';
			const viewport = { width: 1280, height: 720 };
			const options = { quality: 80, format: 'png' as const };

			const result = await analyzer.captureCustomScreenshot(domain, viewport, options);

			expect(result).toBeDefined();
			// Should handle both success and error cases
			if (!result.error)
			{
				expect(result.url).toBeDefined();
				expect(typeof result.size).toBe('number');
			}
		});
	});

	describe('batchCaptureScreenshots', () =>
	{
		it('should capture screenshots for multiple domains', async () =>
		{
			const domains = ['example.com', 'google.com'];
			const options = { concurrency: 1, includeDesktop: true, includeMobile: false };

			const results = await analyzer.batchCaptureScreenshots(domains, options);

			expect(results).toBeDefined();
			expect(Array.isArray(results)).toBe(true);
			expect(results.length).toBe(domains.length);

			for (const result of results)
			{
				expect(result.domain).toBeDefined();
				expect(domains.includes(result.domain)).toBe(true);
			}
		});
	});
});
