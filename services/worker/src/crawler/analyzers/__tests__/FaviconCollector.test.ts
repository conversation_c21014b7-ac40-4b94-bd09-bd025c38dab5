import { describe, it, expect, beforeEach, vi } from 'vitest';
import FaviconCollector from '../FaviconCollector';

describe('FaviconCollector', () =>
{
	let collector: FaviconCollector;

	beforeEach(() =>
	{
		collector = new FaviconCollector();
	});

	describe('collectFavicon', () =>
	{
		it('should collect favicon successfully', async () =>
		{
			const domain = 'example.com';
			const result = await collector.collectFavicon(domain, false); // Disable cache for testing

			expect(result).toBeDefined();
			expect(result.domain).toBe(domain);
			expect(result.lastCollected).toBeDefined();
			expect(result.sources).toBeDefined();
			expect(typeof result.faviconFound).toBe('boolean');
		});

		it('should handle domains without favicons', async () =>
		{
			const domain = 'nonexistent-domain-12345.com';
			const result = await collector.collectFavicon(domain, false);

			expect(result).toBeDefined();
			expect(result.domain).toBe(domain);
			expect(result.faviconFound).toBe(false);
			expect(result.fallbackUsed).toBeNull();
		});

		it('should try multiple favicon sources', async () =>
		{
			const domain = 'example.com';
			const result = await collector.collectFavicon(domain, false);

			expect(result.sources).toBeDefined();
			expect(typeof result.sources.duckduckgo).toBe('boolean');
			expect(typeof result.sources.direct).toBe('boolean');
			expect(typeof result.sources.html).toBe('boolean');
		});

		it('should cache favicon results', async () =>
		{
			const domain = 'example.com';

			// First call should fetch
			const result1 = await collector.collectFavicon(domain, false);

			// Second call with cache enabled should be faster
			const startTime = Date.now();
			const result2 = await collector.collectFavicon(domain, true);
			const endTime = Date.now();

			expect(result2.domain).toBe(domain);
			// Cache hit should be very fast (less than 100ms)
			expect(endTime - startTime).toBeLessThan(100);
		});

		it('should handle network errors gracefully', async () =>
		{
			const domain = 'invalid-domain-test.com';
			const result = await collector.collectFavicon(domain, false);

			expect(result).toBeDefined();
			expect(result.domain).toBe(domain);
			expect(result.faviconFound).toBe(false);
		});
	});

	describe('getFaviconCacheStats', () =>
	{
		it('should return cache statistics', async () =>
		{
			const stats = await collector.getFaviconCacheStats();

			expect(stats).toBeDefined();
			expect(typeof stats.totalCached).toBe('number');
			expect(typeof stats.successfulCached).toBe('number');
			expect(typeof stats.failedCached).toBe('number');
		});
	});

	describe('invalidateFaviconCache', () =>
	{
		it('should invalidate cache for domain', async () =>
		{
			const domain = 'example.com';

			// This should not throw
			await expect(collector.invalidateFaviconCache(domain)).resolves.toBeUndefined();
		});
	});
});
