import { describe, it, expect, beforeEach, vi } from 'vitest';
import { Logger } from '@shared';
import { ImageProcessor } from '../ImageProcessor';

describe('ImageProcessor', () =>
{
	let processor: ImageProcessor;
	let mockLogger: Logger;

	beforeEach(() =>
	{
		mockLogger = {
			info: vi.fn(),
			error: vi.fn(),
			debug: vi.fn(),
			warn: vi.fn(),
		} as any;

		processor = new ImageProcessor(mockLogger);
	});

	describe('processScreenshot', () =>
	{
		it('should process screenshot with WebP format', async () =>
		{
			const imageUrl = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';
			const result = await processor.processScreenshot(imageUrl);

			expect(result).toBeDefined();
			expect(result.originalUrl).toBe(imageUrl);
			expect(result.format).toBe('webp');
			expect(typeof result.optimized).toBe('boolean');
		});

		it('should handle processing errors gracefully', async () =>
		{
			const invalidUrl = 'invalid-image-url';
			const result = await processor.processScreenshot(invalidUrl);

			expect(result).toBeDefined();
			expect(result.originalUrl).toBe(invalidUrl);
			expect(result.optimized).toBe(false);
			expect(result.format).toBe('unknown');
		});

		it('should use custom quality settings', async () =>
		{
			const imageUrl = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';
			const options = { quality: 95 };
			const result = await processor.processScreenshot(imageUrl, options);

			expect(result).toBeDefined();
			expect(result.originalUrl).toBe(imageUrl);
		});
	});

	describe('processFavicon', () =>
	{
		it('should process favicon with PNG and WebP formats', async () =>
		{
			const imageUrl = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';
			const results = await processor.processFavicon(imageUrl);

			expect(results).toBeDefined();
			expect(Array.isArray(results)).toBe(true);
			expect(results.length).toBeGreaterThan(0);

			// Should have PNG format
			const pngResult = results.find(r => r.format === 'png');
			expect(pngResult).toBeDefined();
			if (pngResult)
			{
				expect(pngResult.originalUrl).toBe(imageUrl);
			}
		});

		it('should handle favicon processing errors', async () =>
		{
			const invalidUrl = 'invalid-favicon-url';
			const results = await processor.processFavicon(invalidUrl);

			expect(results).toBeDefined();
			expect(Array.isArray(results)).toBe(true);
			expect(results.length).toBe(1);
			expect(results[0].optimized).toBe(false);
		});

		it('should skip WebP fallback when disabled', async () =>
		{
			const imageUrl = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';
			const options = { fallback: false };
			const results = await processor.processFavicon(imageUrl, options);

			expect(results).toBeDefined();
			expect(Array.isArray(results)).toBe(true);
			// Should only have one result when fallback is disabled
			expect(results.length).toBeLessThanOrEqual(2);
		});
	});

	describe('processImage', () =>
	{
		it('should process different image types correctly', async () =>
		{
			const imageUrl = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';

			// Test screenshot processing
			const screenshotResult = await processor.processImage(imageUrl, 'screenshot');
			expect(screenshotResult).toBeDefined();
			expect(!Array.isArray(screenshotResult)).toBe(true);

			// Test favicon processing
			const faviconResult = await processor.processImage(imageUrl, 'favicon');
			expect(faviconResult).toBeDefined();
			expect(Array.isArray(faviconResult)).toBe(true);

			// Test content processing
			const contentResult = await processor.processImage(imageUrl, 'content');
			expect(contentResult).toBeDefined();
			expect(!Array.isArray(contentResult)).toBe(true);
		});

		it('should throw error for unsupported image type', async () =>
		{
			const imageUrl = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';

			await expect(processor.processImage(imageUrl, 'unsupported' as any))
				.rejects.toThrow('Unsupported image type');
		});
	});

	describe('validateImageUrl', () =>
	{
		it('should validate supported image URLs', async () =>
		{
			// This would require mocking HTTP requests in a real test
			const validUrl = 'https://example.com/image.jpg';
			const isValid = await processor.validateImageUrl(validUrl);

			expect(typeof isValid).toBe('boolean');
		});

		it('should reject invalid URLs', async () =>
		{
			const invalidUrl = 'invalid-url';
			const isValid = await processor.validateImageUrl(invalidUrl);

			expect(isValid).toBe(false);
		});
	});

	describe('batchProcessImages', () =>
	{
		it('should process multiple images in batch', async () =>
		{
			const images = [
				{ url: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==', type: 'screenshot' as const },
				{ url: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==', type: 'favicon' as const },
			];

			const results = await processor.batchProcessImages(images);

			expect(results).toBeDefined();
			expect(Array.isArray(results)).toBe(true);
			expect(results.length).toBe(images.length);
		});

		it('should handle batch processing errors', async () =>
		{
			const images = [
				{ url: 'invalid-url-1', type: 'screenshot' as const },
				{ url: 'invalid-url-2', type: 'favicon' as const },
			];

			const results = await processor.batchProcessImages(images);

			expect(results).toBeDefined();
			expect(Array.isArray(results)).toBe(true);
			expect(results.length).toBe(images.length);

			// All results should indicate processing failure
			for (const result of results)
			{
				if (!Array.isArray(result))
				{
					expect(result.optimized).toBe(false);
				}
			}
		});
	});
});
