/**
 * DNS Analyzer Tests
 *
 * Tests for DNS analysis functionality extracted from crawler service.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { DNSAnalyzer } from '../DNSAnalyzer';
import type { DNSAnalysisResultType } from '../DNSAnalyzer';

// Mock DNS resolution
const mockDns = {
	resolve4: vi.fn(),
	resolve6: vi.fn(),
	resolveMx: vi.fn(),
	resolveTxt: vi.fn(),
	resolveNs: vi.fn(),
	resolveCname: vi.fn(),
	resolveSoa: vi.fn(),
};

vi.mock('dns', () => ({
	promises: mockDns,
}));

describe('DNSAnalyzer', () =>
{
	let analyzer: DNSAnalyzer;

	beforeEach(() =>
	{
		analyzer = new DNSAnalyzer();
		vi.clearAllMocks();
	});

	afterEach(() =>
	{
		vi.restoreAllMocks();
	});

	describe('A Record Analysis', () =>
	{
		it('should resolve A records successfully', async () =>
		{
			mockDns.resolve4.mockResolvedValue(['***********', '192.168.1.2']);

			const result = await analyzer.analyzeARecords('example.com');

			expect(result.success).toBe(true);
			expect(result.records).toEqual(['***********', '192.168.1.2']);
			expect(result.recordCount).toBe(2);
			expect(mockDns.resolve4).toHaveBeenCalledWith('example.com');
		});

		it('should handle A record resolution failure', async () =>
		{
			mockDns.resolve4.mockRejectedValue(new Error('NXDOMAIN'));

			const result = await analyzer.analyzeARecords('nonexistent.com');

			expect(result.success).toBe(false);
			expect(result.error).toContain('NXDOMAIN');
			expect(result.records).toEqual([]);
		});

		it('should detect load balancing configuration', async () =>
		{
			mockDns.resolve4.mockResolvedValue([
				'***********',
				'192.168.1.2',
				'192.168.1.3',
				'192.168.1.4',
			]);

			const result = await analyzer.analyzeARecords('loadbalanced.com');

			expect(result.hasLoadBalancing).toBe(true);
			expect(result.recordCount).toBe(4);
		});
	});

	describe('AAAA Record Analysis', () =>
	{
		it('should resolve AAAA records successfully', async () =>
		{
			mockDns.resolve6.mockResolvedValue(['2001:db8::1', '2001:db8::2']);

			const result = await analyzer.analyzeAAAARecords('example.com');

			expect(result.success).toBe(true);
			expect(result.records).toEqual(['2001:db8::1', '2001:db8::2']);
			expect(result.supportsIPv6).toBe(true);
		});

		it('should handle missing IPv6 support', async () =>
		{
			mockDns.resolve6.mockRejectedValue(new Error('No AAAA records'));

			const result = await analyzer.analyzeAAAARecords('ipv4only.com');

			expect(result.success).toBe(false);
			expect(result.supportsIPv6).toBe(false);
			expect(result.records).toEqual([]);
		});
	});

	describe('MX Record Analysis', () =>
	{
		it('should resolve MX records successfully', async () =>
		{
			mockDns.resolveMx.mockResolvedValue([
				{ exchange: 'mail1.example.com', priority: 10 },
				{ exchange: 'mail2.example.com', priority: 20 },
			]);

			const result = await analyzer.analyzeMXRecords('example.com');

			expect(result.success).toBe(true);
			expect(result.records).toHaveLength(2);
			expect(result.records[0].exchange).toBe('mail1.example.com');
			expect(result.records[0].priority).toBe(10);
			expect(result.hasEmailService).toBe(true);
		});

		it('should detect email service providers', async () =>
		{
			mockDns.resolveMx.mockResolvedValue([
				{ exchange: 'aspmx.l.google.com', priority: 10 },
				{ exchange: 'alt1.aspmx.l.google.com', priority: 20 },
			]);

			const result = await analyzer.analyzeMXRecords('gmail-user.com');

			expect(result.emailProvider).toBe('Google Workspace');
			expect(result.hasEmailService).toBe(true);
		});

		it('should handle domains without email service', async () =>
		{
			mockDns.resolveMx.mockRejectedValue(new Error('No MX records'));

			const result = await analyzer.analyzeMXRecords('noemail.com');

			expect(result.success).toBe(false);
			expect(result.hasEmailService).toBe(false);
			expect(result.records).toEqual([]);
		});
	});

	describe('TXT Record Analysis', () =>
	{
		it('should resolve and parse TXT records', async () =>
		{
			mockDns.resolveTxt.mockResolvedValue([
				['v=spf1 include:_spf.google.com ~all'],
				['google-site-verification=abc123'],
				['v=DMARC1; p=quarantine; rua=mailto:<EMAIL>'],
			]);

			const result = await analyzer.analyzeTXTRecords('example.com');

			expect(result.success).toBe(true);
			expect(result.records).toHaveLength(3);
			expect(result.hasSPF).toBe(true);
			expect(result.hasDMARC).toBe(true);
			expect(result.hasGoogleVerification).toBe(true);
		});

		it('should detect security configurations', async () =>
		{
			mockDns.resolveTxt.mockResolvedValue([
				['v=spf1 -all'],
				['v=DMARC1; p=reject; rua=mailto:<EMAIL>'],
			]);

			const result = await analyzer.analyzeTXTRecords('secure.com');

			expect(result.spfPolicy).toBe('strict');
			expect(result.dmarcPolicy).toBe('reject');
			expect(result.securityScore).toBeGreaterThan(80);
		});
	});

	describe('NS Record Analysis', () =>
	{
		it('should resolve nameserver records', async () =>
		{
			mockDns.resolveNs.mockResolvedValue([
				'ns1.example.com',
				'ns2.example.com',
				'ns3.example.com',
			]);

			const result = await analyzer.analyzeNSRecords('example.com');

			expect(result.success).toBe(true);
			expect(result.nameservers).toHaveLength(3);
			expect(result.hasRedundancy).toBe(true);
		});

		it('should detect DNS providers', async () =>
		{
			mockDns.resolveNs.mockResolvedValue([
				'ns-1234.awsdns-12.com',
				'ns-5678.awsdns-34.net',
			]);

			const result = await analyzer.analyzeNSRecords('aws-hosted.com');

			expect(result.dnsProvider).toBe('Amazon Route 53');
			expect(result.isManaged).toBe(true);
		});
	});

	describe('SOA Record Analysis', () =>
	{
		it('should resolve SOA record successfully', async () =>
		{
			mockDns.resolveSoa.mockResolvedValue({
				nsname: 'ns1.example.com',
				hostmaster: 'admin.example.com',
				serial: **********,
				refresh: 3600,
				retry: 900,
				expire: 604800,
				minttl: 300,
			});

			const result = await analyzer.analyzeSOARecord('example.com');

			expect(result.success).toBe(true);
			expect(result.primaryNameserver).toBe('ns1.example.com');
			expect(result.serialNumber).toBe(**********);
			expect(result.refreshInterval).toBe(3600);
		});

		it('should validate SOA configuration', async () =>
		{
			mockDns.resolveSoa.mockResolvedValue({
				nsname: 'ns1.example.com',
				hostmaster: 'admin.example.com',
				serial: **********,
				refresh: 7200, // Good refresh interval
				retry: 1800,   // Good retry interval
				expire: 1209600, // Good expire time
				minttl: 300,   // Good minimum TTL
			});

			const result = await analyzer.analyzeSOARecord('wellconfigured.com');

			expect(result.configurationScore).toBeGreaterThan(80);
			expect(result.recommendations).toHaveLength(0);
		});

		it('should provide recommendations for poor SOA configuration', async () =>
		{
			mockDns.resolveSoa.mockResolvedValue({
				nsname: 'ns1.example.com',
				hostmaster: 'admin.example.com',
				serial: **********,
				refresh: 86400, // Too long
				retry: 7200,    // Too long
				expire: 604800, // Too short
				minttl: 3600,   // Too long
			});

			const result = await analyzer.analyzeSOARecord('poorconfig.com');

			expect(result.configurationScore).toBeLessThan(60);
			expect(result.recommendations.length).toBeGreaterThan(0);
		});
	});

	describe('Comprehensive DNS Analysis', () =>
	{
		beforeEach(() =>
		{
			// Setup comprehensive mock responses
			mockDns.resolve4.mockResolvedValue(['***********']);
			mockDns.resolve6.mockResolvedValue(['2001:db8::1']);
			mockDns.resolveMx.mockResolvedValue([
				{ exchange: 'mail.example.com', priority: 10 },
			]);
			mockDns.resolveTxt.mockResolvedValue([
				['v=spf1 include:_spf.google.com ~all'],
			]);
			mockDns.resolveNs.mockResolvedValue([
				'ns1.example.com',
				'ns2.example.com',
			]);
			mockDns.resolveSoa.mockResolvedValue({
				nsname: 'ns1.example.com',
				hostmaster: 'admin.example.com',
				serial: **********,
				refresh: 3600,
				retry: 900,
				expire: 604800,
				minttl: 300,
			});
		});

		it('should perform complete DNS analysis', async () =>
		{
			const result: DNSAnalysisResultType = await analyzer.analyzeDomain('example.com');

			expect(result.domain).toBe('example.com');
			expect(result.success).toBe(true);
			expect(result.aRecords).toBeDefined();
			expect(result.aaaaRecords).toBeDefined();
			expect(result.mxRecords).toBeDefined();
			expect(result.txtRecords).toBeDefined();
			expect(result.nsRecords).toBeDefined();
			expect(result.soaRecord).toBeDefined();
			expect(result.overallScore).toBeGreaterThan(0);
			expect(result.overallScore).toBeLessThanOrEqual(100);
		});

		it('should calculate overall DNS health score', async () =>
		{
			const result = await analyzer.analyzeDomain('example.com');

			expect(result.overallScore).toBeGreaterThan(70);
			expect(result.healthStatus).toBe('healthy');
		});

		it('should detect CDN usage', async () =>
		{
			mockDns.resolve4.mockResolvedValue(['**********']); // Cloudflare IP

			const result = await analyzer.analyzeDomain('cdn-enabled.com');

			expect(result.cdnDetection.usesCDN).toBe(true);
			expect(result.cdnDetection.provider).toBe('Cloudflare');
		});

		it('should provide security recommendations', async () =>
		{
			mockDns.resolveTxt.mockResolvedValue([]); // No security records

			const result = await analyzer.analyzeDomain('insecure.com');

			expect(result.securityRecommendations.length).toBeGreaterThan(0);
			expect(result.securityRecommendations).toContain(
				expect.stringContaining('SPF'),
			);
		});

		it('should handle timeout errors gracefully', async () =>
		{
			mockDns.resolve4.mockRejectedValue(new Error('TIMEOUT'));

			const result = await analyzer.analyzeDomain('timeout.com', { timeout: 1000 });

			expect(result.success).toBe(false);
			expect(result.error).toContain('TIMEOUT');
			expect(result.overallScore).toBe(0);
		});

		it('should respect analysis options', async () =>
		{
			const options = {
				timeout: 5000,
				includeIPv6: false,
				includeSecurity: true,
				includeCDNDetection: true,
			};

			await analyzer.analyzeDomain('example.com', options);

			expect(mockDns.resolve4).toHaveBeenCalled();
			expect(mockDns.resolve6).not.toHaveBeenCalled(); // IPv6 disabled
			expect(mockDns.resolveTxt).toHaveBeenCalled(); // Security enabled
		});
	});

	describe('Performance and Caching', () =>
	{
		it('should cache DNS results to avoid duplicate queries', async () =>
		{
			mockDns.resolve4.mockResolvedValue(['***********']);

			// First call
			await analyzer.analyzeDomain('cached.com');
			// Second call
			await analyzer.analyzeDomain('cached.com');

			// Should only call DNS once due to caching
			expect(mockDns.resolve4).toHaveBeenCalledTimes(1);
		});

		it('should respect cache TTL', async () =>
		{
			mockDns.resolve4.mockResolvedValue(['***********']);

			const analyzerWithShortCache = new DNSAnalyzer({ cacheTTL: 100 });

			await analyzerWithShortCache.analyzeDomain('shortcache.com');

			// Wait for cache to expire
			await new Promise(resolve => setTimeout(resolve, 150));

			await analyzerWithShortCache.analyzeDomain('shortcache.com');

			expect(mockDns.resolve4).toHaveBeenCalledTimes(2);
		});

		it('should measure DNS response times', async () =>
		{
			mockDns.resolve4.mockImplementation(() =>
				new Promise(resolve => setTimeout(() => resolve(['***********']), 100)));

			const result = await analyzer.analyzeDomain('slowdns.com');

			expect(result.responseTime).toBeGreaterThan(90);
			expect(result.responseTime).toBeLessThan(200);
		});
	});

	describe('Error Handling and Edge Cases', () =>
	{
		it('should handle malformed domain names', async () =>
		{
			const result = await analyzer.analyzeDomain('invalid..domain..com');

			expect(result.success).toBe(false);
			expect(result.error).toContain('Invalid domain');
		});

		it('should handle network connectivity issues', async () =>
		{
			mockDns.resolve4.mockRejectedValue(new Error('ENOTFOUND'));

			const result = await analyzer.analyzeDomain('networkissue.com');

			expect(result.success).toBe(false);
			expect(result.error).toContain('ENOTFOUND');
			expect(result.healthStatus).toBe('unhealthy');
		});

		it('should handle partial DNS failures gracefully', async () =>
		{
			mockDns.resolve4.mockResolvedValue(['***********']);
			mockDns.resolve6.mockRejectedValue(new Error('No AAAA records'));
			mockDns.resolveMx.mockResolvedValue([]);

			const result = await analyzer.analyzeDomain('partial.com');

			expect(result.success).toBe(true); // Overall success despite partial failures
			expect(result.aRecords.success).toBe(true);
			expect(result.aaaaRecords.success).toBe(false);
			expect(result.overallScore).toBeGreaterThan(0);
		});
	});
});
