import { describe, it, expect, beforeEach, vi } from 'vitest';
import PerformanceAuditor from '../PerformanceAuditor';

describe('PerformanceAuditor', () =>
{
	let auditor: PerformanceAuditor;

	beforeEach(() =>
	{
		auditor = new PerformanceAuditor();
	});

	describe('auditPerformance', () =>
	{
		it('should perform comprehensive performance audit', async () =>
		{
			const domain = 'example.com';
			const result = await auditor.auditPerformance(domain);

			expect(result).toBeDefined();
			expect(result.domain).toBe(domain);
			expect(result.metrics).toBeDefined();
			expect(result.resourceAnalysis).toBeDefined();
			expect(result.networkAnalysis).toBeDefined();
			expect(result.auditDetails).toBeDefined();
			expect(result.recommendations).toBeDefined();
			expect(result.lastAudited).toBeDefined();
		});

		it('should include Core Web Vitals metrics', async () =>
		{
			const domain = 'example.com';
			const result = await auditor.auditPerformance(domain);

			expect(result.metrics).toBeDefined();
			expect(typeof result.metrics.loadTime).toBe('number');
			expect(typeof result.metrics.firstContentfulPaint).toBe('number');
			expect(typeof result.metrics.largestContentfulPaint).toBe('number');
			expect(typeof result.metrics.cumulativeLayoutShift).toBe('number');
			expect(typeof result.metrics.firstInputDelay).toBe('number');
			expect(typeof result.metrics.speedIndex).toBe('number');
			expect(typeof result.metrics.score).toBe('number');
		});

		it('should analyze resource loading', async () =>
		{
			const domain = 'example.com';
			const result = await auditor.auditPerformance(domain);

			expect(result.resourceAnalysis).toBeDefined();
			expect(typeof result.resourceAnalysis.totalRequests).toBe('number');
			expect(typeof result.resourceAnalysis.totalSize).toBe('number');
			expect(typeof result.resourceAnalysis.resourceTypes).toBe('object');
			expect(Array.isArray(result.resourceAnalysis.largestResources)).toBe(true);
		});

		it('should analyze network conditions', async () =>
		{
			const domain = 'example.com';
			const result = await auditor.auditPerformance(domain);

			expect(result.networkAnalysis).toBeDefined();
			expect(typeof result.networkAnalysis.connectionType).toBe('string');
			expect(typeof result.networkAnalysis.rtt).toBe('number');
			expect(typeof result.networkAnalysis.downlink).toBe('number');
			expect(typeof result.networkAnalysis.effectiveType).toBe('string');
		});

		it('should generate performance recommendations', async () =>
		{
			const domain = 'example.com';
			const result = await auditor.auditPerformance(domain);

			expect(result.recommendations).toBeDefined();
			expect(Array.isArray(result.recommendations)).toBe(true);
		});

		it('should handle browserless service unavailability', async () =>
		{
			const domain = 'nonexistent-domain.com';
			const result = await auditor.auditPerformance(domain);

			expect(result).toBeDefined();
			expect(result.domain).toBe(domain);

			// Should handle errors gracefully
			if (result.error)
			{
				expect(typeof result.error).toBe('string');
			}
		});

		it('should include audit timing information', async () =>
		{
			const domain = 'example.com';
			const result = await auditor.auditPerformance(domain);

			expect(result.auditDetails).toBeDefined();
			expect(typeof result.auditDetails.auditTime).toBe('number');
			expect(result.auditDetails.auditTime).toBeGreaterThan(0);
			expect(result.auditDetails.viewport).toBeDefined();
			expect(result.auditDetails.userAgent).toBeDefined();
		});
	});

	describe('getBrowserlessStatus', () =>
	{
		it('should return browserless service status', async () =>
		{
			const status = await auditor.getBrowserlessStatus();

			expect(status).toBeDefined();
			expect(typeof status.available).toBe('boolean');
		});
	});

	describe('validateCapability', () =>
	{
		it('should validate performance auditing capability', async () =>
		{
			const capability = await auditor.validateCapability();

			expect(capability).toBeDefined();
			expect(typeof capability.browserlessAvailable).toBe('boolean');
			expect(typeof capability.performanceAPISupported).toBe('boolean');
		});
	});
});
