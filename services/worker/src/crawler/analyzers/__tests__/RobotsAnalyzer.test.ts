/**
 * Robots Analyzer Tests
 *
 * Tests for robots.txt analysis functionality extracted from crawler service.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { RobotsAnalyzer } from '../RobotsAnalyzer';
import type { RobotsAnalysisResultType } from '../RobotsAnalyzer';

// Mock HTTP client
const mockAxios = {
	get: vi.fn(),
};

vi.mock('axios', () => ({
	default: mockAxios,
}));

describe('RobotsAnalyzer', () =>
{
	let analyzer: RobotsAnalyzer;

	beforeEach(() =>
	{
		analyzer = new RobotsAnalyzer();
		vi.clearAllMocks();
	});

	afterEach(() =>
	{
		vi.restoreAllMocks();
	});

	describe('Robots.txt Fetching', () =>
	{
		it('should fetch robots.txt successfully', async () =>
		{
			const robotsContent = `
User-agent: *
Disallow: /admin/
Disallow: /private/
Allow: /public/

Sitemap: https://example.com/sitemap.xml
			`.trim();

			mockAxios.get.mockResolvedValue({
				status: 200,
				data: robotsContent,
				headers: { 'content-type': 'text/plain' },
			});

			const result = await analyzer.fetchRobotsTxt('example.com');

			expect(result.success).toBe(true);
			expect(result.content).toBe(robotsContent);
			expect(result.statusCode).toBe(200);
			expect(mockAxios.get).toHaveBeenCalledWith('https://example.com/robots.txt', {
				timeout: 10000,
				headers: { 'User-Agent': expect.any(String) },
			});
		});

		it('should handle missing robots.txt (404)', async () =>
		{
			mockAxios.get.mockRejectedValue({
				response: { status: 404 },
			});

			const result = await analyzer.fetchRobotsTxt('norob ots.com');

			expect(result.success).toBe(false);
			expect(result.statusCode).toBe(404);
			expect(result.content).toBe('');
		});

		it('should handle server errors gracefully', async () =>
		{
			mockAxios.get.mockRejectedValue({
				response: { status: 500 },
			});

			const result = await analyzer.fetchRobotsTxt('servererror.com');

			expect(result.success).toBe(false);
			expect(result.statusCode).toBe(500);
			expect(result.error).toContain('Server error');
		});

		it('should handle network timeouts', async () =>
		{
			mockAxios.get.mockRejectedValue(new Error('timeout'));

			const result = await analyzer.fetchRobotsTxt('timeout.com');

			expect(result.success).toBe(false);
			expect(result.error).toContain('timeout');
		});
	});

	describe('Robots.txt Parsing', () =>
	{
		it('should parse basic robots.txt correctly', () =>
		{
			const robotsContent = `
User-agent: *
Disallow: /admin/
Disallow: /private/
Allow: /public/

User-agent: Googlebot
Disallow: /temp/
Allow: /

Sitemap: https://example.com/sitemap.xml
Crawl-delay: 1
			`.trim();

			const result = analyzer.parseRobotsTxt(robotsContent);

			expect(result.isValid).toBe(true);
			expect(result.userAgents).toHaveLength(2);
			expect(result.userAgents).toContain('*');
			expect(result.userAgents).toContain('Googlebot');
			expect(result.sitemaps).toContain('https://example.com/sitemap.xml');
			expect(result.crawlDelay).toBe(1);
		});

		it('should handle malformed robots.txt', () =>
		{
			const malformedContent = `
This is not a valid robots.txt file
Random text here
			`.trim();

			const result = analyzer.parseRobotsTxt(malformedContent);

			expect(result.isValid).toBe(false);
			expect(result.errors.length).toBeGreaterThan(0);
		});

		it('should parse complex user-agent rules', () =>
		{
			const complexContent = `
User-agent: *
Disallow: /admin/
Disallow: /api/
Allow: /api/public/

User-agent: Googlebot
User-agent: Bingbot
Disallow: /temp/
Allow: /

User-agent: BadBot
Disallow: /
			`.trim();

			const result = analyzer.parseRobotsTxt(complexContent);

			expect(result.rules.get('*')).toBeDefined();
			expect(result.rules.get('Googlebot')).toBeDefined();
			expect(result.rules.get('Bingbot')).toBeDefined();
			expect(result.rules.get('BadBot')).toBeDefined();

			const wildcardRules = result.rules.get('*')!;
			expect(wildcardRules.disallow).toContain('/admin/');
			expect(wildcardRules.disallow).toContain('/api/');
			expect(wildcardRules.allow).toContain('/api/public/');
		});

		it('should extract multiple sitemaps', () =>
		{
			const content = `
User-agent: *
Disallow:

Sitemap: https://example.com/sitemap.xml
Sitemap: https://example.com/news-sitemap.xml
Sitemap: https://example.com/images-sitemap.xml
			`.trim();

			const result = analyzer.parseRobotsTxt(content);

			expect(result.sitemaps).toHaveLength(3);
			expect(result.sitemaps).toContain('https://example.com/sitemap.xml');
			expect(result.sitemaps).toContain('https://example.com/news-sitemap.xml');
			expect(result.sitemaps).toContain('https://example.com/images-sitemap.xml');
		});
	});

	describe('Crawl Permission Checking', () =>
	{
		beforeEach(() =>
		{
			const robotsContent = `
User-agent: *
Disallow: /admin/
Disallow: /private/
Allow: /private/public/

User-agent: Googlebot
Disallow: /temp/
Allow: /

User-agent: BadBot
Disallow: /
			`.trim();

			mockAxios.get.mockResolvedValue({
				status: 200,
				data: robotsContent,
			});
		});

		it('should check crawl permissions correctly for wildcard user-agent', async () =>
		{
			const result = await analyzer.analyzeDomain('example.com');

			// Test various paths
			expect(analyzer.isPathAllowed('/', '*', result.parsedRules)).toBe(true);
			expect(analyzer.isPathAllowed('/admin/', '*', result.parsedRules)).toBe(false);
			expect(analyzer.isPathAllowed('/admin/test', '*', result.parsedRules)).toBe(false);
			expect(analyzer.isPathAllowed('/private/', '*', result.parsedRules)).toBe(false);
			expect(analyzer.isPathAllowed('/private/public/', '*', result.parsedRules)).toBe(true);
			expect(analyzer.isPathAllowed('/public/', '*', result.parsedRules)).toBe(true);
		});

		it('should check crawl permissions for specific user-agents', async () =>
		{
			const result = await analyzer.analyzeDomain('example.com');

			// Googlebot has different rules
			expect(analyzer.isPathAllowed('/admin/', 'Googlebot', result.parsedRules)).toBe(true);
			expect(analyzer.isPathAllowed('/temp/', 'Googlebot', result.parsedRules)).toBe(false);

			// BadBot is completely blocked
			expect(analyzer.isPathAllowed('/', 'BadBot', result.parsedRules)).toBe(false);
			expect(analyzer.isPathAllowed('/anything', 'BadBot', result.parsedRules)).toBe(false);
		});

		it('should handle case-insensitive user-agent matching', async () =>
		{
			const result = await analyzer.analyzeDomain('example.com');

			expect(analyzer.isPathAllowed('/temp/', 'googlebot', result.parsedRules)).toBe(false);
			expect(analyzer.isPathAllowed('/temp/', 'GOOGLEBOT', result.parsedRules)).toBe(false);
		});

		it('should fall back to wildcard rules when specific user-agent not found', async () =>
		{
			const result = await analyzer.analyzeDomain('example.com');

			expect(analyzer.isPathAllowed('/admin/', 'UnknownBot', result.parsedRules)).toBe(false);
			expect(analyzer.isPathAllowed('/public/', 'UnknownBot', result.parsedRules)).toBe(true);
		});
	});

	describe('Crawl Delay Analysis', () =>
	{
		it('should detect crawl delay directives', async () =>
		{
			const robotsContent = `
User-agent: *
Crawl-delay: 2

User-agent: Googlebot
Crawl-delay: 1

User-agent: SlowBot
Crawl-delay: 10
			`.trim();

			mockAxios.get.mockResolvedValue({
				status: 200,
				data: robotsContent,
			});

			const result = await analyzer.analyzeDomain('example.com');

			expect(result.crawlDelays.get('*')).toBe(2);
			expect(result.crawlDelays.get('Googlebot')).toBe(1);
			expect(result.crawlDelays.get('SlowBot')).toBe(10);
		});

		it('should provide crawl delay recommendations', async () =>
		{
			const robotsContent = `
User-agent: *
Crawl-delay: 30
			`.trim();

			mockAxios.get.mockResolvedValue({
				status: 200,
				data: robotsContent,
			});

			const result = await analyzer.analyzeDomain('slowsite.com');

			expect(result.recommendations).toContain(
				expect.stringContaining('crawl delay'),
			);
		});
	});

	describe('Sitemap Analysis', () =>
	{
		it('should validate sitemap URLs', async () =>
		{
			const robotsContent = `
User-agent: *
Disallow:

Sitemap: https://example.com/sitemap.xml
Sitemap: http://example.com/insecure-sitemap.xml
Sitemap: invalid-url
			`.trim();

			mockAxios.get.mkResolvedValue({
				status: 200,
				data: robotsContent,
			});

			const result = await analyzer.analyzeDomain('example.com');

			expect(result.sitemapAnalysis.validSitemaps).toHaveLength(2);
			expect(result.sitemapAnalysis.invalidSitemaps).toHaveLength(1);
			expect(result.sitemapAnalysis.hasHttpsSitemaps).toBe(true);
			expect(result.sitemapAnalysis.hasInsecureSitemaps).toBe(true);
		});

		it('should detect missing sitemaps', async () =>
		{
			const robotsContent = `
User-agent: *
Disallow: /admin/
			`.trim();

			mockAxios.get.mockResolvedValue({
				status: 200,
				data: robotsContent,
			});

			const result = await analyzer.analyzeDomain('nositemaps.com');

			expect(result.sitemapAnalysis.validSitemaps).toHaveLength(0);
			expect(result.recommendations).toContain(
				expect.stringContaining('sitemap'),
			);
		});
	});

	describe('Security Analysis', () =>
	{
		it('should detect overly restrictive robots.txt', async () =>
		{
			const restrictiveContent = `
User-agent: *
Disallow: /
			`.trim();

			mockAxios.get.mockResolvedValue({
				status: 200,
				data: restrictiveContent,
			});

			const result = await analyzer.analyzeDomain('blocked.com');

			expect(result.securityAnalysis.isOverlyRestrictive).toBe(true);
			expect(result.securityAnalysis.blockedPaths).toContain('/');
		});

		it('should detect sensitive path exposure', async () =>
		{
			const exposingContent = `
User-agent: *
Disallow: /admin/
Disallow: /backup/
Disallow: /config/
Disallow: /.env
			`.trim();

			mockAxios.get.mockResolvedValue({
				status: 200,
				data: exposingContent,
			});

			const result = await analyzer.analyzeDomain('exposed.com');

			expect(result.securityAnalysis.exposedSensitivePaths.length).toBeGreaterThan(0);
			expect(result.securityAnalysis.exposedSensitivePaths).toContain('/admin/');
			expect(result.securityAnalysis.exposedSensitivePaths).toContain('/.env');
		});

		it('should calculate security score', async () =>
		{
			const secureContent = `
User-agent: *
Allow: /

Sitemap: https://example.com/sitemap.xml
			`.trim();

			mockAxios.get.mockResolvedValue({
				status: 200,
				data: secureContent,
			});

			const result = await analyzer.analyzeDomain('secure.com');

			expect(result.securityAnalysis.securityScore).toBeGreaterThan(80);
		});
	});

	describe('Comprehensive Analysis', () =>
	{
		it('should perform complete robots.txt analysis', async () =>
		{
			const comprehensiveContent = `
# Robots.txt for example.com
User-agent: *
Disallow: /admin/
Disallow: /private/
Allow: /private/public/
Crawl-delay: 1

User-agent: Googlebot
Disallow: /temp/
Allow: /

Sitemap: https://example.com/sitemap.xml
Sitemap: https://example.com/news-sitemap.xml
			`.trim();

			mockAxios.get.mockResolvedValue({
				status: 200,
				data: comprehensiveContent,
			});

			const result: RobotsAnalysisResultType = await analyzer.analyzeDomain('example.com');

			expect(result.domain).toBe('example.com');
			expect(result.success).toBe(true);
			expect(result.exists).toBe(true);
			expect(result.isValid).toBe(true);
			expect(result.userAgents).toContain('*');
			expect(result.userAgents).toContain('Googlebot');
			expect(result.sitemaps).toHaveLength(2);
			expect(result.overallScore).toBeGreaterThan(70);
		});

		it('should handle domains without robots.txt', async () =>
		{
			mockAxios.get.mockRejectedValue({
				response: { status: 404 },
			});

			const result = await analyzer.analyzeDomain('norobots.com');

			expect(result.success).toBe(true); // Analysis succeeds even without robots.txt
			expect(result.exists).toBe(false);
			expect(result.allowsCrawling).toBe(true); // Default behavior
			expect(result.recommendations).toContain(
				expect.stringContaining('robots.txt'),
			);
		});

		it('should provide SEO recommendations', async () =>
		{
			const poorContent = `
User-agent: *
Disallow: /
			`.trim();

			mockAxios.get.mockResolvedValue({
				status: 200,
				data: poorContent,
			});

			const result = await analyzer.analyzeDomain('poorseo.com');

			expect(result.seoRecommendations.length).toBeGreaterThan(0);
			expect(result.seoRecommendations).toContain(
				expect.stringContaining('blocking all crawlers'),
			);
		});

		it('should measure response time', async () =>
		{
			mockAxios.get.mockImplementation(() =>
				new Promise(resolve =>
					setTimeout(() => resolve({
						status: 200,
						data: 'User-agent: *\nDisallow:',
					}), 100)));

			const result = await analyzer.analyzeDomain('slowrobots.com');

			expect(result.responseTime).toBeGreaterThan(90);
			expect(result.responseTime).toBeLessThan(200);
		});
	});

	describe('Caching and Performance', () =>
	{
		it('should cache robots.txt results', async () =>
		{
			mockAxios.get.mockResolvedValue({
				status: 200,
				data: 'User-agent: *\nDisallow:',
			});

			// First call
			await analyzer.analyzeDomain('cached.com');
			// Second call
			await analyzer.analyzeDomain('cached.com');

			// Should only fetch once due to caching
			expect(mockAxios.get).toHaveBeenCalledTimes(1);
		});

		it('should respect cache TTL', async () =>
		{
			mockAxios.get.mockResolvedValue({
				status: 200,
				data: 'User-agent: *\nDisallow:',
			});

			const analyzerWithShortCache = new RobotsAnalyzer({ cacheTTL: 100 });

			await analyzerWithShortCache.analyzeDomain('shortcache.com');

			// Wait for cache to expire
			await new Promise(resolve => setTimeout(resolve, 150));

			await analyzerWithShortCache.analyzeDomain('shortcache.com');

			expect(mockAxios.get).toHaveBeenCalledTimes(2);
		});
	});

	describe('Error Handling', () =>
	{
		it('should handle malformed URLs gracefully', async () =>
		{
			const result = await analyzer.analyzeDomain('invalid..domain..com');

			expect(result.success).toBe(false);
			expect(result.error).toContain('Invalid domain');
		});

		it('should handle network errors gracefully', async () =>
		{
			mockAxios.get.mockRejectedValue(new Error('Network error'));

			const result = await analyzer.analyzeDomain('networkerror.com');

			expect(result.success).toBe(false);
			expect(result.error).toContain('Network error');
		});

		it('should handle redirect loops', async () =>
		{
			mockAxios.get.mockRejectedValue({
				response: { status: 310 },
			});

			const result = await analyzer.analyzeDomain('redirectloop.com');

			expect(result.success).toBe(false);
			expect(result.error).toContain('redirect');
		});
	});
});
