import axios from 'axios';
import robotsParser from 'robots-parser';
import { logger } from '@shared/utils/Logger';

const robotsLogger = logger.getLogger('RobotsAnalyzer');

// Constants for crawler configuration
const CRAWL_TIMEOUT = 30000;
const USER_AGENTS =
[
	'Mozilla/5.0 (compatible; DomainRankingBot/1.0)',
	'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
];

type RobotsRuleType =
{
	userAgent: string;
	allow: string[];
	disallow: string[];
	crawlDelay?: number;
	sitemap?: string[];
};

type RobotsAnalysisResultType =
{
	domain: string;
	robotsTxtUrl: string;
	exists: boolean;
	accessible: boolean;
	content: string | null;
	rules: RobotsRuleType[];
	sitemaps: string[];
	crawlDelay: number | null;
	complianceStatus: {
		isCompliant: boolean;
		violations: string[];
		warnings: string[];
	};
	lastAnalyzed: Date;
	error?: string;
};

type ComplianceCheckType =
{
	userAgent: string;
	path: string;
	isAllowed: boolean;
	rule?: string;
};

/**
 * Robots.txt analyzer for domain crawling compliance
 */
class RobotsAnalyzer
{
	private readonly timeout: number;

	private readonly userAgents: string[];

	constructor()
	{
		this.timeout = CRAWL_TIMEOUT;
		this.userAgents = USER_AGENTS;
	}

	/**
   * Analyze robots.txt for a domain
   */
	async analyzeRobotsTxt(domain: string): Promise<RobotsAnalysisResultType>
	{
		const startTime = Date.now();
		const normalizedDomain = this.normalizeDomain(domain);
		const robotsTxtUrl = `https://${normalizedDomain}/robots.txt`;

		robotsLogger.info(`Analyzing robots.txt for domain: ${domain}`);

		const result: RobotsAnalysisResultType =
		{
			domain: normalizedDomain,
			robotsTxtUrl,
			exists: false,
			accessible: false,
			content: null,
			rules: [],
			sitemaps: [],
			crawlDelay: null,
			complianceStatus: {
				isCompliant: true,
				violations: [],
				warnings: [],
			},
			lastAnalyzed: new Date(),
		};

		try
		{
			// Fetch robots.txt content
			const robotsContent = await this.fetchRobotsTxt(robotsTxtUrl);

			if (robotsContent)
			{
				result.exists = true;
				result.accessible = true;
				result.content = robotsContent;

				// Parse robots.txt content
				const parsedRobots = await this.parseRobotsContent(robotsContent, robotsTxtUrl);
				result.rules = parsedRobots.rules;
				result.sitemaps = parsedRobots.sitemaps;
				result.crawlDelay = parsedRobots.crawlDelay;

				// Check compliance
				result.complianceStatus = await this.checkCompliance(robotsContent, normalizedDomain);
			}

			const duration = Date.now() - startTime;
			robotsLogger.info(`Robots.txt analysis completed for domain: ${domain} in ${duration}ms`);

			return result;
		}
		catch (error)
		{
			const errorMessage = error instanceof Error ? error.message : 'Unknown error';
			robotsLogger.error(`Failed to analyze robots.txt for ${domain}:`, error);

			result.error = errorMessage;
			result.complianceStatus.isCompliant = false;
			result.complianceStatus.violations.push(`Failed to fetch robots.txt: ${errorMessage}`);

			return result;
		}
	}

	/**
   * Fetch robots.txt content from URL
   */
	private async fetchRobotsTxt(url: string): Promise<string | null>
	{
		try
		{
			const response = await axios.get(url, {
				timeout: this.timeout,
				headers: {
					'User-Agent': this.userAgents[0],
					Accept: 'text/plain, */*',
				},
				validateStatus: (status: number) => status < 500, // Accept 4xx as valid responses
				maxRedirects: 3,
			});

			// robots.txt not found (404) is acceptable
			if (response.status === 404)
			{
				robotsLogger.info(`robots.txt not found for URL: ${url}`);
				return null;
			}

			// Other 4xx errors are also acceptable (means no robots.txt)
			if (response.status >= 400 && response.status < 500)
			{
				robotsLogger.info(`robots.txt returned ${response.status} for URL: ${url}`);
				return null;
			}

			// 2xx and 3xx should have content
			if (response.status >= 200 && response.status < 400)
			{
				const content = response.data;
				if (typeof content === 'string' && content.trim().length > 0)
				{
					robotsLogger.info(`Successfully fetched robots.txt from: ${url}`);
					return content;
				}
			}

			return null;
		}
		catch (error)
		{
			if (axios.isAxiosError(error))
			{
				if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED')
				{
					robotsLogger.info(`Domain not accessible for robots.txt: ${url}`);
					return null;
				}
				if (error.response?.status === 404)
				{
					robotsLogger.info(`robots.txt not found (404): ${url}`);
					return null;
				}
			}

			robotsLogger.warn(`Error fetching robots.txt from ${url}:`, error);
			throw error;
		}
	}

	/**
   * Parse robots.txt content using robots-parser
   */
	private async parseRobotsContent(content: string, url: string): Promise<{
		rules: RobotsRule[];
		sitemaps: string[];
		crawlDelay: number | null;
	}>
	{
		try
		{
			const rules: RobotsRule[] = [];
			const sitemaps: string[] = [];
			let globalCrawlDelay: number | null = null;

			// Extract sitemap URLs
			const sitemapMatches = content.match(/^sitemap:\s*(.+)$/gim);
			if (sitemapMatches)
			{
				sitemapMatches.forEach((match) =>
				{
					const sitemapUrl = match.replace(/^sitemap:\s*/i, '').trim();
					if (sitemapUrl && !sitemaps.includes(sitemapUrl))
					{
						sitemaps.push(sitemapUrl);
					}
				});
			}

			// Parse user-agent specific rules
			const lines = content.split('\n');
			let currentUserAgent = '*';
			let currentRules: RobotsRule = {
				userAgent: '*',
				allow: [],
				disallow: [],
				crawlDelay: undefined,
				sitemap: [],
			};

			for (const line of lines)
			{
				const trimmedLine = line.trim();
				if (!trimmedLine || trimmedLine.startsWith('#')) continue;

				const colonIndex = trimmedLine.indexOf(':');
				if (colonIndex === -1) continue;

				const directive = trimmedLine.substring(0, colonIndex).trim().toLowerCase();
				const value = trimmedLine.substring(colonIndex + 1).trim();

				switch (directive)
				{
					case 'user-agent':
						// Save previous rule if it exists
						if (currentRules.allow.length > 0 || currentRules.disallow.length > 0)
						{
							rules.push({ ...currentRules });
						}

						currentUserAgent = value;
						currentRules = {
							userAgent: value,
							allow: [],
							disallow: [],
							crawlDelay: undefined,
							sitemap: [],
						};
						break;

					case 'allow':
						currentRules.allow.push(value);
						break;

					case 'disallow':
						currentRules.disallow.push(value);
						break;

					case 'crawl-delay':
						const delay = parseInt(value, 10);
						if (!isNaN(delay))
						{
							currentRules.crawlDelay = delay;
							if (currentUserAgent === '*' && globalCrawlDelay === null)
							{
								globalCrawlDelay = delay;
							}
						}
						break;

					case 'sitemap':
						if (!sitemaps.includes(value))
						{
							sitemaps.push(value);
						}
						currentRules.sitemap?.push(value);
						break;
				}
			}

			// Add the last rule
			if (currentRules.allow.length > 0 || currentRules.disallow.length > 0)
			{
				rules.push(currentRules);
			}

			return {
				rules,
				sitemaps,
				crawlDelay: globalCrawlDelay,
			};
		}
		catch (error)
		{
			robotsLogger.error('Error parsing robots.txt content:', error);
			throw error;
		}
	}

	/**
   * Check compliance with robots.txt rules
   */
	private async checkCompliance(content: string, domain: string): Promise<{
		isCompliant: boolean;
		violations: string[];
		warnings: string[];
	}>
	{
		const violations: string[] = [];
		const warnings: string[] = [];

		try
		{
			// Basic syntax validation
			const lines = content.split('\n');
			let hasUserAgent = false;
			let lineNumber = 0;

			for (const line of lines)
			{
				lineNumber++;
				const trimmedLine = line.trim();

				if (!trimmedLine || trimmedLine.startsWith('#')) continue;

				const colonIndex = trimmedLine.indexOf(':');
				if (colonIndex === -1)
				{
					warnings.push(`Line ${lineNumber}: Invalid syntax, missing colon`);
					continue;
				}

				const directive = trimmedLine.substring(0, colonIndex).trim().toLowerCase();
				const value = trimmedLine.substring(colonIndex + 1).trim();

				// Check for valid directives
				const validDirectives = ['user-agent', 'allow', 'disallow', 'crawl-delay', 'sitemap'];
				if (!validDirectives.includes(directive))
				{
					warnings.push(`Line ${lineNumber}: Unknown directive '${directive}'`);
				}

				// Check for user-agent directive
				if (directive === 'user-agent')
				{
					hasUserAgent = true;
					if (!value)
					{
						violations.push(`Line ${lineNumber}: User-agent directive has no value`);
					}
				}

				// Check for empty disallow (which allows everything)
				if (directive === 'disallow' && !value)
				{
					warnings.push(`Line ${lineNumber}: Empty disallow directive allows all crawling`);
				}

				// Check for crawl-delay validity
				if (directive === 'crawl-delay')
				{
					const delay = parseInt(value, 10);
					if (isNaN(delay) || delay < 0)
					{
						violations.push(`Line ${lineNumber}: Invalid crawl-delay value '${value}'`);
					}
					else if (delay > 86400)
					{ // More than 24 hours
						warnings.push(`Line ${lineNumber}: Very high crawl-delay (${delay} seconds)`);
					}
				}

				// Check sitemap URLs
				if (directive === 'sitemap')
				{
					if (!value.startsWith('http://') && !value.startsWith('https://'))
					{
						violations.push(`Line ${lineNumber}: Sitemap URL must be absolute: '${value}'`);
					}
				}
			}

			if (!hasUserAgent && content.trim().length > 0)
			{
				violations.push('robots.txt has rules but no User-agent directive');
			}

			// Check file size (should be reasonable)
			if (content.length > 500000)
			{ // 500KB
				warnings.push('robots.txt file is very large (>500KB)');
			}

			const isCompliant = violations.length === 0;

			return {
				isCompliant,
				violations,
				warnings,
			};
		}
		catch (error)
		{
			robotsLogger.error('Error checking robots.txt compliance:', error);
			return {
				isCompliant: false,
				violations: ['Failed to validate robots.txt compliance'],
				warnings: [],
			};
		}
	}

	/**
   * Check if a specific path is allowed for crawling
   */
	async isPathAllowed(domain: string, path: string, userAgent: string = '*'): Promise<ComplianceCheck>
	{
		try
		{
			const normalizedDomain = this.normalizeDomain(domain);
			const robotsTxtUrl = `https://${normalizedDomain}/robots.txt`;

			// Fetch and parse robots.txt
			const robotsContent = await this.fetchRobotsTxt(robotsTxtUrl);

			if (!robotsContent)
			{
				// No robots.txt means everything is allowed
				return {
					userAgent,
					path,
					isAllowed: true,
					rule: 'No robots.txt found - all paths allowed',
				};
			}

			const robots = robotsParser(robotsTxtUrl, robotsContent);
			const isAllowed = robots.isAllowed(path, userAgent);

			return {
				userAgent,
				path,
				isAllowed,
				rule: isAllowed ? 'Path allowed by robots.txt' : 'Path disallowed by robots.txt',
			};
		}
		catch (error)
		{
			robotsLogger.error(`Error checking path permission for ${domain}${path}:`, error);

			// On error, assume allowed to avoid blocking legitimate crawling
			return {
				userAgent,
				path,
				isAllowed: true,
				rule: 'Error checking robots.txt - assuming allowed',
			};
		}
	}

	/**
   * Get crawl delay for a specific user agent
   */
	async getCrawlDelay(domain: string, userAgent: string = '*'): Promise<number | null>
	{
		try
		{
			const normalizedDomain = this.normalizeDomain(domain);
			const robotsTxtUrl = `https://${normalizedDomain}/robots.txt`;

			const robotsContent = await this.fetchRobotsTxt(robotsTxtUrl);
			if (!robotsContent)
			{
				return null;
			}

			const robots = robotsParser(robotsTxtUrl, robotsContent);
			return robots.getCrawlDelay(userAgent);
		}
		catch (error)
		{
			robotsLogger.error(`Error getting crawl delay for ${domain}:`, error);
			return null;
		}
	}

	/**
   * Get sitemap URLs from robots.txt
   */
	async getSitemaps(domain: string): Promise<string[]>
	{
		try
		{
			const normalizedDomain = this.normalizeDomain(domain);
			const robotsTxtUrl = `https://${normalizedDomain}/robots.txt`;

			const robotsContent = await this.fetchRobotsTxt(robotsTxtUrl);
			if (!robotsContent)
			{
				return [];
			}

			const robots = robotsParser(robotsTxtUrl, robotsContent);
			return robots.getSitemaps();
		}
		catch (error)
		{
			robotsLogger.error(`Error getting sitemaps for ${domain}:`, error);
			return [];
		}
	}

	/**
   * Normalize domain name (remove protocol, www, trailing slash)
   */
	private normalizeDomain(domain: string): string
	{
		return domain
			.replace(/^https?:\/\//, '')
			.replace(/^www\./, '')
			.replace(/\/$/, '')
			.toLowerCase();
	}
}

export default RobotsAnalyzer;
