import https from 'https';
import http from 'http';
import { URL } from 'url';
import { Logger } from '@shared';

const logger = Logger.getLogger('HomepageAnalyzer');

type HomepageAnalysisResult = {
	domain: string;
	url: string;
	accessible: boolean;
	statusCode?: number;
	responseTime: number;
	redirects: string[];
	finalUrl: string;
	contentType?: string;
	contentLength?: number;
	metaTags: {
		title?: string;
		description?: string;
		keywords?: string;
		author?: string;
		viewport?: string;
		charset?: string;
		robots?: string;
		canonical?: string;
		ogTitle?: string;
		ogDescription?: string;
		ogImage?: string;
		twitterCard?: string;
	};
	technologies: {
		server?: string;
		framework?: string;
		cms?: string;
		analytics: string[];
		libraries: string[];
	};
	performance: {
		loadTime: number;
		ttfb: number; // Time to first byte
		domContentLoaded?: number;
		resourceCount?: number;
	};
	content: {
		hasH1: boolean;
		hasH2: boolean;
		imageCount: number;
		linkCount: number;
		wordCount: number;
		language?: string;
	};
	lastAnalyzed: string;
	error?: string;
};

/**
 * Homepage Analyzer
 * Analyzes homepage content, meta tags, and basic performance
 */
class HomepageAnalyzer
{
	private readonly USER_AGENT = 'Mozilla/5.0 (compatible; DomainRankingBot/1.0)';

	private readonly TIMEOUT = 15000;

	/**
	 * Analyze homepage content and metadata
	 */
	async analyzeHomepage(domain: string): Promise<HomepageAnalysisResult>
	{
		logger.info(`Analyzing homepage for domain: ${domain}`);

		const startTime = Date.now();
		const url = `https://${domain}`;

		const result: HomepageAnalysisResult = {
			domain,
			url,
			accessible: false,
			responseTime: 0,
			redirects: [],
			finalUrl: url,
			metaTags: {},
			technologies: {
				analytics: [],
				libraries: [],
			},
			performance: {
				loadTime: 0,
				ttfb: 0,
			},
			content: {
				hasH1: false,
				hasH2: false,
				imageCount: 0,
				linkCount: 0,
				wordCount: 0,
			},
			lastAnalyzed: new Date().toISOString(),
		};

		try
		{
			// Fetch homepage content
			const response = await this.fetchHomepage(url);

			result.accessible = true;
			result.statusCode = response.statusCode;
			result.responseTime = Date.now() - startTime;
			result.redirects = response.redirects;
			result.finalUrl = response.finalUrl;
			result.contentType = response.contentType;
			result.contentLength = response.contentLength;
			result.performance.loadTime = result.responseTime;
			result.performance.ttfb = response.ttfb;

			if (response.html)
			{
				// Extract meta tags
				result.metaTags = this.extractMetaTags(response.html);

				// Detect technologies
				result.technologies = this.detectTechnologies(response.html, response.headers);

				// Analyze content
				result.content = this.analyzeContent(response.html);
			}

			logger.info(`Homepage analysis completed for domain: ${domain}`, {
				accessible: result.accessible,
				statusCode: result.statusCode,
				responseTime: result.responseTime,
			});

			return result;
		}
		catch (error)
		{
			logger.error(`Homepage analysis failed for domain: ${domain}:`, error);
			result.error = (error as Error).message;
			result.responseTime = Date.now() - startTime;
			return result;
		}
	}

	/**
	 * Fetch homepage content with redirects handling
	 */
	private async fetchHomepage(url: string, redirectCount = 0): Promise<{
		statusCode: number;
		html: string;
		headers: Record<string, string>;
		redirects: string[];
		finalUrl: string;
		contentType?: string;
		contentLength?: number;
		ttfb: number;
	}>
	{
		const startTime = Date.now();

		return new Promise((resolve, reject) =>
		{
			if (redirectCount > 5)
			{
				reject(new Error('Too many redirects'));
				return;
			}

			const parsedUrl = new URL(url);
			const isHttps = parsedUrl.protocol === 'https:';
			const client = isHttps ? https : http;

			const options = {
				hostname: parsedUrl.hostname,
				port: parsedUrl.port || (isHttps ? 443 : 80),
				path: parsedUrl.pathname + parsedUrl.search,
				method: 'GET',
				headers: {
					'User-Agent': this.USER_AGENT,
					Accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
					'Accept-Language': 'en-US,en;q=0.5',
					'Accept-Encoding': 'gzip, deflate',
					Connection: 'close',
				},
				timeout: this.TIMEOUT,
			};

			const req = client.request(options, (res) =>
			{
				const ttfb = Date.now() - startTime;

				// Handle redirects
				if (res.statusCode && res.statusCode >= 300 && res.statusCode < 400 && res.headers.location)
				{
					const redirectUrl = new URL(res.headers.location, url).toString();
					this.fetchHomepage(redirectUrl, redirectCount + 1)
						.then((response) =>
						{
							response.redirects.unshift(url);
							resolve(response);
						})
						.catch(reject);
					return;
				}

				let data = '';
				res.on('data', (chunk) =>
				{
					data += chunk;
				});

				res.on('end', () =>
				{
					const headers: Record<string, string> = {};
					Object.entries(res.headers).forEach(([key, value]) =>
					{
						headers[key] = Array.isArray(value) ? value.join(', ') : value || '';
					});

					resolve({
						statusCode: res.statusCode || 0,
						html: data,
						headers,
						redirects: [],
						finalUrl: url,
						contentType: res.headers['content-type'],
						contentLength: parseInt(res.headers['content-length'] || '0'),
						ttfb,
					});
				});
			});

			req.on('error', reject);
			req.on('timeout', () =>
			{
				req.destroy();
				reject(new Error('Request timeout'));
			});

			req.end();
		});
	}

	/**
	 * Extract meta tags from HTML
	 */
	private extractMetaTags(html: string): HomepageAnalysisResult['metaTags']
	{
		const metaTags: HomepageAnalysisResult['metaTags'] = {};

		try
		{
			// Title
			const titleMatch = html.match(/<title[^>]*>([^<]*)<\/title>/i);
			if (titleMatch) metaTags.title = titleMatch[1].trim();

			// Meta tags
			const metaRegex = /<meta\s+([^>]*?)>/gi;
			let match;

			while ((match = metaRegex.exec(html)) !== null)
			{
				const metaContent = match[1];

				// Description
				if (/name\s*=\s*["']description["']/i.test(metaContent))
				{
					const contentMatch = metaContent.match(/content\s*=\s*["']([^"']*)["']/i);
					if (contentMatch) metaTags.description = contentMatch[1];
				}

				// Keywords
				if (/name\s*=\s*["']keywords["']/i.test(metaContent))
				{
					const contentMatch = metaContent.match(/content\s*=\s*["']([^"']*)["']/i);
					if (contentMatch) metaTags.keywords = contentMatch[1];
				}

				// Author
				if (/name\s*=\s*["']author["']/i.test(metaContent))
				{
					const contentMatch = metaContent.match(/content\s*=\s*["']([^"']*)["']/i);
					if (contentMatch) metaTags.author = contentMatch[1];
				}

				// Viewport
				if (/name\s*=\s*["']viewport["']/i.test(metaContent))
				{
					const contentMatch = metaContent.match(/content\s*=\s*["']([^"']*)["']/i);
					if (contentMatch) metaTags.viewport = contentMatch[1];
				}

				// Charset
				if (/charset\s*=\s*["']?([^"'\s>]*)/i.test(metaContent))
				{
					const charsetMatch = metaContent.match(/charset\s*=\s*["']?([^"'\s>]*)/i);
					if (charsetMatch) metaTags.charset = charsetMatch[1];
				}

				// Robots
				if (/name\s*=\s*["']robots["']/i.test(metaContent))
				{
					const contentMatch = metaContent.match(/content\s*=\s*["']([^"']*)["']/i);
					if (contentMatch) metaTags.robots = contentMatch[1];
				}

				// Open Graph
				if (/property\s*=\s*["']og:title["']/i.test(metaContent))
				{
					const contentMatch = metaContent.match(/content\s*=\s*["']([^"']*)["']/i);
					if (contentMatch) metaTags.ogTitle = contentMatch[1];
				}

				if (/property\s*=\s*["']og:description["']/i.test(metaContent))
				{
					const contentMatch = metaContent.match(/content\s*=\s*["']([^"']*)["']/i);
					if (contentMatch) metaTags.ogDescription = contentMatch[1];
				}

				if (/property\s*=\s*["']og:image["']/i.test(metaContent))
				{
					const contentMatch = metaContent.match(/content\s*=\s*["']([^"']*)["']/i);
					if (contentMatch) metaTags.ogImage = contentMatch[1];
				}

				// Twitter Card
				if (/name\s*=\s*["']twitter:card["']/i.test(metaContent))
				{
					const contentMatch = metaContent.match(/content\s*=\s*["']([^"']*)["']/i);
					if (contentMatch) metaTags.twitterCard = contentMatch[1];
				}
			}

			// Canonical link
			const canonicalMatch = html.match(/<link\s+[^>]*rel\s*=\s*["']canonical["'][^>]*href\s*=\s*["']([^"']*)["']/i);
			if (canonicalMatch) metaTags.canonical = canonicalMatch[1];
		}
		catch (error)
		{
			logger.debug('Error extracting meta tags:', error);
		}

		return metaTags;
	}

	/**
	 * Detect technologies used on the website
	 */
	private detectTechnologies(html: string, headers: Record<string, string>): HomepageAnalysisResult['technologies']
	{
		const technologies: HomepageAnalysisResult['technologies'] = {
			analytics: [],
			libraries: [],
		};

		try
		{
			// Server from headers
			if (headers.server)
			{
				technologies.server = headers.server;
			}

			// Detect common technologies from HTML
			const htmlLower = html.toLowerCase();

			// CMS Detection
			if (htmlLower.includes('wp-content') || htmlLower.includes('wordpress'))
			{
				technologies.cms = 'WordPress';
			}
			else if (htmlLower.includes('drupal'))
			{
				technologies.cms = 'Drupal';
			}
			else if (htmlLower.includes('joomla'))
			{
				technologies.cms = 'Joomla';
			}

			// Framework Detection
			if (htmlLower.includes('react') || htmlLower.includes('_react'))
			{
				technologies.framework = 'React';
			}
			else if (htmlLower.includes('angular') || htmlLower.includes('ng-'))
			{
				technologies.framework = 'Angular';
			}
			else if (htmlLower.includes('vue') || htmlLower.includes('v-'))
			{
				technologies.framework = 'Vue.js';
			}

			// Analytics Detection
			if (htmlLower.includes('google-analytics') || htmlLower.includes('gtag'))
			{
				technologies.analytics.push('Google Analytics');
			}
			if (htmlLower.includes('googletagmanager'))
			{
				technologies.analytics.push('Google Tag Manager');
			}
			if (htmlLower.includes('facebook.com/tr'))
			{
				technologies.analytics.push('Facebook Pixel');
			}

			// Library Detection
			if (htmlLower.includes('jquery'))
			{
				technologies.libraries.push('jQuery');
			}
			if (htmlLower.includes('bootstrap'))
			{
				technologies.libraries.push('Bootstrap');
			}
		}
		catch (error)
		{
			logger.debug('Error detecting technologies:', error);
		}

		return technologies;
	}

	/**
	 * Analyze content structure and quality
	 */
	private analyzeContent(html: string): HomepageAnalysisResult['content']
	{
		const content: HomepageAnalysisResult['content'] = {
			hasH1: false,
			hasH2: false,
			imageCount: 0,
			linkCount: 0,
			wordCount: 0,
		};

		try
		{
			// Check for headings
			content.hasH1 = /<h1[^>]*>/i.test(html);
			content.hasH2 = /<h2[^>]*>/i.test(html);

			// Count images
			const imgMatches = html.match(/<img[^>]*>/gi);
			content.imageCount = imgMatches ? imgMatches.length : 0;

			// Count links
			const linkMatches = html.match(/<a[^>]*href[^>]*>/gi);
			content.linkCount = linkMatches ? linkMatches.length : 0;

			// Extract text content and count words
			const textContent = html
				.replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
				.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '')
				.replace(/<[^>]*>/g, ' ')
				.replace(/\s+/g, ' ')
				.trim();

			const words = textContent.split(/\s+/).filter(word => word.length > 0);
			content.wordCount = words.length;

			// Detect language (basic detection)
			const langMatch = html.match(/<html[^>]*lang\s*=\s*["']([^"']*)["']/i);
			if (langMatch)
			{
				content.language = langMatch[1];
			}
		}
		catch (error)
		{
			logger.debug('Error analyzing content:', error);
		}

		return content;
	}
}

export default HomepageAnalyzer;
export type { HomepageAnalysisResult };
