import https from 'https';
import http from 'http';
import { URL } from 'url';
import { Logger, RedisClientWrapper, ScyllaClient } from '@shared';

const logger = Logger.getLogger('FaviconCollector');

type FaviconCollectionResult = {
	domain: string;
	faviconUrl?: string;
	faviconFound: boolean;
	faviconSize?: number;
	faviconType?: string;
	sources: {
		duckduckgo: boolean;
		direct: boolean;
		html: boolean;
	};
	fallbackUsed: string | null;
	lastCollected: string;
	error?: string;
};

/**
 * Favicon Collector with caching and storage
 * Collects favicons using DuckDuckGo API and direct domain checks
 * Implements Redis caching and ScyllaDB storage for persistence
 */
class FaviconCollector
{
	private readonly DUCKDUCKGO_FAVICON_API = 'https://icons.duckduckgo.com/ip3';

	private readonly TIMEOUT = 10000;

	private readonly USER_AGENT = 'Mozilla/5.0 (compatible; DomainRankingBot/1.0)';

	private readonly CACHE_TTL = 86400; // 24 hours in seconds

	private readonly CACHE_KEY_PREFIX = 'favicon:';

	private redisClient: RedisClientWrapper;

	private scyllaClient: ScyllaClient;

	constructor(redisClient?: RedisClientWrapper, scyllaClient?: ScyllaClient)
	{
		this.redisClient = redisClient || new RedisClientWrapper();
		this.scyllaClient = scyllaClient || new ScyllaClient();
	}

	/**
	 * Collect favicon for domain with caching and storage
	 */
	async collectFavicon(domain: string, useCache: boolean = true): Promise<FaviconCollectionResult>
	{
		logger.info(`Collecting favicon for domain: ${domain}`);

		// Check cache first if enabled
		if (useCache)
		{
			const cached = await this.getCachedFavicon(domain);
			if (cached)
			{
				logger.info(`Favicon found in cache for domain: ${domain}`);
				return cached;
			}
		}

		const result: FaviconCollectionResult = {
			domain,
			faviconFound: false,
			sources: {
				duckduckgo: false,
				direct: false,
				html: false,
			},
			fallbackUsed: null,
			lastCollected: new Date().toISOString(),
		};

		try
		{
			// Method 1: Try DuckDuckGo favicon API
			const duckduckgoResult = await this.tryDuckDuckGoFavicon(domain);
			if (duckduckgoResult.found)
			{
				result.faviconFound = true;
				result.faviconUrl = duckduckgoResult.url;
				result.faviconSize = duckduckgoResult.size;
				result.faviconType = duckduckgoResult.type;
				result.sources.duckduckgo = true;
				result.fallbackUsed = 'duckduckgo';

				logger.info(`Favicon found via DuckDuckGo for domain: ${domain}`);

				// Cache and store the result
				await this.cacheFavicon(domain, result);
				await this.storeFavicon(domain, result);

				return result;
			}

			// Method 2: Try direct favicon.ico check
			const directResult = await this.tryDirectFavicon(domain);
			if (directResult.found)
			{
				result.faviconFound = true;
				result.faviconUrl = directResult.url;
				result.faviconSize = directResult.size;
				result.faviconType = directResult.type;
				result.sources.direct = true;
				result.fallbackUsed = 'direct';

				logger.info(`Favicon found via direct check for domain: ${domain}`);

				// Cache and store the result
				await this.cacheFavicon(domain, result);
				await this.storeFavicon(domain, result);

				return result;
			}

			// Method 3: Try parsing HTML for favicon links
			const htmlResult = await this.tryHTMLFavicon(domain);
			if (htmlResult.found)
			{
				result.faviconFound = true;
				result.faviconUrl = htmlResult.url;
				result.faviconSize = htmlResult.size;
				result.faviconType = htmlResult.type;
				result.sources.html = true;
				result.fallbackUsed = 'html';

				logger.info(`Favicon found via HTML parsing for domain: ${domain}`);

				// Cache and store the result
				await this.cacheFavicon(domain, result);
				await this.storeFavicon(domain, result);

				return result;
			}

			logger.info(`No favicon found for domain: ${domain}`);

			// Cache negative result to avoid repeated attempts
			await this.cacheFavicon(domain, result);
			await this.storeFavicon(domain, result);

			return result;
		}
		catch (error)
		{
			logger.error(`Favicon collection failed for domain: ${domain}:`, error);
			result.error = (error as Error).message;

			// Cache error result with shorter TTL
			await this.cacheFavicon(domain, result, 3600); // 1 hour for errors
			await this.storeFavicon(domain, result);

			return result;
		}
	}

	/**
	 * Get favicon from cache
	 */
	private async getCachedFavicon(domain: string): Promise<FaviconCollectionResult | null>
	{
		try
		{
			const cacheKey = `${this.CACHE_KEY_PREFIX}${domain}`;
			const cached = await this.redisClient.get<FaviconCollectionResult>(cacheKey);

			if (cached)
			{
				// Update lastCollected timestamp for cache hits
				cached.lastCollected = new Date().toISOString();
				return cached;
			}

			return null;
		}
		catch (error)
		{
			logger.warn(`Failed to get cached favicon for ${domain}:`, error);
			return null;
		}
	}

	/**
	 * Cache favicon result
	 */
	private async cacheFavicon(domain: string, result: FaviconCollectionResult, ttl?: number): Promise<void>
	{
		try
		{
			const cacheKey = `${this.CACHE_KEY_PREFIX}${domain}`;
			const cacheTtl = ttl || this.CACHE_TTL;

			await this.redisClient.set(cacheKey, result, cacheTtl);
			logger.debug(`Favicon cached for domain: ${domain} (TTL: ${cacheTtl}s)`);
		}
		catch (error)
		{
			logger.warn(`Failed to cache favicon for ${domain}:`, error);
		}
	}

	/**
	 * Store favicon result in ScyllaDB
	 */
	private async storeFavicon(domain: string, result: FaviconCollectionResult): Promise<void>
	{
		try
		{
			// Check if ScyllaDB is connected
			if (!this.scyllaClient.getClient())
			{
				await this.scyllaClient.connect();
			}

			// Store in domain_analysis table as part of domain data
			const query = `
				UPDATE domain_analysis
				SET
					technical_metrics = technical_metrics + ?,
					last_crawled = ?
				WHERE domain = ?
			`;

			const faviconMetrics = new Map([
				['favicon_url', result.faviconUrl || ''],
				['favicon_found', result.faviconFound.toString()],
				['favicon_size', (result.faviconSize || 0).toString()],
				['favicon_type', result.faviconType || ''],
				['favicon_source', result.fallbackUsed || ''],
				['favicon_last_collected', result.lastCollected],
			]);

			const params = [
				faviconMetrics,
				new Date(),
				domain,
			];

			await this.scyllaClient.execute(query, params);
			logger.debug(`Favicon stored in ScyllaDB for domain: ${domain}`);
		}
		catch (error)
		{
			logger.warn(`Failed to store favicon in ScyllaDB for ${domain}:`, error);
		}
	}

	/**
	 * Get stored favicon from ScyllaDB
	 */
	async getStoredFavicon(domain: string): Promise<FaviconCollectionResult | null>
	{
		try
		{
			// Check if ScyllaDB is connected
			if (!this.scyllaClient.getClient())
			{
				await this.scyllaClient.connect();
			}

			const query = 'SELECT technical_metrics FROM domain_analysis WHERE domain = ?';
			const result = await this.scyllaClient.execute(query, [domain]);

			if (result.rows.length === 0)
			{
				return null;
			}

			const row = result.first();
			const technicalMetrics = row.technical_metrics || new Map();

			// Extract favicon data from technical metrics
			const faviconUrl = technicalMetrics.get('favicon_url');
			const faviconFound = technicalMetrics.get('favicon_found') === 'true';
			const faviconSize = parseInt(technicalMetrics.get('favicon_size') || '0');
			const faviconType = technicalMetrics.get('favicon_type');
			const faviconSource = technicalMetrics.get('favicon_source');
			const lastCollected = technicalMetrics.get('favicon_last_collected');

			if (!lastCollected)
			{
				return null;
			}

			return {
				domain,
				faviconUrl: faviconUrl || undefined,
				faviconFound,
				faviconSize: faviconSize > 0 ? faviconSize : undefined,
				faviconType: faviconType || undefined,
				sources: {
					duckduckgo: faviconSource === 'duckduckgo',
					direct: faviconSource === 'direct',
					html: faviconSource === 'html',
				},
				fallbackUsed: faviconSource || null,
				lastCollected: lastCollected || new Date().toISOString(),
			};
		}
		catch (error)
		{
			logger.warn(`Failed to get stored favicon for ${domain}:`, error);
			return null;
		}
	}

	/**
	 * Invalidate favicon cache for domain
	 */
	async invalidateFaviconCache(domain: string): Promise<void>
	{
		try
		{
			const cacheKey = `${this.CACHE_KEY_PREFIX}${domain}`;
			await this.redisClient.del(cacheKey);
			logger.info(`Favicon cache invalidated for domain: ${domain}`);
		}
		catch (error)
		{
			logger.warn(`Failed to invalidate favicon cache for ${domain}:`, error);
		}
	}

	/**
	 * Get favicon cache statistics
	 */
	async getFaviconCacheStats(): Promise<{
		totalCached: number;
		successfulCached: number;
		failedCached: number;
	}>
	{
		try
		{
			const pattern = `${this.CACHE_KEY_PREFIX}*`;
			const keys = await this.redisClient.getClient()?.keys(pattern) || [];

			let successfulCached = 0;
			let failedCached = 0;

			// Sample a subset of keys to avoid performance issues
			const sampleSize = Math.min(keys.length, 100);
			const sampleKeys = keys.slice(0, sampleSize);

			for (const key of sampleKeys)
			{
				const cached = await this.redisClient.get<FaviconCollectionResult>(key);
				if (cached)
				{
					if (cached.faviconFound)
					{
						successfulCached++;
					}
					else
					{
						failedCached++;
					}
				}
			}

			// Extrapolate to full dataset
			const ratio = keys.length / sampleSize;

			return {
				totalCached: keys.length,
				successfulCached: Math.round(successfulCached * ratio),
				failedCached: Math.round(failedCached * ratio),
			};
		}
		catch (error)
		{
			logger.warn('Failed to get favicon cache stats:', error);
			return {
				totalCached: 0,
				successfulCached: 0,
				failedCached: 0,
			};
		}
	}

	/**
	 * Try DuckDuckGo favicon API
	 */
	private async tryDuckDuckGoFavicon(domain: string): Promise<{
		found: boolean;
		url?: string;
		size?: number;
		type?: string;
	}>
	{
		try
		{
			const faviconUrl = `${this.DUCKDUCKGO_FAVICON_API}/${domain}.ico`;

			const response = await this.checkUrl(faviconUrl);

			if (response.statusCode === 200)
			{
				return {
					found: true,
					url: faviconUrl,
					size: response.contentLength || 0,
					type: response.contentType || 'image/x-icon',
				};
			}

			return { found: false };
		}
		catch (error)
		{
			logger.debug(`DuckDuckGo favicon check failed for ${domain}:`, error);
			return { found: false };
		}
	}

	/**
	 * Try direct favicon.ico check
	 */
	private async tryDirectFavicon(domain: string): Promise<{
		found: boolean;
		url?: string;
		size?: number;
		type?: string;
	}>
	{
		try
		{
			const faviconUrl = `https://${domain}/favicon.ico`;

			const response = await this.checkUrl(faviconUrl);

			if (response.statusCode === 200 && response.contentLength > 0)
			{
				return {
					found: true,
					url: faviconUrl,
					size: response.contentLength,
					type: response.contentType || 'image/x-icon',
				};
			}

			// Try HTTP if HTTPS fails
			const httpFaviconUrl = `http://${domain}/favicon.ico`;
			const httpResponse = await this.checkUrl(httpFaviconUrl);

			if (httpResponse.statusCode === 200 && httpResponse.contentLength > 0)
			{
				return {
					found: true,
					url: httpFaviconUrl,
					size: httpResponse.contentLength,
					type: httpResponse.contentType || 'image/x-icon',
				};
			}

			return { found: false };
		}
		catch (error)
		{
			logger.debug(`Direct favicon check failed for ${domain}:`, error);
			return { found: false };
		}
	}

	/**
	 * Try parsing HTML for favicon links
	 */
	private async tryHTMLFavicon(domain: string): Promise<{
		found: boolean;
		url?: string;
		size?: number;
		type?: string;
	}>
	{
		try
		{
			const htmlUrl = `https://${domain}`;
			const html = await this.fetchHTML(htmlUrl);

			if (!html)
			{
				return { found: false };
			}

			// Look for favicon links in HTML
			const faviconLinks = this.extractFaviconLinks(html, htmlUrl);

			// Try each favicon link
			for (const link of faviconLinks)
			{
				try
				{
					const response = await this.checkUrl(link.href);

					if (response.statusCode === 200 && response.contentLength > 0)
					{
						return {
							found: true,
							url: link.href,
							size: response.contentLength,
							type: response.contentType || link.type || 'image/x-icon',
						};
					}
				}
				catch (error)
				{
					logger.debug(`Failed to check favicon link ${link.href}:`, error);
					continue;
				}
			}

			return { found: false };
		}
		catch (error)
		{
			logger.debug(`HTML favicon check failed for ${domain}:`, error);
			return { found: false };
		}
	}

	/**
	 * Check if URL is accessible and get basic info
	 */
	private async checkUrl(url: string): Promise<{
		statusCode: number;
		contentLength: number;
		contentType?: string;
	}>
	{
		return new Promise((resolve, reject) =>
		{
			const parsedUrl = new URL(url);
			const isHttps = parsedUrl.protocol === 'https:';
			const client = isHttps ? https : http;

			const options = {
				hostname: parsedUrl.hostname,
				port: parsedUrl.port || (isHttps ? 443 : 80),
				path: parsedUrl.pathname + parsedUrl.search,
				method: 'HEAD',
				headers: {
					'User-Agent': this.USER_AGENT,
					Accept: '*/*',
				},
				timeout: this.TIMEOUT,
			};

			const req = client.request(options, (res) =>
			{
				// For DuckDuckGo API, if HEAD doesn't return content-length, try GET with range
				let contentLength = parseInt(res.headers['content-length'] || '0');

				// If no content-length and this is a successful response, estimate size
				if (contentLength === 0 && res.statusCode === 200 && url.includes('duckduckgo.com'))
				{
					// DuckDuckGo favicons are typically 1-5KB, estimate 2KB
					contentLength = 2048;
				}

				resolve({
					statusCode: res.statusCode || 0,
					contentLength,
					contentType: res.headers['content-type'],
				});
			});

			req.on('error', reject);
			req.on('timeout', () =>
			{
				req.destroy();
				reject(new Error('Request timeout'));
			});

			req.end();
		});
	}

	/**
	 * Fetch HTML content
	 */
	private async fetchHTML(url: string): Promise<string | null>
	{
		return new Promise((resolve) =>
		{
			const parsedUrl = new URL(url);
			const isHttps = parsedUrl.protocol === 'https:';
			const client = isHttps ? https : http;

			const options = {
				hostname: parsedUrl.hostname,
				port: parsedUrl.port || (isHttps ? 443 : 80),
				path: parsedUrl.pathname + parsedUrl.search,
				method: 'GET',
				headers: {
					'User-Agent': this.USER_AGENT,
					Accept: 'text/html',
				},
				timeout: this.TIMEOUT,
			};

			const req = client.request(options, (res) =>
			{
				if (res.statusCode !== 200)
				{
					resolve(null);
					return;
				}

				let data = '';
				res.on('data', (chunk) =>
				{
					data += chunk;
					// Limit HTML size to prevent memory issues
					if (data.length > 100000)
					{
						res.destroy();
						resolve(data);
					}
				});

				res.on('end', () =>
				{
					resolve(data);
				});
			});

			req.on('error', () =>
			{
				resolve(null);
			});

			req.on('timeout', () =>
			{
				req.destroy();
				resolve(null);
			});

			req.end();
		});
	}

	/**
	 * Extract favicon links from HTML
	 */
	private extractFaviconLinks(html: string, baseUrl: string): Array<{
		href: string;
		type?: string;
		sizes?: string;
	}>
	{
		const links: Array<{ href: string; type?: string; sizes?: string }> = [];

		try
		{
			// Look for favicon link tags
			const linkRegex = /<link\s+[^>]*>/gi;
			let match;

			while ((match = linkRegex.exec(html)) !== null)
			{
				const linkTag = match[0];

				// Check if it's a favicon-related link
				if (!/rel\s*=\s*["'](?:icon|shortcut icon|apple-touch-icon)["']/i.test(linkTag))
				{
					continue;
				}

				// Extract href
				const hrefMatch = linkTag.match(/href\s*=\s*["']([^"']*)["']/i);
				if (!hrefMatch) continue;

				let href = hrefMatch[1];

				// Convert relative URLs to absolute
				if (href.startsWith('//'))
				{
					href = new URL(baseUrl).protocol + href;
				}
				else if (href.startsWith('/'))
				{
					href = new URL(href, baseUrl).toString();
				}
				else if (!href.startsWith('http'))
				{
					href = new URL(href, baseUrl).toString();
				}

				// Extract type
				const typeMatch = linkTag.match(/type\s*=\s*["']([^"']*)["']/i);
				const type = typeMatch ? typeMatch[1] : undefined;

				// Extract sizes
				const sizesMatch = linkTag.match(/sizes\s*=\s*["']([^"']*)["']/i);
				const sizes = sizesMatch ? sizesMatch[1] : undefined;

				links.push({ href, type, sizes });
			}

			// Sort by preference (standard favicon first, then by size)
			links.sort((a, b) =>
			{
				// Prefer standard favicon.ico
				if (a.href.endsWith('favicon.ico')) return -1;
				if (b.href.endsWith('favicon.ico')) return 1;

				// Prefer smaller sizes for standard favicons
				if (a.sizes && b.sizes)
				{
					const aSize = parseInt(a.sizes.split('x')[0] || '0');
					const bSize = parseInt(b.sizes.split('x')[0] || '0');
					return aSize - bSize;
				}

				return 0;
			});
		}
		catch (error)
		{
			logger.debug('Error extracting favicon links:', error);
		}

		return links;
	}
}

export default FaviconCollector;
export type { FaviconCollectionResult };
