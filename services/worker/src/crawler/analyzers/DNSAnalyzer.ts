import dns from 'node:dns';
import { promisify } from 'node:util';
import { logger } from '@shared/utils/Logger';

const dnsLogger = logger.getLogger('DNSAnalyzer');

// Promisify DNS methods
const resolveTxt = promisify(dns.resolveTxt);
const resolve4 = promisify(dns.resolve4);
const resolve6 = promisify(dns.resolve6);
const resolveMx = promisify(dns.resolveMx);
const resolveCname = promisify(dns.resolveCname);
const resolveNs = promisify(dns.resolveNs);
const resolveSoa = promisify(dns.resolveSoa);

export interface DNSRecord
{
	type: string;
	value: string | string[];
	ttl?: number;
	priority?: number;
}

export interface DNSAnalysisResult
{
	domain: string;
	records: {
		A: string[];
		AAAA: string[];
		MX: Array<{ exchange: string; priority: number }>;
		CNAME: string[];
		TXT: string[];
		NS: string[];
		SOA?: {
			primary: string;
			admin: string;
			serial: number;
			refresh: number;
			retry: number;
			expiration: number;
			minimum: number;
		};
	};
	ipAddresses: {
		ipv4: string[];
		ipv6: string[];
	};
	mailServers: Array<{ server: string; priority: number }>;
	nameServers: string[];
	txtRecords: string[];
	hasCloudflare: boolean;
	hasCDN: boolean;
	dnsProvider: string | null;
	lastAnalyzed: Date;
	analysisTime: number;
	errors: string[];
}

/**
 * DNS records analyzer for comprehensive domain DNS analysis
 */
class DNSAnalyzer
{
	private readonly timeout: number;

	private readonly cdnProviders: string[];

	private readonly dnsProviders: string[];

	constructor()
	{
		this.timeout = 5000; // 5 second timeout for DNS queries
		this.cdnProviders = [
			'cloudflare',
			'amazonaws.com',
			'fastly',
			'maxcdn',
			'keycdn',
			'stackpath',
			'bunnycdn',
			'jsdelivr',
		];
		this.dnsProviders = [
			'cloudflare.com',
			'amazonaws.com',
			'google.com',
			'azure.com',
			'digitalocean.com',
			'godaddy.com',
			'namecheap.com',
		];
	}

	/**
   * Analyze DNS records for a domain
   */
	async analyzeDNSRecords(domain: string): Promise<DNSAnalysisResult>
	{
		const startTime = Date.now();
		const normalizedDomain = this.normalizeDomain(domain);

		dnsLogger.info(`Analyzing DNS records for domain: ${domain}`);

		const result: DNSAnalysisResult = {
			domain: normalizedDomain,
			records: {
				A: [],
				AAAA: [],
				MX: [],
				CNAME: [],
				TXT: [],
				NS: [],
				SOA: undefined,
			},
			ipAddresses: {
				ipv4: [],
				ipv6: [],
			},
			mailServers: [],
			nameServers: [],
			txtRecords: [],
			hasCloudflare: false,
			hasCDN: false,
			dnsProvider: null,
			lastAnalyzed: new Date(),
			analysisTime: 0,
			errors: [],
		};

		try
		{
			// Set DNS timeout
			dns.setServers(['*******', '*******', '*******', '*******']);

			// Collect all DNS records in parallel
			const dnsPromises = [
				this.getARecords(normalizedDomain),
				this.getAAAARecords(normalizedDomain),
				this.getMXRecords(normalizedDomain),
				this.getCNAMERecords(normalizedDomain),
				this.getTXTRecords(normalizedDomain),
				this.getNSRecords(normalizedDomain),
				this.getSOARecord(normalizedDomain),
			];

			const [aRecords, aaaaRecords, mxRecords, cnameRecords, txtRecords, nsRecords, soaRecord] =
        await Promise.allSettled(dnsPromises);

			// Process A records
			if (aRecords.status === 'fulfilled' && aRecords.value)
			{
				result.records.A = aRecords.value as string[];
				result.ipAddresses.ipv4 = aRecords.value as string[];
			}
			else if (aRecords.status === 'rejected')
			{
				result.errors.push(`A records: ${aRecords.reason.message}`);
			}

			// Process AAAA records
			if (aaaaRecords.status === 'fulfilled' && aaaaRecords.value)
			{
				result.records.AAAA = aaaaRecords.value as string[];
				result.ipAddresses.ipv6 = aaaaRecords.value as string[];
			}
			else if (aaaaRecords.status === 'rejected')
			{
				result.errors.push(`AAAA records: ${aaaaRecords.reason.message}`);
			}

			// Process MX records
			if (mxRecords.status === 'fulfilled' && mxRecords.value)
			{
				result.records.MX = mxRecords.value as Array<{ exchange: string; priority: number }>;
				result.mailServers = (mxRecords.value as Array<{ exchange: string; priority: number }>).map((mx: { exchange: string; priority: number }) => ({
					server: mx.exchange,
					priority: mx.priority,
				}));
			}
			else if (mxRecords.status === 'rejected')
			{
				result.errors.push(`MX records: ${mxRecords.reason.message}`);
			}

			// Process CNAME records
			if (cnameRecords.status === 'fulfilled' && cnameRecords.value)
			{
				result.records.CNAME = cnameRecords.value as string[];
			}
			else if (cnameRecords.status === 'rejected')
			{
				result.errors.push(`CNAME records: ${cnameRecords.reason.message}`);
			}

			// Process TXT records
			if (txtRecords.status === 'fulfilled' && txtRecords.value)
			{
				result.records.TXT = txtRecords.value as string[];
				result.txtRecords = txtRecords.value as string[];
			}
			else if (txtRecords.status === 'rejected')
			{
				result.errors.push(`TXT records: ${txtRecords.reason.message}`);
			}

			// Process NS records
			if (nsRecords.status === 'fulfilled' && nsRecords.value)
			{
				result.records.NS = nsRecords.value as string[];
				result.nameServers = nsRecords.value as string[];
			}
			else if (nsRecords.status === 'rejected')
			{
				result.errors.push(`NS records: ${nsRecords.reason.message}`);
			}

			// Process SOA record
			if (soaRecord.status === 'fulfilled' && soaRecord.value)
			{
				result.records.SOA = soaRecord.value as {
					primary: string;
					admin: string;
					serial: number;
					refresh: number;
					retry: number;
					expiration: number;
					minimum: number;
				};
			}
			else if (soaRecord.status === 'rejected')
			{
				result.errors.push(`SOA record: ${soaRecord.reason.message}`);
			}

			// Analyze DNS provider and CDN usage
			result.hasCloudflare = this.detectCloudflare(result);
			result.hasCDN = this.detectCDN(result);
			result.dnsProvider = this.detectDNSProvider(result);

			result.analysisTime = Date.now() - startTime;

			dnsLogger.info(`DNS analysis completed for domain: ${domain} in ${result.analysisTime}ms`);
			return result;
		}
		catch (error)
		{
			const errorMessage = error instanceof Error ? error.message : 'Unknown error';
			dnsLogger.error(`DNS analysis failed for domain: ${domain}:`, error);

			result.errors.push(`DNS analysis failed: ${errorMessage}`);
			result.analysisTime = Date.now() - startTime;

			return result;
		}
	}

	/**
   * Get A records (IPv4 addresses)
   */
	private async getARecords(domain: string): Promise<string[]>
	{
		try
		{
			const addresses = await resolve4(domain);
			return addresses;
		}
		catch (error: unknown)
		{
			if (error instanceof Error && 'code' in error && (error.code === 'ENODATA' || error.code === 'ENOTFOUND'))
			{
				return [];
			}
			throw error;
		}
	}

	/**
   * Get AAAA records (IPv6 addresses)
   */
	private async getAAAARecords(domain: string): Promise<string[]>
	{
		try
		{
			const addresses = await resolve6(domain);
			return addresses;
		}
		catch (error: unknown)
		{
			if (error instanceof Error && 'code' in error && (error.code === 'ENODATA' || error.code === 'ENOTFOUND'))
			{
				return [];
			}
			throw error;
		}
	}

	/**
   * Get MX records (Mail exchange)
   */
	private async getMXRecords(domain: string): Promise<Array<{ exchange: string; priority: number }>>
	{
		try
		{
			const mxRecords = await resolveMx(domain);
			return mxRecords.map(mx => ({
				exchange: mx.exchange,
				priority: mx.priority,
			}));
		}
		catch (error: unknown)
		{
			if (error instanceof Error && 'code' in error && (error.code === 'ENODATA' || error.code === 'ENOTFOUND'))
			{
				return [];
			}
			throw error;
		}
	}

	/**
   * Get CNAME records
   */
	private async getCNAMERecords(domain: string): Promise<string[]>
	{
		try
		{
			const cnames = await resolveCname(domain);
			return cnames;
		}
		catch (error: unknown)
		{
			if (error instanceof Error && 'code' in error && (error.code === 'ENODATA' || error.code === 'ENOTFOUND'))
			{
				return [];
			}
			throw error;
		}
	}

	/**
   * Get TXT records
   */
	private async getTXTRecords(domain: string): Promise<string[]>
	{
		try
		{
			const txtRecords = await resolveTxt(domain);
			// Flatten the array of arrays and join each record
			return txtRecords.map(record => (Array.isArray(record) ? record.join('') : record));
		}
		catch (error: unknown)
		{
			if (error instanceof Error && 'code' in error && (error.code === 'ENODATA' || error.code === 'ENOTFOUND'))
			{
				return [];
			}
			throw error;
		}
	}

	/**
   * Get NS records (Name servers)
   */
	private async getNSRecords(domain: string): Promise<string[]>
	{
		try
		{
			const nameServers = await resolveNs(domain);
			return nameServers;
		}
		catch (error: unknown)
		{
			if (error instanceof Error && 'code' in error && (error.code === 'ENODATA' || error.code === 'ENOTFOUND'))
			{
				return [];
			}
			throw error;
		}
	}

	/**
   * Get SOA record (Start of Authority)
   */
	private async getSOARecord(domain: string): Promise<{
		primary: string;
		admin: string;
		serial: number;
		refresh: number;
		retry: number;
		expiration: number;
		minimum: number;
	} | null>
	{
		try
		{
			const soa = await resolveSoa(domain);
			return {
				primary: soa.nsname,
				admin: soa.hostmaster,
				serial: soa.serial,
				refresh: soa.refresh,
				retry: soa.retry,
				expiration: soa.expire,
				minimum: soa.minttl,
			};
		}
		catch (error: unknown)
		{
			if (error instanceof Error && 'code' in error && (error.code === 'ENODATA' || error.code === 'ENOTFOUND'))
			{
				return null;
			}
			throw error;
		}
	}

	/**
   * Detect if domain uses Cloudflare
   */
	private detectCloudflare(result: DNSAnalysisResult): boolean
	{
		// Check name servers
		const hasCloudflareNS = result.nameServers.some(ns => ns.toLowerCase().includes('cloudflare'));

		// Check IP ranges (Cloudflare IP ranges)
		const cloudflareIPRanges = [
			'************/20',
			'************/22',
			'************/22',
			'**********/22',
			'************/18',
			'*************/18',
			'************/20',
			'************/20',
			'*************/22',
			'************/17',
			'***********/15',
			'**********/13',
			'**********/14',
			'**********/13',
			'**********/22',
		];

		// Simple check for common Cloudflare IP patterns
		const hasCloudflareIP = result.ipAddresses.ipv4.some(ip => ip.startsWith('104.16.') ||
      ip.startsWith('104.24.') ||
      ip.startsWith('172.64.') ||
      ip.startsWith('162.158.'));

		return hasCloudflareNS || hasCloudflareIP;
	}

	/**
   * Detect if domain uses a CDN
   */
	private detectCDN(result: DNSAnalysisResult): boolean
	{
		// Check CNAME records for CDN providers
		const hasCDNCname = result.records.CNAME.some(cname => this.cdnProviders.some(provider => cname.toLowerCase().includes(provider)));

		// Check name servers for CDN providers
		const hasCDNNameServer = result.nameServers.some(ns => this.cdnProviders.some(provider => ns.toLowerCase().includes(provider)));

		return hasCDNCname || hasCDNNameServer || result.hasCloudflare;
	}

	/**
   * Detect DNS provider
   */
	private detectDNSProvider(result: DNSAnalysisResult): string | null
	{
		for (const provider of this.dnsProviders)
		{
			const hasProvider = result.nameServers.some(ns => ns.toLowerCase().includes(provider));

			if (hasProvider)
			{
				return provider;
			}
		}

		// Special cases
		if (result.hasCloudflare)
		{
			return 'cloudflare.com';
		}

		return null;
	}

	/**
   * Get DNS record summary for storage
   */
	getDNSRecordSummary(result: DNSAnalysisResult): Record<string, string[]>
	{
		return {
			A: result.records.A,
			AAAA: result.records.AAAA,
			MX: result.records.MX.map(mx => `${mx.priority} ${mx.exchange}`),
			CNAME: result.records.CNAME,
			TXT: result.records.TXT,
			NS: result.records.NS,
		};
	}

	/**
   * Check if domain has email configuration
   */
	hasEmailConfiguration(result: DNSAnalysisResult): boolean
	{
		return result.records.MX.length > 0;
	}

	/**
   * Check if domain supports IPv6
   */
	supportsIPv6(result: DNSAnalysisResult): boolean
	{
		return result.records.AAAA.length > 0;
	}

	/**
   * Get primary IP address
   */
	getPrimaryIP(result: DNSAnalysisResult): string | null
	{
		return result.records.A.length > 0 ? result.records.A[0] : null;
	}

	/**
   * Normalize domain name
   */
	private normalizeDomain(domain: string): string
	{
		return domain
			.replace(/^https?:\/\//, '')
			.replace(/^www\./, '')
			.replace(/\/$/, '')
			.toLowerCase();
	}
}

export default DNSAnalyzer;
