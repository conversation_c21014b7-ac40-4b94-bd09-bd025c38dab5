import https from 'https';
import http from 'http';
import { URL } from 'url';
import * as cheerio from 'cheerio';
import { Logger } from '@shared';

const logger = Logger.getLogger('AdvancedContentAnalyzer');

interface PageContent
{
	url: string;
	title: string;
	description: string;
	content: string;
	headings: string[];
	metaKeywords: string[];
	wordCount: number;
	readabilityScore: number;
	language: string;
	lastModified?: string;
	contentHash: string;
}

interface ContentQualityMetrics
{
	overallScore: number;
	contentLength: number;
	readabilityScore: number;
	mediaRichness: number;
	structuralQuality: number;
	uniqueness: number;
	freshness: number;
}

interface AdvancedContentAnalysisResult
{
	domain: string;
	pages: {
		homepage?: PageContent;
		about?: PageContent;
		contact?: PageContent;
		privacy?: PageContent;
		terms?: PageContent;
		services?: PageContent;
	};
	contentQuality: ContentQualityMetrics;
	languageDetection: {
		primary: string;
		confidence: number;
		alternatives: Array<{ language: string; confidence: number }>;
	};
	contentMetrics: {
		totalWordCount: number;
		averageReadability: number;
		duplicateContentPercentage: number;
		mediaToTextRatio: number;
		headingStructureScore: number;
		internalLinkDensity: number;
	};
	technicalSEO: {
		hasStructuredData: boolean;
		structuredDataTypes: string[];
		hasCanonicalTags: boolean;
		hasMetaDescriptions: boolean;
		hasAltTags: boolean;
		imageOptimization: number;
	};
	lastAnalyzed: string;
	error?: string;
}

/**
 * Advanced Content Analyzer
 * Performs comprehensive multi-page content analysis including quality scoring,
 * readability analysis, and language detection
 */
class AdvancedContentAnalyzer
{
	private readonly USER_AGENT = 'Mozilla/5.0 (compatible; DomainRankingBot/1.0; +https://domainranking.com/bot)';

	private readonly TIMEOUT = 20000;

	private readonly MAX_CONTENT_LENGTH = 1000000; // 1MB limit per page

	// Common page paths to analyze
	private readonly COMMON_PAGES = [
		'', // homepage
		'/about',
		'/about-us',
		'/contact',
		'/contact-us',
		'/privacy',
		'/privacy-policy',
		'/terms',
		'/terms-of-service',
		'/services',
		'/products',
	];

	/**
	 * Perform comprehensive content analysis across multiple pages
	 */
	async analyzeContent(domain: string): Promise<AdvancedContentAnalysisResult>
	{
		logger.info(`Starting advanced content analysis for domain: ${domain}`);

		const result: AdvancedContentAnalysisResult = {
			domain,
			pages: {},
			contentQuality: {
				overallScore: 0,
				contentLength: 0,
				readabilityScore: 0,
				mediaRichness: 0,
				structuralQuality: 0,
				uniqueness: 0,
				freshness: 0,
			},
			languageDetection: {
				primary: 'en',
				confidence: 0,
				alternatives: [],
			},
			contentMetrics: {
				totalWordCount: 0,
				averageReadability: 0,
				duplicateContentPercentage: 0,
				mediaToTextRatio: 0,
				headingStructureScore: 0,
				internalLinkDensity: 0,
			},
			technicalSEO: {
				hasStructuredData: false,
				structuredDataTypes: [],
				hasCanonicalTags: false,
				hasMetaDescriptions: false,
				hasAltTags: false,
				imageOptimization: 0,
			},
			lastAnalyzed: new Date().toISOString(),
		};

		try
		{
			// Discover and analyze pages
			const discoveredPages = await this.discoverPages(domain);
			const pageContents: PageContent[] = [];

			// Analyze each discovered page
			for (const pageInfo of discoveredPages)
			{
				try
				{
					const content = await this.analyzePage(pageInfo.url, pageInfo.type);
					if (content)
					{
						pageContents.push(content);
						result.pages[pageInfo.type as keyof typeof result.pages] = content;
					}
				}
				catch (error)
				{
					logger.warn(`Failed to analyze page ${pageInfo.url}:`, error);
				}
			}

			if (pageContents.length === 0)
			{
				throw new Error('No pages could be analyzed');
			}

			// Calculate comprehensive metrics
			result.contentQuality = this.calculateContentQuality(pageContents);
			result.languageDetection = this.detectLanguage(pageContents);
			result.contentMetrics = this.calculateContentMetrics(pageContents);
			result.technicalSEO = this.analyzeTechnicalSEO(pageContents);

			logger.info(`Advanced content analysis completed for domain: ${domain}`, {
				pagesAnalyzed: pageContents.length,
				overallScore: result.contentQuality.overallScore,
				primaryLanguage: result.languageDetection.primary,
			});

			return result;
		}
		catch (error)
		{
			logger.error(`Advanced content analysis failed for domain: ${domain}:`, error);
			result.error = (error as Error).message;
			return result;
		}
	}

	/**
	 * Discover available pages on the domain
	 */
	private async discoverPages(domain: string): Promise<Array<{ url: string; type: string }>>
	{
		const discoveredPages: Array<{ url: string; type: string }> = [];

		// Always include homepage
		discoveredPages.push({
			url: `https://${domain}`,
			type: 'homepage',
		});

		// Check for common pages
		for (const path of this.COMMON_PAGES.slice(1))
		{
			const url = `https://${domain}${path}`;
			const isAccessible = await this.checkPageAccessibility(url);

			if (isAccessible)
			{
				const pageType = this.determinePageType(path);
				discoveredPages.push({ url, type: pageType });
			}
		}

		// Try to discover pages from sitemap
		try
		{
			const sitemapPages = await this.discoverFromSitemap(domain);
			for (const page of sitemapPages)
			{
				if (!discoveredPages.some(p => p.url === page.url))
				{
					discoveredPages.push(page);
				}
			}
		}
		catch (error)
		{
			logger.debug(`Sitemap discovery failed for ${domain}:`, error);
		}

		return discoveredPages.slice(0, 10); // Limit to 10 pages max
	}

	/**
	 * Check if a page is accessible
	 */
	private async checkPageAccessibility(url: string): Promise<boolean>
	{
		try
		{
			const response = await this.makeRequest(url, 'HEAD');
			return response.statusCode >= 200 && response.statusCode < 400;
		}
		catch
		{
			return false;
		}
	}

	/**
	 * Discover pages from sitemap.xml
	 */
	private async discoverFromSitemap(domain: string): Promise<Array<{ url: string; type: string }>>
	{
		const sitemapUrls = [
			`https://${domain}/sitemap.xml`,
			`https://${domain}/sitemap_index.xml`,
			`https://${domain}/sitemaps.xml`,
		];

		for (const sitemapUrl of sitemapUrls)
		{
			try
			{
				const response = await this.makeRequest(sitemapUrl);
				if (response.statusCode === 200 && response.body)
				{
					return this.parseSitemap(response.body);
				}
			}
			catch (error)
			{
				logger.debug(`Failed to fetch sitemap ${sitemapUrl}:`, error);
			}
		}

		return [];
	}

	/**
	 * Parse sitemap XML to extract URLs
	 */
	private parseSitemap(xml: string): Array<{ url: string; type: string }>
	{
		const pages: Array<{ url: string; type: string }> = [];

		try
		{
			const $ = cheerio.load(xml, { xmlMode: true });

			$('url loc').each((_, element) =>
			{
				const url = $(element).text().trim();
				if (url)
				{
					const pageType = this.determinePageTypeFromUrl(url);
					pages.push({ url, type: pageType });
				}
			});
		}
		catch (error)
		{
			logger.debug('Error parsing sitemap:', error);
		}

		return pages.slice(0, 8); // Limit sitemap discoveries
	}

	/**
	 * Determine page type from path
	 */
	private determinePageType(path: string): string
	{
		const pathLower = path.toLowerCase();

		if (pathLower.includes('about')) return 'about';
		if (pathLower.includes('contact')) return 'contact';
		if (pathLower.includes('privacy')) return 'privacy';
		if (pathLower.includes('terms')) return 'terms';
		if (pathLower.includes('service')) return 'services';
		if (pathLower.includes('product')) return 'services';

		return 'other';
	}

	/**
	 * Determine page type from full URL
	 */
	private determinePageTypeFromUrl(url: string): string
	{
		try
		{
			const parsedUrl = new URL(url);
			return this.determinePageType(parsedUrl.pathname);
		}
		catch
		{
			return 'other';
		}
	}

	/**
	 * Analyze individual page content
	 */
	private async analyzePage(url: string, _pageType: string): Promise<PageContent | null>
	{
		try
		{
			const response = await this.makeRequest(url);

			if (response.statusCode !== 200 || !response.body)
			{
				return null;
			}

			const $ = cheerio.load(response.body);

			// Extract basic content
			const title = $('title').text().trim() || $('h1').first().text().trim();
			const description = $('meta[name="description"]').attr('content') || '';

			// Extract text content
			$('script, style, nav, header, footer, aside').remove();
			const textContent = $('body').text().replace(/\s+/g, ' ').trim();

			// Extract headings
			const headings: string[] = [];
			$('h1, h2, h3, h4, h5, h6').each((_, element) =>
			{
				const heading = $(element).text().trim();
				if (heading)
				{
					headings.push(heading);
				}
			});

			// Extract meta keywords
			const metaKeywords = $('meta[name="keywords"]').attr('content')?.split(',').map(k => k.trim()) || [];

			// Calculate metrics
			const wordCount = this.countWords(textContent);
			const readabilityScore = this.calculateReadabilityScore(textContent);
			const language = this.detectPageLanguage($, textContent);
			const contentHash = this.generateContentHash(textContent);

			return {
				url,
				title,
				description,
				content: textContent.substring(0, 10000), // Limit stored content
				headings,
				metaKeywords,
				wordCount,
				readabilityScore,
				language,
				lastModified: response.headers['last-modified'],
				contentHash,
			};
		}
		catch (error)
		{
			logger.warn(`Failed to analyze page ${url}:`, error);
			return null;
		}
	}

	/**
	 * Make HTTP request
	 */
	private async makeRequest(url: string, method: 'GET' | 'HEAD' = 'GET'): Promise<{
		statusCode: number;
		headers: Record<string, string>;
		body?: string;
	}>
	{
		return new Promise((resolve, reject) =>
		{
			const parsedUrl = new URL(url);
			const isHttps = parsedUrl.protocol === 'https:';
			const client = isHttps ? https : http;

			const options = {
				hostname: parsedUrl.hostname,
				port: parsedUrl.port || (isHttps ? 443 : 80),
				path: parsedUrl.pathname + parsedUrl.search,
				method,
				headers: {
					'User-Agent': this.USER_AGENT,
					Accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
					'Accept-Language': 'en-US,en;q=0.5',
					'Accept-Encoding': 'gzip, deflate',
					Connection: 'close',
				},
				timeout: this.TIMEOUT,
			};

			const req = client.request(options, (res) =>
			{
				const headers: Record<string, string> = {};
				Object.entries(res.headers).forEach(([key, value]) =>
				{
					headers[key] = Array.isArray(value) ? value.join(', ') : value || '';
				});

				if (method === 'HEAD')
				{
					resolve({
						statusCode: res.statusCode || 0,
						headers,
					});
					return;
				}

				let data = '';
				let totalLength = 0;

				res.on('data', (chunk) =>
				{
					totalLength += chunk.length;
					if (totalLength > this.MAX_CONTENT_LENGTH)
					{
						req.destroy();
						reject(new Error('Content too large'));
						return;
					}
					data += chunk;
				});

				res.on('end', () =>
				{
					resolve({
						statusCode: res.statusCode || 0,
						headers,
						body: data,
					});
				});
			});

			req.on('error', reject);
			req.on('timeout', () =>
			{
				req.destroy();
				reject(new Error('Request timeout'));
			});

			req.end();
		});
	}

	/**
	 * Count words in text
	 */
	private countWords(text: string): number
	{
		return text.split(/\s+/).filter(word => word.length > 0).length;
	}

	/**
	 * Calculate Flesch Reading Ease score
	 */
	private calculateReadabilityScore(text: string): number
	{
		const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
		const words = text.split(/\s+/).filter(w => w.length > 0);
		const syllables = this.countSyllables(text);

		if (sentences.length === 0 || words.length === 0)
		{
			return 0;
		}

		const avgSentenceLength = words.length / sentences.length;
		const avgSyllablesPerWord = syllables / words.length;

		// Flesch Reading Ease formula
		const score = 206.835 - (1.015 * avgSentenceLength) - (84.6 * avgSyllablesPerWord);

		return Math.max(0, Math.min(100, score));
	}

	/**
	 * Count syllables in text (approximation)
	 */
	private countSyllables(text: string): number
	{
		const words = text.toLowerCase().split(/\s+/);
		let totalSyllables = 0;

		for (const word of words)
		{
			// Remove non-alphabetic characters
			const cleanWord = word.replace(/[^a-z]/g, '');
			if (cleanWord.length === 0) continue;

			// Count vowel groups
			const vowelGroups = cleanWord.match(/[aeiouy]+/g);
			let syllables = vowelGroups ? vowelGroups.length : 1;

			// Adjust for silent 'e'
			if (cleanWord.endsWith('e') && syllables > 1)
			{
				syllables--;
			}

			// Minimum of 1 syllable per word
			totalSyllables += Math.max(1, syllables);
		}

		return totalSyllables;
	}

	/**
	 * Detect page language
	 */
	private detectPageLanguage($: cheerio.CheerioAPI, text: string): string
	{
		// Check HTML lang attribute
		const htmlLang = $('html').attr('lang');
		if (htmlLang)
		{
			return htmlLang.split('-')[0].toLowerCase();
		}

		// Simple language detection based on common words
		const englishWords = ['the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'man', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy', 'did', 'its', 'let', 'put', 'say', 'she', 'too', 'use'];
		const spanishWords = ['que', 'de', 'no', 'a', 'la', 'el', 'es', 'y', 'en', 'lo', 'un', 'por', 'qué', 'me', 'una', 'te', 'los', 'se', 'con', 'para', 'mi', 'está', 'si', 'bien', 'pero', 'yo', 'eso', 'las', 'sí', 'su', 'tu', 'aquí', 'del', 'al', 'como', 'le', 'más', 'esto', 'ya', 'todo'];
		const frenchWords = ['le', 'de', 'et', 'à', 'un', 'il', 'être', 'et', 'en', 'avoir', 'que', 'pour', 'dans', 'ce', 'son', 'une', 'sur', 'avec', 'ne', 'se', 'pas', 'tout', 'plus', 'par', 'grand', 'en', 'une', 'être', 'et', 'en', 'avoir', 'que', 'pour'];

		const words = text.toLowerCase().split(/\s+/).slice(0, 200); // Check first 200 words

		let englishCount = 0;
		let spanishCount = 0;
		let frenchCount = 0;

		for (const word of words)
		{
			if (englishWords.includes(word)) englishCount++;
			if (spanishWords.includes(word)) spanishCount++;
			if (frenchWords.includes(word)) frenchCount++;
		}

		if (spanishCount > englishCount && spanishCount > frenchCount) return 'es';
		if (frenchCount > englishCount && frenchCount > spanishCount) return 'fr';

		return 'en'; // Default to English
	}

	/**
	 * Generate content hash for duplicate detection
	 */
	private generateContentHash(content: string): string
	{
		// Simple hash function for content fingerprinting
		let hash = 0;
		for (let i = 0; i < content.length; i++)
		{
			const char = content.charCodeAt(i);
			hash = ((hash << 5) - hash) + char;
			hash &= hash; // Convert to 32-bit integer
		}
		return hash.toString(36);
	}

	/**
	 * Calculate comprehensive content quality metrics
	 */
	private calculateContentQuality(pages: PageContent[]): ContentQualityMetrics
	{
		const totalWords = pages.reduce((sum, page) => sum + page.wordCount, 0);
		const avgReadability = pages.reduce((sum, page) => sum + page.readabilityScore, 0) / pages.length;

		// Content length score (optimal range: 300-2000 words per page)
		const avgWordsPerPage = totalWords / pages.length;
		const contentLengthScore = this.scoreContentLength(avgWordsPerPage);

		// Readability score (60-70 is optimal)
		const readabilityScore = this.scoreReadability(avgReadability);

		// Media richness (based on content structure)
		const mediaRichness = this.calculateMediaRichness(pages);

		// Structural quality (heading hierarchy, etc.)
		const structuralQuality = this.calculateStructuralQuality(pages);

		// Content uniqueness (detect duplicate content)
		const uniqueness = this.calculateContentUniqueness(pages);

		// Content freshness (based on last modified dates)
		const freshness = this.calculateContentFreshness(pages);

		const overallScore = (
			contentLengthScore * 0.2
			+ readabilityScore * 0.2
			+ mediaRichness * 0.15
			+ structuralQuality * 0.2
			+ uniqueness * 0.15
			+ freshness * 0.1
		);

		return {
			overallScore,
			contentLength: contentLengthScore,
			readabilityScore,
			mediaRichness,
			structuralQuality,
			uniqueness,
			freshness,
		};
	}

	/**
	 * Score content length
	 */
	private scoreContentLength(avgWords: number): number
	{
		if (avgWords < 100) return 0.2;
		if (avgWords < 300) return 0.5;
		if (avgWords < 500) return 0.7;
		if (avgWords <= 2000) return 1.0;
		return Math.max(0.6, 1.0 - (avgWords - 2000) / 5000);
	}

	/**
	 * Score readability
	 */
	private scoreReadability(fleschScore: number): number
	{
		// Optimal range: 60-70
		if (fleschScore >= 60 && fleschScore <= 70) return 1.0;
		return Math.max(0.3, 1.0 - Math.abs(fleschScore - 65) / 100);
	}

	/**
	 * Calculate media richness score
	 */
	private calculateMediaRichness(pages: PageContent[]): number
	{
		// This is a simplified calculation - in a real implementation,
		// you'd analyze actual media content from the HTML
		const hasVariedContent = pages.some(page => page.headings.length > 3);
		const hasDescriptions = pages.some(page => page.description.length > 50);

		let score = 0.5; // Base score
		if (hasVariedContent) score += 0.3;
		if (hasDescriptions) score += 0.2;

		return Math.min(1.0, score);
	}

	/**
	 * Calculate structural quality score
	 */
	private calculateStructuralQuality(pages: PageContent[]): number
	{
		let totalScore = 0;

		for (const page of pages)
		{
			let pageScore = 0;

			// Has proper heading structure
			if (page.headings.length > 0) pageScore += 0.3;
			if (page.headings.length > 2) pageScore += 0.2;

			// Has meta description
			if (page.description.length > 50) pageScore += 0.2;

			// Has title
			if (page.title.length > 10) pageScore += 0.3;

			totalScore += pageScore;
		}

		return totalScore / pages.length;
	}

	/**
	 * Calculate content uniqueness
	 */
	private calculateContentUniqueness(pages: PageContent[]): number
	{
		const hashes = pages.map(page => page.contentHash);
		const uniqueHashes = new Set(hashes);

		return uniqueHashes.size / hashes.length;
	}

	/**
	 * Calculate content freshness
	 */
	private calculateContentFreshness(pages: PageContent[]): number
	{
		const now = new Date();
		let totalFreshness = 0;
		let validDates = 0;

		for (const page of pages)
		{
			if (page.lastModified)
			{
				const lastMod = new Date(page.lastModified);
				const daysSince = (now.getTime() - lastMod.getTime()) / (1000 * 60 * 60 * 24);

				// Fresh content gets higher score
				let freshness = 1.0;
				if (daysSince > 30) freshness = 0.8;
				if (daysSince > 90) freshness = 0.6;
				if (daysSince > 365) freshness = 0.4;
				if (daysSince > 730) freshness = 0.2;

				totalFreshness += freshness;
				validDates++;
			}
		}

		return validDates > 0 ? totalFreshness / validDates : 0.5; // Default to neutral if no dates
	}

	/**
	 * Detect primary language across all pages
	 */
	private detectLanguage(pages: PageContent[]): AdvancedContentAnalysisResult['languageDetection']
	{
		const languageCounts: Record<string, number> = {};

		// Count language occurrences
		for (const page of pages)
		{
			languageCounts[page.language] = (languageCounts[page.language] || 0) + 1;
		}

		// Find primary language
		const sortedLanguages = Object.entries(languageCounts)
			.sort(([, a], [, b]) => b - a);

		const primary = sortedLanguages[0]?.[0] || 'en';
		const confidence = sortedLanguages[0]?.[1] / pages.length || 0;

		const alternatives = sortedLanguages.slice(1, 3).map(([language, count]) => ({
			language,
			confidence: count / pages.length,
		}));

		return {
			primary,
			confidence,
			alternatives,
		};
	}

	/**
	 * Calculate comprehensive content metrics
	 */
	private calculateContentMetrics(pages: PageContent[]): AdvancedContentAnalysisResult['contentMetrics']
	{
		const totalWordCount = pages.reduce((sum, page) => sum + page.wordCount, 0);
		const averageReadability = pages.reduce((sum, page) => sum + page.readabilityScore, 0) / pages.length;

		// Calculate duplicate content percentage
		const hashes = pages.map(page => page.contentHash);
		const uniqueHashes = new Set(hashes);
		const duplicateContentPercentage = ((hashes.length - uniqueHashes.size) / hashes.length) * 100;

		// Calculate heading structure score
		let totalHeadingScore = 0;
		for (const page of pages)
		{
			let pageHeadingScore = 0;
			if (page.headings.length > 0) pageHeadingScore += 0.5;
			if (page.headings.length > 2) pageHeadingScore += 0.3;
			if (page.headings.length > 5) pageHeadingScore += 0.2;
			totalHeadingScore += pageHeadingScore;
		}
		const headingStructureScore = totalHeadingScore / pages.length;

		return {
			totalWordCount,
			averageReadability,
			duplicateContentPercentage,
			mediaToTextRatio: 0.1, // Placeholder - would need actual media analysis
			headingStructureScore,
			internalLinkDensity: 0.05, // Placeholder - would need link analysis
		};
	}

	/**
	 * Analyze technical SEO aspects
	 */
	private analyzeTechnicalSEO(pages: PageContent[]): AdvancedContentAnalysisResult['technicalSEO']
	{
		const hasStructuredData = false;
		const structuredDataTypes: string[] = [];
		const hasCanonicalTags = false;
		let hasMetaDescriptions = false;
		const hasAltTags = false;

		// Check if pages have meta descriptions
		hasMetaDescriptions = pages.some(page => page.description.length > 0);

		// For a more complete implementation, you would need to:
		// - Parse HTML for structured data (JSON-LD, microdata)
		// - Check for canonical tags
		// - Analyze image alt tags
		// - Check image optimization

		return {
			hasStructuredData,
			structuredDataTypes,
			hasCanonicalTags,
			hasMetaDescriptions,
			hasAltTags,
			imageOptimization: 0.5, // Placeholder score
		};
	}
}

export type { AdvancedContentAnalysisResult, PageContent, ContentQualityMetrics };

export default AdvancedContentAnalyzer;
