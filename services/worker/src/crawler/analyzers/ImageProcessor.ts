import type { Logger } from '@shared';
import axios from 'axios';

interface ImageProcessingOptions
{
	format?: 'webp' | 'png' | 'jpeg';
	quality?: number;
	width?: number;
	height?: number;
	compression?: boolean;
	fallback?: boolean;
}

interface ProcessedImage
{
	url: string;
	originalUrl: string;
	format: string;
	size: number;
	width?: number;
	height?: number;
	optimized: boolean;
}

/**
 * Image processing service using weserv.nl for optimization and format conversion
 * Handles screenshots, favicons, and other domain-related images
 */
class ImageProcessor
{
	private readonly weservBaseUrl: string;

	private readonly logger: Logger;

	private readonly defaultOptions: ImageProcessingOptions;

	constructor(logger: Logger, weservUrl?: string)
	{
		this.logger = logger;
		this.weservBaseUrl = weservUrl || 'https://images.weserv.nl';
		this.defaultOptions = {
			format: 'webp',
			quality: 85,
			compression: true,
			fallback: true,
		};
	}

	/**
	 * Process screenshot images with WebP conversion and compression
	 */
	async processScreenshot(
		imageUrl: string,
		options: ImageProcessingOptions = {},
	): Promise<ProcessedImage>
	{
		const processingOptions = { ...this.defaultOptions, ...options };

		try
		{
			// For screenshots, prioritize WebP with high quality
			const webpOptions = {
				...processingOptions,
				format: 'webp' as const,
				quality: processingOptions.quality || 90,
			};

			const processedUrl = this.buildWeservUrl(imageUrl, webpOptions);
			const imageInfo = await this.getImageInfo(processedUrl);

			this.logger.info('Screenshot processed successfully', {
				originalUrl: imageUrl,
				processedUrl,
				format: 'webp',
				size: imageInfo.size,
			});

			return {
				url: processedUrl,
				originalUrl: imageUrl,
				format: 'webp',
				size: imageInfo.size,
				width: imageInfo.width,
				height: imageInfo.height,
				optimized: true,
			};
		}
		catch (error)
		{
			this.logger.error('Screenshot processing failed', {
				imageUrl,
				error: error instanceof Error ? error.message : 'Unknown error',
			});

			// Return original URL as fallback
			return {
				url: imageUrl,
				originalUrl: imageUrl,
				format: 'unknown',
				size: 0,
				optimized: false,
			};
		}
	}

	/**
	 * Process favicon images with PNG format and transparent WebP fallback
	 */
	async processFavicon(
		imageUrl: string,
		options: ImageProcessingOptions = {},
	): Promise<ProcessedImage[]>
	{
		const results: ProcessedImage[] = [];

		try
		{
			// Primary: PNG format for favicons (better transparency support)
			const pngOptions = {
				...this.defaultOptions,
				...options,
				format: 'png' as const,
				quality: 100, // PNG is lossless
			};

			const pngUrl = this.buildWeservUrl(imageUrl, pngOptions);
			const pngInfo = await this.getImageInfo(pngUrl);

			results.push({
				url: pngUrl,
				originalUrl: imageUrl,
				format: 'png',
				size: pngInfo.size,
				width: pngInfo.width,
				height: pngInfo.height,
				optimized: true,
			});

			// Fallback: WebP with transparency support (lossy but smaller)
			if (options.fallback !== false)
			{
				const webpOptions = {
					...this.defaultOptions,
					...options,
					format: 'webp' as const,
					quality: 85,
				};

				const webpUrl = this.buildWeservUrl(imageUrl, webpOptions);
				const webpInfo = await this.getImageInfo(webpUrl);

				results.push({
					url: webpUrl,
					originalUrl: imageUrl,
					format: 'webp',
					size: webpInfo.size,
					width: webpInfo.width,
					height: webpInfo.height,
					optimized: true,
				});
			}

			this.logger.info('Favicon processed successfully', {
				originalUrl: imageUrl,
				formats: results.map(r => r.format),
			});

			return results;
		}
		catch (error)
		{
			this.logger.error('Favicon processing failed', {
				imageUrl,
				error: error instanceof Error ? error.message : 'Unknown error',
			});

			// Return original URL as fallback
			return [{
				url: imageUrl,
				originalUrl: imageUrl,
				format: 'unknown',
				size: 0,
				optimized: false,
			}];
		}
	}

	/**
	 * Automatically select optimal format and processing based on image type
	 */
	async processImage(
		imageUrl: string,
		imageType: 'screenshot' | 'favicon' | 'content',
		options: ImageProcessingOptions = {},
	): Promise<ProcessedImage | ProcessedImage[]>
	{
		switch (imageType)
		{
			case 'screenshot':
				return this.processScreenshot(imageUrl, options);

			case 'favicon':
				return this.processFavicon(imageUrl, options);

			case 'content':
				return this.processContentImage(imageUrl, options);

			default:
				throw new Error(`Unsupported image type: ${imageType}`);
		}
	}

	/**
	 * Process general content images with automatic format selection
	 */
	private async processContentImage(
		imageUrl: string,
		options: ImageProcessingOptions = {},
	): Promise<ProcessedImage>
	{
		const processingOptions = { ...this.defaultOptions, ...options };

		try
		{
			// For content images, use WebP with moderate compression
			const webpOptions = {
				...processingOptions,
				format: 'webp' as const,
				quality: processingOptions.quality || 80,
			};

			const processedUrl = this.buildWeservUrl(imageUrl, webpOptions);
			const imageInfo = await this.getImageInfo(processedUrl);

			return {
				url: processedUrl,
				originalUrl: imageUrl,
				format: 'webp',
				size: imageInfo.size,
				width: imageInfo.width,
				height: imageInfo.height,
				optimized: true,
			};
		}
		catch (error)
		{
			this.logger.error('Content image processing failed', {
				imageUrl,
				error: error instanceof Error ? error.message : 'Unknown error',
			});

			return {
				url: imageUrl,
				originalUrl: imageUrl,
				format: 'unknown',
				size: 0,
				optimized: false,
			};
		}
	}

	/**
	 * Build weserv.nl URL with processing parameters
	 */
	private buildWeservUrl(imageUrl: string, options: ImageProcessingOptions): string
	{
		const params = new URLSearchParams();

		// Add image URL
		params.append('url', imageUrl);

		// Add format
		if (options.format)
		{
			params.append('output', options.format);
		}

		// Add quality
		if (options.quality && options.format !== 'png')
		{
			params.append('q', options.quality.toString());
		}

		// Add dimensions
		if (options.width)
		{
			params.append('w', options.width.toString());
		}

		if (options.height)
		{
			params.append('h', options.height.toString());
		}

		// Add compression settings
		if (options.compression && options.format === 'webp')
		{
			params.append('af', ''); // Enable auto-format optimization
		}

		return `${this.weservBaseUrl}/?${params.toString()}`;
	}

	/**
	 * Get image information (size, dimensions) from processed URL
	 */
	private async getImageInfo(imageUrl: string): Promise<{
		size: number;
		width?: number;
		height?: number;
	}>
	{
		const response = await axios.head(imageUrl, {
			timeout: 10000,
		});

		const size = parseInt(response.headers['content-length'] || '0', 10);

		// Try to get dimensions from headers if available
		const width = response.headers['x-image-width']
			? parseInt(response.headers['x-image-width'], 10)
			: undefined;
		const height = response.headers['x-image-height']
			? parseInt(response.headers['x-image-height'], 10)
			: undefined;

		return { size, width, height };
	}

	/**
	 * Validate if an image URL is processable
	 */
	async validateImageUrl(imageUrl: string): Promise<boolean>
	{
		try
		{
			const response = await axios.head(imageUrl, {
				timeout: 5000,
			});

			const contentType = response.headers['content-type'];
			const supportedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];

			return supportedTypes.some(type => contentType?.includes(type));
		}
		catch (error)
		{
			this.logger.debug('Image URL validation failed', {
				imageUrl,
				error: error instanceof Error ? error.message : 'Unknown error',
			});

			return false;
		}
	}

	/**
	 * Batch process multiple images
	 */
	async batchProcessImages(
		images: Array<{ url: string; type: 'screenshot' | 'favicon' | 'content' }>,
		options: ImageProcessingOptions = {},
	): Promise<Array<ProcessedImage | ProcessedImage[]>>
	{
		const results = await Promise.allSettled(
			images.map(async ({ url, type }) => this.processImage(url, type, options)),
		);

		return results.map((result, index) =>
		{
			if (result.status === 'fulfilled')
			{
				return result.value;
			}

			this.logger.error('Batch image processing failed', {
				imageUrl: images[index].url,
				error: result.reason,
			});

			return {
				url: images[index].url,
				originalUrl: images[index].url,
				format: 'unknown',
				size: 0,
				optimized: false,
			};
		});
	}
}

export { ImageProcessor };
export type { ImageProcessingOptions, ProcessedImage };
