import https from 'https';
import tls from 'tls';
import { logger } from '@shared/utils/Logger';

const sslLogger = logger.getLogger('SSLAnalyzer');

type SSLAnalysisResultType =
{
	domain: string;
	hasSSL: boolean;
	certificate?: {
		issuer: string;
		subject: string;
		validFrom: string;
		validTo: string;
		daysUntilExpiry: number;
		serialNumber: string;
		fingerprint: string;
		keySize: number;
		signatureAlgorithm: string;
	};
	grade: string;
	protocols: string[];
	cipherSuites: string[];
	vulnerabilities: string[];
	securityHeaders: {
		hsts: boolean;
		hstsMaxAge?: number;
		hstsIncludeSubdomains?: boolean;
	};
	lastAnalyzed: string;
	error?: string;
};

/**
 * SSL Certificate Analyzer
 * Analyzes SSL certificates and security configuration
 */
class SSLAnalyzer
{
	/**
	 * Analyze SSL certificate and security configuration
	 */
	async analyzeSSL(domain: string): Promise<SSLAnalysisResultType>
	{
		sslLogger.info(`Analyzing SSL for domain: ${domain}`);

		const result: SSLAnalysisResultType =
		{
			domain,
			hasSSL: false,
			grade: 'F',
			protocols: [],
			cipherSuites: [],
			vulnerabilities: [],
			securityHeaders: {
				hsts: false,
			},
			lastAnalyzed: new Date().toISOString(),
		};

		try
		{
			// Check SSL certificate
			const certificateInfo = await this.getCertificateInfo(domain);

			if (certificateInfo)
			{
				result.hasSSL = true;
				result.certificate = certificateInfo;

				// Calculate SSL grade based on certificate and configuration
				result.grade = this.calculateSSLGrade(certificateInfo);
			}

			// Check security headers
			result.securityHeaders = await this.checkSecurityHeaders(domain);

			// Check for common vulnerabilities
			result.vulnerabilities = await this.checkVulnerabilities(domain);

			sslLogger.info(`SSL analysis completed for domain: ${domain}`, {
				hasSSL: result.hasSSL,
				grade: result.grade,
			});

			return result;
		}
		catch (error)
		{
			sslLogger.error(`SSL analysis failed for domain: ${domain}:`, error);
			result.error = (error as Error).message;
			return result;
		}
	}

	/**
	 * Get SSL certificate information
	 */
	private async getCertificateInfo(domain: string): Promise<SSLAnalysisResultType['certificate'] | null>
	{
		return new Promise((resolve) =>
		{
			const options = {
				host: domain,
				port: 443,
				servername: domain,
				rejectUnauthorized: false,
			};

			const socket = tls.connect(options, () =>
			{
				const certificate = socket.getPeerCertificate(true);

				if (!certificate || Object.keys(certificate).length === 0)
				{
					socket.destroy();
					resolve(null);
					return;
				}

				const validFrom = new Date(certificate.valid_from);
				const validTo = new Date(certificate.valid_to);
				const now = new Date();
				const daysUntilExpiry = Math.floor((validTo.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

				const certInfo = {
					issuer: certificate.issuer?.CN || 'Unknown',
					subject: certificate.subject?.CN || domain,
					validFrom: validFrom.toISOString(),
					validTo: validTo.toISOString(),
					daysUntilExpiry,
					serialNumber: certificate.serialNumber || '',
					fingerprint: certificate.fingerprint || '',
					keySize: certificate.bits || 0,
					signatureAlgorithm: certificate.sigalg || 'Unknown',
				};

				socket.destroy();
				resolve(certInfo);
			});

			socket.on('error', (error) =>
			{
				sslLogger.debug(`SSL connection error for ${domain}:`, error.message);
				socket.destroy();
				resolve(null);
			});

			socket.setTimeout(10000, () =>
			{
				socket.destroy();
				resolve(null);
			});
		});
	}

	/**
	 * Calculate SSL grade based on certificate and configuration
	 */
	private calculateSSLGrade(certificate: NonNullable<SSLAnalysisResultType['certificate']>): string
	{
		let score = 100;

		// Certificate validity
		if (certificate.daysUntilExpiry < 0)
		{
			score -= 50; // Expired certificate
		}
		else if (certificate.daysUntilExpiry < 30)
		{
			score -= 20; // Expiring soon
		}

		// Key size
		if (certificate.keySize < 2048)
		{
			score -= 30; // Weak key
		}
		else if (certificate.keySize < 4096)
		{
			score -= 5; // Moderate key
		}

		// Signature algorithm
		if (certificate.signatureAlgorithm.toLowerCase().includes('sha1'))
		{
			score -= 20; // Weak signature algorithm
		}

		// Convert score to grade
		if (score >= 95) return 'A+';
		if (score >= 90) return 'A';
		if (score >= 80) return 'B';
		if (score >= 70) return 'C';
		if (score >= 60) return 'D';
		return 'F';
	}

	/**
	 * Check security headers
	 */
	private async checkSecurityHeaders(domain: string): Promise<SSLAnalysisResultType['securityHeaders']>
	{
		return new Promise((resolve) =>
		{
			const options = {
				hostname: domain,
				port: 443,
				path: '/',
				method: 'HEAD',
				timeout: 10000,
			};

			const req = https.request(options, (res) =>
			{
				const headers = res.headers;

				const securityHeaders = {
					hsts: false,
					hstsMaxAge: undefined as number | undefined,
					hstsIncludeSubdomains: undefined as boolean | undefined,
				};

				// Check HSTS header
				const hstsHeader = headers['strict-transport-security'];
				if (hstsHeader)
				{
					securityHeaders.hsts = true;

					// Parse max-age
					const maxAgeMatch = hstsHeader.match(/max-age=(\d+)/);
					if (maxAgeMatch)
					{
						securityHeaders.hstsMaxAge = parseInt(maxAgeMatch[1]);
					}

					// Check includeSubDomains
					securityHeaders.hstsIncludeSubdomains = hstsHeader.includes('includeSubDomains');
				}

				resolve(securityHeaders);
			});

			req.on('error', () =>
			{
				resolve({
					hsts: false,
				});
			});

			req.on('timeout', () =>
			{
				req.destroy();
				resolve({
					hsts: false,
				});
			});

			req.end();
		});
	}

	/**
	 * Check for common SSL vulnerabilities
	 */
	private async checkVulnerabilities(domain: string): Promise<string[]>
	{
		const vulnerabilities: string[] = [];

		try
		{
			// Check certificate chain and basic vulnerabilities
			const certificateInfo = await this.getCertificateInfo(domain);

			if (certificateInfo)
			{
				// Check for expired certificate
				if (certificateInfo.daysUntilExpiry < 0)
				{
					vulnerabilities.push('Certificate expired');
				}

				// Check for weak key size
				if (certificateInfo.keySize < 2048)
				{
					vulnerabilities.push('Weak key size (< 2048 bits)');
				}

				// Check for weak signature algorithm
				if (certificateInfo.signatureAlgorithm.toLowerCase().includes('sha1'))
				{
					vulnerabilities.push('Weak signature algorithm (SHA-1)');
				}

				// Check for self-signed certificate
				if (certificateInfo.issuer === certificateInfo.subject)
				{
					vulnerabilities.push('Self-signed certificate');
				}

				// Check certificate expiry warning
				if (certificateInfo.daysUntilExpiry > 0 && certificateInfo.daysUntilExpiry < 30)
				{
					vulnerabilities.push(`Certificate expires in ${certificateInfo.daysUntilExpiry} days`);
				}
			}

			// Check security headers
			const securityHeaders = await this.checkSecurityHeaders(domain);
			if (!securityHeaders.hsts)
			{
				vulnerabilities.push('Missing HSTS header');
			}
			else if (securityHeaders.hstsMaxAge && securityHeaders.hstsMaxAge < 31536000)
			{
				vulnerabilities.push('HSTS max-age too short (< 1 year)');
			}

			return vulnerabilities;
		}
		catch (error)
		{
			sslLogger.debug(`Vulnerability check failed for ${domain}:`, error);
			return vulnerabilities;
		}
	}

	/**
	 * Check if domain supports HTTPS
	 */
	async supportsHTTPS(domain: string): Promise<boolean>
	{
		try
		{
			const certificateInfo = await this.getCertificateInfo(domain);
			return certificateInfo !== null;
		}
		catch (error)
		{
			return false;
		}
	}

	/**
	 * Get SSL certificate grade explanation
	 */
	getGradeExplanation(grade: string): string
	{
		const explanations = {
			'A+': 'Excellent SSL configuration with perfect security',
			A: 'Strong SSL configuration with good security',
			B: 'Good SSL configuration with minor issues',
			C: 'Acceptable SSL configuration with some concerns',
			D: 'Poor SSL configuration with security issues',
			F: 'Failed SSL configuration with serious security problems',
		};

		return explanations[grade as keyof typeof explanations] || 'Unknown grade';
	}
}

export type { SSLAnalysisResultType };

export default SSLAnalyzer;
