/**
 * Configuration for Phase 1 Network and Infrastructure Analysis Analyzers
 */

type AnalyzerTimeoutsType =
{
	dns: number;
	robots: number;
	ssl: number;
	domainInfo: number;
};

type AnalyzerRetryConfigType =
{
	maxAttempts: number;
	baseDelay: number;
	maxDelay: number;
	backoffMultiplier: number;
};

type AnalyzerConfigType =
{
	timeouts: AnalyzerTimeoutsType;
	retries: AnalyzerRetryConfigType;
	dns: {
		servers: string[];
		timeout: number;
		cdnProviders: string[];
		dnsProviders: string[];
	};
	robots: {
		userAgents: string[];
		maxFileSize: number;
		followRedirects: number;
	};
	ssl: {
		connectionTimeout: number;
		certificateValidation: boolean;
		checkVulnerabilities: boolean;
	};
	domainInfo: {
		whoisTimeout: number;
		enableAPIFallback: boolean;
		apiEndpoint: string;
	};
};

const DEFAULT_ANALYZER_CONFIG: AnalyzerConfigType =
{
	timeouts: {
		dns: 15000,
		robots: 10000,
		ssl: 12000,
		domainInfo: 20000,
	},
	retries: {
		maxAttempts: 3,
		baseDelay: 1000,
		maxDelay: 30000,
		backoffMultiplier: 2,
	},
	dns: {
		servers: ['*******', '*******', '*******', '*******'],
		timeout: 5000,
		cdnProviders: [
			'cloudflare',
			'amazonaws.com',
			'fastly',
			'maxcdn',
			'keycdn',
			'stackpath',
			'bunnycdn',
			'jsdelivr',
		],
		dnsProviders: [
			'cloudflare.com',
			'amazonaws.com',
			'google.com',
			'azure.com',
			'digitalocean.com',
			'godaddy.com',
			'namecheap.com',
		],
	},
	robots: {
		userAgents: [
			'Mozilla/5.0 (compatible; DomainRankingBot/1.0)',
			'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
		],
		maxFileSize: 500000, // 500KB
		followRedirects: 3,
	},
	ssl: {
		connectionTimeout: 10000,
		certificateValidation: true,
		checkVulnerabilities: true,
	},
	domainInfo: {
		whoisTimeout: 15000,
		enableAPIFallback: true,
		apiEndpoint: 'https://whois.freeapi.app/api/whois',
	},
};

/**
 * Get analyzer configuration from environment variables with defaults
 */
export function getAnalyzerConfig(): AnalyzerConfigType
{
	return {
		timeouts: {
			dns: parseInt(process.env.DNS_TIMEOUT || '15000', 10),
			robots: parseInt(process.env.ROBOTS_TIMEOUT || '10000', 10),
			ssl: parseInt(process.env.SSL_TIMEOUT || '12000', 10),
			domainInfo: parseInt(process.env.DOMAIN_INFO_TIMEOUT || '20000', 10),
		},
		retries: {
			maxAttempts: parseInt(process.env.ANALYZER_MAX_RETRIES || '3', 10),
			baseDelay: parseInt(process.env.ANALYZER_BASE_DELAY || '1000', 10),
			maxDelay: parseInt(process.env.ANALYZER_MAX_DELAY || '30000', 10),
			backoffMultiplier: parseFloat(process.env.ANALYZER_BACKOFF_MULTIPLIER || '2'),
		},
		dns: {
			servers: process.env.DNS_SERVERS?.split(',') || DEFAULT_ANALYZER_CONFIG.dns.servers,
			timeout: parseInt(process.env.DNS_QUERY_TIMEOUT || '5000', 10),
			cdnProviders: process.env.CDN_PROVIDERS?.split(',') || DEFAULT_ANALYZER_CONFIG.dns.cdnProviders,
			dnsProviders: process.env.DNS_PROVIDERS?.split(',') || DEFAULT_ANALYZER_CONFIG.dns.dnsProviders,
		},
		robots: {
			userAgents: process.env.ROBOTS_USER_AGENTS?.split(',') || DEFAULT_ANALYZER_CONFIG.robots.userAgents,
			maxFileSize: parseInt(process.env.ROBOTS_MAX_FILE_SIZE || '500000', 10),
			followRedirects: parseInt(process.env.ROBOTS_FOLLOW_REDIRECTS || '3', 10),
		},
		ssl: {
			connectionTimeout: parseInt(process.env.SSL_CONNECTION_TIMEOUT || '10000', 10),
			certificateValidation: process.env.SSL_CERTIFICATE_VALIDATION !== 'false',
			checkVulnerabilities: process.env.SSL_CHECK_VULNERABILITIES !== 'false',
		},
		domainInfo: {
			whoisTimeout: parseInt(process.env.WHOIS_TIMEOUT || '15000', 10),
			enableAPIFallback: process.env.WHOIS_ENABLE_API_FALLBACK !== 'false',
			apiEndpoint: process.env.WHOIS_API_ENDPOINT || DEFAULT_ANALYZER_CONFIG.domainInfo.apiEndpoint,
		},
	};
}

export { DEFAULT_ANALYZER_CONFIG };

export default getAnalyzerConfig;
