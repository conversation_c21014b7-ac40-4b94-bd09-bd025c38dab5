# Crawler Core Systems and Orchestration Infrastructure

This directory contains the complete crawler core systems and orchestration infrastructure extracted from the original crawler service. It provides a comprehensive, modular framework for domain data collection with intelligent orchestration, selective collection, and robust error handling.

## Architecture Overview

The crawler infrastructure is built around a modular architecture with the following key components:

### Core Components

1. **DataCollectionModule** - Base class for all data collection modules
2. **ModuleRegistry** - Manages module registration, dependency resolution, and execution planning
3. **DataCollectionOrchestrator** - Provides intelligent, selective data collection with validation
4. **SelectiveDataCollectionService** - High-level API for collection profiles and batch processing

### Module Implementations

All modules from the original crawler service have been extracted and integrated:

- **DNSModule** - DNS records analysis
- **RobotsModule** - Robots.txt analysis
- **SSLModule** - SSL certificate analysis
- **HomepageModule** - Homepage content analysis
- **DomainInfoModule** - Domain information and WHOIS data
- **FaviconModule** - Favicon collection and processing
- **ScreenshotModule** - Screenshot capture with optimization
- **PerformanceModule** - Performance auditing and Core Web Vitals
- **AdvancedContentModule** - Multi-page content analysis
- **ImageProcessingModule** - Image optimization and processing
- **DomainDescriptionModule** - AI-powered domain descriptions

## Key Features

### Intelligent Orchestration

- **Dependency Resolution**: Automatic resolution of module dependencies
- **Parallel Execution**: Modules run in parallel groups based on dependencies
- **Resource Management**: Tracks memory, CPU, and network usage
- **Execution Planning**: Creates optimized execution plans for module collections

### Selective Data Collection

- **Collection Profiles**: Pre-defined profiles (quick, standard, complete, security, performance, visual, refresh)
- **Conditional Collection**: Only collect missing or stale data
- **Field-Level Control**: Specify required fields and skip conditions
- **Batch Processing**: Process multiple domains with controlled concurrency

### Data Quality and Validation

- **Quality Assessment**: Comprehensive data quality scoring for each module
- **Completeness Tracking**: Calculate data completeness percentages
- **Freshness Monitoring**: Track data age and staleness
- **Validation Recommendations**: Generate actionable recommendations

### Error Handling and Recovery

- **Retry Logic**: Configurable retry attempts with exponential backoff
- **Graceful Degradation**: Continue processing when individual modules fail
- **Circuit Breaker**: Prevent cascading failures
- **Recovery Strategies**: Automatic recovery for common failure scenarios

## Usage Examples

### Basic Module Registration and Execution

```typescript
import { ModuleRegistry, DNSModule, SSLModule } from "./crawler";

// Create registry and register modules
const registry = new ModuleRegistry();
registry.registerModule("dns", new DNSModule());
registry.registerModule("ssl", new SSLModule());

// Execute modules for a domain
const result = await registry.executeModules("example.com", ["dns", "ssl"]);
console.log(
  `Collected data for ${result.domain} with ${result.successCount} successful modules`
);
```

### Selective Data Collection with Orchestrator

```typescript
import {
  DataCollectionOrchestrator,
  DataCollectionRequest,
} from "./crawler/core";

const orchestrator = new DataCollectionOrchestrator(registry);

const request: DataCollectionRequest = {
  domain: "example.com",
  modules: ["dns", "ssl", "homepage"],
  priority: "high",
  selective: {
    onlyIfMissing: true,
    requiredFields: ["records.A", "hasSSL", "accessible"],
  },
};

const response = await orchestrator.collectData(request);
console.log(
  `Collection completed with ${
    response.validation.completenessScore * 100
  }% completeness`
);
```

### High-Level Collection Profiles

```typescript
import { SelectiveDataCollectionService } from "./crawler/core";

const service = new SelectiveDataCollectionService(registry);

// Use predefined profile
const response = await service.collectDomain("example.com", "standard");

// Batch processing
const batchResult = await service.collectBatch({
  domains: ["example.com", "test.com", "demo.org"],
  profile: "quick",
  concurrency: 5,
  onProgress: (completed, total, current) => {
    console.log(`Progress: ${completed}/${total} - Processing ${current}`);
  },
});
```

### Custom Collection Profile

```typescript
const customProfile = {
  name: "security-focused",
  description: "Security-focused domain analysis",
  modules: ["dns", "ssl", "robots", "homepage"],
  priority: "high",
  timeout: 45000,
  retryPolicy: {
    maxRetries: 2,
    backoffMultiplier: 1.8,
    maxBackoffDelay: 12000,
  },
  selective: {
    onlyIfMissing: true,
    requiredFields: ["hasSSL", "grade", "securityHeaders"],
    skipIfExists: [],
  },
};

service.setProfile(customProfile);
const result = await service.collectDomain("example.com", "security-focused");
```

## Module Configuration

Each module can be configured with specific parameters:

```typescript
const dnsModule = new DNSModule();
const config = dnsModule.getConfig();
// {
//   name: 'dns',
//   priority: 95,
//   timeout: 15000,
//   retryAttempts: 3,
//   retryDelay: 2000,
//   dependencies: []
// }
```

### Module Dependencies

Modules can specify dependencies that must be executed first:

```typescript
// SSL module depends on DNS module
const sslConfig = {
  name: "ssl",
  priority: 80,
  timeout: 12000,
  retryAttempts: 2,
  retryDelay: 1500,
  dependencies: ["dns"], // Must run after DNS
};
```

## Data Quality Assessment

The orchestrator provides comprehensive data quality assessment:

```typescript
const response = await orchestrator.collectData(request);

// Check data quality for each module
Object.entries(response.validation.dataQuality).forEach(([module, quality]) => {
  console.log(
    `${module}: Score ${quality.score}, Confidence ${quality.confidence}`
  );
  if (quality.issues.length > 0) {
    console.log(`Issues: ${quality.issues.join(", ")}`);
  }
});

// Overall completeness
console.log(
  `Overall completeness: ${response.validation.completenessScore * 100}%`
);
console.log(
  `Missing critical: ${response.validation.missingCritical.join(", ")}`
);
console.log(
  `Missing optional: ${response.validation.missingOptional.join(", ")}`
);
```

## Performance Monitoring

Track resource usage and performance metrics:

```typescript
const response = await orchestrator.collectData(request);

console.log("Resource Usage:", {
  memoryUsed: response.metadata.resourceUsage.memoryUsed,
  cpuTime: response.metadata.resourceUsage.cpuTime,
  networkRequests: response.metadata.resourceUsage.networkRequests,
  totalDuration: response.metadata.totalDuration,
});

// Module-specific execution times
Object.entries(response.execution.results).forEach(([module, result]) => {
  console.log(
    `${module}: ${result.executionTime}ms (${result.retryCount} retries)`
  );
});
```

## Error Handling Patterns

### Module-Level Error Handling

```typescript
class CustomModule extends DataCollectionModule<MyDataType> {
  protected async collect(domain: string): Promise<MyDataType> {
    try {
      // Your collection logic here
      return await this.performCollection(domain);
    } catch (error) {
      // Module-specific error handling
      this.logger.error(`Collection failed for ${domain}:`, error);
      throw error; // Re-throw to trigger retry logic
    }
  }
}
```

### Orchestrator-Level Error Handling

```typescript
try {
  const response = await orchestrator.collectData(request);

  // Check for partial failures
  if (response.execution.failureCount > 0) {
    console.log("Some modules failed:", response.execution.results);
  }

  // Process recommendations
  response.recommendations.forEach((rec) => {
    console.log(`${rec.type}: ${rec.reason} - ${rec.action}`);
  });
} catch (error) {
  console.error("Collection failed completely:", error);
}
```

## Testing

Comprehensive test suites are provided for all components:

```bash
# Run all crawler tests
npm test src/crawler

# Run specific component tests
npm test src/crawler/core
npm test src/crawler/modules
```

### Test Structure

- **Unit Tests**: Individual module and component testing
- **Integration Tests**: Cross-component interaction testing
- **Mock Implementations**: Comprehensive mocking for external dependencies

## Configuration Options

### Global Configuration

```typescript
// Environment variables
process.env.MAX_CONCURRENT_MODULES = "10";
process.env.DEFAULT_MODULE_TIMEOUT = "30000";
process.env.ENABLE_RESOURCE_MONITORING = "true";
```

### Module-Specific Configuration

```typescript
// Database clients for modules that need them
const redisClient = new RedisClientWrapper();
const scyllaClient = new ScyllaClient();

const faviconModule = new FaviconModule(redisClient, scyllaClient);
const screenshotModule = new ScreenshotModule(redisClient, scyllaClient);
```

## Best Practices

### Module Development

1. **Extend DataCollectionModule**: Always extend the base class
2. **Implement collect()**: Focus on the core collection logic
3. **Handle Errors Gracefully**: Let the base class handle retries
4. **Use Structured Logging**: Include domain and module context
5. **Validate Input/Output**: Ensure data quality and consistency

### Orchestration

1. **Use Collection Profiles**: Leverage predefined profiles when possible
2. **Monitor Resource Usage**: Track memory and CPU consumption
3. **Handle Partial Failures**: Design for resilience
4. **Implement Circuit Breakers**: Prevent cascading failures
5. **Cache Strategically**: Use selective collection to avoid redundant work

### Performance Optimization

1. **Parallel Execution**: Leverage dependency-based parallelization
2. **Batch Processing**: Use batch operations for multiple domains
3. **Resource Limits**: Set appropriate timeouts and concurrency limits
4. **Selective Collection**: Only collect what's needed
5. **Monitor and Tune**: Use metrics to optimize performance

## Integration with Worker Service

This crawler infrastructure integrates seamlessly with the worker service pipeline:

1. **Domain Processing Pipeline**: Crawler is the first phase
2. **Data Flow**: Crawler results feed into ranking and indexing
3. **Error Propagation**: Failures are handled gracefully
4. **Resource Coordination**: Shares resource limits with other components
5. **Monitoring Integration**: Metrics flow into worker-level monitoring

The extracted crawler core systems provide a robust, scalable foundation for domain data collection within the consolidated worker service architecture.
