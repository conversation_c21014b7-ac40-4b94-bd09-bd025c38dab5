#!/usr/bin/env tsx

/**
 * Screenshot Capture Service Demo
 *
 * This demo shows how to use the enhanced screenshot capture service
 * with browserless integration and WebP optimization in the worker service.
 */

import { Logger } from '@shared';
import ScreenshotAnalyzer from '../analyzers/ScreenshotAnalyzer';

const logger = Logger.getLogger('ScreenshotDemo');

async function demonstrateScreenshotCapture()
{
	logger.info('Starting Screenshot Capture Service Demo');

	try
	{
		// Initialize the screenshot analyzer
		const analyzer = new ScreenshotAnalyzer();

		// Test service availability
		logger.info('Checking service availability...');
		const capability = await analyzer.validateCapability();

		logger.info('Service Status:', {
			browserlessAvailable: capability.browserlessAvailable,
			imageProxyAvailable: capability.imageProxyAvailable,
			browserlessStatus: capability.browserlessStatus,
		});

		if (!capability.browserlessAvailable)
		{
			logger.warn('Browserless service is not available. Screenshots will fail.');
			logger.info('To run this demo, ensure browserless is running at: http://browserless:3000');
			return;
		}

		// Demo 1: Basic screenshot capture
		logger.info('\n=== Demo 1: Basic Screenshot Capture ===');
		const testDomain = 'example.com';

		logger.info(`Capturing screenshots for: ${testDomain}`);
		const result = await analyzer.captureScreenshots(testDomain);

		logger.info('Screenshot Results:', {
			domain: result.domain,
			captureTime: result.captureTime,
			desktopSuccess: !result.screenshots.desktop.error,
			mobileSuccess: !result.screenshots.mobile.error,
			desktopSize: result.screenshots.desktop.size,
			mobileSize: result.screenshots.mobile.size,
			desktopCompression: result.screenshots.desktop.compressionRatio,
			mobileCompression: result.screenshots.mobile.compressionRatio,
			optimizationEnabled: result.metadata.optimization.enabled,
		});

		// Demo 2: Custom screenshot capture
		logger.info('\n=== Demo 2: Custom Screenshot Capture ===');
		const customViewport = { width: 1366, height: 768 };
		const customOptions = {
			fullPage: true,
			quality: 95,
			format: 'jpeg' as const,
			timeout: 45000,
		};

		logger.info(`Capturing custom screenshot for: ${testDomain}`, {
			viewport: customViewport,
			options: customOptions,
		});

		const customResult = await analyzer.captureCustomScreenshot(
			testDomain,
			customViewport,
			customOptions,
		);

		logger.info('Custom Screenshot Results:', {
			success: !customResult.error,
			size: customResult.size,
			compressionRatio: customResult.compressionRatio,
			optimized: !!customResult.optimizedUrl,
		});

		// Demo 3: Batch screenshot capture
		logger.info('\n=== Demo 3: Batch Screenshot Capture ===');
		const testDomains = ['example.com', 'httpbin.org', 'jsonplaceholder.typicode.com'];

		logger.info(`Capturing screenshots for ${testDomains.length} domains...`);
		const batchResults = await analyzer.batchCaptureScreenshots(testDomains, {
			concurrency: 2,
		});

		const batchStats = {
			totalDomains: batchResults.length,
			successful: batchResults.filter(r => !r.error).length,
			failed: batchResults.filter(r => r.error).length,
			averageCaptureTime: Math.round(
				batchResults.reduce((sum, r) => sum + r.captureTime, 0) / batchResults.length,
			),
		};

		logger.info('Batch Screenshot Results:', batchStats);

		// Demo 4: Error handling
		logger.info('\n=== Demo 4: Error Handling ===');
		const invalidDomain = 'this-domain-does-not-exist-12345.invalid';

		logger.info(`Testing error handling with invalid domain: ${invalidDomain}`);
		const errorResult = await analyzer.captureScreenshots(invalidDomain);

		logger.info('Error Handling Results:', {
			domain: errorResult.domain,
			hasError: !!errorResult.error,
			desktopError: errorResult.screenshots.desktop.error,
			mobileError: errorResult.screenshots.mobile.error,
			captureTime: errorResult.captureTime,
		});

		logger.info('\n=== Screenshot Capture Service Demo Complete ===');
	}
	catch (error)
	{
		logger.error('Demo failed:', error);
	}
}

// Run the demo if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`)
{
	demonstrateScreenshotCapture()
		.then(() =>
		{
			logger.info('Demo completed successfully');
			process.exit(0);
		})
		.catch((error) =>
		{
			logger.error('Demo failed:', error);
			process.exit(1);
		});
}

export { demonstrateScreenshotCapture };
