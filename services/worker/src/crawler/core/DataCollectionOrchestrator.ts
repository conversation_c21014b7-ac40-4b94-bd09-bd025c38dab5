/* eslint-disable max-classes-per-file */
import type { DomainDescriptionInterface } from '@shared';
import { logger as sharedLogger, DomainDescriptionValidator } from '@shared';
import { ModuleRegistryType, ModuleExecutionResultType } from './ModuleRegistry';
import { DataCollectionModuleType, DataCollectionResultType } from './DataCollectionModule';

const logger = sharedLogger.getLogger('DataCollectionOrchestrator');

type DataCollectionRequestType =
{
	domain: string;
	modules: string[];
	priority: 'low' | 'medium' | 'high' | 'critical';
	timeout?: number;
	retryPolicy?: {
		maxRetries: number;
		backoffMultiplier: number;
		maxBackoffDelay: number;
	};
	dependencies?: Record<string, any>;
	selective?: {
		onlyIfMissing?: boolean;
		requiredFields?: string[];
		skipIfExists?: string[];
	};
};

type DataCollectionResponse =
{
	domain: string;
	request: DataCollectionRequestType;
	execution: ModuleExecutionResultType;
	validation: DataValidationResultType;
	recommendations: DataCollectionRecommendationType[];
	metadata: {
		startTime: Date;
		endTime: Date;
		totalDuration: number;
		resourceUsage: ResourceUsageMetricsType;
	};
};

type DataValidationResultType =
{
	isComplete: boolean;
	completenessScore: number;
	missingCritical: string[];
	missingOptional: string[];
	availableData: string[];
	dataQuality: Record<string, DataQualityMetricType>;
	recommendations: string[];
};

type DataQualityMetricType =
{
	score: number;
	issues: string[];
	confidence: number;
	freshness: number; // Age in hours
	completeness: number; // Percentage of expected fields present
};

type DataCollectionRecommendationType =
{
	type: 'retry' | 'skip' | 'prioritize' | 'defer' | 'optimize';
	module: string;
	reason: string;
	impact: 'low' | 'medium' | 'high';
	action: string;
};

type ResourceUsageMetricsType =
{
	memoryUsed: number;
	cpuTime: number;
	networkRequests: number;
	cacheHits: number;
	cacheMisses: number;
};

type OptimizedCollectionPlanType =
{
	domain: string;
	modulesToRun: string[];
	modulesToSkip: string[];
	optimizations: string[];
	existingData: Record<string, any>;
	estimatedDuration: number;
	resourceRequirements: ResourceRequirementsType;
};

type ResourceRequirementsType =
{
	memory: number; // MB
	cpu: number; // CPU units
	network: number; // Network requests
};

type ModuleHistoryStatsType =
{
	totalExecutions: number;
	successes: number;
	failures: number;
	successRate: number;
	failureRate: number;
	avgExecutionTime: number;
};

/**
 * Resource Monitor for tracking resource usage during data collection
 */
class ResourceMonitor
{
	private startTime: number = 0;

	private startMemory: number = 0;

	private networkRequests: number = 0;

	private cacheHits: number = 0;

	private cacheMisses: number = 0;

	startMonitoring(): void
	{
		this.startTime = Date.now();
		this.startMemory = process.memoryUsage().heapUsed;
		this.networkRequests = 0;
		this.cacheHits = 0;
		this.cacheMisses = 0;
	}

	stopMonitoring(): ResourceUsageMetricsType
	{
		const endTime = Date.now();
		const endMemory = process.memoryUsage().heapUsed;

		return {
			memoryUsed: Math.max(0, endMemory - this.startMemory),
			cpuTime: endTime - this.startTime,
			networkRequests: this.networkRequests,
			cacheHits: this.cacheHits,
			cacheMisses: this.cacheMisses,
		};
	}

	recordNetworkRequest(): void
	{
		this.networkRequests++;
	}

	recordCacheHit(): void
	{
		this.cacheHits++;
	}

	recordCacheMiss(): void
	{
		this.cacheMisses++;
	}
}

/**
 * Enhanced Data Collection Orchestrator
 * Provides intelligent, selective data collection with validation and optimization
 */
class DataCollectionOrchestrator
{
	private moduleRegistry: ModuleRegistryType;

	private dataCache: Map<string, any> = new Map();

	private executionHistory: Map<string, ModuleExecutionResultType[]> = new Map();

	private resourceMonitor: ResourceMonitor;

	constructor(moduleRegistry: ModuleRegistryType)
	{
		this.moduleRegistry = moduleRegistry;
		this.resourceMonitor = new ResourceMonitor();
	}

	/**
	 * Execute selective data collection based on request parameters
	 */
	async collectData(request: DataCollectionRequestType): Promise<DataCollectionResponse>
	{
		const startTime = new Date();
		logger.info(`Starting selective data collection for ${request.domain}`, {
			modules: request.modules,
			priority: request.priority,
			selective: request.selective,
		});

		try
		{
			// Start resource monitoring
			this.resourceMonitor.startMonitoring();

			// Analyze existing data and determine what needs to be collected
			const collectionPlan = await this.createCollectionPlan(request);

			// Execute the collection plan
			const execution = await this.executeCollectionPlan(collectionPlan);

			// Validate collected data
			const validation = await this.validateCollectedData(request, execution);

			// Generate recommendations for future collections
			const recommendations = await this.generateRecommendations(request, execution, validation);

			// Stop resource monitoring
			const resourceUsage = this.resourceMonitor.stopMonitoring();

			const endTime = new Date();
			const response: DataCollectionResponse = {
				domain: request.domain,
				request,
				execution,
				validation,
				recommendations,
				metadata: {
					startTime,
					endTime,
					totalDuration: endTime.getTime() - startTime.getTime(),
					resourceUsage,
				},
			};

			// Store execution history for future optimization
			this.storeExecutionHistory(request.domain, execution);

			logger.info(`Data collection completed for ${request.domain}`, {
				duration: response.metadata.totalDuration,
				completeness: validation.completenessScore,
				recommendations: recommendations.length,
			});

			return response;
		}
		catch (error)
		{
			logger.error(`Data collection failed for ${request.domain}:`, error);
			throw error;
		}
	}

	/**
	 * Create an optimized collection plan based on request and existing data
	 */
	private async createCollectionPlan(request: DataCollectionRequestType): Promise<OptimizedCollectionPlanType>
	{
		const existingData = await this.getExistingData(request.domain);
		const modulesToRun: string[] = [];
		const modulesToSkip: string[] = [];
		const optimizations: string[] = [];

		for (const moduleName of request.modules)
		{
			const shouldCollect = await this.shouldCollectModule(
				moduleName,
				request,
				existingData,
			);

			if (shouldCollect.collect)
			{
				modulesToRun.push(moduleName);
				if (shouldCollect.reason)
				{
					optimizations.push(`${moduleName}: ${shouldCollect.reason}`);
				}
			}
			else
			{
				modulesToSkip.push(moduleName);
				optimizations.push(`Skipping ${moduleName}: ${shouldCollect.reason}`);
			}
		}

		return ({
			domain: request.domain,
			modulesToRun,
			modulesToSkip,
			optimizations,
			existingData,
			estimatedDuration: this.estimateExecutionTime(modulesToRun),
			resourceRequirements: this.estimateResourceRequirements(modulesToRun),
		});
	}

	/**
	 * Determine if a specific module should be collected
	 */
	private async shouldCollectModule(
		moduleName: string,
		request: DataCollectionRequestType,
		existingData: Record<string, any>,
	): Promise<{ collect: boolean; reason?: string }>
	{
		const module = this.moduleRegistry.getModule(moduleName);
		if (!module)
		{
			return { collect: false, reason: 'Module not found' };
		}

		// Check if module data already exists and is fresh
		const existingModuleData = existingData[moduleName];
		if (existingModuleData && request.selective?.onlyIfMissing)
		{
			const dataAge = this.calculateDataAge(existingModuleData);
			const maxAge = this.getMaxDataAge(moduleName);

			if (dataAge < maxAge)
			{
				return { collect: false, reason: `Data is fresh (${dataAge}h old, max ${maxAge}h)` };
			}
		}

		// Check if required fields are missing
		if (request.selective?.requiredFields)
		{
			const missingFields = this.findMissingFields(
				existingModuleData,
				request.selective.requiredFields,
			);

			if (missingFields.length === 0)
			{
				return { collect: false, reason: 'All required fields present' };
			}
			return { collect: true, reason: `Missing fields: ${missingFields.join(', ')}` };
		}

		// Check skip conditions
		if (request.selective?.skipIfExists)
		{
			const hasSkipConditions = request.selective.skipIfExists.some(
				field => this.hasField(existingModuleData, field),
			);

			if (hasSkipConditions)
			{
				return ({ collect: false, reason: 'Skip conditions met' });
			}
		}

		// Default: collect if no specific conditions prevent it
		return ({ collect: true, reason: 'Standard collection' });
	}

	/**
	 * Execute the optimized collection plan
	 */
	private async executeCollectionPlan(
		plan: OptimizedCollectionPlanType,
	): Promise<ModuleExecutionResultType>
	{
		if (plan.modulesToRun.length === 0)
		{
			logger.info(`No modules to run for ${plan.domain}, using cached data`);

			return ({
				domain: plan.domain,
				results: {},
				totalExecutionTime: 0,
				successCount: 0,
				failureCount: 0,
				completeness: {
					percentage: 100,
					missing: [],
					available: plan.modulesToSkip,
				},
			});
		}

		logger.info(`Executing collection plan for ${plan.domain}`, {
			toRun: plan.modulesToRun,
			toSkip: plan.modulesToSkip,
			optimizations: plan.optimizations,
		});

		// Execute modules using the registry
		const result = await this.moduleRegistry.executeModules(plan.domain, plan.modulesToRun);

		// Merge with existing data if available
		if (plan.existingData && Object.keys(plan.existingData).length > 0)
		{
			result.completeness.available.push(...plan.modulesToSkip);
			result.completeness.percentage = Math.round((
				result.completeness.available.length
				/ (plan.modulesToRun.length + plan.modulesToSkip.length)
			) * 100);
		}

		return result;
	}

	/**
	 * Validate collected data quality and completeness
	 */
	private async validateCollectedData(
		request: DataCollectionRequestType,
		execution: ModuleExecutionResultType,
	): Promise<DataValidationResultType>
	{
		const allModules = request.modules;
		const availableData = execution.completeness.available;
		const missingData = execution.completeness.missing;

		// Categorize missing data by criticality
		const { critical, optional } = this.categorizeMissingData(missingData, request.priority);

		// Calculate data quality metrics for each available module
		const dataQuality: Record<string, DataQualityMetricType> = {};
		for (const moduleName of availableData)
		{
			const moduleResult = execution.results[moduleName];
			if (moduleResult?.success && moduleResult.data)
			{
				dataQuality[moduleName] = await this.assessDataQuality(moduleName, moduleResult.data);
			}
		}

		// Calculate overall completeness score
		const completenessScore = this.calculateCompletenessScore(
			availableData.length,
			allModules.length,
			critical.length,
			optional.length,
		);

		// Generate validation recommendations
		const recommendations = this.generateValidationRecommendations(
			critical,
			optional,
			dataQuality,
		);

		return ({
			isComplete: critical.length === 0,
			completenessScore,
			missingCritical: critical,
			missingOptional: optional,
			availableData,
			dataQuality,
			recommendations,
		});
	}

	/**
	 * Generate recommendations for future data collection optimization
	 */
	private async generateRecommendations(
		request: DataCollectionRequestType,
		execution: ModuleExecutionResultType,
		validation: DataValidationResultType,
	): Promise<DataCollectionRecommendationType[]>
	{
		const recommendations: DataCollectionRecommendationType[] = [];

		// Recommend retries for failed critical modules
		for (const criticalModule of validation.missingCritical)
		{
			const moduleResult = execution.results[criticalModule];
			if (moduleResult && !moduleResult.success)
			{
				recommendations.push({
					type: 'retry',
					module: criticalModule,
					reason: `Critical module failed: ${moduleResult.error}`,
					impact: 'high',
					action: `Retry ${criticalModule} with increased timeout or different approach`,
				});
			}
		}

		// Recommend skipping consistently failing optional modules
		for (const optionalModule of validation.missingOptional)
		{
			const history = this.getModuleHistory(request.domain, optionalModule);
			if (history && history.failureRate > 0.8)
			{
				recommendations.push({
					type: 'skip',
					module: optionalModule,
					reason: `High failure rate (${Math.round(history.failureRate * 100)}%)`,
					impact: 'low',
					action: `Consider skipping ${optionalModule} for this domain type`,
				});
			}
		}

		// Recommend prioritizing fast, high-value modules
		const fastModules = Object.entries(execution.results)
			.filter(([_, result]) => result.success && result.executionTime < 2000)
			.map(([name]) => name);

		if (fastModules.length > 0)
		{
			recommendations.push({
				type: 'prioritize',
				module: fastModules.join(', '),
				reason: 'Fast execution with good success rate',
				impact: 'medium',
				action: 'Run these modules first in future collections',
			});
		}

		// Recommend optimization for slow modules
		const slowModules = Object.entries(execution.results)
			.filter(([_, result]) => result.success && result.executionTime > 10000)
			.map(([name]) => name);

		if (slowModules.length > 0)
		{
			recommendations.push({
				type: 'optimize',
				module: slowModules.join(', '),
				reason: 'Slow execution time affecting overall performance',
				impact: 'medium',
				action: 'Consider timeout reduction or alternative collection methods',
			});
		}

		return recommendations;
	}

	/**
	 * Get existing data for a domain from cache or database
	 */
	private async getExistingData(domain: string): Promise<Record<string, any>>
	{
		// Check cache first
		const cacheKey = `existing_data:${domain}`;
		if (this.dataCache.has(cacheKey))
		{
			return this.dataCache.get(cacheKey);
		}

		// Implement database lookup for existing data
		const existingData = await this.lookupExistingData(domain);

		// Cache the result
		this.dataCache.set(cacheKey, existingData);

		return existingData;
	}

	/**
	 * Look up existing data from database
	 */
	private async lookupExistingData(domain: string): Promise<any>
	{
		try
		{
			// Check cache first
			const cacheKey = `domain:${domain}`;
			if (this.dataCache.has(cacheKey))
			{
				const cachedData = this.dataCache.get(cacheKey);
				if (cachedData && this.calculateDataAge(cachedData) < 24) // Less than 24 hours old
				{
					return cachedData;
				}
			}

			// Query from database (would be injected via constructor in production)
			// For now, check the execution history as a fallback
			const history = this.executionHistory.get(domain);
			if (history && history.length > 0)
			{
				const lastExecution = history[history.length - 1];
				if (lastExecution.successful && lastExecution.collectedData)
				{
					// Cache the data
					this.dataCache.set(cacheKey, lastExecution.collectedData);
					return lastExecution.collectedData;
				}
			}

			return {};
		}
		catch (error)
		{
			logger.warn(`Failed to lookup existing data for ${domain}:`, error);
			return {};
		}
	}

	/**
	 * Calculate age of data in hours
	 */
	private calculateDataAge(data: any): number
	{
		if (!data || !data.lastAnalyzed)
		{
			return Infinity;
		}

		const lastAnalyzed = new Date(data.lastAnalyzed);
		const now = new Date();
		return (now.getTime() - lastAnalyzed.getTime()) / (1000 * 60 * 60);
	}

	/**
	 * Get maximum acceptable age for module data
	 */
	private getMaxDataAge(moduleName: string): number
	{
		const maxAges: Record<string, number> = {
			dns: 24, // DNS records: 24 hours
			ssl: 24, // SSL certificates: 24 hours
			robots: 168, // Robots.txt: 1 week
			domainInfo: 720, // Domain info: 30 days
			homepage: 6, // Homepage: 6 hours
			favicon: 168, // Favicon: 1 week
		};

		return maxAges[moduleName] || 24; // Default: 24 hours
	}

	/**
	 * Find missing required fields in existing data
	 */
	private findMissingFields(data: any, requiredFields: string[]): string[]
	{
		if (!data)
		{
			return requiredFields;
		}

		return requiredFields.filter(field => !this.hasField(data, field));
	}

	/**
	 * Check if data has a specific field
	 */
	private hasField(data: any, field: string): boolean
	{
		if (!data) return false;

		const fieldParts = field.split('.');
		let current = data;

		for (const part of fieldParts)
		{
			if (current === null || current === undefined || !(part in current))
			{
				return false;
			}
			current = current[part];
		}

		return current !== null && current !== undefined;
	}

	/**
	 * Categorize missing data by criticality
	 */
	private categorizeMissingData(
		missingData: string[],
		priority: string,
	): { critical: string[]; optional: string[] }
	{
		const criticalModules: Record<string, string[]> = {
			critical: ['dns', 'robots', 'ssl', 'domainInfo'],
			high: ['dns', 'robots', 'ssl'],
			medium: ['dns', 'robots'],
			low: ['dns'],
		};

		const critical = missingData.filter(module => criticalModules[priority]?.includes(module));

		const optional = missingData.filter(module => !criticalModules[priority]?.includes(module));

		return { critical, optional };
	}

	/**
	 * Assess data quality for a specific module
	 */
	private async assessDataQuality(moduleName: string, data: any): Promise<DataQualityMetricType>
	{
		const qualityChecks = {
			dns: this.assessDNSQuality,
			ssl: this.assessSSLQuality,
			robots: this.assessRobotsQuality,
			domainInfo: this.assessDomainInfoQuality,
			homepage: this.assessHomepageQuality,
			favicon: this.assessFaviconQuality,
		};

		const assessor = qualityChecks[moduleName as keyof typeof qualityChecks];
		if (assessor)
		{
			return assessor.call(this, data);
		}

		// Default quality assessment
		return {
			score: 0.8,
			issues: [],
			confidence: 0.7,
			freshness: this.calculateDataAge(data),
			completeness: 0.8,
		};
	}

	/**
	 * DNS data quality assessment
	 */
	private assessDNSQuality(data: any): DataQualityMetricType
	{
		const issues: string[] = [];
		let score = 1.0;

		if (!data.records?.A?.length && !data.records?.AAAA?.length)
		{
			issues.push('No A or AAAA records found');
			score -= 0.5;
		}

		if (!data.records?.NS?.length)
		{
			issues.push('No NS records found');
			score -= 0.2;
		}

		if (data.errors?.length > 0)
		{
			issues.push(`${data.errors.length} DNS resolution errors`);
			score -= 0.1 * data.errors.length;
		}

		return {
			score: Math.max(0, score),
			issues,
			confidence: data.errors?.length > 0 ? 0.6 : 0.9,
			freshness: this.calculateDataAge(data),
			completeness: this.calculateFieldCompleteness(data, ['records.A', 'records.NS', 'records.MX']),
		};
	}

	/**
	 * SSL data quality assessment
	 */
	private assessSSLQuality(data: any): DataQualityMetricType
	{
		const issues: string[] = [];
		let score = 1.0;

		if (!data.hasSSL)
		{
			issues.push('No SSL certificate found');
			score -= 0.3;
		}

		if (data.certificate?.daysUntilExpiry < 30)
		{
			issues.push('Certificate expires soon');
			score -= 0.2;
		}

		if (data.vulnerabilities?.length > 0)
		{
			issues.push(`${data.vulnerabilities.length} security vulnerabilities`);
			score -= 0.1 * data.vulnerabilities.length;
		}

		return {
			score: Math.max(0, score),
			issues,
			confidence: data.hasSSL ? 0.9 : 0.7,
			freshness: this.calculateDataAge(data),
			completeness: this.calculateFieldCompleteness(data, ['hasSSL', 'grade', 'certificate']),
		};
	}

	/**
	 * Robots.txt data quality assessment
	 */
	private assessRobotsQuality(data: any): DataQualityMetricType
	{
		const issues: string[] = [];
		let score = 1.0;

		if (!data.accessible && data.exists)
		{
			issues.push('Robots.txt exists but not accessible');
			score -= 0.3;
		}

		if (!data.complianceStatus?.isCompliant)
		{
			issues.push('Robots.txt compliance issues');
			score -= 0.2;
		}

		return {
			score: Math.max(0, score),
			issues,
			confidence: data.accessible ? 0.9 : 0.6,
			freshness: this.calculateDataAge(data),
			completeness: this.calculateFieldCompleteness(data, ['exists', 'accessible', 'rules']),
		};
	}

	/**
	 * Domain info data quality assessment
	 */
	private assessDomainInfoQuality(data: any): DataQualityMetricType
	{
		const issues: string[] = [];
		let score = 1.0;

		if (!data.validation?.isValid)
		{
			issues.push('Domain validation failed');
			score -= 0.5;
		}

		if (!data.whoisData?.registrar)
		{
			issues.push('Missing registrar information');
			score -= 0.2;
		}

		if (data.whoisData?.daysUntilExpiry < 30)
		{
			issues.push('Domain expires soon');
			score -= 0.3;
		}

		return {
			score: Math.max(0, score),
			issues,
			confidence: data.validation?.isValid ? 0.9 : 0.5,
			freshness: this.calculateDataAge(data),
			completeness: this.calculateFieldCompleteness(data, ['validation', 'whoisData.registrar', 'whoisData.registrationDate']),
		};
	}

	/**
	 * Homepage data quality assessment
	 */
	private assessHomepageQuality(data: any): DataQualityMetricType
	{
		const issues: string[] = [];
		let score = 1.0;

		if (!data.accessible)
		{
			issues.push('Homepage not accessible');
			score -= 0.5;
		}

		if (!data.metaTags?.title)
		{
			issues.push('Missing page title');
			score -= 0.2;
		}

		if (!data.metaTags?.description)
		{
			issues.push('Missing meta description');
			score -= 0.1;
		}

		if (data.responseTime > 5000)
		{
			issues.push('Slow response time');
			score -= 0.2;
		}

		return {
			score: Math.max(0, score),
			issues,
			confidence: data.accessible ? 0.8 : 0.4,
			freshness: this.calculateDataAge(data),
			completeness: this.calculateFieldCompleteness(data, ['accessible', 'metaTags.title', 'metaTags.description']),
		};
	}

	/**
	 * Favicon data quality assessment
	 */
	private assessFaviconQuality(data: any): DataQualityMetricType
	{
		const issues: string[] = [];
		let score = 1.0;

		if (!data.faviconFound)
		{
			issues.push('No favicon found');
			score -= 0.3;
		}

		if (data.fallbackUsed)
		{
			issues.push('Using fallback favicon');
			score -= 0.1;
		}

		return {
			score: Math.max(0, score),
			issues,
			confidence: data.faviconFound ? 0.8 : 0.6,
			freshness: this.calculateDataAge(data),
			completeness: this.calculateFieldCompleteness(data, ['faviconFound', 'faviconUrl']),
		};
	}

	/**
	 * Calculate field completeness percentage
	 */
	private calculateFieldCompleteness(data: any, requiredFields: string[]): number
	{
		if (!requiredFields.length) return 1.0;

		const presentFields = requiredFields.filter(field => this.hasField(data, field));
		return presentFields.length / requiredFields.length;
	}

	/**
	 * Calculate overall completeness score
	 */
	private calculateCompletenessScore(
		available: number,
		total: number,
		criticalMissing: number,
		optionalMissing: number,
	): number
	{
		if (total === 0) return 1.0;

		const baseScore = available / total;
		const criticalPenalty = criticalMissing * 0.2; // 20% penalty per critical missing
		const optionalPenalty = optionalMissing * 0.05; // 5% penalty per optional missing

		return Math.max(0, baseScore - criticalPenalty - optionalPenalty);
	}

	/**
	 * Generate validation recommendations
	 */
	private generateValidationRecommendations(
		critical: string[],
		optional: string[],
		dataQuality: Record<string, DataQualityMetricType>,
	): string[]
	{
		const recommendations: string[] = [];

		if (critical.length > 0)
		{
			recommendations.push(`Critical data missing: ${critical.join(', ')}. Immediate retry recommended.`);
		}

		if (optional.length > 0)
		{
			recommendations.push(`Optional data missing: ${optional.join(', ')}. Consider retry if resources allow.`);
		}

		// Quality-based recommendations
		Object.entries(dataQuality).forEach(([module, quality]) =>
		{
			if (quality.score < 0.7)
			{
				recommendations.push(`${module} data quality is low (${Math.round(quality.score * 100)}%). Issues: ${quality.issues.join(', ')}`);
			}

			if (quality.freshness > 24)
			{
				recommendations.push(`${module} data is stale (${Math.round(quality.freshness)}h old). Consider refresh.`);
			}
		});

		return recommendations;
	}

	/**
	 * Estimate execution time for modules
	 */
	private estimateExecutionTime(modules: string[]): number
	{
		const estimatedTimes: Record<string, number> = {
			dns: 2000,
			ssl: 3000,
			robots: 1500,
			domainInfo: 4000,
			homepage: 5000,
			favicon: 2000,
		};

		return modules.reduce((total, module) => total + (estimatedTimes[module] || 3000), 0);
	}

	/**
	 * Estimate resource requirements for modules
	 */
	private estimateResourceRequirements(modules: string[]): ResourceRequirementsType
	{
		const requirements: Record<string, ResourceRequirementsType> =
		{
			dns: { memory: 10, cpu: 20, network: 5 },
			ssl: { memory: 15, cpu: 30, network: 10 },
			robots: { memory: 5, cpu: 10, network: 3 },
			domainInfo: { memory: 20, cpu: 40, network: 15 },
			homepage: { memory: 30, cpu: 50, network: 25 },
			favicon: { memory: 10, cpu: 15, network: 8 },
		};

		return modules.reduce(
			(total, module) =>
			{
				const req = requirements[module] || { memory: 15, cpu: 25, network: 10 };
				return {
					memory: total.memory + req.memory,
					cpu: total.cpu + req.cpu,
					network: total.network + req.network,
				};
			},
			{ memory: 0, cpu: 0, network: 0 },
		);
	}

	/**
	 * Store execution history for optimization
	 */
	private storeExecutionHistory(domain: string, execution: ModuleExecutionResultType): void
	{
		const history = this.executionHistory.get(domain) || [];
		history.push(execution);

		// Keep only last 10 executions
		if (history.length > 10)
		{
			history.shift();
		}

		this.executionHistory.set(domain, history);
	}

	/**
	 * Get module execution history for a domain
	 */
	private getModuleHistory(domain: string, moduleName: string): ModuleHistoryStatsType | null
	{
		const history = this.executionHistory.get(domain);
		if (!history || history.length === 0)
		{
			return null;
		}

		const moduleResults = history
			.map(exec => exec.results[moduleName])
			.filter(result => result !== undefined);

		if (moduleResults.length === 0)
		{
			return null;
		}

		const successes = moduleResults.filter(result => result.success).length;
		const failures = moduleResults.length - successes;
		const avgExecutionTime = moduleResults.reduce(
			(sum, result) => sum + result.executionTime, 0,
		) / moduleResults.length;

		return ({
			totalExecutions: moduleResults.length,
			successes,
			failures,
			successRate: successes / moduleResults.length,
			failureRate: failures / moduleResults.length,
			avgExecutionTime,
		});
	}
}

export type {
	DataCollectionRequestType,
	DataCollectionResponseType,
	DataValidationResultType,
	DataQualityMetricType,
	DataCollectionRecommendationType,
	ResourceUsageMetricsType,
};

export default DataCollectionOrchestrator;
