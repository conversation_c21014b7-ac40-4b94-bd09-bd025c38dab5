import { Logger } from '@shared';
import { DataCollectionRequestType, DataCollectionResponseType } from './DataCollectionOrchestrator';

const logger = Logger.getLogger('ExecutionHistoryTracker');

type ExecutionRecord =
{
	id: string;
	timestamp: Date;
	domain: string;
	operation: string;
	modules: string[];
	request: DataCollectionRequestType;
	response?: DataCollectionResponseType;
	status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
	startTime: Date;
	endTime?: Date;
	duration?: number;
	resourceUsage: ResourceUsageRecord;
	performance: PerformanceRecord;
	errors: ErrorRecord[];
	warnings: WarningRecord[];
	metadata: Record<string, any>;
};

type ResourceUsageRecord =
{
	peakMemoryMB: number;
	averageMemoryMB: number;
	peakCpuPercent: number;
	averageCpuPercent: number;
	networkRequests: number;
	networkBytesTransferred: number;
	diskReads: number;
	diskWrites: number;
	cacheHits: number;
	cacheMisses: number;
};

type PerformanceRecord =
{
	moduleExecutionTimes: Map<string, number>;
	moduleSuccessRates: Map<string, boolean>;
	dataQualityScores: Map<string, number>;
	completenessScore: number;
	overallEfficiency: number;
	bottlenecks: BottleneckRecord[];
};

type BottleneckRecord =
{
	type: 'cpu' | 'memory' | 'network' | 'disk' | 'external';
	module: string;
	description: string;
	impact: number; // 0-1
	duration: number;
};

type ErrorRecord =
{
	timestamp: Date;
	module: string;
	type: string;
	message: string;
	stack?: string;
	severity: 'low' | 'medium' | 'high' | 'critical';
	recoverable: boolean;
	retryCount: number;
};

type WarningRecord =
{
	timestamp: Date;
	module: string;
	type: string;
	message: string;
	impact: 'low' | 'medium' | 'high';
};

type ExecutionPattern =
{
	domain: string;
	operation: string;
	frequency: number;
	averageDuration: number;
	successRate: number;
	commonErrors: Map<string, number>;
	resourceProfile: ResourceProfile;
	seasonality: SeasonalityPattern;
	trends: TrendAnalysis;
};

type ResourceProfile =
{
	memoryPattern: 'constant' | 'linear' | 'exponential' | 'spiky';
	cpuPattern: 'constant' | 'linear' | 'exponential' | 'spiky';
	networkPattern: 'burst' | 'steady' | 'variable';
	predictability: number; // 0-1
};

type SeasonalityPattern =
{
	hourlyPattern: number[]; // 24 values for each hour
	dailyPattern: number[]; // 7 values for each day of week
	monthlyPattern: number[]; // 12 values for each month
	confidence: number;
};

type TrendAnalysis =
{
	durationTrend: 'improving' | 'degrading' | 'stable';
	successRateTrend: 'improving' | 'degrading' | 'stable';
	resourceUsageTrend: 'improving' | 'degrading' | 'stable';
	qualityTrend: 'improving' | 'degrading' | 'stable';
	confidence: number;
};

type HistoryQuery =
{
	domain?: string;
	operation?: string;
	modules?: string[];
	status?: ExecutionRecord['status'][];
	timeRange?: { start: Date; end: Date };
	limit?: number;
	sortBy?: 'timestamp' | 'duration' | 'success_rate' | 'quality';
	sortOrder?: 'asc' | 'desc';
};

type HistoryAnalytics =
{
	totalExecutions: number;
	successRate: number;
	averageDuration: number;
	averageQuality: number;
	resourceEfficiency: number;
	topErrors: Array<{ error: string; count: number; percentage: number }>;
	performanceTrends: PerformanceTrend[];
	recommendations: AnalyticsRecommendation[];
};

type PerformanceTrend =
{
	metric: string;
	trend: 'improving' | 'degrading' | 'stable';
	changeRate: number; // percentage change per time period
	confidence: number;
	timeframe: string;
};

type AnalyticsRecommendation =
{
	type: 'optimization' | 'investigation' | 'maintenance' | 'scaling';
	priority: 'low' | 'medium' | 'high' | 'critical';
	description: string;
	expectedBenefit: string;
	effort: 'low' | 'medium' | 'high';
	basedOn: string[];
};

/**
 * Advanced execution history tracker with analytics and pattern recognition
 */
class ExecutionHistoryTracker
{
	private records: Map<string, ExecutionRecord> = new Map();

	private patterns: Map<string, ExecutionPattern> = new Map();

	private maxRecords: number = 10000;

	private analysisInterval: number = 3600000; // 1 hour

	private analysisTimer: NodeJS.Timeout | null = null;

	private isAnalyzing: boolean = false;

	constructor(maxRecords?: number)
	{
		if (maxRecords)
		{
			this.maxRecords = maxRecords;
		}

		this.startPeriodicAnalysis();
	}

	/**
	 * Start tracking an execution
	 */
	startExecution(
		domain: string,
		operation: string,
		modules: string[],
		request: DataCollectionRequestType,
		metadata?: Record<string, any>,
	): string
	{
		const id = this.generateExecutionId();
		const record: ExecutionRecord = {
			id,
			timestamp: new Date(),
			domain,
			operation,
			modules,
			request,
			status: 'pending',
			startTime: new Date(),
			resourceUsage: this.createEmptyResourceUsage(),
			performance: this.createEmptyPerformance(),
			errors: [],
			warnings: [],
			metadata: metadata || {},
		};

		this.records.set(id, record);

		logger.debug(`Started tracking execution: ${id}`, {
			domain,
			operation,
			modules: modules.length,
		});

		return id;
	}

	/**
	 * Update execution status
	 */
	updateExecutionStatus(id: string, status: ExecutionRecord['status']): void
	{
		const record = this.records.get(id);
		if (!record)
		{
			logger.warn(`Execution record not found: ${id}`);
			return;
		}

		record.status = status;

		if (status === 'running' && record.status === 'pending')
		{
			record.startTime = new Date();
		}

		if (status === 'completed' || status === 'failed' || status === 'cancelled')
		{
			record.endTime = new Date();
			record.duration = record.endTime.getTime() - record.startTime.getTime();
		}

		logger.debug(`Updated execution status: ${id} -> ${status}`);
	}

	/**
	 * Complete execution tracking
	 */
	completeExecution(
		id: string,
		response: DataCollectionResponseType,
		resourceUsage: ResourceUsageRecord,
		performance: PerformanceRecord,
	): void
	{
		const record = this.records.get(id);
		if (!record)
		{
			logger.warn(`Execution record not found: ${id}`);
			return;
		}

		record.response = response;
		record.resourceUsage = resourceUsage;
		record.performance = performance;
		record.status = 'completed';
		record.endTime = new Date();
		record.duration = record.endTime.getTime() - record.startTime.getTime();

		// Update patterns
		this.updateExecutionPatterns(record);

		logger.info(`Completed execution tracking: ${id}`, {
			duration: record.duration,
			quality: performance.completenessScore,
			efficiency: performance.overallEfficiency,
		});

		// Cleanup old records if needed
		this.cleanupOldRecords();
	}

	/**
	 * Record execution error
	 */
	recordError(
		id: string,
		module: string,
		error: Error,
		severity: ErrorRecord['severity'] = 'medium',
		recoverable: boolean = true,
		retryCount: number = 0,
	): void
	{
		const record = this.records.get(id);
		if (!record)
		{
			logger.warn(`Execution record not found: ${id}`);
			return;
		}

		const errorRecord: ErrorRecord = {
			timestamp: new Date(),
			module,
			type: error.constructor.name,
			message: error.message,
			stack: error.stack,
			severity,
			recoverable,
			retryCount,
		};

		record.errors.push(errorRecord);

		logger.debug(`Recorded error for execution: ${id}`, {
			module,
			error: error.message,
			severity,
		});
	}

	/**
	 * Record execution warning
	 */
	recordWarning(
		id: string,
		module: string,
		message: string,
		impact: WarningRecord['impact'] = 'low',
	): void
	{
		const record = this.records.get(id);
		if (!record)
		{
			logger.warn(`Execution record not found: ${id}`);
			return;
		}

		const warningRecord: WarningRecord = {
			timestamp: new Date(),
			module,
			type: 'warning',
			message,
			impact,
		};

		record.warnings.push(warningRecord);

		logger.debug(`Recorded warning for execution: ${id}`, {
			module,
			message,
			impact,
		});
	}

	/**
	 * Query execution history
	 */
	queryHistory(query: HistoryQuery): ExecutionRecord[]
	{
		let results = Array.from(this.records.values());

		// Apply filters
		if (query.domain)
		{
			results = results.filter(record => record.domain === query.domain);
		}

		if (query.operation)
		{
			results = results.filter(record => record.operation === query.operation);
		}

		if (query.modules && query.modules.length > 0)
		{
			results = results.filter(record =>
				query.modules!.some(module => record.modules.includes(module)));
		}

		if (query.status && query.status.length > 0)
		{
			results = results.filter(record => query.status!.includes(record.status));
		}

		if (query.timeRange)
		{
			results = results.filter(record =>
				record.timestamp >= query.timeRange!.start &&
				record.timestamp <= query.timeRange!.end);
		}

		// Apply sorting
		if (query.sortBy)
		{
			results.sort((a, b) =>
			{
				let aValue: number;
				let bValue: number;

				switch (query.sortBy)
				{
					case 'timestamp':
						aValue = a.timestamp.getTime();
						bValue = b.timestamp.getTime();
						break;
					case 'duration':
						aValue = a.duration || 0;
						bValue = b.duration || 0;
						break;
					case 'success_rate':
						aValue = a.status === 'completed' ? 1 : 0;
						bValue = b.status === 'completed' ? 1 : 0;
						break;
					case 'quality':
						aValue = a.performance.completenessScore;
						bValue = b.performance.completenessScore;
						break;
					default:
						aValue = a.timestamp.getTime();
						bValue = b.timestamp.getTime();
				}

				return query.sortOrder === 'desc' ? bValue - aValue : aValue - bValue;
			});
		}

		// Apply limit
		if (query.limit && query.limit > 0)
		{
			results = results.slice(0, query.limit);
		}

		return results;
	}

	/**
	 * Get execution analytics
	 */
	getAnalytics(query?: HistoryQuery): HistoryAnalytics
	{
		const records = query ? this.queryHistory(query) : Array.from(this.records.values());

		if (records.length === 0)
		{
			return this.getEmptyAnalytics();
		}

		const completedRecords = records.filter(r => r.status === 'completed');
		const successRate = completedRecords.length / records.length;

		const totalDuration = completedRecords.reduce((sum, r) => sum + (r.duration || 0), 0);
		const averageDuration = totalDuration / Math.max(completedRecords.length, 1);

		const totalQuality = completedRecords.reduce((sum, r) => sum + r.performance.completenessScore, 0);
		const averageQuality = totalQuality / Math.max(completedRecords.length, 1);

		const totalEfficiency = completedRecords.reduce((sum, r) => sum + r.performance.overallEfficiency, 0);
		const resourceEfficiency = totalEfficiency / Math.max(completedRecords.length, 1);

		// Analyze errors
		const errorCounts = new Map<string, number>();
		for (const record of records)
		{
			for (const error of record.errors)
			{
				const key = `${error.module}: ${error.message}`;
				errorCounts.set(key, (errorCounts.get(key) || 0) + 1);
			}
		}

		const topErrors = Array.from(errorCounts.entries())
			.sort(([, a], [, b]) => b - a)
			.slice(0, 10)
			.map(([error, count]) => ({
				error,
				count,
				percentage: (count / records.length) * 100,
			}));

		// Analyze trends
		const performanceTrends = this.analyzePerformanceTrends(records);

		// Generate recommendations
		const recommendations = this.generateAnalyticsRecommendations(records, {
			successRate,
			averageDuration,
			averageQuality,
			resourceEfficiency,
		});

		return {
			totalExecutions: records.length,
			successRate,
			averageDuration,
			averageQuality,
			resourceEfficiency,
			topErrors,
			performanceTrends,
			recommendations,
		};
	}

	/**
	 * Get execution patterns for a domain/operation
	 */
	getExecutionPattern(domain: string, operation?: string): ExecutionPattern | null
	{
		const key = operation ? `${domain}:${operation}` : domain;
		return this.patterns.get(key) || null;
	}

	/**
	 * Get all execution patterns
	 */
	getAllPatterns(): ExecutionPattern[]
	{
		return Array.from(this.patterns.values());
	}

	/**
	 * Predict execution outcome based on historical patterns
	 */
	predictExecution(
		domain: string,
		operation: string,
		modules: string[],
	): ExecutionPrediction
	{
		const pattern = this.getExecutionPattern(domain, operation);
		const domainPattern = this.getExecutionPattern(domain);

		const basePattern = pattern || domainPattern;

		if (!basePattern)
		{
			return {
				expectedDuration: modules.length * 5000, // 5 seconds per module
				expectedSuccessRate: 0.8,
				expectedQuality: 0.7,
				expectedResourceUsage: this.getDefaultResourceUsage(),
				confidence: 0.3,
				basedOnSamples: 0,
			};
		}

		// Adjust predictions based on modules
		const moduleAdjustment = this.calculateModuleAdjustment(modules, basePattern);

		return {
			expectedDuration: basePattern.averageDuration * moduleAdjustment.durationMultiplier,
			expectedSuccessRate: Math.min(basePattern.successRate * moduleAdjustment.successMultiplier, 1.0),
			expectedQuality: 0.7 * moduleAdjustment.qualityMultiplier,
			expectedResourceUsage: this.adjustResourceUsage(basePattern.resourceProfile, moduleAdjustment),
			confidence: Math.min(basePattern.frequency / 10, 1.0), // Higher confidence with more samples
			basedOnSamples: basePattern.frequency,
		};
	}

	/**
	 * Export history data
	 */
	exportHistory(format: 'json' | 'csv' = 'json'): string
	{
		const records = Array.from(this.records.values());

		if (format === 'csv')
		{
			return this.exportToCSV(records);
		}

		return JSON.stringify(records, null, 2);
	}

	/**
	 * Import history data
	 */
	importHistory(data: string, format: 'json' | 'csv' = 'json'): number
	{
		let records: ExecutionRecord[];

		try
		{
			if (format === 'csv')
			{
				records = this.importFromCSV(data);
			}
			else
			{
				records = JSON.parse(data);
			}

			let imported = 0;
			for (const record of records)
			{
				// Validate record structure
				if (this.validateRecord(record))
				{
					this.records.set(record.id, record);
					imported++;
				}
			}

			// Rebuild patterns
			this.rebuildPatterns();

			logger.info(`Imported ${imported} execution records`);
			return imported;
		}
		catch (error)
		{
			logger.error('Failed to import history data:', error);
			return 0;
		}
	}

	/**
	 * Cleanup and shutdown
	 */
	shutdown(): void
	{
		if (this.analysisTimer)
		{
			clearInterval(this.analysisTimer);
			this.analysisTimer = null;
		}

		logger.info('Execution history tracker shut down');
	}

	/**
	 * Private helper methods
	 */
	private generateExecutionId(): string
	{
		return `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
	}

	private createEmptyResourceUsage(): ResourceUsageRecord
	{
		return {
			peakMemoryMB: 0,
			averageMemoryMB: 0,
			peakCpuPercent: 0,
			averageCpuPercent: 0,
			networkRequests: 0,
			networkBytesTransferred: 0,
			diskReads: 0,
			diskWrites: 0,
			cacheHits: 0,
			cacheMisses: 0,
		};
	}

	private createEmptyPerformance(): PerformanceRecord
	{
		return {
			moduleExecutionTimes: new Map(),
			moduleSuccessRates: new Map(),
			dataQualityScores: new Map(),
			completenessScore: 0,
			overallEfficiency: 0,
			bottlenecks: [],
		};
	}

	private updateExecutionPatterns(record: ExecutionRecord): void
	{
		if (record.status !== 'completed')
		{
			return;
		}

		const key = `${record.domain}:${record.operation}`;
		let pattern = this.patterns.get(key);

		if (!pattern)
		{
			pattern = {
				domain: record.domain,
				operation: record.operation,
				frequency: 0,
				averageDuration: 0,
				successRate: 0,
				commonErrors: new Map(),
				resourceProfile: {
					memoryPattern: 'constant',
					cpuPattern: 'constant',
					networkPattern: 'steady',
					predictability: 0.5,
				},
				seasonality: {
					hourlyPattern: new Array(24).fill(0),
					dailyPattern: new Array(7).fill(0),
					monthlyPattern: new Array(12).fill(0),
					confidence: 0,
				},
				trends: {
					durationTrend: 'stable',
					successRateTrend: 'stable',
					resourceUsageTrend: 'stable',
					qualityTrend: 'stable',
					confidence: 0,
				},
			};
		}

		// Update frequency
		pattern.frequency++;

		// Update average duration (exponential moving average)
		const alpha = 0.1; // Smoothing factor
		pattern.averageDuration = pattern.averageDuration * (1 - alpha) + (record.duration || 0) * alpha;

		// Update success rate
		pattern.successRate = pattern.successRate * (1 - alpha) + 1 * alpha; // This execution was successful

		// Update common errors
		for (const error of record.errors)
		{
			const errorKey = `${error.module}: ${error.type}`;
			pattern.commonErrors.set(errorKey, (pattern.commonErrors.get(errorKey) || 0) + 1);
		}

		// Update seasonality patterns
		const hour = record.timestamp.getHours();
		const day = record.timestamp.getDay();
		const month = record.timestamp.getMonth();

		pattern.seasonality.hourlyPattern[hour]++;
		pattern.seasonality.dailyPattern[day]++;
		pattern.seasonality.monthlyPattern[month]++;

		this.patterns.set(key, pattern);
	}

	private cleanupOldRecords(): void
	{
		if (this.records.size <= this.maxRecords)
		{
			return;
		}

		// Sort by timestamp and keep only the most recent records
		const sortedRecords = Array.from(this.records.entries())
			.sort(([, a], [, b]) => b.timestamp.getTime() - a.timestamp.getTime());

		const toKeep = sortedRecords.slice(0, this.maxRecords);
		const toRemove = sortedRecords.slice(this.maxRecords);

		this.records.clear();
		for (const [id, record] of toKeep)
		{
			this.records.set(id, record);
		}

		logger.debug(`Cleaned up ${toRemove.length} old execution records`);
	}

	private startPeriodicAnalysis(): void
	{
		this.analysisTimer = setInterval(() =>
		{
			if (!this.isAnalyzing)
			{
				this.performPeriodicAnalysis();
			}
		}, this.analysisInterval);
	}

	private async performPeriodicAnalysis(): Promise<void>
	{
		this.isAnalyzing = true;

		try
		{
			// Update trends for all patterns
			for (const pattern of this.patterns.values())
			{
				this.updatePatternTrends(pattern);
			}

			// Cleanup old patterns
			this.cleanupOldPatterns();

			logger.debug('Completed periodic execution analysis');
		}
		catch (error)
		{
			logger.error('Error during periodic analysis:', error);
		}
		finally
		{
			this.isAnalyzing = false;
		}
	}

	private updatePatternTrends(pattern: ExecutionPattern): void
	{
		// This would implement trend analysis logic
		// For now, just update confidence
		pattern.trends.confidence = Math.min(pattern.frequency / 100, 1.0);
	}

	private cleanupOldPatterns(): void
	{
		const cutoff = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 days

		for (const [key, pattern] of this.patterns.entries())
		{
			// Remove patterns with very low frequency and no recent activity
			if (pattern.frequency < 5)
			{
				this.patterns.delete(key);
			}
		}
	}

	private analyzePerformanceTrends(records: ExecutionRecord[]): PerformanceTrend[]
	{
		// Placeholder implementation
		return [
			{
				metric: 'duration',
				trend: 'stable',
				changeRate: 0,
				confidence: 0.7,
				timeframe: '30 days',
			},
		];
	}

	private generateAnalyticsRecommendations(
		records: ExecutionRecord[],
		metrics: { successRate: number; averageDuration: number; averageQuality: number; resourceEfficiency: number },
	): AnalyticsRecommendation[]
	{
		const recommendations: AnalyticsRecommendation[] = [];

		if (metrics.successRate < 0.8)
		{
			recommendations.push({
				type: 'investigation',
				priority: 'high',
				description: 'Success rate is below 80%. Investigate common failure patterns.',
				expectedBenefit: 'Improve reliability and reduce failed executions',
				effort: 'medium',
				basedOn: ['success_rate'],
			});
		}

		if (metrics.resourceEfficiency < 0.6)
		{
			recommendations.push({
				type: 'optimization',
				priority: 'medium',
				description: 'Resource efficiency is low. Consider optimizing resource usage.',
				expectedBenefit: 'Reduce resource consumption and improve performance',
				effort: 'high',
				basedOn: ['resource_efficiency'],
			});
		}

		return recommendations;
	}

	private getEmptyAnalytics(): HistoryAnalytics
	{
		return {
			totalExecutions: 0,
			successRate: 0,
			averageDuration: 0,
			averageQuality: 0,
			resourceEfficiency: 0,
			topErrors: [],
			performanceTrends: [],
			recommendations: [],
		};
	}

	private calculateModuleAdjustment(modules: string[], pattern: ExecutionPattern): ModuleAdjustment
	{
		// Placeholder implementation
		return {
			durationMultiplier: 1.0,
			successMultiplier: 1.0,
			qualityMultiplier: 1.0,
		};
	}

	private adjustResourceUsage(profile: ResourceProfile, adjustment: ModuleAdjustment): ResourceUsageRecord
	{
		// Placeholder implementation
		return this.getDefaultResourceUsage();
	}

	private getDefaultResourceUsage(): ResourceUsageRecord
	{
		return {
			peakMemoryMB: 100,
			averageMemoryMB: 50,
			peakCpuPercent: 70,
			averageCpuPercent: 30,
			networkRequests: 20,
			networkBytesTransferred: 1024 * 1024, // 1MB
			diskReads: 10,
			diskWrites: 5,
			cacheHits: 15,
			cacheMisses: 5,
		};
	}

	private exportToCSV(records: ExecutionRecord[]): string
	{
		// Placeholder implementation
		return 'id,timestamp,domain,operation,status,duration\n';
	}

	private importFromCSV(data: string): ExecutionRecord[]
	{
		// Placeholder implementation
		return [];
	}

	private validateRecord(record: any): record is ExecutionRecord
	{
		return record &&
			typeof record.id === 'string' &&
			record.timestamp &&
			typeof record.domain === 'string' &&
			typeof record.operation === 'string';
	}

	private rebuildPatterns(): void
	{
		this.patterns.clear();
		for (const record of this.records.values())
		{
			if (record.status === 'completed')
			{
				this.updateExecutionPatterns(record);
			}
		}
	}
}

// Supporting interfaces
type ExecutionPrediction =
{
	expectedDuration: number;
	expectedSuccessRate: number;
	expectedQuality: number;
	expectedResourceUsage: ResourceUsageRecord;
	confidence: number;
	basedOnSamples: number;
};

type ModuleAdjustment =
{
	durationMultiplier: number;
	successMultiplier: number;
	qualityMultiplier: number;
};

export type {
	ExecutionRecord,
	ResourceUsageRecord,
	PerformanceRecord,
	BottleneckRecord,
	ErrorRecord,
	WarningRecord,
	ExecutionPattern,
	HistoryQuery,
	HistoryAnalytics,
	ExecutionPrediction,
};

export default ExecutionHistoryTracker;
