import { Logger } from '@shared';
import { DataCollectionOrchestrator, DataCollectionRequestType, DataCollectionResponseType } from './DataCollectionOrchestrator';
import { ModuleRegistry } from './ModuleRegistry';

const logger = Logger.getLogger('SelectiveDataCollectionService');

type CollectionProfileType =
{
	name: string;
	description: string;
	modules: string[];
	priority: 'low' | 'medium' | 'high' | 'critical';
	timeout: number;
	retryPolicy: {
		maxRetries: number;
		backoffMultiplier: number;
		maxBackoffDelay: number;
	};
	selective: {
		onlyIfMissing: boolean;
		requiredFields: string[];
		skipIfExists: string[];
	};
};

type BatchCollectionRequestType =
{
	domains: string[];
	profile: string | CollectionProfileType;
	concurrency?: number;
	priority?: 'low' | 'medium' | 'high' | 'critical';
	onProgress?: (completed: number, total: number, current: string) => void;
	onError?: (domain: string, error: Error) => void;
};

type BatchCollectionResultType =
{
	totalDomains: number;
	successful: number;
	failed: number;
	results: Map<string, DataCollectionResponseType>;
	errors: Map<string, Error>;
	summary: {
		totalDuration: number;
		avgDurationPerDomain: number;
		avgCompletenessScore: number;
		mostCommonIssues: string[];
		resourceUsage: {
			totalMemory: number;
			totalCpuTime: number;
			totalNetworkRequests: number;
		};
	};
};

/**
 * Selective Data Collection Service
 * Provides high-level APIs for intelligent, selective data collection
 */
class SelectiveDataCollectionService
{
	private orchestrator: DataCollectionOrchestrator;

	private profiles: Map<string, CollectionProfileType> = new Map();

	constructor(moduleRegistry: ModuleRegistry)
	{
		this.orchestrator = new DataCollectionOrchestrator(moduleRegistry);
		this.initializeDefaultProfiles();
	}

	/**
	 * Initialize default collection profiles
	 */
	private initializeDefaultProfiles(): void
	{
		// Quick profile - essential data only
		this.profiles.set('quick', {
			name: 'quick',
			description: 'Quick collection of essential domain data',
			modules: ['dns', 'robots'],
			priority: 'medium',
			timeout: 10000,
			retryPolicy: {
				maxRetries: 1,
				backoffMultiplier: 1.5,
				maxBackoffDelay: 5000,
			},
			selective: {
				onlyIfMissing: true,
				requiredFields: ['records.A', 'exists'],
				skipIfExists: [],
			},
		});

		// Standard profile - comprehensive basic analysis
		this.profiles.set('standard', {
			name: 'standard',
			description: 'Standard domain analysis with core modules',
			modules: ['dns', 'robots', 'ssl', 'domainInfo'],
			priority: 'medium',
			timeout: 30000,
			retryPolicy: {
				maxRetries: 2,
				backoffMultiplier: 2.0,
				maxBackoffDelay: 10000,
			},
			selective: {
				onlyIfMissing: true,
				requiredFields: ['records.A', 'exists', 'hasSSL', 'validation.isValid'],
				skipIfExists: [],
			},
		});

		// Complete profile - full analysis
		this.profiles.set('complete', {
			name: 'complete',
			description: 'Complete domain analysis with all available modules',
			modules: ['dns', 'robots', 'ssl', 'domainInfo', 'homepage', 'favicon', 'screenshot', 'performance'],
			priority: 'high',
			timeout: 150000, // Extended timeout for performance audits and screenshots
			retryPolicy: {
				maxRetries: 3,
				backoffMultiplier: 2.0,
				maxBackoffDelay: 20000,
			},
			selective: {
				onlyIfMissing: false,
				requiredFields: [],
				skipIfExists: [],
			},
		});

		// Security-focused profile
		this.profiles.set('security', {
			name: 'security',
			description: 'Security-focused analysis',
			modules: ['dns', 'ssl', 'robots', 'homepage'],
			priority: 'high',
			timeout: 45000,
			retryPolicy: {
				maxRetries: 2,
				backoffMultiplier: 1.8,
				maxBackoffDelay: 12000,
			},
			selective: {
				onlyIfMissing: true,
				requiredFields: ['hasSSL', 'grade', 'securityHeaders', 'complianceStatus'],
				skipIfExists: [],
			},
		});

		// Performance-focused profile
		this.profiles.set('performance', {
			name: 'performance',
			description: 'Performance-focused analysis with Core Web Vitals',
			modules: ['dns', 'homepage', 'ssl', 'performance'],
			priority: 'high',
			timeout: 120000, // Extended timeout for performance audits
			retryPolicy: {
				maxRetries: 2,
				backoffMultiplier: 2.0,
				maxBackoffDelay: 15000,
			},
			selective: {
				onlyIfMissing: true,
				requiredFields: ['responseTime', 'performance.loadTime', 'performance.lcp', 'performance.cls', 'performance.fid'],
				skipIfExists: [],
			},
		});

		// Visual profile - focused on visual analysis with screenshots
		this.profiles.set('visual', {
			name: 'visual',
			description: 'Visual analysis with screenshot capture',
			modules: ['dns', 'robots', 'ssl', 'homepage', 'screenshot'],
			priority: 'high',
			timeout: 75000, // Extended timeout for screenshot capture
			retryPolicy: {
				maxRetries: 2,
				backoffMultiplier: 2.0,
				maxBackoffDelay: 12000,
			},
			selective: {
				onlyIfMissing: true,
				requiredFields: ['screenshots.desktop.url', 'screenshots.mobile.url'],
				skipIfExists: [],
			},
		});

		// Refresh profile - update existing data
		this.profiles.set('refresh', {
			name: 'refresh',
			description: 'Refresh existing domain data',
			modules: ['dns', 'robots', 'ssl', 'domainInfo', 'homepage', 'favicon'],
			priority: 'low',
			timeout: 45000,
			retryPolicy: {
				maxRetries: 1,
				backoffMultiplier: 1.2,
				maxBackoffDelay: 5000,
			},
			selective: {
				onlyIfMissing: false,
				requiredFields: [],
				skipIfExists: [],
			},
		});

		logger.info('Initialized collection profiles', {
			profiles: Array.from(this.profiles.keys()),
		});
	}

	/**
	 * Collect data for a single domain using a profile
	 */
	async collectDomain(
		domain: string,
		profileName: string,
		options?: Partial<DataCollectionRequestType>,
	): Promise<DataCollectionResponseType>
	{
		const profile = this.profiles.get(profileName);
		if (!profile)
		{
			throw new Error(`Collection profile '${profileName}' not found`);
		}

		const request: DataCollectionRequestType = {
			domain,
			modules: profile.modules,
			priority: profile.priority,
			timeout: profile.timeout,
			retryPolicy: profile.retryPolicy,
			selective: profile.selective,
			...options,
		};

		logger.info(`Starting selective data collection for ${domain}`, {
			profile: profileName,
			modules: request.modules,
		});

		return this.orchestrator.collectData(request);
	}

	/**
	 * Collect data for multiple domains in batch
	 */
	async collectBatch(request: BatchCollectionRequestType): Promise<BatchCollectionResultType>
	{
		const startTime = Date.now();
		const results = new Map<string, DataCollectionResponseType>();
		const errors = new Map<string, Error>();
		const concurrency = request.concurrency || 5;

		logger.info(`Starting batch collection for ${request.domains.length} domains`, {
			profile: typeof request.profile === 'string' ? request.profile : request.profile.name,
			concurrency,
		});

		// Process domains in batches with controlled concurrency
		const batches = this.createBatches(request.domains, concurrency);
		let completed = 0;

		for (const batch of batches)
		{
			const batchPromises = batch.map(async (domain) =>
			{
				try
				{
					const result = await this.collectDomain(
						domain,
						typeof request.profile === 'string' ? request.profile : request.profile.name,
						typeof request.profile === 'object' ? {
							modules: request.profile.modules,
							priority: request.profile.priority,
							timeout: request.profile.timeout,
							retryPolicy: request.profile.retryPolicy,
							selective: request.profile.selective,
						} : undefined,
					);

					results.set(domain, result);
					completed++;

					if (request.onProgress)
					{
						request.onProgress(completed, request.domains.length, domain);
					}

					logger.debug(`Completed collection for ${domain}`, {
						completeness: result.validation.completenessScore,
						duration: result.metadata.totalDuration,
					});
				}
				catch (error)
				{
					const err = error instanceof Error ? error : new Error(String(error));
					errors.set(domain, err);
					completed++;

					if (request.onError)
					{
						request.onError(domain, err);
					}

					logger.warn(`Failed collection for ${domain}:`, err.message);
				}
			});

			// Wait for current batch to complete before starting next
			await Promise.allSettled(batchPromises);
		}

		const totalDuration = Date.now() - startTime;
		const summary = this.generateBatchSummary(results, errors, totalDuration);

		logger.info('Batch collection completed', {
			total: request.domains.length,
			successful: results.size,
			failed: errors.size,
			duration: totalDuration,
			avgCompleteness: summary.avgCompletenessScore,
		});

		return {
			totalDomains: request.domains.length,
			successful: results.size,
			failed: errors.size,
			results,
			errors,
			summary,
		};
	}

	/**
	 * Collect only missing data for a domain
	 */
	async collectMissingData(
		domain: string,
		requiredModules: string[],
		options?: {
			priority?: 'low' | 'medium' | 'high' | 'critical';
			timeout?: number;
			requiredFields?: string[];
		},
	): Promise<DataCollectionResponseType>
	{
		const request: DataCollectionRequestType = {
			domain,
			modules: requiredModules,
			priority: options?.priority || 'medium',
			timeout: options?.timeout || 30000,
			retryPolicy: {
				maxRetries: 2,
				backoffMultiplier: 1.5,
				maxBackoffDelay: 10000,
			},
			selective: {
				onlyIfMissing: true,
				requiredFields: options?.requiredFields || [],
				skipIfExists: [],
			},
		};

		logger.info(`Collecting missing data for ${domain}`, {
			modules: requiredModules,
			requiredFields: options?.requiredFields,
		});

		return this.orchestrator.collectData(request);
	}

	/**
	 * Refresh stale data for a domain
	 */
	async refreshStaleData(
		domain: string,
		maxAgeHours: number = 24,
		options?: {
			priority?: 'low' | 'medium' | 'high' | 'critical';
			modules?: string[];
		},
	): Promise<DataCollectionResponseType>
	{
		const modules = options?.modules || ['dns', 'robots', 'ssl', 'domainInfo', 'homepage', 'favicon'];

		const request: DataCollectionRequestType = {
			domain,
			modules,
			priority: options?.priority || 'low',
			timeout: 45000,
			retryPolicy: {
				maxRetries: 1,
				backoffMultiplier: 1.2,
				maxBackoffDelay: 5000,
			},
			selective: {
				onlyIfMissing: false,
				requiredFields: [],
				skipIfExists: [],
			},
		};

		logger.info(`Refreshing stale data for ${domain}`, {
			maxAge: maxAgeHours,
			modules,
		});

		return this.orchestrator.collectData(request);
	}

	/**
	 * Get available collection profiles
	 */
	getProfiles(): CollectionProfileType[]
	{
		return Array.from(this.profiles.values());
	}

	/**
	 * Add or update a collection profile
	 */
	setProfile(profile: CollectionProfileType): void
	{
		this.profiles.set(profile.name, profile);
		logger.info(`Updated collection profile: ${profile.name}`);
	}

	/**
	 * Remove a collection profile
	 */
	removeProfile(name: string): boolean
	{
		const removed = this.profiles.delete(name);
		if (removed)
		{
			logger.info(`Removed collection profile: ${name}`);
		}
		return removed;
	}

	/**
	 * Analyze domain data completeness without collecting
	 */
	async analyzeDomainCompleteness(
		domain: string,
		requiredModules: string[],
	): Promise<{
		completeness: number;
		missing: string[];
		stale: string[];
		recommendations: string[];
	}>
	{
		// This would typically check existing data in the database
		// For now, return a mock analysis
		logger.info(`Analyzing data completeness for ${domain}`, {
			requiredModules,
		});

		return {
			completeness: 0.75,
			missing: ['favicon'],
			stale: ['homepage'],
			recommendations: [
				'Collect missing favicon data',
				'Refresh stale homepage data (>24h old)',
			],
		};
	}

	/**
	 * Get collection statistics
	 */
	getCollectionStats(): {
		profileUsage: Record<string, number>;
		avgCompleteness: number;
		commonIssues: string[];
		performanceMetrics: {
			avgDuration: number;
			successRate: number;
			resourceEfficiency: number;
		};
	}
	{
		// This would typically aggregate from stored execution history
		// For now, return mock statistics
		return {
			profileUsage: {
				standard: 45,
				quick: 30,
				complete: 15,
				security: 7,
				performance: 3,
			},
			avgCompleteness: 0.82,
			commonIssues: [
				'SSL certificate issues',
				'Slow homepage response',
				'Missing favicon',
				'DNS resolution timeouts',
			],
			performanceMetrics: {
				avgDuration: 12500,
				successRate: 0.87,
				resourceEfficiency: 0.73,
			},
		};
	}

	/**
	 * Create batches for concurrent processing
	 */
	private createBatches<T>(items: T[], batchSize: number): T[][]
	{
		const batches: T[][] = [];
		for (let i = 0; i < items.length; i += batchSize)
		{
			batches.push(items.slice(i, i + batchSize));
		}
		return batches;
	}

	/**
	 * Generate summary for batch collection results
	 */
	private generateBatchSummary(
		results: Map<string, DataCollectionResponseType>,
		errors: Map<string, Error>,
		totalDuration: number,
	): BatchCollectionResultType['summary']
	{
		const successfulResults = Array.from(results.values());

		// Calculate averages
		const avgDurationPerDomain = successfulResults.length > 0
			? successfulResults.reduce((sum, result) => sum + result.metadata.totalDuration, 0) / successfulResults.length
			: 0;

		const avgCompletenessScore = successfulResults.length > 0
			? successfulResults.reduce((sum, result) => sum + result.validation.completenessScore, 0) / successfulResults.length
			: 0;

		// Collect common issues
		const allIssues: string[] = [];
		successfulResults.forEach((result) =>
		{
			Object.values(result.validation.dataQuality).forEach((quality) =>
			{
				allIssues.push(...quality.issues);
			});
		});

		const issueFrequency = allIssues.reduce((freq, issue) =>
		{
			freq[issue] = (freq[issue] || 0) + 1;
			return freq;
		}, {} as Record<string, number>);

		const mostCommonIssues = Object.entries(issueFrequency)
			.sort(([, a], [, b]) => b - a)
			.slice(0, 5)
			.map(([issue]) => issue);

		// Calculate resource usage
		const totalMemory = successfulResults.reduce((sum, result) => sum + result.metadata.resourceUsage.memoryUsed, 0);
		const totalCpuTime = successfulResults.reduce((sum, result) => sum + result.metadata.resourceUsage.cpuTime, 0);
		const totalNetworkRequests = successfulResults.reduce((sum, result) => sum + result.metadata.resourceUsage.networkRequests, 0);

		return {
			totalDuration,
			avgDurationPerDomain,
			avgCompletenessScore,
			mostCommonIssues,
			resourceUsage: {
				totalMemory,
				totalCpuTime,
				totalNetworkRequests,
			},
		};
	}
}

export type {
	CollectionProfileType,
	BatchCollectionRequestType,
	BatchCollectionResultType,
};

export default SelectiveDataCollectionService;
