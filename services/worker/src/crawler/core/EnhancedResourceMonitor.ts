import { Logger } from '@shared';
import { EventEmitter } from 'events';

const logger = Logger.getLogger('EnhancedResourceMonitor');

type ResourceMetrics =
{
	timestamp: Date;
	memory: MemoryMetrics;
	cpu: CPUMetrics;
	network: NetworkMetrics;
	disk: DiskMetrics;
	custom: Record<string, number>;
};

type MemoryMetrics =
{
	heapUsed: number;
	heapTotal: number;
	external: number;
	rss: number;
	arrayBuffers: number;
	peakHeapUsed: number;
	gcCount: number;
	gcDuration: number;
};

type CPUMetrics =
{
	usage: number; // Percentage
	userTime: number;
	systemTime: number;
	idleTime: number;
	loadAverage: number[];
	processTime: number;
};

type NetworkMetrics =
{
	requestCount: number;
	responseCount: number;
	bytesReceived: number;
	bytesSent: number;
	activeConnections: number;
	failedRequests: number;
	averageLatency: number;
};

type DiskMetrics =
{
	readBytes: number;
	writeBytes: number;
	readOperations: number;
	writeOperations: number;
	freeSpace: number;
	totalSpace: number;
};

type ResourceThresholds =
{
	memory: {
		warning: number; // MB
		critical: number; // MB
		heapWarning: number; // MB
	};
	cpu: {
		warning: number; // Percentage
		critical: number; // Percentage
		sustainedDuration: number; // ms
	};
	network: {
		maxConcurrentRequests: number;
		maxLatency: number; // ms
		maxFailureRate: number; // Percentage
	};
	disk: {
		minFreeSpace: number; // MB
		maxIOPS: number;
	};
};

type ResourceAlert =
{
	id: string;
	timestamp: Date;
	type: 'memory' | 'cpu' | 'network' | 'disk' | 'custom';
	severity: 'info' | 'warning' | 'critical';
	message: string;
	metrics: Partial<ResourceMetrics>;
	resolved: boolean;
	resolvedAt?: Date;
};

type MonitoringSession =
{
	id: string;
	startTime: Date;
	endTime?: Date;
	domain?: string;
	operation?: string;
	metrics: ResourceMetrics[];
	alerts: ResourceAlert[];
	summary?: SessionSummary;
};

type SessionSummary =
{
	duration: number;
	peakMemory: number;
	averageCPU: number;
	totalNetworkRequests: number;
	totalBytesTransferred: number;
	alertCount: number;
	efficiency: number; // 0-1 score
};

type ResourcePrediction =
{
	operation: string;
	estimatedDuration: number;
	estimatedMemory: number;
	estimatedCPU: number;
	estimatedNetwork: number;
	confidence: number;
	basedOnSamples: number;
};

/**
 * Enhanced Resource Monitor with predictive capabilities and intelligent alerting
 */
class EnhancedResourceMonitor extends EventEmitter
{
	private isMonitoring: boolean = false;

	private currentSession: MonitoringSession | null = null;

	private sessions: Map<string, MonitoringSession> = new Map();

	private thresholds: ResourceThresholds;

	private activeAlerts: Map<string, ResourceAlert> = new Map();

	private metricsHistory: ResourceMetrics[] = [];

	private samplingInterval: number = 1000; // 1 second

	private samplingTimer: NodeJS.Timeout | null = null;

	private baselineMetrics: ResourceMetrics | null = null;

	private operationProfiles: Map<string, ResourcePrediction[]> = new Map();

	private gcObserver: PerformanceObserver | null = null;

	constructor(thresholds?: Partial<ResourceThresholds>)
	{
		super();
		this.thresholds = this.mergeThresholds(thresholds);
		this.initializeGCObserver();
	}

	/**
	 * Start monitoring session
	 */
	startSession(sessionId: string, domain?: string, operation?: string): MonitoringSession
	{
		if (this.currentSession)
		{
			this.endSession();
		}

		const session: MonitoringSession = {
			id: sessionId,
			startTime: new Date(),
			domain,
			operation,
			metrics: [],
			alerts: [],
		};

		this.currentSession = session;
		this.sessions.set(sessionId, session);

		if (!this.isMonitoring)
		{
			this.startMonitoring();
		}

		// Capture baseline if this is the first session
		if (!this.baselineMetrics)
		{
			this.captureBaseline();
		}

		logger.info(`Started monitoring session: ${sessionId}`, {
			domain,
			operation,
		});

		this.emit('sessionStarted', session);
		return session;
	}

	/**
	 * End current monitoring session
	 */
	endSession(): MonitoringSession | null
	{
		if (!this.currentSession)
		{
			return null;
		}

		const session = this.currentSession;
		session.endTime = new Date();
		session.summary = this.generateSessionSummary(session);

		// Store operation profile for future predictions
		if (session.operation)
		{
			this.updateOperationProfile(session);
		}

		logger.info(`Ended monitoring session: ${session.id}`, {
			duration: session.summary.duration,
			peakMemory: session.summary.peakMemory,
			alertCount: session.summary.alertCount,
		});

		this.emit('sessionEnded', session);

		this.currentSession = null;

		// Stop monitoring if no active sessions
		if (this.sessions.size === 0)
		{
			this.stopMonitoring();
		}

		return session;
	}

	/**
	 * Get current resource metrics
	 */
	getCurrentMetrics(): ResourceMetrics
	{
		const memoryUsage = process.memoryUsage();
		const cpuUsage = process.cpuUsage();

		return {
			timestamp: new Date(),
			memory: {
				heapUsed: memoryUsage.heapUsed,
				heapTotal: memoryUsage.heapTotal,
				external: memoryUsage.external,
				rss: memoryUsage.rss,
				arrayBuffers: memoryUsage.arrayBuffers,
				peakHeapUsed: this.getPeakHeapUsed(),
				gcCount: this.getGCCount(),
				gcDuration: this.getGCDuration(),
			},
			cpu: {
				usage: this.calculateCPUUsage(cpuUsage),
				userTime: cpuUsage.user,
				systemTime: cpuUsage.system,
				idleTime: 0, // Would need OS-specific implementation
				loadAverage: this.getLoadAverage(),
				processTime: process.uptime() * 1000,
			},
			network: {
				requestCount: this.getNetworkRequestCount(),
				responseCount: this.getNetworkResponseCount(),
				bytesReceived: this.getNetworkBytesReceived(),
				bytesSent: this.getNetworkBytesSent(),
				activeConnections: this.getActiveConnections(),
				failedRequests: this.getFailedRequests(),
				averageLatency: this.getAverageLatency(),
			},
			disk: {
				readBytes: 0, // Would need OS-specific implementation
				writeBytes: 0,
				readOperations: 0,
				writeOperations: 0,
				freeSpace: 0,
				totalSpace: 0,
			},
			custom: {},
		};
	}

	/**
	 * Predict resource requirements for an operation
	 */
	predictResourceRequirements(operation: string): ResourcePrediction | null
	{
		const profiles = this.operationProfiles.get(operation);
		if (!profiles || profiles.length === 0)
		{
			return null;
		}

		// Calculate weighted average based on recency
		const now = Date.now();
		let totalWeight = 0;
		let weightedDuration = 0;
		let weightedMemory = 0;
		let weightedCPU = 0;
		let weightedNetwork = 0;

		for (const profile of profiles)
		{
			// More recent profiles have higher weight
			const weight = Math.exp(-(now - Date.now()) / (24 * 60 * 60 * 1000)); // Decay over 24 hours
			totalWeight += weight;
			weightedDuration += profile.estimatedDuration * weight;
			weightedMemory += profile.estimatedMemory * weight;
			weightedCPU += profile.estimatedCPU * weight;
			weightedNetwork += profile.estimatedNetwork * weight;
		}

		if (totalWeight === 0)
		{
			return null;
		}

		return {
			operation,
			estimatedDuration: weightedDuration / totalWeight,
			estimatedMemory: weightedMemory / totalWeight,
			estimatedCPU: weightedCPU / totalWeight,
			estimatedNetwork: weightedNetwork / totalWeight,
			confidence: Math.min(profiles.length / 10, 1), // Higher confidence with more samples
			basedOnSamples: profiles.length,
		};
	}

	/**
	 * Add custom metric
	 */
	addCustomMetric(name: string, value: number): void
	{
		if (this.currentSession)
		{
			const latestMetrics = this.currentSession.metrics[this.currentSession.metrics.length - 1];
			if (latestMetrics)
			{
				latestMetrics.custom[name] = value;
			}
		}
	}

	/**
	 * Set resource thresholds
	 */
	setThresholds(thresholds: Partial<ResourceThresholds>): void
	{
		this.thresholds = this.mergeThresholds(thresholds);
		logger.info('Updated resource thresholds', this.thresholds);
	}

	/**
	 * Get session by ID
	 */
	getSession(sessionId: string): MonitoringSession | undefined
	{
		return this.sessions.get(sessionId);
	}

	/**
	 * Get all sessions
	 */
	getAllSessions(): MonitoringSession[]
	{
		return Array.from(this.sessions.values());
	}

	/**
	 * Get active alerts
	 */
	getActiveAlerts(): ResourceAlert[]
	{
		return Array.from(this.activeAlerts.values()).filter(alert => !alert.resolved);
	}

	/**
	 * Get resource usage statistics
	 */
	getUsageStatistics(timeRange?: { start: Date; end: Date }): ResourceUsageStatistics
	{
		const metrics = timeRange
			? this.metricsHistory.filter(m => m.timestamp >= timeRange.start && m.timestamp <= timeRange.end)
			: this.metricsHistory;

		if (metrics.length === 0)
		{
			return this.getEmptyStatistics();
		}

		return {
			timeRange: {
				start: metrics[0].timestamp,
				end: metrics[metrics.length - 1].timestamp,
			},
			memory: {
				average: this.calculateAverage(metrics.map(m => m.memory.heapUsed)),
				peak: Math.max(...metrics.map(m => m.memory.heapUsed)),
				minimum: Math.min(...metrics.map(m => m.memory.heapUsed)),
			},
			cpu: {
				average: this.calculateAverage(metrics.map(m => m.cpu.usage)),
				peak: Math.max(...metrics.map(m => m.cpu.usage)),
				minimum: Math.min(...metrics.map(m => m.cpu.usage)),
			},
			network: {
				totalRequests: metrics[metrics.length - 1].network.requestCount - metrics[0].network.requestCount,
				totalBytes: (metrics[metrics.length - 1].network.bytesReceived + metrics[metrics.length - 1].network.bytesSent)
					- (metrics[0].network.bytesReceived + metrics[0].network.bytesSent),
				averageLatency: this.calculateAverage(metrics.map(m => m.network.averageLatency)),
			},
			efficiency: this.calculateEfficiency(metrics),
		};
	}

	/**
	 * Cleanup old data
	 */
	cleanup(maxAge: number = 24 * 60 * 60 * 1000): void // Default 24 hours
	{
		const cutoff = new Date(Date.now() - maxAge);

		// Clean up metrics history
		this.metricsHistory = this.metricsHistory.filter(m => m.timestamp > cutoff);

		// Clean up old sessions
		for (const [sessionId, session] of this.sessions.entries())
		{
			if (session.endTime && session.endTime < cutoff)
			{
				this.sessions.delete(sessionId);
			}
		}

		// Clean up resolved alerts
		for (const [alertId, alert] of this.activeAlerts.entries())
		{
			if (alert.resolved && alert.resolvedAt && alert.resolvedAt < cutoff)
			{
				this.activeAlerts.delete(alertId);
			}
		}

		logger.debug('Cleaned up old monitoring data', {
			metricsCount: this.metricsHistory.length,
			sessionsCount: this.sessions.size,
			alertsCount: this.activeAlerts.size,
		});
	}

	/**
	 * Start monitoring
	 */
	private startMonitoring(): void
	{
		if (this.isMonitoring)
		{
			return;
		}

		this.isMonitoring = true;
		this.samplingTimer = setInterval(() =>
		{
			this.collectMetrics();
		}, this.samplingInterval);

		logger.debug('Started resource monitoring');
	}

	/**
	 * Stop monitoring
	 */
	private stopMonitoring(): void
	{
		if (!this.isMonitoring)
		{
			return;
		}

		this.isMonitoring = false;
		if (this.samplingTimer)
		{
			clearInterval(this.samplingTimer);
			this.samplingTimer = null;
		}

		logger.debug('Stopped resource monitoring');
	}

	/**
	 * Collect current metrics
	 */
	private collectMetrics(): void
	{
		const metrics = this.getCurrentMetrics();

		// Add to history
		this.metricsHistory.push(metrics);

		// Keep only last 1000 metrics
		if (this.metricsHistory.length > 1000)
		{
			this.metricsHistory = this.metricsHistory.slice(-1000);
		}

		// Add to current session
		if (this.currentSession)
		{
			this.currentSession.metrics.push(metrics);
		}

		// Check for threshold violations
		this.checkThresholds(metrics);

		this.emit('metricsCollected', metrics);
	}

	/**
	 * Check resource thresholds and generate alerts
	 */
	private checkThresholds(metrics: ResourceMetrics): void
	{
		// Memory checks
		const memoryMB = metrics.memory.heapUsed / (1024 * 1024);
		if (memoryMB > this.thresholds.memory.critical)
		{
			this.createAlert('memory', 'critical', `Memory usage critical: ${memoryMB.toFixed(1)}MB`, metrics);
		}
		else if (memoryMB > this.thresholds.memory.warning)
		{
			this.createAlert('memory', 'warning', `Memory usage high: ${memoryMB.toFixed(1)}MB`, metrics);
		}

		// CPU checks
		if (metrics.cpu.usage > this.thresholds.cpu.critical)
		{
			this.createAlert('cpu', 'critical', `CPU usage critical: ${metrics.cpu.usage.toFixed(1)}%`, metrics);
		}
		else if (metrics.cpu.usage > this.thresholds.cpu.warning)
		{
			this.createAlert('cpu', 'warning', `CPU usage high: ${metrics.cpu.usage.toFixed(1)}%`, metrics);
		}

		// Network checks
		if (metrics.network.activeConnections > this.thresholds.network.maxConcurrentRequests)
		{
			this.createAlert('network', 'warning', `High concurrent connections: ${metrics.network.activeConnections}`, metrics);
		}

		if (metrics.network.averageLatency > this.thresholds.network.maxLatency)
		{
			this.createAlert('network', 'warning', `High network latency: ${metrics.network.averageLatency}ms`, metrics);
		}
	}

	/**
	 * Create resource alert
	 */
	private createAlert(
		type: ResourceAlert['type'],
		severity: ResourceAlert['severity'],
		message: string,
		metrics: ResourceMetrics,
	): void
	{
		const alertId = `${type}-${severity}-${Date.now()}`;
		const alert: ResourceAlert = {
			id: alertId,
			timestamp: new Date(),
			type,
			severity,
			message,
			metrics,
			resolved: false,
		};

		this.activeAlerts.set(alertId, alert);

		if (this.currentSession)
		{
			this.currentSession.alerts.push(alert);
		}

		logger.warn(`Resource alert: ${message}`, { type, severity });
		this.emit('alert', alert);
	}

	/**
	 * Generate session summary
	 */
	private generateSessionSummary(session: MonitoringSession): SessionSummary
	{
		const duration = session.endTime
			? session.endTime.getTime() - session.startTime.getTime()
			: Date.now() - session.startTime.getTime();

		const metrics = session.metrics;
		if (metrics.length === 0)
		{
			return {
				duration,
				peakMemory: 0,
				averageCPU: 0,
				totalNetworkRequests: 0,
				totalBytesTransferred: 0,
				alertCount: session.alerts.length,
				efficiency: 0,
			};
		}

		const peakMemory = Math.max(...metrics.map(m => m.memory.heapUsed));
		const averageCPU = this.calculateAverage(metrics.map(m => m.cpu.usage));
		const totalNetworkRequests = metrics[metrics.length - 1].network.requestCount - metrics[0].network.requestCount;
		const totalBytesTransferred = (metrics[metrics.length - 1].network.bytesReceived + metrics[metrics.length - 1].network.bytesSent)
			- (metrics[0].network.bytesReceived + metrics[0].network.bytesSent);

		return {
			duration,
			peakMemory,
			averageCPU,
			totalNetworkRequests,
			totalBytesTransferred,
			alertCount: session.alerts.length,
			efficiency: this.calculateEfficiency(metrics),
		};
	}

	/**
	 * Update operation profile for predictions
	 */
	private updateOperationProfile(session: MonitoringSession): void
	{
		if (!session.operation || !session.summary)
		{
			return;
		}

		const profile: ResourcePrediction = {
			operation: session.operation,
			estimatedDuration: session.summary.duration,
			estimatedMemory: session.summary.peakMemory,
			estimatedCPU: session.summary.averageCPU,
			estimatedNetwork: session.summary.totalNetworkRequests,
			confidence: 1.0,
			basedOnSamples: 1,
		};

		const profiles = this.operationProfiles.get(session.operation) || [];
		profiles.push(profile);

		// Keep only last 50 profiles per operation
		if (profiles.length > 50)
		{
			profiles.splice(0, profiles.length - 50);
		}

		this.operationProfiles.set(session.operation, profiles);
	}

	/**
	 * Initialize GC observer
	 */
	private initializeGCObserver(): void
	{
		try
		{
			if (typeof PerformanceObserver !== 'undefined')
			{
				this.gcObserver = new PerformanceObserver((list) =>
				{
					for (const entry of list.getEntries())
					{
						if (entry.entryType === 'gc')
						{
							this.emit('gc', {
								type: entry.name,
								duration: entry.duration,
								timestamp: new Date(entry.startTime),
							});
						}
					}
				});

				this.gcObserver.observe({ entryTypes: ['gc'] });
			}
		}
		catch (error)
		{
			logger.warn('Failed to initialize GC observer:', error);
		}
	}

	/**
	 * Merge thresholds with defaults
	 */
	private mergeThresholds(thresholds?: Partial<ResourceThresholds>): ResourceThresholds
	{
		const defaults: ResourceThresholds = {
			memory: {
				warning: 200, // 200MB
				critical: 500, // 500MB
				heapWarning: 150, // 150MB
			},
			cpu: {
				warning: 70, // 70%
				critical: 90, // 90%
				sustainedDuration: 30000, // 30 seconds
			},
			network: {
				maxConcurrentRequests: 50,
				maxLatency: 5000, // 5 seconds
				maxFailureRate: 10, // 10%
			},
			disk: {
				minFreeSpace: 1000, // 1GB
				maxIOPS: 1000,
			},
		};

		return {
			memory: { ...defaults.memory, ...thresholds?.memory },
			cpu: { ...defaults.cpu, ...thresholds?.cpu },
			network: { ...defaults.network, ...thresholds?.network },
			disk: { ...defaults.disk, ...thresholds?.disk },
		};
	}

	/**
	 * Capture baseline metrics
	 */
	private captureBaseline(): void
	{
		this.baselineMetrics = this.getCurrentMetrics();
		logger.debug('Captured baseline metrics', {
			memory: this.baselineMetrics.memory.heapUsed,
			cpu: this.baselineMetrics.cpu.usage,
		});
	}

	/**
	 * Helper methods for metrics calculation
	 */
	private calculateAverage(values: number[]): number
	{
		return values.length > 0 ? values.reduce((sum, val) => sum + val, 0) / values.length : 0;
	}

	private calculateEfficiency(metrics: ResourceMetrics[]): number
	{
		// Simple efficiency calculation based on resource utilization
		if (metrics.length === 0) return 0;

		const avgMemoryUsage = this.calculateAverage(metrics.map(m => m.memory.heapUsed / m.memory.heapTotal));
		const avgCpuUsage = this.calculateAverage(metrics.map(m => m.cpu.usage / 100));

		// Efficiency is inverse of resource waste
		return 1 - Math.max(avgMemoryUsage, avgCpuUsage);
	}

	private calculateCPUUsage(cpuUsage: NodeJS.CpuUsage): number
	{
		// Simple CPU usage calculation (would need more sophisticated implementation)
		return (cpuUsage.user + cpuUsage.system) / 1000000; // Convert microseconds to percentage
	}

	private getLoadAverage(): number[]
	{
		try
		{
			return require('os').loadavg();
		}
		catch
		{
			return [0, 0, 0];
		}
	}

	private getPeakHeapUsed(): number
	{
		return Math.max(...this.metricsHistory.map(m => m.memory.heapUsed));
	}

	private getGCCount(): number
	{
		// Would need to track GC events
		return 0;
	}

	private getGCDuration(): number
	{
		// Would need to track GC events
		return 0;
	}

	// Network metrics helpers (would need actual implementation)
	private getNetworkRequestCount(): number { return 0 }
	private getNetworkResponseCount(): number { return 0 }
	private getNetworkBytesReceived(): number { return 0 }
	private getNetworkBytesSent(): number { return 0 }
	private getActiveConnections(): number { return 0 }
	private getFailedRequests(): number { return 0 }
	private getAverageLatency(): number { return 0 }

	private getEmptyStatistics(): ResourceUsageStatistics
	{
		return {
			timeRange: { start: new Date(), end: new Date() },
			memory: { average: 0, peak: 0, minimum: 0 },
			cpu: { average: 0, peak: 0, minimum: 0 },
			network: { totalRequests: 0, totalBytes: 0, averageLatency: 0 },
			efficiency: 0,
		};
	}
}

// Supporting interfaces
type ResourceUsageStatistics =
{
	timeRange: { start: Date; end: Date };
	memory: { average: number; peak: number; minimum: number };
	cpu: { average: number; peak: number; minimum: number };
	network: { totalRequests: number; totalBytes: number; averageLatency: number };
	efficiency: number;
};

export type {
	ResourceMetrics,
	MemoryMetrics,
	CPUMetrics,
	NetworkMetrics,
	DiskMetrics,
	ResourceThresholds,
	ResourceAlert,
	MonitoringSession,
	SessionSummary,
	ResourcePrediction,
	ResourceUsageStatistics,
};

export default EnhancedResourceMonitor;
