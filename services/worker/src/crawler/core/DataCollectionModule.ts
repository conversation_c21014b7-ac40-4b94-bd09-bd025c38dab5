import { Logger } from '@shared';

const logger = Logger.getLogger('DataCollectionModule');

type DataCollectionModuleConfigType =
{
	name: string;
	priority: number;
	timeout: number;
	retryAttempts: number;
	retryDelay: number;
	dependencies?: string[];
};

type DataCollectionResultType<T = any> =
{
	success: boolean;
	data?: T;
	error?: string;
	executionTime: number;
	retryCount: number;
};

abstract class DataCollectionModule<T = any>
{
	protected config: DataCollectionModuleConfigType;

	protected logger: any;

	constructor(config: DataCollectionModuleConfigType)
	{
		this.config = config;
		this.logger = Logger.getLogger(`DataCollectionModule:${config.name}`);
	}

	/**
   * Execute the data collection with retry logic
   */
	async execute(domain: string): Promise<DataCollectionResultType<T>>
	{
		const startTime = Date.now();
		let retryCount = 0;
		let lastError: Error | null = null;

		while (retryCount <= this.config.retryAttempts)
		{
			try
			{
				this.logger.debug(`Executing ${this.config.name} for domain: ${domain} (attempt ${retryCount + 1})`);

				 
				const data = await this.executeWithTimeout(domain);

				const executionTime = Date.now() - startTime;
				this.logger.info(`${this.config.name} completed for domain: ${domain} in ${executionTime}ms`);

				return {
					success: true,
					data,
					executionTime,
					retryCount,
				};
			}
			catch (error)
			{
				lastError = error instanceof Error ? error : new Error(String(error));
				retryCount++;

				this.logger.warn(`${this.config.name} failed for domain: ${domain} (attempt ${retryCount}):`, lastError.message);

				if (retryCount <= this.config.retryAttempts)
				{
					 
					await this.delay(this.config.retryDelay * retryCount);
				}
			}
		}

		const executionTime = Date.now() - startTime;
		this.logger.error(`${this.config.name} failed for domain: ${domain} after ${retryCount} attempts`);

		return {
			success: false,
			error: lastError?.message || 'Unknown error',
			executionTime,
			retryCount: retryCount - 1,
		};
	}

	/**
   * Execute the actual data collection with timeout
   */
	private async executeWithTimeout(domain: string): Promise<T>
	{
		return new Promise<T>((resolve, reject) =>
		{
			const timeoutId = setTimeout(() =>
			{
				reject(new Error(`${this.config.name} timed out after ${this.config.timeout}ms`));
			}, this.config.timeout);

			this.collect(domain)
				.then((result) =>
				{
					clearTimeout(timeoutId);
					resolve(result);
				})
				.catch((error) =>
				{
					clearTimeout(timeoutId);
					reject(error);
				});
		});
	}

	/**
   * Abstract method to be implemented by concrete modules
   */
	protected abstract collect(domain: string): Promise<T>;

	/**
   * Get module configuration
   */
	getConfig(): DataCollectionModuleConfigType
	{
		return ({ ...this.config });
	}

	/**
   * Check if module can run (dependencies satisfied)
   */
	canRun(availableModules: string[]): boolean
	{
		if (!this.config.dependencies)
		{
			return true;
		}

		return this.config.dependencies.every(dep => availableModules.includes(dep));
	}

	/**
   * Delay utility
   */
	private delay(ms: number): Promise<void>
	{
		 
		return new Promise(resolve => setTimeout(resolve, ms));
	}
}

export type { DataCollectionModuleConfigType, DataCollectionResultType };

export default DataCollectionModule;
