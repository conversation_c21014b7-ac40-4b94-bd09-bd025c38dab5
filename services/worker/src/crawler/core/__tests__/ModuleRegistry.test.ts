import {
	describe, it, expect, beforeEach,
} from 'vitest';
import { ModuleRegistry } from '../ModuleRegistry';
import { DataCollectionModule, DataCollectionModuleConfig } from '../DataCollectionModule';

// Mock modules for testing
class MockDNSModule extends DataCollectionModule<any>
{
	constructor()
	{
		const config: DataCollectionModuleConfig = {
			name: 'dns',
			priority: 95,
			timeout: 5000,
			retryAttempts: 2,
			retryDelay: 1000,
			dependencies: [],
		};
		super(config);
	}

	protected async collect(domain: string): Promise<any>
	{
		return {
			domain,
			records: { A: ['192.168.1.1'] },
			lastAnalyzed: new Date().toISOString(),
		};
	}
}

class MockSSLModule extends DataCollectionModule<any>
{
	constructor()
	{
		const config: DataCollectionModuleConfig = {
			name: 'ssl',
			priority: 80,
			timeout: 8000,
			retryAttempts: 2,
			retryDelay: 1500,
			dependencies: ['dns'],
		};
		super(config);
	}

	protected async collect(domain: string): Promise<any>
	{
		return {
			domain,
			hasSSL: true,
			grade: 'A',
			lastAnalyzed: new Date().toISOString(),
		};
	}
}

class MockHomepageModule extends DataCollectionModule<any>
{
	constructor()
	{
		const config: DataCollectionModuleConfig = {
			name: 'homepage',
			priority: 70,
			timeout: 10000,
			retryAttempts: 2,
			retryDelay: 2000,
			dependencies: ['dns', 'ssl'],
		};
		super(config);
	}

	protected async collect(domain: string): Promise<any>
	{
		return {
			domain,
			accessible: true,
			title: 'Test Page',
			lastAnalyzed: new Date().toISOString(),
		};
	}
}

describe('ModuleRegistry', () =>
{
	let registry: ModuleRegistry;

	beforeEach(() =>
	{
		registry = new ModuleRegistry();
	});

	describe('module registration', () =>
	{
		it('should register modules successfully', () =>
		{
			const dnsModule = new MockDNSModule();
			registry.registerModule('dns', dnsModule);

			expect(registry.getModuleNames()).toContain('dns');
			expect(registry.getModule('dns')).toBe(dnsModule);
		});

		it('should unregister modules successfully', () =>
		{
			const dnsModule = new MockDNSModule();
			registry.registerModule('dns', dnsModule);
			registry.unregisterModule('dns');

			expect(registry.getModuleNames()).not.toContain('dns');
			expect(registry.getModule('dns')).toBeUndefined();
		});

		it('should return all registered module names', () =>
		{
			registry.registerModule('dns', new MockDNSModule());
			registry.registerModule('ssl', new MockSSLModule());

			const moduleNames = registry.getModuleNames();
			expect(moduleNames).toContain('dns');
			expect(moduleNames).toContain('ssl');
			expect(moduleNames.length).toBe(2);
		});
	});

	describe('execution planning', () =>
	{
		beforeEach(() =>
		{
			registry.registerModule('dns', new MockDNSModule());
			registry.registerModule('ssl', new MockSSLModule());
			registry.registerModule('homepage', new MockHomepageModule());
		});

		it('should create execution plan with correct module order', () =>
		{
			const plan = registry.createExecutionPlan(['dns', 'ssl', 'homepage']);

			expect(plan.modules).toEqual(['dns', 'ssl', 'homepage']);
			expect(plan.executionOrder).toEqual(['dns', 'ssl', 'homepage']);
		});

		it('should handle missing modules in execution plan', () =>
		{
			const plan = registry.createExecutionPlan(['dns', 'nonexistent', 'ssl']);

			expect(plan.modules).toEqual(['dns', 'ssl']);
			expect(plan.executionOrder).toEqual(['dns', 'ssl']);
		});

		it('should create parallel groups based on dependencies', () =>
		{
			const plan = registry.createExecutionPlan(['dns', 'ssl', 'homepage']);

			// DNS should be in first group (no dependencies)
			// SSL should be in second group (depends on DNS)
			// Homepage should be in third group (depends on DNS and SSL)
			expect(plan.parallelGroups.length).toBeGreaterThan(0);
			expect(plan.parallelGroups[0]).toContain('dns');
		});
	});

	describe('module execution', () =>
	{
		beforeEach(() =>
		{
			registry.registerModule('dns', new MockDNSModule());
			registry.registerModule('ssl', new MockSSLModule());
		});

		it('should execute modules successfully', async () =>
		{
			const result = await registry.executeModules('example.com', ['dns', 'ssl']);

			expect(result.domain).toBe('example.com');
			expect(result.successCount).toBe(2);
			expect(result.failureCount).toBe(0);
			expect(result.results.dns).toBeDefined();
			expect(result.results.ssl).toBeDefined();
			expect(result.results.dns.success).toBe(true);
			expect(result.results.ssl.success).toBe(true);
		});

		it('should handle module execution failures', async () =>
		{
			// Create a failing module
			class FailingModule extends DataCollectionModule<any>
			{
				constructor()
				{
					const config: DataCollectionModuleConfig = {
						name: 'failing',
						priority: 50,
						timeout: 1000,
						retryAttempts: 1,
						retryDelay: 500,
						dependencies: [],
					};
					super(config);
				}

				protected async collect(domain: string): Promise<any>
				{
					throw new Error('Simulated failure');
				}
			}

			registry.registerModule('failing', new FailingModule());

			const result = await registry.executeModules('example.com', ['dns', 'failing']);

			expect(result.successCount).toBe(1);
			expect(result.failureCount).toBe(1);
			expect(result.results.dns.success).toBe(true);
			expect(result.results.failing.success).toBe(false);
		});

		it('should calculate completeness correctly', async () =>
		{
			const result = await registry.executeModules('example.com', ['dns', 'ssl']);

			expect(result.completeness.percentage).toBe(100);
			expect(result.completeness.available).toEqual(['dns', 'ssl']);
			expect(result.completeness.missing).toEqual([]);
		});

		it('should track execution time', async () =>
		{
			const result = await registry.executeModules('example.com', ['dns']);

			expect(result.totalExecutionTime).toBeGreaterThan(0);
			expect(result.results.dns.executionTime).toBeGreaterThan(0);
		});
	});

	describe('configuration validation', () =>
	{
		it('should validate configuration successfully for valid setup', () =>
		{
			registry.registerModule('dns', new MockDNSModule());
			registry.registerModule('ssl', new MockSSLModule());

			const validation = registry.validateConfiguration();

			expect(validation.valid).toBe(true);
			expect(validation.errors).toEqual([]);
		});

		it('should detect missing dependencies', () =>
		{
			// Register SSL module without DNS module (its dependency)
			registry.registerModule('ssl', new MockSSLModule());

			const validation = registry.validateConfiguration();

			expect(validation.valid).toBe(false);
			expect(validation.errors.length).toBeGreaterThan(0);
			expect(validation.errors[0]).toContain('missing dependencies');
		});

		it('should detect circular dependencies', () =>
		{
			// Create modules with circular dependencies
			class ModuleA extends DataCollectionModule<any>
			{
				constructor()
				{
					super({
						name: 'moduleA',
						priority: 50,
						timeout: 5000,
						retryAttempts: 1,
						retryDelay: 1000,
						dependencies: ['moduleB'],
					});
				}

				protected async collect(domain: string): Promise<any>
				{
					return { domain };
				}
			}

			class ModuleB extends DataCollectionModule<any>
			{
				constructor()
				{
					super({
						name: 'moduleB',
						priority: 50,
						timeout: 5000,
						retryAttempts: 1,
						retryDelay: 1000,
						dependencies: ['moduleA'],
					});
				}

				protected async collect(domain: string): Promise<any>
				{
					return { domain };
				}
			}

			registry.registerModule('moduleA', new ModuleA());
			registry.registerModule('moduleB', new ModuleB());

			const validation = registry.validateConfiguration();

			expect(validation.valid).toBe(false);
			expect(validation.errors.length).toBeGreaterThan(0);
			expect(validation.errors[0]).toContain('Circular dependency');
		});
	});

	describe('module statistics', () =>
	{
		it('should return module statistics', () =>
		{
			registry.registerModule('dns', new MockDNSModule());
			registry.registerModule('ssl', new MockSSLModule());

			const stats = registry.getModuleStats();

			expect(stats.dns).toBeDefined();
			expect(stats.ssl).toBeDefined();
			expect(stats.dns.priority).toBe(95);
			expect(stats.ssl.priority).toBe(80);
			expect(stats.ssl.dependencies).toEqual(['dns']);
		});
	});
});
