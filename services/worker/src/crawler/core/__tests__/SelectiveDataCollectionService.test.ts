import {
	describe, it, expect, beforeEach,
} from 'vitest';
import type { DataCollectionModuleConfigType } from '../DataCollectionModule';
import type { CollectionProfileType, BatchCollectionRequestType } from '../SelectiveDataCollectionService';
import SelectiveDataCollectionService from '../SelectiveDataCollectionService';
import ModuleRegistry from '../ModuleRegistry';
import DataCollectionModule from '../DataCollectionModule';

// Mock modules for testing
class MockDNSModule extends DataCollectionModule<any>
{
	constructor()
	{
		const config: DataCollectionModuleConfigType =
		{
			name: 'dns',
			priority: 95,
			timeout: 5000,
			retryAttempts: 2,
			retryDelay: 1000,
			dependencies: [],
		};
		super(config);
	}

	protected async collect(domain: string): Promise<any>
	{
		return {
			domain,
			records: {
				A: ['192.168.1.1'],
				AAAA: [],
				MX: [],
				NS: ['ns1.example.com', 'ns2.example.com'],
			},
			lastAnalyzed: new Date().toISOString(),
		};
	}
}

class MockRobotsModule extends DataCollectionModule<any>
{
	constructor()
	{
		const config: DataCollectionModuleConfig = {
			name: 'robots',
			priority: 100,
			timeout: 3000,
			retryAttempts: 2,
			retryDelay: 1000,
			dependencies: [],
		};
		super(config);
	}

	protected async collect(domain: string): Promise<any>
	{
		return {
			domain,
			exists: true,
			accessible: true,
			rules: [],
			sitemaps: [],
			complianceStatus: {
				isCompliant: true,
				violations: [],
				warnings: [],
			},
			lastAnalyzed: new Date().toISOString(),
		};
	}
}

describe('SelectiveDataCollectionService', () =>
{
	let service: SelectiveDataCollectionService;
	let moduleRegistry: ModuleRegistry;

	beforeEach(() =>
	{
		moduleRegistry = new ModuleRegistry();
		moduleRegistry.registerModule('dns', new MockDNSModule());
		moduleRegistry.registerModule('robots', new MockRobotsModule());
		service = new SelectiveDataCollectionService(moduleRegistry);
	});

	describe('collectDomain', () =>
	{
		it('should collect domain data using quick profile', async () =>
		{
			const response = await service.collectDomain('example.com', 'quick');

			expect(response.domain).toBe('example.com');
			expect(response.request.modules).toContain('dns');
			expect(response.request.modules).toContain('robots');
			expect(response.execution.successCount).toBeGreaterThan(0);
			expect(response.validation.completenessScore).toBeGreaterThan(0);
		});

		it('should collect domain data using standard profile', async () =>
		{
			const response = await service.collectDomain('example.com', 'standard');

			expect(response.domain).toBe('example.com');
			expect(response.request.modules.length).toBeGreaterThan(2);
			expect(response.request.priority).toBe('medium');
			expect(response.validation).toBeDefined();
		});

		it('should handle custom collection options', async () =>
		{
			const response = await service.collectDomain('example.com', 'quick', {
				priority: 'high',
				timeout: 15000,
				selective: {
					onlyIfMissing: true,
					requiredFields: ['records.A'],
					skipIfExists: [],
				},
			});

			expect(response.request.priority).toBe('high');
			expect(response.request.timeout).toBe(15000);
			expect(response.request.selective?.onlyIfMissing).toBe(true);
		});

		it('should throw error for unknown profile', async () =>
		{
			await expect(service.collectDomain('example.com', 'nonexistent'))
				.rejects.toThrow('Collection profile \'nonexistent\' not found');
		});
	});

	describe('collectBatch', () =>
	{
		it('should collect data for multiple domains', async () =>
		{
			const domains = ['example.com', 'test.com', 'demo.org'];
			const batchRequest: BatchCollectionRequestType = {
				domains,
				profile: 'quick',
				concurrency: 2,
			};

			const result = await service.collectBatch(batchRequest);

			expect(result.totalDomains).toBe(3);
			expect(result.successful).toBeGreaterThan(0);
			expect(result.results.size).toBeGreaterThan(0);
			expect(result.summary).toBeDefined();
			expect(result.summary.totalDuration).toBeGreaterThan(0);
		});

		it('should handle batch collection with progress callback', async () =>
		{
			const domains = ['example.com', 'test.com'];
			const progressUpdates: Array<{ completed: number; total: number; current: string }> = [];

			const batchRequest: BatchCollectionRequestType = {
				domains,
				profile: 'quick',
				concurrency: 1,
				onProgress: (completed, total, current) =>
				{
					progressUpdates.push({ completed, total, current });
				},
			};

			const result = await service.collectBatch(batchRequest);

			expect(progressUpdates.length).toBeGreaterThan(0);
			expect(progressUpdates[progressUpdates.length - 1].completed).toBe(domains.length);
			expect(result.successful).toBe(domains.length);
		});

		it('should handle batch collection with error callback', async () =>
		{
			const domains = ['example.com', 'invalid.domain.that.should.fail'];
			const errors: Array<{ domain: string; error: Error }> = [];

			const batchRequest: BatchCollectionRequestType = {
				domains,
				profile: 'quick',
				concurrency: 1,
				onError: (domain, error) =>
				{
					errors.push({ domain, error });
				},
			};

			const result = await service.collectBatch(batchRequest);

			expect(result.totalDomains).toBe(2);
			// At least one should succeed (example.com)
			expect(result.successful).toBeGreaterThan(0);
		});
	});

	describe('collectMissingData', () =>
	{
		it('should collect only missing data for specified modules', async () =>
		{
			const response = await service.collectMissingData('example.com', ['dns', 'robots'], {
				priority: 'high',
				requiredFields: ['records.A', 'exists'],
			});

			expect(response.domain).toBe('example.com');
			expect(response.request.modules).toContain('dns');
			expect(response.request.modules).toContain('robots');
			expect(response.request.priority).toBe('high');
			expect(response.request.selective?.onlyIfMissing).toBe(true);
		});
	});

	describe('refreshStaleData', () =>
	{
		it('should refresh stale data with default settings', async () =>
		{
			const response = await service.refreshStaleData('example.com');

			expect(response.domain).toBe('example.com');
			expect(response.request.priority).toBe('low');
			expect(response.request.selective?.onlyIfMissing).toBe(false);
		});

		it('should refresh stale data with custom settings', async () =>
		{
			const response = await service.refreshStaleData('example.com', 48, {
				priority: 'medium',
				modules: ['dns', 'robots'],
			});

			expect(response.domain).toBe('example.com');
			expect(response.request.priority).toBe('medium');
			expect(response.request.modules).toEqual(['dns', 'robots']);
		});
	});

	describe('profiles management', () =>
	{
		it('should return available collection profiles', () =>
		{
			const profiles = service.getProfiles();

			expect(Array.isArray(profiles)).toBe(true);
			expect(profiles.length).toBeGreaterThan(0);

			const profileNames = profiles.map(p => p.name);

			expect(profileNames).toContain('quick');
			expect(profileNames).toContain('standard');
			expect(profileNames).toContain('complete');
		});

		it('should add custom collection profile', () =>
		{
			const customProfile: CollectionProfileType = {
				name: 'custom',
				description: 'Custom test profile',
				modules: ['dns'],
				priority: 'medium',
				timeout: 10000,
				retryPolicy: {
					maxRetries: 1,
					backoffMultiplier: 1.5,
					maxBackoffDelay: 5000,
				},
				selective: {
					onlyIfMissing: true,
					requiredFields: [],
					skipIfExists: [],
				},
			};

			service.setProfile(customProfile);

			const profiles = service.getProfiles();
			const customProfileFound = profiles.find(p => p.name === 'custom');

			expect(customProfileFound).toBeDefined();
			expect(customProfileFound?.description).toBe('Custom test profile');
		});

		it('should remove collection profile', () =>
		{
			const removed = service.removeProfile('quick');

			expect(removed).toBe(true);

			const profiles = service.getProfiles();
			const quickProfile = profiles.find(p => p.name === 'quick');

			expect(quickProfile).toBeUndefined();
		});
	});

	describe('analyzeDomainCompleteness', () =>
	{
		it('should analyze domain data completeness', async () =>
		{
			const analysis = await service.analyzeDomainCompleteness('example.com', ['dns', 'robots', 'ssl']);

			expect(analysis.completeness).toBeGreaterThanOrEqual(0);
			expect(analysis.completeness).toBeLessThanOrEqual(1);
			expect(Array.isArray(analysis.missing)).toBe(true);
			expect(Array.isArray(analysis.stale)).toBe(true);
			expect(Array.isArray(analysis.recommendations)).toBe(true);
		});
	});

	describe('getCollectionStats', () =>
	{
		it('should return collection statistics', () =>
		{
			const stats = service.getCollectionStats();

			expect(stats.profileUsage).toBeDefined();
			expect(stats.avgCompleteness).toBeGreaterThanOrEqual(0);
			expect(stats.avgCompleteness).toBeLessThanOrEqual(1);
			expect(Array.isArray(stats.commonIssues)).toBe(true);
			expect(stats.performanceMetrics).toBeDefined();
			expect(stats.performanceMetrics.avgDuration).toBeGreaterThan(0);
			expect(stats.performanceMetrics.successRate).toBeGreaterThanOrEqual(0);
			expect(stats.performanceMetrics.successRate).toBeLessThanOrEqual(1);
		});
	});
});
