import { Logger } from '@shared';
import type { DataCollectionResultType } from './DataCollectionModule';
import DataCollectionModule from './DataCollectionModule';

const logger = Logger.getLogger('ModuleRegistry');

type ModuleExecutionPlanType =
{
	modules: string[];
	executionOrder: string[];
	parallelGroups: string[][];
};

type ModuleExecutionResultType =
{
	domain: string;
	results: Record<string, DataCollectionResultType>;
	totalExecutionTime: number;
	successCount: number;
	failureCount: number;
	completeness: {
		percentage: number;
		missing: string[];
		available: string[];
	};
};

/**
 * Module Registry for managing data collection modules
 */
class ModuleRegistry
{
	private modules: Map<string, DataCollectionModule> = new Map();

	private moduleConfigs: Map<string, any> = new Map();

	/**
   * Register a data collection module
   */
	registerModule(name: string, module: DataCollectionModule): void
	{
		this.modules.set(name, module);
		this.moduleConfigs.set(name, module.getConfig());
		logger.info(`Registered module: ${name}`);
	}

	/**
   * Unregister a module
   */
	unregisterModule(name: string): void
	{
		this.modules.delete(name);
		this.moduleConfigs.delete(name);
		logger.info(`Unregistered module: ${name}`);
	}

	/**
   * Get all registered module names
   */
	getModuleNames(): string[]
	{
		return Array.from(this.modules.keys());
	}

	/**
   * Get module by name
   */
	getModule(name: string): DataCollectionModule | undefined
	{
		return this.modules.get(name);
	}

	/**
   * Create execution plan for modules
   */
	createExecutionPlan(requestedModules: string[]): ModuleExecutionPlan
	{
		const availableModules = requestedModules.filter(name => this.modules.has(name));

		if (availableModules.length !== requestedModules.length)
		{
			const missing = requestedModules.filter(name => !this.modules.has(name));
			logger.warn(`Some requested modules are not available: ${missing.join(', ')}`);
		}

		// Sort modules by priority and dependencies
		const sortedModules = this.sortModulesByPriority(availableModules);
		const executionOrder = this.resolveExecutionOrder(sortedModules);
		const parallelGroups = this.createParallelGroups(executionOrder);

		return {
			modules: availableModules,
			executionOrder,
			parallelGroups,
		};
	}

	/**
   * Execute modules for a domain
   */
	async executeModules(domain: string, moduleNames: string[]): Promise<ModuleExecutionResultType>
	{
		const startTime = Date.now();
		const plan = this.createExecutionPlan(moduleNames);
		const results: Record<string, DataCollectionResultType> = {};

		logger.info(`Executing ${plan.modules.length} modules for domain: ${domain}`);

		// Execute modules in parallel groups
		for (const group of plan.parallelGroups)
		{
			const groupPromises = group.map(async (moduleName) =>
			{
				const module = this.modules.get(moduleName);
				if (!module)
				{
					return {
						name: moduleName,
						result: {
							success: false,
							error: 'Module not found',
							executionTime: 0,
							retryCount: 0,
						},
					};
				}

				const result = await module.execute(domain);
				return { name: moduleName, result };
			});

			const groupResults = await Promise.allSettled(groupPromises);

			// Process group results
			groupResults.forEach((promiseResult) =>
			{
				if (promiseResult.status === 'fulfilled')
				{
					const { name, result } = promiseResult.value;
					results[name] = result;
				}
				else
				{
					logger.error('Module execution promise failed:', promiseResult.reason);
				}
			});
		}

		const totalExecutionTime = Date.now() - startTime;
		const successCount = Object.values(results).filter(r => r.success).length;
		const failureCount = Object.values(results).filter(r => !r.success).length;

		// Calculate completeness
		const completeness = this.calculateCompleteness(results, plan.modules);

		const executionResult: ModuleExecutionResultType = {
			domain,
			results,
			totalExecutionTime,
			successCount,
			failureCount,
			completeness,
		};

		logger.info(`Module execution completed for domain: ${domain}`, {
			totalTime: totalExecutionTime,
			success: successCount,
			failures: failureCount,
			completeness: completeness.percentage,
		});

		return executionResult;
	}

	/**
   * Sort modules by priority
   */
	private sortModulesByPriority(moduleNames: string[]): string[]
	{
		return moduleNames.sort((a, b) =>
		{
			const configA = this.moduleConfigs.get(a);
			const configB = this.moduleConfigs.get(b);

			if (!configA || !configB) return 0;

			return configB.priority - configA.priority; // Higher priority first
		});
	}

	/**
   * Resolve execution order based on dependencies
   */
	private resolveExecutionOrder(moduleNames: string[]): string[]
	{
		const resolved: string[] = [];
		const resolving: Set<string> = new Set();

		const resolve = (moduleName: string) =>
		{
			if (resolved.includes(moduleName))
			{
				return;
			}

			if (resolving.has(moduleName))
			{
				throw new Error(`Circular dependency detected involving module: ${moduleName}`);
			}

			resolving.add(moduleName);

			const config = this.moduleConfigs.get(moduleName);
			if (config?.dependencies)
			{
				for (const dependency of config.dependencies)
				{
					if (moduleNames.includes(dependency))
					{
						resolve(dependency);
					}
				}
			}

			resolving.delete(moduleName);
			resolved.push(moduleName);
		};

		for (const moduleName of moduleNames)
		{
			resolve(moduleName);
		}

		return resolved;
	}

	/**
   * Create parallel execution groups
   */
	private createParallelGroups(executionOrder: string[]): string[][]
	{
		const groups: string[][] = [];
		const processed: Set<string> = new Set();

		for (const moduleName of executionOrder)
		{
			if (processed.has(moduleName))
			{
				continue;
			}

			const config = this.moduleConfigs.get(moduleName);
			const dependencies = config?.dependencies || [];

			// Check if all dependencies are already processed
			const canRunInParallel = dependencies.every(dep => processed.has(dep));

			if (canRunInParallel)
			{
				// Find or create a group for modules that can run in parallel
				let targetGroup = groups[groups.length - 1];

				if (!targetGroup)
				{
					targetGroup = [];
					groups.push(targetGroup);
				}

				// Check if this module can run with the current group
				const canRunWithGroup = targetGroup.every((groupModule) =>
				{
					const groupConfig = this.moduleConfigs.get(groupModule);
					const groupDeps = groupConfig?.dependencies || [];

					// Module can run with group if it doesn't depend on group members
					// and group members don't depend on it
					return !groupDeps.includes(moduleName) && !dependencies.includes(groupModule);
				});

				if (canRunWithGroup)
				{
					targetGroup.push(moduleName);
				}
				else
				{
					// Create new group
					groups.push([moduleName]);
				}

				processed.add(moduleName);
			}
			else
			{
				// Create new group for this module
				groups.push([moduleName]);
				processed.add(moduleName);
			}
		}

		return groups;
	}

	/**
   * Calculate data completeness
   */
	private calculateCompleteness(
		results: Record<string, DataCollectionResultType>,
		expectedModules: string[],
	): { percentage: number; missing: string[]; available: string[] }
	{
		const available: string[] = [];
		const missing: string[] = [];

		expectedModules.forEach((moduleName) =>
		{
			const result = results[moduleName];
			if (result && result.success)
			{
				available.push(moduleName);
			}
			else
			{
				missing.push(moduleName);
			}
		});

		const percentage = expectedModules.length > 0
			? Math.round((available.length / expectedModules.length) * 100)
			: 0;

		return { percentage, missing, available };
	}

	/**
   * Get module statistics
   */
	getModuleStats(): Record<string, any>
	{
		const stats: Record<string, any> = {};

		this.modules.forEach((module, name) =>
		{
			const config = module.getConfig();
			stats[name] = {
				priority: config.priority,
				timeout: config.timeout,
				retryAttempts: config.retryAttempts,
				dependencies: config.dependencies || [],
			};
		});

		return stats;
	}

	/**
   * Validate module configuration
   */
	validateConfiguration(): { valid: boolean; errors: string[] }
	{
		const errors: string[] = [];
		const moduleNames = this.getModuleNames();

		// Check for circular dependencies
		try
		{
			this.resolveExecutionOrder(moduleNames);
		}
		catch (error)
		{
			errors.push(`Dependency resolution failed: ${error.message}`);
		}

		// Check for missing dependencies
		this.modules.forEach((module, name) =>
		{
			const config = module.getConfig();
			if (config.dependencies)
			{
				const missingDeps = config.dependencies.filter(dep => !this.modules.has(dep));
				if (missingDeps.length > 0)
				{
					errors.push(`Module ${name} has missing dependencies: ${missingDeps.join(', ')}`);
				}
			}
		});

		return ({
			valid: errors.length === 0,
			errors,
		});
	}
}

export type {
	ModuleExecutionPlanType,
	ModuleExecutionResultType,
};

export default ModuleRegistry;
