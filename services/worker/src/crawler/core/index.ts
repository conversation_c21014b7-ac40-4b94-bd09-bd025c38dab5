export { DataCollectionModule } from './DataCollectionModule';
export type { DataCollectionModuleConfig, DataCollectionResult } from './DataCollectionModule';

export { ModuleRegistry } from './ModuleRegistry';
export type { ModuleExecutionPlan, ModuleExecutionResult } from './ModuleRegistry';

export { default as DataCollectionOrchestrator } from './DataCollectionOrchestrator';
export type {
	DataCollectionRequestType as DataCollectionRequest,
	DataCollectionResponseType as DataCollectionResponse,
	DataValidationResultType as DataValidationResult,
	DataQualityMetricType as DataQualityMetric,
	DataCollectionRecommendationType as DataCollectionRecommendation,
	ResourceUsageMetricsType as ResourceUsageMetrics,
} from './DataCollectionOrchestrator';

export { default as SelectiveDataCollectionService } from './SelectiveDataCollectionService';
export type {
	CollectionProfileType,
	BatchCollectionRequestType,
	BatchCollectionResultType,
} from './SelectiveDataCollectionService';


export { default as EnhancedResourceMonitor } from './EnhancedResourceMonitor';
export type {
	ResourceMetrics,
	MemoryMetrics,
	CPUMetrics,
	NetworkMetrics,
	DiskMetrics,
	ResourceThresholds,
	ResourceAlert,
	MonitoringSession,
	SessionSummary,
	ResourcePrediction,
	ResourceUsageStatistics,
} from './EnhancedResourceMonitor';

export { default as DataCollectionRecommendationEngine } from './DataCollectionRecommendationEngine';
export type {
	RecommendationContext,
	ResourceBudget,
	TimeConstraints,
	BusinessPriority,
	HistoricalPerformance,
	CollectionRecommendation as RecommendationEngineRecommendation,
	RecommendationImpact,
	RecommendationCost,
	OptimizationSuggestion,
	RecommendationSet,
	GapAnalysis,
	TimingRecommendation,
} from './DataCollectionRecommendationEngine';

export { default as ExecutionHistoryTracker } from './ExecutionHistoryTracker';
export type {
	ExecutionRecord,
	ResourceUsageRecord,
	PerformanceRecord,
	BottleneckRecord,
	ErrorRecord,
	WarningRecord,
	ExecutionPattern,
	HistoryQuery,
	HistoryAnalytics,
	ExecutionPrediction,
} from './ExecutionHistoryTracker';
