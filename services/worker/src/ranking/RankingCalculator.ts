import { logger as sharedLogger } from '@shared';
import {
	CompositeRanker,
	DomainDataType,
	RankingWeightsType,
	CompositeScoreType,
	RankingResultType,
} from './CompositeRanker';

const logger = sharedLogger.getLogger('RankingCalculator');

type RankingConfigurationType =
{
	weights?: RankingWeightsType;
	enableValidation?: boolean;
	enableMonitoring?: boolean;
	batchSize?: number;
};

type RankingValidationResultType =
{
	isValid: boolean;
	errors: string[];
	warnings: string[];
	score?: number;
};

type RankingMonitoringMetricsType =
{
	calculationTime: number;
	domainsProcessed: number;
	averageScore: number;
	errorCount: number;
	validationFailures: number;
};

type DetailedBreakdownType =
{
	domain: string;
	compositeScore: CompositeScoreType;
	detailedBreakdowns: {
		performance: unknown;
		security: unknown;
		seo: unknown;
		technical: unknown;
		backlinks: unknown;
	};
};

type RankingStatisticsType =
{
	totalDomains: number;
	averageScore: number;
	medianScore: number;
	topScore: number;
	bottomScore: number;
	gradeDistribution: Record<string, number>;
};

/**
 * Main interface for calculating domain rankings with validation and monitoring
 */
class RankingCalculator
{
	private ranker: CompositeRanker;
	private config: RankingConfigurationType;
	private monitoringMetrics: RankingMonitoringMetricsType;

	constructor(config: RankingConfigurationType = {})
	{
		this.config =
		{
			enableValidation: true,
			enableMonitoring: true,
			batchSize: 100,
			...config,
		};

		this.ranker = new CompositeRanker(config.weights);
		this.monitoringMetrics =
		{
			calculationTime: 0,
			domainsProcessed: 0,
			averageScore: 0,
			errorCount: 0,
			validationFailures: 0,
		};

		logger.info('RankingCalculator initialized', { config: this.config });
	}

	/**
	 * Calculate ranking score for a single domain
	 */
	async calculateDomainScore(domainData: DomainDataType): Promise<CompositeScoreType>
	{
		const startTime = Date.now();

		try
		{
			// Validate input data if enabled
			if (this.config.enableValidation)
			{
				const validation = this.validateDomainData(domainData);
				if (!validation.isValid)
				{
					this.monitoringMetrics.validationFailures++;
					logger.warn('Domain data validation failed', {
						domain: domainData.domain,
						errors: validation.errors,
					});
				}
			}

			// Calculate composite score
			const score = this.ranker.calculateCompositeScore(domainData);

			// Update monitoring metrics
			if (this.config.enableMonitoring)
			{
				this.updateMonitoringMetrics(startTime, 1, score.overallScore, 0);
			}

			logger.debug('Domain score calculated successfully', {
				domain: domainData.domain,
				score: score.overallScore,
				grade: score.grade,
			});

			return score;
		}
		catch (error)
		{
			this.monitoringMetrics.errorCount++;
			logger.error('Error calculating domain score', {
				domain: domainData.domain,
				error: error instanceof Error ? error.message : String(error),
			});
			throw error;
		}
	}

	/**
	 * Calculate rankings for multiple domains
	 */
	async calculateBatchRankings(domains: DomainDataType[]): Promise<RankingResultType[]>
	{
		const startTime = Date.now();
		let processedCount = 0;
		let totalScore = 0;
		let errorCount = 0;

		try
		{
			logger.info(`Starting batch ranking calculation for ${domains.length} domains`);

			// Process domains in batches to manage memory and performance
			const batchSize = this.config.batchSize || 100;
			const rankings: RankingResultType[] = [];

			for (let i = 0; i < domains.length; i += batchSize)
			{
				const batch = domains.slice(i, i + batchSize);
				logger.debug(`Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(domains.length / batchSize)}`);

				try
				{
					// Validate batch if enabled
					if (this.config.enableValidation)
					{
						const validDomains = batch.filter((domain) =>
						{
							const validation = this.validateDomainData(domain);
							if (!validation.isValid)
							{
								this.monitoringMetrics.validationFailures++;
								logger.warn('Skipping invalid domain data', {
									domain: domain.domain,
									errors: validation.errors,
								});
								return false;
							}
							return true;
						});

						if (validDomains.length !== batch.length)
						{
							logger.warn(`Filtered ${batch.length - validDomains.length} invalid domains from batch`);
						}

						// Calculate rankings for valid domains
						const batchRankings = this.ranker.calculateGlobalRankings(validDomains);
						rankings.push(...batchRankings);

						// Update metrics
						processedCount += validDomains.length;
						totalScore += batchRankings.reduce((sum, r) => sum + r.score, 0);
					}
					else
					{
						// Calculate rankings without validation
						const batchRankings = this.ranker.calculateGlobalRankings(batch);
						rankings.push(...batchRankings);

						// Update metrics
						processedCount += batch.length;
						totalScore += batchRankings.reduce((sum, r) => sum + r.score, 0);
					}
				}
				catch (error)
				{
					errorCount++;
					logger.error('Error processing batch', {
						batchIndex: Math.floor(i / batchSize),
						error: error instanceof Error ? error.message : String(error),
					});
				}
			}

			// Sort final rankings globally
			rankings.sort((a, b) => b.score - a.score);

			// Reassign ranks after global sorting
			rankings.forEach((ranking, index) =>
			{
				ranking.rank = index + 1;
			});

			// Update monitoring metrics
			if (this.config.enableMonitoring)
			{
				const averageScore = processedCount > 0 ? totalScore / processedCount : 0;
				this.updateMonitoringMetrics(startTime, processedCount, averageScore, errorCount);
			}

			logger.info('Batch ranking calculation completed', {
				totalDomains: domains.length,
				processedDomains: processedCount,
				errorCount,
				averageScore: processedCount > 0 ? totalScore / processedCount : 0,
			});

			return rankings;
		}
		catch (error)
		{
			logger.error('Error in batch ranking calculation', {
				error: error instanceof Error ? error.message : String(error),
			});
			throw error;
		}
	}

	/**
	 * Calculate category-specific rankings
	 */
	async calculateCategoryRankings(
		domains: DomainDataType[],
		category: string,
	): Promise<RankingResultType[]>
	{
		const startTime = Date.now();

		try
		{
			logger.info(`Calculating category rankings for: ${category}`);

			// Filter domains by category
			const categoryDomains = domains.filter(domain => domain.category === category);

			if (categoryDomains.length === 0)
			{
				logger.warn(`No domains found for category: ${category}`);
				return [];
			}

			// Validate domains if enabled
			let validDomains = categoryDomains;
			if (this.config.enableValidation)
			{
				validDomains = categoryDomains.filter((domain) =>
				{
					const validation = this.validateDomainData(domain);
					if (!validation.isValid)
					{
						this.monitoringMetrics.validationFailures++;
						logger.warn('Skipping invalid domain data', {
							domain: domain.domain,
							category,
							errors: validation.errors,
						});
						return false;
					}
					return true;
				});
			}

			// Calculate category rankings
			const rankings = this.ranker.calculateCategoryRankings(validDomains, category);

			// Update monitoring metrics
			if (this.config.enableMonitoring)
			{
				const averageScore = rankings.length > 0
					? rankings.reduce((sum, r) => sum + r.score, 0) / rankings.length
					: 0;
				this.updateMonitoringMetrics(startTime, rankings.length, averageScore, 0);
			}

			logger.info(`Category rankings calculated for ${category}`, {
				totalDomains: categoryDomains.length,
				validDomains: validDomains.length,
				rankings: rankings.length,
			});

			return rankings;
		}
		catch (error)
		{
			this.monitoringMetrics.errorCount++;
			logger.error('Error calculating category rankings', {
				category,
				error: error instanceof Error ? error.message : String(error),
			});
			throw error;
		}
	}

	/**
	 * Get detailed breakdown for a domain
	 */
	async getDetailedBreakdown(domainData: DomainDataType): Promise<DetailedBreakdownType>
	{
		try
		{
			// Validate domain data if enabled
			if (this.config.enableValidation)
			{
				const validation = this.validateDomainData(domainData);
				if (!validation.isValid)
				{
					logger.warn('Domain data validation failed for detailed breakdown', {
						domain: domainData.domain,
						errors: validation.errors,
					});
				}
			}

			const breakdown = this.ranker.getDetailedBreakdown(domainData);

			logger.debug('Detailed breakdown generated', {
				domain: domainData.domain,
				overallScore: breakdown.compositeScore.overallScore,
			});

			return breakdown;
		}
		catch (error)
		{
			logger.error('Error generating detailed breakdown', {
				domain: domainData.domain,
				error: error instanceof Error ? error.message : String(error),
			});
			throw error;
		}
	}

	/**
	 * Get comprehensive recommendations for a domain
	 */
	async getRecommendations(domainData: DomainDataType): Promise<string[]>
	{
		try
		{
			const recommendations = this.ranker.getComprehensiveRecommendations(domainData);

			logger.debug('Recommendations generated', {
				domain: domainData.domain,
				recommendationCount: recommendations.length,
			});

			return recommendations;
		}
		catch (error)
		{
			logger.error('Error generating recommendations', {
				domain: domainData.domain,
				error: error instanceof Error ? error.message : String(error),
			});
			throw error;
		}
	}

	/**
	 * Validate domain data for completeness and quality
	 */
	private validateDomainData(domainData: DomainDataType): RankingValidationResultType
	{
		const errors: string[] = [];
		const warnings: string[] = [];

		// Basic validation
		if (!domainData.domain)
		{
			errors.push('Domain name is required');
		}

		// Performance metrics validation
		if (domainData.performanceMetrics)
		{
			const perf = domainData.performanceMetrics;
			if (perf.loadTime && perf.loadTime < 0)
			{
				errors.push('Load time cannot be negative');
			}
			if (perf.pageSize && perf.pageSize < 0)
			{
				errors.push('Page size cannot be negative');
			}
			if (perf.resourceCount && perf.resourceCount < 0)
			{
				errors.push('Resource count cannot be negative');
			}
		}
		else
		{
			warnings.push('No performance metrics available');
		}

		// Security metrics validation
		if (domainData.securityMetrics)
		{
			const security = domainData.securityMetrics;
			if (security.sslExpiration && security.sslExpiration < new Date())
			{
				warnings.push('SSL certificate has expired');
			}
		}
		else
		{
			warnings.push('No security metrics available');
		}

		// SEO metrics validation
		if (!domainData.seoMetrics)
		{
			warnings.push('No SEO metrics available');
		}

		// Technical metrics validation
		if (domainData.technicalMetrics)
		{
			const tech = domainData.technicalMetrics;
			if (tech.dnsResponseTime && tech.dnsResponseTime < 0)
			{
				errors.push('DNS response time cannot be negative');
			}
			if (tech.uptimePercentage && (tech.uptimePercentage < 0 || tech.uptimePercentage > 100))
			{
				errors.push('Uptime percentage must be between 0 and 100');
			}
		}
		else
		{
			warnings.push('No technical metrics available');
		}

		// Backlink metrics validation
		if (domainData.backlinkMetrics)
		{
			const backlinks = domainData.backlinkMetrics;
			if (backlinks.totalBacklinks < 0)
			{
				errors.push('Total backlinks cannot be negative');
			}
			if (backlinks.uniqueDomains < 0)
			{
				errors.push('Unique domains cannot be negative');
			}
			if (backlinks.averageAuthority < 0 || backlinks.averageAuthority > 100)
			{
				errors.push('Average authority must be between 0 and 100');
			}
		}
		else
		{
			warnings.push('No backlink metrics available');
		}

		return {
			isValid: errors.length === 0,
			errors,
			warnings,
		};
	}

	/**
	 * Update monitoring metrics
	 */
	private updateMonitoringMetrics(
		startTime: number,
		domainsProcessed: number,
		averageScore: number,
		errorCount: number,
	): void
	{
		const calculationTime = Date.now() - startTime;

		this.monitoringMetrics.calculationTime += calculationTime;
		this.monitoringMetrics.domainsProcessed += domainsProcessed;
		this.monitoringMetrics.averageScore = (
			(
				this.monitoringMetrics.averageScore
				* (this.monitoringMetrics.domainsProcessed - domainsProcessed)
			)
			+ (averageScore * domainsProcessed)
		) / this.monitoringMetrics.domainsProcessed;
		this.monitoringMetrics.errorCount += errorCount;
	}

	/**
	 * Get current monitoring metrics
	 */
	getMonitoringMetrics(): RankingMonitoringMetricsType
	{
		return ({ ...this.monitoringMetrics });
	}

	/**
	 * Reset monitoring metrics
	 */
	resetMonitoringMetrics(): void
	{
		this.monitoringMetrics =
		{
			calculationTime: 0,
			domainsProcessed: 0,
			averageScore: 0,
			errorCount: 0,
			validationFailures: 0,
		};
	}

	/**
	 * Update ranking weights
	 */
	updateWeights(newWeights: Partial<RankingWeightsType>): void
	{
		this.ranker.updateWeights(newWeights);
		logger.info('Ranking weights updated', { weights: newWeights });
	}

	/**
	 * Get current ranking weights
	 */
	getWeights(): RankingWeightsType
	{
		return this.ranker.getWeights();
	}

	/**
	 * Get ranking statistics
	 */
	getRankingStatistics(rankings: RankingResultType[]): RankingStatisticsType
	{
		return this.ranker.getRankingStatistics(rankings);
	}
}

export type {
	RankingConfigurationType,
	RankingValidationResultType,
	RankingMonitoringMetricsType,
};

export default RankingCalculator;
