import { logger as sharedLogger } from '@shared';
import type { PerformanceMetrics, CoreWebVitals } from './scorers/PerformanceScorer';
import PerformanceScorer from './scorers/PerformanceScorer';
import { SecurityScorer } from './scorers/SecurityScorer';
import type { SecurityMetricsType } from './scorers/SecurityScorer';
import { SEOScorer } from './scorers/SEOScorer';
import type { SEOMetrics } from './scorers/SEOScorer';
import { TechnicalScorer } from './scorers/TechnicalScorer';
import type { TechnicalMetricsType } from './scorers/TechnicalScorer';
import { BacklinkScorer } from './scorers/BacklinkScorer';
import type { BacklinkMetricsType } from './scorers/BacklinkScorer';

const logger = sharedLogger.getLogger('CompositeRanker');

type DomainDataType =
{
	domain: string;
	category?: string;
	country?: string;
	performanceMetrics?: PerformanceMetrics;
	coreWebVitals?: CoreWebVitals;
	securityMetrics?: SecurityMetricsType;
	seoMetrics?: SEOMetrics;
	technicalMetrics?: TechnicalMetricsType;
	backlinkMetrics?: BacklinkMetricsType;
	trafficEstimate?: number;
	lastUpdated?: Date;
};

type RankingWeightsType =
{
	PERFORMANCE: number;
	SECURITY: number;
	SEO: number;
	TECHNICAL: number;
	BACKLINKS: number;
};

type CompositeScoreType =
{
	domain: string;
	overallScore: number;
	grade: string;
	globalRank?: number;
	categoryRank?: number;
	scores: {
		performance: number;
		security: number;
		seo: number;
		technical: number;
		backlinks: number;
	};
	breakdown: {
		performance: ScoreBreakdownType;
		security: ScoreBreakdownType;
		seo: ScoreBreakdownType;
		technical: ScoreBreakdownType;
		backlinks: ScoreBreakdownType;
	};
	lastCalculated: Date;
};

type ScoreBreakdownType =
{
	score: number;
	weight: number;
	contribution: number;
	grade: string;
};

type RankingResultType =
{
	domain: string;
	rank: number;
	score: number;
	category?: string;
	previousRank?: number;
	rankChange?: number;
};

type RankingStatisticsType =
{
	totalDomains: number;
	averageScore: number;
	medianScore: number;
	topScore: number;
	bottomScore: number;
	gradeDistribution: Record<string, number>;
};

type DetailedBreakdownType =
{
	domain: string;
	compositeScore: CompositeScoreType;
	detailedBreakdowns: {
		performance: unknown;
		security: unknown;
		seo: unknown;
		technical: unknown;
		backlinks: unknown;
	};
};

/**
 * Composite ranking algorithm that combines all scoring dimensions
 */
class CompositeRanker
{
	private weights: RankingWeightsType;

	constructor(weights?: RankingWeightsType)
	{
		this.weights = weights || {
			PERFORMANCE: 0.25,
			SECURITY: 0.20,
			SEO: 0.20,
			TECHNICAL: 0.15,
			BACKLINKS: 0.20,
		};

		// Validate weights sum to 1.0
		const totalWeight = Object.values(this.weights).reduce((sum, weight) => sum + weight, 0);
		if (Math.abs(totalWeight - 1.0) > 0.001)
		{
			logger.warn('Ranking weights do not sum to 1.0', { weights: this.weights, total: totalWeight });
		}
	}

	/**
	 * Calculate composite score for a single domain
	 */
	calculateCompositeScore(domainData: DomainDataType): CompositeScoreType
	{
		try
		{
			// Calculate individual scores
			const performanceScore = this.calculatePerformanceScore(domainData);
			const securityScore = this.calculateSecurityScore(domainData);
			const seoScore = this.calculateSEOScore(domainData);
			const technicalScore = this.calculateTechnicalScore(domainData);
			const backlinkScore = this.calculateBacklinkScore(domainData);

			// Calculate weighted overall score
			const overallScore = (
				performanceScore * this.weights.PERFORMANCE
				+ securityScore * this.weights.SECURITY
				+ seoScore * this.weights.SEO
				+ technicalScore * this.weights.TECHNICAL
				+ backlinkScore * this.weights.BACKLINKS
			);

			const finalScore = Math.round(overallScore * 1000) / 1000;

			const result: CompositeScoreType = {
				domain: domainData.domain,
				overallScore: finalScore,
				grade: this.getOverallGrade(finalScore),
				scores: {
					performance: Math.round(performanceScore * 1000) / 1000,
					security: Math.round(securityScore * 1000) / 1000,
					seo: Math.round(seoScore * 1000) / 1000,
					technical: Math.round(technicalScore * 1000) / 1000,
					backlinks: Math.round(backlinkScore * 1000) / 1000,
				},
				breakdown: {
					performance: {
						score: Math.round(performanceScore * 1000) / 1000,
						weight: this.weights.PERFORMANCE,
						contribution: Math.round(performanceScore * this.weights.PERFORMANCE * 1000) / 1000,
						grade: PerformanceScorer.getPerformanceGrade(performanceScore),
					},
					security: {
						score: Math.round(securityScore * 1000) / 1000,
						weight: this.weights.SECURITY,
						contribution: Math.round(securityScore * this.weights.SECURITY * 1000) / 1000,
						grade: SecurityScorer.getSecurityGrade(securityScore),
					},
					seo: {
						score: Math.round(seoScore * 1000) / 1000,
						weight: this.weights.SEO,
						contribution: Math.round(seoScore * this.weights.SEO * 1000) / 1000,
						grade: SEOScorer.getSEOGrade(seoScore),
					},
					technical: {
						score: Math.round(technicalScore * 1000) / 1000,
						weight: this.weights.TECHNICAL,
						contribution: Math.round(technicalScore * this.weights.TECHNICAL * 1000) / 1000,
						grade: TechnicalScorer.getTechnicalGrade(technicalScore),
					},
					backlinks: {
						score: Math.round(backlinkScore * 1000) / 1000,
						weight: this.weights.BACKLINKS,
						contribution: Math.round(backlinkScore * this.weights.BACKLINKS * 1000) / 1000,
						grade: BacklinkScorer.getBacklinkGrade(backlinkScore),
					},
				},
				lastCalculated: new Date(),
			};

			logger.debug('Composite score calculated', {
				domain: domainData.domain,
				overallScore: finalScore,
				scores: result.scores,
			});

			return result;
		}
		catch (error)
		{
			logger.error('Error calculating composite score:', error);
			throw error;
		}
	}

	/**
	 * Calculate performance score using PerformanceScorer
	 */
	private calculatePerformanceScore(domainData: DomainDataType): number
	{
		if (!domainData.performanceMetrics)
		{
			return 0.5; // Default score when no performance data
		}

		return PerformanceScorer.calculateScore(
			domainData.performanceMetrics,
			domainData.coreWebVitals,
		);
	}

	/**
	 * Calculate security score using SecurityScorer
	 */
	private calculateSecurityScore(domainData: DomainDataType): number
	{
		if (!domainData.securityMetrics)
		{
			return 0.3; // Conservative default for security
		}

		return SecurityScorer.calculateScore(domainData.securityMetrics);
	}

	/**
	 * Calculate SEO score using SEOScorer
	 */
	private calculateSEOScore(domainData: DomainDataType): number
	{
		if (!domainData.seoMetrics)
		{
			return 0.5; // Default score when no SEO data
		}

		return SEOScorer.calculateScore(domainData.seoMetrics);
	}

	/**
	 * Calculate technical score using TechnicalScorer
	 */
	private calculateTechnicalScore(domainData: DomainDataType): number
	{
		if (!domainData.technicalMetrics)
		{
			return 0.5; // Default score when no technical data
		}

		return TechnicalScorer.calculateScore(domainData.technicalMetrics);
	}

	/**
	 * Calculate backlink score using BacklinkScorer
	 */
	private calculateBacklinkScore(domainData: DomainDataType): number
	{
		if (!domainData.backlinkMetrics)
		{
			return 0.3; // Default score when no backlink data
		}

		return BacklinkScorer.calculateScore(domainData.backlinkMetrics);
	}

	/**
	 * Calculate global rankings for a list of domains
	 */
	calculateGlobalRankings(domains: DomainDataType[]): RankingResultType[]
	{
		try
		{
			logger.info(`Calculating global rankings for ${domains.length} domains`);

			// Calculate composite scores for all domains
			const scoredDomains = domains.map(domain => ({
				domain: domain.domain,
				score: this.calculateCompositeScore(domain).overallScore,
				category: domain.category,
			}));

			// Sort by score (highest first)
			scoredDomains.sort((a, b) => b.score - a.score);

			// Assign ranks
			const rankings: RankingResultType[] = scoredDomains.map((domain, index) => ({
				domain: domain.domain,
				rank: index + 1,
				score: domain.score,
				category: domain.category,
			}));

			logger.info(`Global rankings calculated for ${rankings.length} domains`);
			return rankings;
		}
		catch (error)
		{
			logger.error('Error calculating global rankings:', error);
			throw error;
		}
	}

	/**
	 * Calculate category-based rankings
	 */
	calculateCategoryRankings(domains: DomainDataType[], category: string): RankingResultType[]
	{
		try
		{
			logger.info(`Calculating category rankings for category: ${category}`);

			// Filter domains by category
			const categoryDomains = domains.filter(domain => domain.category === category);

			if (categoryDomains.length === 0)
			{
				logger.warn(`No domains found for category: ${category}`);
				return [];
			}

			// Calculate composite scores for category domains
			const scoredDomains = categoryDomains.map(domain => ({
				domain: domain.domain,
				score: this.calculateCompositeScore(domain).overallScore,
				category: domain.category,
			}));

			// Sort by score (highest first)
			scoredDomains.sort((a, b) => b.score - a.score);

			// Assign category ranks
			const rankings: RankingResultType[] = scoredDomains.map((domain, index) => ({
				domain: domain.domain,
				rank: index + 1,
				score: domain.score,
				category,
			}));

			logger.info(`Category rankings calculated for ${rankings.length} domains in category: ${category}`);
			return rankings;
		}
		catch (error)
		{
			logger.error(`Error calculating category rankings for ${category}:`, error);
			throw error;
		}
	}

	/**
	 * Calculate rankings with rank change tracking
	 */
	calculateRankingsWithHistory(
		domains: DomainDataType[],
		previousRankings?: Map<string, number>,
		category?: string,
	): RankingResultType[]
	{
		try
		{
			// Calculate current rankings
			const currentRankings = category
				? this.calculateCategoryRankings(domains, category)
				: this.calculateGlobalRankings(domains);

			// Add rank change information if previous rankings available
			if (previousRankings)
			{
				currentRankings.forEach((ranking) =>
				{
					const previousRank = previousRankings.get(ranking.domain);
					if (previousRank !== undefined)
					{
						ranking.previousRank = previousRank;
						ranking.rankChange = previousRank - ranking.rank; // Positive = moved up
					}
				});
			}

			return currentRankings;
		}
		catch (error)
		{
			logger.error('Error calculating rankings with history:', error);
			throw error;
		}
	}

	/**
	 * Get overall grade based on composite score
	 */
	private getOverallGrade(score: number): string
	{
		if (score >= 0.9) return 'A+';
		if (score >= 0.8) return 'A';
		if (score >= 0.7) return 'B';
		if (score >= 0.6) return 'C';
		if (score >= 0.5) return 'D';
		return 'F';
	}

	/**
	 * Update ranking weights
	 */
	updateWeights(newWeights: Partial<RankingWeightsType>): void
	{
		this.weights = { ...this.weights, ...newWeights };

		// Validate weights sum to 1.0
		const totalWeight = Object.values(this.weights).reduce((sum, weight) => sum + weight, 0);
		if (Math.abs(totalWeight - 1.0) > 0.001)
		{
			logger.warn('Updated ranking weights do not sum to 1.0', {
				weights: this.weights,
				total: totalWeight,
			});
		}

		logger.info('Ranking weights updated', { weights: this.weights });
	}

	/**
	 * Get current ranking weights
	 */
	getWeights(): RankingWeightsType
	{
		return { ...this.weights };
	}

	/**
	 * Get ranking statistics for a set of domains
	 */
	getRankingStatistics(rankings: RankingResultType[]): RankingStatisticsType
	{
		if (rankings.length === 0)
		{
			return {
				totalDomains: 0,
				averageScore: 0,
				medianScore: 0,
				topScore: 0,
				bottomScore: 0,
				gradeDistribution: {},
			};
		}

		const scores = rankings.map(r => r.score).sort((a, b) => b - a);
		const averageScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;
		const medianScore = scores[Math.floor(scores.length / 2)];

		// Grade distribution
		const gradeDistribution: Record<string, number> = {};
		rankings.forEach((ranking) =>
		{
			const grade = this.getOverallGrade(ranking.score);
			gradeDistribution[grade] = (gradeDistribution[grade] || 0) + 1;
		});

		return {
			totalDomains: rankings.length,
			averageScore: Math.round(averageScore * 1000) / 1000,
			medianScore: Math.round(medianScore * 1000) / 1000,
			topScore: scores[0],
			bottomScore: scores[scores.length - 1],
			gradeDistribution,
		};
	}

	/**
	 * Get detailed breakdown for all scoring dimensions
	 */
	getDetailedBreakdown(domainData: DomainDataType): DetailedBreakdownType
	{
		const performanceBreakdown = domainData.performanceMetrics
			? PerformanceScorer.getPerformanceBreakdown(
				domainData.performanceMetrics,
				domainData.coreWebVitals,
			)
			: null;

		const securityBreakdown = domainData.securityMetrics
			? SecurityScorer.getSecurityBreakdown(domainData.securityMetrics)
			: null;

		const seoBreakdown = domainData.seoMetrics
			? SEOScorer.getSEOBreakdown(domainData.seoMetrics)
			: null;

		const technicalBreakdown = domainData.technicalMetrics
			? TechnicalScorer.getTechnicalBreakdown(domainData.technicalMetrics)
			: null;

		const backlinkBreakdown = domainData.backlinkMetrics
			? BacklinkScorer.getBacklinkBreakdown(domainData.backlinkMetrics)
			: null;

		const compositeScore = this.calculateCompositeScore(domainData);

		return {
			domain: domainData.domain,
			compositeScore,
			detailedBreakdowns: {
				performance: performanceBreakdown,
				security: securityBreakdown,
				seo: seoBreakdown,
				technical: technicalBreakdown,
				backlinks: backlinkBreakdown,
			},
		};
	}

	/**
	 * Get comprehensive recommendations for all scoring dimensions
	 */
	getComprehensiveRecommendations(domainData: DomainDataType): string[]
	{
		const recommendations: string[] = [];

		// Performance recommendations
		if (domainData.performanceMetrics)
		{
			// PerformanceScorer doesn't have recommendations method, so we'll add basic ones
			const perfScore = this.calculatePerformanceScore(domainData);
			if (perfScore < 0.7)
			{
				recommendations.push('Improve website performance by optimizing images, reducing server response time, and implementing caching');
			}
		}

		// Security recommendations
		if (domainData.securityMetrics)
		{
			const securityRecs = SecurityScorer.getSecurityRecommendations(domainData.securityMetrics);
			recommendations.push(...securityRecs);
		}

		// SEO recommendations
		if (domainData.seoMetrics)
		{
			const seoRecs = SEOScorer.getSEORecommendations(domainData.seoMetrics);
			recommendations.push(...seoRecs);
		}

		// Technical recommendations
		if (domainData.technicalMetrics)
		{
			const technicalRecs = TechnicalScorer.getTechnicalRecommendations(domainData.technicalMetrics);
			recommendations.push(...technicalRecs);
		}

		// Backlink recommendations
		if (domainData.backlinkMetrics)
		{
			const backlinkRecs = BacklinkScorer.getBacklinkRecommendations(domainData.backlinkMetrics);
			recommendations.push(...backlinkRecs);
		}

		return recommendations;
	}
}

export type {
	DomainDataType,
	RankingWeightsType,
	CompositeScoreType,
	ScoreBreakdownType,
	RankingResultType,
	RankingStatisticsType,
	DetailedBreakdownType,
};

export { CompositeRanker };

export default CompositeRanker;
