/**
 * Comprehensive demonstration of the extracted ranking functionality
 * This shows how the CompositeRanker and RankingUpdateService work together
 * to provide complete ranking calculation, batch processing, and history tracking
 */

import { CompositeRanker, RankingUpdateService, RankingManager } from '../index';
import { WorkerDatabaseManager } from '../../database/WorkerDatabaseManager';
import { WorkerJobQueue } from '../../queue/WorkerJobQueue';
import { logger, type LoggerInstanceType } from '@shared';
import type {
	DomainDataType,
	RankingWeightsType,
	RankingManagerConfigType,
} from '../index';

// Create demo-specific logger
const demoLogger: LoggerInstanceType = logger.getLogger('ranking-demo');

/**
 * Demo: Basic ranking calculation with CompositeRanker
 */
async function demonstrateBasicRanking(): Promise<void>
{
	demoLogger.info({
		demo: 'basic-ranking',
		stage: 'start',
	}, 'Basic Ranking Calculation Demo');

	// Create a CompositeRanker with custom weights
	const customWeights: RankingWeightsType = {
		PERFORMANCE: 0.3,  // Emphasize performance
		SECURITY: 0.25,    // High security importance
		SEO: 0.2,          // Standard SEO weight
		TECHNICAL: 0.15,   // Technical infrastructure
		BACKLINKS: 0.1,    // Lower backlink weight
	};

	const ranker = new CompositeRanker(customWeights);

	// Sample domain data
	const domainData: DomainDataType = {
		domain: 'example-tech.com',
		category: 'technology',
		performanceMetrics: {
			loadTime: 850,
			firstContentfulPaint: 600,
			largestContentfulPaint: 1200,
			cumulativeLayoutShift: 0.03,
			firstInputDelay: 45,
			speedIndex: 1800,
			responseTime: 120,
			pageSize: 1.5 * 1024 * 1024, // 1.5MB
			resourceCount: 32,
		},
		coreWebVitals: {
			lcp: 1.2,
			fid: 0.045,
			cls: 0.03,
			fcp: 0.6,
			ttfb: 0.12,
		},
		securityMetrics: {
			sslGrade: 'A+',
			sslIssuer: 'Let\'s Encrypt',
			sslExpiration: new Date(Date.now() + 120 * 24 * 60 * 60 * 1000), // 120 days
			usesHttps: true,
			securityHeaders: {
				hsts: true,
				csp: true,
				xframe: true,
				xssProtection: true,
				contentTypeOptions: true,
				referrerPolicy: true,
				permissionsPolicy: true,
			},
		},
		seoMetrics: {
			metaTags: {
				title: 'Example Tech - Innovative Solutions',
				description: 'Leading technology company providing cutting-edge solutions for modern businesses.',
				keywords: ['technology', 'innovation', 'solutions', 'software'],
				canonical: 'https://example-tech.com/',
				robots: 'index,follow',
			},
			robotsTxt: {
				exists: true,
				isValid: true,
				allowsCrawling: true,
				hasDisallowRules: false,
				hasSitemapReference: true,
				userAgents: ['*'],
			},
			sitemap: {
				exists: true,
				isValid: true,
				urlCount: 250,
				hasImages: true,
				hasVideos: true,
				hasNews: false,
			},
			contentMetrics: {
				wordCount: 1500,
				headingStructure: {
					h1Count: 1,
					h2Count: 6,
					h3Count: 12,
					h4Count: 4,
					h5Count: 0,
					h6Count: 0,
					hasProperHierarchy: true,
				},
				imageCount: 18,
				imageAltTextRatio: 0.95,
				internalLinkCount: 25,
				externalLinkCount: 8,
				readabilityScore: 72,
				languageDetected: 'en',
			},
			technicalSEO: {
				pageLoadSpeed: 0.85,
				mobileOptimized: true,
				httpsEnabled: true,
				hasCanonical: true,
				hasHreflang: true,
				breadcrumbsPresent: true,
				schemaMarkupPresent: true,
			},
		},
		technicalMetrics: {
			dnsResponseTime: 35,
			serverResponseTime: 120,
			supportsIpv6: true,
			hasCdn: true,
			compressionEnabled: true,
			keepAliveEnabled: true,
			serverSoftware: 'nginx/1.22.0',
			hostingProvider: 'AWS',
			uptimePercentage: 99.95,
		},
		backlinkMetrics: {
			totalBacklinks: 2500,
			uniqueDomains: 150,
			averageAuthority: 72,
			followLinks: 1800,
			nofollowLinks: 700,
			topReferringDomains: [
				'tech-news.com',
				'industry-leader.com',
				'innovation-blog.com',
				'startup-hub.com',
			],
		},
		trafficEstimate: 125000,
		lastUpdated: new Date(),
	};

	// Calculate composite score
	const compositeScore = ranker.calculateCompositeScore(domainData);

	demoLogger.info({
		demo: 'basic-ranking',
		domain: compositeScore.domain,
		overallScore: compositeScore.overallScore,
		grade: compositeScore.grade,
		scoreBreakdown: {
			performance: compositeScore.scores.performance,
			security: compositeScore.scores.security,
			seo: compositeScore.scores.seo,
			technical: compositeScore.scores.technical,
			backlinks: compositeScore.scores.backlinks,
		},
	}, 'Composite Score Calculated');

	demoLogger.info({
		demo: 'basic-ranking',
		domain: compositeScore.domain,
		contributionBreakdown: Object.fromEntries(
			Object.entries(compositeScore.breakdown).map(([dimension, breakdown]) => [
				dimension,
				{ contribution: breakdown.contribution, grade: breakdown.grade },
			])
		),
	}, 'Score Contribution Breakdown');

	// Get detailed breakdown
	const detailedBreakdown = ranker.getDetailedBreakdown(domainData);
	demoLogger.info({
		demo: 'basic-ranking',
		domain: compositeScore.domain,
		detailedBreakdownsAvailable: Object.keys(detailedBreakdown.detailedBreakdowns),
	}, 'Detailed Breakdown Available');

	// Get recommendations
	const recommendations = ranker.getComprehensiveRecommendations(domainData);
	demoLogger.info({
		demo: 'basic-ranking',
		domain: compositeScore.domain,
		recommendationCount: recommendations.length,
		recommendations: recommendations.map((rec, index) => ({ order: index + 1, recommendation: rec })),
	}, 'Ranking Recommendations Generated');
}

/**
 * Demo: Batch ranking with multiple domains
 */
async function demonstrateBatchRanking(): Promise<void>
{
	demoLogger.info({
		demo: 'batch-ranking',
		stage: 'start',
	}, 'Batch Ranking Demo');

	const ranker = new CompositeRanker();

	// Create sample domains with varying quality
	const domains: DomainDataType[] = [
		{
			domain: 'excellent-site.com',
			category: 'technology',
			performanceMetrics: {
				loadTime: 500,
				firstContentfulPaint: 400,
				largestContentfulPaint: 800,
				cumulativeLayoutShift: 0.01,
				firstInputDelay: 20,
				speedIndex: 1200,
				responseTime: 80,
				pageSize: 800 * 1024, // 800KB
				resourceCount: 20,
			},
			securityMetrics: {
				sslGrade: 'A+',
				usesHttps: true,
				securityHeaders: {
					hsts: true,
					csp: true,
					xframe: true,
					xssProtection: true,
					contentTypeOptions: true,
					referrerPolicy: true,
					permissionsPolicy: true,
				},
			},
			backlinkMetrics: {
				totalBacklinks: 5000,
				uniqueDomains: 200,
				averageAuthority: 85,
				followLinks: 4000,
				nofollowLinks: 1000,
				topReferringDomains: ['authority-site.com'],
			},
		},
		{
			domain: 'average-site.com',
			category: 'technology',
			performanceMetrics: {
				loadTime: 1500,
				firstContentfulPaint: 1000,
				largestContentfulPaint: 2000,
				cumulativeLayoutShift: 0.08,
				firstInputDelay: 100,
				speedIndex: 2500,
				responseTime: 300,
				pageSize: 3 * 1024 * 1024, // 3MB
				resourceCount: 60,
			},
			securityMetrics: {
				sslGrade: 'B',
				usesHttps: true,
				securityHeaders: {
					hsts: true,
					csp: false,
					xframe: true,
					xssProtection: true,
					contentTypeOptions: false,
					referrerPolicy: false,
					permissionsPolicy: false,
				},
			},
			backlinkMetrics: {
				totalBacklinks: 500,
				uniqueDomains: 50,
				averageAuthority: 45,
				followLinks: 300,
				nofollowLinks: 200,
				topReferringDomains: ['medium-site.com'],
			},
		},
		{
			domain: 'poor-site.com',
			category: 'technology',
			performanceMetrics: {
				loadTime: 5000,
				firstContentfulPaint: 3000,
				largestContentfulPaint: 6000,
				cumulativeLayoutShift: 0.25,
				firstInputDelay: 500,
				speedIndex: 8000,
				responseTime: 2000,
				pageSize: 10 * 1024 * 1024, // 10MB
				resourceCount: 150,
			},
			securityMetrics: {
				sslGrade: 'F',
				usesHttps: false,
				securityHeaders: {
					hsts: false,
					csp: false,
					xframe: false,
					xssProtection: false,
					contentTypeOptions: false,
					referrerPolicy: false,
					permissionsPolicy: false,
				},
			},
			backlinkMetrics: {
				totalBacklinks: 10,
				uniqueDomains: 5,
				averageAuthority: 15,
				followLinks: 5,
				nofollowLinks: 5,
				topReferringDomains: ['low-quality.com'],
			},
		},
	];

	// Calculate global rankings
	const globalRankings = ranker.calculateGlobalRankings(domains);

	demoLogger.info({
		demo: 'batch-ranking',
		rankingType: 'global',
		domainCount: domains.length,
		rankings: globalRankings.map(ranking => ({
			rank: ranking.rank,
			domain: ranking.domain,
			score: Number(ranking.score.toFixed(3))
		})),
	}, 'Global Rankings Calculated');

	// Calculate category rankings
	const categoryRankings = ranker.calculateCategoryRankings(domains, 'technology');

	demoLogger.info({
		demo: 'batch-ranking',
		rankingType: 'category',
		category: 'technology',
		domainCount: domains.length,
		rankings: categoryRankings.map(ranking => ({
			rank: ranking.rank,
			domain: ranking.domain,
			score: Number(ranking.score.toFixed(3))
		})),
	}, 'Category Rankings Calculated');

	// Get ranking statistics
	const stats = ranker.getRankingStatistics(globalRankings);

	demoLogger.info({
		demo: 'batch-ranking',
		statistics: {
			totalDomains: stats.totalDomains,
			averageScore: stats.averageScore,
			medianScore: stats.medianScore,
			topScore: stats.topScore,
			bottomScore: stats.bottomScore,
			gradeDistribution: stats.gradeDistribution,
		},
	}, 'Ranking Statistics Generated');
}

/**
 * Demo: Ranking with history tracking
 */
async function demonstrateRankingWithHistory(): Promise<void>
{
	demoLogger.info({
		demo: 'ranking-with-history',
		stage: 'start',
	}, 'Ranking with History Demo');

	const ranker = new CompositeRanker();

	// Sample domains
	const domains: DomainDataType[] = [
		{
			domain: 'rising-star.com',
			category: 'technology',
			performanceMetrics: {
				loadTime: 800,
				firstContentfulPaint: 600,
				largestContentfulPaint: 1200,
				cumulativeLayoutShift: 0.02,
				firstInputDelay: 30,
				speedIndex: 1500,
				responseTime: 100,
				pageSize: 1.2 * 1024 * 1024,
				resourceCount: 25,
			},
		},
		{
			domain: 'stable-performer.com',
			category: 'technology',
			performanceMetrics: {
				loadTime: 1200,
				firstContentfulPaint: 800,
				largestContentfulPaint: 1800,
				cumulativeLayoutShift: 0.05,
				firstInputDelay: 80,
				speedIndex: 2200,
				responseTime: 200,
				pageSize: 2 * 1024 * 1024,
				resourceCount: 40,
			},
		},
	];

	// Simulate previous rankings
	const previousRankings = new Map([
		['rising-star.com', 5], // Was ranked 5th
		['stable-performer.com', 2], // Was ranked 2nd
	]);

	// Calculate rankings with history
	const rankingsWithHistory = ranker.calculateRankingsWithHistory(
		domains,
		previousRankings,
	);

	demoLogger.info({
		demo: 'ranking-with-history',
		domainCount: domains.length,
		previousRankingCount: previousRankings.size,
		rankings: rankingsWithHistory.map((ranking) => {
			const change = ranking.rankChange;
			const changeText = change
				? change > 0
					? `↑${change} (improved)`
					: `↓${Math.abs(change)} (declined)`
				: 'new';

			return {
				rank: ranking.rank,
				domain: ranking.domain,
				score: Number(ranking.score.toFixed(3)),
				rankChange: change,
				changeDescription: changeText,
			};
		}),
	}, 'Rankings with History Calculated');
}

/**
 * Demo: Advanced ranking management with quality control
 */
async function demonstrateAdvancedRankingManagement(): Promise<void>
{
	demoLogger.info({
		demo: 'advanced-ranking-management',
		stage: 'start',
	}, 'Advanced Ranking Management Demo');

	// This would require actual database and job queue instances
	// For demo purposes, we'll show the configuration and structure

	const config: RankingManagerConfigType = {
		weights: {
			PERFORMANCE: 0.35, // High performance emphasis
			SECURITY: 0.25,
			SEO: 0.2,
			TECHNICAL: 0.1,
			BACKLINKS: 0.1,
		},
		validationEnabled: true,
		anomalyDetectionEnabled: true,
		qualityThresholds: {
			minimumScore: 0.1,
			maximumVolatility: 30, // Lower volatility threshold
			minimumDataPoints: 5,
		},
		batchProcessing: {
			maxBatchSize: 500,
			maxConcurrentBatches: 3,
			retryAttempts: 2,
		},
	};

	demoLogger.info({
		demo: 'advanced-ranking-management',
		configuration: {
			weights: config.weights,
			validationEnabled: config.validationEnabled,
			anomalyDetectionEnabled: config.anomalyDetectionEnabled,
			qualityThresholds: config.qualityThresholds,
			batchProcessing: config.batchProcessing,
		},
	}, 'Ranking Manager Configuration');

	demoLogger.info({
		demo: 'advanced-ranking-management',
		featuresAvailable: [
			'Comprehensive ranking calculation with all scoring dimensions',
			'Batch processing with quality control and validation',
			'Ranking history tracking and trend analysis',
			'Anomaly detection for sudden ranking changes',
			'Quality assessment and reporting',
			'Configurable weights and thresholds',
			'Global and category-based rankings',
			'Detailed breakdowns and recommendations',
			'Health monitoring and error handling',
		],
	}, 'Available Features');
}

/**
 * Demo: Weight optimization and A/B testing
 */
async function demonstrateWeightOptimization(): Promise<void>
{
	demoLogger.info({
		demo: 'weight-optimization',
		stage: 'start',
	}, 'Weight Optimization Demo');

	// Sample domain for testing different weight configurations
	const testDomain: DomainDataType = {
		domain: 'test-optimization.com',
		performanceMetrics: {
			loadTime: 1000,
			firstContentfulPaint: 700,
			largestContentfulPaint: 1500,
			cumulativeLayoutShift: 0.04,
			firstInputDelay: 60,
			speedIndex: 2000,
			responseTime: 150,
			pageSize: 2 * 1024 * 1024,
			resourceCount: 35,
		},
		securityMetrics: {
			sslGrade: 'A',
			usesHttps: true,
			securityHeaders: {
				hsts: true,
				csp: true,
				xframe: true,
				xssProtection: true,
				contentTypeOptions: true,
				referrerPolicy: false,
				permissionsPolicy: false,
			},
		},
	};

	// Test different weight configurations
	const weightConfigurations = [
		{
			name: 'Performance Focused',
			weights: { PERFORMANCE: 0.5, SECURITY: 0.2, SEO: 0.15, TECHNICAL: 0.1, BACKLINKS: 0.05 },
		},
		{
			name: 'Security Focused',
			weights: { PERFORMANCE: 0.2, SECURITY: 0.4, SEO: 0.2, TECHNICAL: 0.15, BACKLINKS: 0.05 },
		},
		{
			name: 'Balanced',
			weights: { PERFORMANCE: 0.25, SECURITY: 0.2, SEO: 0.25, TECHNICAL: 0.15, BACKLINKS: 0.15 },
		},
		{
			name: 'SEO Focused',
			weights: { PERFORMANCE: 0.15, SECURITY: 0.15, SEO: 0.4, TECHNICAL: 0.15, BACKLINKS: 0.15 },
		},
	];

	demoLogger.info({
		demo: 'weight-optimization',
		stage: 'configuration-testing',
		testDomain: testDomain.domain,
		configurationCount: weightConfigurations.length,
	}, 'Testing Different Weight Configurations');

	weightConfigurations.forEach((config) =>
	{
		const ranker = new CompositeRanker(config.weights as RankingWeightsType);
		const score = ranker.calculateCompositeScore(testDomain);

		demoLogger.info({
			demo: 'weight-optimization',
			configuration: config.name,
			testDomain: testDomain.domain,
			weights: config.weights,
			results: {
				overallScore: Number(score.overallScore.toFixed(3)),
				grade: score.grade,
				scores: {
					performance: Number(score.scores.performance.toFixed(3)),
					security: Number(score.scores.security.toFixed(3)),
					seo: Number(score.scores.seo.toFixed(3)),
					technical: Number(score.scores.technical.toFixed(3)),
					backlinks: Number(score.scores.backlinks.toFixed(3)),
				},
			},
		}, `Weight Configuration Test: ${config.name}`);
	});
}

/**
 * Main demo function
 */
async function runRankingDemo(): Promise<void>
{
	demoLogger.info({
		demo: 'comprehensive-ranking-system',
		stage: 'start',
		description: 'Showcasing extracted ranking functionality from ranking-engine service',
	}, 'Comprehensive Ranking System Demo Started');

	try
	{
		await demonstrateBasicRanking();
		await demonstrateBatchRanking();
		await demonstrateRankingWithHistory();
		await demonstrateAdvancedRankingManagement();
		await demonstrateWeightOptimization();

		demoLogger.info({
			demo: 'comprehensive-ranking-system',
			stage: 'complete',
			keyFeatures: [
				'CompositeRanker: Complete ranking algorithm with score aggregation',
				'RankingUpdateService: Batch processing and history tracking',
				'RankingManager: Quality control and anomaly detection',
				'Configurable weights and validation',
				'Global and category rankings',
				'Trend analysis and performance tracking',
			],
		}, 'Demo Completed Successfully');
	}
	catch (error)
	{
		demoLogger.error({
			demo: 'comprehensive-ranking-system',
			stage: 'failed',
			error: error instanceof Error ? error.message : 'Unknown error',
			stack: error instanceof Error ? error.stack : undefined,
		}, 'Demo Failed');
	}
}

// Export for use in other modules
export {
	demonstrateBasicRanking,
	demonstrateBatchRanking,
	demonstrateRankingWithHistory,
	demonstrateAdvancedRankingManagement,
	demonstrateWeightOptimization,
	runRankingDemo,
};

// Run demo if this file is executed directly
if (require.main === module)
{
	runRankingDemo().catch((error) => {
		demoLogger.error({
			demo: 'comprehensive-ranking-system',
			stage: 'module-execution',
			error: error instanceof Error ? error.message : 'Unknown error',
			stack: error instanceof Error ? error.stack : undefined,
		}, 'Failed to run demo from module execution');
	});
}
