import { logger as sharedLogger } from '@shared';
import type { DomainDataType, CompositeScoreType, RankingResultType } from './CompositeRanker';
import type { PerformanceMetrics, CoreWebVitals } from './scorers/PerformanceScorer';
import type { SecurityMetricsType, VulnerabilityType } from './scorers/SecurityScorer';
import type { SEOMetrics } from './scorers/SEOScorer';
import type { TechnicalMetricsType } from './scorers/TechnicalScorer';
import type { BacklinkMetricsType } from './scorers/BacklinkScorer';

const logger = sharedLogger.getLogger('RankingValidator');

type ValidationResultType =
{
	isValid: boolean;
	errors: ValidationErrorType[];
	warnings: ValidationWarningType[];
	score?: number;
	confidence?: number;
};

type ValidationErrorType =
{
	field: string;
	message: string;
	severity: 'critical' | 'high' | 'medium' | 'low';
	code: string;
};

type ValidationWarningType =
{
	field: string;
	message: string;
	impact: 'high' | 'medium' | 'low';
	suggestion?: string;
};

type QualityAssessmentType =
{
	dataCompleteness: number; // 0-1 scale
	dataQuality: number; // 0-1 scale
	confidenceScore: number; // 0-1 scale
	missingFields: string[];
	qualityIssues: string[];
};

/**
 * Comprehensive validation and quality assurance for ranking calculations
 */
class RankingValidator
{
	private static readonly REQUIRED_FIELDS = [
		'domain',
	];

	private static readonly RECOMMENDED_FIELDS = [
		'performanceMetrics',
		'securityMetrics',
		'seoMetrics',
		'technicalMetrics',
		'backlinkMetrics',
	];

	/**
	 * Validate domain data for ranking calculation
	 */
	static validateDomainData(domainData: DomainDataType): ValidationResultType
	{
		const errors: ValidationErrorType[] = [];
		const warnings: ValidationWarningType[] = [];

		try
		{
			// Basic validation
			this.validateBasicFields(domainData, errors, warnings);

			// Performance metrics validation
			if (domainData.performanceMetrics)
			{
				this.validatePerformanceMetrics(domainData.performanceMetrics, errors, warnings);
			}

			// Core Web Vitals validation
			if (domainData.coreWebVitals)
			{
				this.validateCoreWebVitals(domainData.coreWebVitals, errors, warnings);
			}

			// Security metrics validation
			if (domainData.securityMetrics)
			{
				this.validateSecurityMetrics(domainData.securityMetrics, errors, warnings);
			}

			// SEO metrics validation
			if (domainData.seoMetrics)
			{
				this.validateSEOMetrics(domainData.seoMetrics, errors, warnings);
			}

			// Technical metrics validation
			if (domainData.technicalMetrics)
			{
				this.validateTechnicalMetrics(domainData.technicalMetrics, errors, warnings);
			}

			// Backlink metrics validation
			if (domainData.backlinkMetrics)
			{
				this.validateBacklinkMetrics(domainData.backlinkMetrics, errors, warnings);
			}

			const isValid = errors.filter(e => e.severity === 'critical' || e.severity === 'high').length === 0;

			return {
				isValid,
				errors,
				warnings,
			};
		}
		catch (error)
		{
			logger.error('Error during domain data validation', {
				domain: domainData.domain,
				error: error instanceof Error ? error.message : String(error),
			});

			return {
				isValid: false,
				errors: [{
					field: 'validation',
					message: 'Validation process failed',
					severity: 'critical',
					code: 'VALIDATION_ERROR',
				}],
				warnings: [],
			};
		}
	}

	/**
	 * Validate basic required fields
	 */
	private static validateBasicFields(
		domainData: DomainDataType,
		errors: ValidationErrorType[],
		warnings: ValidationWarningType[],
	): void
	{
		// Domain name validation
		if (!domainData.domain)
		{
			errors.push({
				field: 'domain',
				message: 'Domain name is required',
				severity: 'critical',
				code: 'MISSING_DOMAIN',
			});
		}
		else if (typeof domainData.domain !== 'string' || domainData.domain.trim().length === 0)
		{
			errors.push({
				field: 'domain',
				message: 'Domain name must be a non-empty string',
				severity: 'critical',
				code: 'INVALID_DOMAIN',
			});
		}
		else if (!/^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?(\.[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?)*$/.test(domainData.domain))
		{
			warnings.push({
				field: 'domain',
				message: 'Domain name format appears invalid',
				impact: 'medium',
				suggestion: 'Ensure domain follows standard format (e.g., example.com)',
			});
		}

		// Category validation
		if (domainData.category && typeof domainData.category !== 'string')
		{
			warnings.push({
				field: 'category',
				message: 'Category should be a string',
				impact: 'low',
			});
		}

		// Country validation
		if (domainData.country && typeof domainData.country !== 'string')
		{
			warnings.push({
				field: 'country',
				message: 'Country should be a string',
				impact: 'low',
			});
		}

		// Traffic estimate validation
		if (domainData.trafficEstimate !== undefined)
		{
			if (typeof domainData.trafficEstimate !== 'number' || domainData.trafficEstimate < 0)
			{
				warnings.push({
					field: 'trafficEstimate',
					message: 'Traffic estimate should be a non-negative number',
					impact: 'low',
				});
			}
		}

		// Last updated validation
		if (domainData.lastUpdated && !(domainData.lastUpdated instanceof Date))
		{
			warnings.push({
				field: 'lastUpdated',
				message: 'Last updated should be a Date object',
				impact: 'low',
			});
		}
	}

	/**
	 * Validate performance metrics
	 */
	private static validatePerformanceMetrics(
		metrics: PerformanceMetrics,
		errors: ValidationErrorType[],
		warnings: ValidationWarningType[],
	): void
	{
		// Load time validation
		if (metrics.loadTime !== undefined)
		{
			if (typeof metrics.loadTime !== 'number' || metrics.loadTime < 0)
			{
				errors.push({
					field: 'performanceMetrics.loadTime',
					message: 'Load time must be a non-negative number',
					severity: 'medium',
					code: 'INVALID_LOAD_TIME',
				});
			}
			else if (metrics.loadTime > 30000)
			{
				warnings.push({
					field: 'performanceMetrics.loadTime',
					message: 'Load time seems unusually high (>30 seconds)',
					impact: 'medium',
					suggestion: 'Verify load time measurement accuracy',
				});
			}
		}

		// Response time validation
		if (metrics.responseTime !== undefined)
		{
			if (typeof metrics.responseTime !== 'number' || metrics.responseTime < 0)
			{
				errors.push({
					field: 'performanceMetrics.responseTime',
					message: 'Response time must be a non-negative number',
					severity: 'medium',
					code: 'INVALID_RESPONSE_TIME',
				});
			}
		}

		// Page size validation
		if (metrics.pageSize !== undefined)
		{
			if (typeof metrics.pageSize !== 'number' || metrics.pageSize < 0)
			{
				errors.push({
					field: 'performanceMetrics.pageSize',
					message: 'Page size must be a non-negative number',
					severity: 'medium',
					code: 'INVALID_PAGE_SIZE',
				});
			}
			else if (metrics.pageSize > 50 * 1024 * 1024) // 50MB
			{
				warnings.push({
					field: 'performanceMetrics.pageSize',
					message: 'Page size seems unusually large (>50MB)',
					impact: 'high',
					suggestion: 'Consider optimizing page size for better performance',
				});
			}
		}

		// Resource count validation
		if (metrics.resourceCount !== undefined)
		{
			if (typeof metrics.resourceCount !== 'number' || metrics.resourceCount < 0)
			{
				errors.push({
					field: 'performanceMetrics.resourceCount',
					message: 'Resource count must be a non-negative number',
					severity: 'medium',
					code: 'INVALID_RESOURCE_COUNT',
				});
			}
			else if (metrics.resourceCount > 500)
			{
				warnings.push({
					field: 'performanceMetrics.resourceCount',
					message: 'Resource count seems very high (>500)',
					impact: 'medium',
					suggestion: 'Consider reducing number of resources for better performance',
				});
			}
		}
	}

	/**
	 * Validate Core Web Vitals
	 */
	private static validateCoreWebVitals(
		vitals: CoreWebVitals,
		errors: ValidationErrorType[],
		warnings: ValidationWarningType[],
	): void
	{
		// LCP validation
		if (vitals.lcp !== undefined)
		{
			if (typeof vitals.lcp !== 'number' || vitals.lcp < 0)
			{
				errors.push({
					field: 'coreWebVitals.lcp',
					message: 'LCP must be a non-negative number',
					severity: 'medium',
					code: 'INVALID_LCP',
				});
			}
			else if (vitals.lcp > 10)
			{
				warnings.push({
					field: 'coreWebVitals.lcp',
					message: 'LCP is very high (>10 seconds)',
					impact: 'high',
					suggestion: 'Optimize Largest Contentful Paint for better user experience',
				});
			}
		}

		// FID validation
		if (vitals.fid !== undefined)
		{
			if (typeof vitals.fid !== 'number' || vitals.fid < 0)
			{
				errors.push({
					field: 'coreWebVitals.fid',
					message: 'FID must be a non-negative number',
					severity: 'medium',
					code: 'INVALID_FID',
				});
			}
		}

		// CLS validation
		if (vitals.cls !== undefined)
		{
			if (typeof vitals.cls !== 'number' || vitals.cls < 0)
			{
				errors.push({
					field: 'coreWebVitals.cls',
					message: 'CLS must be a non-negative number',
					severity: 'medium',
					code: 'INVALID_CLS',
				});
			}
			else if (vitals.cls > 1)
			{
				warnings.push({
					field: 'coreWebVitals.cls',
					message: 'CLS is very high (>1.0)',
					impact: 'high',
					suggestion: 'Reduce layout shifts for better user experience',
				});
			}
		}
	}

	/**
	 * Validate security metrics
	 */
	private static validateSecurityMetrics(
		metrics: SecurityMetricsType,
		errors: ValidationErrorType[],
		warnings: ValidationWarningType[],
	): void
	{
		// SSL grade validation
		if (metrics.sslGrade !== undefined)
		{
			const validGrades = ['A+', 'A', 'A-', 'B', 'C', 'D', 'E', 'F', 'T'];
			if (!validGrades.includes(metrics.sslGrade))
			{
				warnings.push({
					field: 'securityMetrics.sslGrade',
					message: 'SSL grade format is non-standard',
					impact: 'low',
					suggestion: 'Use standard SSL Labs grading (A+ to F, T)',
				});
			}
		}

		// SSL expiration validation
		if (metrics.sslExpiration !== undefined)
		{
			if (!(metrics.sslExpiration instanceof Date))
			{
				errors.push({
					field: 'securityMetrics.sslExpiration',
					message: 'SSL expiration must be a Date object',
					severity: 'medium',
					code: 'INVALID_SSL_EXPIRATION',
				});
			}
			else if (metrics.sslExpiration < new Date())
			{
				warnings.push({
					field: 'securityMetrics.sslExpiration',
					message: 'SSL certificate has expired',
					impact: 'high',
					suggestion: 'Renew SSL certificate immediately',
				});
			}
		}

		// Vulnerabilities validation
		if (metrics.vulnerabilities !== undefined)
		{
			if (!Array.isArray(metrics.vulnerabilities))
			{
				errors.push({
					field: 'securityMetrics.vulnerabilities',
					message: 'Vulnerabilities must be an array',
					severity: 'medium',
					code: 'INVALID_VULNERABILITIES',
				});
			}
			else
			{
				metrics.vulnerabilities.forEach((vuln: VulnerabilityType, index: number) =>
				{
					if (!vuln.type || !vuln.severity || !vuln.description)
					{
						warnings.push({
							field: `securityMetrics.vulnerabilities[${index}]`,
							message: 'Vulnerability missing required fields (type, severity, description)',
							impact: 'medium',
						});
					}

					const validSeverities = ['low', 'medium', 'high', 'critical'];
					if (vuln.severity && !validSeverities.includes(vuln.severity))
					{
						warnings.push({
							field: `securityMetrics.vulnerabilities[${index}].severity`,
							message: 'Invalid vulnerability severity',
							impact: 'medium',
							suggestion: 'Use: low, medium, high, or critical',
						});
					}
				});
			}
		}
	}

	/**
	 * Validate SEO metrics
	 */
	private static validateSEOMetrics(
		metrics: SEOMetrics,
		errors: ValidationErrorType[],
		warnings: ValidationWarningType[],
	): void
	{
		// Meta tags validation
		if (metrics.metaTags)
		{
			const meta = metrics.metaTags;

			if (meta.title && (meta.title.length < 10 || meta.title.length > 70))
			{
				warnings.push({
					field: 'seoMetrics.metaTags.title',
					message: 'Title length is not optimal (should be 30-60 characters)',
					impact: 'medium',
					suggestion: 'Optimize title length for better SEO',
				});
			}

			if (meta.description && (meta.description.length < 120 || meta.description.length > 160))
			{
				warnings.push({
					field: 'seoMetrics.metaTags.description',
					message: 'Meta description length is not optimal (should be 120-160 characters)',
					impact: 'medium',
					suggestion: 'Optimize description length for better SEO',
				});
			}
		}

		// Content metrics validation
		if (metrics.contentMetrics)
		{
			const content = metrics.contentMetrics;

			if (content.wordCount < 300)
			{
				warnings.push({
					field: 'seoMetrics.contentMetrics.wordCount',
					message: 'Content word count is low (<300 words)',
					impact: 'medium',
					suggestion: 'Consider adding more content for better SEO',
				});
			}

			if (content.imageAltTextRatio < 0.8)
			{
				warnings.push({
					field: 'seoMetrics.contentMetrics.imageAltTextRatio',
					message: 'Low alt text coverage for images',
					impact: 'medium',
					suggestion: 'Add alt text to all images for better accessibility and SEO',
				});
			}
		}
	}

	/**
	 * Validate technical metrics
	 */
	private static validateTechnicalMetrics(
		metrics: TechnicalMetricsType,
		errors: ValidationErrorType[],
		warnings: ValidationWarningType[],
	): void
	{
		// DNS response time validation
		if (metrics.dnsResponseTime !== undefined)
		{
			if (typeof metrics.dnsResponseTime !== 'number' || metrics.dnsResponseTime < 0)
			{
				errors.push({
					field: 'technicalMetrics.dnsResponseTime',
					message: 'DNS response time must be a non-negative number',
					severity: 'medium',
					code: 'INVALID_DNS_RESPONSE_TIME',
				});
			}
			else if (metrics.dnsResponseTime > 1000)
			{
				warnings.push({
					field: 'technicalMetrics.dnsResponseTime',
					message: 'DNS response time is very high (>1 second)',
					impact: 'high',
					suggestion: 'Consider using faster DNS providers',
				});
			}
		}

		// Uptime percentage validation
		if (metrics.uptimePercentage !== undefined)
		{
			if (typeof metrics.uptimePercentage !== 'number' || metrics.uptimePercentage < 0 || metrics.uptimePercentage > 100)
			{
				errors.push({
					field: 'technicalMetrics.uptimePercentage',
					message: 'Uptime percentage must be between 0 and 100',
					severity: 'medium',
					code: 'INVALID_UPTIME_PERCENTAGE',
				});
			}
			else if (metrics.uptimePercentage < 99)
			{
				warnings.push({
					field: 'technicalMetrics.uptimePercentage',
					message: 'Uptime percentage is below 99%',
					impact: 'high',
					suggestion: 'Improve hosting reliability and monitoring',
				});
			}
		}
	}

	/**
	 * Validate backlink metrics
	 */
	private static validateBacklinkMetrics(
		metrics: BacklinkMetricsType,
		errors: ValidationErrorType[],
		warnings: ValidationWarningType[],
	): void
	{
		// Total backlinks validation
		if (typeof metrics.totalBacklinks !== 'number' || metrics.totalBacklinks < 0)
		{
			errors.push({
				field: 'backlinkMetrics.totalBacklinks',
				message: 'Total backlinks must be a non-negative number',
				severity: 'medium',
				code: 'INVALID_TOTAL_BACKLINKS',
			});
		}

		// Unique domains validation
		if (typeof metrics.uniqueDomains !== 'number' || metrics.uniqueDomains < 0)
		{
			errors.push({
				field: 'backlinkMetrics.uniqueDomains',
				message: 'Unique domains must be a non-negative number',
				severity: 'medium',
				code: 'INVALID_UNIQUE_DOMAINS',
			});
		}

		// Average authority validation
		if (typeof metrics.averageAuthority !== 'number' || metrics.averageAuthority < 0 || metrics.averageAuthority > 100)
		{
			errors.push({
				field: 'backlinkMetrics.averageAuthority',
				message: 'Average authority must be between 0 and 100',
				severity: 'medium',
				code: 'INVALID_AVERAGE_AUTHORITY',
			});
		}

		// Logical consistency checks
		if (metrics.uniqueDomains > metrics.totalBacklinks)
		{
			warnings.push({
				field: 'backlinkMetrics',
				message: 'Unique domains cannot exceed total backlinks',
				impact: 'high',
				suggestion: 'Verify backlink data consistency',
			});
		}

		if (metrics.followLinks + metrics.nofollowLinks > metrics.totalBacklinks)
		{
			warnings.push({
				field: 'backlinkMetrics',
				message: 'Follow + nofollow links exceed total backlinks',
				impact: 'high',
				suggestion: 'Verify link type data consistency',
			});
		}
	}

	/**
	 * Assess data quality and completeness
	 */
	static assessDataQuality(domainData: DomainDataType): QualityAssessmentType
	{
		const missingFields: string[] = [];
		const qualityIssues: string[] = [];

		// Check for recommended fields
		this.RECOMMENDED_FIELDS.forEach((field) =>
		{
			if (!domainData[field as keyof DomainDataType])
			{
				missingFields.push(field);
			}
		});

		// Calculate completeness score
		const totalRecommendedFields = this.RECOMMENDED_FIELDS.length;
		const presentFields = totalRecommendedFields - missingFields.length;
		const dataCompleteness = presentFields / totalRecommendedFields;

		// Assess data quality
		let qualityScore = 1.0;

		// Performance data quality
		if (domainData.performanceMetrics)
		{
			if (!domainData.performanceMetrics.loadTime && !domainData.performanceMetrics.responseTime)
			{
				qualityIssues.push('Performance metrics missing key timing data');
				qualityScore -= 0.1;
			}
		}

		// Security data quality
		if (domainData.securityMetrics)
		{
			if (!domainData.securityMetrics.sslGrade && !domainData.securityMetrics.usesHttps)
			{
				qualityIssues.push('Security metrics missing SSL/HTTPS information');
				qualityScore -= 0.1;
			}
		}

		// SEO data quality
		if (domainData.seoMetrics)
		{
			if (!domainData.seoMetrics.metaTags?.title)
			{
				qualityIssues.push('SEO metrics missing title tag information');
				qualityScore -= 0.05;
			}
		}

		// Calculate confidence score
		const confidenceScore = Math.min(dataCompleteness * qualityScore, 1.0);

		return {
			dataCompleteness: Math.round(dataCompleteness * 1000) / 1000,
			dataQuality: Math.round(qualityScore * 1000) / 1000,
			confidenceScore: Math.round(confidenceScore * 1000) / 1000,
			missingFields,
			qualityIssues,
		};
	}

	/**
	 * Validate composite score result
	 */
	static validateCompositeScore(score: CompositeScoreType): ValidationResultType
	{
		const errors: ValidationErrorType[] = [];
		const warnings: ValidationWarningType[] = [];

		// Overall score validation
		if (typeof score.overallScore !== 'number' || score.overallScore < 0 || score.overallScore > 1)
		{
			errors.push({
				field: 'overallScore',
				message: 'Overall score must be between 0 and 1',
				severity: 'critical',
				code: 'INVALID_OVERALL_SCORE',
			});
		}

		// Individual scores validation
		const scoreFields = ['performance', 'security', 'seo', 'technical', 'backlinks'];
		scoreFields.forEach((field) =>
		{
			const fieldScore = score.scores[field as keyof typeof score.scores];
			if (typeof fieldScore !== 'number' || fieldScore < 0 || fieldScore > 1)
			{
				errors.push({
					field: `scores.${field}`,
					message: `${field} score must be between 0 and 1`,
					severity: 'high',
					code: 'INVALID_COMPONENT_SCORE',
				});
			}
		});

		// Grade validation
		const validGrades = ['A+', 'A', 'B', 'C', 'D', 'F'];
		if (!validGrades.includes(score.grade))
		{
			warnings.push({
				field: 'grade',
				message: 'Grade format is non-standard',
				impact: 'low',
			});
		}

		return {
			isValid: errors.length === 0,
			errors,
			warnings,
			score: score.overallScore,
		};
	}

	/**
	 * Validate ranking results
	 */
	static validateRankingResults(rankings: RankingResultType[]): ValidationResultType
	{
		const errors: ValidationErrorType[] = [];
		const warnings: ValidationWarningType[] = [];

		if (!Array.isArray(rankings))
		{
			errors.push({
				field: 'rankings',
				message: 'Rankings must be an array',
				severity: 'critical',
				code: 'INVALID_RANKINGS_TYPE',
			});

			return { isValid: false, errors, warnings };
		}

		// Check ranking consistency
		rankings.forEach((ranking, index) =>
		{
			// Rank validation
			if (ranking.rank !== index + 1)
			{
				errors.push({
					field: `rankings[${index}].rank`,
					message: 'Ranking order is inconsistent',
					severity: 'high',
					code: 'INCONSISTENT_RANKING',
				});
			}

			// Score validation
			if (typeof ranking.score !== 'number' || ranking.score < 0 || ranking.score > 1)
			{
				errors.push({
					field: `rankings[${index}].score`,
					message: 'Ranking score must be between 0 and 1',
					severity: 'high',
					code: 'INVALID_RANKING_SCORE',
				});
			}

			// Score order validation
			if (index > 0 && ranking.score > rankings[index - 1].score)
			{
				warnings.push({
					field: `rankings[${index}].score`,
					message: 'Ranking scores are not in descending order',
					impact: 'medium',
					suggestion: 'Ensure rankings are sorted by score (highest first)',
				});
			}
		});

		return {
			isValid: errors.filter(e => e.severity === 'critical' || e.severity === 'high').length === 0,
			errors,
			warnings,
		};
	}
}

export type {
	ValidationResultType,
	ValidationErrorType,
	ValidationWarningType,
	QualityAssessmentType,
};

export default RankingValidator;
