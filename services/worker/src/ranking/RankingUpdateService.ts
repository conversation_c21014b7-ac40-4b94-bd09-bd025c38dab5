import { logger as sharedLogger, IdGenerator } from '@shared';
import { WorkerDatabaseManager } from '../database/WorkerDatabaseManager';
import { WorkerJobQueue } from '../queue/WorkerJobQueue';
import { CompositeRanker } from './CompositeRanker';
import type {
	DomainDataType,
	RankingResultType,
	CompositeScoreType,
	RankingWeightsType,
	RankingStatisticsType,
} from './CompositeRanker';
import type { PerformanceMetrics } from './scorers/PerformanceScorer';
import type { SecurityMetricsType } from './scorers/SecurityScorer';
import type { SEOMetrics } from './scorers/SEOScorer';
import type { TechnicalMetricsType } from './scorers/TechnicalScorer';

const logger = sharedLogger.getLogger('RankingUpdateService');

type RankingUpdateTriggerType =
{
	type: 'domain_updated' | 'scheduled' | 'manual' | 'batch';
	domain?: string;
	category?: string;
	priority: 'low' | 'medium' | 'high';
	scheduledAt?: Date;
	metadata?: Record<string, unknown>;
};

type BatchRankingUpdateType =
{
	batchId: string;
	domains: string[];
	rankingType: 'global' | 'category';
	category?: string;
	status: 'pending' | 'processing' | 'completed' | 'failed';
	startedAt?: Date;
	completedAt?: Date;
	processedCount: number;
	totalCount: number;
	errors: string[];
};

type RankingHistoryEntryType =
{
	domain: string;
	date: string;
	rankingType: string;
	rank: number;
	score: number;
	previousRank?: number;
	rankChange?: number;
	category?: string;
};

type RankingTrendAnalysisType =
{
	domain: string;
	period: string;
	trendDirection: 'up' | 'down' | 'stable';
	averageRank: number;
	bestRank: number;
	worstRank: number;
	volatility: number;
	rankChanges: RankingHistoryEntryType[];
};

/**
 * Service for managing ranking updates, batch processing, and history tracking
 */
class RankingUpdateService
{
	private dbManager: WorkerDatabaseManager;

	private jobQueue: WorkerJobQueue;

	private compositeRanker: CompositeRanker;

	private activeBatches = new Map<string, BatchRankingUpdateType>();

	private updateTriggers = new Map<string, RankingUpdateTriggerType>();

	constructor(
		dbManager: WorkerDatabaseManager,
		jobQueue: WorkerJobQueue,
		compositeRanker?: CompositeRanker,
	)
	{
		this.dbManager = dbManager;
		this.jobQueue = jobQueue;
		this.compositeRanker = compositeRanker || new CompositeRanker();
	}

	/**
	 * Initialize the ranking update service
	 */
	async initialize(): Promise<void>
	{
		try
		{
			logger.info('Initializing Ranking Update Service...');

			// Setup job consumers for ranking updates
			await this.setupJobConsumers();

			// Setup scheduled ranking updates
			await this.setupScheduledUpdates();

			logger.info('Ranking Update Service initialized successfully');
		}
		catch (error)
		{
			logger.error('Failed to initialize Ranking Update Service:', error);
			throw error;
		}
	}

	/**
	 * Setup job queue consumers for ranking updates
	 */
	private async setupJobConsumers(): Promise<void>
	{
		// Single domain ranking update consumer
		await this.jobQueue.createConsumer(
			'ranking:update',
			this.handleSingleRankingUpdate.bind(this),
			{ concurrency: 3 },
		);

		// Batch ranking update consumer
		await this.jobQueue.createConsumer(
			'ranking:batch',
			this.handleBatchRankingUpdate.bind(this),
			{ concurrency: 1 },
		);

		// Ranking history update consumer
		await this.jobQueue.createConsumer(
			'ranking:history',
			this.handleRankingHistoryUpdate.bind(this),
			{ concurrency: 2 },
		);

		logger.info('Ranking update job consumers setup completed');
	}

	/**
	 * Setup scheduled ranking updates
	 */
	private async setupScheduledUpdates(): Promise<void>
	{
		// Schedule daily global ranking recalculation
		await this.scheduleRecurringUpdate({
			type: 'scheduled',
			priority: 'medium',
			metadata: {
				updateType: 'global_daily',
				cronExpression: '0 2 * * *', // 2 AM daily
			},
		});

		// Schedule weekly category ranking updates
		await this.scheduleRecurringUpdate({
			type: 'scheduled',
			priority: 'low',
			metadata: {
				updateType: 'category_weekly',
				cronExpression: '0 3 * * 0', // 3 AM on Sundays
			},
		});

		logger.info('Scheduled ranking updates configured');
	}

	/**
	 * Trigger ranking update for a single domain
	 */
	async triggerDomainRankingUpdate(
		domain: string,
		trigger: Omit<RankingUpdateTriggerType, 'domain'> = { type: 'manual', priority: 'medium' },
	): Promise<string>
	{
		try
		{
			const updateTrigger: RankingUpdateTriggerType = {
				...trigger,
				domain,
				scheduledAt: new Date(),
			};

			// Store trigger for tracking
			const triggerId = this.generateTriggerId();
			this.updateTriggers.set(triggerId, updateTrigger);

			// Publish ranking update job
			const jobId = await this.jobQueue.publishJob('ranking:update', {
				domain,
				rankingType: 'global',
				triggerId,
			});

			logger.info(`Ranking update triggered for domain: ${domain}`, {
				triggerId,
				jobId,
				trigger: updateTrigger,
			});

			return jobId;
		}
		catch (error)
		{
			logger.error(`Failed to trigger ranking update for domain: ${domain}:`, error);
			throw error;
		}
	}

	/**
	 * Trigger batch ranking update for multiple domains
	 */
	async triggerBatchRankingUpdate(
		domains: string[],
		rankingType: 'global' | 'category' = 'global',
		category?: string,
		priority: 'low' | 'medium' | 'high' = 'medium',
	): Promise<string>
	{
		try
		{
			const batchId = this.generateBatchId();
			const batchUpdate: BatchRankingUpdateType = {
				batchId,
				domains,
				rankingType,
				category,
				status: 'pending',
				processedCount: 0,
				totalCount: domains.length,
				errors: [],
			};

			// Store batch for tracking
			this.activeBatches.set(batchId, batchUpdate);

			// Publish batch ranking job
			const jobData = {
				batchId,
				domains,
				rankingType,
				category,
				priority,
			};

			const jobId = await this.jobQueue.publishJob('ranking:batch', jobData, {
				priority: this.getPriorityValue(priority),
			});

			logger.info(`Batch ranking update triggered for ${domains.length} domains`, {
				batchId,
				jobId,
				rankingType,
				category,
			});

			return batchId;
		}
		catch (error)
		{
			logger.error('Failed to trigger batch ranking update:', error);
			throw error;
		}
	}

	/**
	 * Handle single domain ranking update job
	 */
	private async handleSingleRankingUpdate(jobData: unknown): Promise<void>
	{
		const { domain, rankingType = 'global', category } = jobData as {
			domain: string;
			rankingType?: string;
			category?: string;
		};

		try
		{
			logger.info(`Processing single ranking update for domain: ${domain}`);

			// Fetch domain data
			const domainData = await this.fetchDomainData(domain);
			if (!domainData)
			{
				throw new Error(`No data found for domain: ${domain}`);
			}

			// Calculate composite score
			const compositeScore = this.compositeRanker.calculateCompositeScore(domainData);

			// Store updated ranking
			await this.storeDomainRanking(compositeScore, rankingType, category);

			// Update ranking history
			await this.updateRankingHistory(domain, compositeScore, rankingType, category);

			// Trigger related updates if needed
			await this.triggerRelatedUpdates(domain, rankingType, category);

			logger.info(`Single ranking update completed for domain: ${domain}`, {
				overallScore: compositeScore.overallScore,
				grade: compositeScore.grade,
			});
		}
		catch (error)
		{
			logger.error(`Single ranking update failed for domain: ${domain}:`, error);
			throw error;
		}
	}

	/**
	 * Handle batch ranking update job
	 */
	private async handleBatchRankingUpdate(jobData: unknown): Promise<void>
	{
		const {
			batchId, domains, rankingType, category,
		} = jobData as {
			batchId: string;
			domains: string[];
			rankingType: string;
			category?: string;
		};

		try
		{
			logger.info(`Processing batch ranking update: ${batchId}`);

			// Get batch from tracking
			const batch = this.activeBatches.get(batchId);
			if (!batch)
			{
				throw new Error(`Batch not found: ${batchId}`);
			}

			// Update batch status
			batch.status = 'processing';
			batch.startedAt = new Date();

			// Fetch all domain data
			const domainDataList: DomainDataType[] = [];
			for (const domain of domains)
			{
				try
				{
					const domainData = await this.fetchDomainData(domain);
					if (domainData)
					{
						domainDataList.push(domainData);
					}
					batch.processedCount++;
				}
				catch (error)
				{
					const errorMessage = error instanceof Error ? error.message : 'Unknown error';
					batch.errors.push(`Failed to fetch data for ${domain}: ${errorMessage}`);
					logger.warn(`Failed to fetch data for domain: ${domain}`, error);
				}
			}

			if (domainDataList.length === 0)
			{
				throw new Error('No valid domain data found for batch ranking');
			}

			// Calculate rankings
			let rankings: RankingResultType[];
			if (category)
			{
				rankings = this.compositeRanker.calculateCategoryRankings(domainDataList, category);
			}
			else
			{
				rankings = this.compositeRanker.calculateGlobalRankings(domainDataList);
			}

			// Store rankings in database
			await this.storeRankings(rankings, rankingType, category);

			// Update ranking history for all domains
			await this.updateBatchRankingHistory(rankings, rankingType, category);

			// Update batch status
			batch.status = 'completed';
			batch.completedAt = new Date();

			logger.info(`Batch ranking update completed: ${batchId}`, {
				domainsProcessed: domainDataList.length,
				rankingsCalculated: rankings.length,
				errors: batch.errors.length,
			});
		}
		catch (error)
		{
			// Update batch status on error
			const batch = this.activeBatches.get(batchId);
			if (batch)
			{
				batch.status = 'failed';
				const errorMessage = error instanceof Error ? error.message : 'Unknown error';
				batch.errors.push(`Batch processing failed: ${errorMessage}`);
			}

			logger.error(`Batch ranking update failed: ${batchId}:`, error);
			throw error;
		}
	}

	/**
	 * Handle ranking history update job
	 */
	private async handleRankingHistoryUpdate(jobData: unknown): Promise<void>
	{
		const {
			domain, rankingType, category, period = '30d',
		} = jobData as {
			domain: string;
			rankingType: string;
			category?: string;
			period?: string;
		};

		try
		{
			logger.info(`Processing ranking history update for domain: ${domain}`);

			// Calculate trend analysis
			const trendAnalysis = await this.calculateRankingTrend(domain, rankingType, period, category);

			// Store trend analysis results
			await this.storeTrendAnalysis(trendAnalysis);

			logger.info(`Ranking history update completed for domain: ${domain}`, {
				trendDirection: trendAnalysis.trendDirection,
				volatility: trendAnalysis.volatility,
			});
		}
		catch (error)
		{
			logger.error(`Ranking history update failed for domain: ${domain}:`, error);
			throw error;
		}
	}

	/**
	 * Update ranking history for a single domain
	 */
	private async updateRankingHistory(
		domain: string,
		compositeScore: CompositeScoreType,
		rankingType: string,
		category?: string,
	): Promise<void>
	{
		try
		{
			const scylla = this.dbManager.getScyllaClient();
			const today = new Date().toISOString().split('T')[0];

			// Get previous ranking for comparison
			const previousRank = await this.getPreviousRanking(domain, rankingType, category);

			const historyEntry: RankingHistoryEntryType = {
				domain,
				date: today,
				rankingType: category ? `category:${category}` : 'global',
				rank: compositeScore.globalRank || compositeScore.categoryRank || 0,
				score: compositeScore.overallScore,
				previousRank,
				rankChange: previousRank
					? previousRank - (compositeScore.globalRank || compositeScore.categoryRank || 0)
					: undefined,
				category,
			};

			// Store in ranking history table
			const query = `
				INSERT INTO domain_ranking_history (domain, date, ranking_type, rank, overall_score)
				VALUES (?, ?, ?, ?, ?)
			`;

			await scylla.execute(query, [
				historyEntry.domain,
				historyEntry.date,
				historyEntry.rankingType,
				historyEntry.rank,
				historyEntry.score,
			]);

			logger.debug(`Ranking history updated for domain: ${domain}`, historyEntry);
		}
		catch (error)
		{
			logger.error(`Failed to update ranking history for domain: ${domain}:`, error);
			throw error;
		}
	}

	/**
	 * Update ranking history for batch of domains
	 */
	private async updateBatchRankingHistory(
		rankings: RankingResultType[],
		rankingType: string,
		category?: string,
	): Promise<void>
	{
		try
		{
			const scylla = this.dbManager.getScyllaClient();
			const today = new Date().toISOString().split('T')[0];
			const rankingTypeKey = category ? `category:${category}` : 'global';

			// Prepare batch statements
			const queries = [];

			for (const ranking of rankings)
			{
				// Get previous ranking for comparison
				const previousRank = await this.getPreviousRanking(ranking.domain, rankingType, category);

				queries.push({
					query: `
						INSERT INTO domain_ranking_history (domain, date, ranking_type, rank, overall_score)
						VALUES (?, ?, ?, ?, ?)
					`,
					params: [ranking.domain, today, rankingTypeKey, ranking.rank, ranking.score],
				});
			}

			// Execute batch
			for (const queryData of queries)
			{
				await scylla.execute(queryData.query, queryData.params);
			}

			logger.info(`Batch ranking history updated for ${rankings.length} domains`);
		}
		catch (error)
		{
			logger.error('Failed to update batch ranking history:', error);
			throw error;
		}
	}

	/**
	 * Calculate ranking trend analysis for a domain
	 */
	async calculateRankingTrend(
		domain: string,
		rankingType: string,
		period: string = '30d',
		category?: string,
	): Promise<RankingTrendAnalysisType>
	{
		try
		{
			const scylla = this.dbManager.getScyllaClient();
			const rankingTypeKey = category ? `category:${category}` : 'global';

			// Calculate date range
			const endDate = new Date();
			const startDate = new Date();
			const days = parseInt(period.replace('d', ''));
			startDate.setDate(endDate.getDate() - days);

			// Fetch ranking history
			const query = `
				SELECT domain, date, ranking_type, rank, overall_score AS score
				FROM domain_ranking_history
				WHERE domain = ? AND ranking_type = ? AND date >= ? AND date <= ?
				ORDER BY date ASC
			`;

			const result = await scylla.execute(query, [
				domain,
				rankingTypeKey,
				startDate.toISOString().split('T')[0],
				endDate.toISOString().split('T')[0],
			]);

			const rankChanges: RankingHistoryEntryType[] = result.rows.map((row: unknown) =>
			{
				const r = row as {
					domain: string;
					date: string;
					ranking_type: string;
					rank: number;
					score: number;
				};
				return {
					domain: r.domain,
					date: r.date,
					rankingType: r.ranking_type,
					rank: r.rank,
					score: r.score,
					category,
				};
			});

			if (rankChanges.length === 0)
			{
				return {
					domain,
					period,
					trendDirection: 'stable',
					averageRank: 0,
					bestRank: 0,
					worstRank: 0,
					volatility: 0,
					rankChanges: [],
				};
			}

			// Calculate trend metrics
			const ranks = rankChanges.map(entry => entry.rank);
			const averageRank = ranks.reduce((sum, rank) => sum + rank, 0) / ranks.length;
			const bestRank = Math.min(...ranks);
			const worstRank = Math.max(...ranks);

			// Calculate volatility (standard deviation)
			const variance = ranks.reduce((sum, rank) => sum + (rank - averageRank) ** 2, 0) / ranks.length;
			const volatility = Math.sqrt(variance);

			// Determine trend direction
			let trendDirection: 'up' | 'down' | 'stable' = 'stable';
			if (rankChanges.length >= 2)
			{
				const firstRank = rankChanges[0].rank;
				const lastRank = rankChanges[rankChanges.length - 1].rank;
				const rankDifference = firstRank - lastRank; // Positive = improved (lower rank number)

				if (rankDifference > 5)
				{
					trendDirection = 'up'; // Improved ranking
				}
				else if (rankDifference < -5)
				{
					trendDirection = 'down'; // Worsened ranking
				}
			}

			const trendAnalysis: RankingTrendAnalysisType = {
				domain,
				period,
				trendDirection,
				averageRank: Math.round(averageRank),
				bestRank,
				worstRank,
				volatility: Math.round(volatility * 100) / 100,
				rankChanges,
			};

			logger.debug(`Ranking trend calculated for domain: ${domain}`, trendAnalysis);
			return trendAnalysis;
		}
		catch (error)
		{
			logger.error(`Failed to calculate ranking trend for domain: ${domain}:`, error);
			throw error;
		}
	}

	/**
	 * Store trend analysis results
	 */
	private async storeTrendAnalysis(trendAnalysis: RankingTrendAnalysisType): Promise<void>
	{
		try
		{
			// Store trend analysis in cache for quick access
			const redis = this.dbManager.getRedisClient();
			const cacheKey = `trend:${trendAnalysis.domain}:${trendAnalysis.period}`;

			await redis.setex(cacheKey, 86400, JSON.stringify(trendAnalysis)); // Cache for 24 hours

			logger.debug(`Trend analysis stored for domain: ${trendAnalysis.domain}`);
		}
		catch (error)
		{
			logger.error(`Failed to store trend analysis for domain: ${trendAnalysis.domain}:`, error);
		}
	}

	/**
	 * Get previous ranking for comparison
	 */
	private async getPreviousRanking(
		domain: string,
		rankingType: string,
		category?: string,
	): Promise<number | undefined>
	{
		try
		{
			const scylla = this.dbManager.getScyllaClient();
			const rankingTypeKey = category ? `category:${category}` : 'global';

			// Get the most recent ranking before today
			const yesterday = new Date();
			yesterday.setDate(yesterday.getDate() - 1);

			const query = `
				SELECT rank
				FROM domain_ranking_history
				WHERE domain = ? AND ranking_type = ? AND date <= ?
				ORDER BY date DESC
				LIMIT 1
			`;

			const result = await scylla.execute(query, [
				domain,
				rankingTypeKey,
				yesterday.toISOString().split('T')[0],
			]);

			return result.rows[0]?.rank;
		}
		catch (error)
		{
			logger.error(`Failed to get previous ranking for domain: ${domain}:`, error);
			return undefined;
		}
	}

	/**
	 * Trigger related updates after a ranking change
	 */
	private async triggerRelatedUpdates(
		domain: string,
		rankingType: string,
		category?: string,
	): Promise<void>
	{
		try
		{
			// Trigger Manticore sync to update search indexes
			await this.jobQueue.publishJob('manticore:sync', {
				domain,
				syncType: 'incremental',
			});

			// If this is a significant ranking change, trigger category recalculation
			if (category)
			{
				await this.scheduleDelayedCategoryUpdate(category);
			}

			// Trigger ranking history analysis
			await this.jobQueue.publishJob('ranking:history', {
				domain,
				rankingType,
				category,
			}, { delay: 5000 }); // 5 second delay

			logger.debug(`Related updates triggered for domain: ${domain}`);
		}
		catch (error)
		{
			logger.error(`Failed to trigger related updates for domain: ${domain}:`, error);
		}
	}

	/**
	 * Schedule delayed category ranking update to avoid frequent recalculations
	 */
	private async scheduleDelayedCategoryUpdate(category: string): Promise<void>
	{
		const delayKey = `category_update_scheduled:${category}`;
		const redis = this.dbManager.getRedisClient();

		// Check if category update is already scheduled
		const isScheduled = await redis.get(delayKey);
		if (isScheduled)
		{
			return; // Already scheduled
		}

		// Mark as scheduled for 10 minutes
		await redis.setex(delayKey, 600, 'true');

		// Schedule category ranking update with delay
		setTimeout(async () =>
		{
			try
			{
				const domains = await this.getDomainsForCategory(category);
				if (domains.length > 0)
				{
					await this.triggerBatchRankingUpdate(domains, 'category', category, 'low');
				}
			}
			catch (error)
			{
				logger.error(`Failed to trigger delayed category update for: ${category}:`, error);
			}
		}, 600000); // 10 minutes delay

		logger.debug(`Delayed category update scheduled for: ${category}`);
	}

	/**
	 * Get domains for ranking calculation
	 */
	private async getDomainsForRanking(category?: string): Promise<string[]>
	{
		try
		{
			const scylla = this.dbManager.getScyllaClient();

			let query = 'SELECT domain FROM domain_analysis';
			const params: unknown[] = [];

			if (category)
			{
				query += ' WHERE category = ?';
				params.push(category);
			}

			query += ' LIMIT 10000'; // Reasonable limit for batch processing

			const result = await scylla.execute(query, params);
			return result.rows.map((row: unknown) => (row as { domain: string }).domain);
		}
		catch (error)
		{
			logger.error('Failed to get domains for ranking:', error);
			return [];
		}
	}

	/**
	 * Get domains for a specific category
	 */
	private async getDomainsForCategory(category: string): Promise<string[]>
	{
		try
		{
			const scylla = this.dbManager.getScyllaClient();

			const query = `
				SELECT domain
				FROM domain_analysis
				WHERE category = ?
				LIMIT 1000
			`;

			const result = await scylla.execute(query, [category]);
			return result.rows.map((row: unknown) => (row as { domain: string }).domain);
		}
		catch (error)
		{
			logger.error(`Failed to get domains for category: ${category}:`, error);
			return [];
		}
	}

	/**
	 * Schedule recurring ranking update
	 */
	private async scheduleRecurringUpdate(trigger: RankingUpdateTriggerType): Promise<void>
	{
		// This would integrate with a cron scheduler in production
		// For now, just log the scheduled update
		logger.info('Recurring ranking update scheduled', trigger);
	}

	/**
	 * Fetch domain data for ranking calculation
	 */
	private async fetchDomainData(domain: string): Promise<DomainDataType | null>
	{
		logger.debug(`Fetching domain data for: ${domain}`);
		try
		{
			const scylla = this.dbManager.getScyllaClient();
			const query = `
				SELECT domain, category, performance_metrics, security_metrics, seo_metrics,
				       technical_metrics, technologies, server_info, domain_age_days,
				       registrar, screenshot_urls, last_crawled
				FROM domain_analysis
				WHERE domain = ?
			`;
			const res = await scylla.execute(query, [domain]);
			const row = res.rows?.[0];
			if (!row) return null;

			const r = row as {
				domain: string;
				category?: string;
				performance_metrics?: unknown;
				security_metrics?: unknown;
				seo_metrics?: unknown;
				technical_metrics?: unknown;
				technologies?: unknown;
				server_info?: unknown;
				domain_age_days?: number;
				registrar?: string;
				screenshot_urls?: unknown;
				last_crawled?: string;
			};

			// Transform database row to DomainDataType
			const domainData: DomainDataType = {
				domain: r.domain,
				category: r.category,
				performanceMetrics: r.performance_metrics as PerformanceMetrics,
				securityMetrics: r.security_metrics as SecurityMetricsType,
				seoMetrics: r.seo_metrics as SEOMetrics,
				technicalMetrics: r.technical_metrics as TechnicalMetricsType,
				backlinkMetrics: undefined, // Would need to fetch from MariaDB
				lastUpdated: r.last_crawled ? new Date(r.last_crawled) : new Date(),
			};

			return domainData;
		}
		catch (error)
		{
			logger.error(`Failed to fetch domain data for ${domain}:`, error);
			return null;
		}
	}

	/**
	 * Store domain ranking in database
	 */
	private async storeDomainRanking(
		compositeScore: CompositeScoreType,
		rankingType: string,
		category?: string,
	): Promise<void>
	{
		try
		{
			const scylla = this.dbManager.getScyllaClient();

			// Store in domain_rankings table
			const query = `
				INSERT INTO domain_rankings (domain, ranking_type, rank, overall_score, grade,
				                           performance_score, security_score, seo_score,
				                           technical_score, backlink_score, last_updated)
				VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
			`;

			await scylla.execute(query, [
				compositeScore.domain,
				category ? `category:${category}` : 'global',
				compositeScore.globalRank || compositeScore.categoryRank || 0,
				compositeScore.overallScore,
				compositeScore.grade,
				compositeScore.scores.performance,
				compositeScore.scores.security,
				compositeScore.scores.seo,
				compositeScore.scores.technical,
				compositeScore.scores.backlinks,
				new Date(),
			]);

			logger.debug(`Domain ranking stored for: ${compositeScore.domain}`);
		}
		catch (error)
		{
			logger.error(`Failed to store domain ranking for: ${compositeScore.domain}:`, error);
			throw error;
		}
	}

	/**
	 * Store multiple rankings in database
	 */
	private async storeRankings(
		rankings: RankingResultType[],
		rankingType: string,
		category?: string,
	): Promise<void>
	{
		try
		{
			const scylla = this.dbManager.getScyllaClient();
			const rankingTypeKey = category ? `category:${category}` : 'global';

			// Prepare batch statements
			const queries = [];

			for (const ranking of rankings)
			{
				queries.push({
					query: `
						INSERT INTO domain_rankings (domain, ranking_type, rank, overall_score, last_updated)
						VALUES (?, ?, ?, ?, ?)
					`,
					params: [ranking.domain, rankingTypeKey, ranking.rank, ranking.score, new Date()],
				});
			}

			// Execute batch
			for (const queryData of queries)
			{
				await scylla.execute(queryData.query, queryData.params);
			}

			logger.debug(`Stored ${rankings.length} rankings for type: ${rankingType}`);
		}
		catch (error)
		{
			logger.error(`Failed to store rankings for type: ${rankingType}:`, error);
			throw error;
		}
	}

	/**
	 * Get batch status
	 */
	getBatchStatus(batchId: string): BatchRankingUpdateType | undefined
	{
		return this.activeBatches.get(batchId);
	}

	/**
	 * Get all active batches
	 */
	getActiveBatches(): BatchRankingUpdateType[]
	{
		return Array.from(this.activeBatches.values());
	}

	/**
	 * Clean up completed batches
	 */
	cleanupCompletedBatches(): void
	{
		const cutoffTime = new Date();
		cutoffTime.setHours(cutoffTime.getHours() - 24); // Keep for 24 hours

		for (const [batchId, batch] of this.activeBatches)
		{
			if (batch.status === 'completed' && batch.completedAt && batch.completedAt < cutoffTime)
			{
				this.activeBatches.delete(batchId);
			}
		}
	}

	/**
	 * Generate unique batch ID
	 */
	private generateBatchId(): string
	{
		return IdGenerator.batchId();
	}

	/**
	 * Generate unique trigger ID
	 */
	private generateTriggerId(): string
	{
		return IdGenerator.generate({ prefix: 'trigger' }).replace(/-/g, '_');
	}

	/**
	 * Convert priority string to numeric value
	 */
	private getPriorityValue(priority: 'low' | 'medium' | 'high'): number
	{
		const priorities = {
			low: 1,
			medium: 5,
			high: 10,
		};
		return priorities[priority] || 5;
	}

	/**
	 * Get current rankings for a category or global
	 */
	async getCurrentRankings(category?: string, limit: number = 100): Promise<RankingResultType[]>
	{
		try
		{
			const scylla = this.dbManager.getScyllaClient();

			const rankingType = category ? `category:${category}` : 'global';
			const query = `
				SELECT domain, rank, overall_score, last_updated
				FROM domain_rankings
				WHERE ranking_type = ?
				ORDER BY rank ASC
				LIMIT ?
			`;

			const result = await scylla.execute(query, [rankingType, limit]);

			return result.rows.map((row: unknown) =>
			{
				const r = row as {
					domain: string;
					rank: number;
					overall_score: number;
					last_updated: string;
				};
				return {
					domain: r.domain,
					rank: r.rank,
					score: r.overall_score,
					category,
				};
			});
		}
		catch (error)
		{
			logger.error('Failed to get current rankings:', error);
			return [];
		}
	}

	/**
	 * Get ranking history for trend analysis
	 */
	async getRankingHistory(domain: string, days: number = 30): Promise<RankingHistoryEntryType[]>
	{
		try
		{
			const scylla = this.dbManager.getScyllaClient();

			const startDate = new Date();
			startDate.setDate(startDate.getDate() - days);

			const query = `
				SELECT date, ranking_type, rank, overall_score AS score
				FROM domain_ranking_history
				WHERE domain = ? AND date >= ?
				ORDER BY date DESC
			`;

			const result = await scylla.execute(query, [domain, startDate.toISOString().split('T')[0]]);

			return result.rows.map((row: unknown) =>
			{
				const r = row as {
					date: string;
					ranking_type: string;
					rank: number;
					score: number;
				};
				return {
					domain,
					date: r.date,
					rankingType: r.ranking_type,
					rank: r.rank,
					score: r.score,
				};
			});
		}
		catch (error)
		{
			logger.error(`Failed to get ranking history for ${domain}:`, error);
			return [];
		}
	}

	/**
	 * Update ranking weights and recalculate
	 */
	async updateRankingWeights(newWeights: Partial<RankingWeightsType>): Promise<void>
	{
		try
		{
			// Update composite ranker weights
			this.compositeRanker.updateWeights(newWeights);

			// Schedule global ranking recalculation
			await this.recalculateGlobalRankings();

			logger.info('Ranking weights updated and recalculation scheduled', {
				newWeights,
			});
		}
		catch (error)
		{
			logger.error('Failed to update ranking weights:', error);
			throw error;
		}
	}

	/**
	 * Recalculate global rankings
	 */
	async recalculateGlobalRankings(): Promise<void>
	{
		logger.info('Starting global rankings recalculation...');

		try
		{
			// Get all domains
			const domains = await this.getDomainsForRanking();

			if (domains.length === 0)
			{
				logger.warn('No domains found for global ranking recalculation');
				return;
			}

			// Schedule batch ranking job
			await this.triggerBatchRankingUpdate(domains, 'global', undefined, 'medium');

			logger.info(`Scheduled global rankings recalculation for ${domains.length} domains`);
		}
		catch (error)
		{
			logger.error('Failed to recalculate global rankings:', error);
			throw error;
		}
	}

	/**
	 * Recalculate category rankings
	 */
	async recalculateCategoryRankings(category: string): Promise<void>
	{
		logger.info(`Starting category rankings recalculation for: ${category}`);

		try
		{
			// Get domains for category
			const domains = await this.getDomainsForCategory(category);

			if (domains.length === 0)
			{
				logger.warn(`No domains found for category ranking recalculation: ${category}`);
				return;
			}

			// Schedule batch ranking job
			await this.triggerBatchRankingUpdate(domains, 'category', category, 'medium');

			logger.info(`Scheduled category rankings recalculation for ${domains.length} domains in category: ${category}`);
		}
		catch (error)
		{
			logger.error(`Failed to recalculate category rankings for ${category}:`, error);
			throw error;
		}
	}

	/**
	 * Get ranking statistics
	 */
	async getRankingStatistics(): Promise<{
		global: RankingStatisticsType;
		categories: Record<string, RankingStatisticsType>;
	}>
	{
		try
		{
			const globalRankings = await this.getCurrentRankings(undefined, 1000);
			const globalStats = this.compositeRanker.getRankingStatistics(globalRankings);

			// Discover categories dynamically (fallback to static defaults)
			const discoveredCategories = await this.getTopCategories(10);
			const categories = discoveredCategories.length > 0
				? discoveredCategories
				: ['technology', 'business', 'education', 'entertainment', 'news'];
			const categoryStats: Record<string, RankingStatisticsType> = {};

			for (const category of categories)
			{
				const categoryRankings = await this.getCurrentRankings(category, 100);
				categoryStats[category] = this.compositeRanker.getRankingStatistics(categoryRankings);
			}

			return {
				global: globalStats,
				categories: categoryStats,
			};
		}
		catch (error)
		{
			logger.error('Failed to get ranking statistics:', error);
			throw error;
		}
	}

	/**
	 * Discover top categories via search facets
	 */
	private async getTopCategories(limit: number = 10): Promise<string[]>
	{
		try
		{
			const manticore = this.dbManager.getManticoreClient();
			const res = await manticore.searchDomains({
				query: '',
				limit: 1,
				offset: 0,
				facets: ['category'],
			});

			const facets = (res as any)?.facets?.category || [];
			const categories: string[] = facets
				.map((f: any) => String(f.value || '').toLowerCase())
				.filter((v: string) => v && v !== 'unknown');

			if (categories.length > 0)
			{
				return Array.from(new Set(categories)).slice(0, limit);
			}

			// Fallback to categories from data/categories.json
			return this.getFallbackCategories(limit);
		}
		catch (e)
		{
			logger.warn('Category discovery failed, using fallback categories', e);
			return this.getFallbackCategories(limit);
		}
	}

	/**
	 * Get fallback categories from categories.json file
	 */
	private async getFallbackCategories(limit: number = 10): Promise<string[]>
	{
		try
		{
			// Import categories from shared data
			const categoriesPath = require.resolve('@domain-seeder/data/categories.json');
			const categoriesData = require(categoriesPath);

			const mainCategories = Object.keys(categoriesData);
			return mainCategories.slice(0, limit);
		}
		catch (error)
		{
			logger.warn('Failed to load categories.json, using hardcoded fallback', error);
			// Ultimate fallback to hardcoded categories
			return ['technology', 'business', 'education', 'entertainment', 'news-media', 'health', 'finance', 'e-commerce', 'travel', 'food-drink'].slice(0, limit);
		}
	}

	/**
	 * Health check for ranking update service
	 */
	async healthCheck(): Promise<boolean>
	{
		try
		{
			// Check database connections
			const dbHealth = await this.dbManager.healthCheck();
			if (!dbHealth)
			{
				return false;
			}

			// Check job queue
			const queueHealth = await this.jobQueue.getHealth();
			if (queueHealth.status !== 'healthy')
			{
				return false;
			}

			return true;
		}
		catch (error)
		{
			logger.error('Ranking update service health check failed:', error);
			return false;
		}
	}
}

export type {
	RankingUpdateTriggerType,
	BatchRankingUpdateType,
	RankingHistoryEntryType,
	RankingTrendAnalysisType,
};

export default RankingUpdateService;
