# Ranking System

This directory contains the complete ranking calculation functionality extracted from the ranking-engine service.

## Components

### Individual Scorers

- **PerformanceScorer**: Core Web Vitals scoring, load time analysis, performance metrics calculation
- **SecurityScorer**: SSL grading, security headers assessment, vulnerability scoring, certificate analysis
- **SEOScorer**: Meta tags analysis, robots.txt compliance, sitemap evaluation, content quality scoring
- **TechnicalScorer**: DNS configuration, hosting quality, infrastructure assessment, uptime monitoring
- **BacklinkScorer**: Domain authority calculations, link quality assessment, referral analysis

### Composite Ranking

- **CompositeRanker**: Combines all individual scores with configurable weights
- **RankingCalculator**: Main interface for calculating domain rankings
- **RankingValidator**: Validates scoring results and ensures quality assurance

## Usage

```typescript
import { RankingCalculator } from "./RankingCalculator";

const calculator = new RankingCalculator();
const score = await calculator.calculateDomainScore(domainData);
```

## Scoring Weights

Default weights for composite scoring:

- Performance: 25%
- Security: 20%
- SEO: 20%
- Technical: 15%
- Backlinks: 20%

Weights can be configured via environment variables or runtime configuration.
