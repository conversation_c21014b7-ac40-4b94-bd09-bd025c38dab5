import { logger as sharedLogger } from '@shared';
// import { CoreWebVitals , PerformanceMetrics } from '..';

const logger = sharedLogger.getLogger('PerformanceScorer');

type PerformanceMetricsType =
{
	loadTime?: number;
	firstContentfulPaint?: number;
	largestContentfulPaint?: number;
	cumulativeLayoutShift?: number;
	firstInputDelay?: number;
	speedIndex?: number;
	responseTime?: number;
	pageSize?: number;
	resourceCount?: number;
};

type CoreWebVitalsType =
{
	lcp?: number; // Largest Contentful Paint
	fid?: number; // First Input Delay
	cls?: number; // Cumulative Layout Shift
	fcp?: number; // First Contentful Paint
	ttfb?: number; // Time to First Byte
};

type PerformanceBreakdownType =
{
	totalScore: number;
	grade: string;
	breakdown: {
		responseTime: {
			score: number;
			weight: number;
			contribution: number;
		};
		coreWebVitals: {
			score: number;
			weight: number;
			contribution: number;
		};
		pageSize: {
			score: number;
			weight: number;
			contribution: number;
		};
		resourceEfficiency: {
			score: number;
			weight: number;
			contribution: number;
		};
	};
	recommendations?: string[];
};

/**
 * Performance scoring algorithm based on Core Web Vitals and load metrics
 */
class PerformanceScorer
{
	private static readonly WEIGHTS = {
		RESPONSE_TIME: 0.25,
		CORE_WEB_VITALS: 0.45,
		PAGE_SIZE: 0.15,
		RESOURCE_EFFICIENCY: 0.15,
	};

	private static readonly THRESHOLDS = {
		RESPONSE_TIME: {
			EXCELLENT: 1000,
			GOOD: 2000,
			POOR: 5000,
		},
		LCP: {
			GOOD: 2.5,
			NEEDS_IMPROVEMENT: 4.0,
		},
		FID: {
			GOOD: 0.1,
			NEEDS_IMPROVEMENT: 0.3,
		},
		CLS: {
			GOOD: 0.1,
			NEEDS_IMPROVEMENT: 0.25,
		},
		PAGE_SIZE: {
			EXCELLENT: 1024 * 1024, // 1MB
			GOOD: 3 * 1024 * 1024, // 3MB
			POOR: 10 * 1024 * 1024, // 10MB
		},
	};

	/**
	 * Calculate performance score based on various metrics
	 */
	static calculateScore(metrics: PerformanceMetricsType, coreWebVitals?: CoreWebVitalsType): number
	{
		try
		{
			let totalScore = 0;

			// Response time scoring
			const responseTimeScore = this.calculateResponseTimeScore(
				metrics.responseTime || metrics.loadTime || 5000,
			);
			totalScore += responseTimeScore * this.WEIGHTS.RESPONSE_TIME;

			// Core Web Vitals scoring
			if (coreWebVitals)
			{
				const cwvScore = this.calculateCoreWebVitalsScore(coreWebVitals);
				totalScore += cwvScore * this.WEIGHTS.CORE_WEB_VITALS;
			}
			else
			{
				// Fallback to basic metrics if CWV not available
				const basicScore = this.calculateBasicPerformanceScore(metrics);
				totalScore += basicScore * this.WEIGHTS.CORE_WEB_VITALS;
			}

			// Page size scoring
			const pageSizeScore = this.calculatePageSizeScore(metrics.pageSize || 0);
			totalScore += pageSizeScore * this.WEIGHTS.PAGE_SIZE;

			// Resource efficiency scoring
			const resourceScore = this.calculateResourceEfficiencyScore(
				metrics.resourceCount || 0,
				metrics.pageSize || 0,
			);
			totalScore += resourceScore * this.WEIGHTS.RESOURCE_EFFICIENCY;

			const finalScore = Math.min(1.0, Math.max(0.0, totalScore));

			logger.debug('Performance score calculated', {
				responseTimeScore,
				pageSizeScore,
				resourceScore,
				finalScore,
				metrics,
			});

			return Math.round(finalScore * 1000) / 1000;
		}
		catch (error)
		{
			logger.error('Error calculating performance score:', error);
			return 0.5; // Default fallback score
		}
	}

	/**
	 * Calculate response time score
	 */
	private static calculateResponseTimeScore(responseTime: number): number
	{
		if (responseTime <= this.THRESHOLDS.RESPONSE_TIME.EXCELLENT)
		{
			return 1.0;
		}
		if (responseTime <= this.THRESHOLDS.RESPONSE_TIME.GOOD)
		{
			// Linear interpolation between excellent and good
			const ratio = (responseTime - this.THRESHOLDS.RESPONSE_TIME.EXCELLENT)
				/ (this.THRESHOLDS.RESPONSE_TIME.GOOD - this.THRESHOLDS.RESPONSE_TIME.EXCELLENT);
			return 1.0 - (ratio * 0.3); // Score between 0.7 and 1.0
		}
		if (responseTime <= this.THRESHOLDS.RESPONSE_TIME.POOR)
		{
			// Linear interpolation between good and poor
			const ratio = (responseTime - this.THRESHOLDS.RESPONSE_TIME.GOOD)
				/ (this.THRESHOLDS.RESPONSE_TIME.POOR - this.THRESHOLDS.RESPONSE_TIME.GOOD);
			return 0.7 - (ratio * 0.5); // Score between 0.2 and 0.7
		}

		return 0.1; // Very poor performance
	}

	/**
	 * Calculate Core Web Vitals score
	 */
	private static calculateCoreWebVitalsScore(cwv: CoreWebVitalsType): number
	{
		let score = 0;
		let metricCount = 0;

		// LCP scoring (40% weight)
		if (cwv.lcp !== undefined)
		{
			metricCount++;
			if (cwv.lcp <= this.THRESHOLDS.LCP.GOOD)
			{
				score += 0.4;
			}
			else if (cwv.lcp <= this.THRESHOLDS.LCP.NEEDS_IMPROVEMENT)
			{
				const ratio = (cwv.lcp - this.THRESHOLDS.LCP.GOOD)
					/ (this.THRESHOLDS.LCP.NEEDS_IMPROVEMENT - this.THRESHOLDS.LCP.GOOD);
				score += 0.4 * (1 - ratio * 0.5); // Score between 0.2 and 0.4
			}
			else
			{
				score += 0.1;
			}
		}

		// FID scoring (30% weight)
		if (cwv.fid !== undefined)
		{
			metricCount++;
			if (cwv.fid <= this.THRESHOLDS.FID.GOOD)
			{
				score += 0.3;
			}
			else if (cwv.fid <= this.THRESHOLDS.FID.NEEDS_IMPROVEMENT)
			{
				const ratio = (cwv.fid - this.THRESHOLDS.FID.GOOD)
					/ (this.THRESHOLDS.FID.NEEDS_IMPROVEMENT - this.THRESHOLDS.FID.GOOD);
				score += 0.3 * (1 - ratio * 0.5); // Score between 0.15 and 0.3
			}
			else
			{
				score += 0.05;
			}
		}

		// CLS scoring (30% weight)
		if (cwv.cls !== undefined)
		{
			metricCount++;
			if (cwv.cls <= this.THRESHOLDS.CLS.GOOD)
			{
				score += 0.3;
			}
			else if (cwv.cls <= this.THRESHOLDS.CLS.NEEDS_IMPROVEMENT)
			{
				const ratio = (cwv.cls - this.THRESHOLDS.CLS.GOOD)
					/ (this.THRESHOLDS.CLS.NEEDS_IMPROVEMENT - this.THRESHOLDS.CLS.GOOD);
				score += 0.3 * (1 - ratio * 0.5); // Score between 0.15 and 0.3
			}
			else
			{
				score += 0.05;
			}
		}

		// If no CWV metrics available, return default
		if (metricCount === 0)
		{
			return 0.5;
		}

		return score;
	}

	/**
	 * Calculate basic performance score when CWV is not available
	 */
	private static calculateBasicPerformanceScore(metrics: PerformanceMetricsType): number
	{
		let score = 0.3; // Base score

		// FCP scoring
		if (metrics.firstContentfulPaint)
		{
			if (metrics.firstContentfulPaint <= 1800)
			{
				score += 0.2;
			}
			else if (metrics.firstContentfulPaint <= 3000)
			{
				score += 0.1;
			}
		}

		// Speed index scoring
		if (metrics.speedIndex)
		{
			if (metrics.speedIndex <= 3400)
			{
				score += 0.2;
			}
			else if (metrics.speedIndex <= 5800)
			{
				score += 0.1;
			}
		}

		return Math.min(1.0, score);
	}

	/**
	 * Calculate page size score
	 */
	private static calculatePageSizeScore(pageSize: number): number
	{
		if (pageSize === 0)
		{
			return 0.5; // Default when size unknown
		}

		if (pageSize <= this.THRESHOLDS.PAGE_SIZE.EXCELLENT)
		{
			return 1.0;
		}
		if (pageSize <= this.THRESHOLDS.PAGE_SIZE.GOOD)
		{
			const ratio = (pageSize - this.THRESHOLDS.PAGE_SIZE.EXCELLENT)
				/ (this.THRESHOLDS.PAGE_SIZE.GOOD - this.THRESHOLDS.PAGE_SIZE.EXCELLENT);
			return 1.0 - (ratio * 0.3); // Score between 0.7 and 1.0
		}
		if (pageSize <= this.THRESHOLDS.PAGE_SIZE.POOR)
		{
			const ratio = (pageSize - this.THRESHOLDS.PAGE_SIZE.GOOD)
				/ (this.THRESHOLDS.PAGE_SIZE.POOR - this.THRESHOLDS.PAGE_SIZE.GOOD);
			return 0.7 - (ratio * 0.5); // Score between 0.2 and 0.7
		}

		return 0.1;
	}

	/**
	 * Calculate resource efficiency score
	 */
	private static calculateResourceEfficiencyScore(resourceCount: number, pageSize: number): number
	{
		if (resourceCount === 0)
		{
			return 0.5; // Default when count unknown
		}

		let score = 0.5;

		// Resource count scoring
		if (resourceCount <= 50)
		{
			score += 0.3;
		}
		else if (resourceCount <= 100)
		{
			score += 0.2;
		}
		else if (resourceCount <= 200)
		{
			score += 0.1;
		}

		// Resource efficiency (average size per resource)
		if (pageSize > 0 && resourceCount > 0)
		{
			const avgResourceSize = pageSize / resourceCount;
			if (avgResourceSize <= 20 * 1024) // 20KB average
			{
				score += 0.2;
			}
			else if (avgResourceSize <= 50 * 1024) // 50KB average
			{
				score += 0.1;
			}
		}

		return Math.min(1.0, score);
	}

	/**
	 * Get performance grade based on score
	 */
	static getPerformanceGrade(score: number): string
	{
		if (score >= 0.9) return 'A+';
		if (score >= 0.8) return 'A';
		if (score >= 0.7) return 'B';
		if (score >= 0.6) return 'C';
		if (score >= 0.5) return 'D';
		return 'F';
	}

	/**
	 * Get detailed performance breakdown
	 */
	static getPerformanceBreakdown(
		metrics: PerformanceMetricsType,
		coreWebVitals?: CoreWebVitalsType,
	): PerformanceBreakdownType
	{
		const responseTimeScore = this.calculateResponseTimeScore(
			metrics.responseTime || metrics.loadTime || 5000,
		);
		const pageSizeScore = this.calculatePageSizeScore(metrics.pageSize || 0);
		const resourceScore = this.calculateResourceEfficiencyScore(
			metrics.resourceCount || 0,
			metrics.pageSize || 0,
		);

		let cwvScore = 0.5;
		if (coreWebVitals)
		{
			cwvScore = this.calculateCoreWebVitalsScore(coreWebVitals);
		}
		else
		{
			cwvScore = this.calculateBasicPerformanceScore(metrics);
		}

		const totalScore = (
			responseTimeScore * this.WEIGHTS.RESPONSE_TIME
			+ cwvScore * this.WEIGHTS.CORE_WEB_VITALS
			+ pageSizeScore * this.WEIGHTS.PAGE_SIZE
			+ resourceScore * this.WEIGHTS.RESOURCE_EFFICIENCY
		);

		return ({
			totalScore: Math.round(totalScore * 1000) / 1000,
			grade: this.getPerformanceGrade(totalScore),
			breakdown: {
				responseTime: {
					score: Math.round(responseTimeScore * 1000) / 1000,
					weight: this.WEIGHTS.RESPONSE_TIME,
					contribution: Math.round(responseTimeScore * this.WEIGHTS.RESPONSE_TIME * 1000) / 1000,
				},
				coreWebVitals: {
					score: Math.round(cwvScore * 1000) / 1000,
					weight: this.WEIGHTS.CORE_WEB_VITALS,
					contribution: Math.round(cwvScore * this.WEIGHTS.CORE_WEB_VITALS * 1000) / 1000,
				},
				pageSize: {
					score: Math.round(pageSizeScore * 1000) / 1000,
					weight: this.WEIGHTS.PAGE_SIZE,
					contribution: Math.round(pageSizeScore * this.WEIGHTS.PAGE_SIZE * 1000) / 1000,
				},
				resourceEfficiency: {
					score: Math.round(resourceScore * 1000) / 1000,
					weight: this.WEIGHTS.RESOURCE_EFFICIENCY,
					contribution: Math.round(resourceScore * this.WEIGHTS.RESOURCE_EFFICIENCY * 1000) / 1000,
				},
			},
		});
	}
}

export type {
	PerformanceMetricsType,
	CoreWebVitalsType,
};

export default PerformanceScorer;
