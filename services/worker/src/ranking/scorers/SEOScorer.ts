import { logger as sharedLogger } from '@shared';

const logger = sharedLogger.getLogger('SEOScorer');

export interface SEOMetrics
{
	metaTags?: MetaTags;
	robotsTxt?: RobotsInfo;
	sitemap?: SitemapInfo;
	structuredData?: StructuredDataInfo;
	contentMetrics?: ContentMetrics;
	technicalSEO?: TechnicalSEOMetrics;
	socialMetrics?: SocialMetrics;
}

export interface MetaTags
{
	title?: string;
	description?: string;
	keywords?: string[];
	ogTitle?: string;
	ogDescription?: string;
	ogImage?: string;
	twitterCard?: string;
	canonical?: string;
	robots?: string;
	viewport?: string;
	charset?: string;
}

export interface RobotsInfo
{
	exists: boolean;
	isValid: boolean;
	allowsCrawling: boolean;
	hasDisallowRules: boolean;
	hasSitemapReference: boolean;
	crawlDelay?: number;
	userAgents: string[];
}

export interface SitemapInfo
{
	exists: boolean;
	isValid: boolean;
	urlCount: number;
	lastModified?: Date;
	hasImages: boolean;
	hasVideos: boolean;
	hasNews: boolean;
}

export interface StructuredDataInfo
{
	hasStructuredData: boolean;
	types: string[];
	isValid: boolean;
	errors: string[];
}

export interface ContentMetrics
{
	wordCount: number;
	headingStructure: HeadingStructure;
	imageCount: number;
	imageAltTextRatio: number;
	internalLinkCount: number;
	externalLinkCount: number;
	readabilityScore?: number;
	languageDetected?: string;
}

export interface HeadingStructure
{
	h1Count: number;
	h2Count: number;
	h3Count: number;
	h4Count: number;
	h5Count: number;
	h6Count: number;
	hasProperHierarchy: boolean;
}

export interface TechnicalSEOMetrics
{
	pageLoadSpeed: number;
	mobileOptimized: boolean;
	httpsEnabled: boolean;
	hasCanonical: boolean;
	hasHreflang: boolean;
	breadcrumbsPresent: boolean;
	schemaMarkupPresent: boolean;
}

export interface SocialMetrics
{
	hasOpenGraph: boolean;
	hasTwitterCards: boolean;
	socialShareCount?: number;
	socialMentions?: number;
}

/**
 * SEO scoring algorithm based on on-page optimization and technical factors
 */
class SEOScorer
{
	private static readonly WEIGHTS = {
		META_TAGS: 0.25,
		CONTENT_QUALITY: 0.20,
		TECHNICAL_SEO: 0.20,
		ROBOTS_SITEMAP: 0.15,
		STRUCTURED_DATA: 0.10,
		SOCIAL_SIGNALS: 0.10,
	};

	private static readonly CONTENT_THRESHOLDS = {
		WORD_COUNT: {
			EXCELLENT: 1500,
			GOOD: 800,
			MINIMUM: 300,
		},
		READABILITY: {
			EXCELLENT: 70,
			GOOD: 60,
			POOR: 30,
		},
	};

	/**
	 * Calculate SEO score based on various SEO metrics
	 */
	static calculateScore(metrics: SEOMetrics): number
	{
		try
		{
			let totalScore = 0;

			// Meta tags scoring
			const metaScore = this.calculateMetaTagsScore(metrics.metaTags);
			totalScore += metaScore * this.WEIGHTS.META_TAGS;

			// Content quality scoring
			const contentScore = this.calculateContentScore(metrics.contentMetrics);
			totalScore += contentScore * this.WEIGHTS.CONTENT_QUALITY;

			// Technical SEO scoring
			const technicalScore = this.calculateTechnicalSEOScore(metrics.technicalSEO);
			totalScore += technicalScore * this.WEIGHTS.TECHNICAL_SEO;

			// Robots.txt and sitemap scoring
			const robotsSitemapScore = this.calculateRobotsSitemapScore(
				metrics.robotsTxt,
				metrics.sitemap,
			);
			totalScore += robotsSitemapScore * this.WEIGHTS.ROBOTS_SITEMAP;

			// Structured data scoring
			const structuredDataScore = this.calculateStructuredDataScore(metrics.structuredData);
			totalScore += structuredDataScore * this.WEIGHTS.STRUCTURED_DATA;

			// Social signals scoring
			const socialScore = this.calculateSocialScore(metrics.socialMetrics);
			totalScore += socialScore * this.WEIGHTS.SOCIAL_SIGNALS;

			const finalScore = Math.min(1.0, Math.max(0.0, totalScore));

			logger.debug('SEO score calculated', {
				metaScore,
				contentScore,
				technicalScore,
				robotsSitemapScore,
				structuredDataScore,
				socialScore,
				finalScore,
				metrics,
			});

			return Math.round(finalScore * 1000) / 1000;
		}
		catch (error)
		{
			logger.error('Error calculating SEO score:', error);
			return 0.5; // Default fallback score
		}
	}

	/**
	 * Calculate meta tags score
	 */
	private static calculateMetaTagsScore(metaTags?: MetaTags): number
	{
		if (!metaTags)
		{
			return 0.1; // Minimal score for no meta tags
		}

		let score = 0;

		// Title tag (most important)
		if (metaTags.title)
		{
			score += 0.3;
			// Bonus for optimal title length (30-60 characters)
			if (metaTags.title.length >= 30 && metaTags.title.length <= 60)
			{
				score += 0.1;
			}
		}

		// Meta description
		if (metaTags.description)
		{
			score += 0.2;
			// Bonus for optimal description length (120-160 characters)
			if (metaTags.description.length >= 120 && metaTags.description.length <= 160)
			{
				score += 0.1;
			}
		}

		// Canonical URL
		if (metaTags.canonical)
		{
			score += 0.1;
		}

		// Open Graph tags
		if (metaTags.ogTitle && metaTags.ogDescription)
		{
			score += 0.1;
		}

		// Twitter Cards
		if (metaTags.twitterCard)
		{
			score += 0.05;
		}

		// Viewport meta tag (mobile optimization)
		if (metaTags.viewport)
		{
			score += 0.05;
		}

		// Charset declaration
		if (metaTags.charset)
		{
			score += 0.05;
		}

		// Robots meta tag (if present and not blocking)
		if (metaTags.robots && !metaTags.robots.includes('noindex'))
		{
			score += 0.05;
		}

		return Math.min(1.0, score);
	}

	/**
	 * Calculate content quality score
	 */
	private static calculateContentScore(contentMetrics?: ContentMetrics): number
	{
		if (!contentMetrics)
		{
			return 0.3; // Default score when content metrics unavailable
		}

		let score = 0;

		// Word count scoring
		const wordCount = contentMetrics.wordCount || 0;
		if (wordCount >= this.CONTENT_THRESHOLDS.WORD_COUNT.EXCELLENT)
		{
			score += 0.25;
		}
		else if (wordCount >= this.CONTENT_THRESHOLDS.WORD_COUNT.GOOD)
		{
			score += 0.2;
		}
		else if (wordCount >= this.CONTENT_THRESHOLDS.WORD_COUNT.MINIMUM)
		{
			score += 0.15;
		}
		else if (wordCount > 0)
		{
			score += 0.1;
		}

		// Heading structure scoring
		if (contentMetrics.headingStructure)
		{
			const headings = contentMetrics.headingStructure;

			// H1 tag (should have exactly one)
			if (headings.h1Count === 1)
			{
				score += 0.1;
			}
			else if (headings.h1Count > 1)
			{
				score += 0.05; // Penalty for multiple H1s
			}

			// Proper heading hierarchy
			if (headings.hasProperHierarchy)
			{
				score += 0.1;
			}

			// Presence of subheadings
			if (headings.h2Count > 0)
			{
				score += 0.05;
			}
		}

		// Image optimization scoring
		if (contentMetrics.imageCount > 0)
		{
			score += 0.05; // Bonus for having images

			// Alt text ratio scoring
			const altTextRatio = contentMetrics.imageAltTextRatio || 0;
			if (altTextRatio >= 0.9)
			{
				score += 0.1; // Excellent alt text coverage
			}
			else if (altTextRatio >= 0.7)
			{
				score += 0.07; // Good alt text coverage
			}
			else if (altTextRatio >= 0.5)
			{
				score += 0.05; // Fair alt text coverage
			}
		}

		// Internal linking scoring
		if (contentMetrics.internalLinkCount > 0)
		{
			score += 0.05;
			if (contentMetrics.internalLinkCount >= 3)
			{
				score += 0.05; // Bonus for good internal linking
			}
		}

		// Readability scoring
		if (contentMetrics.readabilityScore)
		{
			const readability = contentMetrics.readabilityScore;
			if (readability >= this.CONTENT_THRESHOLDS.READABILITY.EXCELLENT)
			{
				score += 0.1;
			}
			else if (readability >= this.CONTENT_THRESHOLDS.READABILITY.GOOD)
			{
				score += 0.07;
			}
			else if (readability >= this.CONTENT_THRESHOLDS.READABILITY.POOR)
			{
				score += 0.05;
			}
		}

		return Math.min(1.0, score);
	}

	/**
	 * Calculate technical SEO score
	 */
	private static calculateTechnicalSEOScore(technicalSEO?: TechnicalSEOMetrics): number
	{
		if (!technicalSEO)
		{
			return 0.3; // Default score when technical metrics unavailable
		}

		let score = 0;

		// HTTPS enabled
		if (technicalSEO.httpsEnabled)
		{
			score += 0.2;
		}

		// Mobile optimization
		if (technicalSEO.mobileOptimized)
		{
			score += 0.2;
		}

		// Page load speed
		if (technicalSEO.pageLoadSpeed <= 3)
		{
			score += 0.2; // Fast loading
		}
		else if (technicalSEO.pageLoadSpeed <= 5)
		{
			score += 0.15; // Moderate loading
		}
		else if (technicalSEO.pageLoadSpeed <= 8)
		{
			score += 0.1; // Slow loading
		}

		// Canonical URL
		if (technicalSEO.hasCanonical)
		{
			score += 0.1;
		}

		// Hreflang (international SEO)
		if (technicalSEO.hasHreflang)
		{
			score += 0.1;
		}

		// Breadcrumbs
		if (technicalSEO.breadcrumbsPresent)
		{
			score += 0.1;
		}

		// Schema markup
		if (technicalSEO.schemaMarkupPresent)
		{
			score += 0.1;
		}

		return Math.min(1.0, score);
	}

	/**
	 * Calculate robots.txt and sitemap score
	 */
	private static calculateRobotsSitemapScore(robotsTxt?: RobotsInfo, sitemap?: SitemapInfo): number
	{
		let score = 0;

		// Robots.txt scoring
		if (robotsTxt)
		{
			if (robotsTxt.exists)
			{
				score += 0.2;

				if (robotsTxt.isValid)
				{
					score += 0.1;
				}

				if (robotsTxt.allowsCrawling)
				{
					score += 0.1;
				}

				if (robotsTxt.hasSitemapReference)
				{
					score += 0.1;
				}
			}
		}

		// Sitemap scoring
		if (sitemap)
		{
			if (sitemap.exists)
			{
				score += 0.2;

				if (sitemap.isValid)
				{
					score += 0.1;
				}

				// URL count bonus
				if (sitemap.urlCount > 100)
				{
					score += 0.1;
				}
				else if (sitemap.urlCount > 10)
				{
					score += 0.05;
				}

				// Rich sitemaps bonus
				if (sitemap.hasImages || sitemap.hasVideos)
				{
					score += 0.05;
				}
			}
		}

		return Math.min(1.0, score);
	}

	/**
	 * Calculate structured data score
	 */
	private static calculateStructuredDataScore(structuredData?: StructuredDataInfo): number
	{
		if (!structuredData)
		{
			return 0.1; // Minimal score for no structured data
		}

		let score = 0;

		if (structuredData.hasStructuredData)
		{
			score += 0.5;

			if (structuredData.isValid)
			{
				score += 0.3;
			}

			// Bonus for multiple schema types
			if (structuredData.types.length > 1)
			{
				score += 0.1;
			}

			// Common beneficial schema types
			const beneficialTypes = [
				'Organization',
				'LocalBusiness',
				'Product',
				'Article',
				'BreadcrumbList',
				'FAQ',
				'Review',
			];

			const hasBeneficialTypes = structuredData.types.some(type => beneficialTypes.includes(type));

			if (hasBeneficialTypes)
			{
				score += 0.1;
			}
		}

		return Math.min(1.0, score);
	}

	/**
	 * Calculate social signals score
	 */
	private static calculateSocialScore(socialMetrics?: SocialMetrics): number
	{
		if (!socialMetrics)
		{
			return 0.3; // Default score when social metrics unavailable
		}

		let score = 0;

		// Open Graph tags
		if (socialMetrics.hasOpenGraph)
		{
			score += 0.3;
		}

		// Twitter Cards
		if (socialMetrics.hasTwitterCards)
		{
			score += 0.2;
		}

		// Social shares (if available)
		if (socialMetrics.socialShareCount)
		{
			if (socialMetrics.socialShareCount > 1000)
			{
				score += 0.3;
			}
			else if (socialMetrics.socialShareCount > 100)
			{
				score += 0.2;
			}
			else if (socialMetrics.socialShareCount > 10)
			{
				score += 0.1;
			}
			else if (socialMetrics.socialShareCount > 0)
			{
				score += 0.05;
			}
		}

		// Social mentions (if available)
		if (socialMetrics.socialMentions)
		{
			if (socialMetrics.socialMentions > 100)
			{
				score += 0.2;
			}
			else if (socialMetrics.socialMentions > 10)
			{
				score += 0.1;
			}
			else if (socialMetrics.socialMentions > 0)
			{
				score += 0.05;
			}
		}

		return Math.min(1.0, score);
	}

	/**
	 * Get SEO grade based on score
	 */
	static getSEOGrade(score: number): string
	{
		if (score >= 0.9) return 'A+';
		if (score >= 0.8) return 'A';
		if (score >= 0.7) return 'B';
		if (score >= 0.6) return 'C';
		if (score >= 0.5) return 'D';
		return 'F';
	}

	/**
	 * Get detailed SEO breakdown
	 */
	static getSEOBreakdown(metrics: SEOMetrics): any
	{
		const metaScore = this.calculateMetaTagsScore(metrics.metaTags);
		const contentScore = this.calculateContentScore(metrics.contentMetrics);
		const technicalScore = this.calculateTechnicalSEOScore(metrics.technicalSEO);
		const robotsSitemapScore = this.calculateRobotsSitemapScore(
			metrics.robotsTxt,
			metrics.sitemap,
		);
		const structuredDataScore = this.calculateStructuredDataScore(metrics.structuredData);
		const socialScore = this.calculateSocialScore(metrics.socialMetrics);

		const totalScore = (
			metaScore * this.WEIGHTS.META_TAGS
			+ contentScore * this.WEIGHTS.CONTENT_QUALITY
			+ technicalScore * this.WEIGHTS.TECHNICAL_SEO
			+ robotsSitemapScore * this.WEIGHTS.ROBOTS_SITEMAP
			+ structuredDataScore * this.WEIGHTS.STRUCTURED_DATA
			+ socialScore * this.WEIGHTS.SOCIAL_SIGNALS
		);

		return {
			totalScore: Math.round(totalScore * 1000) / 1000,
			grade: this.getSEOGrade(totalScore),
			breakdown: {
				metaTags: {
					score: Math.round(metaScore * 1000) / 1000,
					weight: this.WEIGHTS.META_TAGS,
					contribution: Math.round(metaScore * this.WEIGHTS.META_TAGS * 1000) / 1000,
				},
				contentQuality: {
					score: Math.round(contentScore * 1000) / 1000,
					weight: this.WEIGHTS.CONTENT_QUALITY,
					contribution: Math.round(contentScore * this.WEIGHTS.CONTENT_QUALITY * 1000) / 1000,
				},
				technicalSEO: {
					score: Math.round(technicalScore * 1000) / 1000,
					weight: this.WEIGHTS.TECHNICAL_SEO,
					contribution: Math.round(technicalScore * this.WEIGHTS.TECHNICAL_SEO * 1000) / 1000,
				},
				robotsSitemap: {
					score: Math.round(robotsSitemapScore * 1000) / 1000,
					weight: this.WEIGHTS.ROBOTS_SITEMAP,
					contribution: Math.round(robotsSitemapScore * this.WEIGHTS.ROBOTS_SITEMAP * 1000) / 1000,
				},
				structuredData: {
					score: Math.round(structuredDataScore * 1000) / 1000,
					weight: this.WEIGHTS.STRUCTURED_DATA,
					contribution: Math.round(structuredDataScore * this.WEIGHTS.STRUCTURED_DATA * 1000) / 1000,
				},
				socialSignals: {
					score: Math.round(socialScore * 1000) / 1000,
					weight: this.WEIGHTS.SOCIAL_SIGNALS,
					contribution: Math.round(socialScore * this.WEIGHTS.SOCIAL_SIGNALS * 1000) / 1000,
				},
			},
		};
	}

	/**
	 * Get SEO recommendations based on metrics
	 */
	static getSEORecommendations(metrics: SEOMetrics): string[]
	{
		const recommendations: string[] = [];

		// Meta tags recommendations
		if (!metrics.metaTags?.title)
		{
			recommendations.push('Add a descriptive title tag (30-60 characters)');
		}
		else if (metrics.metaTags.title.length < 30 || metrics.metaTags.title.length > 60)
		{
			recommendations.push('Optimize title tag length (30-60 characters)');
		}

		if (!metrics.metaTags?.description)
		{
			recommendations.push('Add a compelling meta description (120-160 characters)');
		}
		else if (metrics.metaTags.description.length < 120 || metrics.metaTags.description.length > 160)
		{
			recommendations.push('Optimize meta description length (120-160 characters)');
		}

		if (!metrics.metaTags?.canonical)
		{
			recommendations.push('Add canonical URL to prevent duplicate content issues');
		}

		// Content recommendations
		if (!metrics.contentMetrics || metrics.contentMetrics.wordCount < this.CONTENT_THRESHOLDS.WORD_COUNT.MINIMUM)
		{
			recommendations.push('Increase content length to at least 300 words');
		}

		if (metrics.contentMetrics?.headingStructure?.h1Count !== 1)
		{
			recommendations.push('Use exactly one H1 tag per page');
		}

		if (metrics.contentMetrics && metrics.contentMetrics.imageAltTextRatio < 0.9)
		{
			recommendations.push('Add alt text to all images for better accessibility and SEO');
		}

		// Technical SEO recommendations
		if (!metrics.technicalSEO?.httpsEnabled)
		{
			recommendations.push('Enable HTTPS for better security and SEO');
		}

		if (!metrics.technicalSEO?.mobileOptimized)
		{
			recommendations.push('Optimize website for mobile devices');
		}

		if (metrics.technicalSEO && metrics.technicalSEO.pageLoadSpeed > 3)
		{
			recommendations.push('Improve page loading speed (target under 3 seconds)');
		}

		// Robots.txt and sitemap recommendations
		if (!metrics.robotsTxt?.exists)
		{
			recommendations.push('Create a robots.txt file');
		}

		if (!metrics.sitemap?.exists)
		{
			recommendations.push('Create and submit an XML sitemap');
		}

		// Structured data recommendations
		if (!metrics.structuredData?.hasStructuredData)
		{
			recommendations.push('Implement structured data markup (Schema.org)');
		}

		// Social recommendations
		if (!metrics.socialMetrics?.hasOpenGraph)
		{
			recommendations.push('Add Open Graph tags for better social media sharing');
		}

		if (!metrics.socialMetrics?.hasTwitterCards)
		{
			recommendations.push('Add Twitter Card tags for better Twitter sharing');
		}

		return recommendations;
	}
}

export type {
	SEOMetrics,
	MetaTags,
	RobotsInfo,
	SitemapInfo,
	StructuredDataInfo,
	ContentMetrics,
	HeadingStructure,
	TechnicalSEOMetrics,
	SocialMetrics,
};

export { SEOScorer };

export default SEOScorer;
