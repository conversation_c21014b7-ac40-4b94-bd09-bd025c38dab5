import { logger as sharedLogger } from '@shared';

const logger = sharedLogger.getLogger('TechnicalScorer');

type TechnicalMetricsType =
{
	dnsResponseTime?: number;
	serverResponseTime?: number;
	supportsIpv6?: boolean;
	hasCdn?: boolean;
	compressionEnabled?: boolean;
	keepAliveEnabled?: boolean;
	serverSoftware?: string;
	hostingProvider?: string;
	uptimePercentage?: number;
	dnsConfiguration?: DNSConfigurationType;
	hostingQuality?: HostingQualityType;
	infrastructureAssessment?: InfrastructureAssessmentType;
};

type DNSConfigurationType =
{
	recordTypes: string[];
	nameservers: string[];
	ttlValues: Record<string, number>;
	dnssecEnabled?: boolean;
	redundancy?: boolean;
	geoDistribution?: boolean;
};

type HostingQualityType =
{
	provider: string;
	tier: 'enterprise' | 'business' | 'shared' | 'unknown';
	dataCenter?: string;
	networkQuality?: number;
	reliability?: number;
};

type InfrastructureAssessmentType =
{
	loadBalancing?: boolean;
	failoverCapability?: boolean;
	scalabilityRating?: number;
	securityRating?: number;
	performanceRating?: number;
};

/**
 * Technical scoring algorithm based on DNS configuration, hosting quality, and infrastructure assessment
 */
class TechnicalScorer
{
	private static readonly WEIGHTS = {
		DNS_PERFORMANCE: 0.20,
		SERVER_PERFORMANCE: 0.25,
		INFRASTRUCTURE: 0.25,
		HOSTING_QUALITY: 0.15,
		UPTIME_RELIABILITY: 0.15,
	};

	private static readonly THRESHOLDS = {
		DNS_RESPONSE_TIME: {
			EXCELLENT: 50,
			GOOD: 100,
			POOR: 200,
		},
		SERVER_RESPONSE_TIME: {
			EXCELLENT: 200,
			GOOD: 500,
			POOR: 1000,
		},
		UPTIME: {
			EXCELLENT: 99.9,
			GOOD: 99.5,
			FAIR: 99.0,
		},
	};

	private static readonly HOSTING_TIERS = {
		enterprise: 1.0,
		business: 0.8,
		shared: 0.5,
		unknown: 0.3,
	};

	/**
	 * Calculate technical score based on infrastructure metrics
	 */
	static calculateScore(metrics: TechnicalMetricsType): number
	{
		try
		{
			let totalScore = 0;

			// DNS performance scoring
			const dnsScore = this.calculateDNSScore(metrics);
			totalScore += dnsScore * this.WEIGHTS.DNS_PERFORMANCE;

			// Server performance scoring
			const serverScore = this.calculateServerScore(metrics);
			totalScore += serverScore * this.WEIGHTS.SERVER_PERFORMANCE;

			// Infrastructure scoring
			const infrastructureScore = this.calculateInfrastructureScore(metrics);
			totalScore += infrastructureScore * this.WEIGHTS.INFRASTRUCTURE;

			// Hosting quality scoring
			const hostingScore = this.calculateHostingScore(metrics);
			totalScore += hostingScore * this.WEIGHTS.HOSTING_QUALITY;

			// Uptime and reliability scoring
			const uptimeScore = this.calculateUptimeScore(metrics);
			totalScore += uptimeScore * this.WEIGHTS.UPTIME_RELIABILITY;

			const finalScore = Math.min(1.0, Math.max(0.0, totalScore));

			logger.debug('Technical score calculated', {
				dnsScore,
				serverScore,
				infrastructureScore,
				hostingScore,
				uptimeScore,
				finalScore,
				metrics,
			});

			return Math.round(finalScore * 1000) / 1000;
		}
		catch (error)
		{
			logger.error('Error calculating technical score:', error);
			return 0.5; // Default fallback score
		}
	}

	/**
	 * Calculate DNS performance score
	 */
	private static calculateDNSScore(metrics: TechnicalMetricsType): number
	{
		let score = 0.3; // Base score

		// DNS response time scoring
		if (metrics.dnsResponseTime !== undefined)
		{
			if (metrics.dnsResponseTime <= this.THRESHOLDS.DNS_RESPONSE_TIME.EXCELLENT)
			{
				score += 0.3; // Excellent DNS performance
			}
			else if (metrics.dnsResponseTime <= this.THRESHOLDS.DNS_RESPONSE_TIME.GOOD)
			{
				score += 0.2; // Good DNS performance
			}
			else if (metrics.dnsResponseTime <= this.THRESHOLDS.DNS_RESPONSE_TIME.POOR)
			{
				score += 0.1; // Fair DNS performance
			}
		}

		// DNS configuration scoring
		if (metrics.dnsConfiguration)
		{
			const config = metrics.dnsConfiguration;

			// DNSSEC support
			if (config.dnssecEnabled)
			{
				score += 0.1;
			}

			// DNS redundancy
			if (config.redundancy)
			{
				score += 0.1;
			}

			// Geographic distribution
			if (config.geoDistribution)
			{
				score += 0.1;
			}

			// Multiple nameservers
			if (config.nameservers && config.nameservers.length >= 2)
			{
				score += 0.05;
			}

			// Proper TTL configuration
			if (config.ttlValues && Object.keys(config.ttlValues).length > 0)
			{
				score += 0.05;
			}
		}

		return Math.min(1.0, score);
	}

	/**
	 * Calculate server performance score
	 */
	private static calculateServerScore(metrics: TechnicalMetricsType): number
	{
		let score = 0.2; // Base score

		// Server response time scoring
		if (metrics.serverResponseTime !== undefined)
		{
			if (metrics.serverResponseTime <= this.THRESHOLDS.SERVER_RESPONSE_TIME.EXCELLENT)
			{
				score += 0.3; // Excellent server response
			}
			else if (metrics.serverResponseTime <= this.THRESHOLDS.SERVER_RESPONSE_TIME.GOOD)
			{
				score += 0.2; // Good server response
			}
			else if (metrics.serverResponseTime <= this.THRESHOLDS.SERVER_RESPONSE_TIME.POOR)
			{
				score += 0.1; // Fair server response
			}
		}

		// IPv6 support
		if (metrics.supportsIpv6)
		{
			score += 0.1;
		}

		// CDN usage
		if (metrics.hasCdn)
		{
			score += 0.2;
		}

		// Compression enabled
		if (metrics.compressionEnabled)
		{
			score += 0.1;
		}

		// Keep-alive enabled
		if (metrics.keepAliveEnabled)
		{
			score += 0.1;
		}

		return Math.min(1.0, score);
	}

	/**
	 * Calculate infrastructure assessment score
	 */
	private static calculateInfrastructureScore(metrics: TechnicalMetricsType): number
	{
		let score = 0.3; // Base score

		if (metrics.infrastructureAssessment)
		{
			const infra = metrics.infrastructureAssessment;

			// Load balancing capability
			if (infra.loadBalancing)
			{
				score += 0.15;
			}

			// Failover capability
			if (infra.failoverCapability)
			{
				score += 0.15;
			}

			// Scalability rating
			if (infra.scalabilityRating !== undefined)
			{
				score += (infra.scalabilityRating / 10) * 0.2; // Convert 0-10 to 0-0.2
			}

			// Security rating
			if (infra.securityRating !== undefined)
			{
				score += (infra.securityRating / 10) * 0.15; // Convert 0-10 to 0-0.15
			}

			// Performance rating
			if (infra.performanceRating !== undefined)
			{
				score += (infra.performanceRating / 10) * 0.15; // Convert 0-10 to 0-0.15
			}
		}

		return Math.min(1.0, score);
	}

	/**
	 * Calculate hosting quality score
	 */
	private static calculateHostingScore(metrics: TechnicalMetricsType): number
	{
		let score = 0.2; // Base score

		if (metrics.hostingQuality)
		{
			const hosting = metrics.hostingQuality;

			// Hosting tier scoring
			const tierScore = this.HOSTING_TIERS[hosting.tier] || 0.3;
			score += tierScore * 0.4; // 40% weight for tier

			// Network quality
			if (hosting.networkQuality !== undefined)
			{
				score += (hosting.networkQuality / 10) * 0.2; // Convert 0-10 to 0-0.2
			}

			// Reliability rating
			if (hosting.reliability !== undefined)
			{
				score += (hosting.reliability / 10) * 0.2; // Convert 0-10 to 0-0.2
			}

			// Data center presence
			if (hosting.dataCenter)
			{
				score += 0.1;
			}

			// Known quality providers
			const qualityProviders = [
				'AWS',
				'Google Cloud',
				'Microsoft Azure',
				'Cloudflare',
				'DigitalOcean',
				'Linode',
				'Vultr',
			];

			const isQualityProvider = qualityProviders.some(provider =>
				hosting.provider.toLowerCase().includes(provider.toLowerCase()));

			if (isQualityProvider)
			{
				score += 0.1;
			}
		}

		return Math.min(1.0, score);
	}

	/**
	 * Calculate uptime and reliability score
	 */
	private static calculateUptimeScore(metrics: TechnicalMetricsType): number
	{
		let score = 0.3; // Base score

		// Uptime percentage scoring
		if (metrics.uptimePercentage !== undefined)
		{
			if (metrics.uptimePercentage >= this.THRESHOLDS.UPTIME.EXCELLENT)
			{
				score += 0.7; // Excellent uptime
			}
			else if (metrics.uptimePercentage >= this.THRESHOLDS.UPTIME.GOOD)
			{
				score += 0.5; // Good uptime
			}
			else if (metrics.uptimePercentage >= this.THRESHOLDS.UPTIME.FAIR)
			{
				score += 0.3; // Fair uptime
			}
			else if (metrics.uptimePercentage >= 95.0)
			{
				score += 0.1; // Poor but acceptable uptime
			}
			// No additional points for very poor uptime
		}

		return Math.min(1.0, score);
	}

	/**
	 * Get technical grade based on score
	 */
	static getTechnicalGrade(score: number): string
	{
		if (score >= 0.9) return 'A+';
		if (score >= 0.8) return 'A';
		if (score >= 0.7) return 'B';
		if (score >= 0.6) return 'C';
		if (score >= 0.5) return 'D';
		return 'F';
	}

	/**
	 * Get detailed technical breakdown
	 */
	static getTechnicalBreakdown(metrics: TechnicalMetricsType): any
	{
		const dnsScore = this.calculateDNSScore(metrics);
		const serverScore = this.calculateServerScore(metrics);
		const infrastructureScore = this.calculateInfrastructureScore(metrics);
		const hostingScore = this.calculateHostingScore(metrics);
		const uptimeScore = this.calculateUptimeScore(metrics);

		const totalScore = (
			dnsScore * this.WEIGHTS.DNS_PERFORMANCE
			+ serverScore * this.WEIGHTS.SERVER_PERFORMANCE
			+ infrastructureScore * this.WEIGHTS.INFRASTRUCTURE
			+ hostingScore * this.WEIGHTS.HOSTING_QUALITY
			+ uptimeScore * this.WEIGHTS.UPTIME_RELIABILITY
		);

		return {
			totalScore: Math.round(totalScore * 1000) / 1000,
			grade: this.getTechnicalGrade(totalScore),
			breakdown: {
				dnsPerformance: {
					score: Math.round(dnsScore * 1000) / 1000,
					weight: this.WEIGHTS.DNS_PERFORMANCE,
					contribution: Math.round(dnsScore * this.WEIGHTS.DNS_PERFORMANCE * 1000) / 1000,
				},
				serverPerformance: {
					score: Math.round(serverScore * 1000) / 1000,
					weight: this.WEIGHTS.SERVER_PERFORMANCE,
					contribution: Math.round(serverScore * this.WEIGHTS.SERVER_PERFORMANCE * 1000) / 1000,
				},
				infrastructure: {
					score: Math.round(infrastructureScore * 1000) / 1000,
					weight: this.WEIGHTS.INFRASTRUCTURE,
					contribution: Math.round(infrastructureScore * this.WEIGHTS.INFRASTRUCTURE * 1000) / 1000,
				},
				hostingQuality: {
					score: Math.round(hostingScore * 1000) / 1000,
					weight: this.WEIGHTS.HOSTING_QUALITY,
					contribution: Math.round(hostingScore * this.WEIGHTS.HOSTING_QUALITY * 1000) / 1000,
				},
				uptimeReliability: {
					score: Math.round(uptimeScore * 1000) / 1000,
					weight: this.WEIGHTS.UPTIME_RELIABILITY,
					contribution: Math.round(uptimeScore * this.WEIGHTS.UPTIME_RELIABILITY * 1000) / 1000,
				},
			},
		};
	}

	/**
	 * Get technical recommendations based on metrics
	 */
	static getTechnicalRecommendations(metrics: TechnicalMetricsType): string[]
	{
		const recommendations: string[] = [];

		// DNS recommendations
		if (metrics.dnsResponseTime && metrics.dnsResponseTime > this.THRESHOLDS.DNS_RESPONSE_TIME.GOOD)
		{
			recommendations.push('Optimize DNS response time by using faster DNS providers');
		}

		if (!metrics.dnsConfiguration?.dnssecEnabled)
		{
			recommendations.push('Enable DNSSEC for improved DNS security');
		}

		if (!metrics.dnsConfiguration?.redundancy)
		{
			recommendations.push('Implement DNS redundancy with multiple nameservers');
		}

		// Server recommendations
		if (metrics.serverResponseTime && metrics.serverResponseTime > this.THRESHOLDS.SERVER_RESPONSE_TIME.GOOD)
		{
			recommendations.push('Improve server response time through optimization or better hosting');
		}

		if (!metrics.supportsIpv6)
		{
			recommendations.push('Enable IPv6 support for future-proofing');
		}

		if (!metrics.hasCdn)
		{
			recommendations.push('Implement a Content Delivery Network (CDN) for better performance');
		}

		if (!metrics.compressionEnabled)
		{
			recommendations.push('Enable compression (gzip/brotli) to reduce bandwidth usage');
		}

		if (!metrics.keepAliveEnabled)
		{
			recommendations.push('Enable HTTP keep-alive for better connection efficiency');
		}

		// Infrastructure recommendations
		if (!metrics.infrastructureAssessment?.loadBalancing)
		{
			recommendations.push('Consider implementing load balancing for better scalability');
		}

		if (!metrics.infrastructureAssessment?.failoverCapability)
		{
			recommendations.push('Implement failover capabilities for better reliability');
		}

		// Uptime recommendations
		if (metrics.uptimePercentage && metrics.uptimePercentage < this.THRESHOLDS.UPTIME.GOOD)
		{
			recommendations.push('Improve uptime reliability through better hosting and monitoring');
		}

		// Hosting recommendations
		if (metrics.hostingQuality?.tier === 'shared')
		{
			recommendations.push('Consider upgrading to business or enterprise hosting for better performance');
		}

		return recommendations;
	}
}

export type {
	TechnicalMetricsType,
	DNSConfigurationType,
	HostingQualityType,
	InfrastructureAssessmentType,
};

export { TechnicalScorer };

export default TechnicalScorer;
