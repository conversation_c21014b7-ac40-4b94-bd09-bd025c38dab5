import { logger as sharedLogger } from '@shared';

const logger = sharedLogger.getLogger('BacklinkScorer');

type BacklinkMetricsType =
{
	totalBacklinks: number;
	uniqueDomains: number;
	averageAuthority: number;
	followLinks: number;
	nofollowLinks: number;
	topReferringDomains: string[];
	domainAuthority?: DomainAuthorityType;
	linkQuality?: LinkQualityType;
	referralAnalysis?: ReferralAnalysisType;
};

type DomainAuthorityType =
{
	score: number; // 0-100 scale
	rank: number;
	category: string;
	trustFlow?: number;
	citationFlow?: number;
	mozRank?: number;
	pageAuthority?: number;
};

type LinkQualityType =
{
	averageQualityScore: number; // 0-1 scale
	highQualityLinks: number;
	mediumQualityLinks: number;
	lowQualityLinks: number;
	spamLinks: number;
	anchorTextDiversity: number; // 0-1 scale
	linkVelocity?: number; // Links gained per month
};

type ReferralAnalysisType =
{
	organicReferrals: number;
	socialReferrals: number;
	directReferrals: number;
	searchReferrals: number;
	topReferralSources: ReferralSourceType[];
	referralQuality: number; // 0-1 scale
};

type ReferralSourceType =
{
	domain: string;
	visits: number;
	authority: number;
	relevance: number;
};

/**
 * Backlink scoring algorithm based on domain authority calculations, link quality assessment, and referral analysis
 */
class BacklinkScorer
{
	private static readonly WEIGHTS = {
		LINK_QUANTITY: 0.25,
		LINK_QUALITY: 0.30,
		DOMAIN_AUTHORITY: 0.25,
		REFERRAL_ANALYSIS: 0.20,
	};

	private static readonly THRESHOLDS = {
		BACKLINKS: {
			EXCELLENT: 10000,
			GOOD: 1000,
			FAIR: 100,
			MINIMUM: 10,
		},
		UNIQUE_DOMAINS: {
			EXCELLENT: 1000,
			GOOD: 100,
			FAIR: 50,
			MINIMUM: 10,
		},
		AUTHORITY: {
			EXCELLENT: 80,
			GOOD: 60,
			FAIR: 40,
			MINIMUM: 20,
		},
	};

	/**
	 * Calculate backlink score based on link metrics and domain authority
	 */
	static calculateScore(metrics: BacklinkMetricsType): number
	{
		try
		{
			let totalScore = 0;

			// Link quantity scoring
			const quantityScore = this.calculateQuantityScore(metrics);
			totalScore += quantityScore * this.WEIGHTS.LINK_QUANTITY;

			// Link quality scoring
			const qualityScore = this.calculateQualityScore(metrics);
			totalScore += qualityScore * this.WEIGHTS.LINK_QUALITY;

			// Domain authority scoring
			const authorityScore = this.calculateAuthorityScore(metrics);
			totalScore += authorityScore * this.WEIGHTS.DOMAIN_AUTHORITY;

			// Referral analysis scoring
			const referralScore = this.calculateReferralScore(metrics);
			totalScore += referralScore * this.WEIGHTS.REFERRAL_ANALYSIS;

			const finalScore = Math.min(1.0, Math.max(0.0, totalScore));

			logger.debug('Backlink score calculated', {
				quantityScore,
				qualityScore,
				authorityScore,
				referralScore,
				finalScore,
				metrics,
			});

			return Math.round(finalScore * 1000) / 1000;
		}
		catch (error)
		{
			logger.error('Error calculating backlink score:', error);
			return 0.3; // Conservative fallback score
		}
	}

	/**
	 * Calculate link quantity score
	 */
	private static calculateQuantityScore(metrics: BacklinkMetricsType): number
	{
		let score = 0.1; // Base score

		// Total backlinks scoring
		const totalBacklinks = metrics.totalBacklinks || 0;
		if (totalBacklinks >= this.THRESHOLDS.BACKLINKS.EXCELLENT)
		{
			score += 0.4;
		}
		else if (totalBacklinks >= this.THRESHOLDS.BACKLINKS.GOOD)
		{
			score += 0.35;
		}
		else if (totalBacklinks >= this.THRESHOLDS.BACKLINKS.FAIR)
		{
			score += 0.25;
		}
		else if (totalBacklinks >= this.THRESHOLDS.BACKLINKS.MINIMUM)
		{
			score += 0.15;
		}
		else if (totalBacklinks > 0)
		{
			score += 0.1;
		}

		// Unique domains scoring
		const uniqueDomains = metrics.uniqueDomains || 0;
		if (uniqueDomains >= this.THRESHOLDS.UNIQUE_DOMAINS.EXCELLENT)
		{
			score += 0.3;
		}
		else if (uniqueDomains >= this.THRESHOLDS.UNIQUE_DOMAINS.GOOD)
		{
			score += 0.25;
		}
		else if (uniqueDomains >= this.THRESHOLDS.UNIQUE_DOMAINS.FAIR)
		{
			score += 0.2;
		}
		else if (uniqueDomains >= this.THRESHOLDS.UNIQUE_DOMAINS.MINIMUM)
		{
			score += 0.15;
		}
		else if (uniqueDomains > 0)
		{
			score += 0.1;
		}

		// Follow vs nofollow ratio
		const totalLinks = metrics.followLinks + metrics.nofollowLinks;
		if (totalLinks > 0)
		{
			const followRatio = metrics.followLinks / totalLinks;
			if (followRatio >= 0.8)
			{
				score += 0.2; // High follow ratio is good
			}
			else if (followRatio >= 0.6)
			{
				score += 0.15;
			}
			else if (followRatio >= 0.4)
			{
				score += 0.1;
			}
			else if (followRatio >= 0.2)
			{
				score += 0.05;
			}
		}

		return Math.min(1.0, score);
	}

	/**
	 * Calculate link quality score
	 */
	private static calculateQualityScore(metrics: BacklinkMetricsType): number
	{
		let score = 0.2; // Base score

		if (metrics.linkQuality)
		{
			const quality = metrics.linkQuality;

			// Average quality score
			score += quality.averageQualityScore * 0.3;

			// High quality links ratio
			const totalLinks = quality.highQualityLinks + quality.mediumQualityLinks + quality.lowQualityLinks + quality.spamLinks;
			if (totalLinks > 0)
			{
				const highQualityRatio = quality.highQualityLinks / totalLinks;
				score += highQualityRatio * 0.25;

				// Penalty for spam links
				const spamRatio = quality.spamLinks / totalLinks;
				score -= spamRatio * 0.3;
			}

			// Anchor text diversity
			score += quality.anchorTextDiversity * 0.15;

			// Link velocity (moderate growth is good)
			if (quality.linkVelocity !== undefined)
			{
				if (quality.linkVelocity >= 10 && quality.linkVelocity <= 100)
				{
					score += 0.1; // Healthy link growth
				}
				else if (quality.linkVelocity > 100)
				{
					score += 0.05; // Rapid growth might be suspicious
				}
				else if (quality.linkVelocity > 0)
				{
					score += 0.05; // Slow but positive growth
				}
			}
		}

		return Math.min(1.0, Math.max(0.0, score));
	}

	/**
	 * Calculate domain authority score
	 */
	private static calculateAuthorityScore(metrics: BacklinkMetricsType): number
	{
		let score = 0.2; // Base score

		// Average authority of referring domains
		const avgAuthority = metrics.averageAuthority || 0;
		if (avgAuthority >= this.THRESHOLDS.AUTHORITY.EXCELLENT)
		{
			score += 0.3;
		}
		else if (avgAuthority >= this.THRESHOLDS.AUTHORITY.GOOD)
		{
			score += 0.25;
		}
		else if (avgAuthority >= this.THRESHOLDS.AUTHORITY.FAIR)
		{
			score += 0.2;
		}
		else if (avgAuthority >= this.THRESHOLDS.AUTHORITY.MINIMUM)
		{
			score += 0.15;
		}
		else if (avgAuthority > 0)
		{
			score += 0.1;
		}

		// Domain authority metrics
		if (metrics.domainAuthority)
		{
			const da = metrics.domainAuthority;

			// Domain authority score (0-100 scale)
			score += (da.score / 100) * 0.2;

			// Trust flow and citation flow
			if (da.trustFlow !== undefined)
			{
				score += (da.trustFlow / 100) * 0.1;
			}

			if (da.citationFlow !== undefined)
			{
				score += (da.citationFlow / 100) * 0.1;
			}

			// MOZ metrics
			if (da.mozRank !== undefined)
			{
				score += (da.mozRank / 10) * 0.05; // MOZ rank is typically 0-10
			}

			if (da.pageAuthority !== undefined)
			{
				score += (da.pageAuthority / 100) * 0.05;
			}
		}

		// Top referring domains quality
		if (metrics.topReferringDomains && metrics.topReferringDomains.length > 0)
		{
			const hasHighAuthorityReferrers = metrics.topReferringDomains.length >= 5;
			if (hasHighAuthorityReferrers)
			{
				score += 0.1;
			}
		}

		return Math.min(1.0, score);
	}

	/**
	 * Calculate referral analysis score
	 */
	private static calculateReferralScore(metrics: BacklinkMetricsType): number
	{
		let score = 0.3; // Base score

		if (metrics.referralAnalysis)
		{
			const referral = metrics.referralAnalysis;

			// Overall referral quality
			score += referral.referralQuality * 0.3;

			// Referral diversity (multiple sources is good)
			const totalReferrals = referral.organicReferrals + referral.socialReferrals + referral.directReferrals + referral.searchReferrals;

			if (totalReferrals > 0)
			{
				let diversityScore = 0;
				const sources = [
					referral.organicReferrals,
					referral.socialReferrals,
					referral.directReferrals,
					referral.searchReferrals,
				];

				// Count non-zero sources
				const activeSources = sources.filter(source => source > 0).length;
				diversityScore = activeSources / 4; // 0-1 scale

				score += diversityScore * 0.2;

				// Bonus for organic search referrals
				const organicRatio = referral.searchReferrals / totalReferrals;
				if (organicRatio >= 0.4)
				{
					score += 0.15; // Good organic search presence
				}
				else if (organicRatio >= 0.2)
				{
					score += 0.1;
				}
				else if (organicRatio > 0)
				{
					score += 0.05;
				}
			}

			// Top referral sources quality
			if (referral.topReferralSources && referral.topReferralSources.length > 0)
			{
				const avgSourceAuthority = referral.topReferralSources.reduce((sum, source) => sum + source.authority, 0) / referral.topReferralSources.length;
				score += (avgSourceAuthority / 100) * 0.15;

				const avgRelevance = referral.topReferralSources.reduce((sum, source) => sum + source.relevance, 0) / referral.topReferralSources.length;
				score += avgRelevance * 0.1;
			}
		}

		return Math.min(1.0, score);
	}

	/**
	 * Get backlink grade based on score
	 */
	static getBacklinkGrade(score: number): string
	{
		if (score >= 0.9) return 'A+';
		if (score >= 0.8) return 'A';
		if (score >= 0.7) return 'B';
		if (score >= 0.6) return 'C';
		if (score >= 0.5) return 'D';
		return 'F';
	}

	/**
	 * Get detailed backlink breakdown
	 */
	static getBacklinkBreakdown(metrics: BacklinkMetricsType): any
	{
		const quantityScore = this.calculateQuantityScore(metrics);
		const qualityScore = this.calculateQualityScore(metrics);
		const authorityScore = this.calculateAuthorityScore(metrics);
		const referralScore = this.calculateReferralScore(metrics);

		const totalScore = (
			quantityScore * this.WEIGHTS.LINK_QUANTITY
			+ qualityScore * this.WEIGHTS.LINK_QUALITY
			+ authorityScore * this.WEIGHTS.DOMAIN_AUTHORITY
			+ referralScore * this.WEIGHTS.REFERRAL_ANALYSIS
		);

		return {
			totalScore: Math.round(totalScore * 1000) / 1000,
			grade: this.getBacklinkGrade(totalScore),
			breakdown: {
				linkQuantity: {
					score: Math.round(quantityScore * 1000) / 1000,
					weight: this.WEIGHTS.LINK_QUANTITY,
					contribution: Math.round(quantityScore * this.WEIGHTS.LINK_QUANTITY * 1000) / 1000,
					details: {
						totalBacklinks: metrics.totalBacklinks,
						uniqueDomains: metrics.uniqueDomains,
						followRatio: metrics.followLinks / (metrics.followLinks + metrics.nofollowLinks),
					},
				},
				linkQuality: {
					score: Math.round(qualityScore * 1000) / 1000,
					weight: this.WEIGHTS.LINK_QUALITY,
					contribution: Math.round(qualityScore * this.WEIGHTS.LINK_QUALITY * 1000) / 1000,
					details: metrics.linkQuality || {},
				},
				domainAuthority: {
					score: Math.round(authorityScore * 1000) / 1000,
					weight: this.WEIGHTS.DOMAIN_AUTHORITY,
					contribution: Math.round(authorityScore * this.WEIGHTS.DOMAIN_AUTHORITY * 1000) / 1000,
					details: {
						averageAuthority: metrics.averageAuthority,
						domainAuthority: metrics.domainAuthority,
					},
				},
				referralAnalysis: {
					score: Math.round(referralScore * 1000) / 1000,
					weight: this.WEIGHTS.REFERRAL_ANALYSIS,
					contribution: Math.round(referralScore * this.WEIGHTS.REFERRAL_ANALYSIS * 1000) / 1000,
					details: metrics.referralAnalysis || {},
				},
			},
		};
	}

	/**
	 * Get backlink recommendations based on metrics
	 */
	static getBacklinkRecommendations(metrics: BacklinkMetricsType): string[]
	{
		const recommendations: string[] = [];

		// Quantity recommendations
		if (metrics.totalBacklinks < this.THRESHOLDS.BACKLINKS.MINIMUM)
		{
			recommendations.push('Focus on building more backlinks through content marketing and outreach');
		}

		if (metrics.uniqueDomains < this.THRESHOLDS.UNIQUE_DOMAINS.MINIMUM)
		{
			recommendations.push('Diversify backlink sources by getting links from more unique domains');
		}

		// Quality recommendations
		const totalLinks = metrics.followLinks + metrics.nofollowLinks;
		if (totalLinks > 0)
		{
			const followRatio = metrics.followLinks / totalLinks;
			if (followRatio < 0.4)
			{
				recommendations.push('Focus on acquiring more dofollow links for better SEO value');
			}
		}

		if (metrics.linkQuality && metrics.linkQuality.spamLinks > 0)
		{
			recommendations.push('Disavow spam links to improve overall link profile quality');
		}

		if (metrics.linkQuality && metrics.linkQuality.anchorTextDiversity < 0.5)
		{
			recommendations.push('Diversify anchor text to avoid over-optimization penalties');
		}

		// Authority recommendations
		if (metrics.averageAuthority < this.THRESHOLDS.AUTHORITY.FAIR)
		{
			recommendations.push('Target higher authority websites for backlink opportunities');
		}

		if (!metrics.domainAuthority || metrics.domainAuthority.score < 30)
		{
			recommendations.push('Work on improving overall domain authority through quality content and links');
		}

		// Referral recommendations
		if (!metrics.referralAnalysis || metrics.referralAnalysis.referralQuality < 0.5)
		{
			recommendations.push('Improve referral traffic quality through better content and user experience');
		}

		if (metrics.referralAnalysis && metrics.referralAnalysis.searchReferrals === 0)
		{
			recommendations.push('Focus on SEO to increase organic search referrals');
		}

		return recommendations;
	}
}

export type {
	BacklinkMetricsType,
	DomainAuthorityType,
	LinkQualityType,
	ReferralAnalysisType,
	ReferralSourceType,
};

export { BacklinkScorer };

export default BacklinkScorer;
