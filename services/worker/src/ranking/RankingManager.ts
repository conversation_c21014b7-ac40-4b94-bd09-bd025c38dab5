import { logger as sharedLogger, IdGenerator } from '@shared';
import { CompositeRanker } from './CompositeRanker';
import { RankingUpdateService } from './RankingUpdateService';
import { RankingValidator } from './RankingValidator';
import { WorkerDatabaseManager } from '../database/WorkerDatabaseManager';
import { WorkerJobQueue } from '../queue/WorkerJobQueue';
import type {
	DomainDataType,
	RankingResultType,
	CompositeScoreType,
	RankingWeightsType,
	RankingStatisticsType,
} from './CompositeRanker';
import type {
	RankingUpdateTriggerType,
	BatchRankingUpdateType,
	RankingTrendAnalysisType,
} from './RankingUpdateService';
import type {
	ValidationResultType,
	QualityAssessmentType,
} from './RankingValidator';

const logger = sharedLogger.getLogger('RankingManager');

type RankingManagerConfigType =
{
	weights?: RankingWeightsType;
	validationEnabled?: boolean;
	anomalyDetectionEnabled?: boolean;
	qualityThresholds?: {
		minimumScore?: number;
		maximumVolatility?: number;
		minimumDataPoints?: number;
	};
	batchProcessing?: {
		maxBatchSize?: number;
		maxConcurrentBatches?: number;
		retryAttempts?: number;
	};
};

type RankingAnomalyType =
{
	domain: string;
	anomalyType: 'sudden_drop' | 'sudden_rise' | 'high_volatility' | 'data_inconsistency';
	severity: 'low' | 'medium' | 'high' | 'critical';
	description: string;
	currentRank: number;
	previousRank?: number;
	volatility?: number;
	detectedAt: Date;
	metadata?: Record<string, unknown>;
};

type RankingQualityReportType =
{
	totalDomains: number;
	validRankings: number;
	invalidRankings: number;
	anomaliesDetected: number;
	qualityScore: number;
	recommendations: string[];
	anomalies: RankingAnomalyType[];
	statistics: RankingStatisticsType;
	generatedAt: Date;
};

/**
 * Comprehensive ranking management system that orchestrates all ranking functionality
 */
class RankingManager
{
	private compositeRanker: CompositeRanker;

	private updateService: RankingUpdateService;

	private validator: RankingValidator;

	private dbManager: WorkerDatabaseManager;

	private jobQueue: WorkerJobQueue;

	private config: RankingManagerConfigType;

	private anomalies = new Map<string, RankingAnomalyType[]>();

	private qualityMetrics = new Map<string, QualityAssessmentType>();

	constructor(
		dbManager: WorkerDatabaseManager,
		jobQueue: WorkerJobQueue,
		config: RankingManagerConfigType = {},
	)
	{
		this.dbManager = dbManager;
		this.jobQueue = jobQueue;
		this.config = {
			validationEnabled: true,
			anomalyDetectionEnabled: true,
			qualityThresholds: {
				minimumScore: 0.1,
				maximumVolatility: 50,
				minimumDataPoints: 3,
			},
			batchProcessing: {
				maxBatchSize: 1000,
				maxConcurrentBatches: 5,
				retryAttempts: 3,
			},
			...config,
		};

		// Initialize components
		this.compositeRanker = new CompositeRanker(config.weights);
		this.validator = new RankingValidator();
		this.updateService = new RankingUpdateService(dbManager, jobQueue, this.compositeRanker);
	}

	/**
	 * Initialize the ranking manager
	 */
	async initialize(): Promise<void>
	{
		try
		{
			logger.info('Initializing Ranking Manager...');

			// Initialize update service
			await this.updateService.initialize();

			// Setup anomaly detection monitoring
			if (this.config.anomalyDetectionEnabled)
			{
				await this.setupAnomalyDetection();
			}

			// Setup quality monitoring
			if (this.config.validationEnabled)
			{
				await this.setupQualityMonitoring();
			}

			logger.info('Ranking Manager initialized successfully');
		}
		catch (error)
		{
			logger.error('Failed to initialize Ranking Manager:', error);
			throw error;
		}
	}

	/**
	 * Calculate comprehensive ranking with validation and anomaly detection
	 */
	async calculateRankingWithQualityControl(
		domainData: DomainDataType,
		options: {
			validateInput?: boolean;
			detectAnomalies?: boolean;
			storeResults?: boolean;
		} = {},
	): Promise<{
		compositeScore: CompositeScoreType;
		validation: ValidationResult;
		anomalies: RankingAnomalyType[];
		qualityAssessment: QualityAssessment;
	}>
	{
		const {
			validateInput = true,
			detectAnomalies = true,
			storeResults = true,
		} = options;

		try
		{
			logger.debug(`Calculating ranking with quality control for: ${domainData.domain}`);

			// Input validation
			let validation: ValidationResult = { isValid: true, errors: [], warnings: [] };
			if (validateInput && this.config.validationEnabled)
			{
				validation = await this.validator.validateDomainData(domainData);
				if (!validation.isValid)
				{
					logger.warn(`Domain data validation failed for: ${domainData.domain}`, validation.errors);
				}
			}

			// Calculate composite score
			const compositeScore = this.compositeRanker.calculateCompositeScore(domainData);

			// Quality assessment
			const qualityAssessment = await this.validator.assessRankingQuality(
				domainData,
				compositeScore,
			);

			// Store quality metrics
			this.qualityMetrics.set(domainData.domain, qualityAssessment);

			// Anomaly detection
			let anomalies: RankingAnomalyType[] = [];
			if (detectAnomalies && this.config.anomalyDetectionEnabled)
			{
				anomalies = await this.detectRankingAnomalies(domainData.domain, compositeScore);
				if (anomalies.length > 0)
				{
					this.anomalies.set(domainData.domain, anomalies);
					logger.warn(`Ranking anomalies detected for: ${domainData.domain}`, {
						anomalyCount: anomalies.length,
						severities: anomalies.map(a => a.severity),
					});
				}
			}

			// Store results if requested
			if (storeResults)
			{
				await this.storeRankingResults(compositeScore, validation, qualityAssessment, anomalies);
			}

			logger.debug(`Ranking calculation completed for: ${domainData.domain}`, {
				overallScore: compositeScore.overallScore,
				grade: compositeScore.grade,
				qualityScore: qualityAssessment.dataQuality,
				anomalyCount: anomalies.length,
			});

			return {
				compositeScore,
				validation,
				anomalies,
				qualityAssessment,
			};
		}
		catch (error)
		{
			logger.error(`Failed to calculate ranking for: ${domainData.domain}:`, error);
			throw error;
		}
	}

	/**
	 * Process batch rankings with comprehensive quality control
	 */
	async processBatchRankingsWithQualityControl(
		domains: DomainDataType[],
		options: {
			rankingType?: 'global' | 'category';
			category?: string;
			validateAll?: boolean;
			generateReport?: boolean;
		} = {},
	): Promise<{
		rankings: RankingResultType[];
		qualityReport: RankingQualityReportType;
		batchId: string;
	}>
	{
		const {
			rankingType = 'global',
			category,
			validateAll = true,
			generateReport = true,
		} = options;

		try
		{
			logger.info(`Processing batch rankings for ${domains.length} domains`);

			const batchId = this.generateBatchId();
			const results: {
				domain: string;
				compositeScore: CompositeScoreType;
				validation: ValidationResult;
				anomalies: RankingAnomalyType[];
				qualityAssessment: QualityAssessment;
			}[] = [];

			// Process each domain with quality control
			for (const domainData of domains)
			{
				try
				{
					const result = await this.calculateRankingWithQualityControl(domainData, {
						validateInput: validateAll,
						detectAnomalies: true,
						storeResults: false, // Store batch results together
					});

					results.push({
						domain: domainData.domain,
						...result,
					});
				}
				catch (error)
				{
					logger.error(`Failed to process domain in batch: ${domainData.domain}:`, error);
					// Continue with other domains
				}
			}

			// Calculate rankings
			const validDomains = results
				.filter(r => r.validation.isValid)
				.map(r => domains.find(d => d.domain === r.domain)!)
				.filter(Boolean);

			let rankings: RankingResultType[];
			if (category && rankingType === 'category')
			{
				rankings = this.compositeRanker.calculateCategoryRankings(validDomains, category);
			}
			else
			{
				rankings = this.compositeRanker.calculateGlobalRankings(validDomains);
			}

			// Generate quality report
			let qualityReport: RankingQualityReportType;
			if (generateReport)
			{
				qualityReport = this.generateQualityReport(results, rankings);
			}
			else
			{
				qualityReport = {
					totalDomains: domains.length,
					validRankings: results.filter(r => r.validation.isValid).length,
					invalidRankings: results.filter(r => !r.validation.isValid).length,
					anomaliesDetected: results.reduce((sum, r) => sum + r.anomalies.length, 0),
					qualityScore: 0,
					recommendations: [],
					anomalies: [],
					statistics: this.compositeRanker.getRankingStatistics(rankings),
					generatedAt: new Date(),
				};
			}

			// Store batch results
			await this.storeBatchResults(batchId, results, rankings, qualityReport);

			logger.info('Batch ranking processing completed', {
				batchId,
				totalDomains: domains.length,
				validRankings: qualityReport.validRankings,
				qualityScore: qualityReport.qualityScore,
			});

			return {
				rankings,
				qualityReport,
				batchId,
			};
		}
		catch (error)
		{
			logger.error('Failed to process batch rankings:', error);
			throw error;
		}
	}

	/**
	 * Detect ranking anomalies for a domain
	 */
	private async detectRankingAnomalies(
		domain: string,
		currentScore: CompositeScoreType,
	): Promise<RankingAnomalyType[]>
	{
		const anomalies: RankingAnomalyType[] = [];

		try
		{
			// Get historical trend data
			const trendAnalysis = await this.updateService.calculateRankingTrend(domain, 'global', '30d');

			// Check for sudden rank changes
			if (trendAnalysis.rankChanges.length >= 2)
			{
				const latestRank = currentScore.globalRank || 0;
				const previousRank = trendAnalysis.rankChanges[trendAnalysis.rankChanges.length - 2]?.rank;

				if (previousRank && latestRank > 0)
				{
					const rankChange = Math.abs(latestRank - previousRank);
					const changePercentage = (rankChange / previousRank) * 100;

					// Sudden drop (rank number increased significantly)
					if (latestRank > previousRank && changePercentage > 50)
					{
						anomalies.push({
							domain,
							anomalyType: 'sudden_drop',
							severity: changePercentage > 100 ? 'critical' : 'high',
							description: `Sudden ranking drop: ${previousRank} → ${latestRank} (${changePercentage.toFixed(1)}% change)`,
							currentRank: latestRank,
							previousRank,
							detectedAt: new Date(),
							metadata: { changePercentage, rankChange },
						});
					}

					// Sudden rise (rank number decreased significantly)
					if (latestRank < previousRank && changePercentage > 50)
					{
						anomalies.push({
							domain,
							anomalyType: 'sudden_rise',
							severity: changePercentage > 100 ? 'medium' : 'low',
							description: `Sudden ranking rise: ${previousRank} → ${latestRank} (${changePercentage.toFixed(1)}% improvement)`,
							currentRank: latestRank,
							previousRank,
							detectedAt: new Date(),
							metadata: { changePercentage, rankChange },
						});
					}
				}
			}

			// Check for high volatility
			const maxVolatility = this.config.qualityThresholds?.maximumVolatility || 50;
			if (trendAnalysis.volatility > maxVolatility)
			{
				anomalies.push({
					domain,
					anomalyType: 'high_volatility',
					severity: trendAnalysis.volatility > maxVolatility * 2 ? 'high' : 'medium',
					description: `High ranking volatility detected: ${trendAnalysis.volatility.toFixed(2)} (threshold: ${maxVolatility})`,
					currentRank: currentScore.globalRank || 0,
					volatility: trendAnalysis.volatility,
					detectedAt: new Date(),
					metadata: { trendAnalysis },
				});
			}

			// Check for data inconsistencies
			if (currentScore.overallScore < (this.config.qualityThresholds?.minimumScore || 0.1))
			{
				anomalies.push({
					domain,
					anomalyType: 'data_inconsistency',
					severity: 'medium',
					description: `Unusually low composite score: ${currentScore.overallScore.toFixed(3)}`,
					currentRank: currentScore.globalRank || 0,
					detectedAt: new Date(),
					metadata: { compositeScore: currentScore },
				});
			}

			return anomalies;
		}
		catch (error)
		{
			logger.error(`Failed to detect anomalies for domain: ${domain}:`, error);
			return [];
		}
	}

	/**
	 * Generate comprehensive quality report
	 */
	private generateQualityReport(
		results: Array<{
			domain: string;
			compositeScore: CompositeScoreType;
			validation: ValidationResult;
			anomalies: RankingAnomalyType[];
			qualityAssessment: QualityAssessment;
		}>,
		rankings: RankingResultType[],
	): RankingQualityReportType
	{
		const validRankings = results.filter(r => r.validation.isValid);
		const invalidRankings = results.filter(r => !r.validation.isValid);
		const allAnomalies = results.flatMap(r => r.anomalies);

		// Calculate overall quality score
		const qualityScores = validRankings.map(r => r.qualityAssessment.dataQuality);
		const averageQuality = qualityScores.length > 0
			? qualityScores.reduce((sum, score) => sum + score, 0) / qualityScores.length
			: 0;

		// Generate recommendations
		const recommendations: string[] = [];

		if (invalidRankings.length > results.length * 0.1)
		{
			recommendations.push(`High validation failure rate (${((invalidRankings.length / results.length) * 100).toFixed(1)}%). Review data quality processes.`);
		}

		if (allAnomalies.filter(a => a.severity === 'critical').length > 0)
		{
			recommendations.push('Critical ranking anomalies detected. Immediate investigation required.');
		}

		if (averageQuality < 0.7)
		{
			recommendations.push('Overall ranking quality is below acceptable threshold. Consider improving data collection and validation.');
		}

		const highVolatilityDomains = allAnomalies.filter(a => a.anomalyType === 'high_volatility').length;
		if (highVolatilityDomains > results.length * 0.05)
		{
			recommendations.push(`High number of volatile rankings (${highVolatilityDomains}). Review ranking algorithm stability.`);
		}

		return {
			totalDomains: results.length,
			validRankings: validRankings.length,
			invalidRankings: invalidRankings.length,
			anomaliesDetected: allAnomalies.length,
			qualityScore: Math.round(averageQuality * 1000) / 1000,
			recommendations,
			anomalies: allAnomalies,
			statistics: this.compositeRanker.getRankingStatistics(rankings),
			generatedAt: new Date(),
		};
	}

	/**
	 * Setup anomaly detection monitoring
	 */
	private async setupAnomalyDetection(): Promise<void>
	{
		// Setup periodic anomaly detection jobs
		await this.jobQueue.createConsumer(
			'ranking:anomaly-detection',
			this.handleAnomalyDetection.bind(this),
			{ concurrency: 2 },
		);

		logger.info('Anomaly detection monitoring setup completed');
	}

	/**
	 * Setup quality monitoring
	 */
	private async setupQualityMonitoring(): Promise<void>
	{
		// Setup periodic quality assessment jobs
		await this.jobQueue.createConsumer(
			'ranking:quality-assessment',
			this.handleQualityAssessment.bind(this),
			{ concurrency: 1 },
		);

		logger.info('Quality monitoring setup completed');
	}

	/**
	 * Handle anomaly detection job
	 */
	private async handleAnomalyDetection(jobData: unknown): Promise<void>
	{
		const { domains } = jobData as { domains: string[] };

		try
		{
			logger.info(`Running anomaly detection for ${domains.length} domains`);

			for (const domain of domains)
			{
				try
				{
					// Get current ranking data
					const domainData = await this.fetchDomainData(domain);
					if (!domainData) continue;

					const compositeScore = this.compositeRanker.calculateCompositeScore(domainData);
					const anomalies = await this.detectRankingAnomalies(domain, compositeScore);

					if (anomalies.length > 0)
					{
						await this.handleDetectedAnomalies(domain, anomalies);
					}
				}
				catch (error)
				{
					logger.error(`Anomaly detection failed for domain: ${domain}:`, error);
				}
			}

			logger.info('Anomaly detection completed');
		}
		catch (error)
		{
			logger.error('Anomaly detection job failed:', error);
			throw error;
		}
	}

	/**
	 * Handle quality assessment job
	 */
	private async handleQualityAssessment(jobData: unknown): Promise<void>
	{
		const { batchId } = jobData as { batchId: string };

		try
		{
			logger.info(`Running quality assessment for batch: ${batchId}`);

			// Generate and store quality report
			// Implementation would fetch batch data and generate comprehensive report

			logger.info(`Quality assessment completed for batch: ${batchId}`);
		}
		catch (error)
		{
			logger.error(`Quality assessment failed for batch: ${batchId}:`, error);
			throw error;
		}
	}

	/**
	 * Handle detected anomalies
	 */
	private async handleDetectedAnomalies(domain: string, anomalies: RankingAnomalyType[]): Promise<void>
	{
		try
		{
			// Store anomalies in database
			await this.storeAnomalies(domain, anomalies);

			// Trigger alerts for critical anomalies
			const criticalAnomalies = anomalies.filter(a => a.severity === 'critical');
			if (criticalAnomalies.length > 0)
			{
				await this.triggerAnomalyAlerts(domain, criticalAnomalies);
			}

			logger.debug(`Handled ${anomalies.length} anomalies for domain: ${domain}`);
		}
		catch (error)
		{
			logger.error(`Failed to handle anomalies for domain: ${domain}:`, error);
		}
	}

	/**
	 * Store ranking results with quality data
	 */
	private async storeRankingResults(
		compositeScore: CompositeScoreType,
		validation: ValidationResult,
		qualityAssessment: QualityAssessment,
		anomalies: RankingAnomalyType[],
	): Promise<void>
	{
		// Implementation would store comprehensive ranking data
		logger.debug(`Stored ranking results for domain: ${compositeScore.domain}`);
	}

	/**
	 * Store batch results
	 */
	private async storeBatchResults(
		batchId: string,
		results: unknown[],
		rankings: RankingResultType[],
		qualityReport: RankingQualityReportType,
	): Promise<void>
	{
		// Implementation would store batch processing results
		logger.debug(`Stored batch results for batch: ${batchId}`);
	}

	/**
	 * Store anomalies in database
	 */
	private async storeAnomalies(domain: string, anomalies: RankingAnomalyType[]): Promise<void>
	{
		// Implementation would store anomalies in database
		logger.debug(`Stored ${anomalies.length} anomalies for domain: ${domain}`);
	}

	/**
	 * Trigger anomaly alerts
	 */
	private async triggerAnomalyAlerts(domain: string, anomalies: RankingAnomalyType[]): Promise<void>
	{
		// Implementation would trigger alerting system
		logger.warn(`Triggered alerts for ${anomalies.length} critical anomalies in domain: ${domain}`);
	}

	/**
	 * Fetch domain data (placeholder - would use actual data fetching)
	 */
	private async fetchDomainData(domain: string): Promise<DomainDataType | null>
	{
		// This would be implemented to fetch actual domain data
		return null;
	}

	/**
	 * Generate unique batch ID
	 */
	private generateBatchId(): string
	{
		return IdGenerator.batchId();
	}

	/**
	 * Get ranking update service
	 */
	getRankingUpdateService(): RankingUpdateService
	{
		return this.updateService;
	}

	/**
	 * Get composite ranker
	 */
	getCompositeRanker(): CompositeRanker
	{
		return this.compositeRanker;
	}

	/**
	 * Get ranking validator
	 */
	getRankingValidator(): RankingValidator
	{
		return this.validator;
	}

	/**
	 * Get anomalies for a domain
	 */
	getAnomalies(domain: string): RankingAnomalyType[]
	{
		return this.anomalies.get(domain) || [];
	}

	/**
	 * Get quality metrics for a domain
	 */
	getQualityMetrics(domain: string): QualityAssessment | undefined
	{
		return this.qualityMetrics.get(domain);
	}

	/**
	 * Health check for ranking manager
	 */
	async healthCheck(): Promise<boolean>
	{
		try
		{
			const updateServiceHealth = await this.updateService.healthCheck();
			return updateServiceHealth;
		}
		catch (error)
		{
			logger.error('Ranking manager health check failed:', error);
			return false;
		}
	}
}

export type {
	RankingManagerConfigType,
	RankingAnomalyType,
	RankingQualityReportType,
};

export default RankingManager;
