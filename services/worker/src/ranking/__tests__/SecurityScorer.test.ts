/**
 * Security Scorer Tests
 *
 * Tests for security scoring functionality extracted from ranking-engine service.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { SecurityScorer } from '../scorers/SecurityScorer';
import type { SecurityMetricsType } from '../scorers/SecurityScorer';

// Mock the shared module
vi.mock('@shared', () => ({
	logger: {
		getLogger: vi.fn(() => ({
			debug: vi.fn(),
			info: vi.fn(),
			warn: vi.fn(),
			error: vi.fn(),
		})),
	},
}));

describe('SecurityScorer', () =>
{
	// let scorer: SecurityScorer;
	let mockSecurityMetrics: SecurityMetricsType;

	beforeEach(() =>
	{
		// scorer = new SecurityScorer();

		mockSecurityMetrics = {
			sslGrade: 'A',
			sslIssuer: 'Let\'s Encrypt',
			sslExpiration: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days from now
			usesHttps: true,
			securityHeaders: {
				hsts: true,
				csp: true,
				xframe: true,
				xssProtection: true,
				contentTypeOptions: true,
				referrerPolicy: true,
				permissionsPolicy: false,
			},
			vulnerabilities: [],
			certificateChain: [{
				issuer: 'Let\'s Encrypt',
				subject: 'example.com',
				validFrom: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
				validTo: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000),
				signatureAlgorithm: 'SHA256withRSA',
				keySize: 2048,
			}],
		};
	});

	describe('SSL Certificate Scoring', () =>
	{
		it('should give high score for A+ SSL grade', () =>
		{
			mockSecurityMetrics.sslGrade = 'A+';

			const score = SecurityScorer.calculateScore(mockSecurityMetrics);
			const breakdown = SecurityScorer.getSecurityBreakdown(mockSecurityMetrics);

			expect(score).toBeGreaterThan(0.8);
			expect(breakdown.breakdown.sslCertificate.score).toBeGreaterThan(0.9);
			expect(breakdown.breakdown.sslCertificate.details.grade).toBe('A+');
		});

		it('should give good score for A SSL grade', () =>
		{
			mockSecurityMetrics.sslGrade = 'A';

			const score = SecurityScorer.calculateScore(mockSecurityMetrics);
			const breakdown = SecurityScorer.getSecurityBreakdown(mockSecurityMetrics);

			expect(score).toBeGreaterThan(0.7);
			expect(breakdown.breakdown.sslCertificate.score).toBeGreaterThan(0.8);
		});

		it('should penalize poor SSL grades', () =>
		{
			mockSecurityMetrics.sslGrade = 'C';

			const score = SecurityScorer.calculateScore(mockSecurityMetrics);
			const breakdown = SecurityScorer.getSecurityBreakdown(mockSecurityMetrics);

			expect(score).toBeLessThan(0.7);
			expect(breakdown.breakdown.sslCertificate.details.grade).toBe('C');
		});

		it('should penalize missing HTTPS', () =>
		{
			mockSecurityMetrics.usesHttps = false;
			mockSecurityMetrics.sslGrade = 'F';

			const score = SecurityScorer.calculateScore(mockSecurityMetrics);
			const breakdown = SecurityScorer.getSecurityBreakdown(mockSecurityMetrics);

			expect(score).toBeLessThan(0.5);
			expect(breakdown.breakdown.sslCertificate.details.grade).toBe('F');
		});

		it('should warn about expiring certificates', () =>
		{
			// Certificate expires in 10 days
			mockSecurityMetrics.sslExpiration = new Date(Date.now() + 10 * 24 * 60 * 60 * 1000);

			const recommendations = SecurityScorer.getSecurityRecommendations(mockSecurityMetrics);

			expect(recommendations).toContain(
				expect.stringContaining('certificate will expire'),
			);
		});

		it('should penalize expired certificates', () =>
		{
			// Certificate expired yesterday
			mockSecurityMetrics.sslExpiration = new Date(Date.now() - 24 * 60 * 60 * 1000);

			const score = SecurityScorer.calculateScore(mockSecurityMetrics);
			const recommendations = SecurityScorer.getSecurityRecommendations(mockSecurityMetrics);

			expect(score).toBeLessThan(0.5);
			expect(recommendations).toContain(
				expect.stringContaining('certificate has expired'),
			);
		});

		it('should handle certificate chain properly', () =>
		{
			// Test with valid certificate chain
			const score = SecurityScorer.calculateScore(mockSecurityMetrics);
			const breakdown = SecurityScorer.getSecurityBreakdown(mockSecurityMetrics);

			expect(score).toBeGreaterThan(0.5);
			expect(breakdown.breakdown.sslCertificate.details.issuer).toBe('Let\'s Encrypt');
		});
	});

	describe('Security Headers Scoring', () =>
	{
		it('should give high score for comprehensive security headers', () =>
		{
			mockSecurityMetrics.securityHeaders = {
				hsts: true,
				csp: true,
				xframe: true,
				xssProtection: true,
				contentTypeOptions: true,
				referrerPolicy: true,
				permissionsPolicy: true,
			};

			const score = SecurityScorer.calculateScore(mockSecurityMetrics);
			const breakdown = SecurityScorer.getSecurityBreakdown(mockSecurityMetrics);

			expect(score).toBeGreaterThan(0.8);
			expect(breakdown.breakdown.securityHeaders.score).toBeGreaterThan(0.8);
		});

		it('should penalize missing critical headers', () =>
		{
			mockSecurityMetrics.securityHeaders = {
				hsts: false,
				csp: false,
				xframe: false,
				xssProtection: false,
				contentTypeOptions: false,
				referrerPolicy: false,
				permissionsPolicy: false,
			};

			const score = SecurityScorer.calculateScore(mockSecurityMetrics);
			const recommendations = SecurityScorer.getSecurityRecommendations(mockSecurityMetrics);

			expect(score).toBeLessThan(0.4);
			expect(recommendations).toContain(
				expect.stringContaining('HSTS'),
			);
			expect(recommendations).toContain(
				expect.stringContaining('Content Security Policy'),
			);
		});

		it('should provide specific recommendations for missing headers', () =>
		{
			if (!mockSecurityMetrics.securityHeaders)
			{
				mockSecurityMetrics.securityHeaders = {};
			}
			mockSecurityMetrics.securityHeaders.hsts = false;
			mockSecurityMetrics.securityHeaders.csp = false;

			const recommendations = SecurityScorer.getSecurityRecommendations(mockSecurityMetrics);

			expect(recommendations).toContain(
				expect.stringContaining('HSTS'),
			);
			expect(recommendations).toContain(
				expect.stringContaining('CSP'),
			);
		});

		it('should score partial header implementation', () =>
		{
			mockSecurityMetrics.securityHeaders = {
				hsts: true,
				csp: true,
				xframe: false,
				xssProtection: false,
				contentTypeOptions: true,
				referrerPolicy: false,
				permissionsPolicy: false,
			};

			const score = SecurityScorer.calculateScore(mockSecurityMetrics);
			const breakdown = SecurityScorer.getSecurityBreakdown(mockSecurityMetrics);

			if (mockSecurityMetrics.securityHeaders) 
			{
				expect(breakdown.breakdown.securityHeaders.score).toBeGreaterThan(0.4);
				expect(breakdown.breakdown.securityHeaders.score).toBeLessThan(0.7);
			}
		});
	});

	describe('HTTPS Usage Scoring', () =>
	{
		it('should give high score for HTTPS usage', () =>
		{
			mockSecurityMetrics.usesHttps = true;

			const score = SecurityScorer.calculateScore(mockSecurityMetrics);
			const breakdown = SecurityScorer.getSecurityBreakdown(mockSecurityMetrics);

			expect(breakdown.breakdown.httpsUsage.score).toBe(1.0);
		});

		it('should penalize HTTP usage', () =>
		{
			mockSecurityMetrics.usesHttps = false;

			const score = SecurityScorer.calculateScore(mockSecurityMetrics);
			const breakdown = SecurityScorer.getSecurityBreakdown(mockSecurityMetrics);
			const recommendations = SecurityScorer.getSecurityRecommendations(mockSecurityMetrics);

			expect(breakdown.breakdown.httpsUsage.score).toBe(0.0);
			expect(recommendations).toContain(
				expect.stringContaining('HTTPS'),
			);
		});
	});

	describe('Vulnerability Assessment', () =>
	{
		it('should penalize known vulnerabilities', () =>
		{
			mockSecurityMetrics.vulnerabilities = [
				{
					type: 'heartbleed',
					severity: 'high',
					description: 'OpenSSL Heartbleed vulnerability',
				},
				{
					type: 'poodle',
					severity: 'medium',
					description: 'SSLv3 POODLE vulnerability',
				},
			];

			const score = SecurityScorer.calculateScore(mockSecurityMetrics);
			const breakdown = SecurityScorer.getSecurityBreakdown(mockSecurityMetrics);

			expect(score).toBeLessThan(0.8);
			expect(breakdown.breakdown.vulnerabilities.details.count).toBe(2);
		});

		it('should give perfect score for no vulnerabilities', () =>
		{
			mockSecurityMetrics.vulnerabilities = [];

			const score = SecurityScorer.calculateScore(mockSecurityMetrics);
			const breakdown = SecurityScorer.getSecurityBreakdown(mockSecurityMetrics);

			expect(breakdown.breakdown.vulnerabilities.score).toBe(1.0);
		});

		it('should weight vulnerabilities by severity', () =>
		{
			const highSeverityScore = SecurityScorer.calculateScore({
				...mockSecurityMetrics,
				vulnerabilities: [
					{
						type: 'critical-vuln',
						severity: 'critical',
						description: 'Critical vulnerability',
					},
				],
			});

			const lowSeverityScore = SecurityScorer.calculateScore({
				...mockSecurityMetrics,
				vulnerabilities: [
					{
						type: 'low-vuln',
						severity: 'low',
						description: 'Low severity vulnerability',
					},
				],
			});

			expect(highSeverityScore).toBeLessThan(lowSeverityScore);
		});
	});

	describe('Overall Security Score Calculation', () =>
	{
		it('should calculate weighted overall score correctly', () =>
		{
			const score = SecurityScorer.calculateScore(mockSecurityMetrics);
			const breakdown = SecurityScorer.getSecurityBreakdown(mockSecurityMetrics);

			// Verify score is within valid range
			expect(score).toBeGreaterThanOrEqual(0);
			expect(score).toBeLessThanOrEqual(1);

			// Verify breakdown components exist
			expect(breakdown.breakdown.sslGrade).toBeDefined();
			expect(breakdown.breakdown.securityHeaders).toBeDefined();
			expect(breakdown.breakdown.httpsUsage).toBeDefined();
			expect(breakdown.breakdown.vulnerabilities).toBeDefined();

			// Verify weights sum correctly (approximately)
			const totalWeight =
				breakdown.breakdown.sslGrade.weight
				+ breakdown.breakdown.securityHeaders.weight
				+ breakdown.breakdown.httpsUsage.weight
				+ breakdown.breakdown.vulnerabilities.weight;

			expect(Math.abs(totalWeight - 1.0)).toBeLessThan(0.01);
		});

		it('should assign appropriate security grade', () =>
		{
			const excellentScore = SecurityScorer.calculateScore(mockSecurityMetrics);
			const excellentGrade = SecurityScorer.getSecurityGrade(excellentScore);
			expect(excellentGrade).toMatch(/^[AB][+-]?$/);

			const poorMetrics = {
				...mockSecurityMetrics,
				sslGrade: 'F',
				usesHttps: false,
				securityHeaders: {
					hsts: false,
					csp: false,
					xframe: false,
					xssProtection: false,
					contentTypeOptions: false,
					referrerPolicy: false,
					permissionsPolicy: false,
				},
				vulnerabilities: [
					{
						type: 'critical-vuln',
						severity: 'critical' as const,
						description: 'Critical vulnerability',
					},
				],
			};

			const poorScore = SecurityScorer.calculateScore(poorMetrics);
			const poorGrade = SecurityScorer.getSecurityGrade(poorScore);
			expect(poorGrade).toMatch(/^[DF][+-]?$/);
		});

		it('should provide comprehensive recommendations', () =>
		{
			const poorMetrics = {
				...mockSecurityMetrics,
				sslGrade: 'C',
				securityHeaders: {
					hsts: false,
					csp: false,
					xframe: true,
					xssProtection: true,
					contentTypeOptions: false,
					referrerPolicy: false,
					permissionsPolicy: false,
				},
			};

			const recommendations = SecurityScorer.getSecurityRecommendations(poorMetrics);

			expect(recommendations.length).toBeGreaterThan(0);
			expect(recommendations).toContain(
				expect.stringContaining('SSL'),
			);
		});
	});

	describe('Edge Cases and Error Handling', () =>
	{
		it('should handle missing SSL information gracefully', () =>
		{
			const incompleteMetrics = {
				usesHttps: false,
			} as SecurityMetricsType;

			const score = SecurityScorer.calculateScore(incompleteMetrics);
			const recommendations = SecurityScorer.getSecurityRecommendations(incompleteMetrics);

			expect(score).toBeLessThan(0.5);
			expect(recommendations).toContain(
				expect.stringContaining('HTTPS'),
			);
		});

		it('should handle invalid SSL grades', () =>
		{
			mockSecurityMetrics.sslGrade = 'Invalid' as any;

			const score = SecurityScorer.calculateScore(mockSecurityMetrics);
			const breakdown = SecurityScorer.getSecurityBreakdown(mockSecurityMetrics);

			expect(breakdown.breakdown.sslGrade.score).toBe(0);
		});

		it('should handle missing security headers object', () =>
		{
			delete (mockSecurityMetrics as any).securityHeaders;

			const score = SecurityScorer.calculateScore(mockSecurityMetrics);
			const breakdown = SecurityScorer.getSecurityBreakdown(mockSecurityMetrics);
			const recommendations = SecurityScorer.getSecurityRecommendations(mockSecurityMetrics);

			expect(breakdown.breakdown.securityHeaders.score).toBe(0);
			expect(recommendations).toContain(
				expect.stringContaining('security headers'),
			);
		});

		it('should handle undefined vulnerabilities array', () =>
		{
			delete (mockSecurityMetrics as any).vulnerabilities;

			const score = SecurityScorer.calculateScore(mockSecurityMetrics);
			const breakdown = SecurityScorer.getSecurityBreakdown(mockSecurityMetrics);

			expect(breakdown.breakdown.vulnerabilities.score).toBe(1.0); // Assume no vulnerabilities if not provided
		});
	});

	describe('Scoring Weights and Configuration', () =>
	{
		it('should use predefined scoring weights', () =>
		{
			const breakdown = SecurityScorer.getSecurityBreakdown(mockSecurityMetrics);

			expect(breakdown.breakdown.sslGrade.weight).toBeGreaterThan(0);
			expect(breakdown.breakdown.securityHeaders.weight).toBeGreaterThan(0);
			expect(breakdown.breakdown.httpsUsage.weight).toBeGreaterThan(0);
			expect(breakdown.breakdown.vulnerabilities.weight).toBeGreaterThan(0);
		});
	});

	describe('Detailed Breakdown Analysis', () =>
	{
		it('should provide detailed breakdown for each component', () =>
		{
			const breakdown = SecurityScorer.getSecurityBreakdown(mockSecurityMetrics);

			// SSL Grade breakdown
			expect(breakdown.breakdown.sslGrade.score).toBeDefined();
			expect(breakdown.breakdown.sslGrade.weight).toBeDefined();
			expect(breakdown.breakdown.sslGrade.details).toBeDefined();

			// Security Headers breakdown
			expect(breakdown.breakdown.securityHeaders.score).toBeDefined();
			expect(breakdown.breakdown.securityHeaders.weight).toBeDefined();
			expect(breakdown.breakdown.securityHeaders.details).toBeDefined();
		});

		it('should track individual header scores', () =>
		{
			const breakdown = SecurityScorer.getSecurityBreakdown(mockSecurityMetrics);

			const headerDetails = breakdown.breakdown.securityHeaders.details;
			expect(headerDetails.hsts).toBeDefined();
			expect(headerDetails.csp).toBeDefined();
			expect(headerDetails.xframe).toBeDefined();
		});
	});

	describe('Performance and Caching', () =>
	{
		it('should complete scoring within reasonable time', () =>
		{
			const startTime = Date.now();
			SecurityScorer.calculateScore(mockSecurityMetrics);
			const endTime = Date.now();

			expect(endTime - startTime).toBeLessThan(100); // Should complete within 100ms
		});

		it('should handle large vulnerability lists efficiently', () =>
		{
			const manyVulnerabilities = Array.from({ length: 100 }, (_, i) => ({
				type: `vulnerability-${i}`,
				severity: i % 2 === 0 ? 'high' as const : 'low' as const,
				description: `Description for vulnerability ${i}`,
			}));

			mockSecurityMetrics.vulnerabilities = manyVulnerabilities;

			const startTime = Date.now();
			const score = SecurityScorer.calculateScore(mockSecurityMetrics);
			const endTime = Date.now();

			expect(endTime - startTime).toBeLessThan(200);
			expect(score).toBeLessThan(0.5); // Should be heavily penalized
		});
	});
});
