import {
	describe, test, expect, beforeEach, vi,
} from 'vitest';
import { CompositeRanker } from '../CompositeRanker';
import type {
	DomainDataType,
	RankingWeightsType,
} from '../CompositeRanker';

// Mock the shared module
vi.mock('@shared', () => ({
	logger: {
		getLogger: vi.fn(() => ({
			debug: vi.fn(),
			info: vi.fn(),
			warn: vi.fn(),
			error: vi.fn(),
		})),
	},
}));

describe('CompositeRanker', () =>
{
	let ranker: CompositeRanker;
	let mockDomainData: DomainDataType;

	beforeEach(() =>
	{
		ranker = new CompositeRanker();

		mockDomainData = {
			domain: 'example.com',
			category: 'technology',
			country: 'US',
			performanceMetrics: {
				loadTime: 1200,
				firstContentfulPaint: 800,
				largestContentfulPaint: 1500,
				cumulativeLayoutShift: 0.05,
				firstInputDelay: 50,
				speedIndex: 2000,
				responseTime: 1000,
				pageSize: 2 * 1024 * 1024, // 2MB
				resourceCount: 45,
			},
			coreWebVitals: {
				lcp: 1.5,
				fid: 0.08,
				cls: 0.05,
				fcp: 0.8,
				ttfb: 0.3,
			},
			securityMetrics: {
				sslGrade: 'A',
				sslIssuer: 'Let\'s Encrypt',
				sslExpiration: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days from now
				usesHttps: true,
				securityHeaders: {
					hsts: true,
					csp: true,
					xframe: true,
					xssProtection: true,
					contentTypeOptions: true,
					referrerPolicy: true,
					permissionsPolicy: false,
				},
			},
			seoMetrics: {
				metaTags: {
					title: 'Example Domain - Technology Solutions',
					description: 'Leading technology solutions provider with innovative products and services for modern businesses.',
					keywords: ['technology', 'solutions', 'innovation'],
					canonical: 'https://example.com/',
					robots: 'index,follow',
				},
				robotsTxt: {
					exists: true,
					isValid: true,
					allowsCrawling: true,
					hasDisallowRules: false,
					hasSitemapReference: true,
					userAgents: ['*'],
				},
				sitemap: {
					exists: true,
					isValid: true,
					urlCount: 150,
					hasImages: true,
					hasVideos: false,
					hasNews: false,
				},
				contentMetrics: {
					wordCount: 1200,
					headingStructure: {
						h1Count: 1,
						h2Count: 4,
						h3Count: 8,
						h4Count: 2,
						h5Count: 0,
						h6Count: 0,
						hasProperHierarchy: true,
					},
					imageCount: 12,
					imageAltTextRatio: 0.9,
					internalLinkCount: 15,
					externalLinkCount: 5,
					readabilityScore: 65,
					languageDetected: 'en',
				},
				technicalSEO: {
					pageLoadSpeed: 1.2,
					mobileOptimized: true,
					httpsEnabled: true,
					hasCanonical: true,
					hasHreflang: false,
					breadcrumbsPresent: true,
					schemaMarkupPresent: true,
				},
			},
			technicalMetrics: {
				dnsResponseTime: 45,
				serverResponseTime: 180,
				supportsIpv6: true,
				hasCdn: true,
				compressionEnabled: true,
				keepAliveEnabled: true,
				serverSoftware: 'nginx/1.20.1',
				hostingProvider: 'AWS',
				uptimePercentage: 99.9,
			},
			backlinkMetrics: {
				totalBacklinks: 1250,
				uniqueDomains: 85,
				averageAuthority: 65,
				followLinks: 950,
				nofollowLinks: 300,
				topReferringDomains: ['tech-blog.com', 'industry-news.com', 'partner-site.com'],
			},
			trafficEstimate: 50000,
			lastUpdated: new Date(),
		};
	});

	describe('calculateCompositeScore', () =>
	{
		test('should calculate composite score correctly', () =>
		{
			const result = ranker.calculateCompositeScore(mockDomainData);

			expect(result.domain).toBe('example.com');
			expect(result.overallScore).toBeGreaterThan(0);
			expect(result.overallScore).toBeLessThanOrEqual(1);
			expect(result.grade).toMatch(/^[A-F][+-]?$/);
			expect(result.scores.performance).toBeGreaterThan(0);
			expect(result.scores.security).toBeGreaterThan(0);
			expect(result.scores.seo).toBeGreaterThan(0);
			expect(result.scores.technical).toBeGreaterThan(0);
			expect(result.scores.backlinks).toBeGreaterThan(0);
		});

		test('should handle missing performance metrics', () =>
		{
			const domainWithoutPerformance = { ...mockDomainData };
			delete domainWithoutPerformance.performanceMetrics;

			const result = ranker.calculateCompositeScore(domainWithoutPerformance);

			expect(result.scores.performance).toBe(0.5); // Default score
			expect(result.overallScore).toBeGreaterThan(0);
		});

		test('should handle missing security metrics', () =>
		{
			const domainWithoutSecurity = { ...mockDomainData };
			delete domainWithoutSecurity.securityMetrics;

			const result = ranker.calculateCompositeScore(domainWithoutSecurity);

			expect(result.scores.security).toBe(0.3); // Conservative default
			expect(result.overallScore).toBeGreaterThan(0);
		});

		test('should calculate breakdown correctly', () =>
		{
			const result = ranker.calculateCompositeScore(mockDomainData);

			// Check that breakdown contributions sum to overall score
			const totalContribution =
        result.breakdown.performance.contribution
        + result.breakdown.security.contribution
        + result.breakdown.seo.contribution
        + result.breakdown.technical.contribution
        + result.breakdown.backlinks.contribution;

			expect(Math.abs(totalContribution - result.overallScore)).toBeLessThan(0.001);
		});
	});

	describe('calculateGlobalRankings', () =>
	{
		test('should rank domains by overall score', () =>
		{
			const domains: DomainDataType[] = [
				{ ...mockDomainData, domain: 'low-score.com' },
				{ ...mockDomainData, domain: 'high-score.com' },
				{ ...mockDomainData, domain: 'medium-score.com' },
			];

			// Modify scores to create different rankings
			domains[0].performanceMetrics!.loadTime = 5000; // Poor performance
			domains[0].securityMetrics!.sslGrade = 'C'; // Poor security

			domains[1].performanceMetrics!.loadTime = 500; // Excellent performance
			domains[1].securityMetrics!.sslGrade = 'A+'; // Excellent security

			const rankings = ranker.calculateGlobalRankings(domains);

			expect(rankings).toHaveLength(3);
			expect(rankings[0].rank).toBe(1);
			expect(rankings[1].rank).toBe(2);
			expect(rankings[2].rank).toBe(3);

			// Higher scores should have better ranks
			expect(rankings[0].score).toBeGreaterThanOrEqual(rankings[1].score);
			expect(rankings[1].score).toBeGreaterThanOrEqual(rankings[2].score);
		});

		test('should handle empty domain list', () =>
		{
			const rankings = ranker.calculateGlobalRankings([]);

			expect(rankings).toHaveLength(0);
		});
	});

	describe('calculateCategoryRankings', () =>
	{
		test('should filter and rank domains by category', () =>
		{
			const domains: DomainDataType[] = [
				{ ...mockDomainData, domain: 'tech1.com', category: 'technology' },
				{ ...mockDomainData, domain: 'news1.com', category: 'news' },
				{ ...mockDomainData, domain: 'tech2.com', category: 'technology' },
				{ ...mockDomainData, domain: 'news2.com', category: 'news' },
			];

			const techRankings = ranker.calculateCategoryRankings(domains, 'technology');
			const newsRankings = ranker.calculateCategoryRankings(domains, 'news');

			expect(techRankings).toHaveLength(2);
			expect(newsRankings).toHaveLength(2);

			expect(techRankings.every(r => r.category === 'technology')).toBe(true);
			expect(newsRankings.every(r => r.category === 'news')).toBe(true);
		});

		test('should return empty array for non-existent category', () =>
		{
			const domains: DomainDataType[] = [
				{ ...mockDomainData, domain: 'tech1.com', category: 'technology' },
			];

			const rankings = ranker.calculateCategoryRankings(domains, 'non-existent');

			expect(rankings).toHaveLength(0);
		});
	});

	describe('calculateRankingsWithHistory', () =>
	{
		test('should calculate rankings with rank change tracking', () =>
		{
			const domains: DomainDataType[] = [
				{ ...mockDomainData, domain: 'domain1.com' },
				{ ...mockDomainData, domain: 'domain2.com' },
			];

			const previousRankings = new Map([
				['domain1.com', 2],
				['domain2.com', 1],
			]);

			const rankings = ranker.calculateRankingsWithHistory(domains, previousRankings);

			expect(rankings).toHaveLength(2);
			rankings.forEach((ranking) =>
			{
				expect(ranking.previousRank).toBeDefined();
				expect(ranking.rankChange).toBeDefined();
			});
		});

		test('should handle missing previous rankings', () =>
		{
			const domains: DomainDataType[] = [
				{ ...mockDomainData, domain: 'domain1.com' },
			];

			const rankings = ranker.calculateRankingsWithHistory(domains);

			expect(rankings).toHaveLength(1);
			expect(rankings[0].previousRank).toBeUndefined();
			expect(rankings[0].rankChange).toBeUndefined();
		});
	});

	describe('updateWeights', () =>
	{
		test('should update ranking weights', () =>
		{
			const newWeights: Partial<RankingWeightsType> = {
				PERFORMANCE: 0.4,
				SECURITY: 0.3,
			};

			ranker.updateWeights(newWeights);
			const currentWeights = ranker.getWeights();

			expect(currentWeights.PERFORMANCE).toBe(0.4);
			expect(currentWeights.SECURITY).toBe(0.3);
			expect(currentWeights.SEO).toBe(0.2); // Should remain unchanged
		});

		test('should affect composite score calculation', () =>
		{
			const originalScore = ranker.calculateCompositeScore(mockDomainData);

			// Increase performance weight significantly
			ranker.updateWeights({
				PERFORMANCE: 0.8, SECURITY: 0.05, SEO: 0.05, TECHNICAL: 0.05, BACKLINKS: 0.05,
			});

			const newScore = ranker.calculateCompositeScore(mockDomainData);

			// Performance contribution should be much higher now
			expect(newScore.breakdown.performance.contribution).toBeGreaterThan(originalScore.breakdown.performance.contribution);
		});
	});

	describe('getRankingStatistics', () =>
	{
		test('should calculate ranking statistics correctly', () =>
		{
			const rankings = [
				{
					domain: 'domain1.com', rank: 1, score: 0.9, category: 'tech',
				},
				{
					domain: 'domain2.com', rank: 2, score: 0.8, category: 'tech',
				},
				{
					domain: 'domain3.com', rank: 3, score: 0.7, category: 'tech',
				},
				{
					domain: 'domain4.com', rank: 4, score: 0.6, category: 'tech',
				},
			];

			const stats = ranker.getRankingStatistics(rankings);

			expect(stats.totalDomains).toBe(4);
			expect(stats.averageScore).toBe(0.75);
			expect(stats.topScore).toBe(0.9);
			expect(stats.bottomScore).toBe(0.6);
			expect(stats.gradeDistribution).toBeDefined();
		});

		test('should handle empty rankings', () =>
		{
			const stats = ranker.getRankingStatistics([]);

			expect(stats.totalDomains).toBe(0);
			expect(stats.averageScore).toBe(0);
			expect(stats.medianScore).toBe(0);
			expect(stats.topScore).toBe(0);
			expect(stats.bottomScore).toBe(0);
		});
	});

	describe('getDetailedBreakdown', () =>
	{
		test('should return detailed breakdown for all scoring dimensions', () =>
		{
			const breakdown = ranker.getDetailedBreakdown(mockDomainData);

			expect(breakdown.domain).toBe('example.com');
			expect(breakdown.compositeScore).toBeDefined();
			expect(breakdown.detailedBreakdowns).toBeDefined();
			expect(breakdown.detailedBreakdowns.performance).toBeDefined();
			expect(breakdown.detailedBreakdowns.security).toBeDefined();
			expect(breakdown.detailedBreakdowns.seo).toBeDefined();
			expect(breakdown.detailedBreakdowns.technical).toBeDefined();
			expect(breakdown.detailedBreakdowns.backlinks).toBeDefined();
		});

		test('should handle missing metrics gracefully', () =>
		{
			const minimalDomainData: DomainDataType = {
				domain: 'minimal.com',
			};

			const breakdown = ranker.getDetailedBreakdown(minimalDomainData);

			expect(breakdown.domain).toBe('minimal.com');
			expect(breakdown.compositeScore).toBeDefined();
			expect(breakdown.detailedBreakdowns.performance).toBeNull();
			expect(breakdown.detailedBreakdowns.security).toBeNull();
		});
	});

	describe('getComprehensiveRecommendations', () =>
	{
		test('should return recommendations for all scoring dimensions', () =>
		{
			const recommendations = ranker.getComprehensiveRecommendations(mockDomainData);

			expect(Array.isArray(recommendations)).toBe(true);
			expect(recommendations.length).toBeGreaterThan(0);
		});

		test('should include performance recommendations for poor performance', () =>
		{
			const poorPerformanceDomain = {
				...mockDomainData,
				performanceMetrics: {
					...mockDomainData.performanceMetrics!,
					loadTime: 10000, // Very slow
				},
			};

			const recommendations = ranker.getComprehensiveRecommendations(poorPerformanceDomain);

			// Check that recommendations are returned and include performance-related terms
			expect(recommendations.length).toBeGreaterThan(0);
			expect(recommendations.some(rec =>
				rec.toLowerCase().includes('performance') ||
				rec.toLowerCase().includes('website') ||
				rec.toLowerCase().includes('optimiz'))).toBe(true);
		});
	});

	describe('grade calculation', () =>
	{
		test('should assign correct grades', () =>
		{
			const testCases = [
				{ score: 0.95, expectedGrade: 'A+' },
				{ score: 0.85, expectedGrade: 'A' },
				{ score: 0.75, expectedGrade: 'B' },
				{ score: 0.65, expectedGrade: 'C' },
				{ score: 0.55, expectedGrade: 'D' },
				{ score: 0.45, expectedGrade: 'F' },
			];

			testCases.forEach(({ score, expectedGrade }) =>
			{
				// Test the grade calculation method directly by creating a mock score
				const mockScore = { ...mockDomainData };
				const result = ranker.calculateCompositeScore(mockScore);

				// Test the private method through the public interface
				const grade = (ranker as any).getOverallGrade(score);
				expect(grade).toBe(expectedGrade);
			});
		});
	});

	describe('weight validation', () =>
	{
		test('should warn when weights do not sum to 1.0', () =>
		{
			const invalidWeights: RankingWeightsType = {
				PERFORMANCE: 0.5,
				SECURITY: 0.5,
				SEO: 0.5, // This makes total > 1.0
				TECHNICAL: 0.1,
				BACKLINKS: 0.1,
			};

			// This should trigger a warning in the constructor
			const rankerWithInvalidWeights = new CompositeRanker(invalidWeights);

			expect(rankerWithInvalidWeights).toBeDefined();
		});

		test('should accept valid weights that sum to 1.0', () =>
		{
			const validWeights: RankingWeightsType = {
				PERFORMANCE: 0.3,
				SECURITY: 0.25,
				SEO: 0.25,
				TECHNICAL: 0.1,
				BACKLINKS: 0.1,
			};

			const rankerWithValidWeights = new CompositeRanker(validWeights);
			const weights = rankerWithValidWeights.getWeights();

			expect(weights).toEqual(validWeights);
		});
	});
});
