import {
	describe, it, expect, beforeEach, vi, afterEach,
} from 'vitest';
import { RankingUpdateService } from '../RankingUpdateService';
import { CompositeRanker } from '../CompositeRanker';
import { WorkerDatabaseManager } from '../../database/WorkerDatabaseManager';
import { WorkerJobQueue } from '../../queue/WorkerJobQueue';
import type {
	RankingUpdateTriggerType,
	BatchRankingUpdateType,
	RankingTrendAnalysisType,
} from '../RankingUpdateService';

// Mock dependencies
vi.mock('@shared', () => ({
	logger: {
		getLogger: vi.fn().mockReturnValue({
			info: vi.fn(),
			error: vi.fn(),
			warn: vi.fn(),
			debug: vi.fn(),
		}),
	},
}));

vi.mock('../../database/WorkerDatabaseManager');
vi.mock('../../queue/WorkerJobQueue');
vi.mock('../CompositeRanker');

describe('RankingUpdateService', () =>
{
	let service: RankingUpdateService;
	let mockDbManager: vi.Mocked<WorkerDatabaseManager>;
	let mockJobQueue: vi.Mocked<WorkerJobQueue>;
	let mockCompositeRanker: vi.Mocked<CompositeRanker>;

	beforeEach(() =>
	{
		vi.clearAllMocks();

		// Create mock instances
		mockDbManager = {
			getScyllaClient: vi.fn().mockReturnValue({
				execute: vi.fn().mockResolvedValue({ rows: [] }),
			}),
			getRedisClient: vi.fn().mockReturnValue({
				get: vi.fn().mockResolvedValue(null),
				setex: vi.fn().mockResolvedValue('OK'),
			}),
			healthCheck: vi.fn().mockResolvedValue(true),
		} as unknown as vi.Mocked<WorkerDatabaseManager>;

		mockJobQueue = {
			createConsumer: vi.fn().mockResolvedValue({}),
			publishJob: vi.fn().mockResolvedValue('job_123'),
			healthCheck: vi.fn().mockResolvedValue(true),
		} as unknown as vi.Mocked<WorkerJobQueue>;

		mockCompositeRanker = {
			calculateCompositeScore: vi.fn().mockReturnValue({
				domain: 'example.com',
				overallScore: 0.85,
				grade: 'A',
				globalRank: 100,
				categoryRank: 10,
				scores: {
					performance: 0.9,
					security: 0.8,
					seo: 0.85,
					technical: 0.8,
					backlinks: 0.9,
				},
				breakdown: {
					performance: { score: 0.9, weight: 0.25, contribution: 0.225, grade: 'A' },
					security: { score: 0.8, weight: 0.2, contribution: 0.16, grade: 'B' },
					seo: { score: 0.85, weight: 0.2, contribution: 0.17, grade: 'A' },
					technical: { score: 0.8, weight: 0.15, contribution: 0.12, grade: 'B' },
					backlinks: { score: 0.9, weight: 0.2, contribution: 0.18, grade: 'A' },
				},
				lastCalculated: new Date(),
			}),
			calculateGlobalRankings: vi.fn().mockReturnValue([
				{
					domain: 'example1.com', rank: 1, score: 0.9, category: 'tech',
				},
				{
					domain: 'example2.com', rank: 2, score: 0.8, category: 'tech',
				},
			]),
			calculateCategoryRankings: vi.fn().mockReturnValue([
				{
					domain: 'example1.com', rank: 1, score: 0.9, category: 'tech',
				},
			]),
		} as unknown as vi.Mocked<CompositeRanker>;

		service = new RankingUpdateService(mockDbManager, mockJobQueue, mockCompositeRanker);
	});

	afterEach(() =>
	{
		vi.restoreAllMocks();
	});

	describe('initialization', () =>
	{
		it('should initialize successfully', async () =>
		{
			await service.initialize();

			expect(mockJobQueue.createConsumer).toHaveBeenCalledTimes(3);
			expect(mockJobQueue.createConsumer).toHaveBeenCalledWith(
				'ranking:update',
				expect.any(Function),
				{ concurrency: 3 },
			);
			expect(mockJobQueue.createConsumer).toHaveBeenCalledWith(
				'ranking:batch',
				expect.any(Function),
				{ concurrency: 1 },
			);
			expect(mockJobQueue.createConsumer).toHaveBeenCalledWith(
				'ranking:history',
				expect.any(Function),
				{ concurrency: 2 },
			);
		});

		it('should handle initialization errors', async () =>
		{
			mockJobQueue.createConsumer.mockRejectedValue(new Error('Consumer setup failed'));

			await expect(service.initialize()).rejects.toThrow('Consumer setup failed');
		});
	});

	describe('triggerDomainRankingUpdate', () =>
	{
		beforeEach(async () =>
		{
			await service.initialize();
		});

		it('should trigger ranking update for a single domain', async () =>
		{
			const domain = 'example.com';
			const trigger: Omit<RankingUpdateTriggerType, 'domain'> = {
				type: 'manual',
				priority: 'high',
			};

			const jobId = await service.triggerDomainRankingUpdate(domain, trigger);

			expect(jobId).toBe('job_123');
			expect(mockJobQueue.publishJob).toHaveBeenCalledWith('ranking:update', {
				domain,
				rankingType: 'global',
				triggerId: expect.any(String),
			});
		});

		it('should use default trigger when none provided', async () =>
		{
			const domain = 'example.com';

			const jobId = await service.triggerDomainRankingUpdate(domain);

			expect(jobId).toBe('job_123');
			expect(mockJobQueue.publishJob).toHaveBeenCalledWith('ranking:update', {
				domain,
				rankingType: 'global',
				triggerId: expect.any(String),
			});
		});

		it('should handle trigger errors', async () =>
		{
			mockJobQueue.publishJob.mockRejectedValue(new Error('Queue error'));

			await expect(service.triggerDomainRankingUpdate('example.com')).rejects.toThrow('Queue error');
		});
	});

	describe('triggerBatchRankingUpdate', () =>
	{
		beforeEach(async () =>
		{
			await service.initialize();
		});

		it('should trigger batch ranking update for multiple domains', async () =>
		{
			const domains = ['example1.com', 'example2.com', 'example3.com'];
			const rankingType = 'global';

			const batchId = await service.triggerBatchRankingUpdate(domains, rankingType);

			expect(batchId).toMatch(/^batch_\d+_[a-z0-9]+$/);
			expect(mockJobQueue.publishJob).toHaveBeenCalledWith(
				'ranking:batch',
				expect.objectContaining({
					batchId,
					domains,
					rankingType,
				}),
				expect.objectContaining({
					priority: 5, // medium priority
				}),
			);
		});

		it('should trigger category-specific batch update', async () =>
		{
			const domains = ['tech1.com', 'tech2.com'];
			const rankingType = 'category';
			const category = 'technology';

			const batchId = await service.triggerBatchRankingUpdate(domains, rankingType, category, 'high');

			expect(batchId).toMatch(/^batch_\d+_[a-z0-9]+$/);
			expect(mockJobQueue.publishJob).toHaveBeenCalledWith(
				'ranking:batch',
				expect.objectContaining({
					batchId,
					domains,
					rankingType,
					category,
				}),
				expect.objectContaining({
					priority: 10, // high priority
				}),
			);
		});

		it('should track active batches', async () =>
		{
			const domains = ['example.com'];
			const batchId = await service.triggerBatchRankingUpdate(domains);

			const batchStatus = service.getBatchStatus(batchId);

			expect(batchStatus).toBeDefined();
			expect(batchStatus?.domains).toEqual(domains);
			expect(batchStatus?.status).toBe('pending');
		});
	});

	describe('calculateRankingTrend', () =>
	{
		beforeEach(async () =>
		{
			await service.initialize();
		});

		it('should calculate ranking trend with historical data', async () =>
		{
			const domain = 'example.com';
			const mockHistoryData = [
				{
					domain, date: '2024-01-01', ranking_type: 'global', rank: 100, score: 0.8,
				},
				{
					domain, date: '2024-01-02', ranking_type: 'global', rank: 95, score: 0.82,
				},
				{
					domain, date: '2024-01-03', ranking_type: 'global', rank: 90, score: 0.85,
				},
			];

			mockDbManager.getScyllaClient().execute.mockResolvedValue({
				rows: mockHistoryData,
			});

			const trendAnalysis = await service.calculateRankingTrend(domain, 'global', '30d');

			expect(trendAnalysis).toEqual({
				domain,
				period: '30d',
				trendDirection: 'up', // Rank improved from 100 to 90
				averageRank: 95, // (100 + 95 + 90) / 3
				bestRank: 90,
				worstRank: 100,
				volatility: expect.any(Number),
				rankChanges: expect.arrayContaining([
					expect.objectContaining({
						domain,
						rank: 100,
						score: 0.8,
					}),
				]),
			});
		});

		it('should handle empty history data', async () =>
		{
			mockDbManager.getScyllaClient().execute.mockResolvedValue({ rows: [] });

			const trendAnalysis = await service.calculateRankingTrend('example.com', 'global', '30d');

			expect(trendAnalysis).toEqual({
				domain: 'example.com',
				period: '30d',
				trendDirection: 'stable',
				averageRank: 0,
				bestRank: 0,
				worstRank: 0,
				volatility: 0,
				rankChanges: [],
			});
		});

		it('should calculate trend direction correctly for declining domains', async () =>
		{
			const domain = 'declining.com';
			const mockHistoryData = [
				{
					domain, date: '2024-01-01', ranking_type: 'global', rank: 50, score: 0.9,
				},
				{
					domain, date: '2024-01-02', ranking_type: 'global', rank: 60, score: 0.85,
				},
				{
					domain, date: '2024-01-03', ranking_type: 'global', rank: 70, score: 0.8,
				},
			];

			mockDbManager.getScyllaClient().execute.mockResolvedValue({
				rows: mockHistoryData,
			});

			const trendAnalysis = await service.calculateRankingTrend(domain, 'global', '30d');

			expect(trendAnalysis.trendDirection).toBe('down'); // Rank worsened from 50 to 70
		});
	});

	describe('batch management', () =>
	{
		beforeEach(async () =>
		{
			await service.initialize();
		});

		it('should track active batches', async () =>
		{
			const domains1 = ['example1.com'];
			const domains2 = ['example2.com'];

			const batchId1 = await service.triggerBatchRankingUpdate(domains1);
			const batchId2 = await service.triggerBatchRankingUpdate(domains2);

			const activeBatches = service.getActiveBatches();

			expect(activeBatches).toHaveLength(2);
			expect(activeBatches.map(b => b.batchId)).toContain(batchId1);
			expect(activeBatches.map(b => b.batchId)).toContain(batchId2);
		});

		it('should clean up completed batches', async () =>
		{
			const domains = ['example.com'];
			const batchId = await service.triggerBatchRankingUpdate(domains);

			// Simulate completed batch
			const batch = service.getBatchStatus(batchId);
			if (batch)
			{
				batch.status = 'completed';
				batch.completedAt = new Date(Date.now() - 25 * 60 * 60 * 1000); // 25 hours ago
			}

			service.cleanupCompletedBatches();

			const batchAfterCleanup = service.getBatchStatus(batchId);

			expect(batchAfterCleanup).toBeUndefined();
		});
	});

	describe('health check', () =>
	{
		beforeEach(async () =>
		{
			await service.initialize();
		});

		it('should return true when all dependencies are healthy', async () =>
		{
			const isHealthy = await service.healthCheck();

			expect(isHealthy).toBe(true);
		});

		it('should return false when database is unhealthy', async () =>
		{
			mockDbManager.healthCheck.mockResolvedValue(false);

			const isHealthy = await service.healthCheck();

			expect(isHealthy).toBe(false);
		});

		it('should return false when job queue is unhealthy', async () =>
		{
			mockJobQueue.healthCheck.mockResolvedValue(false);

			const isHealthy = await service.healthCheck();

			expect(isHealthy).toBe(false);
		});

		it('should handle health check errors', async () =>
		{
			mockDbManager.healthCheck.mockRejectedValue(new Error('Health check failed'));

			const isHealthy = await service.healthCheck();

			expect(isHealthy).toBe(false);
		});
	});

	describe('priority handling', () =>
	{
		beforeEach(async () =>
		{
			await service.initialize();
		});

		it('should convert priority strings to numeric values correctly', async () =>
		{
			const domains = ['example.com'];

			// Test low priority
			await service.triggerBatchRankingUpdate(domains, 'global', undefined, 'low');

			expect(mockJobQueue.publishJob).toHaveBeenLastCalledWith(
				expect.any(String),
				expect.any(Object),
				expect.objectContaining({ priority: 1 }),
			);

			// Test medium priority
			await service.triggerBatchRankingUpdate(domains, 'global', undefined, 'medium');

			expect(mockJobQueue.publishJob).toHaveBeenLastCalledWith(
				expect.any(String),
				expect.any(Object),
				expect.objectContaining({ priority: 5 }),
			);

			// Test high priority
			await service.triggerBatchRankingUpdate(domains, 'global', undefined, 'high');

			expect(mockJobQueue.publishJob).toHaveBeenLastCalledWith(
				expect.any(String),
				expect.any(Object),
				expect.objectContaining({ priority: 10 }),
			);
		});
	});

	describe('ranking validation and anomaly detection', () =>
	{
		beforeEach(async () =>
		{
			await service.initialize();
		});

		it('should validate ranking calculations', async () =>
		{
			const mockDomainData = {
				domain: 'example.com',
				category: 'technology',
				performanceMetrics: {
					loadTime: 1200,
					firstContentfulPaint: 800,
					largestContentfulPaint: 1500,
					cumulativeLayoutShift: 0.05,
					firstInputDelay: 50,
				},
				securityMetrics: {
					sslGrade: 'A',
					usesHttps: true,
				},
				seoMetrics: {
					metaTags: {
						title: 'Example Domain',
						description: 'Test description',
					},
				},
			};

			// Mock fetchDomainData to return our test data
			const fetchSpy = vi.spyOn(service as any, 'fetchDomainData');
			fetchSpy.mockResolvedValue(mockDomainData);

			// Mock storeDomainRanking
			const storeSpy = vi.spyOn(service as any, 'storeDomainRanking');
			storeSpy.mockResolvedValue(undefined);

			// Mock updateRankingHistory
			const historySpy = vi.spyOn(service as any, 'updateRankingHistory');
			historySpy.mockResolvedValue(undefined);

			// Mock triggerRelatedUpdates
			const updatesSpy = vi.spyOn(service as any, 'triggerRelatedUpdates');
			updatesSpy.mockResolvedValue(undefined);

			// Test single ranking update
			await (service as any).handleSingleRankingUpdate({
				domain: 'example.com',
				rankingType: 'global',
			});

			expect(mockCompositeRanker.calculateCompositeScore).toHaveBeenCalledWith(mockDomainData);
			expect(storeSpy).toHaveBeenCalled();
			expect(historySpy).toHaveBeenCalled();
			expect(updatesSpy).toHaveBeenCalled();
		});
	});

	describe('quality control systems', () =>
	{
		beforeEach(async () =>
		{
			await service.initialize();
		});

		it('should detect ranking anomalies', async () =>
		{
			// Test with extreme ranking changes that should trigger anomaly detection
			const mockHistoryData = [
				{
					domain: 'volatile.com', date: '2024-01-01', ranking_type: 'global', rank: 10, score: 0.9,
				},
				{
					domain: 'volatile.com', date: '2024-01-02', ranking_type: 'global', rank: 1000, score: 0.3,
				},
				{
					domain: 'volatile.com', date: '2024-01-03', ranking_type: 'global', rank: 5, score: 0.95,
				},
			];

			mockDbManager.getScyllaClient().execute.mockResolvedValue({
				rows: mockHistoryData,
			});

			const trendAnalysis = await service.calculateRankingTrend('volatile.com', 'global', '30d');

			// High volatility should be detected
			expect(trendAnalysis.volatility).toBeGreaterThan(100);
			expect(trendAnalysis.bestRank).toBe(5);
			expect(trendAnalysis.worstRank).toBe(1000);
		});
	});
});
