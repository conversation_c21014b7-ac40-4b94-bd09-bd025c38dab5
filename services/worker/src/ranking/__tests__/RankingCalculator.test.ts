import { describe, test, expect, beforeEach } from 'vitest';
import type { DomainData } from '../CompositeRanker';
import { RankingCalculator } from '../RankingCalculator';

describe('RankingCalculator', () =>
{
	let calculator: RankingCalculator;

	beforeEach(() =>
	{
		calculator = new RankingCalculator({
			enableValidation: true,
			enableMonitoring: true,
			batchSize: 10,
		});
	});

	describe('calculateDomainScore', () =>
	{
		test('should calculate score for domain with complete data', async () =>
		{
			const domainData: DomainData = {
				domain: 'example.com',
				category: 'technology',
				performanceMetrics: {
					loadTime: 1200,
					responseTime: 800,
					pageSize: 2 * 1024 * 1024,
					resourceCount: 45,
				},
				coreWebVitals: {
					lcp: 2.0,
					fid: 0.08,
					cls: 0.05,
				},
				securityMetrics: {
					sslGrade: 'A',
					usesHttps: true,
					securityHeaders: {
						hsts: true,
						csp: true,
						xframe: true,
					},
					vulnerabilities: [],
				},
				seoMetrics: {
					metaTags: {
						title: 'Example Domain - Technology Solutions',
						description: 'Leading technology solutions provider with innovative products and services for modern businesses.',
						canonical: 'https://example.com',
					},
					robotsTxt: {
						exists: true,
						isValid: true,
						allowsCrawling: true,
						hasDisallowRules: false,
						hasSitemapReference: true,
						userAgents: ['*'],
					},
					sitemap: {
						exists: true,
						isValid: true,
						urlCount: 150,
						hasImages: true,
						hasVideos: false,
						hasNews: false,
					},
				},
				technicalMetrics: {
					dnsResponseTime: 45,
					serverResponseTime: 180,
					supportsIpv6: true,
					hasCdn: true,
					compressionEnabled: true,
					uptimePercentage: 99.9,
				},
				backlinkMetrics: {
					totalBacklinks: 1500,
					uniqueDomains: 120,
					averageAuthority: 65,
					followLinks: 1200,
					nofollowLinks: 300,
					topReferringDomains: ['tech-blog.com', 'industry-news.com'],
				},
			};

			const score = await calculator.calculateDomainScore(domainData);

			expect(score).toHaveProperty('domain', 'example.com');
			expect(score).toHaveProperty('overallScore');
			expect(score).toHaveProperty('grade');
			expect(score).toHaveProperty('scores');
			expect(score).toHaveProperty('breakdown');
			expect(score).toHaveProperty('lastCalculated');

			expect(score.overallScore).toBeGreaterThan(0);
			expect(score.overallScore).toBeLessThanOrEqual(1);

			expect(score.scores).toHaveProperty('performance');
			expect(score.scores).toHaveProperty('security');
			expect(score.scores).toHaveProperty('seo');
			expect(score.scores).toHaveProperty('technical');
			expect(score.scores).toHaveProperty('backlinks');

			expect(score.breakdown).toHaveProperty('performance');
			expect(score.breakdown).toHaveProperty('security');
			expect(score.breakdown).toHaveProperty('seo');
			expect(score.breakdown).toHaveProperty('technical');
			expect(score.breakdown).toHaveProperty('backlinks');
		});

		test('should handle domain with minimal data', async () =>
		{
			const domainData: DomainData = {
				domain: 'minimal.com',
			};

			const score = await calculator.calculateDomainScore(domainData);

			expect(score).toHaveProperty('domain', 'minimal.com');
			expect(score.overallScore).toBeGreaterThan(0);
			expect(score.overallScore).toBeLessThanOrEqual(1);
		});

		test('should throw error for invalid domain data', async () =>
		{
			const invalidData: DomainData = {
				domain: '',
				performanceMetrics: {
					loadTime: -1000, // Invalid negative value
				},
			};

			// Should not throw due to validation warnings, but should log warnings
			const score = await calculator.calculateDomainScore(invalidData);
			expect(score).toBeDefined();
		});
	});

	describe('calculateBatchRankings', () =>
	{
		test('should calculate rankings for multiple domains', async () =>
		{
			const domains: DomainData[] = [
				{
					domain: 'first.com',
					performanceMetrics: { loadTime: 1000, responseTime: 500 },
					securityMetrics: { sslGrade: 'A+', usesHttps: true },
				},
				{
					domain: 'second.com',
					performanceMetrics: { loadTime: 2000, responseTime: 1000 },
					securityMetrics: { sslGrade: 'B', usesHttps: true },
				},
				{
					domain: 'third.com',
					performanceMetrics: { loadTime: 3000, responseTime: 1500 },
					securityMetrics: { sslGrade: 'C', usesHttps: false },
				},
			];

			const rankings = await calculator.calculateBatchRankings(domains);

			expect(rankings).toHaveLength(3);
			expect(rankings[0]).toHaveProperty('rank', 1);
			expect(rankings[1]).toHaveProperty('rank', 2);
			expect(rankings[2]).toHaveProperty('rank', 3);

			// Rankings should be sorted by score (highest first)
			expect(rankings[0].score).toBeGreaterThanOrEqual(rankings[1].score);
			expect(rankings[1].score).toBeGreaterThanOrEqual(rankings[2].score);

			// First domain should have highest score (better performance and security)
			expect(rankings[0].domain).toBe('first.com');
		});

		test('should handle empty domain list', async () =>
		{
			const rankings = await calculator.calculateBatchRankings([]);

			expect(rankings).toHaveLength(0);
		});

		test('should process large batches correctly', async () =>
		{
			const domains: DomainData[] = Array.from({ length: 25 }, (_, i) => ({
				domain: `domain${i}.com`,
				performanceMetrics: {
					loadTime: 1000 + (i * 100),
					responseTime: 500 + (i * 50),
				},
			}));

			const rankings = await calculator.calculateBatchRankings(domains);

			expect(rankings).toHaveLength(25);
			expect(rankings[0].rank).toBe(1);
			expect(rankings[24].rank).toBe(25);

			// Should be sorted by score
			for (let i = 0; i < rankings.length - 1; i++)
			{
				expect(rankings[i].score).toBeGreaterThanOrEqual(rankings[i + 1].score);
			}
		});
	});

	describe('calculateCategoryRankings', () =>
	{
		test('should calculate category-specific rankings', async () =>
		{
			const domains: DomainData[] = [
				{
					domain: 'tech1.com',
					category: 'technology',
					performanceMetrics: { loadTime: 1000 },
				},
				{
					domain: 'tech2.com',
					category: 'technology',
					performanceMetrics: { loadTime: 2000 },
				},
				{
					domain: 'finance1.com',
					category: 'finance',
					performanceMetrics: { loadTime: 1500 },
				},
			];

			const techRankings = await calculator.calculateCategoryRankings(domains, 'technology');

			expect(techRankings).toHaveLength(2);
			expect(techRankings[0].domain).toBe('tech1.com');
			expect(techRankings[0].category).toBe('technology');
			expect(techRankings[1].domain).toBe('tech2.com');
		});

		test('should return empty array for non-existent category', async () =>
		{
			const domains: DomainData[] = [
				{ domain: 'tech1.com', category: 'technology' },
			];

			const rankings = await calculator.calculateCategoryRankings(domains, 'nonexistent');

			expect(rankings).toHaveLength(0);
		});
	});

	describe('getDetailedBreakdown', () =>
	{
		test('should provide detailed breakdown for domain', async () =>
		{
			const domainData: DomainData = {
				domain: 'example.com',
				performanceMetrics: {
					loadTime: 1200,
					responseTime: 800,
				},
				securityMetrics: {
					sslGrade: 'A',
					usesHttps: true,
				},
			};

			const breakdown = await calculator.getDetailedBreakdown(domainData);

			expect(breakdown).toHaveProperty('domain', 'example.com');
			expect(breakdown).toHaveProperty('compositeScore');
			expect(breakdown).toHaveProperty('detailedBreakdowns');

			expect(breakdown.detailedBreakdowns).toHaveProperty('performance');
			expect(breakdown.detailedBreakdowns).toHaveProperty('security');
		});
	});

	describe('getRecommendations', () =>
	{
		test('should provide recommendations for domain', async () =>
		{
			const domainData: DomainData = {
				domain: 'example.com',
				securityMetrics: {
					sslGrade: 'C',
					usesHttps: false,
					securityHeaders: {
						hsts: false,
						csp: false,
					},
				},
				seoMetrics: {
					metaTags: {
						// Missing title and description
					},
				},
			};

			const recommendations = await calculator.getRecommendations(domainData);

			expect(recommendations).toBeInstanceOf(Array);
			expect(recommendations.length).toBeGreaterThan(0);
			expect(recommendations.some(rec => rec.includes('HTTPS') || rec.includes('title'))).toBe(true);
		});
	});

	describe('monitoring and metrics', () =>
	{
		test('should track monitoring metrics', async () =>
		{
			const domainData: DomainData = {
				domain: 'example.com',
				performanceMetrics: { loadTime: 1000 },
			};

			await calculator.calculateDomainScore(domainData);

			const metrics = calculator.getMonitoringMetrics();

			expect(metrics).toHaveProperty('calculationTime');
			expect(metrics).toHaveProperty('domainsProcessed');
			expect(metrics).toHaveProperty('averageScore');
			expect(metrics).toHaveProperty('errorCount');
			expect(metrics).toHaveProperty('validationFailures');

			expect(metrics.domainsProcessed).toBe(1);
			expect(metrics.calculationTime).toBeGreaterThanOrEqual(0);
		});

		test('should reset monitoring metrics', () =>
		{
			calculator.resetMonitoringMetrics();

			const metrics = calculator.getMonitoringMetrics();

			expect(metrics.calculationTime).toBe(0);
			expect(metrics.domainsProcessed).toBe(0);
			expect(metrics.averageScore).toBe(0);
			expect(metrics.errorCount).toBe(0);
			expect(metrics.validationFailures).toBe(0);
		});
	});

	describe('weight management', () =>
	{
		test('should update and get ranking weights', () =>
		{
			const newWeights = {
				PERFORMANCE: 0.3,
				SECURITY: 0.3,
			};

			calculator.updateWeights(newWeights);

			const weights = calculator.getWeights();

			expect(weights.PERFORMANCE).toBe(0.3);
			expect(weights.SECURITY).toBe(0.3);
		});
	});

	describe('ranking statistics', () =>
	{
		test('should calculate ranking statistics', () =>
		{
			const rankings = [
				{ domain: 'first.com', rank: 1, score: 0.9 },
				{ domain: 'second.com', rank: 2, score: 0.8 },
				{ domain: 'third.com', rank: 3, score: 0.7 },
			];

			const stats = calculator.getRankingStatistics(rankings);

			expect(stats).toHaveProperty('totalDomains', 3);
			expect(stats).toHaveProperty('averageScore');
			expect(stats).toHaveProperty('medianScore');
			expect(stats).toHaveProperty('topScore', 0.9);
			expect(stats).toHaveProperty('bottomScore', 0.7);
			expect(stats).toHaveProperty('gradeDistribution');

			expect(stats.averageScore).toBeCloseTo(0.8, 1);
		});
	});
});
