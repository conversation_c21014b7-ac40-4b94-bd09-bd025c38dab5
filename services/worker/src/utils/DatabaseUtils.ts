/**
 * Database Utilities
 * Helper functions for database operations and management
 */

import { logger as sharedLogger } from '@shared';

const logger = sharedLogger.getLogger('DatabaseUtils');

type ConnectionConfig = {
	host: string;
	port: number;
	username?: string;
	password?: string;
	database?: string;
	ssl?: boolean;
	timeout?: number;
	retries?: number;
};

type QueryResult<T = any> = {
	rows: T[];
	rowCount: number;
	duration: number;
	query: string;
};

type TransactionCallback<T> = () => Promise<T>;

/**
 * Database connection pool manager
 */
class ConnectionPool
{
	private connections: Map<string, any>;
	private config: ConnectionConfig;
	private maxConnections: number;
	private activeConnections: number;

	constructor(config: ConnectionConfig, maxConnections = 10)
	{
		this.connections = new Map();
		this.config = config;
		this.maxConnections = maxConnections;
		this.activeConnections = 0;
	}

	/**
	 * Get connection from pool
	 */
	async getConnection(): Promise<any>
	{
		// Simplified implementation - in real scenario would use actual DB drivers
		if (this.activeConnections < this.maxConnections)
		{
			this.activeConnections++;
			return { id: `conn_${Date.now()}`, config: this.config };
		}

		throw new Error('Connection pool exhausted');
	}

	/**
	 * Release connection back to pool
	 */
	async releaseConnection(connection: any): Promise<void>
	{
		this.activeConnections--;
	}

	/**
	 * Close all connections
	 */
	async close(): Promise<void>
	{
		this.connections.clear();
		this.activeConnections = 0;
	}

	/**
	 * Get pool statistics
	 */
	getStats(): { active: number; max: number; utilization: number }
	{
		return {
			active: this.activeConnections,
			max: this.maxConnections,
			utilization: (this.activeConnections / this.maxConnections) * 100,
		};
	}
}

/**
 * Query builder for common database operations
 */
class QueryBuilder
{
	private query: string;
	private params: any[];

	constructor()
	{
		this.query = '';
		this.params = [];
	}

	/**
	 * SELECT query
	 */
	select(columns: string[] | string = '*'): this
	{
		const cols = Array.isArray(columns) ? columns.join(', ') : columns;
		this.query = `SELECT ${cols}`;
		return this;
	}

	/**
	 * FROM clause
	 */
	from(table: string): this
	{
		this.query += ` FROM ${table}`;
		return this;
	}

	/**
	 * WHERE clause
	 */
	where(condition: string, value?: any): this
	{
		const whereClause = this.query.includes('WHERE') ? ' AND' : ' WHERE';
		this.query += `${whereClause} ${condition}`;

		if (value !== undefined)
		{
			this.params.push(value);
		}

		return this;
	}

	/**
	 * ORDER BY clause
	 */
	orderBy(column: string, direction: 'ASC' | 'DESC' = 'ASC'): this
	{
		this.query += ` ORDER BY ${column} ${direction}`;
		return this;
	}

	/**
	 * LIMIT clause
	 */
	limit(count: number): this
	{
		this.query += ` LIMIT ${count}`;
		return this;
	}

	/**
	 * OFFSET clause
	 */
	offset(count: number): this
	{
		this.query += ` OFFSET ${count}`;
		return this;
	}

	/**
	 * INSERT query
	 */
	insert(table: string, data: Record<string, any>): this
	{
		const columns = Object.keys(data);
		const placeholders = columns.map(() => '?').join(', ');

		this.query = `INSERT INTO ${table} (${columns.join(', ')}) VALUES (${placeholders})`;
		this.params = Object.values(data);

		return this;
	}

	/**
	 * UPDATE query
	 */
	update(table: string, data: Record<string, any>): this
	{
		const setClause = Object.keys(data).map(key => `${key} = ?`).join(', ');

		this.query = `UPDATE ${table} SET ${setClause}`;
		this.params = Object.values(data);

		return this;
	}

	/**
	 * DELETE query
	 */
	delete(table: string): this
	{
		this.query = `DELETE FROM ${table}`;
		return this;
	}

	/**
	 * Build final query
	 */
	build(): { query: string; params: any[] }
	{
		return {
			query: this.query,
			params: this.params,
		};
	}

	/**
	 * Reset builder
	 */
	reset(): this
	{
		this.query = '';
		this.params = [];
		return this;
	}
}

/**
 * Database migration manager
 */
class MigrationManager
{
	private migrations: Array<{
		version: string;
		up: string;
		down: string;
	}>;

	constructor()
	{
		this.migrations = [];
	}

	/**
	 * Add migration
	 */
	addMigration(version: string, up: string, down: string): void
	{
		this.migrations.push({ version, up, down });
		this.migrations.sort((a, b) => a.version.localeCompare(b.version));
	}

	/**
	 * Run migrations up to version
	 */
	async migrate(targetVersion?: string): Promise<string[]>
	{
		const appliedMigrations: string[] = [];

		for (const migration of this.migrations)
		{
			if (targetVersion && migration.version > targetVersion)
			{
				break;
			}

			// In real implementation, would execute migration.up
			logger.info(`Applying migration ${migration.version}`);
			appliedMigrations.push(migration.version);
		}

		return appliedMigrations;
	}

	/**
	 * Rollback migrations
	 */
	async rollback(targetVersion: string): Promise<string[]>
	{
		const rolledBackMigrations: string[] = [];

		// Reverse order for rollback
		const reversedMigrations = [...this.migrations].reverse();

		for (const migration of reversedMigrations)
		{
			if (migration.version <= targetVersion)
			{
				break;
			}

			// In real implementation, would execute migration.down
			logger.info(`Rolling back migration ${migration.version}`);
			rolledBackMigrations.push(migration.version);
		}

		return rolledBackMigrations;
	}
}

/**
 * Database health checker
 */
class DatabaseHealthChecker
{
	private connectionPool: ConnectionPool;

	constructor(connectionPool: ConnectionPool)
	{
		this.connectionPool = connectionPool;
	}

	/**
	 * Check database connectivity
	 */
	async checkConnectivity(): Promise<{ healthy: boolean; latency?: number; error?: string }>
	{
		const start = Date.now();

		try
		{
			const connection = await this.connectionPool.getConnection();
			await this.connectionPool.releaseConnection(connection);

			return {
				healthy: true,
				latency: Date.now() - start,
			};
		}
		catch (error)
		{
			return {
				healthy: false,
				error: error instanceof Error ? error.message : 'Unknown error',
			};
		}
	}

	/**
	 * Check query performance
	 */
	async checkQueryPerformance(testQuery = 'SELECT 1'): Promise<{
		healthy: boolean;
		duration?: number;
		error?: string;
	}>
	{
		const start = Date.now();

		try
		{
			// In real implementation, would execute actual query
			await new Promise(resolve => setTimeout(resolve, 10)); // Simulate query

			const duration = Date.now() - start;

			return {
				healthy: duration < 1000, // Consider healthy if under 1 second
				duration,
			};
		}
		catch (error)
		{
			return {
				healthy: false,
				error: error instanceof Error ? error.message : 'Unknown error',
			};
		}
	}
}

/**
 * SQL injection prevention utilities
 */
class SQLSanitizer
{
	/**
	 * Escape SQL string
	 */
	static escapeString(value: string): string
	{
		return value.replace(/'/g, "''");
	}

	/**
	 * Escape SQL identifier (table/column names)
	 */
	static escapeIdentifier(identifier: string): string
	{
		return `"${identifier.replace(/"/g, '""')}"`;
	}

	/**
	 * Validate SQL identifier
	 */
	static isValidIdentifier(identifier: string): boolean
	{
		// Only allow alphanumeric characters and underscores
		return /^[a-zA-Z_][a-zA-Z0-9_]*$/.test(identifier);
	}

	/**
	 * Detect potential SQL injection
	 */
	static detectSQLInjection(input: string): boolean
	{
		const dangerousPatterns = [
			/(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i,
			/(--|\/\*|\*\/)/,
			/(\b(OR|AND)\b.*=.*)/i,
			/['"]\s*(OR|AND)\s*['"]/i,
		];

		return dangerousPatterns.some(pattern => pattern.test(input));
	}
}

/**
 * Database backup utilities
 */
class BackupManager
{
	/**
	 * Create database backup
	 */
	async createBackup(config: ConnectionConfig, outputPath: string): Promise<{
		success: boolean;
		path?: string;
		size?: number;
		error?: string;
	}>
	{
		try
		{
			// In real implementation, would use database-specific backup tools
			const backupData = JSON.stringify({
				timestamp: new Date().toISOString(),
				config: { ...config, password: '[REDACTED]' },
				tables: [], // Would contain actual table data
			});

			// Simulate writing backup file
			const size = Buffer.byteLength(backupData, 'utf8');

			return {
				success: true,
				path: outputPath,
				size,
			};
		}
		catch (error)
		{
			return {
				success: false,
				error: error instanceof Error ? error.message : 'Unknown error',
			};
		}
	}

	/**
	 * Restore database from backup
	 */
	async restoreBackup(config: ConnectionConfig, backupPath: string): Promise<{
		success: boolean;
		tablesRestored?: number;
		error?: string;
	}>
	{
		try
		{
			// In real implementation, would read and restore backup file
			return {
				success: true,
				tablesRestored: 0, // Would be actual count
			};
		}
		catch (error)
		{
			return {
				success: false,
				error: error instanceof Error ? error.message : 'Unknown error',
			};
		}
	}
}

/**
 * Database performance monitor
 */
class PerformanceMonitor
{
	private queryStats: Map<string, {
		count: number;
		totalDuration: number;
		avgDuration: number;
		maxDuration: number;
		minDuration: number;
	}>;

	constructor()
	{
		this.queryStats = new Map();
	}

	/**
	 * Record query execution
	 */
	recordQuery(query: string, duration: number): void
	{
		const normalizedQuery = this.normalizeQuery(query);
		const stats = this.queryStats.get(normalizedQuery) || {
			count: 0,
			totalDuration: 0,
			avgDuration: 0,
			maxDuration: 0,
			minDuration: Infinity,
		};

		stats.count++;
		stats.totalDuration += duration;
		stats.avgDuration = stats.totalDuration / stats.count;
		stats.maxDuration = Math.max(stats.maxDuration, duration);
		stats.minDuration = Math.min(stats.minDuration, duration);

		this.queryStats.set(normalizedQuery, stats);
	}

	/**
	 * Get performance statistics
	 */
	getStats(): Array<{
		query: string;
		count: number;
		avgDuration: number;
		maxDuration: number;
		minDuration: number;
	}>
	{
		return Array.from(this.queryStats.entries()).map(([query, stats]) => ({
			query,
			count: stats.count,
			avgDuration: Math.round(stats.avgDuration * 100) / 100,
			maxDuration: stats.maxDuration,
			minDuration: stats.minDuration === Infinity ? 0 : stats.minDuration,
		}));
	}

	/**
	 * Get slow queries
	 */
	getSlowQueries(threshold = 1000): Array<{
		query: string;
		avgDuration: number;
		count: number;
	}>
	{
		return this.getStats()
			.filter(stat => stat.avgDuration > threshold)
			.sort((a, b) => b.avgDuration - a.avgDuration);
	}

	/**
	 * Normalize query for grouping
	 */
	private normalizeQuery(query: string): string
	{
		// Replace parameter values with placeholders
		return query
			.replace(/\b\d+\b/g, '?') // Numbers
			.replace(/'[^']*'/g, '?') // String literals
			.replace(/\s+/g, ' ') // Multiple spaces
			.trim()
			.toLowerCase();
	}
}

export type { ConnectionConfig, QueryResult, TransactionCallback };

export {
	ConnectionPool,
	QueryBuilder,
	MigrationManager,
	DatabaseHealthChecker,
	SQLSanitizer,
	BackupManager,
	PerformanceMonitor,
};
