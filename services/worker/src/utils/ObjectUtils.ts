/**
 * Object Utilities
 * Helper functions for object manipulation and processing
 */

/**
 * Deep clone an object
 */
function deepClone<T>(obj: T): T
{
	if (obj === null || typeof obj !== 'object') return obj;

	if (obj instanceof Date) return new Date(obj.getTime()) as unknown as T;
	if (obj instanceof Array) return obj.map(item => deepClone(item)) as unknown as T;
	if (obj instanceof RegExp) return new RegExp(obj) as unknown as T;

	if (typeof obj === 'object')
	{
		const cloned = {} as T;
		for (const key in obj)
		{
			if (obj.hasOwnProperty(key))
			{
				cloned[key] = deepClone(obj[key]);
			}
		}
		return cloned;
	}

	return obj;
}

/**
 * Deep merge objects
 */
function deepMerge<T extends Record<string, any>>(target: T, ...sources: Partial<T>[]): T
{
	if (!sources.length) return target;

	const source = sources.shift();
	if (!source) return deepMerge(target, ...sources);

	if (isObject(target) && isObject(source))
	{
		for (const key in source)
		{
			if (isObject(source[key]))
			{
				if (!target[key]) Object.assign(target, { [key]: {} });
				deepMerge(target[key], source[key]);
			}
			else
			{
				Object.assign(target, { [key]: source[key] });
			}
		}
	}

	return deepMerge(target, ...sources);
}

/**
 * Check if value is an object
 */
function isObject(item: any): item is Record<string, any>
{
	return item && typeof item === 'object' && !Array.isArray(item);
}

/**
 * Get nested property value
 */
function get<T>(obj: any, path: string, defaultValue?: T): T | undefined
{
	const keys = path.split('.');
	let result = obj;

	for (const key of keys)
	{
		if (result === null || result === undefined || typeof result !== 'object')
		{
			return defaultValue;
		}
		result = result[key];
	}

	return result !== undefined ? result : defaultValue;
}

/**
 * Set nested property value
 */
function set<T extends Record<string, any>>(obj: T, path: string, value: any): T
{
	const keys = path.split('.');
	let current = obj;

	for (let i = 0; i < keys.length - 1; i++)
	{
		const key = keys[i];
		if (!(key in current) || !isObject(current[key]))
		{
			current[key] = {};
		}
		current = current[key];
	}

	current[keys[keys.length - 1]] = value;
	return obj;
}

/**
 * Check if object has nested property
 */
function has(obj: any, path: string): boolean
{
	const keys = path.split('.');
	let current = obj;

	for (const key of keys)
	{
		if (current === null || current === undefined || typeof current !== 'object' || !(key in current))
		{
			return false;
		}
		current = current[key];
	}

	return true;
}

/**
 * Delete nested property
 */
function unset<T extends Record<string, any>>(obj: T, path: string): boolean
{
	const keys = path.split('.');
	let current = obj;

	for (let i = 0; i < keys.length - 1; i++)
	{
		const key = keys[i];
		if (!(key in current) || !isObject(current[key]))
		{
			return false;
		}
		current = current[key];
	}

	const lastKey = keys[keys.length - 1];
	if (lastKey in current)
	{
		delete current[lastKey];
		return true;
	}

	return false;
}

/**
 * Pick specified properties from object
 */
function pick<T extends Record<string, any>, K extends keyof T>(obj: T, keys: K[]): Pick<T, K>
{
	const result = {} as Pick<T, K>;

	for (const key of keys)
	{
		if (key in obj)
		{
			result[key] = obj[key];
		}
	}

	return result;
}

/**
 * Omit specified properties from object
 */
function omit<T extends Record<string, any>, K extends keyof T>(obj: T, keys: K[]): Omit<T, K>
{
	const result = { ...obj } as any;

	for (const key of keys)
	{
		delete result[key];
	}

	return result;
}

/**
 * Transform object values
 */
function mapValues<T extends Record<string, any>, R>(
	obj: T,
	mapper: (value: T[keyof T], key: keyof T) => R,
): Record<keyof T, R>
{
	const result = {} as Record<keyof T, R>;

	for (const key in obj)
	{
		if (obj.hasOwnProperty(key))
		{
			result[key] = mapper(obj[key], key);
		}
	}

	return result;
}

/**
 * Transform object keys
 */
function mapKeys<T extends Record<string, any>>(
	obj: T,
	mapper: (key: keyof T, value: T[keyof T]) => string,
): Record<string, T[keyof T]>
{
	const result: Record<string, T[keyof T]> = {};

	for (const key in obj)
	{
		if (obj.hasOwnProperty(key))
		{
			const newKey = mapper(key, obj[key]);
			result[newKey] = obj[key];
		}
	}

	return result;
}

/**
 * Filter object properties
 */
function filterObject<T extends Record<string, any>>(
	obj: T,
	predicate: (value: T[keyof T], key: keyof T) => boolean,
): Partial<T>
{
	const result: Partial<T> = {};

	for (const key in obj)
	{
		if (obj.hasOwnProperty(key) && predicate(obj[key], key))
		{
			result[key] = obj[key];
		}
	}

	return result;
}

/**
 * Invert object (swap keys and values)
 */
function invert<T extends Record<string, string | number>>(obj: T): Record<string, keyof T>
{
	const result: Record<string, keyof T> = {};

	for (const key in obj)
	{
		if (obj.hasOwnProperty(key))
		{
			result[String(obj[key])] = key;
		}
	}

	return result;
}

/**
 * Flatten nested object
 */
function flatten(obj: Record<string, any>, prefix = '', separator = '.'): Record<string, any>
{
	const flattened: Record<string, any> = {};

	for (const key in obj)
	{
		if (obj.hasOwnProperty(key))
		{
			const newKey = prefix ? `${prefix}${separator}${key}` : key;

			if (isObject(obj[key]) && !Array.isArray(obj[key]))
			{
				Object.assign(flattened, flatten(obj[key], newKey, separator));
			}
			else
			{
				flattened[newKey] = obj[key];
			}
		}
	}

	return flattened;
}

/**
 * Unflatten object
 */
function unflatten(obj: Record<string, any>, separator = '.'): Record<string, any>
{
	const result: Record<string, any> = {};

	for (const key in obj)
	{
		if (obj.hasOwnProperty(key))
		{
			set(result, key.replace(new RegExp(`\\${separator}`, 'g'), '.'), obj[key]);
		}
	}

	return result;
}

/**
 * Get all paths in object
 */
function paths(obj: Record<string, any>, prefix = ''): string[]
{
	const allPaths: string[] = [];

	for (const key in obj)
	{
		if (obj.hasOwnProperty(key))
		{
			const currentPath = prefix ? `${prefix}.${key}` : key;
			allPaths.push(currentPath);

			if (isObject(obj[key]))
			{
				allPaths.push(...paths(obj[key], currentPath));
			}
		}
	}

	return allPaths;
}

/**
 * Check if objects are deeply equal
 */
function isEqual(obj1: any, obj2: any): boolean
{
	if (obj1 === obj2) return true;

	if (obj1 == null || obj2 == null) return obj1 === obj2;

	if (typeof obj1 !== typeof obj2) return false;

	if (typeof obj1 !== 'object') return obj1 === obj2;

	if (Array.isArray(obj1) !== Array.isArray(obj2)) return false;

	if (Array.isArray(obj1))
	{
		if (obj1.length !== obj2.length) return false;

		for (let i = 0; i < obj1.length; i++)
		{
			if (!isEqual(obj1[i], obj2[i])) return false;
		}

		return true;
	}

	const keys1 = Object.keys(obj1);
	const keys2 = Object.keys(obj2);

	if (keys1.length !== keys2.length) return false;

	for (const key of keys1)
	{
		if (!keys2.includes(key)) return false;
		if (!isEqual(obj1[key], obj2[key])) return false;
	}

	return true;
}

/**
 * Get object size (number of properties)
 */
function size(obj: Record<string, any>): number
{
	return Object.keys(obj).length;
}

/**
 * Check if object is empty
 */
function isEmpty(obj: Record<string, any>): boolean
{
	return size(obj) === 0;
}

/**
 * Compact object (remove undefined/null values)
 */
function compact<T extends Record<string, any>>(obj: T): Partial<T>
{
	return filterObject(obj, value => value != null);
}

/**
 * Default values for object properties
 */
function defaults<T extends Record<string, any>>(obj: T, defaultValues: Partial<T>): T
{
	const result = { ...obj };

	for (const key in defaultValues)
	{
		if (defaultValues.hasOwnProperty(key) && !(key in result))
		{
			result[key] = defaultValues[key]!;
		}
	}

	return result;
}

/**
 * Group array of objects by property
 */
function groupBy<T>(array: T[], keyFn: (item: T) => string): Record<string, T[]>
{
	return array.reduce((groups, item) =>
	{
		const key = keyFn(item);
		if (!groups[key])
		{
			groups[key] = [];
		}
		groups[key].push(item);
		return groups;
	}, {} as Record<string, T[]>);
}

/**
 * Create object from key-value pairs
 */
function fromPairs<T>(pairs: Array<[string, T]>): Record<string, T>
{
	const result: Record<string, T> = {};

	for (const [key, value] of pairs)
	{
		result[key] = value;
	}

	return result;
}

/**
 * Convert object to key-value pairs
 */
function toPairs<T>(obj: Record<string, T>): Array<[string, T]>
{
	return Object.entries(obj);
}

/**
 * Rename object keys
 */
function renameKeys<T extends Record<string, any>>(
	obj: T,
	keyMap: Record<keyof T, string>,
): Record<string, T[keyof T]>
{
	const result: Record<string, T[keyof T]> = {};

	for (const key in obj)
	{
		if (obj.hasOwnProperty(key))
		{
			const newKey = keyMap[key] || key;
			result[newKey] = obj[key];
		}
	}

	return result;
}

export {
	deepClone,
	deepMerge,
	isObject,
	get,
	set,
	has,
	unset,
	pick,
	omit,
	mapValues,
	mapKeys,
	filterObject,
	invert,
	flatten,
	unflatten,
	paths,
	isEqual,
	size,
	isEmpty,
	compact,
	defaults,
	groupBy,
	fromPairs,
	toPairs,
	renameKeys,
};
