/**
 * Validation Utilities
 * Extended from shared module and consolidated from all services
 */

import * as <PERSON><PERSON> from 'joi';
import xss from 'xss';

type ValidationResult<T> = {
	valid: boolean;
	data?: T;
	errors?: string[];
};

type DomainValidationOptions = {
	allowSubdomains?: boolean;
	allowInternational?: boolean;
	maxLength?: number;
};

type UrlValidationOptions = {
	allowedProtocols?: string[];
	requireTLD?: boolean;
	allowLocalhost?: boolean;
};

/**
 * Enhanced domain validation with options
 */
function validateDomainWithOptions(domain: string, options: DomainValidationOptions = {}): ValidationResult<string>
{
	const {
		allowSubdomains = true,
		allowInternational = true,
		maxLength = 253,
	} = options;

	const errors: string[] = [];

	if (!domain || typeof domain !== 'string')
	{
		errors.push('Domain must be a non-empty string');
		return { valid: false, errors };
	}

	if (domain.length > maxLength)
	{
		errors.push(`Domain length cannot exceed ${maxLength} characters`);
	}

	// Basic format validation
	const domainRegex = allowInternational
		? /^[a-zA-Z0-9\u00a1-\uffff]([a-zA-Z0-9\u00a1-\uffff-]{0,61}[a-zA-Z0-9\u00a1-\uffff])?(\.[a-zA-Z0-9\u00a1-\uffff]([a-zA-Z0-9\u00a1-\uffff-]{0,61}[a-zA-Z0-9\u00a1-\uffff])?)*$/
		: /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;

	if (!domainRegex.test(domain))
	{
		errors.push('Invalid domain format');
	}

	// Check subdomain restrictions
	if (!allowSubdomains && domain.split('.').length > 2)
	{
		errors.push('Subdomains are not allowed');
	}

	// Check for valid TLD
	const parts = domain.split('.');
	if (parts.length < 2)
	{
		errors.push('Domain must have at least one dot');
	}

	const tld = parts[parts.length - 1];
	if (tld.length < 2)
	{
		errors.push('TLD must be at least 2 characters');
	}

	return {
		valid: errors.length === 0,
		data: errors.length === 0 ? domain.toLowerCase() : undefined,
		errors: errors.length > 0 ? errors : undefined,
	};
}

/**
 * Enhanced URL validation with options
 */
function validateUrlWithOptions(url: string, options: UrlValidationOptions = {}): ValidationResult<string>
{
	const {
		allowedProtocols = ['http', 'https'],
		requireTLD = true,
		allowLocalhost = false,
	} = options;

	const errors: string[] = [];

	if (!url || typeof url !== 'string')
	{
		errors.push('URL must be a non-empty string');
		return { valid: false, errors };
	}

	try
	{
		const parsed = new URL(url);

		// Check protocol
		const protocol = parsed.protocol.slice(0, -1); // Remove trailing colon
		if (!allowedProtocols.includes(protocol))
		{
			errors.push(`Protocol must be one of: ${allowedProtocols.join(', ')}`);
		}

		// Check hostname
		const hostname = parsed.hostname;
		if (!hostname)
		{
			errors.push('URL must have a hostname');
		}
		else
		{
			// Check localhost restriction
			if (!allowLocalhost && (hostname === 'localhost' || hostname.startsWith('127.') || hostname.startsWith('192.168.') || hostname.startsWith('10.')))
			{
				errors.push('Localhost and private IPs are not allowed');
			}

			// Check TLD requirement
			if (requireTLD && !hostname.includes('.'))
			{
				errors.push('URL must have a valid TLD');
			}
		}
	}
	catch (error)
	{
		errors.push('Invalid URL format');
	}

	return {
		valid: errors.length === 0,
		data: errors.length === 0 ? url : undefined,
		errors: errors.length > 0 ? errors : undefined,
	};
}

/**
 * Validate crawl job data
 */
function validateCrawlJobData(jobData: any): ValidationResult<any>
{
	const schema = Joi.object({
		domain: Joi.string().domain().required(),
		crawlType: Joi.string().valid('full', 'quick', 'security', 'performance').default('full'),
		priority: Joi.string().valid('high', 'medium', 'low').default('medium'),
		retryCount: Joi.number().integer().min(0).default(0),
		maxRetries: Joi.number().integer().min(1).max(10).default(3),
		userAgent: Joi.string().optional(),
		timeout: Joi.number().integer().min(1000).max(300000).default(30000),
		metadata: Joi.object().optional(),
	});

	const { error, value } = schema.validate(jobData, {
		abortEarly: false,
		stripUnknown: true,
		convert: true,
	});

	if (error)
	{
		return {
			valid: false,
			errors: error.details.map(detail => detail.message),
		};
	}

	return {
		valid: true,
		data: value,
	};
}

/**
 * Validate ranking data
 */
function validateRankingData(rankingData: any): ValidationResult<any>
{
	const schema = Joi.object({
		domain: Joi.string().domain().required(),
		globalRank: Joi.number().integer().min(1).allow(null),
		categoryRank: Joi.number().integer().min(1).allow(null),
		category: Joi.string().optional(),
		overallScore: Joi.number().min(0).max(1).required(),
		performanceScore: Joi.number().min(0).max(1).default(0),
		securityScore: Joi.number().min(0).max(1).default(0),
		seoScore: Joi.number().min(0).max(1).default(0),
		technicalScore: Joi.number().min(0).max(1).default(0),
		backlinkScore: Joi.number().min(0).max(1).default(0),
		trafficEstimate: Joi.number().integer().min(0).default(0),
		lastUpdated: Joi.date().default(() => new Date()),
	});

	const { error, value } = schema.validate(rankingData, {
		abortEarly: false,
		stripUnknown: true,
		convert: true,
	});

	if (error)
	{
		return {
			valid: false,
			errors: error.details.map(detail => detail.message),
		};
	}

	return {
		valid: true,
		data: value,
	};
}

/**
 * Validate configuration object
 */
function validateConfigurationData(config: any): ValidationResult<any>
{
	const schema = Joi.object({
		service: Joi.object({
			name: Joi.string().required(),
			port: Joi.number().integer().min(1).max(65535).required(),
			environment: Joi.string().valid('development', 'production', 'test').required(),
			logLevel: Joi.string().valid('error', 'warn', 'info', 'debug').required(),
		}).required(),
		database: Joi.object({
			scylla: Joi.object({
				hosts: Joi.array().items(Joi.string()).min(1).required(),
				keyspace: Joi.string().required(),
				localDataCenter: Joi.string().required(),
			}).required(),
			maria: Joi.object({
				host: Joi.string().required(),
				port: Joi.number().integer().min(1).max(65535).required(),
				user: Joi.string().required(),
				database: Joi.string().required(),
			}).required(),
			redis: Joi.object({
				host: Joi.string().required(),
				port: Joi.number().integer().min(1).max(65535).required(),
				db: Joi.number().integer().min(0).required(),
			}).required(),
		}).required(),
		crawler: Joi.object({
			timeout: Joi.number().integer().min(1000).required(),
			maxRetries: Joi.number().integer().min(1).required(),
			rateLimit: Joi.number().integer().min(1).required(),
		}).required(),
		ranking: Joi.object({
			weights: Joi.object({
				performance: Joi.number().min(0).max(1).required(),
				security: Joi.number().min(0).max(1).required(),
				seo: Joi.number().min(0).max(1).required(),
				technical: Joi.number().min(0).max(1).required(),
				backlinks: Joi.number().min(0).max(1).required(),
			}).required(),
		}).required(),
	});

	const { error, value } = schema.validate(config, {
		abortEarly: false,
		stripUnknown: true,
		convert: true,
	});

	if (error)
	{
		return {
			valid: false,
			errors: error.details.map(detail => detail.message),
		};
	}

	// Additional validation for ranking weights
	const weights = value.ranking.weights;
	const sum = weights.performance + weights.security + weights.seo + weights.technical + weights.backlinks;
	if (Math.abs(sum - 1.0) > 0.01)
	{
		return {
			valid: false,
			errors: ['Ranking weights must sum to 1.0'],
		};
	}

	return {
		valid: true,
		data: value,
	};
}

/**
 * Sanitize input string
 */
function sanitizeInput(input: string, options: { allowHtml?: boolean; maxLength?: number } = {}): string
{
	const { allowHtml = false, maxLength = 1000 } = options;

	if (!input || typeof input !== 'string') return '';

	// Truncate if too long
	let sanitized = input.length > maxLength ? input.substring(0, maxLength) : input;

	if (!allowHtml)
	{
		// Remove all HTML tags and dangerous content
		sanitized = xss(sanitized, {
			whiteList: {}, // Remove all tags
			stripIgnoreTag: true,
			stripIgnoreTagBody: ['script', 'style'],
			onTagAttr: () => '',
		});
	}
	else
	{
		// Allow safe HTML tags only
		sanitized = xss(sanitized, {
			whiteList: {
				p: [],
				br: [],
				strong: [],
				em: [],
				u: [],
				b: [],
				i: [],
			},
			stripIgnoreTag: true,
			stripIgnoreTagBody: ['script', 'style'],
		});
	}

	// Remove dangerous protocols
	const disallowedSchemes = ['javascript', 'vbscript', 'data'];
	const schemePattern = new RegExp(`\\b(?:${disallowedSchemes.join('|')})\\s*:`, 'gi');
	sanitized = sanitized.replace(schemePattern, '');

	return sanitized.trim();
}

/**
 * Validate and sanitize search query
 */
function validateSearchQuery(query: string): ValidationResult<string>
{
	const errors: string[] = [];

	if (!query || typeof query !== 'string')
	{
		errors.push('Query must be a non-empty string');
		return { valid: false, errors };
	}

	if (query.length > 1000)
	{
		errors.push('Query cannot exceed 1000 characters');
	}

	// Check for dangerous patterns
	const dangerousPatterns = [
		/<script/i,
		/javascript:/i,
		/vbscript:/i,
		/data:/i,
		/on\w+\s*=/i,
		/eval\s*\(/i,
		/SELECT.*FROM/i,
		/UPDATE.*SET/i,
		/DELETE.*FROM/i,
		/DROP.*TABLE/i,
		/INSERT.*INTO/i,
	];

	for (const pattern of dangerousPatterns)
	{
		if (pattern.test(query))
		{
			errors.push('Query contains potentially dangerous content');
			break;
		}
	}

	if (errors.length > 0)
	{
		return { valid: false, errors };
	}

	const sanitized = sanitizeInput(query, { allowHtml: false, maxLength: 1000 });

	return {
		valid: true,
		data: sanitized,
	};
}

/**
 * Validate IP address
 */
function validateIpAddress(ip: string): ValidationResult<string>
{
	const errors: string[] = [];

	if (!ip || typeof ip !== 'string')
	{
		errors.push('IP address must be a non-empty string');
		return { valid: false, errors };
	}

	// IPv4 validation
	const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;

	// IPv6 validation (simplified)
	const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$/;

	if (!ipv4Regex.test(ip) && !ipv6Regex.test(ip))
	{
		errors.push('Invalid IP address format');
	}

	return {
		valid: errors.length === 0,
		data: errors.length === 0 ? ip : undefined,
		errors: errors.length > 0 ? errors : undefined,
	};
}

/**
 * Validate email address
 */
function validateEmailAddress(email: string): ValidationResult<string>
{
	const errors: string[] = [];

	if (!email || typeof email !== 'string')
	{
		errors.push('Email must be a non-empty string');
		return { valid: false, errors };
	}

	const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
	if (!emailRegex.test(email))
	{
		errors.push('Invalid email format');
	}

	if (email.length > 254)
	{
		errors.push('Email cannot exceed 254 characters');
	}

	return {
		valid: errors.length === 0,
		data: errors.length === 0 ? email.toLowerCase() : undefined,
		errors: errors.length > 0 ? errors : undefined,
	};
}

/**
 * Check if value is a valid ranking score (0-1)
 */
function isValidRankingScore(score: any): boolean
{
	return typeof score === 'number' && score >= 0 && score <= 1 && !Number.isNaN(score);
}

/**
 * Validate batch of domains
 */
function validateDomainBatch(domains: string[]): ValidationResult<string[]>
{
	const errors: string[] = [];
	const validDomains: string[] = [];

	if (!Array.isArray(domains))
	{
		errors.push('Domains must be an array');
		return { valid: false, errors };
	}

	if (domains.length === 0)
	{
		errors.push('Domain array cannot be empty');
		return { valid: false, errors };
	}

	if (domains.length > 1000)
	{
		errors.push('Cannot process more than 1000 domains at once');
		return { valid: false, errors };
	}

	for (let i = 0; i < domains.length; i++)
	{
		const domainResult = validateDomainWithOptions(domains[i]);
		if (domainResult.valid && domainResult.data)
		{
			validDomains.push(domainResult.data);
		}
		else
		{
			errors.push(`Domain ${i + 1}: ${domainResult.errors?.join(', ')}`);
		}
	}

	return {
		valid: errors.length === 0,
		data: validDomains,
		errors: errors.length > 0 ? errors : undefined,
	};
}

export type {
	ValidationResult,
	DomainValidationOptions,
	UrlValidationOptions,
};

export {
	validateDomainWithOptions,
	validateUrlWithOptions,
	validateCrawlJobData,
	validateRankingData,
	validateConfigurationData,
	sanitizeInput,
	validateSearchQuery,
	validateIpAddress,
	validateEmailAddress,
	isValidRankingScore,
	validateDomainBatch,
};
