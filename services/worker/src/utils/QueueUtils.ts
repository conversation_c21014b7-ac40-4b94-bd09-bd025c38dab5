/**
 * Queue Utilities
 * Helper functions for queue operations and management
 */

import { logger as sharedLogger, IdGenerator } from '@shared';

const logger = sharedLogger.getLogger('QueueUtils');

type QueueItem<T = any> = {
	id: string;
	data: T;
	priority: number;
	timestamp: number;
	retryCount: number;
	maxRetries: number;
};

type QueueStats = {
	size: number;
	processing: number;
	completed: number;
	failed: number;
	averageProcessingTime: number;
};

/**
 * In-memory priority queue
 */
class PriorityQueue<T = any>
{
	private items: QueueItem<T>[];
	private processing: Set<string>;
	private stats: {
		completed: number;
		failed: number;
		totalProcessingTime: number;
	};

	constructor()
	{
		this.items = [];
		this.processing = new Set();
		this.stats = {
			completed: 0,
			failed: 0,
			totalProcessingTime: 0,
		};
	}

	/**
	 * Add item to queue
	 */
	enqueue(data: T, priority = 0, maxRetries = 3): string
	{
		const id = this.generateId();
		const item: QueueItem<T> = {
			id,
			data,
			priority,
			timestamp: Date.now(),
			retryCount: 0,
			maxRetries,
		};

		// Insert in priority order (higher priority first)
		let inserted = false;
		for (let i = 0; i < this.items.length; i++)
		{
			if (this.items[i].priority < priority)
			{
				this.items.splice(i, 0, item);
				inserted = true;
				break;
			}
		}

		if (!inserted)
		{
			this.items.push(item);
		}

		return id;
	}

	/**
	 * Remove and return highest priority item
	 */
	dequeue(): QueueItem<T> | undefined
	{
		const item = this.items.shift();
		if (item)
		{
			this.processing.add(item.id);
		}
		return item;
	}

	/**
	 * Peek at highest priority item without removing
	 */
	peek(): QueueItem<T> | undefined
	{
		return this.items[0];
	}

	/**
	 * Mark item as completed
	 */
	complete(id: string, processingTime: number): void
	{
		this.processing.delete(id);
		this.stats.completed++;
		this.stats.totalProcessingTime += processingTime;
	}

	/**
	 * Mark item as failed and optionally retry
	 */
	fail(id: string, processingTime: number, retry = true): boolean
	{
		this.processing.delete(id);
		this.stats.failed++;
		this.stats.totalProcessingTime += processingTime;

		if (retry)
		{
			// Find the original item data (this is simplified)
			// In a real implementation, you'd store failed items separately
			return false; // Indicate retry not implemented in this simple version
		}

		return true;
	}

	/**
	 * Get queue size
	 */
	size(): number
	{
		return this.items.length;
	}

	/**
	 * Check if queue is empty
	 */
	isEmpty(): boolean
	{
		return this.items.length === 0;
	}

	/**
	 * Get queue statistics
	 */
	getStats(): QueueStats
	{
		const totalCompleted = this.stats.completed;
		const averageProcessingTime = totalCompleted > 0
			? this.stats.totalProcessingTime / totalCompleted
			: 0;

		return {
			size: this.items.length,
			processing: this.processing.size,
			completed: this.stats.completed,
			failed: this.stats.failed,
			averageProcessingTime,
		};
	}

	/**
	 * Clear all items
	 */
	clear(): void
	{
		this.items = [];
		this.processing.clear();
	}

	/**
	 * Get all items (for debugging)
	 */
	getItems(): QueueItem<T>[]
	{
		return [...this.items];
	}

	/**
	 * Generate unique ID
	 */
	private generateId(): string
	{
		return IdGenerator.generate({ useTimestamp: true });
	}
}

/**
 * FIFO Queue (First In, First Out)
 */
class FIFOQueue<T = any>
{
	private items: T[];

	constructor()
	{
		this.items = [];
	}

	/**
	 * Add item to end of queue
	 */
	enqueue(item: T): void
	{
		this.items.push(item);
	}

	/**
	 * Remove and return first item
	 */
	dequeue(): T | undefined
	{
		return this.items.shift();
	}

	/**
	 * Peek at first item without removing
	 */
	peek(): T | undefined
	{
		return this.items[0];
	}

	/**
	 * Get queue size
	 */
	size(): number
	{
		return this.items.length;
	}

	/**
	 * Check if queue is empty
	 */
	isEmpty(): boolean
	{
		return this.items.length === 0;
	}

	/**
	 * Clear all items
	 */
	clear(): void
	{
		this.items = [];
	}
}

/**
 * LIFO Queue (Last In, First Out) - Stack
 */
class LIFOQueue<T = any>
{
	private items: T[];

	constructor()
	{
		this.items = [];
	}

	/**
	 * Add item to top of stack
	 */
	push(item: T): void
	{
		this.items.push(item);
	}

	/**
	 * Remove and return top item
	 */
	pop(): T | undefined
	{
		return this.items.pop();
	}

	/**
	 * Peek at top item without removing
	 */
	peek(): T | undefined
	{
		return this.items[this.items.length - 1];
	}

	/**
	 * Get stack size
	 */
	size(): number
	{
		return this.items.length;
	}

	/**
	 * Check if stack is empty
	 */
	isEmpty(): boolean
	{
		return this.items.length === 0;
	}

	/**
	 * Clear all items
	 */
	clear(): void
	{
		this.items = [];
	}
}

/**
 * Circular queue with fixed size
 */
class CircularQueue<T = any>
{
	private items: (T | undefined)[];
	private head: number;
	private tail: number;
	private count: number;
	private capacity: number;

	constructor(capacity: number)
	{
		this.capacity = capacity;
		this.items = new Array(capacity);
		this.head = 0;
		this.tail = 0;
		this.count = 0;
	}

	/**
	 * Add item to queue
	 */
	enqueue(item: T): boolean
	{
		if (this.isFull())
		{
			return false;
		}

		this.items[this.tail] = item;
		this.tail = (this.tail + 1) % this.capacity;
		this.count++;

		return true;
	}

	/**
	 * Remove and return first item
	 */
	dequeue(): T | undefined
	{
		if (this.isEmpty())
		{
			return undefined;
		}

		const item = this.items[this.head];
		this.items[this.head] = undefined;
		this.head = (this.head + 1) % this.capacity;
		this.count--;

		return item;
	}

	/**
	 * Peek at first item without removing
	 */
	peek(): T | undefined
	{
		return this.isEmpty() ? undefined : this.items[this.head];
	}

	/**
	 * Get queue size
	 */
	size(): number
	{
		return this.count;
	}

	/**
	 * Check if queue is empty
	 */
	isEmpty(): boolean
	{
		return this.count === 0;
	}

	/**
	 * Check if queue is full
	 */
	isFull(): boolean
	{
		return this.count === this.capacity;
	}

	/**
	 * Clear all items
	 */
	clear(): void
	{
		this.items.fill(undefined);
		this.head = 0;
		this.tail = 0;
		this.count = 0;
	}
}

/**
 * Delayed queue - items become available after a delay
 */
class DelayedQueue<T = any>
{
	private items: Array<{ item: T; availableAt: number }>;

	constructor()
	{
		this.items = [];
	}

	/**
	 * Add item with delay
	 */
	enqueue(item: T, delayMs: number): void
	{
		const availableAt = Date.now() + delayMs;

		// Insert in sorted order by availability time
		let inserted = false;
		for (let i = 0; i < this.items.length; i++)
		{
			if (this.items[i].availableAt > availableAt)
			{
				this.items.splice(i, 0, { item, availableAt });
				inserted = true;
				break;
			}
		}

		if (!inserted)
		{
			this.items.push({ item, availableAt });
		}
	}

	/**
	 * Remove and return available item
	 */
	dequeue(): T | undefined
	{
		const now = Date.now();

		if (this.items.length > 0 && this.items[0].availableAt <= now)
		{
			return this.items.shift()!.item;
		}

		return undefined;
	}

	/**
	 * Get next available time
	 */
	getNextAvailableTime(): number | undefined
	{
		return this.items.length > 0 ? this.items[0].availableAt : undefined;
	}

	/**
	 * Get number of available items
	 */
	getAvailableCount(): number
	{
		const now = Date.now();
		let count = 0;

		for (const item of this.items)
		{
			if (item.availableAt <= now)
			{
				count++;
			}
			else
			{
				break;
			}
		}

		return count;
	}

	/**
	 * Get total queue size
	 */
	size(): number
	{
		return this.items.length;
	}

	/**
	 * Check if queue is empty
	 */
	isEmpty(): boolean
	{
		return this.items.length === 0;
	}

	/**
	 * Clear all items
	 */
	clear(): void
	{
		this.items = [];
	}
}

/**
 * Rate-limited queue processor
 */
class RateLimitedProcessor<T = any>
{
	private queue: FIFOQueue<T>;
	private processor: (item: T) => Promise<void>;
	private rateLimit: number; // items per second
	private lastProcessTime: number;
	private processing: boolean;

	constructor(processor: (item: T) => Promise<void>, rateLimit = 10)
	{
		this.queue = new FIFOQueue<T>();
		this.processor = processor;
		this.rateLimit = rateLimit;
		this.lastProcessTime = 0;
		this.processing = false;
	}

	/**
	 * Add item to queue
	 */
	enqueue(item: T): void
	{
		this.queue.enqueue(item);
		this.processQueue();
	}

	/**
	 * Process queue with rate limiting
	 */
	private async processQueue(): Promise<void>
	{
		if (this.processing || this.queue.isEmpty())
		{
			return;
		}

		this.processing = true;

		while (!this.queue.isEmpty())
		{
			const now = Date.now();
			const timeSinceLastProcess = now - this.lastProcessTime;
			const minInterval = 1000 / this.rateLimit; // ms between items

			if (timeSinceLastProcess < minInterval)
			{
				const delay = minInterval - timeSinceLastProcess;
				await this.sleep(delay);
			}

			const item = this.queue.dequeue();
			if (item)
			{
				try
				{
					await this.processor(item);
				}
				catch (error)
				{
					logger.error('Error processing queue item:', error);
				}

				this.lastProcessTime = Date.now();
			}
		}

		this.processing = false;
	}

	/**
	 * Get queue size
	 */
	size(): number
	{
		return this.queue.size();
	}

	/**
	 * Check if processing
	 */
	isProcessing(): boolean
	{
		return this.processing;
	}

	/**
	 * Sleep utility
	 */
	private sleep(ms: number): Promise<void>
	{
		return new Promise(resolve => setTimeout(resolve, ms));
	}
}

/**
 * Batch processor for queue items
 */
class BatchProcessor<T = any>
{
	private items: T[];
	private processor: (batch: T[]) => Promise<void>;
	private batchSize: number;
	private flushInterval: number;
	private timer?: NodeJS.Timeout;

	constructor(
		processor: (batch: T[]) => Promise<void>,
		batchSize = 10,
		flushInterval = 5000,
	)
	{
		this.items = [];
		this.processor = processor;
		this.batchSize = batchSize;
		this.flushInterval = flushInterval;
		this.startTimer();
	}

	/**
	 * Add item to batch
	 */
	add(item: T): void
	{
		this.items.push(item);

		if (this.items.length >= this.batchSize)
		{
			this.flush();
		}
	}

	/**
	 * Process current batch
	 */
	async flush(): Promise<void>
	{
		if (this.items.length === 0) return;

		const batch = this.items.splice(0);

		try
		{
			await this.processor(batch);
		}
		catch (error)
		{
			logger.error('Error processing batch:', error);
		}

		this.resetTimer();
	}

	/**
	 * Get current batch size
	 */
	size(): number
	{
		return this.items.length;
	}

	/**
	 * Stop batch processor
	 */
	stop(): void
	{
		if (this.timer)
		{
			clearTimeout(this.timer);
			this.timer = undefined;
		}
	}

	/**
	 * Start flush timer
	 */
	private startTimer(): void
	{
		this.timer = setTimeout(() => this.flush(), this.flushInterval);
	}

	/**
	 * Reset flush timer
	 */
	private resetTimer(): void
	{
		if (this.timer)
		{
			clearTimeout(this.timer);
		}
		this.startTimer();
	}
}

export type { QueueItem, QueueStats };

export {
	PriorityQueue,
	FIFOQueue,
	LIFOQueue,
	CircularQueue,
	DelayedQueue,
	RateLimitedProcessor,
	BatchProcessor,
};
