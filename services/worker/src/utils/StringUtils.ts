/**
 * String Utilities
 * Helper functions for string manipulation and processing
 */

/**
 * Capitalize first letter of string
 */
function capitalize(str: string): string
{
	return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

/**
 * Convert string to title case
 */
function toTitleCase(str: string): string
{
	return str.replace(/\w\S*/g, txt =>
		txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase());
}

/**
 * Convert string to slug format
 */
function toSlug(str: string): string
{
	return str
		.toLowerCase()
		.trim()
		.replace(/[^\w\s-]/g, '') // Remove special characters
		.replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
		.replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
}

/**
 * Truncate string with ellipsis
 */
function truncate(str: string, maxLength: number, suffix = '...'): string
{
	if (str.length <= maxLength) return str;
	return str.substring(0, maxLength - suffix.length) + suffix;
}

/**
 * Truncate string at word boundary
 */
function truncateWords(str: string, maxWords: number, suffix = '...'): string
{
	const words = str.split(/\s+/);
	if (words.length <= maxWords) return str;
	return words.slice(0, maxWords).join(' ') + suffix;
}

/**
 * Remove HTML tags from string
 */
function stripHtml(str: string): string
{
	return str.replace(/<[^>]*>/g, '');
}

/**
 * Escape HTML entities
 */
function escapeHtml(str: string): string
{
	const entityMap: Record<string, string> = {
		'&': '&amp;',
		'<': '&lt;',
		'>': '&gt;',
		'"': '&quot;',
		"'": '&#39;',
		'/': '&#x2F;',
	};

	return str.replace(/[&<>"'\/]/g, s => entityMap[s]);
}

/**
 * Unescape HTML entities
 */
function unescapeHtml(str: string): string
{
	const entityMap: Record<string, string> = {
		'&amp;': '&',
		'&lt;': '<',
		'&gt;': '>',
		'&quot;': '"',
		'&#39;': "'",
		'&#x2F;': '/',
	};

	return str.replace(/&(amp|lt|gt|quot|#39|#x2F);/g, match => entityMap[match]);
}

/**
 * Count words in string
 */
function wordCount(str: string): number
{
	return str.trim().split(/\s+/).filter(word => word.length > 0).length;
}

/**
 * Count characters excluding whitespace
 */
function charCount(str: string, includeSpaces = true): number
{
	return includeSpaces ? str.length : str.replace(/\s/g, '').length;
}

/**
 * Extract email addresses from string
 */
function extractEmails(str: string): string[]
{
	const emailRegex = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g;
	return str.match(emailRegex) || [];
}

/**
 * Extract URLs from string
 */
function extractUrls(str: string): string[]
{
	const urlRegex = /https?:\/\/[^\s<>"{}|\\^`[\]]+/g;
	return str.match(urlRegex) || [];
}

/**
 * Extract phone numbers from string
 */
function extractPhoneNumbers(str: string): string[]
{
	const phoneRegex = /(\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})/g;
	return str.match(phoneRegex) || [];
}

/**
 * Remove extra whitespace
 */
function normalizeWhitespace(str: string): string
{
	return str.replace(/\s+/g, ' ').trim();
}

/**
 * Pad string to specified length
 */
function pad(str: string, length: number, padChar = ' ', padLeft = false): string
{
	if (str.length >= length) return str;

	const padding = padChar.repeat(length - str.length);
	return padLeft ? padding + str : str + padding;
}

/**
 * Reverse string
 */
function reverse(str: string): string
{
	return str.split('').reverse().join('');
}

/**
 * Check if string is palindrome
 */
function isPalindrome(str: string): boolean
{
	const cleaned = str.toLowerCase().replace(/[^a-z0-9]/g, '');
	return cleaned === reverse(cleaned);
}

/**
 * Generate random string
 */
function randomString(length: number, charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'): string
{
	let result = '';
	for (let i = 0; i < length; i++)
	{
		result += charset.charAt(Math.floor(Math.random() * charset.length));
	}
	return result;
}

/**
 * Calculate string similarity (Levenshtein distance)
 */
function similarity(str1: string, str2: string): number
{
	const matrix: number[][] = [];

	for (let i = 0; i <= str2.length; i++)
	{
		matrix[i] = [i];
	}

	for (let j = 0; j <= str1.length; j++)
	{
		matrix[0][j] = j;
	}

	for (let i = 1; i <= str2.length; i++)
	{
		for (let j = 1; j <= str1.length; j++)
		{
			if (str2.charAt(i - 1) === str1.charAt(j - 1))
			{
				matrix[i][j] = matrix[i - 1][j - 1];
			}
			else
			{
				matrix[i][j] = Math.min(
					matrix[i - 1][j - 1] + 1, // substitution
					matrix[i][j - 1] + 1,     // insertion
					matrix[i - 1][j] + 1,     // deletion
				);
			}
		}
	}

	const maxLength = Math.max(str1.length, str2.length);
	return maxLength === 0 ? 1 : (maxLength - matrix[str2.length][str1.length]) / maxLength;
}

/**
 * Find common prefix of strings
 */
function commonPrefix(strings: string[]): string
{
	if (strings.length === 0) return '';
	if (strings.length === 1) return strings[0];

	let prefix = '';
	const firstString = strings[0];

	for (let i = 0; i < firstString.length; i++)
	{
		const char = firstString[i];

		if (strings.every(str => str[i] === char))
		{
			prefix += char;
		}
		else
		{
			break;
		}
	}

	return prefix;
}

/**
 * Find common suffix of strings
 */
function commonSuffix(strings: string[]): string
{
	if (strings.length === 0) return '';
	if (strings.length === 1) return strings[0];

	let suffix = '';
	const firstString = strings[0];

	for (let i = firstString.length - 1; i >= 0; i--)
	{
		const char = firstString[i];
		const position = firstString.length - 1 - i;

		if (strings.every(str => str[str.length - 1 - position] === char))
		{
			suffix = char + suffix;
		}
		else
		{
			break;
		}
	}

	return suffix;
}

/**
 * Replace multiple patterns in string
 */
function replaceMultiple(str: string, replacements: Record<string, string>): string
{
	let result = str;

	for (const [search, replace] of Object.entries(replacements))
	{
		result = result.replace(new RegExp(search, 'g'), replace);
	}

	return result;
}

/**
 * Insert string at position
 */
function insertAt(str: string, index: number, insertion: string): string
{
	return str.slice(0, index) + insertion + str.slice(index);
}

/**
 * Remove string at position
 */
function removeAt(str: string, index: number, length = 1): string
{
	return str.slice(0, index) + str.slice(index + length);
}

/**
 * Wrap text to specified width
 */
function wrapText(text: string, width: number): string[]
{
	const words = text.split(' ');
	const lines: string[] = [];
	let currentLine = '';

	for (const word of words)
	{
		if ((currentLine + word).length <= width)
		{
			currentLine += (currentLine ? ' ' : '') + word;
		}
		else
		{
			if (currentLine)
			{
				lines.push(currentLine);
			}
			currentLine = word;
		}
	}

	if (currentLine)
	{
		lines.push(currentLine);
	}

	return lines;
}

/**
 * Format template string with variables
 */
function template(str: string, variables: Record<string, any>): string
{
	return str.replace(/\{\{(\w+)\}\}/g, (match, key) =>
		variables.hasOwnProperty(key) ? String(variables[key]) : match);
}

/**
 * Check if string contains only ASCII characters
 */
function isAscii(str: string): boolean
{
	return /^[\x00-\x7F]*$/.test(str);
}

/**
 * Check if string is valid JSON
 */
function isValidJson(str: string): boolean
{
	try
	{
		JSON.parse(str);
		return true;
	}
	catch
	{
		return false;
	}
}

/**
 * Mask string (e.g., for sensitive data)
 */
function mask(str: string, visibleStart = 2, visibleEnd = 2, maskChar = '*'): string
{
	if (str.length <= visibleStart + visibleEnd)
	{
		return maskChar.repeat(str.length);
	}

	const start = str.substring(0, visibleStart);
	const end = str.substring(str.length - visibleEnd);
	const middle = maskChar.repeat(str.length - visibleStart - visibleEnd);

	return start + middle + end;
}

export {
	capitalize,
	toTitleCase,
	toSlug,
	truncate,
	truncateWords,
	stripHtml,
	escapeHtml,
	unescapeHtml,
	wordCount,
	charCount,
	extractEmails,
	extractUrls,
	extractPhoneNumbers,
	normalizeWhitespace,
	pad,
	reverse,
	isPalindrome,
	randomString,
	similarity,
	commonPrefix,
	commonSuffix,
	replaceMultiple,
	insertAt,
	removeAt,
	wrapText,
	template,
	isAscii,
	isValidJson,
	mask,
};
