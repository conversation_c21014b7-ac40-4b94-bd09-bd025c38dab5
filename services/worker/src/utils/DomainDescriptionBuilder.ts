/**
 * Domain Description Builder Utility
 * Extracted from crawler service
 */

import { domainValidator, DomainDescriptionCacheService, logger as sharedLogger } from '@shared';
import type { DomainDescriptionInterface, RedisClientWrapper } from '@shared';

type CrawlResultType =
{
	domain: string;
	crawlType: string;
	status: string;
	data: {
		dnsAnalysis?: {
			records: {
				A?: string[];
				AAAA?: string[];
				MX?: Array<{ priority: number; exchange: string }>;
				CNAME?: string[];
				TXT?: string[];
				NS?: string[];
			};
		};
		sslAnalysis?: {
			grade?: string;
		};
		homepageAnalysis?: {
			metaTags: {
				title?: string;
				description?: string;
			};
		};
		robotsAnalysis?: {
			sitemaps?: string[];
		};
	};
};

const logger = sharedLogger.getLogger('DomainDescriptionBuilder');

/**
 * Build domain description from crawl result data
 */
function buildDomainDescription(result: CrawlResultType): DomainDescriptionInterface
{
	const now = new Date().toISOString();

	return ({
		metadata: {
			domain: result.domain,
			tld: result.domain.split('.').pop() || '',
			status: 'active',
			category: { primary: 'uncategorized' },
		},
		overview: {},
		technical: {
			technologies: [],
			security: { sslGrade: result.data.sslAnalysis?.grade },
			dns: {
				a: result.data.dnsAnalysis?.records.A || [],
				aaaa: result.data.dnsAnalysis?.records.AAAA || [],
				mx: (result.data.dnsAnalysis?.records.MX || []).map((m: any) => `${m.priority} ${m.exchange}`),
				cname: result.data.dnsAnalysis?.records.CNAME || [],
				txt: result.data.dnsAnalysis?.records.TXT || [],
				ns: result.data.dnsAnalysis?.records.NS || [],
			},
		},
		seo: {
			title: result.data.homepageAnalysis?.metaTags.title,
			metaDescription: result.data.homepageAnalysis?.metaTags.description,
			sitemap: { present: (result.data.robotsAnalysis?.sitemaps?.length || 0) > 0 },
			robots: { present: !!result.data.robotsAnalysis, policy: 'mixed' },
		},
		reputation: {},
		ranking: {},
		compliance: {},
		crawl: {
			lastCrawled: now,
			crawlType: result.crawlType as any,
			errors: result.status === 'failed' ? 1 : 0,
		},
	});
}

/**
 * Extract TLD from domain
 */
function extractTLD(domain: string): string
{
	const parts = domain.split('.');
	return parts[parts.length - 1] || '';
}

/**
 * Extract subdomain from domain
 */
function extractSubdomain(domain: string): string
{
	const parts = domain.split('.');
	if (parts.length > 2)
	{
		return parts.slice(0, -2).join('.');
	}
	return '';
}

/**
 * Extract root domain from domain
 */
function extractRootDomain(domain: string): string
{
	const parts = domain.split('.');
	if (parts.length >= 2)
	{
		return parts.slice(-2).join('.');
	}
	return domain;
}

/**
 * Normalize domain name
 */
function normalizeDomain(domain: string): string
{
	return domain.toLowerCase().trim().replace(/^www\./, '');
}

// Removed isValidDomainFormat - now using shared domainValidator.isValidFormat()

/**
 * Get domain category from TLD
 */
function getDomainCategoryFromTLD(tld: string): string
{
	const commercialTLDs = ['com', 'biz', 'shop', 'store'];
	const organizationTLDs = ['org', 'ngo'];
	const educationTLDs = ['edu', 'ac'];
	const governmentTLDs = ['gov', 'mil'];
	const networkTLDs = ['net', 'io'];

	if (commercialTLDs.includes(tld)) return 'commercial';
	if (organizationTLDs.includes(tld)) return 'organization';
	if (educationTLDs.includes(tld)) return 'education';
	if (governmentTLDs.includes(tld)) return 'government';
	if (networkTLDs.includes(tld)) return 'technology';

	return 'other';
}

export type { CrawlResultType };

/**
 * Build and cache domain description from crawl result data
 */
async function buildAndCacheDomainDescription(
	result: CrawlResultType,
	cacheService: DomainDescriptionCacheService
): Promise<DomainDescriptionInterface>
{
	// Check cache first
	const cached = await cacheService.get(result.domain);
	if (cached)
	{
		logger.debug('Cache hit for domain description', { domain: result.domain });
		return cached;
	}

	logger.debug('Cache miss for domain description', { domain: result.domain });

	// Build fresh description
	const description = buildDomainDescription(result);

	// Cache the description
	await cacheService.set(result.domain, description);

	return description;
}

/**
 * Batch build and cache domain descriptions
 */
async function batchBuildAndCacheDomainDescriptions(
	results: CrawlResultType[],
	cacheService: DomainDescriptionCacheService
): Promise<Map<string, DomainDescriptionInterface>>
{
	const descriptions = new Map<string, DomainDescriptionInterface>();
	const domains = results.map(r => r.domain);

	// Try to get cached descriptions in batch
	const cached = await cacheService.getBatch(domains);

	// Separate cached and non-cached results
	const needBuilding: CrawlResultType[] = [];
	for (const result of results)
	{
		const cachedDesc = cached.get(result.domain);
		if (cachedDesc)
		{
			descriptions.set(result.domain, cachedDesc);
		}
		else
		{
			needBuilding.push(result);
		}
	}

	// Build descriptions for non-cached domains
	const newDescriptions = new Map<string, DomainDescriptionInterface>();
	for (const result of needBuilding)
	{
		const description = buildDomainDescription(result);
		newDescriptions.set(result.domain, description);
		descriptions.set(result.domain, description);
	}

	// Cache the new descriptions in batch
	if (newDescriptions.size > 0)
	{
		await cacheService.setBatch(newDescriptions);
	}

	logger.info('Batch build and cache completed', {
		total: results.length,
		cached: cached.size,
		built: newDescriptions.size,
	});

	return descriptions;
}

export {
	buildDomainDescription,
	buildAndCacheDomainDescription,
	batchBuildAndCacheDomainDescriptions,
	extractTLD,
	extractSubdomain,
	extractRootDomain,
	normalizeDomain,
	// isValidDomainFormat, // Removed - use domainValidator.isValidFormat() from @shared
	getDomainCategoryFromTLD,
};
