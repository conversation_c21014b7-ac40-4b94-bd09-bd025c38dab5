/**
 * Network Utilities
 * Network-related helper functions
 */

import * as dns from 'dns';
import { promisify } from 'util';

const dnsResolve = promisify(dns.resolve);
const dnsReverse = promisify(dns.reverse);
const dnsLookup = promisify(dns.lookup);

type NetworkInterface = {
	name: string;
	addresses: Array<{
		address: string;
		family: 'IPv4' | 'IPv6';
		internal: boolean;
	}>;
};

/**
 * Check if IP address is valid
 */
function isValidIP(ip: string): boolean
{
	const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
	const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$/;

	return ipv4Regex.test(ip) || ipv6Regex.test(ip);
}

/**
 * Check if IP is private/internal
 */
function isPrivateIP(ip: string): boolean
{
	// IPv4 private ranges
	const privateRanges = [
		/^10\./,
		/^172\.(1[6-9]|2[0-9]|3[0-1])\./,
		/^192\.168\./,
		/^127\./,
		/^169\.254\./, // Link-local
	];

	// IPv6 private ranges
	const ipv6PrivateRanges = [
		/^::1$/, // Loopback
		/^fc00:/, // Unique local
		/^fd00:/, // Unique local
		/^fe80:/, // Link-local
	];

	return privateRanges.some(range => range.test(ip)) ||
		   ipv6PrivateRanges.some(range => range.test(ip));
}

/**
 * Get IP version (4 or 6)
 */
function getIPVersion(ip: string): 4 | 6 | null
{
	if (/^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/.test(ip))
	{
		return 4;
	}

	if (/^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$/.test(ip))
	{
		return 6;
	}

	return null;
}

/**
 * Resolve domain to IP addresses
 */
async function resolveDomain(domain: string, recordType: 'A' | 'AAAA' | 'CNAME' | 'MX' | 'TXT' | 'NS' = 'A'): Promise<string[]>
{
	try
	{
		const addresses = await dnsResolve(domain, recordType);
		return Array.isArray(addresses) ? addresses : [addresses];
	}
	catch (error)
	{
		throw new Error(`Failed to resolve ${domain}: ${error instanceof Error ? error.message : 'Unknown error'}`);
	}
}

/**
 * Reverse DNS lookup
 */
async function reverseDNS(ip: string): Promise<string[]>
{
	try
	{
		return await dnsReverse(ip);
	}
	catch (error)
	{
		throw new Error(`Failed to reverse lookup ${ip}: ${error instanceof Error ? error.message : 'Unknown error'}`);
	}
}

/**
 * Get all DNS records for a domain
 */
async function getAllDNSRecords(domain: string): Promise<{
	A: string[];
	AAAA: string[];
	CNAME: string[];
	MX: string[];
	TXT: string[];
	NS: string[];
}>
{
	const results = {
		A: [] as string[],
		AAAA: [] as string[],
		CNAME: [] as string[],
		MX: [] as string[],
		TXT: [] as string[],
		NS: [] as string[],
	};

	const recordTypes: Array<keyof typeof results> = ['A', 'AAAA', 'CNAME', 'MX', 'TXT', 'NS'];

	await Promise.allSettled(
		recordTypes.map(async (type) =>
		{
			try
			{
				results[type] = await resolveDomain(domain, type);
			}
			catch
			{
				// Ignore errors for individual record types
			}
		}),
	);

	return results;
}

/**
 * Check if port is open
 */
async function isPortOpen(host: string, port: number, timeout = 5000): Promise<boolean>
{
	return new Promise((resolve) =>
	{
		const net = require('net');
		const socket = new net.Socket();

		const onError = (): void =>
		{
			socket.destroy();
			resolve(false);
		};

		socket.setTimeout(timeout);
		socket.once('error', onError);
		socket.once('timeout', onError);

		socket.connect(port, host, () =>
		{
			socket.end();
			resolve(true);
		});
	});
}

/**
 * Ping host (simplified)
 */
async function ping(host: string, timeout = 5000): Promise<{ success: boolean; time?: number; error?: string }>
{
	const start = Date.now();

	try
	{
		await dnsLookup(host);
		const time = Date.now() - start;
		return { success: true, time };
	}
	catch (error)
	{
		return {
			success: false,
			error: error instanceof Error ? error.message : 'Unknown error',
		};
	}
}

/**
 * Get network interfaces
 */
function getNetworkInterfaces(): NetworkInterface[]
{
	const os = require('os');
	const interfaces = os.networkInterfaces();
	const result: NetworkInterface[] = [];

	for (const [name, addresses] of Object.entries(interfaces))
	{
		if (addresses)
		{
			result.push({
				name,
				addresses: addresses.map((addr: any) => ({
					address: addr.address,
					family: addr.family,
					internal: addr.internal,
				})),
			});
		}
	}

	return result;
}

/**
 * Get public IP address
 */
async function getPublicIP(): Promise<string>
{
	const https = require('https');

	return new Promise((resolve, reject) =>
	{
		const request = https.get('https://api.ipify.org', (response: any) =>
		{
			let data = '';

			response.on('data', (chunk: string) =>
			{
				data += chunk;
			});

			response.on('end', () =>
			{
				resolve(data.trim());
			});
		});

		request.on('error', reject);
		request.setTimeout(5000, () =>
		{
			request.destroy();
			reject(new Error('Request timeout'));
		});
	});
}

/**
 * Parse URL safely
 */
function parseURL(url: string): {
	protocol: string;
	hostname: string;
	port: number | null;
	pathname: string;
	search: string;
	hash: string;
} | null
{
	try
	{
		const parsed = new URL(url);

		return {
			protocol: parsed.protocol,
			hostname: parsed.hostname,
			port: parsed.port ? parseInt(parsed.port, 10) : null,
			pathname: parsed.pathname,
			search: parsed.search,
			hash: parsed.hash,
		};
	}
	catch
	{
		return null;
	}
}

/**
 * Validate URL format
 */
function isValidURL(url: string): boolean
{
	try
	{
		new URL(url);
		return true;
	}
	catch
	{
		return false;
	}
}

/**
 * Extract domain from URL
 */
function extractDomain(url: string): string | null
{
	const parsed = parseURL(url);
	return parsed ? parsed.hostname : null;
}

/**
 * Check if domain is reachable
 */
async function isDomainReachable(domain: string, timeout = 10000): Promise<boolean>
{
	try
	{
		// Try to resolve DNS first
		await resolveDomain(domain, 'A');

		// Try to connect to HTTP port
		const httpReachable = await isPortOpen(domain, 80, timeout / 2);
		const httpsReachable = await isPortOpen(domain, 443, timeout / 2);

		return httpReachable || httpsReachable;
	}
	catch
	{
		return false;
	}
}

/**
 * Get geolocation for IP (mock implementation)
 */
async function getIPGeolocation(ip: string): Promise<{
	country?: string;
	region?: string;
	city?: string;
	latitude?: number;
	longitude?: number;
} | null>
{
	// This is a mock implementation
	// In a real scenario, you would use a geolocation service
	if (isPrivateIP(ip))
	{
		return {
			country: 'Private Network',
			region: 'Local',
			city: 'Local',
		};
	}

	// Return null for public IPs as we don't have a real service
	return null;
}

/**
 * Calculate network latency
 */
async function measureLatency(host: string, samples = 3): Promise<{
	average: number;
	min: number;
	max: number;
	samples: number[];
}>
{
	const results: number[] = [];

	for (let i = 0; i < samples; i++)
	{
		const pingResult = await ping(host);
		if (pingResult.success && pingResult.time !== undefined)
		{
			results.push(pingResult.time);
		}
	}

	if (results.length === 0)
	{
		throw new Error(`Unable to measure latency to ${host}`);
	}

	const sum = results.reduce((a, b) => a + b, 0);

	return {
		average: sum / results.length,
		min: Math.min(...results),
		max: Math.max(...results),
		samples: results,
	};
}

/**
 * Check if URL is accessible
 */
async function isURLAccessible(url: string, timeout = 10000): Promise<{
	accessible: boolean;
	statusCode?: number;
	error?: string;
}>
{
	return new Promise((resolve) =>
	{
		const urlModule = require('url');
		const parsed = urlModule.parse(url);
		const isHttps = parsed.protocol === 'https:';
		const httpModule = isHttps ? require('https') : require('http');

		const request = httpModule.get(url, (response: any) =>
		{
			resolve({
				accessible: true,
				statusCode: response.statusCode,
			});
		});

		request.on('error', (error: Error) =>
		{
			resolve({
				accessible: false,
				error: error.message,
			});
		});

		request.setTimeout(timeout, () =>
		{
			request.destroy();
			resolve({
				accessible: false,
				error: 'Request timeout',
			});
		});
	});
}

/**
 * Generate User-Agent string
 */
function generateUserAgent(options: {
	browser?: string;
	version?: string;
	os?: string;
} = {}): string
{
	const {
		browser = 'Chrome',
		version = '91.0.4472.124',
		os = 'Windows NT 10.0; Win64; x64',
	} = options;

	return `Mozilla/5.0 (${os}) AppleWebKit/537.36 (KHTML, like Gecko) ${browser}/${version} Safari/537.36`;
}

export type { NetworkInterface };

export {
	isValidIP,
	isPrivateIP,
	getIPVersion,
	resolveDomain,
	reverseDNS,
	getAllDNSRecords,
	isPortOpen,
	ping,
	getNetworkInterfaces,
	getPublicIP,
	parseURL,
	isValidURL,
	extractDomain,
	isDomainReachable,
	getIPGeolocation,
	measureLatency,
	isURLAccessible,
	generateUserAgent,
};
