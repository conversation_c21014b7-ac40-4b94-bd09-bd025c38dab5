/**
 * Scheduler Utilities
 * Helper functions for job scheduling and management
 */

type CronExpression = string;

type JobPriority = 'low' | 'medium' | 'high' | 'critical';

type JobStatus = 'pending' | 'running' | 'completed' | 'failed' | 'cancelled' | 'retrying';

type JobSchedule = {
	id: string;
	name: string;
	cron: CronExpression;
	enabled: boolean;
	lastRun?: number;
	nextRun?: number;
	runCount: number;
	failureCount: number;
};

type JobMetrics = {
	totalJobs: number;
	completedJobs: number;
	failedJobs: number;
	averageExecutionTime: number;
	successRate: number;
};

/**
 * Parse cron expression to get next execution time
 */
function getNextCronExecution(cronExpression: string, fromDate = new Date()): Date | null
{
	try
	{
		// Simple cron parser for basic expressions
		// Format: minute hour day month dayOfWeek
		const parts = cronExpression.trim().split(/\s+/);

		if (parts.length !== 5)
		{
			throw new Error('Invalid cron expression format');
		}

		const [minute, hour, day, month, dayOfWeek] = parts;

		const next = new Date(fromDate);
		next.setSeconds(0);
		next.setMilliseconds(0);

		// Set minute
		if (minute !== '*')
		{
			next.setMinutes(parseInt(minute, 10));
		}

		// Set hour
		if (hour !== '*')
		{
			next.setHours(parseInt(hour, 10));
		}

		// If the time has passed today, move to next day
		if (next <= fromDate)
		{
			next.setDate(next.getDate() + 1);
		}

		return next;
	}
	catch
	{
		return null;
	}
}

/**
 * Validate cron expression
 */
function isValidCronExpression(cronExpression: string): boolean
{
	try
	{
		const parts = cronExpression.trim().split(/\s+/);

		if (parts.length !== 5) return false;

		const [minute, hour, day, month, dayOfWeek] = parts;

		// Validate minute (0-59)
		if (minute !== '*' && (isNaN(parseInt(minute, 10)) || parseInt(minute, 10) < 0 || parseInt(minute, 10) > 59))
		{
			return false;
		}

		// Validate hour (0-23)
		if (hour !== '*' && (isNaN(parseInt(hour, 10)) || parseInt(hour, 10) < 0 || parseInt(hour, 10) > 23))
		{
			return false;
		}

		// Validate day (1-31)
		if (day !== '*' && (isNaN(parseInt(day, 10)) || parseInt(day, 10) < 1 || parseInt(day, 10) > 31))
		{
			return false;
		}

		// Validate month (1-12)
		if (month !== '*' && (isNaN(parseInt(month, 10)) || parseInt(month, 10) < 1 || parseInt(month, 10) > 12))
		{
			return false;
		}

		// Validate day of week (0-7, where 0 and 7 are Sunday)
		if (dayOfWeek !== '*' && (isNaN(parseInt(dayOfWeek, 10)) || parseInt(dayOfWeek, 10) < 0 || parseInt(dayOfWeek, 10) > 7))
		{
			return false;
		}

		return true;
	}
	catch
	{
		return false;
	}
}

/**
 * Calculate job priority score
 */
function calculateJobPriorityScore(priority: JobPriority, age: number, retryCount: number): number
{
	const priorityScores: Record<JobPriority, number> = {
		critical: 1000,
		high: 100,
		medium: 10,
		low: 1,
	};

	const baseScore = priorityScores[priority];
	const ageBonus = Math.min(age / (24 * 60 * 60 * 1000), 1) * 10; // Age in days, max 10 points
	const retryPenalty = retryCount * 2; // 2 points penalty per retry

	return Math.max(0, baseScore + ageBonus - retryPenalty);
}

/**
 * Generate job ID
 */
function generateJobId(prefix = 'job'): string
{
	const timestamp = Date.now().toString(36);
	const random = Math.random().toString(36).substr(2, 9);
	return `${prefix}_${timestamp}_${random}`;
}

/**
 * Calculate job execution delay based on retry count
 */
function calculateRetryDelay(retryCount: number, baseDelay = 1000, maxDelay = 300000): number
{
	// Exponential backoff with jitter
	const delay = Math.min(baseDelay * 2**retryCount, maxDelay);
	const jitter = Math.random() * 0.1 * delay; // ±10% jitter

	return Math.floor(delay + jitter);
}

/**
 * Check if job should be retried
 */
function shouldRetryJob(
	error: Error,
	retryCount: number,
	maxRetries: number,
	retryableErrors: string[] = ['timeout', 'network', 'connection'],
): boolean
{
	if (retryCount >= maxRetries) return false;

	const errorMessage = error.message.toLowerCase();
	return retryableErrors.some(retryableError => errorMessage.includes(retryableError));
}

/**
 * Create job batch
 */
function createJobBatch<T>(
	items: T[],
	batchSize: number,
	jobCreator: (item: T, index: number) => any,
): any[][]
{
	const batches: any[][] = [];

	for (let i = 0; i < items.length; i += batchSize)
	{
		const batch = items.slice(i, i + batchSize).map((item, index) => jobCreator(item, i + index));
		batches.push(batch);
	}

	return batches;
}

/**
 * Calculate job queue metrics
 */
function calculateJobMetrics(jobs: Array<{
	status: JobStatus;
	createdAt: number;
	completedAt?: number;
	executionTime?: number;
}>): JobMetrics
{
	const totalJobs = jobs.length;
	const completedJobs = jobs.filter(job => job.status === 'completed').length;
	const failedJobs = jobs.filter(job => job.status === 'failed').length;

	const executionTimes = jobs
		.filter(job => job.executionTime !== undefined)
		.map(job => job.executionTime!);

	const averageExecutionTime = executionTimes.length > 0
		? executionTimes.reduce((sum, time) => sum + time, 0) / executionTimes.length
		: 0;

	const successRate = totalJobs > 0 ? (completedJobs / totalJobs) * 100 : 0;

	return {
		totalJobs,
		completedJobs,
		failedJobs,
		averageExecutionTime,
		successRate,
	};
}

/**
 * Estimate job completion time
 */
function estimateJobCompletionTime(
	queuePosition: number,
	averageExecutionTime: number,
	concurrentWorkers: number,
): number
{
	const estimatedWaitTime = (queuePosition / concurrentWorkers) * averageExecutionTime;
	return Date.now() + estimatedWaitTime;
}

/**
 * Create job schedule from cron expression
 */
function createJobSchedule(
	name: string,
	cronExpression: string,
	enabled = true,
): JobSchedule | null
{
	if (!isValidCronExpression(cronExpression))
	{
		return null;
	}

	const nextRun = getNextCronExecution(cronExpression);

	return {
		id: generateJobId('schedule'),
		name,
		cron: cronExpression,
		enabled,
		nextRun: nextRun?.getTime(),
		runCount: 0,
		failureCount: 0,
	};
}

/**
 * Update job schedule after execution
 */
function updateJobSchedule(schedule: JobSchedule, success: boolean): JobSchedule
{
	const nextRun = getNextCronExecution(schedule.cron);

	return {
		...schedule,
		lastRun: Date.now(),
		nextRun: nextRun?.getTime(),
		runCount: schedule.runCount + 1,
		failureCount: success ? schedule.failureCount : schedule.failureCount + 1,
	};
}

/**
 * Get jobs due for execution
 */
function getDueJobs(schedules: JobSchedule[], currentTime = Date.now()): JobSchedule[]
{
	return schedules.filter(schedule =>
		schedule.enabled &&
		schedule.nextRun !== undefined &&
		schedule.nextRun <= currentTime);
}

/**
 * Prioritize job queue
 */
function prioritizeJobs<T extends { priority: JobPriority; createdAt: number; retryCount: number }>(
	jobs: T[],
): T[]
{
	return jobs.sort((a, b) =>
	{
		const scoreA = calculateJobPriorityScore(a.priority, Date.now() - a.createdAt, a.retryCount);
		const scoreB = calculateJobPriorityScore(b.priority, Date.now() - b.createdAt, b.retryCount);

		return scoreB - scoreA; // Higher score first
	});
}

/**
 * Create job timeout handler
 */
function createJobTimeout(timeoutMs: number, onTimeout: () => void): NodeJS.Timeout
{
	return setTimeout(onTimeout, timeoutMs);
}

/**
 * Calculate optimal batch size
 */
function calculateOptimalBatchSize(
	totalItems: number,
	processingTimePerItem: number,
	maxBatchTime: number,
	minBatchSize = 1,
	maxBatchSize = 1000,
): number
{
	const theoreticalBatchSize = Math.floor(maxBatchTime / processingTimePerItem);
	return Math.max(minBatchSize, Math.min(maxBatchSize, theoreticalBatchSize));
}

/**
 * Create job dependency graph
 */
function createDependencyGraph<T extends { id: string; dependencies?: string[] }>(
	jobs: T[],
): Map<string, T[]>
{
	const graph = new Map<string, T[]>();

	for (const job of jobs)
	{
		if (job.dependencies)
		{
			for (const depId of job.dependencies)
			{
				if (!graph.has(depId))
				{
					graph.set(depId, []);
				}
				graph.get(depId)!.push(job);
			}
		}
	}

	return graph;
}

/**
 * Get jobs ready for execution (no pending dependencies)
 */
function getReadyJobs<T extends { id: string; dependencies?: string[] }>(
	jobs: T[],
	completedJobIds: Set<string>,
): T[]
{
	return jobs.filter((job) =>
	{
		if (!job.dependencies || job.dependencies.length === 0)
		{
			return true;
		}

		return job.dependencies.every(depId => completedJobIds.has(depId));
	});
}

/**
 * Format job duration
 */
function formatJobDuration(milliseconds: number): string
{
	if (milliseconds < 1000) return `${milliseconds}ms`;

	const seconds = Math.floor(milliseconds / 1000);
	const minutes = Math.floor(seconds / 60);
	const hours = Math.floor(minutes / 60);

	if (hours > 0) return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
	if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
	return `${seconds}s`;
}

/**
 * Common cron expressions
 */
const COMMON_CRON_EXPRESSIONS = {
	EVERY_MINUTE: '* * * * *',
	EVERY_5_MINUTES: '*/5 * * * *',
	EVERY_15_MINUTES: '*/15 * * * *',
	EVERY_30_MINUTES: '*/30 * * * *',
	EVERY_HOUR: '0 * * * *',
	EVERY_2_HOURS: '0 */2 * * *',
	EVERY_6_HOURS: '0 */6 * * *',
	EVERY_12_HOURS: '0 */12 * * *',
	DAILY_AT_MIDNIGHT: '0 0 * * *',
	DAILY_AT_NOON: '0 12 * * *',
	WEEKLY_SUNDAY_MIDNIGHT: '0 0 * * 0',
	MONTHLY_FIRST_DAY: '0 0 1 * *',
};

export type {
	CronExpression,
	JobPriority,
	JobStatus,
	JobSchedule,
	JobMetrics,
};

export {
	getNextCronExecution,
	isValidCronExpression,
	calculateJobPriorityScore,
	generateJobId,
	calculateRetryDelay,
	shouldRetryJob,
	createJobBatch,
	calculateJobMetrics,
	estimateJobCompletionTime,
	createJobSchedule,
	updateJobSchedule,
	getDueJobs,
	prioritizeJobs,
	createJobTimeout,
	calculateOptimalBatchSize,
	createDependencyGraph,
	getReadyJobs,
	formatJobDuration,
	COMMON_CRON_EXPRESSIONS,
};
