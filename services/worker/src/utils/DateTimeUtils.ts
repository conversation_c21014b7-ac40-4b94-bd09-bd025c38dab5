/**
 * Date and Time Utilities
 * Helper functions for date/time manipulation and formatting
 */

type TimeUnit = 'milliseconds' | 'seconds' | 'minutes' | 'hours' | 'days' | 'weeks' | 'months' | 'years';

/**
 * Format date to ISO string
 */
function toISOString(date: Date): string
{
	return date.toISOString();
}

/**
 * Parse ISO string to date
 */
function fromISOString(isoString: string): Date
{
	return new Date(isoString);
}

/**
 * Format date to readable string
 */
function formatDate(date: Date, format = 'YYYY-MM-DD'): string
{
	const year = date.getFullYear();
	const month = String(date.getMonth() + 1).padStart(2, '0');
	const day = String(date.getDate()).padStart(2, '0');
	const hours = String(date.getHours()).padStart(2, '0');
	const minutes = String(date.getMinutes()).padStart(2, '0');
	const seconds = String(date.getSeconds()).padStart(2, '0');

	return format
		.replace('YYYY', String(year))
		.replace('MM', month)
		.replace('DD', day)
		.replace('HH', hours)
		.replace('mm', minutes)
		.replace('ss', seconds);
}

/**
 * Format date to relative time (e.g., "2 hours ago")
 */
function formatRelativeTime(date: Date, now = new Date()): string
{
	const diff = now.getTime() - date.getTime();
	const seconds = Math.floor(diff / 1000);
	const minutes = Math.floor(seconds / 60);
	const hours = Math.floor(minutes / 60);
	const days = Math.floor(hours / 24);
	const weeks = Math.floor(days / 7);
	const months = Math.floor(days / 30);
	const years = Math.floor(days / 365);

	if (years > 0) return `${years} year${years > 1 ? 's' : ''} ago`;
	if (months > 0) return `${months} month${months > 1 ? 's' : ''} ago`;
	if (weeks > 0) return `${weeks} week${weeks > 1 ? 's' : ''} ago`;
	if (days > 0) return `${days} day${days > 1 ? 's' : ''} ago`;
	if (hours > 0) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
	if (minutes > 0) return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
	if (seconds > 0) return `${seconds} second${seconds > 1 ? 's' : ''} ago`;

	return 'just now';
}

/**
 * Add time to date
 */
function addTime(date: Date, amount: number, unit: TimeUnit): Date
{
	const result = new Date(date);

	switch (unit)
	{
		case 'milliseconds':
			result.setMilliseconds(result.getMilliseconds() + amount);
			break;
		case 'seconds':
			result.setSeconds(result.getSeconds() + amount);
			break;
		case 'minutes':
			result.setMinutes(result.getMinutes() + amount);
			break;
		case 'hours':
			result.setHours(result.getHours() + amount);
			break;
		case 'days':
			result.setDate(result.getDate() + amount);
			break;
		case 'weeks':
			result.setDate(result.getDate() + (amount * 7));
			break;
		case 'months':
			result.setMonth(result.getMonth() + amount);
			break;
		case 'years':
			result.setFullYear(result.getFullYear() + amount);
			break;
	}

	return result;
}

/**
 * Subtract time from date
 */
function subtractTime(date: Date, amount: number, unit: TimeUnit): Date
{
	return addTime(date, -amount, unit);
}

/**
 * Get difference between dates
 */
function dateDiff(date1: Date, date2: Date, unit: TimeUnit = 'milliseconds'): number
{
	const diff = date2.getTime() - date1.getTime();

	switch (unit)
	{
		case 'milliseconds':
			return diff;
		case 'seconds':
			return Math.floor(diff / 1000);
		case 'minutes':
			return Math.floor(diff / (1000 * 60));
		case 'hours':
			return Math.floor(diff / (1000 * 60 * 60));
		case 'days':
			return Math.floor(diff / (1000 * 60 * 60 * 24));
		case 'weeks':
			return Math.floor(diff / (1000 * 60 * 60 * 24 * 7));
		case 'months':
			return Math.floor(diff / (1000 * 60 * 60 * 24 * 30));
		case 'years':
			return Math.floor(diff / (1000 * 60 * 60 * 24 * 365));
		default:
			return diff;
	}
}

/**
 * Check if date is today
 */
function isToday(date: Date): boolean
{
	const today = new Date();
	return date.toDateString() === today.toDateString();
}

/**
 * Check if date is yesterday
 */
function isYesterday(date: Date): boolean
{
	const yesterday = subtractTime(new Date(), 1, 'days');
	return date.toDateString() === yesterday.toDateString();
}

/**
 * Check if date is tomorrow
 */
function isTomorrow(date: Date): boolean
{
	const tomorrow = addTime(new Date(), 1, 'days');
	return date.toDateString() === tomorrow.toDateString();
}

/**
 * Check if date is in the past
 */
function isPast(date: Date): boolean
{
	return date.getTime() < Date.now();
}

/**
 * Check if date is in the future
 */
function isFuture(date: Date): boolean
{
	return date.getTime() > Date.now();
}

/**
 * Get start of day
 */
function startOfDay(date: Date): Date
{
	const result = new Date(date);
	result.setHours(0, 0, 0, 0);
	return result;
}

/**
 * Get end of day
 */
function endOfDay(date: Date): Date
{
	const result = new Date(date);
	result.setHours(23, 59, 59, 999);
	return result;
}

/**
 * Get start of week
 */
function startOfWeek(date: Date, startDay = 0): Date
{
	const result = new Date(date);
	const day = result.getDay();
	const diff = (day < startDay ? 7 : 0) + day - startDay;

	result.setDate(result.getDate() - diff);
	return startOfDay(result);
}

/**
 * Get end of week
 */
function endOfWeek(date: Date, startDay = 0): Date
{
	const start = startOfWeek(date, startDay);
	return endOfDay(addTime(start, 6, 'days'));
}

/**
 * Get start of month
 */
function startOfMonth(date: Date): Date
{
	const result = new Date(date);
	result.setDate(1);
	return startOfDay(result);
}

/**
 * Get end of month
 */
function endOfMonth(date: Date): Date
{
	const result = new Date(date);
	result.setMonth(result.getMonth() + 1, 0);
	return endOfDay(result);
}

/**
 * Get start of year
 */
function startOfYear(date: Date): Date
{
	const result = new Date(date);
	result.setMonth(0, 1);
	return startOfDay(result);
}

/**
 * Get end of year
 */
function endOfYear(date: Date): Date
{
	const result = new Date(date);
	result.setMonth(11, 31);
	return endOfDay(result);
}

/**
 * Check if year is leap year
 */
function isLeapYear(year: number): boolean
{
	return (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0);
}

/**
 * Get days in month
 */
function getDaysInMonth(year: number, month: number): number
{
	return new Date(year, month + 1, 0).getDate();
}

/**
 * Get day of year
 */
function getDayOfYear(date: Date): number
{
	const start = startOfYear(date);
	return Math.floor(dateDiff(start, date, 'days')) + 1;
}

/**
 * Get week of year
 */
function getWeekOfYear(date: Date): number
{
	const start = startOfYear(date);
	const startWeek = startOfWeek(start);
	const currentWeek = startOfWeek(date);

	return Math.floor(dateDiff(startWeek, currentWeek, 'weeks')) + 1;
}

/**
 * Parse date string with various formats
 */
function parseDate(dateString: string): Date | null
{
	// Try ISO format first
	if (/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(dateString))
	{
		return new Date(dateString);
	}

	// Try common formats
	const formats = [
		/^(\d{4})-(\d{2})-(\d{2})$/, // YYYY-MM-DD
		/^(\d{2})\/(\d{2})\/(\d{4})$/, // MM/DD/YYYY
		/^(\d{2})-(\d{2})-(\d{4})$/, // MM-DD-YYYY
		/^(\d{4})\/(\d{2})\/(\d{2})$/, // YYYY/MM/DD
	];

	for (const format of formats)
	{
		const match = dateString.match(format);
		if (match)
		{
			const [, part1, part2, part3] = match;

			// Determine if it's YYYY-MM-DD or MM-DD-YYYY format
			if (parseInt(part1, 10) > 31)
			{
				// YYYY-MM-DD
				return new Date(parseInt(part1, 10), parseInt(part2, 10) - 1, parseInt(part3, 10));
			}

			// MM-DD-YYYY
			return new Date(parseInt(part3, 10), parseInt(part1, 10) - 1, parseInt(part2, 10));

		}
	}

	// Try native Date parsing as fallback
	const parsed = new Date(dateString);
	return isNaN(parsed.getTime()) ? null : parsed;
}

/**
 * Get timezone offset in minutes
 */
function getTimezoneOffset(date = new Date()): number
{
	return date.getTimezoneOffset();
}

/**
 * Convert date to UTC
 */
function toUTC(date: Date): Date
{
	return new Date(date.getTime() + (date.getTimezoneOffset() * 60000));
}

/**
 * Convert UTC date to local time
 */
function fromUTC(date: Date): Date
{
	return new Date(date.getTime() - (date.getTimezoneOffset() * 60000));
}

/**
 * Get age from birth date
 */
function getAge(birthDate: Date, referenceDate = new Date()): number
{
	let age = referenceDate.getFullYear() - birthDate.getFullYear();
	const monthDiff = referenceDate.getMonth() - birthDate.getMonth();

	if (monthDiff < 0 || (monthDiff === 0 && referenceDate.getDate() < birthDate.getDate()))
	{
		age--;
	}

	return age;
}

/**
 * Format duration in milliseconds to human readable
 */
function formatDuration(milliseconds: number): string
{
	const seconds = Math.floor(milliseconds / 1000);
	const minutes = Math.floor(seconds / 60);
	const hours = Math.floor(minutes / 60);
	const days = Math.floor(hours / 24);

	if (days > 0) return `${days}d ${hours % 24}h ${minutes % 60}m`;
	if (hours > 0) return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
	if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
	if (seconds > 0) return `${seconds}s`;
	return `${milliseconds}ms`;
}

/**
 * Create date range
 */
function dateRange(start: Date, end: Date, step: number = 1, unit: TimeUnit = 'days'): Date[]
{
	const dates: Date[] = [];
	let current = new Date(start);

	while (current <= end)
	{
		dates.push(new Date(current));
		current = addTime(current, step, unit);
	}

	return dates;
}

/**
 * Get business days between dates (excluding weekends)
 */
function getBusinessDays(start: Date, end: Date): number
{
	let count = 0;
	const current = new Date(start);

	while (current <= end)
	{
		const dayOfWeek = current.getDay();
		if (dayOfWeek !== 0 && dayOfWeek !== 6) // Not Sunday or Saturday
		{
			count++;
		}
		current.setDate(current.getDate() + 1);
	}

	return count;
}

/**
 * Check if date is weekend
 */
function isWeekend(date: Date): boolean
{
	const dayOfWeek = date.getDay();
	return dayOfWeek === 0 || dayOfWeek === 6; // Sunday or Saturday
}

/**
 * Check if date is business day
 */
function isBusinessDay(date: Date): boolean
{
	return !isWeekend(date);
}

export type { TimeUnit };

export {
	toISOString,
	fromISOString,
	formatDate,
	formatRelativeTime,
	addTime,
	subtractTime,
	dateDiff,
	isToday,
	isYesterday,
	isTomorrow,
	isPast,
	isFuture,
	startOfDay,
	endOfDay,
	startOfWeek,
	endOfWeek,
	startOfMonth,
	endOfMonth,
	startOfYear,
	endOfYear,
	isLeapYear,
	getDaysInMonth,
	getDayOfYear,
	getWeekOfYear,
	parseDate,
	getTimezoneOffset,
	toUTC,
	fromUTC,
	getAge,
	formatDuration,
	dateRange,
	getBusinessDays,
	isWeekend,
	isBusinessDay,
};
