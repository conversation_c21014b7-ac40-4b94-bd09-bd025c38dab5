/**
 * Retry Helper Utilities
 * Consolidated retry logic from all three services
 */

type RetryOptions = {
	maxAttempts: number;
	baseDelay: number;
	maxDelay: number;
	backoffMultiplier: number;
	jitter: boolean;
	retryCondition?: (error: Error, attempt: number) => boolean;
	onRetry?: (error: Error, attempt: number) => void;
	abortSignal?: AbortSignal;
};

type RetryResult<T> = {
	success: boolean;
	result?: T;
	error?: Error;
	attempts: number;
	totalDuration: number;
};

type BackoffStrategy = 'exponential' | 'linear' | 'fixed' | 'fibonacci';

/**
 * Retry manager with various backoff strategies
 */
class RetryManager
{
	private defaultOptions: RetryOptions = {
		maxAttempts: 3,
		baseDelay: 1000,
		maxDelay: 30000,
		backoffMultiplier: 2,
		jitter: true,
	};

	/**
	 * Execute operation with retry logic
	 */
	async execute<T>(
		operation: () => Promise<T>,
		options: Partial<RetryOptions> = {},
	): Promise<T>
	{
		const config = { ...this.defaultOptions, ...options };
		const startTime = Date.now();
		let lastError: Error;

		for (let attempt = 1; attempt <= config.maxAttempts; attempt++)
		{
			// Check for abort signal
			if (config.abortSignal?.aborted)
			{
				throw new Error('Operation aborted');
			}

			try
			{
				const result = await operation();
				return result;
			}
			catch (error)
			{
				lastError = error instanceof Error ? error : new Error(String(error));

				// Check if we should retry
				if (attempt === config.maxAttempts)
				{
					break; // Last attempt, don't retry
				}

				if (config.retryCondition && !config.retryCondition(lastError, attempt))
				{
					break; // Custom condition says don't retry
				}

				// Call retry callback
				if (config.onRetry)
				{
					config.onRetry(lastError, attempt);
				}

				// Calculate delay and wait
				const delay = this.calculateDelay(attempt, config);
				await this.sleep(delay);
			}
		}

		throw lastError!;
	}

	/**
	 * Execute with detailed result
	 */
	async executeWithResult<T>(
		operation: () => Promise<T>,
		options: Partial<RetryOptions> = {},
	): Promise<RetryResult<T>>
	{
		const startTime = Date.now();
		let attempts = 0;

		try
		{
			const result = await this.execute(operation, {
				...options,
				onRetry: (error, attempt) =>
				{
					attempts = attempt;
					options.onRetry?.(error, attempt);
				},
			});

			return {
				success: true,
				result,
				attempts: attempts + 1,
				totalDuration: Date.now() - startTime,
			};
		}
		catch (error)
		{
			return {
				success: false,
				error: error instanceof Error ? error : new Error(String(error)),
				attempts: attempts + 1,
				totalDuration: Date.now() - startTime,
			};
		}
	}

	/**
	 * Calculate delay with backoff strategy
	 */
	private calculateDelay(attempt: number, options: RetryOptions): number
	{
		let delay = Math.min(
			options.baseDelay * options.backoffMultiplier**(attempt - 1),
			options.maxDelay,
		);

		if (options.jitter)
		{
			// Add random jitter (±25%)
			const jitterRange = delay * 0.25;
			delay += (Math.random() - 0.5) * 2 * jitterRange;
		}

		return Math.max(0, delay);
	}

	/**
	 * Sleep for specified milliseconds
	 */
	private sleep(ms: number): Promise<void>
	{
		return new Promise(resolve => setTimeout(resolve, ms));
	}
}

/**
 * Exponential backoff calculator
 */
class BackoffCalculator
{
	/**
	 * Calculate exponential backoff delay
	 */
	static exponential(
		attempt: number,
		baseDelay: number,
		multiplier = 2,
		maxDelay = 30000,
		jitter = true,
	): number
	{
		let delay = Math.min(baseDelay * multiplier**(attempt - 1), maxDelay);

		if (jitter)
		{
			delay *= (0.5 + Math.random() * 0.5); // 50-100% of calculated delay
		}

		return Math.floor(delay);
	}

	/**
	 * Calculate linear backoff delay
	 */
	static linear(
		attempt: number,
		baseDelay: number,
		increment: number,
		maxDelay = 30000,
		jitter = true,
	): number
	{
		let delay = Math.min(baseDelay + (attempt - 1) * increment, maxDelay);

		if (jitter)
		{
			delay *= (0.8 + Math.random() * 0.4); // 80-120% of calculated delay
		}

		return Math.floor(delay);
	}

	/**
	 * Calculate fibonacci backoff delay
	 */
	static fibonacci(
		attempt: number,
		baseDelay: number,
		maxDelay = 30000,
		jitter = true,
	): number
	{
		const fibNumber = this.fibonacciNumber(attempt);
		let delay = Math.min(baseDelay * fibNumber, maxDelay);

		if (jitter)
		{
			delay *= (0.7 + Math.random() * 0.6); // 70-130% of calculated delay
		}

		return Math.floor(delay);
	}

	/**
	 * Calculate fixed delay
	 */
	static fixed(baseDelay: number, jitter = true): number
	{
		let delay = baseDelay;

		if (jitter)
		{
			delay *= (0.9 + Math.random() * 0.2); // 90-110% of base delay
		}

		return Math.floor(delay);
	}

	/**
	 * Calculate fibonacci number
	 */
	private static fibonacciNumber(n: number): number
	{
		if (n <= 1) return 1;
		if (n === 2) return 1;

		let a = 1;
		let b = 1;

		for (let i = 3; i <= n; i++)
		{
			const temp = a + b;
			a = b;
			b = temp;
		}

		return b;
	}
}

/**
 * Retry with specific conditions
 */
async function retryWithCondition<T>(
	operation: () => Promise<T>,
	condition: (error: Error) => boolean,
	maxAttempts = 3,
	baseDelay = 1000,
): Promise<T>
{
	const retryManager = new RetryManager();

	return retryManager.execute(operation, {
		maxAttempts,
		baseDelay,
		retryCondition: condition,
	});
}

/**
 * Retry network operations
 */
async function retryNetworkOperation<T>(
	operation: () => Promise<T>,
	maxAttempts = 3,
): Promise<T>
{
	return retryWithCondition(
		operation,
		(error) =>
		{
			const message = error.message.toLowerCase();
			return message.includes('network') ||
				message.includes('timeout') ||
				message.includes('enotfound') ||
				message.includes('econnreset') ||
				message.includes('econnrefused');
		},
		maxAttempts,
		2000,
	);
}

/**
 * Retry database operations
 */
async function retryDatabaseOperation<T>(
	operation: () => Promise<T>,
	maxAttempts = 5,
): Promise<T>
{
	return retryWithCondition(
		operation,
		(error) =>
		{
			const message = error.message.toLowerCase();
			return message.includes('connection') ||
				message.includes('timeout') ||
				message.includes('deadlock') ||
				message.includes('lock wait timeout');
		},
		maxAttempts,
		500,
	);
}

/**
 * Retry with exponential backoff
 */
async function retryExponential<T>(
	operation: () => Promise<T>,
	maxAttempts = 3,
	baseDelay = 1000,
	maxDelay = 30000,
): Promise<T>
{
	const retryManager = new RetryManager();

	return retryManager.execute(operation, {
		maxAttempts,
		baseDelay,
		maxDelay,
		backoffMultiplier: 2,
		jitter: true,
	});
}

/**
 * Retry with linear backoff
 */
async function retryLinear<T>(
	operation: () => Promise<T>,
	maxAttempts = 3,
	baseDelay = 1000,
	increment = 1000,
): Promise<T>
{
	const retryManager = new RetryManager();

	return retryManager.execute(operation, {
		maxAttempts,
		baseDelay,
		maxDelay: baseDelay + (maxAttempts - 1) * increment,
		backoffMultiplier: 1,
		jitter: true,
		onRetry: (error, attempt) =>
		{
			// Custom linear calculation in onRetry
		},
	});
}

/**
 * Retry with timeout
 */
async function retryWithTimeout<T>(
	operation: () => Promise<T>,
	timeoutMs: number,
	maxAttempts = 3,
): Promise<T>
{
	const retryManager = new RetryManager();

	const timeoutOperation = async (): Promise<T> =>
		Promise.race([
			operation(),
			new Promise<never>((_, reject) =>
			{
				setTimeout(() => reject(new Error('Operation timed out')), timeoutMs);
			}),
		]);

	return retryManager.execute(timeoutOperation, {
		maxAttempts,
		baseDelay: 1000,
		retryCondition: error => error.message.includes('timed out'),
	});
}

/**
 * Batch retry operations
 */
async function retryBatch<T>(
	operations: Array<() => Promise<T>>,
	options: Partial<RetryOptions> = {},
): Promise<Array<RetryResult<T>>>
{
	const retryManager = new RetryManager();
	const results: Array<RetryResult<T>> = [];

	for (const operation of operations)
	{
		const result = await retryManager.executeWithResult(operation, options);
		results.push(result);
	}

	return results;
}

/**
 * Parallel retry operations with concurrency limit
 */
async function retryParallel<T>(
	operations: Array<() => Promise<T>>,
	concurrency = 5,
	options: Partial<RetryOptions> = {},
): Promise<Array<RetryResult<T>>>
{
	const retryManager = new RetryManager();
	const results: Array<RetryResult<T>> = [];

	// Process operations in batches
	for (let i = 0; i < operations.length; i += concurrency)
	{
		const batch = operations.slice(i, i + concurrency);
		const batchPromises = batch.map(operation =>
			retryManager.executeWithResult(operation, options));

		const batchResults = await Promise.all(batchPromises);
		results.push(...batchResults);
	}

	return results;
}

/**
 * Create retry decorator
 */
function withRetry<T extends any[], R>(
	fn: (...args: T) => Promise<R>,
	options: Partial<RetryOptions> = {},
): (...args: T) => Promise<R>
{
	const retryManager = new RetryManager();

	return async (...args: T): Promise<R> =>
		retryManager.execute(() => fn(...args), options);
}

/**
 * Create abort controller with timeout
 */
function createAbortController(timeoutMs?: number): AbortController
{
	const controller = new AbortController();

	if (timeoutMs)
	{
		setTimeout(() => controller.abort(), timeoutMs);
	}

	return controller;
}

export type {
	RetryOptions,
	RetryResult,
	BackoffStrategy,
};

export {
	RetryManager,
	BackoffCalculator,
	retryWithCondition,
	retryNetworkOperation,
	retryDatabaseOperation,
	retryExponential,
	retryLinear,
	retryWithTimeout,
	retryBatch,
	retryParallel,
	withRetry,
	createAbortController,
};
