/**
 * Array Utilities
 * Helper functions for array manipulation and processing
 */

/**
 * Remove duplicates from array
 */
function unique<T>(array: T[]): T[]
{
	return [...new Set(array)];
}

/**
 * Remove duplicates by key
 */
function uniqueBy<T>(array: T[], keyFn: (item: T) => any): T[]
{
	const seen = new Set();
	return array.filter((item) =>
	{
		const key = keyFn(item);
		if (seen.has(key))
		{
			return false;
		}
		seen.add(key);
		return true;
	});
}

/**
 * Chunk array into smaller arrays
 */
function chunk<T>(array: T[], size: number): T[][]
{
	const chunks: T[][] = [];
	for (let i = 0; i < array.length; i += size)
	{
		chunks.push(array.slice(i, i + size));
	}
	return chunks;
}

/**
 * Flatten nested arrays
 */
function flatten<T>(array: (T | T[])[]): T[]
{
	return array.reduce<T[]>((acc, val) =>
		Array.isArray(val) ? acc.concat(flatten(val)) : acc.concat(val), []);
}

/**
 * Group array by key
 */
function groupBy<T>(array: T[], keyFn: (item: T) => string | number): Record<string, T[]>
{
	return array.reduce((groups, item) =>
	{
		const key = String(keyFn(item));
		if (!groups[key])
		{
			groups[key] = [];
		}
		groups[key].push(item);
		return groups;
	}, {} as Record<string, T[]>);
}

/**
 * Sort array by multiple keys
 */
function sortBy<T>(array: T[], ...keyFns: Array<(item: T) => any>): T[]
{
	return [...array].sort((a, b) =>
	{
		for (const keyFn of keyFns)
		{
			const aVal = keyFn(a);
			const bVal = keyFn(b);

			if (aVal < bVal) return -1;
			if (aVal > bVal) return 1;
		}
		return 0;
	});
}

/**
 * Find intersection of arrays
 */
function intersection<T>(...arrays: T[][]): T[]
{
	if (arrays.length === 0) return [];
	if (arrays.length === 1) return arrays[0];

	return arrays.reduce((acc, array) =>
		acc.filter(item => array.includes(item)));
}

/**
 * Find union of arrays
 */
function union<T>(...arrays: T[][]): T[]
{
	return unique(arrays.flat());
}

/**
 * Find difference between arrays
 */
function difference<T>(array1: T[], array2: T[]): T[]
{
	return array1.filter(item => !array2.includes(item));
}

/**
 * Shuffle array randomly
 */
function shuffle<T>(array: T[]): T[]
{
	const shuffled = [...array];
	for (let i = shuffled.length - 1; i > 0; i--)
	{
		const j = Math.floor(Math.random() * (i + 1));
		[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
	}
	return shuffled;
}

/**
 * Get random sample from array
 */
function sample<T>(array: T[], count = 1): T[]
{
	const shuffled = shuffle(array);
	return shuffled.slice(0, Math.min(count, array.length));
}

/**
 * Partition array based on predicate
 */
function partition<T>(array: T[], predicate: (item: T) => boolean): [T[], T[]]
{
	const truthy: T[] = [];
	const falsy: T[] = [];

	for (const item of array)
	{
		if (predicate(item))
		{
			truthy.push(item);
		}
		else
		{
			falsy.push(item);
		}
	}

	return [truthy, falsy];
}

/**
 * Find min/max values in array
 */
function minMax<T>(array: T[], valueFn: (item: T) => number): { min: T; max: T } | null
{
	if (array.length === 0) return null;

	let min = array[0];
	let max = array[0];
	let minValue = valueFn(min);
	let maxValue = valueFn(max);

	for (let i = 1; i < array.length; i++)
	{
		const value = valueFn(array[i]);
		if (value < minValue)
		{
			min = array[i];
			minValue = value;
		}
		if (value > maxValue)
		{
			max = array[i];
			maxValue = value;
		}
	}

	return { min, max };
}

/**
 * Calculate sum of array values
 */
function sum<T>(array: T[], valueFn: (item: T) => number): number
{
	return array.reduce((total, item) => total + valueFn(item), 0);
}

/**
 * Calculate average of array values
 */
function average<T>(array: T[], valueFn: (item: T) => number): number
{
	return array.length > 0 ? sum(array, valueFn) / array.length : 0;
}

/**
 * Calculate median of array values
 */
function median<T>(array: T[], valueFn: (item: T) => number): number
{
	if (array.length === 0) return 0;

	const values = array.map(valueFn).sort((a, b) => a - b);
	const middle = Math.floor(values.length / 2);

	return values.length % 2 === 0
		? (values[middle - 1] + values[middle]) / 2
		: values[middle];
}

/**
 * Count occurrences in array
 */
function countBy<T>(array: T[], keyFn: (item: T) => string | number): Record<string, number>
{
	return array.reduce((counts, item) =>
	{
		const key = String(keyFn(item));
		counts[key] = (counts[key] || 0) + 1;
		return counts;
	}, {} as Record<string, number>);
}

/**
 * Zip arrays together
 */
function zip<T>(...arrays: T[][]): T[][]
{
	const maxLength = Math.max(...arrays.map(arr => arr.length));
	const result: T[][] = [];

	for (let i = 0; i < maxLength; i++)
	{
		result.push(arrays.map(arr => arr[i]));
	}

	return result;
}

/**
 * Transpose 2D array
 */
function transpose<T>(matrix: T[][]): T[][]
{
	if (matrix.length === 0) return [];

	const rows = matrix.length;
	const cols = matrix[0].length;
	const result: T[][] = [];

	for (let j = 0; j < cols; j++)
	{
		result[j] = [];
		for (let i = 0; i < rows; i++)
		{
			result[j][i] = matrix[i][j];
		}
	}

	return result;
}

/**
 * Rotate array left or right
 */
function rotate<T>(array: T[], positions: number): T[]
{
	const len = array.length;
	if (len === 0) return array;

	const normalizedPositions = ((positions % len) + len) % len;
	return [...array.slice(normalizedPositions), ...array.slice(0, normalizedPositions)];
}

/**
 * Insert item at index
 */
function insertAt<T>(array: T[], index: number, ...items: T[]): T[]
{
	const result = [...array];
	result.splice(index, 0, ...items);
	return result;
}

/**
 * Remove item at index
 */
function removeAt<T>(array: T[], index: number, count = 1): T[]
{
	const result = [...array];
	result.splice(index, count);
	return result;
}

/**
 * Move item from one index to another
 */
function move<T>(array: T[], fromIndex: number, toIndex: number): T[]
{
	const result = [...array];
	const [item] = result.splice(fromIndex, 1);
	result.splice(toIndex, 0, item);
	return result;
}

/**
 * Check if arrays are equal
 */
function isEqual<T>(array1: T[], array2: T[], compareFn?: (a: T, b: T) => boolean): boolean
{
	if (array1.length !== array2.length) return false;

	const compare = compareFn || ((a, b) => a === b);

	for (let i = 0; i < array1.length; i++)
	{
		if (!compare(array1[i], array2[i]))
		{
			return false;
		}
	}

	return true;
}

/**
 * Find all indices where predicate is true
 */
function findIndices<T>(array: T[], predicate: (item: T, index: number) => boolean): number[]
{
	const indices: number[] = [];

	for (let i = 0; i < array.length; i++)
	{
		if (predicate(array[i], i))
		{
			indices.push(i);
		}
	}

	return indices;
}

/**
 * Create array of numbers in range
 */
function range(start: number, end?: number, step = 1): number[]
{
	if (end === undefined)
	{
		end = start;
		start = 0;
	}

	const result: number[] = [];

	if (step > 0)
	{
		for (let i = start; i < end; i += step)
		{
			result.push(i);
		}
	}
	else if (step < 0)
	{
		for (let i = start; i > end; i += step)
		{
			result.push(i);
		}
	}

	return result;
}

/**
 * Create array with repeated value
 */
function repeat<T>(value: T, count: number): T[]
{
	return Array(count).fill(value);
}

/**
 * Compact array (remove falsy values)
 */
function compact<T>(array: T[]): NonNullable<T>[]
{
	return array.filter(Boolean) as NonNullable<T>[];
}

/**
 * Take first n items
 */
function take<T>(array: T[], count: number): T[]
{
	return array.slice(0, Math.max(0, count));
}

/**
 * Take last n items
 */
function takeLast<T>(array: T[], count: number): T[]
{
	return array.slice(-Math.max(0, count));
}

/**
 * Drop first n items
 */
function drop<T>(array: T[], count: number): T[]
{
	return array.slice(Math.max(0, count));
}

/**
 * Drop last n items
 */
function dropLast<T>(array: T[], count: number): T[]
{
	return array.slice(0, -Math.max(0, count));
}

export {
	unique,
	uniqueBy,
	chunk,
	flatten,
	groupBy,
	sortBy,
	intersection,
	union,
	difference,
	shuffle,
	sample,
	partition,
	minMax,
	sum,
	average,
	median,
	countBy,
	zip,
	transpose,
	rotate,
	insertAt,
	removeAt,
	move,
	isEqual,
	findIndices,
	range,
	repeat,
	compact,
	take,
	takeLast,
	drop,
	dropLast,
};
