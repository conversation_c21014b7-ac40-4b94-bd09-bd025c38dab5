/**
 * Metrics Helper Utilities
 * Consolidated from all three services
 */

type MetricType = 'counter' | 'gauge' | 'histogram' | 'summary';

type MetricValue = {
	value: number;
	timestamp: number;
	labels?: Record<string, string>;
};

type PerformanceMetrics = {
	cpuUsage: number;
	memoryUsage: number;
	heapUsed: number;
	heapTotal: number;
	uptime: number;
	eventLoopDelay: number;
};

type DatabaseMetrics = {
	connectionPoolSize: number;
	activeConnections: number;
	queryCount: number;
	queryDuration: number;
	errorCount: number;
};

type QueueMetrics = {
	queueSize: number;
	processingCount: number;
	completedCount: number;
	failedCount: number;
	averageProcessingTime: number;
};

type CrawlerMetrics = {
	domainsProcessed: number;
	successfulCrawls: number;
	failedCrawls: number;
	averageCrawlTime: number;
	analysisCount: Record<string, number>;
};

type RankingMetrics = {
	rankingsCalculated: number;
	averageCalculationTime: number;
	scoreDistribution: Record<string, number>;
	rankingUpdates: number;
};

/**
 * Metrics collector for worker service
 */
class WorkerMetricsCollector
{
	private metrics: Map<string, MetricValue[]>;
	private counters: Map<string, number>;
	private gauges: Map<string, number>;
	private histograms: Map<string, number[]>;
	private startTime: number;

	constructor()
	{
		this.metrics = new Map();
		this.counters = new Map();
		this.gauges = new Map();
		this.histograms = new Map();
		this.startTime = Date.now();
	}

	/**
	 * Record a counter metric
	 */
	incrementCounter(name: string, value = 1, labels?: Record<string, string>): void
	{
		const key = this.createMetricKey(name, labels);
		const current = this.counters.get(key) || 0;
		this.counters.set(key, current + value);

		this.recordMetric(name, current + value, 'counter', labels);
	}

	/**
	 * Set a gauge metric
	 */
	setGauge(name: string, value: number, labels?: Record<string, string>): void
	{
		const key = this.createMetricKey(name, labels);
		this.gauges.set(key, value);

		this.recordMetric(name, value, 'gauge', labels);
	}

	/**
	 * Record a histogram value
	 */
	recordHistogram(name: string, value: number, labels?: Record<string, string>): void
	{
		const key = this.createMetricKey(name, labels);
		const values = this.histograms.get(key) || [];
		values.push(value);
		this.histograms.set(key, values);

		this.recordMetric(name, value, 'histogram', labels);
	}

	/**
	 * Record a timing metric
	 */
	recordTiming(name: string, duration: number, labels?: Record<string, string>): void
	{
		this.recordHistogram(`${name}_duration`, duration, labels);
		this.incrementCounter(`${name}_total`, 1, labels);
	}

	/**
	 * Time a function execution
	 */
	async timeFunction<T>(name: string, fn: () => Promise<T>, labels?: Record<string, string>): Promise<T>
	{
		const start = Date.now();
		try
		{
			const result = await fn();
			this.recordTiming(name, Date.now() - start, { ...labels, status: 'success' });
			return result;
		}
		catch (error)
		{
			this.recordTiming(name, Date.now() - start, { ...labels, status: 'error' });
			throw error;
		}
	}

	/**
	 * Get performance metrics
	 */
	getPerformanceMetrics(): PerformanceMetrics
	{
		const memUsage = process.memoryUsage();
		const cpuUsage = process.cpuUsage();

		return {
			cpuUsage: (cpuUsage.user + cpuUsage.system) / 1000000, // Convert to seconds
			memoryUsage: memUsage.rss / 1024 / 1024, // Convert to MB
			heapUsed: memUsage.heapUsed / 1024 / 1024,
			heapTotal: memUsage.heapTotal / 1024 / 1024,
			uptime: (Date.now() - this.startTime) / 1000, // Convert to seconds
			eventLoopDelay: this.getEventLoopDelay(),
		};
	}

	/**
	 * Get database metrics
	 */
	getDatabaseMetrics(): DatabaseMetrics
	{
		return {
			connectionPoolSize: this.getGauge('db_connection_pool_size') || 0,
			activeConnections: this.getGauge('db_active_connections') || 0,
			queryCount: this.getCounter('db_queries_total') || 0,
			queryDuration: this.getHistogramAverage('db_query_duration') || 0,
			errorCount: this.getCounter('db_errors_total') || 0,
		};
	}

	/**
	 * Get queue metrics
	 */
	getQueueMetrics(): QueueMetrics
	{
		return {
			queueSize: this.getGauge('queue_size') || 0,
			processingCount: this.getGauge('queue_processing') || 0,
			completedCount: this.getCounter('queue_completed_total') || 0,
			failedCount: this.getCounter('queue_failed_total') || 0,
			averageProcessingTime: this.getHistogramAverage('queue_processing_duration') || 0,
		};
	}

	/**
	 * Get crawler metrics
	 */
	getCrawlerMetrics(): CrawlerMetrics
	{
		return {
			domainsProcessed: this.getCounter('crawler_domains_total') || 0,
			successfulCrawls: this.getCounter('crawler_success_total') || 0,
			failedCrawls: this.getCounter('crawler_failed_total') || 0,
			averageCrawlTime: this.getHistogramAverage('crawler_duration') || 0,
			analysisCount: {
				dns: this.getCounter('crawler_dns_analysis_total') || 0,
				ssl: this.getCounter('crawler_ssl_analysis_total') || 0,
				homepage: this.getCounter('crawler_homepage_analysis_total') || 0,
				robots: this.getCounter('crawler_robots_analysis_total') || 0,
				performance: this.getCounter('crawler_performance_analysis_total') || 0,
			},
		};
	}

	/**
	 * Get ranking metrics
	 */
	getRankingMetrics(): RankingMetrics
	{
		return {
			rankingsCalculated: this.getCounter('ranking_calculations_total') || 0,
			averageCalculationTime: this.getHistogramAverage('ranking_calculation_duration') || 0,
			scoreDistribution: {
				'0.0-0.2': this.getCounter('ranking_score_0_2') || 0,
				'0.2-0.4': this.getCounter('ranking_score_2_4') || 0,
				'0.4-0.6': this.getCounter('ranking_score_4_6') || 0,
				'0.6-0.8': this.getCounter('ranking_score_6_8') || 0,
				'0.8-1.0': this.getCounter('ranking_score_8_10') || 0,
			},
			rankingUpdates: this.getCounter('ranking_updates_total') || 0,
		};
	}

	/**
	 * Get all metrics summary
	 */
	getMetricsSummary(): any
	{
		return {
			performance: this.getPerformanceMetrics(),
			database: this.getDatabaseMetrics(),
			queue: this.getQueueMetrics(),
			crawler: this.getCrawlerMetrics(),
			ranking: this.getRankingMetrics(),
			timestamp: new Date().toISOString(),
		};
	}

	/**
	 * Export metrics in Prometheus format
	 */
	exportPrometheusMetrics(): string
	{
		const lines: string[] = [];

		// Counters
		for (const [key, value] of this.counters.entries())
		{
			lines.push(`# TYPE ${key} counter`);
			lines.push(`${key} ${value}`);
		}

		// Gauges
		for (const [key, value] of this.gauges.entries())
		{
			lines.push(`# TYPE ${key} gauge`);
			lines.push(`${key} ${value}`);
		}

		// Histograms (simplified)
		for (const [key, values] of this.histograms.entries())
		{
			if (values.length > 0)
			{
				const sum = values.reduce((a, b) => a + b, 0);
				const count = values.length;

				lines.push(`# TYPE ${key} histogram`);
				lines.push(`${key}_sum ${sum}`);
				lines.push(`${key}_count ${count}`);
			}
		}

		return lines.join('\n');
	}

	/**
	 * Reset all metrics
	 */
	reset(): void
	{
		this.metrics.clear();
		this.counters.clear();
		this.gauges.clear();
		this.histograms.clear();
	}

	/**
	 * Private helper methods
	 */
	private createMetricKey(name: string, labels?: Record<string, string>): string
	{
		if (!labels || Object.keys(labels).length === 0) return name;

		const labelStr = Object.entries(labels)
			.map(([key, value]) => `${key}="${value}"`)
			.join(',');

		return `${name}{${labelStr}}`;
	}

	private recordMetric(name: string, value: number, type: MetricType, labels?: Record<string, string>): void
	{
		const key = this.createMetricKey(name, labels);
		const metrics = this.metrics.get(key) || [];

		metrics.push({
			value,
			timestamp: Date.now(),
			labels,
		});

		// Keep only last 1000 values
		if (metrics.length > 1000)
		{
			metrics.splice(0, metrics.length - 1000);
		}

		this.metrics.set(key, metrics);
	}

	private getCounter(name: string): number | undefined
	{
		return this.counters.get(name);
	}

	private getGauge(name: string): number | undefined
	{
		return this.gauges.get(name);
	}

	private getHistogramAverage(name: string): number | undefined
	{
		const values = this.histograms.get(name);
		if (!values || values.length === 0) return undefined;

		return values.reduce((a, b) => a + b, 0) / values.length;
	}

	private getEventLoopDelay(): number
	{
		// Simplified event loop delay measurement
		const start = process.hrtime.bigint();
		setImmediate(() =>
		{
			const delay = Number(process.hrtime.bigint() - start) / 1000000; // Convert to ms
			this.setGauge('event_loop_delay', delay);
		});

		return this.getGauge('event_loop_delay') || 0;
	}
}

/**
 * Calculate percentiles from array of values
 */
function calculatePercentiles(values: number[], percentiles: number[]): Record<string, number>
{
	if (values.length === 0) return {};

	const sorted = [...values].sort((a, b) => a - b);
	const result: Record<string, number> = {};

	for (const p of percentiles)
	{
		const index = Math.ceil((p / 100) * sorted.length) - 1;
		result[`p${p}`] = sorted[Math.max(0, index)];
	}

	return result;
}

/**
 * Calculate rate per second
 */
function calculateRate(count: number, timeWindowMs: number): number
{
	return (count / timeWindowMs) * 1000;
}

/**
 * Format bytes to human readable
 */
function formatBytes(bytes: number): string
{
	const units = ['B', 'KB', 'MB', 'GB', 'TB'];
	let size = bytes;
	let unitIndex = 0;

	while (size >= 1024 && unitIndex < units.length - 1)
	{
		size /= 1024;
		unitIndex++;
	}

	return `${size.toFixed(2)} ${units[unitIndex]}`;
}

/**
 * Format duration to human readable
 */
function formatDuration(ms: number): string
{
	if (ms < 1000) return `${ms.toFixed(2)}ms`;
	if (ms < 60000) return `${(ms / 1000).toFixed(2)}s`;
	if (ms < 3600000) return `${(ms / 60000).toFixed(2)}m`;
	return `${(ms / 3600000).toFixed(2)}h`;
}

/**
 * Create metrics aggregator
 */
function createMetricsAggregator(windowSizeMs = 60000)
{
	const windows = new Map<string, { values: number[]; timestamp: number }>();

	return {
		record: (name: string, value: number): void =>
		{
			const now = Date.now();
			const window = windows.get(name) || { values: [], timestamp: now };

			// Clean old values
			if (now - window.timestamp > windowSizeMs)
			{
				window.values = [];
				window.timestamp = now;
			}

			window.values.push(value);
			windows.set(name, window);
		},

		getStats: (name: string): { count: number; sum: number; avg: number; min: number; max: number } | null =>
		{
			const window = windows.get(name);
			if (!window || window.values.length === 0) return null;

			const values = window.values;
			const sum = values.reduce((a, b) => a + b, 0);

			return {
				count: values.length,
				sum,
				avg: sum / values.length,
				min: Math.min(...values),
				max: Math.max(...values),
			};
		},
	};
}

export type {
	MetricType,
	MetricValue,
	PerformanceMetrics,
	DatabaseMetrics,
	QueueMetrics,
	CrawlerMetrics,
	RankingMetrics,
};

export {
	WorkerMetricsCollector,
	calculatePercentiles,
	calculateRate,
	formatBytes,
	formatDuration,
	createMetricsAggregator,
};
