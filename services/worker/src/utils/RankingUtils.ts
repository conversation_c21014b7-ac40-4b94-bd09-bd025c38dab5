/**
 * Ranking Utilities
 * Helper functions for ranking calculations and analysis
 */

type ScoreComponents = {
	performance: number;
	security: number;
	seo: number;
	technical: number;
	backlinks: number;
};

type RankingWeights = {
	performance: number;
	security: number;
	seo: number;
	technical: number;
	backlinks: number;
};

type RankingGrade = 'A+' | 'A' | 'B+' | 'B' | 'C+' | 'C' | 'D+' | 'D' | 'F';

/**
 * Calculate composite score from components
 */
function calculateCompositeScore(scores: ScoreComponents, weights: RankingWeights): number
{
	const weightedSum =
		scores.performance * weights.performance
		+ scores.security * weights.security
		+ scores.seo * weights.seo
		+ scores.technical * weights.technical
		+ scores.backlinks * weights.backlinks;

	// Ensure weights sum to 1
	const totalWeight = Object.values(weights).reduce((sum, weight) => sum + weight, 0);

	return Math.min(1, Math.max(0, weightedSum / totalWeight));
}

/**
 * Convert score to letter grade
 */
function scoreToGrade(score: number): RankingGrade
{
	if (score >= 0.97) return 'A+';
	if (score >= 0.93) return 'A';
	if (score >= 0.87) return 'B+';
	if (score >= 0.83) return 'B';
	if (score >= 0.77) return 'C+';
	if (score >= 0.73) return 'C';
	if (score >= 0.67) return 'D+';
	if (score >= 0.60) return 'D';
	return 'F';
}

/**
 * Convert grade to score range
 */
function gradeToScoreRange(grade: RankingGrade): { min: number; max: number }
{
	const ranges: Record<RankingGrade, { min: number; max: number }> = {
		'A+': { min: 0.97, max: 1.00 },
		'A': { min: 0.93, max: 0.96 },
		'B+': { min: 0.87, max: 0.92 },
		'B': { min: 0.83, max: 0.86 },
		'C+': { min: 0.77, max: 0.82 },
		'C': { min: 0.73, max: 0.76 },
		'D+': { min: 0.67, max: 0.72 },
		'D': { min: 0.60, max: 0.66 },
		'F': { min: 0.00, max: 0.59 },
	};

	return ranges[grade];
}

/**
 * Calculate performance score from metrics
 */
function calculatePerformanceScore(metrics: {
	loadTime?: number;
	firstContentfulPaint?: number;
	largestContentfulPaint?: number;
	cumulativeLayoutShift?: number;
	firstInputDelay?: number;
	speedIndex?: number;
}): number
{
	let score = 0;
	let count = 0;

	// Load time (target: < 3s)
	if (metrics.loadTime !== undefined)
	{
		score += Math.max(0, 1 - (metrics.loadTime / 3000));
		count++;
	}

	// First Contentful Paint (target: < 1.8s)
	if (metrics.firstContentfulPaint !== undefined)
	{
		score += Math.max(0, 1 - (metrics.firstContentfulPaint / 1800));
		count++;
	}

	// Largest Contentful Paint (target: < 2.5s)
	if (metrics.largestContentfulPaint !== undefined)
	{
		score += Math.max(0, 1 - (metrics.largestContentfulPaint / 2500));
		count++;
	}

	// Cumulative Layout Shift (target: < 0.1)
	if (metrics.cumulativeLayoutShift !== undefined)
	{
		score += Math.max(0, 1 - (metrics.cumulativeLayoutShift / 0.1));
		count++;
	}

	// First Input Delay (target: < 100ms)
	if (metrics.firstInputDelay !== undefined)
	{
		score += Math.max(0, 1 - (metrics.firstInputDelay / 100));
		count++;
	}

	// Speed Index (target: < 3.4s)
	if (metrics.speedIndex !== undefined)
	{
		score += Math.max(0, 1 - (metrics.speedIndex / 3400));
		count++;
	}

	return count > 0 ? score / count : 0;
}

/**
 * Calculate security score from analysis
 */
function calculateSecurityScore(analysis: {
	sslGrade?: string;
	hasHttps?: boolean;
	securityHeaders?: Record<string, boolean>;
	vulnerabilities?: string[];
}): number
{
	let score = 0;

	// SSL Grade (40% of security score)
	if (analysis.sslGrade)
	{
		const sslScores: Record<string, number> = {
			'A+': 1.0,
			'A': 0.9,
			'B': 0.7,
			'C': 0.5,
			'D': 0.3,
			'F': 0.0,
		};
		score += (sslScores[analysis.sslGrade] || 0) * 0.4;
	}

	// HTTPS availability (20% of security score)
	if (analysis.hasHttps)
	{
		score += 0.2;
	}

	// Security headers (30% of security score)
	if (analysis.securityHeaders)
	{
		const importantHeaders = [
			'strict-transport-security',
			'content-security-policy',
			'x-frame-options',
			'x-content-type-options',
			'referrer-policy',
		];

		const presentHeaders = importantHeaders.filter(header => analysis.securityHeaders![header]);
		score += (presentHeaders.length / importantHeaders.length) * 0.3;
	}

	// Vulnerabilities penalty (10% of security score)
	if (analysis.vulnerabilities)
	{
		const vulnerabilityPenalty = Math.min(analysis.vulnerabilities.length * 0.02, 0.1);
		score += Math.max(0, 0.1 - vulnerabilityPenalty);
	}
	else
	{
		score += 0.1; // No vulnerabilities found
	}

	return Math.min(1, Math.max(0, score));
}

/**
 * Calculate SEO score from analysis
 */
function calculateSEOScore(analysis: {
	title?: string;
	metaDescription?: string;
	headings?: Record<string, string[]>;
	images?: Array<{ alt?: string }>;
	internalLinks?: number;
	externalLinks?: number;
	robotsTxt?: boolean;
	sitemap?: boolean;
}): number
{
	let score = 0;

	// Title tag (15% of SEO score)
	if (analysis.title && analysis.title.length >= 10 && analysis.title.length <= 60)
	{
		score += 0.15;
	}

	// Meta description (15% of SEO score)
	if (analysis.metaDescription && analysis.metaDescription.length >= 120 && analysis.metaDescription.length <= 160)
	{
		score += 0.15;
	}

	// Heading structure (20% of SEO score)
	if (analysis.headings)
	{
		const hasH1 = analysis.headings.h1 && analysis.headings.h1.length > 0;
		const hasMultipleHeadings = Object.values(analysis.headings).flat().length > 1;

		if (hasH1) score += 0.1;
		if (hasMultipleHeadings) score += 0.1;
	}

	// Image alt text (15% of SEO score)
	if (analysis.images && analysis.images.length > 0)
	{
		const imagesWithAlt = analysis.images.filter(img => img.alt && img.alt.length > 0);
		score += (imagesWithAlt.length / analysis.images.length) * 0.15;
	}
	else
	{
		score += 0.15; // No images to optimize
	}

	// Internal linking (10% of SEO score)
	if (analysis.internalLinks !== undefined)
	{
		const linkScore = Math.min(analysis.internalLinks / 10, 1); // Target: 10+ internal links
		score += linkScore * 0.1;
	}

	// External linking (10% of SEO score)
	if (analysis.externalLinks !== undefined)
	{
		const linkScore = Math.min(analysis.externalLinks / 5, 1); // Target: 5+ external links
		score += linkScore * 0.1;
	}

	// Robots.txt (7.5% of SEO score)
	if (analysis.robotsTxt)
	{
		score += 0.075;
	}

	// Sitemap (7.5% of SEO score)
	if (analysis.sitemap)
	{
		score += 0.075;
	}

	return Math.min(1, Math.max(0, score));
}

/**
 * Calculate technical score from analysis
 */
function calculateTechnicalScore(analysis: {
	dnsResponseTime?: number;
	serverResponseTime?: number;
	compressionEnabled?: boolean;
	cachingEnabled?: boolean;
	minifiedResources?: boolean;
	mobileOptimized?: boolean;
	validHtml?: boolean;
}): number
{
	let score = 0;

	// DNS response time (15% of technical score)
	if (analysis.dnsResponseTime !== undefined)
	{
		score += Math.max(0, 1 - (analysis.dnsResponseTime / 200)) * 0.15; // Target: < 200ms
	}

	// Server response time (20% of technical score)
	if (analysis.serverResponseTime !== undefined)
	{
		score += Math.max(0, 1 - (analysis.serverResponseTime / 500)) * 0.2; // Target: < 500ms
	}

	// Compression (15% of technical score)
	if (analysis.compressionEnabled)
	{
		score += 0.15;
	}

	// Caching (15% of technical score)
	if (analysis.cachingEnabled)
	{
		score += 0.15;
	}

	// Minified resources (10% of technical score)
	if (analysis.minifiedResources)
	{
		score += 0.1;
	}

	// Mobile optimization (15% of technical score)
	if (analysis.mobileOptimized)
	{
		score += 0.15;
	}

	// Valid HTML (10% of technical score)
	if (analysis.validHtml)
	{
		score += 0.1;
	}

	return Math.min(1, Math.max(0, score));
}

/**
 * Calculate backlink score from metrics
 */
function calculateBacklinkScore(metrics: {
	totalBacklinks?: number;
	uniqueDomains?: number;
	domainAuthority?: number;
	anchorTextDiversity?: number;
	followLinks?: number;
	nofollowLinks?: number;
}): number
{
	let score = 0;

	// Total backlinks (25% of backlink score)
	if (metrics.totalBacklinks !== undefined)
	{
		const backlinkScore = Math.min(Math.log10(metrics.totalBacklinks + 1) / 4, 1); // Logarithmic scale
		score += backlinkScore * 0.25;
	}

	// Unique domains (25% of backlink score)
	if (metrics.uniqueDomains !== undefined)
	{
		const domainScore = Math.min(Math.log10(metrics.uniqueDomains + 1) / 3, 1); // Logarithmic scale
		score += domainScore * 0.25;
	}

	// Domain authority (30% of backlink score)
	if (metrics.domainAuthority !== undefined)
	{
		score += (metrics.domainAuthority / 100) * 0.3;
	}

	// Anchor text diversity (10% of backlink score)
	if (metrics.anchorTextDiversity !== undefined)
	{
		score += Math.min(metrics.anchorTextDiversity, 1) * 0.1;
	}

	// Follow vs nofollow ratio (10% of backlink score)
	if (metrics.followLinks !== undefined && metrics.nofollowLinks !== undefined)
	{
		const totalLinks = metrics.followLinks + metrics.nofollowLinks;
		if (totalLinks > 0)
		{
			const followRatio = metrics.followLinks / totalLinks;
			score += followRatio * 0.1;
		}
	}

	return Math.min(1, Math.max(0, score));
}

/**
 * Normalize scores to ensure they're within valid range
 */
function normalizeScores(scores: ScoreComponents): ScoreComponents
{
	return {
		performance: Math.min(1, Math.max(0, scores.performance)),
		security: Math.min(1, Math.max(0, scores.security)),
		seo: Math.min(1, Math.max(0, scores.seo)),
		technical: Math.min(1, Math.max(0, scores.technical)),
		backlinks: Math.min(1, Math.max(0, scores.backlinks)),
	};
}

/**
 * Calculate ranking change trend
 */
function calculateRankingTrend(rankings: Array<{ rank: number; timestamp: number }>): {
	trend: 'improving' | 'declining' | 'stable';
	change: number;
	velocity: number;
}
{
	if (rankings.length < 2)
	{
		return { trend: 'stable', change: 0, velocity: 0 };
	}

	// Sort by timestamp
	const sorted = rankings.sort((a, b) => a.timestamp - b.timestamp);
	const latest = sorted[sorted.length - 1];
	const previous = sorted[sorted.length - 2];

	const change = previous.rank - latest.rank; // Positive = improvement (lower rank number)
	const timeDiff = latest.timestamp - previous.timestamp;
	const velocity = timeDiff > 0 ? change / (timeDiff / (24 * 60 * 60 * 1000)) : 0; // Change per day

	let trend: 'improving' | 'declining' | 'stable' = 'stable';
	if (Math.abs(change) > 5) // Significant change threshold
	{
		trend = change > 0 ? 'improving' : 'declining';
	}

	return { trend, change, velocity };
}

/**
 * Get default ranking weights
 */
function getDefaultRankingWeights(): RankingWeights
{
	return {
		performance: 0.25,
		security: 0.20,
		seo: 0.20,
		technical: 0.15,
		backlinks: 0.20,
	};
}

/**
 * Validate ranking weights
 */
function validateRankingWeights(weights: RankingWeights): { valid: boolean; error?: string }
{
	const sum = Object.values(weights).reduce((total, weight) => total + weight, 0);

	if (Math.abs(sum - 1.0) > 0.01)
	{
		return { valid: false, error: 'Weights must sum to 1.0' };
	}

	for (const [key, weight] of Object.entries(weights))
	{
		if (weight < 0 || weight > 1)
		{
			return { valid: false, error: `Weight for ${key} must be between 0 and 1` };
		}
	}

	return { valid: true };
}

/**
 * Calculate percentile rank
 */
function calculatePercentileRank(score: number, allScores: number[]): number
{
	if (allScores.length === 0) return 0;

	const sorted = allScores.sort((a, b) => a - b);
	const rank = sorted.findIndex(s => s >= score);

	if (rank === -1) return 100; // Score is higher than all others

	return (rank / sorted.length) * 100;
}

export type {
	ScoreComponents,
	RankingWeights,
	RankingGrade,
};

export {
	calculateCompositeScore,
	scoreToGrade,
	gradeToScoreRange,
	calculatePerformanceScore,
	calculateSecurityScore,
	calculateSEOScore,
	calculateTechnicalScore,
	calculateBacklinkScore,
	normalizeScores,
	calculateRankingTrend,
	getDefaultRankingWeights,
	validateRankingWeights,
	calculatePercentileRank,
};
