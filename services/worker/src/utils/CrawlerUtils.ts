/**
 * Crawler Utilities
 * Helper functions specific to crawling operations
 */

type CrawlResult = {
	domain: string;
	url: string;
	statusCode: number;
	headers: Record<string, string>;
	content: string;
	loadTime: number;
	timestamp: number;
};

type RobotsRule = {
	userAgent: string;
	disallow: string[];
	allow: string[];
	crawlDelay?: number;
};

/**
 * Parse robots.txt content
 */
function parseRobotsTxt(content: string): {
	rules: RobotsRule[];
	sitemaps: string[];
}
{
	const lines = content.split('\n').map(line => line.trim());
	const rules: RobotsRule[] = [];
	const sitemaps: string[] = [];
	let currentRule: Partial<RobotsRule> | null = null;

	for (const line of lines)
	{
		if (line.startsWith('#') || line === '') continue;

		const [key, ...valueParts] = line.split(':');
		const value = valueParts.join(':').trim();

		switch (key.toLowerCase())
		{
			case 'user-agent':
				if (currentRule)
				{
					rules.push(currentRule as RobotsRule);
				}
				currentRule = {
					userAgent: value,
					disallow: [],
					allow: [],
				};
				break;

			case 'disallow':
				if (currentRule)
				{
					currentRule.disallow!.push(value);
				}
				break;

			case 'allow':
				if (currentRule)
				{
					currentRule.allow!.push(value);
				}
				break;

			case 'crawl-delay':
				if (currentRule)
				{
					currentRule.crawlDelay = parseInt(value, 10);
				}
				break;

			case 'sitemap':
				sitemaps.push(value);
				break;
		}
	}

	if (currentRule)
	{
		rules.push(currentRule as RobotsRule);
	}

	return { rules, sitemaps };
}

/**
 * Check if URL is allowed by robots.txt
 */
function isAllowedByRobots(url: string, rules: RobotsRule[], userAgent = '*'): boolean
{
	const path = new URL(url).pathname;

	// Find applicable rules
	const applicableRules = rules.filter(rule =>
		rule.userAgent === '*' ||
		rule.userAgent.toLowerCase() === userAgent.toLowerCase());

	if (applicableRules.length === 0) return true;

	// Check rules in order
	for (const rule of applicableRules)
	{
		// Check disallow rules
		for (const disallowPath of rule.disallow)
		{
			if (disallowPath === '' || path.startsWith(disallowPath))
			{
				// Check if there's a more specific allow rule
				for (const allowPath of rule.allow)
				{
					if (allowPath !== '' && path.startsWith(allowPath))
					{
						return true;
					}
				}
				return false;
			}
		}

		// Check allow rules
		for (const allowPath of rule.allow)
		{
			if (allowPath !== '' && path.startsWith(allowPath))
			{
				return true;
			}
		}
	}

	return true;
}

/**
 * Extract meta tags from HTML
 */
function extractMetaTags(html: string): Record<string, string>
{
	const metaTags: Record<string, string> = {};
	const metaRegex = /<meta\s+([^>]*?)>/gi;
	let match;

	while ((match = metaRegex.exec(html)) !== null)
	{
		const attributes = match[1];
		const nameMatch = attributes.match(/name\s*=\s*["']([^"']+)["']/i);
		const propertyMatch = attributes.match(/property\s*=\s*["']([^"']+)["']/i);
		const contentMatch = attributes.match(/content\s*=\s*["']([^"']*?)["']/i);

		if (contentMatch)
		{
			const content = contentMatch[1];

			if (nameMatch)
			{
				metaTags[nameMatch[1].toLowerCase()] = content;
			}
			else if (propertyMatch)
			{
				metaTags[propertyMatch[1].toLowerCase()] = content;
			}
		}
	}

	return metaTags;
}

/**
 * Extract title from HTML
 */
function extractTitle(html: string): string | null
{
	const titleMatch = html.match(/<title[^>]*>([^<]*)<\/title>/i);
	return titleMatch ? titleMatch[1].trim() : null;
}

/**
 * Extract headings from HTML
 */
function extractHeadings(html: string): Record<string, string[]>
{
	const headings: Record<string, string[]> = {
		h1: [],
		h2: [],
		h3: [],
		h4: [],
		h5: [],
		h6: [],
	};

	for (let i = 1; i <= 6; i++)
	{
		const regex = new RegExp(`<h${i}[^>]*>([^<]*)</h${i}>`, 'gi');
		let match;

		while ((match = regex.exec(html)) !== null)
		{
			headings[`h${i}`].push(match[1].trim());
		}
	}

	return headings;
}

/**
 * Extract links from HTML
 */
function extractLinks(html: string, baseUrl: string): {
	internal: string[];
	external: string[];
	all: string[];
}
{
	const links: string[] = [];
	const linkRegex = /<a\s+[^>]*href\s*=\s*["']([^"']+)["'][^>]*>/gi;
	let match;

	while ((match = linkRegex.exec(html)) !== null)
	{
		const href = match[1];

		try
		{
			const url = new URL(href, baseUrl);
			links.push(url.href);
		}
		catch
		{
			// Invalid URL, skip
		}
	}

	const baseDomain = new URL(baseUrl).hostname;
	const internal = links.filter((link) =>
	{
		try
		{
			return new URL(link).hostname === baseDomain;
		}
		catch
		{
			return false;
		}
	});

	const external = links.filter((link) =>
	{
		try
		{
			return new URL(link).hostname !== baseDomain;
		}
		catch
		{
			return false;
		}
	});

	return { internal, external, all: links };
}

/**
 * Extract images from HTML
 */
function extractImages(html: string, baseUrl: string): Array<{
	src: string;
	alt?: string;
	title?: string;
}>
{
	const images: Array<{ src: string; alt?: string; title?: string }> = [];
	const imgRegex = /<img\s+([^>]*?)>/gi;
	let match;

	while ((match = imgRegex.exec(html)) !== null)
	{
		const attributes = match[1];
		const srcMatch = attributes.match(/src\s*=\s*["']([^"']+)["']/i);
		const altMatch = attributes.match(/alt\s*=\s*["']([^"']*?)["']/i);
		const titleMatch = attributes.match(/title\s*=\s*["']([^"']*?)["']/i);

		if (srcMatch)
		{
			try
			{
				const src = new URL(srcMatch[1], baseUrl).href;
				images.push({
					src,
					alt: altMatch ? altMatch[1] : undefined,
					title: titleMatch ? titleMatch[1] : undefined,
				});
			}
			catch
			{
				// Invalid URL, skip
			}
		}
	}

	return images;
}

/**
 * Calculate content quality score
 */
function calculateContentQuality(html: string): {
	score: number;
	factors: Record<string, number>;
}
{
	const factors: Record<string, number> = {};

	// Text content ratio
	const textContent = html.replace(/<[^>]*>/g, '').trim();
	const htmlLength = html.length;
	const textRatio = textContent.length / htmlLength;
	factors.textRatio = Math.min(textRatio * 2, 1); // Max 1 point

	// Word count
	const wordCount = textContent.split(/\s+/).filter(word => word.length > 0).length;
	factors.wordCount = Math.min(wordCount / 500, 1); // Max 1 point for 500+ words

	// Heading structure
	const headings = extractHeadings(html);
	const hasH1 = headings.h1.length > 0;
	const hasMultipleHeadings = Object.values(headings).flat().length > 1;
	factors.headingStructure = (hasH1 ? 0.5 : 0) + (hasMultipleHeadings ? 0.5 : 0);

	// Meta tags
	const metaTags = extractMetaTags(html);
	const hasDescription = 'description' in metaTags && metaTags.description.length > 0;
	const hasKeywords = 'keywords' in metaTags && metaTags.keywords.length > 0;
	factors.metaTags = (hasDescription ? 0.5 : 0) + (hasKeywords ? 0.5 : 0);

	// Images with alt text
	const images = extractImages(html, 'http://example.com');
	const imagesWithAlt = images.filter(img => img.alt && img.alt.length > 0).length;
	const altTextRatio = images.length > 0 ? imagesWithAlt / images.length : 1;
	factors.altTextRatio = altTextRatio;

	// Calculate overall score
	const totalScore = Object.values(factors).reduce((sum, score) => sum + score, 0);
	const maxScore = Object.keys(factors).length;
	const normalizedScore = totalScore / maxScore;

	return {
		score: Math.round(normalizedScore * 100) / 100,
		factors,
	};
}

/**
 * Detect technologies from HTML
 */
function detectTechnologies(html: string, headers: Record<string, string>): string[]
{
	const technologies: string[] = [];

	// Check headers
	const serverHeader = headers.server?.toLowerCase() || '';
	const poweredByHeader = headers['x-powered-by']?.toLowerCase() || '';

	if (serverHeader.includes('nginx')) technologies.push('Nginx');
	if (serverHeader.includes('apache')) technologies.push('Apache');
	if (serverHeader.includes('iis')) technologies.push('IIS');
	if (poweredByHeader.includes('php')) technologies.push('PHP');
	if (poweredByHeader.includes('asp.net')) technologies.push('ASP.NET');

	// Check HTML content
	const htmlLower = html.toLowerCase();

	// JavaScript frameworks
	if (htmlLower.includes('react')) technologies.push('React');
	if (htmlLower.includes('angular')) technologies.push('Angular');
	if (htmlLower.includes('vue')) technologies.push('Vue.js');
	if (htmlLower.includes('jquery')) technologies.push('jQuery');

	// CMS
	if (htmlLower.includes('wp-content') || htmlLower.includes('wordpress')) technologies.push('WordPress');
	if (htmlLower.includes('drupal')) technologies.push('Drupal');
	if (htmlLower.includes('joomla')) technologies.push('Joomla');

	// Analytics
	if (htmlLower.includes('google-analytics') || htmlLower.includes('gtag')) technologies.push('Google Analytics');
	if (htmlLower.includes('gtm')) technologies.push('Google Tag Manager');

	// CDNs
	if (htmlLower.includes('cloudflare')) technologies.push('Cloudflare');
	if (htmlLower.includes('amazonaws')) technologies.push('AWS');

	return [...new Set(technologies)]; // Remove duplicates
}

/**
 * Generate crawl delay based on robots.txt
 */
function getCrawlDelay(rules: RobotsRule[], userAgent = '*', defaultDelay = 1000): number
{
	const applicableRules = rules.filter(rule =>
		rule.userAgent === '*' ||
		rule.userAgent.toLowerCase() === userAgent.toLowerCase());

	for (const rule of applicableRules)
	{
		if (rule.crawlDelay !== undefined)
		{
			return rule.crawlDelay * 1000; // Convert to milliseconds
		}
	}

	return defaultDelay;
}

/**
 * Normalize URL for crawling
 */
function normalizeUrl(url: string): string
{
	try
	{
		const parsed = new URL(url);

		// Remove fragment
		parsed.hash = '';

		// Sort query parameters
		const params = new URLSearchParams(parsed.search);
		const sortedParams = new URLSearchParams();

		Array.from(params.keys()).sort().forEach((key) =>
		{
			params.getAll(key).forEach((value) =>
			{
				sortedParams.append(key, value);
			});
		});

		parsed.search = sortedParams.toString();

		// Remove trailing slash for non-root paths
		if (parsed.pathname !== '/' && parsed.pathname.endsWith('/'))
		{
			parsed.pathname = parsed.pathname.slice(0, -1);
		}

		return parsed.href;
	}
	catch
	{
		return url;
	}
}

/**
 * Check if URL should be crawled
 */
function shouldCrawlUrl(url: string, options: {
	allowedDomains?: string[];
	blockedPaths?: string[];
	allowedExtensions?: string[];
	maxDepth?: number;
	currentDepth?: number;
} = {}): boolean
{
	const {
		allowedDomains = [],
		blockedPaths = [],
		allowedExtensions = [],
		maxDepth = 10,
		currentDepth = 0,
	} = options;

	try
	{
		const parsed = new URL(url);

		// Check depth
		if (currentDepth >= maxDepth) return false;

		// Check allowed domains
		if (allowedDomains.length > 0 && !allowedDomains.includes(parsed.hostname))
		{
			return false;
		}

		// Check blocked paths
		for (const blockedPath of blockedPaths)
		{
			if (parsed.pathname.startsWith(blockedPath))
			{
				return false;
			}
		}

		// Check allowed extensions
		if (allowedExtensions.length > 0)
		{
			const extension = parsed.pathname.split('.').pop()?.toLowerCase();
			if (extension && !allowedExtensions.includes(extension))
			{
				return false;
			}
		}

		return true;
	}
	catch
	{
		return false;
	}
}

export type { CrawlResult, RobotsRule };

export {
	parseRobotsTxt,
	isAllowedByRobots,
	extractMetaTags,
	extractTitle,
	extractHeadings,
	extractLinks,
	extractImages,
	calculateContentQuality,
	detectTechnologies,
	getCrawlDelay,
	normalizeUrl,
	shouldCrawlUrl,
};
