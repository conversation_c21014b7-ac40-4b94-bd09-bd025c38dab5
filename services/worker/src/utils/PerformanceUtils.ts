/**
 * Performance Utilities
 * Performance monitoring and optimization helpers
 */

type PerformanceEntry = {
	name: string;
	startTime: number;
	endTime?: number;
	duration?: number;
	metadata?: Record<string, any>;
};

type PerformanceStats = {
	count: number;
	totalDuration: number;
	averageDuration: number;
	minDuration: number;
	maxDuration: number;
	p50: number;
	p95: number;
	p99: number;
};

/**
 * Performance timer for measuring execution time
 */
class PerformanceTimer
{
	private entries: Map<string, PerformanceEntry>;
	private completedEntries: PerformanceEntry[];

	constructor()
	{
		this.entries = new Map();
		this.completedEntries = [];
	}

	/**
	 * Start timing an operation
	 */
	start(name: string, metadata?: Record<string, any>): void
	{
		const entry: PerformanceEntry = {
			name,
			startTime: performance.now(),
			metadata,
		};

		this.entries.set(name, entry);
	}

	/**
	 * End timing an operation
	 */
	end(name: string): number
	{
		const entry = this.entries.get(name);
		if (!entry)
		{
			throw new Error(`No timer found for operation: ${name}`);
		}

		const endTime = performance.now();
		const duration = endTime - entry.startTime;

		entry.endTime = endTime;
		entry.duration = duration;

		this.completedEntries.push(entry);
		this.entries.delete(name);

		return duration;
	}

	/**
	 * Time a function execution
	 */
	async timeFunction<T>(name: string, fn: () => Promise<T>, metadata?: Record<string, any>): Promise<{ result: T; duration: number }>
	{
		this.start(name, metadata);

		try
		{
			const result = await fn();
			const duration = this.end(name);
			return { result, duration };
		}
		catch (error)
		{
			this.end(name);
			throw error;
		}
	}

	/**
	 * Get statistics for completed operations
	 */
	getStats(operationName?: string): PerformanceStats | null
	{
		const entries = operationName
			? this.completedEntries.filter(e => e.name === operationName)
			: this.completedEntries;

		if (entries.length === 0) return null;

		const durations = entries
			.map(e => e.duration!)
			.filter(d => d !== undefined)
			.sort((a, b) => a - b);

		const totalDuration = durations.reduce((sum, d) => sum + d, 0);

		return {
			count: durations.length,
			totalDuration,
			averageDuration: totalDuration / durations.length,
			minDuration: durations[0],
			maxDuration: durations[durations.length - 1],
			p50: this.percentile(durations, 50),
			p95: this.percentile(durations, 95),
			p99: this.percentile(durations, 99),
		};
	}

	/**
	 * Get all completed entries
	 */
	getEntries(operationName?: string): PerformanceEntry[]
	{
		return operationName
			? this.completedEntries.filter(e => e.name === operationName)
			: [...this.completedEntries];
	}

	/**
	 * Clear completed entries
	 */
	clear(): void
	{
		this.completedEntries = [];
	}

	/**
	 * Calculate percentile
	 */
	private percentile(values: number[], p: number): number
	{
		if (values.length === 0) return 0;

		const index = Math.ceil((p / 100) * values.length) - 1;
		return values[Math.max(0, index)];
	}
}

/**
 * Memory usage monitor
 */
class MemoryMonitor
{
	private snapshots: Array<{ timestamp: number; usage: NodeJS.MemoryUsage }>;
	private maxSnapshots: number;

	constructor(maxSnapshots = 100)
	{
		this.snapshots = [];
		this.maxSnapshots = maxSnapshots;
	}

	/**
	 * Take memory snapshot
	 */
	snapshot(): NodeJS.MemoryUsage
	{
		const usage = process.memoryUsage();

		this.snapshots.push({
			timestamp: Date.now(),
			usage,
		});

		// Keep only recent snapshots
		if (this.snapshots.length > this.maxSnapshots)
		{
			this.snapshots.shift();
		}

		return usage;
	}

	/**
	 * Get memory usage statistics
	 */
	getStats(): {
		current: NodeJS.MemoryUsage;
		peak: NodeJS.MemoryUsage;
		average: NodeJS.MemoryUsage;
		trend: 'increasing' | 'decreasing' | 'stable';
	}
	{
		if (this.snapshots.length === 0)
		{
			const current = this.snapshot();
			return {
				current,
				peak: current,
				average: current,
				trend: 'stable',
			};
		}

		const current = this.snapshots[this.snapshots.length - 1].usage;

		// Calculate peak usage
		const peak = this.snapshots.reduce((max, snapshot) =>
			({
				rss: Math.max(max.rss, snapshot.usage.rss),
				heapTotal: Math.max(max.heapTotal, snapshot.usage.heapTotal),
				heapUsed: Math.max(max.heapUsed, snapshot.usage.heapUsed),
				external: Math.max(max.external, snapshot.usage.external),
				arrayBuffers: Math.max(max.arrayBuffers, snapshot.usage.arrayBuffers),
			}), { rss: 0, heapTotal: 0, heapUsed: 0, external: 0, arrayBuffers: 0 });

		// Calculate average usage
		const sum = this.snapshots.reduce((acc, snapshot) =>
			({
				rss: acc.rss + snapshot.usage.rss,
				heapTotal: acc.heapTotal + snapshot.usage.heapTotal,
				heapUsed: acc.heapUsed + snapshot.usage.heapUsed,
				external: acc.external + snapshot.usage.external,
				arrayBuffers: acc.arrayBuffers + snapshot.usage.arrayBuffers,
			}), { rss: 0, heapTotal: 0, heapUsed: 0, external: 0, arrayBuffers: 0 });

		const count = this.snapshots.length;
		const average = {
			rss: sum.rss / count,
			heapTotal: sum.heapTotal / count,
			heapUsed: sum.heapUsed / count,
			external: sum.external / count,
			arrayBuffers: sum.arrayBuffers / count,
		};

		// Calculate trend
		const trend = this.calculateTrend();

		return { current, peak, average, trend };
	}

	/**
	 * Calculate memory usage trend
	 */
	private calculateTrend(): 'increasing' | 'decreasing' | 'stable'
	{
		if (this.snapshots.length < 3) return 'stable';

		const recent = this.snapshots.slice(-5);
		const first = recent[0].usage.heapUsed;
		const last = recent[recent.length - 1].usage.heapUsed;

		const change = (last - first) / first;

		if (change > 0.1) return 'increasing';
		if (change < -0.1) return 'decreasing';
		return 'stable';
	}

	/**
	 * Force garbage collection if available
	 */
	forceGC(): boolean
	{
		if (global.gc)
		{
			global.gc();
			return true;
		}
		return false;
	}
}

/**
 * CPU usage monitor
 */
class CpuMonitor
{
	private lastUsage: NodeJS.CpuUsage;
	private lastTime: number;
	private samples: Array<{ timestamp: number; usage: number }>;

	constructor()
	{
		this.lastUsage = process.cpuUsage();
		this.lastTime = Date.now();
		this.samples = [];
	}

	/**
	 * Get current CPU usage percentage
	 */
	getCurrentUsage(): number
	{
		const currentUsage = process.cpuUsage(this.lastUsage);
		const currentTime = Date.now();
		const timeDiff = currentTime - this.lastTime;

		// Calculate CPU percentage
		const cpuPercent = (currentUsage.user + currentUsage.system) / (timeDiff * 1000);

		this.lastUsage = process.cpuUsage();
		this.lastTime = currentTime;

		// Store sample
		this.samples.push({ timestamp: currentTime, usage: cpuPercent });

		// Keep only recent samples
		if (this.samples.length > 100)
		{
			this.samples.shift();
		}

		return cpuPercent;
	}

	/**
	 * Get CPU usage statistics
	 */
	getStats(): {
		current: number;
		average: number;
		peak: number;
		samples: number;
	}
	{
		const current = this.getCurrentUsage();

		if (this.samples.length === 0)
		{
			return { current, average: current, peak: current, samples: 1 };
		}

		const usages = this.samples.map(s => s.usage);
		const sum = usages.reduce((a, b) => a + b, 0);

		return {
			current,
			average: sum / usages.length,
			peak: Math.max(...usages),
			samples: usages.length,
		};
	}
}

/**
 * Event loop lag monitor
 */
class EventLoopMonitor
{
	private samples: number[];
	private intervalId?: NodeJS.Timeout;

	constructor()
	{
		this.samples = [];
	}

	/**
	 * Start monitoring event loop lag
	 */
	start(intervalMs = 1000): void
	{
		if (this.intervalId) return;

		this.intervalId = setInterval(() =>
		{
			const start = process.hrtime.bigint();

			setImmediate(() =>
			{
				const lag = Number(process.hrtime.bigint() - start) / 1000000; // Convert to ms
				this.samples.push(lag);

				// Keep only recent samples
				if (this.samples.length > 100)
				{
					this.samples.shift();
				}
			});
		}, intervalMs);
	}

	/**
	 * Stop monitoring
	 */
	stop(): void
	{
		if (this.intervalId)
		{
			clearInterval(this.intervalId);
			this.intervalId = undefined;
		}
	}

	/**
	 * Get event loop lag statistics
	 */
	getStats(): {
		current: number;
		average: number;
		peak: number;
		p95: number;
		samples: number;
	}
	{
		if (this.samples.length === 0)
		{
			return { current: 0, average: 0, peak: 0, p95: 0, samples: 0 };
		}

		const sorted = [...this.samples].sort((a, b) => a - b);
		const sum = sorted.reduce((a, b) => a + b, 0);

		return {
			current: sorted[sorted.length - 1],
			average: sum / sorted.length,
			peak: sorted[sorted.length - 1],
			p95: sorted[Math.floor(sorted.length * 0.95)],
			samples: sorted.length,
		};
	}
}

/**
 * Performance profiler
 */
class PerformanceProfiler
{
	private timer: PerformanceTimer;
	private memoryMonitor: MemoryMonitor;
	private cpuMonitor: CpuMonitor;
	private eventLoopMonitor: EventLoopMonitor;

	constructor()
	{
		this.timer = new PerformanceTimer();
		this.memoryMonitor = new MemoryMonitor();
		this.cpuMonitor = new CpuMonitor();
		this.eventLoopMonitor = new EventLoopMonitor();
	}

	/**
	 * Start profiling
	 */
	start(): void
	{
		this.eventLoopMonitor.start();
	}

	/**
	 * Stop profiling
	 */
	stop(): void
	{
		this.eventLoopMonitor.stop();
	}

	/**
	 * Profile a function execution
	 */
	async profile<T>(name: string, fn: () => Promise<T>): Promise<{
		result: T;
		performance: {
			duration: number;
			memoryBefore: NodeJS.MemoryUsage;
			memoryAfter: NodeJS.MemoryUsage;
			cpuUsage: number;
		};
	}>
	{
		const memoryBefore = this.memoryMonitor.snapshot();
		const cpuBefore = this.cpuMonitor.getCurrentUsage();

		const { result, duration } = await this.timer.timeFunction(name, fn);

		const memoryAfter = this.memoryMonitor.snapshot();
		const cpuAfter = this.cpuMonitor.getCurrentUsage();

		return {
			result,
			performance: {
				duration,
				memoryBefore,
				memoryAfter,
				cpuUsage: (cpuBefore + cpuAfter) / 2,
			},
		};
	}

	/**
	 * Get comprehensive performance report
	 */
	getReport(): {
		timing: Record<string, PerformanceStats>;
		memory: ReturnType<MemoryMonitor['getStats']>;
		cpu: ReturnType<CpuMonitor['getStats']>;
		eventLoop: ReturnType<EventLoopMonitor['getStats']>;
	}
	{
		// Get timing stats for all operations
		const operations = new Set(this.timer.getEntries().map(e => e.name));
		const timing: Record<string, PerformanceStats> = {};

		for (const operation of operations)
		{
			const stats = this.timer.getStats(operation);
			if (stats)
			{
				timing[operation] = stats;
			}
		}

		return {
			timing,
			memory: this.memoryMonitor.getStats(),
			cpu: this.cpuMonitor.getStats(),
			eventLoop: this.eventLoopMonitor.getStats(),
		};
	}
}

/**
 * Create performance decorator
 */
function withPerformanceTracking<T extends any[], R>(
	fn: (...args: T) => Promise<R>,
	name?: string,
): (...args: T) => Promise<R>
{
	const timer = new PerformanceTimer();
	const operationName = name || fn.name || 'anonymous';

	return async (...args: T): Promise<R> =>
	{
		const { result } = await timer.timeFunction(operationName, () => fn(...args));
		return result;
	};
}

/**
 * Debounce function calls
 */
function debounce<T extends any[]>(
	fn: (...args: T) => void,
	delay: number,
): (...args: T) => void
{
	let timeoutId: NodeJS.Timeout;

	return (...args: T): void =>
	{
		clearTimeout(timeoutId);
		timeoutId = setTimeout(() => fn(...args), delay);
	};
}

/**
 * Throttle function calls
 */
function throttle<T extends any[]>(
	fn: (...args: T) => void,
	limit: number,
): (...args: T) => void
{
	let inThrottle: boolean;

	return (...args: T): void =>
	{
		if (!inThrottle)
		{
			fn(...args);
			inThrottle = true;
			setTimeout(() => inThrottle = false, limit);
		}
	};
}

export type { PerformanceEntry, PerformanceStats };

export {
	PerformanceTimer,
	MemoryMonitor,
	CpuMonitor,
	EventLoopMonitor,
	PerformanceProfiler,
	withPerformanceTracking,
	debounce,
	throttle,
};
