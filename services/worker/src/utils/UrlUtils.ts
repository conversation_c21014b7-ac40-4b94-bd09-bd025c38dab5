/**
 * URL Utilities
 * Helper functions for URL manipulation and validation
 */

type UrlParts = {
	protocol: string;
	hostname: string;
	port: string | null;
	pathname: string;
	search: string;
	hash: string;
	origin: string;
};

/**
 * Parse URL into components
 */
function parseUrl(url: string): UrlParts | null
{
	try
	{
		const parsed = new URL(url);

		return {
			protocol: parsed.protocol,
			hostname: parsed.hostname,
			port: parsed.port || null,
			pathname: parsed.pathname,
			search: parsed.search,
			hash: parsed.hash,
			origin: parsed.origin,
		};
	}
	catch
	{
		return null;
	}
}

/**
 * Build URL from components
 */
function buildUrl(parts: Partial<UrlParts> & { hostname: string }): string
{
	const protocol = parts.protocol || 'https:';
	const port = parts.port ? `:${parts.port}` : '';
	const pathname = parts.pathname || '/';
	const search = parts.search || '';
	const hash = parts.hash || '';

	return `${protocol}//${parts.hostname}${port}${pathname}${search}${hash}`;
}

/**
 * Validate URL format
 */
function isValidUrl(url: string): boolean
{
	try
	{
		new URL(url);
		return true;
	}
	catch
	{
		return false;
	}
}

/**
 * Check if URL is absolute
 */
function isAbsoluteUrl(url: string): boolean
{
	return /^https?:\/\//.test(url);
}

/**
 * Check if URL is relative
 */
function isRelativeUrl(url: string): boolean
{
	return !isAbsoluteUrl(url) && !url.startsWith('//');
}

/**
 * Convert relative URL to absolute
 */
function toAbsoluteUrl(url: string, baseUrl: string): string
{
	try
	{
		return new URL(url, baseUrl).href;
	}
	catch
	{
		return url;
	}
}

/**
 * Extract domain from URL
 */
function extractDomain(url: string): string | null
{
	const parsed = parseUrl(url);
	return parsed ? parsed.hostname : null;
}

/**
 * Extract subdomain from URL
 */
function extractSubdomain(url: string): string | null
{
	const domain = extractDomain(url);
	if (!domain) return null;

	const parts = domain.split('.');
	if (parts.length > 2)
	{
		return parts.slice(0, -2).join('.');
	}

	return null;
}

/**
 * Extract root domain from URL
 */
function extractRootDomain(url: string): string | null
{
	const domain = extractDomain(url);
	if (!domain) return null;

	const parts = domain.split('.');
	if (parts.length >= 2)
	{
		return parts.slice(-2).join('.');
	}

	return domain;
}

/**
 * Add query parameters to URL
 */
function addQueryParams(url: string, params: Record<string, string | number | boolean>): string
{
	try
	{
		const parsed = new URL(url);

		for (const [key, value] of Object.entries(params))
		{
			parsed.searchParams.set(key, String(value));
		}

		return parsed.href;
	}
	catch
	{
		return url;
	}
}

/**
 * Remove query parameters from URL
 */
function removeQueryParams(url: string, params: string[]): string
{
	try
	{
		const parsed = new URL(url);

		for (const param of params)
		{
			parsed.searchParams.delete(param);
		}

		return parsed.href;
	}
	catch
	{
		return url;
	}
}

/**
 * Get query parameters from URL
 */
function getQueryParams(url: string): Record<string, string>
{
	try
	{
		const parsed = new URL(url);
		const params: Record<string, string> = {};

		for (const [key, value] of parsed.searchParams.entries())
		{
			params[key] = value;
		}

		return params;
	}
	catch
	{
		return {};
	}
}

/**
 * Normalize URL (remove trailing slash, sort params, etc.)
 */
function normalizeUrl(url: string): string
{
	try
	{
		const parsed = new URL(url);

		// Remove fragment
		parsed.hash = '';

		// Remove trailing slash for non-root paths
		if (parsed.pathname !== '/' && parsed.pathname.endsWith('/'))
		{
			parsed.pathname = parsed.pathname.slice(0, -1);
		}

		// Sort query parameters
		const params = new URLSearchParams(parsed.search);
		const sortedParams = new URLSearchParams();

		Array.from(params.keys()).sort().forEach((key) =>
		{
			params.getAll(key).forEach((value) =>
			{
				sortedParams.append(key, value);
			});
		});

		parsed.search = sortedParams.toString();

		return parsed.href;
	}
	catch
	{
		return url;
	}
}

/**
 * Join URL paths
 */
function joinPaths(...paths: string[]): string
{
	return paths
		.map((path, index) =>
		{
			// Remove leading slash from all but first path
			if (index > 0 && path.startsWith('/'))
			{
				path = path.slice(1);
			}

			// Remove trailing slash from all but last path
			if (index < paths.length - 1 && path.endsWith('/'))
			{
				path = path.slice(0, -1);
			}

			return path;
		})
		.filter(path => path.length > 0)
		.join('/');
}

/**
 * Check if URLs are from same origin
 */
function isSameOrigin(url1: string, url2: string): boolean
{
	try
	{
		const parsed1 = new URL(url1);
		const parsed2 = new URL(url2);

		return parsed1.origin === parsed2.origin;
	}
	catch
	{
		return false;
	}
}

/**
 * Check if URL is secure (HTTPS)
 */
function isSecureUrl(url: string): boolean
{
	const parsed = parseUrl(url);
	return parsed ? parsed.protocol === 'https:' : false;
}

/**
 * Convert HTTP URL to HTTPS
 */
function toHttps(url: string): string
{
	return url.replace(/^http:/, 'https:');
}

/**
 * Convert HTTPS URL to HTTP
 */
function toHttp(url: string): string
{
	return url.replace(/^https:/, 'http:');
}

/**
 * Encode URL component safely
 */
function encodeUrlComponent(str: string): string
{
	return encodeURIComponent(str)
		.replace(/[!'()*]/g, c => `%${c.charCodeAt(0).toString(16).toUpperCase()}`);
}

/**
 * Decode URL component safely
 */
function decodeUrlComponent(str: string): string
{
	try
	{
		return decodeURIComponent(str);
	}
	catch
	{
		return str;
	}
}

/**
 * Get file extension from URL
 */
function getFileExtension(url: string): string | null
{
	try
	{
		const parsed = new URL(url);
		const pathname = parsed.pathname;
		const lastDot = pathname.lastIndexOf('.');

		if (lastDot === -1 || lastDot === pathname.length - 1)
		{
			return null;
		}

		return pathname.slice(lastDot + 1).toLowerCase();
	}
	catch
	{
		return null;
	}
}

/**
 * Get filename from URL
 */
function getFilename(url: string): string | null
{
	try
	{
		const parsed = new URL(url);
		const pathname = parsed.pathname;
		const lastSlash = pathname.lastIndexOf('/');

		if (lastSlash === -1 || lastSlash === pathname.length - 1)
		{
			return null;
		}

		return pathname.slice(lastSlash + 1);
	}
	catch
	{
		return null;
	}
}

/**
 * Check if URL points to an image
 */
function isImageUrl(url: string): boolean
{
	const extension = getFileExtension(url);
	const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'bmp', 'ico'];

	return extension ? imageExtensions.includes(extension) : false;
}

/**
 * Check if URL points to a video
 */
function isVideoUrl(url: string): boolean
{
	const extension = getFileExtension(url);
	const videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', 'm4v'];

	return extension ? videoExtensions.includes(extension) : false;
}

/**
 * Check if URL points to an audio file
 */
function isAudioUrl(url: string): boolean
{
	const extension = getFileExtension(url);
	const audioExtensions = ['mp3', 'wav', 'ogg', 'aac', 'flac', 'm4a', 'wma'];

	return extension ? audioExtensions.includes(extension) : false;
}

/**
 * Shorten URL for display
 */
function shortenUrl(url: string, maxLength = 50): string
{
	if (url.length <= maxLength) return url;

	const parsed = parseUrl(url);
	if (!parsed) return `${url.slice(0, maxLength)  }...`;

	const domain = parsed.hostname;
	const path = parsed.pathname + parsed.search;

	if (domain.length >= maxLength - 3)
	{
		return `${domain.slice(0, maxLength - 3)  }...`;
	}

	const remainingLength = maxLength - domain.length - 3; // 3 for "..."

	if (path.length <= remainingLength)
	{
		return domain + path;
	}

	return `${domain + path.slice(0, remainingLength)  }...`;
}

/**
 * Get URL depth (number of path segments)
 */
function getUrlDepth(url: string): number
{
	const parsed = parseUrl(url);
	if (!parsed) return 0;

	const pathSegments = parsed.pathname.split('/').filter(segment => segment.length > 0);
	return pathSegments.length;
}

/**
 * Check if URL is localhost
 */
function isLocalhost(url: string): boolean
{
	const parsed = parseUrl(url);
	if (!parsed) return false;

	const hostname = parsed.hostname.toLowerCase();
	return hostname === 'localhost' ||
		   hostname === '127.0.0.1' ||
		   hostname.startsWith('192.168.') ||
		   hostname.startsWith('10.') ||
		   hostname.match(/^172\.(1[6-9]|2[0-9]|3[0-1])\./);
}

export type { UrlParts };

export {
	parseUrl,
	buildUrl,
	isValidUrl,
	isAbsoluteUrl,
	isRelativeUrl,
	toAbsoluteUrl,
	extractDomain,
	extractSubdomain,
	extractRootDomain,
	addQueryParams,
	removeQueryParams,
	getQueryParams,
	normalizeUrl,
	joinPaths,
	isSameOrigin,
	isSecureUrl,
	toHttps,
	toHttp,
	encodeUrlComponent,
	decodeUrlComponent,
	getFileExtension,
	getFilename,
	isImageUrl,
	isVideoUrl,
	isAudioUrl,
	shortenUrl,
	getUrlDepth,
	isLocalhost,
};
