/**
 * Security Utilities
 * Security-related helper functions
 */

import * as crypto from 'crypto';

type HashAlgorithm = 'md5' | 'sha1' | 'sha256' | 'sha512';
type EncryptionAlgorithm = 'aes-256-gcm' | 'aes-256-cbc';

/**
 * Generate secure random string
 */
function generateRandomString(length = 32, charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'): string
{
	let result = '';
	const bytes = crypto.randomBytes(length);

	for (let i = 0; i < length; i++)
	{
		result += charset[bytes[i] % charset.length];
	}

	return result;
}

/**
 * Generate UUID v4
 */
function generateUUID(): string
{
	return crypto.randomUUID();
}

/**
 * Hash string with specified algorithm
 */
function hashString(input: string, algorithm: HashAlgorithm = 'sha256'): string
{
	return crypto.createHash(algorithm).update(input).digest('hex');
}

/**
 * Hash password with salt
 */
function hashPassword(password: string, salt?: string): { hash: string; salt: string }
{
	const passwordSalt = salt || crypto.randomBytes(32).toString('hex');
	const hash = crypto.pbkdf2Sync(password, passwordSalt, 10000, 64, 'sha512').toString('hex');

	return { hash, salt: passwordSalt };
}

/**
 * Verify password against hash
 */
function verifyPassword(password: string, hash: string, salt: string): boolean
{
	const { hash: computedHash } = hashPassword(password, salt);
	return computedHash === hash;
}

/**
 * Encrypt data
 */
function encrypt(data: string, key: string, algorithm: EncryptionAlgorithm = 'aes-256-gcm'): {
	encrypted: string;
	iv: string;
	tag?: string;
}
{
	const iv = crypto.randomBytes(16);
	const cipher = crypto.createCipher(algorithm, key);

	let encrypted = cipher.update(data, 'utf8', 'hex');
	encrypted += cipher.final('hex');

	const result: any = {
		encrypted,
		iv: iv.toString('hex'),
	};

	// Add authentication tag for GCM mode
	if (algorithm === 'aes-256-gcm')
	{
		result.tag = (cipher as any).getAuthTag().toString('hex');
	}

	return result;
}

/**
 * Decrypt data
 */
function decrypt(encryptedData: string, key: string, iv: string, algorithm: EncryptionAlgorithm = 'aes-256-gcm', tag?: string): string
{
	const decipher = crypto.createDecipher(algorithm, key);

	// Set authentication tag for GCM mode
	if (algorithm === 'aes-256-gcm' && tag)
	{
		(decipher as any).setAuthTag(Buffer.from(tag, 'hex'));
	}

	let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
	decrypted += decipher.final('utf8');

	return decrypted;
}

/**
 * Generate HMAC signature
 */
function generateHMAC(data: string, secret: string, algorithm = 'sha256'): string
{
	return crypto.createHmac(algorithm, secret).update(data).digest('hex');
}

/**
 * Verify HMAC signature
 */
function verifyHMAC(data: string, signature: string, secret: string, algorithm = 'sha256'): boolean
{
	const expectedSignature = generateHMAC(data, secret, algorithm);
	return crypto.timingSafeEqual(Buffer.from(signature, 'hex'), Buffer.from(expectedSignature, 'hex'));
}

/**
 * Sanitize input to prevent XSS
 */
function sanitizeInput(input: string): string
{
	return input
		.replace(/[<>]/g, '') // Remove angle brackets
		.replace(/javascript:/gi, '') // Remove javascript: protocol
		.replace(/on\w+=/gi, '') // Remove event handlers
		.replace(/script/gi, '') // Remove script tags
		.trim();
}

/**
 * Validate and sanitize URL
 */
function sanitizeUrl(url: string): string | null
{
	try
	{
		const parsed = new URL(url);

		// Only allow http and https protocols
		if (!['http:', 'https:'].includes(parsed.protocol))
		{
			return null;
		}

		// Remove dangerous characters
		const sanitized = url.replace(/[<>"']/g, '');

		return sanitized;
	}
	catch
	{
		return null;
	}
}

/**
 * Generate secure token
 */
function generateSecureToken(length = 32): string
{
	return crypto.randomBytes(length).toString('hex');
}

/**
 * Create rate limiter
 */
function createRateLimiter(maxRequests: number, windowMs: number)
{
	const requests = new Map<string, number[]>();

	return {
		isAllowed: (identifier: string): boolean =>
		{
			const now = Date.now();
			const windowStart = now - windowMs;

			// Get existing requests for this identifier
			const userRequests = requests.get(identifier) || [];

			// Remove old requests outside the window
			const validRequests = userRequests.filter(time => time > windowStart);

			// Check if under limit
			if (validRequests.length >= maxRequests)
			{
				return false;
			}

			// Add current request
			validRequests.push(now);
			requests.set(identifier, validRequests);

			return true;
		},

		getRemainingRequests: (identifier: string): number =>
		{
			const now = Date.now();
			const windowStart = now - windowMs;
			const userRequests = requests.get(identifier) || [];
			const validRequests = userRequests.filter(time => time > windowStart);

			return Math.max(0, maxRequests - validRequests.length);
		},

		reset: (identifier?: string): void =>
		{
			if (identifier)
			{
				requests.delete(identifier);
			}
			else
			{
				requests.clear();
			}
		},
	};
}

/**
 * Validate input against common injection patterns
 */
function validateInput(input: string): { valid: boolean; issues: string[] }
{
	const issues: string[] = [];

	// SQL injection patterns
	const sqlPatterns = [
		/(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i,
		/(--|\/\*|\*\/)/,
		/(\b(OR|AND)\b.*=.*)/i,
		/['"]\s*(OR|AND)\s*['"]/i,
	];

	// XSS patterns
	const xssPatterns = [
		/<script[^>]*>.*?<\/script>/gi,
		/javascript:/gi,
		/on\w+\s*=/gi,
		/<iframe[^>]*>/gi,
		/<object[^>]*>/gi,
		/<embed[^>]*>/gi,
	];

	// Command injection patterns
	const commandPatterns = [
		/[;&|`$(){}[\]]/,
		/\b(cat|ls|pwd|whoami|id|uname|ps|netstat|ifconfig)\b/i,
	];

	// Check SQL injection
	for (const pattern of sqlPatterns)
	{
		if (pattern.test(input))
		{
			issues.push('Potential SQL injection detected');
			break;
		}
	}

	// Check XSS
	for (const pattern of xssPatterns)
	{
		if (pattern.test(input))
		{
			issues.push('Potential XSS attack detected');
			break;
		}
	}

	// Check command injection
	for (const pattern of commandPatterns)
	{
		if (pattern.test(input))
		{
			issues.push('Potential command injection detected');
			break;
		}
	}

	return {
		valid: issues.length === 0,
		issues,
	};
}

/**
 * Generate API key
 */
function generateApiKey(prefix = 'ak'): string
{
	const timestamp = Date.now().toString(36);
	const random = crypto.randomBytes(16).toString('hex');
	return `${prefix}_${timestamp}_${random}`;
}

/**
 * Validate API key format
 */
function validateApiKey(apiKey: string, expectedPrefix = 'ak'): boolean
{
	const pattern = new RegExp(`^${expectedPrefix}_[a-z0-9]+_[a-f0-9]{32}$`);
	return pattern.test(apiKey);
}

/**
 * Create secure session token
 */
function createSessionToken(userId: string, expiresIn = 3600000): {
	token: string;
	expiresAt: number;
}
{
	const expiresAt = Date.now() + expiresIn;
	const payload = JSON.stringify({ userId, expiresAt });
	const signature = generateHMAC(payload, process.env.SESSION_SECRET || 'default-secret');

	const token = Buffer.from(`${payload}.${signature}`).toString('base64');

	return { token, expiresAt };
}

/**
 * Verify session token
 */
function verifySessionToken(token: string): { valid: boolean; userId?: string; expiresAt?: number }
{
	try
	{
		const decoded = Buffer.from(token, 'base64').toString('utf8');
		const [payload, signature] = decoded.split('.');

		// Verify signature
		const expectedSignature = generateHMAC(payload, process.env.SESSION_SECRET || 'default-secret');
		if (!crypto.timingSafeEqual(Buffer.from(signature), Buffer.from(expectedSignature)))
		{
			return { valid: false };
		}

		// Parse payload
		const { userId, expiresAt } = JSON.parse(payload);

		// Check expiration
		if (Date.now() > expiresAt)
		{
			return { valid: false };
		}

		return { valid: true, userId, expiresAt };
	}
	catch
	{
		return { valid: false };
	}
}

/**
 * Mask sensitive data
 */
function maskSensitiveData(data: string, visibleChars = 4): string
{
	if (data.length <= visibleChars * 2)
	{
		return '*'.repeat(data.length);
	}

	const start = data.substring(0, visibleChars);
	const end = data.substring(data.length - visibleChars);
	const middle = '*'.repeat(data.length - visibleChars * 2);

	return `${start}${middle}${end}`;
}

/**
 * Check password strength
 */
function checkPasswordStrength(password: string): {
	score: number;
	feedback: string[];
	isStrong: boolean;
}
{
	const feedback: string[] = [];
	let score = 0;

	// Length check
	if (password.length >= 8)
	{
		score += 1;
	}
	else
	{
		feedback.push('Password should be at least 8 characters long');
	}

	// Uppercase check
	if (/[A-Z]/.test(password))
	{
		score += 1;
	}
	else
	{
		feedback.push('Password should contain uppercase letters');
	}

	// Lowercase check
	if (/[a-z]/.test(password))
	{
		score += 1;
	}
	else
	{
		feedback.push('Password should contain lowercase letters');
	}

	// Number check
	if (/\d/.test(password))
	{
		score += 1;
	}
	else
	{
		feedback.push('Password should contain numbers');
	}

	// Special character check
	if (/[!@#$%^&*(),.?":{}|<>]/.test(password))
	{
		score += 1;
	}
	else
	{
		feedback.push('Password should contain special characters');
	}

	// Length bonus
	if (password.length >= 12)
	{
		score += 1;
	}

	return {
		score,
		feedback,
		isStrong: score >= 4,
	};
}

export type { HashAlgorithm, EncryptionAlgorithm };

export {
	generateRandomString,
	generateUUID,
	hashString,
	hashPassword,
	verifyPassword,
	encrypt,
	decrypt,
	generateHMAC,
	verifyHMAC,
	sanitizeInput,
	sanitizeUrl,
	generateSecureToken,
	createRateLimiter,
	validateInput,
	generateApiKey,
	validateApiKey,
	createSessionToken,
	verifySessionToken,
	maskSensitiveData,
	checkPasswordStrength,
};
