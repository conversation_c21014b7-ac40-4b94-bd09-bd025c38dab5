/* eslint-disable max-classes-per-file */
/**
 * Cache Utilities
 * Helper functions for caching operations
 */

import { logger as sharedLogger } from '@shared';

const logger = sharedLogger.getLogger('CacheUtils');

type CacheEntry<T> = {
	value: T;
	timestamp: number;
	ttl: number;
	hits: number;
};

type CacheStats = {
	size: number;
	hits: number;
	misses: number;
	hitRate: number;
	memoryUsage: number;
};

/**
 * In-memory cache with TTL support
 */
class MemoryCache<T = any>
{
	private cache: Map<string, CacheEntry<T>>;
	private stats: { hits: number; misses: number };
	private maxSize: number;
	private defaultTTL: number;

	constructor(maxSize = 1000, defaultTTL = 3600000) // 1 hour default TTL
	{
		this.cache = new Map();
		this.stats = { hits: 0, misses: 0 };
		this.maxSize = maxSize;
		this.defaultTTL = defaultTTL;
	}

	/**
	 * Set cache entry
	 */
	set(key: string, value: T, ttl = this.defaultTTL): void
	{
		// Remove expired entries if cache is full
		if (this.cache.size >= this.maxSize)
		{
			this.cleanup();

			// If still full, remove oldest entry
			if (this.cache.size >= this.maxSize)
			{
				const oldestKey = this.cache.keys().next().value;
				this.cache.delete(oldestKey);
			}
		}

		this.cache.set(key, {
			value,
			timestamp: Date.now(),
			ttl,
			hits: 0,
		});
	}

	/**
	 * Get cache entry
	 */
	get(key: string): T | undefined
	{
		const entry = this.cache.get(key);

		if (!entry)
		{
			this.stats.misses++;
			return undefined;
		}

		// Check if expired
		if (Date.now() - entry.timestamp > entry.ttl)
		{
			this.cache.delete(key);
			this.stats.misses++;
			return undefined;
		}

		// Update hit count and stats
		entry.hits++;
		this.stats.hits++;

		return entry.value;
	}

	/**
	 * Check if key exists and is not expired
	 */
	has(key: string): boolean
	{
		return this.get(key) !== undefined;
	}

	/**
	 * Delete cache entry
	 */
	delete(key: string): boolean
	{
		return this.cache.delete(key);
	}

	/**
	 * Clear all cache entries
	 */
	clear(): void
	{
		this.cache.clear();
		this.stats = { hits: 0, misses: 0 };
	}

	/**
	 * Get cache statistics
	 */
	getStats(): CacheStats
	{
		const totalRequests = this.stats.hits + this.stats.misses;
		const hitRate = totalRequests > 0 ? (this.stats.hits / totalRequests) * 100 : 0;

		// Estimate memory usage
		let memoryUsage = 0;
		for (const [key, entry] of Array.from(this.cache.entries()))
		{
			memoryUsage += key.length * 2; // String characters are 2 bytes
			memoryUsage += JSON.stringify(entry.value).length * 2;
			memoryUsage += 32; // Overhead for entry object
		}

		return {
			size: this.cache.size,
			hits: this.stats.hits,
			misses: this.stats.misses,
			hitRate: Math.round(hitRate * 100) / 100,
			memoryUsage,
		};
	}

	/**
	 * Get all keys
	 */
	keys(): string[]
	{
		return Array.from(this.cache.keys());
	}

	/**
	 * Get all values
	 */
	values(): T[]
	{
		return Array.from(this.cache.values()).map(entry => entry.value);
	}

	/**
	 * Get cache entries sorted by usage
	 */
	getEntriesByUsage(): Array<{ key: string; value: T; hits: number }>
	{
		return Array.from(this.cache.entries())
			.map(([key, entry]) => ({ key, value: entry.value, hits: entry.hits }))
			.sort((a, b) => b.hits - a.hits);
	}

	/**
	 * Remove expired entries
	 */
	cleanup(): number
	{
		const now = Date.now();
		let removedCount = 0;

		for (const [key, entry] of Array.from(this.cache.entries()))
		{
			if (now - entry.timestamp > entry.ttl)
			{
				this.cache.delete(key);
				removedCount++;
			}
		}

		return removedCount;
	}

	/**
	 * Get or set with factory function
	 */
	async getOrSet(key: string, factory: () => Promise<T>, ttl = this.defaultTTL): Promise<T>
	{
		const cached = this.get(key);
		if (cached !== undefined)
		{
			return cached;
		}

		const value = await factory();
		this.set(key, value, ttl);
		return value;
	}
}

/**
 * LRU (Least Recently Used) Cache
 */
class LRUCache<T = any>
{
	private cache: Map<string, { value: T; timestamp: number }>;
	private maxSize: number;

	constructor(maxSize = 1000)
	{
		this.cache = new Map();
		this.maxSize = maxSize;
	}

	/**
	 * Set cache entry
	 */
	set(key: string, value: T): void
	{
		// If key exists, delete it first to update position
		if (this.cache.has(key))
		{
			this.cache.delete(key);
		}
		// If cache is full, remove least recently used (first entry)
		else if (this.cache.size >= this.maxSize)
		{
			const firstKey = this.cache.keys().next().value;
			this.cache.delete(firstKey);
		}

		this.cache.set(key, { value, timestamp: Date.now() });
	}

	/**
	 * Get cache entry
	 */
	get(key: string): T | undefined
	{
		const entry = this.cache.get(key);
		if (!entry) return undefined;

		// Move to end (most recently used)
		this.cache.delete(key);
		this.cache.set(key, entry);

		return entry.value;
	}

	/**
	 * Check if key exists
	 */
	has(key: string): boolean
	{
		return this.cache.has(key);
	}

	/**
	 * Delete cache entry
	 */
	delete(key: string): boolean
	{
		return this.cache.delete(key);
	}

	/**
	 * Clear all entries
	 */
	clear(): void
	{
		this.cache.clear();
	}

	/**
	 * Get cache size
	 */
	size(): number
	{
		return this.cache.size;
	}

	/**
	 * Get all keys in LRU order
	 */
	keys(): string[]
	{
		return Array.from(this.cache.keys());
	}
}

/**
 * Cache key generator
 */
function generateCacheKey(...parts: (string | number | boolean)[]): string
{
	return parts.map(part => String(part)).join(':');
}

/**
 * Hash-based cache key
 */
function hashCacheKey(data: any): string
{
	const crypto = require('crypto');
	const str = typeof data === 'string' ? data : JSON.stringify(data);
	return crypto.createHash('md5').update(str).digest('hex');
}

/**
 * Memoization decorator
 */
function memoize<T extends (...args: any[]) => any>(
	fn: T,
	keyGenerator?: (...args: Parameters<T>) => string,
	ttl = 3600000,
): T
{
	const cache = new MemoryCache<ReturnType<T>>(1000, ttl);

	return ((...args: Parameters<T>): ReturnType<T> =>
	{
		const key = keyGenerator ? keyGenerator(...args) : JSON.stringify(args);

		let result = cache.get(key);
		if (result === undefined)
		{
			result = fn(...args);
			cache.set(key, result);
		}

		return result;
	}) as T;
}

/**
 * Async memoization decorator
 */
function memoizeAsync<T extends (...args: any[]) => Promise<any>>(
	fn: T,
	keyGenerator?: (...args: Parameters<T>) => string,
	ttl = 3600000,
): T
{
	const cache = new MemoryCache<Awaited<ReturnType<T>>>(1000, ttl);
	const pending = new Map<string, Promise<Awaited<ReturnType<T>>>>();

	return (async (...args: Parameters<T>): Promise<Awaited<ReturnType<T>>> =>
	{
		const key = keyGenerator ? keyGenerator(...args) : JSON.stringify(args);

		// Check cache first
		const cachedResult = cache.get(key);
		if (cachedResult !== undefined)
		{
			return cachedResult;
		}

		// Check if already pending
		const pendingResult = pending.get(key);
		if (pendingResult)
		{
			return pendingResult;
		}

		// Execute function
		const promise = fn(...args);
		pending.set(key, promise);

		try
		{
			const result = await promise;
			cache.set(key, result);
			return result;
		}
		finally
		{
			pending.delete(key);
		}
	}) as T;
}

/**
 * Cache with automatic refresh
 */
class RefreshCache<T = any>
{
	private cache: MemoryCache<T>;
	private refreshFunctions: Map<string, () => Promise<T>>;
	private refreshIntervals: Map<string, NodeJS.Timeout>;

	constructor(maxSize = 1000, defaultTTL = 3600000)
	{
		this.cache = new MemoryCache<T>(maxSize, defaultTTL);
		this.refreshFunctions = new Map();
		this.refreshIntervals = new Map();
	}

	/**
	 * Set cache entry with auto-refresh
	 */
	setWithRefresh(
		key: string,
		value: T,
		refreshFn: () => Promise<T>,
		refreshInterval: number,
		ttl?: number,
	): void
	{
		this.cache.set(key, value, ttl);
		this.refreshFunctions.set(key, refreshFn);

		// Clear existing interval
		const existingInterval = this.refreshIntervals.get(key);
		if (existingInterval)
		{
			clearInterval(existingInterval);
		}

		// Set new refresh interval
		const interval = setInterval(async () =>
		{
			try
			{
				const newValue = await refreshFn();
				this.cache.set(key, newValue, ttl);
			}
			catch (error)
			{
				logger.error(`Failed to refresh cache key ${key}:`, error);
			}
		}, refreshInterval);

		this.refreshIntervals.set(key, interval);
	}

	/**
	 * Get cache entry
	 */
	get(key: string): T | undefined
	{
		return this.cache.get(key);
	}

	/**
	 * Delete cache entry and stop refresh
	 */
	delete(key: string): boolean
	{
		const interval = this.refreshIntervals.get(key);
		if (interval)
		{
			clearInterval(interval);
			this.refreshIntervals.delete(key);
		}

		this.refreshFunctions.delete(key);
		return this.cache.delete(key);
	}

	/**
	 * Clear all entries and stop all refreshes
	 */
	clear(): void
	{
		for (const interval of Array.from(this.refreshIntervals.values()))
		{
			clearInterval(interval);
		}

		this.refreshIntervals.clear();
		this.refreshFunctions.clear();
		this.cache.clear();
	}

	/**
	 * Get cache statistics
	 */
	getStats(): CacheStats
	{
		return this.cache.getStats();
	}
}

/**
 * Multi-level cache (L1: Memory, L2: Persistent)
 */
class MultiLevelCache<T = any>
{
	private l1Cache: MemoryCache<T>;
	private l2Cache: Map<string, string>; // Simplified L2 cache

	constructor(l1Size = 100, l2Size = 1000, defaultTTL = 3600000)
	{
		this.l1Cache = new MemoryCache<T>(l1Size, defaultTTL);
		this.l2Cache = new Map(); // In real implementation, this would be Redis/file-based
	}

	/**
	 * Set cache entry in both levels
	 */
	async set(key: string, value: T, ttl?: number): Promise<void>
	{
		// Set in L1 cache
		this.l1Cache.set(key, value, ttl);

		// Set in L2 cache (serialized)
		this.l2Cache.set(key, JSON.stringify({ value, timestamp: Date.now(), ttl }));
	}

	/**
	 * Get cache entry from L1 or L2
	 */
	async get(key: string): Promise<T | undefined>
	{
		// Try L1 cache first
		const result = this.l1Cache.get(key);
		if (result !== undefined)
		{
			return result;
		}

		// Try L2 cache
		const l2Entry = this.l2Cache.get(key);
		if (l2Entry)
		{
			try
			{
				const parsed = JSON.parse(l2Entry);

				// Check if expired
				if (parsed.ttl && Date.now() - parsed.timestamp > parsed.ttl)
				{
					this.l2Cache.delete(key);
					return undefined;
				}

				// Promote to L1 cache
				this.l1Cache.set(key, parsed.value, parsed.ttl);
				return parsed.value;
			}
			catch
			{
				this.l2Cache.delete(key);
			}
		}

		return undefined;
	}

	/**
	 * Delete from both levels
	 */
	async delete(key: string): Promise<boolean>
	{
		const l1Deleted = this.l1Cache.delete(key);
		const l2Deleted = this.l2Cache.delete(key);

		return l1Deleted || l2Deleted;
	}

	/**
	 * Clear both levels
	 */
	async clear(): Promise<void>
	{
		this.l1Cache.clear();
		this.l2Cache.clear();
	}
}

export type { CacheEntry, CacheStats };

export {
	MemoryCache,
	LRUCache,
	RefreshCache,
	MultiLevelCache,
	generateCacheKey,
	hashCacheKey,
	memoize,
	memoizeAsync,
};
