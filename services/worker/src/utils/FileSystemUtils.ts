/**
 * File System Utilities
 * Helper functions for file system operations
 */

import * as fs from 'fs';
import * as path from 'path';
import { promisify } from 'util';

const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);
const appendFile = promisify(fs.appendFile);
const unlink = promisify(fs.unlink);
const mkdir = promisify(fs.mkdir);
const rmdir = promisify(fs.rmdir);
const readdir = promisify(fs.readdir);
const stat = promisify(fs.stat);
const access = promisify(fs.access);

type FileInfo = {
	name: string;
	path: string;
	size: number;
	isDirectory: boolean;
	isFile: boolean;
	created: Date;
	modified: Date;
	extension?: string;
};

/**
 * Check if file or directory exists
 */
async function exists(filePath: string): Promise<boolean>
{
	try
	{
		await access(filePath);
		return true;
	}
	catch
	{
		return false;
	}
}

/**
 * Get file information
 */
async function getFileInfo(filePath: string): Promise<FileInfo | null>
{
	try
	{
		const stats = await stat(filePath);
		const name = path.basename(filePath);
		const extension = path.extname(filePath).slice(1);

		return {
			name,
			path: filePath,
			size: stats.size,
			isDirectory: stats.isDirectory(),
			isFile: stats.isFile(),
			created: stats.birthtime,
			modified: stats.mtime,
			extension: extension || undefined,
		};
	}
	catch
	{
		return null;
	}
}

/**
 * Read file as text
 */
async function readTextFile(filePath: string, encoding: BufferEncoding = 'utf8'): Promise<string>
{
	return readFile(filePath, encoding);
}

/**
 * Read file as JSON
 */
async function readJsonFile<T = any>(filePath: string): Promise<T>
{
	const content = await readTextFile(filePath);
	return JSON.parse(content);
}

/**
 * Write text to file
 */
async function writeTextFile(filePath: string, content: string, encoding: BufferEncoding = 'utf8'): Promise<void>
{
	await ensureDirectoryExists(path.dirname(filePath));
	return writeFile(filePath, content, encoding);
}

/**
 * Write JSON to file
 */
async function writeJsonFile(filePath: string, data: any, indent = 2): Promise<void>
{
	const content = JSON.stringify(data, null, indent);
	return writeTextFile(filePath, content);
}

/**
 * Append text to file
 */
async function appendTextFile(filePath: string, content: string, encoding: BufferEncoding = 'utf8'): Promise<void>
{
	await ensureDirectoryExists(path.dirname(filePath));
	return appendFile(filePath, content, encoding);
}

/**
 * Copy file
 */
async function copyFile(source: string, destination: string): Promise<void>
{
	await ensureDirectoryExists(path.dirname(destination));

	return new Promise((resolve, reject) =>
	{
		const readStream = fs.createReadStream(source);
		const writeStream = fs.createWriteStream(destination);

		readStream.on('error', reject);
		writeStream.on('error', reject);
		writeStream.on('finish', resolve);

		readStream.pipe(writeStream);
	});
}

/**
 * Move/rename file
 */
async function moveFile(source: string, destination: string): Promise<void>
{
	await copyFile(source, destination);
	await deleteFile(source);
}

/**
 * Delete file
 */
async function deleteFile(filePath: string): Promise<void>
{
	return unlink(filePath);
}

/**
 * Create directory
 */
async function createDirectory(dirPath: string, recursive = true): Promise<void>
{
	await mkdir(dirPath, { recursive });
}

/**
 * Ensure directory exists
 */
async function ensureDirectoryExists(dirPath: string): Promise<void>
{
	if (!(await exists(dirPath)))
	{
		await createDirectory(dirPath, true);
	}
}

/**
 * Delete directory
 */
async function deleteDirectory(dirPath: string, recursive = true): Promise<void>
{
	if (recursive)
	{
		// For Node.js 14+, use recursive option
		await rmdir(dirPath, { recursive: true } as any);
	}
	else
	{
		await rmdir(dirPath);
	}
}

/**
 * List directory contents
 */
async function listDirectory(dirPath: string, recursive = false): Promise<FileInfo[]>
{
	const items: FileInfo[] = [];

	try
	{
		const entries = await readdir(dirPath);

		for (const entry of entries)
		{
			const fullPath = path.join(dirPath, entry);
			const info = await getFileInfo(fullPath);

			if (info)
			{
				items.push(info);

				if (recursive && info.isDirectory)
				{
					const subItems = await listDirectory(fullPath, true);
					items.push(...subItems);
				}
			}
		}
	}
	catch
	{
		// Directory doesn't exist or can't be read
	}

	return items;
}

/**
 * Find files by pattern
 */
async function findFiles(dirPath: string, pattern: RegExp, recursive = true): Promise<string[]>
{
	const files: string[] = [];
	const items = await listDirectory(dirPath, recursive);

	for (const item of items)
	{
		if (item.isFile && pattern.test(item.name))
		{
			files.push(item.path);
		}
	}

	return files;
}

/**
 * Find files by extension
 */
async function findFilesByExtension(dirPath: string, extension: string, recursive = true): Promise<string[]>
{
	const pattern = new RegExp(`\\.${extension.replace('.', '')}$`, 'i');
	return findFiles(dirPath, pattern, recursive);
}

/**
 * Get file size
 */
async function getFileSize(filePath: string): Promise<number>
{
	const info = await getFileInfo(filePath);
	return info ? info.size : 0;
}

/**
 * Get directory size
 */
async function getDirectorySize(dirPath: string): Promise<number>
{
	let totalSize = 0;
	const items = await listDirectory(dirPath, true);

	for (const item of items)
	{
		if (item.isFile)
		{
			totalSize += item.size;
		}
	}

	return totalSize;
}

/**
 * Format file size to human readable
 */
function formatFileSize(bytes: number): string
{
	const units = ['B', 'KB', 'MB', 'GB', 'TB'];
	let size = bytes;
	let unitIndex = 0;

	while (size >= 1024 && unitIndex < units.length - 1)
	{
		size /= 1024;
		unitIndex++;
	}

	return `${size.toFixed(2)} ${units[unitIndex]}`;
}

/**
 * Get file extension
 */
function getFileExtension(filePath: string): string
{
	return path.extname(filePath).slice(1).toLowerCase();
}

/**
 * Get filename without extension
 */
function getBasename(filePath: string): string
{
	return path.basename(filePath, path.extname(filePath));
}

/**
 * Join paths safely
 */
function joinPaths(...paths: string[]): string
{
	return path.join(...paths);
}

/**
 * Resolve path to absolute
 */
function resolvePath(filePath: string): string
{
	return path.resolve(filePath);
}

/**
 * Get relative path
 */
function getRelativePath(from: string, to: string): string
{
	return path.relative(from, to);
}

/**
 * Normalize path
 */
function normalizePath(filePath: string): string
{
	return path.normalize(filePath);
}

/**
 * Check if path is absolute
 */
function isAbsolutePath(filePath: string): boolean
{
	return path.isAbsolute(filePath);
}

/**
 * Create temporary file
 */
async function createTempFile(prefix = 'temp', extension = '.tmp'): Promise<string>
{
	const tempDir = require('os').tmpdir();
	const filename = `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}${extension}`;
	const tempPath = path.join(tempDir, filename);

	// Create empty file
	await writeTextFile(tempPath, '');

	return tempPath;
}

/**
 * Create temporary directory
 */
async function createTempDirectory(prefix = 'temp'): Promise<string>
{
	const tempDir = require('os').tmpdir();
	const dirname = `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
	const tempPath = path.join(tempDir, dirname);

	await createDirectory(tempPath);

	return tempPath;
}

/**
 * Clean up old files
 */
async function cleanupOldFiles(dirPath: string, maxAge: number, unit: 'hours' | 'days' = 'days'): Promise<number>
{
	const items = await listDirectory(dirPath);
	const now = Date.now();
	const maxAgeMs = maxAge * (unit === 'hours' ? 60 * 60 * 1000 : 24 * 60 * 60 * 1000);
	let deletedCount = 0;

	for (const item of items)
	{
		if (item.isFile)
		{
			const age = now - item.modified.getTime();

			if (age > maxAgeMs)
			{
				try
				{
					await deleteFile(item.path);
					deletedCount++;
				}
				catch
				{
					// Ignore errors when deleting files
				}
			}
		}
	}

	return deletedCount;
}

/**
 * Watch file for changes
 */
function watchFile(filePath: string, callback: (eventType: string, filename: string) => void): fs.FSWatcher
{
	return fs.watch(filePath, callback);
}

/**
 * Watch directory for changes
 */
function watchDirectory(dirPath: string, callback: (eventType: string, filename: string) => void): fs.FSWatcher
{
	return fs.watch(dirPath, { recursive: true }, callback);
}

/**
 * Check if file is readable
 */
async function isReadable(filePath: string): Promise<boolean>
{
	try
	{
		await access(filePath, fs.constants.R_OK);
		return true;
	}
	catch
	{
		return false;
	}
}

/**
 * Check if file is writable
 */
async function isWritable(filePath: string): Promise<boolean>
{
	try
	{
		await access(filePath, fs.constants.W_OK);
		return true;
	}
	catch
	{
		return false;
	}
}

/**
 * Check if file is executable
 */
async function isExecutable(filePath: string): Promise<boolean>
{
	try
	{
		await access(filePath, fs.constants.X_OK);
		return true;
	}
	catch
	{
		return false;
	}
}

export type { FileInfo };

export {
	exists,
	getFileInfo,
	readTextFile,
	readJsonFile,
	writeTextFile,
	writeJsonFile,
	appendTextFile,
	copyFile,
	moveFile,
	deleteFile,
	createDirectory,
	ensureDirectoryExists,
	deleteDirectory,
	listDirectory,
	findFiles,
	findFilesByExtension,
	getFileSize,
	getDirectorySize,
	formatFileSize,
	getFileExtension,
	getBasename,
	joinPaths,
	resolvePath,
	getRelativePath,
	normalizePath,
	isAbsolutePath,
	createTempFile,
	createTempDirectory,
	cleanupOldFiles,
	watchFile,
	watchDirectory,
	isReadable,
	isWritable,
	isExecutable,
};
