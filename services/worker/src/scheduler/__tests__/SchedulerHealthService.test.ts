import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import SchedulerHealthService from '../SchedulerHealthService';

// Mock the shared modules
vi.mock('@shared', () => ({
	HealthChecker: vi.fn().mockImplementation(() => ({
		getHealthStatus: vi.fn().mockResolvedValue({
			status: 'healthy',
			services: {
				redis: { status: 'healthy' },
				scylladb: { status: 'healthy' },
			},
		}),
	})),
	MetricsCollector: vi.fn().mockImplementation(() => ({
		getMetricsSummary: vi.fn().mockResolvedValue({}),
		counter: vi.fn(),
		timer: vi.fn(),
		gauge: vi.fn(),
		stop: vi.fn().mockResolvedValue(undefined),
	})),
	logger: {
		getLogger: vi.fn(() => ({
			info: vi.fn(),
			error: vi.fn(),
			warn: vi.fn(),
			debug: vi.fn(),
		})),
	},
}));

describe('SchedulerHealthService', () =>
{
	let healthService: SchedulerHealthService;

	beforeEach(() =>
	{
		healthService = new SchedulerHealthService();
	});

	afterEach(async () =>
	{
		if (healthService)
		{
			await healthService.cleanup();
		}
	});

	describe('health status', () =>
	{
		it('should get health status without metrics', async () =>
		{
			const health = await healthService.getHealthStatus(false);

			expect(health).toHaveProperty('status');
			expect(health).toHaveProperty('services');
		});

		it('should get health status with metrics', async () =>
		{
			const health = await healthService.getHealthStatus(true);

			expect(health).toHaveProperty('status');
			expect(health).toHaveProperty('services');
		});

		it('should check readiness', async () =>
		{
			const isReady = await healthService.isReady();

			expect(typeof isReady).toBe('boolean');
		});
	});

	describe('scheduler metrics', () =>
	{
		it('should get scheduler metrics', async () =>
		{
			const metrics = await healthService.getSchedulerMetrics();

			expect(metrics).toHaveProperty('timestamp');
			expect(metrics).toHaveProperty('summary');
		});

		it('should record job scheduling metrics', () =>
		{
			expect(() =>
			{
				healthService.recordJobSchedulingMetrics('crawl', 'high', true);
			}).not.toThrow();
		});

		it('should record job processing metrics', () =>
		{
			expect(() =>
			{
				healthService.recordJobProcessingMetrics('crawl', 1000, true);
			}).not.toThrow();
		});

		it('should record queue metrics', () =>
		{
			expect(() =>
			{
				healthService.recordQueueMetrics('domain-crawl', 10, 2);
			}).not.toThrow();
		});

		it('should record worker metrics', () =>
		{
			expect(() =>
			{
				healthService.recordWorkerMetrics('worker-1', 0.8, 5);
			}).not.toThrow();
		});

		it('should record cron execution metrics', () =>
		{
			expect(() =>
			{
				healthService.recordCronExecutionMetrics('daily-crawl', 5000, true);
			}).not.toThrow();
		});
	});

	describe('service checks', () =>
	{
		it('should handle health check failures gracefully', async () =>
		{
			// Mock a failure in the health checker
			const mockHealthChecker = {
				getHealthStatus: vi.fn().mockRejectedValue(new Error('Health check failed')),
			};

			// Replace the health checker
			(healthService as any).healthChecker = mockHealthChecker;

			await expect(healthService.getHealthStatus()).rejects.toThrow('Health check failed');
		});

		it('should handle readiness check failures gracefully', async () =>
		{
			// Mock a failure in the health checker
			const mockHealthChecker = {
				getHealthStatus: vi.fn().mockRejectedValue(new Error('Readiness check failed')),
			};

			// Replace the health checker
			(healthService as any).healthChecker = mockHealthChecker;

			const isReady = await healthService.isReady();

			expect(isReady).toBe(false);
		});
	});

	describe('cleanup', () =>
	{
		it('should cleanup resources', async () =>
		{
			await expect(healthService.cleanup()).resolves.not.toThrow();
		});
	});
});
