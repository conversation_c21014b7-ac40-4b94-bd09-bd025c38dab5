import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { logger } from '@shared';
import JobScheduler from '../JobScheduler';

// Mock the shared modules
vi.mock('@shared', () => ({
	logger: {
		getLogger: vi.fn(() => ({
			info: vi.fn(),
			error: vi.fn(),
			warn: vi.fn(),
			debug: vi.fn(),
		})),
	},
	Constants: {
		JOB_QUEUES: {
			DOMAIN_CRAWL: 'queue:domain:crawl',
			RANKING_UPDATE: 'queue:ranking:update',
			TRAFFIC_ANALYSIS: 'queue:traffic:analysis',
			BACKLINK_ANALYSIS: 'queue:backlink:analysis',
			MANTICORE_SYNC: 'queue:manticore:sync',
			MAINTENANCE: 'queue:maintenance',
		},
	},
}));

// Mock config separately
vi.mock('@shared/utils/Config', () => ({
	config: {
		getAll: vi.fn(() => ({
			REDIS_HOST: 'localhost',
			REDIS_PORT: '6379',
			REDIS_DB: '0',
		})),
	},
}));

// Mock redis-smq
vi.mock('redis-smq', () => ({
	Producer: vi.fn().mockImplementation(() => ({
		run: vi.fn().mockResolvedValue(undefined),
		produce: vi.fn().mockResolvedValue(undefined),
		shutdown: vi.fn().mockResolvedValue(undefined),
	})),
	Consumer: vi.fn().mockImplementation(() => ({
		consume: vi.fn(),
		run: vi.fn().mockResolvedValue(undefined),
		shutdown: vi.fn().mockResolvedValue(undefined),
	})),
	Message: vi.fn().mockImplementation(() => ({
		setBody: vi.fn(),
		setPriority: vi.fn(),
		getBody: vi.fn().mockReturnValue('{}'),
	})),
}));

describe('JobScheduler', () =>
{
	let jobScheduler: JobScheduler;

	beforeEach(() =>
	{
		jobScheduler = new JobScheduler();
	});

	afterEach(async () =>
	{
		if (jobScheduler)
		{
			await jobScheduler.shutdown();
		}
	});

	describe('initialization', () =>
	{
		it('should initialize successfully', async () =>
		{
			await expect(jobScheduler.initialize()).resolves.not.toThrow();
		});

		it('should have default queue configurations', () =>
		{
			const stats = jobScheduler.getAllJobStats();

			expect(stats).toHaveLength(6); // 6 default queues
			expect(stats.map(s => s.queueName)).toContain('queue:domain:crawl');
		});
	});

	describe('job scheduling', () =>
	{
		beforeEach(async () =>
		{
			await jobScheduler.initialize();
		});

		it('should schedule a domain crawl job', async () =>
		{
			const jobId = await jobScheduler.scheduleDomainCrawlJob('example.com', 'full', 'high');

			expect(jobId).toBeDefined();
			expect(typeof jobId).toBe('string');
		});

		it('should schedule a ranking update job', async () =>
		{
			const jobId = await jobScheduler.scheduleRankingUpdateJob('example.com', 'global', 'medium');

			expect(jobId).toBeDefined();
			expect(typeof jobId).toBe('string');
		});

		it('should schedule batch jobs', async () =>
		{
			const jobs = [
				{ domain: 'example1.com', crawlType: 'full' },
				{ domain: 'example2.com', crawlType: 'quick' },
			];

			const jobIds = await jobScheduler.scheduleBatchJobs('queue:domain:crawl', jobs, {
				priority: 'medium',
				batchSize: 2,
			});

			expect(jobIds).toHaveLength(2);
			expect(jobIds.every(id => typeof id === 'string')).toBe(true);
		});
	});

	describe('job statistics', () =>
	{
		beforeEach(async () =>
		{
			await jobScheduler.initialize();
		});

		it('should return job statistics for a queue', () =>
		{
			const stats = jobScheduler.getJobStats('queue:domain:crawl');

			expect(stats).toBeDefined();
			expect(stats?.queueName).toBe('queue:domain:crawl');
		});

		it('should return all job statistics', () =>
		{
			const allStats = jobScheduler.getAllJobStats();

			expect(allStats).toHaveLength(6);
			expect(allStats.every(stat => stat.queueName)).toBe(true);
		});
	});

	describe('health check', () =>
	{
		beforeEach(async () =>
		{
			await jobScheduler.initialize();
		});

		it('should return health status', async () =>
		{
			const health = await jobScheduler.healthCheck();

			expect(health).toHaveProperty('status');
			expect(health).toHaveProperty('producer');
			expect(health).toHaveProperty('consumers');
			expect(health).toHaveProperty('queues');
		});
	});

	describe('priority handling', () =>
	{
		beforeEach(async () =>
		{
			await jobScheduler.initialize();
		});

		it('should convert priority strings to numeric values correctly', async () =>
		{
			// This tests the priority handling indirectly through job scheduling
			await expect(jobScheduler.scheduleDomainCrawlJob('test.com', 'full', 'low')).resolves.toBeDefined();
			await expect(jobScheduler.scheduleDomainCrawlJob('test.com', 'full', 'medium')).resolves.toBeDefined();
			await expect(jobScheduler.scheduleDomainCrawlJob('test.com', 'full', 'high')).resolves.toBeDefined();
		});
	});

	describe('queue management', () =>
	{
		beforeEach(async () =>
		{
			await jobScheduler.initialize();
		});

		it('should pause and resume queues', async () =>
		{
			const queueName = 'queue:domain:crawl';

			// Register a handler first
			const mockHandler = vi.fn();
			await jobScheduler.registerJobHandler(queueName, mockHandler);

			// Pause the queue
			await jobScheduler.pauseQueue(queueName);

			// Resume the queue
			await jobScheduler.resumeQueue(queueName);

			// Should not throw
			expect(true).toBe(true);
		});

		it('should purge queues', async () =>
		{
			const queueName = 'queue:domain:crawl';

			await expect(jobScheduler.purgeQueue(queueName)).resolves.not.toThrow();
		});
	});
});
