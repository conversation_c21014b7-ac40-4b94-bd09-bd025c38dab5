import { describe, it, expect, vi } from 'vitest';

// Mock all external dependencies
vi.mock('@shared', () => ({
	DatabaseManager: vi.fn(),
	logger: {
		getLogger: vi.fn(() => ({
			info: vi.fn(),
			error: vi.fn(),
			warn: vi.fn(),
			debug: vi.fn(),
		})),
	},
	Constants: {
		JOB_QUEUES: {
			DOMAIN_CRAWL: 'queue:domain:crawl',
			RANKING_UPDATE: 'queue:ranking:update',
			TRAFFIC_ANALYSIS: 'queue:traffic:analysis',
			BACKLINK_ANALYSIS: 'queue:backlink:analysis',
			MANTICORE_SYNC: 'queue:manticore:sync',
			MAINTENANCE: 'queue:maintenance',
		},
	},
	HealthChecker: vi.fn(),
	MetricsCollector: vi.fn(),
	Config: {
		validate: vi.fn(),
		get: vi.fn(),
	},
}));

vi.mock('@shared/utils/Config', () => ({
	config: {
		getAll: vi.fn(() => ({})),
	},
}));

vi.mock('redis-smq', () => ({
	Producer: vi.fn(),
	Consumer: vi.fn(),
	Message: vi.fn(),
}));

vi.mock('node-cron', () => ({
	schedule: vi.fn(),
}));

describe('Scheduler Module Integration', () =>
{
	it('should export all scheduler components', async () =>
	{
		const schedulerModule = await import('../index');

		expect(schedulerModule.JobScheduler).toBeDefined();
		expect(schedulerModule.CrawlJobManager).toBeDefined();
		expect(schedulerModule.SchedulerService).toBeDefined();
		expect(schedulerModule.SchedulerHealthService).toBeDefined();
	});

	it('should have proper type exports', async () =>
	{
		// This test ensures the types are properly exported
		// The actual types are checked at compile time
		const schedulerModule = await import('../index');

		expect(typeof schedulerModule.JobScheduler).toBe('function');
		expect(typeof schedulerModule.CrawlJobManager).toBe('function');
		expect(typeof schedulerModule.SchedulerService).toBe('function');
		expect(typeof schedulerModule.SchedulerHealthService).toBe('function');
	});

	it('should create scheduler instances without errors', async () =>
	{
		// Test that classes can be instantiated (with mocked dependencies)
		const { default: JobScheduler } = await import('../JobScheduler');
		const { default: SchedulerService } = await import('../SchedulerService');
		const { default: SchedulerHealthService } = await import('../SchedulerHealthService');

		expect(() => new JobScheduler()).not.toThrow();
		expect(() => new SchedulerService()).not.toThrow();
		expect(() => new SchedulerHealthService()).not.toThrow();
	});
});
