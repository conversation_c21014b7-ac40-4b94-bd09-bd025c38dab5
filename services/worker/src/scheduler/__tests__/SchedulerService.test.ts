import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import SchedulerService from '../SchedulerService';

// Mock the shared modules
vi.mock('@shared', () => ({
	DatabaseManager: vi.fn().mockImplementation(() => ({
		initialize: vi.fn().mockResolvedValue(undefined),
		close: vi.fn().mockResolvedValue(undefined),
		getScyllaClient: vi.fn().mockReturnValue({
			execute: vi.fn().mockResolvedValue({ rows: [] }),
		}),
		healthCheck: vi.fn().mockResolvedValue({ status: 'healthy' }),
	})),
	logger: {
		getLogger: vi.fn(() => ({
			info: vi.fn(),
			error: vi.fn(),
			warn: vi.fn(),
			debug: vi.fn(),
		})),
	},
	Config: {
		validate: vi.fn(),
		get: vi.fn((key, defaultValue) => defaultValue),
	},
}));

// Mock node-cron
vi.mock('node-cron', () => ({
	schedule: vi.fn().mockReturnValue({
		start: vi.fn(),
		stop: vi.fn(),
		running: false,
	}),
}));

// Mock JobScheduler
vi.mock('../JobScheduler', () => ({
	default: vi.fn().mockImplementation(() => ({
		initialize: vi.fn().mockResolvedValue(undefined),
		shutdown: vi.fn().mockResolvedValue(undefined),
		scheduleRankingUpdateJob: vi.fn().mockResolvedValue('job-id'),
		healthCheck: vi.fn().mockResolvedValue({ status: 'healthy' }),
	})),
}));

// Mock CrawlJobManager
vi.mock('../CrawlJobManager', () => ({
	default: vi.fn().mockImplementation(() => ({
		initialize: vi.fn().mockResolvedValue(undefined),
		shutdown: vi.fn().mockResolvedValue(undefined),
		createBatchCrawlJobs: vi.fn().mockResolvedValue(['job1', 'job2']),
		cleanupOldJobs: vi.fn().mockResolvedValue(5),
		healthCheck: vi.fn().mockResolvedValue({ status: 'healthy' }),
		getQueueStats: vi.fn().mockResolvedValue([]),
		getJobStatsByStatus: vi.fn().mockResolvedValue({}),
	})),
}));

describe('SchedulerService', () =>
{
	let schedulerService: SchedulerService;

	beforeEach(async () =>
	{
		schedulerService = new SchedulerService();
	});

	afterEach(async () =>
	{
		if (schedulerService)
		{
			await schedulerService.shutdown();
		}
	});

	describe('initialization', () =>
	{
		it('should initialize successfully', async () =>
		{
			await expect(schedulerService.initialize()).resolves.not.toThrow();
		});

		it('should setup scheduled jobs during initialization', async () =>
		{
			await schedulerService.initialize();

			// Should not throw and complete initialization
			expect(true).toBe(true);
		});
	});

	describe('scheduled job management', () =>
	{
		beforeEach(async () =>
		{
			await schedulerService.initialize();
		});

		it('should start and stop scheduled jobs', () =>
		{
			// Start jobs
			schedulerService.startScheduledJobs();

			// Stop jobs
			schedulerService.stopScheduledJobs();

			// Should not throw
			expect(true).toBe(true);
		});

		it('should schedule daily crawls', async () =>
		{
			await expect(schedulerService.scheduleDailyCrawls()).resolves.not.toThrow();
		});

		it('should schedule priority crawls', async () =>
		{
			await expect(schedulerService.schedulePriorityCrawls()).resolves.not.toThrow();
		});

		it('should schedule ranking updates', async () =>
		{
			await expect(schedulerService.scheduleRankingUpdates()).resolves.not.toThrow();
		});

		it('should cleanup old jobs', async () =>
		{
			await expect(schedulerService.cleanupOldJobs()).resolves.not.toThrow();
		});

		it('should run domain discovery', async () =>
		{
			await expect(schedulerService.runDomainDiscovery()).resolves.not.toThrow();
		});
	});

	describe('service lifecycle', () =>
	{
		beforeEach(async () =>
		{
			await schedulerService.initialize();
		});

		it('should start the service', async () =>
		{
			// Start service in background
			const startPromise = schedulerService.start();

			// Stop it immediately
			await schedulerService.stop();

			// Wait for start to complete
			await expect(startPromise).resolves.not.toThrow();
		});

		it('should perform graceful shutdown', async () =>
		{
			await expect(schedulerService.shutdown()).resolves.not.toThrow();
		});
	});

	describe('health monitoring', () =>
	{
		beforeEach(async () =>
		{
			await schedulerService.initialize();
		});

		it('should return health status', async () =>
		{
			const health = await schedulerService.healthCheck();

			expect(health).toHaveProperty('service');
			expect(health).toHaveProperty('status');
			expect(health).toHaveProperty('timestamp');
			expect(health).toHaveProperty('scheduledJobs');
		});

		it('should return job statistics', async () =>
		{
			const stats = await schedulerService.getJobStatistics();

			expect(stats).toHaveProperty('queueStats');
			expect(stats).toHaveProperty('jobStats');
			expect(stats).toHaveProperty('scheduledJobs');
		});
	});

	describe('domain management', () =>
	{
		beforeEach(async () =>
		{
			await schedulerService.initialize();
		});

		it('should get domains for crawling', async () =>
		{
			const domains = await schedulerService.getDomainsForCrawling();

			expect(Array.isArray(domains)).toBe(true);
			expect(domains.every(d => typeof d.name === 'string')).toBe(true);
		});

		it('should get priority domains for crawling', async () =>
		{
			const domains = await schedulerService.getPriorityDomainsForCrawling();

			expect(Array.isArray(domains)).toBe(true);
			expect(domains.every(d => typeof d.name === 'string')).toBe(true);
		});

		it('should get domains for ranking update', async () =>
		{
			const domains = await schedulerService.getDomainsForRankingUpdate();

			expect(Array.isArray(domains)).toBe(true);
			expect(domains.every(d => typeof d.name === 'string')).toBe(true);
		});
	});

	describe('utility functions', () =>
	{
		it('should normalize priority values', () =>
		{
			// Test the private method indirectly through scheduled crawls
			expect(() => schedulerService.scheduleDailyCrawls()).not.toThrow();
		});

		it('should handle delay utility', async () =>
		{
			const start = Date.now();
			await schedulerService.delay(10);
			const end = Date.now();

			expect(end - start).toBeGreaterThanOrEqual(10);
		});
	});
});
