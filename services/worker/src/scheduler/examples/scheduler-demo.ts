/**
 * Scheduler Module Demo
 *
 * This example demonstrates how to use the extracted scheduler functionality
 * from the original scheduler service within the worker service.
 */

import { JobScheduler, CrawlJobManager, SchedulerService, SchedulerHealthService } from '../index';
import type { CrawlJobRequest, JobOptionsType } from '../index';
import { logger } from '@shared';

// Create logger instance for the scheduler demo
const demoLogger = logger.getLogger('scheduler-demo');

/**
 * Demo: Basic Job Scheduling
 */
async function demoBasicJobScheduling()
{
	demoLogger.info({
		demo: 'basic-job-scheduling',
		status: 'starting'
	}, 'Basic Job Scheduling Demo');

	const jobScheduler = new JobScheduler();

	try
	{
		// Initialize the job scheduler
		await jobScheduler.initialize();
		demoLogger.info('Job scheduler initialized successfully');

		// Schedule a domain crawl job
		const crawlJobId = await jobScheduler.scheduleDomainCrawlJob(
			'example.com',
			'full',
			'high'
		);
		demoLogger.info({
			jobId: crawlJobId,
			domain: 'example.com',
			crawlType: 'full',
			priority: 'high'
		}, 'Scheduled crawl job');

		// Schedule a ranking update job
		const rankingJobId = await jobScheduler.scheduleRankingUpdateJob(
			'example.com',
			'global',
			'medium'
		);
		demoLogger.info({
			jobId: rankingJobId,
			domain: 'example.com',
			updateType: 'global',
			priority: 'medium'
		}, 'Scheduled ranking job');

		// Schedule batch jobs
		const batchJobs = [
			{ domain: 'site1.com', crawlType: 'quick' },
			{ domain: 'site2.com', crawlType: 'security' },
			{ domain: 'site3.com', crawlType: 'performance' },
		];

		const batchJobIds = await jobScheduler.scheduleBatchJobs(
			'queue:domain:crawl',
			batchJobs,
			{
				priority: 'medium',
				batchSize: 2,
				delayBetweenBatches: 100,
			}
		);
		demoLogger.info({
			batchJobCount: batchJobIds.length,
			queueName: 'queue:domain:crawl',
			batchSize: 2,
			delayBetweenBatches: 100
		}, 'Scheduled batch jobs');

		// Get job statistics
		const stats = jobScheduler.getAllJobStats();
		demoLogger.info({
			statistics: stats
		}, 'Job Statistics Retrieved');

		// Health check
		const health = await jobScheduler.healthCheck();
		demoLogger.info({
			healthStatus: health
		}, 'Scheduler Health Check');

		await jobScheduler.shutdown();
		demoLogger.info('Job scheduler shutdown completed');
	}
	catch (error)
	{
		demoLogger.error({
			error: error instanceof Error ? error.message : String(error),
			demo: 'basic-job-scheduling'
		}, 'Job scheduling demo failed');
	}
}

/**
 * Demo: Crawl Job Management
 */
async function demoCrawlJobManagement()
{
	demoLogger.info({
		demo: 'crawl-job-management',
		status: 'starting'
	}, 'Crawl Job Management Demo');

	const jobScheduler = new JobScheduler();
	const crawlJobManager = new CrawlJobManager(jobScheduler);

	try
	{
		await jobScheduler.initialize();
		await crawlJobManager.initialize();
		demoLogger.info('Job scheduler and crawl job manager initialized successfully');

		// Create individual crawl jobs
		const jobRequest: CrawlJobRequest = {
			domain: 'demo-site.com',
			crawlType: 'full',
			priority: 'high',
			requestedBy: 'demo-user',
			metadata: {
				source: 'manual',
				tags: ['demo', 'test'],
			},
		};

		const jobId = await crawlJobManager.createCrawlJob(jobRequest);
		demoLogger.info({
			jobId,
			domain: jobRequest.domain,
			crawlType: jobRequest.crawlType,
			priority: jobRequest.priority,
			requestedBy: jobRequest.requestedBy
		}, 'Created crawl job');

		// Create batch crawl jobs
		const batchRequests: CrawlJobRequest[] = [
			{
				domain: 'batch1.com',
				crawlType: 'quick',
				priority: 'medium',
			},
			{
				domain: 'batch2.com',
				crawlType: 'security',
				priority: 'low',
			},
		];

		const batchJobIds = await crawlJobManager.createBatchCrawlJobs(batchRequests);
		demoLogger.info({
			batchJobCount: batchJobIds.length,
			domains: batchRequests.map(req => req.domain)
		}, 'Created batch crawl jobs');

		// Monitor job status
		const jobStatus = crawlJobManager.getJobStatus(jobId);
		demoLogger.info({
			jobId,
			status: jobStatus
		}, 'Job Status Retrieved');

		// Get jobs by domain
		const domainJobs = crawlJobManager.getJobStatusesByDomain('demo-site.com');
		demoLogger.info({
			domain: 'demo-site.com',
			jobCount: domainJobs.length
		}, 'Found jobs for domain');

		// Update job progress (simulated)
		await crawlJobManager.updateJobProgress(
			jobId,
			'demo-site.com',
			'dns_lookup',
			25,
			'Performing DNS lookup',
			{ recordsFound: 5 }
		);
		demoLogger.info({
			jobId,
			domain: 'demo-site.com',
			stage: 'dns_lookup',
			progress: 25,
			recordsFound: 5
		}, 'Updated job progress');

		// Get job progress
		const progress = crawlJobManager.getJobProgress(jobId);
		demoLogger.info({
			jobId,
			progress
		}, 'Job Progress Retrieved');

		// Get statistics
		const queueStats = await crawlJobManager.getQueueStats();
		const statusStats = crawlJobManager.getJobStatsByStatus();
		demoLogger.info({
			queueStats
		}, 'Queue Statistics');
		demoLogger.info({
			statusStats
		}, 'Status Statistics');

		// Health check
		const health = await crawlJobManager.healthCheck();
		demoLogger.info({
			healthStatus: health
		}, 'Crawl Job Manager Health Check');

		await crawlJobManager.shutdown();
		await jobScheduler.shutdown();
		demoLogger.info('Crawl job manager and job scheduler shutdown completed');
	}
	catch (error)
	{
		demoLogger.error({
			error: error instanceof Error ? error.message : String(error),
			demo: 'crawl-job-management'
		}, 'Crawl job management demo failed');
	}
}

/**
 * Demo: Full Scheduler Service
 */
async function demoSchedulerService()
{
	demoLogger.info({
		demo: 'scheduler-service',
		status: 'starting'
	}, 'Scheduler Service Demo');

	const schedulerService = new SchedulerService();

	try
	{
		// Initialize the scheduler service
		await schedulerService.initialize();
		demoLogger.info('Scheduler service initialized successfully');

		// Get health status
		const health = await schedulerService.healthCheck();
		demoLogger.info({
			healthStatus: health
		}, 'Scheduler Service Health Check');

		// Get job statistics
		const stats = await schedulerService.getJobStatistics();
		demoLogger.info({
			statistics: stats
		}, 'Job Statistics Retrieved');

		// Manually trigger scheduled operations (normally done by cron)
		demoLogger.info('Triggering scheduled operations...');

		await schedulerService.scheduleDailyCrawls();
		demoLogger.info({
			operation: 'dailyCrawls',
			status: 'completed'
		}, 'Daily crawls scheduled');

		await schedulerService.schedulePriorityCrawls();
		demoLogger.info({
			operation: 'priorityCrawls',
			status: 'completed'
		}, 'Priority crawls scheduled');

		await schedulerService.scheduleRankingUpdates();
		demoLogger.info({
			operation: 'rankingUpdates',
			status: 'completed'
		}, 'Ranking updates scheduled');

		await schedulerService.runDomainDiscovery();
		demoLogger.info({
			operation: 'domainDiscovery',
			status: 'completed'
		}, 'Domain discovery completed');

		await schedulerService.cleanupOldJobs();
		demoLogger.info({
			operation: 'cleanupOldJobs',
			status: 'completed'
		}, 'Old jobs cleanup completed');

		// Get domain lists
		const crawlDomains = await schedulerService.getDomainsForCrawling();
		const priorityDomains = await schedulerService.getPriorityDomainsForCrawling();
		const rankingDomains = await schedulerService.getDomainsForRankingUpdate();

		demoLogger.info({
			crawlDomainsCount: crawlDomains.length,
			priorityDomainsCount: priorityDomains.length,
			rankingDomainsCount: rankingDomains.length
		}, 'Domain lists retrieved');

		await schedulerService.shutdown();
		demoLogger.info('Scheduler service shutdown completed');
	}
	catch (error)
	{
		demoLogger.error({
			error: error instanceof Error ? error.message : String(error),
			demo: 'scheduler-service'
		}, 'Scheduler service demo failed');
	}
}

/**
 * Demo: Health Monitoring
 */
async function demoHealthMonitoring()
{
	demoLogger.info({
		demo: 'health-monitoring',
		status: 'starting'
	}, 'Health Monitoring Demo');

	const healthService = new SchedulerHealthService();

	try
	{
		// Get basic health status
		const basicHealth = await healthService.getHealthStatus(false);
		demoLogger.info({
			healthStatus: basicHealth,
			detailLevel: 'basic'
		}, 'Basic Health Status Retrieved');

		// Get detailed health status with metrics
		const detailedHealth = await healthService.getHealthStatus(true);
		demoLogger.info({
			healthStatus: detailedHealth,
			detailLevel: 'detailed'
		}, 'Detailed Health Status Retrieved');

		// Check readiness
		const isReady = await healthService.isReady();
		demoLogger.info({
			serviceReady: isReady
		}, 'Service Readiness Check');

		// Get scheduler-specific metrics
		const metrics = await healthService.getSchedulerMetrics();
		demoLogger.info({
			schedulerMetrics: metrics
		}, 'Scheduler Metrics Retrieved');

		// Record some sample metrics
		healthService.recordJobSchedulingMetrics('crawl', 'high', true);
		healthService.recordJobProcessingMetrics('crawl', 5000, true);
		healthService.recordQueueMetrics('domain-crawl', 10, 2);
		healthService.recordWorkerMetrics('worker-1', 0.8, 5);
		healthService.recordCronExecutionMetrics('daily-crawl', 30000, true);

		demoLogger.info({
			metricsRecorded: [
				{ type: 'job-scheduling', jobType: 'crawl', priority: 'high', success: true },
				{ type: 'job-processing', jobType: 'crawl', duration: 5000, success: true },
				{ type: 'queue-metrics', queueName: 'domain-crawl', size: 10, workers: 2 },
				{ type: 'worker-metrics', workerId: 'worker-1', cpuUsage: 0.8, activeJobs: 5 },
				{ type: 'cron-execution', cronName: 'daily-crawl', duration: 30000, success: true }
			]
		}, 'Sample metrics recorded');

		await healthService.cleanup();
		demoLogger.info('Health service cleanup completed');
	}
	catch (error)
	{
		demoLogger.error({
			error: error instanceof Error ? error.message : String(error),
			demo: 'health-monitoring'
		}, 'Health monitoring demo failed');
	}
}

/**
 * Demo: Job Handler Registration
 */
async function demoJobHandlerRegistration()
{
	demoLogger.info({
		demo: 'job-handler-registration',
		status: 'starting'
	}, 'Job Handler Registration Demo');

	const jobScheduler = new JobScheduler();

	try
	{
		await jobScheduler.initialize();
		demoLogger.info('Job scheduler initialized successfully');

		// Register a custom job handler
		await jobScheduler.registerJobHandler('queue:domain:crawl', async (jobData, message) =>
		{
			demoLogger.info({
				jobId: jobData.id,
				domain: jobData.domain,
				status: 'processing'
			}, 'Processing job');

			// Simulate job processing
			await new Promise(resolve => setTimeout(resolve, 1000));

			demoLogger.info({
				jobId: jobData.id,
				domain: jobData.domain,
				status: 'completed'
			}, 'Job completed successfully');
		});

		demoLogger.info({
			queueName: 'queue:domain:crawl'
		}, 'Job handler registered for domain crawl queue');

		// Schedule a job to test the handler
		const jobId = await jobScheduler.scheduleDomainCrawlJob('test-handler.com', 'quick', 'medium');
		demoLogger.info({
			jobId,
			domain: 'test-handler.com',
			crawlType: 'quick',
			priority: 'medium'
		}, 'Scheduled test job');

		// Wait a bit for processing
		await new Promise(resolve => setTimeout(resolve, 2000));
		demoLogger.info('Job processing wait period completed');

		await jobScheduler.shutdown();
		demoLogger.info('Job scheduler shutdown completed');
	}
	catch (error)
	{
		demoLogger.error({
			error: error instanceof Error ? error.message : String(error),
			demo: 'job-handler-registration'
		}, 'Job handler registration demo failed');
	}
}

/**
 * Run all demos
 */
async function runAllDemos()
{
	demoLogger.info({
		demoSuite: 'scheduler-module-demos',
		status: 'starting'
	}, 'Starting Scheduler Module Demos');

	try
	{
		await demoBasicJobScheduling();
		demoLogger.info({ demo: 'basic-job-scheduling', status: 'completed' }, 'Demo completed');

		await demoCrawlJobManagement();
		demoLogger.info({ demo: 'crawl-job-management', status: 'completed' }, 'Demo completed');

		await demoSchedulerService();
		demoLogger.info({ demo: 'scheduler-service', status: 'completed' }, 'Demo completed');

		await demoHealthMonitoring();
		demoLogger.info({ demo: 'health-monitoring', status: 'completed' }, 'Demo completed');

		await demoJobHandlerRegistration();
		demoLogger.info({ demo: 'job-handler-registration', status: 'completed' }, 'Demo completed');

		demoLogger.info({
			demoSuite: 'scheduler-module-demos',
			status: 'all-completed',
			totalDemos: 5
		}, 'All demos completed successfully!');
	}
	catch (error)
	{
		demoLogger.error({
			error: error instanceof Error ? error.message : String(error),
			demoSuite: 'scheduler-module-demos'
		}, 'Demo execution failed');
	}
}

// Export demo functions for individual use
export {
	demoBasicJobScheduling,
	demoCrawlJobManagement,
	demoSchedulerService,
	demoHealthMonitoring,
	demoJobHandlerRegistration,
	runAllDemos,
};

// Run demos if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`)
{
	runAllDemos();
}
