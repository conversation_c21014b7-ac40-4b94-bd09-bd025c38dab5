# Scheduler Module

This module contains all scheduling and job management functionality extracted from the scheduler service. It provides comprehensive job scheduling, queuing, lifecycle management, and cron-based automation.

## Components

### Core Scheduling

- **JobScheduler**: Redis-SMQ job scheduler with priority handling and retry mechanisms
- **CrawlJobManager**: Comprehensive job creation, status tracking, and progress monitoring
- **SchedulerService**: Main scheduler service with lifecycle management and cron jobs

### Job Management

- **Job Types**: Domain crawl, ranking update, traffic analysis, backlink analysis, Manticore sync, maintenance
- **Priority Handling**: Low, medium, high priority with proper queue management
- **Retry Logic**: Exponential backoff retry mechanisms with dead letter queue support
- **Batch Processing**: Efficient batch job creation and processing

### Cron Scheduling

- **Daily Crawls**: Automated daily domain crawling
- **Priority Crawls**: Hourly priority domain processing
- **Weekly Ranking Updates**: Comprehensive ranking recalculation
- **Cleanup Jobs**: Automated maintenance and old job removal

### Monitoring & Health

- **Job Statistics**: Comprehensive job queue and processing metrics
- **Progress Tracking**: Detailed job progress monitoring with stage tracking
- **Health Checks**: Service health monitoring for queues, cron jobs, and workers
- **Performance Metrics**: Job processing times, queue sizes, worker utilization

## Features

- Complete Redis-SMQ integration with all queue configurations
- Comprehensive job lifecycle management (pending → queued → processing → completed/failed)
- Exponential backoff retry logic with configurable maximum retries
- Dead letter queue management for permanently failed jobs
- Cron job scheduling with timezone support and error handling
- Domain discovery and prioritization algorithms
- Job statistics collection and performance monitoring
- Graceful startup/shutdown with proper resource cleanup
- Database persistence for job status and progress tracking
- Batch job processing with rate limiting and concurrency control

## Usage

The scheduler module is integrated into the worker service pipeline and handles:

1. Job creation and queuing from external systems
2. Automated job scheduling based on cron schedules
3. Job processing coordination with the worker pipeline
4. Progress tracking and status updates
5. Error handling and retry management
6. Performance monitoring and health checks
