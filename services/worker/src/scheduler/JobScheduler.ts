import { Producer, Consumer, Message } from 'redis-smq';
import { logger, Constants } from '@shared';
import { config } from '@shared/utils/Config';
import { v4 as uuidv4 } from 'uuid';

type JobDataType = {
	id: string;
	domain?: string;
	crawlType?: 'full' | 'quick' | 'security' | 'performance' | 'content';
	type: string;
	priority: 'low' | 'normal' | 'high';
	timeout: number;
	createdAt: Date;
	startedAt?: Date;
	completedAt?: Date;
	duration?: number;
	status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
	progress: number;
	error?: string;
	errorMessage?: string;
	workerId?: string;
	retryCount: number;
	maxRetries: number;
	scheduledAt?: Date;
	metadata: Record<string, unknown>;
	data: unknown;
};

type JobOptionsType = {
	priority?: 'low' | 'medium' | 'high';
	delay?: number;
	timeout?: number;
	maxRetries?: number;
	ttl?: number;
};

type QueueConfigType = {
	name: string;
	concurrency: number;
	enabled: boolean;
	retryThreshold?: number;
};

type JobStatsType = {
	queueName: string;
	pendingJobs: number;
	activeJobs: number;
	completedJobs: number;
	failedJobs: number;
	delayedJobs: number;
	processingJobs: number;
	retryJobs: number;
	waitTimeMs: {
		average: number;
		min: number;
		max: number;
		p95: number;
		p99: number;
	};
};

type JobHandlerType = (jobData: JobDataType, message: Message) => Promise<void>;

/**
 * Redis-SMQ Job Scheduler
 * Handles job scheduling, queuing, and processing with priority handling and retry mechanisms
 *
 * Extracted from scheduler service with complete Redis-SMQ integration,
 * job scheduling, queuing, lifecycle management, priority handling,
 * and exponential backoff retry mechanisms.
 */
class JobScheduler
{
	private producer: Producer | null = null;

	private consumers = new Map<string, Consumer>();

	private jobHandlers = new Map<string, JobHandlerType>();

	private queueConfigs = new Map<string, QueueConfigType>();

	private logger = logger.getLogger('JobScheduler');

	private isInitialized = false;

	private config = config.getAll();

	private jobStats = new Map<string, JobStatsType>();

	constructor()
	{
		this.setupDefaultQueues();
	}

	/**
	 * Setup default queue configurations
	 */
	private setupDefaultQueues(): void
	{
		const defaultQueues: QueueConfigType[] = [
			{
				name: Constants.JOB_QUEUES.DOMAIN_CRAWL,
				concurrency: 5,
				retryThreshold: 3,
				enabled: true,
			},
			{
				name: Constants.JOB_QUEUES.RANKING_UPDATE,
				concurrency: 3,
				retryThreshold: 3,
				enabled: true,
			},
			{
				name: Constants.JOB_QUEUES.TRAFFIC_ANALYSIS,
				concurrency: 2,
				retryThreshold: 2,
				enabled: true,
			},
			{
				name: Constants.JOB_QUEUES.BACKLINK_ANALYSIS,
				concurrency: 2,
				retryThreshold: 2,
				enabled: true,
			},
			{
				name: Constants.JOB_QUEUES.MANTICORE_SYNC,
				concurrency: 1,
				retryThreshold: 3,
				enabled: true,
			},
			{
				name: Constants.JOB_QUEUES.MAINTENANCE,
				concurrency: 1,
				retryThreshold: 1,
				enabled: true,
			},
		];

		for (const queueConfig of defaultQueues)
		{
			this.queueConfigs.set(queueConfig.name, queueConfig);
			this.initializeJobStats(queueConfig.name);
		}
	}

	/**
	 * Initialize job statistics for a queue
	 */
	private initializeJobStats(queueName: string): void
	{
		this.jobStats.set(queueName, {
			queueName,
			pendingJobs: 0,
			processingJobs: 0,
			completedJobs: 0,
			failedJobs: 0,
			delayedJobs: 0,
			retryJobs: 0,
			activeJobs: 0,
			waitTimeMs: {
				average: 0,
				min: 0,
				max: 0,
				p95: 0,
				p99: 0,
			},
		});
	}

	/**
	 * Initialize the job scheduler
	 */
	async initialize(): Promise<void>
	{
		try
		{
			this.logger.info('Initializing Redis-SMQ Job Scheduler...');

			// Initialize producer
			this.producer = new Producer();
			await this.producer.run();

			this.isInitialized = true;
			this.logger.info('Redis-SMQ Job Scheduler initialized successfully');
		}
		catch (error)
		{
			this.logger.error('Failed to initialize Redis-SMQ Job Scheduler:', error);
			throw error;
		}
	}

	/**
	 * Schedule a job with priority handling
	 */
	async scheduleJob(
		queueName: string,
		jobData: Record<string, unknown>,
		options: JobOptionsType = {},
	): Promise<string>
	{
		if (!this.isInitialized || !this.producer)
		{
			throw new Error('Job scheduler not initialized');
		}

		const queueConfig = this.queueConfigs.get(queueName);
		if (!queueConfig || !queueConfig.enabled)
		{
			throw new Error(`Queue ${queueName} is not configured or disabled`);
		}

		try
		{
			const job: JobDataType = {
				id: uuidv4(),
				priority: options.priority || 'medium',
				retryCount: 0,
				maxRetries: options.maxRetries || 3,
				scheduledAt: new Date(),
				data: jobData,
				...jobData,
			};

			// Calculate priority value for Redis-SMQ (convert 'normal' to 'medium')
			const priority = job.priority === 'normal' ? 'medium' : job.priority;

			if (!this.producer)
			{
				throw new Error('Producer not initialized');
			}

			// Create message
			const message = new Message();
			message.setBody(JSON.stringify(job));

			// Convert priority to number (1=high, 2=medium, 3=low)
			const priorityNumber = priority === 'high' ? 1 : priority === 'medium' ? 2 : 3;
			message.setPriority(priorityNumber);

			// Publish the message with error handling
			try
			{
				// Publish the message to the queue
				await new Promise<void>((resolve, reject) =>
				{
					this.producer?.produce(message, (err) =>
					{
						if (err)
						{
							reject(err);
						}
						else
						{
							resolve();
						}
					});
				});
			}
			catch (error)
			{
				throw new Error(`Failed to produce message: ${error instanceof Error ? error.message : String(error)}`);
			}

			// Update statistics
			this.updateJobStats(queueName, 'scheduled');

			this.logger.info(`Job scheduled successfully: ${job.id} in queue ${queueName} with priority ${job.priority}`);
			return job.id;
		}
		catch (error)
		{
			this.logger.error(`Failed to schedule job in queue ${queueName}:`, error);
			throw error;
		}
	}

	/**
	 * Register a job handler for a specific queue
	 */
	async registerJobHandler(queueName: string, handler: JobHandlerType): Promise<void>
	{
		if (!this.isInitialized)
		{
			throw new Error('Job scheduler not initialized');
		}

		const queueConfig = this.queueConfigs.get(queueName);
		if (!queueConfig)
		{
			throw new Error(`Queue ${queueName} is not configured`);
		}

		try
		{
			// Store the handler
			this.jobHandlers.set(queueName, handler);

			// Create consumer for the queue
			const consumer = new Consumer();

			consumer.consume(queueName, async (message: Message, cb: (error?: Error) => void) =>
			{
				const jobData = JSON.parse(message.getBody() as string) as JobDataType;
				jobData.startedAt = new Date();

				this.updateJobStats(queueName, 'processing');

				try
				{
					this.logger.info(`Processing job ${jobData.id} from queue ${queueName}`);

					// Call the registered handler
					await handler(jobData, message);

					// Mark job as completed
					jobData.completedAt = new Date();
					this.updateJobStats(queueName, 'completed');

					// Acknowledge successful processing
					cb();

					this.logger.info(`Job ${jobData.id} completed successfully`);
				}
				catch (error)
				{
					this.logger.error(`Job ${jobData.id} processing failed:`, error);

					// Handle retry logic with exponential backoff
					await this.handleJobFailure(queueName, jobData, error as Error, cb);
				}
			}, {
				concurrency: queueConfig.concurrency,
				messageRetryThreshold: queueConfig.retryThreshold,
			});

			await consumer.run();
			this.consumers.set(queueName, consumer);

			this.logger.info(`Job handler registered for queue ${queueName} with concurrency ${queueConfig.concurrency}`);
		}
		catch (error)
		{
			this.logger.error(`Failed to register job handler for queue ${queueName}:`, error);
			throw error;
		}
	}

	/**
	 * Handle job failure with exponential backoff retry mechanism
	 */
	private async handleJobFailure(
		queueName: string,
		jobData: JobDataType,
		error: Error,
		callback: (error?: Error) => void,
	): Promise<void>
	{
		jobData.errorMessage = error.message;
		jobData.retryCount = (jobData.retryCount || 0) + 1;

		if (jobData.retryCount <= jobData.maxRetries)
		{
			// Calculate exponential backoff delay
			const baseDelay = 1000; // 1 second
			const maxDelay = 300000; // 5 minutes
			const backoffDelay = Math.min(
				baseDelay * 2 ** (jobData.retryCount - 1),
				maxDelay,
			);

			this.logger.info(
				`Retrying job ${jobData.id} (attempt ${jobData.retryCount}/${jobData.maxRetries}) `
				+ `in ${backoffDelay}ms due to: ${error.message}`,
			);

			this.updateJobStats(queueName, 'retry');

			// Schedule retry with exponential backoff
			setTimeout(async () =>
			{
				try
				{
					await this.scheduleJob(queueName, jobData.data, {
						priority: jobData.priority === 'normal'
							? 'medium'
							: jobData.priority,
						maxRetries: jobData.maxRetries,
					});
				}
				catch (retryError)
				{
					this.logger.error(`Failed to schedule retry for job ${jobData.id}:`, retryError);
				}
			}, backoffDelay);

			// Acknowledge to remove from current queue
			callback();
		}
		else
		{
			// Max retries exceeded, mark as failed
			this.logger.error(
				`Job ${jobData.id} failed permanently after ${jobData.maxRetries} attempts. `
				+ `Final error: ${error.message}`,
			);

			this.updateJobStats(queueName, 'failed');

			// Send to dead letter queue or handle permanent failure
			await this.handlePermanentFailure(queueName, jobData, error);

			// Acknowledge with error to send to dead letter queue
			callback(error);
		}
	}

	/**
	 * Handle permanent job failure
	 */
	private async handlePermanentFailure(
		queueName: string,
		jobData: JobDataType,
		error: Error,
	): Promise<void>
	{
		try
		{
			// Log permanent failure
			this.logger.error(`Permanent failure for job ${jobData.id} in queue ${queueName}:`, {
				jobId: jobData.id,
				queueName,
				retryCount: jobData.retryCount,
				maxRetries: jobData.maxRetries,
				error: error.message,
				jobData: jobData.data,
			});

			// Could implement dead letter queue handling here
			// For now, just log the failure
		}
		catch (handlingError)
		{
			this.logger.error(`Failed to handle permanent failure for job ${jobData.id}:`, handlingError);
		}
	}

	/**
	 * Schedule domain crawl job
	 */
	async scheduleDomainCrawlJob(
		domain: string,
		crawlType: 'full' | 'quick' | 'security' | 'performance' | 'content' = 'full',
		priority: 'low' | 'medium' | 'high' = 'medium',
	): Promise<string>
	{
		const jobData = {
			domain,
			crawlType,
			scheduledAt: new Date().toISOString(),
		};

		return this.scheduleJob(Constants.JOB_QUEUES.DOMAIN_CRAWL, jobData, {
			priority,
			maxRetries: 3,
		});
	}

	/**
	 * Schedule ranking update job
	 */
	async scheduleRankingUpdateJob(
		domain: string,
		rankingType: string = 'global',
		priority: 'low' | 'medium' | 'high' = 'medium',
	): Promise<string>
	{
		const jobData = {
			domain,
			rankingType,
			scheduledAt: new Date().toISOString(),
		};

		return this.scheduleJob(Constants.JOB_QUEUES.RANKING_UPDATE, jobData, {
			priority,
			maxRetries: 2,
		});
	}

	/**
	 * Schedule batch jobs with rate limiting
	 */
	async scheduleBatchJobs(
		queueName: string,
		jobs: Record<string, unknown>[],
		options: JobOptionsType & {
			batchSize?: number;
			delayBetweenBatches?: number;
		} = {},
	): Promise<string[]>
	{
		const batchSize = options.batchSize || 10;
		const delayBetweenBatches = options.delayBetweenBatches || 100;
		const jobIds: string[] = [];

		for (let i = 0; i < jobs.length; i += batchSize)
		{
			const batch = jobs.slice(i, i + batchSize);

			// Schedule jobs in current batch
			const batchPromises = batch.map(jobData => this.scheduleJob(queueName, jobData, options));

			const batchJobIds = await Promise.all(batchPromises);
			jobIds.push(...batchJobIds);

			// Add delay between batches to avoid overwhelming the system
			if (i + batchSize < jobs.length)
			{
				await this.delay(delayBetweenBatches);
			}
		}

		this.logger.info(`Scheduled ${jobIds.length} batch jobs in queue ${queueName}`);
		return jobIds;
	}

	/**
	 * Get job statistics for a queue
	 */
	getJobStats(queueName: string): JobStatsType | undefined
	{
		return this.jobStats.get(queueName);
	}

	/**
	 * Get all job statistics
	 */
	getAllJobStats(): JobStatsType[]
	{
		return Array.from(this.jobStats.values());
	}

	/**
	 * Update job statistics
	 */
	private updateJobStats(queueName: string, action: 'scheduled' | 'processing' | 'completed' | 'failed' | 'retry'): void
	{
		const stats = this.jobStats.get(queueName);
		if (!stats) return;

		switch (action)
		{
			case 'scheduled':
				stats.pendingJobs++;
				break;
			case 'processing':
				stats.pendingJobs = Math.max(0, stats.pendingJobs - 1);
				stats.processingJobs++;
				break;
			case 'completed':
				stats.processingJobs = Math.max(0, stats.processingJobs - 1);
				stats.completedJobs++;
				break;
			case 'failed':
				stats.processingJobs = Math.max(0, stats.processingJobs - 1);
				stats.failedJobs++;
				break;
			case 'retry':
				stats.retryJobs++;
				break;
		}
	}

	/**
	 * Convert priority string to numeric value
	 */
	private getPriorityValue(priority: 'low' | 'medium' | 'high'): number
	{
		const priorities = {
			low: 1,
			medium: 5,
			high: 10,
		};
		return priorities[priority] || 5;
	}

	/**
	 * Pause queue processing
	 */
	async pauseQueue(queueName: string): Promise<void>
	{
		const consumer = this.consumers.get(queueName);
		if (consumer)
		{
			// Redis-SMQ doesn't have a direct pause method, so we'll stop the consumer
			await consumer.shutdown();
			this.consumers.delete(queueName);
			this.logger.info(`Queue ${queueName} paused`);
		}
	}

	/**
	 * Resume queue processing
	 */
	async resumeQueue(queueName: string): Promise<void>
	{
		const handler = this.jobHandlers.get(queueName);
		if (handler && !this.consumers.has(queueName))
		{
			await this.registerJobHandler(queueName, handler);
			this.logger.info(`Queue ${queueName} resumed`);
		}
	}

	/**
	 * Purge queue (remove all pending jobs)
	 */
	async purgeQueue(queueName: string): Promise<void>
	{
		try
		{
			// Implementation would depend on Redis-SMQ API
			// For now, log the action
			this.logger.info(`Purging queue: ${queueName}`);

			// Reset statistics
			this.initializeJobStats(queueName);
		}
		catch (error)
		{
			this.logger.error(`Failed to purge queue ${queueName}:`, error);
			throw error;
		}
	}

	/**
	 * Health check for job scheduler
	 */
	async healthCheck(): Promise<{
		status: 'healthy' | 'unhealthy';
		producer: boolean;
		consumers: number;
		queues: { [key: string]: boolean };
	}>
	{
		try
		{
			const health = {
				status: 'healthy' as 'healthy' | 'unhealthy',
				producer: !!this.producer && this.isInitialized,
				consumers: this.consumers.size,
				queues: {} as { [key: string]: boolean },
			};

			// Check each queue
			for (const [queueName, config] of this.queueConfigs)
			{
				health.queues[queueName] = config.enabled && this.consumers.has(queueName);
			}

			// Determine overall health
			if (!health.producer || health.consumers === 0)
			{
				health.status = 'unhealthy';
			}

			return health;
		}
		catch (error)
		{
			this.logger.error('Health check failed:', error);
			return {
				status: 'unhealthy',
				producer: false,
				consumers: 0,
				queues: {},
			};
		}
	}

	/**
	 * Shutdown job scheduler
	 */
	async shutdown(): Promise<void>
	{
		try
		{
			this.logger.info('Shutting down Redis-SMQ Job Scheduler...');

			// Shutdown all consumers
			for (const [queueName, consumer] of this.consumers)
			{
				await consumer.shutdown();
				this.logger.info(`Consumer for queue ${queueName} shut down`);
			}

			// Shutdown producer
			if (this.producer)
			{
				await this.producer.shutdown();
				this.producer = null;
			}

			// Clear collections
			this.consumers.clear();
			this.jobHandlers.clear();

			this.isInitialized = false;
			this.logger.info('Redis-SMQ Job Scheduler shut down successfully');
		}
		catch (error)
		{
			this.logger.error('Error shutting down Redis-SMQ Job Scheduler:', error);
			throw error;
		}
	}

	/**
	 * Delay utility
	 */
	private delay(ms: number): Promise<void>
	{
		return new Promise(resolve => setTimeout(resolve, ms));
	}
}

export type {
	JobDataType,
	JobOptionsType,
	QueueConfigType,
	JobStatsType,
	JobHandlerType,
};

export default JobScheduler;
