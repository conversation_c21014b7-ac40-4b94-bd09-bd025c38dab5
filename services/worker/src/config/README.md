# Worker Service Configuration Management

This module provides comprehensive configuration management for the worker service, extracted and unified from all three source services (crawler, ranking-engine, scheduler).

## Features

- **Environment Variables**: Full support for environment-based configuration
- **Configuration Files**: JSON, JS, and TS configuration file support
- **Runtime Updates**: Hot reloading and runtime configuration changes
- **Validation**: Comprehensive validation with detailed error messages
- **Security**: Encryption, access control, and secure configuration handling
- **Templates**: Pre-defined configuration templates for different environments
- **Migration**: Automatic migration from legacy configurations
- **Monitoring**: Configuration change tracking and audit logging

## Quick Start

```typescript
import { configManager } from "./config";

// Initialize configuration manager
await configManager.initialize({
  configFilePath: "./config/worker.json",
  watchFileChanges: true,
  encryptionKey: process.env.CONFIG_ENCRYPTION_KEY,
});

// Get configuration values
const maxTasks = configManager.get("processing.worker.maxConcurrentTasks", 10);
const dbHosts = configManager.get("database.scylla.hosts");

// Update configuration at runtime
await configManager.set("processing.worker.maxConcurrentTasks", 20);

// Listen for configuration changes
configManager.on("configChanged", (event) => {
  console.log(`Configuration changed: ${event.path}`, event);
});
```

## Configuration Structure

The worker configuration is organized into logical sections:

### Database Configuration

```typescript
{
  database: {
    scylla: {
      hosts: ['scylla-1:9042', 'scylla-2:9042'],
      keyspace: 'domain_ranking',
      localDataCenter: 'datacenter1',
      username: 'worker_user',
      password: 'secure_password',
      connectTimeout: 30000,
      requestTimeout: 12000,
      maxRetries: 3
    },
    maria: {
      host: 'mariadb',
      port: 3306,
      user: 'worker_user',
      password: 'secure_password',
      database: 'domain_ranking',
      connectionLimit: 10,
      acquireTimeout: 60000,
      timeout: 60000,
      reconnect: true
    },
    redis: {
      url: 'redis://redis:6379',
      db: 0,
      password: 'secure_password',
      maxRetriesPerRequest: 3,
      retryDelayOnFailover: 100,
      connectTimeout: 10000,
      commandTimeout: 5000
    },
    manticore: {
      host: 'manticore',
      port: 9308,
      url: 'http://manticore:9308',
      timeout: 30000,
      maxRetries: 3
    }
  }
}
```

### Processing Configuration

```typescript
{
  processing: {
    worker: {
      instanceId: 'worker-1',
      maxConcurrentTasks: 10,
      taskTimeout: 300000,
      gracefulShutdownTimeout: 30000
    },
    crawling: {
      timeout: 30000,
      maxRetries: 3,
      rateLimit: {
        requestsPerMinute: 60,
        concurrentRequests: 5
      },
      userAgents: ['Mozilla/5.0 ...'],
      enableScreenshots: true,
      enablePerformanceAudit: true,
      enableAiDescriptions: true,
      validateDescriptions: true
    },
    ranking: {
      weights: {
        performance: 0.25,
        security: 0.20,
        seo: 0.20,
        technical: 0.15,
        backlinks: 0.20
      },
      updateBatchSize: 100,
      recalculationInterval: 86400000,
      historyRetentionDays: 365
    },
    scheduling: {
      timezone: 'UTC',
      dailyCrawlTime: '02:00',
      priorityCrawlInterval: '0 * * * *',
      rankingUpdateInterval: '0 3 * * 0',
      cleanupInterval: '0 4 * * *',
      jobRetentionDays: 30
    },
    jobQueue: {
      retryAttempts: 3,
      retryDelay: 5000,
      deadLetterQueueEnabled: true,
      batchSize: 10,
      visibilityTimeout: 300,
      messageRetentionPeriod: 1209600
    }
  }
}
```

### External Services Configuration

```typescript
{
  externalServices: {
    browserless: {
      url: 'http://browserless:3000',
      timeout: 30000,
      maxRetries: 3,
      enabled: true
    },
    imageProxy: {
      url: 'https://images.weserv.nl',
      timeout: 30000,
      maxRetries: 3,
      enabled: true
    },
    ai: {
      openai: {
        apiKey: 'sk-...',
        model: 'gpt-4o-mini',
        timeout: 30000,
        maxRetries: 3,
        enabled: true
      },
      anthropic: {
        apiKey: 'sk-ant-...',
        model: 'claude-3-haiku-20240307',
        timeout: 30000,
        maxRetries: 3,
        enabled: true
      },
      google: {
        apiKey: 'AIza...',
        model: 'gemini-1.5-flash',
        timeout: 30000,
        maxRetries: 3,
        enabled: true
      },
      fallback: {
        enabled: true,
        maxRetries: 3,
        retryDelay: 1000,
        errorThreshold: 0.1
      },
      costTracking: {
        enabled: true,
        dailyBudget: 100,
        monthlyBudget: 2000
      },
      alerts: {
        enabled: true,
        dailyCostThreshold: 80,
        hourlyCostThreshold: 10,
        errorRateThreshold: 0.1,
        latencyThreshold: 30000
      }
    }
  }
}
```

## Environment Variables

All configuration can be overridden using environment variables:

### Core Settings

- `NODE_ENV` - Environment (development, staging, production, test)
- `SERVICE_NAME` - Service name
- `SERVICE_PORT` - Service port
- `WORKER_INSTANCE_ID` - Unique worker identifier
- `MAX_CONCURRENT_TASKS` - Maximum concurrent domain processing tasks

### Database Settings

- `SCYLLA_HOSTS` - Comma-separated ScyllaDB hosts
- `SCYLLA_KEYSPACE` - ScyllaDB keyspace
- `SCYLLA_USERNAME` - ScyllaDB username
- `SCYLLA_PASSWORD` - ScyllaDB password
- `MARIA_HOST` - MariaDB host
- `MARIA_PORT` - MariaDB port
- `MARIA_USER` - MariaDB username
- `MARIA_PASSWORD` - MariaDB password
- `MARIA_DATABASE` - MariaDB database name
- `REDIS_URL` - Redis connection URL
- `MANTICORE_HOST` - Manticore Search host
- `MANTICORE_PORT` - Manticore Search port

### External Services

- `BROWSERLESS_URL` - Browserless service URL
- `IMAGE_PROXY_URL` - Image proxy service URL
- `OPENAI_API_KEY` - OpenAI API key
- `ANTHROPIC_API_KEY` - Anthropic API key
- `GOOGLE_API_KEY` - Google AI API key

### Processing Settings

- `CRAWL_TIMEOUT` - Crawling timeout in milliseconds
- `CRAWL_MAX_RETRIES` - Maximum crawling retry attempts
- `RANKING_UPDATE_BATCH_SIZE` - Batch size for ranking updates
- `JOB_RETRY_ATTEMPTS` - Job retry attempts
- `JOB_RETRY_DELAY` - Job retry delay in milliseconds

### Feature Flags

- `ENABLE_SCREENSHOTS` - Enable screenshot capture
- `ENABLE_PERFORMANCE_AUDIT` - Enable performance auditing
- `ENABLE_AI_DESCRIPTIONS` - Enable AI-powered descriptions
- `VALIDATE_DESCRIPTIONS` - Enable description validation

### Monitoring Settings

- `LOG_LEVEL` - Logging level (error, warn, info, debug)
- `METRICS_ENABLED` - Enable metrics collection
- `HEALTH_CHECK_INTERVAL` - Health check interval in milliseconds

## Configuration Templates

Pre-defined templates for different environments:

### Production Template

```typescript
import { ConfigTemplates } from "./config";

const productionConfig = ConfigTemplates.getTemplate("production");
await configManager.applyTemplate(productionConfig, {
  SERVICE_NAME: "worker-service-prod",
  SCYLLA_HOSTS: "scylla-1:9042,scylla-2:9042,scylla-3:9042",
  JWT_SECRET: "your-super-secure-jwt-secret-here",
  // ... other variables
});
```

### Development Template

```typescript
const devConfig = ConfigTemplates.getTemplate("development");
await configManager.applyTemplate(devConfig);
```

### Available Templates

- `production` - Production-ready with security and monitoring
- `staging` - Staging environment for testing
- `development` - Development with debug features
- `test` - Test environment with minimal resources
- `high-performance` - Optimized for heavy workloads
- `minimal` - Resource-constrained environments
- `ai-enabled` - AI services fully configured
- `security-focused` - Enhanced security features

## Configuration Validation

Comprehensive validation with detailed error messages:

```typescript
import { ConfigValidator } from "./config";

const validator = new ConfigValidator();
const result = validator.validate(config);

if (!result.valid) {
  console.error("Configuration validation failed:");
  result.errors.forEach((error) => {
    console.error(`- ${error.path}: ${error.message}`);
  });
}

if (result.warnings.length > 0) {
  console.warn("Configuration warnings:");
  result.warnings.forEach((warning) => {
    console.warn(`- ${warning.path}: ${warning.message}`);
  });
}
```

## Environment-Specific Validation

```typescript
import { EnvironmentValidator } from "./config";

const envValidator = new EnvironmentValidator();
const envResult = envValidator.validateEnvironment(config);

if (!envResult.valid) {
  console.error("Environment validation failed:");
  envResult.errors.forEach((error) => console.error(`- ${error}`));
}

// Get environment-specific recommendations
const recommendations =
  envValidator.getEnvironmentRecommendations("production");
recommendations.forEach((rec) => console.log(`Recommendation: ${rec}`));
```

## Configuration Migration

Automatic migration from legacy configurations:

```typescript
import { ConfigMigration } from "./config";

const migration = new ConfigMigration();

// Auto-detect and migrate
const migratedConfig = migration.autoMigrate(legacyConfig);

// Check if migration is needed
if (migration.isMigrationNeeded(config)) {
  console.log("Configuration migration required");
  const info = migration.getMigrationInfo(config);
  console.log(`Current: ${info.currentVersion}, Target: ${info.targetVersion}`);
}
```

## Security Features

### Configuration Encryption

```typescript
await configManager.initialize({
  encryptionKey: process.env.CONFIG_ENCRYPTION_KEY,
});

// Save encrypted configuration
await configManager.saveToFile("./config/encrypted.json", true);
```

### Access Control

Configuration changes can be tracked and audited:

```typescript
configManager.on("configChanged", (event) => {
  console.log(`Config changed by ${event.userId}: ${event.path}`);
  // Log to audit system
});
```

## Runtime Configuration Updates

### Hot Reloading

```typescript
// Enable file watching for automatic reloads
await configManager.initialize({
  configFilePath: "./config/worker.json",
  watchFileChanges: true,
});

// Manual reload
await configManager.reload();
```

### Safe Updates

```typescript
// Update with validation and rollback on failure
await configManager.set("processing.worker.maxConcurrentTasks", 20, {
  validateBeforeUpdate: true,
  rollbackOnFailure: true,
  backupCurrent: true,
});
```

## Configuration History and Rollback

```typescript
// Get configuration history
const history = configManager.getHistory();
console.log(`${history.length} configurations in history`);

// Rollback to previous configuration
await configManager.rollback(1);

// Rollback multiple steps
await configManager.rollback(3);
```

## Best Practices

### 1. Environment-Specific Configuration

- Use different configuration files for each environment
- Override sensitive values with environment variables
- Validate configuration for each environment

### 2. Security

- Never commit sensitive values to version control
- Use strong encryption keys for encrypted configurations
- Rotate secrets regularly
- Use environment-specific JWT secrets

### 3. Monitoring

- Enable configuration change tracking
- Set up alerts for configuration validation failures
- Monitor configuration drift between environments

### 4. Performance

- Use appropriate cache settings for your workload
- Configure database connection pools based on load
- Adjust worker concurrency based on available resources

### 5. AI Services

- Set appropriate cost budgets and alerts
- Enable fallback mechanisms for reliability
- Monitor AI service usage and costs
- Rotate API keys regularly

## Troubleshooting

### Configuration Validation Errors

```typescript
// Get detailed validation information
const rules = validator.getValidationRules("database.scylla");
console.log("Validation rules for ScyllaDB:", rules);

// Validate partial configuration
const partialResult = validator.validatePartial({
  database: { scylla: { hosts: ["invalid-host"] } },
});
```

### Environment Transition Issues

```typescript
// Validate configuration transition
const transitionResult = envValidator.validateEnvironmentTransition(
  devConfig,
  prodConfig
);

if (!transitionResult.valid) {
  console.error("Deployment blockers:");
  transitionResult.blockers.forEach((blocker) => console.error(`- ${blocker}`));
}
```

### Migration Issues

```typescript
// Get available migrations
const availableMigrations = migration.getAvailableMigrations();
console.log("Available migrations:", availableMigrations);

// Get migration information
const migrationInfo = migration.getMigrationInfo(config);
console.log("Migration info:", migrationInfo);
```

## Integration Examples

### With Ultimate-Express

```typescript
import express from "ultimate-express";
import { configManager } from "./config";

const app = express();

// Use configuration in middleware
app.use((req, res, next) => {
  const rateLimit = configManager.get("security.rateLimit");
  // Apply rate limiting based on configuration
  next();
});

// Configuration endpoint
app.get("/config", (req, res) => {
  const publicConfig = {
    serviceName: configManager.get("serviceName"),
    environment: configManager.get("environment"),
    features: configManager.get("featureFlags"),
  };
  res.json(publicConfig);
});
```

### With Database Connections

```typescript
import { configManager } from "./config";

// Get database configuration
const scyllaConfig = configManager.get("database.scylla");
const mariaConfig = configManager.get("database.maria");

// Create connections using configuration
const scyllaClient = new ScyllaClient(scyllaConfig);
const mariaClient = new MariaClient(mariaConfig);

// React to configuration changes
configManager.on("configChanged", async (event) => {
  if (event.path.startsWith("database.")) {
    // Reconnect to database with new configuration
    await reconnectDatabases();
  }
});
```

### With Worker Processing

```typescript
import { configManager } from "./config";

class WorkerService {
  private maxConcurrentTasks: number;

  constructor() {
    this.maxConcurrentTasks = configManager.get(
      "processing.worker.maxConcurrentTasks"
    );

    // Listen for configuration changes
    configManager.on("configChanged", (event) => {
      if (event.path === "processing.worker.maxConcurrentTasks") {
        this.updateConcurrency(event.newValue);
      }
    });
  }

  private updateConcurrency(newLimit: number) {
    this.maxConcurrentTasks = newLimit;
    // Adjust worker pool size
  }
}
```

This configuration management system provides a robust, secure, and flexible foundation for the worker service, consolidating all configuration patterns from the three source services while adding advanced features for production deployment.
