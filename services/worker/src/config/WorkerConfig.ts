import { config } from '@shared';

/**
 * Worker service configuration extending shared config
 */
export interface WorkerConfigType {
	// Worker-specific settings
	workerConcurrency: number;
	workerTimeout: number;
	maxRetries: number;
	batchSize: number;
	
	// Queue settings
	queueName: string;
	queueConcurrency: number;
	
	// Feature flags
	enableScreenshots: boolean;
	enableAIDescriptions: boolean;
	enablePerformanceAudit: boolean;
}

/**
 * Worker configuration with defaults
 */
export const workerConfig: WorkerConfigType = {
	// Worker settings
	workerConcurrency: parseInt(process.env.WORKER_CONCURRENCY || '5', 10),
	workerTimeout: parseInt(process.env.WORKER_TIMEOUT || '30000', 10),
	maxRetries: parseInt(process.env.WORKER_MAX_RETRIES || '3', 10),
	batchSize: parseInt(process.env.WORKER_BATCH_SIZE || '10', 10),
	
	// Queue settings
	queueName: process.env.WORKER_QUEUE_NAME || 'domain-processing',
	queueConcurrency: parseInt(process.env.QUEUE_CONCURRENCY || '10', 10),
	
	// Feature flags from shared config
	enableScreenshots: config.ENABLE_SCREENSHOTS,
	enableAIDescriptions: config.ENABLE_AI_DESCRIPTIONS,
	enablePerformanceAudit: config.ENABLE_PERFORMANCE_AUDIT,
};

// Re-export shared config for convenience
export { config };
