import type { DomainCandidateType } from '../types/WorkerTypes';
import { domainValidator } from '@shared';

/**
 * Validation pipeline for domain processing stages
 * Simplified version to resolve TypeScript compilation issues
 */

// Validation result interface
interface ValidationResult
{
	isValid: boolean;
	errors: string[];
	warnings: string[];
}

// Normalized domain interface
interface NormalizedDomain
{
	original: string;
	normalized: string;
	etld1: string;
	tld: string;
	isValid: boolean;
	errors?: string[];
}

interface PipelineStageData
{
	'source-fetch': {
		candidates: DomainCandidateType[];
		source: string;
		fetchedAt: Date;
		totalCount: number;
	};
	normalization: {
		normalizedDomains: NormalizedDomain[];
		originalCount: number;
		processedAt: Date;
	};
	'existence-check': {
		results: Map<string, boolean> | Record<string, boolean>;
		checkedAt: Date;
		totalChecked: number;
		foundCount: number;
	};
	enqueue: {
		domains: Array<{
			domain: string;
			metadata: {
				firstSource: string;
				seenAt: Date | string;
				discoveryStrategy?: string;
				confidence?: number;
			};
		}>;
		enqueuedAt: Date;
		totalEnqueued: number;
	};
}

interface SanitizedData<T extends keyof PipelineStageData>
{
	original: PipelineStageData[T];
	sanitized: PipelineStageData[T];
	sanitizationApplied: string[];
}

interface ValidationConfig
{
	maxCandidatesPerBatch: number;
	maxDomainLength: number;
	maxLabelLength: number;
	allowedSources: string[];
	requiredMetadataFields: string[];
	maxErrorsBeforeAbort: number;
}

class ValidationPipeline
{
	private config: ValidationConfig;

	constructor(config: Partial<ValidationConfig> = {})
	{
		this.config = {
			maxCandidatesPerBatch: 100000,
			maxDomainLength: 253,
			maxLabelLength: 63,
			allowedSources: ['tranco', 'radar', 'umbrella', 'czds', 'pir', 'common-crawl', 'sonar'],
			requiredMetadataFields: ['firstSource', 'seenAt'],
			maxErrorsBeforeAbort: 1000,
			...config,
		};
	}

	async validateStage<T extends keyof PipelineStageData>(
		stage: T,
		data: unknown,
	): Promise<ValidationResult & { sanitizedData?: SanitizedData<T> }>
	{
		const validators: Record<keyof PipelineStageData, (data: any) => ValidationResult & { sanitizedData?: any }> = {
			'source-fetch': this.validateSourceFetch.bind(this),
			normalization: this.validateNormalization.bind(this),
			'existence-check': this.validateExistenceCheck.bind(this),
			enqueue: this.validateEnqueue.bind(this),
		};

		const validator = validators[stage];
		if (!validator)
		{
			return {
				isValid: false,
				errors: [`Unknown validation stage: ${stage}`],
				warnings: [],
			};
		}

		return validator(data);
	}

	private validateSourceFetch(data: any): ValidationResult & { sanitizedData?: SanitizedData<'source-fetch'> }
	{
		const errors: string[] = [];
		const warnings: string[] = [];
		const sanitizationApplied: string[] = [];

		// Basic structure validation
		if (!data || typeof data !== 'object')
		{
			return {
				isValid: false,
				errors: ['Source fetch data must be an object'],
				warnings: [],
			};
		}

		// Validate required fields
		if (!Array.isArray(data.candidates))
		{
			errors.push('Source data must contain candidates array');
		}

		if (!data.source || typeof data.source !== 'string')
		{
			errors.push('Source data must contain valid source field');
		}
		else if (!this.config.allowedSources.includes(data.source))
		{
			warnings.push(`Unknown source: ${data.source}. Known sources: ${this.config.allowedSources.join(', ')}`);
		}

		if (!data.fetchedAt)
		{
			warnings.push('Missing fetchedAt timestamp, using current time');
			data.fetchedAt = new Date();
			sanitizationApplied.push('Added missing fetchedAt timestamp');
		}
		else if (!(data.fetchedAt instanceof Date))
		{
			try
			{
				data.fetchedAt = new Date(data.fetchedAt);
				sanitizationApplied.push('Converted fetchedAt to Date object');
			}
			catch
			{
				errors.push('Invalid fetchedAt timestamp format');
			}
		}

		// Validate batch size
		if (data.candidates && data.candidates.length > this.config.maxCandidatesPerBatch)
		{
			warnings.push(`Batch size ${data.candidates.length} exceeds recommended maximum ${this.config.maxCandidatesPerBatch}`);
		}

		// Validate and sanitize candidates
		const sanitizedCandidates: DomainCandidateType[] = [];
		let candidateErrors = 0;

		if (Array.isArray(data.candidates))
		{
			for (let i = 0; i < data.candidates.length; i++)
			{
				const candidate = data.candidates[i];
				const candidateResult = this.validateAndSanitizeCandidate(candidate, i);

				if (candidateResult.isValid && candidateResult.sanitizedCandidate)
				{
					sanitizedCandidates.push(candidateResult.sanitizedCandidate);
					if (candidateResult.sanitizationApplied.length > 0)
					{
						sanitizationApplied.push(...candidateResult.sanitizationApplied);
					}
				}
				else
				{
					errors.push(...candidateResult.errors);
					candidateErrors++;

					// Abort if too many errors
					if (candidateErrors >= this.config.maxErrorsBeforeAbort)
					{
						errors.push(`Aborting validation after ${candidateErrors} candidate errors`);
						break;
					}
				}

				warnings.push(...candidateResult.warnings);
			}
		}

		// Update total count
		const totalCount = data.totalCount || data.candidates?.length || 0;

		const sanitizedData: SanitizedData<'source-fetch'> = {
			original: data,
			sanitized: {
				candidates: sanitizedCandidates,
				source: data.source,
				fetchedAt: data.fetchedAt,
				totalCount,
			},
			sanitizationApplied,
		};

		return {
			isValid: errors.length === 0,
			errors,
			warnings,
			sanitizedData: errors.length === 0 ? sanitizedData : undefined,
		};
	}

	private validateAndSanitizeCandidate(
		candidate: any,
		index: number,
	): {
		isValid: boolean;
		errors: string[];
		warnings: string[];
		sanitizedCandidate?: DomainCandidateType;
		sanitizationApplied: string[];
	}
	{
		const errors: string[] = [];
		const warnings: string[] = [];
		const sanitizationApplied: string[] = [];

		if (!candidate || typeof candidate !== 'object')
		{
			return {
				isValid: false,
				errors: [`Candidate at index ${index} must be an object`],
				warnings: [],
				sanitizationApplied: [],
			};
		}

		// Validate and sanitize domain
		if (!candidate.domain || typeof candidate.domain !== 'string')
		{
			errors.push(`Candidate at index ${index} missing valid domain field`);
		}
		else
		{
			// Sanitize domain
			let sanitizedDomain = candidate.domain.trim().toLowerCase();

			// Remove protocol if present
			if (sanitizedDomain.startsWith('http://') || sanitizedDomain.startsWith('https://'))
			{
				sanitizedDomain = sanitizedDomain.replace(/^https?:\/\//, '');
				sanitizationApplied.push(`Removed protocol from domain at index ${index}`);
			}

			// Remove www prefix
			if (sanitizedDomain.startsWith('www.'))
			{
				sanitizedDomain = sanitizedDomain.replace(/^www\./, '');
				sanitizationApplied.push(`Removed www prefix from domain at index ${index}`);
			}

			// Remove trailing slash and path
			if (sanitizedDomain.includes('/'))
			{
				sanitizedDomain = sanitizedDomain.split('/')[0];
				sanitizationApplied.push(`Removed path from domain at index ${index}`);
			}

			// Validate domain format
			if (!domainValidator.isValidFormat(sanitizedDomain))
			{
				errors.push(`Invalid domain format at index ${index}: ${sanitizedDomain}`);
			}
			else
			{
				candidate.domain = sanitizedDomain;
			}
		}

		// Validate source
		if (!candidate.source || typeof candidate.source !== 'string')
		{
			errors.push(`Candidate at index ${index} missing valid source field`);
		}

		// Validate and sanitize rank
		if (candidate.rank !== undefined)
		{
			if (typeof candidate.rank !== 'number')
			{
				const numericRank = Number(candidate.rank);
				if (Number.isNaN(numericRank) || numericRank < 1)
				{
					warnings.push(`Invalid rank at index ${index}: ${candidate.rank}, removing rank`);
					delete candidate.rank;
					sanitizationApplied.push(`Removed invalid rank at index ${index}`);
				}
				else
				{
					candidate.rank = Math.floor(numericRank);
					sanitizationApplied.push(`Converted rank to integer at index ${index}`);
				}
			}
			else if (candidate.rank < 1)
			{
				warnings.push(`Invalid rank at index ${index}: ${candidate.rank}, removing rank`);
				delete candidate.rank;
				sanitizationApplied.push(`Removed invalid rank at index ${index}`);
			}
		}

		// Sanitize metadata
		if (candidate.metadata && typeof candidate.metadata === 'object')
		{
			// Remove null/undefined values
			const cleanedMetadata: Record<string, any> = {};
			for (const [key, value] of Object.entries(candidate.metadata))
			{
				if (value !== null && value !== undefined)
				{
					cleanedMetadata[key] = value;
				}
				else
				{
					sanitizationApplied.push(`Removed null/undefined metadata key '${key}' at index ${index}`);
				}
			}
			candidate.metadata = cleanedMetadata;
		}

		return {
			isValid: errors.length === 0,
			errors,
			warnings,
			sanitizedCandidate: errors.length === 0 ? candidate as DomainCandidateType : undefined,
			sanitizationApplied,
		};
	}

	private validateNormalization(data: any): ValidationResult & { sanitizedData?: SanitizedData<'normalization'> }
	{
		const errors: string[] = [];
		const warnings: string[] = [];
		const sanitizationApplied: string[] = [];

		if (!data || typeof data !== 'object')
		{
			return {
				isValid: false,
				errors: ['Normalization data must be an object'],
				warnings: [],
			};
		}

		if (!Array.isArray(data.normalizedDomains))
		{
			errors.push('Data must contain normalizedDomains array');
		}

		if (typeof data.originalCount !== 'number')
		{
			if (data.normalizedDomains)
			{
				data.originalCount = data.normalizedDomains.length;
				sanitizationApplied.push('Added missing originalCount from array length');
			}
			else
			{
				warnings.push('Missing originalCount field');
			}
		}

		if (!data.processedAt)
		{
			data.processedAt = new Date();
			sanitizationApplied.push('Added missing processedAt timestamp');
		}
		else if (!(data.processedAt instanceof Date))
		{
			try
			{
				data.processedAt = new Date(data.processedAt);
				sanitizationApplied.push('Converted processedAt to Date object');
			}
			catch
			{
				errors.push('Invalid processedAt timestamp format');
			}
		}

		// Validate normalized domains
		const sanitizedDomains: NormalizedDomain[] = [];
		if (Array.isArray(data.normalizedDomains))
		{
			for (let i = 0; i < data.normalizedDomains.length; i++)
			{
				const domain = data.normalizedDomains[i];
				const domainResult = this.validateNormalizedDomain(domain, i);

				if (domainResult.isValid && domainResult.sanitizedDomain)
				{
					sanitizedDomains.push(domainResult.sanitizedDomain);
				}
				else
				{
					errors.push(...domainResult.errors);
				}

				warnings.push(...domainResult.warnings);
				sanitizationApplied.push(...domainResult.sanitizationApplied);
			}
		}

		const sanitizedData: SanitizedData<'normalization'> = {
			original: data,
			sanitized: {
				normalizedDomains: sanitizedDomains,
				originalCount: data.originalCount,
				processedAt: data.processedAt,
			},
			sanitizationApplied,
		};

		return {
			isValid: errors.length === 0,
			errors,
			warnings,
			sanitizedData: errors.length === 0 ? sanitizedData : undefined,
		};
	}

	private validateNormalizedDomain(
		domain: any,
		index: number,
	): {
		isValid: boolean;
		errors: string[];
		warnings: string[];
		sanitizedDomain?: NormalizedDomain;
		sanitizationApplied: string[];
	}
	{
		const errors: string[] = [];
		const warnings: string[] = [];
		const sanitizationApplied: string[] = [];

		if (!domain || typeof domain !== 'object')
		{
			return {
				isValid: false,
				errors: [`Normalized domain at index ${index} must be an object`],
				warnings: [],
				sanitizationApplied: [],
			};
		}

		// Validate required fields
		if (!domain.original || typeof domain.original !== 'string')
		{
			errors.push(`Domain at index ${index} missing valid original field`);
		}

		if (!domain.normalized || typeof domain.normalized !== 'string')
		{
			errors.push(`Domain at index ${index} missing valid normalized field`);
		}

		if (!domain.etld1 || typeof domain.etld1 !== 'string')
		{
			errors.push(`Domain at index ${index} missing valid eTLD+1 field`);
		}
		else if (!domainValidator.isValidFormat(domain.etld1))
		{
			errors.push(`Invalid eTLD+1 format at index ${index}: ${domain.etld1}`);
		}

		if (!domain.tld || typeof domain.tld !== 'string')
		{
			errors.push(`Domain at index ${index} missing valid TLD field`);
		}

		// Validate isValid field
		if (typeof domain.isValid !== 'boolean')
		{
			if (errors.length === 0)
			{
				domain.isValid = true;
				sanitizationApplied.push(`Set isValid to true for domain at index ${index}`);
			}
			else
			{
				domain.isValid = false;
				sanitizationApplied.push(`Set isValid to false for domain at index ${index}`);
			}
		}

		// Sanitize errors array
		if (domain.errors && !Array.isArray(domain.errors))
		{
			domain.errors = [];
			sanitizationApplied.push(`Converted errors to array for domain at index ${index}`);
		}

		return {
			isValid: errors.length === 0,
			errors,
			warnings,
			sanitizedDomain: errors.length === 0 ? domain as NormalizedDomain : undefined,
			sanitizationApplied,
		};
	}

	private validateExistenceCheck(data: any): ValidationResult & { sanitizedData?: SanitizedData<'existence-check'> }
	{
		const errors: string[] = [];
		const warnings: string[] = [];
		const sanitizationApplied: string[] = [];

		if (!data || typeof data !== 'object')
		{
			return {
				isValid: false,
				errors: ['Existence check data must be an object'],
				warnings: [],
			};
		}

		if (!data.results)
		{
			errors.push('Data must contain results field');
		}

		// Validate timestamps
		if (!data.checkedAt)
		{
			data.checkedAt = new Date();
			sanitizationApplied.push('Added missing checkedAt timestamp');
		}
		else if (!(data.checkedAt instanceof Date))
		{
			try
			{
				data.checkedAt = new Date(data.checkedAt);
				sanitizationApplied.push('Converted checkedAt to Date object');
			}
			catch
			{
				errors.push('Invalid checkedAt timestamp format');
			}
		}

		// Validate and sanitize results
		let sanitizedResults: Map<string, boolean>;
		let totalChecked = 0;
		let foundCount = 0;

		if (data.results instanceof Map)
		{
			sanitizedResults = new Map();
			for (const [domain, exists] of data.results.entries())
			{
				if (typeof domain !== 'string')
				{
					errors.push(`Domain key must be string, got: ${typeof domain}`);
					continue;
				}

				if (typeof exists !== 'boolean')
				{
					// Try to convert to boolean
					if (exists === 'true' || exists === 1)
					{
						sanitizedResults.set(domain, true);
						sanitizationApplied.push(`Converted existence value for ${domain} to boolean`);
						foundCount++;
					}
					else if (exists === 'false' || exists === 0)
					{
						sanitizedResults.set(domain, false);
						sanitizationApplied.push(`Converted existence value for ${domain} to boolean`);
					}
					else
					{
						errors.push(`Existence value for ${domain} must be boolean, got: ${typeof exists}`);
						continue;
					}
				}
				else
				{
					sanitizedResults.set(domain, exists);
					if (exists)
					{
						foundCount++;
					}
				}

				totalChecked++;
			}
		}
		else if (typeof data.results === 'object')
		{
			sanitizedResults = new Map();
			for (const [domain, exists] of Object.entries(data.results))
			{
				if (typeof exists !== 'boolean')
				{
					// Try to convert to boolean
					if (exists === 'true' || exists === 1)
					{
						sanitizedResults.set(domain, true);
						sanitizationApplied.push(`Converted existence value for ${domain} to boolean`);
						foundCount++;
					}
					else if (exists === 'false' || exists === 0)
					{
						sanitizedResults.set(domain, false);
						sanitizationApplied.push(`Converted existence value for ${domain} to boolean`);
					}
					else
					{
						errors.push(`Existence value for ${domain} must be boolean, got: ${typeof exists}`);
						continue;
					}
				}
				else
				{
					sanitizedResults.set(domain, exists as boolean);
					if (exists)
					{
						foundCount++;
					}
				}

				totalChecked++;
			}
		}
		else
		{
			errors.push('Results must be a Map or object');
			sanitizedResults = new Map();
		}

		// Validate counts
		if (typeof data.totalChecked !== 'number')
		{
			data.totalChecked = totalChecked;
			sanitizationApplied.push('Added missing totalChecked count');
		}
		else if (data.totalChecked !== totalChecked)
		{
			warnings.push(`totalChecked mismatch: expected ${totalChecked}, got ${data.totalChecked}`);
		}

		if (typeof data.foundCount !== 'number')
		{
			data.foundCount = foundCount;
			sanitizationApplied.push('Added missing foundCount');
		}
		else if (data.foundCount !== foundCount)
		{
			warnings.push(`foundCount mismatch: expected ${foundCount}, got ${data.foundCount}`);
		}

		const sanitizedData: SanitizedData<'existence-check'> = {
			original: data,
			sanitized: {
				results: sanitizedResults,
				checkedAt: data.checkedAt,
				totalChecked,
				foundCount,
			},
			sanitizationApplied,
		};

		return {
			isValid: errors.length === 0,
			errors,
			warnings,
			sanitizedData: errors.length === 0 ? sanitizedData : undefined,
		};
	}

	private validateEnqueue(data: any): ValidationResult & { sanitizedData?: SanitizedData<'enqueue'> }
	{
		const errors: string[] = [];
		const warnings: string[] = [];
		const sanitizationApplied: string[] = [];

		if (!data || typeof data !== 'object')
		{
			return {
				isValid: false,
				errors: ['Enqueue data must be an object'],
				warnings: [],
			};
		}

		if (!Array.isArray(data.domains))
		{
			errors.push('Data must contain domains array');
		}

		// Validate timestamps
		if (!data.enqueuedAt)
		{
			data.enqueuedAt = new Date();
			sanitizationApplied.push('Added missing enqueuedAt timestamp');
		}
		else if (!(data.enqueuedAt instanceof Date))
		{
			try
			{
				data.enqueuedAt = new Date(data.enqueuedAt);
				sanitizationApplied.push('Converted enqueuedAt to Date object');
			}
			catch
			{
				errors.push('Invalid enqueuedAt timestamp format');
			}
		}

		// Validate and sanitize domains
		const sanitizedDomains: Array<{
			domain: string;
			metadata: {
				firstSource: string;
				seenAt: Date | string;
				discoveryStrategy?: string;
				confidence?: number;
			};
		}> = [];

		if (Array.isArray(data.domains))
		{
			for (let i = 0; i < data.domains.length; i++)
			{
				const domainData = data.domains[i];
				const domainResult = this.validateEnqueueDomain(domainData, i);

				if (domainResult.isValid && domainResult.sanitizedDomain)
				{
					sanitizedDomains.push(domainResult.sanitizedDomain);
				}
				else
				{
					errors.push(...domainResult.errors);
				}

				warnings.push(...domainResult.warnings);
				sanitizationApplied.push(...domainResult.sanitizationApplied);
			}
		}

		// Validate total count
		const totalEnqueued = sanitizedDomains.length;
		if (typeof data.totalEnqueued !== 'number')
		{
			data.totalEnqueued = totalEnqueued;
			sanitizationApplied.push('Added missing totalEnqueued count');
		}
		else if (data.totalEnqueued !== totalEnqueued)
		{
			warnings.push(`totalEnqueued mismatch: expected ${totalEnqueued}, got ${data.totalEnqueued}`);
		}

		const sanitizedData: SanitizedData<'enqueue'> = {
			original: data,
			sanitized: {
				domains: sanitizedDomains,
				enqueuedAt: data.enqueuedAt,
				totalEnqueued,
			},
			sanitizationApplied,
		};

		return {
			isValid: errors.length === 0,
			errors,
			warnings,
			sanitizedData: errors.length === 0 ? sanitizedData : undefined,
		};
	}

	private validateEnqueueDomain(
		domainData: unknown,
		index: number,
	): {
		isValid: boolean;
		errors: string[];
		warnings: string[];
		sanitizedDomain?: {
			domain: string;
			metadata: {
				firstSource: string;
				seenAt: Date | string;
				discoveryStrategy?: string;
				confidence?: number;
			};
		};
		sanitizationApplied: string[];
	}
	{
		const errors: string[] = [];
		const warnings: string[] = [];
		const sanitizationApplied: string[] = [];

		const obj: Record<string, any> | null = null;
		const metadata: Record<string, any> | null = null;

		if (!domainData || typeof domainData !== 'object')
		{
			return {
				isValid: false,
				errors: [`Domain data at index ${index} must be an object`],
				warnings: [],
				sanitizationApplied: [],
			};
		}

		// Validate domain field
		if (!domainData.domain || typeof domainData.domain !== 'string')
		{
			errors.push(`Domain at index ${index} missing valid domain field`);
		}
		else if (!domainValidator.isValidFormat(domainData.domain))
		{
			errors.push(`Invalid domain format at index ${index}: ${domainData.domain}`);
		}

		// Validate metadata
		if (!domainData.metadata || typeof domainData.metadata !== 'object')
		{
			errors.push(`Domain at index ${index} missing metadata object`);
		}
		else
		{
			const metadata = domainData.metadata;

			// Validate required metadata fields
			for (const field of this.config.requiredMetadataFields)
			{
				if (!metadata[field])
				{
					errors.push(`Domain at index ${index} missing required metadata field: ${field}`);
				}
			}

			// Validate firstSource
			if (metadata.firstSource && !this.config.allowedSources.includes(metadata.firstSource))
			{
				warnings.push(`Unknown source in metadata at index ${index}: ${metadata.firstSource}`);
			}

			// Validate and sanitize seenAt
			if (metadata.seenAt)
			{
				if (!(metadata.seenAt instanceof Date) && typeof metadata.seenAt !== 'string')
				{
					errors.push(`Domain at index ${index} seenAt must be Date or string`);
				}
				else if (typeof metadata.seenAt === 'string')
				{
					try
					{
						metadata.seenAt = new Date(metadata.seenAt);
						sanitizationApplied.push(`Converted seenAt to Date for domain at index ${index}`);
					}
					catch
					{
						errors.push(`Invalid seenAt format at index ${index}: ${metadata.seenAt}`);
					}
				}
			}

			// Validate confidence if present
			if (metadata.confidence !== undefined)
			{
				if (typeof metadata.confidence !== 'number')
				{
					const numericConfidence = Number(metadata.confidence);
					if (Number.isNaN(numericConfidence))
					{
						warnings.push(`Invalid confidence at index ${index}, removing`);
						delete metadata.confidence;
						sanitizationApplied.push(`Removed invalid confidence at index ${index}`);
					}
					else
					{
						metadata.confidence = numericConfidence;
						sanitizationApplied.push(`Converted confidence to number at index ${index}`);
					}
				}

				if (typeof metadata.confidence === 'number')
				{
					if (metadata.confidence < 0 || metadata.confidence > 1)
					{
						metadata.confidence = Math.max(0, Math.min(1, metadata.confidence));
						sanitizationApplied.push(`Clamped confidence to [0,1] range at index ${index}`);
					}
				}
			}
		}

		return {
			isValid: errors.length === 0,
			errors,
			warnings,
			sanitizedDomain: errors.length === 0 ? domainData : undefined,
			sanitizationApplied,
		};
	}

	// Removed isValidDomainFormat - now using shared domainValidator.isValidFormat()
}

export type { PipelineStageData, SanitizedData, ValidationConfig, ValidationResult, NormalizedDomain };
export { ValidationPipeline };
