import { logger } from '@shared';
import type { ValidationResult } from './ValidationPipeline';

/**
 * Error recovery manager for validation failures
 * Extracted from domain-seeder service validation/ErrorRecovery.ts
 */

type RecoveryConfigType =
{
	enableAutoRecovery: boolean;
	maxRecoveryAttempts: number;
	recoveryStrategies: string[];
	quarantineFailedItems: boolean;
	logRecoveryActions: boolean;
};

type RecoveryResultType =
{
	success: boolean;
	appliedStrategies: string[];
	recoveredData?: any;
	remainingErrors: string[];
	warnings: string[];
	quarantinedItems: any[];
};

type RecoveryStrategyType =
{
	name: string;
	description: string;
	canRecover: (stage: string, data: any, errors: string[]) => boolean;
	recover: (stage: string, data: any, errors: string[]) => Promise<RecoveryResultType>;
};

class ErrorRecoveryManager
{
	private config: RecoveryConfigType;

	private logger = logger.getLogger('ErrorRecoveryManager');

	private strategies: Map<string, RecoveryStrategyType>;

	constructor(config: Partial<RecoveryConfigType> = {})
	{
		this.config = {
			enableAutoRecovery: true,
			maxRecoveryAttempts: 3,
			recoveryStrategies: ['sanitization', 'filtering', 'defaults', 'quarantine'],
			quarantineFailedItems: true,
			logRecoveryActions: true,
			...config,
		};

		this.strategies = new Map();
		this.initializeRecoveryStrategies();
	}

	async attemptRecovery(
		stage: string,
		data: any,
		validationResult: ValidationResult,
	): Promise<RecoveryResultType>
	{
		if (!this.config.enableAutoRecovery || validationResult.isValid)
		{
			return {
				success: validationResult.isValid,
				appliedStrategies: [],
				recoveredData: data,
				remainingErrors: validationResult.errors,
				warnings: validationResult.warnings,
				quarantinedItems: [],
			};
		}

		const result: RecoveryResultType = {
			success: false,
			appliedStrategies: [],
			recoveredData: data,
			remainingErrors: [...validationResult.errors],
			warnings: [...validationResult.warnings],
			quarantinedItems: [],
		};

		let currentData = data;
		let currentErrors = [...validationResult.errors];
		let attempts = 0;

		while (currentErrors.length > 0 && attempts < this.config.maxRecoveryAttempts)
		{
			attempts++;
			let recoveryApplied = false;

			// Try each recovery strategy
			for (const strategyName of this.config.recoveryStrategies)
			{
				const strategy = this.strategies.get(strategyName);
				if (!strategy)
				{
					continue;
				}

				if (strategy.canRecover(stage, currentData, currentErrors))
				{
					try
					{
						const strategyResult = await strategy.recover(stage, currentData, currentErrors);

						if (strategyResult.success)
						{
							result.appliedStrategies.push(strategyName);
							currentData = strategyResult.recoveredData || currentData;
							currentErrors = strategyResult.remainingErrors;
							result.warnings.push(...strategyResult.warnings);
							result.quarantinedItems.push(...strategyResult.quarantinedItems);

							recoveryApplied = true;

							if (this.config.logRecoveryActions)
							{
								this.logger.info(`Recovery strategy '${strategyName}' applied successfully`, {
									stage,
									remainingErrors: currentErrors.length,
									attempt: attempts,
								});
							}

							// If no errors remain, we're done
							if (currentErrors.length === 0)
							{
								break;
							}
						}
					}
					catch (error)
					{
						this.logger.warn(`Recovery strategy '${strategyName}' failed`, {
							stage,
							error: error instanceof Error ? error.message : 'Unknown error',
						});
					}
				}
			}

			// If no recovery was applied in this iteration, break to avoid infinite loop
			if (!recoveryApplied)
			{
				break;
			}
		}

		result.success = currentErrors.length === 0;
		result.recoveredData = currentData;
		result.remainingErrors = currentErrors;

		if (this.config.logRecoveryActions)
		{
			this.logger.info('Recovery attempt completed', {
				stage,
				success: result.success,
				appliedStrategies: result.appliedStrategies,
				remainingErrors: result.remainingErrors.length,
				attempts,
			});
		}

		return result;
	}

	addRecoveryStrategy(strategy: RecoveryStrategyType): void
	{
		this.strategies.set(strategy.name, strategy);
		this.logger.debug(`Added recovery strategy: ${strategy.name}`);
	}

	removeRecoveryStrategy(name: string): void
	{
		this.strategies.delete(name);
		this.logger.debug(`Removed recovery strategy: ${name}`);
	}

	getAvailableStrategies(): string[]
	{
		return Array.from(this.strategies.keys());
	}

	updateConfig(config: Partial<RecoveryConfigType>): void
	{
		this.config = { ...this.config, ...config };
	}

	private initializeRecoveryStrategies(): void
	{
		// Sanitization strategy - clean and fix data format issues
		this.addRecoveryStrategy({
			name: 'sanitization',
			description: 'Clean and sanitize data to fix format issues',
			canRecover: (stage, data, errors) =>
				errors.some(error =>
					error.includes('format') ||
					error.includes('invalid') ||
					error.includes('missing') ||
					error.includes('timestamp')),
			recover: async (stage, data, errors) =>
			{
				const recoveredData = JSON.parse(JSON.stringify(data)); // Deep clone
				const remainingErrors: string[] = [];
				const warnings: string[] = [];
				const quarantinedItems: any[] = [];

				// Apply sanitization based on stage
				switch (stage)
				{
					case 'source-fetch':
						return this.sanitizeSourceFetchData(recoveredData, errors);

					case 'normalization':
						return this.sanitizeNormalizationData(recoveredData, errors);

					case 'existence-check':
						return this.sanitizeExistenceCheckData(recoveredData, errors);

					case 'enqueue':
						return this.sanitizeEnqueueData(recoveredData, errors);

					default:
						return {
							success: false,
							appliedStrategies: [],
							recoveredData: data,
							remainingErrors: errors,
							warnings: [],
							quarantinedItems: [],
						};
				}
			},
		});

		// Filtering strategy - remove invalid items
		this.addRecoveryStrategy({
			name: 'filtering',
			description: 'Filter out invalid items to allow processing of valid ones',
			canRecover: (stage, data, errors) =>
				errors.some(error =>
					error.includes('at index') ||
					error.includes('invalid') ||
					error.includes('format')),
			recover: async (stage, data, errors) =>
				this.filterInvalidItems(stage, data, errors),
		});

		// Defaults strategy - apply default values for missing fields
		this.addRecoveryStrategy({
			name: 'defaults',
			description: 'Apply default values for missing required fields',
			canRecover: (stage, data, errors) =>
				errors.some(error =>
					error.includes('missing') ||
					error.includes('required')),
			recover: async (stage, data, errors) =>
				this.applyDefaultValues(stage, data, errors),
		});

		// Quarantine strategy - isolate problematic items
		this.addRecoveryStrategy({
			name: 'quarantine',
			description: 'Quarantine problematic items for later review',
			canRecover: () => true, // Can always quarantine as last resort
			recover: async (stage, data, errors) =>
				this.quarantineProblematicItems(stage, data, errors),
		});
	}

	private async sanitizeSourceFetchData(data: any, errors: string[]): Promise<RecoveryResultType>
	{
		const remainingErrors: string[] = [];
		const warnings: string[] = [];

		// Fix timestamp issues
		if (errors.some(e => e.includes('fetchedAt')))
		{
			if (!data.fetchedAt || !(data.fetchedAt instanceof Date))
			{
				data.fetchedAt = new Date();
				warnings.push('Applied current timestamp for missing fetchedAt');
			}
		}
		else
		{
			remainingErrors.push(...errors.filter(e => e.includes('fetchedAt')));
		}

		// Fix source field
		if (errors.some(e => e.includes('source field')))
		{
			if (!data.source)
			{
				data.source = 'unknown';
				warnings.push('Applied default source value');
			}
		}
		else
		{
			remainingErrors.push(...errors.filter(e => e.includes('source field')));
		}

		// Fix candidates array
		if (errors.some(e => e.includes('candidates array')))
		{
			if (!Array.isArray(data.candidates))
			{
				data.candidates = [];
				warnings.push('Initialized empty candidates array');
			}
		}
		else
		{
			remainingErrors.push(...errors.filter(e => e.includes('candidates array')));
		}

		// Add other errors that couldn't be sanitized
		remainingErrors.push(...errors.filter(e =>
			!e.includes('fetchedAt') &&
			!e.includes('source field') &&
			!e.includes('candidates array')));

		return {
			success: remainingErrors.length < errors.length,
			appliedStrategies: ['sanitization'],
			recoveredData: data,
			remainingErrors,
			warnings,
			quarantinedItems: [],
		};
	}

	private async sanitizeNormalizationData(data: any, errors: string[]): Promise<RecoveryResultType>
	{
		const remainingErrors: string[] = [];
		const warnings: string[] = [];

		// Fix processedAt timestamp
		if (errors.some(e => e.includes('processedAt')))
		{
			data.processedAt = new Date();
			warnings.push('Applied current timestamp for processedAt');
		}
		else
		{
			remainingErrors.push(...errors.filter(e => e.includes('processedAt')));
		}

		// Fix originalCount
		if (errors.some(e => e.includes('originalCount')))
		{
			data.originalCount = data.normalizedDomains?.length || 0;
			warnings.push('Calculated originalCount from array length');
		}
		else
		{
			remainingErrors.push(...errors.filter(e => e.includes('originalCount')));
		}

		// Fix normalizedDomains array
		if (errors.some(e => e.includes('normalizedDomains array')))
		{
			if (!Array.isArray(data.normalizedDomains))
			{
				data.normalizedDomains = [];
				warnings.push('Initialized empty normalizedDomains array');
			}
		}
		else
		{
			remainingErrors.push(...errors.filter(e => e.includes('normalizedDomains array')));
		}

		// Add other errors
		remainingErrors.push(...errors.filter(e =>
			!e.includes('processedAt') &&
			!e.includes('originalCount') &&
			!e.includes('normalizedDomains array')));

		return {
			success: remainingErrors.length < errors.length,
			appliedStrategies: ['sanitization'],
			recoveredData: data,
			remainingErrors,
			warnings,
			quarantinedItems: [],
		};
	}

	private async sanitizeExistenceCheckData(data: any, errors: string[]): Promise<RecoveryResultType>
	{
		const remainingErrors: string[] = [];
		const warnings: string[] = [];

		// Fix checkedAt timestamp
		if (errors.some(e => e.includes('checkedAt')))
		{
			data.checkedAt = new Date();
			warnings.push('Applied current timestamp for checkedAt');
		}
		else
		{
			remainingErrors.push(...errors.filter(e => e.includes('checkedAt')));
		}

		// Fix results field
		if (errors.some(e => e.includes('results field')))
		{
			if (!data.results)
			{
				data.results = new Map();
				warnings.push('Initialized empty results Map');
			}
		}
		else
		{
			remainingErrors.push(...errors.filter(e => e.includes('results field')));
		}

		// Add other errors
		remainingErrors.push(...errors.filter(e =>
			!e.includes('checkedAt') &&
			!e.includes('results field')));

		return {
			success: remainingErrors.length < errors.length,
			appliedStrategies: ['sanitization'],
			recoveredData: data,
			remainingErrors,
			warnings,
			quarantinedItems: [],
		};
	}

	private async sanitizeEnqueueData(data: any, errors: string[]): Promise<RecoveryResultType>
	{
		const remainingErrors: string[] = [];
		const warnings: string[] = [];

		// Fix enqueuedAt timestamp
		if (errors.some(e => e.includes('enqueuedAt')))
		{
			data.enqueuedAt = new Date();
			warnings.push('Applied current timestamp for enqueuedAt');
		}
		else
		{
			remainingErrors.push(...errors.filter(e => e.includes('enqueuedAt')));
		}

		// Fix domains array
		if (errors.some(e => e.includes('domains array')))
		{
			if (!Array.isArray(data.domains))
			{
				data.domains = [];
				warnings.push('Initialized empty domains array');
			}
		}
		else
		{
			remainingErrors.push(...errors.filter(e => e.includes('domains array')));
		}

		// Add other errors
		remainingErrors.push(...errors.filter(e =>
			!e.includes('enqueuedAt') &&
			!e.includes('domains array')));

		return {
			success: remainingErrors.length < errors.length,
			appliedStrategies: ['sanitization'],
			recoveredData: data,
			remainingErrors,
			warnings,
			quarantinedItems: [],
		};
	}

	private async filterInvalidItems(stage: string, data: any, errors: string[]): Promise<RecoveryResultType>
	{
		const warnings: string[] = [];
		const quarantinedItems: any[] = [];
		let filteredCount = 0;

		// Extract indices of invalid items from error messages
		const invalidIndices = new Set<number>();
		errors.forEach((error) =>
		{
			const match = error.match(/at index (\d+)/);
			if (match)
			{
				invalidIndices.add(Number.parseInt(match[1], 10));
			}
		});

		// Filter based on stage
		switch (stage)
		{
			case 'source-fetch':
				if (data.candidates && Array.isArray(data.candidates))
				{
					const originalLength = data.candidates.length;
					const validCandidates = data.candidates.filter((candidate: any, index: number) =>
					{
						if (invalidIndices.has(index))
						{
							quarantinedItems.push({ type: 'candidate', index, data: candidate });
							filteredCount++;
							return false;
						}
						return true;
					});

					data.candidates = validCandidates;
					data.totalCount = validCandidates.length;

					if (filteredCount > 0)
					{
						warnings.push(`Filtered out ${filteredCount} invalid candidates from ${originalLength} total`);
					}
				}
				break;

			case 'normalization':
				if (data.normalizedDomains && Array.isArray(data.normalizedDomains))
				{
					const originalLength = data.normalizedDomains.length;
					const validDomains = data.normalizedDomains.filter((domain: any, index: number) =>
					{
						if (invalidIndices.has(index))
						{
							quarantinedItems.push({ type: 'normalized_domain', index, data: domain });
							filteredCount++;
							return false;
						}
						return true;
					});

					data.normalizedDomains = validDomains;

					if (filteredCount > 0)
					{
						warnings.push(`Filtered out ${filteredCount} invalid domains from ${originalLength} total`);
					}
				}
				break;

			case 'enqueue':
				if (data.domains && Array.isArray(data.domains))
				{
					const originalLength = data.domains.length;
					const validDomains = data.domains.filter((domain: any, index: number) =>
					{
						if (invalidIndices.has(index))
						{
							quarantinedItems.push({ type: 'enqueue_domain', index, data: domain });
							filteredCount++;
							return false;
						}
						return true;
					});

					data.domains = validDomains;
					data.totalEnqueued = validDomains.length;

					if (filteredCount > 0)
					{
						warnings.push(`Filtered out ${filteredCount} invalid domains from ${originalLength} total`);
					}
				}
				break;
		}

		// Remove errors related to filtered items
		const remainingErrors = errors.filter((error) =>
		{
			const match = error.match(/at index (\d+)/);
			return !match || !invalidIndices.has(Number.parseInt(match[1], 10));
		});

		return {
			success: filteredCount > 0,
			appliedStrategies: ['filtering'],
			recoveredData: data,
			remainingErrors,
			warnings,
			quarantinedItems,
		};
	}

	private async applyDefaultValues(stage: string, data: any, errors: string[]): Promise<RecoveryResultType>
	{
		const remainingErrors: string[] = [];
		const warnings: string[] = [];

		// Apply defaults based on stage and error types
		errors.forEach((error) =>
		{
			if (error.includes('missing') || error.includes('required'))
			{
				// Try to apply appropriate defaults
				if (error.includes('source'))
				{
					data.source = data.source || 'unknown';
					warnings.push('Applied default source value');
				}
				else if (error.includes('fetchedAt'))
				{
					data.fetchedAt = data.fetchedAt || new Date();
					warnings.push('Applied current timestamp for fetchedAt');
				}
				else if (error.includes('processedAt'))
				{
					data.processedAt = data.processedAt || new Date();
					warnings.push('Applied current timestamp for processedAt');
				}
				else if (error.includes('checkedAt'))
				{
					data.checkedAt = data.checkedAt || new Date();
					warnings.push('Applied current timestamp for checkedAt');
				}
				else if (error.includes('enqueuedAt'))
				{
					data.enqueuedAt = data.enqueuedAt || new Date();
					warnings.push('Applied current timestamp for enqueuedAt');
				}
				else
				{
					remainingErrors.push(error);
				}
			}
			else
			{
				remainingErrors.push(error);
			}
		});

		return {
			success: remainingErrors.length < errors.length,
			appliedStrategies: ['defaults'],
			recoveredData: data,
			remainingErrors,
			warnings,
			quarantinedItems: [],
		};
	}

	private async quarantineProblematicItems(stage: string, data: any, errors: string[]): Promise<RecoveryResultType>
	{
		const quarantinedItems: any[] = [];
		const warnings: string[] = [];

		// If we reach quarantine, it means other strategies failed
		// Quarantine the entire problematic dataset if configured
		if (this.config.quarantineFailedItems)
		{
			quarantinedItems.push({
				type: 'failed_validation',
				stage,
				data: JSON.parse(JSON.stringify(data)),
				errors: [...errors],
				timestamp: new Date(),
			});

			warnings.push(`Quarantined problematic ${stage} data for manual review`);

			// Return minimal valid data structure
			const minimalData = this.createMinimalValidData(stage);

			return {
				success: true, // Success in the sense that we handled the problem
				appliedStrategies: ['quarantine'],
				recoveredData: minimalData,
				remainingErrors: [], // No errors in minimal data
				warnings,
				quarantinedItems,
			};
		}

		return {
			success: false,
			appliedStrategies: ['quarantine'],
			recoveredData: data,
			remainingErrors: errors,
			warnings,
			quarantinedItems,
		};
	}

	private createMinimalValidData(stage: string): any
	{
		const now = new Date();

		switch (stage)
		{
			case 'source-fetch':
				return {
					candidates: [],
					source: 'recovery',
					fetchedAt: now,
					totalCount: 0,
				};

			case 'normalization':
				return {
					normalizedDomains: [],
					originalCount: 0,
					processedAt: now,
				};

			case 'existence-check':
				return {
					results: new Map(),
					checkedAt: now,
					totalChecked: 0,
					foundCount: 0,
				};

			case 'enqueue':
				return ({
					domains: [],
					enqueuedAt: now,
					totalEnqueued: 0,
				});

			default:
				return ({});
		}
	}
}

export type { RecoveryConfigType, RecoveryResultType, RecoveryStrategyType };

export default ErrorRecoveryManager;
