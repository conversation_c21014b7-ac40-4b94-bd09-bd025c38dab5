import { describe, it, expect, beforeEach, vi } from 'vitest';
import type { ComprehensiveDomainDataType } from '../ComprehensiveDomainSchema';
import DomainDataTracker, {
	DataAvailabilityEnum,
	DataPriorityEnum,
	CollectionPhaseEnum,
} from '../ComprehensiveDomainSchema';

// Mock logger
const mockLogger =
{
	info: vi.fn(),
	debug: vi.fn(),
	warn: vi.fn(),
	error: vi.fn(),
};

describe('DomainDataTracker', () =>
{
	let tracker: DomainDataTracker;

	beforeEach(() =>
	{
		tracker = new DomainDataTracker(mockLogger as any);
		vi.clearAllMocks();
	});

	describe('trackDataAvailability', () =>
	{
		it('should track data availability for empty domain data', () =>
		{
			const domainData = {
				identification: { domain: 'example.com' },
			};

			const report = tracker.trackDataAvailability(domainData);

			expect(report.domain).toBe('example.com');
			expect(report.overallCompleteness).toBeGreaterThan(0);
			expect(report.fieldStatus.size).toBeGreaterThan(0);
		});

		it('should identify missing critical fields', () =>
		{
			const domainData = {};

			const report = tracker.trackDataAvailability(domainData);

			expect(report.missingCriticalFields.length).toBeGreaterThan(0);
			expect(report.recommendations.length).toBeGreaterThan(0);
		});

		it('should calculate phase completeness correctly', () =>
		{
			const domainData = {
				identification: { domain: 'example.com' },
				dns: { a: ['1.2.3.4'] },
			};

			const report = tracker.trackDataAvailability(domainData);

			expect(report.phaseCompleteness[CollectionPhaseEnum.PHASE_1_BASIC]).toBeGreaterThan(0);
		});
	});

	describe('validateDomainData', () =>
	{
		it('should validate valid domain data', () =>
		{
			const domainData = {
				identification: { domain: 'example.com' },
			};

			const result = tracker.validateDomainData(domainData);

			expect(result.valid).toBe(true);
			expect(result.errors.length).toBe(0);
		});

		it('should detect validation errors', () =>
		{
			const domainData = {
				identification: { domain: 'invalid-domain' },
			};

			const result = tracker.validateDomainData(domainData);

			expect(result.valid).toBe(false);
			expect(result.errors.length).toBeGreaterThan(0);
		});
	});

	describe('getPriorityMissingFields', () =>
	{
		it('should categorize missing fields by priority', () =>
		{
			const domainData = {
				identification: { domain: 'example.com' },
			};

			const priorityFields = tracker.getPriorityMissingFields(domainData);

			expect(priorityFields.critical).toBeDefined();
			expect(priorityFields.high).toBeDefined();
			expect(priorityFields.medium).toBeDefined();
			expect(priorityFields.low).toBeDefined();
		});
	});

	describe('createEmptyDomainData', () =>
	{
		it('should create valid empty domain data structure', () =>
		{
			const domain = 'example.com';
			const emptyData = tracker.createEmptyDomainData(domain);

			expect(emptyData.identification.domain).toBe(domain);
			expect(emptyData.identification.normalizedDomain).toBe(domain.toLowerCase());
			expect(emptyData.identification.tld).toBe('com');
			expect(emptyData.identification._metadata.availability).toBe(DataAvailabilityEnum.MISSING);
			expect(emptyData.identification._metadata.priority).toBe(DataPriorityEnum.CRITICAL);
		});

		it('should set appropriate metadata for all fields', () =>
		{
			const emptyData = tracker.createEmptyDomainData('test.org');

			// Check that all major sections have metadata
			expect(emptyData.identification._metadata).toBeDefined();
			expect(emptyData.dns._metadata).toBeDefined();
			expect(emptyData.performance._metadata).toBeDefined();
			expect(emptyData.security._metadata).toBeDefined();
			expect(emptyData.seo._metadata).toBeDefined();
			expect(emptyData.ranking._metadata).toBeDefined();
			expect(emptyData.crawlInfo._metadata).toBeDefined();
		});

		it('should extract root domain correctly', () =>
		{
			const emptyData = tracker.createEmptyDomainData('subdomain.example.com');

			expect(emptyData.identification.rootDomain).toBe('example.com');
		});

		it('should extract TLD correctly', () =>
		{
			const emptyData = tracker.createEmptyDomainData('example.co.uk');

			expect(emptyData.identification.tld).toBe('uk');
		});
	});

	describe('field validation rules', () =>
	{
		it('should validate domain format correctly', () =>
		{
			const validDomains = [
				'example.com',
				'sub.example.org',
				'test-site.co.uk',
			];

			const invalidDomains = [
				'invalid',
				'',
				'example.',
				'.com',
			];

			validDomains.forEach((domain) =>
			{
				const domainData = { identification: { domain } };
				const result = tracker.validateDomainData(domainData);
				expect(result.valid).toBe(true);
			});

			invalidDomains.forEach((domain) =>
			{
				const domainData = { identification: { domain } };
				const result = tracker.validateDomainData(domainData);
				expect(result.valid).toBe(false);
			});
		});

		it('should validate performance metrics ranges', () =>
		{
			const validData = {
				performance: {
					coreWebVitals: {
						largestContentfulPaint: 2500,
					},
				},
			};

			const invalidData = {
				performance: {
					coreWebVitals: {
						largestContentfulPaint: 15000, // Too high
					},
				},
			};

			const validResult = tracker.validateDomainData(validData);
			expect(validResult.valid).toBe(true);

			const invalidResult = tracker.validateDomainData(invalidData);
			expect(invalidResult.valid).toBe(false);
		});

		it('should validate SSL grade format', () =>
		{
			const validGrades = ['A+', 'A', 'B', 'C-', 'F'];
			const invalidGrades = ['G', 'AA', '1', 'A++'];

			validGrades.forEach((grade) =>
			{
				const domainData = { security: { ssl: { grade } } };
				const result = tracker.validateDomainData(domainData);
				expect(result.valid).toBe(true);
			});

			invalidGrades.forEach((grade) =>
			{
				const domainData = { security: { ssl: { grade } } };
				const result = tracker.validateDomainData(domainData);
				expect(result.valid).toBe(false);
			});
		});
	});

	describe('data availability determination', () =>
	{
		it('should detect available data', () =>
		{
			const domainData = {
				identification: { domain: 'example.com' },
				dns: { a: ['1.2.3.4'] },
			};

			const report = tracker.trackDataAvailability(domainData);
			const identificationStatus = report.fieldStatus.get('identification');
			const dnsStatus = report.fieldStatus.get('dns');

			expect(identificationStatus?.availability).toBe(DataAvailabilityEnum.AVAILABLE);
			expect(dnsStatus?.availability).toBe(DataAvailabilityEnum.AVAILABLE);
		});

		it('should detect missing data', () =>
		{
			const domainData = {
				identification: { domain: 'example.com' },
			};

			const report = tracker.trackDataAvailability(domainData);
			const performanceStatus = report.fieldStatus.get('performance');

			expect(performanceStatus?.availability).toBe(DataAvailabilityEnum.MISSING);
		});
	});

	describe('completeness calculations', () =>
	{
		it('should calculate overall completeness correctly', () =>
		{
			const fullData = tracker.createEmptyDomainData('example.com');
			// Simulate some data being available
			fullData.identification._metadata.availability = DataAvailabilityEnum.AVAILABLE;
			fullData.dns._metadata.availability = DataAvailabilityEnum.AVAILABLE;

			const report = tracker.trackDataAvailability(fullData);

			expect(report.overallCompleteness).toBeGreaterThan(0);
			expect(report.overallCompleteness).toBeLessThanOrEqual(100);
		});

		it('should calculate phase completeness correctly', () =>
		{
			const domainData = tracker.createEmptyDomainData('example.com');
			// Mark phase 1 fields as available
			domainData.identification._metadata.availability = DataAvailabilityEnum.AVAILABLE;
			domainData.dns._metadata.availability = DataAvailabilityEnum.AVAILABLE;

			const report = tracker.trackDataAvailability(domainData);

			expect(report.phaseCompleteness[CollectionPhaseEnum.PHASE_1_BASIC]).toBeGreaterThan(0);
		});
	});

	describe('recommendations generation', () =>
	{
		it('should generate recommendations for missing critical fields', () =>
		{
			const domainData = {}; // No data

			const report = tracker.trackDataAvailability(domainData);

			expect(report.recommendations.length).toBeGreaterThan(0);
			expect(report.recommendations.some(r => r.includes('Critical fields missing'))).toBe(true);
		});

		it('should recommend phase 1 completion', () =>
		{
			const domainData = {
				identification: { domain: 'example.com' },
			};

			const report = tracker.trackDataAvailability(domainData);

			expect(report.recommendations.some(r => r.includes('Phase 1'))).toBe(true);
		});

		it('should recommend comprehensive crawl for low completeness', () =>
		{
			const domainData = {
				identification: { domain: 'example.com' },
			};

			const report = tracker.trackDataAvailability(domainData);

			expect(report.recommendations.some(r => r.includes('comprehensive crawl'))).toBe(true);
		});
	});
});
