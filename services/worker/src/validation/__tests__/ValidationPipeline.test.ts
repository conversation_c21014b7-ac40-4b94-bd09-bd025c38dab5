import { describe, it, expect, beforeEach } from 'vitest';
import { ValidationPipeline } from '../ValidationPipeline';

describe('ValidationPipeline', () =>
{
	let pipeline: ValidationPipeline;

	beforeEach(() =>
	{
		pipeline = new ValidationPipeline();
	});

	describe('validateStage', () =>
	{
		it('should return error for unknown stage', async () =>
		{
			const result = await pipeline.validateStage('unknown-stage' as any, {});

			expect(result.isValid).toBe(false);
			expect(result.errors).toContain('Unknown validation stage: unknown-stage');
		});
	});

	describe('source-fetch validation', () =>
	{
		it('should validate valid source-fetch data', async () =>
		{
			const data = {
				candidates: [
					{
						domain: 'example.com',
						source: 'tranco',
						rank: 1,
						metadata: { test: 'value' },
					},
				],
				source: 'tranco',
				fetchedAt: new Date(),
				totalCount: 1,
			};

			const result = await pipeline.validateStage('source-fetch', data);

			expect(result.isValid).toBe(true);
			expect(result.errors.length).toBe(0);
			expect(result.sanitizedData).toBeDefined();
		});

		it('should reject non-object data', async () =>
		{
			const result = await pipeline.validateStage('source-fetch', 'invalid');

			expect(result.isValid).toBe(false);
			expect(result.errors).toContain('Source fetch data must be an object');
		});

		it('should require candidates array', async () =>
		{
			const data = {
				source: 'tranco',
				fetchedAt: new Date(),
			};

			const result = await pipeline.validateStage('source-fetch', data);

			expect(result.isValid).toBe(false);
			expect(result.errors).toContain('Source data must contain candidates array');
		});

		it('should require valid source field', async () =>
		{
			const data = {
				candidates: [],
				fetchedAt: new Date(),
			};

			const result = await pipeline.validateStage('source-fetch', data);

			expect(result.isValid).toBe(false);
			expect(result.errors).toContain('Source data must contain valid source field');
		});

		it('should sanitize missing fetchedAt', async () =>
		{
			const data = {
				candidates: [],
				source: 'tranco',
			};

			const result = await pipeline.validateStage('source-fetch', data);

			expect(result.warnings).toContain('Missing fetchedAt timestamp, using current time');
			expect(result.sanitizedData?.sanitizationApplied).toContain('Added missing fetchedAt timestamp');
		});

		it('should warn about unknown sources', async () =>
		{
			const data = {
				candidates: [],
				source: 'unknown-source',
				fetchedAt: new Date(),
			};

			const result = await pipeline.validateStage('source-fetch', data);

			expect(result.warnings.some(w => w.includes('Unknown source'))).toBe(true);
		});

		it('should warn about large batch sizes', async () =>
		{
			const largeCandidates = Array(200000).fill({
				domain: 'example.com',
				source: 'tranco',
			});

			const data = {
				candidates: largeCandidates,
				source: 'tranco',
				fetchedAt: new Date(),
			};

			const result = await pipeline.validateStage('source-fetch', data);

			expect(result.warnings.some(w => w.includes('exceeds recommended maximum'))).toBe(true);
		});
	});

	describe('candidate validation and sanitization', () =>
	{
		it('should sanitize domain protocols', async () =>
		{
			const data = {
				candidates: [
					{
						domain: 'https://example.com',
						source: 'tranco',
					},
				],
				source: 'tranco',
				fetchedAt: new Date(),
			};

			const result = await pipeline.validateStage('source-fetch', data);

			expect(result.sanitizedData?.sanitizationApplied.some(s => s.includes('Removed protocol'))).toBe(true);
		});

		it('should sanitize www prefix', async () =>
		{
			const data = {
				candidates: [
					{
						domain: 'www.example.com',
						source: 'tranco',
					},
				],
				source: 'tranco',
				fetchedAt: new Date(),
			};

			const result = await pipeline.validateStage('source-fetch', data);

			expect(result.sanitizedData?.sanitizationApplied.some(s => s.includes('Removed www prefix'))).toBe(true);
		});

		it('should sanitize paths from domains', async () =>
		{
			const data = {
				candidates: [
					{
						domain: 'example.com/path',
						source: 'tranco',
					},
				],
				source: 'tranco',
				fetchedAt: new Date(),
			};

			const result = await pipeline.validateStage('source-fetch', data);

			expect(result.sanitizedData?.sanitizationApplied.some(s => s.includes('Removed path'))).toBe(true);
		});

		it('should validate domain format', async () =>
		{
			const data = {
				candidates: [
					{
						domain: 'invalid-domain',
						source: 'tranco',
					},
				],
				source: 'tranco',
				fetchedAt: new Date(),
			};

			const result = await pipeline.validateStage('source-fetch', data);

			expect(result.isValid).toBe(false);
			expect(result.errors.some(e => e.includes('Invalid domain format'))).toBe(true);
		});

		it('should sanitize rank values', async () =>
		{
			const data = {
				candidates: [
					{
						domain: 'example.com',
						source: 'tranco',
						rank: '123.45', // String that can be converted
					},
				],
				source: 'tranco',
				fetchedAt: new Date(),
			};

			const result = await pipeline.validateStage('source-fetch', data);

			expect(result.sanitizedData?.sanitizationApplied.some(s => s.includes('Converted rank to integer'))).toBe(true);
		});

		it('should remove invalid ranks', async () =>
		{
			const data = {
				candidates: [
					{
						domain: 'example.com',
						source: 'tranco',
						rank: 'invalid',
					},
				],
				source: 'tranco',
				fetchedAt: new Date(),
			};

			const result = await pipeline.validateStage('source-fetch', data);

			expect(result.warnings.some(w => w.includes('Invalid rank'))).toBe(true);
			expect(result.sanitizedData?.sanitizationApplied.some(s => s.includes('Removed invalid rank'))).toBe(true);
		});

		it('should clean metadata', async () =>
		{
			const data = {
				candidates: [
					{
						domain: 'example.com',
						source: 'tranco',
						metadata: {
							valid: 'value',
							invalid: null,
							undefined,
						},
					},
				],
				source: 'tranco',
				fetchedAt: new Date(),
			};

			const result = await pipeline.validateStage('source-fetch', data);

			expect(result.sanitizedData?.sanitizationApplied.some(s => s.includes('Removed null/undefined metadata'))).toBe(true);
		});
	});

	describe('normalization validation', () =>
	{
		it('should validate valid normalization data', async () =>
		{
			const data = {
				normalizedDomains: [
					{
						original: 'Example.COM',
						normalized: 'example.com',
						etld1: 'example.com',
						tld: 'com',
						isValid: true,
					},
				],
				originalCount: 1,
				processedAt: new Date(),
			};

			const result = await pipeline.validateStage('normalization', data);

			expect(result.isValid).toBe(true);
			expect(result.errors.length).toBe(0);
		});

		it('should require normalizedDomains array', async () =>
		{
			const data = {
				originalCount: 1,
				processedAt: new Date(),
			};

			const result = await pipeline.validateStage('normalization', data);

			expect(result.isValid).toBe(false);
			expect(result.errors).toContain('Data must contain normalizedDomains array');
		});

		it('should sanitize missing originalCount', async () =>
		{
			const data = {
				normalizedDomains: [
					{
						original: 'example.com',
						normalized: 'example.com',
						etld1: 'example.com',
						tld: 'com',
						isValid: true,
					},
				],
				processedAt: new Date(),
			};

			const result = await pipeline.validateStage('normalization', data);

			expect(result.sanitizedData?.sanitizationApplied).toContain('Added missing originalCount from array length');
		});

		it('should sanitize missing processedAt', async () =>
		{
			const data = {
				normalizedDomains: [],
				originalCount: 0,
			};

			const result = await pipeline.validateStage('normalization', data);

			expect(result.sanitizedData?.sanitizationApplied).toContain('Added missing processedAt timestamp');
		});
	});

	describe('existence-check validation', () =>
	{
		it('should validate valid existence-check data with Map', async () =>
		{
			const results = new Map([
				['example.com', true],
				['nonexistent.com', false],
			]);

			const data = {
				results,
				checkedAt: new Date(),
				totalChecked: 2,
				foundCount: 1,
			};

			const result = await pipeline.validateStage('existence-check', data);

			expect(result.isValid).toBe(true);
			expect(result.errors.length).toBe(0);
		});

		it('should validate valid existence-check data with object', async () =>
		{
			const data = {
				results: {
					'example.com': true,
					'nonexistent.com': false,
				},
				checkedAt: new Date(),
				totalChecked: 2,
				foundCount: 1,
			};

			const result = await pipeline.validateStage('existence-check', data);

			expect(result.isValid).toBe(true);
			expect(result.errors.length).toBe(0);
		});

		it('should require results field', async () =>
		{
			const data = {
				checkedAt: new Date(),
				totalChecked: 0,
				foundCount: 0,
			};

			const result = await pipeline.validateStage('existence-check', data);

			expect(result.isValid).toBe(false);
			expect(result.errors).toContain('Data must contain results field');
		});

		it('should sanitize boolean values', async () =>
		{
			const data = {
				results: {
					'example.com': 'true',
					'another.com': 1,
					'third.com': 'false',
					'fourth.com': 0,
				},
				checkedAt: new Date(),
			};

			const result = await pipeline.validateStage('existence-check', data);

			expect(result.sanitizedData?.sanitizationApplied.some(s => s.includes('Converted existence value'))).toBe(true);
		});

		it('should sanitize missing counts', async () =>
		{
			const data = {
				results: {
					'example.com': true,
				},
				checkedAt: new Date(),
			};

			const result = await pipeline.validateStage('existence-check', data);

			expect(result.sanitizedData?.sanitizationApplied).toContain('Added missing totalChecked count');
			expect(result.sanitizedData?.sanitizationApplied).toContain('Added missing foundCount');
		});
	});

	describe('enqueue validation', () =>
	{
		it('should validate valid enqueue data', async () =>
		{
			const data = {
				domains: [
					{
						domain: 'example.com',
						metadata: {
							firstSource: 'tranco',
							seenAt: new Date(),
							confidence: 0.9,
						},
					},
				],
				enqueuedAt: new Date(),
				totalEnqueued: 1,
			};

			const result = await pipeline.validateStage('enqueue', data);

			expect(result.isValid).toBe(true);
			expect(result.errors.length).toBe(0);
		});

		it('should require domains array', async () =>
		{
			const data = {
				enqueuedAt: new Date(),
				totalEnqueued: 0,
			};

			const result = await pipeline.validateStage('enqueue', data);

			expect(result.isValid).toBe(false);
			expect(result.errors).toContain('Data must contain domains array');
		});

		it('should validate domain metadata', async () =>
		{
			const data = {
				domains: [
					{
						domain: 'example.com',
						metadata: {
							// Missing required fields
						},
					},
				],
				enqueuedAt: new Date(),
			};

			const result = await pipeline.validateStage('enqueue', data);

			expect(result.isValid).toBe(false);
			expect(result.errors.some(e => e.includes('missing required metadata field'))).toBe(true);
		});

		it('should sanitize confidence values', async () =>
		{
			const data = {
				domains: [
					{
						domain: 'example.com',
						metadata: {
							firstSource: 'tranco',
							seenAt: new Date(),
							confidence: '0.8', // String that can be converted
						},
					},
				],
				enqueuedAt: new Date(),
			};

			const result = await pipeline.validateStage('enqueue', data);

			expect(result.sanitizedData?.sanitizationApplied.some(s => s.includes('Converted confidence to number'))).toBe(true);
		});

		it('should clamp confidence values to valid range', async () =>
		{
			const data = {
				domains: [
					{
						domain: 'example.com',
						metadata: {
							firstSource: 'tranco',
							seenAt: new Date(),
							confidence: 1.5, // Out of range
						},
					},
				],
				enqueuedAt: new Date(),
			};

			const result = await pipeline.validateStage('enqueue', data);

			expect(result.sanitizedData?.sanitizationApplied.some(s => s.includes('Clamped confidence to [0,1] range'))).toBe(true);
		});
	});

	describe('domain format validation', () =>
	{
		it('should validate correct domain formats', () =>
		{
			const validDomains = [
				'example.com',
				'sub.example.org',
				'test-site.co.uk',
				'a.b',
				'123.456.789.012',
			];

			validDomains.forEach((domain) =>
			{
				// Access private method through any cast for testing
				const isValid = (pipeline as any).isValidDomainFormat(domain);
				expect(isValid).toBe(true);
			});
		});

		it('should reject invalid domain formats', () =>
		{
			const invalidDomains = [
				'',
				'invalid',
				'example.',
				'.com',
				'ex ample.com',
				'example..com',
				'-example.com',
				'example-.com',
			];

			invalidDomains.forEach((domain) =>
			{
				// Access private method through any cast for testing
				const isValid = (pipeline as any).isValidDomainFormat(domain);
				expect(isValid).toBe(false);
			});
		});

		it('should enforce length constraints', () =>
		{
			// Domain too long
			const longDomain = `${'a'.repeat(254)  }.com`;
			const isValidLong = (pipeline as any).isValidDomainFormat(longDomain);
			expect(isValidLong).toBe(false);

			// Label too long
			const longLabel = `${'a'.repeat(64)  }.com`;
			const isValidLabel = (pipeline as any).isValidDomainFormat(longLabel);
			expect(isValidLabel).toBe(false);
		});
	});

	describe('configuration', () =>
	{
		it('should use custom configuration', () =>
		{
			const customConfig = {
				maxCandidatesPerBatch: 50000,
				maxDomainLength: 100,
				allowedSources: ['custom-source'],
			};

			const customPipeline = new ValidationPipeline(customConfig);

			// Test that custom config is used
			expect((customPipeline as any).config.maxCandidatesPerBatch).toBe(50000);
			expect((customPipeline as any).config.maxDomainLength).toBe(100);
			expect((customPipeline as any).config.allowedSources).toContain('custom-source');
		});
	});
});
