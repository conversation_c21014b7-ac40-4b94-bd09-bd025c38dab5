import { describe, it, expect, beforeEach, vi } from 'vitest';
import type { ComprehensiveDomainDataType } from '../ComprehensiveDomainSchema';
import { DomainDataValidator } from '../DomainDataValidator';
import {
	// DataAvailabilityEnum,
	// DataPriorityEnum,
	CollectionPhaseEnum,
} from '../ComprehensiveDomainSchema';

// Mock logger
const mockLogger =
{
	info: vi.fn(),
	debug: vi.fn(),
	warn: vi.fn(),
	error: vi.fn(),
};

describe('DomainDataValidator', () =>
{
	let validator: DomainDataValidator;

	beforeEach(() =>
	{
		validator = new DomainDataValidator(mockLogger as any);
		vi.clearAllMocks();
	});

	describe('validateDomainData', () =>
	{
		it('should validate complete domain data successfully', async () =>
		{
			const domainData = {
				identification: { domain: 'example.com' },
			};

			const result = await validator.validateDomainData(domainData);

			expect(result.valid).toBe(true);
			expect(result.errors.length).toBe(0);
			expect(mockLogger.info).toHaveBeenCalledWith(
				'Domain data validation completed',
				expect.objectContaining({
					domain: 'example.com',
					valid: true,
				}),
			);
		});

		it('should handle validation errors', async () =>
		{
			const domainData = {
				identification: { domain: 'invalid-domain' },
			};

			const result = await validator.validateDomainData(domainData);

			expect(result.valid).toBe(false);
			expect(result.errors.length).toBeGreaterThan(0);
		});

		it('should handle validation exceptions', async () =>
		{
			const domainData = null;

			await expect(validator.validateDomainData(domainData as any)).rejects.toThrow();
			expect(mockLogger.error).toHaveBeenCalled();
		});
	});

	describe('checkDataCompleteness', () =>
	{
		it('should check data completeness successfully', async () =>
		{
			const domainData = {
				identification: { domain: 'example.com' },
				dns: { a: ['1.2.3.4'] },
			};

			const report = await validator.checkDataCompleteness(domainData);

			expect(report.domain).toBe('example.com');
			expect(report.overallCompleteness).toBeGreaterThan(0);
			expect(report.fieldStatus.size).toBeGreaterThan(0);
			expect(mockLogger.info).toHaveBeenCalledWith(
				'Data completeness check completed',
				expect.objectContaining({
					domain: 'example.com',
				}),
			);
		});

		it('should handle completeness check errors', async () =>
		{
			const domainData = null;

			await expect(validator.checkDataCompleteness(domainData as any)).rejects.toThrow();
			expect(mockLogger.error).toHaveBeenCalled();
		});
	});

	describe('getPriorityMissingFields', () =>
	{
		it('should get priority missing fields successfully', async () =>
		{
			const domainData = {
				identification: { domain: 'example.com' },
			};

			const priorityFields = await validator.getPriorityMissingFields(domainData);

			expect(priorityFields.critical).toBeDefined();
			expect(priorityFields.high).toBeDefined();
			expect(priorityFields.medium).toBeDefined();
			expect(priorityFields.low).toBeDefined();
			expect(mockLogger.debug).toHaveBeenCalledWith(
				'Priority missing fields identified',
				expect.objectContaining({
					domain: 'example.com',
				}),
			);
		});

		it('should handle priority fields errors', async () =>
		{
			const domainData = null;

			await expect(validator.getPriorityMissingFields(domainData as any)).rejects.toThrow();
			expect(mockLogger.error).toHaveBeenCalled();
		});
	});

	describe('createEmptyDomainData', () =>
	{
		it('should create empty domain data successfully', async () =>
		{
			const domain = 'example.com';

			const emptyData = await validator.createEmptyDomainData(domain);

			expect(emptyData.identification.domain).toBe(domain);
			expect(emptyData.identification.normalizedDomain).toBe(domain.toLowerCase());
			expect(mockLogger.info).toHaveBeenCalledWith(
				'Empty domain data structure created',
				{ domain },
			);
		});

		it('should handle empty data creation errors', async () =>
		{
			const domain = '';

			await expect(validator.createEmptyDomainData(domain)).rejects.toThrow();
			expect(mockLogger.error).toHaveBeenCalled();
		});
	});

	describe('assessDataQuality', () =>
	{
		it('should assess data quality successfully', async () =>
		{
			const domainData = {
				identification: { domain: 'example.com' },
				dns: { a: ['1.2.3.4'] },
			};

			const assessment = await validator.assessDataQuality(domainData);

			expect(assessment.domain).toBe('example.com');
			expect(assessment.overallQuality).toBeGreaterThanOrEqual(0);
			expect(assessment.overallQuality).toBeLessThanOrEqual(100);
			expect(assessment.completeness).toBeDefined();
			expect(assessment.validation).toBeDefined();
			expect(assessment.recommendations).toBeDefined();
			expect(assessment.nextActions).toBeDefined();
			expect(mockLogger.info).toHaveBeenCalledWith(
				'Data quality assessment completed',
				expect.objectContaining({
					domain: 'example.com',
				}),
			);
		});

		it('should calculate quality score correctly', async () =>
		{
			const highQualityData = {
				identification: { domain: 'example.com' },
				dns: { a: ['1.2.3.4'] },
				performance: { coreWebVitals: { largestContentfulPaint: 2000 } },
				security: { ssl: { grade: 'A+' } },
			};

			const lowQualityData = {
				identification: { domain: 'invalid-domain' },
			};

			const highQualityAssessment = await validator.assessDataQuality(highQualityData);
			const lowQualityAssessment = await validator.assessDataQuality(lowQualityData);

			expect(highQualityAssessment.overallQuality).toBeGreaterThan(lowQualityAssessment.overallQuality);
		});

		it('should provide appropriate recommendations', async () =>
		{
			const incompleteData = {
				identification: { domain: 'example.com' },
			};

			const assessment = await validator.assessDataQuality(incompleteData);

			expect(assessment.recommendations.length).toBeGreaterThan(0);
			expect(assessment.nextActions.length).toBeGreaterThan(0);
		});
	});

	describe('isReadyForRanking', () =>
	{
		it('should determine ranking readiness correctly', async () =>
		{
			const readyData = {
				identification: { domain: 'example.com' },
				performance: { coreWebVitals: { largestContentfulPaint: 2000 } },
				security: { ssl: { grade: 'A' } },
			};

			const notReadyData = {
				identification: { domain: 'example.com' },
			};

			const readyResult = await validator.isReadyForRanking(readyData);
			const notReadyResult = await validator.isReadyForRanking(notReadyData);

			expect(readyResult.ready).toBe(true);
			expect(readyResult.blockers.length).toBe(0);

			expect(notReadyResult.ready).toBe(false);
			expect(notReadyResult.blockers.length).toBeGreaterThan(0);
		});

		it('should identify specific blockers', async () =>
		{
			const dataWithoutPerformance = {
				identification: { domain: 'example.com' },
				security: { ssl: { grade: 'A' } },
			};

			const result = await validator.isReadyForRanking(dataWithoutPerformance);

			expect(result.ready).toBe(false);
			expect(result.blockers).toContain('Performance data missing');
		});

		it('should provide warnings for incomplete data', async () =>
		{
			const partialData = {
				identification: { domain: 'example.com' },
				performance: { coreWebVitals: { largestContentfulPaint: 2000 } },
				security: { ssl: { grade: 'A' } },
			};

			const result = await validator.isReadyForRanking(partialData);

			expect(result.warnings.length).toBeGreaterThan(0);
		});
	});

	describe('generateCollectionPlan', () =>
	{
		it('should generate collection plan successfully', async () =>
		{
			const domainData = {
				identification: { domain: 'example.com' },
			};

			const plan = await validator.generateCollectionPlan(domainData);

			expect(plan.domain).toBe('example.com');
			expect(plan.phases).toBeDefined();
			expect(plan.estimatedDuration).toBeGreaterThan(0);
			expect(plan.resourceRequirements).toBeDefined();
			expect(plan.priority).toBeDefined();
			expect(mockLogger.info).toHaveBeenCalledWith(
				'Data collection plan generated',
				expect.objectContaining({
					domain: 'example.com',
				}),
			);
		});

		it('should create phases based on missing fields', async () =>
		{
			const dataWithCriticalMissing = {
				identification: { domain: 'example.com' },
			};

			const plan = await validator.generateCollectionPlan(dataWithCriticalMissing);

			expect(plan.phases.length).toBeGreaterThan(0);
			expect(plan.phases[0].phase).toBe(CollectionPhaseEnum.PHASE_1_BASIC);
			expect(plan.phases[0].modules).toContain('dns');
			expect(plan.phases[0].modules).toContain('ssl');
		});

		it('should adjust resource requirements based on phases', async () =>
		{
			const basicData = {
				identification: { domain: 'example.com' },
			};

			const plan = await validator.generateCollectionPlan(basicData);

			// Should start with low requirements
			expect(['low', 'medium', 'high']).toContain(plan.resourceRequirements.cpu);
			expect(['low', 'medium', 'high']).toContain(plan.resourceRequirements.memory);
		});

		it('should set appropriate priority', async () =>
		{
			const criticalMissingData = {};
			const normalData = {
				identification: { domain: 'example.com' },
				dns: { a: ['1.2.3.4'] },
			};

			const criticalPlan = await validator.generateCollectionPlan(criticalMissingData);
			const normalPlan = await validator.generateCollectionPlan(normalData);

			expect(criticalPlan.priority).toBe('critical');
			expect(['high', 'medium', 'low']).toContain(normalPlan.priority);
		});
	});

	describe('error handling', () =>
	{
		it('should handle validation errors gracefully', async () =>
		{
			const invalidData = { invalid: 'data' };

			await expect(validator.validateDomainData(invalidData)).rejects.toThrow();
			expect(mockLogger.error).toHaveBeenCalledWith(
				'Domain data validation failed',
				expect.objectContaining({
					error: expect.any(String),
				}),
			);
		});

		it('should handle completeness check errors gracefully', async () =>
		{
			const invalidData = { invalid: 'data' };

			await expect(validator.checkDataCompleteness(invalidData)).rejects.toThrow();
			expect(mockLogger.error).toHaveBeenCalledWith(
				'Data completeness check failed',
				expect.objectContaining({
					error: expect.any(String),
				}),
			);
		});

		it('should handle ranking readiness check errors gracefully', async () =>
		{
			const invalidData = { invalid: 'data' };

			await expect(validator.isReadyForRanking(invalidData)).rejects.toThrow();
			expect(mockLogger.error).toHaveBeenCalledWith(
				'Ranking readiness check failed',
				expect.objectContaining({
					error: expect.any(String),
				}),
			);
		});
	});
});
