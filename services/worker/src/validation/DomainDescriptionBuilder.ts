import type { DomainDescriptionInterface } from '@shared';

/**
 * Domain description builder for transforming crawl results into domain descriptions
 * Extracted from crawler service utils/DomainDescriptionBuilder.ts
 */

/**
 * Build domain description from crawl analysis result
 */
function buildDomainDescription(result: any): DomainDescriptionInterface
{
	const now = new Date().toISOString();

	return {
		metadata: {
			domain: result.domain,
			tld: result.domain.split('.').pop() || '',
			status: 'active',
			category: { primary: 'uncategorized' },
			tags: result.tags || ['website', 'domain', 'uncategorized', 'analysis', 'web'],
		},
		overview: {
			summary: result.summary || 'This domain provides web services and content to users.',
		},
		technical: {
			technologies: [],
			security: { sslGrade: result.data.sslAnalysis?.grade },
			dns: {
				a: result.data.dnsAnalysis?.records.A || [],
				aaaa: result.data.dnsAnalysis?.records.AAAA || [],
				mx: (result.data.dnsAnalysis?.records.MX || []).map((m: any) => `${m.priority} ${m.exchange}`),
				cname: result.data.dnsAnalysis?.records.CNAME || [],
				txt: result.data.dnsAnalysis?.records.TXT || [],
				ns: result.data.dnsAnalysis?.records.NS || [],
			},
		},
		seo: {
			title: result.data.homepageAnalysis?.metaTags.title,
			metaDescription: result.data.homepageAnalysis?.metaTags.description,
			sitemap: { present: (result.data.robotsAnalysis?.sitemaps?.length || 0) > 0 },
			robots: { present: !!result.data.robotsAnalysis, policy: 'mixed' },
		},
		reputation: {},
		ranking: {},
		compliance: {},
		crawl: {
			lastCrawled: now,
			crawlType: result.crawlType as any,
			errors: result.status === 'failed' ? 1 : 0,
		},
	};
}

/**
 * Build enhanced domain description with comprehensive data transformation
 */
function buildEnhancedDomainDescription(analysisResult: any): DomainDescriptionInterface
{
	const now = new Date().toISOString();
	const domain = analysisResult.domain;

	// Extract data from various analysis modules
	const dnsData = analysisResult.data?.dnsAnalysis || {};
	const sslData = analysisResult.data?.sslAnalysis || {};
	const homepageData = analysisResult.data?.homepageAnalysis || {};
	const robotsData = analysisResult.data?.robotsAnalysis || {};
	const performanceData = analysisResult.data?.performanceAnalysis || {};
	const securityData = analysisResult.data?.securityAnalysis || {};
	const seoData = analysisResult.data?.seoAnalysis || {};
	const technologyData = analysisResult.data?.technologyAnalysis || {};
	const contentData = analysisResult.data?.contentAnalysis || {};

	return {
		metadata: {
			domain,
			tld: extractTLD(domain),
			status: determineStatus(analysisResult),
			category: {
				primary: contentData?.category?.primary || 'uncategorized',
				secondary: contentData?.category?.secondary,
				confidence: contentData?.category?.confidence || 0,
			},
			lastUpdated: now,
			version: '2.0',
		},

		overview: {
			title: homepageData?.metaTags?.title || '',
			description: homepageData?.metaTags?.description || contentData?.aiGenerated?.description?.short || '',
			language: contentData?.languageDetection?.primary || 'en',
			country: extractCountryFromData(dnsData, sslData),
			founded: contentData?.companyInfo?.founded,
			employees: contentData?.companyInfo?.employees,
			revenue: contentData?.companyInfo?.revenue,
		},

		technical: {
			technologies: extractTechnologies(technologyData),
			hosting: {
				provider: technologyData?.infrastructure?.hosting?.provider,
				type: technologyData?.infrastructure?.hosting?.type || 'unknown',
				location: technologyData?.infrastructure?.hosting?.location,
				cdn: technologyData?.infrastructure?.cdn?.provider,
			},
			security: {
				sslGrade: sslData?.grade || '',
				httpsRedirect: sslData?.httpsRedirect || false,
				hsts: sslData?.hsts?.enabled || false,
				securityHeaders: extractSecurityHeaders(securityData),
			},
			dns: {
				a: dnsData?.records?.A || [],
				aaaa: dnsData?.records?.AAAA || [],
				mx: formatMXRecords(dnsData?.records?.MX || []),
				cname: dnsData?.records?.CNAME || [],
				txt: dnsData?.records?.TXT || [],
				ns: dnsData?.records?.NS || [],
				ttl: dnsData?.ttl,
				dnssec: dnsData?.dnssecEnabled || false,
			},
			performance: {
				loadTime: performanceData?.loadTimes?.fullyLoaded || 0,
				firstContentfulPaint: performanceData?.coreWebVitals?.firstContentfulPaint || 0,
				largestContentfulPaint: performanceData?.coreWebVitals?.largestContentfulPaint || 0,
				cumulativeLayoutShift: performanceData?.coreWebVitals?.cumulativeLayoutShift || 0,
				mobileScore: performanceData?.mobile?.score || 0,
				desktopScore: performanceData?.desktop?.score || 0,
			},
		},

		seo: {
			title: homepageData?.metaTags?.title || '',
			metaDescription: homepageData?.metaTags?.description || '',
			keywords: extractKeywords(homepageData, seoData),
			headings: extractHeadings(homepageData),
			images: {
				total: homepageData?.images?.length || 0,
				withAlt: homepageData?.images?.filter((img: any) => img.alt)?.length || 0,
				optimized: homepageData?.images?.filter((img: any) => img.optimized)?.length || 0,
			},
			links: {
				internal: homepageData?.links?.internal || 0,
				external: homepageData?.links?.external || 0,
				nofollow: homepageData?.links?.nofollow || 0,
			},
			sitemap: {
				present: (robotsData?.sitemaps?.length || 0) > 0,
				url: robotsData?.sitemaps?.[0],
				urlCount: robotsData?.sitemapUrlCount || 0,
			},
			robots: {
				present: !!robotsData,
				policy: determineRobotsPolicy(robotsData),
				crawlDelay: robotsData?.crawlDelay,
			},
			structuredData: extractStructuredData(seoData),
			socialMedia: extractSocialMedia(homepageData, contentData),
		},

		content: {
			wordCount: contentData?.wordCount || 0,
			readabilityScore: contentData?.readabilityScore || 0,
			sentiment: contentData?.sentiment?.overall || 0,
			topics: contentData?.topics || [],
			expertise: {
				score: contentData?.expertise?.authorityScore || 0,
				indicators: extractExpertiseIndicators(contentData),
			},
			freshness: {
				lastModified: contentData?.freshness?.lastModified,
				updateFrequency: contentData?.freshness?.updateFrequency || 'unknown',
			},
		},

		business: {
			name: contentData?.companyInfo?.name || extractBusinessName(homepageData),
			industry: contentData?.companyInfo?.industry,
			services: contentData?.companyInfo?.services || [],
			targetAudience: contentData?.targetAudience || '',
			businessModel: contentData?.businessModel,
			contact: extractContactInfo(homepageData, contentData),
			location: {
				address: contentData?.companyInfo?.location,
				country: extractCountryFromData(dnsData, sslData),
				timezone: determineTimezone(dnsData, sslData),
			},
		},

		reputation: {
			trustScore: calculateTrustScore(analysisResult),
			securityScore: calculateSecurityScore(securityData, sslData),
			qualityScore: calculateQualityScore(contentData, seoData),
			reviews: extractReviews(contentData),
			certifications: extractCertifications(contentData),
		},

		ranking: {
			global: analysisResult.ranking?.global || {},
			category: analysisResult.ranking?.category || {},
			scores: analysisResult.ranking?.scores || {},
		},

		compliance: {
			gdpr: securityData?.privacy?.gdprCompliant || false,
			ccpa: securityData?.privacy?.ccpaCompliant || false,
			accessibility: calculateAccessibilityScore(homepageData),
			cookies: securityData?.privacy?.cookieConsent || false,
		},

		crawl: {
			lastCrawled: now,
			crawlType: analysisResult.crawlType || 'full',
			success: analysisResult.status === 'success',
			errors: analysisResult.errors?.length || 0,
			duration: analysisResult.duration || 0,
			dataCompleteness: calculateDataCompleteness(analysisResult),
		},
	};
}

// Helper functions for data transformation

function extractTLD(domain: string): string
{
	return domain.split('.').pop() || '';
}

function determineStatus(analysisResult: any): string
{
	if (analysisResult.status === 'success')
	{
		return 'active';
	}
	if (analysisResult.errors?.some((e: any) => e.includes('DNS')))
	{
		return 'inactive';
	}
	return 'unknown';
}

function extractCountryFromData(dnsData: any, sslData: any): string | undefined
{
	return sslData?.subject?.country || dnsData?.geoLocation?.country;
}

function extractTechnologies(technologyData: any): Array<{ name: string; version?: string; category: string }>
{
	const technologies: Array<{ name: string; version?: string; category: string }> = [];

	// Frontend technologies
	if (technologyData?.frontend?.frameworks)
	{
		technologyData.frontend.frameworks.forEach((fw: any) =>
		{
			technologies.push({
				name: fw.name,
				version: fw.version,
				category: 'frontend-framework',
			});
		});
	}

	// Backend technologies
	if (technologyData?.backend?.languages)
	{
		technologyData.backend.languages.forEach((lang: any) =>
		{
			technologies.push({
				name: lang.name,
				version: lang.version,
				category: 'programming-language',
			});
		});
	}

	// Server software
	if (technologyData?.backend?.server?.software)
	{
		technologies.push({
			name: technologyData.backend.server.software,
			version: technologyData.backend.server.version,
			category: 'web-server',
		});
	}

	return technologies;
}

function extractSecurityHeaders(securityData: any): Record<string, boolean>
{
	return {
		'strict-transport-security': securityData?.headers?.strictTransportSecurity || false,
		'content-security-policy': securityData?.headers?.contentSecurityPolicy?.present || false,
		'x-frame-options': !!securityData?.headers?.xFrameOptions,
		'x-content-type-options': securityData?.headers?.xContentTypeOptions || false,
		'referrer-policy': !!securityData?.headers?.referrerPolicy,
	};
}

function formatMXRecords(mxRecords: any[]): string[]
{
	return mxRecords.map((mx: any) => `${mx.priority} ${mx.exchange}`);
}

function extractKeywords(homepageData: any, seoData: any): string[]
{
	const keywords: string[] = [];

	if (homepageData?.metaTags?.keywords)
	{
		keywords.push(...homepageData.metaTags.keywords.split(',').map((k: string) => k.trim()));
	}

	if (seoData?.keywords)
	{
		keywords.push(...seoData.keywords);
	}

	return [...new Set(keywords)]; // Remove duplicates
}

function extractHeadings(homepageData: any): Record<string, string[]>
{
	return {
		h1: homepageData?.headings?.h1 || [],
		h2: homepageData?.headings?.h2 || [],
		h3: homepageData?.headings?.h3 || [],
	};
}

function determineRobotsPolicy(robotsData: any): string
{
	if (!robotsData)
	{
		return 'unknown';
	}

	const allows = robotsData.allows || [];
	const disallows = robotsData.disallows || [];

	if (disallows.includes('/'))
	{
		return 'restrictive';
	}
	if (allows.length > disallows.length)
	{
		return 'permissive';
	}

	return 'mixed';
}

function extractStructuredData(seoData: any): Array<{ type: string; count: number }>
{
	if (!seoData?.structuredData)
	{
		return [];
	}

	return seoData.structuredData.map((sd: any) => ({
		type: sd.type,
		count: sd.count || 1,
	}));
}

function extractSocialMedia(homepageData: any, contentData: any): Record<string, string>
{
	const social: Record<string, string> = {};

	// Extract from Open Graph data
	if (homepageData?.openGraph)
	{
		Object.entries(homepageData.openGraph).forEach(([key, value]) =>
		{
			if (key.includes('url') && typeof value === 'string')
			{
				const platform = detectSocialPlatform(value);
				if (platform)
				{
					social[platform] = value;
				}
			}
		});
	}

	// Extract from content analysis
	if (contentData?.socialLinks)
	{
		Object.assign(social, contentData.socialLinks);
	}

	return social;
}

function detectSocialPlatform(url: string): string | null
{
	const platforms = {
		'facebook.com': 'facebook',
		'twitter.com': 'twitter',
		'x.com': 'twitter',
		'linkedin.com': 'linkedin',
		'instagram.com': 'instagram',
		'youtube.com': 'youtube',
		'github.com': 'github',
	};

	for (const [domain, platform] of Object.entries(platforms))
	{
		if (url.includes(domain))
		{
			return platform;
		}
	}

	return null;
}

function extractExpertiseIndicators(contentData: any): string[]
{
	const indicators: string[] = [];

	if (contentData?.expertise?.trustworthiness > 0.8)
	{
		indicators.push('high-trustworthiness');
	}

	if (contentData?.expertise?.expertise > 0.8)
	{
		indicators.push('domain-expertise');
	}

	if (contentData?.certifications?.length > 0)
	{
		indicators.push('certified');
	}

	return indicators;
}

function extractBusinessName(homepageData: any): string | undefined
{
	// Try to extract from title tag
	const title = homepageData?.metaTags?.title;
	if (title)
	{
		// Remove common suffixes
		return title.replace(/\s*[-|]\s*(Home|Welcome|Official Site).*$/i, '').trim();
	}

	return undefined;
}

function extractContactInfo(homepageData: any, contentData: any): Record<string, string>
{
	const contact: Record<string, string> = {};

	if (contentData?.contactInfo)
	{
		Object.assign(contact, contentData.contactInfo);
	}

	// Extract from structured data
	if (homepageData?.structuredData)
	{
		homepageData.structuredData.forEach((sd: any) =>
		{
			if (sd.type === 'Organization' && sd.data)
			{
				if (sd.data.telephone)
				{
					contact.phone = sd.data.telephone;
				}
				if (sd.data.email)
				{
					contact.email = sd.data.email;
				}
			}
		});
	}

	return contact;
}

function determineTimezone(dnsData: any, sslData: any): string | undefined
{
	// This would require geolocation data
	// For now, return undefined
	return undefined;
}

function calculateTrustScore(analysisResult: any): number
{
	let score = 0;
	let factors = 0;

	// SSL certificate
	if (analysisResult.data?.sslAnalysis?.grade)
	{
		const grade = analysisResult.data.sslAnalysis.grade;
		if (grade.startsWith('A'))
		{
			score += 0.3;
		}
		else if (grade.startsWith('B'))
		{
			score += 0.2;
		}
		else if (grade.startsWith('C'))
		{
			score += 0.1;
		}
		factors++;
	}

	// Domain age (if available)
	if (analysisResult.data?.whoisData?.registrationDate)
	{
		const age = Date.now() - new Date(analysisResult.data.whoisData.registrationDate).getTime();
		const years = age / (1000 * 60 * 60 * 24 * 365);
		if (years > 5)
		{
			score += 0.2;
		}
		else if (years > 1)
		{
			score += 0.1;
		}
		factors++;
	}

	// Content quality
	if (analysisResult.data?.contentAnalysis?.expertise?.authorityScore)
	{
		score += analysisResult.data.contentAnalysis.expertise.authorityScore * 0.3;
		factors++;
	}

	// Security headers
	if (analysisResult.data?.securityAnalysis?.headers)
	{
		const headers = analysisResult.data.securityAnalysis.headers;
		let headerScore = 0;
		if (headers.strictTransportSecurity) headerScore += 0.05;
		if (headers.contentSecurityPolicy?.present) headerScore += 0.05;
		if (headers.xFrameOptions) headerScore += 0.03;
		if (headers.xContentTypeOptions) headerScore += 0.02;

		score += headerScore;
		factors++;
	}

	return factors > 0 ? Math.min(1, score / factors) : 0;
}

function calculateSecurityScore(securityData: any, sslData: any): number
{
	let score = 0;
	let maxScore = 0;

	// SSL grade (40% of security score)
	if (sslData?.grade)
	{
		const grade = sslData.grade;
		if (grade.startsWith('A'))
		{
			score += 0.4;
		}
		else if (grade.startsWith('B'))
		{
			score += 0.3;
		}
		else if (grade.startsWith('C'))
		{
			score += 0.2;
		}
		else if (grade.startsWith('D'))
		{
			score += 0.1;
		}
	}
	maxScore += 0.4;

	// Security headers (40% of security score)
	if (securityData?.headers)
	{
		const headers = securityData.headers;
		let headerScore = 0;
		let headerMax = 0;

		if (headers.strictTransportSecurity !== undefined)
		{
			headerScore += headers.strictTransportSecurity ? 0.1 : 0;
			headerMax += 0.1;
		}

		if (headers.contentSecurityPolicy !== undefined)
		{
			headerScore += headers.contentSecurityPolicy.present ? 0.15 : 0;
			headerMax += 0.15;
		}

		if (headers.xFrameOptions !== undefined)
		{
			headerScore += headers.xFrameOptions ? 0.08 : 0;
			headerMax += 0.08;
		}

		if (headers.xContentTypeOptions !== undefined)
		{
			headerScore += headers.xContentTypeOptions ? 0.07 : 0;
			headerMax += 0.07;
		}

		score += headerMax > 0 ? (headerScore / headerMax) * 0.4 : 0;
	}
	maxScore += 0.4;

	// Vulnerabilities (20% of security score)
	if (securityData?.vulnerabilities)
	{
		const vulns = securityData.vulnerabilities;
		const criticalVulns = vulns.filter((v: any) => v.severity === 'critical').length;
		const highVulns = vulns.filter((v: any) => v.severity === 'high').length;

		let vulnScore = 0.2;
		vulnScore -= criticalVulns * 0.1;
		vulnScore -= highVulns * 0.05;

		score += Math.max(0, vulnScore);
	}
	else
	{
		score += 0.2; // No known vulnerabilities
	}
	maxScore += 0.2;

	return maxScore > 0 ? score / maxScore : 0;
}

function calculateQualityScore(contentData: any, seoData: any): number
{
	let score = 0;
	let factors = 0;

	// Content quality
	if (contentData?.readabilityScore !== undefined)
	{
		score += Math.min(1, contentData.readabilityScore / 100);
		factors++;
	}

	// Word count (reasonable length)
	if (contentData?.wordCount !== undefined)
	{
		const wordCount = contentData.wordCount;
		if (wordCount >= 300 && wordCount <= 3000)
		{
			score += 1;
		}
		else if (wordCount >= 100)
		{
			score += 0.5;
		}
		factors++;
	}

	// SEO optimization
	if (seoData?.meta)
	{
		let seoScore = 0;
		if (seoData.meta.title?.optimal) seoScore += 0.25;
		if (seoData.meta.description?.optimal) seoScore += 0.25;
		if (seoData.technical?.sitemap?.present) seoScore += 0.25;
		if (seoData.technical?.structuredData?.length > 0) seoScore += 0.25;

		score += seoScore;
		factors++;
	}

	return factors > 0 ? score / factors : 0;
}

function extractReviews(contentData: any): Array<{ platform: string; rating: number; count: number }>
{
	return contentData?.reviews || [];
}

function extractCertifications(contentData: any): string[]
{
	return contentData?.certifications || [];
}

function calculateAccessibilityScore(homepageData: any): number
{
	let score = 0;
	let maxScore = 0;

	// Images with alt text
	if (homepageData?.images)
	{
		const totalImages = homepageData.images.length;
		const imagesWithAlt = homepageData.images.filter((img: any) => img.alt).length;

		if (totalImages > 0)
		{
			score += (imagesWithAlt / totalImages) * 0.3;
		}
		else
		{
			score += 0.3; // No images is also accessible
		}
		maxScore += 0.3;
	}

	// Proper heading structure
	if (homepageData?.headings)
	{
		const hasH1 = homepageData.headings.h1?.length > 0;
		const hasMultipleH1 = homepageData.headings.h1?.length > 1;

		if (hasH1 && !hasMultipleH1)
		{
			score += 0.2;
		}
		else if (hasH1)
		{
			score += 0.1;
		}
		maxScore += 0.2;
	}

	// Form labels (if forms are present)
	if (homepageData?.forms)
	{
		const totalInputs = homepageData.forms.reduce((sum: number, form: any) => sum + (form.inputs?.length || 0), 0);
		const labeledInputs = homepageData.forms.reduce((sum: number, form: any) =>
			sum + (form.inputs?.filter((input: any) => input.label)?.length || 0), 0);

		if (totalInputs > 0)
		{
			score += (labeledInputs / totalInputs) * 0.2;
		}
		else
		{
			score += 0.2; // No forms is neutral
		}
		maxScore += 0.2;
	}

	// Color contrast (if available)
	if (homepageData?.accessibility?.colorContrast !== undefined)
	{
		score += homepageData.accessibility.colorContrast * 0.3;
		maxScore += 0.3;
	}

	return maxScore > 0 ? score / maxScore : 0;
}

function calculateDataCompleteness(analysisResult: any): number
{
	const requiredFields = [
		'dnsAnalysis',
		'sslAnalysis',
		'homepageAnalysis',
		'robotsAnalysis',
	];

	const optionalFields = [
		'performanceAnalysis',
		'securityAnalysis',
		'contentAnalysis',
		'technologyAnalysis',
	];

	let completeness = 0;
	let totalWeight = 0;

	// Required fields (70% weight)
	requiredFields.forEach((field) =>
	{
		if (analysisResult.data?.[field])
		{
			completeness += 0.7 / requiredFields.length;
		}
		totalWeight += 0.7 / requiredFields.length;
	});

	// Optional fields (30% weight)
	optionalFields.forEach((field) =>
	{
		if (analysisResult.data?.[field])
		{
			completeness += 0.3 / optionalFields.length;
		}
		totalWeight += 0.3 / optionalFields.length;
	});

	return totalWeight > 0 ? completeness / totalWeight : 0;
}

export { buildDomainDescription, buildEnhancedDomainDescription };
