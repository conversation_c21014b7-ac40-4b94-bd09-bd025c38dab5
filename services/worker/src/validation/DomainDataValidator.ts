import { logger } from '@shared';
import type {
    DataAvailabilityReportType,
    ComprehensiveDomainDataType,
    ValidationResultType,
    PriorityFieldListType,
} from './ComprehensiveDomainSchema';
import DomainDataTracker from './ComprehensiveDomainSchema';
import {
    // DomainDataTracker,
    DataAvailabilityEnum,
    DataPriorityEnum,
    CollectionPhaseEnum,
} from './ComprehensiveDomainSchema';

// Supporting interfaces

type DataQualityAssessmentType = {
    score: number;
    issues: string[];
    recommendations: string[];
    confidence: number;
    dataCompleteness: number;
    validationErrors: string[];
};

type RankingReadinessCheckType = {
    ready: boolean;
    missingCriticalFields: string[];
    blockers: string[];
    warnings: string[];
    confidenceScore: number;
};

type DataCollectionPlanType = {
    domain: string;
    phases: Array<{
        phase: string;
        priority: string;
        fields: string[];
        estimatedTime: number;
    }>;
    estimatedDuration: number;
    priority: string;
};

/**
 * Domain data validator and completeness checker
 * Provides validation, completeness tracking, and data quality assessment
 */
class DomainDataValidator
{
    private readonly logger = logger.getLogger('DomainDataValidator');

    private readonly dataTracker: DomainDataTracker;

    constructor()
    {
        this.dataTracker = new DomainDataTracker(this.logger);
    }

    /**
     * Validate complete domain data
     */
    async validateDomainData(domainData: Partial<ComprehensiveDomainDataType>): Promise<ValidationResultType>
    {
        this.logger.info('Validating domain data', {
            domain: domainData.identification?.domain,
        });

        try
        {
            const validationResult = this.dataTracker.validateDomainData(domainData);

            this.logger.info('Domain data validation completed', {
                domain: domainData.identification?.domain,
                valid: validationResult.valid,
                errorCount: validationResult.errors.length,
                warningCount: validationResult.warnings.length,
            });

            return validationResult;
        }
        catch (error)
        {
            this.logger.error('Domain data validation failed', {
                domain: domainData.identification?.domain,
                error: error instanceof Error ? error.message : 'Unknown error',
            });

            throw error;
        }
    }

    /**
     * Check data completeness and generate report
     */
    async checkDataCompleteness(
        domainData: Partial<ComprehensiveDomainDataType>,
    ): Promise<DataAvailabilityReportType>
    {
        this.logger.info('Checking data completeness', {
            domain: domainData.identification?.domain,
        });

        try
        {
            const availabilityReport = this.dataTracker.trackDataAvailability(domainData);

            this.logger.info('Data completeness check completed', {
                domain: availabilityReport.domain,
                overallCompleteness: availabilityReport.overallCompleteness,
                missingCriticalFields: availabilityReport.missingCriticalFields.length,
            });

            return availabilityReport;
        }
        catch (error)
        {
            this.logger.error('Data completeness check failed', {
                domain: domainData.identification?.domain,
                error: error instanceof Error ? error.message : 'Unknown error',
            });

            throw error;
        }
    }

    /**
     * Get prioritized list of missing fields for data collection
     */
    async getPriorityMissingFields(
        domainData: Partial<ComprehensiveDomainDataType>,
    ): Promise<PriorityFieldListType>
    {
        this.logger.debug('Getting priority missing fields', {
            domain: domainData.identification?.domain,
        });

        try
        {
            const priorityFields = this.dataTracker.getPriorityMissingFields(domainData);

            this.logger.debug('Priority missing fields identified', {
                domain: domainData.identification?.domain,
                critical: priorityFields.critical.length,
                high: priorityFields.high.length,
                medium: priorityFields.medium.length,
                low: priorityFields.low.length,
            });

            return priorityFields;
        }
        catch (error)
        {
            this.logger.error('Failed to get priority missing fields', {
                domain: domainData.identification?.domain,
                error: error instanceof Error ? error.message : 'Unknown error',
            });

            throw error;
        }
    }

    /**
     * Create empty domain data structure for new domains
     */
    async createEmptyDomainData(domain: string): Promise<ComprehensiveDomainDataType>
    {
        this.logger.info('Creating empty domain data structure', { domain });

        try
        {
            const emptyData = this.dataTracker.createEmptyDomainData(domain);

            this.logger.info('Empty domain data structure created', { domain });

            return emptyData;
        }
        catch (error)
        {
            this.logger.error('Failed to create empty domain data', {
                domain,
                error: error instanceof Error ? error.message : 'Unknown error',
            });

            throw error;
        }
    }

    /**
     * Assess data quality and provide recommendations
     */
    async assessDataQuality(
        domainData: Partial<ComprehensiveDomainDataType>,
    ): Promise<DataQualityAssessmentType>
    {
        this.logger.info('Assessing data quality', {
            domain: domainData.identification?.domain,
        });

        try
        {
            const availabilityReport = await this.checkDataCompleteness(domainData);
            const validationResult = await this.validateDomainData(domainData);
            const priorityFields = await this.getPriorityMissingFields(domainData);

            const assessment: DataQualityAssessmentType = {
                domain: domainData.identification?.domain || 'unknown',
                overallQuality: this.calculateOverallQuality(availabilityReport, validationResult),
                completeness: {
                    overall: availabilityReport.overallCompleteness,
                    byPhase: availabilityReport.phaseCompleteness,
                    missingCritical: availabilityReport.missingCriticalFields.length,
                },
                validation: {
                    valid: validationResult.valid,
                    errorCount: validationResult.errors.length,
                    warningCount: validationResult.warnings.length,
                },
                recommendations: this.generateQualityRecommendations(
                    availabilityReport,
                    validationResult,
                    priorityFields,
                ),
                nextActions: this.determineNextActions(priorityFields, availabilityReport),
                lastAssessed: new Date(),
            };

            this.logger.info('Data quality assessment completed', {
                domain: assessment.domain,
                overallQuality: assessment.overallQuality,
                completeness: assessment.completeness.overall,
                valid: assessment.validation.valid,
            });

            return assessment;
        }
        catch (error)
        {
            this.logger.error('Data quality assessment failed', {
                domain: domainData.identification?.domain,
                error: error instanceof Error ? error.message : 'Unknown error',
            });

            throw error;
        }
    }

    /**
     * Check if domain data is ready for ranking
     */
    async isReadyForRanking(
        domainData: Partial<ComprehensiveDomainDataType>,
    ): Promise<RankingReadinessCheckType>
    {
        this.logger.debug('Checking ranking readiness', {
            domain: domainData.identification?.domain,
        });

        try
        {
            const priorityFields = await this.getPriorityMissingFields(domainData);
            const availabilityReport = await this.checkDataCompleteness(domainData);

            const readiness: RankingReadinessCheckType = {
                ready: priorityFields.critical.length === 0,
                missingCriticalFields: priorityFields.critical,
                minimumDataPresent: availabilityReport.overallCompleteness >= 30, // 30% minimum
                recommendedDataPresent: availabilityReport.overallCompleteness >= 70, // 70% recommended
                blockers: [],
                warnings: [],
            };

            // Check for specific ranking blockers
            if (!domainData.identification?.domain)
            {
                readiness.blockers.push('Domain identification missing');
            }

            if (!domainData.performance || Object.keys(domainData.performance).length === 0)
            {
                readiness.blockers.push('Performance data missing');
            }

            if (!domainData.security || Object.keys(domainData.security).length === 0)
            {
                readiness.blockers.push('Security data missing');
            }

            // Add warnings for recommended data
            if (availabilityReport.phaseCompleteness[CollectionPhaseEnum.PHASE_1_BASIC] < 80)
            {
                readiness.warnings.push('Phase 1 basic data incomplete');
            }

            readiness.ready = readiness.ready && readiness.blockers.length === 0;

            this.logger.debug('Ranking readiness check completed', {
                domain: domainData.identification?.domain,
                ready: readiness.ready,
                blockers: readiness.blockers.length,
                warnings: readiness.warnings.length,
            });

            return readiness;
        }
        catch (error)
        {
            this.logger.error('Ranking readiness check failed', {
                domain: domainData.identification?.domain,
                error: error instanceof Error ? error.message : 'Unknown error',
            });

            throw error;
        }
    }

    /**
     * Generate data collection plan based on missing fields
     */
    async generateCollectionPlan(
        domainData: Partial<ComprehensiveDomainDataType>,
    ): Promise<DataCollectionPlanType>
    {
        this.logger.info('Generating data collection plan', {
            domain: domainData.identification?.domain,
        });

        try
        {
            const priorityFields = await this.getPriorityMissingFields(domainData);
            const availabilityReport = await this.checkDataCompleteness(domainData);

            const plan: DataCollectionPlanType = {
                domain: domainData.identification?.domain || 'unknown',
                phases: [],
                estimatedDuration: 0,
                resourceRequirements: {
                    cpu: 'low',
                    memory: 'low',
                    network: 'medium',
                    storage: 'low',
                },
                dependencies: [],
                priority: this.determinePlanPriority(priorityFields),
            };

            // Phase 1: Critical and high priority fields
            if (priorityFields.critical.length > 0 || priorityFields.high.length > 0)
            {
                plan.phases.push({
                    phase: CollectionPhaseEnum.PHASE_1_BASIC,
                    fields: [...priorityFields.critical, ...priorityFields.high],
                    estimatedDuration: 30, // 30 seconds
                    modules: ['dns', 'ssl', 'homepage', 'robots'],
                    dependencies: [],
                });
            }

            // Phase 2: Medium priority fields (if Phase 1 complete)
            if (
                priorityFields.medium.length > 0 &&
                availabilityReport.phaseCompleteness[CollectionPhaseEnum.PHASE_1_BASIC] > 70
            )
            {
                plan.phases.push({
                    phase: CollectionPhaseEnum.PHASE_2_MODERATE,
                    fields: priorityFields.medium,
                    estimatedDuration: 120, // 2 minutes
                    modules: ['performance', 'screenshot', 'advanced-content', 'image-processing'],
                    dependencies: [CollectionPhaseEnum.PHASE_1_BASIC],
                });
            }

            // Phase 3: Low priority fields (if Phase 2 complete)
            if (
                priorityFields.low.length > 0 &&
                availabilityReport.phaseCompleteness[CollectionPhaseEnum.PHASE_2_MODERATE] > 70
            )
            {
                plan.phases.push({
                    phase: CollectionPhaseEnum.PHASE_3_ADVANCED,
                    fields: priorityFields.low,
                    estimatedDuration: 300, // 5 minutes
                    modules: ['ai-description', 'advanced-analysis'],
                    dependencies: [CollectionPhaseEnum.PHASE_1_BASIC, CollectionPhaseEnum.PHASE_2_MODERATE],
                });
            }

            // Calculate total estimated duration
            plan.estimatedDuration = plan
                .phases
                .reduce((total: number, phase: { estimatedDuration: number }) => total + phase.estimatedDuration, 0);

            // Adjust resource requirements based on phases
            if (plan.phases.some((p: { phase: CollectionPhaseEnum }) => p.phase === CollectionPhaseEnum.PHASE_2_MODERATE))
            {
                plan.resourceRequirements.cpu = 'medium';
                plan.resourceRequirements.memory = 'medium';
            }

            if (plan.phases.some((p: { phase: CollectionPhaseEnum }) => p.phase === CollectionPhaseEnum.PHASE_3_ADVANCED))
            {
                plan.resourceRequirements.cpu = 'high';
                plan.resourceRequirements.memory = 'high';
            }

            this.logger.info('Data collection plan generated', {
                domain: plan.domain,
                phases: plan.phases.length,
                estimatedDuration: plan.estimatedDuration,
                priority: plan.priority,
            });

            return plan;
        }
        catch (error)
        {
            this.logger.error('Failed to generate collection plan', {
                domain: domainData.identification?.domain,
                error: error instanceof Error ? error.message : 'Unknown error',
            });

            throw error;
        }
    }

    /**
     * Calculate overall data quality score
     */
    private calculateOverallQuality(
        availabilityReport: DataAvailabilityReportType,
        validationResult: ValidationResultType,
    ): number
    {
        // Weight completeness and validation
        const completenessScore = availabilityReport.overallCompleteness;
        const validationScore = validationResult.valid
            ? 100
            : Math.max(0, 100 - (validationResult.errors.length * 10));

        // 70% completeness, 30% validation
        return (completenessScore * 0.7) + (validationScore * 0.3);
    }

    /**
     * Generate quality improvement recommendations
     */
    private generateQualityRecommendations(
        availabilityReport: DataAvailabilityReportType,
        validationResult: ValidationResultType,
        priorityFields: PriorityFieldListType,
    ): string[]
    {
        const recommendations: string[] = [];

        // Add availability recommendations
        recommendations.push(...availabilityReport.recommendations);

        // Add validation recommendations
        if (!validationResult.valid)
        {
            recommendations.push(`Fix ${validationResult.errors.length} validation errors`);
        }

        if (validationResult.warnings.length > 0)
        {
            recommendations.push(`Address ${validationResult.warnings.length} validation warnings`);
        }

        // Add priority field recommendations
        if (priorityFields.critical.length > 0)
        {
            recommendations.push(`Collect ${priorityFields.critical.length} critical missing fields immediately`);
        }

        if (priorityFields.high.length > 0)
        {
            recommendations.push(`Collect ${priorityFields.high.length} high-priority missing fields`);
        }

        return recommendations;
    }

	/**
		* Determine next actions based on data status
		*/
	private determineNextActions(
		priorityFields: PriorityFieldListType,
		availabilityReport: DataAvailabilityReportType,
	): string[]
	{
		const actions: string[] = [];

		if (priorityFields.critical.length > 0)
		{
			actions.push('Run critical data collection immediately');
		}
		else if (priorityFields.high.length > 0)
		{
			actions.push('Schedule high-priority data collection');
		}
		else if (availabilityReport.overallCompleteness < 70)
		{
			actions.push('Run comprehensive data collection');
		}
		else
		{
			actions.push('Data collection up to date');
		}

		return actions;
	}

	/**
		* Determine collection plan priority
		*/
	private determinePlanPriority(priorityFields: PriorityFieldListType):
		| 'critical' | 'high' | 'medium' | 'low'
	{
		if (priorityFields.critical.length > 0) return 'critical';
		if (priorityFields.high.length > 0) return 'high';
		if (priorityFields.medium.length > 0) return 'medium';

		return 'low';
	}
}


// Public type aliases for re-export convenience
export type {
	DataQualityAssessmentType,
	RankingReadinessCheckType,
	DataCollectionPlanType,
};

export default DomainDataValidator;
