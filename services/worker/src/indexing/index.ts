/**
 * Search Indexing and Data Synchronization Module
 *
 * This module provides comprehensive search indexing and data synchronization functionality
 * extracted from all three services (crawler, ranking-engine, scheduler) including:
 *
 * - Manticore search index management
 * - Data synchronization from ScyllaDB to Manticore
 * - Intelligent cache invalidation with dependency tracking
 * - Automated index maintenance and optimization
 * - Performance monitoring and capacity planning
 */

// Main service exports
export { default as SearchIndexingService } from './SearchIndexingService';
export { default as ManticoreIndexManager } from './ManticoreIndexManager';
export { default as DataSynchronizationService } from './DataSynchronizationService';
export { default as CacheInvalidationManager } from './CacheInvalidationManager';
export { default as IndexMaintenanceService } from './IndexMaintenanceService';

// Type exports
export type {
	IndexingConfiguration,
	IndexingHealth,
	IndexingStatistics,
} from './SearchIndexingService';

export type {
	IndexHealthStatus,
	IndexUpdateResult,
	BulkIndexResult,
} from './ManticoreIndexManager';

export type {
	SyncOperation,
	SyncResult,
	SyncConfiguration,
	ConflictResolutionResult,
} from './DataSynchronizationService';

export type {
	InvalidationRule,
	InvalidationEvent,
	InvalidationResult,
	CacheDependency,
} from './CacheInvalidationManager';

export type {
	MaintenanceTask,
	MaintenanceResult,
	CapacityMetrics,
	PerformanceOptimization,
} from './IndexMaintenanceService';
