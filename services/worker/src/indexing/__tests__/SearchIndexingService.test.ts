import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import SearchIndexingService from '../SearchIndexingService';
import ManticoreIndexManager from '../ManticoreIndexManager';
import DataSynchronizationService from '../DataSynchronizationService';
import CacheInvalidationManager from '../CacheInvalidationManager';
import IndexMaintenanceService from '../IndexMaintenanceService';
import type { ComprehensiveDomainData } from '../../validation/ComprehensiveDomainSchema';

// Mock all dependencies
vi.mock('../ManticoreIndexManager');
vi.mock('../DataSynchronizationService');
vi.mock('../CacheInvalidationManager');
vi.mock('../IndexMaintenanceService');

vi.mock('@shared/utils/Logger', () => ({
	logger: {
		getLogger: vi.fn(() => ({
			info: vi.fn(),
			debug: vi.fn(),
			warn: vi.fn(),
			error: vi.fn(),
		})),
	},
}));

describe('SearchIndexingService', () =>
{
	let searchIndexingService: SearchIndexingService;
	let mockScyllaClient: any;
	let mockManticoreClient: any;
	let mockRedisClient: any;
	let mockIndexManager: any;
	let mockSyncService: any;
	let mockCacheManager: any;
	let mockMaintenanceService: any;

	beforeEach(() =>
	{
		// Mock database clients
		mockScyllaClient = {
			execute: vi.fn(),
		};

		mockManticoreClient = {
			indexExists: vi.fn(),
			createIndex: vi.fn(),
			upsertDocument: vi.fn(),
			deleteDocument: vi.fn(),
			searchDomains: vi.fn(),
		};

		mockRedisClient = {
			get: vi.fn(),
			set: vi.fn(),
			del: vi.fn(),
			keys: vi.fn(),
		};

		// Mock service components
		mockIndexManager = {
			initialize: vi.fn(),
			indexDomain: vi.fn(),
			bulkIndexDomains: vi.fn(),
			deleteDomain: vi.fn(),
			optimizeAllIndexes: vi.fn(),
			getHealthStatus: vi.fn(),
			getIndexStatistics: vi.fn(),
		};

		mockSyncService = {
			initialize: vi.fn(),
			startPeriodicSync: vi.fn(),
			stopPeriodicSync: vi.fn(),
			performFullSync: vi.fn(),
			performIncrementalSync: vi.fn(),
			syncDomain: vi.fn(),
			getSyncStatistics: vi.fn(),
		};

		mockCacheManager = {
			initialize: vi.fn(),
			triggerInvalidation: vi.fn(),
			manualInvalidation: vi.fn(),
			cancelAllPendingInvalidations: vi.fn(),
			cleanup: vi.fn(),
			getInvalidationStatistics: vi.fn(),
		};

		mockMaintenanceService = {
			initialize: vi.fn(),
			start: vi.fn(),
			stop: vi.fn(),
			executeTaskManually: vi.fn(),
			getMaintenanceStatistics: vi.fn(),
		};

		// Mock constructors
		vi.mocked(ManticoreIndexManager).mockImplementation(() => mockIndexManager);
		vi.mocked(DataSynchronizationService).mockImplementation(() => mockSyncService);
		vi.mocked(CacheInvalidationManager).mockImplementation(() => mockCacheManager);
		vi.mocked(IndexMaintenanceService).mockImplementation(() => mockMaintenanceService);

		// Create service instance
		searchIndexingService = new SearchIndexingService(
			mockScyllaClient,
			mockManticoreClient,
			mockRedisClient,
		);
	});

	afterEach(() =>
	{
		vi.clearAllMocks();
	});

	describe('initialization', () =>
	{
		it('should initialize all component services', async () =>
		{
			await searchIndexingService.initialize();

			expect(mockIndexManager.initialize).toHaveBeenCalled();
			expect(mockSyncService.initialize).toHaveBeenCalled();
			expect(mockCacheManager.initialize).toHaveBeenCalled();
			expect(mockMaintenanceService.initialize).toHaveBeenCalled();
		});

		it('should handle initialization errors', async () =>
		{
			mockIndexManager.initialize.mockRejectedValue(new Error('Init failed'));

			await expect(searchIndexingService.initialize()).rejects.toThrow('Init failed');
		});
	});

	describe('service lifecycle', () =>
	{
		beforeEach(async () =>
		{
			await searchIndexingService.initialize();
		});

		it('should start all services', async () =>
		{
			await searchIndexingService.start();

			expect(mockSyncService.startPeriodicSync).toHaveBeenCalled();
			expect(mockMaintenanceService.start).toHaveBeenCalled();
		});

		it('should stop all services', async () =>
		{
			await searchIndexingService.start();
			await searchIndexingService.stop();

			expect(mockSyncService.stopPeriodicSync).toHaveBeenCalled();
			expect(mockMaintenanceService.stop).toHaveBeenCalled();
			expect(mockCacheManager.cancelAllPendingInvalidations).toHaveBeenCalled();
		});

		it('should not start if not initialized', async () =>
		{
			const uninitializedService = new SearchIndexingService(
				mockScyllaClient,
				mockManticoreClient,
				mockRedisClient,
			);

			await expect(uninitializedService.start()).rejects.toThrow('Service must be initialized before starting');
		});
	});

	describe('domain indexing', () =>
	{
		let mockDomainData: ComprehensiveDomainData;

		beforeEach(async () =>
		{
			await searchIndexingService.initialize();

			mockDomainData = {
				domain: 'example.com',
				lastUpdated: '2024-01-01T00:00:00.000Z',
				identification: {
					domain: 'example.com',
					normalizedDomain: 'example.com',
					rootDomain: 'example.com',
					tld: 'com',
				},
				category: 'technology',
				performance: { score: 85 },
				security: { score: 90, sslGrade: 'A' },
				seo: { score: 80, metaTags: { title: 'Example', description: 'Test domain' } },
				technical: { score: 88, technologies: ['React', 'Node.js'] },
				domainInfo: { age: 365, registrar: 'Example Registrar' },
				ranking: { globalRank: 1000, overallScore: 85 },
			} as ComprehensiveDomainData;
		});

		it('should index a single domain successfully', async () =>
		{
			const mockResult = {
				success: true,
				documentId: 'example.com',
				operation: 'update' as const,
				processingTime: 100,
			};

			mockIndexManager.indexDomain.mockResolvedValue(mockResult);
			mockCacheManager.triggerInvalidation.mockResolvedValue({
				success: true,
				patternsInvalidated: ['analysis:example.com'],
				dependenciesInvalidated: [],
				errors: [],
				processingTime: 50,
			});

			const result = await searchIndexingService.indexDomain(mockDomainData);

			expect(mockIndexManager.indexDomain).toHaveBeenCalledWith(mockDomainData);
			expect(mockCacheManager.triggerInvalidation).toHaveBeenCalled();
			expect(result).toEqual(mockResult);
		});

		it('should handle indexing errors', async () =>
		{
			mockIndexManager.indexDomain.mockRejectedValue(new Error('Indexing failed'));

			await expect(searchIndexingService.indexDomain(mockDomainData)).rejects.toThrow('Indexing failed');
		});

		it('should bulk index multiple domains', async () =>
		{
			const mockResult = {
				totalProcessed: 2,
				successful: 2,
				failed: 0,
				errors: [],
				processingTime: 200,
			};

			mockIndexManager.bulkIndexDomains.mockResolvedValue(mockResult);
			mockCacheManager.triggerInvalidation.mockResolvedValue({
				success: true,
				patternsInvalidated: ['search:results:*'],
				dependenciesInvalidated: [],
				errors: [],
				processingTime: 50,
			});

			const domainsData = [mockDomainData, { ...mockDomainData, domain: 'test.com' }];
			const result = await searchIndexingService.bulkIndexDomains(domainsData);

			expect(mockIndexManager.bulkIndexDomains).toHaveBeenCalledWith(domainsData);
			expect(result).toEqual(mockResult);
		});

		it('should delete domain from index', async () =>
		{
			const mockResult = {
				success: true,
				documentId: 'example.com',
				operation: 'delete' as const,
				processingTime: 50,
			};

			mockIndexManager.deleteDomain.mockResolvedValue(mockResult);

			const result = await searchIndexingService.deleteDomain('example.com');

			expect(mockIndexManager.deleteDomain).toHaveBeenCalledWith('example.com');
			expect(result).toEqual(mockResult);
		});
	});

	describe('synchronization', () =>
	{
		beforeEach(async () =>
		{
			await searchIndexingService.initialize();
		});

		it('should perform full synchronization', async () =>
		{
			const mockResult = {
				operation: 'full' as const,
				startTime: '2024-01-01T00:00:00.000Z',
				endTime: '2024-01-01T00:01:00.000Z',
				duration: 60000,
				domainsProcessed: 100,
				successful: 95,
				failed: 5,
				errors: [],
			};

			mockSyncService.performFullSync.mockResolvedValue(mockResult);

			const result = await searchIndexingService.performFullSync();

			expect(mockSyncService.performFullSync).toHaveBeenCalled();
			expect(result).toEqual(mockResult);
		});

		it('should perform incremental synchronization', async () =>
		{
			const mockResult = {
				operation: 'incremental' as const,
				startTime: '2024-01-01T00:00:00.000Z',
				endTime: '2024-01-01T00:00:30.000Z',
				duration: 30000,
				domainsProcessed: 10,
				successful: 10,
				failed: 0,
				errors: [],
			};

			mockSyncService.performIncrementalSync.mockResolvedValue(mockResult);

			const result = await searchIndexingService.performIncrementalSync();

			expect(mockSyncService.performIncrementalSync).toHaveBeenCalled();
			expect(result).toEqual(mockResult);
		});

		it('should sync specific domain', async () =>
		{
			const mockResult = {
				operation: 'domain_specific' as const,
				startTime: '2024-01-01T00:00:00.000Z',
				endTime: '2024-01-01T00:00:05.000Z',
				duration: 5000,
				domainsProcessed: 1,
				successful: 1,
				failed: 0,
				errors: [],
			};

			mockSyncService.syncDomain.mockResolvedValue(mockResult);

			const result = await searchIndexingService.syncDomain('example.com');

			expect(mockSyncService.syncDomain).toHaveBeenCalledWith('example.com');
			expect(result).toEqual(mockResult);
		});
	});

	describe('cache invalidation', () =>
	{
		beforeEach(async () =>
		{
			await searchIndexingService.initialize();
		});

		it('should invalidate domain cache', async () =>
		{
			const mockResult = {
				success: true,
				patternsInvalidated: ['analysis:example.com'],
				dependenciesInvalidated: [],
				errors: [],
				processingTime: 50,
			};

			mockCacheManager.triggerInvalidation.mockResolvedValue(mockResult);

			const result = await searchIndexingService.invalidateDomainCache('example.com');

			expect(mockCacheManager.triggerInvalidation).toHaveBeenCalledWith({
				type: 'domain_update',
				domain: 'example.com',
				timestamp: expect.any(String),
				triggeredBy: 'SearchIndexingService',
			});
			expect(result).toEqual(mockResult);
		});

		it('should invalidate ranking cache', async () =>
		{
			const mockResult = {
				success: true,
				patternsInvalidated: ['ranking:domain:example.com'],
				dependenciesInvalidated: [],
				errors: [],
				processingTime: 50,
			};

			mockCacheManager.triggerInvalidation.mockResolvedValue(mockResult);

			const result = await searchIndexingService.invalidateRankingCache('example.com', 'technology');

			expect(mockCacheManager.triggerInvalidation).toHaveBeenCalledWith({
				type: 'ranking_update',
				domain: 'example.com',
				category: 'technology',
				timestamp: expect.any(String),
				triggeredBy: 'SearchIndexingService',
			});
			expect(result).toEqual(mockResult);
		});

		it('should perform manual cache invalidation', async () =>
		{
			const patterns = ['test:*', 'cache:example'];
			const metadata = { reason: 'manual cleanup' };
			const mockResult = {
				success: true,
				patternsInvalidated: patterns,
				dependenciesInvalidated: [],
				errors: [],
				processingTime: 25,
			};

			mockCacheManager.manualInvalidation.mockResolvedValue(mockResult);

			const result = await searchIndexingService.manualCacheInvalidation(patterns, metadata);

			expect(mockCacheManager.manualInvalidation).toHaveBeenCalledWith(patterns, metadata);
			expect(result).toEqual(mockResult);
		});
	});

	describe('maintenance operations', () =>
	{
		beforeEach(async () =>
		{
			await searchIndexingService.initialize();
		});

		it('should optimize indexes', async () =>
		{
			await searchIndexingService.optimizeIndexes();

			expect(mockIndexManager.optimizeAllIndexes).toHaveBeenCalled();
		});

		it('should execute maintenance task manually', async () =>
		{
			const taskResult = { success: true, details: {} };
			mockMaintenanceService.executeTaskManually.mockResolvedValue(taskResult);

			const result = await searchIndexingService.executeMaintenanceTask('cleanup');

			expect(mockMaintenanceService.executeTaskManually).toHaveBeenCalledWith('cleanup');
			expect(result).toEqual(taskResult);
		});
	});

	describe('health monitoring', () =>
	{
		beforeEach(async () =>
		{
			await searchIndexingService.initialize();
		});

		it('should get health status', async () =>
		{
			mockIndexManager.getHealthStatus.mockReturnValue(new Map([
				['domains_index', { status: 'healthy', issues: [] }],
			]));
			mockSyncService.getSyncStatistics.mockResolvedValue({
				lastSyncTime: '2024-01-01T00:00:00.000Z',
				isRunning: true,
			});
			mockCacheManager.getInvalidationStatistics.mockResolvedValue({
				totalInvalidations: 10,
				pendingInvalidations: 0,
			});
			mockMaintenanceService.getMaintenanceStatistics.mockResolvedValue({
				totalTasks: 5,
				runningTasks: 0,
				scheduledTasks: 3,
			});

			const health = await searchIndexingService.getHealthStatus();

			expect(health.status).toBe('healthy');
			expect(health.components.indexManager).toBe('healthy');
		});

		it('should get service statistics', async () =>
		{
			mockSyncService.getSyncStatistics.mockResolvedValue({
				lastSyncTime: '2024-01-01T00:00:00.000Z',
			});
			mockCacheManager.getInvalidationStatistics.mockResolvedValue({
				totalInvalidations: 10,
				pendingInvalidations: 0,
				invalidationsByType: { domain_update: 5, ranking_update: 5 },
			});
			mockMaintenanceService.getMaintenanceStatistics.mockResolvedValue({
				totalTasks: 5,
				runningTasks: 0,
				scheduledTasks: 3,
			});
			mockIndexManager.getIndexStatistics.mockResolvedValue({
				domains_index: { documentCount: 1000 },
				categories_index: { documentCount: 50 },
			});

			const stats = await searchIndexingService.getStatistics();

			expect(stats.totalDocuments).toBe(1050);
			expect(stats.indexesCount).toBe(2);
			expect(stats.lastSyncTime).toBe('2024-01-01T00:00:00.000Z');
		});
	});

	describe('configuration management', () =>
	{
		beforeEach(async () =>
		{
			await searchIndexingService.initialize();
		});

		it('should get current configuration', () =>
		{
			const config = searchIndexingService.getConfiguration();

			expect(config).toHaveProperty('syncInterval');
			expect(config).toHaveProperty('batchSize');
			expect(config).toHaveProperty('maxConcurrency');
		});

		it('should update configuration', async () =>
		{
			const newConfig = {
				syncInterval: 600000,
				batchSize: 200,
			};

			await searchIndexingService.updateConfiguration(newConfig);

			const updatedConfig = searchIndexingService.getConfiguration();
			expect(updatedConfig.syncInterval).toBe(600000);
			expect(updatedConfig.batchSize).toBe(200);
		});
	});

	describe('data transformation', () =>
	{
		it('should transform domain data for indexing', () =>
		{
			const mockDomainData: ComprehensiveDomainData = {
				domain: 'example.com',
				lastUpdated: '2024-01-01T00:00:00.000Z',
				identification: {
					domain: 'example.com',
					normalizedDomain: 'example.com',
					rootDomain: 'example.com',
					tld: 'com',
				},
				category: 'technology',
				performance: { score: 85 },
				security: { score: 90, sslGrade: 'A' },
				seo: {
					score: 80,
					metaTags: {
						title: 'Example Domain',
						description: 'A test domain'
					}
				},
				technical: { score: 88, technologies: ['React', 'Node.js'] },
				domainInfo: { age: 365, registrar: 'Example Registrar' },
				ranking: { globalRank: 1000, overallScore: 85 },
				location: { country: 'US' },
				language: { primary: 'en' },
				content: {
					title: 'Example Domain',
					description: 'A test domain',
					fullText: 'This is example content'
				},
				traffic: { estimate: 10000 },
				backlinks: { score: 75 },
				mobile: { friendlyScore: 95 },
				accessibility: { score: 88 },
			} as ComprehensiveDomainData;

			const transformed = searchIndexingService.transformDomainDataForIndex(mockDomainData);

			expect(transformed.domain).toBe('example.com');
			expect(transformed.title).toBe('Example Domain');
			expect(transformed.description).toBe('A test domain');
			expect(transformed.category).toBe('technology');
			expect(transformed.country).toBe('US');
			expect(transformed.language).toBe('en');
			expect(transformed.technologies).toEqual(['React', 'Node.js']);
			expect(transformed.global_rank).toBe(1000);
			expect(transformed.overall_score).toBe(85);
			expect(transformed.ssl_grade).toBe('A');
		});

		it('should normalize domain data', () =>
		{
			const mockDomainData: ComprehensiveDomainData = {
				domain: 'EXAMPLE.COM',
				identification: {
					domain: 'EXAMPLE.COM',
					normalizedDomain: 'EXAMPLE.COM',
					rootDomain: 'EXAMPLE.COM',
					tld: 'COM',
				},
			} as ComprehensiveDomainData;

			const normalized = searchIndexingService.normalizeDomainData(mockDomainData);

			expect(normalized.domain).toBe('example.com');
			expect(normalized.identification.normalizedDomain).toBe('example.com');
		});
	});

	describe('cleanup', () =>
	{
		beforeEach(async () =>
		{
			await searchIndexingService.initialize();
		});

		it('should cleanup service resources', async () =>
		{
			await searchIndexingService.cleanup();

			expect(mockCacheManager.cleanup).toHaveBeenCalled();
		});
	});
});
