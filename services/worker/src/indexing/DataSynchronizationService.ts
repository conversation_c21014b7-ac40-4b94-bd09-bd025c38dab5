import type { ScyllaClient, ManticoreClient, RedisClientWrapper } from '@shared';
import { logger } from '@shared/utils/Logger';
import type { ComprehensiveDomainData } from '../validation';
import type { BulkIndexResultType } from './ManticoreIndexManager';
import ManticoreIndexManager from './ManticoreIndexManager';

type SyncOperationType = 'full' | 'incremental' | 'domain_specific';

type SyncResultType =
{
	operation: SyncOperationType;
	startTime: string;
	endTime: string;
	duration: number;
	domainsProcessed: number;
	successful: number;
	failed: number;
	errors: Array<{ domain: string; error: string }>;
};

type SyncConfigurationType =
{
	batchSize: number;
	maxConcurrency: number;
	retryAttempts: number;
	retryDelay: number;
	conflictResolution: 'latest_wins' | 'merge' | 'skip';
};

type ConflictResolutionResultType =
{
	action: 'update' | 'skip' | 'merge';
	resolvedData?: ComprehensiveDomainData;
	reason: string;
};

/**
 * Data Synchronization Service
 *
 * Handles synchronization of domain data from ScyllaDB to Manticore search indexes.
 * Supports incremental and full synchronization with conflict resolution and error recovery.
 */
class DataSynchronizationService
{
	private scyllaClient: ScyllaClient;

	private manticoreClient: ManticoreClient;

	private redisClient: RedisClientWrapper;

	private indexManager: ManticoreIndexManager;

	private logger = logger.getLogger('DataSynchronizationService');

	private isRunning = false;

	private syncInterval: NodeJS.Timeout | null = null;

	private readonly config: SyncConfigurationType = {
		batchSize: 100,
		maxConcurrency: 10,
		retryAttempts: 3,
		retryDelay: 1000,
		conflictResolution: 'latest_wins',
	};

	constructor(
		scyllaClient: ScyllaClient,
		manticoreClient: ManticoreClient,
		redisClient: RedisClientWrapper,
	)
	{
		this.scyllaClient = scyllaClient;
		this.manticoreClient = manticoreClient;
		this.redisClient = redisClient;
		this.indexManager = new ManticoreIndexManager(manticoreClient);
	}

	/**
	 * Initialize the synchronization service
	 */
	async initialize(): Promise<void>
	{
		try
		{
			this.logger.info('Initializing data synchronization service...');

			// Initialize index manager
			await this.indexManager.initialize();

			// Load configuration from environment or database
			await this.loadConfiguration();

			this.logger.info('Data synchronization service initialized successfully');
		}
		catch (error)
		{
			this.logger.error('Failed to initialize data synchronization service:', error);
			throw error;
		}
	}

	/**
	 * Start periodic synchronization
	 */
	async startPeriodicSync(intervalMs: number = 300000): Promise<void>
	{
		if (this.isRunning)
		{
			this.logger.warn('Periodic sync is already running');
			return;
		}

		try
		{
			this.logger.info(`Starting periodic synchronization (interval: ${intervalMs}ms)`);

			// Perform initial incremental sync
			await this.performIncrementalSync();

			// Schedule periodic sync
			this.syncInterval = setInterval(async () =>
			{
				try
				{
					await this.performIncrementalSync();
				}
				catch (error)
				{
					this.logger.error('Periodic sync failed:', error);
				}
			}, intervalMs);

			this.isRunning = true;
			this.logger.info('Periodic synchronization started successfully');
		}
		catch (error)
		{
			this.logger.error('Failed to start periodic synchronization:', error);
			throw error;
		}
	}

	/**
	 * Stop periodic synchronization
	 */
	async stopPeriodicSync(): Promise<void>
	{
		if (!this.isRunning)
		{
			return;
		}

		this.logger.info('Stopping periodic synchronization...');

		if (this.syncInterval)
		{
			clearInterval(this.syncInterval);
			this.syncInterval = null;
		}

		this.isRunning = false;
		this.logger.info('Periodic synchronization stopped');
	}

	/**
	 * Perform full synchronization
	 */
	async performFullSync(): Promise<SyncResultType>
	{
		const startTime = new Date();
		this.logger.info('Starting full synchronization...');

		try
		{
			// Clear last sync timestamp to force full sync
			await this.clearLastSyncTimestamp();

			// Get all domains from ScyllaDB
			const domains = await this.getAllDomainsFromScylla();

			// Sync all domains
			const result = await this.syncDomainsBatch(domains, 'full');

			// Update last sync timestamp
			await this.updateLastSyncTimestamp(startTime.toISOString());

			const endTime = new Date();
			const syncResult: SyncResultType = {
				operation: 'full',
				startTime: startTime.toISOString(),
				endTime: endTime.toISOString(),
				duration: endTime.getTime() - startTime.getTime(),
				domainsProcessed: result.totalProcessed,
				successful: result.successful,
				failed: result.failed,
				errors: result.errors.map(e => ({ domain: e.documentId, error: e.error })),
			};

			this.logger.info('Full synchronization completed', {
				duration: syncResult.duration,
				domainsProcessed: syncResult.domainsProcessed,
				successful: syncResult.successful,
				failed: syncResult.failed,
			});

			return syncResult;
		}
		catch (error)
		{
			this.logger.error('Full synchronization failed:', error);
			throw error;
		}
	}

	/**
	 * Perform incremental synchronization
	 */
	async performIncrementalSync(): Promise<SyncResultType>
	{
		const startTime = new Date();
		this.logger.info('Starting incremental synchronization...');

		try
		{
			// Get last sync timestamp
			const lastSyncTime = await this.getLastSyncTimestamp();

			// Get updated domains since last sync
			const domains = await this.getUpdatedDomainsFromScylla(lastSyncTime);

			if (domains.length === 0)
			{
				this.logger.info('No domains to sync - incremental sync completed');

				return {
					operation: 'incremental',
					startTime: startTime.toISOString(),
					endTime: new Date().toISOString(),
					duration: Date.now() - startTime.getTime(),
					domainsProcessed: 0,
					successful: 0,
					failed: 0,
					errors: [],
				};
			}

			// Sync updated domains
			const result = await this.syncDomainsBatch(domains, 'incremental');

			// Update last sync timestamp
			await this.updateLastSyncTimestamp(startTime.toISOString());

			const endTime = new Date();
			const syncResult: SyncResultType = {
				operation: 'incremental',
				startTime: startTime.toISOString(),
				endTime: endTime.toISOString(),
				duration: endTime.getTime() - startTime.getTime(),
				domainsProcessed: result.totalProcessed,
				successful: result.successful,
				failed: result.failed,
				errors: result.errors.map(e => ({ domain: e.documentId, error: e.error })),
			};

			this.logger.info('Incremental synchronization completed', {
				duration: syncResult.duration,
				domainsProcessed: syncResult.domainsProcessed,
				successful: syncResult.successful,
				failed: syncResult.failed,
			});

			return syncResult;
		}
		catch (error)
		{
			this.logger.error('Incremental synchronization failed:', error);
			throw error;
		}
	}

	/**
	 * Sync specific domain
	 */
	async syncDomain(domain: string): Promise<SyncResultType>
	{
		const startTime = new Date();
		this.logger.info(`Starting domain-specific synchronization for: ${domain}`);

		try
		{
			// Get domain data from ScyllaDB
			const domainData = await this.getDomainDataFromScylla(domain);

			if (!domainData)
			{
				throw new Error(`Domain not found in ScyllaDB: ${domain}`);
			}

			// Check for conflicts and resolve
			const conflictResult = await this.resolveConflicts(domainData);

			let successful = 0;
			let failed = 0;
			const errors: Array<{ domain: string; error: string }> = [];

			if (conflictResult.action === 'skip')
			{
				this.logger.info(`Skipping domain sync due to conflict resolution: ${domain}`, {
					reason: conflictResult.reason,
				});
			}
			else
			{
				const dataToSync = conflictResult.resolvedData || domainData;
				const indexResult = await this.indexManager.indexDomain(dataToSync);

				if (indexResult.success)
				{
					successful = 1;
				}
				else
				{
					failed = 1;
					errors.push({ domain, error: indexResult.error || 'Unknown error' });
				}
			}

			const endTime = new Date();
			const syncResult: SyncResultType = {
				operation: 'domain_specific',
				startTime: startTime.toISOString(),
				endTime: endTime.toISOString(),
				duration: endTime.getTime() - startTime.getTime(),
				domainsProcessed: 1,
				successful,
				failed,
				errors,
			};

			this.logger.info(`Domain-specific synchronization completed for: ${domain}`, {
				duration: syncResult.duration,
				successful: syncResult.successful,
				failed: syncResult.failed,
			});

			return syncResult;
		}
		catch (error)
		{
			this.logger.error(`Domain-specific synchronization failed for ${domain}:`, error);
			throw error;
		}
	}

	/**
	 * Sync batch of domains
	 */
	private async syncDomainsBatch(
		domains: ComprehensiveDomainData[],
		operation: SyncOperationType,
	): Promise<BulkIndexResultType>
	{
		this.logger.info(`Syncing batch of ${domains.length} domains with operation: ${operation}`);

		// Process domains in smaller batches to manage memory and performance
		const batches = this.chunkArray(domains, this.config.batchSize);
		const totalResult: BulkIndexResultType = {
			totalProcessed: 0,
			successful: 0,
			failed: 0,
			errors: [],
			processingTime: 0,
		};

		for (const batch of batches)
		{
			try
			{
				// Resolve conflicts for batch based on operation type
				const resolvedBatch = operation === 'full' 
					? await this.resolveBatchConflicts(batch)
					: batch; // For incremental, trust the incoming data

				// Index batch with operation-specific settings
				const batchResult = await this.indexManager.bulkIndexDomains(resolvedBatch);

				// Aggregate results
				totalResult.totalProcessed += batchResult.totalProcessed;
				totalResult.successful += batchResult.successful;
				totalResult.failed += batchResult.failed;
				totalResult.errors.push(...batchResult.errors);
				totalResult.processingTime += batchResult.processingTime;

				// Small delay between batches to prevent overload
				if (batches.indexOf(batch) < batches.length - 1)
				{
					await new Promise(resolve => setTimeout(resolve, 100));
				}
			}
			catch (error)
			{
				this.logger.error('Failed to process batch:', error);

				// Mark entire batch as failed
				totalResult.totalProcessed += batch.length;
				totalResult.failed += batch.length;

				batch.forEach((domain) =>
				{
					totalResult.errors.push({
						documentId: domain.domain,
						error: error instanceof Error ? error.message : String(error),
					});
				});
			}
		}

		return totalResult;
	}

	/**
	 * Resolve conflicts for a batch of domains
	 */
	private async resolveBatchConflicts(domains: ComprehensiveDomainData[]): Promise<ComprehensiveDomainData[]>
	{
		const resolved: ComprehensiveDomainData[] = [];

		for (const domain of domains)
		{
			const conflictResult = await this.resolveConflicts(domain);

			if (conflictResult.action !== 'skip' && conflictResult.resolvedData)
			{
				resolved.push(conflictResult.resolvedData);
			}
		}

		return resolved;
	}

	/**
	 * Resolve conflicts for domain data
	 */
	private async resolveConflicts(domainData: ComprehensiveDomainData): Promise<ConflictResolutionResultType>
	{
		try
		{
			// Check if domain exists in Manticore and get last updated timestamp
			const existingData = await this.getExistingDomainFromManticore(domainData.domain);

			if (!existingData)
			{
				// No conflict - new domain
				return {
					action: 'update',
					resolvedData: domainData,
					reason: 'New domain - no conflict',
				};
			}

			// Compare timestamps
			const existingTimestamp = new Date(existingData.last_updated || 0);
			const newTimestamp = new Date(domainData.lastUpdated || 0);

			switch (this.config.conflictResolution)
			{
				case 'latest_wins':
					if (newTimestamp > existingTimestamp)
					{
						return {
							action: 'update',
							resolvedData: domainData,
							reason: 'Latest data wins - updating',
						};
					}

					return {
						action: 'skip',
						reason: 'Existing data is newer - skipping',
					};


				case 'merge':
					// Implement merge logic - combine data from both sources
					const mergedData = this.mergeDomainData(existingData, domainData);
					return {
						action: 'merge',
						resolvedData: mergedData,
						reason: 'Merged existing and new data',
					};

				case 'skip':
					return {
						action: 'skip',
						reason: 'Conflict resolution set to skip',
					};

				default:
					return {
						action: 'update',
						resolvedData: domainData,
						reason: 'Default action - updating',
					};
			}
		}
		catch (error)
		{
			this.logger.warn(`Failed to resolve conflicts for domain ${domainData.domain}:`, error);

			// Default to update on conflict resolution failure
			return {
				action: 'update',
				resolvedData: domainData,
				reason: 'Conflict resolution failed - defaulting to update',
			};
		}
	}

	/**
	 * Merge domain data from existing and new sources
	 */
	private mergeDomainData(
		existing: Record<string, unknown>,
		newData: ComprehensiveDomainData
	): ComprehensiveDomainData
	{
		// Simple merge strategy - prefer new data but keep existing data for missing fields
		return ({
			...existing,
			...newData,
			lastUpdated: new Date().toISOString(),
		}) as ComprehensiveDomainData;
	}

	/**
	 * Get existing domain data from Manticore
	 */
	private async getExistingDomainFromManticore(
		domain: string
	): Promise<Record<string, unknown> | null>
	{
		try
		{
			// Search for domain in Manticore
			const searchResult = await this.manticoreClient.searchDomains({
				query: domain,
				filters: { domain },
				limit: 1,
			});

			return searchResult.results.length > 0 ? searchResult.results[0] : null;
		}
		catch (error)
		{
			this.logger.warn(`Failed to get existing domain from Manticore: ${domain}`, error);
			return null;
		}
	}

	/**
	 * Get all domains from ScyllaDB
	 */
	private async getAllDomainsFromScylla(): Promise<ComprehensiveDomainData[]>
	{
		try
		{
			this.logger.debug('Fetching all domains from ScyllaDB...');

			// Query domain_analysis table for all domains
			const query = `
				SELECT domain, last_crawled, crawl_status, global_rank, category_rank, category,
					   performance_metrics, security_metrics, seo_metrics, technical_metrics,
					   technologies, server_info, domain_age_days, registration_date,
					   expiration_date, registrar, dns_records, screenshot_urls, subdomains
				FROM domain_analysis
			`;

			const result = await this.scyllaClient.execute(query, []);
			const domains: ComprehensiveDomainData[] = [];

			const mappingPromises = result.rows.map(async (row) =>
			{
				try
				{
					const domainData = await this.mapScyllaRowToDomainData(row);
					return domainData;
				}
				catch (error)
				{
					this.logger.warn(`Failed to map domain data for ${(row as { domain?: string }).domain}:`, error);
					return null;
				}
			});

			const mappedResults = await Promise.all(mappingPromises);
			for (const domainData of mappedResults)
			{
				if (domainData)
				{
					domains.push(domainData);
				}
			}

			this.logger.debug(`Fetched ${domains.length} domains from ScyllaDB`);
			return domains;
		}
		catch (error)
		{
			this.logger.error('Failed to get all domains from ScyllaDB:', error);
			throw error;
		}
	}

	/**
	 * Get updated domains from ScyllaDB since last sync
	 */
	private async getUpdatedDomainsFromScylla(lastSyncTime: string | null): Promise<ComprehensiveDomainData[]>
	{
		try
		{
			this.logger.debug('Fetching updated domains from ScyllaDB...', { lastSyncTime });

			if (!lastSyncTime)
			{
				// No previous sync - get all domains
				return await this.getAllDomainsFromScylla();
			}

			// Query for domains updated since last sync
			const query = `
				SELECT domain, last_crawled, crawl_status, global_rank, category_rank, category,
					   performance_metrics, security_metrics, seo_metrics, technical_metrics,
					   technologies, server_info, domain_age_days, registration_date,
					   expiration_date, registrar, dns_records, screenshot_urls, subdomains
				FROM domain_analysis
				WHERE last_crawled > ?
				ALLOW FILTERING
			`;

			const result = await this.scyllaClient.execute(query, [lastSyncTime]);
			const domains: ComprehensiveDomainData[] = [];

			const mappingPromises = result.rows.map(async (row) =>
			{
				try
				{
					const domainData = await this.mapScyllaRowToDomainData(row);
					return domainData;
				}
				catch (error)
				{
					this.logger.warn(`Failed to map domain data for ${(row as { domain?: string }).domain}:`, error);
					return null;
				}
			});

			const mappedResults = await Promise.all(mappingPromises);
			for (const domainData of mappedResults)
			{
				if (domainData)
				{
					domains.push(domainData);
				}
			}

			this.logger.debug(`Fetched ${domains.length} updated domains from ScyllaDB since ${lastSyncTime}`);
			return domains;
		}
		catch (error)
		{
			this.logger.error('Failed to get updated domains from ScyllaDB:', error);
			throw error;
		}
	}

	/**
	 * Get specific domain data from ScyllaDB
	 */
	private async getDomainDataFromScylla(domain: string): Promise<ComprehensiveDomainData | null>
	{
		try
		{
			this.logger.debug(`Fetching domain data from ScyllaDB: ${domain}`);

			const query = `
				SELECT domain, last_crawled, crawl_status, global_rank, category_rank, category,
					   performance_metrics, security_metrics, seo_metrics, technical_metrics,
					   technologies, server_info, domain_age_days, registration_date,
					   expiration_date, registrar, dns_records, screenshot_urls, subdomains
				FROM domain_analysis
				WHERE domain = ?
			`;

			const result = await this.scyllaClient.execute(query, [domain]);

			if (result.rows.length === 0)
			{
				return null;
			}

			return await this.mapScyllaRowToDomainData(result.rows[0]);
		}
		catch (error)
		{
			this.logger.error(`Failed to get domain data from ScyllaDB: ${domain}`, error);
			throw error;
		}
	}

	/**
	 * Map ScyllaDB row to ComprehensiveDomainData
	 */
	private async mapScyllaRowToDomainData(
		row: Record<string, unknown>,
	): Promise<ComprehensiveDomainData>
	{
		// Extract metrics from Map objects
		const performanceMetrics = row.performance_metrics || new Map();
		const securityMetrics = row.security_metrics || new Map();
		const seoMetrics = row.seo_metrics || new Map();
		const technicalMetrics = row.technical_metrics || new Map();
		const serverInfo = row.server_info || new Map();
		const dnsRecords = row.dns_records || new Map();

		// Convert Map objects to regular objects for easier access
		const performanceData = Object.fromEntries(performanceMetrics);
		const securityData = Object.fromEntries(securityMetrics);
		const seoData = Object.fromEntries(seoMetrics);
		const technicalData = Object.fromEntries(technicalMetrics);
		const serverData = Object.fromEntries(serverInfo);
		const dnsData = Object.fromEntries(dnsRecords);

		// Create comprehensive domain data structure
		const domainData: ComprehensiveDomainData = {
			domain: row.domain,
			lastUpdated: row.last_crawled || new Date().toISOString(),

			// Identification
			identification: {
				domain: row.domain,
				normalizedDomain: row.domain.toLowerCase(),
				rootDomain: this.extractRootDomain(row.domain),
				tld: this.extractTLD(row.domain),
			},

			// Category and classification
			category: row.category || 'uncategorized',

			// Performance data
			performance: {
				score: parseFloat(performanceData.score || '0'),
				loadTime: parseFloat(performanceData.load_time || '0'),
				firstContentfulPaint: parseFloat(performanceData.fcp || '0'),
				largestContentfulPaint: parseFloat(performanceData.lcp || '0'),
				cumulativeLayoutShift: parseFloat(performanceData.cls || '0'),
				firstInputDelay: parseFloat(performanceData.fid || '0'),
				speedIndex: parseFloat(performanceData.speed_index || '0'),
			},

			// Security data
			security: {
				score: parseFloat(securityData.score || '0'),
				sslGrade: securityData.ssl_grade || '',
				securityHeaders: JSON.parse(securityData.security_headers || '{}'),
				vulnerabilities: JSON.parse(securityData.vulnerabilities || '[]'),
				certificateInfo: JSON.parse(securityData.certificate_info || '{}'),
			},

			// SEO data
			seo: {
				score: parseFloat(seoData.score || '0'),
				metaTags: {
					title: seoData.title || '',
					description: seoData.description || '',
					keywords: seoData.keywords || '',
				},
				structuredData: JSON.parse(seoData.structured_data || '[]'),
				sitemap: JSON.parse(seoData.sitemap || '{}'),
				robotsTxt: JSON.parse(seoData.robots_txt || '{}'),
			},

			// Technical data
			technical: {
				score: parseFloat(technicalData.score || '0'),
				technologies: Array.from(row.technologies || []),
				serverInfo: serverData,
				httpHeaders: JSON.parse(technicalData.http_headers || '{}'),
				pageSize: parseInt(technicalData.page_size || '0', 10),
				resourceCount: JSON.parse(technicalData.resource_count || '{}'),
			},

			// Domain information
			domainInfo: {
				age: row.domain_age_days || 0,
				registrationDate: row.registration_date || null,
				expirationDate: row.expiration_date || null,
				registrar: row.registrar || '',
				dnsRecords: dnsData,
			},

			// Ranking information
			ranking: {
				globalRank: row.global_rank || 0,
				categoryRank: row.category_rank || 0,
				overallScore: parseFloat(performanceData.score || '0'), // Use performance score as overall for now
			},

			// Additional data
			screenshots: row.screenshot_urls || [],
			subdomains: Array.from(row.subdomains || []),
			crawlStatus: row.crawl_status || 'pending',

			// Location (extracted from server info)
			location: {
				country: serverData.country || '',
				region: serverData.region || '',
				city: serverData.city || '',
			},

			// Language (extracted from content analysis)
			language: {
				primary: seoData.language || 'en',
				detected: JSON.parse(seoData.detected_languages || '[]'),
			},

			// Content (basic structure)
			content: {
				title: seoData.title || '',
				description: seoData.description || '',
				fullText: '', // Would need to be fetched separately
			},
		};

		return domainData;
	}

	/**
	 * Extract root domain from full domain
	 */
	private extractRootDomain(domain: string): string
	{
		const parts = domain.split('.');
		if (parts.length >= 2)
		{
			return parts.slice(-2).join('.');
		}
		return domain;
	}

	/**
	 * Extract TLD from domain
	 */
	private extractTLD(domain: string): string
	{
		const parts = domain.split('.');
		return parts.length > 1 ? parts[parts.length - 1] : '';
	}

	/**
	 * Load configuration from environment or database
	 */
	private async loadConfiguration(): Promise<void>
	{
		try
		{
			// Load from environment variables
			this.config.batchSize = parseInt(process.env.SYNC_BATCH_SIZE || '100', 10);
			this.config.maxConcurrency = parseInt(process.env.SYNC_MAX_CONCURRENCY || '10', 10);
			this.config.retryAttempts = parseInt(process.env.SYNC_RETRY_ATTEMPTS || '3', 10);
			this.config.retryDelay = parseInt(process.env.SYNC_RETRY_DELAY || '1000', 10);

			const conflictResolution = process.env.SYNC_CONFLICT_RESOLUTION as 'latest_wins' | 'merge' | 'skip';
			if (conflictResolution)
			{
				this.config.conflictResolution = conflictResolution;
			}

			this.logger.debug('Loaded synchronization configuration', this.config);
		}
		catch (error)
		{
			this.logger.warn('Failed to load configuration, using defaults:', error);
		}
	}

	/**
	 * Get last sync timestamp from Redis
	 */
	private async getLastSyncTimestamp(): Promise<string | null>
	{
		try
		{
			return await this.redisClient.get('sync:last_sync_timestamp');
		}
		catch (error)
		{
			this.logger.error('Failed to get last sync timestamp:', error);
			return null;
		}
	}

	/**
	 * Update last sync timestamp in Redis
	 */
	private async updateLastSyncTimestamp(timestamp: string): Promise<void>
	{
		try
		{
			await this.redisClient.set('sync:last_sync_timestamp', timestamp);
		}
		catch (error)
		{
			this.logger.error('Failed to update last sync timestamp:', error);
		}
	}

	/**
	 * Clear last sync timestamp in Redis
	 */
	private async clearLastSyncTimestamp(): Promise<void>
	{
		try
		{
			await this.redisClient.del('sync:last_sync_timestamp');
		}
		catch (error)
		{
			this.logger.error('Failed to clear last sync timestamp:', error);
		}
	}

	/**
	 * Chunk array into smaller batches
	 */
	private chunkArray<T>(array: T[], chunkSize: number): T[][]
	{
		const chunks: T[][] = [];
		for (let i = 0; i < array.length; i += chunkSize)
		{
			chunks.push(array.slice(i, i + chunkSize));
		}
		return chunks;
	}

	/**
	 * Get synchronization statistics
	 */
	async getSyncStatistics(): Promise<{
		lastSyncTime: string | null;
		isRunning: boolean;
		configuration: SyncConfigurationType;
		indexHealth: Map<string, any>;
	}>
	{
		return {
			lastSyncTime: await this.getLastSyncTimestamp(),
			isRunning: this.isRunning,
			configuration: this.config,
			indexHealth: this.indexManager.getHealthStatus(),
		};
	}
}

export type {
	SyncOperationType,
	SyncResultType,
	SyncConfigurationType,
	ConflictResolutionResultType,
};

export default DataSynchronizationService;
