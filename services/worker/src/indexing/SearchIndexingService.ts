import type { ScyllaClient, ManticoreClient, RedisClientWrapper } from '@shared';
import { logger } from '@shared/utils/Logger';
import type { ComprehensiveDomainData } from '../validation';
import type { SyncResultType, SyncOperationType } from './DataSynchronizationService';
import type { InvalidationEvent, InvalidationResult } from './CacheInvalidationManager';
import type { IndexUpdateResultType, BulkIndexResultType } from './ManticoreIndexManager';
import ManticoreIndexManager from './ManticoreIndexManager';
import DataSynchronizationService from './DataSynchronizationService';
import CacheInvalidationManager from './CacheInvalidationManager';
import IndexMaintenanceService from './IndexMaintenanceService';

type IndexingConfigurationType =
{
	syncInterval: number; // milliseconds
	batchSize: number;
	maxConcurrency: number;
	enablePeriodicSync: boolean;
	enableMaintenance: boolean;
	enableCacheInvalidation: boolean;
	conflictResolution: 'latest_wins' | 'merge' | 'skip';
};

type IndexingHealthType =
{
	status: 'healthy' | 'degraded' | 'error';
	components: {
		indexManager: 'healthy' | 'degraded' | 'error';
		syncService: 'healthy' | 'degraded' | 'error';
		cacheManager: 'healthy' | 'degraded' | 'error';
		maintenanceService: 'healthy' | 'degraded' | 'error';
	};
	lastHealthCheck: string;
	issues: string[];
};

type IndexingStatisticsType =
{
	totalDocuments: number;
	indexesCount: number;
	lastSyncTime: string | null;
	syncStatistics: {
		totalSyncs: number;
		successfulSyncs: number;
		failedSyncs: number;
		averageSyncTime: number;
	};
	cacheStatistics: {
		totalInvalidations: number;
		pendingInvalidations: number;
		invalidationsByType: Record<string, number>;
	};
	maintenanceStatistics: {
		totalTasks: number;
		runningTasks: number;
		scheduledTasks: number;
	};
};

/**
 * Search Indexing Service
 *
 * Main orchestrator for all search indexing and data synchronization functionality.
 * Coordinates between index management, data synchronization, cache invalidation,
 * and maintenance services to provide a unified search indexing solution.
 */
class SearchIndexingService
{
	private scyllaClient: ScyllaClient;

	private manticoreClient: ManticoreClient;

	private redisClient: RedisClientWrapper;

	private indexManager: ManticoreIndexManager;

	private syncService: DataSynchronizationService;

	private cacheManager: CacheInvalidationManager;

	private maintenanceService: IndexMaintenanceService;

	private logger = logger.getLogger('SearchIndexingService');

	private isInitialized = false;

	private isRunning = false;

	private readonly config: IndexingConfigurationType = {
		syncInterval: 300000, // 5 minutes
		batchSize: 100,
		maxConcurrency: 10,
		enablePeriodicSync: true,
		enableMaintenance: true,
		enableCacheInvalidation: true,
		conflictResolution: 'latest_wins',
	};

	constructor(
		scyllaClient: ScyllaClient,
		manticoreClient: ManticoreClient,
		redisClient: RedisClientWrapper,
	)
	{
		this.scyllaClient = scyllaClient;
		this.manticoreClient = manticoreClient;
		this.redisClient = redisClient;

		// Initialize component services
		this.indexManager = new ManticoreIndexManager(manticoreClient);
		this.syncService = new DataSynchronizationService(scyllaClient, manticoreClient, redisClient);
		this.cacheManager = new CacheInvalidationManager(redisClient);
		this.maintenanceService = new IndexMaintenanceService(manticoreClient, redisClient);
	}

	/**
	 * Initialize the search indexing service
	 */
	async initialize(): Promise<void>
	{
		if (this.isInitialized)
		{
			this.logger.warn('Search indexing service is already initialized');
			return;
		}

		try
		{
			this.logger.info('Initializing search indexing service...');

			// Load configuration
			await this.loadConfiguration();

			// Initialize all component services
			await this.indexManager.initialize();
			await this.syncService.initialize();
			await this.cacheManager.initialize();
			await this.maintenanceService.initialize();

			this.isInitialized = true;
			this.logger.info('Search indexing service initialized successfully');
		}
		catch (error)
		{
			this.logger.error('Failed to initialize search indexing service:', error);
			throw error;
		}
	}

	/**
	 * Start the search indexing service
	 */
	async start(): Promise<void>
	{
		if (!this.isInitialized)
		{
			throw new Error('Service must be initialized before starting');
		}

		if (this.isRunning)
		{
			this.logger.warn('Search indexing service is already running');
			return;
		}

		try
		{
			this.logger.info('Starting search indexing service...');

			// Start periodic synchronization if enabled
			if (this.config.enablePeriodicSync)
			{
				await this.syncService.startPeriodicSync(this.config.syncInterval);
			}

			// Start maintenance service if enabled
			if (this.config.enableMaintenance)
			{
				await this.maintenanceService.start();
			}

			this.isRunning = true;
			this.logger.info('Search indexing service started successfully');
		}
		catch (error)
		{
			this.logger.error('Failed to start search indexing service:', error);
			throw error;
		}
	}

	/**
	 * Stop the search indexing service
	 */
	async stop(): Promise<void>
	{
		if (!this.isRunning)
		{
			return;
		}

		try
		{
			this.logger.info('Stopping search indexing service...');

			// Stop periodic synchronization
			await this.syncService.stopPeriodicSync();

			// Stop maintenance service
			await this.maintenanceService.stop();

			// Cancel pending cache invalidations
			this.cacheManager.cancelAllPendingInvalidations();

			this.isRunning = false;
			this.logger.info('Search indexing service stopped successfully');
		}
		catch (error)
		{
			this.logger.error('Failed to stop search indexing service:', error);
			throw error;
		}
	}

	/**
	 * Index a single domain
	 */
	async indexDomain(domainData: ComprehensiveDomainData): Promise<IndexUpdateResult>
	{
		try
		{
			this.logger.debug(`Indexing domain: ${domainData.identification?.domain}`);

			// Index the domain
			const result = await this.indexManager.indexDomain(domainData);

			// Trigger cache invalidation if successful and enabled
			if (result.success && this.config.enableCacheInvalidation)
			{
				await this.invalidateDomainCache(domainData.identification?.domain || '');
			}

			return result;
		}
		catch (error)
		{
			this.logger.error(`Failed to index domain ${domainData.identification?.domain}:`, error);
			throw error;
		}
	}

	/**
	 * Bulk index multiple domains
	 */
	async bulkIndexDomains(domainsData: ComprehensiveDomainData[]): Promise<BulkIndexResult>
	{
		try
		{
			this.logger.info(`Bulk indexing ${domainsData.length} domains`);

			// Bulk index domains
			const result = await this.indexManager.bulkIndexDomains(domainsData);

			// Trigger cache invalidation for successful domains if enabled
			if (this.config.enableCacheInvalidation && result.successful > 0)
			{
				await this.invalidateSearchCache();
			}

			return result;
		}
		catch (error)
		{
			this.logger.error('Failed to bulk index domains:', error);
			throw error;
		}
	}

	/**
	 * Delete domain from index
	 */
	async deleteDomain(domain: string): Promise<IndexUpdateResult>
	{
		try
		{
			this.logger.debug(`Deleting domain from index: ${domain}`);

			// Delete from index
			const result = await this.indexManager.deleteDomain(domain);

			// Trigger cache invalidation if successful and enabled
			if (result.success && this.config.enableCacheInvalidation)
			{
				await this.invalidateDomainCache(domain);
			}

			return result;
		}
		catch (error)
		{
			this.logger.error(`Failed to delete domain ${domain} from index:`, error);
			throw error;
		}
	}

	/**
	 * Perform full synchronization
	 */
	async performFullSync(): Promise<SyncResult>
	{
		try
		{
			this.logger.info('Performing full synchronization...');

			const result = await this.syncService.performFullSync();

			// Trigger global cache invalidation if successful and enabled
			if (result.successful > 0 && this.config.enableCacheInvalidation)
			{
				await this.invalidateAllCaches();
			}

			return result;
		}
		catch (error)
		{
			this.logger.error('Failed to perform full synchronization:', error);
			throw error;
		}
	}

	/**
	 * Perform incremental synchronization
	 */
	async performIncrementalSync(): Promise<SyncResult>
	{
		try
		{
			this.logger.info('Performing incremental synchronization...');

			const result = await this.syncService.performIncrementalSync();

			// Trigger cache invalidation if domains were updated and enabled
			if (result.successful > 0 && this.config.enableCacheInvalidation)
			{
				await this.invalidateSearchCache();
			}

			return result;
		}
		catch (error)
		{
			this.logger.error('Failed to perform incremental synchronization:', error);
			throw error;
		}
	}

	/**
	 * Sync specific domain
	 */
	async syncDomain(domain: string): Promise<SyncResult>
	{
		try
		{
			this.logger.info(`Syncing specific domain: ${domain}`);

			const result = await this.syncService.syncDomain(domain);

			// Trigger domain-specific cache invalidation if successful and enabled
			if (result.successful > 0 && this.config.enableCacheInvalidation)
			{
				await this.invalidateDomainCache(domain);
			}

			return result;
		}
		catch (error)
		{
			this.logger.error(`Failed to sync domain ${domain}:`, error);
			throw error;
		}
	}

	/**
	 * Invalidate domain-specific cache
	 */
	async invalidateDomainCache(domain: string): Promise<InvalidationResult>
	{
		const event: InvalidationEvent = {
			type: 'domain_update',
			domain,
			timestamp: new Date().toISOString(),
			triggeredBy: 'SearchIndexingService',
		};

		return await this.cacheManager.triggerInvalidation(event);
	}

	/**
	 * Invalidate ranking cache
	 */
	async invalidateRankingCache(domain?: string, category?: string): Promise<InvalidationResult>
	{
		const event: InvalidationEvent = {
			type: 'ranking_update',
			domain,
			category,
			timestamp: new Date().toISOString(),
			triggeredBy: 'SearchIndexingService',
		};

		return await this.cacheManager.triggerInvalidation(event);
	}

	/**
	 * Invalidate search cache
	 */
	async invalidateSearchCache(): Promise<InvalidationResult>
	{
		const event: InvalidationEvent = {
			type: 'search_index_update',
			timestamp: new Date().toISOString(),
			triggeredBy: 'SearchIndexingService',
		};

		return await this.cacheManager.triggerInvalidation(event);
	}

	/**
	 * Invalidate all caches
	 */
	async invalidateAllCaches(): Promise<InvalidationResult[]>
	{
		const events: InvalidationEvent[] = [
			{
				type: 'domain_update',
				timestamp: new Date().toISOString(),
				triggeredBy: 'SearchIndexingService',
			},
			{
				type: 'ranking_update',
				timestamp: new Date().toISOString(),
				triggeredBy: 'SearchIndexingService',
			},
			{
				type: 'search_index_update',
				timestamp: new Date().toISOString(),
				triggeredBy: 'SearchIndexingService',
			},
		];

		const results = await Promise.all(
			events.map(event => this.cacheManager.triggerInvalidation(event)),
		);

		return results;
	}

	/**
	 * Manual cache invalidation
	 */
	async manualCacheInvalidation(
		patterns: string[],
		metadata?: Record<string, unknown>,
	): Promise<InvalidationResult>
	{
		return await this.cacheManager.manualInvalidation(patterns, metadata);
	}

	/**
	 * Optimize all indexes
	 */
	async optimizeIndexes(): Promise<void>
	{
		await this.indexManager.optimizeAllIndexes();
	}

	/**
	 * Execute maintenance task manually
	 */
	async executeMaintenanceTask(taskName: string): Promise<unknown>
	{
		return await this.maintenanceService.executeTaskManually(taskName);
	}

	/**
	 * Get service health status
	 */
	async getHealthStatus(): Promise<IndexingHealthType>
	{
		try
		{
			const issues: string[] = [];

			// Check index manager health
			const indexHealth = this.indexManager.getHealthStatus();
			let indexManagerStatus: 'healthy' | 'degraded' | 'error' = 'healthy';

			for (const [, status] of indexHealth.entries())
			{
				if (status.status === 'error')
				{
					indexManagerStatus = 'error';
					issues.push(...status.issues);
				}
				else if (status.status === 'degraded' && indexManagerStatus === 'healthy')
				{
					indexManagerStatus = 'degraded';
					issues.push(...status.issues);
				}
			}

			// Check sync service health
			const syncStats = await this.syncService.getSyncStatistics();
			const syncServiceStatus: 'healthy' | 'degraded' | 'error' = 'healthy'; // Simplified

			// Check cache manager health
			const cacheStats = await this.cacheManager.getInvalidationStatistics();
			const cacheManagerStatus: 'healthy' | 'degraded' | 'error' = 'healthy'; // Simplified

			// Check maintenance service health
			const maintenanceStats = await this.maintenanceService.getMaintenanceStatistics();
			const maintenanceServiceStatus: 'healthy' | 'degraded' | 'error' = 'healthy'; // Simplified

			// Determine overall status
			let overallStatus: 'healthy' | 'degraded' | 'error' = 'healthy';
			if (indexManagerStatus === 'error' || !this.isRunning)
			{
				overallStatus = 'error';
			}
			else if (indexManagerStatus === 'degraded')
			{
				overallStatus = 'degraded';
			}

			return {
				status: overallStatus,
				components: {
					indexManager: indexManagerStatus,
					syncService: syncServiceStatus,
					cacheManager: cacheManagerStatus,
					maintenanceService: maintenanceServiceStatus,
				},
				lastHealthCheck: new Date().toISOString(),
				issues,
			};
		}
		catch (error)
		{
			this.logger.error('Failed to get health status:', error);

			return {
				status: 'error',
				components: {
					indexManager: 'error',
					syncService: 'error',
					cacheManager: 'error',
					maintenanceService: 'error',
				},
				lastHealthCheck: new Date().toISOString(),
				issues: [`Health check failed: ${error instanceof Error ? error.message : String(error)}`],
			};
		}
	}

	/**
	 * Get service statistics
	 */
	async getStatistics(): Promise<IndexingStatisticsType>
	{
		try
		{
			const syncStats = await this.syncService.getSyncStatistics();
			const cacheStats = await this.cacheManager.getInvalidationStatistics();
			const maintenanceStats = await this.maintenanceService.getMaintenanceStatistics();
			const indexStats = await this.indexManager.getIndexStatistics();

			// Calculate total documents across all indexes
			let totalDocuments = 0;
			for (const stats of Object.values(indexStats))
			{
				if (typeof stats === 'object' && stats.documentCount)
				{
					totalDocuments += stats.documentCount;
				}
			}

			return {
				totalDocuments,
				indexesCount: Object.keys(indexStats).length,
				lastSyncTime: syncStats.lastSyncTime,
				syncStatistics: {
					totalSyncs: 0, // Would need to track this
					successfulSyncs: 0, // Would need to track this
					failedSyncs: 0, // Would need to track this
					averageSyncTime: 0, // Would need to track this
				},
				cacheStatistics: {
					totalInvalidations: 0, // Not available in current stats
					pendingInvalidations: cacheStats.pendingInvalidations,
					invalidationsByType: {}, // Not available in current stats
				},
				maintenanceStatistics: {
					totalTasks: maintenanceStats.totalTasks,
					runningTasks: maintenanceStats.runningTasks,
					scheduledTasks: maintenanceStats.scheduledTasks,
				},
			};
		}
		catch (error)
		{
			this.logger.error('Failed to get statistics:', error);
			throw error;
		}
	}

	/**
	 * Load configuration from environment variables
	 */
	private async loadConfiguration(): Promise<void>
	{
		try
		{
			// Load from environment variables
			this.config.syncInterval = parseInt(process.env.INDEXING_SYNC_INTERVAL || '300000', 10);
			this.config.batchSize = parseInt(process.env.INDEXING_BATCH_SIZE || '100', 10);
			this.config.maxConcurrency = parseInt(process.env.INDEXING_MAX_CONCURRENCY || '10', 10);
			this.config.enablePeriodicSync = process.env.INDEXING_ENABLE_PERIODIC_SYNC !== 'false';
			this.config.enableMaintenance = process.env.INDEXING_ENABLE_MAINTENANCE !== 'false';
			this.config.enableCacheInvalidation = process.env.INDEXING_ENABLE_CACHE_INVALIDATION !== 'false';

			const conflictResolution = process.env.INDEXING_CONFLICT_RESOLUTION as 'latest_wins' | 'merge' | 'skip';
			if (conflictResolution)
			{
				this.config.conflictResolution = conflictResolution;
			}

			this.logger.debug('Loaded indexing configuration', this.config);
		}
		catch (error)
		{
			this.logger.warn('Failed to load configuration, using defaults:', error);
		}
	}

	/**
	 * Process Manticore sync job (extracted from scheduler service)
	 */
	async processManticoreSyncJob(jobData: { domain: string; syncType: 'full' | 'incremental' }): Promise<void>
	{
		try
		{
			this.logger.info(`Processing Manticore sync job for domain: ${jobData.domain}`, {
				syncType: jobData.syncType,
			});

			if (jobData.syncType === 'full')
			{
				await this.performFullSync();
			}
			else
			{
				await this.syncDomain(jobData.domain);
			}

			this.logger.info(`Manticore sync job completed for domain: ${jobData.domain}`);
		}
		catch (error)
		{
			this.logger.error(`Manticore sync job failed for domain ${jobData.domain}:`, error);
			throw error;
		}
	}

	/**
	 * Invalidate domain-specific caches (extracted from crawler service)
	 */
	async invalidateDomainSpecificCaches(domain: string): Promise<void>
	{
		try
		{
			this.logger.debug(`Invalidating domain-specific caches for: ${domain}`);

			// Invalidate favicon cache
			await this.redisClient.del(`favicon:${domain}`);

			// Invalidate screenshot cache
			await this.redisClient.del(`screenshot:${domain}`);

			// Invalidate performance cache
			await this.redisClient.del(`performance:${domain}`);

			// Invalidate analysis cache
			await this.redisClient.del(`analysis:${domain}`);

			// Invalidate ranking cache
			await this.redisClient.del(`ranking:domain:${domain}`);

			this.logger.debug(`Domain-specific caches invalidated for: ${domain}`);
		}
		catch (error)
		{
			this.logger.error(`Failed to invalidate domain-specific caches for ${domain}:`, error);
		}
	}

	/**
	 * Transform domain data for Manticore indexing (extracted from all services)
	 */
	transformDomainDataForIndex(domainData: ComprehensiveDomainData): Record<string, unknown>
	{
		return ({
			// Basic identification
			domain: domainData.identification?.domain || '',
			title: domainData.seo?.meta?.title?.content || '',
			description: domainData.seo?.meta?.description?.content || '',
			content: domainData.content?.contentAnalysis ? 'content available' : '',

			// Classification
			category: domainData.ranking?.category?.category || 'uncategorized',
			country: domainData.whois?.registrant?.country || '',
			language: domainData.seo?.content?.languageDetection?.primary || 'en',

			// Technology stack
			technologies: domainData.technology?.frontend?.frameworks?.map(
				(fw: { name: string }) => fw.name,
			) || [],

			// Domain metadata
			registrar: domainData.whois?.registrar || '',
			domain_age_days: domainData.whois?.registrationDate
				? Math.floor((Date.now() - new Date(domainData.whois.registrationDate).getTime())
					/ (1000 * 60 * 60 * 24)) : 0,

			// Ranking and scoring
			global_rank: domainData.ranking?.global?.rank || 0,
			overall_score: domainData.ranking?.scores?.overall || 0,
			performance_score: domainData.ranking?.scores?.performance || 0,
			security_score: domainData.ranking?.scores?.security || 0,
			seo_score: domainData.ranking?.scores?.seo || 0,
			technical_score: domainData.ranking?.scores?.technical || 0,
			backlink_score: domainData.ranking?.scores?.content || 0,

			// Traffic and engagement
			traffic_estimate: 0, // Not available in new schema

			// Security and quality indicators
			ssl_grade: domainData.security?.ssl?.grade || '',
			mobile_friendly_score: domainData.performance?.mobile?.score || 0,
			accessibility_score: domainData.visual?.design?.layout?.accessibility || 0,

			// Timestamps
			last_updated: new Date().toISOString(),
		});
	}

	/**
	 * Normalize domain data for consistent indexing
	 */
	normalizeDomainData(domainData: ComprehensiveDomainData): ComprehensiveDomainData
	{
		// Normalize domain name
		const normalizedDomain = (domainData.identification?.domain || '').toLowerCase().trim();

		// Ensure consistent data structure
		return ({
			...domainData,
			identification: {
				...domainData.identification,
				domain: normalizedDomain,
				normalizedDomain,
			},
		});
	}

	/**
	 * Get current configuration
	 */
	getConfiguration(): IndexingConfigurationType
	{
		return ({ ...this.config });
	}

	/**
	 * Update configuration
	 */
	async updateConfiguration(newConfig: Partial<IndexingConfigurationType>): Promise<void>
	{
		Object.assign(this.config, newConfig);
		this.logger.info('Updated indexing configuration', newConfig);

		// Apply configuration changes if service is running
		if (this.isRunning)
		{
			// Restart periodic sync if interval changed
			if (newConfig.syncInterval !== undefined)
			{
				await this.syncService.stopPeriodicSync();
				if (this.config.enablePeriodicSync)
				{
					await this.syncService.startPeriodicSync(this.config.syncInterval);
				}
			}
		}
	}

	/**
	 * Cleanup service resources
	 */
	async cleanup(): Promise<void>
	{
		try
		{
			await this.stop();
			await this.cacheManager.cleanup();
			this.logger.info('Search indexing service cleaned up');
		}
		catch (error)
		{
			this.logger.error('Failed to cleanup search indexing service:', error);
		}
	}
}

export type {
	IndexingConfigurationType,
	IndexingHealthType,
	IndexingStatisticsType,
};

export default SearchIndexingService;
