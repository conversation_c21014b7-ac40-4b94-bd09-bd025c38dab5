import type { ManticoreClient, RedisClient } from '@shared';
import { logger } from '@shared/utils/Logger';
import ManticoreIndexManager from './ManticoreIndexManager';
import type { IndexHealthStatus } from './ManticoreIndexManager';

type MaintenanceTask = {
	name: string;
	type: 'optimization' | 'cleanup' | 'health_check' | 'capacity_planning';
	schedule: string; // Cron expression
	enabled: boolean;
	lastRun?: string;
	nextRun?: string;
	duration?: number;
	status: 'idle' | 'running' | 'completed' | 'failed';
};

type MaintenanceResult = {
	taskName: string;
	success: boolean;
	startTime: string;
	endTime: string;
	duration: number;
	details: Record<string, any>;
	errors: string[];
};

type CapacityMetrics = {
	indexName: string;
	documentCount: number;
	indexSize: number; // in bytes
	memoryUsage: number; // in bytes
	diskUsage: number; // in bytes
	queryPerformance: {
		averageResponseTime: number;
		queriesPerSecond: number;
		slowQueries: number;
	};
	recommendations: string[];
};

type PerformanceOptimization = {
	indexName: string;
	currentSettings: Record<string, any>;
	recommendedSettings: Record<string, any>;
	expectedImprovement: string;
	priority: 'low' | 'medium' | 'high';
};

/**
 * Index Maintenance Service
 *
 * Handles automated maintenance tasks for Manticore search indexes including:
 * - Scheduled optimization and cleanup
 * - Performance monitoring and tuning
 * - Capacity planning and scaling recommendations
 * - Health monitoring and alerting
 */
class IndexMaintenanceService
{
	private manticoreClient: ManticoreClient;

	private redisClient: RedisClient;

	private indexManager: ManticoreIndexManager;

	private logger = logger.getLogger('IndexMaintenanceService');

	private maintenanceTasks: Map<string, MaintenanceTask> = new Map();

	private scheduledJobs: Map<string, NodeJS.Timeout> = new Map();

	private isRunning = false;

	private performanceHistory: Map<string, any[]> = new Map();

	constructor(manticoreClient: ManticoreClient, redisClient: RedisClient)
	{
		this.manticoreClient = manticoreClient;
		this.redisClient = redisClient;
		this.indexManager = new ManticoreIndexManager(manticoreClient);
	}

	/**
	 * Initialize the maintenance service
	 */
	async initialize(): Promise<void>
	{
		try
		{
			this.logger.info('Initializing index maintenance service...');

			// Initialize index manager
			await this.indexManager.initialize();

			// Setup default maintenance tasks
			this.setupDefaultTasks();

			// Load custom tasks from configuration
			await this.loadCustomTasks();

			// Load performance history
			await this.loadPerformanceHistory();

			this.logger.info('Index maintenance service initialized successfully');
		}
		catch (error)
		{
			this.logger.error('Failed to initialize index maintenance service:', error);
			throw error;
		}
	}

	/**
	 * Start the maintenance service
	 */
	async start(): Promise<void>
	{
		if (this.isRunning)
		{
			this.logger.warn('Maintenance service is already running');
			return;
		}

		try
		{
			this.logger.info('Starting index maintenance service...');

			// Schedule all enabled tasks
			for (const task of this.maintenanceTasks.values())
			{
				if (task.enabled)
				{
					this.scheduleTask(task);
				}
			}

			this.isRunning = true;
			this.logger.info('Index maintenance service started successfully');
		}
		catch (error)
		{
			this.logger.error('Failed to start index maintenance service:', error);
			throw error;
		}
	}

	/**
	 * Stop the maintenance service
	 */
	async stop(): Promise<void>
	{
		if (!this.isRunning)
		{
			return;
		}

		this.logger.info('Stopping index maintenance service...');

		// Cancel all scheduled jobs
		for (const [taskName, timeout] of this.scheduledJobs.entries())
		{
			clearTimeout(timeout);
			this.logger.debug(`Cancelled scheduled task: ${taskName}`);
		}

		this.scheduledJobs.clear();
		this.isRunning = false;

		this.logger.info('Index maintenance service stopped');
	}

	/**
	 * Setup default maintenance tasks
	 */
	private setupDefaultTasks(): void
	{
		// Daily index optimization
		this.addMaintenanceTask({
			name: 'daily_optimization',
			type: 'optimization',
			schedule: '0 2 * * *', // 2 AM daily
			enabled: true,
			status: 'idle',
		});

		// Weekly cleanup
		this.addMaintenanceTask({
			name: 'weekly_cleanup',
			type: 'cleanup',
			schedule: '0 3 * * 0', // 3 AM on Sundays
			enabled: true,
			status: 'idle',
		});

		// Hourly health check
		this.addMaintenanceTask({
			name: 'hourly_health_check',
			type: 'health_check',
			schedule: '0 * * * *', // Every hour
			enabled: true,
			status: 'idle',
		});

		// Daily capacity planning
		this.addMaintenanceTask({
			name: 'daily_capacity_planning',
			type: 'capacity_planning',
			schedule: '0 4 * * *', // 4 AM daily
			enabled: true,
			status: 'idle',
		});
	}

	/**
	 * Add maintenance task
	 */
	addMaintenanceTask(task: MaintenanceTask): void
	{
		this.maintenanceTasks.set(task.name, task);

		if (this.isRunning && task.enabled)
		{
			this.scheduleTask(task);
		}

		this.logger.debug(`Added maintenance task: ${task.name}`, {
			type: task.type,
			schedule: task.schedule,
			enabled: task.enabled,
		});
	}

	/**
	 * Schedule a maintenance task
	 */
	private scheduleTask(task: MaintenanceTask): void
	{
		try
		{
			// Parse cron expression and calculate next run time
			const nextRun = this.calculateNextRun(task.schedule);
			const delay = nextRun.getTime() - Date.now();

			if (delay > 0)
			{
				const timeout = setTimeout(async () =>
				{
					await this.executeTask(task);

					// Reschedule for next run
					if (this.isRunning && task.enabled)
					{
						this.scheduleTask(task);
					}
				}, delay);

				this.scheduledJobs.set(task.name, timeout);

				task.nextRun = nextRun.toISOString();
				this.logger.debug(`Scheduled task ${task.name} for ${nextRun.toISOString()}`);
			}
		}
		catch (error)
		{
			this.logger.error(`Failed to schedule task ${task.name}:`, error);
		}
	}

	/**
	 * Execute a maintenance task
	 */
	private async executeTask(task: MaintenanceTask): Promise<MaintenanceResult>
	{
		const startTime = new Date();
		task.status = 'running';
		task.lastRun = startTime.toISOString();

		this.logger.info(`Executing maintenance task: ${task.name}`);

		try
		{
			let result: MaintenanceResult;

			switch (task.type)
			{
				case 'optimization':
					result = await this.executeOptimizationTask(task);
					break;
				case 'cleanup':
					result = await this.executeCleanupTask(task);
					break;
				case 'health_check':
					result = await this.executeHealthCheckTask(task);
					break;
				case 'capacity_planning':
					result = await this.executeCapacityPlanningTask(task);
					break;
				default:
					throw new Error(`Unknown task type: ${task.type}`);
			}

			task.status = result.success ? 'completed' : 'failed';
			task.duration = result.duration;

			// Store result in Redis for history
			await this.storeTaskResult(result);

			this.logger.info(`Maintenance task completed: ${task.name}`, {
				success: result.success,
				duration: result.duration,
				errors: result.errors.length,
			});

			return result;
		}
		catch (error)
		{
			const endTime = new Date();
			const errorMessage = error instanceof Error ? error.message : String(error);

			task.status = 'failed';
			task.duration = endTime.getTime() - startTime.getTime();

			const result: MaintenanceResult = {
				taskName: task.name,
				success: false,
				startTime: startTime.toISOString(),
				endTime: endTime.toISOString(),
				duration: task.duration,
				details: {},
				errors: [errorMessage],
			};

			await this.storeTaskResult(result);

			this.logger.error(`Maintenance task failed: ${task.name}`, error);
			return result;
		}
	}

	/**
	 * Execute optimization task
	 */
	private async executeOptimizationTask(task: MaintenanceTask): Promise<MaintenanceResult>
	{
		const startTime = new Date();
		const details: Record<string, any> = {};
		const errors: string[] = [];

		try
		{
			// Optimize all indexes
			await this.indexManager.optimizeAllIndexes();
			details.optimizedIndexes = Object.keys(await this.indexManager.getIndexStatistics());

			// Apply performance optimizations
			const optimizations = await this.generatePerformanceOptimizations();
			details.optimizations = optimizations;

			for (const optimization of optimizations)
			{
				if (optimization.priority === 'high')
				{
					try
					{
						await this.applyOptimization(optimization);
						details.appliedOptimizations = details.appliedOptimizations || [];
						details.appliedOptimizations.push(optimization.indexName);
					}
					catch (error)
					{
						errors.push(`Failed to apply optimization for ${optimization.indexName}: ${error}`);
					}
				}
			}

			const endTime = new Date();
			return {
				taskName: task.name,
				success: errors.length === 0,
				startTime: startTime.toISOString(),
				endTime: endTime.toISOString(),
				duration: endTime.getTime() - startTime.getTime(),
				details,
				errors,
			};
		}
		catch (error)
		{
			const endTime = new Date();
			return {
				taskName: task.name,
				success: false,
				startTime: startTime.toISOString(),
				endTime: endTime.toISOString(),
				duration: endTime.getTime() - startTime.getTime(),
				details,
				errors: [error instanceof Error ? error.message : String(error)],
			};
		}
	}

	/**
	 * Execute cleanup task
	 */
	private async executeCleanupTask(task: MaintenanceTask): Promise<MaintenanceResult>
	{
		const startTime = new Date();
		const details: Record<string, any> = {};
		const errors: string[] = [];

		try
		{
			// Clean up old performance history
			const cleanedHistoryEntries = await this.cleanupPerformanceHistory();
			details.cleanedHistoryEntries = cleanedHistoryEntries;

			// Clean up old task results
			const cleanedTaskResults = await this.cleanupOldTaskResults();
			details.cleanedTaskResults = cleanedTaskResults;

			// Clean up temporary cache entries
			const cleanedCacheEntries = await this.cleanupTemporaryCache();
			details.cleanedCacheEntries = cleanedCacheEntries;

			const endTime = new Date();
			return {
				taskName: task.name,
				success: true,
				startTime: startTime.toISOString(),
				endTime: endTime.toISOString(),
				duration: endTime.getTime() - startTime.getTime(),
				details,
				errors,
			};
		}
		catch (error)
		{
			const endTime = new Date();
			return {
				taskName: task.name,
				success: false,
				startTime: startTime.toISOString(),
				endTime: endTime.toISOString(),
				duration: endTime.getTime() - startTime.getTime(),
				details,
				errors: [error instanceof Error ? error.message : String(error)],
			};
		}
	}

	/**
	 * Execute health check task
	 */
	private async executeHealthCheckTask(task: MaintenanceTask): Promise<MaintenanceResult>
	{
		const startTime = new Date();
		const details: Record<string, any> = {};
		const errors: string[] = [];

		try
		{
			// Perform comprehensive health check
			const healthStatus = await this.indexManager.performHealthCheck();
			details.healthStatus = Object.fromEntries(healthStatus);

			// Check for issues and generate alerts
			const issues: string[] = [];
			for (const [indexName, status] of healthStatus.entries())
			{
				if (status.status === 'error' || status.status === 'degraded')
				{
					issues.push(`${indexName}: ${status.issues.join(', ')}`);
				}
			}

			details.issues = issues;

			// Store performance metrics
			await this.recordPerformanceMetrics();

			const endTime = new Date();
			return {
				taskName: task.name,
				success: issues.length === 0,
				startTime: startTime.toISOString(),
				endTime: endTime.toISOString(),
				duration: endTime.getTime() - startTime.getTime(),
				details,
				errors: issues,
			};
		}
		catch (error)
		{
			const endTime = new Date();
			return {
				taskName: task.name,
				success: false,
				startTime: startTime.toISOString(),
				endTime: endTime.toISOString(),
				duration: endTime.getTime() - startTime.getTime(),
				details,
				errors: [error instanceof Error ? error.message : String(error)],
			};
		}
	}

	/**
	 * Execute capacity planning task
	 */
	private async executeCapacityPlanningTask(task: MaintenanceTask): Promise<MaintenanceResult>
	{
		const startTime = new Date();
		const details: Record<string, any> = {};
		const errors: string[] = [];

		try
		{
			// Generate capacity metrics for all indexes
			const capacityMetrics = await this.generateCapacityMetrics();
			details.capacityMetrics = capacityMetrics;

			// Generate scaling recommendations
			const recommendations = this.generateScalingRecommendations(capacityMetrics);
			details.recommendations = recommendations;

			// Store capacity planning data
			await this.storeCapacityPlanningData(capacityMetrics, recommendations);

			const endTime = new Date();
			return {
				taskName: task.name,
				success: true,
				startTime: startTime.toISOString(),
				endTime: endTime.toISOString(),
				duration: endTime.getTime() - startTime.getTime(),
				details,
				errors,
			};
		}
		catch (error)
		{
			const endTime = new Date();
			return {
				taskName: task.name,
				success: false,
				startTime: startTime.toISOString(),
				endTime: endTime.toISOString(),
				duration: endTime.getTime() - startTime.getTime(),
				details,
				errors: [error instanceof Error ? error.message : String(error)],
			};
		}
	}

	/**
	 * Generate performance optimizations
	 */
	private async generatePerformanceOptimizations(): Promise<PerformanceOptimization[]>
	{
		const optimizations: PerformanceOptimization[] = [];
		const indexStats = await this.indexManager.getIndexStatistics();

		for (const [indexName, stats] of Object.entries(indexStats))
		{
			// Analyze performance and generate recommendations
			const optimization: PerformanceOptimization = {
				indexName,
				currentSettings: stats,
				recommendedSettings: {},
				expectedImprovement: '',
				priority: 'low',
			};

			// Example optimization logic
			if (stats.documentCount > 100000)
			{
				optimization.recommendedSettings.rt_mem_limit = '1G';
				optimization.expectedImprovement = 'Improved memory usage for large index';
				optimization.priority = 'medium';
			}

			if (stats.queryPerformance?.averageResponseTime > 1000)
			{
				optimization.recommendedSettings.max_matches = '5000';
				optimization.expectedImprovement = 'Reduced query response time';
				optimization.priority = 'high';
			}

			optimizations.push(optimization);
		}

		return optimizations;
	}

	/**
	 * Apply performance optimization
	 */
	private async applyOptimization(optimization: PerformanceOptimization): Promise<void>
	{
		this.logger.info(`Applying optimization for index: ${optimization.indexName}`, {
			settings: optimization.recommendedSettings,
		});

		// Apply settings to index
		for (const [setting, value] of Object.entries(optimization.recommendedSettings))
		{
			// This would need to be implemented based on Manticore's ALTER TABLE syntax
			// await this.manticoreClient.executeRaw(`ALTER TABLE ${optimization.indexName} ${setting}='${value}'`);
		}
	}

	/**
	 * Generate capacity metrics
	 */
	private async generateCapacityMetrics(): Promise<CapacityMetrics[]>
	{
		const metrics: CapacityMetrics[] = [];
		const indexStats = await this.indexManager.getIndexStatistics();

		for (const [indexName, stats] of Object.entries(indexStats))
		{
			const metric: CapacityMetrics = {
				indexName,
				documentCount: stats.documentCount || 0,
				indexSize: stats.indexSize || 0,
				memoryUsage: stats.memoryUsage || 0,
				diskUsage: stats.diskUsage || 0,
				queryPerformance: {
					averageResponseTime: stats.averageResponseTime || 0,
					queriesPerSecond: stats.queriesPerSecond || 0,
					slowQueries: stats.slowQueries || 0,
				},
				recommendations: [],
			};

			// Generate recommendations based on metrics
			if (metric.documentCount > 500000)
			{
				metric.recommendations.push('Consider index partitioning for better performance');
			}

			if (metric.queryPerformance.averageResponseTime > 2000)
			{
				metric.recommendations.push('Query performance is degraded - consider optimization');
			}

			if (metric.memoryUsage > 1024 * 1024 * 1024) // 1GB
			{
				metric.recommendations.push('High memory usage - consider increasing rt_mem_limit');
			}

			metrics.push(metric);
		}

		return metrics;
	}

	/**
	 * Generate scaling recommendations
	 */
	private generateScalingRecommendations(metrics: CapacityMetrics[]): string[]
	{
		const recommendations: string[] = [];

		const totalDocuments = metrics.reduce((sum, m) => sum + m.documentCount, 0);
		const totalMemory = metrics.reduce((sum, m) => sum + m.memoryUsage, 0);

		if (totalDocuments > 1000000)
		{
			recommendations.push('Consider horizontal scaling with multiple Manticore instances');
		}

		if (totalMemory > 2 * 1024 * 1024 * 1024) // 2GB
		{
			recommendations.push('Consider increasing server memory allocation');
		}

		const avgResponseTime = metrics.reduce((sum, m) => sum + m.queryPerformance.averageResponseTime, 0) / metrics.length;
		if (avgResponseTime > 1500)
		{
			recommendations.push('Query performance is degraded - consider load balancing');
		}

		return recommendations;
	}

	/**
	 * Record performance metrics
	 */
	private async recordPerformanceMetrics(): Promise<void>
	{
		const timestamp = new Date().toISOString();
		const indexStats = await this.indexManager.getIndexStatistics();

		for (const [indexName, stats] of Object.entries(indexStats))
		{
			const metric = {
				timestamp,
				...stats,
			};

			// Add to in-memory history
			if (!this.performanceHistory.has(indexName))
			{
				this.performanceHistory.set(indexName, []);
			}

			const history = this.performanceHistory.get(indexName)!;
			history.push(metric);

			// Keep only last 100 entries
			if (history.length > 100)
			{
				history.splice(0, history.length - 100);
			}

			// Store in Redis
			await this.redisClient.lpush(`performance:${indexName}`, JSON.stringify(metric));
			await this.redisClient.ltrim(`performance:${indexName}`, 0, 99);
		}
	}

	/**
	 * Load performance history from Redis
	 */
	private async loadPerformanceHistory(): Promise<void>
	{
		try
		{
			const indexNames = Object.keys(await this.indexManager.getIndexStatistics());

			for (const indexName of indexNames)
			{
				const historyJson = await this.redisClient.lrange(`performance:${indexName}`, 0, 99);
				const history = historyJson.map(json => JSON.parse(json));
				this.performanceHistory.set(indexName, history);
			}

			this.logger.debug('Loaded performance history from Redis');
		}
		catch (error)
		{
			this.logger.warn('Failed to load performance history:', error);
		}
	}

	/**
	 * Load custom tasks from configuration
	 */
	private async loadCustomTasks(): Promise<void>
	{
		try
		{
			const tasksKey = 'maintenance:tasks';
			const customTasks = await this.redisClient.get<MaintenanceTask[]>(tasksKey);

			if (customTasks && Array.isArray(customTasks))
			{
				customTasks.forEach(task => this.addMaintenanceTask(task));
				this.logger.debug(`Loaded ${customTasks.length} custom maintenance tasks`);
			}
		}
		catch (error)
		{
			this.logger.warn('Failed to load custom maintenance tasks:', error);
		}
	}

	/**
	 * Store task result in Redis
	 */
	private async storeTaskResult(result: MaintenanceResult): Promise<void>
	{
		try
		{
			const resultKey = `maintenance:result:${result.taskName}:${Date.now()}`;
			await this.redisClient.set(resultKey, result, 86400 * 7); // 7 days TTL

			// Add to history list
			await this.redisClient.lpush(`maintenance:history:${result.taskName}`, JSON.stringify(result));
			await this.redisClient.ltrim(`maintenance:history:${result.taskName}`, 0, 49); // Keep last 50
		}
		catch (error)
		{
			this.logger.error('Failed to store task result:', error);
		}
	}

	/**
	 * Store capacity planning data
	 */
	private async storeCapacityPlanningData(
		metrics: CapacityMetrics[],
		recommendations: string[],
	): Promise<void>
	{
		try
		{
			const data = {
				timestamp: new Date().toISOString(),
				metrics,
				recommendations,
			};

			await this.redisClient.set('capacity:latest', data, 86400); // 24 hours TTL
			await this.redisClient.lpush('capacity:history', JSON.stringify(data));
			await this.redisClient.ltrim('capacity:history', 0, 29); // Keep last 30
		}
		catch (error)
		{
			this.logger.error('Failed to store capacity planning data:', error);
		}
	}

	/**
	 * Cleanup old performance history
	 */
	private async cleanupPerformanceHistory(): Promise<number>
	{
		let cleaned = 0;

		for (const indexName of this.performanceHistory.keys())
		{
			try
			{
				// Keep only last 50 entries in Redis
				await this.redisClient.ltrim(`performance:${indexName}`, 0, 49);
				cleaned++;
			}
			catch (error)
			{
				this.logger.warn(`Failed to cleanup performance history for ${indexName}:`, error);
			}
		}

		return cleaned;
	}

	/**
	 * Cleanup old task results
	 */
	private async cleanupOldTaskResults(): Promise<number>
	{
		let cleaned = 0;

		try
		{
			const resultKeys = await this.redisClient.keys('maintenance:result:*');
			const now = Date.now();
			const weekAgo = now - (7 * 24 * 60 * 60 * 1000);

			for (const key of resultKeys)
			{
				const timestamp = parseInt(key.split(':').pop() || '0', 10);
				if (timestamp < weekAgo)
				{
					await this.redisClient.del(key);
					cleaned++;
				}
			}
		}
		catch (error)
		{
			this.logger.warn('Failed to cleanup old task results:', error);
		}

		return cleaned;
	}

	/**
	 * Cleanup temporary cache entries
	 */
	private async cleanupTemporaryCache(): Promise<number>
	{
		let cleaned = 0;

		try
		{
			const tempKeys = await this.redisClient.keys('temp:*');

			for (const key of tempKeys)
			{
				const ttl = await this.redisClient.ttl(key);
				if (ttl === -1) // No TTL set
				{
					await this.redisClient.del(key);
					cleaned++;
				}
			}
		}
		catch (error)
		{
			this.logger.warn('Failed to cleanup temporary cache:', error);
		}

		return cleaned;
	}

	/**
	 * Calculate next run time from cron expression
	 */
	private calculateNextRun(cronExpression: string): Date
	{
		// Simple cron parser - in production, use a proper cron library
		// For now, return next hour as placeholder
		const nextRun = new Date();
		nextRun.setHours(nextRun.getHours() + 1, 0, 0, 0);
		return nextRun;
	}

	/**
	 * Get maintenance statistics
	 */
	async getMaintenanceStatistics(): Promise<{
		totalTasks: number;
		runningTasks: number;
		scheduledTasks: number;
		recentResults: MaintenanceResult[];
		performanceMetrics: Record<string, any[]>;
	}>
	{
		const runningTasks = Array.from(this.maintenanceTasks.values())
			.filter(task => task.status === 'running').length;

		const scheduledTasks = this.scheduledJobs.size;

		// Get recent results from Redis
		const recentResults: MaintenanceResult[] = [];
		try
		{
			for (const taskName of this.maintenanceTasks.keys())
			{
				const historyJson = await this.redisClient.lrange(`maintenance:history:${taskName}`, 0, 4);
				const taskResults = historyJson.map(json => JSON.parse(json));
				recentResults.push(...taskResults);
			}
		}
		catch (error)
		{
			this.logger.warn('Failed to get recent results:', error);
		}

		return {
			totalTasks: this.maintenanceTasks.size,
			runningTasks,
			scheduledTasks,
			recentResults: recentResults.slice(0, 10),
			performanceMetrics: Object.fromEntries(this.performanceHistory),
		};
	}

	/**
	 * Manual task execution
	 */
	async executeTaskManually(taskName: string): Promise<MaintenanceResult>
	{
		const task = this.maintenanceTasks.get(taskName);
		if (!task)
		{
			throw new Error(`Task not found: ${taskName}`);
		}

		return await this.executeTask(task);
	}
}

export type {
	MaintenanceTask,
	MaintenanceResult,
	CapacityMetrics,
	PerformanceOptimization,
};

export default IndexMaintenanceService;
