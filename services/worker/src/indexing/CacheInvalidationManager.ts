import type { RedisClient } from '@shared';
import { logger } from '@shared/utils/Logger';

type InvalidationRule = {
	name: string;
	trigger: 'domain_update' | 'ranking_update' | 'search_index_update' | 'manual';
	patterns: string[];
	delay?: number; // Delay in milliseconds before invalidation
	dependencies?: string[]; // Other cache keys that depend on this data
	priority: 'low' | 'medium' | 'high';
};

type InvalidationEvent = {
	type: 'domain_update' | 'ranking_update' | 'search_index_update' | 'manual';
	domain?: string;
	category?: string;
	timestamp: string;
	metadata?: Record<string, unknown>;
	triggeredBy?: string; // Service or process that triggered the event
};

type InvalidationResult = {
	success: boolean;
	patternsInvalidated: string[];
	dependenciesInvalidated: string[];
	errors: Array<{ pattern: string; error: string }>;
	processingTime: number;
};

type CacheDependency = {
	key: string;
	dependsOn: string[];
	lastUpdated: string;
	ttl: number;
};

/**
 * Cache Invalidation Manager
 *
 * Manages intelligent cache invalidation across Redis and application caches with:
 * - Rule-based invalidation patterns
 * - Dependency tracking and cascading invalidation
 * - Delayed invalidation for batch operations
 * - Performance monitoring and analytics
 */
class CacheInvalidationManager
{
	private redisClient: RedisClient;

	private logger = logger.getLogger('CacheInvalidationManager');

	private invalidationRules: Map<string, InvalidationRule> = new Map();

	private pendingInvalidations: Map<string, NodeJS.Timeout> = new Map();

	private dependencyGraph: Map<string, CacheDependency> = new Map();

	private invalidationHistory: InvalidationEvent[] = [];

	private readonly MAX_HISTORY_SIZE = 1000;

	constructor(redisClient: RedisClient)
	{
		this.redisClient = redisClient;
		this.setupDefaultRules();
	}

	/**
	 * Initialize the cache invalidation manager
	 */
	async initialize(): Promise<void>
	{
		try
		{
			this.logger.info('Initializing cache invalidation manager...');

			// Load dependency graph from Redis
			await this.loadDependencyGraph();

			// Load invalidation rules from configuration
			await this.loadInvalidationRules();

			// Setup cleanup interval for expired dependencies
			this.setupCleanupInterval();

			this.logger.info('Cache invalidation manager initialized successfully');
		}
		catch (error)
		{
			this.logger.error('Failed to initialize cache invalidation manager:', error);
			throw error;
		}
	}

	/**
	 * Setup default invalidation rules
	 */
	private setupDefaultRules(): void
	{
		// Domain analysis update invalidation
		this.addInvalidationRule({
			name: 'domain_analysis_update',
			trigger: 'domain_update',
			patterns: [
				'analysis:{domain}',
				'domain:{domain}:*',
				'ranking:domain:{domain}',
				'search:results:*{domain}*',
				'favicon:{domain}',
				'screenshot:{domain}',
				'performance:{domain}',
			],
			dependencies: [
				'rankings:global:*',
				'top:domains:*',
			],
			delay: 0, // Immediate invalidation
			priority: 'high',
		});

		// Domain ranking update invalidation
		this.addInvalidationRule({
			name: 'domain_ranking_update',
			trigger: 'ranking_update',
			patterns: [
				'ranking:domain:{domain}',
				'rankings:global:*',
				'rankings:category:{category}:*',
				'top:domains:*',
			],
			dependencies: [
				'search:results:*',
				'analytics:rankings:*',
			],
			delay: 5000, // 5 second delay to allow for batch updates
			priority: 'high',
		});

		// Search index update invalidation
		this.addInvalidationRule({
			name: 'search_index_update',
			trigger: 'search_index_update',
			patterns: [
				'search:results:*',
				'search:facets:*',
				'top:domains:*',
				'domainAnalysis:*',
			],
			dependencies: [
				'analytics:search:*',
			],
			delay: 10000, // 10 second delay for search index updates
			priority: 'medium',
		});

		// Category-specific invalidation
		this.addInvalidationRule({
			name: 'category_update',
			trigger: 'ranking_update',
			patterns: [
				'rankings:category:{category}:*',
				'top:domains:category:{category}:*',
				'analytics:category:{category}:*',
			],
			dependencies: [
				'search:results:*category*',
			],
			delay: 2000,
			priority: 'medium',
		});

		// Technology stack update invalidation
		this.addInvalidationRule({
			name: 'technology_update',
			trigger: 'domain_update',
			patterns: [
				'technology:*',
				'domains:technology:*',
				'search:results:*technology*',
			],
			dependencies: [
				'analytics:technology:*',
			],
			delay: 1000,
			priority: 'low',
		});

		// Performance metrics update invalidation
		this.addInvalidationRule({
			name: 'performance_update',
			trigger: 'domain_update',
			patterns: [
				'performance:{domain}',
				'performance:category:*',
				'performance:global:*',
			],
			dependencies: [
				'rankings:performance:*',
			],
			delay: 0,
			priority: 'medium',
		});

		// Security metrics update invalidation
		this.addInvalidationRule({
			name: 'security_update',
			trigger: 'domain_update',
			patterns: [
				'security:{domain}',
				'security:ssl:*',
				'security:vulnerabilities:*',
			],
			dependencies: [
				'rankings:security:*',
			],
			delay: 0,
			priority: 'high',
		});
	}

	/**
	 * Add custom invalidation rule
	 */
	addInvalidationRule(rule: InvalidationRule): void
	{
		this.invalidationRules.set(rule.name, rule);
		this.logger.debug(`Added invalidation rule: ${rule.name}`, {
			trigger: rule.trigger,
			patterns: rule.patterns.length,
			priority: rule.priority,
		});
	}

	/**
	 * Remove invalidation rule
	 */
	removeInvalidationRule(name: string): void
	{
		this.invalidationRules.delete(name);
		this.logger.debug(`Removed invalidation rule: ${name}`);
	}

	/**
	 * Trigger cache invalidation based on event
	 */
	async triggerInvalidation(event: InvalidationEvent): Promise<InvalidationResult>
	{
		const startTime = Date.now();

		try
		{
			this.logger.info('Triggering cache invalidation:', {
				type: event.type,
				domain: event.domain,
				category: event.category,
				triggeredBy: event.triggeredBy,
			});

			// Find matching rules
			const matchingRules = Array.from(this.invalidationRules.values())
				.filter(rule => rule.trigger === event.type)
				.sort((a, b) => this.getPriorityWeight(b.priority) - this.getPriorityWeight(a.priority));

			if (matchingRules.length === 0)
			{
				this.logger.warn(`No invalidation rules found for event type: ${event.type}`);

				return {
					success: true,
					patternsInvalidated: [],
					dependenciesInvalidated: [],
					errors: [],
					processingTime: Date.now() - startTime,
				};
			}

			// Process each matching rule
			const results = await Promise.allSettled(
				matchingRules.map(rule => this.processInvalidationRule(rule, event)),
			);

			// Aggregate results
			const aggregatedResult = this.aggregateInvalidationResults(results, startTime);

			// Log invalidation event for analytics
			await this.logInvalidationEvent(event, aggregatedResult);

			// Add to history
			this.addToHistory(event);

			return aggregatedResult;
		}
		catch (error)
		{
			this.logger.error('Failed to trigger cache invalidation:', error);

			return {
				success: false,
				patternsInvalidated: [],
				dependenciesInvalidated: [],
				errors: [{ pattern: 'system', error: error instanceof Error ? error.message : String(error) }],
				processingTime: Date.now() - startTime,
			};
		}
	}

	/**
	 * Process individual invalidation rule
	 */
	private async processInvalidationRule(
		rule: InvalidationRule,
		event: InvalidationEvent,
	): Promise<InvalidationResult>
	{
		const startTime = Date.now();

		try
		{
			const invalidationKey = `${rule.name}_${event.domain || 'global'}_${Date.now()}`;

			if (rule.delay && rule.delay > 0)
			{
				// Schedule delayed invalidation
				this.scheduleDelayedInvalidation(invalidationKey, rule, event);

				return {
					success: true,
					patternsInvalidated: [],
					dependenciesInvalidated: [],
					errors: [],
					processingTime: Date.now() - startTime,
				};
			}

			// Immediate invalidation
			return await this.executeInvalidation(rule, event);

		}
		catch (error)
		{
			this.logger.error(`Failed to process invalidation rule ${rule.name}:`, error);

			return {
				success: false,
				patternsInvalidated: [],
				dependenciesInvalidated: [],
				errors: [{ pattern: rule.name, error: error instanceof Error ? error.message : String(error) }],
				processingTime: Date.now() - startTime,
			};
		}
	}

	/**
	 * Schedule delayed invalidation
	 */
	private scheduleDelayedInvalidation(
		key: string,
		rule: InvalidationRule,
		event: InvalidationEvent,
	): void
	{
		// Cancel existing delayed invalidation if any
		if (this.pendingInvalidations.has(key))
		{
			clearTimeout(this.pendingInvalidations.get(key)!);
		}

		// Schedule new invalidation
		const timeout = setTimeout(async () =>
		{
			try
			{
				await this.executeInvalidation(rule, event);
				this.pendingInvalidations.delete(key);
			}
			catch (error)
			{
				this.logger.error(`Delayed invalidation failed for ${key}:`, error);
			}
		}, rule.delay);

		this.pendingInvalidations.set(key, timeout);
		this.logger.debug(`Scheduled delayed invalidation: ${key} (delay: ${rule.delay}ms)`);
	}

	/**
	 * Execute cache invalidation
	 */
	private async executeInvalidation(rule: InvalidationRule, event: InvalidationEvent): Promise<InvalidationResult>
	{
		const startTime = Date.now();
		const patternsInvalidated: string[] = [];
		const dependenciesInvalidated: string[] = [];
		const errors: Array<{ pattern: string; error: string }> = [];

		try
		{
			// Resolve and invalidate primary patterns
			const resolvedPatterns = this.resolvePatterns(rule.patterns, event);

			for (const pattern of resolvedPatterns)
			{
				try
				{
					await this.invalidatePattern(pattern);
					patternsInvalidated.push(pattern);
					this.logger.debug(`Invalidated cache pattern: ${pattern}`);
				}
				catch (error)
				{
					const errorMessage = error instanceof Error ? error.message : String(error);
					errors.push({ pattern, error: errorMessage });
					this.logger.error(`Failed to invalidate pattern ${pattern}:`, error);
				}
			}

			// Invalidate dependencies
			if (rule.dependencies && rule.dependencies.length > 0)
			{
				const resolvedDependencies = this.resolvePatterns(rule.dependencies, event);

				for (const dependency of resolvedDependencies)
				{
					try
					{
						await this.invalidatePattern(dependency);
						dependenciesInvalidated.push(dependency);
						this.logger.debug(`Invalidated dependency: ${dependency}`);
					}
					catch (error)
					{
						const errorMessage = error instanceof Error ? error.message : String(error);
						errors.push({ pattern: dependency, error: errorMessage });
						this.logger.error(`Failed to invalidate dependency ${dependency}:`, error);
					}
				}
			}

			// Cascade invalidation through dependency graph
			await this.cascadeInvalidation(patternsInvalidated);

			return {
				success: errors.length === 0,
				patternsInvalidated,
				dependenciesInvalidated,
				errors,
				processingTime: Date.now() - startTime,
			};
		}
		catch (error)
		{
			this.logger.error('Failed to execute cache invalidation:', error);

			return {
				success: false,
				patternsInvalidated,
				dependenciesInvalidated,
				errors: [{ pattern: 'execution', error: error instanceof Error ? error.message : String(error) }],
				processingTime: Date.now() - startTime,
			};
		}
	}

	/**
	 * Invalidate cache pattern
	 */
	private async invalidatePattern(pattern: string): Promise<void>
	{
		try
		{
			if (pattern.includes('*'))
			{
				// Pattern with wildcards - get matching keys and delete them
				const keys = await this.redisClient.keys(pattern);
				if (keys.length > 0)
				{
					await this.redisClient.del(keys);
				}
			}
			else
			{
				// Exact key
				await this.redisClient.del(pattern);
			}
		}
		catch (error)
		{
			this.logger.error(`Failed to invalidate pattern ${pattern}:`, error);
			throw error;
		}
	}

	/**
	 * Cascade invalidation through dependency graph
	 */
	private async cascadeInvalidation(invalidatedPatterns: string[]): Promise<void>
	{
		const cascadeQueue: string[] = [...invalidatedPatterns];
		const processed = new Set<string>();

		while (cascadeQueue.length > 0)
		{
			const pattern = cascadeQueue.shift()!;

			if (processed.has(pattern))
			{
				continue;
			}

			processed.add(pattern);

			// Find dependencies that depend on this pattern
			for (const [key, dependency] of this.dependencyGraph.entries())
			{
				if (dependency.dependsOn.some(dep => this.patternMatches(pattern, dep)))
				{
					try
					{
						await this.invalidatePattern(key);
						cascadeQueue.push(key);
						this.logger.debug(`Cascaded invalidation to: ${key}`);
					}
					catch (error)
					{
						this.logger.error(`Failed to cascade invalidation to ${key}:`, error);
					}
				}
			}
		}
	}

	/**
	 * Check if pattern matches dependency
	 */
	private patternMatches(pattern: string, dependency: string): boolean
	{
		// Simple pattern matching - could be enhanced with more sophisticated logic
		if (pattern === dependency)
		{
			return true;
		}

		// Handle wildcards
		if (pattern.includes('*'))
		{
			const regex = new RegExp(pattern.replace(/\*/g, '.*'));
			return regex.test(dependency);
		}

		return false;
	}

	/**
	 * Resolve pattern placeholders with event data
	 */
	private resolvePatterns(patterns: string[], event: InvalidationEvent): string[]
	{
		return patterns.map((pattern) =>
		{
			let resolvedPattern = pattern;

			// Replace domain placeholder
			if (event.domain)
			{
				resolvedPattern = resolvedPattern.replace(/{domain}/g, event.domain);
			}

			// Replace category placeholder
			if (event.category)
			{
				resolvedPattern = resolvedPattern.replace(/{category}/g, event.category);
			}

			// Replace other metadata placeholders
			if (event.metadata)
			{
				for (const [key, value] of Object.entries(event.metadata))
				{
					resolvedPattern = resolvedPattern.replace(new RegExp(`{${key}}`, 'g'), String(value));
				}
			}

			return resolvedPattern;
		});
	}

	/**
	 * Register cache dependency
	 */
	async registerDependency(key: string, dependsOn: string[], ttl: number = 3600): Promise<void>
	{
		const dependency: CacheDependency = {
			key,
			dependsOn,
			lastUpdated: new Date().toISOString(),
			ttl,
		};

		this.dependencyGraph.set(key, dependency);

		// Persist to Redis for recovery
		await this.redisClient.set(`dependency:${key}`, dependency, ttl);

		this.logger.debug(`Registered cache dependency: ${key}`, { dependsOn });
	}

	/**
	 * Unregister cache dependency
	 */
	async unregisterDependency(key: string): Promise<void>
	{
		this.dependencyGraph.delete(key);
		await this.redisClient.del(`dependency:${key}`);
		this.logger.debug(`Unregistered cache dependency: ${key}`);
	}

	/**
	 * Load dependency graph from Redis
	 */
	private async loadDependencyGraph(): Promise<void>
	{
		try
		{
			const dependencyKeys = await this.redisClient.keys('dependency:*');

			for (const key of dependencyKeys)
			{
				try
				{
					const dependency = await this.redisClient.get<CacheDependency>(key);
					if (dependency)
					{
						const actualKey = key.replace('dependency:', '');
						this.dependencyGraph.set(actualKey, dependency);
					}
				}
				catch (error)
				{
					this.logger.warn(`Failed to load dependency ${key}:`, error);
				}
			}

			this.logger.debug(`Loaded ${this.dependencyGraph.size} cache dependencies`);
		}
		catch (error)
		{
			this.logger.error('Failed to load dependency graph:', error);
		}
	}

	/**
	 * Load invalidation rules from configuration
	 */
	private async loadInvalidationRules(): Promise<void>
	{
		try
		{
			// Load custom rules from Redis if they exist
			const rulesKey = 'invalidation:rules';
			const customRules = await this.redisClient.get<InvalidationRule[]>(rulesKey);

			if (customRules && Array.isArray(customRules))
			{
				customRules.forEach(rule => this.addInvalidationRule(rule));
				this.logger.debug(`Loaded ${customRules.length} custom invalidation rules`);
			}
		}
		catch (error)
		{
			this.logger.warn('Failed to load custom invalidation rules:', error);
		}
	}

	/**
	 * Setup cleanup interval for expired dependencies
	 */
	private setupCleanupInterval(): void
	{
		setInterval(async () =>
		{
			try
			{
				await this.cleanupExpiredDependencies();
			}
			catch (error)
			{
				this.logger.error('Failed to cleanup expired dependencies:', error);
			}
		}, 300000); // 5 minutes
	}

	/**
	 * Cleanup expired dependencies
	 */
	private async cleanupExpiredDependencies(): Promise<void>
	{
		const now = Date.now();
		const expired: string[] = [];

		for (const [key, dependency] of this.dependencyGraph.entries())
		{
			const lastUpdated = new Date(dependency.lastUpdated).getTime();
			const expiryTime = lastUpdated + (dependency.ttl * 1000);

			if (now > expiryTime)
			{
				expired.push(key);
			}
		}

		for (const key of expired)
		{
			await this.unregisterDependency(key);
		}

		if (expired.length > 0)
		{
			this.logger.debug(`Cleaned up ${expired.length} expired dependencies`);
		}
	}

	/**
	 * Get priority weight for sorting
	 */
	private getPriorityWeight(priority: 'low' | 'medium' | 'high'): number
	{
		switch (priority)
		{
			case 'high': return 3;
			case 'medium': return 2;
			case 'low': return 1;
			default: return 1;
		}
	}

	/**
	 * Aggregate invalidation results
	 */
	private aggregateInvalidationResults(
		results: PromiseSettledResult<InvalidationResult>[],
		startTime: number,
	): InvalidationResult
	{
		const aggregated: InvalidationResult = {
			success: true,
			patternsInvalidated: [],
			dependenciesInvalidated: [],
			errors: [],
			processingTime: Date.now() - startTime,
		};

		for (const result of results)
		{
			if (result.status === 'fulfilled')
			{
				const value = result.value;
				aggregated.patternsInvalidated.push(...value.patternsInvalidated);
				aggregated.dependenciesInvalidated.push(...value.dependenciesInvalidated);
				aggregated.errors.push(...value.errors);

				if (!value.success)
				{
					aggregated.success = false;
				}
			}
			else
			{
				aggregated.success = false;
				aggregated.errors.push({
					pattern: 'rule_processing',
					error: result.reason instanceof Error ? result.reason.message : String(result.reason),
				});
			}
		}

		return aggregated;
	}

	/**
	 * Log invalidation event for analytics
	 */
	private async logInvalidationEvent(event: InvalidationEvent, result: InvalidationResult): Promise<void>
	{
		try
		{
			const logEntry = {
				...event,
				result: {
					success: result.success,
					patternsCount: result.patternsInvalidated.length,
					dependenciesCount: result.dependenciesInvalidated.length,
					errorsCount: result.errors.length,
					processingTime: result.processingTime,
				},
			};

			// Store in Redis with TTL
			const logKey = `invalidation:log:${Date.now()}`;
			await this.redisClient.set(logKey, logEntry, 86400); // 24 hours TTL

			// Add to history list (keep last 100 events)
			await this.redisClient.lpush('invalidation:history', JSON.stringify(logEntry));
			await this.redisClient.ltrim('invalidation:history', 0, 99);
		}
		catch (error)
		{
			this.logger.error('Failed to log invalidation event:', error);
		}
	}

	/**
	 * Add event to in-memory history
	 */
	private addToHistory(event: InvalidationEvent): void
	{
		this.invalidationHistory.unshift(event);

		if (this.invalidationHistory.length > this.MAX_HISTORY_SIZE)
		{
			this.invalidationHistory = this.invalidationHistory.slice(0, this.MAX_HISTORY_SIZE);
		}
	}

	/**
	 * Get invalidation statistics
	 */
	async getInvalidationStatistics(): Promise<{
		totalRules: number;
		pendingInvalidations: number;
		dependencyCount: number;
		recentEvents: InvalidationEvent[];
		rulesByTrigger: Record<string, number>;
	}>
	{
		const rulesByTrigger: Record<string, number> = {};

		for (const rule of this.invalidationRules.values())
		{
			rulesByTrigger[rule.trigger] = (rulesByTrigger[rule.trigger] || 0) + 1;
		}

		return {
			totalRules: this.invalidationRules.size,
			pendingInvalidations: this.pendingInvalidations.size,
			dependencyCount: this.dependencyGraph.size,
			recentEvents: this.invalidationHistory.slice(0, 10),
			rulesByTrigger,
		};
	}

	/**
	 * Manual cache invalidation
	 */
	async manualInvalidation(patterns: string[], metadata?: Record<string, unknown>): Promise<InvalidationResult>
	{
		const event: InvalidationEvent = {
			type: 'manual',
			timestamp: new Date().toISOString(),
			metadata,
			triggeredBy: 'manual',
		};

		// Create temporary rule for manual invalidation
		const tempRule: InvalidationRule = {
			name: 'manual_invalidation',
			trigger: 'manual',
			patterns,
			delay: 0,
			priority: 'high',
		};

		return await this.executeInvalidation(tempRule, event);
	}

	/**
	 * Cancel pending invalidation
	 */
	cancelPendingInvalidation(key: string): boolean
	{
		const timeout = this.pendingInvalidations.get(key);
		if (timeout)
		{
			clearTimeout(timeout);
			this.pendingInvalidations.delete(key);
			this.logger.debug(`Cancelled pending invalidation: ${key}`);
			return true;
		}
		return false;
	}

	/**
	 * Cancel all pending invalidations
	 */
	cancelAllPendingInvalidations(): void
	{
		for (const timeout of this.pendingInvalidations.values())
		{
			clearTimeout(timeout);
		}
		this.pendingInvalidations.clear();
		this.logger.info('Cancelled all pending invalidations');
	}

	/**
	 * Cleanup service - cancel pending invalidations
	 */
	async cleanup(): Promise<void>
	{
		this.cancelAllPendingInvalidations();
		this.logger.info('Cache invalidation manager cleaned up');
	}
}

export type {
	InvalidationRule,
	InvalidationEvent,
	InvalidationResult,
	CacheDependency,
};

export default CacheInvalidationManager;
