# Search Indexing and Data Synchronization

This module contains the complete search indexing and data synchronization functionality extracted from all three services (crawler, ranking-engine, scheduler) as specified in task 12.

## Components Implemented

### 1. SearchIndexingService

Main orchestrator service that coordinates all indexing operations:

- **Domain Indexing**: Single and bulk domain indexing with Manticore
- **Synchronization**: Full and incremental sync from ScyllaDB to Manticore
- **Cache Invalidation**: Intelligent cache invalidation with dependency tracking
- **Maintenance**: Automated index optimization and health monitoring
- **Configuration**: Environment-based configuration management
- **Health Monitoring**: Comprehensive health checks and statistics

### 2. ManticoreIndexManager

Manages all Manticore search indexes:

- **Index Creation**: Automated index creation with proper schemas
- **Document Operations**: Insert, update, delete operations with batch processing
- **Index Optimization**: Performance optimization and maintenance
- **Health Monitoring**: Index health checks and error recovery
- **Schema Validation**: Ensures index schema consistency
- **Raw Query Support**: Executes raw SQL queries on Manticore

### 3. DataSynchronizationService

Handles data synchronization from ScyllaDB to Manticore:

- **Full Synchronization**: Complete data sync for initial setup
- **Incremental Sync**: Efficient updates for changed data only
- **Conflict Resolution**: Handles data conflicts with configurable strategies
- **Batch Processing**: Processes large datasets in manageable chunks
- **Error Recovery**: Robust error handling and retry mechanisms
- **Data Transformation**: Maps ScyllaDB data to Manticore document format

### 4. CacheInvalidationManager

Intelligent cache invalidation across Redis and application caches:

- **Rule-Based Invalidation**: Configurable invalidation patterns
- **Dependency Tracking**: Cascading invalidation for related data
- **Delayed Invalidation**: Batched invalidation for performance
- **Pattern Matching**: Flexible cache key pattern matching
- **Performance Analytics**: Tracks invalidation statistics and performance

### 5. IndexMaintenanceService

Automated maintenance tasks for search indexes:

- **Scheduled Tasks**: Cron-based maintenance scheduling
- **Index Optimization**: Automated performance tuning
- **Capacity Planning**: Monitors and predicts scaling needs
- **Health Monitoring**: Continuous health checks and alerting
- **Cleanup Operations**: Removes old data and optimizes storage
- **Performance Metrics**: Tracks and analyzes index performance

## Features Extracted from Original Services

### From Crawler Service

- **Cache Invalidation Patterns**: Favicon, screenshot, performance cache invalidation
- **Data Transformation**: Domain data normalization and formatting
- **Field Mapping**: Comprehensive field mapping for search indexing

### From Ranking Engine Service

- **Manticore Sync Jobs**: Job queue integration for search index updates
- **Ranking Data Sync**: Synchronizes ranking updates to search indexes
- **Performance Optimization**: Ranking-specific index optimizations

### From Scheduler Service

- **Periodic Sync**: Scheduled synchronization operations
- **Job Management**: Integration with job queue for async processing
- **Maintenance Scheduling**: Automated maintenance task scheduling

## Configuration

The service supports comprehensive configuration through environment variables:

```bash
# Synchronization settings
INDEXING_SYNC_INTERVAL=300000          # 5 minutes
INDEXING_BATCH_SIZE=100                # Batch size for processing
INDEXING_MAX_CONCURRENCY=10            # Max concurrent operations
INDEXING_ENABLE_PERIODIC_SYNC=true     # Enable periodic sync
INDEXING_ENABLE_MAINTENANCE=true       # Enable maintenance tasks
INDEXING_ENABLE_CACHE_INVALIDATION=true # Enable cache invalidation
INDEXING_CONFLICT_RESOLUTION=latest_wins # Conflict resolution strategy

# Sync configuration
SYNC_BATCH_SIZE=100                    # Sync batch size
SYNC_MAX_CONCURRENCY=10                # Max sync concurrency
SYNC_RETRY_ATTEMPTS=3                  # Retry attempts
SYNC_RETRY_DELAY=1000                  # Retry delay in ms
SYNC_CONFLICT_RESOLUTION=latest_wins   # Conflict resolution
```

## Usage

```typescript
import { SearchIndexingService } from "./indexing";

// Initialize the service
const indexingService = new SearchIndexingService(
  scyllaClient,
  manticoreClient,
  redisClient
);

// Initialize and start
await indexingService.initialize();
await indexingService.start();

// Index a domain
const result = await indexingService.indexDomain(domainData);

// Perform synchronization
await indexingService.performIncrementalSync();

// Invalidate cache
await indexingService.invalidateDomainCache("example.com");

// Get health status
const health = await indexingService.getHealthStatus();
```

## Integration with Job Queue

The service integrates with the existing job queue system to process Manticore sync jobs:

```typescript
// Process Manticore sync job
await indexingService.processManticoreSyncJob({
  domain: "example.com",
  syncType: "incremental",
});
```

## Cache Invalidation Patterns

Comprehensive cache invalidation patterns extracted from all services:

- **Domain Updates**: `analysis:{domain}`, `domain:{domain}:*`, `favicon:{domain}`, `screenshot:{domain}`
- **Ranking Updates**: `ranking:domain:{domain}`, `rankings:global:*`, `rankings:category:{category}:*`
- **Search Updates**: `search:results:*`, `search:facets:*`, `domainAnalysis:*`
- **Technology Updates**: `technology:*`, `domains:technology:*`
- **Performance Updates**: `performance:{domain}`, `performance:category:*`
- **Security Updates**: `security:{domain}`, `security:ssl:*`

## Health Monitoring

The service provides comprehensive health monitoring:

- **Component Health**: Individual service component status
- **Index Health**: Manticore index status and performance
- **Sync Statistics**: Synchronization performance and errors
- **Cache Statistics**: Cache invalidation metrics
- **Maintenance Status**: Maintenance task execution status

## Error Recovery

Robust error handling and recovery mechanisms:

- **Retry Logic**: Configurable retry attempts with exponential backoff
- **Conflict Resolution**: Multiple strategies for handling data conflicts
- **Health Checks**: Continuous monitoring with automatic recovery
- **Graceful Degradation**: Service continues operating during partial failures

## Performance Optimization

Multiple performance optimization features:

- **Batch Processing**: Efficient batch operations for large datasets
- **Connection Pooling**: Optimized database connection management
- **Index Optimization**: Automated Manticore index optimization
- **Cache Warming**: Proactive cache population for better performance
- **Capacity Planning**: Monitors and predicts scaling requirements

## Testing

Comprehensive test suite covering:

- **Unit Tests**: Individual component testing with mocks
- **Integration Tests**: Service integration testing
- **Performance Tests**: Load and performance testing
- **Error Scenarios**: Error handling and recovery testing

## Requirements Satisfied

This implementation satisfies all requirements from task 12:

✅ **Copy all Manticore sync operations** from all three services with complete index management and optimization
✅ **Extract domain data transformation** for search index format, field mapping, and data normalization  
✅ **Implement incremental and full index updates** with conflict resolution and consistency checks
✅ **Add search index health monitoring**, error recovery, performance optimization, and capacity management
✅ **Implement cache invalidation** for updated domains across Redis and application caches with dependency tracking
✅ **Extract index maintenance operations**, cleanup procedures, optimization tasks, and performance tuning
✅ **Copy all indexing configurations**, field definitions, and search optimization strategies

The implementation provides a complete, production-ready search indexing and data synchronization solution that consolidates functionality from all three original services while maintaining high performance, reliability, and scalability.
