#!/usr/bin/env node

/**
 * Worker Service CLI Entry Point
 *
 * Command-line interface for worker management, debugging,
 * operational tasks, and system administration.
 */

import { config } from 'dotenv';
import { logger as sharedLogger } from '@shared';
import { WorkerCLI } from './cli/WorkerCLI';

// Load environment variables
config();

const logger = sharedLogger.getLogger('worker-cli');

async function main(): Promise<void>
{
	try
	{
		const cli = new WorkerCLI();
		// Use the CLI's run method which parses process.argv internally
		cli.run();
	}
	catch (error)
	{
		console.error('CLI execution failed:', (error as Error).message);
		logger.error({ error: (error as Error).message }, 'CLI execution failed');
		process.exit(1);
	}
}

// Start the CLI
main().catch((error) =>
{
	console.error('Fatal CLI error:', error);
	logger.error({ error }, 'Fatal CLI error');
	process.exit(1);
});
