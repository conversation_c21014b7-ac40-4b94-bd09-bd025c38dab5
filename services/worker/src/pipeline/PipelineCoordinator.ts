/**
 * Pipeline Coordinator
 *
 * Coordinates multiple pipeline instances and manages resource allocation
 * across concurrent domain processing operations. Provides load balancing,
 * priority management, and system-wide resource optimization.
 */

import { logger as loggerFactory } from '@shared/utils/Logger';
import { readFile } from 'node:fs/promises';
import { cpus, loadavg } from 'node:os';
import type {
	DomainJobType,
	ProcessingResultType,
	WorkerConfigType,
	ProcessingPhaseType,
} from '../types/WorkerTypes';
import DomainProcessingPipeline from './DomainProcessingPipeline';
import TaskExecutorPool from './TaskExecutorPool';

const logger = loggerFactory.getLogger('PipelineCoordinator');

type CoordinatorConfigType =
{
	maxConcurrentPipelines: number;
	priorityWeights: Record<string, number>;
	resourceAllocation: {
		reserveCapacityPercent: number;
		emergencyThreshold: number;
		gracefulDegradationEnabled: boolean;
	};
	loadBalancing: {
		algorithm: 'round_robin' | 'least_loaded' | 'priority_weighted';
		rebalanceInterval: number;
		healthCheckInterval: number;
	};
	monitoring: {
		metricsInterval: number;
		alertThresholds: {
			queueDepth: number;
			processingTime: number;
			errorRate: number;
		};
	};
};

type PipelineInstanceType =
{
	id: string;
	pipeline: DomainProcessingPipeline;
	taskExecutor: TaskExecutorPool;
	currentLoad: number;
	isHealthy: boolean;
	lastHealthCheck: Date;
	activeJobs: Set<string>;
	completedJobs: number;
	failedJobs: number;
};

type CoordinatorStatsType =
{
	totalPipelines: number;
	activePipelines: number;
	healthyPipelines: number;
	totalActiveJobs: number;
	totalQueuedJobs: number;
	totalCompletedJobs: number;
	totalFailedJobs: number;
	averageProcessingTime: number;
	systemLoad: number;
	resourceUtilization: {
		cpu: number;
		memory: number;
		disk: number;
	};
	throughput: {
		jobsPerSecond: number;
		jobsPerMinute: number;
		jobsPerHour: number;
	};
};

type JobAssignmentType =
{
	pipelineId: string;
	estimatedStartTime: Date;
	estimatedCompletionTime: Date;
	queuePosition: number;
};

class PipelineCoordinator
{
	private config: CoordinatorConfigType;
	private pipelines: Map<string, PipelineInstanceType> = new Map();
	private jobQueue: Array<{ job: DomainJobType; resolve: (result: ProcessingResultType) => void; reject: (error: Error) => void }> = [];
	private isInitialized: boolean = false;
	private isShuttingDown: boolean = false;

	// Monitoring and metrics
	private healthCheckInterval?: NodeJS.Timeout;
	private metricsInterval?: NodeJS.Timeout;
	private loadBalancer?: NodeJS.Timeout;

	// Statistics
	private stats: {
		totalJobsProcessed: number;
		totalProcessingTime: number;
		startTime: Date;
	} = {
		totalJobsProcessed: 0,
		totalProcessingTime: 0,
		startTime: new Date(),
	};

	constructor(workerConfig: WorkerConfigType)
	{
		this.config = this.createCoordinatorConfig(workerConfig);
	}

	/**
	 * Initialize the pipeline coordinator
	 */
	async initialize(
		pipelineFactory: () => Promise<{ pipeline: DomainProcessingPipeline; taskExecutor: TaskExecutorPool }>,
	): Promise<void>
	{
		if (this.isInitialized)
		{
			logger.warn('Pipeline coordinator already initialized');
			return;
		}

		try
		{
			logger.info('Initializing Pipeline Coordinator', {
				maxConcurrentPipelines: this.config.maxConcurrentPipelines,
			});

			// Create initial pipeline instances
			for (let i = 0; i < this.config.maxConcurrentPipelines; i++)
			{
				const pipelineId = `pipeline-${i + 1}`;
				const { pipeline, taskExecutor } = await pipelineFactory();

				await pipeline.initialize();
				await taskExecutor.initialize();

				const pipelineInstance: PipelineInstanceType = {
					id: pipelineId,
					pipeline,
					taskExecutor,
					currentLoad: 0,
					isHealthy: true,
					lastHealthCheck: new Date(),
					activeJobs: new Set(),
					completedJobs: 0,
					failedJobs: 0,
				};

				this.pipelines.set(pipelineId, pipelineInstance);
				logger.debug('Pipeline instance created', { pipelineId });
			}

			// Start monitoring and load balancing
			this.startHealthMonitoring();
			this.startMetricsCollection();
			this.startLoadBalancing();

			this.isInitialized = true;
			logger.info('Pipeline Coordinator initialized successfully', {
				pipelineCount: this.pipelines.size,
			});
		}
		catch (error)
		{
			logger.error('Failed to initialize Pipeline Coordinator:', error);
			throw error;
		}
	}

	/**
	 * Process a domain job through the most suitable pipeline
	 */
	async processDomain(domainJob: DomainJobType): Promise<ProcessingResultType>
	{
		if (!this.isInitialized)
		{
			throw new Error('Pipeline coordinator not initialized');
		}

		if (this.isShuttingDown)
		{
			throw new Error('Pipeline coordinator is shutting down');
		}

		logger.info('Received domain processing request', {
			domain: domainJob.domain,
			jobId: domainJob.id,
			priority: domainJob.priority,
		});

		return new Promise<ProcessingResultType>((resolve, reject) =>
		{
			// Add to queue with priority handling
			this.addJobToQueue({ job: domainJob, resolve, reject });

			// Try to assign immediately if capacity available
			this.processJobQueue();
		});
	}

	/**
	 * Get assignment information for a job
	 */
	getJobAssignment(domainJob: DomainJobType): JobAssignmentType | null
	{
		const bestPipeline = this.selectBestPipeline(domainJob);
		if (!bestPipeline)
		{
			return null;
		}

		const queuePosition = this.calculateQueuePosition(domainJob);
		const estimatedStartTime = this.estimateStartTime(bestPipeline, queuePosition);
		const estimatedCompletionTime = this.estimateCompletionTime(estimatedStartTime, domainJob);

		return {
			pipelineId: bestPipeline.id,
			estimatedStartTime,
			estimatedCompletionTime,
			queuePosition,
		};
	}

	/**
	 * Get current coordinator statistics
	 */
	getStatistics(): CoordinatorStatsType
	{
		const now = new Date();
		const uptime = now.getTime() - this.stats.startTime.getTime();
		const hoursElapsed = uptime / (1000 * 60 * 60);

		const healthyPipelines = Array.from(this.pipelines.values()).filter(p => p.isHealthy).length;
		const totalActiveJobs = Array.from(this.pipelines.values()).reduce((sum, p) => sum + p.activeJobs.size, 0);
		const totalCompletedJobs = Array.from(this.pipelines.values()).reduce((sum, p) => sum + p.completedJobs, 0);
		const totalFailedJobs = Array.from(this.pipelines.values()).reduce((sum, p) => sum + p.failedJobs, 0);

		const averageProcessingTime = this.stats.totalJobsProcessed > 0
			? this.stats.totalProcessingTime / this.stats.totalJobsProcessed
			: 0;

		// Calculate system load
		const systemLoad = this.calculateSystemLoad();

		// Calculate resource utilization
		const memUsage = process.memoryUsage();
		const memoryUtilization = (memUsage.heapUsed / memUsage.heapTotal) * 100;

		return {
			totalPipelines: this.pipelines.size,
			activePipelines: this.pipelines.size,
			healthyPipelines,
			totalActiveJobs,
			totalQueuedJobs: this.jobQueue.length,
			totalCompletedJobs,
			totalFailedJobs,
			averageProcessingTime,
			systemLoad,
			resourceUtilization: {
				cpu: process.cpuUsage ? this.calculateCpuUsage() : 0,
				memory: memoryUtilization,
				disk: 0 // Disk monitoring not implemented - would require fs.stat monitoring
			},
			throughput: {
				jobsPerSecond: hoursElapsed > 0 ? totalCompletedJobs / (uptime / 1000) : 0,
				jobsPerMinute: hoursElapsed > 0 ? totalCompletedJobs / (uptime / (1000 * 60)) : 0,
				jobsPerHour: hoursElapsed > 0 ? totalCompletedJobs / hoursElapsed : 0,
			},
		};
	}

	/**
	 * Shutdown the coordinator gracefully
	 */
	async shutdown(timeoutMs: number = 60000): Promise<void>
	{
		logger.info('Shutting down Pipeline Coordinator', {
			activePipelines: this.pipelines.size,
			queuedJobs: this.jobQueue.length,
		});

		this.isShuttingDown = true;

		// Stop monitoring
		if (this.healthCheckInterval)
		{
			clearInterval(this.healthCheckInterval);
		}
		if (this.metricsInterval)
		{
			clearInterval(this.metricsInterval);
		}
		if (this.loadBalancer)
		{
			clearInterval(this.loadBalancer);
		}

		// Reject queued jobs
		this.jobQueue.forEach(({ reject }) =>
		{
			reject(new Error('Pipeline coordinator is shutting down'));
		});
		this.jobQueue = [];

		// Shutdown all pipelines
		const shutdownPromises = Array.from(this.pipelines.values()).map(async (pipelineInstance) =>
		{
			try
			{
				await pipelineInstance.taskExecutor.shutdown(timeoutMs / 2);
				logger.debug('Pipeline instance shut down', { pipelineId: pipelineInstance.id });
			}
			catch (error)
			{
				logger.error('Failed to shutdown pipeline instance:', error, {
					pipelineId: pipelineInstance.id,
				});
			}
		});

		try
		{
			await Promise.allSettled(shutdownPromises);
			logger.info('All pipeline instances shut down');
		}
		catch (error)
		{
			logger.error('Error during pipeline shutdown:', error);
		}

		logger.info('Pipeline Coordinator shutdown complete');
	}

	/**
	 * Add job to queue with priority handling
	 */
	private addJobToQueue(queueItem: { job: DomainJobType; resolve: (result: ProcessingResultType) => void; reject: (error: Error) => void }): void
	{
		const priorityOrder = { critical: 0, high: 1, medium: 2, low: 3 };
		const jobPriority = priorityOrder[queueItem.job.priority as keyof typeof priorityOrder] ?? 3;

		// Find insertion point based on priority
		let insertIndex = this.jobQueue.length;
		for (let i = 0; i < this.jobQueue.length; i++)
		{
			const existingPriority = priorityOrder[this.jobQueue[i].job.priority as keyof typeof priorityOrder] ?? 3;
			if (jobPriority < existingPriority)
			{
				insertIndex = i;
				break;
			}
		}

		this.jobQueue.splice(insertIndex, 0, queueItem);

		logger.debug('Job added to queue', {
			domain: queueItem.job.domain,
			priority: queueItem.job.priority,
			queuePosition: insertIndex,
			queueSize: this.jobQueue.length,
		});
	}

	/**
	 * Process the job queue and assign jobs to available pipelines
	 */
	private processJobQueue(): void
	{
		while (this.jobQueue.length > 0 && !this.isShuttingDown)
		{
			const queueItem = this.jobQueue[0];
			const bestPipeline = this.selectBestPipeline(queueItem.job);

			if (!bestPipeline)
			{
				// No available pipeline, wait for next cycle
				break;
			}

			// Remove from queue and assign to pipeline
			this.jobQueue.shift();
			this.assignJobToPipeline(queueItem, bestPipeline);
		}
	}

	/**
	 * Select the best pipeline for a job based on load balancing algorithm
	 */
	private selectBestPipeline(domainJob: DomainJobType): PipelineInstanceType | null
	{
		const healthyPipelines = Array.from(this.pipelines.values()).filter(p => p.isHealthy);

		if (healthyPipelines.length === 0)
		{
			logger.warn('No healthy pipelines available');
			return null;
		}

		switch (this.config.loadBalancing.algorithm)
		{
			case 'least_loaded':
				return healthyPipelines.reduce((best, current) =>
					current.currentLoad < best.currentLoad ? current : best);

			case 'priority_weighted':
				return this.selectPipelineByPriorityWeight(healthyPipelines, domainJob);

			case 'round_robin':
			default:
				// Simple round-robin based on completed jobs
				return healthyPipelines.reduce((best, current) =>
					current.completedJobs < best.completedJobs ? current : best);
		}
	}

	/**
	 * Select pipeline using priority-weighted algorithm
	 */
	private selectPipelineByPriorityWeight(
		pipelines: PipelineInstanceType[],
		domainJob: DomainJobType,
	): PipelineInstanceType
	{
		const priorityWeight = this.config.priorityWeights[domainJob.priority] || 1;

		// Calculate weighted scores for each pipeline
		const scoredPipelines = pipelines.map((pipeline) =>
		{
			const loadFactor = 1 - (pipeline.currentLoad / 100); // Lower load = higher score
			const healthFactor = pipeline.isHealthy ? 1 : 0.5;
			const priorityFactor = priorityWeight;

			const score = loadFactor * healthFactor * priorityFactor;

			return { pipeline, score };
		});

		// Return pipeline with highest score
		return scoredPipelines.reduce((best, current) =>
			current.score > best.score ? current : best).pipeline;
	}

	/**
	 * Assign job to a specific pipeline
	 */
	private async assignJobToPipeline(
		queueItem: { job: DomainJobType; resolve: (result: ProcessingResultType) => void; reject: (error: Error) => void },
		pipelineInstance: PipelineInstanceType,
	): Promise<void>
	{
		const { job, resolve, reject } = queueItem;

		logger.info('Assigning job to pipeline', {
			domain: job.domain,
			jobId: job.id,
			pipelineId: pipelineInstance.id,
			currentLoad: pipelineInstance.currentLoad,
		});

		// Add to active jobs
		pipelineInstance.activeJobs.add(job.id);
		pipelineInstance.currentLoad = this.calculatePipelineLoad(pipelineInstance);

		try
		{
			const startTime = new Date();
			const result = await pipelineInstance.pipeline.processDomain(job);
			const endTime = new Date();
			const processingTime = endTime.getTime() - startTime.getTime();

			// Update statistics
			pipelineInstance.completedJobs++;
			this.stats.totalJobsProcessed++;
			this.stats.totalProcessingTime += processingTime;

			logger.info('Job completed successfully', {
				domain: job.domain,
				jobId: job.id,
				pipelineId: pipelineInstance.id,
				processingTime,
				status: result.status,
			});

			resolve(result);
		}
		catch (error)
		{
			// Update statistics
			pipelineInstance.failedJobs++;

			logger.error('Job processing failed:', error, {
				domain: job.domain,
				jobId: job.id,
				pipelineId: pipelineInstance.id,
			});

			reject(error as Error);
		}
		finally
		{
			// Remove from active jobs
			pipelineInstance.activeJobs.delete(job.id);
			pipelineInstance.currentLoad = this.calculatePipelineLoad(pipelineInstance);

			// Process next job in queue
			setImmediate(() => this.processJobQueue());
		}
	}

	/**
	 * Calculate current load for a pipeline
	 */
	private calculatePipelineLoad(pipelineInstance: PipelineInstanceType): number
	{
		const taskExecutorStats = pipelineInstance.taskExecutor.getStatistics();
		const maxConcurrent = pipelineInstance.taskExecutor.getConfiguration().maxConcurrentTasks;

		// Load based on active tasks and queue depth
		const taskLoad = (taskExecutorStats.activeTasks / maxConcurrent) * 100;
		const queueLoad = Math.min((taskExecutorStats.queuedTasks / maxConcurrent) * 50, 50);

		return Math.min(taskLoad + queueLoad, 100);
	}

	/**
	 * Calculate system-wide load
	 */
	private calculateSystemLoad(): number
	{
		const pipelineLoads = Array.from(this.pipelines.values()).map(p => p.currentLoad);
		return pipelineLoads.length > 0 ? pipelineLoads.reduce((sum, load) => sum + load, 0) / pipelineLoads.length : 0;
	}

	/**
	 * Calculate queue position for a job
	 */
	private calculateQueuePosition(domainJob: DomainJobType): number
	{
		const priorityOrder = { critical: 0, high: 1, medium: 2, low: 3 };
		const jobPriority = priorityOrder[domainJob.priority as keyof typeof priorityOrder] ?? 3;

		let position = 0;
		for (const queueItem of this.jobQueue)
		{
			const queuePriority = priorityOrder[queueItem.job.priority as keyof typeof priorityOrder] ?? 3;
			if (queuePriority <= jobPriority)
			{
				position++;
			}
		}

		return position;
	}

	/**
	 * Estimate start time for a job
	 */
	private estimateStartTime(pipelineInstance: PipelineInstanceType, queuePosition: number): Date
	{
		const averageProcessingTime = this.stats.totalJobsProcessed > 0
			? this.stats.totalProcessingTime / this.stats.totalJobsProcessed
			: 60000; // Default 1 minute

		const estimatedDelay = queuePosition * averageProcessingTime;
		return new Date(Date.now() + estimatedDelay);
	}

	/**
	 * Estimate completion time for a job
	 */
	private estimateCompletionTime(startTime: Date, domainJob: DomainJobType): Date
	{
		// Estimate based on crawl type
		const processingTimeEstimates = {
			quick: 30000, // 30 seconds
			security: 60000, // 1 minute
			performance: 90000, // 1.5 minutes
			full: 120000, // 2 minutes
		};

		const estimatedProcessingTime = processingTimeEstimates[domainJob.crawlType] || 60000;
		return new Date(startTime.getTime() + estimatedProcessingTime);
	}

	/**
	 * Start health monitoring for all pipelines
	 */
	private startHealthMonitoring(): void
	{
		this.healthCheckInterval = setInterval(async () =>
		{
			for (const [pipelineId, pipelineInstance] of this.pipelines)
			{
				try
				{
					const taskExecutorStats = pipelineInstance.taskExecutor.getStatistics();
					const isHealthy = taskExecutorStats.successRate > 50; // 50% success rate threshold

					if (pipelineInstance.isHealthy !== isHealthy)
					{
						logger.info('Pipeline health status changed', {
							pipelineId,
							previousHealth: pipelineInstance.isHealthy,
							currentHealth: isHealthy,
							successRate: taskExecutorStats.successRate,
						});
					}

					pipelineInstance.isHealthy = isHealthy;
					pipelineInstance.lastHealthCheck = new Date();
				}
				catch (error)
				{
					logger.error('Health check failed for pipeline:', error, { pipelineId });
					pipelineInstance.isHealthy = false;
				}
			}
		}, this.config.loadBalancing.healthCheckInterval);
	}

	/**
	 * Start metrics collection
	 */
	private startMetricsCollection(): void
	{
		this.metricsInterval = setInterval(() =>
		{
			const stats = this.getStatistics();

			logger.debug('Pipeline coordinator metrics', {
				totalActiveJobs: stats.totalActiveJobs,
				queuedJobs: stats.totalQueuedJobs,
				systemLoad: stats.systemLoad,
				throughput: stats.throughput.jobsPerMinute,
				healthyPipelines: stats.healthyPipelines,
			});

			// Check alert thresholds
			if (stats.totalQueuedJobs > this.config.monitoring.alertThresholds.queueDepth)
			{
				logger.warn('High queue depth detected', {
					queueDepth: stats.totalQueuedJobs,
					threshold: this.config.monitoring.alertThresholds.queueDepth,
				});
			}

			if (stats.averageProcessingTime > this.config.monitoring.alertThresholds.processingTime)
			{
				logger.warn('High average processing time detected', {
					averageProcessingTime: stats.averageProcessingTime,
					threshold: this.config.monitoring.alertThresholds.processingTime,
				});
			}
		}, this.config.monitoring.metricsInterval);
	}

	/**
	 * Start load balancing optimization
	 */
	private startLoadBalancing(): void
	{
		this.loadBalancer = setInterval(() =>
		{
			// Process any queued jobs
			this.processJobQueue();

			// Log load balancing status
			const stats = this.getStatistics();
			if (stats.totalQueuedJobs > 0)
			{
				logger.debug('Load balancing status', {
					queuedJobs: stats.totalQueuedJobs,
					activeJobs: stats.totalActiveJobs,
					systemLoad: stats.systemLoad,
				});
			}
		}, this.config.loadBalancing.rebalanceInterval);
	}

	/**
	 * Create coordinator configuration
	 */
	private createCoordinatorConfig(workerConfig: WorkerConfigType): CoordinatorConfigType
	{
		return {
			maxConcurrentPipelines: Math.max(1, Math.floor(workerConfig.maxConcurrentTasks / 3)), // Divide tasks across pipelines
			priorityWeights: {
				critical: 4,
				high: 3,
				medium: 2,
				low: 1,
			},
			resourceAllocation: {
				reserveCapacityPercent: 20,
				emergencyThreshold: 90,
				gracefulDegradationEnabled: true,
			},
			loadBalancing: {
				algorithm: 'least_loaded',
				rebalanceInterval: 5000, // 5 seconds
				healthCheckInterval: 30000, // 30 seconds
			},
			monitoring: {
				metricsInterval: 60000, // 1 minute
				alertThresholds: {
					queueDepth: workerConfig.maxConcurrentTasks * 2,
					processingTime: 300000, // 5 minutes
					errorRate: 25, // 25%
				},
			},
		};
	}

	/**
	 * Calculate CPU usage percentage
	 */
	private calculateCpuUsage(): number
	{
		if (!process.cpuUsage)
		{
			return 0;
		}

		const currentCpuUsage = process.cpuUsage();

		// Calculate CPU usage as a percentage
		// cpuUsage returns microseconds, so we divide by 1000 to get milliseconds
		const totalCpuTime = (currentCpuUsage.user + currentCpuUsage.system) / 1000;

		// Get process uptime in milliseconds
		const uptimeMs = process.uptime() * 1000;

		// Calculate CPU usage percentage
		const cpuPercent = uptimeMs > 0 ? (totalCpuTime / uptimeMs) * 100 : 0;

		// Cap at 100% and ensure it's a reasonable value
		return Math.min(Math.max(cpuPercent, 0), 100);
	}
}

export default PipelineCoordinator;
export type {
	CoordinatorConfigType,
	PipelineInstanceType,
	CoordinatorStatsType,
	JobAssignmentType,
};
