/**
 * Pipeline Module Exports
 *
 * Exports all pipeline-related components for the worker service.
 * This module contains the core processing pipeline that orchestrates
 * all extracted functionality from the three original services.
 */

// Core pipeline components
export { default as DomainProcessingPipeline } from './DomainProcessingPipeline';
export { default as TaskExecutorPool } from './TaskExecutorPool';
export { default as PipelineCoordinator } from './PipelineCoordinator';

// Type exports
export type {
	PipelineConfigType,
	PipelinePhaseResultType,
	PipelineExecutionContextType,
	TaskRetryDecisionType,
} from './DomainProcessingPipeline';

export type {
	TaskType,
	TaskResultType,
	TaskPriorityType,
	TaskStatusType,
	ExecutorPoolConfigType,
	ExecutorPoolStatsType,
} from './TaskExecutorPool';

export type {
	CoordinatorConfigType,
	PipelineInstanceType,
	CoordinatorStatsType,
	JobAssignmentType,
} from './PipelineCoordinator';
