/**
 * Domain Processing Pipeline Tests
 *
 * Tests for the core domain processing pipeline functionality.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import DomainProcessingPipeline from '../DomainProcessingPipeline';
import type {
	WorkerConfigType,
	DomainJobType,
} from '../../types/WorkerTypes';

// Mock dependencies
const mockDatabaseManager = {
	getScyllaClient: vi.fn(),
	getMariaClient: vi.fn(),
	getRedisClient: vi.fn(),
	getManticoreClient: vi.fn(),
	healthCheck: vi.fn().mockResolvedValue({
		scylla: { connected: true, lastCheck: new Date(), responseTime: 10 },
		maria: { connected: true, lastCheck: new Date(), responseTime: 5 },
		redis: { connected: true, lastCheck: new Date(), responseTime: 2 },
		manticore: { connected: true, lastCheck: new Date(), responseTime: 8 },
	}),
};

const mockLockManager = {
	initialize: vi.fn().mockResolvedValue(undefined),
	shutdown: vi.fn().mockResolvedValue(undefined),
	acquireLock: vi.fn().mockResolvedValue({ success: true, lockId: 'test-lock-123' }),
	releaseLock: vi.fn().mockResolvedValue({ success: true, wasOwner: true }),
	renewLock: vi.fn().mockResolvedValue({ success: true, newTtl: 300000 }),
};

const mockCrawlerOrchestrator = {
	initialize: vi.fn().mockResolvedValue(undefined),
	shutdown: vi.fn().mockResolvedValue(undefined),
	collectData: vi.fn().mockResolvedValue({
		domain: 'example.com',
		execution: {
			success: true,
			completedModules: ['dns', 'robots', 'homepage'],
			failedModules: [],
			results: {
				dns: { records: ['A', 'AAAA'] },
				robots: { allowed: true },
				homepage: { title: 'Example Domain' },
			},
			errors: [],
		},
		validation: {
			isComplete: true,
			completenessScore: 95,
			missingCritical: [],
			missingOptional: [],
			availableData: ['dns', 'robots', 'homepage'],
			dataQuality: {},
			recommendations: [],
		},
		recommendations: [],
		metadata: {
			startTime: new Date(),
			endTime: new Date(),
			totalDuration: 1000,
			resourceUsage: {
				cpuTime: 100,
				memoryPeak: 1024 * 1024,
				memoryAverage: 512 * 1024,
				diskIO: 0,
				networkIO: 1024,
			},
		},
	}),
};

const mockRankingManager = {
	initialize: vi.fn().mockResolvedValue(undefined),
	shutdown: vi.fn().mockResolvedValue(undefined),
	calculateRanking: vi.fn().mockResolvedValue({
		domain: 'example.com',
		scores: {
			performance: 85,
			security: 90,
			seo: 80,
			technical: 88,
		},
		compositeScore: 85.75,
		grade: 'B+',
		globalRanking: 1250,
		categoryRanking: 45,
		previousRanking: 1300,
		rankingChange: 50,
		confidence: 0.92,
		lastUpdated: new Date(),
	}),
};

const mockIndexingService = {
	initialize: vi.fn().mockResolvedValue(undefined),
	shutdown: vi.fn().mockResolvedValue(undefined),
	indexDomain: vi.fn().mockResolvedValue({
		success: true,
		updatedIndexes: ['manticore_domains', 'manticore_rankings'],
		invalidatedCaches: ['domain_cache', 'ranking_cache'],
		syncedDatabases: ['scylla', 'manticore'],
		errors: [],
	}),
};

const mockHealthService = {
	initialize: vi.fn().mockResolvedValue(undefined),
	start: vi.fn().mockResolvedValue(undefined),
	stop: vi.fn().mockResolvedValue(undefined),
	getHealth: vi.fn().mockResolvedValue({
		workerId: 'test-worker',
		status: 'healthy',
		uptime: 60000,
		lastHealthCheck: new Date(),
		activeJobs: 0,
		queuedJobs: 0,
		completedJobs: 0,
		failedJobs: 0,
		cpuUsage: 25,
		memoryUsage: 45,
		diskUsage: 30,
		databases: {
			scylla: { connected: true, lastCheck: new Date(), responseTime: 10 },
			maria: { connected: true, lastCheck: new Date(), responseTime: 5 },
			redis: { connected: true, lastCheck: new Date(), responseTime: 2 },
			manticore: { connected: true, lastCheck: new Date(), responseTime: 8 },
		},
		externalServices: {
			browserless: { connected: true, lastCheck: new Date(), responseTime: 15 },
		},
		lockSystem: {
			healthy: true,
			issues: [],
			metrics: {},
			statistics: {
				totalAcquisitions: 10,
				successfulAcquisitions: 10,
				failedAcquisitions: 0,
				conflicts: 0,
				renewals: 5,
				releases: 8,
				orphanedLocks: 0,
				averageHoldTime: 30000,
				currentActiveLocks: 2,
			},
		},
		issues: [],
	}),
};

const mockMetricsCollector = {
	initialize: vi.fn().mockResolvedValue(undefined),
	start: vi.fn().mockResolvedValue(undefined),
	stop: vi.fn().mockResolvedValue(undefined),
	recordProgress: vi.fn().mockResolvedValue(undefined),
	recordMetric: vi.fn().mockResolvedValue(undefined),
	getMetrics: vi.fn().mockResolvedValue({}),
};

describe('DomainProcessingPipeline', () =>
{
	let pipeline: DomainProcessingPipeline;
	let workerConfig: WorkerConfigType;

	beforeEach(() =>
	{
		// Reset all mocks
		vi.clearAllMocks();

		// Re-setup the health service mock
		mockHealthService.getHealth.mockResolvedValue({
			workerId: 'test-worker',
			status: 'healthy',
			uptime: 60000,
			lastHealthCheck: new Date(),
			activeJobs: 0,
			queuedJobs: 0,
			completedJobs: 0,
			failedJobs: 0,
			cpuUsage: 25,
			memoryUsage: 45,
			diskUsage: 30,
			databases: {
				scylla: { connected: true, lastCheck: new Date(), responseTime: 10 },
				maria: { connected: true, lastCheck: new Date(), responseTime: 5 },
				redis: { connected: true, lastCheck: new Date(), responseTime: 2 },
				manticore: { connected: true, lastCheck: new Date(), responseTime: 8 },
			},
			externalServices: {
				browserless: { connected: true, lastCheck: new Date(), responseTime: 15 },
			},
			lockSystem: {
				healthy: true,
				issues: [],
				metrics: {},
				statistics: {
					totalAcquisitions: 10,
					successfulAcquisitions: 10,
					failedAcquisitions: 0,
					conflicts: 0,
					renewals: 5,
					releases: 8,
					orphanedLocks: 0,
					averageHoldTime: 30000,
					currentActiveLocks: 2,
				},
			},
			issues: [],
		});

		workerConfig = {
			workerId: 'test-worker-1',
			maxConcurrentTasks: 5,
			scyllaHosts: ['localhost:9042'],
			mariaHost: 'localhost',
			mariaPort: 3306,
			mariaUser: 'test',
			mariaPassword: 'test',
			mariaDatabase: 'test_db',
			redisUrl: 'redis://localhost:6379',
			manticoreHost: 'localhost',
			manticorePort: 9308,
			browserlessUrl: 'http://localhost:3000',
			imageProxyUrl: 'https://images.weserv.nl',
			crawlTimeout: 30000,
			rankingUpdateBatchSize: 100,
			jobRetryAttempts: 3,
			jobRetryDelay: 5000,
			logLevel: 'info',
			metricsEnabled: true,
			healthCheckInterval: 30000,
		};

		pipeline = new DomainProcessingPipeline(
			workerConfig,
			mockDatabaseManager as any,
			mockLockManager as any,
			mockCrawlerOrchestrator as any,
			mockRankingManager as any,
			mockIndexingService as any,
			mockHealthService as any,
			mockMetricsCollector as any,
		);
	});

	afterEach(() =>
	{
		vi.restoreAllMocks();
	});

	describe('initialization', () =>
	{
		it('should initialize successfully with valid configuration', async () =>
		{
			await expect(pipeline.initialize()).resolves.not.toThrow();
		});

		it('should verify component health during initialization', async () =>
		{
			await pipeline.initialize();
			expect(mockHealthService.getHealth).toHaveBeenCalled();
		});
	});

	describe('domain processing', () =>
	{
		beforeEach(async () =>
		{
			await pipeline.initialize();
		});

		it('should process a domain through complete pipeline successfully', async () =>
		{
			const domainJob: DomainJobType = {
				id: 'test-job-123',
				domain: 'example.com',
				priority: 'medium',
				crawlType: 'full',
				scheduledAt: new Date(),
				createdAt: new Date(),
				tasks: [],
				retryCount: 0,
				maxRetries: 3,
				requestedBy: 'test-system',
				metadata: {},
			};

			const result = await pipeline.processDomain(domainJob);

			// Verify the result structure
			expect(result).toMatchObject({
				domain: 'example.com',
				jobId: 'test-job-123',
				status: 'success',
				crawlingResult: {
					domain: 'example.com',
					status: 'success',
					completedModules: ['dns', 'robots', 'homepage'],
					failedModules: [],
				},
				rankingResult: {
					domain: 'example.com',
					status: 'success',
					compositeScore: 85.75,
					grade: 'B+',
				},
				indexingResult: {
					domain: 'example.com',
					status: 'success',
					updatedIndexes: ['manticore_domains', 'manticore_rankings'],
				},
			});

			expect(result.totalProcessingTime).toBeGreaterThan(0);
			expect(result.lockAcquired).toBe(true);
		});

		it('should acquire and release domain lock during processing', async () =>
		{
			const domainJob: DomainJobType = {
				id: 'test-job-456',
				domain: 'test.com',
				priority: 'high',
				crawlType: 'quick',
				scheduledAt: new Date(),
				createdAt: new Date(),
				tasks: [],
				retryCount: 0,
				maxRetries: 3,
				requestedBy: 'test-system',
				metadata: {},
			};

			await pipeline.processDomain(domainJob);

			// Verify lock operations
			expect(mockLockManager.acquireLock).toHaveBeenCalledWith(
				'test.com',
				'test-worker-1',
				300000, // 5 minutes TTL
			);
			expect(mockLockManager.releaseLock).toHaveBeenCalledWith(
				'test.com',
				'test-worker-1',
			);
		});

		it('should execute all three phases in sequence', async () =>
		{
			const domainJob: DomainJobType = {
				id: 'test-job-789',
				domain: 'sequential.com',
				priority: 'low',
				crawlType: 'full',
				scheduledAt: new Date(),
				createdAt: new Date(),
				tasks: [],
				retryCount: 0,
				maxRetries: 3,
				requestedBy: 'test-system',
				metadata: {},
			};

			await pipeline.processDomain(domainJob);

			// Verify all phases were executed
			expect(mockCrawlerOrchestrator.collectData).toHaveBeenCalledWith({
				domain: 'sequential.com',
				modules: ['dns', 'robots', 'ssl', 'homepage', 'favicon', 'screenshot', 'performance', 'content'],
				priority: 'low',
				timeout: 30000,
				retryPolicy: {
					maxRetries: 3,
					backoffMultiplier: 2,
					maxBackoffDelay: 60000,
				},
			});

			expect(mockRankingManager.calculateRanking).toHaveBeenCalledWith(
				'sequential.com',
				expect.objectContaining({
					crawlData: expect.any(Object),
					recalculateHistory: false,
					validateResults: true,
				}),
			);

			expect(mockIndexingService.indexDomain).toHaveBeenCalledWith(
				'sequential.com',
				expect.objectContaining({
					updateSearchIndex: true,
					invalidateCache: true,
					syncDatabases: ['scylla', 'manticore'],
					rankingData: expect.any(Object),
				}),
			);
		});

		it('should handle crawling phase failure gracefully', async () =>
		{
			// Mock crawling failure
			mockCrawlerOrchestrator.collectData.mockRejectedValueOnce(
				new Error('Network timeout during crawling'),
			);

			const domainJob: DomainJobType = {
				id: 'test-job-error',
				domain: 'error.com',
				priority: 'medium',
				crawlType: 'full',
				scheduledAt: new Date(),
				createdAt: new Date(),
				tasks: [],
				retryCount: 0,
				maxRetries: 1, // Allow one retry
				requestedBy: 'test-system',
				metadata: {},
			};

			const result = await pipeline.processDomain(domainJob);

			// Should return failure result
			expect(result.status).toBe('failure');
			expect(result.crawlingResult.status).toBe('failure');
			expect(result.errors).toHaveLength(1);
			expect(result.errors[0].message).toContain('Network timeout during crawling');
		});

		it('should continue with ranking phase even if crawling partially fails', async () =>
		{
			// Mock partial crawling success
			mockCrawlerOrchestrator.collectData.mockResolvedValueOnce({
				domain: 'partial.com',
				execution: {
					success: false,
					completedModules: ['dns', 'robots'],
					failedModules: ['homepage', 'ssl'],
					results: {
						dns: { records: ['A'] },
						robots: { allowed: true },
					},
					errors: [{ message: 'Homepage timeout' }],
				},
				validation: {
					isComplete: false,
					completenessScore: 50,
					missingCritical: ['homepage'],
					missingOptional: ['ssl'],
					availableData: ['dns', 'robots'],
					dataQuality: {},
					recommendations: [],
				},
				recommendations: [],
				metadata: {
					startTime: new Date(),
					endTime: new Date(),
					totalDuration: 1000,
					resourceUsage: {
						cpuTime: 100,
						memoryPeak: 1024 * 1024,
						memoryAverage: 512 * 1024,
						diskIO: 0,
						networkIO: 1024,
					},
				},
			});

			const domainJob: DomainJobType = {
				id: 'test-job-partial',
				domain: 'partial.com',
				priority: 'medium',
				crawlType: 'full',
				scheduledAt: new Date(),
				createdAt: new Date(),
				tasks: [],
				retryCount: 0,
				maxRetries: 3,
				requestedBy: 'test-system',
				metadata: {},
			};

			const result = await pipeline.processDomain(domainJob);

			// Should continue with ranking despite partial crawling failure
			expect(result.crawlingResult.status).toBe('partial');
			expect(result.rankingResult.status).toBe('success');
			expect(mockRankingManager.calculateRanking).toHaveBeenCalled();
		});

		it('should track processing time and resource usage', async () =>
		{
			const domainJob: DomainJobType = {
				id: 'test-job-metrics',
				domain: 'metrics.com',
				priority: 'medium',
				crawlType: 'full',
				scheduledAt: new Date(),
				createdAt: new Date(),
				tasks: [],
				retryCount: 0,
				maxRetries: 3,
				requestedBy: 'test-system',
				metadata: {},
			};

			const result = await pipeline.processDomain(domainJob);

			// Verify timing information
			expect(result.totalProcessingTime).toBeGreaterThan(0);
			expect(result.phaseTimings.crawling).toBeGreaterThanOrEqual(0);
			expect(result.phaseTimings.ranking).toBeGreaterThanOrEqual(0);
			expect(result.phaseTimings.indexing).toBeGreaterThanOrEqual(0);

			// Verify resource usage tracking
			expect(result.resourceUsage).toMatchObject({
				cpuTime: expect.any(Number),
				memoryPeak: expect.any(Number),
				memoryAverage: expect.any(Number),
				diskIO: expect.any(Number),
				networkIO: expect.any(Number),
			});

			// Verify task metrics
			expect(result.taskMetrics).toHaveLength(3); // One for each phase
			expect(result.taskMetrics[0].taskName).toBe('crawling');
			expect(result.taskMetrics[1].taskName).toBe('ranking');
			expect(result.taskMetrics[2].taskName).toBe('indexing');
		});
	});

	describe('error handling', () =>
	{
		beforeEach(async () =>
		{
			await pipeline.initialize();
		});

		it('should handle lock acquisition failure', async () =>
		{
			// Mock lock acquisition failure
			mockLockManager.acquireLock.mockResolvedValueOnce({
				success: false,
				error: 'Domain already locked by another worker',
			});

			const domainJob: DomainJobType = {
				id: 'test-job-lock-fail',
				domain: 'locked.com',
				priority: 'medium',
				crawlType: 'full',
				scheduledAt: new Date(),
				createdAt: new Date(),
				tasks: [],
				retryCount: 0,
				maxRetries: 3,
				requestedBy: 'test-system',
				metadata: {},
			};

			await expect(pipeline.processDomain(domainJob)).rejects.toThrow(
				'Failed to acquire lock',
			);
		});

		it('should release lock even if processing fails', async () =>
		{
			// Mock ranking failure
			mockRankingManager.calculateRanking.mockRejectedValueOnce(
				new Error('Database connection lost'),
			);

			const domainJob: DomainJobType = {
				id: 'test-job-cleanup',
				domain: 'cleanup.com',
				priority: 'medium',
				crawlType: 'full',
				scheduledAt: new Date(),
				createdAt: new Date(),
				tasks: [],
				retryCount: 0,
				maxRetries: 0, // No retries
				requestedBy: 'test-system',
				metadata: {},
			};

			const result = await pipeline.processDomain(domainJob);

			// Verify lock was still released
			expect(mockLockManager.releaseLock).toHaveBeenCalledWith(
				'cleanup.com',
				'test-worker-1',
			);

			// Verify failure was handled
			expect(result.status).toBe('partial'); // Crawling succeeded, ranking failed
		});
	});

	describe('configuration', () =>
	{
		it('should use different crawling modules based on crawl type', async () =>
		{
			await pipeline.initialize();

			const quickJob: DomainJobType = {
				id: 'quick-job',
				domain: 'quick.com',
				priority: 'medium',
				crawlType: 'quick',
				scheduledAt: new Date(),
				createdAt: new Date(),
				tasks: [],
				retryCount: 0,
				maxRetries: 3,
				requestedBy: 'test-system',
				metadata: {},
			};

			await pipeline.processDomain(quickJob);

			expect(mockCrawlerOrchestrator.collectData).toHaveBeenCalledWith(
				expect.objectContaining({
					modules: ['dns', 'robots', 'homepage'],
				}),
			);
		});

		it('should respect timeout configurations', async () =>
		{
			const customConfig = {
				...workerConfig,
				crawlTimeout: 10000, // 10 seconds
			};

			const customPipeline = new DomainProcessingPipeline(
				customConfig,
				mockDatabaseManager as any,
				mockLockManager as any,
				mockCrawlerOrchestrator as any,
				mockRankingManager as any,
				mockIndexingService as any,
				mockHealthService as any,
				mockMetricsCollector as any,
			);

			await customPipeline.initialize();

			const domainJob: DomainJobType = {
				id: 'timeout-job',
				domain: 'timeout.com',
				priority: 'medium',
				crawlType: 'full',
				scheduledAt: new Date(),
				createdAt: new Date(),
				tasks: [],
				retryCount: 0,
				maxRetries: 3,
				requestedBy: 'test-system',
				metadata: {},
			};

			await customPipeline.processDomain(domainJob);

			expect(mockCrawlerOrchestrator.collectData).toHaveBeenCalledWith(
				expect.objectContaining({
					timeout: 10000,
				}),
			);
		});
	});

	describe('statistics', () =>
	{
		beforeEach(async () =>
		{
			await pipeline.initialize();
		});

		it('should provide pipeline statistics', () =>
		{
			const stats = pipeline.getStatistics();

			expect(stats).toMatchObject({
				activeExecutions: expect.any(Number),
				totalExecutions: expect.any(Number),
				averageProcessingTime: expect.any(Number),
				successRate: expect.any(Number),
				phaseStatistics: expect.any(Object),
			});
		});

		it('should track execution history', async () =>
		{
			const domainJob: DomainJobType = {
				id: 'stats-job',
				domain: 'stats.com',
				priority: 'medium',
				crawlType: 'full',
				scheduledAt: new Date(),
				createdAt: new Date(),
				tasks: [],
				retryCount: 0,
				maxRetries: 3,
				requestedBy: 'test-system',
				metadata: {},
			};

			await pipeline.processDomain(domainJob);

			const stats = pipeline.getStatistics();
			expect(stats.totalExecutions).toBe(1);
			expect(stats.successRate).toBe(100);
		});
	});
});
