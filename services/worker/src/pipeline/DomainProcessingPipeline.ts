/**
 * Domain Processing Pipeline
 *
 * Core orchestrator that manages the complete domain processing workflow:
 * crawling → analysis → ranking → indexing
 *
 * This pipeline coordinates all extracted functionality from the three original services
 * (crawler, ranking-engine, scheduler) into a unified processing flow with comprehensive
 * error handling, retry logic, progress tracking, and resource management.
 */

import { RedisClientWrapper } from '@shared/database/RedisClient';
import { logger as loggerFactory } from '@shared/utils/Logger';
import { MetricsCollector, CacheManager } from '@shared';
import type {
	DomainJobType,
	ProcessingResultType,
	CrawlingResultType,
	RankingResultType,
	IndexingResultType,
	ProcessingPhaseType,
	ProcessingErrorType,
	ProcessingWarningType,
	ResourceUsageType,
	TaskMetricsType,
	WorkerConfigType,
} from '../types/WorkerTypes';

// Import extracted components
import { DomainLockManager } from '../locking/DomainLockManager';
import { DataCollectionOrchestrator } from '../crawler/core/DataCollectionOrchestrator';
import { RankingManager } from '../ranking/RankingManager';
import { SearchIndexingService } from '../indexing/SearchIndexingService';
import { WorkerDatabaseManager } from '../database/WorkerDatabaseManager';

// Import error handling and monitoring
import { HealthCheck } from '../monitoring/HealthCheck';

const logger = loggerFactory.getLogger('DomainProcessingPipeline');

type PipelineConfigType =
{
	// Timeout settings
	crawlingTimeout: number;
	rankingTimeout: number;
	indexingTimeout: number;
	overallTimeout: number;

	// Retry settings
	maxRetries: number;
	retryDelay: number;
	retryBackoffMultiplier: number;
	maxRetryDelay: number;

	// Resource management
	enableResourceMonitoring: boolean;
	memoryThreshold: number;
	cpuThreshold: number;

	// Progress tracking
	enableProgressTracking: boolean;
	progressUpdateInterval: number;

	// Error handling
	enableGracefulDegradation: boolean;
	criticalErrorThreshold: number;
};

type PipelinePhaseResultType =
{
	phase: ProcessingPhaseType;
	status: 'success' | 'failure' | 'partial' | 'skipped';
	startTime: Date;
	endTime: Date;
	duration: number;
	retryCount: number;
	errors: ProcessingErrorType[];
	warnings: ProcessingWarningType[];
	resourceUsage: ResourceUsageType;
	data?: Record<string, unknown>;
};

type PipelineExecutionContextType =
{
	domain: string;
	jobId: string;
	workerId: string;
	startTime: Date;
	lockId?: string;
	lockAcquired: boolean;
	currentPhase: ProcessingPhaseType | null;
	phaseResults: PipelinePhaseResultType[];
	totalRetries: number;
	resourceUsage: ResourceUsageType;
	errors: ProcessingErrorType[];
	warnings: ProcessingWarningType[];
};

type TaskRetryDecisionType =
{
	shouldRetry: boolean;
	retryDelay: number;
	skipPhase: boolean;
	degradeGracefully: boolean;
	reason: string;
};

class DomainProcessingPipeline
{
	private config: PipelineConfigType;
	private workerId: string;
	private databaseManager: WorkerDatabaseManager;
	private lockManager: DomainLockManager;
	private crawlerOrchestrator: DataCollectionOrchestrator;
	private rankingManager: RankingManager;
	private indexingService: SearchIndexingService | null;
	private healthCheck: HealthCheck;
	private metricsCollector: MetricsCollector;
	private cacheManager: CacheManager;
	private redisClient: RedisClientWrapper;

	// Pipeline state
	private isInitialized: boolean = false;
	private activeExecutions: Map<string, PipelineExecutionContextType> = new Map();
	private executionHistory: PipelineExecutionContextType[] = [];

	constructor(
		workerConfig: WorkerConfigType,
		databaseManager: WorkerDatabaseManager,
		lockManager: DomainLockManager,
		crawlerOrchestrator: DataCollectionOrchestrator,
		rankingManager: RankingManager,
		indexingService: SearchIndexingService | null,
	)
	{
		this.workerId = workerConfig.workerId;
		this.databaseManager = databaseManager;
		this.lockManager = lockManager;
		this.crawlerOrchestrator = crawlerOrchestrator;
		this.rankingManager = rankingManager;
		this.indexingService = indexingService;
		this.healthCheck = new HealthCheck(this.databaseManager);
		this.metricsCollector = new MetricsCollector();

		// Initialize Redis client and cache manager
		this.redisClient = new RedisClientWrapper();
		this.cacheManager = new CacheManager(this.redisClient);

		this.config = this.createPipelineConfig(workerConfig);
	}

	/**
	 * Initialize the processing pipeline
	 */
	async initialize(): Promise<void>
	{
		if (this.isInitialized)
		{
			logger.warn('Pipeline already initialized');
			return;
		}

		try
		{
			logger.info('Initializing Domain Processing Pipeline', { workerId: this.workerId });

			// Initialize Redis connection
			if (!this.redisClient.isReady()) {
				await this.redisClient.connect();
			}

			// Verify all components are ready
			await this.verifyComponentHealth();

			this.isInitialized = true;
			logger.info('Domain Processing Pipeline initialized successfully');
		}
		catch (error)
		{
			logger.error('Failed to initialize pipeline:', error);
			throw error;
		}
	}

	/**
	 * Process a domain through the complete pipeline
	 */
	async processDomain(domainJob: DomainJobType): Promise<ProcessingResultType>
	{
		if (!this.isInitialized)
		{
			throw new Error('Pipeline not initialized');
		}

		const executionContext = this.createExecutionContext(domainJob);
		this.activeExecutions.set(domainJob.id, executionContext);

		try
		{
			logger.info('Starting domain processing', {
				domain: domainJob.domain,
				jobId: domainJob.id,
				workerId: this.workerId,
			});

			// Check cache for recently processed results
			try {
				const cached = await this.cacheManager.getCachedDomainAnalysis(domainJob.domain);
				if (cached && typeof cached === 'object') {
					// Check if cached result is recent enough (last 6 hours for worker pipeline)
					const cachedResult = cached as any;
					if (cachedResult.processedAt &&
						Date.now() - new Date(cachedResult.processedAt).getTime() < 6 * 60 * 60 * 1000) {
						logger.info('Using cached processing result', {
							domain: domainJob.domain,
							cachedAt: cachedResult.processedAt
						});
						return cachedResult as ProcessingResultType;
					}
				}
			} catch (error) {
				// Cache miss or error - continue with processing
			}

			// Start resource monitoring
			const resourceMonitor = this.startResourceMonitoring(executionContext);

			// Acquire domain lock
			await this.acquireDomainLock(executionContext);

			// Execute processing phases sequentially
			const crawlingResult = await this.executeCrawlingPhase(executionContext, domainJob);
			const rankingResult = await this.executeRankingPhase(executionContext, domainJob, crawlingResult);
			const indexingResult = await this.executeIndexingPhase(executionContext, domainJob, rankingResult);

			// Stop resource monitoring
			this.stopResourceMonitoring(resourceMonitor, executionContext);

			// Create final result
			const result = this.createProcessingResult(executionContext, crawlingResult, rankingResult, indexingResult);

			// Cache the successful result
			try {
				if (result.status === 'completed') {
					const cacheableResult = {
						...result,
						processedAt: new Date().toISOString()
					};
					// Cache for 6 hours (21600 seconds)
					await this.cacheManager.cacheDomainAnalysis(domainJob.domain, cacheableResult, 21600);
				}
			} catch (error) {
				// Continue even if caching fails
				logger.warn('Failed to cache processing result', { domain: domainJob.domain, error });
			}

			logger.info('Domain processing completed', {
				domain: domainJob.domain,
				jobId: domainJob.id,
				status: result.status,
				totalTime: result.totalProcessingTime,
			});

			return result;
		}
		catch (error)
		{
			logger.error('Domain processing failed:', error, {
				domain: domainJob.domain,
				jobId: domainJob.id,
			});

			// Handle critical pipeline failure
			return this.handlePipelineFailure(executionContext, error as Error);
		}
		finally
		{
			// Always release lock and cleanup
			await this.releaseDomainLock(executionContext);
			this.cleanupExecution(executionContext);
		}
	}

	/**
	 * Execute crawling phase
	 */
	private async executeCrawlingPhase(
		context: PipelineExecutionContextType,
		domainJob: DomainJobType,
	): Promise<CrawlingResultType>
	{
		const phaseResult = this.createPhaseResult('crawling');
		context.currentPhase = 'crawling';
		context.phaseResults.push(phaseResult);

		try
		{
			logger.info('Starting crawling phase', {
				domain: context.domain,
				jobId: context.jobId,
			});

			// Update progress
			await this.updateProgress(context, 'crawling', 0);

			// Execute crawling with timeout
			const crawlingPromise = this.crawlerOrchestrator.collectData({
				domain: context.domain,
				modules: this.getCrawlingModules(domainJob),
				priority: domainJob.priority,
				timeout: this.config.crawlingTimeout,
				retryPolicy: {
					maxRetries: this.config.maxRetries,
					backoffMultiplier: this.config.retryBackoffMultiplier,
					maxBackoffDelay: this.config.maxRetryDelay,
				},
			});

			const timeoutPromise = new Promise<never>((_, reject) =>
			{
				setTimeout(() => reject(new Error('Crawling phase timeout')), this.config.crawlingTimeout);
			});

			const crawlingResponse = await Promise.race([crawlingPromise, timeoutPromise]);

			// Update progress
			await this.updateProgress(context, 'crawling', 100);

			// Convert to standard result format
			const result: CrawlingResultType = {
				domain: context.domain,
				status: crawlingResponse.execution.success ? 'success' : 'partial',
				completedModules: crawlingResponse.execution.completedModules,
				failedModules: crawlingResponse.execution.failedModules,
				data: crawlingResponse.execution.results,
				errors: this.convertToProcessingErrors(crawlingResponse.execution.errors, 'crawling'),
			};

			phaseResult.status = result.status;
			phaseResult.data = result.data;
			phaseResult.errors = result.errors;

			logger.info('Crawling phase completed', {
				domain: context.domain,
				status: result.status,
				completedModules: result.completedModules.length,
				failedModules: result.failedModules.length,
			});

			return result;
		}
		catch (error)
		{
			logger.error('Crawling phase failed:', error, {
				domain: context.domain,
				jobId: context.jobId,
			});

			const processingError = this.createProcessingError(
				'CRAWLING_PHASE_FAILED',
				`Crawling phase failed: ${(error as Error).message}`,
				'crawling',
				true,
			);

			phaseResult.status = 'failure';
			phaseResult.errors = [processingError];
			context.errors.push(processingError);

			// Determine if we should retry or fail
			const retryDecision = await this.handleTaskFailure('crawling', error as Error, context);
			if (retryDecision.shouldRetry && context.totalRetries < this.config.maxRetries)
			{
				logger.info('Retrying crawling phase', {
					domain: context.domain,
					retryCount: context.totalRetries + 1,
					delay: retryDecision.retryDelay,
				});

				await this.delay(retryDecision.retryDelay);
				context.totalRetries++;
				return this.executeCrawlingPhase(context, domainJob);
			}

			// Return failed result
			return {
				domain: context.domain,
				status: 'failure',
				completedModules: [],
				failedModules: [],
				data: {},
				errors: [processingError],
			};
		}
		finally
		{
			phaseResult.endTime = new Date();
			phaseResult.duration = phaseResult.endTime.getTime() - phaseResult.startTime.getTime();
			context.currentPhase = null;
		}
	}

	/**
	 * Execute ranking phase
	 */
	private async executeRankingPhase(
		context: PipelineExecutionContextType,
		domainJob: DomainJobType,
		crawlingResult: CrawlingResultType,
	): Promise<RankingResultType>
	{
		const phaseResult = this.createPhaseResult('ranking');
		context.currentPhase = 'ranking';
		context.phaseResults.push(phaseResult);

		try
		{
			logger.info('Starting ranking phase', {
				domain: context.domain,
				jobId: context.jobId,
			});

			// Skip ranking if crawling completely failed
			if (crawlingResult.status === 'failure' && crawlingResult.completedModules.length === 0)
			{
				logger.warn('Skipping ranking phase due to crawling failure', {
					domain: context.domain,
				});

				phaseResult.status = 'skipped';
				return {
					domain: context.domain,
					status: 'failure',
					scores: {},
					compositeScore: 0,
					grade: 'F',
					ranking: 0,
					errors: [this.createProcessingError(
						'RANKING_SKIPPED',
						'Ranking skipped due to crawling failure',
						'ranking',
						false,
					)],
				};
			}

			// Update progress
			await this.updateProgress(context, 'ranking', 0);

			// Execute ranking calculation
			const rankingPromise = this.rankingManager.calculateRanking(context.domain, {
				crawlData: crawlingResult.data,
				recalculateHistory: false,
				validateResults: true,
			});

			const timeoutPromise = new Promise<never>((_, reject) =>
			{
				setTimeout(() => reject(new Error('Ranking phase timeout')), this.config.rankingTimeout);
			});

			const rankingResponse = await Promise.race([rankingPromise, timeoutPromise]);

			// Update progress
			await this.updateProgress(context, 'ranking', 100);

			const result: RankingResultType = {
				domain: context.domain,
				status: 'success',
				scores: rankingResponse.scores,
				compositeScore: rankingResponse.compositeScore,
				grade: rankingResponse.grade,
				ranking: rankingResponse.globalRanking || 0,
				errors: [],
			};

			phaseResult.status = result.status;
			phaseResult.data = { scores: result.scores, compositeScore: result.compositeScore };

			logger.info('Ranking phase completed', {
				domain: context.domain,
				compositeScore: result.compositeScore,
				grade: result.grade,
			});

			return result;
		}
		catch (error)
		{
			logger.error('Ranking phase failed:', error, {
				domain: context.domain,
				jobId: context.jobId,
			});

			const processingError = this.createProcessingError(
				'RANKING_PHASE_FAILED',
				`Ranking phase failed: ${(error as Error).message}`,
				'ranking',
				true,
			);

			phaseResult.status = 'failure';
			phaseResult.errors = [processingError];
			context.errors.push(processingError);

			// Determine if we should retry or continue with degraded functionality
			const retryDecision = await this.handleTaskFailure('ranking', error as Error, context);
			if (retryDecision.shouldRetry && context.totalRetries < this.config.maxRetries)
			{
				logger.info('Retrying ranking phase', {
					domain: context.domain,
					retryCount: context.totalRetries + 1,
					delay: retryDecision.retryDelay,
				});

				await this.delay(retryDecision.retryDelay);
				context.totalRetries++;
				return this.executeRankingPhase(context, domainJob, crawlingResult);
			}

			// Return failed result but allow pipeline to continue
			return {
				domain: context.domain,
				status: 'failure',
				scores: {},
				compositeScore: 0,
				grade: 'F',
				ranking: 0,
				errors: [processingError],
			};
		}
		finally
		{
			phaseResult.endTime = new Date();
			phaseResult.duration = phaseResult.endTime.getTime() - phaseResult.startTime.getTime();
			context.currentPhase = null;
		}
	}

	/**
	 * Execute indexing phase
	 */
	private async executeIndexingPhase(
		context: PipelineExecutionContextType,
		domainJob: DomainJobType,
		rankingResult: RankingResultType,
	): Promise<IndexingResultType>
	{
		const phaseResult = this.createPhaseResult('indexing');
		context.currentPhase = 'indexing';
		context.phaseResults.push(phaseResult);

		try
		{
			logger.info('Starting indexing phase', {
				domain: context.domain,
				jobId: context.jobId,
			});


				// If indexing service is unavailable, skip indexing with a degraded result
				if (!this.indexingService)
				{
					const processingError = this.createProcessingError(
						'INDEXING_SERVICE_UNAVAILABLE',
						'Indexing skipped: indexing service not available (degraded mode)',
						'indexing',
						false,
					);
					phaseResult.status = 'success'; // Pipeline continues; indexing treated as non-blocking
					return {
						domain: context.domain,
						status: 'failure',
						updatedIndexes: [],
						invalidatedCaches: [],
						syncedDatabases: [],
						errors: [processingError],
					};
				}

			// Update progress
			await this.updateProgress(context, 'indexing', 0);

			// Execute indexing operations
			const indexingPromise = this.indexingService.indexDomain(context.domain, {
				updateSearchIndex: true,
				invalidateCache: true,
				syncDatabases: ['scylla', 'manticore'],
				rankingData: rankingResult,
			});

			const timeoutPromise = new Promise<never>((_, reject) =>
			{
				setTimeout(() => reject(new Error('Indexing phase timeout')), this.config.indexingTimeout);
			});

			const indexingResponse = await Promise.race([indexingPromise, timeoutPromise]);

			// Update progress
			await this.updateProgress(context, 'indexing', 100);

			const result: IndexingResultType = {
				domain: context.domain,
				status: indexingResponse.success ? 'success' : 'partial',
				updatedIndexes: indexingResponse.updatedIndexes || [],
				invalidatedCaches: indexingResponse.invalidatedCaches || [],
				syncedDatabases: indexingResponse.syncedDatabases || [],
				errors: indexingResponse.errors ? this.convertToProcessingErrors(indexingResponse.errors, 'indexing') : [],
			};

			phaseResult.status = result.status;
			phaseResult.data = {
				updatedIndexes: result.updatedIndexes,
				syncedDatabases: result.syncedDatabases,
			};

			logger.info('Indexing phase completed', {
				domain: context.domain,
				status: result.status,
				updatedIndexes: result.updatedIndexes.length,
			});

			return result;
		}
		catch (error)
		{
			logger.error('Indexing phase failed:', error, {
				domain: context.domain,
				jobId: context.jobId,
			});

			const processingError = this.createProcessingError(
				'INDEXING_PHASE_FAILED',
				`Indexing phase failed: ${(error as Error).message}`,
				'indexing',
				true,
			);

			phaseResult.status = 'failure';
			phaseResult.errors = [processingError];
			context.errors.push(processingError);

			// Indexing failures are usually not critical - log and continue
			const warning = this.createProcessingWarning(
				'INDEXING_DEGRADED',
				'Indexing failed but processing completed',
				'indexing',
			);
			context.warnings.push(warning);

			return {
				domain: context.domain,
				status: 'failure',
				updatedIndexes: [],
				invalidatedCaches: [],
				syncedDatabases: [],
				errors: [processingError],
			};
		}
		finally
		{
			phaseResult.endTime = new Date();
			phaseResult.duration = phaseResult.endTime.getTime() - phaseResult.startTime.getTime();
			context.currentPhase = null;
		}
	}

	/**
	 * Handle task failure and determine retry strategy
	 */
	private async handleTaskFailure(
		task: string,
		error: Error,
		context: PipelineExecutionContextType,
	): Promise<TaskRetryDecisionType>
	{
		logger.warn('Task failure occurred', {
			task,
			domain: context.domain,
			error: error.message,
			retryCount: context.totalRetries,
		});

		// Classify error type
		const isTransient = this.isTransientError(error);
		const isCritical = this.isCriticalError(error);
		const hasRetriesLeft = context.totalRetries < this.config.maxRetries;

		// Calculate retry delay with exponential backoff
		const baseDelay = this.config.retryDelay;
		const backoffMultiplier = this.config.retryBackoffMultiplier**context.totalRetries;
		const retryDelay = Math.min(baseDelay * backoffMultiplier, this.config.maxRetryDelay);

		// Determine retry strategy
		if (isTransient && hasRetriesLeft && !isCritical)
		{
			return {
				shouldRetry: true,
				retryDelay,
				skipPhase: false,
				degradeGracefully: false,
				reason: 'Transient error with retries available',
			};
		}

		if (isCritical)
		{
			return {
				shouldRetry: false,
				retryDelay: 0,
				skipPhase: true,
				degradeGracefully: this.config.enableGracefulDegradation,
				reason: 'Critical error - cannot retry',
			};
		}

		return {
			shouldRetry: false,
			retryDelay: 0,
			skipPhase: false,
			degradeGracefully: true,
			reason: 'Max retries exceeded or non-retryable error',
		};
	}

	/**
	 * Acquire domain lock to prevent concurrent processing
	 */
	private async acquireDomainLock(context: PipelineExecutionContextType): Promise<void>
	{
		try
		{
			logger.debug('Acquiring domain lock', {
				domain: context.domain,
				workerId: this.workerId,
			});

			const lockResult = await this.lockManager.acquireLock(
				context.domain,
				this.workerId,
				300000, // 5 minutes TTL
			);

			if (lockResult.success && lockResult.lockId)
			{
				context.lockAcquired = true;
				context.lockId = lockResult.lockId;
				logger.debug('Domain lock acquired', {
					domain: context.domain,
					lockId: lockResult.lockId,
				});
			}
			else
			{
				throw new Error(`Failed to acquire lock: ${lockResult.error || 'Unknown error'}`);
			}
		}
		catch (error)
		{
			logger.error('Failed to acquire domain lock:', error, {
				domain: context.domain,
				workerId: this.workerId,
			});
			throw error;
		}
	}

	/**
	 * Release domain lock
	 */
	private async releaseDomainLock(context: PipelineExecutionContextType): Promise<void>
	{
		if (!context.lockAcquired || !context.lockId)
		{
			return;
		}

		try
		{
			logger.debug('Releasing domain lock', {
				domain: context.domain,
				lockId: context.lockId,
			});

			await this.lockManager.releaseLock(context.domain, this.workerId);
			context.lockAcquired = false;
			context.lockId = undefined;

			logger.debug('Domain lock released', {
				domain: context.domain,
			});
		}
		catch (error)
		{
			logger.error('Failed to release domain lock:', error, {
				domain: context.domain,
				lockId: context.lockId,
			});
			// Don't throw - this is cleanup
		}
	}

	/**
	 * Update processing progress
	 */
	private async updateProgress(
		context: PipelineExecutionContextType,
		phase: ProcessingPhaseType,
		progress: number,
	): Promise<void>
	{
		if (!this.config.enableProgressTracking)
		{
			return;
		}

		try
		{
			// Update progress in database or cache
			// Record progress if metrics collector supports it
			if (this.metricsCollector && typeof (this.metricsCollector as any).recordProgress === 'function')
			{
				await (this.metricsCollector as any).recordProgress(context.jobId, phase, progress);
			}

			logger.debug('Progress updated', {
				domain: context.domain,
				jobId: context.jobId,
				phase,
				progress,
			});
		}
		catch (error)
		{
			logger.warn('Failed to update progress:', error);
			// Don't throw - this is non-critical
		}
	}

	/**
	 * Start resource monitoring for execution
	 */
	private startResourceMonitoring(context: PipelineExecutionContextType): NodeJS.Timeout | null
	{
		if (!this.config.enableResourceMonitoring)
		{
			return null;
		}

		return setInterval(() =>
		{
			const memUsage = process.memoryUsage();
			const memoryUsagePercent = (memUsage.heapUsed / memUsage.heapTotal) * 100;

			// Update resource usage in context
			context.resourceUsage = {
				cpuTime: process.cpuUsage().user + process.cpuUsage().system,
				memoryPeak: Math.max(context.resourceUsage.memoryPeak, memUsage.heapUsed),
				memoryAverage: (context.resourceUsage.memoryAverage + memUsage.heapUsed) / 2,
				diskIO: this.estimateDiskIO(context),
				networkIO: this.estimateNetworkIO(context),
			};

			// Check thresholds
			if (memoryUsagePercent > this.config.memoryThreshold)
			{
				logger.warn('High memory usage detected', {
					domain: context.domain,
					memoryUsage: memoryUsagePercent,
					threshold: this.config.memoryThreshold,
				});
			}
		}, 1000);
	}

	/**
	 * Stop resource monitoring
	 */
	private stopResourceMonitoring(
		monitor: NodeJS.Timeout | null,
		context: PipelineExecutionContextType,
	): void
	{
		if (monitor)
		{
			clearInterval(monitor);
		}
	}

	/**
	 * Create execution context for domain processing
	 */
	private createExecutionContext(domainJob: DomainJobType): PipelineExecutionContextType
	{
		return {
			domain: domainJob.domain,
			jobId: domainJob.id,
			workerId: this.workerId,
			startTime: new Date(),
			lockAcquired: false,
			currentPhase: null,
			phaseResults: [],
			totalRetries: 0,
			resourceUsage: {
				cpuTime: 0,
				memoryPeak: 0,
				memoryAverage: 0,
				diskIO: 0,
				networkIO: 0,
			},
			errors: [],
			warnings: [],
		};
	}

	/**
	 * Create phase result structure
	 */
	private createPhaseResult(phase: ProcessingPhaseType): PipelinePhaseResultType
	{
		return {
			phase,
			status: 'success',
			startTime: new Date(),
			endTime: new Date(),
			duration: 0,
			retryCount: 0,
			errors: [],
			warnings: [],
			resourceUsage: {
				cpuTime: 0,
				memoryPeak: 0,
				memoryAverage: 0,
				diskIO: 0,
				networkIO: 0,
			},
		};
	}

	/**
	 * Create final processing result
	 */
	private createProcessingResult(
		context: PipelineExecutionContextType,
		crawlingResult: CrawlingResultType,
		rankingResult: RankingResultType,
		indexingResult: IndexingResultType,
	): ProcessingResultType
	{
		const endTime = new Date();
		const totalProcessingTime = endTime.getTime() - context.startTime.getTime();

		// Determine overall status
		let overallStatus: 'success' | 'failure' | 'partial' = 'success';
		if (crawlingResult.status === 'failure' && rankingResult.status === 'failure')
		{
			overallStatus = 'failure';
		}
		else if (crawlingResult.status !== 'success' || rankingResult.status !== 'success' || indexingResult.status !== 'success')
		{
			overallStatus = 'partial';
		}

		// Calculate phase timings
		const phaseTimings: Record<ProcessingPhaseType, number> = {
			crawling: 0,
			ranking: 0,
			indexing: 0,
		};

		context.phaseResults.forEach((phase) =>
		{
			phaseTimings[phase.phase] = phase.duration;
		});

		// Create task metrics
		const taskMetrics: TaskMetricsType[] = context.phaseResults.map(phase => ({
			taskName: phase.phase,
			executionTime: phase.duration,
			memoryUsage: phase.resourceUsage.memoryPeak,
			success: phase.status === 'success',
			retryCount: phase.retryCount,
		}));

		return {
			domain: context.domain,
			jobId: context.jobId,
			status: overallStatus,
			crawlingResult,
			rankingResult,
			indexingResult,
			totalProcessingTime,
			phaseTimings,
			lockAcquired: context.lockAcquired,
			lockAcquisitionTime: context.lockId ? 100 : undefined, // TODO: Track actual acquisition time
			lockHoldTime: context.lockAcquired ? totalProcessingTime : undefined,
			lockConflicts: 0, // TODO: Track lock conflicts
			errors: context.errors,
			warnings: context.warnings,
			resourceUsage: context.resourceUsage,
			taskMetrics,
		};
	}

	/**
	 * Handle critical pipeline failure
	 */
	private handlePipelineFailure(
		context: PipelineExecutionContextType,
		error: Error,
	): ProcessingResultType
	{
		const processingError = this.createProcessingError(
			'PIPELINE_CRITICAL_FAILURE',
			`Pipeline failed: ${error.message}`,
			context.currentPhase || 'crawling',
			false,
		);

		return {
			domain: context.domain,
			jobId: context.jobId,
			status: 'failure',
			crawlingResult: {
				domain: context.domain,
				status: 'failure',
				completedModules: [],
				failedModules: [],
				data: {},
				errors: [processingError],
			},
			rankingResult: {
				domain: context.domain,
				status: 'failure',
				scores: {},
				compositeScore: 0,
				grade: 'F',
				ranking: 0,
				errors: [processingError],
			},
			indexingResult: {
				domain: context.domain,
				status: 'failure',
				updatedIndexes: [],
				invalidatedCaches: [],
				syncedDatabases: [],
				errors: [processingError],
			},
			totalProcessingTime: new Date().getTime() - context.startTime.getTime(),
			phaseTimings: { crawling: 0, ranking: 0, indexing: 0 },
			lockAcquired: context.lockAcquired,
			errors: [processingError],
			warnings: context.warnings,
			resourceUsage: context.resourceUsage,
			taskMetrics: [],
		};
	}

	/**
	 * Cleanup execution context
	 */
	private cleanupExecution(context: PipelineExecutionContextType): void
	{
		// Remove from active executions
		this.activeExecutions.delete(context.jobId);

		// Add to history (keep last 100 executions)
		this.executionHistory.push(context);
		if (this.executionHistory.length > 100)
		{
			this.executionHistory.shift();
		}

		logger.debug('Execution context cleaned up', {
			domain: context.domain,
			jobId: context.jobId,
		});
	}

	/**
	 * Verify component health before processing
	 */
	private async verifyComponentHealth(): Promise<void>
	{
		try
		{
			const health = await this.healthCheck.check();

			if (!health)
			{
				throw new Error('Health service returned no data');
			}

			if (health.status === 'unhealthy')
			{
				throw new Error('Worker health check failed - cannot process domains');
			}

			if (health.databases?.scylla?.connected === false)
			{
				throw new Error('ScyllaDB connection required for processing');
			}

			if (health.databases?.redis?.connected === false)
			{
				throw new Error('Redis connection required for locking');
			}
		}
		catch (error)
		{
			logger.error('Component health verification failed:', error);
			throw error;
		}
	}

	/**
	 * Get crawling modules for job type
	 */
	private getCrawlingModules(domainJob: DomainJobType): string[]
	{
		switch (domainJob.crawlType)
		{
			case 'quick':
				return ['dns', 'robots', 'homepage'];
			case 'security':
				return ['dns', 'ssl', 'robots', 'homepage'];
			case 'performance':
				return ['dns', 'homepage', 'performance', 'screenshot'];
			case 'full':
			default:
				return ['dns', 'robots', 'ssl', 'homepage', 'favicon', 'screenshot', 'performance', 'content'];
		}
	}

	/**
	 * Create pipeline configuration from worker config
	 */
	private createPipelineConfig(workerConfig: WorkerConfigType): PipelineConfigType
	{
		return {
			crawlingTimeout: workerConfig.crawlTimeout,
			rankingTimeout: 60000, // 1 minute
			indexingTimeout: 30000, // 30 seconds
			overallTimeout: workerConfig.crawlTimeout + 90000, // crawl timeout + 1.5 minutes
			maxRetries: workerConfig.jobRetryAttempts,
			retryDelay: workerConfig.jobRetryDelay,
			retryBackoffMultiplier: 2,
			maxRetryDelay: 60000, // 1 minute
			enableResourceMonitoring: true,
			memoryThreshold: 80, // 80% memory usage threshold
			cpuThreshold: 90, // 90% CPU usage threshold
			enableProgressTracking: true,
			progressUpdateInterval: 5000, // 5 seconds
			enableGracefulDegradation: true,
			criticalErrorThreshold: 3,
		};
	}

	/**
	 * Create processing error
	 */
	private createProcessingError(
		code: string,
		message: string,
		phase: ProcessingPhaseType,
		retryable: boolean,
		details?: Record<string, unknown>,
	): ProcessingErrorType
	{
		return {
			code,
			message,
			phase,
			timestamp: new Date(),
			retryable,
			details,
		};
	}

	/**
	 * Create processing warning
	 */
	private createProcessingWarning(
		code: string,
		message: string,
		phase: ProcessingPhaseType,
		details?: Record<string, unknown>,
	): ProcessingWarningType
	{
		return {
			code,
			message,
			phase,
			timestamp: new Date(),
			details,
		};
	}

	/**
	 * Convert generic errors to processing errors
	 */
	private convertToProcessingErrors(
		errors: any[],
		phase: ProcessingPhaseType,
	): ProcessingErrorType[]
	{
		return errors.map(error => ({
			code: error.code || 'UNKNOWN_ERROR',
			message: error.message || String(error),
			phase,
			timestamp: new Date(),
			retryable: error.retryable !== false,
			details: error.details,
		}));
	}

	/**
	 * Check if error is transient (retryable)
	 */
	private isTransientError(error: Error): boolean
	{
		const transientPatterns = [
			/timeout/i,
			/connection/i,
			/network/i,
			/temporary/i,
			/rate limit/i,
			/503/,
			/502/,
			/504/,
		];

		return transientPatterns.some(pattern => pattern.test(error.message));
	}

	/**
	 * Check if error is critical (non-recoverable)
	 */
	private isCriticalError(error: Error): boolean
	{
		const criticalPatterns = [
			/authentication/i,
			/authorization/i,
			/permission/i,
			/invalid domain/i,
			/malformed/i,
			/404/,
			/400/,
		];

		return criticalPatterns.some(pattern => pattern.test(error.message));
	}

	/**
	 * Delay execution for specified milliseconds
	 */
	private delay(ms: number): Promise<void>
	{
		return new Promise(resolve => setTimeout(resolve, ms));
	}

	/**
	 * Get current pipeline statistics
	 */
	getStatistics(): Record<string, unknown>
	{
		return {
			activeExecutions: this.activeExecutions.size,
			totalExecutions: this.executionHistory.length,
			averageProcessingTime: this.calculateAverageProcessingTime(),
			successRate: this.calculateSuccessRate(),
			phaseStatistics: this.calculatePhaseStatistics(),
		};
	}

	/**
	 * Calculate average processing time
	 */
	private calculateAverageProcessingTime(): number
	{
		if (this.executionHistory.length === 0) return 0;

		const totalTime = this.executionHistory.reduce((sum, execution) =>
		{
			const endTime = execution.phaseResults.length > 0
				? Math.max(...execution.phaseResults.map(p => p.endTime.getTime()))
				: execution.startTime.getTime();
			return sum + (endTime - execution.startTime.getTime());
		}, 0);

		return totalTime / this.executionHistory.length;
	}

	/**
	 * Calculate success rate
	 */
	private calculateSuccessRate(): number
	{
		if (this.executionHistory.length === 0) return 0;

		const successfulExecutions = this.executionHistory.filter(execution =>
			execution.errors.length === 0 && execution.phaseResults.every(p => p.status === 'success')).length;

		return (successfulExecutions / this.executionHistory.length) * 100;
	}

	/**
	 * Calculate phase statistics
	 */
	private calculatePhaseStatistics(): Record<string, any>
	{
		const phases: ProcessingPhaseType[] = ['crawling', 'ranking', 'indexing'];
		const statistics: Record<string, unknown> = {};

		phases.forEach((phase) =>
		{
			const phaseResults = this.executionHistory.flatMap(e =>
				e.phaseResults.filter(p => p.phase === phase));

			if (phaseResults.length > 0)
			{
				statistics[phase] = {
					totalExecutions: phaseResults.length,
					successfulExecutions: phaseResults.filter(p => p.status === 'success').length,
					averageDuration: phaseResults.reduce((sum, p) => sum + p.duration, 0) / phaseResults.length,
					totalRetries: phaseResults.reduce((sum, p) => sum + p.retryCount, 0),
				};
			}
		});

		return statistics;
	}

	/**
	 * Estimate disk I/O usage based on processing context
	 */
	private estimateDiskIO(context: ProcessingContextType): number
	{
		// Estimate based on modules that perform file operations
		let estimatedIO = 0;

		// Base I/O for logging and temp files
		estimatedIO += 1024; // 1KB base

		// Screenshot module creates image files
		if (context.moduleResults.has('screenshot'))
		{
			estimatedIO += 50 * 1024; // ~50KB for screenshot
		}

		// Content analysis may cache data
		if (context.moduleResults.has('content'))
		{
			estimatedIO += 10 * 1024; // ~10KB for content caching
		}

		// Performance module may save trace files
		if (context.moduleResults.has('performance'))
		{
			estimatedIO += 20 * 1024; // ~20KB for performance data
		}

		return estimatedIO;
	}

	/**
	 * Estimate network I/O usage based on processing context
	 */
	private estimateNetworkIO(context: ProcessingContextType): number
	{
		// Estimate based on modules that make network requests
		let estimatedIO = 0;

		// DNS queries
		if (context.moduleResults.has('dns'))
		{
			estimatedIO += 2 * 1024; // ~2KB for DNS queries
		}

		// Homepage fetch
		if (context.moduleResults.has('homepage'))
		{
			estimatedIO += 100 * 1024; // ~100KB for homepage content
		}

		// SSL certificate check
		if (context.moduleResults.has('ssl'))
		{
			estimatedIO += 5 * 1024; // ~5KB for SSL handshake
		}

		// Robots.txt fetch
		if (context.moduleResults.has('robots'))
		{
			estimatedIO += 1024; // ~1KB for robots.txt
		}

		// Favicon fetch
		if (context.moduleResults.has('favicon'))
		{
			estimatedIO += 10 * 1024; // ~10KB for favicon
		}

		// Performance monitoring
		if (context.moduleResults.has('performance'))
		{
			estimatedIO += 200 * 1024; // ~200KB for performance tests
		}

		return estimatedIO;
	}
}

export default DomainProcessingPipeline;
export type {
	PipelineConfigType,
	PipelinePhaseResultType,
	PipelineExecutionContextType,
	TaskRetryDecisionType,
};
