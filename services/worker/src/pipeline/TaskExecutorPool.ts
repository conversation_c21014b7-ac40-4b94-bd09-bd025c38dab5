/**
 * Task Executor Pool
 *
 * Simplified task execution management with concurrency control.
 * Provides essential functionality without overengineering.
 */

import { logger as loggerFactory } from '@shared';

const logger = loggerFactory.getLogger('TaskExecutorPool');

export type TaskPriorityType = 'low' | 'medium' | 'high' | 'critical';
export type TaskStatusType = 'pending' | 'running' | 'completed' | 'failed' | 'timeout' | 'cancelled';

export type TaskType<T = any> = {
	id: string;
	name: string;
	priority: TaskPriorityType;
	timeout: number;
	retryAttempts: number;
	executor: () => Promise<T>;
	onProgress?: (progress: number) => void;
	onCancel?: () => void;
	metadata?: Record<string, unknown>;
};

export type TaskResultType<T = any> = {
	taskId: string;
	status: TaskStatusType;
	result?: T;
	error?: Error;
	startTime: Date;
	endTime: Date;
	executionTime: number;
	retryCount: number;
	resourceUsage?: any;
	cancelled: boolean;
};

export type ExecutorPoolConfigType = {
	maxConcurrentTasks: number;
	minConcurrentTasks?: number;
	maxQueueSize?: number;
	taskTimeout?: number;
	[key: string]: any; // Accept other config for compatibility
};

export type ExecutorPoolStatsType = {
	queueLength: number;
	runningTasks: number;
	completedTasks: number;
	failedTasks: number;
	totalTasks: number;
	averageExecutionTime: number;
	currentConcurrency: number;
	maxConcurrency: number;
};

interface QueuedTask<T> {
	task: TaskType<T>;
	resolve: (result: TaskResultType<T>) => void;
	reject: (error: Error) => void;
}

class TaskExecutorPool
{
	private config: ExecutorPoolConfigType;
	private taskQueue: QueuedTask<any>[] = [];
	private runningTasks = new Map<string, AbortController>();
	private isShuttingDown = false;
	private isInitialized = false;
	private completedTasks = 0;
	private failedTasks = 0;
	private totalExecutionTime = 0;

	constructor(config: ExecutorPoolConfigType)
	{
		this.config = {
			maxConcurrentTasks: config.maxConcurrentTasks || 5,
			minConcurrentTasks: config.minConcurrentTasks || 1,
			maxQueueSize: config.maxQueueSize || 100,
			taskTimeout: config.taskTimeout || 30000,
			...config
		};
	}

	/**
	 * Initialize the executor pool
	 */
	async initialize(): Promise<void>
	{
		if (this.isInitialized)
		{
			return;
		}
		logger.info('TaskExecutorPool initialized', { config: this.config });
		this.isInitialized = true;
	}

	/**
	 * Execute a task
	 */
	async executeTask<T>(task: TaskType<T>): Promise<TaskResultType<T>>
	{
		if (!this.isInitialized)
		{
			throw new Error('Task executor pool not initialized');
		}

		if (this.isShuttingDown)
		{
			throw new Error('Task executor pool is shutting down');
		}

		if (this.taskQueue.length >= (this.config.maxQueueSize || 100))
		{
			throw new Error('Task queue is full');
		}

		return new Promise<TaskResultType<T>>((resolve, reject) =>
		{
			const queuedTask: QueuedTask<T> = {
				task,
				resolve,
				reject,
			};

			// Add to queue based on priority
			if (task.priority === 'critical')
			{
				this.taskQueue.unshift(queuedTask);
			}
			else if (task.priority === 'high')
			{
				// Insert after critical but before others
				const insertIndex = this.taskQueue.findIndex(qt =>
					qt.task.priority !== 'critical');
				if (insertIndex === -1)
				{
					this.taskQueue.push(queuedTask);
				}
				else
				{
					this.taskQueue.splice(insertIndex, 0, queuedTask);
				}
			}
			else
			{
				this.taskQueue.push(queuedTask);
			}

			// Process queue
			this.processQueue();
		});
	}

	/**
	 * Process queued tasks
	 */
	private processQueue(): void
	{
		while (this.taskQueue.length > 0 && this.runningTasks.size < this.config.maxConcurrentTasks)
		{
			const queuedTask = this.taskQueue.shift();
			if (queuedTask)
			{
				this.runTask(queuedTask);
			}
		}
	}

	/**
	 * Run a single task
	 */
	private async runTask<T>(queuedTask: QueuedTask<T>): Promise<void>
	{
		const { task, resolve, reject } = queuedTask;
		const startTime = new Date();
		const abortController = new AbortController();

		this.runningTasks.set(task.id, abortController);

		// Set timeout
		const timeout = task.timeout || this.config.taskTimeout || 30000;
		const timeoutId = setTimeout(() =>
		{
			abortController.abort();
			this.runningTasks.delete(task.id);

			const result: TaskResultType<T> = {
				taskId: task.id,
				status: 'timeout',
				error: new Error(`Task ${task.name} timed out after ${timeout}ms`),
				startTime,
				endTime: new Date(),
				executionTime: timeout,
				retryCount: 0,
				cancelled: false,
			};

			this.failedTasks++;

			resolve(result);
			this.processQueue();
		}, timeout);

		try
		{
			logger.debug(`Starting task ${task.name}`, { taskId: task.id });

			const taskResult = await task.executor();

			if (!abortController.signal.aborted)
			{
				clearTimeout(timeoutId);
				this.runningTasks.delete(task.id);

				const endTime = new Date();
				const executionTime = endTime.getTime() - startTime.getTime();
				const result: TaskResultType<T> = {
					taskId: task.id,
					status: 'completed',
					result: taskResult,
					startTime,
					endTime,
					executionTime,
					retryCount: 0,
					cancelled: false,
				};

				this.completedTasks++;
				this.totalExecutionTime += executionTime;

				resolve(result);
			}
		}
		catch (error)
		{
			if (!abortController.signal.aborted)
			{
				clearTimeout(timeoutId);
				this.runningTasks.delete(task.id);

				const endTime = new Date();
				const result: TaskResultType<T> = {
					taskId: task.id,
					status: 'failed',
					error: error as Error,
					startTime,
					endTime,
					executionTime: endTime.getTime() - startTime.getTime(),
					retryCount: 0,
					cancelled: false,
				};

				this.failedTasks++;

				resolve(result);
			}
		}

		// Process next task in queue
		this.processQueue();
	}

	/**
	 * Cancel a running task
	 */
	async cancelTask(taskId: string): Promise<boolean>
	{
		const controller = this.runningTasks.get(taskId);
		if (controller)
		{
			controller.abort();
			this.runningTasks.delete(taskId);
			return true;
		}

		// Check if task is in queue
		const index = this.taskQueue.findIndex(qt => qt.task.id === taskId);
		if (index !== -1)
		{
			const [queuedTask] = this.taskQueue.splice(index, 1);
			queuedTask.reject(new Error('Task cancelled'));
			return true;
		}

		return false;
	}

	/**
	 * Get current stats
	 */
	getStats(): ExecutorPoolStatsType
	{
		const totalTasks = this.completedTasks + this.failedTasks;
		return {
			queueLength: this.taskQueue.length,
			runningTasks: this.runningTasks.size,
			completedTasks: this.completedTasks,
			failedTasks: this.failedTasks,
			totalTasks,
			averageExecutionTime: totalTasks > 0 ? this.totalExecutionTime / totalTasks : 0,
			currentConcurrency: this.runningTasks.size,
			maxConcurrency: this.config.maxConcurrentTasks,
		};
	}

	/**
	 * Shutdown the executor
	 */
	async shutdown(timeoutMs: number = 30000): Promise<void>
	{
		this.isShuttingDown = true;

		// Cancel all running tasks
		for (const [taskId, controller] of this.runningTasks)
		{
			controller.abort();
		}
		this.runningTasks.clear();

		// Reject all queued tasks
		while (this.taskQueue.length > 0)
		{
			const queuedTask = this.taskQueue.shift();
			if (queuedTask)
			{
				queuedTask.reject(new Error('Task executor shutdown'));
			}
		}

		logger.info('Task executor shutdown complete');
	}
}

export default TaskExecutorPool;
