# Processing Pipeline Module

This directory contains the core processing pipeline that orchestrates all extracted functionality from the three original services (crawler, ranking-engine, scheduler).

## Implemented Components

### Core Pipeline Components

- `DomainProcessingPipeline.ts` - Main pipeline orchestrator that manages the complete domain processing workflow: crawling → analysis → ranking → indexing
- `TaskExecutorPool.ts` - Concurrent task execution management with resource monitoring, throttling, and backpressure mechanisms
- `PipelineCoordinator.ts` - Coordinates multiple pipeline instances and manages resource allocation across concurrent domain processing operations

### Key Features

#### DomainProcessingPipeline

- Sequential task execution with proper data flow and state management
- Comprehensive task-level error handling with retry logic and timeout management
- Pipeline-level error handling for critical failures and graceful degradation
- Detailed progress tracking and monitoring throughout the entire pipeline
- Resource monitoring and load balancing to prevent system overload
- Domain locking integration for multi-worker coordination

#### TaskExecutorPool

- Configurable concurrency with MAX_CONCURRENT_TASKS environment variable
- Task queuing with priority-based execution
- Resource monitoring with CPU, memory, and disk usage tracking
- Backpressure mechanisms to prevent system overload
- Comprehensive task metrics collection and performance monitoring
- Graceful shutdown with task completion or cancellation

#### PipelineCoordinator

- Load balancing across multiple pipeline instances
- Priority-weighted job assignment
- System-wide resource optimization
- Health monitoring and automatic failover
- Throughput optimization and capacity planning

## Pipeline Flow

```
Domain Job → Pipeline Coordinator → Domain Processing Pipeline → Task Executor Pool
                     ↓                           ↓                        ↓
              Load Balancing              Sequential Phases        Concurrent Tasks
              Priority Management         Error Handling           Resource Management
              Resource Allocation         Progress Tracking        Backpressure Control
```

## Processing Phases

1. **Crawling Phase**: DNS analysis, robots.txt parsing, SSL analysis, homepage analysis, favicon collection, screenshot capture, performance auditing, content analysis
2. **Ranking Phase**: Performance scoring, security scoring, SEO scoring, technical scoring, backlink scoring, composite ranking calculation
3. **Indexing Phase**: ScyllaDB updates, MariaDB updates, Manticore search index synchronization, cache invalidation

## Configuration

The pipeline system is configured through the worker configuration and supports:

- Timeout settings for each phase
- Retry policies with exponential backoff
- Resource thresholds and monitoring
- Progress tracking intervals
- Error handling strategies
- Load balancing algorithms

## Integration

This pipeline integrates with all extracted components:

- **Crawler**: DataCollectionOrchestrator and all analysis modules
- **Ranking**: RankingManager and all scoring algorithms
- **Indexing**: SearchIndexingService and database synchronization
- **Locking**: DomainLockManager for multi-worker coordination
- **Monitoring**: WorkerHealthService and WorkerMetricsCollector

The pipeline serves as the central orchestrator that brings together all functionality from the three original services into a unified, scalable processing system.
