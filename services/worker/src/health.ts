/**
 * Worker Service Health Monitoring System
 *
 * Simplified health monitoring for the consolidated worker service
 */

import { logger as sharedLogger } from '@shared';
import { HealthCheck } from './monitoring';
import type { WorkerDatabaseManager } from './database/WorkerDatabaseManager';
import type { WorkerJobQueue } from './queue/WorkerJobQueue';
import type DomainLockManager from './locking/DomainLockManager';

const logger = sharedLogger.getLogger('WorkerHealth');

// Global health check instance
let healthCheck: HealthCheck;

/**
 * Initialize worker health monitoring system
 */
function initializeHealthMonitoring(
	workerId: string,
	version: string,
	databaseManager?: WorkerDatabaseManager,
	jobQueue?: WorkerJobQueue,
	domainLockManager?: DomainLockManager,
): HealthCheck 
{
	logger.info('Initializing worker health monitoring system', {
		workerId,
		version,
	});

	healthCheck = new HealthCheck();
	
	logger.info('Health monitoring system initialized successfully');
	return healthCheck;
}

/**
 * Get current health status
 */
async function getHealthStatus() 
{
	if (!healthCheck) 
	{
		return {
			status: 'unknown',
			message: 'Health check not initialized',
		};
	}
	
	return healthCheck.check();
}

/**
 * Perform a health check
 */
async function performHealthCheck() 
{
	return getHealthStatus();
}

/**
 * Shutdown health monitoring
 */
async function shutdownHealthMonitoring() 
{
	logger.info('Shutting down health monitoring system');
	// Cleanup if needed
	logger.info('Health monitoring system shutdown complete');
}

export {
	initializeHealthMonitoring,
	getHealthStatus,
	performHealthCheck,
	shutdownHealthMonitoring,
	healthCheck,
};
