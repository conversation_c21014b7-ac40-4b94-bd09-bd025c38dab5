/**
 * Worker Service Entry Point
 *
 * Consolidated worker service that replaces crawler, ranking-engine, and scheduler services.
 * Each worker instance processes domains through a complete pipeline:
 * crawling → analysis → ranking → indexing
 */

import { config } from 'dotenv';
import { logger as loggerFactory } from '@shared/utils/Logger';
import { WorkerService } from './core/WorkerService';
import { WorkerCLI } from './cli/WorkerCLI';

// Load environment variables
config();

const logger = loggerFactory.getLogger('worker-main');

// Global worker service instance for signal handling
let globalWorkerService: WorkerService | null = null;

/**
 * Main application entry point
 */
async function main(): Promise<void>
{
	try
	{
		// Check if running in CLI mode
		const args = process.argv.slice(2);
		if (args.length > 0 && !args[0].startsWith('-'))
		{
			// CLI mode
			const cli = new WorkerCLI();
			await cli.execute(process.argv);
			return;
		}

		// Service mode - start the worker service
		await startWorkerService();
	}
	catch (error)
	{
		logger.error('Application failed to start:', error);
		process.exit(1);
	}
}

/**
 * Start the worker service in daemon mode
 */
async function startWorkerService(): Promise<void>
{
	try
	{
		logger.info('Starting Worker Service in daemon mode...');

		// Create and store global reference for signal handling
		globalWorkerService = new WorkerService();

		// Setup signal handlers before initialization
		setupSignalHandlers();

		// Setup uncaught exception handlers
		setupExceptionHandlers();

		// Perform dependency checks
		await performStartupChecks();

		// Initialize the worker service
		logger.info('Initializing worker service components...');
		await globalWorkerService.initialize();

		// Start processing jobs
		logger.info('Starting job processing...');
		await globalWorkerService.start();

		// Log startup success with service information
		const config = globalWorkerService.getConfiguration();
		logger.info('Worker Service started successfully', {
			workerId: config.workerId,
			maxConcurrentTasks: config.maxConcurrentTasks,
			pid: process.pid,
			nodeVersion: process.version,
			platform: process.platform,
			arch: process.arch,
		});

		// Log service readiness
		const health = await globalWorkerService.getHealth();
		logger.info('Service health check completed', {
			status: health.status,
			databases: Object.keys(health.databases).length,
			activeJobs: health.activeJobs,
		});

		// Keep the process alive
		logger.info('Worker Service is ready to process jobs');
	}
	catch (error)
	{
		logger.error('Failed to start Worker Service:', error);

		// Attempt cleanup if service was partially initialized
		if (globalWorkerService)
		{
			try
			{
				await globalWorkerService.shutdown();
			}
			catch (shutdownError)
			{
				logger.error('Error during cleanup:', shutdownError);
			}
		}

		process.exit(1);
	}
}

/**
 * Perform startup checks and validations
 */
async function performStartupChecks(): Promise<void>
{
	logger.info('Performing startup checks...');

	// Check required environment variables
	const requiredEnvVars = [
		'SCYLLA_HOSTS',
		'MARIA_HOST',
		'REDIS_URL',
		'MANTICORE_HOST',
	];

	const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
	if (missingVars.length > 0)
	{
		throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
	}

	// Check Node.js version
	const nodeVersion = process.version;
	const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
	if (majorVersion < 18)
	{
		logger.warn(`Node.js version ${nodeVersion} is below recommended version 18+`);
	}

	// Check available memory
	const totalMemory = process.memoryUsage();
	logger.info('Memory usage at startup', {
		heapUsed: `${Math.round(totalMemory.heapUsed / 1024 / 1024)  }MB`,
		heapTotal: `${Math.round(totalMemory.heapTotal / 1024 / 1024)  }MB`,
		external: `${Math.round(totalMemory.external / 1024 / 1024)  }MB`,
	});

	logger.info('Startup checks completed successfully');
}

/**
 * Setup signal handlers for graceful shutdown
 */
function setupSignalHandlers(): void
{
	const shutdown = async (signal: string): Promise<void> =>
	{
		logger.info(`Received ${signal}, initiating graceful shutdown...`);

		try
		{
			if (globalWorkerService)
			{
				// Set a timeout for shutdown process
				const shutdownTimeout = setTimeout(() =>
				{
					logger.error('Shutdown timeout exceeded, forcing exit');
					process.exit(1);
				}, 30000); // 30 seconds timeout

				await globalWorkerService.stop();
				await globalWorkerService.shutdown();

				clearTimeout(shutdownTimeout);
				logger.info('Worker Service shut down successfully');
			}

			process.exit(0);
		}
		catch (error)
		{
			logger.error('Error during shutdown:', error);
			process.exit(1);
		}
	};

	// Register signal handlers
	process.on('SIGTERM', () => shutdown('SIGTERM'));
	process.on('SIGINT', () => shutdown('SIGINT'));
	process.on('SIGUSR2', () => shutdown('SIGUSR2')); // For nodemon
	process.on('SIGHUP', () => shutdown('SIGHUP')); // For process managers

	logger.info('Signal handlers registered for graceful shutdown');
}

/**
 * Setup exception handlers
 */
function setupExceptionHandlers(): void
{
	// Handle uncaught exceptions
	process.on('uncaughtException', async (error) =>
	{
		logger.error('Uncaught exception detected:', error);

		try
		{
			// Attempt graceful shutdown
			if (globalWorkerService)
			{
				await globalWorkerService.shutdown();
			}
		}
		catch (shutdownError)
		{
			logger.error('Error during emergency shutdown:', shutdownError);
		}

		process.exit(1);
	});

	// Handle unhandled promise rejections
	process.on('unhandledRejection', async (reason, promise) =>
	{
		logger.error('Unhandled promise rejection detected:', {
			reason: reason instanceof Error ? reason.message : reason,
			stack: reason instanceof Error ? reason.stack : undefined,
			promise: promise.toString(),
		});

		try
		{
			// Attempt graceful shutdown
			if (globalWorkerService)
			{
				await globalWorkerService.shutdown();
			}
		}
		catch (shutdownError)
		{
			logger.error('Error during emergency shutdown:', shutdownError);
		}

		process.exit(1);
	});

	// Handle warnings
	process.on('warning', (warning) =>
	{
		logger.warn('Node.js warning:', {
			name: warning.name,
			message: warning.message,
			stack: warning.stack,
		});
	});

	logger.info('Exception handlers registered');
}

// Start the application
main().catch((error) =>
{
	logger.error('Fatal error:', error);
	process.exit(1);
});

export default main;
