import { logger as sharedLogger } from '@shared';
import type { WorkerService } from '../../core/WorkerService';

const logger = sharedLogger.getLogger('MonitoringCommands');

export class MonitoringCommands 
{
	constructor(private workerService: WorkerService) {}

	/**
	 * Show worker service metrics
	 */
	async showMetrics(options: any): Promise<void> 
	{
		try 
		{
			const metrics = await this.workerService.getMetrics();

			if (options.json) 
			{
				process.stdout.write(`${JSON.stringify(metrics, null, 2)  }\n`);
				return;
			}

			// System metrics
			logger.info({
				cpu: `${metrics.system.cpu}%`,
				memory: `${metrics.system.memory.used}/${metrics.system.memory.total} MB`,
				uptime: `${metrics.system.uptime} seconds`
			}, 'System metrics');

			// Worker metrics
			logger.info({
				active: metrics.workers.active,
				idle: metrics.workers.idle,
				busy: metrics.workers.busy,
				jobsProcessed: metrics.workers.jobsProcessed,
				jobsPerMinute: metrics.workers.jobsPerMinute
			}, 'Worker metrics');

			// Queue metrics
			logger.info({
				waiting: metrics.queues.waiting,
				active: metrics.queues.active,
				completed: metrics.queues.completed,
				failed: metrics.queues.failed,
				delayed: metrics.queues.delayed
			}, 'Queue metrics');

			// Database metrics
			logger.info({
				connections: metrics.database.connections,
				queries: metrics.database.queries,
				avgLatency: `${metrics.database.avgLatency}ms`
			}, 'Database metrics');
		}
		catch (error) 
		{
			logger.error({ error: error.message }, 'Failed to get metrics');
			throw error;
		}
	}

	/**
	 * Watch metrics in real-time
	 */
	async watchMetrics(options: any): Promise<void> 
	{
		const interval = parseInt(options.interval) || 5000;
		
		logger.info({ interval }, 'Starting metrics watch mode (Ctrl+C to exit)');

		const displayMetrics = async () => 
		{
			try 
			{
				// Clear console
				process.stdout.write('\x1B[2J\x1B[0f');

				const metrics = await this.workerService.getMetrics();
				const timestamp = new Date().toISOString();

				logger.info({ timestamp }, 'Worker Service Metrics');
				logger.info('═'.repeat(60));

				// System
				logger.info({
					cpu: `${metrics.system.cpu}%`,
					memory: `${metrics.system.memory.used}/${metrics.system.memory.total} MB`,
					load: metrics.system.load
				}, 'System');

				// Workers
				logger.info({
					active: metrics.workers.active,
					jobsPerMinute: metrics.workers.jobsPerMinute,
					avgProcessingTime: `${metrics.workers.avgProcessingTime}ms`
				}, 'Workers');

				// Queues
				logger.info({
					waiting: metrics.queues.waiting,
					active: metrics.queues.active,
					throughput: `${metrics.queues.throughput}/min`
				}, 'Queues');

				// Errors
				if (metrics.errors && metrics.errors.recent > 0) 
				{
					logger.warn({
						recent: metrics.errors.recent,
						rate: `${metrics.errors.rate}/min`
					}, 'Errors');
				}
			}
			catch (error) 
			{
				logger.error({ error: error.message }, 'Failed to fetch metrics');
			}
		};

		// Initial display
		await displayMetrics();

		// Set up interval
		const intervalId = setInterval(displayMetrics, interval);

		// Handle graceful exit
		process.on('SIGINT', () => 
		{
			clearInterval(intervalId);
			logger.info('Metrics watch stopped');
			process.exit(0);
		});
	}

	/**
	 * Show error logs
	 */
	async showErrors(options: any): Promise<void> 
	{
		try 
		{
			const limit = parseInt(options.limit) || 50;
			const since = options.since || new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();

			const errors = await this.workerService.getErrors({
				limit,
				since,
				severity: options.severity || 'all'
			});

			if (options.json) 
			{
				process.stdout.write(`${JSON.stringify(errors, null, 2)  }\n`);
				return;
			}

			logger.info({ count: errors.length, since }, 'Recent errors');

			errors.forEach((error) => 
			{
				const logFn = error.severity === 'critical' ? logger.error
					: error.severity === 'warning' ? logger.warn : logger.info;

				logFn({
					timestamp: error.timestamp,
					type: error.type,
					message: error.message,
					domain: error.context?.domain,
					jobId: error.context?.jobId
				}, error.severity.toUpperCase());
			});
		}
		catch (error) 
		{
			logger.error({ error: error.message }, 'Failed to get errors');
			throw error;
		}
	}

	/**
	 * Generate performance report
	 */
	async generateReport(options: any): Promise<void> 
	{
		try 
		{
			const period = options.period || 'day';
			const format = options.format || 'text';

			logger.info({ period, format }, 'Generating performance report');

			const report = await this.workerService.generateReport({
				period,
				includeDetails: options.detailed || false
			});

			if (format === 'json') 
			{
				process.stdout.write(`${JSON.stringify(report, null, 2)  }\n`);
				return;
			}

			// Text format output
			logger.info('Performance Report');
			logger.info('═'.repeat(60));
			logger.info({
				period: report.period,
				from: report.from,
				to: report.to
			}, 'Report Period');

			logger.info({
				totalJobs: report.summary.totalJobs,
				completed: report.summary.completed,
				failed: report.summary.failed,
				averageTime: `${report.summary.averageTime}ms`,
				successRate: `${report.summary.successRate}%`
			}, 'Summary');

			if (report.topDomains) 
			{
				logger.info({ domains: report.topDomains.slice(0, 10) }, 'Top Processed Domains');
			}

			if (report.errorSummary) 
			{
				logger.info({ errors: report.errorSummary }, 'Error Summary');
			}

			if (options.output) 
			{
				const fs = await import('fs/promises');
				await fs.writeFile(options.output, JSON.stringify(report, null, 2));
				logger.info({ file: options.output }, 'Report saved to file');
			}
		}
		catch (error) 
		{
			logger.error({ error: error.message }, 'Failed to generate report');
			throw error;
		}
	}
}
