import { logger as sharedLogger } from '@shared';
import type { WorkerService } from '../../core/WorkerService';

const logger = sharedLogger.getLogger('ProcessCommands');

export class ProcessCommands 
{
	constructor(private workerService: WorkerService) {}

	/**
	 * Process a single domain
	 */
	async processDomain(domain: string, options: any): Promise<void> 
	{
		try 
		{
			logger.info({ domain, options }, 'Processing single domain');

			const jobData = {
				domain,
				type: options.type || 'full',
				priority: options.priority || 'medium',
				requestedAt: new Date().toISOString()
			};

			// Submit job to queue
			const jobId = await this.workerService.submitJob(jobData);
			logger.info({ jobId, domain }, 'Job submitted successfully');

			if (options.wait) 
			{
				logger.info({ jobId }, 'Waiting for job completion...');
				const result = await this.workerService.waitForJob(jobId);
				logger.info({ jobId, result }, 'Job completed');
			}
		}
		catch (error) 
		{
			logger.error({ error: error.message, domain }, 'Failed to process domain');
			throw error;
		}
	}

	/**
	 * Process domains from batch file
	 */
	async processBatch(file: string, options: any): Promise<void> 
	{
		try 
		{
			logger.info({ file, options }, 'Processing batch file');

			const fs = await import('fs/promises');
			const content = await fs.readFile(file, 'utf-8');
			const domains = content.split('\n').filter(d => d.trim());

			logger.info({ count: domains.length }, 'Loaded domains from file');

			const results = {
				submitted: 0,
				failed: 0,
				errors: [] as string[]
			};

			// Process domains with concurrency control
			const concurrency = parseInt(options.concurrency) || 5;
			for (let i = 0; i < domains.length; i += concurrency) 
			{
				const batch = domains.slice(i, i + concurrency);
				const promises = batch.map(async (domain) => 
				{
					try 
					{
						const jobData = {
							domain,
							type: 'batch',
							priority: options.priority || 'low',
							requestedAt: new Date().toISOString()
						};
						await this.workerService.submitJob(jobData);
						results.submitted++;
					}
					catch (error) 
					{
						results.failed++;
						results.errors.push(`${domain}: ${error.message}`);
					}
				});
				await Promise.allSettled(promises);
			}

			logger.info({
				submitted: results.submitted,
				failed: results.failed,
				total: domains.length
			}, 'Batch processing completed');

			if (results.errors.length > 0) 
			{
				logger.error({ errors: results.errors }, 'Batch processing errors');
			}
		}
		catch (error) 
		{
			logger.error({ error: error.message, file }, 'Failed to process batch');
			throw error;
		}
	}
}
