import { logger as sharedLogger } from '@shared';
import type { WorkerService } from '../../core/WorkerService';

const logger = sharedLogger.getLogger('QueueCommands');

export class QueueCommands 
{
	constructor(private workerService: WorkerService) {}

	/**
	 * Show queue statistics
	 */
	async showStats(options: any): Promise<void> 
	{
		try 
		{
			const stats = await this.workerService.getQueueStats();

			if (options.json) 
			{
				process.stdout.write(`${JSON.stringify(stats, null, 2)  }\n`);
				return;
			}

			logger.info({
				active: stats.active || 0,
				waiting: stats.waiting || 0,
				completed: stats.completed || 0,
				failed: stats.failed || 0,
				delayed: stats.delayed || 0,
				paused: stats.paused || false
			}, 'Queue statistics');
		}
		catch (error) 
		{
			logger.error({ error: error.message }, 'Failed to get queue stats');
			throw error;
		}
	}

	/**
	 * Pause a queue
	 */
	async pauseQueue(queueName: string): Promise<void> 
	{
		try 
		{
			await this.workerService.pauseQueue(queueName);
			logger.info({ queue: queueName }, 'Queue paused successfully');
		}
		catch (error) 
		{
			logger.error({ error: error.message, queue: queueName }, 'Failed to pause queue');
			throw error;
		}
	}

	/**
	 * Resume a queue
	 */
	async resumeQueue(queueName: string): Promise<void> 
	{
		try 
		{
			await this.workerService.resumeQueue(queueName);
			logger.info({ queue: queueName }, 'Queue resumed successfully');
		}
		catch (error) 
		{
			logger.error({ error: error.message, queue: queueName }, 'Failed to resume queue');
			throw error;
		}
	}

	/**
	 * Clear a queue
	 */
	async clearQueue(queueName: string, options: any): Promise<void> 
	{
		try 
		{
			const cleared = await this.workerService.clearQueue(queueName, {
				status: options.status || 'all',
				force: options.force || false
			});
			logger.info({ queue: queueName, cleared }, 'Queue cleared successfully');
		}
		catch (error) 
		{
			logger.error({ error: error.message, queue: queueName }, 'Failed to clear queue');
			throw error;
		}
	}

	/**
	 * List jobs in queue
	 */
	async listJobs(options: any): Promise<void> 
	{
		try 
		{
			const jobs = await this.workerService.getJobs({
				status: options.status || 'all',
				limit: parseInt(options.limit) || 100,
				offset: parseInt(options.offset) || 0
			});

			if (options.json) 
			{
				process.stdout.write(`${JSON.stringify(jobs, null, 2)  }\n`);
				return;
			}

			logger.info({ count: jobs.length }, 'Jobs in queue');
			jobs.forEach((job) => 
			{
				logger.info({
					id: job.id,
					domain: job.data?.domain,
					status: job.status,
					priority: job.opts?.priority,
					createdAt: job.timestamp
				}, 'Job details');
			});
		}
		catch (error) 
		{
			logger.error({ error: error.message }, 'Failed to list jobs');
			throw error;
		}
	}

	/**
	 * Get job details
	 */
	async getJob(jobId: string, options: any): Promise<void> 
	{
		try 
		{
			const job = await this.workerService.getJob(jobId);

			if (!job) 
			{
				logger.warn({ jobId }, 'Job not found');
				return;
			}

			if (options.json) 
			{
				process.stdout.write(`${JSON.stringify(job, null, 2)  }\n`);
				return;
			}

			logger.info({
				id: job.id,
				status: await job.getState(),
				progress: job.progress,
				data: job.data,
				result: job.returnvalue,
				failedReason: job.failedReason,
				attemptsMade: job.attemptsMade,
				createdAt: new Date(job.timestamp).toISOString(),
				processedAt: job.processedOn ? new Date(job.processedOn).toISOString() : null,
				finishedAt: job.finishedOn ? new Date(job.finishedOn).toISOString() : null
			}, 'Job details');
		}
		catch (error) 
		{
			logger.error({ error: error.message, jobId }, 'Failed to get job');
			throw error;
		}
	}

	/**
	 * Retry failed jobs
	 */
	async retryFailed(options: any): Promise<void> 
	{
		try 
		{
			const limit = parseInt(options.limit) || 100;
			const retried = await this.workerService.retryFailedJobs(limit);
			logger.info({ retried }, 'Failed jobs retried');
		}
		catch (error) 
		{
			logger.error({ error: error.message }, 'Failed to retry jobs');
			throw error;
		}
	}
}
