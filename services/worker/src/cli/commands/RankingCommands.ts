import { logger as sharedLogger } from '@shared';
import type { WorkerService } from '../../core/WorkerService';

const logger = sharedLogger.getLogger('RankingCommands');

export class RankingCommands 
{
	constructor(private workerService: WorkerService) {}

	/**
	 * Calculate ranking for a domain
	 */
	async calculateRanking(domain: string, options: any): Promise<void> 
	{
		try 
		{
			logger.info({ domain, options }, 'Calculating domain ranking');

			const ranking = await this.workerService.calculateRanking(domain, {
				recalculate: options.recalculate || false,
				includeHistory: options.history || false
			});

			if (options.json) 
			{
				process.stdout.write(`${JSON.stringify(ranking, null, 2)  }\n`);
				return;
			}

			logger.info({
				domain,
				score: ranking.score,
				rank: ranking.rank,
				percentile: ranking.percentile,
				components: ranking.components,
				lastUpdated: ranking.lastUpdated
			}, 'Domain ranking calculated');
		}
		catch (error) 
		{
			logger.error({ error: error.message, domain }, 'Failed to calculate ranking');
			throw error;
		}
	}

	/**
	 * Update rankings for multiple domains
	 */
	async updateRankings(options: any): Promise<void> 
	{
		try 
		{
			const batchSize = parseInt(options.batch) || 100;
			const maxAge = parseInt(options.maxAge) || 7; // days

			logger.info({ batchSize, maxAge }, 'Starting ranking update');

			const result = await this.workerService.updateRankings({
				batchSize,
				maxAge,
				dryRun: options.dryRun || false
			});

			logger.info({
				processed: result.processed,
				updated: result.updated,
				failed: result.failed,
				duration: result.duration
			}, 'Ranking update completed');

			if (result.errors && result.errors.length > 0) 
			{
				logger.error({ errors: result.errors }, 'Ranking update errors');
			}
		}
		catch (error) 
		{
			logger.error({ error: error.message }, 'Failed to update rankings');
			throw error;
		}
	}

	/**
	 * Show ranking statistics
	 */
	async showRankingStats(options: any): Promise<void> 
	{
		try 
		{
			const stats = await this.workerService.getRankingStats();

			if (options.json) 
			{
				process.stdout.write(`${JSON.stringify(stats, null, 2)  }\n`);
				return;
			}

			logger.info({
				totalDomains: stats.totalDomains,
				rankedDomains: stats.rankedDomains,
				averageScore: stats.averageScore,
				medianScore: stats.medianScore,
				lastUpdate: stats.lastUpdate,
				distribution: stats.distribution
			}, 'Ranking statistics');
		}
		catch (error) 
		{
			logger.error({ error: error.message }, 'Failed to get ranking stats');
			throw error;
		}
	}

	/**
	 * Export rankings to file
	 */
	async exportRankings(file: string, options: any): Promise<void> 
	{
		try 
		{
			logger.info({ file, options }, 'Exporting rankings');

			const format = options.format || 'csv';
			const limit = parseInt(options.limit) || 10000;
			const minScore = parseFloat(options.minScore) || 0;

			const exported = await this.workerService.exportRankings({
				file,
				format,
				limit,
				minScore,
				includeMetadata: options.metadata || false
			});

			logger.info({ file, exported }, 'Rankings exported successfully');
		}
		catch (error) 
		{
			logger.error({ error: error.message, file }, 'Failed to export rankings');
			throw error;
		}
	}

	/**
	 * Compare rankings between dates
	 */
	async compareRankings(options: any): Promise<void> 
	{
		try 
		{
			const fromDate = options.from || new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString();
			const toDate = options.to || new Date().toISOString();

			logger.info({ fromDate, toDate }, 'Comparing rankings');

			const comparison = await this.workerService.compareRankings({
				fromDate,
				toDate,
				topN: parseInt(options.top) || 1000
			});

			if (options.json) 
			{
				process.stdout.write(`${JSON.stringify(comparison, null, 2)  }\n`);
				return;
			}

			logger.info({
				improved: comparison.improved.length,
				declined: comparison.declined.length,
				stable: comparison.stable.length,
				newEntries: comparison.newEntries.length,
				droppedOut: comparison.droppedOut.length
			}, 'Ranking comparison');

			if (options.detailed) 
			{
				logger.info({ topImproved: comparison.improved.slice(0, 10) }, 'Top improved domains');
				logger.info({ topDeclined: comparison.declined.slice(0, 10) }, 'Top declined domains');
			}
		}
		catch (error) 
		{
			logger.error({ error: error.message }, 'Failed to compare rankings');
			throw error;
		}
	}
}
