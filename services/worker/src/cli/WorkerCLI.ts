/**
 * Worker Service CLI Interface
 *
 * Provides command-line interface for worker management, debugging,
 * operational tasks, and system administration.
 */

import { Command } from 'commander';
import { logger as sharedLogger } from '@shared';
import { WorkerService } from '../core/WorkerService';
import { ProcessCommands } from './commands/ProcessCommands';
import { QueueCommands } from './commands/QueueCommands';
import { RankingCommands } from './commands/RankingCommands';
import { MonitoringCommands } from './commands/MonitoringCommands';
import type { WorkerConfigType } from '../types/WorkerTypes';

// Set CLI mode for better formatting
process.env.CLI_MODE = 'true';

const logger = sharedLogger.getLogger('WorkerCLI');

export class WorkerCLI
{
	private program: Command;
	private workerService: WorkerService | null = null;
	private processCommands: ProcessCommands;
	private queueCommands: QueueCommands;
	private rankingCommands: RankingCommands;
	private monitoringCommands: MonitoringCommands;
	private cliLogger: ReturnType<typeof sharedLogger.getCLILogger>;

	constructor()
	{
		this.program = new Command();
		this.cliLogger = sharedLogger.getCLILogger('WorkerCLI');
		this.setupCommands();
	}

	/**
	 * Setup CLI commands
	 */
	private setupCommands(): void
	{
		this.program
			.name('worker-service')
			.description('Domain Processing Worker Service CLI')
			.version('1.0.0')
			.option('-q, --quiet', 'Quiet output (errors only)')
			.option('-v, --verbose', 'Verbose output')
			.option('--json', 'JSON output format');

		// Start command
		this.program
			.command('start')
			.description('Start the worker service')
			.option('-c, --config <path>', 'Configuration file path')
			.option('-d, --daemon', 'Run as daemon')
			.option('--max-concurrent <number>', 'Maximum concurrent tasks', '10')
			.action(this.handleStart.bind(this));

		// Stop command
		this.program
			.command('stop')
			.description('Stop the worker service gracefully')
			.option('--timeout <seconds>', 'Shutdown timeout in seconds', '30')
			.action(this.handleStop.bind(this));

		// Status command
		this.program
			.command('status')
			.description('Show worker service status')
			.option('--json', 'Output in JSON format')
			.option('--detailed', 'Show detailed status information')
			.action(this.handleStatus.bind(this));

		// Health command
		this.program
			.command('health')
			.description('Check worker service health')
			.option('--json', 'Output in JSON format')
			.option('--check-dependencies', 'Check all dependencies')
			.action(this.handleHealth.bind(this));

		// Process command group
		this.setupProcessCommands();

		// Queue command group
		this.setupQueueCommands();

		// Ranking command group
		this.setupRankingCommands();

		// Monitoring command group
		this.setupMonitoringCommands();

		// AI command group
		this.setupAICommands();

		// Cache command group
		this.setupCacheCommands();

		// Debug command group
		this.setupDebugCommands();
	}

	/**
	 * Setup process commands
	 */
	private setupProcessCommands(): void
	{
		const processCmd = this.program
			.command('process')
			.description('Domain processing operations');

		processCmd
			.command('domain <domain>')
			.description('Process a single domain')
			.option('--priority <level>', 'Job priority (low|medium|high)', 'medium')
			.option('--type <type>', 'Crawl type (full|quick|security|performance)', 'full')
			.option('--wait', 'Wait for completion')
			.action(async (domain, options, command) =>
			{
				const globalOptions = command.optsWithGlobals() as { quiet?: boolean; verbose?: boolean; json?: boolean };
				this.cliLogger = sharedLogger.getCLILogger('WorkerCLI', globalOptions);
				await this.ensureService();
				await this.processCommands.processDomain(domain, options);
			});

		processCmd
			.command('batch <file>')
			.description('Process domains from file')
			.option('--concurrency <number>', 'Batch concurrency', '5')
			.option('--priority <level>', 'Job priority (low|medium|high)', 'medium')
			.action(async (file, options, command) =>
			{
				const globalOptions = command.optsWithGlobals() as { quiet?: boolean; verbose?: boolean; json?: boolean };
				this.cliLogger = sharedLogger.getCLILogger('WorkerCLI', globalOptions);
				await this.ensureService();
				await this.processCommands.processBatch(file, options);
			});
	}

	/**
	 * Setup queue commands
	 */
	private setupQueueCommands(): void
	{
		const queueCmd = this.program
			.command('queue')
			.description('Job queue operations');

		queueCmd
			.command('stats')
			.description('Show queue statistics')
			.option('--json', 'Output in JSON format')
			.action(async (options) =>
			{
				await this.ensureService();
				await this.queueCommands.showStats(options);
			});

		queueCmd
			.command('pause <queue>')
			.description('Pause a queue')
			.action(async (queue) =>
			{
				await this.ensureService();
				await this.queueCommands.pauseQueue(queue);
			});

		queueCmd
			.command('resume <queue>')
			.description('Resume a queue')
			.action(async (queue) =>
			{
				await this.ensureService();
				await this.queueCommands.resumeQueue(queue);
			});

		queueCmd
			.command('clear <queue>')
			.description('Clear a queue')
			.option('--status <status>', 'Job status to clear (completed|failed|all)', 'all')
			.option('--force', 'Force clear without confirmation')
			.action(async (queue, options) =>
			{
				await this.ensureService();
				await this.queueCommands.clearQueue(queue, options);
			});

		queueCmd
			.command('list')
			.description('List jobs in queue')
			.option('--status <status>', 'Filter by status', 'all')
			.option('--limit <number>', 'Maximum jobs to show', '100')
			.option('--offset <number>', 'Offset for pagination', '0')
			.option('--json', 'Output in JSON format')
			.action(async (options) =>
			{
				await this.ensureService();
				await this.queueCommands.listJobs(options);
			});

		queueCmd
			.command('job <id>')
			.description('Get job details')
			.option('--json', 'Output in JSON format')
			.action(async (id, options) =>
			{
				await this.ensureService();
				await this.queueCommands.getJob(id, options);
			});

		queueCmd
			.command('retry-failed')
			.description('Retry failed jobs')
			.option('--limit <number>', 'Maximum jobs to retry', '100')
			.action(async (options) =>
			{
				await this.ensureService();
				await this.queueCommands.retryFailed(options);
			});
	}

	/**
	 * Setup ranking commands
	 */
	private setupRankingCommands(): void
	{
		const rankingCmd = this.program
			.command('ranking')
			.description('Domain ranking operations');

		rankingCmd
			.command('calculate <domain>')
			.description('Calculate ranking for a domain')
			.option('--recalculate', 'Force recalculation')
			.option('--history', 'Include ranking history')
			.option('--json', 'Output in JSON format')
			.action(async (domain, options) =>
			{
				await this.ensureService();
				await this.rankingCommands.calculateRanking(domain, options);
			});

		rankingCmd
			.command('update')
			.description('Update rankings for domains')
			.option('--batch <size>', 'Batch size', '100')
			.option('--max-age <days>', 'Maximum age in days', '7')
			.option('--dry-run', 'Simulate without updating')
			.action(async (options) =>
			{
				await this.ensureService();
				await this.rankingCommands.updateRankings(options);
			});

		rankingCmd
			.command('stats')
			.description('Show ranking statistics')
			.option('--json', 'Output in JSON format')
			.action(async (options) =>
			{
				await this.ensureService();
				await this.rankingCommands.showRankingStats(options);
			});

		rankingCmd
			.command('export <file>')
			.description('Export rankings to file')
			.option('--format <format>', 'Export format (csv|json|tsv)', 'csv')
			.option('--limit <number>', 'Maximum domains to export', '10000')
			.option('--min-score <score>', 'Minimum score threshold', '0')
			.option('--metadata', 'Include metadata')
			.action(async (file, options) =>
			{
				await this.ensureService();
				await this.rankingCommands.exportRankings(file, options);
			});

		rankingCmd
			.command('compare')
			.description('Compare rankings between dates')
			.option('--from <date>', 'From date (ISO format)')
			.option('--to <date>', 'To date (ISO format)')
			.option('--top <number>', 'Top N domains to compare', '1000')
			.option('--detailed', 'Show detailed comparison')
			.option('--json', 'Output in JSON format')
			.action(async (options) =>
			{
				await this.ensureService();
				await this.rankingCommands.compareRankings(options);
			});
	}

	/**
	 * Setup monitoring commands
	 */
	private setupMonitoringCommands(): void
	{
		const monitorCmd = this.program
			.command('monitor')
			.description('Monitoring and metrics operations');

		monitorCmd
			.command('metrics')
			.description('Show worker service metrics')
			.option('--json', 'Output in JSON format')
			.action(async (options) =>
			{
				await this.ensureService();
				await this.monitoringCommands.showMetrics(options);
			});

		monitorCmd
			.command('watch')
			.description('Watch metrics in real-time')
			.option('--interval <ms>', 'Update interval in milliseconds', '5000')
			.action(async (options) =>
			{
				await this.ensureService();
				await this.monitoringCommands.watchMetrics(options);
			});

		monitorCmd
			.command('errors')
			.description('Show error logs')
			.option('--limit <number>', 'Maximum errors to show', '50')
			.option('--since <date>', 'Show errors since date')
			.option('--severity <level>', 'Filter by severity (all|warning|error|critical)', 'all')
			.option('--json', 'Output in JSON format')
			.action(async (options) =>
			{
				await this.ensureService();
				await this.monitoringCommands.showErrors(options);
			});

		monitorCmd
			.command('report')
			.description('Generate performance report')
			.option('--period <period>', 'Report period (hour|day|week|month)', 'day')
			.option('--format <format>', 'Output format (text|json)', 'text')
			.option('--detailed', 'Include detailed metrics')
			.option('--output <file>', 'Save report to file')
			.action(async (options) =>
			{
				await this.ensureService();
				await this.monitoringCommands.generateReport(options);
			});
	}

	/**
	 * Setup AI commands
	 */
	private setupAICommands(): void
	{
		const aiCmd = this.program
			.command('ai')
			.description('AI content generation operations');

		aiCmd
			.command('generate <domain>')
			.description('Generate AI content for a domain')
			.option('--provider <provider>', 'AI provider to use')
			.option('--model <model>', 'Model to use')
			.option('--json', 'Output in JSON format')
			.action(async (domain, options, command) =>
			{
				const globalOptions = command.optsWithGlobals() as { quiet?: boolean; verbose?: boolean; json?: boolean };
				this.cliLogger = sharedLogger.getCLILogger('WorkerCLI', globalOptions);

				await this.ensureService();
				this.cliLogger.info({ domain, options, msg: 'Generating AI content' });
				// Implementation would go here
			});

		aiCmd
			.command('providers')
			.description('List available AI providers')
			.option('--json', 'Output in JSON format')
			.action(async (options, command) =>
			{
				const globalOptions = command.optsWithGlobals() as { quiet?: boolean; verbose?: boolean; json?: boolean };
				this.cliLogger = sharedLogger.getCLILogger('WorkerCLI', globalOptions);

				await this.ensureService();
				this.cliLogger.info('Listing AI providers');
				// Implementation would go here
			});
	}

	/**
	 * Setup cache commands
	 */
	private setupCacheCommands(): void
	{
		const cacheCmd = this.program
			.command('cache')
			.description('Cache management operations');

		cacheCmd
			.command('clear [pattern]')
			.description('Clear cache entries')
			.option('--type <type>', 'Cache type to clear')
			.option('--force', 'Force clear without confirmation')
			.action(async (pattern, options, command) =>
			{
				const globalOptions = command.optsWithGlobals() as { quiet?: boolean; verbose?: boolean; json?: boolean };
				this.cliLogger = sharedLogger.getCLILogger('WorkerCLI', globalOptions);

				await this.ensureService();
				this.cliLogger.info({ pattern, options, msg: 'Clearing cache' });
				// Implementation would go here
			});

		cacheCmd
			.command('stats')
			.description('Show cache statistics')
			.option('--json', 'Output in JSON format')
			.action(async (options, command) =>
			{
				const globalOptions = command.optsWithGlobals() as { quiet?: boolean; verbose?: boolean; json?: boolean };
				this.cliLogger = sharedLogger.getCLILogger('WorkerCLI', globalOptions);

				await this.ensureService();
				this.cliLogger.info({ options, msg: 'Showing cache statistics' });
				// Implementation would go here
			});
	}

	/**
	 * Setup debug commands
	 */
	private setupDebugCommands(): void
	{
		const debugCmd = this.program
			.command('debug')
			.description('Debug and diagnostic operations');

		debugCmd
			.command('trace <domain>')
			.description('Trace processing for a domain')
			.option('--verbose', 'Verbose output')
			.action(async (domain, options, command) =>
			{
				const globalOptions = command.optsWithGlobals() as { quiet?: boolean; verbose?: boolean; json?: boolean };
				this.cliLogger = sharedLogger.getCLILogger('WorkerCLI', globalOptions);

				await this.ensureService();
				this.cliLogger.info({ domain, options, msg: 'Tracing domain processing' });
				// Implementation would go here
			});

		debugCmd
			.command('dump-config')
			.description('Dump current configuration')
			.option('--json', 'Output in JSON format')
			.action(async (options, command) =>
			{
				const globalOptions = command.optsWithGlobals() as { quiet?: boolean; verbose?: boolean; json?: boolean };
				this.cliLogger = sharedLogger.getCLILogger('WorkerCLI', globalOptions);

				await this.ensureService();
				const config = this.workerService!.getConfig();
				if (options.json || globalOptions.json)
				{
					this.cliLogger.outputData(config);
				}
				else
				{
					this.cliLogger.info({ config, msg: 'Current configuration' });
				}
			});
	}

	/**
	 * Handle start command
	 */
	private async handleStart(options: { config?: string; daemon?: boolean; maxConcurrent?: string }, command: Command): Promise<void>
	{
		try
		{
			const globalOptions = command.optsWithGlobals() as { quiet?: boolean; verbose?: boolean; json?: boolean };
			this.cliLogger = sharedLogger.getCLILogger('WorkerCLI', globalOptions);

			this.cliLogger.info('Starting worker service', { options });

			const config: Partial<WorkerConfigType> = {
				daemon: options.daemon || false
			};

			if (options.config)
			{
				// Load config from file
				const fs = await import('fs/promises');
				const configData = await fs.readFile(options.config, 'utf-8');
				Object.assign(config, JSON.parse(configData));
			}

			this.workerService = new WorkerService(config as WorkerConfigType);
			await this.workerService.start();

			this.cliLogger.success('Worker service started successfully');

			if (!options.daemon)
			{
				// Keep process running
				process.on('SIGINT', () => this.handleStop({ timeout: '30' }, command));
				process.on('SIGTERM', () => this.handleStop({ timeout: '30' }, command));
			}
		}
		catch (error)
		{
			this.cliLogger.error('Failed to start worker service', error as Error);
			process.exit(1);
		}
	}

	/**
	 * Handle stop command
	 */
	private async handleStop(options: { timeout?: string }, command: Command): Promise<void>
	{
		try
		{
			const globalOptions = command.optsWithGlobals() as { quiet?: boolean; verbose?: boolean; json?: boolean };
			this.cliLogger = sharedLogger.getCLILogger('WorkerCLI', globalOptions);

			this.cliLogger.info('Stopping worker service', { options });

			if (!this.workerService)
			{
				this.cliLogger.warn('Worker service is not running');
				return;
			}

			const timeout = parseInt(options.timeout || '30');
			await this.workerService.stop();

			this.cliLogger.success('Worker service stopped successfully');
			process.exit(0);
		}
		catch (error)
		{
			this.cliLogger.error('Failed to stop worker service', error as Error);
			process.exit(1);
		}
	}

	/**
	 * Handle status command
	 */
	private async handleStatus(options: { json?: boolean; detailed?: boolean }, command: Command): Promise<void>
	{
		try
		{
			// Update logger with command options
			const globalOptions = command.optsWithGlobals() as { quiet?: boolean; verbose?: boolean; json?: boolean };
			this.cliLogger = sharedLogger.getCLILogger('WorkerCLI', globalOptions);

			await this.ensureService();
			const status = await this.workerService!.getStatus();

			if (options.json || globalOptions.json)
			{
				this.cliLogger.outputData(status);
				return;
			}

			this.cliLogger.info({
				state: status.state,
				uptime: status.uptime,
				workersActive: status.workers.active,
				jobsProcessed: status.jobs.processed,
				jobsPending: status.jobs.pending,
				health: status.health,
				msg: 'Worker service status',
			});

			if (options.detailed)
			{
				this.cliLogger.info({ details: status, msg: 'Detailed status' });
			}
		}
		catch (error)
		{
			this.cliLogger.error({ error: error as Error, msg: 'Failed to get status' });
			process.exit(1);
		}
	}

	/**
	 * Handle health command
	 */
	private async handleHealth(options: { json?: boolean; checkDependencies?: boolean }, command: Command): Promise<void>
	{
		try
		{
			// Update logger with command options
			const globalOptions = command.optsWithGlobals() as { quiet?: boolean; verbose?: boolean; json?: boolean };
			this.cliLogger = sharedLogger.getCLILogger('WorkerCLI', globalOptions);

			await this.ensureService();
			const health = await this.workerService!.checkHealth({
				checkDependencies: options.checkDependencies || false
			});

			if (options.json || globalOptions.json)
			{
				this.cliLogger.outputData(health);
				return;
			}

			const isHealthy = health.status === 'healthy';
			if (isHealthy) {
				this.cliLogger.success('Health check passed', {
					status: health.status,
					checks: health.checks,
					timestamp: health.timestamp
				});
			} else {
				this.cliLogger.error({
					status: health.status,
					checks: health.checks,
					timestamp: health.timestamp,
					msg: 'Health check failed',
				});
			}

			process.exit(isHealthy ? 0 : 1);
		}
		catch (error)
		{
			this.cliLogger.error({ error: error as Error, msg: 'Failed to check health' });
			process.exit(1);
		}
	}

	/**
	 * Ensure worker service is initialized
	 */
	private async ensureService(): Promise<void>
	{
		if (!this.workerService)
		{
			this.workerService = new WorkerService();
			await this.workerService.initialize();

			// Initialize command handlers
			this.processCommands = new ProcessCommands(this.workerService);
			this.queueCommands = new QueueCommands(this.workerService);
			this.rankingCommands = new RankingCommands(this.workerService);
			this.monitoringCommands = new MonitoringCommands(this.workerService);
		}
	}

	/**
	 * Run the CLI
	 */
	run(): void
	{
		this.program.parse(process.argv);
	}
}

// Export for use as module
export default WorkerCLI;

// Run if executed directly
if (require.main === module)
{
	const cli = new WorkerCLI();
	cli.run();
}
