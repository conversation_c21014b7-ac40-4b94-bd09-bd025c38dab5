/**
 * Job Consumer
 *
 * Handles domain job consumption with configurable concurrency and
 * integrates with the domain processing pipeline.
 */

import { EventEmitter } from 'events';
import { logger as loggerFactory, AsyncUtils } from '@shared';
import { WorkerJobQueue } from './WorkerJobQueue';
import type {
	JobDataType,
	JobHandlerType,
	DomainJobType,
	ProcessingResultType,
} from '../types/WorkerTypes';
import type {
	ConsumerConfig,
	ConsumerMetrics,
	JobProgressUpdate,
} from './types';

const logger = loggerFactory.getLogger('job-consumer');

/**
 * Domain Job Consumer
 *
 * Consumes domain processing jobs from queues and orchestrates
 * their processing through the domain processing pipeline.
 */
class JobConsumer extends EventEmitter
{
	private jobQueue: WorkerJobQueue;
	private maxConcurrentTasks: number;
	private isConsuming = false;
	private activeJobs = new Map<string, JobData>();
	private consumerConfigs = new Map<string, ConsumerConfig>();
	private metrics = new Map<string, ConsumerMetrics>();
	private startTime = new Date();

	// Job processing pipeline - will be injected when available
	private domainProcessingPipeline: any = null;

	constructor(jobQueue: WorkerJobQueue, maxConcurrentTasks: number = 10)
	{
		super();
		this.jobQueue = jobQueue;
		this.maxConcurrentTasks = maxConcurrentTasks;
	}

	/**
	 * Set the domain processing pipeline
	 */
	setDomainProcessingPipeline(pipeline: any): void
	{
		this.domainProcessingPipeline = pipeline;
	}

	/**
	 * Start consuming jobs from all configured queues
	 */
	async startConsuming(): Promise<void>
	{
		if (this.isConsuming)
		{
			logger.warn('Job consumer already consuming');
			return;
		}

		try
		{
			logger.info('Starting job consumption...', {
				maxConcurrentTasks: this.maxConcurrentTasks,
				configuredQueues: Array.from(this.consumerConfigs.keys()),
			});

			// Register consumers for all configured queues
			for (const [queueName, config] of this.consumerConfigs)
			{
				if (config.enabled)
				{
					await this.registerQueueConsumer(queueName, config);
				}
			}

			this.isConsuming = true;
			this.startTime = new Date();

			// Emit started event
			this.emit('started');

			logger.info('Job consumption started successfully');
		}
		catch (error)
		{
			logger.error('Failed to start job consumption:', error);
			throw error;
		}
	}

	/**
	 * Stop consuming jobs
	 */
	async stopConsuming(): Promise<void>
	{
		if (!this.isConsuming)
		{
			logger.warn('Job consumer not consuming');
			return;
		}

		try
		{
			logger.info('Stopping job consumption...');

			// Wait for active jobs to complete (with timeout)
			await this.waitForActiveJobsToComplete(30000); // 30 seconds timeout

			this.isConsuming = false;

			// Emit stopped event
			this.emit('stopped');

			logger.info('Job consumption stopped successfully');
		}
		catch (error)
		{
			logger.error('Failed to stop job consumption:', error);
			throw error;
		}
	}

	/**
	 * Configure a queue for consumption
	 */
	configureQueue(
		queueName: string,
		handler: JobHandler,
		options: {
			concurrency?: number;
			retryThreshold?: number;
			enabled?: boolean;
		} = {},
	): void
	{
		const config: ConsumerConfig = {
			queueName,
			concurrency: Math.min(options.concurrency || 1, this.maxConcurrentTasks),
			retryThreshold: options.retryThreshold || 3,
			enabled: options.enabled !== false,
			handler,
		};

		this.consumerConfigs.set(queueName, config);
		this.initializeQueueMetrics(queueName);

		logger.info('Queue configured for consumption:', {
			queueName,
			concurrency: config.concurrency,
			retryThreshold: config.retryThreshold,
			enabled: config.enabled,
		});
	}

	/**
	 * Configure domain crawl queue (main queue for worker)
	 */
	configureDomainCrawlQueue(concurrency?: number): void
	{
		this.configureQueue(
			'queue:domain:crawl',
			this.handleDomainCrawlJob.bind(this),
			{
				concurrency: concurrency || this.maxConcurrentTasks,
				retryThreshold: 3,
				enabled: true,
			},
		);
	}

	/**
	 * Configure ranking update queue
	 */
	configureRankingUpdateQueue(concurrency?: number): void
	{
		this.configureQueue(
			'queue:ranking:update',
			this.handleRankingUpdateJob.bind(this),
			{
				concurrency: concurrency || Math.ceil(this.maxConcurrentTasks / 2),
				retryThreshold: 2,
				enabled: true,
			},
		);
	}

	/**
	 * Configure maintenance queue
	 */
	configureMaintenanceQueue(concurrency?: number): void
	{
		this.configureQueue(
			'queue:maintenance',
			this.handleMaintenanceJob.bind(this),
			{
				concurrency: concurrency || 1,
				retryThreshold: 1,
				enabled: true,
			},
		);
	}

	/**
	 * Handle domain crawl job (main job type)
	 */
	private async handleDomainCrawlJob(jobData: JobData): Promise<void>
	{
		const startTime = Date.now();

		try
		{
			logger.info('Processing domain crawl job:', {
				jobId: jobData.id,
				domain: jobData.domain,
			});

			// Track active job
			this.activeJobs.set(jobData.id, jobData);

			// Convert JobData to DomainJob format
			const domainJob: DomainJob = {
				id: jobData.id,
				domain: jobData.domain,
				priority: jobData.priority,
				crawlType: (jobData.data as any)?.crawlType || 'full',
				scheduledAt: jobData.createdAt,
				createdAt: jobData.createdAt,
				startedAt: jobData.startedAt,
				tasks: [], // Will be populated by the pipeline
				retryCount: jobData.retryCount,
				maxRetries: jobData.maxRetries,
				requestedBy: (jobData.data as any)?.requestedBy || 'system',
				metadata: jobData.metadata,
			};

			// Emit job started event
			this.emit('jobStarted', {
				jobId: jobData.id,
				domain: jobData.domain,
				queueName: 'queue:domain:crawl',
			});

			// Update progress
			await this.updateJobProgress(jobData.id, jobData.domain, 'started', 0, 'Job processing started');

			// Process through domain processing pipeline
			let result: ProcessingResult;
			if (this.domainProcessingPipeline)
			{
				result = await this.domainProcessingPipeline.processDomain(domainJob);
			}
			else
			{
				// Fallback if pipeline not available yet
				logger.warn('Domain processing pipeline not available, using placeholder');
				result = this.createPlaceholderResult(domainJob);
			}

			// Update final progress
			await this.updateJobProgress(
				jobData.id,
				jobData.domain,
				'completed',
				100,
				'Job completed successfully',
			);

			// Update metrics
			const processingTime = Date.now() - startTime;
			this.updateQueueMetrics('queue:domain:crawl', true, processingTime);

			// Emit job completed event
			this.emit('jobCompleted', {
				jobId: jobData.id,
				domain: jobData.domain,
				queueName: 'queue:domain:crawl',
				result,
				processingTime,
			});

			logger.info('Domain crawl job completed successfully:', {
				jobId: jobData.id,
				domain: jobData.domain,
				processingTime,
				status: result.status,
			});
		}
		catch (error)
		{
			const processingTime = Date.now() - startTime;

			// Update progress with error
			await this.updateJobProgress(
				jobData.id,
				jobData.domain,
				'failed',
				0,
				`Job failed: ${(error as Error).message}`,
			);

			// Update metrics
			this.updateQueueMetrics('queue:domain:crawl', false, processingTime);

			// Emit job failed event
			this.emit('jobFailed', {
				jobId: jobData.id,
				domain: jobData.domain,
				queueName: 'queue:domain:crawl',
				error: (error as Error).message,
				processingTime,
			});

			logger.error('Domain crawl job failed:', {
				jobId: jobData.id,
				domain: jobData.domain,
				error: (error as Error).message,
				processingTime,
			});

			throw error;
		}
		finally
		{
			// Remove from active jobs
			this.activeJobs.delete(jobData.id);
		}
	}

	/**
	 * Handle ranking update job
	 */
	private async handleRankingUpdateJob(jobData: JobData): Promise<void>
	{
		const startTime = Date.now();

		try
		{
			logger.info('Processing ranking update job:', {
				jobId: jobData.id,
				domain: jobData.domain,
			});

			// Track active job
			this.activeJobs.set(jobData.id, jobData);

			// Emit job started event
			this.emit('jobStarted', {
				jobId: jobData.id,
				domain: jobData.domain,
				queueName: 'queue:ranking:update',
			});

			// Ranking update logic - placeholder implementation
			// Will be implemented when ranking service is integrated
			// For now, simulate processing
			await this.simulateRankingUpdate(jobData);

			// Update metrics
			const processingTime = Date.now() - startTime;
			this.updateQueueMetrics('queue:ranking:update', true, processingTime);

			// Emit job completed event
			this.emit('jobCompleted', {
				jobId: jobData.id,
				domain: jobData.domain,
				queueName: 'queue:ranking:update',
				processingTime,
			});

			logger.info('Ranking update job completed successfully:', {
				jobId: jobData.id,
				domain: jobData.domain,
				processingTime,
			});
		}
		catch (error)
		{
			const processingTime = Date.now() - startTime;

			// Update metrics
			this.updateQueueMetrics('queue:ranking:update', false, processingTime);

			// Emit job failed event
			this.emit('jobFailed', {
				jobId: jobData.id,
				domain: jobData.domain,
				queueName: 'queue:ranking:update',
				error: (error as Error).message,
				processingTime,
			});

			logger.error('Ranking update job failed:', {
				jobId: jobData.id,
				domain: jobData.domain,
				error: (error as Error).message,
			});

			throw error;
		}
		finally
		{
			// Remove from active jobs
			this.activeJobs.delete(jobData.id);
		}
	}

	/**
	 * Handle maintenance job
	 */
	private async handleMaintenanceJob(jobData: JobData): Promise<void>
	{
		const startTime = Date.now();

		try
		{
			logger.info('Processing maintenance job:', {
				jobId: jobData.id,
				taskType: (jobData.data as any)?.taskType,
			});

			// Track active job
			this.activeJobs.set(jobData.id, jobData);

			// Emit job started event
			this.emit('jobStarted', {
				jobId: jobData.id,
				queueName: 'queue:maintenance',
			});

			// Maintenance logic - placeholder implementation
			// Will be implemented when maintenance service is integrated
			// For now, simulate processing
			await this.simulateMaintenanceTask(jobData);

			// Update metrics
			const processingTime = Date.now() - startTime;
			this.updateQueueMetrics('queue:maintenance', true, processingTime);

			// Emit job completed event
			this.emit('jobCompleted', {
				jobId: jobData.id,
				queueName: 'queue:maintenance',
				processingTime,
			});

			logger.info('Maintenance job completed successfully:', {
				jobId: jobData.id,
				processingTime,
			});
		}
		catch (error)
		{
			const processingTime = Date.now() - startTime;

			// Update metrics
			this.updateQueueMetrics('queue:maintenance', false, processingTime);

			// Emit job failed event
			this.emit('jobFailed', {
				jobId: jobData.id,
				queueName: 'queue:maintenance',
				error: (error as Error).message,
				processingTime,
			});

			logger.error('Maintenance job failed:', {
				jobId: jobData.id,
				error: (error as Error).message,
			});

			throw error;
		}
		finally
		{
			// Remove from active jobs
			this.activeJobs.delete(jobData.id);
		}
	}

	/**
	 * Get current active jobs count
	 */
	getActiveJobsCount(): number
	{
		return this.activeJobs.size;
	}

	/**
	 * Get active jobs by queue
	 */
	getActiveJobsByQueue(): Record<string, number>
	{
		const result: Record<string, number> = {};

		for (const job of this.activeJobs.values())
		{
			const queueName = job.type;
			result[queueName] = (result[queueName] || 0) + 1;
		}

		return result;
	}

	/**
	 * Get consumer metrics for all queues
	 */
	getMetrics(): Record<string, ConsumerMetrics>
	{
		const result: Record<string, ConsumerMetrics> = {};

		for (const [queueName, metrics] of this.metrics)
		{
			result[queueName] = { ...metrics };
		}

		return result;
	}

	/**
	 * Get consumer health status
	 */
	getHealth(): {
		status: 'healthy' | 'degraded' | 'unhealthy';
		isConsuming: boolean;
		activeJobs: number;
		maxConcurrentTasks: number;
		uptime: number;
		queues: Record<string, { enabled: boolean; activeJobs: number }>;
	}
	{
		const now = new Date();
		const uptime = now.getTime() - this.startTime.getTime();
		const activeJobsCount = this.activeJobs.size;

		// Determine health status
		let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
		if (!this.isConsuming)
		{
			status = 'unhealthy';
		}
		else if (activeJobsCount >= this.maxConcurrentTasks * 0.9) // 90% capacity
		{
			status = 'degraded';
		}

		// Get queue status
		const queues: Record<string, { enabled: boolean; activeJobs: number }> = {};
		for (const [queueName, config] of this.consumerConfigs)
		{
			const activeJobsForQueue = Array.from(this.activeJobs.values())
				.filter(job => job.type === queueName).length;

			queues[queueName] = {
				enabled: config.enabled,
				activeJobs: activeJobsForQueue,
			};
		}

		return {
			status,
			isConsuming: this.isConsuming,
			activeJobs: activeJobsCount,
			maxConcurrentTasks: this.maxConcurrentTasks,
			uptime,
			queues,
		};
	}

	/**
	 * Pause consumption for a specific queue
	 */
	async pauseQueue(queueName: string): Promise<void>
	{
		const config = this.consumerConfigs.get(queueName);
		if (config)
		{
			config.enabled = false;
			await this.jobQueue.pauseQueue(queueName);
			logger.info(`Queue consumption paused: ${queueName}`);
		}
	}

	/**
	 * Resume consumption for a specific queue
	 */
	async resumeQueue(queueName: string): Promise<void>
	{
		const config = this.consumerConfigs.get(queueName);
		if (config)
		{
			config.enabled = true;
			await this.jobQueue.resumeQueue(queueName);
			logger.info(`Queue consumption resumed: ${queueName}`);
		}
	}

	// ============================================================================
	// Private Helper Methods
	// ============================================================================

	/**
	 * Register a consumer for a specific queue
	 */
	private async registerQueueConsumer(queueName: string, config: ConsumerConfig): Promise<void>
	{
		await this.jobQueue.createConsumer(queueName, config.handler, {
			concurrency: config.concurrency,
			retryThreshold: config.retryThreshold,
		});

		logger.info(`Consumer registered for queue: ${queueName}`, {
			concurrency: config.concurrency,
			retryThreshold: config.retryThreshold,
		});
	}

	/**
	 * Wait for active jobs to complete
	 */
	private async waitForActiveJobsToComplete(timeoutMs: number): Promise<void>
	{
		const startTime = Date.now();

		while (this.activeJobs.size > 0 && (Date.now() - startTime) < timeoutMs)
		{
			logger.info(`Waiting for ${this.activeJobs.size} active jobs to complete...`);
			await this.delay(1000); // Check every second
		}

		if (this.activeJobs.size > 0)
		{
			logger.warn(`Timeout waiting for jobs to complete. ${this.activeJobs.size} jobs still active.`);
		}
	}

	/**
	 * Initialize metrics for a queue
	 */
	private initializeQueueMetrics(queueName: string): void
	{
		this.metrics.set(queueName, {
			queueName,
			jobsProcessed: 0,
			jobsSucceeded: 0,
			jobsFailed: 0,
			averageProcessingTime: 0,
			uptime: 0,
		});
	}

	/**
	 * Update metrics for a queue
	 */
	private updateQueueMetrics(queueName: string, success: boolean, processingTime: number): void
	{
		let metrics = this.metrics.get(queueName);
		if (!metrics)
		{
			this.initializeQueueMetrics(queueName);
			metrics = this.metrics.get(queueName)!;
		}

		metrics.jobsProcessed++;
		if (success)
		{
			metrics.jobsSucceeded++;
		}
		else
		{
			metrics.jobsFailed++;
		}

		// Update average processing time
		const totalProcessingTime = metrics.averageProcessingTime * (metrics.jobsProcessed - 1);
		metrics.averageProcessingTime = (totalProcessingTime + processingTime) / metrics.jobsProcessed;

		metrics.lastJobProcessed = new Date();
		metrics.uptime = Date.now() - this.startTime.getTime();
	}

	/**
	 * Update job progress
	 */
	private async updateJobProgress(
		jobId: string,
		domain: string,
		stage: string,
		progress: number,
		message: string,
	): Promise<void>
	{
		const progressUpdate: JobProgressUpdate = {
			jobId,
			domain,
			stage,
			progress,
			message,
			timestamp: new Date(),
		};

		// Emit progress update event
		this.emit('jobProgress', progressUpdate);

		// Progress tracking - placeholder implementation
		// Will integrate with persistent storage when available
	}

	/**
	 * Create placeholder result for testing
	 */
	private createPlaceholderResult(domainJob: DomainJob): ProcessingResult
	{
		return {
			domain: domainJob.domain,
			jobId: domainJob.id,
			status: 'success',
			crawlingResult: {
				domain: domainJob.domain,
				status: 'success',
				completedModules: ['dns', 'robots', 'ssl', 'homepage'],
				failedModules: [],
				data: {},
				errors: [],
			},
			rankingResult: {
				domain: domainJob.domain,
				status: 'success',
				scores: { performance: 85, security: 90, seo: 80 },
				compositeScore: 85,
				grade: 'B+',
				ranking: 1000,
				errors: [],
			},
			indexingResult: {
				domain: domainJob.domain,
				status: 'success',
				updatedIndexes: ['manticore'],
				invalidatedCaches: ['redis'],
				syncedDatabases: ['scylla', 'maria'],
				errors: [],
			},
			totalProcessingTime: 5000,
			phaseTimings: {
				crawling: 3000,
				ranking: 1000,
				indexing: 1000,
			},
			lockAcquired: true,
			lockAcquisitionTime: 100,
			lockHoldTime: 5000,
			errors: [],
			warnings: [],
			resourceUsage: {
				cpuTime: 2000,
				memoryPeak: 100 * 1024 * 1024, // 100MB
				memoryAverage: 80 * 1024 * 1024, // 80MB
				diskIO: 1024 * 1024, // 1MB
				networkIO: 5 * 1024 * 1024, // 5MB
			},
			taskMetrics: [],
		};
	}

	/**
	 * Simulate ranking update processing
	 */
	private async simulateRankingUpdate(jobData: JobData): Promise<void>
	{
		// Simulate processing time
		await this.delay(2000);

		logger.debug('Ranking update simulation completed:', {
			jobId: jobData.id,
			domain: jobData.domain,
		});
	}

	/**
	 * Simulate maintenance task processing
	 */
	private async simulateMaintenanceTask(jobData: JobData): Promise<void>
	{
		const taskType = (jobData.data as unknown)?.taskType || 'unknown';

		// Simulate processing time based on task type
		const processingTime = taskType === 'cleanup' ? 5000 : 1000;
		await this.delay(processingTime);

		logger.debug('Maintenance task simulation completed:', {
			jobId: jobData.id,
			taskType,
		});
	}

	/**
	 * Delay utility
	 */
	private delay(ms: number): Promise<void>
	{
		return AsyncUtils.sleep(ms);
	}
}

export { JobConsumer };
