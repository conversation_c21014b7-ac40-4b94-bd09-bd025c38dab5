/**
 * Job Scheduler
 *
 * Extracted job scheduling functionality from the scheduler service
 * with enhanced features for the worker service.
 */

import { CronJob } from 'cron';
import { v4 as uuidv4 } from 'uuid';
import { logger as loggerFactory } from '@shared/utils/Logger';
import { WorkerJobQueue } from './WorkerJobQueue';
import type {
	JobOptions,
	JobPriority,
	JobData,
} from '../types/WorkerTypes';
import type {
	ScheduledJob,
	RecurringJobConfig,
	BatchJobRequest,
	BatchJobResult,
	QueueConfig,
	JobStatistics,
} from './types';

const logger = loggerFactory.getLogger('job-scheduler');

/**
 * Enhanced Job Scheduler for Worker Service
 *
 * Provides comprehensive job scheduling capabilities including:
 * - Immediate job scheduling with priority handling
 * - Recurring job scheduling with cron expressions
 * - Batch job processing with rate limiting
 * - Job statistics and monitoring
 */
class JobScheduler
{
	private jobQueue: WorkerJobQueue;
	private queueConfigs = new Map<string, QueueConfig>();
	private scheduledJobs = new Map<string, ScheduledJob>();
	private cronJobs = new Map<string, CronJob>();
	private jobStatistics = new Map<string, JobStatistics>();
	private isInitialized = false;

	// Default queue configurations
	private readonly defaultQueues: QueueConfig[] = [
		{
			name: 'queue:domain:crawl',
			concurrency: 5,
			enabled: true,
			retryThreshold: 3,
		},
		{
			name: 'queue:ranking:update',
			concurrency: 3,
			enabled: true,
			retryThreshold: 3,
		},
		{
			name: 'queue:traffic:analysis',
			concurrency: 2,
			enabled: true,
			retryThreshold: 2,
		},
		{
			name: 'queue:backlink:analysis',
			concurrency: 2,
			enabled: true,
			retryThreshold: 2,
		},
		{
			name: 'queue:manticore:sync',
			concurrency: 1,
			enabled: true,
			retryThreshold: 3,
		},
		{
			name: 'queue:maintenance',
			concurrency: 1,
			enabled: true,
			retryThreshold: 1,
		},
	];

	constructor(jobQueue: WorkerJobQueue)
	{
		this.jobQueue = jobQueue;
		this.setupDefaultQueues();
	}

	/**
	 * Initialize the job scheduler
	 */
	async initialize(): Promise<void>
	{
		if (this.isInitialized)
		{
			logger.warn('Job scheduler already initialized');
			return;
		}

		try
		{
			logger.info('Initializing Job Scheduler...');

			// Initialize statistics for all queues
			for (const queueConfig of this.defaultQueues)
			{
				this.initializeJobStatistics(queueConfig.name);
			}

			// Start any existing scheduled jobs
			await this.startScheduledJobs();

			this.isInitialized = true;
			logger.info('Job Scheduler initialized successfully');
		}
		catch (error)
		{
			logger.error('Failed to initialize Job Scheduler:', error);
			throw error;
		}
	}

	/**
	 * Shutdown the job scheduler
	 */
	async shutdown(): Promise<void>
	{
		try
		{
			logger.info('Shutting down Job Scheduler...');

			// Stop all cron jobs
			for (const [jobId, cronJob] of this.cronJobs)
			{
				cronJob.stop();
				logger.info(`Stopped scheduled job: ${jobId}`);
			}

			// Clear collections
			this.cronJobs.clear();
			this.scheduledJobs.clear();

			this.isInitialized = false;
			logger.info('Job Scheduler shut down successfully');
		}
		catch (error)
		{
			logger.error('Error shutting down Job Scheduler:', error);
			throw error;
		}
	}

	/**
	 * Schedule a job with priority handling
	 */
	async scheduleJob(
		queueName: string,
		jobData: unknown,
		options: JobOptions = {},
	): Promise<string>
	{
		if (!this.isInitialized)
		{
			throw new Error('Job scheduler not initialized');
		}

		const queueConfig = this.queueConfigs.get(queueName);
		if (!queueConfig || !queueConfig.enabled)
		{
			throw new Error(`Queue ${queueName} is not configured or disabled`);
		}

		try
		{
			// Publish job to queue
			const jobId = await this.jobQueue.publishJob(queueName, jobData, options);

			// Update statistics
			this.updateJobStatistics(queueName, 'scheduled');

			logger.info('Job scheduled successfully:', {
				jobId,
				queueName,
				priority: options.priority || 'medium',
			});

			return jobId;
		}
		catch (error)
		{
			logger.error(`Failed to schedule job in queue ${queueName}:`, error);
			throw error;
		}
	}

	/**
	 * Schedule domain crawl job
	 */
	async scheduleDomainCrawlJob(
		domain: string,
		crawlType: 'full' | 'quick' | 'security' | 'performance' | 'content' = 'full',
		priority: JobPriority = 'medium',
		metadata: Record<string, unknown> = {},
	): Promise<string>
	{
		const jobData = {
			domain,
			crawlType,
			scheduledAt: new Date().toISOString(),
			metadata,
		};

		return this.scheduleJob('queue:domain:crawl', jobData, {
			priority,
			maxRetries: 3,
		});
	}

	/**
	 * Schedule ranking update job
	 */
	async scheduleRankingUpdateJob(
		domain: string,
		rankingType: string = 'global',
		priority: JobPriority = 'medium',
	): Promise<string>
	{
		const jobData = {
			domain,
			rankingType,
			scheduledAt: new Date().toISOString(),
		};

		return this.scheduleJob('queue:ranking:update', jobData, {
			priority,
			maxRetries: 2,
		});
	}

	/**
	 * Schedule traffic analysis job
	 */
	async scheduleTrafficAnalysisJob(
		domain: string,
		priority: JobPriority = 'low',
	): Promise<string>
	{
		const jobData = {
			domain,
			scheduledAt: new Date().toISOString(),
		};

		return this.scheduleJob('queue:traffic:analysis', jobData, {
			priority,
			maxRetries: 2,
		});
	}

	/**
	 * Schedule backlink analysis job
	 */
	async scheduleBacklinkAnalysisJob(
		domain: string,
		priority: JobPriority = 'low',
	): Promise<string>
	{
		const jobData = {
			domain,
			scheduledAt: new Date().toISOString(),
		};

		return this.scheduleJob('queue:backlink:analysis', jobData, {
			priority,
			maxRetries: 2,
		});
	}

	/**
	 * Schedule Manticore sync job
	 */
	async scheduleManticoreSyncJob(
		domain: string,
		syncType: 'full' | 'incremental' = 'incremental',
		priority: JobPriority = 'medium',
	): Promise<string>
	{
		const jobData = {
			domain,
			syncType,
			scheduledAt: new Date().toISOString(),
		};

		return this.scheduleJob('queue:manticore:sync', jobData, {
			priority,
			maxRetries: 3,
		});
	}

	/**
	 * Schedule maintenance job
	 */
	async scheduleMaintenanceJob(
		taskType: string,
		taskData: Record<string, unknown> = {},
		priority: JobPriority = 'low',
	): Promise<string>
	{
		const jobData = {
			taskType,
			taskData,
			scheduledAt: new Date().toISOString(),
		};

		return this.scheduleJob('queue:maintenance', jobData, {
			priority,
			maxRetries: 1,
		});
	}

	/**
	 * Schedule batch jobs with rate limiting
	 */
	async scheduleBatchJobs(request: BatchJobRequest): Promise<BatchJobResult>
	{
		logger.info('Scheduling batch jobs:', {
			totalJobs: request.jobs.length,
			batchSize: request.batchOptions?.batchSize,
		});

		return this.jobQueue.publishBatchJobs(request);
	}

	/**
	 * Schedule batch domain crawl jobs
	 */
	async scheduleBatchDomainCrawlJobs(
		domains: string[],
		crawlType: 'full' | 'quick' | 'security' | 'performance' = 'full',
		priority: JobPriority = 'medium',
		batchOptions?: {
			batchSize?: number;
			delayBetweenBatches?: number;
		},
	): Promise<BatchJobResult>
	{
		const jobs = domains.map(domain => ({
			queueName: 'queue:domain:crawl',
			jobData: {
				domain,
				crawlType,
				scheduledAt: new Date().toISOString(),
			},
			options: {
				priority,
				maxRetries: 3,
			},
		}));

		return this.scheduleBatchJobs({
			jobs,
			batchOptions,
		});
	}

	/**
	 * Schedule recurring job with cron expression
	 */
	async scheduleRecurringJob(config: RecurringJobConfig): Promise<string>
	{
		const jobId = uuidv4();

		try
		{
			const scheduledJob: ScheduledJob = {
				id: jobId,
				queueName: config.queueName,
				jobData: config.jobData,
				cronExpression: config.cronExpression,
				options: config.options || {},
				enabled: config.enabled !== false,
				nextRun: this.calculateNextRun(config.cronExpression),
				runCount: 0,
			};

			// Create cron job
			const cronJob = new CronJob(
				config.cronExpression,
				async () =>
				{
					await this.executeScheduledJob(jobId);
				},
				null,
				scheduledJob.enabled,
				'UTC', // Use UTC timezone for consistency
			);

			// Store job and cron instance
			this.scheduledJobs.set(jobId, scheduledJob);
			this.cronJobs.set(jobId, cronJob);

			logger.info('Recurring job scheduled:', {
				jobId,
				queueName: config.queueName,
				cronExpression: config.cronExpression,
				enabled: scheduledJob.enabled,
				nextRun: scheduledJob.nextRun,
			});

			return jobId;
		}
		catch (error)
		{
			logger.error('Failed to schedule recurring job:', error);
			throw error;
		}
	}

	/**
	 * Schedule daily domain discovery job
	 */
	async scheduleDailyDomainDiscovery(): Promise<string>
	{
		return this.scheduleRecurringJob({
			queueName: 'queue:maintenance',
			jobData: {
				taskType: 'domain_discovery',
				taskData: {
					discoveryType: 'daily',
					maxDomains: 1000,
				},
			},
			cronExpression: '0 2 * * *', // Daily at 2 AM UTC
			options: {
				priority: 'low',
				maxRetries: 2,
			},
		});
	}

	/**
	 * Schedule weekly ranking recalculation
	 */
	async scheduleWeeklyRankingRecalculation(): Promise<string>
	{
		return this.scheduleRecurringJob({
			queueName: 'queue:ranking:update',
			jobData: {
				rankingType: 'global_recalculation',
				scheduledAt: new Date().toISOString(),
			},
			cronExpression: '0 1 * * 0', // Weekly on Sunday at 1 AM UTC
			options: {
				priority: 'high',
				maxRetries: 3,
			},
		});
	}

	/**
	 * Schedule hourly maintenance cleanup
	 */
	async scheduleHourlyCleanup(): Promise<string>
	{
		return this.scheduleRecurringJob({
			queueName: 'queue:maintenance',
			jobData: {
				taskType: 'cleanup',
				taskData: {
					cleanupType: 'hourly',
					maxAge: '24h',
				},
			},
			cronExpression: '0 * * * *', // Every hour
			options: {
				priority: 'low',
				maxRetries: 1,
			},
		});
	}

	/**
	 * Enable/disable a scheduled job
	 */
	async toggleScheduledJob(jobId: string, enabled: boolean): Promise<void>
	{
		const scheduledJob = this.scheduledJobs.get(jobId);
		const cronJob = this.cronJobs.get(jobId);

		if (!scheduledJob || !cronJob)
		{
			throw new Error(`Scheduled job not found: ${jobId}`);
		}

		scheduledJob.enabled = enabled;

		if (enabled)
		{
			cronJob.start();
			logger.info(`Scheduled job enabled: ${jobId}`);
		}
		else
		{
			cronJob.stop();
			logger.info(`Scheduled job disabled: ${jobId}`);
		}
	}

	/**
	 * Remove a scheduled job
	 */
	async removeScheduledJob(jobId: string): Promise<void>
	{
		const cronJob = this.cronJobs.get(jobId);

		if (cronJob)
		{
			cronJob.stop();
			this.cronJobs.delete(jobId);
		}

		this.scheduledJobs.delete(jobId);

		logger.info(`Scheduled job removed: ${jobId}`);
	}

	/**
	 * Get all scheduled jobs
	 */
	getScheduledJobs(): ScheduledJob[]
	{
		return Array.from(this.scheduledJobs.values());
	}

	/**
	 * Get job statistics for a queue
	 */
	getJobStatistics(queueName: string): JobStatistics | undefined
	{
		return this.jobStatistics.get(queueName);
	}

	/**
	 * Get all job statistics
	 */
	getAllJobStatistics(): JobStatistics[]
	{
		return Array.from(this.jobStatistics.values());
	}

	/**
	 * Get scheduler health status
	 */
	getHealth(): {
		status: 'healthy' | 'degraded' | 'unhealthy';
		scheduledJobs: number;
		activeJobs: number;
		enabledJobs: number;
		disabledJobs: number;
		queues: Record<string, { enabled: boolean; statistics: JobStatistics }>;
	}
	{
		const scheduledJobsCount = this.scheduledJobs.size;
		const enabledJobs = Array.from(this.scheduledJobs.values())
			.filter(job => job.enabled).length;
		const disabledJobs = scheduledJobsCount - enabledJobs;

		// Get queue information
		const queues: Record<string, { enabled: boolean; statistics: JobStatistics }> = {};
		for (const [queueName, config] of this.queueConfigs)
		{
			const statistics = this.jobStatistics.get(queueName);
			if (statistics)
			{
				queues[queueName] = {
					enabled: config.enabled,
					statistics,
				};
			}
		}

		// Determine health status
		let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
		if (!this.isInitialized)
		{
			status = 'unhealthy';
		}
		else if (disabledJobs > enabledJobs)
		{
			status = 'degraded';
		}

		return {
			status,
			scheduledJobs: scheduledJobsCount,
			activeJobs: enabledJobs,
			enabledJobs,
			disabledJobs,
			queues,
		};
	}

	// ============================================================================
	// Private Helper Methods
	// ============================================================================

	/**
	 * Setup default queue configurations
	 */
	private setupDefaultQueues(): void
	{
		for (const queueConfig of this.defaultQueues)
		{
			this.queueConfigs.set(queueConfig.name, queueConfig);
			this.initializeJobStatistics(queueConfig.name);
		}
	}

	/**
	 * Initialize job statistics for a queue
	 */
	private initializeJobStatistics(queueName: string): void
	{
		this.jobStatistics.set(queueName, {
			queueName,
			pendingJobs: 0,
			activeJobs: 0,
			completedJobs: 0,
			failedJobs: 0,
			delayedJobs: 0,
			processingJobs: 0,
			retryJobs: 0,
			waitTimeMs: {
				average: 0,
				min: 0,
				max: 0,
				p95: 0,
				p99: 0,
			},
		});
	}

	/**
	 * Update job statistics
	 */
	private updateJobStatistics(
		queueName: string,
		action: 'scheduled' | 'processing' | 'completed' | 'failed' | 'retry',
	): void
	{
		let stats = this.jobStatistics.get(queueName);
		if (!stats)
		{
			this.initializeJobStatistics(queueName);
			stats = this.jobStatistics.get(queueName)!;
		}

		switch (action)
		{
			case 'scheduled':
				stats.pendingJobs++;
				break;
			case 'processing':
				stats.pendingJobs = Math.max(0, stats.pendingJobs - 1);
				stats.processingJobs++;
				break;
			case 'completed':
				stats.processingJobs = Math.max(0, stats.processingJobs - 1);
				stats.completedJobs++;
				break;
			case 'failed':
				stats.processingJobs = Math.max(0, stats.processingJobs - 1);
				stats.failedJobs++;
				break;
			case 'retry':
				stats.retryJobs++;
				break;
		}
	}

	/**
	 * Start all scheduled jobs
	 */
	private async startScheduledJobs(): Promise<void>
	{
		for (const [jobId, scheduledJob] of this.scheduledJobs)
		{
			if (scheduledJob.enabled)
			{
				const cronJob = this.cronJobs.get(jobId);
				if (cronJob)
				{
					cronJob.start();
					logger.info(`Started scheduled job: ${jobId}`);
				}
			}
		}
	}

	/**
	 * Execute a scheduled job
	 */
	private async executeScheduledJob(jobId: string): Promise<void>
	{
		const scheduledJob = this.scheduledJobs.get(jobId);
		if (!scheduledJob || !scheduledJob.enabled)
		{
			return;
		}

		try
		{
			logger.info('Executing scheduled job:', {
				jobId,
				queueName: scheduledJob.queueName,
				runCount: scheduledJob.runCount,
			});

			// Schedule the job
			await this.scheduleJob(
				scheduledJob.queueName,
				scheduledJob.jobData,
				scheduledJob.options,
			);

			// Update job metadata
			scheduledJob.lastRun = new Date();
			scheduledJob.runCount++;
			scheduledJob.nextRun = this.calculateNextRun(scheduledJob.cronExpression);

			logger.info('Scheduled job executed successfully:', {
				jobId,
				runCount: scheduledJob.runCount,
				nextRun: scheduledJob.nextRun,
			});
		}
		catch (error)
		{
			logger.error('Failed to execute scheduled job:', {
				jobId,
				error: (error as Error).message,
			});
		}
	}

	/**
	 * Calculate next run time for cron expression
	 */
	private calculateNextRun(cronExpression: string): Date
	{
		try
		{
			const cronJob = new CronJob(cronExpression, () => {}, null, false);
			return cronJob.nextDate().toDate();
		}
		catch (error)
		{
			logger.error('Failed to calculate next run time:', error);
			return new Date(Date.now() + 24 * 60 * 60 * 1000); // Default to 24 hours from now
		}
	}
}

export { JobScheduler };
