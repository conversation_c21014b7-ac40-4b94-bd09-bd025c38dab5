/**
 * Worker Job Queue Tests
 *
 * Comprehensive tests for the job queue management system
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { WorkerJobQueue } from '../WorkerJobQueue';
import { JobConsumer } from '../JobConsumer';
import { JobScheduler } from '../JobScheduler';
import { JobProgressTracker } from '../JobProgressTracker';
import { JobStatisticsCollector } from '../JobStatisticsCollector';
import type { JobQueueConfig } from '../types';
import type { JobData } from '../../types/WorkerTypes';

// Mock Redis-SMQ
vi.mock('redis-smq', () => ({
	Producer: vi.fn().mockImplementation(() => ({
		run: vi.fn().mockResolvedValue(undefined),
		shutdown: vi.fn().mockResolvedValue(undefined),
		produce: vi.fn().mockImplementation((message, callback) =>
		{
			// Simulate successful message production
			setTimeout(() => callback(), 10);
		}),
	})),
	Consumer: vi.fn().mockImplementation(() => ({
		consume: vi.fn().mockImplementation((queueName, handler, options, callback) =>
		{
			// Simulate consumer setup
			if (callback) callback();
		}),
		run: vi.fn().mockResolvedValue(undefined),
		shutdown: vi.fn().mockResolvedValue(undefined),
	})),
	Message: vi.fn().mockImplementation(() => ({
		setBody: vi.fn(),
		setQueue: vi.fn(),
		setPriority: vi.fn(),
		setTTL: vi.fn(),
		setDelay: vi.fn(),
		getBody: vi.fn().mockReturnValue({}),
	})),
}));

describe('WorkerJobQueue', () =>
{
	let jobQueue: WorkerJobQueue;
	let config: JobQueueConfig;

	beforeEach(() =>
	{
		config = {
			redisUrl: 'redis://localhost:6379',
			redisDatabase: 0,
			defaultConcurrency: 5,
			defaultRetryAttempts: 3,
			defaultRetryDelay: 1000,
			defaultJobTtl: 3600000,
			batchSize: 10,
			batchDelayMs: 100,
			enableStatistics: true,
			statisticsInterval: 60000,
			enableProgressTracking: true,
			enableDeadLetterQueue: true,
			deadLetterQueueName: 'queue:dead-letter',
		};

		jobQueue = new WorkerJobQueue(config);
	});

	afterEach(async () =>
	{
		if (jobQueue)
		{
			await jobQueue.shutdown();
		}
	});

	describe('Initialization', () =>
	{
		it('should initialize successfully', async () =>
		{
			await expect(jobQueue.initialize()).resolves.not.toThrow();
		});

		it('should not initialize twice', async () =>
		{
			await jobQueue.initialize();
			await expect(jobQueue.initialize()).resolves.not.toThrow();
		});

		it('should shutdown successfully', async () =>
		{
			await jobQueue.initialize();
			await expect(jobQueue.shutdown()).resolves.not.toThrow();
		});
	});

	describe('Job Publishing', () =>
	{
		beforeEach(async () =>
		{
			await jobQueue.initialize();
		});

		it('should publish a job successfully', async () =>
		{
			const jobData = {
				domain: 'example.com',
				crawlType: 'full',
			};

			const jobId = await jobQueue.publishJob('queue:domain:crawl', jobData, {
				priority: 'medium',
				maxRetries: 3,
			});

			expect(jobId).toBeDefined();
			expect(typeof jobId).toBe('string');
		});

		it('should publish batch jobs successfully', async () =>
		{
			const batchRequest = {
				jobs: [
					{
						queueName: 'queue:domain:crawl',
						jobData: { domain: 'example1.com', crawlType: 'full' },
						options: { priority: 'medium' as const },
					},
					{
						queueName: 'queue:domain:crawl',
						jobData: { domain: 'example2.com', crawlType: 'quick' },
						options: { priority: 'high' as const },
					},
				],
			};

			const result = await jobQueue.publishBatchJobs(batchRequest);

			expect(result.totalJobs).toBe(2);
			expect(result.successfulJobs).toBe(2);
			expect(result.failedJobs).toBe(0);
			expect(result.jobIds).toHaveLength(2);
		});

		it('should handle job publishing errors', async () =>
		{
			// Mock producer to throw error
			const mockProducer = {
				produce: vi.fn().mockImplementation((message, callback) =>
				{
					callback(new Error('Redis connection failed'));
				}),
				shutdown: vi.fn().mockResolvedValue(undefined),
			};
			(jobQueue as any).producer = mockProducer;

			await expect(
				jobQueue.publishJob('queue:domain:crawl', { domain: 'example.com' }),
			).rejects.toThrow('Redis connection failed');
		});
	});

	describe('Consumer Management', () =>
	{
		beforeEach(async () =>
		{
			await jobQueue.initialize();
		});

		it('should create a consumer successfully', async () =>
		{
			const handler = vi.fn().mockResolvedValue(undefined);

			const consumer = await jobQueue.createConsumer('queue:test', handler, {
				concurrency: 2,
				retryThreshold: 3,
			});

			expect(consumer).toBeDefined();
		});

		it('should handle consumer creation errors', async () =>
		{
			const handler = vi.fn().mockResolvedValue(undefined);

			// Mock consumer to throw error during setup
			const { Consumer } = await import('redis-smq');
			vi.mocked(Consumer).mockImplementationOnce(() =>
			{
				throw new Error('Consumer setup failed');
			});

			await expect(
				jobQueue.createConsumer('queue:test', handler),
			).rejects.toThrow('Consumer setup failed');
		});
	});

	describe('Health Monitoring', () =>
	{
		beforeEach(async () =>
		{
			await jobQueue.initialize();
		});

		it('should return health status', async () =>
		{
			const health = await jobQueue.getHealth();

			expect(health).toHaveProperty('status');
			expect(health).toHaveProperty('producer');
			expect(health).toHaveProperty('consumers');
			expect(health).toHaveProperty('redis');
			expect(health).toHaveProperty('statistics');
		});

		it('should report unhealthy status when not initialized', async () =>
		{
			const uninitializedQueue = new WorkerJobQueue(config);
			const health = await uninitializedQueue.getHealth();

			expect(health.status).toBe('unhealthy');
			expect(health.producer.connected).toBe(false);
		});
	});

	describe('Statistics', () =>
	{
		beforeEach(async () =>
		{
			await jobQueue.initialize();
		});

		it('should track queue statistics', async () =>
		{
			// Publish a job first to initialize statistics
			await jobQueue.publishJob('queue:domain:crawl', { domain: 'example.com' });

			const stats = jobQueue.getQueueStatistics('queue:domain:crawl');
			expect(stats).toBeDefined();
		});

		it('should return all queue statistics', () =>
		{
			const allStats = jobQueue.getAllQueueStatistics();
			expect(typeof allStats).toBe('object');
		});
	});
});

describe('JobConsumer', () =>
{
	let jobQueue: WorkerJobQueue;
	let jobConsumer: JobConsumer;
	let config: JobQueueConfig;

	beforeEach(async () =>
	{
		config = {
			redisUrl: 'redis://localhost:6379',
			redisDatabase: 0,
			defaultConcurrency: 5,
			defaultRetryAttempts: 3,
			defaultRetryDelay: 1000,
			defaultJobTtl: 3600000,
			batchSize: 10,
			batchDelayMs: 100,
			enableStatistics: true,
			statisticsInterval: 60000,
			enableProgressTracking: true,
			enableDeadLetterQueue: true,
			deadLetterQueueName: 'queue:dead-letter',
		};

		jobQueue = new WorkerJobQueue(config);
		await jobQueue.initialize();

		jobConsumer = new JobConsumer(jobQueue, 10);
	});

	afterEach(async () =>
	{
		if (jobConsumer)
		{
			await jobConsumer.stopConsuming();
		}
		if (jobQueue)
		{
			await jobQueue.shutdown();
		}
	});

	describe('Configuration', () =>
	{
		it('should configure domain crawl queue', () =>
		{
			jobConsumer.configureDomainCrawlQueue(5);
			expect(jobConsumer.getActiveJobsCount()).toBe(0);
		});

		it('should configure ranking update queue', () =>
		{
			jobConsumer.configureRankingUpdateQueue(3);
			expect(jobConsumer.getActiveJobsCount()).toBe(0);
		});

		it('should configure maintenance queue', () =>
		{
			jobConsumer.configureMaintenanceQueue(1);
			expect(jobConsumer.getActiveJobsCount()).toBe(0);
		});
	});

	describe('Consumption Control', () =>
	{
		it('should start consuming successfully', async () =>
		{
			jobConsumer.configureDomainCrawlQueue();
			await expect(jobConsumer.startConsuming()).resolves.not.toThrow();
		});

		it('should stop consuming successfully', async () =>
		{
			jobConsumer.configureDomainCrawlQueue();
			await jobConsumer.startConsuming();
			await expect(jobConsumer.stopConsuming()).resolves.not.toThrow();
		});

		it('should pause and resume queues', async () =>
		{
			jobConsumer.configureDomainCrawlQueue();
			await jobConsumer.startConsuming();

			await expect(jobConsumer.pauseQueue('queue:domain:crawl')).resolves.not.toThrow();
			await expect(jobConsumer.resumeQueue('queue:domain:crawl')).resolves.not.toThrow();
		});
	});

	describe('Health Monitoring', () =>
	{
		it('should return health status', () =>
		{
			const health = jobConsumer.getHealth();

			expect(health).toHaveProperty('status');
			expect(health).toHaveProperty('isConsuming');
			expect(health).toHaveProperty('activeJobs');
			expect(health).toHaveProperty('maxConcurrentTasks');
			expect(health).toHaveProperty('uptime');
			expect(health).toHaveProperty('queues');
		});

		it('should return metrics', () =>
		{
			const metrics = jobConsumer.getMetrics();
			expect(typeof metrics).toBe('object');
		});
	});
});

describe('JobScheduler', () =>
{
	let jobQueue: WorkerJobQueue;
	let jobScheduler: JobScheduler;
	let config: JobQueueConfig;

	beforeEach(async () =>
	{
		config = {
			redisUrl: 'redis://localhost:6379',
			redisDatabase: 0,
			defaultConcurrency: 5,
			defaultRetryAttempts: 3,
			defaultRetryDelay: 1000,
			defaultJobTtl: 3600000,
			batchSize: 10,
			batchDelayMs: 100,
			enableStatistics: true,
			statisticsInterval: 60000,
			enableProgressTracking: true,
			enableDeadLetterQueue: true,
			deadLetterQueueName: 'queue:dead-letter',
		};

		jobQueue = new WorkerJobQueue(config);
		await jobQueue.initialize();

		jobScheduler = new JobScheduler(jobQueue);
		await jobScheduler.initialize();
	});

	afterEach(async () =>
	{
		if (jobScheduler)
		{
			await jobScheduler.shutdown();
		}
		if (jobQueue)
		{
			await jobQueue.shutdown();
		}
	});

	describe('Job Scheduling', () =>
	{
		it('should schedule domain crawl job', async () =>
		{
			const jobId = await jobScheduler.scheduleDomainCrawlJob(
				'example.com',
				'full',
				'medium',
			);

			expect(jobId).toBeDefined();
			expect(typeof jobId).toBe('string');
		});

		it('should schedule ranking update job', async () =>
		{
			const jobId = await jobScheduler.scheduleRankingUpdateJob(
				'example.com',
				'global',
				'medium',
			);

			expect(jobId).toBeDefined();
			expect(typeof jobId).toBe('string');
		});

		it('should schedule batch domain crawl jobs', async () =>
		{
			const domains = ['example1.com', 'example2.com', 'example3.com'];
			const result = await jobScheduler.scheduleBatchDomainCrawlJobs(
				domains,
				'full',
				'medium',
			);

			expect(result.totalJobs).toBe(3);
			expect(result.successfulJobs).toBe(3);
			expect(result.jobIds).toHaveLength(3);
		});
	});

	describe('Recurring Jobs', () =>
	{
		it('should schedule recurring job', async () =>
		{
			const jobId = await jobScheduler.scheduleRecurringJob({
				queueName: 'queue:maintenance',
				jobData: { taskType: 'cleanup' },
				cronExpression: '0 * * * *', // Every hour
			});

			expect(jobId).toBeDefined();
			expect(typeof jobId).toBe('string');
		});

		it('should schedule daily domain discovery', async () =>
		{
			const jobId = await jobScheduler.scheduleDailyDomainDiscovery();
			expect(jobId).toBeDefined();
		});

		it('should schedule weekly ranking recalculation', async () =>
		{
			const jobId = await jobScheduler.scheduleWeeklyRankingRecalculation();
			expect(jobId).toBeDefined();
		});

		it('should toggle scheduled job', async () =>
		{
			const jobId = await jobScheduler.scheduleRecurringJob({
				queueName: 'queue:maintenance',
				jobData: { taskType: 'test' },
				cronExpression: '0 * * * *',
			});

			await expect(jobScheduler.toggleScheduledJob(jobId, false)).resolves.not.toThrow();
			await expect(jobScheduler.toggleScheduledJob(jobId, true)).resolves.not.toThrow();
		});

		it('should remove scheduled job', async () =>
		{
			const jobId = await jobScheduler.scheduleRecurringJob({
				queueName: 'queue:maintenance',
				jobData: { taskType: 'test' },
				cronExpression: '0 * * * *',
			});

			await expect(jobScheduler.removeScheduledJob(jobId)).resolves.not.toThrow();
		});
	});

	describe('Statistics and Health', () =>
	{
		it('should return job statistics', () =>
		{
			const stats = jobScheduler.getJobStatistics('queue:domain:crawl');
			expect(stats).toBeDefined();
		});

		it('should return all job statistics', () =>
		{
			const allStats = jobScheduler.getAllJobStatistics();
			expect(Array.isArray(allStats)).toBe(true);
		});

		it('should return health status', () =>
		{
			const health = jobScheduler.getHealth();

			expect(health).toHaveProperty('status');
			expect(health).toHaveProperty('scheduledJobs');
			expect(health).toHaveProperty('activeJobs');
			expect(health).toHaveProperty('queues');
		});
	});
});

describe('JobProgressTracker', () =>
{
	let progressTracker: JobProgressTracker;

	beforeEach(async () =>
	{
		progressTracker = new JobProgressTracker(100);
		await progressTracker.initialize();
	});

	afterEach(async () =>
	{
		if (progressTracker)
		{
			await progressTracker.shutdown();
		}
	});

	describe('Progress Tracking', () =>
	{
		it('should start tracking a job', () =>
		{
			const jobData: JobData = {
				id: 'test-job-1',
				domain: 'example.com',
				type: 'queue:domain:crawl',
				priority: 'medium',
				timeout: 30000,
				createdAt: new Date(),
				status: 'pending',
				progress: 0,
				retryCount: 0,
				maxRetries: 3,
				metadata: {},
				data: {},
			};

			progressTracker.startTracking(jobData);

			const progress = progressTracker.getJobProgress(jobData.id);
			expect(progress).toBeDefined();
			expect(progress?.jobId).toBe(jobData.id);
		});

		it('should update job progress', () =>
		{
			const jobData: JobData = {
				id: 'test-job-2',
				domain: 'example.com',
				type: 'queue:domain:crawl',
				priority: 'medium',
				timeout: 30000,
				createdAt: new Date(),
				status: 'pending',
				progress: 0,
				retryCount: 0,
				maxRetries: 3,
				metadata: {},
				data: {},
			};

			progressTracker.startTracking(jobData);
			progressTracker.updateProgress(
				jobData.id,
				jobData.domain,
				'dns_lookup',
				25,
				'DNS lookup completed',
			);

			const currentProgress = progressTracker.getCurrentProgress(jobData.id);
			expect(currentProgress).toBe(25);
		});

		it('should complete job tracking', () =>
		{
			const jobData: JobData = {
				id: 'test-job-3',
				domain: 'example.com',
				type: 'queue:domain:crawl',
				priority: 'medium',
				timeout: 30000,
				createdAt: new Date(),
				status: 'pending',
				progress: 0,
				retryCount: 0,
				maxRetries: 3,
				metadata: {},
				data: {},
			};

			progressTracker.startTracking(jobData);
			progressTracker.completeTracking(jobData.id, jobData.domain, true);

			const progress = progressTracker.getJobProgress(jobData.id);
			expect(progress?.completedAt).toBeDefined();
			expect(progress?.currentProgress).toBe(100);
		});
	});

	describe('Statistics', () =>
	{
		it('should return progress statistics', () =>
		{
			const stats = progressTracker.getProgressStatistics();

			expect(stats).toHaveProperty('totalJobs');
			expect(stats).toHaveProperty('activeJobs');
			expect(stats).toHaveProperty('completedJobs');
			expect(stats).toHaveProperty('averageCompletionTime');
			expect(stats).toHaveProperty('progressDistribution');
		});

		it('should return health status', () =>
		{
			const health = progressTracker.getHealth();

			expect(health).toHaveProperty('status');
			expect(health).toHaveProperty('activeJobs');
			expect(health).toHaveProperty('stuckJobs');
			expect(health).toHaveProperty('averageProgress');
		});
	});
});

describe('JobStatisticsCollector', () =>
{
	let statisticsCollector: JobStatisticsCollector;

	beforeEach(async () =>
	{
		statisticsCollector = new JobStatisticsCollector(1000, 1000, 5000);
		await statisticsCollector.initialize();
	});

	afterEach(async () =>
	{
		if (statisticsCollector)
		{
			await statisticsCollector.shutdown();
		}
	});

	describe('Statistics Collection', () =>
	{
		it('should record job start', () =>
		{
			const jobData: JobData = {
				id: 'test-job-1',
				domain: 'example.com',
				type: 'queue:domain:crawl',
				priority: 'medium',
				timeout: 30000,
				createdAt: new Date(),
				status: 'pending',
				progress: 0,
				retryCount: 0,
				maxRetries: 3,
				metadata: {},
				data: {},
			};

			expect(() => statisticsCollector.recordJobStart(jobData)).not.toThrow();
		});

		it('should record job completion', () =>
		{
			const jobData: JobData = {
				id: 'test-job-2',
				domain: 'example.com',
				type: 'queue:domain:crawl',
				priority: 'medium',
				timeout: 30000,
				createdAt: new Date(),
				status: 'pending',
				progress: 0,
				retryCount: 0,
				maxRetries: 3,
				metadata: {},
				data: {},
			};

			statisticsCollector.recordJobStart(jobData);
			statisticsCollector.recordJobCompletion(jobData.id, true);

			const stats = statisticsCollector.getQueueStatistics('queue:domain:crawl');
			expect(stats?.completedJobs).toBe(1);
		});

		it('should record job retry', () =>
		{
			const jobData: JobData = {
				id: 'test-job-3',
				domain: 'example.com',
				type: 'queue:domain:crawl',
				priority: 'medium',
				timeout: 30000,
				createdAt: new Date(),
				status: 'pending',
				progress: 0,
				retryCount: 0,
				maxRetries: 3,
				metadata: {},
				data: {},
			};

			statisticsCollector.recordJobStart(jobData);
			statisticsCollector.recordJobRetry(jobData.id, 1, 'Connection timeout');

			expect(() => statisticsCollector.recordJobRetry(jobData.id, 1, 'Connection timeout')).not.toThrow();
		});
	});

	describe('Reports and Analytics', () =>
	{
		it('should generate statistics report', () =>
		{
			const report = statisticsCollector.generateReport('hour');

			expect(report).toHaveProperty('timeframe');
			expect(report).toHaveProperty('startTime');
			expect(report).toHaveProperty('endTime');
			expect(report).toHaveProperty('queues');
			expect(report).toHaveProperty('overall');
		});

		it('should return performance metrics', () =>
		{
			const metrics = statisticsCollector.getPerformanceMetrics();

			expect(metrics).toHaveProperty('totalJobsProcessed');
			expect(metrics).toHaveProperty('averageProcessingTime');
			expect(metrics).toHaveProperty('throughputPerMinute');
			expect(metrics).toHaveProperty('errorRate');
		});

		it('should return health status', () =>
		{
			const health = statisticsCollector.getHealth();

			expect(health).toHaveProperty('status');
			expect(health).toHaveProperty('totalQueues');
			expect(health).toHaveProperty('activeQueues');
			expect(health).toHaveProperty('errorRate');
		});
	});
});
