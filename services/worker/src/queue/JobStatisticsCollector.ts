/**
 * Job Statistics Collector
 *
 * Collects, aggregates, and provides comprehensive job statistics
 * and performance metrics for monitoring and analytics.
 */

import { EventEmitter } from 'events';
import { logger as loggerFactory } from '@shared/utils/Logger';
import type {
	JobQueueStatistics,
	JobStatisticsReport,
	QueueStatisticsReport,
} from './types';
import type { JobData } from '../types/WorkerTypes';

const logger = loggerFactory.getLogger('job-statistics-collector');

interface JobMetric
{
	jobId: string;
	queueName: string;
	domain?: string;
	startTime: Date;
	endTime?: Date;
	duration?: number;
	success: boolean;
	error?: string;
	retryCount: number;
	memoryUsage?: number;
	cpuUsage?: number;
}

interface QueueMetrics
{
	queueName: string;
	totalJobs: number;
	successfulJobs: number;
	failedJobs: number;
	retryJobs: number;
	totalProcessingTime: number;
	averageProcessingTime: number;
	minProcessingTime: number;
	maxProcessingTime: number;
	throughputPerMinute: number;
	errorRate: number;
	lastUpdated: Date;
	hourlyStats: Array<{
		hour: Date;
		jobCount: number;
		successCount: number;
		failureCount: number;
		averageTime: number;
	}>;
	errorBreakdown: Map<string, number>;
}

/**
 * Job Statistics Collector
 *
 * Provides comprehensive statistics collection and analysis
 * for job queue performance monitoring.
 */
class JobStatisticsCollector extends EventEmitter
{
	private queueMetrics = new Map<string, QueueMetrics>();
	private jobMetrics = new Map<string, JobMetric>();
	private isInitialized = false;
	private statisticsInterval: NodeJS.Timeout | null = null;
	private cleanupInterval: NodeJS.Timeout | null = null;

	// Configuration
	private readonly maxJobMetrics: number;
	private readonly statisticsIntervalMs: number;
	private readonly cleanupIntervalMs: number;

	constructor(
		maxJobMetrics: number = 10000,
		statisticsIntervalMs: number = 60000, // 1 minute
		cleanupIntervalMs: number = 3600000, // 1 hour
	)
	{
		super();
		this.maxJobMetrics = maxJobMetrics;
		this.statisticsIntervalMs = statisticsIntervalMs;
		this.cleanupIntervalMs = cleanupIntervalMs;
	}

	/**
	 * Initialize the statistics collector
	 */
	async initialize(): Promise<void>
	{
		if (this.isInitialized)
		{
			logger.warn('Job statistics collector already initialized');
			return;
		}

		try
		{
			logger.info('Initializing Job Statistics Collector...');

			// Start statistics calculation interval
			this.statisticsInterval = setInterval(() =>
			{
				this.calculateStatistics();
			}, this.statisticsIntervalMs);

			// Start cleanup interval
			this.cleanupInterval = setInterval(() =>
			{
				this.cleanupOldMetrics();
			}, this.cleanupIntervalMs);

			this.isInitialized = true;
			logger.info('Job Statistics Collector initialized successfully');
		}
		catch (error)
		{
			logger.error('Failed to initialize Job Statistics Collector:', error);
			throw error;
		}
	}

	/**
	 * Shutdown the statistics collector
	 */
	async shutdown(): Promise<void>
	{
		try
		{
			logger.info('Shutting down Job Statistics Collector...');

			// Stop intervals
			if (this.statisticsInterval)
			{
				clearInterval(this.statisticsInterval);
				this.statisticsInterval = null;
			}

			if (this.cleanupInterval)
			{
				clearInterval(this.cleanupInterval);
				this.cleanupInterval = null;
			}

			// Clear collections
			this.queueMetrics.clear();
			this.jobMetrics.clear();

			this.isInitialized = false;
			logger.info('Job Statistics Collector shut down successfully');
		}
		catch (error)
		{
			logger.error('Error shutting down Job Statistics Collector:', error);
			throw error;
		}
	}

	/**
	 * Record job start
	 */
	recordJobStart(jobData: JobData): void
	{
		const jobMetric: JobMetric = {
			jobId: jobData.id,
			queueName: jobData.type,
			domain: jobData.domain,
			startTime: new Date(),
			success: false,
			retryCount: jobData.retryCount || 0,
		};

		this.jobMetrics.set(jobData.id, jobMetric);

		// Initialize queue metrics if not exists
		if (!this.queueMetrics.has(jobData.type))
		{
			this.initializeQueueMetrics(jobData.type);
		}

		logger.debug('Job start recorded:', {
			jobId: jobData.id,
			queueName: jobData.type,
			domain: jobData.domain,
		});
	}

	/**
	 * Record job completion
	 */
	recordJobCompletion(
		jobId: string,
		success: boolean,
		error?: string,
		resourceUsage?: {
			memoryUsage?: number;
			cpuUsage?: number;
		},
	): void
	{
		const jobMetric = this.jobMetrics.get(jobId);
		if (!jobMetric)
		{
			logger.warn(`Job metric not found for completion: ${jobId}`);
			return;
		}

		const endTime = new Date();
		const duration = endTime.getTime() - jobMetric.startTime.getTime();

		// Update job metric
		jobMetric.endTime = endTime;
		jobMetric.duration = duration;
		jobMetric.success = success;
		jobMetric.error = error;
		jobMetric.memoryUsage = resourceUsage?.memoryUsage;
		jobMetric.cpuUsage = resourceUsage?.cpuUsage;

		// Update queue metrics
		this.updateQueueMetrics(jobMetric);

		// Emit completion event
		this.emit('jobCompleted', {
			jobId,
			queueName: jobMetric.queueName,
			domain: jobMetric.domain,
			success,
			duration,
			error,
		});

		logger.debug('Job completion recorded:', {
			jobId,
			queueName: jobMetric.queueName,
			success,
			duration,
		});
	}

	/**
	 * Record job retry
	 */
	recordJobRetry(jobId: string, retryCount: number, error: string): void
	{
		const jobMetric = this.jobMetrics.get(jobId);
		if (jobMetric)
		{
			jobMetric.retryCount = retryCount;
			jobMetric.error = error;
		}

		// Update queue retry statistics
		const queueMetrics = this.queueMetrics.get(jobMetric?.queueName || '');
		if (queueMetrics)
		{
			queueMetrics.retryJobs++;
		}

		logger.debug('Job retry recorded:', {
			jobId,
			retryCount,
			error,
		});
	}

	/**
	 * Get statistics for a specific queue
	 */
	getQueueStatistics(queueName: string): JobQueueStatistics | undefined
	{
		const metrics = this.queueMetrics.get(queueName);
		if (!metrics)
		{
			return undefined;
		}

		return {
			totalJobs: metrics.totalJobs,
			pendingJobs: 0, // This would need to be tracked separately
			processingJobs: 0, // This would need to be tracked separately
			completedJobs: metrics.successfulJobs,
			failedJobs: metrics.failedJobs,
			retryingJobs: metrics.retryJobs,
			deadLetterJobs: 0, // This would need to be tracked separately
			averageProcessingTime: metrics.averageProcessingTime,
			throughputPerMinute: metrics.throughputPerMinute,
			errorRate: metrics.errorRate,
			lastUpdated: metrics.lastUpdated,
		};
	}

	/**
	 * Get statistics for all queues
	 */
	getAllQueueStatistics(): Record<string, JobQueueStatistics>
	{
		const result: Record<string, JobQueueStatistics> = {};

		for (const queueName of this.queueMetrics.keys())
		{
			const stats = this.getQueueStatistics(queueName);
			if (stats)
			{
				result[queueName] = stats;
			}
		}

		return result;
	}

	/**
	 * Generate comprehensive statistics report
	 */
	generateReport(
		timeframe: 'hour' | 'day' | 'week' | 'month',
		startTime?: Date,
		endTime?: Date,
	): JobStatisticsReport
	{
		const now = new Date();
		const reportEndTime = endTime || now;
		let reportStartTime = startTime;

		// Calculate start time based on timeframe if not provided
		if (!reportStartTime)
		{
			switch (timeframe)
			{
				case 'hour':
					reportStartTime = new Date(now.getTime() - 60 * 60 * 1000);
					break;
				case 'day':
					reportStartTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
					break;
				case 'week':
					reportStartTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
					break;
				case 'month':
					reportStartTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
					break;
			}
		}

		// Generate queue reports
		const queueReports: Record<string, QueueStatisticsReport> = {};
		const overallStats: JobQueueStatistics = {
			totalJobs: 0,
			pendingJobs: 0,
			processingJobs: 0,
			completedJobs: 0,
			failedJobs: 0,
			retryingJobs: 0,
			deadLetterJobs: 0,
			averageProcessingTime: 0,
			throughputPerMinute: 0,
			errorRate: 0,
			lastUpdated: now,
		};

		for (const [queueName, metrics] of this.queueMetrics)
		{
			const queueStats = this.getQueueStatistics(queueName);
			if (queueStats)
			{
				// Generate trends and error analysis
				const trends = this.generateTrends(queueName, reportStartTime, reportEndTime);
				const topErrors = this.getTopErrors(queueName, reportStartTime, reportEndTime);

				queueReports[queueName] = {
					queueName,
					statistics: queueStats,
					trends,
					topErrors,
				};

				// Aggregate overall statistics
				overallStats.totalJobs += queueStats.totalJobs;
				overallStats.completedJobs += queueStats.completedJobs;
				overallStats.failedJobs += queueStats.failedJobs;
				overallStats.retryingJobs += queueStats.retryingJobs;
			}
		}

		// Calculate overall averages
		if (overallStats.totalJobs > 0)
		{
			overallStats.errorRate = overallStats.failedJobs / overallStats.totalJobs;

			// Calculate weighted average processing time
			let totalProcessingTime = 0;
			let totalCompletedJobs = 0;
			for (const queueReport of Object.values(queueReports))
			{
				totalProcessingTime += queueReport.statistics.averageProcessingTime * queueReport.statistics.completedJobs;
				totalCompletedJobs += queueReport.statistics.completedJobs;
			}
			if (totalCompletedJobs > 0)
			{
				overallStats.averageProcessingTime = totalProcessingTime / totalCompletedJobs;
			}
		}

		return {
			timeframe,
			startTime: reportStartTime,
			endTime: reportEndTime,
			queues: queueReports,
			overall: overallStats,
		};
	}

	/**
	 * Get performance metrics
	 */
	getPerformanceMetrics(): {
		totalJobsProcessed: number;
		averageProcessingTime: number;
		throughputPerMinute: number;
		errorRate: number;
		topPerformingQueues: Array<{ queueName: string; throughput: number }>;
		slowestQueues: Array<{ queueName: string; averageTime: number }>;
		memoryUsage: number;
	}
	{
		let totalJobs = 0;
		let totalProcessingTime = 0;
		let totalCompletedJobs = 0;
		let totalFailedJobs = 0;
		const queuePerformance: Array<{ queueName: string; throughput: number; averageTime: number }> = [];

		for (const [queueName, metrics] of this.queueMetrics)
		{
			totalJobs += metrics.totalJobs;
			totalCompletedJobs += metrics.successfulJobs;
			totalFailedJobs += metrics.failedJobs;
			totalProcessingTime += metrics.totalProcessingTime;

			queuePerformance.push({
				queueName,
				throughput: metrics.throughputPerMinute,
				averageTime: metrics.averageProcessingTime,
			});
		}

		const averageProcessingTime = totalCompletedJobs > 0 ? totalProcessingTime / totalCompletedJobs : 0;
		const errorRate = totalJobs > 0 ? totalFailedJobs / totalJobs : 0;

		// Calculate overall throughput (jobs per minute)
		const throughputPerMinute = totalCompletedJobs; // This would need time-based calculation

		// Sort queues by performance
		const topPerformingQueues = queuePerformance
			.sort((a, b) => b.throughput - a.throughput)
			.slice(0, 5)
			.map(q => ({ queueName: q.queueName, throughput: q.throughput }));

		const slowestQueues = queuePerformance
			.sort((a, b) => b.averageTime - a.averageTime)
			.slice(0, 5)
			.map(q => ({ queueName: q.queueName, averageTime: q.averageTime }));

		// Estimate memory usage
		const memoryUsage = this.jobMetrics.size * 1024 + this.queueMetrics.size * 512; // Rough estimate

		return {
			totalJobsProcessed: totalJobs,
			averageProcessingTime,
			throughputPerMinute,
			errorRate,
			topPerformingQueues,
			slowestQueues,
			memoryUsage,
		};
	}

	/**
	 * Get health status
	 */
	getHealth(): {
		status: 'healthy' | 'degraded' | 'unhealthy';
		totalQueues: number;
		activeQueues: number;
		errorRate: number;
		averageProcessingTime: number;
		memoryUsage: number;
	}
	{
		const performance = this.getPerformanceMetrics();
		const totalQueues = this.queueMetrics.size;
		const activeQueues = Array.from(this.queueMetrics.values())
			.filter(metrics => metrics.lastUpdated.getTime() > Date.now() - 5 * 60 * 1000).length; // Active in last 5 minutes

		// Determine health status
		let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
		if (!this.isInitialized)
		{
			status = 'unhealthy';
		}
		else if (performance.errorRate > 0.2) // More than 20% error rate
		{
			status = 'unhealthy';
		}
		else if (performance.errorRate > 0.1 || performance.averageProcessingTime > 60000) // More than 10% error rate or > 1 minute average
		{
			status = 'degraded';
		}

		return {
			status,
			totalQueues,
			activeQueues,
			errorRate: performance.errorRate,
			averageProcessingTime: performance.averageProcessingTime,
			memoryUsage: performance.memoryUsage,
		};
	}

	// ============================================================================
	// Private Helper Methods
	// ============================================================================

	/**
	 * Initialize metrics for a queue
	 */
	private initializeQueueMetrics(queueName: string): void
	{
		this.queueMetrics.set(queueName, {
			queueName,
			totalJobs: 0,
			successfulJobs: 0,
			failedJobs: 0,
			retryJobs: 0,
			totalProcessingTime: 0,
			averageProcessingTime: 0,
			minProcessingTime: Infinity,
			maxProcessingTime: 0,
			throughputPerMinute: 0,
			errorRate: 0,
			lastUpdated: new Date(),
			hourlyStats: [],
			errorBreakdown: new Map(),
		});
	}

	/**
	 * Update queue metrics with job completion data
	 */
	private updateQueueMetrics(jobMetric: JobMetric): void
	{
		let metrics = this.queueMetrics.get(jobMetric.queueName);
		if (!metrics)
		{
			this.initializeQueueMetrics(jobMetric.queueName);
			metrics = this.queueMetrics.get(jobMetric.queueName)!;
		}

		metrics.totalJobs++;
		metrics.lastUpdated = new Date();

		if (jobMetric.success)
		{
			metrics.successfulJobs++;
		}
		else
		{
			metrics.failedJobs++;

			// Track error breakdown
			if (jobMetric.error)
			{
				const errorCount = metrics.errorBreakdown.get(jobMetric.error) || 0;
				metrics.errorBreakdown.set(jobMetric.error, errorCount + 1);
			}
		}

		// Update processing time statistics
		if (jobMetric.duration)
		{
			metrics.totalProcessingTime += jobMetric.duration;
			metrics.averageProcessingTime = metrics.totalProcessingTime / (metrics.successfulJobs + metrics.failedJobs);
			metrics.minProcessingTime = Math.min(metrics.minProcessingTime, jobMetric.duration);
			metrics.maxProcessingTime = Math.max(metrics.maxProcessingTime, jobMetric.duration);
		}

		// Update error rate
		if (metrics.totalJobs > 0)
		{
			metrics.errorRate = metrics.failedJobs / metrics.totalJobs;
		}
	}

	/**
	 * Calculate statistics for all queues
	 */
	private calculateStatistics(): void
	{
		const now = new Date();
		const oneMinuteAgo = new Date(now.getTime() - 60 * 1000);

		for (const metrics of this.queueMetrics.values())
		{
			// Calculate throughput per minute
			const recentJobs = Array.from(this.jobMetrics.values())
				.filter(job =>
					job.queueName === metrics.queueName &&
					job.endTime &&
					job.endTime > oneMinuteAgo);

			metrics.throughputPerMinute = recentJobs.length;

			// Update hourly statistics
			this.updateHourlyStats(metrics, now);
		}

		// Emit statistics update event
		this.emit('statisticsUpdated', {
			timestamp: now,
			queues: Array.from(this.queueMetrics.keys()),
		});
	}

	/**
	 * Update hourly statistics for a queue
	 */
	private updateHourlyStats(metrics: QueueMetrics, currentTime: Date): void
	{
		const currentHour = new Date(currentTime);
		currentHour.setMinutes(0, 0, 0); // Round to hour

		// Find or create hourly stat entry
		let hourlyEntry = metrics.hourlyStats.find(stat =>
			stat.hour.getTime() === currentHour.getTime());

		if (!hourlyEntry)
		{
			hourlyEntry = {
				hour: currentHour,
				jobCount: 0,
				successCount: 0,
				failureCount: 0,
				averageTime: 0,
			};
			metrics.hourlyStats.push(hourlyEntry);

			// Keep only last 24 hours
			if (metrics.hourlyStats.length > 24)
			{
				metrics.hourlyStats = metrics.hourlyStats.slice(-24);
			}
		}

		// Update hourly stats based on recent jobs
		const hourStart = currentHour;
		const hourEnd = new Date(currentHour.getTime() + 60 * 60 * 1000);

		const hourlyJobs = Array.from(this.jobMetrics.values())
			.filter(job =>
				job.queueName === metrics.queueName &&
				job.endTime &&
				job.endTime >= hourStart &&
				job.endTime < hourEnd);

		hourlyEntry.jobCount = hourlyJobs.length;
		hourlyEntry.successCount = hourlyJobs.filter(job => job.success).length;
		hourlyEntry.failureCount = hourlyJobs.filter(job => !job.success).length;

		if (hourlyJobs.length > 0)
		{
			const totalTime = hourlyJobs
				.filter(job => job.duration)
				.reduce((sum, job) => sum + (job.duration || 0), 0);
			hourlyEntry.averageTime = totalTime / hourlyJobs.length;
		}
	}

	/**
	 * Generate trends for a queue
	 */
	private generateTrends(
		queueName: string,
		startTime: Date,
		endTime: Date,
	): {
		jobsPerHour: number[];
		averageProcessingTime: number[];
		errorRate: number[];
	}
	{
		const metrics = this.queueMetrics.get(queueName);
		if (!metrics)
		{
			return {
				jobsPerHour: [],
				averageProcessingTime: [],
				errorRate: [],
			};
		}

		// Filter hourly stats within time range
		const relevantStats = metrics.hourlyStats.filter(stat =>
			stat.hour >= startTime && stat.hour <= endTime);

		return {
			jobsPerHour: relevantStats.map(stat => stat.jobCount),
			averageProcessingTime: relevantStats.map(stat => stat.averageTime),
			errorRate: relevantStats.map(stat =>
				stat.jobCount > 0 ? stat.failureCount / stat.jobCount : 0),
		};
	}

	/**
	 * Get top errors for a queue
	 */
	private getTopErrors(
		queueName: string,
		startTime: Date,
		endTime: Date,
	): Array<{ error: string; count: number; percentage: number }>
	{
		const metrics = this.queueMetrics.get(queueName);
		if (!metrics)
		{
			return [];
		}

		// Get errors within time range
		const relevantJobs = Array.from(this.jobMetrics.values())
			.filter(job =>
				job.queueName === queueName &&
				job.endTime &&
				job.endTime >= startTime &&
				job.endTime <= endTime &&
				!job.success &&
				job.error);

		const errorCounts = new Map<string, number>();
		for (const job of relevantJobs)
		{
			if (job.error)
			{
				const count = errorCounts.get(job.error) || 0;
				errorCounts.set(job.error, count + 1);
			}
		}

		const totalErrors = relevantJobs.length;
		return Array.from(errorCounts.entries())
			.map(([error, count]) => ({
				error,
				count,
				percentage: totalErrors > 0 ? (count / totalErrors) * 100 : 0,
			}))
			.sort((a, b) => b.count - a.count)
			.slice(0, 10); // Top 10 errors
	}

	/**
	 * Clean up old job metrics
	 */
	private cleanupOldMetrics(): void
	{
		const cutoffTime = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago
		let cleanedCount = 0;

		for (const [jobId, jobMetric] of this.jobMetrics)
		{
			if (jobMetric.endTime && jobMetric.endTime < cutoffTime)
			{
				this.jobMetrics.delete(jobId);
				cleanedCount++;
			}
		}

		// If still over limit, remove oldest completed jobs
		if (this.jobMetrics.size > this.maxJobMetrics)
		{
			const completedJobs = Array.from(this.jobMetrics.entries())
				.filter(([, metric]) => metric.endTime)
				.sort(([, a], [, b]) => a.endTime!.getTime() - b.endTime!.getTime());

			const toRemove = this.jobMetrics.size - this.maxJobMetrics;
			for (let i = 0; i < Math.min(toRemove, completedJobs.length); i++)
			{
				this.jobMetrics.delete(completedJobs[i][0]);
				cleanedCount++;
			}
		}

		if (cleanedCount > 0)
		{
			logger.debug(`Cleaned up ${cleanedCount} old job metrics`);
		}
	}
}

export { JobStatisticsCollector };
