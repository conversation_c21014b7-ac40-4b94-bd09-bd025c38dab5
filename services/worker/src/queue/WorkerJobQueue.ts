/**
 * Worker Job Queue
 *
 * Extracted and enhanced job queue functionality from shared module
 * with worker-specific optimizations and features.
 */

// import { Producer, Consumer, Message } from 'redis-smq';
import { v4 as uuidv4 } from 'uuid';
import { logger as loggerFactory, AsyncUtils } from '@shared';
import type {
	JobQueueConfigType,
	JobQueueHealthType,
	ConsumerHealthType,
	JobQueueStatisticsType,
	RetryPolicyType,
	JobFailureClassificationType,
	DeadLetterJobInfoType,
	BatchJobRequestType,
	BatchJobResultType,
	JobHandlerType,
} from './types';
import type {
	JobPriorityType,
	QueueStatsType,
} from '../types/WorkerTypes';

const logger = loggerFactory.getLogger('worker-job-queue');

/**
 * Enhanced Job Queue for Worker Service
 *
 * Provides comprehensive job queue management with:
 * - Redis-SMQ integration
 * - Exponential backoff retry logic
 * - Dead letter queue handling
 * - Batch job processing
 * - Comprehensive statistics and monitoring
 */
class WorkerJobQueue
{
	private config: JobQueueConfigType;
	private producer: any | null = null; // Producer | null = null;
	private consumers = new Map<string, any>(); // Map<string, Consumer>();
	private jobHandlers = new Map<string, JobHandlerType>();
	private queueConfigs = new Map<string, { concurrency: number; retryThreshold: number; enabled: boolean }>();
	private isInitialized = false;
	private statistics = new Map<string, JobQueueStatisticsType>();
	private retryPolicies = new Map<string, RetryPolicyType>();

	// Job tracking
	private activeJobs = new Map<string, JobDataType>();
	private jobStartTimes = new Map<string, Date>();
	private deadLetterJobs = new Map<string, DeadLetterJobInfoType>();

	// Statistics tracking
	private statisticsInterval: NodeJS.Timeout | null = null;
	private lastStatisticsUpdate = new Date();

	constructor(config: JobQueueConfigType)
	{
		this.config = config;
		this.setupDefaultRetryPolicies();
	}

	/**
	 * Initialize the job queue system
	 */
	async initialize(): Promise<void>
	{
		if (this.isInitialized)
		{
			logger.warn('Job queue already initialized');
			return;
		}

		try
		{
			logger.info('Initializing Worker Job Queue...');

			// Initialize Redis-SMQ producer
			this.producer = new Producer();
			await this.producer.run();

			// Start statistics collection if enabled
			if (this.config.enableStatistics)
			{
				this.startStatisticsCollection();
			}

			this.isInitialized = true;
			logger.info('Worker Job Queue initialized successfully');
		}
		catch (error)
		{
			logger.error('Failed to initialize Worker Job Queue:', error);
			throw error;
		}
	}

	/**
	 * Shutdown the job queue system
	 */
	async shutdown(): Promise<void>
	{
		try
		{
			logger.info('Shutting down Worker Job Queue...');

			// Stop statistics collection
			if (this.statisticsInterval)
			{
				clearInterval(this.statisticsInterval);
				this.statisticsInterval = null;
			}

			// Shutdown all consumers
			const shutdownPromises = Array.from(this.consumers.entries())
				.map(([queueName, consumer]) =>
					consumer.shutdown().then(() =>
					{
						logger.info(`Consumer for queue ${queueName} shut down`);
					}));
			await Promise.all(shutdownPromises);

			// Shutdown producer
			if (this.producer)
			{
				await this.producer.shutdown();
				this.producer = null;
			}

			// Clear collections
			this.consumers.clear();
			this.jobHandlers.clear();
			this.activeJobs.clear();
			this.jobStartTimes.clear();

			this.isInitialized = false;
			logger.info('Worker Job Queue shut down successfully');
		}
		catch (error)
		{
			logger.error('Error shutting down Worker Job Queue:', error);
			throw error;
		}
	}

	/**
	 * Publish a job to the specified queue
	 */
	async publishJob(queueName: string, jobData: unknown, options: JobOptions = {}): Promise<string>
	{
		if (!this.isInitialized || !this.producer)
		{
			throw new Error('Job queue not initialized');
		}

		try
		{
			const job: JobData = {
				id: uuidv4(),
				domain: (jobData as any)?.domain || '',
				type: queueName,
				priority: options.priority || 'medium',
				timeout: options.timeout || this.config.defaultJobTtl,
				createdAt: new Date(),
				status: 'pending',
				progress: 0,
				retryCount: 0,
				maxRetries: options.maxRetries || this.config.defaultRetryAttempts,
				metadata: {},
				data: jobData,
			};

			// Create Redis-SMQ message
			const message = new Message();
			message.setBody(job);
			message.setQueue(queueName);

			// Set priority (Redis-SMQ uses 1=high, 2=medium, 3=low)
			const priorityNumber = this.getPriorityNumber(job.priority);
			message.setPriority(priorityNumber);

			// Set TTL if specified
			if (options.ttl)
			{
				message.setTTL(options.ttl);
			}

			// Set delay if specified
			if (options.delay)
			{
				message.setDelay(options.delay);
			}

			// Publish the message
			await new Promise<void>((resolve, reject) =>
			{
				this.producer!.produce(message, (error?: Error | null) =>
				{
					if (error)
					{
						reject(error);
					}
					else
					{
						resolve();
					}
				});
			});

			// Update statistics
			this.updateStatistics(queueName, 'scheduled');

			logger.info(`Job published to queue ${queueName}:`, {
				jobId: job.id,
				domain: job.domain,
				priority: job.priority,
			});

			return job.id;
		}
		catch (error)
		{
			logger.error(`Failed to publish job to queue ${queueName}:`, error);
			throw error;
		}
	}

	/**
	 * Create a consumer for the specified queue
	 */
	async createConsumer(
		queueName: string,
		handler: JobHandlerType,
		options: { concurrency?: number; retryThreshold?: number } = {},
	): Promise<Consumer>
	{
		if (!this.isInitialized)
		{
			throw new Error('Job queue not initialized');
		}

		try
		{
			const concurrency = options.concurrency || this.config.defaultConcurrency;
			const retryThreshold = options.retryThreshold || this.config.defaultRetryAttempts;

			// Store queue configuration
			this.queueConfigs.set(queueName, {
				concurrency,
				retryThreshold,
				enabled: true,
			});

			// Store job handler
			this.jobHandlers.set(queueName, handler);

			// Initialize statistics for this queue
			this.initializeQueueStatistics(queueName);

			// Create consumer
			const consumer = new Consumer();

			consumer.consume(queueName, async (message: Message, cb: (error?: Error) => void) =>
			{
				const jobData = message.getBody() as JobData;
				const startTime = new Date();

				// Track active job
				this.activeJobs.set(jobData.id, jobData);
				this.jobStartTimes.set(jobData.id, startTime);

				// Update job status
				jobData.status = 'processing';
				jobData.startedAt = startTime;

				// Update statistics
				this.updateStatistics(queueName, 'processing');

				try
				{
					logger.info(`Processing job from queue ${queueName}:`, {
						jobId: jobData.id,
						domain: jobData.domain,
					});

					// Call the handler
					await handler(jobData);

					// Mark job as completed
					jobData.status = 'completed';
					jobData.completedAt = new Date();
					jobData.duration = jobData.completedAt.getTime() - startTime.getTime();

					// Update statistics
					this.updateStatistics(queueName, 'completed', jobData.duration);

					// Clean up tracking
					this.activeJobs.delete(jobData.id);
					this.jobStartTimes.delete(jobData.id);

					// Acknowledge successful processing
					cb();

					logger.info('Job completed successfully:', {
						jobId: jobData.id,
						domain: jobData.domain,
						duration: jobData.duration,
					});
				}
				catch (error)
				{
					logger.error('Job processing failed:', {
						jobId: jobData.id,
						domain: jobData.domain,
						error: (error as Error).message,
					});

					// Handle job failure with retry logic
					await this.handleJobFailure(queueName, jobData, error as Error, cb);
				}
			}, {
				concurrency,
				messageRetryThreshold: retryThreshold,
			});

			await consumer.run();
			this.consumers.set(queueName, consumer);

			logger.info(`Consumer created for queue ${queueName}`, {
				concurrency,
				retryThreshold,
			});

			return consumer;
		}
		catch (error)
		{
			logger.error(`Failed to create consumer for queue ${queueName}:`, error);
			throw error;
		}
	}

	/**
	 * Handle job failure with exponential backoff retry
	 */
	private async handleJobFailure(
		queueName: string,
		jobData: JobData,
		error: Error,
		callback: (error?: Error) => void,
	): Promise<void>
	{
		// Classify the failure
		const classification = this.classifyJobFailure(error);

		// Update job data
		jobData.errorMessage = error.message;
		jobData.retryCount = (jobData.retryCount || 0) + 1;
		jobData.status = 'failed';

		// Clean up tracking
		this.activeJobs.delete(jobData.id);
		this.jobStartTimes.delete(jobData.id);

		// Check if we should retry
		if (classification.retryable && jobData.retryCount <= jobData.maxRetries)
		{
			// Get retry policy for this queue
			const retryPolicy = this.retryPolicies.get(queueName) || this.getDefaultRetryPolicy();

			// Calculate retry delay with exponential backoff
			const retryDelay = this.calculateRetryDelay(
				jobData.retryCount,
				retryPolicy,
				classification.retryDelay,
			);

			logger.info(`Retrying job (attempt ${jobData.retryCount}/${jobData.maxRetries})`, {
				jobId: jobData.id,
				domain: jobData.domain,
				retryDelay,
				error: error.message,
			});

			// Update statistics
			this.updateStatistics(queueName, 'retry');

			// Schedule retry
			setTimeout(async () =>
			{
				try
				{
					await this.publishJob(queueName, jobData.data, {
						priority: jobData.priority,
						maxRetries: jobData.maxRetries - jobData.retryCount,
					});
				}
				catch (retryError)
				{
					logger.error(`Failed to schedule retry for job ${jobData.id}:`, retryError);
				}
			}, retryDelay);

			// Acknowledge to remove from current queue
			callback();
		}
		else
		{
			// Max retries exceeded or permanent failure
			logger.error('Job failed permanently:', {
				jobId: jobData.id,
				domain: jobData.domain,
				retryCount: jobData.retryCount,
				maxRetries: jobData.maxRetries,
				error: error.message,
				classification: classification.type,
			});

			// Update statistics
			this.updateStatistics(queueName, 'failed');

			// Send to dead letter queue if enabled
			if (this.config.enableDeadLetterQueue)
			{
				await this.sendToDeadLetterQueue(queueName, jobData, error);
			}

			// Acknowledge with error to trigger dead letter queue
			callback(error);
		}
	}

	/**
	 * Classify job failure to determine retry strategy
	 */
	private classifyJobFailure(error: Error): JobFailureClassification
	{
		const errorMessage = error.message.toLowerCase();

		// Timeout errors - usually retryable
		if (errorMessage.includes('timeout') || errorMessage.includes('timed out'))
		{
			return {
				type: 'timeout',
				retryable: true,
				retryDelay: 5000, // 5 seconds
				escalate: false,
			};
		}

		// Resource errors - retryable with longer delay
		if (errorMessage.includes('resource') || errorMessage.includes('limit') || errorMessage.includes('capacity'))
		{
			return {
				type: 'resource',
				retryable: true,
				retryDelay: 30000, // 30 seconds
				escalate: false,
			};
		}

		// Validation errors - not retryable
		if (errorMessage.includes('validation') || errorMessage.includes('invalid') || errorMessage.includes('malformed'))
		{
			return {
				type: 'validation',
				retryable: false,
				escalate: true,
			};
		}

		// Network/connection errors - retryable
		if (errorMessage.includes('connection') || errorMessage.includes('network') || errorMessage.includes('econnrefused'))
		{
			return {
				type: 'transient',
				retryable: true,
				retryDelay: 10000, // 10 seconds
				escalate: false,
			};
		}

		// Default to transient error
		return {
			type: 'transient',
			retryable: true,
			escalate: false,
		};
	}

	/**
	 * Calculate retry delay with exponential backoff
	 */
	private calculateRetryDelay(
		retryCount: number,
		retryPolicy: RetryPolicy,
		overrideDelay?: number,
	): number
	{
		if (overrideDelay)
		{
			return overrideDelay;
		}

		let delay = retryPolicy.baseDelay * retryPolicy.backoffMultiplier**(retryCount - 1);

		// Apply jitter if enabled
		if (retryPolicy.jitter)
		{
			const jitterAmount = delay * 0.1; // 10% jitter
			delay += (Math.random() - 0.5) * 2 * jitterAmount;
		}

		// Ensure delay doesn't exceed maximum
		return Math.min(delay, retryPolicy.maxDelay);
	}

	/**
	 * Send job to dead letter queue
	 */
	private async sendToDeadLetterQueue(
		originalQueue: string,
		jobData: JobData,
		error: Error,
	): Promise<void>
	{
		try
		{
			const deadLetterJob: DeadLetterJobInfo = {
				originalJobId: jobData.id,
				originalQueue,
				failureReason: error.message,
				failureCount: jobData.retryCount,
				firstFailure: jobData.startedAt || new Date(),
				lastFailure: new Date(),
				jobData,
			};

			// Store in dead letter jobs map
			this.deadLetterJobs.set(jobData.id, deadLetterJob);

			// Publish to dead letter queue
			await this.publishJob(this.config.deadLetterQueueName, deadLetterJob, {
				priority: 'low',
				maxRetries: 0, // No retries for dead letter queue
			});

			logger.info('Job sent to dead letter queue:', {
				jobId: jobData.id,
				originalQueue,
				failureReason: error.message,
			});
		}
		catch (dlqError)
		{
			logger.error('Failed to send job to dead letter queue:', {
				jobId: jobData.id,
				error: dlqError,
			});
		}
	}

	/**
	 * Publish batch jobs with rate limiting
	 */
	async publishBatchJobs(request: BatchJobRequest): Promise<BatchJobResult>
	{
		const batchId = uuidv4();
		const startTime = Date.now();
		const jobIds: string[] = [];
		const errors: Array<{ jobIndex: number; error: string }> = [];

		const batchSize = request.batchOptions?.batchSize || this.config.batchSize;
		const delayBetweenBatches = request.batchOptions?.delayBetweenBatches || this.config.batchDelayMs;
		const failFast = request.batchOptions?.failFast || false;

		logger.info('Starting batch job processing:', {
			batchId,
			totalJobs: request.jobs.length,
			batchSize,
		});

		try
		{
			// Process jobs in batches
			for (let i = 0; i < request.jobs.length; i += batchSize)
			{
				const batch = request.jobs.slice(i, i + batchSize);

				// Process current batch
				const batchPromises = batch.map(async (job, index) =>
				{
					try
					{
						const jobId = await this.publishJob(job.queueName, job.jobData, job.options);
						jobIds.push(jobId);
						return jobId;
					}
					catch (error)
					{
						const globalIndex = i + index;
						errors.push({
							jobIndex: globalIndex,
							error: (error as Error).message,
						});

						if (failFast)
						{
							throw error;
						}

						return null;
					}
				});

				await Promise.all(batchPromises);

				// Add delay between batches (except for the last batch)
				if (i + batchSize < request.jobs.length)
				{
					await this.delay(delayBetweenBatches);
				}
			}

			const processingTime = Date.now() - startTime;
			const result: BatchJobResult = {
				batchId,
				totalJobs: request.jobs.length,
				successfulJobs: jobIds.length,
				failedJobs: errors.length,
				jobIds,
				errors,
				processingTime,
			};

			logger.info('Batch job processing completed:', result);
			return result;
		}
		catch (error)
		{
			logger.error('Batch job processing failed:', {
				batchId,
				error: (error as Error).message,
			});

			return {
				batchId,
				totalJobs: request.jobs.length,
				successfulJobs: jobIds.length,
				failedJobs: errors.length,
				jobIds,
				errors,
				processingTime: Date.now() - startTime,
			};
		}
	}

	/**
	 * Get queue statistics
	 */
	getQueueStatistics(queueName: string): JobQueueStatistics | undefined
	{
		return this.statistics.get(queueName);
	}

	/**
	 * Get all queue statistics
	 */
	getAllQueueStatistics(): Record<string, JobQueueStatistics>
	{
		const result: Record<string, JobQueueStatistics> = {};
		for (const [queueName, stats] of this.statistics)
		{
			result[queueName] = { ...stats };
		}
		return result;
	}

	/**
	 * Get health status of the job queue system
	 */
	async getHealth(): Promise<JobQueueHealth>
	{
		const now = new Date();

		// Check producer health
		const producerHealth = {
			connected: !!this.producer && this.isInitialized,
			lastCheck: now,
		};

		// Check consumer health
		const consumerHealthMap: Record<string, ConsumerHealth> = {};
		for (const [queueName, consumer] of this.consumers)
		{
			const queueConfig = this.queueConfigs.get(queueName);
			const activeJobsForQueue = Array.from(this.activeJobs.values())
				.filter(job => job.type === queueName).length;

			consumerHealthMap[queueName] = {
				queueName,
				running: !!consumer,
				concurrency: queueConfig?.concurrency || 0,
				activeJobs: activeJobsForQueue,
				lastJobProcessed: this.getLastJobProcessedTime(queueName),
			};
		}

		// Check Redis health
		let redisHealth = {
			connected: false,
			responseTime: 0,
			lastCheck: now,
		};

		try
		{
			const startTime = Date.now();
			// Simple health check - try to publish a test message
			if (this.producer)
			{
				// This is a basic check - in production you might want a more sophisticated health check
				redisHealth = {
					connected: true,
					responseTime: Date.now() - startTime,
					lastCheck: now,
				};
			}
		}
		catch (error)
		{
			redisHealth.error = (error as Error).message;
		}

		// Calculate overall statistics
		const overallStats = this.calculateOverallStatistics();

		// Determine overall health status
		let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
		if (!producerHealth.connected || !redisHealth.connected)
		{
			status = 'unhealthy';
		}
		else if (overallStats.errorRate > 0.1) // More than 10% error rate
		{
			status = 'degraded';
		}

		return {
			status,
			producer: producerHealth,
			consumers: {
				active: this.consumers.size,
				total: this.queueConfigs.size,
				queues: consumerHealthMap,
			},
			redis: redisHealth,
			statistics: overallStats,
		};
	}

	/**
	 * Pause queue processing
	 */
	async pauseQueue(queueName: string): Promise<void>
	{
		const consumer = this.consumers.get(queueName);
		if (consumer)
		{
			await consumer.shutdown();
			this.consumers.delete(queueName);

			// Mark queue as disabled
			const queueConfig = this.queueConfigs.get(queueName);
			if (queueConfig)
			{
				queueConfig.enabled = false;
			}

			logger.info(`Queue paused: ${queueName}`);
		}
	}

	/**
	 * Resume queue processing
	 */
	async resumeQueue(queueName: string): Promise<void>
	{
		const handler = this.jobHandlers.get(queueName);
		const queueConfig = this.queueConfigs.get(queueName);

		if (handler && queueConfig && !this.consumers.has(queueName))
		{
			await this.createConsumer(queueName, handler, {
				concurrency: queueConfig.concurrency,
				retryThreshold: queueConfig.retryThreshold,
			});

			queueConfig.enabled = true;
			logger.info(`Queue resumed: ${queueName}`);
		}
	}

	/**
	 * Purge queue (remove all pending jobs)
	 */
	async purgeQueue(queueName: string): Promise<void>
	{
		try
		{
			// Note: Redis-SMQ doesn't have a direct purge method
			// This would need to be implemented based on the specific Redis-SMQ version
			logger.info(`Purging queue: ${queueName}`);

			// Reset statistics for this queue
			this.initializeQueueStatistics(queueName);
		}
		catch (error)
		{
			logger.error(`Failed to purge queue ${queueName}:`, error);
			throw error;
		}
	}

	// ============================================================================
	// Private Helper Methods
	// ============================================================================

	/**
	 * Setup default retry policies for different queue types
	 */
	private setupDefaultRetryPolicies(): void
	{
		// Default retry policy
		const defaultPolicy: RetryPolicy = {
			maxAttempts: this.config.defaultRetryAttempts,
			baseDelay: this.config.defaultRetryDelay,
			maxDelay: 300000, // 5 minutes
			backoffMultiplier: 2,
			jitter: true,
		};

		// Set default policy for common queue types
		this.retryPolicies.set('default', defaultPolicy);
		this.retryPolicies.set('domain:crawl', defaultPolicy);
		this.retryPolicies.set('ranking:update', defaultPolicy);
		this.retryPolicies.set('manticore:sync', {
			...defaultPolicy,
			maxAttempts: 5, // More retries for sync operations
			baseDelay: 2000, // Longer base delay
		});
	}

	/**
	 * Get default retry policy
	 */
	private getDefaultRetryPolicy(): RetryPolicy
	{
		return this.retryPolicies.get('default')!;
	}

	/**
	 * Convert priority string to Redis-SMQ priority number
	 */
	private getPriorityNumber(priority: JobPriority): number
	{
		switch (priority)
		{
			case 'high': return 1;
			case 'medium': return 2;
			case 'low': return 3;
			default: return 2;
		}
	}

	/**
	 * Initialize statistics for a queue
	 */
	private initializeQueueStatistics(queueName: string): void
	{
		this.statistics.set(queueName, {
			totalJobs: 0,
			pendingJobs: 0,
			processingJobs: 0,
			completedJobs: 0,
			failedJobs: 0,
			retryingJobs: 0,
			deadLetterJobs: 0,
			averageProcessingTime: 0,
			throughputPerMinute: 0,
			errorRate: 0,
			lastUpdated: new Date(),
		});
	}

	/**
	 * Update queue statistics
	 */
	private updateStatistics(
		queueName: string,
		action: 'scheduled' | 'processing' | 'completed' | 'failed' | 'retry',
		processingTime?: number,
	): void
	{
		let stats = this.statistics.get(queueName);
		if (!stats)
		{
			this.initializeQueueStatistics(queueName);
			stats = this.statistics.get(queueName)!;
		}

		switch (action)
		{
			case 'scheduled':
				stats.totalJobs++;
				stats.pendingJobs++;
				break;
			case 'processing':
				stats.pendingJobs = Math.max(0, stats.pendingJobs - 1);
				stats.processingJobs++;
				break;
			case 'completed':
				stats.processingJobs = Math.max(0, stats.processingJobs - 1);
				stats.completedJobs++;
				if (processingTime)
				{
					// Update average processing time
					const totalProcessingTime = stats.averageProcessingTime * (stats.completedJobs - 1);
					stats.averageProcessingTime = (totalProcessingTime + processingTime) / stats.completedJobs;
				}
				break;
			case 'failed':
				stats.processingJobs = Math.max(0, stats.processingJobs - 1);
				stats.failedJobs++;
				break;
			case 'retry':
				stats.retryingJobs++;
				break;
		}

		// Update error rate
		if (stats.totalJobs > 0)
		{
			stats.errorRate = stats.failedJobs / stats.totalJobs;
		}

		stats.lastUpdated = new Date();
	}

	/**
	 * Calculate overall statistics across all queues
	 */
	private calculateOverallStatistics(): JobQueueStatistics
	{
		const overall: JobQueueStatistics = {
			totalJobs: 0,
			pendingJobs: 0,
			processingJobs: 0,
			completedJobs: 0,
			failedJobs: 0,
			retryingJobs: 0,
			deadLetterJobs: this.deadLetterJobs.size,
			averageProcessingTime: 0,
			throughputPerMinute: 0,
			errorRate: 0,
			lastUpdated: new Date(),
		};

		let totalProcessingTime = 0;
		let completedJobsCount = 0;

		for (const stats of this.statistics.values())
		{
			overall.totalJobs += stats.totalJobs;
			overall.pendingJobs += stats.pendingJobs;
			overall.processingJobs += stats.processingJobs;
			overall.completedJobs += stats.completedJobs;
			overall.failedJobs += stats.failedJobs;
			overall.retryingJobs += stats.retryingJobs;

			if (stats.completedJobs > 0)
			{
				totalProcessingTime += stats.averageProcessingTime * stats.completedJobs;
				completedJobsCount += stats.completedJobs;
			}
		}

		// Calculate overall average processing time
		if (completedJobsCount > 0)
		{
			overall.averageProcessingTime = totalProcessingTime / completedJobsCount;
		}

		// Calculate overall error rate
		if (overall.totalJobs > 0)
		{
			overall.errorRate = overall.failedJobs / overall.totalJobs;
		}

		return overall;
	}

	/**
	 * Get last job processed time for a queue
	 */
	private getLastJobProcessedTime(queueName: string): Date | undefined
	{
		// This would need to be tracked more precisely in a production system
		// For now, return undefined
		return undefined;
	}

	/**
	 * Start statistics collection interval
	 */
	private startStatisticsCollection(): void
	{
		this.statisticsInterval = setInterval(() =>
		{
			// Update throughput calculations
			const now = new Date();
			const timeDiff = now.getTime() - this.lastStatisticsUpdate.getTime();
			const minutesDiff = timeDiff / (1000 * 60);

			for (const [queueName, stats] of this.statistics)
			{
				// Calculate throughput per minute
				if (minutesDiff > 0)
				{
					stats.throughputPerMinute = stats.completedJobs / minutesDiff;
				}
			}

			this.lastStatisticsUpdate = now;
		}, this.config.statisticsInterval);
	}

	/**
	 * Delay utility
	 */
	private delay(ms: number): Promise<void>
	{
		// eslint-disable-next-line no-promise-executor-return
		return AsyncUtils.sleep(ms);
	}
}

export default WorkerJobQueue;
