# Job Queue Management System

This module provides comprehensive job queue management functionality extracted from the shared module and scheduler service, enhanced for the worker service architecture.

## Overview

The job queue management system consists of several key components:

- **WorkerJobQueue**: Core job queue functionality with Redis-SMQ integration
- **JobConsumer**: Domain job consumption with configurable concurrency
- **JobScheduler**: Job scheduling with priority handling and recurring jobs
- **JobProgressTracker**: Real-time job progress tracking and monitoring
- **JobStatisticsCollector**: Comprehensive statistics and performance metrics

## Features

### Core Job Queue Features

- **Redis-SMQ Integration**: Full integration with Redis-SMQ for reliable message queuing
- **Priority Handling**: Support for high, medium, and low priority jobs
- **Exponential Backoff Retry**: Intelligent retry logic with exponential backoff
- **Dead Letter Queue**: Failed jobs are moved to dead letter queue after max retries
- **Batch Processing**: Efficient batch job processing with rate limiting
- **Health Monitoring**: Comprehensive health checks and status reporting

### Job Consumer Features

- **Configurable Concurrency**: Control concurrent job processing per queue
- **Multiple Queue Support**: Handle different job types (crawl, ranking, maintenance)
- **Domain Processing Pipeline**: Integration with domain processing pipeline
- **Progress Tracking**: Real-time job progress updates
- **Resource Management**: Memory and CPU usage monitoring

### Job Scheduler Features

- **Immediate Scheduling**: Schedule jobs for immediate processing
- **Recurring Jobs**: Cron-based recurring job scheduling
- **Batch Scheduling**: Schedule multiple jobs efficiently
- **Job Management**: Enable/disable/remove scheduled jobs
- **Statistics Tracking**: Comprehensive job statistics collection

### Progress Tracking Features

- **Stage-based Progress**: Track progress through predefined stages
- **Real-time Updates**: Live progress updates with WebSocket support
- **Historical Data**: Maintain progress history for analysis
- **Stuck Job Detection**: Identify and alert on stuck jobs
- **Performance Analytics**: Progress-based performance metrics

### Statistics Collection Features

- **Queue Metrics**: Per-queue performance statistics
- **Error Analysis**: Error classification and trending
- **Performance Reports**: Comprehensive performance reporting
- **Resource Monitoring**: Memory and CPU usage tracking
- **Trend Analysis**: Historical trend analysis and forecasting

## Usage

### Basic Setup

```typescript
import {
  WorkerJobQueue,
  JobConsumer,
  JobScheduler,
  JobProgressTracker,
  JobStatisticsCollector,
} from "./queue/index.js";

// Initialize job queue
const config = {
  redisUrl: "redis://localhost:6379",
  defaultConcurrency: 10,
  defaultRetryAttempts: 3,
  enableStatistics: true,
  enableProgressTracking: true,
};

const jobQueue = new WorkerJobQueue(config);
await jobQueue.initialize();

// Initialize consumer
const jobConsumer = new JobConsumer(jobQueue, 10);
jobConsumer.configureDomainCrawlQueue(5);
jobConsumer.configureRankingUpdateQueue(3);
await jobConsumer.startConsuming();

// Initialize scheduler
const jobScheduler = new JobScheduler(jobQueue);
await jobScheduler.initialize();

// Initialize progress tracker
const progressTracker = new JobProgressTracker();
await progressTracker.initialize();

// Initialize statistics collector
const statisticsCollector = new JobStatisticsCollector();
await statisticsCollector.initialize();
```

### Publishing Jobs

```typescript
// Schedule a domain crawl job
const jobId = await jobScheduler.scheduleDomainCrawlJob(
  "example.com",
  "full",
  "medium"
);

// Schedule batch domain crawl jobs
const domains = ["example1.com", "example2.com", "example3.com"];
const result = await jobScheduler.scheduleBatchDomainCrawlJobs(
  domains,
  "full",
  "medium"
);

// Schedule a recurring maintenance job
const recurringJobId = await jobScheduler.scheduleRecurringJob({
  queueName: "queue:maintenance",
  jobData: { taskType: "cleanup" },
  cronExpression: "0 2 * * *", // Daily at 2 AM
});
```

### Consuming Jobs

```typescript
// Configure job handlers
jobConsumer.configureQueue("queue:domain:crawl", async (jobData) => {
  // Process domain crawl job
  console.log(`Processing domain: ${jobData.domain}`);

  // Update progress
  progressTracker.updateProgress(
    jobData.id,
    jobData.domain,
    "started",
    10,
    "Job processing started"
  );

  // Simulate processing
  await processDomain(jobData.domain);

  // Complete tracking
  progressTracker.completeTracking(jobData.id, jobData.domain, true);
});

// Start consuming
await jobConsumer.startConsuming();
```

### Progress Tracking

```typescript
// Start tracking a job
progressTracker.startTracking(jobData);

// Update progress through stages
progressTracker.updateProgressByStage(
  jobData.id,
  jobData.domain,
  "dns_lookup",
  "DNS lookup completed"
);

progressTracker.updateProgressByStage(
  jobData.id,
  jobData.domain,
  "homepage_analysis",
  "Homepage analysis completed"
);

// Complete tracking
progressTracker.completeTracking(jobData.id, jobData.domain, true);

// Get progress statistics
const stats = progressTracker.getProgressStatistics();
console.log(`Active jobs: ${stats.activeJobs}`);
console.log(`Average completion time: ${stats.averageCompletionTime}ms`);
```

### Statistics Collection

```typescript
// Record job lifecycle
statisticsCollector.recordJobStart(jobData);
statisticsCollector.recordJobCompletion(jobData.id, true);

// Generate performance report
const report = statisticsCollector.generateReport("day");
console.log(`Total jobs processed: ${report.overall.totalJobs}`);
console.log(`Error rate: ${report.overall.errorRate * 100}%`);

// Get performance metrics
const metrics = statisticsCollector.getPerformanceMetrics();
console.log(`Throughput: ${metrics.throughputPerMinute} jobs/min`);
console.log(`Average processing time: ${metrics.averageProcessingTime}ms`);
```

## Configuration

### Job Queue Configuration

```typescript
interface JobQueueConfig {
  // Redis-SMQ configuration
  redisUrl: string;
  redisPassword?: string;
  redisDatabase?: number;

  // Queue settings
  defaultConcurrency: number;
  defaultRetryAttempts: number;
  defaultRetryDelay: number;
  defaultJobTtl: number;

  // Batch processing
  batchSize: number;
  batchDelayMs: number;

  // Monitoring
  enableStatistics: boolean;
  statisticsInterval: number;
  enableProgressTracking: boolean;

  // Dead letter queue
  enableDeadLetterQueue: boolean;
  deadLetterQueueName: string;
}
```

### Environment Variables

```bash
# Redis configuration
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your_password
REDIS_DATABASE=0

# Worker configuration
MAX_CONCURRENT_TASKS=10
JOB_RETRY_ATTEMPTS=3
JOB_RETRY_DELAY=5000

# Queue configuration
BATCH_SIZE=10
BATCH_DELAY_MS=100
STATISTICS_INTERVAL=60000

# Dead letter queue
ENABLE_DEAD_LETTER_QUEUE=true
DEAD_LETTER_QUEUE_NAME=queue:dead-letter
```

## Queue Types

The system supports several predefined queue types:

### Domain Processing Queues

- `queue:domain:crawl` - Domain crawling jobs
- `queue:ranking:update` - Ranking calculation jobs
- `queue:traffic:analysis` - Traffic analysis jobs
- `queue:backlink:analysis` - Backlink analysis jobs
- `queue:manticore:sync` - Search index synchronization jobs

### System Queues

- `queue:maintenance` - System maintenance jobs
- `queue:dead-letter` - Failed jobs after max retries

## Job Lifecycle

1. **Job Creation**: Jobs are created and published to appropriate queues
2. **Queue Assignment**: Jobs are assigned to queues based on type and priority
3. **Consumer Processing**: Consumers pick up jobs based on concurrency limits
4. **Progress Tracking**: Job progress is tracked through predefined stages
5. **Completion/Failure**: Jobs complete successfully or fail with retry logic
6. **Statistics Collection**: Job metrics are collected for analysis
7. **Cleanup**: Completed jobs are cleaned up based on retention policies

## Error Handling

### Retry Logic

The system implements sophisticated retry logic:

- **Exponential Backoff**: Retry delays increase exponentially
- **Jitter**: Random jitter prevents thundering herd problems
- **Max Retries**: Configurable maximum retry attempts
- **Error Classification**: Different retry strategies based on error type

### Error Types

- **Transient Errors**: Network timeouts, temporary service unavailability
- **Resource Errors**: Memory limits, CPU constraints, rate limiting
- **Validation Errors**: Invalid input data, malformed requests
- **Permanent Errors**: Authentication failures, missing resources

### Dead Letter Queue

Jobs that exceed maximum retry attempts are moved to a dead letter queue for manual investigation and potential reprocessing.

## Monitoring and Alerting

### Health Checks

Each component provides comprehensive health checks:

```typescript
// Job queue health
const queueHealth = await jobQueue.getHealth();
console.log(`Queue status: ${queueHealth.status}`);

// Consumer health
const consumerHealth = jobConsumer.getHealth();
console.log(`Active jobs: ${consumerHealth.activeJobs}`);

// Progress tracker health
const progressHealth = progressTracker.getHealth();
console.log(`Stuck jobs: ${progressHealth.stuckJobs}`);

// Statistics collector health
const statsHealth = statisticsCollector.getHealth();
console.log(`Error rate: ${statsHealth.errorRate}`);
```

### Metrics

Key metrics tracked by the system:

- **Throughput**: Jobs processed per minute
- **Latency**: Average job processing time
- **Error Rate**: Percentage of failed jobs
- **Queue Depth**: Number of pending jobs
- **Resource Usage**: Memory and CPU consumption
- **Progress Distribution**: Job progress across different stages

### Alerting

The system emits events for monitoring and alerting:

- `jobStarted` - Job processing started
- `jobCompleted` - Job completed successfully
- `jobFailed` - Job failed with error
- `jobProgress` - Job progress updated
- `queueHealthChanged` - Queue health status changed
- `statisticsUpdated` - Statistics recalculated

## Performance Optimization

### Concurrency Tuning

- Configure appropriate concurrency levels per queue type
- Monitor resource usage and adjust limits accordingly
- Use priority queues to ensure critical jobs are processed first

### Batch Processing

- Use batch job scheduling for high-volume operations
- Configure appropriate batch sizes and delays
- Monitor batch processing performance and adjust parameters

### Memory Management

- Configure appropriate retention policies for job history
- Implement cleanup procedures for old job data
- Monitor memory usage and implement alerts

### Database Optimization

- Use connection pooling for database operations
- Implement proper indexing for job queries
- Monitor database performance and optimize queries

## Testing

The system includes comprehensive tests:

```bash
# Run all tests
npm test

# Run specific test suites
npm test -- WorkerJobQueue.test.ts
npm test -- JobConsumer.test.ts
npm test -- JobScheduler.test.ts
npm test -- JobProgressTracker.test.ts
npm test -- JobStatisticsCollector.test.ts

# Run tests with coverage
npm run test:coverage
```

## Migration from Original Services

This job queue system replaces functionality from:

- **Shared JobQueue**: Enhanced with better error handling and statistics
- **Scheduler JobScheduler**: Enhanced with recurring jobs and better monitoring
- **Scheduler CrawlJobManager**: Integrated into JobConsumer with progress tracking

### Migration Steps

1. **Update Dependencies**: Install required packages (redis-smq, cron, uuid)
2. **Configuration**: Update configuration to use new format
3. **Job Handlers**: Migrate job handlers to new consumer format
4. **Monitoring**: Update monitoring to use new health check endpoints
5. **Testing**: Run comprehensive tests to validate functionality

## Troubleshooting

### Common Issues

1. **Redis Connection Failures**

   - Check Redis server availability
   - Verify connection string and credentials
   - Monitor Redis memory usage

2. **High Error Rates**

   - Check error logs for patterns
   - Verify external service availability
   - Review retry configuration

3. **Performance Issues**

   - Monitor resource usage
   - Check concurrency settings
   - Review database performance

4. **Stuck Jobs**
   - Check for deadlocks or infinite loops
   - Monitor job timeouts
   - Review error handling logic

### Debug Mode

Enable debug logging for detailed troubleshooting:

```bash
LOG_LEVEL=debug npm start
```

### Health Check Endpoints

Monitor system health through dedicated endpoints:

- `/health/queue` - Job queue health status
- `/health/consumer` - Job consumer health status
- `/health/scheduler` - Job scheduler health status
- `/metrics/jobs` - Job processing metrics
- `/metrics/performance` - Performance metrics
