/**
 * Job Queue System Types
 *
 * Type definitions for the job queue management system
 */

import type {
	JobPriorityType,
	JobStatusType,
	JobDataType,
	JobOptionsType,
	QueueStatsType,
} from '../types/WorkerTypes';

// ============================================================================
// Job Queue Configuration Types
// ============================================================================

type JobQueueConfigType =
{
	// Redis-SMQ configuration
	redisUrl: string;
	redisPassword?: string;
	redisDatabase?: number;

	// Queue settings
	defaultConcurrency: number;
	defaultRetryAttempts: number;
	defaultRetryDelay: number;
	defaultJobTtl: number;

	// Batch processing
	batchSize: number;
	batchDelayMs: number;

	// Monitoring
	enableStatistics: boolean;
	statisticsInterval: number;
	enableProgressTracking: boolean;

	// Dead letter queue
	enableDeadLetterQueue: boolean;
	deadLetterQueueName: string;
};

// ============================================================================
// Job Queue Health Types
// ============================================================================

type JobQueueHealthType =
{
	status: 'healthy' | 'degraded' | 'unhealthy';
	producer: {
		connected: boolean;
		lastCheck: Date;
		error?: string;
	};
	consumers: {
		active: number;
		total: number;
		queues: Record<string, ConsumerHealthType>;
	};
	redis: {
		connected: boolean;
		responseTime: number;
		lastCheck: Date;
		error?: string;
	};
	statistics: JobQueueStatisticsType;
};

type ConsumerHealthType =
{
	queueName: string;
	running: boolean;
	concurrency: number;
	activeJobs: number;
	lastJobProcessed?: Date;
	error?: string;
};

// ============================================================================
// Job Progress Tracking Types
// ============================================================================

type JobProgressUpdateType =
{
	jobId: string;
	domain: string;
	stage: string;
	progress: number; // 0-100
	message: string;
	timestamp: Date;
	details?: Record<string, unknown>;
};

type JobProgressHistoryType =
{
	jobId: string;
	domain: string;
	updates: JobProgressUpdateType[];
	startedAt: Date;
	completedAt?: Date;
	currentStage?: string;
	currentProgress: number;
};

// ============================================================================
// Job Statistics Types
// ============================================================================

type JobQueueStatisticsType =
{
	totalJobs: number;
	pendingJobs: number;
	processingJobs: number;
	completedJobs: number;
	failedJobs: number;
	retryingJobs: number;
	deadLetterJobs: number;
	averageProcessingTime: number;
	throughputPerMinute: number;
	errorRate: number;
	lastUpdated: Date;
};

type JobStatisticsReportType =
{
	timeframe: 'hour' | 'day' | 'week' | 'month';
	startTime: Date;
	endTime: Date;
	queues: Record<string, QueueStatisticsReportType>;
	overall: JobQueueStatisticsType;
};

type QueueStatisticsReportType =
{
	queueName: string;
	statistics: JobQueueStatisticsType;
	trends: {
		jobsPerHour: number[];
		averageProcessingTime: number[];
		errorRate: number[];
	};
	topErrors: Array<{
		error: string;
		count: number;
		percentage: number;
	}>;
};

// ============================================================================
// Job Retry and Error Handling Types
// ============================================================================

type RetryPolicyType =
{
	maxAttempts: number;
	baseDelay: number;
	maxDelay: number;
	backoffMultiplier: number;
	jitter: boolean;
};

type JobFailureClassificationType =
{
	type: 'transient' | 'permanent' | 'timeout' | 'resource' | 'validation';
	retryable: boolean;
	retryDelay?: number;
	escalate: boolean;
};

type DeadLetterJobInfoType =
{
	originalJobId: string;
	originalQueue: string;
	failureReason: string;
	failureCount: number;
	firstFailure: Date;
	lastFailure: Date;
	jobData: JobDataType;
};

// ============================================================================
// Batch Processing Types
// ============================================================================

type BatchJobRequestType =
{
	jobs: Array<{
		queueName: string;
		jobData: unknown;
		options?: JobOptionsType;
	}>;
	batchOptions?: {
		batchSize?: number;
		delayBetweenBatches?: number;
		failFast?: boolean;
	};
};

type BatchJobResultType =
{
	batchId: string;
	totalJobs: number;
	successfulJobs: number;
	failedJobs: number;
	jobIds: string[];
	errors: Array<{
		jobIndex: number;
		error: string;
	}>;
	processingTime: number;
};

// ============================================================================
// Job Consumer Types
// ============================================================================

type ConsumerConfigType =
{
	queueName: string;
	concurrency: number;
	retryThreshold: number;
	enabled: boolean;
	handler: JobHandlerType;
};

type JobHandlerType =
{
	(jobData: JobDataType): Promise<void>;
};

type ConsumerMetricsType =
{
	queueName: string;
	jobsProcessed: number;
	jobsSucceeded: number;
	jobsFailed: number;
	averageProcessingTime: number;
	lastJobProcessed?: Date;
	uptime: number;
};

// ============================================================================
// Job Scheduling Types
// ============================================================================

type ScheduledJobType =
{
	id: string;
	queueName: string;
	jobData: unknown;
	cronExpression: string;
	options: JobOptionsType;
	enabled: boolean;
	lastRun?: Date;
	nextRun: Date;
	runCount: number;
};

type RecurringJobConfigType =
{
	queueName: string;
	jobData: unknown;
	cronExpression: string;
	options?: JobOptionsType;
	enabled?: boolean;
	maxRuns?: number;
};

// ============================================================================
// Queue Management Types
// ============================================================================

type QueueManagementOperationType =
{
	operation: 'pause' | 'resume' | 'purge' | 'drain';
	queueName: string;
	reason?: string;
	timestamp: Date;
	operatorId?: string;
};

type QueueInfoType =
{
	name: string;
	enabled: boolean;
	paused: boolean;
	concurrency: number;
	retryThreshold: number;
	statistics: QueueStatsType;
	lastActivity?: Date;
	consumerCount: number;
};

// ============================================================================
// Export all types
// ============================================================================

export type {
	JobPriorityType,
	JobStatusType,
	JobDataType,
	JobOptionsType,
	QueueStatsType,
	// Internal
	JobQueueConfigType,
	JobQueueHealthType,
	ConsumerHealthType,
	JobProgressUpdateType,
	JobProgressHistoryType,
	JobQueueStatisticsType,
	JobStatisticsReportType,
	QueueStatisticsReportType,
	RetryPolicyType,
	JobFailureClassificationType,
	DeadLetterJobInfoType,
	BatchJobRequestType,
	BatchJobResultType,
	ConsumerConfigType,
	JobHandlerType,
	ConsumerMetricsType,
	ScheduledJobType,
	RecurringJobConfigType,
	QueueManagementOperationType,
	QueueInfoType,
};
