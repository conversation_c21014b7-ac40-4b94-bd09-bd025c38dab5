/**
 * Job Progress Tracker
 *
 * Provides comprehensive job progress tracking, status updates,
 * and monitoring capabilities for the worker service.
 */

import { EventEmitter } from 'events';
import { logger as loggerFactory } from '@shared/utils/Logger';
import type {
	JobProgressUpdate,
	JobProgressHistory,
} from './types';
import type { JobData } from '../types/WorkerTypes';

const logger = loggerFactory.getLogger('job-progress-tracker');

/**
 * Job Progress Tracker
 *
 * Tracks job progress through various stages and provides
 * real-time updates and historical data.
 */
class JobProgressTracker extends EventEmitter
{
	private progressHistory = new Map<string, JobProgressHistory>();
	private activeJobs = new Map<string, JobData>();
	private maxHistorySize: number;
	private cleanupInterval: NodeJS.Timeout | null = null;
	private isInitialized = false;

	// Progress stage definitions for different job types
	private readonly stageDefinitions = new Map<string, Array<{ name: string; weight: number }>>([
		['domain:crawl', [
			{ name: 'started', weight: 5 },
			{ name: 'dns_lookup', weight: 10 },
			{ name: 'robots_check', weight: 15 },
			{ name: 'ssl_analysis', weight: 25 },
			{ name: 'homepage_analysis', weight: 40 },
			{ name: 'screenshot_capture', weight: 60 },
			{ name: 'performance_audit', weight: 80 },
			{ name: 'content_analysis', weight: 90 },
			{ name: 'finalization', weight: 95 },
			{ name: 'completed', weight: 100 },
		]],
		['ranking:update', [
			{ name: 'started', weight: 10 },
			{ name: 'data_collection', weight: 30 },
			{ name: 'score_calculation', weight: 60 },
			{ name: 'ranking_update', weight: 80 },
			{ name: 'index_sync', weight: 95 },
			{ name: 'completed', weight: 100 },
		]],
		['maintenance', [
			{ name: 'started', weight: 10 },
			{ name: 'preparation', weight: 30 },
			{ name: 'execution', weight: 70 },
			{ name: 'cleanup', weight: 90 },
			{ name: 'completed', weight: 100 },
		]],
	]);

	constructor(maxHistorySize: number = 1000)
	{
		super();
		this.maxHistorySize = maxHistorySize;
	}

	/**
	 * Initialize the progress tracker
	 */
	async initialize(): Promise<void>
	{
		if (this.isInitialized)
		{
			logger.warn('Job progress tracker already initialized');
			return;
		}

		try
		{
			logger.info('Initializing Job Progress Tracker...');

			// Start cleanup interval (every 5 minutes)
			this.cleanupInterval = setInterval(() =>
			{
				this.cleanupOldProgress();
			}, 5 * 60 * 1000);

			this.isInitialized = true;
			logger.info('Job Progress Tracker initialized successfully');
		}
		catch (error)
		{
			logger.error('Failed to initialize Job Progress Tracker:', error);
			throw error;
		}
	}

	/**
	 * Shutdown the progress tracker
	 */
	async shutdown(): Promise<void>
	{
		try
		{
			logger.info('Shutting down Job Progress Tracker...');

			// Stop cleanup interval
			if (this.cleanupInterval)
			{
				clearInterval(this.cleanupInterval);
				this.cleanupInterval = null;
			}

			// Clear collections
			this.progressHistory.clear();
			this.activeJobs.clear();

			this.isInitialized = false;
			logger.info('Job Progress Tracker shut down successfully');
		}
		catch (error)
		{
			logger.error('Error shutting down Job Progress Tracker:', error);
			throw error;
		}
	}

	/**
	 * Start tracking a job
	 */
	startTracking(jobData: JobData): void
	{
		const jobHistory: JobProgressHistory = {
			jobId: jobData.id,
			domain: jobData.domain,
			updates: [],
			startedAt: new Date(),
			currentProgress: 0,
		};

		this.progressHistory.set(jobData.id, jobHistory);
		this.activeJobs.set(jobData.id, jobData);

		// Add initial progress update
		this.updateProgress(
			jobData.id,
			jobData.domain,
			'started',
			0,
			'Job tracking started',
		);

		logger.debug('Started tracking job:', {
			jobId: jobData.id,
			domain: jobData.domain,
			type: jobData.type,
		});
	}

	/**
	 * Update job progress
	 */
	updateProgress(
		jobId: string,
		domain: string,
		stage: string,
		progress: number,
		message: string,
		details?: Record<string, unknown>,
	): void
	{
		const jobHistory = this.progressHistory.get(jobId);
		if (!jobHistory)
		{
			logger.warn(`Job history not found for job: ${jobId}`);
			return;
		}

		// Create progress update
		const progressUpdate: JobProgressUpdate = {
			jobId,
			domain,
			stage,
			progress: Math.max(0, Math.min(100, progress)), // Clamp between 0-100
			message,
			timestamp: new Date(),
			details,
		};

		// Add to history
		jobHistory.updates.push(progressUpdate);
		jobHistory.currentStage = stage;
		jobHistory.currentProgress = progressUpdate.progress;

		// Limit history size per job
		if (jobHistory.updates.length > 100)
		{
			jobHistory.updates = jobHistory.updates.slice(-50); // Keep last 50 updates
		}

		// Emit progress event
		this.emit('progress', progressUpdate);

		logger.debug('Job progress updated:', {
			jobId,
			domain,
			stage,
			progress: progressUpdate.progress,
			message,
		});
	}

	/**
	 * Update progress by stage name (auto-calculates progress percentage)
	 */
	updateProgressByStage(
		jobId: string,
		domain: string,
		stage: string,
		message: string,
		details?: Record<string, unknown>,
	): void
	{
		const jobData = this.activeJobs.get(jobId);
		if (!jobData)
		{
			logger.warn(`Active job not found for progress update: ${jobId}`);
			return;
		}

		// Calculate progress based on stage
		const progress = this.calculateProgressFromStage(jobData.type, stage);

		this.updateProgress(jobId, domain, stage, progress, message, details);
	}

	/**
	 * Complete job tracking
	 */
	completeTracking(
		jobId: string,
		domain: string,
		success: boolean,
		finalMessage?: string,
	): void
	{
		const jobHistory = this.progressHistory.get(jobId);
		if (!jobHistory)
		{
			logger.warn(`Job history not found for completion: ${jobId}`);
			return;
		}

		// Add final progress update
		const finalStage = success ? 'completed' : 'failed';
		const finalProgress = success ? 100 : jobHistory.currentProgress;
		const message = finalMessage || (success ? 'Job completed successfully' : 'Job failed');

		this.updateProgress(jobId, domain, finalStage, finalProgress, message);

		// Mark as completed
		jobHistory.completedAt = new Date();

		// Remove from active jobs
		this.activeJobs.delete(jobId);

		// Emit completion event
		this.emit('completed', {
			jobId,
			domain,
			success,
			duration: jobHistory.completedAt.getTime() - jobHistory.startedAt.getTime(),
			finalProgress,
		});

		logger.debug('Job tracking completed:', {
			jobId,
			domain,
			success,
			finalProgress,
		});
	}

	/**
	 * Get job progress history
	 */
	getJobProgress(jobId: string): JobProgressHistory | undefined
	{
		return this.progressHistory.get(jobId);
	}

	/**
	 * Get current progress for a job
	 */
	getCurrentProgress(jobId: string): number
	{
		const jobHistory = this.progressHistory.get(jobId);
		return jobHistory?.currentProgress || 0;
	}

	/**
	 * Get progress for all active jobs
	 */
	getActiveJobsProgress(): Record<string, { progress: number; stage: string; domain: string }>
	{
		const result: Record<string, { progress: number; stage: string; domain: string }> = {};

		for (const [jobId, jobHistory] of this.progressHistory)
		{
			if (!jobHistory.completedAt)
			{
				result[jobId] = {
					progress: jobHistory.currentProgress,
					stage: jobHistory.currentStage || 'unknown',
					domain: jobHistory.domain,
				};
			}
		}

		return result;
	}

	/**
	 * Get progress statistics
	 */
	getProgressStatistics(): {
		totalJobs: number;
		activeJobs: number;
		completedJobs: number;
		averageCompletionTime: number;
		progressDistribution: Record<string, number>;
	}
	{
		const totalJobs = this.progressHistory.size;
		let activeJobs = 0;
		let completedJobs = 0;
		let totalCompletionTime = 0;
		const progressDistribution: Record<string, number> = {
			'0-20': 0,
			'21-40': 0,
			'41-60': 0,
			'61-80': 0,
			'81-100': 0,
		};

		for (const jobHistory of this.progressHistory.values())
		{
			if (jobHistory.completedAt)
			{
				completedJobs++;
				totalCompletionTime += jobHistory.completedAt.getTime() - jobHistory.startedAt.getTime();
			}
			else
			{
				activeJobs++;
			}

			// Calculate progress distribution
			const progress = jobHistory.currentProgress;
			if (progress <= 20)
			{
				progressDistribution['0-20']++;
			}
			else if (progress <= 40)
			{
				progressDistribution['21-40']++;
			}
			else if (progress <= 60)
			{
				progressDistribution['41-60']++;
			}
			else if (progress <= 80)
			{
				progressDistribution['61-80']++;
			}
			else
			{
				progressDistribution['81-100']++;
			}
		}

		const averageCompletionTime = completedJobs > 0 ? totalCompletionTime / completedJobs : 0;

		return {
			totalJobs,
			activeJobs,
			completedJobs,
			averageCompletionTime,
			progressDistribution,
		};
	}

	/**
	 * Get jobs by progress range
	 */
	getJobsByProgressRange(minProgress: number, maxProgress: number): JobProgressHistory[]
	{
		const result: JobProgressHistory[] = [];

		for (const jobHistory of this.progressHistory.values())
		{
			if (jobHistory.currentProgress >= minProgress && jobHistory.currentProgress <= maxProgress)
			{
				result.push(jobHistory);
			}
		}

		return result.sort((a, b) => b.startedAt.getTime() - a.startedAt.getTime());
	}

	/**
	 * Get stuck jobs (jobs that haven't progressed in a while)
	 */
	getStuckJobs(timeoutMinutes: number = 30): JobProgressHistory[]
	{
		const cutoffTime = new Date(Date.now() - timeoutMinutes * 60 * 1000);
		const stuckJobs: JobProgressHistory[] = [];

		for (const jobHistory of this.progressHistory.values())
		{
			if (!jobHistory.completedAt && jobHistory.updates.length > 0)
			{
				const lastUpdate = jobHistory.updates[jobHistory.updates.length - 1];
				if (lastUpdate.timestamp < cutoffTime)
				{
					stuckJobs.push(jobHistory);
				}
			}
		}

		return stuckJobs.sort((a, b) => a.startedAt.getTime() - b.startedAt.getTime());
	}

	/**
	 * Get progress health status
	 */
	getHealth(): {
		status: 'healthy' | 'degraded' | 'unhealthy';
		activeJobs: number;
		stuckJobs: number;
		averageProgress: number;
		memoryUsage: number;
	}
	{
		const statistics = this.getProgressStatistics();
		const stuckJobs = this.getStuckJobs(30).length;

		// Calculate average progress of active jobs
		let totalProgress = 0;
		let activeJobCount = 0;
		for (const jobHistory of this.progressHistory.values())
		{
			if (!jobHistory.completedAt)
			{
				totalProgress += jobHistory.currentProgress;
				activeJobCount++;
			}
		}
		const averageProgress = activeJobCount > 0 ? totalProgress / activeJobCount : 0;

		// Calculate memory usage (approximate)
		const memoryUsage = this.progressHistory.size * 1024; // Rough estimate

		// Determine health status
		let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
		if (!this.isInitialized)
		{
			status = 'unhealthy';
		}
		else if (stuckJobs > statistics.activeJobs * 0.2) // More than 20% stuck
		{
			status = 'degraded';
		}
		else if (memoryUsage > this.maxHistorySize * 1024) // Memory usage too high
		{
			status = 'degraded';
		}

		return {
			status,
			activeJobs: statistics.activeJobs,
			stuckJobs,
			averageProgress,
			memoryUsage,
		};
	}

	// ============================================================================
	// Private Helper Methods
	// ============================================================================

	/**
	 * Calculate progress percentage from stage name
	 */
	private calculateProgressFromStage(jobType: string, stage: string): number
	{
		// Normalize job type for lookup
		const normalizedType = jobType.replace('queue:', '').replace(':', ':');
		const stages = this.stageDefinitions.get(normalizedType);

		if (!stages)
		{
			// Default progress calculation for unknown job types
			const commonStages = ['started', 'processing', 'completed', 'failed'];
			const stageIndex = commonStages.indexOf(stage);
			if (stageIndex >= 0)
			{
				return Math.round((stageIndex / (commonStages.length - 1)) * 100);
			}
			return 50; // Default to 50% for unknown stages
		}

		// Find stage in definitions
		const stageDefinition = stages.find(s => s.name === stage);
		if (stageDefinition)
		{
			return stageDefinition.weight;
		}

		// If stage not found, try to interpolate based on position
		const stageNames = stages.map(s => s.name);
		const stageIndex = stageNames.indexOf(stage);
		if (stageIndex >= 0)
		{
			return stages[stageIndex].weight;
		}

		// Default to 50% for unknown stages
		return 50;
	}

	/**
	 * Clean up old progress history
	 */
	private cleanupOldProgress(): void
	{
		const cutoffTime = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago
		let cleanedCount = 0;

		for (const [jobId, jobHistory] of this.progressHistory)
		{
			// Only clean up completed jobs older than cutoff time
			if (jobHistory.completedAt && jobHistory.completedAt < cutoffTime)
			{
				this.progressHistory.delete(jobId);
				cleanedCount++;
			}
		}

		// If still over limit, remove oldest completed jobs
		if (this.progressHistory.size > this.maxHistorySize)
		{
			const completedJobs = Array.from(this.progressHistory.entries())
				.filter(([, history]) => history.completedAt)
				.sort(([, a], [, b]) => a.completedAt!.getTime() - b.completedAt!.getTime());

			const toRemove = this.progressHistory.size - this.maxHistorySize;
			for (let i = 0; i < Math.min(toRemove, completedJobs.length); i++)
			{
				this.progressHistory.delete(completedJobs[i][0]);
				cleanedCount++;
			}
		}

		if (cleanedCount > 0)
		{
			logger.debug(`Cleaned up ${cleanedCount} old progress records`);
		}
	}
}

export { JobProgressTracker };
