/**
 * Pipeline Integration Tests
 *
 * End-to-end integration tests for the complete domain processing pipeline.
 */

import { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach, vi } from 'vitest';
import { WorkerService } from '../../core/WorkerService';
import type { DomainJobType, ProcessingResultType } from '../../types/WorkerTypes';
import { testConfig, createMockDomainJob, testDatabaseHelpers } from '../setup';

// Mock external services for integration tests
const mockBrowserless = {
	screenshot: vi.fn(),
	performance: vi.fn(),
};

const mockImageProxy = {
	optimize: vi.fn(),
};

vi.mock('axios', () => ({
	default: {
		get: vi.fn(),
		post: vi.fn(),
	},
}));

describe('Pipeline Integration Tests', () =>
{
	let workerService: WorkerService;
	let testDomains: string[];

	beforeAll(async () =>
	{
		// Setup test environment
		process.env.NODE_ENV = 'test';
		process.env.WORKER_INSTANCE_ID = 'integration-test-worker';
		process.env.MAX_CONCURRENT_TASKS = '2';
		process.env.SCYLLA_HOSTS = testConfig.scyllaHosts.join(',');
		process.env.MARIA_HOST = testConfig.mariaHost;
		process.env.REDIS_URL = testConfig.redisUrl;
		process.env.MANTICORE_HOST = testConfig.manticoreHost;
		process.env.BROWSERLESS_URL = testConfig.browserlessUrl;

		// Initialize test databases
		await testDatabaseHelpers.seedTestData();

		testDomains = [
			'integration-test-1.com',
			'integration-test-2.com',
			'integration-test-3.com',
		];
	});

	afterAll(async () =>
	{
		// Cleanup test data
		await testDatabaseHelpers.cleanupTestData();
	});

	beforeEach(async () =>
	{
		workerService = new WorkerService();
		await workerService.initialize();
	});

	afterEach(async () =>
	{
		if (workerService.isServiceRunning())
		{
			await workerService.stop();
		}
		if (workerService.isServiceInitialized())
		{
			await workerService.shutdown();
		}
	});

	describe('Complete Domain Processing Pipeline', () =>
	{
		it('should process a domain through the complete pipeline successfully', async () =>
		{
			const domain = testDomains[0];
			const domainJob: DomainJobType = createMockDomainJob(domain, {
				crawlType: 'full',
				priority: 'medium',
			});

			const result: ProcessingResultType = await workerService.processJob(domainJob);

			// Verify overall result
			expect(result.domain).toBe(domain);
			expect(result.jobId).toBe(domainJob.id);
			expect(result.status).toBe('success');
			expect(result.totalProcessingTime).toBeGreaterThan(0);

			// Verify crawling phase
			expect(result.crawlingResult).toBeDefined();
			expect(result.crawlingResult.domain).toBe(domain);
			expect(result.crawlingResult.status).toBe('success');
			expect(result.crawlingResult.completedModules.length).toBeGreaterThan(0);

			// Verify ranking phase
			expect(result.rankingResult).toBeDefined();
			expect(result.rankingResult.domain).toBe(domain);
			expect(result.rankingResult.status).toBe('success');
			expect(result.rankingResult.compositeScore).toBeGreaterThan(0);
			expect(result.rankingResult.compositeScore).toBeLessThanOrEqual(100);

			// Verify indexing phase
			expect(result.indexingResult).toBeDefined();
			expect(result.indexingResult.domain).toBe(domain);
			expect(result.indexingResult.status).toBe('success');
			expect(result.indexingResult.updatedIndexes.length).toBeGreaterThan(0);

			// Verify timing information
			expect(result.phaseTimings.crawling).toBeGreaterThan(0);
			expect(result.phaseTimings.ranking).toBeGreaterThan(0);
			expect(result.phaseTimings.indexing).toBeGreaterThan(0);

			// Verify resource usage tracking
			expect(result.resourceUsage).toBeDefined();
			expect(result.resourceUsage.cpuTime).toBeGreaterThan(0);
			expect(result.resourceUsage.memoryPeak).toBeGreaterThan(0);
		}, 30000); // 30 second timeout for integration test

		it('should handle partial failures gracefully', async () =>
		{
			const domain = 'partial-failure-test.com';
			const domainJob: DomainJobType = createMockDomainJob(domain, {
				crawlType: 'full',
				priority: 'low',
			});

			// Mock some modules to fail
			vi.mocked(mockBrowserless.screenshot).mockRejectedValue(new Error('Screenshot service unavailable'));

			const result = await workerService.processJob(domainJob);

			// Should still succeed overall with partial data
			expect(result.status).toBe('partial');
			expect(result.crawlingResult.status).toBe('partial');
			expect(result.crawlingResult.failedModules.length).toBeGreaterThan(0);
			expect(result.crawlingResult.completedModules.length).toBeGreaterThan(0);

			// Ranking should still work with available data
			expect(result.rankingResult.status).toBe('success');
			expect(result.indexingResult.status).toBe('success');

			// Should have error information
			expect(result.errors.length).toBeGreaterThan(0);
			expect(result.errors[0].message).toContain('Screenshot service unavailable');
		});

		it('should respect crawl type configurations', async () =>
		{
			const domain = testDomains[1];

			// Test quick crawl
			const quickJob: DomainJobType = createMockDomainJob(domain, {
				crawlType: 'quick',
				priority: 'high',
			});

			const quickResult = await workerService.processJob(quickJob);

			expect(quickResult.crawlingResult.completedModules).toContain('dns');
			expect(quickResult.crawlingResult.completedModules).toContain('robots');
			expect(quickResult.crawlingResult.completedModules).toContain('homepage');
			// Quick crawl should not include heavy modules
			expect(quickResult.crawlingResult.completedModules).not.toContain('screenshot');
			expect(quickResult.crawlingResult.completedModules).not.toContain('performance');

			// Test full crawl
			const fullJob: DomainJobType = createMockDomainJob(domain, {
				crawlType: 'full',
				priority: 'medium',
			});

			const fullResult = await workerService.processJob(fullJob);

			expect(fullResult.crawlingResult.completedModules.length).toBeGreaterThan(
				quickResult.crawlingResult.completedModules.length,
			);
		});
	});

	describe('Concurrent Processing', () =>
	{
		it('should handle multiple domains concurrently without conflicts', async () =>
		{
			const jobs = testDomains.map(domain => createMockDomainJob(domain, {
				crawlType: 'quick',
				priority: 'medium',
			}));

			const startTime = Date.now();
			const results = await Promise.all(
				jobs.map(job => workerService.processJob(job)),
			);
			const endTime = Date.now();

			// All jobs should succeed
			expect(results).toHaveLength(3);
			results.forEach((result, index) =>
			{
				expect(result.status).toBe('success');
				expect(result.domain).toBe(testDomains[index]);
			});

			// Concurrent processing should be faster than sequential
			const concurrentTime = endTime - startTime;
			expect(concurrentTime).toBeLessThan(results.length * 10000); // Should be much faster than sequential
		});

		it('should respect concurrency limits', async () =>
		{
			// Create more jobs than the concurrency limit
			const manyJobs = Array.from({ length: 5 }, (_, i) =>
				createMockDomainJob(`concurrent-test-${i}.com`, {
					crawlType: 'quick',
					priority: 'low',
				}));

			const startTime = Date.now();
			const results = await Promise.all(
				manyJobs.map(job => workerService.processJob(job)),
			);
			const endTime = Date.now();

			// All jobs should complete successfully
			expect(results).toHaveLength(5);
			results.forEach((result) =>
			{
				expect(result.status).toBe('success');
			});

			// Should take longer due to concurrency limits
			const totalTime = endTime - startTime;
			expect(totalTime).toBeGreaterThan(5000); // Should queue some jobs
		});
	});

	describe('Error Recovery and Resilience', () =>
	{
		it('should recover from temporary database failures', async () =>
		{
			const domain = 'db-failure-test.com';
			const domainJob: DomainJobType = createMockDomainJob(domain);

			// Mock temporary database failure
			let failureCount = 0;
			const originalMethod = workerService.getDatabaseManager().getScyllaClient;
			vi.spyOn(workerService.getDatabaseManager(), 'getScyllaClient').mockImplementation(() =>
			{
				failureCount++;
				if (failureCount <= 2)
				{
					throw new Error('Temporary database failure');
				}
				return originalMethod.call(workerService.getDatabaseManager());
			});

			const result = await workerService.processJob(domainJob);

			// Should eventually succeed after retries
			expect(result.status).toBe('success');
			expect(failureCount).toBeGreaterThan(2); // Should have retried
		});

		it('should handle lock acquisition failures gracefully', async () =>
		{
			const domain = 'lock-conflict-test.com';
			const domainJob: DomainJobType = createMockDomainJob(domain);

			// Simulate another worker holding the lock
			const lockManager = workerService.getLockManager();
			await lockManager.acquireLock(domain, 300000); // Pre-acquire lock

			const result = await workerService.processJob(domainJob);

			expect(result.status).toBe('failure');
			expect(result.errors[0].message).toContain('lock');
		});

		it('should implement circuit breaker for external services', async () =>
		{
			const domain = 'circuit-breaker-test.com';

			// Mock external service failures
			vi.mocked(mockBrowserless.screenshot).mockRejectedValue(new Error('Service unavailable'));

			const jobs = Array.from({ length: 5 }, () => createMockDomainJob(domain));

			const results = await Promise.all(
				jobs.map(job => workerService.processJob(job)),
			);

			// First few should try and fail, later ones should be circuit-broken
			const failedResults = results.filter(r => r.status === 'partial');
			expect(failedResults.length).toBeGreaterThan(0);

			// Circuit breaker should prevent further attempts
			const circuitBreakerErrors = results.filter(r =>
				r.errors.some(e => e.message.includes('circuit breaker')));
			expect(circuitBreakerErrors.length).toBeGreaterThan(0);
		});
	});

	describe('Data Consistency and Validation', () =>
	{
		it('should maintain data consistency across all databases', async () =>
		{
			const domain = 'consistency-test.com';
			const domainJob: DomainJobType = createMockDomainJob(domain);

			const result = await workerService.processJob(domainJob);

			expect(result.status).toBe('success');

			// Verify data exists in all databases
			const scyllaClient = workerService.getDatabaseManager().getScyllaClient();
			const mariaClient = workerService.getDatabaseManager().getMariaClient();
			const manticoreClient = workerService.getDatabaseManager().getManticoreClient();

			// Check ScyllaDB for crawl data
			const crawlData = await scyllaClient.execute(
				'SELECT * FROM domain_analysis WHERE domain = ?',
				[domain],
			);
			expect(crawlData.rows.length).toBeGreaterThan(0);

			// Check MariaDB for ranking data
			const rankingData = await mariaClient.execute(
				'SELECT * FROM domain_rankings WHERE domain = ?',
				[domain],
			);
			expect(rankingData.length).toBeGreaterThan(0);

			// Check Manticore for search index
			const searchData = await manticoreClient.query(
				`SELECT * FROM domains WHERE domain = '${domain}'`,
			);
			expect(searchData.length).toBeGreaterThan(0);
		});

		it('should validate data integrity after processing', async () =>
		{
			const domain = 'validation-test.com';
			const domainJob: DomainJobType = createMockDomainJob(domain);

			const result = await workerService.processJob(domainJob);

			expect(result.status).toBe('success');

			// Verify data integrity
			expect(result.crawlingResult.validation.isComplete).toBe(true);
			expect(result.crawlingResult.validation.completenessScore).toBeGreaterThan(70);
			expect(result.rankingResult.confidence).toBeGreaterThan(0.8);

			// Verify no data corruption
			expect(result.crawlingResult.results.dns).toBeDefined();
			expect(result.crawlingResult.results.robots).toBeDefined();
			expect(result.crawlingResult.results.homepage).toBeDefined();
		});

		it('should handle schema migrations gracefully', async () =>
		{
			const domain = 'migration-test.com';
			const domainJob: DomainJobType = createMockDomainJob(domain);

			// Simulate schema version mismatch
			const migrationManager = workerService.getDatabaseManager().getMigrationManager();
			vi.spyOn(migrationManager, 'checkSchemaVersion').mockResolvedValue(false);
			vi.spyOn(migrationManager, 'runMigrations').mockResolvedValue(true);

			const result = await workerService.processJob(domainJob);

			expect(result.status).toBe('success');
			expect(migrationManager.runMigrations).toHaveBeenCalled();
		});
	});

	describe('Performance and Scalability', () =>
	{
		it('should meet performance benchmarks', async () =>
		{
			const domain = 'performance-test.com';
			const domainJob: DomainJobType = createMockDomainJob(domain, {
				crawlType: 'quick',
			});

			const startTime = Date.now();
			const result = await workerService.processJob(domainJob);
			const endTime = Date.now();

			expect(result.status).toBe('success');

			// Quick crawl should complete within reasonable time
			const processingTime = endTime - startTime;
			expect(processingTime).toBeLessThan(15000); // 15 seconds max

			// Resource usage should be reasonable
			expect(result.resourceUsage.memoryPeak).toBeLessThan(500 * 1024 * 1024); // 500MB max
			expect(result.resourceUsage.cpuTime).toBeLessThan(10000); // 10 seconds CPU time max
		});

		it('should handle high-volume processing efficiently', async () =>
		{
			const batchSize = 10;
			const jobs = Array.from({ length: batchSize }, (_, i) =>
				createMockDomainJob(`batch-test-${i}.com`, {
					crawlType: 'quick',
					priority: 'low',
				}));

			const startTime = Date.now();
			const results = await Promise.all(
				jobs.map(job => workerService.processJob(job)),
			);
			const endTime = Date.now();

			// All jobs should succeed
			expect(results).toHaveLength(batchSize);
			results.forEach((result) =>
			{
				expect(result.status).toBe('success');
			});

			// Average processing time should be reasonable
			const totalTime = endTime - startTime;
			const averageTime = totalTime / batchSize;
			expect(averageTime).toBeLessThan(5000); // 5 seconds average per domain
		});

		it('should scale memory usage linearly with concurrent jobs', async () =>
		{
			const getMemoryUsage = () => process.memoryUsage().heapUsed;

			const baselineMemory = getMemoryUsage();

			// Process single job
			const singleJob = createMockDomainJob('memory-test-1.com');
			await workerService.processJob(singleJob);
			const singleJobMemory = getMemoryUsage();

			// Process multiple jobs concurrently
			const multipleJobs = Array.from({ length: 3 }, (_, i) =>
				createMockDomainJob(`memory-test-${i + 2}.com`));
			await Promise.all(multipleJobs.map(job => workerService.processJob(job)));
			const multipleJobsMemory = getMemoryUsage();

			// Memory usage should scale reasonably
			const singleJobIncrease = singleJobMemory - baselineMemory;
			const multipleJobsIncrease = multipleJobsMemory - baselineMemory;

			expect(multipleJobsIncrease).toBeLessThan(singleJobIncrease * 5); // Should not be 5x for 3x jobs
		});
	});

	describe('Monitoring and Observability', () =>
	{
		it('should collect comprehensive metrics during processing', async () =>
		{
			const domain = 'metrics-test.com';
			const domainJob: DomainJobType = createMockDomainJob(domain);

			const result = await workerService.processJob(domainJob);

			expect(result.status).toBe('success');

			// Verify metrics collection
			const metrics = workerService.getMetrics();
			expect(metrics.totalJobsProcessed).toBeGreaterThan(0);
			expect(metrics.averageProcessingTime).toBeGreaterThan(0);
			expect(metrics.successRate).toBeGreaterThan(0);

			// Verify task-level metrics
			expect(result.taskMetrics).toHaveLength(3); // crawling, ranking, indexing
			result.taskMetrics.forEach((taskMetric) =>
			{
				expect(taskMetric.executionTime).toBeGreaterThan(0);
				expect(taskMetric.resourceUsage).toBeDefined();
			});
		});

		it('should provide detailed health information', async () =>
		{
			const health = await workerService.getHealth();

			expect(health.status).toBe('healthy');
			expect(health.workerId).toBe('integration-test-worker');
			expect(health.uptime).toBeGreaterThan(0);

			// Database health
			expect(health.databases.scylla.connected).toBe(true);
			expect(health.databases.maria.connected).toBe(true);
			expect(health.databases.redis.connected).toBe(true);
			expect(health.databases.manticore.connected).toBe(true);

			// External services health
			expect(health.externalServices.browserless).toBeDefined();

			// Lock system health
			expect(health.lockSystem.healthy).toBe(true);
			expect(health.lockSystem.statistics).toBeDefined();
		});

		it('should track processing statistics accurately', async () =>
		{
			const initialStats = workerService.getStatistics();

			// Process several jobs
			const jobs = Array.from({ length: 3 }, (_, i) =>
				createMockDomainJob(`stats-test-${i}.com`));

			for (const job of jobs)
			{
				await workerService.processJob(job);
			}

			const finalStats = workerService.getStatistics();

			expect(finalStats.totalExecutions).toBe(initialStats.totalExecutions + 3);
			expect(finalStats.successRate).toBeGreaterThan(0);
			expect(finalStats.averageProcessingTime).toBeGreaterThan(0);
		});
	});
});
