/**
 * Chaos Engineering Tests
 *
 * Tests for failure scenarios, recovery testing, resilience validation, and disaster recovery.
 */

import { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach, vi } from 'vitest';
import { WorkerService } from '../../core/WorkerService';
import DomainLockManager from '../../locking/DomainLockManager';
import type { DomainJobType } from '../../types/WorkerTypes';
import { testConfig, createMockDomainJob } from '../setup';

describe('Chaos Engineering Tests', () =>
{
	let workerService: WorkerService;

	beforeAll(async () =>
	{
		// Setup test environment
		process.env.NODE_ENV = 'test';
		process.env.WORKER_INSTANCE_ID = 'chaos-test-worker';
		process.env.REDIS_URL = testConfig.redisUrl;
		process.env.MAX_CONCURRENT_TASKS = '3';
	});

	beforeEach(async () =>
	{
		workerService = new WorkerService();
		await workerService.initialize();
	});

	afterEach(async () =>
	{
		if (workerService.isServiceRunning())
		{
			await workerService.stop();
		}
		if (workerService.isServiceInitialized())
		{
			await workerService.shutdown();
		}
	});

	describe('Database Failure Scenarios', () =>
	{
		it('should handle ScyllaDB connection loss during processing', async () =>
		{
			const domain = 'scylla-failure-test.com';
			const job = createMockDomainJob(domain);

			// Mock ScyllaDB connection failure during processing
			const databaseManager = workerService.getDatabaseManager();
			let callCount = 0;

			vi.spyOn(databaseManager, 'getScyllaClient').mockImplementation(() =>
			{
				callCount++;
				if (callCount === 2) // Fail on second call (during processing)
				{
					throw new Error('ScyllaDB connection lost');
				}
				return databaseManager.getScyllaClient();
			});

			const result = await workerService.processJob(job);

			// Should handle the failure gracefully
			expect(['success', 'partial', 'failure']).toContain(result.status);

			if (result.status === 'failure')
			{
				expect(result.errors.some(e => e.message.includes('ScyllaDB'))).toBe(true);
			}
		});

		it('should handle MariaDB deadlock scenarios', async () =>
		{
			const domain = 'maria-deadlock-test.com';
			const job = createMockDomainJob(domain);

			// Mock MariaDB deadlock error
			const databaseManager = workerService.getDatabaseManager();
			vi.spyOn(databaseManager, 'getMariaClient').mockImplementation(() =>
			{
				const mockClient = {
					execute: vi.fn().mockRejectedValue(new Error('Deadlock found when trying to get lock')),
					beginTransaction: vi.fn(),
					commit: vi.fn(),
					rollback: vi.fn(),
				};
				return mockClient as any;
			});

			const result = await workerService.processJob(job);

			// Should retry and eventually handle the deadlock
			expect(result.status).toBe('failure');
			expect(result.errors.some(e => e.message.includes('Deadlock'))).toBe(true);
		});

		it('should handle Redis cluster failover', async () =>
		{
			const domain = 'redis-failover-test.com';
			const job = createMockDomainJob(domain);

			// Mock Redis cluster failover
			const lockManager = workerService.getLockManager();
			let failoverCount = 0;

			const originalAcquireLock = lockManager.acquireLock.bind(lockManager);
			vi.spyOn(lockManager, 'acquireLock').mockImplementation(async (domain, ttl) =>
			{
				failoverCount++;
				if (failoverCount <= 2)
				{
					throw new Error('CLUSTERDOWN Hash slot not served');
				}
				return originalAcquireLock(domain, ttl);
			});

			const result = await workerService.processJob(job);

			// Should recover after cluster stabilizes
			expect(result.status).toBe('success');
			expect(failoverCount).toBeGreaterThan(2);
		});

		it('should handle Manticore search index corruption', async () =>
		{
			const domain = 'manticore-corruption-test.com';
			const job = createMockDomainJob(domain);

			// Mock Manticore index corruption
			const databaseManager = workerService.getDatabaseManager();
			vi.spyOn(databaseManager, 'getManticoreClient').mockImplementation(() =>
			{
				const mockClient = {
					query: vi.fn().mockRejectedValue(new Error('index corrupted')),
					insert: vi.fn().mockRejectedValue(new Error('index corrupted')),
				};
				return mockClient as any;
			});

			const result = await workerService.processJob(job);

			// Should complete crawling and ranking, but fail indexing
			expect(result.status).toBe('partial');
			expect(result.crawlingResult.status).toBe('success');
			expect(result.rankingResult.status).toBe('success');
			expect(result.indexingResult.status).toBe('failure');
		});
	});

	describe('Network Partition Scenarios', () =>
	{
		it('should handle network partition between worker and databases', async () =>
		{
			const domain = 'network-partition-test.com';
			const job = createMockDomainJob(domain);

			// Mock network partition (all database connections fail)
			const databaseManager = workerService.getDatabaseManager();

			vi.spyOn(databaseManager, 'getScyllaClient').mockImplementation(() =>
			{
				throw new Error('ENOTFOUND');
			});

			vi.spyOn(databaseManager, 'getMariaClient').mockImplementation(() =>
			{
				throw new Error('ECONNREFUSED');
			});

			const result = await workerService.processJob(job);

			expect(result.status).toBe('failure');
			expect(result.errors.some(e =>
				e.message.includes('ENOTFOUND') || e.message.includes('ECONNREFUSED'))).toBe(true);
		});

		it('should handle intermittent network connectivity', async () =>
		{
			const domain = 'intermittent-network-test.com';
			const job = createMockDomainJob(domain);

			// Mock intermittent network issues
			let networkCallCount = 0;
			const mockAxios = {
				get: vi.fn().mockImplementation(() =>
				{
					networkCallCount++;
					if (networkCallCount % 3 === 0) // Fail every 3rd call
					{
						throw new Error('ETIMEDOUT');
					}
					return Promise.resolve({
						status: 200,
						data: '<html><body>Test content</body></html>',
					});
				}),
			};

			// Mock axios for HTTP requests
			vi.doMock('axios', () => ({ default: mockAxios }));

			const result = await workerService.processJob(job);

			// Should eventually succeed with retries
			expect(result.status).toBe('success');
			expect(networkCallCount).toBeGreaterThan(3); // Should have retried
		});

		it('should handle DNS resolution failures', async () =>
		{
			const domain = 'dns-failure-test.com';
			const job = createMockDomainJob(domain);

			// Mock DNS resolution failure
			const mockDns = {
				resolve4: vi.fn().mockRejectedValue(new Error('ENOTFOUND')),
				resolve6: vi.fn().mockRejectedValue(new Error('ENOTFOUND')),
				resolveMx: vi.fn().mockRejectedValue(new Error('ENOTFOUND')),
			};

			vi.doMock('dns', () => ({ promises: mockDns }));

			const result = await workerService.processJob(job);

			// Should fail DNS analysis but continue with other modules
			expect(result.status).toBe('partial');
			expect(result.crawlingResult.failedModules).toContain('dns');
		});
	});

	describe('Resource Exhaustion Scenarios', () =>
	{
		it('should handle memory exhaustion gracefully', async () =>
		{
			const domain = 'memory-exhaustion-test.com';
			const job = createMockDomainJob(domain);

			// Mock memory exhaustion by creating large objects
			const originalProcessJob = workerService.processJob.bind(workerService);
			vi.spyOn(workerService, 'processJob').mockImplementation(async (job) =>
			{
				// Simulate memory pressure
				const largeArray = new Array(1000000).fill('memory-pressure-test');

				try
				{
					return await originalProcessJob(job);
				}
				finally
				{
					// Clean up
					largeArray.length = 0;
				}
			});

			const result = await workerService.processJob(job);

			// Should handle memory pressure without crashing
			expect(['success', 'partial', 'failure']).toContain(result.status);
		});

		it('should handle file descriptor exhaustion', async () =>
		{
			const jobs = Array.from({ length: 100 }, (_, i) =>
				createMockDomainJob(`fd-exhaustion-${i}.com`));

			// Process many jobs simultaneously to exhaust file descriptors
			const results = await Promise.allSettled(
				jobs.map(job => workerService.processJob(job))
			);

			// Some should succeed, some might fail due to FD exhaustion
			const successful = results.filter(r =>
				r.status === 'fulfilled' && r.value.status === 'success');
			const failed = results.filter(r =>
				r.status === 'rejected' ||
				(r.status === 'fulfilled' && r.value.status === 'failure'));

			expect(successful.length).toBeGreaterThan(0);

			// If any failed due to FD exhaustion, verify error message
			failed.forEach((result) =>
			{
				if (result.status === 'fulfilled')
				{
					const hasResourceError = result.value.errors.some(e =>
						e.message.includes('EMFILE') ||
						e.message.includes('ENFILE') ||
						e.message.includes('too many open files'));
					// Note: This might not always trigger in test environment
				}
			});
		});

		it('should handle CPU starvation scenarios', async () =>
		{
			const domain = 'cpu-starvation-test.com';
			const job = createMockDomainJob(domain);

			// Mock CPU-intensive operation
			const cpuIntensiveTask = () =>
			{
				const start = Date.now();
				while (Date.now() - start < 1000) // Busy wait for 1 second
				{
					Math.random();
				}
			};

			// Start CPU-intensive background task
			const backgroundTask = setInterval(cpuIntensiveTask, 100);

			try
			{
				const startTime = Date.now();
				const result = await workerService.processJob(job);
				const endTime = Date.now();

				// Should complete despite CPU pressure (might be slower)
				expect(result.status).toBe('success');
				expect(endTime - startTime).toBeGreaterThan(1000); // Should take longer
			}
			finally
			{
				clearInterval(backgroundTask);
			}
		});
	});

	describe('External Service Failures', () =>
	{
		it('should handle Browserless service unavailability', async () =>
		{
			const domain = 'browserless-failure-test.com';
			const job = createMockDomainJob(domain, { crawlType: 'full' });

			// Mock Browserless service failure
			const mockBrowserless = {
				screenshot: vi.fn().mockRejectedValue(new Error('Service Unavailable')),
				performance: vi.fn().mockRejectedValue(new Error('Service Unavailable')),
			};

			const result = await workerService.processJob(job);

			// Should complete other modules but fail screenshot/performance
			expect(result.status).toBe('partial');
			expect(result.crawlingResult.failedModules).toContain('screenshot');
			expect(result.crawlingResult.failedModules).toContain('performance');
		});

		it('should handle image proxy service failures', async () =>
		{
			const domain = 'image-proxy-failure-test.com';
			const job = createMockDomainJob(domain, { crawlType: 'full' });

			// Mock image proxy failure
			const mockImageProxy = {
				optimize: vi.fn().mockRejectedValue(new Error('Proxy timeout')),
			};

			const result = await workerService.processJob(job);

			// Should complete but with degraded image processing
			expect(['success', 'partial']).toContain(result.status);
		});

		it('should handle AI service rate limiting', async () =>
		{
			const domain = 'ai-rate-limit-test.com';
			const job = createMockDomainJob(domain);

			// Mock AI service rate limiting
			const aiProvider = workerService.getAIProvider();
			vi.spyOn(aiProvider, 'generateDescription').mockRejectedValue(
				new Error('Rate limit exceeded')
			);

			const result = await workerService.processJob(job);

			// Should complete without AI-generated content
			expect(['success', 'partial']).toContain(result.status);
		});
	});

	describe('Data Corruption Scenarios', () =>
	{
		it('should handle corrupted configuration files', async () =>
		{
			const domain = 'config-corruption-test.com';
			const job = createMockDomainJob(domain);

			// Mock corrupted configuration
			const configManager = workerService.getConfigManager();
			vi.spyOn(configManager, 'getConfig').mockImplementation(() =>
			{
				throw new Error('Configuration file corrupted');
			});

			const result = await workerService.processJob(job);

			// Should fall back to default configuration
			expect(result.status).toBe('failure');
			expect(result.errors.some(e => e.message.includes('Configuration'))).toBe(true);
		});

		it('should handle corrupted job data', async () =>
		{
			// Create job with corrupted/invalid data
			const corruptedJob = {
				id: 'corrupted-job-123',
				domain: null, // Invalid domain
				priority: 'invalid-priority' as any,
				crawlType: 'invalid-type' as any,
				scheduledAt: 'invalid-date' as any,
				createdAt: new Date(),
				tasks: [],
				retryCount: -1, // Invalid retry count
				maxRetries: 'invalid' as any,
				requestedBy: '',
				metadata: null,
			} as DomainJobType;

			const result = await workerService.processJob(corruptedJob);

			expect(result.status).toBe('failure');
			expect(result.errors.some(e =>
				e.message.includes('Invalid') || e.message.includes('validation'))).toBe(true);
		});

		it('should handle schema version mismatches', async () =>
		{
			const domain = 'schema-mismatch-test.com';
			const job = createMockDomainJob(domain);

			// Mock schema version mismatch
			const migrationManager = workerService.getDatabaseManager().getMigrationManager();
			vi.spyOn(migrationManager, 'checkSchemaVersion').mockResolvedValue(false);
			vi.spyOn(migrationManager, 'runMigrations').mockRejectedValue(
				new Error('Migration failed: incompatible schema version')
			);

			const result = await workerService.processJob(job);

			expect(result.status).toBe('failure');
			expect(result.errors.some(e => e.message.includes('schema'))).toBe(true);
		});
	});

	describe('Timing and Race Condition Scenarios', () =>
	{
		it('should handle lock timeout scenarios', async () =>
		{
			const domain = 'lock-timeout-test.com';
			const job = createMockDomainJob(domain);

			// Mock lock acquisition timeout
			const lockManager = workerService.getLockManager();
			vi.spyOn(lockManager, 'acquireLock').mockImplementation(async () =>
			{
				// Simulate timeout
				await new Promise(resolve => setTimeout(resolve, 10000));
				return { success: false, error: 'Lock acquisition timeout' };
			});

			const startTime = Date.now();
			const result = await workerService.processJob(job);
			const endTime = Date.now();

			expect(result.status).toBe('failure');
			expect(result.errors.some(e => e.message.includes('timeout'))).toBe(true);
			expect(endTime - startTime).toBeLessThan(15000); // Should timeout before 15s
		});

		it('should handle concurrent job modifications', async () =>
		{
			const domain = 'concurrent-modification-test.com';
			const job1 = createMockDomainJob(domain);
			const job2 = createMockDomainJob(domain);

			// Start both jobs simultaneously
			const [result1, result2] = await Promise.allSettled([
				workerService.processJob(job1),
				workerService.processJob(job2),
			]);

			// One should succeed, one should fail due to lock conflict
			const results = [result1, result2];
			const successful = results.filter(r =>
				r.status === 'fulfilled' && r.value.status === 'success');
			const failed = results.filter(r =>
				r.status === 'fulfilled' && r.value.status === 'failure');

			expect(successful).toHaveLength(1);
			expect(failed).toHaveLength(1);
		});

		it('should handle worker shutdown during processing', async () =>
		{
			const domain = 'shutdown-during-processing-test.com';
			const job = createMockDomainJob(domain);

			// Start job processing
			const jobPromise = workerService.processJob(job);

			// Shutdown worker after short delay
			setTimeout(async () =>
			{
				await workerService.stop();
			}, 500);

			const result = await jobPromise;

			// Should handle shutdown gracefully
			expect(['success', 'failure']).toContain(result.status);

			if (result.status === 'failure')
			{
				expect(result.errors.some(e =>
					e.message.includes('shutdown') ||
					e.message.includes('cancelled') ||
					e.message.includes('interrupted'))).toBe(true);
			}
		});
	});

	describe('Recovery and Resilience Validation', () =>
	{
		it('should recover from temporary failures automatically', async () =>
		{
			const domain = 'auto-recovery-test.com';
			const job = createMockDomainJob(domain);

			// Mock temporary failure that resolves itself
			let failureCount = 0;
			const databaseManager = workerService.getDatabaseManager();
			const originalGetScyllaClient = databaseManager.getScyllaClient.bind(databaseManager);

			vi.spyOn(databaseManager, 'getScyllaClient').mockImplementation(() =>
			{
				failureCount++;
				if (failureCount <= 2)
				{
					throw new Error('Temporary failure');
				}
				return originalGetScyllaClient();
			});

			const result = await workerService.processJob(job);

			// Should recover and succeed
			expect(result.status).toBe('success');
			expect(failureCount).toBeGreaterThan(2);
		});

		it('should maintain data consistency during failures', async () =>
		{
			const domain = 'consistency-during-failure-test.com';
			const job = createMockDomainJob(domain);

			// Mock failure during indexing phase
			const indexingService = workerService.getIndexingService();
			vi.spyOn(indexingService, 'indexDomain').mockRejectedValue(
				new Error('Indexing failed')
			);

			const result = await workerService.processJob(job);

			// Should complete crawling and ranking but fail indexing
			expect(result.status).toBe('partial');
			expect(result.crawlingResult.status).toBe('success');
			expect(result.rankingResult.status).toBe('success');
			expect(result.indexingResult.status).toBe('failure');

			// Data should be consistent (no partial writes)
			expect(result.crawlingResult.validation.isComplete).toBe(true);
		});

		it('should handle cascading failures gracefully', async () =>
		{
			const domain = 'cascading-failure-test.com';
			const job = createMockDomainJob(domain);

			// Mock cascading failures (one failure triggers others)
			const databaseManager = workerService.getDatabaseManager();

			vi.spyOn(databaseManager, 'getScyllaClient').mockImplementation(() =>
			{
				throw new Error('ScyllaDB failure');
			});

			vi.spyOn(databaseManager, 'getMariaClient').mockImplementation(() =>
			{
				throw new Error('MariaDB failure (cascade from ScyllaDB)');
			});

			const result = await workerService.processJob(job);

			expect(result.status).toBe('failure');
			expect(result.errors.length).toBeGreaterThan(1); // Multiple failures
			expect(result.errors.some(e => e.message.includes('ScyllaDB'))).toBe(true);
			expect(result.errors.some(e => e.message.includes('MariaDB'))).toBe(true);
		});

		it('should validate system health after chaos events', async () =>
		{
			// Simulate various chaos events
			const chaosEvents = [
				() => { throw new Error('Random failure 1') },
				() => { throw new Error('Random failure 2') },
				() => { throw new Error('Random failure 3') },
			];

			// Apply random chaos
			const randomChaos = chaosEvents[Math.floor(Math.random() * chaosEvents.length)];

			try
			{
				randomChaos();
			}
			catch (error)
			{
				// Expected chaos event
			}

			// Verify system health after chaos
			const health = await workerService.getHealth();

			// Core systems should still be healthy
			expect(health.workerId).toBeDefined();
			expect(health.status).toBe('healthy');
			expect(health.databases).toBeDefined();
		});
	});

	describe('Disaster Recovery Scenarios', () =>
	{
		it('should handle complete database cluster failure', async () =>
		{
			const domain = 'cluster-failure-test.com';
			const job = createMockDomainJob(domain);

			// Mock complete database cluster failure
			const databaseManager = workerService.getDatabaseManager();

			vi.spyOn(databaseManager, 'getScyllaClient').mockImplementation(() =>
			{
				throw new Error('Cluster unreachable');
			});

			vi.spyOn(databaseManager, 'getMariaClient').mockImplementation(() =>
			{
				throw new Error('Cluster unreachable');
			});

			vi.spyOn(databaseManager, 'getRedisClient').mockImplementation(() =>
			{
				throw new Error('Cluster unreachable');
			});

			const result = await workerService.processJob(job);

			expect(result.status).toBe('failure');
			expect(result.errors.some(e => e.message.includes('Cluster unreachable'))).toBe(true);
		});

		it('should handle worker isolation scenarios', async () =>
		{
			const domain = 'worker-isolation-test.com';
			const job = createMockDomainJob(domain);

			// Mock worker isolation (can't communicate with other services)
			const lockManager = workerService.getLockManager();
			vi.spyOn(lockManager, 'acquireLock').mockRejectedValue(
				new Error('Network unreachable')
			);

			const result = await workerService.processJob(job);

			expect(result.status).toBe('failure');
			expect(result.errors.some(e => e.message.includes('Network unreachable'))).toBe(true);
		});

		it('should validate backup and restore procedures', async () =>
		{
			// This test would validate that backup/restore procedures work
			// In a real scenario, this would test actual backup/restore operations

			const domain = 'backup-restore-test.com';
			const job = createMockDomainJob(domain);

			// Simulate successful processing to create data
			const result = await workerService.processJob(job);
			expect(result.status).toBe('success');

			// Mock backup operation
			const backupManager = workerService.getDatabaseManager().getBackupManager();
			const backupResult = await backupManager.createBackup('test-backup');
			expect(backupResult.success).toBe(true);

			// Mock restore operation
			const restoreResult = await backupManager.restoreFromBackup('test-backup');
			expect(restoreResult.success).toBe(true);
		});
	});
});
