/**
 * Comprehensive Test Runner
 *
 * Runs all test suites for the worker service with proper setup and teardown.
 */

import { execSync } from 'child_process';
import { existsSync, mkdirSync } from 'fs';
import { join } from 'path';

interface TestSuiteResult
{
	name: string;
	passed: boolean;
	duration: number;
	coverage?: number;
	errors?: string[];
}

interface TestRunResult
{
	totalSuites: number;
	passedSuites: number;
	failedSuites: number;
	totalDuration: number;
	overallCoverage: number;
	results: TestSuiteResult[];
}

class ComprehensiveTestRunner
{
	private testSuites = [
		// Unit Tests
		{
			name: 'Core WorkerService',
			pattern: 'src/__tests__/WorkerService.test.ts',
			timeout: 30000,
		},
		{
			name: 'Configuration Management',
			pattern: 'src/config/__tests__/*.test.ts',
			timeout: 15000,
		},
		{
			name: 'Error Handling',
			pattern: 'src/errors/__tests__/*.test.ts',
			timeout: 20000,
		},
		{
			name: 'Pipeline Processing',
			pattern: 'src/pipeline/__tests__/*.test.ts',
			timeout: 45000,
		},
		{
			name: 'Monitoring Services',
			pattern: 'src/monitoring/__tests__/*.test.ts',
			timeout: 25000,
		},
		{
			name: 'Scheduler Services',
			pattern: 'src/scheduler/__tests__/*.test.ts',
			timeout: 30000,
		},
		{
			name: 'Indexing Services',
			pattern: 'src/indexing/__tests__/*.test.ts',
			timeout: 35000,
		},
		{
			name: 'Ranking System',
			pattern: 'src/ranking/__tests__/*.test.ts',
			timeout: 40000,
		},
		{
			name: 'Validation System',
			pattern: 'src/validation/__tests__/*.test.ts',
			timeout: 25000,
		},
		{
			name: 'Queue Management',
			pattern: 'src/queue/__tests__/*.test.ts',
			timeout: 30000,
		},
		{
			name: 'Domain Locking',
			pattern: 'src/locking/__tests__/*.test.ts',
			timeout: 35000,
		},
		{
			name: 'Crawler Analyzers',
			pattern: 'src/crawler/analyzers/__tests__/*.test.ts',
			timeout: 50000,
		},
		{
			name: 'Crawler Core',
			pattern: 'src/crawler/core/__tests__/*.test.ts',
			timeout: 40000,
		},
		{
			name: 'AI Services',
			pattern: 'src/ai/__tests__/*.test.ts',
			timeout: 35000,
		},

		// Integration Tests
		{
			name: 'Pipeline Integration',
			pattern: 'src/__tests__/integration/pipeline-integration.test.ts',
			timeout: 120000,
		},
		{
			name: 'Database Integration',
			pattern: 'src/__tests__/database/database-integration.test.ts',
			timeout: 90000,
		},

		// Performance Tests
		{
			name: 'Concurrency & Performance',
			pattern: 'src/__tests__/performance/concurrency.test.ts',
			timeout: 180000,
		},

		// Chaos Engineering Tests
		{
			name: 'Chaos Engineering',
			pattern: 'src/__tests__/chaos/chaos-engineering.test.ts',
			timeout: 300000,
		},
	];

	private results: TestSuiteResult[] = [];

	async runAllTests(): Promise<TestRunResult>
	{
		console.log('🚀 Starting Comprehensive Test Suite for Worker Service');
		console.log('=' .repeat(80));

		const startTime = Date.now();

		// Setup test environment
		await this.setupTestEnvironment();

		// Run each test suite
		for (const suite of this.testSuites)
		{
			await this.runTestSuite(suite);
		}

		// Generate coverage report
		const overallCoverage = await this.generateCoverageReport();

		const endTime = Date.now();
		const totalDuration = endTime - startTime;

		const result: TestRunResult = {
			totalSuites: this.testSuites.length,
			passedSuites: this.results.filter(r => r.passed).length,
			failedSuites: this.results.filter(r => !r.passed).length,
			totalDuration,
			overallCoverage,
			results: this.results,
		};

		this.printSummary(result);
		return result;
	}

	private async setupTestEnvironment(): Promise<void>
	{
		console.log('🔧 Setting up test environment...');

		// Create test directories
		const testDirs = [
			'coverage',
			'test-results',
			'test-logs',
		];

		testDirs.forEach(dir =>
		{
			const fullPath = join(process.cwd(), dir);
			if (!existsSync(fullPath))
			{
				mkdirSync(fullPath, { recursive: true });
			}
		});

		// Set test environment variables
		process.env.NODE_ENV = 'test';
		process.env.VITEST_REPORTER = 'verbose';
		process.env.VITEST_COVERAGE = 'true';

		console.log('✅ Test environment setup complete');
	}

	private async runTestSuite(suite: { name: string; pattern: string; timeout: number }): Promise<void>
	{
		console.log(`\n📋 Running: ${suite.name}`);
		console.log('-'.repeat(50));

		const startTime = Date.now();
		let passed = false;
		const errors: string[] = [];

		try
		{
			// Run vitest with specific pattern
			const command = [
				'npx vitest run',
				`--testTimeout=${suite.timeout}`,
				'--reporter=verbose',
				'--coverage.enabled=true',
				'--coverage.reporter=json',
				`--coverage.reportsDirectory=coverage/${suite.name.replace(/\s+/g, '-').toLowerCase()}`,
				suite.pattern,
			].join(' ');

			console.log(`Command: ${command}`);

			const output = execSync(command, {
				encoding: 'utf8',
				stdio: 'pipe',
				timeout: suite.timeout + 30000, // Add buffer time
			});

			console.log(output);
			passed = true;
		}
		catch (error: any)
		{
			console.error(`❌ Test suite failed: ${suite.name}`);
			console.error(error.message);

			if (error.stdout)
			{
				console.log('STDOUT:', error.stdout);
			}
			if (error.stderr)
			{
				console.error('STDERR:', error.stderr);
			}

			errors.push(error.message);
			passed = false;
		}

		const endTime = Date.now();
		const duration = endTime - startTime;

		const result: TestSuiteResult = {
			name: suite.name,
			passed,
			duration,
			errors: errors.length > 0 ? errors : undefined,
		};

		this.results.push(result);

		const status = passed ? '✅ PASSED' : '❌ FAILED';
		const durationStr = `${(duration / 1000).toFixed(2)}s`;
		console.log(`${status} - ${suite.name} (${durationStr})`);
	}

	private async generateCoverageReport(): Promise<number>
	{
		console.log('\n📊 Generating overall coverage report...');

		try
		{
			// Merge coverage reports from all test suites
			const mergeCommand = [
				'npx nyc merge',
				'coverage',
				'coverage/merged-coverage.json',
			].join(' ');

			execSync(mergeCommand, { stdio: 'pipe' });

			// Generate final coverage report
			const reportCommand = [
				'npx nyc report',
				'--reporter=text',
				'--reporter=html',
				'--reporter=lcov',
				'--temp-dir=coverage',
				'--report-dir=coverage/final',
			].join(' ');

			const coverageOutput = execSync(reportCommand, {
				encoding: 'utf8',
				stdio: 'pipe',
			});

			console.log(coverageOutput);

			// Extract coverage percentage from output
			const coverageMatch = coverageOutput.match(/All files\s+\|\s+(\d+\.?\d*)/);
			const coverage = coverageMatch ? parseFloat(coverageMatch[1]) : 0;

			console.log(`📈 Overall Coverage: ${coverage}%`);
			return coverage;
		}
		catch (error)
		{
			console.warn('⚠️  Could not generate coverage report:', error);
			return 0;
		}
	}

	private printSummary(result: TestRunResult): void
	{
		console.log('\n' + '='.repeat(80));
		console.log('📋 TEST SUITE SUMMARY');
		console.log('='.repeat(80));

		console.log(`Total Test Suites: ${result.totalSuites}`);
		console.log(`Passed: ${result.passedSuites} ✅`);
		console.log(`Failed: ${result.failedSuites} ❌`);
		console.log(`Success Rate: ${((result.passedSuites / result.totalSuites) * 100).toFixed(1)}%`);
		console.log(`Total Duration: ${(result.totalDuration / 1000 / 60).toFixed(2)} minutes`);
		console.log(`Overall Coverage: ${result.overallCoverage.toFixed(1)}%`);

		if (result.failedSuites > 0)
		{
			console.log('\n❌ FAILED TEST SUITES:');
			result.results
				.filter(r => !r.passed)
				.forEach(r =>
				{
					console.log(`  - ${r.name} (${(r.duration / 1000).toFixed(2)}s)`);
					if (r.errors)
					{
						r.errors.forEach(error =>
						{
							console.log(`    Error: ${error}`);
						});
					}
				});
		}

		console.log('\n✅ PASSED TEST SUITES:');
		result.results
			.filter(r => r.passed)
			.forEach(r =>
			{
				console.log(`  - ${r.name} (${(r.duration / 1000).toFixed(2)}s)`);
			});

		// Performance analysis
		console.log('\n⚡ PERFORMANCE ANALYSIS:');
		const sortedByDuration = [...result.results].sort((a, b) => b.duration - a.duration);
		console.log('Slowest test suites:');
		sortedByDuration.slice(0, 5).forEach((r, i) =>
		{
			console.log(`  ${i + 1}. ${r.name}: ${(r.duration / 1000).toFixed(2)}s`);
		});

		// Recommendations
		console.log('\n💡 RECOMMENDATIONS:');

		if (result.overallCoverage < 80)
		{
			console.log('  - Increase test coverage (target: 80%+)');
		}

		if (result.failedSuites > 0)
		{
			console.log('  - Fix failing test suites before deployment');
		}

		const slowSuites = result.results.filter(r => r.duration > 60000);
		if (slowSuites.length > 0)
		{
			console.log('  - Optimize slow test suites (>60s)');
		}

		console.log('\n' + '='.repeat(80));

		if (result.failedSuites === 0)
		{
			console.log('🎉 ALL TESTS PASSED! Ready for deployment.');
		}
		else
		{
			console.log('⚠️  Some tests failed. Please fix before deployment.');
		}

		console.log('='.repeat(80));
	}
}

// CLI execution
if (require.main === module)
{
	const runner = new ComprehensiveTestRunner();

	runner.runAllTests()
		.then(result =>
		{
			process.exit(result.failedSuites === 0 ? 0 : 1);
		})
		.catch(error =>
		{
			console.error('❌ Test runner failed:', error);
			process.exit(1);
		});
}

export { ComprehensiveTestRunner };
export type { TestRunResult, TestSuiteResult };
