/**
 * Worker Service Tests
 *
 * Basic tests for the main WorkerService class to verify the setup is working correctly.
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { WorkerService } from '../core/WorkerService';
import { testConfig, createMockDomainJob } from './setup';

describe('WorkerService', () =>
{
	let workerService: WorkerService;

	beforeEach(() =>
	{
		// Set test environment variables
		process.env.WORKER_INSTANCE_ID = 'test-worker-1';
		process.env.MAX_CONCURRENT_TASKS = '2';
		process.env.SCYLLA_HOSTS = testConfig.scyllaHosts.join(',');
		process.env.MARIA_HOST = testConfig.mariaHost;
		process.env.MARIA_PORT = testConfig.mariaPort.toString();
		process.env.REDIS_URL = testConfig.redisUrl;

		workerService = new WorkerService();
	});

	afterEach(async () =>
	{
		// Cleanup after each test
		if (workerService.isServiceRunning())
		{
			await workerService.stop();
		}

		if (workerService.isServiceInitialized())
		{
			await workerService.shutdown();
		}
	});

	describe('Configuration', () =>
	{
		it('should load configuration from environment variables', () =>
		{
			const config = workerService.getConfiguration();

			expect(config.workerId).toBe('test-worker-1');
			expect(config.maxConcurrentTasks).toBe(2);
			expect(config.scyllaHosts).toEqual(testConfig.scyllaHosts);
			expect(config.mariaHost).toBe(testConfig.mariaHost);
			expect(config.redisUrl).toBe(testConfig.redisUrl);
		});

		it('should use default values when environment variables are not set', () =>
		{
			// Clear environment variables
			delete process.env.WORKER_INSTANCE_ID;
			delete process.env.MAX_CONCURRENT_TASKS;

			const newWorkerService = new WorkerService();
			const config = newWorkerService.getConfiguration();

			expect(config.workerId).toBe('worker-unknown');
			expect(config.maxConcurrentTasks).toBe(10);
		});
	});

	describe('Lifecycle Management', () =>
	{
		it('should initialize successfully', async () =>
		{
			expect(workerService.isServiceInitialized()).toBe(false);

			await workerService.initialize();

			expect(workerService.isServiceInitialized()).toBe(true);
		});

		it('should start successfully after initialization', async () =>
		{
			await workerService.initialize();

			expect(workerService.isServiceRunning()).toBe(false);

			await workerService.start();

			expect(workerService.isServiceRunning()).toBe(true);
		});

		it('should stop successfully when running', async () =>
		{
			await workerService.initialize();
			await workerService.start();

			expect(workerService.isServiceRunning()).toBe(true);

			await workerService.stop();

			expect(workerService.isServiceRunning()).toBe(false);
		});

		it('should shutdown successfully', async () =>
		{
			await workerService.initialize();
			await workerService.start();
			await workerService.stop();

			expect(workerService.isServiceInitialized()).toBe(true);

			await workerService.shutdown();

			expect(workerService.isServiceInitialized()).toBe(false);
		});

		it('should not allow starting without initialization', async () =>
		{
			await expect(workerService.start()).rejects.toThrow(
				'Worker service must be initialized before starting'
			);
		});
	});

	describe('Health Monitoring', () =>
	{
		it('should return health status', async () =>
		{
			await workerService.initialize();

			const health = await workerService.getHealth();

			expect(health).toHaveProperty('workerId');
			expect(health).toHaveProperty('status');
			expect(health).toHaveProperty('uptime');
			expect(health).toHaveProperty('databases');
			expect(health).toHaveProperty('externalServices');

			expect(health.workerId).toBe('test-worker-1');
		});

		it('should report unhealthy status when not running', async () =>
		{
			const health = await workerService.getHealth();

			expect(health.status).toBe('unhealthy');
		});

		it('should report healthy status when running', async () =>
		{
			await workerService.initialize();
			await workerService.start();

			const health = await workerService.getHealth();

			expect(health.status).toBe('healthy');
		});
	});

	describe('Job Processing', () =>
	{
		it('should process a domain job successfully', async () =>
		{
			await workerService.initialize();

			const mockJob = createMockDomainJob('example.com');
			const result = await workerService.processJob(mockJob);

			expect(result).toHaveProperty('domain', 'example.com');
			expect(result).toHaveProperty('jobId', mockJob.id);
			expect(result).toHaveProperty('status', 'success');
			expect(result).toHaveProperty('crawlingResult');
			expect(result).toHaveProperty('rankingResult');
			expect(result).toHaveProperty('indexingResult');
		});
	});

	describe('Error Handling', () =>
	{
		it('should handle initialization errors gracefully', async () =>
		{
			// TODO: This will be implemented when we have actual database connections
			// For now, initialization should succeed with placeholder implementations

			await expect(workerService.initialize()).resolves.not.toThrow();
		});

		it('should handle multiple initialization calls gracefully', async () =>
		{
			await workerService.initialize();

			// Second initialization should not throw
			await expect(workerService.initialize()).resolves.not.toThrow();

			expect(workerService.isServiceInitialized()).toBe(true);
		});

		it('should handle multiple start calls gracefully', async () =>
		{
			await workerService.initialize();
			await workerService.start();

			// Second start should not throw
			await expect(workerService.start()).resolves.not.toThrow();

			expect(workerService.isServiceRunning()).toBe(true);
		});
	});
});
