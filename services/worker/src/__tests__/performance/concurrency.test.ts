/**
 * Concurrency and Performance Tests
 *
 * Tests for multi-worker coordination, domain locking, and performance under load.
 */

import { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach, vi } from 'vitest';
import { WorkerService } from '../../core/WorkerService';
import { DomainLockManager } from '../../locking/DomainLockManager';
import type { DomainJobType } from '../../types/WorkerTypes';
import { testConfig, createMockDomainJob } from '../setup';

describe('Concurrency and Performance Tests', () =>
{
	let workerServices: WorkerService[];
	let lockManagers: DomainLockManager[];

	beforeAll(async () =>
	{
		// Setup test environment for multiple workers
		process.env.NODE_ENV = 'test';
		process.env.REDIS_URL = testConfig.redisUrl;
		process.env.MAX_CONCURRENT_TASKS = '3';
	});

	beforeEach(async () =>
	{
		workerServices = [];
		lockManagers = [];
	});

	afterEach(async () =>
	{
		// Cleanup all workers
		for (const worker of workerServices)
		{
			if (worker.isServiceRunning())
			{
				await worker.stop();
			}
			if (worker.isServiceInitialized())
			{
				await worker.shutdown();
			}
		}

		for (const lockManager of lockManagers)
		{
			await lockManager.shutdown();
		}
	});

	describe('Multi-Worker Coordination', () =>
	{
		it('should prevent multiple workers from processing the same domain', async () =>
		{
			// Create two worker instances
			const worker1 = new WorkerService();
			const worker2 = new WorkerService();

			worker1.setWorkerId('test-worker-1');
			worker2.setWorkerId('test-worker-2');

			await worker1.initialize();
			await worker2.initialize();

			workerServices.push(worker1, worker2);

			const domain = 'concurrent-lock-test.com';
			const job1 = createMockDomainJob(domain, { priority: 'high' });
			const job2 = createMockDomainJob(domain, { priority: 'high' });

			// Start both jobs simultaneously
			const [result1, result2] = await Promise.allSettled([
				worker1.processJob(job1),
				worker2.processJob(job2),
			]);

			// One should succeed, one should fail due to lock conflict
			const results = [result1, result2];
			const successful = results.filter(r => r.status === 'fulfilled' && r.value.status === 'success');
			const failed = results.filter(r =>
				r.status === 'fulfilled' && r.value.status === 'failure' ||
				r.status === 'rejected');

			expect(successful).toHaveLength(1);
			expect(failed).toHaveLength(1);

			// Verify the failure is due to lock conflict
			if (result1.status === 'fulfilled' && result1.value.status === 'failure')
			{
				expect(result1.value.errors[0].message).toContain('lock');
			}
			if (result2.status === 'fulfilled' && result2.value.status === 'failure')
			{
				expect(result2.value.errors[0].message).toContain('lock');
			}
		});

		it('should distribute work evenly across multiple workers', async () =>
		{
			const numWorkers = 3;
			const numJobs = 15;

			// Create multiple workers
			for (let i = 0; i < numWorkers; i++)
			{
				const worker = new WorkerService();
				worker.setWorkerId(`test-worker-${i + 1}`);
				await worker.initialize();
				workerServices.push(worker);
			}

			// Create jobs for different domains
			const jobs = Array.from({ length: numJobs }, (_, i) =>
				createMockDomainJob(`distributed-test-${i}.com`, {
					priority: 'medium',
					crawlType: 'quick',
				}));

			// Distribute jobs across workers
			const workerPromises = jobs.map((job, index) =>
			{
				const workerIndex = index % numWorkers;
				return workerServices[workerIndex].processJob(job);
			});

			const results = await Promise.all(workerPromises);

			// All jobs should succeed
			expect(results).toHaveLength(numJobs);
			results.forEach((result) =>
			{
				expect(result.status).toBe('success');
			});

			// Verify work distribution
			const workerStats = workerServices.map(worker => worker.getStatistics());
			workerStats.forEach((stats) =>
			{
				expect(stats.totalExecutions).toBeGreaterThan(0);
				expect(stats.totalExecutions).toBeLessThanOrEqual(Math.ceil(numJobs / numWorkers) + 1);
			});
		});

		it('should handle worker failures gracefully', async () =>
		{
			// Create two workers
			const worker1 = new WorkerService();
			const worker2 = new WorkerService();

			worker1.setWorkerId('test-worker-1');
			worker2.setWorkerId('test-worker-2');

			await worker1.initialize();
			await worker2.initialize();

			workerServices.push(worker1, worker2);

			const domain = 'worker-failure-test.com';
			const job1 = createMockDomainJob(domain);
			const job2 = createMockDomainJob(domain);

			// Start first job
			const job1Promise = worker1.processJob(job1);

			// Simulate worker1 failure after it acquires the lock
			setTimeout(async () =>
			{
				await worker1.stop();
				await worker1.shutdown();
			}, 1000);

			// Start second job on worker2 (should wait for lock to be released)
			const job2Promise = worker2.processJob(job2);

			const [result1, result2] = await Promise.allSettled([job1Promise, job2Promise]);

			// One should fail due to worker shutdown, the other should eventually succeed
			const successful = [result1, result2].filter(r =>
				r.status === 'fulfilled' && r.value.status === 'success');
			expect(successful.length).toBeGreaterThanOrEqual(1);
		});
	});

	describe('Domain Locking Coordination', () =>
	{
		beforeEach(async () =>
		{
			// Create lock managers for testing
			for (let i = 0; i < 3; i++)
			{
				const lockManager = new DomainLockManager(testConfig.redisUrl, `test-worker-${i + 1}`);
				await lockManager.initialize();
				lockManagers.push(lockManager);
			}
		});

		it('should prevent race conditions in lock acquisition', async () =>
		{
			const domain = 'race-condition-test.com';
			const ttl = 300000;

			// Attempt to acquire the same lock simultaneously from multiple workers
			const lockPromises = lockManagers.map(manager =>
				manager.acquireLock(domain, ttl));

			const results = await Promise.all(lockPromises);

			// Only one should succeed
			const successful = results.filter(r => r.success);
			const failed = results.filter(r => !r.success);

			expect(successful).toHaveLength(1);
			expect(failed).toHaveLength(2);

			// Clean up the lock
			const successfulResult = successful[0];
			await lockManagers[0].releaseLock(domain, successfulResult.lockId!);
		});

		it('should handle lock renewal under contention', async () =>
		{
			const domain = 'renewal-contention-test.com';
			const ttl = 300000;

			// First worker acquires lock
			const lockResult = await lockManagers[0].acquireLock(domain, ttl);
			expect(lockResult.success).toBe(true);

			// Multiple workers try to renew (only owner should succeed)
			const renewalPromises = lockManagers.map(manager =>
				manager.renewLock(domain, lockResult.lockId!, ttl * 2));

			const renewalResults = await Promise.all(renewalPromises);

			// Only the owner should succeed
			const successfulRenewals = renewalResults.filter(r => r.success);
			expect(successfulRenewals).toHaveLength(1);

			// Clean up
			await lockManagers[0].releaseLock(domain, lockResult.lockId!);
		});

		it('should detect and clean up orphaned locks', async () =>
		{
			const domain = 'orphaned-lock-test.com';
			const ttl = 300000;

			// Acquire lock with first manager
			const lockResult = await lockManagers[0].acquireLock(domain, ttl);
			expect(lockResult.success).toBe(true);

			// Simulate worker death by shutting down the lock manager
			await lockManagers[0].shutdown();

			// Second manager should detect orphaned lock
			const isWorkerAlive = vi.fn().mockResolvedValue(false);
			const orphanedLocks = await lockManagers[1].findOrphanedLocks(isWorkerAlive);

			expect(orphanedLocks.length).toBeGreaterThan(0);
			expect(orphanedLocks.some(lock => lock.domain === domain)).toBe(true);

			// Clean up orphaned locks
			const cleanedCount = await lockManagers[1].forceReleaseOrphanedLocks([domain]);
			expect(cleanedCount).toBe(1);
		});

		it('should maintain lock statistics across workers', async () =>
		{
			const domains = ['stats-test-1.com', 'stats-test-2.com', 'stats-test-3.com'];
			const ttl = 300000;

			// Each worker acquires a different lock
			const lockPromises = domains.map((domain, index) =>
				lockManagers[index].acquireLock(domain, ttl));

			const lockResults = await Promise.all(lockPromises);

			// All should succeed
			lockResults.forEach((result) =>
			{
				expect(result.success).toBe(true);
			});

			// Check statistics from any manager
			const stats = await lockManagers[0].getLockStatistics();

			expect(stats.totalLocks).toBe(3);
			expect(stats.locksOwnedByThisWorker).toBe(1);
			expect(stats.locksOwnedByOthers).toBe(2);

			// Clean up all locks
			await Promise.all(
				lockResults.map((result, index) =>
					lockManagers[index].releaseLock(domains[index], result.lockId!))
			);
		});
	});

	describe('Performance Under Load', () =>
	{
		it('should maintain performance with high concurrency', async () =>
		{
			const numWorkers = 5;
			const jobsPerWorker = 10;
			const totalJobs = numWorkers * jobsPerWorker;

			// Create workers
			for (let i = 0; i < numWorkers; i++)
			{
				const worker = new WorkerService();
				worker.setWorkerId(`perf-worker-${i + 1}`);
				await worker.initialize();
				workerServices.push(worker);
			}

			// Create jobs
			const jobs = Array.from({ length: totalJobs }, (_, i) =>
				createMockDomainJob(`perf-test-${i}.com`, {
					crawlType: 'quick',
					priority: 'low',
				}));

			const startTime = Date.now();

			// Process all jobs concurrently
			const workerPromises = jobs.map((job, index) =>
			{
				const workerIndex = index % numWorkers;
				return workerServices[workerIndex].processJob(job);
			});

			const results = await Promise.all(workerPromises);
			const endTime = Date.now();

			// Verify all jobs completed successfully
			expect(results).toHaveLength(totalJobs);
			results.forEach((result) =>
			{
				expect(result.status).toBe('success');
			});

			// Performance benchmarks
			const totalTime = endTime - startTime;
			const averageTimePerJob = totalTime / totalJobs;

			expect(averageTimePerJob).toBeLessThan(2000); // 2 seconds per job max
			expect(totalTime).toBeLessThan(30000); // 30 seconds total max

			// Memory usage should be reasonable
			const memoryUsage = process.memoryUsage();
			expect(memoryUsage.heapUsed).toBeLessThan(1024 * 1024 * 1024); // 1GB max
		});

		it('should handle memory pressure gracefully', async () =>
		{
			const worker = new WorkerService();
			worker.setWorkerId('memory-pressure-worker');
			await worker.initialize();
			workerServices.push(worker);

			const initialMemory = process.memoryUsage().heapUsed;

			// Process many jobs to create memory pressure
			const jobs = Array.from({ length: 50 }, (_, i) =>
				createMockDomainJob(`memory-pressure-${i}.com`, {
					crawlType: 'quick',
				}));

			const results = await Promise.all(
				jobs.map(job => worker.processJob(job))
			);

			// All jobs should complete
			results.forEach((result) =>
			{
				expect(result.status).toBe('success');
			});

			// Force garbage collection if available
			if (global.gc)
			{
				global.gc();
			}

			const finalMemory = process.memoryUsage().heapUsed;
			const memoryIncrease = finalMemory - initialMemory;

			// Memory increase should be reasonable (less than 500MB)
			expect(memoryIncrease).toBeLessThan(500 * 1024 * 1024);
		});

		it('should maintain throughput under sustained load', async () =>
		{
			const worker = new WorkerService();
			worker.setWorkerId('throughput-worker');
			await worker.initialize();
			workerServices.push(worker);

			const batchSize = 10;
			const numBatches = 5;
			const throughputResults: number[] = [];

			// Process multiple batches and measure throughput
			for (let batch = 0; batch < numBatches; batch++)
			{
				const jobs = Array.from({ length: batchSize }, (_, i) =>
					createMockDomainJob(`throughput-batch-${batch}-${i}.com`, {
						crawlType: 'quick',
					}));

				const batchStartTime = Date.now();
				const results = await Promise.all(
					jobs.map(job => worker.processJob(job))
				);
				const batchEndTime = Date.now();

				// Verify batch success
				results.forEach((result) =>
				{
					expect(result.status).toBe('success');
				});

				const batchTime = batchEndTime - batchStartTime;
				const throughput = batchSize / (batchTime / 1000); // jobs per second
				throughputResults.push(throughput);
			}

			// Throughput should remain consistent across batches
			const averageThroughput = throughputResults.reduce((a, b) => a + b) / throughputResults.length;
			const throughputVariance = throughputResults.reduce((variance, throughput) =>
				variance + (throughput - averageThroughput)**2, 0) / throughputResults.length;

			expect(averageThroughput).toBeGreaterThan(1); // At least 1 job per second
			expect(throughputVariance).toBeLessThan(averageThroughput * 0.5); // Low variance
		});
	});

	describe('Error Recovery and Resilience', () =>
	{
		it('should recover from Redis connection failures', async () =>
		{
			const worker = new WorkerService();
			worker.setWorkerId('redis-failure-worker');
			await worker.initialize();
			workerServices.push(worker);

			// Mock Redis connection failure
			const lockManager = worker.getLockManager();
			const originalAcquireLock = lockManager.acquireLock.bind(lockManager);

			let failureCount = 0;
			vi.spyOn(lockManager, 'acquireLock').mockImplementation(async (domain, ttl) =>
			{
				failureCount++;
				if (failureCount <= 3)
				{
					throw new Error('Redis connection failed');
				}
				return originalAcquireLock(domain, ttl);
			});

			const job = createMockDomainJob('redis-failure-test.com');
			const result = await worker.processJob(job);

			// Should eventually succeed after retries
			expect(result.status).toBe('success');
			expect(failureCount).toBeGreaterThan(3);
		});

		it('should handle database connection pool exhaustion', async () =>
		{
			const worker = new WorkerService();
			worker.setWorkerId('db-pool-worker');
			await worker.initialize();
			workerServices.push(worker);

			// Create many concurrent jobs to exhaust connection pool
			const jobs = Array.from({ length: 20 }, (_, i) =>
				createMockDomainJob(`db-pool-test-${i}.com`));

			const results = await Promise.all(
				jobs.map(job => worker.processJob(job))
			);

			// Most should succeed, some might fail due to pool exhaustion
			const successful = results.filter(r => r.status === 'success');
			const failed = results.filter(r => r.status === 'failure');

			expect(successful.length).toBeGreaterThan(jobs.length * 0.7); // At least 70% success

			// Failed jobs should have appropriate error messages
			failed.forEach((result) =>
			{
				expect(result.errors.some(e =>
					e.message.includes('connection') ||
					e.message.includes('pool') ||
					e.message.includes('timeout'))).toBe(true);
			});
		});

		it('should implement circuit breaker for external services', async () =>
		{
			const worker = new WorkerService();
			worker.setWorkerId('circuit-breaker-worker');
			await worker.initialize();
			workerServices.push(worker);

			// Mock external service failures
			const mockExternalService = vi.fn().mockRejectedValue(new Error('Service unavailable'));

			// Process multiple jobs to trigger circuit breaker
			const jobs = Array.from({ length: 10 }, (_, i) =>
				createMockDomainJob(`circuit-breaker-${i}.com`));

			const results = await Promise.all(
				jobs.map(job => worker.processJob(job))
			);

			// Early jobs should fail with service errors
			// Later jobs should fail with circuit breaker errors
			const serviceErrors = results.filter(r =>
				r.errors.some(e => e.message.includes('Service unavailable')));
			const circuitBreakerErrors = results.filter(r =>
				r.errors.some(e => e.message.includes('circuit breaker')));

			expect(serviceErrors.length).toBeGreaterThan(0);
			expect(circuitBreakerErrors.length).toBeGreaterThan(0);
		});
	});

	describe('Resource Management', () =>
	{
		it('should limit concurrent executions per worker', async () =>
		{
			const worker = new WorkerService();
			worker.setWorkerId('concurrency-limit-worker');
			worker.setMaxConcurrentTasks(2); // Limit to 2 concurrent tasks
			await worker.initialize();
			workerServices.push(worker);

			const jobs = Array.from({ length: 5 }, (_, i) =>
				createMockDomainJob(`concurrency-limit-${i}.com`));

			const startTime = Date.now();
			const results = await Promise.all(
				jobs.map(job => worker.processJob(job))
			);
			const endTime = Date.now();

			// All jobs should succeed
			results.forEach((result) =>
			{
				expect(result.status).toBe('success');
			});

			// Should take longer due to concurrency limits
			const totalTime = endTime - startTime;
			expect(totalTime).toBeGreaterThan(2000); // Should queue some jobs

			// Verify concurrency was actually limited
			const stats = worker.getStatistics();
			expect(stats.maxConcurrentExecutions).toBeLessThanOrEqual(2);
		});

		it('should clean up resources after job completion', async () =>
		{
			const worker = new WorkerService();
			worker.setWorkerId('cleanup-worker');
			await worker.initialize();
			workerServices.push(worker);

			const initialHandles = process._getActiveHandles().length;
			const initialRequests = process._getActiveRequests().length;

			// Process a job
			const job = createMockDomainJob('cleanup-test.com');
			const result = await worker.processJob(job);

			expect(result.status).toBe('success');

			// Allow time for cleanup
			await new Promise(resolve => setTimeout(resolve, 1000));

			const finalHandles = process._getActiveHandles().length;
			const finalRequests = process._getActiveRequests().length;

			// Should not have significantly more handles/requests
			expect(finalHandles).toBeLessThanOrEqual(initialHandles + 5);
			expect(finalRequests).toBeLessThanOrEqual(initialRequests + 5);
		});

		it('should handle graceful shutdown during processing', async () =>
		{
			const worker = new WorkerService();
			worker.setWorkerId('shutdown-worker');
			await worker.initialize();
			await worker.start();
			workerServices.push(worker);

			// Start a long-running job
			const job = createMockDomainJob('shutdown-test.com');
			const jobPromise = worker.processJob(job);

			// Initiate shutdown after a short delay
			setTimeout(async () =>
			{
				await worker.stop();
			}, 1000);

			const result = await jobPromise;

			// Job should complete or fail gracefully
			expect(['success', 'failure']).toContain(result.status);

			// Worker should be stopped
			expect(worker.isServiceRunning()).toBe(false);
		});
	});

	describe('Monitoring and Observability', () =>
	{
		it('should collect accurate metrics under concurrent load', async () =>
		{
			const worker = new WorkerService();
			worker.setWorkerId('metrics-worker');
			await worker.initialize();
			workerServices.push(worker);

			const numJobs = 20;
			const jobs = Array.from({ length: numJobs }, (_, i) =>
				createMockDomainJob(`metrics-test-${i}.com`, {
					crawlType: 'quick',
				}));

			const results = await Promise.all(
				jobs.map(job => worker.processJob(job))
			);

			const successfulJobs = results.filter(r => r.status === 'success').length;
			const failedJobs = results.filter(r => r.status === 'failure').length;

			const metrics = worker.getMetrics();

			expect(metrics.totalJobsProcessed).toBe(numJobs);
			expect(metrics.successfulJobs).toBe(successfulJobs);
			expect(metrics.failedJobs).toBe(failedJobs);
			expect(metrics.successRate).toBe(successfulJobs / numJobs);
			expect(metrics.averageProcessingTime).toBeGreaterThan(0);
		});

		it('should maintain health status during concurrent operations', async () =>
		{
			const worker = new WorkerService();
			worker.setWorkerId('health-worker');
			await worker.initialize();
			await worker.start();
			workerServices.push(worker);

			// Start multiple jobs
			const jobs = Array.from({ length: 10 }, (_, i) =>
				createMockDomainJob(`health-test-${i}.com`));

			const jobPromises = jobs.map(job => worker.processJob(job));

			// Check health during processing
			const healthChecks = Array.from({ length: 5 }, async (_, i) =>
			{
				await new Promise(resolve => setTimeout(resolve, i * 200));
				return worker.getHealth();
			});

			const [results, healthResults] = await Promise.all([
				Promise.all(jobPromises),
				Promise.all(healthChecks),
			]);

			// All jobs should succeed
			results.forEach((result) =>
			{
				expect(result.status).toBe('success');
			});

			// Health should remain good throughout
			healthResults.forEach((health) =>
			{
				expect(health.status).toBe('healthy');
				expect(health.databases.scylla.connected).toBe(true);
				expect(health.databases.maria.connected).toBe(true);
				expect(health.databases.redis.connected).toBe(true);
			});
		});
	});
});
