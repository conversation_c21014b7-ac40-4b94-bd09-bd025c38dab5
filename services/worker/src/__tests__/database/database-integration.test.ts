/**
 * Database Integration Tests
 *
 * Tests for database operations, external services, cross-component interactions, and data consistency.
 */

import { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach, vi } from 'vitest';
import { WorkerDatabaseManager } from '../../database/WorkerDatabaseManager';
import { DatabaseMigrationManager } from '../../database/DatabaseMigrationManager';
import type { WorkerDatabaseConfigType } from '../../types/DatabaseTypes';
import { testConfig } from '../setup';

describe('Database Integration Tests', () =>
{
	let databaseManager: WorkerDatabaseManager;
	let migrationManager: DatabaseMigrationManager;
	let testDatabaseConfig: WorkerDatabaseConfigType;

	beforeAll(async () =>
	{
		testDatabaseConfig = {
			scylla: {
				hosts: testConfig.scyllaHosts,
				keyspace: 'worker_test',
				replicationFactor: 1,
			},
			maria: {
				host: testConfig.mariaHost,
				port: testConfig.mariaPort,
				user: testConfig.mariaUser,
				password: testConfig.mariaPassword,
				database: 'worker_test',
			},
			redis: {
				url: testConfig.redisUrl,
				keyPrefix: 'worker_test:',
			},
			manticore: {
				host: testConfig.manticoreHost,
				port: testConfig.manticorePort,
				indexPrefix: 'worker_test_',
			},
		};
	});

	beforeEach(async () =>
	{
		databaseManager = new WorkerDatabaseManager(testDatabaseConfig);
		migrationManager = new DatabaseMigrationManager(testDatabaseConfig);

		await databaseManager.initialize();
		await migrationManager.initialize();
	});

	afterEach(async () =>
	{
		await databaseManager.shutdown();
		await migrationManager.shutdown();
	});

	describe('Multi-Database Operations', () =>
	{
		it('should perform atomic operations across multiple databases', async () =>
		{
			const domain = 'atomic-test.com';
			const crawlData = {
				domain,
				timestamp: new Date(),
				dns: { records: ['***********'] },
				robots: { allowed: true },
				homepage: { title: 'Test Domain' },
			};

			const rankingData = {
				domain,
				compositeScore: 85.5,
				grade: 'B+',
				lastUpdated: new Date(),
			};

			// Start transaction across databases
			const transaction = await databaseManager.beginTransaction();

			try
			{
				// Insert into ScyllaDB
				await databaseManager.getScyllaClient().execute(
					'INSERT INTO domain_analysis (domain, timestamp, data) VALUES (?, ?, ?)',
					[domain, crawlData.timestamp, JSON.stringify(crawlData)],
					{ prepare: true }
				);

				// Insert into MariaDB
				await databaseManager.getMariaClient().execute(
					'INSERT INTO domain_rankings (domain, composite_score, grade, last_updated) VALUES (?, ?, ?, ?)',
					[domain, rankingData.compositeScore, rankingData.grade, rankingData.lastUpdated]
				);

				// Insert into Manticore
				await databaseManager.getManticoreClient().insert('domains', {
					domain,
					title: crawlData.homepage.title,
					score: rankingData.compositeScore,
				});

				await transaction.commit();

				// Verify data exists in all databases
				const scyllaResult = await databaseManager.getScyllaClient().execute(
					'SELECT * FROM domain_analysis WHERE domain = ?',
					[domain]
				);
				expect(scyllaResult.rows.length).toBe(1);

				const mariaResult = await databaseManager.getMariaClient().execute(
					'SELECT * FROM domain_rankings WHERE domain = ?',
					[domain]
				);
				expect(mariaResult.length).toBe(1);

				const manticoreResult = await databaseManager.getManticoreClient().query(
					`SELECT * FROM domains WHERE domain = '${domain}'`
				);
				expect(manticoreResult.length).toBe(1);
			}
			catch (error)
			{
				await transaction.rollback();
				throw error;
			}
		});

		it('should handle transaction rollback on failure', async () =>
		{
			const domain = 'rollback-test.com';
			const transaction = await databaseManager.beginTransaction();

			try
			{
				// Insert valid data into ScyllaDB
				await databaseManager.getScyllaClient().execute(
					'INSERT INTO domain_analysis (domain, timestamp, data) VALUES (?, ?, ?)',
					[domain, new Date(), JSON.stringify({ domain })]
				);

				// Attempt invalid operation on MariaDB (should fail)
				await databaseManager.getMariaClient().execute(
					'INSERT INTO non_existent_table (domain) VALUES (?)',
					[domain]
				);

				await transaction.commit();
			}
			catch (error)
			{
				await transaction.rollback();

				// Verify rollback - data should not exist in ScyllaDB
				const scyllaResult = await databaseManager.getScyllaClient().execute(
					'SELECT * FROM domain_analysis WHERE domain = ?',
					[domain]
				);
				expect(scyllaResult.rows.length).toBe(0);
			}
		});

		it('should maintain data consistency across database failures', async () =>
		{
			const domain = 'consistency-test.com';

			// Mock MariaDB failure during transaction
			const originalMariaExecute = databaseManager.getMariaClient().execute;
			vi.spyOn(databaseManager.getMariaClient(), 'execute')
				.mockRejectedValueOnce(new Error('Connection lost'))
				.mockImplementation(originalMariaExecute);

			const transaction = await databaseManager.beginTransaction();

			try
			{
				await databaseManager.getScyllaClient().execute(
					'INSERT INTO domain_analysis (domain, timestamp, data) VALUES (?, ?, ?)',
					[domain, new Date(), JSON.stringify({ domain })]
				);

				await databaseManager.getMariaClient().execute(
					'INSERT INTO domain_rankings (domain, composite_score, grade, last_updated) VALUES (?, ?, ?, ?)',
					[domain, 80, 'B', new Date()]
				);

				await transaction.commit();
			}
			catch (error)
			{
				await transaction.rollback();

				// Verify no partial data exists
				const scyllaResult = await databaseManager.getScyllaClient().execute(
					'SELECT * FROM domain_analysis WHERE domain = ?',
					[domain]
				);
				expect(scyllaResult.rows.length).toBe(0);
			}
		});
	});

	describe('Database Schema Management', () =>
	{
		it('should detect and run necessary migrations', async () =>
		{
			// Mock outdated schema version
			vi.spyOn(migrationManager, 'getCurrentSchemaVersion').mockResolvedValue('1.0.0');
			vi.spyOn(migrationManager, 'getLatestSchemaVersion').mockResolvedValue('1.1.0');

			const migrationNeeded = await migrationManager.isMigrationNeeded();
			expect(migrationNeeded).toBe(true);

			const migrationResult = await migrationManager.runMigrations();
			expect(migrationResult.success).toBe(true);
			expect(migrationResult.migrationsRun.length).toBeGreaterThan(0);
		});

		it('should handle migration failures gracefully', async () =>
		{
			// Mock migration failure
			vi.spyOn(migrationManager, 'runMigration').mockRejectedValue(
				new Error('Migration failed: syntax error')
			);

			const migrationResult = await migrationManager.runMigrations();
			expect(migrationResult.success).toBe(false);
			expect(migrationResult.error).toContain('Migration failed');
		});

		it('should validate schema integrity after migrations', async () =>
		{
			await migrationManager.runMigrations();

			const schemaValidation = await migrationManager.validateSchema();
			expect(schemaValidation.isValid).toBe(true);
			expect(schemaValidation.missingTables).toHaveLength(0);
			expect(schemaValidation.missingIndexes).toHaveLength(0);
		});

		it('should backup data before major migrations', async () =>
		{
			// Mock major migration (version jump)
			vi.spyOn(migrationManager, 'getCurrentSchemaVersion').mockResolvedValue('1.0.0');
			vi.spyOn(migrationManager, 'getLatestSchemaVersion').mockResolvedValue('2.0.0');

			const migrationResult = await migrationManager.runMigrations();
			expect(migrationResult.success).toBe(true);
			expect(migrationResult.backupCreated).toBe(true);
			expect(migrationResult.backupLocation).toBeDefined();
		});
	});

	describe('Connection Pool Management', () =>
	{
		it('should handle connection pool exhaustion gracefully', async () =>
		{
			const maxConnections = 5;
			const connectionPromises: Promise<any>[] = [];

			// Create more connections than the pool limit
			for (let i = 0; i < maxConnections + 5; i++)
			{
				const promise = databaseManager.getMariaClient().execute('SELECT 1');
				connectionPromises.push(promise);
			}

			const results = await Promise.allSettled(connectionPromises);

			// Some should succeed, some might be queued or fail
			const successful = results.filter(r => r.status === 'fulfilled');
			expect(successful.length).toBeGreaterThan(0);
		});

		it('should recover from connection failures', async () =>
		{
			// Mock connection failure
			const originalExecute = databaseManager.getMariaClient().execute;
			let failureCount = 0;

			vi.spyOn(databaseManager.getMariaClient(), 'execute').mockImplementation(async (...args) =>
			{
				failureCount++;
				if (failureCount <= 2)
				{
					throw new Error('Connection lost');
				}
				return originalExecute.apply(databaseManager.getMariaClient(), args);
			});

			// Should eventually succeed after reconnection
			const result = await databaseManager.getMariaClient().execute('SELECT 1');
			expect(result).toBeDefined();
			expect(failureCount).toBeGreaterThan(2);
		});

		it('should monitor connection health', async () =>
		{
			const healthCheck = await databaseManager.healthCheck();

			expect(healthCheck.scylla.connected).toBe(true);
			expect(healthCheck.maria.connected).toBe(true);
			expect(healthCheck.redis.connected).toBe(true);
			expect(healthCheck.manticore.connected).toBe(true);

			expect(healthCheck.scylla.responseTime).toBeGreaterThan(0);
			expect(healthCheck.maria.responseTime).toBeGreaterThan(0);
			expect(healthCheck.redis.responseTime).toBeGreaterThan(0);
			expect(healthCheck.manticore.responseTime).toBeGreaterThan(0);
		});
	});

	describe('Data Synchronization', () =>
	{
		it('should synchronize data between ScyllaDB and MariaDB', async () =>
		{
			const domain = 'sync-test.com';
			const crawlData = {
				domain,
				timestamp: new Date(),
				dns: { records: ['***********'] },
			};

			// Insert into ScyllaDB
			await databaseManager.getScyllaClient().execute(
				'INSERT INTO domain_analysis (domain, timestamp, data) VALUES (?, ?, ?)',
				[domain, crawlData.timestamp, JSON.stringify(crawlData)]
			);

			// Trigger synchronization
			const syncResult = await databaseManager.synchronizeData(domain);
			expect(syncResult.success).toBe(true);

			// Verify data exists in MariaDB
			const mariaResult = await databaseManager.getMariaClient().execute(
				'SELECT * FROM domain_sync WHERE domain = ?',
				[domain]
			);
			expect(mariaResult.length).toBe(1);
		});

		it('should handle synchronization conflicts', async () =>
		{
			const domain = 'conflict-test.com';

			// Insert different data in both databases
			await databaseManager.getScyllaClient().execute(
				'INSERT INTO domain_analysis (domain, timestamp, data) VALUES (?, ?, ?)',
				[domain, new Date(), JSON.stringify({ version: 1 })]
			);

			await databaseManager.getMariaClient().execute(
				'INSERT INTO domain_sync (domain, data, updated_at) VALUES (?, ?, ?)',
				[domain, JSON.stringify({ version: 2 }), new Date()]
			);

			// Synchronization should detect and resolve conflict
			const syncResult = await databaseManager.synchronizeData(domain);
			expect(syncResult.success).toBe(true);
			expect(syncResult.conflictsResolved).toBe(1);
		});

		it('should maintain referential integrity across databases', async () =>
		{
			const domain = 'integrity-test.com';

			// Insert parent record
			await databaseManager.getScyllaClient().execute(
				'INSERT INTO domain_analysis (domain, timestamp, data) VALUES (?, ?, ?)',
				[domain, new Date(), JSON.stringify({ domain })]
			);

			// Insert child record with foreign key reference
			await databaseManager.getMariaClient().execute(
				'INSERT INTO domain_rankings (domain, composite_score, grade, last_updated) VALUES (?, ?, ?, ?)',
				[domain, 85, 'B+', new Date()]
			);

			// Attempt to delete parent record should fail or cascade properly
			const deleteResult = await databaseManager.deleteWithIntegrityCheck(domain);
			expect(deleteResult.success).toBe(true);
			expect(deleteResult.cascadedDeletes).toBeGreaterThan(0);
		});
	});

	describe('Performance and Optimization', () =>
	{
		it('should execute bulk operations efficiently', async () =>
		{
			const domains = Array.from({ length: 1000 }, (_, i) => `bulk-test-${i}.com`);
			const bulkData = domains.map(domain => ({
				domain,
				timestamp: new Date(),
				data: JSON.stringify({ domain }),
			}));

			const startTime = Date.now();
			const bulkResult = await databaseManager.bulkInsert('domain_analysis', bulkData);
			const endTime = Date.now();

			expect(bulkResult.success).toBe(true);
			expect(bulkResult.insertedCount).toBe(1000);
			expect(endTime - startTime).toBeLessThan(5000); // Should complete within 5 seconds
		});

		it('should optimize query performance with proper indexing', async () =>
		{
			const domain = 'performance-test.com';

			// Insert test data
			await databaseManager.getMariaClient().execute(
				'INSERT INTO domain_rankings (domain, composite_score, grade, last_updated) VALUES (?, ?, ?, ?)',
				[domain, 85, 'B+', new Date()]
			);

			// Query with index usage
			const startTime = Date.now();
			const result = await databaseManager.getMariaClient().execute(
				'SELECT * FROM domain_rankings WHERE domain = ? AND composite_score > ?',
				[domain, 80]
			);
			const endTime = Date.now();

			expect(result.length).toBe(1);
			expect(endTime - startTime).toBeLessThan(100); // Should be fast with proper indexing
		});

		it('should handle large result sets efficiently', async () =>
		{
			// Insert large dataset
			const largeDataset = Array.from({ length: 10000 }, (_, i) => ({
				domain: `large-dataset-${i}.com`,
				composite_score: Math.random() * 100,
				grade: 'B',
				last_updated: new Date(),
			}));

			await databaseManager.bulkInsert('domain_rankings', largeDataset);

			// Query large result set with pagination
			const pageSize = 100;
			let totalResults = 0;
			let page = 0;

			while (true)
			{
				const results = await databaseManager.getMariaClient().execute(
					'SELECT * FROM domain_rankings WHERE domain LIKE ? LIMIT ? OFFSET ?',
					['large-dataset-%', pageSize, page * pageSize]
				);

				if (results.length === 0) break;

				totalResults += results.length;
				page++;

				// Prevent infinite loop
				if (page > 200) break;
			}

			expect(totalResults).toBe(10000);
		});
	});

	describe('Backup and Recovery', () =>
	{
		it('should create consistent backups across all databases', async () =>
		{
			const backupManager = databaseManager.getBackupManager();
			const backupName = `test-backup-${Date.now()}`;

			const backupResult = await backupManager.createBackup(backupName);

			expect(backupResult.success).toBe(true);
			expect(backupResult.backupId).toBeDefined();
			expect(backupResult.databases).toContain('scylla');
			expect(backupResult.databases).toContain('maria');
			expect(backupResult.databases).toContain('redis');
		});

		it('should restore from backup successfully', async () =>
		{
			const backupManager = databaseManager.getBackupManager();
			const backupName = `restore-test-backup-${Date.now()}`;

			// Create backup
			const backupResult = await backupManager.createBackup(backupName);
			expect(backupResult.success).toBe(true);

			// Modify data
			await databaseManager.getMariaClient().execute(
				'INSERT INTO domain_rankings (domain, composite_score, grade, last_updated) VALUES (?, ?, ?, ?)',
				['restore-test.com', 90, 'A', new Date()]
			);

			// Restore from backup
			const restoreResult = await backupManager.restoreFromBackup(backupResult.backupId);
			expect(restoreResult.success).toBe(true);

			// Verify data was restored (new data should be gone)
			const result = await databaseManager.getMariaClient().execute(
				'SELECT * FROM domain_rankings WHERE domain = ?',
				['restore-test.com']
			);
			expect(result.length).toBe(0);
		});

		it('should handle point-in-time recovery', async () =>
		{
			const backupManager = databaseManager.getBackupManager();
			const recoveryPoint = new Date();

			// Insert data before recovery point
			await databaseManager.getMariaClient().execute(
				'INSERT INTO domain_rankings (domain, composite_score, grade, last_updated) VALUES (?, ?, ?, ?)',
				['before-recovery.com', 85, 'B+', new Date(recoveryPoint.getTime() - 60000)]
			);

			// Wait a moment
			await new Promise(resolve => setTimeout(resolve, 100));

			// Insert data after recovery point
			await databaseManager.getMariaClient().execute(
				'INSERT INTO domain_rankings (domain, composite_score, grade, last_updated) VALUES (?, ?, ?, ?)',
				['after-recovery.com', 90, 'A', new Date(recoveryPoint.getTime() + 60000)]
			);

			// Perform point-in-time recovery
			const recoveryResult = await backupManager.pointInTimeRecovery(recoveryPoint);
			expect(recoveryResult.success).toBe(true);

			// Verify only data before recovery point exists
			const beforeResult = await databaseManager.getMariaClient().execute(
				'SELECT * FROM domain_rankings WHERE domain = ?',
				['before-recovery.com']
			);
			expect(beforeResult.length).toBe(1);

			const afterResult = await databaseManager.getMariaClient().execute(
				'SELECT * FROM domain_rankings WHERE domain = ?',
				['after-recovery.com']
			);
			expect(afterResult.length).toBe(0);
		});
	});

	describe('Monitoring and Alerting', () =>
	{
		it('should monitor database performance metrics', async () =>
		{
			const metrics = await databaseManager.getPerformanceMetrics();

			expect(metrics.scylla.queryLatency).toBeDefined();
			expect(metrics.scylla.throughput).toBeDefined();
			expect(metrics.scylla.connectionCount).toBeDefined();

			expect(metrics.maria.queryLatency).toBeDefined();
			expect(metrics.maria.throughput).toBeDefined();
			expect(metrics.maria.connectionCount).toBeDefined();

			expect(metrics.redis.commandLatency).toBeDefined();
			expect(metrics.redis.memoryUsage).toBeDefined();

			expect(metrics.manticore.indexSize).toBeDefined();
			expect(metrics.manticore.searchLatency).toBeDefined();
		});

		it('should detect and alert on performance degradation', async () =>
		{
			const alertManager = databaseManager.getAlertManager();

			// Simulate slow query
			const slowQueryPromise = databaseManager.getMariaClient().execute(
				'SELECT SLEEP(2)' // 2 second delay
			);

			// Check for performance alerts
			const alerts = await alertManager.checkPerformanceAlerts();

			await slowQueryPromise;

			expect(alerts.some(alert =>
				alert.type === 'slow_query' && alert.severity === 'warning')).toBe(true);
		});

		it('should track database capacity and growth', async () =>
		{
			const capacityManager = databaseManager.getCapacityManager();
			const capacityReport = await capacityManager.generateCapacityReport();

			expect(capacityReport.scylla.diskUsage).toBeDefined();
			expect(capacityReport.scylla.projectedGrowth).toBeDefined();
			expect(capacityReport.scylla.recommendedActions).toBeDefined();

			expect(capacityReport.maria.diskUsage).toBeDefined();
			expect(capacityReport.maria.projectedGrowth).toBeDefined();

			expect(capacityReport.redis.memoryUsage).toBeDefined();
			expect(capacityReport.redis.projectedGrowth).toBeDefined();
		});
	});
});
