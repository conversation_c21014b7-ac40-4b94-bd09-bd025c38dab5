/**
 * Comprehensive Test Setup for Worker Service
 *
 * This file configures the test environment for the consolidated worker service.
 * It includes setup for all testing utilities needed for crawler, ranking-engine,
 * and scheduler functionality testing, extracted from all three source services.
 */

import { vi, beforeAll, afterAll, beforeEach, afterEach, expect } from 'vitest';
import { config } from 'dotenv';

// Load test environment variables
config({ path: '.env.test' });

// Set test timeout globally
vi.setConfig({ testTimeout: 30000 });

// Mock environment variables for testing
process.env.NODE_ENV = 'test';
process.env.SCYLLA_HOSTS = 'localhost:9042';
process.env.MARIA_HOST = 'localhost';
process.env.MARIA_PORT = '3306';
process.env.MARIA_USER = 'test';
process.env.MARIA_PASSWORD = 'test';
process.env.MARIA_DATABASE = 'test_db';
process.env.REDIS_URL = 'redis://localhost:6379/1';
process.env.MANTICORE_HOST = 'localhost';
process.env.MANTICORE_PORT = '9308';
process.env.BROWSERLESS_URL = 'http://localhost:3000';
process.env.IMAGE_PROXY_URL = 'https://images.weserv.nl';

// Mock console methods to reduce noise in tests
const originalConsole = console;

beforeAll(async () =>
{
  // Suppress console output during tests unless explicitly needed
  global.console = {
    ...originalConsole,
    log: vi.fn(),
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    debug: vi.fn(),
  };

  console.log('Worker Service test environment initialized');
});

afterAll(async () =>
{
  // Restore original console
  global.console = originalConsole;

  console.log('Worker Service test environment cleaned up');
});

beforeEach(async () =>
{
  // Reset all mocks before each test
  vi.clearAllMocks();
});

afterEach(async () =>
{
  // Cleanup after each test
  vi.restoreAllMocks();
});

// Test configuration extracted from all source services
export const testConfig = {
  // Database test configuration
  scyllaHosts: ['localhost:9042'],
  mariaHost: 'localhost',
  mariaPort: 3306,
  mariaUser: 'test',
  mariaPassword: 'test',
  mariaDatabase: 'domain_ranking_test',
  redisUrl: 'redis://localhost:6379/1', // Use database 1 for tests
  manticoreHost: 'localhost',
  manticorePort: 9308,

  // External service test configuration
  browserlessUrl: 'http://localhost:3000',
  imageProxyUrl: 'https://images.weserv.nl',

  // Test-specific settings
  testTimeout: 30000,
  maxConcurrentTasks: 2, // Lower concurrency for tests
  jobRetryAttempts: 1, // Fewer retries for faster tests
};

// Global test utilities extracted from crawler service
global.testUtils = {
  delay: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),

  // Mock domain for testing
  mockDomain: 'example.com',

  // Test domains that should have favicons
  domainsWithFavicons: [
    'google.com',
    'github.com',
    'stackoverflow.com',
    'wikipedia.org',
  ],

  // Test domains that might not have favicons
  domainsWithoutFavicons: [
    'nonexistent-domain-12345.com',
    'invalid..domain',
  ],

  // Test domains for various scenarios
  testDomains: [
    'example.com',
    'test.org',
    'sample.net',
    'demo.io',
  ],

  // Ranking test domains
  rankingTestDomains: [
    'google.com',
    'facebook.com',
    'amazon.com',
    'microsoft.com',
    'apple.com',
  ],

  // Scheduler test domains
  schedulerTestDomains: [
    'github.com',
    'stackoverflow.com',
    'techcrunch.com',
    'wired.com',
  ],
};

// Mock domain data for testing
export const mockDomains = [
  'example.com',
  'test.org',
  'sample.net',
  'demo.io',
];

// Mock job data for testing
export function createMockDomainJob(domain: string, overrides?: Record<string, unknown>)
{
  return {
    id: `job-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    domain,
    priority: 'medium' as const,
    crawlType: 'full' as const,
    scheduledAt: new Date(),
    createdAt: new Date(),
    tasks: [],
    retryCount: 0,
    maxRetries: 3,
    requestedBy: 'test-suite',
    metadata: {},
    ...(overrides || {}),
  };
}

// Mock ranking data for testing
export function createMockRankingData(domain: string, overrides?: Record<string, unknown>)
{
  return {
    domain,
    globalRank: Math.floor(Math.random() * 1000000) + 1,
    categoryRank: Math.floor(Math.random() * 10000) + 1,
    category: 'technology',
    score: Math.random() * 100,
    grade: 'B+',
    lastUpdated: new Date(),
    ...(overrides || {}),
  };
}

// Mock crawling result for testing
export function createMockCrawlingResult(domain: string, overrides?: Record<string, unknown>)
{
  return {
    domain,
    status: 'success',
    timestamp: new Date(),
    dnsAnalysis: {
      hasA: true,
      hasAAAA: false,
      hasMX: true,
      hasCNAME: false,
    },
    robotsAnalysis: {
      exists: true,
      allowsCrawling: true,
      hasDisallows: false,
    },
    sslAnalysis: {
      hasSSL: true,
      grade: 'A',
      validCertificate: true,
    },
    homepageAnalysis: {
      title: `${domain} - Test Site`,
      description: `Test description for ${domain}`,
      hasContent: true,
    },
    ...(overrides || {}),
  };
}

// Test database helpers
export const testDatabaseHelpers = {
  async cleanupTestData(): Promise<void>
  {
    // Implementation will be added when database functionality is extracted
    console.log('Cleaning up test data...');
  },

  async seedTestData(): Promise<void>
  {
    // Implementation will be added when database functionality is extracted
    console.log('Seeding test data...');
  },

  async createTestDomain(domain: string): Promise<void>
  {
    console.log(`Creating test domain: ${domain}`);
  },

  async deleteTestDomain(domain: string): Promise<void>
  {
    console.log(`Deleting test domain: ${domain}`);
  },
};

// Test assertion helpers
export const testAssertions = {
  assertValidCrawlingResult(result: unknown): void
  {
    expect(result).toBeDefined();
    expect(typeof result).toBe('object');
    expect(result).toHaveProperty('domain');
    expect(result).toHaveProperty('status');
    expect(result).toHaveProperty('timestamp');
  },

  assertValidRankingResult(result: unknown): void
  {
    expect(result).toBeDefined();
    expect(typeof result).toBe('object');
    expect(result).toHaveProperty('domain');
    expect(result).toHaveProperty('score');
    expect(result).toHaveProperty('grade');
  },

  assertValidIndexingResult(result: unknown): void
  {
    expect(result).toBeDefined();
    expect(typeof result).toBe('object');
    expect(result).toHaveProperty('domain');
    expect(result).toHaveProperty('indexed');
  },

  assertValidDomainJob(job: unknown): void
  {
    expect(job).toBeDefined();
    expect(typeof job).toBe('object');
    expect(job).toHaveProperty('id');
    expect(job).toHaveProperty('domain');
    expect(job).toHaveProperty('priority');
    expect(job).toHaveProperty('crawlType');
  },
};

// Custom matchers for Vitest (extracted from crawler service)
expect.extend({
  toBeValidUrl(received: string)
  {
    const urlRegex = /^https?:\/\/[^\s/$.?#].[^\s]*$/i;
    const pass = urlRegex.test(received);

    if (pass)
    {
      return {
        message: () => `expected ${received} not to be a valid URL`,
        pass: true,
      };
    }

    return {
      message: () => `expected ${received} to be a valid URL`,
      pass: false,
    };
  },

  toBeValidTimestamp(received: string)
  {
    const date = new Date(received);
    const pass = !isNaN(date.getTime()) && date.getTime() > 0;

    if (pass)
    {
      return {
        message: () => `expected ${received} not to be a valid timestamp`,
        pass: true,
      };
    }

    return {
      message: () => `expected ${received} to be a valid timestamp`,
      pass: false,
    };
  },

  toBeValidDomain(received: string)
  {
    const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
    const pass = domainRegex.test(received);

    if (pass)
    {
      return {
        message: () => `expected ${received} not to be a valid domain`,
        pass: true,
      };
    }

    return {
      message: () => `expected ${received} to be a valid domain`,
      pass: false,
    };
  },

  toBeValidScore(received: number)
  {
    const pass = typeof received === 'number' && received >= 0 && received <= 100;

    if (pass)
    {
      return {
        message: () => `expected ${received} not to be a valid score (0-100)`,
        pass: true,
      };
    }

    return {
      message: () => `expected ${received} to be a valid score (0-100)`,
      pass: false,
    };
  },
});

// Extend Vitest matchers type definitions
declare global
{
  namespace Vi
  {
    interface Matchers<R>
    {
      toBeValidUrl(): R;
      toBeValidTimestamp(): R;
      toBeValidDomain(): R;
      toBeValidScore(): R;
    }
  }

  var testUtils: {
    delay: (ms: number) => Promise<void>;
    mockDomain: string;
    domainsWithFavicons: string[];
    domainsWithoutFavicons: string[];
    testDomains: string[];
    rankingTestDomains: string[];
    schedulerTestDomains: string[];
  };
}

export default {
  testConfig,
  mockDomains,
  createMockDomainJob,
  createMockRankingData,
  createMockCrawlingResult,
  testDatabaseHelpers,
  testAssertions,
};
