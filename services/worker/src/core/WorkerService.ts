/**
 * Main Worker Service Class
 *
 * This is the core orchestrator that manages the entire worker lifecycle.
 * It coordinates job consumption, domain processing, and resource management.
 */

import { logger as loggerFactory } from '@shared/utils/Logger';
import type {
	WorkerConfigType,
	WorkerHealthType,
	DomainJobType,
	ProcessingResultType,
} from '../types/WorkerTypes';
import type { WorkerDatabaseConfigType } from '../types/DatabaseTypes';
import { WorkerDatabaseManager } from '../database';
import { DomainProcessingPipeline, TaskExecutorPool, PipelineCoordinator } from '../pipeline';
import { DomainLockManager } from '../locking';
// Skipping heavy orchestrators in simplified mode
// import DataCollectionOrchestrator from '../crawler/core/DataCollectionOrchestrator';
// import RankingManager from '../ranking/RankingManager';
import SearchIndexingService from '../indexing/SearchIndexingService';
import { HealthCheck } from '../monitoring/HealthCheck';
import { WorkerErrorHandler } from '../errors/WorkerErrorHandler';
import { JobConsumer } from '../queue/JobConsumer';
import WorkerJobQueue from '../queue/WorkerJobQueue';

const logger = loggerFactory.getLogger('worker-service');

class WorkerService
{
	private config: WorkerConfigType;
	private databaseManager: WorkerDatabaseManager | null = null;
	private isInitialized: boolean = false;
	private isRunning: boolean = false;
	private startTime: Date = new Date();

	// Core components
	private domainLockManager: DomainLockManager | null = null;
	private crawlerOrchestrator: DataCollectionOrchestrator | null = null;
	private rankingManager: RankingManager | null = null;
	private indexingService: SearchIndexingService | null = null;
	private healthCheck: HealthCheck | null = null;
	private pipelineCoordinator: PipelineCoordinator | null = null;
	private errorHandler: WorkerErrorHandler | null = null;

	// Job processing components
	private jobQueue: WorkerJobQueue | null = null;
	private jobConsumer: JobConsumer | null = null;

	// Service state tracking
	private shutdownInProgress: boolean = false;
	private initializationPromise: Promise<void> | null = null;

	constructor()
	{
		this.config = this.loadConfiguration();
	}

	/**
   * Initialize the worker service and all its components
   */
	async initialize(): Promise<void>
	{
		if (this.isInitialized)
		{
			logger.warn('Worker service already initialized');
			return;
		}

		try
		{
			logger.info('Initializing Worker Service...', { workerId: this.config.workerId });

			// Initialize comprehensive error handler
			this.errorHandler = new WorkerErrorHandler(logger, {
				classification: { enabled: true, confidenceThreshold: 0.7 },
				retry: {
					enabled: true,
					defaultStrategy: {
						maxAttempts: this.config.jobRetryAttempts,
						baseDelay: this.config.jobRetryDelay,
						maxDelay: 60000,
						backoffMultiplier: 2,
						jitterEnabled: true,
					},
				},
				circuitBreaker: { enabled: true },
				degradation: { enabled: true, monitoringInterval: 30000 },
				reporting: { enabled: true, channels: ['log'] },
				recovery: { enabled: true, autoRecoveryEnabled: true },
				analytics: { enabled: true, analysisInterval: 300000 },
			});
			logger.info('Comprehensive error handler initialized');

			// Initialize database connections
			this.databaseManager = new WorkerDatabaseManager(this.createDatabaseConfig());
			await this.databaseManager.initialize();
			logger.info('Database connections initialized');

			// Initialize domain lock manager (constructor performs setup)
			this.domainLockManager = new DomainLockManager(
				this.databaseManager.getRedisClient(),
				this.config.workerId,
				{
					defaultTtl: 300,
					maxTtl: 3600,
					renewalThreshold: 60,
					maxRetries: 3,
					baseRetryDelay: 1000,
					maxRetryDelay: 30000,
					lockPrefix: 'p4:lock:domain',
					statsPrefix: 'p4:lock:stats',
					cleanupInterval: 60000,
					enableStatistics: true,
					enableMonitoring: true,
				},
			);
			logger.info('Domain lock manager initialized');

			// Initialize health check
			this.healthCheck = new HealthCheck(this.databaseManager);
			logger.info('Health check initialized');

			// Skip heavy components in simplified mode (no DB deps)
			this.crawlerOrchestrator = null;
			logger.warn('Skipping crawler orchestrator initialization in simplified mode');

			this.rankingManager = null;
			logger.warn('Skipping ranking manager initialization in simplified mode');

			// Initialize indexing service (degraded mode if Manticore unavailable)
			try
			{
				this.indexingService = new SearchIndexingService(
					this.databaseManager.getScyllaClient(),
					this.databaseManager.getManticoreClient(),
					this.databaseManager.getRedisClient(),
					{
						syncInterval: 30000, // 30 seconds
						batchSize: 100,
						maxConcurrency: 5,
						enablePeriodicSync: true,
						enableMaintenance: true,
						enableCacheInvalidation: true,
						conflictResolution: 'latest_wins',
					},
				);
				await this.indexingService.initialize();
				logger.info('Indexing service initialized');
			}
			catch (e)
			{
				logger.warn('Manticore unavailable or indexing init failed, continuing in degraded mode', e);
				this.indexingService = null;
			}

			// Skip job queue in simplified mode (redis-smq not wired)
			this.jobQueue = null as any;
			this.jobConsumer = null as any;
			logger.warn('Skipping job queue initialization in simplified mode');

			// Initialize pipeline coordinator only if heavy deps are available
			if (this.crawlerOrchestrator && this.rankingManager)
			{
				this.pipelineCoordinator = new PipelineCoordinator(this.config);
				await this.pipelineCoordinator.initialize(async () =>
				{
					// Factory function to create pipeline instances
					const pipeline = new DomainProcessingPipeline(
						this.config,
						this.databaseManager!,
						this.domainLockManager!,
						this.crawlerOrchestrator!,
						this.rankingManager!,
						this.indexingService!,
					);

					const taskExecutor = new TaskExecutorPool(this.config);

					await pipeline.initialize();
					await taskExecutor.initialize();

					return { pipeline, taskExecutor };
				});
				logger.info('Pipeline coordinator initialized');

				// Connect job consumer to pipeline coordinator
				if (this.jobConsumer && this.pipelineCoordinator)
				{
					this.jobConsumer.setDomainProcessingPipeline(this.pipelineCoordinator);
					logger.info('Job consumer connected to pipeline coordinator');
				}
			}
			else
			{
				logger.warn('Skipping pipeline coordinator (crawler/ranking unavailable)');
			}

			this.isInitialized = true;
			logger.info('Worker Service initialized successfully');
		}
		catch (error)
		{
			logger.error('Failed to initialize Worker Service:', error);
			// TEMP: verbose console for debugging init failure
			// eslint-disable-next-line no-console
			console.error('WorkerService.initialize() error:', error instanceof Error ? error.stack || error.message : error);
			throw error;
		}
	}

	/**
   * Start the worker service and begin processing jobs
   */
	async start(): Promise<void>
	{
		if (!this.isInitialized)
		{
			throw new Error('Worker service must be initialized before starting');
		}

		if (this.isRunning)
		{
			logger.warn('Worker service already running');
			return;
		}

		try
		{
			logger.info('Starting Worker Service...', { workerId: this.config.workerId });

			// Health check is ready - no startup needed

			// Start job consumer
			if (this.jobConsumer)
			{
				await this.jobConsumer.startConsuming();
				logger.info('Job consumer started');
			}

			this.isRunning = true;
			this.startTime = new Date();

			logger.info('Worker Service started successfully');
		}
		catch (error)
		{
			logger.error('Failed to start Worker Service:', error);
			// TEMP DEBUG: also print to console for visibility
			// eslint-disable-next-line no-console
			console.error('WorkerService.start() error:', error instanceof Error ? error.stack || error.message : error);
			throw error;
		}
	}

	/**
   * Stop the worker service gracefully
   */
	async stop(): Promise<void>
	{
		if (!this.isRunning)
		{
			logger.warn('Worker service not running');
			return;
		}

		try
		{
			logger.info('Stopping Worker Service...', { workerId: this.config.workerId });

			// Stop accepting new jobs
			if (this.jobConsumer)
			{
				await this.jobConsumer.stopConsuming();
				logger.info('Job consumer stopped');
			}

			// Health check - no shutdown needed

			this.isRunning = false;

			logger.info('Worker Service stopped successfully');
		}
		catch (error)
		{
			logger.error('Failed to stop Worker Service:', error);
			throw error;
		}
	}

	/**
   * Shutdown the worker service and cleanup resources
   */
	async shutdown(): Promise<void>
	{
		try
		{
			logger.info('Shutting down Worker Service...', { workerId: this.config.workerId });

			// Prevent multiple shutdown attempts
			if (this.shutdownInProgress)
			{
				logger.warn('Shutdown already in progress');
				return;
			}
			this.shutdownInProgress = true;

			// Ensure service is stopped first
			if (this.isRunning)
			{
				await this.stop();
			}

			// Shutdown pipeline coordinator
			if (this.pipelineCoordinator)
			{
				await this.pipelineCoordinator.shutdown();
				this.pipelineCoordinator = null;
				logger.info('Pipeline coordinator shut down');
			}

			// Shutdown job consumer
			if (this.jobConsumer)
			{
				// Job consumer should already be stopped, but ensure cleanup
				this.jobConsumer = null;
				logger.info('Job consumer cleaned up');
			}

			// Shutdown job queue
			if (this.jobQueue)
			{
				await this.jobQueue.shutdown();
				this.jobQueue = null;
				logger.info('Job queue shut down');
			}

			// Shutdown indexing service
			if (this.indexingService)
			{
				await this.indexingService.shutdown();
				this.indexingService = null;
				logger.info('Indexing service shut down');
			}

			// Shutdown ranking manager
			if (this.rankingManager)
			{
				await this.rankingManager.shutdown();
				this.rankingManager = null;
				logger.info('Ranking manager shut down');
			}

			// Shutdown crawler orchestrator
			if (this.crawlerOrchestrator)
			{
				await this.crawlerOrchestrator.shutdown();
				this.crawlerOrchestrator = null;
				logger.info('Crawler orchestrator shut down');
			}

			// Shutdown domain lock manager
			if (this.domainLockManager)
			{
				await this.domainLockManager.shutdown();
				this.domainLockManager = null;
				logger.info('Domain lock manager shut down');
			}

			// Close database connections
			if (this.databaseManager)
			{
				await this.databaseManager.close();
				this.databaseManager = null;
				logger.info('Database connections closed');
			}

			// Clear health check
			this.healthCheck = null;

			// Shutdown error handler
			if (this.errorHandler)
			{
				this.errorHandler.shutdown();
				this.errorHandler = null;
				logger.info('Error handler shut down');
			}

			this.isInitialized = false;
			this.shutdownInProgress = false;

			logger.info('Worker Service shutdown complete');
		}
		catch (error)
		{
			logger.error('Failed to shutdown Worker Service:', error);
			throw error;
		}
	}

	/**
   * Process a single domain job through the complete pipeline
   */
	async processJob(domainJob: DomainJobType): Promise<ProcessingResultType>
	{
		if (!this.pipelineCoordinator)
		{
			throw new Error('Pipeline coordinator not initialized');
		}

		if (!this.errorHandler)
		{
			throw new Error('Error handler not initialized');
		}

		logger.info('Processing domain job', {
			jobId: domainJob.id,
			domain: domainJob.domain,
			workerId: this.config.workerId,
		});

		try
		{
			// Execute domain processing with comprehensive error handling
			const result = await this.errorHandler.executeWithErrorHandling(
				'domain-processing',
				async () => await this.pipelineCoordinator!.processDomain(domainJob),
				{
					operationName: 'domain-processing',
					operationId: domainJob.id,
					metadata: {
						domain: domainJob.domain,
						jobId: domainJob.id,
						workerId: this.config.workerId,
						jobType: domainJob.type || 'standard',
						priority: domainJob.priority || 'medium',
					},
				}
			);

			logger.info('Domain job completed', {
				jobId: domainJob.id,
				domain: domainJob.domain,
				status: result.status,
				totalTime: result.totalProcessingTime,
			});

			return result;
		}
		catch (error)
		{
			logger.error('Domain job failed after comprehensive error handling:', error, {
				jobId: domainJob.id,
				domain: domainJob.domain,
			});
			throw error;
		}
	}

	/**
   * Get current health status of the worker service
   */
	async getHealth(): Promise<WorkerHealthType>
	{
		const now = new Date();
		const uptime = now.getTime() - this.startTime.getTime();

		// Get health status
		if (this.healthCheck)
		{
			try
			{
				return await this.healthCheck.getHealthStatus();
			}
			catch (error)
			{
				logger.warn('Health check failed:', error);
			}
		}

		// Fallback health check if health service not available
		let databaseHealth = {
			scylla: { connected: false, lastCheck: now, responseTime: 0 },
			maria: { connected: false, lastCheck: now, responseTime: 0 },
			redis: { connected: false, lastCheck: now, responseTime: 0 },
			manticore: { connected: false, lastCheck: now, responseTime: 0 },
		};

		if (this.databaseManager)
		{
			try
			{
				databaseHealth = await this.databaseManager.healthCheck();
			}
			catch (error)
			{
				logger.warn('Database health check failed:', error);
			}
		}

		const allDatabasesHealthy = Object.values(databaseHealth).every(db => db.connected);
		const overallStatus = this.isRunning && allDatabasesHealthy ? 'healthy' : 'unhealthy';

		// Get pipeline statistics if available
		let pipelineStats = {
			activeJobs: 0,
			queuedJobs: 0,
			completedJobs: 0,
			failedJobs: 0,
		};

		if (this.pipelineCoordinator)
		{
			try
			{
				const stats = this.pipelineCoordinator.getStatistics();
				pipelineStats = {
					activeJobs: stats.totalActiveJobs,
					queuedJobs: stats.totalQueuedJobs,
					completedJobs: stats.totalCompletedJobs,
					failedJobs: stats.totalFailedJobs,
				};
			}
			catch (error)
			{
				logger.warn('Pipeline statistics check failed:', error);
			}
		}

		return {
			workerId: this.config.workerId,
			status: overallStatus,
			uptime,
			lastHealthCheck: now,
			activeJobs: pipelineStats.activeJobs,
			queuedJobs: pipelineStats.queuedJobs,
			completedJobs: pipelineStats.completedJobs,
			failedJobs: pipelineStats.failedJobs,
			cpuUsage: 0,
			memoryUsage: process.memoryUsage().heapUsed / process.memoryUsage().heapTotal * 100,
			diskUsage: 0,
			databases: databaseHealth,
			externalServices: {
				browserless: { connected: false, lastCheck: now, responseTime: 0 },
			},
			lockSystem: {
				healthy: !!this.domainLockManager,
				issues: [],
				metrics: {},
				statistics: {
					totalAcquisitions: 0,
					successfulAcquisitions: 0,
					failedAcquisitions: 0,
					conflicts: 0,
					renewals: 0,
					releases: 0,
					orphanedLocks: 0,
					averageHoldTime: 0,
					currentActiveLocks: 0,
				},
			},
			issues: [],
		};
	}

	/**
   * Load configuration from environment variables and config files
   */
	private loadConfiguration(): WorkerConfigType
	{
		return ({
			// Worker settings
			workerId: process.env.WORKER_INSTANCE_ID || 'worker-unknown',
			maxConcurrentTasks: parseInt(process.env.MAX_CONCURRENT_TASKS || '10', 10),

			// Database connections
			scyllaHosts: (process.env.SCYLLA_HOSTS || 'localhost:9042').split(','),
			scyllaUsername: process.env.SCYLLA_USERNAME,
			scyllaPassword: process.env.SCYLLA_PASSWORD,
			mariaHost: process.env.MARIA_HOST || 'localhost',
			mariaPort: parseInt(process.env.MARIA_PORT || '3306', 10),
			mariaUser: process.env.MARIA_USER || 'root',
			mariaPassword: process.env.MARIA_PASSWORD || '',
			mariaDatabase: process.env.MARIA_DATABASE || 'domain_ranking',
			redisUrl: process.env.REDIS_URL || 'redis://localhost:6379',
			redisPassword: process.env.REDIS_PASSWORD,
			manticoreHost: process.env.MANTICORE_HOST || 'localhost',
			manticorePort: parseInt(process.env.MANTICORE_PORT || '9308', 10),

			// External services
			browserlessUrl: process.env.BROWSERLESS_URL || 'http://localhost:3000',
			imageProxyUrl: process.env.IMAGE_PROXY_URL || 'https://images.weserv.nl',

			// Processing settings
			crawlTimeout: parseInt(process.env.CRAWL_TIMEOUT || '30000', 10),
			rankingUpdateBatchSize: parseInt(process.env.RANKING_UPDATE_BATCH_SIZE || '100', 10),
			jobRetryAttempts: parseInt(process.env.JOB_RETRY_ATTEMPTS || '3', 10),
			jobRetryDelay: parseInt(process.env.JOB_RETRY_DELAY || '5000', 10),

			// Logging and monitoring
			logLevel: process.env.LOG_LEVEL || 'info',
			metricsEnabled: process.env.METRICS_ENABLED === 'true',
			healthCheckInterval: parseInt(process.env.HEALTH_CHECK_INTERVAL || '30000', 10),
		});
	}

	/**
   * Get current configuration
   */
	getConfiguration(): WorkerConfigType
	{
		return ({ ...this.config });
	}

	/**
   * Check if the worker service is running
   */
	isServiceRunning(): boolean
	{
		return this.isRunning;
	}

	/**
   * Check if the worker service is initialized
   */
	isServiceInitialized(): boolean
	{
		return this.isInitialized;
	}

	/**
	 * Get database manager instance
	 */
	getDatabaseManager(): WorkerDatabaseManager
	{
		if (!this.databaseManager)
		{
			throw new Error('Database manager not initialized');
		}
		return this.databaseManager;
	}

	/**
	 * Get error handler instance
	 */
	getErrorHandler(): WorkerErrorHandler
	{
		if (!this.errorHandler)
		{
			throw new Error('Error handler not initialized');
		}
		return this.errorHandler;
	}

	/**
	 * Get system health including error handling metrics
	 */
	async getSystemHealth(): Promise<any>
	{
		if (!this.errorHandler)
		{
			throw new Error('Error handler not initialized');
		}
		return this.errorHandler.getSystemHealth();
	}

	/**
	 * Get error analytics
	 */
	getErrorAnalytics(): any
	{
		if (!this.errorHandler)
		{
			throw new Error('Error handler not initialized');
		}
		return this.errorHandler.getErrorAnalytics();
	}

	/**
	 * Get active alerts
	 */
	getActiveAlerts(): any
	{
		if (!this.errorHandler)
		{
			throw new Error('Error handler not initialized');
		}
		return this.errorHandler.getActiveAlerts();
	}

	/**
	 * Acknowledge an alert
	 */
	acknowledgeAlert(alertId: string, userId?: string): boolean
	{
		if (!this.errorHandler)
		{
			throw new Error('Error handler not initialized');
		}
		return this.errorHandler.acknowledgeAlert(alertId, userId);
	}

	/**
	 * Resolve an alert
	 */
	resolveAlert(alertId: string, userId?: string): boolean
	{
		if (!this.errorHandler)
		{
			throw new Error('Error handler not initialized');
		}
		return this.errorHandler.resolveAlert(alertId, userId);
	}

	/**
	 * Restore all capabilities after degradation
	 */
	restoreAllCapabilities(): void
	{
		if (!this.errorHandler)
		{
			throw new Error('Error handler not initialized');
		}
		this.errorHandler.restoreAllCapabilities();
	}

	/**
	 * Get job consumer instance
	 */
	getJobConsumer(): JobConsumer
	{
		if (!this.jobConsumer)
		{
			throw new Error('Job consumer not initialized');
		}
		return this.jobConsumer;
	}

	/**
	 * Get job queue instance
	 */
	getJobQueue(): WorkerJobQueue
	{
		if (!this.jobQueue)
		{
			throw new Error('Job queue not initialized');
		}
		return this.jobQueue;
	}

	/**
	 * Get job consumer metrics
	 */
	getJobConsumerMetrics(): Record<string, any>
	{
		if (!this.jobConsumer)
		{
			return {};
		}
		return this.jobConsumer.getMetrics();
	}

	/**
	 * Get job consumer health
	 */
	getJobConsumerHealth(): any
	{
		if (!this.jobConsumer)
		{
			return { status: 'unavailable' };
		}
		return this.jobConsumer.getHealth();
	}

	/**
	 * Pause job queue consumption
	 */
	async pauseJobQueue(queueName: string): Promise<void>
	{
		if (!this.jobConsumer)
		{
			throw new Error('Job consumer not initialized');
		}
		await this.jobConsumer.pauseQueue(queueName);
	}

	/**
	 * Resume job queue consumption
	 */
	async resumeJobQueue(queueName: string): Promise<void>
	{
		if (!this.jobConsumer)
		{
			throw new Error('Job consumer not initialized');
		}
		await this.jobConsumer.resumeQueue(queueName);
	}

	/**
	 * Clear a queue - removes all pending jobs
	 */
	async clearQueue(queueName: string): Promise<number>
	{
		if (!this.jobQueue)
		{
			throw new Error('Job queue not initialized');
		}
		// Would clear the queue and return count of cleared jobs
		// For now, return 0 as placeholder
		return 0;
	}

	/**
	 * Pause queue (alias for pauseJobQueue)
	 */
	async pauseQueue(queueName: string): Promise<void>
	{
		return this.pauseJobQueue(queueName);
	}

	/**
	 * Resume queue (alias for resumeJobQueue)
	 */
	async resumeQueue(queueName: string): Promise<void>
	{
		return this.resumeJobQueue(queueName);
	}

	/**
	 * Run database migrations
	 */
	async runDatabaseMigrations(direction: 'up' | 'down' = 'up'): Promise<{ applied: number; failed: number }>
	{
		if (!this.databaseManager)
		{
			throw new Error('Database manager not initialized');
		}
		// Would run migrations using the database migration manager
		return { applied: 0, failed: 0 };
	}

	/**
	 * Get worker metrics
	 */
	async getMetrics(): Promise<any>
	{
		const metrics = {
			worker: {
				uptime: process.uptime(),
				activeJobs: 0,
				completedJobs: 0,
				failedJobs: 0,
			},
			system: {
				cpuUsage: process.cpuUsage().user / 1000000,
				memoryUsage: process.memoryUsage().heapUsed / 1024 / 1024,
				diskUsage: 0,
			},
		};

		if (this.healthCheck)
		{
			const basicMetrics = this.healthCheck.getBasicMetrics();
			Object.assign(metrics.worker, basicMetrics);
		}

		if (this.jobConsumer)
		{
			const consumerMetrics = this.jobConsumer.getMetrics();
			Object.assign(metrics.worker, consumerMetrics);
		}

		return metrics;
	}

	/**
	 * Get lock information
	 */
	async getLockInfo(): Promise<any>
	{
		if (!this.domainLockManager)
		{
			return {
				activeLocks: [],
				expiredLocks: [],
				statistics: {
					totalAcquisitions: 0,
					successfulAcquisitions: 0,
					failedAcquisitions: 0,
				},
			};
		}

		const stats = this.domainLockManager.getStatistics();
		const activeLocks = await this.domainLockManager.getActiveLocks();

		return {
			activeLocks,
			expiredLocks: [],
			statistics: stats,
		};
	}

	/**
	 * Get pipeline status
	 */
	async getPipelineStatus(): Promise<any>
	{
		if (!this.pipelineCoordinator)
		{
			return {
				status: 'unknown',
				activeJobs: 0,
				queuedJobs: 0,
				statistics: {},
			};
		}

		const stats = this.pipelineCoordinator.getStatistics();
		return {
			status: 'running',
			activeJobs: stats.activeJobs || 0,
			queuedJobs: stats.queuedJobs || 0,
			statistics: stats,
		};
	}

	/**
	 * Get pipeline coordinator statistics
	 */
	getPipelineStatistics(): any
	{
		if (!this.pipelineCoordinator)
		{
			return {};
		}
		return this.pipelineCoordinator.getStatistics();
	}

	/**
	 * Get health check instance
	 */
	getHealthCheck(): HealthCheck | null
	{
		return this.healthCheck;
	}

	/**
	 * Create database configuration from worker configuration
	 */
	private createDatabaseConfig(): WorkerDatabaseConfig
	{
		return ({
			scylla: {
				hosts: this.config.scyllaHosts,
				keyspace: 'domain_ranking',
				localDataCenter: 'datacenter1',
				username: this.config.scyllaUsername,
				password: this.config.scyllaPassword,
			},
			maria: {
				host: this.config.mariaHost,
				port: this.config.mariaPort,
				user: this.config.mariaUser,
				password: this.config.mariaPassword,
				database: this.config.mariaDatabase,
				connectionLimit: 10,
			},
			redis: {
				url: this.config.redisUrl,
				database: 0,
				password: this.config.redisPassword,
				maxRetries: 3,
			},
			manticore: {
				host: this.config.manticoreHost,
				port: this.config.manticorePort,
				timeout: 30000,
			},
			healthCheckInterval: this.config.healthCheckInterval,
			runMigrationsOnStartup: false,
		});
	}
}

export default WorkerService;
export { WorkerService };
