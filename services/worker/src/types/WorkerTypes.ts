/**
 * Core type definitions for the Worker Service
 *
 * These types define the interfaces and data structures used throughout
 * the consolidated worker service.
 */

// Import AI types
import type {
	AIProvidersConfigType,
	ContentGenerationRequestType,
	GeneratedContentType,
	ContentAnalysisRequestType,
	ContentAnalysisResultType,
} from '../ai/types';

// ============================================================================
// Core Worker Types
// ============================================================================

type WorkerStatusType = 'healthy' | 'degraded' | 'unhealthy';
type ProcessingPhaseType = 'crawling' | 'ranking' | 'indexing';
type JobStatusType = 'pending' | 'processing' | 'completed' | 'failed' | 'retrying';
type JobPriorityType = 'low' | 'medium' | 'high';
type CrawlTypeType = 'full' | 'quick' | 'security' | 'performance';
type LockConflictResolutionType = 'fail' | 'wait' | 'force';

// ============================================================================
// Database and Service Connection Types
// ============================================================================

type DatabaseConnectionStatusType =
{
	connected: boolean;
	lastCheck: Date;
	responseTime: number;
	error?: string;
};

type ServiceConnectionStatusType =
{
	connected: boolean;
	lastCheck: Date;
	responseTime: number;
	error?: string;
};

// ============================================================================
// Lock System Types
// ============================================================================

type LockAcquisitionResultType =
{
	success: boolean;
	lockId?: string;
	conflictingOwner?: string;
	retryAfter?: number;
	error?: string;
};

type LockReleaseResultType =
{
	success: boolean;
	wasOwner: boolean;
	error?: string;
};

type LockStatisticsType =
{
	totalAcquisitions: number;
	successfulAcquisitions: number;
	failedAcquisitions: number;
	conflicts: number;
	renewals: number;
	releases: number;
	orphanedLocks: number;
	averageHoldTime: number;
	currentActiveLocks: number;
};

type DomainLockType =
{
	domain: string;
	workerId: string;
	acquiredAt: Date;
	expiresAt: Date;
	renewedAt?: Date;
};

// ============================================================================
// Error and Warning Types
// ============================================================================

type ProcessingErrorType =
{
	code: string;
	message: string;
	phase: ProcessingPhaseType;
	task?: string;
	timestamp: Date;
	retryable: boolean;
	details?: Record<string, unknown>;
};

type ProcessingWarningType =
{
	code: string;
	message: string;
	phase: ProcessingPhaseType;
	task?: string;
	timestamp: Date;
	details?: Record<string, unknown>;
};

// ============================================================================
// Resource and Performance Types
// ============================================================================

type ResourceUsageType =
{
	cpuTime: number;
	memoryPeak: number;
	memoryAverage: number;
	diskIO: number;
	networkIO: number;
};

type TaskMetricsType =
{
	taskName: string;
	executionTime: number;
	memoryUsage: number;
	success: boolean;
	retryCount: number;
};

// ============================================================================
// Health Monitoring Types
// ============================================================================

type HealthIssueType =
{
	id: string;
	severity: 'low' | 'medium' | 'high' | 'critical';
	message: string;
	timestamp: Date;
	resolved: boolean;
};

type WorkerHealthType =
{
	workerId: string;
	status: WorkerStatusType;
	uptime: number;
	lastHealthCheck: Date;

	// Processing status
	activeJobs: number;
	queuedJobs: number;
	completedJobs: number;
	failedJobs: number;

	// Resource status
	cpuUsage: number;
	memoryUsage: number;
	diskUsage: number;

	// Database connectivity
	databases: {
		scylla: DatabaseConnectionStatusType;
		maria: DatabaseConnectionStatusType;
		redis: DatabaseConnectionStatusType;
		manticore: DatabaseConnectionStatusType;
	};

	// External services
	externalServices: {
		browserless: ServiceConnectionStatusType;
	};

	// Lock system health
	lockSystem: {
		healthy: boolean;
		issues: string[];
		metrics: Record<string, number>;
		statistics: LockStatisticsType;
	};

	// Issues and alerts
	issues: HealthIssueType[];
};

// ============================================================================
// Task Configuration Types
// ============================================================================

type CrawlerTaskConfigType =
{
	modules: string[];
	timeout: number;
	retryAttempts: number;
	collectScreenshots: boolean;
	collectPerformanceMetrics: boolean;
};

type RankingTaskConfigType =
{
	scoringAlgorithms: string[];
	weights: Record<string, number>;
	recalculateHistory: boolean;
};

type IndexingTaskConfigType =
{
	updateSearchIndex: boolean;
	invalidateCache: boolean;
	syncDatabases: string[];
};

type TaskConfigurationType =
{
	name: string;
	type: 'crawler' | 'ranking' | 'indexing';
	enabled: boolean;
	timeout: number;
	retryAttempts: number;

	// Task-specific configuration
	crawlerConfig?: CrawlerTaskConfigType;
	rankingConfigType?: RankingTaskConfigType;
	indexingConfig?: IndexingTaskConfigType;

	// Dependencies
	dependsOn: string[];
	runInParallel: boolean;
};

// ============================================================================
// Processing Result Types
// ============================================================================

type CrawlingResultType =
{
	domain: string;
	status: 'success' | 'failure' | 'partial';
	completedModules: string[];
	failedModules: string[];
	data: Record<string, unknown>;
	errors: ProcessingErrorType[];
};

type RankingResultType =
{
	domain: string;
	status: 'success' | 'failure' | 'partial';
	scores: Record<string, number>;
	compositeScore: number;
	grade: string;
	ranking: number;
	errors: ProcessingErrorType[];
};

type IndexingResultType =
{
	domain: string;
	status: 'success' | 'failure' | 'partial';
	updatedIndexes: string[];
	invalidatedCaches: string[];
	syncedDatabases: string[];
	errors: ProcessingErrorType[];
};

type ProcessingResultType =
{
	domain: string;
	jobId: string;
	status: 'success' | 'failure' | 'partial';

	// Processing phases
	crawlingResult: CrawlingResultType;
	rankingResult: RankingResultType;
	indexingResult: IndexingResultType;

	// Timing and performance
	totalProcessingTime: number;
	phaseTimings: Record<ProcessingPhaseType, number>;

	// Lock information
	lockAcquired: boolean;
	lockAcquisitionTime?: number;
	lockHoldTime?: number;
	lockConflicts?: number;

	// Error information
	errors: ProcessingErrorType[];
	warnings: ProcessingWarningType[];

	// Metrics
	resourceUsage: ResourceUsageType;
	taskMetrics: TaskMetricsType[];
};

// ============================================================================
// Job Processing Types
// ============================================================================

type DomainJobType =
{
	id: string;
	domain: string;
	priority: JobPriorityType;
	crawlType: CrawlTypeType;

	// Scheduling information
	scheduledAt: Date;
	createdAt: Date;
	startedAt?: Date;
	completedAt?: Date;

	// Processing configuration
	tasks: TaskConfigurationType[];
	retryCount: number;
	maxRetries: number;

	// Metadata
	requestedBy: string;
	metadata: Record<string, unknown>;
};

// ============================================================================
// Configuration Types
// ============================================================================

type WorkerConfigType =
{
	// Worker settings
	workerId: string;
	maxConcurrentTasks: number;

	// Database connections
	scyllaHosts: string[];
	scyllaUsername?: string;
	scyllaPassword?: string;
	mariaHost: string;
	mariaPort: number;
	mariaUser: string;
	mariaPassword: string;
	mariaDatabase: string;
	redisUrl: string;
	redisPassword?: string;
	manticoreHost: string;
	manticorePort: number;

	// External services
	browserlessUrl: string;
	imageProxyUrl: string;

	// Processing settings
	crawlTimeout: number;
	rankingUpdateBatchSize: number;
	jobRetryAttempts: number;
	jobRetryDelay: number;

	// Lock system settings
	lockDefaultTtl?: number;
	lockMaxTtl?: number;
	lockRenewalThreshold?: number;
	lockMaxRetries?: number;
	lockRetryDelay?: number;
	lockCleanupInterval?: number;
	enableLockStatistics?: boolean;
	enableLockMonitoring?: boolean;

	// Logging and monitoring
	logLevel: string;
	metricsEnabled: boolean;
	healthCheckInterval: number;

	// AI Configuration
	aiProviders?: AIProvidersConfigType;
	aiEnabled?: boolean;
	aiContentGeneration?: boolean;
	aiContentAnalysis?: boolean;
};

// ============================================================================
// Queue Types
// ============================================================================

type QueueStatsType =
{
	pending: number;
	processing: number;
	completed: number;
	failed: number;
	retrying: number;
};

// ============================================================================
// Validation Pipeline Types
// ============================================================================

type DomainCandidateType =
{
	domain: string;
	source: string;
	rank?: number;
	metadata?: Record<string, any>;
};

export type {
	WorkerStatusType,
	ProcessingPhaseType,
	JobStatusType,
	JobPriorityType,
	CrawlTypeType,
	LockConflictResolutionType,
	DatabaseConnectionStatusType,
	ServiceConnectionStatusType,
	LockAcquisitionResultType,
	LockReleaseResultType,
	LockStatisticsType,
	DomainLockType,
	ProcessingErrorType,
	ProcessingWarningType,
	ResourceUsageType,
	TaskMetricsType,
	HealthIssueType,
	WorkerHealthType,
	CrawlerTaskConfigType,
	RankingTaskConfigType,
	IndexingTaskConfigType,
	TaskConfigurationType,
	CrawlingResultType,
	RankingResultType,
	IndexingResultType,
	ProcessingResultType,
	DomainJobType,
	WorkerConfigType,
	QueueStatsType,
	DomainCandidateType,
	// Re-export AI types for convenience
	AIProvidersConfigType,
	ContentGenerationRequestType,
	GeneratedContentType,
	ContentAnalysisRequestType,
	ContentAnalysisResultType,
};
