/**
 * Performance monitoring and optimization types
 */

export type PerformanceMetrics = {
	name: string;
	timestamp: number;
	duration: number;
	data?: any;
};

export type ResourceUsage = {
	timestamp: number;
	memory: {
		rss: number;
		heapTotal: number;
		heapUsed: number;
		external: number;
		arrayBuffers: number;
	};
	cpu: {
		user: number;
		system: number;
	};
	uptime: number;
};

export type ProfilerConfig = {
	resourceMonitoringInterval?: number;
	maxMetrics?: number;
	enableGCMonitoring?: boolean;
	enableHTTPMonitoring?: boolean;
};

export type MemoryProfile = {
	current: NodeJS.MemoryUsage;
	trend: number[];
	statistics: {
		max: number;
		min: number;
		average: number;
		growth: number;
	};
	gcEvents: number;
};

export type CPUProfile = {
	trend: number[];
	statistics: {
		max: number;
		min: number;
		average: number;
	};
	uptime: number;
};

export type LoadTestResult = {
	testName: string;
	concurrency: number;
	duration: number;
	totalOperations: number;
	successfulOperations: number;
	failedOperations: number;
	averageDuration: number;
	throughput: number;
	errors: string[];
};

export type PerformanceReport = {
	duration: number;
	memoryProfile: MemoryProfile;
	cpuProfile: CPUProfile;
	operationMetrics: PerformanceMetrics[];
	httpMetrics: PerformanceMetrics[];
	loadTestResults: LoadTestResult[];
	recommendations: string[];
};

export type DatabaseOptimizationConfig = {
	connectionPoolSize: number;
	queryTimeout: number;
	transactionTimeout: number;
	enableQueryCache: boolean;
	enableConnectionReuse: boolean;
	maxRetries: number;
	retryDelay: number;
};

export type CacheStrategy = {
	type: 'memory' | 'redis' | 'hybrid';
	ttl: number;
	maxSize: number;
	evictionPolicy: 'lru' | 'lfu' | 'fifo';
	compressionEnabled: boolean;
};

export type MemoryOptimizationConfig = {
	gcStrategy: 'aggressive' | 'balanced' | 'conservative';
	heapSizeLimit: number;
	objectPooling: boolean;
	stringInterning: boolean;
	bufferPooling: boolean;
};

export type AutoScalingConfig = {
	enabled: boolean;
	minInstances: number;
	maxInstances: number;
	cpuThreshold: number;
	memoryThreshold: number;
	queueLengthThreshold: number;
	scaleUpCooldown: number;
	scaleDownCooldown: number;
};

export type MonitoringAlert = {
	id: string;
	type: 'performance' | 'resource' | 'error' | 'capacity';
	severity: 'low' | 'medium' | 'high' | 'critical';
	message: string;
	timestamp: number;
	metrics: Record<string, number>;
	threshold: number;
	currentValue: number;
};

export type CapacityMetrics = {
	currentLoad: number;
	maxCapacity: number;
	utilizationPercentage: number;
	queueLength: number;
	activeConnections: number;
	throughput: number;
	responseTime: number;
};

export type PerformanceBenchmark = {
	name: string;
	baseline: number;
	current: number;
	improvement: number;
	status: 'improved' | 'degraded' | 'stable';
	threshold: number;
};

export type OptimizationResult = {
	category: string;
	description: string;
	beforeMetrics: Record<string, number>;
	afterMetrics: Record<string, number>;
	improvement: Record<string, number>;
	recommendations: string[];
};

export type ProductionReadinessCheck = {
	category: string;
	name: string;
	status: 'pass' | 'fail' | 'warning';
	message: string;
	metrics?: Record<string, number>;
	recommendations?: string[];
};

export type ProductionReadinessReport = {
	overallStatus: 'ready' | 'needs-attention' | 'not-ready';
	score: number;
	checks: ProductionReadinessCheck[];
	criticalIssues: string[];
	recommendations: string[];
	performanceBenchmarks: PerformanceBenchmark[];
};
