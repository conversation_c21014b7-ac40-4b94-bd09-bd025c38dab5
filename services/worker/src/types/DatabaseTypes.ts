/**
 * Database Types for Worker Service
 *
 * Consolidated type definitions for all database operations extracted from:
 * - crawler service: Domain analysis, crawl data
 * - ranking-engine service: Rankings, backlinks, categories
 * - scheduler service: Job management
 */

// ===== DATABASE CONNECTION TYPES =====

type DatabaseConnectionStatusType =
{
	connected: boolean;
	lastCheck: Date;
	responseTime: number;
	error?: string;
};

type WorkerDatabaseConfigType =
{
	scylla:
	{
		hosts: string[];
		keyspace: string;
		localDataCenter: string;
		username?: string;
		password?: string;
		connectionTimeout?: number;
		readTimeout?: number;
	};
	maria:
	{
		host: string;
		port: number;
		user: string;
		password: string;
		database: string;
		connectionLimit: number;
		charset?: string;
		timezone?: string;
	};
	redis:
	{
		url: string;
		database: number;
		password?: string;
		maxRetries: number;
		connectTimeout?: number;
	};
	manticore:
	{
		host: string;
		port: number;
		timeout: number;
	};
	healthCheckInterval?: number;
	runMigrationsOnStartup?: boolean;
};

// ===== SCYLLA DB DATA TYPES =====

type DomainAnalysisDataType =
{
	domain: string;
	globalRank?: number;
	category?: string;
	categoryRank?: number;
	performanceMetrics:
	{
		loadTime?: number;
		firstContentfulPaint?: number;
		largestContentfulPaint?: number;
		cumulativeLayoutShift?: number;
		firstInputDelay?: number;
		speedIndex?: number;
		score?: number;
		[key: string]: unknown;
	};
	securityMetrics:
	{
		sslGrade?: string;
		securityHeaders?: Record<string, unknown>;
		vulnerabilities?: Array<Record<string, unknown>>;
		certificateInfo?: Record<string, unknown>;
		score?: number;
		[key: string]: unknown;
	};
	seoMetrics:
	{
		title?: string;
		description?: string;
		metaTags?: Record<string, unknown>;
		structuredData?: Array<Record<string, unknown>>;
		sitemap?: Record<string, unknown>;
		robotsTxt?: Record<string, unknown>;
		score?: number;
		[key: string]: unknown;
	};
	technicalMetrics:
	{
		technologies?: string[];
		serverInfo?: Record<string, unknown>;
		httpHeaders?: Record<string, unknown>;
		pageSize?: number;
		resourceCount?: Record<string, unknown>;
		score?: number;
		[key: string]: unknown;
	};
	technologies: string[];
	serverInfo: Record<string, unknown>;
	domainAgeDays?: number;
	registrationDate?: Date;
	expirationDate?: Date;
	registrar?: string;
	dnsRecords: Record<string, unknown>;
	screenshotUrls: string[];
	subdomains: string[];
	lastCrawled?: Date;
	crawlStatus: string;
	robotsAnalysis: Record<string, unknown>;
	sslAnalysis: Record<string, unknown>;
	homepageAnalysis: Record<string, unknown>;
};

type DomainRankingDataType =
{
	domain: string;
	rankingType: string;
	rank: number;
	overallScore: number;
	performanceScore: number;
	securityScore: number;
	seoScore: number;
	technicalScore: number;
	backlinkScore: number;
	trafficEstimate?: number;
	lastUpdated: Date;
	grade?: string;
	category?: string;
};

type CrawlJobDataType =
{
	jobId: string;
	domain: string;
	crawlType: string;
	priority: string;
	status: string;
	retryCount: number;
	scheduledAt: Date;
	startedAt?: Date;
	completedAt?: Date;
	errorMessage?: string;
	userAgent?: string;
	pagesToCrawl?: string[];
	metadata?: Record<string, unknown>;
};

// ===== MARIA DB DATA TYPES =====

type BacklinkDataType =
{
	id: string;
	sourceDomain: string;
	targetDomain: string;
	linkQualityScore: number;
	anchorText?: string;
	linkType: 'follow' | 'nofollow' | 'sponsored' | 'ugc';
	discoveredAt: Date;
	lastVerified?: Date;
	isActive: boolean;
};

type DomainCategoryDataType =
{
	domain: string;
	categoryId: number;
	confidenceScore: number;
	assignedAt?: Date;
};

type DomainWhoisDataType =
{
	domain: string;
	registrar?: string;
	registrationDate?: Date;
	expirationDate?: Date;
	nameServers?: string;
	registrantCountry?: string;
	registrantOrganization?: string;
	privacyProtected: boolean;
	lastUpdated?: Date;
};

type DomainReviewDataType =
{
	id: string;
	domain: string;
	source: string;
	rating: number;
	reviewText?: string;
	reviewDate: Date;
	sentimentScore?: number;
	verified: boolean;
	createdAt?: Date;
};

// ===== REDIS DATA TYPES =====

type CacheKeyTemplateType =
{
	domainAnalysis: string;
	domainRanking: string;
	globalRankings: string;
	categoryRankings: string;
	searchResults: string;
	topDomains: string;
	domainLock: string;
	jobQueue: string;
};

type DomainLockDataType =
{
	domain: string;
	workerId: string;
	acquiredAt: Date;
	ttl: number;
	renewCount: number;
};

type JobQueueDataType =
{
	id: string;
	type: string;
	domain: string;
	priority: number;
	data: Record<string, unknown>;
	createdAt: Date;
	scheduledAt?: Date;
	attempts: number;
	maxAttempts: number;
};

// ===== MANTICORE SEARCH DATA TYPES =====

type SearchIndexDataType =
{
	domain: string;
	title?: string;
	description?: string;
	category?: string;
	country?: string;
	language?: string;
	technologies: string[];
	registrar?: string;
	domainAgeDays?: number;
	globalRank?: number;
	overallScore?: number;
	performanceScore?: number;
	securityScore?: number;
	seoScore?: number;
	technicalScore?: number;
	backlinkScore?: number;
	trafficEstimate?: number;
	sslGrade?: string;
	mobileFriendlyScore?: number;
	accessibilityScore?: number;
	lastUpdated: Date;
};

type SearchFiltersType =
{
	category?: string;
	country?: string;
	technologies?: string[];
	sslGrade?: string;
	minRank?: number;
	maxRank?: number;
	minScore?: number;
	maxScore?: number;
	[key: string]: unknown;
};

type SearchResultType =
{
	domain: string;
	title?: string;
	description?: string;
	category?: string;
	country?: string;
	language?: string;
	technologies: string[];
	registrar?: string;
	domainAge?: number;
	globalRank?: number;
	scores:
	{
		overall?: number;
		performance?: number;
		security?: number;
		seo?: number;
		technical?: number;
		backlink?: number;
	};
	trafficEstimate?: number;
	sslGrade?: string;
	mobileFriendlyScore?: number;
	accessibilityScore?: number;
	lastUpdated?: string;
	_score: number;
};

type SearchResponseType =
{
	results: SearchResultType[];
	total: number;
	facets: Record<string, Array<{ value: string; count: number }>>;
	took: number;
};

// ===== DATABASE OPERATION RESULT TYPES =====

type DatabaseOperationResultType =
{
	success: boolean;
	affectedRows?: number;
	insertId?: string | number;
	error?: string;
	executionTime: number;
};

type BatchOperationResultType =
{
	success: boolean;
	totalOperations: number;
	successfulOperations: number;
	failedOperations: number;
	errors: Array<{ operation: number; error: string }>;
	executionTime: number;
};

type HealthCheckResultType =
{
	database: string;
	connected: boolean;
	responseTime: number;
	lastCheck: Date;
	error?: string;
	details?: Record<string, unknown>;
};

// ===== MIGRATION AND SCHEMA TYPES =====

type DatabaseSchemaType =
{
	version: string;
	tables: Array<{
		name: string;
		columns: Array<{
			name: string;
			type: string;
			nullable: boolean;
			defaultValue?: unknown;
			primaryKey?: boolean;
			foreignKey?:
			{
				table: string;
				column: string;
			};
		}>;
		indexes: Array<{
			name: string;
			columns: string[];
			unique: boolean;
		}>;
	}>;
};

type MigrationScriptType =
{
	version: string;
	description: string;
	up: string;
	down: string;
	checksum: string;
};

// ===== EXTRACTED OPERATION TYPES =====
// These represent the specific operations extracted from each service

type CrawlerDatabaseOperationsType =
{
	upsertDomainAnalysis: (data: DomainAnalysisDataType) => Promise<void>;
	getDomainAnalysis: (domain: string) => Promise<DomainAnalysisDataType | null>;
	storeScreenshots: (domain: string, urls: string[]) => Promise<void>;
	getScreenshots: (domain: string) => Promise<string[]>;
	cacheAnalysisResult: (domain: string, moduleType: string, result: Record<string, unknown>) => Promise<void>;
	getCachedAnalysisResult: (domain: string, moduleType: string) => Promise<Record<string, unknown> | null>;
	storeDnsAnalysis: (domain: string, analysis: Record<string, unknown>) => Promise<void>;
	storeSslAnalysis: (domain: string, analysis: Record<string, unknown>) => Promise<void>;
	storeRobotsAnalysis: (domain: string, analysis: Record<string, unknown>) => Promise<void>;
	storeHomepageAnalysis: (domain: string, analysis: Record<string, unknown>) => Promise<void>;
};

type RankingEngineDatabaseOperationsType =
{
	upsertDomainRanking: (data: DomainRankingDataType) => Promise<void>;
	getDomainRankings: (rankingType: string, limit?: number) => Promise<DomainRankingDataType[]>;
	insertRankingHistory: (data: any) => Promise<void>;
	getRankingHistory: (domain: string, rankingType: string, days?: number) => Promise<any[]>;
	insertBacklink: (data: BacklinkDataType) => Promise<void>;
	getBacklinksByDomain: (domain: string, limit?: number) => Promise<BacklinkDataType[]>;
	updateBacklinkQuality: (id: string, score: number) => Promise<void>;
	assignDomainToCategory: (data: DomainCategoryDataType) => Promise<void>;
	getDomainCategories: (domain: string) => Promise<DomainCategoryDataType[]>;
	calculateCompositeRanking: (domain: string) => Promise<number>;
	updateGlobalRankings: (category?: string) => Promise<void>;
};

type SchedulerDatabaseOperationsType =
{
	insertCrawlJob: (data: CrawlJobDataType) => Promise<void>;
	updateCrawlJobStatus: (jobId: string, status: string, errorMessage?: string) => Promise<void>;
	getCrawlJob: (jobId: string) => Promise<CrawlJobDataType | null>;
	getPendingJobs: (limit?: number) => Promise<CrawlJobDataType[]>;
	enqueueJob: (queueName: string, jobData: Record<string, unknown>) => Promise<void>;
	dequeueJob: (queueName: string) => Promise<Record<string, unknown> | null>;
	getQueueLength: (queueName: string) => Promise<number>;
	cleanupCompletedJobs: (olderThanDays: number) => Promise<number>;
	cleanupFailedJobs: (olderThanDays: number) => Promise<number>;
	getJobStatistics: (timeframe: string) => Promise<Record<string, number>>;
};

type ConsolidatedDatabaseOperationsType =
	& CrawlerDatabaseOperationsType
	& RankingEngineDatabaseOperationsType
	& SchedulerDatabaseOperationsType
	& {
		indexDomain: (data: SearchIndexDataType) => Promise<void>;
		searchDomains: (
			query: string,
			filters?: SearchFiltersType,
			limit?: number,
		) => Promise<SearchResponseType>;
		cacheDomainAnalysis: (
			domain: string,
			data: DomainAnalysisDataType,
			ttl?: number,
		) => Promise<void>;
		getCachedDomainAnalysis: (domain: string) => Promise<DomainAnalysisDataType | null>;
		cacheDomainRanking: (domain: string, data: DomainRankingDataType, ttl?: number) => Promise<void>;
		getCachedDomainRanking: (domain: string) => Promise<DomainRankingDataType | null>;
		invalidateDomainCache: (domain: string) => Promise<void>;
		acquireDomainLock: (domain: string, workerId: string, ttl?: number) => Promise<boolean>;
		releaseDomainLock: (domain: string, workerId: string) => Promise<void>;
		renewDomainLock: (domain: string, workerId: string, ttl?: number) => Promise<boolean>;
		healthCheck: () => Promise<Record<string, DatabaseConnectionStatusType>>;
		getConnectionStats: () => Promise<Record<string, Record<string, unknown>>>;
	};

export type {
	DatabaseConnectionStatusType,
	WorkerDatabaseConfigType,
	DomainAnalysisDataType,
	DomainRankingDataType,
	CrawlJobDataType,
	BacklinkDataType,
	DomainCategoryDataType,
	DomainWhoisDataType,
	DomainReviewDataType,
	CacheKeyTemplateType,
	DomainLockDataType,
	JobQueueDataType,
	SearchIndexDataType,
	SearchFiltersType,
	SearchResultType,
	SearchResponseType,
	DatabaseOperationResultType,
	BatchOperationResultType,
	HealthCheckResultType,
	DatabaseSchemaType,
	MigrationScriptType,
	CrawlerDatabaseOperationsType,
	RankingEngineDatabaseOperationsType,
	SchedulerDatabaseOperationsType,
	ConsolidatedDatabaseOperationsType
};
