/**
 * Module Interfaces for Worker Service
 *
 * This module provides comprehensive interface definitions extracted from
 * all three source services (crawler, ranking-engine, scheduler).
 */

import type {
	ServiceStatus,
	ProcessingStatus,
	Priority,
	OperationResult,
	ValidationResult,
	PerformanceMetrics,
	HealthCheckResult,
	RetryOptions,
	TimeoutOptions,
	JobOptions,
	JobResult,
	LockOptions,
	LockInfo,
} from './UtilityTypes';

// Base interfaces
type BaseModuleType =
{
	name: string;
	version: string;
	status: ServiceStatus;
	initialize(): Promise<void>;
	start(): Promise<void>;
	stop(): Promise<void>;
	healthCheck(): Promise<HealthCheckResult>;
	getMetrics(): Promise<PerformanceMetrics>;
};

type ConfigurableModuleType = BaseModuleType
& {
	configure(config: Record<string, unknown>): Promise<void>;
	getConfiguration(): Record<string, unknown>;
	validateConfiguration(config: Record<string, unknown>): ValidationResult;
};

// Crawler module interfaces
type CrawlerModuleType = ConfigurableModuleType
& {
	crawlDomain(domain: string, options?: CrawlOptionsType): Promise<CrawlResultType>;
	batchCrawl(domains: string[], options?: BatchCrawlOptionsType): Promise<BatchCrawlResultType>;
	validateCapability(): Promise<CapabilityResultType>;
};

type CrawlOptionsType =
{
	timeout?: number;
	retries?: number;
	userAgent?: string;
	headers?: Record<string, string>;
	followRedirects?: boolean;
	maxRedirects?: number;
	validateSSL?: boolean;
};

type CrawlResultType =
{
	domain: string;
	status: ProcessingStatus;
	data: Record<string, unknown>;
	error?: string;
	duration: number;
	timestamp: Date;
	metadata: Record<string, unknown>;
};

type BatchCrawlOptionsType = CrawlOptionsType
& {
	concurrency?: number;
	batchSize?: number;
	delayBetweenBatches?: number;
};

type BatchCrawlResultType =
{
	results: CrawlResultType[];
	summary:
	{
		total: number;
		successful: number;
		failed: number;
		duration: number;
	};
};

type CapabilityResultType =
{
	browserlessAvailable: boolean;
	imageProxyAvailable: boolean;
	browserlessStatus?: string;
	imageProxyStatus?: string;
};

// Analyzer interfaces
type AnalyzerModuleType = BaseModuleType
& {
	analyze(domain: string, data?: unknown): Promise<AnalysisResultType>;
	batchAnalyze(domains: string[], data?: unknown[]): Promise<BatchAnalysisResultType>;
	getSupportedAnalysisTypes(): string[];
};

type AnalysisResultType =
{
	domain: string;
	analysisType: string;
	status: ProcessingStatus;
	data: Record<string, unknown>;
	confidence?: number;
	error?: string;
	duration: number;
	timestamp: Date;
};

type BatchAnalysisResultType =
{
	results: AnalysisResultType[];
	summary:
	{
		total: number;
		successful: number;
		failed: number;
		averageConfidence?: number;
		duration: number;
	};
};

// Ranking module interfaces
type RankingModuleType = ConfigurableModuleType
& {
	calculateRanking(domain: string, data: RankingInputDataType): Promise<RankingResultType>;
	batchCalculateRanking(inputs: RankingInputType[]): Promise<BatchRankingResultType>;
	updateRankingWeights(weights: RankingWeightsType): Promise<void>;
	getRankingWeights(): RankingWeightsType;
};

type RankingInputDataType =
{
	performanceMetrics?: Record<string, number>;
	securityMetrics?: Record<string, number>;
	seoMetrics?: Record<string, number>;
	technicalMetrics?: Record<string, number>;
	backlinkMetrics?: Record<string, number>;
};

type RankingInputType =
{
	domain: string;
	data: RankingInputDataType;
};

type RankingResultType =
{
	domain: string;
	globalRank?: number;
	categoryRank?: number;
	category?: string;
	score: number;
	grade: string;
	breakdown: Record<string, number>;
	timestamp: Date;
};

type BatchRankingResultType =
{
	results: RankingResultType[];
	summary:
	{
		total: number;
		successful: number;
		failed: number;
		averageScore: number;
		duration: number;
	};
};

type RankingWeightsType =
{
	performance: number;
	security: number;
	seo: number;
	technical: number;
	backlinks: number;
};

// Scoring interfaces
type ScoringModuleType = BaseModuleType
& {
	calculateScore(domain: string, data: unknown): Promise<ScoringResultType>;
	getScoreBreakdown(domain: string): Promise<ScoreBreakdownType>;
	validateScoringData(data: unknown): ValidationResult;
};

type ScoringResultType =
{
	domain: string;
	score: number;
	maxScore: number;
	percentage: number;
	grade: string;
	factors: ScoreFactorType[];
	timestamp: Date;
};

type ScoreFactorType =
{
	name: string;
	score: number;
	maxScore: number;
	weight: number;
	description: string;
};

type ScoreBreakdownType =
{
	totalScore: number;
	maxScore: number;
	factors: ScoreFactorType[];
	recommendations: string[];
};

// Indexing module interfaces
type IndexingModuleType = ConfigurableModuleType
& {
	indexDomain(domain: string, data: IndexingDataType): Promise<IndexingResultType>;
	batchIndex(inputs: IndexingInputType[]): Promise<BatchIndexingResultType>;
	updateIndex(domain: string, data: Partial<IndexingDataType>): Promise<IndexingResultType>;
	deleteFromIndex(domain: string): Promise<OperationResult>;
	searchIndex(query: SearchQueryType): Promise<SearchResultType>;
};

type IndexingDataType =
{
	domain: string;
	title?: string;
	description?: string;
	category?: string;
	tags?: string[];
	ranking?: number;
	score?: number;
	metadata?: Record<string, unknown>;
};

type IndexingInputType =
{
	domain: string;
	data: IndexingDataType;
};

type IndexingResultType =
{
	domain: string;
	success: boolean;
	indexed: boolean;
	error?: string;
	duration: number;
	timestamp: Date;
};

type BatchIndexingResultType =
{
	results: IndexingResultType[];
	summary:
	{
		total: number;
		successful: number;
		failed: number;
		duration: number;
	};
};

type SearchQueryType =
{
	query: string;
	filters?: Record<string, unknown>;
	sort?: string;
	limit?: number;
	offset?: number;
};

type SearchResultType =
{
	results: IndexSearchResultItemType[];
	total: number;
	duration: number;
	facets?: Record<string, unknown>;
};

type IndexSearchResultItemType =
{
	domain: string;
	title?: string;
	description?: string;
	score: number;
	ranking?: number;
	category?: string;
	tags?: string[];
	metadata?: Record<string, unknown>;
};

// Job and queue interfaces
type JobModuleType = BaseModuleType
& {
	createJob(type: string, data: unknown, options?: JobOptions): Promise<JobInfoType>;
	getJob(jobId: string): Promise<JobInfoType | null>;
	cancelJob(jobId: string): Promise<boolean>;
	retryJob(jobId: string): Promise<boolean>;
	getJobStats(): Promise<JobStatsType>;
};

type JobInfoType =
{
	id: string;
	type: string;
	status: ProcessingStatus;
	priority: Priority;
	data: unknown;
	result?: JobResult;
	createdAt: Date;
	startedAt?: Date;
	completedAt?: Date;
	attempts: number;
	maxAttempts: number;
	error?: string;
	progress?: number;
	metadata?: Record<string, unknown>;
};

type JobStatsType =
{
	total: number;
	pending: number;
	running: number;
	completed: number;
	failed: number;
	cancelled: number;
	averageProcessingTime: number;
	throughput: number;
};

// Queue interfaces
type QueueModuleType = BaseModuleType
& {
	enqueue(item: QueueItemType, options?: QueueOptionsType): Promise<string>;
	dequeue(count?: number): Promise<QueueItemType[]>;
	peek(count?: number): Promise<QueueItemType[]>;
	getQueueSize(): Promise<number>;
	clearQueue(): Promise<number>;
	getQueueStats(): Promise<QueueStatsType>;
};

type QueueItemType =
{
	id: string;
	type: string;
	data: unknown;
	priority: Priority;
	createdAt: Date;
	attempts: number;
	maxAttempts: number;
	delay?: number;
	metadata?: Record<string, unknown>;
};

type QueueOptionsType =
{
	priority?: Priority;
	delay?: number;
	maxAttempts?: number;
	timeout?: number;
};

type QueueStatsType =
{
	size: number;
	processing: number;
	completed: number;
	failed: number;
	throughput: number;
	averageWaitTime: number;
	averageProcessingTime: number;
};

// Lock manager interfaces
type LockModuleType = BaseModuleType
& {
	acquireLock(key: string, options?: LockOptions): Promise<LockInfo | null>;
	releaseLock(key: string, owner: string): Promise<boolean>;
	renewLock(key: string, owner: string, ttl?: number): Promise<boolean>;
	isLocked(key: string): Promise<boolean>;
	getLockInfo(key: string): Promise<LockInfo | null>;
	cleanupExpiredLocks(): Promise<number>;
};

// Database interfaces
type DatabaseModuleType = ConfigurableModuleType
& {
	connect(): Promise<void>;
	disconnect(): Promise<void>;
	isConnected(): boolean;
	execute(query: string, params?: unknown[]): Promise<QueryResultType>;
	transaction<T>(callback: (tx: TransactionContextType) => Promise<T>): Promise<T>;
	migrate(): Promise<MigrationResultType>;
};

type QueryResultType =
{
	rows: unknown[];
	rowCount: number;
	duration: number;
	metadata?: Record<string, unknown>;
};

type TransactionContextType =
{
	execute(query: string, params?: unknown[]): Promise<QueryResultType>;
	rollback(): Promise<void>;
	commit(): Promise<void>;
};

type MigrationResultType =
{
	success: boolean;
	migrationsRun: string[];
	error?: string;
	duration: number;
};

// Cache interfaces
type CacheModuleType = ConfigurableModuleType
& {
	get<T>(key: string): Promise<T | null>;
	set<T>(key: string, value: T, ttl?: number): Promise<boolean>;
	delete(key: string): Promise<boolean>;
	exists(key: string): Promise<boolean>;
	clear(): Promise<number>;
	keys(pattern?: string): Promise<string[]>;
	getCacheStats(): Promise<CacheStatsType>;
};

type CacheStatsType =
{
	size: number;
	hits: number;
	misses: number;
	hitRate: number;
	memoryUsage: number;
	evictions: number;
};

// Monitoring interfaces
type MonitoringModuleType = BaseModuleType
& {
	recordMetric(name: string, value: number, labels?: Record<string, string>): void;
	recordEvent(event: MonitoringEventType): void;
	getMetricData(name?: string): Promise<MetricDataType[]>;
	getEvents(filter?: EventFilterType): Promise<MonitoringEventType[]>;
	createAlert(alert: AlertDefinitionType): Promise<string>;
	getAlerts(active?: boolean): Promise<AlertType[]>;
};

type MonitoringEventType =
{
	id: string;
	type: string;
	level: 'info' | 'warn' | 'error' | 'critical';
	message: string;
	data?: Record<string, unknown>;
	timestamp: Date;
	source: string;
};

type EventFilterType =
{
	type?: string;
	level?: string;
	source?: string;
	startTime?: Date;
	endTime?: Date;
	limit?: number;
};

type MetricDataType =
{
	name: string;
	value: number;
	timestamp: Date;
	labels?: Record<string, string>;
};

type AlertDefinitionType =
{
	name: string;
	condition: string;
	threshold: number;
	severity: 'low' | 'medium' | 'high' | 'critical';
	enabled: boolean;
	notifications: string[];
};

type AlertType =
{
	id: string;
	name: string;
	status: 'active' | 'resolved' | 'suppressed';
	severity: 'low' | 'medium' | 'high' | 'critical';
	message: string;
	triggeredAt: Date;
	resolvedAt?: Date;
	data?: Record<string, unknown>;
};

// Validation interfaces
type ValidationModuleType = BaseModuleType
& {
	validate(data: unknown, schema?: string): Promise<ValidationResult>;
	validateBatch(items: unknown[], schema?: string): Promise<BatchValidationResultType>;
	registerSchema(name: string, schema: unknown): Promise<void>;
	getSchemas(): Promise<string[]>;
};

type BatchValidationResultType =
{
	results: ValidationResult[];
	summary:
	{
		total: number;
		valid: number;
		invalid: number;
		errors: number;
		warnings: number;
	};
};

// AI and content generation interfaces
type AIModuleType = ConfigurableModuleType
& {
	generateContent(prompt: string, options?: AIGenerationOptionsType): Promise<AIGenerationResultType>;
	analyzeContent(content: string, options?: AIAnalysisOptionsType): Promise<AIAnalysisResultType>;
	getProviders(): Promise<AIProviderType[]>;
	switchProvider(providerId: string): Promise<void>;
};

type AIGenerationOptionsType =
{
	maxTokens?: number;
	temperature?: number;
	model?: string;
	provider?: string;
};

type AIGenerationResultType =
{
	content: string;
	confidence: number;
	tokensUsed: number;
	model: string;
	provider: string;
	duration: number;
	cost?: number;
};

type AIAnalysisOptionsType =
{
	analysisType?: string;
	model?: string;
	provider?: string;
};

type AIAnalysisResultType =
{
	analysis: Record<string, unknown>;
	confidence: number;
	model: string;
	provider: string;
	duration: number;
	cost?: number;
};

type AIProviderType =
{
	id: string;
	name: string;
	status: ServiceStatus;
	models: string[];
	capabilities: string[];
	costPerToken?: number;
};

export type {
	ServiceStatus,
	ProcessingStatus,
	Priority,
	OperationResult,
	ValidationResult,
	PerformanceMetrics,
	HealthCheckResult,
	RetryOptions,
	TimeoutOptions,
	JobOptions,
	JobResult,
	LockOptions,
	LockInfo,
	BaseModuleType,
	ConfigurableModuleType,
	CrawlerModuleType,
	CrawlOptionsType,
	CrawlResultType,
	BatchCrawlOptionsType,
	BatchCrawlResultType,
	CapabilityResultType,
	AnalyzerModuleType,
	AnalysisResultType,
	BatchAnalysisResultType,
	RankingModuleType,
	RankingInputDataType,
	RankingInputType,
	RankingResultType,
	BatchRankingResultType,
	RankingWeightsType,
	ScoringModuleType,
	ScoringResultType,
	ScoreFactorType,
	ScoreBreakdownType,
	IndexingModuleType,
	IndexingDataType,
	IndexingInputType,
	IndexingResultType,
	BatchIndexingResultType,
	IndexSearchQueryType,
	IndexSearchResultType,
	IndexSearchResultItemType,
	JobModuleType,
	JobInfoType,
	JobStatsType,
	QueueModuleType,
	QueueItemType,
	QueueOptionsType,
	QueueStatsType,
	LockModuleType,
	DatabaseModuleType,
	DatabaseQueryResultType,
	TransactionContextType,
	MigrationResultType,
	CacheModuleType,
	CacheStatsType,
	MonitoringModuleType,
	MonitoringEventType,
	EventFilterType,
	MetricDataType,
	AlertDefinitionType,
	AlertType,
	ValidationModuleType,
	BatchValidationResultType,
	AIModuleType,
	AIGenerationOptionsType,
	AIGenerationResultType,
	AIAnalysisOptionsType,
	AIAnalysisResultType,
	AIProviderType,
};
