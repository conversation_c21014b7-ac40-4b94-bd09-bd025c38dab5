/**
 * Utility Types for Worker Service
 *
 * This module provides comprehensive utility types extracted from
 * all three source services (crawler, ranking-engine, scheduler).
 */

// Common utility types
type Nullable<T> = T | null;
type Optional<T> = T | undefined;
type Maybe<T> = T | null | undefined;

// Function types
type AsyncFunction<T = void> = () => Promise<T>;
type CallbackFunction<T = void> = (error?: Error, result?: T) => void;
type EventHandler<T = unknown> = (event: T) => void | Promise<void>;

// Array and collection types
type ArrayElement<T> = T extends (infer U)[] ? U : never;
type Head<T extends readonly unknown[]> = T extends readonly [infer H, ...unknown[]] ? H : never;
type Tail<T extends readonly unknown[]> = T extends readonly [unknown, ...infer Rest] ? Rest : [];
type Last<T extends readonly unknown[]> = T extends readonly [...unknown[], infer L] ? L : never;

// Configuration types
type ConfigurationValueType = string | number | boolean | object | null;
type ConfigurationMapType = Record<string, ConfigurationValueType>;

// Status and state types
type ServiceStatusType = 'healthy' | 'degraded' | 'unhealthy' | 'unknown';
type ProcessingStatusType = 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
type PriorityType = 'low' | 'medium' | 'high' | 'critical';

// Time and duration types
type Timestamp = number;
type Duration = number;
type TimeUnit = 'ms' | 's' | 'm' | 'h' | 'd';

// Error and result types
type ErrorInfoType =
{
	code: string;
	message: string;
	details?: Record<string, unknown>;
	timestamp: Date;
	stack?: string;
};

type OperationResultType<T = unknown> =
{
	success: boolean;
	data?: T;
	error?: ErrorInfoType;
	metadata?: Record<string, unknown>;
};

type ValidationResultType =
{
	isValid: boolean;
	errors: string[];
	warnings: string[];
	data?: unknown;
};

// Pagination types
type PaginationOptionsType =
{
	page: number;
	limit: number;
	sortBy?: string;
	sortOrder?: 'asc' | 'desc';
};

type PaginatedResultType<T> =
{
	items: T[];
	total: number;
	page: number;
	limit: number;
	hasNext: boolean;
	hasPrevious: boolean;
};

// Retry and timeout types
type RetryOptionsType =
{
	maxAttempts: number;
	delay: number;
	backoffMultiplier?: number;
	maxDelay?: number;
	jitter?: boolean;
};

type TimeoutOptionsType =
{
	timeout: number;
	abortSignal?: AbortSignal;
};

// Metrics and monitoring types
type MetricValueType =
{
	value: number;
	timestamp: Date;
	labels?: Record<string, string>;
};

type PerformanceMetrics =
{
	duration: number;
	memoryUsage: number;
	cpuUsage: number;
	timestamp: Date;
};

type HealthCheckResult =
{
	status: ServiceStatusType;
	message?: string;
	details?: Record<string, unknown>;
	timestamp: Date;
};

// Cache types
type CacheOptions =
{
	ttl?: number;
	maxSize?: number;
	strategy?: 'lru' | 'fifo' | 'lfu';
};

type CacheEntry<T> =
{
	key: string;
	value: T;
	timestamp: Date;
	ttl?: number;
	accessCount: number;
};

// Queue and job types
type JobOptions =
{
	priority?: PriorityType;
	delay?: number;
	attempts?: number;
	backoff?: RetryOptionsType;
	timeout?: number;
	metadata?: Record<string, unknown>;
};

type JobProgress =
{
	percentage: number;
	message?: string;
	data?: Record<string, unknown>;
};

type JobResult<T = unknown> =
{
	success: boolean;
	data?: T;
	error?: ErrorInfoType;
	duration: number;
	attempts: number;
};

// Lock types
type LockOptions =
{
	ttl: number;
	retryDelay?: number;
	maxRetries?: number;
	identifier?: string;
};

type LockInfo =
{
	key: string;
	owner: string;
	ttl: number;
	acquiredAt: Date;
	expiresAt: Date;
};

// Network and HTTP types
type HttpRequestOptions =
{
	method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
	headers?: Record<string, string>;
	body?: unknown;
	timeout?: number;
	retries?: number;
};

type HttpResponse<T = unknown> =
{
	status: number;
	statusText: string;
	headers: Record<string, string>;
	data: T;
};

// File and storage types
type FileInfo =
{
	path: string;
	size: number;
	mimeType: string;
	lastModified: Date;
	checksum?: string;
};

type StorageOptions =
{
	bucket?: string;
	path?: string;
	metadata?: Record<string, unknown>;
	encryption?: boolean;
};

// Logging types
export type LogLevel = 'trace' | 'debug' | 'info' | 'warn' | 'error' | 'fatal';

type LogEntry =
{
	level: LogLevel;
	message: string;
	timestamp: Date;
	module?: string;
	correlationId?: string;
	data?: Record<string, unknown>;
};

type LoggerOptions =
{
	level: LogLevel;
	module: string;
	enableConsole?: boolean;
	enableFile?: boolean;
	filePath?: string;
};

// Database types
type DatabaseConnectionOptions =
{
	host: string;
	port: number;
	username?: string;
	password?: string;
	database?: string;
	ssl?: boolean;
	timeout?: number;
	poolSize?: number;
};

type QueryOptions =
{
	timeout?: number;
	retries?: number;
	consistency?: 'one' | 'quorum' | 'all';
	prepare?: boolean;
};

type QueryResult<T = unknown> =
{
	rows: T[];
	rowCount: number;
	duration: number;
	metadata?: Record<string, unknown>;
};

// Event types
type EventEmitterOptions =
{
	maxListeners?: number;
	captureRejections?: boolean;
};

type EventData =
{
	type: string;
	payload: unknown;
	timestamp: Date;
	source?: string;
};

// Utility function types
export type DeepPartial<T> = {
	[P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type DeepRequired<T> = {
	[P in keyof T]-?: T[P] extends object ? DeepRequired<T[P]> : T[P];
};

export type KeysOfType<T, U> = {
	[K in keyof T]: T[K] extends U ? K : never;
}[keyof T];

export type Omit<T, K extends keyof T> = Pick<T, Exclude<keyof T, K>>;

export type PartialBy<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export type RequiredBy<T, K extends keyof T> = Omit<T, K> & Required<Pick<T, K>>;

// Branded types for type safety
export type Brand<T, B> = T & { __brand: B };

export type DomainName = Brand<string, 'DomainName'>;
export type EmailAddress = Brand<string, 'EmailAddress'>;
export type URL = Brand<string, 'URL'>;
export type UUID = Brand<string, 'UUID'>;
export type Base64 = Brand<string, 'Base64'>;
export type JSONString = Brand<string, 'JSONString'>;

// Conditional types
export type NonEmptyArray<T> = [T, ...T[]];

export type StringKeys<T> = Extract<keyof T, string>;

export type FunctionKeys<T> = {
	[K in keyof T]: T[K] extends Function ? K : never;
}[keyof T];

export type NonFunctionKeys<T> = {
	[K in keyof T]: T[K] extends Function ? never : K;
}[keyof T];

// Promise and async types
export type PromiseType<T> = T extends Promise<infer U> ? U : T;

export type AsyncReturnType<T extends (...args: any[]) => Promise<any>> = T extends (
	...args: any[]
) => Promise<infer R>
	? R
	: any;

// Object manipulation types
export type Flatten<T> = T extends object
	? T extends infer O
		? { [K in keyof O]: O[K] }
		: never
	: T;

export type Merge<T, U> = Omit<T, keyof U & keyof T> & U;

export type Override<T, U> = Omit<T, keyof U & keyof T> & U;

// Validation and transformation types
export type Validator<T> = (value: unknown) => value is T;

export type Transformer<T, U> = (value: T) => U;

export type Serializer<T> = (value: T) => string;

export type Deserializer<T> = (value: string) => T;

// Configuration and environment types
export interface EnvironmentConfig
{
	nodeEnv: 'development' | 'production' | 'test';
	logLevel: LogLevel;
	port: number;
	host: string;
}

export interface ServiceConfig
{
	name: string;
	version: string;
	environment: EnvironmentConfig;
	database: DatabaseConnectionOptions;
	cache: CacheOptions;
	logging: LoggerOptions;
}

// Export all types
export type {
	Nullable,
	Optional,
	Maybe,
	AsyncFunction,
	CallbackFunction,
	EventHandler,

	ConfigurationValueType,
	ConfigurationMapType,
	ServiceStatusType,
	ProcessingStatusType,
	PriorityType,
	Timestamp,
	Duration,
	TimeUnit,
	ErrorInfoType,
	OperationResultType,
	ValidationResultType,
	PaginationOptionsType,
	PaginatedResultType,
	RetryOptionsType,
	TimeoutOptionsType,
	MetricValueType,
	PerformanceMetrics,
	HealthCheckResult,
	CacheOptions,
	CacheEntry,
	JobOptions,
	JobProgress,
	JobResult,
	LockOptions,
	LockInfo,
	HttpRequestOptions,
	HttpResponse,
	FileInfo,
	StorageOptions,
	LogLevel,
	LogEntry,
	LoggerOptions,
	DatabaseConnectionOptions,
	QueryOptions,
	QueryResult,
	EventEmitterOptions,
	EventData,
	DeepPartial,
	DeepRequired,
	KeysOfType,
	PartialBy,
	RequiredBy,
	Brand,
	DomainName,
	EmailAddress,
	UUID,
	Base64,
	JSONString,
	NonEmptyArray,
	StringKeys,
	FunctionKeys,
	NonFunctionKeys,
	PromiseType,
	AsyncReturnType,
	ArrayElement,
	Head,
	Tail,
	Last,
	Flatten,
	Merge,
	Override,
	Validator,
	Transformer,
	Serializer,
	Deserializer,
	EnvironmentConfig,
	ServiceConfig,
};
