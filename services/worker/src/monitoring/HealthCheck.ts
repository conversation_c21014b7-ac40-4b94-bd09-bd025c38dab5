/**
 * Simple Health Check Service
 * 
 * Provides basic health monitoring without overengineering.
 * Use proper APM tools for detailed monitoring in production.
 */

interface HealthStatus {
	status: 'healthy' | 'degraded' | 'unhealthy';
	uptime: number;
	memory: {
		used: number;
		total: number;
		percentage: number;
	};
	checks: {
		database: boolean;
		redis: boolean;
		filesystem: boolean;
	};
	timestamp: number;
}

class HealthCheck 
{
	private databaseManager?: any;

	constructor(databaseManager?: any) 
	{
		this.databaseManager = databaseManager;
	}

	/**
	 * Get current health status
	 */
	async getHealthStatus(): Promise<HealthStatus> 
	{
		const memory = process.memoryUsage();
		const memoryPercentage = Math.round((memory.heapUsed / memory.heapTotal) * 100);

		const checks = {
			database: await this.checkDatabase(),
			redis: await this.checkRedis(),
			filesystem: await this.checkFilesystem(),
		};

		const allChecksPass = Object.values(checks).every(check => check);
		const isHighMemory = memoryPercentage > 85;

		let status: 'healthy' | 'degraded' | 'unhealthy';
		if (!allChecksPass) 
		{
			status = 'unhealthy';
		}
		else if (isHighMemory) 
		{
			status = 'degraded';
		}
		else 
		{
			status = 'healthy';
		}

		return {
			status,
			uptime: Math.floor(process.uptime()),
			memory: {
				used: Math.round(memory.heapUsed / 1024 / 1024), // MB
				total: Math.round(memory.heapTotal / 1024 / 1024), // MB
				percentage: memoryPercentage,
			},
			checks,
			timestamp: Date.now(),
		};
	}

	/**
	 * Check database connectivity
	 */
	private async checkDatabase(): Promise<boolean> 
	{
		if (!this.databaseManager) 
		{
			return true; // No database configured
		}

		try 
		{
			const scylla = this.databaseManager.getScyllaClient();
			await scylla.execute('SELECT now() FROM system.local LIMIT 1');
			return true;
		}
		catch 
		{
			return false;
		}
	}

	/**
	 * Check Redis connectivity
	 */
	private async checkRedis(): Promise<boolean> 
	{
		if (!this.databaseManager) 
		{
			return true; // No Redis configured
		}

		try 
		{
			const redis = this.databaseManager.getRedisClient();
			await redis.ping();
			return true;
		}
		catch 
		{
			return false;
		}
	}

	/**
	 * Check filesystem access
	 */
	private async checkFilesystem(): Promise<boolean> 
	{
		try 
		{
			const fs = await import('fs/promises');
			await fs.access('/tmp');
			return true;
		}
		catch 
		{
			return false;
		}
	}

	/**
	 * Simple metrics for basic monitoring
	 */
	getBasicMetrics() 
	{
		const memory = process.memoryUsage();
		const cpu = process.cpuUsage();

		return {
			memory: {
				heapUsed: Math.round(memory.heapUsed / 1024 / 1024), // MB
				heapTotal: Math.round(memory.heapTotal / 1024 / 1024), // MB
				external: Math.round(memory.external / 1024 / 1024), // MB
			},
			cpu: {
				user: Math.round(cpu.user / 1000), // ms
				system: Math.round(cpu.system / 1000), // ms
			},
			uptime: Math.floor(process.uptime()),
			pid: process.pid,
		};
	}
}

export { HealthCheck };
export type { HealthStatus };
