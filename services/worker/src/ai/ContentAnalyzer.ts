/**
 * Content Analyzer - AI-powered content analysis for business intelligence
 * Extracted and consolidated from domain-seeder service
 */

import type { Logger } from '@shared';
import { logger as sharedLogger } from '@shared';
import type {
	ContentAnalysisRequest,
	ContentAnalysisOptions,
	ContentAnalysisResult,
	AIProvidersConfig,
	AIFallbackConfig,
} from './types';
import { AIProviderManager } from './providers/AIProviderManager';
import { PromptManager } from './PromptManager';

const logger = sharedLogger.getLogger('ContentAnalyzer');

/**
 * Analyzes domain content for business intelligence using AI
 */
class ContentAnalyzer
{
	private readonly logger: Logger;

	private readonly providerManager: AIProviderManager;

	private readonly promptManager: PromptManager;

	private readonly defaultOptions: Required<ContentAnalysisOptions>;

	// Metrics tracking
	private readonly metrics = {
		totalAnalyses: 0,
		successfulAnalyses: 0,
		failedAnalyses: 0,
		averageProcessingTime: 0,
		confidenceScores: [] as number[],
		analysisTypes: new Map<string, number>(),
	};

	constructor(
		providersConfig: AIProvidersConfig,
		fallbackConfig?: Partial<AIFallbackConfig>,
	)
	{
		this.logger = logger;

		this.providerManager = new AIProviderManager(providersConfig, fallbackConfig);
		this.promptManager = new PromptManager();

		this.defaultOptions = {
			extractCompanyInfo: true,
			detectBusinessModel: true,
			classifyIndustry: true,
			identifyTargetAudience: true,
			extractKeywords: true,
			analyzeSentiment: true,
			timeout: 30000,
		};

		this.logger.info('Content Analyzer initialized', {
			availableProviders: this.providerManager.getAvailableProviders(),
			defaultOptions: this.defaultOptions,
		});
	}

	/**
	 * Analyze domain content for business intelligence
	 */
	async analyzeContent(request: ContentAnalysisRequest): Promise<ContentAnalysisResult>
	{
		const startTime = Date.now();
		this.metrics.totalAnalyses++;

		try
		{
			this.logger.info('Starting content analysis', {
				domain: request.domain,
				contentLength: request.content.length,
				hasTitle: !!request.title,
				hasDescription: !!request.description,
			});

			// Merge options with defaults
			const options = { ...this.defaultOptions, ...request.options };

			// Build prompts for analysis
			const { systemPrompt, userPrompt } = this.buildAnalysisPrompts(request, options);

			// Perform AI analysis
			const aiResponse = await this.providerManager.analyzeContent(
				systemPrompt,
				userPrompt,
			);

			// Process and validate the response
			const analysisResult = await this.processAnalysisResponse(
				request.domain,
				aiResponse,
				options,
			);

			// Update metrics
			const processingTime = Date.now() - startTime;
			this.updateSuccessMetrics(processingTime, analysisResult.confidence, aiResponse.provider);

			this.logger.info('Content analysis successful', {
				domain: request.domain,
				provider: aiResponse.provider,
				model: aiResponse.model,
				confidence: analysisResult.confidence,
				processingTime,
			});

			return analysisResult;
		}
		catch (error)
		{
			const processingTime = Date.now() - startTime;
			this.updateFailureMetrics(processingTime);

			this.logger.error('Content analysis failed', {
				domain: request.domain,
				error: error.message,
				processingTime,
			});

			throw error;
		}
	}

	/**
	 * Analyze multiple domains in batch
	 */
	async analyzeBatch(
		requests: ContentAnalysisRequest[],
		concurrency: number = 3,
	): Promise<Map<string, ContentAnalysisResult | Error>>
	{
		const results = new Map<string, ContentAnalysisResult | Error>();

		this.logger.info('Starting batch content analysis', {
			requestCount: requests.length,
			concurrency,
		});

		// Process requests in batches to control concurrency
		const batches = this.createBatches(requests, concurrency);

		for (const batch of batches)
		{
			const batchPromises = batch.map(async (request) =>
			{
				try
				{
					const result = await this.analyzeContent(request);
					return { domain: request.domain, result };
				}
				catch (error)
				{
					return { domain: request.domain, result: error as Error };
				}
			});

			const batchResults = await Promise.allSettled(batchPromises);

			for (const result of batchResults)
			{
				if (result.status === 'fulfilled')
				{
					results.set(result.value.domain, result.value.result);
				}
			}
		}

		this.logger.info('Batch content analysis completed', {
			requestCount: requests.length,
			successCount: Array.from(results.values()).filter(r => !(r instanceof Error)).length,
			failureCount: Array.from(results.values()).filter(r => r instanceof Error).length,
		});

		return results;
	}

	/**
	 * Extract company information from content
	 */
	async extractCompanyInfo(domain: string, content: string): Promise<ContentAnalysisResult['companyInfo']>
	{
		const request: ContentAnalysisRequest = {
			domain,
			content,
			options: {
				extractCompanyInfo: true,
				detectBusinessModel: false,
				classifyIndustry: false,
				identifyTargetAudience: false,
				extractKeywords: false,
				analyzeSentiment: false,
			},
		};

		const result = await this.analyzeContent(request);
		return result.companyInfo;
	}

	/**
	 * Detect business model from content
	 */
	async detectBusinessModel(domain: string, content: string): Promise<ContentAnalysisResult['businessModel']>
	{
		const request: ContentAnalysisRequest = {
			domain,
			content,
			options: {
				extractCompanyInfo: false,
				detectBusinessModel: true,
				classifyIndustry: false,
				identifyTargetAudience: false,
				extractKeywords: false,
				analyzeSentiment: false,
			},
		};

		const result = await this.analyzeContent(request);
		return result.businessModel;
	}

	/**
	 * Classify industry from content
	 */
	async classifyIndustry(domain: string, content: string): Promise<ContentAnalysisResult['industryClassification']>
	{
		const request: ContentAnalysisRequest = {
			domain,
			content,
			options: {
				extractCompanyInfo: false,
				detectBusinessModel: false,
				classifyIndustry: true,
				identifyTargetAudience: false,
				extractKeywords: false,
				analyzeSentiment: false,
			},
		};

		const result = await this.analyzeContent(request);
		return result.industryClassification;
	}

	/**
	 * Identify target audience from content
	 */
	async identifyTargetAudience(domain: string, content: string): Promise<ContentAnalysisResult['targetAudience']>
	{
		const request: ContentAnalysisRequest = {
			domain,
			content,
			options: {
				extractCompanyInfo: false,
				detectBusinessModel: false,
				classifyIndustry: false,
				identifyTargetAudience: true,
				extractKeywords: false,
				analyzeSentiment: false,
			},
		};

		const result = await this.analyzeContent(request);
		return result.targetAudience;
	}

	/**
	 * Build analysis prompts
	 */
	private buildAnalysisPrompts(
		request: ContentAnalysisRequest,
		options: Required<ContentAnalysisOptions>,
	): { systemPrompt: string; userPrompt: string }
	{
		// Get content analysis template
		const template = this.promptManager.getContentAnalysisTemplate();

		// Build context for prompt
		const context = {
			domain: request.domain,
			title: request.title || '',
			description: request.description || '',
			content: request.content,
			extractCompanyInfo: options.extractCompanyInfo.toString(),
			detectBusinessModel: options.detectBusinessModel.toString(),
			classifyIndustry: options.classifyIndustry.toString(),
			identifyTargetAudience: options.identifyTargetAudience.toString(),
			extractKeywords: options.extractKeywords.toString(),
			analyzeSentiment: options.analyzeSentiment.toString(),
		};

		// Render prompts with context
		const systemPrompt = this.promptManager.renderTemplate(template.systemPrompt, context);
		const userPrompt = this.promptManager.renderTemplate(template.userTemplate, context);

		return { systemPrompt, userPrompt };
	}

	/**
	 * Process AI analysis response
	 */
	private async processAnalysisResponse(
		domain: string,
		aiResponse: any,
		options: Required<ContentAnalysisOptions>,
	): Promise<ContentAnalysisResult>
	{
		// Extract content from AI response
		const rawContent = aiResponse.content;

		// Validate response structure
		this.validateAnalysisResponse(rawContent);

		// Calculate confidence based on completeness and quality
		const confidence = this.calculateAnalysisConfidence(rawContent, options);

		// Create analysis result
		const analysisResult: ContentAnalysisResult = {
			companyInfo: rawContent.companyInfo,
			businessModel: rawContent.businessModel,
			industryClassification: rawContent.industryClassification,
			targetAudience: rawContent.targetAudience,
			keywords: rawContent.keywords,
			sentiment: rawContent.sentiment,
			confidence,
			metadata: {
				provider: aiResponse.provider,
				model: aiResponse.model,
				analyzedAt: aiResponse.timestamp,
				processingTime: 0, // Will be set by caller
			},
		};

		return analysisResult;
	}

	/**
	 * Validate AI analysis response structure
	 */
	private validateAnalysisResponse(content: any): void
	{
		if (!content || typeof content !== 'object')
		{
			throw new Error('AI analysis response must be a valid object');
		}

		// Validate optional sections exist if they should
		if (content.companyInfo && typeof content.companyInfo !== 'object')
		{
			throw new Error('companyInfo must be an object');
		}

		if (content.businessModel && typeof content.businessModel !== 'object')
		{
			throw new Error('businessModel must be an object');
		}

		if (content.industryClassification && typeof content.industryClassification !== 'object')
		{
			throw new Error('industryClassification must be an object');
		}

		if (content.targetAudience && typeof content.targetAudience !== 'object')
		{
			throw new Error('targetAudience must be an object');
		}

		if (content.keywords && typeof content.keywords !== 'object')
		{
			throw new Error('keywords must be an object');
		}

		if (content.sentiment && typeof content.sentiment !== 'object')
		{
			throw new Error('sentiment must be an object');
		}
	}

	/**
	 * Calculate analysis confidence based on completeness and quality
	 */
	private calculateAnalysisConfidence(content: any, options: Required<ContentAnalysisOptions>): number
	{
		let confidence = 0.5; // Base confidence
		let maxPossibleScore = 0.5;

		// Check each requested analysis type
		if (options.extractCompanyInfo)
		{
			maxPossibleScore += 0.15;
			if (content.companyInfo?.name && content.companyInfo?.description)
			{
				confidence += 0.15;
			}
			else if (content.companyInfo?.name || content.companyInfo?.description)
			{
				confidence += 0.075;
			}
		}

		if (options.detectBusinessModel)
		{
			maxPossibleScore += 0.1;
			if (content.businessModel?.type && content.businessModel?.valueProposition)
			{
				confidence += 0.1;
			}
			else if (content.businessModel?.type)
			{
				confidence += 0.05;
			}
		}

		if (options.classifyIndustry)
		{
			maxPossibleScore += 0.1;
			if (content.industryClassification?.primary && content.industryClassification?.confidence > 0.7)
			{
				confidence += 0.1;
			}
			else if (content.industryClassification?.primary)
			{
				confidence += 0.05;
			}
		}

		if (options.identifyTargetAudience)
		{
			maxPossibleScore += 0.1;
			if (content.targetAudience?.demographics?.length > 0 || content.targetAudience?.businessSegments?.length > 0)
			{
				confidence += 0.1;
			}
		}

		if (options.extractKeywords)
		{
			maxPossibleScore += 0.1;
			if (content.keywords?.primary?.length > 0)
			{
				confidence += 0.1;
			}
		}

		if (options.analyzeSentiment)
		{
			maxPossibleScore += 0.05;
			if (content.sentiment?.overall && content.sentiment?.score !== undefined)
			{
				confidence += 0.05;
			}
		}

		// Normalize confidence to 0-1 scale
		return Math.min(1, confidence / maxPossibleScore);
	}

	/**
	 * Update success metrics
	 */
	private updateSuccessMetrics(
		processingTime: number,
		confidence: number,
		provider: string,
	): void
	{
		this.metrics.successfulAnalyses++;

		// Update average processing time
		const totalAnalyses = this.metrics.successfulAnalyses + this.metrics.failedAnalyses;
		this.metrics.averageProcessingTime =
			(this.metrics.averageProcessingTime * (totalAnalyses - 1) + processingTime) / totalAnalyses;

		// Track confidence scores
		this.metrics.confidenceScores.push(confidence);
		if (this.metrics.confidenceScores.length > 1000)
		{
			this.metrics.confidenceScores.shift(); // Keep last 1000 scores
		}
	}

	/**
	 * Update failure metrics
	 */
	private updateFailureMetrics(processingTime: number): void
	{
		this.metrics.failedAnalyses++;

		// Update average processing time
		const totalAnalyses = this.metrics.successfulAnalyses + this.metrics.failedAnalyses;
		this.metrics.averageProcessingTime =
			(this.metrics.averageProcessingTime * (totalAnalyses - 1) + processingTime) / totalAnalyses;
	}

	/**
	 * Create batches for concurrent processing
	 */
	private createBatches<T>(items: T[], batchSize: number): T[][]
	{
		const batches: T[][] = [];
		for (let i = 0; i < items.length; i += batchSize)
		{
			batches.push(items.slice(i, i + batchSize));
		}
		return batches;
	}

	/**
	 * Get analysis metrics
	 */
	getMetrics(): typeof this.metrics & {
		averageConfidence: number;
		successRate: number;
	}
	{
		const averageConfidence = this.metrics.confidenceScores.length > 0
			? this.metrics.confidenceScores.reduce((sum, score) => sum + score, 0) / this.metrics.confidenceScores.length
			: 0;

		const successRate = this.metrics.totalAnalyses > 0
			? this.metrics.successfulAnalyses / this.metrics.totalAnalyses
			: 0;

		return {
			...this.metrics,
			averageConfidence,
			successRate,
		};
	}

	/**
	 * Test AI providers for content analysis
	 */
	async testProviders(): Promise<Map<string, boolean>>
	{
		const results = new Map<string, boolean>();
		const providers = this.providerManager.getAvailableProviders();

		this.logger.info('Testing AI providers for content analysis', { providers });

		for (const providerId of providers)
		{
			try
			{
				// Simple test analysis
				const testRequest: ContentAnalysisRequest = {
					domain: 'test.com',
					content: 'This is a test website for a technology company that provides software solutions.',
					options: {
						extractCompanyInfo: true,
						detectBusinessModel: false,
						classifyIndustry: true,
						identifyTargetAudience: false,
						extractKeywords: false,
						analyzeSentiment: false,
						timeout: 10000,
					},
				};

				await this.analyzeContent(testRequest);
				results.set(providerId, true);

				this.logger.info('AI provider analysis test successful', { providerId });
			}
			catch (error)
			{
				results.set(providerId, false);

				this.logger.error('AI provider analysis test failed', {
					providerId,
					error: error.message,
				});
			}
		}

		return results;
	}

	/**
	 * Shutdown the analyzer
	 */
	async shutdown(): Promise<void>
	{
		this.logger.info('Shutting down Content Analyzer');

		await this.providerManager.shutdown();

		// Clear metrics
		this.metrics.confidenceScores.length = 0;
		this.metrics.analysisTypes.clear();

		this.logger.info('Content Analyzer shutdown complete');
	}
}

export { ContentAnalyzer };
