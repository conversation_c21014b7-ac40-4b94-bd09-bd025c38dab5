/**
 * Prompt Manager - Manages AI prompts and templates
 * Extracted and consolidated from domain-seeder service
 */

import type { Logger } from '@shared';
import { logger as sharedLogger } from '@shared';
import type { PromptTemplate, PromptContext } from './types';

const logger = sharedLogger.getLogger('PromptManager');

/**
 * Manages AI prompts and template rendering
 */
class PromptManager
{
	private readonly logger: Logger;

	private readonly templates = new Map<string, PromptTemplate>();

	constructor()
	{
		this.logger = logger;
		this.initializeDefaultTemplates();
	}

	/**
	 * Initialize default prompt templates
	 */
	private initializeDefaultTemplates(): void
	{
		// Content Generation Template
		this.addTemplate({
			id: 'content_generation_v1',
			name: 'Domain Content Generation',
			description: 'Generate comprehensive domain descriptions with SEO optimization',
			systemPrompt: `You are an expert content generator specializing in creating accurate, engaging domain descriptions. Your goal is to produce high-quality, SEO-optimized content that accurately represents the domain's purpose and value proposition.

Rules:
1. Generate factual, brand-aligned content
2. Keep descriptions concise but informative
3. Include relevant keywords naturally
4. Focus on user value and benefits
5. Maintain professional tone
6. Return ONLY valid JSON matching the specified schema

Response Format:
{
  "summary": "string ({{minWords}}-{{maxWords}} words)",
  "keyFeatures": ["string", "string", "string"],
  "audience": ["string", "string", "string"],
  "useCases": ["string", "string", "string"],
  "keywords": ["string", "string", "string"],
  "category": {
    "primary": "string",
    "secondary": "string"
  }
}`,
			userTemplate: `Generate content for the following domain:

Domain: {{domain}}

{{#if brandInfo}}
Brand Information:
{{brandInfo}}
{{/if}}

{{#if domainAnalysis}}
Domain Analysis:
{{domainAnalysis}}
{{/if}}

{{#if webContext}}
Web Context:
{{webContext}}
{{/if}}

Requirements:
- Summary: {{minWords}}-{{maxWords}} words
- Language: {{language}}
- SEO Optimization: {{seoOptimization}}
- Include Keywords: {{includeKeywords}}
- Include Audience: {{includeAudience}}
- Include Use Cases: {{includeUseCases}}

Return ONLY valid JSON matching the schema above. No code fences or additional text.`,
			variables: [
				'domain', 'brandInfo', 'domainAnalysis', 'webContext',
				'minWords', 'maxWords', 'language', 'seoOptimization',
				'includeKeywords', 'includeAudience', 'includeUseCases',
			],
			version: '1.0.0',
			createdAt: new Date().toISOString(),
			updatedAt: new Date().toISOString(),
		});

		// Content Analysis Template
		this.addTemplate({
			id: 'content_analysis_v1',
			name: 'Domain Content Analysis',
			description: 'Analyze domain content for business intelligence',
			systemPrompt: `You are an expert business analyst specializing in web content analysis. Your goal is to extract meaningful business intelligence from domain content, including company information, business models, industry classification, and target audience identification.

Rules:
1. Analyze content objectively and factually
2. Extract only information that can be reasonably inferred
3. Provide confidence scores for classifications
4. Focus on business-relevant insights
5. Return ONLY valid JSON matching the specified schema

Response Format:
{
  "companyInfo": {
    "name": "string",
    "description": "string",
    "industry": "string",
    "size": "string",
    "founded": "string",
    "headquarters": "string"
  },
  "businessModel": {
    "type": "string",
    "revenue": ["string"],
    "targetMarket": "string",
    "valueProposition": "string"
  },
  "industryClassification": {
    "primary": "string",
    "secondary": ["string"],
    "confidence": 0.0
  },
  "targetAudience": {
    "demographics": ["string"],
    "psychographics": ["string"],
    "businessSegments": ["string"]
  },
  "keywords": {
    "primary": ["string"],
    "secondary": ["string"],
    "branded": ["string"]
  },
  "sentiment": {
    "overall": "positive|neutral|negative",
    "score": 0.0,
    "aspects": [
      {
        "aspect": "string",
        "sentiment": "positive|neutral|negative",
        "score": 0.0
      }
    ]
  }
}`,
			userTemplate: `Analyze the following domain content:

Domain: {{domain}}

{{#if title}}
Title: {{title}}
{{/if}}

{{#if description}}
Description: {{description}}
{{/if}}

Content:
{{content}}

Analysis Requirements:
- Extract company information: {{extractCompanyInfo}}
- Detect business model: {{detectBusinessModel}}
- Classify industry: {{classifyIndustry}}
- Identify target audience: {{identifyTargetAudience}}
- Extract keywords: {{extractKeywords}}
- Analyze sentiment: {{analyzeSentiment}}

Return ONLY valid JSON matching the schema above. No code fences or additional text.`,
			variables: [
				'domain', 'title', 'description', 'content',
				'extractCompanyInfo', 'detectBusinessModel', 'classifyIndustry',
				'identifyTargetAudience', 'extractKeywords', 'analyzeSentiment',
			],
			version: '1.0.0',
			createdAt: new Date().toISOString(),
			updatedAt: new Date().toISOString(),
		});

		// Industry Classification Template
		this.addTemplate({
			id: 'industry_classification_v1',
			name: 'Industry Classification',
			description: 'Classify domains into industry categories',
			systemPrompt: `You are an expert industry analyst specializing in business classification. Your goal is to accurately classify domains into appropriate industry categories based on available information.

Rules:
1. Use standard industry classification systems (NAICS, SIC)
2. Provide primary and secondary classifications
3. Include confidence scores
4. Consider domain name, content, and business model
5. Return ONLY valid JSON matching the specified schema

Response Format:
{
  "primary": {
    "code": "string",
    "name": "string",
    "description": "string",
    "confidence": 0.0
  },
  "secondary": [
    {
      "code": "string",
      "name": "string",
      "description": "string",
      "confidence": 0.0
    }
  ],
  "reasoning": "string",
  "alternativeClassifications": ["string"]
}`,
			userTemplate: `Classify the following domain into industry categories:

Domain: {{domain}}

{{#if domainAnalysis}}
Domain Analysis:
{{domainAnalysis}}
{{/if}}

{{#if businessInfo}}
Business Information:
{{businessInfo}}
{{/if}}

{{#if contentSummary}}
Content Summary:
{{contentSummary}}
{{/if}}

Return ONLY valid JSON matching the schema above. No code fences or additional text.`,
			variables: ['domain', 'domainAnalysis', 'businessInfo', 'contentSummary'],
			version: '1.0.0',
			createdAt: new Date().toISOString(),
			updatedAt: new Date().toISOString(),
		});

		this.logger.info('Default prompt templates initialized', {
			templateCount: this.templates.size,
			templates: Array.from(this.templates.keys()),
		});
	}

	/**
	 * Add a new prompt template
	 */
	addTemplate(template: PromptTemplate): void
	{
		this.templates.set(template.id, {
			...template,
			updatedAt: new Date().toISOString(),
		});

		this.logger.debug('Prompt template added', {
			id: template.id,
			name: template.name,
			version: template.version,
		});
	}

	/**
	 * Get a prompt template by ID
	 */
	getTemplate(templateId: string): PromptTemplate | null
	{
		return this.templates.get(templateId) || null;
	}

	/**
	 * Get content generation template for a specific language
	 */
	getContentGenerationTemplate(language: string = 'en'): PromptTemplate
	{
		// For now, return the default template
		// In the future, we could have language-specific templates
		const template = this.getTemplate('content_generation_v1');
		if (!template)
		{
			throw new Error('Content generation template not found');
		}
		return template;
	}

	/**
	 * Get content analysis template
	 */
	getContentAnalysisTemplate(): PromptTemplate
	{
		const template = this.getTemplate('content_analysis_v1');
		if (!template)
		{
			throw new Error('Content analysis template not found');
		}
		return template;
	}

	/**
	 * Get industry classification template
	 */
	getIndustryClassificationTemplate(): PromptTemplate
	{
		const template = this.getTemplate('industry_classification_v1');
		if (!template)
		{
			throw new Error('Industry classification template not found');
		}
		return template;
	}

	/**
	 * Render a template with context variables
	 */
	renderTemplate(template: string, context: PromptContext): string
	{
		let rendered = template;

		// Handle simple {{variable}} replacements
		rendered = rendered.replace(/\{\{(\w+)\}\}/g, (match, variable) =>
		{
			const value = context[variable];
			return value !== undefined ? String(value) : match;
		});

		// Handle {{#if variable}} conditionals
		rendered = rendered.replace(/\{\{#if\s+(\w+)\}\}(.*?)\{\{\/if\}\}/gs, (match, variable, content) =>
		{
			const value = context[variable];
			const shouldInclude = value && value !== 'false' && value !== '0' && value !== '';
			return shouldInclude ? content : '';
		});

		// Handle {{#unless variable}} conditionals
		rendered = rendered.replace(/\{\{#unless\s+(\w+)\}\}(.*?)\{\{\/unless\}\}/gs, (match, variable, content) =>
		{
			const value = context[variable];
			const shouldInclude = !value || value === 'false' || value === '0' || value === '';
			return shouldInclude ? content : '';
		});

		return rendered.trim();
	}

	/**
	 * Validate template variables against context
	 */
	validateTemplateContext(template: PromptTemplate, context: PromptContext): {
		valid: boolean;
		missingVariables: string[];
		extraVariables: string[];
	}
	{
		const contextKeys = Object.keys(context);
		const missingVariables = template.variables.filter(variable => !(variable in context));
		const extraVariables = contextKeys.filter(key => !template.variables.includes(key));

		return {
			valid: missingVariables.length === 0,
			missingVariables,
			extraVariables,
		};
	}

	/**
	 * Get all available templates
	 */
	getAllTemplates(): PromptTemplate[]
	{
		return Array.from(this.templates.values());
	}

	/**
	 * Update an existing template
	 */
	updateTemplate(templateId: string, updates: Partial<PromptTemplate>): boolean
	{
		const existing = this.templates.get(templateId);
		if (!existing)
		{
			return false;
		}

		const updated = {
			...existing,
			...updates,
			id: templateId, // Prevent ID changes
			updatedAt: new Date().toISOString(),
		};

		this.templates.set(templateId, updated);

		this.logger.info('Prompt template updated', {
			id: templateId,
			version: updated.version,
		});

		return true;
	}

	/**
	 * Delete a template
	 */
	deleteTemplate(templateId: string): boolean
	{
		const deleted = this.templates.delete(templateId);

		if (deleted)
		{
			this.logger.info('Prompt template deleted', { id: templateId });
		}

		return deleted;
	}

	/**
	 * Export templates for backup/migration
	 */
	exportTemplates(): PromptTemplate[]
	{
		return this.getAllTemplates();
	}

	/**
	 * Import templates from backup/migration
	 */
	importTemplates(templates: PromptTemplate[]): {
		imported: number;
		skipped: number;
		errors: string[];
	}
	{
		let imported = 0;
		let skipped = 0;
		const errors: string[] = [];

		for (const template of templates)
		{
			try
			{
				// Validate template structure
				if (!template.id || !template.systemPrompt || !template.userTemplate)
				{
					errors.push(`Invalid template structure: ${template.id || 'unknown'}`);
					continue;
				}

				// Check if template already exists
				if (this.templates.has(template.id))
				{
					skipped++;
					continue;
				}

				this.addTemplate(template);
				imported++;
			}
			catch (error)
			{
				errors.push(`Error importing template ${template.id}: ${error.message}`);
			}
		}

		this.logger.info('Templates import completed', {
			imported,
			skipped,
			errorCount: errors.length,
		});

		return { imported, skipped, errors };
	}

	/**
	 * Get template usage statistics
	 */
	getTemplateStats(): Array<{
		id: string;
		name: string;
		version: string;
		variableCount: number;
		createdAt: string;
		updatedAt: string;
	}>
	{
		return Array.from(this.templates.values()).map(template => ({
			id: template.id,
			name: template.name,
			version: template.version,
			variableCount: template.variables.length,
			createdAt: template.createdAt,
			updatedAt: template.updatedAt,
		}));
	}
}

export { PromptManager };
