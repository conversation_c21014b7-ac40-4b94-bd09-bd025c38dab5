/**
 * Domain Description Generator Tests
 * Tests for AI-powered content generation functionality
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import type { AIProvidersConfig, ContentGenerationRequest } from '../types';
import { DomainDescriptionGenerator } from '../DomainDescriptionGenerator';

// Mock AI SDK modules
vi.mock('@ai-sdk/openai', () => ({
	openai: vi.fn(() => ({})),
}));

vi.mock('@ai-sdk/anthropic', () => ({
	anthropic: vi.fn(() => ({})),
}));

vi.mock('@ai-sdk/google', () => ({
	google: vi.fn(() => ({})),
}));

vi.mock('ai', () => ({
	generateText: vi.fn(),
}));

describe('DomainDescriptionGenerator', () =>
{
	let generator: DomainDescriptionGenerator;
	let mockProvidersConfig: AIProvidersConfig;

	beforeEach(() =>
	{
		mockProvidersConfig = {
			'test-openai': {
				type: 'openai',
				model: 'gpt-4o-mini',
				apiKey: 'test-key',
				enabled: true,
			},
		};

		generator = new DomainDescriptionGenerator(mockProvidersConfig);
	});

	afterEach(async () =>
	{
		await generator.shutdown();
	});

	describe('generateDescription', () =>
	{
		it('should generate domain description successfully', async () =>
		{
			// Mock successful AI response
			const { generateText } = await import('ai');
			vi.mocked(generateText).mockResolvedValue({
				text: JSON.stringify({
					summary: 'Test domain provides excellent services for users.',
					keyFeatures: ['Feature 1', 'Feature 2', 'Feature 3'],
					audience: ['Developers', 'Businesses'],
					useCases: ['Use case 1', 'Use case 2'],
					keywords: ['test', 'domain', 'services'],
					category: {
						primary: 'Technology',
						secondary: 'Software',
					},
				}),
				usage: {
					promptTokens: 100,
					completionTokens: 50,
					totalTokens: 150,
				},
			});

			const request: ContentGenerationRequest = {
				domain: 'test.com',
				brandInfo: {
					name: 'Test Company',
					categoryPrimary: 'Technology',
					tags: ['software', 'development'],
				},
				options: {
					minWords: 10,
					maxWords: 50,
				},
			};

			const result = await generator.generateDescription(request);

			expect(result).toBeDefined();
			expect(result.summary).toBe('Test domain provides excellent services for users.');
			expect(result.keyFeatures).toHaveLength(3);
			expect(result.audience).toHaveLength(2);
			expect(result.useCases).toHaveLength(2);
			expect(result.keywords).toHaveLength(3);
			expect(result.category?.primary).toBe('Technology');
			expect(result.confidence).toBeGreaterThan(0);
			expect(result.metadata.provider).toBe('test-openai');
		});

		it('should handle AI provider errors gracefully', async () =>
		{
			// Mock AI provider error
			const { generateText } = await import('ai');
			vi.mocked(generateText).mockRejectedValue(new Error('API Error'));

			const request: ContentGenerationRequest = {
				domain: 'test.com',
				options: {
					minWords: 10,
					maxWords: 50,
				},
			};

			await expect(generator.generateDescription(request)).rejects.toThrow();
		});

		it('should validate AI response structure', async () =>
		{
			// Mock invalid AI response
			const { generateText } = await import('ai');
			vi.mocked(generateText).mockResolvedValue({
				text: JSON.stringify({
					// Missing required summary field
					keyFeatures: ['Feature 1'],
				}),
				usage: {
					promptTokens: 100,
					completionTokens: 50,
					totalTokens: 150,
				},
			});

			const request: ContentGenerationRequest = {
				domain: 'test.com',
				options: {
					minWords: 10,
					maxWords: 50,
				},
			};

			await expect(generator.generateDescription(request)).rejects.toThrow('AI response must include a summary string');
		});
	});

	describe('generateBatch', () =>
	{
		it('should generate descriptions for multiple domains', async () =>
		{
			// Mock successful AI responses
			const { generateText } = await import('ai');
			vi.mocked(generateText).mockResolvedValue({
				text: JSON.stringify({
					summary: 'Test domain provides excellent services.',
					keyFeatures: ['Feature 1'],
					audience: ['Users'],
					useCases: ['Use case 1'],
					keywords: ['test'],
				}),
				usage: {
					promptTokens: 100,
					completionTokens: 50,
					totalTokens: 150,
				},
			});

			const requests: ContentGenerationRequest[] = [
				{ domain: 'test1.com', options: { minWords: 10, maxWords: 50 } },
				{ domain: 'test2.com', options: { minWords: 10, maxWords: 50 } },
			];

			const results = await generator.generateBatch(requests, 2);

			expect(results.size).toBe(2);
			expect(results.has('test1.com')).toBe(true);
			expect(results.has('test2.com')).toBe(true);

			const result1 = results.get('test1.com');
			expect(result1).toBeDefined();
			expect(result1).not.toBeInstanceOf(Error);
		});

		it('should handle partial failures in batch processing', async () =>
		{
			// Mock mixed success/failure responses
			const { generateText } = await import('ai');
			vi.mocked(generateText)
				.mockResolvedValueOnce({
					text: JSON.stringify({
						summary: 'Success response',
						keyFeatures: ['Feature 1'],
						audience: ['Users'],
						useCases: ['Use case 1'],
						keywords: ['test'],
					}),
					usage: { promptTokens: 100, completionTokens: 50, totalTokens: 150 },
				})
				.mockRejectedValueOnce(new Error('API Error'));

			const requests: ContentGenerationRequest[] = [
				{ domain: 'success.com', options: { minWords: 10, maxWords: 50 } },
				{ domain: 'failure.com', options: { minWords: 10, maxWords: 50 } },
			];

			const results = await generator.generateBatch(requests, 2);

			expect(results.size).toBe(2);

			const successResult = results.get('success.com');
			expect(successResult).toBeDefined();
			expect(successResult).not.toBeInstanceOf(Error);

			const failureResult = results.get('failure.com');
			expect(failureResult).toBeDefined();
			expect(failureResult).toBeInstanceOf(Error);
		});
	});

	describe('getMetrics', () =>
	{
		it('should return initial metrics', () =>
		{
			const metrics = generator.getMetrics();

			expect(metrics.totalRequests).toBe(0);
			expect(metrics.successfulGenerations).toBe(0);
			expect(metrics.failedGenerations).toBe(0);
			expect(metrics.averageProcessingTime).toBe(0);
			expect(metrics.averageQualityScore).toBe(0);
			expect(metrics.successRate).toBe(0);
			expect(metrics.providerDistribution).toHaveLength(0);
		});

		it('should update metrics after successful generation', async () =>
		{
			// Mock successful AI response
			const { generateText } = await import('ai');
			vi.mocked(generateText).mockResolvedValue({
				text: JSON.stringify({
					summary: 'Test domain provides excellent services.',
					keyFeatures: ['Feature 1'],
					audience: ['Users'],
					useCases: ['Use case 1'],
					keywords: ['test'],
				}),
				usage: { promptTokens: 100, completionTokens: 50, totalTokens: 150 },
			});

			const request: ContentGenerationRequest = {
				domain: 'test.com',
				options: { minWords: 10, maxWords: 50 },
			};

			await generator.generateDescription(request);

			const metrics = generator.getMetrics();

			expect(metrics.totalRequests).toBe(1);
			expect(metrics.successfulGenerations).toBe(1);
			expect(metrics.failedGenerations).toBe(0);
			expect(metrics.successRate).toBe(1);
			expect(metrics.averageProcessingTime).toBeGreaterThan(0);
		});
	});

	describe('testProviders', () =>
	{
		it('should test provider connectivity', async () =>
		{
			// Mock successful test response
			const { generateText } = await import('ai');
			vi.mocked(generateText).mockResolvedValue({
				text: JSON.stringify({
					summary: 'Test response',
					keyFeatures: [],
					audience: [],
					useCases: [],
					keywords: [],
				}),
				usage: { promptTokens: 10, completionTokens: 5, totalTokens: 15 },
			});

			const results = await generator.testProviders();

			expect(results.size).toBe(1);
			expect(results.get('test-openai')).toBe(true);
		});

		it('should handle provider test failures', async () =>
		{
			// Mock test failure
			const { generateText } = await import('ai');
			vi.mocked(generateText).mockRejectedValue(new Error('Connection failed'));

			const results = await generator.testProviders();

			expect(results.size).toBe(1);
			expect(results.get('test-openai')).toBe(false);
		});
	});
});
