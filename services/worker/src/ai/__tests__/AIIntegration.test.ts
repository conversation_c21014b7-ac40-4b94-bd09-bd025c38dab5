/**
 * AI Integration Tests
 *
 * Tests for AI service integration and content generation functionality.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { DomainDescriptionGenerator } from '../DomainDescriptionGenerator';
import { ContentAnalyzer } from '../ContentAnalyzer';
import { ContentQualityValidator } from '../ContentQualityValidator';
import { AIProviderManager } from '../providers/AIProviderManager';
import type { DomainDataType } from '../types';

// Mock AI providers
const mockOpenAI = {
	generateCompletion: vi.fn(),
	generateEmbedding: vi.fn(),
};

const mockAnthropic = {
	generateCompletion: vi.fn(),
};

vi.mock('../providers/OpenAIProvider', () => ({
	OpenAIProvider: vi.fn(() => mockOpenAI),
}));

vi.mock('../providers/AnthropicProvider', () => ({
	AnthropicProvider: vi.fn(() => mockAnthropic),
}));

describe('AI Integration Tests', () =>
{
	let descriptionGenerator: DomainDescriptionGenerator;
	let contentAnalyzer: ContentAnalyzer;
	let qualityValidator: ContentQualityValidator;
	let providerManager: AIProviderManager;

	beforeEach(() =>
	{
		vi.clearAllMocks();

		providerManager = new AIProviderManager();
		descriptionGenerator = new DomainDescriptionGenerator(providerManager);
		contentAnalyzer = new ContentAnalyzer(providerManager);
		qualityValidator = new ContentQualityValidator(providerManager);
	});

	afterEach(() =>
	{
		vi.restoreAllMocks();
	});

	describe('Domain Description Generation', () =>
	{
		it('should generate high-quality domain descriptions', async () =>
		{
			const domainData: DomainDataType = {
				domain: 'example-tech.com',
				homepage: {
					title: 'Example Tech - Innovative Software Solutions',
					description: 'Leading provider of cutting-edge software solutions for modern businesses.',
					content: 'We specialize in cloud computing, AI, and digital transformation services.',
				},
				category: 'technology',
				keywords: ['software', 'cloud', 'AI', 'digital transformation'],
			};

			mockOpenAI.generateCompletion.mockResolvedValue({
				text: 'Example Tech is a forward-thinking technology company that specializes in delivering innovative software solutions. With expertise in cloud computing, artificial intelligence, and digital transformation, they help modern businesses optimize their operations and achieve sustainable growth.',
				usage: { tokens: 150 },
			});

			const result = await descriptionGenerator.generateDescription(domainData);

			expect(result.success).toBe(true);
			expect(result.description).toBeDefined();
			expect(result.description.length).toBeGreaterThan(100);
			expect(result.confidence).toBeGreaterThan(0.8);
			expect(result.metadata.provider).toBe('openai');
			expect(result.metadata.tokensUsed).toBe(150);
		});

		it('should handle AI provider failures gracefully', async () =>
		{
			const domainData: DomainDataType = {
				domain: 'failure-test.com',
				homepage: {
					title: 'Test Domain',
					description: 'Test description',
					content: 'Test content',
				},
			};

			mockOpenAI.generateCompletion.mockRejectedValue(new Error('API rate limit exceeded'));

			const result = await descriptionGenerator.generateDescription(domainData);

			expect(result.success).toBe(false);
			expect(result.error).toContain('API rate limit exceeded');
			expect(result.fallbackUsed).toBe(true);
			expect(result.description).toBeDefined(); // Should have fallback description
		});

		it('should use fallback providers when primary fails', async () =>
		{
			const domainData: DomainDataType = {
				domain: 'fallback-test.com',
				homepage: {
					title: 'Fallback Test',
					description: 'Testing fallback functionality',
					content: 'Content for fallback testing',
				},
			};

			// Primary provider fails
			mockOpenAI.generateCompletion.mockRejectedValue(new Error('Service unavailable'));

			// Fallback provider succeeds
			mockAnthropic.generateCompletion.mockResolvedValue({
				text: 'Generated description using fallback provider.',
				usage: { tokens: 120 },
			});

			const result = await descriptionGenerator.generateDescription(domainData);

			expect(result.success).toBe(true);
			expect(result.description).toBe('Generated description using fallback provider.');
			expect(result.metadata.provider).toBe('anthropic');
			expect(result.metadata.fallbackUsed).toBe(true);
		});

		it('should optimize prompts based on domain category', async () =>
		{
			const techDomain: DomainDataType = {
				domain: 'tech-company.com',
				category: 'technology',
				homepage: { title: 'Tech Company', description: 'Software solutions', content: 'AI and cloud services' },
			};

			const ecommerceDomain: DomainDataType = {
				domain: 'shop-online.com',
				category: 'ecommerce',
				homepage: { title: 'Online Shop', description: 'Buy products online', content: 'Wide selection of products' },
			};

			mockOpenAI.generateCompletion.mockResolvedValue({
				text: 'Category-optimized description',
				usage: { tokens: 100 },
			});

			await descriptionGenerator.generateDescription(techDomain);
			await descriptionGenerator.generateDescription(ecommerceDomain);

			// Verify different prompts were used based on category
			expect(mockOpenAI.generateCompletion).toHaveBeenCalledTimes(2);

			const techCall = mockOpenAI.generateCompletion.mock.calls[0][0];
			const ecommerceCall = mockOpenAI.generateCompletion.mock.calls[1][0];

			expect(techCall.prompt).toContain('technology');
			expect(ecommerceCall.prompt).toContain('ecommerce');
		});
	});

	describe('Content Analysis', () =>
	{
		it('should analyze content sentiment and topics', async () =>
		{
			const content = 'We are excited to announce our new innovative product that will revolutionize the industry. Our cutting-edge technology provides exceptional value to customers.';

			mockOpenAI.generateCompletion.mockResolvedValue({
				text: JSON.stringify({
					sentiment: 'positive',
					confidence: 0.92,
					topics: ['innovation', 'technology', 'product launch'],
					emotions: ['excitement', 'confidence'],
					keyPhrases: ['innovative product', 'revolutionize', 'cutting-edge technology'],
				}),
				usage: { tokens: 80 },
			});

			const result = await contentAnalyzer.analyzeContent(content);

			expect(result.success).toBe(true);
			expect(result.sentiment).toBe('positive');
			expect(result.confidence).toBe(0.92);
			expect(result.topics).toContain('innovation');
			expect(result.keyPhrases).toContain('innovative product');
		});

		it('should detect content quality issues', async () =>
		{
			const poorContent = 'This is a very short and low-quality content with spelling erors and bad grammer.';

			mockOpenAI.generateCompletion.mockResolvedValue({
				text: JSON.stringify({
					qualityScore: 0.3,
					issues: ['spelling_errors', 'grammar_errors', 'insufficient_length'],
					suggestions: ['Fix spelling errors', 'Improve grammar', 'Add more content'],
				}),
				usage: { tokens: 60 },
			});

			const result = await contentAnalyzer.analyzeContentQuality(poorContent);

			expect(result.success).toBe(true);
			expect(result.qualityScore).toBe(0.3);
			expect(result.issues).toContain('spelling_errors');
			expect(result.suggestions).toContain('Fix spelling errors');
		});

		it('should extract structured data from unstructured content', async () =>
		{
			const content = 'Contact <NAME_EMAIL> or call (*************. We are located at 123 Main St, New York, NY 10001. Founded in 2020, we serve over 10,000 customers.';

			mockOpenAI.generateCompletion.mockResolvedValue({
				text: JSON.stringify({
					entities: {
						emails: ['<EMAIL>'],
						phones: ['(*************'],
						addresses: ['123 Main St, New York, NY 10001'],
						dates: ['2020'],
						numbers: ['10,000'],
					},
					facts: [
						{ type: 'founding_year', value: '2020' },
						{ type: 'customer_count', value: '10,000' },
					],
				}),
				usage: { tokens: 120 },
			});

			const result = await contentAnalyzer.extractStructuredData(content);

			expect(result.success).toBe(true);
			expect(result.entities.emails).toContain('<EMAIL>');
			expect(result.entities.phones).toContain('(*************');
			expect(result.facts).toHaveLength(2);
		});
	});

	describe('Content Quality Validation', () =>
	{
		it('should validate generated content meets quality standards', async () =>
		{
			const generatedContent = 'This is a well-written, comprehensive description that provides valuable information about the company and its services. It maintains professional tone and includes relevant keywords.';

			mockOpenAI.generateCompletion.mockResolvedValue({
				text: JSON.stringify({
					overallScore: 0.88,
					criteria: {
						readability: 0.9,
						relevance: 0.85,
						completeness: 0.9,
						accuracy: 0.87,
						engagement: 0.88,
					},
					passed: true,
					recommendations: ['Consider adding more specific examples'],
				}),
				usage: { tokens: 90 },
			});

			const result = await qualityValidator.validateContent(generatedContent, {
				minScore: 0.8,
				requiredCriteria: ['readability', 'relevance', 'completeness'],
			});

			expect(result.success).toBe(true);
			expect(result.passed).toBe(true);
			expect(result.overallScore).toBe(0.88);
			expect(result.criteria.readability).toBe(0.9);
		});

		it('should reject low-quality content', async () =>
		{
			const lowQualityContent = 'Bad content with errors and poor quality.';

			mockOpenAI.generateCompletion.mockResolvedValue({
				text: JSON.stringify({
					overallScore: 0.45,
					criteria: {
						readability: 0.6,
						relevance: 0.3,
						completeness: 0.4,
						accuracy: 0.5,
						engagement: 0.4,
					},
					passed: false,
					issues: ['insufficient_detail', 'poor_relevance', 'low_engagement'],
					recommendations: ['Add more specific information', 'Improve relevance to topic', 'Make content more engaging'],
				}),
				usage: { tokens: 70 },
			});

			const result = await qualityValidator.validateContent(lowQualityContent, {
				minScore: 0.7,
			});

			expect(result.success).toBe(true);
			expect(result.passed).toBe(false);
			expect(result.overallScore).toBe(0.45);
			expect(result.issues).toContain('insufficient_detail');
		});

		it('should validate content against specific domain requirements', async () =>
		{
			const content = 'E-commerce platform offering secure online shopping with fast delivery.';
			const domainRequirements = {
				category: 'ecommerce',
				requiredTopics: ['shopping', 'delivery', 'security'],
				tone: 'professional',
				length: { min: 50, max: 200 },
			};

			mockOpenAI.generateCompletion.mockResolvedValue({
				text: JSON.stringify({
					overallScore: 0.82,
					categoryAlignment: 0.9,
					topicCoverage: 0.8,
					toneMatch: 0.85,
					lengthAppropriate: true,
					passed: true,
				}),
				usage: { tokens: 85 },
			});

			const result = await qualityValidator.validateAgainstRequirements(content, domainRequirements);

			expect(result.success).toBe(true);
			expect(result.passed).toBe(true);
			expect(result.categoryAlignment).toBe(0.9);
			expect(result.topicCoverage).toBe(0.8);
		});
	});

	describe('AI Provider Management', () =>
	{
		it('should load balance across multiple providers', async () =>
		{
			const requests = Array.from({ length: 10 }, (_, i) => ({
				prompt: `Test prompt ${i}`,
				maxTokens: 100,
			}));

			// Mock responses from different providers
			mockOpenAI.generateCompletion.mockResolvedValue({
				text: 'OpenAI response',
				usage: { tokens: 50 },
			});

			mockAnthropic.generateCompletion.mockResolvedValue({
				text: 'Anthropic response',
				usage: { tokens: 45 },
			});

			const results = await Promise.all(
				requests.map(req => providerManager.generateCompletion(req))
			);

			// Verify requests were distributed across providers
			expect(mockOpenAI.generateCompletion).toHaveBeenCalled();
			expect(mockAnthropic.generateCompletion).toHaveBeenCalled();

			const openAICalls = mockOpenAI.generateCompletion.mock.calls.length;
			const anthropicCalls = mockAnthropic.generateCompletion.mock.calls.length;

			expect(openAICalls + anthropicCalls).toBe(10);
			expect(Math.abs(openAICalls - anthropicCalls)).toBeLessThanOrEqual(2); // Roughly balanced
		});

		it('should handle provider rate limits with backoff', async () =>
		{
			let callCount = 0;
			mockOpenAI.generateCompletion.mockImplementation(() =>
			{
				callCount++;
				if (callCount <= 3)
				{
					throw new Error('Rate limit exceeded');
				}
				return Promise.resolve({
					text: 'Success after backoff',
					usage: { tokens: 50 },
				});
			});

			const startTime = Date.now();
			const result = await providerManager.generateCompletion({
				prompt: 'Test prompt',
				maxTokens: 100,
			});
			const endTime = Date.now();

			expect(result.success).toBe(true);
			expect(result.text).toBe('Success after backoff');
			expect(endTime - startTime).toBeGreaterThan(1000); // Should have waited for backoff
			expect(callCount).toBe(4); // 3 failures + 1 success
		});

		it('should track usage and costs across providers', async () =>
		{
			mockOpenAI.generateCompletion.mockResolvedValue({
				text: 'Response',
				usage: { tokens: 100, cost: 0.002 },
			});

			await providerManager.generateCompletion({
				prompt: 'Test prompt',
				maxTokens: 100,
			});

			const usage = providerManager.getUsageStatistics();

			expect(usage.totalRequests).toBe(1);
			expect(usage.totalTokens).toBe(100);
			expect(usage.totalCost).toBe(0.002);
			expect(usage.providerBreakdown.openai.requests).toBe(1);
		});
	});

	describe('Error Handling and Resilience', () =>
	{
		it('should handle network timeouts gracefully', async () =>
		{
			mockOpenAI.generateCompletion.mockImplementation(() =>
				new Promise((_, reject) =>
					setTimeout(() => reject(new Error('Request timeout')), 100)));

			const result = await descriptionGenerator.generateDescription({
				domain: 'timeout-test.com',
				homepage: { title: 'Test', description: 'Test', content: 'Test' },
			});

			expect(result.success).toBe(false);
			expect(result.error).toContain('timeout');
			expect(result.fallbackUsed).toBe(true);
		});

		it('should handle malformed AI responses', async () =>
		{
			mockOpenAI.generateCompletion.mockResolvedValue({
				text: 'Invalid JSON response that cannot be parsed',
				usage: { tokens: 50 },
			});

			const result = await contentAnalyzer.analyzeContent('Test content');

			expect(result.success).toBe(false);
			expect(result.error).toContain('parse');
		});

		it('should implement circuit breaker for failing providers', async () =>
		{
			// Mock consecutive failures
			mockOpenAI.generateCompletion.mockRejectedValue(new Error('Service unavailable'));

			const requests = Array.from({ length: 10 }, () =>
				providerManager.generateCompletion({
					prompt: 'Test',
					maxTokens: 50,
				}));

			const results = await Promise.all(requests);

			// First few should attempt and fail, later ones should be circuit-broken
			const attemptedFailures = results.filter(r => !r.success && r.error?.includes('Service unavailable'));
			const circuitBreakerFailures = results.filter(r => !r.success && r.error?.includes('circuit breaker'));

			expect(attemptedFailures.length).toBeGreaterThan(0);
			expect(circuitBreakerFailures.length).toBeGreaterThan(0);
		});
	});

	describe('Performance and Optimization', () =>
	{
		it('should cache similar requests to reduce API calls', async () =>
		{
			const content = 'This is test content for caching';

			mockOpenAI.generateCompletion.mockResolvedValue({
				text: 'Cached response',
				usage: { tokens: 50 },
			});

			// Make the same request multiple times
			await contentAnalyzer.analyzeContent(content);
			await contentAnalyzer.analyzeContent(content);
			await contentAnalyzer.analyzeContent(content);

			// Should only call the API once due to caching
			expect(mockOpenAI.generateCompletion).toHaveBeenCalledTimes(1);
		});

		it('should batch multiple requests for efficiency', async () =>
		{
			const contents = [
				'Content 1 for batching test',
				'Content 2 for batching test',
				'Content 3 for batching test',
			];

			mockOpenAI.generateCompletion.mockResolvedValue({
				text: JSON.stringify([
					{ sentiment: 'neutral', topics: ['test'] },
					{ sentiment: 'neutral', topics: ['test'] },
					{ sentiment: 'neutral', topics: ['test'] },
				]),
				usage: { tokens: 150 },
			});

			const results = await contentAnalyzer.batchAnalyzeContent(contents);

			expect(results).toHaveLength(3);
			expect(mockOpenAI.generateCompletion).toHaveBeenCalledTimes(1); // Batched into single call
		});

		it('should optimize token usage for cost efficiency', async () =>
		{
			const longContent = 'Very long content '.repeat(1000);

			mockOpenAI.generateCompletion.mockResolvedValue({
				text: 'Optimized response',
				usage: { tokens: 200 }, // Should be less than input due to optimization
			});

			const result = await contentAnalyzer.analyzeContent(longContent, {
				optimizeTokens: true,
				maxInputTokens: 500,
			});

			expect(result.success).toBe(true);
			expect(result.metadata.tokensUsed).toBeLessThan(1000); // Should be optimized
		});
	});
});
