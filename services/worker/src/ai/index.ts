/**
 * AI Module - AI-powered content generation and analysis functionality
 * Extracted and consolidated from domain-seeder service for worker consolidation
 */

// Types
export type {
	AIProviderType,
	AIProviderConfigType,
	AIProvidersConfigType,
	ContentGenerationRequestType,
	ContentGenerationOptionsType,
	GeneratedContentType,
	ContentAnalysisRequestType,
	ContentAnalysisOptionsType,
	ContentAnalysisResultType,
	AIServiceUsageType,
	AIServiceMetricsType,
	AIServiceErrorType,
	AIFallbackConfigType,
	PromptTemplateType,
	PromptContextType,
	ContentQualityMetricsType,
	QualityValidationResultType,
} from './types';

// Providers
export {
	BaseAIProvider,
	AIProviderManager,
	OpenAIProvider,
	AnthropicProvider,
	GoogleProvider,
	OpenRouterProvider,
} from './providers';
export type { AIResponse } from './providers';

// Core Services
export { DomainDescriptionGenerator } from './DomainDescriptionGenerator';
export { ContentAnalyzer } from './ContentAnalyzer';
export { PromptManager } from './PromptManager';
export { ContentQualityValidator } from './ContentQualityValidator';
export { AIServiceMonitor } from './AIServiceMonitor';
export { IntegratedContentGenerator } from './IntegratedContentGenerator';
export { AIConfigLoader } from './AIConfigLoader';
export type { CostConfig, AlertConfig } from './AIServiceMonitor';
export type {
	ContentGenerationMode,
	ContentVersionInfo,
	ContentUpdateRequest,
	IntegratedContentConfig,
} from './IntegratedContentGenerator';
