/**
 * AI Configuration Loader - Loads and validates AI provider configurations
 * Extracted and consolidated from domain-seeder service for worker consolidation
 */

import type { Logger } from '@shared';
import { logger as sharedLogger } from '@shared';
import type {
	AIProviderType,
	AIProviderConfigType,
	AIProvidersConfigType,
	AIFallbackConfigType,
} from './types';
import type { CostConfig, AlertConfig } from './AIServiceMonitor';

const logger = sharedLogger.getLogger('AIConfigLoader');

type AIEnvironmentVariablesType =
{
	// OpenAI Configuration
	OPENAI_API_KEY?: string;
	OPENAI_MODEL?: string;
	OPENAI_BASE_URL?: string;
	OPENAI_TIMEOUT?: string;
	OPENAI_MAX_RETRIES?: string;
	OPENAI_ENABLED?: string;

	// Anthropic Configuration
	ANTHROPIC_API_KEY?: string;
	ANTHROPIC_MODEL?: string;
	ANTHROPIC_BASE_URL?: string;
	ANTHROPIC_TIMEOUT?: string;
	ANTHROPIC_MAX_RETRIES?: string;
	ANTHROPIC_ENABLED?: string;

	// Google AI Configuration
	GOOGLE_API_KEY?: string;
	GOOGLE_MODEL?: string;
	GOOGLE_BASE_URL?: string;
	GOOGLE_TIMEOUT?: string;
	GOOGLE_MAX_RETRIES?: string;
	GOOGLE_ENABLED?: string;

	// OpenRouter Configuration
	OPENROUTER_API_KEY?: string;
	OPENROUTER_MODEL?: string;
	OPENROUTER_BASE_URL?: string;
	OPENROUTER_TIMEOUT?: string;
	OPENROUTER_MAX_RETRIES?: string;
	OPENROUTER_ENABLED?: string;

	// Fallback Configuration
	AI_FALLBACK_ENABLED?: string;
	AI_FALLBACK_MAX_RETRIES?: string;
	AI_FALLBACK_RETRY_DELAY?: string;
	AI_FALLBACK_ERROR_THRESHOLD?: string;

	// Cost Tracking Configuration
	AI_COST_TRACKING_ENABLED?: string;
	AI_DAILY_BUDGET?: string;
	AI_MONTHLY_BUDGET?: string;

	// Alert Configuration
	AI_ALERTS_ENABLED?: string;
	AI_DAILY_COST_THRESHOLD?: string;
	AI_HOURLY_COST_THRESHOLD?: string;
	AI_ERROR_RATE_THRESHOLD?: string;
	AI_LATENCY_THRESHOLD?: string;

	// Key Rotation Configuration
	AI_KEY_ROTATION_ENABLED?: string;
	AI_KEY_ROTATION_INTERVAL?: string;
	AI_KEY_BALANCE_USAGE?: string;
};

/**
 * Loads and validates AI provider configurations from environment variables
 */
class AIConfigLoader
{
	private readonly logger: Logger;

	private readonly env: AIEnvironmentVariablesType;

	constructor(env: NodeJS.ProcessEnv = process.env)
	{
		this.logger = logger;
		this.env = env as AIEnvironmentVariablesType;
	}

	/**
	 * Load complete AI configuration
	 */
	loadConfiguration(): {
		providers: AIProvidersConfigType;
		fallback: AIFallbackConfigType;
		costs: CostConfig;
		alerts: AlertConfig;
	}
	{
		this.logger.info('Loading AI configuration from environment variables');

		const providers = this.loadProviderConfigs();
		const fallback = this.loadFallbackConfig();
		const costs = this.loadCostConfig();
		const alerts = this.loadAlertConfig();

		// Validate configuration
		this.validateConfiguration(providers, fallback);

		this.logger.info('AI configuration loaded successfully', {
			providerCount: Object.keys(providers).length,
			enabledProviders: Object.entries(providers)
				.filter(([, config]) => config.enabled)
				.map(([id]) => id),
			fallbackEnabled: fallback.enabled,
			costTrackingEnabled: costs && Object.keys(costs).length > 0,
			alertsEnabled: alerts.enableAlerts,
		});

		return { providers, fallback, costs, alerts };
	}

	/**
	 * Load provider configurations
	 */
	private loadProviderConfigs(): AIProvidersConfigType
	{
		const providers: AIProvidersConfigType = {};

		// OpenAI Configuration
		if (this.env.OPENAI_API_KEY)
		{
			providers.openai = {
				type: 'openai',
				model: this.env.OPENAI_MODEL || 'gpt-4o-mini',
				apiKey: this.env.OPENAI_API_KEY,
				baseUrl: this.env.OPENAI_BASE_URL,
				timeout: this.parseNumber(this.env.OPENAI_TIMEOUT, 30000),
				maxRetries: this.parseNumber(this.env.OPENAI_MAX_RETRIES, 3),
				enabled: this.parseBoolean(this.env.OPENAI_ENABLED, true),
			};
		}

		// Anthropic Configuration
		if (this.env.ANTHROPIC_API_KEY)
		{
			providers.anthropic = {
				type: 'anthropic',
				model: this.env.ANTHROPIC_MODEL || 'claude-3-haiku-20240307',
				apiKey: this.env.ANTHROPIC_API_KEY,
				baseUrl: this.env.ANTHROPIC_BASE_URL,
				timeout: this.parseNumber(this.env.ANTHROPIC_TIMEOUT, 30000),
				maxRetries: this.parseNumber(this.env.ANTHROPIC_MAX_RETRIES, 3),
				enabled: this.parseBoolean(this.env.ANTHROPIC_ENABLED, true),
			};
		}

		// Google AI Configuration
		if (this.env.GOOGLE_API_KEY)
		{
			providers.google = {
				type: 'google',
				model: this.env.GOOGLE_MODEL || 'gemini-1.5-flash',
				apiKey: this.env.GOOGLE_API_KEY,
				baseUrl: this.env.GOOGLE_BASE_URL,
				timeout: this.parseNumber(this.env.GOOGLE_TIMEOUT, 30000),
				maxRetries: this.parseNumber(this.env.GOOGLE_MAX_RETRIES, 3),
				enabled: this.parseBoolean(this.env.GOOGLE_ENABLED, true),
			};
		}

		// OpenRouter Configuration
		if (this.env.OPENROUTER_API_KEY)
		{
			providers.openrouter = {
				type: 'openrouter',
				model: this.env.OPENROUTER_MODEL || 'openai/gpt-4o-mini',
				apiKey: this.env.OPENROUTER_API_KEY,
				baseUrl: this.env.OPENROUTER_BASE_URL || 'https://openrouter.ai/api/v1',
				timeout: this.parseNumber(this.env.OPENROUTER_TIMEOUT, 30000),
				maxRetries: this.parseNumber(this.env.OPENROUTER_MAX_RETRIES, 3),
				enabled: this.parseBoolean(this.env.OPENROUTER_ENABLED, true),
			};
		}

		return providers;
	}

	/**
	 * Load fallback configuration
	 */
	private loadFallbackConfig(): AIFallbackConfig
	{
		return {
			enabled: this.parseBoolean(this.env.AI_FALLBACK_ENABLED, true),
			maxRetries: this.parseNumber(this.env.AI_FALLBACK_MAX_RETRIES, 3),
			retryDelay: this.parseNumber(this.env.AI_FALLBACK_RETRY_DELAY, 1000),
			fallbackProviders: [], // Will be determined by provider availability
			errorThreshold: this.parseNumber(this.env.AI_FALLBACK_ERROR_THRESHOLD, 0.1),
		};
	}

	/**
	 * Load cost tracking configuration
	 */
	private loadCostConfig(): CostConfig
	{
		const costConfig: CostConfig = {};

		// OpenAI costs (as of 2024)
		if (this.env.OPENAI_API_KEY)
		{
			costConfig.openai = {
				'gpt-4o': {
					promptCostPer1K: 0.0025,
					completionCostPer1K: 0.01,
				},
				'gpt-4o-mini': {
					promptCostPer1K: 0.00015,
					completionCostPer1K: 0.0006,
				},
				'gpt-4-turbo': {
					promptCostPer1K: 0.01,
					completionCostPer1K: 0.03,
				},
			};
		}

		// Anthropic costs (as of 2024)
		if (this.env.ANTHROPIC_API_KEY)
		{
			costConfig.anthropic = {
				'claude-3-5-sonnet-20241022': {
					promptCostPer1K: 0.003,
					completionCostPer1K: 0.015,
				},
				'claude-3-haiku-20240307': {
					promptCostPer1K: 0.00025,
					completionCostPer1K: 0.00125,
				},
				'claude-3-opus-20240229': {
					promptCostPer1K: 0.015,
					completionCostPer1K: 0.075,
				},
			};
		}

		// Google AI costs (as of 2024)
		if (this.env.GOOGLE_API_KEY)
		{
			costConfig.google = {
				'gemini-1.5-pro': {
					promptCostPer1K: 0.00125,
					completionCostPer1K: 0.005,
				},
				'gemini-1.5-flash': {
					promptCostPer1K: 0.000075,
					completionCostPer1K: 0.0003,
				},
			};
		}

		// OpenRouter costs (variable, using conservative estimates)
		if (this.env.OPENROUTER_API_KEY)
		{
			costConfig.openrouter = {
				'openai/gpt-4o-mini': {
					promptCostPer1K: 0.00015,
					completionCostPer1K: 0.0006,
				},
				'anthropic/claude-3-haiku': {
					promptCostPer1K: 0.00025,
					completionCostPer1K: 0.00125,
				},
			};
		}

		return costConfig;
	}

	/**
	 * Load alert configuration
	 */
	private loadAlertConfig(): AlertConfig
	{
		return {
			dailyCostThreshold: this.parseNumber(this.env.AI_DAILY_COST_THRESHOLD, 100),
			hourlyCostThreshold: this.parseNumber(this.env.AI_HOURLY_COST_THRESHOLD, 10),
			errorRateThreshold: this.parseNumber(this.env.AI_ERROR_RATE_THRESHOLD, 0.1),
			latencyThreshold: this.parseNumber(this.env.AI_LATENCY_THRESHOLD, 30000),
			enableAlerts: this.parseBoolean(this.env.AI_ALERTS_ENABLED, true),
		};
	}

	/**
	 * Validate the loaded configuration
	 */
	private validateConfiguration(
		providers: AIProvidersConfigType,
		fallback: AIFallbackConfig,
	): void
	{
		const enabledProviders = Object.entries(providers)
			.filter(([, config]) => config.enabled);

		if (enabledProviders.length === 0)
		{
			throw new Error('At least one AI provider must be configured and enabled');
		}

		// Validate each provider configuration
		for (const [providerId, config] of enabledProviders)
		{
			this.validateProviderConfig(providerId, config);
		}

		// Validate fallback configuration
		if (fallback.enabled && fallback.maxRetries < 1)
		{
			throw new Error('Fallback max retries must be at least 1');
		}

		if (fallback.enabled && fallback.retryDelay < 100)
		{
			throw new Error('Fallback retry delay must be at least 100ms');
		}
	}

	/**
	 * Validate individual provider configuration
	 */
	private validateProviderConfig(providerId: string, config: AIProviderConfigType): void
	{
		if (!config.apiKey)
		{
			throw new Error(`API key is required for provider ${providerId}`);
		}

		if (!config.model)
		{
			throw new Error(`Model is required for provider ${providerId}`);
		}

		if (config.timeout && config.timeout < 1000)
		{
			throw new Error(`Timeout must be at least 1000ms for provider ${providerId}`);
		}

		if (config.maxRetries && config.maxRetries < 0)
		{
			throw new Error(`Max retries must be non-negative for provider ${providerId}`);
		}

		// Validate provider type
		const validTypes: AIProviderType[] = ['openai', 'anthropic', 'google', 'openrouter'];
		if (!validTypes.includes(config.type))
		{
			throw new Error(`Invalid provider type '${config.type}' for provider ${providerId}`);
		}
	}

	/**
	 * Parse boolean from string
	 */
	private parseBoolean(value: string | undefined, defaultValue: boolean): boolean
	{
		if (value === undefined)
		{
			return defaultValue;
		}

		const lowerValue = value.toLowerCase();
		return lowerValue === 'true' || lowerValue === '1' || lowerValue === 'yes';
	}

	/**
	 * Parse number from string
	 */
	private parseNumber(value: string | undefined, defaultValue: number): number
	{
		if (value === undefined)
		{
			return defaultValue;
		}

		const parsed = Number(value);
		if (isNaN(parsed))
		{
			this.logger.warn('Invalid number value, using default', {
				value,
				defaultValue,
			});
			return defaultValue;
		}

		return parsed;
	}

	/**
	 * Get example environment configuration
	 */
	static getExampleEnvConfig(): string
	{
		return `# AI Provider Configuration

# OpenAI Configuration
OPENAI_API_KEY=sk-your-openai-api-key
OPENAI_MODEL=gpt-4o-mini
OPENAI_TIMEOUT=30000
OPENAI_MAX_RETRIES=3
OPENAI_ENABLED=true

# Anthropic Configuration
ANTHROPIC_API_KEY=sk-ant-your-anthropic-api-key
ANTHROPIC_MODEL=claude-3-haiku-20240307
ANTHROPIC_TIMEOUT=30000
ANTHROPIC_MAX_RETRIES=3
ANTHROPIC_ENABLED=true

# Google AI Configuration
GOOGLE_API_KEY=your-google-ai-api-key
GOOGLE_MODEL=gemini-1.5-flash
GOOGLE_TIMEOUT=30000
GOOGLE_MAX_RETRIES=3
GOOGLE_ENABLED=true

# OpenRouter Configuration
OPENROUTER_API_KEY=sk-or-your-openrouter-api-key
OPENROUTER_MODEL=openai/gpt-4o-mini
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
OPENROUTER_TIMEOUT=30000
OPENROUTER_MAX_RETRIES=3
OPENROUTER_ENABLED=false

# Fallback Configuration
AI_FALLBACK_ENABLED=true
AI_FALLBACK_MAX_RETRIES=3
AI_FALLBACK_RETRY_DELAY=1000
AI_FALLBACK_ERROR_THRESHOLD=0.1

# Cost Tracking Configuration
AI_COST_TRACKING_ENABLED=true
AI_DAILY_BUDGET=100
AI_MONTHLY_BUDGET=2000

# Alert Configuration
AI_ALERTS_ENABLED=true
AI_DAILY_COST_THRESHOLD=80
AI_HOURLY_COST_THRESHOLD=10
AI_ERROR_RATE_THRESHOLD=0.1
AI_LATENCY_THRESHOLD=30000

# Key Rotation Configuration
AI_KEY_ROTATION_ENABLED=true
AI_KEY_ROTATION_INTERVAL=60
AI_KEY_BALANCE_USAGE=true`;
	}
}

export default AIConfigLoader;
