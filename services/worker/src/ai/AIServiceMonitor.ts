/**
 * AI Service Monitor - Monitors AI service usage, costs, and performance
 * Extracted and consolidated from domain-seeder service
 */

import type { Logger } from '@shared';
import { logger as sharedLogger } from '@shared';
import type {
	AIServiceUsage,
	AIServiceMetrics,
	AIServiceError,
} from './types';

const logger = sharedLogger.getLogger('AIServiceMonitor');

type CostConfigType =
{
	[providerId: string]:
	{
		[model: string]:
		{
			promptCostPer1K: number;
			completionCostPer1K: number;
		};
	};
};

type AlertConfigType =
{
	dailyCostThreshold: number;
	hourlyCostThreshold: number;
	errorRateThreshold: number;
	latencyThreshold: number;
	enableAlerts: boolean;
};

/**
 * Monitors AI service usage, costs, and performance metrics
 */
class AIServiceMonitor
{
	private readonly logger: Logger;

	private readonly costConfig: CostConfigType;

	private readonly alertConfig: AlertConfigType;

	private readonly usage = new Map<string, AIServiceUsage>();

	private readonly errors: AIServiceError[] = [];

	private readonly dailyUsage = new Map<string, Map<string, AIServiceUsage>>(); // date -> providerId -> usage

	private readonly maxErrorHistory = 1000;

	private readonly maxDailyHistory = 30; // Keep 30 days of history

	constructor(
		costConfig: CostConfigType = {},
		alertConfig: Partial<AlertConfigType> = {},
	)
	{
		this.logger = logger;
		this.costConfig = costConfig;
		this.alertConfig = {
			dailyCostThreshold: alertConfig.dailyCostThreshold ?? 100,
			hourlyCostThreshold: alertConfig.hourlyCostThreshold ?? 10,
			errorRateThreshold: alertConfig.errorRateThreshold ?? 0.1,
			latencyThreshold: alertConfig.latencyThreshold ?? 30000,
			enableAlerts: alertConfig.enableAlerts ?? true,
		};

		this.logger.info('AI Service Monitor initialized', {
			costConfigProviders: Object.keys(this.costConfig),
			alertConfig: this.alertConfig,
		});
	}

	/**
	 * Record AI service usage
	 */
	recordUsage(
		providerId: string,
		model: string,
		tokenUsage: { prompt: number; completion: number; total: number },
		latency: number,
		success: boolean = true,
	): void
	{
		// Update overall usage
		let usage = this.usage.get(providerId);
		if (!usage)
		{
			usage = {
				providerId,
				model,
				requestCount: 0,
				tokenUsage: { prompt: 0, completion: 0, total: 0 },
				costs: { prompt: 0, completion: 0, total: 0 },
				averageLatency: 0,
				errorCount: 0,
				lastUsed: new Date().toISOString(),
			};
			this.usage.set(providerId, usage);
		}

		// Update request count
		usage.requestCount++;
		usage.lastUsed = new Date().toISOString();

		// Update token usage
		usage.tokenUsage.prompt += tokenUsage.prompt;
		usage.tokenUsage.completion += tokenUsage.completion;
		usage.tokenUsage.total += tokenUsage.total;

		// Calculate costs
		const costs = this.calculateCosts(providerId, model, tokenUsage);
		usage.costs.prompt += costs.prompt;
		usage.costs.completion += costs.completion;
		usage.costs.total += costs.total;

		// Update average latency
		usage.averageLatency = (usage.averageLatency * (usage.requestCount - 1) + latency)
			/ usage.requestCount;

		// Update error count
		if (!success)
		{
			usage.errorCount++;
		}

		// Update daily usage
		this.updateDailyUsage(providerId, model, tokenUsage, costs, latency, success);

		// Check for alerts
		if (this.alertConfig.enableAlerts)
		{
			this.checkAlerts(usage);
		}

		this.logger.debug('AI service usage recorded', {
			providerId,
			model,
			tokenUsage,
			costs,
			latency,
			success,
		});
	}

	/**
	 * Record AI service error
	 */
	recordError(error: AIServiceError): void
	{
		this.errors.push({
			...error,
			timestamp: new Date().toISOString(),
		});

		// Limit error history
		if (this.errors.length > this.maxErrorHistory)
		{
			this.errors.splice(0, this.errors.length - this.maxErrorHistory);
		}

		// Update error count in usage
		const usage = this.usage.get(error.provider);
		if (usage)
		{
			usage.errorCount++;
		}

		this.logger.warn('AI service error recorded', {
			provider: error.provider,
			code: error.code,
			message: error.message,
			retryable: error.retryable,
		});
	}

	/**
	 * Calculate costs for token usage
	 */
	private calculateCosts(
		providerId: string,
		model: string,
		tokenUsage: { prompt: number; completion: number; total: number },
	): { prompt: number; completion: number; total: number }
	{
		const providerConfig = this.costConfig[providerId];
		if (!providerConfig)
		{
			return { prompt: 0, completion: 0, total: 0 };
		}

		const modelConfig = providerConfig[model];
		if (!modelConfig)
		{
			return { prompt: 0, completion: 0, total: 0 };
		}

		const promptCost = (tokenUsage.prompt / 1000) * modelConfig.promptCostPer1K;
		const completionCost = (tokenUsage.completion / 1000) * modelConfig.completionCostPer1K;
		const totalCost = promptCost + completionCost;

		return {
			prompt: promptCost,
			completion: completionCost,
			total: totalCost,
		};
	}

	/**
	 * Update daily usage tracking
	 */
	private updateDailyUsage(
		providerId: string,
		model: string,
		tokenUsage: { prompt: number; completion: number; total: number },
		costs: { prompt: number; completion: number; total: number },
		latency: number,
		success: boolean,
	): void
	{
		const today = new Date().toISOString().split('T')[0];

		let dayUsage = this.dailyUsage.get(today);
		if (!dayUsage)
		{
			dayUsage = new Map();
			this.dailyUsage.set(today, dayUsage);
		}

		let providerDayUsage = dayUsage.get(providerId);
		if (!providerDayUsage)
		{
			providerDayUsage = {
				providerId,
				model,
				requestCount: 0,
				tokenUsage: { prompt: 0, completion: 0, total: 0 },
				costs: { prompt: 0, completion: 0, total: 0 },
				averageLatency: 0,
				errorCount: 0,
				lastUsed: new Date().toISOString(),
			};
			dayUsage.set(providerId, providerDayUsage);
		}

		// Update daily usage
		providerDayUsage.requestCount++;
		providerDayUsage.tokenUsage.prompt += tokenUsage.prompt;
		providerDayUsage.tokenUsage.completion += tokenUsage.completion;
		providerDayUsage.tokenUsage.total += tokenUsage.total;
		providerDayUsage.costs.prompt += costs.prompt;
		providerDayUsage.costs.completion += costs.completion;
		providerDayUsage.costs.total += costs.total;
		providerDayUsage.averageLatency =
			(providerDayUsage.averageLatency * (providerDayUsage.requestCount - 1) + latency) / providerDayUsage.requestCount;
		providerDayUsage.lastUsed = new Date().toISOString();

		if (!success)
		{
			providerDayUsage.errorCount++;
		}

		// Cleanup old daily usage data
		this.cleanupOldDailyUsage();
	}

	/**
	 * Cleanup old daily usage data
	 */
	private cleanupOldDailyUsage(): void
	{
		const cutoffDate = new Date();
		cutoffDate.setDate(cutoffDate.getDate() - this.maxDailyHistory);
		const cutoffDateStr = cutoffDate.toISOString().split('T')[0];

		for (const [date] of this.dailyUsage)
		{
			if (date < cutoffDateStr)
			{
				this.dailyUsage.delete(date);
			}
		}
	}

	/**
	 * Check for alert conditions
	 */
	private checkAlerts(usage: AIServiceUsage): void
	{
		// Check error rate
		const errorRate = usage.requestCount > 0 ? usage.errorCount / usage.requestCount : 0;
		if (errorRate > this.alertConfig.errorRateThreshold)
		{
			this.logger.warn('High error rate detected', {
				providerId: usage.providerId,
				errorRate,
				threshold: this.alertConfig.errorRateThreshold,
			});
		}

		// Check latency
		if (usage.averageLatency > this.alertConfig.latencyThreshold)
		{
			this.logger.warn('High latency detected', {
				providerId: usage.providerId,
				averageLatency: usage.averageLatency,
				threshold: this.alertConfig.latencyThreshold,
			});
		}

		// Check daily costs
		const today = new Date().toISOString().split('T')[0];
		const todayUsage = this.dailyUsage.get(today)?.get(usage.providerId);
		if (todayUsage && todayUsage.costs.total > this.alertConfig.dailyCostThreshold)
		{
			this.logger.warn('Daily cost threshold exceeded', {
				providerId: usage.providerId,
				dailyCost: todayUsage.costs.total,
				threshold: this.alertConfig.dailyCostThreshold,
			});
		}
	}

	/**
	 * Get comprehensive metrics
	 */
	getMetrics(): AIServiceMetrics
	{
		const providerUsage = Array.from(this.usage.values());
		const totalRequests = providerUsage.reduce((sum, usage) => sum + usage.requestCount, 0);
		const totalTokens = providerUsage.reduce((sum, usage) => sum + usage.tokenUsage.total, 0);
		const totalCosts = providerUsage.reduce((sum, usage) => sum + usage.costs.total, 0);
		const totalErrors = providerUsage.reduce((sum, usage) => sum + usage.errorCount, 0);

		const averageLatency = totalRequests > 0
			? providerUsage.reduce((sum, usage) => sum + (usage.averageLatency * usage.requestCount), 0) / totalRequests
			: 0;

		const errorRate = totalRequests > 0 ? totalErrors / totalRequests : 0;

		// Build daily usage array
		const dailyUsage = Array.from(this.dailyUsage.entries())
			.map(([date, dayUsage]) =>
			{
				const dayProviders = Array.from(dayUsage.values());
				return {
					date,
					requests: dayProviders.reduce((sum, usage) => sum + usage.requestCount, 0),
					tokens: dayProviders.reduce((sum, usage) => sum + usage.tokenUsage.total, 0),
					costs: dayProviders.reduce((sum, usage) => sum + usage.costs.total, 0),
				};
			})
			.sort((a, b) => a.date.localeCompare(b.date));

		return {
			totalRequests,
			totalTokens,
			totalCosts,
			averageLatency,
			errorRate,
			providerUsage,
			dailyUsage,
		};
	}

	/**
	 * Get usage for a specific provider
	 */
	getProviderUsage(providerId: string): AIServiceUsage | null
	{
		return this.usage.get(providerId) || null;
	}

	/**
	 * Get daily usage for a specific date
	 */
	getDailyUsage(date: string): Map<string, AIServiceUsage> | null
	{
		return this.dailyUsage.get(date) || null;
	}

	/**
	 * Get recent errors
	 */
	getRecentErrors(limit: number = 50): AIServiceError[]
	{
		return this.errors.slice(-limit);
	}

	/**
	 * Get errors for a specific provider
	 */
	getProviderErrors(providerId: string, limit: number = 50): AIServiceError[]
	{
		return this.errors
			.filter(error => error.provider === providerId)
			.slice(-limit);
	}

	/**
	 * Get cost breakdown by provider and model
	 */
	getCostBreakdown(): Array<{
		providerId: string;
		model: string;
		requests: number;
		tokens: number;
		costs: number;
		percentage: number;
	}>
	{
		const totalCosts = Array.from(this.usage.values())
			.reduce((sum, usage) => sum + usage.costs.total, 0);

		return Array.from(this.usage.values())
			.map(usage => ({
				providerId: usage.providerId,
				model: usage.model,
				requests: usage.requestCount,
				tokens: usage.tokenUsage.total,
				costs: usage.costs.total,
				percentage: totalCosts > 0 ? (usage.costs.total / totalCosts) * 100 : 0,
			}))
			.sort((a, b) => b.costs - a.costs);
	}

	/**
	 * Get performance statistics
	 */
	getPerformanceStats(): {
		averageLatencyByProvider: Array<{ providerId: string; averageLatency: number }>;
		errorRateByProvider: Array<{ providerId: string; errorRate: number }>;
		throughputByProvider: Array<{ providerId: string; requestsPerHour: number }>;
	}
	{
		const now = new Date();
		const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

		const averageLatencyByProvider = Array.from(this.usage.values())
			.map(usage => ({
				providerId: usage.providerId,
				averageLatency: usage.averageLatency,
			}))
			.sort((a, b) => a.averageLatency - b.averageLatency);

		const errorRateByProvider = Array.from(this.usage.values())
			.map(usage => ({
				providerId: usage.providerId,
				errorRate: usage.requestCount > 0 ? usage.errorCount / usage.requestCount : 0,
			}))
			.sort((a, b) => a.errorRate - b.errorRate);

		// Calculate throughput (simplified - would need more detailed tracking in production)
		const throughputByProvider = Array.from(this.usage.values())
			.map(usage => ({
				providerId: usage.providerId,
				requestsPerHour: usage.requestCount, // Simplified calculation
			}))
			.sort((a, b) => b.requestsPerHour - a.requestsPerHour);

		return {
			averageLatencyByProvider,
			errorRateByProvider,
			throughputByProvider,
		};
	}

	/**
	 * Reset metrics (for testing or periodic cleanup)
	 */
	resetMetrics(): void
	{
		this.usage.clear();
		this.errors.length = 0;
		this.dailyUsage.clear();

		this.logger.info('AI service metrics reset');
	}

	/**
	 * Export metrics for backup or analysis
	 */
	exportMetrics(): {
		usage: AIServiceUsage[];
		errors: AIServiceError[];
		dailyUsage: Array<{ date: string; usage: AIServiceUsage[] }>;
		exportedAt: string;
	}
	{
		const dailyUsageArray = Array.from(this.dailyUsage.entries())
			.map(([date, dayUsage]) => ({
				date,
				usage: Array.from(dayUsage.values()),
			}));

		return {
			usage: Array.from(this.usage.values()),
			errors: [...this.errors],
			dailyUsage: dailyUsageArray,
			exportedAt: new Date().toISOString(),
		};
	}

	/**
	 * Import metrics from backup
	 */
	importMetrics(data: {
		usage: AIServiceUsage[];
		errors: AIServiceError[];
		dailyUsage: Array<{ date: string; usage: AIServiceUsage[] }>;
	}): void
	{
		// Import usage data
		for (const usage of data.usage)
		{
			this.usage.set(usage.providerId, usage);
		}

		// Import errors
		this.errors.push(...data.errors);
		if (this.errors.length > this.maxErrorHistory)
		{
			this.errors.splice(0, this.errors.length - this.maxErrorHistory);
		}

		// Import daily usage
		for (const { date, usage } of data.dailyUsage)
		{
			const dayUsage = new Map<string, AIServiceUsage>();
			for (const providerUsage of usage)
			{
				dayUsage.set(providerUsage.providerId, providerUsage);
			}
			this.dailyUsage.set(date, dayUsage);
		}

		this.logger.info('AI service metrics imported', {
			usageCount: data.usage.length,
			errorCount: data.errors.length,
			dailyUsageCount: data.dailyUsage.length,
		});
	}
}

export type { CostConfigType as CostConfig, AlertConfigType as AlertConfig };

export { AIServiceMonitor };
