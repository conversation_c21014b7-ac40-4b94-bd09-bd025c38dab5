/**
 * Domain Description Generator - AI-powered domain content generation
 * Extracted and consolidated from crawler service for worker consolidation
 */

import type { Logger } from '@shared';
import { logger as sharedLogger } from '@shared';
import type {
	ContentGenerationRequest,
	ContentGenerationOptions,
	GeneratedContent,
	AIProvidersConfig,
	AIFallbackConfig,
} from './types';
import { AIProviderManager } from './providers/AIProviderManager';
import { PromptManager } from './PromptManager';
import { ContentQualityValidator } from './ContentQualityValidator';

const logger = sharedLogger.getLogger('DomainDescriptionGenerator');

type DomainContent = {
	domain: string;
	title?: string;
	description?: string;
	content?: string;
	aboutContent?: string;
	contactInfo?: string;
	technologies?: string[];
	industry?: string;
	language?: string;
};

type CompanyInfo = {
	name?: string;
	industry?: string;
	services?: string[];
	location?: string;
	founded?: string;
};

type GeneratedDescription = {
	shortDescription: string;
	longDescription: string;
	companyInfo: CompanyInfo;
	keyFeatures: string[];
	targetAudience: string;
	businessModel?: string;
	confidence: number;
	generatedAt: Date;
};

/**
 * Generates comprehensive domain descriptions using AI
 */
class DomainDescriptionGenerator
{
	private readonly logger: Logger;

	private readonly providerManager: AIProviderManager;

	private readonly promptManager: PromptManager;

	private readonly qualityValidator: ContentQualityValidator;

	// Metrics tracking
	private readonly metrics = {
		totalGenerations: 0,
		successfulGenerations: 0,
		failedGenerations: 0,
		averageProcessingTime: 0,
		averageConfidence: 0,
		confidenceScores: [] as number[],
	};

	constructor(
		providersConfig: AIProvidersConfig,
		fallbackConfig?: Partial<AIFallbackConfig>,
	)
	{
		this.logger = logger;

		this.providerManager = new AIProviderManager(providersConfig, fallbackConfig);
		this.promptManager = new PromptManager();
		this.qualityValidator = new ContentQualityValidator();

		this.logger.info('Domain Description Generator initialized', {
			availableProviders: this.providerManager.getAvailableProviders(),
		});
	}

	/**
	 * Generate comprehensive domain description from website content
	 */
	async generateDescription(
		domainContent: DomainContent,
		options?: Partial<ContentGenerationOptions>,
	): Promise<GeneratedDescription>
	{
		const startTime = Date.now();
		this.metrics.totalGenerations++;

		try
		{
			this.logger.info('Starting domain description generation', {
				domain: domainContent.domain,
				hasTitle: !!domainContent.title,
				hasContent: !!domainContent.content,
				hasAboutContent: !!domainContent.aboutContent,
			});

			// Build generation request
			const request = this.buildGenerationRequest(domainContent, options);

			// Generate content using AI
			const generatedContent = await this.generateContentWithAI(request);

			// Convert to legacy format for compatibility
			const description = this.convertToLegacyFormat(generatedContent, domainContent);

			// Update metrics
			const processingTime = Date.now() - startTime;
			this.updateSuccessMetrics(processingTime, description.confidence);

			this.logger.info('Domain description generation successful', {
				domain: domainContent.domain,
				confidence: description.confidence,
				processingTime,
			});

			return description;
		}
		catch (error)
		{
			const processingTime = Date.now() - startTime;
			this.updateFailureMetrics(processingTime);

			this.logger.error('Domain description generation failed', {
				domain: domainContent.domain,
				error: error.message,
				processingTime,
			});

			// Return fallback description
			return this.generateFallbackDescription(domainContent);
		}
	}

	/**
	 * Generate descriptions for multiple domains in batch
	 */
	async batchGenerateDescriptions(
		domains: DomainContent[],
		options?: Partial<ContentGenerationOptions>,
		concurrency: number = 3,
	): Promise<Map<string, GeneratedDescription>>
	{
		const results = new Map<string, GeneratedDescription>();

		this.logger.info('Starting batch domain description generation', {
			domainCount: domains.length,
			concurrency,
		});

		// Process in batches to avoid rate limits
		const batches = this.createBatches(domains, concurrency);

		for (const batch of batches)
		{
			const batchPromises = batch.map(async (domainContent) =>
			{
				try
				{
					const description = await this.generateDescription(domainContent, options);
					return { domain: domainContent.domain, description };
				}
				catch (error)
				{
					this.logger.error('Batch description generation failed', {
						domain: domainContent.domain,
						error: error.message,
					});

					const fallback = this.generateFallbackDescription(domainContent);
					return { domain: domainContent.domain, description: fallback };
				}
			});

			const batchResults = await Promise.allSettled(batchPromises);

			for (const result of batchResults)
			{
				if (result.status === 'fulfilled')
				{
					results.set(result.value.domain, result.value.description);
				}
			}

			// Add delay between batches to respect rate limits
			if (batches.indexOf(batch) < batches.length - 1)
			{
				await this.delay(1000);
			}
		}

		this.logger.info('Batch domain description generation completed', {
			domainCount: domains.length,
			successCount: results.size,
		});

		return results;
	}

	/**
	 * Extract company information from website content
	 */
	async extractCompanyInfo(domainContent: DomainContent): Promise<CompanyInfo>
	{
		try
		{
			const request = this.buildCompanyExtractionRequest(domainContent);
			const result = await this.providerManager.generateContent(
				request.systemPrompt,
				request.userPrompt,
			);

			return this.parseCompanyInfo(result.content);
		}
		catch (error)
		{
			this.logger.error('Company info extraction failed', {
				domain: domainContent.domain,
				error: error.message,
			});

			return this.extractBasicCompanyInfo(domainContent);
		}
	}

	/**
	 * Check if AI services are available
	 */
	async isAvailable(): Promise<boolean>
	{
		return this.providerManager.hasAvailableProviders();
	}

	/**
	 * Build generation request from domain content
	 */
	private buildGenerationRequest(
		domainContent: DomainContent,
		options?: Partial<ContentGenerationOptions>,
	): ContentGenerationRequest
	{
		const mergedOptions: ContentGenerationOptions = {
			minWords: options?.minWords ?? 50,
			maxWords: options?.maxWords ?? 200,
			language: options?.language ?? 'en',
			seoOptimization: options?.seoOptimization ?? true,
			includeKeywords: options?.includeKeywords ?? true,
			includeAudience: options?.includeAudience ?? true,
			includeUseCases: options?.includeUseCases ?? true,
			timeout: options?.timeout ?? 30000,
		};

		return {
			domain: domainContent.domain,
			webContext: {
				title: domainContent.title,
				description: domainContent.description,
				content: domainContent.content,
				status: 'success',
			},
			options: mergedOptions,
		};
	}

	/**
	 * Generate content using AI providers
	 */
	private async generateContentWithAI(request: ContentGenerationRequest): Promise<GeneratedContent>
	{
		// Build prompts for content generation
		const { systemPrompt, userPrompt } = this.buildGenerationPrompts(request);

		// Generate content using AI
		const aiResponse = await this.providerManager.generateContent(systemPrompt, userPrompt);

		// Process and validate the response
		const generatedContent = this.processGenerationResponse(aiResponse, request);

		// Validate content quality
		const validationResult = await this.qualityValidator.validateContent(generatedContent);

		if (!validationResult.passed)
		{
			this.logger.warn('Generated content failed quality validation', {
				domain: request.domain,
				score: validationResult.score,
				issues: validationResult.issues.length,
			});
		}

		return generatedContent;
	}

	/**
	 * Build generation prompts
	 */
	private buildGenerationPrompts(
		request: ContentGenerationRequest,
	): { systemPrompt: string; userPrompt: string }
	{
		// Get appropriate prompt template
		const template = this.promptManager.getContentGenerationTemplate(request.options?.language);

		// Build context for prompt
		const context = {
			domain: request.domain,
			title: request.webContext?.title || '',
			description: request.webContext?.description || '',
			content: request.webContext?.content || '',
			minWords: request.options?.minWords?.toString() || '50',
			maxWords: request.options?.maxWords?.toString() || '200',
			language: request.options?.language || 'en',
			includeKeywords: request.options?.includeKeywords?.toString() || 'true',
			includeAudience: request.options?.includeAudience?.toString() || 'true',
			includeUseCases: request.options?.includeUseCases?.toString() || 'true',
		};

		// Render prompts with context
		const systemPrompt = this.promptManager.renderTemplate(template.systemPrompt, context);
		const userPrompt = this.promptManager.renderTemplate(template.userTemplate, context);

		return { systemPrompt, userPrompt };
	}

	/**
	 * Process AI generation response
	 */
	private processGenerationResponse(aiResponse: any, request: ContentGenerationRequest): GeneratedContent
	{
		// Extract content from AI response
		const rawContent = aiResponse.content;

		// Validate response structure
		this.validateGenerationResponse(rawContent);

		// Calculate confidence based on completeness and quality
		const confidence = this.calculateGenerationConfidence(rawContent, request);

		// Create generated content
		const generatedContent: GeneratedContent = {
			summary: rawContent.summary || rawContent.shortDescription || '',
			keyFeatures: rawContent.keyFeatures || [],
			audience: rawContent.audience || rawContent.targetAudience || [],
			useCases: rawContent.useCases || [],
			keywords: rawContent.keywords || [],
			category: rawContent.category || { primary: 'business' },
			confidence,
			metadata: {
				provider: aiResponse.provider,
				model: aiResponse.model,
				generatedAt: aiResponse.timestamp,
				processingTime: 0, // Will be set by caller
				tokenUsage: aiResponse.tokenUsage,
			},
		};

		return generatedContent;
	}

	/**
	 * Validate AI generation response structure
	 */
	private validateGenerationResponse(content: any): void
	{
		if (!content || typeof content !== 'object')
		{
			throw new Error('AI generation response must be a valid object');
		}

		if (!content.summary && !content.shortDescription)
		{
			throw new Error('Generated content must include a summary or shortDescription');
		}
	}

	/**
	 * Calculate generation confidence
	 */
	private calculateGenerationConfidence(content: any, request: ContentGenerationRequest): number
	{
		let confidence = 0.5; // Base confidence

		// Check for required fields
		if (content.summary || content.shortDescription)
		{
			confidence += 0.2;
		}

		if (content.keyFeatures && Array.isArray(content.keyFeatures) && content.keyFeatures.length > 0)
		{
			confidence += 0.1;
		}

		if (content.audience && Array.isArray(content.audience) && content.audience.length > 0)
		{
			confidence += 0.1;
		}

		if (content.keywords && Array.isArray(content.keywords) && content.keywords.length > 0)
		{
			confidence += 0.1;
		}

		// Check content quality
		const summary = content.summary || content.shortDescription || '';
		const wordCount = summary.split(/\s+/).length;

		if (wordCount >= (request.options?.minWords || 50))
		{
			confidence += 0.1;
		}

		return Math.min(1, confidence);
	}

	/**
	 * Convert generated content to legacy format for compatibility
	 */
	private convertToLegacyFormat(
		generatedContent: GeneratedContent,
		domainContent: DomainContent,
	): GeneratedDescription
	{
		return {
			shortDescription: generatedContent.summary.substring(0, 200),
			longDescription: generatedContent.summary,
			companyInfo: {
				name: domainContent.title,
				industry: domainContent.industry,
				services: generatedContent.keyFeatures,
				location: undefined,
				founded: undefined,
			},
			keyFeatures: generatedContent.keyFeatures,
			targetAudience: Array.isArray(generatedContent.audience)
				? generatedContent.audience.join(', ')
				: generatedContent.audience || 'General audience',
			businessModel: undefined,
			confidence: generatedContent.confidence,
			generatedAt: new Date(),
		};
	}

	/**
	 * Build company extraction request
	 */
	private buildCompanyExtractionRequest(domainContent: DomainContent): {
		systemPrompt: string;
		userPrompt: string;
	}
	{
		const systemPrompt = 'You are an expert business analyst. Extract specific company information from website content. Return only factual information that is explicitly mentioned in the content.';

		const userPrompt = `Extract company information from this website:

Domain: ${domainContent.domain}
Title: ${domainContent.title || 'Not available'}
Content: ${domainContent.content?.substring(0, 1500) || 'Not available'}
About: ${domainContent.aboutContent?.substring(0, 800) || 'Not available'}
Contact: ${domainContent.contactInfo || 'Not available'}

Return JSON with:
{
  "name": "Exact company name",
  "industry": "Specific industry",
  "services": ["Specific services offered"],
  "location": "Physical location/address",
  "founded": "Year founded"
}

Only include information explicitly mentioned. Use null for missing information.`;

		return { systemPrompt, userPrompt };
	}

	/**
	 * Parse company information from AI response
	 */
	private parseCompanyInfo(response: any): CompanyInfo
	{
		try
		{
			if (typeof response === 'string')
			{
				const jsonMatch = response.match(/\{[\s\S]*\}/);
				if (jsonMatch)
				{
					return JSON.parse(jsonMatch[0]);
				}
			}
			else if (typeof response === 'object')
			{
				return response;
			}
		}
		catch (error)
		{
			this.logger.warn('Failed to parse company info', { error: error.message });
		}

		return {};
	}

	/**
	 * Extract basic company info without AI
	 */
	private extractBasicCompanyInfo(domainContent: DomainContent): CompanyInfo
	{
		return {
			name: domainContent.title,
			industry: domainContent.industry,
			services: [],
			location: undefined,
			founded: undefined,
		};
	}

	/**
	 * Generate fallback description when AI fails
	 */
	private generateFallbackDescription(domainContent: DomainContent): GeneratedDescription
	{
		const domain = domainContent.domain;
		const title = domainContent.title || domain;

		return {
			shortDescription: `${title} is a website providing online services and information.`,
			longDescription: `${title} operates as a web-based platform offering various services to its users. The website serves as a digital presence for the organization, providing information and potentially interactive features for visitors.`,
			companyInfo: {
				name: title !== domain ? title : undefined,
				industry: domainContent.industry,
				services: domainContent.technologies || [],
				location: undefined,
				founded: undefined,
			},
			keyFeatures: domainContent.technologies || ['online services', 'web platform'],
			targetAudience: 'General internet users',
			confidence: 0.2,
			generatedAt: new Date(),
		};
	}

	/**
	 * Update success metrics
	 */
	private updateSuccessMetrics(processingTime: number, confidence: number): void
	{
		this.metrics.successfulGenerations++;

		// Update average processing time
		const totalGenerations = this.metrics.successfulGenerations + this.metrics.failedGenerations;
		this.metrics.averageProcessingTime =
			(this.metrics.averageProcessingTime * (totalGenerations - 1) + processingTime) / totalGenerations;

		// Track confidence scores
		this.metrics.confidenceScores.push(confidence);
		if (this.metrics.confidenceScores.length > 1000)
		{
			this.metrics.confidenceScores.shift(); // Keep last 1000 scores
		}

		// Update average confidence
		this.metrics.averageConfidence =
			this.metrics.confidenceScores.reduce((sum, score) => sum + score, 0) / this.metrics.confidenceScores.length;
	}

	/**
	 * Update failure metrics
	 */
	private updateFailureMetrics(processingTime: number): void
	{
		this.metrics.failedGenerations++;

		// Update average processing time
		const totalGenerations = this.metrics.successfulGenerations + this.metrics.failedGenerations;
		this.metrics.averageProcessingTime =
			(this.metrics.averageProcessingTime * (totalGenerations - 1) + processingTime) / totalGenerations;
	}

	/**
	 * Create batches for concurrent processing
	 */
	private createBatches<T>(items: T[], batchSize: number): T[][]
	{
		const batches: T[][] = [];
		for (let i = 0; i < items.length; i += batchSize)
		{
			batches.push(items.slice(i, i + batchSize));
		}
		return batches;
	}

	/**
	 * Delay execution for rate limiting
	 */
	private delay(ms: number): Promise<void>
	{
		return new Promise(resolve => setTimeout(resolve, ms));
	}

	/**
	 * Get generation metrics
	 */
	getMetrics(): typeof this.metrics & {
		successRate: number;
	}
	{
		const successRate = this.metrics.totalGenerations > 0
			? this.metrics.successfulGenerations / this.metrics.totalGenerations
			: 0;

		return {
			...this.metrics,
			successRate,
		};
	}

	/**
	 * Test AI providers for content generation
	 */
	async testProviders(): Promise<Map<string, boolean>>
	{
		const results = new Map<string, boolean>();
		const providers = this.providerManager.getAvailableProviders();

		this.logger.info('Testing AI providers for content generation', { providers });

		for (const providerId of providers)
		{
			try
			{
				// Simple test generation
				const testContent: DomainContent =
				{
					domain: 'test.com',
					title: 'Test Website',
					content: 'This is a test website for a technology company that provides software solutions.',
				};

				await this.generateDescription(testContent, { minWords: 20, maxWords: 50 });
				results.set(providerId, true);

				this.logger.info('AI provider generation test successful', { providerId });
			}
			catch (error)
			{
				results.set(providerId, false);

				this.logger.error('AI provider generation test failed', {
					providerId,
					error: error.message,
				});
			}
		}

		return results;
	}

	/**
	 * Shutdown the generator
	 */
	async shutdown(): Promise<void>
	{
		this.logger.info('Shutting down Domain Description Generator');

		await this.providerManager.shutdown();

		// Clear metrics
		this.metrics.confidenceScores.length = 0;

		this.logger.info('Domain Description Generator shutdown complete');
	}
}

export type { DomainContent, GeneratedDescription, CompanyInfo };

export default DomainDescriptionGenerator;
