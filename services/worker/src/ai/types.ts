/**
 * AI-powered content generation and analysis types
 * Extracted from domain-seeder service for worker consolidation
 */

// AI Provider Types
type AIProviderType = 'openai' | 'anthropic' | 'google' | 'openrouter' | 'gemini';

type AIProviderConfigType =
{
	type: AIProviderType;
	model: string;
	apiKey: string;
	baseUrl?: string;
	proxy?: string;
	timeout?: number;
	maxRetries?: number;
	enabled: boolean;
};

type AIProvidersConfigType =
{
	[providerId: string]: AIProviderConfigType;
};

// Content Generation Types
type ContentGenerationRequestType =
{
	domain: string;
	brandInfo?: {
		name: string;
		parent?: string;
		categoryPrimary: string;
		tags: string[];
	};
	domainAnalysis?: {
		tld: string;
		domainName: string;
		tldCategory?: string;
		matchedCategories: string[];
		structuralAnalysis: string[];
		confidence: number;
	};
	webContext?: {
		title?: string;
		description?: string;
		content?: string;
		status: 'success' | 'failed' | 'disabled';
	};
	options?: ContentGenerationOptionsType;
};

type ContentGenerationOptionsType =
{
	minWords?: number;
	maxWords?: number;
	language?: string;
	seoOptimization?: boolean;
	includeKeywords?: boolean;
	includeAudience?: boolean;
	includeUseCases?: boolean;
	timeout?: number;
};

type GeneratedContentType =
{
	summary: string;
	keyFeatures: string[];
	audience: string[];
	useCases: string[];
	keywords: string[];
	category?: {
		primary: string;
		secondary?: string;
	};
	confidence: number;
	metadata: {
		provider: string;
		model: string;
		generatedAt: string;
		processingTime: number;
		tokenUsage?: {
			prompt: number;
			completion: number;
			total: number;
		};
	};
};

// Content Analysis Types
type ContentAnalysisRequestType =
{
	domain: string;
	content: string;
	title?: string;
	description?: string;
	options?: ContentAnalysisOptionsType;
};

type ContentAnalysisOptionsType =
{
	extractCompanyInfo?: boolean;
	detectBusinessModel?: boolean;
	classifyIndustry?: boolean;
	identifyTargetAudience?: boolean;
	extractKeywords?: boolean;
	analyzeSentiment?: boolean;
	timeout?: number;
};

type ContentAnalysisResultType =
{
	companyInfo?: {
		name?: string;
		description?: string;
		industry?: string;
		size?: string;
		founded?: string;
		headquarters?: string;
	};
	businessModel?: {
		type: string;
		revenue: string[];
		targetMarket: string;
		valueProposition: string;
	};
	industryClassification?: {
		primary: string;
		secondary: string[];
		confidence: number;
	};
	targetAudience?: {
		demographics: string[];
		psychographics: string[];
		businessSegments: string[];
	};
	keywords?: {
		primary: string[];
		secondary: string[];
		branded: string[];
	};
	sentiment?: {
		overall: 'positive' | 'neutral' | 'negative';
		score: number;
		aspects: Array<{
			aspect: string;
			sentiment: 'positive' | 'neutral' | 'negative';
			score: number;
		}>;
	};
	confidence: number;
	metadata: {
		provider: string;
		model: string;
		analyzedAt: string;
		processingTime: number;
	};
};

// AI Service Integration Types
type AIServiceUsageType =
{
	providerId: string;
	model: string;
	requestCount: number;
	tokenUsage: {
		prompt: number;
		completion: number;
		total: number;
	};
	costs: {
		prompt: number;
		completion: number;
		total: number;
	};
	averageLatency: number;
	errorCount: number;
	lastUsed: string;
};

type AIServiceMetricsType =
{
	totalRequests: number;
	totalTokens: number;
	totalCosts: number;
	averageLatency: number;
	errorRate: number;
	providerUsage: AIServiceUsageType[];
	dailyUsage: Array<{
		date: string;
		requests: number;
		tokens: number;
		costs: number;
	}>;
};

// Error Handling Types
type AIServiceErrorType =
{
	code: string;
	message: string;
	provider: string;
	model?: string;
	retryable: boolean;
	timestamp: string;
};

type AIFallbackConfigType =
{
	enabled: boolean;
	maxRetries: number;
	retryDelay: number;
	fallbackProviders: string[];
	errorThreshold: number;
};

// Prompt Management Types
type PromptTemplateType =
{
	id: string;
	name: string;
	description: string;
	systemPrompt: string;
	userTemplate: string;
	variables: string[];
	version: string;
	createdAt: string;
	updatedAt: string;
};

type PromptContextType =
{
	domain: string;
	brandInfo?: string;
	domainAnalysis?: string;
	webContext?: string;
	[key: string]: unknown;
};

// Quality Validation Types
type ContentQualityMetricsType =
{
	wordCount: number;
	readabilityScore: number;
	keywordDensity: number;
	sentimentScore: number;
	factualAccuracy: number;
	brandAlignment: number;
	seoOptimization: number;
	overallScore: number;
};

type QualityValidationResultType =
{
	passed: boolean;
	score: number;
	metrics: ContentQualityMetricsType;
	issues: Array<{
		type: 'warning' | 'error';
		message: string;
		suggestion?: string;
	}>;
	recommendations: string[];
};

export type {
	AIProviderType,
	AIProviderConfigType,
	AIProvidersConfigType,
	ContentGenerationRequestType,
	ContentGenerationOptionsType,
	GeneratedContentType,
	ContentAnalysisRequestType,
	ContentAnalysisOptionsType,
	ContentAnalysisResultType,
	AIServiceUsageType,
	AIServiceMetricsType,
	AIServiceErrorType,
	AIFallbackConfigType,
	PromptTemplateType,
	PromptContextType,
	ContentQualityMetricsType,
	QualityValidationResultType,
};
