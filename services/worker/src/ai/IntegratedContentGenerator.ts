/**
 * Integrated Content Generator - Manages AI-powered content generation
 * Extracted and consolidated from domain-seeder service for worker consolidation
 */

import type { Logger } from '@shared';
import { logger as sharedLogger } from '@shared';
import type {
	ContentGenerationRequest,
	ContentGenerationOptions,
	GeneratedContent,
	AIProvidersConfig,
	AIFallbackConfig,
} from './types';
import { DomainDescriptionGenerator } from './DomainDescriptionGenerator';
import { ContentAnalyzer } from './ContentAnalyzer';
import { ContentQualityValidator } from './ContentQualityValidator';
import { AIServiceMonitor } from './AIServiceMonitor';

const logger = sharedLogger.getLogger('IntegratedContentGenerator');

type ContentGenerationMode = 'ai' | 'heuristic' | 'hybrid';

type ContentVersionInfo = {
	domain: string;
	currentVersion: number;
	versions: Array<{
		version: number;
		method: ContentGenerationMode;
		generatedAt: string;
		confidence: number;
		updateReason?: string;
	}>;
	lastAI?: string;
	lastHeuristic?: string;
	scheduledForUpdate?: boolean;
	updatePriority?: 'high' | 'normal' | 'low';
};

type ContentUpdateRequest = {
	domain: string;
	fromMethod: ContentGenerationMode;
	toMethod: ContentGenerationMode;
	reason: string;
	priority?: 'high' | 'normal' | 'low';
	scheduledFor?: string;
};

type IntegratedContentConfig = {
	enableVersioning: boolean;
	maxVersionsPerDomain: number;
	autoScheduleAIUpdates: boolean;
	aiUpdateDelayHours: number;
	highPriorityDomainThreshold: number;
	contentUpdateBatchSize: number;
	enableContentMetrics: boolean;
	fallbackToHeuristic: boolean;
};

/**
 * Manages comprehensive AI-powered content generation with fallback strategies
 */
class IntegratedContentGenerator
{
	private readonly logger: Logger;

	private readonly descriptionGenerator: DomainDescriptionGenerator;

	private readonly contentAnalyzer: ContentAnalyzer;

	private readonly qualityValidator: ContentQualityValidator;

	private readonly serviceMonitor: AIServiceMonitor;

	private readonly config: IntegratedContentConfig;

	// Content versioning storage (in production, this would be Redis/ScyllaDB)
	private readonly contentVersions = new Map<string, ContentVersionInfo>();

	private readonly pendingUpdates = new Map<string, ContentUpdateRequest>();

	// Metrics
	private readonly metrics = {
		aiGenerationCount: 0,
		heuristicGenerationCount: 0,
		hybridGenerationCount: 0,
		contentUpdatesCount: 0,
		averageProcessingTime: 0,
		errorCount: 0,
		versioningOperations: 0,
		qualityScores: [] as number[],
	};

	constructor(
		providersConfig: AIProvidersConfig,
		config: Partial<IntegratedContentConfig> = {},
		fallbackConfig?: Partial<AIFallbackConfig>,
	)
	{
		this.logger = logger;

		this.config = {
			enableVersioning: config.enableVersioning ?? true,
			maxVersionsPerDomain: config.maxVersionsPerDomain ?? 5,
			autoScheduleAIUpdates: config.autoScheduleAIUpdates ?? true,
			aiUpdateDelayHours: config.aiUpdateDelayHours ?? 24,
			highPriorityDomainThreshold: config.highPriorityDomainThreshold ?? 0.8,
			contentUpdateBatchSize: config.contentUpdateBatchSize ?? 100,
			enableContentMetrics: config.enableContentMetrics ?? true,
			fallbackToHeuristic: config.fallbackToHeuristic ?? true,
		};

		// Initialize AI components
		this.descriptionGenerator = new DomainDescriptionGenerator(providersConfig, fallbackConfig);
		this.contentAnalyzer = new ContentAnalyzer(providersConfig, fallbackConfig);
		this.qualityValidator = new ContentQualityValidator();
		this.serviceMonitor = new AIServiceMonitor();

		this.logger.info('Integrated Content Generator initialized', {
			config: this.config,
			availableProviders: Object.keys(providersConfig),
		});
	}

	/**
	 * Generate content for multiple domains
	 */
	async generateContentForDomains(
		domains: string[],
		options: Partial<ContentGenerationOptions> = {},
	): Promise<Map<string, GeneratedContent>>
	{
		const startTime = Date.now();
		const results = new Map<string, GeneratedContent>();

		if (domains.length === 0)
		{
			return results;
		}

		const mergedOptions: ContentGenerationOptions = {
			minWords: options.minWords ?? 50,
			maxWords: options.maxWords ?? 200,
			language: options.language ?? 'en',
			seoOptimization: options.seoOptimization ?? true,
			includeKeywords: options.includeKeywords ?? true,
			includeAudience: options.includeAudience ?? true,
			includeUseCases: options.includeUseCases ?? true,
			timeout: options.timeout ?? 30000,
		};

		this.logger.info('Starting content generation for domains', {
			domainCount: domains.length,
			options: mergedOptions,
		});

		// Process domains in batches to avoid overwhelming the system
		const batches = this.createBatches(domains, this.config.contentUpdateBatchSize);

		for (const batch of batches)
		{
			const batchResults = await this.processBatch(batch, mergedOptions);

			// Merge batch results
			for (const [domain, content] of batchResults)
			{
				results.set(domain, content);
			}
		}

		// Update metrics
		const processingTime = Date.now() - startTime;
		this.updateMetrics(results.size, processingTime);

		this.logger.info('Content generation completed for domains', {
			domainCount: domains.length,
			successCount: results.size,
			processingTime,
		});

		return results;
	}

	/**
	 * Generate content for a single domain with versioning support
	 */
	async generateContentForDomain(
		domain: string,
		options: ContentGenerationOptions,
		mode: ContentGenerationMode = 'ai',
	): Promise<GeneratedContent>
	{
		const startTime = Date.now();

		try
		{
			let content: GeneratedContent;

			switch (mode)
			{
				case 'ai':
					content = await this.generateAIContent(domain, options);
					break;
				case 'heuristic':
					content = await this.generateHeuristicContent(domain, options);
					break;
				case 'hybrid':
					content = await this.generateHybridContent(domain, options);
					break;
				default:
					throw new Error(`Unsupported content generation mode: ${mode}`);
			}

			// Validate content quality
			const validationResult = await this.qualityValidator.validateContent(content);

			if (!validationResult.passed)
			{
				this.logger.warn('Generated content failed quality validation', {
					domain,
					mode,
					score: validationResult.score,
					issues: validationResult.issues.length,
				});

				// Try fallback if quality is too low and fallback is enabled
				if (this.config.fallbackToHeuristic && mode === 'ai' && validationResult.score < 0.5)
				{
					this.logger.info('Falling back to heuristic content generation', { domain });
					content = await this.generateHeuristicContent(domain, options);
				}
			}

			// Handle versioning if enabled
			if (this.config.enableVersioning)
			{
				await this.updateContentVersion(domain, content, mode, 'automatic');
			}

			const processingTime = Date.now() - startTime;
			content.metadata.processingTime = processingTime;

			// Update metrics
			this.updateMetrics(1, processingTime);
			this.metrics.qualityScores.push(validationResult.score);

			this.logger.info('Content generated for domain', {
				domain,
				mode,
				confidence: content.confidence,
				processingTime,
				qualityScore: validationResult.score,
			});

			return content;
		}
		catch (error)
		{
			this.metrics.errorCount++;
			this.logger.error('Content generation failed for domain', {
				domain,
				mode,
				error: error.message,
			});

			// Try fallback if enabled
			if (this.config.fallbackToHeuristic && mode !== 'heuristic')
			{
				this.logger.info('Attempting fallback content generation', { domain });
				return this.generateHeuristicContent(domain, options);
			}

			throw error;
		}
	}

	/**
	 * Generate AI-powered content
	 */
	private async generateAIContent(
		domain: string,
		options: ContentGenerationOptions,
	): Promise<GeneratedContent>
	{
		const request: ContentGenerationRequest = {
			domain,
			options,
		};

		// Use DomainDescriptionGenerator for AI content
		const domainContent = {
			domain,
			title: `${domain} - Website`,
			description: 'AI-generated content',
		};

		const description = await this.descriptionGenerator.generateDescription(domainContent, options);

		// Convert to GeneratedContent format
		const content: GeneratedContent = {
			summary: description.longDescription,
			keyFeatures: description.keyFeatures,
			audience: [description.targetAudience],
			useCases: [], // Would be populated by AI analysis
			keywords: [], // Would be populated by AI analysis
			category: { primary: 'business' }, // Would be determined by AI
			confidence: description.confidence,
			metadata: {
				provider: 'ai',
				model: 'integrated',
				generatedAt: description.generatedAt.toISOString(),
				processingTime: 0, // Will be set by caller
			},
		};

		this.metrics.aiGenerationCount++;
		return content;
	}

	/**
	 * Generate heuristic-based content (fallback)
	 */
	private async generateHeuristicContent(
		domain: string,
		options: ContentGenerationOptions,
	): Promise<GeneratedContent>
	{
		// Simple heuristic content generation based on domain analysis
		const tokens = domain.split('.')[0].split(/[-_]/);
		const tld = domain.split('.').pop() || 'com';

		const summary = `${domain} is a ${this.inferCategoryFromDomain(domain)} website that provides online services and information. The platform offers various tools and resources designed to meet user needs with a focus on quality and reliability.`;

		const content: GeneratedContent = {
			summary,
			keyFeatures: this.generateHeuristicFeatures(tokens, tld),
			audience: this.generateHeuristicAudience(tokens, tld),
			useCases: this.generateHeuristicUseCases(tokens, tld),
			keywords: this.generateHeuristicKeywords(tokens, tld),
			category: { primary: this.inferCategoryFromDomain(domain) },
			confidence: 0.4, // Lower confidence for heuristic content
			metadata: {
				provider: 'heuristic',
				model: 'rule-based',
				generatedAt: new Date().toISOString(),
				processingTime: 0,
			},
		};

		this.metrics.heuristicGenerationCount++;
		return content;
	}

	/**
	 * Generate hybrid content (AI + heuristic)
	 */
	private async generateHybridContent(
		domain: string,
		options: ContentGenerationOptions,
	): Promise<GeneratedContent>
	{
		try
		{
			// Try AI first
			const aiContent = await this.generateAIContent(domain, options);

			// Enhance with heuristic data
			const heuristicContent = await this.generateHeuristicContent(domain, options);

			// Merge the best of both
			const hybridContent: GeneratedContent = {
				summary: aiContent.summary,
				keyFeatures: [...aiContent.keyFeatures, ...heuristicContent.keyFeatures].slice(0, 5),
				audience: [...aiContent.audience, ...heuristicContent.audience].slice(0, 3),
				useCases: [...aiContent.useCases, ...heuristicContent.useCases].slice(0, 3),
				keywords: [...aiContent.keywords, ...heuristicContent.keywords].slice(0, 8),
				category: aiContent.category,
				confidence: Math.max(aiContent.confidence, heuristicContent.confidence * 0.8),
				metadata: {
					provider: 'hybrid',
					model: 'ai-heuristic',
					generatedAt: new Date().toISOString(),
					processingTime: 0,
				},
			};

			this.metrics.hybridGenerationCount++;
			return hybridContent;
		}
		catch (error)
		{
			// Fallback to heuristic if AI fails
			this.logger.warn('AI generation failed in hybrid mode, using heuristic', {
				domain,
				error: error.message,
			});

			return this.generateHeuristicContent(domain, options);
		}
	}

	/**
	 * Process a batch of domains
	 */
	private async processBatch(
		domains: string[],
		options: ContentGenerationOptions,
	): Promise<Map<string, GeneratedContent>>
	{
		const results = new Map<string, GeneratedContent>();

		const batchPromises = domains.map(async (domain) =>
		{
			try
			{
				const content = await this.generateContentForDomain(domain, options);
				return { domain, content };
			}
			catch (error)
			{
				this.logger.error('Batch content generation failed for domain', {
					domain,
					error: error.message,
				});

				// Return fallback content
				const fallbackContent = await this.generateHeuristicContent(domain, options);
				return { domain, content: fallbackContent };
			}
		});

		const batchResults = await Promise.allSettled(batchPromises);

		for (const result of batchResults)
		{
			if (result.status === 'fulfilled')
			{
				results.set(result.value.domain, result.value.content);
			}
		}

		return results;
	}

	/**
	 * Update content version information
	 */
	private async updateContentVersion(
		domain: string,
		content: GeneratedContent,
		method: ContentGenerationMode,
		reason: string,
	): Promise<void>
	{
		let versionInfo = this.contentVersions.get(domain);

		if (!versionInfo)
		{
			versionInfo = {
				domain,
				currentVersion: 1,
				versions: [],
			};
		}
		else
		{
			versionInfo.currentVersion++;
		}

		// Add new version
		versionInfo.versions.push({
			version: versionInfo.currentVersion,
			method,
			generatedAt: content.metadata.generatedAt,
			confidence: content.confidence,
			updateReason: reason,
		});

		// Limit version history
		if (versionInfo.versions.length > this.config.maxVersionsPerDomain)
		{
			versionInfo.versions = versionInfo.versions.slice(-this.config.maxVersionsPerDomain);
		}

		// Update timestamps
		if (method === 'ai')
		{
			versionInfo.lastAI = content.metadata.generatedAt;
		}
		else if (method === 'heuristic')
		{
			versionInfo.lastHeuristic = content.metadata.generatedAt;
		}

		this.contentVersions.set(domain, versionInfo);
		this.metrics.versioningOperations++;

		// Update content metadata
		content.metadata.contentVersion = versionInfo.currentVersion;
		content.metadata.lastUpdated = new Date().toISOString();
		content.metadata.updateReason = reason;
	}

	/**
	 * Helper methods for heuristic content generation
	 */
	private inferCategoryFromDomain(domain: string): string
	{
		const domainLower = domain.toLowerCase();

		if (domainLower.includes('shop') || domainLower.includes('store'))
		{
			return 'e-commerce';
		}
		if (domainLower.includes('tech') || domainLower.includes('dev'))
		{
			return 'technology';
		}
		if (domainLower.includes('health') || domainLower.includes('medical'))
		{
			return 'health';
		}
		if (domainLower.includes('edu') || domainLower.includes('learn'))
		{
			return 'education';
		}

		return 'business';
	}

	private generateHeuristicFeatures(tokens: string[], tld: string): string[]
	{
		const features = ['online platform', 'user-friendly interface', 'reliable service'];

		// Add token-based features
		for (const token of tokens.slice(0, 2))
		{
			if (token.length > 2)
			{
				features.push(`${token} solutions`);
			}
		}

		// Add TLD-based features
		if (tld === 'io')
		{
			features.push('developer tools');
		}
		else if (tld === 'ai')
		{
			features.push('AI-powered features');
		}

		return features.slice(0, 5);
	}

	private generateHeuristicAudience(tokens: string[], tld: string): string[]
	{
		const audience = ['general users'];

		if (tld === 'io' || tokens.some(t => ['dev', 'api', 'code'].includes(t)))
		{
			audience.push('developers');
		}
		if (tokens.some(t => ['business', 'enterprise', 'corp'].includes(t)))
		{
			audience.push('businesses');
		}

		return audience.slice(0, 3);
	}

	private generateHeuristicUseCases(tokens: string[], tld: string): string[]
	{
		const useCases = ['information access', 'online services'];

		// Add token-based use cases
		if (tokens.some(t => ['shop', 'store', 'buy'].includes(t)))
		{
			useCases.push('online shopping');
		}
		if (tokens.some(t => ['learn', 'edu', 'course'].includes(t)))
		{
			useCases.push('learning and education');
		}

		return useCases.slice(0, 3);
	}

	private generateHeuristicKeywords(tokens: string[], tld: string): string[]
	{
		const keywords = [...tokens.filter(t => t.length > 2)];

		// Add common keywords
		keywords.push('website', 'online', 'service');

		// Add TLD-based keywords
		if (tld === 'com')
		{
			keywords.push('commercial', 'business');
		}
		else if (tld === 'org')
		{
			keywords.push('organization', 'nonprofit');
		}

		return [...new Set(keywords)].slice(0, 8);
	}

	/**
	 * Create batches for processing
	 */
	private createBatches<T>(items: T[], batchSize: number): T[][]
	{
		const batches: T[][] = [];
		for (let i = 0; i < items.length; i += batchSize)
		{
			batches.push(items.slice(i, i + batchSize));
		}
		return batches;
	}

	/**
	 * Update metrics
	 */
	private updateMetrics(count: number, processingTime: number): void
	{
		// Update average processing time
		const totalOperations = this.metrics.aiGenerationCount
			+ this.metrics.heuristicGenerationCount
			+ this.metrics.hybridGenerationCount;

		if (totalOperations > 0)
		{
			this.metrics.averageProcessingTime =
				(this.metrics.averageProcessingTime * (totalOperations - count) + processingTime) / totalOperations;
		}
		else
		{
			this.metrics.averageProcessingTime = processingTime;
		}
	}

	/**
	 * Get comprehensive metrics
	 */
	getMetrics(): typeof this.metrics & {
		averageQualityScore: number;
		successRate: number;
		aiServiceMetrics: any;
	}
	{
		const averageQualityScore = this.metrics.qualityScores.length > 0
			? this.metrics.qualityScores.reduce((sum, score) => sum + score, 0) / this.metrics.qualityScores.length
			: 0;

		const totalOperations = this.metrics.aiGenerationCount
			+ this.metrics.heuristicGenerationCount
			+ this.metrics.hybridGenerationCount;

		const successRate = totalOperations > 0
			? (totalOperations - this.metrics.errorCount) / totalOperations
			: 0;

		return {
			...this.metrics,
			averageQualityScore,
			successRate,
			aiServiceMetrics: this.serviceMonitor.getMetrics(),
		};
	}

	/**
	 * Get content version information
	 */
	getContentVersionInfo(domain: string): ContentVersionInfo | null
	{
		return this.contentVersions.get(domain) || null;
	}

	/**
	 * Test AI providers
	 */
	async testProviders(): Promise<Map<string, boolean>>
	{
		return this.descriptionGenerator.testProviders();
	}

	/**
	 * Shutdown the generator
	 */
	async shutdown(): Promise<void>
	{
		this.logger.info('Shutting down Integrated Content Generator');

		await this.descriptionGenerator.shutdown();
		await this.contentAnalyzer.shutdown();

		// Clear data
		this.contentVersions.clear();
		this.pendingUpdates.clear();
		this.metrics.qualityScores.length = 0;

		this.logger.info('Integrated Content Generator shutdown complete');
	}
}

export type {
	ContentGenerationMode,
	ContentVersionInfo,
	ContentUpdateRequest,
	IntegratedContentConfig,
};

export { IntegratedContentGenerator };
