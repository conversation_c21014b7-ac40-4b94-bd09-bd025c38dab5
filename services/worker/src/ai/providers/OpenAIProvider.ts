/**
 * OpenAI Provider - AI content generation using OpenAI API
 * Extracted and consolidated from domain-seeder service
 */

import { openai } from '@ai-sdk/openai';
import { generateText } from 'ai';
import type { AIProviderConfigType } from '../types';
import type { AIResponseType } from './BaseAIProvider';
import BaseAIProvider from './BaseAIProvider';

/**
 * OpenAI provider for content generation and analysis
 */
class OpenAIProvider extends BaseAIProvider
{
	private client: any;

	constructor(providerId: string, config: AIProviderConfigType)
	{
		super(providerId, config);
		this.initializeClient();
	}

	/**
	 * Initialize OpenAI client
	 */
	private initializeClient(): void
	{
		try
		{
			this.client = openai({
				apiKey: this.config.apiKey,
				...(this.config.baseUrl && { baseURL: this.config.baseUrl }),
			});

			this.logger.info('OpenAI provider initialized', {
				providerId: this.providerId,
				model: this.config.model,
				baseUrl: this.config.baseUrl || 'default',
			});
		}
		catch (error: unknown)
		{
			const errorMessage = error instanceof Error ? error.message : String(error);
			this.logger.error('Failed to initialize OpenAI provider', {
				providerId: this.providerId,
				error: errorMessage,
			});
			throw error;
		}
	}

	/**
	 * Generate content using OpenAI
	 */
	override async generateContent(systemPrompt: string, userPrompt: string): Promise<AIResponse>
	{
		const startTime = Date.now();

		try
		{
			this.logger.debug('Generating content with OpenAI', {
				providerId: this.providerId,
				model: this.config.model,
				systemPromptLength: systemPrompt.length,
				userPromptLength: userPrompt.length,
			});

			const result = await generateText({
				model: this.client(this.config.model),
				system: systemPrompt,
				prompt: userPrompt,
				temperature: 0.7,
				maxTokens: 2000,
				timeout: this.config.timeout || 30000,
			});

			const processingTime = Date.now() - startTime;

			// Parse JSON response
			const content = this.parseJSONResponse(result.text);

			// Extract token usage
			const tokenUsage = result.usage ? {
				prompt: result.usage.promptTokens,
				completion: result.usage.completionTokens,
				total: result.usage.totalTokens,
			} : undefined;

			this.logger.info('OpenAI content generation successful', {
				providerId: this.providerId,
				model: this.config.model,
				processingTime,
				tokenUsage,
			});

			return this.createResponse(content, tokenUsage);
		}
		catch (error: unknown)
		{
			const processingTime = Date.now() - startTime;
			const errorMessage = error instanceof Error ? error.message : String(error);
			this.logger.error('OpenAI content generation failed', {
				providerId: this.providerId,
				model: this.config.model,
				processingTime,
				error: errorMessage,
			});

			await this.handleAPIError(error);
		}
	}

	/**
	 * Analyze content using OpenAI
	 */
	override async analyzeContent(systemPrompt: string, userPrompt: string): Promise<AIResponse>
	{
		const startTime = Date.now();

		try
		{
			this.logger.debug('Analyzing content with OpenAI', {
				providerId: this.providerId,
				model: this.config.model,
				systemPromptLength: systemPrompt.length,
				userPromptLength: userPrompt.length,
			});

			const result = await generateText({
				model: this.client(this.config.model),
				system: systemPrompt,
				prompt: userPrompt,
				temperature: 0.3, // Lower temperature for analysis
				maxTokens: 3000,
				timeout: this.config.timeout || 30000,
			});

			const processingTime = Date.now() - startTime;

			// Parse JSON response
			const content = this.parseJSONResponse(result.text);

			// Extract token usage
			const tokenUsage = result.usage ? {
				prompt: result.usage.promptTokens,
				completion: result.usage.completionTokens,
				total: result.usage.totalTokens,
			} : undefined;

			this.logger.info('OpenAI content analysis successful', {
				providerId: this.providerId,
				model: this.config.model,
				processingTime,
				tokenUsage,
			});

			return this.createResponse(content, tokenUsage);
		}
		catch (error: unknown)
		{
			const processingTime = Date.now() - startTime;
			const errorMessage = error instanceof Error ? error.message : String(error);
			this.logger.error('OpenAI content analysis failed', {
				providerId: this.providerId,
				model: this.config.model,
				processingTime,
				error: errorMessage,
			});

			await this.handleAPIError(error);
		}
	}

	/**
	 * Test OpenAI connection
	 */
	override async testConnection(): Promise<boolean>
	{
		try
		{
			const result = await generateText({
				model: this.client(this.config.model),
				prompt: 'Test connection. Respond with "OK".',
				maxTokens: 10,
				timeout: 10000,
			});

			const success = result.text.toLowerCase().includes('ok');

			this.logger.info('OpenAI connection test completed', {
				providerId: this.providerId,
				success,
				response: result.text,
			});

			return success;
		}
		catch (error: unknown)
		{
			const errorMessage = error instanceof Error ? error.message : String(error);
			this.logger.error('OpenAI connection test failed', {
				providerId: this.providerId,
				error: errorMessage,
			});
			return false;
		}
	}

	/**
	 * Get supported models for this provider
	 */
	getSupportedModels(): string[]
	{
		return [
			'gpt-4o',
			'gpt-4o-mini',
			'gpt-4-turbo',
			'gpt-4',
			'gpt-3.5-turbo',
		];
	}

	/**
	 * Validate model is supported
	 */
	protected override validateConfig(): void
	{
		super.validateConfig();

		const supportedModels = this.getSupportedModels();
		if (!supportedModels.includes(this.config.model))
		{
			this.logger.warn('Unsupported OpenAI model specified', {
				providerId: this.providerId,
				model: this.config.model,
				supportedModels,
			});
		}
	}

	/**
	 * Shutdown OpenAI provider
	 */
	override async shutdown(): Promise<void>
	{
		await super.shutdown();
		this.client = null;
	}
}

export default OpenAIProvider;
