/**
 * Base AI Provider - Abstract base class for all AI providers
 * Extracted and consolidated from domain-seeder service
 */

import { logger as sharedLogger, Logger } from '@shared';
import type { AIProviderConfigType } from '../types';

const logger = sharedLogger.getLogger('BaseAIProvider');

type AIResponseType =
{
	content: any;
	tokenUsage?: {
		prompt: number;
		completion: number;
		total: number;
	};
	model: string;
	provider: string;
	timestamp: string;
};

/**
 * Abstract base class for AI providers
 */
abstract class BaseAIProvider
{
	protected readonly logger: ReturnType<Logger['getLogger']>;

	protected readonly providerId: string;

	protected readonly config: AIProviderConfigType;

	constructor(providerId: string, config: AIProviderConfigType)
	{
		this.logger = logger;
		this.providerId = providerId;
		this.config = config;

		this.validateConfig();
	}

	/**
	 * Validate provider configuration
	 */
	protected validateConfig(): void
	{
		if (!this.config.apiKey)
		{
			throw new Error(`API key is required for provider ${this.providerId}`);
		}

		if (!this.config.model)
		{
			throw new Error(`Model is required for provider ${this.providerId}`);
		}

		if (this.config.timeout && this.config.timeout < 1000)
		{
			throw new Error(`Timeout must be at least 1000ms for provider ${this.providerId}`);
		}
	}

	/**
	 * Generate content using the AI provider
	 */
	abstract generateContent(systemPrompt: string, userPrompt: string): Promise<AIResponseType>;

	/**
	 * Analyze content using the AI provider
	 */
	abstract analyzeContent(systemPrompt: string, userPrompt: string): Promise<AIResponseType>;

	/**
	 * Get the model name
	 */
	getModel(): string
	{
		return this.config.model;
	}

	/**
	 * Get the provider ID
	 */
	getProviderId(): string
	{
		return this.providerId;
	}

	/**
	 * Get the provider type
	 */
	getProviderType(): string
	{
		return this.config.type;
	}

	/**
	 * Check if provider is enabled
	 */
	isEnabled(): boolean
	{
		return this.config.enabled;
	}

	/**
	 * Test provider connectivity
	 */
	async testConnection(): Promise<boolean>
	{
		try
		{
			const testPrompt = 'Test connection. Respond with "OK".';
			await this.generateContent('You are a test assistant.', testPrompt);
			return true;
		}
		catch (error: unknown)
		{
			const errorMessage = error instanceof Error ? error.message : String(error);
			this.logger.error('Provider connection test failed', {
				providerId: this.providerId,
				error: errorMessage,
			});
			return false;
		}
	}

	/**
	 * Parse JSON response with error handling
	 */
	protected parseJSONResponse(content: string): any
	{
		try
		{
			// Remove code fences if present
			const cleanContent = content.replace(/```json\n?|\n?```/g, '').trim();
			return JSON.parse(cleanContent);
		}
		catch (error: unknown)
		{
			const errorMessage = error instanceof Error ? error.message : String(error);
			this.logger.error('Failed to parse JSON response', {
				providerId: this.providerId,
				content: content.substring(0, 200),
				error: errorMessage,
			});
			throw new Error(`Invalid JSON response from ${this.providerId}: ${errorMessage}`);
		}
	}

	/**
	 * Handle API errors with retry logic
	 */
	protected async handleAPIError(error: any, attempt: number = 1): Promise<never>
	{
		const maxRetries = this.config.maxRetries || 3;
		const isRetryable = this.isRetryableError(error);

		this.logger.error('AI provider API error', {
			providerId: this.providerId,
			attempt,
			maxRetries,
			isRetryable,
			error: error.message,
			statusCode: error.status || error.statusCode,
		});

		if (isRetryable && attempt < maxRetries)
		{
			const delay = Math.min(1000 * 2**(attempt - 1), 10000); // Exponential backoff, max 10s
			this.logger.info('Retrying AI provider request', {
				providerId: this.providerId,
				attempt: attempt + 1,
				delay,
			});

			await this.delay(delay);
			throw new Error(`Retry needed for ${this.providerId}`);
		}

		// Transform error for consistent handling
		const errorMessage = error.message || error.toString();
		throw new Error(`${this.providerId} API error: ${errorMessage}`);
	}

	/**
	 * Determine if an error is retryable
	 */
	protected isRetryableError(error: any): boolean
	{
		const statusCode = error.status || error.statusCode;

		// Retryable HTTP status codes
		const retryableStatusCodes = [408, 429, 500, 502, 503, 504];
		if (statusCode && retryableStatusCodes.includes(statusCode))
		{
			return true;
		}

		// Retryable error messages
		const retryablePatterns = [
			/timeout/i,
			/rate limit/i,
			/service unavailable/i,
			/internal server error/i,
			/network/i,
			/connection/i,
			/temporary/i,
		];

		const errorMessage = this.getErrorMessage(error);
		return retryablePatterns.some(pattern => pattern.test(errorMessage));
	}

	/**
	 * Extract error message from various error formats
	 */
	protected getErrorMessage(error: any): string
	{
		if (typeof error === 'string')
		{
			return error;
		}

		if (error.message)
		{
			return error.message;
		}

		if (error.error?.message)
		{
			return error.error.message;
		}

		if (error.response?.data?.error?.message)
		{
			return error.response.data.error.message;
		}

		return 'Unknown error';
	}

	/**
	 * Add delay utility
	 */
	protected delay(ms: number): Promise<void>
	{
		return new Promise(resolve => setTimeout(resolve, ms));
	}

	/**
	 * Create standardized response
	 */
	protected createResponse(content: any, tokenUsage?: any): AIResponseType
	{
		return {
			content,
			tokenUsage,
			model: this.config.model,
			provider: this.providerId,
			timestamp: new Date().toISOString(),
		};
	}

	/**
	 * Shutdown provider (cleanup resources)
	 */
	async shutdown(): Promise<void>
	{
		this.logger.debug('Shutting down AI provider', { providerId: this.providerId });
		// Base implementation - override in subclasses if needed
	}
}

export type { AIResponseType };

export default BaseAIProvider;
