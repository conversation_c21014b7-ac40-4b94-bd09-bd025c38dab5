/**
 * Google Provider - AI content generation using Google Gemini API
 * Extracted and consolidated from domain-seeder service
 */

import { google } from '@ai-sdk/google';
import { generateText } from 'ai';
import type { AIResponseType } from './BaseAIProvider';
import type { AIProviderConfigType } from '../types';
import BaseAIProvider from './BaseAIProvider';

/**
 * Google Gemini provider for content generation and analysis
 */
class GoogleProvider extends BaseAIProvider
{
	private client: any;

	constructor(providerId: string, config: AIProviderConfigType)
	{
		super(providerId, config);
		this.initializeClient();
	}

	/**
	 * Initialize Google client
	 */
	private initializeClient(): void
	{
		try
		{
			this.client = google({
				apiKey: this.config.apiKey,
				baseURL: this.config.baseUrl,
			});

			this.logger.info('Google provider initialized', {
				providerId: this.providerId,
				model: this.config.model,
				baseUrl: this.config.baseUrl || 'default',
			});
		}
		catch (error)
		{
			this.logger.error('Failed to initialize Google provider', {
				providerId: this.providerId,
				error: error.message,
			});
			throw error;
		}
	}

	/**
	 * Generate content using Google Gemini
	 */
	override async generateContent(systemPrompt: string, userPrompt: string): Promise<AIResponseType>
	{
		const startTime = Date.now();

		try
		{
			this.logger.debug('Generating content with Google', {
				providerId: this.providerId,
				model: this.config.model,
				systemPromptLength: systemPrompt.length,
				userPromptLength: userPrompt.length,
			});

			// Combine system and user prompts for Gemini
			const combinedPrompt = `${systemPrompt}\n\n${userPrompt}`;

			const result = await generateText({
				model: this.client(this.config.model),
				prompt: combinedPrompt,
				temperature: 0.7,
				maxCompletionTokens: 2000,
				abortSignal: AbortSignal.timeout(this.config.timeout || 30000),
			});

			const processingTime = Date.now() - startTime;

			// Parse JSON response
			const content = this.parseJSONResponse(result.text);

			// Extract token usage
			const tokenUsage = result.usage ? {
				prompt: result.usage.promptTokens,
				completion: result.usage.completionTokens,
				total: result.usage.totalTokens,
			} : undefined;

			this.logger.info('Google content generation successful', {
				providerId: this.providerId,
				model: this.config.model,
				processingTime,
				tokenUsage,
			});

			return this.createResponse(content, tokenUsage);
		}
		catch (error: unknown)
		{
			const processingTime = Date.now() - startTime;
			const errorMessage = error instanceof Error ? error.message : String(error);
			this.logger.error('Google content generation failed', {
				providerId: this.providerId,
				model: this.config.model,
				processingTime,
				error: errorMessage,
			});

			await this.handleAPIError(error);
			throw error;
		}
	}

	/**
	 * Analyze content using Google Gemini
	 */
	override async analyzeContent(systemPrompt: string, userPrompt: string): Promise<AIResponseType>
	{
		const startTime = Date.now();

		try
		{
			this.logger.debug('Analyzing content with Google', {
				providerId: this.providerId,
				model: this.config.model,
				systemPromptLength: systemPrompt.length,
				userPromptLength: userPrompt.length,
			});

			// Combine system and user prompts for Gemini
			const combinedPrompt = `${systemPrompt}\n\n${userPrompt}`;

			const result = await generateText({
				model: this.client(this.config.model),
				prompt: combinedPrompt,
				temperature: 0.3, // Lower temperature for analysis
				maxCompletionTokens: 3000,
				abortSignal: AbortSignal.timeout(this.config.timeout || 30000),
			});

			const processingTime = Date.now() - startTime;

			// Parse JSON response
			const content = this.parseJSONResponse(result.text);

			// Extract token usage
			const tokenUsage = result.usage ? {
				prompt: result.usage.promptTokens,
				completion: result.usage.completionTokens,
				total: result.usage.totalTokens,
			} : undefined;

			this.logger.info('Google content analysis successful', {
				providerId: this.providerId,
				model: this.config.model,
				processingTime,
				tokenUsage,
			});

			return this.createResponse(content, tokenUsage);
		}
		catch (error: unknown)
		{
			const processingTime = Date.now() - startTime;
			const errorMessage = error instanceof Error ? error.message : String(error);
			this.logger.error('Google content analysis failed', {
				providerId: this.providerId,
				model: this.config.model,
				processingTime,
				error: errorMessage,
			});

			await this.handleAPIError(error);
			throw error;
		}
	}

	/**
	 * Test Google connection
	 */
	override async testConnection(): Promise<boolean>
	{
		try
		{
			const result = await generateText({
				model: this.client(this.config.model),
				prompt: 'Test connection. Respond with "OK".',
				maxCompletionTokens: 10,
				abortSignal: AbortSignal.timeout(10000),
			});

			const success = result.text.toLowerCase().includes('ok');

			this.logger.info('Google connection test completed', {
				providerId: this.providerId,
				success,
				response: result.text,
			});

			return success;
		}
		catch (error: unknown)
		{
			const errorMessage = error instanceof Error ? error.message : String(error);
			this.logger.error('Google connection test failed', {
				providerId: this.providerId,
				error: errorMessage,
			});
			return false;
		}
	}

	/**
	 * Get supported models for this provider
	 */
	override getSupportedModels(): string[]
	{
		return [
			'gemini-1.5-pro',
			'gemini-1.5-flash',
			'gemini-1.0-pro',
		];
	}

	/**
	 * Validate model is supported
	 */
	protected override validateConfig(): void
	{
		super.validateConfig();

		const supportedModels = this.getSupportedModels();
		if (!supportedModels.includes(this.config.model))
		{
			this.logger.warn('Unsupported Google model specified', {
				providerId: this.providerId,
				model: this.config.model,
				supportedModels,
			});
		}
	}

	/**
	 * Shutdown Google provider
	 */
	override async shutdown(): Promise<void>
	{
		await super.shutdown();
		this.client = null;
	}
}

export default GoogleProvider;
