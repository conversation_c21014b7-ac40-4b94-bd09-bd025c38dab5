/**
 * Anthropic Provider - AI content generation using Anthropic Claude API
 * Extracted and consolidated from domain-seeder service
 */

import { anthropic } from '@ai-sdk/anthropic';
import { generateText } from 'ai';
import type { AIProviderConfigType } from '../types';
import type { AIResponseType } from './BaseAIProvider';
import BaseAIProvider from './BaseAIProvider';

/**
 * Anthropic Claude provider for content generation and analysis
 */
class AnthropicProvider extends BaseAIProvider
{
	private client: any;

	constructor(providerId: string, config: AIProviderConfigType)
	{
		super(providerId, config);
		this.initializeClient();
	}

	/**
	 * Initialize Anthropic client
	 */
	private initializeClient(): void
	{
		try
		{
			this.client = anthropic({
				apiKey: this.config.apiKey,
				baseURL: this.config.baseUrl,
			});

			this.logger.info('Anthropic provider initialized', {
				providerId: this.providerId,
				model: this.config.model,
				baseUrl: this.config.baseUrl || 'default',
			});
		}
		catch (error)
		{
			this.logger.error('Failed to initialize Anthropic provider', {
				providerId: this.providerId,
				error: error.message,
			});
			throw error;
		}
	}

	/**
	 * Generate content using Anthropic Claude
	 */
	async generateContent(systemPrompt: string, userPrompt: string): Promise<AIResponseType>
	{
		const startTime = Date.now();

		try
		{
			this.logger.debug('Generating content with Anthropic', {
				providerId: this.providerId,
				model: this.config.model,
				systemPromptLength: systemPrompt.length,
				userPromptLength: userPrompt.length,
			});

			const result = await generateText({
				model: this.client(this.config.model),
				system: systemPrompt,
				prompt: userPrompt,
				temperature: 0.7,
				maxTokens: 2000,
				timeout: this.config.timeout || 30000,
			});

			const processingTime = Date.now() - startTime;

			// Parse JSON response
			const content = this.parseJSONResponse(result.text);

			// Extract token usage
			const tokenUsage = result.usage ? {
				prompt: result.usage.promptTokens,
				completion: result.usage.completionTokens,
				total: result.usage.totalTokens,
			} : undefined;

			this.logger.info('Anthropic content generation successful', {
				providerId: this.providerId,
				model: this.config.model,
				processingTime,
				tokenUsage,
			});

			return this.createResponse(content, tokenUsage);
		}
		catch (error)
		{
			const processingTime = Date.now() - startTime;
			this.logger.error('Anthropic content generation failed', {
				providerId: this.providerId,
				model: this.config.model,
				processingTime,
				error: error.message,
			});

			await this.handleAPIError(error);
		}
	}

	/**
	 * Analyze content using Anthropic Claude
	 */
	async analyzeContent(systemPrompt: string, userPrompt: string): Promise<AIResponseType>
	{
		const startTime = Date.now();

		try
		{
			this.logger.debug('Analyzing content with Anthropic', {
				providerId: this.providerId,
				model: this.config.model,
				systemPromptLength: systemPrompt.length,
				userPromptLength: userPrompt.length,
			});

			const result = await generateText({
				model: this.client(this.config.model),
				system: systemPrompt,
				prompt: userPrompt,
				temperature: 0.3, // Lower temperature for analysis
				maxTokens: 3000,
				timeout: this.config.timeout || 30000,
			});

			const processingTime = Date.now() - startTime;

			// Parse JSON response
			const content = this.parseJSONResponse(result.text);

			// Extract token usage
			const tokenUsage = result.usage ? {
				prompt: result.usage.promptTokens,
				completion: result.usage.completionTokens,
				total: result.usage.totalTokens,
			} : undefined;

			this.logger.info('Anthropic content analysis successful', {
				providerId: this.providerId,
				model: this.config.model,
				processingTime,
				tokenUsage,
			});

			return this.createResponse(content, tokenUsage);
		}
		catch (error)
		{
			const processingTime = Date.now() - startTime;
			this.logger.error('Anthropic content analysis failed', {
				providerId: this.providerId,
				model: this.config.model,
				processingTime,
				error: error.message,
			});

			await this.handleAPIError(error);
		}
	}

	/**
	 * Test Anthropic connection
	 */
	async testConnection(): Promise<boolean>
	{
		try
		{
			const result = await generateText({
				model: this.client(this.config.model),
				prompt: 'Test connection. Respond with "OK".',
				maxTokens: 10,
				timeout: 10000,
			});

			const success = result.text.toLowerCase().includes('ok');

			this.logger.info('Anthropic connection test completed', {
				providerId: this.providerId,
				success,
				response: result.text,
			});

			return success;
		}
		catch (error)
		{
			this.logger.error('Anthropic connection test failed', {
				providerId: this.providerId,
				error: error.message,
			});
			return false;
		}
	}

	/**
	 * Get supported models for this provider
	 */
	getSupportedModels(): string[]
	{
		return [
			'claude-3-5-sonnet-20241022',
			'claude-3-5-sonnet-20240620',
			'claude-3-5-haiku-20241022',
			'claude-3-opus-20240229',
			'claude-3-sonnet-20240229',
			'claude-3-haiku-20240307',
		];
	}

	/**
	 * Validate model is supported
	 */
	protected validateConfig(): void
	{
		super.validateConfig();

		const supportedModels = this.getSupportedModels();
		if (!supportedModels.includes(this.config.model))
		{
			this.logger.warn('Unsupported Anthropic model specified', {
				providerId: this.providerId,
				model: this.config.model,
				supportedModels,
			});
		}
	}

	/**
	 * Shutdown Anthropic provider
	 */
	async shutdown(): Promise<void>
	{
		await super.shutdown();
		this.client = null;
	}
}

export default AnthropicProvider;
