/**
 * AI Provider Manager - Manages multiple AI providers with fallback support
 * Extracted and consolidated from domain-seeder service
 */

import type { Logger } from '@shared';
import { logger as sharedLogger } from '@shared';
import type {
	AIProviderConfigType,
	AIProvidersConfigType,
	AIServiceErrorType,
	AIFallbackConfigType,
	AIServiceUsageType,
	AIServiceMetricsType,
} from '../types';
import { OpenAIProvider } from './OpenAIProvider';
import { AnthropicProvider } from './AnthropicProvider';
import { GoogleProvider } from './GoogleProvider';
import { OpenRouterProvider } from './OpenRouterProvider';
import type { BaseAIProvider } from './BaseAIProvider';

const logger = sharedLogger.getLogger('AIProviderManager');

/**
 * Manages multiple AI providers with automatic fallback and load balancing
 */
class AIProviderManager
{
	private readonly logger: Logger;

	private readonly providers = new Map<string, BaseAIProvider>();

	private readonly config: AIProvidersConfigType;

	private readonly fallbackConfig: AIFallbackConfigType;

	private readonly usage = new Map<string, AIServiceUsageType>();

	private readonly errors: AIServiceErrorType[] = [];

	private readonly maxErrorHistory = 1000;

	private readonly dailyUsage: Map<string, { requests: number; tokens: number; costs: number }> = new Map();

	constructor(
		config: AIProvidersConfigType,
		fallbackConfig: Partial<AIFallbackConfigType> = {},
	)
	{
		this.logger = logger;
		this.config = config;
		this.fallbackConfig = {
			enabled: fallbackConfig.enabled ?? true,
			maxRetries: fallbackConfig.maxRetries ?? 3,
			retryDelay: fallbackConfig.retryDelay ?? 1000,
			fallbackProviders: fallbackConfig.fallbackProviders ?? [],
			errorThreshold: fallbackConfig.errorThreshold ?? 0.1,
			...fallbackConfig,
		};

		this.initializeProviders();
	}

	/**
	 * Initialize all configured providers
	 */
	private initializeProviders(): void
	{
		for (const [providerId, providerConfig] of Object.entries(this.config))
		{
			if (!providerConfig.enabled)
			{
				this.logger.info('Skipping disabled AI provider', { providerId });
				continue;
			}

			try
			{
				const provider = this.createProvider(providerId, providerConfig);
				this.providers.set(providerId, provider);

				// Initialize usage tracking
				this.usage.set(providerId, {
					providerId,
					model: providerConfig.model,
					requestCount: 0,
					tokenUsage: { prompt: 0, completion: 0, total: 0 },
					costs: { prompt: 0, completion: 0, total: 0 },
					averageLatency: 0,
					errorCount: 0,
					lastUsed: new Date().toISOString(),
				});

				this.logger.info('AI provider initialized', {
					providerId,
					type: providerConfig.type,
					model: providerConfig.model,
				});
			}
			catch (error: unknown)
			{
				const errorMessage = error instanceof Error ? error.message : String(error);
				this.logger.error('Failed to initialize AI provider', {
					providerId,
					error: errorMessage,
				});
			}
		}

		this.logger.info('AI Provider Manager initialized', {
			totalProviders: this.providers.size,
			enabledProviders: Array.from(this.providers.keys()),
			fallbackEnabled: this.fallbackConfig.enabled,
		});
	}

	/**
	 * Create provider instance based on type
	 */
	private createProvider(providerId: string, config: AIProviderConfigType): BaseAIProvider
	{
		switch (config.type)
		{
			case 'openai':
				return new OpenAIProvider(providerId, config);
			case 'anthropic':
				return new AnthropicProvider(providerId, config);
			case 'google':
				return new GoogleProvider(providerId, config);
			case 'openrouter':
				return new OpenRouterProvider(providerId, config);
			default:
				throw new Error(`Unsupported AI provider type: ${config.type}`);
		}
	}

	/**
	 * Generate content using the best available provider
	 */
	async generateContent(
		systemPrompt: string,
		userPrompt: string,
		preferredProvider?: string,
	): Promise<any>
	{
		const startTime = Date.now();
		let lastError: Error | null = null;

		// Determine provider order
		const providerOrder = this.getProviderOrder(preferredProvider);

		for (const providerId of providerOrder)
		{
			const provider = this.providers.get(providerId);
			if (!provider)
			{
				continue;
			}

			try
			{
				this.logger.debug('Attempting content generation', {
					providerId,
					model: provider.getModel(),
				});

				const result = await provider.generateContent(systemPrompt, userPrompt);
				const processingTime = Date.now() - startTime;

				// Update usage metrics
				this.updateUsageMetrics(providerId, processingTime, result.tokenUsage);

				this.logger.info('Content generation successful', {
					providerId,
					model: provider.getModel(),
					processingTime,
					tokenUsage: result.tokenUsage,
				});

				return result;
			}
			catch (error: unknown)
			{
				lastError = (error instanceof Error) ? error : new Error(String(error));
				const processingTime = Date.now() - startTime;

				// Record error
				this.recordError(providerId, lastError);

				// Update error metrics
				this.updateErrorMetrics(providerId);

				this.logger.warn('Content generation failed, trying next provider', {
					providerId,
					error: lastError.message,
					processingTime,
				});

				// Add delay before trying next provider
				if (this.fallbackConfig.retryDelay > 0)
				{
					await this.delay(this.fallbackConfig.retryDelay);
				}
			}
		}

		// All providers failed
		const totalTime = Date.now() - startTime;
		this.logger.error('All AI providers failed for content generation', {
			attemptedProviders: providerOrder,
			totalTime,
			lastError: lastError?.message,
		});

		throw new Error(`Content generation failed: ${lastError?.message || 'All providers unavailable'}`);
	}

	/**
	 * Analyze content using the best available provider
	 */
	async analyzeContent(
		systemPrompt: string,
		userPrompt: string,
		preferredProvider?: string,
	): Promise<any>
	{
		const startTime = Date.now();
		let lastError: Error | null = null;

		const providerOrder = this.getProviderOrder(preferredProvider);

		for (const providerId of providerOrder)
		{
			const provider = this.providers.get(providerId);
			if (!provider)
			{
				continue;
			}

			try
			{
				this.logger.debug('Attempting content analysis', {
					providerId,
					model: provider.getModel(),
				});

				const result = await provider.analyzeContent(systemPrompt, userPrompt);
				const processingTime = Date.now() - startTime;

				this.updateUsageMetrics(providerId, processingTime, result.tokenUsage);

				this.logger.info('Content analysis successful', {
					providerId,
					model: provider.getModel(),
					processingTime,
					tokenUsage: result.tokenUsage,
				});

				return result;
			}
			catch (error: unknown)
			{
				lastError = (error instanceof Error) ? error : new Error(String(error));
				const processingTime = Date.now() - startTime;

				this.recordError(providerId, lastError);
				this.updateErrorMetrics(providerId);

				this.logger.warn('Content analysis failed, trying next provider', {
					providerId,
					error: lastError.message,
					processingTime,
				});

				if (this.fallbackConfig.retryDelay > 0)
				{
					await this.delay(this.fallbackConfig.retryDelay);
				}
			}
		}

		const totalTime = Date.now() - startTime;
		this.logger.error('All AI providers failed for content analysis', {
			attemptedProviders: providerOrder,
			totalTime,
			lastError: lastError?.message,
		});

		throw new Error(`Content analysis failed: ${lastError?.message || 'All providers unavailable'}`);
	}

	/**
	 * Get provider order for requests (preferred first, then by reliability)
	 */
	private getProviderOrder(preferredProvider?: string): string[]
	{
		const availableProviders = Array.from(this.providers.keys());

		if (!preferredProvider || !this.providers.has(preferredProvider))
		{
			// Sort by reliability (lowest error rate first)
			return availableProviders.sort((a, b) =>
			{
				const usageA = this.usage.get(a);
				const usageB = this.usage.get(b);

				if (!usageA || !usageB)
				{
					return 0;
				}

				const errorRateA = usageA.requestCount > 0 ? usageA.errorCount / usageA.requestCount : 0;
				const errorRateB = usageB.requestCount > 0 ? usageB.errorCount / usageB.requestCount : 0;

				return errorRateA - errorRateB;
			});
		}

		// Put preferred provider first, then others by reliability
		const otherProviders = availableProviders
			.filter(id => id !== preferredProvider)
			.sort((a, b) =>
			{
				const usageA = this.usage.get(a);
				const usageB = this.usage.get(b);

				if (!usageA || !usageB)
				{
					return 0;
				}

				const errorRateA = usageA.requestCount > 0 ? usageA.errorCount / usageA.requestCount : 0;
				const errorRateB = usageB.requestCount > 0 ? usageB.errorCount / usageB.requestCount : 0;

				return errorRateA - errorRateB;
			});

		return [preferredProvider as string, ...otherProviders];
	}

	/**
	 * Update usage metrics for a provider
	 */
	private updateUsageMetrics(
		providerId: string,
		processingTime: number,
		tokenUsage?: { prompt: number; completion: number; total: number },
	): void
	{
		const usage = this.usage.get(providerId);
		if (!usage)
		{
			return;
		}

		usage.requestCount++;
		usage.lastUsed = new Date().toISOString();

		// Update average latency
		usage.averageLatency = (usage.averageLatency * (usage.requestCount - 1) + processingTime) / usage.requestCount;

		// Update token usage
		if (tokenUsage)
		{
			usage.tokenUsage.prompt += tokenUsage.prompt;
			usage.tokenUsage.completion += tokenUsage.completion;
			usage.tokenUsage.total += tokenUsage.total;
		}

		// Update daily usage tracking
		const estimatedCost = this.calculateEstimatedCost(providerId, tokenUsage);
		this.updateDailyUsage(tokenUsage?.total || 0, estimatedCost);

		this.usage.set(providerId, usage);
	}

	/**
	 * Update daily usage tracking
	 */
	private updateDailyUsage(tokens: number, costs: number): void
	{
		const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format

		const currentUsage = this.dailyUsage.get(today) || {
			requests: 0,
			tokens: 0,
			costs: 0,
		};

		currentUsage.requests++;
		currentUsage.tokens += tokens;
		currentUsage.costs += costs;

		this.dailyUsage.set(today, currentUsage);

		// Clean up old entries (keep only last 30 days)
		const thirtyDaysAgo = new Date();
		thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
		const cutoffDate = thirtyDaysAgo.toISOString().split('T')[0];

		for (const [date] of this.dailyUsage)
		{
			if (date < cutoffDate)
			{
				this.dailyUsage.delete(date);
			}
		}
	}

	/**
	 * Update error metrics for a provider
	 */
	private updateErrorMetrics(providerId: string): void
	{
		const usage = this.usage.get(providerId);
		if (usage)
		{
			usage.errorCount++;
			this.usage.set(providerId, usage);
		}
	}

	/**
	 * Record an error for monitoring and analysis
	 */
	private recordError(providerId: string, error: Error): void
	{
		const aiError: AIServiceErrorType =
		{
			code: 'PROVIDER_ERROR',
			message: error.message,
			provider: providerId,
			retryable: this.isRetryableError(error),
			timestamp: new Date().toISOString(),
		};

		this.errors.push(aiError);

		// Limit error history
		if (this.errors.length > this.maxErrorHistory)
		{
			this.errors.splice(0, this.errors.length - this.maxErrorHistory);
		}
	}

	/**
	 * Determine if an error is retryable
	 */
	private isRetryableError(error: Error): boolean
	{
		const retryablePatterns = [
			/timeout/i,
			/rate limit/i,
			/service unavailable/i,
			/internal server error/i,
			/network/i,
			/connection/i,
		];

		return retryablePatterns.some(pattern => pattern.test(error.message));
	}

	/**
	 * Get comprehensive metrics for all providers
	 */
	getMetrics(): AIServiceMetricsType
	{
		const providerUsage = Array.from(this.usage.values());
		const totalRequests = providerUsage.reduce((sum, usage) => sum + usage.requestCount, 0);
		const totalTokens = providerUsage.reduce((sum, usage) => sum + usage.tokenUsage.total, 0);
		const totalCosts = providerUsage.reduce((sum, usage) => sum + usage.costs.total, 0);
		const totalErrors = providerUsage.reduce((sum, usage) => sum + usage.errorCount, 0);

		const averageLatency = totalRequests > 0
			? providerUsage.reduce((sum, usage) => sum + (usage.averageLatency * usage.requestCount), 0) / totalRequests
			: 0;

		const errorRate = totalRequests > 0 ? totalErrors / totalRequests : 0;

		// Convert daily usage map to array format expected by the type
		const dailyUsageArray = Array.from(this.dailyUsage.entries())
			.map(([date, usage]) => ({
				date,
				requests: usage.requests,
				tokens: usage.tokens,
				costs: usage.costs,
			}))
			.sort((a, b) => a.date.localeCompare(b.date)); // Sort by date

		return {
			totalRequests,
			totalTokens,
			totalCosts,
			averageLatency,
			errorRate,
			providerUsage,
			dailyUsage: dailyUsageArray,
		};
	}

	/**
	 * Get recent errors for debugging
	 */
	getRecentErrors(limit: number = 50): AIServiceErrorType[]
	{
		return this.errors.slice(-limit);
	}

	/**
	 * Check if any providers are available
	 */
	hasAvailableProviders(): boolean
	{
		return this.providers.size > 0;
	}

	/**
	 * Get list of available provider IDs
	 */
	getAvailableProviders(): string[]
	{
		return Array.from(this.providers.keys());
	}

	/**
	 * Get provider configuration
	 */
	getProviderConfig(providerId: string): AIProviderConfigType | null
	{
		return this.config[providerId] || null;
	}

	/**
	 * Utility method to add delay
	 */
	private delay(ms: number): Promise<void>
	{
		return new Promise(resolve => setTimeout(resolve, ms));
	}

	/**
	 * Shutdown all providers
	 */
	async shutdown(): Promise<void>
	{
		this.logger.info('Shutting down AI Provider Manager');

		for (const [providerId, provider] of this.providers)
		{
			try
			{
				await provider.shutdown();
				this.logger.debug('AI provider shut down', { providerId });
			}
			catch (error: unknown)
			{
				const errorMessage = error instanceof Error ? error.message : String(error);
				this.logger.error('Error shutting down AI provider', {
					providerId,
					error: errorMessage,
				});
			}
		}

		this.providers.clear();
		this.usage.clear();
		this.errors.length = 0;

		this.logger.info('AI Provider Manager shutdown complete');
	}

	/**
	 * Calculate estimated cost based on token usage and provider pricing
	 */
	private calculateEstimatedCost(
		providerId: string,
		tokenUsage?: { prompt: number; completion: number; total: number }
	): number
	{
		if (!tokenUsage)
		{
			return 0;
		}

		// Approximate pricing per 1K tokens (as of 2024)
		const pricingTable: Record<string, { prompt: number; completion: number }> = {
			'openai': { prompt: 0.03, completion: 0.06 }, // GPT-4 pricing
			'anthropic': { prompt: 0.015, completion: 0.075 }, // Claude-3 pricing
			'google': { prompt: 0.0015, completion: 0.002 }, // Gemini Pro pricing
			'openrouter': { prompt: 0.02, completion: 0.04 }, // Average pricing
		};

		const pricing = pricingTable[providerId] || pricingTable['openrouter'];

		const promptCost = (tokenUsage.prompt / 1000) * pricing.prompt;
		const completionCost = (tokenUsage.completion / 1000) * pricing.completion;

		return promptCost + completionCost;
	}
}

export { AIProviderManager };
