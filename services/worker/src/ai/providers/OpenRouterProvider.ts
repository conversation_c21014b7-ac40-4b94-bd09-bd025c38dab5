/**
 * OpenRouter Provider - AI content generation using OpenRouter API
 * Extracted and consolidated from domain-seeder service
 */

import { Curl } from 'node-libcurl';
import type { AIProviderConfigType } from '../types';
import type { AIResponseType } from './BaseAIProvider';
import BaseAIProvider from './BaseAIProvider';

// Define Proxy configuration type at module scope (class scope types are not allowed)
type ProxyConfigType =
{
	protocol: string;
	host: string;
	port: number;
	auth?: { username: string; password: string };
};

// Minimal OpenRouter response types used by this provider
// These cover only the fields we actually access to avoid `any`
type OpenRouterMessageType =
{
	role?: string;
	content?: string;
};

type OpenRouterChoiceType =
{
	message?: OpenRouterMessageType;
};

type OpenRouterUsageType =
{
	prompt_tokens?: number;
	completion_tokens?: number;
	total_tokens?: number;
};

type OpenRouterResponseType =
{
	choices?: OpenRouterChoiceType[];
	usage?: OpenRouterUsageType;
};

/**
 * OpenRouter provider for content generation and analysis
 * Supports multiple models through OpenRouter's unified API
 */
class OpenRouterProvider extends BaseAIProvider
{
	private client: Curl | null = null;

	private readonly baseUrl = 'https://openrouter.ai/api/v1';

	constructor(providerId: string, config: AIProviderConfigType)
	{
		super(providerId, config);
		this.initializeClient();
	}

	/**
	 * Initialize OpenRouter client
	 */
	private initializeClient(): void
	{
		try
		{
			this.client = new Curl();
			this.client.setOpt('URL', this.config.baseUrl || this.baseUrl);
			this.client.setOpt('FOLLOWLOCATION', true);
			this.client.setOpt('TIMEOUT_MS', this.config.timeout || 60000);
			this.client.setOpt('ACCEPT_ENCODING', ''); // Disable compression

			// Set up proxy if configured
			if (this.config.proxy)
			{
				const proxyConfig = this.parseProxy(this.config.proxy);
				if (proxyConfig)
				{
					this.client.setOpt('PROXY', `${proxyConfig.host}:${proxyConfig.port}`);
					if (proxyConfig.auth)
					{
						this.client.setOpt('PROXYUSERPWD', `${proxyConfig.auth.username}:${proxyConfig.auth.password}`);
					}
				}
			}

			this.logger.info('OpenRouter provider initialized', {
				providerId: this.providerId,
				model: this.config.model,
				baseUrl: this.config.baseUrl || this.baseUrl,
				proxy: !!this.config.proxy,
			});
		}
		catch (error: unknown)
		{
			this.logger.error('Failed to initialize OpenRouter provider', {
				providerId: this.providerId,
				error: error instanceof Error ? error.message : String(error),
			});
			throw error;
		}
	}

	/**
	 * Parse proxy configuration
	 */
	private parseProxy(proxyUrl: string): ProxyConfigType | undefined
	{
		try
		{
			const url = new URL(proxyUrl);
			return {
				protocol: url.protocol.replace(':', ''),
				host: url.hostname,
				port: parseInt(url.port, 10) || (url.protocol === 'https:' ? 443 : 80),
				auth: url.username && url.password ? {
					username: url.username,
					password: url.password,
				} : undefined,
			};
		}
		catch (error: unknown)
		{
			this.logger.error('Invalid proxy URL', {
				providerId: this.providerId,
				proxyUrl,
				error: error instanceof Error ? error.message : String(error),
			});
			return undefined;
		}
	}

	/**
	 * Safely extract HTTP status from unknown error objects
	 */
	private getErrorStatus(error: unknown): number | undefined
	{
		if (typeof error === 'object' && error !== null)
		{
			const maybe = error as { response?: { status?: number } };
			return typeof maybe.response?.status === 'number' ? maybe.response?.status : undefined;
		}
		return undefined;
	}

	/**
	 * Generate content using OpenRouter
	 */
	override async generateContent(systemPrompt: string, userPrompt: string): Promise<AIResponseType>
	{
		const startTime = Date.now();

		try
		{
			this.logger.debug('Generating content with OpenRouter', {
				providerId: this.providerId,
				model: this.config.model,
				systemPromptLength: systemPrompt.length,
				userPromptLength: userPrompt.length,
			});

			const requestBody =
			{
				model: this.config.model,
				messages: [
					{ role: 'system', content: systemPrompt },
					{ role: 'user', content: userPrompt },
				],
				response_format: { type: 'json_object' },
				temperature: 0.7,
				max_tokens: 2000,
			};

			const fullUrl = `${this.config.baseUrl || this.baseUrl}/chat/completions`;

			const response = await new Promise<
				{ status: number; data: OpenRouterResponseType }
			>((resolve, reject) =>
			{
				const curl = new Curl();

				curl.setOpt('URL', fullUrl);
				curl.setOpt('POSTFIELDS', JSON.stringify(requestBody));
				curl.setOpt('HTTPHEADER', [
					`Authorization: Bearer ${this.config.apiKey}`,
					'Content-Type: application/json',
					'HTTP-Referer: https://domainr.ai',
					'X-Title: Domainr AI Content Generator',
				]);
				curl.setOpt('TIMEOUT_MS', this.config.timeout || 60000);

				curl.on('end', (statusCode, data) =>
				{
					try
					{
						curl.close();
						resolve({
							status: statusCode,
							data: JSON.parse(data.toString()) as OpenRouterResponseType,
						});
					}
					catch (error: unknown)
					{
						curl.close();
						reject(error);
					}
				});

				curl.on('error', (error) =>
				{
					curl.close();
					reject(error);
				});

				curl.perform();
			});

			const processingTime = Date.now() - startTime;

			if (response.status < 200 || response.status >= 300)
			{
				throw new Error(`OpenRouter API error: ${response.status}`);
			}

			const result = response.data;
			const messageContent = result.choices?.[0]?.message?.content;

			if (!messageContent)
			{
				throw new Error('No content in OpenRouter response');
			}

			// Parse JSON response
			const content = this.parseJSONResponse(messageContent);

			// Extract token usage
			const tokenUsage = result.usage ? {
				prompt: result.usage.prompt_tokens,
				completion: result.usage.completion_tokens,
				total: result.usage.total_tokens,
			} : undefined;

			this.logger.info('OpenRouter content generation successful', {
				providerId: this.providerId,
				model: this.config.model,
				processingTime,
				tokenUsage,
			});

			return this.createResponse(content, tokenUsage);
		}
		catch (error: unknown)
		{
			const processingTime = Date.now() - startTime;
			const errorMessage = error instanceof Error ? error.message : String(error);
			const errorStatus = this.getErrorStatus(error);

			this.logger.error('OpenRouter content generation failed', {
				providerId: this.providerId,
				model: this.config.model,
				processingTime,
				error: errorMessage,
				status: errorStatus,
			});

			await this.handleAPIError(error);
			return this.createResponse({}, undefined);
		}
	}

	/**
	 * Analyze content using OpenRouter
	 */
	override async analyzeContent(systemPrompt: string, userPrompt: string): Promise<AIResponseType>
	{
		const startTime = Date.now();

		try
		{
			this.logger.debug('Analyzing content with OpenRouter', {
				providerId: this.providerId,
				model: this.config.model,
				systemPromptLength: systemPrompt.length,
				userPromptLength: userPrompt.length,
			});

			const requestBody =
			{
				model: this.config.model,
				messages: [
					{ role: 'system', content: systemPrompt },
					{ role: 'user', content: userPrompt },
				],
				response_format: { type: 'json_object' },
				temperature: 0.3, // Lower temperature for analysis
				max_tokens: 3000,
			};

			const fullUrl = `${this.config.baseUrl || this.baseUrl}/chat/completions`;

			const response = await new Promise<
				{ status: number; data: OpenRouterResponseType }
			>((resolve, reject) =>
			{
				const curl = new Curl();

				curl.setOpt('URL', fullUrl);
				curl.setOpt('POSTFIELDS', JSON.stringify(requestBody));
				curl.setOpt('HTTPHEADER', [
					`Authorization: Bearer ${this.config.apiKey}`,
					'Content-Type: application/json',
					'HTTP-Referer: https://domainr.ai',
					'X-Title: Domainr AI Content Analyzer',
				]);
				curl.setOpt('TIMEOUT_MS', this.config.timeout || 60000);

				curl.on('end', (statusCode, data) =>
				{
					try
					{
						curl.close();
						resolve({
							status: statusCode,
							data: JSON.parse(
								data.toString(),
							) as OpenRouterResponseType,
						});
					}
					catch (error: unknown)
					{
						curl.close();
						reject(error);
					}
				});

				curl.on('error', (error) =>
				{
					curl.close();
					reject(error);
				});

				curl.perform();
			});

			const processingTime = Date.now() - startTime;

			if (response.status < 200 || response.status >= 300)
			{
				await this.handleAPIError(new Error(`HTTP ${response.status}`));
				return this.createResponse({}, undefined);
			}

			const result = response.data;
			const messageContent = result.choices?.[0]?.message?.content;

			if (!messageContent)
			{
				throw new Error('No content in OpenRouter response');
			}

			// Parse JSON response
			const content = this.parseJSONResponse(messageContent);

			// Extract token usage
			const tokenUsage = result.usage ? {
				prompt: result.usage.prompt_tokens,
				completion: result.usage.completion_tokens,
				total: result.usage.total_tokens,
			} : undefined;

			this.logger.info('OpenRouter content analysis successful', {
				providerId: this.providerId,
				model: this.config.model,
				processingTime,
				tokenUsage,
			});

			return this.createResponse(content, tokenUsage);
		}
		catch (error: unknown)
		{
			const processingTime = Date.now() - startTime;
			const errorMessage = error instanceof Error ? error.message : String(error);
			const errorStatus = this.getErrorStatus(error);

			this.logger.error('OpenRouter content analysis failed', {
				providerId: this.providerId,
				model: this.config.model,
				processingTime,
				error: errorMessage,
				status: errorStatus,
			});

			await this.handleAPIError(error);
			return this.createResponse({}, undefined);
		}
	}

	/**
	 * Test OpenRouter connection
	 */
	override async testConnection(): Promise<boolean>
	{
		try
		{
			const requestBody =
			{
				model: this.config.model,
				messages: [
					{ role: 'user', content: 'Test connection. Respond with "OK".' },
				],
				max_tokens: 10,
			};

			const fullUrl = `${this.config.baseUrl || this.baseUrl}/chat/completions`;

			const response = await new Promise<
				{ status: number; data: OpenRouterResponseType }
			>((resolve, reject) =>
			{
				const curl = new Curl();

				curl.setOpt('URL', fullUrl);
				curl.setOpt('POSTFIELDS', JSON.stringify(requestBody));
				curl.setOpt('HTTPHEADER', [
					`Authorization: Bearer ${this.config.apiKey}`,
					'Content-Type: application/json',
					'HTTP-Referer: https://domainr.ai',
					'X-Title: Domainr AI Test Connection',
				]);
				curl.setOpt('TIMEOUT_MS', this.config.timeout || 30000);

				curl.on('end', (statusCode, data) =>
				{
					try
					{
						curl.close();
						resolve({
							status: statusCode,
							data: JSON.parse(data.toString()) as OpenRouterResponseType,
						});
					}
					catch (error: unknown)
					{
						curl.close();
						reject(error);
					}
				});

				curl.on('error', (error) =>
				{
					curl.close();
					reject(error);
				});

				curl.perform();
			});

			const messageContent = response.data.choices?.[0]?.message?.content;
			const success = !!(messageContent && messageContent.toLowerCase().includes('ok'));

			this.logger.info('OpenRouter connection test completed', {
				providerId: this.providerId,
				success,
				response: messageContent,
			});

			return success;
		}
		catch (error: unknown)
		{
			const errorMessage = error instanceof Error ? error.message : String(error);
			const errorStatus = this.getErrorStatus(error);

			this.logger.error('OpenRouter connection test failed', {
				providerId: this.providerId,
				error: errorMessage,
				status: errorStatus,
			});
			return false;
		}
	}

	/**
	 * Get supported models for this provider
	 */
	getSupportedModels(): string[]
	{
		return ([
			'anthropic/claude-3-opus',
			'anthropic/claude-3-sonnet',
			'anthropic/claude-3-haiku',
			'google/gemini-pro-1.5',
			'google/gemini-flash-1.5',
			'meta-llama/llama-3.1-405b-instruct',
			'meta-llama/llama-3.1-70b-instruct',
			'meta-llama/llama-3.1-8b-instruct',
		]);
	}

	/**
	 * Shutdown provider (cleanup resources)
	 */
	override async shutdown(): Promise<void>
	{
		await super.shutdown();
		this.client = null;
	}

	/**
	 * Validate model is supported
	 */
	protected override validateConfig(): void
	{
		super.validateConfig();

		const supportedModels = this.getSupportedModels();
		if (!supportedModels.includes(this.config.model))
		{
			this.logger.warn('Unsupported OpenRouter model specified', {
				providerId: this.providerId,
				model: this.config.model,
				supportedModels: supportedModels.slice(0, 10), // Log first 10 for brevity
			});
		}
	}

	/**
	 * Handle OpenRouter-specific errors
	 */
	protected override async handleAPIError(error: unknown, attempt: number = 1): Promise<never>
	{
		// Add OpenRouter-specific error handling
		if (error && typeof error === 'object' && 'response' in error)
		{
			const errorWithResponse = error as {
				response?: { status?: number; headers?: Record<string, string> };
			};

			if (errorWithResponse.response?.status === 402)
			{
				throw new Error(`OpenRouter: Insufficient credits for ${this.providerId}`);
			}

			if (errorWithResponse.response?.status === 429)
			{
				const retryAfter = errorWithResponse.response.headers?.['retry-after'];
				if (retryAfter)
				{
					const delay = parseInt(retryAfter, 10) * 1000;
					this.logger.info('OpenRouter rate limit hit, waiting', {
						providerId: this.providerId,
						attempt,
						delay,
					});
					await this.delay(delay);
					throw new Error('Retry after rate limit');
				}
			}
		}

		return super.handleAPIError(error, attempt);
	}
}

export default OpenRouterProvider;
