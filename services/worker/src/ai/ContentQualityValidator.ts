/**
 * Content Quality Validator - Validates AI-generated content quality
 * Extracted and consolidated from domain-seeder service
 */

import type { Logger } from '@shared';
import { logger as sharedLogger } from '@shared';
import type {
	GeneratedContent,
	ContentQualityMetrics,
	QualityValidationResult,
} from './types';

const logger = sharedLogger.getLogger('ContentQualityValidator');

type ValidationOptionsType =
{
	minWords?: number;
	maxWords?: number;
	requireKeywords?: boolean;
	requireAudience?: boolean;
	requireUseCases?: boolean;
	minKeywords?: number;
	maxKeywords?: number;
	minAudience?: number;
	maxAudience?: number;
	minUseCases?: number;
	maxUseCases?: number;
	minFeatures?: number;
	maxFeatures?: number;
};

/**
 * Validates the quality of AI-generated content
 */
class ContentQualityValidator
{
	private readonly logger: Logger;

	private readonly stopWords = new Set([
		'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
		'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did',
		'will', 'would', 'could', 'should', 'may', 'might', 'can', 'must', 'shall',
		'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they',
		'me', 'him', 'her', 'us', 'them', 'my', 'your', 'his', 'her', 'its', 'our', 'their',
	]);

	constructor()
	{
		this.logger = logger;
	}

	/**
	 * Validate content quality and return detailed metrics
	 */
	async validateContent(
		content: GeneratedContent,
		options: ValidationOptionsType = {},
	): Promise<QualityValidationResult>
	{
		const startTime = Date.now();

		try
		{
			this.logger.debug('Starting content quality validation', {
				summaryLength: content.summary.length,
				keyFeaturesCount: content.keyFeatures.length,
				audienceCount: content.audience.length,
				useCasesCount: content.useCases.length,
				keywordsCount: content.keywords.length,
			});

			// Calculate quality metrics
			const metrics = this.calculateQualityMetrics(content);

			// Perform validation checks
			const issues = this.performValidationChecks(content, metrics, options);

			// Generate recommendations
			const recommendations = this.generateRecommendations(content, metrics, issues);

			// Calculate overall score
			const overallScore = this.calculateOverallScore(metrics, issues);

			const processingTime = Date.now() - startTime;

			const result: QualityValidationResult = {
				passed: issues.filter(i => i.type === 'error').length === 0,
				score: overallScore,
				metrics,
				issues,
				recommendations,
			};

			this.logger.debug('Content quality validation completed', {
				score: overallScore,
				passed: result.passed,
				issueCount: issues.length,
				processingTime,
			});

			return result;
		}
		catch (error)
		{
			this.logger.error('Content quality validation failed', {
				error: error.message,
			});
			throw error;
		}
	}

	/**
	 * Calculate comprehensive quality metrics
	 */
	private calculateQualityMetrics(content: GeneratedContent): ContentQualityMetrics
	{
		const wordCount = this.countWords(content.summary);
		const readabilityScore = this.calculateReadabilityScore(content.summary);
		const keywordDensity = this.calculateKeywordDensity(content.summary, content.keywords);
		const sentimentScore = this.calculateSentimentScore(content.summary);
		const factualAccuracy = this.assessFactualAccuracy(content);
		const brandAlignment = this.assessBrandAlignment(content);
		const seoOptimization = this.assessSEOOptimization(content);

		// Calculate overall score as weighted average
		const overallScore = (
			readabilityScore * 0.2
			+ keywordDensity * 0.15
			+ sentimentScore * 0.1
			+ factualAccuracy * 0.25
			+ brandAlignment * 0.15
			+ seoOptimization * 0.15
		);

		return {
			wordCount,
			readabilityScore,
			keywordDensity,
			sentimentScore,
			factualAccuracy,
			brandAlignment,
			seoOptimization,
			overallScore,
		};
	}

	/**
	 * Count words in text (excluding stop words for keyword density)
	 */
	private countWords(text: string): number
	{
		return text.trim().split(/\s+/).filter(word => word.length > 0).length;
	}

	/**
	 * Calculate readability score (simplified Flesch Reading Ease)
	 */
	private calculateReadabilityScore(text: string): number
	{
		const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
		const words = text.split(/\s+/).filter(w => w.length > 0);
		const syllables = words.reduce((count, word) => count + this.countSyllables(word), 0);

		if (sentences.length === 0 || words.length === 0)
		{
			return 0;
		}

		const avgWordsPerSentence = words.length / sentences.length;
		const avgSyllablesPerWord = syllables / words.length;

		// Simplified Flesch Reading Ease formula
		const score = 206.835 - (1.015 * avgWordsPerSentence) - (84.6 * avgSyllablesPerWord);

		// Normalize to 0-1 scale
		return Math.max(0, Math.min(1, score / 100));
	}

	/**
	 * Count syllables in a word (approximation)
	 */
	private countSyllables(word: string): number
	{
		word = word.toLowerCase();
		if (word.length <= 3)
		{
			return 1;
		}

		const vowels = 'aeiouy';
		let syllableCount = 0;
		let previousWasVowel = false;

		for (let i = 0; i < word.length; i++)
		{
			const isVowel = vowels.includes(word[i]);
			if (isVowel && !previousWasVowel)
			{
				syllableCount++;
			}
			previousWasVowel = isVowel;
		}

		// Handle silent 'e'
		if (word.endsWith('e'))
		{
			syllableCount--;
		}

		return Math.max(1, syllableCount);
	}

	/**
	 * Calculate keyword density
	 */
	private calculateKeywordDensity(text: string, keywords: string[]): number
	{
		if (keywords.length === 0)
		{
			return 0;
		}

		const words = text.toLowerCase().split(/\s+/).filter(w => w.length > 0);
		const totalWords = words.length;

		if (totalWords === 0)
		{
			return 0;
		}

		let keywordMatches = 0;
		for (const keyword of keywords)
		{
			const keywordWords = keyword.toLowerCase().split(/\s+/);
			for (const keywordWord of keywordWords)
			{
				keywordMatches += words.filter(word => word.includes(keywordWord)).length;
			}
		}

		const density = keywordMatches / totalWords;

		// Optimal keyword density is 1-3%, normalize to 0-1 scale
		return Math.min(1, density / 0.03);
	}

	/**
	 * Calculate sentiment score (simplified)
	 */
	private calculateSentimentScore(text: string): number
	{
		const positiveWords = [
			'excellent', 'great', 'amazing', 'outstanding', 'superior', 'best', 'top',
			'leading', 'innovative', 'advanced', 'professional', 'quality', 'reliable',
			'trusted', 'secure', 'efficient', 'effective', 'powerful', 'comprehensive',
		];

		const negativeWords = [
			'poor', 'bad', 'terrible', 'awful', 'worst', 'inferior', 'outdated',
			'unreliable', 'insecure', 'inefficient', 'ineffective', 'weak', 'limited',
		];

		const words = text.toLowerCase().split(/\s+/);
		let positiveCount = 0;
		let negativeCount = 0;

		for (const word of words)
		{
			if (positiveWords.some(pw => word.includes(pw)))
			{
				positiveCount++;
			}
			if (negativeWords.some(nw => word.includes(nw)))
			{
				negativeCount++;
			}
		}

		const totalSentimentWords = positiveCount + negativeCount;
		if (totalSentimentWords === 0)
		{
			return 0.5; // Neutral
		}

		return positiveCount / totalSentimentWords;
	}

	/**
	 * Assess factual accuracy (basic checks)
	 */
	private assessFactualAccuracy(content: GeneratedContent): number
	{
		let score = 1.0;

		// Check for obvious factual issues
		const text = content.summary.toLowerCase();

		// Penalize vague or generic statements
		const vaguePatterns = [
			/many (people|users|customers)/,
			/various (features|services|options)/,
			/different (types|kinds|ways)/,
			/numerous (benefits|advantages)/,
		];

		for (const pattern of vaguePatterns)
		{
			if (pattern.test(text))
			{
				score -= 0.1;
			}
		}

		// Penalize superlatives without evidence
		const superlativePatterns = [
			/best in the world/,
			/number one/,
			/most popular/,
			/leading provider/,
		];

		for (const pattern of superlativePatterns)
		{
			if (pattern.test(text))
			{
				score -= 0.15;
			}
		}

		return Math.max(0, score);
	}

	/**
	 * Assess brand alignment
	 */
	private assessBrandAlignment(content: GeneratedContent): number
	{
		let score = 0.8; // Base score

		// Check if content includes relevant keywords
		if (content.keywords.length >= 3)
		{
			score += 0.1;
		}

		// Check if audience is well-defined
		if (content.audience.length >= 2)
		{
			score += 0.1;
		}

		// Check if use cases are provided
		if (content.useCases.length >= 2)
		{
			score += 0.1;
		}

		return Math.min(1, score);
	}

	/**
	 * Assess SEO optimization
	 */
	private assessSEOOptimization(content: GeneratedContent): number
	{
		let score = 0.5; // Base score

		const summary = content.summary;
		const wordCount = this.countWords(summary);

		// Optimal word count for SEO (50-160 words for meta descriptions)
		if (wordCount >= 50 && wordCount <= 160)
		{
			score += 0.2;
		}
		else if (wordCount >= 30 && wordCount <= 200)
		{
			score += 0.1;
		}

		// Check for keyword presence
		if (content.keywords.length >= 3)
		{
			score += 0.2;
		}

		// Check for action-oriented language
		const actionWords = ['discover', 'explore', 'learn', 'find', 'get', 'access', 'use', 'try'];
		const hasActionWords = actionWords.some(word =>
			summary.toLowerCase().includes(word));

		if (hasActionWords)
		{
			score += 0.1;
		}

		return Math.min(1, score);
	}

	/**
	 * Perform validation checks and identify issues
	 */
	private performValidationChecks(
		content: GeneratedContent,
		metrics: ContentQualityMetrics,
		options: ValidationOptionsType,
	): Array<{ type: 'warning' | 'error'; message: string; suggestion?: string }>
	{
		const issues: Array<{ type: 'warning' | 'error'; message: string; suggestion?: string }> = [];

		// Word count validation
		if (options.minWords && metrics.wordCount < options.minWords)
		{
			issues.push({
				type: 'error',
				message: `Summary is too short (${metrics.wordCount} words, minimum ${options.minWords})`,
				suggestion: 'Expand the summary with more detailed information',
			});
		}

		if (options.maxWords && metrics.wordCount > options.maxWords)
		{
			issues.push({
				type: 'error',
				message: `Summary is too long (${metrics.wordCount} words, maximum ${options.maxWords})`,
				suggestion: 'Condense the summary to focus on key points',
			});
		}

		// Required fields validation
		if (options.requireKeywords && content.keywords.length === 0)
		{
			issues.push({
				type: 'error',
				message: 'Keywords are required but not provided',
				suggestion: 'Add relevant keywords for SEO optimization',
			});
		}

		if (options.requireAudience && content.audience.length === 0)
		{
			issues.push({
				type: 'error',
				message: 'Target audience is required but not provided',
				suggestion: 'Define the target audience for better marketing alignment',
			});
		}

		if (options.requireUseCases && content.useCases.length === 0)
		{
			issues.push({
				type: 'error',
				message: 'Use cases are required but not provided',
				suggestion: 'Add practical use cases to demonstrate value',
			});
		}

		// Quality warnings
		if (metrics.readabilityScore < 0.3)
		{
			issues.push({
				type: 'warning',
				message: 'Content readability is low',
				suggestion: 'Use shorter sentences and simpler words',
			});
		}

		if (metrics.keywordDensity < 0.1)
		{
			issues.push({
				type: 'warning',
				message: 'Keyword density is very low',
				suggestion: 'Include more relevant keywords naturally in the content',
			});
		}

		if (metrics.keywordDensity > 0.8)
		{
			issues.push({
				type: 'warning',
				message: 'Keyword density is too high (keyword stuffing)',
				suggestion: 'Reduce keyword repetition for better readability',
			});
		}

		if (metrics.factualAccuracy < 0.7)
		{
			issues.push({
				type: 'warning',
				message: 'Content may contain vague or unsubstantiated claims',
				suggestion: 'Use more specific and factual language',
			});
		}

		return issues;
	}

	/**
	 * Generate recommendations for content improvement
	 */
	private generateRecommendations(
		content: GeneratedContent,
		metrics: ContentQualityMetrics,
		issues: Array<{ type: 'warning' | 'error'; message: string; suggestion?: string }>,
	): string[]
	{
		const recommendations: string[] = [];

		// Add suggestions from issues
		for (const issue of issues)
		{
			if (issue.suggestion)
			{
				recommendations.push(issue.suggestion);
			}
		}

		// Additional recommendations based on metrics
		if (metrics.seoOptimization < 0.7)
		{
			recommendations.push('Improve SEO optimization by including more relevant keywords and action-oriented language');
		}

		if (content.keyFeatures.length < 3)
		{
			recommendations.push('Add more key features to highlight the domain\'s value proposition');
		}

		if (content.audience.length < 2)
		{
			recommendations.push('Define multiple target audience segments for better market coverage');
		}

		if (metrics.sentimentScore < 0.6)
		{
			recommendations.push('Use more positive and engaging language to improve content appeal');
		}

		// Remove duplicates
		return Array.from(new Set(recommendations));
	}

	/**
	 * Calculate overall quality score
	 */
	private calculateOverallScore(
		metrics: ContentQualityMetrics,
		issues: Array<{ type: 'warning' | 'error'; message: string }>,
	): number
	{
		let score = metrics.overallScore;

		// Penalize for errors and warnings
		const errorCount = issues.filter(i => i.type === 'error').length;
		const warningCount = issues.filter(i => i.type === 'warning').length;

		score -= errorCount * 0.2; // 20% penalty per error
		score -= warningCount * 0.1; // 10% penalty per warning

		return Math.max(0, Math.min(1, score));
	}

	/**
	 * Validate content in batch
	 */
	async validateBatch(
		contents: GeneratedContent[],
		options: ValidationOptionsType = {},
	): Promise<Map<string, QualityValidationResult>>
	{
		const results = new Map<string, QualityValidationResult>();

		this.logger.info('Starting batch content validation', {
			contentCount: contents.length,
		});

		for (const content of contents)
		{
			try
			{
				const result = await this.validateContent(content, options);
				// Use summary as key (in real implementation, might use domain or ID)
				const key = content.summary.substring(0, 50);
				results.set(key, result);
			}
			catch (error)
			{
				this.logger.error('Batch validation failed for content', {
					error: error.message,
				});
			}
		}

		this.logger.info('Batch content validation completed', {
			contentCount: contents.length,
			validatedCount: results.size,
		});

		return results;
	}

	/**
	 * Get validation statistics
	 */
	getValidationStats(results: QualityValidationResult[]): {
		averageScore: number;
		passRate: number;
		commonIssues: Array<{ message: string; count: number }>;
		scoreDistribution: { excellent: number; good: number; fair: number; poor: number };
	}
	{
		if (results.length === 0)
		{
			return {
				averageScore: 0,
				passRate: 0,
				commonIssues: [],
				scoreDistribution: { excellent: 0, good: 0, fair: 0, poor: 0 },
			};
		}

		const averageScore = results.reduce((sum, r) => sum + r.score, 0) / results.length;
		const passRate = results.filter(r => r.passed).length / results.length;

		// Count common issues
		const issueCount = new Map<string, number>();
		for (const result of results)
		{
			for (const issue of result.issues)
			{
				const count = issueCount.get(issue.message) || 0;
				issueCount.set(issue.message, count + 1);
			}
		}

		const commonIssues = Array.from(issueCount.entries())
			.map(([message, count]) => ({ message, count }))
			.sort((a, b) => b.count - a.count)
			.slice(0, 10);

		// Score distribution
		const scoreDistribution = {
			excellent: results.filter(r => r.score >= 0.8).length,
			good: results.filter(r => r.score >= 0.6 && r.score < 0.8).length,
			fair: results.filter(r => r.score >= 0.4 && r.score < 0.6).length,
			poor: results.filter(r => r.score < 0.4).length,
		};

		return {
			averageScore,
			passRate,
			commonIssues,
			scoreDistribution,
		};
	}
}

export default ContentQualityValidator;
