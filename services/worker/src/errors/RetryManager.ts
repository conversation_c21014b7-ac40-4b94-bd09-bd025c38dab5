import type { Logger } from '@shared';
import { AsyncUtils } from '@shared';
import type { ErrorClassificationResultType } from './ErrorClassification';
import { IdGenerator } from 'shared/src/utils/IdGenerator';

/**
 * Retry strategy configuration
 */
type RetryStrategyConfigType =
{
	maxAttempts: number;
	baseDelay: number;
	maxDelay: number;
	backoffMultiplier: number;
	jitterEnabled: boolean;
	jitterRange: number;
	adaptiveEnabled: boolean;
	timeoutMs?: number;
};

/**
 * Retry attempt information
 */
type RetryAttemptType =
{
	attemptNumber: number;
	delay: number;
	timestamp: Date;
	error?: Error;
	success: boolean;
	duration: number;
};

/**
 * Retry context for tracking attempts
 */
type RetryContextType =
{
	operationId: string;
	operationName: string;
	attempts: RetryAttemptType[];
	startTime: Date;
	totalDuration: number;
	metadata: Record<string, any>;
};

/**
 * Retry decision result
 */
type RetryDecisionType =
{
	shouldRetry: boolean;
	delay: number;
	reason: string;
	nextAttemptNumber: number;
	adaptedStrategy?: Partial<RetryStrategyConfigType>;
};

/**
 * Retry statistics for analytics
 */
type RetryStatisticsType =
{
	operationName: string;
	totalAttempts: number;
	successfulAttempts: number;
	failedAttempts: number;
	averageDelay: number;
	averageDuration: number;
	successRate: number;
	lastUpdated: Date;
};

/**
 * Sophisticated retry manager with exponential backoff and adaptive strategies
 */
class RetryManager
{
	private readonly logger: Logger;
	private readonly defaultStrategy: RetryStrategyConfigType;
	private readonly operationStrategies: Map<string, RetryStrategyConfigType>;
	private readonly activeRetries: Map<string, RetryContextType>;
	private readonly retryStatistics: Map<string, RetryStatisticsType>;

	constructor(logger: Logger, defaultStrategy?: Partial<RetryStrategyConfigType>)
	{
		this.logger = logger;
		this.defaultStrategy = {
			maxAttempts: 3,
			baseDelay: 1000,
			maxDelay: 30000,
			backoffMultiplier: 2,
			jitterEnabled: true,
			jitterRange: 0.1,
			adaptiveEnabled: true,
			timeoutMs: 300000, // 5 minutes
			...defaultStrategy,
		};
		this.operationStrategies = new Map();
		this.activeRetries = new Map();
		this.retryStatistics = new Map();
	}

	/**
     * Execute operation with retry logic
     */
	async executeWithRetry<T>(
		operationName: string,
		operation: () => Promise<T>,
		errorClassification?: ErrorClassificationResultType,
		customStrategy?: Partial<RetryStrategyConfigType>
	): Promise<T>
	{
		const operationId = this.generateOperationId(operationName);
		const strategy = this.getStrategy(operationName, customStrategy);

		const context: RetryContextType = {
			operationId,
			operationName,
			attempts: [],
			startTime: new Date(),
			totalDuration: 0,
			metadata: {
				errorClassification,
				strategy,
			},
		};

		this.activeRetries.set(operationId, context);

		try
		{
			const result = await this.executeWithRetryInternal(
				operation,
				context,
				strategy,
				errorClassification,
			);

			this.updateStatistics(operationName, context, true);
			return result;
		}
		catch (error)
		{
			this.updateStatistics(operationName, context, false);
			throw error;
		}
		finally
		{
			this.activeRetries.delete(operationId);
		}
	}

	/**
     * Internal retry execution logic
     */
	private async executeWithRetryInternal<T>(
		operation: () => Promise<T>,
		context: RetryContextType,
		strategy: RetryStrategyConfigType,
		errorClassification?: ErrorClassificationResultType,
	): Promise<T>
	{
		let lastError: Error | null = null;

		for (let attempt = 1; attempt <= strategy.maxAttempts; attempt++)
		{
			const attemptStart = Date.now();

			try
			{
				// Check for timeout
				if (strategy.timeoutMs && Date.now() - context.startTime.getTime() > strategy.timeoutMs)
				{
					throw new Error(`Operation timeout after ${strategy.timeoutMs}ms`);
				}

				 
				const result = await operation();

				// Record successful attempt
				const attemptInfo: RetryAttemptType = {
					attemptNumber: attempt,
					delay: 0,
					timestamp: new Date(),
					success: true,
					duration: Date.now() - attemptStart,
				};
				context.attempts.push(attemptInfo);

				this.logger.info('Operation succeeded', {
					operationId: context.operationId,
					operationName: context.operationName,
					attempt,
					duration: attemptInfo.duration,
				});

				return result;
			}
			catch (error)
			{
				lastError = error as Error;

				// Record failed attempt
				const attemptInfo: RetryAttemptType = {
					attemptNumber: attempt,
					delay: 0,
					timestamp: new Date(),
					error: lastError,
					success: false,
					duration: Date.now() - attemptStart,
				};
				context.attempts.push(attemptInfo);

				// Decide whether to retry
				const decision = this.shouldRetry(attempt, lastError, strategy, errorClassification, context);

				if (!decision.shouldRetry)
				{
					this.logger.error('Operation failed, no more retries', {
						operationId: context.operationId,
						operationName: context.operationName,
						attempt,
						error: lastError.message,
						reason: decision.reason,
					});
					throw lastError;
				}

				// Calculate delay and wait
				const delay = decision.delay;
				attemptInfo.delay = delay;

				this.logger.warn('Operation failed, retrying', {
					operationId: context.operationId,
					operationName: context.operationName,
					attempt,
					nextAttempt: decision.nextAttemptNumber,
					delay,
					error: lastError.message,
					reason: decision.reason,
				});

				// Apply adapted strategy if provided
				if (decision.adaptedStrategy)
				{
					Object.assign(strategy, decision.adaptedStrategy);
				}

				 
				await this.sleep(delay);
			}
		}

		// All retries exhausted
		throw lastError || new Error('All retry attempts exhausted');
	}

	/**
     * Determine if operation should be retried
     */
	private shouldRetry(
		attempt: number,
		error: Error,
		strategy: RetryStrategyConfigType,
		errorClassification?: ErrorClassificationResultType,
		context?: RetryContextType
	): RetryDecisionType
	{
		// Check if we've reached max attempts
		if (attempt >= strategy.maxAttempts)
		{
			return {
				shouldRetry: false,
				delay: 0,
				reason: 'Maximum retry attempts reached',
				nextAttemptNumber: attempt + 1,
			};
		}

		// Check error classification
		if (errorClassification && !errorClassification.classification.retryable)
		{
			return {
				shouldRetry: false,
				delay: 0,
				reason: 'Error is not retryable based on classification',
				nextAttemptNumber: attempt + 1,
			};
		}

		// Calculate delay
		const delay = this.calculateDelay(attempt, strategy, context);

		// Check if delay exceeds maximum
		if (delay > strategy.maxDelay)
		{
			return {
				shouldRetry: false,
				delay: 0,
				reason: 'Calculated delay exceeds maximum allowed delay',
				nextAttemptNumber: attempt + 1,
			};
		}

		// Adaptive strategy adjustments
		let adaptedStrategy: Partial<RetryStrategyConfigType> | undefined;
		if (strategy.adaptiveEnabled && context)
		{
			adaptedStrategy = this.adaptStrategy(strategy, context, errorClassification);
		}

		return {
			shouldRetry: true,
			delay,
			reason: 'Error is retryable, continuing with backoff',
			nextAttemptNumber: attempt + 1,
			adaptedStrategy,
		};
	}

	/**
     * Calculate delay with exponential backoff and jitter
     */
	private calculateDelay(
		attempt: number,
		strategy: RetryStrategyConfigType,
		context?: RetryContextType,
	): number
	{
		// Base exponential backoff
		let delay = strategy.baseDelay * strategy.backoffMultiplier**(attempt - 1);

		// Apply context-based adjustments
		if (context)
		{
			// Increase delay for rate limit errors
			if (context.errorClassification?.category === 'rate_limiting')
			{
				delay *= 2; // Double the delay for rate limiting
			}
			
			// Reduce delay for transient errors that typically resolve quickly
			if (context.errorClassification?.isTransient && context.errorClassification.confidence > 0.8)
			{
				delay *= 0.75; // Reduce delay by 25% for highly confident transient errors
			}
			
			// Consider resource pressure
			if (context.resourcePressure)
			{
				if (context.resourcePressure.cpu > 80 || context.resourcePressure.memory > 80)
				{
					delay *= 1.5; // Increase delay under high resource pressure
				}
			}
		}

		// Apply jitter if enabled
		if (strategy.jitterEnabled)
		{
			const jitter = delay * strategy.jitterRange * (Math.random() * 2 - 1);
			delay += jitter;
		}

		// Ensure delay is within bounds
		delay = Math.max(0, Math.min(delay, strategy.maxDelay));

		return Math.round(delay);
	}

	/**
     * Adapt retry strategy based on historical performance
     */
	private adaptStrategy(
		currentStrategy: RetryStrategyConfigType,
		context: RetryContextType,
		errorClassification?: ErrorClassificationResultType,
	): Partial<RetryStrategyConfigType> | undefined
	{
		const stats = this.retryStatistics.get(context.operationName);
		if (!stats || stats.totalAttempts < 10)
		{
			return undefined; // Not enough data for adaptation
		}

		const adaptations: Partial<RetryStrategyConfigType> = {};

		// Adjust based on success rate
		if (stats.successRate < 0.5)
		{
			// Low success rate, increase delays and attempts
			adaptations.baseDelay = Math.min(currentStrategy.baseDelay * 1.5, currentStrategy.maxDelay / 4);
			adaptations.maxAttempts = Math.min(currentStrategy.maxAttempts + 1, 10);
		}
		else if (stats.successRate > 0.9)
		{
			// High success rate, reduce delays
			adaptations.baseDelay = Math.max(currentStrategy.baseDelay * 0.8, 500);
		}

		// Adjust based on error classification
		if (errorClassification)
		{
			const { classification } = errorClassification;

			if (classification.severity === 'critical')
			{
				adaptations.maxAttempts = Math.min(currentStrategy.maxAttempts + 2, 10);
				adaptations.baseDelay = Math.min(currentStrategy.baseDelay * 2, currentStrategy.maxDelay / 3);
			}

			if (classification.category === 'rate_limit')
			{
				adaptations.baseDelay = Math.min(currentStrategy.baseDelay * 3, currentStrategy.maxDelay);
				adaptations.backoffMultiplier = Math.min(currentStrategy.backoffMultiplier * 1.5, 5);
			}
		}

		return Object.keys(adaptations).length > 0 ? adaptations : undefined;
	}

	/**
     * Set custom retry strategy for specific operation
     */
	setOperationStrategy(operationName: string, strategy: Partial<RetryStrategyConfigType>): void
	{
		const fullStrategy = { ...this.defaultStrategy, ...strategy };
		this.operationStrategies.set(operationName, fullStrategy);
	}

	/**
     * Get retry strategy for operation
     */
	private getStrategy(
		operationName: string,
		customStrategy?: Partial<RetryStrategyConfigType>,
	): RetryStrategyConfigType
	{
		const baseStrategy = this.operationStrategies.get(operationName) || this.defaultStrategy;
		return ({ ...baseStrategy, ...customStrategy });
	}

	/**
     * Update retry statistics
     */
	private updateStatistics(operationName: string, context: RetryContextType, success: boolean): void
	{
		const existing = this.retryStatistics.get(operationName) || {
			operationName,
			totalAttempts: 0,
			successfulAttempts: 0,
			failedAttempts: 0,
			averageDelay: 0,
			averageDuration: 0,
			successRate: 0,
			lastUpdated: new Date(),
		};

		const totalAttempts = context.attempts.length;
		const totalDelay = context.attempts.reduce((sum, attempt) => sum + attempt.delay, 0);
		const totalDuration = context.attempts.reduce((sum, attempt) => sum + attempt.duration, 0);

		existing.totalAttempts += totalAttempts;
		if (success)
		{
			existing.successfulAttempts += 1;
		}
		else
		{
			existing.failedAttempts += 1;
		}

		// Update averages (simple moving average)
		const totalOperations = existing.successfulAttempts + existing.failedAttempts;

		existing.averageDelay = (existing.averageDelay * (totalOperations - 1) + totalDelay)
			/ totalOperations;

		existing.averageDuration = (existing.averageDuration * (totalOperations - 1) + totalDuration)
			/ totalOperations;

		existing.successRate = existing.successfulAttempts / totalOperations;
		existing.lastUpdated = new Date();

		this.retryStatistics.set(operationName, existing);
	}

	/**
     * Get retry statistics for operation
     */
	getStatistics(operationName?: string): RetryStatisticsType | RetryStatisticsType[]
	{
		if (operationName)
		{
			return this.retryStatistics.get(operationName) || {
				operationName,
				totalAttempts: 0,
				successfulAttempts: 0,
				failedAttempts: 0,
				averageDelay: 0,
				averageDuration: 0,
				successRate: 0,
				lastUpdated: new Date(),
			};
		}

		return Array.from(this.retryStatistics.values());
	}

	/**
     * Get active retry contexts
     */
	getActiveRetries(): RetryContextType[]
	{
		return Array.from(this.activeRetries.values());
	}

	/**
     * Generate unique operation ID
     */
	private generateOperationId(operationName: string): string
	{
		return IdGenerator.operationId(operationName);
	}

	/**
     * Sleep for specified milliseconds
     */
	private sleep(ms: number): Promise<void>
	{
		 
		return AsyncUtils.sleep(ms);
	}
}

export type {
	RetryStrategyConfigType,
	RetryAttemptType,
	RetryContextType,
	RetryDecisionType,
	RetryStatisticsType,
};

export default RetryManager;
