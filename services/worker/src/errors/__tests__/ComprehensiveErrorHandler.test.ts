import { describe, it, expect, beforeEach, vi } from 'vitest';
import type { Logger } from '@shared';
import { ComprehensiveErrorHandler } from '../ComprehensiveErrorHandler';
import CircuitBreakerState from '../CircuitBreaker';

describe('ComprehensiveErrorHandler', () =>
{
	let errorHandler: ComprehensiveErrorHandler;
	let mockLogger: Logger;

	beforeEach(() =>
	{
		mockLogger = {
			info: vi.fn(),
			warn: vi.fn(),
			error: vi.fn(),
			debug: vi.fn(),
		} as any;

		errorHandler = new ComprehensiveErrorHandler(mockLogger);
	});

	afterEach(() =>
	{
		errorHandler.shutdown();
	});

	describe('handleError', () =>
	{
		it('should handle error comprehensively', async () =>
		{
			const error = new Error('Test error');
			const context = {
				operationName: 'test-operation',
				metadata: { userId: '123' },
			};

			const result = await errorHandler.handleError(error, context);

			expect(result.handled).toBe(true);
			expect(result.classification).toBeDefined();
			expect(result.actions).toContain('Error classified');
			expect(result.actions).toContain('Error recorded for analytics');
			expect(result.actions).toContain('Error reported');
		});

		it('should handle classification failures gracefully', async () =>
		{
			// Create handler with faulty configuration
			const faultyHandler = new ComprehensiveErrorHandler(mockLogger, {
				classification: { enabled: false },
			});

			const error = new Error('Test error');
			const context = {
				operationName: 'test-operation',
				metadata: {},
			};

			const result = await faultyHandler.handleError(error, context);

			expect(result.handled).toBe(true);
			expect(result.classification).toBeDefined();

			faultyHandler.shutdown();
		});

		it('should trigger recovery when enabled', async () =>
		{
			const handler = new ComprehensiveErrorHandler(mockLogger, {
				recovery: {
					enabled: true,
					autoRecoveryEnabled: true,
				},
			});

			const error = new Error('Database connection lost');
			const context = {
				operationName: 'database-operation',
				metadata: {},
			};

			const result = await handler.handleError(error, context);

			expect(result.recoveryAttempted).toBe(true);
			expect(result.actions).toContain('Auto-recovery attempted');

			handler.shutdown();
		});

		it('should trigger degradation when conditions are met', async () =>
		{
			const error = new Error('High error rate detected');
			const context = {
				operationName: 'high-load-operation',
				metadata: { errorRate: 0.5 },
			};

			const result = await errorHandler.handleError(error, context);

			// Degradation might be triggered based on system state
			expect(result.degradationTriggered).toBeDefined();
		});
	});

	describe('executeWithErrorHandling', () =>
	{
		it('should execute operation successfully', async () =>
		{
			const operation = vi.fn().mockResolvedValue('success');

			const result = await errorHandler.executeWithErrorHandling(
				'test-operation',
				operation
			);

			expect(result).toBe('success');
			expect(operation).toHaveBeenCalledTimes(1);
		});

		it('should handle operation failures', async () =>
		{
			const operation = vi.fn().mockRejectedValue(new Error('Operation failed'));

			await expect(errorHandler.executeWithErrorHandling(
				'failing-operation',
				operation
			)).rejects.toThrow('Operation failed');

			expect(operation).toHaveBeenCalled();
		});

		it('should use circuit breaker when enabled', async () =>
		{
			const handler = new ComprehensiveErrorHandler(mockLogger, {
				circuitBreaker: { enabled: true },
				retry: { enabled: false },
			});

			const operation = vi.fn().mockRejectedValue(new Error('Service unavailable'));

			// Execute multiple failing operations to open circuit breaker
			for (let i = 0; i < 10; i++)
			{
				try
				{
					await handler.executeWithErrorHandling('circuit-test', operation);
				}
				catch (error)
				{
					// Expected failures
				}
			}

			// Circuit breaker should now be open
			await expect(handler.executeWithErrorHandling('circuit-test', operation))
				.rejects.toThrow();

			handler.shutdown();
		});

		it('should use retry when enabled', async () =>
		{
			const handler = new ComprehensiveErrorHandler(mockLogger, {
				retry: { enabled: true },
				circuitBreaker: { enabled: false },
			});

			const operation = vi.fn()
				.mockRejectedValueOnce(new Error('Temporary failure'))
				.mockResolvedValue('success');

			const result = await handler.executeWithErrorHandling('retry-test', operation);

			expect(result).toBe('success');
			expect(operation).toHaveBeenCalledTimes(2);

			handler.shutdown();
		});
	});

	describe('system health monitoring', () =>
	{
		it('should provide system health status', () =>
		{
			const health = errorHandler.getSystemHealth();

			expect(health.overall).toMatch(/^(healthy|degraded|unhealthy)$/);
			expect(health.components).toBeDefined();
			expect(health.metrics).toBeDefined();
			expect(health.lastUpdated).toBeInstanceOf(Date);
		});

		it('should track component health', () =>
		{
			const health = errorHandler.getSystemHealth();

			expect(health.components.errorClassification).toMatch(/^(healthy|degraded|unhealthy)$/);
			expect(health.components.retryManager).toMatch(/^(healthy|degraded|unhealthy)$/);
			expect(health.components.circuitBreakers).toMatch(/^(healthy|degraded|unhealthy)$/);
			expect(health.components.degradationManager).toMatch(/^(healthy|degraded|unhealthy)$/);
			expect(health.components.reportingSystem).toMatch(/^(healthy|degraded|unhealthy)$/);
			expect(health.components.recoverySystem).toMatch(/^(healthy|degraded|unhealthy)$/);
			expect(health.components.analyticsEngine).toMatch(/^(healthy|degraded|unhealthy)$/);
		});
	});

	describe('analytics and statistics', () =>
	{
		it('should provide error analytics', () =>
		{
			const analytics = errorHandler.getErrorAnalytics();

			expect(analytics.patterns).toBeDefined();
			expect(analytics.trends).toBeDefined();
			expect(analytics.anomalies).toBeDefined();
			expect(analytics.correlations).toBeDefined();
		});

		it('should provide retry statistics', () =>
		{
			const stats = errorHandler.getRetryStatistics();
			expect(Array.isArray(stats) || typeof stats === 'object').toBe(true);
		});

		it('should provide recovery statistics', () =>
		{
			const stats = errorHandler.getRecoveryStatistics();
			expect(Array.isArray(stats) || typeof stats === 'object').toBe(true);
		});
	});

	describe('alert management', () =>
	{
		it('should get active alerts', () =>
		{
			const alerts = errorHandler.getActiveAlerts();
			expect(Array.isArray(alerts)).toBe(true);
		});

		it('should acknowledge alerts', () =>
		{
			// This would require creating an alert first
			const result = errorHandler.acknowledgeAlert('non-existent-alert', 'user123');
			expect(typeof result).toBe('boolean');
		});

		it('should resolve alerts', () =>
		{
			// This would require creating an alert first
			const result = errorHandler.resolveAlert('non-existent-alert', 'user123');
			expect(typeof result).toBe('boolean');
		});
	});

	describe('manual controls', () =>
	{
		it('should force circuit breaker state', () =>
		{
			// First create a circuit breaker by executing an operation
			const operation = vi.fn().mockResolvedValue('success');
			errorHandler.executeWithErrorHandling('manual-test', operation);

			const result = errorHandler.forceCircuitBreakerState(
				'manual-test',
				CircuitBreakerState.OPEN,
				'Manual intervention'
			);

			expect(result).toBe(true);
		});

		it('should restore all capabilities', () =>
		{
			expect(() => errorHandler.restoreAllCapabilities()).not.toThrow();
		});
	});

	describe('configuration', () =>
	{
		it('should respect disabled features', () =>
		{
			const handler = new ComprehensiveErrorHandler(mockLogger, {
				classification: { enabled: false },
				retry: { enabled: false },
				circuitBreaker: { enabled: false },
				degradation: { enabled: false },
				reporting: { enabled: false },
				recovery: { enabled: false },
				analytics: { enabled: false },
			});

			expect(() => handler.getSystemHealth()).not.toThrow();

			handler.shutdown();
		});

		it('should use custom configuration', () =>
		{
			const customConfig = {
				retry: {
					enabled: true,
					defaultStrategy: {
						maxAttempts: 5,
						baseDelay: 2000,
					},
				},
			};

			const handler = new ComprehensiveErrorHandler(mockLogger, customConfig);

			expect(() => handler.getSystemHealth()).not.toThrow();

			handler.shutdown();
		});
	});

	describe('shutdown', () =>
	{
		it('should shutdown cleanly', () =>
		{
			expect(() => errorHandler.shutdown()).not.toThrow();
		});

		it('should stop monitoring after shutdown', () =>
		{
			errorHandler.shutdown();

			// Verify that monitoring has stopped
			const health = errorHandler.getSystemHealth();
			expect(health).toBeDefined();
		});
	});
});
