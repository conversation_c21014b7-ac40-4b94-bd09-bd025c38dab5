import { describe, it, expect, beforeEach, vi } from 'vitest';
import type { Logger } from '@shared';
import { ErrorClassificationService, ErrorSeverity, ErrorCategory } from '../ErrorClassification';

describe('ErrorClassificationService', () =>
{
	let service: ErrorClassificationService;
	let mockLogger: Logger;

	beforeEach(() =>
	{
		mockLogger = {
			info: vi.fn(),
			warn: vi.fn(),
			error: vi.fn(),
			debug: vi.fn(),
		} as any;

		service = new ErrorClassificationService(mockLogger);
	});

	describe('classifyError', () =>
	{
		it('should classify network errors correctly', () =>
		{
			const error = new Error('ECONNREFUSED: Connection refused');
			const result = service.classifyError(error);

			expect(result.classification.category).toBe(ErrorCategory.NETWORK);
			expect(result.classification.severity).toBe(ErrorSeverity.MEDIUM);
			expect(result.classification.retryable).toBe(true);
			expect(result.confidence).toBeGreaterThan(0.8);
		});

		it('should classify database errors correctly', () =>
		{
			const error = new Error('Database connection lost');
			const result = service.classifyError(error);

			expect(result.classification.category).toBe(ErrorCategory.DATABASE);
			expect(result.classification.severity).toBe(ErrorSeverity.HIGH);
			expect(result.classification.retryable).toBe(true);
			expect(result.classification.requiresImmediateAttention).toBe(true);
		});

		it('should classify timeout errors correctly', () =>
		{
			const error = new Error('Operation timed out after 30000ms');
			const result = service.classifyError(error);

			expect(result.classification.category).toBe(ErrorCategory.TIMEOUT);
			expect(result.classification.severity).toBe(ErrorSeverity.MEDIUM);
			expect(result.classification.retryable).toBe(true);
		});

		it('should classify resource errors correctly', () =>
		{
			const error = new Error('Out of memory: heap limit exceeded');
			const result = service.classifyError(error);

			expect(result.classification.category).toBe(ErrorCategory.RESOURCE);
			expect(result.classification.severity).toBe(ErrorSeverity.CRITICAL);
			expect(result.classification.escalationRequired).toBe(true);
		});

		it('should handle unknown errors with default classification', () =>
		{
			const error = new Error('Some unknown error');
			const result = service.classifyError(error);

			expect(result.classification.category).toBe(ErrorCategory.BUSINESS_LOGIC);
			expect(result.classification.severity).toBe(ErrorSeverity.MEDIUM);
			expect(result.confidence).toBeLessThan(0.7);
		});

		it('should include context in metadata', () =>
		{
			const error = new Error('Test error');
			const context = { userId: '123', operation: 'test' };
			const result = service.classifyError(error, context);

			expect(result.metadata.context).toEqual(context);
			expect(result.metadata.errorMessage).toBe('Test error');
		});

		it('should handle classification errors gracefully', () =>
		{
			// Create a service that will throw during classification
			const faultyService = new ErrorClassificationService(mockLogger);

			// Mock the pattern matching to throw
			vi.spyOn(faultyService as any, 'matchErrorPattern').mockImplementation(() =>
			{
				throw new Error('Classification failed');
			});

			const error = new Error('Test error');
			const result = faultyService.classifyError(error);

			expect(result.confidence).toBe(0.1);
			expect(result.metadata.classificationFailed).toBe(true);
			expect(mockLogger.error).toHaveBeenCalled();
		});
	});

	describe('pattern matching', () =>
	{
		it('should match regex patterns correctly', () =>
		{
			const error = new Error('ENOTFOUND: DNS lookup failed');
			const result = service.classifyError(error);

			expect(result.pattern?.name).toBe('DNS Resolution Failed');
			expect(result.confidence).toBeGreaterThan(0.8);
		});

		it('should match string patterns correctly', () =>
		{
			const error = new Error('Rate limit exceeded: too many requests');
			const result = service.classifyError(error);

			expect(result.pattern?.name).toBe('Rate Limit Exceeded');
			expect(result.classification.category).toBe(ErrorCategory.RATE_LIMIT);
		});

		it('should prioritize pattern matches over heuristics', () =>
		{
			const error = new Error('Connection refused by server');
			const result = service.classifyError(error);

			expect(result.pattern?.name).toBe('Connection Refused');
			expect(result.confidence).toBeGreaterThan(0.8);
		});
	});

	describe('heuristic classification', () =>
	{
		it('should classify based on error message keywords', () =>
		{
			const networkError = new Error('Network unreachable');
			const networkResult = service.classifyError(networkError);
			expect(networkResult.classification.category).toBe(ErrorCategory.NETWORK);

			const dbError = new Error('Query execution failed');
			const dbResult = service.classifyError(dbError);
			expect(dbResult.classification.category).toBe(ErrorCategory.DATABASE);
		});

		it('should classify based on error name', () =>
		{
			const error = new Error('Something went wrong');
			error.name = 'ValidationError';
			const result = service.classifyError(error);

			// Should fall back to default classification since ValidationError doesn't match any heuristics
			expect(result.classification.category).toBe(ErrorCategory.BUSINESS_LOGIC);
		});
	});
});
