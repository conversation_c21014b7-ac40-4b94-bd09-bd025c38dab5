import { describe, it, expect, beforeEach, vi } from 'vitest';
import type { Logger } from '@shared';
import { RetryManager } from '../RetryManager';

describe('RetryManager', () =>
{
	let retryManager: RetryManager;
	let mockLogger: Logger;

	beforeEach(() =>
	{
		mockLogger = {
			info: vi.fn(),
			warn: vi.fn(),
			error: vi.fn(),
			debug: vi.fn(),
		} as any;

		retryManager = new RetryManager(mockLogger);
	});

	describe('executeWithRetry', () =>
	{
		it('should succeed on first attempt', async () =>
		{
			const operation = vi.fn().mockResolvedValue('success');

			const result = await retryManager.executeWithRetry('test-operation', operation);

			expect(result).toBe('success');
			expect(operation).toHaveBeenCalledTimes(1);
		});

		it('should retry on failure and eventually succeed', async () =>
		{
			const operation = vi.fn()
				.mockRejectedValueOnce(new Error('First failure'))
				.mockRejectedValueOnce(new Error('Second failure'))
				.mockResolvedValue('success');

			const result = await retryManager.executeWithRetry('test-operation', operation);

			expect(result).toBe('success');
			expect(operation).toHaveBeenCalledTimes(3);
		});

		it('should fail after max attempts', async () =>
		{
			const operation = vi.fn().mockRejectedValue(new Error('Persistent failure'));

			await expect(retryManager.executeWithRetry('test-operation', operation))
				.rejects.toThrow('Persistent failure');

			expect(operation).toHaveBeenCalledTimes(3); // Default max attempts
		});

		it('should respect custom retry strategy', async () =>
		{
			const operation = vi.fn().mockRejectedValue(new Error('Failure'));
			const customStrategy = { maxAttempts: 5, baseDelay: 100 };

			await expect(retryManager.executeWithRetry('test-operation', operation, undefined, customStrategy))
				.rejects.toThrow('Failure');

			expect(operation).toHaveBeenCalledTimes(5);
		});

		it('should not retry non-retryable errors', async () =>
		{
			const operation = vi.fn().mockRejectedValue(new Error('Non-retryable'));
			const errorClassification = {
				classification: {
					retryable: false,
					category: 'validation' as any,
					severity: 'low' as any,
					persistence: 'permanent' as any,
					recoverability: 'fatal' as any,
					scope: 'service_specific' as any,
					requiresImmediateAttention: false,
					escalationRequired: false,
				},
				confidence: 0.9,
				metadata: {},
			};

			await expect(retryManager.executeWithRetry('test-operation', operation, errorClassification))
				.rejects.toThrow('Non-retryable');

			expect(operation).toHaveBeenCalledTimes(1);
		});

		it('should apply exponential backoff', async () =>
		{
			const operation = vi.fn().mockRejectedValue(new Error('Failure'));
			const startTime = Date.now();

			await expect(retryManager.executeWithRetry('test-operation', operation, undefined, {
				maxAttempts: 3,
				baseDelay: 100,
				backoffMultiplier: 2,
			})).rejects.toThrow();

			const endTime = Date.now();
			const totalTime = endTime - startTime;

			// Should have waited at least 100ms + 200ms = 300ms
			expect(totalTime).toBeGreaterThan(250);
		});

		it('should apply jitter when enabled', async () =>
		{
			const operation = vi.fn().mockRejectedValue(new Error('Failure'));

			// Run multiple times to test jitter variation
			const delays: number[] = [];

			for (let i = 0; i < 5; i++)
			{
				const startTime = Date.now();
				try
				{
					await retryManager.executeWithRetry('test-operation', operation, undefined, {
						maxAttempts: 2,
						baseDelay: 1000,
						jitterEnabled: true,
						jitterRange: 0.5,
					});
				}
				catch (error)
				{
					// Expected to fail
				}
				const endTime = Date.now();
				delays.push(endTime - startTime);
			}

			// Delays should vary due to jitter
			const uniqueDelays = new Set(delays.map(d => Math.floor(d / 100)));
			expect(uniqueDelays.size).toBeGreaterThan(1);
		});

		it('should respect timeout configuration', async () =>
		{
			const operation = vi.fn().mockRejectedValue(new Error('Test error'));

			// Test that timeout configuration is accepted
			await expect(retryManager.executeWithRetry('test-operation', operation, undefined, {
				maxAttempts: 1,
				timeoutMs: 500,
			})).rejects.toThrow('Test error');

			expect(operation).toHaveBeenCalledTimes(1);
		});
	});

	describe('adaptive retry strategy', () =>
	{
		it('should track statistics for adaptive strategy', async () =>
		{
			// Create some operations to build statistics
			const operation1 = vi.fn().mockResolvedValue('success');
			const operation2 = vi.fn().mockRejectedValue(new Error('Failure'));

			// Execute successful operation
			await retryManager.executeWithRetry('adaptive-test', operation1, undefined, {
				maxAttempts: 1,
				baseDelay: 1,
			});

			// Execute failing operation
			try
			{
				await retryManager.executeWithRetry('adaptive-test', operation2, undefined, {
					maxAttempts: 2,
					baseDelay: 1,
				});
			}
			catch (error)
			{
				// Expected failure
			}

			// Should have statistics
			const stats = retryManager.getStatistics('adaptive-test') as any;
			expect(stats.totalAttempts).toBeGreaterThan(0);
			expect(stats.successfulAttempts).toBe(1);
			expect(stats.failedAttempts).toBe(1);
		});
	});

	describe('statistics', () =>
	{
		it('should track operation statistics', async () =>
		{
			const successOperation = vi.fn().mockResolvedValue('success');
			const failOperation = vi.fn().mockRejectedValue(new Error('Failure'));

			// Execute some operations
			await retryManager.executeWithRetry('stats-test', successOperation);

			try
			{
				await retryManager.executeWithRetry('stats-test', failOperation);
			}
			catch (error)
			{
				// Expected failure
			}

			const stats = retryManager.getStatistics('stats-test') as any;

			expect(stats.operationName).toBe('stats-test');
			expect(stats.successfulAttempts).toBe(1);
			expect(stats.failedAttempts).toBe(1);
			expect(stats.successRate).toBe(0.5);
		});

		it('should return all statistics when no operation specified', () =>
		{
			const allStats = retryManager.getStatistics() as any[];
			expect(Array.isArray(allStats)).toBe(true);
		});
	});

	describe('active retries', () =>
	{
		it('should track active retry contexts', async () =>
		{
			const slowOperation = vi.fn().mockImplementation(() =>
				new Promise(resolve => setTimeout(() => resolve('success'), 100)));

			const promise = retryManager.executeWithRetry('slow-operation', slowOperation);

			// Check active retries while operation is running
			const activeRetries = retryManager.getActiveRetries();
			expect(activeRetries.length).toBe(1);
			expect(activeRetries[0].operationName).toBe('slow-operation');

			await promise;

			// Should be cleared after completion
			expect(retryManager.getActiveRetries().length).toBe(0);
		});
	});

	describe('operation-specific strategies', () =>
	{
		it('should use operation-specific retry strategy', async () =>
		{
			retryManager.setOperationStrategy('special-operation', {
				maxAttempts: 5,
				baseDelay: 50,
			});

			const operation = vi.fn().mockRejectedValue(new Error('Failure'));

			await expect(retryManager.executeWithRetry('special-operation', operation))
				.rejects.toThrow();

			expect(operation).toHaveBeenCalledTimes(5);
		});
	});
});
