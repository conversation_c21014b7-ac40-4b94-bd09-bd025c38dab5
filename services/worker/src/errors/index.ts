// Error Classification
export {
	ErrorClassificationService,
	ErrorSeverity,
	ErrorCategory,
	ErrorPersistence,
	ErrorRecoverability,
	ErrorScope,
} from './ErrorClassification';
export type {
	ErrorClassificationType,
	ErrorPatternType,
	ErrorClassificationResultType,
} from './ErrorClassification';

// Retry Management
export { RetryManager } from './RetryManager';
export type {
	RetryStrategyConfigType,
	RetryAttemptType,
	RetryContextType,
	RetryDecisionType,
	RetryStatisticsType,
} from './RetryManager';

// Circuit Breaker
export {
	CircuitBreaker,
	CircuitBreakerState,
} from './CircuitBreaker';
export type {
	CircuitBreakerConfigType,
	CircuitBreakerMetricsType,
	CircuitBreakerEventType,
	CallResultType,
} from './CircuitBreaker';

// Graceful Degradation
export {
	GracefulDegradationManager,
	DegradationLevel,
} from './GracefulDegradationManager';
export type {
	ServiceCapabilityType,
	DegradationRuleType,
	DegradationActionType,
	DegradationContextType,
	DegradationStatusType,
} from './GracefulDegradationManager';

// Error Reporting
export {
	ErrorReportingSystem,
	AlertSeverity,
	AlertChannel,
} from './ErrorReportingSystem';
export type {
	ErrorReportType,
	AlertConfigType,
	AlertConditionType,
	AlertThrottlingType,
	AlertEscalationType,
	AlertInstanceType,
	NotificationPayloadType,
	ErrorAnalyticsType,
} from './ErrorReportingSystem';

// Auto Recovery
export {
	AutoRecoverySystem,
	RecoveryStrategyType,
} from './AutoRecoverySystem';
export type {
	RecoveryActionConfigType,
	RecoveryConditionType,
	RecoverySuccessCriteriaType,
	RecoveryAttemptType,
	RecoveryContextType,
	RecoveryStatisticsType,
} from './AutoRecoverySystem';

// Error Analytics
export { ErrorAnalyticsEngine } from './ErrorAnalyticsEngine';
export type {
	ErrorPatternType as AnalyticsErrorPatternType,
	ErrorOccurrenceType,
	ErrorTrendType,
	ErrorCorrelationType,
	AnomalyDetectionResultType,
	PredictionResultType,
} from './ErrorAnalyticsEngine';

// Worker Error Handler (extends shared BaseErrorHandler)
export { WorkerErrorHandler } from './WorkerErrorHandler';
export type {
	WorkerErrorHandlingConfigType,
	SystemHealthStatusType,
} from './WorkerErrorHandler';

// Legacy Comprehensive Error Handler (deprecated - use WorkerErrorHandler instead)
export { ComprehensiveErrorHandler } from './ComprehensiveErrorHandler';
export type {
	ErrorHandlingConfigType,
	ErrorHandlingContextType,
	ErrorHandlingResultType,
} from './ComprehensiveErrorHandler';
