import {
	BaseE<PERSON>r<PERSON><PERSON><PERSON>,
	type ErrorHandlingConfigType,
	type ErrorHandlingContextType,
	type ErrorClassificationResultType
	, LoggerInstanceType } from '@shared';
import GracefulDegradationManager, { type DegradationContextType } from './GracefulDegradationManager';
import ErrorReportingSystem from './ErrorReportingSystem';
import AutoRecoverySystem, { type RecoveryContextType } from './AutoRecoverySystem';
import ErrorAnalyticsEngine from './ErrorAnalyticsEngine';

export interface WorkerErrorHandlingConfigType extends ErrorHandlingConfigType {
	degradation: {
		enabled: boolean;
		monitoringInterval: number;
	};
	reporting: {
		enabled: boolean;
		channels: string[];
	};
	recovery: {
		enabled: boolean;
		autoRecoveryEnabled: boolean;
	};
	analytics: {
		enabled: boolean;
		analysisInterval: number;
	};
}

export type SystemHealthStatusType = {
	overall: 'healthy' | 'degraded' | 'unhealthy';
	components: {
		errorClassification: 'healthy' | 'degraded' | 'unhealthy';
		retryManager: 'healthy' | 'degraded' | 'unhealthy';
		circuitBreakers: 'healthy' | 'degraded' | 'unhealthy';
		degradationManager: 'healthy' | 'degraded' | 'unhealthy';
		reportingSystem: 'healthy' | 'degraded' | 'unhealthy';
		recoverySystem: 'healthy' | 'degraded' | 'unhealthy';
		analyticsEngine: 'healthy' | 'degraded' | 'unhealthy';
	};
	metrics: {
		errorRate: number;
		retrySuccessRate: number;
		recoverySuccessRate: number;
		circuitBreakerOpenCount: number;
		degradationLevel: string;
	};
	lastUpdated: Date;
};

export class WorkerErrorHandler extends BaseErrorHandler
{
	protected readonly config: WorkerErrorHandlingConfigType;
	private degradationManager?: GracefulDegradationManager;
	private reportingSystem?: ErrorReportingSystem;
	private recoverySystem?: AutoRecoverySystem;
	private analyticsEngine?: ErrorAnalyticsEngine;
	private healthMonitoringInterval?: NodeJS.Timeout;

	constructor(logger: LoggerInstanceType, config?: Partial<WorkerErrorHandlingConfigType>)
	{
		const fullConfig: WorkerErrorHandlingConfigType = {
			classification: {
				enabled: true,
				confidenceThreshold: 0.7,
			},
			retry: {
				enabled: true,
				defaultStrategy: {
					maxAttempts: 3,
					baseDelay: 1000,
					maxDelay: 30000,
					backoffMultiplier: 2,
					jitterEnabled: true,
				},
			},
			circuitBreaker: {
				enabled: true,
				defaultConfig: {
					failureThreshold: 5,
					timeout: 60000,
					volumeThreshold: 10,
				},
			},
			degradation: {
				enabled: true,
				monitoringInterval: 30000,
			},
			reporting: {
				enabled: true,
				channels: ['log', 'email'],
			},
			recovery: {
				enabled: true,
				autoRecoveryEnabled: true,
			},
			analytics: {
				enabled: true,
				analysisInterval: 300000,
			},
			...config,
		};

		super(logger, fullConfig);
		this.config = fullConfig;

		this.degradationManager = this.degradationManager || new GracefulDegradationManager(logger);
		this.reportingSystem = this.reportingSystem || new ErrorReportingSystem(logger);
		this.recoverySystem = this.recoverySystem || new AutoRecoverySystem(logger);
		this.analyticsEngine = this.analyticsEngine || new ErrorAnalyticsEngine(logger);
	}

	protected initialize(): void
	{
		super.initialize();

		// Ensure subsystems are constructed in case super() triggered initialize before our constructor ran
		if (!this.degradationManager) this.degradationManager = new GracefulDegradationManager(this.logger);
		if (!this.reportingSystem) this.reportingSystem = new ErrorReportingSystem(this.logger);
		if (!this.recoverySystem) this.recoverySystem = new AutoRecoverySystem(this.logger);
		if (!this.analyticsEngine) this.analyticsEngine = new ErrorAnalyticsEngine(this.logger);

		if (this.config.degradation.enabled)
		{
			this.degradationManager.startMonitoring(this.config.degradation.monitoringInterval);
		}

		if (this.config.analytics.enabled)
		{
			this.analyticsEngine.startAnalysis(this.config.analytics.analysisInterval);
		}

		this.startHealthMonitoring();
	}

	protected async reportError(
		error: Error,
		classification: ErrorClassificationResultType,
		context: ErrorHandlingContextType
	): Promise<string | undefined>
	{
		if (!this.config.reporting.enabled)
		{
			return undefined;
		}

		return await this.reportingSystem.reportError(error, classification, context.metadata);
	}

	protected async handleServiceSpecificError(
		error: Error,
		classification: ErrorClassificationResultType,
		context: ErrorHandlingContextType
	): Promise<{ actions: string[]; recoveryAttempted?: boolean; degradationTriggered?: boolean } | null>
	{
		const actions: string[] = [];
		let recoveryAttempted = false;
		let degradationTriggered = false;

		if (this.config.analytics.enabled)
		{
			this.analyticsEngine.recordError(error, classification, context.metadata);
			actions.push('Error recorded for analytics');
		}

		if (this.config.recovery.enabled && this.config.recovery.autoRecoveryEnabled)
		{
			const recoveryContext: RecoveryContextType = {
				triggeredBy: 'error',
				errorReports: [classification],
				metadata: context.metadata,
			};

			recoveryAttempted = await this.recoverySystem.attemptRecovery(recoveryContext);
			if (recoveryAttempted)
			{
				actions.push('Auto-recovery attempted');
			}
		}

		if (this.config.degradation.enabled)
		{
			const degradationContext = await this.buildDegradationContext();
			const degradationStatus = this.degradationManager.evaluateAndApplyDegradation(degradationContext);

			if (degradationStatus.level !== 'none')
			{
				degradationTriggered = true;
				actions.push(`Degradation triggered: ${degradationStatus.level}`);
			}
		}

		return {
			actions,
			recoveryAttempted,
			degradationTriggered,
		};
	}

	private async buildDegradationContext(): Promise<DegradationContextType>
	{
		const circuitBreakerStates: Record<string, string> = {};
		for (const [name, breaker] of this.circuitBreakers)
		{
			circuitBreakerStates[name] = breaker.getState();
		}

		return {
			errorRate: 0.1,
			responseTime: 1000,
			resourceUsage: {
				cpu: 0.5,
				memory: 0.6,
				disk: 0.3,
			},
			serviceHealth: {
				database: 'healthy',
				external_apis: 'healthy',
				cache: 'healthy',
			},
			circuitBreakerStates,
			activeErrors: [],
			timestamp: new Date(),
		};
	}

	private startHealthMonitoring(): void
	{
		this.healthMonitoringInterval = setInterval(() =>
		{
			try
			{
				const healthStatus = this.getSystemHealth();

				this.logger.debug('System health check completed', {
					overall: healthStatus.overall,
					errorRate: healthStatus.metrics.errorRate,
					degradationLevel: healthStatus.metrics.degradationLevel,
				});

				if (healthStatus.overall === 'unhealthy' && this.config.recovery.autoRecoveryEnabled)
				{
					const recoveryContext: RecoveryContextType = {
						triggeredBy: 'health_check',
						serviceHealth: {
							overall: healthStatus.overall,
						},
						metadata: {
							healthStatus,
						},
					};

					this.recoverySystem.attemptRecovery(recoveryContext).catch((error) =>
					{
						this.logger.error('Health-triggered recovery failed', {
							error: error.message,
						});
					});
				}
			}
			catch (error)
			{
				this.logger.error('Health monitoring failed', {
					error: (error as Error).message,
				});
			}
		}, 60000);
	}

	getSystemHealth(): SystemHealthStatusType
	{
		const retryStats = this.retryManager.getStatistics();
		const recoveryStats = this.recoverySystem.getStatistics() as any[];
		const degradationStatus = this.degradationManager.getStatus();

		const retrySuccessRate = retryStats.length > 0
			? retryStats.reduce((sum, stat) => sum + stat.successRate, 0) / retryStats.length
			: 1;

		const recoverySuccessRate = recoveryStats.length > 0
			? recoveryStats.reduce((sum, stat) => sum + stat.successRate, 0) / recoveryStats.length
			: 1;

		const circuitBreakerStates = this.getCircuitBreakerStates();
		const circuitBreakerOpenCount = Object.values(circuitBreakerStates)
			.filter(state => state === 'OPEN').length;

		const components = {
			errorClassification: 'healthy' as const,
			retryManager: retrySuccessRate > 0.7 ? 'healthy' as const : 'degraded' as const,
			circuitBreakers: circuitBreakerOpenCount === 0 ? 'healthy' as const : 'degraded' as const,
			degradationManager: degradationStatus.level === 'none' ? 'healthy' as const : 'degraded' as const,
			reportingSystem: 'healthy' as const,
			recoverySystem: recoverySuccessRate > 0.5 ? 'healthy' as const : 'degraded' as const,
			analyticsEngine: 'healthy' as const,
		};

		const unhealthyComponents = Object.values(components).filter(status => status === 'unhealthy').length;
		const degradedComponents = Object.values(components).filter(status => status === 'degraded').length;

		let overall: 'healthy' | 'degraded' | 'unhealthy';
		if (unhealthyComponents > 0)
		{
			overall = 'unhealthy';
		}
		else if (degradedComponents > 2)
		{
			overall = 'unhealthy';
		}
		else if (degradedComponents > 0)
		{
			overall = 'degraded';
		}
		else
		{
			overall = 'healthy';
		}

		return {
			overall,
			components,
			metrics: {
				errorRate: 0.1,
				retrySuccessRate,
				recoverySuccessRate,
				circuitBreakerOpenCount,
				degradationLevel: degradationStatus.level,
			},
			lastUpdated: new Date(),
		};
	}

	getErrorAnalytics(): any
	{
		return {
			patterns: this.analyticsEngine.getDetectedPatterns(),
			trends: this.analyticsEngine.getErrorTrends(),
			anomalies: this.analyticsEngine.getAnomalies(),
			correlations: this.analyticsEngine.getCorrelations(),
		};
	}

	getRecoveryStatistics(): any
	{
		return this.recoverySystem.getStatistics();
	}

	getActiveAlerts(): any
	{
		return this.reportingSystem.getActiveAlerts();
	}

	acknowledgeAlert(alertId: string, userId?: string): boolean
	{
		return this.reportingSystem.acknowledgeAlert(alertId, userId);
	}

	resolveAlert(alertId: string, userId?: string): boolean
	{
		return this.reportingSystem.resolveAlert(alertId, userId);
	}

	restoreAllCapabilities(): void
	{
		this.degradationManager.restoreAllCapabilities();
	}

	protected onShutdown(): void
	{
		if (this.healthMonitoringInterval)
		{
			clearInterval(this.healthMonitoringInterval);
		}

		this.degradationManager.stopMonitoring();
		this.analyticsEngine.stopAnalysis();

		this.logger.info('Worker error handler shutdown completed');
	}
}

export default WorkerErrorHandler;
