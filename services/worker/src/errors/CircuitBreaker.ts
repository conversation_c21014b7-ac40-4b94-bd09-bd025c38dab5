import type { Logger } from '@shared';
import type { ErrorClassificationResultType } from './ErrorClassification';

/**
 * Circuit breaker states
 */
enum CircuitBreakerStateEnum
{
	CLOSED = 'closed',
	OPEN = 'open',
	HALF_OPEN = 'half_open',
}

/**
 * Circuit breaker configuration
 */
type CircuitBreakerConfigType =
{
	failureThreshold: number;
	successThreshold: number;
	timeout: number;
	monitoringPeriod: number;
	volumeThreshold: number;
	errorRateThreshold: number;
	slowCallThreshold: number;
	slowCallDurationThreshold: number;
};

/**
 * Circuit breaker metrics
 */
type CircuitBreakerMetricsType =
{
	totalCalls: number;
	successfulCalls: number;
	failedCalls: number;
	slowCalls: number;
	errorRate: number;
	slowCallRate: number;
	averageResponseTime: number;
	lastFailureTime?: Date;
	lastSuccessTime?: Date;
};

/**
 * Circuit breaker event
 */
type CircuitBreakerEventType =
{
	serviceName: string;
	state: CircuitBreakerStateEnum;
	previousState: CircuitBreakerStateEnum;
	timestamp: Date;
	metrics: CircuitBreakerMetricsType;
	reason: string;
};

/**
 * Call result for circuit breaker
 */
type CallResultType =
{
	success: boolean;
	duration: number;
	error?: Error;
	timestamp: Date;
};

/**
 * Circuit breaker for external service failures and dependency management
 */
class CircuitBreaker
{
	private readonly serviceName: string;
	private readonly logger: Logger;
	private readonly config: CircuitBreakerConfigType;
	private state: CircuitBreakerStateEnum;
	private metrics: CircuitBreakerMetricsType;
	private callHistory: CallResultType[];
	private lastStateChange: Date;
	private nextAttemptTime: Date;
	private eventListeners: ((event: CircuitBreakerEventType) => void)[];

	constructor(serviceName: string, logger: Logger, config?: Partial<CircuitBreakerConfigType>)
	{
		this.serviceName = serviceName;
		this.logger = logger;
		this.config = {
			failureThreshold: 5,
			successThreshold: 3,
			timeout: 60000, // 1 minute
			monitoringPeriod: 300000, // 5 minutes
			volumeThreshold: 10,
			errorRateThreshold: 0.5, // 50%
			slowCallThreshold: 5,
			slowCallDurationThreshold: 5000, // 5 seconds
			...config,
		};
		this.state = CircuitBreakerStateEnum.CLOSED;
		this.metrics = this.initializeMetrics();
		this.callHistory = [];
		this.lastStateChange = new Date();
		this.nextAttemptTime = new Date();
		this.eventListeners = [];
	}

	/**
     * Execute operation through circuit breaker
     */
	async execute<T>(
		operation: () => Promise<T>,
		errorClassification?: ErrorClassificationResultType
	): Promise<T>
	{
		// Check if circuit breaker allows the call
		if (!this.canExecute())
		{
			const error = new Error(`Circuit breaker is ${this.state} for service: ${this.serviceName}`);
			this.logger.warn('Circuit breaker blocked call', {
				serviceName: this.serviceName,
				state: this.state,
				nextAttemptTime: this.nextAttemptTime,
			});
			throw error;
		}

		const startTime = Date.now();
		let callResult: CallResultType;

		try
		{
			const result = await operation();
			const duration = Date.now() - startTime;

			callResult = {
				success: true,
				duration,
				timestamp: new Date(),
			};

			this.recordCall(callResult);
			this.onSuccess();

			return result;
		}
		catch (error)
		{
			const duration = Date.now() - startTime;

			callResult = {
				success: false,
				duration,
				error: error as Error,
				timestamp: new Date(),
			};

			this.recordCall(callResult);
			this.onFailure(error as Error, errorClassification);

			throw error;
		}
	}

	/**
     * Check if circuit breaker allows execution
     */
	private canExecute(): boolean
	{
		const now = new Date();

		switch (this.state)
		{
			case CircuitBreakerStateEnum.CLOSED:
				return true;

			case CircuitBreakerStateEnum.OPEN:
				if (now >= this.nextAttemptTime)
				{
					this.transitionToHalfOpen();
					return true;
				}
				return false;

			case CircuitBreakerStateEnum.HALF_OPEN:
				return true;

			default:
				return false;
		}
	}

	/**
     * Handle successful operation
     */
	private onSuccess(): void
	{
		this.metrics.lastSuccessTime = new Date();

		if (this.state === CircuitBreakerStateEnum.HALF_OPEN)
		{
			const recentSuccesses = this.getRecentSuccessfulCalls();
			if (recentSuccesses >= this.config.successThreshold)
			{
				this.transitionToClosed();
			}
		}
	}

	/**
     * Handle failed operation
     */
	private onFailure(error: Error, errorClassification?: ErrorClassificationResultType): void
	{
		this.metrics.lastFailureTime = new Date();

		// Only count certain types of failures for circuit breaking
		if (this.shouldCountFailure(error, errorClassification))
		{
			if (this.state === CircuitBreakerStateEnum.HALF_OPEN)
			{
				this.transitionToOpen('Failure in half-open state');
			}
			else if (this.state === CircuitBreakerStateEnum.CLOSED)
			{
				this.updateMetrics();
				if (this.shouldOpen())
				{
					this.transitionToOpen('Failure threshold exceeded');
				}
			}
		}
	}

	/**
     * Determine if failure should count towards circuit breaking
     */
	private shouldCountFailure(
		error: Error,
		errorClassification?: ErrorClassificationResultType,
	): boolean
	{
		if (!errorClassification)
		{
			return true; // Count all failures if no classification
		}

		const { classification } = errorClassification;

		// Don't count validation errors or permanent failures
		if (classification.category === 'validation' || classification.persistence === 'permanent')
		{
			return false;
		}

		// Count network, database, and external service failures
		return ['network', 'database', 'external_service', 'timeout', 'rate_limit']
			.includes(classification.category);
	}

	/**
     * Check if circuit breaker should open
     */
	private shouldOpen(): boolean
	{
		const recentCalls = this.getRecentCalls();

		// Check volume threshold
		if (recentCalls.length < this.config.volumeThreshold)
		{
			return false;
		}

		// Check error rate
		const failedCalls = recentCalls.filter(call => !call.success).length;
		const errorRate = failedCalls / recentCalls.length;

		if (errorRate >= this.config.errorRateThreshold)
		{
			return true;
		}

		// Check slow call rate
		const slowCalls = recentCalls
			.filter(call => call.duration > this.config.slowCallDurationThreshold)
			.length;

		const slowCallRate = slowCalls / recentCalls.length;

		if (slowCalls >= this.config.slowCallThreshold && slowCallRate >= this.config.errorRateThreshold)
		{
			return true;
		}

		return false;
	}

	/**
     * Transition to CLOSED state
     */
	private transitionToClosed(): void
	{
		const previousState = this.state;
		this.state = CircuitBreakerStateEnum.CLOSED;
		this.lastStateChange = new Date();
		this.resetMetrics();

		this.logger.info('Circuit breaker transitioned to CLOSED', {
			serviceName: this.serviceName,
			previousState,
			currentState: this.state,
		});

		this.emitEvent(previousState, 'Successful calls threshold reached');
	}

	/**
     * Transition to OPEN state
     */
	private transitionToOpen(reason: string): void
	{
		const previousState = this.state;
		this.state = CircuitBreakerStateEnum.OPEN;
		this.lastStateChange = new Date();
		this.nextAttemptTime = new Date(Date.now() + this.config.timeout);

		this.logger.warn('Circuit breaker transitioned to OPEN', {
			serviceName: this.serviceName,
			previousState,
			currentState: this.state,
			reason,
			nextAttemptTime: this.nextAttemptTime,
		});

		this.emitEvent(previousState, reason);
	}

	/**
     * Transition to HALF_OPEN state
     */
	private transitionToHalfOpen(): void
	{
		const previousState = this.state;
		this.state = CircuitBreakerStateEnum.HALF_OPEN;
		this.lastStateChange = new Date();

		this.logger.info('Circuit breaker transitioned to HALF_OPEN', {
			serviceName: this.serviceName,
			previousState,
			currentState: this.state,
		});

		this.emitEvent(previousState, 'Timeout period elapsed, testing service');
	}

	/**
     * Record call result
     */
	private recordCall(result: CallResultType): void
	{
		this.callHistory.push(result);

		// Keep only recent calls within monitoring period
		const cutoffTime = new Date(Date.now() - this.config.monitoringPeriod);
		this.callHistory = this.callHistory.filter(call => call.timestamp >= cutoffTime);

		this.updateMetrics();
	}

	/**
     * Update metrics based on call history
     */
	private updateMetrics(): void
	{
		const recentCalls = this.getRecentCalls();

		this.metrics.totalCalls = recentCalls.length;
		this.metrics.successfulCalls = recentCalls.filter(call => call.success).length;
		this.metrics.failedCalls = recentCalls.filter(call => !call.success).length;
		this.metrics.slowCalls = recentCalls.filter(call => call.duration > this.config.slowCallDurationThreshold).length;

		this.metrics.errorRate = this.metrics.totalCalls > 0 ? this.metrics.failedCalls / this.metrics.totalCalls : 0;
		this.metrics.slowCallRate = this.metrics.totalCalls > 0 ? this.metrics.slowCalls / this.metrics.totalCalls : 0;

		const totalDuration = recentCalls.reduce((sum, call) => sum + call.duration, 0);
		this.metrics.averageResponseTime = this.metrics.totalCalls > 0 ? totalDuration / this.metrics.totalCalls : 0;
	}

	/**
     * Get recent calls within monitoring period
     */
	private getRecentCalls(): CallResultType[]
	{
		const cutoffTime = new Date(Date.now() - this.config.monitoringPeriod);
		return this.callHistory.filter(call => call.timestamp >= cutoffTime);
	}

	/**
     * Get recent successful calls count
     */
	private getRecentSuccessfulCalls(): number
	{
		const recentCalls = this.getRecentCalls();
		return recentCalls.filter(call => call.success).length;
	}

	/**
     * Reset metrics
     */
	private resetMetrics(): void
	{
		this.metrics = this.initializeMetrics();
		this.callHistory = [];
	}

	/**
     * Initialize metrics
     */
	private initializeMetrics(): CircuitBreakerMetricsType
	{
		return {
			totalCalls: 0,
			successfulCalls: 0,
			failedCalls: 0,
			slowCalls: 0,
			errorRate: 0,
			slowCallRate: 0,
			averageResponseTime: 0,
		};
	}

	/**
     * Emit circuit breaker event
     */
	private emitEvent(previousState: CircuitBreakerStateEnum, reason: string): void
	{
		const event: CircuitBreakerEventType = {
			serviceName: this.serviceName,
			state: this.state,
			previousState,
			timestamp: new Date(),
			metrics: { ...this.metrics },
			reason,
		};

		this.eventListeners.forEach((listener) =>
		{
			try
			{
				listener(event);
			}
			catch (error)
			{
				this.logger.error('Error in circuit breaker event listener', {
					serviceName: this.serviceName,
					error: error.message,
				});
			}
		});
	}

	/**
     * Add event listener
     */
	addEventListener(listener: (event: CircuitBreakerEventType) => void): void
	{
		this.eventListeners.push(listener);
	}

	/**
     * Remove event listener
     */
	removeEventListener(listener: (event: CircuitBreakerEventType) => void): void
	{
		const index = this.eventListeners.indexOf(listener);
		if (index > -1)
		{
			this.eventListeners.splice(index, 1);
		}
	}

	/**
     * Get current state
     */
	getState(): CircuitBreakerStateEnum
	{
		return this.state;
	}

	/**
     * Get current metrics
     */
	getMetrics(): CircuitBreakerMetricsType
	{
		return { ...this.metrics };
	}

	/**
     * Get configuration
     */
	getConfig(): CircuitBreakerConfigType
	{
		return { ...this.config };
	}

	/**
     * Force state change (for testing/manual intervention)
     */
	forceState(state: CircuitBreakerStateEnum, reason: string = 'Manual intervention'): void
	{
		const previousState = this.state;
		this.state = state;
		this.lastStateChange = new Date();

		if (state === CircuitBreakerStateEnum.OPEN)
		{
			this.nextAttemptTime = new Date(Date.now() + this.config.timeout);
		}

		this.logger.warn('Circuit breaker state forced', {
			serviceName: this.serviceName,
			previousState,
			currentState: state,
			reason,
		});

		this.emitEvent(previousState, reason);
	}
}

export type {
	CircuitBreakerConfigType,
	CircuitBreakerMetricsType,
	CircuitBreakerEventType,
	CallResultType,
};

export { CircuitBreakerStateEnum };

export default CircuitBreaker;
