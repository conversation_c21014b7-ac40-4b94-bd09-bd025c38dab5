import type { Logger } from '@shared';

/**
 * Error severity levels for classification and handling
 */
export enum ErrorSeverityEnum
{
	LOW = 'low',
	MEDIUM = 'medium',
	HIGH = 'high',
	CRITICAL = 'critical',
}

/**
 * Error categories for classification
 */
export enum ErrorCategoryEnum
{
	NETWORK = 'network',
	DATABASE = 'database',
	EXTERNAL_SERVICE = 'external_service',
	VALIDATION = 'validation',
	RESOURCE = 'resource',
	CONFIGURATION = 'configuration',
	BUSINESS_LOGIC = 'business_logic',
	SECURITY = 'security',
	TIMEOUT = 'timeout',
	RATE_LIMIT = 'rate_limit',
}

/**
 * Error persistence types
 */
export enum ErrorPersistenceEnum
{
	TRANSIENT = 'transient',
	PERMANENT = 'permanent',
}

/**
 * Error recoverability types
 */
export enum ErrorRecoverabilityEnum
{
	RECOVERABLE = 'recoverable',
	FATAL = 'fatal',
}

/**
 * Error scope types
 */
export enum ErrorScopeEnum
{
	SERVICE_SPECIFIC = 'service_specific',
	SYSTEM_WIDE = 'system_wide',
}

/**
 * Comprehensive error classification
 */
export interface ErrorClassificationType
{
	severity: ErrorSeverityEnum;
	category: ErrorCategoryEnum;
	persistence: ErrorPersistenceEnum;
	recoverability: ErrorRecoverabilityEnum;
	scope: ErrorScopeEnum;
	retryable: boolean;
	requiresImmediateAttention: boolean;
	escalationRequired: boolean;
}

/**
 * Error pattern for matching and classification
 */
export interface ErrorPatternType
{
	name: string;
	pattern: RegExp | string;
	classification: ErrorClassificationType;
	description: string;
	commonCauses: string[];
	suggestedActions: string[];
}

/**
 * Error classification result
 */
export interface ErrorClassificationResultType
{
	classification: ErrorClassificationType;
	pattern?: ErrorPatternType;
	confidence: number;
	metadata: Record<string, any>;
}

/**
 * Comprehensive error classification system
 */
class ErrorClassificationService
{
	private readonly logger: Logger;
	private readonly errorPatterns: ErrorPatternType[];

	constructor(logger: Logger)
	{
		this.logger = logger;
		this.errorPatterns = this.initializeErrorPatterns();
	}

	/**
     * Classify an error based on its characteristics
     */
	classifyError(error: Error, context?: Record<string, any>): ErrorClassificationResultType
	{
		try
		{
			// Try to match against known patterns
			const patternMatch = this.matchErrorPattern(error);
			if (patternMatch)
			{
				return {
					classification: patternMatch.classification,
					pattern: patternMatch,
					confidence: 0.9,
					metadata: {
						errorMessage: error.message,
						errorName: error.name,
						stack: error.stack,
						context: context || {},
					},
				};
			}

			// Fallback to heuristic classification
			const heuristicClassification = this.classifyByHeuristics(error, context);
			return {
				classification: heuristicClassification,
				confidence: 0.6,
				metadata: {
					errorMessage: error.message,
					errorName: error.name,
					stack: error.stack,
					context: context || {},
				},
			};
		}
		catch (classificationError)
		{
			this.logger.error('Error during error classification', {
				originalError: error.message,
				classificationError: classificationError.message,
			});

			// Return default classification for unknown errors
			return {
				classification: this.getDefaultClassification(),
				confidence: 0.1,
				metadata: {
					errorMessage: error.message,
					errorName: error.name,
					classificationFailed: true,
				},
			};
		}
	}

	/**
     * Match error against known patterns
     */
	private matchErrorPattern(error: Error): ErrorPatternType | null
	{
		for (const pattern of this.errorPatterns)
		{
			if (this.matchesPattern(error, pattern))
			{
				return pattern;
			}
		}
		return null;
	}

	/**
     * Check if error matches a specific pattern
     */
	private matchesPattern(error: Error, pattern: ErrorPatternType): boolean
	{
		const errorText = `${error.name}: ${error.message}`;

		if (pattern.pattern instanceof RegExp)
		{
			return pattern.pattern.test(errorText);
		}

		return errorText.toLowerCase().includes(pattern.pattern.toLowerCase());
	}

	/**
     * Classify error using heuristics when no pattern matches
     */
	private classifyByHeuristics(error: Error, context?: Record<string, any>): ErrorClassificationType
	{
		const errorMessage = error.message.toLowerCase();
		const errorName = error.name.toLowerCase();

		// Network-related errors
		if (this.isNetworkError(errorMessage, errorName))
		{
			return {
				severity: ErrorSeverity.MEDIUM,
				category: ErrorCategory.NETWORK,
				persistence: ErrorPersistence.TRANSIENT,
				recoverability: ErrorRecoverability.RECOVERABLE,
				scope: ErrorScope.SERVICE_SPECIFIC,
				retryable: true,
				requiresImmediateAttention: false,
				escalationRequired: false,
			};
		}

		// Database-related errors
		if (this.isDatabaseError(errorMessage, errorName))
		{
			return {
				severity: ErrorSeverity.HIGH,
				category: ErrorCategory.DATABASE,
				persistence: ErrorPersistence.TRANSIENT,
				recoverability: ErrorRecoverability.RECOVERABLE,
				scope: ErrorScope.SYSTEM_WIDE,
				retryable: true,
				requiresImmediateAttention: true,
				escalationRequired: false,
			};
		}

		// Timeout errors
		if (this.isTimeoutError(errorMessage, errorName))
		{
			return {
				severity: ErrorSeverity.MEDIUM,
				category: ErrorCategory.TIMEOUT,
				persistence: ErrorPersistence.TRANSIENT,
				recoverability: ErrorRecoverability.RECOVERABLE,
				scope: ErrorScope.SERVICE_SPECIFIC,
				retryable: true,
				requiresImmediateAttention: false,
				escalationRequired: false,
			};
		}

		// Resource errors
		if (this.isResourceError(errorMessage, errorName))
		{
			return {
				severity: ErrorSeverity.HIGH,
				category: ErrorCategory.RESOURCE,
				persistence: ErrorPersistence.TRANSIENT,
				recoverability: ErrorRecoverability.RECOVERABLE,
				scope: ErrorScope.SYSTEM_WIDE,
				retryable: false,
				requiresImmediateAttention: true,
				escalationRequired: true,
			};
		}

		// Default classification for unknown errors
		return this.getDefaultClassification();
	}

	/**
     * Check if error is network-related
     */
	private isNetworkError(message: string, name: string): boolean
	{
		const networkKeywords = [
			'network', 'connection', 'timeout', 'refused', 'unreachable',
			'dns', 'resolve', 'enotfound', 'econnrefused', 'etimedout',
		];
		return networkKeywords.some(keyword => message.includes(keyword) || name.includes(keyword));
	}

	/**
     * Check if error is database-related
     */
	private isDatabaseError(message: string, name: string): boolean
	{
		const dbKeywords = [
			'database', 'connection', 'query', 'transaction', 'deadlock',
			'scylla', 'cassandra', 'mariadb', 'mysql', 'redis', 'manticore',
		];
		return dbKeywords.some(keyword => message.includes(keyword) || name.includes(keyword));
	}

	/**
     * Check if error is timeout-related
     */
	private isTimeoutError(message: string, name: string): boolean
	{
		const timeoutKeywords = ['timeout', 'timed out', 'etimedout', 'deadline'];
		return timeoutKeywords.some(keyword => message.includes(keyword) || name.includes(keyword));
	}

	/**
     * Check if error is resource-related
     */
	private isResourceError(message: string, name: string): boolean
	{
		const resourceKeywords = [
			'memory', 'cpu', 'disk', 'space', 'limit', 'quota',
			'enomem', 'enospc', 'resource', 'capacity',
		];
		return resourceKeywords.some(keyword => message.includes(keyword) || name.includes(keyword));
	}

	/**
     * Get default classification for unknown errors
     */
	private getDefaultClassification(): ErrorClassificationType
	{
		return {
			severity: ErrorSeverity.MEDIUM,
			category: ErrorCategory.BUSINESS_LOGIC,
			persistence: ErrorPersistence.PERMANENT,
			recoverability: ErrorRecoverability.FATAL,
			scope: ErrorScope.SERVICE_SPECIFIC,
			retryable: false,
			requiresImmediateAttention: false,
			escalationRequired: false,
		};
	}

	/**
     * Initialize known error patterns
     */
	private initializeErrorPatterns(): ErrorPatternType[]
	{
		return [
			// Network errors
			{
				name: 'Connection Refused',
				pattern: /ECONNREFUSED|Connection refused/i,
				classification: {
					severity: ErrorSeverity.MEDIUM,
					category: ErrorCategory.NETWORK,
					persistence: ErrorPersistence.TRANSIENT,
					recoverability: ErrorRecoverability.RECOVERABLE,
					scope: ErrorScope.SERVICE_SPECIFIC,
					retryable: true,
					requiresImmediateAttention: false,
					escalationRequired: false,
				},
				description: 'Connection to external service was refused',
				commonCauses: ['Service down', 'Network issues', 'Firewall blocking'],
				suggestedActions: ['Retry with backoff', 'Check service status', 'Verify network connectivity'],
			},
			{
				name: 'DNS Resolution Failed',
				pattern: /ENOTFOUND|getaddrinfo|DNS/i,
				classification: {
					severity: ErrorSeverity.MEDIUM,
					category: ErrorCategory.NETWORK,
					persistence: ErrorPersistence.TRANSIENT,
					recoverability: ErrorRecoverability.RECOVERABLE,
					scope: ErrorScope.SERVICE_SPECIFIC,
					retryable: true,
					requiresImmediateAttention: false,
					escalationRequired: false,
				},
				description: 'DNS resolution failed for hostname',
				commonCauses: ['DNS server issues', 'Invalid hostname', 'Network connectivity'],
				suggestedActions: ['Retry with different DNS', 'Verify hostname', 'Check network'],
			},
			// Database errors
			{
				name: 'Database Connection Lost',
				pattern: /Connection lost|Database connection/i,
				classification: {
					severity: ErrorSeverity.HIGH,
					category: ErrorCategory.DATABASE,
					persistence: ErrorPersistence.TRANSIENT,
					recoverability: ErrorRecoverability.RECOVERABLE,
					scope: ErrorScope.SYSTEM_WIDE,
					retryable: true,
					requiresImmediateAttention: true,
					escalationRequired: false,
				},
				description: 'Database connection was lost',
				commonCauses: ['Database restart', 'Network issues', 'Connection timeout'],
				suggestedActions: ['Reconnect to database', 'Check database status', 'Verify connection pool'],
			},
			// Rate limiting
			{
				name: 'Rate Limit Exceeded',
				pattern: /rate limit|too many requests|429/i,
				classification: {
					severity: ErrorSeverity.MEDIUM,
					category: ErrorCategory.RATE_LIMIT,
					persistence: ErrorPersistence.TRANSIENT,
					recoverability: ErrorRecoverability.RECOVERABLE,
					scope: ErrorScope.SERVICE_SPECIFIC,
					retryable: true,
					requiresImmediateAttention: false,
					escalationRequired: false,
				},
				description: 'API rate limit exceeded',
				commonCauses: ['Too many requests', 'Rate limit configuration', 'Burst traffic'],
				suggestedActions: ['Implement backoff', 'Reduce request rate', 'Check rate limits'],
			},
			// Resource errors
			{
				name: 'Out of Memory',
				pattern: /out of memory|ENOMEM|heap|memory/i,
				classification: {
					severity: ErrorSeverity.CRITICAL,
					category: ErrorCategory.RESOURCE,
					persistence: ErrorPersistence.TRANSIENT,
					recoverability: ErrorRecoverability.RECOVERABLE,
					scope: ErrorScope.SYSTEM_WIDE,
					retryable: false,
					requiresImmediateAttention: true,
					escalationRequired: true,
				},
				description: 'System is out of memory',
				commonCauses: ['Memory leak', 'High load', 'Insufficient resources'],
				suggestedActions: ['Restart service', 'Increase memory', 'Check for leaks'],
			},
		];
	}
}

export { ErrorClassificationService };
export type { ErrorClassificationType, ErrorPatternType, ErrorClassificationResultType };

export default ErrorClassificationService;
