/**
 * @deprecated Use WorkerErrorHandler instead. This class is kept for backward compatibility.
 * 
 * ComprehensiveErrorHandler is now a thin wrapper around Worker<PERSON>rror<PERSON>and<PERSON>
 * to maintain backward compatibility. All new code should use WorkerErrorHandler directly.
 */
import type { LoggerInstanceType } from '@shared';
import { WorkerErrorHandler } from './WorkerErrorHandler';

// Re-export types from WorkerErrorHandler for backward compatibility
export type {
	WorkerErrorHandlingConfigType as ErrorHandlingConfigType,
	SystemHealthStatusType,
} from './WorkerErrorHandler';

export type {
	ErrorHandlingContextType,
	ErrorHandlingResultType,
} from '@shared';

/**
 * @deprecated Use WorkerErrorHandler instead
 * 
 * Legacy comprehensive error handler - now extends Worker<PERSON>rrorHandler
 * for backward compatibility
 */
class ComprehensiveErrorHandler extends WorkerErrorHandler 
{
	constructor(logger: LoggerInstanceType, config?: any)
	{
		super(logger, config);
		logger.warn('[DEPRECATED] ComprehensiveErrorHandler is deprecated. Use WorkerErrorHandler instead.');
	}
}

export { ComprehensiveErrorHandler };
