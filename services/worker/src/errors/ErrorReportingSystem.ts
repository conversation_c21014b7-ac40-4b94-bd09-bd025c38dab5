import { IdGenerator } from '@shared';
import type { LoggerInstanceType, ErrorClassificationResultType } from '@shared';
import type { DegradationStatusType } from './GracefulDegradationManager';

/**
 * Alert severity levels
 */
export enum AlertSeverity
{
	INFO = 'info',
	WARNING = 'warning',
	ERROR = 'error',
	CRITICAL = 'critical',
}

/**
 * Alert channel types
 */
export enum AlertChannel
{
	LOG = 'log',
	EMAIL = 'email',
	SLACK = 'slack',
	WEBHOOK = 'webhook',
	SMS = 'sms',
}

/**
 * Error report
 */
export interface ErrorReportType
{
	id: string;
	timestamp: Date;
	error: Error;
	classification: ErrorClassificationResultType;
	context: Record<string, any>;
	stackTrace?: string;
	userId?: string;
	sessionId?: string;
	requestId?: string;
	environment: string;
	service: string;
	version: string;
}

/**
 * Alert configuration
 */
export interface AlertConfigType
{
	name: string;
	enabled: boolean;
	severity: AlertSeverity;
	channels: AlertChannel[];
	conditions: AlertConditionType[];
	throttling: AlertThrottlingType;
	escalation?: AlertEscalationType;
}

/**
 * Alert condition
 */
export interface AlertConditionType
{
	type: 'error_rate' | 'error_count' | 'error_severity' | 'degradation_level' | 'custom';
	operator: 'gt' | 'gte' | 'lt' | 'lte' | 'eq' | 'neq';
	value: number | string;
	timeWindow?: number; // milliseconds
}

/**
 * Alert throttling configuration
 */
export interface AlertThrottlingType
{
	enabled: boolean;
	maxAlertsPerHour: number;
	cooldownPeriod: number; // milliseconds
	groupByFields: string[];
}

/**
 * Alert escalation configuration
 */
export interface AlertEscalationType
{
	enabled: boolean;
	escalationDelay: number; // milliseconds
	escalationChannels: AlertChannel[];
	maxEscalations: number;
}

/**
 * Alert instance
 */
export interface AlertInstanceType
{
	id: string;
	configName: string;
	severity: AlertSeverity;
	title: string;
	message: string;
	timestamp: Date;
	context: Record<string, any>;
	channels: AlertChannel[];
	acknowledged: boolean;
	resolved: boolean;
	escalationLevel: number;
}

/**
 * Notification payload
 */
export interface NotificationPayloadType
{
	alert: AlertInstanceType;
	channel: AlertChannel;
	recipient?: string;
	template?: string;
}

/**
 * Error analytics data
 */
export interface ErrorAnalyticsType
{
	timeWindow: string;
	totalErrors: number;
	errorsByCategory: Record<string, number>;
	errorsBySeverity: Record<string, number>;
	errorRate: number;
	topErrors: Array<{
		message: string;
		count: number;
		lastOccurrence: Date;
	}>;
	trends: Array<{
		timestamp: Date;
		errorCount: number;
		errorRate: number;
	}>;
}

/**
 * Comprehensive error reporting and alerting system
 */
class ErrorReportingSystem
{
	private readonly logger: LoggerInstanceType;
	private readonly errorReports: Map<string, ErrorReportType>;
	private readonly alertConfigs: Map<string, AlertConfigType>;
	private readonly activeAlerts: Map<string, AlertInstanceType>;
	private readonly alertHistory: AlertInstanceType[];
	private readonly notificationHandlers: Map<AlertChannel, (payload: NotificationPayloadType) => Promise<void>>;
	private readonly errorPatterns: Map<string, number>;
	private monitoringInterval?: NodeJS.Timeout;

	constructor(logger: LoggerInstanceType)
	{
		this.logger = logger;
		this.errorReports = new Map();
		this.alertConfigs = new Map();
		this.activeAlerts = new Map();
		this.alertHistory = [];
		this.notificationHandlers = new Map();
		this.errorPatterns = new Map();
		this.initializeDefaultAlerts();
		this.initializeNotificationHandlers();
	}

	/**
     * Report an error to the system
     */
	async reportError(
		error: Error,
		classification: ErrorClassificationResultType,
		context: Record<string, any> = {}
	): Promise<string>
	{
		const reportId = this.generateReportId();

		const report: ErrorReportType = {
			id: reportId,
			timestamp: new Date(),
			error,
			classification,
			context,
			stackTrace: error.stack,
			environment: process.env.NODE_ENV || 'development',
			service: 'worker-service',
			version: process.env.SERVICE_VERSION || '1.0.0',
			...context,
		};

		this.errorReports.set(reportId, report);

		// Update error patterns for analytics
		this.updateErrorPatterns(error, classification);

		// Check if any alerts should be triggered
		await this.evaluateAlerts(report);

		this.logger.error('Error reported', {
			reportId,
			errorMessage: error.message,
			classification: classification.classification,
			context,
		});

		return reportId;
	}

	/**
     * Update error patterns for analytics
     */
	private updateErrorPatterns(error: Error, classification: ErrorClassificationResultType): void
	{
		const pattern = `${classification.classification.category}:${error.name}`;
		const currentCount = this.errorPatterns.get(pattern) || 0;
		this.errorPatterns.set(pattern, currentCount + 1);
	}

	/**
     * Evaluate alert conditions and trigger alerts if necessary
     */
	private async evaluateAlerts(report: ErrorReportType): Promise<void>
	{
		for (const [configName, config] of this.alertConfigs)
		{
			if (!config.enabled)
			{
				continue;
			}

			try
			{
				const shouldAlert = await this.evaluateAlertConditions(config, report);
				if (shouldAlert)
				{
					await this.triggerAlert(config, report);
				}
			}
			catch (error)
			{
				this.logger.error('Error evaluating alert conditions', {
					configName,
					error: error.message,
				});
			}
		}
	}

	/**
     * Evaluate if alert conditions are met
     */
	private async evaluateAlertConditions(config: AlertConfigType, report: ErrorReportType): boolean
	{
		for (const condition of config.conditions)
		{
			const conditionMet = await this.evaluateCondition(condition, report);
			if (!conditionMet)
			{
				return false; // All conditions must be met
			}
		}
		return true;
	}

	/**
     * Evaluate individual alert condition
     */
	private async evaluateCondition(condition: AlertConditionType, report: ErrorReportType): boolean
	{
		switch (condition.type)
		{
			case 'error_severity':
				return this.compareValues(
					this.getSeverityLevel(report.classification.classification.severity),
					condition.operator,
					this.getSeverityLevel(condition.value as ErrorSeverity)
				);

			case 'error_rate':
				const errorRate = await this.calculateErrorRate(condition.timeWindow || 300000);
				return this.compareValues(errorRate, condition.operator, condition.value as number);

			case 'error_count':
				const errorCount = await this.calculateErrorCount(condition.timeWindow || 300000);
				return this.compareValues(errorCount, condition.operator, condition.value as number);

			case 'custom':
				// Custom conditions would be implemented based on specific needs
				return false;

			default:
				return false;
		}
	}

	/**
     * Compare values based on operator
     */
	private compareValues(actual: number, operator: string, expected: number): boolean
	{
		switch (operator)
		{
			case 'gt': return actual > expected;
			case 'gte': return actual >= expected;
			case 'lt': return actual < expected;
			case 'lte': return actual <= expected;
			case 'eq': return actual === expected;
			case 'neq': return actual !== expected;
			default: return false;
		}
	}

	/**
     * Get numeric severity level for comparison
     */
	private getSeverityLevel(severity: ErrorSeverity): number
	{
		const levels = {
			low: 1,
			medium: 2,
			high: 3,
			critical: 4,
		};
		return levels[severity] || 0;
	}

	/**
     * Calculate error rate over time window
     */
	private async calculateErrorRate(timeWindowMs: number): Promise<number>
	{
		const cutoffTime = new Date(Date.now() - timeWindowMs);
		const recentReports = Array.from(this.errorReports.values())
			.filter(report => report.timestamp >= cutoffTime);

		// This is a simplified calculation
		// In a real system, you'd want to calculate against total requests
		return recentReports.length / (timeWindowMs / 1000); // errors per second
	}

	/**
     * Calculate error count over time window
     */
	private async calculateErrorCount(timeWindowMs: number): Promise<number>
	{
		const cutoffTime = new Date(Date.now() - timeWindowMs);
		return Array.from(this.errorReports.values())
			.filter(report => report.timestamp >= cutoffTime).length;
	}

	/**
     * Trigger an alert
     */
	private async triggerAlert(config: AlertConfigType, report: ErrorReportType): Promise<void>
	{
		// Check throttling
		if (config.throttling.enabled && this.isThrottled(config))
		{
			return;
		}

		const alertId = this.generateAlertId();
		const alert: AlertInstanceType = {
			id: alertId,
			configName: config.name,
			severity: config.severity,
			title: this.generateAlertTitle(config, report),
			message: this.generateAlertMessage(config, report),
			timestamp: new Date(),
			context: {
				reportId: report.id,
				errorMessage: report.error.message,
				classification: report.classification,
				...report.context,
			},
			channels: config.channels,
			acknowledged: false,
			resolved: false,
			escalationLevel: 0,
		};

		this.activeAlerts.set(alertId, alert);
		this.alertHistory.push(alert);

		// Send notifications
		await this.sendNotifications(alert);

		// Schedule escalation if configured
		if (config.escalation?.enabled)
		{
			this.scheduleEscalation(alert, config.escalation);
		}

		this.logger.warn('Alert triggered', {
			alertId,
			configName: config.name,
			severity: config.severity,
			title: alert.title,
		});
	}

	/**
     * Check if alert is throttled
     */
	private isThrottled(config: AlertConfigType): boolean
	{
		if (!config.throttling.enabled)
		{
			return false;
		}

		const cutoffTime = new Date(Date.now() - 3600000); // 1 hour
		const recentAlerts = this.alertHistory.filter(alert =>
			alert.configName === config.name &&
            alert.timestamp >= cutoffTime);

		return recentAlerts.length >= config.throttling.maxAlertsPerHour;
	}

	/**
     * Generate alert title
     */
	private generateAlertTitle(config: AlertConfigType, report: ErrorReportType): string
	{
		const { classification } = report.classification;
		return `${config.severity.toUpperCase()}: ${classification.category} error in ${report.service}`;
	}

	/**
     * Generate alert message
     */
	private generateAlertMessage(config: AlertConfigType, report: ErrorReportType): string
	{
		const { error, classification } = report;
		return `Error: ${error.message}\n`
               + `Category: ${classification.classification.category}\n`
               + `Severity: ${classification.classification.severity}\n`
               + `Service: ${report.service}\n`
               + `Time: ${report.timestamp.toISOString()}`;
	}

	/**
     * Send notifications for alert
     */
	private async sendNotifications(alert: AlertInstanceType): Promise<void>
	{
		const notifications = alert.channels.map(async (channel) =>
		{
			const handler = this.notificationHandlers.get(channel);
			if (handler)
			{
				try
				{
					await handler({ alert, channel });
				}
				catch (error)
				{
					this.logger.error('Failed to send notification', {
						alertId: alert.id,
						channel,
						error: error.message,
					});
				}
			}
		});

		await Promise.allSettled(notifications);
	}

	/**
     * Schedule alert escalation
     */
	private scheduleEscalation(alert: AlertInstanceType, escalation: AlertEscalationType): void
	{
		setTimeout(async () =>
		{
			if (!alert.acknowledged && !alert.resolved && alert.escalationLevel < escalation.maxEscalations)
			{
				alert.escalationLevel++;
				alert.channels = escalation.escalationChannels;

				this.logger.warn('Alert escalated', {
					alertId: alert.id,
					escalationLevel: alert.escalationLevel,
				});

				await this.sendNotifications(alert);

				// Schedule next escalation if needed
				if (alert.escalationLevel < escalation.maxEscalations)
				{
					this.scheduleEscalation(alert, escalation);
				}
			}
		}, escalation.escalationDelay);
	}

	/**
     * Acknowledge an alert
     */
	acknowledgeAlert(alertId: string, userId?: string): boolean
	{
		const alert = this.activeAlerts.get(alertId);
		if (alert && !alert.acknowledged)
		{
			alert.acknowledged = true;

			this.logger.info('Alert acknowledged', {
				alertId,
				userId,
				timestamp: new Date(),
			});

			return true;
		}
		return false;
	}

	/**
     * Resolve an alert
     */
	resolveAlert(alertId: string, userId?: string): boolean
	{
		const alert = this.activeAlerts.get(alertId);
		if (alert && !alert.resolved)
		{
			alert.resolved = true;
			this.activeAlerts.delete(alertId);

			this.logger.info('Alert resolved', {
				alertId,
				userId,
				timestamp: new Date(),
			});

			return true;
		}
		return false;
	}

	/**
     * Get error analytics
     */
	getErrorAnalytics(timeWindowMs: number = 3600000): ErrorAnalyticsType
	{
		const cutoffTime = new Date(Date.now() - timeWindowMs);
		const recentReports = Array.from(this.errorReports.values())
			.filter(report => report.timestamp >= cutoffTime);

		const errorsByCategory: Record<string, number> = {};
		const errorsBySeverity: Record<string, number> = {};
		const errorCounts: Record<string, number> = {};

		recentReports.forEach((report) =>
		{
			const category = report.classification.classification.category;
			const severity = report.classification.classification.severity;
			const message = report.error.message;

			errorsByCategory[category] = (errorsByCategory[category] || 0) + 1;
			errorsBySeverity[severity] = (errorsBySeverity[severity] || 0) + 1;
			errorCounts[message] = (errorCounts[message] || 0) + 1;
		});

		const topErrors = Object.entries(errorCounts)
			.sort(([, a], [, b]) => b - a)
			.slice(0, 10)
			.map(([message, count]) => ({
				message,
				count,
				lastOccurrence: recentReports
					.filter(r => r.error.message === message)
					.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())[0]?.timestamp || new Date(),
			}));

		return {
			timeWindow: `${timeWindowMs / 1000}s`,
			totalErrors: recentReports.length,
			errorsByCategory,
			errorsBySeverity,
			errorRate: recentReports.length / (timeWindowMs / 1000),
			topErrors,
			trends: [], // Would be calculated from historical data
		};
	}

	/**
     * Initialize default alert configurations
     */
	private initializeDefaultAlerts(): void
	{
		const defaultAlerts: AlertConfigType[] = [
			{
				name: 'critical_errors',
				enabled: true,
				severity: AlertSeverity.CRITICAL,
				channels: [AlertChannel.LOG, AlertChannel.EMAIL],
				conditions: [
					{
						type: 'error_severity',
						operator: 'gte',
						value: 'critical' as unknown,
					},
				],
				throttling: {
					enabled: true,
					maxAlertsPerHour: 10,
					cooldownPeriod: 300000,
					groupByFields: ['error.name'],
				},
				escalation: {
					enabled: true,
					escalationDelay: 900000, // 15 minutes
					escalationChannels: [AlertChannel.SMS],
					maxEscalations: 2,
				},
			},
			{
				name: 'high_error_rate',
				enabled: true,
				severity: AlertSeverity.WARNING,
				channels: [AlertChannel.LOG, AlertChannel.SLACK],
				conditions: [
					{
						type: 'error_rate',
						operator: 'gt',
						value: 0.1, // 0.1 errors per second
						timeWindow: 300000, // 5 minutes
					},
				],
				throttling: {
					enabled: true,
					maxAlertsPerHour: 5,
					cooldownPeriod: 600000,
					groupByFields: [],
				},
			},
		];

		defaultAlerts.forEach((alert) =>
		{
			this.alertConfigs.set(alert.name, alert);
		});
	}

	/**
     * Initialize notification handlers
     */
	private initializeNotificationHandlers(): void
	{
		// Log handler
		this.notificationHandlers.set(AlertChannel.LOG, async (payload) =>
		{
			this.logger.error('ALERT', {
				alertId: payload.alert.id,
				severity: payload.alert.severity,
				title: payload.alert.title,
				message: payload.alert.message,
				context: payload.alert.context,
			});
		});

		// Email handler (placeholder)
		this.notificationHandlers.set(AlertChannel.EMAIL, async (payload) =>
		{
			this.logger.info('Email notification sent', {
				alertId: payload.alert.id,
				recipient: payload.recipient || '<EMAIL>',
			});
		});

		// Slack handler (placeholder)
		this.notificationHandlers.set(AlertChannel.SLACK, async (payload) =>
		{
			this.logger.info('Slack notification sent', {
				alertId: payload.alert.id,
				channel: '#alerts',
			});
		});
	}

	/**
     * Generate unique report ID
     */
	private generateReportId(): string
	{
		return IdGenerator.errorId();
	}

	/**
     * Generate unique alert ID
     */
	private generateAlertId(): string
	{
		return IdGenerator.alertId();
	}

	/**
     * Get active alerts
     */
	getActiveAlerts(): AlertInstanceType[]
	{
		return Array.from(this.activeAlerts.values());
	}

	/**
     * Get alert history
     */
	getAlertHistory(limit: number = 100): AlertInstanceType[]
	{
		return this.alertHistory
			.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
			.slice(0, limit);
	}

	/**
     * Add custom alert configuration
     */
	addAlertConfig(config: AlertConfigType): void
	{
		this.alertConfigs.set(config.name, config);
	}

	/**
     * Remove alert configuration
     */
	removeAlertConfig(name: string): boolean
	{
		return this.alertConfigs.delete(name);
	}
}

export type {
	ErrorReportType,
	AlertConfigType,
	AlertConditionType,
	AlertThrottlingType,
	AlertEscalationType,
	AlertInstanceType,
	NotificationPayloadType,
	ErrorAnalyticsType,
};
export { ErrorReportingSystem };
export default ErrorReportingSystem;
