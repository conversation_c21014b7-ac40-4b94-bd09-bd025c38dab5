import type { LoggerInstanceType, ErrorClassificationResultType } from '@shared';
import type { CircuitBreakerEventType } from './CircuitBreaker';

/**
 * Degradation levels
 */
export enum DegradationLevel
{
	NONE = 'none',
	MINIMAL = 'minimal',
	MODERATE = 'moderate',
	SEVERE = 'severe',
	CRITICAL = 'critical',
}

/**
 * Service capability
 */
export interface ServiceCapabilityType
{
	name: string;
	essential: boolean;
	priority: number;
	dependencies: string[];
	fallbackAvailable: boolean;
	currentStatus: 'available' | 'degraded' | 'unavailable';
}

/**
 * Degradation rule
 */
export interface DegradationRuleType
{
	name: string;
	condition: (context: DegradationContextType) => boolean;
	action: DegradationActionType;
	priority: number;
	description: string;
}

/**
 * Degradation action
 */
export interface DegradationActionType
{
	type: 'disable_capability' | 'reduce_quality' | 'use_fallback' | 'skip_optional' | 'cache_only';
	target: string;
	parameters: Record<string, any>;
}

/**
 * Degradation context
 */
export interface DegradationContextType
{
	errorRate: number;
	responseTime: number;
	resourceUsage: {
		cpu: number;
		memory: number;
		disk: number;
	};
	serviceHealth: Record<string, 'healthy' | 'degraded' | 'unhealthy'>;
	circuitBreakerStates: Record<string, string>;
	activeErrors: ErrorClassificationResultType[];
	timestamp: Date;
}

/**
 * Degradation status
 */
export interface DegradationStatusType
{
	level: DegradationLevel;
	activeRules: string[];
	disabledCapabilities: string[];
	fallbacksActive: string[];
	lastUpdate: Date;
	reason: string;
}

/**
 * Graceful degradation manager for handling partial system failures
 */
class GracefulDegradationManager
{
	private readonly logger: LoggerInstanceType;
	private readonly capabilities: Map<string, ServiceCapabilityType>;
	private readonly degradationRules: DegradationRuleType[];
	private currentStatus: DegradationStatusType;
	private readonly eventListeners: ((status: DegradationStatusType) => void)[];
	private monitoringInterval?: NodeJS.Timeout;

	constructor(logger: LoggerInstanceType)
	{
		this.logger = logger;
		this.capabilities = new Map();
		this.degradationRules = [];
		this.currentStatus = {
			level: DegradationLevel.NONE,
			activeRules: [],
			disabledCapabilities: [],
			fallbacksActive: [],
			lastUpdate: new Date(),
			reason: 'System initialized',
		};
		this.eventListeners = [];
		this.initializeDefaultCapabilities();
		this.initializeDefaultRules();
	}

	/**
     * Initialize default service capabilities
     */
	private initializeDefaultCapabilities(): void
	{
		const defaultCapabilities: ServiceCapabilityType[] = [
			{
				name: 'domain_crawling',
				essential: true,
				priority: 1,
				dependencies: ['database', 'external_apis'],
				fallbackAvailable: false,
				currentStatus: 'available',
			},
			{
				name: 'content_analysis',
				essential: true,
				priority: 2,
				dependencies: ['ai_services', 'image_processing'],
				fallbackAvailable: true,
				currentStatus: 'available',
			},
			{
				name: 'ranking_calculation',
				essential: true,
				priority: 3,
				dependencies: ['database', 'historical_data'],
				fallbackAvailable: true,
				currentStatus: 'available',
			},
			{
				name: 'search_indexing',
				essential: true,
				priority: 4,
				dependencies: ['manticore', 'database'],
				fallbackAvailable: false,
				currentStatus: 'available',
			},
			{
				name: 'screenshot_capture',
				essential: false,
				priority: 5,
				dependencies: ['browserless'],
				fallbackAvailable: true,
				currentStatus: 'available',
			},
			{
				name: 'performance_auditing',
				essential: false,
				priority: 6,
				dependencies: ['browserless', 'external_apis'],
				fallbackAvailable: true,
				currentStatus: 'available',
			},
			{
				name: 'ai_content_generation',
				essential: false,
				priority: 7,
				dependencies: ['ai_services'],
				fallbackAvailable: true,
				currentStatus: 'available',
			},
			{
				name: 'image_processing',
				essential: false,
				priority: 8,
				dependencies: ['external_apis'],
				fallbackAvailable: true,
				currentStatus: 'available',
			},
		];

		defaultCapabilities.forEach((capability) =>
		{
			this.capabilities.set(capability.name, capability);
		});
	}

	/**
     * Initialize default degradation rules
     */
	private initializeDefaultRules(): void
	{
		this.degradationRules.push(
			// High error rate rule
			{
				name: 'high_error_rate',
				condition: context => context.errorRate > 0.3,
				action: {
					type: 'disable_capability',
					target: 'ai_content_generation',
					parameters: { reason: 'High error rate detected' },
				},
				priority: 1,
				description: 'Disable AI content generation when error rate exceeds 30%',
			},
			// High response time rule
			{
				name: 'high_response_time',
				condition: context => context.responseTime > 30000,
				action: {
					type: 'reduce_quality',
					target: 'content_analysis',
					parameters: { quality: 'basic', timeout: 10000 },
				},
				priority: 2,
				description: 'Reduce content analysis quality when response time exceeds 30s',
			},
			// High memory usage rule
			{
				name: 'high_memory_usage',
				condition: context => context.resourceUsage.memory > 0.85,
				action: {
					type: 'disable_capability',
					target: 'screenshot_capture',
					parameters: { reason: 'High memory usage' },
				},
				priority: 3,
				description: 'Disable screenshot capture when memory usage exceeds 85%',
			},
			// Database unavailable rule
			{
				name: 'database_unavailable',
				condition: context => context.serviceHealth.database === 'unhealthy',
				action: {
					type: 'cache_only',
					target: 'ranking_calculation',
					parameters: { cacheTtl: 3600 },
				},
				priority: 4,
				description: 'Use cached rankings when database is unavailable',
			},
			// External API failures rule
			{
				name: 'external_api_failures',
				condition: (context) =>
				{
					const apiErrors = context.activeErrors.filter(error =>
						error.classification.category === 'external_service');
					return apiErrors.length > 5;
				},
				action: {
					type: 'use_fallback',
					target: 'image_processing',
					parameters: { fallbackMethod: 'local_processing' },
				},
				priority: 5,
				description: 'Use local image processing when external APIs fail',
			},
			// Circuit breaker open rule
			{
				name: 'circuit_breaker_open',
				condition: context =>
					Object.values(context.circuitBreakerStates).some(state => state === 'open'),
				action: {
					type: 'skip_optional',
					target: 'performance_auditing',
					parameters: { reason: 'Circuit breaker open' },
				},
				priority: 6,
				description: 'Skip performance auditing when circuit breakers are open',
			},
		);
	}

	/**
     * Evaluate current system state and apply degradation rules
     */
	evaluateAndApplyDegradation(context: DegradationContextType): DegradationStatusType
	{
		const applicableRules = this.evaluateRules(context);
		const newStatus = this.calculateDegradationLevel(applicableRules);

		if (this.shouldUpdateStatus(newStatus))
		{
			this.applyDegradationActions(applicableRules);
			this.updateStatus(newStatus, applicableRules, context);
		}

		return this.currentStatus;
	}

	/**
     * Evaluate which rules should be applied
     */
	private evaluateRules(context: DegradationContextType): DegradationRuleType[]
	{
		const applicableRules: DegradationRuleType[] = [];

		for (const rule of this.degradationRules)
		{
			try
			{
				if (rule.condition(context))
				{
					applicableRules.push(rule);
				}
			}
			catch (error)
			{
				this.logger.error('Error evaluating degradation rule', {
					ruleName: rule.name,
					error: error.message,
				});
			}
		}

		// Sort by priority (lower number = higher priority)
		return applicableRules.sort((a, b) => a.priority - b.priority);
	}

	/**
     * Calculate degradation level based on applicable rules
     */
	private calculateDegradationLevel(rules: DegradationRuleType[]): DegradationLevel
	{
		if (rules.length === 0)
		{
			return DegradationLevel.NONE;
		}

		const essentialCapabilitiesAffected = rules.some((rule) =>
		{
			const capability = this.capabilities.get(rule.action.target);
			return capability?.essential === true;
		});

		const ruleCount = rules.length;

		if (essentialCapabilitiesAffected)
		{
			if (ruleCount >= 4)
			{
				return DegradationLevel.CRITICAL;
			}
			if (ruleCount >= 2)
			{
				return DegradationLevel.SEVERE;
			}

			return DegradationLevel.MODERATE;

		}

		if (ruleCount >= 3)
		{
			return DegradationLevel.MODERATE;
		}

		return DegradationLevel.MINIMAL;


	}

	/**
     * Check if status should be updated
     */
	private shouldUpdateStatus(newLevel: DegradationLevel): boolean
	{
		return newLevel !== this.currentStatus.level;
	}

	/**
     * Apply degradation actions
     */
	private applyDegradationActions(rules: DegradationRuleType[]): void
	{
		for (const rule of rules)
		{
			try
			{
				this.applyAction(rule.action);
			}
			catch (error)
			{
				this.logger.error('Error applying degradation action', {
					ruleName: rule.name,
					action: rule.action,
					error: error.message,
				});
			}
		}
	}

	/**
     * Apply individual degradation action
     */
	private applyAction(action: DegradationActionType): void
	{
		const capability = this.capabilities.get(action.target);
		if (!capability)
		{
			this.logger.warn('Unknown capability in degradation action', {
				target: action.target,
				action: action.type,
			});
			return;
		}

		switch (action.type)
		{
			case 'disable_capability':
				capability.currentStatus = 'unavailable';
				this.logger.info('Capability disabled due to degradation', {
					capability: action.target,
					reason: action.parameters.reason,
				});
				break;

			case 'reduce_quality':
				capability.currentStatus = 'degraded';
				this.logger.info('Capability quality reduced due to degradation', {
					capability: action.target,
					parameters: action.parameters,
				});
				break;

			case 'use_fallback':
				capability.currentStatus = 'degraded';
				this.logger.info('Using fallback for capability due to degradation', {
					capability: action.target,
					fallback: action.parameters.fallbackMethod,
				});
				break;

			case 'skip_optional':
				if (!capability.essential)
				{
					capability.currentStatus = 'unavailable';
					this.logger.info('Optional capability skipped due to degradation', {
						capability: action.target,
						reason: action.parameters.reason,
					});
				}
				break;

			case 'cache_only':
				capability.currentStatus = 'degraded';
				this.logger.info('Capability switched to cache-only mode due to degradation', {
					capability: action.target,
					cacheTtl: action.parameters.cacheTtl,
				});
				break;
		}
	}

	/**
     * Update degradation status
     */
	private updateStatus(
		level: DegradationLevel,
		rules: DegradationRuleType[],
		context: DegradationContextType
	): void
	{
		const previousStatus = { ...this.currentStatus };

		this.currentStatus = {
			level,
			activeRules: rules.map(rule => rule.name),
			disabledCapabilities: Array.from(this.capabilities.values())
				.filter(cap => cap.currentStatus === 'unavailable')
				.map(cap => cap.name),
			fallbacksActive: Array.from(this.capabilities.values())
				.filter(cap => cap.currentStatus === 'degraded')
				.map(cap => cap.name),
			lastUpdate: new Date(),
			reason: this.generateStatusReason(rules, context),
		};

		this.logger.info('Degradation status updated', {
			previousLevel: previousStatus.level,
			currentLevel: level,
			activeRules: this.currentStatus.activeRules,
			disabledCapabilities: this.currentStatus.disabledCapabilities,
		});

		this.notifyListeners();
	}

	/**
     * Generate human-readable reason for status change
     */
	private generateStatusReason(rules: DegradationRuleType[], context: DegradationContextType): string
	{
		if (rules.length === 0)
		{
			return 'System operating normally';
		}

		const reasons = rules.map(rule => rule.description);
		return `Degradation triggered by: ${reasons.join(', ')}`;
	}

	/**
     * Restore capability to normal operation
     */
	restoreCapability(capabilityName: string): boolean
	{
		const capability = this.capabilities.get(capabilityName);
		if (!capability)
		{
			return false;
		}

		capability.currentStatus = 'available';

		this.logger.info('Capability restored to normal operation', {
			capability: capabilityName,
		});

		return true;
	}

	/**
     * Restore all capabilities to normal operation
     */
	restoreAllCapabilities(): void
	{
		for (const capability of this.capabilities.values())
		{
			capability.currentStatus = 'available';
		}

		this.currentStatus = {
			level: DegradationLevel.NONE,
			activeRules: [],
			disabledCapabilities: [],
			fallbacksActive: [],
			lastUpdate: new Date(),
			reason: 'All capabilities restored',
		};

		this.logger.info('All capabilities restored to normal operation');
		this.notifyListeners();
	}

	/**
     * Check if capability is available
     */
	isCapabilityAvailable(capabilityName: string): boolean
	{
		const capability = this.capabilities.get(capabilityName);
		return capability?.currentStatus === 'available';
	}

	/**
     * Check if capability is degraded
     */
	isCapabilityDegraded(capabilityName: string): boolean
	{
		const capability = this.capabilities.get(capabilityName);
		return capability?.currentStatus === 'degraded';
	}

	/**
     * Get current degradation status
     */
	getStatus(): DegradationStatusType
	{
		return { ...this.currentStatus };
	}

	/**
     * Get capability status
     */
	getCapabilityStatus(capabilityName: string): ServiceCapabilityType | null
	{
		const capability = this.capabilities.get(capabilityName);
		return capability ? { ...capability } : null;
	}

	/**
     * Get all capabilities status
     */
	getAllCapabilities(): ServiceCapabilityType[]
	{
		return Array.from(this.capabilities.values()).map(cap => ({ ...cap }));
	}

	/**
     * Add custom degradation rule
     */
	addRule(rule: DegradationRuleType): void
	{
		this.degradationRules.push(rule);
		this.degradationRules.sort((a, b) => a.priority - b.priority);
	}

	/**
     * Remove degradation rule
     */
	removeRule(ruleName: string): boolean
	{
		const index = this.degradationRules.findIndex(rule => rule.name === ruleName);
		if (index > -1)
		{
			this.degradationRules.splice(index, 1);
			return true;
		}
		return false;
	}

	/**
     * Add status change listener
     */
	addListener(listener: (status: DegradationStatusType) => void): void
	{
		this.eventListeners.push(listener);
	}

	/**
     * Remove status change listener
     */
	removeListener(listener: (status: DegradationStatusType) => void): void
	{
		const index = this.eventListeners.indexOf(listener);
		if (index > -1)
		{
			this.eventListeners.splice(index, 1);
		}
	}

	/**
     * Notify all listeners of status change
     */
	private notifyListeners(): void
	{
		this.eventListeners.forEach((listener) =>
		{
			try
			{
				listener(this.currentStatus);
			}
			catch (error)
			{
				this.logger.error('Error in degradation status listener', {
					error: error.message,
				});
			}
		});
	}

	/**
     * Start monitoring for automatic degradation evaluation
     */
	startMonitoring(intervalMs: number = 30000): void
	{
		if (this.monitoringInterval)
		{
			clearInterval(this.monitoringInterval);
		}

		this.monitoringInterval = setInterval(() =>
		{
			// This would typically get real system metrics
			// For now, we'll just log that monitoring is active
			this.logger.debug('Degradation monitoring check', {
				currentLevel: this.currentStatus.level,
				activeRules: this.currentStatus.activeRules.length,
			});
		}, intervalMs);
	}

	/**
     * Stop monitoring
     */
	stopMonitoring(): void
	{
		if (this.monitoringInterval)
		{
			clearInterval(this.monitoringInterval);
			this.monitoringInterval = undefined;
		}
	}
}

export type {
	ServiceCapabilityType,
	DegradationRuleType,
	DegradationActionType,
	DegradationContextType,
	DegradationStatusType,
};
export { GracefulDegradationManager };
export default GracefulDegradationManager;
