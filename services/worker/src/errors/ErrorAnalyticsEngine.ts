import type { LoggerInstanceType, ErrorClassificationResultType, ErrorCategoryType, ErrorSeverityType } from '@shared';

/**
 * Error pattern
 */
type ErrorPatternType =
{
	id: string;
	name: string;
	pattern: RegExp | string;
	category: ErrorCategoryType;
	severity: ErrorSeverityType;
	frequency: number;
	firstSeen: Date;
	lastSeen: Date;
	occurrences: ErrorOccurrenceType[];
	confidence: number;
	description: string;
	suggestedActions: string[];
};

/**
 * Error occurrence
 */
type ErrorOccurrenceType =
{
	timestamp: Date;
	errorMessage: string;
	context: Record<string, any>;
	classification: ErrorClassificationResultType;
};

/**
 * Error trend
 */
type ErrorTrendType =
{
	timeWindow: string;
	startTime: Date;
	endTime: Date;
	totalErrors: number;
	errorRate: number;
	categories: Record<ErrorCategoryType, number>;
	severities: Record<ErrorSeverityType, number>;
	trend: 'increasing' | 'decreasing' | 'stable';
	changeRate: number; // percentage change
};

/**
 * Error correlation
 */
type ErrorCorrelationType =
{
	pattern1: string;
	pattern2: string;
	correlation: number; // -1 to 1
	coOccurrences: number;
	timeWindow: number;
	confidence: number;
};

/**
 * Anomaly detection result
 */
type AnomalyDetectionResultType =
{
	isAnomaly: boolean;
	anomalyScore: number;
	threshold: number;
	type: 'spike' | 'drop' | 'pattern_change' | 'new_error';
	description: string;
	affectedMetrics: string[];
	timestamp: Date;
};

/**
 * Prediction result
 */
type PredictionResultType =
{
	metric: string;
	predictedValue: number;
	confidence: number;
	timeHorizon: number; // milliseconds
	factors: Array<{
		name: string;
		impact: number;
	}>;
};

/**
 * Error analytics and pattern detection engine
 */
class ErrorAnalyticsEngine
{
	private readonly logger: LoggerInstanceType;
	private readonly errorOccurrences: ErrorOccurrenceType[];
	private readonly detectedPatterns: Map<string, ErrorPatternType>;
	private readonly errorTrends: ErrorTrendType[];
	private readonly correlations: ErrorCorrelationType[];
	private readonly anomalies: AnomalyDetectionResultType[];
	private readonly baselineMetrics: Map<string, number[]>;
	private analysisInterval?: NodeJS.Timeout;

	constructor(logger: LoggerInstanceType)
	{
		this.logger = logger;
		this.errorOccurrences = [];
		this.detectedPatterns = new Map();
		this.errorTrends = [];
		this.correlations = [];
		this.anomalies = [];
		this.baselineMetrics = new Map();
	}

	/**
     * Record error occurrence for analysis
     */
	recordError(
		error: Error,
		classification: ErrorClassificationResultType,
		context: Record<string, any> = {}
	): void
	{
		const occurrence: ErrorOccurrenceType = {
			timestamp: new Date(),
			errorMessage: error.message,
			context,
			classification,
		};

		this.errorOccurrences.push(occurrence);

		// Keep only recent occurrences (last 24 hours)
		const cutoffTime = new Date(Date.now() - 24 * 60 * 60 * 1000);
		const recentIndex = this.errorOccurrences.findIndex(occ => occ.timestamp >= cutoffTime);
		if (recentIndex > 0)
		{
			this.errorOccurrences.splice(0, recentIndex);
		}

		// Update patterns
		this.updatePatterns(occurrence);

		// Update baseline metrics
		this.updateBaselineMetrics();
	}

	/**
     * Update error patterns based on new occurrence
     */
	private updatePatterns(occurrence: ErrorOccurrenceType): void
	{
		// Check existing patterns
		let matchedPattern = false;

		for (const pattern of this.detectedPatterns.values())
		{
			if (this.matchesPattern(occurrence, pattern))
			{
				pattern.frequency++;
				pattern.lastSeen = occurrence.timestamp;
				pattern.occurrences.push(occurrence);

				// Keep only recent occurrences for each pattern
				const cutoffTime = new Date(Date.now() - 6 * 60 * 60 * 1000); // 6 hours
				pattern.occurrences = pattern.occurrences.filter(occ => occ.timestamp >= cutoffTime);

				matchedPattern = true;
				break;
			}
		}

		// Create new pattern if no match found and error is frequent enough
		if (!matchedPattern)
		{
			const similarOccurrences = this.findSimilarOccurrences(occurrence);
			if (similarOccurrences.length >= 3) // Minimum occurrences to create pattern
			{
				this.createNewPattern(occurrence, similarOccurrences);
			}
		}
	}

	/**
     * Check if occurrence matches existing pattern
     */
	private matchesPattern(occurrence: ErrorOccurrenceType, pattern: ErrorPatternType): boolean
	{
		if (pattern.pattern instanceof RegExp)
		{
			return pattern.pattern.test(occurrence.errorMessage);
		}

		return occurrence.errorMessage.includes(pattern.pattern as string);
	}

	/**
     * Find similar error occurrences
     */
	private findSimilarOccurrences(occurrence: ErrorOccurrenceType): ErrorOccurrenceType[]
	{
		const cutoffTime = new Date(Date.now() - 60 * 60 * 1000); // 1 hour

		return this.errorOccurrences.filter(occ =>
			occ.timestamp >= cutoffTime &&
            occ.classification.classification.category === occurrence.classification.classification.category &&
            this.calculateSimilarity(occ.errorMessage, occurrence.errorMessage) > 0.7);
	}

	/**
     * Calculate similarity between two error messages
     */
	private calculateSimilarity(message1: string, message2: string): number
	{
		// Simple similarity calculation based on common words
		const words1 = message1.toLowerCase().split(/\s+/);
		const words2 = message2.toLowerCase().split(/\s+/);

		const commonWords = words1.filter(word => words2.includes(word));
		const totalWords = new Set([...words1, ...words2]).size;

		return commonWords.length / totalWords;
	}

	/**
     * Create new error pattern
     */
	private createNewPattern(occurrence: ErrorOccurrenceType, similarOccurrences: ErrorOccurrenceType[]): void
	{
		const patternId = this.generatePatternId();
		const commonWords = this.extractCommonWords(similarOccurrences.map(occ => occ.errorMessage));

		const pattern: ErrorPatternType = {
			id: patternId,
			name: `Pattern ${patternId}`,
			pattern: commonWords.join('.*'),
			category: occurrence.classification.classification.category,
			severity: occurrence.classification.classification.severity,
			frequency: similarOccurrences.length,
			firstSeen: similarOccurrences[0].timestamp,
			lastSeen: occurrence.timestamp,
			occurrences: similarOccurrences,
			confidence: Math.min(0.9, similarOccurrences.length / 10),
			description: `Recurring error pattern: ${commonWords.join(' ')}`,
			suggestedActions: this.generateSuggestedActions(occurrence.classification),
		};

		this.detectedPatterns.set(patternId, pattern);

		this.logger.info('New error pattern detected', {
			patternId,
			category: pattern.category,
			frequency: pattern.frequency,
			confidence: pattern.confidence,
		});
	}

	/**
     * Extract common words from error messages
     */
	private extractCommonWords(messages: string[]): string[]
	{
		const wordCounts = new Map<string, number>();

		messages.forEach((message) =>
		{
			const words = message.toLowerCase().split(/\s+/);
			words.forEach((word) =>
			{
				if (word.length > 3) // Ignore short words
				{
					wordCounts.set(word, (wordCounts.get(word) || 0) + 1);
				}
			});
		});

		// Return words that appear in at least 50% of messages
		const threshold = Math.ceil(messages.length * 0.5);
		return Array.from(wordCounts.entries())
			.filter(([, count]) => count >= threshold)
			.sort(([, a], [, b]) => b - a)
			.slice(0, 5) // Top 5 words
			.map(([word]) => word);
	}

	/**
     * Generate suggested actions based on error classification
     */
	private generateSuggestedActions(classification: ErrorClassificationResultType): string[]
	{
		const actions: string[] = [];
		const { category, severity, recoverability } = classification.classification;

		switch (category)
		{
			case 'network':
				actions.push('Check network connectivity', 'Verify DNS resolution', 'Review firewall rules');
				break;
			case 'database':
				actions.push('Check database connection', 'Review query performance', 'Verify database health');
				break;
			case 'external_service':
				actions.push('Check service status', 'Review API limits', 'Implement circuit breaker');
				break;
			case 'resource':
				actions.push('Monitor resource usage', 'Scale resources', 'Optimize memory usage');
				break;
		}

		if (severity === 'critical')
		{
			actions.unshift('Immediate escalation required');
		}

		if (recoverability === 'recoverable')
		{
			actions.push('Implement retry logic', 'Add error recovery');
		}

		return actions;
	}

	/**
     * Analyze error trends
     */
	analyzeTrends(timeWindowMs: number = 3600000): ErrorTrendType
	{
		const endTime = new Date();
		const startTime = new Date(endTime.getTime() - timeWindowMs);

		const windowOccurrences = this.errorOccurrences.filter(occ =>
			occ.timestamp >= startTime && occ.timestamp <= endTime);

		const categories: Record<ErrorCategoryType, number> = {} as any;
		const severities: Record<ErrorSeverityType, number> = {} as any;

		windowOccurrences.forEach((occ) =>
		{
			const category = occ.classification.classification.category;
			const severity = occ.classification.classification.severity;

			categories[category] = (categories[category] || 0) + 1;
			severities[severity] = (severities[severity] || 0) + 1;
		});

		// Calculate trend
		const previousWindowStart = new Date(startTime.getTime() - timeWindowMs);
		const previousOccurrences = this.errorOccurrences.filter(occ =>
			occ.timestamp >= previousWindowStart && occ.timestamp < startTime);

		const currentCount = windowOccurrences.length;
		const previousCount = previousOccurrences.length;
		const changeRate = previousCount > 0 ? ((currentCount - previousCount) / previousCount) * 100 : 0;

		let trend: 'increasing' | 'decreasing' | 'stable';
		if (Math.abs(changeRate) < 10)
		{
			trend = 'stable';
		}
		else if (changeRate > 0)
		{
			trend = 'increasing';
		}
		else
		{
			trend = 'decreasing';
		}

		const trendResult: ErrorTrendType = {
			timeWindow: `${timeWindowMs / 1000}s`,
			startTime,
			endTime,
			totalErrors: currentCount,
			errorRate: currentCount / (timeWindowMs / 1000),
			categories,
			severities,
			trend,
			changeRate,
		};

		this.errorTrends.push(trendResult);

		// Keep only recent trends
		if (this.errorTrends.length > 100)
		{
			this.errorTrends.splice(0, this.errorTrends.length - 100);
		}

		return trendResult;
	}

	/**
     * Detect anomalies in error patterns
     */
	detectAnomalies(): AnomalyDetectionResultType[]
	{
		const newAnomalies: AnomalyDetectionResultType[] = [];

		// Detect error rate spikes
		const currentErrorRate = this.calculateCurrentErrorRate();
		const baselineErrorRate = this.getBaselineMetric('error_rate');

		if (baselineErrorRate && currentErrorRate > baselineErrorRate * 2)
		{
			newAnomalies.push({
				isAnomaly: true,
				anomalyScore: currentErrorRate / baselineErrorRate,
				threshold: baselineErrorRate * 2,
				type: 'spike',
				description: `Error rate spike detected: ${currentErrorRate.toFixed(2)} vs baseline ${baselineErrorRate.toFixed(2)}`,
				affectedMetrics: ['error_rate'],
				timestamp: new Date(),
			});
		}

		// Detect new error patterns
		const recentPatterns = Array.from(this.detectedPatterns.values())
			.filter(pattern => Date.now() - pattern.firstSeen.getTime() < 3600000); // Last hour

		recentPatterns.forEach((pattern) =>
		{
			if (pattern.frequency >= 5 && pattern.confidence > 0.7)
			{
				newAnomalies.push({
					isAnomaly: true,
					anomalyScore: pattern.confidence,
					threshold: 0.7,
					type: 'new_error',
					description: `New error pattern detected: ${pattern.description}`,
					affectedMetrics: ['error_patterns'],
					timestamp: pattern.firstSeen,
				});
			}
		});

		this.anomalies.push(...newAnomalies);

		// Keep only recent anomalies
		const cutoffTime = new Date(Date.now() - 24 * 60 * 60 * 1000);
		const recentIndex = this.anomalies.findIndex(anomaly => anomaly.timestamp >= cutoffTime);
		if (recentIndex > 0)
		{
			this.anomalies.splice(0, recentIndex);
		}

		return newAnomalies;
	}

	/**
     * Calculate current error rate
     */
	private calculateCurrentErrorRate(): number
	{
		const cutoffTime = new Date(Date.now() - 300000); // Last 5 minutes
		const recentErrors = this.errorOccurrences.filter(occ => occ.timestamp >= cutoffTime);
		return recentErrors.length / 300; // errors per second
	}

	/**
     * Update baseline metrics
     */
	private updateBaselineMetrics(): void
	{
		const errorRate = this.calculateCurrentErrorRate();

		const errorRateHistory = this.baselineMetrics.get('error_rate') || [];
		errorRateHistory.push(errorRate);

		// Keep only last 100 measurements
		if (errorRateHistory.length > 100)
		{
			errorRateHistory.shift();
		}

		this.baselineMetrics.set('error_rate', errorRateHistory);
	}

	/**
     * Get baseline metric value
     */
	private getBaselineMetric(metric: string): number | null
	{
		const history = this.baselineMetrics.get(metric);
		if (!history || history.length < 10)
		{
			return null;
		}

		// Calculate median as baseline
		const sorted = [...history].sort((a, b) => a - b);
		const mid = Math.floor(sorted.length / 2);
		return sorted.length % 2 === 0 ? (sorted[mid - 1] + sorted[mid]) / 2 : sorted[mid];
	}

	/**
     * Analyze error correlations
     */
	analyzeCorrelations(): ErrorCorrelationType[]
	{
		const patterns = Array.from(this.detectedPatterns.values());
		const newCorrelations: ErrorCorrelationType[] = [];

		for (let i = 0; i < patterns.length; i++)
		{
			for (let j = i + 1; j < patterns.length; j++)
			{
				const correlation = this.calculatePatternCorrelation(patterns[i], patterns[j]);
				if (Math.abs(correlation.correlation) > 0.5)
				{
					newCorrelations.push(correlation);
				}
			}
		}

		this.correlations.push(...newCorrelations);

		// Keep only recent correlations
		if (this.correlations.length > 1000)
		{
			this.correlations.splice(0, this.correlations.length - 1000);
		}

		return newCorrelations;
	}

	/**
     * Calculate correlation between two error patterns
     */
	private calculatePatternCorrelation(pattern1: ErrorPatternType, pattern2: ErrorPatternType): ErrorCorrelationType
	{
		const timeWindow = 3600000; // 1 hour
		const cutoffTime = new Date(Date.now() - timeWindow);

		const occurrences1 = pattern1.occurrences.filter(occ => occ.timestamp >= cutoffTime);
		const occurrences2 = pattern2.occurrences.filter(occ => occ.timestamp >= cutoffTime);

		// Count co-occurrences within 5-minute windows
		let coOccurrences = 0;
		const windowSize = 300000; // 5 minutes

		occurrences1.forEach((occ1) =>
		{
			const hasCoOccurrence = occurrences2.some(occ2 =>
				Math.abs(occ1.timestamp.getTime() - occ2.timestamp.getTime()) <= windowSize);
			if (hasCoOccurrence)
			{
				coOccurrences++;
			}
		});

		// Calculate correlation coefficient (simplified)
		const maxOccurrences = Math.max(occurrences1.length, occurrences2.length);
		const correlation = maxOccurrences > 0 ? (coOccurrences / maxOccurrences) : 0;

		return {
			pattern1: pattern1.id,
			pattern2: pattern2.id,
			correlation,
			coOccurrences,
			timeWindow,
			confidence: Math.min(0.9, coOccurrences / 10),
		};
	}

	/**
     * Predict future error trends
     */
	predictTrends(timeHorizonMs: number = 3600000): PredictionResultType[]
	{
		const predictions: PredictionResultType[] = [];

		// Predict error rate
		const errorRateHistory = this.baselineMetrics.get('error_rate') || [];
		if (errorRateHistory.length >= 10)
		{
			const trend = this.calculateTrend(errorRateHistory);
			const currentRate = errorRateHistory[errorRateHistory.length - 1];
			const predictedRate = currentRate + (trend * (timeHorizonMs / 300000)); // Extrapolate

			predictions.push({
				metric: 'error_rate',
				predictedValue: Math.max(0, predictedRate),
				confidence: Math.min(0.8, errorRateHistory.length / 50),
				timeHorizon: timeHorizonMs,
				factors: [
					{ name: 'historical_trend', impact: 0.7 },
					{ name: 'current_patterns', impact: 0.3 },
				],
			});
		}

		return predictions;
	}

	/**
     * Calculate trend from historical data
     */
	private calculateTrend(data: number[]): number
	{
		if (data.length < 2)
		{
			return 0;
		}

		// Simple linear regression slope
		const n = data.length;
		const sumX = (n * (n - 1)) / 2;
		const sumY = data.reduce((sum, val) => sum + val, 0);
		const sumXY = data.reduce((sum, val, index) => sum + (index * val), 0);
		const sumXX = (n * (n - 1) * (2 * n - 1)) / 6;

		return (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
	}

	/**
     * Get detected patterns
     */
	getDetectedPatterns(): ErrorPatternType[]
	{
		return Array.from(this.detectedPatterns.values())
			.sort((a, b) => b.frequency - a.frequency);
	}

	/**
     * Get error trends
     */
	getErrorTrends(limit: number = 50): ErrorTrendType[]
	{
		return this.errorTrends
			.sort((a, b) => b.endTime.getTime() - a.endTime.getTime())
			.slice(0, limit);
	}

	/**
     * Get anomalies
     */
	getAnomalies(limit: number = 50): AnomalyDetectionResultType[]
	{
		return this.anomalies
			.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
			.slice(0, limit);
	}

	/**
     * Get correlations
     */
	getCorrelations(): ErrorCorrelationType[]
	{
		return this.correlations
			.sort((a, b) => Math.abs(b.correlation) - Math.abs(a.correlation));
	}

	/**
     * Start continuous analysis
     */
	startAnalysis(intervalMs: number = 300000): void
	{
		if (this.analysisInterval)
		{
			clearInterval(this.analysisInterval);
		}

		this.analysisInterval = setInterval(() =>
		{
			try
			{
				this.analyzeTrends();
				this.detectAnomalies();
				this.analyzeCorrelations();

				this.logger.debug('Error analytics analysis completed', {
					patterns: this.detectedPatterns.size,
					trends: this.errorTrends.length,
					anomalies: this.anomalies.length,
					correlations: this.correlations.length,
				});
			}
			catch (error)
			{
				this.logger.error('Error during analytics analysis', {
					error: error.message,
				});
			}
		}, intervalMs);
	}

	/**
     * Stop continuous analysis
     */
	stopAnalysis(): void
	{
		if (this.analysisInterval)
		{
			clearInterval(this.analysisInterval);
			this.analysisInterval = undefined;
		}
	}

	/**
     * Generate unique pattern ID
     */
	private generatePatternId(): string
	{
		return `pattern-${Date.now()}-${Math.random().toString(36).substr(2, 6)}`;
	}
}

export type {
	ErrorPatternType,
	ErrorOccurrenceType,
	ErrorTrendType,
	ErrorCorrelationType,
	AnomalyDetectionResultType,
	PredictionResultType,
};
export default ErrorAnalyticsEngine;
