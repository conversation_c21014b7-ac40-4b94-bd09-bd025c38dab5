/**
 * Domain Locking System
 *
 * Provides distributed locking mechanism for multi-worker coordination
 * with comprehensive monitoring, debugging, and alerting capabilities.
 */

export { default as DomainLockManager } from './DomainLockManager';
export { default as LockMonitoringService } from './LockMonitoringService';

export type {
	LockAcquisitionResultType,
	LockRenewalResultType,
	LockReleaseResultType,
	LockStatusType,
	LockStatisticsType,
	LockConfigType,
	LockConflictResolutionType,
	LockInfoType,
	LockHealthCheckType,
	LockMonitoringEventType,
	LockAlertType,
	LockAlertRuleType,
} from './types';

export type { MonitoringConfigType } from './LockMonitoringService';
