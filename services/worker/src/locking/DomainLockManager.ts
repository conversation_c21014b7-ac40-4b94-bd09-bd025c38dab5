/**
 * Domain Lock Manager for Multi-Worker Coordination
 *
 * Provides distributed locking mechanism using Redis to ensure no domain
 * is processed by multiple workers simultaneously. Includes TTL management,
 * automatic renewal, conflict resolution, retry logic, monitoring, and
 * comprehensive error handling.
 */

import type { RedisClient } from '@shared/database/RedisClient';
import { logger as loggerFactory, AsyncUtils } from '@shared';
import { IdGenerator } from 'shared/src/utils/IdGenerator';

const logger = loggerFactory.getLogger('DomainLockManager');

type LockAcquisitionResultType =
{
	success: boolean;
	lockId?: string;
	conflictingOwner?: string;
	retryAfter?: number;
	error?: string;
};

type LockRenewalResultType =
{
	success: boolean;
	newTtl?: number;
	error?: string;
};

type LockReleaseResultType =
{
	success: boolean;
	wasOwner: boolean;
	error?: string;
};

type LockStatusType =
{
	isLocked: boolean;
	owner?: string;
	ttl?: number;
	lockId?: string;
	acquiredAt?: Date;
	expiresAt?: Date;
};

type LockStatisticsType =
{
	totalAcquisitions: number;
	successfulAcquisitions: number;
	failedAcquisitions: number;
	conflicts: number;
	renewals: number;
	releases: number;
	orphanedLocks: number;
	averageHoldTime: number;
	currentActiveLocks: number;
};

type LockConfigType =
{
	defaultTtl: number; // Default lock TTL in seconds
	maxTtl: number; // Maximum allowed TTL
	renewalThreshold: number; // Renew when TTL drops below this (seconds)
	maxRetries: number; // Maximum retry attempts for lock acquisition
	baseRetryDelay: number; // Base delay for exponential backoff (ms)
	maxRetryDelay: number; // Maximum retry delay (ms)
	lockPrefix: string; // Redis key prefix for locks
	statsPrefix: string; // Redis key prefix for statistics
	cleanupInterval: number; // Orphaned lock cleanup interval (ms)
	enableStatistics: boolean; // Enable statistics collection
	enableMonitoring: boolean; // Enable lock monitoring
};

type LockConflictResolutionType = 'fail' | 'wait' | 'force';

type LockInfoType =
{
	domain: string;
	lockId: string;
	workerId: string;
	ttl: number;
	acquiredAt: Date;
	expiresAt: Date;
};

class DomainLockManager
{
	private redisClient: RedisClient;
	private config: LockConfigType;
	private workerId: string;
	private statistics: LockStatisticsType;
	private activeLocks: Map<string, LockInfoType> = new Map();
	private renewalTimers: Map<string, NodeJS.Timeout> = new Map();
	private cleanupTimer?: NodeJS.Timeout;
	private isShuttingDown: boolean = false;

	constructor(redisClient: RedisClient, workerId: string, config?: Partial<LockConfigType>)
	{
		this.redisClient = redisClient;
		this.workerId = workerId;
		this.config = {
			defaultTtl: 300, // 5 minutes
			maxTtl: 3600, // 1 hour
			renewalThreshold: 60, // Renew when < 1 minute left
			maxRetries: 5,
			baseRetryDelay: 1000, // 1 second
			maxRetryDelay: 30000, // 30 seconds
			lockPrefix: 'lock:domain',
			statsPrefix: 'lock:stats',
			cleanupInterval: 60000, // 1 minute
			enableStatistics: true,
			enableMonitoring: true,
			...config,
		};

		this.statistics = {
			totalAcquisitions: 0,
			successfulAcquisitions: 0,
			failedAcquisitions: 0,
			conflicts: 0,
			renewals: 0,
			releases: 0,
			orphanedLocks: 0,
			averageHoldTime: 0,
			currentActiveLocks: 0,
		};

		this.startCleanupTimer();
		logger.info('DomainLockManager initialized', {
			workerId: this.workerId,
			config: this.config,
		});
	}

	/**
	 * Acquire lock for a domain with retry logic and exponential backoff
	 */
	async acquireLock(
		domain: string,
		ttl?: number,
		conflictResolution: LockConflictResolutionType = 'fail',
	): Promise<LockAcquisitionResultType>
	{
		const lockTtl = Math.min(ttl || this.config.defaultTtl, this.config.maxTtl);
		const lockId = this.generateLockId();
		const startTime = Date.now();

		logger.debug('Attempting to acquire lock', {
			domain,
			workerId: this.workerId,
			lockId,
			ttl: lockTtl,
			conflictResolution,
		});

		this.updateStatistics('totalAcquisitions', 1);

		for (let attempt = 0; attempt <= this.config.maxRetries; attempt++)
		{
			try
			{

				// eslint-disable-next-line no-await-in-loop
				const result = await this.attemptLockAcquisition(domain, lockId, lockTtl);

				if (result.success)
				{
					// Lock acquired successfully
					const lockInfo: LockInfoType =
					{
						domain,
						lockId,
						workerId: this.workerId,
						ttl: lockTtl,
						acquiredAt: new Date(),
						expiresAt: new Date(Date.now() + lockTtl * 1000),
					};

					this.activeLocks.set(domain, lockInfo);
					this.scheduleRenewal(domain, lockTtl);
					this.updateStatistics('successfulAcquisitions', 1);
					this.updateStatistics('currentActiveLocks', this.activeLocks.size);

					logger.info('Lock acquired successfully', {
						domain,
						workerId: this.workerId,
						lockId,
						ttl: lockTtl,
						attempt: attempt + 1,
						duration: Date.now() - startTime,
					});

					return {
						success: true,
						lockId,
					};
				}

				// Lock acquisition failed - handle conflict
				if (conflictResolution === 'fail')
				{
					this.updateStatistics('failedAcquisitions', 1);
					this.updateStatistics('conflicts', 1);

					logger.warn('Lock acquisition failed - conflict', {
						domain,
						workerId: this.workerId,
						conflictingOwner: result.conflictingOwner,
						attempt: attempt + 1,
					});

					return {
						success: false,
						conflictingOwner: result.conflictingOwner,
						error: 'Domain is locked by another worker',
					};
				}

				if (conflictResolution === 'force')
				{
					// Force acquire by breaking existing lock

					const forceResult = await this.forceAcquireLock(domain, lockId, lockTtl);
					if (forceResult.success)
					{
						const lockInfo: LockInfoType =
						{
							domain,
							lockId,
							workerId: this.workerId,
							ttl: lockTtl,
							acquiredAt: new Date(),
							expiresAt: new Date(Date.now() + lockTtl * 1000),
						};

						this.activeLocks.set(domain, lockInfo);
						this.scheduleRenewal(domain, lockTtl);
						this.updateStatistics('successfulAcquisitions', 1);
						this.updateStatistics('currentActiveLocks', this.activeLocks.size);

						logger.warn('Lock force acquired', {
							domain,
							workerId: this.workerId,
							lockId,
							previousOwner: result.conflictingOwner,
						});

						return ({
							success: true,
							lockId,
						});
					}
				}

				// Wait and retry (exponential backoff)
				if (attempt < this.config.maxRetries)
				{
					const delay = this.calculateRetryDelay(attempt);
					logger.debug('Lock acquisition retry scheduled', {
						domain,
						workerId: this.workerId,
						attempt: attempt + 1,
						delay,
					});


					// eslint-disable-next-line no-await-in-loop
					await this.sleep(delay);
				}
			}
			catch (error)
			{
				logger.error('Lock acquisition error', {
					domain,
					workerId: this.workerId,
					attempt: attempt + 1,
					error: error instanceof Error ? error.message : String(error),
				});

				if (attempt === this.config.maxRetries)
				{
					this.updateStatistics('failedAcquisitions', 1);
					return {
						success: false,
						error: `Lock acquisition failed after ${this.config.maxRetries + 1} attempts: ${error instanceof Error ? error.message : String(error)}`,
					};
				}

				// Wait before retry on error
				const delay = this.calculateRetryDelay(attempt);

				// eslint-disable-next-line no-await-in-loop
				await this.sleep(delay);
			}
		}

		this.updateStatistics('failedAcquisitions', 1);
		return {
			success: false,
			error: `Lock acquisition failed after ${this.config.maxRetries + 1} attempts`,
		};
	}

	/**
	 * Release lock for a domain
	 */
	async releaseLock(domain: string): Promise<LockReleaseResultType>
	{
		logger.debug('Attempting to release lock', {
			domain,
			workerId: this.workerId,
		});

		try
		{
			const lockInfo = this.activeLocks.get(domain);
			if (!lockInfo)
			{
				logger.warn('Attempted to release non-existent lock', {
					domain,
					workerId: this.workerId,
				});

				return ({
					success: false,
					wasOwner: false,
					error: 'Lock not found in active locks',
				});
			}

			const key = this.getLockKey(domain);
			const currentOwner = await this.redisClient.get<string>(key);

			if (currentOwner !== this.workerId)
			{
				logger.warn('Attempted to release lock owned by another worker', {
					domain,
					workerId: this.workerId,
					currentOwner,
				});

				return ({
					success: false,
					wasOwner: false,
					error: 'Lock is owned by another worker',
				});
			}

			// Release the lock
			await this.redisClient.del(key);

			// Clean up local state
			this.activeLocks.delete(domain);
			this.cancelRenewal(domain);

			// Update statistics
			this.updateStatistics('releases', 1);
			this.updateStatistics('currentActiveLocks', this.activeLocks.size);

			// Update average hold time
			const holdTime = Date.now() - lockInfo.acquiredAt.getTime();
			this.updateAverageHoldTime(holdTime);

			logger.info('Lock released successfully', {
				domain,
				workerId: this.workerId,
				lockId: lockInfo.lockId,
				holdTime,
			});

			return ({
				success: true,
				wasOwner: true,
			});
		}
		catch (error)
		{
			logger.error('Lock release error', {
				domain,
				workerId: this.workerId,
				error: error instanceof Error ? error.message : String(error),
			});

			return ({
				success: false,
				wasOwner: false,
				error: error instanceof Error ? error.message : String(error),
			});
		}
	}

	/**
	 * Renew lock TTL
	 */
	async renewLock(domain: string, newTtl?: number): Promise<LockRenewalResultType>
	{
		const lockInfo = this.activeLocks.get(domain);
		if (!lockInfo)
		{
			return ({
				success: false,
				error: 'Lock not found in active locks',
			});
		}

		const ttl = Math.min(newTtl || this.config.defaultTtl, this.config.maxTtl);

		logger.debug('Attempting to renew lock', {
			domain,
			workerId: this.workerId,
			lockId: lockInfo.lockId,
			newTtl: ttl,
		});

		try
		{
			const key = this.getLockKey(domain);
			const currentOwner = await this.redisClient.get<string>(key);

			if (currentOwner !== this.workerId)
			{
				logger.warn('Cannot renew lock owned by another worker', {
					domain,
					workerId: this.workerId,
					currentOwner,
				});

				// Remove from active locks since we don't own it
				this.activeLocks.delete(domain);
				this.cancelRenewal(domain);

				return ({
					success: false,
					error: 'Lock is owned by another worker',
				});
			}

			// Renew the lock
			await this.redisClient.expire(key, ttl);

			// Update local state
			lockInfo.ttl = ttl;
			lockInfo.expiresAt = new Date(Date.now() + ttl * 1000);

			// Reschedule renewal
			this.scheduleRenewal(domain, ttl);

			this.updateStatistics('renewals', 1);

			logger.debug('Lock renewed successfully', {
				domain,
				workerId: this.workerId,
				lockId: lockInfo.lockId,
				newTtl: ttl,
			});

			return ({
				success: true,
				newTtl: ttl,
			});
		}
		catch (error)
		{
			logger.error('Lock renewal error', {
				domain,
				workerId: this.workerId,
				error: error instanceof Error ? error.message : String(error),
			});

			return ({
				success: false,
				error: error instanceof Error ? error.message : String(error),
			});
		}
	}

	/**
	 * Check if domain is locked
	 */
	async isLocked(domain: string): Promise<boolean>
	{
		try
		{
			const key = this.getLockKey(domain);
			return await this.redisClient.exists(key);
		}
		catch (error)
		{
			logger.error('Lock status check error', {
				domain,
				error: error instanceof Error ? error.message : String(error),
			});
			return false;
		}
	}

	/**
	 * Get lock owner
	 */
	async getLockOwner(domain: string): Promise<string | null>
	{
		try
		{
			const key = this.getLockKey(domain);
			return await this.redisClient.get<string>(key);
		}
		catch (error)
		{
			logger.error('Lock owner check error', {
				domain,
				error: error instanceof Error ? error.message : String(error),
			});

			return null;
		}
	}

	/**
	 * Get detailed lock status
	 */
	async getLockStatus(domain: string): Promise<LockStatusType>
	{
		try
		{
			const key = this.getLockKey(domain);
			const owner = await this.redisClient.get<string>(key);

			if (!owner)
			{
				return ({ isLocked: false });
			}

			const ttl = await this.redisClient.ttl(key);
			const lockInfo = this.activeLocks.get(domain);

			return ({
				isLocked: true,
				owner,
				ttl: ttl > 0 ? ttl : undefined,
				lockId: lockInfo?.lockId,
				acquiredAt: lockInfo?.acquiredAt,
				expiresAt: ttl > 0 ? new Date(Date.now() + ttl * 1000) : undefined,
			});
		}
		catch (error)
		{
			logger.error('Lock status retrieval error', {
				domain,
				error: error instanceof Error ? error.message : String(error),
			});

			return ({ isLocked: false });
		}
	}

	/**
	 * Get current lock statistics
	 */
	getStatistics(): LockStatisticsType
	{
		return { ...this.statistics };
	}

	/**
	 * Get active locks for this worker
	 */
	getActiveLocks(): Array<LockInfoType & { domain: string }>
	{
		return Array.from(this.activeLocks.entries()).map(([domain, lockInfo]) => ({
			domain,
			...lockInfo,
		}));
	}

	/**
	 * Clean up orphaned locks
	 */
	async cleanupOrphanedLocks(): Promise<number>
	{
		logger.debug('Starting orphaned lock cleanup');

		try
		{
			const pattern = `${this.config.lockPrefix}:*`;
			const lockKeys = await this.redisClient.keys(pattern);
			let cleanedCount = 0;

			for (const key of lockKeys)
			{
				try
				{
					const ttl = await this.redisClient.ttl(key);

					// If TTL is -1, the key exists but has no expiration (orphaned)
					if (ttl === -1)
					{
						await this.redisClient.del(key);
						cleanedCount++;
						logger.debug('Cleaned orphaned lock', { key });
					}
				}
				catch (error)
				{
					logger.error('Error cleaning lock', {
						key,
						error: error instanceof Error ? error.message : String(error),
					});
				}
			}

			this.updateStatistics('orphanedLocks', cleanedCount);

			logger.info('Orphaned lock cleanup completed', {
				totalKeys: lockKeys.length,
				cleanedCount,
			});

			return cleanedCount;
		}
		catch (error)
		{
			logger.error('Orphaned lock cleanup error', {
				error: error instanceof Error ? error.message : String(error),
			});
			return 0;
		}
	}

	/**
	 * Perform health check on lock system
	 */
	async healthCheck(): Promise<{
		healthy: boolean;
		issues: string[];
		metrics: Record<string, number>;
	}>
	{
		const issues: string[] = [];
		const metrics: Record<string, number> = {};

		try
		{
			// Check Redis connectivity
			const pingResult = await this.redisClient.ping();
			if (pingResult !== 'PONG')
			{
				issues.push('Redis connectivity issue');
			}

			// Check active locks consistency
			let inconsistentLocks = 0;
			for (const [domain, lockInfo] of this.activeLocks)
			{
				// eslint-disable-next-line no-await-in-loop
				const owner = await this.getLockOwner(domain);
				if (owner !== lockInfo.lockId)
				{
					inconsistentLocks++;
					issues.push(`Inconsistent lock state for domain: ${domain}. Expected: ${lockInfo.lockId}, Actual: ${owner}`);
				}
				
				// Check for expired locks
				const lockAge = Date.now() - lockInfo.acquiredAt.getTime();
				if (lockAge > this.config.ttl)
				{
					issues.push(`Expired lock detected for domain: ${domain}. Age: ${lockAge}ms`);
				}
			}

			metrics.activeLocks = this.activeLocks.size;
			metrics.inconsistentLocks = inconsistentLocks;
			metrics.totalAcquisitions = this.statistics.totalAcquisitions;
			metrics.successRate = this.statistics.totalAcquisitions > 0
				? (this.statistics.successfulAcquisitions / this.statistics.totalAcquisitions) * 100
				: 100;
			metrics.conflictRate = this.statistics.totalAcquisitions > 0
				? (this.statistics.conflicts / this.statistics.totalAcquisitions) * 100
				: 0;

			// Check for high conflict rate
			if (metrics.conflictRate > 50)
			{
				issues.push('High lock conflict rate detected');
			}

			// Check for low success rate
			if (metrics.successRate < 90)
			{
				issues.push('Low lock acquisition success rate');
			}

			return ({
				healthy: issues.length === 0,
				issues,
				metrics,
			});
		}
		catch (error)
		{
			issues.push(`Health check error: ${error instanceof Error ? error.message : String(error)}`);
			return ({
				healthy: false,
				issues,
				metrics,
			});
		}
	}

	/**
	 * Shutdown lock manager and release all locks
	 */
	async shutdown(): Promise<void>
	{
		logger.info('Shutting down DomainLockManager', {
			workerId: this.workerId,
			activeLocks: this.activeLocks.size,
		});

		this.isShuttingDown = true;

		// Stop cleanup timer
		if (this.cleanupTimer)
		{
			clearInterval(this.cleanupTimer);
		}

		// Cancel all renewal timers
		for (const timer of this.renewalTimers.values())
		{
			clearTimeout(timer);
		}
		this.renewalTimers.clear();

		// Release all active locks
		const releasePromises = Array.from(this.activeLocks.keys()).map(domain =>
			this.releaseLock(domain).catch(error =>
				logger.error('Error releasing lock during shutdown', {
					domain,
					error: error instanceof Error ? error.message : String(error),
				})));

		await Promise.all(releasePromises);

		logger.info('DomainLockManager shutdown completed', {
			workerId: this.workerId,
		});
	}

	// ===== PRIVATE METHODS =====

	private async attemptLockAcquisition(
		domain: string,
		lockId: string,
		ttl: number,
	): Promise<{ success: boolean; conflictingOwner?: string }>
	{
		const key = this.getLockKey(domain);

		// Try to acquire lock atomically
		const acquired = await this.redisClient.setnx(key, lockId);

		if (acquired)
		{
			// Set TTL
			await this.redisClient.expire(key, ttl);
			return ({ success: true });
		}

		// Lock exists, get current owner
		const currentOwner = await this.redisClient.get<string>(key);
		return ({
			success: false,
			conflictingOwner: currentOwner || 'unknown',
		});
	}

	private async forceAcquireLock(
		domain: string,
		lockId: string,
		ttl: number,
	): Promise<{ success: boolean }>
	{
		const key = this.getLockKey(domain);

		try
		{
			// Force set the lock
			await this.redisClient.set(key, lockId, ttl);
			return ({ success: true });
		}
		catch (error)
		{
			logger.error('Force lock acquisition failed', {
				domain,
				lockId,
				error: error instanceof Error ? error.message : String(error),
			});
			return ({ success: false });
		}
	}

	private scheduleRenewal(domain: string, ttl: number): void
	{
		// Cancel existing renewal timer
		this.cancelRenewal(domain);

		// Schedule renewal when TTL drops below threshold
		const renewalTime = Math.max(
			(ttl - this.config.renewalThreshold) * 1000,
			1000, // Minimum 1 second
		);

		const timer = setTimeout(async () =>
		{
			if (this.isShuttingDown) return;

			logger.debug('Auto-renewing lock', { domain, workerId: this.workerId });

			const result = await this.renewLock(domain);
			if (!result.success)
			{
				logger.warn('Auto-renewal failed', {
					domain,
					workerId: this.workerId,
					error: result.error,
				});

				// Remove from active locks if renewal failed
				this.activeLocks.delete(domain);
			}
		}, renewalTime);

		this.renewalTimers.set(domain, timer);
	}

	private cancelRenewal(domain: string): void
	{
		const timer = this.renewalTimers.get(domain);
		if (timer)
		{
			clearTimeout(timer);
			this.renewalTimers.delete(domain);
		}
	}

	private calculateRetryDelay(attempt: number): number
	{
		const exponentialDelay = this.config.baseRetryDelay * 2**attempt;
		const jitter = Math.random() * 0.1 * exponentialDelay; // 10% jitter
		return Math.min(exponentialDelay + jitter, this.config.maxRetryDelay);
	}

	private generateLockId(): string
	{
		return `${this.workerId}-${IdGenerator.randomString()}`;
	}

	private getLockKey(domain: string): string
	{
		return `${this.config.lockPrefix}:${domain}`;
	}

	private updateStatistics(key: keyof LockStatisticsType, value: number): void
	{
		if (!this.config.enableStatistics) return;

		if (key === 'currentActiveLocks')
		{
			this.statistics[key] = value;
		}
		else
		{
			(this.statistics[key] as number) += value;
		}
	}

	private updateAverageHoldTime(holdTime: number): void
	{
		if (!this.config.enableStatistics) return;

		const totalReleases = this.statistics.releases;
		if (totalReleases === 1)
		{
			this.statistics.averageHoldTime = holdTime;
		}
		else
		{
			this.statistics.averageHoldTime = (
				(this.statistics.averageHoldTime * (totalReleases - 1) + holdTime) / totalReleases
			);
		}
	}

	private startCleanupTimer(): void
	{
		if (!this.config.enableMonitoring) return;

		this.cleanupTimer = setInterval(async () =>
		{
			if (this.isShuttingDown) return;

			try
			{
				await this.cleanupOrphanedLocks();
			}
			catch (error)
			{
				logger.error('Cleanup timer error', {
					error: error instanceof Error ? error.message : String(error),
				});
			}
		}, this.config.cleanupInterval);
	}

	private sleep(ms: number): Promise<void>
	{
		// eslint-disable-next-line no-promise-executor-return
		return AsyncUtils.sleep(ms);
	}
}

export type {
	LockAcquisitionResultType,
	LockRenewalResultType,
	LockReleaseResultType,
	LockStatusType,
	LockStatisticsType,
	LockConfigType,
	LockConflictResolutionType,
	LockInfoType,
};

export default DomainLockManager;
