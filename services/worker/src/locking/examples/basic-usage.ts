/**
 * Basic Usage Example for Domain Locking System
 *
 * This example demonstrates how to use the DomainLockManager
 * and LockMonitoringService for multi-worker coordination.
 */

import { logger as loggerFactory } from '@shared/utils/Logger';
import { RedisClient } from '@shared/database/RedisClient';
import { DomainLockManager, LockMonitoringService } from '..';

const logger = loggerFactory.getLogger('LockingExample');

async function basicLockingExample(): Promise<void>
{
	// Initialize Redis client
	const redisClient = new RedisClient({
		url: process.env.REDIS_URL || 'redis://localhost:6379',
		database: 0,
		maxRetries: 3,
	});

	await redisClient.connect();

	// Initialize lock manager for this worker
	const workerId = `worker-${process.pid}`;
	const lockManager = new DomainLockManager(redisClient, workerId, {
		defaultTtl: 300, // 5 minutes
		maxRetries: 5,
		baseRetryDelay: 1000, // 1 second
		enableStatistics: true,
		enableMonitoring: true,
	});

	// Initialize monitoring service
	const monitoringService = new LockMonitoringService(
		redisClient,
		lockManager,
		workerId,
		{
			enableEventLogging: true,
			enableAlerting: true,
			enableMetricsCollection: true,
		},
	);

	try
	{
		const domain = 'example.com';

		logger.info('=== Basic Lock Acquisition ===');

		// 1. Acquire lock for a domain
		const acquireResult = await lockManager.acquireLock(domain);
		if (acquireResult.success)
		{
			logger.info(`Successfully acquired lock for ${domain}`, {
				lockId: acquireResult.lockId,
			});

			// Log the acquisition event
			await monitoringService.logEvent({
				type: 'acquisition',
				domain,
				workerId,
				lockId: acquireResult.lockId,
				details: { ttl: 300 },
			});
		}
		else
		{
			logger.error(`Failed to acquire lock for ${domain}`, {
				error: acquireResult.error,
				conflictingOwner: acquireResult.conflictingOwner,
			});
			return;
		}

		// 2. Check lock status
		const lockStatus = await lockManager.getLockStatus(domain);
		logger.info('Lock status:', lockStatus);

		// 3. Renew the lock
		logger.info('=== Lock Renewal ===');
		const renewResult = await lockManager.renewLock(domain, 600); // Extend to 10 minutes
		if (renewResult.success)
		{
			logger.info(`Successfully renewed lock for ${domain}`, {
				newTtl: renewResult.newTtl,
			});
		}

		// 4. Simulate some work
		logger.info('=== Simulating Work ===');
		await new Promise(resolve => setTimeout(resolve, 2000)); // 2 seconds

		// 5. Release the lock
		logger.info('=== Lock Release ===');
		const releaseResult = await lockManager.releaseLock(domain);
		if (releaseResult.success)
		{
			logger.info(`Successfully released lock for ${domain}`);

			// Log the release event
			await monitoringService.logEvent({
				type: 'release',
				domain,
				workerId,
				details: { wasOwner: releaseResult.wasOwner },
			});
		}

		// 6. Show statistics
		logger.info('=== Lock Statistics ===');
		const statistics = lockManager.getStatistics();
		logger.info('Lock statistics:', statistics);

		// 7. Health check
		logger.info('=== Health Check ===');
		const health = await lockManager.healthCheck();
		logger.info('Lock system health:', health);

		// 8. Generate debug report
		logger.info('=== Debug Report ===');
		const debugReport = await monitoringService.generateDebugReport(domain);
		logger.info('Debug report generated:', {
			workerId: debugReport.workerId,
			activeLocks: debugReport.activeLocks.length,
			recentEvents: debugReport.recentEvents.length,
			systemHealth: debugReport.systemHealth.healthy,
		});
	}
	catch (error)
	{
		logger.error('Example failed:', error);
	}
	finally
	{
		// Cleanup
		await lockManager.shutdown();
		await monitoringService.shutdown();
		await redisClient.disconnect();
	}
}

async function conflictResolutionExample(): Promise<void>
{
	logger.info('\n=== Conflict Resolution Example ===');

	// Initialize Redis client
	const redisClient = new RedisClient({
		url: process.env.REDIS_URL || 'redis://localhost:6379',
		database: 0,
		maxRetries: 3,
	});

	await redisClient.connect();

	// Create two workers
	const worker1 = new DomainLockManager(redisClient, 'worker-1');
	const worker2 = new DomainLockManager(redisClient, 'worker-2');

	try
	{
		const domain = 'conflict-test.com';

		// Worker 1 acquires lock
		const result1 = await worker1.acquireLock(domain);
		logger.info('Worker 1 acquire result:', result1);

		// Worker 2 tries to acquire (should fail)
		const result2 = await worker2.acquireLock(domain, undefined, 'fail');
		logger.info('Worker 2 acquire result (fail strategy):', result2);

		// Worker 2 tries with wait strategy (will retry and eventually fail)
		const result3 = await worker2.acquireLock(domain, undefined, 'wait');
		logger.info('Worker 2 acquire result (wait strategy):', result3);

		// Worker 2 tries with force strategy (will succeed)
		const result4 = await worker2.acquireLock(domain, undefined, 'force');
		logger.info('Worker 2 acquire result (force strategy):', result4);

		// Check who owns the lock now
		const owner = await worker2.getLockOwner(domain);
		logger.info(`Lock owner is now: ${owner}`);

		// Cleanup
		await worker2.releaseLock(domain);
	}
	catch (error)
	{
		logger.error('Conflict resolution example failed:', error);
	}
	finally
	{
		await worker1.shutdown();
		await worker2.shutdown();
		await redisClient.disconnect();
	}
}

async function monitoringExample(): Promise<void>
{
	logger.info('\n=== Monitoring and Alerting Example ===');

	const redisClient = new RedisClient({
		url: process.env.REDIS_URL || 'redis://localhost:6379',
		database: 0,
		maxRetries: 3,
	});

	await redisClient.connect();

	const lockManager = new DomainLockManager(redisClient, 'monitoring-worker');
	const monitoringService = new LockMonitoringService(
		redisClient,
		lockManager,
		'monitoring-worker',
	);

	try
	{
		// Add custom alert rule
		monitoringService.addAlertRule({
			id: 'custom-rule',
			name: 'Custom High Activity Rule',
			condition: 'activeLocks > 5',
			threshold: 5,
			enabled: true,
			severity: 'medium',
			cooldown: 60, // 1 minute
		});

		// Simulate some lock activity to trigger monitoring
		const domains = ['site1.com', 'site2.com', 'site3.com'];

		for (const domain of domains)
		{
			await lockManager.acquireLock(domain);
			await monitoringService.logEvent({
				type: 'acquisition',
				domain,
				workerId: 'monitoring-worker',
			});
		}

		// Get detailed metrics
		const metrics = await monitoringService.getDetailedMetrics();
		logger.info('Detailed metrics:', {
			activeLocks: metrics.activeLocks.length,
			statistics: metrics.statistics,
			systemHealth: metrics.systemHealth.healthy,
		});

		// Get recent events
		const recentEvents = await monitoringService.getRecentEvents(10);
		logger.info(`Recent events: ${recentEvents.length} events found`);

		// Check for active alerts
		const activeAlerts = monitoringService.getActiveAlerts();
		logger.info(`Active alerts: ${activeAlerts.length} alerts`);

		// Cleanup locks
		for (const domain of domains)
		{
			await lockManager.releaseLock(domain);
		}
	}
	catch (error)
	{
		logger.error('Monitoring example failed:', error);
	}
	finally
	{
		await lockManager.shutdown();
		await monitoringService.shutdown();
		await redisClient.disconnect();
	}
}

// Run examples if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`)
{
	async function runExamples(): Promise<void>
	{
		try
		{
			await basicLockingExample();
			await conflictResolutionExample();
			await monitoringExample();
		}
		catch (error)
		{
			logger.error('Examples failed:', error);
			process.exit(1);
		}
	}

	runExamples();
}

export { basicLockingExample, conflictResolutionExample, monitoringExample };
