/**
 * Integration Tests for Domain Locking System
 *
 * Tests the complete locking system including DomainLockManager,
 * LockMonitoringService, and their interaction with Redis.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import type { RedisClient } from '@shared/database/RedisClient';
import { DomainLockManager } from '../DomainLockManager';
import { LockMonitoringService } from '../LockMonitoringService';

// Mock Redis client with more realistic behavior
class MockRedisClient
{
	private storage = new Map<string, { value: string; ttl?: number; expiresAt?: number }>();
	private connected = true;

	async setnx(key: string, value: string): Promise<boolean>
	{
		if (!this.connected) throw new Error('Redis not connected');

		if (this.storage.has(key))
		{
			const item = this.storage.get(key)!;
			// Check if expired
			if (item.expiresAt && Date.now() > item.expiresAt)
			{
				this.storage.delete(key);
			}
			else
			{
				return false; // Key exists
			}
		}

		this.storage.set(key, { value });
		return true;
	}

	async expire(key: string, ttl: number): Promise<boolean>
	{
		if (!this.connected) throw new Error('Redis not connected');

		const item = this.storage.get(key);
		if (item)
		{
			item.ttl = ttl;
			item.expiresAt = Date.now() + ttl * 1000;
			return true;
		}
		return false;
	}

	async get<T>(key: string): Promise<T | null>
	{
		if (!this.connected) throw new Error('Redis not connected');

		const item = this.storage.get(key);
		if (!item) return null;

		// Check if expired
		if (item.expiresAt && Date.now() > item.expiresAt)
		{
			this.storage.delete(key);
			return null;
		}

		try
		{
			return JSON.parse(item.value) as T;
		}
		catch
		{
			return item.value as T;
		}
	}

	async set<T>(key: string, value: T, ttl?: number): Promise<string>
	{
		if (!this.connected) throw new Error('Redis not connected');

		const item = {
			value: typeof value === 'string' ? value : JSON.stringify(value),
			ttl,
			expiresAt: ttl ? Date.now() + ttl * 1000 : undefined,
		};

		this.storage.set(key, item);
		return 'OK';
	}

	async del(keys: string | string[]): Promise<number>
	{
		if (!this.connected) throw new Error('Redis not connected');

		const keyArray = Array.isArray(keys) ? keys : [keys];
		let deleted = 0;

		for (const key of keyArray)
		{
			if (this.storage.delete(key))
			{
				deleted++;
			}
		}

		return deleted;
	}

	async exists(key: string): Promise<boolean>
	{
		if (!this.connected) throw new Error('Redis not connected');

		const item = this.storage.get(key);
		if (!item) return false;

		// Check if expired
		if (item.expiresAt && Date.now() > item.expiresAt)
		{
			this.storage.delete(key);
			return false;
		}

		return true;
	}

	async ttl(key: string): Promise<number>
	{
		if (!this.connected) throw new Error('Redis not connected');

		const item = this.storage.get(key);
		if (!item) return -2; // Key doesn't exist

		if (!item.expiresAt) return -1; // No expiration

		const remaining = Math.ceil((item.expiresAt - Date.now()) / 1000);
		return remaining > 0 ? remaining : -2;
	}

	async keys(pattern: string): Promise<string[]>
	{
		if (!this.connected) throw new Error('Redis not connected');

		const regex = new RegExp(pattern.replace(/\*/g, '.*'));
		return Array.from(this.storage.keys()).filter(key => regex.test(key));
	}

	async ping(): Promise<string>
	{
		return this.connected ? 'PONG' : 'ERROR';
	}

	async setex<T>(key: string, ttl: number, value: T): Promise<void>
	{
		await this.set(key, value, ttl);
	}

	getClient(): any
	{
		return {
			zAdd: vi.fn(),
			zRevRange: vi.fn().mockResolvedValue([]),
		};
	}

	getConnectionStats(): Record<string, unknown>
	{
		return {
			connected: this.connected,
			url: 'redis://localhost:6379',
			database: 0,
		};
	}

	// Test utilities
	setConnected(connected: boolean): void
	{
		this.connected = connected;
	}

	clear(): void
	{
		this.storage.clear();
	}

	getStorageSize(): number
	{
		return this.storage.size;
	}

	// Simulate time passing for TTL testing
	simulateTimePass(seconds: number): void
	{
		const now = Date.now();
		for (const [key, item] of this.storage.entries())
		{
			if (item.expiresAt && item.expiresAt <= now + seconds * 1000)
			{
				this.storage.delete(key);
			}
		}
	}
}

describe('Domain Locking System Integration', () =>
{
	let redisClient: MockRedisClient;
	let lockManager1: DomainLockManager;
	let lockManager2: DomainLockManager;
	let monitoringService: LockMonitoringService;

	const worker1Id = 'worker-1';
	const worker2Id = 'worker-2';
	const testDomain = 'example.com';

	beforeEach(() =>
	{
		redisClient = new MockRedisClient();

		lockManager1 = new DomainLockManager(
			redisClient as unknown as RedisClient,
			worker1Id,
			{
				defaultTtl: 300,
				maxRetries: 3,
				baseRetryDelay: 10, // Faster for tests
				cleanupInterval: 1000,
				enableStatistics: true,
				enableMonitoring: false, // Disable auto-cleanup for tests
			},
		);

		lockManager2 = new DomainLockManager(
			redisClient as unknown as RedisClient,
			worker2Id,
			{
				defaultTtl: 300,
				maxRetries: 3,
				baseRetryDelay: 10,
				cleanupInterval: 1000,
				enableStatistics: true,
				enableMonitoring: false,
			},
		);

		monitoringService = new LockMonitoringService(
			redisClient as unknown as RedisClient,
			lockManager1,
			worker1Id,
			{
				eventRetentionDays: 7,
				healthCheckInterval: 1000,
				metricsCollectionInterval: 500,
				enableEventLogging: true,
				enableAlerting: true,
				enableMetricsCollection: false, // Disable for tests
			},
		);
	});

	afterEach(async () =>
	{
		await lockManager1.shutdown();
		await lockManager2.shutdown();
		await monitoringService.shutdown();
		redisClient.clear();
	});

	describe('Multi-Worker Coordination', () =>
	{
		it('should prevent multiple workers from acquiring the same lock', async () =>
		{
			// Both workers try to acquire the same domain lock simultaneously
			const [result1, result2] = await Promise.all([
				lockManager1.acquireLock(testDomain),
				lockManager2.acquireLock(testDomain),
			]);

			// Only one should succeed
			const successCount = [result1, result2].filter(r => r.success).length;
			expect(successCount).toBe(1);

			// The other should fail with conflict
			const failedResult = result1.success ? result2 : result1;
			expect(failedResult.success).toBe(false);
			expect(failedResult.conflictingOwner).toBeDefined();
		});

		it('should allow different workers to acquire locks for different domains', async () =>
		{
			const domain1 = 'example1.com';
			const domain2 = 'example2.com';

			const [result1, result2] = await Promise.all([
				lockManager1.acquireLock(domain1),
				lockManager2.acquireLock(domain2),
			]);

			expect(result1.success).toBe(true);
			expect(result2.success).toBe(true);

			// Verify both locks are active
			expect(await lockManager1.isLocked(domain1)).toBe(true);
			expect(await lockManager2.isLocked(domain2)).toBe(true);
		});

		it('should handle lock release and re-acquisition by different workers', async () =>
		{
			// Worker 1 acquires lock
			const acquireResult = await lockManager1.acquireLock(testDomain);
			expect(acquireResult.success).toBe(true);

			// Worker 2 cannot acquire
			const conflictResult = await lockManager2.acquireLock(testDomain);
			expect(conflictResult.success).toBe(false);

			// Worker 1 releases lock
			const releaseResult = await lockManager1.releaseLock(testDomain);
			expect(releaseResult.success).toBe(true);

			// Worker 2 can now acquire
			const reacquireResult = await lockManager2.acquireLock(testDomain);
			expect(reacquireResult.success).toBe(true);
		});
	});

	describe('Lock Expiration and Renewal', () =>
	{
		it('should automatically expire locks after TTL', async () =>
		{
			// Acquire lock with short TTL
			const result = await lockManager1.acquireLock(testDomain, 1); // 1 second
			expect(result.success).toBe(true);

			// Verify lock exists
			expect(await lockManager1.isLocked(testDomain)).toBe(true);

			// Simulate time passing
			redisClient.simulateTimePass(2); // 2 seconds

			// Lock should be expired
			expect(await lockManager1.isLocked(testDomain)).toBe(false);

			// Another worker should be able to acquire
			const newResult = await lockManager2.acquireLock(testDomain);
			expect(newResult.success).toBe(true);
		});

		it('should successfully renew locks before expiration', async () =>
		{
			// Acquire lock
			const result = await lockManager1.acquireLock(testDomain, 60);
			expect(result.success).toBe(true);

			// Renew lock
			const renewResult = await lockManager1.renewLock(testDomain, 120);
			expect(renewResult.success).toBe(true);
			expect(renewResult.newTtl).toBe(120);

			// Verify lock is still active
			expect(await lockManager1.isLocked(testDomain)).toBe(true);
		});

		it('should prevent renewal by non-owner', async () =>
		{
			// Worker 1 acquires lock
			const result = await lockManager1.acquireLock(testDomain);
			expect(result.success).toBe(true);

			// Worker 2 tries to renew (should fail because it doesn't have the lock in its active locks)
			const renewResult = await lockManager2.renewLock(testDomain);
			expect(renewResult.success).toBe(false);
			// The error could be either "not found in active locks" or "owned by another worker"
			// depending on the implementation path taken
			expect(
				renewResult.error?.includes('not found in active locks') ||
				renewResult.error?.includes('owned by another worker')
			).toBe(true);
		});
	});

	describe('Error Handling and Recovery', () =>
	{
		it('should handle Redis connection failures gracefully', async () =>
		{
			// Disconnect Redis
			redisClient.setConnected(false);

			// Lock operations should fail gracefully
			const result = await lockManager1.acquireLock(testDomain);
			expect(result.success).toBe(false);
			expect(result.error).toContain('Redis not connected');

			// Reconnect Redis
			redisClient.setConnected(true);

			// Operations should work again
			const retryResult = await lockManager1.acquireLock(testDomain);
			expect(retryResult.success).toBe(true);
		});

		it('should retry lock acquisition with exponential backoff', async () =>
		{
			// Worker 1 acquires lock
			await lockManager1.acquireLock(testDomain);

			const startTime = Date.now();

			// Worker 2 tries to acquire with wait strategy
			const result = await lockManager2.acquireLock(testDomain, undefined, 'wait');

			const duration = Date.now() - startTime;

			// Should have failed after retries
			expect(result.success).toBe(false);
			// Should have taken some time due to retries
			expect(duration).toBeGreaterThan(50); // At least some retry delay
		});

		it('should force acquire locks when specified', async () =>
		{
			// Worker 1 acquires lock
			const result1 = await lockManager1.acquireLock(testDomain);
			expect(result1.success).toBe(true);

			// Worker 2 force acquires
			const result2 = await lockManager2.acquireLock(testDomain, undefined, 'force');
			expect(result2.success).toBe(true);

			// Worker 2 should now own the lock
			const owner = await lockManager2.getLockOwner(testDomain);
			expect(owner).toBe(worker2Id);
		});
	});

	describe('Orphaned Lock Cleanup', () =>
	{
		it('should clean up orphaned locks without TTL', async () =>
		{
			// Manually create orphaned lock (no TTL)
			await redisClient.set('lock:domain:orphaned.com', 'dead-worker');

			// Run cleanup
			const cleanedCount = await lockManager1.cleanupOrphanedLocks();

			expect(cleanedCount).toBe(1);
			expect(await redisClient.exists('lock:domain:orphaned.com')).toBe(false);
		});

		it('should not clean up valid locks with TTL', async () =>
		{
			// Create valid lock
			await lockManager1.acquireLock(testDomain);

			// Run cleanup
			const cleanedCount = await lockManager1.cleanupOrphanedLocks();

			expect(cleanedCount).toBe(0);
			expect(await lockManager1.isLocked(testDomain)).toBe(true);
		});
	});

	describe('Statistics and Monitoring', () =>
	{
		it('should track lock statistics across operations', async () =>
		{
			// Perform various lock operations
			await lockManager1.acquireLock('domain1.com');
			await lockManager1.acquireLock('domain2.com');
			await lockManager2.acquireLock('domain1.com'); // Should fail
			await lockManager1.releaseLock('domain1.com');
			await lockManager2.acquireLock('domain1.com'); // Should succeed

			const stats = lockManager1.getStatistics();

			expect(stats.totalAcquisitions).toBe(2);
			expect(stats.successfulAcquisitions).toBe(2);
			expect(stats.releases).toBe(1);

			const stats2 = lockManager2.getStatistics();
			expect(stats2.totalAcquisitions).toBe(2);
			expect(stats2.successfulAcquisitions).toBe(1);
			expect(stats2.failedAcquisitions).toBe(1);
			expect(stats2.conflicts).toBe(1);
		});

		it('should log monitoring events', async () =>
		{
			// Perform lock operation
			await lockManager1.acquireLock(testDomain);

			// Log event
			await monitoringService.logEvent({
				type: 'acquisition',
				domain: testDomain,
				workerId: worker1Id,
				lockId: 'test-lock-id',
			});

			// Verify event was logged (would check Redis in real implementation)
			// For this test, we just verify no errors were thrown
			expect(true).toBe(true);
		});

		it('should generate comprehensive debug reports', async () =>
		{
			// Set up some lock activity
			await lockManager1.acquireLock(testDomain);
			await lockManager1.acquireLock('another.com');

			const report = await monitoringService.generateDebugReport(testDomain);

			expect(report.workerId).toBe(worker1Id);
			expect(report.timestamp).toBeInstanceOf(Date);
			expect(report.lockStatus).toBeDefined();
			expect(report.statistics.currentActiveLocks).toBe(2);
			expect(report.activeLocks).toHaveLength(2);
		});
	});

	describe('Health Checks', () =>
	{
		it('should report healthy status when system is working', async () =>
		{
			const health = await lockManager1.healthCheck();

			expect(health.healthy).toBe(true);
			expect(health.issues).toHaveLength(0);
			expect(health.metrics).toBeDefined();
		});

		it('should detect Redis connectivity issues', async () =>
		{
			// Disconnect Redis
			redisClient.setConnected(false);

			const health = await lockManager1.healthCheck();

			expect(health.healthy).toBe(false);
			expect(health.issues.some(issue => issue.includes('Redis connectivity'))).toBe(true);
		});

		it('should detect inconsistent lock states', async () =>
		{
			// Acquire lock
			await lockManager1.acquireLock(testDomain);

			// Manually corrupt the lock in Redis
			await redisClient.set(`lock:domain:${  testDomain}`, 'corrupted-worker');

			const health = await lockManager1.healthCheck();

			expect(health.healthy).toBe(false);
			expect(health.issues.some(issue => issue.includes('Inconsistent lock state'))).toBe(true);
		});
	});

	describe('Concurrent Operations', () =>
	{
		it('should handle high concurrency without race conditions', async () =>
		{
			const domains = Array.from({ length: 10 }, (_, i) => `domain${i}.com`);
			const workers = [lockManager1, lockManager2];

			// Each worker tries to acquire all domains simultaneously
			const promises = workers.flatMap(worker =>
				domains.map(domain => worker.acquireLock(domain)));

			const results = await Promise.all(promises);

			// Each domain should be acquired by exactly one worker
			for (const domain of domains)
			{
				const domainResults = results.filter((_, index) =>
					domains[index % domains.length] === domain);
				const successCount = domainResults.filter(r => r.success).length;
				expect(successCount).toBe(1);
			}
		});

		it('should handle rapid acquire/release cycles', async () =>
		{
			const cycles = 5;

			for (let i = 0; i < cycles; i++)
			{
				// Acquire
				const acquireResult = await lockManager1.acquireLock(testDomain);
				expect(acquireResult.success).toBe(true);

				// Release
				const releaseResult = await lockManager1.releaseLock(testDomain);
				expect(releaseResult.success).toBe(true);
			}

			// Verify final state is clean
			expect(await lockManager1.isLocked(testDomain)).toBe(false);
			expect(lockManager1.getActiveLocks()).toHaveLength(0);
		});
	});

	describe('System Shutdown', () =>
	{
		it('should release all locks on shutdown', async () =>
		{
			// Acquire multiple locks
			await lockManager1.acquireLock('domain1.com');
			await lockManager1.acquireLock('domain2.com');
			await lockManager1.acquireLock('domain3.com');

			expect(lockManager1.getActiveLocks()).toHaveLength(3);

			// Shutdown
			await lockManager1.shutdown();

			// All locks should be released
			expect(await redisClient.exists('lock:domain:domain1.com')).toBe(false);
			expect(await redisClient.exists('lock:domain:domain2.com')).toBe(false);
			expect(await redisClient.exists('lock:domain:domain3.com')).toBe(false);
		});

		it('should handle shutdown gracefully even with Redis errors', async () =>
		{
			// Acquire lock
			await lockManager1.acquireLock(testDomain);

			// Disconnect Redis before shutdown
			redisClient.setConnected(false);

			// Shutdown should not throw
			await expect(lockManager1.shutdown()).resolves.toBeUndefined();
		});
	});
});
