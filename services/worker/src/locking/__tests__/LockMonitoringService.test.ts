/**
 * Tests for LockMonitoringService
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import type { RedisClient } from '@shared/database/RedisClient';
import type { LockStatisticsType, LockHealthCheckType } from '../types';
import LockMonitoringService from '../LockMonitoringService';
import DomainLockManager from '../DomainLockManager';

// Mock Redis client
const mockRedisClient =
{
	setex: vi.fn(),
	get: vi.fn(),
	set: vi.fn(),
	expire: vi.fn(),
	getClient: vi.fn(() => ({
		zAdd: vi.fn(),
		zRevRange: vi.fn(),
	})),
	getConnectionStats: vi.fn(() => ({
		connected: true,
		url: 'redis://localhost:6379',
		database: 0,
	})),
} as unknown as RedisClient;

// Mock DomainLockManager
const mockLockManager =
{
	healthCheck: vi.fn(),
	getStatistics: vi.fn(),
	getActiveLocks: vi.fn(),
	getLockStatus: vi.fn(),
} as unknown as DomainLockManager;

describe('LockMonitoringService', () =>
{
	let monitoringService: LockMonitoringService;
	const workerId = 'test-worker-1';

	beforeEach(() =>
	{
		vi.clearAllMocks();
		monitoringService = new LockMonitoringService(
			mockRedisClient,
			mockLockManager,
			workerId,
			{
				eventRetentionDays: 7,
				alertRetentionDays: 30,
				healthCheckInterval: 60000,
				metricsCollectionInterval: 30000,
				enableEventLogging: true,
				enableAlerting: true,
				enableMetricsCollection: true,
			},
		);
	});

	afterEach(async () =>
	{
		await monitoringService.shutdown();
	});

	describe('Event Logging', () =>
	{
		it('should log lock events to Redis', async () =>
		{
			const mockZAdd = vi.fn();
			vi.mocked(mockRedisClient.getClient).mockReturnValue({
				zAdd: mockZAdd,
			} as any);

			await monitoringService.logEvent({
				type: 'acquisition',
				domain: 'example.com',
				workerId: 'worker-1',
				lockId: 'lock-123',
				details: { ttl: 300 },
			});

			expect(mockRedisClient.setex).toHaveBeenCalled();
			expect(mockZAdd).toHaveBeenCalled();
		});

		it('should retrieve recent events with filters', async () =>
		{
			const mockZRevRange = vi.fn().mockResolvedValue(['event-key-1', 'event-key-2']);
			vi.mocked(mockRedisClient.getClient).mockReturnValue({
				zRevRange: mockZRevRange,
			} as any);

			const mockEvents = [
				{
					type: 'acquisition',
					domain: 'example.com',
					workerId: 'worker-1',
					timestamp: new Date(),
				},
				{
					type: 'release',
					domain: 'test.com',
					workerId: 'worker-1',
					timestamp: new Date(),
				},
			];

			vi.mocked(mockRedisClient.get)
				.mockResolvedValueOnce(mockEvents[0])
				.mockResolvedValueOnce(mockEvents[1]);

			const events = await monitoringService.getRecentEvents(10, 'acquisition');

			expect(events).toHaveLength(1);
			expect(events[0].type).toBe('acquisition');
			expect(events[0].domain).toBe('example.com');
		});
	});

	describe('Alert Rules', () =>
	{
		it('should add and retrieve alert rules', () =>
		{
			const rule = {
				id: 'test-rule',
				name: 'Test Rule',
				condition: 'conflictRate > 50',
				threshold: 50,
				enabled: true,
				severity: 'high' as const,
				cooldown: 300,
			};

			monitoringService.addAlertRule(rule);

			const rules = monitoringService.getAlertRules();
			const addedRule = rules.find(r => r.id === 'test-rule');

			expect(addedRule).toBeDefined();
			expect(addedRule?.name).toBe('Test Rule');
		});

		it('should remove alert rules', () =>
		{
			const rule = {
				id: 'test-rule',
				name: 'Test Rule',
				condition: 'conflictRate > 50',
				threshold: 50,
				enabled: true,
				severity: 'high' as const,
				cooldown: 300,
			};

			monitoringService.addAlertRule(rule);
			const removed = monitoringService.removeAlertRule('test-rule');

			expect(removed).toBe(true);

			const rules = monitoringService.getAlertRules();
			const removedRule = rules.find(r => r.id === 'test-rule');

			expect(removedRule).toBeUndefined();
		});
	});

	describe('Alert Management', () =>
	{
		it('should acknowledge alerts', async () =>
		{
			// First trigger an alert by setting up conditions
			const mockStatistics: LockStatistics = {
				totalAcquisitions: 100,
				successfulAcquisitions: 40,
				failedAcquisitions: 60,
				conflicts: 60,
				renewals: 10,
				releases: 40,
				orphanedLocks: 0,
				averageHoldTime: 5000,
				currentActiveLocks: 5,
			};

			const mockHealth: LockHealthCheck = {
				healthy: false,
				issues: ['High conflict rate'],
				metrics: { conflictRate: 60 },
			};

			vi.mocked(mockLockManager.healthCheck).mockResolvedValue(mockHealth);
			vi.mocked(mockLockManager.getStatistics).mockReturnValue(mockStatistics);

			// Manually trigger health check to generate alert
			await (monitoringService as any).performHealthCheck();

			const activeAlerts = monitoringService.getActiveAlerts();
			expect(activeAlerts.length).toBeGreaterThan(0);

			const alertId = activeAlerts[0].id;
			const acknowledged = await monitoringService.acknowledgeAlert(alertId);

			expect(acknowledged).toBe(true);
			expect(mockRedisClient.set).toHaveBeenCalled();
		});

		it('should resolve alerts', async () =>
		{
			// Set up similar conditions as above
			const mockStatistics: LockStatistics = {
				totalAcquisitions: 100,
				successfulAcquisitions: 40,
				failedAcquisitions: 60,
				conflicts: 60,
				renewals: 10,
				releases: 40,
				orphanedLocks: 0,
				averageHoldTime: 5000,
				currentActiveLocks: 5,
			};

			const mockHealth: LockHealthCheck = {
				healthy: false,
				issues: ['High conflict rate'],
				metrics: { conflictRate: 60 },
			};

			vi.mocked(mockLockManager.healthCheck).mockResolvedValue(mockHealth);
			vi.mocked(mockLockManager.getStatistics).mockReturnValue(mockStatistics);

			await (monitoringService as any).performHealthCheck();

			const activeAlerts = monitoringService.getActiveAlerts();
			expect(activeAlerts.length).toBeGreaterThan(0);

			const alertId = activeAlerts[0].id;
			const resolved = await monitoringService.resolveAlert(alertId);

			expect(resolved).toBe(true);
			expect(mockRedisClient.set).toHaveBeenCalled();
		});
	});

	describe('Health Monitoring', () =>
	{
		it('should get health status from lock manager', async () =>
		{
			const mockHealth: LockHealthCheck = {
				healthy: true,
				issues: [],
				metrics: { activeLocks: 5, successRate: 95 },
			};

			vi.mocked(mockLockManager.healthCheck).mockResolvedValue(mockHealth);

			const health = await monitoringService.getHealthStatus();

			expect(health.healthy).toBe(true);
			expect(health.issues).toHaveLength(0);
			expect(health.metrics.activeLocks).toBe(5);
		});

		it('should collect detailed metrics', async () =>
		{
			const mockStatistics: LockStatistics = {
				totalAcquisitions: 100,
				successfulAcquisitions: 95,
				failedAcquisitions: 5,
				conflicts: 3,
				renewals: 20,
				releases: 90,
				orphanedLocks: 1,
				averageHoldTime: 5000,
				currentActiveLocks: 10,
			};

			const mockActiveLocks = [
				{
					domain: 'example.com',
					lockId: 'lock-1',
					workerId: 'worker-1',
					ttl: 300,
					acquiredAt: new Date(),
					expiresAt: new Date(Date.now() + 300000),
				},
			];

			const mockHealth: LockHealthCheck = {
				healthy: true,
				issues: [],
				metrics: { activeLocks: 10 },
			};

			vi.mocked(mockLockManager.getStatistics).mockReturnValue(mockStatistics);
			vi.mocked(mockLockManager.getActiveLocks).mockReturnValue(mockActiveLocks);
			vi.mocked(mockLockManager.healthCheck).mockResolvedValue(mockHealth);

			const mockZRevRange = vi.fn().mockResolvedValue([]);
			vi.mocked(mockRedisClient.getClient).mockReturnValue({
				zRevRange: mockZRevRange,
			} as any);

			const metrics = await monitoringService.getDetailedMetrics();

			expect(metrics.statistics.totalAcquisitions).toBe(100);
			expect(metrics.activeLocks).toHaveLength(1);
			expect(metrics.systemHealth.healthy).toBe(true);
		});
	});

	describe('Debug Report Generation', () =>
	{
		it('should generate comprehensive debug report', async () =>
		{
			const mockStatistics: LockStatistics = {
				totalAcquisitions: 50,
				successfulAcquisitions: 45,
				failedAcquisitions: 5,
				conflicts: 2,
				renewals: 10,
				releases: 40,
				orphanedLocks: 0,
				averageHoldTime: 3000,
				currentActiveLocks: 5,
			};

			const mockActiveLocks = [
				{
					domain: 'example.com',
					lockId: 'lock-1',
					workerId: 'worker-1',
					ttl: 300,
					acquiredAt: new Date(),
					expiresAt: new Date(Date.now() + 300000),
				},
			];

			const mockHealth: LockHealthCheck = {
				healthy: true,
				issues: [],
				metrics: { activeLocks: 5 },
			};

			const mockLockStatus = {
				isLocked: true,
				owner: 'worker-1',
				ttl: 250,
			};

			vi.mocked(mockLockManager.getStatistics).mockReturnValue(mockStatistics);
			vi.mocked(mockLockManager.getActiveLocks).mockReturnValue(mockActiveLocks);
			vi.mocked(mockLockManager.healthCheck).mockResolvedValue(mockHealth);
			vi.mocked(mockLockManager.getLockStatus).mockResolvedValue(mockLockStatus);

			const mockZRevRange = vi.fn().mockResolvedValue([]);
			vi.mocked(mockRedisClient.getClient).mockReturnValue({
				zRevRange: mockZRevRange,
			} as any);

			const report = await monitoringService.generateDebugReport('example.com');

			expect(report.workerId).toBe(workerId);
			expect(report.timestamp).toBeInstanceOf(Date);
			expect(report.lockStatus).toBeDefined();
			expect(report.statistics.totalAcquisitions).toBe(50);
			expect(report.activeLocks).toHaveLength(1);
			expect(report.systemHealth.healthy).toBe(true);
		});
	});

	describe('Alert Rule Evaluation', () =>
	{
		it('should evaluate high conflict rate rule', async () =>
		{
			const mockStatistics: LockStatistics = {
				totalAcquisitions: 100,
				successfulAcquisitions: 40,
				failedAcquisitions: 60,
				conflicts: 60, // 60% conflict rate
				renewals: 10,
				releases: 40,
				orphanedLocks: 0,
				averageHoldTime: 5000,
				currentActiveLocks: 5,
			};

			const mockHealth: LockHealthCheck = {
				healthy: false,
				issues: ['High conflict rate'],
				metrics: { conflictRate: 60 },
			};

			vi.mocked(mockLockManager.healthCheck).mockResolvedValue(mockHealth);
			vi.mocked(mockLockManager.getStatistics).mockReturnValue(mockStatistics);

			// Trigger health check
			await (monitoringService as any).performHealthCheck();

			const activeAlerts = monitoringService.getActiveAlerts();
			const conflictAlert = activeAlerts.find(alert =>
				alert.ruleId === 'high-conflict-rate');

			expect(conflictAlert).toBeDefined();
			expect(conflictAlert?.severity).toBe('high');
		});

		it('should evaluate low success rate rule', async () =>
		{
			const mockStatistics: LockStatistics = {
				totalAcquisitions: 100,
				successfulAcquisitions: 80, // 80% success rate (below 90% threshold)
				failedAcquisitions: 20,
				conflicts: 15,
				renewals: 10,
				releases: 75,
				orphanedLocks: 0,
				averageHoldTime: 5000,
				currentActiveLocks: 5,
			};

			const mockHealth: LockHealthCheck = {
				healthy: false,
				issues: ['Low success rate'],
				metrics: { successRate: 80 },
			};

			vi.mocked(mockLockManager.healthCheck).mockResolvedValue(mockHealth);
			vi.mocked(mockLockManager.getStatistics).mockReturnValue(mockStatistics);

			await (monitoringService as any).performHealthCheck();

			const activeAlerts = monitoringService.getActiveAlerts();
			const successAlert = activeAlerts.find(alert =>
				alert.ruleId === 'low-success-rate');

			expect(successAlert).toBeDefined();
			expect(successAlert?.severity).toBe('medium');
		});

		it('should respect alert cooldown periods', async () =>
		{
			const mockStatistics: LockStatistics = {
				totalAcquisitions: 100,
				successfulAcquisitions: 40,
				failedAcquisitions: 60,
				conflicts: 60,
				renewals: 10,
				releases: 40,
				orphanedLocks: 0,
				averageHoldTime: 5000,
				currentActiveLocks: 5,
			};

			const mockHealth: LockHealthCheck = {
				healthy: false,
				issues: ['High conflict rate'],
				metrics: { conflictRate: 60 },
			};

			vi.mocked(mockLockManager.healthCheck).mockResolvedValue(mockHealth);
			vi.mocked(mockLockManager.getStatistics).mockReturnValue(mockStatistics);

			// First health check should trigger alert
			await (monitoringService as any).performHealthCheck();
			const firstAlerts = monitoringService.getActiveAlerts();
			const firstAlertCount = firstAlerts.length;

			// Immediate second health check should not trigger new alert (cooldown)
			await (monitoringService as any).performHealthCheck();
			const secondAlerts = monitoringService.getActiveAlerts();

			expect(secondAlerts.length).toBe(firstAlertCount); // No new alerts
		});
	});

	describe('Error Handling', () =>
	{
		it('should handle Redis errors gracefully during event logging', async () =>
		{
			vi.mocked(mockRedisClient.setex).mockRejectedValue(new Error('Redis error'));

			// Should not throw
			await expect(monitoringService.logEvent({
				type: 'acquisition',
				domain: 'example.com',
				workerId: 'worker-1',
			})).resolves.toBeUndefined();
		});

		it('should handle lock manager errors during health checks', async () =>
		{
			vi.mocked(mockLockManager.healthCheck).mockRejectedValue(new Error('Lock manager error'));

			// Should not throw
			await expect((monitoringService as any).performHealthCheck()).resolves.toBeUndefined();
		});
	});

	describe('Shutdown', () =>
	{
		it('should resolve all active alerts on shutdown', async () =>
		{
			// Set up conditions to generate alerts
			const mockStatistics: LockStatistics = {
				totalAcquisitions: 100,
				successfulAcquisitions: 40,
				failedAcquisitions: 60,
				conflicts: 60,
				renewals: 10,
				releases: 40,
				orphanedLocks: 0,
				averageHoldTime: 5000,
				currentActiveLocks: 5,
			};

			const mockHealth: LockHealthCheck = {
				healthy: false,
				issues: ['High conflict rate'],
				metrics: { conflictRate: 60 },
			};

			vi.mocked(mockLockManager.healthCheck).mockResolvedValue(mockHealth);
			vi.mocked(mockLockManager.getStatistics).mockReturnValue(mockStatistics);

			await (monitoringService as any).performHealthCheck();

			const alertsBeforeShutdown = monitoringService.getActiveAlerts();
			expect(alertsBeforeShutdown.length).toBeGreaterThan(0);

			await monitoringService.shutdown();

			// All alerts should be resolved
			const alertsAfterShutdown = monitoringService.getActiveAlerts();
			expect(alertsAfterShutdown.length).toBe(0);
		});
	});
});
