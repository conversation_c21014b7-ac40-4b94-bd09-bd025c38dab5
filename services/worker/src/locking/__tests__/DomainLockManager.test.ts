/**
 * Domain Lock Manager Tests
 *
 * Tests for distributed domain locking functionality.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { DomainLockManager } from '../DomainLockManager';
import type { LockResultType, LockStatusType } from '../types';

// Mock Redis client
const mockRedisClient = {
	set: vi.fn(),
	get: vi.fn(),
	del: vi.fn(),
	exists: vi.fn(),
	expire: vi.fn(),
	ttl: vi.fn(),
	eval: vi.fn(),
	keys: vi.fn(),
	mget: vi.fn(),
	pipeline: vi.fn(),
	quit: vi.fn(),
	ping: vi.fn(),
};

// Mock pipeline
const mockPipeline = {
	set: vi.fn().mockReturnThis(),
	expire: vi.fn().mockReturnThis(),
	exec: vi.fn(),
};

vi.mock('redis', () => ({
	createClient: vi.fn(() => mockRedisClient),
}));

describe('DomainLockManager', () =>
{
	let lockManager: DomainLockManager;
	const workerId = 'test-worker-1';
	const redisUrl = 'redis://localhost:6379';

	beforeEach(async () =>
	{
		vi.clearAllMocks();

		// Setup default mock responses
		mockRedisClient.ping.mockResolvedValue('PONG');
		mockRedisClient.pipeline.mockReturnValue(mockPipeline);
		mockPipeline.exec.mockResolvedValue([['OK'], [1]]);

		lockManager = new DomainLockManager(redisUrl, workerId);
		await lockManager.initialize();
	});

	afterEach(async () =>
	{
		await lockManager.shutdown();
		vi.restoreAllMocks();
	});

	describe('Initialization and Shutdown', () =>
	{
		it('should initialize successfully with valid Redis connection', async () =>
		{
			expect(mockRedisClient.ping).toHaveBeenCalled();
		});

		it('should handle Redis connection failures during initialization', async () =>
		{
			mockRedisClient.ping.mockRejectedValue(new Error('Connection failed'));

			const failingLockManager = new DomainLockManager(redisUrl, workerId);

			await expect(failingLockManager.initialize()).rejects.toThrow('Connection failed');
		});

		it('should shutdown gracefully', async () =>
		{
			await lockManager.shutdown();

			expect(mockRedisClient.quit).toHaveBeenCalled();
		});
	});

	describe('Lock Acquisition', () =>
	{
		it('should acquire lock successfully for available domain', async () =>
		{
			const domain = 'example.com';
			const ttl = 300000; // 5 minutes

			// Mock successful lock acquisition
			mockRedisClient.eval.mockResolvedValue('OK');

			const result: LockResultType = await lockManager.acquireLock(domain, ttl);

			expect(result.success).toBe(true);
			expect(result.lockId).toBeDefined();
			expect(result.ttl).toBe(ttl);
			expect(result.acquiredAt).toBeInstanceOf(Date);

			// Verify Redis script was called with correct parameters
			expect(mockRedisClient.eval).toHaveBeenCalledWith(
				expect.any(String), // Lua script
				1, // Number of keys
				`domain_lock:${domain}`, // Lock key
				expect.any(String), // Lock value (workerId:lockId)
				ttl.toString(), // TTL in milliseconds
			);
		});

		it('should fail to acquire lock when domain is already locked', async () =>
		{
			const domain = 'locked.com';
			const ttl = 300000;

			// Mock lock acquisition failure (domain already locked)
			mockRedisClient.eval.mockResolvedValue(null);

			const result = await lockManager.acquireLock(domain, ttl);

			expect(result.success).toBe(false);
			expect(result.error).toContain('already locked');
			expect(result.lockId).toBeUndefined();
		});

		it('should handle Redis errors during lock acquisition', async () =>
		{
			const domain = 'error.com';
			const ttl = 300000;

			mockRedisClient.eval.mockRejectedValue(new Error('Redis error'));

			const result = await lockManager.acquireLock(domain, ttl);

			expect(result.success).toBe(false);
			expect(result.error).toContain('Redis error');
		});

		it('should validate domain name format', async () =>
		{
			const invalidDomain = 'invalid..domain..com';
			const ttl = 300000;

			const result = await lockManager.acquireLock(invalidDomain, ttl);

			expect(result.success).toBe(false);
			expect(result.error).toContain('Invalid domain');
		});

		it('should validate TTL range', async () =>
		{
			const domain = 'example.com';

			// Test minimum TTL
			const tooShortResult = await lockManager.acquireLock(domain, 100);
			expect(tooShortResult.success).toBe(false);
			expect(tooShortResult.error).toContain('TTL too short');

			// Test maximum TTL
			const tooLongResult = await lockManager.acquireLock(domain, 24 * 60 * 60 * 1000 + 1);
			expect(tooLongResult.success).toBe(false);
			expect(tooLongResult.error).toContain('TTL too long');
		});
	});

	describe('Lock Release', () =>
	{
		it('should release lock successfully when worker owns it', async () =>
		{
			const domain = 'example.com';
			const lockId = 'test-lock-123';

			// Mock successful lock release
			mockRedisClient.eval.mockResolvedValue(1);

			const result = await lockManager.releaseLock(domain, lockId);

			expect(result.success).toBe(true);
			expect(result.wasOwner).toBe(true);

			// Verify Redis script was called
			expect(mockRedisClient.eval).toHaveBeenCalledWith(
				expect.any(String), // Lua script
				1, // Number of keys
				`domain_lock:${domain}`, // Lock key
				`${workerId}:${lockId}`, // Expected lock value
			);
		});

		it('should fail to release lock when worker does not own it', async () =>
		{
			const domain = 'example.com';
			const lockId = 'wrong-lock-123';

			// Mock lock release failure (not owner)
			mockRedisClient.eval.mockResolvedValue(0);

			const result = await lockManager.releaseLock(domain, lockId);

			expect(result.success).toBe(false);
			expect(result.wasOwner).toBe(false);
			expect(result.error).toContain('not the owner');
		});

		it('should handle Redis errors during lock release', async () =>
		{
			const domain = 'example.com';
			const lockId = 'test-lock-123';

			mockRedisClient.eval.mockRejectedValue(new Error('Redis error'));

			const result = await lockManager.releaseLock(domain, lockId);

			expect(result.success).toBe(false);
			expect(result.error).toContain('Redis error');
		});
	});

	describe('Lock Renewal', () =>
	{
		it('should renew lock successfully when worker owns it', async () =>
		{
			const domain = 'example.com';
			const lockId = 'test-lock-123';
			const newTtl = 600000; // 10 minutes

			// Mock successful lock renewal
			mockRedisClient.eval.mockResolvedValue(1);

			const result = await lockManager.renewLock(domain, lockId, newTtl);

			expect(result.success).toBe(true);
			expect(result.newTtl).toBe(newTtl);
			expect(result.renewedAt).toBeInstanceOf(Date);

			// Verify Redis script was called
			expect(mockRedisClient.eval).toHaveBeenCalledWith(
				expect.any(String), // Lua script
				1, // Number of keys
				`domain_lock:${domain}`, // Lock key
				`${workerId}:${lockId}`, // Expected lock value
				newTtl.toString(), // New TTL
			);
		});

		it('should fail to renew lock when worker does not own it', async () =>
		{
			const domain = 'example.com';
			const lockId = 'wrong-lock-123';
			const newTtl = 600000;

			// Mock lock renewal failure (not owner)
			mockRedisClient.eval.mockResolvedValue(0);

			const result = await lockManager.renewLock(domain, lockId, newTtl);

			expect(result.success).toBe(false);
			expect(result.error).toContain('not the owner');
		});

		it('should validate new TTL during renewal', async () =>
		{
			const domain = 'example.com';
			const lockId = 'test-lock-123';

			const result = await lockManager.renewLock(domain, lockId, 100);

			expect(result.success).toBe(false);
			expect(result.error).toContain('TTL too short');
		});
	});

	describe('Lock Status Checking', () =>
	{
		it('should return correct status for locked domain', async () =>
		{
			const domain = 'example.com';
			const lockValue = `${workerId}:test-lock-123`;
			const ttl = 250; // seconds

			mockRedisClient.get.mockResolvedValue(lockValue);
			mockRedisClient.ttl.mockResolvedValue(ttl);

			const status: LockStatusType = await lockManager.getLockStatus(domain);

			expect(status.isLocked).toBe(true);
			expect(status.owner).toBe(workerId);
			expect(status.lockId).toBe('test-lock-123');
			expect(status.ttl).toBe(ttl * 1000); // Convert to milliseconds
			expect(status.isOwnedByThisWorker).toBe(true);
		});

		it('should return correct status for unlocked domain', async () =>
		{
			const domain = 'unlocked.com';

			mockRedisClient.get.mockResolvedValue(null);

			const status = await lockManager.getLockStatus(domain);

			expect(status.isLocked).toBe(false);
			expect(status.owner).toBeNull();
			expect(status.lockId).toBeNull();
			expect(status.ttl).toBe(0);
			expect(status.isOwnedByThisWorker).toBe(false);
		});

		it('should handle locks owned by other workers', async () =>
		{
			const domain = 'example.com';
			const lockValue = 'other-worker:other-lock-456';
			const ttl = 180;

			mockRedisClient.get.mockResolvedValue(lockValue);
			mockRedisClient.ttl.mockResolvedValue(ttl);

			const status = await lockManager.getLockStatus(domain);

			expect(status.isLocked).toBe(true);
			expect(status.owner).toBe('other-worker');
			expect(status.lockId).toBe('other-lock-456');
			expect(status.isOwnedByThisWorker).toBe(false);
		});

		it('should handle malformed lock values', async () =>
		{
			const domain = 'example.com';
			const malformedValue = 'malformed-lock-value';

			mockRedisClient.get.mockResolvedValue(malformedValue);
			mockRedisClient.ttl.mockResolvedValue(300);

			const status = await lockManager.getLockStatus(domain);

			expect(status.isLocked).toBe(true);
			expect(status.owner).toBe('unknown');
			expect(status.lockId).toBe('unknown');
		});
	});

	describe('Bulk Operations', () =>
	{
		it('should check multiple domain statuses efficiently', async () =>
		{
			const domains = ['domain1.com', 'domain2.com', 'domain3.com'];
			const lockKeys = domains.map(d => `domain_lock:${d}`);

			// Mock bulk get response
			mockRedisClient.mget.mockResolvedValue([
				`${workerId}:lock1`,
				null,
				'other-worker:lock3',
			]);

			const statuses = await lockManager.getBulkLockStatus(domains);

			expect(statuses).toHaveLength(3);
			expect(statuses[0].isLocked).toBe(true);
			expect(statuses[0].isOwnedByThisWorker).toBe(true);
			expect(statuses[1].isLocked).toBe(false);
			expect(statuses[2].isLocked).toBe(true);
			expect(statuses[2].isOwnedByThisWorker).toBe(false);

			expect(mockRedisClient.mget).toHaveBeenCalledWith(lockKeys);
		});

		it('should release multiple locks atomically', async () =>
		{
			const lockReleases = [
				{ domain: 'domain1.com', lockId: 'lock1' },
				{ domain: 'domain2.com', lockId: 'lock2' },
			];

			// Mock successful bulk release
			mockRedisClient.eval.mockResolvedValue([1, 1]);

			const results = await lockManager.releaseBulkLocks(lockReleases);

			expect(results).toHaveLength(2);
			expect(results[0].success).toBe(true);
			expect(results[1].success).toBe(true);
		});
	});

	describe('Lock Cleanup and Maintenance', () =>
	{
		it('should clean up expired locks', async () =>
		{
			const expiredLockKeys = [
				'domain_lock:expired1.com',
				'domain_lock:expired2.com',
			];

			mockRedisClient.keys.mockResolvedValue(expiredLockKeys);
			mockRedisClient.ttl.mockResolvedValueOnce(-1).mockResolvedValueOnce(-1); // Expired
			mockRedisClient.del.mockResolvedValue(2);

			const cleanedCount = await lockManager.cleanupExpiredLocks();

			expect(cleanedCount).toBe(2);
			expect(mockRedisClient.del).toHaveBeenCalledWith(expiredLockKeys);
		});

		it('should identify orphaned locks', async () =>
		{
			const allLockKeys = [
				'domain_lock:active.com',
				'domain_lock:orphaned.com',
			];

			mockRedisClient.keys.mockResolvedValue(allLockKeys);
			mockRedisClient.mget.mockResolvedValue([
				`${workerId}:active-lock`,
				'dead-worker:orphaned-lock',
			]);

			// Mock worker health check - only current worker is alive
			const isWorkerAlive = vi.fn()
				.mockResolvedValueOnce(true)  // Current worker is alive
				.mockResolvedValueOnce(false); // Dead worker is not alive

			const orphanedLocks = await lockManager.findOrphanedLocks(isWorkerAlive);

			expect(orphanedLocks).toHaveLength(1);
			expect(orphanedLocks[0].domain).toBe('orphaned.com');
			expect(orphanedLocks[0].owner).toBe('dead-worker');
		});

		it('should force release orphaned locks', async () =>
		{
			const orphanedDomains = ['orphaned1.com', 'orphaned2.com'];

			mockRedisClient.del.mockResolvedValue(2);

			const releasedCount = await lockManager.forceReleaseOrphanedLocks(orphanedDomains);

			expect(releasedCount).toBe(2);
			expect(mockRedisClient.del).toHaveBeenCalledWith([
				'domain_lock:orphaned1.com',
				'domain_lock:orphaned2.com',
			]);
		});
	});

	describe('Lock Statistics and Monitoring', () =>
	{
		it('should collect lock statistics', async () =>
		{
			const allLockKeys = [
				'domain_lock:domain1.com',
				'domain_lock:domain2.com',
				'domain_lock:domain3.com',
			];

			mockRedisClient.keys.mockResolvedValue(allLockKeys);
			mockRedisClient.mget.mockResolvedValue([
				`${workerId}:lock1`,
				`${workerId}:lock2`,
				'other-worker:lock3',
			]);

			const stats = await lockManager.getLockStatistics();

			expect(stats.totalLocks).toBe(3);
			expect(stats.locksOwnedByThisWorker).toBe(2);
			expect(stats.locksOwnedByOthers).toBe(1);
			expect(stats.locksByWorker[workerId]).toBe(2);
			expect(stats.locksByWorker['other-worker']).toBe(1);
		});

		it('should track lock operation metrics', async () =>
		{
			const domain = 'metrics.com';
			const ttl = 300000;

			// Perform some operations
			mockRedisClient.eval.mockResolvedValue('OK');
			await lockManager.acquireLock(domain, ttl);

			mockRedisClient.eval.mockResolvedValue(1);
			await lockManager.renewLock(domain, 'test-lock', ttl);

			mockRedisClient.eval.mockResolvedValue(1);
			await lockManager.releaseLock(domain, 'test-lock');

			const metrics = lockManager.getOperationMetrics();

			expect(metrics.totalAcquisitions).toBe(1);
			expect(metrics.successfulAcquisitions).toBe(1);
			expect(metrics.totalRenewals).toBe(1);
			expect(metrics.successfulRenewals).toBe(1);
			expect(metrics.totalReleases).toBe(1);
			expect(metrics.successfulReleases).toBe(1);
		});

		it('should calculate average lock hold time', async () =>
		{
			const domain = 'timing.com';
			const ttl = 300000;

			// Mock lock acquisition and release with timing
			mockRedisClient.eval.mockResolvedValue('OK');
			const acquireResult = await lockManager.acquireLock(domain, ttl);

			// Simulate some processing time
			await new Promise(resolve => setTimeout(resolve, 100));

			mockRedisClient.eval.mockResolvedValue(1);
			await lockManager.releaseLock(domain, acquireResult.lockId!);

			const metrics = lockManager.getOperationMetrics();

			expect(metrics.averageLockHoldTime).toBeGreaterThan(90);
			expect(metrics.averageLockHoldTime).toBeLessThan(200);
		});
	});

	describe('Error Handling and Resilience', () =>
	{
		it('should handle Redis connection loss gracefully', async () =>
		{
			const domain = 'connection-lost.com';
			const ttl = 300000;

			mockRedisClient.eval.mockRejectedValue(new Error('Connection lost'));

			const result = await lockManager.acquireLock(domain, ttl);

			expect(result.success).toBe(false);
			expect(result.error).toContain('Connection lost');
		});

		it('should implement exponential backoff for retries', async () =>
		{
			const domain = 'retry.com';
			const ttl = 300000;

			// Mock temporary failures followed by success
			mockRedisClient.eval
				.mockRejectedValueOnce(new Error('Temporary failure'))
				.mockRejectedValueOnce(new Error('Temporary failure'))
				.mockResolvedValue('OK');

			const startTime = Date.now();
			const result = await lockManager.acquireLock(domain, ttl, { maxRetries: 3, baseDelay: 100 });
			const endTime = Date.now();

			expect(result.success).toBe(true);
			expect(endTime - startTime).toBeGreaterThan(300); // Should have waited for retries
		});

		it('should timeout long-running operations', async () =>
		{
			const domain = 'timeout.com';
			const ttl = 300000;

			// Mock operation that never resolves
			mockRedisClient.eval.mockImplementation(() => new Promise(() => {}));

			const result = await lockManager.acquireLock(domain, ttl, { timeout: 1000 });

			expect(result.success).toBe(false);
			expect(result.error).toContain('timeout');
		});

		it('should validate lock consistency', async () =>
		{
			const domain = 'consistency.com';
			const lockId = 'test-lock-123';

			// Mock inconsistent state (lock exists but with wrong value)
			mockRedisClient.get.mockResolvedValue('corrupted-value');

			const isConsistent = await lockManager.validateLockConsistency(domain, lockId);

			expect(isConsistent).toBe(false);
		});
	});

	describe('Concurrency and Race Conditions', () =>
	{
		it('should handle concurrent lock acquisition attempts', async () =>
		{
			const domain = 'concurrent.com';
			const ttl = 300000;

			// Simulate race condition - first succeeds, second fails
			mockRedisClient.eval
				.mockResolvedValueOnce('OK')
				.mockResolvedValueOnce(null);

			const [result1, result2] = await Promise.all([
				lockManager.acquireLock(domain, ttl),
				lockManager.acquireLock(domain, ttl),
			]);

			expect(result1.success).toBe(true);
			expect(result2.success).toBe(false);
		});

		it('should handle concurrent renewal attempts', async () =>
		{
			const domain = 'renewal-race.com';
			const lockId = 'test-lock-123';
			const newTtl = 600000;

			// First renewal succeeds, second fails (lock no longer owned)
			mockRedisClient.eval
				.mockResolvedValueOnce(1)
				.mockResolvedValueOnce(0);

			const [result1, result2] = await Promise.all([
				lockManager.renewLock(domain, lockId, newTtl),
				lockManager.renewLock(domain, lockId, newTtl),
			]);

			expect(result1.success).toBe(true);
			expect(result2.success).toBe(false);
		});
	});

	describe('Configuration and Customization', () =>
	{
		it('should respect custom lock key prefix', async () =>
		{
			const customLockManager = new DomainLockManager(redisUrl, workerId, {
				lockKeyPrefix: 'custom_lock:',
			});

			await customLockManager.initialize();

			const domain = 'example.com';
			const ttl = 300000;

			mockRedisClient.eval.mockResolvedValue('OK');
			await customLockManager.acquireLock(domain, ttl);

			expect(mockRedisClient.eval).toHaveBeenCalledWith(
				expect.any(String),
				1,
				'custom_lock:example.com',
				expect.any(String),
				expect.any(String),
			);

			await customLockManager.shutdown();
		});

		it('should respect custom default TTL', async () =>
		{
			const customLockManager = new DomainLockManager(redisUrl, workerId, {
				defaultTtl: 600000, // 10 minutes
			});

			await customLockManager.initialize();

			const domain = 'example.com';

			mockRedisClient.eval.mockResolvedValue('OK');
			const result = await customLockManager.acquireLock(domain);

			expect(result.ttl).toBe(600000);

			await customLockManager.shutdown();
		});
	});
});
