/**
 * Type definitions for Domain Locking System
 */

type LockAcquisitionResultType =
{
	success: boolean;
	lockId?: string;
	conflictingOwner?: string;
	retryAfter?: number;
	error?: string;
};

type LockRenewalResultType =
{
	success: boolean;
	newTtl?: number;
	error?: string;
};

type LockReleaseResultType =
{
	success: boolean;
	wasOwner: boolean;
	error?: string;
};

type LockStatusType =
{
	isLocked: boolean;
	owner?: string;
	ttl?: number;
	lockId?: string;
	acquiredAt?: Date;
	expiresAt?: Date;
};

type LockStatisticsType =
{
	totalAcquisitions: number;
	successfulAcquisitions: number;
	failedAcquisitions: number;
	conflicts: number;
	renewals: number;
	releases: number;
	orphanedLocks: number;
	averageHoldTime: number;
	currentActiveLocks: number;
};

type LockConfigType =
{
	defaultTtl: number; // Default lock TTL in seconds
	maxTtl: number; // Maximum allowed TTL
	renewalThreshold: number; // Renew when TTL drops below this (seconds)
	maxRetries: number; // Maximum retry attempts for lock acquisition
	baseRetryDelay: number; // Base delay for exponential backoff (ms)
	maxRetryDelay: number; // Maximum retry delay (ms)
	lockPrefix: string; // Redis key prefix for locks
	statsPrefix: string; // Redis key prefix for statistics
	cleanupInterval: number; // Orphaned lock cleanup interval (ms)
	enableStatistics: boolean; // Enable statistics collection
	enableMonitoring: boolean; // Enable lock monitoring
};

type LockConflictResolutionType =
'fail' | 'wait' | 'force';

type LockInfoType =
{
	domain: string;
	lockId: string;
	workerId: string;
	ttl: number;
	acquiredAt: Date;
	expiresAt: Date;
};

type LockHealthCheckType =
{
	healthy: boolean;
	issues: string[];
	metrics: Record<string, number>;
};

type LockMonitoringEventType =
{
	type: 'acquisition' | 'renewal' | 'release' | 'conflict' | 'cleanup' | 'error';
	domain: string;
	workerId: string;
	lockId?: string;
	timestamp: Date;
	details?: Record<string, unknown>;
	error?: string;
};

type LockAlertRuleType =
{
	id: string;
	name: string;
	condition: string; // e.g., 'conflictRate > 50'
	threshold: number;
	enabled: boolean;
	severity: 'low' | 'medium' | 'high' | 'critical';
	cooldown: number; // Minimum time between alerts (seconds)
};

type LockAlertType =
{
	id: string;
	ruleId: string;
	severity: 'low' | 'medium' | 'high' | 'critical';
	message: string;
	timestamp: Date;
	workerId: string;
	metrics: Record<string, number>;
	acknowledged: boolean;
	resolvedAt?: Date;
};

export type {
	LockAcquisitionResultType,
	LockRenewalResultType,
	LockReleaseResultType,
	LockStatusType,
	LockStatisticsType,
	LockConfigType,
	LockConflictResolutionType,
	LockInfoType,
	LockHealthCheckType,
	LockMonitoringEventType,
	LockAlertRuleType,
	LockAlertType,
};
