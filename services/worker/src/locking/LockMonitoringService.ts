/**
 * Lock Monitoring Service
 *
 * Provides comprehensive monitoring, debugging, and alerting capabilities
 * for the domain locking system. Tracks lock operations, detects anomalies,
 * and generates alerts for lock system failures.
 */

import type { RedisClient } from '@shared/database/RedisClient';
import { logger as loggerFactory } from '@shared/utils/Logger';
import type { DomainLockManager } from './DomainLockManager';
import type {
	LockMonitoringEventType,
	LockAlertType,
	LockAlertRuleType,
	LockStatisticsType,
	LockHealthCheckType,
} from './types';

const logger = loggerFactory.getLogger('LockMonitoringService');

type MonitoringConfigType =
{
	eventRetentionDays: number;
	alertRetentionDays: number;
	healthCheckInterval: number; // ms
	metricsCollectionInterval: number; // ms
	enableEventLogging: boolean;
	enableAlerting: boolean;
	enableMetricsCollection: boolean;
};

class LockMonitoringService
{
	private redisClient: RedisClient;
	private lockManager: DomainLockManager;
	private config: MonitoringConfigType;
	private workerId: string;
	private alertRules: Map<string, LockAlertRuleType> = new Map();
	private activeAlerts: Map<string, LockAlertType> = new Map();
	private lastAlertTimes: Map<string, number> = new Map();
	private healthCheckTimer?: NodeJS.Timeout;
	private metricsTimer?: NodeJS.Timeout;
	private isShuttingDown: boolean = false;

	constructor(
		redisClient: RedisClient,
		lockManager: DomainLockManager,
		workerId: string,
		config?: Partial<MonitoringConfigType>,
	)
	{
		this.redisClient = redisClient;
		this.lockManager = lockManager;
		this.workerId = workerId;
		this.config = {
			eventRetentionDays: 7,
			alertRetentionDays: 30,
			healthCheckInterval: 60000, // 1 minute
			metricsCollectionInterval: 30000, // 30 seconds
			enableEventLogging: true,
			enableAlerting: true,
			enableMetricsCollection: true,
			...config,
		};

		this.initializeDefaultAlertRules();
		this.startMonitoring();

		logger.info('LockMonitoringService initialized', {
			workerId: this.workerId,
			config: this.config,
		});
	}

	/**
	 * Log a lock monitoring event
	 */
	async logEvent(event: Omit<LockMonitoringEventType, 'timestamp'>): Promise<void>
	{
		if (!this.config.enableEventLogging) return;

		const fullEvent: LockMonitoringEventType =
		{
			...event,
			timestamp: new Date(),
		};

		try
		{
			// Store event in Redis with expiration
			const eventKey = `lock:events:${this.workerId}:${Date.now()}:${Math.random().toString(36).substr(2, 9)}`;
			const ttl = this.config.eventRetentionDays * 24 * 60 * 60; // Convert to seconds

			await this.redisClient.setex(eventKey, ttl, fullEvent);

			// Also add to a sorted set for easy querying
			const eventsSetKey = `lock:events:${this.workerId}:index`;
			await this.redisClient.getClient()?.zAdd(eventsSetKey, {
				score: fullEvent.timestamp.getTime(),
				value: eventKey,
			});

			// Set expiration on the index as well
			await this.redisClient.expire(eventsSetKey, ttl);

			logger.debug('Lock event logged', {
				type: event.type,
				domain: event.domain,
				workerId: event.workerId,
			});
		}
		catch (error)
		{
			logger.error('Failed to log lock event', {
				event: fullEvent,
				error: error instanceof Error ? error.message : String(error),
			});
		}
	}

	/**
	 * Get recent lock events
	 */
	async getRecentEvents(
		limit: number = 100,
		eventType?: string,
		domain?: string,
	): Promise<LockMonitoringEventType[]>
	{
		try
		{
			const eventsSetKey = `lock:events:${this.workerId}:index`;
			const client = this.redisClient.getClient();

			if (!client) return [];

			// Get recent event keys
			const eventKeys = await client.RevRange(eventsSetKey, 0, limit - 1);
			const events: LockMonitoringEventType[] = [];

			for (const eventKey of eventKeys)
			{
				try
				{
					const event = await this.redisClient.get<LockMonitoringEventType>(eventKey);
					if (event)
					{
						// Apply filters
						if (
							(eventType && event.type !== eventType) ||
							(domain && event.domain !== domain)
						)
						{
							continue;
						}

						events.push(event);
					}
				}
				catch (error)
				{
					logger.warn('Failed to retrieve event', {
						eventKey,
						error: error instanceof Error ? error.message : String(error),
					});
				}
			}

			return events;
		}
		catch (error)
		{
			logger.error('Failed to get recent events', {
				error: error instanceof Error ? error.message : String(error),
			});
			return ([]);
		}
	}

	/**
	 * Add or update alert rule
	 */
	addAlertRule(rule: LockAlertRuleType): void
	{
		this.alertRules.set(rule.id, rule);
		logger.info('Alert rule added/updated', {
			ruleId: rule.id,
			name: rule.name,
			condition: rule.condition,
		});
	}

	/**
	 * Remove alert rule
	 */
	removeAlertRule(ruleId: string): boolean
	{
		const removed = this.alertRules.delete(ruleId);
		if (removed)
		{
			logger.info('Alert rule removed', { ruleId });
		}
		return removed;
	}

	/**
	 * Get all alert rules
	 */
	getAlertRules(): LockAlertRuleType[]
	{
		return Array.from(this.alertRules.values());
	}

	/**
	 * Get active alerts
	 */
	getActiveAlerts(): LockAlertType[]
	{
		return Array.from(this.activeAlerts.values()).filter(alert => !alert.acknowledged);
	}

	/**
	 * Acknowledge alert
	 */
	async acknowledgeAlert(alertId: string): Promise<boolean>
	{
		const alert = this.activeAlerts.get(alertId);
		if (!alert) return false;

		alert.acknowledged = true;

		try
		{
			// Update in Redis
			const alertKey = `lock:alerts:${alertId}`;
			await this.redisClient.set(alertKey, alert);

			logger.info('Alert acknowledged', {
				alertId,
				ruleId: alert.ruleId,
				workerId: this.workerId,
			});

			return true;
		}
		catch (error)
		{
			logger.error('Failed to acknowledge alert', {
				alertId,
				error: error instanceof Error ? error.message : String(error),
			});
			return false;
		}
	}

	/**
	 * Resolve alert
	 */
	async resolveAlert(alertId: string): Promise<boolean>
	{
		const alert = this.activeAlerts.get(alertId);
		if (!alert) return false;

		alert.resolvedAt = new Date();
		this.activeAlerts.delete(alertId);

		try
		{
			// Update in Redis
			const alertKey = `lock:alerts:${alertId}`;
			await this.redisClient.set(alertKey, alert);

			logger.info('Alert resolved', {
				alertId,
				ruleId: alert.ruleId,
				workerId: this.workerId,
			});

			return true;
		}
		catch (error)
		{
			logger.error('Failed to resolve alert', {
				alertId,
				error: error instanceof Error ? error.message : String(error),
			});
			return false;
		}
	}

	/**
	 * Get lock system health status
	 */
	async getHealthStatus(): Promise<LockHealthCheckType>
	{
		return await this.lockManager.healthCheck();
	}

	/**
	 * Get detailed lock metrics
	 */
	async getDetailedMetrics(): Promise<{
		statistics: LockStatisticsType;
		activeLocks: Array<{ domain: string; lockId: string; acquiredAt: Date; expiresAt: Date }>;
		recentEvents: LockMonitoringEventType[];
		activeAlerts: LockAlertType[];
		systemHealth: LockHealthCheckType;
	}>
	{
		const [
			statistics,
			activeLocks,
			recentEvents,
			activeAlerts,
			systemHealth,
		] = await Promise.all([
			Promise.resolve(this.lockManager.getStatistics()),
			Promise.resolve(this.lockManager.getActiveLocks()),
			this.getRecentEvents(50),
			Promise.resolve(this.getActiveAlerts()),
			this.getHealthStatus(),
		]);

		return ({
			statistics,
			activeLocks,
			recentEvents,
			activeAlerts,
			systemHealth,
		});
	}

	/**
	 * Generate debug report for troubleshooting
	 */
	async generateDebugReport(domain?: string): Promise<{
		workerId: string;
		timestamp: Date;
		lockStatus?: any;
		recentEvents: LockMonitoringEventType[];
		statistics: LockStatisticsType;
		activeLocks: any[];
		systemHealth: LockHealthCheckType;
		redisInfo: any;
	}>
	{
		const report = {
			workerId: this.workerId,
			timestamp: new Date(),
			lockStatus: domain ? await this.lockManager.getLockStatus(domain) : undefined,
			recentEvents: await this.getRecentEvents(100, undefined, domain),
			statistics: this.lockManager.getStatistics(),
			activeLocks: this.lockManager.getActiveLocks(),
			systemHealth: await this.getHealthStatus(),
			redisInfo: this.redisClient.getConnectionStats(),
		};

		logger.info('Debug report generated', {
			workerId: this.workerId,
			domain,
			eventCount: report.recentEvents.length,
			activeLocksCount: report.activeLocks.length,
		});

		return report;
	}

	/**
	 * Shutdown monitoring service
	 */
	async shutdown(): Promise<void>
	{
		logger.info('Shutting down LockMonitoringService', {
			workerId: this.workerId,
		});

		this.isShuttingDown = true;

		// Stop timers
		if (this.healthCheckTimer)
		{
			clearInterval(this.healthCheckTimer);
		}
		if (this.metricsTimer)
		{
			clearInterval(this.metricsTimer);
		}

		// Resolve any active alerts
		for (const alertId of this.activeAlerts.keys())
		{
			await this.resolveAlert(alertId);
		}

		logger.info('LockMonitoringService shutdown completed');
	}

	// ===== PRIVATE METHODS =====

	private initializeDefaultAlertRules(): void
	{
		const defaultRules: LockAlertRuleType[] =
		[
			{
				id: 'high-conflict-rate',
				name: 'High Lock Conflict Rate',
				condition: 'conflictRate > 50',
				threshold: 50,
				enabled: true,
				severity: 'high',
				cooldown: 300, // 5 minutes
			},
			{
				id: 'low-success-rate',
				name: 'Low Lock Acquisition Success Rate',
				condition: 'successRate < 90',
				threshold: 90,
				enabled: true,
				severity: 'medium',
				cooldown: 300,
			},
			{
				id: 'many-active-locks',
				name: 'Too Many Active Locks',
				condition: 'activeLocks > 100',
				threshold: 100,
				enabled: true,
				severity: 'medium',
				cooldown: 600, // 10 minutes
			},
			{
				id: 'redis-connectivity',
				name: 'Redis Connectivity Issues',
				condition: 'redisHealthy = false',
				threshold: 1,
				enabled: true,
				severity: 'critical',
				cooldown: 60, // 1 minute
			},
		];

		for (const rule of defaultRules)
		{
			this.alertRules.set(rule.id, rule);
		}

		logger.info('Default alert rules initialized', {
			ruleCount: defaultRules.length,
		});
	}

	private startMonitoring(): void
	{
		if (this.config.enableAlerting)
		{
			this.healthCheckTimer = setInterval(async () =>
			{
				if (this.isShuttingDown) return;
				await this.performHealthCheck();
			}, this.config.healthCheckInterval);
		}

		if (this.config.enableMetricsCollection)
		{
			this.metricsTimer = setInterval(async () =>
			{
				if (this.isShuttingDown) return;
				await this.collectMetrics();
			}, this.config.metricsCollectionInterval);
		}
	}

	private async performHealthCheck(): Promise<void>
	{
		try
		{
			const health = await this.lockManager.healthCheck();
			const statistics = this.lockManager.getStatistics();

			// Check alert rules
			for (const rule of this.alertRules.values())
			{
				if (!rule.enabled) continue;

				const shouldAlert = this.evaluateAlertRule(rule, health, statistics);
				if (shouldAlert)
				{
					await this.triggerAlert(rule, health, statistics);
				}
			}
		}
		catch (error)
		{
			logger.error('Health check failed', {
				error: error instanceof Error ? error.message : String(error),
			});
		}
	}

	private async collectMetrics(): Promise<void>
	{
		try
		{
			const statistics = this.lockManager.getStatistics();
			const timestamp = Date.now();

			// Store metrics in Redis for historical analysis
			const metricsKey = `lock:metrics:${this.workerId}:${timestamp}`;
			const ttl = this.config.eventRetentionDays * 24 * 60 * 60;

			await this.redisClient.setex(metricsKey, ttl, {
				timestamp,
				workerId: this.workerId,
				statistics,
			});

			logger.debug('Metrics collected', {
				workerId: this.workerId,
				activeLocks: statistics.currentActiveLocks,
				successRate: statistics.totalAcquisitions > 0
					? (statistics.successfulAcquisitions / statistics.totalAcquisitions) * 100
					: 100,
			});
		}
		catch (error)
		{
			logger.error('Metrics collection failed', {
				error: error instanceof Error ? error.message : String(error),
			});
		}
	}

	private evaluateAlertRule(
		rule: LockAlertRuleType,
		health: LockHealthCheckType,
		statistics: LockStatisticsType,
	): boolean
	{
		try
		{
			// Check cooldown
			const lastAlertTime = this.lastAlertTimes.get(rule.id) || 0;
			const now = Date.now();
			if (now - lastAlertTime < rule.cooldown * 1000)
			{
				return false;
			}

			// Evaluate condition
			switch (rule.condition)
			{
				case 'conflictRate > 50':
				{
					const conflictRate = statistics.totalAcquisitions > 0
						? (statistics.conflicts / statistics.totalAcquisitions) * 100
						: 0;
					return conflictRate > rule.threshold;
				}

				case 'successRate < 90':
				{
					const successRate = statistics.totalAcquisitions > 0
						? (statistics.successfulAcquisitions / statistics.totalAcquisitions) * 100
						: 100;
					return successRate < rule.threshold;
				}

				case 'activeLocks > 100':
					return statistics.currentActiveLocks > rule.threshold;

				case 'redisHealthy = false':
					return !health.healthy;

				default:
					logger.warn('Unknown alert rule condition', {
						ruleId: rule.id,
						condition: rule.condition,
					});
					return false;
			}
		}
		catch (error)
		{
			logger.error('Alert rule evaluation failed', {
				ruleId: rule.id,
				error: error instanceof Error ? error.message : String(error),
			});
			return false;
		}
	}

	private async triggerAlert(
		rule: LockAlertRuleType,
		health: LockHealthCheckType,
		statistics: LockStatisticsType,
	): Promise<void>
	{
		const alertId = `${rule.id}-${Date.now()}`;
		const alert: LockAlertType =
		{
			id: alertId,
			ruleId: rule.id,
			severity: rule.severity,
			message: this.generateAlertMessage(rule, health, statistics),
			timestamp: new Date(),
			workerId: this.workerId,
			metrics: health.metrics,
			acknowledged: false,
		};

		this.activeAlerts.set(alertId, alert);
		this.lastAlertTimes.set(rule.id, Date.now());

		try
		{
			// Store alert in Redis
			const alertKey = `lock:alerts:${alertId}`;
			const ttl = this.config.alertRetentionDays * 24 * 60 * 60;
			await this.redisClient.setex(alertKey, ttl, alert);

			// Log the alert
			logger.warn('Lock system alert triggered', {
				alertId,
				ruleId: rule.id,
				severity: rule.severity,
				message: alert.message,
				workerId: this.workerId,
			});

			// Log as monitoring event
			await this.logEvent({
				type: 'error',
				domain: 'system',
				workerId: this.workerId,
				details: {
					alertId,
					ruleId: rule.id,
					severity: rule.severity,
					message: alert.message,
				},
			});
		}
		catch (error)
		{
			logger.error('Failed to store alert', {
				alertId,
				error: error instanceof Error ? error.message : String(error),
			});
		}
	}

	private generateAlertMessage(
		rule: LockAlertRuleType,
		health: LockHealthCheckType,
		statistics: LockStatisticsType,
	): string
	{
		switch (rule.condition)
		{
			case 'conflictRate > 50':
			{
				const conflictRate = statistics.totalAcquisitions > 0
					? (statistics.conflicts / statistics.totalAcquisitions) * 100
					: 0;
				return `High lock conflict rate detected: ${conflictRate.toFixed(1)}% (threshold: ${rule.threshold}%)`;
			}

			case 'successRate < 90':
			{
				const successRate = statistics.totalAcquisitions > 0
					? (statistics.successfulAcquisitions / statistics.totalAcquisitions) * 100
					: 100;
				return `Low lock acquisition success rate: ${successRate.toFixed(1)}% (threshold: ${rule.threshold}%)`;
			}

			case 'activeLocks > 100':
				return `Too many active locks: ${statistics.currentActiveLocks} (threshold: ${rule.threshold})`;

			case 'redisHealthy = false':
				return `Redis connectivity issues detected: ${health.issues.join(', ')}`;

			default:
				return `Alert condition met: ${rule.condition}`;
		}
	}
}

export type { MonitoringConfigType };

export default LockMonitoringService;
