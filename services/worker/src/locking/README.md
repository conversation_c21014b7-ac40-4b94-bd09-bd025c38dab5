# Domain Locking System

A comprehensive distributed locking mechanism for multi-worker coordination in the domain processing pipeline. This system ensures that no domain is processed by multiple workers simultaneously, preventing race conditions and data corruption.

## Features

- **Distributed Locking**: Redis-based locks with TTL and automatic renewal
- **Conflict Resolution**: Multiple strategies for handling lock conflicts (fail, wait, force)
- **Retry Logic**: Exponential backoff with configurable retry attempts
- **Lock Monitoring**: Comprehensive monitoring, debugging, and alerting
- **Statistics Collection**: Detailed metrics on lock operations and performance
- **Orphaned Lock Cleanup**: Automatic detection and cleanup of stale locks
- **Health Monitoring**: System health checks and anomaly detection
- **Error Handling**: Robust error handling with graceful degradation

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│    Worker 1     │    │    Worker 2     │    │    Worker N     │
│                 │    │                 │    │                 │
│ DomainLockMgr   │    │ DomainLockMgr   │    │ DomainLockMgr   │
│ MonitoringSvc   │    │ MonitoringSvc   │    │ MonitoringSvc   │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │         Redis             │
                    │                           │
                    │ • Domain Locks            │
                    │ • Lock Statistics         │
                    │ • Monitoring Events       │
                    │ • Alert Management        │
                    └───────────────────────────┘
```

## Quick Start

### Basic Usage

```typescript
import { RedisClient } from "../database/clients/RedisClient.js";
import { DomainLockManager, LockMonitoringService } from "./index.js";

// Initialize Redis client
const redisClient = new RedisClient({
  url: "redis://localhost:6379",
  database: 0,
  maxRetries: 3,
});
await redisClient.connect();

// Initialize lock manager
const lockManager = new DomainLockManager(redisClient, "worker-1", {
  defaultTtl: 300, // 5 minutes
  maxRetries: 5,
  enableStatistics: true,
  enableMonitoring: true,
});

// Initialize monitoring service
const monitoringService = new LockMonitoringService(
  redisClient,
  lockManager,
  "worker-1"
);

// Acquire lock for domain processing
const domain = "example.com";
const result = await lockManager.acquireLock(domain);

if (result.success) {
  try {
    // Process domain safely
    await processDomain(domain);
  } finally {
    // Always release lock
    await lockManager.releaseLock(domain);
  }
} else {
  console.log(`Domain ${domain} is locked by ${result.conflictingOwner}`);
}
```

### Conflict Resolution Strategies

```typescript
// Fail immediately if lock is not available (default)
const result1 = await lockManager.acquireLock(domain, undefined, "fail");

// Wait and retry with exponential backoff
const result2 = await lockManager.acquireLock(domain, undefined, "wait");

// Force acquire by breaking existing lock
const result3 = await lockManager.acquireLock(domain, undefined, "force");
```

### Lock Monitoring

```typescript
// Log lock events
await monitoringService.logEvent({
  type: "acquisition",
  domain: "example.com",
  workerId: "worker-1",
  lockId: "lock-123",
});

// Get system health
const health = await monitoringService.getHealthStatus();

// Generate debug report
const report = await monitoringService.generateDebugReport("example.com");

// Get detailed metrics
const metrics = await monitoringService.getDetailedMetrics();
```

## Configuration

### DomainLockManager Configuration

```typescript
const config = {
  defaultTtl: 300, // Default lock TTL in seconds
  maxTtl: 3600, // Maximum allowed TTL
  renewalThreshold: 60, // Renew when TTL drops below this
  maxRetries: 5, // Maximum retry attempts
  baseRetryDelay: 1000, // Base delay for exponential backoff (ms)
  maxRetryDelay: 30000, // Maximum retry delay (ms)
  lockPrefix: "lock:domain", // Redis key prefix for locks
  cleanupInterval: 60000, // Orphaned lock cleanup interval (ms)
  enableStatistics: true, // Enable statistics collection
  enableMonitoring: true, // Enable lock monitoring
};
```

### LockMonitoringService Configuration

```typescript
const monitoringConfig = {
  eventRetentionDays: 7, // How long to keep events
  alertRetentionDays: 30, // How long to keep alerts
  healthCheckInterval: 60000, // Health check frequency (ms)
  metricsCollectionInterval: 30000, // Metrics collection frequency (ms)
  enableEventLogging: true, // Enable event logging
  enableAlerting: true, // Enable alerting system
  enableMetricsCollection: true, // Enable metrics collection
};
```

## API Reference

### DomainLockManager

#### `acquireLock(domain, ttl?, conflictResolution?)`

Acquire a lock for the specified domain.

**Parameters:**

- `domain` (string): Domain to lock
- `ttl` (number, optional): Lock TTL in seconds
- `conflictResolution` ('fail' | 'wait' | 'force', optional): Conflict resolution strategy

**Returns:** `Promise<LockAcquisitionResult>`

#### `releaseLock(domain)`

Release a lock for the specified domain.

**Parameters:**

- `domain` (string): Domain to unlock

**Returns:** `Promise<LockReleaseResult>`

#### `renewLock(domain, newTtl?)`

Renew a lock's TTL.

**Parameters:**

- `domain` (string): Domain to renew lock for
- `newTtl` (number, optional): New TTL in seconds

**Returns:** `Promise<LockRenewalResult>`

#### `isLocked(domain)`

Check if a domain is currently locked.

**Parameters:**

- `domain` (string): Domain to check

**Returns:** `Promise<boolean>`

#### `getLockOwner(domain)`

Get the owner of a domain lock.

**Parameters:**

- `domain` (string): Domain to check

**Returns:** `Promise<string | null>`

#### `getLockStatus(domain)`

Get detailed status of a domain lock.

**Parameters:**

- `domain` (string): Domain to check

**Returns:** `Promise<LockStatus>`

#### `getStatistics()`

Get lock operation statistics.

**Returns:** `LockStatistics`

#### `getActiveLocks()`

Get all active locks for this worker.

**Returns:** `Array<LockInfo & { domain: string }>`

#### `cleanupOrphanedLocks()`

Clean up orphaned locks without TTL.

**Returns:** `Promise<number>` - Number of locks cleaned up

#### `healthCheck()`

Perform comprehensive health check.

**Returns:** `Promise<LockHealthCheck>`

#### `shutdown()`

Shutdown lock manager and release all locks.

**Returns:** `Promise<void>`

### LockMonitoringService

#### `logEvent(event)`

Log a lock monitoring event.

**Parameters:**

- `event` (LockMonitoringEvent): Event to log

**Returns:** `Promise<void>`

#### `getRecentEvents(limit?, eventType?, domain?)`

Get recent lock events with optional filtering.

**Parameters:**

- `limit` (number, optional): Maximum number of events to return
- `eventType` (string, optional): Filter by event type
- `domain` (string, optional): Filter by domain

**Returns:** `Promise<LockMonitoringEvent[]>`

#### `addAlertRule(rule)`

Add or update an alert rule.

**Parameters:**

- `rule` (LockAlertRule): Alert rule to add

**Returns:** `void`

#### `removeAlertRule(ruleId)`

Remove an alert rule.

**Parameters:**

- `ruleId` (string): ID of rule to remove

**Returns:** `boolean`

#### `getActiveAlerts()`

Get all active (unacknowledged) alerts.

**Returns:** `LockAlert[]`

#### `acknowledgeAlert(alertId)`

Acknowledge an alert.

**Parameters:**

- `alertId` (string): ID of alert to acknowledge

**Returns:** `Promise<boolean>`

#### `resolveAlert(alertId)`

Resolve an alert.

**Parameters:**

- `alertId` (string): ID of alert to resolve

**Returns:** `Promise<boolean>`

#### `getDetailedMetrics()`

Get comprehensive system metrics.

**Returns:** `Promise<DetailedMetrics>`

#### `generateDebugReport(domain?)`

Generate debug report for troubleshooting.

**Parameters:**

- `domain` (string, optional): Specific domain to focus on

**Returns:** `Promise<DebugReport>`

## Error Handling

The locking system implements comprehensive error handling:

### Lock Acquisition Errors

- **Redis Connection Failures**: Graceful degradation with retry logic
- **Lock Conflicts**: Configurable conflict resolution strategies
- **Timeout Errors**: Exponential backoff with maximum retry limits

### Lock Management Errors

- **Renewal Failures**: Automatic cleanup of stale local state
- **Release Failures**: Logging and monitoring of failed releases
- **Orphaned Locks**: Automatic detection and cleanup

### Monitoring Errors

- **Event Logging Failures**: Silent failure with error logging
- **Alert System Failures**: Fallback to basic logging
- **Health Check Failures**: Graceful degradation

## Best Practices

### 1. Always Use Try-Finally for Lock Release

```typescript
const result = await lockManager.acquireLock(domain);
if (result.success) {
  try {
    // Process domain
  } finally {
    await lockManager.releaseLock(domain);
  }
}
```

### 2. Handle Lock Conflicts Gracefully

```typescript
const result = await lockManager.acquireLock(domain);
if (!result.success) {
  if (result.conflictingOwner) {
    logger.info(
      `Domain ${domain} is being processed by ${result.conflictingOwner}`
    );
    // Maybe retry later or skip
  } else {
    logger.error(`Failed to acquire lock: ${result.error}`);
    // Handle error appropriately
  }
}
```

### 3. Monitor Lock System Health

```typescript
// Regular health checks
setInterval(async () => {
  const health = await lockManager.healthCheck();
  if (!health.healthy) {
    logger.warn("Lock system unhealthy:", health.issues);
  }
}, 60000);
```

### 4. Use Appropriate TTL Values

- **Short-running tasks**: 5-15 minutes
- **Medium tasks**: 30-60 minutes
- **Long-running tasks**: 2-4 hours (with renewal)

### 5. Enable Monitoring in Production

```typescript
const monitoringService = new LockMonitoringService(
  redisClient,
  lockManager,
  workerId,
  {
    enableEventLogging: true,
    enableAlerting: true,
    enableMetricsCollection: true,
  }
);
```

## Troubleshooting

### Common Issues

#### High Lock Conflict Rate

- **Cause**: Multiple workers trying to process same domains
- **Solution**: Implement better job distribution or increase worker capacity

#### Lock Acquisition Timeouts

- **Cause**: Redis connectivity issues or high load
- **Solution**: Check Redis health, increase timeout values, or add more Redis instances

#### Orphaned Locks

- **Cause**: Worker crashes without releasing locks
- **Solution**: Enable automatic cleanup and monitor for frequent occurrences

#### Memory Leaks in Lock Manager

- **Cause**: Not properly shutting down lock managers
- **Solution**: Always call `shutdown()` in cleanup code

### Debug Commands

```typescript
// Generate comprehensive debug report
const report = await monitoringService.generateDebugReport();

// Check specific domain lock status
const status = await lockManager.getLockStatus("problematic-domain.com");

// Get recent lock events
const events = await monitoringService.getRecentEvents(100, "conflict");

// Check system health
const health = await lockManager.healthCheck();
```

## Performance Considerations

### Redis Configuration

- Use Redis persistence for lock durability
- Configure appropriate memory limits
- Monitor Redis performance metrics

### Lock Manager Tuning

- Adjust TTL values based on task duration
- Configure retry delays for your network latency
- Enable statistics collection for monitoring

### Monitoring Overhead

- Event logging has minimal performance impact
- Metrics collection runs in background
- Alert evaluation is lightweight

## Integration with Worker Service

The locking system is designed to integrate seamlessly with the worker service:

```typescript
// In worker processing pipeline
async function processDomain(domain: string): Promise<ProcessingResult> {
  const lockResult = await lockManager.acquireLock(domain);

  if (!lockResult.success) {
    return {
      domain,
      status: "failure",
      lockAcquired: false,
      errors: [`Failed to acquire lock: ${lockResult.error}`],
    };
  }

  try {
    // Execute processing pipeline
    const result = await executePipeline(domain);

    return {
      ...result,
      lockAcquired: true,
      lockHoldTime: Date.now() - lockAcquisitionTime,
    };
  } finally {
    await lockManager.releaseLock(domain);
  }
}
```

## Examples

See the `examples/` directory for complete usage examples:

- `basic-usage.ts`: Basic lock operations and monitoring
- More examples coming soon...

## Testing

Run the test suite:

```bash
cd services/worker
pnpm test src/locking
```

The test suite includes:

- Unit tests for DomainLockManager
- Unit tests for LockMonitoringService
- Integration tests for multi-worker scenarios
- Performance and stress tests
