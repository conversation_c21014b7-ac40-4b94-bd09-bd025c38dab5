import type { Router as ExpressRouter, Request, Response } from 'ultimate-express';
import { Router } from 'ultimate-express';
import { HealthCheck } from '../monitoring/HealthCheck';

const router: ExpressRouter = Router();

// Service instance
let healthCheck: HealthCheck;

/**
 * Initialize health routes with service dependencies
 */
function initializeHealthRoutes(healthCheckInstance?: HealthCheck): void 
{
	healthCheck = healthCheckInstance || new HealthCheck();
}

/**
 * Basic health check
 * GET /health
 */
router.get('/', async (req: Request, res: Response) => 
{
	try 
	{
		if (!healthCheck) 
		{
			healthCheck = new HealthCheck();
		}

		const status = await healthCheck.check();
		const httpStatus = status.status === 'healthy' ? 200 : 503;
		
		res.status(httpStatus).json(status);
	}
	catch (error) 
	{
		res.status(503).json({
			status: 'unhealthy',
			error: error instanceof Error ? error.message : 'Health check failed',
			timestamp: new Date().toISOString(),
		});
	}
});

/**
 * Liveness probe for Kubernetes
 * GET /health/live
 */
router.get('/live', (req: Request, res: Response) => 
{
	// Simple liveness check - if the server can respond, it's alive
	res.status(200).json({
		status: 'alive',
		timestamp: new Date().toISOString(),
	});
});

/**
 * Readiness probe for Kubernetes
 * GET /health/ready
 */
router.get('/ready', async (req: Request, res: Response) => 
{
	try 
	{
		if (!healthCheck) 
		{
			healthCheck = new HealthCheck();
		}

		const status = await healthCheck.check();
		const isReady = status.status === 'healthy' && 
			status.connectivity.database && 
			status.connectivity.redis;
		
		const httpStatus = isReady ? 200 : 503;
		
		res.status(httpStatus).json({
			ready: isReady,
			status: status.status,
			connectivity: status.connectivity,
			timestamp: new Date().toISOString(),
		});
	}
	catch (error) 
	{
		res.status(503).json({
			ready: false,
			error: error instanceof Error ? error.message : 'Readiness check failed',
			timestamp: new Date().toISOString(),
		});
	}
});

export { router as healthRouter, initializeHealthRoutes };
