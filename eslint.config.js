// Flat config bridge for ESLint v9 to reuse legacy .eslintrc.js across the monorepo
// This allows `eslint .` to work from any package (e.g., services/worker)

// const path = require('node:path');
const { FlatCompat } = require('@eslint/eslintrc');

const compat = new FlatCompat({
	baseDirectory: __dirname,
});

// Load the legacy config object
// Note: compat will translate ignores/overrides/plugins/extends for flat config
const legacyConfig = require('./.eslintrc');

module.exports = [
	// Ensure top-level ignores are respected in flat config
	legacyConfig.ignorePatterns && legacyConfig.ignorePatterns.length
		? { ignores: legacyConfig.ignorePatterns }
		: {},

	// Translate the legacy config (including overrides) into flat config entries
	...compat.config(legacyConfig),
];
