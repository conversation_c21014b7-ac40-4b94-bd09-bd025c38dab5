{"knowledge_relations": [{"id": 270, "source_id": "e19b965d-a436-40f1-8709-c1a266dd1b37", "target_id": "df0d55ba-fd8d-495c-af3f-d408e5af42e0", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: e19b965d-a436-40f1-8709-c1a266dd1b37 -> df0d55ba-fd8d-495c-af3f-d408e5af42e0", "gmt_create": "2025-09-15T22:29:29.12402+03:00", "gmt_modified": "2025-09-15T22:29:29.12402+03:00"}, {"id": 271, "source_id": "e19b965d-a436-40f1-8709-c1a266dd1b37", "target_id": "79e52daf-6f92-4d18-a3ca-54f27db2e394", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: e19b965d-a436-40f1-8709-c1a266dd1b37 -> 79e52daf-6f92-4d18-a3ca-54f27db2e394", "gmt_create": "2025-09-15T22:29:29.127934+03:00", "gmt_modified": "2025-09-15T22:29:29.127934+03:00"}, {"id": 272, "source_id": "e19b965d-a436-40f1-8709-c1a266dd1b37", "target_id": "9388a71a-461e-4668-b95b-e789f9f776dc", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: e19b965d-a436-40f1-8709-c1a266dd1b37 -> 9388a71a-461e-4668-b95b-e789f9f776dc", "gmt_create": "2025-09-15T22:29:29.130431+03:00", "gmt_modified": "2025-09-15T22:29:29.130432+03:00"}, {"id": 273, "source_id": "e19b965d-a436-40f1-8709-c1a266dd1b37", "target_id": "646cf5c4-aa18-4510-9f39-7453802d6341", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: e19b965d-a436-40f1-8709-c1a266dd1b37 -> 646cf5c4-aa18-4510-9f39-7453802d6341", "gmt_create": "2025-09-15T22:29:29.132388+03:00", "gmt_modified": "2025-09-15T22:29:29.132388+03:00"}, {"id": 274, "source_id": "999507f4-1d05-41c5-9d73-d4891bdb40ec", "target_id": "80c9974c-bd62-4c1d-97a4-6c43268e5a00", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 999507f4-1d05-41c5-9d73-d4891bdb40ec -> 80c9974c-bd62-4c1d-97a4-6c43268e5a00", "gmt_create": "2025-09-15T22:29:29.134895+03:00", "gmt_modified": "2025-09-15T22:29:29.134895+03:00"}, {"id": 275, "source_id": "999507f4-1d05-41c5-9d73-d4891bdb40ec", "target_id": "559d0ee1-ecf0-4c1c-976e-26fd8543f874", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 999507f4-1d05-41c5-9d73-d4891bdb40ec -> 559d0ee1-ecf0-4c1c-976e-26fd8543f874", "gmt_create": "2025-09-15T22:29:29.138667+03:00", "gmt_modified": "2025-09-15T22:29:29.138667+03:00"}, {"id": 276, "source_id": "999507f4-1d05-41c5-9d73-d4891bdb40ec", "target_id": "81eb5d3d-6a3b-44f5-b9a2-a7a5f7f3e0a1", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 999507f4-1d05-41c5-9d73-d4891bdb40ec -> 81eb5d3d-6a3b-44f5-b9a2-a7a5f7f3e0a1", "gmt_create": "2025-09-15T22:29:29.140874+03:00", "gmt_modified": "2025-09-15T22:29:29.140874+03:00"}, {"id": 277, "source_id": "999507f4-1d05-41c5-9d73-d4891bdb40ec", "target_id": "603b9b16-90c8-4c70-acbb-60e82ab498b8", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 999507f4-1d05-41c5-9d73-d4891bdb40ec -> 603b9b16-90c8-4c70-acbb-60e82ab498b8", "gmt_create": "2025-09-15T22:29:29.142124+03:00", "gmt_modified": "2025-09-15T22:29:29.142124+03:00"}, {"id": 278, "source_id": "999507f4-1d05-41c5-9d73-d4891bdb40ec", "target_id": "8da30d92-b747-4048-afdd-9a665b6ab86e", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 999507f4-1d05-41c5-9d73-d4891bdb40ec -> 8da30d92-b747-4048-afdd-9a665b6ab86e", "gmt_create": "2025-09-15T22:29:29.143288+03:00", "gmt_modified": "2025-09-15T22:29:29.143288+03:00"}, {"id": 279, "source_id": "999507f4-1d05-41c5-9d73-d4891bdb40ec", "target_id": "2d18454b-43d7-4375-85d7-5b67b23f149a", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 999507f4-1d05-41c5-9d73-d4891bdb40ec -> 2d18454b-43d7-4375-85d7-5b67b23f149a", "gmt_create": "2025-09-15T22:29:29.144447+03:00", "gmt_modified": "2025-09-15T22:29:29.144448+03:00"}, {"id": 280, "source_id": "999507f4-1d05-41c5-9d73-d4891bdb40ec", "target_id": "ebf65400-74b3-43b3-a43c-5279fb915a21", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 999507f4-1d05-41c5-9d73-d4891bdb40ec -> ebf65400-74b3-43b3-a43c-5279fb915a21", "gmt_create": "2025-09-15T22:29:29.145736+03:00", "gmt_modified": "2025-09-15T22:29:29.145736+03:00"}, {"id": 281, "source_id": "e38f1904-6c2c-462e-9379-13baf7db544f", "target_id": "a45b65e6-9391-478e-a45c-bc91d342161c", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: e38f1904-6c2c-462e-9379-13baf7db544f -> a45b65e6-9391-478e-a45c-bc91d342161c", "gmt_create": "2025-09-15T22:29:29.148717+03:00", "gmt_modified": "2025-09-15T22:29:29.148717+03:00"}, {"id": 282, "source_id": "e38f1904-6c2c-462e-9379-13baf7db544f", "target_id": "d28216a3-d986-4478-84a9-b69317a679d3", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: e38f1904-6c2c-462e-9379-13baf7db544f -> d28216a3-d986-4478-84a9-b69317a679d3", "gmt_create": "2025-09-15T22:29:29.150453+03:00", "gmt_modified": "2025-09-15T22:29:29.150453+03:00"}, {"id": 283, "source_id": "e38f1904-6c2c-462e-9379-13baf7db544f", "target_id": "41ff8a68-0781-40cc-bd7e-6a1feefb965d", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: e38f1904-6c2c-462e-9379-13baf7db544f -> 41ff8a68-0781-40cc-bd7e-6a1feefb965d", "gmt_create": "2025-09-15T22:29:29.151514+03:00", "gmt_modified": "2025-09-15T22:29:29.151515+03:00"}, {"id": 284, "source_id": "e38f1904-6c2c-462e-9379-13baf7db544f", "target_id": "152b11e0-ac41-47f6-87ef-e29c511a2d00", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: e38f1904-6c2c-462e-9379-13baf7db544f -> 152b11e0-ac41-47f6-87ef-e29c511a2d00", "gmt_create": "2025-09-15T22:29:29.152591+03:00", "gmt_modified": "2025-09-15T22:29:29.152591+03:00"}, {"id": 285, "source_id": "d7a22420-208c-486f-9902-736f90733e5a", "target_id": "d2a7722c-2b9a-4a45-afea-86236fce285d", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: d7a22420-208c-486f-9902-736f90733e5a -> d2a7722c-2b9a-4a45-afea-86236fce285d", "gmt_create": "2025-09-15T22:29:29.153597+03:00", "gmt_modified": "2025-09-15T22:29:29.153597+03:00"}, {"id": 286, "source_id": "d7a22420-208c-486f-9902-736f90733e5a", "target_id": "5f518d15-9fda-4b51-a95b-e7b332744c16", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: d7a22420-208c-486f-9902-736f90733e5a -> 5f518d15-9fda-4b51-a95b-e7b332744c16", "gmt_create": "2025-09-15T22:29:29.154492+03:00", "gmt_modified": "2025-09-15T22:29:29.154492+03:00"}, {"id": 287, "source_id": "d7a22420-208c-486f-9902-736f90733e5a", "target_id": "ca8658ba-d8bf-4e86-bd3b-b3d79870ed14", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: d7a22420-208c-486f-9902-736f90733e5a -> ca8658ba-d8bf-4e86-bd3b-b3d79870ed14", "gmt_create": "2025-09-15T22:29:29.155589+03:00", "gmt_modified": "2025-09-15T22:29:29.155589+03:00"}, {"id": 288, "source_id": "d7a22420-208c-486f-9902-736f90733e5a", "target_id": "88bc0bfc-2987-4172-9cf7-64d2680475c6", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: d7a22420-208c-486f-9902-736f90733e5a -> 88bc0bfc-2987-4172-9cf7-64d2680475c6", "gmt_create": "2025-09-15T22:29:29.156636+03:00", "gmt_modified": "2025-09-15T22:29:29.156636+03:00"}, {"id": 289, "source_id": "d7a22420-208c-486f-9902-736f90733e5a", "target_id": "55efdd8d-76bf-4ed4-93a3-86c874cddf05", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: d7a22420-208c-486f-9902-736f90733e5a -> 55efdd8d-76bf-4ed4-93a3-86c874cddf05", "gmt_create": "2025-09-15T22:29:29.157692+03:00", "gmt_modified": "2025-09-15T22:29:29.157692+03:00"}, {"id": 290, "source_id": "d7a22420-208c-486f-9902-736f90733e5a", "target_id": "9bf873f5-e7a7-4432-98a8-5c6f0448fcea", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: d7a22420-208c-486f-9902-736f90733e5a -> 9bf873f5-e7a7-4432-98a8-5c6f0448fcea", "gmt_create": "2025-09-15T22:29:29.158628+03:00", "gmt_modified": "2025-09-15T22:29:29.158628+03:00"}, {"id": 291, "source_id": "d7a22420-208c-486f-9902-736f90733e5a", "target_id": "6da1d28f-9649-48c2-98c7-10e13534d09b", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: d7a22420-208c-486f-9902-736f90733e5a -> 6da1d28f-9649-48c2-98c7-10e13534d09b", "gmt_create": "2025-09-15T22:29:29.159593+03:00", "gmt_modified": "2025-09-15T22:29:29.159594+03:00"}, {"id": 292, "source_id": "fc2bc0dd-aa39-4407-960b-ccfa0cedf7a2", "target_id": "c0c7416d-3eaf-4e99-a5a6-b261c208b211", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: fc2bc0dd-aa39-4407-960b-ccfa0cedf7a2 -> c0c7416d-3eaf-4e99-a5a6-b261c208b211", "gmt_create": "2025-09-15T22:29:29.160542+03:00", "gmt_modified": "2025-09-15T22:29:29.160542+03:00"}, {"id": 293, "source_id": "fc2bc0dd-aa39-4407-960b-ccfa0cedf7a2", "target_id": "e18543fc-6a52-4941-8271-9df2b10e1f6f", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: fc2bc0dd-aa39-4407-960b-ccfa0cedf7a2 -> e18543fc-6a52-4941-8271-9df2b10e1f6f", "gmt_create": "2025-09-15T22:29:29.161961+03:00", "gmt_modified": "2025-09-15T22:29:29.161961+03:00"}, {"id": 294, "source_id": "fc2bc0dd-aa39-4407-960b-ccfa0cedf7a2", "target_id": "976ecedd-de24-4f72-aec1-7428989e4e43", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: fc2bc0dd-aa39-4407-960b-ccfa0cedf7a2 -> 976ecedd-de24-4f72-aec1-7428989e4e43", "gmt_create": "2025-09-15T22:29:29.163801+03:00", "gmt_modified": "2025-09-15T22:29:29.163801+03:00"}, {"id": 295, "source_id": "fc2bc0dd-aa39-4407-960b-ccfa0cedf7a2", "target_id": "b7cd3781-d68f-4613-8200-6a689bd0262d", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: fc2bc0dd-aa39-4407-960b-ccfa0cedf7a2 -> b7cd3781-d68f-4613-8200-6a689bd0262d", "gmt_create": "2025-09-15T22:29:29.165233+03:00", "gmt_modified": "2025-09-15T22:29:29.165233+03:00"}, {"id": 296, "source_id": "fc2bc0dd-aa39-4407-960b-ccfa0cedf7a2", "target_id": "004b950e-0c29-4eec-8b8b-1c1080200915", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: fc2bc0dd-aa39-4407-960b-ccfa0cedf7a2 -> 004b950e-0c29-4eec-8b8b-1c1080200915", "gmt_create": "2025-09-15T22:29:29.167435+03:00", "gmt_modified": "2025-09-15T22:29:29.167435+03:00"}, {"id": 297, "source_id": "41455cce-17e2-4257-a1e5-127134ddca51", "target_id": "8af464a1-f759-49ce-ae4c-375c288a489b", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 41455cce-17e2-4257-a1e5-127134ddca51 -> 8af464a1-f759-49ce-ae4c-375c288a489b", "gmt_create": "2025-09-15T22:29:29.168674+03:00", "gmt_modified": "2025-09-15T22:29:29.168674+03:00"}, {"id": 298, "source_id": "41455cce-17e2-4257-a1e5-127134ddca51", "target_id": "5e208e96-e17a-419a-8923-17f033a1f8a4", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 41455cce-17e2-4257-a1e5-127134ddca51 -> 5e208e96-e17a-419a-8923-17f033a1f8a4", "gmt_create": "2025-09-15T22:29:29.17041+03:00", "gmt_modified": "2025-09-15T22:29:29.17041+03:00"}, {"id": 299, "source_id": "41455cce-17e2-4257-a1e5-127134ddca51", "target_id": "c1867b41-9639-4f88-b4e7-3238ae50965d", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 41455cce-17e2-4257-a1e5-127134ddca51 -> c1867b41-9639-4f88-b4e7-3238ae50965d", "gmt_create": "2025-09-15T22:29:29.171451+03:00", "gmt_modified": "2025-09-15T22:29:29.171451+03:00"}, {"id": 300, "source_id": "a06c97bf-0d70-40ed-819b-a4df8a9e7407", "target_id": "12df9505-aeaa-45a1-a2c5-14285a457158", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: a06c97bf-0d70-40ed-819b-a4df8a9e7407 -> 12df9505-aeaa-45a1-a2c5-14285a457158", "gmt_create": "2025-09-15T22:29:29.172627+03:00", "gmt_modified": "2025-09-15T22:29:29.172627+03:00"}, {"id": 301, "source_id": "a06c97bf-0d70-40ed-819b-a4df8a9e7407", "target_id": "1ccde41e-2d06-4be2-8200-9107bab70f19", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: a06c97bf-0d70-40ed-819b-a4df8a9e7407 -> 1ccde41e-2d06-4be2-8200-9107bab70f19", "gmt_create": "2025-09-15T22:29:29.174972+03:00", "gmt_modified": "2025-09-15T22:29:29.174972+03:00"}, {"id": 302, "source_id": "a06c97bf-0d70-40ed-819b-a4df8a9e7407", "target_id": "93034c34-d2b6-4ba3-bb0f-457cc2ca6f51", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: a06c97bf-0d70-40ed-819b-a4df8a9e7407 -> 93034c34-d2b6-4ba3-bb0f-457cc2ca6f51", "gmt_create": "2025-09-15T22:29:29.176008+03:00", "gmt_modified": "2025-09-15T22:29:29.176008+03:00"}, {"id": 303, "source_id": "a06c97bf-0d70-40ed-819b-a4df8a9e7407", "target_id": "d1060869-9e81-4c33-81a8-7a85743bb73f", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: a06c97bf-0d70-40ed-819b-a4df8a9e7407 -> d1060869-9e81-4c33-81a8-7a85743bb73f", "gmt_create": "2025-09-15T22:29:29.177366+03:00", "gmt_modified": "2025-09-15T22:29:29.177367+03:00"}, {"id": 304, "source_id": "c7fdf3f0-9a8a-4232-8504-1074f8978dd0", "target_id": "90d48463-c0ff-48f3-a138-9f63778550f9", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: c7fdf3f0-9a8a-4232-8504-1074f8978dd0 -> 90d48463-c0ff-48f3-a138-9f63778550f9", "gmt_create": "2025-09-15T22:29:29.183285+03:00", "gmt_modified": "2025-09-15T22:29:29.183286+03:00"}, {"id": 305, "source_id": "c7fdf3f0-9a8a-4232-8504-1074f8978dd0", "target_id": "e8ec4183-f24b-4e33-8864-2ffc086b4235", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: c7fdf3f0-9a8a-4232-8504-1074f8978dd0 -> e8ec4183-f24b-4e33-8864-2ffc086b4235", "gmt_create": "2025-09-15T22:29:29.186756+03:00", "gmt_modified": "2025-09-15T22:29:29.186756+03:00"}, {"id": 306, "source_id": "c7fdf3f0-9a8a-4232-8504-1074f8978dd0", "target_id": "7746e968-390c-4fa4-a5da-7eb73284e37e", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: c7fdf3f0-9a8a-4232-8504-1074f8978dd0 -> 7746e968-390c-4fa4-a5da-7eb73284e37e", "gmt_create": "2025-09-15T22:29:29.188969+03:00", "gmt_modified": "2025-09-15T22:29:29.188969+03:00"}, {"id": 307, "source_id": "c7fdf3f0-9a8a-4232-8504-1074f8978dd0", "target_id": "10ee392f-803f-4f53-ad7c-61ea41a490f9", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: c7fdf3f0-9a8a-4232-8504-1074f8978dd0 -> 10ee392f-803f-4f53-ad7c-61ea41a490f9", "gmt_create": "2025-09-15T22:29:29.191477+03:00", "gmt_modified": "2025-09-15T22:29:29.191477+03:00"}, {"id": 308, "source_id": "90cccf9d-3bc3-4947-8701-76745cf4e7c3", "target_id": "d2a473ce-6df4-4aa7-a4c7-9238c93d1220", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 90cccf9d-3bc3-4947-8701-76745cf4e7c3 -> d2a473ce-6df4-4aa7-a4c7-9238c93d1220", "gmt_create": "2025-09-15T22:29:29.193706+03:00", "gmt_modified": "2025-09-15T22:29:29.193706+03:00"}, {"id": 309, "source_id": "90cccf9d-3bc3-4947-8701-76745cf4e7c3", "target_id": "93e06c9e-cb7e-452e-a35b-ff28d6790d09", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 90cccf9d-3bc3-4947-8701-76745cf4e7c3 -> 93e06c9e-cb7e-452e-a35b-ff28d6790d09", "gmt_create": "2025-09-15T22:29:29.194747+03:00", "gmt_modified": "2025-09-15T22:29:29.194747+03:00"}, {"id": 310, "source_id": "90cccf9d-3bc3-4947-8701-76745cf4e7c3", "target_id": "9b4c9f2c-e56e-40e5-b683-fcd575ef07ee", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 90cccf9d-3bc3-4947-8701-76745cf4e7c3 -> 9b4c9f2c-e56e-40e5-b683-fcd575ef07ee", "gmt_create": "2025-09-15T22:29:29.196363+03:00", "gmt_modified": "2025-09-15T22:29:29.196364+03:00"}, {"id": 311, "source_id": "90cccf9d-3bc3-4947-8701-76745cf4e7c3", "target_id": "503e45d0-572f-452a-9d6b-1753461b783b", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 90cccf9d-3bc3-4947-8701-76745cf4e7c3 -> 503e45d0-572f-452a-9d6b-1753461b783b", "gmt_create": "2025-09-15T22:29:29.200204+03:00", "gmt_modified": "2025-09-15T22:29:29.200205+03:00"}, {"id": 312, "source_id": "90cccf9d-3bc3-4947-8701-76745cf4e7c3", "target_id": "071dd99c-3abd-46fe-a1bd-560a93532556", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 90cccf9d-3bc3-4947-8701-76745cf4e7c3 -> 071dd99c-3abd-46fe-a1bd-560a93532556", "gmt_create": "2025-09-15T22:29:29.201368+03:00", "gmt_modified": "2025-09-15T22:29:29.201368+03:00"}, {"id": 313, "source_id": "90cccf9d-3bc3-4947-8701-76745cf4e7c3", "target_id": "5b8f4a38-e344-462c-a92d-e5dd543042be", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 90cccf9d-3bc3-4947-8701-76745cf4e7c3 -> 5b8f4a38-e344-462c-a92d-e5dd543042be", "gmt_create": "2025-09-15T22:29:29.202471+03:00", "gmt_modified": "2025-09-15T22:29:29.202471+03:00"}], "wiki_catalogs": [{"id": "23e969fd-eca8-41bb-bb1a-d40f9f9756fb", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "System Overview", "description": "system-overview", "prompt": "Create comprehensive content for this section focused on the domainr system. Explain its purpose as a domain ranking and analysis platform that evaluates websites based on performance, security, SEO, and technical metrics. Describe the high-level architecture including the microservices components (web-app, worker, domain-seeder, admin), their interactions, and data flow. Document the core features: domain search, analysis, top domains ranking, comparison, automated crawling, and ranking calculations. Include both conceptual overviews for beginners and technical details for experienced developers. Use terminology consistent with the codebase such as 'Domain Seeder', 'Worker Service', and 'CompositeRanker'. Provide practical examples of common user workflows like domain search and analysis. Include diagrams illustrating the system context and major component interactions.", "parent_id": "", "order": 0, "progress_status": "completed", "dependent_files": "README.md,CRUSH.md,TODO.md", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:36:59.650691+03:00", "gmt_modified": "2025-09-15T21:40:47.035933+03:00", "raw_data": "WikiEncrypted:gWB8HBj+8+/15rQhXgtMjECeCGuLoR2IwruDlfaI6j3yrxjzUYfuy/PkXL4s2fe2Q/+ElQvzrakKLa+r4Z4VhNnAF2F2qnko2/QRvNk016PSaisnma0LMoxqhHPc0j4rwc3EEuDcSqrhCfHKxdr/StBvtwcvF/tqoDZLKzaduU7xvxrFhMFN5kJ8JjYQkpyhYFugCmDiJCWR+c5SFTMa2bw0ezfWRXle/835JcHT++ITY+N2gNc0JteMpFeuGNrC2sxNQgRmKP83vZ5onMWIjKrnnOMu6b4x5Hn6RbCxqe8NjRjY+9X/epVsPcM9RS0LKEWMFaaIIYd0/WuJVoln9CcfVJwWLWhnMzxmxkhJlCqOVP/ZGolMbD0ot04Vph+b6xrXoUmfVGMhTdODbm/hVI/5I400aIOZLkEPgYDXriksYWLAvKGAt4qB3UpvprCi/xwKin8DRtQNKLwUzrcagIn27wurkmFsXw1cethbSBCal3KaeMQq5KXtNnzTVdhYvyjLKJnjLGx0jLKafMGPoviwpxJcNGsUD2F/DuK9jZU2HFcWMmVh+WauYALE0br1Otv+Pyx+LrSf/sVp923ktNXMvzVlk7Mdy9oj+VF9bEXJ/s+UweVb9zkWGSN6F9Z8w39c3NqpuNZQdpoQ9e+KC1F3pAFTWf0Zv8HQH654AEMHiTgMSAtaiRQssvxJCiu7GQhphj/r+uIlynup65Rk7Oe1481TkVOE2mr87r7uHvpaoF1URPgxXgZfTCeJwQj7AURj3q3wHa7X9lkc4OoexOZGVo46c0ssfoAH/PqEcZdU+eNee4Ka1SElCirR5lE1R5FMTKlowWfc4J0WSFmdSywGIPht9altyUKoK4+ruVtYL3FKagsHAknkQ5RBT0q1J2hFz9XGOFXXG1Z6gHs6NsLmV5EVZ45OKdLk0D/YdggXaZOcZE0Oc55G4FJIpLZ9lBX6H0XsUKWj4urleVAvMOTKwlifKmmAsmbbbQ4NPNscNsEC4QJ26TA1SwOxDZpjzKlspZ98BTtcapze4orjl2DnXDP4pv/SoJnr+tanrBSYiJNBJZHagHqvUbaHC/FF1huZoI2ewYXY9p79VoxaY80Cpnq4lSG/PKV0x/Ny2qz2Pv1iBjJtnpGt9rmH9WBmwCsqcthkW0hFnK/btGArZhARZhshdp/uJdZ2EO1r9QQZtnWhxBDwCQDc2LBVAogZiyfzLY5YMCWrsHRxWSOWM30AaeqEHmf/UN55MFZn5OUY7nvRRpePvczT7XYaKgAsfXrrcK95fuEgWtzBUC2XriZzc3VyYeU6Yl5eG2DK0A/2mEv5Wtl3nK+10isSYYRH50wTuYxJaUOSom2+FSIyR4xVMhFeqtpdWXqsnI5fBm+jm2sUWBt5yEcsnZNXW3n9+2E5gG+iK3zM/IWfq9kE73KfXLFFhyaixO3mgdGdmI1yI9WxkNCv+rKPePjLltVkXlEiRRrQ3zIAUjsTThU7Sj3tG3i+LpQUPpJrzjf6slg=", "layer_level": 0}, {"id": "6a6a66a3-4597-423f-a04c-990680971ad8", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Web Application", "description": "web-application", "prompt": "Create architectural documentation for the Web Application service. Describe its role as the frontend and API gateway for the domainr platform, built with React 18 and Next.js. Document the presentation layer components, API routing structure, and integration with backend services. Explain how it handles domain search, analysis requests, and top domains retrieval. Detail the interaction patterns with the Worker Service for processing jobs and Domain Seeder for domain discovery. Include information on the service's deployment configuration via Docker, its health checking mechanism, and integration with Nginx. Address security considerations, performance optimization for SSR, and caching strategies. Provide examples of API consumption patterns and error handling in the frontend.", "parent_id": "2fd75159-4155-48d5-b146-4fd895ab5d86", "order": 0, "progress_status": "completed", "dependent_files": "services/web-app/README.md,services/web-app/Dockerfile,services/web-app/package.json,services/web-app/src/app/routes/api/domains.ts,services/web-app/src/components/SearchPage.tsx,services/web-app/src/services/DomainAnalysisService.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:37:21.304642+03:00", "gmt_modified": "2025-09-15T21:51:58.053751+03:00", "raw_data": "WikiEncrypted:DWTNXhY5YnktqaJGZiAVvJj2aCxI1mpHmtq+KBfWhZb1zVLZxUhItkg7yEfGRePZoLh3Q95u8SlKFpHGGsnF8/1nh34rAnPpgpFYE6u4K9r54RIqEPvW3qScH+WwFkkVCXHhpMzLz0vL9RHt3x7rUv6ydgEKrb/14men6fST/rrWNplXiX2HwbrqMQ1AHog8gYE9ehbZPHKlANbB9wmCoeR/y+ytbm9bJzVU5HE3dLVm5uuPTfhLQjgKg4XpymhsRD9GbnTS5Yq6fHLJE6ZTExZEn0QkGyrbksoT23yuixxtaUTQEkEkHs44R7RQOwaQ+N7d9bkNDwPZf4Gb7naidyBVyPR6JIHKweFiW4mCklWUtjqZji0+0EfGnIIBTi2172MELbSDxfqxQlmmeyL+yjYdGxNZAwH1auUYWac94LzovswzMezii5Rvx/iqqVIwWQ9cTCSXDnxxsq8dkH+Ca6jFUqEIYMvW4Vq2smn41Qyu0Rb8yferH0CGM+EFFd1V85DXyTG7gQUkZWffpl/dO7rcosKlqFV4LtE8HulXfJy5OcnnK2GneV0z8EPolKAaXj920OCx4eZdky6CCNLbkRn1EBxbD/R8vOEgrKJrvDhV5grcXGb1dneBvhrqJ6kelFf7cC9fAmzcumRnZHA+SYgt/7fz0IhinLs7A+zev0/3XHt+CYXS5a7CJledQJTtLBFM1x5n0w2/rK8rx2OkCbj4TaXaWO0LKlShE4SWrXswhGp/CWT5LdoDPitdSuLUA3fT2vxOTZvjU9Z3E8FtXeph8sDEjZgZYU31TROHfZQDNE9f1BSbWJdal0rWQOqivnvWAB15ikT/x/9e1K1nVMB73Q++O0qvK+eiFzBcY+mv1CN/Hj3qgBtTy04ZKwqYQy84YzyX1P2nmvFkH9RUpNkVo2MnESnEr4y/or9L6jd/opIvOxFJm9ebyELDCKjUnqIbw9bBYdhg879/vbdwtbc4VgtZNGML0+7/3E9d8B7QJKMmPoah7jccrLEJE9/1HA43Y+zYFhIzHmHOwDhb8SojnPYld75tdj+0/Z7Z0Tc1xdY5DDLjbl767X2SHy/k+rKF5D6/yHHWcGZRpMF+f+VKYugID7IVFvoQWFzjA3kVhB7goeqPabgXtpQFJrIEwSswA0gp6sTf+ZKix89QEtk9U/hWcKciNl0i32Cp1jZM+HQYAIr8HcSFHmBUs7CbPWrSK5Sh1/IwHnL2WHnJeYIGfhWRWEU55TEuB/K24/tyUrio7oHPrWuK7XobhElXrqWZqiC6whEILOezI0U0Y3Aytm+XoQsf0jKBVV8OmWdNhTeAOIYm5+u7x05M8IQI9Md5J92WBWYgI7tik97CnVeSnb5kbN7gFSk8BnXRqiFMRyCISkTO5aGinwLTkfvb/avpRBi2P75RGKMzLpexPxZgKrkAvl/RL62/St+PLqwLOWKSe0edmRhRcUG/mqQF0oYBoOI6gnfkI1PchKxjJm6ut0O7t+z/uj2LGz2obn2pc7icdw0tYtRaVDI+QoeRYQ/vCHgxLYBXC8Oz5JnfXixiN9ZqdDEmEUJ/5tOFieNCmpVkP12ZHjE7IbojKYoeHa0r25+PU6T0QDsSZUKkTeT3r/ciw/dub7rWncBMvtv78PJHuc2YxQofg9/4kVjSpaQHNoFgn+iwiKexo8J21g==", "layer_level": 1}, {"id": "9deb6e28-4945-403f-bbf8-ccc5b77acb6c", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "DomainAnalysis Model", "description": "domain-analysis-model", "prompt": "Create comprehensive data model documentation for the DomainAnalysis entity. Detail all field definitions, data types, and relationships with other models such as DomainCrawlJob and DomainRanking. Document the schema structure used in ScyllaDB and MariaDB, including primary keys, clustering columns, and secondary indexes. Explain the data validation rules, business logic, and lifecycle events associated with this model. Include examples of how DomainAnalysis is populated by the Worker service during crawling and how it's accessed by the Web Application for domain analysis pages. Address performance considerations for querying and storage, caching strategies using Redis, and data retention policies. Provide sample data and usage patterns from actual code implementations.", "parent_id": "54aacf35-eba1-4936-90c8-2768157d3278", "order": 0, "progress_status": "completed", "dependent_files": "shared/src/models/DomainAnalysis.ts,shared/src/models/types/DomainTypes.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:37:25.737083+03:00", "gmt_modified": "2025-09-15T21:53:43.948042+03:00", "raw_data": "WikiEncrypted:JLXqlo28VAn5UOPLsLiQgefqtVpQU9u6XYfkNTu6YiWpMXHc48fZ0OnGmz6JcyKriYVcrJ+ytmOzV9ibltnlVJ/2gdmOPA1AIVonKetUWKItBtXNWdDpv3U7gLLZobaysWMBIM3XFIgKyxwxhmGbmSBinOtdyQ0g2oSZIfBw71ln8jdZQuV0ezyPteU2YH64a5QkZvzIBvPm9T3xu8mr7mZJiDf0s7YPy/nfQM1R0i2ojcMaAar/IcTMYlLu1uvHR5bBpbjh997uzhH/VRV3SrQCEZ9ymdBrfwqVV6ZbNnFYH1V7TOE+JtHMkuNARpGkGM75I/WsDJ+FcJ9VtxwMbd6ROCCu8tJsMPA6KkMDUjs8Ebz1mX2oTo7qo8CRoQKI/1Frk3Mb7CeAu5XpJs8GgJx5kV6gEZAPtONu4ses5bB8tPLpSXBhLrtMrLBZK/6uuWoe1WffGKzqTtsq1B1bpyGFCMBFzGglu2LQwbTUV9YVc+ImXuyj83sxd9ElcCq7+CsmwgLxR01NyHj13L1knpq8eYe/4lQ2iOngfGsAfy0PH1eGSAo3Y2+PDsJ+Y6yY2pHWYnrdApNSFy3GIvdnkvcde65WZ+/sEGLLtT2pdRoUH0jOTmEeiLE5KS+nPAkhV32VEW6jgrkDMIqDQmca+UNH1fY4Tpeau7fdgBqjc2JOi0AeH98BCA0w6UVgVqgw6elM7cKOw/GFUY5TZyGh65mjB7Fb9XVPvVW+jubj/UtBhLEwrBzF4IYgfaGSdqzv1/8ifIDRl+mhiyiwhfon76FIpGvyYg7D2J8N05eU1UE1Q8Ms0BEOyiKQiQjp7d6DSEkQc5IFIvDTmjWftM61wSphAo5pONDNlmQsQ2ghrMUl+OMnqnOxO+tmn4oo5+Lj4SvSAbkC5yP4gMIMALAh/NMf8JmWeYbX9i/kImntvsW6yFSJtY3wwW7spiUttI8H/+tHb4qK5qT0jx8l+YMn1sk+rvdNUb9wpPWzX5rB3eofE9jC9o9nlmYDouFquzOO0nMeKd6mhkfNUVIZmtEeXjnf5J+NXVYlJXdIoOwFH/8RPaXtrTh2DdV6AVh/YCp4r/37Fu7O2s6hhPVER9t2xjMxaz+0SChpY1QdyFYIzBQbQNljOPav20TeO1frvh2NOr28WlB0p/urpzr/4R24SKDKUkDCl/w7L0SCnvi/zjTmmRR6Up+zwN7jnuMLJp7Neynjk4CQmjShK/jaSn2Wp2y9vfbvi2QObrVEVYS0Q3ZXMXlGmsS7gBjlfqMhx+2vZkQGUttgVX2SDCBCOjtl0CbdBlkanvj+U6tl2C1rgfjHLouM1/KSyJ4pAMDqgC3T6JgcisyTC15AvLmtyGTdrQ7LyNZR/Tlzpvw5tQ+EwBaI1fEvL9D7gRDqdtiD8Nw0ov9PDQ/tr7YUDSag482ijsgQ8Vv8nGTMNwo5wOocmoCiDvrdjmlvXGMdqWl3lv0W", "layer_level": 1}, {"id": "faeba3f2-cfcd-4ca8-a192-3d1edb28dfcc", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Domain Search API", "description": "domain-search-api", "prompt": "Create API documentation for the Domain Search API endpoints. Document the HTTP GET method, URL pattern (/api/search), query parameters (q, limit, offset, category, country), and authentication requirements. Detail the request/response schemas including the SearchResult model structure. Explain full-text search capabilities powered by Manticore Search, faceted filtering, and pagination implementation. Include examples of successful responses and error cases (400, 429, 500). Document rate limiting strategy using Redis, caching mechanisms, and performance optimization techniques. Provide client implementation examples in JavaScript/TypeScript showing how to handle search results and pagination. Address common issues like query parsing errors and slow response times with troubleshooting guidance.", "parent_id": "4cc1d72f-ebfb-4439-994d-803e620048d9", "order": 0, "progress_status": "completed", "dependent_files": "services/web-app/src/app/routes/api/search.ts,services/web-app/src/services/DomainSearchService.ts,shared/src/models/SearchResult.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:37:40.423994+03:00", "gmt_modified": "2025-09-15T21:54:05.733807+03:00", "raw_data": "WikiEncrypted:JLXqlo28VAn5UOPLsLiQgVOnDuGck8J81xkkZUi4MM90Hul3pNLt4OiVeRfI+rQWK6v3OcrjAQvj191CNcd6e5KqFwjhnm/9C8nRz+h4KeuBUjBQTI9FssG8XBc1NY3x/utEjAiLf2vlw+at84po2WJ1rY1NOLfLa2w/ziaYZfAQ5CONqYw9xZZKaRl2CPEusvkjSWRp1C4LHioPqGRWFqYBMl5LR4t8XAYNJEa+V8Rf6BBZn5dB/4pcjz0pwAMt1N2cfzNf/XeTBW1+Bk4Q/f30RNx1fs8GGgWh8y7ts0Vcj6KnmSZfBh4wgOwBYST8zGBGu3POpUPhY2oEGTCCbvPPPgshf4oGw/1VV25zCVQ0qajkaV4VSEnFgp3GVX88eILiGf82WYoaqelruqq0JlAvAtQ6HuceUJ6g0K7b1xytDm9wqpT2bT5smLfe8+vgGYJq1LbF2KmG+WnB9rwJbyRljmcPjcB7c2SR/1DJfhPZyvZtVmqQlVlEYzPqgm7ovAIl+oVAjzCprycyh5Mf5JGTOzLNk1PzO7TL5dEOrQ7IRD0uTDy3ABzhsW04OKm4hApOgqM7LC6qlwHwH4Kz1OTsHrvTNfLQZswS9Lss2Nh7Y18JV5TDyhY31K/gtnKAwwwlLUHr3kFE/oUPgrmlueMLHrqil2zNp33dzQJmNf9WoXmtt7AHeo6WCVlZpRGkzI3JhnAo3BP/7cefzlyosnZ8YxM56TUZCo2SxDwrx2cVuTfzQovvKpcQjYy6FpFdj35NZ0t+tDA4KZmVcK1JM1ceNYWqsGquYPEHH3ZOfSoBLhC7XvOLV9ByI41js2REysGBNOoXFWHaDHYHAWzks92USQ7Gu5SM4G5PKBtHI80Nhx7L/9NAGDyZHZ2wSZouYefYoRXiOKZLGG91DZ68lonHflUEJS7YDxDU1d2xSud3fHefHIbQKrFExSO8QfUqGuXuhqshlmNPyRzTl0vBq/yTfxuOkrlV9N8XPhzNvQ72N7z66siUm5d7uVicYmqV2mViQyqy65p1PI1PCOTagJNJA1wp2y0tjYHM7LjDYgUTIqkVyW/5AGYdPdzaymVjtxVcCeTRLvHE8oqiwaftiZjbtqjSeQoXkBgzVdU3kLmcZj/ARLI5NvYI5/IjrLnLgwIpwKXZAB9igJXQSxkl3NE5AXbOqv3Xox8BnjjwqT8qzdO2POZ/sDstGzkkeVUVPPnhXVRi+gt+ipkzFqgxNEpNW5DeBTljIfCiaHKbWLdS27ybz+a581fGkE4oMHUk2giYVWoQStqqE2S+HRT7doqmfKY2UqJ/txyrAceHg64q6Por/0volPHqxWm34EVE1w/bn7DwWgowHZNkhfwk8lROBy2KV25keEjzEeEa/Z6o/qoDTTEyHkJEmw29zE1CAsP68KXfum667xAcd3PH8Nk540EveGl2WcJiSejQcZrgIb+Vhqng50ov+Qzh1l/NcaH1x9PVeVtS7d4BT5CGAPkQJTFqi0M85jyTVcjUQ1hhumfNnEcTilcXI484zqBRDGAFyL9I8TNjmCSmsZFHAVAdI186Dc67t1ffVkoEUV0=", "layer_level": 1}, {"id": "931fbe7a-d787-4335-9e72-8ce7b2ef2098", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Domain Search", "description": "domain-search", "prompt": "Develop detailed content for the Domain Search feature. Explain the implementation of the search interface including SearchForm and SearchResults components, and how they interact with the DomainSearchService. Document the full-text search capabilities powered by Manticore Search, including faceted filtering, sorting options, and query parsing. Include examples of search queries and expected responses. Describe the integration between frontend components and backend API endpoints. Address common search performance issues and optimization techniques. Explain how search results are cached and invalidated. Provide code examples showing how to extend search functionality with custom filters or boost parameters.", "parent_id": "e3ce4565-5d2d-4e25-9f46-14b64749ee98", "order": 0, "progress_status": "completed", "dependent_files": "services/web-app/src/components/SearchPage.tsx,services/web-app/src/components/Search/SearchForm.tsx,services/web-app/src/components/Search/SearchResults.tsx,services/web-app/src/app/routes/api/search.ts,services/web-app/src/services/DomainSearchService.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:38:04.736634+03:00", "gmt_modified": "2025-09-15T21:55:15.478188+03:00", "raw_data": "WikiEncrypted:JLXqlo28VAn5UOPLsLiQgVEd4bem+smfupZ3adZaSebz0PCocwMyQDq8/55U3FoLRHwVV/HuEZi+xcygGLq0yL2Hn+wu3FTfAtCKOb+rr+Y2mC901n3JmCnnU1XElTSWH0Do8Tm6+k7bTekuvII6d0f85UHQN2iFfMlrNAXMIpq46J2vEVnHDzvGB8DLbX3uUTAgiBnTFknR5/uziRg9VUXSNxY/Mpa6SXYQ5rdXyxpJ2u9dl6GzaPKkytQN1MS/i0BpdShBkm+0inFMQnqQi7DH54G3dO4A7644jPnl/xTOxexYntwA4yyfEyWjC+jL9IwAIJe8u6+RnNUDcCUBZCvZkJPMRDtpZ0wxFhfEJoyCIcrJ23KDy01lYlTPSldnnZWw3tiq6JClQQnADBTgssO+FxNrwhfIPBvjcMU4HcT4JEs+GPj88nWaOYQa4pR+3cQoV33ei+KeBLblNeeEMfPXMRJOplxTeW05HwKBGrr3JWzZG4ZFy+ndjvY9lQ9nouJO8/AZ1JZv5dlWG5f0kRh7Ykgr8uw3NGrXdTGe4GLX6wQsaq6wVsjEJ1xO/5+sRlAX901TxB+uOwAmNMA5HQ0tYhPF3RqO97iuBELdjHow2av41sAYW1a7o6ZdV3WbVKIEIAuOXfijayT9XYWDHbvFAw5OWr9Frto7kO+znrFfbwibyAwCrkQbEft5l1231jY3hKG398yYoOS4TBzwZcpB+gEtPNXVNkxptt+m/O9nnWOBONhZpbXIQXRTjxZkiE5wlvFNBY/ICcKIxiXHbJkLT4L5Etey8lSu64C84HX995pud7RwsP/xxrqDPbtLnS05IVyZH+UN19r/zjy4WoYHPMwuIGHsm7bEoC19yefBN5iroUnZUpdqMi4yPMvs3hXESLuL4JgrADfidq2RhmHyNk6Dz4mhSyJL9+fq92awFO2IWvIL2BkJfjgdz1+IZ7ApFq91F0hN9LPRCAPtrHmAk9brc7JSzt4j8JMNtwme1rFe3disV0mwJ39U32HkGiY0J56+JLfzlwGTIWpC6bSdXsyN7k9tp5g40WcX/vZHzpU2AmGKaa8VAHftvQmCQINYhNBUSUUI7pjMYoJnd5MX7kxkIBYJnbjvuRugyuNSE3bPwWSqc7bCkUUnwKTYRz7yPUWAwgDZ3p7+DmSl70YYre/UM4+NpYaDwchXWLBSfaJKXhelzKnMazpezMOFb+EeP6SO799YA6gtOves0EjA1H6/aoJ2z7yILoLMjh79qkGLBZPeSgK0Lpj7LMcKFzT3jJfz61624GO1n+mlfvxTTJssJHW3aLqNOJ82x0NkI0/DZQNcE0PpGwnQRrJUkB1R7pHU+2wp1ehzvskx3QQPZYLVx0XXNTIBC6OgsB00T1Am7b7OTuY+/qXgV2Tx437HFmLhtC2YC1/0mwHwHEy+nk93dRYnXZTxCTxWxR0ffFgjfYad6cD+CGEb6qpSScfpyBhzJ9UG099ymzyFOPAK3n+vczcmPJxxZzjMVOKsprSgYjT70Sp2tAOTH4f3E75f6nDicTXsnet064Xo51xfNi/YICtF72wNHMT07zhQJwwte7OS/aO01YaQgX41tPFLKWRri6FdZpL4zMr6WA==", "layer_level": 1}, {"id": "2ea74480-e59a-4cda-8944-b19f3a122786", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Weighted Scoring System", "description": "weighted-scoring-system", "prompt": "Create comprehensive content for the domainr weighted scoring system. Explain how the CompositeRanker combines multiple scoring dimensions (performance, security, SEO, technical, backlinks) using predefined weights (25%, 20%, 20%, 15%, 20%). Document the implementation of score normalization, aggregation, and final ranking calculation in RankingCalculator. Include examples from ranking-demo.ts showing how raw metrics are converted to weighted scores. Illustrate the data flow from individual scorers to composite score generation. Address configuration options for weight adjustments and their impact on ranking stability. Provide guidance on performance optimization for large-scale calculations.", "parent_id": "86fb1ab3-0d9d-4f57-bdd1-151e7cd91e0e", "order": 0, "progress_status": "completed", "dependent_files": "services/worker/src/ranking/CompositeRanker.ts,services/worker/src/ranking/RankingCalculator.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:38:05.719468+03:00", "gmt_modified": "2025-09-15T21:55:38.816399+03:00", "raw_data": "WikiEncrypted:OIjw11orMfmQmqdsZyiVhI3BloA6IRiZy2bj1brpPvoY8vkAX/60C9zyVXIvnTmbzfhvagi/FPr9HKZf8euL3YwPnRIMRAjI0MVbVlaep1SBNqJP0ImmZtFf7XlUsWrV3TKvLK+Nl0czROZY3QCICmQZ0OPOjq/51s02oo/dAxRPPOrzY4gET3GD8K9+NXdm60dHOHdyuEfQiZ2SDu+IgnbFGnWrxXJ4Yp4U5Z1iHDOnAo/lLek0dwGQc63OMhs8aXSSOD9cYxv3/MhzTlEAavdsY+h93IOmf49XZ/WQmBzj4HpbwJ+/oFBVdcuGZfsmQKskYiMrrPTISCAL5oIwJGfXD0kA5vHYfWg66N9p+YMrmS1HdpUVNP39ukK0Nky2E+I8FvxKhqG29oFEje2/o2XIXITFre2KAVxTN8WPk6m8jL92HBb9XmDY9ueHGQhfNyyDl5oJkIg+yBgn2fPT7O6mW2aE8t5zz67SSnjt1Z1Ug1wBU2dKPj/Zw+KR3cC/laaDbm3V87SdyCy7tILofeBeBJdUwdm7xgefkSy/SAiJ78ETXfHbq+r3697Lta2i+OTNYCmYUbJOmasAB/S0yfc5kanEl97+GFr6gUQdkBfKpx91U7QgLkzMvMIH8OGEMlGC3EYKeXJCTDs7qyykPJXnJIlq4L8e9kKS6oORrI/p5CNiCl6gsw/G071ih+6PlNfJu6GJObmBrokMOiiaGN4F/PT0WuwO/rYOiq7tPF2JDEaP4Sd5emlqjSiRTpxT7OjU+KzojEpYBLjGPDGiGqoamBc3eGmjRo9Tl2hIUQGcMVtQp5HK5tePyxiBxryOMnCvy5zqwguyMqU8YjZ7HlEH3DsTQHXhwWctODjmaAaSXkPKOe3Fh4ndZ+qHu4NMqwqp5UpUl//T6GMMMd/dTkw0WMoe13jl7b9Q8xro2UdfJLBTX9CYRnCmazsoqDA1j7ugZ8GxyqWjp1XEZBmBIM5pM2p8bmVuAKSdy09b2M5aS4Y2Xqn0cKOl6MtkGMoHg7JlBDRU3/SeruiawfMhHNX78A/2nVDb0iV4TK5XeityTIplxkCTuuNFLYFDCJqZdIqExIBRxSuPNAGOVlyl+q39tFvpvrJ30kX+KOibhdytEyVt7N49kh9PdyChZ3RIREpLbFiwkPDlTLI3SuEvB76d5wwVKpXYZJp0rSDcujk0ZYZqgaMz8QDBT8TvLyVuFfimmcCDllyZbhKy87BrP2jaFFtXqDWuUrIqLNGBZ5JX5E87gpZcdGXS+otZd3/6t+OUX9UBXp+fmY9ShQS317YyqE2M2C9jkOgYrO377TO6abudoLD2iXa+AmtoKAGKr1qp/nHeSsLCIXzCEuTW8vr64Uuv83g1FyOk60JswhqERPY/o+jblHDvVa65aPxi", "layer_level": 1}, {"id": "d5a967f6-64bb-44d1-9ce5-9958cd9686fe", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Crawler Architecture", "description": "crawler-architecture", "prompt": "Create architectural documentation for the domainr crawler architecture. Describe the high-level design of the DataCollectionOrchestrator, ModuleRegistry, and SelectiveDataCollectionService components. Explain how the crawler dynamically selects and executes analysis modules based on domain characteristics and resource constraints. Document the component interactions between orchestrator, modules, and execution history tracker. Include infrastructure requirements for concurrent crawling, rate limiting considerations, and resource monitoring via EnhancedResourceMonitor. Provide system context diagrams showing data flow from job queue to module execution and result aggregation. Address cross-cutting concerns like error handling in module execution, retry mechanisms for failed analyses, and performance optimization through selective data collection strategies. Document technology stack, third-party dependencies, and version compatibility for the crawler core components.", "parent_id": "71a79def-fdc0-4d40-80ea-b74ed07e1196", "order": 0, "progress_status": "completed", "dependent_files": "services/worker/src/crawler/core/DataCollectionOrchestrator.ts,services/worker/src/crawler/core/ModuleRegistry.ts,services/worker/src/crawler/core/SelectiveDataCollectionService.ts,services/worker/src/crawler/index.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:38:27.300383+03:00", "gmt_modified": "2025-09-15T21:57:21.632489+03:00", "raw_data": "WikiEncrypted:GzCxGfjsSu4JWLTFBL4DuFr1e5rLeBAWIcbDxb03fSzUIiM7+crsEWzz2ziJNQvZIu1TvGCSNDLEuGFU9pC+SqEHdVVTsiheeJVzk+TZ0cI6xA1/RrmWsOCK4xkDcvdTPrLEy80QkDB0bajbHnDgnjUVzieKCOUKpx+lkSTEYxzt/ftyJHBlIPVote3E5zD61C3QEFg+DyYlKgBUKzg46fazFHjEfkObYm+ZFXB/yWQ6bsCn6DVSlBcRV1LmCGDwX7h9uDFISNGztEJ758/dgjDTV+YO68EJmSKvdqnMXwjarRyhUUK2fj62UScs571GdapKgI/bLnqLk/tUBsKjj9zMjSoYg0Aq0rV3AEznlqAg3ULG2USY2x7ggwN3jQwdzUdK6D+7y9XAHZQrovArNImeFVcp/B7Z8f8mqqT5/HK/r5SfQYNlPPjin2ZmA8ta5ihpLTFRZiCpF9/94qT3QDo3RQPJWnLsBMuVhkalJ9uuaTypCBQM4DJipcX0yhH3SdJ/S3/KU4lz1Ti0L+7StvQZ0vSuTvsrbsLxC0tE0X9N0/5k3eNbX36urLRfLKIxUTjc4d4pU19GB3NWquHOAQiAg0Nt87Shp1ZJe3VZIwu6wXOsIoeIH7LZMfuC2GFj6vmym4Ic8c1gOx+Ki5TzLjwdjdPvvZukUHRLPqkKEmDCOZ7KOJWHhJKuxkKWiEX/u6lzskb/fi7790t0bliDigGJVPf+RBu/iLW33nnDVyTrBm4VqVuePpaveuMofUHu998nPr5OJU5FtJE0vlvU45msweiSCRF8FniReB8TNEUOm3UmP3RMCTAuvwipwk8R8jjkEOS41fDao9nEnCIj65//3MZk+QaCCCoL5qj8UCjTBch5Ce2eS9mFx8gOaoO81IvyxZep1SKn2j+8CQ/JNeV9fR0wwNULiqLPXRaHbJWS2gN62gFfRUOFlnAPGzfPAuiCoqP+svSZVMoJa9wpCZpMn9vqHuIGsWyN4jyT2T2jV1ZWYT5q30z9Wg7sA4pONXXH1lk18Lvog5Q9O5RZxuqTItCIksLUfQyknGu+u8HxXyGkoSKCKoAEJy4EVuJmkO4gpG4fmsmSSXJf7O2nzUoBkAEorYAnBjD3frkkMjkgGh5zClel0VeX9bk9jpEIhZz5+LBde7upg/SozryQJ5uP4lPRcrtPgU2Ow37lgCAlvufOl8+tpfZ4DC+u0ybWxd4WjWvGN5MIIHKABXn12JsmMu9JRtiqNeth0TC2iip7r8hujzjGqwHqGVAiZWxXEA65wKvnYcvKUJ+QdYKZC6rGSksA5OnmMupkL16o5pkaQTmaIEnf2Y94mz/7HV0YJfZr0wSgBjDq5bRo2bwAZ0Qb3yqcHl+XlXwEJAdyr2mSdsyYO1T9yHzFu7lQA0YtDoGlgqzuUhBZ0n0zYwvaopMLoC2HngEzRakLEg6Tp8QzQF6Q+rDaoS0DQAl4tgQTn3wQA+IcVF/LWrHE9/n7HGNQYwiIxXg7lfJYEUT4OZcFd7W+Pk/MVkc9wc8kTGwFWKW9j1JwMyFm4azbfn5NMT61FbO//raRFd4f+f9gv9ujHN7vw5UtY3ixuysGaNnmXU8GN3ZM49SRlIwFz/kUY2ePZ25QRWLb6MwdqiCjq3lclVOVRtGoqYPKDUm8gNJckjqssgAvVs9o8vtzMb7DWkkf0vRGcNgF4b4F2I3KeuEEK8tNau+uqAkNJsmxaiBSnJEOxCpWe1ocNsofDD+EGVyWqi+hVWvjfNea+8lmFHeJOkuEDsK20RcJeJcFNTJA+ysYZlU5m0C6h//9RG//3zIUu8q29MqDehGoBt5Z3umAeLwgmm+QW/PvtzHdZGJcBM/lEBVW4UxHzlqIdDI52/KKRE/KJPxkAvvcUEBd60IZky4q15+8k/8ST55JiOWradSROZT5Qp2qQM0lC6H9Mw==", "layer_level": 1}, {"id": "822ab717-f6fe-4eba-b5a7-232d7aabf8c1", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Discovery Strategies", "description": "discovery-strategies", "prompt": "Develop detailed content for the domainr discovery strategies system. Explain the purpose and implementation of each strategy: ZoneFileProcessor for parsing DNS zone files, LongTailProcessor for identifying low-frequency domains, TemporalAnalysisProcessor for time-based discovery patterns, and DifferentialAnalysisProcessor for change detection between datasets. Document the shared interface defined in BaseStrategyProcessor, including initialization, execution lifecycle, and error handling. Include code examples showing how strategies are registered and invoked through DiscoveryEngineFactory. Describe configuration parameters, performance characteristics, and integration with the enqueuer and normalization layers. Address common issues such as data drift, processing bottlenecks, and recovery from partial failures. Provide guidance on selecting appropriate strategies for different discovery goals and scaling considerations for large-scale operations.", "parent_id": "ddd20eff-e57b-4e7e-8c38-46b85ba85130", "order": 0, "progress_status": "completed", "dependent_files": "services/domain-seeder/src/discovery/strategies/BaseStrategyProcessor.ts,services/domain-seeder/src/discovery/strategies/DifferentialAnalysisProcessor.ts,services/domain-seeder/src/discovery/strategies/LongTailProcessor.ts,services/domain-seeder/src/discovery/strategies/TemporalAnalysisProcessor.ts,services/domain-seeder/src/discovery/strategies/ZoneFileProcessor.ts,services/domain-seeder/src/discovery/strategies/README.md", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:38:40.655438+03:00", "gmt_modified": "2025-09-15T21:57:34.839785+03:00", "raw_data": "WikiEncrypted: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", "layer_level": 1}, {"id": "79a228e1-2d8e-40e4-a161-bbb4545a4acb", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Development Setup", "description": "development-setup", "prompt": "Create comprehensive content for the domainr development setup. Explain how to configure the local development environment using docker-compose.dev.yml. Document the purpose and usage of setup-environment.sh script for initializing environment variables and dependencies. Provide step-by-step instructions for starting all services in development mode, including the admin interface, web application, worker, and domain seeder. Include configuration options for local debugging, hot reloading, and service-specific development settings. Use terminology consistent with the codebase such as 'domain-seeder', 'worker service', and 'web-app'. Provide practical examples of common development workflows, troubleshooting tips for container startup issues, and performance considerations when running the full stack locally. Address integration points between services and how to verify successful development environment initialization.", "parent_id": "d162c57f-e749-45c4-b3a6-43a7d4a45c2b", "order": 0, "progress_status": "completed", "dependent_files": "docker-compose.dev.yml,scripts/setup-environment.sh,.env.example,services/admin/README.md,services/web-app/README.md", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:39:00.9106+03:00", "gmt_modified": "2025-09-15T21:58:35.643399+03:00", "raw_data": "WikiEncrypted:F3QgleoEfoy16cQggYe9Cwwn1VyI+0nt1BIQO3/qq8RNqttL2RM0Hg15Gl1pCoUbMInQgpu+taJ/jFRqjKQQh+tVJLFDpZR91CRRcC+27beUOOEZZkQjwd5A/4hhkWmcFAYwaAj8Bf6aBnW6g8APs61r/yDMHzuCq7q/vc7jPdN+anB9ZJZRxTVFr+giS36z87EAD+9brHI82uXhNL+AAs6xkRWbYEj2oGq/h6s6NjVSi1efD8KNJh9q6FSx5hupaMTclZ3y95tl0U2FqwBEbNKAk9aHGK6UuLY7x+qB0MmwWTWpt4y2YtRLlnbFBwonpI6CbkMunaCKB0T1MGct7/5Fb7ddSEiB7bMdp5bSt+EZwdO3j0hJDQxcihxExHTw3xRLGGQVvh1MdV1xvCdsErXy8xXO/WE3AdM7/qURZM9+M8jheAfees7ZgyV6nAL+L1UO+xHuACEwfOnVEEvKxNRUzRH0PeEBltW/MNe4NWA+oarnI279X6dsC+c3BWvghmihGyGl3ppu5XvgjH0cFdkMfYFZQIX9Q6YdqeuQKs+i881H1iTCQC+4N3klasfLyK1rYU0SqmBnJbpI/kl+Dt6plOKMmmxIkRRBpeUu5olgWB9L8kE+ic7cZPJbu3Ip1/aC2Gve3lRH8HvNk80Cc7kv2NgqFT7HNVZym82cs2wqis/pzN4Gfp2jqTwfuHflSNsP8Oc4DhRpztHYnBbd81BQAHUM9PsIxDg0n6kGLPexIwPaNqY9gkKS3YeRc7KQ0oHJGzHJcPzErt4IwxxZAjYvZ+7uIXhtUu8eZ5XU91geSDXaM+J0QXJeWKziLaPNsBgGzIUlIyXIMUhXZebA4/PWvgrwedkuEz+6ChPNvD6XGxhs1rqtC1qCzfeYSTr2jAtu8fazLF5T3G79X0qbOVDPzUuCgvlZKjBJxB5+/6LRP29c41OYQHusccqnICTFBmPLbhTyT3cIbNzb1X+r48BfVtG13r/678ipcsC7RWiJz+xZVjPQ9CQX0YJqWGSQtW0cQgCl3DEXNp65krWetg9KA5rv6uhe/pnWGK8/XnsWKdXogYj2SSWRwpGDr4B89Bgx2te31OeQXCrYX3Jx+YF3jZFafYLZTKrCX5oAgIAlJ9cCTRRPyYuC+j7cvVo0ZsU5Fqt67Vcq53IpSAvUk+chsdQimDbb/Khd1dy9DzOJpT45JO1366lj4ra4hGfiE1vdlzUGlqwwalDBjSsBgsEXHHwCx+owKJem/veCwGK/Iee2mgj8LQFecRX0LYTvt0+iA9Jw3Ij2GzlC3c1C9WrB4omxR0cXAyVSGwThGPp1tiPRPfFzSR3Gc4yEzoVOlQRKOmgS+pgFnxYMsEVBkdubQmZMIkBDrPLMV54mUF2mO0WVWNGdGEtbeLMPc99G4ts/OK+sgjiuoyWuja67uSMJzXhNa149fl19Eb0XSychY0mBc/4pfZGK4G2D2CTdpoI9HuyKZP30EN9PjGHm6OluXHb5jcvI+oVR8ETIOyEiouPHmYml/32ohS6o3NxmedvKi/Fzzo2rxC49nYtgIfiumh9zaiMrWgbBG87Iq8IcUzyuzz5wsdIF+NTro81bPGgoZcuoNpFygIOOnGp5iTWBwYic/xvkWhqHCWNGUVr+GkXKBpatrGV2tezHjdPd4YtbnT74yWi7ArY4CPw/eLKTFlASS1NFLY1hzzQa0seT09jKSd85d5EG7e3229sq", "layer_level": 1}, {"id": "b8ac8bfb-0d24-4a73-9957-c087e7f2e15c", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Unit Testing", "description": "unit-testing", "prompt": "Develop detailed content for unit testing in the domainr codebase. Explain the use of Vitest as the primary testing framework across all services, including test organization, mocking strategies, and assertion patterns. Document how unit tests validate individual functions, classes, and modules in isolation using dependency injection and mock implementations. Include concrete examples from the codebase such as authentication service tests, database manager tests, and domain validation tests. Explain test setup and teardown patterns using setup.ts files, describe how test utilities and mocks are shared across test suites, and detail best practices for writing maintainable unit tests. Address common issues like flaky tests and provide solutions for test data management and state isolation.", "parent_id": "5ec7435b-599e-4858-8bb8-f0eef69cf07d", "order": 0, "progress_status": "completed", "dependent_files": "services/admin/src/lib/auth/__tests__/auth.test.ts,services/admin/src/lib/database/__tests__/DatabaseManager.test.ts,services/domain-seeder/src/__tests__/setup.ts,services/worker/src/__tests__/WorkerService.test.ts,shared/src/__tests__/DomainDescriptionValidator.test.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:39:26.324615+03:00", "gmt_modified": "2025-09-15T21:58:51.402457+03:00", "raw_data": "WikiEncrypted:oQ7VNoYBl3ApU4O5/TGa/YEO9ObmRNw6knddpSF6M+pRguDSJvLGU1zFuX1cyrsqOoLWuALaX5qZd9muAmZeBv18gIPtrFQt9xfd0bCC1bnOV/AE2hcCAWQDj2N98W8jTHV9rP5EOg7zrSroFY3xCuPqpZEbUyirp/TRe/h0HYJiPV1IMFg3k2YGjsSNqmseWMKoVwUzXFBtiKPT4iwAgIPWXpiqDNGkV4w/g4Y3HKyDHXz5QteBZoJg2uP8nNRTedVR+ce2Ar38oaP2JoIuhHKDUAXNjlsf3osMInMgqXUQLLlyrdfXOVDJtKz8Oen2LpLKrLsHOstcf2jHGq7k8CiBzMpu/AlZEYjkGaupzPGfmJhS68JQELmHg3sKmLUNjWIrSev1hOtOOIc7PxVxF4hlb2GUnAsasDp7LRszPwceOMbX7yRgCceyz4+O5HmWYDUHrnntv0d23RNJW80DXTFkxm16HJX/kf/q8FTYGPy91FKcZBkMkjKtm2MRmU1wxD3Lnu3LEtBLfMBQtdb2/D3UXo6WceoRbnSsrgMLYSfU9OTZYG3UG1jCWKjEqKn0Zyv2HxmPQtp0pYTmVcmEtcE1+L953qQZqV8agD2levCQQxdmbhfs5et1NI2qsN5Y7u3YckVEvbiNCN5N97iXsVoEEq70r9NPP9glLgcCwEYtKFTU3DEgrkM/Mjjm1LtRjSJ5u8cb9olhAL/SUgJjbLeDuhhrUv4fWpPrmnuTTptqjYXne/6GLd0GDFwjg7FjZteUu/5Mn+uP7LNQDlmtqaQqLLD60k0no7r0iKPqjHJnS68oDsslJMQHrhd4svQwmahEQLx0r1IXdF8/bKfT32cTGQ5ZkNmx7gZTH4waOEWa+o5Sz169Yojzfn1tNSWkREBBiJuzUJN6wjpNdgTaxQ/05VVvm5oShBS8LWLVlwFGufi69xkV3rUXUWuzz8IqKDytwV7jmW3Byc2l+PBir6+/PGC13FaNhJbxmxs4vYGHSVs3wPJGqOvGutzLs5+F3NbXEyNMhB7AwmclRe63OZ/MxOX2+B0Z8PEuBf1yyIur7E86w1yL5m0A3t/mgkzSVdm2pIfZb/ZdAPjPNYuzoYitSUzntcaMfCR1dW9vgL/OLHehbBiCOjh60k0Uo77BJ4WAE+trhkqKSV+2NI/TLr4oZVWRwnj0uU2fjv31iOSET+Vml+OFpU5ZuPnTtnpPSaixeAHR5/gVNa/gJeudeaLrN3hoZ5RNe4r3sGcjZmCL8uA9tp5vvIRQaHpc2GCfnuYx8TmfX0E//wqEyrgeVq6Sz4OSKSER+acaI2FCIeZP88QmCD6C9/U4KwflIUcyHa0qWCHJ18LrXmn3e0q3+6CHlOfZ7sUhhIkJl5gID5DSDRZWrQCSaVtkIcw0kglTJvBY27PLCo4EdpwqAltazFPhkDP6/jcrj2an/sArB4eV61FVRb0jy+tdFwm7wK28yeem/prSCa9c2zNDP4MHz/WnQOtlPwlyUN5Dtn0nhzLNr6wSl53yTr9dIdwE9SLDEXjYfVbYs82KpJulWyk7xPlAXdCbeJnq+Dv6a4frLhON41DJ/GkU7iG8DCXugiGEj8DEkXirENmW7kPEFykmBQxqcv0sKfEZg1LsGalPdHcDim3bWJLuXlnx9r+m1OpmwcTELcQSQlavnJvFh7LdQhflfxC/hF/2OBbE7OJhQAHEXUB2GdBTsT5C4CmFyNTjkBWxtxI7orceVcHcDa8d2A==", "layer_level": 1}, {"id": "4a375289-bde2-4aa5-8c8a-8321983ef3ad", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Technology Stack", "description": "technology-stack", "prompt": "Create comprehensive content for this section focused on the domainr technology stack. Explain the primary programming languages (TypeScript, JavaScript) and critical frameworks (React 18, Next.js, Express.js, ultimate-express). Document the UI framework (Mantine UI), testing tools (<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Jest), and message queue system (Redis-SMQ). Detail the database technologies: ScyllaDB for main data storage, MariaDB for relational data, Manticore Search for full-text search, and Redis for caching. Include infrastructure components: Docker, Kubernetes (k8s), Nginx, Prometheus, and Grafana. Provide both conceptual overviews for beginners and technical details for experienced developers. Use terminology consistent with the codebase. Include practical examples of how these technologies are used together in the system. Document version compatibility and integration patterns between components.", "parent_id": "", "order": 1, "progress_status": "completed", "dependent_files": "services/admin/package.json,services/domain-seeder/package.json,services/web-app/package.json,services/worker/package.json,shared/package.json", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:36:59.652193+03:00", "gmt_modified": "2025-09-15T21:40:52.552738+03:00", "raw_data": "WikiEncrypted:w0CkdCdnXCcvlN5xOpiEhEX3U68GI0Ngj6azFMJo9xSXD5adKnu6Qo1t8CLnFs+ZP+IW+8SVWDLlYtdgYFwxhLH+AzgSdDC9Aw3SFitIjLGMGUt5kY1dy00q+CnrMfE1p9Ko9CND21PTOs8DpO0+TXvUO3MyXAi5oWQxgW7447nw0+e7W2Z0MfnuaBFBV7FA9aBLYJxJAo+fzLogiKlWnc3QyQkr+dI0/rJc2C5WtvqcyGxGTlnR1K0KOh6M2ZRfBGxMn291VmVEbternyJKYcLIoki+SpZe1Pe0URWxTLqNhWyfv/4fr6P4KrVd41MVEVc65QOy+s3ETK6V1ZjdtjuM9Dw1bqwBYI6GJoA3+4CKLhsRQzze3H9I8TU0JQeI1cpO3hsh/vUbfWLQP2YsjGB+Euyg/DDA4Hl5iwLqW+nwCwUAw5qvAkOmwinkdP+PX9TYfBCey1kkVnUEXigukLMECUP/gO4QVTmbUNliQhT7y3FjI54nQo6gsg+/RUfoJwt5Np1UeHkXFuTjgAHjULLnQY/w9AxpU/qFgPDIefO3H1vxBl5uBhq0SQ1+9TNAnLa5MTpJqulHCfAjzNgr90AJfAv9yB9q68Pkugp8c/emB9cvdS4E4jLJGL87gnK4Ir9ARPkyJBveluYMsyv4fJlxtzntZNtJ1WTtkHkKZ57YbUZXWrQzTEQ5/qCtv9Rsnxo1yGlgfvy2PRlBAD1Y4lH3fcl1cI3XiMrreONumNgQ7wz+6tRY9Nd4Zc+ZPnhJ5u5inv9SwNwVd5X4I+IeAvsWRy3y8B98yoDSj/CJj6jYZUBnw/HnL4X1sQLAh1slzL2oLmg2SnshL5ilcdgfbQ6LWuO8j9odpTCeMnQv3qRQjI++5flFkgNm13N6A4VcHMgNHmVgOnozcHdXwxdM3aFL126QodJrgu5ivrGh+AfGzt1HSkHardc7KEvf92MNfUntNKrGwE5Na2f0Wfx9D1g7xgfws2yJ5WOVGpP6d0Q4L21PqNsGghMPO7M8kfUWNz9zgb5tcOAsNuLbte8uR1IOdM1MQTS4I/GcCfCrQQVsJyQNVZAb4z0gmEPr/wwl0NgH43eUoVEB/EDO/KxfLEi1haRuFW9h1GWZqhKfP41UBqbFCOBvPFE2NtcURgQVxjfiMwUMS1aJgTyx7WcrtDe0lE+9Td1Kyv8IPJLKVreLoISnFBMJ3qOj2Sy7dyvqjiA17v7s2RxPm4e98LPkGaMdlLrN0sX+HXlfbMV+04xldlx+Kj8ef3KWU1DLLoplA2Ud34S+KH/0xVc36XU5KW6pyY3yjlTBmNXVyltRm3kGs/Ghp1lOmnYDhV+W7mnw9GlmFS5no4jc7DVbx4Twj5Zcim9wNcPXmBRIMs2VDYQQFINrLYcygZ4R/u3fr83UJP6OTWvzYSVya4KFA8LYK8XtsMB1RX1l9GuLPPe6wtB5w9jZIBO6EINB413p3dX5O7uA0GzMlXmz74hbCig8XRj5dvhYrYFmB2h61eBdG0U+04HmGix2o2ThuKEJbda8SLr9nEyEq+6s32iIInoq073cKsewO8cbkicF3zYPefvmx87vDowkEeza0ElJUV5dXsLIXtKAe0c7bV1EEKsi76Ihp7VqFB68+Jt1C4Xuk54fXwx3eeck7zN0YxZwwIT1akmhZIu0QBQDgOVyQuBo5SElBH2S3HBvBGG/dHorIaJoHLSPhgakoKMLst+zUwF5uvMFDG1pA5mBgJRnWZLIQSFpSCgiSmDx6k+/3YKlrCc=", "layer_level": 0}, {"id": "43ac1b83-47dd-40d3-8d39-42e772a18e7f", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Worker Service", "description": "worker-service", "prompt": "Create architectural documentation for the Worker Service. Describe its role in background processing, data collection, domain analysis, and ranking calculations. Document the microservices architecture with modular components for crawling, analysis, ranking, and indexing. Explain the job processing pipeline, task execution pool, and integration with Redis-SMQ for message queuing. Detail the implementation of the ranking algorithm, crawler orchestrator, and data validation pipeline. Include information on Kubernetes deployment configuration, horizontal pod autoscaling, and monitoring setup. Address fault tolerance, retry mechanisms, and graceful degradation strategies. Provide examples of pipeline configuration and performance optimization for high-throughput processing.", "parent_id": "2fd75159-4155-48d5-b146-4fd895ab5d86", "order": 1, "progress_status": "completed", "dependent_files": "services/worker/README.md,services/worker/Dockerfile,services/worker/package.json,services/worker/src/ranking/CompositeRanker.ts,services/worker/src/crawler/core/DataCollectionOrchestrator.ts,services/worker/src/pipeline/DomainProcessingPipeline.ts,services/worker/k8s/deployment.yaml", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:37:21.307384+03:00", "gmt_modified": "2025-09-15T22:00:58.47125+03:00", "raw_data": "WikiEncrypted:NKkj/YX2GdbxYjFllBaf86XxS93vuFtHBQAg+dmLb0zx9wMV9GtcLcFXHf7EAn1XKJOZvPJJATNE3S3raHd8d4dSbpPmC/axKfNa6Lvu28PpngQcBQjwH3f46h9J/d7pZULDcHK2CiHvbXx3/XKuOgtdYwVdUA2B+MQSL614iSqca++vM37yogERymKxsBlqBRQhQsBgD0SevheOfZ+1jCOnZ1+F+zQ7X0HWoR1w+SV1hXWWIgeFPLW2nlOr3UP/aa65+NS5rCmzWd0GQg5pq4gHBFYIuySgoeUDAWdnjxwE3KCk8s+wF5OniMTVNeD11Tgcp3vYrH19EQpcacNhQNRrylk/rZDjFbFjFeoYfrEjje2MBZ4Y4LhvqLdV2tHnCX+DvGNoSS2YkLmKfKl3SwIRpGCnrmhCeT6s2vNOvPHpc/KW3z2LV8ZF+/kO2w2rFISOi/uBrm44wTXyHjm2aiSLMbT8+Xoqv/8zUBnZDEJ4YChSXH1cOqN5iVZyIwDycqEOPGvbOyB5WgKfEpVQhyzvqrxFyFYjq1xeVoxAcZSZqCEHGsrNZxvmD0PjIRM6ef+Vznm1cW2VDD7pAta5SJTsAhOlQryrRGq+5WttCO74hfeuIZHZtn2tzQq8mLDBmlwQGcxr3HfILRee7JzWIesx9s9AWHwU1Kl2m6jb2IXRnZq/1cWcyTZ3ZMh3GZdoKsBUyvsYjS3jL58w4KI+KzjgZ+t2WOdNoFUvCyYpz6Y6Zyzjd9oxR/sw9gWGiZUuHrceZAONe4gF/yDK4g6ZGOZ1I6yFnP9ol0IN4gUMkMQHftWU2QAzDNh/xd2/XyAKyvNr/d6Vh1cR9BWXfh3y2rC6f8FaBXoErbhyES7yjsknuoB7N6eo7J3hlSysnbFL6ktMWkoYZCPv3N/t4Dvcjh1yf0UhcQiB7Rrd/tG8XuA0dIWDKuNzvcDfytL4jErvgnOaaTjf6mVSvN3uF1t2x4211ryytSLvQEGie9FZysWO6G70mua57fTdkIr0V3vFSs9LD8v/J21EL737dA40bouvevbuuxHuhlAZN/pO5XRjges9hkExmPbQfOqBy5Zo6lvKzuqhZhfW3hPFhngTW8mGGGVjzF8UytBBIdJzM274MtsERPPFPhzHX/6a4co6uB4im4GT9OtDCj5lty6LELqxalXzhbANo8m0wuPAis2bdUaZzjAx1E3jNPfhkzj9GlCGBdB1eBFYlcA6KDOGW0so2/3pIoVnWztmbceX6n3FB8HTcXJizBy2vGo+UmjYAI5LVyIywfueajPZk2s1E2+ixTQwVh6VVjEoQ+I2UE7Vjo2YXQCvyPbiHXMZIap+WZ9LFeMHCTUSXRZ9wy5gChXC4iXHU5+B3NipxeJe7kQUYtnB9uePfSGmJ43ISrgt0a9St9pjZvPeay7QkbNrOTpnY8V6Dnv6G6Ub22zISV5HtYAepW6oe/vhJeK3KTUdljLhVeQ6HCCR2O0apM3aI7TIrp0WMJPSX2n+4FUUm3fExa5jMEjGBQcVhNivU9D71rWNnOG3BZJ/sfox1EAO13pJJJ2ZeZpctoNC0fiyxXqqvws3cWVX1dWjy6uasYYkkmEVM5i7rq8tbaUZVgCurfmfmGHiux+BugPn/XZ0cU0VKMrmcqkOyhNTz6qn/0iaFFOwEgeaIMyg7AXRFZXhsvxWFv/ha+SyDaYiZyJ2bYd+wX2nNGvKo04jXYOAUXfJ5Ayy0TG2bBXfIhEO2K+vhw==", "layer_level": 1}, {"id": "dd192e8d-bf7d-4707-a60a-0a3a0f6bf9db", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "DomainCrawlJob Model", "description": "domain-crawl-job-model", "prompt": "Create comprehensive data model documentation for the DomainCrawlJob entity. Detail field definitions including job status, priority, retry attempts, and execution timestamps. Document the schema implementation in MariaDB and its relationship with the Worker service's job queue system. Explain how crawl jobs are created by the Web Application, processed by the Worker service, and updated during execution. Describe the state transitions, error handling mechanisms, and integration with the DomainAnalysis model upon completion. Include information about indexing strategies, query patterns for job monitoring, and performance considerations for high-throughput job processing. Provide practical examples from the Worker service codebase showing job creation and status updates.", "parent_id": "54aacf35-eba1-4936-90c8-2768157d3278", "order": 1, "progress_status": "completed", "dependent_files": "shared/src/models/DomainCrawlJob.ts,shared/src/models/types/DomainTypes.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:37:25.738409+03:00", "gmt_modified": "2025-09-15T22:00:14.39637+03:00", "raw_data": "WikiEncrypted:JLXqlo28VAn5UOPLsLiQgWDSYEcWKjhNrhLGlJhZXQwB8iYiZB+TQRVL1uYKi0lUDuahxj2fWr0XLy47FyzM84dV8YcdK5D0HKWIMs5JbGHxTpySlQpOjzmwYN6fNpLD4W/+GeiiAYx4NDNJKQZn0OgdCrgSo1Sk+vKERvyt/EJmFLejMDMoEIH6D8RJSvVrial+4LUnd4HOhb9LYasWmyMaLSCszSQqmPQjciVH9tbmeekzV61B8FHgIt2T8FCBwNpjbNY288truqnhsPLW99t19MoumNkZkUHJNUcvRcsovXDfLnEoxIm8cfc68Q4KmnwEUsdrbo/+6oL1Qk8AyiIEuPZETy1ZQBm1Jae4HmjQSWZXGusOgfxa5onfrAUmpAAfABYI4XLDSrwiT0Oi1aRyImidiKakTgSlKMZ0jqHrysT0Lu218TbhewkyELi4RDUlnCb2Sn3WGy2CLMc+efm2lSPBp4efB7SaEyN8p+NjgEFdxA5k9HDuX81AqjUnexHsmQtpvN8fHt6Bd78nvJTf5JLB8qEVTl8vn2mrzf0FPCFbsgSQkO032UY8vKfgFxGBG1b9JnAA5LL32wB5coHhgjG+Xc8ybvsFT9AYeVZTkgd6EtJeluwUdy9bePAor4aNjh/2/ABAY84Lu1VHaihpyXQzQwt9aVL5LKjM+7wmBoNadkkt3u6vTQdfQkXY1VrCeHWITQnppAdr0dAGEoSS/Fzb+Ay6YCSxf4uVFLCvh5ZZnRVrEXCfkABzM2wScXihd+82VY2e7FNKGhcpbSaBb1gHSZ7XhmNXAARNU/d6MuesmI8Ad4Bj0isXXDCH1RpH/wtCgzvVjm7hCNaeFuC+fkXHSWYLKgG3/+cOXHksW+9+nKJk5TCcRTb9lDsUOJi7oL2V6hzvfztv8URQm6lyqADW7iYDPYFVMFJnVAXyLn6f9Er1hnHLOjRUsg3O8tj88mUnSoi4iTP1SP7BM92cdRt8+7DTJ30KeGdpUHOXbMLyX6HYNfiDyRPBrhU/O7tdvixTF6WIje26USXwLzfLUOQkExb53g18g/cz42LUgf0RB5VeHVD76FwKNKsXnZS393N7/JiaJpQ/00ebCtEm/708f4KrVNtUExYQa9f/RjPCMuEjKEogeG3YlFVYws9HaYMI/2TU6uiaslE4EpwYkbPAe1AZgXSJnh7T+GLosfAJ2HuaJ4Dvg9XqAOxp0+RmrBWdmR+gtWbQPau49QoRk8qf9eknClvkvwEP1IHQwq9e531ewRBRga6Qf84y/RoloMAlfsZV+5cZORkup5XpcA45Ttz03gKNY8jy3SnfBP8krWKKQg7DwspVF5cjR+sqT22baB44h7Yjm9UWdho68LF/kCLFOsdNthkgmBB9Qq0h3PTtrIeQzRlliYA4ahgkTey4YrNmE6OShvriveNKa+jT7sii3rerhUN7s8w7qkomZ7TH36fBEQHbNcmS", "layer_level": 1}, {"id": "8c7c6bc3-731a-421f-8479-5be100222e11", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Domain Analysis API", "description": "domain-analysis-api", "prompt": "Create API documentation for the Domain Analysis API endpoints. Document the HTTP GET method for domain analysis retrieval (/api/domains/{domain}) and POST method for analysis requests. Detail the request/response schemas including DomainAnalysis and DomainCrawlJob models. Explain the caching strategy using Redis, cache miss handling, and job queuing to the Worker service. Document the asynchronous processing workflow, polling mechanism for job status, and final result retrieval. Include examples of response structures for both cached results and pending analyses. Cover error handling for invalid domains, rate limiting (429), and service unavailability. Provide client implementation examples showing both synchronous and asynchronous usage patterns. Address performance considerations and best practices for batch analysis requests.", "parent_id": "4cc1d72f-ebfb-4439-994d-803e620048d9", "order": 1, "progress_status": "completed", "dependent_files": "services/web-app/src/app/routes/api/domains.ts,services/web-app/src/services/DomainAnalysisService.ts,shared/src/models/DomainAnalysis.ts,shared/src/models/DomainCrawlJob.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:37:40.425616+03:00", "gmt_modified": "2025-09-15T22:01:27.769135+03:00", "raw_data": "WikiEncrypted:JLXqlo28VAn5UOPLsLiQgSnv5YV/qgoGcwZaMUNdocXBuXfQ+T+QD/IwUlU7aG5Q2TqtJwTZFJDmLsXn9hXfBhJhx/6phvVtm12FaE/cNbZN2iM213RY/xrF+82sYYy1Cha9SwqPzeI9zG2Y8k+dlxNRSgnGh+WPBJmXxCx/Ee/iBUusCm/U8Pzrkz5IM0g2RMD5xu47QTV5vbnB+tklhReMOpUALW5b7RchP4z2S+g817pWmpXz9I+45W2NyhyATN6oPVIz0vOJPGI7vfJ6APqhjG70yqHN803oPaWrQCRwxMau7N7uHTXoBxv4ou9R6K5jJydLdO5ppEEs9+nZ2qyuXdcNNTKY26Vdo4/loNR4vD1NmErgV+jNM9PVEConDHKDJq/v3MVs9F+hvHMMjKAr7XRTlKtpSpIJcOWhz5p3H1GR6ic7YO8lWQRmWayV0Fw9Yu169JHHowOPVkuScmqc59PxjM8H2sh7MOOWS6bagDfIGzA+Tf3ryPyA/hUkXj9doWT28swHlO7Wj/9MzC1qehngn2zVLWX0p/0n63RY6zVmGYehjZPo/SnkNHpg4CflTDviYUZaW1L629D6mrMxOazvHWWgaf6ZDoZrAaVqDSdkNQngURVESuTQVSAjD+ms8QFm0W4yGaFHkaCK8pMR0RAdObaFPKFsermGHEDAdzN67APqVEjRuWKNGPh3krnqGOj71Hv7yCdjBC0MMaspI8/N1tMyO0C1AnKAUJDcNnoFCwX/t3HaFwq9Q7XSUgRK9btb46LwdJSvcxmjFI9oM62zXq8EcQVKIKdHGcF5xFpVy3Aqd9J0FandegzRasYJj/MSvwzoRQV+fxdyYsBMNP9v4LqWgcTuNVO3S5SM/E9nLYzxtZdAwNLOlNDz+X1ABLAgtG3wtJ9Cb9GgiXzCjBRzOaOjlZeuZc9yKiKiKcygwLujkCSzAlKSt0Uo9bvvMgsYhybMgGpa2DGIk8bp6UepI5tnNjZYZsKwxeGrxADI9tlTI56db0tdpx6p5IeHYyNVuOzVWopSu/VT4IygiQPQ4NDvy80jPkI7g1HOSiISE0EVMxMp802ue8WSU8QI376P/vXapeIAtQFXACKGMFKjjhoeyXP6QyhDmrraVQDu58cMejpSA1fgSS+/07QYNypYFzVWVcOZ02N1ADdc45KJrl4r8V6hgLo0L7H2SKbwu/Aiv5lfAp1VOL/lhETLrlc1GBUSIl5E6dvTnUFgqC8KyC2D3niT1dQ9tUSt3l9XW8SW5WylBakr6zVpbbtcHhD220+4q1XF8WyVl3KE9Uq5vFQQtMtual71HpE6Am4oaFvSeihUpTR2a4QHaKBz9Zx9T9piYviqQcNVuA0v0a4bDGDZYtF4J03lHNQFkrEkHpOhWWp2o2lk+xK4eKhv5VxdHVmPP6qcbBgBhtiFgxxhycad27tKd7NHnIK1TByp2mI/j9HWZ5nSeHt/temgY97f972Ci21liPL6evfmuFo+juG8h44UvaD+ooNBvBNNIdF/iQwwpwqTN0K/rxUY6t0V12OtTQW5VpN04zM+WaGuv5bmWMclLyv4JImKE7CblpoarxRtBTLRD5wthNhoLPkeLWk3hbn3++IS0A6nIHJ+eA33pLA26qZ7zoGRugZ+Xe+GwhXHYu5dB7Axhz9GmW/be6MwGJAfNyrjaA==", "layer_level": 1}, {"id": "d14dcd20-4919-40a8-9202-80e510f7055e", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Domain Analysis", "description": "domain-analysis", "prompt": "Develop detailed content for the Domain Analysis feature. Explain how the DomainAnalysisPage requests and displays comprehensive website metrics including technical, performance, security, and SEO evaluations. Document the data collection pipeline from request initiation through Worker Service processing with DataCollectionOrchestrator and various analysis modules. Describe the DomainAnalysis data model and how results are stored and retrieved. Include examples of analysis requests and responses. Explain the caching strategy and when re-analysis is triggered. Address common analysis failures and troubleshooting steps. Provide guidance on interpreting different metric scores and their business implications.", "parent_id": "e3ce4565-5d2d-4e25-9f46-14b64749ee98", "order": 1, "progress_status": "completed", "dependent_files": "services/web-app/src/components/DomainAnalysisPage.tsx,services/worker/src/crawler/core/DataCollectionOrchestrator.ts,services/worker/src/crawler/modules,services/web-app/src/services/DomainAnalysisService.ts,shared/src/models/DomainAnalysis.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:38:04.738186+03:00", "gmt_modified": "2025-09-15T22:02:12.166761+03:00", "raw_data": "WikiEncrypted:JLXqlo28VAn5UOPLsLiQgWV4XFlc9U2kCmdQYVYwLKd90JBMSi9f1ScjXfe+5GUMLwl2yVXqAXcARYynqQcTjmAxEVUFRBnhSv3oVMBafTGujQKxicYDKkGAYfgWTde0lTiPSV0hzKZovKw9twLa/4dwP6GH6WPw3IJCcRPF5XiFqtsDjDKHRODghTIrOECOYpkqGyNoKwFSB6gfxF/Y6tGPTbNlSD7Y+9Cd+Di0zV0PUxCsvja0wHs+av6+OSLpLjbCOyimZ+XrKzS1Rtv1RCTszYpa9WqRFceOrA40vVAky36o6UMB2cAItIBeUqkOygAtmTpUCppEyQig4RU/fxfnYYnzT2XJAuaOXu6aEokvlrHEJvGQGdr/8u2ELfJdgRB9BMHPnCXGQu6eSEytM6Y0wLdwhd4gsqy+xyDumqgCXLAY7nbYy2PdY4Mn9P9dlKpZ3tTHc4t1IsXAurVh3AFQQUsFZ6RnYyPwjA3F67hnn6Zjiqj33qUpq8mzOpgRzyoiiEf1OrZse3viYo5jWIzJZaMhAHyAYPG8cpa2Dpn4F0Lfy2O8BQfs4GFZBdKUbqhPWrh/3+ffRqgCvVje3bP00uh9k75AqMVKJRXVfJXEFD7dpyfnjzt4Bq+qalSU1TCrnfqTgh/ZI5vWqWzrWIF+EwqvGXAi/LoLDZlv1MVuPv5k6Zn0M1XihFiOL6BQu9DEJSzsrN9P+Ta6CJWVsE7vJ89czCr3v1J108/wE17g+0Z+K+wFklu2WnXuCUSGOVEPMDYqx/Y6yVrJMWTdoOJhhZ/s+g28/7TVrdMbK5Upy/b2ii6MSDGtJ5Q+BU8YrDnlJF6KHQqrh1FkuAENKs1s8+Sg+lW1EyfvXZf6NlTI59NB4zDYq7IvabI0jGC38THTM+sHKZrQG/lMGEE6YV6MsvZN+yg0vjTCVvUeqI4T4ZukAlStYoMJ3y3rR2PX1CKKguE/J90Mjhw+vYO8l3fHV5TF+BStZPZ6Mqs1qdcSVEGBAWhCDKBO9bqfnB3L0maRexwBYIsyzwdKShTDqqp2UhehK1IYsEOhA5nxNfEj4coFHk/zSP4FpE9SCIqoXpgBWShNkT6hg7xbw12oTpwgLJWqYqTSRHIioqrchaA/rQJLwbCY8XNKqUDq74ocuWO9qBMjGTbI9huMXRnLY/9cy6AFpq4kAeLaHCoLPwMpNf8LF+B9lYZA73P7NouIiZk3W6jZQWie9EdbesKSz3p5ej6b48jj3+0GHwKckcyyPXefG0Td2qfJjLrAp4e+WMk6e4t8XCZSb19QawLp3+kiX/fbCPtZ0tDQ/kGh5bZWyDuV4vSejiBnxvHP/0Cm+XgTeoSCZ+BBADFwBY6pAsSYkWo49mLIAsdpQVJjAY8Z4zhp7CLKOCEilLbH0r/Ef2kXDUZXWwmKJbTwxteTQb7Xv5zxGkrKLDHvc3a3r9dNcLKZeYOWU/DWsWD5ggZbGl47IHrkN3/rlxKqgw5Uz62tmi5Gnsv/R50I8+KctYvsrR+U7Q18C6eyTnskXpGJ5Y9fbMbpYbvqIr3G3IxlzghaRyZ0mWogBb8eUKEka+pmemTUI6hvo7uzPk3fWqdE", "layer_level": 1}, {"id": "fa6eceee-9082-4792-8ab5-d938d0dc3e5c", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Performance Metrics", "description": "performance-metrics", "prompt": "Develop detailed content for the PerformanceScorer component. Explain how website performance metrics (load time, resource size, Lighthouse scores) are collected by PerformanceAuditor and transformed into a normalized score (0-100). Document the scoring algorithm, including weight distribution across sub-metrics like First Contentful Paint, Time to Interactive, and Cumulative Layout Shift. Include examples from integration tests showing score calculations. Explain the relationship between crawler data collection and scoring service. Address common performance bottlenecks and optimization strategies for accurate scoring.", "parent_id": "86fb1ab3-0d9d-4f57-bdd1-151e7cd91e0e", "order": 1, "progress_status": "completed", "dependent_files": "services/worker/src/ranking/scorers/PerformanceScorer.ts,services/worker/src/crawler/analyzers/PerformanceAuditor.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:38:05.721462+03:00", "gmt_modified": "2025-09-15T22:02:53.55326+03:00", "raw_data": "WikiEncrypted:9uOBpMbLX4DyZqW4us3Wm/m2PBBSkxjfAweRp/sA5ZJiBX01mhKUndlGX5eio7B0quH+srPa8n+saQo9qEUqnfHiee4hK4bpUC0CCKpXGS/xWcgV1xIoB8Y1+6sQsW2fVTdQ14qasXcnErlHqRL1VQokLuGpdvKAZSK6N4T1r3UHQglXv7CSXuYN6JruAFHkqD/eBh/C/Gj2hf9OWY4wEHaFUBijVMcSRI5hSPrg9vHh5GBfyOxwkUqt1iyo7TN53bXgIZ0zA0ZmZ4IATU29oFSLU5fY3w9ujd4ft6yqixpRGQlzJLC5Zf6XSmW+TmbCI980tXrVTFSO36dfxVO/lYUQkwGuY+oL1GrP+pybvO/VtTbJHlTeIsxaLk6+158Oi3HFzfgMXXObbq5rbKi/YL9AdSHSoULT/Z3cp5SCKZiYGJ5ODgPjTjW31yRRhdfI5DkSV8ikhpTYiWkgRh3RAbyNMCzsODsuvHfbjbqSK2s+W18dCQScel73y7hMwePRYfmFckhzGpxk6tG08qIR5myM3hRIwOFozfk0r2mGfRWPm2X4SfeKV4ZdA2eMaFz4FX745u7k3OFj/8fDuTu7Zfl5eY0pUtZ0qSNjA7l0Dzj23QX42DVLWM/51f5r5Bm5SodDPUyYfTv28tmpdz46RLp48Lu3hQHyne2sQqPr1jHlzugryZObt+2DTTy4kxNWMkd8sGPQ7FctWVwwxYvqI/3VK0zyF+RspekISIhlmoDwGj3R6mlbIG/mHSPUVTaxRzBjkkq8LpTBWEquozC0/wWokcj0/KTGvhK0Omx3GAYTtbbbKBbC2TXFE1z5stv+6/TMLvuhd+s+sfjRu9XSPkp6s6qTPXIoBIcR4CY7QnCgse1o1zN/71vNLPpkqVCsTrihyb3VMMIXcZbmW2ZQDwD/Vo3jPcI1Izq/2stJWW8ur0MoCFkOfDXDK1hz+szdYrdVk0mpCJ9dnz/OD2yqpOuvzvgFPBSeb0gvlqgt2OODWbKARayk7LOSctDOMRQuCbR/1VXvkhNvl+wIJErALvTDNI7cwuHcH9eX2awnJG+XnynrLbF/AnQhXKZv2gzCYJf74G0ULIeOibgJUI5JO4bp3hXl+SI7Odt/KzmZX8X87B5pJtYekRxgzao678sVDMqQV/ADO51EigjSgPlSPgNMwcsruhqwNc+k4KX2FJgoZqiHEiaQmKLONa+iFs+ZeeEHcqnL/hNKtexVCWg/CUpjWOguYxXweNTWbzWmNZu+da3H0GZKzKA3ThaCTPxlMWNB1nLLI/0TbKL6MfMCRKhGqkLs6DoxJykNIJFxh8M=", "layer_level": 1}, {"id": "f6603e3e-2312-4d41-9734-79e7cc7d4afb", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Data Validation", "description": "data-validation", "prompt": "Create comprehensive data model documentation for the domainr data validation system. Detail the entity relationships and field definitions in ComprehensiveDomainSchema, including data types, constraints, and validation rules for domain analysis data. Document the validation pipeline architecture and how DomainDataValidator processes raw crawler output against the schema. Explain business rules for data integrity, such as required fields, format validations, and cross-field consistency checks. Include sample data showing valid and invalid domain analysis payloads. Document data access patterns, performance considerations for validation at scale, and error reporting mechanisms. Specify data lifecycle aspects including failed validation handling, retry strategies, and quarantine procedures for invalid data. Address data security requirements during validation and access control for validation error logs.", "parent_id": "71a79def-fdc0-4d40-80ea-b74ed07e1196", "order": 1, "progress_status": "completed", "dependent_files": "services/worker/src/validation/ComprehensiveDomainSchema.ts,services/worker/src/validation/DomainDataValidator.ts,services/worker/src/validation/ValidationPipeline.ts,services/worker/src/validation/index.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:38:27.305112+03:00", "gmt_modified": "2025-09-15T22:06:02.797496+03:00", "raw_data": "WikiEncrypted:LlssVZ/E1BSUywfa50FLlbIKz7HwC4V5/bsCSyKRBkqVCXg3WiP6ae+iDPdEkQVYiCmxH8KByiT4UiMOTnVlg+nEjwIgVoXNgX15gBivhhBj8tZ87CX1cUNmpdlX+YD7m73AXc8BPueGxLngXYG5HBsRsCh1rJzkynmFSGEHHCa/JBY8V54kHJ58+D6wADSCEmIK0opr3vGovapS+cr+3EWcEux2vrTJF0bhzQUwpQvmR/cvllWZyaUfFcXi+Ht1/1SjamFEIHtOsWx9ozz2pSBGeMnaYIVSEfLSPiSgixfrFqTz0EjMQUe4r6YgmsfOHYILdvZ1X7sAapOGNDO445gRlD9LO4kiQ1/pQ8dw/Eiwz2rCRv84nDLCwk7ZiG3n/yvBoGwnRpTj0eQmxtwO4Bv6t7Tw6YDWCGsP3agX7D6oin8m+4u0A2nVoBgh4jzlwYFbaR9wNZ2kWjGpcDozfL0dlfbvo7V5KX6iGlRhuM8pNiY0jM3HeH7NIZED8IpWvKzk1+Xkpc7VgVyPo9BKW+9Q+gq0wWme1b7HFE206m7RlmfAcUj/ImXShIl0JQT9fhQD2UiPsa5UdLPTa3/qQQL7+V6++vRg32PFp16FktGA4r6iUYzS/CNNKEsUOm9XvAu4Ri+QEPLRe5JT0Aq1ARQ0a7uXeE/p3FzPIelKNHDqYU390FU/eXf+wFmEpCVqu0zbuHIg5c9fxqp0rMJhIF2UAzY8TZqF1i9KqKrNcR10HwD7XgvlmVluQdBUcxfATDY/COFKsUBrLsYGWu2i+JdxililJUQAkqsm2pAdfb9WFqjCN7nfuRquE+ZUPTHJ8qMtxOJD8/Quh6Z/+GzO8QbH2SSBFXsJTBsB6B/+VffhOT0utG5VE3VmMdti2fIKl+NYZCp90QMd2VozAFiCB8owWo4Eoi1bg+4W10CmzbW0lvzVXJ8k4Dz6rPUDsVNSdzET5YpqRxNxK8pecErdYni2hIbkl6HC2npB1njzF8OkeSchj0swYPSOwceg9p5hgF7Q7kMN4skMZguM0DfUthgPJnGOT+Sltaxb5y7YSiPIgDvg/ZVCQIW9sPL9ek7dAwnnCEeV38k8wAOfTbTgktesDydijqQH5/Dx5wXJ+afVaGGFIVQA4MvFo8nFreChIsIJw1DFLEvwrDDTuHvcmh1h4immsmseFL1ytFaYUGPI5XIrRDvlv9BsT+YNKIpvtLilpW4mlhFH7PGuEwEQ4q8klZWi7HV/37fIUXIgfRFD3iqgqhJnS/D1RtzE9NIJ4EH0SpY6S28AnRd5Y8kOlymSCoT9pSBi8INWCaNXuP5eseHBWcS/JqhCGP4wuThqOq/HCEtia713Qhw57/I8SlY5b50fGxSPYGj5MZGVN1DP095T5Ov9o6CasZT0R1RZ3j4LQ9R/UbLTaG76Hj3GoV4+J5siIlIWkx1yu+wc/vuEZ9PpeQpGHI6HFHJa6S/mJx2j2JLeki9QiarP14IEhDELDYLJqgbA+hK0KL0zGOHeZVteWKSAorfj74yNnbR5fo4Gf18P5D1UUDZWv5OsQYH0cpiEVLhLAZeCnlSAxjcA4WO9SRqrq8xLkNJ+72X7bJphvoGG7w31LFnmBvZVJD5929/xIrGXTB1vVF8CjNnuNJzFYZhTdWotZMmtStSr8C0S1cdi78W5+Z1RF3vTQNWr91hx7cSFgBlyhUMKc0NtcVh9ShQDpqSfek3NspnNTIovuopV29ZAC1lv0hVUkAikipygjQgXt7XKVGBvHpIikL88cuJAYOVWK6b9Nnj3d70MPfk8QDIVc0C1HqD/BA==", "layer_level": 1}, {"id": "1ceec158-3bf4-4c4c-b815-d187a629bab7", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Source Connectors", "description": "source-connectors", "prompt": "Develop detailed content for the domainr source connector framework. Document each connector implementation: CZDSConnector for ICANN zone file access, CommonCrawlConnector for web archive data, PIRConnector for .onion service discovery, RadarConnector for real-time domain monitoring, SonarConnector for security-focused discovery, TrancoConnector for top-site lists, and UmbrellaConnector for Cisco's domain rankings. Explain the common SourceConnector interface, authentication mechanisms, data retrieval patterns, and error recovery strategies. Include configuration examples for API keys, rate limits, and data filtering. Describe how connectors integrate with the discovery engine and normalization pipeline. Address reliability concerns, retry logic, and handling of API deprecations. Provide troubleshooting guidance for common connectivity issues and performance optimization tips for high-throughput scenarios.", "parent_id": "ddd20eff-e57b-4e7e-8c38-46b85ba85130", "order": 1, "progress_status": "completed", "dependent_files": "services/domain-seeder/src/connectors/CZDSConnector.ts,services/domain-seeder/src/connectors/CommonCrawlConnector.ts,services/domain-seeder/src/connectors/PIRConnector.ts,services/domain-seeder/src/connectors/RadarConnector.ts,services/domain-seeder/src/connectors/SonarConnector.ts,services/domain-seeder/src/connectors/TrancoConnector.ts,services/domain-seeder/src/connectors/UmbrellaConnector.ts,services/domain-seeder/src/connectors/index.ts,services/domain-seeder/src/connectors/README.md", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:38:40.657092+03:00", "gmt_modified": "2025-09-15T22:04:35.737958+03:00", "raw_data": "WikiEncrypted: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", "layer_level": 1}, {"id": "809af01f-3b6e-4e9d-bfe5-49007a5b9966", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Production Deployment", "description": "production-deployment", "prompt": "Develop detailed content for domainr production deployment. Explain the architecture and configuration differences between docker-compose.yml and development setup. Document the deployment workflow using deploy.sh script, including pre-deployment checks, service orchestration, and post-deployment validation. Cover production-specific configurations such as Nginx reverse proxy settings, SSL termination, and load balancing. Include instructions for deploying on both Docker-based and Kubernetes environments, with focus on the worker service's k8s configuration. Provide guidance on environment variable management, secret handling, and security hardening. Include real-world examples from the codebase such as healthcheck.js implementations and production-ready Dockerfiles. Address scalability considerations, high availability patterns, and disaster recovery procedures for the full domainr stack.", "parent_id": "d162c57f-e749-45c4-b3a6-43a7d4a45c2b", "order": 1, "progress_status": "completed", "dependent_files": "docker-compose.yml,scripts/deploy.sh,services/admin/docker-compose.production.yml,services/worker/k8s,nginx/conf.d/default.conf", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:39:00.911989+03:00", "gmt_modified": "2025-09-15T22:06:37.919271+03:00", "raw_data": "WikiEncrypted:1PcQ+NupkgQiLQ506NXlaizhNZeqS8C1INCZPnjb6ox3WfDC1Javv60UW9uJoqTZw5QTR5lAkR9YPLJg1vZtRwpot2vQf+E0qMnJ8iUczzOvv3qsx1bMxbxPQMvKGMH3mWSlxD7dBYuSVzeCmhY9rzJs7YixWBNrwR0mgxRMUB/+EmbB7uSzOVnh+w+lptDvQY/s5y+skMwSZaOmJRA6VmWevGsy+MAyNjIajOup1e09D8/WA5rmrmjSqf9q01lZgY8kIIOjb0+gAvZTGl3G0R0Cro24ZhlsZNRJyKrex9LofVme0fzBXVsZYaXUgbK0e5fzYpBP/yD/Bhs15GOGPKeTC9jpTfvUnSwwcjAag6t6TsQ+DLz4BYBjkWJKCarxXWFbT/mV6qxq0CP3xFp11zjd/BV4p6wGrfFFyB5wOMqnb56vB8/jT3m4QUbLhiW+i3t624TypMj/gUkNn0seWAOGMLc0AW1cdoYJilD9jdBvm6825qWD8vsHTmg6OL4C6GudF5jXPIlH1m+RdzwJ5FN6lTRx0RDLgxW775xSvcYQ/LV7TsU1cxCy8zmWmgpJh78sGpwyv48nqc+r+Lu3Qgy5GVKmrNm3gjSJGc/JCGUbWt73lkPHloIV9WJ3uevMGq57sEbHkaI13PcGnb7R2WwDQgiKtuZI38SvatcxmO8ig9dd/1R0jCMUzVmxWYdFhXOM6XbrhmrdboSbI0yGcOM9OhYvw0gfpX54vye1bXhr22pxnDtOtlRmDI14VhqGdkI9QN5msRBaoS1bBpOwtO4wwbThN+rz1ZMwLs2u1X4cTqaFP8To31uieNqR63q9jWguQc3tTNQq0Gu/jjj54TgGZWpkPUrN/16zroT6OV5YjndqFoIB59QRWaSBXkSe91ZOdPeulknixfwLyox51lO6d30bowqTL4wCva7OxKYv87XYa98x7ahGPBduCLEN+Dpnx17zdS29ArVIIMUreywN72rq3FOHI/RpnAM0PVDa3HIWonySjUwP+5OQXs4RqxJXA4b9u2YXGta4zkdhjsFhxUPp2MoSjr2arShswfg7TYwHrVTW+zbSjdC5Cd6haSH7sQXdVnH60qLtFmdA2yVJXN6VcOLmcQOdn6bRakuwHrs1ffguUi2hOhTw/d9twfJiCSzz/tBqNXybztlaphQ/9l7k9XpUVS5tZqJ5uRBl+fuWQS3/e4lwZe2NLBiOF46nWKV3esikogAacgGHsG0wjua21Li3AOZsmUBF/NjP8Bw17+sT3P74dDqhBmcJlY0GhrXtkna0Ua44AKNR8Op3bP5KIEJfc9ZErD8m/Ij94gwqVOUt9CDWOPsqWc8mC5p5Vdu3cojTLujuIVDXwzrraMW6I9IhIy++MpQLDe8KAxO6KJ3KPtXpG3TiwJaNnURHkRQ/jxjF2LHvKGAnHgyZCzKakk1rtwdoXPwvQu1IbmrbJ8F/ASzeNqnaYLelX49nKbAhGN0gYpnUk8Mo9BxulcCsYIDxhta3z3+J6QdcOs7cnHJtM093LMMpsJPuPOZYrADls0d4CrJEixrb0xZ80Qw1Vm8JPlIh4GncEodz3E+y6Ecx9yyXLoPxcRyRq80dIAO62RW5iQwvLPqGjtSHE/Rr4qkYXqfKQUFOL2o3Yg2xSmv78e7EsDXQlPAPrNJFSkHCYacYsqAiD3+Q0gLqXIkVjC6qC1IhvJqDDOTvVmD0BBna3CBtm7878KCf", "layer_level": 1}, {"id": "f14449bc-69a3-4513-a50f-b634821da35f", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Integration Testing", "description": "integration-testing", "prompt": "Develop detailed content for integration testing in the domainr system. Explain how integration tests verify interactions between components such as API endpoints, database clients, and external services. Document the use of mock servers (MSW) to simulate external dependencies and isolate service behavior. Include examples from the codebase showing integration between web-app and worker services, database integration tests in the worker service, and shared library validation workflows. Describe how integration tests validate data flow across layers, handle transactional boundaries, and test error recovery mechanisms. Address testing strategies for Redis-SMQ message passing, database schema compatibility, and cross-service communication patterns. Provide guidance on test data seeding and cleanup procedures.", "parent_id": "5ec7435b-599e-4858-8bb8-f0eef69cf07d", "order": 1, "progress_status": "completed", "dependent_files": "services/admin/tests/mocks/handlers.ts,services/admin/tests/mocks/server.ts,services/web-app/src/__tests__/integration/api.test.ts,services/worker/src/__tests__/integration/pipeline-integration.test.ts,shared/src/__tests__/DomainDescriptionValidator.enhanced.test.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:39:26.327311+03:00", "gmt_modified": "2025-09-15T22:07:30.319714+03:00", "raw_data": "WikiEncrypted:0j4RRfWJQdenLQLpT+DwLTyjMVG5sQffpuJQoSG8tbPh5or+98uRUrVo4jYX714zl8Ln3V3T+xK7E1MoacGU2gSVgYntnKEFCSnRSonpyAv3gKmdruuogKpJbyEzjCn4ySUeUo4sPKd7SY4uBmnawoirC9STE7h4jpC5WdJJyCnGB1Vg4Gg5eFtk43Va+Z0S4wLxIiC5jD0k4RDV76djE7RWipXJngGwJ0VFtwgyyPSgo08y+TBWmDDyv1ySV0Th7LANy+S3kYZqMD97idg3LqSi54C47qqrKSS7QP8WhZg2i01Vw6DTkrLDpkkpT1ukY4aFONRs6J8pi9vqmg71dN14ePQY96rEOl4RePWDZvHzmWD22jJacJtbauA/my//QxmOZp+a6BPqz/KkeB3E7H990TYOcloeJ3+jteJK0QKrYx06/3Vivfg2ZVWbJCFrhN37FB7RbdsQTLJkuDVbFS86sxZw36YH+hGMG/JgTK11Ik7kDpXyUtDRS1cU6J9vE5q7MjgmQMhcr0qU7oQgEPxiezw75tGe/PnP1J0GrtagoppP9zA+pOnp3rSiukFaVhL1bq3L0z/p7An4H5+alm0DvbRO2YCoAt5BZjr95cCshMDSRQgVd+8Yur13IRWtVxG/PClW6qqg0tKpAgfSZRFiGe6hIythzlfDTEgrXVfLvoLUafZny1uuotmM0Rhzfv6G1KARh0lzJI1gEEgRUxFwONneVEFPVMOJOCpq6S9cT9o7Z0xx/atUuDpaVmFPpwdlMIHFpTGWyyQtwOBbEA2/B4Ug8udCWEiSX+s0KCwX1NjeOwq9BGmCCMNV2s02J8Tof2I00BkplzKSVFcjZrfcNyPPlDST2kYWQqeSUWP6xQ20A/iAX7kdJRRCsNLU6pJcZmbQoeqS3p2aGAXGXX82rZ6FIN3sIjx9DH3soTe8kspoYdgvOXtSD04jVca18OmX0EQAnnTojQGZ/hWDouKaHw84Qcr4dN3jPQdAKTsYSL4KBE+sKxZV+E1BHbnQGBhmnIBQGUNfUq3DGMbVO0eIU9mBOXxR/vW4wIwQdXmrj36zNZSSZQUWc1dxeJFGO85Oo5vJQnOyjzh9YGBqrYlr9SBjYs31NYhpdvSQ5T5r0SzuGbwk83sGfGTzNG09By+VQC0bNPEtnDzJ+vqEUnKW6yYCqcffwhoGS66VXYcbZcsFM9vck5ZytOWn6jeXlH7Yr5juva/B0vfcqxmAx0LMdFrvbPrlPudlj8o9kFbqtpfVilqWe98naXF7HVoMMVuw/v35N9d/EH7uk2XCQQ+L5I8weKPVo8HVjVWQBd88vcqneH1i6iHWhk/rYpVN9FvyKUon0rNWUrUzyV8Bt9m5wfq89DNY70cCixC43cXFVF1Rg+3o1ofHjR/JXHKk3Q0wBD88F/YQhZcbtDgWFANqPKQlLy/sei2cMjl9fav6g92BfElPO1GEm2OYY1w2dxgmyb0UirIW1P1GK/6AdomH8z46cQwZs6bgd8Wq3Nx6mNlIkZdND0OPhhugXLikIOTi7b1sa6+0Cs3aZQ6ZhHDjh0wcVI06KmVWF2I+ly69sEKFGpeExde9yLAtwlON2FUTxWDFcejWG/gRIX7CyOAAzDn2h524uCRIiQEEuttb6iqRlz+md6b/Ux7XU8McCK8UU7XUiTn1ekX6+BvDATAK2JhkYah2K+6JUVvOdlERr5xAq3m/AdMV4NxpjerkdOlpoIppANm2hqYJlSHS62/xzOr0oGggXP51b1toSrgt9kAzlkO3FX0Qtwil+rS9", "layer_level": 1}, {"id": "d97bcd15-136a-470c-879e-3f2704b95035", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Directory Structure Breakdown", "description": "directory-structure", "prompt": "Create comprehensive content for this section focused on the domainr directory structure. Explain the monorepo organization with pnpm workspaces and the purpose of each top-level directory: _config for ESLint and TypeScript configurations, data for static data files, database for initialization scripts, monitoring for Prometheus configuration, nginx for web server configuration, scripts for deployment and environment setup, services for the main application services, and shared for common libraries. Document the role of each service directory (admin, domain-seeder, web-app, worker) and their internal organization. Include both conceptual overviews for beginners and technical details for experienced developers. Use terminology consistent with the codebase. Provide practical examples of how developers navigate and work within this structure. Highlight the relationship between the directory structure and the microservices architecture.", "parent_id": "", "order": 2, "progress_status": "completed", "dependent_files": "pnpm-workspace.yaml,_config/eslint.rules.js,data/categories.json,database/mariadb/init.sql,monitoring/prometheus.yml,nginx/conf.d/default.conf,scripts/deploy.sh", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:36:59.65286+03:00", "gmt_modified": "2025-09-15T21:42:54.022849+03:00", "raw_data": "WikiEncrypted:vEO7Ijy2dU+nEIDHKdZkAfwFWhgNVyNCKalPSpB9ioKUA535o+OXZ/qQirsyZXiKxgQHkuFDRH55agoJKdlsysWqQzX0Ve+kdUMfiF77gVrGTNJGhhvz3hoVOWpqwvxXG8IEs8eLW0MpWq+4RGA6UrYkEBp6U/vQLbGuZRSxkeT1G8ypJXjtWe+5PdNr59rVWuqYXi/rUIGnDEEiGHlztBpQ0usY9P3VizqF1b8VeyiDinxtX6IZdFhlo52CRS22k4ebRJQt7jIWzKyFr/h12UVqmiNuN+d+no42n8KRXQp7ttl+wTIX5aLY+W/TffizCt7ngM2IQmdes4tCuo75N45IESSnc1UdhFC4nRzW5wWpqNb01bEJ9V0lffqGwZfS4lkC/mOCQq0JR9rJFJrTerSssVeh+CFvmUbAYZzX8saH2g+OlC/acWnZdGxgnc6+kv8vTOLNsg0hOYmVjlSdlwRGnaGzD5W3525i4gyshxsqC8lihgmUY8xEop58UQ6rEYRZnrAluDE7XVx5rvaG4nsHIRTBXXVmF+dkxvNUFChXiSyTqfFf928wv0piA0vmwqCHYsY9+u538j7mnK3QmTlCB8TTWjKZ5mqfsVv/vnAkXy3dD7Egmp3LnxRawojAElJk0RwkbTKh2Np98KT6MCvPY3j9EYECA3+8I5Sfjn/MJ2WMzeGq0qg7Tyf5v9+fe3Wvq5Q+ZkGxWPcqWuT+pfYqPr09gLbzirwh6k+D3ZCXZtesJGG15VHh5JAuI9bnh1zja+Q2eOC4xtuamtvUpkzLUB8Yw5ncYK1tc0peq4FTznP+k1mdB+sULASC4rsCyns0g3ArYAxHvePf4nxhEUKgUQQkzFBkhUkvBJMkE/SSkCO/VxVMt2RVcmfKDLHD/lagw/wJbnTIsy8BFzBkSrvesk3c1XGzb8+SS15aTbAeJgkHAL+Trlb4iyet3PXA/4nx9e8YTSFMZHQlklELFXyWSRXomQmQh43aBj4Hv8C6gkv6OWax1rNcMff7n1OP6DBB/2NtPygnr2xCG5kG8U/IKfvERaFirfmRXqf1AqU4nGNfmbUmXMnXlranSFYSZj2IgT1MTafgZnFZ9rmH+pPc4sboIhNZlJJXgkXPoKhCjvACcNHX5x6ncmS42qkDjIMlNpIgIAvk4w39evQ2HNXCB0DRSyZA6Wh9x8VtIc/By0v5MbLL4zw/Kvx7jxiIYVqGkEV+IPmmXYhjwDygeensz92NPigWAElsFbo989xsyV4SxKpBSpqji/MBOUYk5TJBwOXDrLuQwjMBuVtXcKHy0kVNVWVmfXwS8d1o+d/9VYihR4SASZw/Zn94zmzPFC1kkIsqU3LbXYHxX6xar0bDVQ2W2sJSnwhNv4wSiIVyoa4YiqOWJV9rNqISOK686/z43xSrc3rL8EQIdAzCxCGlxliwBxNk8iHaeTMeq5c32/xpd0z2Cskyaj5ccIQqdp+ql51pdVs6Z+nNY094JoQaQrBwQwtUJA29L8rvXCFKFb42RUCgoGejB5yp334bQaE9VLx8RJrA74Rk7853DCFeO07E8gBAn0Gruk0+eFXC/H3hlqaYvSk28MyoIGRdxpcbfPvcFjIZ3lNb0R75xukBVtshtWyW4ZG4BeK+SSCQlNzIGkdF0GTxOqRKXxqjnpuo6hp5Zjr6xEEfs7UpUVF6jDemqDFnc6bEZWXjI1S/BiLcJoaoiA35p4Cu4ArHtNfgFg7/l8mcxMm2as9JztotaXv82H5cK6+LrevuBpHJIZ1nbhkwkbvQszrepRUzLTufigirb4BLrPdyPTytfw==", "layer_level": 0}, {"id": "9df73c8b-c582-44b4-90d7-c70a3062f381", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Domain Seeder", "description": "domain-seeder", "prompt": "Create architectural documentation for the Domain Seeder service. Describe its role in discovering and ingesting new domains through multiple external sources. Document the discovery engine architecture with support for various strategies (differential analysis, temporal analysis, zone file processing). Explain the connector system for external data sources (CZDS, CommonCrawl, PIR, Radar, Sonar, Tranco, Umbrella) and rate limiting implementation using token bucket algorithm. Detail the data normalization process, provenance tracking, and reliability mechanisms. Include information on the service's configuration management, health endpoints, and integration with the main database. Address scalability considerations for handling large-scale domain discovery and strategies for avoiding rate limits on external APIs.", "parent_id": "2fd75159-4155-48d5-b146-4fd895ab5d86", "order": 2, "progress_status": "completed", "dependent_files": "services/domain-seeder/README.md,services/domain-seeder/Dockerfile,services/domain-seeder/package.json,services/domain-seeder/src/discovery/IntelligentDiscoveryEngine.ts,services/domain-seeder/src/connectors,services/domain-seeder/src/ratelimiting/TokenBucket.ts,services/domain-seeder/data/categories.json", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:37:21.3086+03:00", "gmt_modified": "2025-09-15T22:08:54.242136+03:00", "raw_data": "WikiEncrypted:JLXqlo28VAn5UOPLsLiQgUdg6iWWSFGNX9qhRuSwqFJ2Y6AKKqgVCbnZUNnFCf+HYmnpgNNNt85JCInwZr5xHIKF4v6LFgjU2mxJRgUsH3+zHkzHKd1y0GP0BsrEiVffRURsq08FsJ2OYqY8pxBI7IXA7AnZAB6971ZgQPlBVwwF0qj9UdWiP4u8GdzGc103IXR2HR+5KlgI61yKunXHSyBmv7e4yCtNWlv0+6BBJ9U/8aV9v7VoLzSdrjoxIQXV/PpboXC9pxju4NYHOntjttQY2C5Jr4s1uhdEQJisZUcxfnjU3O+wF6MI8PunnQsqXbPWcYozi2APKSgn23chG9L0gGwivhiFxJvAWx0zHYbsbaR8wrlCZAc8RwehitBMC8dRGG9qOcNMK9vBUTSwzg+EDlPrTnAxep8X6oAuzlisSopeWDKIaA7CUzi48fg2A1vgQbWWIAJmmW0O8QxXQeLXjwbZrEhNm0vLILCy0McEYz7rmzcnJjFstJPJW8rrP9Lmcnej/4DJK0VEYnxSdBUw0sd8Sawlo0vmTY86dOpO7BHjkKfaN5neJXZdyqnjeZvoST3qGI/mkuul/zVS91eQn2KqJ0aBXl2JB6SmV2oa5eIOGxSZusa4tj/LGcQa21iPPDTgjXgcWx15RClX432g0Ml6PHtW0xqeIyze1BEcs1f/LDwOhbFLu+lFDV7yoTsO5LTmAgaCpwRYJPtAUjIu716bRM2AyyrDVtfPAbRRsgU9skpByMgrMax44fH3qdPUROHvB6HYj/jaN5Gt7xfNzISIbhLRxT1nIslFmJtQynNGONznsDhzI+nc9gN4bG6ZU7PHltJDaKGknSLiAXsXbyG64plegdHwVLrV76g6jbYPR5Ty3QCNfVJ0To+FDnGRfGgx69/ylhH6Z8eI/sD8I+K2u19QjWqiAskMeUK9V7XIKM4jzSx93K73d+SSDQa62hwVZJFcnscZPPzewcjtPZg4zsG06tE6xGM9BpzL1P3vsbbIyrBJZoU1OprQYrAuKtFNR5R3cZ1KABcRQIfp+H2LjmTRnGTP874kuSmAve9FvlP9voKTVwjb8rguv/hvvQv7/06SylEBHgXcwMY4cspXZm5Jcl+iyuME48siuG8wcBo42cp4pSwoIHazayp6WZRkDyBAnxnkTGkV8wJrGDFEFkoxcg/VukhNA4WwYtk+R3SBrUfdzbO/KKfdufLgZJv1+/Y64amkQrUmXvAv4GwB9m6RvFwfvKIhhjCod0JGxI3MzJbMOvkFTB1eRF/tBjS5Uj3FilDg3ixeU9c3Ss/ZEuXIgF6gpYytRYqEIUmGUFfqwfhNzupKsx3F86T+3F5WDA9ZyZVswUDq+HAjdgef8B8w08otCVg7XToj5QsWdPUd1zas/W9hsqp9sh+GO3KPnHyg0X9x5a1biCktQ1mHDghkZhTPrHM/xs7UYJwuCvdAY2mgHRSotPmQemAcJWMoHAmVVqp1bx6bW+qUVK/HwbeRuLrW/YBJhXIKgTmo8C6QlP3N80YdOmckYffJw8ErYgYvC+oTZmUsb4LpfKYRa1Io6Z1Xzg+rQJLFPid/eQVH7/O83bFk3BbuiVZcBzxEtgvrpHSqLfEeZu6Q7hpGI2e7K4hdHCUAvRc4Tqlsugh/I2qhcJ702dIoP29v9+tMJUc87k11wkbu4u1cami+VCWMHIA9hwMCA7uXMo1TdykL4MD/FwpN9apBK6W+Xq5XtXcAp7ZOpJo42vmP5PE0zdxuY8/SHCfhAH54L7/ySW7lFeKOk1+YIbTBKUbcEBd+FectTB68NX4YtvIjgRB1kgF7ycmCh0VRfi0=", "layer_level": 1}, {"id": "ba8b07d7-beb3-4d6d-a888-ce45dc86f37e", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "DomainDescription Model", "description": "domain-description-model", "prompt": "Create comprehensive data model documentation for the DomainDescription entity. Detail all fields including title, meta description, content summary, and AI-generated descriptions. Document the validation rules enforced by DomainDescriptionValidator and the quality metrics tracked in the system. Explain the relationship between DomainDescription and DomainAnalysis, and how descriptions are generated by AI providers in the Worker service. Describe the storage strategy across Manticore Search for full-text indexing and MariaDB for structured storage. Include information about content moderation, language detection, and SEO optimization features. Provide examples of how descriptions are consumed by the Web Application and cached for performance.", "parent_id": "54aacf35-eba1-4936-90c8-2768157d3278", "order": 2, "progress_status": "completed", "dependent_files": "shared/src/models/DomainDescription.ts,shared/src/models/types/DomainTypes.ts,shared/src/utils/DomainDescriptionValidator.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:37:25.739878+03:00", "gmt_modified": "2025-09-15T22:09:29.37367+03:00", "raw_data": "WikiEncrypted:JLXqlo28VAn5UOPLsLiQgUeJ9y6Pq/lZu3ix+rRgaJYrNnSdWxZrBb4rtEbtAUDtZegcLdC5BnOMgC4EdVjpCBVRwFKs0v7lMZISkwqq96T9+udEBXTF9LvEWmoMideip1ZcJotFb2n8c26tHF2BstxFwzrlwtQIUpN7Pz80os2tEVRzxNRH7BdVDo9vUCDk+r37My+f/Z5pfEpJAyMLlbRbwTVCPS/8QAHbb89gD7Dt44wjq8l6RffG8k8TQ+/btE+eUFpKjnNxMPG+FKqPTw6HxR93AF+G5ARUewmLHoDS5A/3QhATbP5E+atCM7srkJgBwaSSKo6ASwstsQVElKv7sj0/6BH8CZo/1V4It3aKNbqxum2fI8usFXz0ccFLFs8n9m05mlHqUoI9TIze5JBGLNmD89jfX84Bc4d5BLO/v4Z04VT91jCioUDzvAfafP3coIK7cAeTdlvuutU73X5xV8Tf97u8Xs5NjzsLuZ5wGKDCDFdOtxLN5tfGiT7YRuNitzploKvOj1a6zkqQb3Hs18Xyqxi2+B6a5xgte0td9sM6L0DzbIqQd+AE+zh+fMyNn+Yo6KjDtFZBY8K3VBdPVLiyIKWHjGtKvu5Ib2RxCmu4m/utd960ZC8BxSTFFzoLIdsKXGGLbSKPof4JvYIVEgpfwXN3uLOg3ODy5WuVHer586ZzvnvuPYPKB8bVoPRho9UzoaOh4nUeN5mfcOUURQ2okQa7LL/fGEfjbYhWLssC2VCCzyqjC3INIBhsiP6L/LkPPdcLiBfSED3/JArhqlrypgQzYPwWI2Mv6lhQXlh/RqzhNx0utQj0mhqhpiMVwBxkAwpgnHQ4BckwAoBY6fWCLv0u3jbrwxwxu8gFg+hezH3aPtXpBTdKOF0fRrR9JaBq4IqcBC5eo4XF8f7s5x+iySqpUHgBIiwKe9sBTikYVH5yD2/yQeMDRaOmFZN2SToj+LZBvUbB4j6/zypGfTCp1JkEvUstE4by6B42Hh6Fkh/XhG3WW2DZaBO6LhistsZtVfqYrj1i8Wlw5yU/TKlWlzFyD6jeRtBvmtcM68plKMH58OVBS/dz/b0NUL9h3aRWq2EGwgzqOVrsFom2V93A4osY4ws9jVKZHOfHQMLCfg+p0+a3a+bAZ7UWCY6Zq2oFHueRypfoSO4V2GNMUrPfLn8y9vaXWhu4iQk6pMyzEsaSi2YQFw+2L6CQsA+KHZkcUB6YDePfnkVIXddwPfkpQMJ3MPVw+9jrc5+4CxqWZRVyh+yNNRk9ddrtq3Q9BJNPQWEDffTLLWT1fnpX6ZQYw+fbEZTHnSiQ+SjivCE+3joPHHEE7UPuGslNvoPTKPv8Qtku0mqJDYG8EE+UiN84AGaR8aQr8cOihBjewhrB5mo2IqdvbWAmiUfD96z6OGxwDonXf8+R9ZcGrurUGGry9x5oqQGUvdPmbm1+P5q7k6hw1vNmvTzg3lIfxzakMQ70FxcmNwQEWLKVkd/B49feEWqAPn62ohn7mmU=", "layer_level": 1}, {"id": "ad279493-c280-480e-9426-8e9dff83bddd", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Rankings API", "description": "rankings-api", "prompt": "Create API documentation for the Rankings API endpoints. Document the HTTP GET methods for retrieving top domains (/api/rankings/top) with query parameters for category, country, limit, and offset. Detail the response schema based on the DomainRanking model including domain, rank, score, and metric breakdowns. Explain the pre-calculated nature of rankings data stored in ScyllaDB and MariaDB, and the periodic update process by the Worker service. Document filtering capabilities by category and country, pagination implementation, and caching strategy. Include examples of successful responses and error cases. Provide client implementation examples for building ranking tables and visualizations. Address common issues like stale data and missing categories with troubleshooting guidance and cache invalidation procedures.", "parent_id": "4cc1d72f-ebfb-4439-994d-803e620048d9", "order": 2, "progress_status": "completed", "dependent_files": "services/web-app/src/app/routes/api/rankings.ts,services/web-app/src/services/TopDomainsService.ts,shared/src/models/DomainRanking.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:37:40.426662+03:00", "gmt_modified": "2025-09-15T22:10:12.037163+03:00", "raw_data": "WikiEncrypted:x7xafZBxVSAVM+m13jmM+0U8THsHt3xNjU8ZwUm4ZM8cY/IdfexuR3PqN4r+C5NJpEqulJCKRrwc+js/A/Xgh28DvKCs5+dDGZ3Dt24Qi6hvaBY2cwVDUWlmcGzKTVSQZnrR0VaQ965NSWO3m62H5Iin5iWaJJ7Tx2/vk8uj3Q7Dp+B/qd6wQnboFeG+6n1VIUhxQjKr8Bk1BmD6LMptvs5unfxAWfSPVG6dxeKOgZW7Z+MJAgiSJMmUFv1w0HwbQkeCZbU5ND5ROSZundVDxRYoXlFikUUf1a6MtOQgmWOn9ClLyCO3sDFNh4zn+o5T0omdwPOL/XNn+oSlkvRQH7CUz3Z9uTSZ3jHQSJF0HwLsX29/Fcr9Bsw/CyhrIyVEBhYDL6ni/mRRdJzt/CzVzTt3hWWyXU+ucuOxQck7/3AZt/PEcS521elg5FaJg2UQbjYuSZADAJx55EOJT3XC/bs9i9r0fZTKTdpfumtUZGLRBN6rt6EOoH3+2VW5xpdT4xCyjKeWglMW7gidHQHFURL7CePzFtx/JVI8y5rQNOCDlSgLjFzMSasvHF7+XtlE57rJyKD1LWjgkMFfciSFeJQOaredOzaZ3PSwgOH1l2LRCbDEgaVsd0jBNdHsn/W/hkjAAW9LgfO/VtQrpNX6Hg3x1qQ7hJQ8qUYfWnly/nr0Sh0qAWU5J2eu/16TgKZIeGCB8JPJgY44/oWbO6fX5ZDmMNCJH4ctYH0G9y4f5igylt/MT7k+JnCwK+af4CfWDAXcA6q1oT3h2waHYRs1b4oFIcBKebkfxGjpJxDLRo4Xj98usJtsfCumMLAdiuGlSHgJ7dLk4VkFrp18AB4eM3gbkdYDN6d71m2APldghGPyF/tKZ5Gq1EJPFNBmA3Q2ubN+Pova2eaxIvxmJkG0nFN3IEyYwCVuW5g/i51A8Z023HgRkGFKgLbDpYTbh129vk9W7IRqYvqUsXHk4qziqVRexqGEk5G5Sz0uNlKV6iROcoIE+/aBAMHwuStnwZ8mE9/kMbuWlN0ICM1sC+LiqktWljEXMqORDEOsNDxgOQb951pfZESTXWmJg6tjIgsx7Qdo1+howoHpEsMcxZOLF1lP9sxElu9ydJVS26EWW5CZIpi2grRUZBswWLDSmlYMswTnJ87zzSjthw2VFt1EJnVDkYE6LTriAnh5j1RpaJVUqSh2dxCZ0WUdnnZpcR/G8CcxfN3H4JC8lfdsRsj6lLOGFeoZbpVRFtqNIGRySkB1dst5X9A2FxDFI5HGAx9NNIrEyoRMQzS5fILWALb6X68tmpa5SWyFFr4EeI9xCAYrFNvG0Q372KUfan/GumIBsup5Nnh75Hyy2EsXTuAAvOKE66HANyEOFjTkTrU+I6tk+B4Q1h3YPLWJkCdABVEC5OrsRjIoHy8Kd1zPYJwubGlOGECSb9OG/MGKdSJIXp2AQWFg5fUwKWzB6wqewNwwyVO3OuwQrDJ/5+AEkjbJfRxWC2Fa1F5TIlaK2mx1JZTaWqp3BZQ86ONaL2oCC0xcYvlrG18ZoUdlUjRvMQXZeKl5k0DV/B4zTPYAlFMydJWwJdqMmt02an6/cCjmFQoB", "layer_level": 1}, {"id": "50786d98-c54b-4450-9cfc-b00faa627a0c", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Top Domains", "description": "top-domains", "prompt": "Develop detailed content for the Top Domains feature. Explain how the TopDomainsPage displays global, category-based, and country-specific rankings. Document the ranking calculation process including the CompositeRanker and RankingUpdateService in the Worker service. Describe the DomainRanking data model and how rankings are updated periodically. Include examples of ranking queries and responses. Explain the weighting system (Performance 25%, Security 20%, SEO 20%, Technical 15%, Backlinks 20%) and how it affects rankings. Address common issues with ranking updates and data freshness. Provide guidance on customizing ranking views and filtering options.", "parent_id": "e3ce4565-5d2d-4e25-9f46-14b64749ee98", "order": 2, "progress_status": "completed", "dependent_files": "services/web-app/src/components/TopDomainsPage.tsx,services/worker/src/ranking/RankingUpdateService.ts,services/worker/src/ranking/CompositeRanker.ts,services/web-app/src/services/TopDomainsService.ts,shared/src/models/DomainRanking.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:38:04.739231+03:00", "gmt_modified": "2025-09-15T22:11:10.042128+03:00", "raw_data": "WikiEncrypted:aNa6WtdgZz+yXCW0WVqfmJ4ItA1m1Mgus9p7bMprapve5p/pvKd15Ty0QN5+SqIMAAhsr+Ruz4OHE/FCM04zFSUIE+Vtz+oIS+dZ/xFK9IJemOl8nfOkBi7xJYN9GtkkKocg0ZkrgLx/vrbLp9/6sU7Q0O/J2cb+7ozSnt9DHU55hQP8qRtz131v8wi2G3aP8RWFGSw0Zut/EPdfiBapI8vZRSOU55OO3N7TO6NL87AqLnb/SLwileD89rjoAuliJZ70MYKzBg8Dh82pMmPaverW29T2b8ATzLJaWawMvt6WfDaCH83vno3vSTzSK4SAAhx9BDsfFTAPjqokhjJcdWsP/CrKPE3mQrOWwbysETIdKDOkvDRolxPZVamKsWfDk9QmPgGZbs5nk5bMQBjsAASxHerflBJKbVowGjJ0J+zY2vfPXHqh0jAi+yqcx5WDJQg8b/R8Z4A+KhXiS8lbAmXbq8BlF6qEImkPa7HMKToSEbA/SimSwGAH4VCRZX4u/d3OdfNcWb1d4h+8WV7lAOi6OemmrnMsyLtAcUvOolAfBV3HlEjfKLSxbZpeqSCC1YhZC8wo5VHPwMnOf+zI+If5T8c23Hczv+I7mSULovaMfzNRhUqRWMQ8N0Qs2fP/SP1v1K2b5K1S1qRertSWWJdP9wAsyHwSOeFpO2Q8/58eInfp1FjcJpIA7UmhuO5uYfRzjEX0Yke4mVPRsnZ2M/GR1QhM29aUhT4UZi9Ttsta7VN/go5uaMLijkcRup3OmIbE8WGrPOr7Ejzc6qKn1eypb4lKXSbWr1v67qv8xgfBLTM10/Qtis5I2wy4Lvhph6zal6BJyADGL+Uui9bGF7V4yxS1FTfK18n0QvZuYiQeYGKECMPRBNRUyRiQbwV1Amc/16oriUnOD5QqiIGPXUtCfVuqKBHEe2UBDvQTGhySQmbw4YGBRr4IP8HU8eRTy+Iswu+ZAuUuXw6IOKFDpg1pB3AhxlMPwQ6Qt7xCU8tUPhmSiFwNoLm8VeFy/hm67VGLb0qDZ45tqtw0WbQSu6RSsKE2XFFiEUbKw9ErnWiHaZXRRdH6CL4EZycv0Z60ieHint419KUjljEIF6Ijwn6BgquLg0fEA4wNSXM1bbc97COQOKJQuSbS5Yywuo2ywJlnnm4t+xNgX6R99QO+7h2dRXRcJziBqjIaKu+HP9IT1OM7SxU7C0VorC8HMNbk2JzHLQbseyCvANLY7lEKPbp0OVcTKa85OJSzth8GbqZtfBzdNhtII+vJ0w4UVW+PDLYOcyeA2bhjLdEzBuekVbJ/i/Auz1h8UpFeSvynC+byH32g5MJrebKuBZk6hgYhZoNmspcdTM55CRGgqrpcXh/uAfhavfLj+PwdRux0a81hN0Qwqr/q2xMppU26DBPlX7XsHADrE6IFVgY47St8E5i3nUc9XiztaKSQr1mSg6mAk/hKExKEJycdHZJbzM2BzTmQ79a2tzTYSlPy1x056JPXjsKkeREdAeFBlA+pVbU=", "layer_level": 1}, {"id": "0dfa7ea4-bb9e-4970-8a0b-13844a4d82b5", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Security Metrics", "description": "security-metrics", "prompt": "Develop detailed content for the SecurityScorer component. Explain how SSL/TLS configuration, security headers (HSTS, CSP, XSS), and vulnerability assessments are evaluated to generate a security score. Document the implementation details in SecurityScorer.ts, including rule weighting and penalty application. Show how SSLAnalyzer provides certificate information and security findings. Include examples from test cases demonstrating score calculations for various security configurations. Address edge cases like mixed content and certificate chain issues. Provide guidance on interpreting security scores and recommended remediation steps.", "parent_id": "86fb1ab3-0d9d-4f57-bdd1-151e7cd91e0e", "order": 2, "progress_status": "completed", "dependent_files": "services/worker/src/ranking/scorers/SecurityScorer.ts,services/worker/src/crawler/analyzers/SSLAnalyzer.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:38:05.722372+03:00", "gmt_modified": "2025-09-15T22:12:34.665162+03:00", "raw_data": "WikiEncrypted:JNhY5K+GVMrcGagKRy9Mk7vP4ohrf75iUjESF0gX3fxmJAelcmzcjW4dXqP3rdZfFCRmNrls3/3A1q0E1AnkqME6TQ8T8SfiB6jZB+rpnQ+Q9wF6Fvb3Qy6tUuMYJL5i5r7dsw+Yw06JASM92t2RCzIoLNUzmva8DxrV8c+8v7R7hdKi/70CG67VszZxm4/KTGiZoiYdz5pJgGxQugrfIWnCsE+MYJzfeljKPVzrCYj2tTrbXMwfitmOlSkP47Fwo/EukHfib0RdOXEPowvazkYUbHQRo2kWYCxb2XmcbxMzvjEIMGg5nNBgx3618O1ksdrNEl2b5Gt34HQvF8WhEvTPJMvBK1QS+TSJmIx9PvK51/HKYUB+m9WW7xJGiQXiyesHWhvk7uWWZrj0Pn5Um6MPYDq1kjjZEuZLsk3lL30Z0S6T00HIYow/9FTXTWw5KXe15n5uUDn8LFTljT2EBW1EFXdnp+okJ7aeEup/UxbyuxEUuI5bw5QWKJLlZBX6/QuAaKlnswrxf4DoY3AIgTUlAle66rf6ZhxXosFRYLxpJbQGRx8faVpq7PV0CaNU0Chwsy4F6kYswyOMg+EzzM47L+BE5tlcv9VeEbfSVEbxbDpQfWnkwsJxzQH3dBs9LPW2UUh2VGRBXIMXxCAKLiIy4LGb/CkI4PJvYB1tO5hIe5Ko2/45d2vmL1ZSGa/lHjmPvWP4BEMBozFnRvToUzeh3+OFaZcQApno0dIlNTNhSBzBQvK5ha7Sct0dbkpF8ShM9J1aXSeb7Y7tvtyMnLs6TvA9P5QdzbBDyk4tSJcEIQezAj3iuDPtVu+sdnPSZVYo/oMAWcxyDM/Vd2csqGNu28+okF7qfHX+Bonfp2Dwb1PVh5JLEBctSvQhc4L9lX8i952EdXC3y1pdqQbxhVqiGrHLvXGXf+oyRinxSSH5GaVag+bdLsCyoMBXau33kJUWm+gTJaX0Dx5n4tVRdVxCaHusqsSAOR+zJQzAR6o/e93DoMfaf4P91ySUbVhyF5q1OV5Lm5GXfe47F08c8oOHAVEhyUN3r8nzOP/J6j4fMEoLi8d7QHZ8SYT4T4jR08KeqqcOBxA7o+nGHk95UAq6cHxzIlqn7eBZZrh1UMfA/f9o4wdhMSBAEkgsD6ekaYy/LFmLOEgkJuk3UD1QkjpNdHqQB5hbZosS+vuB/bW6SGnUgDEgVrCii1JpfxhbypnZnmLLcQ5HjbV26E9gJtB3uPNIcJQo94IVVluhJBd2LM8M/8cWQtPu3AKLnf95pDD3SHGX0Cl1ETiQQYkuJhKWkzTV2nuCeaDfR7+09Rg=", "layer_level": 1}, {"id": "bafe0343-307c-446d-a856-f7eb3ea04e8a", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Data Normalization", "description": "data-normalization", "prompt": "Create architectural documentation for the domainr data normalization process. Describe the high-level design of the DomainNormalizer and PSLManager components responsible for standardizing domain data across different sources and formats. Document the normalization workflow from raw crawler output to consistent domain representation, including URL standardization, hostname parsing, and top-level domain identification using Public Suffix List. Explain component interactions between normalizer and external data sources, as well as integration with the validation pipeline. Include infrastructure requirements for maintaining up-to-date PSL data and performance considerations for high-throughput normalization. Provide system context diagrams showing data flow through the normalization layer. Address cross-cutting concerns like handling internationalized domain names, dealing with malformed URLs, and ensuring consistency across distributed processing nodes. Document technology stack, third-party dependencies, and version compatibility for the normalization components.", "parent_id": "71a79def-fdc0-4d40-80ea-b74ed07e1196", "order": 2, "progress_status": "completed", "dependent_files": "services/worker/src/normalization/DomainNormalizer.ts,services/worker/src/normalization/PSLManager.ts,services/worker/src/normalization/index.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:38:27.306053+03:00", "gmt_modified": "2025-09-15T22:12:41.31903+03:00", "raw_data": "WikiEncrypted:sk7Kd5CVxFVqsc2tAs5xAF8clCRRX2iFEUkWZlucOMK7rwnjBSJK0LsiHmq/u4OWiPDeZCYmNLQUxUy5WOfwKBwnqOJZHiH0tq2mcJOYGUOpPCVGrrKLQ7qC0NdUOrDjUyuabe8RnRa/Ue9TsTgBr6FmPIh2Ma+vCKeVZU9tcJdIz4RFGwAY1XmVFjRkO2rUBlH8/3PhDPc9ZbUO2m76lckM0/E7vGJeDsJ5brqEE16vRwTQUaqQRjpoPwaKIfRAT6xeyhDxsyEWAXKZLbPF3Ic71V6cpPFAMEp5FLZJgakp9OwMViUvl3oldz4TsRgRlA8yuQSUGQU0GTVllfsxTDMlEYyBG+W4hQaWBUP2SirNdQ52dPIK4xqUNqy5DRNQHwv1Wk1OvL/Lko6XphgSIue5MgAt9B2HPVY1sn1ZRHIOP76qF5R388hZ3jyVDM3irGr8gdD9YmaFdU9Plte0NtvSH/83LBCuD4GxWuNY8l9uYbXo6gVpx8gLsLtWHZCZDV9nmolP3VQGS05jmPjhYVgmgR5X1eknTCELEGNXD3V9H0/VrRcGenXksYX+/cYfSkUySSjzED8027sCIVY13kellsAgk9kYx8raIN6izP4wPjwI4GMRtjbQxHsywxsSgE5007IG6L+zpe87tS1Wi0qbZ8H/dEp/wvCrCkv188tiVth4GkvEiP0jJ1EePf3p8Namf5OxRfzhfAGc+0Xd/CWNC5U/AGHQQpBKTpF3XVKfWF2+4k37ENrQoJ1usvNAoyui1PmvjB9guQAd2fCFyCmMKWeW2L/NwvEpdNJqqMn0GL+OU2JCSTfIS7o50F9MK+ztcAXeIKf6rfc6hs9eDH4d+PXi3Jb5Q5hZ4/T8RP916WkfKLuTjT1Kn9BuTVQkW5z7NMAA+GVsN2hOEgMHVgE3jWZ9cxKS4pRal9EvnYv+76vT+6aXSDh5fcaR9t04kyHK0IOVe/g3Kxn1XY0RD2Y3gylSeOf99v88xKoW49valiyrGFx0Otcz8cV4usFJEh4qoN1izUvV5P87DGPZqQR7xJlwlUt+al+pPpR/ZbLKfQ3BRhqyIzWXi0rhVZfF+2NeyzudRzOZyDkbZkvSV4AO8fIkNPNaDrFvk99rdRnO59HrYhqHV0kk7f+HTKKtM06aNtMsajhfrtg5n5o01PRD3BLbqqXaELe1U1RdcD2Ay9hhdCWRk3eWnFE6EQIxbCsYcNr4vwWzvZd/xQxgila8ZdR1iMYH8Ka30BwmKd3Q0Sd4tBpiw54BQSWadiP7f+Hc6zl4BWR1GyFd6xYYr0AjusbolBhZbUc7v7TSWtSBpIrc4pT/pddpLnD0F8xwLQ0L69zPKn+fp49l7e4OE55J7GvVpy2ExE/9XV44kmbzEYKT8Ua01k3o3511dSsCfgJP8w2Z4pucQu7EVKkvigSkyYUbacWOewe6ipgrOQtqqtFbyxSBm1KUgxLH5LXHDByQAHHEXwAuR78lT4HlDm33GjBVVb7p1YHFR8wu9wcZttAahP1tzyrFTGPUhAIfvnloxah/eH0PkK97Qo1RGNz70Zo9XfW93YmZdIWCGFFK+sCtdnwPT32W3Xn3nrxTrou1xQzB4oNoNOJWYNx9Eecnl6kRN4LeFr6YvLqrBCT4MrbXt247obQOXS3ixsNrBVfKS+wbCjvyB50f5rWDrBJ/2PSJWSxP7I9Ri542PSqzqNARfD3TdQnQ8CS5adpcsGG2rIkHzLUY4S/CM3pkt3BBFMV4J4PUPX0XirZK9t1IcBtQQCtPy3MIYnntbfzjBYkVMWCfsjeKRL4l9hvYlfGtTD7pUpSkIf0lspkxNG/8AdUG9eeZfP1Xg1fdHMv+5g6OAWJmgV6Peyru8hS/4OTLdVc7tDpT51038mPinI+VcEVn9OJkJbL7drT45YWTFc9I92UQkB6Sc5M+gKIHEELYUQeBkHx5IrYVwju9tT8=", "layer_level": 1}, {"id": "435af0ef-9921-4186-b358-2f30155aded6", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Rate Limiting & Reliability", "description": "rate-limiting-reliability", "prompt": "Create comprehensive content for the domainr rate limiting and reliability system. Explain the TokenBucket implementation for quota management across external APIs and the BackpressureController for system-wide load regulation. Document integration points with source connectors and the enqueuer pipeline. Describe the CrashSafeReliabilityManager's role in ensuring data integrity during failures and the ReliableStreamProcessor's exactly-once processing guarantees. Include configuration parameters for burst limits, refill rates, and checkpoint intervals. Provide examples of how these systems prevent service abuse and maintain stability under variable load. Address common issues such as clock drift in token buckets, recovery from broker failures, and tuning reliability settings for different deployment scales. Include monitoring metrics and alerting thresholds for proactive system health management.", "parent_id": "ddd20eff-e57b-4e7e-8c38-46b85ba85130", "order": 2, "progress_status": "completed", "dependent_files": "services/domain-seeder/src/ratelimiting/BackpressureController.ts,services/domain-seeder/src/ratelimiting/TokenBucket.ts,services/domain-seeder/src/ratelimiting/index.ts,services/domain-seeder/src/ratelimiting/README.md,services/domain-seeder/src/reliability/CrashSafeReliabilityManager.ts,services/domain-seeder/src/reliability/ReliableStreamProcessor.ts,services/domain-seeder/src/reliability/index.ts,services/domain-seeder/src/reliability/README.md", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:38:40.658255+03:00", "gmt_modified": "2025-09-15T22:14:03.827567+03:00", "raw_data": "WikiEncrypted: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", "layer_level": 1}, {"id": "5b9541ae-7682-471c-b4aa-745a492e1dac", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Docker Configuration", "description": "docker-configuration", "prompt": "Create comprehensive content for domainr Docker configuration. Explain the multi-stage Docker build process used in each service's Dockerfile, including base images, dependency installation, and artifact copying. Document the differences between development and production docker-compose configurations, focusing on network setup, volume mounting, and resource allocation. Provide detailed breakdown of service-specific Docker configurations, such as the admin service's Next.js optimization settings and worker service's resource constraints. Include practical examples of Docker networking between services, volume persistence strategies for databases, and container health checks. Address common Docker-related issues like port conflicts, image size optimization, and build cache management. Cover best practices for Docker security, including non-root user execution and minimal image construction.", "parent_id": "d162c57f-e749-45c4-b3a6-43a7d4a45c2b", "order": 2, "progress_status": "completed", "dependent_files": "docker-compose.yml,docker-compose.dev.yml,services/admin/Dockerfile,services/domain-seeder/Dockerfile,services/web-app/Dockerfile,services/worker/Dockerfile", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:39:00.913322+03:00", "gmt_modified": "2025-09-15T22:14:22.551931+03:00", "raw_data": "WikiEncrypted:h7ADvj4WLJHyulzCl+z7kO0sWr/7DVDWqnuY5jDnxosIim0nvkp2TZJufhiYuzFdm5WN2hFXBKEUQvlVECv2nlt5Pst+0cp0jWGrhgVHSqWWxdEvGERQnPhQWdLaTw893Cp+5yu0YlaZcsSF/yYM6F0DocQJKn/xPOZPogq4YhBEUPyZdBTIF9MUJdRxJu+FrKxs0GimxAkzdKQp4jRq93oIq02xBXnioEPxhLSvQ48wDX5sFxCbiWFFfEWJ4PJWfzRqK21J4kRD1uwwYE/K+eMJdosj2iNY03jRGoiD/kvESHQaxn5OrhaVxHiAMDg/RbKA63C5v6K/vDGqc0jTt78XfFw6+yrKsr/ZxdJprn24mWKqq+st/nc+2TVdgNq0XwT8f4PC9xPHiRWcNc/0X6B94wk0LWcaytDz7rbn6Ke3luCzTnfhgSy+rNqAF0UH3YJu0FyIaKM1I44EECP7Fr0BaIkOqba7PVfZOBsU1nzWvCu7+/yFJddRmarKPRmz/Y+HotJmCugQT4yt/bzvqiZR6Y3X+goCANF6R0F/HsDJK9sFsGpSj1fqlAVrfZnvxDNSgTarX7kvygZLrWO8DI0Upnma7UFWVAtCC4Y5DPn3bPaJ0tluB5l/1VuNQz3odOZbJeHReNBlz5SUG+gMauqNloTD0TGP9dl48dtLaNN2gVHPXyTXLc3zHi7USjQHsjwbKMNq/plvAbsoia3S4ia81uqU9m6V/k+02N35NtnopUM0KXQvrU+tPPYAd+rBL458KePWDA8TdrEo82jaAPT/bTi4lGyaWR3m6V7hWnJ+PDst5RFxa6dyPmbJESk+BMsdtTKm4kU9zFw80u/7BGBJcxiaVEAACJD6vp8h3qwTVyVOAUVXJmXxrboemAaqd4TJzaAtrK0SqPi8Duas1tueaJIYfybf69h1mE3H7XnVFouIEFHmmOxmNAnXCuUgvMyk3Tgtei/2xAJazi9nJWCO/m4ixXPxhqMpiy0iNOuwHUGW0YPJrQ84XkwbaoYgwVHQeozwCyunf/vj1Zb18HrjtgsEDz4PLwcqEwUN0SB0ZAnURifknBNwNIQc4uJY25Atr9h0jl5yQ2pAhDC8qMp8i+GiLgrTPmQclvJFfjQ1QGJv9bGAnZIvMPeTlUwCn9uBX2YucEeXl5qYvm0whEads2+9xw4ZgXif969ZfNAc6JJlt4SA1M1LJGPYdD3HHgWD822NhJvujBPv+HPYW2YJUXXtqBhtfmRBnNQ2MEiTn68Mva05qL7pefhfEYOMGF6s9t8o6gbqRqlYm9Bf94s9ThCe1/Lx8ol0zDQjZWEZZqwRzz78pEXPBokXAqHlQx+0QR4RWqk6DdNuSk3JKRB01xwMtbU2KxRDG10UB7aEcdndVxK+4xJUA61F7wZlhhfTEu/xDYmZez5Bf1lDKN2DLkD18G8Gal511dCiL6UHvZkX0C4g2Hg7wGh+SZiCgfApHgrdgVwCJNylBZHXhzMzEiBVYtN++4eqXlwSnaMxeGdUCmTifALdsj8lqI0xJZ+stdN04Y78UlcYINUP6LjoABbEQZN5UwAW+uG0WieFll7+4eFvmTTxMb3XJ5B6XaWXj2rM9xVE2TVppv9f4A8dAntrT3HhVBOT+qHTtCgFTuCo1R2cOnzsRHf0ImwEO6fqkuwB8RgTA4mH/IYiR5qvMI3uFZVyXdTfy1wWLPTSAGpRwpI2C2rqs/jJle47I4TEeCTZkszSuqhhNhYM2Q==", "layer_level": 1}, {"id": "0ed41d92-542d-418c-88bc-e531cfe94570", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "End-to-End Testing", "description": "e2e-testing", "prompt": "Develop detailed content for end-to-end testing in the domainr application. Explain how <PERSON><PERSON> is used to test complete user workflows across the web application, including authentication, domain search, analysis requests, and result visualization. Document the global setup and teardown procedures that prepare the test environment, initialize databases, and start required services. Include examples from auth.spec.ts showing login flow testing, and describe how E2E tests validate both UI behavior and backend API responses. Explain how tests simulate real user interactions, handle asynchronous operations, and verify data consistency across services. Address challenges in testing distributed systems, such as service startup synchronization, network latency simulation, and handling flaky infrastructure dependencies.", "parent_id": "5ec7435b-599e-4858-8bb8-f0eef69cf07d", "order": 2, "progress_status": "completed", "dependent_files": "services/admin/tests/e2e/auth.spec.ts,services/admin/tests/e2e/global-setup.ts,services/admin/tests/e2e/global-teardown.ts,services/web-app/src/__tests__/integration/service-communication.test.ts,services/worker/src/__tests__/integration/pipeline-integration.test.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:39:26.3281+03:00", "gmt_modified": "2025-09-15T22:15:46.868261+03:00", "raw_data": "WikiEncrypted:xx8/OLImtQwrfE5ni0Thr5Uvs70dUQhBZAzMwaWp+tZ+W3syVmsLu5S3r5Jv2ugMGpueGWChvVSCl1yDz+B1ONL7swtcljMAgNsg+AssWRJ3WLbqrd3yxwkGJis8o5xLewxX3MIPFz3xwIy0kYOzUGKdDv3+FCZP2TrQeE4qkFnITF1NYesalT6OJnPZAg1YSE71uN7fQCUtcOUEoWwjvD63fhmgVohCkrakZe7M9ETsQSaLuUVDKigdmeqP99kiTKJavfu2TsCpSwywTMu5DTFKfYn8PF2wbiowSN55KUX7If4K3JpHgetJXOtD916r3RmKb6AJsEDZl4fupgyfbcaHbrHnpXC9dLC2tJLnGX5YMhIWrgdAdgohMLB8h4ZHUyMnVTiHCXmKo0mHAaaYa/0FAx6KNSWbRApgcujeJ7p0XQz+tWU9QAq9RH7lIU719zBGsx+Y2+5BqZ1daTRXVD5gTlb+UPDAKxsnJviVPKudcnXdZtnGXd/ReVlnW2sX8iIKywslRJlHvCfyjklFz7U8NekhntjQCRNQAr5pczIjWT9RlQLCTzli/hPoQtH3NIEbRfcEdLDMNxgkezYWoxBQ5L/alFEQEFmlF18MRemHPQW/T7xK1Rfx8FeLai33BNtU5jzrA7CLqVrOmtQgfS8qcoqIXPTxiqozn4+RMU9q7oGe+vUCb+V1TDa98T3ByAQz6dumuGG2SelTFehURxEdUIuphxKbxRCGtpVYSvFMhPsIf6MR8w7m7Jaeb1u3zTFDbFWUiVZqXJQd/ORHB6O/9gob207+lmSKHrCMCbeNHdnlJgi1Fdm+uihVbQexzoDeIzhyCKeeFroMLlJmEW8yVXalZqaD2dpTQyKgCU/Jg24d3yP6nd7UusJJJXLueLfx4pytBVvXSrpaf7C/8trjGoaVONKNkL3bIxK9iK+EZVyPQ7CsobhzBg1VKVGVhQ3Vnzvt3orpcK0cF0SL5mLXv89Z1ea4sda5AzIsW9UEJ0Hp9aeMhl5Xra6XIcxOWgUiQcTRk6NyJkzwvpYCY9dWOTIxuyiJtfqWn9WNXeGmxH2VdLImt/PN9hfws91bZhlg6+gNQ1LeZstB95zlAnwpEBQu3nS+ts2NF2tjLZMVlyuHoeh91Cu4ENSh6Dl5imMKNbKsDCJpe6oGpQMihiTNiTRm0yKWZ+a5Tnevn8Z/poH1vdMOdKB2q5/Lttq+vL/oimKnOzX9w7YIdXOefem8qUNfdo5pGKtFpwKVOhN/jxe2ejxmF1txamluYbhx4o0jY2moHl+FLUyYjzlSGaTLJBkr0notJhMLXCQGOKffkzQteVJx7wJv0cMwgBHPe4s8JS6I1+k0HZeJXOCgx3Q6ACj7FFHDmRhcxVZDygFgp2lX0mjmvry1xwc1SMXvbmm5uTOAq0mPzSYd+TZPnptOMdb9A847Kb3DScGbdW3lSR6iu/qHdkGN2oHLvshU84eTe8O1C+M0afLygMdA3ZyLHcXZ9mkESAJJbvXLmORWmJhCz02MJBROEC3GsNuvej+p0vy9Ra8lMbouRxKs8qDWoKgspwMEpaWuoFT2Juz3XePOylskbZwVn2T/czUAX9v5rGJ1lGMqCyruP8dQA9xfqSrXOvZCMqlblL7YjrpnC9rqeFDIj7Fpog3FSgU1W9HTRsb74ouENEfu0yLb0BAipYIy4VmlCyGnKo50UDvt4U9wE/ELdBvzm98RFzEGY0kpxfWSWS9q+iaF36Hl6/Rf24JERNoONmZskzNSbQPb0KsvLvWdtBOCPl8VAYkU", "layer_level": 1}, {"id": "2fd75159-4155-48d5-b146-4fd895ab5d86", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Service Architecture", "description": "service-architecture", "prompt": "Create architectural documentation for the domainr service architecture. Describe the high-level design of the four main services: Web Application (frontend and API gateway), Worker Service (background processing and analysis), Domain Seeder (domain discovery and ingestion), and Admin Interface (system management and monitoring). Document the architectural patterns used: microservices, event-driven communication, layered architecture, and configuration as code. Explain component interactions, data flows, and integration patterns between services. Include infrastructure requirements, scalability considerations, and deployment topology. Provide system context diagrams and component breakdowns. Address cross-cutting concerns like security, monitoring, and disaster recovery. Document technology stack, third-party dependencies, and version compatibility for each service.", "parent_id": "", "order": 3, "progress_status": "completed", "dependent_files": "services/web-app/README.md,services/worker/README.md,services/domain-seeder/README.md,services/admin/README.md", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:36:59.654137+03:00", "gmt_modified": "2025-09-15T21:42:00.077861+03:00", "raw_data": "WikiEncrypted:vMsgMax61wW2sTWhXd8DHzaJT8VrRDiEcKUjOTwlxPgyjNVwwqiciGszuxp7Nu1SfmzAJPnVJj50LL5i6LXuWTUIpuspicYnHVqaEzt5HyYJBItLphCiiWKADegOJpW5eI8Z6ktn1Xu0lyh1FH1xulnRWDkRthv3QnrNzsqMcJGIiLxX7803f2MPX2rGXNND1MkEhI0+GnvVZabPb0KabDJhaSMn7kPfrVap0arM0MDWIRCIpOxxZxvlU8umJ+2Ez/CxXA+iARYJWmSKGBlQ405Ag5MFoJVDGokqYOrUCOJ2oQv9+zXEXcFaSdmRI7lpX8jojt/mQ6pFjcKfJSXIr+0OX1JbgXSFr2Po281LpG0E/U9GRPrTF/1WSBkU8s2IKqg0CIpyp8qGOh6qHUJtRYKggiH+tvlO04H9i50ymlP3yG39sdfLI/ndPgU2d8oxBaJEoPna4EnIZtILAsza+LcQVV5Qb6HLIVTLOh6Zc2gMjB7S0csy+Zf/PhG7sYpfkt4Y7zMqeb7xFTxmub6HFvzJsM8H7xnyfKZUTe8erIJueZSpGCSUKZqnD9ItRwyP4tPDCIETPocwU5V0ovZUv7lg34azfap1XGRLiupYce/d0dFe9J5YQw/fPnIoay3Qzvia8eDM9uReLJuZ2v8ebdKeXwpeSaXDWbUDjP/i8IqH2I8nC2eq2wK/AdJeKr5OL+BZMRRsPhFwwWzS8weneOkytHJsxnh+bi3lnByHS3+2GGdDj2vHRWCJRADLbdYS+ID3qCF7jiNWcLsDYkDgWoi08khC3j2eoAWlxwG1STiFvPwDPIFzUP4TiXE6fon4wOa9pTN7uXi/IgMQJQs0TOhWyefIcQafgP3m1h1kqviMDFsotYr26KRhHVIUXuhICNkEV66tVxihrTVYNGsupzo9JR+b1+AOfx1s2tIPev49WRg8cL2ivjBNy70AGAMWhkYbhmeqRsU46XGwbXYFTw4MMJNxByOTCiqS91gT7wEmkRuv8BH69b6xqRP9P9JQ1XgHmSsAU2OAek/RV03ZgdY07BD10p7Q8+axs4vFLmYl/qRfwu+N5NX/iHP05gP5G0eYiMgu5DbmR8MRsKklfVHLdq/VfsO5giqzvXrZy+HZdVd5sCJ0LZo7Yp9laLznUBTvaHcO3zP38VswtV4FwDB3ByTgN62Lar1dNblJ5mXUsCo1XUDTMUB8aV0Yp/J+TGy0hXpjvqNmDXLsB6THt7A5kC9a9q1kxEDZ1Rj8PHbd/68ks/66HXFKmiqMqbSvrLavwyeQzdNA+gRCBRG1K+RxefKki3+ETmM7PEdEpYulm4nfnTfPHoE9CiF4e3boUM0XAPwEwY278I9F9od08Vjm6llNk7iCkmE5sbW1LezGaf+VL9uEeN9bxUBStS0GE9n2wom0WUkQ8LrHyBthNBXagTdj/YSbPWbeKIufeQS3qaTzKnw97V82upL36qYEGqrbcfkKbUzX1dYsqwBrNz94o9iCRueGUmLGRHfoUnGRCZWueBQaDGdqAkAeAGwnjEQ3Obk8N9myKbqzz+f9/DFkcZgyY+mcBHwbcYN+8H93eu+7/iuyb5rhY3nxId2nzfnHtxMlK9zaltLwKvVBtCjzFKoiURXP1JlgvDyxBMTp6civYW9NBxdIHp5q9QV/aKl+kdrKu4xBWz9qxoIkLcICWwWnaHfoOb8Oty6YNAbWtjvTXVuvD1ixaAZlWOzR7z1kqVOFjRhYCZ4Hilv/6zx7k15QOKMVF/f5JNVJTEh5rNH0yhl1MuVcuC2OUqcfv795dwYLO/R65BkyQyLyTPW4/zZVgOf7I8r5f+Yd7nNJwiHUXwK7DMAiaWHSV/7I", "layer_level": 0}, {"id": "81d26d99-2470-4522-9577-46fb4bf87d99", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Admin Interface", "description": "admin-interface", "prompt": "Create architectural documentation for the Admin Interface service. Describe its role as the system management and monitoring dashboard built with Next.js and Mantine UI. Document the admin features including user management, system monitoring, analytics visualization, and alert management. Explain the implementation of real-time updates via SSE and WebSockets, centralized logging, and security headers. Detail the integration with Prometheus and Grafana for monitoring, Alertmanager for notifications, and backup management system. Include information on authentication mechanisms, role-based access control, and audit logging. Address deployment options (production vs simple), testing strategies, and accessibility features. Provide examples of dashboard customization and alert rule configuration.", "parent_id": "2fd75159-4155-48d5-b146-4fd895ab5d86", "order": 3, "progress_status": "completed", "dependent_files": "services/admin/README.md,services/admin/Dockerfile,services/admin/package.json,services/admin/src/app,services/admin/monitoring/grafana-dashboard.json,services/admin/DEPLOYMENT.md,services/admin/TESTING.md", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:37:21.309503+03:00", "gmt_modified": "2025-09-15T22:16:01.757455+03:00", "raw_data": "WikiEncrypted:WWG9qBb74s3FuYyeVUFZFNVBgoDDfQnTVf6JIXSs+2ZVT/jBd0Tohjq15y1NP/7b7yfay4ZSf1dP5XPBIgZ2qLCUHvtQOHG91H2eE++NMOP41GXl7CnR88fm1KPAZawFX/sjaDQFHTUxPkFZgD2HHl+lxS7xx2IzgwQoaC8xtZuqxSVb4lqS4IV3EINRzKFzKTWcigwnklFMnMnNiEgf2O1cC0rmxri5wG1kRjYY/+RLBfPHyYuj/hlTKhVNNWyb9gOGKzjcC6dF38DH8nZAyynYpzFB9LGsVLDWa3Vj6/2l0CYmjabt/yh/ymX+Z109Gd3NFvFPIzoUSafd+dUI4M+P0smOjuufmOmIy8QZwvp8GaJQdz8/PjWTVohm7oqZ9YBMSwA5WTJDFctB68r5GprWWtlsGgBb7TfhBhbixW5HoNnmjpM2mJfOZDh416xR8Ws8HshBrHcHNW/U5o0KhS50O0mG7kk16A89Np2mu628HgP3KI41sgIo2sIgEoM0KhPZNPlJFtl9vhe9d/mTTGyTJwCEVaEqOnhTjSoEU073Ia0I80QeriQed9x67t6Wo8JuOdJePA3hpopl8yegkCGn0fxWNwYwlxykj2TTOYz5StHpNyZ3IoSS5HIoSZWuC4O16xBSxN68t4sVOx+Vm+oGDyrTbus/EaoUYirgEQiC+wjbpzSqD+tv9zzXZMiZfW7tx2RwDePnHTKKTLl4Yw6WCRX4yKmHGIQIwb/TrrEQ6OA2cf1zck5uwnp4ap5pmuC5qgt34yGTTxDCnMQZBQd8hfmsqiia65ZH3Jq2qYs/nZtmVSU3JpYYDkolzBXksN9803oWDtsRbclBHwLOJzOtsg5ds0oI3w4PFTzjz1v8O6+bfxplnc4L8kcXQWCd+7f286iEqI+FiFx4Ob/sDQGkuoBhI27OPrXbia+A+Ny7I/P5UdnJ012MRqBiGfsuP5NO8J+ZnJUzYyidscPpW0VzxRFuQPaqPWfg3XnVgH3TnaJ9iBdQaDzUYagkU0iX1O4dkN1GD5eDWQ9/DdtJSJiKoIXVZ7rSiVo9CiAD1tY1A6cJR/5uRYYaRG4eB8oyy2bkbuwXTyGBTxjmJjrKGZo0gFaN6jEasDoN+Uf5hh2dW9QmEcTLDp6jCvNXuhTURBDG9Qo+nhKNdeexJZFnBjKnAGQm7njwUYR0YKUqBy1NbDMJB4G25CSkCGP5hsmD1Jkx4bdUC/g6EjCirqHUrrp7RAbGvLjG9YObM3RiK7IZzuJhVJiVWjcSAcGroiRxAryrhcOPVBuPTu6C2LDs3BiVT53vvAbZ5v1UamFAmVwdMVrXVXhxskOnIqgxxWh2d4VscU5hBH20uizBbpOOABg3KEmpJlju0B+iMl+lRHbgTNmEG2HAj4da6WEl0rMcmWggO0h6MIPV58LKIdMRmO2aHeXuaTjqgRjiA+KaNcjarUikMmR6rOzBaZ8TmIwuutfsR4jeBR9/O0QKBlcEx+Ce4cG6Lg4sxHHOZSGFH7PZj72QBCK/daW4/G4n2ZPvwYbRvlRy08UeRv+OPxaWDOuiXN8aTG7tPSTcKpqCfRJ8hb6KmJzaTAnr0QB1notq5nOXjHGKR74yP/GRk9fGSa+OwNqqd7qFO2TbiWsxEopbA8T7ZQhOnPbKXrFv/8HIAu5pFnDYnRO+ciDzzds0fg==", "layer_level": 1}, {"id": "a2dacecd-ea2a-4671-ac7c-de5baf803dbc", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "DomainRanking Model", "description": "domain-ranking-model", "prompt": "Create comprehensive data model documentation for the DomainRanking entity. Detail all scoring components including performance, security, SEO, technical, and backlink metrics with their respective weights. Document the schema structure in ScyllaDB optimized for fast ranking lookups and time-series analysis. Explain how rankings are calculated by the RankingCalculator in the Worker service and updated incrementally. Describe the relationship between DomainRanking and historical data tracking, including versioning and change detection. Include information about category-specific rankings, country filters, and real-time update mechanisms. Provide examples of ranking queries from the Web Application and performance optimization techniques for handling large-scale ranking data.", "parent_id": "54aacf35-eba1-4936-90c8-2768157d3278", "order": 3, "progress_status": "completed", "dependent_files": "shared/src/models/DomainRanking.ts,shared/src/models/types/DomainTypes.ts,services/worker/src/ranking/RankingCalculator.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:37:25.740924+03:00", "gmt_modified": "2025-09-15T22:17:15.93667+03:00", "raw_data": "WikiEncrypted:JLXqlo28VAn5UOPLsLiQgVrPqgDM4O6KY3QZOw8LyVTtKt0yzThWEUGAQe58SEznv2OQwSS0DrxyWJ3ErZuA8HQ1Hb5k5zZPdis5xNUtUG1jzubZbUkTwXbRuCGsFDkgGkK9nOyaIV68KAHmYHA0B+faRmFsRpgTZP6H4s1CVbSKy9mAjsm6LRsdSK7MegyboBpT/V+IJ+SjDONXBO/1zQ0u2xR9N5IeNrOu766q4DK8HDVBMRQqrQuL2NXclAWu9lAxaQwco9rI+p46HFXzcGhs8kJWhK/g2Xh3MAxvw1mXsrclK7mEKsv5zFZHxGpn4sHAAuOSyJ903omjp8dUNrJcFI1j8XwnY6LjYUhQIyPCdqNdj+RmAafmcB0Ks31fPf4AMrUDzqmlsYFoGj2NN59JKa/+wWWkSXwLg5NluCNjFaJqgu1azDjo86JZVpKcMtYfvIzxvHZ52Rz4jQUQJVWlLMQAmx9dpvDsOWLyoh9cY9StTPvpgQIGmZ7yQsz/6fTebk4DgR9HUKpCa/y0rxO8/fKH2yu6ShaHf3S2a0yoc426TVUJKZJLhn+narz8m7zxsDDCIOjiExFPQTfK1STz2oGgC2Xna7CZNqZGQ8yZV8WR8idfJTIrcmJi2WoVv5F3lDk099dyBrmZ5qiiDc8tMTQVd80CTtasz85nsQl/PowsI1O4egcMtUZlOT76Evvcgot6PjwqTWghQ7mezKbnW6OMm7EOejM0vQi7MR+lVsF3xYbB65x5ZMD0lewqllrhQ0u2DIzkKMroAPVeHCcenQdlPBq9Gitn4c56soH1Vbpl/S22LOuojAYJffvZh11UXT+JhNydcJ3ecd0/YHrmJW++VD5hUy1bZg1I9yMQQeBiVHswTIWNb6ygOqm0NPufQSQWwwUVYUquMUx+2rpC7lC4lwLsGOFdeXgPo8F6YA0mAf+GyCuc9FYCPFdNQkJJzQPDpyVBr3MTmvU/x+LkZ9UKLJYZl+NQ/tcvheKZqKi07syyQEW12b+aQ96lpS6rbBmAzPvVrN+XHLK7iDg++jI4uxjAg6+EDRpSSwwrLVAQeAVDySJdHKm7jU8HwKa6gphr4gsp2yJVhft2wwvr8ud/FrBCFSMVuS2LhxVURz6vF3ynDmr6ewSFZj1dOdyvp3OANXbIR1QjWHQvpa/tU+ZpGKrbwIWmR/Hkh+jXlUyrYkAiJEssYJDyxrncwbtwHFHgbkM48A4Ejc1548/QGnI2RIsLDgaL5sg3DXtmC0ACBFbnPX4BKDvu35FBJpp7dl7X9NZI9+9O3x2W9MoAaYCRNiFCjdsM02FrhoByAkJzpmIX/2bvIaorXJ9WUAeSYlrBJACncwCYoRmok9Scyi/UwJ1EdYmO7JVBZ300fxfTEOyvVgOPYDwuHDnx0MEzsutYHTWQNArxsPcpR4Zp7CgYRXnLm0Se51Q23tSCwwhetxYvCDFmNtsZBzOyWqcXqJ59GGnYfh4uL+oGLJrTN1gbshLCadP0PTRSxnPI+/6x96BkO5So/AYEAr1C", "layer_level": 1}, {"id": "61966302-93b2-4fc8-b0fc-0011ce342be0", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Admin API", "description": "admin-api", "prompt": "Create API documentation for the Admin API endpoints. Document the protected routes requiring authentication and authorization. Detail the endpoints for system monitoring, user management, and analytics data retrieval. Explain the authentication mechanism using session-based auth with secure cookies and role-based access control. Document request/response schemas for admin-specific operations including user CRUD operations, system health checks, and analytics queries. Include examples of authentication headers, successful responses, and error cases (401, 403). Provide client implementation examples for admin dashboard integration. Address security considerations, audit logging, and rate limiting for admin endpoints. Cover common issues like session expiration and permission errors with troubleshooting guidance.", "parent_id": "4cc1d72f-ebfb-4439-994d-803e620048d9", "order": 3, "progress_status": "completed", "dependent_files": "services/web-app/src/app/routes/api/admin.ts,services/admin/src/app/api/route.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:37:40.427802+03:00", "gmt_modified": "2025-09-15T22:17:17.068652+03:00", "raw_data": "WikiEncrypted:WWG9qBb74s3FuYyeVUFZFPnQCmwwfFO5MgWRJLXlupMzh97LtVQERT4fYr0HWL2vkGKh9XddhQdX7Cvdrkvw9/mQvQyLT/Tomlqaf4aOZvji5rKqhNwTOtYpEAvRCL78nmYFDQuKSnzShA/Yb5wJ8qesQwvUC572I7+d4NjWh2wLuaNl/OFtjz7EZMdWxw0Szs3SEyH17Yl5M3QmM+rQKF1Q6F37MyF5PitMh250gZI18DuvOCTz7+YH1xVcT1MmVvlCJBmWzGG7TrkwAOpxY3o0jq3Va7lBg8YBUVGLJMFDFC5wPSayhohce28wnSNBcMZD8vdkBPlNw3u3RVHY939vyUIgGY+rHfzQbBShwF+65ScXCmr7Jb0GmngPnhc8YjRc6MA6QXq+OF5sfZxmkApUfSK9f5CuLByLPGRGPBnAK7XvqXRunsz8FXJEzSALxvVC5GZTAtL03RgAkYtOJHxDz2l4heSVO9xIOtb6dPvkoBYP9YnJXScIEDL45e9evlx7vAuMcaGlhRwgKSyOKFPtCOG+0pvWPYvqK7Sz3mHOIjD1HYi5cvEiucHpSMo+Y93bfOBnLB90cZQppeyP0yBDbdBaxUJCIWHpatIV1KlHbLA6DhVDm34rFzGcu40aRkRTkTRr6XVO1ddwjpWKfneJww9tTX3TS/8S7UAXDZmomwQWGiJOVTXzRgaBTSLhBrGgq2m11NU1hJNN72KXFggfAgKbqveDNBS7WXNIVkef9fiO5tpqvi7JJhLQ4ySOcpQHd2Y3+l8Uu3aGfBtJGQwbJZvYFnlYt2ZLh40PthatuDpfV4vph6lAWe//lZB4NWAV5zqLxU7j+0xQ+T+Cawwa80h25xVXYOPTEWo7XvRBZCUcg6suLpU5LRQVhtQ3qG/NcaIl7ZpkFQhNAnVSbEfiT5uQU72IiLv888wkALOW1dgDz8Bpuwmj+eW6QUPbM3lJSGe1oloxzMB7EhGIbMdmmFRQnwkLfaLlCctpnuy9PwGfwzDR52xt5XOtPFC7PxzYBOv19CQrXAR4BExgQo1pku+DzKg1RQ73kd4ylE9Ih+mO1gCBRXOysH72vDMNC38/w/u5SVCR/gLui1hzkXrCxZtEmFvctaOAyM259U7z7M2GmWZVTikYp9o+17vxBbJ9NPsOFjMX44e8wNuUcK7NJ5MWb/457MX3bEe0zQzozEyB5aORMFJICD/1+XU/HJquLzBW9QsRXEChzw0VwbTmeIkltPy1l0DND5fvYDrhwhB1R1aNGKFXcV3ZgDcfwJzB5j71jKMypsNqCE+0J0mzOSl4ueyXyr/lBew3p3m9xGO1+lUYlVo859vbNCuX42NQJXNokGH3fleGJeY5TnkhbwU3RsQ5S4bL6g4s9Ef6FCLhf26LFOHeaLfqenTgR9pbrUAFC5DUZD3WTQydGJ31F1AXM3PfHkbJBFgrVOVSfWJuHbJL4wBK5nigIbJOEiUJHeW1YXlAFKOGFffMNtZZyFvNjlp8v7sC0193vl8=", "layer_level": 1}, {"id": "978c1cf5-dc9d-430b-9695-eb8a64a12160", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Domain Comparison", "description": "domain-comparison", "prompt": "Develop detailed content for the Domain Comparison feature. Explain how the DomainComparisonPage enables side-by-side comparison of multiple domains using data from DomainAnalysisService. Document the user interface components and interaction patterns for selecting domains and viewing comparative metrics. Describe how analysis data is synchronized between compared domains and displayed in visual formats. Include examples of comparison scenarios and interpretation of results. Explain the technical implementation of data fetching and state management for multiple domain analyses. Address performance considerations when comparing domains with large datasets. Provide guidance on using comparison results for competitive analysis and decision making.", "parent_id": "e3ce4565-5d2d-4e25-9f46-14b64749ee98", "order": 3, "progress_status": "completed", "dependent_files": "services/web-app/src/components/DomainComparisonPage.tsx,services/web-app/src/components/DomainAnalysisPage.tsx,services/web-app/src/services/DomainAnalysisService.ts,shared/src/models/DomainAnalysis.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:38:04.740025+03:00", "gmt_modified": "2025-09-15T22:18:57.374428+03:00", "raw_data": "WikiEncrypted:JLXqlo28VAn5UOPLsLiQgV6K0krIfJeX7GcABSMr6cuZanrbbi0BiPszoi1AeCTkjcD5fIQiCsf2Huig6zaU+DqtB/O2kjY51VTFQuKrUNahdLgmgjZzYfQ521rGOKUaHso1RCbQLCcklC0M5htUsneFvMEtnZ2wTk6MOiBQl8YLIXv/CMQMKUsaVRXP64cVQ4F9ad+NXyEvBvL2bh4SKDQ9o3kgAY4FBsMx8eOjVF0tL//Aps0fwcYJ/9GEjFr9Whl+bFc7FJn9vH2Az28WzpKb9h6FdjHI8TOe7nvye2RilhVLR6jPZhWzdTBImiSp5/pvrdsDg5nJ5101FObeQNmc/k0acBHifEnL2R/CxP/qFPUWlktpeaXB1uRVpy2bA541wA7/YyknPwfxyUt4OtbYGyiqIx5b0bcfkxa0hUUKDB0iopgBBjFvdgVlJnVQEtR7+ztHro//lempyGj+eenikUYHeouJTDjNHUbRQy8eTPmlpTA1yK2kVIDiZZeJ3yYYa0F6x64INbvHDWADEdmJopQQIsQR4zyc1n+cpKTgqUJb03Fk5J6pln06b6kwK2AD2c0Vch+iiwvDUQNG6jvafzavlj5/iFp7/QLHmB/zNe8obZjxUgAvIfHcRrbHU5q2SKVcbUl31tMbLsBGNX6m8lunES2nrzfBBd9DsJsD+royxALQw1Dt1nCWH/5160q0KsDI5W2bfvfwkC4NuQw4YPpw04wqWs+0tLne3cRS+kmvd1vpOIrvHsOuvd+vt0LJhzr3KlOI8pX18ZBn8SMB6iNi3folC4HON3J75mf1qCrrm0yiFZH1SVoztzro2wEPowSmqIuesoLk1MJTrpFcsdp+DiNSpiHWmcI4nlFcYDae6aDMzMHEe7l42uxs8t+fProcqfZnX9KSiRnKrEDT4lEvPVxIt2uodFstTkYOAgR+t49OKimu9aQnLQXzh5sZDF6v9iK+mvhjy53eDxoO2tk8ka+iim3hgGVqPQSheVZvNzuXSu2p6poddtMdscVDGN27K6niO1NGlKtOKqmxBR4jZdi3ifMOEyFlu1Y7OMoIUXdetKdisbCtSiMQBkZhV1QgRqGDPq3RgmKy30nWw55ftgUAI3nuBchkjGNKb0LowdxBRLvJ7LgD936pULmPlc6JoQhJXK5L5zazlTn3NgXiWQJDBX+M3Rk5T52SrCaMQp8iIj12uvA/ZAZJ/tZOE8VWs2qC2BkUkBhcQk//EN1eQzq5Irr3U0KBojwtkeEFvHjt2ESoP2aWMlbmNF8JTJOQ4yV126b6KsxXY6FrpbAxFLktjJZL4qcTeTR5fUNpv8vHkhDoTXyfJklyuUzOaTrbvHQLfQF3sy5uSdlBXLs1tMRaAyY1vwPhFbqkqgJQ8joKEdMz4C/Q4PpulV7TWCdm+ZPlUFlcikHTiKS+K9QeAnl3lMjKyzYELZFYbZGnSUaQN7RjGOJNOAsOTJJuZ7EPzAGgGY2fEUkR3fgkqWw3Nk66qQQlJqABLTrwsSOVu8+EAN2sM3PM7pSTRvE9rZO2jPhqseLwPAFqp9SJVStAqJW3a8rsJJ5jZSiDsJiogXG5cSBPPXOsuY8K", "layer_level": 1}, {"id": "0f222083-d810-445c-af67-e8d4ed27496d", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "SEO Metrics", "description": "seo-metrics", "prompt": "Develop detailed content for the SEOScorer component. Explain how on-page SEO factors (meta tags, heading structure, content quality, mobile-friendliness) are analyzed by HomepageAnalyzer and converted into an SEO score. Document the scoring algorithm, including keyword optimization, semantic markup, and technical SEO factors. Include examples from test cases showing score variations based on different SEO implementations. Explain the integration between crawler modules and scoring service. Address common SEO issues detected by the system and their impact on rankings. Provide best practices for improving SEO scores.", "parent_id": "86fb1ab3-0d9d-4f57-bdd1-151e7cd91e0e", "order": 3, "progress_status": "completed", "dependent_files": "services/worker/src/ranking/scorers/SEOScorer.ts,services/worker/src/crawler/analyzers/HomepageAnalyzer.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:38:05.723581+03:00", "gmt_modified": "2025-09-15T22:18:41.54986+03:00", "raw_data": "WikiEncrypted:g1LvnXckWC2Gt8pyDxY2fr+VwOCa+Vj/QL7H6Xya/vUE2VvYV0ifkxLlDVhDXmSQkeI8M2PJpdJsyCDaB9uarS36ElzHD2yLCEOBYkXYn/BJrZJmwryiYjJpuM9CUhu0flbk+6CPCaZ3Fsz7lF/fojKpiFSDhqeKBKsrBlElx1JwmfTrjtBMjBjTvar2zIXZBvBHPsr4ZgQm9J89LYzLGTZlF/HjqbwbrvZ+ZfI31+9+bvkLk015NKaqH1dM17v1yaOToe4GKGrj5Ha0U+hLhNyO5qQkYNr1y3jzriqdOLxbSiVt0IRuiFWLvzL70oyF7Byo4c1AuNGJ4q3y6pMZNfVk9dSQdtoFBL+AamphY2+t0ih4DRg4sRvkwRiyxDlY0kNgIwkXg5GXR/pEtNAUNnMa5ljbhOsKQzbxUt2f//q3dTT4KAyqlTITOTbmUrWfuEsgxJYl2nrF1USmPF2N7s8L853aaKJrWLGXPwtAvjkNh0qg0USD5Wv+mHvZ/3qutdkbBZsbgVYGEAxNeCa0c54gNbH6MHqBFxGhVDSIcffqiYog4sc2Pjz3PNeijhbb1ZVk9+mKdOAl7BeKpKde9hWcnA+2uf/bA32cEwjNULClBHMVyx7QBR2CIr79JL3VVPsx4b6DuUDUXCGi4+NtYv5wd6i8cbu8AIZonHNZXIwW1oQo97CHhYJLyGhGa9mHgQ1ILIzXuEuR1goG5XjHR5i1CJL06MPkPb3hksq68cykSVGXF39EW2yOQP+UchZ+qR9+k4qVznczwG2oSeqPhXEX+13opfJb6FrHMzznmgmY28y08H7/wXSjQALUwco1UHo6/OX0dQHilLjT/0lt8nqs1R8FEyLmOZ/hPAplgUf2bWHJPUvp6B4DA1H3tUCxgjPtXvrdendkmHqq+I2sG3lCvpf+0Do4Mtlc8FxqNMLubsQhNSb6qidkm+MAD549Gm7k7ukpxfEEmYSRvZJGrUh7EUtIDCfMxY60O77Io1hFpSLiDRpQWSPD29t5RiV/6wSsgwzd+DJxtEAxw+VvAhirtkQGssKNkVg+0oXrrwh59pErq7I2gkPq1NXrkwk69XLhYyU5U8EBQAefVGG+nUh54p+8U714zu69HT0HfZGUbkeHgNx+McVRUy2Wcbv7VXqyb3Al2ajPrTvY5ECxvEccVV3WSYYHRQFULZh29bt5iXjUGtPRGbmqwINbSFooeOvCdEW09DveIOFwS45jsDCJHcyolOARbD/exHXb5CHuy1im3ra+APz8D9a2gj07", "layer_level": 1}, {"id": "17e04bb1-d9cf-4f21-a4f9-3e2c5ee31a58", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Indexing Process", "description": "indexing-process", "prompt": "Create architectural documentation for the domainr indexing process. Describe the high-level design of the SearchIndexingService, ManticoreIndexManager, and related components responsible for populating search indexes with processed domain data. Document the component interactions between indexing service, database clients, and cache layers. Explain technical decisions behind using Manticore Search for full-text indexing, including trade-offs between real-time vs. batch indexing approaches. Include infrastructure requirements for index storage, scalability considerations for handling millions of domains, and performance optimization strategies like incremental indexing and bulk operations. Provide system context diagrams showing data flow from validated/normalized data to search index updates. Address cross-cutting concerns like data consistency between primary storage and search indexes, error handling during index updates, and recovery procedures for index corruption. Document technology stack, third-party dependencies, and version compatibility for the indexing components.", "parent_id": "71a79def-fdc0-4d40-80ea-b74ed07e1196", "order": 3, "progress_status": "completed", "dependent_files": "services/worker/src/indexing/SearchIndexingService.ts,services/worker/src/indexing/ManticoreIndexManager.ts,services/worker/src/indexing/DataSynchronizationService.ts,services/worker/src/indexing/CacheInvalidationManager.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:38:27.306969+03:00", "gmt_modified": "2025-09-15T22:21:02.05313+03:00", "raw_data": "WikiEncrypted: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", "layer_level": 1}, {"id": "7556675a-016d-4207-a449-c63e9ac57d7f", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Environment Variables", "description": "environment-variables", "prompt": "Develop detailed content for domainr environment variables management. Explain the purpose and usage of each configuration parameter listed in .env.example files across all services. Document the hierarchical configuration system where root-level environment variables interact with service-specific settings. Provide comprehensive coverage of critical variables such as database connection strings, API keys, AI provider credentials, and monitoring endpoints. Include examples of environment variable injection in both Docker and Kubernetes deployments. Address security considerations for handling sensitive data, variable validation mechanisms, and fallback strategies. Cover advanced topics like configuration profiles for different deployment stages and dynamic configuration reloading. Provide troubleshooting guidance for common environment-related issues such as misconfigured database URLs or expired API tokens.", "parent_id": "d162c57f-e749-45c4-b3a6-43a7d4a45c2b", "order": 3, "progress_status": "completed", "dependent_files": ".env.example,services/admin/.env.example,services/domain-seeder/.env.example,services/worker/.env.example,services/web-app/.env.example,scripts/deploy.sh", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:39:00.914625+03:00", "gmt_modified": "2025-09-15T22:21:14.379028+03:00", "raw_data": "WikiEncrypted:mJFb0eo+82adP8NAeq7d4FgcCqZafL0ho3sap74cjuawm6BAm+y/jz4KDexmVDXrPqAH/aJ7iLDHiMrQF8uIUsUgR+NHldZCqWVLiz4zEtxHxVI4vDFyEI17oMwGu8DB4ozz+vbVoj7ixUTzGAvjHcGEkVxZ4rFXEb0IEg+nTMQKCiA5JiIkoZJJlOUVf+J5Bdj/3QcA8gH45xIUYvl4ww7RHhgtAhcumk4m4KVZF2nwOtbQuwzPPu8JlOpnbxBFA9Yj4X24VTIUZnffIpwTyJyH8zhIA5Dws4GR7pIz/casc6Mfh/F6DMc8opHiNnsMhyb7f8naxUst8TZ20/gkr64waEljco/hHSwcLdHCqLItjatt2asN0J7IAulok/W2/E8xT2xZ3hcpc6jLubCF0KkwL5l6mmfEciW2yLEkgaCPkrmWd9cPlTYA3aGOjtDW+wsutPYJhJ9gDFhXKNRdQyjjTzY2mh4E2Of/hzo3BPBPJ8XQpJLw+8LjU5mFmcSk66UNh1B8DLh3eMCmebcka8Nezqb2HfG97PpMwdl5pYaKei57UB3VHFYSOIAwdhlvLekR/0lfhvSovSALIdgwErHt9M22ZVd+1IoqbODzTAZnJ8WG1wOgmDRSBvMeHbRgy8TW3Kd669wq6JN13SZp0AbXCWqsfY9bPXN8GfIoGLBmvHWMGufmoUFJzehDcJNF0yEcFIZvnWWhTB4fN9Njmf51/wWpM4DqHz0n/pLoHlwEU0WeMdItRK4JHymkaqjcdBOKfj4gufYLzyRjuIeQc7I6+cl4k2jSUHuXo5m5rEwArJBZ/VdI+g0r9VBlQ+qlB9Ao37aP+RTUhJylct68K4isTrcciZbVgOaCYGwhb2CZdGiKmjPxS38t23QZWhU4kdujSWEKvS/oG11gxlhcKLsljvavwB7Ue3wVPU0+s7X3bENi54b3tysKSazyTiAH+DjtPrhCC7IVxgDj9S070tU59LGGx6IIs5Mlimu3pabGrjE30Ll3edIDsO3JNlQ7XFrbfytV1GV1OTfuJBRyazFDKJcPoFIylKcRnG5ZqYUtHGRhQfL+QPSfWcfkM4kkHtJhFR0rB+PAmMQpDCWdCcuVyyC8wk0do+VP6pHeQuHZV0+CiXV019xpMvEKqWvPJP9gnUJDxOXV5sKpJSgZ44AFrfyCGGcNW/8YsjNA4Gh2cTHfGgoJ9PArv9gXvtYfmM+kOHvCnjMoE2JOgIy5sjDw5ZRJXor+X3YNyUu8R50ANdp7A6Y+8hd+6ipPCbrFXzg3YLHrmAsERkS7RQHaUEl9fM64LvmX8iG6f7V0ny/0QEjnv4nu8SU3/Y+QKN6rJfK3k2CkceoydBTamPh6p9Wgdd9qKWOQfQnTTZX0tuJJcwjrlpCvGFX+Mr/0hPnhnued1UTvcl5MA+Y9uTca7ljlGYxKzWGkjmXE52cbBbS+hm2HZIg77liZshqRpaFZDhY2eJyZsjIqFCAdUZOOt6C9SBlglOXXDW0Elmg0xqLo8mYwJPAlKJcWON3PbdBYbNDnhxHES5JRk+vyNDndV0+E7MOpD0JTOrRbe6Gm/8AhpkjQBR/+a/sNsTY3zIXvzxL52qCp8H/rBTgS1LzWhRTJaVFbkLUnkf20SX+EMoH9cfx8ai4vUNOadAiFIJrqeKfM9EacSvbM7NeT26c3K7EgqXFT1/qbqrnhBzCbVBVHbg1EZ65uCvg6iL5eduAjoPQY9GO70KimJme52RCBQaD7I+5vRtbIeo4I+kJqhlQ=", "layer_level": 1}, {"id": "d3f62451-df7a-4a95-a787-05a984a1991a", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Performance Testing", "description": "performance-testing", "prompt": "Develop detailed content for performance testing in the domainr platform. Explain how load testing is conducted using scripts like auth-load.js to simulate high-concurrency scenarios and measure system throughput and response times. Document the use of Vitest performance tests to evaluate critical code paths such as domain processing pipelines and ranking calculations. Include examples from concurrency.test.ts showing stress testing of worker services under heavy load. Describe how performance validation scripts compare execution times across versions and detect regressions. Address testing strategies for database query performance, API rate limiting effectiveness, and caching efficiency. Provide guidance on interpreting performance metrics, identifying bottlenecks, and optimizing resource utilization.", "parent_id": "5ec7435b-599e-4858-8bb8-f0eef69cf07d", "order": 3, "progress_status": "completed", "dependent_files": "services/admin/tests/load/auth-load.js,services/worker/src/__tests__/performance/concurrency.test.ts,services/worker/scripts/performance-comparison.sh,services/worker/scripts/performance-validation.sh", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:39:26.328951+03:00", "gmt_modified": "2025-09-15T22:22:12.690614+03:00", "raw_data": "WikiEncrypted:9uOBpMbLX4DyZqW4us3Wmw1eieMDbEcTEvMV5ETXHG2LnxRH2/kgM7gFiUJtKFS2l686PcP47iZkKci9K/QkA6vygz/+oZGIZlNuKKTk7UglwtQAuumd5nq5SsVjZBXHl8BXZi41HUEcmaoD3XqAsR7+HJfH5tqljiiN5Je7nPq1fs+72ZbZ6E7JlofQO1XptsQc7vMh5APjiXsr4Cf2HZDJpH2XL4OQVYiM6g+q5aDC+QHL99ywpHI5R/GpsecGE+XlgNaH8ueW6d8ad+xv4Zt5FNL38tVNcjIcFNYy2ZhP2sR77KSnZyY401mdogSMsR7WMOccw88T0Mwc2R4mMSWPq24wF7YLTL2phoUiGF7nC1a4QyJ5wnxSvAbn4F9MTOYPfkC0T6pNzUA1f3/wM701WNQTvs9yQXmChwcyvHOVVE3+HU8nTxEctMRAfStEUWMoSbvt+FuF78Sdu0XadohbsGVJlu4XhEGiqNbm1U+1/5ojrI9G0vOX3a+YtMJC4gtQLNsNgxAak3/5RDge8R5398Cr2J0aIqHjinoMJ3B1KXX7eGDv/qO29C+aEuhWxiuSAkBVyTTs4CR3lD9r/STM32FH+SlMv+w/5W6ZpFaOE39V9zIHgTbbSBpJNKPG0MF7QU0ieEgxTmHQhFGA3ihdqlvooZdrYD4oRqHxYkyqLI3F2WGCY4UpyYLmAsAFNhhIT5rjmyhO6+WZqMQloOYmpP8vIhuZ6CNJEERuUWX8kB4DTKrYsWaqfomAsHF0imxLF9bT3nAbk1g4VCeqroKSQBsQXiiyiFtekWfS5NNEhzCwTfN2XT2zvsdvkWZ1S3HoEc/mkt7yalTC4RTpk8a6aVNNoNug0+53AMXfFV2QFYb8GK1GohA9+FzdJKlhJVFCB8h7xgunQuI7BZtJbPC65x89ioxDzcmsMRWKU7t4yC7nEJzmwoD9FuqL7PsIBUaT69Zr1PDN2aPMcBTKRqnrjhABnnUdWPUGES7fHrLbdYWpVg8W7sDNh5IGQR/OL204w+g5Z5oloyEP0wTKiDWg1ydC7TUMmzc6cEwme8LX69y4pPGpT+6SqsyfUYcJw2UWxCsI2m+Rcd52gezWhzxDN1QCPOtbORTEaDQM3xZO07upG2AVpUbvHpKFmHkDiRlDbSVVh3v3F63+o1erGAV//6RGeZe8oIiQaHs3PjOZ3wbrJ5pi3An+zYWyZsJ23RycHaDnbUgH9EEpLMsYKd6ZI6JCEmM7zeIT+5EkoMuVnOD2CxEioIS0vShjDuzegRs65UL5JkrBBV+4DYW2qqDpRiZlLSET5LJkfBXLvdFpHMcA2N79P8N0rSQySBtcEWp2sTjsMbxi9+Q9GNHY22SuVpK2DWRIspEIipU8X6oT5+SChU2tmfi7NDRPMVsuxjpO63ERwrA0NwpJbFHqT+5NLOHkXAZ4O3lNizAhNmQVqCMbNgesURmditIt/bLbbkTEl5+lOJ9KECNHP+cxyXtGLqUlXCrdTgWattllyKZODOQ4pA4mvsgzwMOU02Sebm3Sxt6bU99xUeb5cB3iutPozuXAqPkl7fDVXNBiVfKNfO0zRtbY3T3IgpRjr1+EFtN54q3G4HPsHsylwcQA1nJO5xJ62eiJl+MmwhEh00PeUWAmTdMtNS2R5Turlm8jGP5mRpZQhlSKgdZbs8JIOQ==", "layer_level": 1}, {"id": "54aacf35-eba1-4936-90c8-2768157d3278", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Data Models & Database Schema", "description": "data-models", "prompt": "Create comprehensive data model documentation for the domainr data models. Detail the entity relationships, field definitions, and data types for key models: DomainAnalysis, DomainCrawlJob, DomainDescription, DomainRanking, and SearchResult. Document primary/foreign keys, indexes, and constraints in the MariaDB and Manticore Search schemas. Explain data validation rules and business rules. Include database schema diagrams and sample data. Document data access patterns, caching strategies, and performance considerations. Specify data lifecycle, retention policies, and archival rules. Include data migration paths and version management. Address data security, privacy requirements, and access control. Provide practical examples of how these models are used in the application.", "parent_id": "", "order": 4, "progress_status": "completed", "dependent_files": "shared/src/models/types/DomainTypes.ts,shared/src/models/DomainAnalysis.ts,shared/src/models/DomainCrawlJob.ts,shared/src/models/DomainDescription.ts,shared/src/models/DomainRanking.ts,shared/src/models/SearchResult.ts,database/mariadb/init.sql,database/manticore/init.sql,shared/src/database/MariaClient.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:36:59.655018+03:00", "gmt_modified": "2025-09-15T21:43:10.777166+03:00", "raw_data": "WikiEncrypted:klcgW2PbPxJambbKMzvFtx4yZMulRdNd9HqN3nq5P5dm9RJUgmpT9mDxZh+SOD9dVvJwdifgQ2wsZV4B+lXj6oZwoDLheDs/lI3NB7N66HU0nBqYfmNMyycBjdzzTcQ7A2lBuxME4Ckrfsn1xZlZpiFyL1IEAyolZ1araMQalop+3143aoNmrdr0VAVchNBZGCxGekdYDBVtSgq9pEWndzTgo4xfhq3Meo2dqLstax4I7K9TFCZ3VRk1C+iZ3AVgqJJeoXYG/ztL3zj0VkQ6MA7sjorMOfdzNtoD7voucdDa7+at+jdpa73bmjphLHPeNg8dPEC7GO33Oi56LB/2Rpr0AVEdT5HC81JouklMb9Ke0VAS72TdS9ZOLQ6BX0D1llN/pSvxjr9M7wre4rPkEz9/Cg+yGCKK7Fi+EM1FQDpDamh2YoiyKjNU3NdQwbrDHYVRANYUq2FBKMTEf4OCVciaE7mHshjaih6NUNEDfrWDLRqqWMX7d4pSCx4815QMFuIZ4JLwjp9t8foniD0T5y4f+GXZMJtefPlU3zxsrxzl+TtOPHIDztVMO4zrjGntLhu5ToiPNnLZ/AoXqMMPtuqYvIKt4r/FfkDpfUk7wfcvfZkbOBJFnOnl/8jxrlxs+7CPoCvTYPWlKtlyICghjMhvX7a5lxUPis+PLzt0KMv4r2A3bSEwhIPpuNdr2HGFm6h1xRgeruMvnLw3KMdMH8NnfYnztDGGYWdFWtzPOy2ysqPy6q1yW4PdCwiuOqEQ/DSswW6/RXzG0rploh2BuEg1qULV7PYDRvNMwzImnhxEaqDMZ1T8ciMioBC/IGbgW8UWBBqUC7PrE665KcZ3o/IsHUdU6UZsNUMIN3/d/Q1zSLzImC2xGLo45IUHKBT57xJjJDH71koDdItmYkFUHt5363SlKBdB3Pp7uVv8L3H77COC1iZmOmNrkJboYt2VNaW3IWHs+WHsdGXli2xqCi2YYai5B2cRtK2j8hHohLB4Fx8/p/wONsU1VZ5JrMTyqzWIAxFrh6Hrr5uZw9it9a7AxOfAQqn0Vq0z5OhnF1tWhP57RQorZIOFuNcPF19WWmy7Ck7BfmzyCvLVZgnW1veB8FfTaie7pfF+5JNsTWrW0lapOAvBHvlr2t1IJCjQFJ7HCPDq0JQrre36CBmV+ydKfmlAe9hxMMQxswE3rCjT+XGFMXtd7yMm3phQcJwyZpCXP7FQ0AQAylHlSz1x4MohamTeB/bm5HuBjJwXCJsdeLTVz1WYCx/TiSRhjp4k7zOPGqzkvgtaCbCVb3z9PfDIz62VNEdI4aactvakzO30lA9NUTaZzYc5V1bVNzWjkNclDJvKCIx+toBaVJD172V0wdoM9jajUqZLUEfAIMUiQTWcfDyDsyEwdYQ6IEV7lKaSsHUyzt7Ey5gQg25rUVOEbR+RPECn+HdGb8zC7F8MY0qgnEvPCcxfkcVBrZ+5dSQEKC71xkWjKE+ScnWMEe4XVQEhVJ5uJBk+nIKElBewWLb2Ns5ytWSpCXkCqgtUs/gIvUE8F1glz+tMGn63HbMpo+DYROlCiaSXSiCCszMFgBzCpDFzkdVh1R/XZ6CwPbdbmAvRPbF+gh0U3CToHTeNpFJjoanIjTHwFDpCHsaXVKs+yt+ts4ikNILND5xBkzLeVA3GsmiKdYh296f8qDxgTM59n4V96I6CvPh2xwLVUfLhxotz2AzjLSTsdVomycwatIUU04euzGw56Urod4oCSfzli0p6Ftw4rz8D9WVs7KoS3RL63EsQJRKNVMnxUQGCt0Zk/FxMJiPVHLl1UXDp76X5jZewl9qtYwYEv1Q=", "layer_level": 0}, {"id": "988e0cd8-7272-42a8-9829-9a228397a2d3", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "SearchResult Model", "description": "search-result-model", "prompt": "Create comprehensive data model documentation for the SearchResult entity. Detail the field composition optimized for Manticore Search indexing, including full-text fields, attributes, and ranking weights. Explain how search results are transformed from raw database records into the SearchResult format for efficient rendering in the Web Application. Document the integration between Manticore Search indexes and the SearchResult model, including faceted filtering, sorting, and relevance scoring. Describe the pagination strategy, result highlighting, and performance considerations for high-concurrency search operations. Provide examples of search queries and result processing from the DomainSearchService implementation.", "parent_id": "54aacf35-eba1-4936-90c8-2768157d3278", "order": 4, "progress_status": "completed", "dependent_files": "shared/src/models/SearchResult.ts,shared/src/models/types/DomainTypes.ts,services/web-app/src/services/DomainSearchService.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:37:25.742+03:00", "gmt_modified": "2025-09-15T22:22:37.765119+03:00", "raw_data": "WikiEncrypted:uPtIvUbMezH0ofnqqRSEttuVA03gcIGmoz29rnyl9THWQ455L3WdCs1ddwjd09ZsuJ0d/186xvR5bs+Pm4tAdqncJ3/+rASmSeGVSKNGrmFada0oXHj5jLta6krF0Rq9j20Yks5G8N6NFHWZqROryxDbSEE0olRYH+A4tlDl2jZA42umcIw7NQaVbAvo/anlaGItZq16gmsWSMRwXvTuT8DdCdFZ7x0HfsJavmrDloP/KRQi+GuUCVW0+0huPxFrRbESvmoROWqPQFQ4E2+QpT1unoNl7hJ2yOo/+IlPFmhcwil8/VQXIqji9pLkr6Cfs1VOevKSZTIB2+mrVpFpDOb40EfyrpUZehyfZma5uV0qfpU2ah8FeewXtF/2FLa//9LyS0UW+TCYOG8gj3kgm7/UdwmPpi8njU8VmpElyiSZWEwfBVLUJNW5NcGtrllBS5I3ao5qD2dZRa+y5TgtSpRfS5DZzNBeQ0hMBUeI9EQvi9FpF71K132lvsBD3PTxdsczzL10FxK47m7dErQ8oq+CUVlCSTDk/YfizZ4Sly4iHMvZFPdJJybDlsFQaa/xRIHpwW/q1j3+msnkFpDZjCD/0ExXvPE4YDBbSTOv5EU5L8bs5DB9fRHKjFV4tWfA0E4LaIVxP6co8839IdkqadzvLp7c797w7SIM60oZYNr96OrrrHggVYRx8Rdi/PEdZoNlSCTRxttjg9HYTwxX5vgkgcX3Rn9JUxCKfahqXyfeaEPmkKTA1INdd7mC4HapfyKqIrmK5fZRSyv0qDXuFy11yjaoIiNDUDardGmgTdqXQaEKLkN6YJ/C7qnlDwfytR1dpVz3XYfkAL21sCyiS6izTqVm/T0mM3xW3K3JB6X1GdvEOLGLHHNPsahPRFoICX2Q2B2PvTobLJvGPB1yAMO1iU6nMicJ4reFfg+SNOblRzK1lnCZwQgnS6OYiOQgsLUrMvgHqCtSW0tibvsMAWpoqP9ey3roUF3655mISHw9h1Z5OS6a3dO5R2FktuMXmWJaYp6mX4teTvk6sqVr0pCZ5obfpiWcL7B1iHthGBz2Q+mMmrCCIZEH50WB1B2/dmI+solObDRm5T83FRC/vUQpAPPbD5H9mv8ZH1SBKEmXQVVk77gDn27rju87yNQj6IE2GU2ELptoSRVkQkU/JOGL4k6jAqvOEtAcwgJ6B2TgXodXEND/VXlaSUJJhH8grwtZVgNcP3VDqbdW+f9RfSooUltHp2rPUfK0GI0qPOUZz1UeqvTOD8ZUaQC5vyW0rsOIiLbi1Xh9ANO5xIUth6Hm24k6esZwikqEvaqE2iMJszyF1423ILN0S6YJVzgIzdbwdJr1CSMdqNdpjIwYM9XEHbENS7Qm10675EvQX9mQylTmp3q6a5PPaeBlAdNzB4g07NgZ5W9gRbrCyE9YOox++5H5alzpxeLp1ednkh/tLLr72L+QL/55RMSqqxjL", "layer_level": 1}, {"id": "c1d04d48-1d95-49d9-a8e3-a7ce26721b81", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Automated Crawling", "description": "automated-crawling", "prompt": "Develop detailed content for the Automated Crawling feature. Explain the crawling architecture including the DataCollectionOrchestrator, various analysis modules (DNS, SSL, Performance, etc.), and the scheduling system with CrawlJobManager. Document how crawl jobs are created, queued in WorkerJobQueue, and processed with rate limiting. Describe the modular design that allows selective data collection based on configuration. Include examples of crawl job configurations and execution workflows. Explain the retry mechanisms and error handling for failed crawls. Address resource management considerations and how crawling impacts system performance. Provide guidance on configuring crawl frequency and depth for different use cases.", "parent_id": "e3ce4565-5d2d-4e25-9f46-14b64749ee98", "order": 4, "progress_status": "completed", "dependent_files": "services/worker/src/crawler/core/DataCollectionOrchestrator.ts,services/worker/src/crawler/modules,services/worker/src/scheduler/CrawlJobManager.ts,services/worker/src/queue/WorkerJobQueue.ts,services/domain-seeder/src/enqueuer/RateLimitedDomainEnqueuer.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:38:04.741109+03:00", "gmt_modified": "2025-09-15T22:25:08.737199+03:00", "raw_data": "WikiEncrypted:ZCORAne7l5wn5JMRd1ey/ifqVp1sQmfM7jNx4EqNH0i982YMOPzCIMePMX2Ya6mKB4+RdO5Cccy71ZXW1RvCtJvinS5gpExcAQMP3uknsFxOfrN5DV26AYFl++VqZCMngzY3QGp1/MkiWbJSHrZgJI2s4Wal5/HlAG9B+Fw4jyYa3QcY0swxgNQQEZlVpA8wpPc474LX964vuCx3ugCcCqcFR8/Qw/nVMps45ts65/njqKJ0oUqDrQcHZ73Ow/09FmyFJSezUuY5A8Y+O/9GtoZVrXIRWU9kRiNdlrBy9ce3rMcpXIQO2/YllvYCPvQIzGu7MVtAfAum11C/cFPnTr8OgQ8e5Tv/EfniQiPtTI1nzhsxbtbqGNWKxN9Dk2SSLoQGIhdiPLJ2bbjVlBmMR0Qjk2uQpVy7IzzyNCgRGw+Ni7Ugw9hYmwGgaCcViWIjE8vqTHnZ5Ide44I8zyYtmTR5r1wdaNXtG0bbjp5firiP1IL1bfbjHpzk2N1AM7gKU4PcTcvNFt44n8+4BS0gvDjIqUt+erwuoNBF1p2w4umLTaeDCS/vz8x/J5gs4PFTqmIsO12yIZavWF00/2TqG/FikPtSoOhtAf+d0NZOhKvK5CU0VslmlX/638IN2jEy25pFOqZuQ9EaLJeSIUwC3Dwb18RdJGAbO4xwZ2kB2Oj2nWLFXqoYvVEigw4U6ck55IekD7ctHfNND+Pqsvgbd1LDyz/FYcX41Val9rCTzokJDSCSrR78x3jNhwLA03m8/0//HWBSwpEYnaat/6t6anmxgMMr2Ltba+S31zbUDwd32sxTabvo/rIF7mCfMeAipfaFCp6hakFZi0Hbv6IOM93UwgiIuhLVM+PyBeDqRTyKLuKYLSfk2c6tW5SfNFaPYOCh3Nl6TKCnYIkVC7vIBuEShIQs4K1+d2JNV6q/yBsJ/1+uA+Nf2z0JbjX2UZv/CDcYiw2/1jod9kAmQ5vSNDukpCWVMOeAj8P9zSZSdqMyMvIqJ+zDGKr9QEMRyMJ5UiWbVVgxCPvYrcnp1sUPZcvgsjNQ0Pq5vtJN9PFUv5bzSGOSEDnjwdpFINJgbFAqQVt8CRIO/+4Sc8K4GPEgnh8AUuDnAssfd+OpMd0Ahbd1wQq+hvaaGTwIErwarJuhhAvs5SIcUGwPglMAV4TVmtbJXzfn2mAztf5c9YhDWAetEoO7S+WmV4TabCxXUNR7G2B3PFo0ElgoFN4MoLc3Jclzw0Ey0lzVJ6D3gtIGdb3ygE3Evp0yKuSf0ynICZcPbEkZWedjA99SiHYIJI5ioQCFSP9Z0htkRINpkPwhXK1OiSYWOZ6Dxg/YhXp/5jK+vYawTpKfrKivA5ZjfWz/fKCCN7t9qDUkCAHT1XQCf03Dmb75NT5SWOTZlXgdLYx/y8TJNH56iSEma/tDpzL7og+0GcJiWCyPX4/gMHaXE9SGEmkZM9HEpJGavSaF90ItulsiCNwgUkMBXqHQhqpfzFjsrzl5oVBjgHsszNJF+SMxeI1DcAl59M5U84F6iR3U2ctpuRo9I4juE2xFKYnzfMWFWXsQsV8hZmf7QnVgawUNrXA1byZir8sovnLiW0MDjK/12Bm7IQBOPtIZcYWEBOd2+SO/uhFPtya6+KJ5vG9Kb1zZPFFmFCHgRA0n/oAZ", "layer_level": 1}, {"id": "8a751669-a594-448a-a6ba-78eb890246cc", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Technical Metrics", "description": "technical-metrics", "prompt": "Develop detailed content for the TechnicalScorer component. Explain how technical factors like DNS configuration, robots.txt compliance, site structure, and crawlability are evaluated to generate a technical score. Document the implementation in TechnicalScorer.ts, including analysis of DNS records, server response codes, and robots.txt directives. Show how DNSAnalyzer and RobotsAnalyzer provide input data. Include examples from test cases demonstrating score calculations for various technical configurations. Address common technical issues affecting scores and their solutions. Provide guidance on technical optimization for better rankings.", "parent_id": "86fb1ab3-0d9d-4f57-bdd1-151e7cd91e0e", "order": 4, "progress_status": "completed", "dependent_files": "services/worker/src/ranking/scorers/TechnicalScorer.ts,services/worker/src/crawler/analyzers/DNSAnalyzer.ts,services/worker/src/crawler/analyzers/RobotsAnalyzer.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:38:05.724887+03:00", "gmt_modified": "2025-09-15T22:24:16.468586+03:00", "raw_data": "WikiEncrypted:F9710e33V4DUOECCkaC1Wh3hsxD/sIXHSGfvA8bgcsCKcK55U9MWRdhKkgfnk3Fp8HfMYKDNQznDtBRzlpTsGLqu8j5dq+KwTRVNZc6BM9X2kaCv/JZbwoRMoDyN+bo3f7MhnNh3WNoVUpzaNmP8cwMdKf/2iQGnxpRCUtRBf4Pa5E8Bf12wbtHjBu/EKJgVC5zoC3zVF4sxdCobzNNcnFIeyXwknQ7Q+Fw30BWYVhHbxYIqKstSkdA7AzaDe2cOGQpkfgF/w+dIJ8G6K1B7ei6MA0RvOR6vDlPtpTlUxxLFtdi7Fabczn3EnovX0NKQd5CciPAkoBFMCb/dBm8smpjbTksWffyPhAhd8ClTuGpe1sN7VZnjb/4Llg+ltBhxJW2CXi3kKLkkW65D/46JA9fIeeoGgPp3RAokuUvl5vM26dF/27GgS6wI6aDKEsqP/UQGDgY0uohnxjZHiixVyKIKyb7LfKymmMM7FUHISn0Igo9pVZ/UCDox3mUB4EwtNrszxrYgNnqgla6mhxmymtc+BoHUm+JM3Dtj2yNpCccBZuHw3bwz9k3xLjSB4FYNWOW5CQ7iI7nuzJk4s4aM8CVxfnLP8YDAa61DTLSIcPQy3WvQxplEwf/vawOsg3pHZzDUUyuCGlh1rgANuTB7bU+84Cd5mqhz49zRtFOYHYVl7b3CcRXYjslc6xYsbwZS+EhxshOo2CsaSh91b0vqryScda2uNXpF/yL5+G/FfLaQEJxgbSShWBkWVE4lfM25PxZA7jQCI0nL5v9aNgW1VRWHP6jBkrGXV9ftmU9EQCMgKYEeVSFuleP0FX8RO8CukpAVuFiZ9vLGhcoh9c9w1C2socRkacog0sKEJ6Fe2Ni1MFtkJWRkQtQW2POLM1mKL/GQ0q5TwFU2zV6caQA4kx06bYDb3Sifqh4+nSeWAE2DpGwW40Wz4beGrCpE8NIp8YLAl7dXYD0hLOEtR94lqhfzEuIvM33hSjGJ9TNNXDKAMGKcUuDuWeEUOZxmxKHTSLVoQqC8FksjATBnC7BM5emoe1NcFCQ/A6vKg+noIv7G05sknBT8k4CfK8HHLUsIeuPYancCOB/t73mClmCDPYNrc268CZ7XKFOzWjn84Coiclu0Q/lWldyUkOBN7r+2WsU9kHa32X+zNjDQ1neLNo/dVAkYntueGNrPD2C8QXfEtx70wDvMPLFzyxPI1JZE/5KdPZfHSnSpqTP0onYf1g++KIwAlMf0jclN6ugv2gSSEBpihOHqwseTgbr0LjjSEWInuXgGEtbI3QHNSgYYp002nhTAsrkIrrVVOT8S3FFkF7M5TOYhDxHeX0CxAs2lsXCYARJdplA1UI0Vp6LyctFXSzqf7JPVfCkHxb81EfxiBvAs3Z5Pu2qjSZtBsjke", "layer_level": 1}, {"id": "bd3d1bdf-7006-433e-af3c-0ba42eed671c", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Chaos Testing", "description": "chaos-testing", "prompt": "Develop detailed content for chaos testing in the domainr system. Explain how chaos engineering principles are applied to test system resilience by intentionally introducing failures such as database outages, network partitions, and service crashes. Document the implementation of database-failures.test.ts that simulates MariaDB and ScyllaDB failures to validate error handling and recovery mechanisms. Include examples from chaos-engineering.test.ts showing fault injection into the worker service pipeline. Describe how the system's circuit breakers, retry managers, and auto-recovery systems respond to induced failures. Address testing strategies for graceful degradation, data consistency during outages, and alerting effectiveness under stress conditions. Provide guidance on safe chaos testing practices in staging environments.", "parent_id": "5ec7435b-599e-4858-8bb8-f0eef69cf07d", "order": 4, "progress_status": "completed", "dependent_files": "services/admin/tests/chaos/database-failures.test.ts,services/worker/src/__tests__/chaos/chaos-engineering.test.ts,services/worker/src/errors/AutoRecoverySystem.ts,services/worker/src/errors/CircuitBreaker.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:39:26.3299+03:00", "gmt_modified": "2025-09-15T22:25:57.620479+03:00", "raw_data": "WikiEncrypted:dxi1KPGlY4TKEjRehWxWDnt6LWDsA6Ue8RucQY/TGlgIK2Myp489O4uyQ/FsijCZ3H2JnH8b1lZwhTZqzWoRMaMzCYD/4MCm5c/Y3TVFVx1tYP8QDbCjkhDzrXt2s6md55/DlkzyKQYqnSuXmZT9hRDd5jyx+IgAcCZZo/VvaWH5lTVQNL8vL6njy4WiPsKf0RovEzpfYAObD2lxeNBCVq00Bodnh0F6Sqwf1JH6n9tmunQqwodBXYKaTyhLtCQuh4OLFHgP8YzNRSfD73ilFYA8moGnsQWrpJ7XFDbLYyCcDR1Rco3nfIHQ4LCBFXAJFpqkdUiW98WHHpPpdLlmsetRcKP53GGGKfyBwbgrVNpGnkcmbAZNMtL3y8g1ma+Y8pniRWSHiMNboEkTH/4D5d95FR6B8CUQDg6I66AE8qCMVwrDvUKNWiN5sCYI6LYbozd1FmN09MBjSpbmK6nXNTtmH3pyBq9gzFPgecpS/Yf/K3eo6Y3naVOUbX6M4MnB9oNx5xQa0x68D301ndGwYmes5Ws/np7FbVtVBL0Y9J/cweuXhLTNjALvKfxF/UxbyPKvdkcJyup1GKvb+StRa3v9AZTfDBcf/SjgPeFeUuPrUd5W5hKyj9qvvLr0wxOms7P/O2UAcvpXW66UoMFe+L0y0CwKoXuUzHIGL2/T5sfNv/7IoQPWQ5S2r3gzoJTxRks60pkh3gsdtj/GMBPu4kaPZW8W61yLPn1UXzKMunvR3OuPy3/1BFqRyMQI+7m6hj7bZjWPoM0HRQdWgzFL5zQCZcMXkQ8Cyn1bcgevcLEA/NevzFajrF6YelP8jJdtyF/mBjzAysjuir0Oh7kX+eD0uvgmx7HYRjsfgrJ6z/w1G8Xkq+/+rDUzPhKcrUmWsMtHP3HPciW4Pn7+pId1GfgDyWEmeGewWaQyiZFqPSG40FEvFFnjZBty1hf1J+rRO3IF6RFBNAoP4fJb9RfllkrmwjZGuHe5vPpG9tbLncUONy4cA2aab8m8HKKkBf+Er4+8XgNU8K94/rYSDcQcEp1nQjiK0yRCqb7o7sa1d650lEdAGYR+DujsaOV8FnvT9DjMRzpnCzSe2rMfNt8JzOTmStZn8S7QleXrENByuDBkydbxqcDrJo5h98TFw8EDuqEvHvz0tVelOWo/mgOmoYq1FGYDJEmsLIfR1zOsDoeYEcriQd9ePXyC48gKtOd3a8DF8QIOJUzVSLpsq2iYQFEuZ4pImIw27lDV58qyd/bKNPFc7I1ozivGByRFgcYDTbcJsFxdg63pSqdpQ44pL5NrVFAc8573wx3O0xNLKyIkJYimLdmqPhujubiwpj4nNITmqIdhOQpAACOpfs4ibbzqA5H0XAUMcMjLSSvyDrQK0bSJluSE8rN272aTxf8ALI5DJZTOJcxdg25sSRXHsdRI0VDlU/V9NqKwiM2kWaO9suq8pZndHoIhpnPYOzYHji7LGzNZf8vdaU9XJpcjzL8xIjQFdG4J9s0Bc9EwFdc/hkROv6Tey27f647oIxUCWBBprYcWQ0QJZH/al9Cr4RiLau2ORbAUyT81pR7La6/UXHa6PK+kb1vN6HaqQVzjAfzGHZ62Km8zwUoraiR66odkY38U7GZ9yrdDGNl2CY6gPO0biOq9AYyzRy0zmR1cvNwvM3FOnL2LXWpQ7q0GIMEU5P4zztvjyD/9ng15q5U=", "layer_level": 1}, {"id": "4cc1d72f-ebfb-4439-994d-803e620048d9", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "API Endpoints Reference", "description": "api-reference", "prompt": "Create API documentation for the domainr API endpoints. Document the RESTful APIs for domain search, analysis, rankings, and search functionality. For each endpoint, document HTTP methods, URL patterns, request/response schemas, and authentication methods. Include detailed information about query parameters, request body formats, and response structures. Document error handling strategies, rate limiting, and versioning information. Provide protocol-specific examples, security considerations, and performance optimization tips. Include client implementation guidelines and common use cases. Address API evolution and backwards compatibility notes. Use concrete examples from the actual codebase to illustrate usage patterns.", "parent_id": "", "order": 5, "progress_status": "completed", "dependent_files": "services/web-app/src/app/routes/api/domains.ts,services/web-app/src/app/routes/api/rankings.ts,services/web-app/src/app/routes/api/search.ts,services/web-app/src/services/DomainAnalysisService.ts,services/web-app/src/services/TopDomainsService.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:36:59.65564+03:00", "gmt_modified": "2025-09-15T21:44:00.807082+03:00", "raw_data": "WikiEncrypted:C34GewOyK1SlumqKiPsSg+WPNa2UHH7yP2PPjE4/OPkAqUcHUJir7KChuXtog6jM+wNKb2BFOO3dDGf184Kq8kZWZ6GsOpxYgkVO1LzgCQJBj/2cV/Wsg4hR2kxkcHAgsFsQBOoFNpCWHCNhjR7yIjtcT1hG+Di0ntYDGogecIbNAklegjRaq4AjVXbSeA7yUGNj05SIf923POASLIn3dAemgb+YA3SBDRUYL5T04Pr+se25n/0y4NFLWAl2FtRiVy0cMEo3o5IZalFDyusRYPCyOr6VKObj/7s6CNZVNOobyxFm9FiY1DovENKl2r4CIsiCfnJvlpYqM9LSjlAkLS31yxZD2DTEze3M/8/Vl21vvST70NGvTjLBn5Ilu039ViKyRy6ZNG9LeV/7HTIxxVJQEhqpPA4BkRd6BZohotg+Z7FIBq+mgIBwmdZsE01CjRwuIcBRCefUiVl1VAEo0U0Ml7a6AkY21z2YDwlQxbl+DCtgiv/XN6n/DkgXQfLlG6h+uZGwCt7s2R8FscsH2dnkHdMQH3GDsgmP4kABJAa2L0nQMv/TVK25gKteuL1Xrs2iTnSTyPfFnhTiLY9z5oSFDrNwLGfQsJKxKAHvRucIEjvY8Ypx6TQM73WIDiHqngSlDuTLWBwnM9pTjpOnJtK/jUJn8sJzBGv/BJwqY0rK7zRLcGx3CoR1fPp1Kx2u2/msN+PQmjabsvYVTl20yIFNc069XjHO3Ubzdu/Jg04aDjvFR1mfE7SOFuuPXGEXPlam+LjL0QCLttKjSqgz9xUyAA9JtUAHL39Si6x7M/H529OjUs+3quzzIbotUApY311NRMcQsv1IYoxZCMF0GcEuec5NHgQP6+AnONA8hNqlMcPyr3WnhXT1fPA97OB39O1MnmAJ5gjS0mHA7He2L94hk2UhoPmD7rg3LTYPuA2YyLs+gocunpB0jVtp98ZQKdEaHlUyo9HJui/GvDl6jMVN4yayVgTDEYDBIthpCqv9D+ca7xkj+e8rTpUYM5RrhQl+ELF71+aItHlZAxpcXN2GlfEbevDw6GPUHK14iaZyG32a7JWCTCUJ49Ed9zgLzvt3exMiNVAhd49/0ZOMzUL+VZIo2ZB9u4KyLsEJOePjGWwcV/NVm25fPLZW4CCaB+PcuJRr87u5RMzEJkVxyrIsmuIiVzwfAkXR8b9iD5ZC98ZK9YkUetuPANrYbhrSxIod+Xaai0Vb1C1Bkbg3VM9DYTvYpQCKhbOu+uEtGUvkSP9sA9xXQGywveL2MPqCN+W9JAhjTCcf8M/8R6uG6ZfsUmVI3e2qtiSEzhSX292ZHj2dHDx3f6xCMLo6O+JwyoXJw2oeFjW7FudkS3eGgb4XE/8leK6u7D3TBodGuTOM9bByh0pI6IQjCxM4w3gTjJQdCNxffLVtiFT9kbNzCf90/fi3CGu4YgZNdzC7xgKIGYjfk3ggwRSGtzvhlLeNwJl7pufU1wl/kRPr7XRSkeA0muH9kyYDtQpdWGl3yU1Qyl7tecl3pc6B/RYlQJdV0QvuVsObtzwQAJV2kLHPUKMt7CSiHx+Hw2+7IWlKwxNvPgAXjoc+uD81ZOJONkUOyZtqS/p42BDxDf6JjC4d1GLBqy+/ZsMTZNtDsy9M600DgkaB1eUV6bExM/Z9kd4p", "layer_level": 0}, {"id": "16f2d074-d19f-4ec4-b84a-84799c340ae4", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "MariaDB Schema", "description": "mariadb-schema", "prompt": "Create comprehensive documentation for the MariaDB database schema. Detail all tables including domains, categories, backlinks, whois, and crawl_jobs with their column definitions, data types, and constraints. Document primary keys, foreign keys, indexes, and relationships between tables. Explain the schema design decisions for read/write optimization and referential integrity. Include information about data normalization levels, storage engines, and character set configurations. Describe how the MariaClient interacts with the schema and handles connection pooling. Provide examples of complex queries used in the application and performance tuning recommendations. Address backup strategies, replication setup, and migration procedures using the database scripts.", "parent_id": "54aacf35-eba1-4936-90c8-2768157d3278", "order": 5, "progress_status": "completed", "dependent_files": "database/mariadb/init.sql,shared/src/database/MariaClient.ts,shared/src/models/types/DomainTypes.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:37:25.742831+03:00", "gmt_modified": "2025-09-15T22:26:29.372819+03:00", "raw_data": "WikiEncrypted:5YQ56MSTMCMqWKtmO1JMhYQGLaWYx1cIYU89OXNbg/Qb6oRvw2d17+42SQvmVJHMVwJF61tXdSreJp5SBQkHNx5bR3V4a0SPQwP+uKoX8KZNzmGPRJYwVXIcRVtkAfihLyQufqcXxBIkq0H9UPZIzg0lJBsPJvrFbXa9+acUmU9bFECI9hG+FCGgY1zbE9RwUOPqOlOyGJxRpSg/AgLoyirMcqGEFktsH1QCyAVFjALNxHJ6yj6p77geECnWn6sCkLXyufOubNwdjfCk+Jt7qTPa23elHayuB3yKvGibYWvRar3/dnhktq9kWQPmO2l3XL2XNcou1aRsIF4HpSU7KmN0654skDgB+L1sQ2tI5KTbIKZmXM2/JDT9CGodqdaJWp4qD0NkDPg44B++RcON4Y0dl1DSeSaknkfjVSqeEDEGf9UF1uTsmZ5641m3KBSM8flPmP3h7nKIYch+WGkxMdTx0vSrpakhNxMLzKPaFSdI4XveP6XJ3SoWe6CJxEL5MitjLpa9Qs2ItEuJuEgfKeR8SvSqP/SRbS5cuHh9ky1xdo2LNfSuDfKhwUAVMWVCtAFdwkaHHYZpzucy+6SYQHBZ7f8RhBeXoml0kuzXtDUnnOtOpZelUjNO4EX8m1zBJK5TGneOk+JdX4dAHWAl4Xpti8Q2IHUKxZ/O40Z88PAbtRwrpHtVVyqC418nvtqTpWzEG5L0BLwJ0QVYCbkoAGiGoqLV4N/lhiX1jlq4U2wrMXeBRoRyXXPMuYSYN1dccK26Ivn34vLbqVJYHZdf4DsOnwWsK6+RUvrOXTM9OGJV5qAuZb8Bupxa8fXNhERqPT6ORHtrjkNHGggm2Y2Afi5p1e5prMOhH2lgqOaXl4rOYcraRVb4wPZUus13sWzE3rCxHWWKpa+u674TmdX7e5zXYnX5r/L9un0rW46L8ra8fZiNP1twkqy9iQwsSRcI+rKgnRElqSEJdbie4CxO51Z0I9kJF1EvphWlO6+/oAZMFnCZe3wnuLhNf3OHuCbN7gs1EOY7Xgy9Bc4KfsWBVUdVzPJQ4EY4Muam7kXfod/2NsB7gzfkpfW1MSZN2RhPeyXiPi0tPS9oBwXLGKG7uCvUWr7yMZd5VotFBYyMXln2XEFXZL/tu2AwShMW1Nhg9p9pgyzs3A0Gxb2OEw5pd4zaVPFVXXRNGR5zzAWJ7LHLbfE0QcTV4cSr+Qlti0BI48jPqjDFrpkLAQu7Xy2WVVqKmKsjLnZWFdEpr1vNYkXMMbDoThYtLz943N5psy0MLRn+UjOGA05o0IbA1JXVkgWZ8W68pY1w2J/K1LF323iiSROVAxv5aCAmSQn1sQgd6aNLRS/+QVC7O3SX2W+OqcpzASjct904b4BhZcoy8k0fxz8uLpw5D/hiDCVmcLhNqhwotCbobUf39nTG3Oz1gEwJQ01oy9nrap/DzUfwT4vOVs8IkwU8CHKHYxAW2Dq2", "layer_level": 1}, {"id": "03b49ff6-2295-400f-9533-d04f0f74dc3b", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Backlink Metrics", "description": "backlink-metrics", "prompt": "Develop detailed content for the BacklinkScorer component. Explain how backlink profile analysis (quantity, quality, diversity, anchor text) is performed to generate a backlink score. Document the implementation in BacklinkScorer.ts, including analysis of inbound links, referring domains, and link authority. Show how AdvancedContentAnalyzer extracts backlink data during crawling. Include examples from test cases demonstrating score calculations for different backlink profiles. Address challenges in backlink data collection and validation. Provide guidance on interpreting backlink scores and strategies for improving link profiles.", "parent_id": "86fb1ab3-0d9d-4f57-bdd1-151e7cd91e0e", "order": 5, "progress_status": "completed", "dependent_files": "services/worker/src/ranking/scorers/BacklinkScorer.ts,services/worker/src/crawler/analyzers/AdvancedContentAnalyzer.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:38:05.725823+03:00", "gmt_modified": "2025-09-15T22:27:25.427391+03:00", "raw_data": "WikiEncrypted:G1CRSyEbW1suW6dt9AZHhTsBcH9xixCcsHUpN1fHvapvExoBIzTjgHwlydBsTVq5mslVl8cZHWDprT36ZuNJpy4MVUaCyZVEpoJZI1atYZSt+VZY3zFChG4JXBQXl757TSTFt+/zs8W7bZS9+/A0UCJ8LvVlH/2UC6rxioSyxOpv278vUEdgPwIEJvEnBahXHC/m/AlmqHEyiWXY0EF6R8PBdEbjyLLoW5aEaFlHhUxX9yaoBuLnBxAOoV75RNdGZT6/U8HbqIeIU0hstHHNVWofJiBR+dM5b5j3zQiXxdRGVHGDc5EeUuHZxykFSzyR+4OefdPACu7aY7h2rHmCAVN1lP1RJLp//OXGaMBcL+8yb4A5pT9EZj9uZnGYy040/3ibS2nuS74t2b4XccgUvoBSHdm+LW+mV/a0DIsx617I9H9485ItpLA8YwdEdF9pRxBhwYI6BKyhoL7qN8sVsK3bLWFDdCDz+BrCB7QLPL/79B/mjvTI8E6/Usi/vLnWx4yDJ3Q+r9tWDLH/r15ZK8Ah2A1uBZIhqhv7STEwK+U0G8jsf12Lc8bs+yBYHO6SKGwDmq33IznQkxS4m1uVTbGwLbJZLxAiEjjbjSEokm0xn3gUTmgBYUX3TQXmkXDyjTGyUYGfMIdNkB4GEOExd2P1CTYyr+GPPiXNvzq3J2/qqHAsIGpQ4FsYF9VzZLY3FUvc3aeKUsNh6EL0oqrQznlrJpzi2+xFuAUKNCHnU3WCecVh6Ze/vmAE0NN5xa7051dif4NCtO/6Ychcpwcnwk2dfZZui57M8q3kYyKNJxoBGY4nZRXweU6cisnlM5WB9MlW49R9zv/JCIoorKPwTL+tCIXSNVqKKNa/8ZBeyBOVJ0ZtPV6tL6N+/KZVCVkjbeUvb4NGxRUM2dZJ047aNCcZxgUkcZCZs3GLT1PYD5pch9rfSj4LFYDypIfU6xKVgUplJPd08OMXacEZFX4HkT5alEr/i3DHHY3eh7JbD6AEsyg0BQLvIZUBvjDspMKg8+kAfxRBcIJruM+UpotL1xo41OK6lhYrlw1FCv8DPAEupmPqup8Xc2Go8D8lOuqt8/0j/QvdDMfqVnUmelrvUROxf5IDkrp0vle/tgpHN6auM2VXRGeODJ9SAlHqUxjOXHVq9jEKcyraanq7X71Uw4KFjsfS5GgUqkyXi3cDtZsWR2vxXYp6PkOaSdh8qHSl/g/o+SCmBvsGL5Su2eQUp8GgpufCDfmavJOQZgVFDL9bWy7/mYM/88lffbDbWw4uICyPrHOeI5Ryjboe8SBEL12ucZ7n5S485upZV+0xzLY=", "layer_level": 1}, {"id": "eff23966-17fa-409c-9add-d34d956a5c62", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Visual Testing", "description": "visual-testing", "prompt": "Develop detailed content for visual testing in the domainr admin interface. Explain how <PERSON><PERSON>'s visual regression testing capabilities are used to detect unintended UI changes in critical components such as dashboards, data tables, and analytics visualizations. Document the configuration in playwright-visual.config.ts that defines screenshot comparison thresholds and test environments. Include examples from components.spec.ts showing how component states are captured and compared across builds. Describe how visual tests integrate with the CI/CD pipeline to prevent visual regressions in the admin dashboard. Address challenges in testing responsive layouts, handling dynamic content, and managing baseline image updates. Provide best practices for maintaining visual test suites and handling legitimate design changes.", "parent_id": "5ec7435b-599e-4858-8bb8-f0eef69cf07d", "order": 5, "progress_status": "completed", "dependent_files": "services/admin/tests/visual/components.spec.ts,services/admin/playwright-visual.config.ts,services/admin/playwright.config.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:39:26.330441+03:00", "gmt_modified": "2025-09-15T22:27:39.291352+03:00", "raw_data": "WikiEncrypted:iRwwdaIwaxjXYwUFfgBjqHWTYypjr1KjeFmTJ19DFONK+SLPKnQVUuDGWaavRZ9ehAetDDyfDXz3dbRhcTx2K/cwPqjFon7O4WA64bKt6sPxLm8ap93zK8gyOMEUCsszskGaCOJHhu8qXRqJC2NsNKl3rsXM34IP9ty5PHwuAkDvGZ9AV79U56TqhvVl0vVRLde2MV85daOJzwNzxv2aO5E15lj4bSuXTHfYyukIUeSi6G1Cl8uyQCo3v7F+cTCPKYCA+3x01uBVnpYvILZCKju1jlikLBo3VP1vd3gk/MYWQrBrcv9v8LpsMr4sNh0o+EZ+7k4YaB4xirhSSJUJZGmIca3zktKG8Hr4h3eGVLYPNfsPrR78FEnCSyO2Gcrt1KXK/lHhWcR9qaqHVjR3vnT6JwlUW1+brtGcsndEwR6vnT7DCYFfSMiT/pp/+R59iAGkGNIsYJAnQejCdLovasCDxy8uQ4D7rozvz2ovUpcs5irEpS1nkIrAOsx00UZbJQMJzs0IxYMGiESb1Se6QtR+HWTblNyNhfxQ3Ug5fLjLy8JafFS7jDGoeJ0CYGK3VVfeHnoXZEGBwktCZxrgK3h8ymg9w7KLl333kDRZN4Da8I5htMus0qths9FzhwgMFN7fT8fexsgxDTyJipC/PPrsRsS5e41TPJhC2w3awPqaDibkVPieRvFHFBEfFQsxCeuhKgSbveZmovw7LIg03Xdz+LKFwHo02XC5RxfvtqJJmxAS+vYCW4ugDY373LQnXjTS4r3iIl+d1eXICw7AvV/Z/Ms6sBQCtBe5HbJ/ch7kkI1EgtCtztaPa6G6G1cpQVrNN7/lzcusJjhdXeVLh/VBW6Cuwb1lBRfoXYQhcGKAt6MZjry6awSBdHFsZqnjBtlYz4UnuAsYOA6i/Wi72sT7JTsAx744cH0lb6k8jzZU55fBW2+aeRzb0HQ3oJ5IyJcHpDYJwBkfpw/P98HM8rnJaBVA04UR+VA59F20B1O9vb/a9jy0+zrXw3OijkRPM9jkyXH9VEkz/DtkNxd2dneJEp26uDZA0CIapARtFtcTHpEY7gBQx4mSQmM/lJruUQNUsmsRqRInoiMY/xO0krT4L4Ir3ghhevGDFVPOD0qtiUEAKBh69Qk7bHwwS3pgNYfFdLL5D1JZra78MIx9tcSmAp8KdPEBp0Nk98cj0+Zb4LVLjSOxetS3nXUIuF6wgRANaJklryNUuhudxGPLYk7ZtG3t6BslR3beTGO7EvZGSUJ0kaEzBJDqbA4B3IU3Lh9vVG2g49EAESAdnDWggD1YoFD9qsyU+3+14fyhWmIjCJ635qKCsbKd5kpfgxrhtcwsier2gPaHiOwQfeGINd6rjpTCjXXFQK6FHQm7yhUx9O0Lyx0w8Gbq6a20kpgBb+XYB3OYzTwPrr66vy1MqtPiPNpz77B+CUwBbeaV4elh/BIBhLIIlqlcn4uLQLr+NAs8RNwOIGI0GjfDQ1E/mHvBsFq3n1CaQ8eAogbFTBMKDfWMem9E0rVJizTfhR7QCOnSmParsx0tEGSzocUgARtAL/e9glIcxaDvtl4tpX4BF1HnT/K2oG9wsVCPRipg", "layer_level": 1}, {"id": "e3ce4565-5d2d-4e25-9f46-14b64749ee98", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Core Features", "description": "core-features", "prompt": "Develop detailed content for the domainr core features. Thoroughly explain implementation details, invocation relationships, interfaces, domain models, and usage patterns for Domain Search, Domain Analysis, Top Domains, Domain Comparison, and Automated Crawling. Include concrete examples from the actual codebase. Document configuration options, parameters, and return values. Explain relationships with other components. Address common issues and their solutions. Make content accessible to beginners while providing sufficient technical depth for experienced developers. Use terminology consistent with the codebase such as 'DomainAnalysisPage' and 'SearchPage'. Provide practical examples of feature usage and integration.", "parent_id": "", "order": 6, "progress_status": "completed", "dependent_files": "services/web-app/src/components/DomainAnalysisPage.tsx,services/web-app/src/components/DomainComparisonPage.tsx,services/web-app/src/components/HomePage.tsx,services/web-app/src/components/SearchPage.tsx,services/web-app/src/components/TopDomainsPage.tsx", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:36:59.656198+03:00", "gmt_modified": "2025-09-15T21:46:07.706553+03:00", "raw_data": "WikiEncrypted:luoNp8LvFa7zGThvIT9T4gfD6KbzJyTAydbcRfVv0b4ipMir7bDcMLDQzSOEcRtlGF3LseUbKUQSZy8GFXzEwTpi66geWMy7vOrsf0z3nZP8OvYPMxqy1nHEGzknarrn6HuYtePyJUf8NF3EJtTQUZ3v9aZaD38vticPrp1PxANfSqexMPiedAYhlZ0mJ5/Zo37UcQYKjYKa1gj9fDTAbFRgE2x4s8k9WM4iuUAu/GIEbMrSGI9r5AAEKzTdn6CNJj62m9RW8pttx/7wlU26Il380jUqmbjQWGgHdxNf/ISpnzDj6m4LpPrsHKviZ7bBTowKyFTkzSXCKENv0BhAPg5r5HWBk9PuW7p9GtG3vlHFbLG3M8VGpa85zlUTN334kXxwmSvN+OCbc2H028S0/Z6J+jWOiJaM4kj1yL7ydI0oNblUjxUVFg9O7lJ1DSS5jhI3jH2j9hnLazbGZomH+liJQ8W1z1zcL3F7ZRQiIUf9nFe1/pY47dD0NPw8zOSBMtVY7FKfxs9MoMjBFnsUcbUBWEjy4LnmVE0G997nmb9sG37V4KS3UAIQbB//pQS8DYPWfcq//92LuWQj0QPR6oV3AitqyjeIEDbhASskUsr+r1Ome3laauLT17uHa9ANRI+QjComRe6SMp6ged7cOTzZGPF4UG3BZZmU16TZHaGjfzbwepZScKkjPuoKqXgWoEDTf+jruCtzijGIC3EdMtXF7f54EN0SyyXyNb58wawCdfkLzHC3eS0EjqQaLRNx+Y90BImYTVL2QGNcDWk+RJ8US5wO4+0I4ov0pxv0IqyfFwWM7gWYtuk5cQbu4bfOTqRCEkY89mDe31+9JPBvuPhaH/Ocpl5+8CIP8IRR3Q4R7oYljaqHLG5fkDRTWCVcKk0GjqD8bXTHtvUtdazbaNNw2r++3fAu6GOZP2E94w9nady97ODXzM3aKrTxrV8uTyuRF4vIQeILky0kefg0wYuqk7UH1oNacyiJainjgFfLRKXxpkGLj9mSOlvYflBxrh6u7xJon/N2YhdwtsXG85DXTorwjYTe/uQoKe4fXPKhZVpIJ828q7r2vdSZB4XiYEfdnxztQaBPUQVUVLsN0shJlcU43V6p2fxQ08zBRwmtahwF6k5j6Ju+O7S/SP96C2wCRnnn5fiHbDxlCY7WrO+VHHB42LP8SVOHE0S5w+VgMm7EDRKbUfPj563A4uX1Y6FWBwE31l2fPa/YRz2deQqbhHgGCUDHhmbas6WZCEv0WhrNZ99FWuL+/oQVI9senxbjGUXsliBbyhsAu5icIS4rN89W69UAfSHmyjyAHvjnmSGtVyxlggALFo3MCEE89Vl+HplYt+YnAF+3VjTGAE+k2rqJu3Jk3kK0nV5v3FHTmq5Yan3HkjGv+c8/4BKlDNxuLghQ0lvy0L0LUA5l50ENQVeuFnBBWd2i1RNw6xTIxf7AuEBqGQOPg2YWytKywdsLF+u5JK4yvWzRsYuOtgOHW8REOkez+OksiGkoFkmTTSGPzN8p28p/Pqt6DCgpDchnq3s7klhn2HHGfDrQs5nRluuSWetYOdzeenpgtbEkkINHjXQMGW8nMBnD3wMHTsp1tzTRY60QNXVyv+4dh+0Q/97HoRTHm1QiD6qk/4TozR4NaZn8CN5QFgDduBH7", "layer_level": 0}, {"id": "608ebf66-7fa9-4be9-aad4-e4defd6c77b1", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Manticore Search Schema", "description": "manticore-schema", "prompt": "Create comprehensive documentation for the Manticore Search schema. Detail the index definitions, including full-text fields, attributes, and distributed index configurations. Explain the tokenization rules, stemming, and stopword handling configured in the schema. Document the data pipeline from MariaDB/ScyllaDB to Manticore indexes through the indexing service. Describe the search query syntax, ranking algorithms, and faceting capabilities used by the Web Application. Include information about real-time index updates, delta indexing strategy, and performance optimization for high-volume search operations. Provide examples of search queries from the DomainSearchService and configuration best practices for relevance tuning.", "parent_id": "54aacf35-eba1-4936-90c8-2768157d3278", "order": 6, "progress_status": "completed", "dependent_files": "database/manticore/init.sql,shared/src/database/ManticoreClient.ts,services/web-app/src/services/DomainSearchService.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:37:25.743629+03:00", "gmt_modified": "2025-09-15T22:28:51.664969+03:00", "raw_data": "WikiEncrypted:H95+9J21CVPT/u5Cib4BFFyk1RVm6gfhDcxVf7p/+4X552668erE7NGOxvj7VEqiTYjY+HLtJ2ZkkMs0yI/NXj0SBJDdYQzNrTTuAI3AI4gVmPK6MB7fbA1hovh1aZikJjHV0LQ/2ms3MucsUDvhSEUUtyBTO/xx7oDpSTmYLbdn0cGYlZgHApyofRKJ2MaKYYXnuUcQ9bQGo2GYyE3mWvwkPm83EwtY46Q10dl+YKOXJbEv6O9En0vzCwnR9pfXTo/DLzbed6iudp4OJUVY3KVy3pPXkv5Tq9O5KbJtNa+IzeqfGOi5bfZRRQz5Cmg4nFZbqq/qnqiGiYy4IqE12iwSLMq9dfngqW8fPMIC77QsTMKE1pxh1xxTo/DmkwNszrOpB5lzU7Yw0elxOnQ0N7Emo/SDt6iRT46an0l/pL023EoRfpHWRVEC+X5pF5Rg98E3fVelcVorEu6HX1oYpUY1h6W4HTSrfgMM8I1UoyTHO8EGCbuxWTTBwMavJGNWU83h/hIfkAVAQtuLV2QGV12SGZ1ZlSz3NtML5sHFfWei4zJXnxtV26H9uzFedCsLEa2iztR7HcNugrqdWzk0SqkuGTYMvlwq+IDWsm3Km+B6Pyog+DZjtiUF8AqaCfnVC5unwBLIJXUkFCqAKpSKA0RKPn0mo7mgpcGrEQyYTIiH8ZTDfSl0soCmqHgpWr8HiONPnYCjV9PGXxzgXAFcb3cvYZ3qhZ3rTnXDQq4Uo+V+bkNRzK/D4B5v58KPTV+DnbtbuwFo/BKHObFV1PQouSedqwU6/SN2B/E9JZvmn/49Dm7DLFcy08FbmIaaONng7Ky6NGfFfj2PQFunDqT5vUwtKyAAbVzUeAtwnM+evv8XBkXmmUt84tkwyD3iTQeXSLG+icv2nDGPyI/piWXwt5nGX94wijyhJHPl9oXNoOcDqxhpt3FIm8ozbjJlPylYsgaujuf9zKqX2HgcwP80uzxaar5ijWtFIQA695uV40/bMbGA5jkUWopmKC9gVz6pcn6Vu05n6z9SrJV7rYf98Pk8UFvFXr0k8flT1lz4mMQBJm8zXowVYc81o1A5bArEIEgi8dv1VGGr30i6kX0dK8XAEq7UMR1c2oq0yBSZPN9OIZ6NBI0GwffF+lVV77rEM4zf453WfVTqTzoCdmFB+Uyp9XRNu/7RFbGOCpxyFikJKuJnOseWBgQ1ujcD03epmF1BgQ/eIgj5BL4gfrd77LLC3YxOiK64Oop0nLOIDSk2p1ec8F0GEfv6U7iNdymRLb2XZ97g0UYqGE3/LAVWyvDmNAoZc/NsXxjb67xmfZ6ghZIb+Xnw/Lt78+qCXD4RWzVDMomQu8fNoCRrrXd2i+FJHU0OTDTjcWow0YQeXf6yZTWHI8cVCCh2Lzr92YlguIbHddz6L1qqPcDq8UlVg1KleKaE8kC4NLz+wvMZwcqhc2JoABZflE0s+j16n3o9", "layer_level": 1}, {"id": "b5217cb3-2874-4c18-ad3e-b5dc2696aeac", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Ranking Update Mechanism", "description": "ranking-update-mechanism", "prompt": "Create API documentation for the ranking update mechanism. Explain how RankingUpdateService orchestrates periodic ranking recalculations using SchedulerService and WorkerJobQueue. Document the workflow from job scheduling to completion, including batch processing, error handling, and status tracking. Include details on configuration options for update frequency, batch sizes, and concurrency limits. Explain the integration with monitoring systems for tracking update progress and failures. Provide examples from ranking-update-demo.ts showing update workflows. Address strategies for minimizing downtime during updates and handling large-scale recalculations.", "parent_id": "86fb1ab3-0d9d-4f57-bdd1-151e7cd91e0e", "order": 6, "progress_status": "completed", "dependent_files": "services/worker/src/ranking/RankingUpdateService.ts,services/worker/src/scheduler/SchedulerService.ts,services/worker/src/queue/WorkerJobQueue.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:38:05.726675+03:00", "gmt_modified": "2025-09-15T22:29:28.663345+03:00", "raw_data": "WikiEncrypted:x7xafZBxVSAVM+m13jmM+5FLRzcd8kA/XNvonSm02kyxxuu1OR2UohLbq8We4MqRqXtOPzccnOnxpEIqWoU+a7ORSDS3+x/tAQE20Wn3/JFOA9FTwaPQ0i4ceHfTHV8h62ha/m2JMOtsjxnwJDscX/T9ZBJ7FTlLuFyI6IESVevRW4o2q92jCJuXTgMoF37syTNOFrc/6MTKPqxAsbkV6Msz8BnMbtWGzyOjei/COOpnaa/2sugpFXBHos9k+YRfWnr2l7YKIsl33wiVMZjQFeIj5lzP3k4er9kdAoMEcvsysIxSFRzo89KfoaRymrQJcg7zomxjkYnA9F0nRg/wW6Xv3Yn7+HPmxudDLu2TVm/6et9DdQ6L/oIXehzSPdWxyB74iC1v5DvpuOf1U/hUC8TDiNuJfx5voEtLO7h6rVqX5yAryZ+8oCvbz2g+hvn2O23jgbVJzoi39Z9nkvb0EoSCMotLVxOR+XoqbpQtBRZkVLir8nv8gFStyWtozTgp6N87VFd99xTx51krZT1I6K4OQLKGw9vNOdHRJbxUeeZ0Vwbd44RHgwkhP3wa0DoTdIcikeii5v2PxYbq9IaloTZfZdZzoEjYVTd3mSaEyUUy4RQMFBAh9rNZzVyZcvrHtuCeTlGGJBClmZzvX1arf7oqXOjI6Sp429pyqEIoxb+Ukngosh2oVTWhfOHVf3oFMvJ1n5ato97QfOp0CUBFX49NMarzFHLzbQMVst9ZXr7aTgNICcVc8a0x6J6c314NxJRf6zJXe3ccXsIyfjeAEBsM5Qu8u2ilbWIrk1pdyknMuJryqC5tcfo8FD+CPYdFh3YMuuOuCImqSOU+bkZgkgTpiykqY3YpEZ1OejMSVd7kjjzxwyt2bUFDNKSf/jimoAjTqywHr6/mVzgun+IGaDyG2Vty7/FWq8HFhesnwXqXexriQFYA1gWhwi/xEVjfY5qPXGKXqGx6vewHzZfhwdawBCLowh6b4uMgKy+3bUdGSjM33ZGiKi0xqSaFSxIrEciuww63zcmgkylzQR47Hp4+IesHPwFUhsFM1T/LqsIpakWdi5+88pB9k8Ten3u/yjAJjl2qRXV5Ljks7zMuqgZaaFutW0sjAqgZQcK8RECHT6CtFOBSySCvOgcGebue+U4u2oa95Jm/8lnWCk/FPxS0lRRLxbPvwSObYI7qTMkpz7VT8xd5EOb/C2wTZIOCUNab4SDnev5wH+kd189usbwCJgMGoKqzFI0/J0XtS89nI6zz77ekoiPBw3Iy4drTCU8rm0t+hckjvFVMSkBPxA4wOoeVqUT84NDxvVpyWSay/jdVyByZClmtT1iWbj9Y6uGzFben7WJgboiZTPN/oKejhZyIwfFFgus93JfFacs7e/0Acfa5ucaAY21RL+ZpQa8xwvvnPjmitkyiJgnRHw==", "layer_level": 1}, {"id": "86fb1ab3-0d9d-4f57-bdd1-151e7cd91e0e", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Ranking Algorithm", "description": "ranking-algorithm", "prompt": "Create comprehensive content for the domainr ranking algorithm. Explain the purpose of the weighted scoring system that evaluates domains based on performance (25%), security (20%), SEO (20%), technical (15%), and backlinks (20%). Document the implementation details of the CompositeRanker, RankingCalculator, and RankingUpdateService. Include both conceptual overviews for beginners and technical details for experienced developers. Use terminology consistent with the codebase. Provide practical examples of how rankings are calculated and updated. Include diagrams illustrating the scoring algorithm and data flow. Address performance considerations and optimization strategies for large-scale ranking calculations.", "parent_id": "", "order": 7, "progress_status": "completed", "dependent_files": "services/worker/src/ranking/CompositeRanker.ts,services/worker/src/ranking/RankingCalculator.ts,services/worker/src/ranking/RankingUpdateService.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:36:59.656749+03:00", "gmt_modified": "2025-09-15T21:45:39.489343+03:00", "raw_data": "WikiEncrypted:x7xafZBxVSAVM+m13jmM+9BiPMeEd86avCq/tuWD+yBeP/UYFMm0TUafierJ+M5YkZjPUvU2kkw7ZJ8Csneq29H/PkWGync3xqjGe1eQzs5uTGlAnlnziUn6xqtSK0DCeFq3EI49FwLOdmsy9oh8+xicG84yPhWM4iUVQvfsZGCB+NvFPbkdGb6FoNw46sSFnacbFUyyOsyVGT7XS6m1e13zl/OAcNMp49LarFQ7rHlN2VIjy2cKob4Gc7DSOkX0Fm96yl0fhW4bjYPpJoHgWuKo9wFx48DgzAWdCICGPOLuLqUOtEo56FviNK8/CX9JtC/qz8FQ5+OnJPnGi4O1F6oRDXwzWU/NcB2oqmOUsqP9Z5jvVTzc8Or9KPyt2CHYfzk9/4G3d7E3OvdIodphdd9BAv4wvXAwgJGutAHZIKN8PQQgXtIX0ojBGiVMmZYFK5L68DtEA7MDNMLN9tf8vi18Qml9vs3WXY/XE2nkNu/qnQ9fFUjE/2JhjNqbTac98AvnjB5L3S2fe7+RcfMv7zZdVJpoHp/dt7Ubp5EmnIhYsNSlxjmfxMtfBcfLrfGQXq0lGkXA2oAlvb0rrniAEcARA8vpoCnL7DvBrhD2XZi28692yn85mHFMaZXSqfVYcdlNpXrqmERHiV9u+z60Z67Ps6oUJ+t6FV5eyTYJIa8kSbDIx0w5Be2ppu+ZnTVfnfHjIl+DgRLnqcGgacx4w2RqNxnI52iHjJZchBR6CiBPPri5ocJ1d6vBXuyhTcALOgWrgKHKKVmBPxSkTo9WxbqHb4Pt8CYvy3VzfYDUwFIXCOHcrtrVa3/EiZeJRC4aSllEDR+LqYk2Z6naBvoTaVAfYWN/0pQQQ/d1vYf8Efdk+jLFR6tpY9VTk6z+wiTryUXbvJ8kimu2Ki5NuWgnDP5kW1kq/FQateBhDJuf4TMQlUASK9bOqjocVudip5ps1xftRl1DT3zY0KhwDU/TiCHIeDSDoIkDT4YTVZsC1FPpwN1LU6n15AGxHT+0p0pDWjzFeDz9wOcGdV1PB00AWoH/h2y35qJ54xYQ9pbwpp0br/E6+mS8FaSLtf07xDuiskqOvsCxwCht+9XhhgB0mENwZLykxKUpt/mZ11hk1SeADiovIr8XlJoF+GF6O6txsD9UsCiBwFF1i5wlq87z2rQnozg4xwtnMBMU6JDeIv6dTLucnZgrfEtonbkFTBZGzht3byiEFzUfq+M3kTTgr1vyXCEDmzkdCrn8SvrT8qnDpuM2FjnA5TmYbviqBzBMpyO3xh/sfNsjDSFD/ExLLLkIrBkiyiUhMmZpUfqhrRnhSBmPXUkmbHPkSXlT0jz2x0RY5Kgr42C4PUK4pipPEACcuVtqJg6BRgUZlweFIO8zgHfYeyKmXPHJman1qgYu2ktmkmVR/xBjB9aw2KGs/XTsOtgwX6ptlRt64+mNKFx+cvC8uHpBNsKkru1cPl9ZD+7+SIg1RwsxmNvRQOax/SQ3peuPJg0gVmS2T5I5zXc=", "layer_level": 0}, {"id": "ddd20eff-e57b-4e7e-8c38-46b85ba85130", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Domain Discovery", "description": "domain-discovery", "prompt": "Develop detailed content for the domainr domain discovery system. Thoroughly explain implementation details, invocation relationships, interfaces, domain models, and usage patterns for discovery strategies and source connectors. Include concrete examples from the actual codebase such as the CZDSConnector, CommonCrawlConnector, PIRConnector, RadarConnector, SonarConnector, TrancoConnector, and UmbrellaConnector. Document configuration options, parameters, and return values. Explain relationships with other components. Address common issues and their solutions. Make content accessible to beginners while providing sufficient technical depth for experienced developers. Include information about rate limiting and reliability mechanisms.", "parent_id": "", "order": 8, "progress_status": "completed", "dependent_files": "services/domain-seeder/src/discovery/strategies,services/domain-seeder/src/connectors,services/domain-seeder/src/ratelimiting", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:36:59.657266+03:00", "gmt_modified": "2025-09-15T21:47:30.289041+03:00", "raw_data": "WikiEncrypted:JLXqlo28VAn5UOPLsLiQgTPNvb2bfmAjUW4DK8VuFzm/ggJ6gKjkCDcG7N/s3QPI3CRNMnuTA5knEFC+7jzxfX8NNNau27/IpBAsmsEC8p9o3MdETV74cN3WtEYaeIURt7MJwxzVBnife68cMWG9YOCKjpRK5rZYCG3PaV/y/TKIfIHS9uMXTLBx0VagZkNQ/TQz1wf/VhUbmzMMb8KWoFN80XzxZSkfrun/LDwK3eDyrAsVFPBg/S8B7UTBW4K5YT54XqZdhR9ce0kgyQT9q/D96z2iLon0lkLfUZcKrhG7UQsuApitWg9pWihdFXbIjrH1KxrK+lk+7zpV6ZMeILl3BAzXTY8hFrMkAHNdcbR8tg2nXRxJP0UvTQ8WAzyYCmGdhYYRwkUREcd0T0EFRWWZsDXmc6/QGRCLtnYW4BGF0ZzpVawLQTCfnoRDlxcDPPvXD/Hagy8oADbZik4NQHUKEx30/E1DmQZIrxGG586fy1nyhOBc8q2npsgF4q8HVkP3thGLNsZIg8uZUP/Gjyu8avHAnhvl7Bt9MwbOlcCOCpt2JIXilcYIHWRuwIerkMQJTT0h3ubiTg/ovWRrQk+0hBONny+QLtLYknNQi7S11wMR3hr1nq4b6V56TTDXwuGeV84Q+Iyx+SNbcmCernqnFSUSTvvjDX/rNgCncL3/466+LaNb19pBsWGGRbrWJ6CohaALHVC8kJCr+cyjcKQ7gGOJW8Ek9fTXgmnsDYagfZcBLoIJRBaatBy1Bbn1vkye3MOA/JQ66Gjy8jskqs9YX5wwXeU2etjLTID78wc4PsH2w0SzX9l98RG2DVwde/gEbv9Q9ynAdLFy1sMhFMcD3XKBl4qxkfZsiO3NZGlmS50RAv+OU4JgmH/j5HGpci8btzL7kGTalvYTm40KbTXF8fEC8Z7pZHgx/pUwPiYtQGWWoDsMFId1AnJGI1Frfn4JO1earJ7pPmUb72rmWiZC7SENa2qZ5bzPjkEj7UcRO4ACdspgZXtc48mmxo/7KS9NCNec01ppZqZojNAuiazskqAT/EVmwuFWW+1Eb/ZsTopO5yja7g0twTWuymJ4dfHxkPevCtFH/8A289dT+eSuzHpl9YacqVN/x0xU0MdZGad2IIjLbvZnRaM4s/GiOw2h34dtSnoyui1ylKzxUaddOfu1qXdcGip6RhuchuNx1inypKRgZYISKuX8MHFtGpUXVFKQaIQ502om39V0p3ciY8zDTlDGEUqnj/La4fhnDrU+l3QRfYZ+ntZ8zpaBG2PtELND4qCYCEodnSLKF0+5QF0CWg6ve9F8WvHwRrtx2MCOuboXauanjBfMhj6+oUuN9cQKwZBZiFcYAYyOEWlnibgIM5VgMwjCVVLfCUKiMuIkw9ab8HTkEqpM2YT/ssusfGEAOmlvn5QW7dSi+TPmKm0GTxHKJoYwJ+08YgAaxcy6MbIbtTSrBay8S1DcLZrt0yEGSZLCWNNVb+2Y6KDL6tW0oSxMkHiRNm9rNm4=", "layer_level": 0}, {"id": "71a79def-fdc0-4d40-80ea-b74ed07e1196", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Data Processing Pipeline", "description": "data-processing-pipeline", "prompt": "Create architectural documentation for the domainr data processing pipeline. Describe the high-level design of the crawler architecture, data validation, normalization, and indexing processes. Document the component interactions, data flows, and integration patterns. Explain technical decisions, trade-offs, and constraints in the pipeline design. Include infrastructure requirements, scalability considerations, and performance optimization strategies. Provide system context diagrams and component breakdowns. Address cross-cutting concerns like error handling, retry mechanisms, and data consistency. Document technology stack, third-party dependencies, and version compatibility for each stage of the pipeline.", "parent_id": "", "order": 9, "progress_status": "completed", "dependent_files": "services/worker/src/crawler,services/worker/src/validation,services/worker/src/normalization,services/worker/src/indexing", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:36:59.657985+03:00", "gmt_modified": "2025-09-15T21:48:05.811103+03:00", "raw_data": "WikiEncrypted:YZI97ep9QXDPfRNDXs4+JObG+Dg6NPCOcIUuLSCprtO9Uk9Wrd6CAS2kEidhbu1dh6n7nXilfsWqF2Sq6o9dk1zbvLeSxD10/mX1sdWvSd4INfj3EJEbUhUjn50L0dJnzXRBwNzPQYiqgUrG00ILEtcdr+dUiT0dUlMY5KVzU7WwALTdDuYE/SZQvHG5QTOly2YK0L7WVmQH1pwle7f7xkiePafamTd6lY/p/hK+YE9IdpGstgJxAAduYyEaq4K38zIE95ovDQ1cYRnaJQ+v9nKQ/GXDwjIkycajh+yM/d8XJm/ZEwVFFO5G2b+9RJd908gLxa3+9EIbmZFDUsusxUIisBMKV488En4v0RlgJGKotNGKeDI6FsloFUCNGDn61yA+w0y+RDVfp7Qil1uTGe00KNGeTee5wgO3lDHAak+SjcC4hjW9H+qh7uq736YNxurWhw7Wj7VEdslf+F+wKMDANGX+pOhiak7xbEVJM64fbGTPt31VDV6VEFTyRRosn1U027RPEEJetBOLgZplWIsTx7KUF90jxFx+sXmQVa/kdQGHWO8GWkRq6T6AjlBPLvkE457rbNfIgRcv1PppZhvIxcyGW4zMRLsbAc27ZXXechE/XPtmNVdF3qLasgQdSi7qCrj7i/mEPnpi6rU9lmLuVloeTbVWeqAVGHAQP833hszj7VRT4augHnCMXtIGIjrxpIUfmmKoOuwJ6qwD07UxVqsV1mR0qG6xiP/ZGdsqH2LCioiboLtK6mFcuz2OG/Hn+Mlce7gvxzf4NT6AkkQQERn1Yzb7n3Qb8qmDaevzBuNYZIAVQay9QGoEkm5vxHTJREGYpeX2p6YI3kF7t7rVZMFHdojb7glJen01yEGDJa+w1UPSm9pCho9+xzjXYlNnYZuBXZFJx5h+CcWugkQHT55XtLgijcKuxVyLN3ILlf1hLCAzcqI2pN+WULHmMpyyHEmT0Ppb9eNZx3kFo+Xkh6Wl3GxGej137FzjMGDOPgKiMJgT/cwyM56YPfK1Hum9+W4Vnu53y+tkJ9dv3xRIVIEER1sygDOuNOPaWZQ8sesVFtqkXKGnwNqMMRaKlruIxM5ds+pSNsj/a1GIAbroAu46EDZ/QoODiiYrMrFNziEe/Sc+ncXY8T+LFJPnCkBlH4CX21qVH5gYA6g/ub0XWF99DNCIW8YJMlDB1OossBJGIsKYX+EdMTGk3rRddD+qDPDdHacv7hYkM3IQQPd/c8cWY0SwoZpgwEYEtnX5sOE/t8h1nKZjZmgUth3BZA5LMrVfQFUgHlnvV3yIuAs9hfRasZmtGaxq7AinZIr8XZkR2HyqSa+AUeuh398TcDNnGnv5eab+Y9wwDbAjgpRLK0aAdG/nFEXBzivpcBeDgcKTvWXqT7hPu57qpGT9wSnR/wFEYgiVRzWuz2rhKaEdEHQmWEswWnyS9C2uRJX38ZW6maCyW49qrNqYsQtIgyerag562tVvhrkYvE/Itw==", "layer_level": 0}, {"id": "08f81c55-d7e0-42f8-bc2a-4f4e49b6064d", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Monitoring & Alerting", "description": "monitoring-alerting", "prompt": "Create comprehensive content for the domainr monitoring and alerting system. Explain the purpose and configuration of Prometheus metrics collection, Grafana dashboards, and alert rules. Document the health check endpoints, performance monitoring, and error reporting mechanisms across all services. Include both conceptual overviews for beginners and technical details for experienced developers. Use terminology consistent with the codebase. Provide practical examples of monitoring setup and alert configuration. Include diagrams illustrating the monitoring architecture and data flow. Address performance considerations and optimization strategies for monitoring at scale.", "parent_id": "", "order": 10, "progress_status": "completed", "dependent_files": "monitoring/prometheus.yml,services/admin/monitoring,services/worker/monitoring,services/domain-seeder/monitoring", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:36:59.658962+03:00", "gmt_modified": "2025-09-15T21:49:03.434195+03:00", "raw_data": "WikiEncrypted:HL3VqGjXq8A3aGeLxAjVPoyDIjn9turWnP2eTLJ4ScM6ARLr8S2dOApt77RZPM79fU1dSKfGSAe9QkcwJVAa71Ib+NrFz1YLdokhQxHUvuu+jwdMPrvF42e361uwHsek2JRYkeuSjLqu21TWpKORhvtm4a0Iopy/3l5h0k5cyeLIH1rMdl82ZogPdQHvYp1apHGYheNqQCcp4yMEfZx07axU1Qb2i8j3EP35HJx7B3DzB9U5nXdXDLqlGXoZLzSfRPAUpA5RXYc1ERtkTmW3FnrM6GvYEaIvt8N01Iuc03cCHM+mjqaKbKVA+3wVXUlecqp4bVqTtCC2W3IHrZymwOdBOOaik4fL/uPuoUv9Fk/ybw/TiwfcC48HjO3HcjHNUUKtnD8XR4EApLTyUpHp/0uqOQUUXM6bXbUnfDAv+B/bt88i5B5iPJLAPQ8oE++99NDmyvOfY1I9rMxvnpJ5Qllm+Uqp4bCzK8vOj9ChuFchH+bVa5AWIl+NIad+mtDNQYGoHXGsRh2pmtyfzowMuAx+Z00nzih95Mkqatk0C1TwAtLA1mR9kbfdz4FnTQC2q7bD0wWiLSqRHY4jZWNwDLV3Iav4dIrr2AwACbUWNNysK1sQXb2y9bGk/iiYYoB+AJsFpJKMPlPxN52pADnhAWjYyZR6BrbiDToyfC0fIkj5vdR4JDzRt1+In+fTRTd7HgVwcOVPoDWEUdeF5PkdZwgCsyzn4y5OMjbbL43f0dPVODr28DV6fYGMbCUJWgVCsv7BcAuB1ZG+IKZk8iz4tsLfWMBVxAkJK21sieO8TUh41Jwkno5BlOhmDtQc3rEUDV7KcemdAKeC5CTyvV6TY9nwuoWzoJqduCxvt0rCPxMzmAa4ADLCtogOwQbwuU9sK++6Dny0SalxBmkxkEfIvx04vJvn1dniDqD0GUySnbKvtj8NaqFpFrIv5e2twzolJVSrHSnkl+sRzYq711dIrw1Y+os5x0cYKlpM2+ifN1Bu1GFNbG0Qy5zpBV5TzXJeuxucp4V8zMOHLsY06G3smCz4F1ebaxVIrJUHX52v7PrhyHfRLBaRRteNITmR/Ps9rXL0eD36P7/5yXqCAieAR6TfrI5pmh2rEABXC1PLdMCU0h6/1H3p6rHE6smgj21Q9ssFLKiLdeOHfhZ3NywrqYh0AvpcZ5gBz4J4ZklqJwya1WthJgA1wRQjQENYIG69cZdhzL/rTh+jOrfRvk5RhJsBxYGn9UxQNl8SRvDjmSWCjgNHHRCcHTmOvHNw87hDh3mbd+QCdo8kHu81ZFf68hfhJuBeuqXLIucwsttKaxaASyWdjjEn/8vfvEFc2gCl1k9RUltKbiS4yzarVs6XhZ+/yLyNa0cbYxDogOsHufzUSvUohTQFiJhG/JXlPrwqk1oF4v4aPkq9OaPf6WAGjA==", "layer_level": 0}, {"id": "d162c57f-e749-45c4-b3a6-43a7d4a45c2b", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Deployment Guide", "description": "deployment-guide", "prompt": "Create comprehensive content for the domainr deployment guide. Explain development setup, production deployment, Docker configuration, and environment variables. Document the different docker-compose configurations for development, production, and testing environments. Include step-by-step instructions for deploying each service and the complete system. Provide both conceptual overviews for beginners and technical details for experienced developers. Use terminology consistent with the codebase. Provide practical examples of deployment scenarios and common issues. Include diagrams illustrating the deployment topology and service interactions. Address performance considerations and optimization strategies for different deployment environments.", "parent_id": "", "order": 11, "progress_status": "completed", "dependent_files": "docker-compose.yml,docker-compose.dev.yml,scripts/deploy.sh,.env.example", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:36:59.660231+03:00", "gmt_modified": "2025-09-15T21:50:01.08167+03:00", "raw_data": "WikiEncrypted:0IKfLNOWe9mZfG1jVts3wxq1Ie83+Lf6EMuycQSN7s1ZiC4k+Re4cTo/Hl4M5+hO43KrSa+zQXKAvcopQVmlNX6ZXzByGFqKNH8HXx5eluMDgXvXiosZ67YARoEqb4uvK3KelUF7THkE8lJUATapTQu5GkdgHg8I2QiiOaJvGOKPNPxvijtsqAbR/ThSGCZgkCFVUK4EJqKpumLuO3GuWP83ZYSudLZc0SEKcUnKQ7nda+oJsGwTrEO5fedJnM08Ki9XQULSwdiGjz27KGN3CrBmS7KTcBtaERTKxwLvSWYVnt3anfXs3O6IgYFvLMF2Cy9TtS6vPWy2k6USCC+oONEXBz53vr5j7wqvQNFz6MC/Gi0LuqBuzangNlqV0DiFKaUnSOkYNq9TN7Rma/ZPljI3k8TmRGXbNgdLOeF4bricx7tIxDYFxKw33PuRv72g7THzA5nx9sQMBb/9XmpJYjJXjaLSMr7+fdgzLROspVXBd8yjbz5Co/L9AXdZFV+jx/8hRz88qK1f92YfxGY0bNs21usiKj3xkM/2wfAJ3NIjBE0tm4xWo7NxWKBzS4X5BJLvL0cd93+uWescjN15gSF25uc9K1NrC07lBrmbIg9mbn9Rvtk6/+IKIe5VC7Vnmt+9KymVbwBpJ9LhHgvnWGYf7aQ+VVCoQYFeRZ/yXmIMrFD+bJI6/WjJXOLLxy0DwJdyPkWN5zJ5ue2MdbzgSMqAKM3Ii7Rs054YuUz6vTX82cYi8AlSI2kQqUWwEwj8WzYQBR9G8hsJ/H1qg+xrhKDMr8UU3Stao2yENiJNZw7G0VrBCE4VWdBCJB0WT3r2v93EhKSme8DPI879eULenh3W30mtRcspZivpkngXXa+o1G8AkBoU3iw1temGC3zoBk8O6GcPNxok3dJg9WO0RUY6S4EKvVHVBOI8Kw8shs2fPi/dVw0gv+r66Ib/ILrq2Cs1cwEianstDqBvtb1l8i5LfgL2w8duvw4jgpNzwfvFLLYizLeuS3j27UMaGZT526i9JIsCxFkZY1BRbRcOnQHVQn/UYshUdn1iAC2gDMCr6gv0ARa3l/WlBpRk3A59WPtnkz60eN416DBjyFQJOtEEGxVQ3ydGm41DtzNO7WNCRQgB0mMhAEsUXSoc7oaslkVhegt3AmNPHCl2KixQ5AfmVmunlNf5qdOCp7gDcackSAtQs6b5Vom8DPegbi27R3Pp/UeHKlNwKAB4IF9i2zfdRk05cu1JQjZuepQLGpjUUAkZfECiqV9sw/2odd2YPQn1pMOYq6lLDo9Dk/C4SVDkjVELW8+53inyfmO0M7t/3aFrPNRRYwbqyVyIg6bV3vhi9I1dm5P+idl3fY/nX9wzPEiRP3LYW6oz923ckStl+R2SZXG/CD0NY+mQyYfkpacNSVkCFhgIjpRt8RdOcDf7NH/Y1UYaOmW919E8UsM=", "layer_level": 0}, {"id": "5ec7435b-599e-4858-8bb8-f0eef69cf07d", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Testing Strategy", "description": "testing-strategy", "prompt": "Create comprehensive content for the domainr testing strategy. Explain the unit testing, integration testing, end-to-end testing, performance testing, and chaos testing approaches used in the codebase. Document the testing frameworks (<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>) and their configuration. Include both conceptual overviews for beginners and technical details for experienced developers. Use terminology consistent with the codebase. Provide practical examples of test implementations from the actual codebase. Include diagrams illustrating the testing pyramid and test coverage. Address performance considerations and optimization strategies for test execution and maintenance.", "parent_id": "", "order": 12, "progress_status": "completed", "dependent_files": "services/admin/tests,services/domain-seeder/tests,services/web-app/__tests__,services/worker/src/__tests__,shared/src/__tests__", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:36:59.660805+03:00", "gmt_modified": "2025-09-15T21:50:34.625889+03:00", "raw_data": "WikiEncrypted:a51tw9+B5Xez88YuZqi4cPV0J1CJauHTZselAZQ9sG40Lb0MosJIfbxZqj+gJA5m8musMdI2GJsw80+O72ORSZ9xe/Qru47L+mr2CUibMYC9L9lXu0eUCU1GL6kAjVG1fiGtIg+6MXy9CT/8/N3g2d/WjZ18U05HQ5bFefGJ5HGDQOK9j8YSqDP4eECgpXee8d5Kl94v9HDs6wuBvr8nudXF2HJ7mD3a0NJp2b8mLAHXQDKz4gGvEXGLTzcYS2jhF3xz2ehvuL7i1EkBqdaZEYHhbWSYQf3duvrMEF0fJaoSHi0pldmzwGXnZhV2WeH0Flu+Lf8P2+poaHTcLUI6YSIgLBe3oA8XH1F9rWNXvE2WPKKtl6xDHjbI9ZLS7mauggN/e8Vp4Bup9vnVv8jb/8oFEhA7pMLaY4P8u5gonfcV/B/VPkT5rbfKNcf8PkSikBMsPWTy3TI5nYoglfj/U4lk05C9Oz+VhWybeu/ipK6jMeEqVaHlopa/6dgDz+MRhIPQxsnXAt5NrcS/anMjAPfsWEQp1dV9XQUhIWXOLLsibFzNTM0Ddu6B+ICR1ZuTnZ9xFDGc3rsx/4Gdkp6P2QIHmq1AqnIhD7PePTwftRN2x/JqG/qECNvC0cc3+uytXg/RWNtJL9+RWS/5ar37Sg3Du0NgcIiGd9rumaJDxDyu2Ks2k6S3ODxHtc93dACJDnNk+juw4WMzlfts7g4FV4Fl6jWovmeJw+0iDIEd3bHdbgiWU/oi2usiNiGO6QPNBAzA47DcD3u1pVX6j41cT6z5McO5efrlrIXGwezpaNd1LoqMoYs147tksZcdecVWFYYApgj+5IfOnUvQdCZKc5/eZI8G1n6If2qzJ1c7xciWsJ3JQhxVqPF1AhRJEAZIKV+fdXp6jZxLPvvGszz6rI9Vfc+EeMYG+luMcotj4O3TnInIVnJupXc6QjuYFGQPCB/95zFzh1yUmxcIaJmHiWMR/MLcP7mpLmmxSQbgVvz02ieJEXL9YxsR/Fi1iRHD1G9USD60m19KdlaRgC8euud9bTIwAdbdZGtEVSF3mL0J3PXMgizEmlW9RHmpdDJwVpC15JniL2sJnn/D16mvI5vWlPcyPuTMxG71qc258u0SJuxukJLDBxj1V8tUF8H2pVKFtd5O+zRqzbeAU7fzqWEaWpduCFzapYSteBtBECLD9D2mDJPK0ESbRLNJrqG0dli9fRh9AfA03UXFd7Ch9sZCsbMJ3prN/060iN9MVaONBcCKLiotyVOtDqtFl1Sk5TzMsqCNxjosmLBxvkc+tATaPhjuOIr2h1Ly9T8Z+DEfXfHtQYHivZV7ZCx32OJYohaZSFto/trnigTjT0hs0UW0IiUFaQf703vTmzztT5MoxU+47b2GoyyyNgHVSnuuIzCmPUKN/KgozD4Pnt12hw==", "layer_level": 0}, {"id": "49fc82e5-b6fb-4e5c-bd46-022c8a79d927", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "name": "Developer Guide", "description": "developer-guide", "prompt": "Create comprehensive content for the domainr developer guide. Explain contribution guidelines, code quality standards, debugging tips, and performance optimization techniques. Document the development workflow, pull request process, and code review standards. Include both conceptual overviews for beginners and technical details for experienced developers. Use terminology consistent with the codebase. Provide practical examples of development tasks and common issues. Include guidelines for writing tests, debugging complex issues, and optimizing performance. Address best practices for working with the monorepo structure and microservices architecture.", "parent_id": "", "order": 13, "progress_status": "completed", "dependent_files": "CONTRIBUTING.md,CODE_OF_CONDUCT.md,services/admin/DEPLOYMENT.md,services/admin/TESTING.md", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-15T21:36:59.661721+03:00", "gmt_modified": "2025-09-15T21:52:53.689634+03:00", "raw_data": "WikiEncrypted:F3QgleoEfoy16cQggYe9C5IaSqlGa9MAZLOFAWytoRNMybnI1ljYEEif5toDwhEQ3OcIFU7EdxlfsxvLQBZG13lZztEKWNkkflI1H+gM378+eRbhT6jzJvkpWLDBLtnP6qiq5KGmegPdf3unc/8XJ4h3cUf4hOI4Y8DjE3nYjXOMNPuDcvWGgMGefjQLZ44qOalcz5XUzO9m1fuzASibdQy5weIvEa46rECq2x2SlATJ/dZw90iClLfQ4LrcINtIajNG75+QT6Fhe/wQ4jnTOqH94RvH7zSDH4+o+CZCR0sjlWtksgha2bJBoJTklJWPULegYxnOLGw9vx1fIsfDhbugNJsF0Xa5lzlb7B3XVZ7fTNfNYLLNp53prvRnDg1yh8TLM7e6TmtNd2P9j/gtSp49622Pxi/VohFEkoZuMLRkKq9DRv6B1LYcRN4X9QRY8Du4LD/gQtHS32AT0+zHFKNugvo8oy9rWuRk2cJ/2SK5CsQyyq1yqO35Xx2PoLLF54sQd1KMRy8osNxVhIgEljHOBRxpo5A9secUtQrbQn5beZWsb9rI8ZebaV+TGr/yIZoAaN1qNiFCreII/vSKOjdI/YDqqakNw2a3g8y02hR1NlWmD+zUBVa/s+4JS3iPE93mMPOQHSFuySpcmZhrpv/TvbhhIJv9I7hIUoTUirJVNs6WBYYqmQcDtOwoiCyfH1kJ+dvb4b5SFiAJvIbsJp2tp2UjUJzmONkG11O1hhDd292iE6iNbmuQW5IdIzsIuBgtuF22J/+zeBSbAe2Lj/DkW7B1WlqZ8KUHfv0g+CLqosRgTqtiRg1MbfyZuJtqlBoCRyLV09ycPnlKGRIC8AVAiUgDyjWeslDfgNJ73roy6BhoYKjxPeAI3QLSqfNNgU5WxyKcIU4LcKVmVrF2INUukzB+eSGgTUXH5VP4JyFKq7mhoe5MXHgOYqdDKkPuYZx3to/uvW+33Fn6pRL2jZP6QskeFRjQRF5Iu3BvqPSoCG3dnGgrj29Sl1vnfOFT7yBCP10ZNc36djgwrFErmXzLR1Lxwwsp9SgBCErn+QfIvYSpHgHw2rrKMGV9zJJGRJnO/RbuIvS7aPtmAXrHBpglDSL/HEBcy0gUTdDbZLOfE014Ntm33NewoN6zCR521JAWUR7iqtSPrdeq6fyO0A0FmY38kYWi3Tc4aMHFBiH7MLyKvc37gPMRwx0p7ra29/XTkBnHsYWW1jS4TXJEu4FD51lChjlaUOyii0qE37Igq34wo9MZNulK+uhWR2/ouScBcZTTi8VWInyIzk83HXNrR+pK/FQKYC+RwTS8CJ/0XdQOK6oaqZx5tANlDHPsvcxkqNvyevaOSmEWyqwRO+uU1CKDdFsFI73amdVKOZQ=", "layer_level": 0}], "wiki_items": [{"catalog_id": "23e969fd-eca8-41bb-bb1a-d40f9f9756fb", "content": "", "title": "System Overview", "description": "system-overview", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "dc0bf21b-7d64-4e9b-9142-7594608c3766", "gmt_create": "2025-09-15T21:40:47.032559+03:00", "gmt_modified": "2025-09-15T21:40:47.036261+03:00"}, {"catalog_id": "4a375289-bde2-4aa5-8c8a-8321983ef3ad", "content": "", "title": "Technology Stack", "description": "technology-stack", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "f0d8e91a-3ae4-4252-8d8e-8a35fdf84bf0", "gmt_create": "2025-09-15T21:40:52.549155+03:00", "gmt_modified": "2025-09-15T21:40:52.553031+03:00"}, {"catalog_id": "2fd75159-4155-48d5-b146-4fd895ab5d86", "content": "", "title": "Service Architecture", "description": "service-architecture", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "e19b965d-a436-40f1-8709-c1a266dd1b37", "gmt_create": "2025-09-15T21:42:00.074661+03:00", "gmt_modified": "2025-09-15T21:42:00.078151+03:00"}, {"catalog_id": "d97bcd15-136a-470c-879e-3f2704b95035", "content": "", "title": "Directory Structure Breakdown", "description": "directory-structure", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "0b51320a-2070-45cb-beec-3491ead00752", "gmt_create": "2025-09-15T21:42:54.017225+03:00", "gmt_modified": "2025-09-15T21:42:54.023401+03:00"}, {"catalog_id": "54aacf35-eba1-4936-90c8-2768157d3278", "content": "", "title": "Data Models & Database Schema", "description": "data-models", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "999507f4-1d05-41c5-9d73-d4891bdb40ec", "gmt_create": "2025-09-15T21:43:10.768375+03:00", "gmt_modified": "2025-09-15T21:43:10.779792+03:00"}, {"catalog_id": "4cc1d72f-ebfb-4439-994d-803e620048d9", "content": "", "title": "API Endpoints Reference", "description": "api-reference", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "e38f1904-6c2c-462e-9379-13baf7db544f", "gmt_create": "2025-09-15T21:44:00.802228+03:00", "gmt_modified": "2025-09-15T21:44:00.807489+03:00"}, {"catalog_id": "86fb1ab3-0d9d-4f57-bdd1-151e7cd91e0e", "content": "", "title": "Ranking Algorithm", "description": "ranking-algorithm", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "d7a22420-208c-486f-9902-736f90733e5a", "gmt_create": "2025-09-15T21:45:39.48496+03:00", "gmt_modified": "2025-09-15T21:45:39.48977+03:00"}, {"catalog_id": "e3ce4565-5d2d-4e25-9f46-14b64749ee98", "content": "", "title": "Core Features", "description": "core-features", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "fc2bc0dd-aa39-4407-960b-ccfa0cedf7a2", "gmt_create": "2025-09-15T21:46:07.701031+03:00", "gmt_modified": "2025-09-15T21:46:07.706808+03:00"}, {"catalog_id": "ddd20eff-e57b-4e7e-8c38-46b85ba85130", "content": "", "title": "Domain Discovery", "description": "domain-discovery", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "41455cce-17e2-4257-a1e5-127134ddca51", "gmt_create": "2025-09-15T21:47:30.284148+03:00", "gmt_modified": "2025-09-15T21:47:30.290046+03:00"}, {"catalog_id": "71a79def-fdc0-4d40-80ea-b74ed07e1196", "content": "", "title": "Data Processing Pipeline", "description": "data-processing-pipeline", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "a06c97bf-0d70-40ed-819b-a4df8a9e7407", "gmt_create": "2025-09-15T21:48:05.805594+03:00", "gmt_modified": "2025-09-15T21:48:05.811934+03:00"}, {"catalog_id": "08f81c55-d7e0-42f8-bc2a-4f4e49b6064d", "content": "", "title": "Monitoring & Alerting", "description": "monitoring-alerting", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "b226883e-29e2-475a-a248-a11575b7c540", "gmt_create": "2025-09-15T21:49:03.427535+03:00", "gmt_modified": "2025-09-15T21:49:03.434913+03:00"}, {"catalog_id": "d162c57f-e749-45c4-b3a6-43a7d4a45c2b", "content": "", "title": "Deployment Guide", "description": "deployment-guide", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "c7fdf3f0-9a8a-4232-8504-1074f8978dd0", "gmt_create": "2025-09-15T21:50:01.078077+03:00", "gmt_modified": "2025-09-15T21:50:01.081938+03:00"}, {"catalog_id": "5ec7435b-599e-4858-8bb8-f0eef69cf07d", "content": "", "title": "Testing Strategy", "description": "testing-strategy", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "90cccf9d-3bc3-4947-8701-76745cf4e7c3", "gmt_create": "2025-09-15T21:50:34.620185+03:00", "gmt_modified": "2025-09-15T21:50:34.626551+03:00"}, {"catalog_id": "6a6a66a3-4597-423f-a04c-990680971ad8", "content": "", "title": "Web Application", "description": "web-application", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "df0d55ba-fd8d-495c-af3f-d408e5af42e0", "gmt_create": "2025-09-15T21:51:58.044471+03:00", "gmt_modified": "2025-09-15T21:51:58.054324+03:00"}, {"catalog_id": "49fc82e5-b6fb-4e5c-bd46-022c8a79d927", "content": "", "title": "Developer Guide", "description": "developer-guide", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "d438de05-2541-4fdd-9f27-f9ebabc146a8", "gmt_create": "2025-09-15T21:52:53.682422+03:00", "gmt_modified": "2025-09-15T21:52:53.689949+03:00"}, {"catalog_id": "9deb6e28-4945-403f-bbf8-ccc5b77acb6c", "content": "", "title": "DomainAnalysis Model", "description": "domain-analysis-model", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "80c9974c-bd62-4c1d-97a4-6c43268e5a00", "gmt_create": "2025-09-15T21:53:43.941147+03:00", "gmt_modified": "2025-09-15T21:53:43.948792+03:00"}, {"catalog_id": "faeba3f2-cfcd-4ca8-a192-3d1edb28dfcc", "content": "", "title": "Domain Search API", "description": "domain-search-api", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "a45b65e6-9391-478e-a45c-bc91d342161c", "gmt_create": "2025-09-15T21:54:05.730365+03:00", "gmt_modified": "2025-09-15T21:54:05.734085+03:00"}, {"catalog_id": "931fbe7a-d787-4335-9e72-8ce7b2ef2098", "content": "", "title": "Domain Search", "description": "domain-search", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "c0c7416d-3eaf-4e99-a5a6-b261c208b211", "gmt_create": "2025-09-15T21:55:15.473239+03:00", "gmt_modified": "2025-09-15T21:55:15.478902+03:00"}, {"catalog_id": "2ea74480-e59a-4cda-8944-b19f3a122786", "content": "", "title": "Weighted Scoring System", "description": "weighted-scoring-system", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "d2a7722c-2b9a-4a45-afea-86236fce285d", "gmt_create": "2025-09-15T21:55:38.810564+03:00", "gmt_modified": "2025-09-15T21:55:38.817105+03:00"}, {"catalog_id": "d5a967f6-64bb-44d1-9ce5-9958cd9686fe", "content": "", "title": "Crawler Architecture", "description": "crawler-architecture", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "12df9505-aeaa-45a1-a2c5-14285a457158", "gmt_create": "2025-09-15T21:57:21.625011+03:00", "gmt_modified": "2025-09-15T21:57:21.633032+03:00"}, {"catalog_id": "822ab717-f6fe-4eba-b5a7-232d7aabf8c1", "content": "", "title": "Discovery Strategies", "description": "discovery-strategies", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "8af464a1-f759-49ce-ae4c-375c288a489b", "gmt_create": "2025-09-15T21:57:34.834344+03:00", "gmt_modified": "2025-09-15T21:57:34.840065+03:00"}, {"catalog_id": "79a228e1-2d8e-40e4-a161-bbb4545a4acb", "content": "", "title": "Development Setup", "description": "development-setup", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "90d48463-c0ff-48f3-a138-9f63778550f9", "gmt_create": "2025-09-15T21:58:35.640406+03:00", "gmt_modified": "2025-09-15T21:58:35.643592+03:00"}, {"catalog_id": "b8ac8bfb-0d24-4a73-9957-c087e7f2e15c", "content": "", "title": "Unit Testing", "description": "unit-testing", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "d2a473ce-6df4-4aa7-a4c7-9238c93d1220", "gmt_create": "2025-09-15T21:58:51.394735+03:00", "gmt_modified": "2025-09-15T21:58:51.403003+03:00"}, {"catalog_id": "dd192e8d-bf7d-4707-a60a-0a3a0f6bf9db", "content": "", "title": "DomainCrawlJob Model", "description": "domain-crawl-job-model", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "559d0ee1-ecf0-4c1c-976e-26fd8543f874", "gmt_create": "2025-09-15T22:00:14.390611+03:00", "gmt_modified": "2025-09-15T22:00:14.396635+03:00"}, {"catalog_id": "43ac1b83-47dd-40d3-8d39-42e772a18e7f", "content": "", "title": "Worker Service", "description": "worker-service", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "79e52daf-6f92-4d18-a3ca-54f27db2e394", "gmt_create": "2025-09-15T22:00:58.464005+03:00", "gmt_modified": "2025-09-15T22:00:58.47203+03:00"}, {"catalog_id": "8c7c6bc3-731a-421f-8479-5be100222e11", "content": "", "title": "Domain Analysis API", "description": "domain-analysis-api", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "d28216a3-d986-4478-84a9-b69317a679d3", "gmt_create": "2025-09-15T22:01:27.763755+03:00", "gmt_modified": "2025-09-15T22:01:27.769536+03:00"}, {"catalog_id": "d14dcd20-4919-40a8-9202-80e510f7055e", "content": "", "title": "Domain Analysis", "description": "domain-analysis", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "e18543fc-6a52-4941-8271-9df2b10e1f6f", "gmt_create": "2025-09-15T22:02:12.161153+03:00", "gmt_modified": "2025-09-15T22:02:12.167486+03:00"}, {"catalog_id": "fa6eceee-9082-4792-8ab5-d938d0dc3e5c", "content": "", "title": "Performance Metrics", "description": "performance-metrics", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "5f518d15-9fda-4b51-a95b-e7b332744c16", "gmt_create": "2025-09-15T22:02:53.543071+03:00", "gmt_modified": "2025-09-15T22:02:53.55461+03:00"}, {"catalog_id": "1ceec158-3bf4-4c4c-b815-d187a629bab7", "content": "", "title": "Source Connectors", "description": "source-connectors", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "5e208e96-e17a-419a-8923-17f033a1f8a4", "gmt_create": "2025-09-15T22:04:35.732365+03:00", "gmt_modified": "2025-09-15T22:04:35.738636+03:00"}, {"catalog_id": "f6603e3e-2312-4d41-9734-79e7cc7d4afb", "content": "", "title": "Data Validation", "description": "data-validation", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "1ccde41e-2d06-4be2-8200-9107bab70f19", "gmt_create": "2025-09-15T22:06:02.781652+03:00", "gmt_modified": "2025-09-15T22:06:02.798961+03:00"}, {"catalog_id": "809af01f-3b6e-4e9d-bfe5-49007a5b9966", "content": "", "title": "Production Deployment", "description": "production-deployment", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "e8ec4183-f24b-4e33-8864-2ffc086b4235", "gmt_create": "2025-09-15T22:06:37.904458+03:00", "gmt_modified": "2025-09-15T22:06:37.919835+03:00"}, {"catalog_id": "f14449bc-69a3-4513-a50f-b634821da35f", "content": "", "title": "Integration Testing", "description": "integration-testing", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "93e06c9e-cb7e-452e-a35b-ff28d6790d09", "gmt_create": "2025-09-15T22:07:30.313953+03:00", "gmt_modified": "2025-09-15T22:07:30.320562+03:00"}, {"catalog_id": "9df73c8b-c582-44b4-90d7-c70a3062f381", "content": "", "title": "Domain Seeder", "description": "domain-seeder", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "9388a71a-461e-4668-b95b-e789f9f776dc", "gmt_create": "2025-09-15T22:08:54.206777+03:00", "gmt_modified": "2025-09-15T22:08:54.242688+03:00"}, {"catalog_id": "ba8b07d7-beb3-4d6d-a888-ce45dc86f37e", "content": "", "title": "DomainDescription Model", "description": "domain-description-model", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "81eb5d3d-6a3b-44f5-b9a2-a7a5f7f3e0a1", "gmt_create": "2025-09-15T22:09:29.369314+03:00", "gmt_modified": "2025-09-15T22:09:29.374209+03:00"}, {"catalog_id": "ad279493-c280-480e-9426-8e9dff83bddd", "content": "", "title": "Rankings API", "description": "rankings-api", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "41ff8a68-0781-40cc-bd7e-6a1feefb965d", "gmt_create": "2025-09-15T22:10:12.033316+03:00", "gmt_modified": "2025-09-15T22:10:12.037342+03:00"}, {"catalog_id": "50786d98-c54b-4450-9cfc-b00faa627a0c", "content": "", "title": "Top Domains", "description": "top-domains", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "976ecedd-de24-4f72-aec1-7428989e4e43", "gmt_create": "2025-09-15T22:11:10.039219+03:00", "gmt_modified": "2025-09-15T22:11:10.042419+03:00"}, {"catalog_id": "0dfa7ea4-bb9e-4970-8a0b-13844a4d82b5", "content": "", "title": "Security Metrics", "description": "security-metrics", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "ca8658ba-d8bf-4e86-bd3b-b3d79870ed14", "gmt_create": "2025-09-15T22:12:34.656715+03:00", "gmt_modified": "2025-09-15T22:12:34.665934+03:00"}, {"catalog_id": "bafe0343-307c-446d-a856-f7eb3ea04e8a", "content": "", "title": "Data Normalization", "description": "data-normalization", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "93034c34-d2b6-4ba3-bb0f-457cc2ca6f51", "gmt_create": "2025-09-15T22:12:41.3123+03:00", "gmt_modified": "2025-09-15T22:12:41.320042+03:00"}, {"catalog_id": "435af0ef-9921-4186-b358-2f30155aded6", "content": "", "title": "Rate Limiting & Reliability", "description": "rate-limiting-reliability", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "c1867b41-9639-4f88-b4e7-3238ae50965d", "gmt_create": "2025-09-15T22:14:03.821648+03:00", "gmt_modified": "2025-09-15T22:14:03.828182+03:00"}, {"catalog_id": "5b9541ae-7682-471c-b4aa-745a492e1dac", "content": "", "title": "Docker Configuration", "description": "docker-configuration", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "7746e968-390c-4fa4-a5da-7eb73284e37e", "gmt_create": "2025-09-15T22:14:22.545448+03:00", "gmt_modified": "2025-09-15T22:14:22.552665+03:00"}, {"catalog_id": "0ed41d92-542d-418c-88bc-e531cfe94570", "content": "", "title": "End-to-End Testing", "description": "e2e-testing", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "9b4c9f2c-e56e-40e5-b683-fcd575ef07ee", "gmt_create": "2025-09-15T22:15:46.862684+03:00", "gmt_modified": "2025-09-15T22:15:46.868657+03:00"}, {"catalog_id": "81d26d99-2470-4522-9577-46fb4bf87d99", "content": "", "title": "Admin Interface", "description": "admin-interface", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "646cf5c4-aa18-4510-9f39-7453802d6341", "gmt_create": "2025-09-15T22:16:01.752358+03:00", "gmt_modified": "2025-09-15T22:16:01.75784+03:00"}, {"catalog_id": "a2dacecd-ea2a-4671-ac7c-de5baf803dbc", "content": "", "title": "DomainRanking Model", "description": "domain-ranking-model", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "603b9b16-90c8-4c70-acbb-60e82ab498b8", "gmt_create": "2025-09-15T22:17:15.931449+03:00", "gmt_modified": "2025-09-15T22:17:15.936987+03:00"}, {"catalog_id": "61966302-93b2-4fc8-b0fc-0011ce342be0", "content": "", "title": "Admin API", "description": "admin-api", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "152b11e0-ac41-47f6-87ef-e29c511a2d00", "gmt_create": "2025-09-15T22:17:17.064625+03:00", "gmt_modified": "2025-09-15T22:17:17.069006+03:00"}, {"catalog_id": "0f222083-d810-445c-af67-e8d4ed27496d", "content": "", "title": "SEO Metrics", "description": "seo-metrics", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "88bc0bfc-2987-4172-9cf7-64d2680475c6", "gmt_create": "2025-09-15T22:18:41.547118+03:00", "gmt_modified": "2025-09-15T22:18:41.550135+03:00"}, {"catalog_id": "978c1cf5-dc9d-430b-9695-eb8a64a12160", "content": "", "title": "Domain Comparison", "description": "domain-comparison", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "b7cd3781-d68f-4613-8200-6a689bd0262d", "gmt_create": "2025-09-15T22:18:57.369092+03:00", "gmt_modified": "2025-09-15T22:18:57.374753+03:00"}, {"catalog_id": "17e04bb1-d9cf-4f21-a4f9-3e2c5ee31a58", "content": "", "title": "Indexing Process", "description": "indexing-process", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "d1060869-9e81-4c33-81a8-7a85743bb73f", "gmt_create": "2025-09-15T22:21:02.040814+03:00", "gmt_modified": "2025-09-15T22:21:02.053719+03:00"}, {"catalog_id": "7556675a-016d-4207-a449-c63e9ac57d7f", "content": "", "title": "Environment Variables", "description": "environment-variables", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "10ee392f-803f-4f53-ad7c-61ea41a490f9", "gmt_create": "2025-09-15T22:21:14.370768+03:00", "gmt_modified": "2025-09-15T22:21:14.379624+03:00"}, {"catalog_id": "d3f62451-df7a-4a95-a787-05a984a1991a", "content": "", "title": "Performance Testing", "description": "performance-testing", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "503e45d0-572f-452a-9d6b-1753461b783b", "gmt_create": "2025-09-15T22:22:12.687727+03:00", "gmt_modified": "2025-09-15T22:22:12.690853+03:00"}, {"catalog_id": "988e0cd8-7272-42a8-9829-9a228397a2d3", "content": "", "title": "SearchResult Model", "description": "search-result-model", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "8da30d92-b747-4048-afdd-9a665b6ab86e", "gmt_create": "2025-09-15T22:22:37.754888+03:00", "gmt_modified": "2025-09-15T22:22:37.765736+03:00"}, {"catalog_id": "8a751669-a594-448a-a6ba-78eb890246cc", "content": "", "title": "Technical Metrics", "description": "technical-metrics", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "55efdd8d-76bf-4ed4-93a3-86c874cddf05", "gmt_create": "2025-09-15T22:24:16.462906+03:00", "gmt_modified": "2025-09-15T22:24:16.469021+03:00"}, {"catalog_id": "c1d04d48-1d95-49d9-a8e3-a7ce26721b81", "content": "", "title": "Automated Crawling", "description": "automated-crawling", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "004b950e-0c29-4eec-8b8b-1c1080200915", "gmt_create": "2025-09-15T22:25:08.721512+03:00", "gmt_modified": "2025-09-15T22:25:08.739516+03:00"}, {"catalog_id": "bd3d1bdf-7006-433e-af3c-0ba42eed671c", "content": "", "title": "Chaos Testing", "description": "chaos-testing", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "071dd99c-3abd-46fe-a1bd-560a93532556", "gmt_create": "2025-09-15T22:25:57.616615+03:00", "gmt_modified": "2025-09-15T22:25:57.62087+03:00"}, {"catalog_id": "16f2d074-d19f-4ec4-b84a-84799c340ae4", "content": "", "title": "MariaDB Schema", "description": "mariadb-schema", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "2d18454b-43d7-4375-85d7-5b67b23f149a", "gmt_create": "2025-09-15T22:26:29.369041+03:00", "gmt_modified": "2025-09-15T22:26:29.373116+03:00"}, {"catalog_id": "03b49ff6-2295-400f-9533-d04f0f74dc3b", "content": "", "title": "Backlink Metrics", "description": "backlink-metrics", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "9bf873f5-e7a7-4432-98a8-5c6f0448fcea", "gmt_create": "2025-09-15T22:27:25.423982+03:00", "gmt_modified": "2025-09-15T22:27:25.427628+03:00"}, {"catalog_id": "eff23966-17fa-409c-9add-d34d956a5c62", "content": "", "title": "Visual Testing", "description": "visual-testing", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "5b8f4a38-e344-462c-a92d-e5dd543042be", "gmt_create": "2025-09-15T22:27:39.286122+03:00", "gmt_modified": "2025-09-15T22:27:39.291697+03:00"}, {"catalog_id": "608ebf66-7fa9-4be9-aad4-e4defd6c77b1", "content": "", "title": "Manticore Search Schema", "description": "manticore-schema", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "ebf65400-74b3-43b3-a43c-5279fb915a21", "gmt_create": "2025-09-15T22:28:51.659276+03:00", "gmt_modified": "2025-09-15T22:28:51.665444+03:00"}, {"catalog_id": "b5217cb3-2874-4c18-ad3e-b5dc2696aeac", "content": "", "title": "Ranking Update Mechanism", "description": "ranking-update-mechanism", "extend": "{}", "progress_status": "completed", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "id": "6da1d28f-9649-48c2-98c7-10e13534d09b", "gmt_create": "2025-09-15T22:29:28.641545+03:00", "gmt_modified": "2025-09-15T22:29:28.663949+03:00"}], "wiki_overview": {"content": "<blog>\n# Comprehensive Project Analysis: Domain Ranking System\n\n## 1. Project Introduction\n\n### Purpose Statement\nThe Domain Ranking System is a comprehensive platform designed to evaluate websites based on performance, security, SEO, and technical metrics. Similar to industry leaders like Alexa Rankings and Cloudflare Radar, this system provides automated website analysis, data collection through web crawling, and user-friendly interfaces for browsing ranked domains.\n\n### Core Goals and Objectives\n- Automate domain discovery and analysis\n- Calculate comprehensive domain rankings using a weighted scoring algorithm\n- Provide user-friendly interfaces for domain search and comparison\n- Implement robust monitoring and health checks\n- Support scalable deployment through containerization\n\n### Target Audience\n- Web analysts and researchers\n- SEO professionals\n- Security auditors\n- Domain investors\n- Web developers\n\n## 2. Technical Architecture\n\n### Component Breakdown\nThe project follows a microservices architecture with three main services and a shared utilities library:\n\n```mermaid\ngraph TD\n    SharedLib[Shared Libraries] --> WebApp\n    SharedLib --> Worker\n    SharedLib --> DomainSeeder\n    SharedLib --> Admin\n    \n    WebApp[Web Application] --> API[API Layer]\n    WebApp --> UI[React Frontend]\n    \n    Worker[Worker Service] --> Crawler[Crawler]\n    Worker --> Ranking[Ranking Engine]\n    Worker --> Scheduler[Scheduler]\n    \n    DomainSeeder[Domain Seeder] --> Discovery[Domain Discovery]\n    DomainSeeder --> Normalization[Domain Normalization]\n    DomainSeeder --> Validation[Validation]\n    \n    Admin[Admin Interface] --> Dashboard[Analytics Dashboard]\n    Admin --> Management[User Management]\n    Admin --> Monitoring[Monitoring]\n```\n\n### Design Patterns\nThe system implements several key design patterns:\n\n- **Monorepo Structure**: Using pnpm workspaces to manage multiple services\n- **Microservices Architecture**: Separated concerns with independent services\n- **Event-Driven Communication**: Using Redis-SMQ for inter-service communication\n- **Layered Architecture**: Clear separation between presentation, business logic, and data access layers\n- **Configuration as Code**: Environment variables and configuration files for deployment flexibility\n\n### System Relationships\n```mermaid\ngraph LR\n    Client[Client] --> WebApp[Web Application]\n    WebApp --> Worker[Worker Service]\n    WebApp --> DomainSeeder[Domain Seeder]\n    Worker --> Database[(Databases)]\n    DomainSeeder --> Database\n    Admin --> WebApp\n    Admin --> Worker\n    Monitoring --> AllServices\n```\n\n### Data Flow\n```mermaid\nflowchart TD\n    A[Client Request] --> B[Web Application]\n    B --> C{Request Type}\n    C -->|Search| D[Query Manticore Search]\n    C -->|Analysis| E[Check Redis Cache]\n    E -->|Cache Miss| F[Send Job to Worker]\n    F --> G[Worker Processes Domain]\n    G --> H[Store Results in ScyllaDB/MariaDB]\n    G --> I[Update Manticore Index]\n    H --> J[Return Results to Web App]\n    I --> J\n    J --> K[Client Response]\n```\n\n## 3. Key Implementation\n\n### Main Entry Points\n- Web Application: `services/web-app/src/index.ts`\n- Worker Service: `services/worker/src/index.ts`\n- Domain Seeder: `services/domain-seeder/src/index.ts`\n- Admin Interface: `services/admin/src/app/page.tsx`\n\n### Core Modules\n- **Domain Analysis**: Comprehensive evaluation of website metrics\n- **Ranking Engine**: Weighted scoring algorithm for domain rankings\n- **Crawler**: Automated data collection from websites\n- **Search Indexing**: Full-text search capabilities via Manticore Search\n- **User Authentication**: Secure access control for admin interface\n\n### Configuration Approach\nThe project uses a comprehensive configuration system with environment variables and service-specific configuration files:\n\n```mermaid\ngraph TD\n    Env[.env File] --> WebApp\n    Env --> Worker\n    Env --> DomainSeeder\n    Env --> Admin\n    \n    WebApp -->|Port| WEB_APP_PORT\n    WebApp -->|CORS| CORS_ORIGIN\n    WebApp -->|Logging| LOG_LEVEL\n    \n    Worker -->|Crawling| CRAWL_RATE_LIMIT\n    Worker -->|Crawling| CRAWL_CONCURRENT_REQUESTS\n    Worker -->|Ranking| RANKING_WEIGHT_PERFORMANCE\n    Worker -->|Ranking| RANKING_WEIGHT_SECURITY\n    Worker -->|Ranking| RANKING_WEIGHT_SEO\n    Worker -->|Ranking| RANKING_WEIGHT_TECHNICAL\n    Worker -->|Ranking| RANKING_WEIGHT_BACKLINKS\n    \n    DomainSeeder -->|Discovery| DISCOVERY_STRATEGIES\n    DomainSeeder -->|Rate Limiting| RATE_LIMIT_CONFIG\n    \n    Admin -->|Authentication| JWT_SECRET\n    Admin -->|Database| MARIA_PASSWORD\n```\n\n### External Dependencies\n- **Databases**: ScyllaDB, MariaDB, Redis, Manticore Search\n- **Frontend**: React 18, Next.js, Mantine UI\n- **Backend**: Express.js, ultimate-express\n- **Testing**: Vitest, Playwright, Jest\n- **Monitoring**: Prometheus, Grafana\n- **Containerization**: Docker, Docker Compose\n\n### Integration Points\n- **Web Application**: API endpoints for domain search and analysis\n- **Worker Service**: Job queue processing and domain analysis\n- **Domain Seeder**: External data sources for domain discovery\n- **Admin Interface**: User management and monitoring dashboards\n- **Monitoring System**: Prometheus metrics and Grafana dashboards\n\n```mermaid\ngraph LR\n    WebApp[Web Application] -->|API| Client\n    WebApp -->|Jobs| RedisSMQ[Redis-SMQ]\n    Worker[Worker Service] -->|Consume| RedisSMQ\n    Worker -->|Store| ScyllaDB\n    Worker -->|Store| MariaDB\n    Worker -->|Index| Manticore\n    DomainSeeder -->|Discover| ExternalAPIs\n    DomainSeeder -->|Store| ScyllaDB\n    Admin -->|Monitor| Prometheus\n    Admin -->|Visualize| Grafana\n```\n\n## 4. Key Features\n\n### Functionality Overview\nThe Domain Ranking System provides several key features:\n\n1. **Domain Search**: Full-text search with filtering and sorting\n2. **Domain Analysis**: Comprehensive technical analysis of websites\n3. **Top Domains**: Ranked lists by category and overall\n4. **Domain Comparison**: Side-by-side comparison of multiple domains\n5. **Automated Crawling**: Regular data collection from websites\n6. **Ranking Calculations**: Weighted scoring algorithm for domain rankings\n7. **Admin Dashboard**: User management and system monitoring\n\n### Implementation Highlights\n- **Ranking Algorithm**: Weighted scoring system with five key metrics\n- **Microservices Architecture**: Independent services for scalability\n- **Real-time Monitoring**: Comprehensive health checks and metrics\n- **Automated Deployment**: Docker Compose and Justfile for easy deployment\n- **Comprehensive Testing**: Unit, integration, E2E, performance, and chaos testing\n\n### Feature Architecture\n```mermaid\nstateDiagram-v2\n    [*] --> Idle\n    Idle --> DomainSearch: User searches\n    DomainSearch --> QueryProcessing: Process query\n    QueryProcessing --> SearchIndex: Query Manticore\n    SearchIndex --> ReturnResults: Return domains\n    ReturnResults --> Idle\n    \n    Idle --> DomainAnalysis: User requests analysis\n    DomainAnalysis --> CacheCheck: Check Redis\n    CacheCheck -->|Hit| ReturnCached: Return cached\n    CacheCheck -->|Miss| QueueJob: Send to worker\n    QueueJob --> WorkerProcessing: Worker processes\n    WorkerProcessing --> StoreResults: Save to DB\n    StoreResults --> UpdateIndex: Update search\n    StoreResults --> ReturnAnalysis: Return results\n    ReturnAnalysis --> Idle\n    \n    Idle --> RankingCalculation: Scheduled task\n    RankingCalculation --> CollectMetrics: Gather data\n    CollectMetrics --> ApplyWeights: Weighted scoring\n    ApplyWeights --> UpdateRankings: Save rankings\n    UpdateRankings --> Idle\n    \n    Idle --> Monitoring: Regular check\n    Monitoring --> HealthChecks: Service health\n    HealthChecks --> Alerting: Notify on failure\n    Alerting --> AdminDashboard: Display status\n    AdminDashboard --> Idle\n```\n\nSources:\n- [README.md](README.md)\n- [package.json](package.json)\n- [pnpm-workspace.yaml](pnpm-workspace.yaml)\n- [services/admin/package.json](services/admin/package.json)\n- [services/domain-seeder/package.json](services/domain-seeder/package.json)\n- [services/web-app/package.json](services/web-app/package.json)\n- [services/worker/package.json](services/worker/package.json)\n- [shared/package.json](shared/package.json)\n</blog>", "gmt_create": "2025-09-15T21:34:51.420858+03:00", "gmt_modified": "2025-09-15T21:34:51.420858+03:00", "id": "933789f2-791b-4a62-85e7-dfb7281a8ff4", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": ""}, "wiki_readme": {"content": "No readme file", "gmt_create": "2025-09-15T16:44:52.840975+03:00", "gmt_modified": "2025-09-15T16:44:52.840975+03:00", "id": "20f68894-b07a-4752-a526-a6f570837bcd", "repo_id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": ""}, "wiki_repo": {"id": "6d0aa1a2-a339-45d3-97df-e9327b1f7219", "workspace_path": "", "name": "domainr", "progress_status": "completed", "wiki_present_status": "COMPLETED", "optimized_catalog": "\".\\n├── _config\\n│   ├── eslint.rules.failed.js\\n│   ├── eslint.rules.js\\n│   ├── eslint.rules.last_failed.js\\n│   ├── eslint.rules.latest.js\\n│   ├── eslint.rules.my.js\\n│   ├── eslint.rules.new.js\\n│   ├── tsconfig.base.json\\n│   ├── tsconfig.browser.json\\n│   ├── tsconfig.mobile.json\\n│   ├── tsconfig.server.json\\n│   ├── tsconfig.test-mobile.json\\n│   └── tsconfig.test-web.json\\n├── data\\n│   ├── brand-seeds.json\\n│   ├── categories.json\\n│   ├── stopwords.json\\n│   ├── tags.json\\n│   └── tld-categories.json\\n├── database\\n│   ├── manticore\\n│   │   └── init.sql\\n│   ├── mariadb\\n│   │   └── init.sql\\n│   ├── sample-data\\n│   │   ├── categories.json\\n│   │   └── domains.json\\n│   └── scripts\\n│       ├── migrate.js\\n│       └── package.json\\n├── monitoring\\n│   └── prometheus.yml\\n├── nginx\\n│   ├── conf.d\\n│   │   └── default.conf\\n│   └── nginx.conf\\n├── scripts\\n│   ├── backup.sh\\n│   ├── compare-eslint-configs.ts\\n│   ├── deploy-domain-seeder.sh\\n│   ├── deploy.sh\\n│   ├── setup-environment.sh\\n│   └── validate-docker-config.sh\\n├── services\\n│   ├── admin\\n│   │   ├── .github/workflows\\n│   │   │   └── test.yml\\n│   │   ├── backup\\n│   │   │   └── backup-manager.js\\n│   │   ├── logging\\n│   │   │   └── centralized-logging.js\\n│   │   ├── monitoring\\n│   │   │   ├── alert_rules.yml\\n│   │   │   ├── alertmanager.yml\\n│   │   │   ├── grafana-dashboard.json\\n│   │   │   └── prometheus.yml\\n│   │   ├── nginx\\n│   │   │   └── admin.conf\\n│   │   ├── scripts\\n│   │   │   ├── deploy.sh\\n│   │   │   ├── migrate.js\\n│   │   │   ├── production-health-check.sh\\n│   │   │   └── run-tests.sh\\n│   │   ├── security\\n│   │   │   └── security-headers.js\\n│   │   ├── src\\n│   │   │   ├── app\\n│   │   │   │   ├── (authenticated)\\n│   │   │   │   │   ├── dashboard\\n│   │   │   │   │   ├── realtime-test\\n│   │   │   │   │   ├── ui-demo\\n│   │   │   │   │   └── layout.tsx\\n│   │   │   │   ├── ai\\n│   │   │   │   │   └── page.tsx\\n│   │   │   │   ├── alerts\\n│   │   │   │   │   ├── analytics\\n│   │   │   │   │   ├── notifications\\n│   │   │   │   │   ├── rules\\n│   │   │   │   │   ├── test\\n│   │   │   │   │   └── page.tsx\\n│   │   │   │   ├── analytics\\n│   │   │   │   │   └── page.tsx\\n│   │   │   │   ├── api\\n│   │   │   │   │   ├── __tests__\\n│   │   │   │   │   ├── ai\\n│   │   │   │   │   ├── alerts\\n│   │   │   │   │   ├── analytics\\n│   │   │   │   │   ├── auth\\n│   │   │   │   │   ├── config\\n│   │   │   │   │   ├── crawl\\n│   │   │   │   │   ├── database\\n│   │   │   │   │   ├── domains\\n│   │   │   │   │   ├── errors\\n│   │   │   │   │   ├── health\\n│   │   │   │   │   ├── logs\\n│   │   │   │   │   ├── realtime\\n│   │   │   │   │   ├── seeder\\n│   │   │   │   │   ├── services\\n│   │   │   │   │   ├── simple-health\\n│   │   │   │   │   └── users\\n│   │   │   │   ├── crawl\\n│   │   │   │   │   └── page.tsx\\n│   │   │   │   ├── database\\n│   │   │   │   │   └── page.tsx\\n│   │   │   │   ├── domains\\n│   │   │   │   │   └── page.tsx\\n│   │   │   │   ├── errors\\n│   │   │   │   │   └── page.tsx\\n│   │   │   │   ├── logging\\n│   │   │   │   │   └── page.tsx\\n│   │   │   │   ├── login\\n│   │   │   │   │   └── page.tsx\\n│   │   │   │   ├── seeder\\n│   │   │   │   │   └── page.tsx\\n│   │   │   │   ├── simple-dashboard\\n│   │   │   │   │   └── page.tsx\\n│   │   │   │   ├── users\\n│   │   │   │   │   └── page.tsx\\n│   │   │   │   ├── layout.tsx\\n│   │   │   │   └── page.tsx\\n│   │   │   ├── components\\n│   │   │   │   ├── AI\\n│   │   │   │   │   ├── AIContentQuality.tsx\\n│   │   │   │   │   ├── AIPromptManagement.tsx\\n│   │   │   │   │   ├── AIProviderConfig.tsx\\n│   │   │   │   │   ├── AIServiceConfig.tsx\\n│   │   │   │   │   ├── AIServiceManagement.tsx\\n│   │   │   │   │   ├── AIUsageAnalytics.tsx\\n│   │   │   │   │   └── index.ts\\n│   │   │   │   ├── Alerts\\n│   │   │   │   │   ├── AlertAnalytics.tsx\\n│   │   │   │   │   ├── AlertDashboard.tsx\\n│   │   │   │   │   ├── AlertRuleManager.tsx\\n│   │   │   │   │   ├── AlertTestFramework.tsx\\n│   │   │   │   │   ├── NotificationManager.tsx\\n│   │   │   │   │   ├── README.md\\n│   │   │   │   │   └── index.ts\\n│   │   │   │   ├── Analytics\\n│   │   │   │   │   ├── AnalyticsDashboard.tsx\\n│   │   │   │   │   ├── BusinessIntelligence.tsx\\n│   │   │   │   │   ├── ChartsGrid.tsx\\n│   │   │   │   │   ├── MetricsOverview.tsx\\n│   │   │   │   │   ├── ReportGenerator.tsx\\n│   │   │   │   │   ├── TrendAnalysis.tsx\\n│   │   │   │   │   └── index.ts\\n│   │   │   │   ├── Auth\\n│   │   │   │   │   ├── __tests__\\n│   │   │   │   │   ├── AuthTest.tsx\\n│   │   │   │   │   └── LoginForm.tsx\\n│   │   │   │   ├── Config\\n│   │   │   │   │   ├── ConfigurationDashboard.tsx\\n│   │   │   │   │   └── DatabaseConfigForm.tsx\\n│   │   │   │   ├── Crawl\\n│   │   │   │   │   └── ... 11 files, 0 dirs not shown\\n│   │   │   │   ├── Database\\n│   │   │   │   │   └── ... 11 files, 0 dirs not shown\\n│   │   │   │   ├── Domains\\n│   │   │   │   │   └── ... 8 files, 0 dirs not shown\\n│   │   │   │   ├── Errors\\n│   │   │   │   │   └── ... 2 files, 0 dirs not shown\\n│   │   │   │   ├── Layout\\n│   │   │   │   │   └── ... 2 files, 0 dirs not shown\\n│   │   │   │   ├── Logging\\n│   │   │   │   │   └── ... 12 files, 0 dirs not shown\\n│   │   │   │   ├── Realtime\\n│   │   │   │   │   └── ... 5 files, 0 dirs not shown\\n│   │   │   │   ├── Seeder\\n│   │   │   │   │   └── ... 10 files, 0 dirs not shown\\n│   │   │   │   ├── Services\\n│   │   │   │   │   └── ... 3 files, 0 dirs not shown\\n│   │   │   │   ├── UI\\n│   │   │   │   │   └── ... 1 files, 10 dirs not shown\\n│   │   │   │   ├── Users\\n│   │   │   │   │   └── ... 9 files, 0 dirs not shown\\n│   │   │   │   └── index.ts\\n│   │   │   ├── hooks\\n│   │   │   │   ├── useRealtime.ts\\n│   │   │   │   ├── useRealtimeSubscription.ts\\n│   │   │   │   └── useServiceAlerts.tsx\\n│   │   │   ├── lib\\n│   │   │   │   ├── alerts\\n│   │   │   │   │   └── ... 1 files, 0 dirs not shown\\n│   │   │   │   ├── auth\\n│   │   │   │   │   └── ... 8 files, 1 dirs not shown\\n│   │   │   │   ├── database\\n│   │   │   │   │   └── ... 7 files, 1 dirs not shown\\n│   │   │   │   ├── errors\\n│   │   │   │   │   └── ... 12 files, 0 dirs not shown\\n│   │   │   │   ├── realtime\\n│   │   │   │   │   └── ... 8 files, 0 dirs not shown\\n│   │   │   │   ├── utils\\n│   │   │   │   │   └── ... 1 files, 0 dirs not shown\\n│   │   │   │   ├── config.ts\\n│   │   │   │   ├── logger.ts\\n│   │   │   │   └── theme.ts\\n│   │   │   ├── scripts\\n│   │   │   │   └── init-user-management.ts\\n│   │   │   ├── types\\n│   │   │   │   ├── ai.ts\\n│   │   │   │   ├── alerts.ts\\n│   │   │   │   ├── analytics.ts\\n│   │   │   │   ├── auth.ts\\n│   │   │   │   ├── config.ts\\n│   │   │   │   ├── crawl.ts\\n│   │   │   │   ├── database.ts\\n│   │   │   │   ├── domain.ts\\n│   │   │   │   ├── index.ts\\n│   │   │   │   ├── logs.ts\\n│   │   │   │   ├── seeder.ts\\n│   │   │   │   └── services.ts\\n│   │   │   ├── utils\\n│   │   │   │   ├── __tests__\\n│   │   │   │   │   └── ... 1 files, 0 dirs not shown\\n│   │   │   │   ├── index.ts\\n│   │   │   │   ├── logger.ts\\n│   │   │   │   ├── passwordValidation.ts\\n│   │   │   │   └── validation.ts\\n│   │   │   └── middleware.ts\\n│   │   ├── tests\\n│   │   │   ├── chaos\\n│   │   │   │   └── database-failures.test.ts\\n│   │   │   ├── e2e\\n│   │   │   │   ├── auth.spec.ts\\n│   │   │   │   ├── global-setup.ts\\n│   │   │   │   └── global-teardown.ts\\n│   │   │   ├── load\\n│   │   │   │   └── auth-load.js\\n│   │   │   ├── mocks\\n│   │   │   │   ├── handlers.ts\\n│   │   │   │   └── server.ts\\n│   │   │   ├── visual\\n│   │   │   │   └── components.spec.ts\\n│   │   │   └── setup.ts\\n│   │   ├── DEPLOYMENT.md\\n│   │   ├── Dockerfile\\n│   │   ├── README.md\\n│   │   ├── TESTING.md\\n│   │   ├── docker-compose.production.yml\\n│   │   ├── docker-compose.simple.yml\\n│   │   ├── healthcheck.js\\n│   │   ├── next-env.d.ts\\n│   │   ├── next.config.js\\n│   │   ├── package.json\\n│   │   ├── playwright-visual.config.ts\\n│   │   ├── playwright.config.ts\\n│   │   ├── postcss.config.js\\n│   │   ├── start-simple.sh\\n│   │   ├── tsconfig.json\\n│   │   └── vitest.config.ts\\n│   ├── domain-seeder\\n│   │   ├── data\\n│   │   │   ├── brand-seeds.json\\n│   │   │   ├── categories.json\\n│   │   │   ├── stopwords.json\\n│   │   │   ├── tags.json\\n│   │   │   ├── tld-categories.json\\n│   │   │   └── tlds.json\\n│   │   ├── src\\n│   │   │   ├── __tests__\\n│   │   │   │   └── setup.ts\\n│   │   │   ├── api\\n│   │   │   │   ├── HttpApiServer.ts\\n│   │   │   │   └── README.md\\n│   │   │   ├── cli\\n│   │   │   │   └── CLIOutput.ts\\n│   │   │   ├── commands\\n│   │   │   │   ├── analyzeLive.ts\\n│   │   │   │   ├── backfill.ts\\n│   │   │   │   ├── describe.ts\\n│   │   │   │   ├── logs.ts\\n│   │   │   │   ├── run.ts\\n│   │   │   │   ├── status.ts\\n│   │   │   │   └── top.ts\\n│   │   │   ├── config\\n│   │   │   │   ├── ConfigurationProfiles.ts\\n│   │   │   │   ├── CredentialManager.ts\\n│   │   │   │   ├── EnvironmentProfiles.ts\\n│   │   │   │   ├── SeederConfig.ts\\n│   │   │   │   └── index.ts\\n│   │   │   ├── connectors\\n│   │   │   │   ├── CZDSConnector.ts\\n│   │   │   │   ├── CommonCrawlConnector.ts\\n│   │   │   │   ├── PIRConnector.ts\\n│   │   │   │   ├── README.md\\n│   │   │   │   ├── RadarConnector.ts\\n│   │   │   │   ├── SonarConnector.ts\\n│   │   │   │   ├── TrancoConnector.ts\\n│   │   │   │   ├── UmbrellaConnector.ts\\n│   │   │   │   └── index.ts\\n│   │   │   ├── content\\n│   │   │   │   ├── ai\\n│   │   │   │   │   └── ... 8 files, 1 dirs not shown\\n│   │   │   │   ├── CategoryTagger.ts\\n│   │   │   │   ├── DomainSummaryService.ts\\n│   │   │   │   ├── IntegratedContentGenerator.ts\\n│   │   │   │   ├── LiveContentAnalyzer.ts\\n│   │   │   │   ├── PreGeneratedContentGenerator.ts\\n│   │   │   │   ├── README.md\\n│   │   │   │   ├── SchedulerIntegrationService.ts\\n│   │   │   │   └── index.ts\\n│   │   │   ├── core\\n│   │   │   │   ├── DiscoveryOperations.ts\\n│   │   │   │   ├── GracefulShutdown.ts\\n│   │   │   │   └── HttpServer.ts\\n│   │   │   ├── discovery\\n│   │   │   │   ├── stores\\n│   │   │   │   │   └── ... 4 files, 0 dirs not shown\\n│   │   │   │   ├── strategies\\n│   │   │   │   │   └── ... 6 files, 0 dirs not shown\\n│   │   │   │   ├── DiscoveryEngineFactory.ts\\n│   │   │   │   ├── IntelligentDiscoveryEngine.ts\\n│   │   │   │   ├── OptimizedDiscoveryPipeline.ts\\n│   │   │   │   ├── README.md\\n│   │   │   │   └── index.ts\\n│   │   │   ├── enqueuer\\n│   │   │   │   ├── RateLimitedDomainEnqueuer.ts\\n│   │   │   │   └── index.ts\\n│   │   │   ├── errors\\n│   │   │   │   └── SeederErrorHandler.ts\\n│   │   │   ├── examples\\n│   │   │   │   ├── cache-warming-example.ts\\n│   │   │   │   └── reliable-logging-example.ts\\n│   │   │   ├── interfaces\\n│   │   │   │   ├── DiscoveryEngine.ts\\n│   │   │   │   ├── DomainNormalizer.ts\\n│   │   │   │   ├── ReliabilityManager.ts\\n│   │   │   │   └── SourceConnector.ts\\n│   │   │   ├── logging\\n│   │   │   │   └── index.ts\\n│   │   │   ├── monitoring\\n│   │   │   │   ├── AlertingSystem.md\\n│   │   │   │   ├── HealthChecker.ts\\n│   │   │   │   ├── HealthEndpoints.md\\n│   │   │   │   ├── MetricsCollector.ts\\n│   │   │   │   ├── README.md\\n│   │   │   │   ├── example-metrics-usage.ts\\n│   │   │   │   └── index.ts\\n│   │   │   ├── normalization\\n│   │   │   │   ├── PSLDomainNormalizer.ts\\n│   │   │   │   ├── PSLManager.ts\\n│   │   │   │   └── index.ts\\n│   │   │   ├── provenance\\n│   │   │   │   ├── ProvenanceTracker.ts\\n│   │   │   │   ├── README.md\\n│   │   │   │   └── index.ts\\n│   │   │   ├── ratelimiting\\n│   │   │   │   ├── BackpressureController.ts\\n│   │   │   │   ├── README.md\\n│   │   │   │   ├── TokenBucket.ts\\n│   │   │   │   └── index.ts\\n│   │   │   ├── reliability\\n│   │   │   │   ├── CrashSafeReliabilityManager.ts\\n│   │   │   │   ├── ErrorHandler.ts\\n│   │   │   │   ├── ErrorReporting.ts\\n│   │   │   │   ├── README.md\\n│   │   │   │   ├── ReliableStreamProcessor.ts\\n│   │   │   │   └── index.ts\\n│   │   │   ├── repositories\\n│   │   │   │   ├── BloomFilter.ts\\n│   │   │   │   ├── CompositeDomainRepository.ts\\n│   │   │   │   ├── DomainRepository.ts\\n│   │   │   │   ├── EnhancedManticoreDomainIndex.ts\\n│   │   │   │   ├── IMPLEMENTATION_SUMMARY.md\\n│   │   │   │   ├── ManticoreDomainRepository.ts\\n│   │   │   │   ├── MariaDomainRepository.ts\\n│   │   │   │   ├── OptimizedCompositeDomainRepository.ts\\n│   │   │   │   ├── OptimizedDomainExistenceService.ts\\n│   │   │   │   ├── README.md\\n│   │   │   │   ├── REDIS_CACHE_IMPLEMENTATION.md\\n│   │   │   │   ├── RedisCacheLayer.ts\\n│   │   │   │   ├── RedisOperationalLayer.ts\\n│   │   │   │   ├── ScyllaDomainRepository.ts\\n│   │   │   │   └── index.ts\\n│   │   │   ├── routes\\n│   │   │   │   └── health.ts\\n│   │   │   ├── scheduling\\n│   │   │   │   ├── LiveContentScheduler.ts\\n│   │   │   │   ├── README.md\\n│   │   │   │   ├── SchedulerOrchestrator.ts\\n│   │   │   │   ├── SmartDiscoveryScheduler.ts\\n│   │   │   │   └── index.ts\\n│   │   │   ├── scripts\\n│   │   │   │   ├── examplePhaseOneContentCreator.md\\n│   │   │   │   ├── examplePhaseOneContentCreator.ts\\n│   │   │   │   ├── requirements.example.json\\n│   │   │   │   └── requirements.json\\n│   │   │   ├── services\\n│   │   │   │   ├── DomainAnalyzer.ts\\n│   │   │   │   ├── SeederCachingService.ts\\n│   │   │   │   ├── SeederDataSyncService.ts\\n│   │   │   │   ├── SharedServiceIntegration.ts\\n│   │   │   │   └── index.ts\\n│   │   │   ├── synchronization\\n│   │   │   │   └── DataConsistencyService.ts\\n│   │   │   ├── validation\\n│   │   │   │   ├── ErrorRecovery.ts\\n│   │   │   │   ├── README.md\\n│   │   │   │   ├── ValidationManager.ts\\n│   │   │   │   ├── ValidationPipeline.ts\\n│   │   │   │   └── index.ts\\n│   │   │   ├── cli.ts\\n│   │   │   ├── health.ts\\n│   │   │   ├── index.backup.ts\\n│   │   │   └── index.ts\\n│   │   ├── Dockerfile\\n│   │   ├── README.md\\n│   │   ├── package.json\\n│   │   ├── tsconfig.json\\n│   │   └── vitest.config.ts\\n│   ├── web-app\\n│   │   ├── src\\n│   │   │   ├── __tests__\\n│   │   │   │   ├── integration\\n│   │   │   │   │   └── ... 3 files, 0 dirs not shown\\n│   │   │   │   ├── description-api.test.ts\\n│   │   │   │   └── setup.ts\\n│   │   │   ├── app\\n│   │   │   │   ├── routes\\n│   │   │   │   │   └── ... 1 files, 1 dirs not shown\\n│   │   │   │   ├── WebAppService.ts\\n│   │   │   │   ├── errorHandling.ts\\n│   │   │   │   └── middleware.ts\\n│   │   │   ├── components\\n│   │   │   │   ├── Search\\n│   │   │   │   │   └── ... 9 files, 0 dirs not shown\\n│   │   │   │   ├── DomainAnalysisPage.tsx\\n│   │   │   │   ├── DomainComparisonPage.tsx\\n│   │   │   │   ├── HomePage.tsx\\n│   │   │   │   ├── SearchPage.tsx\\n│   │   │   │   └── TopDomainsPage.tsx\\n│   │   │   ├── middleware\\n│   │   │   │   └── metricsMiddleware.ts\\n│   │   │   ├── routes\\n│   │   │   │   ├── description.ts\\n│   │   │   │   └── health.ts\\n│   │   │   ├── services\\n│   │   │   │   ├── __tests__\\n│   │   │   │   │   └── ... 1 files, 0 dirs not shown\\n│   │   │   │   ├── DomainAnalysisService.ts\\n│   │   │   │   ├── DomainSearchService.ts\\n│   │   │   │   └── TopDomainsService.ts\\n│   │   │   ├── utils\\n│   │   │   │   └── ReactRenderer.tsx\\n│   │   │   └── index.ts\\n│   │   ├── Dockerfile\\n│   │   ├── IMPLEMENTATION_SUMMARY.md\\n│   │   ├── package.json\\n│   │   ├── test-server.js\\n│   │   ├── tsconfig.json\\n│   │   └── vitest.config.ts\\n│   └── worker\\n│       ├── .github/workflows\\n│       │   └── deploy.yml\\n│       ├── k8s\\n│       │   ├── overlays\\n│       │   │   ├── production\\n│       │   │   │   └── ... 4 files, 0 dirs not shown\\n│       │   │   └── staging\\n│       │   │       └── ... 3 files, 0 dirs not shown\\n│       │   ├── configmap.yaml\\n│       │   ├── deployment.yaml\\n│       │   ├── hpa.yaml\\n│       │   ├── kustomization.yaml\\n│       │   ├── monitoring.yaml\\n│       │   ├── network-policy.yaml\\n│       │   ├── rbac.yaml\\n│       │   ├── secret.yaml\\n│       │   └── service.yaml\\n│       ├── monitoring\\n│       │   ├── grafana/dashboards\\n│       │   │   └── worker-service.json\\n│       │   ├── alert_rules.yml\\n│       │   ├── performance_dashboard.json\\n│       │   ├── production-monitoring.yml\\n│       │   └── prometheus.yml\\n│       ├── scripts\\n│       │   ├── api-compatibility-test.sh\\n│       │   ├── deploy.sh\\n│       │   ├── performance-comparison.sh\\n│       │   ├── performance-validation.sh\\n│       │   ├── rollback.sh\\n│       │   └── validate-functionality.sh\\n│       ├── src\\n│       │   ├── __tests__\\n│       │   │   ├── chaos\\n│       │   │   │   └── ... 1 files, 0 dirs not shown\\n│       │   │   ├── database\\n│       │   │   │   └── ... 1 files, 0 dirs not shown\\n│       │   │   ├── integration\\n│       │   │   │   └── ... 1 files, 0 dirs not shown\\n│       │   │   ├── performance\\n│       │   │   │   └── ... 1 files, 0 dirs not shown\\n│       │   │   ├── WorkerService.test.ts\\n│       │   │   ├── run-all-tests.ts\\n│       │   │   └── setup.ts\\n│       │   ├── ai\\n│       │   │   ├── __tests__\\n│       │   │   │   └── ... 2 files, 0 dirs not shown\\n│       │   │   ├── providers\\n│       │   │   │   └── ... 7 files, 0 dirs not shown\\n│       │   │   ├── AIConfigLoader.ts\\n│       │   │   ├── AIServiceMonitor.ts\\n│       │   │   ├── ContentAnalyzer.ts\\n│       │   │   ├── ContentQualityValidator.ts\\n│       │   │   ├── DomainDescriptionGenerator.ts\\n│       │   │   ├── IntegratedContentGenerator.ts\\n│       │   │   ├── PromptManager.ts\\n│       │   │   ├── index.ts\\n│       │   │   └── types.ts\\n│       │   ├── cli\\n│       │   │   ├── commands\\n│       │   │   │   └── ... 4 files, 0 dirs not shown\\n│       │   │   └── WorkerCLI.ts\\n│       │   ├── config\\n│       │   │   ├── README.md\\n│       │   │   ├── WorkerConfig.ts\\n│       │   │   └── index.ts\\n│       │   ├── core\\n│       │   │   └── WorkerService.ts\\n│       │   ├── crawler\\n│       │   │   ├── analyzers\\n│       │   │   │   └── ... 11 files, 1 dirs not shown\\n│       │   │   ├── config\\n│       │   │   │   └── ... 2 files, 0 dirs not shown\\n│       │   │   ├── core\\n│       │   │   │   └── ... 8 files, 1 dirs not shown\\n│       │   │   ├── examples\\n│       │   │   │   └── ... 2 files, 0 dirs not shown\\n│       │   │   ├── modules\\n│       │   │   │   └── ... 12 files, 1 dirs not shown\\n│       │   │   ├── monitoring\\n│       │   │   │   └── ... 2 files, 0 dirs not shown\\n│       │   │   ├── README.md\\n│       │   │   └── index.ts\\n│       │   ├── database\\n│       │   │   ├── __tests__\\n│       │   │   │   └── ... 1 files, 0 dirs not shown\\n│       │   │   ├── schemas\\n│       │   │   │   └── ... 3 files, 0 dirs not shown\\n│       │   │   ├── DatabaseMigrationManager.ts\\n│       │   │   ├── README.md\\n│       │   │   ├── WorkerDatabaseManager.ts\\n│       │   │   └── index.ts\\n│       │   ├── errors\\n│       │   │   ├── __tests__\\n│       │   │   │   └── ... 3 files, 0 dirs not shown\\n│       │   │   ├── AutoRecoverySystem.ts\\n│       │   │   ├── CircuitBreaker.ts\\n│       │   │   ├── ComprehensiveErrorHandler.ts\\n│       │   │   ├── ErrorAnalyticsEngine.ts\\n│       │   │   ├── ErrorClassification.ts\\n│       │   │   ├── ErrorReportingSystem.ts\\n│       │   │   ├── GracefulDegradationManager.ts\\n│       │   │   ├── README.md\\n│       │   │   ├── RetryManager.ts\\n│       │   │   ├── WorkerErrorHandler.ts\\n│       │   │   └── index.ts\\n│       │   ├── examples\\n│       │   │   ├── comprehensive-demo.ts\\n│       │   │   └── index.ts\\n│       │   ├── indexing\\n│       │   │   ├── __tests__\\n│       │   │   │   └── ... 2 files, 0 dirs not shown\\n│       │   │   ├── CacheInvalidationManager.ts\\n│       │   │   ├── DataSynchronizationService.ts\\n│       │   │   ├── IndexMaintenanceService.ts\\n│       │   │   ├── ManticoreIndexManager.ts\\n│       │   │   ├── README.md\\n│       │   │   ├── SearchIndexingService.ts\\n│       │   │   └── index.ts\\n│       │   ├── locking\\n│       │   │   ├── __tests__\\n│       │   │   │   └── ... 3 files, 0 dirs not shown\\n│       │   │   ├── examples\\n│       │   │   │   └── ... 1 files, 0 dirs not shown\\n│       │   │   ├── DomainLockManager.ts\\n│       │   │   ├── LockMonitoringService.ts\\n│       │   │   ├── README.md\\n│       │   │   ├── index.ts\\n│       │   │   └── types.ts\\n│       │   ├── monitoring\\n│       │   │   ├── HealthCheck.ts\\n│       │   │   ├── README.md\\n│       │   │   └── index.ts\\n│       │   ├── pipeline\\n│       │   │   ├── __tests__\\n│       │   │   │   └── ... 1 files, 0 dirs not shown\\n│       │   │   ├── DomainProcessingPipeline.ts\\n│       │   │   ├── PipelineCoordinator.ts\\n│       │   │   ├── README.md\\n│       │   │   ├── TaskExecutorPool.ts\\n│       │   │   └── index.ts\\n│       │   ├── queue\\n│       │   │   ├── __tests__\\n│       │   │   │   └── ... 1 files, 0 dirs not shown\\n│       │   │   ├── JobConsumer.ts\\n│       │   │   ├── JobProgressTracker.ts\\n│       │   │   ├── JobScheduler.ts\\n│       │   │   ├── JobStatisticsCollector.ts\\n│       │   │   ├── README.md\\n│       │   │   ├── WorkerJobQueue.ts\\n│       │   │   ├── index.ts\\n│       │   │   └── types.ts\\n│       │   ├── ranking\\n│       │   │   ├── __tests__\\n│       │   │   │   └── ... 5 files, 0 dirs not shown\\n│       │   │   ├── examples\\n│       │   │   │   └── ... 2 files, 0 dirs not shown\\n│       │   │   ├── scorers\\n│       │   │   │   └── ... 5 files, 0 dirs not shown\\n│       │   │   ├── CompositeRanker.ts\\n│       │   │   ├── README.md\\n│       │   │   ├── RankingCalculator.ts\\n│       │   │   ├── RankingManager.ts\\n│       │   │   ├── RankingUpdateService.ts\\n│       │   │   ├── RankingValidator.ts\\n│       │   │   └── index.ts\\n│       │   ├── routes\\n│       │   │   └── health.ts\\n│       │   ├── scheduler\\n│       │   │   ├── __tests__\\n│       │   │   │   └── ... 6 files, 0 dirs not shown\\n│       │   │   ├── examples\\n│       │   │   │   └── ... 1 files, 0 dirs not shown\\n│       │   │   ├── CrawlJobManager.ts\\n│       │   │   ├── JobScheduler.ts\\n│       │   │   ├── README.md\\n│       │   │   ├── SchedulerHealthService.ts\\n│       │   │   ├── SchedulerService.ts\\n│       │   │   └── index.ts\\n│       │   ├── types\\n│       │   │   ├── DatabaseTypes.ts\\n│       │   │   ├── ModuleInterfaces.ts\\n│       │   │   ├── PerformanceTypes.ts\\n│       │   │   ├── UtilityTypes.ts\\n│       │   │   ├── WorkerTypes.ts\\n│       │   │   └── index.ts\\n│       │   ├── utils\\n│       │   │   ├── ArrayUtils.ts\\n│       │   │   ├── CacheUtils.ts\\n│       │   │   ├── CrawlerUtils.ts\\n│       │   │   ├── DatabaseUtils.ts\\n│       │   │   ├── DateTimeUtils.ts\\n│       │   │   ├── DomainDescriptionBuilder.ts\\n│       │   │   ├── FileSystemUtils.ts\\n│       │   │   ├── MetricsHelpers.ts\\n│       │   │   ├── NetworkUtils.ts\\n│       │   │   ├── ObjectUtils.ts\\n│       │   │   ├── PerformanceUtils.ts\\n│       │   │   ├── QueueUtils.ts\\n│       │   │   ├── RankingUtils.ts\\n│       │   │   ├── RecoveryFunctions.ts\\n│       │   │   ├── RetryHelpers.ts\\n│       │   │   ├── SchedulerUtils.ts\\n│       │   │   ├── SecurityUtils.ts\\n│       │   │   ├── StringUtils.ts\\n│       │   │   ├── UrlUtils.ts\\n│       │   │   ├── ValidationUtilities.ts\\n│       │   │   └── index.ts\\n│       │   ├── validation\\n│       │   │   ├── __tests__\\n│       │   │   │   └── ... 3 files, 0 dirs not shown\\n│       │   │   ├── ComprehensiveDomainSchema.ts\\n│       │   │   ├── DomainDataValidator.ts\\n│       │   │   ├── DomainDescriptionBuilder.ts\\n│       │   │   ├── ErrorRecovery.ts\\n│       │   │   ├── ValidationManager.ts\\n│       │   │   ├── ValidationPipeline.ts\\n│       │   │   └── index.ts\\n│       │   ├── EXTRACTED_FUNCTIONALITY.md\\n│       │   ├── cli.ts\\n│       │   ├── health.ts\\n│       │   └── index.ts\\n│       ├── Dockerfile\\n│       ├── README.md\\n│       ├── docker-compose.test.yml\\n│       ├── docker-compose.yml\\n│       ├── healthcheck.js\\n│       ├── package.json\\n│       ├── tsconfig.json\\n│       ├── vitest.config.integration.ts\\n│       └── vitest.config.ts\\n├── shared\\n│   ├── bin\\n│   │   └── validate-description.ts\\n│   ├── data\\n│   │   ├── iso-3166-countries.json\\n│   │   └── iso-639-languages.json\\n│   ├── src\\n│   │   ├── __tests__\\n│   │   │   ├── DomainDescriptionValidator.contentQuality.test.ts\\n│   │   │   ├── DomainDescriptionValidator.enhanced.test.ts\\n│   │   │   ├── DomainDescriptionValidator.test.ts\\n│   │   │   └── setup.ts\\n│   │   ├── api\\n│   │   │   ├── ApiResponse.ts\\n│   │   │   └── index.ts\\n│   │   ├── database\\n│   │   │   ├── __tests__\\n│   │   │   │   └── DatabaseManager.test.ts\\n│   │   │   ├── CacheManager.ts\\n│   │   │   ├── DatabaseManager.ts\\n│   │   │   ├── ManticoreClient.ts\\n│   │   │   ├── MariaClient.ts\\n│   │   │   ├── RedisClient.ts\\n│   │   │   └── ScyllaClient.ts\\n│   │   ├── errors\\n│   │   │   ├── CircuitBreaker.ts\\n│   │   │   ├── ErrorClassification.ts\\n│   │   │   ├── ErrorHandler.ts\\n│   │   │   ├── RetryManager.ts\\n│   │   │   ├── index.ts\\n│   │   │   └── types.ts\\n│   │   ├── examples\\n│   │   │   └── sync-and-cache-example.ts\\n│   │   ├── middleware\\n│   │   │   ├── ErrorMiddleware.ts\\n│   │   │   ├── MetricsMiddleware.ts\\n│   │   │   ├── RateLimitMiddleware.ts\\n│   │   │   ├── SecurityMiddleware.ts\\n│   │   │   ├── index.ts\\n│   │   │   └── types.ts\\n│   │   ├── models\\n│   │   │   ├── types\\n│   │   │   │   └── DomainTypes.ts\\n│   │   │   ├── DomainAnalysis.ts\\n│   │   │   ├── DomainCrawlJob.ts\\n│   │   │   ├── DomainDescription.ts\\n│   │   │   ├── DomainModels.ts\\n│   │   │   ├── DomainRanking.ts\\n│   │   │   └── SearchResult.ts\\n│   │   ├── monitoring\\n│   │   │   ├── HealthChecker.ts\\n│   │   │   └── MetricsCollector.ts\\n│   │   ├── queue\\n│   │   │   └── JobQueue.ts\\n│   │   ├── services\\n│   │   │   ├── __tests__\\n│   │   │   │   └── CachingService.test.ts\\n│   │   │   ├── CacheInvalidationService.ts\\n│   │   │   ├── CachingService.ts\\n│   │   │   ├── DataSyncService.ts\\n│   │   │   └── SyncMonitoringService.ts\\n│   │   ├── utils\\n│   │   │   ├── __tests__\\n│   │   │   │   └── Validators.test.ts\\n│   │   │   ├── validation\\n│   │   │   │   ├── ContentValidator.ts\\n│   │   │   │   ├── ISOValidator.ts\\n│   │   │   │   ├── ReadabilityValidator.ts\\n│   │   │   │   ├── SEOValidator.ts\\n│   │   │   │   └── index.ts\\n│   │   │   ├── Config.ts\\n│   │   │   ├── DomainDescriptionValidator.ts\\n│   │   │   ├── DomainDescriptionValidatorV2.ts\\n│   │   │   ├── HttpClient.ts\\n│   │   │   ├── HttpClientStream.ts\\n│   │   │   ├── Logger.ts\\n│   │   │   └── Validators.ts\\n│   │   └── index.ts\\n│   ├── index.js\\n│   ├── package.json\\n│   ├── tsconfig.json\\n│   └── vitest.config.ts\\n├── CRUSH.md\\n├── README.md\\n├── TODO.md\\n├── debug-package.json\\n├── docker-compose.dev.yml\\n├── docker-compose.yml\\n├── eslint.config.js\\n├── eslint.config.my.js\\n├── eslint.config.new.js\\n├── package.json\\n├── pnpm-lock.yaml\\n├── pnpm-workspace.yaml\\n└── vitest.config.ts\\n\"", "current_document_structure": "WikiEncrypted: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", "catalogue_think_content": "WikiEncrypted: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", "recovery_checkpoint": "wiki_generation_completed", "last_commit_id": "67afcc54768ce34962d14c90b01fb46e60211310", "last_commit_update": "2025-09-15T21:33:46+03:00", "gmt_create": "2025-09-15T16:13:34.538319+03:00", "gmt_modified": "2025-09-15T22:29:29.207718+03:00", "extend_info": "{\"language\":\"en\",\"active\":true,\"branch\":\"main\",\"shareStatus\":\"\",\"server_error_code\":\"\",\"cosy_version\":\"\"}"}}