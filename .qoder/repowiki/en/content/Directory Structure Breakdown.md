# Directory Structure Breakdown

<cite>
**Referenced Files in This Document**   
- [pnpm-workspace.yaml](file://pnpm-workspace.yaml)
- [README.md](file://README.md)
- [_config/tsconfig.base.json](file://_config/tsconfig.base.json)
- [data/categories.json](file://data/categories.json)
- [database/mariadb/init.sql](file://database/mariadb/init.sql)
- [monitoring/prometheus.yml](file://monitoring/prometheus.yml)
- [nginx/nginx.conf](file://nginx/nginx.conf)
- [scripts/deploy.sh](file://scripts/deploy.sh)
- [services/admin/README.md](file://services/admin/README.md)
- [services/domain-seeder/README.md](file://services/domain-seeder/README.md)
- [services/web-app/README.md](file://services/web-app/README.md)
- [services/worker/README.md](file://services/worker/README.md)
- [shared/src/index.ts](file://shared/src/index.ts)
</cite>

## Table of Contents
1. [Monorepo Organization with pnpm Workspaces](#monorepo-organization-with-pnpm-workspaces)
2. [Top-Level Directory Purposes](#top-level-directory-purposes)
3. [Service Directory Roles](#service-directory-roles)
4. [Internal Service Organization](#internal-service-organization)
5. [Relationship to Microservices Architecture](#relationship-to-microservices-architecture)
6. [Developer Navigation and Workflow](#developer-navigation-and-workflow)

## Monorepo Organization with pnpm Workspaces

The domainr repository implements a monorepo architecture using pnpm workspaces to manage multiple related packages and services within a single codebase. This approach enables shared dependencies, consistent tooling, and coordinated versioning across the entire system.

The workspace configuration is defined in `pnpm-workspace.yaml`, which specifies the packages included in the workspace:

```yaml
packages:
  - 'services/*'
  - 'shared'
```

This configuration establishes two primary workspace patterns: all directories under `services/` and the `shared` directory. This structure allows for independent development of each service while maintaining the benefits of a unified repository.

The monorepo approach provides several advantages for the domainr system:
- **Dependency Management**: Shared dependencies are installed once at the root level, reducing duplication and disk usage
- **Code Sharing**: The `shared` package provides common utilities and models across services
- **Consistent Tooling**: ESLint, TypeScript, and other development tools are configured at the root level for consistency
- **Atomic Changes**: Cross-service changes can be made in a single commit
- **Simplified Testing**: Integration testing between services is streamlined

The pnpm workspace enables efficient development workflows through commands that can operate across multiple packages simultaneously. For example, running `pnpm -r run dev` executes the dev script in all workspaces, allowing developers to start multiple services with a single command.

**Section sources**
- [pnpm-workspace.yaml](file://pnpm-workspace.yaml#L1-L4)
- [README.md](file://README.md#L100-L120)

## Top-Level Directory Purposes

The domainr repository is organized into several top-level directories, each serving a specific purpose in the overall system architecture. This organization supports the microservices architecture by separating concerns and enabling independent development of different system components.

### _config Directory

The `_config` directory contains shared configuration files for development tools, primarily ESLint and TypeScript. This directory ensures consistent code quality and type checking across all services in the monorepo.

Key files in this directory include:
- **ESLint configurations**: Multiple ESLint rule files (e.g., `eslint.rules.js`, `eslint.rules.new.js`) that define coding standards and linting rules
- **TypeScript configurations**: Base and environment-specific tsconfig files (e.g., `tsconfig.base.json`, `tsconfig.server.json`, `tsconfig.browser.json`) that provide consistent type checking across different execution environments

These shared configurations are critical for maintaining code quality and consistency across the various services in the monorepo, ensuring that all code adheres to the same standards regardless of which team or developer wrote it.

**Section sources**
- [_config/tsconfig.base.json](file://_config/tsconfig.base.json)
- [README.md](file://README.md#L150-L160)

### data Directory

The `data` directory contains static data files used by the application for domain categorization, filtering, and analysis. These files provide reference data that supports the domain ranking and analysis functionality.

Key data files include:
- **categories.json**: Domain category definitions used for classification and filtering
- **tags.json**: Tags used for domain metadata and search functionality
- **tld-categories.json**: Top-level domain category mappings
- **brand-seeds.json**: Seed data for brand-related domains
- **stopwords.json**: Stop words used in text processing and analysis

This directory serves as a centralized repository for reference data that may be used by multiple services, ensuring consistency in domain categorization and analysis across the system.

**Section sources**
- [data/categories.json](file://data/categories.json)
- [README.md](file://README.md#L165-L175)

### database Directory

The `database` directory contains scripts and configuration files for database initialization and management. This directory supports the system's multiple database technologies, including relational, NoSQL, and search databases.

The directory structure includes:
- **manticore/init.sql**: Initialization script for Manticore Search, a full-text search engine used for domain search functionality
- **mariadb/init.sql**: Initialization script for MariaDB, a relational database used for structured data storage
- **sample-data**: Sample JSON data for categories and domains used in development and testing
- **scripts**: JavaScript scripts for database migration and management tasks

This directory plays a crucial role in ensuring consistent database schema and initial data across development, testing, and production environments.

**Section sources**
- [database/mariadb/init.sql](file://database/mariadb/init.sql)
- [database/scripts/migrate.js](file://database/scripts/migrate.js)
- [README.md](file://README.md#L180-L195)

### monitoring Directory

The `monitoring` directory contains configuration files for system monitoring and observability tools. This directory supports the operational health and performance monitoring of the domainr system.

The primary file in this directory is:
- **prometheus.yml**: Configuration for Prometheus, an open-source monitoring and alerting toolkit. This configuration defines the metrics to collect, scrape intervals, and alerting rules for the various services in the system.

This directory enables centralized monitoring of the microservices architecture, providing visibility into system performance, error rates, and resource utilization across all services.

**Section sources**
- [monitoring/prometheus.yml](file://monitoring/prometheus.yml)
- [README.md](file://README.md#L200-L210)

### nginx Directory

The `nginx` directory contains configuration files for the NGINX web server, which serves as a reverse proxy and load balancer for the domainr system. This directory supports the deployment and routing of HTTP requests to the appropriate backend services.

The directory structure includes:
- **conf.d/default.conf**: Virtual host configuration defining server blocks, routing rules, and SSL settings
- **nginx.conf**: Main NGINX configuration file with global settings for worker processes, logging, and performance tuning

These configuration files enable the system to handle incoming HTTP requests, route them to the appropriate services (e.g., web-app, admin), and provide SSL termination and other web server functionality.

**Section sources**
- [nginx/nginx.conf](file://nginx/nginx.conf)
- [nginx/conf.d/default.conf](file://nginx/conf.d/default.conf)
- [README.md](file://README.md#L215-L225)

### scripts Directory

The `scripts` directory contains shell and TypeScript scripts for deployment, environment setup, and operational tasks. This directory supports the automation of common development and operations workflows.

Key scripts include:
- **deploy.sh**: Main deployment script that orchestrates the deployment process
- **deploy-domain-seeder.sh**: Service-specific deployment script for the domain-seeder service
- **setup-environment.sh**: Script to configure the development environment
- **backup.sh**: Script for creating system backups
- **validate-docker-config.sh**: Script to validate Docker configuration files

These scripts provide a consistent interface for common operations, reducing the potential for human error and ensuring that tasks are performed the same way across different environments and by different team members.

**Section sources**
- [scripts/deploy.sh](file://scripts/deploy.sh)
- [scripts/setup-environment.sh](file://scripts/setup-environment.sh)
- [README.md](file://README.md#L230-L245)

### services Directory

The `services` directory contains the main application services that implement the core functionality of the domainr system. Each subdirectory represents a microservice with its own codebase, dependencies, and lifecycle.

The services architecture follows a microservices pattern, with each service responsible for a specific domain of functionality. This separation enables independent development, deployment, and scaling of different system components.

The services in this directory include:
- **admin**: Administrative interface for system management
- **domain-seeder**: Domain discovery and seeding service
- **web-app**: Main web application and API service
- **worker**: Background processing and job execution service

Each service is designed to be independently deployable and scalable, communicating with other services through well-defined APIs and message queues.

**Section sources**
- [services/admin/README.md](file://services/admin/README.md#L1-L50)
- [services/domain-seeder/README.md](file://services/domain-seeder/README.md#L1-L50)
- [services/web-app/README.md](file://services/web-app/README.md#L1-L50)
- [services/worker/README.md](file://services/worker/README.md#L1-L50)

### shared Directory

The `shared` directory contains libraries and utilities that are used across multiple services in the monorepo. This directory promotes code reuse and consistency across the system while avoiding duplication.

The shared codebase includes:
- **Database clients**: Common database connection and query utilities for ScyllaDB, MariaDB, Redis, and Manticore
- **Data models**: Shared TypeScript interfaces and types for domain entities like DomainAnalysis, DomainRanking, and SearchResult
- **Utilities**: Common functions for logging, validation, configuration management, and HTTP requests
- **Middleware**: Shared Express middleware for error handling, rate limiting, and security
- **Queue management**: Job queue utilities for inter-service communication

By centralizing these common components, the shared directory reduces code duplication and ensures consistent behavior across services, such as uniform error handling and logging formats.

**Section sources**
- [shared/src/index.ts](file://shared/src/index.ts)
- [shared/src/models/DomainTypes.ts](file://shared/src/models/types/DomainTypes.ts)
- [README.md](file://README.md#L250-L265)

## Service Directory Roles

Each service in the `services` directory plays a distinct role in the domainr system, contributing to the overall domain ranking and analysis functionality. These services are designed to work together as a cohesive system while maintaining independence in their implementation and deployment.

### admin Service

The admin service provides a comprehensive administrative interface for managing and monitoring the domainr system. Built with Next.js 14 and Mantine UI, this service offers system administrators complete control over the platform's operations.

Key responsibilities of the admin service include:
- **Service Health Monitoring**: Real-time status of all microservices with detailed performance metrics
- **Domain Management**: CRUD operations for domain data, including bulk operations and data import/export
- **Crawl Job Management**: Control and monitoring of crawling operations, with the ability to start, stop, and configure crawl jobs
- **User Management**: Role-based access control for administrators with audit logging of all administrative actions
- **System Configuration**: Management of system settings and parameters through a user-friendly interface
- **Analytics Dashboard**: Comprehensive system metrics and reporting with data visualization
- **System Logs**: Advanced log viewing and debugging tools with filtering and search capabilities

The admin service integrates with all major system components, including ScyllaDB, MariaDB, Redis, and Manticore, providing a centralized interface for system administration.

**Section sources**
- [services/admin/README.md](file://services/admin/README.md#L50-L100)
- [services/admin/src/components/Alerts/README.md](file://services/admin/src/components/Alerts/README.md)
- [services/admin/src/components/Users/<USER>//services/admin/src/components/Users/<USER>

### domain-seeder Service

The domain-seeder service is responsible for discovering and seeding new domains into the system. This service implements multiple discovery strategies to identify domains that should be analyzed and ranked.

Key responsibilities of the domain-seeder service include:
- **Domain Discovery**: Identifying new domains through various strategies including differential analysis, zone file processing, and long-tail exploration
- **Content Generation**: Creating domain descriptions using both pre-generated content and live analysis
- **Source Integration**: Connecting to external data sources such as Cloudflare Radar, CZDS, Cisco Umbrella, and PIR Registry
- **Rate Limiting**: Managing API rate limits for external services to ensure reliable data collection
- **Monitoring**: Exposing Prometheus metrics for monitoring discovery performance and system health

The domain-seeder service uses a combination of CLI commands and HTTP APIs to provide flexibility in how domain discovery is triggered and managed, supporting both automated and manual operations.

**Section sources**
- [services/domain-seeder/README.md](file://services/domain-seeder/README.md#L50-L100)
- [services/domain-seeder/src/discovery/README.md](file://services/domain-seeder/src/discovery/README.md)
- [services/domain-seeder/src/content/README.md](file://services/domain-seeder/src/content/README.md)

### web-app Service

The web-app service serves as the main user-facing application, providing both a web interface and API endpoints for domain search and analysis. This service is built with Express.js and React, offering a server-side rendered interface for optimal performance and SEO.

Key responsibilities of the web-app service include:
- **User Interface**: Providing a responsive web interface for domain search, analysis, and comparison
- **API Endpoints**: Exposing RESTful APIs for domain search, analysis, and ranking data
- **Server-Side Rendering**: Rendering React components on the server for improved performance and SEO
- **Authentication**: Handling user authentication and session management
- **Caching**: Implementing response caching to improve performance for frequently accessed data

The web-app service acts as the primary entry point for users, aggregating data from various backend services and presenting it in a user-friendly format.

**Section sources**
- [services/web-app/README.md](file://services/web-app/README.md#L50-L100)
- [services/web-app/IMPLEMENTATION_SUMMARY.md](file://services/web-app/IMPLEMENTATION_SUMMARY.md)

### worker Service

The worker service is a consolidated, horizontally scalable domain processing worker that replaces multiple specialized services (crawler, ranking-engine, and scheduler) by consolidating their functionality into a single service. This design simplifies operations and improves resource utilization.

Key responsibilities of the worker service include:
- **Domain Processing Pipeline**: Executing a complete pipeline of crawling, analysis, ranking, and indexing for domains
- **Job Queue Management**: Processing jobs from a shared queue with support for multiple worker instances
- **Distributed Locking**: Using Redis-based domain locking to coordinate work across multiple worker instances
- **Error Handling**: Implementing robust error handling and retry mechanisms for failed jobs
- **Monitoring**: Exposing comprehensive health endpoints and metrics for observability

The worker service processes domains through a sequential pipeline that includes crawling (DNS analysis, SSL analysis, homepage analysis), ranking (performance scoring, security scoring, SEO scoring), and indexing (database updates, search index synchronization).

**Section sources**
- [services/worker/README.md](file://services/worker/README.md#L50-L100)
- [services/worker/src/pipeline/README.md](file://services/worker/src/pipeline/README.md)
- [services/worker/src/queue/README.md](file://services/worker/src/queue/README.md)

## Internal Service Organization

Each service in the `services` directory follows a consistent internal organization that promotes maintainability, testability, and scalability. This standardized structure makes it easier for developers to navigate and contribute to different services within the monorepo.

### Common Service Structure

Most services follow a similar directory structure that separates concerns and organizes code by functionality:

```
service/
├── src/
│   ├── app/                 # Main application logic
│   ├── components/         # Reusable UI components
│   ├── lib/               # Core libraries and utilities
│   ├── types/             # TypeScript type definitions
│   ├── utils/             # Utility functions
│   └── middleware.ts      # Application middleware
├── tests/                # Test files
├── scripts/              # Service-specific scripts
├── Dockerfile           # Docker configuration
├── package.json         # Service dependencies
└── tsconfig.json        # TypeScript configuration
```

This structure ensures that code is organized in a predictable way, making it easier for developers to find and understand the codebase.

### Service-Specific Organization

While following the common structure, each service also has specialized directories that support its specific functionality:

#### admin Service Organization

The admin service, being a Next.js application, follows the App Router pattern with a focus on pages and components:

- **app/**: Next.js App Router pages including dashboard, login, and API routes
- **components/**: Reusable UI components organized by feature (e.g., Alerts, Analytics, Auth)
- **lib/**: Core libraries for configuration, theme, and API clients
- **types/**: TypeScript definitions for application data structures
- **utils/**: Utility functions for validation, formatting, and other common tasks

The admin service also includes specialized directories for monitoring and security, reflecting its role as a system management interface.

#### domain-seeder Service Organization

The domain-seeder service is organized around its core functionality of domain discovery and content generation:

- **discovery/**: Implementation of different discovery strategies (differential, zone-file, long-tail)
- **content/**: Domain description generation and content analysis
- **connectors/**: Integration with external data sources (Cloudflare, CZDS, Umbrella)
- **scheduling/**: Job scheduling and orchestration
- **validation/**: Data validation and error recovery
- **monitoring/**: Health checks and metrics collection

This organization reflects the service's focus on discovering and processing domain data from various sources.

#### web-app Service Organization

The web-app service is organized to support both server-side rendering and API functionality:

- **app/**: Next.js App Router pages for the user interface
- **components/**: Reusable UI components for search, domain analysis, and navigation
- **services/**: Business logic for domain search, analysis, and comparison
- **middleware/**: Express middleware for request processing
- **routes/**: API route handlers
- **utils/**: Utility functions for rendering, formatting, and data processing

This structure supports the service's dual role as both a web application and an API provider.

#### worker Service Organization

The worker service is organized around its domain processing pipeline and job execution model:

- **pipeline/**: Implementation of the domain processing pipeline (crawling, analysis, ranking, indexing)
- **queue/**: Job queue management and consumer logic
- **crawler/**: Web crawling and data extraction functionality
- **ranking/**: Ranking algorithms and scoring calculations
- **database/**: Database operations and migrations
- **monitoring/**: Health checks, metrics collection, and alerting
- **locking/**: Distributed domain locking mechanism

This organization reflects the service's role as a background processing engine that handles computationally intensive tasks.

**Section sources**
- [services/admin/README.md](file://services/admin/README.md#L250-L300)
- [services/domain-seeder/README.md](file://services/domain-seeder/README.md#L300-L350)
- [services/web-app/README.md](file://services/web-app/README.md#L200-L250)
- [services/worker/README.md](file://services/worker/README.md#L200-L250)

## Relationship to Microservices Architecture

The directory structure of the domainr repository directly reflects and supports its microservices architecture, with each major component of the system implemented as an independent service with well-defined responsibilities.

### Service Boundaries and Responsibilities

The microservices architecture is evident in the clear separation of concerns between services:

- **Single Responsibility Principle**: Each service has a well-defined, focused responsibility (e.g., admin for system management, domain-seeder for domain discovery, web-app for user interface, worker for background processing)
- **Independent Deployability**: Each service can be developed, tested, and deployed independently, enabling faster iteration and reduced risk
- **Technology Flexibility**: While most services use Node.js and TypeScript, the architecture allows for different technology stacks if needed for specific services
- **Scalability**: Services can be scaled independently based on their resource requirements and usage patterns

This separation of concerns enables the system to handle high volumes of domain data and user requests by scaling individual services as needed.

### Inter-Service Communication

The services communicate with each other through well-defined interfaces, primarily using HTTP APIs and message queues:

- **HTTP APIs**: Services expose RESTful endpoints for synchronous communication (e.g., the admin service calling the domain-seeder API to trigger discovery)
- **Message Queues**: Asynchronous communication is handled through Redis-based queues, allowing services to process jobs independently (e.g., the web-app service adding domains to analyze, and the worker service processing them)
- **Shared Databases**: Services share data through common databases (ScyllaDB, MariaDB, Redis, Manticore), with each service responsible for specific data models and operations

This communication pattern enables loose coupling between services while maintaining data consistency and system reliability.

### Data Management

The microservices architecture is supported by a polyglot persistence model, with different databases optimized for specific use cases:

- **ScyllaDB**: Main data storage for domain analysis and rankings, chosen for its high performance and scalability
- **MariaDB**: Relational data storage for structured data like categories and backlinks
- **Manticore Search**: Full-text search and faceted filtering for domain search functionality
- **Redis**: Caching and session storage, as well as job queue management

This approach allows each service to use the most appropriate database technology for its specific needs while maintaining data consistency through well-defined access patterns.

### Operational Benefits

The directory structure and microservices architecture provide several operational benefits:

- **Fault Isolation**: Issues in one service are less likely to affect other services, improving overall system reliability
- **Independent Scaling**: Services can be scaled independently based on their resource requirements (e.g., scaling worker instances during peak processing times)
- **Technology Evolution**: Individual services can be updated or replaced without affecting the entire system
- **Team Autonomy**: Different teams can work on different services with minimal coordination overhead

This architecture enables the domainr system to handle the complex requirements of domain ranking and analysis while maintaining operational efficiency and developer productivity.

**Section sources**
- [README.md](file://README.md#L300-L350)
- [services/admin/README.md](file://services/admin/README.md#L500-L550)
- [services/worker/README.md](file://services/worker/README.md#L500-L550)

## Developer Navigation and Workflow

The directory structure of the domainr repository is designed to support efficient developer workflows, making it easy for developers to navigate the codebase, understand service responsibilities, and contribute to the system.

### Getting Started

New developers can quickly get started with the system by following these steps:

1. **Clone the repository and install dependencies**:
   ```bash
   git clone <repository-url>
   cd domainr
   pnpm install
   ```

2. **Set up environment variables**:
   ```bash
   cp .env.example .env
   # Edit .env with appropriate configuration
   ```

3. **Start the development environment**:
   ```bash
   # Start all services
   pnpm dev
   
   # Or start individual services
   cd services/web-app
   pnpm dev
   ```

The consistent structure across services means that once a developer understands one service, they can quickly navigate and contribute to others.

### Development Commands

The pnpm workspace enables powerful development workflows through commands that can operate across multiple services:

- **Run scripts across all services**:
  ```bash
  pnpm -r run dev    # Start development mode for all services
  pnpm -r run test   # Run tests for all services
  pnpm -r run build  # Build all services
  ```

- **Run scripts in specific services**:
  ```bash
  pnpm --filter web-app run dev
  pnpm --filter worker run test
  ```

- **Run scripts in parallel**:
  ```bash
  pnpm -r --parallel run dev
  ```

These commands streamline common development tasks and ensure consistency across the codebase.

### Code Navigation

The standardized directory structure makes it easy to navigate the codebase:

- **Configuration**: All shared configuration is in the `_config` directory
- **Static data**: Reference data is in the `data` directory
- **Database scripts**: Initialization and migration scripts are in the `database` directory
- **Monitoring configuration**: Prometheus configuration is in the `monitoring` directory
- **Deployment scripts**: Operational scripts are in the `scripts` directory
- **Services**: Main application logic is in the `services` directory
- **Shared code**: Common utilities and models are in the `shared` directory

This organization allows developers to quickly locate the code they need to work on, regardless of which service it belongs to.

### Contribution Guidelines

When contributing to the codebase, developers should follow these guidelines:

1. **Maintain service boundaries**: Add new functionality to the appropriate service based on its responsibility
2. **Use shared code when possible**: Leverage utilities and models in the `shared` directory to avoid duplication
3. **Follow coding standards**: Adhere to the ESLint and TypeScript configurations in the `_config` directory
4. **Write tests**: Add appropriate tests for new functionality
5. **Update documentation**: Keep README files and other documentation up to date

The consistent structure and tooling across services make it easier for developers to contribute to different parts of the system, promoting knowledge sharing and reducing the learning curve for new team members.

**Section sources**
- [README.md](file://README.md#L400-L450)
- [services/admin/README.md](file://services/admin/README.md#L600-L650)
- [services/worker/README.md](file://services/worker/README.md#L600-L650)