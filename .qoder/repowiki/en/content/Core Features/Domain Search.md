# Domain Search

<cite>
**Referenced Files in This Document**   
- [DomainSearchService.ts](file://services/web-app/src/services/DomainSearchService.ts)
- [SearchForm.tsx](file://services/web-app/src/components/Search/SearchForm.tsx)
- [SearchResults.tsx](file://services/web-app/src/components/Search/SearchResults.tsx)
- [ManticoreClient.ts](file://shared/src/database/ManticoreClient.ts)
- [init.sql](file://database/manticore/init.sql)
- [SearchResult.ts](file://shared/src/models/SearchResult.ts)
- [CachingService.ts](file://shared/src/services/CachingService.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Core Components](#core-components)
3. [Architecture Overview](#architecture-overview)
4. [Detailed Component Analysis](#detailed-component-analysis)
5. [Full-Text Search and Query Parsing](#full-text-search-and-query-parsing)
6. [Faceted Filtering and Sorting](#faceted-filtering-and-sorting)
7. [Search Performance and Optimization](#search-performance-and-optimization)
8. [Caching Strategy](#caching-strategy)
9. [Extending Search Functionality](#extending-search-functionality)
10. [API Integration](#api-integration)
11. [Conclusion](#conclusion)

## Introduction
The Domain Search feature provides a comprehensive search interface for discovering and analyzing domains based on various criteria including keywords, categories, technologies, and performance metrics. Built on Manticore Search, the system supports full-text search, faceted filtering, and real-time suggestions. The frontend components interact with a backend service layer that orchestrates queries to the search engine and applies business logic for result processing.

## Core Components
The Domain Search functionality is implemented through a set of interconnected components that handle user input, execute searches, and display results. The system architecture separates concerns between the user interface, service logic, and data access layers to ensure maintainability and scalability.

**Section sources**
- [DomainSearchService.ts](file://services/web-app/src/services/DomainSearchService.ts#L1-L303)
- [SearchForm.tsx](file://services/web-app/src/components/Search/SearchForm.tsx#L1-L208)
- [SearchResults.tsx](file://services/web-app/src/components/Search/SearchResults.tsx#L1-L65)

## Architecture Overview
The Domain Search system follows a layered architecture with clear separation between presentation, service, and data access components. The frontend SearchForm and SearchResults components interact with the DomainSearchService, which in turn communicates with the ManticoreClient to execute queries against the Manticore Search engine.

```mermaid
graph TD
A[SearchForm] --> B[DomainSearchService]
C[SearchResults] --> B
B --> D[ManticoreClient]
D --> E[Manticore Search Engine]
F[CachingService] --> B
G[SearchResult Model] --> C
H[Search Filters] --> A
I[Pagination] --> C
style A fill:#f9f,stroke:#333
style B fill:#bbf,stroke:#333
style C fill:#f9f,stroke:#333
style D fill:#9cf,stroke:#333
style E fill:#cfc,stroke:#333
style F fill:#fcb,stroke:#333
```

**Diagram sources**
- [SearchForm.tsx](file://services/web-app/src/components/Search/SearchForm.tsx#L1-L208)
- [SearchResults.tsx](file://services/web-app/src/components/Search/SearchResults.tsx#L1-L65)
- [DomainSearchService.ts](file://services/web-app/src/services/DomainSearchService.ts#L1-L303)
- [ManticoreClient.ts](file://shared/src/database/ManticoreClient.ts#L1-L530)

## Detailed Component Analysis

### SearchForm Component
The SearchForm component provides the user interface for entering search queries and triggering domain searches. It includes features such as real-time suggestions, keyboard navigation, and query clearing functionality.

```mermaid
flowchart TD
Start([User Interaction]) --> InputChange["Handle Input Change"]
InputChange --> QueryLength{"Query Length >= 2?"}
QueryLength --> |Yes| FetchSuggestions["Fetch Search Suggestions"]
QueryLength --> |No| ClearSuggestions["Clear Suggestions"]
FetchSuggestions --> UpdateUI["Update Suggestions UI"]
SubmitEvent["Form Submit"] --> ValidateQuery{"Query Valid?"}
ValidateQuery --> |Yes| ExecuteSearch["Call onSearch with query"]
ValidateQuery --> |No| Ignore["Ignore submission"]
ClearEvent["Clear Button Click"] --> ResetForm["Reset query and clear suggestions"]
KeyboardEvent["Keyboard Navigation"] --> ArrowDown{"Arrow Down?"}
ArrowDown --> |Yes| NextSuggestion["Move to next suggestion"]
ArrowUp["Arrow Up?"] --> |Yes| PreviousSuggestion["Move to previous suggestion"]
Enter["Enter Key?"] --> |Yes| SelectSuggestion["Select active suggestion"]
Escape["Escape Key?"] --> |Yes| HideSuggestions["Hide suggestions dropdown"]
SuggestionClick["Suggestion Clicked"] --> UseSuggestion["Use clicked suggestion as query"]
style Start fill:#f9f,stroke:#333
style InputChange fill:#bbf,stroke:#333
style FetchSuggestions fill:#9cf,stroke:#333
style UpdateUI fill:#9cf,stroke:#333
style ExecuteSearch fill:#cfc,stroke:#333
style ResetForm fill:#cfc,stroke:#333
```

**Diagram sources**
- [SearchForm.tsx](file://services/web-app/src/components/Search/SearchForm.tsx#L1-L208)

**Section sources**
- [SearchForm.tsx](file://services/web-app/src/components/Search/SearchForm.tsx#L1-L208)

### SearchResults Component
The SearchResults component renders the search results in a grid layout using DomainCard components. It handles loading states and empty result conditions with appropriate visual feedback.

```mermaid
flowchart TD
A[SearchResults Component] --> B{"isLoading?"}
B --> |Yes| C[Render Loading Skeleton]
B --> |No| D{"results.length > 0?"}
D --> |No| E[Render Empty State]
D --> |Yes| F[Render Results Grid]
F --> G[Map results to DomainCard components]
G --> H[Display domain information]
H --> I[Handle domain click events]
style A fill:#f9f,stroke:#333
style C fill:#ff9,stroke:#333
style E fill:#f99,stroke:#333
style F fill:#9f9,stroke:#333
```

**Diagram sources**
- [SearchResults.tsx](file://services/web-app/src/components/Search/SearchResults.tsx#L1-L65)

**Section sources**
- [SearchResults.tsx](file://services/web-app/src/components/Search/SearchResults.tsx#L1-L65)

### DomainSearchService
The DomainSearchService orchestrates search operations by transforming user parameters into Manticore Search queries and processing the results. It provides methods for domain search, suggestions, and statistics.

```mermaid
classDiagram
class DomainSearchService {
+dbManager : DatabaseManager
+manticoreClient : ManticoreClient
+logger : Logger
+searchDomains(params) : Promise~SearchResult~
+getTopDomains(category, options) : Promise~TopDomainsResult~
+compareDomains(domains) : Promise~CompareResult~
+getSearchSuggestions(query, limit) : Promise~string[]~
+getPopularSearches() : Promise~PopularSearchesResult~
+getSearchStats() : Promise~SearchStatsResult~
}
class ManticoreClient {
+client : HttpClient
+logger : Logger
+baseUrl : string
+searchDomains(params) : Promise~ManticoreResponse~
+getTopDomains(params) : Promise~ManticoreResponse~
+compareDomains(domains) : Promise~ManticoreResponse~
+buildSearchQuery(params) : SearchQuery
+buildFilters(filters) : FilterObject
+buildSort(sort) : SortObject
+parseSearchResponse(response) : ParsedResponse
}
DomainSearchService --> ManticoreClient : "uses"
DomainSearchService --> SearchResult : "returns"
ManticoreClient --> HttpClient : "uses"
```

**Diagram sources**
- [DomainSearchService.ts](file://services/web-app/src/services/DomainSearchService.ts#L1-L303)
- [ManticoreClient.ts](file://shared/src/database/ManticoreClient.ts#L1-L530)

**Section sources**
- [DomainSearchService.ts](file://services/web-app/src/services/DomainSearchService.ts#L1-L303)

## Full-Text Search and Query Parsing
The Domain Search system implements full-text search capabilities through Manticore Search, allowing users to search across multiple domain attributes including domain name, title, description, and technologies. The query parsing system supports both simple keyword searches and advanced query syntax.

When a search query is submitted, the system constructs a Manticore Search query that performs a boolean search across multiple fields with a minimum_should_match parameter of 1, ensuring that documents matching any of the search criteria are returned. The search is case-insensitive and supports partial matching through wildcard expansion in suggestions.

```mermaid
sequenceDiagram
participant User as "User"
participant Form as "SearchForm"
participant Service as "DomainSearchService"
participant Client as "ManticoreClient"
participant Engine as "Manticore Search Engine"
User->>Form : Enters search query
Form->>Service : onSearch(query)
Service->>Client : searchDomains({query : params})
Client->>Client : buildSearchQuery()
Client->>Engine : POST /search with query
Engine-->>Client : Return search results
Client->>Client : parseSearchResponse()
Client-->>Service : Return parsed results
Service-->>Form : Return formatted results
Form->>User : Display results in SearchResults
Note over Client,Engine : Query spans domain, title,<br/>description, and technologies
```

**Diagram sources**
- [DomainSearchService.ts](file://services/web-app/src/services/DomainSearchService.ts#L1-L303)
- [ManticoreClient.ts](file://shared/src/database/ManticoreClient.ts#L1-L530)

**Section sources**
- [ManticoreClient.ts](file://shared/src/database/ManticoreClient.ts#L1-L530)

## Faceted Filtering and Sorting
The search system supports faceted filtering that allows users to narrow results based on categorical dimensions such as domain category, country, technology stack, and SSL grade. The filtering system also supports numerical ranges for metrics like global rank and overall score.

Sorting options include relevance (default), global rank, overall score, performance, security, SEO, domain name, and traffic estimate. The system applies default sorting by overall score (descending) and global rank (ascending) when no specific sort parameter is provided.

```mermaid
flowchart TD
A[Search Parameters] --> B{Has Filters?}
B --> |Yes| C[Apply Category Filter]
B --> |Yes| D[Apply Country Filter]
B --> |Yes| E[Apply Technology Filter]
B --> |Yes| F[Apply SSL Grade Filter]
B --> |Yes| G[Apply Rank Range Filter]
B --> |Yes| H[Apply Score Range Filter]
A --> I{Has Sort?}
I --> |Yes| J[Apply Sort: rank]
I --> |Yes| K[Apply Sort: score]
I --> |Yes| L[Apply Sort: performance]
I --> |Yes| M[Apply Sort: security]
I --> |Yes| N[Apply Sort: seo]
I --> |Yes| O[Apply Sort: domain]
I --> |Yes| P[Apply Sort: traffic]
I --> |No| Q[Apply Default Sort]
C --> R[Build Manticore Filters]
D --> R
E --> R
F --> R
G --> R
H --> R
J --> S[Build Sort Configuration]
K --> S
L --> S
M --> S
N --> S
O --> S
P --> S
Q --> S
R --> T[Execute Search]
S --> T
style R fill:#9cf,stroke:#333
style S fill:#9cf,stroke:#333
style T fill:#cfc,stroke:#333
```

**Diagram sources**
- [DomainSearchService.ts](file://services/web-app/src/services/DomainSearchService.ts#L1-L303)
- [ManticoreClient.ts](file://shared/src/database/ManticoreClient.ts#L1-L530)

**Section sources**
- [DomainSearchService.ts](file://services/web-app/src/services/DomainSearchService.ts#L1-L303)

## Search Performance and Optimization
The Domain Search system implements several performance optimizations to ensure responsive search experiences even with large datasets. These include query optimization, result caching, and efficient data retrieval patterns.

Common performance issues include slow response times for complex queries with multiple filters, high memory usage during facet computation, and latency in suggestion retrieval. Optimization techniques include proper indexing strategies, query simplification, and result set size management.

```mermaid
flowchart TD
A[Performance Issues] --> B[Slow Query Response]
A --> C[High Memory Usage]
A --> D[Latency in Suggestions]
A --> E[Large Result Sets]
B --> F[Optimization: Query Profiling]
C --> G[Optimization: Facet Size Limits]
D --> H[Optimization: Debounced Requests]
E --> I[Optimization: Pagination]
F --> J[Analyze query execution plan]
G --> K[Limit facet bucket size to 20]
H --> L[Implement 300ms debounce]
I --> M[Use limit/offset pagination]
J --> N[Optimize index structure]
K --> N
L --> O[Reduce server load]
M --> O
N --> P[Improved Search Performance]
O --> P
style P fill:#cfc,stroke:#333
```

**Section sources**
- [ManticoreClient.ts](file://shared/src/database/ManticoreClient.ts#L1-L530)
- [DomainSearchService.ts](file://services/web-app/src/services/DomainSearchService.ts#L1-L303)

## Caching Strategy
The system implements a multi-layer caching strategy to improve search performance and reduce load on the Manticore Search engine. The CachingService is used to store frequently accessed search results and suggestions.

Cache invalidation occurs when domain data is updated, ensuring that search results remain current. The system uses time-based expiration for cached results, with a default TTL that balances freshness and performance.

```mermaid
sequenceDiagram
participant Client as "Search Client"
participant Service as "DomainSearchService"
participant Cache as "CachingService"
participant Manticore as "ManticoreClient"
Client->>Service : searchDomains(params)
Service->>Cache : get(cacheKey)
Cache-->>Service : null (cache miss)
Service->>Manticore : execute search
Manticore-->>Service : return results
Service->>Cache : set(cacheKey, results, TTL)
Service-->>Client : return results
Client->>Service : searchDomains(same params)
Service->>Cache : get(cacheKey)
Cache-->>Service : return cached results
Service-->>Client : return cached results
Note over Service,Cache : Cache key includes<br/>query, filters, and sort
Note over Service,Manticore : Cache miss triggers<br/>actual search execution
```

**Diagram sources**
- [CachingService.ts](file://shared/src/services/CachingService.ts#L1-L100)
- [DomainSearchService.ts](file://services/web-app/src/services/DomainSearchService.ts#L1-L303)

**Section sources**
- [CachingService.ts](file://shared/src/services/CachingService.ts#L1-L100)

## Extending Search Functionality
The Domain Search system can be extended with custom filters and boost parameters to support specialized search requirements. Developers can modify the search query construction process to add new filtering dimensions or adjust relevance scoring.

To add a custom filter, developers can extend the filter building logic in the ManticoreClient to support additional domain attributes. For custom boost parameters, the query construction can be modified to apply field-specific weighting based on search context.

```mermaid
classDiagram
class CustomSearchExtension {
+addCustomFilter(field, value)
+addBoostParameter(field, weight)
+modifyQueryPipeline()
+registerCustomScorer()
}
class ManticoreClient {
+buildSearchQuery()
+buildFilters()
+buildSort()
+parseSearchResponse()
}
class DomainSearchService {
+searchDomains()
+getSearchSuggestions()
}
CustomSearchExtension --> ManticoreClient : "extends"
CustomSearchExtension --> DomainSearchService : "enhances"
note right of CustomSearchExtension
Extensions can add :
- Custom filters for new domain attributes
- Boost parameters to influence relevance
- Custom scoring algorithms
- Query preprocessing rules
end
```

**Section sources**
- [ManticoreClient.ts](file://shared/src/database/ManticoreClient.ts#L1-L530)

## API Integration
The Domain Search feature integrates with backend API endpoints through the DomainSearchService, which acts as a facade for Manticore Search operations. The frontend components communicate with the service layer, which handles the translation between application-level parameters and search engine queries.

The API integration follows a consistent pattern where search parameters are normalized and validated before being passed to the ManticoreClient. Response data is then transformed into a format suitable for frontend consumption, with proper error handling and logging.

```mermaid
sequenceDiagram
participant Frontend as "Web App"
participant Service as "DomainSearchService"
participant API as "Manticore API"
Frontend->>Service : searchDomains(params)
Service->>Service : Normalize parameters
Service->>Service : Validate inputs
Service->>API : Build and send query
API-->>Service : Return raw results
Service->>Service : Parse and transform results
Service->>Service : Add pagination metadata
Service->>Frontend : Return formatted response
Frontend->>Service : getSuggestions(query)
Service->>API : searchDomains(query*)
API-->>Service : Return matching domains
Service->>Service : Extract unique suggestions
Service->>Frontend : Return suggestion list
Note over Service,API : HTTP/JSON communication
Note over Service,Frontend : TypeScript interfaces ensure<br/>type safety across layers
```

**Diagram sources**
- [DomainSearchService.ts](file://services/web-app/src/services/DomainSearchService.ts#L1-L303)
- [ManticoreClient.ts](file://shared/src/database/ManticoreClient.ts#L1-L530)

**Section sources**
- [DomainSearchService.ts](file://services/web-app/src/services/DomainSearchService.ts#L1-L303)

## Conclusion
The Domain Search feature provides a robust and scalable solution for discovering and analyzing domains through a combination of full-text search, faceted filtering, and real-time suggestions. The architecture separates concerns between frontend components, service logic, and data access layers, enabling maintainability and extensibility. By leveraging Manticore Search as the underlying engine, the system delivers high-performance search capabilities with support for complex queries and large datasets. The caching strategy and optimization techniques ensure responsive user experiences, while the modular design allows for easy extension with custom filters and scoring parameters.