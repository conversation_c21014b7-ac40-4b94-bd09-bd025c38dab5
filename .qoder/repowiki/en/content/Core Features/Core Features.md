# Core Features

<cite>
**Referenced Files in This Document**   
- [SearchPage.tsx](file://services/web-app/src/components/SearchPage.tsx)
- [DomainAnalysisPage.tsx](file://services/web-app/src/components/DomainAnalysisPage.tsx)
- [DomainComparisonPage.tsx](file://services/web-app/src/components/DomainComparisonPage.tsx)
- [TopDomainsPage.tsx](file://services/web-app/src/components/TopDomainsPage.tsx)
- [DomainSearchService.ts](file://services/web-app/src/services/DomainSearchService.ts)
- [DomainAnalysisService.ts](file://services/web-app/src/services/DomainAnalysisService.ts)
- [TopDomainsService.ts](file://services/web-app/src/services/TopDomainsService.ts)
- [DomainAnalyzer.ts](file://services/domain-seeder/src/services/DomainAnalyzer.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Domain Search](#domain-search)
3. [Domain Analysis](#domain-analysis)
4. [Top Domains](#top-domains)
5. [Domain Comparison](#domain-comparison)
6. [Automated Crawling](#automated-crawling)
7. [Troubleshooting Guide](#troubleshooting-guide)
8. [Conclusion](#conclusion)

## Introduction
The domainr platform provides comprehensive domain intelligence through several core features: Domain Search, Domain Analysis, Top Domains, Domain Comparison, and Automated Crawling. These features are implemented through a combination of React components for the user interface and backend services that interact with various databases and external systems. The architecture follows a service-oriented pattern where frontend components invoke backend services that coordinate data retrieval from multiple sources including Manticore, ScyllaDB, MariaDB, and Redis. This document details the implementation, invocation relationships, interfaces, domain models, and usage patterns for each core feature, providing practical examples and integration guidance.

## Domain Search

The Domain Search feature enables users to search for domains with faceted filtering, pagination, and real-time suggestions. The implementation consists of the SearchPage component that orchestrates the search experience and the DomainSearchService that handles backend operations.

```mermaid
flowchart TD
A[SearchPage] --> B[SearchForm]
A --> C[SearchFilters]
A --> D[SearchResults]
B --> E[handleSearch]
E --> F[DomainSearchService.searchDomains]
F --> G[ManticoreClient.searchDomains]
G --> H[Database Query]
H --> I[Return Results]
I --> J[Update UI State]
K[SearchForm] --> L[getSearchSuggestions]
L --> M[DomainSearchService.getSearchSuggestions]
```

**Diagram sources**
- [SearchPage.tsx](file://services/web-app/src/components/SearchPage.tsx#L0-L799)
- [DomainSearchService.ts](file://services/web-app/src/services/DomainSearchService.ts#L0-L302)

The SearchPage component accepts initial data for server-side rendering and manages state for the search query, active filters, results, and pagination. When a user submits a search, the handleSearch function constructs URL parameters and makes a fetch request to the /api/domains/search endpoint. The DomainSearchService.searchDomains method normalizes input parameters, builds database filters, and delegates to the ManticoreClient for execution. The service supports multiple parameter overloads - it can accept either a query string with optional filters or a complete parameters object.

Key configuration options include:
- **query**: Search term (required)
- **category**: Filter by domain category
- **country**: Filter by country
- **technology**: Filter by technology stack
- **sslGrade**: Filter by SSL security grade
- **minRank/maxRank**: Filter by global rank range
- **minScore/maxScore**: Filter by overall score range
- **sort**: Sorting criteria (rank, score, performance, security, seo, traffic)
- **page**: Pagination page number
- **limit**: Results per page (1-100)

The service returns a structured response containing domains, pagination metadata, facets for filtering, applied filters, search duration, and total results. Facets include categories, countries, technologies, and SSL grades, enabling dynamic filter generation. The SearchPage component also implements real-time suggestions through the getSearchSuggestions method, which queries for domains matching the input prefix and returns up to 10 unique suggestions.

**Section sources**
- [SearchPage.tsx](file://services/web-app/src/components/SearchPage.tsx#L0-L799)
- [DomainSearchService.ts](file://services/web-app/src/services/DomainSearchService.ts#L0-L302)

## Domain Analysis

The Domain Analysis feature provides comprehensive metrics and insights for individual domains through the DomainAnalysisPage component and DomainAnalysisService. This feature aggregates data from multiple sources to present a holistic view of domain performance, security, SEO, and technical characteristics.

```mermaid
classDiagram
class DomainAnalysisPage {
+initialData : DomainAnalysisPageProps
-handleAnalysisRequest()
-renderHeader()
-renderMetrics()
-renderTechnologies()
-renderRankingExplanation()
}
class DomainAnalysisService {
-dbManager : DatabaseManager
-scyllaClient : ScyllaClient
-mariaClient : MariaClient
-manticoreClient : ManticoreClient
+getDomainAnalysis(domain : string)
+compareDomains(domains : string[])
+getDomainRankingExplanation(domain : string)
-getDomainWhoisInfo(domain : string)
-calculatePerformanceScore(metrics : any)
-calculateSecurityScore(securityMetrics : any, sslCertificate : any)
-calculateSEOScore(seoMetrics : any)
-calculateTechnicalScore(technicalMetrics : any)
-calculateOverallScore(metrics : any)
}
class DomainAnalysis {
+domain : string
+globalRank : number
+categoryRank : number
+category : string
+overallScore : number
+performanceScore : number
+securityScore : number
+seoScore : number
+technicalScore : number
+trafficEstimate : number
+domainAge : number
+sslGrade : string
+technologies : string[]
+metrics : PerformanceMetrics & SecurityMetrics & SEOMetrics & TechnicalMetrics
+rankingExplanation : RankingExplanation
}
DomainAnalysisPage --> DomainAnalysisService : "uses"
DomainAnalysisService --> DomainAnalysis : "returns"
DomainAnalysis --> PerformanceMetrics
DomainAnalysis --> SecurityMetrics
DomainAnalysis --> SEOMetrics
DomainAnalysis --> TechnicalMetrics
DomainAnalysis --> RankingExplanation
```

**Diagram sources**
- [DomainAnalysisPage.tsx](file://services/web-app/src/components/DomainAnalysisPage.tsx#L0-L599)
- [DomainAnalysisService.ts](file://services/web-app/src/services/DomainAnalysisService.ts#L0-L621)

The DomainAnalysisPage component renders a structured analysis view with overview cards, detailed metrics, technology detection, and ranking explanations. It accepts initialData containing either analysis results or an error message. When no initial data is provided, it displays a loading state while fetching analysis from the backend.

The DomainAnalysisService coordinates data retrieval from multiple databases:
- **ScyllaDB**: Primary source for domain analysis metrics including performance, security, SEO, and technical data
- **MariaDB**: Source for WHOIS information such as registrar, registration date, and expiration date
- **Manticore**: Used for domain comparison operations

The service implements several key methods:
- **getDomainAnalysis**: Retrieves comprehensive domain metrics and calculates scores using weighted algorithms
- **compareDomains**: Compares multiple domains across performance, security, SEO, and technical dimensions
- **getDomainRankingExplanation**: Provides detailed score breakdowns, strengths, weaknesses, and recommendations

Domain metrics are organized into categories with specific scoring algorithms:
- **Performance Score**: Based on load time, First Contentful Paint, Largest Contentful Paint, Cumulative Layout Shift, and First Input Delay
- **Security Score**: Based on SSL grade, security headers (HSTS, CSP, X-Frame-Options), and vulnerability detection
- **SEO Score**: Based on title length, meta description quality, robots.txt presence, sitemap availability, and structured data
- **Technical Score**: Based on page size, resource count, compression, HTTP version, and CDN usage

The service also normalizes domain names by removing protocols, www prefixes, and trailing slashes before querying. Error handling is implemented with comprehensive logging through the shared logger service.

**Section sources**
- [DomainAnalysisPage.tsx](file://services/web-app/src/components/DomainAnalysisPage.tsx#L0-L599)
- [DomainAnalysisService.ts](file://services/web-app/src/services/DomainAnalysisService.ts#L0-L621)

## Top Domains

The Top Domains feature displays ranked lists of domains based on comprehensive analysis metrics, with filtering by category, country, and timeframe. The implementation consists of the TopDomainsPage component and TopDomainsService, with extensive caching through Redis to optimize performance.

```mermaid
sequenceDiagram
participant User
participant TopDomainsPage
participant TopDomainsService
participant Redis
participant Manticore
participant ScyllaDB
User->>TopDomainsPage : Navigate to /top-domains
TopDomainsPage->>TopDomainsService : getTopDomains(params)
TopDomainsService->>Redis : GET cache_key
Redis-->>TopDomainsService : null (cache miss)
TopDomainsService->>Manticore : getTopDomains(query)
Manticore->>ScyllaDB : Execute query
ScyllaDB-->>Manticore : Return results
Manticore-->>TopDomainsService : Return domains
TopDomainsService->>Redis : SETEX cache_key TTL results
TopDomainsService-->>TopDomainsPage : Return formatted results
TopDomainsPage-->>User : Render Top Domains
```

**Diagram sources**
- [TopDomainsPage.tsx](file://services/web-app/src/components/TopDomainsPage.tsx#L0-L799)
- [TopDomainsService.ts](file://services/web-app/src/services/TopDomainsService.ts#L0-L666)

The TopDomainsPage component provides a rich interface with category, country, and timeframe filters, view mode toggling (table/cards), and pagination. It manages state for the current view mode, active filters, and pagination. The component supports both server-side rendering with initialData and client-side data fetching.

The TopDomainsService implements several caching strategies to optimize performance:
- **Top Domains Cache**: Caches top domain results for 1 hour with keys based on category and limit
- **Categories Cache**: Caches available categories for 2 hours
- **Rankings Cache**: Caches global and category rankings for 30 minutes

The service provides multiple methods for different use cases:
- **getTopDomains**: Retrieves top domains with category and country filtering, using Manticore for fast retrieval
- **getGlobalRankings**: Retrieves paginated global rankings from ScyllaDB with sorting options
- **getCategoryRankings**: Retrieves category-specific rankings with global rank context
- **getAvailableCategories**: Retrieves category metadata from Manticore facets with descriptive information
- **getTrendingDomains**: Identifies domains with significant rank changes over specified timeframes

Cache invalidation is implemented through the invalidateCache method, which can invalidate specific category caches or all top domains caches. The service uses a comprehensive logging system to monitor operations and performance.

**Section sources**
- [TopDomainsPage.tsx](file://services/web-app/src/components/TopDomainsPage.tsx#L0-L799)
- [TopDomainsService.ts](file://services/web-app/src/services/TopDomainsService.ts#L0-L666)

## Domain Comparison

The Domain Comparison feature enables side-by-side analysis of up to five domains through the DomainComparisonPage component. This feature leverages the DomainAnalysisService to retrieve and compare metrics across multiple dimensions, providing insights into relative performance, security, SEO, and technical characteristics.

```mermaid
flowchart TD
A[DomainComparisonPage] --> B[handleCompare]
B --> C[Fetch /api/domains/compare]
C --> D[DomainAnalysisService.compareDomains]
D --> E[Retrieve domain metrics]
E --> F[Calculate winners]
F --> G[Generate insights]
G --> H[Return comparison]
H --> I[Render OverviewComparison]
I --> J[Render MetricsComparison]
J --> K[Render RecommendationsSection]
```

**Diagram sources**
- [DomainComparisonPage.tsx](file://services/web-app/src/components/DomainComparisonPage.tsx#L0-L570)
- [DomainAnalysisService.ts](file://services/web-app/src/services/DomainAnalysisService.ts#L0-L621)

The DomainComparisonPage component provides a form interface for entering domains to compare, with support for adding and removing domains (minimum 2, maximum 5). When the user clicks "Compare Domains", the handleCompare function validates input and makes a POST request to the /api/domains/compare endpoint with the list of domains.

The DomainAnalysisService.compareDomains method processes the domain list by:
1. Normalizing domain names
2. Retrieving metrics for each domain from Manticore
3. Calculating scores for each metric category
4. Determining winners in each category
5. Generating comparative insights

The comparison results include:
- **Domain metrics**: Global rank, overall score, performance, security, SEO, technical, and traffic metrics for each domain
- **Category winners**: Identification of the top-performing domain in each metric category
- **Key insights**: Specific strengths and differentiators for each domain
- **Recommendations**: Actionable suggestions for improvement

The UI presents results in three sections:
- **Overview Comparison**: Tabular view showing all metrics side-by-side
- **Detailed Metrics Comparison**: Visual comparison of performance, security, SEO, and technical metrics with winner highlighting
- **Recommendations**: Actionable suggestions for improving domain performance

The component also updates the browser URL with the compared domains for easy sharing and bookmarking. Error handling is implemented with user-friendly alerts for comparison failures.

**Section sources**
- [DomainComparisonPage.tsx](file://services/web-app/src/components/DomainComparisonPage.tsx#L0-L570)
- [DomainAnalysisService.ts](file://services/web-app/src/services/DomainAnalysisService.ts#L0-L621)

## Automated Crawling

The Automated Crawling system is implemented through the DomainAnalyzer service in the domain-seeder module. This component performs comprehensive domain analysis by combining TLD-based categorization, structural analysis, and optional web content retrieval to determine domain characteristics and purpose.

```mermaid
classDiagram
class DomainAnalyzer {
-categories : TLDCategories
+analyzeDomain(domain : string, fetchWeb : boolean, webProxy? : string)
-parseDomain(domain : string)
-analyzeCategories(tld : string, domainName : string)
-analyzeStructure(domainName : string)
-fetchWebData(domain : string, proxy? : string)
-calculateConfidence(primaryCategory : any, matchedCategories : string[], structuralAnalysis : string[], webData : any)
+formatForLLM(analysis : DomainAnalysisResult)
}
class DomainAnalysisResult {
+domain : string
+tld : string
+domainName : string
+tldCategory? : string
+tldDescription? : string
+matchedCategories : string[]
+structuralAnalysis : string[]
+webData? : WebData
+confidence : number
}
class WebData {
+title? : string
+description? : string
+content? : string
+status : 'success' | 'failed' | 'disabled'
}
class TLDCategories {
[category : string] : CategoryData
}
class CategoryData {
+description : string
+tlds : string[]
+domainPatterns : string[]
}
DomainAnalyzer --> DomainAnalysisResult
DomainAnalyzer --> WebData
DomainAnalyzer --> TLDCategories
TLDCategories --> CategoryData
```

**Diagram sources**
- [DomainAnalyzer.ts](file://services/domain-seeder/src/services/DomainAnalyzer.ts#L0-L358)

The DomainAnalyzer class loads category definitions from tld-categories.json, which contains mappings of TLDs and domain patterns to categories with descriptions. The analyzeDomain method orchestrates the analysis process:

1. **Domain Parsing**: Splits the domain into TLD and domain name components
2. **Category Analysis**: Matches the domain against TLD and pattern-based categories
3. **Structural Analysis**: Evaluates domain length, character usage, prefixes/suffixes, and readability
4. **Web Data Retrieval**: Optionally fetches and analyzes web content (title, description, main content)
5. **Confidence Calculation**: Computes a confidence score based on available evidence

The category analysis uses a unified approach that considers both TLD membership and domain name patterns. For example, a domain with a .tech TLD would match the technology category, while a domain containing "shop" or "buy" would match e-commerce patterns regardless of TLD.

Structural analysis examines multiple characteristics:
- Length (short vs. long domains)
- Special characters (hyphens, underscores)
- Numbers
- Common marketing prefixes (my, get, go, the, best, top, pro)
- Business suffixes (app, hub, lab, pro, plus, max, online, digital, tech, group)
- Vowel-to-consonant ratio for pronounceability

Web data retrieval uses node-libcurl with configurable proxy support and extracts title, meta description, and cleaned main content. The confidence score combines evidence from category matches, structural analysis, and web data to provide a reliability metric.

The formatForLLM method prepares analysis results for consumption by large language models, structuring the information with clear sections and formatting. This enables integration with AI systems for domain description generation and categorization refinement.

**Section sources**
- [DomainAnalyzer.ts](file://services/domain-seeder/src/services/DomainAnalyzer.ts#L0-L358)

## Troubleshooting Guide

This section addresses common issues encountered when using the core features and provides solutions based on the implementation details.

**Domain Search Issues**
- **No results for valid domains**: Verify that the domain exists in the Manticore index. The search service relies on pre-indexed data, so recently crawled domains may not appear immediately.
- **Slow search performance**: Check Manticore server health and query optimization. The search service includes performance monitoring through the took field in responses.
- **Missing filter options**: Ensure the facets are being properly returned from the backend. The SearchPage component dynamically generates filters based on facet data.

**Domain Analysis Issues**
- **Analysis not available**: This typically occurs when the domain has not been crawled or analysis data is not available in ScyllaDB. Check the crawl status and ensure the domain exists in the database.
- **Incomplete metrics**: Some metrics may be missing if the corresponding data was not collected during crawling. The service handles missing data gracefully by returning default values.
- **Score calculation discrepancies**: Verify the scoring algorithms in DomainAnalysisService and ensure all required metrics are available.

**Top Domains Issues**
- **Stale data**: The TopDomainsService uses Redis caching with TTLs. If data appears stale, check the cache configuration and consider manual cache invalidation.
- **Missing categories**: Category data is cached and retrieved from Manticore facets. If categories are missing, verify the tld-categories.json file and Manticore indexing.
- **Pagination issues**: Ensure the limit parameter is within the valid range (1-100) and that the offset is correctly calculated.

**Domain Comparison Issues**
- **Comparison fails with error**: Validate that at least two domains are entered and that they exist in the system. The service returns descriptive error messages for debugging.
- **Incomplete comparison results**: Check that all domains have been properly analyzed and that metrics are available in the database.
- **Performance issues with multiple domains**: The comparison service queries each domain individually. For large numbers of domains, consider implementing batch operations.

**Automated Crawling Issues**
- **Web data fetching fails**: Verify network connectivity and proxy configuration. The DomainAnalyzer handles fetch errors gracefully by marking web data as failed.
- **Incorrect categorization**: Review the tld-categories.json file and ensure domain patterns are correctly defined. The confidence score indicates analysis reliability.
- **Performance bottlenecks**: The crawling process can be resource-intensive. Consider implementing rate limiting and parallel processing for large-scale operations.

General debugging tips:
- Check server logs for error messages and stack traces
- Verify database connectivity and query performance
- Monitor Redis cache hit rates for performance optimization
- Use the provided health check endpoints to validate service status
- Review configuration files for correct settings

**Section sources**
- [SearchPage.tsx](file://services/web-app/src/components/SearchPage.tsx#L0-L799)
- [DomainAnalysisPage.tsx](file://services/web-app/src/components/DomainAnalysisPage.tsx#L0-L599)
- [TopDomainsPage.tsx](file://services/web-app/src/components/TopDomainsPage.tsx#L0-L799)
- [DomainComparisonPage.tsx](file://services/web-app/src/components/DomainComparisonPage.tsx#L0-L570)
- [DomainSearchService.ts](file://services/web-app/src/services/DomainSearchService.ts#L0-L302)
- [DomainAnalysisService.ts](file://services/web-app/src/services/DomainAnalysisService.ts#L0-L621)
- [TopDomainsService.ts](file://services/web-app/src/services/TopDomainsService.ts#L0-L666)
- [DomainAnalyzer.ts](file://services/domain-seeder/src/services/DomainAnalyzer.ts#L0-L358)

## Conclusion

The domainr platform's core features provide comprehensive domain intelligence through a well-structured architecture that separates concerns between presentation components and backend services. The Domain Search, Domain Analysis, Top Domains, and Domain Comparison features offer powerful capabilities for exploring and analyzing domain data, while the Automated Crawling system ensures data freshness and accuracy.

Key architectural strengths include:
- Clear separation of concerns between UI components and service logic
- Comprehensive error handling and logging
- Performance optimization through caching (Redis) and efficient database queries
- Flexible configuration options and parameter handling
- Robust data modeling with consistent interfaces

The implementation demonstrates best practices in modern web development, including type safety, modular design, and maintainable code structure. The services are designed for scalability, with caching strategies and database optimizations that support growing data volumes.

For developers integrating with these features, the key considerations are understanding the service interfaces, handling asynchronous operations properly, and respecting rate limits and caching behavior. The documented configuration options and return value structures provide a solid foundation for building custom integrations and extensions.