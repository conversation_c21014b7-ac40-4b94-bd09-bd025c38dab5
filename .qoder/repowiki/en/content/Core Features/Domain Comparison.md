# Domain Comparison

<cite>
**Referenced Files in This Document**   
- [DomainComparisonPage.tsx](file://services/web-app/src/components/DomainComparisonPage.tsx)
- [DomainAnalysisService.ts](file://services/web-app/src/services/DomainAnalysisService.ts)
- [ManticoreClient.ts](file://shared/src/database/ManticoreClient.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [User Interface Components](#user-interface-components)
3. [Domain Selection and Interaction](#domain-selection-and-interaction)
4. [Data Synchronization and Visualization](#data-synchronization-and-visualization)
5. [Comparison Scenarios and Results Interpretation](#comparison-scenarios-and-results-interpretation)
6. [Technical Implementation](#technical-implementation)
7. [Performance Considerations](#performance-considerations)
8. [Decision Making Guidance](#decision-making-guidance)

## Introduction
The Domain Comparison feature enables users to perform side-by-side analysis of multiple domains using comprehensive data from the DomainAnalysisService. This functionality allows for competitive analysis by comparing key metrics such as performance, security, SEO, and technical implementation across domains. The feature supports comparison of up to five domains simultaneously, providing visual representations of comparative metrics and actionable recommendations. The system synchronizes analysis data from multiple sources including ScyllaDB, MariaDB, and Manticore Search to provide a complete picture of each domain's characteristics and performance.

## User Interface Components
The DomainComparisonPage component provides a user-friendly interface for domain comparison with several key elements. The interface includes input fields for domain entry, allowing users to add and remove domains from the comparison set. Each domain input field supports text entry with real-time validation. The interface displays comparative metrics in tabular and visual formats, including score cells with color-coded performance indicators. The results section presents data in organized categories with winner highlighting and detailed insights. The UI also includes loading states and error handling to provide feedback during the comparison process. Recommendation sections offer actionable insights based on the comparative analysis.

**Section sources**
- [DomainComparisonPage.tsx](file://services/web-app/src/components/DomainComparisonPage.tsx#L1-L571)

## Domain Selection and Interaction
Users can select domains for comparison through an interactive interface that supports adding up to five domains. The selection process begins with empty input fields that users populate with domain names. Additional domains can be added using the "Add Domain" button until the maximum of five is reached. Users can remove domains they no longer wish to compare using the "Remove" button adjacent to each input field. The interface validates that at least two domains are entered before allowing comparison. When the "Compare Domains" button is clicked, the system initiates the comparison process, displaying a loading indicator while data is fetched. The URL is updated with the selected domains to enable bookmarking and sharing of comparison results.

**Section sources**
- [DomainComparisonPage.tsx](file://services/web-app/src/components/DomainComparisonPage.tsx#L53-L283)

## Data Synchronization and Visualization
The comparison feature synchronizes data from multiple domains and presents it in visual formats that facilitate easy interpretation. The OverviewComparison component displays a table with key metrics including global rank, overall score, performance, security, SEO, technical scores, and traffic estimates. Each metric is color-coded based on performance level, with green indicating strong performance and red indicating areas for improvement. The MetricsComparison component shows detailed category comparisons with winners highlighted and scores displayed as percentages. The RecommendationsSection presents actionable insights derived from the comparative analysis. Score cells use a color gradient from red to green based on performance thresholds, providing immediate visual feedback on domain quality.

```mermaid
flowchart TD
A[User Input] --> B[Domain Normalization]
B --> C[Fetch Analysis Data]
C --> D[Compare Domains]
D --> E[Calculate Winners]
E --> F[Generate Recommendations]
F --> G[Display Results]
G --> H[Visual Representation]
```

**Diagram sources**
- [DomainComparisonPage.tsx](file://services/web-app/src/components/DomainComparisonPage.tsx#L285-L571)
- [DomainAnalysisService.ts](file://services/web-app/src/services/DomainAnalysisService.ts#L100-L618)

## Comparison Scenarios and Results Interpretation
The system supports various comparison scenarios for competitive analysis. When comparing domains, the system identifies winners in each category (performance, security, SEO, technical) based on calculated scores. The overall winner is determined by the highest overall score, which combines multiple metrics with weighted importance. Results interpretation involves analyzing both quantitative scores and qualitative insights. High scores (80%+) indicate strengths in specific areas, while scores below 50% highlight critical weaknesses. The recommendation engine generates specific improvement suggestions based on identified weaknesses. For example, a domain with low performance scores would receive recommendations for optimizing page load speed and Core Web Vitals. The traffic estimate comparison helps identify domains with stronger market presence.

**Section sources**
- [DomainAnalysisService.ts](file://services/web-app/src/services/DomainAnalysisService.ts#L200-L618)

## Technical Implementation
The domain comparison functionality is implemented through a coordinated system of frontend and backend components. The DomainComparisonPage component manages user interaction and state, handling domain selection and comparison requests. When a comparison is initiated, the frontend sends a POST request to the /api/domains/compare endpoint with the selected domains. The DomainAnalysisService processes this request by normalizing domain names and querying the ManticoreClient for comparative data. The ManticoreClient executes a search query against the domains_index, retrieving records for the specified domains and sorting them by overall score. The service calculates category winners by comparing scores across domains and generates recommendations based on identified performance gaps. The system uses ScyllaDB for detailed domain analysis data and MariaDB for WHOIS information, with Manticore Search providing the comparison capability.

```mermaid
sequenceDiagram
participant User as "User"
participant UI as "DomainComparisonPage"
participant API as "DomainAnalysisService"
participant Search as "ManticoreClient"
participant DB as "ScyllaDB/MariaDB"
User->>UI : Enter domains and click Compare
UI->>API : POST /api/domains/compare with domains
API->>Search : compareDomains(domains)
Search->>DB : Query domain data
DB-->>Search : Return domain records
Search-->>API : Return comparison results
API-->>UI : Return processed comparison data
UI-->>User : Display comparison results
```

**Diagram sources**
- [DomainComparisonPage.tsx](file://services/web-app/src/components/DomainComparisonPage.tsx#L150-L283)
- [DomainAnalysisService.ts](file://services/web-app/src/services/DomainAnalysisService.ts#L100-L200)
- [ManticoreClient.ts](file://shared/src/database/ManticoreClient.ts#L168-L192)

## Performance Considerations
The system is designed to handle domain comparisons efficiently, even with large datasets. The comparison process leverages Manticore Search's optimized querying capabilities to retrieve domain data quickly. The system limits comparisons to five domains to maintain performance and usability. Data is cached at multiple levels, including Redis caching for domain existence and Manticore's built-in search result caching. The frontend implements loading states to provide feedback during data retrieval. Error handling is implemented to manage failed requests gracefully. The system normalizes domain names to ensure consistent matching. For domains with extensive analysis data, the system prioritizes key metrics to avoid overwhelming the user interface. The architecture supports horizontal scaling of the search and database components to handle increased load.

**Section sources**
- [DomainAnalysisService.ts](file://services/web-app/src/services/DomainAnalysisService.ts#L100-L618)
- [ManticoreClient.ts](file://shared/src/database/ManticoreClient.ts#L168-L192)

## Decision Making Guidance
The comparison results provide valuable insights for competitive analysis and strategic decision making. Users should focus on both winners and weaknesses when interpreting results. Domains that win in multiple categories represent strong competitors worthy of further study. Identified weaknesses highlight opportunities for improvement in one's own domains. The recommendation engine provides specific, actionable steps for addressing performance gaps. Traffic estimates offer insights into market presence and audience size. The overall score provides a comprehensive measure of domain quality, while category scores reveal specific strengths and weaknesses. Users should consider the context of their industry and target audience when interpreting results, as different metrics may have varying importance depending on business goals.

**Section sources**
- [DomainAnalysisService.ts](file://services/web-app/src/services/DomainAnalysisService.ts#L500-L618)