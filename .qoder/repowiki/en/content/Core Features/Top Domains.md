# Top Domains

<cite>
**Referenced Files in This Document**   
- [TopDomainsPage.tsx](file://services/web-app/src/components/TopDomainsPage.tsx)
- [TopDomainsService.ts](file://services/web-app/src/services/TopDomainsService.ts)
- [CompositeRanker.ts](file://services/worker/src/ranking/CompositeRanker.ts)
- [RankingUpdateService.ts](file://services/worker/src/ranking/RankingUpdateService.ts)
- [DomainRanking.ts](file://shared/src/models/DomainRanking.ts)
- [PerformanceScorer.ts](file://services/worker/src/ranking/scorers/PerformanceScorer.ts)
- [SecurityScorer.ts](file://services/worker/src/ranking/scorers/SecurityScorer.ts)
- [SEOScorer.ts](file://services/worker/src/ranking/scorers/SEOScorer.ts)
- [TechnicalScorer.ts](file://services/worker/src/ranking/scorers/TechnicalScorer.ts)
- [BacklinkScorer.ts](file://services/worker/src/ranking/scorers/BacklinkScorer.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Top Domains Page Overview](#top-domains-page-overview)
3. [Ranking Calculation Process](#ranking-calculation-process)
4. [Domain Ranking Data Model](#domain-ranking-data-model)
5. [Ranking Update Service](#ranking-update-service)
6. [Weighting System](#weighting-system)
7. [Ranking Queries and Responses](#ranking-queries-and-responses)
8. [Data Freshness and Update Issues](#data-freshness-and-update-issues)
9. [Customization and Filtering](#customization-and-filtering)
10. [Conclusion](#conclusion)

## Introduction

The Top Domains feature provides a comprehensive ranking system that evaluates websites based on multiple performance, security, SEO, technical, and backlink metrics. This documentation details how the Top Domains page displays global, category-based, and country-specific rankings, the underlying ranking calculation process, and the architecture that supports periodic ranking updates. The system leverages a sophisticated weighting algorithm to produce composite scores that reflect a domain's overall quality and performance.

**Section sources**
- [TopDomainsPage.tsx](file://services/web-app/src/components/TopDomainsPage.tsx)
- [TopDomainsService.ts](file://services/web-app/src/services/TopDomainsService.ts)

## Top Domains Page Overview

The Top Domains page serves as the primary interface for users to explore domain rankings. It displays rankings in both table and card view modes, allowing users to easily compare domains across various metrics. The page supports multiple filtering options including category, country, and timeframe, enabling users to customize their view of the rankings.

The page is implemented as a React component (`TopDomainsPage`) that fetches ranking data through API calls to the backend services. It provides interactive controls for filtering and pagination, with real-time updates to the displayed rankings based on user selections. The component renders detailed information for each domain including its global rank, category rank, overall score, and individual metric scores.

The page also displays trend information, showing how a domain's ranking has changed over time. This includes visual indicators for rank changes and historical trend data. The interface is designed to be responsive and user-friendly, with loading states and error handling to ensure a smooth user experience.

```mermaid
flowchart TD
A[User Accesses Top Domains Page] --> B[Page Loads Initial Data]
B --> C[Fetch Available Categories]
C --> D[Display Filters and Controls]
D --> E[User Applies Filters]
E --> F[Fetch Updated Rankings]
F --> G[Render Rankings in Selected View Mode]
G --> H[Display Trend Information]
H --> I[Handle Pagination]
```

**Diagram sources**
- [TopDomainsPage.tsx](file://services/web-app/src/components/TopDomainsPage.tsx)

**Section sources**
- [TopDomainsPage.tsx](file://services/web-app/src/components/TopDomainsPage.tsx)
- [TopDomainsService.ts](file://services/web-app/src/services/TopDomainsService.ts)

## Ranking Calculation Process

The ranking calculation process is centered around the `CompositeRanker` class, which combines scores from multiple specialized scorers to produce a comprehensive ranking for each domain. The process begins with the collection of domain data from various sources, including performance metrics, security assessments, SEO analysis, technical evaluations, and backlink data.

The `CompositeRanker` calculates a composite score by applying weighted values to each dimension of the domain analysis. Each scoring dimension is evaluated independently by specialized scorer classes (PerformanceScorer, SecurityScorer, SEOScorer, TechnicalScorer, and BacklinkScorer), which apply domain-specific algorithms to convert raw metrics into normalized scores between 0 and 1.

The composite score calculation follows a multi-step process:
1. Fetch domain data from the database
2. Calculate individual scores for each dimension
3. Apply weighting factors to each dimension's score
4. Sum the weighted scores to produce the overall composite score
5. Assign ranks based on the composite scores

The ranking process supports both global rankings and category-specific rankings. For category-based rankings, the system filters domains by category before calculating scores and assigning ranks. This allows for meaningful comparisons within specific industry segments.

```mermaid
classDiagram
class CompositeRanker {
+calculateCompositeScore(domainData)
+calculateGlobalRankings(domains)
+calculateCategoryRankings(domains, category)
+getWeights()
+updateWeights(newWeights)
}
class PerformanceScorer {
+calculateScore(metrics)
+getPerformanceGrade(score)
}
class SecurityScorer {
+calculateScore(metrics)
+getSecurityGrade(score)
}
class SEOScorer {
+calculateScore(metrics)
+getSEOGrade(score)
}
class TechnicalScorer {
+calculateScore(metrics)
+getTechnicalGrade(score)
}
class BacklinkScorer {
+calculateScore(metrics)
+getBacklinkGrade(score)
}
CompositeRanker --> PerformanceScorer : "uses"
CompositeRanker --> SecurityScorer : "uses"
CompositeRanker --> SEOScorer : "uses"
CompositeRanker --> TechnicalScorer : "uses"
CompositeRanker --> BacklinkScorer : "uses"
```

**Diagram sources**
- [CompositeRanker.ts](file://services/worker/src/ranking/CompositeRanker.ts)
- [PerformanceScorer.ts](file://services/worker/src/ranking/scorers/PerformanceScorer.ts)
- [SecurityScorer.ts](file://services/worker/src/ranking/scorers/SecurityScorer.ts)
- [SEOScorer.ts](file://services/worker/src/ranking/scorers/SEOScorer.ts)
- [TechnicalScorer.ts](file://services/worker/src/ranking/scorers/TechnicalScorer.ts)
- [BacklinkScorer.ts](file://services/worker/src/ranking/scorers/BacklinkScorer.ts)

**Section sources**
- [CompositeRanker.ts](file://services/worker/src/ranking/CompositeRanker.ts)
- [PerformanceScorer.ts](file://services/worker/src/ranking/scorers/PerformanceScorer.ts)
- [SecurityScorer.ts](file://services/worker/src/ranking/scorers/SecurityScorer.ts)

## Domain Ranking Data Model

The Domain Ranking data model is implemented as a class that represents a domain's ranking information. The model includes properties for the domain name, ranking type (global or category-specific), rank position, overall score, and individual scores for each evaluation dimension (performance, security, SEO, technical, and backlinks).

The data model also includes metadata such as traffic estimates, last updated timestamp, and category information. This comprehensive structure allows for rich ranking displays that show not only the rank position but also the underlying factors that contribute to a domain's score.

The model is designed to be compatible with the ScyllaDB database, with a `toScyllaFormat()` method that converts the object properties to the format expected by the database schema. This ensures consistent data storage and retrieval across the system.

The ranking data is stored in the `domain_rankings` table in ScyllaDB, with composite primary keys that support efficient querying by ranking type and rank position. This enables fast retrieval of top-ranked domains for display on the Top Domains page.

```mermaid
erDiagram
DOMAIN_RANKING {
string domain PK
string ranking_type PK
int rank
float overall_score
float performance_score
float security_score
float seo_score
float technical_score
float backlink_score
float traffic_estimate
timestamp last_updated
string category
}
```

**Diagram sources**
- [DomainRanking.ts](file://shared/src/models/DomainRanking.ts)

**Section sources**
- [DomainRanking.ts](file://shared/src/models/DomainRanking.ts)
- [RankingUpdateService.ts](file://services/worker/src/ranking/RankingUpdateService.ts)

## Ranking Update Service

The Ranking Update Service is responsible for managing the periodic updating of domain rankings. Implemented as the `RankingUpdateService` class, this service handles both single-domain updates and batch updates for multiple domains. It uses a job queue system to process ranking updates asynchronously, ensuring that the main application remains responsive.

The service is initialized with database and job queue connections, and it sets up consumers for different types of ranking update jobs. It supports several update triggers including domain updates, scheduled updates, manual updates, and batch updates. The service also manages ranking history, tracking changes in domain rankings over time.

Key features of the Ranking Update Service include:
- Job queue integration for asynchronous processing
- Batch processing for efficient updates of multiple domains
- Ranking history tracking for trend analysis
- Related update triggering (e.g., search index updates)
- Error handling and retry mechanisms

The service is configured with scheduled updates that recalculate global rankings daily and category rankings weekly. This ensures that rankings remain current while balancing computational resources.

```mermaid
sequenceDiagram
participant User
participant WebApp
participant Worker
participant Database
User->>WebApp : Request ranking update
WebApp->>Worker : Publish ranking update job
Worker->>Database : Fetch domain data
Database-->>Worker : Return domain data
Worker->>Worker : Calculate composite score
Worker->>Database : Store updated ranking
Database-->>Worker : Confirmation
Worker->>Database : Update ranking history
Database-->>Worker : Confirmation
Worker->>WebApp : Complete job
WebApp->>User : Display updated rankings
```

**Diagram sources**
- [RankingUpdateService.ts](file://services/worker/src/ranking/RankingUpdateService.ts)

**Section sources**
- [RankingUpdateService.ts](file://services/worker/src/ranking/RankingUpdateService.ts)
- [CompositeRanker.ts](file://services/worker/src/ranking/CompositeRanker.ts)

## Weighting System

The ranking system employs a sophisticated weighting system that assigns different importance levels to each evaluation dimension. The current weighting configuration is:

- Performance: 25%
- Security: 20%
- SEO: 20%
- Technical: 15%
- Backlinks: 20%

This weighting system reflects the relative importance of each dimension in determining a domain's overall quality and performance. The weights are applied during the composite score calculation, with each dimension's score multiplied by its corresponding weight before being summed to produce the final composite score.

The weighting system is configurable and can be updated without requiring code changes. The `CompositeRanker` class provides methods to get and update the current weights, allowing for adjustments based on changing priorities or user feedback. When weights are updated, the system validates that they sum to 1.0 to ensure mathematical correctness.

The impact of the weighting system on rankings is significant. Domains that excel in high-weight dimensions (such as Performance) receive a greater boost to their overall score than those that excel in lower-weight dimensions. This encourages website owners to focus on the most impactful areas for improvement.

The weighting system also supports category-specific configurations, allowing different weightings to be applied for different industry segments. This enables more relevant rankings within specific categories where certain dimensions may be more important than others.

**Section sources**
- [CompositeRanker.ts](file://services/worker/src/ranking/CompositeRanker.ts)

## Ranking Queries and Responses

The system supports various ranking queries through both the web interface and API endpoints. Users can retrieve rankings filtered by category, country, and timeframe, with options for pagination and sorting. The queries are processed by the `TopDomainsService` class, which handles both cache management and database queries.

Example ranking query:
```
GET /api/domains/top?category=technology&country=US&limit=50&page=1
```

Example ranking response:
```json
{
  "domains": [
    {
      "domain": "example.com",
      "globalRank": 1,
      "categoryRank": 1,
      "category": "technology",
      "country": "US",
      "scores": {
        "overall": 0.95,
        "performance": 0.92,
        "security": 0.98,
        "seo": 0.94,
        "technical": 0.91
      },
      "trafficEstimate": 1500000,
      "trend": "up",
      "lastUpdated": "2025-09-13T10:30:00Z"
    }
  ],
  "category": "technology",
  "country": "US",
  "timeframe": "current",
  "total": 1500,
  "timestamp": "2025-09-13T10:30:00Z"
}
```

The service implements caching to improve performance, storing frequently accessed ranking data in Redis with appropriate TTL values. This reduces database load and improves response times for common queries. Cache invalidation is handled automatically when rankings are updated.

The API also supports queries for available categories, global rankings, category-specific rankings, and trending domains. Each endpoint returns structured JSON responses that include pagination information and metadata.

**Section sources**
- [TopDomainsService.ts](file://services/web-app/src/services/TopDomainsService.ts)
- [web.ts](file://services/web-app/src/app/routes/web.ts)

## Data Freshness and Update Issues

Data freshness is a critical aspect of the ranking system, with rankings updated periodically to reflect changes in domain performance and characteristics. The system employs several mechanisms to ensure data freshness while addressing potential update issues.

The primary update mechanism is the scheduled ranking recalculation, which runs daily for global rankings and weekly for category rankings. Additionally, the system supports on-demand updates triggered by domain changes or manual requests. These updates are processed through a job queue system that handles failures and retries.

Common issues with ranking updates include:
- Data availability: Some domains may lack complete data for all evaluation dimensions
- Processing delays: Large batch updates may take time to complete
- Cache consistency: Cached rankings may temporarily differ from database values
- Resource constraints: High update volumes may impact system performance

To address these issues, the system implements several strategies:
- Default scores for missing data to prevent ranking calculation failures
- Priority-based job processing to ensure critical updates are processed first
- Cache invalidation on updates to maintain consistency
- Monitoring and alerting for update failures or delays

The system also tracks ranking history, allowing for trend analysis and detection of anomalous changes. This helps identify potential issues with the ranking algorithm or data quality.

**Section sources**
- [RankingUpdateService.ts](file://services/worker/src/ranking/RankingUpdateService.ts)
- [TopDomainsService.ts](file://services/web-app/src/services/TopDomainsService.ts)

## Customization and Filtering

The Top Domains page provides extensive customization and filtering options to allow users to tailor their view of the rankings. Users can filter rankings by category, country, and timeframe, with the ability to combine multiple filters for more specific views.

The filtering interface includes:
- Category dropdown with category counts and descriptions
- Country selector with major countries and "All Countries" option
- Timeframe selection for trend analysis
- View mode toggle between table and card layouts
- Pagination controls for navigating through large result sets

The customization options are implemented with client-side state management, allowing for smooth transitions between different views without full page reloads. Filter changes trigger API requests to fetch updated ranking data, with loading states displayed during data retrieval.

Users can also customize the display by choosing between table and card view modes. The table view provides a compact, sortable display of key metrics, while the card view offers a more detailed, visually rich presentation of domain information.

The system supports deep linking, with URL parameters that reflect the current filter state. This allows users to share specific views of the rankings with others. The URL is updated dynamically as filters are changed, enabling browser navigation and bookmarking.

**Section sources**
- [TopDomainsPage.tsx](file://services/web-app/src/components/TopDomainsPage.tsx)
- [TopDomainsService.ts](file://services/web-app/src/services/TopDomainsService.ts)

## Conclusion

The Top Domains feature provides a comprehensive and flexible ranking system that evaluates websites across multiple dimensions of performance, security, SEO, technical quality, and backlink profile. The system combines sophisticated scoring algorithms with efficient data processing and caching to deliver timely and accurate rankings.

Key strengths of the system include its modular architecture, configurable weighting system, and robust update mechanisms. The separation of concerns between the web interface, service layer, and worker processes ensures scalability and maintainability. The use of specialized scorer classes allows for independent improvement of each evaluation dimension.

The system effectively balances data freshness with performance through scheduled updates, caching, and asynchronous processing. The comprehensive filtering and customization options provide users with meaningful insights into domain performance across different contexts.

Future enhancements could include more granular category-specific weightings, real-time ranking updates, and advanced trend analysis features. The existing architecture provides a solid foundation for these and other improvements.