# Domain Analysis

<cite>
**Referenced Files in This Document**  
- [DomainAnalysisPage.tsx](file://services/web-app/src/components/DomainAnalysisPage.tsx)
- [DomainAnalysis.ts](file://shared/src/models/DomainAnalysis.ts)
- [WorkerService.ts](file://services/worker/src/core/WorkerService.ts)
- [DomainProcessingPipeline.ts](file://services/worker/src/pipeline/DomainProcessingPipeline.ts)
- [DomainAnalyzer.ts](file://services/domain-seeder/src/services/DomainAnalyzer.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Domain Analysis Request and Display](#domain-analysis-request-and-display)
3. [Data Collection Pipeline](#data-collection-pipeline)
4. [DomainAnalysis Data Model](#domainanalysis-data-model)
5. [Analysis Request and Response Examples](#analysis-request-and-response-examples)
6. [Caching Strategy and Re-analysis Triggers](#caching-strategy-and-re-analysis-triggers)
7. [Common Analysis Failures and Troubleshooting](#common-analysis-failures-and-troubleshooting)
8. [Interpreting Metric Scores and Business Implications](#interpreting-metric-scores-and-business-implications)

## Introduction
The Domain Analysis feature provides comprehensive evaluation of websites across technical, performance, security, and SEO dimensions. This document details the end-to-end process from user request through data collection, processing, storage, and presentation. The system leverages a distributed architecture with specialized worker services that orchestrate domain analysis through multiple phases including crawling, ranking calculation, and indexing.

**Section sources**
- [DomainAnalysisPage.tsx](file://services/web-app/src/components/DomainAnalysisPage.tsx)
- [DomainAnalysis.ts](file://shared/src/models/DomainAnalysis.ts)

## Domain Analysis Request and Display
The DomainAnalysisPage component serves as the user interface for requesting and displaying comprehensive website metrics. When a user requests analysis of a domain, the page initiates a request to the backend system which coordinates the analysis process. The page displays both loading states and error conditions, providing feedback during the analysis process.

Once results are available, the page presents a comprehensive view including global rank, overall score, traffic estimates, and domain age. Detailed metrics are organized into performance, security, SEO, and technical categories, each with individual scores and specific metric values. The interface also displays detected technologies and provides a ranking explanation that breaks down contributing factors, recommendations, and competitor comparisons.

```mermaid
sequenceDiagram
participant User as "User"
participant Page as "DomainAnalysisPage"
participant API as "Web App Service"
participant Worker as "Worker Service"
User->>Page : Request domain analysis
Page->>API : Fetch analysis data
API->>Worker : Submit analysis job
Worker->>Worker : Process through pipeline
Worker-->>API : Return analysis results
API-->>Page : Send results
Page-->>User : Display comprehensive metrics
```

**Diagram sources**
- [DomainAnalysisPage.tsx](file://services/web-app/src/components/DomainAnalysisPage.tsx)

**Section sources**
- [DomainAnalysisPage.tsx](file://services/web-app/src/components/DomainAnalysisPage.tsx)

## Data Collection Pipeline
The data collection pipeline is orchestrated by the WorkerService and DomainProcessingPipeline components. When an analysis request is received, the WorkerService coordinates job processing through multiple phases: crawling, ranking, and indexing. The pipeline ensures that each domain is processed systematically with proper error handling and resource management.

The DataCollectionOrchestrator manages the crawling phase, collecting data from various sources and modules. This is followed by the ranking phase where the RankingManager calculates composite scores based on the collected data. Finally, the SearchIndexingService handles the indexing phase, updating search indexes and synchronizing data across databases.

```mermaid
flowchart TD
A[Analysis Request] --> B{Domain Lock}
B --> C[Crawling Phase]
C --> D[Data Collection Orchestrator]
D --> E[Performance Analysis]
D --> F[Security Analysis]
D --> G[SEO Analysis]
D --> H[Technical Analysis]
C --> I{Crawling Complete?}
I --> |Yes| J[Ranking Phase]
I --> |No| K[Handle Partial Results]
J --> L[Ranking Manager]
L --> M[Calculate Composite Score]
M --> N[Indexing Phase]
N --> O[Update Search Index]
N --> P[Synchronize Databases]
N --> Q[Invalidate Cache]
O --> R[Analysis Complete]
P --> R
Q --> R
```

**Diagram sources**
- [WorkerService.ts](file://services/worker/src/core/WorkerService.ts)
- [DomainProcessingPipeline.ts](file://services/worker/src/pipeline/DomainProcessingPipeline.ts)

**Section sources**
- [WorkerService.ts](file://services/worker/src/core/WorkerService.ts)
- [DomainProcessingPipeline.ts](file://services/worker/src/pipeline/DomainProcessingPipeline.ts)

## DomainAnalysis Data Model
The DomainAnalysis class defines the comprehensive data model for storing website evaluation results. This model includes scores for performance, security, SEO, and technical aspects, along with detailed metrics for each category. The data structure also captures domain information such as age, registration details, and DNS records.

The model incorporates content quality metrics including readability, media richness, and structural quality. Language detection results and content metrics like word count and duplicate content percentage are also stored. The technical SEO component tracks structured data, canonical tags, meta descriptions, and image optimization.

The data model supports conversion to database format for storage in ScyllaDB, with specific mappings for different data types including maps for metrics, sets for technologies and subdomains, and appropriate type conversions for numerical values.

```mermaid
classDiagram
class DomainAnalysis {
+string domain
+number globalRank
+number categoryRank
+string category
+number overallScore
+PerformanceMetrics performance
+SecurityMetrics security
+SEOMetrics seo
+TechnicalMetrics technical
+DomainInfo domainInfo
+ContentQualityMetrics contentQuality
+LanguageDetection languageDetection
+ContentMetrics contentMetrics
+TechnicalSEO technicalSEO
+string[] screenshots
+string[] subdomains
+string lastCrawled
+string crawlStatus
+string lastUpdated
+calculateOverallScore(weights) number
+toScyllaFormat() Map
}
class PerformanceMetrics {
+number loadTime
+number firstContentfulPaint
+number largestContentfulPaint
+number cumulativeLayoutShift
+number firstInputDelay
+number speedIndex
+number score
}
class SecurityMetrics {
+string sslGrade
+Map securityHeaders
+string[] vulnerabilities
+Map certificateInfo
+number score
}
class SEOMetrics {
+Map metaTags
+string[] structuredData
+Map sitemap
+Map robotsTxt
+number score
}
class TechnicalMetrics {
+string[] technologies
+Map serverInfo
+Map httpHeaders
+number pageSize
+Map resourceCount
+number score
}
DomainAnalysis --> PerformanceMetrics
DomainAnalysis --> SecurityMetrics
DomainAnalysis --> SEOMetrics
DomainAnalysis --> TechnicalMetrics
```

**Diagram sources**
- [DomainAnalysis.ts](file://shared/src/models/DomainAnalysis.ts)

**Section sources**
- [DomainAnalysis.ts](file://shared/src/models/DomainAnalysis.ts)

## Analysis Request and Response Examples
When a domain analysis is requested, the system processes the request through the worker pipeline and returns structured results. The request typically includes the domain name and optional parameters such as analysis priority or specific modules to include.

The response contains comprehensive metrics organized into categories. For example, a successful analysis response includes the domain name, global and category ranks, overall score, and detailed scores for performance, security, SEO, and technical aspects. Specific metrics are provided for each category, such as load time and first contentful paint for performance, SSL grade and security headers for security, and title, description, and structured data for SEO.

Error responses include descriptive messages explaining why the analysis could not be completed, such as connectivity issues, timeout errors, or invalid domain names. Partial results may be returned when some analysis modules succeed while others fail, providing available data with appropriate status indicators.

**Section sources**
- [DomainAnalysisPage.tsx](file://services/web-app/src/components/DomainAnalysisPage.tsx)
- [DomainAnalysis.ts](file://shared/src/models/DomainAnalysis.ts)

## Caching Strategy and Re-analysis Triggers
The system implements a comprehensive caching strategy to optimize performance and reduce redundant processing. Domain locks prevent concurrent analysis of the same domain, ensuring data consistency. The RedisCacheLayer provides fast access to recently analyzed domains, reducing the need for repeated crawling and processing.

Re-analysis is triggered by several factors including expiration of the cache TTL (typically 24-48 hours), manual user requests for fresh analysis, and system-driven updates based on scheduled jobs. Significant changes in domain characteristics such as SSL certificate updates, technology stack changes, or content modifications also trigger re-analysis.

The CacheInvalidationManager monitors for changes that warrant cache invalidation, while the JobScheduler manages periodic re-analysis of high-priority domains. The system balances freshness requirements with resource constraints, prioritizing re-analysis based on domain importance, traffic volume, and change frequency.

**Section sources**
- [WorkerService.ts](file://services/worker/src/core/WorkerService.ts)
- [DomainProcessingPipeline.ts](file://services/worker/src/pipeline/DomainProcessingPipeline.ts)

## Common Analysis Failures and Troubleshooting
Analysis failures can occur at various stages of the pipeline. Common issues include connectivity problems during crawling, timeout errors when analyzing large sites, and authentication failures when accessing protected resources. The system implements comprehensive error handling with retry mechanisms and graceful degradation.

For troubleshooting, the system provides detailed error logs and metrics through the WorkerErrorHandler and ErrorReportingSystem. Common solutions include verifying network connectivity, checking domain accessibility, ensuring proper configuration of external services, and validating API credentials. The HealthChecker component can be used to verify the status of dependent services.

When analysis fails completely, the system returns appropriate error messages to the user interface. For partial failures, the system attempts to provide available data while clearly indicating which components could not be analyzed. The CircuitBreaker pattern prevents cascading failures by temporarily halting requests to failing services.

**Section sources**
- [WorkerService.ts](file://services/worker/src/core/WorkerService.ts)
- [DomainProcessingPipeline.ts](file://services/worker/src/pipeline/DomainProcessingPipeline.ts)

## Interpreting Metric Scores and Business Implications
The metric scores provide actionable insights into website quality and performance. The overall score (0-1 scale) represents a composite assessment, with higher scores indicating better website quality. Performance scores reflect loading speed and user experience, directly impacting bounce rates and conversion.

Security scores indicate the robustness of website protection, with implications for user trust and data protection compliance. High security scores reduce the risk of breaches and enhance customer confidence. SEO scores measure search engine optimization effectiveness, directly influencing organic traffic and visibility.

Technical scores assess infrastructure quality, including technology stack, server configuration, and resource optimization. These scores impact scalability, reliability, and maintenance costs. Business decisions can leverage these metrics to prioritize website improvements, allocate resources, and measure the impact of optimization efforts.

**Section sources**
- [DomainAnalysis.ts](file://shared/src/models/DomainAnalysis.ts)
- [DomainAnalysisPage.tsx](file://services/web-app/src/components/DomainAnalysisPage.tsx)