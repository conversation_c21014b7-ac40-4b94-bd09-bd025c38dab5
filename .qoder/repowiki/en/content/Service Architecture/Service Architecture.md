# Service Architecture

<cite>
**Referenced Files in This Document**   
- [services/admin/package.json](file://services/admin/package.json)
- [services/admin/Dockerfile](file://services/admin/Dockerfile)
- [services/web-app/package.json](file://services/web-app/package.json)
- [services/web-app/Dockerfile](file://services/web-app/Dockerfile)
- [services/worker/package.json](file://services/worker/package.json)
- [services/worker/Dockerfile](file://services/worker/Dockerfile)
- [services/domain-seeder/package.json](file://services/domain-seeder/package.json)
- [services/domain-seeder/Dockerfile](file://services/domain-seeder/Dockerfile)
- [docker-compose.yml](file://docker-compose.yml)
- [nginx/conf.d/default.conf](file://nginx/conf.d/default.conf)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Project Structure](#project-structure)
3. [Core Components](#core-components)
4. [Architecture Overview](#architecture-overview)
5. [Detailed Component Analysis](#detailed-component-analysis)
6. [Dependency Analysis](#dependency-analysis)
7. [Performance Considerations](#performance-considerations)
8. [Troubleshooting Guide](#troubleshooting-guide)
9. [Conclusion](#conclusion)

## Introduction
The domainr service architecture is a distributed system designed for domain discovery, analysis, ranking, and administration. It follows a microservices pattern with four primary services: Web Application (frontend and API gateway), Worker Service (background processing and analysis), Domain Seeder (domain discovery and ingestion), and Admin Interface (system management and monitoring). The architecture leverages event-driven communication, layered design, and configuration-as-code principles to ensure scalability, maintainability, and operational resilience.

## Project Structure

```mermaid
graph TD
A[Root] --> B[_config]
A --> C[data]
A --> D[database]
A --> E[monitoring]
A --> F[nginx]
A --> G[scripts]
A --> H[services]
A --> I[shared]
H --> H1[admin]
H --> H2[domain-seeder]
H --> H3[web-app]
H --> H4[worker]
I --> I1[bin]
I --> I2[data]
I --> I3[src]
```

**Diagram sources**
- [services/admin/package.json](file://services/admin/package.json)
- [services/domain-seeder/package.json](file://services/domain-seeder/package.json)
- [services/web-app/package.json](file://services/web-app/package.json)
- [services/worker/package.json](file://services/worker/package.json)

**Section sources**
- [services/admin/package.json](file://services/admin/package.json)
- [services/domain-seeder/package.json](file://services/domain-seeder/package.json)
- [services/web-app/package.json](file://services/web-app/package.json)
- [services/worker/package.json](file://services/worker/package.json)

## Core Components

The domainr system consists of four main services:

1. **Web Application**: Serves as the frontend interface and API gateway for domain search and retrieval.
2. **Worker Service**: Handles background processing including crawling, indexing, ranking, and AI-based content generation.
3. **Domain Seeder**: Discovers new domains through various external sources and seeds them into the processing pipeline.
4. **Admin Interface**: Provides system monitoring, configuration management, and operational controls.

These services communicate via Redis-based message queues and share data through MariaDB, ScyllaDB, and ManticoreSearch.

**Section sources**
- [services/web-app/package.json](file://services/web-app/package.json)
- [services/worker/package.json](file://services/worker/package.json)
- [services/domain-seeder/package.json](file://services/domain-seeder/package.json)
- [services/admin/package.json](file://services/admin/package.json)

## Architecture Overview

```mermaid
graph TD
subgraph "External"
User[End User]
ExternalSources[External Data Sources]
end
subgraph "Frontend Layer"
WebApp[Web Application<br>API Gateway]
Admin[Admin Interface]
end
subgraph "Processing Layer"
Worker[Worker Service<br>Crawling, Ranking, AI]
Seeder[Domain Seeder<br>Discovery & Ingestion]
end
subgraph "Data Layer"
Redis[(Redis<br>Queue & Cache)]
Manticore[(Manticore<br>Search Index)]
MariaDB[(MariaDB<br>Relational Data)]
ScyllaDB[(ScyllaDB<br>Time Series)]
end
subgraph "Infrastructure"
NGINX[NGINX<br>Reverse Proxy]
Monitoring[Prometheus/Grafana]
end
User --> WebApp
User --> Admin
WebApp --> Worker
WebApp --> Redis
WebApp --> Manticore
Admin --> WebApp
Admin --> Worker
Admin --> Seeder
Admin --> Redis
Admin --> Monitoring
Seeder --> Redis
Worker --> Redis
Worker --> MariaDB
Worker --> ScyllaDB
Worker --> Manticore
Redis --> Worker
ExternalSources --> Seeder
NGINX --> WebApp
NGINX --> Admin
```

**Diagram sources**
- [services/web-app/package.json](file://services/web-app/package.json)
- [services/worker/package.json](file://services/worker/package.json)
- [services/domain-seeder/package.json](file://services/domain-seeder/package.json)
- [services/admin/package.json](file://services/admin/package.json)
- [nginx/conf.d/default.conf](file://nginx/conf.d/default.conf)

## Detailed Component Analysis

### Web Application Analysis

The Web Application serves as the primary entry point for users and external clients. It provides RESTful APIs for domain search, ranking, and analysis through an Ultimate Express-based server.

```mermaid
sequenceDiagram
participant Client as "Client"
participant WebApp as "Web Application"
participant Redis as "Redis Queue"
participant Manticore as "Manticore Search"
Client->>WebApp : GET /search?q=domain
WebApp->>Manticore : Query search index
Manticore-->>WebApp : Return search results
WebApp-->>Client : JSON response
Client->>WebApp : POST /analyze
WebApp->>Redis : Push analysis job
WebApp-->>Client : 202 Accepted
```

**Diagram sources**
- [services/web-app/package.json](file://services/web-app/package.json)
- [services/web-app/Dockerfile](file://services/web-app/Dockerfile)

**Section sources**
- [services/web-app/package.json](file://services/web-app/package.json)
- [services/web-app/Dockerfile](file://services/web-app/Dockerfile)

### Worker Service Analysis

The Worker Service processes domain data through a pipeline of crawling, analysis, ranking, and AI-driven content generation. It consumes jobs from Redis and updates multiple data stores.

```mermaid
flowchart TD
Start([Job Received]) --> Crawl["Crawl Domain<br>via node-libcurl"]
Crawl --> Parse["Parse HTML<br>via cheerio"]
Parse --> Analyze["Analyze Content<br>AI & Heuristics"]
Analyze --> Rank["Calculate Ranking<br>Composite Algorithm"]
Rank --> Index["Update Indexes<br>Manticore & ScyllaDB"]
Index --> Cache["Invalidate Redis Cache"]
Cache --> End([Job Complete])
```

**Diagram sources**
- [services/worker/package.json](file://services/worker/package.json)
- [services/worker/Dockerfile](file://services/worker/Dockerfile)

**Section sources**
- [services/worker/package.json](file://services/worker/package.json)
- [services/worker/Dockerfile](file://services/worker/Dockerfile)

### Domain Seeder Analysis

The Domain Seeder discovers new domains from external sources including Common Crawl, CZDS, and other public datasets, then enqueues them for processing.

```mermaid
classDiagram
class DomainSeeder {
+string[] sources
+int batchSize
+float rateLimit
+run() void
+backfill() void
+status() Status
}
class SourceConnector {
+fetch() DomainStream
+validate() boolean
}
class RateLimitedEnqueuer {
+enqueue(domains) int
+getQueueStats() Stats
}
class DomainNormalizer {
+normalize(domain) string
+validate(domain) boolean
}
DomainSeeder --> SourceConnector : "uses"
DomainSeeder --> RateLimitedEnqueuer : "delegates"
DomainSeeder --> DomainNormalizer : "uses"
```

**Diagram sources**
- [services/domain-seeder/package.json](file://services/domain-seeder/package.json)
- [services/domain-seeder/Dockerfile](file://services/domain-seeder/Dockerfile)

**Section sources**
- [services/domain-seeder/package.json](file://services/domain-seeder/package.json)
- [services/domain-seeder/Dockerfile](file://services/domain-seeder/Dockerfile)

### Admin Interface Analysis

The Admin Interface provides monitoring, configuration, and management capabilities through a Next.js application with Mantine UI components.

```mermaid
graph TD
Admin[Admin Interface] --> Monitoring[Monitoring Dashboard]
Admin --> Configuration[Service Configuration]
Admin --> Users[User Management]
Admin --> Logs[Centralized Logging]
Admin --> Alerts[Alert Management]
Monitoring --> Prometheus[Prometheus Metrics]
Monitoring --> Grafana[Grafana Dashboard]
Logs --> Pino[Pino Logging]
Configuration --> Redis[Redis Configuration]
Configuration --> MariaDB[MariaDB Settings]
```

**Diagram sources**
- [services/admin/package.json](file://services/admin/package.json)
- [services/admin/Dockerfile](file://services/admin/Dockerfile)

**Section sources**
- [services/admin/package.json](file://services/admin/package.json)
- [services/admin/Dockerfile](file://services/admin/Dockerfile)

## Dependency Analysis

```mermaid
graph LR
WebApp --> Shared
Worker --> Shared
Seeder --> Shared
Admin --> Shared
WebApp --> Redis
WebApp --> Manticore
WebApp --> MariaDB
Worker --> Redis
Worker --> ScyllaDB
Worker --> MariaDB
Worker --> Manticore
Seeder --> Redis
Seeder --> MariaDB
Admin --> WebApp
Admin --> Worker
Admin --> Seeder
Admin --> Redis
Admin --> MariaDB
Shared[Shared Library] --> RedisClient
Shared --> MariaClient
Shared --> ManticoreClient
Shared --> ScyllaClient
Shared --> JobQueue
```

**Diagram sources**
- [services/web-app/package.json](file://services/web-app/package.json)
- [services/worker/package.json](file://services/worker/package.json)
- [services/domain-seeder/package.json](file://services/domain-seeder/package.json)
- [services/admin/package.json](file://services/admin/package.json)
- [shared/package.json](file://shared/package.json)

**Section sources**
- [services/web-app/package.json](file://services/web-app/package.json)
- [services/worker/package.json](file://services/worker/package.json)
- [services/domain-seeder/package.json](file://services/domain-seeder/package.json)
- [services/admin/package.json](file://services/admin/package.json)

## Performance Considerations

The architecture is designed for horizontal scalability with stateless services that can be independently scaled. The Worker Service can be scaled based on queue depth, while the Web Application scales with incoming request volume. Data stores are optimized for their specific use cases: Manticore for full-text search, ScyllaDB for time-series ranking data, and MariaDB for relational domain metadata.

Caching is implemented at multiple levels using Redis for both application-level caching and job queuing. The Domain Seeder implements rate limiting and backpressure controls to prevent overwhelming external data sources.

## Troubleshooting Guide

Key monitoring endpoints:
- Web Application: `/health` (port 3000)
- Worker Service: `/health` (port 3000)
- Domain Seeder: `/health` endpoint in health.ts
- Admin Interface: healthcheck.js script

Log locations:
- All services write to local `logs/` directory
- Centralized logging configured in admin/logging/centralized-logging.js
- Error tracking through Pino logger with structured JSON output

Common issues:
- Redis connection failures: check network policies and authentication
- Database connectivity: verify environment variables and service availability
- High memory usage: monitor Node.js heap and adjust worker concurrency
- Processing backlogs: scale worker instances or optimize job processing

**Section sources**
- [services/web-app/Dockerfile](file://services/web-app/Dockerfile)
- [services/worker/Dockerfile](file://services/worker/Dockerfile)
- [services/domain-seeder/Dockerfile](file://services/domain-seeder/Dockerfile)
- [services/admin/Dockerfile](file://services/admin/Dockerfile)

## Conclusion

The domainr service architecture implements a robust microservices design with clear separation of concerns. The system leverages modern Node.js technologies with TypeScript, containerization via Docker, and orchestration through docker-compose. Event-driven communication via Redis enables loose coupling between services, while shared libraries ensure consistency across the codebase. The architecture supports horizontal scaling, fault tolerance, and comprehensive monitoring, making it suitable for high-volume domain processing at scale.