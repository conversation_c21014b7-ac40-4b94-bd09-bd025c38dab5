# Domain Seeder

<cite>
**Referenced Files in This Document**   
- [SeederConfig.ts](file://services/domain-seeder/src/config/SeederConfig.ts)
- [IntelligentDiscoveryEngine.ts](file://services/domain-seeder/src/discovery/IntelligentDiscoveryEngine.ts)
- [TrancoConnector.ts](file://services/domain-seeder/src/connectors/TrancoConnector.ts)
- [RadarConnector.ts](file://services/domain-seeder/src/connectors/RadarConnector.ts)
- [DifferentialAnalysisProcessor.ts](file://services/domain-seeder/src/discovery/strategies/DifferentialAnalysisProcessor.ts)
- [TemporalAnalysisProcessor.ts](file://services/domain-seeder/src/discovery/strategies/TemporalAnalysisProcessor.ts)
- [ZoneFileProcessor.ts](file://services/domain-seeder/src/discovery/strategies/ZoneFileProcessor.ts)
- [TokenBucket.ts](file://services/domain-seeder/src/ratelimiting/TokenBucket.ts)
- [PSLDomainNormalizer.ts](file://services/domain-seeder/src/normalization/PSLDomainNormalizer.ts)
- [ProvenanceTracker.ts](file://services/domain-seeder/src/provenance/ProvenanceTracker.ts)
- [CrashSafeReliabilityManager.ts](file://services/domain-seeder/src/reliability/CrashSafeReliabilityManager.ts)
- [health.ts](file://services/domain-seeder/src/health.ts)
- [HttpApiServer.ts](file://services/domain-seeder/src/api/HttpApiServer.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Discovery Engine Architecture](#discovery-engine-architecture)
3. [External Data Connectors](#external-data-connectors)
4. [Rate Limiting Implementation](#rate-limiting-implementation)
5. [Data Normalization and Provenance](#data-normalization-and-provenance)
6. [Reliability Mechanisms](#reliability-mechanisms)
7. [Configuration Management](#configuration-management)
8. [Health Endpoints and Monitoring](#health-endpoints-and-monitoring)
9. [Scalability Considerations](#scalability-considerations)
10. [Integration with Main Database](#integration-with-main-database)

## Introduction

The Domain Seeder service is a high-performance microservice responsible for discovering and ingesting new domains from multiple external sources. It employs various discovery strategies to identify newly registered or rapidly rising domains, processes them through a robust pipeline, and ensures reliable ingestion into the main database system. The service is designed for scalability, reliability, and efficient handling of large-scale domain discovery operations while respecting rate limits of external APIs.

**Section sources**
- [README.md](file://services/domain-seeder/README.md#L1-L100)

## Discovery Engine Architecture

The Domain Seeder's discovery engine is built around a modular architecture that supports multiple discovery strategies. The core component is the `IntelligentDiscoveryEngine`, which coordinates the execution of different strategies and aggregates results. The engine supports four primary discovery strategies: differential analysis, temporal analysis, zone file processing, and long-tail exploration.

The discovery process begins with the `IntelligentDiscoveryEngine` receiving domain candidates from various external sources through connectors. The engine then applies the appropriate strategy processors based on the source and discovery requirements. Each strategy processor implements a specific algorithm for identifying valuable domains:

- **Differential Analysis**: Compares current domain rankings with historical snapshots to identify newly appearing domains
- **Temporal Analysis**: Tracks ranking velocity over time to identify rapidly rising domains
- **Zone File Processing**: Analyzes zone file data to identify newly registered domains
- **Long-tail Exploration**: Discovers domains beyond typical top-1M lists

The engine maintains historical snapshots of domain rankings in a snapshot store, which enables differential analysis across multiple sources. These snapshots are stored in Redis, ScyllaDB, or other configured storage backends, allowing for efficient retrieval and comparison operations.

```mermaid
classDiagram
class IntelligentDiscoveryEngine {
+snapshotStore : SnapshotStore
+strategies : Map~DiscoveryStrategy, StrategyProcessor~
+registerStrategy(strategy, processor)
+processWithStrategy(strategy, candidates)
+getHistoricalSnapshot(source, date)
+storeSnapshot(source, domains)
+getMetrics()
}
class DiscoveryEngine {
<<interface>>
+registerStrategy(strategy, processor)
+processWithStrategy(strategy, candidates)
+getHistoricalSnapshot(source, date)
+storeSnapshot(source, domains)
+getMetrics()
}
class StrategyProcessor {
<<interface>>
+process(candidates, snapshotStore)
}
class DifferentialAnalysisProcessor {
+process(candidates, snapshotStore)
+findNewDomains(currentCandidates, historicalSnapshot)
}
class TemporalAnalysisProcessor {
+process(candidates, snapshotStore)
+buildRankingHistories(snapshots, candidates)
+identifyRisingDomains(histories)
}
class ZoneFileProcessor {
+process(candidates, snapshotStore)
+processRecentRegistrations(candidates)
+extractRegistrationInfo(candidate)
}
class SnapshotStore {
<<interface>>
+getSnapshot(source, date)
+storeSnapshot(source, domains)
+getRecentSnapshots(source, days)
}
IntelligentDiscoveryEngine ..> DiscoveryEngine : implements
IntelligentDiscoveryEngine --> StrategyProcessor : uses
IntelligentDiscoveryEngine --> SnapshotStore : uses
DifferentialAnalysisProcessor ..> StrategyProcessor : implements
TemporalAnalysisProcessor ..> StrategyProcessor : implements
ZoneFileProcessor ..> StrategyProcessor : implements
```

**Diagram sources**
- [IntelligentDiscoveryEngine.ts](file://services/domain-seeder/src/discovery/IntelligentDiscoveryEngine.ts#L1-L424)
- [DifferentialAnalysisProcessor.ts](file://services/domain-seeder/src/discovery/strategies/DifferentialAnalysisProcessor.ts#L1-L301)
- [TemporalAnalysisProcessor.ts](file://services/domain-seeder/src/discovery/strategies/TemporalAnalysisProcessor.ts#L1-L557)
- [ZoneFileProcessor.ts](file://services/domain-seeder/src/discovery/strategies/ZoneFileProcessor.ts#L1-L416)

**Section sources**
- [IntelligentDiscoveryEngine.ts](file://services/domain-seeder/src/discovery/IntelligentDiscoveryEngine.ts#L1-L424)
- [DifferentialAnalysisProcessor.ts](file://services/domain-seeder/src/discovery/strategies/DifferentialAnalysisProcessor.ts#L1-L301)
- [TemporalAnalysisProcessor.ts](file://services/domain-seeder/src/discovery/strategies/TemporalAnalysisProcessor.ts#L1-L557)
- [ZoneFileProcessor.ts](file://services/domain-seeder/src/discovery/strategies/ZoneFileProcessor.ts#L1-L416)

## External Data Connectors

The Domain Seeder service integrates with multiple external data sources through a connector system. Each connector implements the `SourceConnector` interface and provides access to a specific data source. The service currently supports connectors for CZDS, CommonCrawl, PIR, Radar, Sonar, Tranco, and Umbrella.

The connector system is designed to be extensible, allowing new data sources to be added without modifying the core discovery engine. Each connector handles the specific authentication, rate limiting, and data format requirements of its corresponding source. The connectors are responsible for fetching domain data, normalizing it into a common format, and streaming it to the discovery engine.

The Tranco connector, for example, downloads compressed CSV files containing domain rankings, extracts them, and streams the domains to the discovery engine. It supports both differential analysis and long-tail exploration strategies by filtering domains based on their rank position. The Radar connector, on the other hand, uses the Cloudflare Radar API to fetch domain rankings and provides additional metadata such as category, subcategory, and popularity rank.

```mermaid
classDiagram
class SourceConnector {
<<interface>>
+name : string
+priority : number
+cadence : string
+supportsStrategy(strategy)
+fetchDomains(options)
+getLastUpdate()
+healthCheck()
}
class TrancoConnector {
+fetchDomains(options)
+getCurrentListInfo()
+downloadAndExtractList(listInfo)
+streamDomains(csvFilePath, options)
}
class RadarConnector {
+fetchDomains(options)
+fetchDomainsWithPagination(options)
+fetchDomainsWithTemporalAnalysis(options)
+hasSignificantChange(domainData)
+isRisingDomain(domainData)
}
class CZDSConnector {
+fetchDomains(options)
+authenticate()
+downloadZoneFiles()
+parseZoneFileData()
}
class UmbrellaConnector {
+fetchDomains(options)
+getTopDomains()
+getDomainCategories()
+getSecurityRankings()
}
SourceConnector <|-- TrancoConnector
SourceConnector <|-- RadarConnector
SourceConnector <|-- CZDSConnector
SourceConnector <|-- UmbrellaConnector
```

**Diagram sources**
- [TrancoConnector.ts](file://services/domain-seeder/src/connectors/TrancoConnector.ts#L1-L420)
- [RadarConnector.ts](file://services/domain-seeder/src/connectors/RadarConnector.ts#L1-L532)
- [interfaces/SourceConnector.ts](file://services/domain-seeder/src/interfaces/SourceConnector.ts#L1-L20)

**Section sources**
- [TrancoConnector.ts](file://services/domain-seeder/src/connectors/TrancoConnector.ts#L1-L420)
- [RadarConnector.ts](file://services/domain-seeder/src/connectors/RadarConnector.ts#L1-L532)
- [connectors/index.ts](file://services/domain-seeder/src/connectors/index.ts#L1-L12)

## Rate Limiting Implementation

The Domain Seeder service implements rate limiting using the token bucket algorithm to prevent overwhelming external APIs and ensure compliance with their usage policies. The rate limiting system is implemented in the `TokenBucket` class, which provides a flexible and efficient mechanism for controlling the rate of API requests.

Each external data source has its own rate limiting configuration, specified in the service configuration. The configuration includes parameters such as the rate limit window (in milliseconds), the maximum number of requests allowed in that window, and the refill rate of tokens. The token bucket algorithm works by maintaining a bucket of tokens that are consumed with each API request. When the bucket is empty, requests are delayed until tokens are refilled at the specified rate.

The rate limiting system is integrated at multiple levels of the service. At the connector level, each connector respects the rate limits of its corresponding API by introducing appropriate delays between requests. At the enqueuer level, the `RateLimitedDomainEnqueuer` ensures that domains are enqueued at a controlled rate, preventing overload of downstream systems.

```mermaid
classDiagram
class TokenBucket {
-capacity : number
-tokens : number
-refillRate : number
-lastRefillTime : Date
+constructor(capacity, refillRate)
+take(tokens) : boolean
+tryTake(tokens) : boolean
+getTokens() : number
+setTokens(tokens)
+refill()
}
class RateLimitedDomainEnqueuer {
-tokenBucket : TokenBucket
-enqueueBatch : number
-enqueueIntervalMs : number
+enqueue(domains)
+processQueue()
+getQueueDepth()
}
class BackpressureController {
-currentLoad : number
-maxLoad : number
-throttleThreshold : number
+shouldThrottle() : boolean
+adjustRate()
+getLoadMetrics()
}
RateLimitedDomainEnqueuer --> TokenBucket : uses
RateLimitedDomainEnqueuer --> BackpressureController : uses
```

**Diagram sources**
- [ratelimiting/TokenBucket.ts](file://services/domain-seeder/src/ratelimiting/TokenBucket.ts#L1-L50)
- [enqueuer/RateLimitedDomainEnqueuer.ts](file://services/domain-seeder/src/enqueuer/RateLimitedDomainEnqueuer.ts#L1-L30)
- [ratelimiting/BackpressureController.ts](file://services/domain-seeder/src/ratelimiting/BackpressureController.ts#L1-L40)

**Section sources**
- [ratelimiting/TokenBucket.ts](file://services/domain-seeder/src/ratelimiting/TokenBucket.ts#L1-L50)
- [enqueuer/RateLimitedDomainEnqueuer.ts](file://services/domain-seeder/src/enqueuer/RateLimitedDomainEnqueuer.ts#L1-L30)

## Data Normalization and Provenance

The Domain Seeder service includes a comprehensive data normalization process to ensure consistency and accuracy of domain data. The normalization system is implemented in the `PSLDomainNormalizer` class, which uses the Public Suffix List (PSL) to correctly identify the registrable domain and public suffix for each domain.

The normalization process involves several steps:
1. Parsing the domain name to identify its components
2. Using the PSL to determine the registrable domain and public suffix
3. Converting the domain to lowercase for consistency
4. Removing any leading or trailing whitespace
5. Validating the domain format according to DNS standards

Provenance tracking is implemented through the `ProvenanceTracker` class, which maintains a complete record of the origin and processing history of each domain. For each discovered domain, the provenance tracker records:
- The source(s) from which the domain was discovered
- The discovery strategy used
- The timestamp of discovery
- The confidence score of the discovery
- Any metadata associated with the discovery
- The processing steps applied to the domain

This provenance information is stored alongside the domain data in the main database, enabling auditability and traceability of domain discoveries.

```mermaid
classDiagram
class DomainNormalizer {
<<interface>>
+normalize(domain) : NormalizedDomain
+validate(domain) : boolean
}
class PSLDomainNormalizer {
-pslManager : PSLManager
+normalize(domain) : NormalizedDomain
+updatePSL()
+getPSLVersion()
}
class PSLManager {
+downloadPSL()
+parsePSL(data)
+isSuffix(domain)
+getRegistrableDomain(domain)
+getPublicSuffix(domain)
}
class ProvenanceTracker {
+trackDiscovery(domain, source, strategy, confidence)
+addMetadata(domain, metadata)
+getProvenance(domain)
+exportProvenanceReport()
}
class NormalizedDomain {
+domain : string
+registrableDomain : string
+publicSuffix : string
+normalized : string
+isValid : boolean
+source : string
+strategy : string
+confidence : number
+timestamp : Date
}
DomainNormalizer <|-- PSLDomainNormalizer
PSLDomainNormalizer --> PSLManager : uses
ProvenanceTracker --> NormalizedDomain : produces
```

**Diagram sources**
- [normalization/PSLDomainNormalizer.ts](file://services/domain-seeder/src/normalization/PSLDomainNormalizer.ts#L1-L40)
- [normalization/PSLManager.ts](file://services/domain-seeder/src/normalization/PSLManager.ts#L1-L30)
- [provenance/ProvenanceTracker.ts](file://services/domain-seeder/src/provenance/ProvenanceTracker.ts#L1-L50)

**Section sources**
- [normalization/PSLDomainNormalizer.ts](file://services/domain-seeder/src/normalization/PSLDomainNormalizer.ts#L1-L40)
- [provenance/ProvenanceTracker.ts](file://services/domain-seeder/src/provenance/ProvenanceTracker.ts#L1-L50)

## Reliability Mechanisms

The Domain Seeder service incorporates several reliability mechanisms to ensure robust operation and data integrity. The core reliability component is the `CrashSafeReliabilityManager`, which provides crash-safe operations and ensures that domain discoveries are not lost in the event of a service interruption.

The reliability system includes the following key components:
- **Crash-safe operations**: All critical operations are designed to be idempotent and can be safely retried in case of failure
- **Transaction logging**: A transaction log records all domain discoveries and processing steps, enabling recovery from failures
- **Checkpointing**: The system periodically creates checkpoints of its state, allowing it to resume from the last known good state after a restart
- **Error recovery**: Comprehensive error handling and recovery mechanisms are in place to handle transient failures and network issues

The service also implements a reliable stream processor that ensures domains are processed exactly once, even in the presence of failures. This is achieved through a combination of message acknowledgments, retry mechanisms, and deduplication checks.

```mermaid
classDiagram
class ReliabilityManager {
<<interface>>
+ensureReliability(operation)
+handleFailure(error)
+recoverFromFailure()
+getReliabilityMetrics()
}
class CrashSafeReliabilityManager {
-transactionLog : TransactionLog
-checkpointManager : CheckpointManager
-retryManager : RetryManager
+ensureReliability(operation)
+handleFailure(error)
+recoverFromFailure()
+createCheckpoint()
+restoreFromCheckpoint()
}
class TransactionLog {
+logOperation(operation)
+getPendingOperations()
+markComplete(operationId)
+replayLog()
}
class CheckpointManager {
+createCheckpoint(state)
+restoreCheckpoint(checkpointId)
+listCheckpoints()
+pruneOldCheckpoints()
}
class RetryManager {
+scheduleRetry(operation, delay)
+executeRetries()
+getRetryQueueSize()
}
class ReliableStreamProcessor {
-acknowledgmentTracker : AcknowledgmentTracker
-deduplicationCache : DeduplicationCache
+processStream(stream)
+acknowledgeMessage(messageId)
+reprocessFailedMessages()
}
ReliabilityManager <|-- CrashSafeReliabilityManager
CrashSafeReliabilityManager --> TransactionLog : uses
CrashSafeReliabilityManager --> CheckpointManager : uses
CrashSafeReliabilityManager --> RetryManager : uses
ReliableStreamProcessor --> AcknowledgmentTracker : uses
ReliableStreamProcessor --> DeduplicationCache : uses
```

**Diagram sources**
- [reliability/CrashSafeReliabilityManager.ts](file://services/domain-seeder/src/reliability/CrashSafeReliabilityManager.ts#L1-L50)
- [reliability/RetryManager.ts](file://services/domain-seeder/src/reliability/RetryManager.ts#L1-L40)
- [reliability/ReliableStreamProcessor.ts](file://services/domain-seeder/src/reliability/ReliableStreamProcessor.ts#L1-L60)

**Section sources**
- [reliability/CrashSafeReliabilityManager.ts](file://services/domain-seeder/src/reliability/CrashSafeReliabilityManager.ts#L1-L50)

## Configuration Management

The Domain Seeder service uses a comprehensive configuration management system implemented in the `SeederConfig` class. The configuration system supports environment-specific profiles (development, staging, production) and allows for dynamic configuration updates through hot reloading.

The configuration is structured into several categories:
- **Environment configuration**: Service name, version, port, and environment settings
- **Database configuration**: Connection details for ScyllaDB, MariaDB, Manticore, and Redis
- **Seeder configuration**: Parameters controlling discovery operations, such as maximum new domains per day and enqueue batch sizes
- **Source configuration**: Settings for each external data source, including API keys, priorities, and rate limits
- **AI configuration**: Settings for AI providers used in content generation
- **Monitoring configuration**: Settings for metrics, health checks, and alerting
- **Security configuration**: Encryption keys, credential rotation intervals, and TLS settings

Configuration values can be set through environment variables, configuration files, or a combination of both. The system validates all configuration values using Zod schemas and provides detailed error messages for invalid configurations.

```mermaid
classDiagram
class SeederConfig {
-configuration : SeederConfiguration
-validationErrors : string[]
-validationWarnings : string[]
-hotReloadOptions : ConfigHotReloadOptions
-watchers : FSWatcher[]
+getConfiguration()
+getEnvironmentConfig()
+getDatabaseConfig()
+getSeederConfig()
+getSourcesConfig()
+getAIConfig()
+getMonitoringConfig()
+getSecurityConfig()
+enableHotReload(options)
+disableHotReload()
}
class SeederConfiguration {
+environment : EnvironmentConfig
+database : DatabaseConfig
+seeder : SeederConfigSettings
+sources : SourcesConfig
+ai : AIConfig
+monitoring : MonitoringConfig
+security : SecurityConfig
}
class EnvironmentConfig {
+nodeEnv : string
+serviceName : string
+serviceVersion : string
+port : number
+timezone : string
+gracefulShutdownTimeoutMs : number
}
class DatabaseConfig {
+scylla : ScyllaConfig
+maria : MariaConfig
+manticore : ManticoreConfig
+redis : RedisConfig
}
SeederConfig --> SeederConfiguration : uses
SeederConfiguration --> EnvironmentConfig : contains
SeederConfiguration --> DatabaseConfig : contains
```

**Diagram sources**
- [config/SeederConfig.ts](file://services/domain-seeder/src/config/SeederConfig.ts#L1-L850)

**Section sources**
- [config/SeederConfig.ts](file://services/domain-seeder/src/config/SeederConfig.ts#L1-L850)

## Health Endpoints and Monitoring

The Domain Seeder service provides comprehensive health endpoints and monitoring capabilities to ensure operational visibility and reliability. The health system is implemented in the `HealthChecker` class and exposes multiple endpoints for different health check purposes.

The service provides the following health endpoints:
- **Liveness endpoint** (`/health`): Indicates whether the service is running and able to respond to requests
- **Readiness endpoint** (`/ready`): Indicates whether the service is ready to accept traffic and all dependencies are available
- **Detailed status endpoint** (`/status`): Provides comprehensive system status information, including discovery metrics, queue depths, and component health

The monitoring system collects and exposes metrics in Prometheus format, enabling integration with monitoring and alerting systems. Key metrics include:
- Discovery metrics: Number of candidates fetched, domains discovered, and processing duration
- Queue metrics: Current queue sizes, processing rates, and failure rates
- System metrics: Memory usage, CPU utilization, and database connection counts
- External API metrics: Request rates, success rates, and response times for external data sources

```mermaid
classDiagram
class HealthChecker {
+checkLiveness()
+checkReadiness()
+getDetailedStatus()
+registerHealthIndicator(indicator)
+getHealthIndicators()
}
class HealthIndicator {
<<interface>>
+getName() : string
+checkHealth() : HealthStatus
}
class DatabaseHealthIndicator {
+checkHealth() : HealthStatus
+getConnectionStatus()
}
class QueueHealthIndicator {
+checkHealth() : HealthStatus
+getQueueDepth()
+getProcessingRate()
}
class ExternalAPIHealthIndicator {
+checkHealth() : HealthStatus
+getAPIStatus()
+getResponseTime()
}
class MetricsCollector {
+collectMetrics()
+exportMetrics()
+registerMetric(metric)
}
HealthChecker --> HealthIndicator : uses
HealthChecker --> MetricsCollector : uses
DatabaseHealthIndicator ..> HealthIndicator : implements
QueueHealthIndicator ..> HealthIndicator : implements
ExternalAPIHealthIndicator ..> HealthIndicator : implements
```

**Diagram sources**
- [monitoring/HealthChecker.ts](file://services/domain-seeder/src/monitoring/HealthChecker.ts#L1-L40)
- [monitoring/MetricsCollector.ts](file://services/domain-seeder/src/monitoring/MetricsCollector.ts#L1-L30)
- [routes/health.ts](file://services/domain-seeder/src/routes/health.ts#L1-L20)

**Section sources**
- [monitoring/HealthChecker.ts](file://services/domain-seeder/src/monitoring/HealthChecker.ts#L1-L40)
- [health.ts](file://services/domain-seeder/src/health.ts#L1-L30)
- [api/HttpApiServer.ts](file://services/domain-seeder/src/api/HttpApiServer.ts#L1-L50)

## Scalability Considerations

The Domain Seeder service is designed with scalability in mind to handle large-scale domain discovery operations. The architecture supports horizontal scaling through several mechanisms:

- **Parallel processing**: The discovery engine can process multiple strategies and sources in parallel, maximizing resource utilization
- **Batch processing**: Domains are processed in batches to reduce overhead and improve throughput
- **Asynchronous operations**: I/O operations are performed asynchronously to prevent blocking and improve responsiveness
- **Distributed architecture**: The service can be deployed in a distributed environment with multiple instances sharing the workload

To handle large volumes of domain data, the service implements several optimization strategies:
- **Efficient data structures**: Bloom filters are used to quickly check for domain existence without querying the database
- **Caching**: Frequently accessed data is cached in Redis to reduce database load
- **Connection pooling**: Database connections are pooled to reduce connection overhead
- **Stream processing**: Large datasets are processed as streams to minimize memory usage

The service also includes mechanisms to avoid rate limits on external APIs:
- **Rate limiting**: The token bucket algorithm ensures requests are made within the allowed rate limits
- **Request scheduling**: Requests are scheduled to distribute load evenly over time
- **Retry with backoff**: Failed requests are retried with exponential backoff to handle temporary issues
- **Source prioritization**: Higher priority sources are processed first, ensuring critical data is retrieved even under rate limiting constraints

**Section sources**
- [repositories/BloomFilter.ts](file://services/domain-seeder/src/repositories/BloomFilter.ts#L1-L30)
- [repositories/RedisCacheLayer.ts](file://services/domain-seeder/src/repositories/RedisCacheLayer.ts#L1-L40)
- [enqueuer/RateLimitedDomainEnqueuer.ts](file://services/domain-seeder/src/enqueuer/RateLimitedDomainEnqueuer.ts#L1-L30)

## Integration with Main Database

The Domain Seeder service integrates with the main database system through a repository layer that provides an abstraction over the underlying data storage. The service supports multiple database technologies, including ScyllaDB, MariaDB, and Manticore Search, allowing for flexible deployment configurations.

The integration is implemented through the `DomainRepository` interface, which defines the operations for storing and retrieving domain data. Concrete implementations are provided for each supported database technology:
- **ScyllaDomainRepository**: For storing domain data in ScyllaDB
- **MariaDomainRepository**: For storing domain data in MariaDB
- **ManticoreDomainRepository**: For indexing domain data in Manticore Search

The repository layer handles data consistency across multiple storage systems through the `DataConsistencyService`, which ensures that domain data is synchronized across all configured databases. This service uses a transaction log to track changes and applies them to all databases in a consistent order.

```mermaid
classDiagram
class DomainRepository {
<<interface>>
+saveDomain(domain)
+getDomain(domain)
+exists(domain)
+batchSave(domains)
+search(query)
}
class ScyllaDomainRepository {
+saveDomain(domain)
+getDomain(domain)
+exists(domain)
+batchSave(domains)
}
class MariaDomainRepository {
+saveDomain(domain)
+getDomain(domain)
+exists(domain)
+batchSave(domains)
+search(query)
}
class ManticoreDomainRepository {
+saveDomain(domain)
+indexDomain(domain)
+search(query)
+deleteDomain(domain)
}
class CompositeDomainRepository {
-repositories : DomainRepository[]
+saveDomain(domain)
+getDomain(domain)
+exists(domain)
+batchSave(domains)
+search(query)
}
class DataConsistencyService {
+synchronizeData()
+resolveConflicts()
+getConsistencyStatus()
}
DomainRepository <|-- ScyllaDomainRepository
DomainRepository <|-- MariaDomainRepository
DomainRepository <|-- ManticoreDomainRepository
CompositeDomainRepository --> DomainRepository : uses
DataConsistencyService --> CompositeDomainRepository : uses
```

**Diagram sources**
- [repositories/DomainRepository.ts](file://services/domain-seeder/src/repositories/DomainRepository.ts#L1-L30)
- [repositories/ScyllaDomainRepository.ts](file://services/domain-seeder/src/repositories/ScyllaDomainRepository.ts#L1-L40)
- [repositories/MariaDomainRepository.ts](file://services/domain-seeder/src/repositories/MariaDomainRepository.ts#L1-L40)
- [repositories/ManticoreDomainRepository.ts](file://services/domain-seeder/src/repositories/ManticoreDomainRepository.ts#L1-L40)
- [synchronization/DataConsistencyService.ts](file://services/domain-seeder/src/synchronization/DataConsistencyService.ts#L1-L30)

**Section sources**
- [repositories/DomainRepository.ts](file://services/domain-seeder/src/repositories/DomainRepository.ts#L1-L30)
- [synchronization/DataConsistencyService.ts](file://services/domain-seeder/src/synchronization/DataConsistencyService.ts#L1-L30)