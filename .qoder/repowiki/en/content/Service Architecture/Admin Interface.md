# Admin Interface

<cite>
**Referenced Files in This Document**   
- [README.md](file://services/admin/README.md)
- [page.tsx](file://services/admin/src/app/page.tsx)
- [login/page.tsx](file://services/admin/src/app/login/page.tsx)
- [api/auth/login/route.ts](file://services/admin/src/app/api/auth/login/route.ts)
- [auth.ts](file://services/admin/src/types/auth.ts)
- [auth.test.ts](file://services/admin/src/lib/auth/__tests__/auth.test.ts)
- [security-headers.js](file://services/admin/security/security-headers.js)
- [centralized-logging.js](file://services/admin/logging/centralized-logging.js)
- [backup-manager.js](file://services/admin/backup/backup-manager.js)
- [useRealtime.ts](file://services/admin/src/hooks/useRealtime.ts)
- [useRealtimeSubscription.ts](file://services/admin/src/hooks/useRealtimeSubscription.ts)
- [alert_rules.yml](file://services/admin/monitoring/alert_rules.yml)
- [alertmanager.yml](file://services/admin/monitoring/alertmanager.yml)
- [grafana-dashboard.json](file://services/admin/monitoring/grafana-dashboard.json)
- [prometheus.yml](file://services/admin/monitoring/prometheus.yml)
- [docker-compose.production.yml](file://services/admin/docker-compose.production.yml)
- [docker-compose.simple.yml](file://services/admin/docker-compose.simple.yml)
- [DEPLOYMENT.md](file://services/admin/DEPLOYMENT.md)
- [TESTING.md](file://services/admin/TESTING.md)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Project Structure](#project-structure)
3. [Core Components](#core-components)
4. [Architecture Overview](#architecture-overview)
5. [Detailed Component Analysis](#detailed-component-analysis)
6. [Dependency Analysis](#dependency-analysis)
7. [Performance Considerations](#performance-considerations)
8. [Troubleshooting Guide](#troubleshooting-guide)
9. [Conclusion](#conclusion)

## Introduction
The Admin Interface service serves as the central management and monitoring dashboard for the Domain Ranking System. Built with Next.js and Mantine UI, this comprehensive administrative platform provides system administrators with complete control over the entire domain analysis ecosystem. The interface enables real-time monitoring of microservices, domain management, crawl job scheduling, user administration, system configuration, analytics reporting, and alert management. Designed with security, scalability, and usability in mind, the Admin Interface integrates with multiple databases (ScyllaDB, MariaDB, Redis, Manticore) and external services to provide a unified view of system health and performance. The service implements robust authentication mechanisms, role-based access control, and comprehensive audit logging to ensure secure access and compliance with operational requirements.

**Section sources**
- [README.md](file://services/admin/README.md#L1-L703)

## Project Structure
The Admin Interface follows a structured Next.js App Router architecture with clear separation of concerns. The project is organized into distinct directories for source code, configuration, testing, and operational scripts. The core application resides in the `src` directory, which contains the App Router pages, reusable components, business logic libraries, type definitions, and utility functions. Configuration files are maintained in the root directory, including environment variables, Docker configurations, and deployment manifests. The service-specific configuration and monitoring files are organized in dedicated subdirectories for security, logging, backup management, and monitoring integrations. Testing is comprehensive, with unit, integration, end-to-end, load, and visual regression tests organized in the `tests` directory. The deployment strategy supports both production and simple configurations through separate Docker Compose files, enabling flexible deployment options based on operational requirements.

```mermaid
graph TD
A[Admin Interface] --> B[src/]
A --> C[config/]
A --> D[scripts/]
A --> E[tests/]
A --> F[Dockerfile]
A --> G[docker-compose.*.yml]
B --> H[app/]
B --> I[components/]
B --> J[lib/]
B --> K[types/]
B --> L[utils/]
B --> M[hooks/]
H --> N[(authenticated)]
H --> O[login/]
H --> P[api/]
C --> Q[security/]
C --> R[logging/]
C --> S[backup/]
C --> T[monitoring/]
C --> U[nginx/]
D --> V[deploy.sh]
D --> W[run-tests.sh]
D --> X[production-health-check.sh]
E --> Y[chaos/]
E --> Z[e2e/]
E --> AA[load/]
E --> AB[visual/]
```

**Diagram sources**
- [README.md](file://services/admin/README.md#L1-L703)
- [package.json](file://services/admin/package.json#L1-L50)

**Section sources**
- [README.md](file://services/admin/README.md#L1-L703)

## Core Components
The Admin Interface comprises several core components that enable comprehensive system management and monitoring. The authentication system implements session-based authentication with iron-session, featuring role-based access control and comprehensive audit logging. User management functionality supports CRUD operations for administrative users with different permission levels (super_admin, admin, viewer). The real-time monitoring system provides live updates on service health, database status, and system performance through both SSE and WebSockets. Analytics visualization components present system metrics and business intelligence through interactive charts and dashboards. Alert management enables configuration of alert rules, notification channels, and incident response workflows. The interface also includes tools for database operations, backup management, and system configuration. Security features include input validation, CSRF protection, rate limiting, and secure HTTP headers to protect against common web vulnerabilities.

**Section sources**
- [README.md](file://services/admin/README.md#L1-L703)
- [auth.ts](file://services/admin/src/types/auth.ts#L1-L253)

## Architecture Overview
The Admin Interface follows a modern web application architecture built on Next.js 14 with the App Router pattern. The frontend is implemented with React components using the Mantine UI library for consistent styling and responsive design. The application follows a client-server architecture where the Next.js server handles API routes, authentication, and server-side rendering, while the client-side components manage user interactions and real-time updates. The service integrates with multiple backend systems including ScyllaDB for domain analysis data, MariaDB for configuration and user data, Redis for sessions and caching, and Manticore for search indexes. External microservices (web-app, worker, domain-seeder) are monitored and controlled through dedicated API endpoints. The architecture supports both server-side and client-side rendering, with SWR used for real-time data fetching and revalidation. Security is implemented at multiple layers including authentication middleware, input validation, and secure HTTP headers.

```mermaid
graph TD
subgraph "Frontend"
A[Browser] --> B[Next.js App Router]
B --> C[Mantine UI Components]
B --> D[Recharts]
B --> E[SWR]
end
subgraph "Backend"
F[Next.js Server]
F --> G[API Routes]
F --> H[Authentication]
F --> I[Middleware]
end
subgraph "Data Stores"
J[ScyllaDB]
K[MariaDB]
L[Redis]
M[Manticore]
end
subgraph "External Services"
N[Web App]
O[Worker Service]
P[Domain Seeder]
end
subgraph "Monitoring"
Q[Prometheus]
R[Grafana]
S[Alertmanager]
end
B --> F
G --> J
G --> K
G --> L
G --> M
G --> N
G --> O
G --> P
H --> L
I --> Q
Q --> R
Q --> S
```

**Diagram sources**
- [README.md](file://services/admin/README.md#L1-L703)
- [docker-compose.production.yml](file://services/admin/docker-compose.production.yml#L1-L50)

## Detailed Component Analysis

### Authentication and Authorization
The Admin Interface implements a robust authentication and authorization system with multiple security layers. The authentication mechanism uses session-based authentication with iron-session for secure cookie management. Users are authenticated against predefined credentials with password hashing using bcrypt. The system supports three distinct roles (super_admin, admin, viewer) with granular permission controls defined in the ROLE_PERMISSIONS mapping. Each permission corresponds to specific actions within the system, enabling fine-grained access control. The authentication process includes client IP and user agent tracking for security auditing. Rate limiting is implemented to prevent brute force attacks, with automatic lockout after multiple failed attempts. Session management includes timeout controls and the ability to terminate sessions, with comprehensive audit logging of all authentication events.

```mermaid
classDiagram
class UserRoleType {
<<enumeration>>
super_admin
admin
viewer
}
class PermissionType {
<<enumeration>>
users.view
users.create
users.edit
users.delete
services.view
services.restart
domains.view
domains.edit
crawl.view
crawl.create
analytics.view
config.view
config.edit
logs.view
ai.view
}
class AdminUserType {
+id : string
+username : string
+role : UserRoleType
+permissions : PermissionType[]
+createdAt : Date
+lastLogin : Date
+isActive : boolean
+sessionCount : number
}
class UserSessionType {
+sessionId : string
+userId : string
+username : string
+role : UserRoleType
+permissions : PermissionType[]
+createdAt : Date
+lastActivity : Date
+expiresAt : Date
+ipAddress : string
+userAgent : string
+isActive : boolean
}
class AuthService {
+authenticate(username, password, ipAddress, userAgent)
+validateSession(sessionId)
+logout(sessionId)
+getUserSessions(username)
+destroyAllUserSessions(username)
}
class PasswordPolicyType {
+minLength : number
+requireUppercase : boolean
+requireLowercase : boolean
+requireNumbers : boolean
+requireSpecialChars : boolean
+maxAge : number
+historyCount : number
+maxLoginAttempts : number
+lockoutDuration : number
}
AdminUserType --> UserRoleType : "has"
AdminUserType --> PermissionType : "has"
UserSessionType --> AdminUserType : "references"
AuthService --> UserSessionType : "manages"
AuthService --> AdminUserType : "authenticates"
PasswordPolicyType --> AdminUserType : "applies to"
```

**Diagram sources**
- [auth.ts](file://services/admin/src/types/auth.ts#L1-L253)
- [auth.test.ts](file://services/admin/src/lib/auth/__tests__/auth.test.ts#L1-L271)

**Section sources**
- [auth.ts](file://services/admin/src/types/auth.ts#L1-L253)
- [auth.test.ts](file://services/admin/src/lib/auth/__tests__/auth.test.ts#L1-L271)
- [api/auth/login/route.ts](file://services/admin/src/app/api/auth/login/route.ts#L1-L89)

### Real-time Updates and Monitoring
The Admin Interface implements real-time updates through multiple mechanisms to provide live system monitoring. The primary approach uses SWR (Stale While Revalidate) for data fetching, enabling automatic revalidation and real-time updates without manual refresh. For more immediate updates, the system supports both Server-Sent Events (SSE) and WebSockets through dedicated hooks (`useRealtime` and `useRealtimeSubscription`). These real-time capabilities are used extensively in the dashboard, service monitoring, and alert notification components. The monitoring system integrates with Prometheus for metrics collection, Grafana for visualization, and Alertmanager for notifications. Custom alert rules are defined in YAML configuration files and can be managed through the interface. The system provides comprehensive health checks for all microservices and databases, with visual indicators of service status and performance metrics.

```mermaid
sequenceDiagram
participant Browser
participant NextJS as Next.js Server
participant Prometheus
participant Grafana
participant Alertmanager
participant Microservices
Browser->>NextJS : Load Dashboard
NextJS->>Prometheus : Query system metrics
Prometheus-->>NextJS : Return metrics data
NextJS->>Browser : Render dashboard
NextJS->>Microservices : Health check requests
Microservices-->>NextJS : Health status
NextJS->>Browser : Update service status
loop Real-time Updates
NextJS->>Prometheus : SSE connection
Prometheus->>NextJS : Push metric updates
NextJS->>Browser : WebSocket update
Browser->>Browser : Update UI components
end
Prometheus->>Alertmanager : Trigger alert based on rules
Alertmanager->>NextJS : Send alert notification
NextJS->>Browser : Display alert in UI
Browser->>User : Show notification
```

**Diagram sources**
- [useRealtime.ts](file://services/admin/src/hooks/useRealtime.ts#L1-L20)
- [useRealtimeSubscription.ts](file://services/admin/src/hooks/useRealtimeSubscription.ts#L1-L20)
- [alert_rules.yml](file://services/admin/monitoring/alert_rules.yml#L1-L50)
- [alertmanager.yml](file://services/admin/monitoring/alertmanager.yml#L1-L50)

**Section sources**
- [useRealtime.ts](file://services/admin/src/hooks/useRealtime.ts#L1-L20)
- [useRealtimeSubscription.ts](file://services/admin/src/hooks/useRealtimeSubscription.ts#L1-L20)
- [alert_rules.yml](file://services/admin/monitoring/alert_rules.yml#L1-L50)

### Centralized Logging and Audit Trail
The Admin Interface implements comprehensive logging and audit trail capabilities through a centralized logging system. All user actions, authentication events, and system operations are logged with detailed context including timestamps, user identifiers, IP addresses, and user agents. The logging system uses structured JSON format for easy parsing and analysis. Audit logs capture both successful and failed operations, with severity levels assigned to different event types. Security-critical events such as failed login attempts, permission changes, and configuration modifications are flagged for immediate attention. The system supports log export and search capabilities, enabling forensic analysis and compliance reporting. Logs are stored in a durable storage system and can be integrated with external SIEM solutions for advanced threat detection and response.

```mermaid
flowchart TD
A[User Action] --> B{Action Type}
B --> |Authentication| C[Log Login/Logout]
B --> |Configuration| D[Log Config Change]
B --> |User Management| E[Log User Operation]
B --> |Service Control| F[Log Service Action]
B --> |Data Operation| G[Log Data Change]
C --> H[Include: Timestamp, User, IP, User Agent, Success/Failure]
D --> H
E --> H
F --> H
G --> H
H --> I[Store in Centralized Log]
I --> J[Classify by Severity]
J --> K{Critical Event?}
K --> |Yes| L[Trigger Immediate Alert]
K --> |No| M[Store for Analysis]
L --> N[Send to Alertmanager]
M --> O[Aggregate for Reporting]
O --> P[Generate Audit Reports]
N --> Q[Notify Admins]
P --> R[Compliance Documentation]
```

**Diagram sources**
- [centralized-logging.js](file://services/admin/logging/centralized-logging.js#L1-L50)
- [auth.ts](file://services/admin/src/types/auth.ts#L1-L253)

**Section sources**
- [centralized-logging.js](file://services/admin/logging/centralized-logging.js#L1-L50)
- [auth.ts](file://services/admin/src/types/auth.ts#L1-L253)

## Dependency Analysis
The Admin Interface has dependencies on multiple internal and external systems. The primary dependencies include the Next.js framework, Mantine UI library, and various Node.js packages for authentication, validation, and API development. The service depends on several databases: ScyllaDB for domain analysis data, MariaDB for configuration and user data, Redis for session storage and caching, and Manticore for search functionality. It also integrates with external microservices including the web-app, worker, and domain-seeder services for system operations. Monitoring dependencies include Prometheus for metrics collection, Grafana for visualization, and Alertmanager for notifications. The deployment infrastructure relies on Docker and Docker Compose for containerization and orchestration. Security dependencies include iron-session for session management and bcrypt for password hashing. The testing framework uses Vitest for unit and integration tests, Playwright for end-to-end testing, and various tools for load and visual regression testing.

```mermaid
graph TD
A[Admin Interface] --> B[Next.js]
A --> C[Mantine UI]
A --> D[Recharts]
A --> E[SWR]
A --> F[iron-session]
A --> G[bcrypt]
A --> H[TypeScript]
A --> I[ScyllaDB]
A --> J[MariaDB]
A --> K[Redis]
A --> L[Manticore]
A --> M[Web App Service]
A --> N[Worker Service]
A --> O[Domain Seeder]
A --> P[Prometheus]
A --> Q[Grafana]
A --> R[Alertmanager]
A --> S[Docker]
A --> T[Docker Compose]
A --> U[Vitest]
A --> V[Playwright]
B --> W[React]
C --> X[React]
E --> Y[React]
F --> Z[Node.js]
G --> AA[Node.js]
```

**Diagram sources**
- [package.json](file://services/admin/package.json#L1-L50)
- [docker-compose.production.yml](file://services/admin/docker-compose.production.yml#L1-L50)
- [README.md](file://services/admin/README.md#L1-L703)

**Section sources**
- [package.json](file://services/admin/package.json#L1-L50)
- [docker-compose.production.yml](file://services/admin/docker-compose.production.yml#L1-L50)
- [README.md](file://services/admin/README.md#L1-L703)

## Performance Considerations
The Admin Interface is designed with performance optimization in mind, implementing several strategies to ensure responsive user experience and efficient resource utilization. The Next.js App Router enables server-side rendering and static generation where appropriate, reducing client-side processing. SWR (Stale While Revalidate) is used for data fetching, providing instant loading from cache while background updates ensure data freshness. The interface implements code splitting and lazy loading for components, minimizing initial bundle size. Real-time updates are optimized through selective revalidation and efficient WebSocket/SSE implementations. Database queries are optimized with appropriate indexing and caching strategies. The system monitors performance metrics including response times, resource utilization, and error rates, with alerts configured for performance degradation. Load testing scripts are provided to validate system performance under expected traffic conditions. The deployment configuration includes health checks and auto-scaling capabilities to maintain performance during traffic spikes.

**Section sources**
- [README.md](file://services/admin/README.md#L1-L703)
- [TESTING.md](file://services/admin/TESTING.md#L1-L50)

## Troubleshooting Guide
Common issues with the Admin Interface typically fall into several categories: authentication problems, database connectivity issues, performance degradation, and deployment failures. For authentication issues, verify that the SESSION_SECRET is properly configured and of sufficient length (256-bit). Check that user credentials match the expected format and that account lockout policies are not preventing access. For database connectivity problems, verify connection strings and network accessibility to database hosts. Use the provided connection test scripts to isolate specific database issues. Performance issues may be addressed by checking resource utilization, optimizing database queries, and reviewing cache effectiveness. Deployment failures often relate to missing environment variables or incorrect Docker configurations. The system provides comprehensive logging that can be used to diagnose issues, with different log levels for error, warning, information, and debug messages. Health check endpoints are available to verify service status and dependencies.

**Section sources**
- [README.md](file://services/admin/README.md#L1-L703)
- [TESTING.md](file://services/admin/TESTING.md#L1-L50)
- [DEPLOYMENT.md](file://services/admin/DEPLOYMENT.md#L1-L50)

## Conclusion
The Admin Interface service provides a comprehensive management and monitoring solution for the Domain Ranking System. Built with modern web technologies and following best practices in security and usability, the interface enables administrators to effectively manage users, monitor system health, analyze performance metrics, and respond to alerts. The implementation of real-time updates through SSE and WebSockets ensures that administrators have access to current system information without manual refresh. The integration with Prometheus, Grafana, and Alertmanager provides a robust monitoring and alerting ecosystem. Security features including role-based access control, audit logging, and secure authentication protect the system from unauthorized access and provide accountability for administrative actions. The service supports flexible deployment options through Docker Compose configurations for both production and simple environments. With comprehensive testing strategies and detailed documentation, the Admin Interface is well-positioned to serve as the central control point for the domain analysis platform.