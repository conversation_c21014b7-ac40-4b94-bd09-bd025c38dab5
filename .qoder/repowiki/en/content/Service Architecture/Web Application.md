# Web Application

<cite>
**Referenced Files in This Document**   
- [WebAppService.ts](file://services/web-app/src/app/WebAppService.ts)
- [web.ts](file://services/web-app/src/app/routes/web.ts)
- [api/index.ts](file://services/web-app/src/app/routes/api/index.ts)
- [search.ts](file://services/web-app/src/app/routes/api/search.ts)
- [domains.ts](file://services/web-app/src/app/routes/api/domains.ts)
- [rankings.ts](file://services/web-app/src/app/routes/api/rankings.ts)
- [Dockerfile](file://services/web-app/Dockerfile)
- [nginx/conf.d/default.conf](file://nginx/conf.d/default.conf)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Project Structure](#project-structure)
3. [Core Components](#core-components)
4. [Architecture Overview](#architecture-overview)
5. [Detailed Component Analysis](#detailed-component-analysis)
6. [API Routing Structure](#api-routing-structure)
7. [Integration with Backend Services](#integration-with-backend-services)
8. [Deployment and Configuration](#deployment-and-configuration)
9. [Health Checking Mechanism](#health-checking-mechanism)
10. [Security Considerations](#security-considerations)
11. [Performance Optimization and Caching](#performance-optimization-and-caching)
12. [Error Handling in Frontend](#error-handling-in-frontend)
13. [Conclusion](#conclusion)

## Introduction
The Web Application service serves as the frontend and API gateway for the domainr platform, providing a user interface for domain search, analysis, and ranking functionalities. Built with React 18 and Next.js, it enables server-side rendering (SSR) for improved performance and SEO. The service interacts with backend components such as the Worker Service for job processing and the Domain Seeder for domain discovery. This document details the architecture, routing, integrations, deployment, and operational aspects of the Web Application service.

**Section sources**
- [WebAppService.ts](file://services/web-app/src/app/WebAppService.ts#L1-L242)

## Project Structure
The Web Application service is organized into several key directories: `src/app` contains the main application logic and service initialization; `src/components` holds React components for UI rendering; `src/routes` manages both web and API routes; `src/services` encapsulates business logic for domain operations; and `src/utils` includes utilities like the React renderer. The service uses a modular route setup with separate files for different API endpoints, promoting maintainability and separation of concerns.

```mermaid
graph TD
A[Web Application] --> B[src/app]
A --> C[src/components]
A --> D[src/routes]
A --> E[src/services]
A --> F[src/utils]
B --> G[WebAppService.ts]
D --> H[web.ts]
D --> I[api/]
I --> J[index.ts]
I --> K[search.ts]
I --> L[domains.ts]
I --> M[rankings.ts]
```

**Diagram sources**
- [WebAppService.ts](file://services/web-app/src/app/WebAppService.ts#L1-L242)
- [web.ts](file://services/web-app/src/app/routes/web.ts#L1-L308)
- [index.ts](file://services/web-app/src/app/routes/api/index.ts#L1-L33)

## Core Components
The core of the Web Application service is the `WebAppService` class, which initializes the Express-based server, sets up middleware, configures routes, and handles graceful shutdown. It integrates with shared database clients through `DatabaseManager` and instantiates key services: `DomainSearchService`, `DomainAnalysisService`, and `TopDomainsService`. These services are injected into route handlers to enable domain search, detailed analysis, and top domains retrieval. The service supports development mode fallback when database connections fail, ensuring usability during local development.

**Section sources**
- [WebAppService.ts](file://services/web-app/src/app/WebAppService.ts#L1-L242)

## Architecture Overview
The Web Application follows a layered architecture with presentation, routing, service, and data access layers. It acts as a reverse proxy and API gateway, forwarding requests to internal services while serving SSR-rendered React pages. The architecture integrates with Nginx as a reverse proxy and load balancer, Docker for containerization, and multiple backend services including Worker and Domain Seeder. Communication with backend systems occurs through shared libraries and direct API calls, enabling asynchronous job processing and real-time data updates.

```mermaid
graph LR
Client[User Client] --> Nginx[Nginx Reverse Proxy]
Nginx --> WebApp[Web Application]
WebApp --> Worker[Worker Service]
WebApp --> Seeder[Domain Seeder]
WebApp --> DB[(Shared Databases)]
WebApp --> Cache[(Redis Cache)]
WebApp --> SearchEngine[(Manticore Search)]
WebApp --> Analytics[Monitoring System]
```

**Diagram sources**
- [WebAppService.ts](file://services/web-app/src/app/WebAppService.ts#L1-L242)
- [nginx/conf.d/default.conf](file://nginx/conf.d/default.conf#L1-L50)

## Detailed Component Analysis

### WebAppService Analysis
The `WebAppService` class orchestrates the initialization and lifecycle management of the web server. It configures Express with middleware, defines health check endpoints, and dynamically imports route modules. Database connections are established via `DatabaseManager`, which provides access to Scylla, MariaDB, Redis, and Manticore. In case of database initialization failure, the service continues in development mode, allowing frontend functionality without backend data.

#### Class Diagram
```mermaid
classDiagram
class WebAppService {
-app : Application
-server : Server
-dbManager : DatabaseManager
-domainSearchService : DomainSearchService
-domainAnalysisService : DomainAnalysisService
-topDomainsService : TopDomainsService
+initialize() : Promise~void~
+start() : Promise~void~
+stop() : Promise~void~
+shutdown() : Promise~void~
+getApp() : Application
}
class DatabaseManager {
+initialize() : Promise~void~
+healthCheck() : Promise~Object~
+shutdown() : Promise~void~
}
class DomainSearchService {
+searchDomains(params) : Promise~SearchResults~
+getSearchSuggestions(query, limit) : Promise~Suggestions~
+getPopularSearches() : Promise~PopularSearches~
+getSearchStats() : Promise~SearchStats~
}
class DomainAnalysisService {
+getDomainAnalysis(domain) : Promise~DomainAnalysis~
+getDomainRankingExplanation(domain) : Promise~RankingExplanation~
+compareDomains(domains) : Promise~DomainComparison~
}
class TopDomainsService {
+getTopDomains(params) : Promise~TopDomains~
+getTrendingDomains(params) : Promise~TrendingDomains~
+getGlobalRankings(params) : Promise~Rankings~
+getCategoryRankings(category, params) : Promise~Rankings~
+getAvailableCategories() : Promise~Categories~
}
WebAppService --> DatabaseManager : "uses"
WebAppService --> DomainSearchService : "creates"
WebAppService --> DomainAnalysisService : "creates"
WebAppService --> TopDomainsService : "creates"
```

**Diagram sources**
- [WebAppService.ts](file://services/web-app/src/app/WebAppService.ts#L1-L242)
- [services/DomainSearchService.ts](file://services/web-app/src/services/DomainSearchService.ts#L1-L50)
- [services/DomainAnalysisService.ts](file://services/web-app/src/services/DomainAnalysisService.ts#L1-L50)
- [services/TopDomainsService.ts](file://services/web-app/src/services/TopDomainsService.ts#L1-L50)

## API Routing Structure
The Web Application implements a modular API routing system through the `setupApiRoutes` function, which delegates to specialized route setup functions for search, domains, rankings, and admin functionality. The API endpoints follow RESTful conventions and are prefixed with `/api`. Each route handler validates input, checks service availability, and returns structured JSON responses. Error handling is centralized, with detailed logging and appropriate HTTP status codes.

### API Endpoints Table
| Endpoint | Method | Description | Parameters |
|--------|--------|-------------|------------|
| `/api/domains/search` | GET | Search domains with filters | q, category, country, technology, ssl_grade, min_rank, max_rank, min_score, max_score, sort, page, limit |
| `/api/domains/search/suggestions` | GET | Get search suggestions | q, limit |
| `/api/domains/search/popular` | GET | Get popular searches | - |
| `/api/domains/search/stats` | GET | Get search statistics | - |
| `/api/domains/:domain/analysis` | GET | Get domain analysis | domain |
| `/api/domains/:domain/ranking` | GET | Get ranking explanation | domain |
| `/api/domains/top` | GET | Get top domains | category, country, limit, offset, timeframe |
| `/api/domains/compare` | POST | Compare multiple domains | domains[] |
| `/api/domains/trending` | GET | Get trending domains | timeframe, limit |
| `/api/rankings/global` | GET | Get global rankings | page, limit, sort |
| `/api/rankings/category/:category` | GET | Get category rankings | category, page, limit |
| `/api/categories` | GET | Get available categories | - |

**Section sources**
- [api/index.ts](file://services/web-app/src/app/routes/api/index.ts#L1-L33)
- [search.ts](file://services/web-app/src/app/routes/api/search.ts#L1-L160)
- [domains.ts](file://services/web-app/src/app/routes/api/domains.ts#L1-L198)
- [rankings.ts](file://services/web-app/src/app/routes/api/rankings.ts#L1-L106)

## Integration with Backend Services
The Web Application integrates with backend services through shared libraries and direct API calls. It uses the `@shared` module for common utilities like `DatabaseManager`, `JobQueue`, and `HttpClient`. For domain discovery, it communicates with the Domain Seeder service, which provides fresh domain data from various sources. The Worker Service processes intensive tasks such as domain crawling and analysis, with the Web Application submitting jobs and retrieving results asynchronously. This decoupled architecture enables scalability and fault tolerance.

```mermaid
sequenceDiagram
participant Client
participant WebApp
participant Worker
participant Seeder
participant Database
Client->>WebApp : GET /domain/example.com
WebApp->>Database : Query domain analysis
alt Analysis exists
Database-->>WebApp : Return analysis
WebApp-->>Client : Render page
else Analysis not found
WebApp->>Worker : Submit analysis job
Worker-->>WebApp : Job accepted
WebApp-->>Client : Show "processing" state
Worker->>Database : Store job status
Worker->>Seeder : Request domain data
Seeder-->>Worker : Return domain info
Worker->>Database : Update analysis
end
```

**Diagram sources**
- [WebAppService.ts](file://services/web-app/src/app/WebAppService.ts#L1-L242)
- [services/DomainAnalysisService.ts](file://services/web-app/src/services/DomainAnalysisService.ts#L1-L50)

## Deployment and Configuration
The Web Application is containerized using Docker, with a `Dockerfile` that specifies the Node.js runtime, installs dependencies, and exposes port 3000. It is configured through environment variables managed by the shared `config` module. The service is orchestrated via Docker Compose in development and Kubernetes in production. Nginx acts as a reverse proxy, handling SSL termination, static asset serving, and load balancing across multiple Web Application instances.

### Docker Configuration
```mermaid
graph TD
Docker[Docker Container] --> Node[Node.js 18]
Docker --> App[Web Application]
Docker --> Port[Expose Port 3000]
Docker --> Health[Health Check Script]
App --> Express[Express Server]
App --> React[React 18 + Next.js]
App --> SSR[Server-Side Rendering]
```

**Diagram sources**
- [Dockerfile](file://services/web-app/Dockerfile#L1-L20)
- [nginx/conf.d/default.conf](file://nginx/conf.d/default.conf#L1-L50)

## Health Checking Mechanism
The Web Application implements a comprehensive health check endpoint at `/health`, which reports the status of all connected databases (Scylla, MariaDB, Redis, Manticore). The health check attempts to verify connectivity to each database and includes timestamps and operational mode (development/production). This endpoint is used by Docker, Kubernetes, and monitoring systems to determine service availability and trigger auto-recovery procedures when necessary.

```mermaid
flowchart TD
Start([Health Check Request]) --> DBCheck["Check Database Connections"]
DBCheck --> Scylla{"Scylla OK?"}
Scylla --> |Yes| Maria{"MariaDB OK?"}
Scylla --> |No| SetWarning
Maria --> |Yes| Redis{"Redis OK?"}
Maria --> |No| SetWarning
Redis --> |Yes| Manticore{"Manticore OK?"}
Redis --> |No| SetWarning
Manticore --> |Yes| SetHealthy
Manticore --> |No| SetWarning
SetWarning --> Response["Return: status=ok, message=degraded"]
SetHealthy --> Response["Return: status=ok, message=healthy"]
Response --> End([Send Response])
```

**Diagram sources**
- [WebAppService.ts](file://services/web-app/src/app/WebAppService.ts#L100-L150)

## Security Considerations
Security is implemented through multiple layers: Nginx handles SSL/TLS termination and rate limiting; the Web Application uses Express middleware for input validation and error handling; and shared security modules provide consistent protection across services. Authentication and authorization are managed through centralized mechanisms, with sensitive operations requiring valid sessions or API keys. All database queries are parameterized to prevent injection attacks, and user input is sanitized before processing.

## Performance Optimization and Caching
The Web Application employs several performance optimization strategies: Server-Side Rendering (SSR) with React 18 improves initial load time and SEO; API responses are cached using Redis to reduce database load; and Manticore Search provides fast full-text search capabilities. The service implements pagination, filtering, and efficient query patterns to minimize response times. Static assets are served through Nginx with compression and caching headers.

## Error Handling in Frontend
Error handling in the frontend is centralized through the `errorHandling` module, which registers global error handlers for Express. All API routes wrap operations in try-catch blocks, with detailed logging via the shared `logger`. Client-facing errors include appropriate HTTP status codes and user-friendly messages, while sensitive error details are only logged server-side. The React components handle API errors gracefully, displaying fallback UI states when data is unavailable.

**Section sources**
- [WebAppService.ts](file://services/web-app/src/app/WebAppService.ts#L1-L242)
- [errorHandling.ts](file://services/web-app/src/app/errorHandling.ts#L1-L30)

## Conclusion
The Web Application service effectively serves as the frontend and API gateway for the domainr platform, providing a responsive user interface and robust API endpoints. Its modular architecture, integration with backend services, and comprehensive operational features make it a critical component of the system. The use of modern technologies like React 18, Next.js, and Docker ensures maintainability, scalability, and performance.