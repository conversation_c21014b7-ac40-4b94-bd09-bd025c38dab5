# Source Connectors

<cite>
**Referenced Files in This Document**   
- [SourceConnector.ts](file://services/domain-seeder/src/interfaces/SourceConnector.ts)
- [CZDSConnector.ts](file://services/domain-seeder/src/connectors/CZDSConnector.ts)
- [CommonCrawlConnector.ts](file://services/domain-seeder/src/connectors/CommonCrawlConnector.ts)
- [PIRConnector.ts](file://services/domain-seeder/src/connectors/PIRConnector.ts)
- [RadarConnector.ts](file://services/domain-seeder/src/connectors/RadarConnector.ts)
- [SonarConnector.ts](file://services/domain-seeder/src/connectors/SonarConnector.ts)
- [TrancoConnector.ts](file://services/domain-seeder/src/connectors/TrancoConnector.ts)
- [UmbrellaConnector.ts](file://services/domain-seeder/src/connectors/UmbrellaConnector.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Common Interface](#common-interface)
3. [Authentication Mechanisms](#authentication-mechanisms)
4. [Data Retrieval Patterns](#data-retrieval-patterns)
5. [Error Recovery Strategies](#error-recovery-strategies)
6. [Configuration Examples](#configuration-examples)
7. [Integration with Discovery Engine](#integration-with-discovery-engine)
8. [Reliability and Retry Logic](#reliability-and-retry-logic)
9. [API Deprecation Handling](#api-deprecation-handling)
10. [Troubleshooting Guide](#troubleshooting-guide)
11. [Performance Optimization](#performance-optimization)
12. [Connector Implementations](#connector-implementations)
    - [CZDSConnector](#czdsconnector)
    - [CommonCrawlConnector](#commoncrawlconnector)
    - [PIRConnector](#pirconnector)
    - [RadarConnector](#radarconnector)
    - [SonarConnector](#sonarconnector)
    - [TrancoConnector](#trancoconnector)
    - [UmbrellaConnector](#umbrellaconnector)

## Introduction
The domainr source connector framework provides a unified interface for retrieving domain data from various sources. Each connector implements a standardized interface while adapting to the specific requirements of its data source. The framework supports multiple discovery strategies, rate limiting, authentication, and error recovery to ensure reliable data collection.

## Common Interface
All connectors implement the `SourceConnectorType` interface which defines a consistent API for domain discovery operations.

```mermaid
classDiagram
class SourceConnectorType {
+string name
+number priority
+'daily' | 'weekly' | 'monthly' cadence
+fetchDomains(options : FetchOptionsType) : AsyncIterable~DomainCandidateType~
+getLastUpdate() : Promise~Date | null~
+healthCheck() : Promise~boolean~
+supportsStrategy(strategy : DiscoveryStrategyType) : boolean
}
class DomainCandidateType {
+string domain
+number rank
+string source
+Record~string, any~ metadata
}
class FetchOptionsType {
+number limit
+number offset
+Date since
+('top-10k' | '100k' | '1M' | 'long-tail')[] tiers
+DiscoveryStrategyType strategy
}
class SourceMetricsType {
+number candidatesFetched
+number fetchDuration
+number errors
+Date | null lastFetchTime
}
```

**Diagram sources**
- [SourceConnector.ts](file://services/domain-seeder/src/interfaces/SourceConnector.ts#L1-L45)

**Section sources**
- [SourceConnector.ts](file://services/domain-seeder/src/interfaces/SourceConnector.ts#L1-L45)

## Authentication Mechanisms
Connectors implement various authentication mechanisms depending on their target API requirements. These include API key authentication, username/password authentication, and token-based authentication with refresh capabilities.

```mermaid
sequenceDiagram
participant Connector
participant AuthProvider
participant API
Connector->>AuthProvider : authenticate(credentials)
AuthProvider-->>Connector : accessToken
Connector->>API : request with Authorization header
alt Token expired
API-->>Connector : 401 Unauthorized
Connector->>AuthProvider : refresh token
AuthProvider-->>Connector : new accessToken
Connector->>API : retry with new token
end
API-->>Connector : data
```

**Diagram sources**
- [CZDSConnector.ts](file://services/domain-seeder/src/connectors/CZDSConnector.ts#L150-L185)
- [PIRConnector.ts](file://services/domain-seeder/src/connectors/PIRConnector.ts#L200-L230)
- [UmbrellaConnector.ts](file://services/domain-seeder/src/connectors/UmbrellaConnector.ts#L350-L370)

## Data Retrieval Patterns
The framework uses async iterable patterns for efficient data retrieval, allowing for streaming of large datasets without excessive memory usage. Connectors implement pagination, streaming downloads, and incremental processing.

```mermaid
flowchart TD
Start([Start fetchDomains]) --> ValidateOptions["Validate fetch options"]
ValidateOptions --> CheckAuth["Ensure authentication"]
CheckAuth --> GetAvailableData["Get available data sources"]
GetAvailableData --> ProcessData["Process data in chunks"]
ProcessData --> HasMoreData{"More data available?"}
HasMoreData --> |Yes| ProcessData
HasMoreData --> |No| Complete([Complete])
Complete --> YieldData["Yield domain candidates"]
YieldData --> End([Return async iterable])
```

**Diagram sources**
- [CZDSConnector.ts](file://services/domain-seeder/src/connectors/CZDSConnector.ts#L200-L250)
- [CommonCrawlConnector.ts](file://services/domain-seeder/src/connectors/CommonCrawlConnector.ts#L250-L300)
- [SonarConnector.ts](file://services/domain-seeder/src/connectors/SonarConnector.ts#L250-L300)

## Error Recovery Strategies
Connectors implement comprehensive error recovery strategies including retry logic with exponential backoff, rate limit handling, and graceful degradation when specific data sources are unavailable.

```mermaid
flowchart TD
Start([Request]) --> AttemptRequest["Make HTTP request"]
AttemptRequest --> RequestSuccess{"Request successful?"}
RequestSuccess --> |Yes| ReturnResponse["Return response"]
RequestSuccess --> |No| CheckAttempts{"Max attempts reached?"}
CheckAttempts --> |No| CalculateDelay["Calculate delay: retryDelayMs * 2^(attempt-1)"]
CalculateDelay --> Wait["Wait for calculated delay"]
Wait --> AttemptRequest
CheckAttempts --> |Yes| ThrowError["Throw error after max retries"]
ReturnResponse --> End([Success])
ThrowError --> End
```

**Diagram sources**
- [CZDSConnector.ts](file://services/domain-seeder/src/connectors/CZDSConnector.ts#L500-L550)
- [CommonCrawlConnector.ts](file://services/domain-seeder/src/connectors/CommonCrawlConnector.ts#L500-L550)
- [UmbrellaConnector.ts](file://services/domain-seeder/src/connectors/UmbrellaConnector.ts#L600-L650)

## Configuration Examples
Connectors support configurable parameters for API keys, rate limits, and data filtering. Configuration can be provided via environment variables or constructor parameters.

```mermaid
erDiagram
CONNECTOR_CONFIG {
string connector_name PK
string api_key
number max_retries
number retry_delay_ms
number request_timeout_ms
number rate_limit_delay_ms
string temp_dir
number registration_cutoff_hours
number page_size
number long_tail_threshold
number max_rank
boolean monthly_rollup_enabled
number daily_request_limit
number monthly_request_limit
boolean tos_compliance_check
}
CONNECTOR_CONFIG ||--o{ CZDS_CONFIG : "extends"
CONNECTOR_CONFIG ||--o{ PIR_CONFIG : "extends"
CONNECTOR_CONFIG ||--o{ RADAR_CONFIG : "extends"
CONNECTOR_CONFIG ||--o{ UMBRELLA_CONFIG : "extends"
CONNECTOR_CONFIG ||--o{ COMMON_CRAWL_CONFIG : "extends"
CONNECTOR_CONFIG ||--o{ SONAR_CONFIG : "extends"
```

**Diagram sources**
- [CZDSConnector.ts](file://services/domain-seeder/src/connectors/CZDSConnector.ts#L50-L100)
- [PIRConnector.ts](file://services/domain-seeder/src/connectors/PIRConnector.ts#L50-L100)
- [RadarConnector.ts](file://services/domain-seeder/src/connectors/RadarConnector.ts#L50-L100)
- [UmbrellaConnector.ts](file://services/domain-seeder/src/connectors/UmbrellaConnector.ts#L50-L100)

## Integration with Discovery Engine
Connectors integrate with the discovery engine through a standardized interface that allows the engine to orchestrate data collection from multiple sources based on discovery strategies and priorities.

```mermaid
graph TB
subgraph "Discovery Engine"
DE[DiscoveryEngine]
DP[DiscoveryPipeline]
RM[ReliabilityManager]
end
subgraph "Connectors"
CZDS[CZDSConnector]
PIR[PIRConnector]
TR[TrancoConnector]
UM[UmbrellaConnector]
RD[RadarConnector]
CC[CommonCrawlConnector]
SN[SonarConnector]
end
subgraph "Data Processing"
DN[DomainNormalizer]
VP[ValidationPipeline]
RE[Repository]
end
DE --> CZDS
DE --> PIR
DE --> TR
DE --> UM
DE --> RD
DE --> CC
DE --> SN
CZDS --> DP
PIR --> DP
TR --> DP
UM --> DP
RD --> DP
CC --> DP
SN --> DP
DP --> RM
RM --> DN
DN --> VP
VP --> RE
```

**Diagram sources**
- [SourceConnector.ts](file://services/domain-seeder/src/interfaces/SourceConnector.ts#L1-L45)
- [DiscoveryEngine.ts](file://services/domain-seeder/src/interfaces/DiscoveryEngine.ts#L1-L20)

## Reliability and Retry Logic
The framework implements robust reliability features including retry logic with exponential backoff, circuit breaking, and health monitoring to ensure stable operation even when external APIs experience temporary issues.

```mermaid
sequenceDiagram
participant Connector
participant RetryManager
participant API
loop For each attempt
Connector->>API : Make request
API-->>Connector : Error response
Connector->>RetryManager : Report failure
RetryManager->>RetryManager : Calculate delay
alt Max attempts not reached
RetryManager-->>Connector : Wait instruction
Connector->>Connector : Wait for calculated delay
else Max attempts reached
RetryManager-->>Connector : Retry failed
Connector->>Client : Throw final error
end
end
```

**Diagram sources**
- [CZDSConnector.ts](file://services/domain-seeder/src/connectors/CZDSConnector.ts#L500-L550)
- [CommonCrawlConnector.ts](file://services/domain-seeder/src/connectors/CommonCrawlConnector.ts#L500-L550)
- [SonarConnector.ts](file://services/domain-seeder/src/connectors/SonarConnector.ts#L600-L650)

## API Deprecation Handling
Connectors are designed to handle API deprecations gracefully by implementing versioned endpoints, fallback mechanisms, and clear error messaging to facilitate smooth transitions when APIs change.

```mermaid
flowchart TD
Start([API Request]) --> CheckVersion["Check API version"]
CheckVersion --> IsDeprecated{"Endpoint deprecated?"}
IsDeprecated --> |Yes| UseFallback["Use fallback endpoint"]
UseFallback --> FallbackSuccess{"Fallback successful?"}
FallbackSuccess --> |Yes| ReturnData["Return data from fallback"]
FallbackSuccess --> |No| LogDeprecation["Log deprecation warning"]
IsDeprecated --> |No| UsePrimary["Use primary endpoint"]
UsePrimary --> PrimarySuccess{"Primary successful?"}
PrimarySuccess --> |Yes| ReturnData
PrimarySuccess --> |No| UseFallback
LogDeprecation --> ReturnData
ReturnData --> End([Complete])
```

**Diagram sources**
- [CZDSConnector.ts](file://services/domain-seeder/src/connectors/CZDSConnector.ts#L500-L550)
- [PIRConnector.ts](file://services/domain-seeder/src/connectors/PIRConnector.ts#L500-L550)
- [UmbrellaConnector.ts](file://services/domain-seeder/src/connectors/UmbrellaConnector.ts#L600-L650)

## Troubleshooting Guide
Common connectivity issues and their solutions are documented to assist with debugging and maintaining connector reliability.

```mermaid
flowchart TD
Issue([Connectivity Issue]) --> IdentifySource["Identify source of issue"]
IdentifySource --> IsAuthProblem{"Authentication failure?"}
IsAuthProblem --> |Yes| CheckCredentials["Verify API keys/credentials"]
CheckCredentials --> UpdateCredentials["Update environment variables"]
UpdateCredentials --> TestConnection["Test connection"]
IdentifySource --> IsRateLimit{"Rate limited?"}
IsRateLimit --> |Yes| CheckLimits["Check rate limit headers"]
CheckLimits --> AdjustRate["Increase rateLimitDelayMs"]
AdjustRate --> WaitReset["Wait for reset or reduce frequency"]
IdentifySource --> IsTimeout{"Request timeout?"}
IsTimeout --> |Yes| IncreaseTimeout["Increase requestTimeoutMs"]
IncreaseTimeout --> OptimizeRequest["Optimize request size"]
IdentifySource --> IsDataError{"Data parsing error?"}
IsDataError --> |Yes| CheckFormat["Verify data format"]
CheckFormat --> UpdateParser["Update parsing logic"]
TestConnection --> Success{"Connection successful?"}
Success --> |Yes| Resolved([Issue resolved])
Success --> |No| Escalate([Escalate to engineering])
```

**Diagram sources**
- [CZDSConnector.ts](file://services/domain-seeder/src/connectors/CZDSConnector.ts#L200-L250)
- [CommonCrawlConnector.ts](file://services/domain-seeder/src/connectors/CommonCrawlConnector.ts#L250-L300)
- [UmbrellaConnector.ts](file://services/domain-seeder/src/connectors/UmbrellaConnector.ts#L350-L400)

## Performance Optimization
High-throughput scenarios require careful optimization of connector performance through batching, parallelization, and efficient data processing.

```mermaid
flowchart TD
Start([Optimization]) --> BatchRequests["Implement request batching"]
BatchRequests --> ParallelProcessing["Enable parallel processing"]
ParallelProcessing --> StreamData["Use streaming for large datasets"]
StreamData --> CacheResults["Implement result caching"]
CacheResults --> OptimizeParsing["Optimize data parsing"]
OptimizeParsing --> MonitorPerformance["Monitor performance metrics"]
MonitorPerformance --> AdjustConfiguration["Adjust configuration based on metrics"]
AdjustConfiguration --> End([Optimized performance])
```

**Diagram sources**
- [CZDSConnector.ts](file://services/domain-seeder/src/connectors/CZDSConnector.ts#L300-L350)
- [CommonCrawlConnector.ts](file://services/domain-seeder/src/connectors/CommonCrawlConnector.ts#L300-L350)
- [SonarConnector.ts](file://services/domain-seeder/src/connectors/SonarConnector.ts#L300-L350)

## Connector Implementations

### CZDSConnector
The CZDSConnector provides access to ICANN zone files for comprehensive domain coverage. It handles large compressed zone files through streaming downloads and incremental parsing.

**Section sources**
- [CZDSConnector.ts](file://services/domain-seeder/src/connectors/CZDSConnector.ts#L1-L714)

### CommonCrawlConnector
The CommonCrawlConnector retrieves domain data from web archives, focusing on long-tail domains with low frequency but potential value. It processes host frequency data from Common Crawl's index.

**Section sources**
- [CommonCrawlConnector.ts](file://services/domain-seeder/src/connectors/CommonCrawlConnector.ts#L1-L630)

### PIRConnector
The PIRConnector accesses .org domain data through the Public Interest Registry API, specializing in newly registered domains and zone file information for the .org TLD.

**Section sources**
- [PIRConnector.ts](file://services/domain-seeder/src/connectors/PIRConnector.ts#L1-L645)

### RadarConnector
The RadarConnector leverages Cloudflare Radar data for domain ranking and popularity analysis, supporting differential and temporal analysis strategies to identify trending domains.

**Section sources**
- [RadarConnector.ts](file://services/domain-seeder/src/connectors/RadarConnector.ts#L1-L532)

### SonarConnector
The SonarConnector retrieves domain data from Rapid7's Sonar project, focusing on DNS resolution data to discover domains in the long tail of internet usage.

**Section sources**
- [SonarConnector.ts](file://services/domain-seeder/src/connectors/SonarConnector.ts#L1-L707)

### TrancoConnector
The TrancoConnector provides access to the Tranco ranking list, offering a reliable source of top domain rankings with support for differential analysis and long-tail exploration.

**Section sources**
- [TrancoConnector.ts](file://services/domain-seeder/src/connectors/TrancoConnector.ts#L1-L420)

### UmbrellaConnector
The UmbrellaConnector integrates with Cisco Umbrella's domain ranking API, providing comprehensive domain data with strict ToS compliance and usage limit management.

**Section sources**
- [UmbrellaConnector.ts](file://services/domain-seeder/src/connectors/UmbrellaConnector.ts#L1-L638)