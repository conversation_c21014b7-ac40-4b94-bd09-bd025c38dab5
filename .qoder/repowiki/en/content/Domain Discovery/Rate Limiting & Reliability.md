# Rate Limiting & Reliability

<cite>
**Referenced Files in This Document**   
- [TokenBucket.ts](file://services/domain-seeder/src/ratelimiting/TokenBucket.ts)
- [BackpressureController.ts](file://services/domain-seeder/src/ratelimiting/BackpressureController.ts)
- [RateLimitedDomainEnqueuer.ts](file://services/domain-seeder/src/enqueuer/RateLimitedDomainEnqueuer.ts)
- [CrashSafeReliabilityManager.ts](file://services/domain-seeder/src/reliability/CrashSafeReliabilityManager.ts)
- [ReliableStreamProcessor.ts](file://services/domain-seeder/src/reliability/ReliableStreamProcessor.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [TokenBucket Implementation](#tokenbucket-implementation)
3. [BackpressureController for System-wide Load Regulation](#backpressurecontroller-for-system-wide-load-regulation)
4. [Integration with Source Connectors and Enqueuer Pipeline](#integration-with-source-connectors-and-enqueuer-pipeline)
5. [CrashSafeReliabilityManager for Data Integrity](#crashsafeprobabilitymanager-for-data-integrity)
6. [ReliableStreamProcessor for Exactly-once Processing](#reliablestreamprocessor-for-exactly-once-processing)
7. [Configuration Parameters](#configuration-parameters)
8. [Preventing Service Abuse and Maintaining Stability](#preventing-service-abuse-and-maintaining-stability)
9. [Common Issues and Solutions](#common-issues-and-solutions)
10. [Monitoring Metrics and Alerting Thresholds](#monitoring-metrics-and-alerting-thresholds)
11. [Conclusion](#conclusion)

## Introduction
The domainr rate limiting and reliability system ensures stable and efficient processing of domain discovery operations while maintaining data integrity during failures. This document details the TokenBucket implementation for quota management across external APIs, the BackpressureController for system-wide load regulation, and their integration with source connectors and the enqueuer pipeline. It also covers the CrashSafeReliabilityManager's role in ensuring data integrity during failures and the ReliableStreamProcessor's exactly-once processing guarantees. Configuration parameters for burst limits, refill rates, and checkpoint intervals are described, along with examples of how these systems prevent service abuse and maintain stability under variable load.

**Section sources**
- [TokenBucket.ts](file://services/domain-seeder/src/ratelimiting/TokenBucket.ts#L1-L294)
- [BackpressureController.ts](file://services/domain-seeder/src/ratelimiting/BackpressureController.ts#L1-L503)

## TokenBucket Implementation
The TokenBucket class implements a token bucket algorithm for rate limiting operations across external APIs. It uses Redis to store bucket state and Lua scripts for atomic operations. The bucket is configured with a capacity, refill rate, and refill interval. Tokens are consumed when operations are performed, and the bucket is refilled at regular intervals. The implementation includes jitter to prevent thundering herd problems and supports waiting for tokens when the bucket is empty.

```mermaid
classDiagram
class TokenBucket {
+RedisClientWrapper redis
+string bucketKey
+TokenBucketConfigType config
+start() : Promise~void~
+stop() : Promise~void~
+consume(tokens : number) : Promise~boolean~
+waitForTokens(tokens : number, maxWaitMs : number) : Promise~boolean~
+getState() : Promise~TokenBucketStateType~
+reset() : Promise~void~
}
class TokenBucketConfigType {
+number capacity
+number refillRate
+number refillInterval
+number jitterRange
+string keyPrefix
}
class TokenBucketStateType {
+number tokens
+number lastRefill
}
TokenBucket --> TokenBucketConfigType : "has"
TokenBucket --> TokenBucketStateType : "returns"
```

**Diagram sources**
- [TokenBucket.ts](file://services/domain-seeder/src/ratelimiting/TokenBucket.ts#L1-L294)

**Section sources**
- [TokenBucket.ts](file://services/domain-seeder/src/ratelimiting/TokenBucket.ts#L1-L294)

## BackpressureController for System-wide Load Regulation
The ComprehensiveBackpressureController monitors system metrics such as queue depth, database latency, memory usage, and error rate to determine system pressure. It uses adaptive rate limiting to adjust the rate of operations based on system load. The controller includes circuit breakers for individual services to prevent cascading failures. It also implements exponential backoff for retrying failed operations.

```mermaid
classDiagram
class ComprehensiveBackpressureController {
+RedisClientWrapper redis
+DatabaseManager dbManager
+BackpressureThresholdsType thresholds
+AdaptiveRateLimitConfigType adaptiveConfig
+start() : Promise~void~
+stop() : Promise~void~
+shouldThrottle() : Promise~boolean~
+getQueueDepth() : Promise~number~
+waitForCapacity() : Promise~void~
+updateMetrics(queueDepth : number, latency : number) : void
+getAdaptiveRateLimit() : Promise~number~
+recordServiceFailure(serviceName : string) : Promise~void~
+recordServiceSuccess(serviceName : string) : Promise~void~
+isServiceAvailable(serviceName : string) : Promise~boolean~
}
class BackpressureMetricsType {
+number queueDepth
+number dbLatency
+number memoryUsage
+number errorRate
+number timestamp
+number responseTime
}
class BackpressureThresholdsType {
+number queueDepthWarning
+number queueDepthCritical
+number dbLatencyWarning
+number dbLatencyCritical
+number memoryUsageWarning
+number memoryUsageCritical
+number errorRateWarning
+number errorRateCritical
}
class AdaptiveRateLimitConfigType {
+number baseRate
+number minRate
+number maxRate
+number adjustmentFactor
+number recoveryFactor
}
ComprehensiveBackpressureController --> BackpressureMetricsType : "collects"
ComprehensiveBackpressureController --> BackpressureThresholdsType : "uses"
ComprehensiveBackpressureController --> AdaptiveRateLimitConfigType : "uses"
```

**Diagram sources**
- [BackpressureController.ts](file://services/domain-seeder/src/ratelimiting/BackpressureController.ts#L1-L503)

**Section sources**
- [BackpressureController.ts](file://services/domain-seeder/src/ratelimiting/BackpressureController.ts#L1-L503)

## Integration with Source Connectors and Enqueuer Pipeline
The RateLimitedDomainEnqueuer integrates the TokenBucket and BackpressureController with source connectors and the enqueuer pipeline. It uses the TokenBucket to limit the rate of domain enqueuing and the BackpressureController to regulate system load. The enqueuer also implements idempotency checks to prevent duplicate domains from being enqueued. It supports tiered queuing with high and normal priority streams.

```mermaid
sequenceDiagram
participant SourceConnector
participant RateLimitedDomainEnqueuer
participant TokenBucket
participant BackpressureController
participant RedisStream
SourceConnector->>RateLimitedDomainEnqueuer : Discovered domains
RateLimitedDomainEnqueuer->>RateLimitedDomainEnqueuer : Check daily limit
RateLimitedDomainEnqueuer->>BackpressureController : isQueueDepthExceeded()
BackpressureController-->>RateLimitedDomainEnqueuer : Queue depth status
alt Queue depth exceeded
RateLimitedDomainEnqueuer->>BackpressureController : waitForCapacity()
end
RateLimitedDomainEnqueuer->>TokenBucket : waitForTokens()
TokenBucket-->>RateLimitedDomainEnqueuer : Tokens available
RateLimitedDomainEnqueuer->>RateLimitedDomainEnqueuer : Process batch
RateLimitedDomainEnqueuer->>RedisStream : Enqueue domains
```

**Diagram sources**
- [RateLimitedDomainEnqueuer.ts](file://services/domain-seeder/src/enqueuer/RateLimitedDomainEnqueuer.ts#L1-L1538)

**Section sources**
- [RateLimitedDomainEnqueuer.ts](file://services/domain-seeder/src/enqueuer/RateLimitedDomainEnqueuer.ts#L1-L1538)

## CrashSafeReliabilityManager for Data Integrity
The CrashSafeReliabilityManager ensures data integrity during failures by creating checkpoints at various stages of processing. It uses Redis to store checkpoints with a TTL. The manager also implements idempotency checks to prevent duplicate operations. In case of a crash, processing can be resumed from the last checkpoint. The manager includes error reporting and recovery mechanisms.

```mermaid
classDiagram
class CrashSafeReliabilityManager {
+Logger logger
+RedisClientWrapper redis
+ReliabilityConfig config
+createCheckpoint(stage : string, data : unknown) : Promise~string~
+resumeFromCheckpoint(checkpointId : string) : Promise~unknown~
+validatePipelineStage(stage : string, data : unknown) : Promise~ValidationResultType~
+ensureIdempotency(operation : string, key : string) : Promise~boolean~
+executeWithReliability~T~(operation : () => Promise~T~, context : ErrorContext, options? : { maxRetries? : number; enableCheckpoints? : boolean; enableRecovery? : boolean; }) : Promise~T~
+getHealthStatus() : Promise~HealthStatus~
+getErrorReports(filters? : any) : Promise~any[]~
+cleanup() : Promise~void~
}
class ReliabilityConfig {
+number checkpointTtlMs
+number maxCheckpoints
+boolean enableErrorReporting
+boolean enableRecovery
+string correlationIdHeader
}
class OperationContext {
+string id
+ErrorContext context
+Date startTime
+Date endTime
+boolean success
+Error error
+string[] checkpoints
}
class HealthStatus {
+boolean healthy
+any errorStats
+number activeOperations
+boolean checkpointStoreHealthy
+number uptime
+NodeJS.MemoryUsage memoryUsage
}
CrashSafeReliabilityManager --> ReliabilityConfig : "uses"
CrashSafeReliabilityManager --> OperationContext : "manages"
CrashSafeReliabilityManager --> HealthStatus : "returns"
```

**Diagram sources**
- [CrashSafeReliabilityManager.ts](file://services/domain-seeder/src/reliability/CrashSafeReliabilityManager.ts#L1-L732)

**Section sources**
- [CrashSafeReliabilityManager.ts](file://services/domain-seeder/src/reliability/CrashSafeReliabilityManager.ts#L1-L732)

## ReliableStreamProcessor for Exactly-once Processing
The ReliableStreamProcessor ensures exactly-once processing of messages from Redis Streams. It uses consumer groups and ACKs to track message processing. Failed messages are moved to a dead letter queue after a configurable number of retries. The processor also implements exponential backoff for retrying failed messages. It can recover pending messages that were not processed due to consumer failures.

```mermaid
sequenceDiagram
participant ReliableStreamProcessor
participant RedisStream
participant MessageHandler
participant DeadLetterQueue
ReliableStreamProcessor->>RedisStream : xReadGroup()
RedisStream-->>ReliableStreamProcessor : Messages
loop For each message
ReliableStreamProcessor->>MessageHandler : Process message
alt Success
MessageHandler-->>ReliableStreamProcessor : Success
ReliableStreamProcessor->>RedisStream : xAck()
else Failure
ReliableStreamProcessor->>ReliableStreamProcessor : Increment retry count
alt Retry count < maxRetries
ReliableStreamProcessor->>ReliableStreamProcessor : Schedule retry with exponential backoff
else
ReliableStreamProcessor->>DeadLetterQueue : Move to DLQ
end
end
end
```

**Diagram sources**
- [ReliableStreamProcessor.ts](file://services/domain-seeder/src/reliability/ReliableStreamProcessor.ts#L1-L626)

**Section sources**
- [ReliableStreamProcessor.ts](file://services/domain-seeder/src/reliability/ReliableStreamProcessor.ts#L1-L626)

## Configuration Parameters
The system includes various configuration parameters for tuning behavior:

- **TokenBucket**: capacity, refillRate, refillInterval, jitterRange, keyPrefix
- **BackpressureController**: queueDepthWarning, queueDepthCritical, dbLatencyWarning, dbLatencyCritical, memoryUsageWarning, memoryUsageCritical, errorRateWarning, errorRateCritical, baseRate, minRate, maxRate, adjustmentFactor, recoveryFactor
- **RateLimitedDomainEnqueuer**: enqueueBatchSize, enqueueIntervalMs, maxNewPerDay, newQueueMaxDepth, normalStreamName, highPriorityStreamName, idempotencyTtlSeconds
- **CrashSafeReliabilityManager**: checkpointTtlMs, maxCheckpoints, enableErrorReporting, enableRecovery, correlationIdHeader
- **ReliableStreamProcessor**: maxRetries, retryDelayMs, processingTimeoutMs, batchSize, blockTimeMs

**Section sources**
- [TokenBucket.ts](file://services/domain-seeder/src/ratelimiting/TokenBucket.ts#L1-L294)
- [BackpressureController.ts](file://services/domain-seeder/src/ratelimiting/BackpressureController.ts#L1-L503)
- [RateLimitedDomainEnqueuer.ts](file://services/domain-seeder/src/enqueuer/RateLimitedDomainEnqueuer.ts#L1-L1538)
- [CrashSafeReliabilityManager.ts](file://services/domain-seeder/src/reliability/CrashSafeReliabilityManager.ts#L1-L732)
- [ReliableStreamProcessor.ts](file://services/domain-seeder/src/reliability/ReliableStreamProcessor.ts#L1-L626)

## Preventing Service Abuse and Maintaining Stability
The TokenBucket and BackpressureController work together to prevent service abuse and maintain stability under variable load. The TokenBucket limits the rate of operations to prevent overwhelming external APIs. The BackpressureController monitors system metrics and adjusts the rate of operations to prevent overloading the system. The RateLimitedDomainEnqueuer integrates these components to ensure stable processing of domain discovery operations.

**Section sources**
- [TokenBucket.ts](file://services/domain-seeder/src/ratelimiting/TokenBucket.ts#L1-L294)
- [BackpressureController.ts](file://services/domain-seeder/src/ratelimiting/BackpressureController.ts#L1-L503)
- [RateLimitedDomainEnqueuer.ts](file://services/domain-seeder/src/enqueuer/RateLimitedDomainEnqueuer.ts#L1-L1538)

## Common Issues and Solutions
Common issues include clock drift in token buckets, recovery from broker failures, and tuning reliability settings for different deployment scales. Clock drift can be mitigated by using Redis for time synchronization. Broker failures can be recovered by using the ReliableStreamProcessor's pending message recovery feature. Reliability settings should be tuned based on the deployment scale and workload characteristics.

**Section sources**
- [TokenBucket.ts](file://services/domain-seeder/src/ratelimiting/TokenBucket.ts#L1-L294)
- [BackpressureController.ts](file://services/domain-seeder/src/ratelimiting/BackpressureController.ts#L1-L503)
- [ReliableStreamProcessor.ts](file://services/domain-seeder/src/reliability/ReliableStreamProcessor.ts#L1-L626)

## Monitoring Metrics and Alerting Thresholds
The system includes various monitoring metrics and alerting thresholds:

- **TokenBucket**: tokens consumed, tokens refilled, bucket capacity
- **BackpressureController**: queue depth, database latency, memory usage, error rate, adaptive rate limit
- **RateLimitedDomainEnqueuer**: enqueued total, rate limited total, duplicates skipped, content generation failures, backpressure waits
- **CrashSafeReliabilityManager**: active operations, checkpoint store health, error stats
- **ReliableStreamProcessor**: pending messages, dead letter queue size, processing latency

Alerting thresholds should be set based on system capacity and workload characteristics.

**Section sources**
- [TokenBucket.ts](file://services/domain-seeder/src/ratelimiting/TokenBucket.ts#L1-L294)
- [BackpressureController.ts](file://services/domain-seeder/src/ratelimiting/BackpressureController.ts#L1-L503)
- [RateLimitedDomainEnqueuer.ts](file://services/domain-seeder/src/enqueuer/RateLimitedDomainEnqueuer.ts#L1-L1538)
- [CrashSafeReliabilityManager.ts](file://services/domain-seeder/src/reliability/CrashSafeReliabilityManager.ts#L1-L732)
- [ReliableStreamProcessor.ts](file://services/domain-seeder/src/reliability/ReliableStreamProcessor.ts#L1-L626)

## Conclusion
The domainr rate limiting and reliability system provides a robust foundation for stable and efficient processing of domain discovery operations. The TokenBucket and BackpressureController work together to prevent service abuse and maintain stability under variable load. The CrashSafeReliabilityManager and ReliableStreamProcessor ensure data integrity and exactly-once processing. The system is highly configurable and can be tuned for different deployment scales and workload characteristics.