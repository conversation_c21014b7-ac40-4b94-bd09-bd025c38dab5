# Domain Discovery

<cite>
**Referenced Files in This Document**   
- [CZDSConnector.ts](file://services/domain-seeder/src/connectors/CZDSConnector.ts)
- [CommonCrawlConnector.ts](file://services/domain-seeder/src/connectors/CommonCrawlConnector.ts)
- [PIRConnector.ts](file://services/domain-seeder/src/connectors/PIRConnector.ts)
- [RadarConnector.ts](file://services/domain-seeder/src/connectors/RadarConnector.ts)
- [SonarConnector.ts](file://services/domain-seeder/src/connectors/SonarConnector.ts)
- [TrancoConnector.ts](file://services/domain-seeder/src/connectors/TrancoConnector.ts)
- [UmbrellaConnector.ts](file://services/domain-seeder/src/connectors/UmbrellaConnector.ts)
- [SourceConnector.ts](file://services/domain-seeder/src/interfaces/SourceConnector.ts)
- [DiscoveryEngine.ts](file://services/domain-seeder/src/interfaces/DiscoveryEngine.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Core Architecture](#core-architecture)
3. [Domain Discovery Strategies](#domain-discovery-strategies)
4. [Source Connectors](#source-connectors)
5. [Configuration and Parameters](#configuration-and-parameters)
6. [Rate Limiting and Reliability](#rate-limiting-and-reliability)
7. [Common Issues and Solutions](#common-issues-and-solutions)
8. [Integration with Other Components](#integration-with-other-components)
9. [Usage Patterns](#usage-patterns)

## Introduction
The Domain Discovery system is a comprehensive framework for identifying and analyzing domain names from various data sources. It employs multiple discovery strategies and source connectors to gather domain candidates, process them through different analytical approaches, and maintain reliable data collection. The system is designed to be extensible, allowing new data sources and discovery methods to be integrated while maintaining consistent interfaces and reliability mechanisms.

## Core Architecture
The Domain Discovery system follows a modular architecture with clear separation between data sources, discovery strategies, and processing components. The core interfaces define the contract between these components, ensuring consistent behavior across different implementations.

```mermaid
classDiagram
class SourceConnector {
+string name
+number priority
+string cadence
+fetchDomains(options) AsyncIterable~DomainCandidateType~
+getLastUpdate() Promise~Date | null~
+healthCheck() Promise~boolean~
+supportsStrategy(strategy) boolean
}
class DiscoveryEngine {
+processWithStrategy(strategy, candidates) Promise~DiscoveredDomainInterface[]~
+getHistoricalSnapshot(source, date) Promise~DomainSnapshotType | null~
+storeSnapshot(source, domains) Promise~void~
}
class StrategyProcessor {
+process(candidates, snapshotStore) Promise~DiscoveredDomainInterface[]~
}
SourceConnector --> DiscoveryEngine : "provides"
DiscoveryEngine --> StrategyProcessor : "uses"
```

**Diagram sources**
- [SourceConnector.ts](file://services/domain-seeder/src/interfaces/SourceConnector.ts)
- [DiscoveryEngine.ts](file://services/domain-seeder/src/interfaces/DiscoveryEngine.ts)

**Section sources**
- [SourceConnector.ts](file://services/domain-seeder/src/interfaces/SourceConnector.ts#L1-L45)
- [DiscoveryEngine.ts](file://services/domain-seeder/src/interfaces/DiscoveryEngine.ts#L1-L72)

## Domain Discovery Strategies
The system implements several discovery strategies to identify domains based on different criteria and data patterns. Each strategy is designed to work with specific types of data sources and provides unique insights into domain characteristics.

### Differential Analysis
This strategy identifies domains that show significant changes in their metrics over time, such as traffic patterns or ranking positions. It's particularly effective for identifying emerging domains that are gaining popularity.

### Zone File Analysis
Focused on newly registered domains, this strategy processes zone files from domain registries to identify domains that have recently been created. It's essential for discovering fresh domains before they become widely known.

### Long-Tail Exploration
This strategy targets domains that are not part of the popular top-ranked lists but may have niche value. It analyzes large datasets of DNS resolution records or web crawl data to discover obscure but potentially valuable domains.

### Temporal Analysis
By examining historical patterns in domain data, this strategy identifies domains with specific temporal characteristics, such as seasonal popularity or consistent growth trends over time.

```mermaid
graph TD
A[Data Sources] --> B{Strategy Selection}
B --> C[Differential Analysis]
B --> D[Zone File Analysis]
B --> E[Long-Tail Exploration]
B --> F[Temporal Analysis]
C --> G[Emerging Domains]
D --> H[Newly Registered]
E --> I[Niche Domains]
F --> J[Trending Domains]
```

**Diagram sources**
- [DiscoveryEngine.ts](file://services/domain-seeder/src/interfaces/DiscoveryEngine.ts#L1-L72)

**Section sources**
- [DiscoveryEngine.ts](file://services/domain-seeder/src/interfaces/DiscoveryEngine.ts#L1-L72)

## Source Connectors
Source connectors are the interface between the Domain Discovery system and external data sources. Each connector implements the SourceConnector interface and is responsible for fetching domain data from its specific source.

### CZDS Connector
The CZDS (Centralized Zone Data Service) connector retrieves zone files from ICANN for various top-level domains. It's optimized for discovering newly registered domains through zone file analysis.

**Configuration Options:**
- `baseUrl`: API endpoint for CZDS service
- `username`: CZDS account username
- `password`: CZDS account password
- `rateLimitDelayMs`: Delay between requests to respect rate limits
- `registrationCutoffHours`: Only include domains registered within this timeframe

**Section sources**
- [CZDSConnector.ts](file://services/domain-seeder/src/connectors/CZDSConnector.ts#L1-L713)

### Common Crawl Connector
This connector accesses the Common Crawl dataset to discover domains from web crawl data. It's particularly effective for long-tail exploration by analyzing host frequency data.

**Configuration Options:**
- `indexUrl`: Base URL for Common Crawl index
- `longTailThreshold`: Starting rank for long-tail analysis
- `maxRank`: Maximum rank to process
- `monthlyRollupEnabled`: Whether to process comprehensive monthly data

**Section sources**
- [CommonCrawlConnector.ts](file://services/domain-seeder/src/connectors/CommonCrawlConnector.ts#L1-L629)

### PIR Connector
The Public Interest Registry (PIR) connector specifically targets .org domains, fetching registration data and zone files from the PIR registry. It's optimized for discovering newly registered .org domains.

**Configuration Options:**
- `apiKey`: PIR API authentication key
- `registrationCutoffHours`: Time window for new registrations
- `pageSize`: Number of records to fetch per request

**Section sources**
- [PIRConnector.ts](file://services/domain-seeder/src/connectors/PIRConnector.ts#L1-L644)

### Radar Connector
This connector interfaces with Cloudflare's Radar service to access domain ranking and popularity data. It supports differential and temporal analysis strategies to identify domains with changing metrics.

**Configuration Options:**
- `apiKey`: Cloudflare API key
- `rateLimitDelayMs`: Delay to respect Cloudflare's rate limits
- `pageSize`: Number of domains to fetch per page

**Section sources**
- [RadarConnector.ts](file://services/domain-seeder/src/connectors/RadarConnector.ts#L1-L531)

### Sonar Connector
The Sonar connector accesses Rapid7's Forward DNS (FDNS) dataset to discover domains from DNS resolution records. It's particularly effective for long-tail exploration by analyzing DNS query patterns.

**Configuration Options:**
- `apiKey`: Rapid7 API key
- `longTailThreshold`: Starting point for long-tail analysis
- `backfillDays`: Number of days to include in historical analysis

**Section sources**
- [SonarConnector.ts](file://services/domain-seeder/src/connectors/SonarConnector.ts#L1-L706)

### Tranco and Umbrella Connectors
These connectors interface with the Tranco and Cisco Umbrella ranking services to access comprehensive domain ranking data. They provide reliable, frequently updated lists of popular domains and support various discovery strategies.

## Configuration and Parameters
The Domain Discovery system uses a consistent configuration pattern across all connectors, with environment variables for sensitive data and configurable parameters for operational settings.

### Common Configuration Parameters
- **Authentication**: API keys, usernames, and passwords are typically provided through environment variables
- **Rate Limiting**: Configurable delays between requests to respect API rate limits
- **Retries**: Number of retry attempts and delay between retries for failed requests
- **Timeouts**: Request timeout settings to prevent hanging operations
- **Data Filtering**: Parameters to filter results by date, rank, or other criteria

### Strategy-Specific Parameters
Each discovery strategy may have specific parameters that control its behavior:
- **Zone File Analysis**: Registration cutoff periods, zone file processing thresholds
- **Long-Tail Exploration**: Rank thresholds, frequency minimums, spam filtering criteria
- **Differential Analysis**: Change thresholds, comparison periods, velocity calculations
- **Temporal Analysis**: Historical data ranges, trend detection parameters

```mermaid
classDiagram
class Configuration {
+string baseUrl
+string apiKey
+number maxRetries
+number retryDelayMs
+number requestTimeoutMs
+number rateLimitDelayMs
}
class CZDSConfig {
+string username
+string password
+string tempDir
+number registrationCutoffHours
}
class CommonCrawlConfig {
+number longTailThreshold
+number maxRank
+boolean monthlyRollupEnabled
}
class PIRConfig {
+string tempDir
+number registrationCutoffHours
+number pageSize
}
class RadarConfig {
+number pageSize
}
class SonarConfig {
+number longTailThreshold
+number maxResults
+boolean monthlyProcessingEnabled
+number backfillDays
}
Configuration <|-- CZDSConfig
Configuration <|-- CommonCrawlConfig
Configuration <|-- PIRConfig
Configuration <|-- RadarConfig
Configuration <|-- SonarConfig
```

**Diagram sources**
- [CZDSConnector.ts](file://services/domain-seeder/src/connectors/CZDSConnector.ts#L1-L713)
- [CommonCrawlConnector.ts](file://services/domain-seeder/src/connectors/CommonCrawlConnector.ts#L1-L629)
- [PIRConnector.ts](file://services/domain-seeder/src/connectors/PIRConnector.ts#L1-L644)
- [RadarConnector.ts](file://services/domain-seeder/src/connectors/RadarConnector.ts#L1-L531)
- [SonarConnector.ts](file://services/domain-seeder/src/connectors/SonarConnector.ts#L1-L706)

**Section sources**
- [CZDSConnector.ts](file://services/domain-seeder/src/connectors/CZDSConnector.ts#L1-L713)
- [CommonCrawlConnector.ts](file://services/domain-seeder/src/connectors/CommonCrawlConnector.ts#L1-L629)
- [PIRConnector.ts](file://services/domain-seeder/src/connectors/PIRConnector.ts#L1-L644)
- [RadarConnector.ts](file://services/domain-seeder/src/connectors/RadarConnector.ts#L1-L531)
- [SonarConnector.ts](file://services/domain-seeder/src/connectors/SonarConnector.ts#L1-L706)

## Rate Limiting and Reliability
The Domain Discovery system implements comprehensive rate limiting and reliability mechanisms to ensure stable operation and respect for external API limits.

### Rate Limiting Implementation
Each connector implements rate limiting through configurable delays between requests. The system uses exponential backoff for retry attempts, with delays that increase with each failed attempt.

```mermaid
flowchart TD
A[Make Request] --> B{Success?}
B --> |Yes| C[Process Response]
B --> |No| D[Wait Delay Period]
D --> E{Max Retries Reached?}
E --> |No| F[Exponential Backoff]
F --> A
E --> |Yes| G[Fail with Error]
C --> H[Apply Rate Limit Delay]
H --> I[Next Request]
```

**Diagram sources**
- [CZDSConnector.ts](file://services/domain-seeder/src/connectors/CZDSConnector.ts#L1-L713)
- [CommonCrawlConnector.ts](file://services/domain-seeder/src/connectors/CommonCrawlConnector.ts#L1-L629)
- [PIRConnector.ts](file://services/domain-seeder/src/connectors/PIRConnector.ts#L1-L644)
- [RadarConnector.ts](file://services/domain-seeder/src/connectors/RadarConnector.ts#L1-L531)
- [SonarConnector.ts](file://services/domain-seeder/src/connectors/SonarConnector.ts#L1-L706)

**Section sources**
- [CZDSConnector.ts](file://services/domain-seeder/src/connectors/CZDSConnector.ts#L1-L713)
- [CommonCrawlConnector.ts](file://services/domain-seeder/src/connectors/CommonCrawlConnector.ts#L1-L629)
- [PIRConnector.ts](file://services/domain-seeder/src/connectors/PIRConnector.ts#L1-L644)
- [RadarConnector.ts](file://services/domain-seeder/src/connectors/RadarConnector.ts#L1-L531)
- [SonarConnector.ts](file://services/domain-seeder/src/connectors/SonarConnector.ts#L1-L706)

### Reliability Mechanisms
The system implements several reliability features:
- **Retry Logic**: Automatic retries with exponential backoff for failed requests
- **Error Handling**: Comprehensive error logging and graceful degradation
- **Streaming Processing**: Large datasets are processed in streams to minimize memory usage
- **Checkpointing**: Progress is tracked for long-running operations to enable resumption
- **Health Checks**: Regular health checks ensure connectors are operational

## Common Issues and Solutions
### Authentication Failures
**Issue**: Connectors fail to authenticate with external services.
**Solution**: Verify that environment variables containing credentials are properly set and that API keys have not expired.

### Rate Limit Exceeded
**Issue**: External APIs return rate limit exceeded errors.
**Solution**: Increase the `rateLimitDelayMs` configuration parameter and ensure the system respects the API's rate limit policy.

### Large File Processing
**Issue**: Zone files or large datasets cause memory issues.
**Solution**: The system uses streaming processing for large files, but ensure adequate temporary storage is available and monitor system resources.

### Data Quality Issues
**Issue**: Discovered domains include spam or low-quality entries.
**Solution**: Each connector implements spam filtering heuristics; these can be adjusted based on requirements.

### Network Timeouts
**Issue**: Requests timeout when processing large datasets.
**Solution**: Increase the `requestTimeoutMs` configuration parameter for connectors that process large amounts of data.

**Section sources**
- [CZDSConnector.ts](file://services/domain-seeder/src/connectors/CZDSConnector.ts#L1-L713)
- [CommonCrawlConnector.ts](file://services/domain-seeder/src/connectors/CommonCrawlConnector.ts#L1-L629)
- [PIRConnector.ts](file://services/domain-seeder/src/connectors/PIRConnector.ts#L1-L644)
- [RadarConnector.ts](file://services/domain-seeder/src/connectors/RadarConnector.ts#L1-L531)
- [SonarConnector.ts](file://services/domain-seeder/src/connectors/SonarConnector.ts#L1-L706)

## Integration with Other Components
The Domain Discovery system integrates with various other components in the domainr ecosystem:

### Data Storage
Discovered domains are stored in multiple database systems, including Manticore, MariaDB, and Scylla, with Redis used for caching frequently accessed data.

### Content Generation
Discovered domains feed into content generation systems that create descriptions, analyses, and other value-added information about the domains.

### Monitoring and Alerting
The system integrates with Prometheus and Grafana for monitoring, with alerting configured for critical failures or performance issues.

### Scheduling
Discovery operations are coordinated through a scheduling system that manages the cadence of different connectors based on their update frequency.

```mermaid
graph LR
A[Domain Discovery] --> B[Manticore]
A --> C[MariaDB]
A --> D[Scylla]
A --> E[Redis]
A --> F[Content Generation]
A --> G[Monitoring]
A --> H[Scheduling]
B --> I[Search]
C --> I
D --> I
E --> I
F --> J[Domain Analysis]
G --> K[Alerts]
H --> L[Automation]
```

**Diagram sources**
- [CZDSConnector.ts](file://services/domain-seeder/src/connectors/CZDSConnector.ts)
- [CommonCrawlConnector.ts](file://services/domain-seeder/src/connectors/CommonCrawlConnector.ts)
- [PIRConnector.ts](file://services/domain-seeder/src/connectors/PIRConnector.ts)
- [RadarConnector.ts](file://services/domain-seeder/src/connectors/RadarConnector.ts)
- [SonarConnector.ts](file://services/domain-seeder/src/connectors/SonarConnector.ts)

**Section sources**
- [CZDSConnector.ts](file://services/domain-seeder/src/connectors/CZDSConnector.ts#L1-L713)
- [CommonCrawlConnector.ts](file://services/domain-seeder/src/connectors/CommonCrawlConnector.ts#L1-L629)
- [PIRConnector.ts](file://services/domain-seeder/src/connectors/PIRConnector.ts#L1-L644)
- [RadarConnector.ts](file://services/domain-seeder/src/connectors/RadarConnector.ts#L1-L531)
- [SonarConnector.ts](file://services/domain-seeder/src/connectors/SonarConnector.ts#L1-L706)

## Usage Patterns
The Domain Discovery system supports several usage patterns for different scenarios:

### Real-time Discovery
For applications requiring up-to-date domain information, connectors with daily cadence (like CZDS, PIR, and Radar) can be configured to run frequently, providing near real-time discovery of new domains.

### Comprehensive Analysis
For deep analysis, the system can be configured to perform monthly backfills using connectors like Common Crawl and Sonar, processing large historical datasets to identify long-term trends.

### Targeted Research
By combining different discovery strategies and filtering parameters, researchers can target specific types of domains, such as newly registered domains in specific TLDs or domains showing rapid growth in popularity.

### Continuous Monitoring
The system can be set up to continuously monitor domain data sources, with alerts configured for specific events like the appearance of domains matching certain patterns or significant changes in domain rankings.

**Section sources**
- [CZDSConnector.ts](file://services/domain-seeder/src/connectors/CZDSConnector.ts#L1-L713)
- [CommonCrawlConnector.ts](file://services/domain-seeder/src/connectors/CommonCrawlConnector.ts#L1-L629)
- [PIRConnector.ts](file://services/domain-seeder/src/connectors/PIRConnector.ts#L1-L644)
- [RadarConnector.ts](file://services/domain-seeder/src/connectors/RadarConnector.ts#L1-L531)
- [SonarConnector.ts](file://services/domain-seeder/src/connectors/SonarConnector.ts#L1-L706)