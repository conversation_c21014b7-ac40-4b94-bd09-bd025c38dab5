# Discovery Strategies

<cite>
**Referenced Files in This Document**   
- [BaseStrategyProcessor.ts](file://services/domain-seeder/src/discovery/strategies/BaseStrategyProcessor.ts)
- [ZoneFileProcessor.ts](file://services/domain-seeder/src/discovery/strategies/ZoneFileProcessor.ts)
- [LongTailProcessor.ts](file://services/domain-seeder/src/discovery/strategies/LongTailProcessor.ts)
- [TemporalAnalysisProcessor.ts](file://services/domain-seeder/src/discovery/strategies/TemporalAnalysisProcessor.ts)
- [DifferentialAnalysisProcessor.ts](file://services/domain-seeder/src/discovery/strategies/DifferentialAnalysisProcessor.ts)
- [DiscoveryEngineFactory.ts](file://services/domain-seeder/src/discovery/DiscoveryEngineFactory.ts)
- [DiscoveryEngine.ts](file://services/domain-seeder/src/interfaces/DiscoveryEngine.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Core Discovery Strategies](#core-discovery-strategies)
3. [Base Strategy Interface](#base-strategy-interface)
4. [Strategy Registration and Execution](#strategy-registration-and-execution)
5. [Configuration and Performance](#configuration-and-performance)
6. [Integration with Enqueuer and Normalization](#integration-with-enqueuer-and-normalization)
7. [Error Handling and Recovery](#error-handling-and-recovery)
8. [Strategy Selection Guidance](#strategy-selection-guidance)
9. [Scaling Considerations](#scaling-considerations)
10. [Conclusion](#conclusion)

## Introduction
The domainr discovery system implements a multi-strategy approach to identify valuable domains through various analytical methods. This document details the four primary discovery strategies: ZoneFileProcessor for parsing DNS zone files, LongTailProcessor for identifying low-frequency domains, TemporalAnalysisProcessor for time-based discovery patterns, and DifferentialAnalysisProcessor for change detection between datasets. Each strategy extends a common base interface and integrates with the DiscoveryEngineFactory for unified execution. The system is designed to handle large-scale domain discovery operations with robust error handling, performance optimization, and recovery mechanisms.

## Core Discovery Strategies

### ZoneFileProcessor
The ZoneFileProcessor strategy identifies newly registered domains from CZDS (Centralized Zone Data Service) zone files, focusing on domains registered within the last 48 hours. This strategy processes candidates from authoritative DNS sources such as CZDS, PIR, and registry providers, extracting registration metadata to determine recency. Domains are considered "new" if their registration date falls within the configured cutoff period. The processor validates candidates by ensuring they have complete metadata for registration date extraction and filters for supported zone file sources. Confidence scoring is adjusted based on recency (domains registered in the last 24 hours receive higher confidence), source reliability (authoritative sources like CZDS and PIR receive higher reliability scores), and metadata quality.

**Section sources**
- [ZoneFileProcessor.ts](file://services/domain-seeder/src/discovery/strategies/ZoneFileProcessor.ts#L1-L416)

### LongTailProcessor
The LongTailProcessor strategy discovers domains beyond typical top-1M lists by processing data from Common Crawl, Sonar, FDNS, and Rapid7 sources. This strategy targets domains ranked between 1 million and 10 million, filtering candidates based on rank thresholds and source compatibility. The processor applies additional validation to eliminate likely spam domains through heuristics such as excessive numbers, random character sequences, suspicious TLDs, and known spam patterns. Confidence scoring incorporates rank proximity to the 1M threshold (domains closer to 1M receive higher confidence), source reliability, metadata quality, and recent activity indicators. The strategy groups candidates by source for optimized processing and applies a minimum confidence threshold of 0.35 to filter out low-quality discoveries.

**Section sources**
- [LongTailProcessor.ts](file://services/domain-seeder/src/discovery/strategies/LongTailProcessor.ts#L1-L528)

### TemporalAnalysisProcessor
The TemporalAnalysisProcessor strategy identifies rapidly rising domains by tracking ranking velocity and detecting significant upward movement (50%+ improvement). This processor requires historical snapshots to analyze ranking trends over time, with a minimum of two days of history required for analysis. The strategy builds ranking histories for domains, calculating improvement percentages, velocity (rank change per day), and trend consistency. Domains are considered "rising" if they meet the improvement threshold, show positive velocity, and demonstrate consistent upward movement. Confidence scoring is enhanced for domains with high improvement percentages, fast velocity, and consistent trends, with additional boosts for domains in top rankings (top 10k, 100k, or 1M). The processor supports configurable parameters for analysis window, improvement threshold, and maximum rank for consideration.

**Section sources**
- [TemporalAnalysisProcessor.ts](file://services/domain-seeder/src/discovery/strategies/TemporalAnalysisProcessor.ts#L1-L557)

### DifferentialAnalysisProcessor
The DifferentialAnalysisProcessor strategy identifies domains that newly appear in ranking lists by comparing current snapshots with historical snapshots. This processor implements a change detection mechanism, finding domains present in current data but absent from previous snapshots (typically yesterday's data). The strategy groups candidates by source and performs set-based lookups to identify new entries efficiently. For each new domain discovered, the processor stores the current snapshot for future comparisons, enabling ongoing differential analysis. Confidence scoring considers the domain's rank, source reliability, metadata completeness, and the recency of the historical snapshot. This strategy is particularly effective for identifying emerging domains that have recently entered ranking lists.

**Section sources**
- [DifferentialAnalysisProcessor.ts](file://services/domain-seeder/src/discovery/strategies/DifferentialAnalysisProcessor.ts#L1-L301)

## Base Strategy Interface

### BaseStrategyProcessor
The BaseStrategyProcessor class provides a common foundation for all discovery strategies, implementing the StrategyProcessor interface and enforcing consistent behavior across implementations. This abstract base class handles shared functionality including candidate validation, confidence calculation, result logging, and error handling. Each strategy inherits from this base class and must implement the abstract process method. The base processor includes methods for validating candidates (ensuring domain and source are present and valid), creating discovered domain results with normalized confidence scores, and logging processing metrics. The confidence calculation system incorporates multiple factors including base confidence, rank, source reliability, metadata quality, and recency, with each strategy able to extend this system with strategy-specific adjustments.

```mermaid
classDiagram
class BaseStrategyProcessor {
+logger : any
+strategyName : DiscoveryStrategyType
+process(candidates : DomainCandidate[], snapshotStore : SnapshotStore) : Promise~DiscoveredDomain[]~
+validateCandidates(candidates : DomainCandidate[]) : DomainCandidate[]
+isValidCandidate(candidate : DomainCandidate) : boolean
+createDiscoveredDomain(candidate : DomainCandidate, confidence : number, discoveryReason : string) : DiscoveredDomain
+calculateConfidence(factors : ConfidenceFactors) : number
+logResults(inputCount : number, outputCount : number, processingTime : number, additionalInfo? : Record~string, any~) : void
+handleError(error : Error, context : Record~string, any~) : never
+getStrategyMetadata() : Record~string, any~
}
class ZoneFileProcessor {
+process(candidates : DomainCandidate[], snapshotStore : SnapshotStore) : Promise~DiscoveredDomain[]~
+filterZoneFileCandidates(candidates : DomainCandidate[]) : DomainCandidate[]
+processRecentRegistrations(candidates : DomainCandidate[]) : DiscoveredDomain[]
+extractRegistrationInfo(candidate : DomainCandidate) : RegistrationInfo | null
+parseRegistrationDate(dateValue : unknown) : Date | null
+isRecentRegistration(registrationDate : Date, cutoffDate : Date) : boolean
+calculateCutoffDate() : Date
+calculateZoneFileConfidence(candidate : DomainCandidate, registrationInfo : RegistrationInfo) : number
+buildZoneFileDiscoveryReason(candidate : DomainCandidate, registrationInfo : RegistrationInfo) : string
+isVeryRecentRegistration(registrationDate : Date) : boolean
+getZoneFileSourceReliability(source : string) : number
+isAuthoritativeSource(source : string) : boolean
+validateCandidates(candidates : DomainCandidate[]) : DomainCandidate[]
}
class LongTailProcessor {
+process(candidates : DomainCandidate[], snapshotStore : SnapshotStore) : Promise~DiscoveredDomain[]~
+filterLongTailCandidates(candidates : DomainCandidate[]) : DomainCandidate[]
+isLongTailSource(source : string) : boolean
+isLongTailRank(rank? : number) : boolean
+isValidLongTailCandidate(candidate : DomainCandidate) : boolean
+processLongTailCandidates(candidates : DomainCandidate[], snapshotStore : SnapshotStore) : Promise~DiscoveredDomain[]~
+processSourceLongTail(source : string, candidates : DomainCandidate[], snapshotStore : SnapshotStore) : Promise~DiscoveredDomain[]~
+calculateLongTailConfidence(candidate : DomainCandidate) : number
+buildLongTailDiscoveryReason(candidate : DomainCandidate) : string
+getLongTailSourceReliability(source : string) : number
+hasQualityMetadata(candidate : DomainCandidate) : boolean
+hasRecentActivity(candidate : DomainCandidate) : boolean
+hasLongTailQualityIndicators(candidate : DomainCandidate) : boolean
+isLikelySpam(domain : string) : boolean
+hasMinimumMetadata(candidate : DomainCandidate) : boolean
+groupCandidatesBySource(candidates : DomainCandidate[]) : Map~string, DomainCandidate[]~
+calculateAverageRank(candidates : DomainCandidate[]) : number
+getMinimumConfidence() : number
}
class TemporalAnalysisProcessor {
+config : TemporalConfig
+process(candidates : DomainCandidate[], snapshotStore : SnapshotStore) : Promise~DiscoveredDomain[]~
+processSourceCandidates(source : string, candidates : DomainCandidate[], snapshotStore : SnapshotStore) : Promise~DiscoveredDomain[]~
+buildRankingHistories(snapshots : DomainSnapshot[], currentCandidates : DomainCandidate[]) : RankingHistory[]
+identifyRisingDomains(histories : RankingHistory[]) : DiscoveredDomain[]
+analyzeRankingTrend(history : RankingHistory) : TemporalAnalysis
+calculateTrendConsistency(rankings : { rank : number; date : string }[]) : number
+calculateTemporalConfidence(history : RankingHistory, analysis : TemporalAnalysis) : number
+buildTemporalDiscoveryReason(history : RankingHistory, analysis : TemporalAnalysis) : string
+groupCandidatesBySource(candidates : DomainCandidate[]) : Map~string, DomainCandidate[]~
+calculateAverageConfidence(discovered : DiscoveredDomain[]) : number
+calculateDaysDifference(date1 : string, date2 : string) : number
+getSourceReliability(source : string) : number
}
class DifferentialAnalysisProcessor {
+process(candidates : DomainCandidate[], snapshotStore : SnapshotStore) : Promise~DiscoveredDomain[]~
+processSourceCandidates(source : string, candidates : DomainCandidate[], snapshotStore : SnapshotStore) : Promise~DiscoveredDomain[]~
+findNewDomains(currentCandidates : DomainCandidate[], historicalSnapshot : DomainSnapshot) : DiscoveredDomain[]
+calculateDifferentialConfidence(candidate : DomainCandidate, historicalSnapshot : DomainSnapshot) : number
+buildDiscoveryReason(candidate : DomainCandidate, historicalSnapshot : DomainSnapshot) : string
+groupCandidatesBySource(candidates : DomainCandidate[]) : Map~string, DomainCandidate[]~
+storeCurrentSnapshots(candidatesBySource : Map~string, DomainCandidate[]~, snapshotStore : SnapshotStore) : Promise~void~
+getYesterdayDate() : string
+getSourceReliability(source : string) : number
+isRecentSnapshot(snapshot : DomainSnapshot) : boolean
}
BaseStrategyProcessor <|-- ZoneFileProcessor
BaseStrategyProcessor <|-- LongTailProcessor
BaseStrategyProcessor <|-- TemporalAnalysisProcessor
BaseStrategyProcessor <|-- DifferentialAnalysisProcessor
```

**Diagram sources**
- [BaseStrategyProcessor.ts](file://services/domain-seeder/src/discovery/strategies/BaseStrategyProcessor.ts#L1-L203)
- [ZoneFileProcessor.ts](file://services/domain-seeder/src/discovery/strategies/ZoneFileProcessor.ts#L1-L416)
- [LongTailProcessor.ts](file://services/domain-seeder/src/discovery/strategies/LongTailProcessor.ts#L1-L528)
- [TemporalAnalysisProcessor.ts](file://services/domain-seeder/src/discovery/strategies/TemporalAnalysisProcessor.ts#L1-L557)
- [DifferentialAnalysisProcessor.ts](file://services/domain-seeder/src/discovery/strategies/DifferentialAnalysisProcessor.ts#L1-L301)

**Section sources**
- [BaseStrategyProcessor.ts](file://services/domain-seeder/src/discovery/strategies/BaseStrategyProcessor.ts#L1-L203)

## Strategy Registration and Execution

### DiscoveryEngineFactory
The DiscoveryEngineFactory class is responsible for creating and configuring discovery engines with registered strategies. This factory handles dependency injection and strategy registration, providing a centralized mechanism for engine creation. The factory creates a Redis-based snapshot store for persistent storage of domain snapshots and registers the configured strategies with the intelligent discovery engine. By default, all four strategies (differential, zone-new, long-tail, and temporal) are enabled, but this can be customized through configuration options. The factory supports fail-safe registration, where errors in individual strategy creation do not prevent the engine from being created unless explicitly configured to fail. For testing purposes, the factory can create a minimal discovery engine with only the differential strategy registered, using a mock snapshot store.

```mermaid
sequenceDiagram
participant Client as "Client Application"
participant Factory as "DiscoveryEngineFactory"
participant Engine as "IntelligentDiscoveryEngine"
participant Store as "RedisSnapshotStore"
participant Strategy as "Strategy Processors"
Client->>Factory : createDiscoveryEngine(redis, options)
Factory->>Factory : Initialize logger
Factory->>Factory : Create snapshot store
Factory->>Store : new RedisSnapshotStore(redis, options)
Factory->>Engine : new IntelligentDiscoveryEngine(snapshotStore)
Factory->>Factory : Register strategies
loop For each strategy
Factory->>Factory : createStrategyProcessor(strategy)
alt Strategy exists
Factory->>Strategy : Instantiate processor
Factory->>Engine : registerStrategy(name, processor)
else Unknown strategy
Factory->>Factory : Log warning, skip registration
end
end
Factory-->>Client : Return configured engine
Note over Client,Engine : Engine ready for processing domain candidates
```

**Diagram sources**
- [DiscoveryEngineFactory.ts](file://services/domain-seeder/src/discovery/DiscoveryEngineFactory.ts#L1-L291)

**Section sources**
- [DiscoveryEngineFactory.ts](file://services/domain-seeder/src/discovery/DiscoveryEngineFactory.ts#L1-L291)

## Configuration and Performance

### Configuration Parameters
Each discovery strategy supports configurable parameters to tune its behavior for specific use cases. The ZoneFileProcessor allows configuration of the registration cutoff period (default 48 hours) to adjust sensitivity to new domain registrations. The LongTailProcessor can be configured with different rank thresholds to target specific segments of the long-tail domain space. The TemporalAnalysisProcessor supports extensive configuration including minimum history days, improvement threshold (default 50%), maximum rank for analysis, and time window for comparison. The DifferentialAnalysisProcessor uses a fixed one-day comparison window but could be extended to support configurable time periods. The DiscoveryEngineFactory accepts options for snapshot store configuration (key prefix, compression) and strategy selection, allowing fine-grained control over engine behavior.

### Performance Characteristics
The discovery strategies are designed with performance optimization in mind, implementing several techniques to handle large-scale operations efficiently. All strategies include early filtering to reduce processing load, with invalid candidates removed before intensive analysis. The processors group candidates by source to enable batch processing and optimize database interactions. The ZoneFileProcessor uses set-based operations to quickly filter candidates by source, while the LongTailProcessor sorts candidates by rank to prioritize higher-quality domains. The TemporalAnalysisProcessor builds ranking histories in memory for fast trend analysis, and the DifferentialAnalysisProcessor uses Set data structures for O(1) lookup performance when identifying new domains. Confidence calculations are optimized to minimize redundant operations, and logging is structured to provide detailed metrics without excessive overhead.

**Section sources**
- [ZoneFileProcessor.ts](file://services/domain-seeder/src/discovery/strategies/ZoneFileProcessor.ts#L1-L416)
- [LongTailProcessor.ts](file://services/domain-seeder/src/discovery/strategies/LongTailProcessor.ts#L1-L528)
- [TemporalAnalysisProcessor.ts](file://services/domain-seeder/src/discovery/strategies/TemporalAnalysisProcessor.ts#L1-L557)
- [DifferentialAnalysisProcessor.ts](file://services/domain-seeder/src/discovery/strategies/DifferentialAnalysisProcessor.ts#L1-L301)
- [DiscoveryEngineFactory.ts](file://services/domain-seeder/src/discovery/DiscoveryEngineFactory.ts#L1-L291)

## Integration with Enqueuer and Normalization

### Enqueuer Integration
The discovery strategies integrate with the RateLimitedDomainEnqueuer to manage the flow of discovered domains into downstream processing systems. After a strategy identifies domains, they are passed through the enqueuer which applies rate limiting to prevent overwhelming external services. The enqueuer uses a token bucket algorithm to control the rate of domain processing, ensuring compliance with API rate limits and preventing service disruptions. Each discovered domain includes confidence and discovery reason metadata that informs the enqueuer's prioritization logic, with higher-confidence discoveries potentially receiving preferential treatment. The integration is designed to be resilient, with the enqueuer implementing backpressure control to handle temporary spikes in discovery volume.

### Normalization Integration
Discovered domains are processed through the PSLDomainNormalizer to ensure consistent formatting and accurate domain analysis. The normalization layer handles tasks such as removing subdomains, standardizing case, and validating domain structure according to public suffix rules. This ensures that domains from different sources are compared and analyzed consistently. The normalizer also enriches domain data with additional metadata such as TLD category and country code, which can be used by subsequent processing stages. The integration between discovery strategies and normalization is seamless, with the discovery engine passing raw candidates to the normalizer before strategy processing begins, ensuring that all strategies operate on consistently formatted data.

**Section sources**
- [ZoneFileProcessor.ts](file://services/domain-seeder/src/discovery/strategies/ZoneFileProcessor.ts#L1-L416)
- [LongTailProcessor.ts](file://services/domain-seeder/src/discovery/strategies/LongTailProcessor.ts#L1-L528)
- [TemporalAnalysisProcessor.ts](file://services/domain-seeder/src/discovery/strategies/TemporalAnalysisProcessor.ts#L1-L557)
- [DifferentialAnalysisProcessor.ts](file://services/domain-seeder/src/discovery/strategies/DifferentialAnalysisProcessor.ts#L1-L301)
- [enqueuer/RateLimitedDomainEnqueuer.ts](file://services/domain-seeder/src/enqueuer/RateLimitedDomainEnqueuer.ts)
- [normalization/PSLDomainNormalizer.ts](file://services/domain-seeder/src/normalization/PSLDomainNormalizer.ts)

## Error Handling and Recovery

### Processing Bottlenecks
The discovery system addresses processing bottlenecks through several mechanisms. Each strategy implements source-specific processing to prevent any single source from dominating resources. The LongTailProcessor groups candidates by source and processes them in parallel, while the TemporalAnalysisProcessor uses Promise.all to handle multiple sources concurrently. The system includes backpressure control through the BackpressureController, which monitors processing queues and adjusts ingestion rates when thresholds are exceeded. For particularly large datasets, the processors implement chunking and streaming where appropriate to avoid memory exhaustion. The DiscoveryEngineFactory includes fail-safe strategy registration, allowing the system to continue operating even if individual strategies encounter issues.

### Data Drift and Partial Failures
The system is designed to handle data drift and partial failures gracefully. The BaseStrategyProcessor includes comprehensive error handling that logs detailed context and rethrows errors with strategy-specific messaging. Each strategy validates input data and filters invalid candidates early in the process to prevent cascading failures. The DifferentialAnalysisProcessor includes fallback logic when historical snapshots are unavailable, treating the first run as a baseline for future comparisons. The TemporalAnalysisProcessor requires a minimum amount of historical data before performing analysis, preventing unreliable trend detection with insufficient data. The system implements retry mechanisms at multiple levels, with the DiscoveryEngineFactory able to retry strategy registration and individual processors able to handle transient errors during snapshot retrieval.

**Section sources**
- [BaseStrategyProcessor.ts](file://services/domain-seeder/src/discovery/strategies/BaseStrategyProcessor.ts#L1-L203)
- [ZoneFileProcessor.ts](file://services/domain-seeder/src/discovery/strategies/ZoneFileProcessor.ts#L1-L416)
- [LongTailProcessor.ts](file://services/domain-seeder/src/discovery/strategies/LongTailProcessor.ts#L1-L528)
- [TemporalAnalysisProcessor.ts](file://services/domain-seeder/src/discovery/strategies/TemporalAnalysisProcessor.ts#L1-L557)
- [DifferentialAnalysisProcessor.ts](file://services/domain-seeder/src/discovery/strategies/DifferentialAnalysisProcessor.ts#L1-L301)
- [reliability/CrashSafeReliabilityManager.ts](file://services/domain-seeder/src/reliability/CrashSafeReliabilityManager.ts)

## Strategy Selection Guidance

### Discovery Goals
The selection of appropriate discovery strategies depends on specific goals and data characteristics. For identifying newly registered domains, the ZoneFileProcessor is the optimal choice, particularly when working with authoritative DNS sources. When the objective is to discover emerging domains that have recently entered ranking lists, the DifferentialAnalysisProcessor provides the most direct approach. For finding high-potential domains in the long tail of the internet, the LongTailProcessor offers targeted analysis of domains ranked between 1M and 10M. To identify domains with rapid growth trajectories, the TemporalAnalysisProcessor's trend analysis capabilities are essential. In most production scenarios, a combination of multiple strategies provides the most comprehensive discovery coverage.

### Complementary Strategies
The strategies are designed to complement each other, with each focusing on different aspects of domain discovery. The ZoneFileProcessor and DifferentialAnalysisProcessor both identify new domains but through different mechanisms—registration timing versus ranking list appearance. The LongTailProcessor and TemporalAnalysisProcessor can work together to identify domains that are both in the long tail and showing rapid growth. A typical discovery pipeline might use the DifferentialAnalysisProcessor for broad change detection, the TemporalAnalysisProcessor for growth trend analysis, and the ZoneFileProcessor for confirming recent registrations, with the LongTailProcessor providing additional coverage for domains outside top rankings. This multi-strategy approach reduces the risk of missing valuable domains that might be invisible to any single strategy.

**Section sources**
- [ZoneFileProcessor.ts](file://services/domain-seeder/src/discovery/strategies/ZoneFileProcessor.ts#L1-L416)
- [LongTailProcessor.ts](file://services/domain-seeder/src/discovery/strategies/LongTailProcessor.ts#L1-L528)
- [TemporalAnalysisProcessor.ts](file://services/domain-seeder/src/discovery/strategies/TemporalAnalysisProcessor.ts#L1-L557)
- [DifferentialAnalysisProcessor.ts](file://services/domain-seeder/src/discovery/strategies/DifferentialAnalysisProcessor.ts#L1-L301)

## Scaling Considerations

### Large-Scale Operations
For large-scale operations, several considerations ensure optimal performance and reliability. The discovery engine should be deployed with sufficient memory to handle large candidate sets, particularly for the TemporalAnalysisProcessor which maintains ranking histories in memory. The Redis snapshot store should be configured with appropriate memory limits and eviction policies to prevent out-of-memory conditions. Processing can be distributed across multiple instances, with each instance handling a subset of sources or strategies. The system supports horizontal scaling through the use of distributed Redis for snapshot storage and message queues for coordinating discovery tasks. For extremely large datasets, the processors can be modified to use streaming or chunked processing to minimize memory footprint.

### Resource Optimization
Resource optimization is achieved through several techniques. The strategies implement early filtering to reduce the volume of data passed through expensive operations. The use of Set and Map data structures enables efficient lookups and grouping operations. The system leverages Redis for fast snapshot storage and retrieval, with optional compression to reduce bandwidth and storage requirements. The enqueuer's rate limiting prevents resource exhaustion in downstream systems. Monitoring is integrated throughout the system, with metrics collected on processing times, discovery rates, and error conditions to inform capacity planning and optimization efforts. The modular design allows individual strategies to be scaled independently based on their resource requirements and discovery effectiveness.

**Section sources**
- [DiscoveryEngineFactory.ts](file://services/domain-seeder/src/discovery/DiscoveryEngineFactory.ts#L1-L291)
- [stores/RedisSnapshotStore.ts](file://services/domain-seeder/src/discovery/stores/RedisSnapshotStore.ts)
- [enqueuer/RateLimitedDomainEnqueuer.ts](file://services/domain-seeder/src/enqueuer/RateLimitedDomainEnqueuer.ts)
- [ratelimiting/TokenBucket.ts](file://services/domain-seeder/src/ratelimiting/TokenBucket.ts)
- [monitoring/MetricsCollector.ts](file://services/domain-seeder/src/monitoring/MetricsCollector.ts)

## Conclusion
The domainr discovery strategies system provides a comprehensive framework for identifying valuable domains through multiple analytical approaches. Each strategy—ZoneFileProcessor, LongTailProcessor, TemporalAnalysisProcessor, and DifferentialAnalysisProcessor—addresses a specific aspect of domain discovery, from new registrations to long-tail exploration and trend analysis. The shared BaseStrategyProcessor interface ensures consistent behavior across implementations, while the DiscoveryEngineFactory enables flexible configuration and registration. The system integrates with enqueuer and normalization layers to ensure reliable processing and consistent data quality. With robust error handling, performance optimization, and scalability features, the discovery system is well-suited for large-scale operations. By selecting appropriate strategies based on specific discovery goals, organizations can effectively identify emerging domains and gain competitive intelligence in the domain space.