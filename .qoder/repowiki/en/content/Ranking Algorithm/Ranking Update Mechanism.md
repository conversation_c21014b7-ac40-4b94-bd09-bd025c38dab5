# Ranking Update Mechanism

<cite>
**Referenced Files in This Document**   
- [RankingUpdateService.ts](file://services/worker/src/ranking/RankingUpdateService.ts)
- [SchedulerService.ts](file://services/worker/src/scheduler/SchedulerService.ts)
- [WorkerJobQueue.ts](file://services/worker/src/queue/WorkerJobQueue.ts)
- [ranking-update-demo.ts](file://services/worker/src/ranking/examples/ranking-update-demo.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Architecture Overview](#architecture-overview)
3. [Workflow from Job Scheduling to Completion](#workflow-from-job-scheduling-to-completion)
4. [Batch Processing and Error Handling](#batch-processing-and-error-handling)
5. [Configuration Options](#configuration-options)
6. [Monitoring Integration](#monitoring-integration)
7. [Minimizing Downtime and Large-Scale Recalculations](#minimizing-downtime-and-large-scale-recalculations)
8. [Examples from ranking-update-demo.ts](#examples-from-ranking-update-demo.ts)

## Introduction
The Ranking Update Mechanism is a critical component of the domain analysis system, responsible for orchestrating periodic ranking recalculations through the coordination of RankingUpdateService, SchedulerService, and WorkerJobQueue. This system ensures that domain rankings are updated regularly based on various metrics such as performance, security, SEO, and technical factors. The mechanism supports both single domain updates and batch processing for large-scale recalculations, with comprehensive error handling and status tracking capabilities.

**Section sources**
- [RankingUpdateService.ts](file://services/worker/src/ranking/RankingUpdateService.ts#L1-L50)

## Architecture Overview

```mermaid
graph TB
subgraph "Scheduler Service"
SCHED[SchedulerService]
CRON[Cron Scheduler]
end
subgraph "Ranking Update Service"
RANK[RankingUpdateService]
COMPOSITE[CompositeRanker]
BATCH[Batch Management]
end
subgraph "Job Queue System"
QUEUE[WorkerJobQueue]
REDIS[(Redis)]
CONSUMER[Job Consumers]
end
subgraph "Data Storage"
SCYLLA[(ScyllaDB)]
RANKING[domain_rankings]
HISTORY[domain_ranking_history]
end
subgraph "Monitoring"
PROM[Prometheus]
GRAFANA[Grafana]
end
CRON --> |Schedules| SCHED
SCHED --> |Triggers| RANK
RANK --> |Publishes Jobs| QUEUE
QUEUE --> |Processes| CONSUMER
CONSUMER --> |Updates| RANK
RANK --> |Stores| SCYLLA
RANK --> |Caches| REDIS
QUEUE --> |Metrics| PROM
SCHED --> |Metrics| PROM
PROM --> |Visualizes| GRAFANA
SCYLLA --> |Provides Data| RANK
```

**Diagram sources**
- [RankingUpdateService.ts](file://services/worker/src/ranking/RankingUpdateService.ts#L1-L1358)
- [SchedulerService.ts](file://services/worker/src/scheduler/SchedulerService.ts#L1-L815)
- [WorkerJobQueue.ts](file://services/worker/src/queue/WorkerJobQueue.ts#L1-L1055)

## Workflow from Job Scheduling to Completion

The ranking update workflow begins with the SchedulerService initiating periodic updates according to predefined cron expressions. The RankingUpdateService then orchestrates the entire process through the WorkerJobQueue system.

The workflow follows these steps:
1. **Scheduling**: SchedulerService triggers ranking updates based on cron expressions (daily at 2 AM for global updates, weekly on Sundays at 3 AM for category updates)
2. **Job Creation**: RankingUpdateService creates jobs for single domains or batches of domains
3. **Queue Processing**: WorkerJobQueue manages the job lifecycle with configurable concurrency and retry policies
4. **Execution**: Job consumers process ranking calculations and store results
5. **History Tracking**: Ranking history is maintained for trend analysis
6. **Related Updates**: Secondary processes like search index synchronization are triggered

For single domain updates, the process is straightforward with immediate processing. For batch operations, domains are processed in parallel with error isolation to prevent single failures from affecting the entire batch.

```mermaid
sequenceDiagram
participant Scheduler as SchedulerService
participant Ranking as RankingUpdateService
participant Queue as WorkerJobQueue
participant Consumer as Job Consumer
participant DB as ScyllaDB
Scheduler->>Ranking : scheduleRankingUpdates()
Ranking->>Ranking : getDomainsForRankingUpdate()
loop Each Domain
Ranking->>Queue : publishJob('ranking : update')
end
Queue->>Consumer : Process job
Consumer->>Ranking : handleSingleRankingUpdate()
Ranking->>DB : fetchDomainData()
Ranking->>Ranking : calculateCompositeScore()
Ranking->>DB : storeDomainRanking()
Ranking->>DB : updateRankingHistory()
Ranking->>Queue : triggerRelatedUpdates()
Consumer->>Queue : Acknowledge completion
```

**Diagram sources**
- [RankingUpdateService.ts](file://services/worker/src/ranking/RankingUpdateService.ts#L1-L1358)
- [SchedulerService.ts](file://services/worker/src/scheduler/SchedulerService.ts#L1-L815)

## Batch Processing and Error Handling

The RankingUpdateService implements robust batch processing capabilities with comprehensive error handling mechanisms. Batch operations are managed through the activeBatches Map, which tracks the status, progress, and errors for each batch operation.

Key features of batch processing include:
- **Progress Tracking**: Each batch maintains processedCount and totalCount to monitor progress
- **Error Isolation**: Individual domain processing errors are captured in the batch's errors array without stopping the entire batch
- **Status Management**: Batches transition through pending, processing, completed, and failed states
- **Cleanup**: Completed batches are automatically cleaned up after 24 hours

Error handling is implemented at multiple levels:
1. **Domain-level errors**: When fetching domain data fails, the error is logged and added to the batch errors array
2. **Batch-level errors**: If no valid domain data is found, the entire batch fails
3. **System-level errors**: Unhandled exceptions during batch processing result in batch status being set to failed

The system also implements retry mechanisms through the WorkerJobQueue, with exponential backoff strategies for transient failures. Permanent failures are sent to a dead letter queue for further analysis.

```mermaid
flowchart TD
Start([Start Batch Update]) --> ValidateInput["Validate Input Domains"]
ValidateInput --> InputValid{"Input Valid?"}
InputValid --> |No| ReturnError["Return Error Response"]
InputValid --> |Yes| InitializeBatch["Initialize Batch Tracking"]
InitializeBatch --> PublishJob["Publish to ranking:batch Queue"]
PublishJob --> ProcessBatch["handleBatchRankingUpdate()"]
ProcessBatch --> FetchData["Fetch Domain Data"]
FetchData --> ProcessDomain["Process Each Domain"]
ProcessDomain --> FetchSuccess{"Fetch Success?"}
FetchSuccess --> |No| CaptureError["Add to batch.errors"]
FetchSuccess --> |Yes| CalculateRank["Calculate Ranking"]
CalculateRank --> StoreResult["Store Ranking in DB"]
StoreResult --> UpdateHistory["Update Ranking History"]
ProcessDomain --> AllProcessed{"All Domains<br/>Processed?"}
AllProcessed --> |No| ProcessDomain
AllProcessed --> |Yes| CheckResults{"Any Valid Results?"}
CheckResults --> |No| BatchFailed["Set batch.status = 'failed'"]
CheckResults --> |Yes| CompleteBatch["Set batch.status = 'completed'"]
CompleteBatch --> Cleanup["cleanupCompletedBatches()"]
BatchFailed --> Cleanup
CaptureError --> AllProcessed
ReturnError --> End([End])
Cleanup --> End
```

**Diagram sources**
- [RankingUpdateService.ts](file://services/worker/src/ranking/RankingUpdateService.ts#L1-L1358)
- [WorkerJobQueue.ts](file://services/worker/src/queue/WorkerJobQueue.ts#L1-L1055)

## Configuration Options

The ranking update mechanism provides several configuration options for tuning update frequency, batch sizes, and concurrency limits:

### Update Frequency Configuration
The system uses cron expressions to schedule recurring updates:
- **Global Daily Updates**: Scheduled at 2 AM daily using cron expression `0 2 * * *`
- **Category Weekly Updates**: Scheduled at 3 AM on Sundays using cron expression `0 3 * * 0`

These schedules are configured in the setupScheduledUpdates method of RankingUpdateService.

### Batch Size Configuration
Batch processing parameters are configurable through the WorkerJobQueue:
- **Maximum Batch Size**: Configurable via batchSize parameter (default: 10)
- **Delay Between Batches**: Configurable via batchDelayMs parameter (default: 100ms)
- **Fail Fast Mode**: Optional setting to stop processing on first error

### Concurrency Limits
Concurrency is managed at multiple levels:
- **Single Domain Updates**: Configured with concurrency: 3 in the 'ranking:update' consumer
- **Batch Updates**: Configured with concurrency: 1 in the 'ranking:batch' consumer to prevent resource contention
- **History Updates**: Configured with concurrency: 2 in the 'ranking:history' consumer

### Priority Configuration
The system supports three priority levels with corresponding numeric values:
- **Low**: Priority value 1
- **Medium**: Priority value 5 (default)
- **High**: Priority value 10

Priority affects job processing order in the queue, with higher numeric values processed first.

**Section sources**
- [RankingUpdateService.ts](file://services/worker/src/ranking/RankingUpdateService.ts#L1-L1358)
- [WorkerJobQueue.ts](file://services/worker/src/queue/WorkerJobQueue.ts#L1-L1055)

## Monitoring Integration

The ranking update mechanism integrates with monitoring systems to track update progress and failures through multiple channels:

### Prometheus Metrics
The system exposes metrics through the WorkerJobQueue health check and statistics collection:
- **Queue Statistics**: Pending, processing, completed, and failed job counts
- **Error Rate**: Percentage of failed jobs
- **Throughput**: Jobs completed per minute
- **Processing Time**: Average time to process jobs

These metrics are collected and exposed to Prometheus for visualization in Grafana dashboards.

### Health Checks
Comprehensive health checks are implemented at multiple levels:
- **Service Health**: RankingUpdateService.healthCheck() verifies database connections and job queue status
- **Queue Health**: WorkerJobQueue.getHealth() provides detailed consumer and producer status
- **Database Health**: DatabaseManager.healthCheck() confirms connectivity to ScyllaDB and Redis

### Logging
Structured logging is implemented using Pino with the following features:
- **Multiple Log Levels**: trace, debug, info, warn, error, fatal
- **Structured JSON**: Production logging with consistent format
- **Performance Logging**: Duration tracking for key operations
- **Correlation IDs**: For tracing requests across components

### Alerting
The system supports alerting based on:
- **High Error Rates**: Alert when error rate exceeds 10%
- **Queue Backlogs**: Alert when pending jobs exceed thresholds
- **Service Unavailability**: Alert when health checks fail
- **Processing Delays**: Alert when average processing time exceeds expected values

**Section sources**
- [RankingUpdateService.ts](file://services/worker/src/ranking/RankingUpdateService.ts#L1-L1358)
- [WorkerJobQueue.ts](file://services/worker/src/queue/WorkerJobQueue.ts#L1-L1055)

## Minimizing Downtime and Large-Scale Recalculations

The system implements several strategies to minimize downtime and efficiently handle large-scale recalculations:

### Staged Processing
Large-scale updates are processed in batches to prevent system overload:
- **Batch Size Limits**: 10,000 domains for global updates, 1,000 for category updates
- **Rate Limiting**: Delay between batches to control resource usage
- **Priority-Based Scheduling**: High-priority domains processed first

### Incremental Updates
The system supports incremental updates to minimize processing overhead:
- **Selective Updates**: Only domains that haven't been updated recently are processed
- **Change Detection**: Updates triggered only when domain data changes
- **Category-Specific Updates**: Only recalculate rankings for affected categories

### Fault Tolerance
Multiple fault tolerance mechanisms are implemented:
- **Retry Logic**: Exponential backoff for transient failures
- **Error Isolation**: Single domain failures don't affect batch completion
- **Dead Letter Queue**: Failed jobs are preserved for analysis and reprocessing

### Performance Optimization
Several performance optimizations are in place:
- **Caching**: Trend analysis results cached in Redis for 24 hours
- **Delayed Category Updates**: Category recalculations delayed by 10 minutes to avoid frequent updates
- **Database Optimization**: Batched database operations for efficiency

### Scalability
The system is designed for horizontal scalability:
- **Distributed Processing**: Multiple worker instances can process jobs in parallel
- **Queue-Based Architecture**: Decouples scheduling from processing
- **Stateless Services**: RankingUpdateService maintains minimal state

**Section sources**
- [RankingUpdateService.ts](file://services/worker/src/ranking/RankingUpdateService.ts#L1-L1358)
- [SchedulerService.ts](file://services/worker/src/scheduler/SchedulerService.ts#L1-L815)

## Examples from ranking-update-demo.ts

The ranking-update-demo.ts file provides practical examples of using the ranking update mechanism:

### Single Domain Update
```typescript
const singleDomainJobId = await rankingUpdateService.triggerDomainRankingUpdate(
    'example.com',
    {
        type: 'manual',
        priority: 'high',
        metadata: {
            reason: 'Demo update',
            requestedBy: 'demo-script',
        },
    },
);
```
This example demonstrates triggering a high-priority update for a single domain with custom metadata.

### Batch Ranking Update
```typescript
const domains = [
    'google.com',
    'facebook.com',
    'amazon.com',
    'microsoft.com',
    'apple.com',
];

const batchId = await rankingUpdateService.triggerBatchRankingUpdate(
    domains,
    'global',
    undefined,
    'medium',
);
```
This shows how to trigger a batch update for multiple domains with medium priority.

### Category-Specific Update
```typescript
const techDomains = [
    'github.com',
    'stackoverflow.com',
    'techcrunch.com',
    'wired.com',
];

const categoryBatchId = await rankingUpdateService.triggerBatchRankingUpdate(
    techDomains,
    'category',
    'technology',
    'low',
);
```
This example demonstrates updating rankings for domains within a specific category.

### Batch Status Monitoring
```typescript
const batchStatus = rankingUpdateService.getBatchStatus(batchId);
if (batchStatus) {
    logger.info('Batch Status:', {
        batchId: batchStatus.batchId,
        status: batchStatus.status,
        totalCount: batchStatus.totalCount,
        processedCount: batchStatus.processedCount,
        errors: batchStatus.errors.length,
    });
}
```
This shows how to monitor the status of a batch operation.

### Trend Analysis
```typescript
const trendAnalysis = await rankingUpdateService.calculateRankingTrend(
    'example.com',
    'global',
    '30d',
);
```
This demonstrates calculating ranking trend analysis over a 30-day period.

**Section sources**
- [ranking-update-demo.ts](file://services/worker/src/ranking/examples/ranking-update-demo.ts#L1-L230)