# Security Metrics

<cite>
**Referenced Files in This Document**   
- [SecurityScorer.ts](file://services/worker/src/ranking/scorers/SecurityScorer.ts)
- [SSLAnalyzer.ts](file://services/worker/src/crawler/analyzers/SSLAnalyzer.ts)
- [SecurityScorer.test.ts](file://services/worker/src/ranking/__tests__/SecurityScorer.test.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Security Scoring Architecture](#security-scoring-architecture)
3. [SSL/TLS Configuration Evaluation](#ssltls-configuration-evaluation)
4. [Security Headers Assessment](#security-headers-assessment)
5. [Vulnerability Assessment and Penalty Application](#vulnerability-assessment-and-penalty-application)
6. [Security Score Calculation Algorithm](#security-score-calculation-algorithm)
7. [SSLAnalyzer Implementation Details](#sslanalyzer-implementation-details)
8. [Test Case Examples and Score Calculations](#test-case-examples-and-score-calculations)
9. [Edge Case Handling](#edge-case-handling)
10. [Security Recommendations and Remediation](#security-recommendations-and-remediation)
11. [Performance Considerations](#performance-considerations)

## Introduction
The SecurityScorer component provides a comprehensive security evaluation system that assesses domain security across multiple dimensions including SSL/TLS configuration, security headers, HTTPS usage, and known vulnerabilities. This document details the implementation of the SecurityScorer class, its integration with the SSLAnalyzer, and the methodology for generating security scores. The system uses a weighted scoring algorithm to produce a normalized security score between 0.0 and 1.0, which is then converted to a letter grade (A+ to F) for easy interpretation. The scoring system is designed to identify security weaknesses and provide actionable recommendations for improvement.

## Security Scoring Architecture

```mermaid
classDiagram
class SecurityScorer {
+static WEIGHTS : object
+static SSL_GRADES : object
+static VULNERABILITY_PENALTIES : object
+static calculateScore(metrics : SecurityMetricsType) : number
+static getSecurityGrade(score : number) : string
+static getSecurityBreakdown(metrics : SecurityMetricsType) : object
+static getSecurityRecommendations(metrics : SecurityMetricsType) : string[]
-static calculateSSLScore(metrics : SecurityMetricsType) : number
-static calculateSecurityHeadersScore(headers : SecurityHeadersType) : number
-static calculateHttpsScore(usesHttps : boolean) : number
-static calculateVulnerabilityScore(vulnerabilities : VulnerabilityType[]) : number
}
class SSLAnalyzer {
+analyzeSSL(domain : string) : Promise~SSLAnalysisResultType~
+supportsHTTPS(domain : string) : Promise~boolean~
+getGradeExplanation(grade : string) : string
-getCertificateInfo(domain : string) : Promise~CertificateInfoType~
-calculateSSLGrade(certificate : CertificateInfoType) : string
-checkSecurityHeaders(domain : string) : Promise~SecurityHeadersType~
-checkVulnerabilities(domain : string) : Promise~string[]~
}
class SecurityMetricsType {
+sslGrade? : string
+sslIssuer? : string
+sslExpiration? : Date
+usesHttps? : boolean
+securityHeaders? : SecurityHeadersType
+vulnerabilities? : VulnerabilityType[]
+certificateChain? : CertificateInfoType[]
}
class SecurityHeadersType {
+hsts? : boolean
+csp? : boolean
+xframe? : boolean
+xssProtection? : boolean
+contentTypeOptions? : boolean
+referrerPolicy? : boolean
+permissionsPolicy? : boolean
}
SecurityScorer --> SecurityMetricsType : "uses"
SSLAnalyzer --> SSLAnalysisResultType : "produces"
SecurityScorer ..> SSLAnalyzer : "depends on"
SecurityScorer --> SecurityHeadersType : "uses"
SecurityScorer --> VulnerabilityType : "uses"
SecurityScorer --> CertificateInfoType : "uses"
```

**Diagram sources**
- [SecurityScorer.ts](file://services/worker/src/ranking/scorers/SecurityScorer.ts#L1-L458)
- [SSLAnalyzer.ts](file://services/worker/src/crawler/analyzers/SSLAnalyzer.ts#L1-L365)

**Section sources**
- [SecurityScorer.ts](file://services/worker/src/ranking/scorers/SecurityScorer.ts#L1-L458)
- [SSLAnalyzer.ts](file://services/worker/src/crawler/analyzers/SSLAnalyzer.ts#L1-L365)

## SSL/TLS Configuration Evaluation

The SecurityScorer evaluates SSL/TLS configuration through multiple factors including certificate grade, validity period, issuer trustworthiness, and certificate chain completeness. The SSL grade is determined by the SSLAnalyzer based on certificate properties such as key size, signature algorithm, and expiration status. The system uses a predefined mapping of SSL grades to score values, with A+ receiving the highest score (1.0) and F or untrusted certificates receiving minimal scores.

Certificate validity is assessed by calculating days until expiration, with certificates expiring within 30 days receiving reduced scores and expired certificates receiving the lowest possible score. The system also evaluates the certificate issuer against a list of trusted certificate authorities (Let's Encrypt, DigiCert, Comodo, etc.), providing a small score bonus for certificates from trusted issuers. Additionally, the presence of a complete certificate chain contributes to the overall SSL score.

```mermaid
flowchart TD
Start([SSL Evaluation]) --> CheckGrade["Check SSL Grade"]
CheckGrade --> GradeValid{"Grade Valid?"}
GradeValid --> |No| AssignLowScore["Assign Low Score (0.1)"]
GradeValid --> |Yes| CalculateValidity["Calculate Validity Score"]
CalculateValidity --> CheckExpiration["Check Expiration Date"]
CheckExpiration --> DaysRemaining{"Days Until Expiry > 90?"}
DaysRemaining --> |Yes| HighValidity["High Validity Score (0.2)"]
DaysRemaining --> |30-90| MediumValidity["Medium Validity Score (0.15)"]
DaysRemaining --> |0-30| LowValidity["Low Validity Score (0.1)"]
DaysRemaining --> |Expired| NoValidity["No Validity Score"]
HighValidity --> CheckIssuer
MediumValidity --> CheckIssuer
LowValidity --> CheckIssuer
NoValidity --> CheckIssuer
CheckIssuer --> CheckTrusted["Check Trusted Issuer"]
CheckTrusted --> IsTrusted{"Trusted Issuer?"}
IsTrusted --> |Yes| AddIssuerBonus["Add Issuer Bonus (0.1)"]
IsTrusted --> |No| NoIssuerBonus
AddIssuerBonus --> CheckChain
NoIssuerBonus --> CheckChain
CheckChain --> CheckCertificateChain["Check Certificate Chain"]
CheckChain --> HasChain{"Chain Present?"}
HasChain --> |Yes| AddChainBonus["Add Chain Bonus (0.1)"]
HasChain --> |No| NoChainBonus
AddChainBonus --> CalculateTotal
NoChainBonus --> CalculateTotal
CalculateTotal --> CalculateSSLScore["Calculate Total SSL Score"]
CalculateSSLScore --> ReturnScore["Return SSL Score (≤1.0)"]
AssignLowScore --> ReturnScore
```

**Diagram sources**
- [SecurityScorer.ts](file://services/worker/src/ranking/scorers/SecurityScorer.ts#L150-L215)
- [SSLAnalyzer.ts](file://services/worker/src/crawler/analyzers/SSLAnalyzer.ts#L150-L190)

**Section sources**
- [SecurityScorer.ts](file://services/worker/src/ranking/scorers/SecurityScorer.ts#L150-L215)
- [SSLAnalyzer.ts](file://services/worker/src/crawler/analyzers/SSLAnalyzer.ts#L150-L190)

## Security Headers Assessment

The SecurityScorer evaluates seven key security headers that protect against common web vulnerabilities. Each header contributes to the overall security headers score, with HSTS (HTTP Strict Transport Security) being the most valuable component. The system checks for the presence of HSTS, CSP (Content Security Policy), X-Frame-Options, X-XSS-Protection, X-Content-Type-Options, Referrer-Policy, and Permissions-Policy headers.

HSTS receives the highest weight (0.25) as it prevents SSL stripping attacks and ensures all communications occur over HTTPS. CSP is the second most important header (0.20 weight) as it mitigates cross-site scripting (XSS) attacks by controlling which resources can be loaded. X-Frame-Options (0.15 weight) prevents clickjacking attacks, while X-XSS-Protection and X-Content-Type-Options (0.10 each) provide additional XSS and MIME-type sniffing protection. The system accumulates points for each present header, capping the total at 1.0.

```mermaid
flowchart TD
Start([Security Headers Evaluation]) --> CheckHSTS["Check HSTS Header"]
CheckHSTS --> HSTSPresent{"HSTS Present?"}
HSTSPresent --> |Yes| AddHSTSScore["Add 0.25 to Score"]
HSTSPresent --> |No| SkipHSTS
AddHSTSScore --> CheckCSP
SkipHSTS --> CheckCSP
CheckCSP --> CSPPresent{"CSP Present?"}
CSPPresent --> |Yes| AddCSPScore["Add 0.20 to Score"]
CSPPresent --> |No| SkipCSP
AddCSPScore --> CheckXFrame
SkipCSP --> CheckXFrame
CheckXFrame --> XFramePresent{"X-Frame-Options Present?"}
XFramePresent --> |Yes| AddXFrameScore["Add 0.15 to Score"]
XFramePresent --> |No| SkipXFrame
AddXFrameScore --> CheckXXSS
SkipXFrame --> CheckXXSS
CheckXXSS --> XXSSPresent{"X-XSS-Protection Present?"}
XXSSPresent --> |Yes| AddXXSSScore["Add 0.10 to Score"]
XXSSPresent --> |No| SkipXXSS
AddXXSSScore --> CheckContentType
SkipXXSS --> CheckContentType
CheckContentType --> ContentTypePresent{"X-Content-Type-Options Present?"}
ContentTypePresent --> |Yes| AddContentTypeScore["Add 0.10 to Score"]
ContentTypePresent --> |No| SkipContentType
AddContentTypeScore --> CheckReferrer
SkipContentType --> CheckReferrer
CheckReferrer --> ReferrerPresent{"Referrer-Policy Present?"}
ReferrerPresent --> |Yes| AddReferrerScore["Add 0.10 to Score"]
ReferrerPresent --> |No| SkipReferrer
AddReferrerScore --> CheckPermissions
SkipReferrer --> CheckPermissions
CheckPermissions --> PermissionsPresent{"Permissions-Policy Present?"}
PermissionsPresent --> |Yes| AddPermissionsScore["Add 0.10 to Score"]
PermissionsPresent --> |No| SkipPermissions
AddPermissionsScore --> CapScore
SkipPermissions --> CapScore
CapScore --> CapAtOne["Cap Score at 1.0"]
CapAtOne --> ReturnHeadersScore["Return Security Headers Score"]
```

**Diagram sources**
- [SecurityScorer.ts](file://services/worker/src/ranking/scorers/SecurityScorer.ts#L220-L270)

**Section sources**
- [SecurityScorer.ts](file://services/worker/src/ranking/scorers/SecurityScorer.ts#L220-L270)

## Vulnerability Assessment and Penalty Application

The SecurityScorer applies penalties for known security vulnerabilities based on their severity level. The system uses a penalty mapping where critical vulnerabilities incur the highest penalty (0.4), followed by high (0.2), medium (0.1), and low (0.05) severity vulnerabilities. The vulnerability score starts at 1.0 (perfect score) and decreases as penalties are applied for each identified vulnerability.

The system also applies an additional penalty of 0.1 if more than five vulnerabilities are detected, recognizing that a large number of vulnerabilities indicates systemic security issues. When no vulnerabilities are reported, the component returns a perfect score of 1.0. The vulnerability assessment is typically populated by external security scanning tools and vulnerability databases, allowing the SecurityScorer to focus on scoring rather than vulnerability detection.

```mermaid
flowchart TD
Start([Vulnerability Assessment]) --> CheckVulnerabilities["Check for Vulnerabilities"]
CheckVulnerabilities --> VulnerabilitiesExist{"Vulnerabilities Found?"}
VulnerabilitiesExist --> |No| PerfectScore["Return Perfect Score (1.0)"]
VulnerabilitiesExist --> |Yes| InitializeScore["Initialize Score = 1.0"]
InitializeScore --> ProcessEach["Process Each Vulnerability"]
ProcessEach --> GetSeverity["Get Vulnerability Severity"]
GetSeverity --> ApplyPenalty["Apply Penalty Based on Severity"]
ApplyPenalty --> UpdateScore["Update Score = Score - Penalty"]
UpdateScore --> MoreVulnerabilities{"More Vulnerabilities?"}
MoreVulnerabilities --> |Yes| ProcessEach
MoreVulnerabilities --> |No| CheckCount["Check Vulnerability Count"]
CheckCount --> CountGreaterThanFive{"Count > 5?"}
CountGreaterThanFive --> |Yes| ApplyAdditionalPenalty["Apply Additional Penalty (-0.1)"]
CountGreaterThanFive --> |No| FinalScore
ApplyAdditionalPenalty --> FinalScore
FinalScore --> EnsureNonNegative["Ensure Score ≥ 0.0"]
EnsureNonNegative --> ReturnVulnerabilityScore["Return Vulnerability Score"]
PerfectScore --> ReturnVulnerabilityScore
```

**Diagram sources**
- [SecurityScorer.ts](file://services/worker/src/ranking/scorers/SecurityScorer.ts#L290-L335)

**Section sources**
- [SecurityScorer.ts](file://services/worker/src/ranking/scorers/SecurityScorer.ts#L290-L335)

## Security Score Calculation Algorithm

The SecurityScorer uses a weighted scoring algorithm that combines four main components: SSL certificate quality (35% weight), security headers (25% weight), HTTPS usage (20% weight), and vulnerability status (20% weight). The final score is calculated by multiplying each component's score by its respective weight and summing the results, with the final value capped between 0.0 and 1.0.

The algorithm follows a systematic approach: first calculating individual component scores, then applying weights, summing the weighted scores, and finally rounding to three decimal places. Error handling is implemented to return a conservative fallback score of 0.3 if any errors occur during calculation, ensuring that the system always produces a valid security assessment even in exceptional circumstances.

```mermaid
sequenceDiagram
participant S as SecurityScorer
participant M as Metrics
participant L as Logger
S->>S : Initialize totalScore = 0
S->>S : Calculate SSLScore
S->>S : totalScore += SSLScore * 0.35
S->>S : Calculate HeadersScore
S->>S : totalScore += HeadersScore * 0.25
S->>S : Calculate HttpsScore
S->>S : totalScore += HttpsScore * 0.20
S->>S : Calculate VulnerabilityScore
S->>S : totalScore += VulnerabilityScore * 0.20
S->>S : Cap score between 0.0 and 1.0
S->>S : Round to 3 decimal places
alt Success
S->>L : Log debug information
S-->>S : Return final score
else Error
S->>L : Log error
S-->>S : Return fallback score (0.3)
end
```

**Diagram sources**
- [SecurityScorer.ts](file://services/worker/src/ranking/scorers/SecurityScorer.ts#L50-L145)

**Section sources**
- [SecurityScorer.ts](file://services/worker/src/ranking/scorers/SecurityScorer.ts#L50-L145)

## SSLAnalyzer Implementation Details

The SSLAnalyzer component is responsible for gathering SSL/TLS information and security header data from target domains. It establishes TLS connections to retrieve certificate information, performs HTTP requests to check security headers, and identifies potential vulnerabilities. The analyzer uses Node.js built-in tls and https modules to securely connect to domains and extract certificate details including issuer, subject, validity period, key size, and signature algorithm.

For security header assessment, the SSLAnalyzer makes HEAD requests to retrieve response headers, specifically looking for HSTS configuration including max-age and includeSubDomains directives. The component also checks for common SSL vulnerabilities such as weak key sizes (<2048 bits), SHA-1 signature algorithms, self-signed certificates, and missing security headers. All operations include timeout handling (10 seconds) and error recovery to ensure reliable analysis even for problematic domains.

```mermaid
classDiagram
class SSLAnalyzer {
+analyzeSSL(domain : string) : Promise~SSLAnalysisResultType~
+supportsHTTPS(domain : string) : Promise~boolean~
+getGradeExplanation(grade : string) : string
-getCertificateInfo(domain : string) : Promise~CertificateInfoType~
-calculateSSLGrade(certificate : CertificateInfoType) : string
-checkSecurityHeaders(domain : string) : Promise~SecurityHeadersType~
-checkVulnerabilities(domain : string) : Promise~string[]~
}
class SSLAnalysisResultType {
+domain : string
+hasSSL : boolean
+certificate? : CertificateInfoType
+grade : string
+protocols : string[]
+cipherSuites : string[]
+vulnerabilities : string[]
+securityHeaders : SecurityHeadersType
+lastAnalyzed : string
+error? : string
}
class CertificateInfoType {
+issuer : string
+subject : string
+validFrom : string
+validTo : string
+daysUntilExpiry : number
+serialNumber : string
+fingerprint : string
+keySize : number
+signatureAlgorithm : string
}
class SecurityHeadersType {
+hsts : boolean
+hstsMaxAge? : number
+hstsIncludeSubdomains? : boolean
}
SSLAnalyzer --> SSLAnalysisResultType : "produces"
SSLAnalyzer --> CertificateInfoType : "uses"
SSLAnalyzer --> SecurityHeadersType : "uses"
```

**Diagram sources**
- [SSLAnalyzer.ts](file://services/worker/src/crawler/analyzers/SSLAnalyzer.ts#L1-L365)

**Section sources**
- [SSLAnalyzer.ts](file://services/worker/src/crawler/analyzers/SSLAnalyzer.ts#L1-L365)

## Test Case Examples and Score Calculations

The test suite demonstrates various security configurations and their corresponding scores. For a domain with A+ SSL grade, valid certificate, trusted issuer, complete certificate chain, full security headers implementation, HTTPS usage, and no vulnerabilities, the SecurityScorer produces a score of approximately 0.9-1.0 (A+ grade). Conversely, a domain with no SSL certificate, HTTP-only access, missing security headers, and critical vulnerabilities receives a score close to 0.0 (F grade).

Intermediate cases show predictable score variations: a domain with B-grade SSL certificate and missing CSP header might score around 0.6-0.7 (C grade), while a domain with expired certificate but otherwise good security configuration would score very low due to the severe penalty for expired certificates. The tests verify that the scoring algorithm correctly handles edge cases such as missing data, invalid SSL grades, and large numbers of vulnerabilities while maintaining performance under various conditions.

```mermaid
flowchart LR
subgraph ExcellentSecurity[Excellent Security Configuration]
A[SSL Grade: A+]
B[All Security Headers Present]
C[HTTPS Enabled]
D[No Vulnerabilities]
E[Trusted Issuer]
F[Valid Certificate Chain]
end
subgraph PoorSecurity[Poor Security Configuration]
G[SSL Grade: F]
H[No Security Headers]
I[HTTP Only]
J[Critical Vulnerabilities]
K[Expired Certificate]
L[Self-Signed Certificate]
end
ExcellentSecurity --> |Score: 0.9-1.0| GradeA["Grade: A+"]
PoorSecurity --> |Score: 0.0-0.3| GradeF["Grade: F"]
subgraph Intermediate[Intermediate Security Configuration]
M[SSL Grade: B]
N[Missing CSP Header]
O[HTTPS Enabled]
P[Medium Severity Vulnerabilities]
end
Intermediate --> |Score: 0.5-0.7| GradeC["Grade: C/D"]
```

**Diagram sources**
- [SecurityScorer.test.ts](file://services/worker/src/ranking/__tests__/SecurityScorer.test.ts#L57-L394)

**Section sources**
- [SecurityScorer.test.ts](file://services/worker/src/ranking/__tests__/SecurityScorer.test.ts#L57-L394)

## Edge Case Handling

The SecurityScorer includes robust handling for various edge cases and incomplete data scenarios. When SSL information is missing or incomplete, the component assigns conservative scores that reflect the uncertainty. For example, if SSL grade information is unavailable but HTTPS usage is confirmed, the system still provides a moderate score based on the available HTTPS evidence.

The component gracefully handles invalid SSL grades by defaulting to a low score (0.1), preventing malformed data from causing calculation errors. Missing security headers objects are treated as having no security headers (minimal score of 0.1), while undefined vulnerability arrays are interpreted as having no known vulnerabilities (perfect score of 1.0). The system also handles certificate chain issues by checking the presence of the certificateChain array and providing appropriate scoring based on chain completeness.

```mermaid
flowchart TD
Start([Edge Case Handling]) --> CheckSSLGrade["Check SSL Grade Validity"]
CheckSSLGrade --> ValidGrade{"Valid Grade?"}
ValidGrade --> |Yes| UseGradeScore
ValidGrade --> |No| AssignDefaultSSL["Assign Default SSL Score (0.1)"]
UseGradeScore --> CheckHeaders["Check Security Headers Object"]
AssignDefaultSSL --> CheckHeaders
CheckHeaders --> HeadersExist{"Headers Object Exists?"}
HeadersExist --> |Yes| EvaluateHeaders
HeadersExist --> |No| AssignLowHeaders["Assign Low Headers Score (0.1)"]
EvaluateHeaders --> CheckVulnerabilities["Check Vulnerabilities Array"]
AssignLowHeaders --> CheckVulnerabilities
CheckVulnerabilities --> VulnerabilitiesDefined{"Vulnerabilities Defined?"}
VulnerabilitiesDefined --> |Yes| EvaluateVulnerabilities
VulnerabilitiesDefined --> |No| AssignPerfectVuln["Assign Perfect Vulnerability Score (1.0)"]
EvaluateVulnerabilities --> CheckHTTPS["Check HTTPS Usage"]
AssignPerfectVuln --> CheckHTTPS
EvaluateHeaders --> CheckHTTPS
UseGradeScore --> CheckHTTPS
CheckHTTPS --> HTTPSDefined{"HTTPS Usage Defined?"}
HTTPSDefined --> |Yes| UseHTTPSScore
HTTPSDefined --> |No| AssignMediumHTTPS["Assign Medium HTTPS Score (0.5)"]
UseHTTPSScore --> CalculateFinal
AssignMediumHTTPS --> CalculateFinal
CalculateFinal --> ApplyWeights["Apply Component Weights"]
ApplyWeights --> CapScore["Cap Final Score (0.0-1.0)"]
CapScore --> ReturnResult["Return Security Score"]
```

**Diagram sources**
- [SecurityScorer.ts](file://services/worker/src/ranking/scorers/SecurityScorer.ts#L340-L458)
- [SecurityScorer.test.ts](file://services/worker/src/ranking/__tests__/SecurityScorer.test.ts#L438-L525)

**Section sources**
- [SecurityScorer.ts](file://services/worker/src/ranking/scorers/SecurityScorer.ts#L340-L458)
- [SecurityScorer.test.ts](file://services/worker/src/ranking/__tests__/SecurityScorer.test.ts#L438-L525)

## Security Recommendations and Remediation

The SecurityScorer provides actionable recommendations through the getSecurityRecommendations method, which analyzes the security metrics and suggests specific improvements. For SSL/TLS issues, recommendations include upgrading to A/A+ grade certificates, renewing expiring certificates, and using certificates from trusted issuers. When security headers are missing, the system recommends implementing HSTS, CSP, X-Frame-Options, and other protective headers.

For domains not using HTTPS, the primary recommendation is enabling HTTPS for all pages. When vulnerabilities are detected, the system prioritizes remediation by severity, urging immediate action for critical vulnerabilities and addressing high-severity issues promptly. The recommendations are designed to be specific and actionable, helping domain owners understand exactly what needs to be fixed to improve their security posture and score.

```mermaid
flowchart TD
Start([Generate Recommendations]) --> CheckSSLGrade["Check SSL Grade"]
CheckSSLGrade --> PoorGrade{"Grade < B?"}
PoorGrade --> |Yes| RecommendUpgrade["Recommend: Upgrade SSL Certificate"]
PoorGrade --> |No| CheckExpiry
RecommendUpgrade --> CheckExpiry
CheckExpiry --> CheckExpiration["Check Certificate Expiry"]
CheckExpiry --> CheckExpiry
CheckExpiration --> ExpiringSoon{"Expires < 30 days?"}
ExpiringSoon --> |Yes| RecommendRenew["Recommend: Renew Certificate"]
ExpiringSoon --> |No| CheckHTTPS
RecommendRenew --> CheckHTTPS
CheckHTTPS --> CheckHTTPSUsage["Check HTTPS Usage"]
CheckHTTPS --> CheckHTTPSUsage
CheckHTTPSUsage --> HTTPOnly{"HTTP Only?"}
HTTPOnly --> |Yes| RecommendHTTPS["Recommend: Enable HTTPS"]
HTTPOnly --> |No| CheckHeaders
RecommendHTTPS --> CheckHeaders
CheckHeaders --> CheckHSTS["Check HSTS Header"]
CheckHeaders --> CheckHSTS
CheckHSTS --> HSTSMissing{"HSTS Missing?"}
HSTSMissing --> |Yes| RecommendHSTS["Recommend: Implement HSTS"]
HSTSMissing --> |No| CheckCSP
RecommendHSTS --> CheckCSP
CheckCSP --> CSPMissing{"CSP Missing?"}
CSPMissing --> |Yes| RecommendCSP["Recommend: Implement CSP"]
CSPMissing --> |No| CheckVulnerabilities
RecommendCSP --> CheckVulnerabilities
CheckVulnerabilities --> CheckCritical["Check Critical Vulnerabilities"]
CheckVulnerabilities --> CheckCritical
CheckCritical --> CriticalFound{"Critical Vulnerabilities?"}
CriticalFound --> |Yes| RecommendCritical["Recommend: Address Critical Vulnerabilities"]
CriticalFound --> |No| CheckHigh
RecommendCritical --> CompileList
CheckHigh --> CheckHighVulns["Check High Severity Vulnerabilities"]
CheckHigh --> CheckHighVulns
CheckHighVulns --> HighFound{"High Severity Found?"}
HighFound --> |Yes| RecommendHigh["Recommend: Address High Severity Vulnerabilities"]
HighFound --> |No| CompileList
RecommendHigh --> CompileList
CompileList --> ReturnRecommendations["Return Recommendations List"]
```

**Diagram sources**
- [SecurityScorer.ts](file://services/worker/src/ranking/scorers/SecurityScorer.ts#L400-L458)

**Section sources**
- [SecurityScorer.ts](file://services/worker/src/ranking/scorers/SecurityScorer.ts#L400-L458)

## Performance Considerations

The SecurityScorer is designed for high performance with scoring operations typically completing within 100 milliseconds. The algorithm uses efficient data structures and avoids unnecessary computations, making it suitable for processing large volumes of domains. The component handles large vulnerability lists efficiently, with tests confirming that even 100+ vulnerabilities can be processed within 200 milliseconds.

The system minimizes external dependencies in the scoring calculation itself, relying on pre-collected metrics rather than performing network operations during scoring. This separation of concerns allows the SSLAnalyzer to handle time-consuming network operations asynchronously, while the SecurityScorer focuses on fast, deterministic calculations. The implementation includes comprehensive error handling to prevent scoring failures from impacting overall system stability, with a conservative fallback score provided when exceptions occur.

**Section sources**
- [SecurityScorer.test.ts](file://services/worker/src/ranking/__tests__/SecurityScorer.test.ts#L479-L525)
- [SecurityScorer.ts](file://services/worker/src/ranking/scorers/SecurityScorer.ts#L100-L145)