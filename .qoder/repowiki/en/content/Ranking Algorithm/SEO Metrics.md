# SEO Metrics

<cite>
**Referenced Files in This Document**   
- [SEOScorer.ts](file://services/worker/src/ranking/scorers/SEOScorer.ts)
- [HomepageAnalyzer.ts](file://services/worker/src/crawler/analyzers/HomepageAnalyzer.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Core Components](#core-components)
3. [SEO Scoring Algorithm](#seo-scoring-algorithm)
4. [On-Page SEO Factor Analysis](#on-page-seo-factor-analysis)
5. [Integration Between Crawler and Scoring Service](#integration-between-crawler-and-scoring-service)
6. [Common SEO Issues and Impact](#common-seo-issues-and-impact)
7. [Best Practices for Improving SEO Scores](#best-practices-for-improving-seo-scores)
8. [Conclusion](#conclusion)

## Introduction
The SEOScorer component is responsible for evaluating the search engine optimization (SEO) quality of websites by analyzing various on-page and technical factors. It processes data collected from crawlers, particularly the HomepageAnalyzer, to generate a comprehensive SEO score between 0 and 100. This score reflects how well a website adheres to SEO best practices across multiple dimensions including meta tags, content quality, mobile-friendliness, and technical implementation. The system supports actionable insights through detailed breakdowns and recommendations, enabling users to improve their domain rankings.

## Core Components

The SEO analysis pipeline consists of two primary components: the **HomepageAnalyzer**, which collects raw SEO metrics from website content, and the **SEOScorer**, which computes a weighted score based on these metrics. The HomepageAnalyzer performs HTTP requests to extract metadata, content structure, and performance indicators, while the SEOScorer applies a multi-factor scoring algorithm to produce a final SEO evaluation.

**Section sources**
- [SEOScorer.ts](file://services/worker/src/ranking/scorers/SEOScorer.ts#L1-L800)
- [HomepageAnalyzer.ts](file://services/worker/src/crawler/analyzers/HomepageAnalyzer.ts#L1-L485)

## SEO Scoring Algorithm

The SEOScorer uses a weighted scoring model that evaluates six key SEO categories:

```mermaid
graph TD
A[SEO Score] --> B[Meta Tags 25%]
A --> C[Content Quality 20%]
A --> D[Technical SEO 20%]
A --> E[Robots & Sitemap 15%]
A --> F[Structured Data 10%]
A --> G[Social Signals 10%]
```

**Diagram sources**
- [SEOScorer.ts](file://services/worker/src/ranking/scorers/SEOScorer.ts#L105-L115)

Each category contributes to the total score according to its importance. The final score is normalized to a range of 0–1.0 and then scaled to a 0–100 point system. A fallback score of 0.5 is returned if an error occurs during calculation.

### Scoring Breakdown

| Category | Weight | Key Factors |
|--------|--------|-------------|
| Meta Tags | 25% | Title, description, canonical, Open Graph, Twitter Cards |
| Content Quality | 20% | Word count, heading structure, readability, image alt text |
| Technical SEO | 20% | HTTPS, mobile optimization, page speed, schema markup |
| Robots & Sitemap | 15% | robots.txt existence, sitemap validity, crawl allowance |
| Structured Data | 10% | Schema.org implementation, validation, multiple types |
| Social Signals | 10% | Open Graph, Twitter Cards, social shares, mentions |

**Section sources**
- [SEOScorer.ts](file://services/worker/src/ranking/scorers/SEOScorer.ts#L105-L115)

## On-Page SEO Factor Analysis

### Meta Tags Analysis
The SEOScorer evaluates meta tags such as `<title>`, `<meta name="description">`, and social meta tags (Open Graph, Twitter Cards). Optimal title length (30–60 characters) and description length (120–160 characters) provide bonus points. Missing or poorly formatted tags significantly reduce the score.

```mermaid
flowchart TD
Start([Meta Tags Analysis]) --> TitleCheck{"Title Present?"}
TitleCheck --> |Yes| TitleLength{"30-60 Chars?"}
TitleLength --> |Yes| AddTitleBonus["Add 0.1 Bonus"]
TitleLength --> |No| NoBonus
TitleCheck --> |No| MinimalScore["Score = 0.1"]
AddTitleBonus --> DescriptionCheck
NoBonus --> DescriptionCheck
DescriptionCheck{"Description Present?"}
DescriptionCheck --> |Yes| DescLength{"120-160 Chars?"}
DescLength --> |Yes| AddDescBonus["Add 0.1 Bonus"]
DescLength --> |No| NoDescBonus
DescriptionCheck --> |No| Continue
AddDescBonus --> CanonicalCheck
NoDescBonus --> CanonicalCheck
CanonicalCheck{"Canonical URL?"} --> |Yes| AddCanonical["Add 0.1"]
CanonicalCheck --> |No| SkipCanonical
```

**Diagram sources**
- [SEOScorer.ts](file://services/worker/src/ranking/scorers/SEOScorer.ts#L148-L210)

### Heading Structure and Content Quality
Content quality is assessed using word count thresholds: excellent (≥1500 words), good (≥800), minimum (≥300). The presence of exactly one H1 tag earns full points, while multiple H1s incur a penalty. Proper heading hierarchy (H1 → H2 → H3) improves the score. Image alt text coverage is also evaluated, with ≥90% coverage considered excellent.

```mermaid
flowchart TD
Start([Content Analysis]) --> WordCount{"Word Count ≥ 1500?"}
WordCount --> |Yes| ExcellentContent["Score += 0.25"]
WordCount --> |No| GoodCheck{"≥ 800?"}
GoodCheck --> |Yes| GoodContent["Score += 0.2"]
GoodCheck --> |No| MinCheck{"≥ 300?"}
MinCheck --> |Yes| MinContent["Score += 0.15"]
MinCheck --> |No| LowContent["Score += 0.1"]
ExcellentContent --> H1Check
GoodContent --> H1Check
MinContent --> H1Check
LowContent --> H1Check
H1Check{"Exactly One H1?"} --> |Yes| AddH1Score["Score += 0.1"]
H1Check --> |No| Penalty{"Multiple H1s?"}
Penalty --> |Yes| AddPartialH1["Score += 0.05"]
Penalty --> |No| NoH1Score
```

**Diagram sources**
- [SEOScorer.ts](file://services/worker/src/ranking/scorers/SEOScorer.ts#L212-L278)

### Mobile-Friendliness and Technical SEO
Mobile optimization is determined by the presence of a viewport meta tag and responsive design indicators. Page load speed is scored as follows: ≤3s (0.2), ≤5s (0.15), ≤8s (0.1). HTTPS enforcement adds 0.2 to the technical SEO score. Additional points are awarded for breadcrumbs, hreflang tags, and canonical URLs.

```mermaid
flowchart TD
Start([Technical SEO]) --> HTTPS{"HTTPS Enabled?"}
HTTPS --> |Yes| AddHTTPS["Score += 0.2"]
HTTPS --> |No| NoHTTPS
AddHTTPS --> Mobile{"Mobile Optimized?"}
Mobile --> |Yes| AddMobile["Score += 0.2"]
Mobile --> |No| NoMobile
AddMobile --> Speed{"Load Time ≤ 3s?"}
Speed --> |Yes| FastLoad["Score += 0.2"]
Speed --> |No| Moderate{"≤ 5s?"}
Moderate --> |Yes| ModLoad["Score += 0.15"]
Moderate --> |No| Slow{"≤ 8s?"}
Slow --> |Yes| SlowLoad["Score += 0.1"]
Slow --> |No| VerySlow
```

**Diagram sources**
- [SEOScorer.ts](file://services/worker/src/ranking/scorers/SEOScorer.ts#L280-L332)

## Integration Between Crawler and Scoring Service

The HomepageAnalyzer collects SEO metrics during the crawling process and passes them to the SEOScorer for evaluation. The crawler extracts meta tags, heading structure, image counts, and other content features, which are structured into a `SEOMetrics` object used by the scorer.

```mermaid
sequenceDiagram
participant Crawler as HomepageAnalyzer
participant Scorer as SEOScorer
participant Domain as Domain
Crawler->>Domain : HTTP GET (https : //domain.com)
Domain-->>Crawler : HTML Response
Crawler->>Crawler : Extract Meta Tags
Crawler->>Crawler : Analyze Headings
Crawler->>Crawler : Count Images & Links
Crawler->>Crawler : Detect Technologies
Crawler->>Scorer : calculateScore(SEOMetrics)
Scorer->>Scorer : Compute Weighted Score
Scorer-->>Crawler : Return SEO Score (0.0–1.0)
```

**Diagram sources**
- [HomepageAnalyzer.ts](file://services/worker/src/crawler/analyzers/HomepageAnalyzer.ts#L60-L480)
- [SEOScorer.ts](file://services/worker/src/ranking/scorers/SEOScorer.ts#L120-L145)

## Common SEO Issues and Impact

The SEOScorer identifies several common SEO issues that negatively affect rankings:

- **Missing title or description**: Results in minimal meta tags score (0.1)
- **Incorrect heading hierarchy**: Multiple H1 tags reduce content score
- **Lack of HTTPS**: Reduces technical SEO score by 0.2
- **No robots.txt or sitemap**: Eliminates 15% of potential score
- **Missing structured data**: Limits rich snippet eligibility
- **Poor image optimization**: Low alt text coverage reduces content quality score

These issues are reported via the `getSEORecommendations()` method, which provides actionable feedback such as "Add a descriptive title tag (30-60 characters)" or "Enable HTTPS for better security and SEO".

**Section sources**
- [SEOScorer.ts](file://services/worker/src/ranking/scorers/SEOScorer.ts#L678-L778)

## Best Practices for Improving SEO Scores

To achieve high SEO scores (A or A+), websites should follow these best practices:

1. **Meta Tags Optimization**
   - Use unique, descriptive titles (30–60 characters)
   - Write compelling meta descriptions (120–160 characters)
   - Include Open Graph and Twitter Card tags
   - Specify canonical URLs to avoid duplication

2. **Content Quality Enhancement**
   - Maintain at least 300 words per page (1500+ for excellence)
   - Use exactly one H1 tag and proper heading hierarchy
   - Add descriptive alt text to all images
   - Include internal links (≥3 recommended)

3. **Technical SEO Improvements**
   - Enable HTTPS with valid SSL certificate
   - Optimize for mobile devices (responsive design)
   - Improve page load speed (target <3 seconds)
   - Implement schema.org structured data

4. **Indexability and Discovery**
   - Create a valid robots.txt file
   - Submit an XML sitemap with >10 URLs
   - Ensure crawlers are not blocked

Following these practices can significantly improve both the SEO score and search engine visibility.

**Section sources**
- [SEOScorer.ts](file://services/worker/src/ranking/scorers/SEOScorer.ts#L678-L778)

## Conclusion
The SEOScorer component provides a comprehensive, weighted evaluation of on-page and technical SEO factors. By integrating with the HomepageAnalyzer, it transforms raw HTML analysis into actionable insights and a quantifiable SEO score. The system emphasizes best practices in meta tags, content quality, mobile-friendliness, and technical implementation, offering clear recommendations for improvement. This enables domain owners to systematically enhance their SEO performance and achieve better search engine rankings.