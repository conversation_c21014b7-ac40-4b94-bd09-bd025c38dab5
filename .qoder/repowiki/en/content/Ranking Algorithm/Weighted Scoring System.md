# Weighted Scoring System

<cite>
**Referenced Files in This Document**   
- [CompositeRanker.ts](file://services/worker/src/ranking/CompositeRanker.ts)
- [RankingCalculator.ts](file://services/worker/src/ranking/RankingCalculator.ts)
- [ranking-demo.ts](file://services/worker/src/ranking/examples/ranking-demo.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Weighted Scoring Architecture](#weighted-scoring-architecture)
3. [CompositeRanker Implementation](#compositeranker-implementation)
4. [Score Normalization and Aggregation](#score-normalization-and-aggregation)
5. [RankingCalculator and Batch Processing](#rankingcalculator-and-batch-processing)
6. [Configuration and Weight Management](#configuration-and-weight-management)
7. [Performance Optimization](#performance-optimization)
8. [Example Implementation](#example-implementation)
9. [Conclusion](#conclusion)

## Introduction

The domainr weighted scoring system provides a comprehensive framework for evaluating domain quality across multiple dimensions. This system combines performance, security, SEO, technical, and backlinks metrics into a unified composite score using predefined weights. The architecture is designed to be flexible, scalable, and transparent, allowing for configuration adjustments while maintaining ranking stability. This document explains the implementation details of the CompositeRanker and RankingCalculator components, their data flow, and optimization strategies for large-scale calculations.

## Weighted Scoring Architecture

The weighted scoring system follows a modular architecture where individual scorers evaluate specific dimensions, and the CompositeRanker aggregates these scores using weighted averaging. The system processes raw metrics from various sources, normalizes them to a common scale, applies configurable weights, and produces a final composite score. This approach enables comprehensive domain evaluation while maintaining flexibility in weight configuration and scoring methodology.

```mermaid
graph TD
A[Raw Metrics] --> B[Individual Scorers]
B --> C[Performance Score]
B --> D[Security Score]
B --> E[SEO Score]
B --> F[Technical Score]
B --> G[Backlinks Score]
C --> H[CompositeRanker]
D --> H
E --> H
F --> H
G --> H
H --> I[Weighted Aggregation]
I --> J[Composite Score]
J --> K[RankingCalculator]
K --> L[Final Rankings]
```

**Diagram sources**
- [CompositeRanker.ts](file://services/worker/src/ranking/CompositeRanker.ts#L0-L590)
- [RankingCalculator.ts](file://services/worker/src/ranking/RankingCalculator.ts#L0-L576)

**Section sources**
- [CompositeRanker.ts](file://services/worker/src/ranking/CompositeRanker.ts#L0-L590)
- [RankingCalculator.ts](file://services/worker/src/ranking/RankingCalculator.ts#L0-L576)

## CompositeRanker Implementation

The CompositeRanker class serves as the core component for calculating composite scores by combining multiple scoring dimensions with predefined weights. It implements a weighted averaging algorithm where each dimension contributes proportionally to the final score based on its assigned weight. The default weight configuration allocates 25% to performance, 20% to security, 20% to SEO, 15% to technical factors, and 20% to backlinks, ensuring a balanced evaluation across critical website quality aspects.

The implementation includes validation to ensure that all weights sum to 1.0, with warnings logged if the sum deviates significantly from this requirement. For domains with missing data in specific dimensions, the system applies conservative default scores: 0.5 for performance, SEO, and technical metrics, and 0.3 for security and backlinks, reflecting the higher risk associated with missing security information.

```mermaid
classDiagram
class CompositeRanker {
-weights : RankingWeightsType
+calculateCompositeScore(domainData : DomainDataType) : CompositeScoreType
+calculateGlobalRankings(domains : DomainDataType[]) : RankingResultType[]
+calculateCategoryRankings(domains : DomainDataType[], category : string) : RankingResultType[]
+updateWeights(newWeights : Partial<RankingWeightsType>) : void
+getWeights() : RankingWeightsType
}
class RankingWeightsType {
PERFORMANCE : number
SECURITY : number
SEO : number
TECHNICAL : number
BACKLINKS : number
}
class CompositeScoreType {
domain : string
overallScore : number
grade : string
scores : {
performance : number
security : number
seo : number
technical : number
backlinks : number
}
breakdown : {
performance : ScoreBreakdownType
security : ScoreBreakdownType
seo : ScoreBreakdownType
technical : ScoreBreakdownType
backlinks : ScoreBreakdownType
}
}
CompositeRanker --> RankingWeightsType : "uses"
CompositeRanker --> CompositeScoreType : "returns"
```

**Diagram sources**
- [CompositeRanker.ts](file://services/worker/src/ranking/CompositeRanker.ts#L0-L590)

**Section sources**
- [CompositeRanker.ts](file://services/worker/src/ranking/CompositeRanker.ts#L0-L590)

## Score Normalization and Aggregation

The scoring system implements a robust normalization and aggregation process to ensure consistent and fair evaluation across domains. Each individual scorer (PerformanceScorer, SecurityScorer, SEOScorer, TechnicalScorer, BacklinkScorer) converts raw metrics into normalized scores between 0.0 and 1.0, where 1.0 represents optimal performance. These normalized scores are then aggregated through weighted averaging, with each dimension's contribution determined by its assigned weight.

The aggregation formula is: `overallScore = (performanceScore × PERFORMANCE_WEIGHT) + (securityScore × SECURITY_WEIGHT) + (seoScore × SEO_WEIGHT) + (technicalScore × TECHNICAL_WEIGHT) + (backlinkScore × BACKLINKS_WEIGHT)`. The final score is rounded to three decimal places for consistency. The system also calculates contribution values for each dimension, showing how much each score contributes to the overall composite score based on both the raw score and its weight.

```mermaid
flowchart TD
A[Raw Performance Metrics] --> B[PerformanceScorer]
C[Raw Security Metrics] --> D[SecurityScorer]
E[Raw SEO Metrics] --> F[SEOScorer]
G[Raw Technical Metrics] --> H[TechnicalScorer]
I[Raw Backlink Metrics] --> J[BacklinkScorer]
B --> K[Normalized Performance Score 0.0-1.0]
D --> L[Normalized Security Score 0.0-1.0]
F --> M[Normalized SEO Score 0.0-1.0]
H --> N[Normalized Technical Score 0.0-1.0]
J --> O[Normalized Backlink Score 0.0-1.0]
K --> P[Weighted Aggregation]
L --> P
M --> P
N --> P
O --> P
P --> Q[Composite Score 0.0-1.0]
Q --> R[Grade Assignment A+-F]
```

**Diagram sources**
- [CompositeRanker.ts](file://services/worker/src/ranking/CompositeRanker.ts#L103-L151)
- [CompositeRanker.ts](file://services/worker/src/ranking/CompositeRanker.ts#L153-L185)

**Section sources**
- [CompositeRanker.ts](file://services/worker/src/ranking/CompositeRanker.ts#L103-L185)

## RankingCalculator and Batch Processing

The RankingCalculator class provides a higher-level interface for calculating domain rankings with additional features like validation, monitoring, and batch processing. It wraps the CompositeRanker functionality and adds quality control mechanisms, including input validation and performance monitoring. The calculator processes domains in configurable batches to manage memory usage and optimize performance during large-scale calculations.

The batch processing system divides domains into chunks (default size 100) and processes them sequentially, validating each domain's data before scoring. It maintains monitoring metrics such as calculation time, domains processed, average score, and error counts. After processing all batches, it sorts the results globally and reassigns ranks based on the final sorted order. This approach ensures efficient processing of large datasets while maintaining data integrity and providing detailed performance statistics.

```mermaid
sequenceDiagram
participant Client
participant RankingCalculator
participant CompositeRanker
participant Monitoring
Client->>RankingCalculator : calculateBatchRankings(domains)
RankingCalculator->>Monitoring : Log start time
loop For each batch
RankingCalculator->>RankingCalculator : Extract batch of domains
RankingCalculator->>RankingCalculator : Validate domain data
RankingCalculator->>CompositeRanker : calculateGlobalRankings(validDomains)
CompositeRanker-->>RankingCalculator : Return batch rankings
RankingCalculator->>Monitoring : Update metrics
end
RankingCalculator->>RankingCalculator : Sort all rankings globally
RankingCalculator->>RankingCalculator : Reassign final ranks
RankingCalculator->>Monitoring : Update final metrics
RankingCalculator-->>Client : Return final rankings
```

**Diagram sources**
- [RankingCalculator.ts](file://services/worker/src/ranking/RankingCalculator.ts#L128-L250)

**Section sources**
- [RankingCalculator.ts](file://services/worker/src/ranking/RankingCalculator.ts#L68-L250)

## Configuration and Weight Management

The weighted scoring system supports flexible configuration through the RankingCalculator and CompositeRanker classes. Weights can be customized during initialization or updated dynamically using the updateWeights method. The default weight configuration (25% performance, 20% security, 20% SEO, 15% technical, 20% backlinks) can be overridden by passing a custom weights object to the constructor.

The system validates that all weights sum to 1.0, logging warnings if the sum deviates significantly from this requirement. This validation helps maintain the mathematical integrity of the weighted averaging process. Configuration options also include batch size for processing, validation enablement, and monitoring settings, allowing administrators to optimize the system for their specific use case and infrastructure constraints.

```mermaid
classDiagram
class RankingConfigurationType {
weights? : RankingWeightsType
enableValidation? : boolean
enableMonitoring? : boolean
batchSize? : number
}
class RankingCalculator {
-config : RankingConfigurationType
-ranker : CompositeRanker
+updateWeights(newWeights : Partial<RankingWeightsType>) : void
+getWeights() : RankingWeightsType
}
class CompositeRanker {
-weights : RankingWeightsType
+updateWeights(newWeights : Partial<RankingWeightsType>) : void
+getWeights() : RankingWeightsType
}
RankingConfigurationType --> RankingCalculator : "configures"
RankingCalculator --> CompositeRanker : "delegates"
```

**Diagram sources**
- [RankingCalculator.ts](file://services/worker/src/ranking/RankingCalculator.ts#L50-L67)
- [CompositeRanker.ts](file://services/worker/src/ranking/CompositeRanker.ts#L424-L450)

**Section sources**
- [RankingCalculator.ts](file://services/worker/src/ranking/RankingCalculator.ts#L50-L67)
- [CompositeRanker.ts](file://services/worker/src/ranking/CompositeRanker.ts#L424-L450)

## Performance Optimization

The weighted scoring system incorporates several performance optimization strategies for efficient large-scale calculations. The batch processing approach limits memory usage by processing domains in chunks rather than loading all data into memory simultaneously. The system also includes monitoring metrics that track calculation time, domains processed, and error rates, enabling administrators to identify and address performance bottlenecks.

For large datasets, the system can be configured with optimal batch sizes based on available memory and processing power. The validation step helps prevent processing invalid data, reducing wasted computational resources. Additionally, the modular design allows for potential future enhancements such as parallel processing of batches or distributed computing across multiple nodes, further improving scalability for very large domain lists.

**Section sources**
- [RankingCalculator.ts](file://services/worker/src/ranking/RankingCalculator.ts#L252-L350)

## Example Implementation

The ranking-demo.ts file provides comprehensive examples of the weighted scoring system in action. It demonstrates basic ranking calculations with custom weights, batch processing of multiple domains, ranking with history tracking, and advanced ranking management with quality control. The examples show how to create a CompositeRanker instance, provide domain data with various metrics, and obtain detailed scoring breakdowns.

One example demonstrates weight optimization and A/B testing by applying different weight configurations to the same domain and comparing the resulting scores. This illustrates how changing the relative importance of different dimensions affects the final composite score. Another example shows ranking with history tracking, where previous rankings are used to calculate rank changes, providing insights into domain performance trends over time.

```mermaid
flowchart TD
A[Create CompositeRanker] --> B[Prepare Domain Data]
B --> C[Calculate Composite Score]
C --> D[Generate Score Breakdown]
D --> E[Display Results]
F[Create Multiple Weight Configs] --> G[Test Each Configuration]
G --> H[Compare Results]
H --> I[Analyze Impact]
J[Calculate Current Rankings] --> K[Compare with Previous]
K --> L[Calculate Rank Changes]
L --> M[Display Trend Analysis]
```

**Diagram sources**
- [ranking-demo.ts](file://services/worker/src/ranking/examples/ranking-demo.ts#L0-L573)

**Section sources**
- [ranking-demo.ts](file://services/worker/src/ranking/examples/ranking-demo.ts#L0-L573)

## Conclusion

The domainr weighted scoring system provides a robust, flexible framework for evaluating domain quality across multiple dimensions. By combining performance, security, SEO, technical, and backlink metrics with configurable weights, it delivers comprehensive insights into website quality. The system's modular architecture, with the CompositeRanker handling score aggregation and the RankingCalculator managing batch processing and quality control, ensures both accuracy and scalability. The implementation supports dynamic weight adjustments, detailed score breakdowns, and performance optimization for large-scale calculations, making it suitable for both individual domain analysis and extensive ranking operations.