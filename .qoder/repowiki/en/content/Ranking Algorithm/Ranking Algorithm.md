# Ranking Algorithm

<cite>
**Referenced Files in This Document**   
- [CompositeRanker.ts](file://services/worker/src/ranking/CompositeRanker.ts)
- [RankingCalculator.ts](file://services/worker/src/ranking/RankingCalculator.ts)
- [RankingUpdateService.ts](file://services/worker/src/ranking/RankingUpdateService.ts)
- [PerformanceScorer.ts](file://services/worker/src/ranking/scorers/PerformanceScorer.ts)
- [SecurityScorer.ts](file://services/worker/src/ranking/scorers/SecurityScorer.ts)
- [SEOScorer.ts](file://services/worker/src/ranking/scorers/SEOScorer.ts)
- [TechnicalScorer.ts](file://services/worker/src/ranking/scorers/TechnicalScorer.ts)
- [BacklinkScorer.ts](file://services/worker/src/ranking/scorers/BacklinkScorer.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Weighted Scoring System](#weighted-scoring-system)
3. [Core Components](#core-components)
4. [CompositeRanker Implementation](#compositeranker-implementation)
5. [RankingCalculator Implementation](#rankingcalculator-implementation)
6. [RankingUpdateService Implementation](#rankingupdateservice-implementation)
7. [Scoring Algorithm Details](#scoring-algorithm-details)
8. [Data Flow and Processing](#data-flow-and-processing)
9. [Performance Considerations](#performance-considerations)
10. [Practical Examples](#practical-examples)
11. [Conclusion](#conclusion)

## Introduction
The domainr ranking algorithm evaluates domains based on a weighted scoring system that considers five key dimensions: performance (25%), security (20%), SEO (20%), technical (15%), and backlinks (20%). This comprehensive approach provides a holistic assessment of domain quality, enabling accurate ranking and comparison across millions of domains. The system is designed to be both precise and scalable, with components that handle real-time updates, batch processing, and historical trend analysis.

The ranking system is implemented through three primary components: CompositeRanker, RankingCalculator, and RankingUpdateService. These components work together to calculate scores, validate data, and manage ranking updates across the domain database. The architecture supports both global rankings and category-specific rankings, with mechanisms for tracking rank changes over time.

**Section sources**
- [CompositeRanker.ts](file://services/worker/src/ranking/CompositeRanker.ts#L106-L575)
- [RankingCalculator.ts](file://services/worker/src/ranking/RankingCalculator.ts#L1-L576)
- [RankingUpdateService.ts](file://services/worker/src/ranking/RankingUpdateService.ts#L1-L1357)

## Weighted Scoring System
The domainr ranking algorithm employs a weighted scoring system that evaluates domains across five key dimensions:

- **Performance (25%)**: Measures website speed, responsiveness, and user experience metrics including load time, Core Web Vitals, and resource efficiency
- **Security (20%)**: Assesses SSL/TLS implementation, security headers, vulnerability exposure, and certificate validity
- **SEO (20%)**: Evaluates search engine optimization factors such as meta tags, content quality, mobile-friendliness, and structured data
- **Technical (15%)**: Analyzes technical infrastructure including DNS performance, uptime, server response times, and technology stack
- **Backlinks (20%)**: Quantifies the quality and quantity of inbound links, domain authority, and link profile diversity

The default weights are configured to sum to 1.0 (100%) and can be dynamically adjusted through the system's configuration. The CompositeRanker validates that weights sum to 1.0 within a small tolerance (0.001), issuing warnings if the sum deviates significantly. This weighted approach ensures that critical factors like performance and security have appropriate influence on the final ranking while maintaining flexibility for different use cases and business requirements.

**Section sources**
- [CompositeRanker.ts](file://services/worker/src/ranking/CompositeRanker.ts#L106-L115)

## Core Components
The ranking system consists of three main components that work together to calculate, validate, and update domain rankings:

- **CompositeRanker**: The core algorithm that calculates composite scores by combining individual dimension scores according to their weights
- **RankingCalculator**: The primary interface for calculating rankings with additional features like validation, monitoring, and batch processing
- **RankingUpdateService**: Manages ranking updates, batch processing, and history tracking through a job queue system

These components follow a layered architecture where the CompositeRanker handles the fundamental scoring logic, the RankingCalculator adds validation and monitoring capabilities, and the RankingUpdateService manages asynchronous processing and persistence. This separation of concerns allows each component to focus on its specific responsibilities while maintaining loose coupling between them.

**Section sources**
- [CompositeRanker.ts](file://services/worker/src/ranking/CompositeRanker.ts#L106-L575)
- [RankingCalculator.ts](file://services/worker/src/ranking/RankingCalculator.ts#L1-L576)
- [RankingUpdateService.ts](file://services/worker/src/ranking/RankingUpdateService.ts#L1-L1357)

## CompositeRanker Implementation
The CompositeRanker is the core component responsible for calculating domain rankings based on the weighted scoring system. It combines individual scores from five dimensions (performance, security, SEO, technical, and backlinks) into a single composite score.

```mermaid
classDiagram
class CompositeRanker {
+calculateCompositeScore(domainData) CompositeScoreType
+calculateGlobalRankings(domains) RankingResultType[]
+calculateCategoryRankings(domains, category) RankingResultType[]
+getWeights() RankingWeightsType
+updateWeights(newWeights) void
+getRankingStatistics(rankings) RankingStatisticsType
}
class RankingCalculator {
+calculateDomainScore(domainData) Promise~CompositeScoreType~
+calculateBatchRankings(domains) Promise~RankingResultType[]~
+calculateCategoryRankings(domains, category) Promise~RankingResultType[]~
}
class RankingUpdateService {
+triggerDomainRankingUpdate(domain) Promise~string~
+triggerBatchRankingUpdate(domains) Promise~string~
+getCurrentRankings(category) Promise~RankingResultType[]~
}
CompositeRanker --> PerformanceScorer : "uses"
CompositeRanker --> SecurityScorer : "uses"
CompositeRanker --> SEOScorer : "uses"
CompositeRanker --> TechnicalScorer : "uses"
CompositeRanker --> BacklinkScorer : "uses"
RankingCalculator --> CompositeRanker : "delegates"
RankingUpdateService --> CompositeRanker : "delegates"
```

**Diagram sources**
- [CompositeRanker.ts](file://services/worker/src/ranking/CompositeRanker.ts#L106-L575)
- [RankingCalculator.ts](file://services/worker/src/ranking/RankingCalculator.ts#L1-L576)
- [RankingUpdateService.ts](file://services/worker/src/ranking/RankingUpdateService.ts#L1-L1357)

**Section sources**
- [CompositeRanker.ts](file://services/worker/src/ranking/CompositeRanker.ts#L106-L575)

## RankingCalculator Implementation
The RankingCalculator serves as the main interface for calculating domain rankings, providing additional features beyond the basic scoring functionality of the CompositeRanker. It includes validation, monitoring, and batch processing capabilities.

```mermaid
sequenceDiagram
participant Client
participant RankingCalculator
participant CompositeRanker
participant Database
Client->>RankingCalculator : calculateBatchRankings(domains)
RankingCalculator->>RankingCalculator : validateDomainData()
loop For each batch
RankingCalculator->>CompositeRanker : calculateGlobalRankings(validDomains)
CompositeRanker->>PerformanceScorer : calculateScore()
CompositeRanker->>SecurityScorer : calculateScore()
CompositeRanker->>SEOScorer : calculateScore()
CompositeRanker->>TechnicalScorer : calculateScore()
CompositeRanker->>BacklinkScorer : calculateScore()
CompositeRanker-->>RankingCalculator : CompositeScoreType
end
RankingCalculator->>RankingCalculator : updateMonitoringMetrics()
RankingCalculator-->>Client : RankingResultType[]
```

**Diagram sources**
- [RankingCalculator.ts](file://services/worker/src/ranking/RankingCalculator.ts#L1-L576)

**Section sources**
- [RankingCalculator.ts](file://services/worker/src/ranking/RankingCalculator.ts#L1-L576)

## RankingUpdateService Implementation
The RankingUpdateService manages asynchronous ranking updates, batch processing, and history tracking through a job queue system. It handles both single domain updates and batch operations, ensuring that ranking calculations do not impact system performance during peak usage.

```mermaid
flowchart TD
A[Ranking Update Trigger] --> B{Trigger Type}
B --> |Single Domain| C[Job Queue: ranking:update]
B --> |Batch Update| D[Job Queue: ranking:batch]
B --> |History Update| E[Job Queue: ranking:history]
C --> F[handleSingleRankingUpdate]
D --> G[handleBatchRankingUpdate]
E --> H[handleRankingHistoryUpdate]
F --> I[fetchDomainData]
G --> J[fetchDomainData for each domain]
I --> K[calculateCompositeScore]
J --> K
K --> L[storeDomainRanking]
K --> M[updateRankingHistory]
L --> N[Trigger Related Updates]
M --> N
N --> O[Manticore Sync]
N --> P[Delayed Category Update]
N --> Q[Ranking History Analysis]
```

**Diagram sources**
- [RankingUpdateService.ts](file://services/worker/src/ranking/RankingUpdateService.ts#L1-L1357)

**Section sources**
- [RankingUpdateService.ts](file://services/worker/src/ranking/RankingUpdateService.ts#L1-L1357)

## Scoring Algorithm Details
The scoring algorithm calculates composite scores by combining individual dimension scores according to their weights. Each dimension has its own scorer class that implements specific logic for that domain quality aspect.

```mermaid
graph TB
A[Domain Data] --> B[Performance Score]
A --> C[Security Score]
A --> D[SEO Score]
A --> E[Technical Score]
A --> F[Backlink Score]
B --> G[Composite Score]
C --> G
D --> G
E --> G
F --> G
G --> H[Overall Grade]
subgraph "Performance Scoring"
B --> I[Response Time]
B --> J[Core Web Vitals]
B --> K[Page Size]
B --> L[Resource Efficiency]
end
subgraph "Security Scoring"
C --> M[SSL/TLS]
C --> N[Security Headers]
C --> O[Vulnerabilities]
C --> P[Certificate Validity]
end
subgraph "SEO Scoring"
D --> Q[Meta Tags]
D --> R[Content Quality]
D --> S[Mobile-Friendliness]
D --> T[Structured Data]
end
subgraph "Technical Scoring"
E --> U[DNS Performance]
E --> V[Uptime]
E --> W[Server Response]
E --> X[Technology Stack]
end
subgraph "Backlink Scoring"
F --> Y[Link Quantity]
F --> Z[Link Quality]
F --> AA[Domain Authority]
F --> AB[Link Diversity]
end
```

**Diagram sources**
- [CompositeRanker.ts](file://services/worker/src/ranking/CompositeRanker.ts#L106-L575)
- [PerformanceScorer.ts](file://services/worker/src/ranking/scorers/PerformanceScorer.ts#L1-L446)
- [SecurityScorer.ts](file://services/worker/src/ranking/scorers/SecurityScorer.ts)
- [SEOScorer.ts](file://services/worker/src/ranking/scorers/SEOScorer.ts)
- [TechnicalScorer.ts](file://services/worker/src/ranking/scorers/TechnicalScorer.ts)
- [BacklinkScorer.ts](file://services/worker/src/ranking/scorers/BacklinkScorer.ts)

**Section sources**
- [CompositeRanker.ts](file://services/worker/src/ranking/CompositeRanker.ts#L106-L575)
- [PerformanceScorer.ts](file://services/worker/src/ranking/scorers/PerformanceScorer.ts#L1-L446)

## Data Flow and Processing
The ranking system processes domain data through a well-defined flow that ensures accuracy, consistency, and scalability. The data flow begins with raw domain data collection and ends with updated rankings stored in the database.

```mermaid
flowchart LR
A[Raw Domain Data] --> B[Data Validation]
B --> C{Data Complete?}
C --> |Yes| D[Calculate Individual Scores]
C --> |No| E[Apply Default Scores]
D --> F[Weighted Composite Score]
F --> G[Rank Assignment]
G --> H[Store Rankings]
H --> I[Update Ranking History]
I --> J[Trigger Related Updates]
J --> K[Manticore Sync]
J --> L[Category Recalculation]
J --> M[Trend Analysis]
style C fill:#f9f,stroke:#333,stroke-width:2px
style E fill:#f96,stroke:#333,stroke-width:2px
```

**Diagram sources**
- [RankingUpdateService.ts](file://services/worker/src/ranking/RankingUpdateService.ts#L1-L1357)
- [RankingCalculator.ts](file://services/worker/src/ranking/RankingCalculator.ts#L1-L576)

**Section sources**
- [RankingUpdateService.ts](file://services/worker/src/ranking/RankingUpdateService.ts#L1-L1357)
- [RankingCalculator.ts](file://services/worker/src/ranking/RankingCalculator.ts#L1-L576)

## Performance Considerations
The ranking system is designed with performance and scalability in mind, incorporating several optimization strategies for handling large-scale ranking calculations:

- **Batch Processing**: Domains are processed in batches (default size: 100) to manage memory usage and improve processing efficiency
- **Asynchronous Operations**: Ranking updates are handled through a job queue system to prevent blocking of critical application processes
- **Caching**: Trend analysis results are cached in Redis for 24 hours to reduce database load and improve response times
- **Database Optimization**: ScyllaDB is used for ranking storage with optimized queries and indexing for fast retrieval
- **Concurrent Processing**: Job consumers are configured with appropriate concurrency levels (3 for single updates, 1 for batch, 2 for history)
- **Scheduled Updates**: Global and category rankings are recalculated on scheduled intervals (daily and weekly) to balance freshness with resource usage

The system also includes comprehensive monitoring metrics that track calculation time, domains processed, average scores, and error rates, enabling performance analysis and optimization.

**Section sources**
- [RankingCalculator.ts](file://services/worker/src/ranking/RankingCalculator.ts#L1-L576)
- [RankingUpdateService.ts](file://services/worker/src/ranking/RankingUpdateService.ts#L1-L1357)

## Practical Examples
The ranking algorithm can be used in various practical scenarios to evaluate and compare domain quality:

**Example 1: Single Domain Ranking**
```typescript
const calculator = new RankingCalculator();
const domainData = {
  domain: "example.com",
  performanceMetrics: { loadTime: 1500, pageSize: 1.2 * 1024 * 1024 },
  securityMetrics: { sslValid: true, hsts: true },
  seoMetrics: { metaTags: true, mobileFriendly: true },
  technicalMetrics: { uptime: 99.9, dnsResponseTime: 50 },
  backlinkMetrics: { totalBacklinks: 1500, authority: 75 }
};

const score = await calculator.calculateDomainScore(domainData);
// Returns composite score with breakdown by dimension
```

**Example 2: Batch Ranking Update**
```typescript
const updateService = new RankingUpdateService(dbManager, jobQueue);
const batchId = await updateService.triggerBatchRankingUpdate(
  ["example1.com", "example2.com", "example3.com"],
  "global",
  undefined,
  "medium"
);
// Returns batch ID for tracking processing status
```

**Example 3: Category Ranking with History**
```typescript
const rankings = await updateService.getCurrentRankings("technology");
const history = await updateService.getRankingHistory("example.com", 30);
const trend = await updateService.calculateRankingTrend(
  "example.com", 
  "global", 
  "30d"
);
// Returns current rankings, historical data, and trend analysis
```

**Section sources**
- [RankingCalculator.ts](file://services/worker/src/ranking/RankingCalculator.ts#L1-L576)
- [RankingUpdateService.ts](file://services/worker/src/ranking/RankingUpdateService.ts#L1-L1357)

## Conclusion
The domainr ranking algorithm provides a comprehensive and flexible system for evaluating domain quality across multiple dimensions. The weighted scoring system (performance 25%, security 20%, SEO 20%, technical 15%, backlinks 20%) ensures a balanced assessment that reflects real-world domain quality factors. The implementation through CompositeRanker, RankingCalculator, and RankingUpdateService components offers a robust architecture that supports both real-time and batch processing of rankings.

The system's design emphasizes scalability, with features like batch processing, asynchronous job queues, and caching that enable efficient handling of large domain datasets. The modular architecture allows for easy updates to scoring weights and algorithms without disrupting the overall system. With comprehensive monitoring and error handling, the ranking system provides reliable and accurate domain evaluations that can be used for various applications including domain marketplace listings, SEO analysis, and competitive intelligence.