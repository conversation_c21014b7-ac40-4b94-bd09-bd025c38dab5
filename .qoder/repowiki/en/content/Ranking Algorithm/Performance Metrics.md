# Performance Metrics

<cite>
**Referenced Files in This Document**   
- [PerformanceScorer.ts](file://services/worker/src/ranking/scorers/PerformanceScorer.ts)
- [PerformanceScorer.test.ts](file://services/worker/src/ranking/__tests__/PerformanceScorer.test.ts)
- [PerformanceAuditor.ts](file://services/worker/src/crawler/analyzers/PerformanceAuditor.ts)
- [PerformanceAuditor.test.ts](file://services/worker/src/crawler/analyzers/__tests__/PerformanceAuditor.test.ts)
- [DomainCrawlJob.ts](file://shared/src/models/DomainCrawlJob.ts)
- [DomainAnalysis.ts](file://shared/src/models/DomainAnalysis.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Performance Scoring System Overview](#performance-scoring-system-overview)
3. [PerformanceAuditor: Data Collection](#performanceauditor-data-collection)
4. [PerformanceScorer: Scoring Algorithm](#performancescorer-scoring-algorithm)
5. [Weight Distribution and Metric Thresholds](#weight-distribution-and-metric-thresholds)
6. [Score Calculation Examples](#score-calculation-examples)
7. [Crawler Integration and Data Flow](#crawler-integration-and-data-flow)
8. [Performance Bottlenecks and Optimization Strategies](#performance-bottlenecks-and-optimization-strategies)
9. [Conclusion](#conclusion)

## Introduction
The PerformanceScorer component is a critical part of the domain analysis system, responsible for transforming raw website performance metrics into a normalized score between 0 and 100. This document details how performance data is collected by the PerformanceAuditor, processed by the PerformanceScorer, and ultimately used to evaluate website quality. The system leverages Core Web Vitals and additional performance metrics to provide a comprehensive assessment of website performance, enabling accurate domain ranking and optimization recommendations.

## Performance Scoring System Overview
The performance scoring system consists of two primary components: the PerformanceAuditor, which collects performance metrics, and the PerformanceScorer, which transforms these metrics into a normalized score. The system is designed to provide a comprehensive evaluation of website performance by measuring key indicators such as load time, resource size, and Core Web Vitals. The scoring algorithm uses a weighted approach to balance different aspects of performance, ensuring that critical user experience metrics receive appropriate emphasis. The final score is normalized to a 0-100 scale, with corresponding performance grades from A+ to F.

```mermaid
graph TD
A[Website] --> B[PerformanceAuditor]
B --> C[Raw Performance Metrics]
C --> D[PerformanceScorer]
D --> E[Normalized Score 0-100]
D --> F[Performance Grade A+-F]
D --> G[Optimization Recommendations]
E --> H[Domain Ranking System]
```

**Diagram sources**
- [PerformanceAuditor.ts](file://services/worker/src/crawler/analyzers/PerformanceAuditor.ts#L1-L50)
- [PerformanceScorer.ts](file://services/worker/src/ranking/scorers/PerformanceScorer.ts#L1-L50)

**Section sources**
- [PerformanceAuditor.ts](file://services/worker/src/crawler/analyzers/PerformanceAuditor.ts#L1-L100)
- [PerformanceScorer.ts](file://services/worker/src/ranking/scorers/PerformanceScorer.ts#L1-L100)

## PerformanceAuditor: Data Collection
The PerformanceAuditor component is responsible for collecting comprehensive performance metrics from websites using browser automation. It leverages the browserless service to execute JavaScript in a real browser environment, allowing it to access browser performance APIs. The auditor measures Core Web Vitals including Largest Contentful Paint (LCP), First Input Delay (FID), and Cumulative Layout Shift (CLS), along with additional metrics such as load time, first contentful paint, speed index, page size, and resource count. The data collection process includes network throttling to ensure consistent and realistic measurements across different websites.

```mermaid
sequenceDiagram
participant Auditor as PerformanceAuditor
participant Browserless as Browserless Service
participant Website as Target Website
Auditor->>Browserless : Start performance audit
Browserless->>Website : Navigate to URL with throttling
Website-->>Browserless : Page loads with resources
Browserless->>Website : Execute performance measurement script
Website->>Browserless : Return performance metrics
Browserless-->>Auditor : Return Core Web Vitals and resource data
Auditor->>Auditor : Calculate initial performance score
Auditor->>Auditor : Generate optimization recommendations
Auditor-->>System : Return comprehensive audit result
```

**Diagram sources**
- [PerformanceAuditor.ts](file://services/worker/src/crawler/analyzers/PerformanceAuditor.ts#L150-L300)
- [PerformanceAuditor.test.ts](file://services/worker/src/crawler/analyzers/__tests__/PerformanceAuditor.test.ts#L10-L50)

**Section sources**
- [PerformanceAuditor.ts](file://services/worker/src/crawler/analyzers/PerformanceAuditor.ts#L1-L400)
- [PerformanceAuditor.test.ts](file://services/worker/src/crawler/analyzers/__tests__/PerformanceAuditor.test.ts#L1-L100)

## PerformanceScorer: Scoring Algorithm
The PerformanceScorer implements a sophisticated algorithm that transforms raw performance metrics into a normalized score. The algorithm uses a weighted combination of four primary components: response time (25% weight), Core Web Vitals (45% weight), page size (15% weight), and resource efficiency (15% weight). Each component is scored individually on a 0-1 scale and then combined according to its weight. The system includes fallback mechanisms for cases where Core Web Vitals data is unavailable, using basic performance metrics to calculate a reasonable score. The final score is capped between 0 and 1, then multiplied by 100 to create the 0-100 scale.

```mermaid
flowchart TD
A[Raw Metrics] --> B{Core Web Vitals Available?}
B --> |Yes| C[Calculate CWV Score]
B --> |No| D[Calculate Basic Performance Score]
C --> E[Calculate Response Time Score]
D --> E
E --> F[Calculate Page Size Score]
F --> G[Calculate Resource Efficiency Score]
G --> H[Apply Weights: Response Time 25%, CWV 45%, Page Size 15%, Resource Efficiency 15%]
H --> I[Sum Weighted Scores]
I --> J[Cap between 0.0 and 1.0]
J --> K[Multiply by 100 for 0-100 scale]
K --> L[Final Performance Score]
```

**Diagram sources**
- [PerformanceScorer.ts](file://services/worker/src/ranking/scorers/PerformanceScorer.ts#L100-L200)
- [PerformanceScorer.test.ts](file://services/worker/src/ranking/__tests__/PerformanceScorer.test.ts#L10-L50)

**Section sources**
- [PerformanceScorer.ts](file://services/worker/src/ranking/scorers/PerformanceScorer.ts#L1-L300)
- [PerformanceScorer.test.ts](file://services/worker/src/ranking/__tests__/PerformanceScorer.test.ts#L1-L100)

## Weight Distribution and Metric Thresholds
The performance scoring algorithm uses specific thresholds and weight distributions to evaluate different aspects of website performance. Response time is evaluated with excellent performance below 1 second, good performance below 2 seconds, and poor performance above 5 seconds. Core Web Vitals are assessed individually with LCP (Largest Contentful Paint) considered good at 2.5 seconds or less, FID (First Input Delay) good at 100 milliseconds or less, and CLS (Cumulative Layout Shift) good at 0.1 or less. Page size is evaluated with excellent performance below 1MB, good below 3MB, and poor above 10MB. Resource efficiency considers both the total number of resources and the average size per resource.

```mermaid
erDiagram
PERFORMANCE_METRICS {
float loadTime
float firstContentfulPaint
float largestContentfulPaint
float cumulativeLayoutShift
float firstInputDelay
float speedIndex
float responseTime
float pageSize
int resourceCount
}
CORE_WEB_VITALS {
float lcp
float fid
float cls
float fcp
float ttfb
}
SCORING_WEIGHTS {
float RESPONSE_TIME 0.25
float CORE_WEB_VITALS 0.45
float PAGE_SIZE 0.15
float RESOURCE_EFFICIENCY 0.15
}
THRESHOLDS {
int RESPONSE_TIME_EXCELLENT 1000
int RESPONSE_TIME_GOOD 2000
int RESPONSE_TIME_POOR 5000
float LCP_GOOD 2.5
float LCP_NEEDS_IMPROVEMENT 4.0
float FID_GOOD 0.1
float FID_NEEDS_IMPROVEMENT 0.3
float CLS_GOOD 0.1
float CLS_NEEDS_IMPROVEMENT 0.25
int PAGE_SIZE_EXCELLENT 1048576
int PAGE_SIZE_GOOD 3145728
int PAGE_SIZE_POOR 10485760
}
PERFORMANCE_METRICS ||--o{ CORE_WEB_VITALS : "contains"
PERFORMANCE_METRICS }|--|| SCORING_WEIGHTS : "weighted_by"
PERFORMANCE_METRICS }|--|| THRESHOLDS : "evaluated_against"
```

**Diagram sources**
- [PerformanceScorer.ts](file://services/worker/src/ranking/scorers/PerformanceScorer.ts#L50-L100)
- [PerformanceScorer.ts](file://services/worker/src/ranking/scorers/PerformanceScorer.ts#L150-L250)

**Section sources**
- [PerformanceScorer.ts](file://services/worker/src/ranking/scorers/PerformanceScorer.ts#L50-L200)

## Score Calculation Examples
The integration tests for the PerformanceScorer provide concrete examples of score calculations under various conditions. For a website with excellent performance metrics (load time of 500ms, response time of 200ms, page size of 512KB, and 20 resources), the system calculates a score above 0.8, resulting in an A or A+ grade. Conversely, a website with poor performance metrics (load time of 8000ms, response time of 6000ms, page size of 15MB, and 300 resources) receives a score below 0.5, resulting in a D or F grade. The system also handles edge cases such as missing metrics, where it applies default values and calculates a reasonable fallback score.

```mermaid
graph TB
subgraph ExcellentPerformance
A[Load Time: 500ms]
B[Response Time: 200ms]
C[Page Size: 512KB]
D[Resources: 20]
E[LCP: 1.5s]
F[FID: 0.05]
G[CLS: 0.05]
H[Score: >0.8]
I[Grade: A+]
end
subgraph PoorPerformance
J[Load Time: 8000ms]
K[Response Time: 6000ms]
L[Page Size: 15MB]
M[Resources: 300]
N[LCP: 6.0s]
O[FID: 0.5]
P[CLS: 0.4]
Q[Score: <0.5]
R[Grade: F]
end
subgraph MissingMetrics
S[Load Time: undefined]
T[Response Time: undefined]
U[Page Size: undefined]
V[Resources: undefined]
W[Score: ~0.335]
X[Grade: F]
end
ExcellentPerformance --> |High Score| H
PoorPerformance --> |Low Score| Q
MissingMetrics --> |Fallback Score| W
```

**Diagram sources**
- [PerformanceScorer.test.ts](file://services/worker/src/ranking/__tests__/PerformanceScorer.test.ts#L50-L100)
- [PerformanceScorer.test.ts](file://services/worker/src/ranking/__tests__/PerformanceScorer.test.ts#L100-L140)

**Section sources**
- [PerformanceScorer.test.ts](file://services/worker/src/ranking/__tests__/PerformanceScorer.test.ts#L50-L140)

## Crawler Integration and Data Flow
The performance scoring system is tightly integrated with the domain crawling infrastructure through the DomainCrawlJob system. When a crawl job is initiated with a performance focus, the system creates a DomainCrawlJob with the appropriate type and priority. The crawler then executes the PerformanceAuditor to collect metrics, which are passed to the PerformanceScorer for evaluation. The resulting score and recommendations are stored as part of the domain analysis data, making them available for ranking and reporting. This integration ensures that performance data is collected consistently and efficiently across the entire domain portfolio.

```mermaid
sequenceDiagram
participant Scheduler as Crawl Scheduler
participant JobQueue as Job Queue
participant Worker as Worker Process
participant Auditor as PerformanceAuditor
participant Scorer as PerformanceScorer
participant Database as Database
Scheduler->>JobQueue : Create DomainCrawlJob (performance type)
JobQueue->>Worker : Assign job
Worker->>Auditor : Execute auditPerformance(domain)
Auditor->>Browserless : Collect metrics via browser automation
Browserless-->>Auditor : Return raw performance data
Auditor-->>Worker : Return PerformanceAuditResult
Worker->>Scorer : Calculate score from metrics
Scorer-->>Worker : Return normalized score (0-100)
Worker->>Database : Store score with domain analysis
Database-->>Worker : Confirmation
Worker->>JobQueue : Mark job as completed
```

**Diagram sources**
- [DomainCrawlJob.ts](file://shared/src/models/DomainCrawlJob.ts#L1-L50)
- [PerformanceAuditor.ts](file://services/worker/src/crawler/analyzers/PerformanceAuditor.ts#L1-L50)
- [PerformanceScorer.ts](file://services/worker/src/ranking/scorers/PerformanceScorer.ts#L1-L50)

**Section sources**
- [DomainCrawlJob.ts](file://shared/src/models/DomainCrawlJob.ts#L1-L100)
- [PerformanceAuditor.ts](file://services/worker/src/crawler/analyzers/PerformanceAuditor.ts#L1-L100)
- [PerformanceScorer.ts](file://services/worker/src/ranking/scorers/PerformanceScorer.ts#L1-L100)

## Performance Bottlenecks and Optimization Strategies
Several potential bottlenecks can affect the accuracy and efficiency of the performance scoring system. Network connectivity issues with the browserless service can delay or prevent data collection, while complex websites with heavy JavaScript may require extended execution time. To address these bottlenecks, the system implements timeout mechanisms, retry logic, and error handling. Optimization strategies include using connection pooling for browserless requests, implementing caching for recently audited domains, and prioritizing crawl jobs based on domain importance. The system also generates specific recommendations for website owners to improve their performance scores, focusing on critical areas such as image optimization, JavaScript reduction, and server response time improvement.

```mermaid
flowchart TD
A[Performance Bottlenecks] --> B[Network Issues with Browserless]
A --> C[Complex Websites with Heavy JS]
A --> D[Resource-Intensive Measurements]
A --> E[Data Processing Delays]
B --> F[Optimization: Connection Pooling]
B --> G[Optimization: Retry Logic]
B --> H[Optimization: Circuit Breaker]
C --> I[Optimization: Extended Timeouts]
C --> J[Optimization: Resource Limits]
C --> K[Optimization: Progressive Loading]
D --> L[Optimization: Parallel Processing]
D --> M[Optimization: Efficient Algorithms]
D --> N[Optimization: Caching Results]
E --> O[Optimization: Streamlined Data Flow]
E --> P[Optimization: Batch Processing]
E --> Q[Optimization: Asynchronous Operations]
style A fill:#f9f,stroke:#333,stroke-width:2px
style F fill:#bbf,stroke:#333
style G fill:#bbf,stroke:#333
style H fill:#bbf,stroke:#333
style I fill:#bbf,stroke:#333
style J fill:#bbf,stroke:#333
style K fill:#bbf,stroke:#333
style L fill:#bbf,stroke:#333
style M fill:#bbf,stroke:#333
style N fill:#bbf,stroke:#333
style O fill:#bbf,stroke:#333
style P fill:#bbf,stroke:#333
style Q fill:#bbf,stroke:#333
```

**Diagram sources**
- [PerformanceAuditor.ts](file://services/worker/src/crawler/analyzers/PerformanceAuditor.ts#L400-L500)
- [PerformanceScorer.ts](file://services/worker/src/ranking/scorers/PerformanceScorer.ts#L300-L400)
- [PerformanceAuditor.test.ts](file://services/worker/src/crawler/analyzers/__tests__/PerformanceAuditor.test.ts#L50-L100)

**Section sources**
- [PerformanceAuditor.ts](file://services/worker/src/crawler/analyzers/PerformanceAuditor.ts#L400-L600)
- [PerformanceScorer.ts](file://services/worker/src/ranking/scorers/PerformanceScorer.ts#L300-L447)

## Conclusion
The PerformanceScorer component provides a robust and comprehensive system for evaluating website performance through a combination of Core Web Vitals and additional performance metrics. By leveraging the PerformanceAuditor to collect accurate data and applying a weighted scoring algorithm, the system generates reliable 0-100 scores that reflect true user experience quality. The integration with the domain crawling infrastructure ensures consistent data collection across the portfolio, while the detailed breakdown and recommendations provide actionable insights for website optimization. This system enables accurate domain ranking based on performance characteristics and supports continuous improvement through targeted optimization suggestions.