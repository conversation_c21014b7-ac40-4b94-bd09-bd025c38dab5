# Manticore Search Schema

<cite>
**Referenced Files in This Document**   
- [init.sql](file://database/manticore/init.sql)
- [stopwords.json](file://data/stopwords.json)
- [EnhancedManticoreDomainIndex.ts](file://services/domain-seeder/src/repositories/EnhancedManticoreDomainIndex.ts)
- [DomainSearchService.ts](file://services/web-app/src/services/DomainSearchService.ts)
- [SearchIndexingService.ts](file://services/worker/src/indexing/SearchIndexingService.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Index Definitions](#index-definitions)
3. [Tokenization and Text Processing](#tokenization-and-text-processing)
4. [Data Pipeline Architecture](#data-pipeline-architecture)
5. [Search Query Syntax and Capabilities](#search-query-syntax-and-capabilities)
6. [Real-Time Updates and Delta Indexing](#real-time-updates-and-delta-indexing)
7. [Performance Optimization](#performance-optimization)
8. [Search Query Examples](#search-query-examples)
9. [Configuration Best Practices](#configuration-best-practices)

## Introduction
This document provides comprehensive documentation for the Manticore Search schema used in the domain analysis platform. It details the index structure, text processing configuration, data ingestion pipeline, search capabilities, and performance optimization strategies. The system enables high-volume, low-latency domain search with faceted filtering, relevance ranking, and real-time updates.

## Index Definitions

The Manticore Search schema consists of three primary columnar storage indexes designed for different search use cases:

```mermaid
erDiagram
domains_index {
bigint id PK
text domain
text title
text description
text content
string category
string country
string language
multi attributes technologies
string registrar
int domain_age_days
int global_rank
float overall_score
float performance_score
float security_score
float seo_score
float technical_score
float backlink_score
bigint traffic_estimate
string ssl_grade
float mobile_friendly_score
float accessibility_score
timestamp last_updated
}
domain_content_index {
bigint id PK
text domain
string page_type
text title
text content
text headings
multi attributes meta_keywords
string language
timestamp last_updated
}
domain_reviews_index {
bigint id PK
text domain
text review_text
string source
float rating
float sentiment_score
timestamp review_date
int verified
}
domains_index ||--o{ domain_content_index : "contains"
domains_index ||--o{ domain_reviews_index : "has"
```

**Diagram sources**
- [init.sql](file://database/manticore/init.sql#L10-L57)

**Section sources**
- [init.sql](file://database/manticore/init.sql#L10-L57)

### Domains Index
The primary `domains_index` contains comprehensive domain metadata and scores:
- **Full-text fields**: `domain`, `title`, `description`, `content`
- **String attributes**: `category`, `country`, `language`, `registrar`, `ssl_grade`
- **Multi-value attributes**: `technologies` (array of technology names)
- **Numeric attributes**: Various score metrics and `domain_age_days`, `global_rank`, `traffic_estimate`
- **Timestamp**: `last_updated` for tracking index freshness

### Domain Content Index
The `domain_content_index` supports full-text search across website content:
- **Full-text fields**: `domain`, `title`, `content`, `headings`
- **Attributes**: `page_type`, `language`, `meta_keywords` (multi-value)
- Used for deep content analysis and page-level search

### Domain Reviews Index
The `domain_reviews_index` stores user and system-generated reviews:
- **Full-text field**: `review_text` for review content search
- **Attributes**: `source`, `rating`, `sentiment_score`, `verified`
- Supports sentiment analysis and review-based filtering

## Tokenization and Text Processing

The text processing configuration includes custom stopword handling and normalization rules defined in the stopwords.json file.

```mermaid
flowchart TD
InputText["Raw Text Input"] --> Normalization["Apply Normalization Rules"]
Normalization --> StopwordRemoval["Remove Stopwords"]
StopwordRemoval --> Tokenization["Tokenize Text"]
Tokenization --> Stemming["Apply Stemming"]
Stemming --> Indexing["Store in Index"]
style Normalization fill:#f9f,stroke:#333
style StopwordRemoval fill:#f9f,stroke:#333
style Tokenization fill:#f9f,stroke:#333
style Stemming fill:#f9f,stroke:#333
```

**Diagram sources**
- [stopwords.json](file://data/stopwords.json#L1-L5)

**Section sources**
- [stopwords.json](file://data/stopwords.json#L1-L5)

### Stopword Configuration
The system uses a custom stopwords list optimized for domain search:
- **Tags**: Common words filtered from domain analysis: "the", "and", "for", "with", "of", "in", "to", "a", "an", "on", "by", "from", "at", "as", "is", "are", "be", "www", "official", "site", "homepage", "welcome"
- **Categories**: General classification terms: "misc", "other", "general"

### Normalization Rules
Character-level normalization is applied during indexing:
- "&" → "and"
- "+" → "and" 
- "/" → "-"
- " " → "-"

These rules ensure consistent tokenization of domain names and technical terms containing special characters.

## Data Pipeline Architecture

The data pipeline integrates MariaDB, ScyllaDB, and Redis with Manticore Search through a coordinated indexing service.

```mermaid
graph LR
subgraph DataSources
MariaDB[(MariaDB)]
ScyllaDB[(ScyllaDB)]
Redis[(Redis)]
end
subgraph Processing
SearchIndexingService[Search Indexing Service]
DataSync[Data Synchronization]
CacheInvalidation[Cache Invalidation]
IndexMaintenance[Index Maintenance]
end
subgraph Search
Manticore[(Manticore Search)]
end
MariaDB --> DataSync
ScyllaDB --> DataSync
Redis --> DataSync
DataSync --> SearchIndexingService
SearchIndexingService --> Manticore
CacheInvalidation --> Redis
IndexMaintenance --> Manticore
SearchIndexingService --> CacheInvalidation
SearchIndexingService --> IndexMaintenance
style DataSources fill:#f0f0f0,stroke:#666
style Processing fill:#e0e0ff,stroke:#666
style Search fill:#d0ffd0,stroke:#666
```

**Diagram sources**
- [SearchIndexingService.ts](file://services/worker/src/indexing/SearchIndexingService.ts#L1-L831)
- [EnhancedManticoreDomainIndex.ts](file://services/domain-seeder/src/repositories/EnhancedManticoreDomainIndex.ts#L1-L864)

**Section sources**
- [SearchIndexingService.ts](file://services/worker/src/indexing/SearchIndexingService.ts#L1-L831)
- [EnhancedManticoreDomainIndex.ts](file://services/domain-seeder/src/repositories/EnhancedManticoreDomainIndex.ts#L1-L864)

### Data Flow Process
1. **Source Integration**: Comprehensive domain data is stored in MariaDB (relational data), ScyllaDB (wide-column data), and Redis (caching)
2. **Synchronization**: The `DataSynchronizationService` coordinates data extraction and transformation
3. **Indexing**: Transformed data is indexed in Manticore through the `ManticoreIndexManager`
4. **Cache Management**: Index updates trigger cache invalidation through the `CacheInvalidationManager`
5. **Maintenance**: Regular index optimization and cleanup tasks are managed by the `IndexMaintenanceService`

### Component Responsibilities
- **SearchIndexingService**: Orchestrates all indexing operations and coordinates between components
- **DataSynchronizationService**: Handles data transfer between databases and search index
- **ManticoreIndexManager**: Manages CRUD operations on Manticore indexes
- **CacheInvalidationManager**: Ensures cache consistency with index updates
- **IndexMaintenanceService**: Performs optimization, merging, and cleanup tasks

## Search Query Syntax and Capabilities

The search system supports rich query syntax with faceting, filtering, and ranking capabilities through the `DomainSearchService`.

```mermaid
sequenceDiagram
participant Client as "Web Application"
participant SearchService as "DomainSearchService"
participant ManticoreClient as "ManticoreClient"
participant Manticore as "Manticore Search"
Client->>SearchService : searchDomains(params)
SearchService->>SearchService : Normalize parameters
SearchService->>SearchService : Build filters
SearchService->>ManticoreClient : searchDomains(request)
ManticoreClient->>Manticore : Execute search query
Manticore-->>ManticoreClient : Return results with facets
ManticoreClient-->>SearchService : Results with metadata
SearchService->>SearchService : Calculate pagination
SearchService-->>Client : Formatted response with domains, facets, pagination
```

**Diagram sources**
- [DomainSearchService.ts](file://services/web-app/src/services/DomainSearchService.ts#L1-L303)

**Section sources**
- [DomainSearchService.ts](file://services/web-app/src/services/DomainSearchService.ts#L1-L303)

### Query Parameters
The `searchDomains` method accepts flexible parameters:
- **query**: Full-text search term (supports wildcards like `query*`)
- **category, country, technology, sslGrade**: Faceted filtering
- **minRank, maxRank, minScore, maxScore**: Numeric range filtering
- **sort**: Sorting criteria (default: "score")
- **page, limit**: Pagination (limit capped at 100)

### Faceting Capabilities
The system automatically generates facets for key attributes:
- **Category**: Domain classification categories
- **Country**: Geographic location of domains
- **Technologies**: Technology stack components
- **SSL Grade**: Security certification levels

Facets are returned with value counts to support faceted navigation interfaces.

### Ranking Algorithm
Results are ranked using Manticore's built-in ranking with domain-specific scoring:
- **Default ranking**: Based on full-text relevance
- **Custom scoring**: Domain scores (overall, performance, security, SEO, technical) influence ranking
- **Boosting**: Certain fields may be weighted more heavily in relevance calculations

## Real-Time Updates and Delta Indexing

The system implements a real-time update strategy with delta indexing to maintain index freshness while optimizing performance.

### Update Strategy
The indexing service supports multiple update methods:
- **Single domain indexing**: `indexDomain()` for real-time updates
- **Bulk indexing**: `bulkIndexDomains()` for batch operations
- **Domain deletion**: `deleteDomain()` for removal from index
- **Incremental sync**: `performIncrementalSync()` for periodic updates
- **Full sync**: `performFullSync()` for complete index rebuild

### Delta Indexing Implementation
The delta indexing strategy uses timestamp-based change detection:
- **Last updated tracking**: Each document has a `last_updated` timestamp
- **Incremental sync**: Queries source databases for records modified since last sync
- **Conflict resolution**: Configurable strategy (latest_wins, merge, skip)
- **Batch processing**: Updates processed in configurable batch sizes (default: 100)

### Real-Time Update Flow
```mermaid
flowchart TD
DataChange["Data Change in MariaDB/ScyllaDB"] --> Trigger["Trigger Index Update"]
Trigger --> CheckConfig["Check Real-Time Indexing Config"]
CheckConfig --> |Enabled| ProcessUpdate["Process Update via SearchIndexingService"]
CheckConfig --> |Disabled| QueueUpdate["Queue for Batch Processing"]
ProcessUpdate --> Transform["Transform Domain Data"]
Transform --> Index["Update Manticore Index"]
Index --> Invalidate["Invalidate Related Caches"]
Invalidate --> Complete["Update Complete"]
QueueUpdate --> BatchProcess["Process in Scheduled Batch"]
BatchProcess --> Complete
```

**Section sources**
- [SearchIndexingService.ts](file://services/worker/src/indexing/SearchIndexingService.ts#L1-L831)

## Performance Optimization

The system implements multiple performance optimization strategies for high-volume search operations.

### Enhanced Manticore Operations
The `EnhancedManticoreDomainIndex` class provides optimized operations:
- **Batch processing**: Supports 10K-100K domains per query
- **Client-side caching**: 5-minute TTL cache for query results
- **Connection pooling**: 20 connections for parallel operations
- **Intelligent batching**: Dynamic batch size optimization based on error rates

```mermaid
classDiagram
class EnhancedManticoreDomainIndex {
+maxBatchSize : number
+optimalBatchSize : number
+queryTimeout : number
+maxRetries : number
+connectionPoolSize : number
+enableClientCache : boolean
+clientCacheTtl : number
+maxCacheSize : number
+indexName : string
+batchCheckExistence(domains) : EnhancedBatchResult
+checkExistence(domain) : boolean
+optimizedBatchQuery(domains) : Promise~BatchQueryResult~
+getMetrics() : Metrics
+healthCheck() : HealthStatus
+clearCache() : void
}
class ManticoreClient {
+searchDomains(request) : SearchResult
+compareDomains(domains) : CompareResult
+getTopDomains(request) : TopDomainsResult
+healthCheck() : boolean
+indexExists(indexName) : boolean
}
EnhancedManticoreDomainIndex --> ManticoreClient : "uses"
```

**Diagram sources**
- [EnhancedManticoreDomainIndex.ts](file://services/domain-seeder/src/repositories/EnhancedManticoreDomainIndex.ts#L1-L864)

**Section sources**
- [EnhancedManticoreDomainIndex.ts](file://services/domain-seeder/src/repositories/EnhancedManticoreDomainIndex.ts#L1-L864)

### Configuration Tuning
Key performance configuration parameters:
- **Batch size**: Configurable via `INDEXING_BATCH_SIZE` environment variable (default: 100)
- **Concurrency**: Configurable via `INDEXING_MAX_CONCURRENCY` (default: 10)
- **Sync interval**: Configurable via `INDEXING_SYNC_INTERVAL` (default: 300000ms)
- **Cache invalidation**: Toggle via `INDEXING_ENABLE_CACHE_INVALIDATION`

### Index Optimization
Regular maintenance tasks ensure optimal performance:
- **Index optimization**: Periodic merging of disk chunks
- **Resource monitoring**: Connection pool utilization tracking
- **Error handling**: Retry mechanisms with exponential backoff
- **Health checks**: Comprehensive service health monitoring

## Search Query Examples

The `DomainSearchService` provides several methods for different search use cases.

### Basic Domain Search
```typescript
// [searchDomains](file://services/web-app/src/services/DomainSearchService.ts#L40-L150)
const results = await domainSearchService.searchDomains({
  query: "tech",
  category: "technology",
  minScore: 80,
  limit: 20,
  page: 1
});
```

### Top Domains Retrieval
```typescript
// [getTopDomains](file://services/web-app/src/services/DomainSearchService.ts#L152-L165)
const topResults = await domainSearchService.getTopDomains("technology", {
  limit: 50
});
```

### Domain Comparison
```typescript
// [compareDomains](file://services/web-app/src/services/DomainSearchService.ts#L167-L180)
const comparison = await domainSearchService.compareDomains([
  "example.com", 
  "sample.org",
  "test.net"
]);
```

### Search Suggestions
```typescript
// [getSearchSuggestions](file://services/web-app/src/services/DomainSearchService.ts#L182-L215)
const suggestions = await domainSearchService.getSearchSuggestions("cloud", 10);
```

### Popular Searches Analysis
```typescript
// [getPopularSearches](file://services/web-app/src/services/DomainSearchService.ts#L217-L256)
const popular = await domainSearchService.getPopularSearches();
```

### Search Statistics
```typescript
// [getSearchStats](file://services/web-app/src/services/DomainSearchService.ts#L258-L298)
const stats = await domainSearchService.getSearchStats();
```

## Configuration Best Practices

### Relevance Tuning
For optimal search relevance:
- **Field weighting**: Configure field weights in Manticore to prioritize important fields
- **Stopword optimization**: Regularly review and update stopwords based on search analytics
- **Stemming configuration**: Ensure stemming rules match domain industry terminology
- **Query expansion**: Consider implementing synonym expansion for common domain terms

### Performance Recommendations
- **Batch sizing**: Monitor error rates and adjust batch sizes accordingly
- **Connection pooling**: Ensure pool size matches concurrent workload
- **Caching strategy**: Balance cache TTL with data freshness requirements
- **Index partitioning**: Consider partitioning large indexes by category or region

### Monitoring and Maintenance
- **Health checks**: Implement regular health checks for all indexing components
- **Metrics collection**: Monitor key metrics (query latency, error rates, cache hit rates)
- **Scheduled optimization**: Run index optimization during low-traffic periods
- **Capacity planning**: Monitor index growth and plan for storage scaling

### High-Availability Considerations
- **Replication**: Configure Manticore replication for fault tolerance
- **Backup strategy**: Implement regular index snapshots
- **Disaster recovery**: Test index rebuild procedures regularly
- **Rolling updates**: Deploy indexing service updates with zero downtime