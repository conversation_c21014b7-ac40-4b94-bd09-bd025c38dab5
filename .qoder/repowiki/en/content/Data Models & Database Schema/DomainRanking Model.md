# DomainRanking Model

<cite>
**Referenced Files in This Document**   
- [DomainRanking.ts](file://shared/src/models/DomainRanking.ts)
- [RankingCalculator.ts](file://services/worker/src/ranking/RankingCalculator.ts)
- [ScyllaDomainRepository.ts](file://services/domain-seeder/src/repositories/ScyllaDomainRepository.ts)
- [RankingUpdateService.ts](file://services/worker/src/ranking/RankingUpdateService.ts)
- [TopDomainsService.ts](file://services/web-app/src/services/TopDomainsService.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [DomainRanking Schema in ScyllaDB](#domainranking-schema-in-scylladb)
3. [Scoring Components and Weights](#scoring-components-and-weights)
4. [Ranking Calculation Process](#ranking-calculation-process)
5. [Historical Data and Versioning](#historical-data-and-versioning)
6. [Category-Specific and Country-Based Rankings](#category-specific-and-country-based-rankings)
7. [Real-Time Update Mechanisms](#real-time-update-mechanisms)
8. [Query Examples from Web Application](#query-examples-from-web-application)
9. [Performance Optimization Techniques](#performance-optimization-techniques)
10. [Conclusion](#conclusion)

## Introduction

The DomainRanking model is a core component of the domain analysis system, responsible for quantifying and tracking the performance, security, SEO, technical, and backlink metrics of domains. This document provides comprehensive documentation of the DomainRanking entity, detailing its schema structure in ScyllaDB, scoring methodology, update mechanisms, and integration with the broader system. The model supports both global and category-specific rankings with country filtering, enabling granular insights into domain performance across different segments.

**Section sources**
- [DomainRanking.ts](file://shared/src/models/DomainRanking.ts#L1-L70)

## DomainRanking Schema in ScyllaDB

The DomainRanking entity is stored in ScyllaDB, a high-performance NoSQL database optimized for fast ranking lookups and time-series analysis. The schema is designed to support efficient querying of rankings, historical data, and trend analysis.

```mermaid
erDiagram
domain_rankings {
string ranking_type PK
string domain PK
int rank
float overall_score
float performance_score
float security_score
float seo_score
float technical_score
float backlink_score
float traffic_estimate
timestamp last_updated
}
domain_ranking_history {
string domain PK
string date PK
string ranking_type PK
int rank
float overall_score
}
```

**Diagram sources**
- [DomainRanking.ts](file://shared/src/models/DomainRanking.ts#L1-L70)
- [RankingUpdateService.ts](file://services/worker/src/ranking/RankingUpdateService.ts#L1-L1358)

The schema includes two main tables:
- `domain_rankings`: Stores current rankings with a composite primary key of ranking_type and domain for efficient lookups
- `domain_ranking_history`: Tracks historical rankings with date-based partitioning for time-series analysis

The ScyllaDB implementation is optimized for batch queries and handles ScyllaDB-specific query patterns, ensuring high-performance domain existence checking and ranking retrieval.

**Section sources**
- [DomainRanking.ts](file://shared/src/models/DomainRanking.ts#L1-L70)
- [ScyllaDomainRepository.ts](file://services/domain-seeder/src/repositories/ScyllaDomainRepository.ts#L1-L247)

## Scoring Components and Weights

The DomainRanking model calculates an overall score based on five key components: performance, security, SEO, technical, and backlink metrics. Each component is scored individually and combined using weighted averages to produce the final ranking.

```mermaid
classDiagram
class DomainRanking {
+string domain
+string rankingType
+int rank
+float overallScore
+float performanceScore
+float securityScore
+float seoScore
+float technicalScore
+float backlinkScore
+float trafficEstimate
+string lastUpdated
+toScyllaFormat() Map~string,unknown~
}
class RankingCalculator {
-CompositeRanker ranker
-RankingConfigurationType config
+calculateDomainScore(domainData) CompositeScoreType
+calculateBatchRankings(domains) RankingResultType[]
+calculateCategoryRankings(domains, category) RankingResultType[]
+getDetailedBreakdown(domainData) DetailedBreakdownType
+getRecommendations(domainData) string[]
+validateDomainData(domainData) RankingValidationResultType
}
class CompositeRanker {
-RankingWeightsType weights
+calculateCompositeScore(domainData) CompositeScoreType
+calculateGlobalRankings(domains) RankingResultType[]
+calculateCategoryRankings(domains, category) RankingResultType[]
+getDetailedBreakdown(domainData) DetailedBreakdownType
+getComprehensiveRecommendations(domainData) string[]
}
DomainRanking --> RankingCalculator : "uses for scoring"
RankingCalculator --> CompositeRanker : "delegates calculations"
```

**Diagram sources**
- [DomainRanking.ts](file://shared/src/models/DomainRanking.ts#L1-L70)
- [RankingCalculator.ts](file://services/worker/src/ranking/RankingCalculator.ts#L1-L577)

The scoring components are defined as follows:
- **Performance Score**: Based on load time, page size, and resource count metrics
- **Security Score**: Derived from SSL certificate status, security headers, and vulnerability assessments
- **SEO Score**: Calculated from meta tags, content quality, and on-page optimization factors
- **Technical Score**: Includes DNS response time, uptime percentage, and server response metrics
- **Backlink Score**: Based on total backlinks, unique domains, and average authority

The weights for each component can be dynamically updated through the RankingCalculator's updateWeights method, allowing for flexible ranking strategies based on business requirements.

**Section sources**
- [RankingCalculator.ts](file://services/worker/src/ranking/RankingCalculator.ts#L1-L577)

## Ranking Calculation Process

The ranking calculation process is managed by the RankingCalculator in the Worker service, which orchestrates the scoring of domains using the CompositeRanker. The process involves several key steps:

```mermaid
sequenceDiagram
participant WebApp
participant Worker as Worker Service
participant Calculator as RankingCalculator
participant Ranker as CompositeRanker
participant DB as ScyllaDB
WebApp->>Worker : Trigger ranking update
Worker->>Calculator : calculateDomainScore()
Calculator->>Calculator : validateDomainData()
Calculator->>Ranker : calculateCompositeScore()
Ranker->>Ranker : Calculate individual component scores
Ranker-->>Calculator : Return composite score
Calculator->>Calculator : Update monitoring metrics
Calculator-->>Worker : Return ranking result
Worker->>DB : storeDomainRanking()
Worker->>DB : updateRankingHistory()
Worker-->>WebApp : Ranking update complete
```

**Diagram sources**
- [RankingCalculator.ts](file://services/worker/src/ranking/RankingCalculator.ts#L1-L577)
- [RankingUpdateService.ts](file://services/worker/src/ranking/RankingUpdateService.ts#L1-L1358)

The process begins when a ranking update is triggered, either manually or through scheduled jobs. The RankingCalculator first validates the domain data, checking for completeness and quality. It then delegates the actual scoring to the CompositeRanker, which calculates the composite score by combining the individual component scores according to their weights.

For batch operations, the system processes domains in configurable batches to manage memory and performance. The rankings are sorted globally after calculation, and ranks are reassigned accordingly. The process includes comprehensive monitoring and error handling, with metrics collected on calculation time, domains processed, and error rates.

**Section sources**
- [RankingCalculator.ts](file://services/worker/src/ranking/RankingCalculator.ts#L1-L577)
- [RankingUpdateService.ts](file://services/worker/src/ranking/RankingUpdateService.ts#L1-L1358)

## Historical Data and Versioning

The DomainRanking model includes comprehensive historical data tracking and versioning capabilities, enabling trend analysis and change detection over time.

```mermaid
flowchart TD
A[Domain Analysis] --> B[Calculate Current Ranking]
B --> C[Store in domain_rankings]
C --> D[Compare with Previous Ranking]
D --> E{Significant Change?}
E --> |Yes| F[Update Ranking History]
E --> |No| G[Update Last Updated Timestamp]
F --> H[Calculate Trend Analysis]
H --> I[Store in domain_ranking_history]
I --> J[Trigger Related Updates]
J --> K[Update Search Indexes]
K --> L[Notify Subscribers]
```

**Diagram sources**
- [RankingUpdateService.ts](file://services/worker/src/ranking/RankingUpdateService.ts#L1-L1358)

Historical data is stored in the `domain_ranking_history` table, which maintains a record of rankings over time. Each entry includes the domain, date, ranking type, rank, and overall score, allowing for time-series analysis of ranking trends.

The system automatically tracks changes in rankings and calculates rank differences compared to previous periods. This enables the detection of significant ranking movements, which can trigger additional processing such as category recalculation or alert generation.

Trend analysis is performed by calculating metrics such as average rank, best/worst rank, volatility (standard deviation), and trend direction (up, down, stable). These results are cached in Redis for quick access and displayed in the web application's trending domains feature.

**Section sources**
- [RankingUpdateService.ts](file://services/worker/src/ranking/RankingUpdateService.ts#L1-L1358)

## Category-Specific and Country-Based Rankings

The DomainRanking model supports both category-specific and country-filtered rankings, providing granular insights into domain performance across different segments.

```mermaid
graph TB
A[Global Rankings] --> B[Category Rankings]
A --> C[Country Rankings]
B --> D[Technology]
B --> E[Business]
B --> F[News]
B --> G[E-commerce]
C --> H[United States]
C --> I[Germany]
C --> J[Japan]
C --> K[United Kingdom]
D --> L[Top 100 Domains]
E --> M[Top 100 Domains]
F --> N[Top 100 Domains]
G --> O[Top 100 Domains]
H --> P[Top 100 Domains]
I --> Q[Top 100 Domains]
J --> R[Top 100 Domains]
K --> S[Top 100 Domains]
```

**Diagram sources**
- [RankingCalculator.ts](file://services/worker/src/ranking/RankingCalculator.ts#L1-L577)
- [TopDomainsService.ts](file://services/web-app/src/services/TopDomainsService.ts#L1-L667)

Category-specific rankings are calculated by filtering domains based on their category attribute and then applying the same scoring algorithm used for global rankings. The ranking type is stored as `category:{category_name}` in the database, allowing for efficient queries.

Country-based filtering is implemented at the query level, where the TopDomainsService applies country filters to the results. This allows users to view rankings for domains in specific countries, providing localized insights.

The system supports both independent category rankings and combined category-country filters, enabling complex queries such as "top technology domains in Germany." These rankings are cached separately to optimize performance for frequently accessed combinations.

**Section sources**
- [RankingCalculator.ts](file://services/worker/src/ranking/RankingCalculator.ts#L1-L577)
- [TopDomainsService.ts](file://services/web-app/src/services/TopDomainsService.ts#L1-L667)

## Real-Time Update Mechanisms

The DomainRanking model employs a sophisticated real-time update mechanism that ensures rankings are kept current while maintaining system performance.

```mermaid
sequenceDiagram
participant WebApp
participant JobQueue
participant Worker
participant ScyllaDB
participant Redis
WebApp->>Worker : triggerDomainRankingUpdate()
Worker->>JobQueue : publishJob('ranking : update')
JobQueue->>Worker : Consume job
Worker->>Worker : handleSingleRankingUpdate()
Worker->>ScyllaDB : fetchDomainData()
Worker->>Worker : calculateCompositeScore()
Worker->>ScyllaDB : storeDomainRanking()
Worker->>ScyllaDB : updateRankingHistory()
Worker->>JobQueue : publishJob('manticore : sync')
Worker->>JobQueue : publishJob('ranking : history')
Worker->>Redis : invalidate cache
Worker-->>WebApp : Update complete
```

**Diagram sources**
- [RankingUpdateService.ts](file://services/worker/src/ranking/RankingUpdateService.ts#L1-L1358)

The update process is orchestrated through a job queue system that handles both single domain updates and batch operations. When a domain's data changes, a ranking update is triggered, which publishes a job to the 'ranking:update' queue.

The Worker service consumes these jobs and processes the ranking calculation. After updating the ranking in ScyllaDB, the system triggers related updates, including synchronizing with Manticore search indexes and scheduling ranking history analysis.

The system also supports scheduled updates, with daily global ranking recalculation at 2 AM and weekly category ranking updates on Sundays at 3 AM. These recurring jobs ensure that rankings remain accurate even without explicit triggers.

Cache invalidation is performed automatically when rankings are updated, ensuring that the web application always displays current data. The system uses Redis to cache frequently accessed ranking data, reducing database load and improving response times.

**Section sources**
- [RankingUpdateService.ts](file://services/worker/src/ranking/RankingUpdateService.ts#L1-L1358)

## Query Examples from Web Application

The web application provides several endpoints for querying DomainRanking data, optimized for different use cases.

```mermaid
flowchart TD
A[Web Application] --> B[TopDomainsService]
B --> C{Query Type}
C --> D[getTopDomains]
C --> E[getGlobalRankings]
C --> F[getCategoryRankings]
C --> G[getAvailableCategories]
C --> H[getTrendingDomains]
D --> I[Use Manticore for fast retrieval]
E --> J[Query ScyllaDB with pagination]
F --> K[Query ScyllaDB by category]
G --> L[Get from Manticore facets]
H --> M[Query ranking history]
I --> N[Apply category/country filters]
J --> O[Return paginated results]
K --> P[Calculate category/global rank]
L --> Q[Cache results in Redis]
M --> R[Calculate trend direction]
N --> S[Transform and return results]
O --> S
P --> S
Q --> S
R --> S
S --> T[Client]
```

**Diagram sources**
- [TopDomainsService.ts](file://services/web-app/src/services/TopDomainsService.ts#L1-L667)

The TopDomainsService provides the following key methods:
- `getTopDomains`: Retrieves top domains with optional category and country filtering, using Manticore for fast retrieval
- `getGlobalRankings`: Returns paginated global rankings from ScyllaDB with sorting and filtering options
- `getCategoryRankings`: Provides category-specific rankings with pagination and global rank comparison
- `getAvailableCategories`: Returns a list of available categories with counts and descriptions
- `getTrendingDomains`: Identifies domains with significant ranking changes over a specified timeframe

All queries include caching mechanisms using Redis, with configurable TTLs to balance freshness and performance. The service automatically checks the cache before querying the database and updates the cache with new results.

**Section sources**
- [TopDomainsService.ts](file://services/web-app/src/services/TopDomainsService.ts#L1-L667)

## Performance Optimization Techniques

The DomainRanking system employs several performance optimization techniques to handle large-scale ranking data efficiently.

```mermaid
graph TB
A[Performance Optimization] --> B[Database Optimization]
A --> C[Query Optimization]
A --> D[Caching Strategy]
A --> E[Batch Processing]
A --> F[Asynchronous Processing]
B --> G[ScyllaDB Partitioning]
B --> H[Composite Primary Keys]
B --> I[Materialized Views]
C --> J[IN Queries for Batch Operations]
C --> K[Covering Indexes]
C --> L[Pagination]
D --> M[Redis Caching]
D --> N[Cache Invalidation]
D --> O[Cache TTL Management]
E --> P[Batch Size Configuration]
E --> Q[Parallel Processing]
E --> R[Memory Management]
F --> S[Job Queue System]
F --> T[Background Processing]
F --> U[Rate Limiting]
```

**Diagram sources**
- [ScyllaDomainRepository.ts](file://services/domain-seeder/src/repositories/ScyllaDomainRepository.ts#L1-L247)
- [RankingUpdateService.ts](file://services/worker/src/ranking/RankingUpdateService.ts#L1-L1358)
- [TopDomainsService.ts](file://services/web-app/src/services/TopDomainsService.ts#L1-L667)

Key optimization techniques include:
- **Database Optimization**: ScyllaDB is configured with appropriate partitioning and composite primary keys to ensure fast lookups. The schema is designed to minimize read amplification and optimize for the most common query patterns.
- **Query Optimization**: The system uses IN queries for batch operations, covering indexes to avoid secondary lookups, and pagination to limit result set sizes.
- **Caching Strategy**: Redis is used extensively to cache frequently accessed data, including top domains, category rankings, and available categories. Cache invalidation is performed automatically when data changes.
- **Batch Processing**: Large-scale operations are processed in configurable batches to manage memory usage and prevent system overload. The batch size can be adjusted based on system load and performance requirements.
- **Asynchronous Processing**: A job queue system handles ranking updates asynchronously, preventing blocking operations and allowing for better resource utilization. This enables the system to process thousands of domains without impacting user-facing operations.

These optimizations ensure that the DomainRanking system can handle large-scale data while maintaining responsive performance for user queries.

**Section sources**
- [ScyllaDomainRepository.ts](file://services/domain-seeder/src/repositories/ScyllaDomainRepository.ts#L1-L247)
- [RankingUpdateService.ts](file://services/worker/src/ranking/RankingUpdateService.ts#L1-L1358)
- [TopDomainsService.ts](file://services/web-app/src/services/TopDomainsService.ts#L1-L667)

## Conclusion

The DomainRanking model provides a comprehensive system for quantifying and tracking domain performance across multiple dimensions. By leveraging ScyllaDB for storage and a sophisticated scoring algorithm, the system delivers accurate, up-to-date rankings that support both global and category-specific analysis. The integration of historical data tracking, real-time updates, and performance optimizations ensures that the model can handle large-scale data while providing responsive queries for the web application. This robust foundation enables detailed insights into domain performance, trend analysis, and competitive benchmarking across different market segments.