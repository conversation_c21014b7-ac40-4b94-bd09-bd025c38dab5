# DomainAnalysis Model

<cite>
**Referenced Files in This Document**   
- [DomainAnalysis.ts](file://shared/src/models/DomainAnalysis.ts)
- [DomainCrawlJob.ts](file://shared/src/models/DomainCrawlJob.ts)
- [DomainRanking.ts](file://shared/src/models/DomainRanking.ts)
- [ScyllaSchema.ts](file://services/worker/src/database/schemas/ScyllaSchema.ts)
- [MariaSchema.ts](file://services/worker/src/database/schemas/MariaSchema.ts)
- [ManticoreSchema.ts](file://services/worker/src/database/schemas/ManticoreSchema.ts)
- [AdvancedContentAnalyzer.ts](file://services/worker/src/crawler/analyzers/AdvancedContentAnalyzer.ts)
- [DomainInfoAnalyzer.ts](file://services/worker/src/crawler/analyzers/DomainInfoAnalyzer.ts)
- [HomepageAnalyzer.ts](file://services/worker/src/crawler/analyzers/HomepageAnalyzer.ts)
- [PerformanceAuditor.ts](file://services/worker/src/crawler/analyzers/PerformanceAuditor.ts)
- [SSLAnalyzer.ts](file://services/worker/src/crawler/analyzers/SSLAnalyzer.ts)
- [DomainProcessingPipeline.ts](file://services/worker/src/pipeline/DomainProcessingPipeline.ts)
- [DomainAnalysisPage.tsx](file://services/web-app/src/components/DomainAnalysisPage.tsx)
- [ScyllaClient.ts](file://shared/src/database/ScyllaClient.ts)
- [MariaClient.ts](file://shared/src/database/MariaClient.ts)
- [RedisClient.ts](file://shared/src/database/RedisClient.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Core Data Model](#core-data-model)
3. [Database Schema Implementation](#database-schema-implementation)
4. [Field Definitions and Data Types](#field-definitions-and-data-types)
5. [Relationships with Other Models](#relationships-with-other-models)
6. [Data Validation and Business Logic](#data-validation-and-business-logic)
7. [Lifecycle and Population Process](#lifecycle-and-population-process)
8. [Querying and Access Patterns](#querying-and-access-patterns)
9. [Performance and Storage Considerations](#performance-and-storage-considerations)
10. [Caching Strategy](#caching-strategy)
11. [Data Retention Policies](#data-retention-policies)
12. [Usage Examples](#usage-examples)

## Introduction

The DomainAnalysis model serves as the central data structure for storing comprehensive analysis data about internet domains within the domainr platform. This model captures technical, content, performance, and metadata attributes collected during domain crawling operations, providing the foundation for domain intelligence features across the web application. The model is designed to support high-volume data ingestion from the Worker service while enabling efficient retrieval for domain analysis pages in the Web Application.

**Section sources**
- [DomainAnalysis.ts](file://shared/src/models/DomainAnalysis.ts)

## Core Data Model

The DomainAnalysis entity represents a complete technical and content analysis of a domain, capturing data points from multiple analysis modules including content, performance, security, and metadata extraction. The model is implemented as a TypeScript interface with strict typing to ensure data consistency across services.

```mermaid
classDiagram
class DomainAnalysis {
+string domain
+string tld
+string subdomain
+string primaryCategory
+string[] categories
+string[] tags
+string title
+string description
+string language
+number wordCount
+number readabilityScore
+string favicon
+string screenshot
+boolean hasSSL
+string sslIssuer
+number sslAgeDays
+number loadTimeMs
+number pageSizeKb
+number performanceScore
+number accessibilityScore
+number seoScore
+number bestPracticesScore
+string[] technologies
+string[] backlinks
+number domainAgeDays
+string country
+string ip
+string server
+string[] dnsRecords
+string robotsTxt
+string[] sitemapUrls
+string[] socialProfiles
+string[] emails
+string[] phones
+string[] addresses
+string contentHash
+datetime lastCrawled
+datetime createdAt
+datetime updatedAt
+string crawlJobId
+string rankingId
}
class DomainCrawlJob {
+string jobId
+string domain
+string status
+string priority
+number retryCount
+datetime createdAt
+datetime startedAt
+datetime completedAt
+string workerId
+string error
}
class DomainRanking {
+string domain
+number globalRank
+number countryRank
+number categoryRank
+number trafficTrend
+number engagementScore
+number backlinkAuthority
+number socialAuthority
+number estimatedVisits
+datetime lastUpdated
}
DomainAnalysis --> DomainCrawlJob : "generated by"
DomainAnalysis --> DomainRanking : "includes"
```

**Diagram sources**
- [DomainAnalysis.ts](file://shared/src/models/DomainAnalysis.ts)
- [DomainCrawlJob.ts](file://shared/src/models/DomainCrawlJob.ts)
- [DomainRanking.ts](file://shared/src/models/DomainRanking.ts)

**Section sources**
- [DomainAnalysis.ts](file://shared/src/models/DomainAnalysis.ts)
- [DomainCrawlJob.ts](file://shared/src/models/DomainCrawlJob.ts)
- [DomainRanking.ts](file://shared/src/models/DomainRanking.ts)

## Database Schema Implementation

The DomainAnalysis model is persisted across multiple database systems to optimize for different access patterns and performance requirements. Each database implementation leverages specific features to maximize efficiency for its intended use case.

### ScyllaDB Schema

ScyllaDB serves as the primary storage for DomainAnalysis data, optimized for high-throughput writes from the Worker service and efficient retrieval by the Web Application. The schema uses composite partitioning and clustering for optimal performance.

```mermaid
erDiagram
DOMAIN_ANALYSIS {
string domain PK
string tld
string subdomain
string primaryCategory
string[] categories
string[] tags
string title
string description
string language
int wordCount
float readabilityScore
string favicon
string screenshot
boolean hasSSL
string sslIssuer
int sslAgeDays
int loadTimeMs
int pageSizeKb
int performanceScore
int accessibilityScore
int seoScore
int bestPracticesScore
string[] technologies
string[] backlinks
int domainAgeDays
string country
string ip
string server
string[] dnsRecords
string robotsTxt
string[] sitemapUrls
string[] socialProfiles
string[] emails
string[] phones
string[] addresses
string contentHash
timestamp lastCrawled
timestamp createdAt
timestamp updatedAt
string crawlJobId
string rankingId
}
```

**Diagram sources**
- [ScyllaSchema.ts](file://services/worker/src/database/schemas/ScyllaSchema.ts)
- [ScyllaClient.ts](file://shared/src/database/ScyllaClient.ts)

**Section sources**
- [ScyllaSchema.ts](file://services/worker/src/database/schemas/ScyllaSchema.ts)

### MariaDB Schema

MariaDB provides secondary storage for DomainAnalysis data, primarily used for complex queries, reporting, and backup purposes. The schema includes full-text indexing on content fields and secondary indexes on frequently queried attributes.

```mermaid
erDiagram
DOMAIN_ANALYSIS {
varchar domain PK
varchar tld
varchar subdomain
varchar primaryCategory
text categories
text tags
varchar title
text description
varchar language
int wordCount
float readabilityScore
varchar favicon
varchar screenshot
boolean hasSSL
varchar sslIssuer
int sslAgeDays
int loadTimeMs
int pageSizeKb
int performanceScore
int accessibilityScore
int seoScore
int bestPracticesScore
text technologies
text backlinks
int domainAgeDays
varchar country
varchar ip
varchar server
text dnsRecords
text robotsTxt
text sitemapUrls
text socialProfiles
text emails
text phones
text addresses
varchar contentHash
datetime lastCrawled
datetime createdAt
datetime updatedAt
varchar crawlJobId FK
varchar rankingId FK
}
DOMAIN_CRAWL_JOB {
varchar jobId PK
varchar domain
varchar status
varchar priority
int retryCount
datetime createdAt
datetime startedAt
datetime completedAt
varchar workerId
text error
}
DOMAIN_RANKING {
varchar domain PK
int globalRank
int countryRank
int categoryRank
float trafficTrend
float engagementScore
float backlinkAuthority
float socialAuthority
int estimatedVisits
datetime lastUpdated
}
DOMAIN_ANALYSIS ||--|| DOMAIN_CRAWL_JOB : "crawlJobId"
DOMAIN_ANALYSIS ||--|| DOMAIN_RANKING : "rankingId"
```

**Diagram sources**
- [MariaSchema.ts](file://services/worker/src/database/schemas/MariaSchema.ts)
- [MariaClient.ts](file://shared/src/database/MariaClient.ts)

**Section sources**
- [MariaSchema.ts](file://services/worker/src/database/schemas/MariaSchema.ts)

### Manticore Search Schema

Manticore Search provides full-text search capabilities for DomainAnalysis data, enabling fast content-based searches across domain titles, descriptions, and other text fields. The schema is optimized for relevance scoring and faceted search.

```mermaid
erDiagram
DOMAIN_ANALYSIS_INDEX {
bigint id PK
string domain
string tld
string subdomain
string primaryCategory
string categories
string tags
string title
string description
string language
int wordCount
float readabilityScore
string favicon
string screenshot
bool hasSSL
string sslIssuer
int sslAgeDays
int loadTimeMs
int pageSizeKb
int performanceScore
int accessibilityScore
int seoScore
int bestPracticesScore
string technologies
string backlinks
int domainAgeDays
string country
string ip
string server
string dnsRecords
string robotsTxt
string sitemapUrls
string socialProfiles
string emails
string phones
string addresses
string contentHash
timestamp lastCrawled
timestamp createdAt
timestamp updatedAt
string crawlJobId
string rankingId
}
```

**Diagram sources**
- [ManticoreSchema.ts](file://services/worker/src/database/schemas/ManticoreSchema.ts)

**Section sources**
- [ManticoreSchema.ts](file://services/worker/src/database/schemas/ManticoreSchema.ts)

## Field Definitions and Data Types

The DomainAnalysis model contains comprehensive fields capturing various aspects of domain analysis. Each field is defined with specific data types and validation rules to ensure data quality.

### Core Identification Fields
- **domain**: `string` - The fully qualified domain name (e.g., "example.com")
- **tld**: `string` - Top-level domain (e.g., "com", "org")
- **subdomain**: `string` - Subdomain portion (e.g., "www", "blog")
- **primaryCategory**: `string` - Primary classification category
- **categories**: `string[]` - Array of assigned categories
- **tags**: `string[]` - Array of descriptive tags

### Content Analysis Fields
- **title**: `string` - HTML title tag content
- **description**: `string` - Meta description content
- **language**: `string` - Detected content language (ISO 639-1)
- **wordCount**: `number` - Total words in main content
- **readabilityScore**: `number` - Flesch reading ease score (0-100)
- **contentHash**: `string` - SHA-256 hash of main content

### Visual and Media Fields
- **favicon**: `string` - Base64-encoded favicon image
- **screenshot**: `string` - Base64-encoded homepage screenshot
- **technologies**: `string[]` - Detected technologies (CMS, frameworks, etc.)

### Performance Metrics
- **loadTimeMs**: `number` - Page load time in milliseconds
- **pageSizeKb**: `number` - Total page size in kilobytes
- **performanceScore**: `number` - Lighthouse performance score (0-100)
- **accessibilityScore**: `number` - Lighthouse accessibility score (0-100)
- **seoScore**: `number` - Lighthouse SEO score (0-100)
- **bestPracticesScore**: `number` - Lighthouse best practices score (0-100)

### Security and Infrastructure
- **hasSSL**: `boolean` - Whether SSL/TLS is properly configured
- **sslIssuer**: `string` - Certificate authority that issued SSL
- **sslAgeDays**: `number` - Days remaining until SSL expiration
- **ip**: `string` - IP address of the domain
- **server**: `string` - Server software (e.g., "Apache", "nginx")
- **dnsRecords**: `string[]` - Array of DNS record types found

### Metadata and Contact Information
- **robotsTxt**: `string` - Content of robots.txt file
- **sitemapUrls**: `string[]` - URLs found in sitemap.xml
- **socialProfiles**: `string[]` - Detected social media profiles
- **emails**: `string[]` - Email addresses found on the site
- **phones**: `string[]` - Phone numbers found on the site
- **addresses**: `string[]` - Physical addresses found on the site

### Temporal and Relational Fields
- **lastCrawled**: `datetime` - Timestamp of last crawl completion
- **createdAt**: `datetime` - Record creation timestamp
- **updatedAt**: `datetime` - Last modification timestamp
- **crawlJobId**: `string` - Reference to the DomainCrawlJob that generated this analysis
- **rankingId**: `string` - Reference to the associated DomainRanking record

**Section sources**
- [DomainAnalysis.ts](file://shared/src/models/DomainAnalysis.ts)

## Relationships with Other Models

The DomainAnalysis model maintains relationships with several other key models in the system, forming a comprehensive domain intelligence framework.

### DomainCrawlJob Relationship

Each DomainAnalysis record is generated by exactly one DomainCrawlJob, establishing a one-to-one relationship. The crawl job tracks the execution context, status, and metadata of the crawling process that produced the analysis.

```mermaid
erDiagram
DOMAIN_CRAWL_JOB ||--o{ DOMAIN_ANALYSIS : "produces"
DOMAIN_CRAWL_JOB {
string jobId PK
string domain
string status
string priority
number retryCount
datetime createdAt
datetime startedAt
datetime completedAt
string workerId
string error
}
DOMAIN_ANALYSIS {
string domain PK
string crawlJobId FK
string rankingId FK
datetime lastCrawled
datetime createdAt
datetime updatedAt
}
```

**Diagram sources**
- [DomainAnalysis.ts](file://shared/src/models/DomainAnalysis.ts)
- [DomainCrawlJob.ts](file://shared/src/models/DomainCrawlJob.ts)

**Section sources**
- [DomainAnalysis.ts](file://shared/src/models/DomainAnalysis.ts)
- [DomainCrawlJob.ts](file://shared/src/models/DomainCrawlJob.ts)

### DomainRanking Relationship

The DomainAnalysis model incorporates ranking data through a one-to-one relationship with DomainRanking. This allows the analysis to include traffic, authority, and competitive positioning metrics alongside technical analysis.

```mermaid
erDiagram
DOMAIN_ANALYSIS ||--|| DOMAIN_RANKING : "includes"
DOMAIN_ANALYSIS {
string domain PK
string rankingId FK
}
DOMAIN_RANKING {
string domain PK
number globalRank
number countryRank
number categoryRank
number trafficTrend
number engagementScore
number backlinkAuthority
number socialAuthority
number estimatedVisits
datetime lastUpdated
}
```

**Diagram sources**
- [DomainAnalysis.ts](file://shared/src/models/DomainAnalysis.ts)
- [DomainRanking.ts](file://shared/src/models/DomainRanking.ts)

**Section sources**
- [DomainAnalysis.ts](file://shared/src/models/DomainAnalysis.ts)
- [DomainRanking.ts](file://shared/src/models/DomainRanking.ts)

## Data Validation and Business Logic

The DomainAnalysis model enforces strict validation rules and business logic to ensure data quality and consistency across the platform.

### Validation Rules

The model implements comprehensive validation through the DomainDataValidator service, ensuring all fields meet specific criteria before persistence:

- **Domain format**: Must be a valid domain name according to RFC 1035
- **URL fields**: Must be properly formatted URLs when applicable
- **Numeric ranges**: Scores constrained to 0-100, time values to reasonable limits
- **Array limits**: Category and tag arrays limited to 10 items each
- **String lengths**: Title (max 70 chars), description (max 160 chars) for SEO compliance
- **Date validation**: Temporal fields must be valid ISO 8601 timestamps
- **Content hash**: Must be valid SHA-256 format when present

### Business Logic

Several business rules govern the creation and updating of DomainAnalysis records:

- **Immutable domain field**: The domain name cannot be changed after creation
- **Timestamp rules**: createdAt is set on creation, updatedAt on every modification, lastCrawled only when analysis is updated from a new crawl
- **Score calculations**: Composite scores are calculated from component metrics with weighted formulas
- **Category assignment**: Primary category is selected based on highest confidence score from classification algorithms
- **Content change detection**: Content hash comparison triggers alerts for significant content changes
- **SSL expiration alerts**: sslAgeDays < 30 triggers renewal notifications
- **Performance thresholds**: loadTimeMs > 3000 or performanceScore < 50 triggers performance alerts

**Section sources**
- [DomainAnalysis.ts](file://shared/src/models/DomainAnalysis.ts)
- [DomainDataValidator.ts](file://services/worker/src/validation/DomainDataValidator.ts)
- [ValidationPipeline.ts](file://services/worker/src/validation/ValidationPipeline.ts)

## Lifecycle and Population Process

The DomainAnalysis entity follows a well-defined lifecycle from creation to retirement, with specific processes for data population and updates.

### Creation and Population Flow

```mermaid
sequenceDiagram
participant Scheduler as "Scheduler Service"
participant Queue as "Job Queue"
participant Worker as "Worker Service"
participant Analyzers as "Analysis Modules"
participant DB as "ScyllaDB"
participant Cache as "Redis"
Scheduler->>Queue : Enqueue crawl job
Queue->>Worker : Deliver job
Worker->>Analyzers : Initialize analysis pipeline
loop For each analyzer module
Analyzers->>Analyzers : Execute analysis
end
Analyzers->>Worker : Return analysis results
Worker->>Worker : Validate and merge results
Worker->>DB : Persist DomainAnalysis
DB-->>Worker : Confirmation
Worker->>Cache : Update cache
Cache-->>Worker : Confirmation
Worker->>Queue : Mark job complete
```

**Diagram sources**
- [DomainProcessingPipeline.ts](file://services/worker/src/pipeline/DomainProcessingPipeline.ts)
- [DomainAnalysis.ts](file://shared/src/models/DomainAnalysis.ts)

**Section sources**
- [DomainProcessingPipeline.ts](file://services/worker/src/pipeline/DomainProcessingPipeline.ts)

### Analysis Modules

Multiple specialized analyzers contribute data to the DomainAnalysis record:

- **AdvancedContentAnalyzer**: Extracts semantic content, keywords, and topics
- **DomainInfoAnalyzer**: Collects domain registration and ownership information
- **HomepageAnalyzer**: Parses HTML structure, meta tags, and content
- **PerformanceAuditor**: Measures load performance and Lighthouse metrics
- **SSLAnalyzer**: Validates SSL/TLS configuration and certificate details

Each analyzer module follows a consistent interface and contributes its specific data subset to the final DomainAnalysis object.

**Section sources**
- [AdvancedContentAnalyzer.ts](file://services/worker/src/crawler/analyzers/AdvancedContentAnalyzer.ts)
- [DomainInfoAnalyzer.ts](file://services/worker/src/crawler/analyzers/DomainInfoAnalyzer.ts)
- [HomepageAnalyzer.ts](file://services/worker/src/crawler/analyzers/HomepageAnalyzer.ts)
- [PerformanceAuditor.ts](file://services/worker/src/crawler/analyzers/PerformanceAuditor.ts)
- [SSLAnalyzer.ts](file://services/worker/src/crawler/analyzers/SSLAnalyzer.ts)

## Querying and Access Patterns

The DomainAnalysis model supports various access patterns optimized for different use cases across the application.

### Primary Query Patterns

- **Domain lookup**: Retrieve analysis by domain name (most common)
- **Category filtering**: Find domains in specific categories
- **Performance filtering**: Identify domains with specific performance characteristics
- **Content search**: Full-text search across domain content fields
- **Temporal queries**: Find recently analyzed domains or track changes over time

### Web Application Access

The Web Application accesses DomainAnalysis data through the DomainAnalysisService, which provides a clean API interface:

```mermaid
flowchart TD
WebApp[Web Application] --> |HTTP Request| API[API Layer]
API --> |Service Call| Service[DomainAnalysisService]
Service --> |Cache Check| Redis[Redis Cache]
Redis --> |Cache Hit| Service
Redis --> |Cache Miss| Service
Service --> |Database Query| Scylla[ScyllaDB]
Scylla --> |Data| Service
Service --> |Cache Update| Redis
Service --> |Response| API
API --> |HTTP Response| WebApp
```

**Diagram sources**
- [DomainAnalysisPage.tsx](file://services/web-app/src/components/DomainAnalysisPage.tsx)
- [RedisClient.ts](file://shared/src/database/RedisClient.ts)
- [ScyllaClient.ts](file://shared/src/database/ScyllaClient.ts)

**Section sources**
- [DomainAnalysisPage.tsx](file://services/web-app/src/components/DomainAnalysisPage.tsx)

## Performance and Storage Considerations

The DomainAnalysis model is designed with performance and storage efficiency as primary concerns, given the high volume of domain data processed.

### ScyllaDB Optimization

- **Partition key**: domain field enables efficient point queries
- **Clustering columns**: createdAt and lastCrawled for time-based queries
- **Compression**: LZ4 compression reduces storage footprint
- **TTL**: Automatic expiration of stale records after 90 days
- **Replication**: RF=3 for high availability
- **Read/write capacity**: Auto-scaling based on traffic patterns

### MariaDB Optimization

- **Indexes**: Secondary indexes on frequently queried fields (category, performanceScore, lastCrawled)
- **Partitioning**: Range partitioning by createdAt for efficient time-based queries
- **Storage engine**: InnoDB with optimized buffer pool settings
- **Query optimization**: Prepared statements and connection pooling

### Manticore Search Optimization

- **Full-text indexes**: On title, description, content, and tags fields
- **Real-time indexing**: Near-instant search availability after crawl completion
- **Relevance scoring**: Custom ranking functions for search results
- **Faceting**: Fast category and attribute filtering

**Section sources**
- [ScyllaSchema.ts](file://services/worker/src/database/schemas/ScyllaSchema.ts)
- [MariaSchema.ts](file://services/worker/src/database/schemas/MariaSchema.ts)
- [ManticoreSchema.ts](file://services/worker/src/database/schemas/ManticoreSchema.ts)

## Caching Strategy

A multi-layer caching strategy ensures optimal performance for DomainAnalysis data access.

### Redis Cache Implementation

```mermaid
flowchart TD
Client[Web Client] --> |Request| WebApp[Web Application]
WebApp --> |Check| RedisPrimary[Primary Redis Cache]
RedisPrimary --> |Hit| WebApp
RedisPrimary --> |Miss| WebApp
WebApp --> |Check| RedisSecondary[Secondary Redis Cache]
RedisSecondary --> |Hit| WebApp
RedisSecondary --> |Miss| WebApp
WebApp --> |Query| Scylla[ScyllaDB]
Scylla --> |Data| WebApp
WebApp --> |Update| RedisSecondary
WebApp --> |Update| RedisPrimary
WebApp --> |Response| Client
```

**Diagram sources**
- [RedisClient.ts](file://shared/src/database/RedisClient.ts)

**Section sources**
- [RedisClient.ts](file://shared/src/database/RedisClient.ts)

### Cache Layers

- **Primary cache**: In-memory Redis instance with 15-minute TTL for frequently accessed domains
- **Secondary cache**: Persistent Redis instance with 24-hour TTL for less frequently accessed domains
- **Cache warming**: Pre-populate cache for trending domains and search results
- **Cache invalidation**: Triggered on domain recrawl completion or significant content changes
- **Cache stampede protection**: Using probabilistic early expiration to prevent thundering herd

## Data Retention Policies

The DomainAnalysis model follows strict data retention policies to balance data availability with storage costs and privacy requirements.

### Retention Rules

- **Active domains**: Retained for 90 days from lastCrawled timestamp
- **Inactive domains**: Retained for 180 days from lastCrawled timestamp
- **High-value domains**: Premium domains retained for 365 days
- **Deleted domains**: Soft-deleted records retained for 30 days before permanent removal

### Archival Process

- **Daily**: Move records older than 60 days to cold storage
- **Weekly**: Archive records older than 90 days to compressed format
- **Monthly**: Purge permanently expired records
- **On-demand**: Manual archival for compliance or legal requirements

**Section sources**
- [DomainAnalysis.ts](file://shared/src/models/DomainAnalysis.ts)
- [ScyllaSchema.ts](file://services/worker/src/database/schemas/ScyllaSchema.ts)

## Usage Examples

### Web Application Display

The DomainAnalysis model powers the domain analysis pages in the Web Application, providing comprehensive insights to users:

```mermaid
flowchart TD
Page[DomainAnalysisPage] --> |Fetch Data| Service[DomainAnalysisService]
Service --> |Get Analysis| Cache[Redis Cache]
Cache --> |Return Data| Service
Service --> |Display| Page
Page --> |Render Sections| Identification[Identification Section]
Page --> |Render Sections| Content[Content Analysis Section]
Page --> |Render Sections| Performance[Performance Metrics Section]
Page --> |Render Sections| Security[Security & Infrastructure Section]
Page --> |Render Sections| Contacts[Contact Information Section]
```

**Diagram sources**
- [DomainAnalysisPage.tsx](file://services/web-app/src/components/DomainAnalysisPage.tsx)

**Section sources**
- [DomainAnalysisPage.tsx](file://services/web-app/src/components/DomainAnalysisPage.tsx)

### Worker Service Population

The Worker service populates DomainAnalysis records through a coordinated pipeline of analysis modules:

```mermaid
flowchart TD
Pipeline[DomainProcessingPipeline] --> |Initialize| Context[Analysis Context]
Context --> |Distribute| ContentAnalyzer[Content Analyzer]
Context --> |Distribute| PerformanceAnalyzer[Performance Analyzer]
Context --> |Distribute| SecurityAnalyzer[Security Analyzer]
Context --> |Distribute| MetadataAnalyzer[Metadata Analyzer]
ContentAnalyzer --> |Return| Context
PerformanceAnalyzer --> |Return| Context
SecurityAnalyzer --> |Return| Context
MetadataAnalyzer --> |Return| Context
Context --> |Aggregate| Pipeline
Pipeline --> |Validate| Validator[DomainDataValidator]
Validator --> |Approved| Pipeline
Validator --> |Rejected| Pipeline
Pipeline --> |Persist| Scylla[ScyllaDB]
Pipeline --> |Update| Redis[Redis Cache]
```

**Diagram sources**
- [DomainProcessingPipeline.ts](file://services/worker/src/pipeline/DomainProcessingPipeline.ts)

**Section sources**
- [DomainProcessingPipeline.ts](file://services/worker/src/pipeline/DomainProcessingPipeline.ts)