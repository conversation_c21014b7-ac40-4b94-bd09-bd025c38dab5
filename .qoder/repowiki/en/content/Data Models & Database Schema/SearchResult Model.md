# SearchResult Model

<cite>
**Referenced Files in This Document**   
- [SearchResult.ts](file://shared/src/models/SearchResult.ts)
- [DomainTypes.ts](file://shared/src/models/types/DomainTypes.ts)
- [ManticoreClient.ts](file://shared/src/database/ManticoreClient.ts)
- [DomainSearchService.ts](file://services/web-app/src/services/DomainSearchService.ts)
- [init.sql](file://database/manticore/init.sql)
- [WorkerDatabaseManager.ts](file://services/worker/src/database/WorkerDatabaseManager.ts)
- [SearchPage.tsx](file://services/web-app/src/components/SearchPage.tsx)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [SearchResult Entity Structure](#searchresult-entity-structure)
3. [Manticore Search Integration](#manticore-search-integration)
4. [Field Composition and Indexing Strategy](#field-composition-and-indexing-strategy)
5. [Search Result Transformation Process](#search-result-transformation-process)
6. [Faceted Filtering and Sorting](#faceted-filtering-and-sorting)
7. [Pagination Strategy](#pagination-strategy)
8. [Result Highlighting and Rendering](#result-highlighting-and-rendering)
9. [Performance Considerations](#performance-considerations)
10. [Search Query Examples](#search-query-examples)

## Introduction
The SearchResult model serves as the central data structure for delivering domain search results in the domain ranking system. It encapsulates search results, pagination metadata, filtering information, and performance metrics to support efficient rendering in the Web Application. The model acts as an intermediary between Manticore Search indexes and the frontend UI components, transforming raw database records into a structured format optimized for display and interaction.

**Section sources**
- [SearchResult.ts](file://shared/src/models/SearchResult.ts#L1-L38)
- [DomainTypes.ts](file://shared/src/models/types/DomainTypes.ts#L160-L181)

## SearchResult Entity Structure
The SearchResult class represents a paginated collection of domain search results with associated metadata. It provides a standardized interface for search operations across the application.

```mermaid
classDiagram
class SearchResult {
+domains : unknown[]
+totalResults : number
+currentPage : number
+totalPages : number
+filters : Record<string, unknown>
+facets : Record<string, unknown>
+searchTime : number
+constructor(data : SearchResultData)
}
class SearchResultData {
+domains? : unknown[]
+totalResults? : number
+currentPage? : number
+totalPages? : number
+filters? : Record<string, unknown>
+facets? : Record<string, unknown>
+searchTime? : number
}
SearchResult --> SearchResultData : "constructed from"
```

**Diagram sources**
- [SearchResult.ts](file://shared/src/models/SearchResult.ts#L1-L38)
- [DomainTypes.ts](file://shared/src/models/types/DomainTypes.ts#L160-L181)

**Section sources**
- [SearchResult.ts](file://shared/src/models/SearchResult.ts#L1-L38)
- [DomainTypes.ts](file://shared/src/models/types/DomainTypes.ts#L160-L181)

## Manticore Search Integration
The SearchResult model is tightly integrated with Manticore Search, serving as the output format for search queries executed against the domains_index. The integration enables full-text search capabilities, faceted filtering, and relevance scoring.

```mermaid
sequenceDiagram
participant WebApp as "Web Application"
participant DomainSearchService as "DomainSearchService"
participant ManticoreClient as "ManticoreClient"
participant Manticore as "Manticore Search"
WebApp->>DomainSearchService : searchDomains(params)
DomainSearchService->>ManticoreClient : searchDomains(params)
ManticoreClient->>Manticore : POST /search with query
Manticore-->>ManticoreClient : Return search response
ManticoreClient->>ManticoreClient : parseSearchResponse()
ManticoreClient-->>DomainSearchService : Return structured results
DomainSearchService->>DomainSearchService : Transform to SearchResult
DomainSearchService-->>WebApp : Return SearchResult object
```

**Diagram sources**
- [DomainSearchService.ts](file://services/web-app/src/services/DomainSearchService.ts#L31-L135)
- [ManticoreClient.ts](file://shared/src/database/ManticoreClient.ts#L117-L182)
- [ManticoreClient.ts](file://shared/src/database/ManticoreClient.ts#L390-L454)

**Section sources**
- [DomainSearchService.ts](file://services/web-app/src/services/DomainSearchService.ts#L31-L135)
- [ManticoreClient.ts](file://shared/src/database/ManticoreClient.ts#L117-L182)

## Field Composition and Indexing Strategy
The SearchResult model's fields are optimized for Manticore Search indexing, with a clear distinction between full-text fields and attributes. The indexing strategy supports efficient querying and faceted filtering.

```mermaid
erDiagram
SEARCH_RESULT {
array domains PK
number totalResults
number currentPage
number totalPages
object filters
object facets
number searchTime
}
MANTICORE_INDEX {
text domain
text title
text description
string category
string country
multi technologies
int global_rank
float overall_score
float performance_score
float security_score
float seo_score
float technical_score
float backlink_score
bigint traffic_estimate
string ssl_grade
}
SEARCH_RESULT ||--o{ MANTICORE_INDEX : "contains"
```

**Diagram sources**
- [init.sql](file://database/manticore/init.sql#L5-L57)
- [WorkerDatabaseManager.ts](file://services/worker/src/database/WorkerDatabaseManager.ts#L664-L713)
- [ManticoreClient.ts](file://shared/src/database/ManticoreClient.ts#L390-L454)

**Section sources**
- [init.sql](file://database/manticore/init.sql#L5-L57)
- [WorkerDatabaseManager.ts](file://services/worker/src/database/WorkerDatabaseManager.ts#L664-L713)

## Search Result Transformation Process
The transformation process converts raw Manticore Search responses into the SearchResult format, enriching the data for frontend consumption. This process includes parsing, scoring, and structural transformation.

```mermaid
flowchart TD
A["Raw Manticore Response"] --> B["Parse hits and aggregations"]
B --> C["Extract domain documents"]
C --> D["Map to SearchResult structure"]
D --> E["Calculate pagination metadata"]
E --> F["Apply result highlighting"]
F --> G["Transform scores and metrics"]
G --> H["Return SearchResult object"]
```

**Diagram sources**
- [ManticoreClient.ts](file://shared/src/database/ManticoreClient.ts#L390-L454)
- [DomainSearchService.ts](file://services/web-app/src/services/DomainSearchService.ts#L99-L135)
- [SearchResult.ts](file://shared/src/models/SearchResult.ts#L1-L38)

**Section sources**
- [ManticoreClient.ts](file://shared/src/database/ManticoreClient.ts#L390-L454)
- [DomainSearchService.ts](file://services/web-app/src/services/DomainSearchService.ts#L99-L135)

## Faceted Filtering and Sorting
The SearchResult model supports comprehensive faceted filtering and sorting capabilities, enabling users to refine search results based on multiple criteria. The system implements both exact match filters and range-based constraints.

```mermaid
classDiagram
class SearchFilters {
+category : string
+country : string
+technologies : string[]
+sslGrade : string
+minRank : number
+maxRank : number
+minScore : number
+maxScore : number
}
class SortingOptions {
+rank : "global_rank asc"
+score : "overall_score desc"
+performance : "performance_score desc"
+security : "security_score desc"
+seo : "seo_score desc"
+traffic : "traffic_estimate desc"
}
class FacetData {
+category : Array<{value : string, count : number}>
+country : Array<{value : string, count : number}>
+technologies : Array<{value : string, count : number}>
+ssl_grade : Array<{value : string, count : number}>
}
SearchResult --> SearchFilters : "applies"
SearchResult --> SortingOptions : "supports"
SearchResult --> FacetData : "returns"
```

**Diagram sources**
- [DomainSearchService.ts](file://services/web-app/src/services/DomainSearchService.ts#L99-L135)
- [ManticoreClient.ts](file://shared/src/database/ManticoreClient.ts#L184-L249)
- [DomainTypes.ts](file://shared/src/models/types/DomainTypes.ts#L130-L159)

**Section sources**
- [DomainSearchService.ts](file://services/web-app/src/services/DomainSearchService.ts#L99-L135)
- [ManticoreClient.ts](file://shared/src/database/ManticoreClient.ts#L184-L249)

## Pagination Strategy
The SearchResult model implements a robust pagination strategy that supports large result sets with efficient navigation. The system calculates pagination metadata based on total results and page size.

```mermaid
flowchart LR
A["Request with page=2, limit=20"] --> B["Calculate offset=20"]
B --> C["Execute Manticore search"]
C --> D["Get total results count"]
D --> E["Calculate totalPages"]
E --> F["Create pagination object"]
F --> G["Return with SearchResult"]
subgraph Pagination Object
H[page: 2]
I[limit: 20]
J[total: 1500]
K[totalPages: 75]
L[hasNext: true]
M[hasPrev: true]
end
```

**Diagram sources**
- [DomainSearchService.ts](file://services/web-app/src/services/DomainSearchService.ts#L99-L135)
- [web.ts](file://services/web-app/src/app/routes/web.ts#L53-L103)
- [SearchPage.tsx](file://services/web-app/src/components/SearchPage.tsx#L732-L778)

**Section sources**
- [DomainSearchService.ts](file://services/web-app/src/services/DomainSearchService.ts#L99-L135)
- [web.ts](file://services/web-app/src/app/routes/web.ts#L53-L103)

## Result Highlighting and Rendering
The SearchResult model supports result highlighting and rendering in the Web Application, with the data structure optimized for UI components. The transformation process prepares data for visual presentation.

```mermaid
flowchart TD
A["SearchResult Object"] --> B["Extract domains array"]
B --> C["Map to DomainResult interface"]
C --> D["Format traffic estimates"]
D --> E["Format dates"]
E --> F["Apply conditional styling"]
F --> G["Render SearchResultCard components"]
subgraph UI Components
H[SearchResults]
I[SearchResultCard]
J[Pagination]
K[SearchStats]
end
G --> H
H --> I
H --> J
H --> K
```

**Diagram sources**
- [SearchPage.tsx](file://services/web-app/src/components/SearchPage.tsx#L732-L828)
- [SearchResults.tsx](file://services/web-app/src/components/Search/SearchResults.tsx#L0-L63)
- [DomainSearchService.ts](file://services/web-app/src/services/DomainSearchService.ts#L135-L195)

**Section sources**
- [SearchPage.tsx](file://services/web-app/src/components/SearchPage.tsx#L732-L828)
- [SearchResults.tsx](file://services/web-app/src/components/Search/SearchResults.tsx#L0-L63)

## Performance Considerations
The SearchResult implementation is optimized for high-concurrency search operations, with considerations for response time, memory usage, and database load. The system employs caching and efficient query patterns.

```mermaid
flowchart LR
A["High-Concurrency Search Operations"] --> B["Manticore Search Optimization"]
B --> C["Columnar Storage Engine"]
C --> D["Indexing Strategy"]
D --> E["Query Caching"]
E --> F["Connection Pooling"]
F --> G["Result Caching"]
G --> H["Rate Limiting"]
H --> I["Load Balancing"]
subgraph Performance Metrics
J[searchTime: 45ms]
K[took: 38ms]
L[totalResults: 1500]
M[resultsPerPage: 20]
end
```

**Diagram sources**
- [ManticoreClient.ts](file://shared/src/database/ManticoreClient.ts#L52-L117)
- [DomainSearchService.ts](file://services/web-app/src/services/DomainSearchService.ts#L135-L195)
- [WorkerDatabaseManager.ts](file://services/worker/src/database/WorkerDatabaseManager.ts#706-L776)

**Section sources**
- [ManticoreClient.ts](file://shared/src/database/ManticoreClient.ts#L52-L117)
- [DomainSearchService.ts](file://services/web-app/src/services/DomainSearchService.ts#L135-L195)

## Search Query Examples
The DomainSearchService provides examples of search queries and result processing, demonstrating the capabilities of the SearchResult model in various scenarios.

```mermaid
sequenceDiagram
participant Client as "Client"
participant API as "API Route"
participant Service as "DomainSearchService"
participant Manticore as "ManticoreClient"
Client->>API : GET /api/domains/search?q=tech&category=technology
API->>Service : searchDomains(params)
Service->>Manticore : buildSearchQuery(params)
Manticore->>Manticore : Execute Manticore search
Manticore-->>Service : Return search response
Service->>Service : Transform to SearchResult
Service-->>API : Return SearchResult
API-->>Client : JSON response with results
Note over Client,Manticore : Example : Technology category search with query
```

**Diagram sources**
- [search.ts](file://services/web-app/src/app/routes/api/search.ts#L0-L94)
- [DomainSearchService.ts](file://services/web-app/src/services/DomainSearchService.ts#L31-L97)
- [ManticoreClient.ts](file://shared/src/database/ManticoreClient.ts#L184-L249)

**Section sources**
- [search.ts](file://services/web-app/src/app/routes/api/search.ts#L0-L94)
- [DomainSearchService.ts](file://services/web-app/src/services/DomainSearchService.ts#L31-L97)