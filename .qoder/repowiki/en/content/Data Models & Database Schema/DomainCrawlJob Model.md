# DomainCrawlJob Model

<cite>
**Referenced Files in This Document**   
- [DomainCrawlJob.ts](file://shared/src/models/DomainCrawlJob.ts)
- [mariadb/init.sql](file://database/mariadb/init.sql)
- [WorkerJobQueue.ts](file://services/worker/src/queue/WorkerJobQueue.ts)
- [CrawlJobManager.ts](file://services/worker/src/scheduler/CrawlJobManager.ts)
- [DomainAnalysis.ts](file://shared/src/models/DomainAnalysis.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Core Field Definitions](#core-field-definitions)
3. [MariaDB Schema Implementation](#mariadb-schema-implementation)
4. [Job Lifecycle and State Transitions](#job-lifecycle-and-state-transitions)
5. [Worker Service Integration](#worker-service-integration)
6. [Error Handling and Retry Mechanisms](#error-handling-and-retry-mechanisms)
7. [Integration with DomainAnalysis Model](#integration-with-domainanalysis-model)
8. [Indexing and Query Performance](#indexing-and-query-performance)
9. [Code Examples from Worker Service](#code-examples-from-worker-service)
10. [Performance Considerations](#performance-considerations)

## Introduction

The DomainCrawlJob model represents a fundamental component in the domain ranking system, serving as the primary mechanism for managing and tracking domain crawling operations. This model facilitates the creation, execution, and monitoring of crawl jobs initiated by the Web Application and processed by the Worker service. The DomainCrawlJob entity encapsulates essential metadata about crawl operations, including job status, priority levels, retry configurations, and execution timestamps. It acts as the central coordination point between the web interface that requests crawls and the backend worker processes that execute them, ensuring reliable and efficient domain analysis across various crawl types such as full, quick, security, and performance assessments.

**Section sources**
- [DomainCrawlJob.ts](file://shared/src/models/DomainCrawlJob.ts#L1-L108)

## Core Field Definitions

The DomainCrawlJob model contains comprehensive fields that define the characteristics and state of each crawl operation. These fields provide granular control over job execution and enable detailed monitoring and analysis of the crawling process.

### Job Identification and Domain Targeting
The model includes essential identification fields that uniquely identify each crawl job and specify its target domain:
- **jobId**: Unique identifier for the crawl job, automatically generated if not provided
- **domain**: Target domain to be crawled, specified as a string
- **userAgent**: User agent string used during crawling operations

### Crawl Configuration Parameters
These fields define the nature and requirements of the crawl operation:
- **crawlType**: Type of crawl to perform, with options including 'full', 'quick', 'security', or 'performance'
- **priority**: Job priority level, which can be 'high', 'medium', or 'low', influencing queue processing order
- **pagesToCrawl**: Array of page paths to crawl, defaulting to ['homepage']

### Execution Status and Lifecycle Tracking
Comprehensive fields track the job's progress through its lifecycle:
- **status**: Current job status, which can be 'pending', 'processing', 'completed', or 'failed'
- **scheduledAt**: Timestamp when the job was initially scheduled
- **startedAt**: Timestamp when job processing began (nullable)
- **completedAt**: Timestamp when job processing completed or failed (nullable)

### Retry Management and Error Handling
Robust retry mechanisms ensure reliability in the face of transient failures:
- **retryCount**: Current number of retry attempts made for this job
- **maxRetries**: Maximum number of retry attempts allowed before permanent failure
- **errorMessage**: Error message if the job failed (nullable)

**Section sources**
- [DomainCrawlJob.ts](file://shared/src/models/DomainCrawlJob.ts#L8-L104)

## MariaDB Schema Implementation

While the primary storage for DomainCrawlJob entities appears to be in ScyllaDB based on code references, the MariaDB schema includes related tables that support the overall domain analysis system. The database schema is designed to efficiently store and retrieve crawl job data with appropriate indexing for common query patterns.

```mermaid
erDiagram
domain_crawl_jobs {
string job_id PK
string domain
string crawl_type
string priority
string status
int retry_count
int max_retries
timestamp scheduled_at
timestamp started_at
timestamp completed_at
string error_message
string user_agent
}
domain_analysis {
string domain PK
float ranking_score
json category_data
json technical_metrics
timestamp last_crawled
string crawl_job_id FK
}
domain_crawl_jobs ||--o{ domain_analysis : "produces"
```

**Diagram sources**
- [mariadb/init.sql](file://database/mariadb/init.sql#L1-L136)
- [DomainCrawlJob.ts](file://shared/src/models/DomainCrawlJob.ts#L8-L104)
- [DomainAnalysis.ts](file://shared/src/models/DomainAnalysis.ts)

## Job Lifecycle and State Transitions

The DomainCrawlJob model implements a well-defined lifecycle with explicit state transitions that ensure proper job execution and error recovery. The state machine governs how jobs progress from creation to completion or failure.

```mermaid
stateDiagram-v2
[*] --> Pending
Pending --> Queued : "scheduled"
Queued --> Processing : "dequeued"
Processing --> Completed : "success"
Processing --> Failed : "error"
Failed --> Pending : "retryable"
Failed --> Completed : "max retries exceeded"
Pending --> Cancelled : "manual cancellation"
state Processing {
[*] --> DNS_Lookup
DNS_Lookup --> Robots_Check
Robots_Check --> SSL_Analysis
SSL_Analysis --> Content_Analysis
Content_Analysis --> Finalization
Finalization --> [*]
}
```

**Diagram sources**
- [DomainCrawlJob.ts](file://shared/src/models/DomainCrawlJob.ts#L65-L104)
- [CrawlJobManager.ts](file://services/worker/src/scheduler/CrawlJobManager.ts#L250-L350)

## Worker Service Integration

The DomainCrawlJob model is tightly integrated with the Worker service's job queue system, enabling distributed processing of crawl operations. The WorkerJobQueue class handles the queuing, distribution, and execution of crawl jobs across multiple worker instances.

### Job Queue Architecture
The integration follows a producer-consumer pattern where the Web Application creates jobs (producer) and Worker services process them (consumers). The Redis-SMQ message broker facilitates reliable job delivery with persistence and retry capabilities.

```mermaid
flowchart TD
A[Web Application] --> |Creates Job| B[Redis-SMQ Queue]
B --> C{Worker Instances}
C --> D[Process Crawl Job]
D --> E[Update Job Status]
E --> F[Store Results]
F --> G[DomainAnalysis Model]
```

**Diagram sources**
- [WorkerJobQueue.ts](file://services/worker/src/queue/WorkerJobQueue.ts#L1-L799)
- [CrawlJobManager.ts](file://services/worker/src/scheduler/CrawlJobManager.ts#L1-L821)

## Error Handling and Retry Mechanisms

The DomainCrawlJob model incorporates sophisticated error handling and retry mechanisms to ensure resilience against transient failures and network issues during crawl operations.

### Retry Policy Implementation
The system implements exponential backoff retry logic with configurable policies based on failure classification:

```mermaid
flowchart TD
A[Job Failure] --> B{Failure Type}
B --> |Timeout| C[Retry after 5s]
B --> |Resource Limit| D[Retry after 30s]
B --> |Network Error| E[Retry after 10s]
B --> |Validation Error| F[No Retry - Permanent Failure]
C --> G[Exponential Backoff]
D --> G
E --> G
G --> H[Apply Jitter]
H --> I[Schedule Retry]
I --> J[Update retryCount]
```

The retry mechanism is governed by the following rules:
- Jobs can be retried up to **maxRetries** times (default: 3)
- Retry delay follows exponential backoff: baseDelay × backoffMultiplier^(retryCount-1)
- Jitter is applied to prevent thundering herd problems
- Different failure types have customized retry delays

**Section sources**
- [WorkerJobQueue.ts](file://services/worker/src/queue/WorkerJobQueue.ts#L500-L650)
- [DomainCrawlJob.ts](file://shared/src/models/DomainCrawlJob.ts#L95-L104)

## Integration with DomainAnalysis Model

Upon successful completion of a DomainCrawlJob, the results are integrated into the DomainAnalysis model, which serves as the authoritative source of domain intelligence within the system.

### Data Flow from Crawl to Analysis
The integration process follows a well-defined sequence:

```mermaid
sequenceDiagram
participant WebApp as Web Application
participant CrawlManager as CrawlJobManager
participant Worker as Worker Service
participant DB as Database
participant Analysis as DomainAnalysis
WebApp->>CrawlManager : createCrawlJob(request)
CrawlManager->>DB : saveJobStatus(pending)
CrawlManager->>Worker : scheduleJob(jobData)
Worker->>Worker : handleDomainCrawlJob()
Worker->>Worker : simulateCrawlProcessing()
alt Success
Worker->>CrawlManager : updateJobStatus(completed)
CrawlManager->>DB : saveJobStatus(completed)
CrawlManager->>Analysis : updateDomainAnalysis(results)
Analysis->>DB : store domain analysis data
CrawlManager->>WebApp : notify completion
else Failure
Worker->>CrawlManager : updateJobStatus(failed)
CrawlManager->>Worker : handleJobFailure()
CrawlManager->>WebApp : notify failure
end
```

**Diagram sources**
- [CrawlJobManager.ts](file://services/worker/src/scheduler/CrawlJobManager.ts#L250-L400)
- [DomainAnalysis.ts](file://shared/src/models/DomainAnalysis.ts)

## Indexing and Query Performance

The DomainCrawlJob implementation includes optimized indexing strategies to support efficient querying and monitoring of job status across high-volume workloads.

### Primary Indexing Strategy
The system employs a composite indexing approach to optimize common query patterns:

```mermaid
erDiagram
domain_crawl_jobs {
string job_id PK "Primary Key"
string domain "Index: domain_status"
string status "Index: domain_status, status_created"
timestamp scheduled_at "Index: status_created, scheduled_at"
timestamp created_at "Index: status_created"
string crawl_type "Index: type_priority"
string priority "Index: type_priority"
}
index "domain_status" on domain_crawl_jobs(domain, status)
index "status_created" on domain_crawl_jobs(status, scheduled_at)
index "type_priority" on domain_crawl_jobs(crawl_type, priority)
index "scheduled_at" on domain_crawl_jobs(scheduled_at)
```

### Common Query Patterns
The indexing strategy supports the following key query patterns:
- **Job monitoring by domain**: `SELECT * FROM domain_crawl_jobs WHERE domain = ? ORDER BY scheduled_at DESC`
- **Active job tracking**: `SELECT * FROM domain_crawl_jobs WHERE status IN ('pending', 'processing')`
- **Performance analysis**: `SELECT AVG(completedAt - startedAt) FROM domain_crawl_jobs WHERE status = 'completed' AND crawl_type = ?`
- **Error rate monitoring**: `SELECT COUNT(*) FROM domain_crawl_jobs WHERE status = 'failed' AND scheduled_at > ?`

**Section sources**
- [mariadb/init.sql](file://database/mariadb/init.sql#L1-L136)
- [CrawlJobManager.ts](file://services/worker/src/scheduler/CrawlJobManager.ts#L150-L200)

## Code Examples from Worker Service

The Worker service contains practical implementations demonstrating how DomainCrawlJob instances are created, processed, and updated during execution.

### Job Creation Example
```mermaid
flowchart TD
A[createCrawlJob] --> B[Generate UUID]
B --> C[Create Job Status]
C --> D[Save to Database]
D --> E[Schedule Job]
E --> F[Update Status to Queued]
```

**Section sources**
- [CrawlJobManager.ts](file://services/worker/src/scheduler/CrawlJobManager.ts#L400-L450)

### Status Update Implementation
The model provides dedicated methods for managing job state transitions:

```mermaid
classDiagram
class DomainCrawlJob {
+jobId : string
+domain : string
+status : string
+markStarted() : void
+markCompleted() : void
+markFailed(errorMessage : string) : void
+canRetry() : boolean
+incrementRetry() : void
}
```

**Diagram sources**
- [DomainCrawlJob.ts](file://shared/src/models/DomainCrawlJob.ts#L65-L104)

## Performance Considerations

The DomainCrawlJob implementation incorporates several performance optimizations to handle high-throughput job processing efficiently.

### Batch Processing Capabilities
The system supports batch job processing to optimize resource utilization:

```mermaid
flowchart TD
A[Batch Job Request] --> B{Process in Batches}
B --> C[Batch 1: Jobs 1-50]
C --> D[Delay 100ms]
D --> E[Batch 2: Jobs 51-100]
E --> F[Delay 100ms]
F --> G[Continue...]
G --> H[Return Results]
```

### Scalability Features
Key performance characteristics include:
- **Concurrency control**: Configurable concurrency levels per queue
- **Rate limiting**: Built-in delay between batch operations
- **Memory efficiency**: In-memory job tracking with periodic persistence
- **Asynchronous processing**: Non-blocking job execution and status updates
- **Connection pooling**: Reused database connections for status updates

The system is designed to handle thousands of concurrent crawl jobs while maintaining low latency for status queries and efficient resource utilization across worker nodes.

**Section sources**
- [WorkerJobQueue.ts](file://services/worker/src/queue/WorkerJobQueue.ts#L800-L1055)
- [CrawlJobManager.ts](file://services/worker/src/scheduler/CrawlJobManager.ts#L500-L600)