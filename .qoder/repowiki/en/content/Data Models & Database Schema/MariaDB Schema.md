# MariaDB Schema

<cite>
**Referenced Files in This Document**   
- [init.sql](file://database/mariadb/init.sql)
- [MariaClient.ts](file://shared/src/database/MariaClient.ts)
- [migrate.js](file://database/scripts/migrate.js)
- [DatabaseManager.ts](file://services/admin/src/lib/database/DatabaseManager.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Project Structure](#project-structure)
3. [Core Components](#core-components)
4. [Architecture Overview](#architecture-overview)
5. [Detailed Component Analysis](#detailed-component-analysis)
6. [Dependency Analysis](#dependency-analysis)
7. [Performance Considerations](#performance-considerations)
8. [Troubleshooting Guide](#troubleshooting-guide)
9. [Conclusion](#conclusion)
10. [Appendices](#appendices)

## Introduction
This document provides comprehensive documentation for the MariaDB database schema used in the domainr project. The schema is designed to store and manage domain-related data including categories, backlinks, WHOIS information, and crawl jobs. The database supports complex queries for domain analysis, ranking, and monitoring. The schema design emphasizes data integrity, query performance, and scalability. This documentation covers all tables, their relationships, indexing strategies, and the application's interaction with the database through the MariaClient.

## Project Structure
The MariaDB schema is located in the `database/mariadb` directory of the project. The schema initialization is handled by the `init.sql` file, which creates all necessary tables and indexes. Database migration scripts are managed through the `migrate.js` script in the `database/scripts` directory. The application interacts with the database through the MariaClient class located in `shared/src/database/MariaClient.ts`. Connection pooling and database management are handled by the DatabaseManager in `services/admin/src/lib/database/DatabaseManager.ts`.

```mermaid
graph TB
subgraph "Database Schema"
A[init.sql] --> B[domain_categories]
A --> C[domain_category_mapping]
A --> D[backlinks]
A --> E[domain_whois]
A --> F[domain_reviews]
A --> G[system_config]
end
subgraph "Application Layer"
H[MariaClient.ts] --> A
I[migrate.js] --> A
J[DatabaseManager.ts] --> H
end
K[Application Services] --> J
```

**Diagram sources**
- [init.sql](file://database/mariadb/init.sql)
- [MariaClient.ts](file://shared/src/database/MariaClient.ts)
- [migrate.js](file://database/scripts/migrate.js)
- [DatabaseManager.ts](file://services/admin/src/lib/database/DatabaseManager.ts)

**Section sources**
- [init.sql](file://database/mariadb/init.sql)
- [MariaClient.ts](file://shared/src/database/MariaClient.ts)
- [migrate.js](file://database/scripts/migrate.js)
- [DatabaseManager.ts](file://services/admin/src/lib/database/DatabaseManager.ts)

## Core Components
The core components of the MariaDB schema include domain categorization, backlink analysis, WHOIS data storage, and system configuration management. The schema uses InnoDB storage engine with utf8mb4 character set for full Unicode support. All tables are designed with appropriate indexes to optimize query performance. The domain categorization system supports hierarchical categories with parent-child relationships. Backlink data includes quality scoring and verification tracking. WHOIS information is stored with comprehensive domain registration details.

**Section sources**
- [init.sql](file://database/mariadb/init.sql)
- [MariaClient.ts](file://shared/src/database/MariaClient.ts)

## Architecture Overview
The MariaDB schema is part of a multi-database architecture that includes ScyllaDB for time-series data and Manticore for full-text search. MariaDB serves as the primary relational database for structured domain metadata. The architecture follows a microservices pattern with dedicated services for domain crawling, analysis, and administration. The MariaClient provides a unified interface for database operations with built-in connection pooling and retry logic. The DatabaseManager class manages connections to all database systems and provides health monitoring capabilities.

```mermaid
graph TD
subgraph "Application Services"
A[Domain Crawler] --> B[MariaClient]
C[Domain Analyzer] --> B
D[Admin Interface] --> B
end
subgraph "Database Layer"
B --> E[MariaDB]
F[ScyllaDB] --> G[Time-series Data]
H[Manticore] --> I[Full-text Search]
end
subgraph "Infrastructure"
J[Redis] --> K[Cache]
L[Monitoring] --> M[Prometheus]
end
E --> N[Relational Data]
B --> O[Connection Pooling]
P[DatabaseManager] --> Q[Multi-database Orchestration]
```

**Diagram sources**
- [MariaClient.ts](file://shared/src/database/MariaClient.ts)
- [DatabaseManager.ts](file://services/admin/src/lib/database/DatabaseManager.ts)

## Detailed Component Analysis

### Domain Categories Analysis
The domain categorization system consists of two related tables: `domain_categories` and `domain_category_mapping`. The `domain_categories` table stores the category hierarchy with support for parent-child relationships. Each category has a unique name, description, and creation timestamp. The `domain_category_mapping` table establishes the many-to-many relationship between domains and categories, including a confidence score for the categorization.

```mermaid
classDiagram
class domain_categories {
+id : INT
+name : VARCHAR(100)
+description : TEXT
+parent_category_id : INT
+created_at : TIMESTAMP
+PRIMARY KEY(id)
+UNIQUE(name)
+FOREIGN KEY(parent_category_id) REFERENCES domain_categories(id)
}
class domain_category_mapping {
+domain : VARCHAR(255)
+category_id : INT
+confidence_score : DECIMAL(3,2)
+assigned_at : TIMESTAMP
+PRIMARY KEY(domain, category_id)
+FOREIGN KEY(category_id) REFERENCES domain_categories(id)
}
domain_categories "1" --> "0..*" domain_category_mapping : contains
domain_categories "1" --> "0..*" domain_categories : parent-child
```

**Diagram sources**
- [init.sql](file://database/mariadb/init.sql)

**Section sources**
- [init.sql](file://database/mariadb/init.sql)
- [MariaClient.ts](file://shared/src/database/MariaClient.ts)

### Backlinks Analysis
The backlinks table stores comprehensive information about inbound links to domains. Each backlink record includes source and target domains, link quality score, anchor text, link type (follow, nofollow, sponsored, ugc), discovery and verification timestamps, and active status. The table is optimized for queries that analyze backlink profiles by target domain, source domain, or quality score.

```mermaid
classDiagram
class backlinks {
+id : CHAR(36)
+source_domain : VARCHAR(255)
+target_domain : VARCHAR(255)
+link_quality_score : DECIMAL(3,2)
+anchor_text : VARCHAR(500)
+link_type : ENUM
+discovered_at : TIMESTAMP
+last_verified : TIMESTAMP
+is_active : BOOLEAN
+PRIMARY KEY(id)
+INDEX idx_target_domain(target_domain)
+INDEX idx_source_domain(source_domain)
+INDEX idx_quality_score(link_quality_score)
+INDEX idx_discovered(discovered_at)
+INDEX idx_active(is_active)
}
```

**Diagram sources**
- [init.sql](file://database/mariadb/init.sql)

**Section sources**
- [init.sql](file://database/mariadb/init.sql)
- [MariaClient.ts](file://shared/src/database/MariaClient.ts)

### WHOIS Data Analysis
The domain_whois table stores WHOIS registration information for domains. The schema includes registrar details, registration and expiration dates, name servers, registrant country and organization, privacy protection status, and last update timestamp. Indexes are created on key search fields including registrar, registration date, country, and expiration date to support various query patterns.

```mermaid
classDiagram
class domain_whois {
+domain : VARCHAR(255)
+registrar : VARCHAR(255)
+registration_date : DATE
+expiration_date : DATE
+name_servers : TEXT
+registrant_country : VARCHAR(2)
+registrant_organization : VARCHAR(255)
+privacy_protected : BOOLEAN
+last_updated : TIMESTAMP
+PRIMARY KEY(domain)
+INDEX idx_registrar(registrar)
+INDEX idx_registration_date(registration_date)
+INDEX idx_country(registrant_country)
+INDEX idx_expiration(expiration_date)
}
```

**Diagram sources**
- [init.sql](file://database/mariadb/init.sql)

**Section sources**
- [init.sql](file://database/mariadb/init.sql)
- [MariaClient.ts](file://shared/src/database/MariaClient.ts)

### MariaClient Analysis
The MariaClient class provides a robust interface for interacting with the MariaDB database. It implements connection pooling, automatic reconnection, query retry logic, and transaction support. The client uses the mysql2/promise library for efficient database operations. Connection parameters are configured through environment variables, allowing for flexible deployment across different environments.

```mermaid
sequenceDiagram
participant Application
participant MariaClient
participant ConnectionPool
participant MariaDB
Application->>MariaClient : execute(query, params)
MariaClient->>ConnectionPool : getConnection()
ConnectionPool-->>MariaClient : Connection
MariaClient->>MariaDB : Execute query
alt Success
MariaDB-->>MariaClient : Results
MariaClient-->>Application : {rows, fields}
else Failure
MariaClient->>MariaClient : Retry logic
MariaClient->>ConnectionPool : Release connection
MariaClient->>MariaClient : delay()
MariaClient->>ConnectionPool : getConnection()
MariaClient->>MariaDB : Execute query
end
```

**Diagram sources**
- [MariaClient.ts](file://shared/src/database/MariaClient.ts)

**Section sources**
- [MariaClient.ts](file://shared/src/database/MariaClient.ts)

## Dependency Analysis
The MariaDB schema has dependencies on several external components and services. The database initialization depends on the Docker environment configuration, with connection parameters provided through environment variables. The MariaClient depends on the mysql2/promise library for database connectivity. The migration script depends on the cassandra-driver for ScyllaDB operations and dotenv for environment variable loading. The DatabaseManager has dependencies on redis and cassandra-driver for multi-database connectivity.

```mermaid
graph TD
A[MariaDB Schema] --> B[Docker Environment]
A --> C[Environment Variables]
D[MariaClient] --> E[mysql2/promise]
F[migrate.js] --> G[cassandra-driver]
F --> H[dotenv]
I[DatabaseManager] --> J[redis]
I --> K[cassandra-driver]
I --> L[mysql2/promise]
M[Application] --> D
M --> I
```

**Diagram sources**
- [init.sql](file://database/mariadb/init.sql)
- [MariaClient.ts](file://shared/src/database/MariaClient.ts)
- [migrate.js](file://database/scripts/migrate.js)
- [DatabaseManager.ts](file://services/admin/src/lib/database/DatabaseManager.ts)

## Performance Considerations
The MariaDB schema is optimized for performance through careful indexing, appropriate data types, and query optimization. All tables use the InnoDB storage engine which provides row-level locking and foreign key constraints. The utf8mb4 character set with utf8mb4_unicode_ci collation ensures proper Unicode support and consistent string comparison. Indexes are created on all frequently queried columns, including foreign keys and search fields. The connection pool in MariaClient is configured with a connection limit of 10 to balance resource usage and performance. Query retry logic with exponential backoff helps maintain reliability under transient load conditions.

**Section sources**
- [init.sql](file://database/mariadb/init.sql)
- [MariaClient.ts](file://shared/src/database/MariaClient.ts)

## Troubleshooting Guide
Common issues with the MariaDB schema and their solutions include connection failures, slow queries, and migration problems. Connection issues are typically caused by incorrect environment variables or network connectivity problems. The MariaClient includes comprehensive error logging and retry logic to handle transient connection issues. Slow queries can be diagnosed using the indexes defined in the schema and the query execution plans. Migration issues can be resolved by checking the schema_migrations table to identify which migrations have been applied. The healthCheck method in MariaClient can be used to verify database connectivity.

**Section sources**
- [MariaClient.ts](file://shared/src/database/MariaClient.ts)
- [migrate.js](file://database/scripts/migrate.js)
- [DatabaseManager.ts](file://services/admin/src/lib/database/DatabaseManager.ts)

## Conclusion
The MariaDB schema provides a robust foundation for storing and managing domain-related data in the domainr application. The schema design follows best practices for relational database design with proper normalization, referential integrity, and indexing. The integration with the application through the MariaClient provides a reliable and efficient interface with connection pooling and error handling. The migration system ensures consistent schema evolution across environments. The multi-database architecture leverages MariaDB for structured relational data while integrating with ScyllaDB and Manticore for specialized data storage needs.

## Appendices

### Table Schema Details
The following tables are defined in the MariaDB schema:

**domain_categories**
- id: INT, AUTO_INCREMENT, PRIMARY KEY
- name: VARCHAR(100), NOT NULL, UNIQUE
- description: TEXT
- parent_category_id: INT, FOREIGN KEY to domain_categories(id)
- created_at: TIMESTAMP, DEFAULT CURRENT_TIMESTAMP
- Indexes: idx_parent(parent_category_id), idx_name(name)

**domain_category_mapping**
- domain: VARCHAR(255)
- category_id: INT, FOREIGN KEY to domain_categories(id)
- confidence_score: DECIMAL(3,2)
- assigned_at: TIMESTAMP, DEFAULT CURRENT_TIMESTAMP
- PRIMARY KEY: (domain, category_id)
- Indexes: idx_domain(domain), idx_category(category_id), idx_confidence(confidence_score)

**backlinks**
- id: CHAR(36), PRIMARY KEY
- source_domain: VARCHAR(255)
- target_domain: VARCHAR(255)
- link_quality_score: DECIMAL(3,2)
- anchor_text: VARCHAR(500)
- link_type: ENUM('follow', 'nofollow', 'sponsored', 'ugc')
- discovered_at: TIMESTAMP, DEFAULT CURRENT_TIMESTAMP
- last_verified: TIMESTAMP
- is_active: BOOLEAN, DEFAULT TRUE
- Indexes: idx_target_domain(target_domain), idx_source_domain(source_domain), idx_quality_score(link_quality_score), idx_discovered(discovered_at), idx_active(is_active)

**domain_whois**
- domain: VARCHAR(255), PRIMARY KEY
- registrar: VARCHAR(255)
- registration_date: DATE
- expiration_date: DATE
- name_servers: TEXT
- registrant_country: VARCHAR(2)
- registrant_organization: VARCHAR(255)
- privacy_protected: BOOLEAN, DEFAULT FALSE
- last_updated: TIMESTAMP, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
- Indexes: idx_registrar(registrar), idx_registration_date(registration_date), idx_country(registrant_country), idx_expiration(expiration_date)

**domain_reviews**
- id: CHAR(36), PRIMARY KEY
- domain: VARCHAR(255)
- source: VARCHAR(100) -- 'trustpilot', 'sitejabber', 'google', 'manual'
- rating: DECIMAL(2,1)
- review_text: TEXT
- review_date: DATE
- sentiment_score: DECIMAL(3,2)
- verified: BOOLEAN, DEFAULT FALSE
- created_at: TIMESTAMP, DEFAULT CURRENT_TIMESTAMP
- Indexes: idx_domain(domain), idx_source(source), idx_rating(rating), idx_review_date(review_date), idx_verified(verified)

**system_config**
- config_key: VARCHAR(100), PRIMARY KEY
- config_value: TEXT
- description: TEXT
- updated_at: TIMESTAMP, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP

**Section sources**
- [init.sql](file://database/mariadb/init.sql)