# Data Models & Database Schema

<cite>
**Referenced Files in This Document**   
- [DomainAnalysis.ts](file://shared/src/models/DomainAnalysis.ts)
- [DomainCrawlJob.ts](file://shared/src/models/DomainCrawlJob.ts)
- [DomainDescription.ts](file://shared/src/models/DomainDescription.ts)
- [DomainRanking.ts](file://shared/src/models/DomainRanking.ts)
- [SearchResult.ts](file://shared/src/models/SearchResult.ts)
- [init.sql](file://database/mariadb/init.sql)
- [manticore/init.sql](file://database/manticore/init.sql)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Project Structure](#project-structure)
3. [Core Components](#core-components)
4. [Architecture Overview](#architecture-overview)
5. [Detailed Component Analysis](#detailed-component-analysis)
6. [Dependency Analysis](#dependency-analysis)
7. [Performance Considerations](#performance-considerations)
8. [Troubleshooting Guide](#troubleshooting-guide)
9. [Conclusion](#conclusion)

## Introduction
This document provides comprehensive data model documentation for the domainr system, detailing the entity relationships, field definitions, and data types for key models: DomainAnalysis, DomainCrawlJob, DomainDescription, DomainRanking, and SearchResult. It covers database schema definitions in both MariaDB and Manticore Search, including primary/foreign keys, indexes, constraints, data validation rules, business logic, access patterns, caching strategies, performance considerations, data lifecycle policies, migration paths, and security requirements.

## Project Structure

```mermaid
graph TD
Database[Database] --> MariaDB[MariaDB Schema]
Database --> Manticore[Manticore Search Schema]
Models[Data Models] --> DomainAnalysis[DomainAnalysis]
Models --> DomainCrawlJob[DomainCrawlJob]
Models --> DomainDescription[DomainDescription]
Models --> DomainRanking[DomainRanking]
Models --> SearchResult[SearchResult]
MariaDB --> |Schema Definition| mariadb_init[init.sql]
Manticore --> |Schema Definition| manticore_init[init.sql]
Models --> |Implementation| shared_models[shared/src/models/]
```

**Diagram sources**
- [mariadb/init.sql](file://database/mariadb/init.sql)
- [manticore/init.sql](file://database/manticore/init.sql)
- [shared/src/models/](file://shared/src/models/)

**Section sources**
- [database/mariadb/init.sql](file://database/mariadb/init.sql)
- [database/manticore/init.sql](file://database/manticore/init.sql)
- [shared/src/models/](file://shared/src/models/)

## Core Components

The domainr system utilizes a multi-database architecture with MariaDB for relational data storage, Manticore Search for full-text search capabilities, and ScyllaDB (inferred from code) for high-performance analytics. The core data models are defined in TypeScript interfaces and classes within the shared module, ensuring consistency across services.

**Section sources**
- [shared/src/models/DomainAnalysis.ts](file://shared/src/models/DomainAnalysis.ts)
- [shared/src/models/DomainCrawlJob.ts](file://shared/src/models/DomainCrawlJob.ts)
- [shared/src/models/DomainDescription.ts](file://shared/src/models/DomainDescription.ts)
- [shared/src/models/DomainRanking.ts](file://shared/src/models/DomainRanking.ts)
- [shared/src/models/SearchResult.ts](file://shared/src/models/SearchResult.ts)

## Architecture Overview

```mermaid
graph LR
subgraph "Search & Analytics Layer"
Manticore[Manticore Search]
Manticore --> |Full-Text Search| domains_index[domains_index]
Manticore --> |Content Search| domain_content_index[domain_content_index]
Manticore --> |Review Search| domain_reviews_index[domain_reviews_index]
end
subgraph "Relational Storage Layer"
MariaDB[MariaDB]
MariaDB --> |Categories| domain_categories[domain_categories]
MariaDB --> |Whois Data| domain_whois[domain_whois]
MariaDB --> |Backlinks| backlinks[backlinks]
MariaDB --> |Reviews| domain_reviews[domain_reviews]
end
subgraph "Application Layer"
WebApp[Web App]
Worker[Worker Service]
Seeder[Domain Seeder]
end
WebApp --> Manticore
WebApp --> MariaDB
Worker --> Manticore
Worker --> MariaDB
Seeder --> Manticore
Seeder --> MariaDB
style Manticore fill:#f9f,stroke:#333
style MariaDB fill:#bbf,stroke:#333
```

**Diagram sources**
- [database/mariadb/init.sql](file://database/mariadb/init.sql)
- [database/manticore/init.sql](file://database/manticore/init.sql)

## Detailed Component Analysis

### Domain Analysis Model

The DomainAnalysis model captures comprehensive technical and qualitative metrics about domains, serving as the foundation for ranking calculations.

```mermaid
classDiagram
class DomainAnalysis {
+string domain
+number globalRank
+number categoryRank
+string category
+number overallScore
+PerformanceMetricsType performance
+SecurityMetricsType security
+SEOMetricsType seo
+TechnicalMetricsType technical
+DomainInfoType domainInfo
+ContentQualityMetricsType contentQuality
+LanguageDetectionType languageDetection
+ContentMetricsType contentMetrics
+TechnicalSEOType technicalSEO
+string[] screenshots
+string[] subdomains
+string lastCrawled
+string crawlStatus
+string lastUpdated
+calculateOverallScore(weights) number
+toScyllaFormat() Record~string, unknown~
}
class PerformanceMetricsType {
+number loadTime
+number firstContentfulPaint
+number largestContentfulPaint
+number cumulativeLayoutShift
+number firstInputDelay
+number speedIndex
+number score
}
class SecurityMetricsType {
+string sslGrade
+Record~string, unknown~ securityHeaders
+unknown[] vulnerabilities
+Record~string, unknown~ certificateInfo
+number score
}
class SEOMetricsType {
+Record~string, unknown~ metaTags
+unknown[] structuredData
+Record~string, unknown~ sitemap
+Record~string, unknown~ robotsTxt
+number score
}
class TechnicalMetricsType {
+string[] technologies
+Record~string, unknown~ serverInfo
+Record~string, unknown~ httpHeaders
+number pageSize
+Record~string, unknown~ resourceCount
+number score
}
DomainAnalysis --> PerformanceMetricsType : "contains"
DomainAnalysis --> SecurityMetricsType : "contains"
DomainAnalysis --> SEOMetricsType : "contains"
DomainAnalysis --> TechnicalMetricsType : "contains"
```

**Diagram sources**
- [shared/src/models/DomainAnalysis.ts](file://shared/src/models/DomainAnalysis.ts)

**Section sources**
- [shared/src/models/DomainAnalysis.ts](file://shared/src/models/DomainAnalysis.ts)

### Domain Crawl Job Model

The DomainCrawlJob model represents scheduled or ad-hoc crawling operations with configurable types, priorities, and retry mechanisms.

```mermaid
classDiagram
class DomainCrawlJob {
+string jobId
+string domain
+'full'|'quick'|'security'|'performance' crawlType
+'high'|'medium'|'low' priority
+'pending'|'processing'|'completed'|'failed' status
+number retryCount
+number maxRetries
+string scheduledAt
+string startedAt
+string completedAt
+string errorMessage
+string userAgent
+string[] pagesToCrawl
+generateJobId() string
+markStarted() void
+markCompleted() void
+markFailed(errorMessage) void
+canRetry() boolean
+incrementRetry() void
}
```

**Diagram sources**
- [shared/src/models/DomainCrawlJob.ts](file://shared/src/models/DomainCrawlJob.ts)

**Section sources**
- [shared/src/models/DomainCrawlJob.ts](file://shared/src/models/DomainCrawlJob.ts)

### Domain Description Model

The DomainDescription interface provides a comprehensive profile of a domain, combining technical data, content analysis, reputation metrics, and ranking information.

```mermaid
classDiagram
class DomainDescriptionInterface {
+metadata : DomainMetadata
+overview : DomainOverview
+technical : TechnicalMetrics
+seo : SEOMetrics
+contentProfile : ContentProfile
+reputation : ReputationMetrics
+ranking : RankingMetrics
+compliance : ComplianceInfo
+crawl : CrawlInfo
}
class DomainMetadata {
+string domain
+string tld
+'active'|'parked'|'redirect'|'inactive' status
+string country
+string language
+RegistrationInfo registration
+IDNInfo idn
+OwnerInfo owner
+CategoryInfo category
+string[] tags
+boolean preGenerated
}
class RankingMetrics {
+number globalRank
+number categoryRank
+ScoreObject scores
+'improving'|'stable'|'declining' trend
+number trafficEstimateMonthly
}
DomainDescriptionInterface --> DomainMetadata : "contains"
DomainDescriptionInterface --> RankingMetrics : "contains"
```

**Diagram sources**
- [shared/src/models/DomainDescription.ts](file://shared/src/models/DomainDescription.ts)

**Section sources**
- [shared/src/models/DomainDescription.ts](file://shared/src/models/DomainDescription.ts)

### Domain Ranking Model

The DomainRanking model stores calculated ranking positions and scores for domains across different ranking types (global, category-specific, etc.).

```mermaid
classDiagram
class DomainRanking {
+string domain
+string rankingType
+number rank
+number overallScore
+number performanceScore
+number securityScore
+number seoScore
+number technicalScore
+number backlinkScore
+number trafficEstimate
+string lastUpdated
+toScyllaFormat() Record~string, unknown~
}
```

**Diagram sources**
- [shared/src/models/DomainRanking.ts](file://shared/src/models/DomainRanking.ts)

**Section sources**
- [shared/src/models/DomainRanking.ts](file://shared/src/models/DomainRanking.ts)

### Search Result Model

The SearchResult class encapsulates paginated search results with filtering, faceting, and performance metrics.

```mermaid
classDiagram
class SearchResult {
+unknown[] domains
+number totalResults
+number currentPage
+number totalPages
+Record~string, unknown~ filters
+Record~string, unknown~ facets
+number searchTime
}
```

**Diagram sources**
- [shared/src/models/SearchResult.ts](file://shared/src/models/SearchResult.ts)

**Section sources**
- [shared/src/models/SearchResult.ts](file://shared/src/models/SearchResult.ts)

## Dependency Analysis

```mermaid
graph TD
DomainAnalysis --> DomainCrawlJob : "triggered by"
DomainCrawlJob --> DomainDescription : "produces"
DomainDescription --> DomainRanking : "informs"
DomainRanking --> SearchResult : "included in"
DomainAnalysis --> DomainRanking : "contributes to"
DomainDescription --> SearchResult : "included in"
MariaDB --> DomainAnalysis : "stores domain_whois"
MariaDB --> DomainCrawlJob : "stores backlinks"
Manticore --> SearchResult : "provides search results"
ScyllaDB[(ScyllaDB)] --> DomainAnalysis : "stores analysis data"
ScyllaDB --> DomainRanking : "stores ranking data"
style MariaDB fill:#bbf,stroke:#333
style Manticore fill:#f9f,stroke:#333
style ScyllaDB fill:#bfb,stroke:#333
```

**Diagram sources**
- [database/mariadb/init.sql](file://database/mariadb/init.sql)
- [database/manticore/init.sql](file://database/manticore/init.sql)
- [shared/src/models/](file://shared/src/models/)

**Section sources**
- [shared/src/models/DomainAnalysis.ts](file://shared/src/models/DomainAnalysis.ts)
- [shared/src/models/DomainCrawlJob.ts](file://shared/src/models/DomainCrawlJob.ts)
- [shared/src/models/DomainDescription.ts](file://shared/src/models/DomainDescription.ts)
- [shared/src/models/DomainRanking.ts](file://shared/src/models/DomainRanking.ts)
- [shared/src/models/SearchResult.ts](file://shared/src/models/SearchResult.ts)

## Performance Considerations

The system employs a multi-layered data access strategy to optimize performance:

1. **Caching Strategy**: Data is cached in Redis with configurable TTLs (default 1800 seconds as defined in system_config)
2. **Search Optimization**: Manticore Search provides fast full-text search capabilities with columnar storage
3. **Database Indexing**: MariaDB tables include strategic indexes on frequently queried fields
4. **Data Partitioning**: ScyllaDB handles high-volume analytics data with distributed architecture
5. **Asynchronous Processing**: Crawl jobs are processed asynchronously via worker queues

The DomainAnalysis model's `toScyllaFormat()` method optimizes data for NoSQL storage, while the ranking system uses weighted calculations to balance different quality metrics.

**Section sources**
- [database/mariadb/init.sql](file://database/mariadb/init.sql)
- [shared/src/models/DomainAnalysis.ts](file://shared/src/models/DomainAnalysis.ts)
- [shared/src/models/DomainRanking.ts](file://shared/src/models/DomainRanking.ts)

## Troubleshooting Guide

Common issues and their resolutions:

1. **Crawl Job Failures**: Check DomainCrawlJob status and errorMessage fields; verify retryCount against maxRetries
2. **Missing Rankings**: Ensure DomainAnalysis data is complete and calculateOverallScore() has been called
3. **Search Performance Issues**: Verify Manticore indexes are properly configured and data is indexed
4. **Data Inconsistencies**: Check system_config values for crawl_rate_limit and ranking_update_interval
5. **Cache Invalidation**: Use CacheInvalidationService when data updates require immediate visibility

The system includes comprehensive error handling with retry mechanisms and detailed status tracking.

**Section sources**
- [shared/src/models/DomainCrawlJob.ts](file://shared/src/models/DomainCrawlJob.ts)
- [database/mariadb/init.sql](file://database/mariadb/init.sql)
- [shared/src/models/DomainAnalysis.ts](file://shared/src/models/DomainAnalysis.ts)

## Conclusion

The domainr data models provide a comprehensive framework for domain analysis, ranking, and search. The architecture leverages multiple database technologies to optimize for different access patterns: MariaDB for relational data integrity, Manticore Search for fast text search, and ScyllaDB for high-performance analytics. The TypeScript models ensure type safety and consistency across services, while the defined relationships and business rules maintain data quality and enable sophisticated ranking algorithms. The system supports robust data lifecycle management, caching strategies, and error handling to ensure reliability and performance at scale.