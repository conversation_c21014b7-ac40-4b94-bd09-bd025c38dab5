# Technology Stack

<cite>
**Referenced Files in This Document**   
- [package.json](file://package.json)
- [services/admin/package.json](file://services/admin/package.json)
- [services/domain-seeder/package.json](file://services/domain-seeder/package.json)
- [services/web-app/package.json](file://services/web-app/package.json)
- [services/worker/package.json](file://services/worker/package.json)
- [shared/package.json](file://shared/package.json)
- [docker-compose.yml](file://docker-compose.yml)
- [services/admin/next.config.js](file://services/admin/next.config.js)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Programming Languages](#programming-languages)
3. [Frontend Frameworks](#frontend-frameworks)
4. [Backend Frameworks](#backend-frameworks)
5. [UI Framework](#ui-framework)
6. [Testing Tools](#testing-tools)
7. [Message Queue System](#message-queue-system)
8. [Database Technologies](#database-technologies)
9. [Infrastructure Components](#infrastructure-components)
10. [Integration Patterns](#integration-patterns)
11. [Version Compatibility](#version-compatibility)
12. [Conclusion](#conclusion)

## Introduction
The domainr technology stack is a comprehensive, modern ecosystem designed for high-performance domain discovery, analysis, and ranking. Built primarily with TypeScript and JavaScript, the system leverages a microservices architecture with specialized components for different functional domains. This document provides a detailed overview of the technology stack, covering programming languages, frameworks, databases, infrastructure, and integration patterns. The stack is optimized for scalability, reliability, and maintainability, supporting complex operations including domain crawling, AI-powered content analysis, real-time ranking calculations, and comprehensive monitoring.

**Section sources**
- [package.json](file://package.json)
- [docker-compose.yml](file://docker-compose.yml)

## Programming Languages
The domainr system is built primarily on TypeScript (v5.x) and JavaScript, providing strong typing and modern language features across the entire stack. TypeScript is used consistently across all services, enabling better code quality, maintainability, and developer productivity. The system requires Node.js v22.0.0 or higher, ensuring access to the latest JavaScript features and performance improvements. The use of TypeScript across both frontend and backend components creates a unified development experience and enables code sharing through the shared workspace package. This consistent language choice reduces context switching for developers and ensures type safety across service boundaries.

```mermaid
flowchart TD
A["TypeScript v5.x"] --> B["Node.js v22+"]
A --> C["React 18"]
A --> D["Next.js 15"]
B --> E["Express.js via ultimate-express"]
C --> F["Mantine UI v7"]
D --> G["Server-Side Rendering"]
E --> H["Microservices Architecture"]
F --> I["Admin Dashboard"]
G --> J["Web Application"]
H --> K["Service Communication"]
```

**Diagram sources**
- [package.json](file://package.json#L10-L12)
- [services/admin/package.json](file://services/admin/package.json#L10-L12)

## Frontend Frameworks
The frontend architecture is built on React 18 and Next.js 15, providing a powerful foundation for server-side rendering, static site generation, and client-side interactivity. Next.js serves as the primary framework for both the admin interface and web application, enabling SEO-friendly pages, automatic code splitting, and optimized performance. The combination of React 18's concurrent features and Next.js 15's enhanced server components allows for highly responsive user interfaces with minimal loading times. The system uses React Server Components selectively to reduce bundle size and improve Time to Interactive (TTI) metrics.

**Section sources**
- [services/admin/package.json](file://services/admin/package.json#L20-L21)
- [services/admin/next.config.js](file://services/admin/next.config.js)

## Backend Frameworks
The backend services utilize Express.js through the ultimate-express wrapper, providing a robust foundation for API development and service communication. ultimate-express enhances the standard Express.js functionality with additional middleware, error handling, and configuration options tailored to the domainr ecosystem. This framework choice enables rapid development of RESTful APIs while maintaining flexibility for custom routing and middleware integration. The worker, web-app, and domain-seeder services all leverage this framework, ensuring consistency in API patterns and error handling across the system.

```mermaid
flowchart TD
A["ultimate-express"] --> B["Express.js"]
B --> C["Middleware Pipeline"]
C --> D["Request Validation"]
C --> E["Error Handling"]
C --> F["Rate Limiting"]
C --> G["Security Headers"]
D --> H["Joi/AJV Validation"]
E --> I["Centralized Error Logging"]
F --> J["Token Bucket Algorithm"]
G --> K["Helmet Integration"]
A --> L["Service Endpoints"]
L --> M["/api/health"]
L --> N["/api/domains"]
L --> O["/api/analytics"]
```

**Diagram sources**
- [services/web-app/package.json](file://services/web-app/package.json#L20-L21)
- [services/domain-seeder/package.json](file://services/domain-seeder/package.json#L20-L21)

## UI Framework
The admin interface is built using Mantine UI v7, a modern React component library that provides a comprehensive set of accessible, customizable components. Mantine UI is used extensively in the admin service, offering pre-built components for forms, modals, notifications, and data visualization. The framework's integration with emotion and CSS variables enables dynamic theme switching and consistent styling across the application. Mantine's form handling and validation utilities streamline the development of complex administrative interfaces for managing domain data, user accounts, and system configuration.

**Section sources**
- [services/admin/package.json](file://services/admin/package.json#L13-L19)

## Testing Tools
The testing ecosystem combines Vitest, Playwright, and Jest to provide comprehensive test coverage across unit, integration, end-to-end, and performance testing. Vitest serves as the primary test runner for unit and integration tests, offering fast execution and excellent TypeScript support. Playwright enables reliable end-to-end testing of both the admin interface and web application, with capabilities for cross-browser testing and visual regression detection. Jest is used for specific testing scenarios requiring its unique features, particularly in legacy test suites. The system also includes k6 for load testing and performance validation.

```mermaid
flowchart TD
A["Testing Tools"] --> B["Vitest"]
A --> C["Playwright"]
A --> D["Jest"]
A --> E["k6"]
B --> F["Unit Tests"]
B --> G["Integration Tests"]
B --> H["Mocking"]
C --> I["E2E Tests"]
C --> J["Visual Regression"]
C --> K["Accessibility Testing"]
D --> L["Snapshot Testing"]
D --> M["Legacy Test Suites"]
E --> N["Load Testing"]
E --> O["Performance Validation"]
F --> P["Service Logic"]
G --> Q["API Integration"]
I --> R["User Flows"]
J --> S["Component Appearance"]
```

**Diagram sources**
- [services/admin/package.json](file://services/admin/package.json#L50-L58)
- [services/worker/package.json](file://services/worker/package.json#L60-L62)

## Message Queue System
The system uses Redis-SMQ v7 as its primary message queue system, enabling reliable communication between microservices and asynchronous processing of domain analysis tasks. Redis-SMQ provides durable message queues with support for message acknowledgment, retries, and dead-letter queues, ensuring that critical operations like domain crawling and ranking calculations are not lost during system failures. The worker service consumes messages from Redis-SMQ queues to process domain data, while the domain-seeder service produces messages when new domains are discovered. This decoupled architecture allows for independent scaling of message producers and consumers.

**Section sources**
- [shared/package.json](file://shared/package.json#L20-L21)
- [services/worker/package.json](file://services/worker/package.json#L30-L31)

## Database Technologies
The domainr system employs a polyglot persistence strategy with multiple specialized databases: ScyllaDB for main data storage, MariaDB for relational data, Manticore Search for full-text search, and Redis for caching. This multi-database approach allows each component to use the optimal storage solution for its specific use case, maximizing performance and scalability.

### ScyllaDB for Main Data Storage
ScyllaDB 6.2 serves as the primary database for domain data storage, providing high-performance, distributed NoSQL capabilities. The system leverages ScyllaDB's Cassandra compatibility for storing domain records, crawl data, and ranking information. With its shared-nothing architecture and low-latency performance, ScyllaDB handles the high-volume write operations generated by domain discovery and crawling. The database is configured with appropriate replication and consistency levels to ensure data durability while maintaining high throughput.

### MariaDB for Relational Data
MariaDB 11.2 is used for structured relational data that requires ACID transactions and complex querying capabilities. This includes user accounts, authentication data, configuration settings, and audit logs. The relational nature of MariaDB makes it ideal for enforcing data integrity constraints and performing complex joins across related entities. The system uses mysql2 as the Node.js driver, providing efficient connection pooling and prepared statement support.

### Manticore Search for Full-Text Search
Manticore Search 6.2 provides fast, scalable full-text search capabilities for domain data. The system indexes domain names, descriptions, and metadata in Manticore, enabling rapid search operations with support for relevance ranking, filtering, and faceting. Manticore's SQL-compatible interface allows for seamless integration with existing application code, while its distributed architecture supports horizontal scaling of search workloads.

### Redis for Caching
Redis 7.2 is used for multiple purposes: caching frequently accessed data, session storage, and as a message broker. The system implements a multi-layer caching strategy with Redis, storing pre-computed rankings, domain metadata, and API response caches. Redis's in-memory performance ensures low-latency access to cached data, significantly reducing database load and improving response times for common queries.

```mermaid
erDiagram
SCYLLADB {
string domain_name PK
timestamp discovered_at
map crawl_data
map ranking_data
map metadata
}
MARIADB {
int user_id PK
string username UK
string email UK
string password_hash
timestamp created_at
timestamp updated_at
json preferences
}
MANTICORE {
int doc_id PK
string domain_name
string description
string content_snippet
float search_score
timestamp indexed_at
}
REDIS {
string cache_key PK
string cache_value
int ttl
string cache_type
}
SCYLLADB ||--o{ REDIS : "cached_as"
MARIADB ||--o{ REDIS : "session_store"
MANTICORE ||--o{ SCYLLADB : "indexes"
REDIS ||--o{ MANTICORE : "query_cache"
```

**Diagram sources**
- [docker-compose.yml](file://docker-compose.yml#L100-L250)

## Infrastructure Components
The system is containerized using Docker and orchestrated with Kubernetes, providing a scalable, resilient deployment environment. Nginx acts as a reverse proxy and load balancer, routing traffic to appropriate services. Prometheus and Grafana provide comprehensive monitoring and observability, enabling proactive system management and performance optimization.

### Docker and Kubernetes
The entire system is containerized using Docker, with each service having its own Dockerfile and configuration. The services are orchestrated using Kubernetes (k8s), enabling automated scaling, self-healing, and rolling updates. The worker service includes Kubernetes manifests for deployment, horizontal pod autoscaling, network policies, and RBAC configuration, ensuring secure and reliable operation in production environments.

### Nginx Configuration
Nginx serves as the entry point for all external traffic, providing SSL termination, request routing, and security features. The configuration includes rate limiting, DDoS protection, and HTTP security headers to protect against common web vulnerabilities. Nginx routes requests to the appropriate backend services based on URL paths, enabling a clean separation between the admin interface, web application, and API endpoints.

### Monitoring with Prometheus and Grafana
The monitoring stack consists of Prometheus for metrics collection and storage, and Grafana for visualization and alerting. Prometheus scrapes metrics from all services, collecting data on request rates, latencies, error rates, and system resource usage. Grafana provides pre-configured dashboards for monitoring service health, database performance, and business metrics. The system includes alerting rules for critical conditions such as high error rates, latency spikes, and resource exhaustion.

```mermaid
graph TB
A["Client"] --> B["Nginx"]
B --> C["Web App"]
B --> D["Admin"]
B --> E["API Gateway"]
C --> F["Redis Cache"]
D --> G["MariaDB"]
E --> H["ScyllaDB"]
E --> I["Manticore"]
F --> J["Redis-SMQ"]
J --> K["Worker Service"]
K --> H
K --> I
L["Prometheus"] --> M["Metrics Collection"]
M --> N["Grafana"]
N --> O["Dashboards"]
N --> P["Alerts"]
L --> C
L --> D
L --> E
L --> K
H --> L
I --> L
G --> L
```

**Diagram sources**
- [docker-compose.yml](file://docker-compose.yml)
- [monitoring/prometheus.yml](file://monitoring/prometheus.yml)

## Integration Patterns
The system employs several key integration patterns to ensure reliable communication between components. Services communicate primarily through REST APIs and message queues, with Redis-SMQ facilitating asynchronous processing. The shared workspace package contains common interfaces, types, and utilities used across all services, ensuring consistency in data structures and error handling. Environment variables and configuration files manage service-specific settings, with Docker Compose orchestrating the deployment of the complete system.

**Section sources**
- [shared/package.json](file://shared/package.json)
- [docker-compose.yml](file://docker-compose.yml)

## Version Compatibility
All components in the domainr stack are carefully versioned to ensure compatibility and stability. The system requires Node.js v22.0.0 or higher, with TypeScript v5.x providing type safety across the codebase. The frontend uses React 18 with Next.js 15, while the backend services rely on Express.js via ultimate-express. Database drivers are kept up-to-date with their respective database versions, with mysql2 for MariaDB, cassandra-driver for ScyllaDB, and redis v4-5 for Redis connectivity. Testing tools are aligned across services, with Vitest v2.x, Playwright v1.47, and Jest v29 providing a consistent testing experience.

**Section sources**
- [package.json](file://package.json)
- [services/admin/package.json](file://services/admin/package.json)

## Conclusion
The domainr technology stack represents a modern, comprehensive approach to building a scalable domain discovery and analysis system. By combining TypeScript, React, Next.js, and a suite of specialized databases and infrastructure components, the system achieves high performance, reliability, and maintainability. The microservices architecture enables independent development and deployment of functional components, while the shared codebase ensures consistency across services. This technology stack is well-suited for handling the complex requirements of domain data processing, from high-volume crawling to AI-powered analysis and real-time ranking calculations.