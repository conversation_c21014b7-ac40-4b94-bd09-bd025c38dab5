# Data Processing Pipeline

<cite>
**Referenced Files in This Document**   
- [DomainProcessingPipeline.ts](file://services/worker/src/pipeline/DomainProcessingPipeline.ts)
- [PipelineCoordinator.ts](file://services/worker/src/pipeline/PipelineCoordinator.ts)
- [WorkerService.ts](file://services/worker/src/core/WorkerService.ts)
- [ValidationPipeline.ts](file://services/worker/src/validation/ValidationPipeline.ts)
- [PSLDomainNormalizer.ts](file://services/domain-seeder/src/normalization/PSLDomainNormalizer.ts)
- [DataConsistencyService.ts](file://services/domain-seeder/src/synchronization/DataConsistencyService.ts)
- [ErrorHandler.ts](file://services/domain-seeder/src/reliability/ErrorHandler.ts)
- [RetryManager.ts](file://services/worker/src/errors/RetryManager.ts)
- [WorkerJobQueue.ts](file://services/worker/src/queue/WorkerJobQueue.ts)
- [JobConsumer.ts](file://services/worker/src/queue/JobConsumer.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Project Structure](#project-structure)
3. [Core Components](#core-components)
4. [Architecture Overview](#architecture-overview)
5. [Detailed Component Analysis](#detailed-component-analysis)
6. [Dependency Analysis](#dependency-analysis)
7. [Performance Considerations](#performance-considerations)
8. [Troubleshooting Guide](#troubleshooting-guide)
9. [Conclusion](#conclusion)

## Introduction
The domainr data processing pipeline is a distributed system designed for discovering, validating, normalizing, and indexing domain data at scale. The architecture consists of specialized microservices that handle different stages of the data lifecycle, from initial discovery through final indexing. The system is built to handle high-volume domain processing with robust error handling, retry mechanisms, and data consistency guarantees. This documentation provides a comprehensive overview of the pipeline's design, component interactions, and operational characteristics.

## Project Structure
The domainr project follows a microservices architecture with distinct services handling different aspects of domain data processing. The core services include domain-seeder for discovery operations, worker for domain processing, web-app for user interface, and admin for administrative functions. Shared components provide common utilities, database access, and error handling across services. The pipeline leverages multiple databases including ScyllaDB for authoritative storage, Manticore Search for indexing, MariaDB for relational data, and Redis for caching and coordination.

```mermaid
graph TD
A[domain-seeder] --> |Discovers domains| B[worker]
B --> |Processes domains| C[Manticore Search]
B --> |Stores data| D[ScyllaDB]
B --> |Caches data| E[Redis]
F[web-app] --> |Queries| C
G[admin] --> |Manages| A
G --> |Monitors| B
H[shared] --> |Provides utilities| A
H --> |Provides utilities| B
H --> |Provides utilities| F
H --> |Provides utilities| G
```

**Diagram sources**
- [DomainProcessingPipeline.ts](file://services/worker/src/pipeline/DomainProcessingPipeline.ts)
- [WorkerService.ts](file://services/worker/src/core/WorkerService.ts)

**Section sources**
- [DomainProcessingPipeline.ts](file://services/worker/src/pipeline/DomainProcessingPipeline.ts)
- [PipelineCoordinator.ts](file://services/worker/src/pipeline/PipelineCoordinator.ts)

## Core Components
The data processing pipeline consists of several core components that work together to process domain data. The domain-seeder service discovers new domains through multiple strategies including differential analysis, zone file processing, and long-tail exploration. The worker service processes domains through crawling, ranking, and indexing phases. The shared components provide common functionality like database access, error handling, and validation across services. The system uses Redis for coordination, ScyllaDB for authoritative storage, and Manticore Search for fast querying.

**Section sources**
- [DomainProcessingPipeline.ts](file://services/worker/src/pipeline/DomainProcessingPipeline.ts)
- [PipelineCoordinator.ts](file://services/worker/src/pipeline/PipelineCoordinator.ts)
- [WorkerService.ts](file://services/worker/src/core/WorkerService.ts)

## Architecture Overview
The data processing pipeline follows a distributed microservices architecture with clear separation of concerns. The domain-seeder service acts as the entry point, discovering new domains from various sources and enqueuing them for processing. The worker service processes domains through a multi-phase pipeline including crawling, ranking, and indexing. Each worker instance runs a DomainProcessingPipeline that coordinates the processing of individual domains. The PipelineCoordinator manages multiple pipeline instances, providing load balancing and resource optimization. The system uses Redis for job queuing and coordination, ensuring exactly-once processing through distributed locking.

```mermaid
graph TD
A[Discovery Sources] --> B[domain-seeder]
B --> C[Redis Job Queue]
C --> D[Worker 1]
C --> E[Worker 2]
C --> F[Worker N]
D --> G[ScyllaDB]
D --> H[Manticore Search]
D --> I[Redis Cache]
E --> G
E --> H
E --> I
F --> G
F --> H
F --> I
G --> J[Data Consistency Service]
H --> J
J --> K[Consistency Monitoring]
L[Monitoring] --> D
L --> E
L --> F
L --> B
```

**Diagram sources**
- [DomainProcessingPipeline.ts](file://services/worker/src/pipeline/DomainProcessingPipeline.ts)
- [PipelineCoordinator.ts](file://services/worker/src/pipeline/PipelineCoordinator.ts)
- [WorkerService.ts](file://services/worker/src/core/WorkerService.ts)

## Detailed Component Analysis

### Domain Processing Pipeline Analysis
The DomainProcessingPipeline is the core component responsible for processing individual domains through the complete data pipeline. It orchestrates the crawling, ranking, and indexing phases, ensuring proper error handling and resource management throughout the process.

#### For Object-Oriented Components:
```mermaid
classDiagram
class DomainProcessingPipeline {
+string workerId
+boolean isInitialized
+Map~string, PipelineExecutionContextType~ activeExecutions
+Map~string, PipelineExecutionContextType~ executionHistory
+initialize() Promise~void~
+processDomain(domainJob) Promise~ProcessingResultType~
+createExecutionContext(domainJob) PipelineExecutionContextType
+createPhaseResult(phase) PipelinePhaseResultType
+createProcessingResult(context, crawlingResult, rankingResult, indexingResult) ProcessingResultType
+executeCrawlingPhase(context, domainJob) Promise~CrawlingResultType~
+executeRankingPhase(context, domainJob, crawlingResult) Promise~RankingResultType~
+executeIndexingPhase(context, domainJob, rankingResult) Promise~IndexingResultType~
+acquireDomainLock(context) Promise~void~
+releaseDomainLock(context) Promise~void~
+handlePipelineFailure(context, error) ProcessingResultType
+handleTaskFailure(phase, error, context) Promise~TaskRetryDecisionType~
+startResourceMonitoring(context) ResourceMonitor
+stopResourceMonitoring(monitor, context) void
+cleanupExecution(context) void
}
class PipelineCoordinator {
+boolean isInitialized
+Map~string, PipelineInstanceType~ pipelines
+processDomain(domainJob) Promise~ProcessingResultType~
+selectBestPipeline(domainJob) DomainProcessingPipeline
+addJobToQueue(job) void
+processJobQueue() void
+startHealthMonitoring() void
+startMetricsCollection() void
+startLoadBalancing() void
}
class WorkerService {
+WorkerConfigType config
+WorkerDatabaseManager databaseManager
+DomainLockManager domainLockManager
+DataCollectionOrchestrator crawlerOrchestrator
+RankingManager rankingManager
+SearchIndexingService indexingService
+JobConsumer jobConsumer
+PipelineCoordinator pipelineCoordinator
+initialize() Promise~void~
+start() Promise~void~
+stop() Promise~void~
+processJob(domainJob) Promise~ProcessingResultType~
}
DomainProcessingPipeline --> PipelineCoordinator : "registered with"
PipelineCoordinator --> WorkerService : "managed by"
WorkerService --> JobConsumer : "uses"
JobConsumer --> Redis : "stores jobs in"
```

**Diagram sources**
- [DomainProcessingPipeline.ts](file://services/worker/src/pipeline/DomainProcessingPipeline.ts)
- [PipelineCoordinator.ts](file://services/worker/src/pipeline/PipelineCoordinator.ts)
- [WorkerService.ts](file://services/worker/src/core/WorkerService.ts)

#### For API/Service Components:
```mermaid
sequenceDiagram
participant Client as "Client"
participant WorkerService as "WorkerService"
participant PipelineCoordinator as "PipelineCoordinator"
participant DomainProcessingPipeline as "DomainProcessingPipeline"
participant Redis as "Redis"
Client->>WorkerService : processJob(domainJob)
WorkerService->>PipelineCoordinator : processDomain(domainJob)
PipelineCoordinator->>PipelineCoordinator : addJobToQueue()
PipelineCoordinator->>PipelineCoordinator : processJobQueue()
PipelineCoordinator->>DomainProcessingPipeline : processDomain(domainJob)
DomainProcessingPipeline->>DomainProcessingPipeline : createExecutionContext()
DomainProcessingPipeline->>DomainProcessingPipeline : acquireDomainLock()
DomainProcessingPipeline->>DomainProcessingPipeline : executeCrawlingPhase()
DomainProcessingPipeline->>DomainProcessingPipeline : executeRankingPhase()
DomainProcessingPipeline->>DomainProcessingPipeline : executeIndexingPhase()
DomainProcessingPipeline->>DomainProcessingPipeline : createProcessingResult()
DomainProcessingPipeline->>DomainProcessingPipeline : releaseDomainLock()
DomainProcessingPipeline-->>PipelineCoordinator : return result
PipelineCoordinator-->>WorkerService : return result
WorkerService-->>Client : return result
```

**Diagram sources**
- [DomainProcessingPipeline.ts](file://services/worker/src/pipeline/DomainProcessingPipeline.ts)
- [PipelineCoordinator.ts](file://services/worker/src/pipeline/PipelineCoordinator.ts)
- [WorkerService.ts](file://services/worker/src/core/WorkerService.ts)

### Data Validation and Normalization Analysis
The data validation and normalization components ensure data quality and consistency throughout the pipeline. The ValidationPipeline validates domain data at various stages, while the PSLDomainNormalizer handles domain normalization using the Public Suffix List.

#### For Object-Oriented Components:
```mermaid
classDiagram
class ValidationPipeline {
+ValidationConfig config
+validate(data, stage) Promise~ValidationResult~
+validateSourceFetch(data) ValidationResult
+validateNormalization(data) ValidationResult
+validateExistenceCheck(data) ValidationResult
+validateEnqueue(data) ValidationResult
+isValidDomainFormat(domain) boolean
+sanitizeCandidate(candidate) SanitizedCandidate
+validateNormalizedDomain(domain, index) ValidationResult
}
class PSLDomainNormalizer {
+PSLManager pslManager
+NormalizationMetricsType metrics
+normalize(domain) NormalizedDomainType
+batchNormalize(domains) Map~string, NormalizedDomainType~
+isValidDomain(domain) boolean
+getETLD1(domain) string
+cleanDomain(domain) string
+toPunycode(domain) string
+isIPAddress(domain) boolean
+isValidLabel(label) boolean
+createInvalidResult(original, errors) NormalizedDomainType
+getMetrics() NormalizationMetricsType
+resetMetrics() void
+healthCheck() Promise~boolean~
+destroy() void
}
class PSLManager {
+string cacheDir
+Map~string, string~ publicSuffixes
+Map~string, string~ domains
+fetchPSL() Promise~void~
+updatePSL() Promise~void~
+getPublicSuffix(domain) string
+getDomain(domain) string
+healthCheck() Promise~boolean~
+destroy() void
}
ValidationPipeline --> PSLDomainNormalizer : "uses"
PSLDomainNormalizer --> PSLManager : "uses"
```

**Diagram sources**
- [ValidationPipeline.ts](file://services/worker/src/validation/ValidationPipeline.ts)
- [PSLDomainNormalizer.ts](file://services/domain-seeder/src/normalization/PSLDomainNormalizer.ts)

#### For Complex Logic Components:
```mermaid
flowchart TD
Start([Start]) --> ValidateInput["Validate Input Parameters"]
ValidateInput --> InputValid{"Input Valid?"}
InputValid --> |No| ReturnError["Return Validation Errors"]
InputValid --> |Yes| CleanDomain["Clean Domain (trim, lowercase)"]
CleanDomain --> IsIP{"Is IP Address?"}
IsIP --> |Yes| ReturnNull["Return null (filter out)"]
IsIP --> |No| ToPunycode["Convert to Punycode if needed"]
ToPunycode --> ExtractETLD1["Extract eTLD+1 using PSL"]
ExtractETLD1 --> ETLD1Valid{"ETLD1 Valid?"}
ETLD1Valid --> |No| ReturnInvalid["Return invalid result"]
ETLD1Valid --> |Yes| GetTLD["Get TLD"]
GetTLD --> TLDValid{"TLD Valid?"}
TLDValid --> |No| ReturnInvalid
TLDValid --> |Yes| ValidateFormat["Validate Domain Format"]
ValidateFormat --> FormatValid{"Format Valid?"}
FormatValid --> |No| ReturnInvalid
FormatValid --> |Yes| ReturnValid["Return valid normalized domain"]
ReturnNull --> End([End])
ReturnInvalid --> End
ReturnValid --> End
```

**Diagram sources**
- [PSLDomainNormalizer.ts](file://services/domain-seeder/src/normalization/PSLDomainNormalizer.ts)

### Data Consistency and Synchronization Analysis
The DataConsistencyService ensures data consistency between the authoritative ScyllaDB store and the Manticore Search index. It performs regular consistency checks and repairs any detected inconsistencies.

#### For Object-Oriented Components:
```mermaid
classDiagram
class DataConsistencyService {
+DataConsistencyConfigType config
+ScyllaClient scyllaClient
+ManticoreClient manticoreClient
+RedisClientWrapper redisClient
+performConsistencyCheck(sampleSize) Promise~ConsistencyCheckResult~
+repairInconsistencies(consistencyResult) Promise~SynchronizationResultType~
+synchronizeScyllaToManticore(domains) Promise~SynchronizationResultType~
+removeDomainsFromManticore(domains) Promise~void~
+getSampleDomainsFromScylla(sampleSize) Promise~string[]~
+checkDomainExistenceInManticore(domains) Promise~Map~string, boolean~~
+startConsistencyMonitoring() void
+stopConsistencyMonitoring() void
}
class ConsistencyCheckResult {
+number totalDomains
+number consistentDomains
+string[] missingInManticore
+string[] missingInScylla
+number inconsistencyRate
+number executionTime
+boolean isWithinThreshold
}
class SynchronizationResultType {
+number syncedToManticore
+number syncedToScylla
+Map~string, Error~ failedDomains
+number executionTime
+boolean success
}
class IndexRebuildResultType {
+number totalDomains
+number indexedDomains
+Map~string, Error~ failedDomains
+number executionTime
+boolean success
}
DataConsistencyService --> ScyllaClient : "reads from"
DataConsistencyService --> ManticoreClient : "writes to"
DataConsistencyService --> RedisClientWrapper : "stores metrics in"
```

**Diagram sources**
- [DataConsistencyService.ts](file://services/domain-seeder/src/synchronization/DataConsistencyService.ts)

#### For Complex Logic Components:
```mermaid
flowchart TD
Start([Start]) --> PerformCheck["Perform Consistency Check"]
PerformCheck --> GetScyllaDomains["Get sample from ScyllaDB"]
GetScyllaDomains --> CheckManticore["Check existence in Manticore"]
CheckManticore --> IdentifyMissing["Identify missing domains"]
IdentifyMissing --> MissingInManticore{"Missing in Manticore?"}
MissingInManticore --> |Yes| SyncToManticore["Sync from ScyllaDB to Manticore"]
MissingInManticore --> |No| MissingInScylla{"Missing in ScyllaDB?"}
MissingInScylla --> |Yes| RemoveFromManticore["Remove from Manticore"]
MissingInScylla --> |No| CalculateRate["Calculate Inconsistency Rate"]
CalculateRate --> WithinThreshold{"Within Threshold?"}
WithinThreshold --> |Yes| LogSuccess["Log success"]
WithinThreshold --> |No| Alert["Send alert"]
LogSuccess --> End([End])
Alert --> End
SyncToManticore --> HandleManticoreErrors["Handle errors"]
HandleManticoreErrors --> MissingInScylla
RemoveFromManticore --> HandleScyllaErrors["Handle errors"]
HandleScyllaErrors --> CalculateRate
```

**Diagram sources**
- [DataConsistencyService.ts](file://services/domain-seeder/src/synchronization/DataConsistencyService.ts)

### Error Handling and Retry Mechanisms Analysis
The error handling and retry mechanisms ensure system reliability and resilience. The ErrorHandler class manages error classification, retry strategies, and graceful degradation, while the RetryManager handles retry logic with exponential backoff.

#### For Object-Oriented Components:
```mermaid
classDiagram
class ErrorHandler {
+Map~string, ErrorCategoryType~ errorCategories
+GracefulDegradationStrategy[] degradationStrategies
+ErrorAggregator errorAggregator
+Map~string, CircuitBreaker~ circuitBreakers
+handleError(operation, context, retryConfig) Promise~ErrorHandlingResultType~
+handleValidationError(validationResult, context) Promise~ErrorHandlingResultType~
+initializeErrorCategories() void
+initializeDegradationStrategies() void
+classifyError(error, context) ErrorCategoryType
+isRetryableError(error, config) boolean
+calculateDelay(retryCount, config) number
+shouldAttemptGracefulDegradation(error, context) boolean
+attemptGracefulDegradation(error, context) Promise~any~
}
class RetryManager {
+RetryStrategyConfigType strategy
+RetryStatisticsType statistics
+executeWithRetry(operation, context, strategy) Promise~any~
+shouldRetry(attempt, error, strategy, errorClassification, context) RetryDecisionType
+calculateDelay(attempt, strategy, context) number
+sleep(ms) Promise~void~
}
class ErrorCategoryType {
+string name
+string description
+'low' | 'medium' | 'high' | 'critical' severity
+boolean isTransient
+number maxRetries
+number baseDelayMs
+number maxDelayMs
}
class RetryStrategyConfigType {
+number maxAttempts
+number baseDelay
+number maxDelay
+number backoffMultiplier
+boolean jitter
+string[] retryableErrors
}
class RetryDecisionType {
+boolean shouldRetry
+number delay
+string reason
+number nextAttemptNumber
}
ErrorHandler --> ErrorCategoryType : "contains"
RetryManager --> RetryStrategyConfigType : "uses"
RetryManager --> RetryDecisionType : "returns"
```

**Diagram sources**
- [ErrorHandler.ts](file://services/domain-seeder/src/reliability/ErrorHandler.ts)
- [RetryManager.ts](file://services/worker/src/errors/RetryManager.ts)

#### For Complex Logic Components:
```mermaid
flowchart TD
Start([Start]) --> ExecuteOperation["Execute Operation"]
ExecuteOperation --> Success{"Success?"}
Success --> |Yes| ReturnResult["Return Result"]
Success --> |No| ClassifyError["Classify Error"]
ClassifyError --> IsTransient{"Transient Error?"}
IsTransient --> |No| ReturnFailure["Return Failure"]
IsTransient --> |Yes| MaxRetries{"Max Retries Reached?"}
MaxRetries --> |Yes| ReturnFailure
MaxRetries --> |No| CalculateDelay["Calculate Delay"]
CalculateDelay --> Wait["Wait Delay Period"]
Wait --> RetryOperation["Retry Operation"]
RetryOperation --> Success
ReturnResult --> End([End])
ReturnFailure --> End
```

**Diagram sources**
- [RetryManager.ts](file://services/worker/src/errors/RetryManager.ts)

## Dependency Analysis
The data processing pipeline has a well-defined dependency structure with clear separation between components. The worker service depends on the shared components for database access, error handling, and validation. The domain-seeder service also depends on shared components and communicates with the worker service through Redis. The web-app and admin services depend on shared components and query the Manticore Search index directly. The system uses a combination of direct dependencies and message-based communication through Redis to maintain loose coupling between components.

```mermaid
graph TD
A[domain-seeder] --> H[shared]
B[worker] --> H
F[web-app] --> H
G[admin] --> H
B --> I[Redis]
A --> I
B --> J[ScyllaDB]
B --> K[Manticore Search]
A --> L[CZDS]
A --> M[Common Crawl]
A --> N[PIR]
A --> O[Radar]
A --> P[Sonar]
A --> Q[Tranco]
A --> R[Umbrella]
H --> S[Redis Client]
H --> T[Manticore Client]
H --> U[Maria Client]
H --> V[Scylla Client]
H --> W[Logger]
H --> X[Error Handling]
H --> Y[Validation]
```

**Diagram sources**
- [package.json](file://services/domain-seeder/package.json)
- [package.json](file://services/worker/package.json)
- [package.json](file://shared/package.json)

**Section sources**
- [package.json](file://services/domain-seeder/package.json)
- [package.json](file://services/worker/package.json)
- [package.json](file://shared/package.json)

## Performance Considerations
The data processing pipeline is designed for high performance and scalability. The system uses multiple optimization strategies including batch processing, connection pooling, and efficient data structures. The worker service can process multiple domains concurrently through the PipelineCoordinator, which manages multiple pipeline instances and distributes load based on resource availability. The system uses Redis for fast job queuing and coordination, and implements distributed locking to prevent duplicate processing. Database queries are optimized with appropriate indexing, and the system uses connection pooling to minimize connection overhead. The validation and normalization components are optimized for performance, with caching of frequently accessed data and efficient algorithms for domain processing.

**Section sources**
- [DomainProcessingPipeline.ts](file://services/worker/src/pipeline/DomainProcessingPipeline.ts)
- [PipelineCoordinator.ts](file://services/worker/src/pipeline/PipelineCoordinator.ts)
- [WorkerService.ts](file://services/worker/src/core/WorkerService.ts)

## Troubleshooting Guide
The data processing pipeline includes comprehensive monitoring and error handling to facilitate troubleshooting. Common issues include database connectivity problems, Redis connectivity issues, and external API rate limiting. The system provides health check endpoints for each service, and logs detailed information about processing operations. When troubleshooting issues, start by checking the service logs and health endpoints. For database connectivity issues, verify the database configuration and network connectivity. For Redis issues, check the Redis server status and configuration. For external API issues, verify API credentials and check rate limit status. The system includes retry mechanisms for transient errors, but persistent failures may require manual intervention.

**Section sources**
- [ErrorHandler.ts](file://services/domain-seeder/src/reliability/ErrorHandler.ts)
- [RetryManager.ts](file://services/worker/src/errors/RetryManager.ts)
- [WorkerJobQueue.ts](file://services/worker/src/queue/WorkerJobQueue.ts)
- [JobConsumer.ts](file://services/worker/src/queue/JobConsumer.ts)

## Conclusion
The domainr data processing pipeline is a robust, scalable system for discovering, validating, normalizing, and indexing domain data. The architecture follows microservices principles with clear separation of concerns, allowing for independent scaling and maintenance of components. The system includes comprehensive error handling, retry mechanisms, and data consistency checks to ensure reliability and data integrity. The use of modern technologies like ScyllaDB, Manticore Search, and Redis enables high-performance processing at scale. The pipeline is designed to be extensible, with well-defined interfaces between components that facilitate future enhancements and modifications.