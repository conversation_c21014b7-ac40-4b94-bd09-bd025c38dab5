# Data Normalization

<cite>
**Referenced Files in This Document**   
- [PSLDomainNormalizer.ts](file://services/domain-seeder/src/normalization/PSLDomainNormalizer.ts)
- [PSLManager.ts](file://services/domain-seeder/src/normalization/PSLManager.ts)
- [DomainNormalizer.ts](file://services/domain-seeder/src/interfaces/DomainNormalizer.ts)
- [ValidationPipeline.ts](file://services/domain-seeder/src/validation/ValidationPipeline.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Project Structure](#project-structure)
3. [Core Components](#core-components)
4. [Architecture Overview](#architecture-overview)
5. [Detailed Component Analysis](#detailed-component-analysis)
6. [Dependency Analysis](#dependency-analysis)
7. [Performance Considerations](#performance-considerations)
8. [Troubleshooting Guide](#troubleshooting-guide)
9. [Conclusion](#conclusion)

## Introduction
This document provides comprehensive architectural documentation for the domain data normalization process within the domainr system. The normalization layer standardizes domain data from various sources into a consistent format, enabling reliable processing and analysis across the platform. The core components responsible for this process are the DomainNormalizer and PSLManager, which work together to transform raw crawler output into normalized domain representations.

The normalization workflow handles URL standardization, hostname parsing, and top-level domain identification using the Public Suffix List (PSL). This process ensures consistency across distributed processing nodes and prepares domain data for subsequent validation and analysis stages. The system is designed to handle high-throughput normalization with robust error handling and performance optimization.

## Project Structure
The domain normalization components are located within the domain-seeder service, specifically in the normalization module. This module contains the core classes responsible for domain standardization and PSL management.

```mermaid
graph TD
subgraph "Domain Seeder Service"
subgraph "Normalization Module"
PSLDomainNormalizer[PSLDomainNormalizer.ts]
PSLManager[PSLManager.ts]
Interfaces[DomainNormalizer.ts]
end
subgraph "Validation Module"
ValidationPipeline[ValidationPipeline.ts]
ErrorRecovery[ErrorRecovery.ts]
end
subgraph "Connectors"
SourceConnectors[Source connectors]
end
subgraph "Core"
HttpServer[HttpServer.ts]
DiscoveryEngine[DiscoveryEngine.ts]
end
end
SourceConnectors --> PSLDomainNormalizer
PSLDomainNormalizer --> ValidationPipeline
PSLManager --> PSLDomainNormalizer
PSLDomainNormalizer --> DiscoveryEngine
```

**Diagram sources**
- [PSLDomainNormalizer.ts](file://services/domain-seeder/src/normalization/PSLDomainNormalizer.ts)
- [PSLManager.ts](file://services/domain-seeder/src/normalization/PSLManager.ts)

**Section sources**
- [PSLDomainNormalizer.ts](file://services/domain-seeder/src/normalization/PSLDomainNormalizer.ts)
- [PSLManager.ts](file://services/domain-seeder/src/normalization/PSLManager.ts)

## Core Components
The domain normalization process is built around two primary components: the PSLDomainNormalizer and PSLManager. These components work in tandem to standardize domain data according to industry standards and best practices.

The PSLDomainNormalizer implements the DomainNormalizerType interface, providing methods for normalizing individual domains and batches of domains. It handles the complete normalization workflow from raw input to standardized output, including cleaning, validation, and eTLD+1 extraction.

The PSLManager manages the Public Suffix List data, which is essential for accurate domain parsing and classification. It handles downloading, caching, and updating the PSL data, ensuring that the normalization process has access to the most current domain suffix information.

**Section sources**
- [PSLDomainNormalizer.ts](file://services/domain-seeder/src/normalization/PSLDomainNormalizer.ts#L1-L50)
- [PSLManager.ts](file://services/domain-seeder/src/normalization/PSLManager.ts#L1-L50)

## Architecture Overview
The domain normalization architecture follows a layered approach with clear separation of concerns. The system processes domain data through a series of well-defined stages, from raw input to normalized output, with validation and error handling at each step.

```mermaid
graph LR
A[Raw Crawler Output] --> B[Domain Normalization]
B --> C[Validation Pipeline]
C --> D[Normalized Domain Data]
subgraph "Normalization Layer"
B --> E[URL Standardization]
E --> F[Hostname Parsing]
F --> G[eTLD+1 Extraction]
G --> H[TLD Identification]
end
subgraph "PSL Management"
I[Public Suffix List] --> J[PSLManager]
J --> K[Cache Storage]
K --> L[Auto-update System]
L --> M[Version History]
end
J --> G
H --> C
style B fill:#f9f,stroke:#333
style J fill:#f9f,stroke:#333
```

**Diagram sources**
- [PSLDomainNormalizer.ts](file://services/domain-seeder/src/normalization/PSLDomainNormalizer.ts#L1-L100)
- [PSLManager.ts](file://services/domain-seeder/src/normalization/PSLManager.ts#L1-L100)

## Detailed Component Analysis

### Domain Normalizer Analysis
The PSLDomainNormalizer class implements the DomainNormalizerType interface and provides comprehensive domain normalization capabilities. It processes raw domain strings through multiple stages to produce standardized domain representations.

```mermaid
classDiagram
class PSLDomainNormalizer {
+logger : Logger
+pslManager : PSLManagerImpl
+metrics : NormalizationMetricsType
+constructor(cacheDir? : string)
+normalize(domain : string) : NormalizedDomainType | null
+isValidDomain(domain : string) : boolean
+getETLD1(domain : string) : string | null
+batchNormalize(domains : string[]) : Promise~Map~string, NormalizedDomainType~~
+getMetrics() : NormalizationMetricsType
+resetMetrics() : void
+healthCheck() : Promise~boolean~
+destroy() : void
}
class PSLManagerImpl {
+logger : Logger
+client : HttpClient
+pslData : PSLDataType | null
+lastUpdate : Date | null
+updateInterval : number
+updateTimer : NodeJS.Timeout | null
+cacheDir : string
+currentVersion : PSLVersionType | null
+versionHistory : PSLVersionType[]
+PSL_URL : string
+PSL_CHECKSUM_URL : string
+MAX_VERSION_HISTORY : number
+MIN_PSL_SIZE : number
+MAX_PSL_SIZE : number
+constructor(cacheDir : string, autoInit : boolean)
+initialize() : Promise~void~
+updatePSL() : Promise~void~
+getLastUpdate() : Date | null
+isValidTLD(tld : string) : boolean
+getDomain(hostname : string) : string | null
+getPublicSuffix(hostname : string) : string | null
+rollbackToVersion(version : string) : Promise~void~
+getVersionHistory() : PSLVersionType[]
+getCurrentVersion() : PSLVersionType | null
+healthCheck() : Promise~boolean~
+getStatistics() : object
+forceUpdate() : Promise~void~
+isUpdateDue() : boolean
+destroy() : void
}
class DomainNormalizerType {
<<interface>>
+normalize(domain : string) : NormalizedDomainType | null
+isValidDomain(domain : string) : boolean
+getETLD1(domain : string) : string | null
+batchNormalize(domains : string[]) : Promise~Map~string, NormalizedDomainType~~
}
PSLDomainNormalizer --> PSLManagerImpl : "uses"
PSLDomainNormalizer --> DomainNormalizerType : "implements"
```

**Diagram sources**
- [PSLDomainNormalizer.ts](file://services/domain-seeder/src/normalization/PSLDomainNormalizer.ts#L1-L50)
- [PSLManager.ts](file://services/domain-seeder/src/normalization/PSLManager.ts#L1-L50)

#### Normalization Workflow
The domain normalization process follows a systematic workflow to transform raw domain input into a standardized format. This workflow ensures consistency and accuracy across all processed domains.

```mermaid
flowchart TD
A[Start] --> B[Input Validation]
B --> C{Valid Input?}
C --> |No| D[Return Invalid Result]
C --> |Yes| E[Clean Domain]
E --> F[Remove Protocol]
F --> G[Remove www Prefix]
G --> H[Remove Path/Query]
H --> I[Convert to Lowercase]
I --> J[Check for IP Address]
J --> K{Is IP Address?}
K --> |Yes| L[Filter Out]
K --> |No| M[Convert to Punycode]
M --> N{Conversion Success?}
N --> |No| D
N --> |Yes| O[Extract eTLD+1]
O --> P{Extraction Success?}
P --> |No| D
P --> |Yes| Q[Get TLD]
Q --> R{TLD Found?}
R --> |No| D
R --> |Yes| S[Validate Format]
S --> T{Valid Format?}
T --> |No| D
T --> |Yes| U[Return Normalized Result]
U --> V[End]
D --> V
style A fill:#f9f,stroke:#333
style V fill:#f9f,stroke:#333
```

**Diagram sources**
- [PSLDomainNormalizer.ts](file://services/domain-seeder/src/normalization/PSLDomainNormalizer.ts#L50-L150)

**Section sources**
- [PSLDomainNormalizer.ts](file://services/domain-seeder/src/normalization/PSLDomainNormalizer.ts#L1-L200)

### PSL Manager Analysis
The PSLManager component is responsible for managing the Public Suffix List data used in domain normalization. It handles downloading, caching, and updating the PSL data to ensure accurate domain parsing.

```mermaid
sequenceDiagram
participant Client as "Normalization Process"
participant PSLManager as "PSLManager"
participant HttpClient as "HTTP Client"
participant Cache as "File System Cache"
Client->>PSLManager : normalize(domain)
PSLManager->>PSLManager : getDomain(hostname)
alt PSL Data Available
PSLManager->>PSLManager : Use cached PSL data
PSLManager->>PSLManager : Parse domain using rules
PSLManager-->>Client : Return eTLD+1
else PSL Data Not Available
PSLManager->>PSLManager : loadLatestPSL()
alt Cache Exists
PSLManager->>Cache : Read cached PSL
Cache-->>PSLManager : PSL content
PSLManager->>PSLManager : Verify checksum
alt Checksum Valid
PSLManager->>PSLManager : Parse PSL content
PSLManager-->>Client : Return eTLD+1
else Checksum Invalid
PSLManager->>PSLManager : updatePSL()
end
else No Cache
PSLManager->>PSLManager : updatePSL()
end
end
alt Scheduled Update
PSLManager->>PSLManager : scheduleUpdates()
PSLManager->>PSLManager : updatePSL()
PSLManager->>HttpClient : GET PSL_URL
HttpClient-->>PSLManager : PSL content
PSLManager->>HttpClient : GET PSL_CHECKSUM_URL
HttpClient-->>PSLManager : Checksum
PSLManager->>PSLManager : Verify content
PSLManager->>Cache : Save PSL data
PSLManager->>PSLManager : Update version history
end
```

**Diagram sources**
- [PSLManager.ts](file://services/domain-seeder/src/normalization/PSLManager.ts#L1-L100)

**Section sources**
- [PSLManager.ts](file://services/domain-seeder/src/normalization/PSLManager.ts#L1-L200)

## Dependency Analysis
The domain normalization components have several key dependencies that enable their functionality. These dependencies include both internal modules and external services.

```mermaid
graph TD
A[PSLDomainNormalizer] --> B[PSLManager]
B --> C[HttpClient]
C --> D[Shared Logger]
B --> E[File System]
B --> F[Crypto Module]
A --> G[Punycode Module]
A --> H[Node.js Net Module]
A --> I[Shared Logger]
style A fill:#f9f,stroke:#333
style B fill:#f9f,stroke:#333
subgraph "External Dependencies"
C
E
F
G
H
end
subgraph "Internal Dependencies"
D
I
end
```

**Diagram sources**
- [PSLDomainNormalizer.ts](file://services/domain-seeder/src/normalization/PSLDomainNormalizer.ts)
- [PSLManager.ts](file://services/domain-seeder/src/normalization/PSLManager.ts)

**Section sources**
- [PSLDomainNormalizer.ts](file://services/domain-seeder/src/normalization/PSLDomainNormalizer.ts#L1-L50)
- [PSLManager.ts](file://services/domain-seeder/src/normalization/PSLManager.ts#L1-L50)

## Performance Considerations
The domain normalization system is designed for high-throughput processing with several performance optimizations in place. The architecture balances memory usage, processing speed, and accuracy to handle large volumes of domain data efficiently.

The PSLManager implements a caching strategy that stores PSL data on disk and loads it into memory for fast access. This eliminates the need to download and parse the PSL on every normalization request. The cache is updated automatically on a 7-day interval, but can be forced to update when needed.

For batch processing, the PSLDomainNormalizer includes a batchNormalize method that processes multiple domains efficiently while avoiding duplicates. The component maintains metrics on processing performance, including counts of valid and invalid domains, which can be used for monitoring and optimization.

The system also implements health checks for both components, allowing for proactive monitoring of normalization service health. These checks verify that the PSL data is current and that basic normalization functionality is working correctly.

**Section sources**
- [PSLDomainNormalizer.ts](file://services/domain-seeder/src/normalization/PSLDomainNormalizer.ts#L300-L378)
- [PSLManager.ts](file://services/domain-seeder/src/normalization/PSLManager.ts#L700-L800)

## Troubleshooting Guide
When troubleshooting issues with the domain normalization system, several key areas should be examined. The components provide comprehensive logging and health checking capabilities to aid in diagnosis.

Common issues include:
- PSL data not updating: Check network connectivity and verify that the PSL URLs are accessible
- Domain normalization failures: Examine the input domains for malformed URLs or invalid characters
- Performance degradation: Monitor the normalization metrics and check for excessive invalid domains
- Cache corruption: The system automatically verifies PSL checksums and will redownload if corruption is detected

The healthCheck methods on both components can be used to verify system functionality. For the PSLManager, this includes checking that the PSL data is reasonably fresh (within 30 days) and that basic domain parsing works correctly. The PSLDomainNormalizer health check verifies that both basic and IDN domain normalization functions properly.

**Section sources**
- [PSLDomainNormalizer.ts](file://services/domain-seeder/src/normalization/PSLDomainNormalizer.ts#L300-L378)
- [PSLManager.ts](file://services/domain-seeder/src/normalization/PSLManager.ts#L700-L800)

## Conclusion
The domain normalization system in domainr provides a robust and efficient solution for standardizing domain data from various sources. The PSLDomainNormalizer and PSLManager components work together to ensure consistent domain representation across the platform.

Key strengths of the system include:
- Comprehensive handling of internationalized domain names through Punycode conversion
- Accurate eTLD+1 extraction using the Public Suffix List
- Robust error handling and validation at each processing stage
- Efficient caching and batch processing capabilities
- Automated PSL updates with version history and rollback capabilities

The architecture supports high-throughput processing while maintaining accuracy and reliability. The separation of concerns between normalization and PSL management allows for independent scaling and maintenance of each component. Future enhancements could include additional validation rules and improved performance monitoring.