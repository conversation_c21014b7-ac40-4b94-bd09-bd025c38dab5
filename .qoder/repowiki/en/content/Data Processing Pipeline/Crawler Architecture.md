# Crawler Architecture

<cite>
**Referenced Files in This Document**   
- [DataCollectionOrchestrator.ts](file://services/worker/src/crawler/core/DataCollectionOrchestrator.ts)
- [ModuleRegistry.ts](file://services/worker/src/crawler/core/ModuleRegistry.ts)
- [SelectiveDataCollectionService.ts](file://services/worker/src/crawler/core/SelectiveDataCollectionService.ts)
- [EnhancedResourceMonitor.ts](file://services/worker/src/crawler/core/EnhancedResourceMonitor.ts)
- [DataCollectionModule.ts](file://services/worker/src/crawler/core/DataCollectionModule.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Core Components](#core-components)
3. [Data Collection Orchestration](#data-collection-orchestration)
4. [Module Registry and Execution](#module-registry-and-execution)
5. [Selective Data Collection Service](#selective-data-collection-service)
6. [Resource Monitoring and Optimization](#resource-monitoring-and-optimization)
7. [Component Interactions](#component-interactions)
8. [Infrastructure and Concurrency](#infrastructure-and-concurrency)
9. [Error Handling and Retry Mechanisms](#error-handling-and-retry-mechanisms)
10. [Technology Stack and Dependencies](#technology-stack-and-dependencies)

## Introduction
The domainr crawler architecture is designed as a modular, scalable system for comprehensive domain analysis. The architecture centers around three core components: the DataCollectionOrchestrator, ModuleRegistry, and SelectiveDataCollectionService, which work together to enable intelligent, selective data collection based on domain characteristics and resource constraints. The system implements sophisticated resource monitoring through the EnhancedResourceMonitor to ensure efficient operation under varying loads. This documentation provides a comprehensive overview of the crawler's architecture, detailing component interactions, execution workflows, and optimization strategies.

## Core Components
The crawler architecture consists of several key components that work together to provide a robust domain analysis system. The DataCollectionOrchestrator manages the overall data collection process, making intelligent decisions about what data to collect based on existing information and resource constraints. The ModuleRegistry maintains and manages all available analysis modules, handling their execution and dependencies. The SelectiveDataCollectionService provides a high-level interface for initiating data collection with various predefined profiles. The EnhancedResourceMonitor tracks system resource usage and provides predictive capabilities for resource allocation. These components work in concert to enable efficient, selective data collection while maintaining system stability and performance.

**Section sources**
- [DataCollectionOrchestrator.ts](file://services/worker/src/crawler/core/DataCollectionOrchestrator.ts#L1-L50)
- [ModuleRegistry.ts](file://services/worker/src/crawler/core/ModuleRegistry.ts#L1-L50)
- [SelectiveDataCollectionService.ts](file://services/worker/src/crawler/core/SelectiveDataCollectionService.ts#L1-L50)
- [EnhancedResourceMonitor.ts](file://services/worker/src/crawler/core/EnhancedResourceMonitor.ts#L1-L50)

## Data Collection Orchestration
The DataCollectionOrchestrator is the central component responsible for managing the data collection process. It implements a sophisticated decision-making algorithm that determines which modules should be executed based on the current request parameters, existing data, and resource constraints. The orchestrator begins by creating an optimized collection plan that identifies which modules need to be run and which can be skipped based on data freshness and completeness requirements.

The orchestrator evaluates several factors when determining whether to collect data from a specific module:
- Data freshness: Compares the age of existing data against module-specific maximum age thresholds
- Required fields: Checks if specific data fields are missing from existing data
- Skip conditions: Evaluates whether certain conditions are met that would make collection unnecessary

For example, DNS data has a maximum age threshold of 24 hours, while robots.txt data can remain fresh for up to 168 hours (1 week). This intelligent selection process significantly reduces unnecessary network requests and processing overhead, improving overall system efficiency.

```mermaid
flowchart TD
Start([Data Collection Request]) --> PlanCreation["Create Collection Plan"]
PlanCreation --> DataAnalysis["Analyze Existing Data"]
DataAnalysis --> FreshnessCheck{"Data Fresh?"}
FreshnessCheck --> |Yes| SkipModule["Skip Module"]
FreshnessCheck --> |No| ExecuteModule["Execute Module"]
DataAnalysis --> RequiredFields{"Required Fields Missing?"}
RequiredFields --> |Yes| ExecuteModule
RequiredFields --> |No| SkipModule
ExecuteModule --> Validation["Validate Collected Data"]
Validation --> Recommendations["Generate Recommendations"]
Recommendations --> Response["Return Response"]
SkipModule --> Response
```

**Diagram sources **
- [DataCollectionOrchestrator.ts](file://services/worker/src/crawler/core/DataCollectionOrchestrator.ts#L166-L1079)

**Section sources**
- [DataCollectionOrchestrator.ts](file://services/worker/src/crawler/core/DataCollectionOrchestrator.ts#L166-L1079)

## Module Registry and Execution
The ModuleRegistry serves as the central repository for all data collection modules, managing their registration, configuration, and execution. It provides a standardized interface for module execution while handling complex dependency relationships and execution ordering. The registry maintains a collection of modules, each with its own configuration including priority, timeout settings, retry policies, and dependencies.

When executing multiple modules, the registry creates an optimized execution plan that considers both module priorities and dependencies. It resolves execution order to ensure that dependent modules are executed before their dependents, preventing circular dependencies through validation. The execution plan groups modules into parallel execution groups where possible, maximizing concurrency while respecting dependency constraints.

The registry also provides comprehensive statistics about module performance, including success rates, average execution times, and failure patterns. This information is used by the orchestrator to make optimization recommendations for future collections, such as prioritizing fast, reliable modules or skipping consistently failing ones.

```mermaid
classDiagram
class ModuleRegistry {
+modules Map[string, DataCollectionModule]
+moduleConfigs Map[string, any]
+registerModule(name, module)
+unregisterModule(name)
+getModuleNames()
+getModule(name)
+createExecutionPlan(requestedModules)
+executeModules(domain, moduleNames)
+getModuleStats()
+validateConfiguration()
}
class DataCollectionModule {
+config DataCollectionModuleConfigType
+logger
+execute(domain)
+executeWithTimeout(domain)
+collect(domain)
+getConfig()
+canRun(availableModules)
}
ModuleRegistry --> DataCollectionModule : "manages"
DataCollectionModule <|-- DNSModule : "implements"
DataCollectionModule <|-- SSLModule : "implements"
DataCollectionModule <|-- RobotsModule : "implements"
DataCollectionModule <|-- DomainInfoModule : "implements"
DataCollectionModule <|-- HomepageModule : "implements"
DataCollectionModule <|-- FaviconModule : "implements"
```

**Diagram sources **
- [ModuleRegistry.ts](file://services/worker/src/crawler/core/ModuleRegistry.ts#L30-L391)
- [DataCollectionModule.ts](file://services/worker/src/crawler/core/DataCollectionModule.ts#L1-L154)

**Section sources**
- [ModuleRegistry.ts](file://services/worker/src/crawler/core/ModuleRegistry.ts#L30-L391)
- [DataCollectionModule.ts](file://services/worker/src/crawler/core/DataCollectionModule.ts#L1-L154)

## Selective Data Collection Service
The SelectiveDataCollectionService provides a high-level interface for initiating data collection with various predefined profiles tailored to different use cases. It acts as a facade to the underlying orchestrator and registry, simplifying the API for clients while enabling sophisticated collection strategies. The service comes with several built-in collection profiles:

- **Quick profile**: Collects essential data (DNS, robots.txt) with a 10-second timeout
- **Standard profile**: Comprehensive basic analysis (DNS, robots.txt, SSL, domain info) with a 30-second timeout
- **Complete profile**: Full analysis with all available modules and extended timeouts
- **Security profile**: Focuses on security-related analysis (SSL, security headers)
- **Performance profile**: Emphasizes performance metrics and Core Web Vitals
- **Visual profile**: Includes screenshot capture for visual analysis
- **Refresh profile**: Updates existing data without selective collection

Each profile specifies which modules to include, execution priority, timeout values, retry policies, and selective collection criteria. The service allows for both single-domain and batch collection operations, with configurable concurrency levels to balance performance and resource usage. It also provides methods for collecting only missing data or refreshing stale data, enabling targeted updates without full re-analysis.

```mermaid
sequenceDiagram
participant Client as "Client Application"
participant Service as "SelectiveDataCollectionService"
participant Orchestrator as "DataCollectionOrchestrator"
participant Registry as "ModuleRegistry"
Client->>Service : collectDomain(domain, profile)
Service->>Service : Resolve profile configuration
Service->>Orchestrator : collectData(request)
Orchestrator->>Orchestrator : createCollectionPlan()
Orchestrator->>Orchestrator : getExistingData()
Orchestrator->>Registry : executeModules()
Registry->>Registry : createExecutionPlan()
Registry->>Module : execute(domain)
Module-->>Registry : Return result
Registry-->>Orchestrator : Return execution results
Orchestrator->>Orchestrator : validateCollectedData()
Orchestrator->>Orchestrator : generateRecommendations()
Orchestrator-->>Service : Return response
Service-->>Client : Return collection results
```

**Diagram sources **
- [SelectiveDataCollectionService.ts](file://services/worker/src/crawler/core/SelectiveDataCollectionService.ts#L59-L584)

**Section sources**
- [SelectiveDataCollectionService.ts](file://services/worker/src/crawler/core/SelectiveDataCollectionService.ts#L59-L584)

## Resource Monitoring and Optimization
The EnhancedResourceMonitor provides comprehensive tracking of system resource usage during data collection operations. It monitors memory consumption, CPU utilization, network activity, and other key metrics at regular intervals, providing real-time insights into system performance. The monitor operates on a session-based model, allowing for detailed tracking of resource usage for specific operations or domains.

Key features of the resource monitor include:
- **Threshold-based alerting**: Generates warnings when resource usage exceeds configurable thresholds
- **Predictive capabilities**: Uses historical data to predict resource requirements for future operations
- **Session-based monitoring**: Tracks resource usage for specific operations with detailed summaries
- **Garbage collection monitoring**: Observes GC events to identify memory pressure issues
- **Efficiency calculations**: Computes resource efficiency scores based on utilization patterns

The monitor integrates with the orchestrator to provide resource usage metrics in the final response, enabling clients to understand the cost of data collection operations. It also supports custom metrics, allowing modules to report specific performance characteristics. The predictive capabilities help optimize resource allocation by forecasting the requirements for similar future operations based on historical patterns.

```mermaid
flowchart TD
Start([Start Monitoring Session]) --> CollectMetrics["Collect Metrics"]
CollectMetrics --> MemoryCheck{"Memory Threshold Exceeded?"}
MemoryCheck --> |Yes| MemoryAlert["Generate Memory Alert"]
MemoryCheck --> |No| CPUCheck{"CPU Threshold Exceeded?"}
CPUCheck --> |Yes| CPUAlert["Generate CPU Alert"]
CPUCheck --> |No| NetworkCheck{"Network Threshold Exceeded?"}
NetworkCheck --> |Yes| NetworkAlert["Generate Network Alert"]
NetworkCheck --> |No| Continue["Continue Monitoring"]
MemoryAlert --> LogAlert["Log Alert"]
CPUAlert --> LogAlert
NetworkAlert --> LogAlert
LogAlert --> StoreMetrics["Store Metrics History"]
Continue --> CollectMetrics
StoreMetrics --> End([End Monitoring Session])
```

**Diagram sources **
- [EnhancedResourceMonitor.ts](file://services/worker/src/crawler/core/EnhancedResourceMonitor.ts#L130-L822)

**Section sources**
- [EnhancedResourceMonitor.ts](file://services/worker/src/crawler/core/EnhancedResourceMonitor.ts#L130-L822)

## Component Interactions
The crawler components interact through well-defined interfaces, creating a cohesive system for domain analysis. The primary data flow begins with a client request to the SelectiveDataCollectionService, which translates the request into a specific collection profile. The service then delegates to the DataCollectionOrchestrator, which coordinates with the ModuleRegistry to execute the required analysis modules.

The orchestrator maintains an execution history tracker that records the results of previous collections for each domain. This history is used to optimize future collections by identifying patterns in module performance, such as consistently failing modules or particularly fast ones. The resource monitor operates alongside the collection process, providing real-time feedback on system resource usage.

When a collection request is received, the orchestrator first checks the execution history and existing data cache to determine what information is already available. Based on this assessment, it creates an optimized collection plan that minimizes redundant work. The plan is then executed through the module registry, which manages the parallel execution of independent modules while respecting dependency constraints.

The entire process is designed to be idempotent and stateless, allowing for easy scaling and fault tolerance. Each component exposes its functionality through clean interfaces, enabling independent development and testing.

```mermaid
graph TB
subgraph "Client Layer"
Client[Client Application]
end
subgraph "Service Layer"
Service[SelectiveDataCollectionService]
end
subgraph "Orchestration Layer"
Orchestrator[DataCollectionOrchestrator]
History[Execution History Tracker]
end
subgraph "Execution Layer"
Registry[ModuleRegistry]
Modules[Analysis Modules]
end
subgraph "Monitoring Layer"
Monitor[EnhancedResourceMonitor]
end
Client --> Service
Service --> Orchestrator
Orchestrator --> History
Orchestrator --> Registry
Registry --> Modules
Orchestrator --> Monitor
Monitor --> Orchestrator
History --> Orchestrator
```

**Diagram sources **
- [DataCollectionOrchestrator.ts](file://services/worker/src/crawler/core/DataCollectionOrchestrator.ts#L166-L1079)
- [ModuleRegistry.ts](file://services/worker/src/crawler/core/ModuleRegistry.ts#L30-L391)
- [SelectiveDataCollectionService.ts](file://services/worker/src/crawler/core/SelectiveDataCollectionService.ts#L59-L584)
- [EnhancedResourceMonitor.ts](file://services/worker/src/crawler/core/EnhancedResourceMonitor.ts#L130-L822)

**Section sources**
- [DataCollectionOrchestrator.ts](file://services/worker/src/crawler/core/DataCollectionOrchestrator.ts#L166-L1079)
- [ModuleRegistry.ts](file://services/worker/src/crawler/core/ModuleRegistry.ts#L30-L391)
- [SelectiveDataCollectionService.ts](file://services/worker/src/crawler/core/SelectiveDataCollectionService.ts#L59-L584)
- [EnhancedResourceMonitor.ts](file://services/worker/src/crawler/core/EnhancedResourceMonitor.ts#L130-L822)

## Infrastructure and Concurrency
The crawler architecture is designed to support concurrent crawling operations with careful rate limiting and resource management. The system leverages Node.js's event-driven, non-blocking I/O model to handle multiple concurrent requests efficiently. The SelectiveDataCollectionService supports batch operations with configurable concurrency levels, allowing clients to balance throughput with resource constraints.

Rate limiting is implemented at multiple levels:
- **Per-module rate limits**: Individual modules can implement their own rate limiting based on external API constraints
- **System-wide rate limits**: The orchestrator monitors overall system resource usage and can throttle operations when thresholds are exceeded
- **Domain-specific rate limits**: Certain domains may have specific rate limiting requirements based on their characteristics

The EnhancedResourceMonitor plays a crucial role in concurrency management by providing real-time feedback on system resource usage. When resource thresholds are approached, the system can automatically reduce concurrency or delay non-critical operations. The monitor also tracks network request patterns, helping to prevent overwhelming external services with too many simultaneous requests.

For high-volume operations, the system supports distributed deployment across multiple worker instances, with load balancing to distribute the workload evenly. Each worker maintains its own resource monitoring and execution history, while shared storage systems (such as Redis) can be used to coordinate state across instances when necessary.

**Section sources**
- [SelectiveDataCollectionService.ts](file://services/worker/src/crawler/core/SelectiveDataCollectionService.ts#L59-L584)
- [EnhancedResourceMonitor.ts](file://services/worker/src/crawler/core/EnhancedResourceMonitor.ts#L130-L822)

## Error Handling and Retry Mechanisms
The crawler implements comprehensive error handling and retry mechanisms to ensure robust operation in the face of network issues, service outages, and other transient failures. Each data collection module includes built-in retry logic with configurable parameters including maximum retry attempts, retry delay, and backoff multiplier.

When a module fails to collect data, the system follows a structured error handling process:
1. Log the error with detailed context for debugging
2. Apply exponential backoff before retrying (if retries remain)
3. After exhausting retries, record the failure in the execution history
4. Generate recommendations for future attempts based on failure patterns

The orchestrator analyzes failure patterns across multiple executions to make intelligent recommendations. For example, if a particular module consistently fails for a specific domain type, the system may recommend skipping that module in future collections for similar domains. Conversely, for critical modules that fail, the system recommends retrying with adjusted parameters such as increased timeouts.

The system also implements circuit breaker patterns to prevent cascading failures. If a particular external service is consistently unavailable, the system can temporarily disable requests to that service and fail fast, preventing resource exhaustion from repeated failed attempts. Error recovery strategies are configurable per module, allowing different tolerance levels for different types of analysis.

```mermaid
flowchart TD
Start([Module Execution]) --> Attempt["Execute Module"]
Attempt --> Success{"Success?"}
Success --> |Yes| Complete["Return Success"]
Success --> |No| RetryCheck{"Retries Remaining?"}
RetryCheck --> |Yes| Backoff["Apply Backoff Delay"]
Backoff --> Attempt
RetryCheck --> |No| RecordFailure["Record Failure in History"]
RecordFailure --> AnalyzePattern["Analyze Failure Pattern"]
AnalyzePattern --> GenerateRecommendation["Generate Recommendation"]
GenerateRecommendation --> ReturnError["Return Error Response"]
```

**Diagram sources **
- [DataCollectionModule.ts](file://services/worker/src/crawler/core/DataCollectionModule.ts#L1-L154)
- [DataCollectionOrchestrator.ts](file://services/worker/src/crawler/core/DataCollectionOrchestrator.ts#L166-L1079)

**Section sources**
- [DataCollectionModule.ts](file://services/worker/src/crawler/core/DataCollectionModule.ts#L1-L154)
- [DataCollectionOrchestrator.ts](file://services/worker/src/crawler/core/DataCollectionOrchestrator.ts#L166-L1079)

## Technology Stack and Dependencies
The crawler is built using a modern TypeScript/Node.js stack with a focus on maintainability and performance. The core technology stack includes:

- **Runtime**: Node.js 18+ with TypeScript 5.0
- **Package Manager**: pnpm for efficient dependency management
- **Logging**: Pino-based structured logging system with multiple log levels and JSON output
- **Testing**: Vitest for unit and integration testing with comprehensive coverage
- **Monitoring**: Prometheus integration for metrics collection and alerting
- **Caching**: Redis for high-performance caching with configurable TTLs
- **Database**: MariaDB and Manticore Search for persistent storage and indexing

Key third-party dependencies include:
- **Puppeteer**: For browser-based analysis and screenshot capture
- **Axios**: For HTTP requests with robust error handling
- **Redis**: For distributed caching and rate limiting
- **Manticore Search**: For full-text search and indexing capabilities
- **Prometheus-client**: For metrics collection and monitoring
- **Pino**: For structured logging

The system follows semantic versioning for all dependencies, with regular updates to ensure security and performance improvements. Version compatibility is maintained through comprehensive testing, including unit tests, integration tests, and performance benchmarks. The architecture is designed to be extensible, allowing new modules and dependencies to be added with minimal impact on existing functionality.

**Section sources**
- [package.json](file://package.json#L1-L50)
- [pnpm-lock.yaml](file://pnpm-lock.yaml#L1-L50)
- [vitest.config.ts](file://vitest.config.ts#L1-L50)