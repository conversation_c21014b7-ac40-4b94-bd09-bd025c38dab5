# Developer Guide

<cite>
**Referenced Files in This Document**   
- [README.md](file://README.md)
- [TODO.md](file://TODO.md)
- [CRUSH.md](file://CRUSH.md)
- [services/admin/README.md](file://services/admin/README.md)
- [services/admin/DEPLOYMENT.md](file://services/admin/DEPLOYMENT.md)
- [services/admin/TESTING.md](file://services/admin/TESTING.md)
- [services/domain-seeder/README.md](file://services/domain-seeder/README.md)
- [services/worker/README.md](file://services/worker/README.md)
- [docker-compose.yml](file://docker-compose.yml)
- [docker-compose.dev.yml](file://docker-compose.dev.yml)
- [package.json](file://package.json)
- [pnpm-workspace.yaml](file://pnpm-workspace.yaml)
- [eslint.config.js](file://eslint.config.js)
- [vitest.config.ts](file://vitest.config.ts)
- [services/admin/src/lib/auth/README.md](file://services/admin/src/lib/auth/README.md)
- [services/admin/src/lib/errors/README.md](file://services/admin/src/lib/errors/README.md)
- [services/domain-seeder/src/api/README.md](file://services/domain-seeder/src/api/README.md)
- [services/domain-seeder/src/connectors/README.md](file://services/domain-seeder/src/connectors/README.md)
- [services/domain-seeder/src/content/README.md](file://services/domain-seeder/src/content/README.md)
- [services/domain-seeder/src/discovery/README.md](file://services/domain-seeder/src/discovery/README.md)
- [services/domain-seeder/src/monitoring/README.md](file://services/domain-seeder/src/monitoring/README.md)
- [services/domain-seeder/src/provenance/README.md](file://services/domain-seeder/src/provenance/README.md)
- [services/domain-seeder/src/ratelimiting/README.md](file://services/domain-seeder/src/ratelimiting/README.md)
- [services/domain-seeder/src/reliability/README.md](file://services/domain-seeder/src/reliability/README.md)
- [services/domain-seeder/src/repositories/README.md](file://services/domain-seeder/src/repositories/README.md)
- [services/domain-seeder/src/scheduling/README.md](file://services/domain-seeder/src/scheduling/README.md)
- [services/domain-seeder/src/validation/README.md](file://services/domain-seeder/src/validation/README.md)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Development Workflow](#development-workflow)
3. [Contribution Guidelines](#contribution-guidelines)
4. [Code Quality Standards](#code-quality-standards)
5. [Testing Strategy](#testing-strategy)
6. [Debugging Tips](#debugging-tips)
7. [Performance Optimization Techniques](#performance-optimization-techniques)
8. [Pull Request Process](#pull-request-process)
9. [Code Review Standards](#code-review-standards)
10. [Monorepo Structure](#monorepo-structure)
11. [Microservices Architecture](#microservices-architecture)
12. [Development Environment Setup](#development-environment-setup)
13. [Deployment Process](#deployment-process)
14. [Monitoring and Observability](#monitoring-and-observability)
15. [Troubleshooting Common Issues](#troubleshooting-common-issues)
16. [Best Practices](#best-practices)

## Introduction

The Domainr platform is a comprehensive domain ranking and analysis system that evaluates websites based on performance, security, SEO, and technical metrics. The system follows a microservices architecture with a monorepo structure, using modern technologies including TypeScript, Node.js, React, and various database systems (ScyllaDB, MariaDB, Redis, Manticore Search).

This developer guide provides comprehensive documentation for contributing to the project, covering development workflows, code quality standards, testing strategies, debugging techniques, and performance optimization practices. The guide is designed to support both beginners and experienced developers working with the codebase.

The platform consists of three main services:
- **Web Application**: Express.js API and React frontend for domain search and analysis
- **Worker Service**: Combined service handling data collection, analysis, scoring, and job scheduling
- **Domain Seeder**: Domain discovery and seeding service

All services share common utilities and models through the shared package, enabling consistent implementation across the codebase.

**Section sources**
- [README.md](file://README.md#L1-L735)
- [TODO.md](file://TODO.md#L1-L415)

## Development Workflow

The development workflow for the Domainr platform follows a structured process that emphasizes code quality, testing, and collaboration. The workflow is designed to support both individual development and team collaboration in the monorepo environment.

### Branching Strategy

The project uses a Git branching model with the following conventions:
- `main` - Production-ready code
- `develop` - Integration branch for features
- Feature branches - `feature/short-description` for new functionality
- Bugfix branches - `bugfix/issue-description` for fixes
- Release branches - `release/vX.X.X` for version preparation

### Task Management

Tasks are tracked in the TODO.md file with priority levels and status markers:
- 🔥 Critical - Must fix ASAP
- ⚠️ High - Important issues requiring attention
- 📌 Medium - Standard priority items
- 💡 Low - Nice-to-have improvements

Each TODO includes file paths and line numbers for precise location of required changes.

### Development Commands

The project uses pnpm as the package manager with workspace support. Key commands include:

```bash
# Install all dependencies
pnpm install

# Run development servers for all services
pnpm dev

# Build all services
pnpm build

# Run tests across all workspaces
pnpm test

# Lint code
pnpm lint
pnpm lint:fix
```

Service-specific commands can be run from the root using workspace filters:
```bash
# Run dev in a specific service
pnpm --filter domain-ranking-worker run dev

# Add dependency to specific workspace
pnpm --filter shared add lodash
```

### Code Generation and Scaffolding

The platform includes various scripts and tools to support development:
- CLI tools for domain analysis and content generation
- Configuration profiles for different environments
- Automated deployment scripts
- Database migration utilities

**Section sources**
- [README.md](file://README.md#L500-L735)
- [TODO.md](file://TODO.md#L1-L415)
- [package.json](file://package.json#L1-L100)

## Contribution Guidelines

Contributors to the Domainr platform should follow these guidelines to ensure code quality and consistency across the codebase.

### Getting Started

1. Fork the repository
2. Create a feature branch from `develop`
3. Make your changes following code quality standards
4. Add appropriate tests
5. Run linting and tests locally
6. Submit a pull request to the `develop` branch

### Code Style

The project enforces consistent code style through ESLint and Prettier configurations. Key style rules include:
- Use TypeScript with strict type checking
- Follow Airbnb JavaScript style guide with TypeScript extensions
- Use ES6+ syntax and features
- Import statements should be organized and deduplicated
- Use meaningful variable and function names

Configuration files are located in the root and _config directories:
- eslint.config.js - Main ESLint configuration
- tsconfig.json - TypeScript configuration
- prettier.config.js - Code formatting rules

### Documentation

All public APIs and complex functions should be documented with JSDoc comments. Documentation should include:
- Function purpose and behavior
- Parameter types and descriptions
- Return value descriptions
- Example usage when applicable

Component documentation is maintained in README.md files within component directories, particularly in the admin service components.

### Testing Requirements

All contributions must include appropriate tests:
- Unit tests for new functions and classes
- Integration tests for service interactions
- Type tests for shared interfaces
- Performance tests for critical paths

Tests should achieve high code coverage and test both success and failure cases.

### Commit Messages

Commit messages should follow conventional commit format:
```
<type>(<scope>): <subject>
<BLANK LINE>
<body>
<BLANK LINE>
<footer>
```

Types include: feat, fix, docs, style, refactor, test, chore
Scope should indicate the affected service or component

**Section sources**
- [README.md](file://README.md#L650-L735)
- [TODO.md](file://TODO.md#L1-L415)
- [eslint.config.js](file://eslint.config.js#L1-L50)

## Code Quality Standards

The Domainr platform maintains high code quality standards through automated tools, code reviews, and established best practices.

### TypeScript Usage

The codebase uses TypeScript extensively with strict type checking enabled. Key practices include:
- Avoid using `any` type; use proper interfaces and types
- Use union types and type guards for complex type handling
- Implement interfaces for shared data structures
- Use generics for reusable components and functions
- Enable strict compilation options in tsconfig.json

Type errors are tracked and prioritized in TODO.md, with 1,413 type errors identified across the codebase, primarily consisting of:
- TS2345: Argument type mismatch (482 errors)
- TS2339: Property doesn't exist on type (454 errors)
- TS2724: Module has no exported member (86 errors)

### Error Handling

Comprehensive error handling is implemented throughout the system:
- Use structured error types with classification and severity levels
- Implement global error handlers for all services
- Add circuit breakers for external service calls
- Use retry mechanisms with exponential backoff
- Implement proper error logging and monitoring

The worker service includes an ErrorAnalyticsEngine that classifies errors by category (network, database, external_service, resource) and severity, generating suggested actions for resolution.

### Logging Practices

The platform uses structured logging with Pino logger:
- Replace console.log statements with proper logger calls
- Use different log levels (error, warn, info, debug)
- Include context information in log entries
- Implement centralized logging in production
- Add correlation IDs for request tracing

A TODO item tracks the replacement of 1,125 console.log statements across services, with priority given to non-CLI files and critical services.

### Code Organization

Services follow a consistent directory structure:
- src/ - Main source code
- __tests__/ - Test files
- config/ - Configuration files
- scripts/ - Utility scripts
- types/ - Type definitions
- utils/ - Utility functions
- lib/ - Library components

Shared code is organized in the shared workspace with clear separation of concerns:
- database/ - Database clients and managers
- models/ - Data models and interfaces
- utils/ - Common utilities
- middleware/ - Express middleware functions
- queue/ - Job queue management

**Section sources**
- [TODO.md](file://TODO.md#L100-L415)
- [services/worker/src/errors/ErrorAnalyticsEngine.ts](file://services/worker/src/errors/ErrorAnalyticsEngine.ts#L1-L100)
- [services/admin/src/lib/errors/README.md](file://services/admin/src/lib/errors/README.md#L1-L50)

## Testing Strategy

The Domainr platform employs a comprehensive testing strategy to ensure reliability and maintainability of the codebase.

### Test Types

The testing strategy includes multiple layers of testing:

#### Unit Tests
- Test individual functions and classes in isolation
- Use Vitest as the test runner
- Achieve high code coverage (target: 80%+)
- Test both success and failure cases
- Use mocks for external dependencies

#### Integration Tests
- Test interactions between components
- Verify service-to-service communication
- Test database operations with real connections
- Validate API endpoints and request handling
- Located in __tests__/integration directories

#### End-to-End (E2E) Tests
- Test complete user workflows
- Use Playwright for browser automation
- Run against running services
- Validate UI functionality and API integration
- Located in tests/e2e directories

#### Load and Performance Tests
- Test system under heavy load
- Measure response times and resource usage
- Identify performance bottlenecks
- Validate scalability
- Located in tests/load directories

### Test Organization

Tests are organized by service and component:
- Each service has its own test suite
- Component tests are co-located with source files
- Integration tests are in dedicated directories
- E2E tests are in top-level tests directory

The admin service includes visual regression tests using Playwright to ensure UI consistency.

### Testing Tools

Key testing tools and frameworks:
- **Vitest**: Primary test runner for unit and integration tests
- **Playwright**: E2E testing and browser automation
- **Jest**: Used for some legacy tests
- **Supertest**: API endpoint testing
- **MSW (Mock Service Worker)**: API mocking
- **Puppeteer**: Browser automation for crawler tests

### Test Coverage Goals

The project aims to achieve:
- 80%+ unit test coverage
- Comprehensive integration tests for critical paths
- E2E coverage for key user workflows
- Performance benchmarks for core functionality
- Load testing for API endpoints

A TODO item tracks increasing test coverage, noting that there are currently 203 test files for the entire codebase but missing E2E tests and integration tests for critical paths.

### Running Tests

Tests can be run at different levels:
```bash
# Run all tests
pnpm test

# Run tests for a specific service
pnpm --filter domain-ranking-worker test

# Run unit tests only
pnpm test:unit

# Run integration tests
pnpm test:integration

# Run E2E tests
pnpm test:e2e

# Run tests in watch mode
pnpm test:watch
```

**Section sources**
- [README.md](file://README.md#L600-L650)
- [TODO.md](file://TODO.md#L350-L415)
- [vitest.config.ts](file://vitest.config.ts#L1-L50)

## Debugging Tips

Effective debugging is essential for maintaining and enhancing the Domainr platform. The system provides several tools and techniques to help diagnose and resolve issues.

### Error Monitoring and Debugging

The admin service includes a comprehensive error monitoring dashboard that provides:
- Real-time error tracking and visualization
- Error classification by category and severity
- Trend analysis over time
- Debug information with reproduction steps
- Service state monitoring during errors

The ErrorMonitoringDashboard component displays error details including:
- Error ID and message
- Timestamp and frequency
- Resolution status
- Debug information with environment details
- Service states at time of error

```mermaid
flowchart TD
A[Error Occurs] --> B{Error Classification}
B --> C[Network Error]
B --> D[Database Error]
B --> E[External Service Error]
B --> F[Resource Error]
C --> G[Check Connectivity]
D --> H[Verify Database Health]
E --> I[Review API Limits]
F --> J[Monitor Resource Usage]
G --> K[Suggested Actions]
H --> K
I --> K
J --> K
K --> L[Resolution Steps]
```

**Diagram sources**
- [services/admin/src/components/Errors/ErrorMonitoringDashboard.tsx](file://services/admin/src/components/Errors/ErrorMonitoringDashboard.tsx#L1-L100)
- [services/worker/src/errors/ErrorAnalyticsEngine.ts](file://services/worker/src/errors/ErrorAnalyticsEngine.ts#L1-L100)

### Debug Endpoints

The system provides API endpoints for retrieving debug information:
- `/api/errors/debug/[errorId]` - Retrieve detailed debug information for a specific error
- Supports JSON and text formats
- Includes reproduction steps and environment information
- Provides service state snapshots

The debug endpoint returns structured information including:
- Error ID and classification
- Timestamp and occurrence count
- Reproduction steps
- Environment information (versions, configuration)
- Service states (health, response times)
- Stack traces and context data

### Logging and Tracing

Effective debugging relies on comprehensive logging:
- Use structured JSON logging with Pino
- Include correlation IDs for request tracing
- Log at appropriate levels (error, warn, info, debug)
- Include context information in log entries
- Use centralized logging in production

The platform should implement log aggregation and analysis to identify patterns and trends in errors.

### Common Debugging Scenarios

#### Memory Issues
- Monitor memory usage with Docker stats
- Check for memory leaks in long-running processes
- Use streaming for large data processing
- Implement proper cleanup in event handlers

#### Database Connection Problems
- Verify database service availability
- Check connection strings and credentials
- Monitor connection pool usage
- Implement connection retry logic
- Review firewall and network configuration

#### Performance Bottlenecks
- Use profiling tools to identify slow functions
- Monitor database query performance
- Check for inefficient algorithms
- Analyze network latency
- Review resource utilization

**Section sources**
- [services/admin/src/components/Errors/ErrorMonitoringDashboard.tsx](file://services/admin/src/components/Errors/ErrorMonitoringDashboard.tsx#L1-L100)
- [services/admin/src/app/api/errors/debug/[errorId]/route.ts](file://services/admin/src/app/api/errors/debug/[errorId]/route.ts#L1-L100)
- [services/worker/src/errors/ErrorAnalyticsEngine.ts](file://services/worker/src/errors/ErrorAnalyticsEngine.ts#L1-L100)

## Performance Optimization Techniques

The Domainr platform requires careful performance optimization to handle large-scale domain analysis and ranking operations efficiently.

### Database Optimizations

#### Connection Management
- Implement connection pooling for MariaDB and ScyllaDB
- Optimize Redis connection management
- Use prepared statements for frequent queries
- Implement query result caching
- Add proper indexes on frequently queried columns

#### Query Optimization
- Use efficient query patterns
- Implement pagination for large result sets
- Use projection to retrieve only needed fields
- Optimize JOIN operations
- Use database-specific optimizations

### Caching Strategies

#### Multi-layer Caching
- Implement Redis-based caching for domain descriptions
- Add in-memory LRU cache for hot data
- Use CDN caching for static assets
- Implement cache warming strategies
- Add cache invalidation patterns

#### Cache Implementation
The platform should implement:
- CacheInvalidationService for maintaining data consistency
- CachingService with configurable TTL and eviction policies
- CacheInvalidationManager for handling cache updates
- DataSyncService for synchronizing cache with primary data

```mermaid
graph TD
A[Request] --> B{In Cache?}
B --> |Yes| C[Return Cached Data]
B --> |No| D[Fetch from Database]
D --> E[Process Data]
E --> F[Store in Cache]
F --> G[Return Data]
H[Data Update] --> I[Invalidate Cache]
I --> J[Update Cache]
```

**Diagram sources**
- [shared/src/services/CacheInvalidationService.ts](file://shared/src/services/CacheInvalidationService.ts#L1-L50)
- [shared/src/services/CachingService.ts](file://shared/src/services/CachingService.ts#L1-L50)

### Build and Deployment Optimizations

#### Docker Image Optimization
- Use multi-stage builds for all services
- Implement layer caching strategies
- Remove development dependencies from production images
- Use Alpine base images where possible
- Optimize node_modules with pruning

#### Build Performance
- Configure TypeScript incremental builds
- Implement parallel builds for services
- Add build caching with Turborepo
- Optimize bundle sizes
- Implement code splitting for admin panel

### Algorithm and Processing Optimizations

#### Efficient Data Processing
- Use streaming for large data processing
- Implement batch processing for bulk operations
- Optimize memory usage in data transformations
- Use efficient data structures
- Minimize data copying

#### Ranking Algorithm Optimization
- Cache ranking calculations when possible
- Use incremental updates for ranking changes
- Implement efficient sorting algorithms
- Optimize database queries for ranking data
- Use appropriate data types for calculations

### Monitoring and Performance Testing

#### Performance Metrics
- Implement Prometheus metrics for all services
- Add custom business metrics
- Implement distributed tracing
- Add performance monitoring
- Implement SLA tracking

#### Load Testing
- Test system under expected load
- Identify performance bottlenecks
- Validate scalability
- Measure response times
- Monitor resource utilization

**Section sources**
- [TODO.md](file://TODO.md#L300-L415)
- [shared/src/services/CacheInvalidationService.ts](file://shared/src/services/CacheInvalidationService.ts#L1-L50)
- [shared/src/services/CachingService.ts](file://shared/src/services/CachingService.ts#L1-L50)

## Pull Request Process

The pull request process for the Domainr platform ensures code quality, proper review, and smooth integration of changes.

### PR Creation

When creating a pull request:
1. Base the PR on the `develop` branch
2. Include a clear title following conventional commit format
3. Provide a detailed description of changes
4. Reference related issues or TODO items
5. Include screenshots for UI changes
6. Link to relevant documentation updates

### PR Requirements

All pull requests must meet the following requirements:
- Pass all automated tests
- Meet code quality standards
- Include appropriate tests for new functionality
- Update documentation as needed
- Address all TODO items related to the change
- Follow coding style guidelines

### PR Review Process

The review process includes:
1. Automated checks (CI/CD pipeline)
2. Code review by at least one team member
3. Verification of test coverage
4. Validation of performance impact
5. Security review for sensitive changes
6. Final approval and merge

### PR Templates

The project should implement PR templates to ensure consistency:
- Description of changes
- Related issues or TODO items
- Testing instructions
- Screenshots (for UI changes)
- Performance considerations
- Documentation updates

### Merge Strategy

Approved PRs are merged using:
- Squash and merge for feature branches
- Regular merge for small fixes
- Rebase and merge when appropriate
- Merge to `develop` branch for integration

Hotfixes follow a separate process:
1. Create hotfix branch from `main`
2. Implement and test the fix
3. Create PR to `main`
4. After merge, cherry-pick to `develop`

**Section sources**
- [README.md](file://README.md#L650-L735)
- [TODO.md](file://TODO.md#L1-L415)

## Code Review Standards

Code reviews are a critical part of maintaining code quality in the Domainr platform. Reviewers should evaluate changes against these standards.

### Technical Quality

Reviewers should assess:
- Correctness of implementation
- Efficiency of algorithms and data structures
- Proper error handling and edge cases
- Appropriate use of types and interfaces
- Memory and resource management
- Performance implications

### Code Style and Readability

Code should be:
- Consistent with project style guidelines
- Well-organized and structured
- Properly commented and documented
- Easy to understand and maintain
- Free of code smells and anti-patterns

### Testing and Reliability

Reviewers should verify:
- Adequate test coverage
- Proper test structure and organization
- Realistic test cases and data
- Appropriate use of mocks and stubs
- Performance and load testing when applicable

### Security Considerations

Security aspects to review:
- Input validation and sanitization
- Authentication and authorization
- Data protection and privacy
- Error handling that doesn't leak information
- Secure configuration
- Vulnerability to common attacks

### Architecture and Design

Reviewers should evaluate:
- Alignment with overall architecture
- Proper separation of concerns
- Appropriate use of design patterns
- Maintainability and extensibility
- Impact on system performance
- Backward compatibility

### Specific Review Checklists

#### For Backend Changes
- [ ] Proper error handling and logging
- [ ] Database query optimization
- [ ] API contract consistency
- [ ] Input validation
- [ ] Rate limiting and security
- [ ] Performance implications

#### For Frontend Changes
- [ ] Responsive design
- [ ] Accessibility
- [ ] Browser compatibility
- [ ] State management
- [ ] Performance optimization
- [ ] Error boundaries

#### For Infrastructure Changes
- [ ] Security implications
- [ ] Resource requirements
- [ ] Monitoring and alerting
- [ ] Backup and recovery
- [ ] Scalability
- [ ] Cost implications

**Section sources**
- [README.md](file://README.md#L650-L735)
- [TODO.md](file://TODO.md#L1-L415)

## Monorepo Structure

The Domainr platform uses a monorepo structure to manage multiple services and shared components in a single repository.

### Workspace Organization

The monorepo is organized with pnpm workspaces defined in pnpm-workspace.yaml:
- services/ - Individual microservices
- shared/ - Shared utilities and models
- database/ - Database initialization scripts
- scripts/ - Utility scripts
- _config/ - Configuration files

```mermaid
graph TD
A[Root] --> B[services]
A --> C[shared]
A --> D[database]
A --> E[scripts]
A --> F[_config]
B --> G[admin]
B --> H[domain-seeder]
B --> I[web-app]
B --> J[worker]
C --> K[database]
C --> L[models]
C --> M[utils]
C --> N[middleware]
C --> O[queue]
```

**Diagram sources**
- [pnpm-workspace.yaml](file://pnpm-workspace.yaml#L1-L20)
- [package.json](file://package.json#L1-L50)

### Shared Code Management

The shared workspace contains code used across multiple services:
- **database/** - Database clients and managers for ScyllaDB, MariaDB, Redis, Manticore
- **models/** - Data models and interfaces (DomainAnalysis, DomainDescription, etc.)
- **utils/** - Common utilities (Logger, Config, Validators, HttpClient)
- **middleware/** - Express middleware functions (error handling, rate limiting, security)
- **queue/** - Job queue management with Redis-SMQ

Shared code follows strict versioning and compatibility rules:
- Breaking changes require coordination across services
- Semantic versioning for shared package
- Backward compatibility maintained when possible
- Clear documentation for public APIs

### Dependency Management

The monorepo uses pnpm for efficient dependency management:
- Shared dependencies installed once at root
- Workspace dependencies linked locally
- Version consistency across services
- Selective dependency installation

Key commands:
```bash
# Install all dependencies
pnpm install

# Add dependency to specific workspace
pnpm --filter shared add package-name

# Run command across all workspaces
pnpm -r run build

# Run command in specific workspace
pnpm --filter domain-ranking-worker run dev
```

### Build and Test Coordination

The monorepo enables coordinated builds and tests:
- Parallel execution of builds and tests
- Dependency-aware task running
- Centralized configuration
- Consistent tooling across services

Root-level scripts in package.json coordinate operations:
- pnpm dev - Start all services in development mode
- pnpm build - Build all services
- pnpm test - Test all services
- pnpm lint - Lint all code

**Section sources**
- [pnpm-workspace.yaml](file://pnpm-workspace.yaml#L1-L20)
- [package.json](file://package.json#L1-L100)
- [shared/src/index.ts](file://shared/src/index.ts#L1-L50)

## Microservices Architecture

The Domainr platform follows a microservices architecture with three main services and shared components.

### Service Overview

#### Web Application Service
- **Purpose**: User interface and API gateway
- **Technology**: Next.js, React, TypeScript
- **Responsibilities**:
  - Serve web interface
  - Handle user authentication
  - Provide API endpoints
  - Display analytics and reports
  - Admin interface for system management

#### Worker Service
- **Purpose**: Data processing and analysis
- **Technology**: Node.js, TypeScript, Express
- **Responsibilities**:
  - Web crawling and data collection
  - Domain analysis and scoring
  - Ranking calculations
  - Job scheduling
  - Queue management

#### Domain Seeder Service
- **Purpose**: Domain discovery and seeding
- **Technology**: Node.js, TypeScript, Express
- **Responsibilities**:
  - Discover new domains
  - Seed domain data
  - Manage discovery strategies
  - Handle rate limiting
  - Validate domain data

```mermaid
graph TD
A[Client] --> B[Web Application]
B --> C[Worker Service]
B --> D[Domain Seeder]
C --> E[ScyllaDB]
C --> F[MariaDB]
C --> G[Redis]
C --> H[Manticore]
D --> E
D --> F
D --> G
D --> H
B --> E
B --> F
B --> G
B --> H
```

**Diagram sources**
- [README.md](file://README.md#L150-L200)
- [docker-compose.yml](file://docker-compose.yml#L1-L100)

### Communication Patterns

Services communicate through:
- **HTTP/REST APIs**: Synchronous communication
- **Redis-SMQ**: Asynchronous job queue
- **Shared databases**: Data sharing and synchronization
- **Event-driven architecture**: Real-time updates

### Data Management

Each service has access to multiple databases:
- **ScyllaDB**: Main data storage for domain analysis and rankings
- **MariaDB**: Relational data (categories, backlinks, whois)
- **Manticore Search**: Full-text search and faceted filtering
- **Redis**: Caching and session storage

Data consistency is maintained through:
- Synchronization services
- Cache invalidation patterns
- Transaction management
- Event sourcing

### Service Isolation

Services are isolated through:
- Separate codebases within monorepo
- Independent deployment
- Dedicated configuration
- Service-specific monitoring
- Isolated error handling

### Scaling and Deployment

Services can be scaled independently:
- Web application: Scale based on user traffic
- Worker service: Scale based on processing load
- Domain seeder: Scale based on discovery requirements

Deployment is managed through Docker Compose and Kubernetes:
- docker-compose.yml: Production deployment
- docker-compose.dev.yml: Development environment
- k8s/: Kubernetes configurations

**Section sources**
- [README.md](file://README.md#L150-L200)
- [docker-compose.yml](file://docker-compose.yml#L1-L100)
- [services/worker/README.md](file://services/worker/README.md#L1-L50)

## Development Environment Setup

Setting up a development environment for the Domainr platform requires several steps to ensure all services can run locally.

### Prerequisites

- Node.js 18+
- pnpm 8+
- Docker 20.10+
- Docker Compose 2.0+
- OpenSSL (for SSL certificate generation)
- At least 8GB RAM and 4 CPU cores
- 50GB+ storage space

### Initial Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd domainr
   ```

2. **Install dependencies**
   ```bash
   pnpm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Generate SSL certificates**
   ```bash
   just ssl-create
   ```

### Development Commands

The project uses "just" as a command runner for common tasks:

```bash
# Full setup
just setup

# Deploy all services
just deploy

# Check deployment status
just deploy-status

# View logs
just logs

# Run health checks
just health

# Create backup
just backup
```

Alternative script-based commands:
```bash
# Deploy using scripts
./scripts/deploy.sh

# Setup environment
./scripts/setup-environment.sh

# Validate Docker configuration
./scripts/validate-docker-config.sh
```

### Service-Specific Development

Each service can be developed independently:

```bash
# Web Application
cd services/web-app
pnpm dev

# Worker Service
cd services/worker
pnpm dev

# Domain Seeder Service
cd services/domain-seeder
pnpm dev
```

### Database Initialization

Initialize databases with sample data:

```bash
# Initialize all databases
just db-init

# Or run individual steps
just db-migrate
just db-sample-data
```

Manual initialization:
```bash
# Initialize ScyllaDB
docker exec -it scylla cqlsh -f /docker-entrypoint-initdb.d/init.cql

# Initialize MariaDB
docker exec -it mariadb mysql -u root -p < /docker-entrypoint-initdb.d/init.sql

# Initialize Manticore
docker exec -it manticore mysql -h127.0.0.1 -P9306 < /docker-entrypoint-initdb.d/init.sql
```

### Environment Configuration

Key configuration options in `.env`:

```bash
# Required - Set secure passwords
MARIA_PASSWORD=your_secure_password_here
JWT_SECRET=your_jwt_secret_here

# Optional - Customize as needed
NODE_ENV=development
LOG_LEVEL=debug
CORS_ORIGIN=http://localhost:3000
```

**Section sources**
- [README.md](file://README.md#L50-L150)
- [docker-compose.dev.yml](file://docker-compose.dev.yml#L1-L50)
- [scripts/setup-environment.sh](file://scripts/setup-environment.sh#L1-L50)

## Deployment Process

The Domainr platform supports multiple deployment strategies for different environments.

### Production Deployment

#### Automated Deployment with Just

```bash
# Full deployment with health checks
just deploy

# Check deployment status
just deploy-status

# View logs
just logs
```

#### Manual Deployment Steps

1. **Build images**
   ```bash
   docker-compose build
   ```

2. **Start databases**
   ```bash
   docker-compose up -d scylla mariadb redis manticore
   ```

3. **Wait for databases and run migrations**
   ```bash
   sleep 60
   just db-init
   ```

4. **Start application services**
   ```bash
   docker-compose up -d browserless worker web-app
   ```

5. **Start reverse proxy and monitoring**
   ```bash
   docker-compose up -d nginx prometheus grafana
   ```

### Configuration Management

#### Environment Variables

Copy `.env.example` to `.env` and configure:

```bash
# Required - Set secure passwords
MARIA_PASSWORD=your_secure_password_here
JWT_SECRET=your_jwt_secret_here

# Optional - Customize as needed
NODE_ENV=production
LOG_LEVEL=info
CORS_ORIGIN=https://yourdomain.com
```

#### SSL Certificates

For production, replace self-signed certificates:

```bash
# Place your certificates in nginx/ssl/
cp your-cert.pem nginx/ssl/cert.pem
cp your-key.pem nginx/ssl/key.pem
```

### Service URLs

After deployment, services are available at:
- **Web Application**: <http://localhost> or <https://localhost>
- **Prometheus**: <http://localhost:9090>
- **Grafana**: <http://localhost:3001> (admin/admin)
- **ScyllaDB API**: <http://localhost:10000>

### Scaling

#### Horizontal Scaling

```bash
# Scale specific services
docker-compose up -d --scale worker=3
docker-compose up -d --scale web-app=2
```

#### Resource Limits

Resource limits are configured in docker-compose.yml:

```yaml
deploy:
  resources:
    limits:
      memory: 2G
      cpus: "2.0"
    reservations:
      memory: 1G
      cpus: "1.0"
```

### Backup and Recovery

#### Automated Backups

```bash
# Create backup
just backup

# Backups are stored in ./backups/
```

#### Manual Backup

```bash
# Run backup script directly
./scripts/backup.sh
```

#### Backup Contents
- ScyllaDB snapshots
- MariaDB dumps
- Redis data
- Manticore indexes
- Configuration files

### Maintenance

#### Updates

```bash
# Pull latest changes
git pull

# Rebuild and deploy
just build-no-cache
just deploy
```

#### Cleanup

```bash
# Clean unused resources
just clean

# Full cleanup (removes all data)
just clean-all
```

#### Database Maintenance

```bash
# Check migration status
just db-status

# Run new migrations
just db-migrate
```

**Section sources**
- [README.md](file://README.md#L50-L150)
- [docker-compose.yml](file://docker-compose.yml#L1-L100)
- [scripts/deploy.sh](file://scripts/deploy.sh#L1-L50)

## Monitoring and Observability

The Domainr platform includes comprehensive monitoring and observability features to ensure system reliability and performance.

### Health Checks

All services include health check endpoints:
- Web App: `GET /health`
- Worker: `GET /health`
- Domain Seeder: `GET /health`

Health check implementation:
```bash
# Check all services
just health

# Individual service health
curl http://localhost/health
```

### Logging Infrastructure

#### Centralized Logging

- Use Pino for structured JSON logging
- Log files in `./logs/` directory
- Include correlation IDs for request tracing
- Implement log rotation
- Aggregate logs in production

#### Log Viewing

```bash
# View all logs
just logs

# View specific service logs
just logs web-app
just logs worker
```

### Metrics and Monitoring

#### Prometheus and Grafana

- **Prometheus**: <http://localhost:9090>
- **Grafana**: <http://localhost:3001> (admin/admin)

Monitoring configuration:
- Custom alert rules
- Pre-configured dashboards
- Service-specific metrics
- System resource monitoring

```mermaid
graph TD
A[Services] --> B[Prometheus]
B --> C[Grafana]
C --> D[Alerts]
C --> E[Dashboards]
D --> F[Notification]
E --> G[Visualization]
```

**Diagram sources**
- [monitoring/prometheus.yml](file://monitoring/prometheus.yml#L1-L50)
- [services/admin/monitoring/grafana-dashboard.json](file://services/admin/monitoring/grafana-dashboard.json#L1-L50)

### Performance Monitoring

#### Key Metrics

- Request latency and throughput
- Error rates
- Database query performance
- Memory and CPU usage
- Queue lengths
- Cache hit ratios

#### Custom Business Metrics

- Domain analysis completion rate
- Ranking calculation time
- Crawl success rate
- Data validation quality
- AI content generation quality

### Alerting System

#### Alert Configuration

- Define alert rules in alert_rules.yml
- Configure alert manager for notifications
- Set up escalation policies
- Implement alert deduplication
- Add alert annotations

#### Alert Categories

- Service health (down, slow response)
- Resource usage (high CPU, memory)
- Error rates (increasing errors)
- Performance degradation
- Data consistency issues

### Tracing and Debugging

#### Distributed Tracing

- Implement trace IDs across service boundaries
- Track request flow through microservices
- Measure latency at each service
- Identify performance bottlenecks
- Visualize request paths

#### Debug Endpoints

- `/api/errors/debug/[errorId]` - Detailed error information
- `/health` - Service health status
- `/metrics` - Prometheus metrics
- `/status` - System status overview

**Section sources**
- [monitoring/prometheus.yml](file://monitoring/prometheus.yml#L1-L50)
- [services/admin/monitoring/alert_rules.yml](file://services/admin/monitoring/alert_rules.yml#L1-L50)
- [services/admin/monitoring/grafana-dashboard.json](file://services/admin/monitoring/grafana-dashboard.json#L1-L50)

## Troubleshooting Common Issues

This section addresses common issues encountered when developing and operating the Domainr platform.

### Service Startup Issues

#### Services Not Starting

```bash
# Check logs for specific service
just logs [service-name]

# Check resource usage
docker stats

# Verify environment variables
cat .env

# Check Docker network
docker network ls
docker network inspect domainr_default
```

#### Database Connection Errors

```bash
# Restart databases
docker-compose restart scylla mariadb redis

# Check database logs
just logs scylla
just logs mariadb

# Verify connection settings
# Check .env file for correct hostnames and ports
```

### Health Check Failures

#### Common Causes

- Database not ready
- Incorrect environment variables
- Port conflicts
- Resource constraints
- Network configuration issues

#### Troubleshooting Steps

```bash
# Check service status
just ps

# Perform health checks
just deploy-health

# View detailed logs
just logs

# Verify database initialization
just db-status
```

### Performance Issues

#### High Resource Usage

```bash
# Check resource usage
just top

# Scale services
docker-compose up -d --scale worker=2

# Monitor database performance
just logs scylla
just logs mariadb
```

#### Slow Response Times

- Check database query performance
- Verify cache effectiveness
- Monitor network latency
- Review service dependencies
- Analyze request patterns

### Data and Configuration Issues

#### Missing Data

- Verify database initialization completed
- Check data import scripts
- Validate data sources
- Review ETL processes
- Confirm data consistency

#### Configuration Problems

- Verify .env file settings
- Check service-specific configuration
- Validate database connection strings
- Review API endpoint configurations
- Confirm SSL certificate setup

### Common Error Patterns

#### Memory Issues

- Implement streaming for large data processing
- Use efficient data structures
- Optimize memory usage in loops
- Implement proper cleanup
- Monitor memory growth

#### Rate Limiting

- Review rate limiting configuration
- Check API usage patterns
- Adjust rate limits as needed
- Implement retry logic
- Monitor rate limit headers

#### Authentication Problems

- Verify JWT secret configuration
- Check token expiration settings
- Validate user permissions
- Review authentication flow
- Test with different user roles

**Section sources**
- [README.md](file://README.md#L500-L735)
- [TODO.md](file://TODO.md#L1-L415)

## Best Practices

Adhering to best practices ensures the Domainr platform remains maintainable, scalable, and reliable.

### Code Organization

#### Directory Structure

Follow consistent directory structure:
```
service/
├── src/
│   ├── app/ - Application logic
│   ├── components/ - UI components
│   ├── lib/ - Library functions
│   ├── types/ - Type definitions
│   ├── utils/ - Utility functions
│   └── middleware/ - Express middleware
├── __tests__/ - Test files
├── scripts/ - Utility scripts
├── config/ - Configuration files
└── public/ - Static assets
```

#### File Naming

- Use kebab-case for file names
- Use PascalCase for React components
- Use camelCase for functions and variables
- Use UPPER_CASE for constants
- Be descriptive and consistent

### Testing Best Practices

#### Test Structure

- Organize tests by feature or component
- Use descriptive test names
- Group related tests with describe blocks
- Use beforeEach and afterEach for setup/teardown
- Isolate tests from each other

#### Test Data

- Use realistic test data
- Create data factories for complex objects
- Use test-specific data sets
- Clean up test data after tests
- Avoid relying on external data sources

### Performance Best Practices

#### Efficient Algorithms

- Choose appropriate data structures
- Optimize time and space complexity
- Use memoization for expensive calculations
- Implement lazy loading
- Batch operations when possible

#### Resource Management

- Release resources promptly
- Use connection pooling
- Implement proper cleanup
- Monitor resource usage
- Handle resource exhaustion gracefully

### Security Best Practices

#### Input Validation

- Validate all user input
- Sanitize data before processing
- Use parameterized queries
- Implement rate limiting
- Validate file uploads

#### Authentication and Authorization

- Use secure password hashing
- Implement proper session management
- Use HTTPS in production
- Validate tokens on each request
- Implement role-based access control

### Documentation Best Practices

#### Code Comments

- Comment complex logic
- Document public APIs
- Explain non-obvious decisions
- Keep comments up to date
- Avoid redundant comments

#### External Documentation

- Maintain README files for services
- Document API endpoints
- Create architecture diagrams
- Write user guides
- Keep documentation in sync with code

### Deployment Best Practices

#### Configuration Management

- Use environment variables for configuration
- Never commit secrets to version control
- Use configuration files for complex settings
- Validate configuration on startup
- Provide default values

#### Monitoring and Alerting

- Monitor key metrics
- Set up meaningful alerts
- Implement health checks
- Use structured logging
- Create dashboards for important metrics

**Section sources**
- [README.md](file://README.md#L1-L735)
- [TODO.md](file://TODO.md#L1-L415)
- [CRUSH.md](file://CRUSH.md#L1-L50)