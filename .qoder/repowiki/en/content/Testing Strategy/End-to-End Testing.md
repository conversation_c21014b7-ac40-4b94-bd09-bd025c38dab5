# End-to-End Testing

<cite>
**Referenced Files in This Document**   
- [auth.spec.ts](file://services/admin/tests/e2e/auth.spec.ts)
- [global-setup.ts](file://services/admin/tests/e2e/global-setup.ts)
- [global-teardown.ts](file://services/admin/tests/e2e/global-teardown.ts)
- [playwright.config.ts](file://services/admin/playwright.config.ts)
- [DatabaseManager.ts](file://services/admin/src/lib/database/DatabaseManager.ts)
- [scripts/deploy.sh](file://scripts/deploy.sh)
- [services/admin/src/app/api/services/restart/route.ts](file://services/admin/src/app/api/services/restart/route.ts)
- [services/web-app/src/__tests__/integration/service-communication.test.ts](file://services/web-app/src/__tests__/integration/service-communication.test.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Playwright E2E Testing Framework](#playwright-e2e-testing-framework)
3. [Global Setup and Teardown Procedures](#global-setup-and-teardown-procedures)
4. [Authentication Flow Testing](#authentication-flow-testing)
5. [Domain Search and Analysis Workflows](#domain-search-and-analysis-workflows)
6. [UI and API Validation Strategies](#ui-and-api-validation-strategies)
7. [Handling Asynchronous Operations](#handling-asynchronous-operations)
8. [Data Consistency Across Services](#data-consistency-across-services)
9. [Testing Distributed Systems Challenges](#testing-distributed-systems-challenges)
10. [Conclusion](#conclusion)

## Introduction
The domainr application employs a comprehensive end-to-end (E2E) testing strategy to ensure reliability across its distributed microservices architecture. This documentation details how Playwright is leveraged to simulate real user workflows, validate both frontend behavior and backend API responses, and maintain data consistency across services such as authentication, domain search, analysis requests, and result visualization. The test suite addresses the complexities of testing in distributed environments, including service synchronization, network latency simulation, and infrastructure resilience.

**Section sources**
- [auth.spec.ts](file://services/admin/tests/e2e/auth.spec.ts)
- [playwright.config.ts](file://services/admin/playwright.config.ts)

## Playwright E2E Testing Framework
Playwright serves as the primary tool for executing end-to-end tests in the domainr application, enabling cross-browser automation across Chromium, Firefox, and WebKit. The framework supports testing on both desktop and mobile viewports, ensuring responsive design compliance. Configuration is managed through `playwright.config.ts`, which defines test directories, browser projects, reporting mechanisms, and integration with the development server.

The test runner executes in parallel by default, optimizing execution time while maintaining isolation between test runs. Reports are generated in multiple formats including HTML, JSON, and JUnit XML, facilitating integration with CI/CD pipelines and monitoring tools. Traces, screenshots, and videos are automatically captured on failure for debugging purposes.

```mermaid
graph TD
A[Playwright Test Runner] --> B[Chromium]
A --> C[Firefox]
A --> D[WebKit]
A --> E[Mobile Chrome]
A --> F[Mobile Safari]
G[HTML Report] <-- A
H[JSON Output] <-- A
I[JUnit XML] <-- A
J[Trace Viewer] <-- A
K[Screenshots] <-- A
L[Videos] <-- A
```

**Diagram sources**
- [playwright.config.ts](file://services/admin/playwright.config.ts)

**Section sources**
- [playwright.config.ts](file://services/admin/playwright.config.ts)

## Global Setup and Teardown Procedures
The E2E test suite utilizes global setup and teardown hooks to prepare and clean the test environment. The `global-setup.ts` script initializes the test context by launching a browser instance, navigating to the application base URL, and verifying server readiness. It then authenticates using predefined credentials and persists the session state to `auth-state.json`, enabling authenticated testing without repeated login steps.

During setup, the system waits for the server to respond and ensures all required UI elements are present before proceeding. This prevents race conditions between test execution and service startup. Authentication state is stored to streamline subsequent test runs that require authorized access.

Conversely, `global-teardown.ts` performs cleanup operations after test completion. It removes the persisted authentication state file and preserves test artifacts such as logs and results while ensuring no residual data affects future test runs. This approach maintains test isolation and prevents state leakage between executions.

```mermaid
sequenceDiagram
participant Setup as globalSetup
participant Browser as Chromium
participant Server as Application Server
participant Auth as Auth System
Setup->>Browser : Launch browser
Setup->>Server : Navigate to baseURL
Server-->>Setup : Page loaded
Setup->>Auth : Perform login
Auth-->>Setup : Authentication successful
Setup->>Setup : Save storage state
Setup->>Browser : Close browser
```

**Diagram sources**
- [global-setup.ts](file://services/admin/tests/e2e/global-setup.ts)
- [global-teardown.ts](file://services/admin/tests/e2e/global-teardown.ts)

**Section sources**
- [global-setup.ts](file://services/admin/tests/e2e/global-setup.ts)
- [global-teardown.ts](file://services/admin/tests/e2e/global-teardown.ts)

## Authentication Flow Testing
The `auth.spec.ts` file contains comprehensive test cases for the authentication workflow, validating both positive and negative scenarios. Tests verify form rendering, input validation, credential submission, session management, and security features such as account lockout after multiple failed attempts.

Key test cases include:
- Form visibility and required field validation
- Successful login with valid credentials
- Error handling for invalid credentials
- Account lockout mechanism after five failed attempts
- Password visibility toggle functionality
- Keyboard navigation and accessibility compliance
- Loading state display during authentication requests

Each test simulates real user interactions through Playwright's API, filling input fields, clicking buttons, and asserting expected outcomes. For example, the login process involves locating form elements by role or label, entering credentials, submitting the form, and verifying redirection to the dashboard with appropriate content.

The test suite also validates session expiration by manually clearing cookies and attempting to access protected routes, confirming proper redirection to the login page with an appropriate error message.

```mermaid
sequenceDiagram
participant User as Test User
participant Page as Web Page
participant API as Auth API
User->>Page : Navigate to /login
Page->>User : Display login form
User->>Page : Fill username and password
User->>Page : Click Sign In
Page->>API : POST /api/auth/login
API-->>Page : 200 OK with session
Page->>User : Redirect to /dashboard
User->>Page : Verify dashboard content
```

**Diagram sources**
- [auth.spec.ts](file://services/admin/tests/e2e/auth.spec.ts)

**Section sources**
- [auth.spec.ts](file://services/admin/tests/e2e/auth.spec.ts)

## Domain Search and Analysis Workflows
E2E tests validate complete user workflows for domain search and analysis, ensuring seamless interaction between UI components and backend services. These workflows begin with user authentication, followed by navigation to the domains section, execution of searches, filtering by category, and inspection of detailed domain analysis results.

Tests confirm that search functionality returns accurate results based on user input, with proper handling of partial matches and case insensitivity. Filtering operations are validated to ensure correct application of category constraints and consistent data presentation. When users trigger domain crawls or export data, the system responds appropriately with success notifications and correct file generation.

The workflow includes verification of modal dialogs for domain details, ensuring that performance metrics, security analysis, and other relevant information are properly displayed. Bulk actions such as crawl triggering are tested to confirm correct selection handling and job creation.

```mermaid
flowchart TD
A[Login] --> B[Navigate to Domains]
B --> C[Search Domains]
C --> D[Filter by Category]
D --> E[View Domain Details]
E --> F[Trigger Crawl Job]
F --> G[Export Data]
G --> H[Verify Download]
```

**Diagram sources**
- [auth.spec.ts](file://services/admin/tests/e2e/auth.spec.ts)

**Section sources**
- [auth.spec.ts](file://services/admin/tests/e2e/auth.spec.ts)

## UI and API Validation Strategies
The E2E test suite combines UI element assertions with API response validation to ensure comprehensive coverage. While Playwright primarily interacts with the DOM, it can intercept network requests to validate backend behavior. For instance, during login testing, the suite intercepts the `/api/auth/login` request to simulate network delays and verify loading state presentation.

Assertions are made on both visible content and underlying states, such as button disabled states during loading, URL changes after navigation, and toast notifications for success or error conditions. Text content, attribute values, and element visibility are checked against expected outcomes using Playwright's built-in matchers.

For API validation, route interception allows inspection of request payloads and response handling without depending on external services. This enables testing of edge cases like slow responses, errors, and authentication failures in a controlled manner.

```mermaid
graph TB
A[User Action] --> B[UI Interaction]
B --> C[Network Request]
C --> D{Intercepted?}
D --> |Yes| E[Mock Response]
D --> |No| F[Real API]
E --> G[UI Update]
F --> G
G --> H[Assertion]
H --> I[Pass/Fail]
```

**Diagram sources**
- [auth.spec.ts](file://services/admin/tests/e2e/auth.spec.ts)

**Section sources**
- [auth.spec.ts](file://services/admin/tests/e2e/auth.spec.ts)

## Handling Asynchronous Operations
The domainr application relies heavily on asynchronous operations, particularly in domain crawling, data indexing, and real-time updates. E2E tests account for these async behaviors through Playwright's auto-waiting mechanisms and explicit timeouts.

Tests use methods like `waitForSelector`, `toHaveURL`, and `toHaveText` that automatically wait for elements to reach the expected state before proceeding. This eliminates the need for arbitrary `sleep` calls and makes tests more resilient to timing variations.

For operations with known delays, such as login with simulated network latency, tests intercept API calls to introduce controlled delays and verify intermediate UI states like loading indicators and disabled buttons. This ensures the application provides appropriate feedback during long-running operations.

```mermaid
sequenceDiagram
participant Test as Test Script
participant Page as Page
participant API as API
Test->>Page : Click Sign In
Page->>API : Request in progress
Page->>Page : Show loading state
API-->>Page : Delayed response
Page->>Page : Hide loading state
Page->>Test : Navigation complete
Test->>Test : Assertion passed
```

**Diagram sources**
- [auth.spec.ts](file://services/admin/tests/e2e/auth.spec.ts)

**Section sources**
- [auth.spec.ts](file://services/admin/tests/e2e/auth.spec.ts)

## Data Consistency Across Services
Ensuring data consistency across distributed services is a critical aspect of E2E testing. The domainr application integrates multiple databases (ScyllaDB, MariaDB, Redis, Manticore) and message queues, requiring coordinated state management.

Tests validate that operations in one service correctly propagate to others. For example, when a domain crawl is initiated, the system should update the job queue, store results in the appropriate databases, and refresh search indexes. The test suite verifies these cross-service updates by checking data availability and consistency after operations complete.

Database connections are managed through a centralized `DatabaseManager` that initializes all clients during service startup. E2E tests assume this infrastructure is properly configured and available, relying on deployment scripts to establish the correct environment before test execution.

```mermaid
erDiagram
SCYLLADB {
string domain_id PK
string name
timestamp created_at
}
MARIADB {
int job_id PK
string domain_id FK
string status
timestamp started_at
}
REDIS {
string cache_key PK
string data
timestamp expiry
}
MANTICORE {
int doc_id PK
string content
float rank
}
SCYLLADB ||--o{ MARIADB : "domain_id"
SCYLLADB ||--o{ REDIS : "domain_id"
MARIADB ||--o{ MANTICORE : "job_id"
```

**Diagram sources**
- [DatabaseManager.ts](file://services/admin/src/lib/database/DatabaseManager.ts)

**Section sources**
- [DatabaseManager.ts](file://services/admin/src/lib/database/DatabaseManager.ts)

## Testing Distributed Systems Challenges
Testing the domainr application presents several challenges inherent to distributed systems. Service startup synchronization is addressed through health checks in deployment scripts that wait for each component to become available before proceeding.

Network latency and flaky dependencies are simulated using Playwright's request interception capabilities, allowing tests to verify graceful degradation and error recovery. The deployment script includes explicit waits and health checks for critical services like databases, worker processes, and the reverse proxy.

Service discovery and distributed locking mechanisms are tested to ensure coordination across instances. Redis is used for both service registration and distributed locks, preventing race conditions during critical operations. Tests validate that services can locate each other and coordinate exclusive access to shared resources.

The architecture includes circuit breakers and retry mechanisms to handle transient failures, which are validated through chaos engineering tests that simulate service outages and network partitions.

```mermaid
graph TB
subgraph Infrastructure
A[Redis] --> B[Distributed Locks]
A --> C[Service Discovery]
D[ScyllaDB] --> E[Data Storage]
F[MariaDB] --> E
G[Manticore] --> H[Search Index]
end
subgraph Services
I[Web App] --> A
I --> D
I --> F
I --> G
J[Worker] --> D
J --> F
J --> G
K[Domain Seeder] --> D
K --> F
end
subgraph Testing
L[Health Checks] --> I
L --> J
L --> K
M[Network Simulation] --> I
N[Chaos Testing] --> J
end
```

**Diagram sources**
- [scripts/deploy.sh](file://scripts/deploy.sh)
- [services/admin/src/app/api/services/restart/route.ts](file://services/admin/src/app/api/services/restart/route.ts)
- [services/web-app/src/__tests__/integration/service-communication.test.ts](file://services/web-app/src/__tests__/integration/service-communication.test.ts)

**Section sources**
- [scripts/deploy.sh](file://scripts/deploy.sh)
- [services/admin/src/app/api/services/restart/route.ts](file://services/admin/src/app/api/services/restart/route.ts)
- [services/web-app/src/__tests__/integration/service-communication.test.ts](file://services/web-app/src/__tests__/integration/service-communication.test.ts)

## Conclusion
The end-to-end testing strategy for the domainr application provides comprehensive coverage of user workflows across its distributed architecture. By leveraging Playwright for browser automation, combined with robust setup/teardown procedures and targeted validation of both UI and API layers, the test suite ensures high reliability and consistency. The approach effectively addresses challenges in testing distributed systems through careful service coordination, asynchronous operation handling, and simulation of real-world conditions such as network latency and infrastructure failures.