# Unit Testing

<cite>
**Referenced Files in This Document**   
- [vitest.config.ts](file://vitest.config.ts)
- [services/admin/vitest.config.ts](file://services/admin/vitest.config.ts)
- [services/domain-seeder/vitest.config.ts](file://services/domain-seeder/vitest.config.ts)
- [services/web-app/vitest.config.ts](file://services/web-app/vitest.config.ts)
- [services/worker/vitest.config.ts](file://services/worker/vitest.config.ts)
- [shared/vitest.config.ts](file://shared/vitest.config.ts)
- [services/admin/tests/setup.ts](file://services/admin/tests/setup.ts)
- [services/admin/src/lib/auth/__tests__/auth.test.ts](file://services/admin/src/lib/auth/__tests__/auth.test.ts)
- [services/admin/src/lib/database/__tests__/DatabaseManager.test.ts](file://services/admin/src/lib/database/__tests__/DatabaseManager.test.ts)
- [services/web-app/src/__tests__/description-api.test.ts](file://services/web-app/src/__tests__/description-api.test.ts)
- [services/worker/src/__tests__/WorkerService.test.ts](file://services/worker/src/__tests__/WorkerService.test.ts)
- [shared/src/__tests__/DomainDescriptionValidator.test.ts](file://shared/src/__tests__/DomainDescriptionValidator.test.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Vitest Configuration Across Services](#vitest-configuration-across-services)
3. [Test Organization and Structure](#test-organization-and-structure)
4. [Test Setup and Teardown Patterns](#test-setup-and-teardown-patterns)
5. [Mocking Strategies](#mocking-strategies)
6. [Assertion and Coverage Practices](#assertion-and-coverage-practices)
7. [Unit Testing Core Components](#unit-testing-core-components)
8. [Shared Test Utilities and Mocks](#shared-test-utilities-and-mocks)
9. [Best Practices for Maintainable Unit Tests](#best-practices-for-maintainable-unit-tests)
10. [Common Issues and Solutions](#common-issues-and-solutions)

## Introduction
The domainr codebase employs Vitest as the primary unit testing framework across all services, ensuring consistent, reliable, and efficient test execution. This document details the testing architecture, configuration, and implementation patterns used throughout the system. Unit tests are designed to validate individual functions, classes, and modules in isolation using dependency injection, mocking, and controlled test environments. The framework supports both unit and integration testing, with a strong emphasis on test coverage, performance, and maintainability.

**Section sources**
- [vitest.config.ts](file://vitest.config.ts#L1-L36)

## Vitest Configuration Across Services
Each service in the domainr ecosystem maintains its own Vitest configuration tailored to its execution environment and testing needs. The root-level `vitest.config.ts` provides a base configuration that is extended by individual services. The configuration includes test file patterns, coverage reporting, timeout settings, and plugin integration for TypeScript path resolution.

Service-specific configurations vary based on environment requirements:
- **Admin Service**: Uses `jsdom` environment for frontend component testing with global test setup via `setupFiles`.
- **Domain Seeder, Web App, Worker, and Shared Libraries**: Use `node` environment for backend logic testing.
- All configurations integrate `vite-tsconfig-paths` to support path aliases (`@`, `@shared`), enabling cleaner import statements in tests.

Coverage is uniformly reported using the `v8` provider with multiple output formats (text, lcov, html, json), and thresholds are enforced in the admin service to maintain code quality standards.

```mermaid
graph TB
RootConfig["vitest.config.ts (root)"] --> AdminConfig["services/admin/vitest.config.ts"]
RootConfig --> DomainSeederConfig["services/domain-seeder/vitest.config.ts"]
RootConfig --> WebAppConfig["services/web-app/vitest.config.ts"]
RootConfig --> WorkerConfig["services/worker/vitest.config.ts"]
RootConfig --> SharedConfig["shared/vitest.config.ts"]
AdminConfig --> |jsdom environment| FrontendTesting
DomainSeederConfig --> |node environment| BackendTesting
WebAppConfig --> |node environment| BackendTesting
WorkerConfig --> |node environment| BackendTesting
SharedConfig --> |node environment| BackendTesting
style RootConfig fill:#4CAF50,stroke:#388E3C
style AdminConfig fill:#2196F3,stroke:#1976D2
style DomainSeederConfig fill:#2196F3,stroke:#1976D2
style WebAppConfig fill:#2196F3,stroke:#1976D2
style WorkerConfig fill:#2196F3,stroke:#1976D2
style SharedConfig fill:#2196F3,stroke:#1976D2
```

**Diagram sources**
- [vitest.config.ts](file://vitest.config.ts#L1-L36)
- [services/admin/vitest.config.ts](file://services/admin/vitest.config.ts#L1-L38)
- [services/domain-seeder/vitest.config.ts](file://services/domain-seeder/vitest.config.ts#L1-L27)
- [services/web-app/vitest.config.ts](file://services/web-app/vitest.config.ts#L1-L23)
- [services/worker/vitest.config.ts](file://services/worker/vitest.config.ts#L1-L22)
- [shared/vitest.config.ts](file://shared/vitest.config.ts#L1-L21)

**Section sources**
- [vitest.config.ts](file://vitest.config.ts#L1-L36)
- [services/admin/vitest.config.ts](file://services/admin/vitest.config.ts#L1-L38)
- [services/domain-seeder/vitest.config.ts](file://services/domain-seeder/vitest.config.ts#L1-L27)
- [services/web-app/vitest.config.ts](file://services/web-app/vitest.config.ts#L1-L23)
- [services/worker/vitest.config.ts](file://services/worker/vitest.config.ts#L1-L22)
- [shared/vitest.config.ts](file://shared/vitest.config.ts#L1-L21)

## Test Organization and Structure
Unit tests are organized using a consistent directory structure across services. Test files are colocated within `__tests__` directories adjacent to the source code they validate, following the pattern `**/__tests__/**/*.(test|spec).{ts,tsx,js}`. This structure ensures tests are discoverable and maintainable.

Key organizational patterns include:
- **Service-specific test suites**: Each service maintains its own test files under `src/**/__tests__`.
- **Integration and chaos testing**: Specialized test categories (e.g., `integration`, `chaos`, `performance`) are used to isolate different test types.
- **Global setup files**: Each service defines a `setup.ts` file to initialize test environments, mocks, and cleanup routines.

This organization enables parallel test execution and clear separation of concerns between unit, integration, and end-to-end tests.

**Section sources**
- [vitest.config.ts](file://vitest.config.ts#L6-L10)
- [services/admin/vitest.config.ts](file://services/admin/vitest.config.ts#L6-L10)
- [services/web-app/vitest.config.ts](file://services/web-app/vitest.config.ts#L7-L11)

## Test Setup and Teardown Patterns
Test lifecycle management is implemented through standardized setup and teardown procedures. Each service uses a `setup.ts` file to configure global test state, initialize mocks, and register cleanup hooks. The admin service's `tests/setup.ts` demonstrates a comprehensive setup pattern using Vitest's `beforeAll`, `afterEach`, and `afterAll` hooks.

Key setup responsibilities include:
- Initializing MSW (Mock Service Worker) for API mocking
- Mocking Next.js router and headers for frontend components
- Setting environment variables for database and service connections
- Mocking browser APIs (WebSocket, ResizeObserver, IntersectionObserver)
- Cleaning up DOM state after each test

These patterns ensure test isolation and prevent side effects between test runs.

```mermaid
flowchart TD
Start([Test Execution]) --> BeforeAll["beforeAll: Start MSW server"]
BeforeAll --> MockEnv["Mock Environment Variables"]
MockEnv --> MockAPIs["Mock Browser APIs (WebSocket, ResizeObserver)"]
MockAPIs --> MockRouter["Mock Next.js Router and Headers"]
MockRouter --> RunTests["Run Test Suite"]
RunTests --> AfterEach["afterEach: Cleanup DOM and Reset Handlers"]
AfterEach --> NextTest["Next Test"]
NextTest --> RunTests
RunTests --> AfterAll["afterAll: Close MSW server"]
AfterAll --> End([Test Complete])
```

**Diagram sources**
- [services/admin/tests/setup.ts](file://services/admin/tests/setup.ts#L1-L101)

**Section sources**
- [services/admin/tests/setup.ts](file://services/admin/tests/setup.ts#L1-L101)

## Mocking Strategies
The codebase employs multiple mocking strategies to isolate units under test:
- **MSW (Mock Service Worker)**: Used in the admin service to intercept and mock HTTP requests at the network level.
- **Vitest `vi.mock()`**: Used to replace module imports, such as Next.js navigation and headers.
- **Manual mocks**: Browser APIs like `WebSocket`, `EventSource`, `ResizeObserver`, and `IntersectionObserver` are mocked using `vi.fn()`.
- **Environment variable mocking**: Critical service URLs and secrets are overridden to point to test instances.

These strategies enable realistic simulation of external dependencies while maintaining test speed and reliability.

**Section sources**
- [services/admin/tests/setup.ts](file://services/admin/tests/setup.ts#L1-L101)

## Assertion and Coverage Practices
Unit tests use standard assertion patterns with Jest-compatible matchers provided by Vitest. Coverage is enforced using the `v8` coverage provider with multiple reporting formats. The admin service enforces minimum coverage thresholds (80% across branches, functions, lines, and statements) to ensure code quality.

Test timeouts are standardized at 30 seconds across most services, with the shared library using a shorter 10-second timeout for faster feedback. Coverage reports are generated in text, JSON, and HTML formats for integration with CI/CD pipelines and code quality tools.

**Section sources**
- [vitest.config.ts](file://vitest.config.ts#L18-L34)
- [services/admin/vitest.config.ts](file://services/admin/vitest.config.ts#L12-L30)

## Unit Testing Core Components
Unit tests validate critical components across the codebase:
- **Authentication Service**: Tests in `services/admin/src/lib/auth/__tests__` validate session management, user authentication, and security policies.
- **Database Manager**: Tests in `services/admin/src/lib/database/__tests__` verify connection handling, query execution, and error recovery.
- **Domain Validation**: Shared validation logic in `shared/src/__tests__/DomainDescriptionValidator.test.ts` ensures domain descriptions meet quality and formatting standards.
- **Worker Service**: The `WorkerService.test.ts` file validates job processing, error handling, and pipeline coordination.

These tests use dependency injection and mock implementations to isolate functionality and ensure fast, deterministic execution.

```mermaid
classDiagram
class AuthService {
+authenticate(credentials) Promise~AuthResult~
+createSession(user) Promise~Session~
+validateToken(token) boolean
-hashPassword(password) string
}
class DatabaseManager {
+connect() Promise~Connection~
+query(sql, params) Promise~ResultSet~
+transaction(callback) Promise~any~
-getConnectionPool() Pool
}
class DomainValidator {
+validate(description) ValidationResult
+checkContentQuality(text) QualityScore
+validateStructure(data) boolean
}
class WorkerService {
+processJob(job) Promise~JobResult~
+handleError(error) RecoveryAction
+emitMetrics() void
}
AuthService --> DatabaseManager : "uses for user lookup"
DomainValidator --> ContentValidator : "delegates quality check"
WorkerService --> DatabaseManager : "persists job state"
WorkerService --> DomainValidator : "validates output"
```

**Diagram sources**
- [services/admin/src/lib/auth/__tests__/auth.test.ts](file://services/admin/src/lib/auth/__tests__/auth.test.ts)
- [services/admin/src/lib/database/__tests__/DatabaseManager.test.ts](file://services/admin/src/lib/database/__tests__/DatabaseManager.test.ts)
- [shared/src/__tests__/DomainDescriptionValidator.test.ts](file://shared/src/__tests__/DomainDescriptionValidator.test.ts)
- [services/worker/src/__tests__/WorkerService.test.ts](file://services/worker/src/__tests__/WorkerService.test.ts)

**Section sources**
- [services/admin/src/lib/auth/__tests__/auth.test.ts](file://services/admin/src/lib/auth/__tests__/auth.test.ts)
- [services/admin/src/lib/database/__tests__/DatabaseManager.test.ts](file://services/admin/src/lib/database/__tests__/DatabaseManager.test.ts)
- [shared/src/__tests__/DomainDescriptionValidator.test.ts](file://shared/src/__tests__/DomainDescriptionValidator.test.ts)
- [services/worker/src/__tests__/WorkerService.test.ts](file://services/worker/src/__tests__/WorkerService.test.ts)

## Shared Test Utilities and Mocks
The `shared` package contains reusable test utilities and validation logic used across services. The `DomainDescriptionValidator` tests ensure consistent domain description quality throughout the system. These shared tests are executed independently in the shared package and also run as part of dependent services' test suites.

Cross-service test utilities include:
- HTTP client mocks
- Database connection stubs
- Validation schema definitions
- Error handling patterns

This sharing reduces duplication and ensures consistent behavior across the distributed system.

**Section sources**
- [shared/src/__tests__/DomainDescriptionValidator.test.ts](file://shared/src/__tests__/DomainDescriptionValidator.test.ts)

## Best Practices for Maintainable Unit Tests
The codebase follows several best practices for writing maintainable unit tests:
- **Test isolation**: Each test runs in a clean environment with mocked dependencies.
- **Descriptive test names**: Tests use clear, behavior-driven naming conventions.
- **Setup/teardown automation**: Common initialization and cleanup logic is centralized.
- **Coverage enforcement**: Minimum thresholds ensure adequate test coverage.
- **Environment consistency**: Test configurations mirror production as closely as possible while maintaining isolation.
- **Parallel execution**: Tests are designed to run independently and can be executed in parallel.

These practices ensure tests remain reliable, readable, and maintainable as the codebase evolves.

**Section sources**
- [vitest.config.ts](file://vitest.config.ts#L1-L36)
- [services/admin/tests/setup.ts](file://services/admin/tests/setup.ts#L1-L101)

## Common Issues and Solutions
The testing infrastructure addresses common issues in unit testing:
- **Flaky tests**: Solved through deterministic mocks, proper cleanup, and increased timeout values.
- **Test data management**: Environment variables are mocked to point to test databases, and MSW ensures consistent API responses.
- **State isolation**: The `afterEach` cleanup ensures no state leakage between tests.
- **Browser API dependencies**: Critical browser APIs are mocked to enable testing in node environments.
- **Slow tests**: The `maxConcurrency: 4` setting in the root config enables parallel test execution to reduce total runtime.

These solutions ensure a stable, fast, and reliable testing experience across all services.

**Section sources**
- [vitest.config.ts](file://vitest.config.ts#L33-L35)
- [services/admin/tests/setup.ts](file://services/admin/tests/setup.ts#L1-L101)