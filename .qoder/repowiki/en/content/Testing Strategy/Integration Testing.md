# Integration Testing

<cite>
**Referenced Files in This Document**   
- [handlers.ts](file://services/admin/tests/mocks/handlers.ts)
- [WorkerJobQueue.ts](file://services/worker/src/queue/WorkerJobQueue.ts)
- [SharedServiceIntegration.ts](file://services/domain-seeder/src/services/SharedServiceIntegration.ts)
- [AutoRecoverySystem.ts](file://services/worker/src/errors/AutoRecoverySystem.ts)
- [JobQueue.ts](file://shared/src/queue/JobQueue.ts)
- [DatabaseManager.test.ts](file://shared/src/database/__tests__/DatabaseManager.test.ts)
- [CachingService.test.ts](file://shared/src/services/__tests__/CachingService.test.ts)
- [DomainDescriptionValidator.test.ts](file://shared/src/__tests__/DomainDescriptionValidator.test.ts)
- [api.test.ts](file://services/web-app/src/__tests__/integration/api.test.ts)
- [database.test.ts](file://services/web-app/src/__tests__/integration/database.test.ts)
- [service-communication.test.ts](file://services/web-app/src/__tests__/integration/service-communication.test.ts)
- [pipeline-integration.test.ts](file://services/worker/src/__tests__/integration/pipeline-integration.test.ts)
- [database-integration.test.ts](file://services/worker/src/__tests__/database/database-integration.test.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Integration Testing Strategy](#integration-testing-strategy)
3. [Web-App and Worker Service Integration](#web-app-and-worker-service-integration)
4. [Database Integration Testing](#database-integration-testing)
5. [Shared Library Validation Workflows](#shared-library-validation-workflows)
6. [Message Passing with Redis-SMQ](#message-passing-with-redis-smq)
7. [Cross-Service Communication Patterns](#cross-service-communication-patterns)
8. [Error Recovery and Transactional Boundaries](#error-recovery-and-transactional-boundaries)
9. [Test Data Seeding and Cleanup](#test-data-seeding-and-cleanup)
10. [Conclusion](#conclusion)

## Introduction
Integration testing in the domainr system ensures that components work together as expected across service boundaries. This includes verifying interactions between API endpoints, database clients, external services, and message queues. The testing framework validates data flow across layers, transactional integrity, error recovery mechanisms, and cross-service communication patterns. Integration tests use mock servers (MSW) to simulate external dependencies and isolate service behavior for reliable testing.

## Integration Testing Strategy
The domainr system employs a comprehensive integration testing strategy that verifies component interactions across the entire stack. Tests validate API endpoints, database operations, message queue processing, and external service integrations. The framework uses MSW (Mock Service Worker) to intercept HTTP requests and simulate external dependencies, allowing tests to run in isolation without relying on actual external services.

```mermaid
flowchart TD
A[Integration Test] --> B[API Request]
B --> C[MSW Interceptor]
C --> D{Request Type}
D --> |External| E[Mock Response]
D --> |Internal| F[Service Handler]
F --> G[Database Operation]
G --> H[Redis-SMQ Message]
H --> I[Worker Processing]
I --> J[Test Assertion]
```

**Diagram sources**
- [handlers.ts](file://services/admin/tests/mocks/handlers.ts)
- [api.test.ts](file://services/web-app/src/__tests__/integration/api.test.ts)

**Section sources**
- [handlers.ts](file://services/admin/tests/mocks/handlers.ts)
- [api.test.ts](file://services/web-app/src/__tests__/integration/api.test.ts)

## Web-App and Worker Service Integration
Integration tests verify the interaction between the web-app and worker services through API endpoints and message queues. The web-app service exposes REST APIs that trigger background processing in the worker service via Redis-SMQ message passing. Tests validate that API requests correctly enqueue jobs and that the worker service processes them as expected.

```mermaid
sequenceDiagram
participant Client
participant WebApp
participant RedisSMQ
participant Worker
Client->>WebApp : POST /api/domains/analyze
WebApp->>RedisSMQ : Publish job to domain : crawl queue
RedisSMQ->>Worker : Deliver job message
Worker->>Worker : Process domain analysis
Worker->>WebApp : Update status via API
WebApp->>Client : Return job ID and status
```

**Diagram sources**
- [WorkerJobQueue.ts](file://services/worker/src/queue/WorkerJobQueue.ts)
- [service-communication.test.ts](file://services/web-app/src/__tests__/integration/service-communication.test.ts)

**Section sources**
- [WorkerJobQueue.ts](file://services/worker/src/queue/WorkerJobQueue.ts)
- [service-communication.test.ts](file://services/web-app/src/__tests__/integration/service-communication.test.ts)

## Database Integration Testing
Database integration tests validate interactions with multiple database systems including Scylla, MariaDB, Manticore, and Redis. Tests verify schema compatibility, data consistency across databases, and proper handling of database operations. The worker service includes comprehensive database integration tests that validate complex data processing pipelines.

```mermaid
erDiagram
USER ||--o{ DOMAIN_ANALYSIS : "performs"
DOMAIN_ANALYSIS ||--o{ CRAWL_JOB : "has"
CRAWL_JOB ||--o{ RANKING_DATA : "produces"
USER {
string id PK
string username
string email
timestamp created_at
timestamp updated_at
}
DOMAIN_ANALYSIS {
string id PK
string domain UK
float global_rank
float category_rank
string category
float overall_score
string crawl_status
timestamp last_crawled
string seeder_status
}
CRAWL_JOB {
string id PK
string domain FK
string crawl_type
string priority
string status
int progress
timestamp scheduled_at
timestamp started_at
timestamp completed_at
string error_message
int retry_count
string requested_by
}
RANKING_DATA {
string id PK
string domain FK
float performance_score
float security_score
float seo_score
float technical_score
float backlinks_score
timestamp updated_at
}
```

**Diagram sources**
- [DatabaseManager.test.ts](file://shared/src/database/__tests__/DatabaseManager.test.ts)
- [database-integration.test.ts](file://services/worker/src/__tests__/database/database-integration.test.ts)

**Section sources**
- [DatabaseManager.test.ts](file://shared/src/database/__tests__/DatabaseManager.test.ts)
- [database-integration.test.ts](file://services/worker/src/__tests__/database/database-integration.test.ts)

## Shared Library Validation Workflows
Shared library validation workflows ensure consistent data validation and processing across services. The shared library contains validation utilities used by both web-app and worker services. Integration tests verify that validation rules are consistently applied and that data quality is maintained across service boundaries.

```mermaid
flowchart TD
A[Domain Data] --> B[Validation Pipeline]
B --> C{Valid?}
C --> |Yes| D[Process Data]
C --> |No| E[Error Recovery]
D --> F[Generate Description]
F --> G[Quality Check]
G --> H{Meets Quality?}
H --> |Yes| I[Store Result]
H --> |No| J[Retry with Adjustments]
E --> K[Apply Recovery Strategy]
K --> L{Recovered?}
L --> |Yes| D
L --> |No| M[Log Failure]
```

**Diagram sources**
- [DomainDescriptionValidator.test.ts](file://shared/src/__tests__/DomainDescriptionValidator.test.ts)
- [CachingService.test.ts](file://shared/src/services/__tests__/CachingService.test.ts)

**Section sources**
- [DomainDescriptionValidator.test.ts](file://shared/src/__tests__/DomainDescriptionValidator.test.ts)
- [CachingService.test.ts](file://shared/src/services/__tests__/CachingService.test.ts)

## Message Passing with Redis-SMQ
Redis-SMQ message passing enables reliable communication between services through message queues. Integration tests validate message publishing, consumption, retry mechanisms, and dead letter queue handling. The worker service uses Redis-SMQ for processing domain analysis jobs, crawl jobs, and ranking updates.

```mermaid
classDiagram
class WorkerJobQueue {
+initialize() Promise~void~
+shutdown() Promise~void~
+publishJob(queueName, jobData, options) Promise~string~
+createConsumer(queueName, handler, options) Promise~Consumer~
+pauseQueue(queueName) Promise~void~
+resumeQueue(queueName) Promise~void~
+purgeQueue(queueName) Promise~void~
}
class Message {
+setBody(body) void
+setQueue(queueName) void
+setPriority(priority) void
+setTTL(ttl) void
+setDelay(delay) void
}
class Producer {
+produce(message, callback) void
+run() Promise~void~
+shutdown() Promise~void~
}
class Consumer {
+consume(queueName, handler, options) void
+run() Promise~void~
+shutdown() Promise~void~
}
WorkerJobQueue --> Producer : "uses"
WorkerJobQueue --> Consumer : "creates"
WorkerJobQueue --> Message : "creates"
Producer --> Message : "sends"
Consumer --> Message : "receives"
```

**Diagram sources**
- [WorkerJobQueue.ts](file://services/worker/src/queue/WorkerJobQueue.ts)
- [JobQueue.ts](file://shared/src/queue/JobQueue.ts)

**Section sources**
- [WorkerJobQueue.ts](file://services/worker/src/queue/WorkerJobQueue.ts)
- [JobQueue.ts](file://shared/src/queue/JobQueue.ts)

## Cross-Service Communication Patterns
Cross-service communication patterns in domainr follow a producer-consumer model with API-based coordination. Services communicate through REST APIs for request-response interactions and message queues for asynchronous processing. Integration tests validate these communication patterns, ensuring data consistency and proper error handling.

```mermaid
sequenceDiagram
participant WebApp
participant Worker
participant DomainSeeder
participant Database
WebApp->>Worker : POST /api/jobs {domain : "example.com"}
Worker->>Database : INSERT crawl_job
Database-->>Worker : job_id
Worker->>Worker : Enqueue job in Redis-SMQ
Worker->>WebApp : 202 Accepted {jobId : "123"}
Worker->>DomainSeeder : GET /api/seeder/status
DomainSeeder-->>Worker : 200 OK {queueSize : 500}
Worker->>Worker : Process job
Worker->>Database : UPDATE crawl_job status=completed
Worker->>WebApp : POST /api/webhooks/job-completed
```

**Diagram sources**
- [SharedServiceIntegration.ts](file://services/domain-seeder/src/services/SharedServiceIntegration.ts)
- [pipeline-integration.test.ts](file://services/worker/src/__tests__/integration/pipeline-integration.test.ts)

**Section sources**
- [SharedServiceIntegration.ts](file://services/domain-seeder/src/services/SharedServiceIntegration.ts)
- [pipeline-integration.test.ts](file://services/worker/src/__tests__/integration/pipeline-integration.test.ts)

## Error Recovery and Transactional Boundaries
Error recovery mechanisms and transactional boundaries ensure system reliability during failures. Integration tests validate automatic recovery strategies, circuit breaker patterns, and graceful degradation. The worker service includes comprehensive error recovery systems that handle database connection failures, message processing errors, and service disruptions.

```mermaid
stateDiagram-v2
[*] --> NormalOperation
NormalOperation --> DegradedMode : "error rate > threshold"
DegradedMode --> NormalOperation : "health restored"
DegradedMode --> RecoveryMode : "critical failure"
RecoveryMode --> NormalOperation : "recovery successful"
RecoveryMode --> DegradedMode : "partial recovery"
RecoveryMode --> FailSafeMode : "recovery failed"
FailSafeMode --> NormalOperation : "manual intervention"
state RecoveryMode {
[*] --> ReconnectDatabase
ReconnectDatabase --> ClearCache : "success"
ClearCache --> ResetCircuitBreaker : "success"
ResetCircuitBreaker --> NormalOperation : "success"
ResetCircuitBreaker --> FailSafeMode : "failure"
}
```

**Diagram sources**
- [AutoRecoverySystem.ts](file://services/worker/src/errors/AutoRecoverySystem.ts)
- [database-integration.test.ts](file://services/worker/src/__tests__/database/database-integration.test.ts)

**Section sources**
- [AutoRecoverySystem.ts](file://services/worker/src/errors/AutoRecoverySystem.ts)
- [database-integration.test.ts](file://services/worker/src/__tests__/database/database-integration.test.ts)

## Test Data Seeding and Cleanup
Test data seeding and cleanup procedures ensure consistent test environments and prevent data pollution. Integration tests use dedicated test databases and implement setup/teardown routines to prepare and clean test data. The domain-seeder service provides utilities for seeding test domains and categories.

```mermaid
flowchart TD
A[Before Test] --> B[Seed Test Data]
B --> C[Run Integration Test]
C --> D[Verify Results]
D --> E[Cleanup Test Data]
E --> F[Test Complete]
B --> B1[Create Test Users]
B --> B2[Insert Domain Seeds]
B --> B3[Initialize Categories]
B --> B4[Setup Mock Services]
E --> E1[Delete Test Users]
E --> E2[Remove Domain Data]
E --> E3[Clear Cache]
E --> E4[Reset Mocks]
```

**Diagram sources**
- [database.test.ts](file://services/web-app/src/__tests__/integration/database.test.ts)
- [setup.ts](file://services/web-app/src/__tests__/setup.ts)

**Section sources**
- [database.test.ts](file://services/web-app/src/__tests__/integration/database.test.ts)
- [setup.ts](file://services/web-app/src/__tests__/setup.ts)

## Conclusion
Integration testing in the domainr system ensures reliable interactions between components through comprehensive validation of API endpoints, database operations, message queues, and external service integrations. The testing framework uses mock servers to isolate service behavior and validate error recovery mechanisms. Key aspects include web-app and worker service integration, database schema compatibility, Redis-SMQ message passing, and cross-service communication patterns. Test data seeding and cleanup procedures maintain consistent test environments, while error recovery systems ensure system reliability during failures.