# Environment Variables

<cite>
**Referenced Files in This Document**   
- [EnvironmentProfiles.ts](file://services/domain-seeder/src/config/EnvironmentProfiles.ts)
- [SeederConfig.ts](file://services/domain-seeder/src/config/SeederConfig.ts)
- [WorkerConfig.ts](file://services/worker/src/config/WorkerConfig.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Configuration Hierarchy and Profiles](#configuration-hierarchy-and-profiles)
3. [Core Environment Variables](#core-environment-variables)
4. [Database Configuration](#database-configuration)
5. [AI Provider Credentials](#ai-provider-credentials)
6. [Monitoring and Logging](#monitoring-and-logging)
7. [Security Settings](#security-settings)
8. [Source Configuration](#source-configuration)
9. [Deployment Examples](#deployment-examples)
10. [Validation and Error Handling](#validation-and-error-handling)
11. [Dynamic Configuration and Hot Reload](#dynamic-configuration-and-hot-reload)
12. [Troubleshooting Guide](#troubleshooting-guide)

## Introduction
The domainr platform employs a sophisticated environment variable management system to control configuration across multiple services and deployment environments. This document provides comprehensive coverage of the environment variable system, focusing on the domain-seeder service as the primary example of configuration management. The system supports hierarchical configuration profiles, validation rules, and dynamic reloading to ensure reliable operation across development, staging, and production environments.

**Section sources**
- [EnvironmentProfiles.ts](file://services/domain-seeder/src/config/EnvironmentProfiles.ts#L1-L50)
- [SeederConfig.ts](file://services/domain-seeder/src/config/SeederConfig.ts#L1-L50)

## Configuration Hierarchy and Profiles
The domainr platform implements a hierarchical configuration system using environment profiles that support inheritance and validation. The system is managed by the `EnvironmentProfileManager` class, which handles profile creation, loading, and application.

```mermaid
classDiagram
class EnvironmentProfileManager {
+initialize() Promise~void~
+getProfile(name) Promise~EnvironmentProfile | null~
+listProfiles() Promise~EnvironmentProfile[]~
+createProfile(profile) Promise~void~
+updateProfile(name, updates) Promise~void~
+deleteProfile(name) Promise~void~
+applyProfile(profileName, baseConfig) Promise~any~
+migrateConfig(config, fromVersion, toVersion) Promise~any~
+exportProfile(name) Promise~string~
+importProfile(profileData) Promise~void~
}
class EnvironmentProfile {
+name : string
+extends : string?
+description : string?
+overrides : Record~string, any~
+validation : ValidationRules
+metadata : Metadata
}
class ValidationRules {
+required : string[]
+forbidden : string[]
+warnings : string[]
}
class Metadata {
+createdAt : string
+updatedAt : string
+version : string
+author : string?
+tags : string[]
}
EnvironmentProfileManager --> EnvironmentProfile : "manages"
```

**Diagram sources**
- [EnvironmentProfiles.ts](file://services/domain-seeder/src/config/EnvironmentProfiles.ts#L46-L109)
- [EnvironmentProfiles.ts](file://services/domain-seeder/src/config/EnvironmentProfiles.ts#L137-L179)

The configuration system includes four default profiles:
- **development**: Optimized for local development with verbose logging and disabled security features
- **staging**: Pre-production environment with moderate resource limits and enabled security
- **production**: High-performance configuration with strict security requirements
- **test**: Minimal configuration for automated testing

Each profile can extend another profile, creating an inheritance chain. For example, the staging profile extends the development profile, and the production profile extends the staging profile. This allows for incremental configuration changes while maintaining consistency across environments.

The system also supports configuration migrations, allowing automatic updates when configuration schema versions change. Migration paths are defined for transitions between different configuration versions, ensuring backward compatibility.

**Section sources**
- [EnvironmentProfiles.ts](file://services/domain-seeder/src/config/EnvironmentProfiles.ts#L137-L179)
- [EnvironmentProfiles.ts](file://services/domain-seeder/src/config/EnvironmentProfiles.ts#L596-L639)

## Core Environment Variables
The domainr platform uses environment variables to configure core service behavior. These variables are processed by the `SeederConfig` class, which validates and normalizes configuration values.

```mermaid
classDiagram
class SeederConfig {
+getConfiguration() SeederConfiguration
+getEnvironmentConfig() EnvironmentConfig
+getDatabaseConfig() DatabaseConfig
+getSeederConfig() SeederConfig
+getSourcesConfig() SourcesConfig
+getAIConfig() AIConfig
+getMonitoringConfig() MonitoringConfig
+getSecurityConfig() SecurityConfig
+enableHotReload(options) void
+disableHotReload() void
}
class EnvironmentConfig {
+nodeEnv : 'development' | 'staging' | 'production' | 'test'
+serviceName : string
+serviceVersion : string
+port : number
+timezone : string
+gracefulShutdownTimeoutMs : number
}
SeederConfig --> EnvironmentConfig : "contains"
```

**Diagram sources**
- [SeederConfig.ts](file://services/domain-seeder/src/config/SeederConfig.ts#L200-L250)

### Environment Configuration
The following environment variables control the basic service environment:

- **NODE_ENV**: Sets the operational environment (development, staging, production, test)
- **SERVICE_NAME**: Service identifier used in logging and monitoring
- **SERVICE_VERSION**: Version identifier for the service
- **PORT**: HTTP port for the service to listen on
- **TIMEZONE**: Timezone for date/time operations
- **GRACEFUL_SHUTDOWN_TIMEOUT_MS**: Maximum time to wait for graceful shutdown

These variables are validated using Zod schemas to ensure correct types and acceptable values. The configuration system provides default values for all variables, ensuring the service can start even with minimal configuration.

**Section sources**
- [SeederConfig.ts](file://services/domain-seeder/src/config/SeederConfig.ts#L369-L383)

## Database Configuration
The domainr platform integrates with multiple database systems, each with its own configuration parameters. The database configuration is validated using strict Zod schemas to ensure connection reliability.

```mermaid
classDiagram
class DatabaseConfig {
+scylla : ScyllaConfig
+maria : MariaConfig
+manticore : ManticoreConfig
+redis : RedisConfig
}
class ScyllaConfig {
+contactPoints : string[]
+localDataCenter : string
+keyspace : string
+username : string?
+password : string?
+connectTimeout : number
+requestTimeout : number
}
class MariaConfig {
+host : string
+port : number
+user : string
+password : string
+database : string
+connectionLimit : number
+acquireTimeout : number
+timeout : number
}
class ManticoreConfig {
+host : string
+port : number
+maxConnections : number
+timeout : number
}
class RedisConfig {
+host : string
+port : number
+password : string?
+db : number
+maxRetriesPerRequest : number
+retryDelayOnFailover : number
+connectTimeout : number
+commandTimeout : number
}
DatabaseConfig --> ScyllaConfig
DatabaseConfig --> MariaConfig
DatabaseConfig --> ManticoreConfig
DatabaseConfig --> RedisConfig
```

**Diagram sources**
- [SeederConfig.ts](file://services/domain-seeder/src/config/SeederConfig.ts#L50-L100)

### Database Connection Variables
The following environment variables configure database connections:

- **SCYLLA_CONTACT_POINTS**: Comma-separated list of ScyllaDB contact points
- **SCYLLA_LOCAL_DC**: Local data center name for ScyllaDB
- **SCYLLA_KEYSPACE**: Keyspace name for ScyllaDB
- **SCYLLA_USERNAME**: Username for ScyllaDB authentication
- **SCYLLA_PASSWORD**: Password for ScyllaDB authentication
- **SCYLLA_CONNECT_TIMEOUT**: Connection timeout in milliseconds
- **SCYLLA_REQUEST_TIMEOUT**: Request timeout in milliseconds

- **MARIA_HOST**: MariaDB host address
- **MARIA_PORT**: MariaDB port number
- **MARIA_USER**: MariaDB username
- **MARIA_PASSWORD**: MariaDB password
- **MARIA_DATABASE**: Database name
- **MARIA_CONNECTION_LIMIT**: Maximum number of connections in pool
- **MARIA_ACQUIRE_TIMEOUT**: Timeout for acquiring connection from pool
- **MARIA_TIMEOUT**: Query timeout

- **MANTICORE_HOST**: Manticore Search host address
- **MANTICORE_PORT**: Manticore Search port number
- **MANTICORE_MAX_CONNECTIONS**: Maximum connections to Manticore
- **MANTICORE_TIMEOUT**: Query timeout

- **REDIS_HOST**: Redis server host
- **REDIS_PORT**: Redis server port
- **REDIS_PASSWORD**: Redis authentication password
- **REDIS_DB**: Redis database number
- **REDIS_MAX_RETRIES**: Maximum retries for failed requests
- **REDIS_RETRY_DELAY**: Delay between retry attempts
- **REDIS_CONNECT_TIMEOUT**: Connection timeout
- **REDIS_COMMAND_TIMEOUT**: Command execution timeout

All database configuration values are validated for type and range, with sensible defaults provided for optional parameters.

**Section sources**
- [SeederConfig.ts](file://services/domain-seeder/src/config/SeederConfig.ts#L100-L150)

## AI Provider Credentials
The domainr platform supports multiple AI providers for domain content generation and analysis. The AI configuration system allows for flexible provider selection and fallback strategies.

```mermaid
classDiagram
class AIConfig {
+providers : AIProviders
+proxy : AIProxyConfig
+fallbackEnabled : boolean
+maxRetries : number
+timeoutMs : number
}
class AIProviders {
+openai : AIProviderConfig
+claude : AIProviderConfig
+gemini : AIProviderConfig
+openrouter : AIProviderConfig
}
class AIProviderConfig {
+enabled : boolean
+apiKeys : string[]
+priority : number
+model : string
+maxTokens : number
+temperature : number
+rateLimit : number
}
class AIProxyConfig {
+enabled : boolean
+ips : string[]
+rotationIntervalMs : number
+healthCheckIntervalMs : number
}
AIConfig --> AIProviders
AIProviders --> AIProviderConfig
AIConfig --> AIProxyConfig
```

**Diagram sources**
- [SeederConfig.ts](file://services/domain-seeder/src/config/SeederConfig.ts#L150-L200)

### AI Provider Configuration
The following environment variables configure AI provider integration:

- **OPENAI_ENABLED**: Enable OpenAI integration
- **OPENAI_API_KEYS**: Comma-separated list of OpenAI API keys for load balancing
- **OPENAI_PRIORITY**: Priority for OpenAI in provider selection (lower = higher priority)
- **OPENAI_MODEL**: Model identifier for OpenAI API
- **OPENAI_MAX_TOKENS**: Maximum tokens for AI responses
- **OPENAI_TEMPERATURE**: Creativity level for AI responses
- **OPENAI_RATE_LIMIT**: Maximum requests per minute

- **CLAUDE_ENABLED**: Enable Anthropic Claude integration
- **CLAUDE_API_KEYS**: Comma-separated list of Claude API keys
- **CLAUDE_PRIORITY**: Priority for Claude in provider selection
- **CLAUDE_MODEL**: Model identifier for Claude API
- **CLAUDE_MAX_TOKENS**: Maximum tokens for AI responses
- **CLAUDE_TEMPERATURE**: Creativity level for AI responses
- **CLAUDE_RATE_LIMIT**: Maximum requests per minute

- **GEMINI_ENABLED**: Enable Google Gemini integration
- **GEMINI_API_KEYS**: Comma-separated list of Gemini API keys
- **GEMINI_PRIORITY**: Priority for Gemini in provider selection
- **GEMINI_MODEL**: Model identifier for Gemini API
- **GEMINI_MAX_TOKENS**: Maximum tokens for AI responses
- **GEMINI_TEMPERATURE**: Creativity level for AI responses
- **GEMINI_RATE_LIMIT**: Maximum requests per minute

- **OPENROUTER_ENABLED**: Enable OpenRouter integration
- **OPENROUTER_API_KEYS**: Comma-separated list of OpenRouter API keys
- **OPENROUTER_PRIORITY**: Priority for OpenRouter in provider selection
- **OPENROUTER_MODEL**: Model identifier for OpenRouter API
- **OPENROUTER_MAX_TOKENS**: Maximum tokens for AI responses
- **OPENROUTER_TEMPERATURE**: Creativity level for AI responses
- **OPENROUTER_RATE_LIMIT**: Maximum requests per minute

Additional AI configuration variables:
- **AI_PROXY_ENABLED**: Enable proxy rotation for AI requests
- **AI_PROXY_IPS**: Comma-separated list of proxy IP addresses
- **AI_PROXY_ROTATION_INTERVAL_MS**: Interval for rotating proxy IPs
- **AI_PROXY_HEALTH_CHECK_INTERVAL_MS**: Interval for checking proxy health
- **AI_FALLBACK_ENABLED**: Enable fallback to alternative providers on failure
- **AI_MAX_RETRIES**: Maximum retry attempts for failed AI requests
- **AI_TIMEOUT_MS**: Timeout for AI API requests

The AI configuration supports multiple API keys per provider for load balancing and rate limit management. Providers are selected based on priority, with fallback to lower-priority providers when enabled.

**Section sources**
- [SeederConfig.ts](file://services/domain-seeder/src/config/SeederConfig.ts#L250-L300)

## Monitoring and Logging
The domainr platform includes comprehensive monitoring and logging configuration to support observability across environments.

```mermaid
classDiagram
class MonitoringConfig {
+metricsEnabled : boolean
+metricsPort : number
+healthCheckEnabled : boolean
+healthCheckPort : number
+alertingEnabled : boolean
+logLevel : 'error' | 'warn' | 'info' | 'debug'
+logFormat : 'json' | 'text'
+logDir : string
+maxLogFiles : number
+maxLogSize : string
}
MonitoringConfig
```

**Diagram sources**
- [SeederConfig.ts](file://services/domain-seeder/src/config/SeederConfig.ts#L300-L350)

### Monitoring Configuration
The following environment variables control monitoring and logging:

- **METRICS_ENABLED**: Enable Prometheus metrics collection
- **METRICS_PORT**: Port for exposing Prometheus metrics
- **HEALTH_CHECK_ENABLED**: Enable health check endpoint
- **HEALTH_CHECK_PORT**: Port for health check endpoint
- **ALERTING_ENABLED**: Enable alerting system
- **LOG_LEVEL**: Minimum log level to record (error, warn, info, debug)
- **LOG_FORMAT**: Log output format (json, text)
- **LOG_DIR**: Directory for log files
- **MAX_LOG_FILES**: Maximum number of log files to retain
- **MAX_LOG_SIZE**: Maximum size of individual log files

The monitoring system integrates with Prometheus for metrics collection and alerting. Health checks are exposed as HTTP endpoints for container orchestration systems. Logging supports both JSON and text formats, with JSON recommended for production environments to facilitate log parsing and analysis.

**Section sources**
- [SeederConfig.ts](file://services/domain-seeder/src/config/SeederConfig.ts#L350-L369)

## Security Settings
The domainr platform implements robust security configuration to protect sensitive data and ensure secure communications.

```mermaid
classDiagram
class SecurityConfig {
+encryptionKey : string?
+credentialRotationIntervalDays : number
+credentialExpiryWarningDays : number
+secretsPath : string
+tlsEnabled : boolean
+tlsCertPath : string?
+tlsKeyPath : string?
}
SecurityConfig
```

**Diagram sources**
- [SeederConfig.ts](file://services/domain-seeder/src/config/SeederConfig.ts#L350-L369)

### Security Configuration
The following environment variables control security settings:

- **ENCRYPTION_KEY**: Encryption key for data protection (minimum 32 characters)
- **CREDENTIAL_ROTATION_INTERVAL_DAYS**: Interval for rotating credentials
- **CREDENTIAL_EXPIRY_WARNING_DAYS**: Days before credential expiry to issue warnings
- **SECRETS_PATH**: Directory for storing encrypted secrets
- **TLS_ENABLED**: Enable TLS for secure communications
- **TLS_CERT_PATH**: Path to TLS certificate file
- **TLS_KEY_PATH**: Path to TLS private key file

Security configuration is validated differently based on the environment. In production, the encryption key and TLS configuration are required, while in development they are optional. The system issues warnings when security settings are insufficient for the current environment.

**Section sources**
- [SeederConfig.ts](file://services/domain-seeder/src/config/SeederConfig.ts#L369-L383)

## Source Configuration
The domainr platform integrates with multiple data sources for domain discovery and analysis. Each source has configurable parameters for connection and rate limiting.

```mermaid
classDiagram
class SourcesConfig {
+tranco : SourceConfig
+radar : SourceConfig
+umbrella : SourceConfig
+czds : SourceConfig
+pir : SourceConfig
+commonCrawl : SourceConfig
+sonar : SourceConfig
}
class SourceConfig {
+enabled : boolean
+priority : number
+timeout : number
+maxRetries : number
+rateLimit : number
}
class RadarConfig {
+apiKey : string
}
class UmbrellaConfig {
+apiKey : string
}
class CZDSConfig {
+username : string
+password : string
+downloadPath : string
}
class PIRConfig {
+apiKey : string
}
class CommonCrawlConfig {
+indexUrl : string
+batchSize : number
}
class SonarConfig {
+apiKey : string
}
SourcesConfig --> SourceConfig
SourcesConfig --> RadarConfig
SourcesConfig --> UmbrellaConfig
SourcesConfig --> CZDSConfig
SourcesConfig --> PIRConfig
SourcesConfig --> CommonCrawlConfig
SourcesConfig --> SonarConfig
```

**Diagram sources**
- [SeederConfig.ts](file://services/domain-seeder/src/config/SeederConfig.ts#L200-L250)

### Source Configuration Variables
The following environment variables configure data source integration:

- **TRANCO_ENABLED**: Enable Tranco data source
- **TRANCO_PRIORITY**: Priority for Tranco in source selection
- **TRANCO_TIMEOUT**: Request timeout for Tranco API
- **TRANCO_MAX_RETRIES**: Maximum retry attempts for failed requests
- **TRANCO_RATE_LIMIT**: Maximum requests per minute

- **RADAR_ENABLED**: Enable Cloudflare Radar data source
- **CLOUDFLARE_API_KEY**: API key for Cloudflare Radar access
- **RADAR_PRIORITY**: Priority for Radar in source selection
- **RADAR_TIMEOUT**: Request timeout for Radar API
- **RADAR_MAX_RETRIES**: Maximum retry attempts
- **RADAR_RATE_LIMIT**: Maximum requests per minute

- **UMBRELLA_ENABLED**: Enable Cisco Umbrella data source
- **UMBRELLA_API_KEY**: API key for Cisco Umbrella access
- **UMBRELLA_PRIORITY**: Priority for Umbrella in source selection
- **UMBRELLA_TIMEOUT**: Request timeout for Umbrella API
- **UMBRELLA_MAX_RETRIES**: Maximum retry attempts
- **UMBRELLA_RATE_LIMIT**: Maximum requests per minute

- **CZDS_ENABLED**: Enable CZDS data source
- **CZDS_USERNAME**: Username for CZDS access
- **CZDS_PASSWORD**: Password for CZDS access
- **CZDS_PRIORITY**: Priority for CZDS in source selection
- **CZDS_TIMEOUT**: Request timeout for CZDS API
- **CZDS_MAX_RETRIES**: Maximum retry attempts
- **CZDS_DOWNLOAD_PATH**: Local path for CZDS downloads

- **PIR_ENABLED**: Enable PassiveTotal (PIR) data source
- **PIR_API_KEY**: API key for PassiveTotal access
- **PIR_PRIORITY**: Priority for PIR in source selection
- **PIR_TIMEOUT**: Request timeout for PIR API
- **PIR_MAX_RETRIES**: Maximum retry attempts
- **PIR_RATE_LIMIT**: Maximum requests per minute

- **COMMON_CRAWL_ENABLED**: Enable Common Crawl data source
- **COMMON_CRAWL_PRIORITY**: Priority for Common Crawl in source selection
- **COMMON_CRAWL_TIMEOUT**: Request timeout for Common Crawl API
- **COMMON_CRAWL_MAX_RETRIES**: Maximum retry attempts
- **COMMON_CRAWL_INDEX_URL**: URL for Common Crawl index
- **COMMON_CRAWL_BATCH_SIZE**: Number of records to process per batch

- **SONAR_ENABLED**: Enable Rapid7 Sonar data source
- **SONAR_API_KEY**: API key for Rapid7 Sonar access
- **SONAR_PRIORITY**: Priority for Sonar in source selection
- **SONAR_TIMEOUT**: Request timeout for Sonar API
- **SONAR_MAX_RETRIES**: Maximum retry attempts
- **SONAR_RATE_LIMIT**: Maximum requests per minute

Source configuration supports priority-based selection, allowing the system to prefer certain sources over others. Rate limiting is enforced to comply with API usage policies.

**Section sources**
- [SeederConfig.ts](file://services/domain-seeder/src/config/SeederConfig.ts#L300-L350)

## Deployment Examples
The domainr platform supports multiple deployment environments with different configuration requirements.

### Docker Deployment
For Docker deployments, environment variables are typically provided through environment files or command-line arguments:

```dockerfile
# Example Docker run command
docker run -d \
  --name domain-seeder \
  -e NODE_ENV=production \
  -e MARIA_HOST=db.domainr.com \
  -e MARIA_USER=seeder \
  -e MARIA_PASSWORD=securepassword \
  -e REDIS_HOST=redis.domainr.com \
  -e OPENAI_API_KEYS=sk-xxx,sk-yyy \
  -e UMBRELLA_API_KEY=umbrella-key-123 \
  -e ENCRYPTION_KEY=32-character-encryption-key-here \
  -e TLS_ENABLED=true \
  -e TLS_CERT_PATH=/certs/domainr.com.crt \
  -e TLS_KEY_PATH=/certs/domainr.com.key \
  -p 3000:3000 \
  -p 9090:9090 \
  -v /path/to/certs:/certs:ro \
  domainr/domain-seeder:latest
```

### Kubernetes Deployment
For Kubernetes deployments, environment variables are typically configured through ConfigMaps and Secrets:

```yaml
# Example Kubernetes deployment configuration
apiVersion: apps/v1
kind: Deployment
metadata:
  name: domain-seeder
spec:
  replicas: 3
  selector:
    matchLabels:
      app: domain-seeder
  template:
    metadata:
      labels:
        app: domain-seeder
    spec:
      containers:
      - name: domain-seeder
        image: domainr/domain-seeder:latest
        env:
        - name: NODE_ENV
          value: "production"
        - name: MARIA_HOST
          valueFrom:
            configMapKeyRef:
              name: domainr-config
              key: maria-host
        - name: MARIA_USER
          valueFrom:
            configMapKeyRef:
              name: domainr-config
              key: maria-user
        - name: MARIA_PASSWORD
          valueFrom:
            secretKeyRef:
              name: domainr-secrets
              key: maria-password
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: domainr-config
              key: redis-host
        - name: OPENAI_API_KEYS
          valueFrom:
            secretKeyRef:
              name: domainr-secrets
              key: openai-api-keys
        - name: UMBRELLA_API_KEY
          valueFrom:
            secretKeyRef:
              name: domainr-secrets
              key: umbrella-api-key
        - name: ENCRYPTION_KEY
          valueFrom:
            secretKeyRef:
              name: domainr-secrets
              key: encryption-key
        - name: TLS_ENABLED
          value: "true"
        - name: TLS_CERT_PATH
          value: "/certs/tls.crt"
        - name: TLS_KEY_PATH
          value: "/certs/tls.key"
        ports:
        - containerPort: 3000
        - containerPort: 9090
        volumeMounts:
        - name: certs
          mountPath: /certs
          readOnly: true
      volumes:
      - name: certs
        secret:
          secretName: domainr-tls
```

**Section sources**
- [SeederConfig.ts](file://services/domain-seeder/src/config/SeederConfig.ts#L369-L383)

## Validation and Error Handling
The domainr platform implements comprehensive configuration validation to prevent runtime errors due to misconfiguration.

```mermaid
flowchart TD
Start([Configuration Load]) --> ParseEnv["Parse Environment Variables"]
ParseEnv --> ValidateSchema["Validate Against Zod Schema"]
ValidateSchema --> SchemaValid{"Schema Valid?"}
SchemaValid --> |No| ReportSchemaErrors["Report Schema Errors"]
SchemaValid --> |Yes| AdditionalValidation["Perform Additional Validation"]
AdditionalValidation --> CheckAPIKeys["Check Required API Keys"]
CheckAPIKeys --> CheckSecurity["Check Security Configuration"]
CheckSecurity --> CheckResources["Check Resource Limits"]
CheckResources --> ValidationComplete["Validation Complete"]
ReportSchemaErrors --> End([Fail with Error])
ValidationComplete --> WarnIssues["Warn About Issues"]
WarnIssues --> EndSuccess([Configuration Valid])
```

**Diagram sources**
- [SeederConfig.ts](file://services/domain-seeder/src/config/SeederConfig.ts#L400-L500)

The validation system performs two levels of validation:
1. **Schema validation**: Using Zod to ensure correct types and required fields
2. **Additional validation**: Business logic checks for configuration consistency

Validation checks include:
- Required API keys for enabled sources
- Sufficient security configuration for production environments
- Reasonable resource limits to prevent performance issues
- Correct formatting of connection strings and endpoints

When validation fails, the system provides detailed error messages indicating the specific configuration issues. For non-critical issues, warnings are logged instead of failing startup.

**Section sources**
- [SeederConfig.ts](file://services/domain-seeder/src/config/SeederConfig.ts#L500-L600)

## Dynamic Configuration and Hot Reload
The domainr platform supports dynamic configuration reloading to allow configuration changes without service restarts.

```mermaid
sequenceDiagram
participant FS as "File System"
participant Config as "Configuration Manager"
participant Service as "Service Components"
FS->>Config : File change detected
Config->>Config : Start debounce timer
Config->>Config : Wait for debounce period
Config->>Config : Reload configuration
Config->>Config : Validate new configuration
alt Valid Configuration
Config->>Config : Update internal state
Config->>Service : Notify components of change
Service->>Service : Apply new configuration
Config->>FS : Watch for further changes
else Invalid Configuration
Config->>Config : Log error
Config->>FS : Continue with previous configuration
Config->>FS : Watch for further changes
end
```

**Diagram sources**
- [SeederConfig.ts](file://services/domain-seeder/src/config/SeederConfig.ts#L800-L900)

The hot reload system monitors configuration files and environment files for changes. When a change is detected, it:
1. Applies a debounce period to avoid processing rapid successive changes
2. Reloads and validates the new configuration
3. Updates the internal configuration state if valid
4. Notifies registered components of the change
5. Continues monitoring for further changes

Hot reload can be enabled programmatically with customizable options:
- **enabled**: Whether hot reload is active
- **watchPaths**: Additional paths to monitor
- **debounceMs**: Milliseconds to wait before processing changes
- **callback**: Function to call when configuration changes

This allows for zero-downtime configuration updates in production environments.

**Section sources**
- [SeederConfig.ts](file://services/domain-seeder/src/config/SeederConfig.ts#L900-L1000)

## Troubleshooting Guide
This section provides guidance for diagnosing and resolving common configuration-related issues.

### Database Connection Issues
**Symptoms**: Service fails to start or reports database connection errors.

**Common causes and solutions**:
- **Incorrect host or port**: Verify the MARIA_HOST, MARIA_PORT, and other database connection variables
- **Authentication failure**: Check MARIA_USER and MARIA_PASSWORD values
- **Network connectivity**: Ensure the database server is reachable from the service container
- **Firewall rules**: Verify that firewall rules allow connections on the database port

**Diagnostic steps**:
1. Check the service logs for specific database error messages
2. Verify the database server is running and accessible
3. Test connectivity using tools like telnet or nc
4. Confirm credentials are correct and have appropriate permissions

### API Token Issues
**Symptoms**: Source integration fails with authentication errors.

**Common causes and solutions**:
- **Missing API keys**: Ensure API key variables are set for enabled sources
- **Expired tokens**: Regenerate API keys and update configuration
- **Insufficient permissions**: Verify API keys have required permissions
- **Rate limiting**: Check rate limit configuration and usage

**Diagnostic steps**:
1. Verify the source is enabled (e.g., RADAR_ENABLED=true)
2. Check that the API key variable is set (e.g., CLOUDFLARE_API_KEY)
3. Test the API key directly with the provider's API
4. Monitor rate limit headers in API responses

### Configuration Validation Errors
**Symptoms**: Service fails to start with configuration validation errors.

**Common causes and solutions**:
- **Invalid data types**: Ensure numeric values are actually numbers
- **Missing required fields**: Provide values for required configuration
- **Incorrect enum values**: Use allowed values for enum fields
- **Security requirements**: Add required security configuration in production

**Diagnostic steps**:
1. Review the specific validation error messages
2. Check the configuration against the documented requirements
3. Verify all required fields are present and correctly formatted
4. Consult the environment profile requirements for your deployment environment

**Section sources**
- [SeederConfig.ts](file://services/domain-seeder/src/config/SeederConfig.ts#L500-L600)
- [EnvironmentProfiles.ts](file://services/domain-seeder/src/config/EnvironmentProfiles.ts#L499-L539)