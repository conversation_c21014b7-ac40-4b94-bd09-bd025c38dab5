# Development Setup

<cite>
**Referenced Files in This Document**   
- [docker-compose.dev.yml](file://docker-compose.dev.yml)
- [setup-environment.sh](file://scripts/setup-environment.sh)
- [Dockerfile](file://services/domain-seeder/Dockerfile)
- [docker-compose.yml](file://docker-compose.yml)
- [package.json](file://services/domain-seeder/package.json)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Development Environment Configuration](#development-environment-configuration)
3. [Environment Setup Script](#environment-setup-script)
4. [Service Initialization](#service-initialization)
5. [Development Mode Configuration](#development-mode-configuration)
6. [Debugging and Hot Reloading](#debugging-and-hot-reloading)
7. [Common Development Workflows](#common-development-workflows)
8. [Troubleshooting Guide](#troubleshooting-guide)
9. [Performance Considerations](#performance-considerations)
10. [Service Integration and Verification](#service-integration-and-verification)

## Introduction
This document provides comprehensive guidance for configuring and managing the local development environment for the domainr platform. It covers the setup process using Docker Compose, initialization of environment variables, and configuration of all core services including the domain-seeder, worker service, web-app, and admin interface. The documentation emphasizes practical workflows, debugging capabilities, and performance optimization for local development.

## Development Environment Configuration

The development environment is configured through the `docker-compose.dev.yml` file, which extends the base `docker-compose.yml` configuration with development-specific overrides. This two-file approach allows for environment-specific configurations while maintaining consistency across development, staging, and production environments.

The development configuration enables service port exposure, code mounting for hot reloading, and enhanced logging for debugging purposes. Services are configured to run in development mode with appropriate resource allocations and network connectivity.

```mermaid
graph TB
subgraph "Development Configuration"
ComposeBase[docker-compose.yml] --> ComposeDev[docker-compose.dev.yml]
ComposeDev --> |Overrides| DomainSeeder[domain-seeder]
ComposeDev --> |Overrides| WebApp[web-app]
ComposeDev --> |Overrides| Worker[worker]
ComposeDev --> |Overrides| Databases[Databases]
ComposeDev --> |Overrides| Monitoring[Monitoring]
end
DomainSeeder --> |Connects to| Databases
WebApp --> |Connects to| Databases
Worker --> |Connects to| Databases
Databases --> |Provides data| AllServices
Monitoring --> |Monitors| AllServices
```

**Diagram sources**
- [docker-compose.dev.yml](file://docker-compose.dev.yml)
- [docker-compose.yml](file://docker-compose.yml)

**Section sources**
- [docker-compose.dev.yml](file://docker-compose.dev.yml)
- [docker-compose.yml](file://docker-compose.yml)

## Environment Setup Script

The `setup-environment.sh` script automates the initialization of the development environment by creating configuration files, directories, and Docker networks. It supports both development and production environment setups through command-line arguments.

Key functionalities of the script include:
- Creation of `.env` configuration file from template
- Generation of secure passwords for production environments
- Creation of required directories for logs and data
- Setup of Docker network for service communication
- Database initialization and validation

The script implements environment-specific configurations, with development mode enabling debug logging, reduced processing limits, and shorter health check intervals to facilitate faster development cycles.

```mermaid
flowchart TD
Start([Script Execution]) --> ParseArgs["Parse Command Line Arguments"]
ParseArgs --> ValidateEnv{"Environment Valid?"}
ValidateEnv --> |No| ShowUsage["Display Usage Instructions"]
ValidateEnv --> |Yes| CreateEnv["Create .env File"]
CreateEnv --> CreateDirs["Create Directories"]
CreateDirs --> SetupNetwork["Setup Docker Network"]
SetupNetwork --> InitDB["Initialize Databases"]
InitDB --> Validate["Validate Setup"]
Validate --> Summary["Display Setup Summary"]
Summary --> End([Setup Complete])
ShowUsage --> End
```

**Diagram sources**
- [setup-environment.sh](file://scripts/setup-environment.sh)

**Section sources**
- [setup-environment.sh](file://scripts/setup-environment.sh)

## Service Initialization

To start all services in development mode, use the following command:

```bash
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d
```

This command combines the base configuration with development overrides to launch the complete stack. The following services are initialized:

- **domain-seeder**: Domain discovery and seeding service
- **web-app**: Web application interface
- **worker**: Background processing service
- **admin**: Administrative interface
- **Database services**: ScyllaDB, MariaDB, Redis, Manticore
- **Monitoring services**: Prometheus, Grafana

Service dependencies are automatically resolved through the `depends_on` directives in the Docker Compose configuration, ensuring that database services are available before application services start.

```mermaid
sequenceDiagram
participant Terminal as "Terminal"
participant DockerCompose as "Docker Compose"
participant Services as "Service Containers"
Terminal->>DockerCompose : docker-compose up -d
DockerCompose->>DockerCompose : Load docker-compose.yml
DockerCompose->>DockerCompose : Load docker-compose.dev.yml
DockerCompose->>DockerCompose : Resolve service dependencies
DockerCompose->>Services : Start databases (scylla, mariadb, redis, manticore)
Services-->>DockerCompose : Database services started
DockerCompose->>Services : Start application services
Services-->>DockerCompose : Application services started
DockerCompose-->>Terminal : All services up and running
Note over DockerCompose,Services : Services start in dependency order
```

**Diagram sources**
- [docker-compose.dev.yml](file://docker-compose.dev.yml)
- [docker-compose.yml](file://docker-compose.yml)

**Section sources**
- [docker-compose.dev.yml](file://docker-compose.dev.yml)
- [docker-compose.yml](file://docker-compose.yml)

## Development Mode Configuration

The `docker-compose.dev.yml` file contains specific configurations for development mode, including:

- **Port mappings**: Development ports are exposed for direct access to services
- **Volume mounts**: Source code is mounted for hot reloading
- **Environment variables**: Development-specific settings for logging and performance
- **Resource limits**: Adjusted memory and CPU allocations for development

Key configuration options include:

- **domain-seeder**: Configured with hot reloading and debug logging
- **Web application**: Port 3000 exposed for local access
- **Worker service**: Port 3001 exposed for monitoring
- **Database services**: Development ports exposed for direct connection
- **Monitoring**: Grafana and Prometheus ports exposed for visualization

The configuration reduces processing limits for development (e.g., `MAX_NEW_PER_DAY=10000` vs production's 500000) to conserve resources during local development.

**Section sources**
- [docker-compose.dev.yml](file://docker-compose.dev.yml)

## Debugging and Hot Reloading

The development environment supports comprehensive debugging capabilities and hot reloading for rapid development cycles.

### Hot Reloading Configuration
The domain-seeder service is configured for hot reloading by mounting the source code directories:
- `./services/domain-seeder/src:/app/services/domain-seeder/src`
- `./shared/src:/app/shared/src`

This allows code changes to be immediately reflected in the running container without requiring a restart.

### Debugging Options
- **Log level**: Set to `debug` for detailed output
- **Metrics**: Enabled for performance monitoring
- **Health checks**: Shorter intervals for faster feedback
- **Port exposure**: All services accessible via localhost

The Dockerfile for domain-seeder specifies a development target that uses `tsx` for running TypeScript files with watch mode:
```json
"dev": "tsx --watch src/index.ts"
```

This enables automatic restart of the service when code changes are detected.

**Section sources**
- [docker-compose.dev.yml](file://docker-compose.dev.yml)
- [Dockerfile](file://services/domain-seeder/Dockerfile)
- [package.json](file://services/domain-seeder/package.json)

## Common Development Workflows

### Local Development Setup
1. Run the environment setup script:
```bash
./scripts/setup-environment.sh development
```
2. Start all services:
```bash
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d
```
3. Access services via localhost:
   - Web application: http://localhost:3000
   - Domain seeder: http://localhost:3004
   - Grafana: http://localhost:3001

### Domain Seeder Development
For focused development on the domain-seeder service:
```bash
# Start only domain-seeder and dependencies
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d scylla mariadb redis manticore domain-seeder
```

### Testing and Validation
The setup includes comprehensive testing capabilities:
- Unit tests: `pnpm test` within service directories
- Integration tests: Available in test suites
- Health checks: Accessible at `/health` endpoints
- Metrics: Available at `/metrics` endpoints

**Section sources**
- [setup-environment.sh](file://scripts/setup-environment.sh)
- [docker-compose.dev.yml](file://docker-compose.dev.yml)
- [docker-compose.yml](file://docker-compose.yml)

## Troubleshooting Guide

### Container Startup Issues
**Issue**: Database services fail to start
**Solution**: Ensure sufficient system resources and check logs:
```bash
docker-compose logs mariadb
docker-compose logs scylla
```

**Issue**: Environment variables not loading
**Solution**: Verify `.env` file creation and content:
```bash
cat .env | grep MARIA_PASSWORD
```

**Issue**: Port conflicts
**Solution**: Check for conflicting processes:
```bash
lsof -i :3000
# Or modify port mappings in .env file
```

### Common Errors and Solutions
- **Database connection errors**: Verify database services are running and network connectivity
- **Permission issues**: Ensure proper directory permissions for logs and data
- **Missing dependencies**: Run `pnpm install` in service directories if needed
- **Configuration errors**: Validate `.env` file against `.env.example`

**Section sources**
- [setup-environment.sh](file://scripts/setup-environment.sh)
- [docker-compose.dev.yml](file://docker-compose.dev.yml)
- [docker-compose.yml](file://docker-compose.yml)

## Performance Considerations

When running the full stack locally, consider the following performance aspects:

### Resource Allocation
The development configuration sets resource limits to balance performance and system stability:
- **domain-seeder**: 1GB memory limit, 1 CPU
- **worker**: 4GB memory limit, 4 CPUs (production), reduced for development
- **Databases**: Appropriate memory allocations for local testing

### Optimization Tips
- Run only necessary services during development
- Use smaller dataset sizes for testing
- Monitor resource usage through Grafana
- Adjust processing batch sizes for development needs

### Memory Management
The system uses multiple databases with different purposes:
- **ScyllaDB**: Primary domain storage
- **MariaDB**: Relational data
- **Redis**: Caching and queuing
- **Manticore**: Full-text search

Ensure sufficient memory is available for all database services, particularly when processing large datasets.

**Section sources**
- [docker-compose.dev.yml](file://docker-compose.dev.yml)
- [docker-compose.yml](file://docker-compose.yml)

## Service Integration and Verification

### Integration Points
The services are integrated through:
- **Database connections**: Shared database configurations
- **Network communication**: Docker network for service discovery
- **Environment variables**: Centralized configuration
- **Shared code**: Common utilities in shared directory

### Verification Steps
After starting the services, verify successful initialization:

1. Check container status:
```bash
docker-compose ps
```

2. Verify service health:
```bash
curl http://localhost:3004/health/live
curl http://localhost:3000/health
```

3. Check logs for errors:
```bash
docker-compose logs domain-seeder | grep -i error
```

4. Access monitoring dashboards:
   - Grafana: http://localhost:3001
   - Prometheus: http://localhost:9090

Successful initialization is indicated by all services showing "running" status, health checks passing, and no critical errors in logs.

**Section sources**
- [docker-compose.dev.yml](file://docker-compose.dev.yml)
- [docker-compose.yml](file://docker-compose.yml)