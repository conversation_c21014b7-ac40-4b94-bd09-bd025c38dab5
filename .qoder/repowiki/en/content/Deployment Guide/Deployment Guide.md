# Deployment Guide

<cite>
**Referenced Files in This Document**   
- [docker-compose.yml](file://docker-compose.yml)
- [docker-compose.dev.yml](file://docker-compose.dev.yml)
- [services/admin/docker-compose.production.yml](file://services/admin/docker-compose.production.yml)
- [services/admin/docker-compose.simple.yml](file://services/admin/docker-compose.simple.yml)
- [services/worker/docker-compose.test.yml](file://services/worker/docker-compose.test.yml)
- [services/admin/DEPLOYMENT.md](file://services/admin/DEPLOYMENT.md)
- [README.md](file://README.md)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Development Setup](#development-setup)
3. [Production Deployment](#production-deployment)
4. [Docker Configuration](#docker-configuration)
5. [Environment Variables](#environment-variables)
6. [Docker Compose Configurations](#docker-compose-configurations)
7. [Service Deployment Instructions](#service-deployment-instructions)
8. [Deployment Topology](#deployment-topology)
9. [Performance Considerations](#performance-considerations)
10. [Troubleshooting](#troubleshooting)

## Introduction

The Domainr platform is a comprehensive domain ranking and analysis system that evaluates websites based on performance, security, SEO, and technical metrics. This deployment guide provides detailed instructions for setting up the system in various environments, from development to production. The architecture consists of multiple services including web applications, worker processes, domain seeding, and monitoring components, all orchestrated through Docker and Docker Compose.

The system is designed with scalability and reliability in mind, featuring containerized services, comprehensive monitoring with Prometheus and Grafana, centralized logging, and automated backup capabilities. This guide covers all aspects of deployment, from initial setup to production configuration, ensuring a smooth deployment process for both beginners and experienced developers.

**Section sources**
- [README.md](file://README.md#L0-L735)

## Development Setup

### Prerequisites

Before beginning development setup, ensure the following prerequisites are met:
- Docker 20.10+
- Docker Compose 2.0+
- Node.js 18+
- Yarn 4.0+ or pnpm
- At least 8GB RAM and 4 CPU cores
- 50GB+ storage space

### Quick Start Development

1. **Clone and Initialize Repository**
   ```bash
   git clone <repository-url>
   cd domainr
   cp .env.example .env
   # Edit .env with your configuration
   ```

2. **Install Dependencies**
   ```bash
   pnpm install
   # or using yarn
   yarn install
   ```

3. **Start Development Environment**
   ```bash
   # Using docker-compose with development overrides
   docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
   
   # Or use development scripts
   ./scripts/setup-environment.sh
   ```

4. **Access Development Services**
   - Web Application: http://localhost:3000
   - Admin Panel: http://localhost:3004
   - Prometheus: http://localhost:9090
   - Grafana: http://localhost:3001

### Development Configuration

The development environment is configured through `docker-compose.dev.yml`, which overrides production settings with development-friendly configurations:

- **Hot Reloading**: Source code is mounted directly into containers for immediate changes
- **Debug Logging**: Enhanced logging levels for troubleshooting
- **Reduced Resource Limits**: Lower memory and CPU requirements for development machines
- **Exposed Ports**: All service ports are exposed for direct access
- **Disabled Health Checks**: Health checks are disabled to prevent restarts during development

Key environment variables for development:
```bash
NODE_ENV=development
LOG_LEVEL=debug
WEB_APP_DEV_PORT=3000
WORKER_DEV_PORT=3001
SEEDER_DEV_PORT=3004
```

### Running Individual Services

For targeted development, individual services can be started independently:

```bash
# Web Application development
cd services/web-app
pnpm dev

# Worker Service development
cd services/worker
pnpm dev

# Domain Seeder development
cd services/domain-seeder
pnpm dev

# Admin Panel development
cd services/admin
pnpm dev
```

**Section sources**
- [README.md](file://README.md#L0-L735)
- [docker-compose.dev.yml](file://docker-compose.dev.yml#L0-L121)

## Production Deployment

### Production Architecture

The production deployment follows a secure, scalable architecture with multiple layers of monitoring, logging, and redundancy. The core components include:

- **Nginx Reverse Proxy**: Handles SSL termination, security headers, and load balancing
- **Admin Panel**: Next.js application with security hardening
- **Monitoring Stack**: Prometheus, Grafana, and Alertmanager for comprehensive monitoring
- **Centralized Logging**: Fluentd for log aggregation and processing
- **Backup and Recovery**: Automated backup system with S3 integration
- **Database Services**: MariaDB, ScyllaDB, Redis, and Manticore Search

### Production Prerequisites

Before deploying to production, ensure the following requirements are met:

**System Requirements:**
- Docker Engine 20.10+
- Docker Compose 2.0+
- 8GB RAM minimum (16GB recommended)
- 100GB disk space minimum
- SSL certificates from a trusted CA

**Network Requirements:**
- Port 80 (HTTP redirect)
- Port 443 (HTTPS)
- Internal ports for monitoring and administration
- Firewall configuration for security

**Security Requirements:**
- Valid SSL/TLS certificates
- Secure environment variables
- Regular security patching
- Access control and authentication

### Production Deployment Process

1. **Environment Configuration**
   ```bash
   # Copy production environment template
   cp .env.example .env
   # Edit .env with production values
   nano .env
   ```

2. **SSL Certificate Setup**
   ```bash
   # Create SSL directory
   mkdir -p nginx/ssl
   
   # Copy your SSL certificates
   cp your-cert.pem nginx/ssl/cert.pem
   cp your-key.pem nginx/ssl/key.pem
   
   # Or use Let's Encrypt
   certbot certonly --nginx -d yourdomain.com
   ```

3. **Database Initialization**
   ```bash
   # Initialize databases with sample data
   just db-init
   
   # Or run migrations individually
   just db-migrate
   just db-sample-data
   ```

4. **Deploy Services**
   ```bash
   # Build and start all services
   docker-compose up -d
   
   # Verify deployment status
   docker-compose ps
   ```

5. **Health Checks**
   ```bash
   # Check application health
   curl http://localhost/health
   
   # Check all service health
   just health
   ```

### Admin Panel Production Deployment

The Admin Panel has a dedicated production configuration in `services/admin/docker-compose.production.yml`:

```bash
# Deploy Admin Panel
cd services/admin
docker-compose -f docker-compose.production.yml up -d

# Run production health check
./scripts/production-health-check.sh

# Monitor logs
docker-compose -f docker-compose.production.yml logs -f admin-panel
```

Key production features:
- **Security Hardening**: Non-root containers, security headers, rate limiting
- **Monitoring**: Prometheus metrics, Grafana dashboards, Alertmanager alerts
- **Backup**: Automated daily backups with 30-day retention
- **High Availability**: Resource limits and reservations for stable performance

**Section sources**
- [services/admin/DEPLOYMENT.md](file://services/admin/DEPLOYMENT.md#L0-L463)
- [docker-compose.yml](file://docker-compose.yml#L0-L387)

## Docker Configuration

### Base Docker Configuration

The Domainr platform uses Docker for containerization, ensuring consistent environments across development, testing, and production. The base configuration is defined in `docker-compose.yml`, which specifies all services and their dependencies.

Key Docker configuration elements:

**Network Configuration:**
- Custom bridge network `domain-ranking-network` with subnet 172.20.0.0/16
- Isolated networking for service communication
- External access through Nginx reverse proxy

**Volume Configuration:**
- Named volumes for persistent data storage
- Bind mounts for configuration and log files
- Data persistence across container restarts

**Resource Management:**
- CPU and memory limits for each service
- Resource reservations to ensure minimum requirements
- Prevents resource starvation in multi-service environments

### Container Security

All containers follow security best practices:

- **Non-root Users**: Services run as non-root users where possible
- **Read-only Filesystems**: Configuration files mounted as read-only
- **Minimal Images**: Alpine-based images for reduced attack surface
- **Health Checks**: Built-in health checks for automatic recovery
- **Restart Policies**: `unless-stopped` policy for service resilience

### Service-Specific Docker Configuration

Each service has specific Docker configuration tailored to its requirements:

**Web Application:**
- 1GB memory limit, 512MB reservation
- Exposed on port 3000 internally
- Depends on all database services

**Worker Service:**
- 4GB memory limit, 2GB reservation (resource-intensive)
- Exposed on port 3001 internally
- Additional dependency on browserless service

**Domain Seeder:**
- 2GB memory limit, 1GB reservation
- Exposed on port 3004 internally
- Health check endpoint for monitoring

**Database Services:**
- Dedicated resource allocations
- Persistent volume storage
- Initialization scripts for schema creation

**Monitoring Services:**
- Prometheus with 30-day retention
- Grafana with pre-configured dashboards
- Alertmanager for notification handling

**Section sources**
- [docker-compose.yml](file://docker-compose.yml#L0-L387)

## Environment Variables

### Core Environment Variables

Environment variables are used to configure the Domainr platform across different environments. The main configuration file is `.env`, with example values in `.env.example`.

**Required Variables:**
```bash
# Database credentials
MARIA_PASSWORD=your_secure_password_here
JWT_SECRET=your_jwt_secret_here

# Service configuration
NODE_ENV=production
LOG_LEVEL=info
CORS_ORIGIN=https://yourdomain.com
```

### Service-Specific Environment Variables

Each service has specific environment variables for configuration:

**Web Application Variables:**
```bash
WEB_APP_PORT=3000
SCYLLA_HOSTS=scylla:9042
MARIA_HOST=mariadb
REDIS_URL=redis://redis:6379
MANTICORE_HOST=manticore
```

**Worker Service Variables:**
```bash
WORKER_PORT=3001
CRAWL_RATE_LIMIT=60
CRAWL_CONCURRENT_REQUESTS=5
CRAWL_TIMEOUT=30000
RANKING_WEIGHT_PERFORMANCE=0.25
RANKING_WEIGHT_SECURITY=0.20
RANKING_WEIGHT_SEO=0.20
RANKING_WEIGHT_TECHNICAL=0.15
RANKING_WEIGHT_BACKLINKS=0.20
```

**Domain Seeder Variables:**
```bash
SEEDER_MAX_NEW_PER_DAY=500000
SEEDER_ENQUEUE_BATCH=1000
SEEDER_ENQUEUE_INTERVAL=1000
SEEDER_QUEUE_MAX_DEPTH=200000
SEEDER_DB_CHECK_BATCH=5000
SEEDER_BLOOM_FP_RATE=0.01
```

**External Service Credentials:**
```bash
# Domain data sources
CZDS_USERNAME=your_username
CZDS_PASSWORD=your_password
UMBRELLA_API_KEY=your_api_key
PIR_API_KEY=your_api_key

# AI providers
OPENAI_API_KEYS=your_openai_key
CLAUDE_API_KEYS=your_claude_key
GEMINI_API_KEYS=your_gemini_key
OPENROUTER_API_KEYS=your_openrouter_key

# Proxy configuration
PROXY_IPS=ip1,ip2,ip3
PROXY_ROTATION_ENABLED=true
```

### Environment-Specific Configuration

Different environments use different configuration approaches:

**Development Environment:**
- Debug logging enabled
- Reduced resource limits
- Hot reloading of code
- Self-signed SSL certificates

**Production Environment:**
- Secure environment variables
- Valid SSL certificates
- Comprehensive monitoring
- Automated backups
- Rate limiting and security headers

**Testing Environment:**
- Isolated database instances
- Mock external services
- Enhanced logging for debugging
- Performance testing capabilities

**Section sources**
- [README.md](file://README.md#L0-L735)
- [docker-compose.yml](file://docker-compose.yml#L0-L387)

## Docker Compose Configurations

### Overview of Docker Compose Files

The Domainr platform uses multiple Docker Compose files for different environments and purposes:

**Main Configuration Files:**
- `docker-compose.yml`: Base production configuration
- `docker-compose.dev.yml`: Development overrides
- `services/admin/docker-compose.production.yml`: Admin Panel production
- `services/admin/docker-compose.simple.yml`: Simple Admin Panel development
- `services/worker/docker-compose.test.yml`: Worker service testing

### Production Configuration (docker-compose.yml)

The main `docker-compose.yml` file defines the complete production environment with all services:

```yaml
version: '3.8'

services:
  nginx: # Reverse proxy with SSL termination
  web-app: # Web application service
  worker: # Worker service with resource-intensive operations
  domain-seeder: # Domain discovery and seeding service
  scylla: # ScyllaDB database
  mariadb: # MariaDB database
  redis: # Redis cache and queue
  manticore: # Manticore Search
  browserless: # Browserless for screenshots
  prometheus: # Monitoring
  grafana: # Visualization
```

Key features:
- **Service Dependencies**: Proper startup order with `depends_on`
- **Resource Management**: CPU and memory limits/reservations
- **Health Checks**: Built-in health checks for service monitoring
- **Volume Management**: Persistent storage for databases and logs
- **Network Isolation**: Custom network for service communication

### Development Configuration (docker-compose.dev.yml)

The development configuration extends the production configuration with development-friendly settings:

```yaml
# Development override for domain-seeder service
# Usage: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

services:
  domain-seeder:
    build:
      target: development
    ports:
      - "${SEEDER_DEV_PORT:-3004}:3004"
    environment:
      - NODE_ENV=development
      - LOG_LEVEL=debug
    volumes:
      - ./services/domain-seeder/src:/app/services/domain-seeder/src
      - ./shared/src:/app/shared/src
    healthcheck:
      disable: true
```

Key development features:
- **Code Mounting**: Source code mounted for hot reloading
- **Port Exposure**: All service ports exposed for direct access
- **Development Logging**: Debug-level logging enabled
- **Disabled Health Checks**: Prevents restarts during development
- **Reduced Resource Usage**: Lower memory and CPU requirements

### Admin Panel Configurations

The Admin Panel has two dedicated Docker Compose configurations:

**Production Configuration:**
- Comprehensive monitoring stack
- Centralized logging with Fluentd
- Backup and recovery system
- Security hardening
- High availability configuration

**Simple Development Configuration:**
```yaml
version: '3.8'
services:
  admin:
    build: .
    ports:
      - "3004:3004"
    environment:
      - NODE_ENV=development
      - PORT=3004
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next
    command: pnpm dev
```

Features:
- Simple single-service configuration
- Hot reloading of code
- Direct port access
- Development environment variables
- Easy setup for local development

### Testing Configuration

The worker service includes a dedicated testing configuration:

**Key Testing Features:**
- Isolated database instances
- Mock external services
- Enhanced logging for debugging
- Performance testing capabilities
- Integration test support

**Section sources**
- [docker-compose.yml](file://docker-compose.yml#L0-L387)
- [docker-compose.dev.yml](file://docker-compose.dev.yml#L0-L121)
- [services/admin/docker-compose.production.yml](file://services/admin/docker-compose.production.yml#L0-L340)
- [services/admin/docker-compose.simple.yml](file://services/admin/docker-compose.simple.yml#L0-L15)

## Service Deployment Instructions

### Complete System Deployment

To deploy the complete Domainr system:

1. **Initial Setup**
   ```bash
   # Clone repository
   git clone <repository-url>
   cd domainr
   
   # Copy environment file
   cp .env.example .env
   # Edit .env with your configuration
   ```

2. **Build and Start Services**
   ```bash
   # Build all images and start services
   docker-compose up -d
   
   # Or use the just command runner
   just deploy
   ```

3. **Initialize Databases**
   ```bash
   # Wait for databases to start
   sleep 60
   
   # Run database initialization
   just db-init
   ```

4. **Verify Deployment**
   ```bash
   # Check service status
   docker-compose ps
   
   # Check logs
   docker-compose logs
   
   # Run health checks
   just health
   ```

### Individual Service Deployment

Services can be deployed individually for targeted updates or troubleshooting:

**Web Application:**
```bash
# Deploy only web application
docker-compose up -d web-app

# Verify web application
curl http://localhost/health
```

**Worker Service:**
```bash
# Deploy only worker service
docker-compose up -d worker

# Monitor worker logs
docker-compose logs -f worker
```

**Domain Seeder:**
```bash
# Deploy only domain seeder
docker-compose up -d domain-seeder

# Check seeder status
curl http://localhost:3004/health/live
```

**Database Services:**
```bash
# Start only database services
docker-compose up -d scylla mariadb redis manticore

# Initialize databases
just db-init
```

**Monitoring Services:**
```bash
# Start monitoring stack
docker-compose up -d prometheus grafana

# Access monitoring
# Prometheus: http://localhost:9090
# Grafana: http://localhost:3001 (admin/admin)
```

### Admin Panel Deployment

The Admin Panel can be deployed separately:

```bash
# Deploy Admin Panel
cd services/admin
docker-compose -f docker-compose.production.yml up -d

# Run health check
./scripts/production-health-check.sh

# Monitor logs
docker-compose -f docker-compose.production.yml logs -f admin-panel
```

### Rolling Updates

For zero-downtime deployments:

```bash
# Update specific service
docker-compose up -d --no-deps --force-recreate web-app

# Verify health
./scripts/production-health-check.sh

# Rollback if needed
docker-compose down
git checkout previous-version
docker-compose up -d
```

### Backup and Recovery

Regular backups are essential for production systems:

```bash
# Create backup
just backup

# Or use backup script directly
./scripts/backup.sh

# Disaster recovery
./backup/restore-manager.js --backup-id=backup-2024-01-01
```

**Section sources**
- [README.md](file://README.md#L0-L735)
- [services/admin/DEPLOYMENT.md](file://services/admin/DEPLOYMENT.md#L0-L463)

## Deployment Topology

```mermaid
graph TB
subgraph "External Access"
Client[Internet Client]
DNS[DNS Service]
end
subgraph "Reverse Proxy"
Nginx[Nginx Reverse Proxy]
SSL[SSL/TLS Termination]
end
subgraph "Application Services"
WebApp[Web Application]
AdminPanel[Admin Panel]
Worker[Worker Service]
Seeder[Domain Seeder]
end
subgraph "Data Storage"
Scylla[ScyllaDB]
MariaDB[MariaDB]
Redis[Redis Cache]
Manticore[Manticore Search]
end
subgraph "Monitoring"
Prometheus[Prometheus]
Grafana[Grafana]
Alertmanager[Alertmanager]
Fluentd[Fluentd Logging]
end
subgraph "External Services"
Browserless[Browserless]
ImageProxy[Image Proxy]
end
Client --> DNS
DNS --> Nginx
Nginx --> WebApp
Nginx --> AdminPanel
WebApp --> Worker
WebApp --> Seeder
Worker --> Scylla
Worker --> MariaDB
Worker --> Redis
Worker --> Manticore
Seeder --> Scylla
Seeder --> MariaDB
Seeder --> Redis
Seeder --> Manticore
AdminPanel --> Scylla
AdminPanel --> MariaDB
AdminPanel --> Redis
WebApp --> Browserless
WebApp --> ImageProxy
WebApp --> Prometheus
Worker --> Prometheus
Seeder --> Prometheus
AdminPanel --> Prometheus
Prometheus --> Grafana
Prometheus --> Alertmanager
WebApp --> Fluentd
Worker --> Fluentd
Seeder --> Fluentd
AdminPanel --> Fluented
style Client fill:#f9f,stroke:#333
style DNS fill:#f9f,stroke:#333
style Nginx fill:#bbf,stroke:#333
style SSL fill:#bbf,stroke:#333
style WebApp fill:#9f9,stroke:#333
style AdminPanel fill:#9f9,stroke:#333
style Worker fill:#9f9,stroke:#333
style Seeder fill:#9f9,stroke:#333
style Scylla fill:#f96,stroke:#333
style MariaDB fill:#f96,stroke:#333
style Redis fill:#f96,stroke:#333
style Manticore fill:#f96,stroke:#333
style Prometheus fill:#f66,stroke:#333
style Grafana fill:#f66,stroke:#333
style Alertmanager fill:#f66,stroke:#333
style Fluentd fill:#f66,stroke:#333
style Browserless fill:#69f,stroke:#333
style ImageProxy fill:#69f,stroke:#333
```

**Diagram sources**
- [docker-compose.yml](file://docker-compose.yml#L0-L387)
- [README.md](file://README.md#L0-L735)

## Performance Considerations

### Resource Allocation

Proper resource allocation is critical for system performance and stability. The Docker Compose configuration includes resource limits and reservations for each service:

**Web Application:**
- Memory: 1GB limit, 512MB reservation
- CPU: 1.0 limit, 0.5 reservation
- Suitable for handling API requests and serving web content

**Worker Service:**
- Memory: 4GB limit, 2GB reservation
- CPU: 4.0 limit, 2.0 reservation
- Resource-intensive operations like crawling and ranking calculations

**Domain Seeder:**
- Memory: 2GB limit, 1GB reservation
- CPU: 2.0 limit, 1.0 reservation
- Domain discovery and seeding operations

**Database Services:**
- ScyllaDB: 3GB limit, 2GB reservation
- MariaDB: 1GB limit, 512MB reservation
- Redis: 512MB limit, 256MB reservation
- Manticore: 1GB limit, 512MB reservation

### Performance Optimization Strategies

**Caching:**
- Redis for session storage and frequently accessed data
- Manticore Search for full-text search results
- Browserless caching for screenshots
- CDN for static assets

**Database Optimization:**
- Proper indexing in ScyllaDB and MariaDB
- Query optimization and connection pooling
- Regular maintenance and compaction
- Appropriate data partitioning

**Crawling Efficiency:**
- Rate limiting to prevent overwhelming target sites
- Concurrent request management
- Intelligent retry mechanisms
- Proxy rotation for IP diversity

**Monitoring and Scaling:**
- Prometheus metrics for performance monitoring
- Grafana dashboards for visualization
- Alertmanager for performance degradation alerts
- Horizontal scaling of worker services

### Scaling Strategies

**Horizontal Scaling:**
```bash
# Scale worker service
docker-compose up -d --scale worker=3

# Scale web application
docker-compose up -d --scale web-app=2
```

**Vertical Scaling:**
- Increase resource limits in docker-compose.yml
- Upgrade host machine resources
- Optimize service configurations

**Auto-scaling:**
- Implement Kubernetes for auto-scaling
- Use monitoring metrics to trigger scaling
- Configure load balancers for traffic distribution

### Performance Testing

Regular performance testing is essential:

```bash
# Run load tests
npm run test:load

# Monitor resource usage
docker stats

# Analyze performance metrics
curl http://localhost:9090/api/v1/query?query=nodejs_heap_size_used_bytes
```

**Section sources**
- [docker-compose.yml](file://docker-compose.yml#L0-L387)
- [README.md](file://README.md#L0-L735)

## Troubleshooting

### Common Issues and Solutions

**Service Won't Start:**
```bash
# Check logs for specific service
docker-compose logs [service-name]

# Verify environment variables
docker exec [container-name] env | grep -E "(DATABASE|REDIS)"

# Check resource usage
docker stats
```

**Database Connection Errors:**
```bash
# Restart database services
docker-compose restart scylla mariadb redis

# Check database logs
docker-compose logs scylla
docker-compose logs mariadb

# Verify database connectivity
docker exec web-app node -e "console.log('DB test')"
```

**SSL Certificate Issues:**
```bash
# Check certificate validity
openssl x509 -in nginx/ssl/cert.pem -text -noout

# Test SSL configuration
openssl s_client -connect localhost:443

# Renew certificate
certbot renew --nginx
```

**High Memory Usage:**
```bash
# Check memory usage
docker stats

# Analyze heap dump
docker exec worker node --inspect=0.0.0.0:9229 server.js

# Restart with memory limit
docker-compose up -d --force-recreate worker
```

### Health Check Failures

When health checks fail:

```bash
# Check service status
docker-compose ps

# Perform detailed health checks
just deploy-health

# View detailed logs
docker-compose logs -f

# Check specific health endpoints
curl http://localhost/health
curl http://localhost:3004/health/live
```

### Log Analysis

Effective log analysis is crucial for troubleshooting:

```bash
# View application logs
docker-compose logs -f web-app

# Search for errors
docker-compose logs | grep -i error

# Monitor real-time logs
docker-compose logs -f | grep -v "GET /health"

# Analyze structured logs
docker-compose logs --json | jq '.'
```

### Performance Issues

For performance-related problems:

```bash
# Check resource usage
just top

# Scale services
docker-compose up -d --scale worker=2

# Monitor performance metrics
curl http://localhost:9090/api/v1/query?query=rate(http_requests_total[5m])

# Analyze slow queries
docker exec worker node scripts/analyze-queries.js
```

### Maintenance Procedures

Regular maintenance ensures system stability:

**Updates:**
```bash
# Pull latest changes
git pull

# Rebuild and deploy
just build-no-cache
just deploy
```

**Cleanup:**
```bash
# Clean unused resources
just clean

# Full cleanup (removes all data)
just clean-all
```

**Database Maintenance:**
```bash
# Check migration status
just db-status

# Run new migrations
just db-migrate
```

**Section sources**
- [README.md](file://README.md#L0-L735)
- [services/admin/DEPLOYMENT.md](file://services/admin/DEPLOYMENT.md#L0-L463)