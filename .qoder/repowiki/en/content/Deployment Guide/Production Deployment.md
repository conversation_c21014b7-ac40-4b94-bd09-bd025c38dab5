# Production Deployment

<cite>
**Referenced Files in This Document**   
- [docker-compose.yml](file://docker-compose.yml)
- [docker-compose.dev.yml](file://docker-compose.dev.yml)
- [services/admin/docker-compose.production.yml](file://services/admin/docker-compose.production.yml)
- [scripts/deploy.sh](file://scripts/deploy.sh)
- [nginx/conf.d/default.conf](file://nginx/conf.d/default.conf)
- [nginx/nginx.conf](file://nginx/nginx.conf)
- [services/worker/k8s/deployment.yaml](file://services/worker/k8s/deployment.yaml)
- [services/worker/k8s/hpa.yaml](file://services/worker/k8s/hpa.yaml)
- [services/worker/k8s/service.yaml](file://services/worker/k8s/service.yaml)
- [services/worker/k8s/configmap.yaml](file://services/worker/k8s/configmap.yaml)
- [services/worker/k8s/secret.yaml](file://services/worker/k8s/secret.yaml)
- [services/worker/k8s/rbac.yaml](file://services/worker/k8s/rbac.yaml)
- [services/worker/k8s/network-policy.yaml](file://services/worker/k8s/network-policy.yaml)
- [services/worker/k8s/monitoring.yaml](file://services/worker/k8s/monitoring.yaml)
- [services/worker/Dockerfile](file://services/worker/Dockerfile)
- [services/admin/Dockerfile](file://services/admin/Dockerfile)
- [services/web-app/Dockerfile](file://services/web-app/Dockerfile)
- [services/domain-seeder/Dockerfile](file://services/domain-seeder/Dockerfile)
- [services/admin/healthcheck.js](file://services/admin/healthcheck.js)
- [services/worker/healthcheck.js](file://services/worker/healthcheck.js)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Architecture Overview](#architecture-overview)
3. [Docker Compose Configuration](#docker-compose-configuration)
4. [Deployment Workflow](#deployment-workflow)
5. [Nginx Reverse Proxy and SSL Configuration](#nginx-reverse-proxy-and-ssl-configuration)
6. [Kubernetes Deployment](#kubernetes-deployment)
7. [Environment and Secret Management](#environment-and-secret-management)
8. [Health Checks and Monitoring](#health-checks-and-monitoring)
9. [Scalability and High Availability](#scalability-and-high-availability)
10. [Disaster Recovery](#disaster-recovery)
11. [Security Hardening](#security-hardening)

## Introduction
This document provides comprehensive guidance for deploying the domainr system in production environments. It covers architectural differences between development and production setups, deployment workflows, container orchestration strategies, and infrastructure-specific configurations for both Docker and Kubernetes environments. The documentation focuses on ensuring reliability, security, and scalability of the domain ranking platform.

## Architecture Overview

```mermaid
graph TB
subgraph "Edge Layer"
Nginx[Nginx Reverse Proxy]
SSL[SSL Termination]
end
subgraph "Application Layer"
WebApp[Web Application]
Worker[Worker Service]
Seeder[Domain Seeder]
Admin[Admin Panel]
end
subgraph "Data Layer"
Scylla[ScyllaDB]
MariaDB[MariaDB]
Redis[Redis Cache]
Manticore[Manticore Search]
end
subgraph "Infrastructure"
Browserless[Browserless]
Prometheus[Prometheus]
Grafana[Grafana]
Alertmanager[Alertmanager]
Fluentd[Fluentd]
end
Nginx --> WebApp
Nginx --> Admin
WebApp --> Worker
WebApp --> Seeder
Worker --> Scylla
Worker --> MariaDB
Worker --> Redis
Worker --> Manticore
Seeder --> Scylla
Seeder --> MariaDB
Seeder --> Redis
Seeder --> Manticore
WebApp --> Scylla
WebApp --> MariaDB
WebApp --> Redis
WebApp --> Manticore
Admin --> Redis
Admin --> Prometheus
Prometheus --> Grafana
Prometheus --> Alertmanager
Fluentd --> Admin
Browserless --> Worker
style Nginx fill:#4CAF50,stroke:#388E3C
style WebApp fill:#2196F3,stroke:#1976D2
style Worker fill:#2196F3,stroke:#1976D2
style Seeder fill:#2196F3,stroke:#1976D2
style Admin fill:#2196F3,stroke:#1976D2
style Scylla fill:#FF9800,stroke:#F57C00
style MariaDB fill:#FF9800,stroke:#F57C00
style Redis fill:#FF9800,stroke:#F57C00
style Manticore fill:#FF9800,stroke:#F57C00
style Browserless fill:#9C27B0,stroke:#7B1FA2
style Prometheus fill:#F44336,stroke:#D32F2F
style Grafana fill:#F44336,stroke:#D32F2F
style Alertmanager fill:#F44336,stroke:#D32F2F
style Fluentd fill:#607D8B,stroke:#455A64
```

**Diagram sources**
- [docker-compose.yml](file://docker-compose.yml#L5-L388)
- [services/admin/docker-compose.production.yml](file://services/admin/docker-compose.production.yml#L5-L341)

**Section sources**
- [docker-compose.yml](file://docker-compose.yml#L1-L388)
- [services/admin/docker-compose.production.yml](file://services/admin/docker-compose.production.yml#L1-L341)

## Docker Compose Configuration

The domainr system utilizes multiple Docker Compose configuration files to manage different deployment scenarios. The primary production configuration is defined in `docker-compose.yml`, while environment-specific overrides are provided through additional files.

### Production vs Development Configuration

The production and development configurations differ significantly in resource allocation, service exposure, and operational parameters:

```mermaid
flowchart TD
A[Configuration Type] --> B[Production]
A --> C[Development]
B --> D["Resource Limits: Higher (4G RAM, 4 CPUs for worker)"]
B --> E["Health Checks: Enabled with 60s start period"]
B --> F["Restart Policy: unless-stopped"]
B --> G["Logging: JSON format with size limits"]
B --> H["Security: Environment variables via .env files"]
B --> I["Ports: Minimal exposure (only nginx and databases)"]
C --> J["Resource Limits: Lower (1G RAM, 1 CPU)"]
C --> K["Health Checks: Disabled for faster startup"]
C --> L["Restart Policy: unless-stopped"]
C --> M["Logging: Debug level output"]
C --> N["Security: Direct environment variable injection"]
C --> O["Ports: Full service exposure for debugging"]
```

**Diagram sources**
- [docker-compose.yml](file://docker-compose.yml#L5-L388)
- [docker-compose.dev.yml](file://docker-compose.dev.yml#L5-L122)

**Section sources**
- [docker-compose.yml](file://docker-compose.yml#L1-L388)
- [docker-compose.dev.yml](file://docker-compose.dev.yml#L1-L122)

### Service-Specific Configuration

Each service in the domainr stack has tailored configuration parameters optimized for production operation:

#### Worker Service Configuration
The worker service is configured with substantial resources to handle intensive crawling and ranking operations:

```yaml
worker:
  deploy:
    resources:
      limits:
        memory: 4G
        cpus: '4.0'
      reservations:
        memory: 2G
        cpus: '2.0'
```

#### Database Services Configuration
Database services are configured with appropriate resource limits and persistent storage:

```yaml
scylla:
  deploy:
    resources:
      limits:
        memory: 3G
        cpus: '2.0'
  volumes:
    - scylla_data:/var/lib/scylla

mariadb:
  deploy:
    resources:
      limits:
        memory: 1G
        cpus: '1.0'
  volumes:
    - mariadb_data:/var/lib/mysql
```

**Section sources**
- [docker-compose.yml](file://docker-compose.yml#L100-L388)

## Deployment Workflow

The deployment process is automated through the `deploy.sh` script, which orchestrates the entire deployment lifecycle from prerequisites checking to post-deployment validation.

```mermaid
sequenceDiagram
participant User
participant DeployScript
participant Docker
participant Services
User->>DeployScript : Execute deploy.sh
DeployScript->>DeployScript : Set environment variables
DeployScript->>DeployScript : Define color-coded logging
DeployScript->>DeployScript : Check prerequisites
alt Docker not running
DeployScript-->>User : ERROR : Docker not running
stop
end
alt docker-compose not available
DeployScript-->>User : ERROR : docker-compose not installed
stop
end
DeployScript->>DeployScript : Create SSL certificates if needed
DeployScript->>DeployScript : Create backup if enabled
DeployScript->>DeployScript : Build Docker images
DeployScript->>Services : Start database services
Services-->>DeployScript : Databases started
DeployScript->>DeployScript : Run database migrations
DeployScript->>Services : Start application services
DeployScript->>DeployScript : Perform health checks
alt Health check fails
DeployScript-->>User : ERROR : Health check failed
stop
end
DeployScript->>User : Deployment completed successfully
```

**Diagram sources**
- [scripts/deploy.sh](file://scripts/deploy.sh#L1-L250)

**Section sources**
- [scripts/deploy.sh](file://scripts/deploy.sh#L1-L250)

### Pre-Deployment Checks

The deployment script performs several critical pre-deployment checks to ensure system readiness:

- **Docker Availability**: Verifies that Docker daemon is running and accessible
- **docker-compose Installation**: Confirms docker-compose CLI is available
- **Environment Configuration**: Checks for the presence of `.env` file
- **SSL Certificates**: Validates existence of SSL certificates for HTTPS

These checks prevent deployment failures due to missing prerequisites and provide early feedback on configuration issues.

### Service Orchestration

The deployment script follows a strict service startup order to ensure proper dependencies are met:

1. **Database Services**: Scylla, MariaDB, Redis, and Manticore are started first
2. **Migration Execution**: Database schema migrations are applied after databases are ready
3. **Application Services**: Worker, web-app, and other application components are started
4. **Reverse Proxy**: Nginx is started last to ensure backend services are available

This orchestration ensures that services are not started before their dependencies are ready, preventing cascading failures.

### Post-Deployment Validation

After services are deployed, comprehensive health checks validate the system's operational status:

- **Web Application**: Checks `/health` endpoint on localhost
- **ScyllaDB**: Validates API endpoint at port 10000
- **Service Status**: Displays all running containers and their status
- **Access Information**: Provides URLs for web application, monitoring tools, and APIs

The health check function implements a retry mechanism with configurable timeout, ensuring transient startup delays don't cause deployment failures.

## Nginx Reverse Proxy and SSL Configuration

The Nginx reverse proxy serves as the entry point for all external traffic, providing SSL termination, load balancing, and security features.

### Nginx Configuration Structure

```mermaid
graph TD
A[Nginx Configuration] --> B[Main Configuration]
A --> C[Site Configuration]
B --> D[nginx.conf]
C --> E[default.conf]
D --> F["Events: worker_connections, use"]
D --> G["HTTP: include mime.types, default_type"]
D --> H["HTTP: server_tokens off"]
D --> I["HTTP: client_max_body_size 100M"]
E --> J["Server Block: Listen 80 & 443"]
E --> K["Server Name: localhost"]
E --> L["SSL Certificate: ssl/cert.pem"]
E --> M["SSL Key: ssl/key.pem"]
E --> N["Location /: Proxy to web-app:3000"]
E --> O["Location /api/: Proxy to appropriate services"]
E --> P["Location /admin: Proxy to admin-panel"]
```

**Diagram sources**
- [nginx/nginx.conf](file://nginx/nginx.conf)
- [nginx/conf.d/default.conf](file://nginx/conf.d/default.conf)

**Section sources**
- [nginx/nginx.conf](file://nginx/nginx.conf)
- [nginx/conf.d/default.conf](file://nginx/conf.d/default.conf)

### SSL Termination

The Nginx configuration enables SSL termination, decrypting HTTPS traffic before forwarding it to backend services:

```nginx
server {
    listen 443 ssl;
    server_name localhost;
    
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;
    
    location / {
        proxy_pass http://web-app:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

During deployment, if SSL certificates are not present, the `deploy.sh` script generates self-signed certificates valid for one year, enabling HTTPS functionality even in development environments.

## Kubernetes Deployment

The worker service includes comprehensive Kubernetes configuration for production deployment in container orchestration environments.

### Kubernetes Configuration Overview

```mermaid
graph TD
A[Kubernetes Configuration] --> B[Base Configuration]
A --> C[Production Overlay]
A --> D[Staging Overlay]
B --> E[configmap.yaml]
B --> F[deployment.yaml]
B --> G[hpa.yaml]
B --> H[service.yaml]
B --> I[secret.yaml]
B --> J[rbac.yaml]
B --> K[network-policy.yaml]
B --> L[monitoring.yaml]
B --> M[kustomization.yaml]
C --> N[configmap-patch.yaml]
C --> O[deployment-patch.yaml]
C --> P[hpa-patch.yaml]
C --> Q[kustomization.yaml]
D --> R[configmap-patch.yaml]
D --> S[deployment-patch.yaml]
D --> T[kustomization.yaml]
```

**Section sources**
- [services/worker/k8s](file://services/worker/k8s)
- [services/worker/k8s/overlays/production](file://services/worker/k8s/overlays/production)
- [services/worker/k8s/overlays/staging](file://services/worker/k8s/overlays/staging)

### Deployment Configuration

The Kubernetes deployment configuration for the worker service includes production-grade settings:

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: worker
spec:
  replicas: 3
  selector:
    matchLabels:
      app: worker
  template:
    metadata:
      labels:
        app: worker
    spec:
      containers:
      - name: worker
        image: domainr/worker:latest
        ports:
        - containerPort: 3001
        envFrom:
        - configMapRef:
            name: worker-config
        - secretRef:
            name: worker-secrets
        resources:
          limits:
            memory: "4Gi"
            cpu: "4000m"
          requests:
            memory: "2Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /health/live
            port: 3001
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 3001
          initialDelaySeconds: 30
          periodSeconds: 10
```

**Section sources**
- [services/worker/k8s/deployment.yaml](file://services/worker/k8s/deployment.yaml)
- [services/worker/k8s/overlays/production/deployment-patch.yaml](file://services/worker/k8s/overlays/production/deployment-patch.yaml)

### Horizontal Pod Autoscaler

The worker service is configured with horizontal pod autoscaling to automatically adjust capacity based on CPU utilization:

```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: worker-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: worker
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
    scaleUp:
      stabilizationWindowSeconds: 60
```

**Section sources**
- [services/worker/k8s/hpa.yaml](file://services/worker/k8s/hpa.yaml)
- [services/worker/k8s/overlays/production/hpa-patch.yaml](file://services/worker/k8s/overlays/production/hpa-patch.yaml)

### Service Configuration

The Kubernetes service exposes the worker deployment internally within the cluster:

```yaml
apiVersion: v1
kind: Service
metadata:
  name: worker-service
spec:
  selector:
    app: worker
  ports:
    - protocol: TCP
      port: 3001
      targetPort: 3001
  type: ClusterIP
```

**Section sources**
- [services/worker/k8s/service.yaml](file://services/worker/k8s/service.yaml)

### Configuration Management

Kubernetes configuration uses ConfigMap and Secret resources to manage environment-specific settings:

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: worker-config
data:
  NODE_ENV: "production"
  SERVICE_NAME: "worker"
  SCYLLA_KEYSPACE: "domain_ranking"
  LOG_LEVEL: "info"
  CRAWL_CONCURRENT_REQUESTS: "5"
  RANKING_WEIGHT_PERFORMANCE: "0.25"
```

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: worker-secrets
type: Opaque
data:
  MARIA_PASSWORD: <base64-encoded-password>
  REDIS_PASSWORD: <base64-encoded-password>
  OPENAI_API_KEYS: <base64-encoded-keys>
  CZDS_USERNAME: <base64-encoded-username>
  CZDS_PASSWORD: <base64-encoded-password>
```

**Section sources**
- [services/worker/k8s/configmap.yaml](file://services/worker/k8s/configmap.yaml)
- [services/worker/k8s/secret.yaml](file://services/worker/k8s/secret.yaml)

### Security Configuration

The deployment includes RBAC and network policies to enforce security best practices:

```yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: worker-role
rules:
- apiGroups: [""]
  resources: ["pods", "services"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get"]
```

```yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: worker-network-policy
spec:
  podSelector:
    matchLabels:
      app: worker
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: web-app
    ports:
    - protocol: TCP
      port: 3001
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: scylla
    ports:
    - protocol: TCP
      port: 9042
```

**Section sources**
- [services/worker/k8s/rbac.yaml](file://services/worker/k8s/rbac.yaml)
- [services/worker/k8s/network-policy.yaml](file://services/worker/k8s/network-policy.yaml)

## Environment and Secret Management

The domainr system employs a comprehensive approach to environment and secret management across different deployment environments.

### Environment Variable Strategy

The system uses a hierarchical approach to environment variable management:

```mermaid
flowchart TD
A[Environment Variable Sources] --> B[Default Values in Code]
A --> C[.env File]
A --> D[Docker Compose Environment Section]
A --> E[Kubernetes ConfigMap/Secret]
A --> F[Command Line Overrides]
B --> G["Fallback when no other value provided"]
C --> H["Development and testing configuration"]
D --> I["Docker Compose deployment settings"]
E --> J["Kubernetes production configuration"]
F --> K["One-time overrides and debugging"]
```

**Section sources**
- [docker-compose.yml](file://docker-compose.yml#L5-L388)
- [services/worker/k8s/configmap.yaml](file://services/worker/k8s/configmap.yaml)
- [services/worker/k8s/secret.yaml](file://services/worker/k8s/secret.yaml)

### Sensitive Data Handling

Sensitive information is managed through multiple mechanisms depending on the deployment environment:

#### Docker Environment
- **Database passwords**: Provided via `.env` file or environment variables
- **API keys**: Stored in environment variables with restricted access
- **SSL certificates**: Mounted as volumes from secure locations

#### Kubernetes Environment
- **Secrets**: Encrypted storage for passwords, API keys, and credentials
- **RBAC**: Role-based access control limits who can access secrets
- **Network Policies**: Restrict network access to sensitive services

The system follows the principle of least privilege, ensuring that each component only has access to the credentials it requires for operation.

## Health Checks and Monitoring

The domainr system implements comprehensive health checking and monitoring to ensure reliability and facilitate troubleshooting.

### Health Check Implementation

Each service includes a health check endpoint and configuration:

```javascript
// healthcheck.js
const axios = require('axios');

async function checkHealth() {
  try {
    // Check database connections
    await axios.get('http://mariadb:3306/health');
    await axios.get('http://scylla:9042/health');
    await axios.get('http://redis:6379/health');
    
    // Check internal service health
    await axios.get('http://localhost:3001/api/health');
    
    console.log('Health check passed');
    process.exit(0);
  } catch (error) {
    console.error('Health check failed:', error.message);
    process.exit(1);
  }
}

checkHealth();
```

**Section sources**
- [services/admin/healthcheck.js](file://services/admin/healthcheck.js)
- [services/worker/healthcheck.js](file://services/worker/healthcheck.js)

### Docker Health Check Configuration

Services are configured with Docker health checks to automatically detect and recover from failures:

```yaml
healthcheck:
  test: ["CMD", "curl", "-f", "http://localhost:3004/health/live"]
  interval: 30s
  timeout: 10s
  retries: 3
  start_period: 60s
```

The `start_period` allows services time to initialize before health checks begin, preventing premature restarts during startup.

### Monitoring Stack

The system includes a comprehensive monitoring stack with Prometheus, Grafana, and Alertmanager:

```mermaid
graph TD
A[Application Metrics] --> B[Prometheus]
C[Service Logs] --> D[Fluentd]
D --> E[Elasticsearch]
E --> F[Grafana]
B --> F
B --> G[Alertmanager]
G --> H[Notification Channels]
style A fill:#2196F3,stroke:#1976D2
style B fill:#F44336,stroke:#D32F2F
style C fill:#4CAF50,stroke:#388E3C
style D fill:#607D8B,stroke:#455A64
style E fill:#FF9800,stroke:#F57C00
style F fill:#F44336,stroke:#D32F2F
style G fill:#F44336,stroke:#D32F2F
style H fill:#9C27B0,stroke:#7B1FA2
```

**Section sources**
- [monitoring/prometheus.yml](file://monitoring/prometheus.yml)
- [services/admin/docker-compose.production.yml](file://services/admin/docker-compose.production.yml#L5-L341)

## Scalability and High Availability

The domainr architecture is designed for horizontal scalability and high availability in production environments.

### Horizontal Scaling Strategy

```mermaid
graph TD
A[Incoming Traffic] --> B[Nginx Load Balancer]
B --> C[Web App Instance 1]
B --> D[Web App Instance 2]
B --> E[Web App Instance N]
F[Task Queue] --> G[Worker Instance 1]
F --> H[Worker Instance 2]
F --> I[Worker Instance N]
C --> J[Shared Data Layer]
D --> J
E --> J
G --> J
H --> J
I --> J
J --> K[ScyllaDB Cluster]
J --> L[MariaDB Cluster]
J --> M[Redis Cluster]
J --> N[Manticore Cluster]
style B fill:#4CAF50,stroke:#388E3C
style C fill:#2196F3,stroke:#1976D2
style D fill:#2196F3,stroke:#1976D2
style E fill:#2196F3,stroke:#1976D2
style F fill:#FF9800,stroke:#F57C00
style G fill:#2196F3,stroke:#1976D2
style H fill:#2196F3,stroke:#1976D2
style I fill:#2196F3,stroke:#1976D2
style K fill:#9C27B0,stroke:#7B1FA2
style L fill:#9C27B0,stroke:#7B1FA2
style M fill:#9C27B0,stroke:#7B1FA2
style N fill:#9C27B0,stroke:#7B1FA2
```

**Section sources**
- [docker-compose.yml](file://docker-compose.yml#L5-L388)
- [services/worker/k8s/deployment.yaml](file://services/worker/k8s/deployment.yaml)

### Data Layer High Availability

The data layer components are configured for high availability:

- **ScyllaDB**: Distributed NoSQL database with automatic sharding and replication
- **MariaDB**: Configured with potential for master-slave replication
- **Redis**: Used for caching and queuing with persistence enabled
- **Manticore**: Distributed search engine with replication capabilities

The architecture separates read and write concerns, allowing independent scaling of different data access patterns.

### Worker Service Scalability

The worker service is designed to scale horizontally to handle increased crawling and ranking workloads:

- **Task Queue**: Redis-based queue distributes work across worker instances
- **Stateless Design**: Workers do not maintain local state, enabling easy scaling
- **Database Coordination**: Shared databases coordinate work and prevent duplication
- **Auto-scaling**: Kubernetes HPA automatically adjusts worker count based on CPU usage

This design allows the system to handle variable workloads efficiently by adding or removing worker instances as needed.

## Disaster Recovery

The domainr system includes comprehensive disaster recovery procedures to ensure business continuity.

### Backup Strategy

The system implements a multi-layered backup strategy:

```mermaid
flowchart TD
A[Backup Types] --> B[Database Backups]
A --> C[Configuration Backups]
A --> D[Application Code Backups]
A --> E[SSL Certificate Backups]
B --> F["ScyllaDB: Snapshot-based backups"]
B --> G["MariaDB: Logical backups with mysqldump"]
B --> H["Redis: RDB snapshots with appendonly"]
B --> I["Manticore: Configuration and index backups"]
C --> J["Docker Compose files"]
C --> K["Nginx configuration"]
C --> L["Environment files"]
D --> M["Version-controlled application code"]
D --> N["Docker images in registry"]
E --> O["SSL private keys and certificates"]
style B fill:#FF9800,stroke:#F57C00
style C fill:#2196F3,stroke:#1976D2
style D fill:#4CAF50,stroke:#388E3C
style E fill:#F44336,stroke:#D32F2F
```

**Section sources**
- [scripts/backup.sh](file://scripts/backup.sh)
- [services/admin/docker-compose.production.yml](file://services/admin/docker-compose.production.yml#L5-L341)

### Recovery Procedures

The deployment script includes rollback functionality to recover from failed deployments:

```bash
rollback() {
    warn "Rolling back deployment..."
    docker-compose down
    # Restore from backup if needed
    error "Rollback completed"
}
```

The system also implements automated recovery through Docker's restart policies and Kubernetes liveness probes, which automatically restart failed containers.

### Data Durability

Data durability is ensured through multiple mechanisms:

- **Persistent Volumes**: All databases use Docker volumes or host-mounted directories
- **Write-Ahead Logging**: Redis and databases use write-ahead logs for crash recovery
- **Replication**: Critical data is replicated across multiple instances
- **Regular Backups**: Automated backup scripts run on scheduled intervals

These measures ensure that data is protected against hardware failures and other disruptions.

## Security Hardening

The domainr system implements multiple security hardening measures to protect against common threats.

### Container Security

```mermaid
flowchart TD
A[Container Security] --> B[Minimal Base Images]
A --> C[Non-Root User Execution]
A --> D[Read-Only Filesystems]
A --> E[Resource Limits]
A --> F[Network Segmentation]
A --> G[Regular Updates]
B --> H["Alpine Linux base images"]
C --> I["Running as non-root user where possible"]
D --> J["Mounting configuration as read-only"]
E --> K["CPU and memory limits to prevent DoS"]
F --> L["Dedicated networks for different service tiers"]
G --> M["Regular base image updates"]
```

**Section sources**
- [services/worker/Dockerfile](file://services/worker/Dockerfile)
- [services/admin/Dockerfile](file://services/admin/Dockerfile)
- [services/web-app/Dockerfile](file://services/web-app/Dockerfile)
- [services/domain-seeder/Dockerfile](file://services/domain-seeder/Dockerfile)

### Network Security

The system implements network security through:

- **Network Segmentation**: Separate Docker networks for different service tiers
- **Firewall Rules**: Container-level firewall rules restrict unnecessary access
- **TLS Encryption**: All external traffic uses HTTPS with TLS
- **Internal Encryption**: Sensitive internal communications use encrypted channels

The Nginx reverse proxy acts as a security gateway, filtering and inspecting all incoming traffic before it reaches application services.

### Application Security

Application-level security measures include:

- **Input Validation**: All user inputs are validated and sanitized
- **Authentication and Authorization**: Role-based access control for administrative functions
- **Rate Limiting**: Protection against brute force and denial-of-service attacks
- **Security Headers**: HTTP headers to prevent common web vulnerabilities
- **Logging and Monitoring**: Comprehensive logging for security auditing

These measures work together to create a defense-in-depth security posture that protects the system against a wide range of threats.