# Docker Configuration

<cite>
**Referenced Files in This Document**   
- [services/admin/Dockerfile](file://services/admin/Dockerfile)
- [services/domain-seeder/Dockerfile](file://services/domain-seeder/Dockerfile)
- [services/web-app/Dockerfile](file://services/web-app/Dockerfile)
- [services/worker/Dockerfile](file://services/worker/Dockerfile)
- [docker-compose.dev.yml](file://docker-compose.dev.yml)
- [docker-compose.yml](file://docker-compose.yml)
- [services/admin/docker-compose.production.yml](file://services/admin/docker-compose.production.yml)
- [services/admin/docker-compose.simple.yml](file://services/admin/docker-compose.simple.yml)
- [services/worker/docker-compose.yml](file://services/worker/docker-compose.yml)
- [services/worker/docker-compose.test.yml](file://services/worker/docker-compose.test.yml)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Multi-Stage Docker Build Process](#multi-stage-docker-build-process)
3. [Development vs Production Docker Compose](#development-vs-production-docker-compose)
4. [Service-Specific Docker Configurations](#service-specific-docker-configurations)
5. [Docker Networking and Volume Strategies](#docker-networking-and-volume-strategies)
6. [Container Health Checks and Monitoring](#container-health-checks-and-monitoring)
7. [Common Docker Issues and Solutions](#common-docker-issues-and-solutions)
8. [Docker Security Best Practices](#docker-security-best-practices)
9. [Conclusion](#conclusion)

## Introduction
This document provides comprehensive documentation for the Docker configuration of the domainr application. It covers the multi-stage build processes, differences between development and production environments, service-specific configurations, networking, volume management, health checks, common issues, and security best practices. The domainr platform consists of multiple services including admin, domain-seeder, web-app, and worker, each with specialized Docker configurations optimized for their respective roles.

## Multi-Stage Docker Build Process

The domainr services implement multi-stage Docker builds to optimize image size, security, and performance. This approach separates the build environment from the runtime environment, resulting in smaller, more secure production images.

### Base Image Selection and Security Updates
All services use Node.js base images with security hardening measures. The admin service uses `node:22-bookworm-slim`, while the worker service uses the more minimal `node:22-alpine`. These base images are updated with security patches during the build process:

```mermaid
flowchart TD
A[Base Image] --> B[Security Updates]
B --> C[Package Installation]
C --> D[User Creation]
D --> E[Working Directory Setup]
```

**Diagram sources**
- [services/admin/Dockerfile](file://services/admin/Dockerfile#L1-L15)
- [services/worker/Dockerfile](file://services/worker/Dockerfile#L1-L10)

### Dependency Installation Strategy
Services follow a layered approach to dependency management. The domain-seeder service demonstrates a sophisticated dependency strategy with separate development and production stages:

```mermaid
flowchart LR
DevStage[Development Stage] --> BuildStage[Build Stage]
BuildStage --> ProdStage[Production Stage]
DevStage --> |Full Dependencies| BuildStage
BuildStage --> |Production Only| ProdStage
```

The build process installs all dependencies for compilation, then prunes development dependencies to minimize the final image size. This is particularly evident in the worker service which uses `pnpm prune --production` to remove unnecessary packages.

**Section sources**
- [services/domain-seeder/Dockerfile](file://services/domain-seeder/Dockerfile#L40-L80)
- [services/worker/Dockerfile](file://services/worker/Dockerfile#L40-L60)

### Artifact Copying and Optimization
The multi-stage build process carefully copies only necessary artifacts to the final image. The admin service's production stage copies built assets with proper ownership:

```mermaid
flowchart TD
Builder[Builder Stage] --> Runner[Runner Stage]
Builder --> |Copy .next/standalone| Runner
Builder --> |Copy .next/static| Runner
Builder --> |Copy public| Runner
Runner --> |Set Permissions| Runner
```

This selective copying reduces image size and improves security by excluding source code and development tools from the production container.

**Diagram sources**
- [services/admin/Dockerfile](file://services/admin/Dockerfile#L55-L65)

## Development vs Production Docker Compose

The domainr platform maintains distinct Docker Compose configurations for development and production environments, addressing different requirements for each stage of the development lifecycle.

### Network Configuration Differences
The production configuration establishes a dedicated bridge network with a defined subnet, while development configurations extend this with additional service-specific networks:

```mermaid
graph TB
subgraph Production
A[domain-ranking-network: **********/16]
end
subgraph Development
B[admin-network: **********/16]
C[monitoring-network: **********/16]
D[logging-network: **********/16]
end
```

The production network provides service isolation and predictable IP addressing, while development networks enable specialized monitoring and logging capabilities.

**Section sources**
- [docker-compose.yml](file://docker-compose.yml#L380-L388)
- [services/admin/docker-compose.production.yml](file://services/admin/docker-compose.production.yml#L270-L285)

### Volume Mounting Strategies
Development configurations use volume mounting for hot-reloading and real-time code updates, while production configurations use bind mounts for persistent data storage:

```mermaid
flowchart LR
Dev[Development Volumes] --> |Source Code Mounts| HotReload[Hot Reload]
Prod[Production Volumes] --> |Bind Mounts| Persistence[Data Persistence]
Dev --> ./services/domain-seeder/src:/app/services/domain-seeder/src
Dev --> ./shared/src:/app/shared/src
Prod --> /var/lib/redis/admin-panel:/data
Prod --> /var/lib/prometheus/admin-panel:/prometheus
```

This approach allows developers to modify code without rebuilding containers, while ensuring critical data survives container restarts in production.

**Diagram sources**
- [docker-compose.dev.yml](file://docker-compose.dev.yml#L45-L55)
- [services/admin/docker-compose.production.yml](file://services/admin/docker-compose.production.yml#L287-L302)

### Resource Allocation and Constraints
Production configurations implement strict resource limits and reservations to ensure system stability and fair resource distribution:

```mermaid
graph TB
subgraph Resource Management
Limits[Limits]
Reservations[Reservations]
Limits --> CPU[CPUs: 2.0]
Limits --> Memory[Memory: 2G]
Reservations --> CPURes[CPUs: 0.5]
Reservations --> MemRes[Memory: 512M]
end
```

The worker service, being resource-intensive, has higher limits (4G memory, 4.0 CPUs) compared to other services, reflecting its computational requirements for domain processing and ranking calculations.

**Section sources**
- [docker-compose.yml](file://docker-compose.yml#L140-L145)
- [services/admin/docker-compose.production.yml](file://services/admin/docker-compose.production.yml#L90-L95)

## Service-Specific Docker Configurations

Each service in the domainr platform has specialized Docker configurations tailored to its specific functionality and performance requirements.

### Admin Service Configuration
The admin service, built with Next.js, includes optimization settings for production deployment:

```mermaid
classDiagram
class AdminConfig {
+NEXT_TELEMETRY_DISABLED : 1
+NODE_ENV : production
+PORT : 3004
+dumb-init entrypoint
+non-root user execution
+health check integration
}
AdminConfig --> Security : implements
AdminConfig --> Performance : optimizes
```

The configuration disables Next.js telemetry, uses dumb-init for proper signal handling, and runs as a non-root user for security. The multi-stage build optimizes the Next.js standalone mode deployment.

**Diagram sources**
- [services/admin/Dockerfile](file://services/admin/Dockerfile#L1-L72)

### Worker Service Resource Constraints
The worker service has stringent resource constraints due to its intensive processing requirements:

```mermaid
flowchart TD
Worker[Worker Service]
Worker --> CPU[Limits: 4.0 CPUs]
Worker --> Memory[Limits: 4G Memory]
Worker --> Reservations[Reservations: 2G/2.0]
Worker --> Chromium[Headless Chrome Integration]
Worker --> Puppeteer[Puppeteer Configuration]
Chromium --> |Installed via APK| Worker
Puppeteer --> |Environment Variables| Worker
```

The Alpine-based image includes Chromium and related dependencies for browser automation, with environment variables configured for Puppeteer compatibility.

**Section sources**
- [services/worker/Dockerfile](file://services/worker/Dockerfile#L1-L101)
- [docker-compose.yml](file://docker-compose.yml#L100-L110)

### Domain Seeder Build Optimization
The domain-seeder service implements a sophisticated multi-stage build with development and production targets:

```mermaid
stateDiagram-v2
[*] --> Base
Base --> Development : target=development
Base --> Builder : target=builder
Builder --> Production : target=production
Development --> |Full dev dependencies| Running
Builder --> |Build artifacts| Production
Production --> |Minimal runtime| Running
```

This allows developers to use the same Dockerfile for both development (with hot reload) and production (with optimized image size) by specifying different build targets.

**Diagram sources**
- [services/domain-seeder/Dockerfile](file://services/domain-seeder/Dockerfile#L1-L135)

## Docker Networking and Volume Strategies

The domainr platform implements comprehensive networking and volume management strategies to ensure service connectivity, data persistence, and security.

### Inter-Service Communication
Services communicate through the domain-ranking-network bridge network, with DNS-based service discovery:

```mermaid
graph LR
Nginx[nginx] --> WebApp[web-app:3000]
WebApp --> Scylla[scylla:9042]
WebApp --> Maria[mariadb:3306]
WebApp --> Redis[redis:6379]
WebApp --> Manticore[manticore:9308]
Worker --> Browserless[browserless:3000]
```

Each service references others by container name, leveraging Docker's built-in DNS resolution for service discovery without hardcoded IP addresses.

**Section sources**
- [docker-compose.yml](file://docker-compose.yml#L50-L380)

### Volume Persistence for Databases
Database services use named volumes for data persistence across container restarts:

```mermaid
erDiagram
VOLUMES {
string name PK
string driver
string device FK
}
DATABASES {
string service PK
string volume FK
string mount_point
}
VOLUMES ||--o{ DATABASES : "has"
class VOLUMES {
scylla_data
mariadb_data
redis_data
manticore_data
}
class DATABASES {
scylla → scylla_data
mariadb → mariadb_data
redis → redis_data
manticore → manticore_data
}
```

This ensures that database data persists even when containers are recreated, while configuration files are mounted from the host for easy management.

**Diagram sources**
- [docker-compose.yml](file://docker-compose.yml#L350-L379)

### Configuration and Log Management
Configuration files and logs are managed through volume mounts, with different strategies for development and production:

```mermaid
flowchart TB
subgraph Configuration
Host[Host File System]
Container[Container]
Host --> |Read-only Mount| Container
./nginx/nginx.conf --> /etc/nginx/nginx.conf
./monitoring/prometheus.yml --> /etc/prometheus/prometheus.yml
end
subgraph Logging
ContainerLogs[Container Logs]
HostLogs[Host Logs Directory]
ContainerLogs --> |Mounted Volume| HostLogs
/app/logs --> ./logs
/var/log/nginx --> ./logs/nginx
end
```

This approach allows centralized log management and configuration updates without rebuilding images.

**Section sources**
- [docker-compose.yml](file://docker-compose.yml#L60-L80)
- [services/admin/docker-compose.production.yml](file://services/admin/docker-compose.production.yml#L30-L50)

## Container Health Checks and Monitoring

The domainr platform implements comprehensive health checking and monitoring to ensure service reliability and rapid issue detection.

### Health Check Implementation
Each service includes tailored health checks that validate service availability:

```mermaid
sequenceDiagram
participant Docker
participant Container
participant HealthCheck
Docker->>Container : Check health (interval : 30s)
Container->>HealthCheck : Execute health check command
alt Service Healthy
HealthCheck-->>Container : Return success (0)
Container-->>Docker : Healthy
else Service Unhealthy
HealthCheck-->>Container : Return failure (1)
Container-->>Docker : Unhealthy
Docker->>Container : Restart (if configured)
end
```

The domain-seeder service uses an HTTP endpoint check, while the admin service executes a Node.js script:

**Section sources**
- [services/domain-seeder/Dockerfile](file://services/domain-seeder/Dockerfile#L115-L120)
- [services/admin/Dockerfile](file://services/admin/Dockerfile#L68-L70)

### Monitoring Stack Integration
The platform includes a comprehensive monitoring stack with Prometheus, Grafana, and Alertmanager:

```mermaid
graph TB
Services --> Prometheus[Prometheus]
Prometheus --> Grafana[Grafana]
Prometheus --> Alertmanager[Alertmanager]
Grafana --> Users[Dashboard Users]
Alertmanager --> Notifications[Email/Slack]
class Services collect metrics
class Prometheus scrape metrics
class Grafana visualize metrics
class Alertmanager handle alerts
```

The admin service's production configuration includes this full monitoring stack, with pre-configured dashboards and alerting rules.

**Diagram sources**
- [services/admin/docker-compose.production.yml](file://services/admin/docker-compose.production.yml#L120-L220)

### Log Aggregation
The production configuration includes Fluentd for log aggregation:

```mermaid
flowchart LR
Services --> |JSON Logs| Fluentd[Fluentd]
Fluentd --> |Buffered| Storage[Elasticsearch/Files]
Fluentd --> |Real-time| Analysis[Log Analysis]
class Services log in JSON format
class Fluentd aggregate and route logs
class Storage persist logs
```

Logs are collected from all services and can be forwarded to external systems or stored locally for analysis.

**Section sources**
- [services/admin/docker-compose.production.yml](file://services/admin/docker-compose.production.yml#L220-L250)

## Common Docker Issues and Solutions

The domainr Docker configuration addresses common issues encountered in containerized environments.

### Port Conflicts and Resolution
Port conflicts are managed through environment variables and conditional port mapping:

```mermaid
flowchart TD
DevConfig[Development Config]
ProdConfig[Production Config]
DevConfig --> |Exposes ports| DirectAccess[Direct Host Access]
ProdConfig --> |Internal network| ServiceDiscovery[DNS-Based Discovery]
DevConfig --> HTTP_PORT:${HTTP_PORT:-80}
DevConfig --> HTTPS_PORT:${HTTPS_PORT:-443}
DevConfig --> SCYLLA_DEV_PORT:${SCYLLA_DEV_PORT:-9042}
Conflict[Port Conflict] --> Solution[Use Environment Variables]
Solution --> Default[Default Values]
Solution --> Override[Override via .env]
```

The use of environment variables with default values allows flexible port configuration across different environments.

**Section sources**
- [docker-compose.dev.yml](file://docker-compose.dev.yml#L70-L100)
- [docker-compose.yml](file://docker-compose.yml#L50-L60)

### Image Size Optimization
Multiple strategies are employed to minimize image sizes:

```mermaid
flowchart LR
Base[Small Base Image]
Prune[Remove Dev Dependencies]
Clean[Clean Package Cache]
Multi[Multi-Stage Build]
Minimal[Minimal Packages]
Base --> Alpine[node:22-alpine]
Prune --> pnpm[prune --production]
Clean --> npm[npm cache clean]
Multi --> Copy[Copy only artifacts]
Minimal --> no-recommends[--no-install-recommends]
All --> SmallImage[Smaller Final Image]
```

The worker service reduces image size by using Alpine Linux and removing development dependencies after the build process.

**Diagram sources**
- [services/worker/Dockerfile](file://services/worker/Dockerfile#L40-L60)

### Build Cache Management
The configuration leverages Docker's layer caching effectively:

```mermaid
flowchart TB
Layer1[Base Image + Security Updates]
Layer2[User Creation]
Layer3[Package Files Copy]
Layer4[Dependency Installation]
Layer5[Source Code Copy]
Layer6[Build Process]
Layer7[Final Image]
style Layer1 fill:#f9f,stroke:#333
style Layer2 fill:#f9f,stroke:#333
style Layer3 fill:#bbf,stroke:#333
style Layer4 fill:#bbf,stroke:#333
style Layer5 fill:#9f9,stroke:#333
style Layer6 fill:#9f9,stroke:#333
style Layer7 fill:#ff9,stroke:#333
Layer3 --> |Changes trigger| Layer4
Layer5 --> |Changes trigger| Layer6
```

By copying package files before source code, dependency installation is cached unless package.json changes, significantly speeding up development builds.

**Section sources**
- [services/admin/Dockerfile](file://services/admin/Dockerfile#L20-L30)

## Docker Security Best Practices

The domainr configuration implements industry-standard security practices to minimize attack surface and protect sensitive data.

### Non-Root User Execution
All services run as non-root users with specific UID/GID:

```mermaid
classDiagram
class Security {
+RUN groupadd -g 1000 nodejs
+useradd -r -u 1000 -g nodejs
+chown -R user : group /app
+USER directive
+Minimal capabilities
}
Security --> Admin : nextjs : nodejs
Security --> DomainSeeder : seeder : nodejs
Security --> WebApp : nodejs : nodejs
Security --> Worker : worker : worker
```

The admin service creates a `nextjs` user, while the domain-seeder service uses a `seeder` user, both with UID 1000 for consistency across environments.

**Diagram sources**
- [services/admin/Dockerfile](file://services/admin/Dockerfile#L10-L15)
- [services/domain-seeder/Dockerfile](file://services/domain-seeder/Dockerfile#L15-L20)

### Minimal Image Construction
Services use minimal base images and install only required packages:

```mermaid
flowchart TD
Full[Full OS Image]
Minimal[Minimal Image]
Full --> |Includes| Unneeded[Unused packages]
Full --> |Larger| AttackSurface[Attack surface]
Full --> |Slower| Transfers[Image transfers]
Minimal --> |Only essential| Required[Required packages]
Minimal --> |Smaller| AttackSurface
Minimal --> |Faster| Transfers
Minimal --> Alpine[Alpine Linux]
Minimal --> Slim[Debian Slim]
Minimal --> no-recommends[--no-install-recommends]
Required --> dumb-init[Process supervisor]
Required --> ca-certificates[SSL certificates]
Required --> tzdata[Timezone data]
```

The worker service uses Alpine Linux and installs only necessary runtime dependencies like Chromium for Puppeteer.

**Section sources**
- [services/worker/Dockerfile](file://services/worker/Dockerfile#L30-L40)

### Security Hardening Measures
Additional security measures are implemented across all services:

```mermaid
flowchart LR
Permissions[File Permissions]
Cleanup[Tmp File Cleanup]
ReadOnly[Read-only mounts]
Labels[Image Labels]
Health[Health checks]
Permissions --> chmod[chmod 755 /app]
Permissions --> chown[chown -R user:group]
Cleanup --> rm[rm -rf /tmp/*]
Cleanup --> apt[apt-get clean]
Cleanup --> apk[apk cache cleanup]
ReadOnly --> config[Configuration files]
ReadOnly --> data[Data directories]
Labels --> provenance[Image provenance]
Labels --> maintainers[Maintainer info]
Health --> restart[Auto-restart on failure]
```

These measures include removing temporary files, cleaning package caches, setting appropriate file permissions, and using read-only mounts for configuration.

**Diagram sources**
- [services/admin/Dockerfile](file://services/admin/Dockerfile#L65-L67)
- [services/domain-seeder/Dockerfile](file://services/domain-seeder/Dockerfile#L25-L30)

## Conclusion
The domainr Docker configuration demonstrates a comprehensive approach to containerization, balancing development efficiency with production reliability and security. The multi-stage build process optimizes image size and security, while the differentiated development and production configurations address the unique requirements of each environment. Service-specific configurations reflect the specialized needs of different components, from the Next.js-optimized admin interface to the resource-intensive worker service. The implementation of Docker best practices—including non-root user execution, minimal image construction, comprehensive health checks, and proper volume management—ensures a robust and secure deployment. This configuration serves as a model for complex microservices applications requiring high availability, performance, and security.