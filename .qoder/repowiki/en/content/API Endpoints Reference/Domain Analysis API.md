# Domain Analysis API

<cite>
**Referenced Files in This Document**   
- [description.ts](file://services/web-app/src/routes/description.ts)
- [DomainAnalysis.ts](file://shared/src/models/DomainAnalysis.ts)
- [DomainCrawlJob.ts](file://shared/src/models/DomainCrawlJob.ts)
- [JobConsumer.ts](file://services/worker/src/queue/JobConsumer.ts)
- [SeederCachingService.ts](file://services/domain-seeder/src/services/SeederCachingService.ts)
- [RedisClient.ts](file://shared/src/database/RedisClient.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [API Endpoints](#api-endpoints)
3. [Request/Response Schemas](#requestresponse-schemas)
4. [Caching Strategy](#caching-strategy)
5. [Asynchronous Processing Workflow](#asynchronous-processing-workflow)
6. [Error Handling](#error-handling)
7. [Client Implementation Examples](#client-implementation-examples)
8. [Performance Considerations](#performance-considerations)
9. [Best Practices](#best-practices)

## Introduction
The Domain Analysis API provides comprehensive domain intelligence through synchronous and asynchronous analysis capabilities. The API enables clients to retrieve detailed domain information either from cached results or by initiating new analysis jobs. The system leverages Redis for caching, implements a robust job queuing mechanism to the Worker service, and supports both immediate retrieval and polling-based workflows for analysis results.

**Section sources**
- [description.ts](file://services/web-app/src/routes/description.ts)

## API Endpoints

### GET /api/domains/{domain}
Retrieves domain analysis information for a specific domain. The endpoint first checks the Redis cache for existing analysis results. If found, it returns the cached data immediately. If no cached result exists, the system returns a pending status and clients must use the POST endpoint to initiate analysis.

### POST /api/domains/{domain}/analyze
Initiates a new domain analysis job for the specified domain. This endpoint enqueues a job to the Worker service for processing and returns a job identifier that can be used to poll for analysis status and results.

**Section sources**
- [description.ts](file://services/web-app/src/routes/description.ts)

## Request/Response Schemas

### DomainAnalysis Model
The DomainAnalysis model represents comprehensive domain intelligence data including performance, security, SEO, technical, and content metrics.

```mermaid
classDiagram
class DomainAnalysis {
+string domain
+number globalRank
+number categoryRank
+string category
+number overallScore
+PerformanceMetricsType performance
+SecurityMetricsType security
+SEOMetricsType seo
+TechnicalMetricsType technical
+DomainInfoType domainInfo
+ContentQualityMetricsType contentQuality
+LanguageDetectionType languageDetection
+ContentMetricsType contentMetrics
+TechnicalSEOType technicalSEO
+string[] screenshots
+string[] subdomains
+string lastCrawled
+string crawlStatus
+string lastUpdated
+calculateOverallScore(weights) number
+toScyllaFormat() Record~string, unknown~
}
class PerformanceMetricsType {
+number loadTime
+number firstContentfulPaint
+number largestContentfulPaint
+number cumulativeLayoutShift
+number firstInputDelay
+number speedIndex
+number score
}
class SecurityMetricsType {
+string sslGrade
+Record~string, unknown~ securityHeaders
+string[] vulnerabilities
+Record~string, unknown~ certificateInfo
+number score
}
class SEOMetricsType {
+Record~string, unknown~ metaTags
+unknown[] structuredData
+Record~string, unknown~ sitemap
+Record~string, unknown~ robotsTxt
+number score
}
DomainAnalysis --> PerformanceMetricsType : "contains"
DomainAnalysis --> SecurityMetricsType : "contains"
DomainAnalysis --> SEOMetricsType : "contains"
```

**Diagram sources**
- [DomainAnalysis.ts](file://shared/src/models/DomainAnalysis.ts)

### DomainCrawlJob Model
The DomainCrawlJob model represents a domain analysis job in the system, tracking its status, priority, and processing details.

```mermaid
classDiagram
class DomainCrawlJob {
+string jobId
+string domain
+string crawlType
+string priority
+string status
+number retryCount
+number maxRetries
+string scheduledAt
+string startedAt
+string completedAt
+string errorMessage
+string userAgent
+string[] pagesToCrawl
+generateJobId() string
+markStarted() void
+markCompleted() void
+markFailed(errorMessage) void
+canRetry() boolean
+incrementRetry() void
}
class DomainCrawlJobStatus {
<<enumeration>>
pending
processing
completed
failed
}
class DomainCrawlJobType {
<<enumeration>>
full
quick
security
performance
}
class DomainCrawlJobPriority {
<<enumeration>>
high
medium
low
}
DomainCrawlJob --> DomainCrawlJobStatus : "status"
DomainCrawlJob --> DomainCrawlJobType : "crawlType"
DomainCrawlJob --> DomainCrawlJobPriority : "priority"
```

**Diagram sources**
- [DomainCrawlJob.ts](file://shared/src/models/DomainCrawlJob.ts)

## Caching Strategy
The Domain Analysis API implements a multi-layer caching strategy using Redis to optimize performance and reduce processing load. The system first checks for cached results before initiating new analysis jobs.

The caching mechanism includes:
- **Domain existence caching**: Stores whether a domain has been analyzed previously
- **Result caching**: Stores complete DomainAnalysis objects with TTL
- **Bloom filter optimization**: Uses probabilistic data structure to quickly determine if a domain likely exists in the system
- **Idempotency keys**: Prevents duplicate analysis jobs for the same domain

When a cache miss occurs, the system returns a pending status and requires clients to initiate analysis via the POST endpoint.

```mermaid
flowchart TD
A["Client Request\nGET /api/domains/{domain}"] --> B{Cache Check}
B --> |Hit| C["Return Cached\nDomainAnalysis"]
B --> |Miss| D["Return Pending Status"]
D --> E["Client Initiates\nPOST /api/domains/{domain}/analyze"]
E --> F["Enqueue Job to\nWorker Service"]
F --> G["Process Analysis"]
G --> H["Store Results in\nRedis & Database"]
H --> I["Analysis Complete"]
style B fill:#f9f,stroke:#333
style C fill:#bbf,stroke:#333
style D fill:#f96,stroke:#333
```

**Diagram sources**
- [description.ts](file://services/web-app/src/routes/description.ts)
- [SeederCachingService.ts](file://services/domain-seeder/src/services/SeederCachingService.ts)
- [RedisClient.ts](file://shared/src/database/RedisClient.ts)

**Section sources**
- [description.ts](file://services/web-app/src/routes/description.ts)
- [SeederCachingService.ts](file://services/domain-seeder/src/services/SeederCachingService.ts)

## Asynchronous Processing Workflow
The Domain Analysis API employs an asynchronous processing workflow to handle resource-intensive domain analysis tasks. This approach ensures responsive API performance while enabling comprehensive analysis.

```mermaid
sequenceDiagram
participant Client
participant API
participant Redis
participant Worker
participant Database
Client->>API : GET /api/domains/example.com
API->>Redis : Check cache
Redis-->>API : Cache miss
API-->>Client : 200 OK {status : "pending"}
Client->>API : POST /api/domains/example.com/analyze
API->>Redis : Enqueue job to queue : domain : crawl
API-->>Client : 202 Accepted {jobId : "crawl_123"}
Worker->>Redis : Consume job
Worker->>Worker : Process domain analysis
Worker->>Database : Store results
Worker->>Redis : Update job status
Client->>API : GET /api/jobs/crawl_123/status
API->>Redis : Get job progress
Redis-->>API : {status : "completed", progress : 100}
API-->>Client : 200 OK {status : "completed"}
Client->>API : GET /api/domains/example.com
API->>Redis : Check cache
Redis-->>API : Return cached results
API-->>Client : 200 OK {DomainAnalysis...}
```

**Diagram sources**
- [description.ts](file://services/web-app/src/routes/description.ts)
- [JobConsumer.ts](file://services/worker/src/queue/JobConsumer.ts)
- [SeederCachingService.ts](file://services/domain-seeder/src/services/SeederCachingService.ts)

**Section sources**
- [JobConsumer.ts](file://services/worker/src/queue/JobConsumer.ts)

## Error Handling
The Domain Analysis API implements comprehensive error handling for various failure scenarios:

### Error Responses
- **400 Bad Request**: Invalid domain format
- **404 Not Found**: Domain does not exist in the system
- **429 Too Many Requests**: Rate limiting threshold exceeded
- **500 Internal Server Error**: System processing failure
- **503 Service Unavailable**: Worker service or database unavailable

### Rate Limiting
The API implements rate limiting to prevent abuse and ensure system stability. Clients exceeding the allowed request threshold receive a 429 status code with retry-after guidance.

### Cache Miss Handling
When a domain analysis is not available in cache, the API returns a structured response indicating the pending status:

```json
{
  "metadata": {
    "domain": "example.com",
    "status": "pending",
    "category": {
      "primary": "uncategorized"
    }
  },
  "crawl": {
    "lastCrawled": "2023-12-07T10:30:00Z",
    "crawlType": "quick"
  }
}
```

**Section sources**
- [description.ts](file://services/web-app/src/routes/description.ts)

## Client Implementation Examples

### Synchronous Usage (Cached Results)
```javascript
// Check for cached analysis results
async function getDomainAnalysis(domain) {
  try {
    const response = await fetch(`/api/domains/${domain}`);
    const data = await response.json();
    
    if (data.metadata.status === 'pending') {
      console.log('Analysis not available, initiate processing');
      return null;
    }
    
    return data;
  } catch (error) {
    console.error('Failed to retrieve domain analysis:', error);
    throw error;
  }
}
```

### Asynchronous Usage (Polling Mechanism)
```javascript
// Initiate analysis and poll for results
async function analyzeDomain(domain) {
  // Start analysis job
  const jobResponse = await fetch(`/api/domains/${domain}/analyze`, {
    method: 'POST'
  });
  
  const jobData = await jobResponse.json();
  const jobId = jobData.jobId;
  
  // Poll for job completion
  let status = 'processing';
  while (status === 'processing' || status === 'pending') {
    await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5 seconds
    
    const statusResponse = await fetch(`/api/jobs/${jobId}/status`);
    const statusData = await statusResponse.json();
    status = statusData.status;
  }
  
  if (status === 'completed') {
    return getDomainAnalysis(domain);
  } else {
    throw new Error(`Analysis failed: ${statusData.errorMessage}`);
  }
}
```

**Section sources**
- [description.ts](file://services/web-app/src/routes/description.ts)

## Performance Considerations
The Domain Analysis API is designed with performance optimization in mind:

- **Cache-first approach**: Minimizes database queries and processing overhead
- **Asynchronous processing**: Prevents blocking API requests during intensive analysis
- **Bloom filter optimization**: Reduces cache lookup overhead for non-existent domains
- **Batch operations**: Supports efficient batch retrieval of domain existence status
- **Connection pooling**: Maintains efficient Redis and database connections
- **Job prioritization**: Enables high-priority analysis requests to be processed first

The system is optimized to handle high request volumes while maintaining responsive performance for cached results.

**Section sources**
- [SeederCachingService.ts](file://services/domain-seeder/src/services/SeederCachingService.ts)
- [JobConsumer.ts](file://services/worker/src/queue/JobConsumer.ts)

## Best Practices

### For Single Domain Analysis
- Always check for cached results first using GET before initiating analysis
- Implement exponential backoff when polling for job status
- Cache client-side results to minimize API calls
- Handle 429 responses gracefully with appropriate retry logic

### For Batch Analysis Requests
- Use batch endpoints when available to reduce request overhead
- Implement rate limiting awareness in batch processing loops
- Process domains in parallel within rate limit constraints
- Use idempotency keys to prevent duplicate processing
- Monitor job queue status during large batch operations

### General Recommendations
- Set appropriate TTL values based on use case requirements
- Implement circuit breakers for resilience during service degradation
- Monitor API response times and error rates
- Use the health check endpoints to verify system status
- Implement proper error handling for all possible response codes

**Section sources**
- [description.ts](file://services/web-app/src/routes/description.ts)
- [JobConsumer.ts](file://services/worker/src/queue/JobConsumer.ts)
- [SeederCachingService.ts](file://services/domain-seeder/src/services/SeederCachingService.ts)