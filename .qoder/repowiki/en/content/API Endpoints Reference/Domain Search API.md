# Domain Search API

<cite>
**Referenced Files in This Document**   
- [DomainSearchService.ts](file://services/web-app/src/services/DomainSearchService.ts)
- [description.ts](file://services/web-app/src/routes/description.ts)
- [SearchResult.ts](file://shared/src/models/SearchResult.ts)
- [ManticoreClient.ts](file://shared/src/database/ManticoreClient.ts)
- [ManticoreDomainRepository.ts](file://services/domain-seeder/src/repositories/ManticoreDomainRepository.ts)
- [RateLimitMiddleware.ts](file://shared/src/middleware/RateLimitMiddleware.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [API Endpoint](#api-endpoint)
3. [Request Parameters](#request-parameters)
4. [Response Schema](#response-schema)
5. [Search Capabilities](#search-capabilities)
6. [Pagination](#pagination)
7. [Authentication and Rate Limiting](#authentication-and-rate-limiting)
8. [Caching and Performance](#caching-and-performance)
9. [Error Handling](#error-handling)
10. [Client Implementation Example](#client-implementation-example)
11. [Troubleshooting](#troubleshooting)

## Introduction
The Domain Search API provides a powerful interface for discovering and analyzing domain names across the internet. Built on top of Manticore Search, the API enables full-text search, faceted filtering, and real-time domain analytics. This documentation details the API endpoints, request/response formats, search capabilities, and implementation details for developers integrating with the system.

## API Endpoint
The Domain Search API is accessible via HTTP GET method at the following URL pattern:

```
GET /api/search
```

This endpoint serves as the primary interface for domain discovery and supports various query parameters to filter and refine search results.

**Section sources**
- [DomainSearchService.ts](file://services/web-app/src/services/DomainSearchService.ts)

## Request Parameters
The API supports the following query parameters to customize search results:

| Parameter | Type | Required | Description |
|---------|------|----------|-------------|
| q | string | Yes | Search query for full-text search across domain names, titles, descriptions, and technologies |
| limit | number | No | Maximum number of results to return (default: 20, maximum: 100) |
| offset | number | No | Number of results to skip for pagination (default: 0) |
| category | string | No | Filter results by domain category (e.g., "technology", "finance", "healthcare") |
| country | string | No | Filter results by country code (ISO 3166-1 alpha-2 format) |

**Section sources**
- [DomainSearchService.ts](file://services/web-app/src/services/DomainSearchService.ts)
- [ManticoreClient.ts](file://shared/src/database/ManticoreClient.ts)

## Response Schema
The API returns results in a standardized format based on the SearchResult model.

### SearchResult Model
The response structure includes metadata about the search operation and the matching domains.

```json
{
  "domains": [
    {
      "domain": "example.com",
      "title": "Example Domain",
      "description": "An example domain for demonstration purposes",
      "category": "uncategorized",
      "country": "US",
      "language": "en",
      "technologies": ["WordPress", "Apache", "PHP"],
      "registrar": "Example Registrar Inc.",
      "domainAge": 3650,
      "globalRank": 15000,
      "scores": {
        "overall": 85.5,
        "performance": 78.2,
        "security": 92.1,
        "seo": 88.7,
        "technical": 80.3,
        "backlink": 75.4
      },
      "trafficEstimate": 500000,
      "sslGrade": "A+",
      "mobileFriendlyScore": 95,
      "accessibilityScore": 88,
      "lastUpdated": "2024-01-15T10:30:00Z",
      "_score": 1.25
    }
  ],
  "totalResults": 1500,
  "currentPage": 1,
  "totalPages": 75,
  "filters": {
    "category": "technology",
    "country": "US"
  },
  "facets": {
    "category": [
      { "value": "technology", "count": 850 },
      { "value": "finance", "count": 320 },
      { "value": "healthcare", "count": 180 }
    ],
    "country": [
      { "value": "US", "count": 950 },
      { "value": "GB", "count": 230 },
      { "value": "DE", "count": 175 }
    ],
    "technologies": [
      { "value": "WordPress", "count": 650 },
      { "value": "React", "count": 420 },
      { "value": "Angular", "count": 310 }
    ],
    "ssl_grade": [
      { "value": "A+", "count": 720 },
      { "value": "A", "count": 480 },
      { "value": "B", "count": 200 }
    ]
  },
  "searchTime": 45
}
```

**Section sources**
- [SearchResult.ts](file://shared/src/models/SearchResult.ts)
- [ManticoreClient.ts](file://shared/src/database/ManticoreClient.ts)

## Search Capabilities
The Domain Search API leverages Manticore Search to provide advanced search functionality.

### Full-Text Search
The API performs full-text search across multiple domain attributes including:
- Domain name
- Page title
- Meta description
- Technologies used

The search uses a boolean query with multiple "should" clauses, requiring a minimum of one match across the searchable fields.

### Faceted Filtering
The API automatically returns faceted data that enables users to refine their search results. Available facets include:
- Category
- Country
- Technologies
- SSL grade

These facets are calculated server-side and returned with each search response, allowing for dynamic filtering interfaces in client applications.

```mermaid
flowchart TD
A["User Search Query"] --> B["Build Manticore Query"]
B --> C["Apply Full-Text Search"]
C --> D["Apply Faceted Filters"]
D --> E["Execute Search"]
E --> F["Parse Results"]
F --> G["Return SearchResult Object"]
```

**Diagram sources**
- [ManticoreClient.ts](file://shared/src/database/ManticoreClient.ts)
- [ManticoreDomainRepository.ts](file://services/domain-seeder/src/repositories/ManticoreDomainRepository.ts)

**Section sources**
- [ManticoreClient.ts](file://shared/src/database/ManticoreClient.ts)
- [ManticoreDomainRepository.ts](file://services/domain-seeder/src/repositories/ManticoreDomainRepository.ts)

## Pagination
The API implements standard pagination using limit and offset parameters.

### Pagination Parameters
- `limit`: Controls the number of results per page (default: 20, max: 100)
- `offset`: Specifies the starting position for results (default: 0)

### Response Metadata
The API includes pagination metadata in the response:
- `totalResults`: Total number of matching domains
- `currentPage`: Current page number (calculated from offset and limit)
- `totalPages`: Total number of pages available

This allows clients to implement pagination controls and display result statistics.

**Section sources**
- [DomainSearchService.ts](file://services/web-app/src/services/DomainSearchService.ts)
- [ManticoreClient.ts](file://shared/src/database/ManticoreClient.ts)

## Authentication and Rate Limiting
The API implements rate limiting to ensure fair usage and prevent abuse.

### Rate Limiting Strategy
The system uses Redis-based token bucket algorithm for rate limiting, implemented through the RateLimitMiddleware.

- Default limit: 100 requests per hour per IP address
- Burst capacity: 10 requests
- Redis stores rate limit counters with TTL (time-to-live) matching the rate limit window

When the rate limit is exceeded, the API returns a 429 status code with retry-after header.

```mermaid
sequenceDiagram
participant Client
participant API
participant Redis
Client->>API : GET /api/search?q=example
API->>Redis : GET rate_limit : client_ip
Redis-->>API : counter value
API->>API : Check against limit
alt Within limit
API->>API : Process request
API->>Client : 200 OK + results
else Exceeded limit
API->>Client : 429 Too Many Requests
API->>Client : Retry-After : 3600
end
```

**Diagram sources**
- [RateLimitMiddleware.ts](file://shared/src/middleware/RateLimitMiddleware.ts)

**Section sources**
- [RateLimitMiddleware.ts](file://shared/src/middleware/RateLimitMiddleware.ts)

## Caching and Performance
The API implements multiple layers of caching to optimize performance.

### Caching Layers
1. **Redis Cache**: Stores frequently accessed search results with a TTL of 5 minutes
2. **Manticore Search**: Full-text search engine optimized for fast domain lookups
3. **Client-Side Caching**: API responses include appropriate cache headers

### Performance Optimization
- Query optimization through Manticore's distributed search capabilities
- Batch processing for multiple domain checks
- Connection pooling for database and search engine connections
- Asynchronous processing for non-critical operations

The system monitors search performance and returns the search execution time in milliseconds as part of the response.

**Section sources**
- [ManticoreClient.ts](file://shared/src/database/ManticoreClient.ts)
- [ManticoreDomainRepository.ts](file://services/domain-seeder/src/repositories/ManticoreDomainRepository.ts)

## Error Handling
The API returns standardized error responses for various failure scenarios.

### Error Responses
| Status Code | Error Type | Response Body | Description |
|-----------|----------|--------------|-------------|
| 400 | Bad Request | `{ "error": "Invalid query parameter" }` | Missing or malformed query parameters |
| 429 | Too Many Requests | `{ "error": "Rate limit exceeded", "retryAfter": 3600 }` | Client has exceeded rate limit |
| 500 | Internal Server Error | `{ "error": "Internal Server Error" }` | Unexpected server error during processing |

### Error Response Schema
```json
{
  "error": "Error message describing the issue",
  "retryAfter": 3600 // Present in 429 responses
}
```

The API logs all errors with detailed context for debugging while returning generic error messages to clients to avoid information disclosure.

**Section sources**
- [DomainSearchService.ts](file://services/web-app/src/services/DomainSearchService.ts)
- [description.ts](file://services/web-app/src/routes/description.ts)

## Client Implementation Example
The following example demonstrates how to implement a Domain Search API client in JavaScript/TypeScript.

### JavaScript/TypeScript Implementation
```typescript
class DomainSearchClient {
  private baseUrl: string;
  private defaultLimit: number;

  constructor(baseUrl: string = 'https://api.domainr.com') {
    this.baseUrl = baseUrl;
    this.defaultLimit = 20;
  }

  async search(params: {
    q: string;
    limit?: number;
    offset?: number;
    category?: string;
    country?: string;
  }): Promise<SearchResult> {
    try {
      const queryParams = new URLSearchParams();
      
      // Required parameter
      if (!params.q || params.q.trim() === '') {
        throw new Error('Search query (q) is required');
      }
      queryParams.append('q', params.q);
      
      // Optional parameters
      if (params.limit) queryParams.append('limit', params.limit.toString());
      if (params.offset) queryParams.append('offset', params.offset.toString());
      if (params.category) queryParams.append('category', params.category);
      if (params.country) queryParams.append('country', params.country);
      
      const response = await fetch(`${this.baseUrl}/api/search?${queryParams}`);
      
      if (!response.ok) {
        if (response.status === 429) {
          const retryAfter = response.headers.get('Retry-After');
          throw new Error(`Rate limit exceeded. Retry after ${retryAfter} seconds.`);
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Search failed:', error);
      throw error;
    }
  }
}

// Usage example with pagination
async function searchWithPagination() {
  const client = new DomainSearchClient();
  const resultsPerPage = 20;
  let allResults: any[] = [];
  let currentPage = 0;
  let hasMore = true;

  try {
    while (hasMore) {
      const offset = currentPage * resultsPerPage;
      const result = await client.search({
        q: 'technology',
        limit: resultsPerPage,
        offset: offset,
        category: 'technology'
      });

      allResults = allResults.concat(result.domains);
      
      // Check if we've retrieved all results
      if (result.domains.length < resultsPerPage || allResults.length >= result.totalResults) {
        hasMore = false;
      }
      
      currentPage++;
      
      // Add small delay to be respectful to the API
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    console.log(`Retrieved ${allResults.length} domains out of ${allResults[0]?.totalResults} total`);
    return allResults;
  } catch (error) {
    console.error('Failed to retrieve all pages:', error);
    return allResults;
  }
}
```

**Section sources**
- [DomainSearchService.ts](file://services/web-app/src/services/DomainSearchService.ts)

## Troubleshooting
This section addresses common issues encountered when using the Domain Search API.

### Common Issues and Solutions

#### Query Parsing Errors
**Issue**: Search returns no results despite valid query
**Solution**: 
- Ensure the query parameter `q` is provided and not empty
- Check for special characters that may need URL encoding
- Verify the search term exists in the domain index

#### Slow Response Times
**Issue**: API responses are slower than expected
**Solutions**:
- Implement client-side caching of frequent queries
- Reduce the limit parameter for faster initial responses
- Use faceted filtering to narrow results before full-text search
- Consider using the offset parameter judiciously, as large offsets can impact performance

#### Rate Limiting
**Issue**: Receiving 429 responses
**Solutions**:
- Implement exponential backoff in client retry logic
- Cache results locally to reduce API calls
- Monitor usage patterns and adjust client behavior accordingly
- Consider requesting a higher rate limit for production applications

#### Empty Facets
**Issue**: Facet data is missing or empty
**Solution**: 
- Ensure the search query returns sufficient results (facets require a minimum number of matches)
- Check that the filtered fields contain diverse values
- Verify the index contains up-to-date data

**Section sources**
- [DomainSearchService.ts](file://services/web-app/src/services/DomainSearchService.ts)
- [ManticoreClient.ts](file://shared/src/database/ManticoreClient.ts)
- [ManticoreDomainRepository.ts](file://services/domain-seeder/src/repositories/ManticoreDomainRepository.ts)