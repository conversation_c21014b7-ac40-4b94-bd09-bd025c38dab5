# Rankings API

<cite>
**Referenced Files in This Document**   
- [DomainRanking.ts](file://shared/src/models/DomainRanking.ts)
- [TopDomainsService.ts](file://services/web-app/src/services/TopDomainsService.ts)
- [RankingUpdateService.ts](file://services/worker/src/ranking/RankingUpdateService.ts)
- [CompositeRanker.ts](file://services/worker/src/ranking/CompositeRanker.ts)
- [ScyllaClient.ts](file://shared/src/database/ScyllaClient.ts)
- [MariaClient.ts](file://shared/src/database/MariaClient.ts)
- [RedisClient.ts](file://shared/src/database/RedisClient.ts)
- [ManticoreClient.ts](file://shared/src/database/ManticoreClient.ts)
- [TopDomainsPage.tsx](file://services/web-app/src/components/TopDomainsPage.tsx)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [API Endpoint](#api-endpoint)
3. [Request Parameters](#request-parameters)
4. [Response Schema](#response-schema)
5. [Data Storage and Architecture](#data-storage-and-architecture)
6. [Ranking Calculation and Update Process](#ranking-calculation-and-update-process)
7. [Filtering and Pagination](#filtering-and-pagination)
8. [Caching Strategy](#caching-strategy)
9. [Example Responses](#example-responses)
10. [Client Implementation Examples](#client-implementation-examples)
11. [Troubleshooting Guide](#troubleshooting-guide)
12. [Conclusion](#conclusion)

## Introduction
The Rankings API provides access to pre-calculated domain rankings data, enabling clients to retrieve top domains by category, country, and other filters. The system is designed for high performance and scalability, leveraging pre-computed rankings stored in ScyllaDB and MariaDB, with periodic updates managed by the Worker service. This documentation details the API endpoints, response structure, underlying data architecture, and operational characteristics to support effective integration and troubleshooting.

**Section sources**
- [TopDomainsService.ts](file://services/web-app/src/services/TopDomainsService.ts#L1-L50)

## API Endpoint
The primary endpoint for retrieving top domains is:

```
GET /api/rankings/top
```

This endpoint returns a list of top-ranked domains with comprehensive ranking metrics, supporting various query parameters for filtering and pagination. The API is served through the web application layer and backed by high-performance databases and caching infrastructure.

```mermaid
flowchart TD
Client["Client Application"] --> API["Rankings API /api/rankings/top"]
API --> Cache{Redis Cache}
Cache --> |Hit| Response["Return Cached Response"]
Cache --> |Miss| DB["ScyllaDB/ManticoreDB"]
DB --> Cache
DB --> Response
Response --> Client
```

**Diagram sources**
- [TopDomainsService.ts](file://services/web-app/src/services/TopDomainsService.ts#L1-L100)

**Section sources**
- [TopDomainsService.ts](file://services/web-app/src/services/TopDomainsService.ts#L1-L100)

## Request Parameters
The API supports the following query parameters for filtering and pagination:

| Parameter | Type | Required | Default | Description |
|---------|------|----------|---------|-------------|
| `category` | string | No | "all" | Filter domains by category (e.g., technology, ecommerce, news) |
| `country` | string | No | "all" | Filter domains by country code (ISO 3166-1 alpha-2) |
| `limit` | number | No | 50 | Number of results to return (maximum 500) |
| `offset` | number | No | 0 | Number of records to skip for pagination |
| `timeframe` | string | No | "current" | Time period for ranking data |

These parameters enable flexible querying of the rankings data, allowing clients to retrieve specific subsets of domains based on their requirements.

**Section sources**
- [TopDomainsService.ts](file://services/web-app/src/services/TopDomainsService.ts#L100-L200)

## Response Schema
The API response follows a consistent structure based on the DomainRanking model, with the following schema:

```json
{
  "domains": [
    {
      "domain": "string",
      "globalRank": "number",
      "categoryRank": "number",
      "category": "string",
      "country": "string",
      "scores": {
        "overall": "number",
        "performance": "number",
        "security": "number",
        "seo": "number",
        "technical": "number"
      },
      "trafficEstimate": "number",
      "trend": "string",
      "lastUpdated": "string"
    }
  ],
  "category": "string",
  "country": "string",
  "timeframe": "string",
  "total": "number",
  "timestamp": "string"
}
```

The `DomainRanking` class defines the core structure of ranking data, including the domain name, rank, overall score, and breakdown of performance, security, SEO, technical, and backlink scores. The response includes metadata about the query parameters and timing.

```mermaid
classDiagram
class DomainRanking {
+string domain
+string rankingType
+number rank
+number overallScore
+number performanceScore
+number securityScore
+number seoScore
+number technicalScore
+number backlinkScore
+number trafficEstimate
+string lastUpdated
+toScyllaFormat() Record~string, unknown~
}
```

**Diagram sources**
- [DomainRanking.ts](file://shared/src/models/DomainRanking.ts#L1-L70)

**Section sources**
- [DomainRanking.ts](file://shared/src/models/DomainRanking.ts#L1-L70)

## Data Storage and Architecture
The rankings data is stored in a distributed database architecture using ScyllaDB for primary storage and MariaDB for secondary storage and analytics. ScyllaDB provides high-performance, low-latency access to ranking data, while MariaDB supports complex queries and reporting.

The data flow begins with the Worker service calculating rankings and storing them in ScyllaDB. The ManticoreDB serves as a search-optimized index for fast retrieval of top domains by category and other filters. Redis is used as a caching layer to reduce database load and improve response times.

```mermaid
graph TB
subgraph "Data Storage"
ScyllaDB[(ScyllaDB)]
MariaDB[(MariaDB)]
ManticoreDB[(ManticoreDB)]
Redis[(Redis)]
end
subgraph "Processing"
Worker[Worker Service]
WebApp[Web Application]
end
Worker --> ScyllaDB
Worker --> MariaDB
Worker --> ManticoreDB
WebApp --> Redis
Redis --> ScyllaDB
Redis --> ManticoreDB
WebApp --> ScyllaDB
WebApp --> ManticoreDB
```

**Diagram sources**
- [ScyllaClient.ts](file://shared/src/database/ScyllaClient.ts#L1-L20)
- [MariaClient.ts](file://shared/src/database/MariaClient.ts#L1-L20)
- [ManticoreClient.ts](file://shared/src/database/ManticoreClient.ts#L1-L20)
- [RedisClient.ts](file://shared/src/database/RedisClient.ts#L1-L20)

**Section sources**
- [ScyllaClient.ts](file://shared/src/database/ScyllaClient.ts#L1-L50)
- [MariaClient.ts](file://shared/src/database/MariaClient.ts#L1-L50)

## Ranking Calculation and Update Process
Rankings are pre-calculated by the Worker service using a composite ranking algorithm that combines multiple metrics into an overall score. The process is periodic and batch-oriented, with global rankings updated daily and category-specific rankings updated weekly.

The `RankingUpdateService` manages the scheduling and execution of ranking updates, using a job queue system to process domains in batches. The `CompositeRanker` calculates scores based on performance, security, SEO, technical, and backlink metrics, with configurable weights for each component.

```mermaid
sequenceDiagram
participant Scheduler
participant Worker as "Worker Service"
participant Queue as "Job Queue"
participant ScyllaDB as "ScyllaDB"
Scheduler->>Worker : Trigger scheduled update
Worker->>Queue : Publish batch ranking jobs
loop For each job
Queue->>Worker : Deliver job
Worker->>Worker : Calculate composite score
Worker->>ScyllaDB : Store updated ranking
Worker->>Queue : Acknowledge completion
end
Worker->>Scheduler : Report completion
```

**Diagram sources**
- [RankingUpdateService.ts](file://services/worker/src/ranking/RankingUpdateService.ts#L1-L100)
- [CompositeRanker.ts](file://services/worker/src/ranking/CompositeRanker.ts#L1-L20)

**Section sources**
- [RankingUpdateService.ts](file://services/worker/src/ranking/RankingUpdateService.ts#L1-L200)

## Filtering and Pagination
The API supports comprehensive filtering by category and country, with pagination implemented through limit and offset parameters. The `TopDomainsService` handles these operations by querying the appropriate database based on the request parameters.

For category filtering, the service first retrieves available categories from ManticoreDB's facet system, then applies the filter to the ranking query. Pagination is implemented with offset-based navigation, with a maximum limit of 500 results per request to prevent performance issues.

```mermaid
flowchart TD
Start([Request Received]) --> Validate["Validate Parameters"]
Validate --> Filter{"Category/Country Filter?"}
Filter --> |Yes| ApplyFilter["Apply Category/Country Filter"]
Filter --> |No| AllDomains["Retrieve All Domains"]
ApplyFilter --> Paginate["Apply Limit/Offset"]
AllDomains --> Paginate
Paginate --> Execute["Execute Database Query"]
Execute --> Transform["Transform Results"]
Transform --> Cache["Check Cache Status"]
Cache --> Return["Return Response"]
```

**Diagram sources**
- [TopDomainsService.ts](file://services/web-app/src/services/TopDomainsService.ts#L200-L400)

**Section sources**
- [TopDomainsService.ts](file://services/web-app/src/services/TopDomainsService.ts#L200-L500)

## Caching Strategy
The API employs a multi-layer caching strategy using Redis to optimize performance and reduce database load. Different endpoints have varying TTL (Time To Live) values based on their update frequency:

- Top domains: 1 hour
- Categories: 2 hours
- Rankings: 30 minutes

The `TopDomainsService` implements cache-aside pattern, checking Redis before querying the database. When data is retrieved from the database, it is stored in Redis with the appropriate TTL. Cache invalidation can be triggered manually through the service's invalidateCache method when data is updated.

```mermaid
flowchart LR
A["Client Request"] --> B{Cache Key Exists?}
B --> |Yes| C["Return Cached Data"]
B --> |No| D["Query Database"]
D --> E["Store in Cache"]
E --> F["Return Response"]
C --> G["Client"]
F --> G
```

**Diagram sources**
- [TopDomainsService.ts](file://services/web-app/src/services/TopDomainsService.ts#L500-L600)
- [RedisClient.ts](file://shared/src/database/RedisClient.ts#L1-L20)

**Section sources**
- [TopDomainsService.ts](file://services/web-app/src/services/TopDomainsService.ts#L500-L667)

## Example Responses
### Successful Response
```json
{
  "domains": [
    {
      "domain": "google.com",
      "globalRank": 1,
      "categoryRank": 1,
      "category": "technology",
      "country": "US",
      "scores": {
        "overall": 0.98,
        "performance": 0.95,
        "security": 0.99,
        "seo": 0.97,
        "technical": 0.96
      },
      "trafficEstimate": 15000000,
      "trend": "up",
      "lastUpdated": "2024-01-15T10:30:00Z"
    }
  ],
  "category": "technology",
  "country": "all",
  "timeframe": "current",
  "total": 1500,
  "timestamp": "2024-01-15T10:35:00Z"
}
```

### Error Response
```json
{
  "error": "Invalid category parameter",
  "message": "The requested category 'invalid-category' does not exist",
  "timestamp": "2024-01-15T10:35:00Z"
}
```

**Section sources**
- [TopDomainsService.ts](file://services/web-app/src/services/TopDomainsService.ts#L100-L667)

## Client Implementation Examples
### Building a Ranking Table
```javascript
async function fetchTopDomains(category = 'all', limit = 50) {
  const response = await fetch(`/api/rankings/top?category=${category}&limit=${limit}`);
  const data = await response.json();
  
  const table = document.getElementById('rankings-table');
  data.domains.forEach(domain => {
    const row = table.insertRow();
    row.innerHTML = `
      <td>${domain.globalRank}</td>
      <td>${domain.domain}</td>
      <td>${domain.scores.overall}</td>
      <td>${domain.trafficEstimate.toLocaleString()}</td>
      <td><span class="trend-${domain.trend}">${domain.trend}</span></td>
    `;
  });
}
```

### Creating a Visualization
```javascript
async function createRankingsChart(category) {
  const response = await fetch(`/api/rankings/top?category=${category}&limit=10`);
  const data = await response.json();
  
  const ctx = document.getElementById('rankings-chart').getContext('2d');
  new Chart(ctx, {
    type: 'bar',
    data: {
      labels: data.domains.map(d => d.domain),
      datasets: [{
        label: 'Overall Score',
        data: data.domains.map(d => d.scores.overall),
        backgroundColor: 'rgba(54, 162, 235, 0.5)'
      }]
    }
  });
}
```

**Section sources**
- [TopDomainsPage.tsx](file://services/web-app/src/components/TopDomainsPage.tsx#L1-L50)

## Troubleshooting Guide
### Common Issues and Solutions

**Issue: Stale Data in Response**
- **Cause**: Cached data has not been invalidated after updates
- **Solution**: Call the cache invalidation endpoint or wait for TTL expiration
- **Prevention**: Implement proper cache invalidation in the Worker service

**Issue: Missing Categories**
- **Cause**: Category data not properly indexed in ManticoreDB
- **Solution**: Verify category data in the database and reindex if necessary
- **Prevention**: Ensure category updates trigger Manticore sync jobs

**Issue: Slow Response Times**
- **Cause**: Cache miss with high database load
- **Solution**: Monitor Redis performance and consider increasing cache TTL
- **Prevention**: Optimize database queries and indexing

**Cache Invalidation Procedure**
To manually invalidate the cache for a specific category:
```javascript
await topDomainsService.invalidateCache('technology');
```

Or to invalidate all rankings cache:
```javascript
await topDomainsService.invalidateCache();
```

**Section sources**
- [TopDomainsService.ts](file://services/web-app/src/services/TopDomainsService.ts#L600-L667)
- [RankingUpdateService.ts](file://services/worker/src/ranking/RankingUpdateService.ts#L1000-L1200)

## Conclusion
The Rankings API provides efficient access to pre-calculated domain rankings through a well-designed endpoint with comprehensive filtering and pagination capabilities. The system leverages a distributed database architecture with ScyllaDB for primary storage and Redis for caching, ensuring high performance and scalability. Rankings are periodically updated by the Worker service using a composite scoring algorithm, with data consistency maintained through proper job scheduling and cache invalidation. This documentation provides the necessary information for integrating with the API and troubleshooting common issues.