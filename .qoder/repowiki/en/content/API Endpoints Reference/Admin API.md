# Admin API

<cite>
**Referenced Files in This Document**   
- [session.ts](file://services/admin/src/lib/auth/session.ts)
- [config.ts](file://services/admin/src/lib/auth/config.ts)
- [service.ts](file://services/admin/src/lib/auth/service.ts)
- [audit.ts](file://services/admin/src/lib/auth/audit.ts)
- [middleware.ts](file://services/admin/src/lib/auth/middleware.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Authentication Mechanism](#authentication-mechanism)
3. [Protected Routes and Authorization](#protected-routes-and-authorization)
4. [Admin API Endpoints](#admin-api-endpoints)
5. [Request/Response Schemas](#requestresponse-schemas)
6. [Client Implementation Examples](#client-implementation-examples)
7. [Security Considerations](#security-considerations)
8. [Audit Logging](#audit-logging)
9. [Rate Limiting and Session Management](#rate-limiting-and-session-management)
10. [Troubleshooting Guide](#troubleshooting-guide)

## Introduction
The Admin API provides secure access to administrative functions for system monitoring, user management, and analytics data retrieval. All endpoints are protected by session-based authentication using secure HTTP-only cookies and enforce role-based access control (RBAC). This documentation details the authentication flow, protected endpoints, request/response formats, and security practices for integrating with the admin dashboard.

## Authentication Mechanism

The Admin API uses session-based authentication with secure cookies managed by Iron-Session and Redis-backed session storage. Authentication is implemented through a combination of password hashing (Argon2), session validation, and role-based permissions.

```mermaid
sequenceDiagram
participant Client
participant AuthController
participant AuthService
participant Redis
participant Database
Client->>AuthController : POST /api/auth/login
AuthController->>AuthService : authenticate(username, password)
AuthService->>Database : Lookup user credentials
Database-->>AuthService : Return hashed password
AuthService->>AuthService : Verify password with Argon2
AuthService->>Redis : Store session data with TTL
Redis-->>AuthService : Session stored
AuthService-->>AuthController : Return session ID
AuthController->>Client : Set secure cookie (admin-session)
```

**Diagram sources**
- [service.ts](file://services/admin/src/lib/auth/service.ts#L150-L250)
- [session.ts](file://services/admin/src/lib/auth/session.ts#L20-L50)

**Section sources**
- [service.ts](file://services/admin/src/lib/auth/service.ts#L1-L420)
- [session.ts](file://services/admin/src/lib/auth/session.ts#L1-L142)
- [config.ts](file://services/admin/src/lib/auth/config.ts#L1-L135)

## Protected Routes and Authorization

All admin API endpoints require authentication and enforce authorization based on user roles and permissions. The system implements a middleware chain that validates sessions and checks access rights before allowing requests to proceed.

### Role-Based Access Control
The following roles are defined in the system:

| Role | Permissions | Description |
|------|-------------|-------------|
| super_admin | * (all permissions) | Full system access |
| admin | dashboard.view, services.view, domains.view, domains.edit, crawl.view, crawl.manage, analytics.view, logs.view, database.view | Operational management |
| viewer | dashboard.view, services.view, domains.view, analytics.view, logs.view | Read-only access |

### Permission Structure
Permissions follow a dot-notation format: `resource.action`. For example:
- `users.create` – Create new users
- `domains.delete` – Delete domains
- `analytics.export` – Export analytics data

```mermaid
flowchart TD
Start([Request Received]) --> ExtractCookie["Extract admin-session cookie"]
ExtractCookie --> ValidateSession["Validate session in Redis"]
ValidateSession --> SessionValid{"Session valid?"}
SessionValid --> |No| Return401["Return 401 Unauthorized"]
SessionValid --> |Yes| CheckPermissions["Check user permissions"]
CheckPermissions --> HasPermission{"Has required permission?"}
HasPermission --> |No| Return403["Return 403 Forbidden"]
HasPermission --> |Yes| ProcessRequest["Process API Request"]
ProcessRequest --> Return200["Return 200 OK"]
Return401 --> End([Response Sent])
Return403 --> End
Return200 --> End
```

**Diagram sources**
- [middleware.ts](file://services/admin/src/lib/auth/middleware.ts#L10-L100)
- [service.ts](file://services/admin/src/lib/auth/service.ts#L250-L300)

**Section sources**
- [middleware.ts](file://services/admin/src/lib/auth/middleware.ts#L1-L116)
- [config.ts](file://services/admin/src/lib/auth/config.ts#L50-L80)

## Admin API Endpoints

### Authentication Endpoints
#### POST /api/auth/login
Authenticate with username and password to receive a session cookie.

**Request Body**
```json
{
  "username": "string",
  "password": "string"
}
```

**Responses**
- `200 OK`: Authentication successful, session cookie set
- `401 Unauthorized`: Invalid credentials
- `403 Forbidden`: Account locked or disabled

#### POST /api/auth/logout
Clear the session and invalidate the current session token.

**Responses**
- `200 OK`: Session destroyed
- `401 Unauthorized`: No active session

### User Management Endpoints
#### GET /api/users
Retrieve list of all admin users (requires `users.view` permission)

#### POST /api/users
Create a new admin user (requires `users.create` permission)

#### PUT /api/users/{id}
Update user details or permissions (requires `users.edit` permission)

#### DELETE /api/users/{id}
Remove an admin user (requires `users.delete` permission)

### System Monitoring Endpoints
#### GET /api/health
Check system health status (requires `services.view`)

#### GET /api/logs
Retrieve system logs (requires `logs.view`)

#### GET /api/services/status
Get status of all running services

### Analytics Data Retrieval
#### GET /api/analytics/summary
Get analytics dashboard summary (requires `analytics.view`)

#### GET /api/analytics/export
Export analytics data in CSV format (requires `analytics.export`)

#### GET /api/analytics/realtime
Stream real-time analytics events (requires `analytics.view`)

**Section sources**
- [config.ts](file://services/admin/src/lib/auth/config.ts#L30-L130)
- [middleware.ts](file://services/admin/src/lib/auth/middleware.ts#L40-L90)

## Request/Response Schemas

### Authentication Request
```json
{
  "username": "admin",
  "password": "AdminPass123!"
}
```

### Successful Authentication Response (200)
```json
{
  "success": true,
  "user": {
    "username": "admin",
    "role": "admin",
    "permissions": [
      "dashboard.view",
      "services.view",
      "domains.view",
      "domains.edit"
    ]
  }
}
```

### Error Responses
#### 401 Unauthorized
```json
{
  "error": "Authentication required"
}
```

#### 403 Forbidden
```json
{
  "error": "Permission required: users.create"
}
```

#### 429 Too Many Requests
```json
{
  "error": "Too many login attempts. Please try again later."
}
```

### User Object Schema
```json
{
  "id": "string",
  "username": "string",
  "role": "super_admin|admin|viewer",
  "permissions": ["string"],
  "createdAt": "datetime",
  "lastLogin": "datetime|null",
  "isActive": "boolean",
  "failedLoginAttempts": "number",
  "lockedUntil": "datetime|null"
}
```

**Section sources**
- [config.ts](file://services/admin/src/lib/auth/config.ts#L10-L135)
- [service.ts](file://services/admin/src/lib/auth/service.ts#L20-L50)
- [audit.ts](file://services/admin/src/lib/auth/audit.ts#L10-L30)

## Client Implementation Examples

### Admin Dashboard Login (JavaScript)
```javascript
async function login(username, password) {
  const response = await fetch('/api/auth/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ username, password })
  });

  if (response.ok) {
    const data = await response.json();
    // Store user info in context/state
    setUser(data.user);
    navigate('/dashboard');
  } else {
    const error = await response.json();
    setError(error.message);
  }
}
```

### Making Authenticated API Calls
```javascript
async function fetchUsers() {
  const response = await fetch('/api/users', {
    method: 'GET',
    credentials: 'include' // Important: include cookies
  });

  if (response.status === 401) {
    // Redirect to login
    window.location.href = '/login';
    return;
  }

  if (response.status === 403) {
    throw new Error('Insufficient permissions to view users');
  }

  return response.json();
}
```

### Handling Session Expiration
```javascript
// Global error handler
function handleApiError(error) {
  if (error.response?.status === 401) {
    // Clear local state and redirect
    clearAuthState();
    window.location.href = '/login';
  }
}
```

**Section sources**
- [middleware.ts](file://services/admin/src/lib/auth/middleware.ts#L60-L80)
- [session.ts](file://services/admin/src/lib/auth/session.ts#L60-L80)

## Security Considerations

### Secure Cookie Configuration
The session cookie (`admin-session`) is configured with the following security attributes:
- `HttpOnly`: Prevents client-side JavaScript access
- `Secure`: Only transmitted over HTTPS
- `SameSite: strict`: Prevents CSRF attacks
- `Max-Age`: 8 hours (configurable)

### Password Policy
All admin passwords must meet the following requirements:
- Minimum 8 characters
- At least one uppercase letter
- At least one lowercase letter
- At least one number
- At least one special character
- Maximum age of 90 days

### Session Security
- Sessions are stored in Redis with TTL matching session timeout
- Each session tracks IP address and user agent for anomaly detection
- Failed login attempts are rate-limited (5 attempts per 15 minutes)
- Accounts are locked for 30 minutes after maximum failed attempts

**Section sources**
- [config.ts](file://services/admin/src/lib/auth/config.ts#L90-L130)
- [service.ts](file://services/admin/src/lib/auth/service.ts#L350-L400)
- [session.ts](file://services/admin/src/lib/auth/session.ts#L100-L120)

## Audit Logging

All administrative actions are logged for security and compliance purposes. The audit system captures:

### Logged Events
- User login/logout
- Permission changes
- User creation/deletion
- Bulk operations
- Failed authentication attempts

### Audit Log Schema
```json
{
  "id": "string",
  "userId": "string",
  "username": "string",
  "action": "string",
  "resource": "string",
  "resourceId": "string|null",
  "oldValues": "object|null",
  "newValues": "object|null",
  "timestamp": "datetime",
  "ipAddress": "string",
  "userAgent": "string",
  "severity": "low|medium|high|critical"
}
```

### Security Alerts
Certain actions trigger automatic security alerts:
- Permission escalation attempts
- Bulk user operations
- Multiple logins from different IP addresses within 1 hour
- Login from unusual geographic locations

```mermaid
flowchart TD
Action["User Action"] --> CheckType["Check Action Type"]
CheckType --> |Permission Change| TriggerAlert["Log Security Alert: High Severity"]
CheckType --> |Bulk Operation| TriggerAlert
CheckType --> |Multiple IPs| TriggerAlert
CheckType --> |Normal Action| LogAudit["Log Audit Event"]
TriggerAlert --> StoreAlert["Store in security_alerts table"]
LogAudit --> StoreAudit["Store in audit_logs table"]
```

**Diagram sources**
- [audit.ts](file://services/admin/src/lib/auth/audit.ts#L150-L250)

**Section sources**
- [audit.ts](file://services/admin/src/lib/auth/audit.ts#L1-L312)

## Rate Limiting and Session Management

### Login Rate Limiting
- Maximum 5 failed login attempts per 15 minutes
- Account lockout for 30 minutes after threshold exceeded
- Per-username and per-IP tracking

### Session Management
- Session timeout: 8 hours of inactivity
- Maximum concurrent sessions per user: 5
- Session refresh on each authenticated request
- Ability to terminate all sessions for a user

### API Rate Limiting
All admin endpoints are protected by rate limiting:
- 100 requests per minute per IP address
- 1000 requests per hour per authenticated user
- Exemptions for critical monitoring endpoints

**Section sources**
- [service.ts](file://services/admin/src/lib/auth/service.ts#L300-L350)
- [config.ts](file://services/admin/src/lib/auth/config.ts#L70-L85)

## Troubleshooting Guide

### Common Issues and Solutions

#### Session Expiration (401 Errors)
**Symptoms**: User is unexpectedly logged out
**Causes**:
- 8-hour inactivity timeout reached
- Server-side session cleanup
- Clock skew between client and server

**Solutions**:
- Implement silent refresh before expiration
- Handle 401 responses gracefully with login redirect
- Ensure system clocks are synchronized

#### Permission Errors (403 Forbidden)
**Symptoms**: Access denied to specific endpoints
**Causes**:
- User lacks required permission
- Role assignment not properly configured
- Cache inconsistency

**Solutions**:
- Verify user permissions in `/api/users` endpoint
- Clear browser cache and re-authenticate
- Contact super-admin for permission adjustment

#### Login Failures
**Symptoms**: "Invalid credentials" or "Account locked" messages
**Causes**:
- Incorrect password
- Account locked due to failed attempts
- User deactivated

**Solutions**:
- Reset password using recovery process
- Wait for lockout period to expire
- Contact system administrator

#### CSRF Protection Issues
**Symptoms**: Login succeeds but session cookie not set
**Causes**:
- Missing SameSite cookie configuration
- Cross-origin requests from unauthorized domains

**Solutions**:
- Ensure frontend and API are on same domain
- Verify cookie SameSite=strict configuration
- Check browser security settings

**Section sources**
- [service.ts](file://services/admin/src/lib/auth/service.ts#L100-L150)
- [middleware.ts](file://services/admin/src/lib/auth/middleware.ts#L20-L60)
- [audit.ts](file://services/admin/src/lib/auth/audit.ts#L250-L300)