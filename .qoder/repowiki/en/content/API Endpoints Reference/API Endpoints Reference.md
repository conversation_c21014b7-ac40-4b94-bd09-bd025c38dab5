# API Endpoints Reference

<cite>
**Referenced Files in This Document**   
- [description.ts](file://services/web-app/src/routes/description.ts)
- [HttpApiServer.ts](file://services/domain-seeder/src/api/HttpApiServer.ts)
- [health.ts](file://services/worker/src/routes/health.ts)
- [ApiResponse.ts](file://shared/src/api/ApiResponse.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Core API Endpoints](#core-api-endpoints)
3. [Health and Monitoring Endpoints](#health-and-monitoring-endpoints)
4. [Request and Response Standards](#request-and-response-standards)
5. [Error Handling and Status Codes](#error-handling-and-status-codes)
6. [Rate Limiting and Authentication](#rate-limiting-and-authentication)
7. [API Versioning and Backwards Compatibility](#api-versioning-and-backwards-compatibility)
8. [Client Implementation Guidelines](#client-implementation-guidelines)
9. [Performance Optimization Tips](#performance-optimization-tips)
10. [Security Considerations](#security-considerations)

## Introduction
This document provides comprehensive reference documentation for the domainr API endpoints. The API enables domain search, analysis, ranking, and description retrieval through a set of RESTful endpoints. The system is composed of multiple microservices including the web-app, domain-seeder, and worker services, each exposing specific endpoints for different functionality domains. This documentation covers the available endpoints, their request/response schemas, authentication methods, error handling strategies, and usage guidelines.

## Core API Endpoints

### Domain Description Endpoint
Retrieves comprehensive analysis and metadata for a specific domain.

**HTTP Method**: `GET`  
**URL Pattern**: `/api/description/:domain`  
**Authentication**: Not required (public endpoint)  
**Parameters**:
- `domain` (path): Domain name to analyze (e.g., "example.com")

**Request Example**:
```
GET /api/description/example.com HTTP/1.1
Host: api.domainr.com
Accept: application/json
```

**Response Structure**:
```json
{
  "metadata": {
    "domain": "string",
    "tld": "string",
    "status": "active|inactive",
    "category": {
      "primary": "string"
    },
    "owner": {
      "registrar": "string"
    },
    "ageDays": "number"
  },
  "technical": {
    "technologies": ["string"],
    "performance": {
      "loadTimeMs": "number"
    },
    "security": {
      "sslGrade": "string"
    }
  },
  "seo": {
    "title": "string",
    "metaDescription": "string"
  },
  "ranking": {},
  "crawl": {
    "lastCrawled": "ISO 8601 timestamp",
    "crawlType": "quick|full"
  }
}
```

**Response Codes**:
- `200 OK`: Domain found and description returned
- `404 Not Found`: Domain not found in database
- `500 Internal Server Error`: Server processing error

**Section sources**
- [description.ts](file://services/web-app/src/routes/description.ts#L1-L54)

## Health and Monitoring Endpoints

### Domain Seeder Health Endpoints
The domain-seeder service provides comprehensive health and monitoring endpoints for system status and operational control.

**Base URL**: `/` (domain-seeder service)

#### Health Check
Returns overall health status of the seeder system.

**HTTP Method**: `GET`  
**URL**: `/health`  
**Response Code**: `200` (healthy), `503` (unhealthy)

```json
{
  "status": "healthy|degraded|unhealthy",
  "checks": [
    {
      "name": "string",
      "status": "healthy|unhealthy",
      "details": {}
    }
  ],
  "timestamp": "ISO 8601 timestamp"
}
```

#### Readiness Probe
Kubernetes readiness probe indicating if the service can accept traffic.

**HTTP Method**: `GET`  
**URL**: `/health/ready`  
**Response Code**: `200` (ready), `503` (not ready)

```json
{
  "ready": "boolean",
  "timestamp": "ISO 8601 timestamp"
}
```

#### Liveness Probe
Kubernetes liveness probe indicating if the service is running.

**HTTP Method**: `GET`  
**URL**: `/health/live`  
**Response Code**: `200` (alive), `503` (not alive)

```json
{
  "alive": "boolean",
  "timestamp": "ISO 8601 timestamp"
}
```

#### Metrics Endpoint
Prometheus-compatible metrics endpoint for monitoring.

**HTTP Method**: `GET`  
**URL**: `/metrics`  
**Response Code**: `200`

Returns metrics in Prometheus text format including:
- Request counters and latencies
- Discovery operation metrics
- Database connection metrics
- Cache hit/miss ratios

#### Status Endpoint
Comprehensive system status with metrics and sync information.

**HTTP Method**: `GET`  
**URL**: `/status`  
**Response Code**: `200`, `500`

```json
{
  "status": "string",
  "checks": [],
  "metrics": {},
  "syncMetrics": {},
  "timestamp": "ISO 8601 timestamp"
}
```

#### Trigger Endpoint
Manual operation trigger for administrative tasks.

**HTTP Method**: `POST`  
**URL**: `/trigger`  
**Authentication**: Required (admin credentials)  
**Request Body**:
```json
{
  "operation": "discovery-run|top-sources-only|backfill|health-check|clear-cache",
  "parameters": {}
}
```

**Supported Operations**:
- `discovery-run`: Execute full discovery pipeline
- `top-sources-only`: Run discovery on top sources only
- `backfill`: Process backfill operations
- `health-check`: Perform health check
- `clear-cache`: Clear system caches

**Response**:
```json
{
  "operation": "string",
  "parameters": {},
  "result": {},
  "timestamp": "ISO 8601 timestamp"
}
```

**Section sources**
- [HttpApiServer.ts](file://services/domain-seeder/src/api/HttpApiServer.ts#L1-L495)

### Worker Service Health Endpoints
The worker service provides health endpoints for monitoring worker status and readiness.

#### Basic Health Check
**HTTP Method**: `GET`  
**URL**: `/health`  
**Response Code**: `200` (healthy), `503` (unhealthy)

```json
{
  "status": "healthy|unhealthy",
  "connectivity": {
    "database": "boolean",
    "redis": "boolean"
  },
  "timestamp": "ISO 8601 timestamp"
}
```

#### Readiness Check
**HTTP Method**: `GET`  
**URL**: `/health/ready`  
**Response Code**: `200` (ready), `503` (not ready)

```json
{
  "ready": "boolean",
  "status": "string",
  "connectivity": {
    "database": "boolean",
    "redis": "boolean"
  },
  "timestamp": "ISO 8601 timestamp"
}
```

#### Liveness Check
**HTTP Method**: `GET`  
**URL**: `/health/live`  
**Response Code**: `200`

```json
{
  "status": "alive",
  "timestamp": "ISO 8601 timestamp"
}
```

**Section sources**
- [health.ts](file://services/worker/src/routes/health.ts#L1-L97)

## Request and Response Standards

### Response Format
All API responses follow a standardized format using the `ApiResponse` class from the shared library.

```json
{
  "success": "boolean",
  "data": {},
  "error": {
    "message": "string",
    "code": "string",
    "details": {},
    "timestamp": "ISO 8601 timestamp",
    "requestId": "string"
  },
  "meta": {
    "pagination": {
      "page": "number",
      "limit": "number",
      "total": "number",
      "totalPages": "number",
      "hasNext": "boolean",
      "hasPrev": "boolean"
    },
    "version": "string",
    "timestamp": "ISO 8601 timestamp",
    "requestId": "string"
  }
}
```

### Pagination
Endpoints that return lists support pagination via query parameters.

**Query Parameters**:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10, max: 100)

**Example**:
```
GET /api/domains?limit=25&page=2
```

**Section sources**
- [ApiResponse.ts](file://shared/src/api/ApiResponse.ts#L1-L264)

## Error Handling and Status Codes

### Standard Error Response
All error responses follow the standardized format:

```json
{
  "success": false,
  "error": {
    "message": "Descriptive error message",
    "code": "ERROR_CODE",
    "details": {},
    "timestamp": "ISO 8601 timestamp"
  }
}
```

### Error Codes
| Code | HTTP Status | Description |
|------|-----------|-------------|
| `NOT_FOUND` | 404 | Resource not found |
| `VALIDATION_ERROR` | 400 | Request validation failed |
| `UNAUTHORIZED` | 401 | Authentication required |
| `FORBIDDEN` | 403 | Access forbidden |
| `RATE_LIMITED` | 429 | Rate limit exceeded |
| `INTERNAL_ERROR` | 500 | Internal server error |

### Error Handling Implementation
The API implements comprehensive error handling:
- Input validation with descriptive error messages
- Structured error logging with context
- Client-friendly error messages
- Error classification and reporting
- Graceful degradation when possible

**Section sources**
- [ApiResponse.ts](file://shared/src/api/ApiResponse.ts#L1-L264)

## Rate Limiting and Authentication

### Rate Limiting
The API implements rate limiting to prevent abuse and ensure service availability.

**Rate Limit Strategy**:
- Token bucket algorithm
- Configurable limits per API key
- Burst capacity support
- Retry-After header on limit exceeded

**Rate Limited Response**:
```json
{
  "success": false,
  "error": {
    "message": "Rate limit exceeded",
    "code": "RATE_LIMITED",
    "details": {
      "retryAfter": "number (seconds)"
    }
  }
}
```

### Authentication
While the current endpoints shown are public, the system supports authentication for protected endpoints.

**Authentication Methods**:
- API keys in headers
- JWT tokens
- OAuth 2.0 (planned)

**Authentication Header**:
```
Authorization: Bearer <token>
```

## API Versioning and Backwards Compatibility

### Versioning Strategy
The API supports versioning through the response metadata.

**Version Indication**:
```json
"meta": {
  "version": "1.0.0"
}
```

### Backwards Compatibility
The system maintains backwards compatibility through:
- Semantic versioning
- Deprecation warnings in responses
- Support for multiple API versions
- Non-breaking changes in minor versions
- Comprehensive testing of API contracts

## Client Implementation Guidelines

### HTTP Clients
Use standard HTTP clients with proper error handling:

```javascript
// Example using fetch
async function getDomainDescription(domain) {
  try {
    const response = await fetch(`/api/description/${domain}`);
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error.message);
    }
    
    return data.data;
  } catch (error) {
    console.error('API call failed:', error);
    throw error;
  }
}
```

### Retry Logic
Implement retry logic for transient failures:

```javascript
async function withRetry(fn, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      if (error.code === 'INTERNAL_ERROR') {
        await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
        continue;
      }
      throw error;
    }
  }
}
```

### Caching
Implement client-side caching to reduce API calls:

- Cache successful responses with appropriate TTL
- Use ETag or Last-Modified headers when available
- Invalidate cache on error responses

## Performance Optimization Tips

### Batch Requests
Where possible, batch multiple requests to reduce overhead.

### Connection Reuse
Use HTTP keep-alive to reuse connections for multiple requests.

### Efficient Parsing
Use streaming JSON parsers for large responses to reduce memory usage.

### Parallel Requests
Make independent requests in parallel to reduce total latency.

### Cache Strategically
Implement appropriate caching strategies based on data volatility.

## Security Considerations

### Input Validation
All inputs are validated to prevent injection attacks and ensure data integrity.

### Security Headers
The API includes standard security headers:
- `X-Content-Type-Options: nosniff`
- `X-Frame-Options: DENY`
- `X-XSS-Protection: 1; mode=block`

### Data Protection
Sensitive data is protected through:
- Secure database connections
- Proper access controls
- Regular security audits

### Monitoring
Comprehensive monitoring detects and alerts on suspicious activity.