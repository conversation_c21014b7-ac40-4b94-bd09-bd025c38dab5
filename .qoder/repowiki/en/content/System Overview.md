# System Overview

<cite>
**Referenced Files in This Document**   
- [services/admin/README.md](file://services/admin/README.md)
- [services/domain-seeder/README.md](file://services/domain-seeder/README.md)
- [services/worker/README.md](file://services/worker/README.md)
- [services/web-app/package.json](file://services/web-app/package.json)
- [services/worker/package.json](file://services/worker/package.json)
- [services/domain-seeder/package.json](file://services/domain-seeder/package.json)
- [services/admin/package.json](file://services/admin/package.json)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Core Components](#core-components)
3. [Architecture Overview](#architecture-overview)
4. [Core Features](#core-features)
5. [Data Flow and Processing Pipeline](#data-flow-and-processing-pipeline)
6. [User Workflows](#user-workflows)
7. [Component Interactions](#component-interactions)

## Introduction

The domainr system is a comprehensive domain ranking and analysis platform designed to evaluate websites based on performance, security, SEO, and technical metrics. The system provides domain search, analysis, ranking, comparison, and automated crawling capabilities through a microservices architecture. It enables users to discover, analyze, and compare domains with detailed insights into their technical characteristics and competitive positioning.

The platform consists of four primary microservices: web-app, worker, domain-seeder, and admin. These components work together to collect domain data, process it through comprehensive analysis, calculate rankings, and present the results through user interfaces. The system is designed for scalability, reliability, and extensibility, supporting both real-time domain analysis and batch processing of large domain datasets.

**Section sources**
- [services/admin/README.md](file://services/admin/README.md#L1-L50)
- [services/domain-seeder/README.md](file://services/domain-seeder/README.md#L1-L50)
- [services/worker/README.md](file://services/worker/README.md#L1-L50)

## Core Components

The domainr system comprises four main microservices that handle different aspects of domain analysis and ranking:

**Web-App**: The frontend service that provides the user interface for domain search, analysis, and comparison. It serves as the primary entry point for end users to interact with the system.

**Worker Service**: A consolidated processing engine that replaces multiple specialized services by handling crawling, analysis, ranking calculations, and indexing in a single, scalable component. Each worker instance processes domains from a shared job queue through a complete pipeline.

**Domain Seeder**: A high-performance microservice responsible for discovering new domains through multiple strategies including differential analysis, zone file processing, and long-tail exploration. It seeds discovered domains into the processing pipeline.

**Admin Panel**: A comprehensive administrative interface that provides system monitoring, user management, configuration, and operational control over the entire domainr ecosystem.

These components are supported by shared infrastructure including ScyllaDB for domain data storage, MariaDB for relational data, Redis for caching and job queuing, and Manticore Search for full-text search capabilities.

**Section sources**
- [services/admin/README.md](file://services/admin/README.md#L150-L200)
- [services/domain-seeder/README.md](file://services/domain-seeder/README.md#L100-L150)
- [services/worker/README.md](file://services/worker/README.md#L50-L100)

## Architecture Overview

The domainr system follows a microservices architecture with clear separation of concerns and well-defined interactions between components. The architecture enables horizontal scalability, fault isolation, and independent deployment of services.

```mermaid
graph TD
subgraph "User Interface"
WebApp[Web Application]
AdminPanel[Admin Panel]
end
subgraph "Processing Layer"
DomainSeeder[Domain Seeder]
WorkerService[Worker Service]
end
subgraph "Data Layer"
ScyllaDB[ScyllaDB<br>Domain Data]
MariaDB[MariaDB<br>Relational Data]
Redis[Redis<br>Caching & Queues]
Manticore[Manticore Search<br>Full-text Index]
end
WebApp --> |Domain Requests| WorkerService
WebApp --> |Search Queries| Manticore
AdminPanel --> |Monitoring & Control| DomainSeeder
AdminPanel --> |Monitoring & Control| WorkerService
DomainSeeder --> |Enqueue Domains| Redis
Redis --> |Process Jobs| WorkerService
WorkerService --> |Store Results| ScyllaDB
WorkerService --> |Store Rankings| MariaDB
WorkerService --> |Update Index| Manticore
WorkerService --> |Cache Results| Redis
style WebApp fill:#4CAF50,stroke:#388E3C
style AdminPanel fill:#2196F3,stroke:#1976D2
style DomainSeeder fill:#FF9800,stroke:#F57C00
style WorkerService fill:#9C27B0,stroke:#7B1FA2
style ScyllaDB fill:#607D8B,stroke:#455A64
style MariaDB fill:#607D8B,stroke:#455A64
style Redis fill:#607D8B,stroke:#455A64
style Manticore fill:#607D8B,stroke:#455A64
```

**Diagram sources**
- [services/admin/README.md](file://services/admin/README.md#L200-L250)
- [services/domain-seeder/README.md](file://services/domain-seeder/README.md#L200-L250)
- [services/worker/README.md](file://services/worker/README.md#L100-L150)

## Core Features

The domainr system provides several core features for domain analysis and ranking:

**Domain Search**: Users can search for domains by name, returning matching results with basic information and ranking data.

**Domain Analysis**: Comprehensive analysis of individual domains across multiple dimensions including performance, security, SEO, and technical metrics. The analysis includes DNS records, SSL configuration, robots.txt compliance, content quality, and performance metrics.

**Top Domains Ranking**: Generation of ranked lists of domains based on composite scores that aggregate multiple metrics. The system maintains historical ranking data to track domain performance over time.

**Domain Comparison**: Side-by-side comparison of multiple domains across key metrics, enabling users to evaluate competitive positioning and identify strengths and weaknesses.

**Automated Crawling**: Scheduled and on-demand crawling of domains to collect up-to-date information. The crawling process includes HTML parsing, resource loading, and performance measurement.

**Ranking Calculations**: Implementation of the CompositeRanker system that calculates domain rankings by combining scores from multiple analyzers. The ranking system supports configurable weightings for different metric categories.

The system also includes advanced features such as AI-powered content generation, real-time monitoring, and administrative controls for system management.

**Section sources**
- [services/admin/README.md](file://services/admin/README.md#L50-L100)
- [services/domain-seeder/README.md](file://services/domain-seeder/README.md#L50-L100)
- [services/worker/README.md](file://services/worker/README.md#L150-L200)

## Data Flow and Processing Pipeline

The domainr system processes domains through a well-defined pipeline that begins with discovery and ends with indexed, ranked results. The data flow follows these stages:

```mermaid
flowchart TD
A[Domain Discovery] --> B[Job Queue]
B --> C[Crawling Phase]
C --> D[Analysis Phase]
D --> E[Ranking Calculation]
E --> F[Indexing Phase]
F --> G[Search & Retrieval]
subgraph "Domain Seeder"
A
end
subgraph "Worker Service"
C
D
E
F
end
subgraph "Data Storage"
H[ScyllaDB]
I[MariaDB]
J[Redis]
K[Manticore]
end
E --> H
E --> I
F --> J
F --> K
G --> K
style A fill:#FF9800,stroke:#F57C00
style B fill:#2196F3,stroke:#1976D2
style C fill:#4CAF50,stroke:#388E3C
style D fill:#4CAF50,stroke:#388E3C
style E fill:#4CAF50,stroke:#388E3C
style F fill:#4CAF50,stroke:#388E3C
style G fill:#673AB7,stroke:#5E35B1
```

The process begins with the Domain Seeder discovering new domains through various strategies and enqueuing them in Redis. Worker instances consume these jobs and execute the complete processing pipeline: crawling the domain to collect data, analyzing the collected data across multiple dimensions, calculating a composite ranking score, and updating the various data stores. The processed results become available for search and retrieval through the web interface.

**Diagram sources**
- [services/domain-seeder/README.md](file://services/domain-seeder/README.md#L300-L350)
- [services/worker/README.md](file://services/worker/README.md#L200-L250)

## User Workflows

The domainr system supports several common user workflows for domain analysis and research:

**Domain Search and Analysis**:
1. User enters a domain name in the search interface
2. Web-app queries Manticore Search for matching domains
3. System retrieves domain data from ScyllaDB and MariaDB
4. If data is stale or missing, system triggers analysis via Worker Service
5. Processed results are displayed with detailed metrics and rankings

**Top Domains Exploration**:
1. User navigates to top domains section
2. System queries MariaDB for current rankings
3. Results are displayed in ranked order with key metrics
4. User can filter by category, TLD, or other criteria
5. Clicking on a domain shows detailed analysis

**Domain Comparison**:
1. User selects multiple domains for comparison
2. System retrieves analysis data for each domain
3. Metrics are normalized and displayed in a comparative format
4. Differences and similarities are highlighted
5. Composite scores are shown side-by-side

**Real-time Monitoring**:
1. User configures monitoring for specific domains
2. System schedules periodic analysis via Worker Service
3. Changes are detected and recorded
4. Alerts are generated for significant changes
5. Historical trends are visualized

These workflows are supported by the underlying microservices architecture, with the Web-app handling user interactions, the Worker Service processing analysis jobs, and the Domain Seeder continuously discovering new domains to analyze.

**Section sources**
- [services/admin/README.md](file://services/admin/README.md#L100-L150)
- [services/web-app/package.json](file://services/web-app/package.json#L1-L20)
- [services/worker/package.json](file://services/worker/package.json#L1-L20)

## Component Interactions

The microservices in the domainr system interact through well-defined interfaces and shared data stores. The primary interaction patterns include:

```mermaid
sequenceDiagram
participant User as "User"
participant WebApp as "Web-App"
participant Worker as "Worker Service"
participant Seeder as "Domain Seeder"
participant Redis as "Redis"
participant Scylla as "ScyllaDB"
participant Maria as "MariaDB"
User->>WebApp : Search for domain
WebApp->>Manticore : Search index query
Manticore-->>WebApp : Domain results
WebApp->>Scylla : Retrieve domain data
Scylla-->>WebApp : Analysis data
alt Data not available
WebApp->>Redis : Enqueue analysis job
Redis->>Worker : Job available
Worker->>WebApp : Start processing
Worker->>Scylla : Store results
Worker->>Maria : Update rankings
end
WebApp-->>User : Display results
Seeder->>Redis : Enqueue new domains
Redis->>Worker : Process domain
Worker->>Scylla : Store crawl results
Worker->>Maria : Update rankings
Worker->>Redis : Update cache
User->>WebApp : Request top domains
WebApp->>Maria : Query rankings
Maria-->>WebApp : Ranked list
WebApp-->>User : Display rankings
```

The Web-app serves as the primary interface for end users, handling search requests and displaying results. When domain data is not available or needs updating, it coordinates with the Worker Service through Redis job queues. The Domain Seeder operates independently, discovering new domains and seeding them into the processing pipeline. The Worker Service acts as the central processing engine, consuming jobs from Redis and updating the various data stores with analysis results.

**Diagram sources**
- [services/admin/README.md](file://services/admin/README.md#L250-L300)
- [services/domain-seeder/README.md](file://services/domain-seeder/README.md#L250-L300)
- [services/worker/README.md](file://services/worker/README.md#L250-L300)