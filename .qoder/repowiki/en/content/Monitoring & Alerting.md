# Monitoring & Alerting

<cite>
**Referenced Files in This Document**   
- [prometheus.yml](file://monitoring/prometheus.yml)
- [alert_rules.yml](file://services/admin/monitoring/alert_rules.yml)
- [alert_rules.yml](file://services/worker/monitoring/alert_rules.yml)
- [grafana-dashboard.json](file://services/admin/monitoring/grafana-dashboard.json)
- [health.ts](file://services/domain-seeder/src/health.ts)
- [health.ts](file://services/worker/src/health.ts)
- [health.ts](file://services/web-app/src/routes/health.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Monitoring Architecture Overview](#monitoring-architecture-overview)
3. [Prometheus Configuration](#prometheus-configuration)
4. [Alerting Rules](#alerting-rules)
5. [Grafana Dashboards](#grafana-dashboards)
6. [Health Check Endpoints](#health-check-endpoints)
7. [Performance Monitoring](#performance-monitoring)
8. [Error Reporting Mechanisms](#error-reporting-mechanisms)
9. [Scaling and Optimization](#scaling-and-optimization)
10. [Conclusion](#conclusion)

## Introduction

The domainr monitoring and alerting system provides comprehensive observability across all services in the platform. This documentation details the implementation of Prometheus for metrics collection, Grafana for visualization, and Alertmanager for alerting. The system is designed to ensure high availability, performance, and reliability of critical services including the web application, worker processes, domain seeder, and supporting infrastructure components.

The monitoring framework supports both technical teams and operational staff with appropriate levels of detail, from high-level dashboards for business stakeholders to granular metrics for developers. The system enables proactive detection of issues, performance optimization, and rapid incident response through well-defined alerting rules and runbooks.

**Section sources**
- [prometheus.yml](file://monitoring/prometheus.yml)
- [alert_rules.yml](file://services/admin/monitoring/alert_rules.yml)
- [alert_rules.yml](file://services/worker/monitoring/alert_rules.yml)

## Monitoring Architecture Overview

The monitoring architecture follows a centralized model with Prometheus as the primary metrics collection system, Grafana for visualization, and Alertmanager for alert routing. Each service exposes metrics endpoints that are scraped by Prometheus at configured intervals. The collected metrics are then visualized in Grafana dashboards and evaluated against alerting rules.

```mermaid
graph TD
subgraph "Services"
A[Web Application]
B[Worker Service]
C[Domain Seeder]
D[Admin Panel]
E[Database Services]
F[Cache Services]
end
subgraph "Monitoring Stack"
G[Prometheus]
H[Grafana]
I[Alertmanager]
end
A --> |/metrics| G
B --> |/metrics| G
C --> |/metrics| G
D --> |/metrics| G
E --> |/metrics| G
F --> |/metrics| G
G --> H
G --> I
H --> |Visualization| Users
I --> |Alerts| NotificationChannels
style G fill:#444,stroke:#333
style H fill:#666,stroke:#333
style I fill:#555,stroke:#333
```

**Diagram sources**
- [prometheus.yml](file://monitoring/prometheus.yml)
- [alert_rules.yml](file://services/admin/monitoring/alert_rules.yml)
- [alert_rules.yml](file://services/worker/monitoring/alert_rules.yml)

**Section sources**
- [prometheus.yml](file://monitoring/prometheus.yml)
- [grafana-dashboard.json](file://services/admin/monitoring/grafana-dashboard.json)

## Prometheus Configuration

The Prometheus configuration defines the scraping targets, intervals, and global evaluation settings for the monitoring system. The configuration is centralized in the `monitoring/prometheus.yml` file and includes scrape configurations for all major services in the domainr ecosystem.

The global settings establish a 15-second scrape interval and evaluation interval, providing timely metric collection while balancing system resource usage. Individual service jobs are configured with appropriate scrape intervals based on their importance and metric volatility.

```mermaid
flowchart TD
A["Prometheus Configuration"] --> B["Global Settings"]
A --> C["Scrape Configurations"]
B --> D["scrape_interval: 15s"]
B --> E["evaluation_interval: 15s"]
C --> F["Web Application<br/>job: web-app<br/>interval: 30s"]
C --> G["Worker Service<br/>job: worker<br/>interval: 30s"]
C --> H["ScyllaDB<br/>job: scylla<br/>interval: 60s"]
C --> I["Redis<br/>job: redis<br/>interval: 30s"]
C --> J["Nginx<br/>job: nginx<br/>interval: 30s"]
C --> K["Node Exporter<br/>job: node<br/>interval: 30s"]
style A fill:#f9f,stroke:#333
style B fill:#bbf,stroke:#333
style C fill:#bbf,stroke:#333
```

**Diagram sources**
- [prometheus.yml](file://monitoring/prometheus.yml)

**Section sources**
- [prometheus.yml](file://monitoring/prometheus.yml)

## Alerting Rules

The alerting system is configured with severity-based rules across multiple service domains. Alert rules are defined in service-specific configuration files, with the admin panel and worker service having comprehensive alerting configurations.

### Admin Panel Alerting

The admin panel monitoring includes alerts for service availability, performance degradation, database health, authentication issues, and business logic anomalies. Alerts are categorized by severity (critical, warning, info) and include detailed annotations with runbook URLs for incident response.

```mermaid
classDiagram
class AdminPanelAlerts {
+AdminPanelDown : critical
+AdminPanelHighErrorRate : warning
+AdminPanelHighResponseTime : warning
+AdminPanelHighMemoryUsage : warning
+AdminPanelHighCPUUsage : warning
+AdminPanelDatabaseConnectionsHigh : warning
+AdminPanelDatabaseSlowQueries : warning
+AdminPanelHighFailedLogins : warning
+AdminPanelSuspiciousActivity : critical
+AdminPanelLowUserActivity : info
+AdminPanelCriticalOperationFailures : critical
}
class AlertLabels {
+severity : string
+service : string
+component : string
}
class AlertAnnotations {
+summary : string
+description : string
}
AdminPanelAlerts --> AlertLabels : "has"
AdminPanelAlerts --> AlertAnnotations : "has"
```

**Diagram sources**
- [alert_rules.yml](file://services/admin/monitoring/alert_rules.yml)

### Worker Service Alerting

The worker service has a comprehensive alerting configuration with multiple rule groups organized by severity and functional area. The alerting system monitors critical performance metrics, capacity constraints, business logic health, data quality, security events, and infrastructure conditions.

```mermaid
graph TD
A[Worker Service Alerting] --> B[Critical Alerts]
A --> C[High Severity Alerts]
A --> D[Medium Severity Alerts]
A --> E[Capacity Alerts]
A --> F[Business Logic Alerts]
A --> G[Data Quality Alerts]
A --> H[Security Alerts]
A --> I[Infrastructure Alerts]
B --> B1["WorkerServiceDown"]
B --> B2["HighErrorRate"]
B --> B3["DatabaseConnectionFailure"]
B --> B4["MemoryLeakDetected"]
B --> B5["QueueBacklogCritical"]
C --> C1["HighLatency"]
C --> C2["LowThroughput"]
C --> C3["HighCPUUsage"]
C --> C4["HighMemoryUsage"]
C --> C5["DatabaseSlowQueries"]
C --> C6["LowCacheHitRate"]
style A fill:#f96,stroke:#333
style B fill:#f66,stroke:#333
style C fill:#f99,stroke:#333
```

**Diagram sources**
- [alert_rules.yml](file://services/worker/monitoring/alert_rules.yml)

**Section sources**
- [alert_rules.yml](file://services/admin/monitoring/alert_rules.yml)
- [alert_rules.yml](file://services/worker/monitoring/alert_rules.yml)

## Grafana Dashboards

Grafana dashboards provide visual representations of system health and performance metrics. The admin panel includes a comprehensive monitoring dashboard that displays key service indicators, request patterns, response times, error rates, and resource utilization.

### Admin Panel Dashboard

The admin panel dashboard includes multiple panels that visualize different aspects of service health and performance:

```mermaid
erDiagram
DASHBOARD ||--o{ PANEL : contains
PANEL ||--o{ METRIC : displays
METRIC ||--o{ QUERY : based_on
DASHBOARD {
string title
string timezone
string refresh_interval
}
PANEL {
int id
string title
string type
int width
int height
int x_position
int y_position
}
METRIC {
string name
string description
string unit
}
QUERY {
string expression
string legend_format
string time_range
}
DASHBOARD }|--|| PANEL : "1 to many"
PANEL }|--|| METRIC : "1 to many"
METRIC }|--|| QUERY : "1 to many"
```

The dashboard includes panels for:
- Service health status (up/down)
- Request rate (requests per second)
- Response time percentiles (50th, 95th, 99th)
- Error rates (4xx and 5xx responses)
- Memory usage (heap size)
- CPU usage
- Database connections (active and idle)
- Authentication metrics (successful and failed logins)

**Diagram sources**
- [grafana-dashboard.json](file://services/admin/monitoring/grafana-dashboard.json)

**Section sources**
- [grafana-dashboard.json](file://services/admin/monitoring/grafana-dashboard.json)

## Health Check Endpoints

Each service implements standardized health check endpoints that provide both basic and detailed health information. The health check system is built on a shared HealthChecker class from the @shared module, ensuring consistency across services.

### Health Check Structure

The health check endpoints follow a standardized structure with three main endpoints:

```mermaid
sequenceDiagram
participant Client
participant Router
participant HealthService
Client->>Router : GET /health
Router->>HealthService : getHealthStatus(false)
HealthService-->>Router : Basic health status
Router-->>Client : 200/503 with basic info
Client->>Router : GET /health/detailed
Router->>HealthService : getHealthStatus(true)
HealthService-->>Router : Detailed health status
Router-->>Client : 200/503 with detailed info
Client->>Router : GET /health/ready
Router->>HealthService : isReady()
HealthService-->>Router : Boolean readiness
Router-->>Client : 200/503 with readiness status
Note over Client,HealthService : Standardized health check pattern across services
```

### Domain Seeder Health Implementation

The domain seeder service implements an extensive health check system that monitors multiple internal components:

```mermaid
classDiagram
class DomainSeederHealthService {
+getHealthStatus(includeMetrics)
+isReady()
+getSeederMetrics()
+recordDiscoveryMetrics()
+recordValidationMetrics()
+recordPipelineMetrics()
+cleanup()
}
class HealthChecker {
+getHealthStatus()
+registerHealthCheck()
+startHealthMonitoring()
}
class MetricsCollector {
+counter()
+gauge()
+timer()
+getMetricsSummary()
+stop()
}
DomainSeederHealthService --> HealthChecker : "uses"
DomainSeederHealthService --> MetricsCollector : "uses"
class ComponentHealth {
+status : string
+lastCheck : timestamp
+details : object
+error : string
}
DomainSeederHealthService --> ComponentHealth : "returns"
ComponentHealth <|-- DataConnectorsHealth
ComponentHealth <|-- DiscoveryPipelinesHealth
ComponentHealth <|-- ValidationSystemsHealth
ComponentHealth <|-- SchedulingSystemsHealth
DataConnectorsHealth : +connectors : array
DataConnectorsHealth : +totalDomainsFound : number
DiscoveryPipelinesHealth : +pipelines : array
DiscoveryPipelinesHealth : +averageSuccessRate : number
ValidationSystemsHealth : +systems : array
ValidationSystemsHealth : +averageErrorRate : number
SchedulingSystemsHealth : +schedulers : array
SchedulingSystemsHealth : +averageSuccessRate : number
```

**Diagram sources**
- [health.ts](file://services/domain-seeder/src/health.ts)
- [health.ts](file://services/domain-seeder/src/routes/health.ts)

**Section sources**
- [health.ts](file://services/domain-seeder/src/health.ts)
- [health.ts](file://services/domain-seeder/src/routes/health.ts)
- [health.ts](file://services/worker/src/health.ts)
- [health.ts](file://services/web-app/src/routes/health.ts)

## Performance Monitoring

The performance monitoring system tracks key metrics across all services to ensure optimal operation and identify potential bottlenecks. The system collects metrics at multiple levels, from infrastructure to application performance.

### Key Performance Indicators

The monitoring system tracks several critical performance indicators:

```mermaid
flowchart TD
A[Performance Monitoring] --> B[Request Performance]
A --> C[Resource Utilization]
A --> D[Queue Metrics]
A --> E[Database Performance]
A --> F[Cache Performance]
B --> B1["Request Rate"]
B --> B2["Response Time Percentiles"]
B --> B3["Error Rates"]
C --> C1["CPU Usage"]
C --> C2["Memory Usage"]
C --> C3["Disk Space"]
C --> C4["Network Latency"]
D --> D1["Queue Length"]
D --> D2["Processing Rate"]
D --> D3["Backlog"]
E --> E1["Query Duration"]
E --> E2["Connection Pool Utilization"]
E --> E3["Slow Queries"]
F --> F1["Cache Hit Rate"]
F --> F2["Eviction Rate"]
F --> F3["Miss Rate"]
style A fill:#9cf,stroke:#333
style B fill:#69f,stroke:#333
style C fill:#69f,stroke:#333
style D fill:#69f,stroke:#333
style E fill:#69f,stroke:#333
style F fill:#69f,stroke:#333
```

### Worker Service Performance Metrics

The worker service implements specialized performance metrics for domain processing:

```mermaid
stateDiagram-v2
[*] --> Idle
Idle --> Processing : "Job received"
Processing --> Success : "Processing completed"
Processing --> Failure : "Processing failed"
Success --> Idle : "Metrics recorded"
Failure --> Idle : "Error metrics recorded"
Processing : worker_domain_processing_duration_seconds
Processing : worker_domains_processed_total
Processing : worker_domains_failed_total
Success : worker : domain_processing_success_rate = 1
Failure : worker : domain_processing_success_rate = 0
note right of Processing
Metrics collected :
- Processing duration (histogram)
- Domains processed (counter)
- Domains failed (counter)
- Success rate (gauge)
end note
```

**Section sources**
- [alert_rules.yml](file://services/worker/monitoring/alert_rules.yml)
- [health.ts](file://services/worker/src/health.ts)

## Error Reporting Mechanisms

The error reporting system provides comprehensive visibility into application errors and exceptions across all services. The system integrates with both monitoring and logging infrastructure to ensure errors are detected, recorded, and alerted appropriately.

### Error Classification and Handling

The system implements a structured approach to error reporting with classification by severity and type:

```mermaid
classDiagram
class ErrorReportingSystem {
+reportError(error, context)
+classifyError(error)
+getErrorMetrics()
+getErrorTrends()
+generateErrorReports()
}
class ErrorClassification {
+CRITICAL
+HIGH
+MEDIUM
+LOW
+INFO
}
class ErrorType {
+INFRASTRUCTURE
+DATABASE
+NETWORK
+APPLICATION
+VALIDATION
+SECURITY
}
ErrorReportingSystem --> ErrorClassification : "uses"
ErrorReportingSystem --> ErrorType : "uses"
class ErrorMetrics {
+errors_total
+errors_by_type
+errors_by_service
+error_rate
+mean_time_to_repair
}
ErrorReportingSystem --> ErrorMetrics : "produces"
class AlertingIntegration {
+createErrorAlert()
+sendToAlertmanager()
+updateRunbook()
}
ErrorReportingSystem --> AlertingIntegration : "integrates with"
```

The error reporting system captures errors from multiple sources:
- Unhandled exceptions in application code
- Failed HTTP requests (4xx and 5xx responses)
- Database query failures
- External service communication errors
- Validation failures
- Security-related events

Error metrics are exposed via the standard metrics endpoint and used to trigger alerts when error rates exceed defined thresholds.

**Section sources**
- [alert_rules.yml](file://services/admin/monitoring/alert_rules.yml)
- [alert_rules.yml](file://services/worker/monitoring/alert_rules.yml)
- [health.ts](file://services/domain-seeder/src/health.ts)

## Scaling and Optimization

The monitoring system is designed to scale with the domainr platform and provide actionable insights for performance optimization. The configuration includes considerations for resource efficiency, data retention, and alert fatigue reduction.

### Monitoring at Scale

As the system scales, several optimization strategies are employed:

```mermaid
graph TD
A[Scaling Challenges] --> B[Metric Volume]
A --> C[Alert Fatigue]
A --> D[Resource Consumption]
A --> E[Data Retention]
B --> B1["Selective metric collection"]
B --> B2["Appropriate scrape intervals"]
B --> B3["Metric relabeling"]
C --> C1["Severity-based alerting"]
C --> C2["Alert grouping"]
C --> C3["Deduplication"]
C --> C4["Throttling"]
D --> D1["Efficient storage"]
D --> D2["Compression"]
D --> D3["Downsampling"]
E --> E1["Tiered retention"]
E --> E2["Automated cleanup"]
E --> E3["Archival"]
style A fill:#f96,stroke:#333
style B fill:#f66,stroke:#333
style C fill:#f66,stroke:#333
style D fill:#f66,stroke:#333
style E fill:#f66,stroke:#333
```

### Optimization Strategies

The system implements several optimization strategies to ensure efficient monitoring at scale:

1. **Metric Collection Optimization**: Different scrape intervals for different services based on their criticality and metric volatility
2. **Alert Tuning**: Careful threshold setting to balance sensitivity with false positive reduction
3. **Resource Management**: Configuration of appropriate resource limits for monitoring components
4. **Data Lifecycle Management**: Defined retention policies for metrics data
5. **Alert Grouping**: Consolidation of related alerts to reduce notification volume
6. **Runbook Integration**: Direct links to remediation procedures in alert annotations

These strategies ensure that the monitoring system remains effective and manageable as the platform grows in complexity and scale.

**Section sources**
- [prometheus.yml](file://monitoring/prometheus.yml)
- [alert_rules.yml](file://services/worker/monitoring/alert_rules.yml)

## Conclusion

The domainr monitoring and alerting system provides a comprehensive observability framework that ensures the reliability, performance, and security of the platform. By leveraging Prometheus for metrics collection, Grafana for visualization, and Alertmanager for alerting, the system delivers actionable insights across all services.

Key strengths of the monitoring implementation include:
- Standardized health check endpoints across all services
- Comprehensive alerting rules with clear severity levels and runbook integration
- Detailed Grafana dashboards for operational visibility
- Performance monitoring at multiple levels (infrastructure, application, business logic)
- Scalable architecture designed for growth

The system enables proactive issue detection, rapid incident response, and continuous performance optimization. By following the documented configuration patterns and best practices, teams can maintain high service quality and quickly resolve issues when they arise.

**Section sources**
- [prometheus.yml](file://monitoring/prometheus.yml)
- [alert_rules.yml](file://services/admin/monitoring/alert_rules.yml)
- [alert_rules.yml](file://services/worker/monitoring/alert_rules.yml)
- [grafana-dashboard.json](file://services/admin/monitoring/grafana-dashboard.json)